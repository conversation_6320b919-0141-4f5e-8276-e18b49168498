package com.estone.erp.publish.lazada.jobHandler;

import com.estone.erp.common.jobHandler.AbstractCheckSkuSyncLogJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022-11-23 10:47
 */
@Component
public class LazadaCheckSkuSyncLogJobHandler extends AbstractCheckSkuSyncLogJobHandler {

    public LazadaCheckSkuSyncLogJobHandler() {
        super(LazadaCheckSkuSyncLogJobHandler.class.getName());
    }

    /**
     * 平台
     */
    @Override
    protected String getSaleChannel() {
        return SaleChannelEnum.LAZADA.getChannelName();
    }

    /**
     * 要发送的队列名称
     */
    @Override
    protected String getQueueName() {
        return PublishQueues.LAZADA_SYNC_PRODUCT_INFO_QUEUE;
    }

    /**
     * 绑定的交换机
     */
    @Override
    protected String getExchange() {
        return PublishMqConfig.LAZADA_API_DIRECT_EXCHANGE;
    }

    /**
     * 路由
     */
    @Override
    protected String getRoutingKey() {
        return PublishQueues.LAZADA_SYNC_PRODUCT_INFO_KEY;
    }

    @Data
    private static class InnerParam {
        private int beforeDay;
        private int clearDay = 7;
    }

    @Override
    @XxlJob("LazadaCheckSkuSyncLogJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数不能为空");
            return ReturnT.FAIL;
        }
        // 同步对账
        checkProcess(innerParam.getBeforeDay());
        // 清理日志
        clearHistorySkuSyncLog(innerParam.getClearDay());
        return ReturnT.SUCCESS;
    }
}
