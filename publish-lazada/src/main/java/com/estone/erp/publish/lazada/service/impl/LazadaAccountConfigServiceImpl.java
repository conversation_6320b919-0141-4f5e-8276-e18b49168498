package com.estone.erp.publish.lazada.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.lazada.bean.annotation.NeedToLog;
import com.estone.erp.publish.lazada.enums.OperateLogEnum;
import com.estone.erp.publish.lazada.mapper.LazadaAccountConfigMapper;
import com.estone.erp.publish.lazada.model.LazadaAccountConfig;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigCriteria;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.model.LazadaOperateLog;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.lazada.service.LazadaOperateLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.SaleClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lazada_account_config
 * 2020-06-11 18:05:29
 */
@Service("lazadaAccountConfigService")
@Slf4j
public class LazadaAccountConfigServiceImpl implements LazadaAccountConfigService {
    @Resource
    private LazadaAccountConfigMapper lazadaAccountConfigMapper;
    @Resource
    private SaleClient saleClient;
    @Resource
    private LazadaOperateLogService lazadaOperateLogService;

    @Override
    public int countByExample(LazadaAccountConfigExample example) {
        Assert.notNull(example, "example is null!");
        return lazadaAccountConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<LazadaAccountConfig> search(CQuery<LazadaAccountConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        LazadaAccountConfigCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        CQueryResult<LazadaAccountConfig> cQueryResult = new CQueryResult<>();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_LAZADA);
        if (!superAdminOrEquivalent.isSuccess()) {
            cQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return cQueryResult;
        }
        String accountNumber = query.getAccountNumber();
        if (StringUtils.isBlank(accountNumber) && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> apiResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_LAZADA, false);
            if (!apiResult.isSuccess()) {
                cQueryResult.setErrorMsg(apiResult.getErrorMsg());
                return cQueryResult;
            }
            List<String> authorAccountList = apiResult.getResult();
            if (CollectionUtils.isEmpty(authorAccountList)) {
                return cQueryResult;
            }
            query.setAccountNumber(StringUtils.join(authorAccountList, ","));
        }
        LazadaAccountConfigExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = lazadaAccountConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<LazadaAccountConfig> lazadaAccountConfigs = lazadaAccountConfigMapper.selectByExample(example);
        // 组装结果
        CQueryResult<LazadaAccountConfig> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(lazadaAccountConfigs);
        return result;
    }

    @Override
    public LazadaAccountConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return lazadaAccountConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<LazadaAccountConfig> selectByExample(LazadaAccountConfigExample example) {
        Assert.notNull(example, "example is null!");
        return lazadaAccountConfigMapper.selectByExample(example);
    }

    @Override
    public List<String> selectAccountNumberByExample(LazadaAccountConfigExample example) {
        Assert.notNull(example, "example is null!");
        return lazadaAccountConfigMapper.selectAccountNumberByExample(example);
    }

    @Override
    public int insert(LazadaAccountConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return lazadaAccountConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(LazadaAccountConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return lazadaAccountConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(LazadaAccountConfig record, LazadaAccountConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return lazadaAccountConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int batchUpdateByAccountNumber(List<LazadaAccountConfig> list) {
        if (!CollectionUtils.isEmpty(list)) {
            lazadaAccountConfigMapper.batchUpdateByAccountNumber(list);
        }
        return 0;
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return lazadaAccountConfigMapper.deleteByPrimaryKey(ids);
    }

    /**
     * 同步lazada所有可用账号
     */
    @Override
    public void syncAccountInfo() {
        List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_LAZADA);
        if (CollectionUtils.isEmpty(saleAccountListBySaleChannel)){
            return;
        }

        List<LazadaAccountConfig> configList = saleAccountListBySaleChannel.stream().map(saleAccountAndBusinessResponse ->
                new LazadaAccountConfig().toLazadaAccountConfig(saleAccountAndBusinessResponse)).collect(Collectors.toList());

        Timestamp now = new Timestamp(System.currentTimeMillis());
        configList.stream().forEach(bean -> {
            bean.setCreateDate(now);
            bean.setUpdateDate(now);
            bean.setSyncTime(now);
            bean.setUpdateBy(WebUtils.getUserName());
        });

        List<List<LazadaAccountConfig>> pagingList = PagingUtils.pagingList(configList, 10000);
        for (List<LazadaAccountConfig> list : pagingList) {
            //1.查询不存在的店铺账号进行新增
            List<String> collectAccount = list.stream()
                    .filter(obj -> StringUtils.isNotBlank(obj.getAccountNumber()))
                    .map(LazadaAccountConfig::getAccountNumber)
                    .collect(Collectors.toList());

            LazadaAccountConfigExample example = new LazadaAccountConfigExample();
            example.createCriteria().andAccountNumberIn(collectAccount);
            List<LazadaAccountConfig> dbAccounts = lazadaAccountConfigMapper.selectByExample(example);
            List<String> dbAccount = dbAccounts.stream().map(LazadaAccountConfig::getAccountNumber).collect(Collectors.toList());

            List<LazadaAccountConfig> insetList = list.stream().filter(obj -> !dbAccount.contains(obj.getAccountNumber())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(insetList)) {
                for (LazadaAccountConfig insert : insetList) {
                    insert.setIsAutoUpdateStock(true);
                    insert.setAutoUpdatePrice(false);
                    insert.setMinGrossMargin(0.1);
                    insert.setMinGross(5.0);
                    insert.setMaxGrossMargin(0.35);
                    insert.setMaxGross(20.0);
                }
                try {
                    lazadaAccountConfigMapper.batchInsert(insetList);
                } catch (Exception e) {
                    log.error("批量新增错误：", e);
                }
            }

            //2.先批量更新存在的账号信息
            //货号后缀只在第一次新增的时候填充，后面不需要更新
            list.stream().forEach(o -> {
                o.setSkuSuffix(null);
            });
            lazadaAccountConfigMapper.batchUpdateByAccountNumber(list);
        }

        //把 SyncTime 不等于 now的数据置为 禁用
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        LazadaAccountConfig record = new LazadaAccountConfig();
        //禁用
        record.setAccountStatus(LazadaAccountConfigCriteria.accountStatusDisable);
        record.setUpdateDate(timestamp);
        record.setSyncTime(timestamp);

        List<String> existList = configList.stream().map(o -> o.getAccountNumber()).distinct().collect(Collectors.toList());
        LazadaAccountConfigExample example = new LazadaAccountConfigExample();
        example.createCriteria().andAccountNumberNotIn(existList);
        int count = lazadaAccountConfigMapper.updateByExampleSelective(record, example);
        log.info("{} 条数据没有被更新到，置为禁用", count);

    }

    @Override
    public void generateAccountConfigLog(LazadaAccountConfig dbAccountConfig, LazadaAccountConfig updateAccountConfig, String userName) {
        try {
            Class<LazadaAccountConfig> clazz = LazadaAccountConfig.class;
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                if (field.isAnnotationPresent(NeedToLog.class)) {
                    field.setAccessible(true);

                    // 为空也会保存，均为空不修改
                    if (isEmpty(field.get(updateAccountConfig)) && isEmpty(field.get(dbAccountConfig))) {
                        continue;
                    }

                    LazadaOperateLog lazadaOperateLog = new LazadaOperateLog();
                    lazadaOperateLog.setAccountNumber(dbAccountConfig.getAccountNumber());
                    lazadaOperateLog.setBusinessId(dbAccountConfig.getId());
                    lazadaOperateLog.setFieldName(field.getName());

                    String before;
                    String after;

                    if (field.get(dbAccountConfig) == null) {
                        before = "";
                    } else {
                        before = field.get(dbAccountConfig).toString();
                    }

                    if (field.get(updateAccountConfig) == null) {
                        after = "";
                    } else {
                        after = field.get(updateAccountConfig).toString();
                    }

                    if (StringUtils.isBlank(before) && StringUtils.isBlank(after)) {
                        continue;
                    }

                    // 分类特殊处理
                    if ("categoryCodes".equals(field.getName())) {
                        //不一样才对比
                        after = changeCategoryLog(before, after);
                        if (StringUtils.isBlank(after)) {
                            continue;
                        }
                    }
                    lazadaOperateLog.setBefore(before);
                    lazadaOperateLog.setAfter(after);
                    lazadaOperateLog.setCreateBy(userName);
                    lazadaOperateLog.setType(OperateLogEnum.UPDATE_ACCOUNT_CONFIG.getCode());
                    lazadaOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

                    if (!StringUtils.equalsIgnoreCase(before, after)) {
                        lazadaOperateLogService.insert(lazadaOperateLog);
                    }
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public Integer selectAccountGPPerWeight(String accountNumber) {
        LazadaAccountConfigExample example = new LazadaAccountConfigExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<LazadaAccountConfig> lazadaAccountConfigs = lazadaAccountConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lazadaAccountConfigs)) {
            return 0;
        }
        LazadaAccountConfig accountConfig = lazadaAccountConfigs.get(0);
        Integer gpPerWeight = accountConfig.getGpPerWeight();
        return Optional.ofNullable(gpPerWeight).orElse(0);
    }

    /**
     * 判断object是否为空
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if ((obj instanceof List)) {
            return ((List) obj).size() == 0;
        }
        if ((obj instanceof String)) {
            return ((String) obj).trim().equals("");
        }
        return false;
    }


    /**
     * 分类日志特殊处理
     *
     * @param
     */
    private static String changeCategoryLog(String before, String after) {
        List<String> beforeCategoryIds = CommonUtils.splitList(before, ",");
        List<String> afterCategoryIds = CommonUtils.splitList(after, ",");

        // 之前有 且不存在修改之后中则为删除
        List<String> delCategaryIds = beforeCategoryIds.stream()
                .filter(o -> !afterCategoryIds.contains(o)).collect(Collectors.toList());

        // 之前无 且存在修改之后中则为新增
        List<String> addCategaryIds = afterCategoryIds.stream()
                .filter(o -> !beforeCategoryIds.contains(o)).collect(Collectors.toList());

        List<String> afterArr = new ArrayList<>();
        StringBuffer deStringBuffer = new StringBuffer();

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(delCategaryIds)) {
            deStringBuffer.append("删除：" + org.apache.commons.lang.StringUtils.join(delCategaryIds, ","));
            String delStr = deStringBuffer.toString();
            afterArr.add(delStr);
        }

        StringBuffer addStringBuffer = new StringBuffer();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(addCategaryIds)) {
            addStringBuffer.append("增加：" + org.apache.commons.lang.StringUtils.join(addCategaryIds, ","));
            String addStr = addStringBuffer.toString();
            afterArr.add(addStr);
        }
        return org.apache.commons.collections.CollectionUtils.isNotEmpty(afterArr) ? JSON.toJSONString(afterArr) : "";
    }

    @Override
    public Map<String, String> getAccountSite(List<String> accountNumberList) {
        Map<String, String> accountToSiteMap = new HashMap<>();
        for (String accountNumber : accountNumberList) {
            String site = getAccountSite(accountNumber);
            accountToSiteMap.put(accountNumber, site);
        }

        return accountToSiteMap;
    }

    @Override
    public LazadaAccountConfig getConfigByPriority(List<String> accountNmuberList) {
        if (CollectionUtils.isEmpty(accountNmuberList)) {
            return null;
        }

        // 获取店铺配置
        LazadaAccountConfigExample example = new LazadaAccountConfigExample();
        example.createCriteria().andAccountNumberIn(accountNmuberList);
        List<LazadaAccountConfig> accountConfigs = selectByExample(example);
        if (CollectionUtils.isEmpty(accountConfigs)) {
            return null;
        }

        // 根据优先级取值
        LazadaAccountConfig returnConfig = new LazadaAccountConfig();
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_LAZADA, "LAZADA", "SITE_PRIORITY", 60);
        List<String> siteList = CommonUtils.splitList(systemParamValue, ",");
        for (String site : siteList) {
            LazadaAccountConfig accountConfig = accountConfigs.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getAccountNumber()) && o.getAccountNumber().contains(site)).findFirst().orElse(null);
            if (null == accountConfig) {
                continue;
            }

            if (StringUtils.isBlank(returnConfig.getSkuSuffix()) && StringUtils.isNotBlank(accountConfig.getSkuSuffix())) {
                returnConfig.setSkuSuffix(accountConfig.getSkuSuffix());
            }

            if (null == returnConfig.getGrossMargin() && null != accountConfig.getGrossMargin()) {
                returnConfig.setGrossMargin(accountConfig.getGrossMargin());
            }
        }

        return returnConfig;
    }

    @Override
    public Pair<String, Double> getSkuSuffixAndGrossMarginPair(List<LazadaAccountConfig> accountConfigs) {
        if (CollectionUtils.isEmpty(accountConfigs)) {
            return null;
        }
        String skuSuffix = null;
        Double grossMargin = null;
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_LAZADA, "LAZADA", "SITE_PRIORITY", 60);
        List<String> siteList = CommonUtils.splitList(systemParamValue, ",");
        for (String site : siteList) {
            // 根据优先级取值
            LazadaAccountConfig accountConfig = accountConfigs.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getAccountNumber()) && o.getAccountNumber().contains(site)).findFirst().orElse(null);
            if (null == accountConfig) {
                continue;
            }

            if (StringUtils.isBlank(skuSuffix) && StringUtils.isNotBlank(accountConfig.getSkuSuffix())) {
                skuSuffix = accountConfig.getSkuSuffix();
            }

            if (null == grossMargin && null != accountConfig.getGrossMargin()) {
                grossMargin = accountConfig.getGrossMargin();
            }
        }

        return Pair.of(skuSuffix, grossMargin);
    }


    public String getAccountSite(String accountNumber) {
        if (StringUtils.isBlank(accountNumber)) {
            return null;
        }

        if (!accountNumber.contains("-")) {
            throw new RuntimeException("无法获取该账号站点：" + accountNumber);
        }

        return StringUtils.substringAfterLast(accountNumber, "-");
    }


}