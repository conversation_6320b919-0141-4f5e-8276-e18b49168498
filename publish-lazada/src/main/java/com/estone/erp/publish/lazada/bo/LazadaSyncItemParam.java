package com.estone.erp.publish.lazada.bo;

import com.estone.erp.publish.lazada.enums.LazadaSyncItemTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/27 17:10
 * @description 同步产品参数类
 */
@Data
public class LazadaSyncItemParam {

    //更新类型
    private LazadaSyncItemTypeEnum syncItemType;

    //非全量更新时候用到
    private int day;

    //选择产品更新时用到
    private List<String> sellerSku;
}
