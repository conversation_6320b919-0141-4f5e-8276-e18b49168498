package com.estone.erp.publish.base.pms.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class InfringementProductExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public InfringementProductExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRecorderidIsNull() {
            addCriterion("recorderid is null");
            return (Criteria) this;
        }

        public Criteria andRecorderidIsNotNull() {
            addCriterion("recorderid is not null");
            return (Criteria) this;
        }

        public Criteria andRecorderidEqualTo(Integer value) {
            addCriterion("recorderid =", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidNotEqualTo(Integer value) {
            addCriterion("recorderid <>", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidGreaterThan(Integer value) {
            addCriterion("recorderid >", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidGreaterThanOrEqualTo(Integer value) {
            addCriterion("recorderid >=", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidLessThan(Integer value) {
            addCriterion("recorderid <", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidLessThanOrEqualTo(Integer value) {
            addCriterion("recorderid <=", value, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidIn(List<Integer> values) {
            addCriterion("recorderid in", values, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidNotIn(List<Integer> values) {
            addCriterion("recorderid not in", values, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidBetween(Integer value1, Integer value2) {
            addCriterion("recorderid between", value1, value2, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecorderidNotBetween(Integer value1, Integer value2) {
            addCriterion("recorderid not between", value1, value2, "recorderid");
            return (Criteria) this;
        }

        public Criteria andRecordtimeIsNull() {
            addCriterion("recordtime is null");
            return (Criteria) this;
        }

        public Criteria andRecordtimeIsNotNull() {
            addCriterion("recordtime is not null");
            return (Criteria) this;
        }

        public Criteria andRecordtimeEqualTo(Timestamp value) {
            addCriterion("recordtime =", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeNotEqualTo(Timestamp value) {
            addCriterion("recordtime <>", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeGreaterThan(Timestamp value) {
            addCriterion("recordtime >", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("recordtime >=", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeLessThan(Timestamp value) {
            addCriterion("recordtime <", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("recordtime <=", value, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeIn(List<Timestamp> values) {
            addCriterion("recordtime in", values, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeNotIn(List<Timestamp> values) {
            addCriterion("recordtime not in", values, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recordtime between", value1, value2, "recordtime");
            return (Criteria) this;
        }

        public Criteria andRecordtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recordtime not between", value1, value2, "recordtime");
            return (Criteria) this;
        }

        public Criteria andArticlenumberIsNull() {
            addCriterion("articlenumber is null");
            return (Criteria) this;
        }

        public Criteria andArticlenumberIsNotNull() {
            addCriterion("articlenumber is not null");
            return (Criteria) this;
        }

        public Criteria andArticlenumberEqualTo(String value) {
            addCriterion("articlenumber =", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberNotEqualTo(String value) {
            addCriterion("articlenumber <>", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberGreaterThan(String value) {
            addCriterion("articlenumber >", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberGreaterThanOrEqualTo(String value) {
            addCriterion("articlenumber >=", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberLessThan(String value) {
            addCriterion("articlenumber <", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberLessThanOrEqualTo(String value) {
            addCriterion("articlenumber <=", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberLike(String value) {
            addCriterion("articlenumber like", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberNotLike(String value) {
            addCriterion("articlenumber not like", value, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberIn(List<String> values) {
            addCriterion("articlenumber in", values, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberNotIn(List<String> values) {
            addCriterion("articlenumber not in", values, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberBetween(String value1, String value2) {
            addCriterion("articlenumber between", value1, value2, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andArticlenumberNotBetween(String value1, String value2) {
            addCriterion("articlenumber not between", value1, value2, "articlenumber");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIsNull() {
            addCriterion("productmanagerid is null");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIsNotNull() {
            addCriterion("productmanagerid is not null");
            return (Criteria) this;
        }

        public Criteria andProductmanageridEqualTo(Integer value) {
            addCriterion("productmanagerid =", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotEqualTo(Integer value) {
            addCriterion("productmanagerid <>", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridGreaterThan(Integer value) {
            addCriterion("productmanagerid >", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridGreaterThanOrEqualTo(Integer value) {
            addCriterion("productmanagerid >=", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridLessThan(Integer value) {
            addCriterion("productmanagerid <", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridLessThanOrEqualTo(Integer value) {
            addCriterion("productmanagerid <=", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIn(List<Integer> values) {
            addCriterion("productmanagerid in", values, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotIn(List<Integer> values) {
            addCriterion("productmanagerid not in", values, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridBetween(Integer value1, Integer value2) {
            addCriterion("productmanagerid between", value1, value2, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotBetween(Integer value1, Integer value2) {
            addCriterion("productmanagerid not between", value1, value2, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeIsNull() {
            addCriterion("infringementtype is null");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeIsNotNull() {
            addCriterion("infringementtype is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeEqualTo(String value) {
            addCriterion("infringementtype =", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeNotEqualTo(String value) {
            addCriterion("infringementtype <>", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeGreaterThan(String value) {
            addCriterion("infringementtype >", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeGreaterThanOrEqualTo(String value) {
            addCriterion("infringementtype >=", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeLessThan(String value) {
            addCriterion("infringementtype <", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeLessThanOrEqualTo(String value) {
            addCriterion("infringementtype <=", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeLike(String value) {
            addCriterion("infringementtype like", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeNotLike(String value) {
            addCriterion("infringementtype not like", value, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeIn(List<String> values) {
            addCriterion("infringementtype in", values, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeNotIn(List<String> values) {
            addCriterion("infringementtype not in", values, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeBetween(String value1, String value2) {
            addCriterion("infringementtype between", value1, value2, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtypeNotBetween(String value1, String value2) {
            addCriterion("infringementtype not between", value1, value2, "infringementtype");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetIsNull() {
            addCriterion("infringementtarget is null");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetIsNotNull() {
            addCriterion("infringementtarget is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetEqualTo(String value) {
            addCriterion("infringementtarget =", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetNotEqualTo(String value) {
            addCriterion("infringementtarget <>", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetGreaterThan(String value) {
            addCriterion("infringementtarget >", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetGreaterThanOrEqualTo(String value) {
            addCriterion("infringementtarget >=", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetLessThan(String value) {
            addCriterion("infringementtarget <", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetLessThanOrEqualTo(String value) {
            addCriterion("infringementtarget <=", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetLike(String value) {
            addCriterion("infringementtarget like", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetNotLike(String value) {
            addCriterion("infringementtarget not like", value, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetIn(List<String> values) {
            addCriterion("infringementtarget in", values, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetNotIn(List<String> values) {
            addCriterion("infringementtarget not in", values, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetBetween(String value1, String value2) {
            addCriterion("infringementtarget between", value1, value2, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andInfringementtargetNotBetween(String value1, String value2) {
            addCriterion("infringementtarget not between", value1, value2, "infringementtarget");
            return (Criteria) this;
        }

        public Criteria andDetailIsNull() {
            addCriterion("detail is null");
            return (Criteria) this;
        }

        public Criteria andDetailIsNotNull() {
            addCriterion("detail is not null");
            return (Criteria) this;
        }

        public Criteria andDetailEqualTo(String value) {
            addCriterion("detail =", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotEqualTo(String value) {
            addCriterion("detail <>", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThan(String value) {
            addCriterion("detail >", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThanOrEqualTo(String value) {
            addCriterion("detail >=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThan(String value) {
            addCriterion("detail <", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThanOrEqualTo(String value) {
            addCriterion("detail <=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLike(String value) {
            addCriterion("detail like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotLike(String value) {
            addCriterion("detail not like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailIn(List<String> values) {
            addCriterion("detail in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotIn(List<String> values) {
            addCriterion("detail not in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailBetween(String value1, String value2) {
            addCriterion("detail between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotBetween(String value1, String value2) {
            addCriterion("detail not between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathIsNull() {
            addCriterion("infringementproductimagepath is null");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathIsNotNull() {
            addCriterion("infringementproductimagepath is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathEqualTo(String value) {
            addCriterion("infringementproductimagepath =", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathNotEqualTo(String value) {
            addCriterion("infringementproductimagepath <>", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathGreaterThan(String value) {
            addCriterion("infringementproductimagepath >", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathGreaterThanOrEqualTo(String value) {
            addCriterion("infringementproductimagepath >=", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathLessThan(String value) {
            addCriterion("infringementproductimagepath <", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathLessThanOrEqualTo(String value) {
            addCriterion("infringementproductimagepath <=", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathLike(String value) {
            addCriterion("infringementproductimagepath like", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathNotLike(String value) {
            addCriterion("infringementproductimagepath not like", value, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathIn(List<String> values) {
            addCriterion("infringementproductimagepath in", values, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathNotIn(List<String> values) {
            addCriterion("infringementproductimagepath not in", values, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathBetween(String value1, String value2) {
            addCriterion("infringementproductimagepath between", value1, value2, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andInfringementproductimagepathNotBetween(String value1, String value2) {
            addCriterion("infringementproductimagepath not between", value1, value2, "infringementproductimagepath");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNull() {
            addCriterion("salechannel is null");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNotNull() {
            addCriterion("salechannel is not null");
            return (Criteria) this;
        }

        public Criteria andSalechannelEqualTo(String value) {
            addCriterion("salechannel =", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotEqualTo(String value) {
            addCriterion("salechannel <>", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThan(String value) {
            addCriterion("salechannel >", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThanOrEqualTo(String value) {
            addCriterion("salechannel >=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThan(String value) {
            addCriterion("salechannel <", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThanOrEqualTo(String value) {
            addCriterion("salechannel <=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLike(String value) {
            addCriterion("salechannel like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotLike(String value) {
            addCriterion("salechannel not like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelIn(List<String> values) {
            addCriterion("salechannel in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotIn(List<String> values) {
            addCriterion("salechannel not in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelBetween(String value1, String value2) {
            addCriterion("salechannel between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotBetween(String value1, String value2) {
            addCriterion("salechannel not between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSkuimageIsNull() {
            addCriterion("skuimage is null");
            return (Criteria) this;
        }

        public Criteria andSkuimageIsNotNull() {
            addCriterion("skuimage is not null");
            return (Criteria) this;
        }

        public Criteria andSkuimageEqualTo(String value) {
            addCriterion("skuimage =", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageNotEqualTo(String value) {
            addCriterion("skuimage <>", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageGreaterThan(String value) {
            addCriterion("skuimage >", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageGreaterThanOrEqualTo(String value) {
            addCriterion("skuimage >=", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageLessThan(String value) {
            addCriterion("skuimage <", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageLessThanOrEqualTo(String value) {
            addCriterion("skuimage <=", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageLike(String value) {
            addCriterion("skuimage like", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageNotLike(String value) {
            addCriterion("skuimage not like", value, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageIn(List<String> values) {
            addCriterion("skuimage in", values, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageNotIn(List<String> values) {
            addCriterion("skuimage not in", values, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageBetween(String value1, String value2) {
            addCriterion("skuimage between", value1, value2, "skuimage");
            return (Criteria) this;
        }

        public Criteria andSkuimageNotBetween(String value1, String value2) {
            addCriterion("skuimage not between", value1, value2, "skuimage");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidIsNull() {
            addCriterion("lastrecorderid is null");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidIsNotNull() {
            addCriterion("lastrecorderid is not null");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidEqualTo(Integer value) {
            addCriterion("lastrecorderid =", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidNotEqualTo(Integer value) {
            addCriterion("lastrecorderid <>", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidGreaterThan(Integer value) {
            addCriterion("lastrecorderid >", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidGreaterThanOrEqualTo(Integer value) {
            addCriterion("lastrecorderid >=", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidLessThan(Integer value) {
            addCriterion("lastrecorderid <", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidLessThanOrEqualTo(Integer value) {
            addCriterion("lastrecorderid <=", value, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidIn(List<Integer> values) {
            addCriterion("lastrecorderid in", values, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidNotIn(List<Integer> values) {
            addCriterion("lastrecorderid not in", values, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidBetween(Integer value1, Integer value2) {
            addCriterion("lastrecorderid between", value1, value2, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecorderidNotBetween(Integer value1, Integer value2) {
            addCriterion("lastrecorderid not between", value1, value2, "lastrecorderid");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeIsNull() {
            addCriterion("lastrecordtime is null");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeIsNotNull() {
            addCriterion("lastrecordtime is not null");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeEqualTo(Timestamp value) {
            addCriterion("lastrecordtime =", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeNotEqualTo(Timestamp value) {
            addCriterion("lastrecordtime <>", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeGreaterThan(Timestamp value) {
            addCriterion("lastrecordtime >", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("lastrecordtime >=", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeLessThan(Timestamp value) {
            addCriterion("lastrecordtime <", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("lastrecordtime <=", value, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeIn(List<Timestamp> values) {
            addCriterion("lastrecordtime in", values, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeNotIn(List<Timestamp> values) {
            addCriterion("lastrecordtime not in", values, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lastrecordtime between", value1, value2, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andLastrecordtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lastrecordtime not between", value1, value2, "lastrecordtime");
            return (Criteria) this;
        }

        public Criteria andCountIsNull() {
            addCriterion("`count` is null");
            return (Criteria) this;
        }

        public Criteria andCountIsNotNull() {
            addCriterion("`count` is not null");
            return (Criteria) this;
        }

        public Criteria andCountEqualTo(Integer value) {
            addCriterion("`count` =", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotEqualTo(Integer value) {
            addCriterion("`count` <>", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThan(Integer value) {
            addCriterion("`count` >", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("`count` >=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThan(Integer value) {
            addCriterion("`count` <", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThanOrEqualTo(Integer value) {
            addCriterion("`count` <=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountIn(List<Integer> values) {
            addCriterion("`count` in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotIn(List<Integer> values) {
            addCriterion("`count` not in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountBetween(Integer value1, Integer value2) {
            addCriterion("`count` between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotBetween(Integer value1, Integer value2) {
            addCriterion("`count` not between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemIsNull() {
            addCriterion("isexistincurrentsystem is null");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemIsNotNull() {
            addCriterion("isexistincurrentsystem is not null");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemEqualTo(Boolean value) {
            addCriterion("isexistincurrentsystem =", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemNotEqualTo(Boolean value) {
            addCriterion("isexistincurrentsystem <>", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemGreaterThan(Boolean value) {
            addCriterion("isexistincurrentsystem >", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isexistincurrentsystem >=", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemLessThan(Boolean value) {
            addCriterion("isexistincurrentsystem <", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemLessThanOrEqualTo(Boolean value) {
            addCriterion("isexistincurrentsystem <=", value, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemIn(List<Boolean> values) {
            addCriterion("isexistincurrentsystem in", values, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemNotIn(List<Boolean> values) {
            addCriterion("isexistincurrentsystem not in", values, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemBetween(Boolean value1, Boolean value2) {
            addCriterion("isexistincurrentsystem between", value1, value2, "isexistincurrentsystem");
            return (Criteria) this;
        }

        public Criteria andIsexistincurrentsystemNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isexistincurrentsystem not between", value1, value2, "isexistincurrentsystem");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}