package com.estone.erp.publish.platform.model;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> template_queue
 * 2019-08-14 09:50:22
 */
public class TemplateQueueCriteria extends TemplateQueue {
    private static final long serialVersionUID = 1L;
    
    private Integer perAccountPublishCount;

    private List<String> accountnumberList ;

    private Timestamp creationDateFrom;

    private Timestamp creationDateTo;

    private Timestamp startTimeFrom;

    private Timestamp startTimeTo;

    private Timestamp lessThanStartTime;

    private String sellerIds;

    private List<String> sellerIdLikeList;

    private String titleLike;

    private String templateIds;

    private String skus;

    private List<Integer> resultStatusList;

    public String getTitleLike() {
        return titleLike;
    }

    public void setTitleLike(String titleLike) {
        this.titleLike = titleLike;
    }

    public List<String> getSellerIdLikeList() {
        return sellerIdLikeList;
    }

    public void setSellerIdLikeList(List<String> sellerIdLikeList) {
        this.sellerIdLikeList = sellerIdLikeList;
    }

    public Integer getPerAccountPublishCount() {
        return perAccountPublishCount;
    }

    public void setPerAccountPublishCount(Integer perAccountPublishCount) {
        this.perAccountPublishCount = perAccountPublishCount;
    }

    public List<String> getAccountnumberList() {
        return accountnumberList;
    }

    public void setAccountnumberList(List<String> accountnumberList) {
        this.accountnumberList = accountnumberList;
    }

    public Timestamp getLessThanStartTime() {
        return lessThanStartTime;
    }

    public void setLessThanStartTime(Timestamp lessThanStartTime) {
        this.lessThanStartTime = lessThanStartTime;
    }

    public Timestamp getCreationDateFrom() {
        return creationDateFrom;
    }

    public void setCreationDateFrom(Timestamp creationDateFrom) {
        this.creationDateFrom = creationDateFrom;
    }

    public Timestamp getCreationDateTo() {
        return creationDateTo;
    }

    public void setCreationDateTo(Timestamp creationDateTo) {
        this.creationDateTo = creationDateTo;
    }

    public Timestamp getStartTimeFrom() {
        return startTimeFrom;
    }

    public void setStartTimeFrom(Timestamp startTimeFrom) {
        this.startTimeFrom = startTimeFrom;
    }

    public Timestamp getStartTimeTo() {
        return startTimeTo;
    }

    public void setStartTimeTo(Timestamp startTimeTo) {
        this.startTimeTo = startTimeTo;
    }

    public String getSellerIds() {
        return sellerIds;
    }

    public void setSellerIds(String sellerIds) {
        this.sellerIds = sellerIds;
    }

    public String getTemplateIds() {
        return templateIds;
    }

    public void setTemplateIds(String templateIds) {
        this.templateIds = templateIds;
    }

    public String getSkus() {
        return skus;
    }

    public void setSkus(String skus) {
        this.skus = skus;
    }

    public TemplateQueueExample getExample() {
        TemplateQueueExample example = new TemplateQueueExample();
        TemplateQueueExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isNotBlank(this.getProblemType())){
            criteria.andProblemTypeEqualTo(this.getProblemType());
        }


        if(StringUtils.isNotBlank(this.getTitleLike())){
            criteria.andTitleLike("%" + this.getTitleLike().trim() + "%");
        }

        if(CollectionUtils.isNotEmpty(this.getSellerIdLikeList())){
            criteria.andSellerIdLikeIn(this.getSellerIdLikeList());
        }

        if(this.getPublishResult() != null){
            criteria.andPublishResultEqualTo(this.getPublishResult());
        }
        if(CollectionUtils.isNotEmpty(resultStatusList)){
            if (resultStatusList.size() == 1) {
                criteria.andResultStatusEqualTo(resultStatusList.get(0));
            }else {
                criteria.andResultStatusIn(resultStatusList);
            }
        }

        if(this.getPublishRole() != null){
            criteria.andPublishRoleEqualTo(this.getPublishRole());
        }

        if (StringUtils.isNotBlank(this.getSaleChannel())) {
            criteria.andSaleChannelEqualTo(this.getSaleChannel());
        }

        if(this.getTimingType() != null){
            criteria.andTimingTypeEqualTo(this.getTimingType());
        }

        if (this.getTemplateId() != null) {
            criteria.andTemplateIdEqualTo(this.getTemplateId());
        }
        if (StringUtils.isNotBlank(this.getTemplateIds())) {
            List<Integer> templateIdList = new ArrayList<>();
            String[] templateIdArr = this.getTemplateIds().split(",");
            for (String templateIdStr : templateIdArr) {
                templateIdList.add(Integer.valueOf(templateIdStr));
            }
            criteria.andTemplateIdIn(templateIdList);
        }
         if(StringUtils.isNotBlank(this.getSellerIds())) {
            if(this.getSellerIds().contains(",")) {
                List<String> list = new ArrayList<>();
                for (String str : this.getSellerIds().split(",")) {
                    list.add(str);
                }
                criteria.andSellerIdIn(list);
            }else {
                criteria.andSellerIdEqualTo(this.getSellerIds());
            }
        }
        if (StringUtils.isNotBlank(this.getSellerId())) {
            criteria.andSellerIdEqualTo(this.getSellerId());
        }
        if (StringUtils.isNotBlank(this.getTitle())) {
            criteria.andTitleEqualTo(this.getTitle());
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            criteria.andSkuIn(Arrays.asList(this.getSkus().split(",")));
        }
        if (StringUtils.isNotBlank(this.getItemId())) {
            criteria.andItemIdEqualTo(this.getItemId());
        }
        if (this.getNextRunTime() != null) {
            criteria.andNextRunTimeEqualTo(this.getNextRunTime());
        }
        if (this.getPrevRunTime() != null) {
            criteria.andPrevRunTimeEqualTo(this.getPrevRunTime());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (this.getStartTime() != null) {
            criteria.andStartTimeEqualTo(this.getStartTime());
        }
        if (this.getEndTime() != null) {
            criteria.andEndTimeEqualTo(this.getEndTime());
        }
        if (this.getCreationDate() != null) {
            criteria.andCreationDateEqualTo(this.getCreationDate());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdatedBy())) {
            criteria.andLastUpdatedByEqualTo(this.getLastUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getRemark())) {
            criteria.andRemarkEqualTo(this.getRemark());
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(this.accountnumberList)){
            criteria.andSellerIdIn(this.accountnumberList);
        }
        if (StringUtils.isNotEmpty(this.getCreatedBy())){
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (null != this.getLessThanStartTime()){
            criteria.andStartTimeLessThan(this.getLessThanStartTime());
        }
        if(this.getCreationDateFrom() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(this.getCreationDateFrom());
        }
        if(this.getCreationDateTo() != null) {
            criteria.andCreationDateLessThanOrEqualTo(this.getCreationDateTo());
        }
        if(this.getStartTimeFrom() != null) {
            criteria.andStartTimeGreaterThanOrEqualTo(this.getStartTimeFrom());
        }
        if(this.getStartTimeTo() != null) {
            criteria.andStartTimeLessThanOrEqualTo(this.getStartTimeTo());
        }

        if(this.getTempType() != null){
            criteria.andTempTypeEqualTo(this.getTempType());
        }

        if(this.getRuleName() != null){
            criteria.andRuleNameEqualTo(this.getRuleName());
        }

        return example;
    }

    public List<Integer> getResultStatusList() {
        return resultStatusList;
    }

    public void setResultStatusList(List<Integer> resultStatusList) {
        this.resultStatusList = resultStatusList;
    }
}