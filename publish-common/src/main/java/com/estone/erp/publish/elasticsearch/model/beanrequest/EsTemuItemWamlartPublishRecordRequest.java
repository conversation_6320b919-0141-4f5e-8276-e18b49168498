package com.estone.erp.publish.elasticsearch.model.beanrequest;

import lombok.Data;

import java.util.List;

/**
 * @Auther yucm
 * @Date 2022/12/8
 */
@Data
public class EsTemuItemWamlartPublishRecordRequest {

    /**
     * 正序还是倒序，正序ASC,倒序DESC
     */
    private String sequence;
    private String orderBy;
    private String groupBy;
    private Integer pageSize = 200;

    /**
     * 商品id
     */
    private String itemId;
    private List<String> itemIds;

    /**
     * 刊登中数量范围
     */
    private Integer publishingQuantityGt;
    private Integer publishingQuantityLt;

    /**
     * 成功数量范围
     */
    private Integer successQuantityGt;
    private Integer successQuantityLt;

    /**
     * 可以设置需要查询的字段，不设置则取下方默认字段
     */
    private String [] queryFields = {
            "id", "itemId", "publishingAccounts", "publishingQuantity", "successAccounts", "successQuantity" , "createdTime", "updateTime"
    };

    private String [] PageFields = {
            "id", "itemId", "publishingAccounts", "publishingQuantity", "successAccounts", "successQuantity" , "createdTime", "updateTime"
    };
}
