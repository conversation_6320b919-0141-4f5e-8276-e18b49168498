package com.estone.erp.publish.system.product.enums;

public enum SpecialTagEnum {

    S_2000(2000, "防疫用品"),
    s_2001(2001, "客户定制"),
    s_1001(1001, "平销"),
    s_1002(1002, "热销"),
    s_1003(1003, "滞销"),
    s_1004(1004, "爆款"),
    s_2002(2002, "侵权"),
    s_1005(1005, "无货源"),
    s_1006(1006, "待货源处理"),
    s_1007(1007, "新品首单"),
    s_2003(2003, "已确认样品"),
    s_2004(2004, "独立站SKU"),
    s_2005(2005, "服装项目"),
    s_2006(2006, "已审版"),
    s_2007(2007, "FBA精铺"),
    s_1008(1008, "暂无其他货源"),
    s_2008(2008, "安全认证"),
    s_2009(2009, "贵重物品"),
    s_2010(2010, "二手产品"),
    s_2011(2011, "服装-年轻时尚系列"),
    s_2012(2012, "服装-欧美女装青年系列"),
    s_2013(2013, "服装-欧美大码女装系列"),
    s_2014(2014, "服装-日韩系列"),
    s_2016(2016, "服装-情趣类系列"),
    s_2017(2017, "服装-运动瑜伽系列"),
    s_2018(2018, "服装-居家贴身衣物系列"),
    s_2019(2019, "服装-穆斯林系列"),
    s_2020(2020, "服装-泳衣-东南亚"),
    s_2021(2021, "服装-泳衣-欧美"),
    s_2022(2022, "服装-穆斯林服饰"),
    s_2023(2023, "FZ"),
    s_2024(2024, "模特外拍"),
    s_2025(2025, "沃尔玛低风险产品"),
    s_2026(2026, "重点质检产品"),
    s_2027(2027, "战略SKU"),
    s_2028(2028, "AMZ特供"),
    s_2029(2029, "OZON线上物流禁运"),
    s_2030(2030, "SMT半托管禁售"),
    s_2031(2031, "SMT线上物流禁运"),
    s_2032(2032, "eBay真仓"),
    s_2036(2036, "NN滞销"),
    s_2037(2037, "TG-KF"),
    s_2038(2038, "TG-MZ"),
    s_2039(2039, "TG-IP"),
    s_2041(2041, "NN禁止刊登"),
    ;

    public int code;

    public String name;

    SpecialTagEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public static String getNameByCode(int co) {
        SpecialTagEnum[] values = SpecialTagEnum.values();
        for (SpecialTagEnum nn : values) {
            if (nn.code == co) {
                return nn.name;
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}
