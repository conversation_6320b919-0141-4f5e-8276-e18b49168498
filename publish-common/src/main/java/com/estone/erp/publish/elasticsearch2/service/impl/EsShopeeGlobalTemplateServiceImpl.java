package com.estone.erp.publish.elasticsearch2.service.impl;

import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.dao.EsShopeeGlobalTemplateRepository;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeGlobalTemplateRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeGlobalTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Auther yucm
 * @Date 2022/9/16
 */
@Slf4j
@Service
public class EsShopeeGlobalTemplateServiceImpl implements EsShopeeGlobalTemplateService {

    private IndexCoordinates shopeeGlobalTemplateIndexCoordinates = IndexCoordinates.of("shopee_global_template");

    @Autowired
    private EsShopeeGlobalTemplateRepository esShopeeGlobalTemplateRepository;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Override
    public long count() {
        return esShopeeGlobalTemplateRepository.count();
    }

    @Override
    public void save(EsShopeeGlobalTemplate esShopeeGlobalTemplate) {
        if(esShopeeGlobalTemplate != null){
            elasticsearchRestTemplate2.save(esShopeeGlobalTemplate);
        }
    }

    @Override
    public void saveAll(List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates) {
        if(CollectionUtils.isNotEmpty(esShopeeGlobalTemplates)){
            elasticsearchRestTemplate2.save(esShopeeGlobalTemplates);
        }
    }

    @Override
    public void deleteById(Long id) {
        if(null != id) {
            esShopeeGlobalTemplateRepository.deleteById(id);
        }
    }

    @Override
    public EsShopeeGlobalTemplate findAllById(Long id) {
        return elasticsearchRestTemplate2.get(id + "", EsShopeeGlobalTemplate.class);
    }

    private void setQuery(EsShopeeGlobalTemplateRequest request, BoolQueryBuilder builder) {
        // id
        if(!ObjectUtils.isEmpty(request.getId())) {
            builder.must(QueryBuilders.termQuery("id", request.getId()));
        }
        if(!ObjectUtils.isEmpty(request.getNotId())) {
            builder.mustNot(QueryBuilders.termQuery("id", request.getNotId()));
        }
        if(StringUtils.isNotBlank(request.getIdStr())){
            String idStr = request.getIdStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(idStr, ",");
            builder.must(QueryBuilders.termsQuery("id", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getIds())){
            builder.must(QueryBuilders.termsQuery("id", request.getIds()));
        }
        if (request.getGtId() != null) {
            builder.must(QueryBuilders.rangeQuery("id").gt(request.getGtId()));
        }

        // 子账号 subAccount
        if(StringUtils.isNotBlank(request.getSubAccount())){
            builder.must(QueryBuilders.termQuery("subAccount", request.getSubAccount()));
        }
        if(StringUtils.isNotBlank(request.getSubAccountStr())){
            String subAccountStr = request.getSubAccountStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(subAccountStr, ",");
            builder.must(QueryBuilders.termsQuery("subAccount", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getSubAccounts())){
            builder.must(QueryBuilders.termsQuery("subAccount", request.getSubAccounts()));
        }

        // 商家id merchantId
        if(StringUtils.isNotBlank(request.getMerchantId())){
            builder.must(QueryBuilders.termQuery("merchantId", request.getMerchantId()));
        }
        if(StringUtils.isNotBlank(request.getMerchantIdStr())){
            String merchantIdStr = request.getMerchantIdStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(merchantIdStr, ",");
            builder.must(QueryBuilders.termsQuery("merchantId", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getMerchantIds())){
            builder.must(QueryBuilders.termsQuery("merchantId", request.getMerchantIds()));
        }

        // 店铺 accounts
        if(StringUtils.isNotBlank(request.getAccountsStr())){
            String accountsStr = request.getAccountsStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(accountsStr, ",");
            builder.must(QueryBuilders.termsQuery("accounts", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getAccountsList())){
            builder.must(QueryBuilders.termsQuery("accounts", request.getAccountsList()));
        }

        // sku
        if(StringUtils.isNotBlank(request.getSku())){
            builder.must(QueryBuilders.termQuery("sku", request.getSku()));
        }
        if(StringUtils.isNotBlank(request.getSkuStr())){
            String skuStr = request.getSkuStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(skuStr, ",");
            builder.must(QueryBuilders.termsQuery("sku", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getSkus())){
            builder.must(QueryBuilders.termsQuery("sku", request.getSkus()));
        }

        // mtsku
        if(StringUtils.isNotBlank(request.getMtsku())){
            builder.must(QueryBuilders.termQuery("mtsku", request.getMtsku()));
        }
        if(StringUtils.isNotBlank(request.getMtskuStr())){
            String mtskuStr = request.getMtskuStr().replace("，",",").trim();
            List<String> strs = CommonUtils.splitList(mtskuStr, ",");
            builder.must(QueryBuilders.termsQuery("mtsku", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getMtskus())){
            builder.must(QueryBuilders.termsQuery("mtsku", request.getMtskus()));
        }

        // 平台分类ID categoryId
        if(null != request.getCategoryId()){
            builder.must(QueryBuilders.termQuery("categoryId", request.getCategoryId()));
        }

        // name
        if(StringUtils.isNotBlank(request.getNameLike())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("name", "*" + request.getNameLike() + "*"));
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        // 刊登状态 publishStatus
        if(null != request.getPublishStatus()){
            builder.must(QueryBuilders.termQuery("publishStatus", request.getPublishStatus()));
        }
        if(CollectionUtils.isNotEmpty(request.getPublishStatusList())){
            builder.must(QueryBuilders.termsQuery("publishStatus", request.getPublishStatusList()));
        }

        // 创建人
        if(StringUtils.isNotBlank(request.getCreatedBy())){
            builder.must(QueryBuilders.termQuery("createdBy", request.getCreatedBy()));
        }

        if (CollectionUtils.isNotEmpty(request.getCreatedByList())) {
            builder.must(QueryBuilders.termsQuery("createdBy", request.getCreatedByList()));
        }


        // 修改人
        if(StringUtils.isNotBlank(request.getLastUpdatedBy())){
            builder.must(QueryBuilders.termQuery("lastUpdatedBy", request.getLastUpdatedBy()));
        }

        // 创建时间 creationDate
        if(!ObjectUtils.isEmpty(request.getFromCreateDate())) {
            builder.must(QueryBuilders.rangeQuery("createDate").from(request.getFromCreateDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToCreateDate())) {
            builder.must(QueryBuilders.rangeQuery("createDate").to(request.getToCreateDate()));
        }
        // 修改时间
        if(!ObjectUtils.isEmpty(request.getFromLastUpdateDate())) {
            builder.must(QueryBuilders.rangeQuery("lastUpdateDate").from(request.getFromLastUpdateDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToLastUpdateDate())) {
            builder.must(QueryBuilders.rangeQuery("lastUpdateDate").to(request.getToLastUpdateDate()));
        }

        // 刊登类型 type
        if(null != request.getType()){
            builder.must(QueryBuilders.termQuery("type", request.getType()));
        }

        // 刊登角色 publishRole
        if(null != request.getPublishRole()){
            builder.must(QueryBuilders.termQuery("publishRole", request.getPublishRole()));
        }

        // 数据来源 dataSource
        if(null != request.getDataSource()){
            builder.must(QueryBuilders.termQuery("dataSource", request.getDataSource()));
        }

        // 是否范本 true范本 false模板
        if(null != request.getIsParent()) {
            builder.must(QueryBuilders.termQuery("isParent", request.getIsParent()));
        }

        // 母版id
        if(CollectionUtils.isNotEmpty(request.getParentIds())) {
            builder.must(QueryBuilders.termsQuery("parentId", request.getParentIds()));
        }

        // 发布步骤
        if(StringUtils.isNotBlank(request.getPublishSteps())) {
            builder.must(QueryBuilders.termQuery("publishSteps", request.getPublishSteps()));
        }

        // 全球产品id globalItemId
        if(null != request.getGlobalItemId()) {
            builder.must(QueryBuilders.termQuery("globalItemId", request.getGlobalItemId()));
        }
        if(CollectionUtils.isNotEmpty(request.getGlobalItemIds())) {
            builder.must(QueryBuilders.termsQuery("globalItemId", request.getGlobalItemIds()));
        }

        // shop对象
        BoolQueryBuilder shopsBool = QueryBuilders.boolQuery();
        // 店铺发布到平台状态
        if(CollectionUtils.isNotEmpty(request.getShopPublishStatusList())) {
            shopsBool.must(QueryBuilders.termsQuery("esShopeeGlobalTemplateShops.publishStatus", request.getShopPublishStatusList()));
        }

        // 店铺本地刊登状态
        if(CollectionUtils.isNotEmpty(request.getShopLocalStatusList())) {
            shopsBool.must(QueryBuilders.termsQuery("esShopeeGlobalTemplateShops.localStatus", request.getShopLocalStatusList()));
        }
        // shop对象店铺
        if(StringUtils.isNotBlank(request.getShopAccountNumber())) {
            shopsBool.must(QueryBuilders.termQuery("esShopeeGlobalTemplateShops.accountNumber", request.getShopAccountNumber()));
        }
        if(CollectionUtils.isNotEmpty(request.getShopAccountNumberList())) {
            shopsBool.must(QueryBuilders.termsQuery("esShopeeGlobalTemplateShops.accountNumber", request.getShopAccountNumberList()));
        }

        // 店铺创建时间 creationDate
        if(!ObjectUtils.isEmpty(request.getFromShopCreateDate())) {
            shopsBool.must(QueryBuilders.rangeQuery("esShopeeGlobalTemplateShops.createDate").from(request.getFromShopCreateDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToShopCreateDate())) {
            shopsBool.must(QueryBuilders.rangeQuery("esShopeeGlobalTemplateShops.createDate").to(request.getToShopCreateDate()));
        }
        if (CollectionUtils.isNotEmpty(shopsBool.filter())
                || CollectionUtils.isNotEmpty(shopsBool.must())
                || CollectionUtils.isNotEmpty(shopsBool.should())
                || CollectionUtils.isNotEmpty(shopsBool.mustNot()) ) {
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("esShopeeGlobalTemplateShops", shopsBool, ScoreMode.Total);
            builder.filter(nestedQuery);
        }
    }

    @Override
    public List<EsShopeeGlobalTemplate> getEsShopeeGlobalTemplates(EsShopeeGlobalTemplateRequest request) {
        if(null == request) {
            return Collections.emptyList();
        }

        List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates = new ArrayList<>();

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(request, boolQueryBuilder);

        String[] queryFields = request.getQueryFields();
        if(queryFields == null){
            queryBuilder.withQuery(boolQueryBuilder);
        }else{
            queryBuilder.withQuery(boolQueryBuilder)
                    .withFields(queryFields);
        }

        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, request.getPageSize())).build();
        /*int i = 0;
        long start1 = System.currentTimeMillis();
        ScrolledPage<EsShopeeGlobalTemplate> scroll = (ScrolledPage<EsShopeeGlobalTemplate>) c2ElasticsearchTemplate
                .startScroll(1000*60*2, searchQuery, EsShopeeGlobalTemplate.class);
        long o1 = System.currentTimeMillis() - start1;
        if(o1 > 2000L){
            log.info("查询ES->esShopeeGlobalTemplate{}页耗时{}ms", i, o1);
        }
        while (scroll.hasContent()) {
            i++;
            esShopeeGlobalTemplates.addAll(scroll.getContent());
            long start2 = System.currentTimeMillis();
            scroll = (ScrolledPage<EsShopeeGlobalTemplate>) c2ElasticsearchTemplate.continueScroll(scroll.getScrollId(), 1000*60*2,
                    EsShopeeGlobalTemplate.class);
            long o11 = System.currentTimeMillis() - start2;
            if(o11 > 2000L){
                log.info("查询ES->esShopeeGlobalTemplate{}页耗时{}ms", i, o11);
            }
        }
        // 最后释放查询
        c2ElasticsearchTemplate.clearScroll(scroll.getScrollId());*/

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        esShopeeGlobalTemplates = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate2, 10 * 60 * 1000,
                        searchQuery, EsShopeeGlobalTemplate.class,
                        shopeeGlobalTemplateIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 3000L){
            log.warn("查询ES->esShopeeGlobalTemplate 条数{}耗时{}ms", esShopeeGlobalTemplates.size(), totalTimeMillis);
        }
        return esShopeeGlobalTemplates;
    }

    @Override
    public Page<EsShopeeGlobalTemplate> page(EsShopeeGlobalTemplateRequest request, int pageSize, Integer offset) {
        if(null == request){
            return null;
        }

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 设置查询条件
        setQuery(request, boolQueryBuilder);

        // 创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        // 前端默认传的是偏移量
        offset = offset == null ? 0 : offset;
        //es的分页的页码从0开始
        int pageIndex = offset / pageSize;
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        // 排序
        String orderby = request.getOrderBy();
        if (StringUtils.isBlank(orderby)) {
            orderby = "lastUpdateDate";
        }
        String sequence = request.getSequence();
        if (StringUtils.isBlank(sequence) || sequence.equalsIgnoreCase("DESC")) {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.ASC));
        }

        queryBuilder.withFields(request.getPageFields()).withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsShopeeGlobalTemplate> results = esShopeeGlobalTemplateRepository.search(searchQuery);
        return results;
    }

    @Override
    public Map<String, Integer> getCreatedByTemplateSumMap(EsShopeeGlobalTemplateRequest request) {
        if(null == request) {
            return null;
        }

        long now = System.currentTimeMillis();
        Map<String, Integer> createdByTemplateSumMap = new HashMap<>();
        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            setQuery(request, boolQueryBuilder);

            // 按创建人分组
            TermsAggregationBuilder createdByBuilder = AggregationBuilders.terms("templateGroup").field("createdBy").size((1<<31)-1);

            // sum
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("createdBy", "id")
                    .addAggregation(createdByBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsShopeeGlobalTemplate> search = (AggregatedPage) esShopeeGlobalTemplateRepository.search(query);
            if (null != search) {
                Map<String, Aggregation> asMap = search.getAggregations().getAsMap();
                MultiBucketsAggregation term = (MultiBucketsAggregation) asMap.get("templateGroup");
                if (null != term) {
                    for (MultiBucketsAggregation.Bucket bucket : term.getBuckets()) {
                        String key = (String) bucket.getKey();
                        if (key == null || StringUtils.isBlank(key.toString())) continue;
                        createdByTemplateSumMap.put(key, new Long(bucket.getDocCount()).intValue());
                    }
                }
            }

            long timeES = System.currentTimeMillis() - now;
            log.info("获取条件范围内账号对应listing数量，查询ES->esShopeeGlobalTemplate,耗时->{}ms", timeES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取条件范围内账号对应listing数量 es 查询失败！");
        }
        return createdByTemplateSumMap;
    }


    @Override
    public AggregatedPage<EsShopeeGlobalTemplate> getAccountNumberLocalStatusShopSumMap(EsShopeeGlobalTemplateRequest request) {
        if(null == request) {
            return null;
        }

        long now = System.currentTimeMillis();
        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            setQuery(request, boolQueryBuilder);

            // 分组显示的字段
            String[] fields = new String[]{"esShopeeGlobalTemplateShops.site", "esShopeeGlobalTemplateShops.createdBy"};
            TopHitsAggregationBuilder topBuilder = AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1);

            // 按店铺 刊登状态分组
            TermsAggregationBuilder localStatusGroupBuilder = AggregationBuilders.terms("localStatusGroup").field("esShopeeGlobalTemplateShops.localStatus").subAggregation(topBuilder).size((1<<31)-1);
            TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("accountNumberGroup").field("esShopeeGlobalTemplateShops.accountNumber").subAggregation(localStatusGroupBuilder).size((1<<31)-1);

            // nested 嵌套需要用到这个
            NestedAggregationBuilder nestedAggregationBuilder = AggregationBuilders.nested("shopsGroup","esShopeeGlobalTemplateShops")
                    .subAggregation(accountBuilder);

            // sum
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("esShopeeGlobalTemplateShops.accountNumber", "id")
                    .addAggregation(nestedAggregationBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsShopeeGlobalTemplate> search = (AggregatedPage) esShopeeGlobalTemplateRepository.search(query);

            log.info("获取条件范围内账号对应listing数量，查询ES->esShopeeGlobalTemplate,耗时->{}ms", System.currentTimeMillis() - now);
            return search;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取条件范围内账号对应listing数量 es 查询失败！" + e.getMessage());
        }
        return null;
    }
}
