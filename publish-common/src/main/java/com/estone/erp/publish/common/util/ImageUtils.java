package com.estone.erp.publish.common.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CopyObjectResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.common.oss.AliOss;
import com.estone.erp.publish.common.oss.OssMultiplexUtils;
import com.estone.erp.publish.common.oss.OssUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class ImageUtils {
    public static final Random random = new Random();
    private static String FILL_SYSTEM_URl ="http://************:8888/";

    private static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);

    public static String transferImageUrl(String image) {
        if (image != null && image.contains(Env.FILE_SERVER_HOST)) {
            return image.replace(Env.FILE_SERVER_HOST, Env.PUBLIC_FILE_SERVER_HOST + ":" + Env.PUBLIC_FILE_SERVER_PORT);
        }
        else if (image != null) {
            return transferFileServiceImageUrl(image);
        }

        return image;
    }

    /**
     * 替换文件系统的 图片地址
     *
     * @param image
     * @return
     */
    public static String transferFileServiceImageUrl(String image) {

        // 图片url前缀
        SystemParam fileParam = systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.image_prefix");

        if (fileParam != null && StringUtils.isNotBlank(fileParam.getParamValue())) {
            String paramValue = fileParam.getParamValue();

            if (StringUtils.contains(image, paramValue)) {
                image = image.replace(paramValue,
                        Env.PUBLIC_FILE_SERVER_HOST + ":" + Env.PUBLIC_FILE_SERVER_PORT + "/fms");
            }
        }

        return image;
    }

    public static String transferFileServiceImageUrlForEbay(String image) {

        // 图片url前缀
        SystemParam fileParam = systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.image_prefix");

        if (fileParam != null && StringUtils.isNotBlank(fileParam.getParamValue())) {
            String paramValue = fileParam.getParamValue();

            if (StringUtils.contains(image, paramValue)) {
                image = image.replace(paramValue,"photo.hjuge.top/fms");
            }
        }

        return image;
    }

    public static byte[] getFileStream(String url) throws Exception{
        try {
            URL httpUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection)httpUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10 * 1000);
            InputStream inStream = conn.getInputStream();//通过输入流获取图片数据
            byte[] btImg = readInputStream(inStream);//得到图片的二进制数据
            return btImg;
        } catch (Exception e) {
            throw e;
        }
    }


    public static byte[] downloadAndModifyImage(String url) {
        try {
            BufferedImage image = ImageIO.read(new URL(url));
            if (image == null) {
                log.error("读取图片失败，可能是URL无效：{}", url);
                return null;
            }

            int width = image.getWidth();
            int height = image.getHeight();
            if (width == 0 || height == 0) {
                log.error("图片尺寸异常，width={} height={}，url={}", width, height, url);
                return null;
            }

            // 随机位置
            int x = random.nextInt(width);
            int y = random.nextInt(height);

            // 设置一个透明色像素
            image.setRGB(x, y, new Color(0, 0, 0, 0).getRGB());

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                ImageIO.write(image, "jpg", baos);
                return baos.toByteArray();
            }
        } catch (Exception e) {
            log.error("处理图片失败！url={}, 错误={}", url, e.getMessage());
            return null;
        }
    }


    /**
     * 向图片添加噪点
     *
     * @param original
     * @return
     */
    public static byte[] modifyImage(BufferedImage original) {
        try {
            int width = original.getWidth();
            int height = original.getHeight();

            BufferedImage argbImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g = argbImage.createGraphics();
            g.drawImage(original, 0, 0, null);
            g.dispose();

            int x = ThreadLocalRandom.current().nextInt(width);
            int y = ThreadLocalRandom.current().nextInt(height);
            argbImage.setRGB(x, y, new Color(0, 0, 0, 0).getRGB());

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                ImageIO.write(argbImage, "png", baos);
                return baos.toByteArray();
            }
        } catch (Exception e) {
            log.error("图片添加噪点失败：{}", e.getMessage(), e);
            return null;
        }
    }

    public static byte[] getLocalFileStream(String url) throws Exception{
        try {
            InputStream inStream = new FileInputStream(url);
            byte[] btImg = readInputStream(inStream);//得到的二进制数据
            return btImg;
        } catch (Exception e) {
            throw e;
        }
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while( (len=inStream.read(buffer)) != -1 ){
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }

    public static void uploadOss(Map<String, Object> fileMap) throws Exception{
        AliOss aliOss = AliOss.Amazon_Image;
        OSS ossClient = OssUtils.getOssClientSingle(AliOss.Amazon_Image);
        try {
            fileMap.forEach((name, data) -> {
                if (data instanceof byte[]) {
                    OssUtils.uploadObj(ossClient, aliOss.getBucketname(), name, (byte[]) data);
                }
                else if (data instanceof String) {
                    OssUtils.uploadObj(ossClient, aliOss.getBucketname(), name, ((String) data).getBytes());
                }
                else if (data instanceof File) {
                    OssUtils.uploadObj(ossClient, aliOss.getBucketname(), name, (File) data);
                }
            });
        }catch(Exception e) {
            throw e;
        }finally {
            ossClient.shutdown();
        }
    }

    /***
     * 图片镜像处理
     * @param file
     * @param FX 0 为上下反转 1 为左右反转
     * @param format 格式
     * @return
     */
    public static void imageMisro(File file,int FX,String format)
    {
        try
        {
            BufferedImage bufferedimage = ImageIO.read(file);
            int w = bufferedimage.getWidth();
            int h = bufferedimage.getHeight();
            int[][] datas = new int[w][h];
            for (int i = 0; i < h; i++) {
                for (int j = 0; j < w; j++) {
                    datas[j][i] = bufferedimage.getRGB(j, i);
                }
            }
            int[][] tmps = new int[w][h];
            if (FX == 0) {
                for (int i = 0, a = h - 1; i < h; i++, a--) {
                    for (int j = 0; j < w; j++) {
                        tmps[j][a] = datas[j][i];
                    }
                }
            } else if (FX == 1) {
                for (int i = 0; i < h; i++) {
                    for (int j = 0, b = w - 1; j < w; j++, b--) {
                        tmps[b][i] = datas[j][i];
                    }
                }
            }
            for (int i = 0; i < h; i++){
                for (int j = 0; j<w ;j++){
                    bufferedimage.setRGB(j, i, tmps[j][i]);
                }
            }
            ImageIO.write(bufferedimage, format, file);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /***
     * 图片镜像处理
     * @param file
     * @return
     */
    public static File rotateImage180(File file, String format) throws Exception{
        try {
            BufferedImage bufferedimage = ImageIO.read(file);
            int w = bufferedimage.getWidth();
            int h = bufferedimage.getHeight();
            BufferedImage img;
            Graphics2D graphics2d;

            int type = bufferedimage.getColorModel().getTransparency();
            (graphics2d = (img = new BufferedImage(w, h, type)).createGraphics()).setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            graphics2d.rotate(Math.toRadians(180), (double) w / 2, (double) h / 2);
            graphics2d.drawImage(bufferedimage, 0, 0, null);
            graphics2d.dispose();

            ImageIO.write(img, format, file);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }


    public static void imageMisro(File file,int FX)
    {
        try
        {
            BufferedImage bufferedimage = ImageIO.read(file);
            int w = bufferedimage.getWidth();
            int h = bufferedimage.getHeight();
            int[][] datas = new int[w][h];
            for (int i = 0; i < h; i++) {
                for (int j = 0; j < w; j++) {
                    datas[j][i] = bufferedimage.getRGB(j, i);
                }
            }
            int[][] tmps = new int[w][h];
            if (FX == 0) {
                for (int i = 0, a = h - 1; i < h; i++, a--) {
                    for (int j = 0; j < w; j++) {
                        tmps[j][a] = datas[j][i];
                    }
                }
            } else if (FX == 1) {
                for (int i = 0; i < h; i++) {
                    for (int j = 0, b = w - 1; j < w; j++, b--) {
                        tmps[b][i] = datas[j][i];
                    }
                }
            }
            for (int i = 0; i < h; i++){
                for (int j = 0; j<w ;j++){
                    bufferedimage.setRGB(j, i, tmps[j][i]);
                }
            }
            ImageIO.write(bufferedimage, "jpg", file);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void downloadFile(String relativePath, File file) {
        try {
            URL url = new URL(relativePath);
            URLConnection con = url.openConnection();
            con.setConnectTimeout(40 * 1000);

            try (InputStream is = con.getInputStream();
                 FileOutputStream os = new FileOutputStream(file)) {

                byte[] data = new byte[1024];
                int len;
                while ((len = is.read(data)) != -1) {
                    os.write(data, 0, len);
                }
            }
        } catch (IOException e) {
            log.error(String.format("从文件系统下载失败路径%s, 报错信息：%s", relativePath, e.getMessage()));
        }
    }

    /**
     * httP://************:8888/amazon/
     * httP://************:8888/public/
     * httP://************:8888/aliexpress/
     * @param aliOss
     * @param orignalImageUrl
     * @param newUrl
     * @return
     */
    // 检测是原图就从oss复制图片
    public static boolean checkIsOriginalCopyObject(AliOss aliOss,String orignalImageUrl,String newUrl) {
        try {
            if (StringUtils.isEmpty(orignalImageUrl) || !ImageUtils.checkIsOriginal(orignalImageUrl)) {
                return false;
            }
            String sourceBucketName = AliOss.Original_Image.getBucketname();
            String sourceKey = orignalImageUrl.replaceAll(FILL_SYSTEM_URl, "");
            String destinationKey = newUrl.replaceAll(aliOss.getPrefix(), "");
            CopyObjectResult result = OssUtils.CopyObject(aliOss, sourceBucketName, sourceKey, aliOss.getBucketname(), destinationKey);
            if (null != result && StringUtils.isNotEmpty(result.getETag())) {
                return true;
            }
        }catch (Exception e){
            throw e;
        }
        return false;
    }


    // 检测是原图就从oss复制图片
    public static boolean checkIsOriginalCopyObject(OSS ossClient,String orignalImageUrl,String newUrl,AliOss aliOss) {
        try {
            if (StringUtils.isEmpty(orignalImageUrl) || !ImageUtils.checkIsOriginal(orignalImageUrl)) {
                return false;
            }
            String sourceBucketName = AliOss.Original_Image.getBucketname();
            String sourceKey = orignalImageUrl.replaceAll(FILL_SYSTEM_URl, "");
            String destinationKey = newUrl.replaceAll(aliOss.getPrefix(), "");
            CopyObjectResult result = OssMultiplexUtils.CopyObject(ossClient, sourceBucketName, sourceKey, aliOss.getBucketname(), destinationKey);
            if (null != result && StringUtils.isNotEmpty(result.getETag())) {
                return true;
            }
        }catch (Exception e){
            throw e;
        }
        return false;
    }

    // 检测是原图就从oss复制图片
    public static boolean checkIsOriginal(String orignalImageUrl) {
            if (StringUtils.isNotBlank(orignalImageUrl)
                    && (orignalImageUrl.contains(FILL_SYSTEM_URl  + PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName() +"/")
                    || orignalImageUrl.contains(FILL_SYSTEM_URl  + PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName() +"/")
                    || orignalImageUrl.contains(FILL_SYSTEM_URl  + PictureTypeEnum.SMT_PRODUCT_PLAT.getName() +"/")
                    || orignalImageUrl.contains(FILL_SYSTEM_URl + PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName() +"/")
                    || orignalImageUrl.contains(FILL_SYSTEM_URl + PictureTypeEnum.AMAZON_EXCLUSIVE_IMAGE.getName() +"/"))) {
                return true;
            }
        return false;
    }

    public static byte[] getImageUrlBytes(String imageUrl) throws RuntimeException {
        try {
            URL url = new URL(imageUrl);
            try (InputStream inputStream = url.openStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                // 读取流数据并写入到字节数组输出流
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
