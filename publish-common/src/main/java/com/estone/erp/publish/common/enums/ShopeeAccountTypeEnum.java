package com.estone.erp.publish.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 店铺类型
 */
public enum ShopeeAccountTypeEnum {

    GENERAL(0, "普通店铺"),

    TOP_SELLER(1, "优选店铺");

    @Getter
    private final Integer type;

    @Getter
    private final String desc;

    ShopeeAccountTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean containType(Integer type) {
        if (type == null) {
            return false;
        }
        ShopeeAccountTypeEnum[] values = values();
        for (ShopeeAccountTypeEnum value : values) {
            if (Objects.equals(value.type, type)) {
                return true;
            }
        }
        return false;
    }

    public static String getDesc(Integer type) {
        if (type == null) {
            return null;
        }
        ShopeeAccountTypeEnum[] values = values();
        for (ShopeeAccountTypeEnum value : values) {
            if (Objects.equals(value.type, type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
