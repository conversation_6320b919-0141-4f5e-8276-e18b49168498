package com.estone.erp.publish.component;

import com.estone.erp.common.constant.SmtConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.EmployeeInfoDO;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-05-06 15:55
 */
@Slf4j
@Component
public class PermissionsHelper {

    @Autowired
    private SaleAccountService saleAccountService;

    public static final NewUsermgtClient newUsermgtClient = SpringUtils.getBean(NewUsermgtClient.class);
    /**
     * 判断是否特殊处理陈凤松权限
     *
     * @param isNotSpecialHandleCfsAuth
     * @param saleChannel
     * @param employeeNo
     * @return
     */
    public static boolean judgeIsSpecialHandleCfsAuth(Boolean isNotSpecialHandleCfsAuth, String saleChannel, String employeeNo) {

        return !BooleanUtils.isTrue(isNotSpecialHandleCfsAuth) &&
                StringUtils.equals(saleChannel, SaleChannelEnum.ALIEXPRESS.getChannelName()) && StringUtils.equals(employeeNo, SmtConstant.CFS_EMPLOYEE_NO);
    }


    public List<String> smtAuth(List<String> accountNumbers, List<String> managerIds, List<String> leaderIds, List<String> saleIds,
                                List<Integer> groupIds, String isOnlySelf, Boolean isNotSpecialHandleCfsAuth) {
        //默认只查自己，但当有选择销售，组长，主管,店铺分组的时候，需要查全部
        if (null == isOnlySelf && CollectionUtils.isEmpty(saleIds) && CollectionUtils.isEmpty(managerIds)
                && CollectionUtils.isEmpty(leaderIds) && CollectionUtils.isEmpty(groupIds)) {
            isOnlySelf = "1";
        }

        List<String> currentUserPermission = getCurrentUserPermission(accountNumbers, managerIds, leaderIds, saleIds, SaleChannel.CHANNEL_SMT, true, isOnlySelf);

        // 获取当前登录用户信息
        String employeeNo = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        // 判断是否特殊处理陈凤松权限
        if (judgeIsSpecialHandleCfsAuth(isNotSpecialHandleCfsAuth, SaleChannelEnum.ALIEXPRESS.getChannelName(), employeeNo)) {
            ApiResult<NewUser> newUserResult = NewUsermgtUtils.getUserByNo(employeeNo);
            if (!newUserResult.isSuccess()) {
                throw new BusinessException(newUserResult.getErrorMsg());
            }
            List<Integer> employeeIds = Collections.singletonList(newUserResult.getResult().getEmployeeId());
            // 把陈凤松当作普通销售处理,去查询店铺
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel(SaleChannelEnum.ALIEXPRESS.getChannelName());
            request.setEmployeeIds(employeeIds);
            List<String> accountListByEs = EsAccountUtils.getAccountListByEs(request);
            if (CollectionUtils.isEmpty(currentUserPermission)) {
                return accountListByEs;
            } else {
                currentUserPermission.removeIf(account -> !currentUserPermission.contains(account));
            }
        }
        return currentUserPermission;
    }


    public Boolean isSuperAdminOrSupervisor(String saleChannel) {
        ApiResult<Boolean> superAdminOrSupervisor = NewUsermgtUtils.isSuperAdminOrSupervisor(saleChannel);
        if (!superAdminOrSupervisor.isSuccess()) {
            return false;
        }
        return superAdminOrSupervisor.getResult();
    }

    public Boolean isSuperAdminOrEquivalent(String saleChannel) {
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        if (!superAdminResult.isSuccess()) {
            return false;
        }
        return superAdminResult.getResult();
    }

    /**
     * 获取当前用户是否为超级管理员或者平台主管
     */
    public Boolean isSuperAdminOrBigSupervisor(String saleChannel) {
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdminOrBigSupervisor(saleChannel);
        if (!superAdminResult.isSuccess()) {
            return false;
        }
        return superAdminResult.getResult();
    }

    public Integer getEmployeeId(String employeeNo) {
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            return null;
        }
        NewUser user = newUserApiResult.getResult();
        return user.getEmployeeId();
    }

    /**
     * @param saleChannel
     * @param isOnlySelf
     * @return
     */
    public List<String> getCurrentPermissionAccount(String saleChannel, boolean isOnlySelf, boolean dataSupper) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);

        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }
        // 非超级管理员 和 非数据分析组
        boolean dataSupportDepartment = false;
        // shopee平台，数据分析部门能看到全部数据不用调整。其他平台，数据分析部门只能看到自己关联店铺的数据
        if (SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(saleChannel) && dataSupper) {
            dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
        }
        if (!superAdminOrEquivalent.getResult() && !dataSupportDepartment) {
            ApiResult<List<String>> selfAccounts = EsAccountUtils.getAuthorAccountList(saleChannel, isOnlySelf);
            if (!selfAccounts.isSuccess()) {
                throw new BusinessException(selfAccounts.getErrorMsg());
            }
            List<String> authSellerList = selfAccounts.getResult();
            if (CollectionUtils.isEmpty(authSellerList)) {
                throw new BusinessException("没有权限！");
            }
            return authSellerList;
        } else {
//            EsSaleAccountRequest request = new EsSaleAccountRequest();
//            request.setSaleChannel(saleChannel);
//            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request, "accountNumber");
//            return accountInfoList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            // 超管返回空列表
            return Collections.emptyList();
        }
    }
    /**
     * 获取当前用户可搜索的店铺账号
     */
    public List<String> getCurrentPermissionAccount(String saleChannel, boolean isOnlySelf) {
        return getCurrentPermissionAccount(saleChannel, isOnlySelf, false);
    }

    public void filterAccountPermission(List<String> accountNumberList, List<String> employeeNoList, boolean isOnlySelf, String saleChannel) {
        if (CollectionUtils.isNotEmpty(employeeNoList)) {
            List<String> permissionList = new ArrayList<>();
            Set<String> saleIds = new HashSet<>();
            // 获取对应的工号ID
            for (String employeeNo : employeeNoList) {
                Integer employeeId = getEmployeeId(employeeNo);
                if (employeeId != null) {
                    saleIds.add(employeeId.toString());
                }
                if (isOnlySelf) {
                    continue;
                }
                // 管理的销售人员
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(saleChannel, employeeNo);
                if (listApiResult.isSuccess()) {
                    List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString())
                            .collect(Collectors.toList());
                    saleIds.addAll(collect);
                }
            }
            if (CollectionUtils.isEmpty(saleIds)) {
                return;
            }
            // 查询对应店铺
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleIds(new ArrayList<>(saleIds));
            request.setSaleChannel(saleChannel);
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
            if (CollectionUtils.isNotEmpty(accountInfoList)) {
                permissionList.addAll(accountInfoList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList()));
            }

            if (CollectionUtils.isNotEmpty(permissionList)) {
                if (CollectionUtils.isEmpty(accountNumberList)) {
                    accountNumberList.addAll(permissionList);
                } else {
                    accountNumberList.removeIf(accountNumber -> !permissionList.contains(accountNumber));
                }
            }
        }
    }

    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；某平台助理和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     * @return 可查看的店铺账号
     */
    public List<String> getCurrentUserPermission(List<String> accountNumbers, List<String> managerIds, List<String> leaderIds, List<String> saleIds, String saleChannel, boolean dataSupper, String... sign) {
        List<String> accountList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            accountList.addAll(accountNumbers);
        }

        // 主管
        filterAccountPermission(accountList, managerIds, false, saleChannel);
        // 组长
        filterAccountPermission(accountList, leaderIds, false, saleChannel);
        // 销售
        filterAccountPermission(accountList, saleIds, true, saleChannel);

        if (accountList.isEmpty() && CollectionUtils.isNotEmpty(saleIds)) {
            throw new BusinessException("该销售无店铺数据");
        }

        if (accountList.isEmpty() && CollectionUtils.isNotEmpty(leaderIds)) {
            throw new BusinessException("该组长无店铺数据");
        }

        if (accountList.isEmpty() && CollectionUtils.isNotEmpty(managerIds)) {
            throw new BusinessException("该主管无店铺数据");
        }

        // 获取当前登录用户数据权限账号
        boolean isOnlySelf = false;
        if (sign != null && sign.length > 0) {
            String s = sign[0];
            //只查自己的权限
            if (StringUtils.equalsIgnoreCase(s, "1")) {
                isOnlySelf = true;
            }
        }

        // 获取当前登录用户数据权限账号
        List<String> currentUserPermissionList = this.getCurrentPermissionAccount(saleChannel, isOnlySelf, dataSupper);
        if (CollectionUtils.isNotEmpty(currentUserPermissionList)) {
            if (CollectionUtils.isNotEmpty(accountList)) {
                // 过滤当前未选择的店铺
                currentUserPermissionList.removeIf(accountNumber -> !accountList.contains(accountNumber));
            }
            accountList.addAll(currentUserPermissionList);
        }
        return accountList;
    }


    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；平台主管和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     *
     * @return 可查看的店铺账号
     */
    public List<String> getCurrentUserPermission(List<String> accountNumbers, List<String> managerIds, List<String> leaderIds, List<String> saleIds, String saleChannel, String... sign) {
        return getCurrentUserPermission(accountNumbers, managerIds, leaderIds, saleIds, saleChannel, false, sign);
    }

    /**
     * 获取当前用户可搜索的员工工号
     */
    public List<String> getCurrentPermissionEmployeeNo(String saleChannel, boolean isOnlySelf) {
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdmin();
        if (!superAdminResult.isSuccess()) {
            throw new BusinessException(superAdminResult.getErrorMsg());
        }
        // 非超级管理员 和 非数据分析组
        boolean dataSupportDepartment = false;
        // shopee平台，数据分析部门能看到全部数据不用调整。其他平台，数据分析部门只能看到自己关联店铺的数据
        if (SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(saleChannel)) {
            dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
        }
        if (!superAdminResult.getResult() && !dataSupportDepartment) {
            if (BooleanUtils.isFalse(isOnlySelf)) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
                if (!listApiResult.isSuccess()) {
                    throw new BusinessException(listApiResult.getErrorMsg());
                }

                List<NewUser> newUsers = listApiResult.getResult();
                List<String> employeeNos = newUsers.stream().map(NewUser::getEmployeeNo).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeNos)) {
                    throw new BusinessException("查询不到员工信息");
                }
                return employeeNos;
            }

            String employeeNo = WebUtils.getUserName();
            if (StringUtils.isBlank(employeeNo)) {
                employeeNo = DataContextHolder.getUsername();
            }
            if (StringUtils.isBlank(employeeNo)) {
                throw new BusinessException("查询不到员工信息!");
            }
            List<String> employeeNos = new ArrayList<>();
            employeeNos.add(employeeNo);
            return employeeNos;
        } else {
            List<NewUser> userList = NewUsermgtUtils.getEmployeeByServicePlatform(saleChannel);
            NewUser newUser = new NewUser();
            newUser.setEmployeeNo(WebUtils.getUserName());
            userList.add(newUser);
            return userList.stream().map(NewUser::getEmployeeNo).collect(Collectors.toList());
        }
    }
    /**
     * 获取当前用户可搜索的员工id
     */
    public List<Integer> getCurrentPermissionEmployeeId(String saleChannel, boolean isOnlySelf) {
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdmin();
        if (!superAdminResult.isSuccess()) {
            throw new BusinessException(superAdminResult.getErrorMsg());
        }
        // 非超级管理员或非数据支持部门
        Boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
        if (!superAdminResult.getResult() && !dataSupportDepartment) {
            if (BooleanUtils.isFalse(isOnlySelf)) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
                if (!listApiResult.isSuccess()) {
                    throw new BusinessException(listApiResult.getErrorMsg());
                }

                List<NewUser> newUsers = listApiResult.getResult();
                List<Integer> employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeIds)) {
                    throw new BusinessException("查询不到员工信息");
                }
                return employeeIds;
            }
            ApiResult<NewUser> apiResult = NewUsermgtUtils.tokenUser();
            if (!apiResult.isSuccess()) {
                throw new BusinessException(apiResult.getErrorMsg());
            }
            NewUser result = apiResult.getResult();
            if (result == null || result.getEmployeeId() == null) {
                throw new BusinessException("查询不到员工信息!");
            }
            List<Integer> employeeNos = new ArrayList<>();
            employeeNos.add(result.getEmployeeId());
            return employeeNos;
        } else {
            ApiResult<NewUser> apiResult = NewUsermgtUtils.tokenUser();
            if (!apiResult.isSuccess()) {
                throw new BusinessException(apiResult.getErrorMsg());
            }
            NewUser result = apiResult.getResult();
            if (result == null || result.getEmployeeId() == null) {
                throw new BusinessException("查询不到员工信息!");
            }

            List<NewUser> userList = NewUsermgtUtils.getEmployeeByServicePlatform(saleChannel);
            NewUser newUser = new NewUser();
            newUser.setEmployeeId(result.getEmployeeId());
            userList.add(newUser);
            return userList.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
        }
    }

    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；平台主管和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     */
    public List<String> getCurrentUserEmployeeNoPermission(List<String> employeeNos, List<String> managerNos, List<String> leaderNos, String saleChannel) {
        List<String> employeeNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeNos)) {
            employeeNoList.addAll(employeeNos);
        }
        // 获取当前登录用户数据权限账号
        List<String> currentUserPermissionList = getCurrentPermissionEmployeeNo(saleChannel, false);
        if (CollectionUtils.isNotEmpty(currentUserPermissionList)) {
            if (CollectionUtils.isNotEmpty(employeeNos)) {
                // 过滤当前未选择的店铺
                currentUserPermissionList.removeIf(employeeNo -> !employeeNos.contains(employeeNo));
            }
            employeeNoList.addAll(currentUserPermissionList);
        }

        // 主管
        filterEmployeeNoPermission(employeeNoList, managerNos, false, saleChannel);
        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(managerNos)) {
            throw new BusinessException("该主管无权限");
        }
        // 组长
        filterEmployeeNoPermission(employeeNoList, leaderNos, false, saleChannel);
        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(leaderNos)) {
            throw new BusinessException("该组长无权限");
        }

        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(employeeNos)) {
            throw new BusinessException("该销售无权限");
        }

        return employeeNoList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；平台主管和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     * 超级管理员 主管不查询，默认返回空（加快查询速度）
     */
    public List<String> getCurrentUserEmployeeNoPermission2(List<String> employeeNos, List<String> managerNos, List<String> leaderNos, String saleChannel) {
        List<String> employeeNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeNos)) {
            employeeNoList.addAll(employeeNos);
        }
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        if (!superAdminOrEquivalent.getResult()) {
            // 获取当前登录用户数据权限账号
            List<String> currentUserPermissionList = getCurrentPermissionEmployeeNo(saleChannel, false);
            if (CollectionUtils.isNotEmpty(currentUserPermissionList)) {
                if (CollectionUtils.isNotEmpty(employeeNos)) {
                    // 过滤当前未选择的店铺
                    currentUserPermissionList.removeIf(employeeNo -> !employeeNos.contains(employeeNo));
                }
                employeeNoList.addAll(currentUserPermissionList);
            }
        }

        // 主管
        filterEmployeeNoPermission(employeeNoList, managerNos, false, saleChannel);
        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(managerNos)) {
            throw new BusinessException("该主管无权限");
        }
        // 组长
        filterEmployeeNoPermission(employeeNoList, leaderNos, false, saleChannel);
        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(leaderNos)) {
            throw new BusinessException("该组长无权限");
        }

        if (employeeNoList.isEmpty() && CollectionUtils.isNotEmpty(employeeNos)) {
            throw new BusinessException("该销售无权限");
        }

        return employeeNoList.stream().distinct().collect(Collectors.toList());
    }

    private void filterEmployeeNoPermission(List<String> employeeNoList, List<String> managerNos, boolean isOnlySelf, String saleChannel) {
        if (CollectionUtils.isNotEmpty(managerNos)) {
            Set<String> employeeNos = new HashSet<>();
            // 获取对应的工号ID
            for (String employeeNo : managerNos) {
                employeeNos.add(employeeNo);
                if (isOnlySelf) {
                    continue;
                }
                // 管理的销售人员
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(saleChannel, employeeNo);
                if (listApiResult.isSuccess()) {
                    List<String> collect = listApiResult.getResult().stream().map(NewUser::getEmployeeNo)
                            .collect(Collectors.toList());
                    employeeNos.addAll(collect);
                }
            }

            if (CollectionUtils.isNotEmpty(employeeNos)) {
                if (CollectionUtils.isEmpty(employeeNoList)) {
                    employeeNoList.addAll(employeeNos);
                } else {
                    employeeNoList.removeIf(accountNumber -> !employeeNos.contains(accountNumber));
                }
            }
        }
    }

    /**
     * 超级管理员或者平台主管或者数据分析不获取下级员工，其他获取自己的下级或者自己
     *
     * @return
     */
    public List<String> getUnderlingPermissionUsers(String saleChannel) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }
        // 获取当前登录用户信息
        String employeeNo = com.estone.erp.common.redis.util.WebUtils.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            throw new BusinessException("查询不到员工信息！");
        }
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            throw new BusinessException(newUserApiResult.getErrorMsg());
        }
        // 超级管理员或数据支持部（数据分析）查询所有
        if (superAdminOrEquivalent.getResult() || newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
            return Collections.emptyList();
        } else {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
            if (!listApiResult.isSuccess()) {
                throw new BusinessException(listApiResult.getErrorMsg());
            }
            return listApiResult.getResult().stream().map(NewUser::getEmployeeNo).collect(Collectors.toList());

        }
    }

    public NewUser getCurrentUser() {
        // 获取当前登录用户信息
        String employeeNo = com.estone.erp.common.redis.util.WebUtils.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            throw new BusinessException("查询不到员工信息！");
        }
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            throw new BusinessException(newUserApiResult.getErrorMsg());
        }
        return newUserApiResult.getResult();
    }


    /**
     * 默认查询人员:销售和组长返回自己工号、主管返回自己和下级工号
     *
     * @param saleChannel
     * @return
     */
    public static List<String> getDefaultEmployeeNoList(String saleChannel) {
        String currentUser = WebUtils.getUserName();

        Map<String, List<EmployeeInfoDO>> threeLevelEmployee = getThreeLevelEmployeeMap(saleChannel);

        if (MapUtils.isNotEmpty(threeLevelEmployee)) {
            // 主管查自己和下级
            List<String> leaderList = Optional.ofNullable(threeLevelEmployee.get("leaderList")).orElse(Collections.emptyList()).stream().map(EmployeeInfoDO::getEmployeeNo).collect(Collectors.toList());

            // 判断是否为主管,主管才查自己和下级
            if (leaderList.contains(currentUser)) {
                ApiResult<List<String>> listApiResult = NewUsermgtUtils.getAuthorEmployeeNos(saleChannel);
                if (!listApiResult.isSuccess()) {
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                return listApiResult.getResult();
            }
        }
        return Collections.singletonList(currentUser);
    }


    /**
     * 获取授权人员:即员工及其下级员工工号
     *
     * @param saleChannel
     * @return
     */
    public static List<String> getAuthorEmployeeNoList(String saleChannel) {
        ApiResult<List<String>> listApiResult = NewUsermgtUtils.getAuthorEmployeeNos(saleChannel);
        if (!listApiResult.isSuccess()) {
            throw new RuntimeException(listApiResult.getErrorMsg());
        }
        return listApiResult.getResult();
    }

    /**
     * 获取销售、销售人员、主管信息
     *
     * @param saleChannel
     * @return
     */
    public static Map<String, List<EmployeeInfoDO>> getThreeLevelEmployeeMap(String saleChannel) {
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("servicePlatform", saleChannel);
        ApiResult<Map<String, List<EmployeeInfoDO>>> threeLevelEmployeeResult = newUsermgtClient.threeLevelEmployee(reqMap);
        if (!threeLevelEmployeeResult.isSuccess()) {
            throw new RuntimeException(String.format("调用用户系统获取三层销售异常信息:%s", threeLevelEmployeeResult.getErrorMsg()));
        }
        return threeLevelEmployeeResult.getResult();

    }


    /**
     * 获取默认查询人员或授权人员
     * 默认查询人员:销售和组长返回自己工号、主管返回自己和下级工号
     * 授权人员:即员工及其下级员工工号
     *
     * @param platform
     * @param createBy
     * @param createByList
     * @param accountNumber
     * @param accountNumberList
     * @return
     */
    public static Pair<Boolean, List<String>> getDefaultOrAuthorEmployeePair(String platform, String createBy, List<String> createByList,
                                                                             String accountNumber, List<String> accountNumberList) {
        // 权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(platform);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        if (!superAdminOrEquivalent.getResult()) {
            boolean noCreateBy = StringUtils.isBlank(createBy) && CollectionUtils.isEmpty(createByList);
            boolean noAccount = StringUtils.isBlank(accountNumber) && CollectionUtils.isEmpty(accountNumberList);
            // 据此判断为默认查询
            if (noCreateBy && noAccount) {
                List<String> defaultEmployeeNos = PermissionsHelper.getDefaultEmployeeNoList(platform);
                if (CollectionUtils.isNotEmpty(defaultEmployeeNos)) {
                    return Pair.of(Boolean.TRUE, defaultEmployeeNos);
                } else {
                    throw new RuntimeException("未获取到授权用户！");
                }
            } else {
                List<String> createBys = CommonUtils.takeCreateByList(createByList, createBy);
                // 查询授权人员
                List<String> authorEmployeeNoList = PermissionsHelper.getAuthorEmployeeNoList(platform);
                // 如果人员没设置值查询，则设置为当前账号可访问人员
                if (CollectionUtils.isEmpty(createBys)) {
                    return Pair.of(Boolean.TRUE, authorEmployeeNoList);
                }
            }
        }
        return Pair.of(Boolean.FALSE, Collections.emptyList());
    }

}
