package com.estone.erp.publish.platform.model;

import com.estone.erp.publish.platform.util.Platform;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class CategoryAttrExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * 平台
     */
    private String platform;

    private String tableIndex;

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getTableIndex() {
        return tableIndex;
    }

    public void setTableIndex(String tableIndex) {
        if (StringUtils.isNotEmpty(tableIndex)) {
            this.tableIndex = tableIndex;
        }else if (StringUtils.isNotEmpty(this.platform)) {
            this.tableIndex = Platform.getTableIndex(this.platform);
        }
    }

    public CategoryAttrExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdIsNull() {
            addCriterion("platform_category_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdIsNotNull() {
            addCriterion("platform_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdEqualTo(String value) {
            addCriterion("platform_category_id =", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdNotEqualTo(String value) {
            addCriterion("platform_category_id <>", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdGreaterThan(String value) {
            addCriterion("platform_category_id >", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_category_id >=", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdLessThan(String value) {
            addCriterion("platform_category_id <", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("platform_category_id <=", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdLike(String value) {
            addCriterion("platform_category_id like", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdNotLike(String value) {
            addCriterion("platform_category_id not like", value, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdIn(List<String> values) {
            addCriterion("platform_category_id in", values, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdNotIn(List<String> values) {
            addCriterion("platform_category_id not in", values, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdBetween(String value1, String value2) {
            addCriterion("platform_category_id between", value1, value2, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryIdNotBetween(String value1, String value2) {
            addCriterion("platform_category_id not between", value1, value2, "platformCategoryId");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameIsNull() {
            addCriterion("platform_category_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameIsNotNull() {
            addCriterion("platform_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameEqualTo(String value) {
            addCriterion("platform_category_name =", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameNotEqualTo(String value) {
            addCriterion("platform_category_name <>", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameGreaterThan(String value) {
            addCriterion("platform_category_name >", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_category_name >=", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameLessThan(String value) {
            addCriterion("platform_category_name <", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("platform_category_name <=", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameLike(String value) {
            addCriterion("platform_category_name like", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameNotLike(String value) {
            addCriterion("platform_category_name not like", value, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameIn(List<String> values) {
            addCriterion("platform_category_name in", values, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameNotIn(List<String> values) {
            addCriterion("platform_category_name not in", values, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameBetween(String value1, String value2) {
            addCriterion("platform_category_name between", value1, value2, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNameNotBetween(String value1, String value2) {
            addCriterion("platform_category_name not between", value1, value2, "platformCategoryName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameIsNull() {
            addCriterion("product_type_name is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameIsNotNull() {
            addCriterion("product_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameEqualTo(String value) {
            addCriterion("product_type_name =", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameNotEqualTo(String value) {
            addCriterion("product_type_name <>", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameGreaterThan(String value) {
            addCriterion("product_type_name >", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_type_name >=", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameLessThan(String value) {
            addCriterion("product_type_name <", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameLessThanOrEqualTo(String value) {
            addCriterion("product_type_name <=", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameLike(String value) {
            addCriterion("product_type_name like", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameNotLike(String value) {
            addCriterion("product_type_name not like", value, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameIn(List<String> values) {
            addCriterion("product_type_name in", values, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameNotIn(List<String> values) {
            addCriterion("product_type_name not in", values, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameBetween(String value1, String value2) {
            addCriterion("product_type_name between", value1, value2, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andProductTypeNameNotBetween(String value1, String value2) {
            addCriterion("product_type_name not between", value1, value2, "productTypeName");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeIsNull() {
            addCriterion("plat_attr_code is null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeIsNotNull() {
            addCriterion("plat_attr_code is not null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeEqualTo(String value) {
            addCriterion("plat_attr_code =", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeNotEqualTo(String value) {
            addCriterion("plat_attr_code <>", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeGreaterThan(String value) {
            addCriterion("plat_attr_code >", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeGreaterThanOrEqualTo(String value) {
            addCriterion("plat_attr_code >=", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeLessThan(String value) {
            addCriterion("plat_attr_code <", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeLessThanOrEqualTo(String value) {
            addCriterion("plat_attr_code <=", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeLike(String value) {
            addCriterion("plat_attr_code like", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeNotLike(String value) {
            addCriterion("plat_attr_code not like", value, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeIn(List<String> values) {
            addCriterion("plat_attr_code in", values, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeNotIn(List<String> values) {
            addCriterion("plat_attr_code not in", values, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeBetween(String value1, String value2) {
            addCriterion("plat_attr_code between", value1, value2, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrCodeNotBetween(String value1, String value2) {
            addCriterion("plat_attr_code not between", value1, value2, "platAttrCode");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnIsNull() {
            addCriterion("plat_attr_name_cn is null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnIsNotNull() {
            addCriterion("plat_attr_name_cn is not null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnEqualTo(String value) {
            addCriterion("plat_attr_name_cn =", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnNotEqualTo(String value) {
            addCriterion("plat_attr_name_cn <>", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnGreaterThan(String value) {
            addCriterion("plat_attr_name_cn >", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnGreaterThanOrEqualTo(String value) {
            addCriterion("plat_attr_name_cn >=", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnLessThan(String value) {
            addCriterion("plat_attr_name_cn <", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnLessThanOrEqualTo(String value) {
            addCriterion("plat_attr_name_cn <=", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnLike(String value) {
            addCriterion("plat_attr_name_cn like", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnNotLike(String value) {
            addCriterion("plat_attr_name_cn not like", value, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnIn(List<String> values) {
            addCriterion("plat_attr_name_cn in", values, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnNotIn(List<String> values) {
            addCriterion("plat_attr_name_cn not in", values, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnBetween(String value1, String value2) {
            addCriterion("plat_attr_name_cn between", value1, value2, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameCnNotBetween(String value1, String value2) {
            addCriterion("plat_attr_name_cn not between", value1, value2, "platAttrNameCn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnIsNull() {
            addCriterion("plat_attr_name_en is null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnIsNotNull() {
            addCriterion("plat_attr_name_en is not null");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnEqualTo(String value) {
            addCriterion("plat_attr_name_en =", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnNotEqualTo(String value) {
            addCriterion("plat_attr_name_en <>", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnGreaterThan(String value) {
            addCriterion("plat_attr_name_en >", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("plat_attr_name_en >=", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnLessThan(String value) {
            addCriterion("plat_attr_name_en <", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnLessThanOrEqualTo(String value) {
            addCriterion("plat_attr_name_en <=", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnLike(String value) {
            addCriterion("plat_attr_name_en like", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnNotLike(String value) {
            addCriterion("plat_attr_name_en not like", value, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnIn(List<String> values) {
            addCriterion("plat_attr_name_en in", values, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnNotIn(List<String> values) {
            addCriterion("plat_attr_name_en not in", values, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnBetween(String value1, String value2) {
            addCriterion("plat_attr_name_en between", value1, value2, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andPlatAttrNameEnNotBetween(String value1, String value2) {
            addCriterion("plat_attr_name_en not between", value1, value2, "platAttrNameEn");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryIsNull() {
            addCriterion("is_mandatory is null");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryIsNotNull() {
            addCriterion("is_mandatory is not null");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryEqualTo(Boolean value) {
            addCriterion("is_mandatory =", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryNotEqualTo(Boolean value) {
            addCriterion("is_mandatory <>", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryGreaterThan(Boolean value) {
            addCriterion("is_mandatory >", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_mandatory >=", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryLessThan(Boolean value) {
            addCriterion("is_mandatory <", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryLessThanOrEqualTo(Boolean value) {
            addCriterion("is_mandatory <=", value, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryIn(List<Boolean> values) {
            addCriterion("is_mandatory in", values, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryNotIn(List<Boolean> values) {
            addCriterion("is_mandatory not in", values, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryBetween(Boolean value1, Boolean value2) {
            addCriterion("is_mandatory between", value1, value2, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andIsMandatoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_mandatory not between", value1, value2, "isMandatory");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}