package com.estone.erp.publish.temu.service.impl;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.EmployeeInfoDO;
import com.estone.erp.publish.temu.call.TemuApiClient;
import com.estone.erp.publish.temu.call.model.response.AttributeResponse;
import com.estone.erp.publish.temu.call.model.response.ParentSpecResponse;
import com.estone.erp.publish.temu.call.model.response.TemuBGApiResponse;
import com.estone.erp.publish.temu.enums.PublishTypeEmun;
import com.estone.erp.publish.temu.enums.TemuOperationTypeEmun;
import com.estone.erp.publish.temu.mapper.TemuPropertyConfigMapper;
import com.estone.erp.publish.temu.model.TemuPropertyChangeLog;
import com.estone.erp.publish.temu.model.TemuPropertyConfig;
import com.estone.erp.publish.temu.model.TemuPropertyConfigCriteria;
import com.estone.erp.publish.temu.model.TemuPropertyConfigExample;
import com.estone.erp.publish.temu.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.temu.model.vo.CategoryAttributeConfigsVO;
import com.estone.erp.publish.temu.model.vo.TemuPropertyConfigVO;
import com.estone.erp.publish.temu.service.TemuPropertyChangeLogService;
import com.estone.erp.publish.temu.service.TemuPropertyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-05-20 15:28:07
 */
@Slf4j
@Service("temuPropertyConfigService")
public class TemuPropertyConfigServiceImpl implements TemuPropertyConfigService {

    @Autowired
    private TemuApiClient temuApiClient;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private TemuPropertyConfigMapper temuPropertyConfigMapper;

    @Autowired
    protected TransactionTemplate transactionTemplate;

    @Autowired
    protected TemuPropertyChangeLogService temuPropertyChangeLogService;

    @Override
    public int countByExample(TemuPropertyConfigExample example) {
        Assert.notNull(example, "example is null!");
        return temuPropertyConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<TemuPropertyConfigVO> search(CQuery<TemuPropertyConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");


        //判断是否超管
        ApiResult<Boolean> superAdmin = NewUsermgtUtils.isSuperAdmin();
        if (!superAdmin.isSuccess()) {
            throw new RuntimeException(superAdmin.getErrorMsg());
        }

        Map<String, List<EmployeeInfoDO>> threeLevelEmployeeMap = PermissionsHelper.getThreeLevelEmployeeMap(SaleChannel.CHANNEL_TEMU);

        String currentUser = WebUtils.getUserName();
        // 是否主管
        boolean isLeader = false;
        if (MapUtils.isNotEmpty(threeLevelEmployeeMap)) {
            // 主管查自己和下级
            List<String> leaderList = Optional.ofNullable(threeLevelEmployeeMap.get("leaderList")).orElse(Collections.emptyList()).stream().map(EmployeeInfoDO::getEmployeeNo).collect(Collectors.toList());
            // 判断是否为主管
            isLeader = leaderList.contains(currentUser);
        }

        if (Boolean.FALSE.equals(superAdmin.getResult()) || !isLeader) {
            throw new RuntimeException("当前账户暂无权限查询");
        }

        // 组装查询条件
        TemuPropertyConfigCriteria query = cquery.getSearch();
        TemuPropertyConfigExample example = query.getExample();
        example.setOrderByClause("create_time desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = temuPropertyConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<TemuPropertyConfig> temuPropertyConfigs = temuPropertyConfigMapper.selectByExample(example);
        // 结构处理
        List<TemuPropertyConfigVO> temuPropertyConfigVOS = temuPropertyConfigs.stream().map(TemuPropertyConfigVO::reconvert).collect(Collectors.toList());
        // 组装结果
        CQueryResult<TemuPropertyConfigVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(temuPropertyConfigVOS);
        return result;
    }

    @Override
    public TemuPropertyConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return temuPropertyConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<TemuPropertyConfig> selectByExample(TemuPropertyConfigExample example) {
        Assert.notNull(example, "example is null!");
        return temuPropertyConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(TemuPropertyConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间
        record.setCreatedBy(WebUtils.getUserName());
        record.setCreateTime(new Timestamp(System.currentTimeMillis()));
        record.setUpdateBy(WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return temuPropertyConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(TemuPropertyConfig record) {
        Assert.notNull(record, "record is null!");
        // 判断是否重复
        TemuPropertyConfigExample example = new TemuPropertyConfigExample();
        example.createCriteria().andIsGeneralEqualTo(false).andCategoryIdEqualTo(record.getCategoryId());
        List<TemuPropertyConfig> propertyConfigs = temuPropertyConfigMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(propertyConfigs)) {
            // 判断是否重复，排除自己
            if (propertyConfigs.size() >= 2) {
                throw new RuntimeException("该类目已存在属性配置，请勿重复添加");
            }
        }

        // 通过主键查询记录
        TemuPropertyConfig temuPropertyConfig = temuPropertyConfigMapper.selectByPrimaryKey(record.getId());
        return transactionTemplate.execute(transactionStatus -> {
            // 记录修改前的属性值
            savePropertyChangeLog(record, temuPropertyConfig);

            // 默认加修改时间和修改人
            record.setUpdateBy(WebUtils.getUserName());
            record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            return temuPropertyConfigMapper.updateByPrimaryKeySelective(record);
        });
    }

    @Override
    public int updateByExampleSelective(TemuPropertyConfig record, TemuPropertyConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return temuPropertyConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return temuPropertyConfigMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void savePropertyConfig(TemuPropertyConfig propertyConfig) {
        TemuPropertyConfigExample example = new TemuPropertyConfigExample();
        example.createCriteria().andIsGeneralEqualTo(false).andCategoryIdEqualTo(propertyConfig.getCategoryId());
        int count = temuPropertyConfigMapper.countByExample(example);
        if (count > 0) {
            throw new RuntimeException("该类目已存在属性配置，请勿重复添加");
        }
        // 默认加时间
        propertyConfig.setIsGeneral(false);
        propertyConfig.setCreatedBy(WebUtils.getUserName());
        propertyConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
        temuPropertyConfigMapper.insert(propertyConfig);
    }

    @Override
    public TemuPropertyConfig getPropertyConfigById(Integer id) {
        return temuPropertyConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public TemuPropertyConfig getPropertyConfigByIdOrFetchGeneralConfig(BuilderTemplateDO builderTemplateDO) {
        // 优先获取非通用类目信息
        TemuPropertyConfigExample example = new TemuPropertyConfigExample();
        example.createCriteria()
                .andStatusEqualTo(true) // 启动的
                .andCategoryIdEqualTo(builderTemplateDO.getCategoryId());
        List<TemuPropertyConfig> propertyConfigs = temuPropertyConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(propertyConfigs)) {
            if (builderTemplateDO.getPublishType().equals(PublishTypeEmun.NORMAL_RELEASE)) {
                // 只处理模板刊登保存并刊登
                TemuPropertyConfigVO configVO = getTemuPropertyConfigVO(builderTemplateDO.getCategoryId());
                return TemuPropertyConfigVO.convert(configVO);
            }
            throw new BusinessException("未找到对应的属性配置信息");
        }

        for (TemuPropertyConfig propertyConfig : propertyConfigs) {
            if (!propertyConfig.getIsGeneral()) {
                return propertyConfig;
            }
        }
        return propertyConfigs.get(0);
    }

    /**
     * 获取平台分类规格信息
     *
     * @param categoryId
     * @return
     */
    private TemuPropertyConfigVO getTemuPropertyConfigVO(Integer categoryId) {
        // 调用平台接口获取类目信息和规格信息
        TemuPropertyConfigVO configVO = new TemuPropertyConfigVO();
        configVO.setCategoryId(categoryId);
        // 分类属性
        TemuBGApiResponse<AttributeResponse> goodAttrs = temuApiClient.getGoodAttrs("LANDU", categoryId);
        if (Objects.isNull(goodAttrs.getResult())) {
            throw new BusinessException("当前分类无分类属性");
        }
        List<AttributeResponse.Property> properties = goodAttrs.getResult().getProperties();
        List<CategoryAttributeConfigsVO> categoryAttributeConfigsVOS = properties.stream().map(property -> {
            CategoryAttributeConfigsVO vo = new CategoryAttributeConfigsVO();
            vo.setName(property.getName());
            vo.setPid(property.getPid());
            vo.setRequired(property.getRequired());
            vo.setTemplatePid(property.getTemplatePid());
            vo.setRefPid(Long.valueOf(property.getRefPid()));
            return vo;
        }).collect(Collectors.toList());
        configVO.setCategoryAttributeConfigs(categoryAttributeConfigsVOS);

        // 规格信息
        TemuBGApiResponse<ParentSpecResponse> apiClientGoodParentSpec = temuApiClient.getGoodParentSpec("LANDU");
        if (Objects.isNull(apiClientGoodParentSpec.getResult())) {
            throw new BusinessException("当前分类无规格信息");
        }
        List<ParentSpecResponse.ParentSpecDTO> parentSpecDTOS = apiClientGoodParentSpec.getResult().getParentSpecDTOS();
        List<TemuPropertyConfigVO.ProductSpecificationConfigs> productSpecificationConfigs = parentSpecDTOS.stream().map(parentSpecDTO -> {
            TemuPropertyConfigVO.ProductSpecificationConfigs configs = new TemuPropertyConfigVO.ProductSpecificationConfigs();
            configs.setId(parentSpecDTO.getParentSpecId());
            configs.setName(parentSpecDTO.getParentSpecName());
            configs.setPriority(1);
            return configs;
        }).collect(Collectors.toList());
        configVO.setProductSpecificationConfigs(List.of(productSpecificationConfigs.get(0)));
        return configVO;
    }

    /**
     * 日志记录
     *
     * @param record
     * @param temuPropertyConfig
     */
    private void savePropertyChangeLog(TemuPropertyConfig record, TemuPropertyConfig temuPropertyConfig) {
        TemuPropertyChangeLog propertyChangeLog = new TemuPropertyChangeLog();
        // 记录修改前的属性值
        if (!temuPropertyConfig.getStatus().equals(record.getStatus())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.STATUS.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getStatus().toString());
            propertyChangeLog.setAfterValue(record.getStatus().toString());
        }
        if (!temuPropertyConfig.getCategoryId().equals(record.getCategoryId())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.PLATFORM_CATEGORY.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getPlatformCategory());
            propertyChangeLog.setAfterValue(record.getPlatformCategory());
        }
        if (!temuPropertyConfig.getCategoryAttributeConfig().equals(record.getCategoryAttributeConfig())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.CLASSIFY_PROPERTY.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getCategoryAttributeConfig());
            propertyChangeLog.setAfterValue(record.getCategoryAttributeConfig());
        }
        if (!temuPropertyConfig.getProductSpecificationConfig().equals(record.getProductSpecificationConfig())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.PRODUCT_SPECIFICATION.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getProductSpecificationConfig());
            propertyChangeLog.setAfterValue(record.getProductSpecificationConfig());
        }
        if (!temuPropertyConfig.getImageOrderConfig().equals(record.getImageOrderConfig())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.IMAGE_ORDER.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getImageOrderConfig());
            propertyChangeLog.setAfterValue(record.getImageOrderConfig());
        }
        if (!temuPropertyConfig.getDeclarePriceConfig().equals(record.getDeclarePriceConfig())) {
            propertyChangeLog.setOperationType(TemuOperationTypeEmun.DECLARE_PRICE_CONFIG.name());
            propertyChangeLog.setBeforeValue(temuPropertyConfig.getDeclarePriceConfig());
            propertyChangeLog.setAfterValue(record.getDeclarePriceConfig());
        }
        // 记录修改后的属性值
        if (Objects.nonNull(propertyChangeLog.getOperationType())) {
            propertyChangeLog.setRelevancyId(temuPropertyConfig.getId());
            propertyChangeLog.setOperator(WebUtils.getUserName());
            propertyChangeLog.setOperationTime(new Timestamp(System.currentTimeMillis()));
            temuPropertyChangeLogService.insert(propertyChangeLog);
        }
    }

}