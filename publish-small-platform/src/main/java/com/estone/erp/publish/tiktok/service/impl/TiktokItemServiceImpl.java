package com.estone.erp.publish.tiktok.service.impl;

import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.TiktokExecutors;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch4.model.EsTiktokItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsTiktokItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsTiktokItemService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.system.product.util.CheckSkuUtils;
import com.estone.erp.publish.tiktok.call.products.TiktokProductCall;
import com.estone.erp.publish.tiktok.componet.TiktokAccountCacheManager;
import com.estone.erp.publish.tiktok.componet.TiktokEsItemBulkProcessor;
import com.estone.erp.publish.tiktok.componet.TiktokFeedTaskHelper;
import com.estone.erp.publish.tiktok.componet.TiktokSyncItemHelper;
import com.estone.erp.publish.tiktok.constant.TiktokConstant;
import com.estone.erp.publish.tiktok.enums.TiktokOperateTypeEnum;
import com.estone.erp.publish.tiktok.enums.TiktokProductStatusEnum;
import com.estone.erp.publish.tiktok.model.dto.TiktokCalcPriceRequest;
import com.estone.erp.publish.tiktok.model.dto.TiktokOfflineDTO;
import com.estone.erp.publish.tiktok.model.dto.TiktokSyncListingMqParams;
import com.estone.erp.publish.tiktok.model.vo.EsTiktokItemResponse;
import com.estone.erp.publish.tiktok.model.vo.TiktokItemEsExtend;
import com.estone.erp.publish.tiktok.service.TiktokAccountConfigService;
import com.estone.erp.publish.tiktok.service.TiktokItemService;
import com.estone.erp.publish.tiktok.util.TiktokCalcPriceUtils;
import com.estone.erp.publish.tiktok.util.TiktokItemLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/7 15:42
 */
@Slf4j
@Service("TiktokItemService")
public class TiktokItemServiceImpl implements TiktokItemService {

    @Resource
    private TiktokSyncItemHelper tiktokSyncItemHelper;

    @Resource
    private EsTiktokItemService esTiktokItemService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private TiktokFeedTaskHelper tiktokFeedTaskHelper;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private TiktokEsItemBulkProcessor tiktokEsItemBulkProcessor;

    @Resource
    private TiktokAccountConfigService tiktokAccountConfigService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Resource
    private TiktokAccountCacheManager tiktokAccountCacheManager;

    @Override
    public void save(EsTiktokItem esTiktokItem) {
        if (null == esTiktokItem) {
            return;
        }

        esTiktokItemService.save(esTiktokItem);
    }

    @Override
    public void saveAll(List<EsTiktokItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        esTiktokItemService.saveAll(items);
    }

    @Override
    public EsTiktokItem findById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        return esTiktokItemService.findById(id);
    }

    @Override
    public Boolean exists(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }
        return esTiktokItemService.exists(id);
    }

    @Override
    public long countByCondition(EsTiktokItemRequest esTiktokItemRequest) {
        return esTiktokItemService.countByCondition(esTiktokItemRequest);
    }

    @Override
    public List<EsTiktokItem> getEsTiktokItems(EsTiktokItemRequest request) {
        return esTiktokItemService.getEsTiktokItems(request);
    }

    @Override
    public PageInfo<EsTiktokItem> page(EsTiktokItemRequest request) {
        return esTiktokItemService.page(request);
    }

    @Override
    public EsTiktokItemResponse search(EsTiktokItemRequest request) {
        // 处理权限
        handleAuth(request);

        // 查询数据
        PageInfo<EsTiktokItem> results = this.page(request);

        // 扩展信息
        Map<String, TiktokItemEsExtend> extendMap = handelPageExtend(results.getContents(), request);

        EsTiktokItemResponse esTiktokItemResponse = new EsTiktokItemResponse();
        esTiktokItemResponse.setEsTiktokItemPage(results);
        esTiktokItemResponse.setExtendMap(extendMap);

        return esTiktokItemResponse;
    }

    @Override
    public void handleAuth(EsTiktokItemRequest request) {
        // 避免分页查询时候重复调用授权
        if (!request.getNeedSearchAuth()) {
            return;
        }

        List<String> accountList = new ArrayList<>();
        List<String> accounts = request.getAccountNumberList();
        if (CollectionUtils.isNotEmpty(accounts)) {
            accountList.addAll(accounts);
        }
        List<String> authorAccountList = permissionsHelper.getCurrentUserPermission(accountList,
                request.getSaleSupervisorList(), request.getSaleLeaderList(), request.getSaleManList(), SaleChannel.CHANNEL_TIKTOK);

        request.setAccountNumberList(authorAccountList);
        request.setNeedSearchAuth(false);
    }

    private Map<String, TiktokItemEsExtend> handelPageExtend(List<EsTiktokItem> esTiktokItems, EsTiktokItemRequest request) {
        Map<String, TiktokItemEsExtend> extendMap = new HashMap<>();

        if (CollectionUtils.isEmpty(esTiktokItems) || BooleanUtils.isFalse(request.getQueryExtend())) {
            return extendMap;
        }

        List<String> accountNumberList = esTiktokItems.stream().map(EsTiktokItem::getAccountNumber).distinct().collect(Collectors.toList());

        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_TIKTOK);
        for (EsTiktokItem esTiktokItem : esTiktokItems) {
            TiktokItemEsExtend esExtend = extendMap.get(esTiktokItem.getId());
            if (null == esExtend) {
                esExtend = new TiktokItemEsExtend();
                extendMap.put(esTiktokItem.getId(), esExtend);
            }

            // 销售、销售组长、销售主管
            if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(esTiktokItem.getAccountNumber());
                esExtend.setSaleMan(saleSuperiorTriple.getLeft());
                esExtend.setSaleLeader(saleSuperiorTriple.getMiddle());
                esExtend.setSaleSupervisor(saleSuperiorTriple.getRight());
            }
        }

        return extendMap;
    }

    @Override
    public void syncAllItem(String accountNumber) {
        List<SaleAccountAndBusinessResponse> saleAccounts = getSaleAccountAndBusinessResponses(accountNumber);

        if (CollectionUtils.isEmpty(saleAccounts)) {
            return;
        }

        for (SaleAccountAndBusinessResponse saleAccount : saleAccounts) {
            // 检查是否存在未完成的同步任务
            if (checkExecutingSyncItemTask(accountNumber)) {
                tiktokFeedTaskHelper.newSyncItemFailFeedTask(saleAccount.getAccountNumber(), "存在未完成的同步任务,请稍后再试！");
                continue;
            }

            FeedTask feedTask = tiktokFeedTaskHelper
                    .initFeedTask(SaleChannel.CHANNEL_TIKTOK, saleAccount.getAccountNumber(),
                            TiktokOperateTypeEnum.SYNC_ITEM.getStatusMsgEn(), null, null);
            TiktokSyncListingMqParams publishMessage = new TiktokSyncListingMqParams();
            publishMessage.setAccountNumber(saleAccount.getAccountNumber());
            publishMessage.setOperator(WebUtils.getUserName() == null ? "admin" : WebUtils.getUserName());
            publishMessage.setFeedTaskId(feedTask.getId());
            rabbitTemplate.convertAndSend(PublishMqConfig.TIKTOK_API_DIRECT_EXCHANGE, PublishQueues.TIKTOK_ALL_SYNC_LISTING_QUEUE_KEY, publishMessage);
        }
    }

    private boolean checkExecutingSyncItemTask(String accountNumber) {
        FeedTask executingSyncItemTask = tiktokFeedTaskHelper.getExecutingSyncItemTask(accountNumber);
        if (null == executingSyncItemTask) {
            return false;
        }
        LocalDateTime createTime = LocalDateTimeUtil.of(executingSyncItemTask.getCreateTime());
        LocalDateTime now = LocalDateTime.now();
        if (createTime.plusHours(24).isBefore(now)) {
            // 超过24小时未完成,重新执行
            tiktokFeedTaskHelper.failTask(executingSyncItemTask, "任务超时,重新执行");
            String aslKey = TiktokConstant.ACCOUNT_SYNC_LOCK + accountNumber;
            PublishRedisClusterUtils.del(aslKey);
            return false;
        }
        return true;
    }

    @Override
    public void syncAddItem(String accountNumber, Date updateTimeGe, Date updateTimeLe) {
        List<SaleAccountAndBusinessResponse> saleAccounts = getSaleAccountAndBusinessResponses(accountNumber);

        if (CollectionUtils.isEmpty(saleAccounts)) {
            return;
        }
        Date dateBegin = DateUtils.getDateBegin(-1);
        Date dateEnd = DateUtils.getDateEnd(0);
        String userName = WebUtils.getUserName();
        for (SaleAccountAndBusinessResponse saleAccount : saleAccounts) {
            if (userName != null) {
                // 判断这个店铺是否现在正在执行同步，如果是的话就不执行了
                FeedTaskExample example = new FeedTaskExample();
                example.createCriteria().andAccountNumberEqualTo(saleAccount.getAccountNumber())
                        .andTaskTypeEqualTo(TiktokOperateTypeEnum.SYNC_ITEM.getStatusMsgEn())
                        .andTaskStatusIn(List.of(FeedTaskStatusEnum.WAITING.getTaskStatus(), FeedTaskStatusEnum.RUNNING.getTaskStatus()))
                        .andCreateTimeBetween(dateBegin, dateEnd)
                        .andCreatedByNotEqualTo("admin");
                int i = feedTaskService.countByExample(example, Platform.Tiktok.name());
                if (i > 0) {
                    tiktokFeedTaskHelper.newSyncItemFailFeedTask(saleAccount.getAccountNumber(), "店铺上次同步未完成,请稍后再试！");
                    continue;
                }
            }
            String ge = DateUtils.format(updateTimeGe, "yyyy-MM-dd HH:mm:ss");
            String le = DateUtils.format(updateTimeLe, "yyyy-MM-dd HH:mm:ss");

            FeedTask feedTask = tiktokFeedTaskHelper
                    .initIncrementSyncFeedTask(SaleChannel.CHANNEL_TIKTOK, saleAccount.getAccountNumber(),
                            TiktokOperateTypeEnum.SYNC_ITEM.getStatusMsgEn(), ge, le);
            TiktokSyncListingMqParams publishMessage = new TiktokSyncListingMqParams();
            publishMessage.setAccountNumber(saleAccount.getAccountNumber());
            publishMessage.setOperator(WebUtils.getUserName() == null ? "admin" : WebUtils.getUserName());
            publishMessage.setFeedTaskId(feedTask.getId());
            publishMessage.setUpdatedLe(le);
            publishMessage.setUpdatedGe(ge);
            rabbitTemplate.convertAndSend(PublishMqConfig.TIKTOK_API_DIRECT_EXCHANGE, PublishQueues.TIKTOK_INCREMENT_SYNC_LISTING_QUEUE_KEY, publishMessage);
        }
    }

    private List<SaleAccountAndBusinessResponse> getSaleAccountAndBusinessResponses(String accountNumber) {
        List<SaleAccountAndBusinessResponse> saleAccounts = new ArrayList<>();
        if (StringUtils.isNotBlank(accountNumber)) {
            SaleAccountAndBusinessResponse saleAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, accountNumber);
            saleAccounts.add(saleAccount);
        } else {
            List<String> accountList = EsAccountUtils.getPlatformNormaLAccountListByEs(SaleChannel.CHANNEL_TIKTOK);
            for (String account : accountList) {
                SaleAccountAndBusinessResponse saleAccount = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, account);
                saleAccounts.add(saleAccount);
            }
        }
        return saleAccounts;
    }

    @Override
    public void syncItemByProductId(String accountNumber, List<String> productIdList) {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, accountNumber);

        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        for (String productId : productIdList) {
            TiktokExecutors.syncItem(() -> {
                try {
                    // 执行同步
                    tiktokSyncItemHelper.syncItem(saleAccount, productId);
                } catch (Exception e) {
                    log.error("账号{} 同步item：{}", saleAccount.getAccountNumber(), e.getMessage());
                }
            });
        }
    }

    @Override
    public void updateInventory(List<EsTiktokItem> esTiktokItems) {
        if (CollectionUtils.isEmpty(esTiktokItems)) {
            return;
        }

        Map<String, List<EsTiktokItem>> accountToItemMap =
                esTiktokItems.stream().collect(Collectors.groupingBy(EsTiktokItem::getAccountNumber));
        accountToItemMap.forEach((account, esTiktokItemList) -> TiktokExecutors.updateInventory(() -> {
            SaleAccountAndBusinessResponse saleAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, account);
            if (null == saleAccount) {
                log.error(account + " 修改库存获取账号失败！");
                return;
            }

            this.updateInventory(esTiktokItemList, saleAccount, "listing", null);
        }));
    }

    @Override
    public void updateInventory(List<EsTiktokItem> updateItemList, SaleAccountAndBusinessResponse saleAccount, String job, Map<String, StockObj> stockMap) {
        if (CollectionUtils.isEmpty(updateItemList)) {
            return;
        }
        if (stockMap == null) {
            stockMap = new HashMap<>();
        }

        // 查询本地库存等字段
        EsTiktokItemRequest request = new EsTiktokItemRequest();
        request.setQueryFields(new String[]{"id","accountNumber", "productId", "skuId", "sellerSku", "sku", "inventory"});
        request.setAccountNumber(saleAccount.getAccountNumber());
        List<String> skuIdList = updateItemList.stream().map(EsTiktokItem::getSkuId).collect(Collectors.toList());
        List<String> productIdList = updateItemList.stream().map(EsTiktokItem::getProductId).distinct().collect(Collectors.toList());
        request.setSkuIdList(skuIdList);
        request.setProductIdList(productIdList);
        List<EsTiktokItem> esTiktokItems = this.getEsTiktokItems(request);
        Map<String, EsTiktokItem> dbItemMap = esTiktokItems.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getSkuId(), item), HashMap::putAll);

        // 记录改前库存值
        Map<String, Integer> skuIdToInventoryMap = new HashMap<>();

        // 改前改后库存相同 不做修改
        Iterator<EsTiktokItem> it = updateItemList.iterator();
        while (it.hasNext()) {
            EsTiktokItem item = it.next();
            Integer newValue = item.getInventory();
            EsTiktokItem localItem = dbItemMap.get(item.getSkuId());
            if (null == localItem || ObjectUtils.nullSafeEquals(localItem.getInventory(), newValue)) {
                it.remove();
            } else {
                item.setId(localItem.getId());
                item.setSellerSku(localItem.getSellerSku());
                item.setSku(localItem.getSku());
                item.setAccountNumber(localItem.getAccountNumber());
                item.setLastUpdateDate(new Date());
                item.setLastUpdatedBy(StringUtils.isBlank(DataContextHolder.getUsername()) ? StrConstant.ADMIN : DataContextHolder.getUsername());

                skuIdToInventoryMap.put(localItem.getSkuId(), localItem.getInventory());
            }
        }
        if (CollectionUtils.isEmpty(updateItemList)) {
            throw new RuntimeException("改前改后库存相同");
        }

        // 初始化处理报告
        Map<String, List<FeedTask>> idToTaskMap = tiktokFeedTaskHelper
                .initItemFeedTask(updateItemList,  TiktokOperateTypeEnum.UPDATE_INVENTORY.getStatusMsgEn(), job, stockMap);

        // 提交修改
        submitUpdateInventory(updateItemList, idToTaskMap, skuIdToInventoryMap, saleAccount);
    }

    private void submitUpdateInventory(List<EsTiktokItem> updateItemList, Map<String, List<FeedTask>> idToTaskMap,
                                       Map<String, Integer> skuIdToInventoryMap, SaleAccountAndBusinessResponse saleAccount) {
        // 根据productId分组提交
        Map<String, List<EsTiktokItem>> idToItemMap = updateItemList.stream().collect(Collectors.groupingBy(EsTiktokItem::getProductId));
        for (Map.Entry<String, List<EsTiktokItem>> entry : idToItemMap.entrySet()) {
            List<FeedTask> feedTasks = idToTaskMap.get(entry.getKey());
            try {
                Map<String, FeedTask> skuIdToTaskMap =
                        feedTasks.stream().collect(Collectors.toMap(FeedTask::getAttribute4, item -> item));
                List<EsTiktokItem> updateItems = entry.getValue();

                // 提交修改
                new TiktokProductCall(saleAccount).updateInventory(updateItems);

                // 本地保存
                tiktokEsItemBulkProcessor.updateItemInventory(updateItems);

                // 更新处理报告
                for (EsTiktokItem item : updateItems) {
                    Integer oldValue = skuIdToInventoryMap.get(item.getSkuId());
                    FeedTask feedTask = skuIdToTaskMap.get(item.getSkuId());
                    tiktokFeedTaskHelper.succeedFeedTask(feedTask, oldValue, item.getInventory());
                }
            } catch (Exception e) {
                feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                        ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
            }
        }
    }

    @Override
    public void batchDeactivateOrDelete(TiktokOfflineDTO tiktokOfflineDTO) {
        String offlineType = tiktokOfflineDTO.getOfflineType();
        if (StringUtils.isBlank(offlineType)) {
            throw new RuntimeException("请传入下架类型");
        }
        List<String> accountNumberList = tiktokOfflineDTO.getAccountNumberList();
        List<TiktokOfflineDTO.SelectData> selectDataList = tiktokOfflineDTO.getSelectDataList();
        if (CollectionUtils.isEmpty(accountNumberList) && CollectionUtils.isEmpty(selectDataList)) {
            throw new RuntimeException("参数不可为空");
        }

        String createBy = WebUtils.getUserName();
        if (CollectionUtils.isNotEmpty(selectDataList)) {
            Map<String, List<String>> groupMapping = selectDataList.stream()
                    .collect(Collectors.groupingBy(TiktokOfflineDTO.SelectData::getAccountNumber,
                            Collectors.mapping(TiktokOfflineDTO.SelectData::getProductId, Collectors.toList())));
            for (Map.Entry<String, List<String>> entry : groupMapping.entrySet()) {
                TiktokExecutors.deleteAccountProducts(() -> {
                    try {
                        List<String> productIdList = entry.getValue().stream().distinct().collect(Collectors.toList());
                        batchDeactivateOrDelete(offlineType, entry.getKey(), productIdList, createBy, null);
                    } catch (Exception e) {
                        log.error("账号{} 下架操作报错：{}", entry.getKey(), e.getMessage());
                    }
                });
            }
        } else {
            for (String accountNumber : accountNumberList) {
                TiktokExecutors.deleteAccountProducts(() -> {
                    try {
                        batchDeactivateOrDelete(offlineType, accountNumber, null, createBy, null);
                    } catch (Exception e) {
                        log.error("账号{} 下架操作报错：{}", accountNumber, e.getMessage());
                    }
                });
            }
        }
    }

    @Override
    public void batchDeactivateOrDelete(String offlineType, String accountNumber, List<String> productIdList, String createBy, String remark) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        // 获取账号
        SaleAccountAndBusinessResponse account = tiktokAccountCacheManager.getAccount(accountNumber);
        if (null == account) {
            throw new RuntimeException("获取不到账号信息");
        }

        // 查询在线列表数据
        List<EsTiktokItem> esTiktokItems = findItemByAccountAndProductIds(accountNumber, productIdList, offlineType);
        if (CollectionUtils.isEmpty(esTiktokItems)) {
            log.info("账号：" + accountNumber + "没有需要操作的数据");
            return;
        }

        // 根据产品id分组处理
        List<String> deleteProductIdList = new ArrayList<>();
        Map<String, List<EsTiktokItem>> productIdToItem = esTiktokItems.stream().collect(Collectors.groupingBy(EsTiktokItem::getProductId));
        productIdToItem.forEach((key, value) -> {
            // 检查是否符合下架条件
            if (checkDeleteItem(value, offlineType, createBy)) {
                deleteProductIdList.add(key);
            }
        });
        if (CollectionUtils.isEmpty(deleteProductIdList)) {
            log.info("账号：" + accountNumber + "没有需要操作的数据");
            return;
        }

        List<List<String>> lists = PagingUtils.newPagingList(deleteProductIdList, 20);
        CompletableFuture.allOf(lists.stream().map(deleteProductIds -> CompletableFuture.runAsync(() -> {
            DataContextHolder.setUsername(createBy);
            List<EsTiktokItem> tiktokItemList = new ArrayList<>();
            deleteProductIds.forEach(o -> tiktokItemList.addAll(productIdToItem.get(o)));

            // 生成处理报告
            Map<String, List<FeedTask>> feedTaskMap = tiktokFeedTaskHelper.initItemFeedTask(tiktokItemList, offlineType, null, null);

            try {
                // 请求接口
                Map<String, String> resultMap = new HashMap<>();
                if (TiktokOperateTypeEnum.DEACTIVATE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
                    resultMap = EnvironmentSupplierWrapper.execute(() -> {
                        return new TiktokProductCall(account).deactivateProduct(deleteProductIds);
                    }, () -> {
                        Map<String, String> deactiveMap = new HashMap<>();
                        for (String deleteProductId : deleteProductIds) {
                            deactiveMap.put(deleteProductId, "非正式环境，无法下架！");
                        }
                        return deactiveMap;
                    });
                } else if (TiktokOperateTypeEnum.DELETE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
                    resultMap = EnvironmentSupplierWrapper.execute(() -> {
                        return new TiktokProductCall(account).deleteProduct(deleteProductIds);
                    }, () -> {
                        Map<String, String> deactiveMap = new HashMap<>();
                        for (String deleteProductId : deleteProductIds) {
                            deactiveMap.put(deleteProductId, "非正式环境，无法删除！");
                        }
                        return deactiveMap;
                    });
                } else {
                    throw new RuntimeException("下架类型错误");
                }

                // 更新处理报告
                for (String productId : deleteProductIds) {
                    List<FeedTask> feedTasks = feedTaskMap.get(productId);
                    String message = resultMap.get(productId);
                    if (StringUtils.isBlank(message)) {
                        feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                                ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), remark);
                        // 成功后修改本地数据
                        tiktokEsItemBulkProcessor.updateItemStatus(productIdToItem.get(productId), offlineType, remark);
                    } else {
                        feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                                ResultStatusEnum.RESULT_FAIL.getStatusCode(), message);
                    }
                }
            } catch (Exception e) {
                List<FeedTask> feedTasks = new ArrayList<>();
                feedTaskMap.values().forEach(feedTasks::addAll);
                feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                        ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
            }
        }, TiktokExecutors.BATCH_DEACTIVATE_DELETE_POOL)).toArray(CompletableFuture[]::new)).join();
    }

    private boolean checkDeleteItem(List<EsTiktokItem> tiktokItems, String offlineType, String createBy) {
        try {
            // 系统操作无需校验
            if (StrConstant.ADMIN.equals(createBy)) {
                return true;
            }

            if (TiktokOperateTypeEnum.DELETE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
                // 判断全部商品id的总销量是否为0
                String globalProductId = tiktokItems.stream().map(EsTiktokItem::getGlobalProductId).filter(StringUtils::isNotBlank).findFirst().orElse(null);
                if (StringUtils.isNotBlank(globalProductId)) {
                    Boolean b = checkGlobalProductIdCanDelete(globalProductId);
                    if (BooleanUtils.isTrue(b)) {
                        return true;
                    }
                }
            }

            List<EsTiktokItem> tiktokItemList = new ArrayList<>(tiktokItems);

            if (TiktokOperateTypeEnum.DEACTIVATE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
                // 正常状态的上架超过30天且无历史销量的允许下架

                Date date30DaysBefore = DateUtils.getDateXDaysAfterOrBefore(new Date(), -30);
                boolean isAllow = tiktokItemList.stream().allMatch(item -> {
                    // 单品状态检验
                    boolean skuStatusCheck = SkuStatusEnum.NORMAL.getCode().equals(item.getSkuStatus());
                    // 上架时间检验,取创建时间判断
                    boolean onlineTimeCheck = null != item.getCreationDate() && item.getCreationDate().before(date30DaysBefore);
                    // 销量检验,取总销量判断
                    boolean salesCheck = item.getOrder_num_total() == null ||
                            (Objects.equals(item.getOrder_num_total(), 0) && CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(SaleChannel.CHANNEL_TIKTOK + "_" + item.getId()));
                    return skuStatusCheck && onlineTimeCheck && salesCheck;
                });

                if (isAllow) {
                    return true;
                }
            }

            // 状态为FAILED，FREEZE可以下架
            tiktokItemList.removeIf(o -> StringUtils.isBlank(o.getProductStatus()) || TiktokConstant.DELETE_ITEM_STATUS.contains(o.getProductStatus()));
            if (CollectionUtils.isEmpty(tiktokItemList)) {
                return true;
            }

            // 单品状态为停产存档的可以下架
            Iterator<EsTiktokItem> it = tiktokItemList.iterator();
            while (it.hasNext()) {
                EsTiktokItem item = it.next();
                if (StringUtils.isBlank(item.getSkuStatus()) || !TiktokConstant.DELETE_SKU_STATUS.contains(item.getSkuStatus())) {
                    continue;
                }
                // 校验产品系统和在线列表状态是否一致
                Boolean checked = CheckSkuUtils.checkSkuStatus(item.getSku(), item.getSkuDataSource(), SaleChannel.CHANNEL_TIKTOK);
                if (!checked) {
                    return false;
                }
                it.remove();
            }
            if (CollectionUtils.isEmpty(tiktokItemList)) {
                return true;
            }

            // 对应平台站点禁售可以下架
            String site = tiktokAccountConfigService.getAccountSite(tiktokItemList.get(0).getAccountNumber());
            Iterator<EsTiktokItem> iterator = tiktokItemList.iterator();
            while (iterator.hasNext()) {
                EsTiktokItem item = iterator.next();
                if (StringUtils.isBlank(item.getForbidChannel())
                        || CollectionUtils.isEmpty(item.getProhibitionSites())
                        || !item.getForbidChannel().contains(SaleChannel.CHANNEL_TIKTOK)
                        || !item.getProhibitionSites().contains(SaleChannel.CHANNEL_TIKTOK + "_" + site)) {
                    continue;
                }
                // 校验产品系统和在线列表禁售信息是否一致
                Boolean checked = TiktokItemLocalUtils.checkSkuForbid(item.getSku(), item.getSkuDataSource(), site);
                if (!checked) {
                    return false;
                }
                iterator.remove();
            }
            return CollectionUtils.isEmpty(tiktokItemList);
        } catch (Exception e) {
            log.error("校验产品是否可以删除下架失败：" + e.getMessage());
            return false;
        }
    }

    private Boolean checkGlobalProductIdCanDelete(String globalProductId) {
        EsTiktokItemRequest request = new EsTiktokItemRequest();
        request.setQueryFields(new String[]{"id", "globalProductId", "order_num_total"});
        request.setGlobalProductId(globalProductId);
        List<EsTiktokItem> esTiktokItems = this.getEsTiktokItems(request);
        long sum = esTiktokItems.stream().filter(a -> a.getOrder_num_total() != null).mapToLong(EsTiktokItem::getOrder_num_total).sum();
        if (sum != 0) {
            return false;
        }
        for (EsTiktokItem esTiktokItem : esTiktokItems) {
            boolean b = CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(SaleChannel.CHANNEL_TIKTOK + "_" + esTiktokItem.getId());
            if (!b) {
                return false;
            }
        }
        return true;
    }

    private List<EsTiktokItem> findItemByAccountAndProductIds(String accountNumber, List<String> productIdList, String offlineType) {
        if (StringUtils.isBlank(accountNumber) && CollectionUtils.isEmpty(productIdList)) {
            throw new RuntimeException("参数异常");
        }

        List<EsTiktokItem> esTiktokItems = new ArrayList<>();
        String id = null;
        EsTiktokItemRequest request = new EsTiktokItemRequest();
        request.setPageSize(1000);
        request.setPageIndex(0);
        request.setOrderBy("id");
        request.setSequence("ASC");
        String[] fields = {"id", "sku", "skuStatus", "productId", "skuId", "accountNumber",
                "sellerSku", "forbidChannel", "prohibitionSites", "skuDataSource", "productStatus", "globalProductId", "creationDate", "order_num_total"};
        request.setPageFields(fields);
        request.setAccountNumber(accountNumber);
        if (TiktokOperateTypeEnum.DEACTIVATE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
            request.setExcludeProductStatus(TiktokProductStatusEnum.SELLER_DEACTIVATED.getStatusMsgEn());
        } else if (TiktokOperateTypeEnum.DELETE_PRODUCT.getStatusMsgEn().equals(offlineType)) {
            request.setExcludeProductStatus(TiktokProductStatusEnum.DELETED.getStatusMsgEn());
            request.setGlobalProductIdIsNull(false);
        }
        if (CollectionUtils.isNotEmpty(productIdList)) {
            request.setProductIdList(productIdList);
        }
        while (true) {
            if (StringUtils.isNotBlank(id)) {
                request.setGreaterThanId(id);
            }
            PageInfo<EsTiktokItem> page = this.page(request);
            if (null == page) {
                break;
            }
            List<EsTiktokItem> esTiktokItemList = new ArrayList<>(page.getContents());
            if (CollectionUtils.isEmpty(esTiktokItemList)) {
                break;
            }
            esTiktokItems.addAll(esTiktokItemList);

            // 分页
            id = esTiktokItems.get(esTiktokItems.size() - 1).getId();
        }

        return esTiktokItems;
    }

    @Override
    public void updateListingPrice(List<TiktokCalcPriceRequest> tiktokCalcPriceRequests) {
        Map<String, List<TiktokCalcPriceRequest>> accountMap = tiktokCalcPriceRequests.stream().collect(Collectors.groupingBy(TiktokCalcPriceRequest::getAccountNumber));
        for (Map.Entry<String, List<TiktokCalcPriceRequest>> entry : accountMap.entrySet()) {
            String accountNumber = entry.getKey();
            List<TiktokCalcPriceRequest> requests = entry.getValue();

            // 算价
            TiktokCalcPriceUtils.calcTiktokPrice(accountNumber, requests);

            // 记录处理报告
            Map<String, List<FeedTask>> feedTaskMap = tiktokFeedTaskHelper.initPriceFeedTask(requests);
            requests.removeIf(o -> BooleanUtils.isFalse(o.getIsSuccess()));
            if (CollectionUtils.isEmpty(requests)) {
                continue;
            }

            // 修改价格
            submitUpdatePrice(accountNumber, requests, feedTaskMap);
        }
    }

    private void submitUpdatePrice(String accountNumber, List<TiktokCalcPriceRequest> requestList, Map<String, List<FeedTask>> feedTaskMap) {
        try {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, accountNumber);
            if (null == account) {
                throw new RuntimeException(String.format("账号：%s查询账号信息失败", accountNumber));
            }

            // 根据productId分组提交
            Map<String, List<TiktokCalcPriceRequest>> idToRequestMap = requestList.stream().collect(Collectors.groupingBy(TiktokCalcPriceRequest::getProductId));
            CompletableFuture.allOf(idToRequestMap.values().stream().map(requests -> CompletableFuture.runAsync(() -> {
                String productId = requests.get(0).getProductId();
                List<FeedTask> feedTasks = feedTaskMap.get(productId);
                if (CollectionUtils.isEmpty(feedTasks)) {
                    throw new RuntimeException("获取处理报告异常，productId" + productId);
                }
                try {
                    new TiktokProductCall(account).updatePrice(requests);

                    // 本地保存
                    tiktokEsItemBulkProcessor.updateItemPrice(requests, feedTasks.get(0).getCreatedBy());

                    // 更新处理报告
                    Map<String, FeedTask> skuIdToTaskMap =
                            feedTasks.stream().collect(Collectors.toMap(FeedTask::getAttribute4, item -> item));
                    for (TiktokCalcPriceRequest request : requests) {
                        FeedTask feedTask = skuIdToTaskMap.get(request.getSkuId());
                        tiktokFeedTaskHelper.succeedFeedTask(feedTask, request.getPrice(), request.getAfterPrice());
                    }
                } catch (Exception e) {
                    feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                            ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
                }
            }, TiktokExecutors.UPDATE_PRICE_POOL)).toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            List<FeedTask> feedTasks = new ArrayList<>();
            feedTaskMap.values().forEach(feedTasks::addAll);
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
        }
    }

    @Override
    public void syncEmptyDetail(String accountNumber, List<String> productIdList) {
        String gtProductId = "";
        int pageSize = 100;
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, accountNumber);

        while (true) {
            EsTiktokItemRequest esTiktokItemRequest = new EsTiktokItemRequest();
            esTiktokItemRequest.setAccountNumber(accountNumber);
            esTiktokItemRequest.setMainImageIsNull(true);
            esTiktokItemRequest.setGtProductId(gtProductId);
            esTiktokItemRequest.setPageSize(pageSize);
            esTiktokItemRequest.setOrderBy("productId");
            esTiktokItemRequest.setSequence("ASC");
            esTiktokItemRequest.setProductIdList(productIdList);

            List<String> productIds = esTiktokItemService.getEsItemGroupByProductId(esTiktokItemRequest);
            if (CollectionUtils.isEmpty(productIds)) {
                return;
            }
            gtProductId = productIds.get(productIds.size() - 1);

            EsTiktokItemRequest request = new EsTiktokItemRequest();
            request.setProductIdList(productIds);
            request.setAccountNumber(accountNumber);
            List<EsTiktokItem> esTiktokItems = esTiktokItemService.getEsTiktokItems(request);
            tiktokSyncItemHelper.syncSaveItemDetail(saleAccount, esTiktokItems, TiktokExecutors.SYNC_NO_ITEM_DETAIL_POOL);
        }
    }

    @Override
    public void syncEmptyGlobalProductId(String accountNumber, List<String> productIdList) {
        String gtId = "";
        int pageSize = 1000;
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_TIKTOK, accountNumber);

        Set<String> allProductIds = new HashSet<>();

        while (true) {
            EsTiktokItemRequest esTiktokItemRequest = new EsTiktokItemRequest();
            esTiktokItemRequest.setAccountNumber(accountNumber);
            esTiktokItemRequest.setGlobalProductIdIsNull(true);
            esTiktokItemRequest.setGreaterThanId(gtId);
            esTiktokItemRequest.setPageSize(pageSize);
            esTiktokItemRequest.setPageIndex(0);
            esTiktokItemRequest.setOrderBy("id");
            esTiktokItemRequest.setSequence("ASC");
            esTiktokItemRequest.setProductIdList(productIdList);
            esTiktokItemRequest.setPageFields(new String[]{"id", "productId", "globalProductId", "sellerSku"});
            PageInfo<EsTiktokItem> page = esTiktokItemService.page(esTiktokItemRequest);
            List<EsTiktokItem> contents = page.getContents();
            if (CollectionUtils.isEmpty(contents)) {
                return;
            }
            gtId = contents.get(contents.size() - 1).getId();

            Set<String> collect1 = contents.stream().map(EsTiktokItem::getProductId).filter(a -> !allProductIds.contains(a)).collect(Collectors.toSet());
            allProductIds.addAll(collect1);
            EsTiktokItemRequest request = new EsTiktokItemRequest();
            request.setAccountNumber(accountNumber);
            request.setProductIdList(new ArrayList<>(collect1));
            request.setQueryFields(new String[]{"id", "productId", "globalProductId", "sellerSku"});
            List<EsTiktokItem> esTiktokItems = esTiktokItemService.getEsTiktokItems(request);

            esTiktokItems.sort(Comparator.comparing(EsTiktokItem::getProductId));

            Map<String, List<EsTiktokItem>> collect = esTiktokItems.stream().collect(Collectors.groupingBy(EsTiktokItem::getProductId));
            CompletableFuture.allOf(collect.values().stream().map(tiktokGlobalItem -> CompletableFuture.runAsync(() -> {
                try {
                    try {
                        tiktokSyncItemHelper.syncGlobalProductId(saleAccount, tiktokGlobalItem);
                    } catch (Exception e) {
                        log.error("获取商品全球id失败：{}，商品id：{}", e.getMessage(), tiktokGlobalItem.get(0).getProductId());
                    }
                } catch (Exception ignored) {
                    log.error("获取商品全球id失败：{}，商品id：{}", ignored.getMessage(), tiktokGlobalItem.get(0).getProductId());
                }
            }, TiktokExecutors.SYNC_GLOBAL_ITEM_ID_POOL)).toArray(CompletableFuture[]::new)).join();

        }
    }
}
