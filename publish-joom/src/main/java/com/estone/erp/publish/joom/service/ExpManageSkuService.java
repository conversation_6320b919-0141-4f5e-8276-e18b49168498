package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.ExpManageSku;
import com.estone.erp.publish.joom.model.ExpManageSkuCriteria;
import com.estone.erp.publish.joom.model.ExpManageSkuExample;

import java.util.List;

/**
 * <AUTHOR> exp_manage_sku
 * 2022-11-29 15:18:11
 */
public interface ExpManageSkuService {
    int countByExample(ExpManageSkuExample example);

    CQueryResult<ExpManageSku> search(CQuery<ExpManageSkuCriteria> cquery);

    List<ExpManageSku> selectByExample(ExpManageSkuExample example);

    ExpManageSku selectByPrimaryKey(Integer id);

    int insert(ExpManageSku record);

    int updateByPrimaryKeySelective(ExpManageSku record);

    int updateByExampleSelective(ExpManageSku record, ExpManageSkuExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    ApiResult<List<String>> expSkus();

    List<ExpManageSku> searchExistRecord(List<String> skuList, List<String> batchNos);

    void batchUpdateRecord(List<ExpManageSku> updateRecord);

    void batchInsertRecord(List<ExpManageSku> insertRecord);

    List<ExpManageSku> selectIdByExample(ExpManageSkuExample example);


    void batchResumePromotion(List<ExpManageSku> expManageSkuList);
}