package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch.model.EsRecommendProduct;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsRecommendProductRequest;
import com.estone.erp.publish.elasticsearch.service.EsRecommendProductService;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 推荐产品同步产品变更消息消费者
 */
@Slf4j
@Component
public class RecommendProductSyncProductConsumer {

    @Autowired
    private EsRecommendProductService esRecommendProductService;

    private static final int MAX_RETRY_COUNT = 3;

    private static final int PAGE_SIZE = 1000;

    @RabbitListener(queues = PublishQueues.RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE, containerFactory = "batchConsumeFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }
            //log.info("on message:{}", body);
            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_JOOM);
            }
        } catch (Exception e) {
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_JOOM);
            log.error("RecommendProduct RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE Exception error: {}", e.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新推荐产品列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        syncProductInfo(skuList);
        return true;
    }

    private void syncProductInfo(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }
        //log.info(" skuList 更新条数:" + skuList.size());
        Map<String, ProductInfoVO> map = new HashMap<>(skuList.size());
        List<EsRecommendProduct> updateRecommendProductList = new ArrayList<>();
        EsRecommendProductRequest request = new EsRecommendProductRequest();
        request.setArticleNumberList(skuList);
        request.setRecommendDate(DateUtils.dateToString(DateUtils.getToday(),"yyyy-MM-dd HH:mm:ss"));
        List<EsRecommendProduct> esRecommendProductList =esRecommendProductService .getEsRecommendProductList(request,PAGE_SIZE);
        if (CollectionUtils.isEmpty(esRecommendProductList)){
            return;
        }
        //log.info(" esRecommendProductList更新条数:" + esRecommendProductList.size());
        esRecommendProductList.forEach(esRecommendProduct -> {
            updateProductInfo(esRecommendProduct, map, updateRecommendProductList);
        });

        if (CollectionUtils.isEmpty(updateRecommendProductList)) {
            return;
        }
        esRecommendProductService.saveAll(updateRecommendProductList);
    }

    private void updateProductInfo(EsRecommendProduct esRecommendProduct, Map<String, ProductInfoVO> productMap, List<EsRecommendProduct> updateRecommendProductList) {
        String articleNumber = esRecommendProduct.getArticleNumber();
        try {
            if (StringUtils.isBlank(articleNumber)) {
                return;
            }
            articleNumber = articleNumber.toUpperCase().trim();
            // 查询产品信息
            ProductInfoVO productInfoVO = productMap.get(articleNumber);
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                productMap.put(articleNumber, productInfoVO);
            }
            if (ObjectUtils.isEmpty(productInfoVO)) {
                return;
            }
            // 修改产品信息
            setProductInfo(esRecommendProduct, productInfoVO);
            Date time = new Date();
            if ( DateUtils.isEmptyDate(esRecommendProduct.getCreateDate())) {
                if (StringUtils.isNotBlank(esRecommendProduct.getRecommendDate())) {
                    //推荐时间影响查询
                    esRecommendProduct.setCreateDate(DateUtils.stringToDate(esRecommendProduct.getRecommendDate(), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    esRecommendProduct.setCreateDate(time);
                }
            }
            esRecommendProduct.setUpdateDate(time);
            updateRecommendProductList.add(esRecommendProduct);
        } catch (Exception e) {
            log.error(articleNumber + "更新产品信息失败：{}", e.getMessage(), e);
        }
    }
    /**
     * 设置产品系统信息
     * @param esRecommendProduct
     * @param productInfoVO
     */
    public static void setProductInfo(EsRecommendProduct esRecommendProduct, ProductInfoVO productInfoVO) {
        if (null == productInfoVO){
            return;
        }
        try {
            //主图
            esRecommendProduct.setMainImage(productInfoVO.getMainImage());
            // 标题
            esRecommendProduct.setName(productInfoVO.getName());
            //产品 singleitemes.categoryPath,这里存的是ES中的类目id路径
            esRecommendProduct.setProCategoryId(productInfoVO.getCategoryId());
            // 类别中文名
            esRecommendProduct.setProCategoryCnName(productInfoVO.getCategoryCnName());
            //销售成本价
            esRecommendProduct.setSaleCost(productInfoVO.getSaleCost());
            // 单品状态
            esRecommendProduct.setSkuStatus(productInfoVO.getSkuStatus());
            // 是否促销
            esRecommendProduct.setPromotion(productInfoVO.getPromotion());
            // 是否新品
            esRecommendProduct.setNewState(productInfoVO.getNewState());
            // 禁售平台
            esRecommendProduct.setForbidChannel(CommonUtils.splitList(productInfoVO.getForbidChannel(), ","));
            // 禁售类型
            esRecommendProduct.setInfringementTypeNames(CommonUtils.splitList(productInfoVO.getInfringementTypeName(),"|"));
            // 禁售原因
            esRecommendProduct.setInfringementObjs(CommonUtils.splitList(productInfoVO.getInfringementObj(), "|"));
            // 禁售站点
            esRecommendProduct.setProhibitionSites(productInfoVO.getProhibitionSiteWithPlatformDefaultSite());
            // 重量
            Double weight = (null == productInfoVO.getPackageWeight() || productInfoVO.getPackageWeight()==0.0)? productInfoVO.getTotalWeight(3.0) : productInfoVO.getPackageWeight();
            if (null != weight) {
                esRecommendProduct.setWeight(weight);
            }
        } catch (Exception e) {
            log.error(String.format("同步listing数据获取产品SKu信息: %s, ", esRecommendProduct.getArticleNumber()), e);
        }
    }

}
