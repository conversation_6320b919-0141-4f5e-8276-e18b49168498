package com.estone.erp.publish.joom.service;

import java.util.List;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.JoomTemplate;
import com.estone.erp.publish.joom.model.JoomTemplateExample;
import com.estone.erp.publish.joom.model.dto.JoomTemplateCriteria;

/**
 * <AUTHOR> joom_template
 * 2019-08-08 10:45:07
 */
public interface JoomTemplateService {
    int countByExample(JoomTemplateExample example);

    CQueryResult<JoomTemplate> search(CQuery<JoomTemplateCriteria> cquery);

    List<JoomTemplate> selectByExample(JoomTemplateExample example);

    JoomTemplate selectByPrimaryKey(Integer templateId);

    int insert(JoomTemplate record);

    int insertSelective(JoomTemplate record);

    int updateByPrimaryKeySelective(JoomTemplate record);

    int updateJoomTemplate(JoomTemplate record);

    int updateByExampleSelective(JoomTemplate record, JoomTemplateExample example);

    int deleteByPrimaryKey(Integer templateId);

    int deleteParentsBySkuStatus();

    int deleteByExample(JoomTemplateExample example);

    void batchInsert(List<JoomTemplate> recordList);

    void batchUpdate(List<JoomTemplate> recordList);

    void batchDelete(List<Integer> ids);

    String batchCopy(List<JoomTemplate> parentTemplateList);

    void batchSetWeight(List<JoomTemplate> templateList);
}