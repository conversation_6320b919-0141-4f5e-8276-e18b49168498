package com.estone.erp.publish.joom.service;

import java.util.List;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.Host;
import com.estone.erp.publish.joom.model.HostCriteria;
import com.estone.erp.publish.joom.model.HostExample;

/**
 * <AUTHOR> t_host
 * 2019-08-12 18:15:59
 */
public interface HostService {
    int countByExample(HostExample example);

    CQueryResult<Host> search(CQuery<HostCriteria> cquery);

    List<Host> selectByExample(HostExample example);

    Host selectByPrimaryKey(Integer hostId);

    int insert(Host record);

    int insertSelective(Host record);

    int updateByPrimaryKeySelective(Host record);

    int updateByExampleSelective(Host record, HostExample example);

    int deleteByPrimaryKey(Integer hostId);

    int deleteByExample(HostExample example);

    List<String> selectHostSiteList();

    void batchUpdate(List<Host> hostList);

    void batchUpdateSite(List<Host> hostList, String hostSite);
}