package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2025-01-03 14:57:29
 */
public class JoomUpdateStockZeroRecordsCriteria extends JoomUpdateStockZeroRecords {
    private static final long serialVersionUID = 1L;

    public JoomUpdateStockZeroRecordsExample getExample() {
        JoomUpdateStockZeroRecordsExample example = new JoomUpdateStockZeroRecordsExample();
        JoomUpdateStockZeroRecordsExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (StringUtils.isNotBlank(this.getItemId())) {
            criteria.andSellerSkuEqualTo(this.getItemId());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getSkuStatus())) {
            criteria.andSkuStatusEqualTo(this.getSkuStatus());
        }
        if (StringUtils.isNotBlank(this.getBeforeValue())) {
            criteria.andBeforeValueEqualTo(this.getBeforeValue());
        }
        if (StringUtils.isNotBlank(this.getAfterValue())) {
            criteria.andAfterValueEqualTo(this.getAfterValue());
        }
        if (this.getResultStauts() != null) {
            criteria.andResultStautsEqualTo(this.getResultStauts());
        }
        if (StringUtils.isNotBlank(this.getResultMsg())) {
            criteria.andResultMsgEqualTo(this.getResultMsg());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getUpdateTime() != null) {
            criteria.andUpdateTimeEqualTo(this.getUpdateTime());
        }
        return example;
    }
}