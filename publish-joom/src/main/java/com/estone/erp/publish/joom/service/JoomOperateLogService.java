package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.JoomOperateLog;
import com.estone.erp.publish.joom.model.JoomOperateLogCriteria;
import com.estone.erp.publish.joom.model.JoomOperateLogExample;
import java.util.List;

/**
 * <AUTHOR> joom_operate_log
 * 2022-11-10 11:52:21
 */
public interface JoomOperateLogService {
    int countByExample(Jo<PERSON><PERSON><PERSON>ateLogExample example);

    CQueryResult<JoomOperateLog> search(CQuery<JoomOperateLogCriteria> cquery);

    List<JoomOperateLog> selectByExample(Joom<PERSON>perateLogExample example);

    JoomOperateLog selectByPrimaryKey(Integer id);

    int insert(JoomOperateLog record);

    int updateByPrimaryKeySelective(JoomOperateLog record);

    int updateByExampleSelective(JoomOperateLog record, <PERSON>om<PERSON>perateLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);
}