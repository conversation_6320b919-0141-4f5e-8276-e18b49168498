package com.estone.erp.publish.joom.api.thread;

import java.sql.Timestamp;
import java.util.concurrent.CountDownLatch;

import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.JoomTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import org.apache.commons.lang.StringUtils;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.call.JoomUpdateItemCall;
import com.estone.erp.publish.joom.call.JoomUpdateVariationCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EditJoomItemThread implements Runnable {
    private CountDownLatch countDownLatch;
    private JoomItem joomItem;
    private JoomItem dbJoomItem;
    private ResponseJson response;
    private FeedTaskService feedTaskService;
    private String userName;

    public EditJoomItemThread(JoomItem joomItem,JoomItem dbJoomItem, CountDownLatch countDownLatch, ResponseJson response, FeedTaskService feedTaskService, String userName) {
        super();
        this.joomItem = joomItem;
        this.dbJoomItem = dbJoomItem;
        this.countDownLatch = countDownLatch;
        this.response = response;
        this.feedTaskService = feedTaskService;
        this.userName = userName;
    }

    @Override
    public void run() {
        if (StringUtils.isBlank(joomItem.getItemSeller())) {
            response.getErrors()
                    .add(new ResponseError("帐号为空", joomItem.getJoomItemId() + "," + joomItem.getSku(), "卖家帐号为空！"));
        }

        try {
            SaleAccountAndBusinessResponse joomPmsAccount = 
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, joomItem.getItemSeller());

            JoomUpdateVariationCall call = new JoomUpdateVariationCall(joomPmsAccount);
            ResponseJson json = call.updateVariation(joomItem);

            try {
                FeedTask feedTask = new FeedTask();
                feedTask.setPlatform(Platform.Joom.name());
                feedTask.setTableIndex();
                feedTask.setAssociationId(dbJoomItem.getJoomItemId());
                feedTask.setAccountNumber(joomPmsAccount.getAccountNumber());
                feedTask.setArticleNumber(dbJoomItem.getArticleNumber());
                feedTask.setTaskType(JoomTaskTypeEnum.UPDATE_STOCK_AND_PRICE.getStatusMsgEn());
                feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                feedTask.setResultStatus(json.isSuccess() ? ResultStatusEnum.RESULT_SUCCESS.getStatusCode() : ResultStatusEnum.RESULT_FAIL.getStatusCode());
                feedTask.setResultMsg(json.getMessage());
                feedTask.setCreatedBy(userName);
                feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                //价格前
                feedTask.setAttribute1(dbJoomItem.getPrice().toString());
                //价格后
                feedTask.setAttribute2(joomItem.getPrice().toString());
                //库存前
                feedTask.setAttribute3(dbJoomItem.getInventory().toString());
                //库存后
                feedTask.setAttribute4(joomItem.getInventory().toString());
                feedTaskService.insert(feedTask);
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            JoomUpdateItemCall updateCall = new JoomUpdateItemCall(joomPmsAccount);
            updateCall.updateDangerouserKind(joomItem);
            response.addError(new ResponseError(json.getStatus(), joomItem.getJoomItemId() + "," + joomItem.getSku(),
                    json.getMessage() + "-" + json.getErrors().toString()));
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            countDownLatch.countDown();
        }

    }

}
