package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomItemExample;
import com.estone.erp.publish.joom.model.dto.JoomItemCriteria;
import com.estone.erp.publish.joom.util.modal.UpdateCountryShipping;
import com.estone.erp.publish.system.skuSellAccountAmount.model.SkuSellAccountAmount;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> joom_item 2019-08-08 10:44:45
 */
public interface JoomItemService {
    int countByExample(JoomItemExample example);

    int countJoomItemListByExample(JoomItemExample example);

    CQueryResult<JoomItem> search(CQuery<JoomItemCriteria> cquery);

    CQueryResult<JoomItem> searchJoomItemList(CQuery<JoomItemCriteria> cquery);

    List<JoomItem> selectByExample(JoomItemExample example);

    List<JoomItem> selectJoomItemListByExample(JoomItemExample example);

    JoomItem selectByPrimaryKey(Long itemId);

    int insert(JoomItem record);

    int insertSelective(JoomItem record);

    int updateByPrimaryKeySelective(JoomItem record);

    int updateByExampleSelective(JoomItem record, JoomItemExample example);

    int deleteByPrimaryKey(Integer itemId);

    int deleteByExample(JoomItemExample example);

    void batchUpdateJoomItem(List<JoomItem> entityList);

    void batchInsertBySync(List<JoomItem> createProducts);

    void synchJoomItem(List<JoomItem> joomItems);

    List<JoomItem> selectUpdateListByFlag(JoomItemCriteria query);

    List<JoomItem> selectUpdateListByMeasurement(JoomItemCriteria query);

    ResponseJson batchUpdatePrice(List<JoomItem> itemList);

    ResponseJson batchUpdateMeasurement(List<JoomItem> itemList);

    Boolean isExistJoomItem(String sellerId, List<String> skuList);

    void disableJoomProduct(List<String> updateSkus, String account);

    List<String> selectEmptyCategoryItem();

    void saveJoomItem(JoomItem joomItem);

    /**
     * 查询上架状态sku总数
     * @param joomItem
     * @return
     */
    Integer selectJoomOnsellingSkuCount(JoomItem joomItem);

    /**
     * 查询货号
     * @param example
     * @return
     */
    List<String> selectJoomOnSellingSkuByExample(JoomItemExample example);

    /**
     * 根据条件统计货号售卖店铺数量
     * @param articleNumberList
     * @param record
     * @param day
     * @return
     */
    List<SkuSellAccountAmount> getJoomOnSellingSkuListingNum(List<String> articleNumberList, JoomItem record, Integer day);

    /**
     * 批量同步国家运费
     * @param joomItems
     * @return
     */
    ApiResult<?> synchCountryShipping(List<JoomItem> joomItems);

    /**
     * 批量修改渠道运费
     * @param batchUpdateCountryShippings
     * @return
     */
    ApiResult<?> batchUpdateChannelShipping(List<UpdateCountryShipping> batchUpdateCountryShippings);

    /**
     * 过滤 不是在线 不是 active，pending，warning 状态的引流SKU
     * @param account
     * @param skus
     * @return
     */
    List<String> filterNotEnabledDrainageSku(String account, List<String> skus);

    /**
     * 获取JoomItem
     * @param id
     * @return
     */
    ApiResult<JoomItem> getInfoById(Long id);

    /**
     * 获取变体
     * @param joomItemId
     * @param itemSeller
     * @return
     */
    List<JoomItem> loadVariations(String joomItemId, String itemSeller);

    /**
     * 获取指定状态的在线列表sku
     */
    List<String> listJoomSkuBySkuStatus(List<String> statusList, Integer offset, Integer limit);

    /**
     * 清仓甩卖库存改零限制
     */
    ApiResult<Map<String, Integer>> checkStockReductionAndClean(List<Integer> itemIds);


    /**
     * 获取指定时间段内的店铺新增链接数
     * @return 新增链接数
     */
    int getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime);


    /**
     * 获取店铺侵权禁售链接数
     * @param accountNumberList 店铺
     */
    int getForbiddenListingNum(List<String> accountNumberList);


    /**
     * 获取停产存档库存不为0链接
     * @param accountNumberList 店铺
     */
    int getStopStatusListingNum(List<String> accountNumberList);


    /**
     * 获取店铺库存不足链接数
     *
     * @param accountNumberList 店铺
     * @param stockThreshold
     */
    int getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold);


    /**
     * 获取店铺在线链接总数
     * @param accountNumberList 店铺
     */
    int getOnlineListingNum(List<String> accountNumberList);

}