package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomTemplate;
import com.estone.erp.publish.joom.model.JoomTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JoomTemplateMapper {
    int countByExample(JoomTemplateExample example);

    int deleteByExample(JoomTemplateExample example);

    int deleteByPrimaryKey(Integer templateId);

    int deleteParentsBySkuStatus();

    int insert(JoomTemplate record);
    
    void batchInsertJoomTemplate(@Param("recordList") List<JoomTemplate> recordList);

    int insertSelective(JoomTemplate record);

    List<JoomTemplate> selectByExample(JoomTemplateExample example);

    JoomTemplate selectByPrimaryKey(Integer templateId);

    int updateByExampleSelective(@Param("record") JoomTemplate record, @Param("example") JoomTemplateExample example);

    int updateByExample(@Param("record") JoomTemplate record, @Param("example") JoomTemplateExample example);

    int updateByPrimaryKeySelective(JoomTemplate record);

    //页面范本模板修改，允许价格数量传null
    int updateJoomTemplate(JoomTemplate record);

    int updateByPrimaryKey(JoomTemplate record);

    void batchUpdateJoomTemplate(@Param("recordList") List<JoomTemplate> recordList);

    void batchDelete(@Param("ids") List<Integer> ids);
}