package com.estone.erp.publish.joom.common;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.util.JoomTemplateUtils;
import com.estone.erp.publish.joom.util.MapUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *
 */
@Slf4j
public class JoomProductCommon {

    private static Cache<String, ProductInfoVO> PRODUCT_INFO_CACHE;

    static {
        PRODUCT_INFO_CACHE = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.MINUTES).maximumSize(1000).build();
    }

    @SuppressWarnings("unchecked")
    public static List<JoomItem> getJoomProducts(Map<String, Object> productParam, SaleAccountAndBusinessResponse joomPmsAccount) {
        EsSkuBindService skuBindService = SpringUtils.getBean(EsSkuBindService.class);

        List<JoomItem> createProducts = new ArrayList<JoomItem>();

        // 基本item
        JoomItem generalItem = buildItem(joomPmsAccount, productParam);
        generalItem.setIsVariation(false);
        generalItem.setIsFirstItem(false);

        String parentSku = generalItem.getParentSku();

        // 先查询绑定sku
        EsSkuBind skuBind = skuBindService.getEsSkuBind(parentSku, SaleChannel.CHANNEL_JOOM);
        if (skuBind != null) {
            generalItem.setArticleNumber(skuBind.getSku().trim());
            generalItem.setParentSku(skuBind.getSku().trim());

        }
        else {
            // 与产品系统对应的sku
            String articleNumber = generalItem.getParentSku();

            if (StringUtils.isNotBlank(joomPmsAccount.getSellerSkuPrefix())
                    && StringUtils.startsWith(parentSku, joomPmsAccount.getSellerSkuPrefix())) {
                articleNumber = StringUtils.substringAfter(parentSku, joomPmsAccount.getSellerSkuPrefix());
            }

            // 对应系统的sku
            generalItem.setArticleNumber(articleNumber);
        }

        // 补充产品系统信息
        assembleProductInfo(generalItem);

        createProducts.add(generalItem);

        List<Object> tagList = (List<Object>) productParam.get("tags");
        if (CollectionUtils.isNotEmpty(tagList)) {
            // 标签
            StringBuffer tagBuffer = new StringBuffer();

            Iterator<Object> iterator = tagList.iterator();

            while (iterator.hasNext()) {
                Object tag = iterator.next();

                Map<String, Object> tagObj = (Map<String, Object>) tag;

                Map<String, Object> tagParam = (Map<String, Object>) tagObj.get("Tag");

                String name = MapUtils.getString(tagParam, "name");
                tagBuffer.append(name);

                if (iterator.hasNext()) {
                    tagBuffer.append(",");
                }
            }

            generalItem.setTags(JoomTemplateUtils.sortTransition(tagBuffer.toString()));

        }

        // 敏感货分类
        // String dangerousKind = MapUtils.getString(productParam,
        // "dangerous_kind");

        List<Object> variantList = (List<Object>) productParam.get("variants");

        // 多属性
        Boolean isMultiAttr = true;

        // 是否上架
        Boolean isOnline = false;

        int i = 0;

        boolean measurementNull = false; //先默认不为空，如果存在一个为空就是true

        if (CollectionUtils.isNotEmpty(variantList)) {
            for (Object variant : variantList) {
                Map<String, Object> variantParamObj = (Map<String, Object>) variant;

                Map<String, Object> variantParam = (Map<String, Object>) variantParamObj.get("Variant");

                String sku = MapUtils.getString(variantParam, "sku");

                // 当条目数量只有1个，并且sku与父sku 相同时，判定不是多属性
                if (StringUtils.equals(generalItem.getParentSkuOnline(), sku) && variantList.size() == 1) {
                    isMultiAttr = false;
                    generalItem.setIsMultiAttr(false);
                    generalItem.setIsFirstItem(true);

                    // String product_id =
                    // JsonReponseParam.getString(variantParam, "product_id");

                    // 颜色
                    String color = MapUtils.getString(variantParam, "color");

                    String size = MapUtils.getString(variantParam, "size");

                    generalItem.setMultiAttr("color :" + (StringUtils.isEmpty(color) ? "" : color) + "， size:"
                            + (StringUtils.isEmpty(size) ? "" : size));

                    // 是否启用
                    Boolean enabled = MapUtils.getBoolean(variantParam, "enabled");
                    generalItem.setIsEnabled(enabled);

                    // 只要有一个开启，就说明是上架 产品
                    if (enabled) {
                        isOnline = true;
                    }

                    // 唯一id
                    String id = MapUtils.getString(variantParam, "id");
                    generalItem.setChildId(id);

                    // sku 修改item 使用 （但多属性的父属性 这个值为null） 这里用原始数据不能改变
                    generalItem.setSku(sku);

                    // 库存
                    Integer inventory = MapUtils.getInteger(variantParam, "inventory");
                    generalItem.setInventory(inventory);

                    // 价格
                    Double msrp = MapUtils.getDouble(variantParam, "msrp");
                    generalItem.setMsrp(msrp);

                    // 实际出售价格
                    Double price = MapUtils.getDouble(variantParam, "price");
                    generalItem.setPrice(price);

                    Double shippingHeight = MapUtils.getDouble(variantParam, "shipping_height");
                    Double shippingLength = MapUtils.getDouble(variantParam, "shipping_length");
                    Double shippingWidth = MapUtils.getDouble(variantParam, "shipping_width");
                    if(shippingHeight == null || shippingLength  == null || shippingWidth == null){
                        measurementNull = true;
                    }

                    generalItem.setShippingHeight(shippingHeight);
                    generalItem.setShippingLength(shippingLength);
                    generalItem.setShippingWidth(shippingWidth);

                    // 费用
                    Double shippingCost = MapUtils.getDouble(variantParam, "shipping");
                    generalItem.setShippingCost(shippingCost);

                    // 运输时间
                    String shipping_time = MapUtils.getString(variantParam, "shipping_time");
                    generalItem.setShippingTime(shipping_time);

                    // 敏感货类别
                    // generalItem.setDangerousKind(dangerousKind);

                }
                // else if(StringUtils.equals(generalItem.getParentSku(), sku)
                // && variantList.size() > 1)
                // {
                // // 子属性跟父属性SKU一样，不保存
                // continue;
                // }
                // 多属性
                else {
                    generalItem.setIsMultiAttr(isMultiAttr);

                    JoomItem variationJoomItem = buildItem(joomPmsAccount, productParam);

                    // 设置系统价格
                    // variationWishItem.setSystemPrice(ProductFeeUtils.getProductFee(sku,
                    // FeeCategories.WISH_CPAM, null, "USD"));

                    variationJoomItem.setIsMultiAttr(isMultiAttr);

                    // 是否为多属性的子属性
                    variationJoomItem.setIsVariation(true);

                    // String product_id = MapUtil.getString(variantParam,
                    // "product_id");

                    String color = MapUtils.getString(variantParam, "color");

                    String size = MapUtils.getString(variantParam, "size");

                    variationJoomItem.setMultiAttr("color:" + (StringUtils.isEmpty(color) ? "" : color) + ",size:"
                            + (StringUtils.isEmpty(size) ? "" : size));

                    Boolean enabled = MapUtils.getBoolean(variantParam, "enabled");
                    variationJoomItem.setIsEnabled(enabled);

                    // 只要有一个开启，就说明是上架 产品
                    if (enabled) {
                        isOnline = true;
                    }

                    // 设置是否上架
                    variationJoomItem.setIsOnline(enabled);

                    String id = MapUtils.getString(variantParam, "id");
                    variationJoomItem.setChildId(id);

                    String articleNumber = transformSkuAlias(sku);
                    variationJoomItem.setSku(sku);

                    // 先查询绑定sku
                    EsSkuBind variationSkuBind = skuBindService.getEsSkuBind(articleNumber, SaleChannel.CHANNEL_JOOM);
                    if (variationSkuBind != null) {
                        variationJoomItem.setArticleNumber(variationSkuBind.getSku().trim());

                    }
                    else {
                        // 与产品系统对应的sku
                        String variationArticleNumber = articleNumber;

                        if (StringUtils.isNotBlank(joomPmsAccount.getSellerSkuPrefix())
                                && StringUtils.startsWith(articleNumber, joomPmsAccount.getSellerSkuPrefix())) {
                            variationArticleNumber = StringUtils.substringAfter(articleNumber,
                                    joomPmsAccount.getSellerSkuPrefix());
                        }

                        // 对应系统的sku
                        variationJoomItem.setArticleNumber(variationArticleNumber);
                    }

                    //
                    variationJoomItem.setParentSku(generalItem.getParentSku());

                    // 库存
                    Integer inventory = MapUtils.getInteger(variantParam, "inventory");
                    variationJoomItem.setInventory(inventory);

                    // 价格
                    Double msrp = MapUtils.getDouble(variantParam, "msrp");
                    variationJoomItem.setMsrp(msrp);

                    // 实际出售价格
                    Double price = MapUtils.getDouble(variantParam, "price");
                    variationJoomItem.setPrice(price);

                    Double shippingHeight = MapUtils.getDouble(variantParam, "shipping_height");
                    Double shippingLength = MapUtils.getDouble(variantParam, "shipping_length");
                    Double shippingWidth = MapUtils.getDouble(variantParam, "shipping_width");
                    if(shippingHeight == null || shippingLength  == null || shippingWidth == null){
                        measurementNull = true;
                    }

                    variationJoomItem.setShippingHeight(shippingHeight);
                    variationJoomItem.setShippingLength(shippingLength);
                    variationJoomItem.setShippingWidth(shippingWidth);

                    // 费用
                    Double shippingCost = MapUtils.getDouble(variantParam, "shipping");
                    variationJoomItem.setShippingCost(shippingCost);

                    // 运输时间
                    String shipping_time = MapUtils.getString(variantParam, "shipping_time");
                    variationJoomItem.setShippingTime(shipping_time);

                    // 敏感货类别
                    // variationJoomItem.setDangerousKind(dangerousKind);

                    // 多属性设置特性标签
                    variationJoomItem.setTags(generalItem.getTags());

                    // 将多属性的库存和卖出数量 加入到父SKU 中，为了页面排序（只针对启用产品）
                    if (variationJoomItem.getIsEnabled() != null && variationJoomItem.getIsEnabled()) {
                        generalItem
                                .setNumberSold((generalItem.getNumberSold() == null ? 0 : generalItem.getNumberSold())
                                        + variationJoomItem.getNumberSold());
                        generalItem.setInventory((generalItem.getInventory() == null ? 0 : generalItem.getInventory())
                                + variationJoomItem.getInventory());
                    }

                    if (i == 0) {
                        variationJoomItem.setIsFirstItem(true);
                    }
                    else {
                        variationJoomItem.setIsFirstItem(false);
                    }
                    assembleProductInfo(variationJoomItem);
                    i++;

                    createProducts.add(variationJoomItem);
                }

            }

            // 是否上架
            generalItem.setIsOnline(isOnline);
        }

        // joom item 通过childId 来确定是否唯一 多属性的父SKU 这个值为null 就用joom_item_id 替代
        if (StringUtils.isEmpty(generalItem.getChildId())) {
            generalItem.setChildId(generalItem.getJoomItemId());
        }

        //设置尺寸是否为空
        for (JoomItem createProduct : createProducts) {
            createProduct.setMeasurementNull(measurementNull);
        }

        return createProducts;

    }

    private static JoomItem buildItem(SaleAccountAndBusinessResponse joomPmsAccount, Map<String, Object> productParam) {

        JoomItem joomItem = new JoomItem();

        String joomItemId = MapUtils.getString(productParam, "id");
        joomItem.setJoomItemId(joomItemId);

        // 卖家
        joomItem.setItemSeller(joomPmsAccount.getAccountNumber());

        // 是否促销
        Boolean is_promoted = MapUtils.getBoolean(productParam, "is_promoted");
        joomItem.setIsPromoted(is_promoted);

        // 主图
        String main_image = MapUtils.getString(productParam, "main_image");
        joomItem.setMainImage(main_image);

        // 说明
        String description = MapUtils.getString(productParam, "description");
        joomItem.setDescription(description);

        // 扩展图 由 '|' 拼接
        String extra_images = MapUtils.getString(productParam, "extra_images");
        joomItem.setExtraImages(extra_images);

        // 标题
        String title = MapUtils.getString(productParam, "name");
        joomItem.setItemTitle(StringUtils.isNotBlank(title) ? title.trim() : title);

        String parentId = MapUtils.getString(productParam, "id");
        joomItem.setParentId(parentId);

        // 收藏数量
        Integer number_saves = MapUtils.getInteger(productParam, "number_saves");
        joomItem.setNumberSaves(number_saves);

        // 卖出数量
        Integer number_sold = MapUtils.getInteger(productParam, "number_sold");
        joomItem.setNumberSold(number_sold);

        // 父sku
        String parent_sku = MapUtils.getString(productParam, "parent_sku");
        parent_sku = transformSkuAlias(parent_sku);
        joomItem.setParentSku(parent_sku);
        joomItem.setParentSkuOnline(parent_sku); // 后台父sku，线上父sku

        // 敏感货分类
        String dangerousKind = MapUtils.getString(productParam, "dangerous_kind");
        joomItem.setDangerousKind(dangerousKind);

        // 状态
        String review_status = MapUtils.getString(productParam, "review_status");
        joomItem.setReviewStatus(review_status);

        // 平台状态
        String state = MapUtils.getString(productParam, "state");
        joomItem.setState(state);

        // 产品 上传时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        Date date_uploaded_date = MapUtils.getTimestamp(productParam, "date_uploaded", sdf);
        joomItem.setItemUploadedDate(date_uploaded_date);

        // 没有卖出的商品 设置 最后售出时间为 产品上传时间
        if (number_sold == null || number_sold == 0) {
            joomItem.setLastSoldDate(date_uploaded_date);
        }

        return joomItem;

    }

    public static void assembleProductInfo(JoomItem sourceItem) {
        if (StringUtils.isBlank(sourceItem.getArticleNumber())) {
            return;
        }
        // 产品系统信息补充
//        ProductInfoVO productInfoVO = PRODUCT_INFO_CACHE.getIfPresent(sourceItem.getArticleNumber());
        ProductInfoVO skuInfo = ProductUtils.getSkuInfo(sourceItem.getArticleNumber());
        setProductInfo(sourceItem, skuInfo);
    }

    /**
     * 设置产品系统信息
     * @param sourceItem
     * @param productInfoVO
     */
    public static void setProductInfo(JoomItem sourceItem, ProductInfoVO productInfoVO) {
        String articleNumber = sourceItem.getArticleNumber();
        try{
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(articleNumber);
            }

            if (null != productInfoVO  && StringUtils.isNotBlank(productInfoVO.getSonSku())) {
                String skuStatus = productInfoVO.getSkuStatus();
                if(StringUtils.equalsIgnoreCase(skuStatus, SkuStatusEnum.DISCARD.getCode())){
                    String mergeSku = ProductUtils.getMergeSku(articleNumber);
                    //不一样就从新查询产品信息
                    if(!StringUtils.equalsIgnoreCase(articleNumber, mergeSku)){
                        //重新查询产品信息
                        productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                    }
                    articleNumber = mergeSku;
                }
                //重设货号
                sourceItem.setArticleNumber(articleNumber);
                // 产品系统类目
                sourceItem.setCategoryId(productInfoVO.getCatId());
                sourceItem.setCategoryIdPath(productInfoVO.getCategoryId());
                sourceItem.setCategoryCnName(productInfoVO.getCategoryCnName());
                // 特殊标签
                sourceItem.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
                // 单品状态
                sourceItem.setSkuStatus(productInfoVO.getSkuStatus());
                // 产品标签
                sourceItem.setSkuTagCode(productInfoVO.getTagCodes());
                // 禁售平台
                sourceItem.setForbidChannel(productInfoVO.getForbidChannel());
                // 禁售原因
                sourceItem.setInfringementObj(productInfoVO.getInfringementObj());
                // 禁售类型
                sourceItem.setInfringementTypeName(productInfoVO.getInfringementTypeName());
                // 禁售站点
                List<String> prohibitionSites = productInfoVO.getProhibitionSiteWithPlatformDefaultSite();
                if (CollectionUtils.isNotEmpty(prohibitionSites)) {
                    String prohibitionSiteStr = String.join(",", prohibitionSites);
                    sourceItem.setProhibitionSites("," + prohibitionSiteStr + ",");
                }
                // 是否促销
                sourceItem.setPromotion(productInfoVO.getPromotion());
                // 是否新品
                sourceItem.setNewState(productInfoVO.getNewState());
                sourceItem.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            }else{
                // 匹配套装
                matchComposeProduct(sourceItem);
            }

        }catch (Exception e){
            log.error(String.format("同步listing数据获取产品SKu信息: %s, ", articleNumber), e);
        }
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param item  listing
     */
    public static Boolean matchComposeProduct(JoomItem item) {
        String articleNumber = item.getArticleNumber();
        log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",item.getItemSeller(), articleNumber);
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(item, composeProduct);
            return true;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (org.apache.commons.collections4.MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {

            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return false;
            }

            item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            item.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
//            item.setComposeStatus(ComposeCheckStepEnum.NORMAL.getCode());

            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            item.setComposeStatus(stepEnum.getCode());

            // 禁售平台
            item.setForbidChannel(StringUtils.join(suiteSku.getForbidChannels(),","));
            // 禁售类型
            item.setInfringementTypeName(StringUtils.join(suiteSku.getInfringementTypeNames(), ","));
            // 禁售原因
            item.setInfringementObj(StringUtils.join(suiteSku.getInfringementObjs(), ","));
            // 禁售站点
            item.setProhibitionSites(StringUtils.join(suiteSku.getProhibitionPlatSites(),","));
            return true;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(item, composeProductRef);
            return true;
        }
        return false;
    }

    private static void setProductInfoByCompose(JoomItem item, ComposeSku composeProduct) {
        item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        item.setArticleNumber(composeProduct.getComposeSku());
        item.setComposeStatus(composeProduct.getCheckStep());

        String categoryName =StringUtils.isNotBlank(composeProduct.getCategoryName()) ? composeProduct.getCategoryName().replaceAll(">", ",") : null;
        item.setCategoryCnName(categoryName);
        // 禁售平台
        item.setForbidChannel(StringUtils.join(composeProduct.getForbidChannels(),","));
        // 禁售类型
        item.setInfringementTypeName(StringUtils.join(composeProduct.getInfringementTypeNames(), ","));
        // 禁售原因
        item.setInfringementObj(StringUtils.join(composeProduct.getInfringementObjs(), ","));
        // 禁售站点
        item.setProhibitionSites(StringUtils.join(composeProduct.getProhibitionPlatSites(),","));
        // 单品状态
        item.setSkuStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 产品标签code
        item.setSkuTagCode(composeProduct.getTagCode());
        // 特殊标签
        item.setSpecialGoodsCode(null);
        // 是否促销
        item.setPromotion(0);
        // 是否新品
        item.setNewState(false);
    }


    /**
     * 匹配解析平台自动修改的sku名称（店铺id_sku）&& (sku_itemId_archived)
     * 如:1484623586932191161-88-3-629-3804217419_5A61247
     * 如:5AC701543_5d10318528fc7104013daede_archived
     * @param skuName
     * @return
     */
    private static String transformSkuAlias(String skuName) {
        if(StringUtils.isNotBlank(skuName) && skuName.length() > 30) {
            if(skuName.contains("_")) {
                String pattern = "\\d{1,}-\\d{1,}-\\d{1,}-\\d{1,}-\\d{1,}";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(skuName.split("_")[0]);
                if(m.matches()) {
                    return skuName.split("_")[1];
                }else {
                    if(skuName.contains("_archived")) {
                        return skuName.split("_")[0];
                    }
                }
            }
        }
        return skuName;
    }
}
