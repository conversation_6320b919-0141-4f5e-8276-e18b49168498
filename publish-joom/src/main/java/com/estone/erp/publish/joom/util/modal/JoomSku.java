package com.estone.erp.publish.joom.util.modal;

import com.estone.erp.common.util.StrUtil;
import lombok.Data;

@Data
public class JoomSku {

    /**
     * 颜色
     */
    private String color;

    /**
     * 尺寸大小
     */
    private String size;

    /**
     * 货号sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private Double price;

    /**
     * 毛利率 默认0.26
     */
    private Double profitMargin = 0.26;

    /**
     * 运费
     */
    private Double shipping;
    
    /**
     * 运费毛利
     */
    private Double shippingProfitMargin;

    /**
     * 零售价
     */
    private Double msrp;

    /**
     * 运输时间
     */
    private String shippingTime;

    /**
     * 在线
     */
    private Boolean enabled = false;

    /**
     * 主图
     */
    private String mainImage;

    /**
     * 属性 仅供查看
     */
    private String attribute;
    
    
    /**
     * 产品重量 kg
     */
    private Double shippingWeight;
    
    /**
     * sku最新状态
     */
    private String productStatus;

    /**
     * 宽
     */
    private Double shippingWidth;
    /**
     * 长
     */
    private Double shippingLength;
    /**
     * 高
     */
    private Double shippingHeight;
    
    
    public String getProductStatus() {
		return productStatus;
	}

	public void setProductStatus(String productStatus) {
		this.productStatus = productStatus;
	}

	public Double getShippingProfitMargin() {
		return shippingProfitMargin;
	}

	public void setShippingProfitMargin(Double shippingProfitMargin) {
		this.shippingProfitMargin = shippingProfitMargin;
	}

	public Double getShippingWeight() {
        return shippingWeight;
    }

    public void setShippingWeight(Double shippingWeight) {
        this.shippingWeight = shippingWeight;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSku() {
        return StrUtil.strTrimToUpperCase(sku);
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getShipping() {
        return shipping;
    }

    public void setShipping(Double shipping) {
        this.shipping = shipping;
    }

    public Double getMsrp() {
        return msrp;
    }

    public void setMsrp(Double msrp) {
        this.msrp = msrp;
    }

    public String getShippingTime() {
        return shippingTime;
    }

    public void setShippingTime(String shippingTime) {
        this.shippingTime = shippingTime;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public Double getProfitMargin() {
        return profitMargin;
    }

    public void setProfitMargin(Double profitMargin) {
        this.profitMargin = profitMargin;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }
}
