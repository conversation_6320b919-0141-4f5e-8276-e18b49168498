package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomWarehouse;
import com.estone.erp.publish.joom.model.JoomWarehouseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JoomWarehouseMapper {
    int countByExample(JoomWarehouseExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(JoomWarehouse record);

    JoomWarehouse selectByPrimaryKey(Integer id);

    List<JoomWarehouse> selectByExample(JoomWarehouseExample example);

    int updateByExampleSelective(@Param("record") JoomWarehouse record, @Param("example") JoomWarehouseExample example);

    int updateByPrimaryKeySelective(JoomWarehouse record);
}