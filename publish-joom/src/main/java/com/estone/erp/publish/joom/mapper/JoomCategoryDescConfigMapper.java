package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomCategoryDescConfig;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JoomCategoryDescConfigMapper {
    int countByExample(JoomCategoryDescConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(JoomCategoryDescConfig record);

    JoomCategoryDescConfig selectByPrimaryKey(Integer id);

    JoomCategoryDescConfig selectByFullCategoryCode(@Param("fullCategoryCode") String fullCategoryCode);

    List<JoomCategoryDescConfig> selectByExample(JoomCategoryDescConfigExample example);

    int updateByExampleSelective(@Param("record") JoomCategoryDescConfig record, @Param("example") JoomCategoryDescConfigExample example);

    int updateByPrimaryKeySelective(JoomCategoryDescConfig record);
}