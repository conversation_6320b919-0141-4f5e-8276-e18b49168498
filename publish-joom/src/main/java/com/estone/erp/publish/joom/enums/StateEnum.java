package com.estone.erp.publish.joom.enums;

public enum StateEnum {

    active("可供购买"),

    disabledByJoom("被Joom下架"),

    disabledByMerchant("被卖家禁用"),

    pending("Joom处理中"),

    rejected("被Joom拒绝"),

    warning("可供购买，但需注意"),

    archived("archived"),

    locked("更改推迟")
    ;

    private String name;

    private StateEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
