package com.estone.erp.publish.joom.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.HostMapper;
import com.estone.erp.publish.joom.model.Host;
import com.estone.erp.publish.joom.model.HostCriteria;
import com.estone.erp.publish.joom.model.HostExample;
import com.estone.erp.publish.joom.service.HostService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> t_host
 * 2019-08-12 18:15:59
 */
@Service("hostService")
@Slf4j
public class HostServiceImpl implements HostService {
    @Resource
    private HostMapper hostMapper;

    @Override
    public int countByExample(HostExample example) {
        Assert.notNull(example, "example is null!");
        return hostMapper.countByExample(example);
    }

    @Override
    public CQueryResult<Host> search(CQuery<HostCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        HostCriteria query = cquery.getSearch();
        HostExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = hostMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<Host> hosts = hostMapper.selectByExample(example);
        // 组装结果
        CQueryResult<Host> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(hosts);
        return result;
    }

    @Override
    public Host selectByPrimaryKey(Integer hostId) {
        Assert.notNull(hostId, "hostId is null!");
        return hostMapper.selectByPrimaryKey(hostId);
    }

    @Override
    public List<Host> selectByExample(HostExample example) {
        Assert.notNull(example, "example is null!");
        return hostMapper.selectByExample(example);
    }

    @Override
    public int insert(Host record) {
        Assert.notNull(record, "record is null!");
        return hostMapper.insert(record);
    }

    @Override
    public int insertSelective(Host record) {
        Assert.notNull(record, "record is null!");
        return hostMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(Host record) {
        Assert.notNull(record, "record is null!");
        return hostMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(Host record, HostExample example) {
        Assert.notNull(record, "record is null!");
        return hostMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(Integer hostId) {
        Assert.notNull(hostId, "hostId is null!");
        return hostMapper.deleteByPrimaryKey(hostId);
    }

    @Override
    public int deleteByExample(HostExample example) {
        Assert.notNull(example, "example is null!");
        return hostMapper.deleteByExample(example);
    }

    @Override
    public List<String> selectHostSiteList() {
        return hostMapper.selectHostSiteList();
    }

    @Override
    public void batchUpdate(List<Host> hostList) {
        if(CollectionUtils.isNotEmpty(hostList)) {
            for (Host host : hostList) {
                hostMapper.updateByPrimaryKey(host);
            }
        }
    }

    @Override
    public void batchUpdateSite(List<Host> hostList, String hostSite) {
        if(CollectionUtils.isNotEmpty(hostList)) {
            for (Host host : hostList) {
                host.setHostSite(hostSite);
                hostMapper.updateByPrimaryKey(host);
            }
        }
    }
}