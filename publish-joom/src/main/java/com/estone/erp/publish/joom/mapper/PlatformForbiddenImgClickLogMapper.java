package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.PlatformForbiddenImgClickLog;
import com.estone.erp.publish.joom.model.PlatformForbiddenImgClickLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PlatformForbiddenImgClickLogMapper {
    int countByExample(PlatformForbiddenImgClickLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(PlatformForbiddenImgClickLog record);

    PlatformForbiddenImgClickLog selectByPrimaryKey(Long id);

    List<PlatformForbiddenImgClickLog> selectByExample(PlatformForbiddenImgClickLogExample example);

    int updateByExampleSelective(@Param("record") PlatformForbiddenImgClickLog record, @Param("example") PlatformForbiddenImgClickLogExample example);

    int updateByPrimaryKeySelective(PlatformForbiddenImgClickLog record);
}