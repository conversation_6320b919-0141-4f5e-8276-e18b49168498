package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomOperateLog;
import com.estone.erp.publish.joom.model.JoomOperateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JoomOperateLogMapper {
    int countByExample(JoomOperateLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(JoomOperateLog record);

    JoomOperateLog selectByPrimaryKey(Integer id);

    List<JoomOperateLog> selectByExample(JoomOperateLogExample example);

    int updateByExampleSelective(@Param("record") JoomOperateLog record, @Param("example") JoomOperateLogExample example);

    int updateByPrimaryKeySelective(JoomOperateLog record);
}