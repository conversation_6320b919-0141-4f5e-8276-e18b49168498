package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.joom.common.JoomProductCommon;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomItemExample;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 组合套装产品更新
 * <AUTHOR>
 * @date 2023年9月4日14:34:23
 */
@Slf4j
@Component
public class JoomSyncComposeReceiver {

    @Autowired
    private JoomItemService joomItemService;

    @RabbitListener(queues = PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE, containerFactory = "commonFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
//        log.info("queue[{}]: {}", "COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE", body);
        try {
            String spu = JSON.parseObject(body, String.class);
            syncComposeProductInfo(spu);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("处理队列[{}]消息[{}]失败：{}", "COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE", body, e.getMessage(), e);
        }
    }

    private void syncComposeProductInfo(String spu) {
        if (StringUtils.isBlank(spu)) {
            return;
        }

        JoomItemExample itemExample = new JoomItemExample();
        JoomItemExample.Criteria criteria = itemExample.createCriteria();
        criteria.andArticleNumberEqualTo(spu).andSkuDataSourceEqualTo(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        List<JoomItem> joomItems = joomItemService.selectByExample(itemExample);
        if(CollectionUtils.isEmpty(joomItems)){
            return;
        }

        for (JoomItem joomItem : joomItems) {
            try{
                Boolean match = JoomProductCommon.matchComposeProduct(joomItem);
                if (!match) {
                    continue;
                }
                joomItemService.updateByPrimaryKeySelective(joomItem);
            }catch (Exception e){
                log.error("{}更新失败：{}",spu,e.getMessage(),e);
            }
        }
    }

}
