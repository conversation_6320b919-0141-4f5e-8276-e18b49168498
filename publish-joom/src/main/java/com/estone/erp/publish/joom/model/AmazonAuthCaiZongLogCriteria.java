package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-04-07 09:37:29
 */
public class AmazonAuthCaiZongLogCriteria extends AmazonAuthCaiZongLog {
    private static final long serialVersionUID = 1L;

    public AmazonAuthCaiZongLogExample getExample() {
        AmazonAuthCaiZongLogExample example = new AmazonAuthCaiZongLogExample();
        AmazonAuthCaiZongLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAppName())) {
            criteria.andAppNameEqualTo(this.getAppName());
        }
        if (StringUtils.isNotBlank(this.getSellingPartnerId())) {
            criteria.andSellingPartnerIdEqualTo(this.getSellingPartnerId());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        return example;
    }
}