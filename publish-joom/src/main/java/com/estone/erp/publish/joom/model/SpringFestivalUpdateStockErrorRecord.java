package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SpringFestivalUpdateStockErrorRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 平台
     */
    private String platform;

    /**
     * 店铺
     */
    private String account;

    /**
     * 在线列表id
     */
    private String itemId;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Timestamp createTime;
}