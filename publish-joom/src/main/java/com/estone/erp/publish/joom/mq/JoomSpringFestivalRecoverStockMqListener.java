package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.BO.JoomUpdateStockMqMsg;
import com.estone.erp.publish.joom.call.JoomUpdateInventoryCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecords;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.joom.service.JoomUpdateStockZeroRecordsService;
import com.estone.erp.publish.joom.service.SpringFestivalUpdateStockErrorRecordService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.JoomTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;

@Slf4j
@Component
public class JoomSpringFestivalRecoverStockMqListener implements ChannelAwareMessageListener {

    @Resource
    private JoomItemService joomItemService;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private JoomUpdateStockZeroRecordsService joomUpdateStockZeroRecordsService;

    @Resource
    private SpringFestivalUpdateStockErrorRecordService springFestivalUpdateStockErrorRecordService;
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.debug("queue[{}]: {}", "JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE", body);
        JoomUpdateStockMqMsg joomUpdateStockMqMsg = null;
        JoomItem joomItem = null;
        try {
            joomUpdateStockMqMsg = JSON.parseObject(body, JoomUpdateStockMqMsg.class);
            if (ObjectUtils.isEmpty(joomUpdateStockMqMsg)){
                log.debug("消息为空");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }

            joomItem = joomItemService.selectByPrimaryKey(joomUpdateStockMqMsg.getItemId());
            if (ObjectUtils.isEmpty(joomItem) || joomUpdateStockMqMsg.getInventory().equals(joomItem.getInventory())){
                log.debug("未获取到在线列表数据或者修改的库存相同");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            this.dealMsg(joomUpdateStockMqMsg, joomItem);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("JoomSpringFestivalUpdateStockMqListener 消费异常, boyd: {}, message:{}", body, e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (Exception ex) {
                log.error("JoomSpringFestivalUpdateStockMqListener 异常消费确认失败, boyd: {}, message:{}", body, ex.getMessage(), ex);
            }
        }
    }



    private void dealMsg(JoomUpdateStockMqMsg joomUpdateStockMqMsg, JoomItem joomItem) {
        //记录操作报告
        FeedTask feedTask = this.saveFeedTask(joomUpdateStockMqMsg, joomItem);
        //记录春节记录
        JoomUpdateStockZeroRecords joomUpdateStockZeroRecords = this.saveRecord(joomUpdateStockMqMsg, joomItem);
        // 修改库存
        SaleAccountAndBusinessResponse joomAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.JOOM.getChannelName(), joomItem.getItemSeller());
        JoomUpdateInventoryCall call = new JoomUpdateInventoryCall(joomAccount);
        joomItem.setInventory(joomUpdateStockMqMsg.getInventory());
        ResponseJson rsp = call.updateInventory(joomItem);
        //回写修改状态
        if(StatusCode.SUCCESS.equals(rsp.getStatus())){
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setResultStatus( ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
            feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            feedTaskService.updateByPrimaryKeySelective(feedTask);

            joomUpdateStockZeroRecords.setResultStauts(1);
            joomUpdateStockZeroRecords.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            joomUpdateStockZeroRecordsService.updateByPrimaryKeySelective(joomUpdateStockZeroRecords);

            //修改在线列表数据
            joomItem.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            joomItem.setLastUpdatedBy(joomUpdateStockMqMsg.getUsername());
            joomItemService.updateByPrimaryKeySelective(joomItem);
        }else {
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setResultStatus( ResultStatusEnum.RESULT_FAIL.getStatusCode());
            feedTask.setResultMsg(rsp.getMessage());
            feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            feedTaskService.updateByPrimaryKeySelective(feedTask);

            joomUpdateStockZeroRecords.setResultStauts(0);
            joomUpdateStockZeroRecords.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            joomUpdateStockZeroRecords.setResultMsg(rsp.getMessage());
            joomUpdateStockZeroRecordsService.updateByPrimaryKeySelective(joomUpdateStockZeroRecords);
        }
    }

    private JoomUpdateStockZeroRecords saveRecord(JoomUpdateStockMqMsg joomUpdateStockMqMsg, JoomItem joomItem) {
        JoomUpdateStockZeroRecords joomUpdateStockZeroRecords = new JoomUpdateStockZeroRecords();
        joomUpdateStockZeroRecords.setAccount(joomItem.getItemSeller());
        joomUpdateStockZeroRecords.setItemId(joomItem.getItemId().toString());
        joomUpdateStockZeroRecords.setArticleNumber(joomItem.getArticleNumber());
        joomUpdateStockZeroRecords.setSkuStatus(joomUpdateStockMqMsg.getStatus());
        joomUpdateStockZeroRecords.setBeforeValue(joomItem.getInventory()+"");
        joomUpdateStockZeroRecords.setAfterValue(joomUpdateStockMqMsg.getInventory()+"");
        joomUpdateStockZeroRecords.setCreateTime(new Timestamp(System.currentTimeMillis()));
        joomUpdateStockZeroRecords.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        joomUpdateStockZeroRecords.setExecStatus(1);
        joomUpdateStockZeroRecords.setJoomItemId(joomItem.getJoomItemId());
        joomUpdateStockZeroRecordsService.insert(joomUpdateStockZeroRecords);
        return joomUpdateStockZeroRecords;

    }

    private FeedTask saveFeedTask(JoomUpdateStockMqMsg joomUpdateStockMqMsg, JoomItem joomItem) {
        FeedTask feedTask = new FeedTask();
        feedTask.setPlatform(Platform.Joom.name());
        feedTask.setTableIndex();
        feedTask.setAssociationId(joomItem.getJoomItemId());
        feedTask.setAccountNumber(joomItem.getItemSeller());
        feedTask.setArticleNumber(joomItem.getArticleNumber());
        feedTask.setTaskType(JoomTaskTypeEnum.UPDATE_STOCK.getStatusMsgEn());
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setCreatedBy(joomUpdateStockMqMsg.getUsername());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        //库存前
        feedTask.setAttribute3(joomItem.getInventory().toString());
        //库存后
        feedTask.setAttribute4(joomUpdateStockMqMsg.getInventory().toString());
        feedTaskService.insert(feedTask);
        return feedTask;
    }
}
