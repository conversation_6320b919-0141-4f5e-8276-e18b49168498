package com.estone.erp.publish.joom.call;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JoomUpdateInventoryCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "variant/update-inventory";

    public JoomUpdateInventoryCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    @SuppressWarnings("unchecked")
    public ResponseJson updateInventory(JoomItem joomItem) {
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);

        httpClient = HttpClientUtils.createSSLClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();

        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        standardNameValue.add(new BasicNameValuePair("sku", joomItem.getSku()));

        standardNameValue.add(new BasicNameValuePair("inventory", String.valueOf(joomItem.getInventory())));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));

            HttpPost httpPost = new HttpPost();

            try {
                httpPost.setURI(new URI(url.toString()));
            }
            catch (URISyntaxException e) {
            }

            Map<String, Object> productDetails = null;

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpPost);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();

                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        productDetails = JSON.parseObject(responseJson, Map.class);
                    }
                    else {
                        log.error("JoomUpdateItemCall error. Account " + joomPmsAccount.getAccountNumber() + " Message "
                                + responseJson);
                        rsp.setMessage(responseJson);
                    }

                    break;
                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomRetrieveProductCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    rsp.setMessage(e.getMessage());
                    return rsp;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            if (productDetails == null || productDetails.isEmpty()) {
                if (StringUtils.isEmpty(rsp.getMessage())) {
                    rsp.setMessage("服务器没有响应，请重试");
                }
                return rsp;
            }

            int code = (int) productDetails.get("code");

            // If the request was successful the API will return an HTTP status
            // code of 200 and a status code of 0.
            if (code == 0) {
                // 修改成功
                rsp.setStatus(StatusCode.SUCCESS);
                rsp.setMessage("修改成功");
            }
            else {
                String message = (String) productDetails.get("message");
                rsp.setMessage(code + message);
            }

            return rsp;
        }
        catch (Exception e) {
            log.error("updateInventory Exception" + e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }
}
