package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.joom.common.JoomProductCommon;
import com.estone.erp.publish.joom.mapper.JoomItemMapper;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * joom 同步产品变更消息消费者
 *
 * <AUTHOR>
 * @date 2022-09-01 10:45
 */
@Slf4j
@Component
public class JoomSyncProductConsumer {

    @Resource
    private ChangeSkuLogService changeSkuLogService;
    @Resource
    private JoomItemMapper joomItemMapper;

    private static final int MAX_RETRY_COUNT = 3;

    @RabbitListener(queues = PublishQueues.JOOM_SYNC_PRODUCT_INFO_QUEUE, containerFactory = "batchConsumeFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }
//            log.info("on message:{}", body);
            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_JOOM);
            }
        } catch (Exception e) {
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_JOOM);
            log.error("mkd MKD_SYNC_PRODUCT_INFO_QUEUE Exception error: {}", e.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        syncProductInfo(skuList);
        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }

    private void syncProductInfo(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }
        Map<String, ProductInfoVO> map = new HashMap<>(skuList.size());
        List<JoomItem> updateItemList = new ArrayList<>();
        List<JoomItem> items = joomItemMapper.selectItemIdBySku(skuList);
        items.forEach(item -> {
            updateProductInfo(item, map, updateItemList);
        });

        if (CollectionUtils.isEmpty(updateItemList)) {
            return;
        }

        List<List<JoomItem>> partition = Lists.partition(updateItemList, 200);
        for (List<JoomItem> partList : partition) {
            joomItemMapper.batchUpdateProductJoomItem(partList);
        }

    }

    private void updateProductInfo(JoomItem item, Map<String, ProductInfoVO> productMap, List<JoomItem> updateItemList) {
        try {
            if (StringUtils.isBlank(item.getArticleNumber())) {
                return;
            }
            String skuNumber = item.getArticleNumber();
            // 查询产品信息
            ProductInfoVO productInfoVO = productMap.get(skuNumber);
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(skuNumber);
                productMap.put(skuNumber, productInfoVO);
            }
            // 修改产品信息
            JoomProductCommon.setProductInfo(item, productInfoVO);
            item.setLastUpdateDate(new Date());
            updateItemList.add(item);
        } catch (Exception e) {
            log.error("更新产品信息失败：{}", e.getMessage(), e);
        }
    }
}
