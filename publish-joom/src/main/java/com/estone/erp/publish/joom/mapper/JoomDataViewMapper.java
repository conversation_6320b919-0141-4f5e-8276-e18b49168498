package com.estone.erp.publish.joom.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.estone.erp.publish.joom.model.JoomDataView;
import com.estone.erp.publish.joom.model.JoomDataViewCriteria;
import com.estone.erp.publish.joom.model.JoomDataViewExample;

public interface JoomDataViewMapper {
    int countByExample(JoomDataViewExample example);

    int countTotalTemplate(@Param("search") JoomDataViewCriteria query);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(JoomDataView record);

    JoomDataView selectByPrimaryKey(Integer id);

    List<JoomDataView> selectByExample(JoomDataViewExample example);

    List<JoomDataView> selectTotalTemplate(@Param("search") JoomDataViewCriteria query);

    int updateByExampleSelective(@Param("record") JoomDataView record, @Param("example") JoomDataViewExample example);

    int updateByPrimaryKeySelective(JoomDataView record);

    void batchInsert(@Param("recordList") List<JoomDataView> recordList);

    /*
     * 统计上月上架listing统计数据
     */
    List<Map<String, Object>> countUnStandardData();

    /*
     * 统计昨日刊登成功模板数
     */
    List<Map<String, Object>> countSuccessTemplate();

    /*
     * 统计新增模板
     */
    List<JoomDataView> countTemplate();

    /*
     * 统计新增listing
     */
    List<JoomDataView> countListing();

    /*
     * 统计listing总数
     */
    List<JoomDataView> selectTotalListing(@Param("search") JoomDataViewCriteria query);

    /*
     * 统计listing总数
     */
    int countTotalListing(@Param("search") JoomDataViewCriteria query);

    List<JoomDataView> getSuccessListing();
}