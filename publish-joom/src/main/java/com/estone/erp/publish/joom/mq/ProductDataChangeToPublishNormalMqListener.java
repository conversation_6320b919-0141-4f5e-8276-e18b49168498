package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.platform.model.ProductDataChangeToPublish;
import com.estone.erp.publish.platform.service.ProductDataChangeToPublishService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/8/12 10:16
 * @description
 */
@Slf4j
public class ProductDataChangeToPublishNormalMqListener implements ChannelAwareMessageListener {

   @Resource
   private ProductDataChangeToPublishService productDataChangeToPublishService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");

        //log.info("ProductDataChangeToPublishNormalMqListener message body -> {}", body);

        Boolean isSuccess = doService(body);

        if(isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private boolean doService(String body) {
        boolean isSuccess = false;
        if (StringUtils.isBlank(body)) {
            return isSuccess;
        }

        ProductDataChangeToPublish bean;
        try {
            bean = JSON.parseObject(body,ProductDataChangeToPublish.class);
            if (null == bean || StringUtils.isEmpty(bean.getMainSku())) {
                return isSuccess;
            }
            if(StringUtils.isBlank(bean.getSonSku())){
                bean.setSonSku(bean.getMainSku());
            }
        }catch(Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
            return isSuccess;
        }

        bean.setCreateDate(new Timestamp(System.currentTimeMillis()));
        try {
            int count = productDataChangeToPublishService.insert(bean);
            isSuccess = true;
        }catch (Exception e){
            log.error("新增异常:", e);
        }

        return isSuccess;
    }
}
