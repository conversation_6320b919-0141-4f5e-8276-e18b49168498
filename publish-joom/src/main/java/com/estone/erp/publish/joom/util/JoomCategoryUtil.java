package com.estone.erp.publish.joom.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.util.HttpUtils;
import com.estone.erp.publish.joom.util.modal.JoomCategory;

import lombok.extern.slf4j.Slf4j;

/**
 * Joom爬取系统分配类目
 * <AUTHOR>
 *
 */
@Slf4j
public class JoomCategoryUtil {

    private final static String URL = "http://172.16.10.10:8890/api/getJoomCategories";

    public static String getJoomCategoryName(String joomItemId) {
        HttpParams<String> httpParams = new HttpParams<>();
        httpParams.setUrl(URL);
        httpParams.setHttpMethod(HttpMethod.POST);
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("productId", joomItemId);
        httpParams.setBody(JSON.toJSONString(bodyMap));
        try {
            ApiResult<String> result = HttpUtils.exchange(httpParams, ApiResult.class);
            if(result.isSuccess()) {
                JoomCategory joomCategory = JSON.parseObject(JSON.toJSONString(result.getResult()), JoomCategory.class);
                if(CollectionUtils.isNotEmpty(joomCategory.getCategories())) {
                    return StringUtils.join(joomCategory.getCategories(), " > ");
                }
            }else {
                log.error("JoomItem:{}爬取类目失败:{}", joomItemId, result.getErrorMsg());
            }
        }catch(Exception e) {
            log.error(e.getMessage());
        }
        return "";
    }
}
