package com.estone.erp.publish.joom.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.joom.model.ExpManageSku;
import com.estone.erp.publish.joom.service.ExpManageSkuService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步仓库保质期促销sku
 *
 * <AUTHOR>
 * @date 2022-11-30 9:26
 */
@Slf4j
@Component
public class SyncExpManageSkuConsumer {

    @Autowired
    private ExpManageSkuService expManageSkuService;

    @RabbitListener(queues = PublishQueues.PUSH_EXP_MANAGE_TO_PUBLISH_QUEUE, containerFactory = "oneConsumerManualAck")
    public void onMessage(Message message, Channel channel) throws IOException {
        StopWatch started = StopWatch.createStarted();
        Exception exception = null;
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            boolean isSuccess = execute(body);
            if (isSuccess) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        } catch (Exception e) {
            exception = e;
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            log.info("同步保质期促销sku消费:{} error:{}", started, exception == null ? "" : exception.getMessage());
        }
    }

    private boolean execute(String body) {
        List<ExpManageSku> expManageSkus = passParam(body);
        if (expManageSkus == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(expManageSkus)) {
            return true;
        }
//        log.info("msg:{}",JSON.toJSONString(expManageSkus));
        List<List<ExpManageSku>> partition = Lists.partition(expManageSkus, 500);
        partition.forEach(manageSkus -> {
            List<ExpManageSku> updateRecord = new ArrayList<>();
            List<ExpManageSku> insertRecord = new ArrayList<>();
            Map<String, Integer> existRecordMap = searchExistRecord(manageSkus);
            for (ExpManageSku skus : manageSkus) {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                Integer recordId = existRecordMap.get(skus.getBatchNo() + skus.getSku());
                if (recordId != null) {
                    skus.setId(recordId);
                    skus.setSyncTime(now);
                    skus.setUpdatedTime(now);
                    updateRecord.add(skus);
                }else {
                    skus.setSyncTime(now);
                    skus.setCreatedTime(now);
                    skus.setUpdatedTime(now);
                    insertRecord.add(skus);
                }
            }
            if (CollectionUtils.isNotEmpty(updateRecord)) {
                log.info("update record size:{}", updateRecord.size());
                expManageSkuService.batchUpdateRecord(updateRecord);
            }
            if (CollectionUtils.isNotEmpty(insertRecord)) {
                log.info("insert record size:{}", insertRecord.size());
                expManageSkuService.batchInsertRecord(insertRecord);
            }
        });
        return true;
    }

    /**
     * 查询存在记录的保质期促销sku
     * @return key: batch_no + sku
     *         value: id
     */
    private Map<String, Integer> searchExistRecord(List<ExpManageSku> manageSkus) {
        List<String> skuList = new ArrayList<>(manageSkus.size());
        List<String> batchNos = new ArrayList<>(manageSkus.size());
        for (ExpManageSku skus : manageSkus) {
            skuList.add(skus.getSku());
            batchNos.add(skus.getBatchNo());
        }
        List<ExpManageSku> existRecord = expManageSkuService.searchExistRecord(skuList, batchNos);
        return existRecord.stream()
                .collect(Collectors.toMap(
                    record -> record.getBatchNo()+record.getSku(), // key : batch_no + sku
                    ExpManageSku::getId, // value : id
                    (o1, o2) -> o1)
                );
    }

    private List<ExpManageSku> passParam(String body) {
        try {
            return JSON.parseArray(body, ExpManageSku.class);
        } catch (Exception e) {
            log.error("同步保质期促销sku, 解析参数异常：{} error:{}", body, e.getMessage());
            return null;
        }
    }


}
