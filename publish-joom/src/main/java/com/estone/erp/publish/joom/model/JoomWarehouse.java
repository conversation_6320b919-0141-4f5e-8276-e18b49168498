package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class JoomWarehouse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column joom_warehouse.id
     */
    private Integer id;

    /**
     * 帐号名称 database column joom_warehouse.account
     */
    private String account;

    /**
     * 仓库ID database column joom_warehouse.warehouse_id
     */
    private String warehouseId;

    /**
     * 仓库名称 database column joom_warehouse.warehouse_name
     */
    private String warehouseName;

    /**
     *  database column joom_warehouse.creation_date
     */
    private Timestamp creationDate;
}