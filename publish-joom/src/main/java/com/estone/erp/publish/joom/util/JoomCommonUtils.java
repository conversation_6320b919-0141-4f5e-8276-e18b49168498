package com.estone.erp.publish.joom.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.util.Env;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.joom.model.Host;
import com.estone.erp.publish.joom.model.HostCriteria;
import com.estone.erp.publish.joom.service.HostService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

@Slf4j
public class JoomCommonUtils {

    private static SystemParamService systemParamService = SpringUtils.getBean("systemParamService", SystemParamService.class);

    public final static  String JOOM_PUBLISH_UPLOAD ="/api/template/singleUpload";

    /**
     * 绑定sku
     * 
     * @param sellerId
     * @param sku
     * @param platform
     * @return
     */
    public static EsSkuBind getRandomSku(String sellerId, String sku, String platform) {

        EsSkuBind skuBind = new EsSkuBind();
        String randomSku = "";

        String frist = sellerId.substring(0, 1);

        String last = sellerId.substring(sellerId.length() - 1, sellerId.length());

        Random random = new Random();

        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");

        // 171123111659
        String format = sdf.format(System.currentTimeMillis());

        String year = format.substring(0, 2);

        String month = format.substring(2, 4);

        String fDay = format.substring(4, 5);

        String tDay = format.substring(5, 6);

        String hours = format.substring(6, 8);

        // String fMinutes = format.substring(8, 9);
        //
        // String tMinutes = format.substring(9, 10);
        //
        // String fSeconds = format.substring(10, 11);
        //
        // String tSeconds = format.substring(11, 12);

        // 随机大写字母 1
        randomSku += (char) (random.nextInt(25) + 65);

        // 年 大写字母 2
        Integer valueOf = Integer.valueOf(year);
        
        if(valueOf != null && valueOf > 25){
            randomSku += (char) (random.nextInt(25) + 65);
        }else{
            randomSku += (char) (valueOf + 65);
        }
        
        // 随机小写字母 3
        randomSku += (char) (random.nextInt(25) + 97);

        // 月 小写字母 4
        randomSku += (char) (Integer.valueOf(month) + 97);

        // 随机小写字母 5
        randomSku += (char) (random.nextInt(25) + 97);

        // 第一个日 6
        randomSku += fDay;

        // 随机小写字母 7
        randomSku += random.nextInt(10);

        // 随机小写字母 8
        randomSku += (char) (random.nextInt(25) + 97);

        // 第二个日 9
        randomSku += tDay;

        // 随机小写字母 10
        randomSku += (char) (random.nextInt(25) + 97);

        // 小时 小写字母 11
        randomSku += (char) (Integer.valueOf(hours) + 97);

        // 随机数 12
        randomSku += random.nextInt(10);

        // 账号第一个字母 13
        randomSku += frist;

        // 随机数 14
        randomSku += random.nextInt(10);

        // 账号最后一个字母 15
        randomSku += last;

        // 随机小写字母 16
        randomSku += (char) (random.nextInt(25) + 97);

        //wish 平台会加 -666
        if(StringUtils.isNotEmpty(sku) && StringUtils.indexOf(sku, "-666") != -1){
           sku = StringUtils.substringBefore(sku, "-666");
        }

        randomSku = randomSku.toUpperCase();//转成大写
        skuBind.setId(platform + "_" + randomSku);
        skuBind.setSku(sku);
        skuBind.setBindSku(randomSku);
        skuBind.setSellerId(sellerId);
        skuBind.setPlatform(platform);
        skuBind.setCreateDate(new Timestamp(new Date().getTime()));
        skuBind.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        return skuBind;
    }

    // joom 平台更换图片域名
    public static String transferImageUrlForJoom(String image, String sellerId, String sku) {

        Assert.notNull(sellerId, "sellerId is null");

        HostService hostService = SpringUtils.getBean("hostService", HostService.class);

        HostCriteria query = new HostCriteria();
        query.setSellerIds(sellerId);
        Host host = null;
        List<Host> queryHosts = hostService.selectByExample(query.getExample());
        if (CollectionUtils.isNotEmpty(queryHosts)) {
            host = queryHosts.get(0);
        }

        if (host != null && StringUtils.isNotBlank(host.getHostSite())) {

            // 需要转换 750*750 的像素
            if (host.getHostSite().equals("image.boruite.ltd") || host.getHostSite().equals("image.hengyue.site")) {
                StringBuffer sb = new StringBuffer();
                // 新增俩家公司截取图片750*750像素
                String[] oldImages = image.split("\\|");
                int flag = 0;
                for (int i = 0; i < oldImages.length; i++) {

                    String newurl = getImages(oldImages[i], sellerId, sku);
                    if (newurl != null) {
                        if (flag > 0) {

                            sb.append("|").append(newurl);
                        }
                        else {
                            sb.append(newurl);
                        }
                        flag++;
                    }

                }
//                return sb.toString().replace(Env.FILE_SERVER_HOST, host.getHostSite());
                image = sb.toString();
                image = image.replace(Env.FILE_SERVER_HOST, host.getHostSite());
                SystemParam fileParam = systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.image_prefix");

                if (fileParam != null && StringUtils.isNotBlank(fileParam.getParamValue())) {
                    image = image.replace(fileParam.getParamValue(), host.getHostSite() + "/fms");
                }

            }
            else {

                image = image.replace(Env.FILE_SERVER_HOST, host.getHostSite());
                SystemParam fileParam = systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.image_prefix");

                if (fileParam != null && StringUtils.isNotBlank(fileParam.getParamValue())) {
                    image = image.replace(fileParam.getParamValue(), host.getHostSite() + "/fms");
                }
            }

            // if (image != null && image.contains(Env.FILE_SERVER_HOST)) {
            // StringBuffer sb = new StringBuffer();
            // if (host.getHostSite().equals("image.boruite.ltd") ||
            // host.getHostSite().equals("image.hengyue.site")) {
            //
            // // 新增俩家公司截取图片750*750像素
            // String[] oldImages = image.split("\\|");
            // int flag = 0;
            // for (int i = 0; i < oldImages.length; i++) {
            //
            // String newurl = getImages(oldImages[i], sellerId, sku);
            // if (newurl != null) {
            // if (flag > 0) {
            //
            // sb.append("|").append(newurl);
            // }
            // else {
            // sb.append(newurl);
            // }
            // flag++;
            // }
            //
            // }
            // return sb.toString().replace(Env.FILE_SERVER_HOST,
            // host.getHostSite());
            // }
            // return image.replace(Env.FILE_SERVER_HOST, host.getHostSite());
            // }
        }
        else {
            return transferImageUrl(image);
        }

        return image;
    }

    public static String getImages(String imgUrl, String sellerId, String sku) {

        try {

            URL url = new URL(imgUrl);

            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("GET");

            conn.setConnectTimeout(5 * 1000);

            InputStream inStream = conn.getInputStream();

            Image bi = ImageIO.read(inStream);

            BufferedImage tag = new BufferedImage(750, 750, BufferedImage.TYPE_INT_RGB);

            tag.getGraphics().drawImage(bi, 0, 0, 750, 750, null);

            String relativePath = sku;
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(tag, "jpg", os);
            InputStream is = new ByteArrayInputStream(os.toByteArray());
            String fileName = UUID.randomUUID() + ".jpg";

            fileName = fileName.replaceAll("-", "");
            Map<String, Object> rspMap = new HashMap<>();
            String res  = uploadPictureJoom(JOOM_PUBLISH_UPLOAD, fileName, is);
            is.close();
            os.close();
            conn.disconnect();
            return res;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 上传至文件系统
     * @param url
     * @param file
     * @return
     */
    public  static String  uploadPictureJoom(String url, String originalFilename, InputStream contentStream) {
        Map<String, Object> responseMap = null;
        try {
            String picture_url = "http://" + systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.picture_url").getParamValue();
            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpPost httpPost = new HttpPost(picture_url + url);
            httpPost.setHeader("Authorization", PublishCommonConstant.Authorization_Token);// token
            ContentType strContent = ContentType.create("text/plain", Charset.forName("UTF-8"));
            MultipartEntityBuilder meb = MultipartEntityBuilder.create();
            byte[] bytes = FileCopyUtils.copyToByteArray(contentStream);
            meb.addBinaryBody("picFile", bytes, ContentType.DEFAULT_BINARY, originalFilename);
            HttpEntity httpEntity = meb.build();
            httpPost.setEntity(httpEntity);
            HttpResponse response = httpClient.execute(httpPost);
            // 获取响应消息实体
            HttpEntity entity = response.getEntity();
            String responseJson = EntityUtils.toString(entity);
            String res = null;
            if (response.getStatusLine().getStatusCode() == 200 && entity != null) {
                responseMap = JSON.parseObject(responseJson, Map.class);
                res = responseMap.get("result").toString();
            }
            return res;
        }
        catch (Exception e) {
        }
        return null;
    }

    public static String transferImageUrl(String image) {
        if (image != null && image.contains(Env.FILE_SERVER_HOST)) {
            return image.replace(Env.FILE_SERVER_HOST, Env.PUBLIC_FILE_SERVER_HOST + ":" + Env.PUBLIC_FILE_SERVER_PORT);
        }
        else if (image != null) {
            return transferFileServiceImageUrl(image);
        }

        return image;
    }

    /**
     * 替换文件系统的 图片地址
     * 
     * @param image
     * @return
     */
    public static String transferFileServiceImageUrl(String image) {

        // 图片url前缀
        SystemParam fileParam = systemParamService.querySystemParamByCodeKey("PICTURE_URL_ROUTE.image_prefix");

        if (fileParam != null && StringUtils.isNotBlank(fileParam.getParamValue())) {
            String paramValue = fileParam.getParamValue();

            if (StringUtils.contains(image, paramValue)) {
                image = image.replace(paramValue,
                        Env.PUBLIC_FILE_SERVER_HOST + ":" + Env.PUBLIC_FILE_SERVER_PORT + "/fms");
            }
        }

        return image;
    }
}
