package com.estone.erp.publish.joom.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ExpManageSkuExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ExpManageSkuExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`source` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`source` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("`source` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("`source` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("`source` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("`source` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("`source` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("`source` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("`source` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("`source` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("`source` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("`source` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andProDateIsNull() {
            addCriterion("pro_date is null");
            return (Criteria) this;
        }

        public Criteria andProDateIsNotNull() {
            addCriterion("pro_date is not null");
            return (Criteria) this;
        }

        public Criteria andProDateEqualTo(Timestamp value) {
            addCriterion("pro_date =", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateNotEqualTo(Timestamp value) {
            addCriterion("pro_date <>", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateGreaterThan(Timestamp value) {
            addCriterion("pro_date >", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("pro_date >=", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateLessThan(Timestamp value) {
            addCriterion("pro_date <", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("pro_date <=", value, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateIn(List<Timestamp> values) {
            addCriterion("pro_date in", values, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateNotIn(List<Timestamp> values) {
            addCriterion("pro_date not in", values, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("pro_date between", value1, value2, "proDate");
            return (Criteria) this;
        }

        public Criteria andProDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("pro_date not between", value1, value2, "proDate");
            return (Criteria) this;
        }

        public Criteria andDaysIsNull() {
            addCriterion("`days` is null");
            return (Criteria) this;
        }

        public Criteria andDaysIsNotNull() {
            addCriterion("`days` is not null");
            return (Criteria) this;
        }

        public Criteria andDaysEqualTo(Integer value) {
            addCriterion("`days` =", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotEqualTo(Integer value) {
            addCriterion("`days` <>", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThan(Integer value) {
            addCriterion("`days` >", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("`days` >=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThan(Integer value) {
            addCriterion("`days` <", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThanOrEqualTo(Integer value) {
            addCriterion("`days` <=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysIn(List<Integer> values) {
            addCriterion("`days` in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotIn(List<Integer> values) {
            addCriterion("`days` not in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysBetween(Integer value1, Integer value2) {
            addCriterion("`days` between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("`days` not between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andExpDateIsNull() {
            addCriterion("exp_date is null");
            return (Criteria) this;
        }

        public Criteria andExpDateIsNotNull() {
            addCriterion("exp_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpDateEqualTo(Timestamp value) {
            addCriterion("exp_date =", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateNotEqualTo(Timestamp value) {
            addCriterion("exp_date <>", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateGreaterThan(Timestamp value) {
            addCriterion("exp_date >", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("exp_date >=", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateLessThan(Timestamp value) {
            addCriterion("exp_date <", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("exp_date <=", value, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateIn(List<Timestamp> values) {
            addCriterion("exp_date in", values, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateNotIn(List<Timestamp> values) {
            addCriterion("exp_date not in", values, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("exp_date between", value1, value2, "expDate");
            return (Criteria) this;
        }

        public Criteria andExpDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("exp_date not between", value1, value2, "expDate");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsIsNull() {
            addCriterion("remaining_months is null");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsIsNotNull() {
            addCriterion("remaining_months is not null");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsEqualTo(Double value) {
            addCriterion("remaining_months =", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsNotEqualTo(Double value) {
            addCriterion("remaining_months <>", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsGreaterThan(Double value) {
            addCriterion("remaining_months >", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsGreaterThanOrEqualTo(Double value) {
            addCriterion("remaining_months >=", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsLessThan(Double value) {
            addCriterion("remaining_months <", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsLessThanOrEqualTo(Double value) {
            addCriterion("remaining_months <=", value, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsIn(List<Double> values) {
            addCriterion("remaining_months in", values, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsNotIn(List<Double> values) {
            addCriterion("remaining_months not in", values, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsBetween(Double value1, Double value2) {
            addCriterion("remaining_months between", value1, value2, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andRemainingMonthsNotBetween(Double value1, Double value2) {
            addCriterion("remaining_months not between", value1, value2, "remainingMonths");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityIsNull() {
            addCriterion("lend_on_way_quantity is null");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityIsNotNull() {
            addCriterion("lend_on_way_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityEqualTo(Integer value) {
            addCriterion("lend_on_way_quantity =", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityNotEqualTo(Integer value) {
            addCriterion("lend_on_way_quantity <>", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityGreaterThan(Integer value) {
            addCriterion("lend_on_way_quantity >", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("lend_on_way_quantity >=", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityLessThan(Integer value) {
            addCriterion("lend_on_way_quantity <", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("lend_on_way_quantity <=", value, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityIn(List<Integer> values) {
            addCriterion("lend_on_way_quantity in", values, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityNotIn(List<Integer> values) {
            addCriterion("lend_on_way_quantity not in", values, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityBetween(Integer value1, Integer value2) {
            addCriterion("lend_on_way_quantity between", value1, value2, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andLendOnWayQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("lend_on_way_quantity not between", value1, value2, "lendOnWayQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityIsNull() {
            addCriterion("check_in_quantity is null");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityIsNotNull() {
            addCriterion("check_in_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityEqualTo(Integer value) {
            addCriterion("check_in_quantity =", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityNotEqualTo(Integer value) {
            addCriterion("check_in_quantity <>", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityGreaterThan(Integer value) {
            addCriterion("check_in_quantity >", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_in_quantity >=", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityLessThan(Integer value) {
            addCriterion("check_in_quantity <", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("check_in_quantity <=", value, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityIn(List<Integer> values) {
            addCriterion("check_in_quantity in", values, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityNotIn(List<Integer> values) {
            addCriterion("check_in_quantity not in", values, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityBetween(Integer value1, Integer value2) {
            addCriterion("check_in_quantity between", value1, value2, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andCheckInQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("check_in_quantity not between", value1, value2, "checkInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityIsNull() {
            addCriterion("allocation_in_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityIsNotNull() {
            addCriterion("allocation_in_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityEqualTo(Integer value) {
            addCriterion("allocation_in_quantity =", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityNotEqualTo(Integer value) {
            addCriterion("allocation_in_quantity <>", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityGreaterThan(Integer value) {
            addCriterion("allocation_in_quantity >", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("allocation_in_quantity >=", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityLessThan(Integer value) {
            addCriterion("allocation_in_quantity <", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("allocation_in_quantity <=", value, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityIn(List<Integer> values) {
            addCriterion("allocation_in_quantity in", values, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityNotIn(List<Integer> values) {
            addCriterion("allocation_in_quantity not in", values, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityBetween(Integer value1, Integer value2) {
            addCriterion("allocation_in_quantity between", value1, value2, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationInQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("allocation_in_quantity not between", value1, value2, "allocationInQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIsNull() {
            addCriterion("return_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIsNotNull() {
            addCriterion("return_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityEqualTo(Integer value) {
            addCriterion("return_quantity =", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotEqualTo(Integer value) {
            addCriterion("return_quantity <>", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityGreaterThan(Integer value) {
            addCriterion("return_quantity >", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_quantity >=", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityLessThan(Integer value) {
            addCriterion("return_quantity <", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("return_quantity <=", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIn(List<Integer> values) {
            addCriterion("return_quantity in", values, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotIn(List<Integer> values) {
            addCriterion("return_quantity not in", values, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityBetween(Integer value1, Integer value2) {
            addCriterion("return_quantity between", value1, value2, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("return_quantity not between", value1, value2, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityIsNull() {
            addCriterion("deliver_quantity is null");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityIsNotNull() {
            addCriterion("deliver_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityEqualTo(Integer value) {
            addCriterion("deliver_quantity =", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityNotEqualTo(Integer value) {
            addCriterion("deliver_quantity <>", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityGreaterThan(Integer value) {
            addCriterion("deliver_quantity >", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("deliver_quantity >=", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityLessThan(Integer value) {
            addCriterion("deliver_quantity <", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("deliver_quantity <=", value, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityIn(List<Integer> values) {
            addCriterion("deliver_quantity in", values, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityNotIn(List<Integer> values) {
            addCriterion("deliver_quantity not in", values, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityBetween(Integer value1, Integer value2) {
            addCriterion("deliver_quantity between", value1, value2, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andDeliverQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("deliver_quantity not between", value1, value2, "deliverQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityIsNull() {
            addCriterion("scrap_quantity is null");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityIsNotNull() {
            addCriterion("scrap_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityEqualTo(Integer value) {
            addCriterion("scrap_quantity =", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityNotEqualTo(Integer value) {
            addCriterion("scrap_quantity <>", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityGreaterThan(Integer value) {
            addCriterion("scrap_quantity >", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("scrap_quantity >=", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityLessThan(Integer value) {
            addCriterion("scrap_quantity <", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("scrap_quantity <=", value, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityIn(List<Integer> values) {
            addCriterion("scrap_quantity in", values, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityNotIn(List<Integer> values) {
            addCriterion("scrap_quantity not in", values, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityBetween(Integer value1, Integer value2) {
            addCriterion("scrap_quantity between", value1, value2, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andScrapQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("scrap_quantity not between", value1, value2, "scrapQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityIsNull() {
            addCriterion("bad_product_quantity is null");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityIsNotNull() {
            addCriterion("bad_product_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityEqualTo(Integer value) {
            addCriterion("bad_product_quantity =", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityNotEqualTo(Integer value) {
            addCriterion("bad_product_quantity <>", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityGreaterThan(Integer value) {
            addCriterion("bad_product_quantity >", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("bad_product_quantity >=", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityLessThan(Integer value) {
            addCriterion("bad_product_quantity <", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("bad_product_quantity <=", value, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityIn(List<Integer> values) {
            addCriterion("bad_product_quantity in", values, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityNotIn(List<Integer> values) {
            addCriterion("bad_product_quantity not in", values, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityBetween(Integer value1, Integer value2) {
            addCriterion("bad_product_quantity between", value1, value2, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andBadProductQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("bad_product_quantity not between", value1, value2, "badProductQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityIsNull() {
            addCriterion("allocation_out_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityIsNotNull() {
            addCriterion("allocation_out_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityEqualTo(Integer value) {
            addCriterion("allocation_out_quantity =", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityNotEqualTo(Integer value) {
            addCriterion("allocation_out_quantity <>", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityGreaterThan(Integer value) {
            addCriterion("allocation_out_quantity >", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("allocation_out_quantity >=", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityLessThan(Integer value) {
            addCriterion("allocation_out_quantity <", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("allocation_out_quantity <=", value, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityIn(List<Integer> values) {
            addCriterion("allocation_out_quantity in", values, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityNotIn(List<Integer> values) {
            addCriterion("allocation_out_quantity not in", values, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityBetween(Integer value1, Integer value2) {
            addCriterion("allocation_out_quantity between", value1, value2, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andAllocationOutQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("allocation_out_quantity not between", value1, value2, "allocationOutQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIsNull() {
            addCriterion("inventory_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIsNotNull() {
            addCriterion("inventory_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityEqualTo(Integer value) {
            addCriterion("inventory_quantity =", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotEqualTo(Integer value) {
            addCriterion("inventory_quantity <>", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityGreaterThan(Integer value) {
            addCriterion("inventory_quantity >", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_quantity >=", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityLessThan(Integer value) {
            addCriterion("inventory_quantity <", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_quantity <=", value, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityIn(List<Integer> values) {
            addCriterion("inventory_quantity in", values, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotIn(List<Integer> values) {
            addCriterion("inventory_quantity not in", values, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityBetween(Integer value1, Integer value2) {
            addCriterion("inventory_quantity between", value1, value2, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_quantity not between", value1, value2, "inventoryQuantity");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateIsNull() {
            addCriterion("push_publish_state is null");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateIsNotNull() {
            addCriterion("push_publish_state is not null");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateEqualTo(Integer value) {
            addCriterion("push_publish_state =", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateNotEqualTo(Integer value) {
            addCriterion("push_publish_state <>", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateGreaterThan(Integer value) {
            addCriterion("push_publish_state >", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("push_publish_state >=", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateLessThan(Integer value) {
            addCriterion("push_publish_state <", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateLessThanOrEqualTo(Integer value) {
            addCriterion("push_publish_state <=", value, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateIn(List<Integer> values) {
            addCriterion("push_publish_state in", values, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateNotIn(List<Integer> values) {
            addCriterion("push_publish_state not in", values, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateBetween(Integer value1, Integer value2) {
            addCriterion("push_publish_state between", value1, value2, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andPushPublishStateNotBetween(Integer value1, Integer value2) {
            addCriterion("push_publish_state not between", value1, value2, "pushPublishState");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateIsNull() {
            addCriterion("flag_update_date is null");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateIsNotNull() {
            addCriterion("flag_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedDateIsNull() {
            addCriterion("deleted_time is null");
            return (Criteria) this;
        }
        public Criteria andDeletedDateIsNotNull() {
            addCriterion("deleted_time is not null");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateEqualTo(Timestamp value) {
            addCriterion("flag_update_date =", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("flag_update_date <>", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateGreaterThan(Timestamp value) {
            addCriterion("flag_update_date >", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("flag_update_date >=", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateLessThan(Timestamp value) {
            addCriterion("flag_update_date <", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("flag_update_date <=", value, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateIn(List<Timestamp> values) {
            addCriterion("flag_update_date in", values, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("flag_update_date not in", values, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("flag_update_date between", value1, value2, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andFlagUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("flag_update_date not between", value1, value2, "flagUpdateDate");
            return (Criteria) this;
        }

        public Criteria andSyncTimeIsNull() {
            addCriterion("sync_time is null");
            return (Criteria) this;
        }

        public Criteria andSyncTimeIsNotNull() {
            addCriterion("sync_time is not null");
            return (Criteria) this;
        }

        public Criteria andSyncTimeEqualTo(Timestamp value) {
            addCriterion("sync_time =", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeNotEqualTo(Timestamp value) {
            addCriterion("sync_time <>", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeGreaterThan(Timestamp value) {
            addCriterion("sync_time >", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sync_time >=", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeLessThan(Timestamp value) {
            addCriterion("sync_time <", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("sync_time <=", value, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeIn(List<Timestamp> values) {
            addCriterion("sync_time in", values, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeNotIn(List<Timestamp> values) {
            addCriterion("sync_time not in", values, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_time between", value1, value2, "syncTime");
            return (Criteria) this;
        }

        public Criteria andSyncTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_time not between", value1, value2, "syncTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}