package com.estone.erp.publish.joom.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.enums.DrpTurnoverOderTypeEnum;
import com.estone.erp.publish.joom.enums.PushPublishExpFlagEnums;
import com.estone.erp.publish.joom.model.ExpManageSku;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-29 17:30
 */
@Data
public class ExpManageSkuVO implements Serializable {
    /**
     * sku
     */
    @ExcelProperty("sku")
    private String sku;

    /**
     * 批次号
     */
    @ExcelProperty("批次号")
    private String batchNo;

    /**
     * 来源
     */
    @ExcelProperty("来源")
    private String sourceStr;

    /**
     * 生产日期
     */
    @ExcelProperty("生产日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date proDate;

    /**
     * 到期时间
     */
    @ExcelProperty("到期时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date expDate;


    /**
     * 保质期天数
     */
    @ExcelProperty("保质期天数")
    private Integer days;


    /**
     * 剩余月份
     */
    @ExcelProperty("剩余月份")
    private Double remainingMonths;

    /**
     * 在线库存
     */
    @ExcelProperty("在线库存")
    private Integer quantity;

    /**
     * 外借在途
     */
    @ExcelProperty("外借在途")
    private Integer lendOnWayQuantity;

    /**
     * 采购入库
     */
    @ExcelProperty("采购入库")
    private Integer checkInQuantity;

    /**
     * 调拨入库
     */
    @ExcelProperty("调拨入库")
    private Integer allocationInQuantity;

    /**
     * 退件入库
     */
    @ExcelProperty("退件入库")
    private Integer returnQuantity;

    /**
     * 订单出库
     */
    @ExcelProperty("订单出库")
    private Integer deliverQuantity;

    /**
     * 报废出库
     */
    @ExcelProperty("报废出库")
    private Integer scrapQuantity;

    /**
     * 不良品出库
     */
    @ExcelProperty("不良品出库")
    private Integer badProductQuantity;

    /**
     * 调拨出库
     */
    @ExcelProperty("调拨出库")
    private Integer allocationOutQuantity;

    /**
     * 盘点
     */
    @ExcelProperty("盘点")
    private Integer inventoryQuantity;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String pushPublishStateStr;

    /**
     * 状态修改时间
     */
    @ExcelProperty(value = "状态修改时间")
    private Date flagUpdateDate;



    public static ExpManageSkuVO conventVO(ExpManageSku sku) {
        ExpManageSkuVO expManageSkuVO = BeanUtil.copyProperties(sku, ExpManageSkuVO.class);
        expManageSkuVO.setPushPublishStateStr(PushPublishExpFlagEnums.getNameByCode(sku.getPushPublishState().toString()));
        expManageSkuVO.setSourceStr(DrpTurnoverOderTypeEnum.getNameByCode(sku.getSource().toString()));
        return expManageSkuVO;
    }
}
