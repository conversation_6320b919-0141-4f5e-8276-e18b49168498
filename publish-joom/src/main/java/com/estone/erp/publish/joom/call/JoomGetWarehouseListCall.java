package com.estone.erp.publish.joom.call;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.joom.model.JoomWarehouse;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * Joom获取仓库列表
 * @Auther yucm
 * @Date 2020/10/9
 */
@Slf4j
public class JoomGetWarehouseListCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;
    private static final String PATH = "warehouse/multi-get";
    private static final String DEFAULT_CHARSET = "utf-8";


    public JoomGetWarehouseListCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    public List<JoomWarehouse>  getWarehouseList(Integer start, Integer limit) {
        httpClient = HttpClientUtils.createClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));
        standardNameValue.add(new BasicNameValuePair("start", start.toString()));
        standardNameValue.add(new BasicNameValuePair("limit", limit.toString()));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        List<JoomWarehouse> joomWarehouses = null;
        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));
            HttpGet httpGet = new HttpGet();
            httpGet.setURI(new URI(url.toString()));

            CloseableHttpResponse httpResponse = null;
            int retryTimes = 0;

            // 重试三次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpGet);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();
                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        joomWarehouses = this.toJoomWarehouses(responseJson, joomPmsAccount.getAccountNumber());
                    }
                    else {
                        log.error("JoomGetWarehouseListCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;
                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes) + "--" + e.getMessage(), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomGetWarehouseListCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    return null;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            return joomWarehouses;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return joomWarehouses;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    /**
     * 返回数据转化为Joom仓库
     * @param responseJsonStr
     * @return
     */
    private static List<JoomWarehouse> toJoomWarehouses(String responseJsonStr, String account) {
        List<JoomWarehouse> joomWarehouses = new ArrayList<>();

        JSONObject responseJson = JSONObject.parseObject(responseJsonStr);
        if(null == responseJson) {
            return joomWarehouses;
        }

        JSONArray dataJsonArray = responseJson.getJSONArray("data");
        if(null == dataJsonArray) {
            return joomWarehouses;
        }

        for (int i = 0 ; i < dataJsonArray.size(); i++) {
            JSONObject dataJsonObject = dataJsonArray.getJSONObject(i);
            JSONObject warehouseJsonObject = dataJsonObject.getJSONObject("Warehouse");
            if(null == warehouseJsonObject) {
                continue;
            }

            JoomWarehouse joomWarehouse = new JoomWarehouse();
            joomWarehouses.add(joomWarehouse);

            joomWarehouse.setAccount(account);
            joomWarehouse.setWarehouseId(warehouseJsonObject.getString("id"));
            joomWarehouse.setWarehouseName(warehouseJsonObject.getString("name"));
        }

        return joomWarehouses;
    }
}
