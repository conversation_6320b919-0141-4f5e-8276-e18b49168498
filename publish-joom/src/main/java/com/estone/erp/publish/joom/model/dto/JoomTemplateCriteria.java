package com.estone.erp.publish.joom.model.dto;

import com.estone.erp.publish.joom.model.JoomTemplate;
import com.estone.erp.publish.joom.model.JoomTemplateExample;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> joom_template 2019-08-08 10:45:07
 */
public class JoomTemplateCriteria extends JoomTemplate {
    private static final long serialVersionUID = 1L;

    private List<Integer> ids;

    private String idStr;

    private String skus;

    private Date fromCreateDate;

    private Date toCreateDate;

    private String orderBy = "last_update_date desc";

    private String skuChild;

    private String fullPathCodes;

    private List<String> sellerIdList;

    private List<String> createByList;
    public JoomTemplateExample getExample() {
        JoomTemplateExample example = new JoomTemplateExample();
        JoomTemplateExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(this.getIds())) {
            criteria.andTemplateIdIn(ids);
        }
        if (StringUtils.isNotBlank(this.getIdStr())) {
            List<Integer> idList = new ArrayList<>();
            if (StringUtils.contains(this.getIdStr(), ",")) {
                String[] splits = StringUtils.split(this.getIdStr(), ",");
                for (String idStr : splits) {
                    idList.add(Integer.valueOf(idStr.trim()));
                }
                criteria.andTemplateIdIn(idList);
            }
            else {
                criteria.andTemplateIdEqualTo(Integer.valueOf(this.getIdStr().trim()));
            }
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            if (StringUtils.contains(this.getSkus(), ",")) {
                List<String> skuList = new ArrayList<>();
                for( String sku : StringUtils.split(this.getSkus(), ",")) {
                    skuList.add(sku.trim());
                }
                criteria.andSkuIn(skuList);
            }
            else {
                criteria.andSkuEqualTo(this.getSkus().trim());
            }
        }
        if (this.getFromCreateDate() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(this.getFromCreateDate());
        }
        if (this.getToCreateDate() != null) {
            criteria.andCreationDateLessThanOrEqualTo(this.getToCreateDate());
        }
        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameLike("%" + this.getName() + "%");
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreatedByIn(this.getCreateByList());
        }
        if (StringUtils.isNotBlank(this.getOrderBy())) {
            example.setOrderByClause(this.getOrderBy());
        }
        if (StringUtils.isNotBlank(this.getSkuChild())) {
            criteria.andVariationsLike("%" + this.getSkuChild() + "%");
        }
        if (StringUtils.isNotBlank(this.getFullPathCodes())) {
            if (StringUtils.contains(this.getFullPathCodes(), ",")) {
                String[] splits = StringUtils.split(this.getFullPathCodes(), ",");
                criteria.andFullPathCodeIn(Arrays.asList(splits));
            }
            else {
                criteria.andFullPathCodeEqualTo(this.getFullPathCodes());
            }
        }
        if (this.getIsLock() != null) {
            criteria.andIsLockEqualTo(this.getIsLock());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        else {
            criteria.andCategoryIdSpecialOr();
        }
        if (StringUtils.isNotBlank(this.getSellerId())) {
            if (StringUtils.contains(this.getSellerId(), ",")) {
                String[] splits = StringUtils.split(this.getSellerId(), ",");
                criteria.andSellerIdIn(Arrays.asList(splits));
            }
            else {
                criteria.andSellerIdEqualTo(this.getSellerId());
            }
        }
        if (CollectionUtils.isNotEmpty(this.getSellerIdList())) {
            criteria.andSellerIdIn(this.getSellerIdList());
            criteria.andContentLike("[刊登成功]%");
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        return example;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getIdStr() {
        return idStr;
    }

    public void setIdStr(String idStr) {
        this.idStr = idStr;
    }

    public String getSkus() {
        return skus;
    }

    public void setSkus(String skus) {
        this.skus = skus;
    }

    public Date getFromCreateDate() {
        return fromCreateDate;
    }

    public void setFromCreateDate(Date fromCreateDate) {
        this.fromCreateDate = fromCreateDate;
    }

    public Date getToCreateDate() {
        return toCreateDate;
    }

    public void setToCreateDate(Date toCreateDate) {
        this.toCreateDate = toCreateDate;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getSkuChild() {
        return skuChild;
    }

    public void setSkuChild(String skuChild) {
        this.skuChild = skuChild;
    }

    public String getFullPathCodes() {
        return fullPathCodes;
    }

    public void setFullPathCodes(String fullPathCodes) {
        this.fullPathCodes = fullPathCodes;
    }

    public List<String> getSellerIdList() {
        return sellerIdList;
    }

    public void setSellerIdList(List<String> sellerIdList) {
        this.sellerIdList = sellerIdList;
    }

    public List<String> getCreateByList() {
        return createByList;
    }

    public void setCreateByList(List<String> createByList) {
        this.createByList = createByList;
    }
}