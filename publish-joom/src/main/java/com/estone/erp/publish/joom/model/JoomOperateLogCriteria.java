package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> joom_operate_log
 * 2022-11-10 11:52:21
 */
public class JoomOperateLogCriteria extends JoomOperateLog {
    private static final long serialVersionUID = 1L;

    public JoomOperateLogExample getExample() {
        JoomOperateLogExample example = new JoomOperateLogExample();
        JoomOperateLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getType())) {
            criteria.andTypeEqualTo(this.getType());
        }
        if (this.getBusinessId() != null) {
            criteria.andBusinessIdEqualTo(this.getBusinessId());
        }
        if (StringUtils.isNotBlank(this.getFieldName())) {
            criteria.andFieldNameEqualTo(this.getFieldName());
        }
        if (StringUtils.isNotBlank(this.getBefore())) {
            criteria.andBeforeEqualTo(this.getBefore());
        }
        if (StringUtils.isNotBlank(this.getAfter())) {
            criteria.andAfterEqualTo(this.getAfter());
        }
        if (StringUtils.isNotBlank(this.getMessage())) {
            criteria.andMessageEqualTo(this.getMessage());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        return example;
    }
}