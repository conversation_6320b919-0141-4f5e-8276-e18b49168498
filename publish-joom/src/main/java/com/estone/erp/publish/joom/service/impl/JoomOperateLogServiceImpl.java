package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.JoomOperateLogMapper;
import com.estone.erp.publish.joom.model.JoomOperateLog;
import com.estone.erp.publish.joom.model.JoomOperateLogCriteria;
import com.estone.erp.publish.joom.model.JoomOperateLogExample;
import com.estone.erp.publish.joom.service.JoomOperateLogService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> joom_operate_log
 * 2022-11-10 11:52:21
 */
@Service("joomOperateLogService")
@Slf4j
public class JoomOperateLogServiceImpl implements JoomOperateLogService {
    @Resource
    private JoomOperateLogMapper joomOperateLogMapper;

    @Override
    public int countByExample(JoomOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return joomOperateLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomOperateLog> search(CQuery<JoomOperateLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomOperateLogCriteria query = cquery.getSearch();
        JoomOperateLogExample example = query.getExample();

        // 默认倒叙
        example.setOrderByClause(" id desc ");

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomOperateLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomOperateLog> joomOperateLogs = joomOperateLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomOperateLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomOperateLogs);
        return result;
    }

    @Override
    public JoomOperateLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return joomOperateLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<JoomOperateLog> selectByExample(JoomOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return joomOperateLogMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return joomOperateLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomOperateLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomOperateLog record, JoomOperateLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomOperateLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return joomOperateLogMapper.deleteByPrimaryKey(ids);
    }
}