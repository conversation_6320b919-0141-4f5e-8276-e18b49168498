package com.estone.erp.publish.joom.model;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> t_host
 * 2019-08-12 18:15:59
 */
public class HostCriteria extends Host {
    private static final long serialVersionUID = 1L;

    private String sellerIds;

    public HostExample getExample() {
        HostExample example = new HostExample();
        HostExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSellerIds())) {
            if(StringUtils.contains(this.getSellerIds(), ",")) {
                criteria.andSellerIdIn(Arrays.asList(StringUtils.split(this.getSellerIds(), ",")));
            }else {
                criteria.andSellerIdEqualTo(this.getSellerIds());
            }
        }
        if (StringUtils.isNotBlank(this.getPlatform())) {
            criteria.andPlatformEqualTo(this.getPlatform());
        }
        if (StringUtils.isNotBlank(this.getHostSite())) {
            criteria.andHostSiteEqualTo(this.getHostSite());
        }
        return example;
    }

    public String getSellerIds() {
        return sellerIds;
    }

    public void setSellerIds(String sellerIds) {
        this.sellerIds = sellerIds;
    }
}