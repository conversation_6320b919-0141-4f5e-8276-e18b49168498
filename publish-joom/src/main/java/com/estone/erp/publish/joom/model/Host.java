package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import lombok.Data;

@Data
public class Host implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column t_host.host_id
     */
    private Integer hostId;

    /**
     *  database column t_host.seller_id
     */
    private String sellerId;

    /**
     *  database column t_host.platform
     */
    private String platform;

    /**
     *  database column t_host.host_site
     */
    private String hostSite;
}