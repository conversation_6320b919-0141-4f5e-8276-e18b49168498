package com.estone.erp.publish.joom.model;

import com.estone.erp.common.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class JoomItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column joom_item.item_id
     */
    private Long itemId;

    /**
     * 平台ID database column joom_item.joom_item_id
     */
    private String joomItemId;

    /**
     * 帐号 database column joom_item.item_seller
     */
    private String itemSeller;

    /**
     * 标题 database column joom_item.item_title
     */
    private String itemTitle;

    /**
     * 状态 database column joom_item.review_status
     */
    private String reviewStatus;

    /**
     * 是否促销 database column joom_item.is_promoted
     */
    private Boolean isPromoted;

    /**
     * 收藏数量 database column joom_item.number_saves
     */
    private Integer numberSaves;

    /**
     * 卖出数量 database column joom_item.number_sold
     */
    private Integer numberSold;

    /**
     * 父属性ID database column joom_item.parent_id
     */
    private String parentId;

    /**
     * 父属性SKU database column joom_item.parent_sku
     */
    private String parentSku;

    /**
     * 多属性 database column joom_item.is_multi_attr
     */
    private Boolean isMultiAttr;

    /**
     * 是否为多属性的子属性 database column joom_item.is_variation
     */
    private Boolean isVariation;
    private List<JoomItem> variations;

    /**
     * 是否海外仓 database column joom_item.is_joom_express
     */
    private Boolean isJoomExpress;

    /**
     * 子ID database column joom_item.child_id
     */
    private String childId;

    /**
     * sku database column joom_item.sku
     */
    private String sku;

    /**
     * 货号 database column joom_item.article_number
     */
    private String articleNumber;

    /**
     * 库存 database column joom_item.inventory
     */
    private Integer inventory;

    /**
     *  database column joom_item.last_inventory
     */
    private Integer lastInventory;

    /**
     * 是否启用 database column joom_item.is_enabled
     */
    private Boolean isEnabled;

    /**
     * 建议零售价 database column joom_item.msrp
     */
    private Double msrp;

    /**
     * 价格 database column joom_item.price
     */
    private Double price;

    /**
     * 系统产品价格 database column joom_item.system_price
     */
    private Double systemPrice;

    /**
     * 多属性内容 database column joom_item.multi_attr
     */
    private String multiAttr;

    /**
     * 物流费用 database column joom_item.shipping_cost
     */
    private Double shippingCost;

    /**
     * 运输时间 例如 7-15 7天到15天的范围 database column joom_item.shipping_time
     */
    private String shippingTime;

    /**
     * 主图 database column joom_item.main_image
     */
    private String mainImage;

    /**
     * 特效图 database column joom_item.extra_images
     */
    private String extraImages;

    /**
     *  database column joom_item.all_images
     */
    private String allImages;

    /**
     * 分配类目 database column joom_item.auto_tags
     */
    private String autoTags;

    /**
     * 标签 database column joom_item.tags
     */
    private String tags;

    /**
     * 描述 database column joom_item.description
     */
    private String description;

    /**
     * 是否上架 database column joom_item.is_online
     */
    private Boolean isOnline;

    /**
     *  database column joom_item.is_first_item
     */
    private Boolean isFirstItem;

    /**
     *  database column joom_item.last_sold_date
     */
    private Date lastSoldDate;

    /**
     * 创建时间 database column joom_item.creation_date
     */
    private Date creationDate;

    /**
     * 创建人 database column joom_item.created_by
     */
    private String createdBy;

    /**
     * 修改时间 database column joom_item.last_update_date
     */
    private Date lastUpdateDate;

    /**
     * 修改人 database column joom_item.last_updated_by
     */
    private String lastUpdatedBy;

    /**
     * 产品上传时间 database column joom_item.item_uploaded_date
     */
    private Date itemUploadedDate;

    /**
     *  database column joom_item.item_down_date
     */
    private Date itemDownDate;

    /**
     *  database column joom_item.dangerous_kind
     */
    private String dangerousKind;

    /**
     * 后台父sku database column joom_item.parent_sku_online
     */
    private String parentSkuOnline;

    /**
     * 刊登使用 属性
     */

    private String brand;

    private String landingPageUrl;

    private String upc;

    private String size;

    private String color;

    /**
     * 产品重量kg
     */
    private Double shippingWeight;

    /**
     * 单品状态
     */
    private String skuStatus;

    /**
     * 产品标签
     */
    private String skuTagCode;

    /**
     * 禁售平台
     */
    private String forbidChannel;

    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;

    /**
     * 禁售站点
     */
    private String prohibitionSites;

    /**
     * 产品系统.类目ID
     */
    private Integer categoryId;

    /**
     * 产品系统.类目id路径：12/23/34
     */
    private String categoryIdPath;

    /**
     * 产品系统.类目中文路径
     */
    private String categoryCnName;

    /**
     * 特殊标签
     */
    private String specialGoodsCode;

    /**
     * sku可用库存
     */
    private Integer skuInventory;

    /**
     * 平台状态
     */
    private String state;

    /**
     * 产品系统 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    private Integer promotion;

    /**
     * 新品状态
     */
    private Boolean newState;

    /**
     * sku数据来源
     */
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    private Integer composeStatus;

    private Double shippingHeight;

    private Double shippingLength;

    private Double shippingWidth;

    private Boolean measurementNull;

    /**
     * 修改尺寸输入的值
     */
    private Double inputShippingHeight;

    private Double inputShippingLength;

    private Double inputShippingWidth;

    /**
     * 紫鸟账号
     */
    private String znAccount;

    public Long getItemId() {
        return  itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getJoomItemId() {
        return joomItemId;
    }

    public void setJoomItemId(String joomItemId) {
        this.joomItemId = joomItemId;
    }

    public String getItemSeller() {
        return itemSeller;
    }

    public void setItemSeller(String itemSeller) {
        this.itemSeller = itemSeller;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Boolean getIsPromoted() {
        return isPromoted;
    }

    public void setIsPromoted(Boolean isPromoted) {
        this.isPromoted = isPromoted;
    }

    public Integer getNumberSaves() {
        return numberSaves;
    }

    public void setNumberSaves(Integer numberSaves) {
        this.numberSaves = numberSaves;
    }

    public Integer getNumberSold() {
        return numberSold;
    }

    public void setNumberSold(Integer numberSold) {
        this.numberSold = numberSold;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentSku() {
        return parentSku;
    }

    public void setParentSku(String parentSku) {
        this.parentSku = parentSku;
    }

    public Boolean getIsMultiAttr() {
        return isMultiAttr;
    }

    public void setIsMultiAttr(Boolean isMultiAttr) {
        this.isMultiAttr = isMultiAttr;
    }

    public Boolean getIsVariation() {
        return isVariation;
    }

    public void setIsVariation(Boolean isVariation) {
        this.isVariation = isVariation;
    }

    public List<JoomItem> getVariations() {
        return variations;
    }

    public void setVariations(List<JoomItem> variations) {
        this.variations = variations;
    }

    public Boolean getIsJoomExpress() {
        return isJoomExpress;
    }

    public void setIsJoomExpress(Boolean isJoomExpress) {
        this.isJoomExpress = isJoomExpress;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getArticleNumber() {
        return StrUtil.strTrimToUpperCase(articleNumber);
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public Integer getInventory() {
        return inventory;
    }

    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }

    public Integer getLastInventory() {
        return lastInventory;
    }

    public void setLastInventory(Integer lastInventory) {
        this.lastInventory = lastInventory;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Double getMsrp() {
        return msrp;
    }

    public void setMsrp(Double msrp) {
        this.msrp = msrp;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getSystemPrice() {
        return systemPrice;
    }

    public void setSystemPrice(Double systemPrice) {
        this.systemPrice = systemPrice;
    }

    public String getMultiAttr() {
        return multiAttr;
    }

    public void setMultiAttr(String multiAttr) {
        this.multiAttr = multiAttr;
    }

    public Double getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(Double shippingCost) {
        this.shippingCost = shippingCost;
    }

    public String getShippingTime() {
        return shippingTime;
    }

    public void setShippingTime(String shippingTime) {
        this.shippingTime = shippingTime;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public String getExtraImages() {
        return extraImages;
    }

    public void setExtraImages(String extraImages) {
        this.extraImages = extraImages;
    }

    public String getAllImages() {
        return allImages;
    }

    public void setAllImages(String allImages) {
        this.allImages = allImages;
    }

    public String getAutoTags() {
        return autoTags;
    }

    public void setAutoTags(String autoTags) {
        this.autoTags = autoTags;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(Boolean isOnline) {
        this.isOnline = isOnline;
    }

    public Boolean getIsFirstItem() {
        return isFirstItem;
    }

    public void setIsFirstItem(Boolean isFirstItem) {
        this.isFirstItem = isFirstItem;
    }

    public Date getLastSoldDate() {
        return lastSoldDate;
    }

    public void setLastSoldDate(Date lastSoldDate) {
        this.lastSoldDate = lastSoldDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getItemUploadedDate() {
        return itemUploadedDate;
    }

    public void setItemUploadedDate(Date itemUploadedDate) {
        this.itemUploadedDate = itemUploadedDate;
    }

    public Date getItemDownDate() {
        return itemDownDate;
    }

    public void setItemDownDate(Date itemDownDate) {
        this.itemDownDate = itemDownDate;
    }

    public String getDangerousKind() {
        return dangerousKind;
    }

    public void setDangerousKind(String dangerousKind) {
        this.dangerousKind = dangerousKind;
    }

    public String getParentSkuOnline() {
        return parentSkuOnline;
    }

    public void setParentSkuOnline(String parentSkuOnline) {
        this.parentSkuOnline = parentSkuOnline;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getLandingPageUrl() {
        return landingPageUrl;
    }

    public void setLandingPageUrl(String landingPageUrl) {
        this.landingPageUrl = landingPageUrl;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Double getShippingWeight() {
        return shippingWeight;
    }

    public void setShippingWeight(Double shippingWeight) {
        this.shippingWeight = shippingWeight;
    }

    public String getSkuStatus() {
        return skuStatus;
    }

    public void setSkuStatus(String skuStatus) {
        this.skuStatus = skuStatus;
    }

    public String getSkuTagCode() {
        return skuTagCode;
    }

    public void setSkuTagCode(String skuTagCode) {
        this.skuTagCode = skuTagCode;
    }
}