package com.estone.erp.publish.joom.model;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> exp_manage_sku
 * 2022-11-29 15:18:11
 */
public class ExpManageSkuCriteria extends ExpManageSku {
    private static final long serialVersionUID = 1L;
    private List<Integer> idList;
    private String skus;

    public ExpManageSkuExample getExample() {
        ExpManageSkuExample example = new ExpManageSkuExample();
        ExpManageSkuExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSkus())) {
            String skus = this.getSkus();
            String[] split = skus.split(",");
            List<String> skuList = new ArrayList<>(Arrays.asList(split));
            criteria.andSkuIn(skuList);
        }
        if (CollectionUtils.isNotEmpty(this.getIdList())) {
            criteria.andIdIn(this.getIdList());
        }

        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getBatchNo())) {
            criteria.andBatchNoEqualTo(this.getBatchNo());
        }
        if (this.getSource() != null) {
            criteria.andSourceEqualTo(this.getSource());
        }
        if (this.getProDate() != null) {
            criteria.andProDateEqualTo(this.getProDate());
        }
        if (this.getDays() != null) {
            criteria.andDaysEqualTo(this.getDays());
        }
        if (this.getExpDate() != null) {
            criteria.andExpDateEqualTo(this.getExpDate());
        }
        if (this.getRemainingMonths() != null) {
            criteria.andRemainingMonthsEqualTo(this.getRemainingMonths());
        }
        if (this.getQuantity() != null) {
            criteria.andQuantityEqualTo(this.getQuantity());
        }
        if (this.getLendOnWayQuantity() != null) {
            criteria.andLendOnWayQuantityEqualTo(this.getLendOnWayQuantity());
        }
        if (this.getCheckInQuantity() != null) {
            criteria.andCheckInQuantityEqualTo(this.getCheckInQuantity());
        }
        if (this.getAllocationInQuantity() != null) {
            criteria.andAllocationInQuantityEqualTo(this.getAllocationInQuantity());
        }
        if (this.getReturnQuantity() != null) {
            criteria.andReturnQuantityEqualTo(this.getReturnQuantity());
        }
        if (this.getDeliverQuantity() != null) {
            criteria.andDeliverQuantityEqualTo(this.getDeliverQuantity());
        }
        if (this.getScrapQuantity() != null) {
            criteria.andScrapQuantityEqualTo(this.getScrapQuantity());
        }
        if (this.getBadProductQuantity() != null) {
            criteria.andBadProductQuantityEqualTo(this.getBadProductQuantity());
        }
        if (this.getAllocationOutQuantity() != null) {
            criteria.andAllocationOutQuantityEqualTo(this.getAllocationOutQuantity());
        }
        if (this.getInventoryQuantity() != null) {
            criteria.andInventoryQuantityEqualTo(this.getInventoryQuantity());
        }
        if (this.getPushPublishState() != null) {
            criteria.andPushPublishStateEqualTo(this.getPushPublishState());
        }
        if (this.getFlagUpdateDate() != null) {
            criteria.andFlagUpdateDateEqualTo(this.getFlagUpdateDate());
        }
        if (this.getSyncTime() != null) {
            criteria.andSyncTimeEqualTo(this.getSyncTime());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        criteria.andDeletedDateIsNull();
        return example;
    }

    public String getSkus() {
        return skus;
    }

    public void setSkus(String skus) {
        this.skus = skus;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }
}