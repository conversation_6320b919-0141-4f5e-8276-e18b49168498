package com.estone.erp.publish.joom.util.modal;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Joom国家运费
 * @Auther yucm
 * @Date 2020/10/12
 */
@Setter
@Getter
public class JoomItemVariantCountyShipping {

    /**
     * 商品ID
     */
    private String joomItemId;

    /**
     * 变体Sku
      */
    private String variantSku;

    /**
     * 变体ID
     */
    private String variantId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 默认价格
     */
    private Double defaultShippingPrice;

    /**
     * 运输地区
     */
    private List<JoomShippingRegion> joomShippingRegions;
}
