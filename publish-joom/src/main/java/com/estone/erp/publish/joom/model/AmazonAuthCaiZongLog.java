package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AmazonAuthCaiZongLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column amazon_auth_cai_zong_log.id
     */
    private Integer id;

    /**
     * appname database column amazon_auth_cai_zong_log.app_name
     */
    private String appName;

    /**
     * 商家id database column amazon_auth_cai_zong_log.selling_partner_id
     */
    private String sellingPartnerId;

    /**
     * 创建时间 database column amazon_auth_cai_zong_log.create_date
     */
    private Timestamp createDate;
}