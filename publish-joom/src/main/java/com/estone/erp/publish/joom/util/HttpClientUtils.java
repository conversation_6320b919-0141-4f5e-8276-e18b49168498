package com.estone.erp.publish.joom.util;

import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

@SuppressWarnings("deprecation")
public class HttpClientUtils
{

    /**
     * 释放资源
     */
    public static void closeQuietly(CloseableHttpResponse response)
    {

        if (response == null)
        {
            return;
        }

        HttpEntity entity = response.getEntity();

        try
        {

            // 关闭流
            if (entity != null && entity.isStreaming())
            {
                InputStream instream = entity.getContent();

                if (instream != null)
                {
                    instream.close();
                }
            }

            // 释放链接
            response.close();

        }
        catch (IOException e)
        {
        }
    }

    /**
     * 释放资源
     */
    public static void closeQuietly(CloseableHttpClient httpClient)
    {

        if (httpClient == null)
        {
            return;
        }

        try
        {
            httpClient.close();
        }
        catch (IOException e)
        {
        }
    }

    public static CloseableHttpClient createClientDefault()
    {
        /* 从连接池中取连接的超时时间 setConnectionRequestTimeout  */

        /* 连接超时 setConnectTimeout */

        /* 请求超时 setConnectionRequestTimeout*/

        RequestConfig defaultRequestConfig = RequestConfig.custom().setConnectionRequestTimeout(2 * 60 * 1000).setSocketTimeout(2 * 60 * 1000).setConnectTimeout(5000).build();

        CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).setMaxConnPerRoute(10).setMaxConnTotal(30).build();

        return httpclient;
    }

    public static CloseableHttpClient createClientMinimal()
    {
        CloseableHttpClient httpclient = HttpClients.createMinimal();
        return httpclient;
    }

    public static CloseableHttpClient createSSLClientDefault()
    {
        try
        {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy()
            {
                // 信任所有
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException
                {
                    return true;
                }
            }).build();
            
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
            
            RequestConfig defaultRequestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(180000)
                    .setSocketTimeout(180000)
                    .setConnectTimeout(180000).build();
            
            return HttpClients.custom().setSSLSocketFactory(sslsf).setDefaultRequestConfig(defaultRequestConfig).build();
        }
        catch (KeyManagementException e)
        {
            e.printStackTrace();
        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
        }
        catch (KeyStoreException e)
        {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

}
