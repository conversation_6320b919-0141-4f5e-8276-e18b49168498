package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.JoomWarehouseMapper;
import com.estone.erp.publish.joom.model.JoomWarehouse;
import com.estone.erp.publish.joom.model.JoomWarehouseCriteria;
import com.estone.erp.publish.joom.model.JoomWarehouseExample;
import com.estone.erp.publish.joom.service.JoomWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> joom_warehouse
 * 2020-10-09 17:18:19
 */
@Service("joomWarehouseService")
@Slf4j
public class JoomWarehouseServiceImpl implements JoomWarehouseService {
    @Resource
    private JoomWarehouseMapper joomWarehouseMapper;

    @Override
    public int countByExample(JoomWarehouseExample example) {
        Assert.notNull(example, "example is null!");
        return joomWarehouseMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomWarehouse> search(CQuery<JoomWarehouseCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomWarehouseCriteria query = cquery.getSearch();
        JoomWarehouseExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomWarehouseMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomWarehouse> joomWarehouses = joomWarehouseMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomWarehouse> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomWarehouses);
        return result;
    }

    @Override
    public JoomWarehouse selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return joomWarehouseMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<JoomWarehouse> selectByExample(JoomWarehouseExample example) {
        Assert.notNull(example, "example is null!");
        return joomWarehouseMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomWarehouse record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        return joomWarehouseMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomWarehouse record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomWarehouseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomWarehouse record, JoomWarehouseExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomWarehouseMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return joomWarehouseMapper.deleteByPrimaryKey(ids);
    }
}