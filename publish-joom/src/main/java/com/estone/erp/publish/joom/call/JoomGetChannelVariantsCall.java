package com.estone.erp.publish.joom.call;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.joom.util.modal.JoomChannelVarant;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取渠道运费（存在未修改过变体 获取不到数据问题暂未启用）
 * @Auther yucm
 * @Date 2020/10/12
 */
@Slf4j
public class JoomGetChannelVariantsCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;
    private static final String PATH = "warehouse/channel-variant/multi-get";
    private static final String DEFAULT_CHARSET = "utf-8";


    public JoomGetChannelVariantsCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    public List<JoomChannelVarant> getChannelVariants(String sku) {
        httpClient = HttpClientUtils.createClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));
        standardNameValue.add(new BasicNameValuePair("sku", sku));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        List<JoomChannelVarant> joomChannelVarants = null;
        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));
            HttpGet httpGet = new HttpGet();
            httpGet.setURI(new URI(url.toString()));

            CloseableHttpResponse httpResponse = null;
            int retryTimes = 0;

            // 重试三次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpGet);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();
                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        joomChannelVarants = this.toJoomChannelVarants(responseJson, joomPmsAccount.getAccountNumber());
                    }
                    else {
                        log.error("JoomGetChannelVariantsCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;
                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes) + "--" + e.getMessage(), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomGetChannelVariantsCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    return null;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            return joomChannelVarants;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return joomChannelVarants;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    /**
     * 返回数据转化为Joom仓库
     * @param responseJsonStr
     * @return
     */
    private static List<JoomChannelVarant> toJoomChannelVarants(String responseJsonStr, String account) {
        List<JoomChannelVarant> joomChannelVarants = new ArrayList<>();

        JSONObject responseJson = JSONObject.parseObject(responseJsonStr);
        if(null == responseJson) {
            return joomChannelVarants;
        }

        JSONArray dataJsonArray = responseJson.getJSONArray("data");
        if(null == dataJsonArray) {
            return joomChannelVarants;
        }

        for (int i = 0 ; i < dataJsonArray.size(); i++) {
            JSONObject dataJsonObject = dataJsonArray.getJSONObject(i);
            JSONObject channelVariantJsonObject = dataJsonObject.getJSONObject("ChannelVariant");
            if(null == channelVariantJsonObject) {
                continue;
            }

            JoomChannelVarant joomChannelVarant = new JoomChannelVarant();
            joomChannelVarants.add(joomChannelVarant);

            joomChannelVarant.setChannelId(channelVariantJsonObject.getString("channel_id"));
            joomChannelVarant.setChannelName(channelVariantJsonObject.getString("channel_name"));
            joomChannelVarant.setVariantId(channelVariantJsonObject.getString("variant_id"));
            joomChannelVarant.setSku(channelVariantJsonObject.getString("sku"));
            joomChannelVarant.setShippingPrice(channelVariantJsonObject.getDouble("shipping_price"));
            joomChannelVarant.setCurrency(channelVariantJsonObject.getString("currency"));
        }

        return joomChannelVarants;
    }
}
