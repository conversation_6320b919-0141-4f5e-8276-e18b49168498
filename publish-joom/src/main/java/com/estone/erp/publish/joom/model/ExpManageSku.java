package com.estone.erp.publish.joom.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class ExpManageSku implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column exp_manage_sku.id
     */
    private Integer id;

    /**
     * sku database column exp_manage_sku.sku
     */
    private String sku;

    /**
     * 批次号 database column exp_manage_sku.batch_no
     */
    private String batchNo;

    /**
     * 来源 database column exp_manage_sku.source
     */
    private Integer source;

    /**
     * 生产日期 database column exp_manage_sku.pro_date
     */
    private Timestamp proDate;

    /**
     * 保质期天数 database column exp_manage_sku.days
     */
    private Integer days;

    /**
     * 到期时间 database column exp_manage_sku.exp_date
     */
    private Timestamp expDate;

    /**
     * 剩余月份 database column exp_manage_sku.remaining_months
     */
    private Double remainingMonths;

    /**
     * 在线库存 database column exp_manage_sku.quantity
     */
    private Integer quantity;

    /**
     * 外借在途 database column exp_manage_sku.lend_on_way_quantity
     */
    private Integer lendOnWayQuantity;

    /**
     * 采购入库 database column exp_manage_sku.check_in_quantity
     */
    private Integer checkInQuantity;

    /**
     * 调拨入库 database column exp_manage_sku.allocation_in_quantity
     */
    private Integer allocationInQuantity;

    /**
     * 退件入库 database column exp_manage_sku.return_quantity
     */
    private Integer returnQuantity;

    /**
     * 订单出库 database column exp_manage_sku.deliver_quantity
     */
    private Integer deliverQuantity;

    /**
     * 报废出库 database column exp_manage_sku.scrap_quantity
     */
    private Integer scrapQuantity;

    /**
     * 不良品出库 database column exp_manage_sku.bad_product_quantity
     */
    private Integer badProductQuantity;

    /**
     * 调拨出库 database column exp_manage_sku.allocation_out_quantity
     */
    private Integer allocationOutQuantity;

    /**
     * 盘点 database column exp_manage_sku.inventory_quantity
     */
    private Integer inventoryQuantity;

    /**
     * 状态 database column exp_manage_sku.push_publish_state
     */
    private Integer pushPublishState;

    /**
     * 状态修改时间 database column exp_manage_sku.flag_update_date
     */
    private Timestamp flagUpdateDate;

    /**
     * 仓库同步时间 database column exp_manage_sku.sync_time
     */
    private Timestamp syncTime;

    /**
     * 创建时间 database column exp_manage_sku.created_time
     */
    private Timestamp createdTime;

    /**
     * 修改时间 database column exp_manage_sku.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 修改时间 database column exp_manage_sku.updated_time
     */
    private Timestamp deletedTime;
}