package com.estone.erp.publish.joom.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class MapUtils {
    public static Integer getInteger(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        if (value != null)
        {
            return Integer.valueOf(value.toString());
        }

        return null;
    }

    public static String getString(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        if (value != null)
        {
            return value.toString();
        }

        return null;
    }

    public static Double getDouble(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        if (value != null)
        {
            return Double.valueOf(value.toString());
        }

        return null;
    }

    public static Boolean getBoolean(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        if (value != null)
        {
            return Boolean.valueOf(value.toString());
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    public static String getCurrency(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        Map<String, Object> amountParam = (Map<String, Object>) value;

        return getString(amountParam, "currencyCode");
    }

    @SuppressWarnings("unchecked")
    public static Double getAmount(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        Map<String, Object> amountParam = (Map<String, Object>) value;

        return getDouble(amountParam, "amount");
    }

    public static Timestamp getTimestamp(Map<String, Object> param, String key, SimpleDateFormat gmtSdf)
    {
        if (param == null)
        {
            return null;
        }

        if (gmtSdf == null)
        {
            gmtSdf = new SimpleDateFormat("yyyyMMddHHmmssS");
        }

        Object value = param.get(key);

        if (value != null)
        {
            try
            {
                Date parse = gmtSdf.parse(value.toString());
                return new Timestamp(parse.getTime());
            }
            catch (Exception e)
            {
                return null;
            }
        }

        return null;
    }

    public static Timestamp getTimestampByStyle(Map<String, Object> param, String key, String timeStyle)
    {
        if (param == null)
        {
            return null;
        }

        SimpleDateFormat sdf = null;

        if (timeStyle == null)
        {
            sdf = new SimpleDateFormat("yyyyMMddHHmmssS");
        }
        else
        {
            sdf = new SimpleDateFormat(timeStyle);
        }

        Object value = param.get(key);

        if (value != null)
        {
            try
            {
                Date parse = sdf.parse(value.toString());
                return new Timestamp(parse.getTime());
            }
            catch (Exception e)
            {
                return null;
            }
        }

        return null;
    }

    
    /**
     * 时间为long 类型 转换
     * @param param
     * @param key
     * @param gmtSdf
     * @return
     */
    public static Timestamp getTimestampForLong(Map<String, Object> param, String key)
    {
        if (param == null)
        {
            return null;
        }

        Object value = param.get(key);

        if (value != null)
        {
            try
            {
                long long_time = (long) value;
                return new Timestamp(long_time);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        return null;
    }
}
