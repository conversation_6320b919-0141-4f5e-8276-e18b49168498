
package com.estone.erp.publish.joom.util;

import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import com.estone.erp.publish.common.util.TortUtils;
import org.apache.commons.lang.StringUtils;

public class ProductImgUtils {

    /**
     * 获取图片像素
     * 
     * @param url
     */
    public static Img getImgPixel(String url) {

        if (StringUtils.isNotBlank(url) && StringUtils.indexOf(url, ".") != -1) {

            BufferedImage bi = null;
            try {
                bi = ImageIO.read(new URL(url));
            }
            catch (Exception e) {
                return null;
            }

            if (bi != null) {

                int width = bi.getWidth();
                int height = bi.getHeight();

                Img img = new Img();
                img.setHeight(height);
                img.setWidth(width);
                img.setUrl(url);

                return img;
            }
        }
        return null;
    }

    public static class Img {
        private int width;
        private int height;
        private String url;

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public static void main(String[] args) {
        String enTitle = "<div>aa c bb cc daa</div>";
        String oldWord = "aa";
        String newWord = "dd";

        Map<String,String> specialSignMap = new HashMap<String,String>(){
            {
                super.put("<", " < ");
                super.put(">", "> ");
                super.put("\n", " \n ");
                super.put("\r", " \r ");
                super.put("&nbsp", " &nbsp ");
            }
        };
        for (Map.Entry<String, String> map : specialSignMap.entrySet()) {
            String key = map.getKey();
            String value = map.getValue();
            enTitle = enTitle.replaceAll(key, value);
        }

        enTitle = TortUtils.replace(enTitle, oldWord, newWord, true);

        for (Map.Entry<String, String> map : specialSignMap.entrySet()) {
            String key = map.getKey();
            String value = map.getValue();
            enTitle = enTitle.replaceAll(value, key);
        }
        System.out.println(enTitle);
    }

}
