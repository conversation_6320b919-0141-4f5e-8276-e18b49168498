package com.estone.erp.publish.joom.call;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.joom.util.modal.JoomItemVariantCountyShipping;
import com.estone.erp.publish.joom.util.modal.JoomShippingRegion;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2020/10/8
 */
@Slf4j
public class JoomGetCountyShippingCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;
    private static final String PATH = "products/shipping";
    private static final String DEFAULT_CHARSET = "utf-8";

    public JoomGetCountyShippingCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    public List<JoomItemVariantCountyShipping> getCountyShipping(String joomItemId) {
        httpClient = HttpClientUtils.createClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));
        standardNameValue.add(new BasicNameValuePair("id", joomItemId));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT_V3).append(PATH);

        List<JoomItemVariantCountyShipping>  joomItemVariantCountyShippings = null;
        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));

            HttpGet httpGet = new HttpGet();
            httpGet.setURI(new URI(url.toString()));

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            // 重试三次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpGet);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();
                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        joomItemVariantCountyShippings = this.toItemVariantCountyShipping(responseJson, joomItemId);
                    }
                    else {
                        log.error("JoomGetCountyShippingCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;
                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes) + "--" + e.getMessage(), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomGetCountyShippingCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    return null;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            return joomItemVariantCountyShippings;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return joomItemVariantCountyShippings;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    /**
     * 转化国家运费
     * @param responseJsonStr
     * @return
     */
    private List<JoomItemVariantCountyShipping> toItemVariantCountyShipping(String responseJsonStr, String joomItemId) {
        List<JoomItemVariantCountyShipping> joomItemVariantCountyShippings = new ArrayList<>();

        JSONObject responseJson = JSONObject.parseObject(responseJsonStr);
        if(null == responseJson) {
            return joomItemVariantCountyShippings;
        }

        JSONObject dataJsonObject = responseJson.getJSONObject("data");
        if(null == dataJsonObject) {
            return joomItemVariantCountyShippings;
        }

        JSONArray variantsJsonArray = dataJsonObject.getJSONArray("variants");
        if(null == variantsJsonArray) {
            return joomItemVariantCountyShippings;
        }

        for (int i = 0 ; i < variantsJsonArray.size(); i++) {
            JSONObject variantJsonObject = variantsJsonArray.getJSONObject(i);
            if(null == variantJsonObject) {
                continue;
            }

            JoomItemVariantCountyShipping joomItemVariantCountyShipping = new JoomItemVariantCountyShipping();
            joomItemVariantCountyShipping.setJoomItemId(joomItemId);
            joomItemVariantCountyShipping.setVariantSku(variantJsonObject.getString("sku"));
            joomItemVariantCountyShipping.setVariantId(variantJsonObject.getString("id"));
            joomItemVariantCountyShipping.setCurrency(variantJsonObject.getString("currency"));
            joomItemVariantCountyShipping.setDefaultShippingPrice(variantJsonObject.getDouble("defaultShippingPrice"));

            JSONArray shippingRegionsJsonArray = variantJsonObject.getJSONArray("shippingRegions");
            if(null == shippingRegionsJsonArray) {
                continue;
            }

            List<JoomShippingRegion> joomShippingRegions = new ArrayList<>();
            joomItemVariantCountyShipping.setJoomShippingRegions(joomShippingRegions);

            for (int j = 0 ; j < shippingRegionsJsonArray.size(); j++) {
                JSONObject shippingJsonObject = shippingRegionsJsonArray.getJSONObject(j);
                if(null == shippingJsonObject) {
                    continue;
                }

                JoomShippingRegion joomShippingRegion = new JoomShippingRegion();
                joomShippingRegions.add(joomShippingRegion);
                joomShippingRegion.setCountryCode(shippingJsonObject.getString("countryCode"));
                joomShippingRegion.setType(shippingJsonObject.getString("type"));
                joomShippingRegion.setPrice(shippingJsonObject.getDouble("price"));

                JSONObject detailsJsonObject = shippingJsonObject.getJSONObject("details");
                if(null != detailsJsonObject) {
                    joomShippingRegion.setMinPrice(detailsJsonObject.getDouble("minPrice"));
                    joomShippingRegion.setMessage(detailsJsonObject.getString("message"));
                }
            }
            joomItemVariantCountyShippings.add(joomItemVariantCountyShipping);
        }

        return joomItemVariantCountyShippings;
    }
}
