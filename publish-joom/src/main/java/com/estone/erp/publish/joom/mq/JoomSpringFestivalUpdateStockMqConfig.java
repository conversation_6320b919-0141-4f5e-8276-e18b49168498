package com.estone.erp.publish.joom.mq;


import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class JoomSpringFestivalUpdateStockMqConfig {

    private int joomSpringFestivalUpdateStockMqConsumers;
    private int joomSpringFestivalUpdateStockMqPrefetchCount;
    private boolean joomSpringFestivalUpdateStockMqListener;

    @Bean
    public Queue joomSpringFestivalUpdateStock() {
        return new Queue(PublishQueues.JOOM_SPRING_FESTIVAL_UPDATE_STOCK_QUEUE,true);
    }

    @Bean
    public Binding joomSpringFestivalUpdateStockBinding() {
        return new Binding(PublishQueues.JOOM_SPRING_FESTIVAL_UPDATE_STOCK_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.JOOM_API_DIRECT_EXCHANGE,
                PublishQueues.JOOM_SPRING_FESTIVAL_UPDATE_STOCK_KEY, null);
    }

    @Bean
    public JoomSpringFestivalUpdateStockMqListener joomSpringFestivalUpdateStockMqListener() {
        return new JoomSpringFestivalUpdateStockMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer joomSpringFestivalUpdateStockListenerContainer(
            JoomSpringFestivalUpdateStockMqListener joomSpringFestivalUpdateStockMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.JOOM_SPRING_FESTIVAL_UPDATE_STOCK_QUEUE, joomSpringFestivalUpdateStockMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (joomSpringFestivalUpdateStockMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(joomSpringFestivalUpdateStockMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(joomSpringFestivalUpdateStockMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
