package com.estone.erp.publish.joom.api;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.call.JoomCreateItemCall;
import com.estone.erp.publish.joom.call.JoomCreateVariationItemCall;
import com.estone.erp.publish.joom.call.JoomGetItemCall;
import com.estone.erp.publish.joom.call.JoomUpdateVariationCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomTemplate;
import com.estone.erp.publish.joom.service.JoomTemplateService;
import com.estone.erp.publish.joom.util.JoomTemplateUtils;
import com.estone.erp.publish.joom.util.modal.JoomSku;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class JoomApi {

    public static ResponseJson createJoomItem(SaleAccountAndBusinessResponse joomPmsAccount, JoomTemplate joomTemplate) {

        JoomTemplateService joomTemplateService = SpringUtils.getBean("joomTemplateService", JoomTemplateService.class);

        ResponseJson resultRsp = new ResponseJson();
        resultRsp.setStatus(StatusCode.FAIL);
        
        //状态检查
        String ckeckTemplateSku = JoomTemplateUtils.ckeckTemplateSku(joomTemplate);
        if(StringUtils.isNotBlank(ckeckTemplateSku)){
        	 resultRsp.setMessage("[刊登失败]" + ckeckTemplateSku);
        	 joomTemplate.setContent("[刊登失败]" + ckeckTemplateSku);
        	 // 修改模板
             joomTemplateService.updateByPrimaryKeySelective(joomTemplate);
             return resultRsp;
        }
        //校验价格是否小于等于0
        if (ObjectUtils.isEmpty(joomTemplate.getPrice()) || joomTemplate.getPrice() <= 0) {
             resultRsp.setMessage("[刊登失败]价格必须大于0");
        }

        //检查该账号是否重复刊登
        String sellerId = joomPmsAccount.getAccountNumber();
        boolean checkSkuRepeat = JoomTemplateUtils.checkSkuRepeat(sellerId, joomTemplate);        
        if(checkSkuRepeat){
            resultRsp.setMessage("重复刊登！");
            return resultRsp;
        }

        //获取所有货号
        Set<String> articleNumberSet = new HashSet<>();
        articleNumberSet.add(joomTemplate.getSku());
        List<JoomSku> joomSkus = joomTemplate.getJoomSkus();
        if(CollectionUtils.isNotEmpty(joomSkus)){
            for (JoomSku skus : joomSkus) {
                articleNumberSet.add(skus.getSku());
            }
        }
        
        //返回 这一步已经换了随机sku了
        JoomTemplate ckeckTemplate = JoomTemplateUtils.ckeckTemplate(joomPmsAccount, joomTemplate);
        if(ckeckTemplate == null){
            resultRsp.setMessage("系统异常！");
            return resultRsp;
        }
        
        
        //使用检查的模板刊登
        JoomItem joomItem = null;
        try {
            joomItem = JoomTemplateUtils.templateToItem(joomPmsAccount, ckeckTemplate, new ArrayList<>(articleNumberSet));
        } catch (Exception e) {
            resultRsp.setMessage(e.getMessage());
            return resultRsp;
        }
        //校验另2家公司刊登图片750*750是否失败
        /*if(JoomCommonUtils.hasFailImage(joomItem)) {
        	 resultRsp.setMessage("[刊登失败]图片上传失败！");
        	 return resultRsp;
        }*/
        //修改 因为 刊登变化了 标题主图，sku 
        JoomTemplate updateJoomTemplate = new JoomTemplate();
        updateJoomTemplate.setTemplateId(joomTemplate.getTemplateId());
        updateJoomTemplate.setIsLock(false);
        updateJoomTemplate.setName(joomTemplate.getName());
        
        // 刊登父属性
        JoomCreateItemCall call = new JoomCreateItemCall(joomPmsAccount);
        
        List<JoomItem> variations = joomItem.getVariations();
     
        JoomItem childJoomItem = null;
        if (CollectionUtils.isNotEmpty(variations)) {
        	childJoomItem = variations.get(0);
        }
        ResponseJson responseJson = call.createItem(joomItem, childJoomItem);
        resultRsp.setStatus(responseJson.getStatus());
        resultRsp.setLocation(joomItem.getSku());
        resultRsp.setMessage(responseJson.getMessage());
        
        // 成功后刊登子属性
        if (StatusCode.SUCCESS.equals(responseJson.getStatus())) {
            
            if (CollectionUtils.isNotEmpty(variations)) {
                for (JoomItem variationsJoomItem : variations) {
                	// 判断变体是否已存在
                	if (childJoomItem != null && childJoomItem == variationsJoomItem){
                			continue;
                	}
                	
                    JoomCreateVariationItemCall variationCall = new JoomCreateVariationItemCall(joomPmsAccount);
                    responseJson = variationCall.createVariation(variationsJoomItem);

                    resultRsp.addError(
                            new ResponseError(responseJson.getStatus(), joomItem.getSku(), responseJson.getMessage()));

                    if (responseJson.getStatus().equals(StatusCode.SUCCESS)
                            && (joomItem.getIsEnabled() == null || !joomItem.getIsEnabled())) {
                        try {
                            JoomUpdateVariationCall updateItemCall = new JoomUpdateVariationCall(joomPmsAccount);
                            updateItemCall.updateVariation(variationsJoomItem);
                        }
                        catch (Exception e) {
                            log.warn("update enabled error :" + e.getMessage());
                        }
                    }
                }
            }

            JoomGetItemCall getItemCall = new JoomGetItemCall(joomPmsAccount);
            // 刊登成功后同步到系统
            List<JoomItem> list = getItemCall.getJoomItems(null, joomItem.getSku());
            String joomItemId = null;
            if (CollectionUtils.isNotEmpty(list)) {
                joomItemId = list.get(0).getJoomItemId();
                resultRsp.setMessage("刊登成功，平台id:" + joomItemId); // 成功的模版编号
            }
            else {
                resultRsp.setMessage("刊登成功，同步产品失败，请稍后自动或手动同步！");
            }

            /*刊登成功为1*/
            updateJoomTemplate.setStatus(1);
            updateJoomTemplate.setContent("[刊登成功]" + joomItemId);
        }
        else {
            /*刊登成功为2*/
            updateJoomTemplate.setStatus(2);
            updateJoomTemplate.setContent("[刊登失败]" + responseJson.getMessage());
            
        }
        updateJoomTemplate.setSellerId(sellerId);
        // 修改模板
        joomTemplateService.updateByPrimaryKeySelective(updateJoomTemplate);

        return resultRsp;
    }

}
