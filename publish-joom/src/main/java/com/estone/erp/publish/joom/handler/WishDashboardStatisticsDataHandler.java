package com.estone.erp.publish.joom.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.Constant;
import com.estone.erp.publish.common.context.DashboardStatistContext;
import com.estone.erp.publish.common.model.dto.PublishStatisticsConfigDO;
import com.estone.erp.publish.component.AbstractDashboardStatisticsDataHandler;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.pmssalePublicData.client.PublishWishClient;
import com.estone.erp.publish.system.pmssalePublicData.model.WishRequest;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class WishDashboardStatisticsDataHandler extends AbstractDashboardStatisticsDataHandler {

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private PublishWishClient publishWishClient;

    @Override
    public String getPlatform() {
        return SaleChannelEnum.WISH.getChannelName();
    }

    /**
     * 补充特殊链接数据
     *
     * @param data    统计数据
     */
    @Override
    protected void assemblyListingData(DashboardStatistContext context, EsSalesStatisticsData data) {
        List<String> accountNumberList = context.getAccountNumberList();
        Integer stockThreshold = context.getStockThreshold();
        // listing链接指数 侵权禁售链接数,库存不足链接数,包含侵权词链接

        int onlineListingNum = 0;
        int forbiddenListingNum = 0;
        int stopStatusListingNum = 0;
        int notEnoughStockListingNum = 0;

        ApiResult<Integer> onlineListingNumResult = publishWishClient.getOnlineListingNum(accountNumberList);
        if(!onlineListingNumResult.isSuccess()){
            log.error("wish首页接口调用异常" + onlineListingNumResult.getErrorMsg());
        }else{
            onlineListingNum = onlineListingNumResult.getResult();
        }

        ApiResult<Integer> forbiddenListingNumResult = publishWishClient.getForbiddenListingNum(accountNumberList);
        if(!forbiddenListingNumResult.isSuccess()){
            log.error("wish首页接口调用异常" + forbiddenListingNumResult.getErrorMsg());
        }else{
            forbiddenListingNum = forbiddenListingNumResult.getResult();
        }

        ApiResult<Integer> stopStatusListingNumResult = publishWishClient.getStopStatusListingNum(accountNumberList);
        if(!stopStatusListingNumResult.isSuccess()){
            log.error("wish首页接口调用异常" + stopStatusListingNumResult.getErrorMsg());
        }else{
            stopStatusListingNum = stopStatusListingNumResult.getResult();
        }

        WishRequest request = new WishRequest();
        request.setAccountNumberList(accountNumberList);
        request.setStockThreshold(stockThreshold);
        ApiResult<Integer> notEnoughStockListingNumResult = publishWishClient.getNotEnoughStockListingNum(request);
        if(!notEnoughStockListingNumResult.isSuccess()){
            log.error("wish首页接口调用异常" + notEnoughStockListingNumResult.getErrorMsg());
        }else{
            notEnoughStockListingNum = notEnoughStockListingNumResult.getResult();
        }

        data.setOnlineListingNum(onlineListingNum);
        data.setForbiddenListingNum(forbiddenListingNum);
        data.setStopStatusListingNum(stopStatusListingNum);
        data.setStockThreshold(stockThreshold);
        data.setNotEnoughStockListingNum(notEnoughStockListingNum);
    }

    /**
     * 设置特殊链接的阈值
     *
     * @param context context
     */
    @Override
    public void setListingThreshold(DashboardStatistContext context) {
        PublishStatisticsConfigDO publishStatisticsConfigDO = getPublishStatisticsConfigDO();
        if (Objects.nonNull(publishStatisticsConfigDO.getGrossThreshold())) {
            context.setGrossThreshold(publishStatisticsConfigDO.getGrossThreshold());
        }
        if (Objects.nonNull(publishStatisticsConfigDO.getStockThreshold())) {
            context.setStockThreshold(publishStatisticsConfigDO.getStockThreshold());
        }
    }

    /**
     * 设置店铺目标总值
     *
     * @param context context
     */
    @Override
    protected void setAccountConfigTargetData(DashboardStatistContext context, EsSalesStatisticsData data) {
        int count = 1;
        List<String> accountNumberList = context.getAccountNumberList();
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            count = accountNumberList.size();
        }
        PublishStatisticsConfigDO publishStatisticsConfigDO = getPublishStatisticsConfigDO();
        if (Objects.nonNull(publishStatisticsConfigDO.getMonthSaleTarget())) {
            data.setMonthSaleTarget(publishStatisticsConfigDO.getMonthSaleTarget() * count);
        }
        if (Objects.nonNull(publishStatisticsConfigDO.getMonthAddListingTarget())) {
            data.setMonthAddListingTarget(publishStatisticsConfigDO.getMonthAddListingTarget() * count);
        }
    }

    private PublishStatisticsConfigDO getPublishStatisticsConfigDO() {
        try {
            SystemParam systemParam = systemParamService.queryParamValue(SaleChannelEnum.WISH.getChannelName(), Constant.STATISTICS, Constant.CONFIG);
            if(systemParam != null) {
                String value = systemParam.getParamValue();
                return JSON.parseObject(value, PublishStatisticsConfigDO.class);
            }
        }catch(Exception e){
            XxlJobLogger.log("获取首页统计配置异常", e);
            log.error(e.getMessage(), e);
        }
        return new PublishStatisticsConfigDO();
    }

    /**
     * 获取指定时间段内的店铺新增链接数
     *
     * @return 新增链接数
     */
    @Override
    protected Long getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        WishRequest request = new WishRequest();
        request.setAccountNumber(accountNumber);
        request.setStarTime(starTime);
        request.setEndTime(endTime);
        Long count = 0L;
        ApiResult<Integer> result = publishWishClient.getRangeTimeAddListingTotal(request);
        if(!result.isSuccess()){
            log.error("wish首页接口调用异常" + result.getErrorMsg());
        }else{
            count = Long.valueOf(result.getResult());
        }
        return count;
    }

}
