package com.estone.erp.publish.joom.mapper;


import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLog;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLogExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AmazonAuthCaiZongLogMapper {
    int countByExample(AmazonAuthCaiZongLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(AmazonAuthCaiZongLog record);

    AmazonAuthCaiZongLog selectByPrimaryKey(Integer id);

    List<AmazonAuthCaiZongLog> selectByExample(AmazonAuthCaiZongLogExample example);

    int updateByExampleSelective(@Param("record") AmazonAuthCaiZongLog record, @Param("example") AmazonAuthCaiZongLogExample example);

    int updateByPrimaryKeySelective(AmazonAuthCaiZongLog record);
}