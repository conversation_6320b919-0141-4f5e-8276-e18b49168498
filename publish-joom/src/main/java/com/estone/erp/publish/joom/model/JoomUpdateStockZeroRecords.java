package com.estone.erp.publish.joom.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class JoomUpdateStockZeroRecords implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 店铺
     */
    private String account;

    /**
     * 平台id
     */
    private String itemId;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 单品状态
     */
    private String skuStatus;

    /**
     * 改前值
     */
    private String beforeValue;

    /**
     * 改后值
     */
    private String afterValue;

    /**
     * 结果状态，0-失败，1-成功
     */
    private Integer resultStauts;

    /**
     * 执行信息
     */
    private String resultMsg;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 执行状态，0-年前修改库存，1-恢复库存
     */
    private Integer execStatus;

    /**
     * 平台ID
     */
    private String joomItemId;
}