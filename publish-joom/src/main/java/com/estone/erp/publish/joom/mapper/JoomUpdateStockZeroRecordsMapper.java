package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecords;
import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecordsExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JoomUpdateStockZeroRecordsMapper {
    int countByExample(JoomUpdateStockZeroRecordsExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(JoomUpdateStockZeroRecords record);

    JoomUpdateStockZeroRecords selectByPrimaryKey(Integer id);

    List<JoomUpdateStockZeroRecords> selectByExample(JoomUpdateStockZeroRecordsExample example);

    int updateByExampleSelective(@Param("record") JoomUpdateStockZeroRecords record, @Param("example") JoomUpdateStockZeroRecordsExample example);

    int updateByPrimaryKeySelective(JoomUpdateStockZeroRecords record);

    List<JoomUpdateStockZeroRecords> selectMinCreateTimeRecordList(@Param("accountList")List<String> accountList, @Param("articleNumberList")List<String> articleNumberList, @Param("id")Integer lastId, @Param("pageSize") Integer pageSize);
}