package com.estone.erp.publish.joom.api.thread;

import org.apache.commons.lang.StringUtils;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.joom.call.JoomGetItemCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SynchJoomItemThread implements Runnable {

    private JoomItem joomItem;

    public SynchJoomItemThread(JoomItem joomItem) {
        super();
        this.joomItem = joomItem;
    }

    @Override
    public void run() {

        if (StringUtils.isBlank(joomItem.getItemSeller())) {
            return;
        }

        try {
            SaleAccountAndBusinessResponse joomPmsAccount = 
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, joomItem.getItemSeller());

            JoomGetItemCall call = new JoomGetItemCall(joomPmsAccount);

            call.getJoomItems(joomItem.getJoomItemId(), null);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }
}
