package com.estone.erp.publish.joom.model;

import com.estone.erp.common.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> joom_category_desc_config
 * 2022-11-09 17:35:02
 */
public class JoomCategoryDescConfigCriteria extends JoomCategoryDescConfig {
    private static final long serialVersionUID = 1L;

    public JoomCategoryDescConfigExample getExample() {
        JoomCategoryDescConfigExample example = new JoomCategoryDescConfigExample();
        JoomCategoryDescConfigExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getTwoCategoryCn())) {
            criteria.andTwoCategoryCnEqualTo(this.getTwoCategoryCn());
        }
        if (StringUtils.isNotBlank(this.getLeafCategoryEn())) {
            criteria.andLeafCategoryEnEqualTo(this.getLeafCategoryEn());
        }
        if (StringUtils.isNotBlank(this.getLeafCategoryCn())) {
            criteria.andLeafCategoryCnEqualTo(this.getLeafCategoryCn());
        }
        if (StringUtils.isNotBlank(this.getLeafCategoryCode())) {
            criteria.andLeafCategoryCodeEqualTo(this.getLeafCategoryCode());
        }
        if (StringUtils.isNotBlank(this.getFullCategoryCode())) {
            List<String> strings = CommonUtils.splitList(this.getFullCategoryCode(), ",");
            criteria.andFullCategoryCodeIn(strings);
        }
        if (StringUtils.isNotBlank(this.getAddDesc())) {
//            criteria.andAddDescEqualTo(this.getAddDesc());
            criteria.andAddDescLike("%" + this.getAddDesc() + "%");
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        return example;
    }
}