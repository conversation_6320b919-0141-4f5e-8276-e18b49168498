package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.AmazonAuthCaiZongLogMapper;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLog;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLogCriteria;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLogExample;
import com.estone.erp.publish.joom.service.AmazonAuthCaiZongLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-07 09:37:29
 */
@Service("amazonAuthCaiZongLogService")
@Slf4j
public class AmazonAuthCaiZongLogServiceImpl implements AmazonAuthCaiZongLogService {
    @Resource
    private AmazonAuthCaiZongLogMapper amazonAuthCaiZongLogMapper;

    @Override
    public int countByExample(AmazonAuthCaiZongLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonAuthCaiZongLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonAuthCaiZongLog> search(CQuery<AmazonAuthCaiZongLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonAuthCaiZongLogCriteria query = cquery.getSearch();
        AmazonAuthCaiZongLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonAuthCaiZongLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonAuthCaiZongLog> amazonAuthCaiZongLogs = amazonAuthCaiZongLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonAuthCaiZongLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonAuthCaiZongLogs);
        return result;
    }

    @Override
    public AmazonAuthCaiZongLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonAuthCaiZongLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonAuthCaiZongLog> selectByExample(AmazonAuthCaiZongLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonAuthCaiZongLogMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonAuthCaiZongLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonAuthCaiZongLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonAuthCaiZongLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonAuthCaiZongLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonAuthCaiZongLog record, AmazonAuthCaiZongLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonAuthCaiZongLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonAuthCaiZongLogMapper.deleteByPrimaryKey(ids);
    }
}