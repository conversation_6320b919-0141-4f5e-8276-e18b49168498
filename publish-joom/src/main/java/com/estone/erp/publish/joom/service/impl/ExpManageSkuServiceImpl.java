package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.ExpManageSkuMapper;
import com.estone.erp.publish.joom.model.ExpManageSku;
import com.estone.erp.publish.joom.model.ExpManageSkuCriteria;
import com.estone.erp.publish.joom.model.ExpManageSkuExample;
import com.estone.erp.publish.joom.service.ExpManageSkuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> exp_manage_sku
 * 2022-11-29 15:18:11
 */
@Service("expManageSkuService")
@Slf4j
public class ExpManageSkuServiceImpl implements ExpManageSkuService {
    @Resource
    private ExpManageSkuMapper expManageSkuMapper;

    @Override
    public int countByExample(ExpManageSkuExample example) {
        Assert.notNull(example, "example is null!");
        return expManageSkuMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ExpManageSku> search(CQuery<ExpManageSkuCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ExpManageSkuCriteria query = cquery.getSearch();
        ExpManageSkuExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = expManageSkuMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset((cquery.getOffset() - 1) * cquery.getOffset());
        }

        List<ExpManageSku> expManageSkus = expManageSkuMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ExpManageSku> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(expManageSkus);
        return result;
    }

    @Override
    public ExpManageSku selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return expManageSkuMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ExpManageSku> selectByExample(ExpManageSkuExample example) {
        Assert.notNull(example, "example is null!");
        return expManageSkuMapper.selectByExample(example);
    }

    @Override
    public int insert(ExpManageSku record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return expManageSkuMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExpManageSku record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return expManageSkuMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ExpManageSku record, ExpManageSkuExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return expManageSkuMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return expManageSkuMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ApiResult<List<String>> expSkus() {
        List<String> expSkus = expManageSkuMapper.listSkuByExpSkus();
        return ApiResult.newSuccess(expSkus);
    }

    @Override
    public List<ExpManageSku> searchExistRecord(List<String> skuList, List<String> batchNos) {
        if (CollectionUtils.isEmpty(skuList) && CollectionUtils.isEmpty(batchNos)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        ExpManageSkuExample expManageSkuExample = new ExpManageSkuExample();
        ExpManageSkuExample.Criteria criteria = expManageSkuExample.createCriteria();
        if (CollectionUtils.isNotEmpty(batchNos)) {
            criteria.andBatchNoIn(batchNos);
        }
        if (CollectionUtils.isNotEmpty(skuList)) {
            criteria.andSkuIn(skuList);
        }
        return expManageSkuMapper.searchExistRecord(expManageSkuExample);
    }

    @Override
    public void batchUpdateRecord(List<ExpManageSku> updateRecord) {
        if (CollectionUtils.isEmpty(updateRecord)) {
            return;
        }
        expManageSkuMapper.batchUpdateRecord(updateRecord);
    }

    @Override
    public void batchInsertRecord(List<ExpManageSku> insertRecord) {
        if (CollectionUtils.isEmpty(insertRecord)) {
            return;
        }
        expManageSkuMapper.batchInsertRecord(insertRecord);
    }

    @Override
    public List<ExpManageSku> selectIdByExample(ExpManageSkuExample example) {
        return expManageSkuMapper.selectIdByExample(example);
    }

    @Override
    public void batchResumePromotion(List<ExpManageSku> expManageSkuList) {
        expManageSkuMapper.batchResumePromotion(expManageSkuList);
    }
}