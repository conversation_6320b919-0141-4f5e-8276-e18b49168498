package com.estone.erp.publish.joom.call;

import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import javax.net.ssl.SSLHandshakeException;

import org.apache.commons.lang.StringUtils;
import org.apache.http.conn.ConnectTimeoutException;

import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

public class AbstrachJoomCall {
    protected static final String ENDPOINT = "https://api-merchant.joom.com/api/v2/";
    protected static final String ENDPOINT_V3 = "https://api-merchant.joom.com/api/v3/";

    protected SaleAccountAndBusinessResponse joomPmsAccount;

    public AbstrachJoomCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        this.joomPmsAccount = joomPmsAccount;
    }

    /**
     * 检查是否重试异常
     * 
     * <p>
     *
     * @param e
     * @return
     * @return boolean
     */
    protected boolean isNeedRetry(Exception e) {
        if ((e instanceof SocketTimeoutException || e instanceof ConnectTimeoutException
                || e instanceof UnknownHostException || e instanceof SSLHandshakeException)) {
            return true;
        }

        String[] exceptionMessages = new String[] { "timed out",
                "The requested data is currently not available due to an eBay system error", "Premature end of file",
                "Web Service framework internal error", "Failed to read a response",
                "fail to get xml string from SOAP message", "Unsupported Content-Type" };

        for (int i = 0, len = exceptionMessages.length; i < len; i++) {
            if (StringUtils.containsIgnoreCase(e.getMessage(), exceptionMessages[i])) {
                return true;
            }
        }

        return false;
    }
}
