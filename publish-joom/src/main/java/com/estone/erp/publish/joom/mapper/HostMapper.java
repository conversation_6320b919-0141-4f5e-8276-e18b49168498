package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.Host;
import com.estone.erp.publish.joom.model.HostExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HostMapper {
    int countByExample(HostExample example);

    int deleteByExample(HostExample example);

    int deleteByPrimaryKey(Integer hostId);

    int insert(Host record);

    int insertSelective(Host record);

    List<Host> selectByExample(HostExample example);

    Host selectByPrimaryKey(Integer hostId);

    int updateByExampleSelective(@Param("record") Host record, @Param("example") HostExample example);

    int updateByExample(@Param("record") Host record, @Param("example") HostExample example);

    int updateByPrimaryKeySelective(Host record);

    int updateByPrimaryKey(Host record);

    List<String> selectHostSiteList();
}