package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.joom.enums.DataViewStatusEnum;
import com.estone.erp.publish.joom.enums.DataViewTypeEnum;
import com.estone.erp.publish.joom.mapper.JoomDataViewMapper;
import com.estone.erp.publish.joom.mapper.custom.CustomJoomDataViewMapper;
import com.estone.erp.publish.joom.model.JoomDataView;
import com.estone.erp.publish.joom.model.JoomDataViewCriteria;
import com.estone.erp.publish.joom.model.JoomDataViewExample;
import com.estone.erp.publish.joom.service.JoomDataViewService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> joom_data_view
 * 2020-04-15 18:24:07
 */
@Service("joomDataViewService")
@Slf4j
public class JoomDataViewServiceImpl implements JoomDataViewService {
    @Resource
    private JoomDataViewMapper joomDataViewMapper;

    @Resource
    private CustomJoomDataViewMapper customJoomDataViewMapper;

    @Override
    public int countByExample(JoomDataViewExample example) {
        Assert.notNull(example, "example is null!");
        return joomDataViewMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomDataView> searchOrig(CQuery<JoomDataViewCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomDataViewCriteria query = cquery.getSearch();
        JoomDataViewExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomDataViewMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomDataView> joomDataViews = joomDataViewMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomDataView> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomDataViews);
        return result;
    }

    @Override
    public CQueryResult<JoomDataView> search(CQuery<JoomDataViewCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomDataViewCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        CQueryResult<JoomDataView> cQueryResult = new CQueryResult<>();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_JOOM);
        if (!superAdminOrEquivalent.isSuccess()) {
            cQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return cQueryResult;
        }
        String accountNumber = query.getAccountNumber();
        String saleUser = query.getSaleUser();
        if (StringUtils.isBlank(accountNumber)
                && StringUtils.isBlank(saleUser)
                && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> apiResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_JOOM, false);
            if (!apiResult.isSuccess()) {
                cQueryResult.setErrorMsg(apiResult.getErrorMsg());
                return cQueryResult;
            }
            List<String> accountNumberList = apiResult.getResult();
            if (CollectionUtils.isEmpty(accountNumberList)) {
                return cQueryResult;
            }
            query.setAccountNumber(StringUtils.join(accountNumberList,","));
        }
        JoomDataViewExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        Boolean isTotal = query.getStatus().equals(DataViewStatusEnum.TOTAL.getId());
        if(isTotal) {
            query.setStatus(DataViewStatusEnum.ADD.getId());
        }
        // 是否分页
        if (cquery.isPageReqired()) {
            query.setLimit(cquery.getLimit());
            query.setOffset(cquery.getOffset());
            if(query.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
                total = isTotal ? joomDataViewMapper.countTotalTemplate(query) : joomDataViewMapper.countByExample(example);
            }else {
                total = isTotal ? joomDataViewMapper.countTotalListing(query) : joomDataViewMapper.countByExample(example);
            }
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomDataView> joomDataViews = new ArrayList<>();
        if(query.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
            joomDataViews = isTotal ? joomDataViewMapper.selectTotalTemplate(query) : joomDataViewMapper.selectByExample(example);
        }else {
            joomDataViews = isTotal ? joomDataViewMapper.selectTotalListing(query) : joomDataViewMapper.selectByExample(example);
        }
        // 组装结果
        CQueryResult<JoomDataView> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomDataViews);
        return result;
    }

    @Override
    public JoomDataView selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return joomDataViewMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<JoomDataView> selectByExample(JoomDataViewExample example) {
        Assert.notNull(example, "example is null!");
        return joomDataViewMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomDataView record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        return joomDataViewMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomDataView record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomDataViewMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomDataView record, JoomDataViewExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomDataViewMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return joomDataViewMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<JoomDataView> countTemplate() {
        return joomDataViewMapper.countTemplate();
    }

    @Override
    public List<JoomDataView> countListing() {
        return joomDataViewMapper.countListing();
    }

    @Override
    public void batchInsert(List<JoomDataView> recordList) {
        if(CollectionUtils.isNotEmpty(recordList)) {
            recordList.stream().forEach(record-> record.setCreationDate(new Timestamp(System.currentTimeMillis())));
            joomDataViewMapper.batchInsert(recordList);
        }
    }

    @Override
    @Transactional
    public void countJoomData() {
        try {
            List<JoomDataView> countTemplateAdd = this.countTemplate();
            this.batchInsert(countTemplateAdd);
            List<JoomDataView> countListingAdd = this.countListing();
            this.batchInsert(countListingAdd);
        }catch(Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> countSuccessTemplate() {
        return joomDataViewMapper.countSuccessTemplate();
    }

    @Override
    public List<Map<String, Object>> countUnStandardData() {
        return joomDataViewMapper.countUnStandardData();
    }

    @Override
    public List<JoomDataView> getSuccessListing() {
        return joomDataViewMapper.getSuccessListing();
    }
}