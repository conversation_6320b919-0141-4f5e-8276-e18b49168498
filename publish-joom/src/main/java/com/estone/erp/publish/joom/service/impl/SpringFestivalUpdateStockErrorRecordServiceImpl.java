package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.joom.mapper.SpringFestivalUpdateStockErrorRecordMapper;
import com.estone.erp.publish.joom.model.SpringFestivalUpdateStockErrorRecord;
import com.estone.erp.publish.joom.model.SpringFestivalUpdateStockErrorRecordCriteria;
import com.estone.erp.publish.joom.model.SpringFestivalUpdateStockErrorRecordExample;
import com.estone.erp.publish.joom.service.SpringFestivalUpdateStockErrorRecordService;
import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * 2025-01-03 17:53:55
 */
@Service("springFestivalUpdateStockErrorRecordService")
@Slf4j
public class SpringFestivalUpdateStockErrorRecordServiceImpl implements SpringFestivalUpdateStockErrorRecordService {
    @Resource
    private SpringFestivalUpdateStockErrorRecordMapper springFestivalUpdateStockErrorRecordMapper;

    @Override
    public int countByExample(SpringFestivalUpdateStockErrorRecordExample example) {
        Assert.notNull(example, "example is null!");
        return springFestivalUpdateStockErrorRecordMapper.countByExample(example);
    }

    @Override
    public CQueryResult<SpringFestivalUpdateStockErrorRecord> search(CQuery<SpringFestivalUpdateStockErrorRecordCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SpringFestivalUpdateStockErrorRecordCriteria query = cquery.getSearch();
        SpringFestivalUpdateStockErrorRecordExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = springFestivalUpdateStockErrorRecordMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<SpringFestivalUpdateStockErrorRecord> springFestivalUpdateStockErrorRecords = springFestivalUpdateStockErrorRecordMapper.selectByExample(example);
        // 组装结果
        CQueryResult<SpringFestivalUpdateStockErrorRecord> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(springFestivalUpdateStockErrorRecords);
        return result;
    }

    @Override
    public SpringFestivalUpdateStockErrorRecord selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return springFestivalUpdateStockErrorRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SpringFestivalUpdateStockErrorRecord> selectByExample(SpringFestivalUpdateStockErrorRecordExample example) {
        Assert.notNull(example, "example is null!");
        return springFestivalUpdateStockErrorRecordMapper.selectByExample(example);
    }

    @Override
    public int insert(SpringFestivalUpdateStockErrorRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return springFestivalUpdateStockErrorRecordMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(SpringFestivalUpdateStockErrorRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return springFestivalUpdateStockErrorRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SpringFestivalUpdateStockErrorRecord record, SpringFestivalUpdateStockErrorRecordExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return springFestivalUpdateStockErrorRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return springFestivalUpdateStockErrorRecordMapper.deleteByPrimaryKey(ids);
    }
}