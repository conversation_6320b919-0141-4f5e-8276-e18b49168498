package com.estone.erp.publish.joom.call;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.joom.util.modal.JoomDeliveryChannel;
import com.estone.erp.publish.joom.util.modal.JoomDeliveryChannelTier;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2020/10/9
 */
@Slf4j
public class JoomGetDeliveryChannelsCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;
    private static final String PATH = "warehouse/channel/multi-get";
    private static final String DEFAULT_CHARSET = "utf-8";


    public JoomGetDeliveryChannelsCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    public List<JoomDeliveryChannel> getDeliveryChannels(String warehouseId) {
        httpClient = HttpClientUtils.createClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));
        standardNameValue.add(new BasicNameValuePair("warehouse_id", warehouseId));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        List<JoomDeliveryChannel> joomDeliveryChannels = null;
        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));
            HttpGet httpGet = new HttpGet();
            httpGet.setURI(new URI(url.toString()));

            CloseableHttpResponse httpResponse = null;
            int retryTimes = 0;

            // 重试三次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpGet);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();
                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        joomDeliveryChannels = this.toJoomDeliveryChannels(responseJson, joomPmsAccount.getAccountNumber());
                    }
                    else {
                        log.error("JoomGetDeliveryChannelsCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;
                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes) + "--" + e.getMessage(), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomGetDeliveryChannelsCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    return joomDeliveryChannels;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }
            return joomDeliveryChannels;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    /**
     * 返回数据转化为Joom运输渠道
     * @param responseJsonStr
     * @return
     */
    private static List<JoomDeliveryChannel> toJoomDeliveryChannels(String responseJsonStr, String account) {
        List<JoomDeliveryChannel> joomDeliveryChannels = new ArrayList<>();

        JSONObject responseJson = JSONObject.parseObject(responseJsonStr);
        if(null == responseJson) {
            return joomDeliveryChannels;
        }

        JSONArray dataJsonArray = responseJson.getJSONArray("data");
        if(null == dataJsonArray) {
            return joomDeliveryChannels;
        }

        for (int i = 0 ; i < dataJsonArray.size(); i++) {
            JSONObject dataJsonObject = dataJsonArray.getJSONObject(i);
            JSONObject channelJsonObject = dataJsonObject.getJSONObject("Channel");
            if(null == channelJsonObject) {
                continue;
            }

            JoomDeliveryChannel joomDeliveryChannel = new JoomDeliveryChannel();
            joomDeliveryChannels.add(joomDeliveryChannel);

            joomDeliveryChannel.setAccount(account);
            joomDeliveryChannel.setId(channelJsonObject.getString("id"));
            joomDeliveryChannel.setName(channelJsonObject.getString("name"));
            joomDeliveryChannel.setWarehouseId(channelJsonObject.getString("warehouse_id"));
            joomDeliveryChannel.setWarehouseName(channelJsonObject.getString("warehouse_name"));

            // 国家
            JSONArray countryJsonArray = channelJsonObject.getJSONArray("enabled_in_countries");
            if(null == channelJsonObject) {
                continue;
            }

            List<String> countryList = countryJsonArray.toJavaList(String.class);
            joomDeliveryChannel.setEnabledInCountries(countryList);

            JoomDeliveryChannelTier tier = new JoomDeliveryChannelTier();
            joomDeliveryChannel.setJoomDeliveryChannelTier(tier);

            JSONObject tierJsonObject = channelJsonObject.getJSONObject("tier");
            if(null != tierJsonObject) {
                tier.setId(tierJsonObject.getString("id"));
                tier.setName(tierJsonObject.getString("name"));
                tier.setType(tierJsonObject.getString("type"));
                tier.setRegionId(tierJsonObject.getString("region_id"));

                JSONObject slaJsonObject = tierJsonObject.getJSONObject("sla");
                if(null != slaJsonObject) {
                    tier.setWarrantyDurationDays(slaJsonObject.getString("warranty_duration_days"));
                }
            }
        }

        return joomDeliveryChannels;
    }
}
