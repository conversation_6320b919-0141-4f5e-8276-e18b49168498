package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.JoomDataView;
import com.estone.erp.publish.joom.model.JoomDataViewCriteria;
import com.estone.erp.publish.joom.model.JoomDataViewExample;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> joom_data_view
 * 2020-04-15 18:24:07
 */
public interface JoomDataViewService {
    int countByExample(JoomDataViewExample example);

    CQueryResult<JoomDataView> search(CQuery<JoomDataViewCriteria> cquery);

    CQueryResult<JoomDataView> searchOrig(CQuery<JoomDataViewCriteria> cquery);

    List<JoomDataView> selectByExample(JoomDataViewExample example);

    JoomDataView selectByPrimaryKey(Integer id);

    int insert(JoomDataView record);

    void batchInsert(List<JoomDataView> recordList);

    int updateByPrimaryKeySelective(JoomDataView record);

    int updateByExampleSelective(JoomDataView record, JoomDataViewExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    List<Map<String, Object>> countUnStandardData();

    List<Map<String, Object>> countSuccessTemplate();

    List<JoomDataView> countTemplate();

    List<JoomDataView> countListing();

    void countJoomData();

    List<JoomDataView> getSuccessListing();
}