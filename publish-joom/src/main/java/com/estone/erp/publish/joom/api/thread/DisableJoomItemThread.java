package com.estone.erp.publish.joom.api.thread;

import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.call.JoomDisableItemCall;
import com.estone.erp.publish.joom.call.JoomDisableVariationCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.concurrent.CountDownLatch;

@Slf4j
public class DisableJoomItemThread implements Runnable {

    private CountDownLatch countDownLatch;

    private JoomItem joomItem;

    private ResponseJson response;

    private Boolean isVariation;

    public DisableJoomItemThread(JoomItem joomItem, CountDownLatch countDownLatch, ResponseJson response,
            Boolean isVariation) {
        super();
        this.joomItem = joomItem;
        this.countDownLatch = countDownLatch;
        this.response = response;
        this.isVariation = isVariation;
    }

    @Override
    public void run() {
        if (StringUtils.isBlank(joomItem.getItemSeller())) {
            response.getErrors()
                    .add(new ResponseError("帐号为空", joomItem.getJoomItemId() + "," + joomItem.getSku(), "卖家帐号为空！"));
        }
        EnvironmentSupplierWrapper.execute(task->{
            try {
                SaleAccountAndBusinessResponse joomPmsAccount =
                        AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, joomItem.getItemSeller());

                ResponseJson json = new ResponseJson();
                if (isVariation) {
                    JoomDisableVariationCall call = new JoomDisableVariationCall(joomPmsAccount);
                    json = call.disableVariation(joomItem);
                }
                else {
                    JoomDisableItemCall call = new JoomDisableItemCall(joomPmsAccount);
                    json = call.disableItem(joomItem);
                }

                response.addError(new ResponseError(json.getStatus(), joomItem.getJoomItemId() + "," + joomItem.getSku(),
                        json.getMessage() + "-" + json.getErrors().toString()));
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            finally {
                countDownLatch.countDown();
            }
        }, defaultTask->{
            response.getErrors()
                    .add(new ResponseError("非正式环境不执行下架", joomItem.getJoomItemId() + "," + joomItem.getSku(), "非正式环境不执行下架"));
        });

    }

}
