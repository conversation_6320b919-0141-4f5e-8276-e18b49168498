package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.JoomCategoryDescConfigMapper;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfig;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfigCriteria;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfigExample;
import com.estone.erp.publish.joom.service.JoomCategoryDescConfigService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> joom_category_desc_config
 * 2022-11-09 17:35:02
 */
@Service("joomCategoryDescConfigService")
@Slf4j
public class JoomCategoryDescConfigServiceImpl implements JoomCategoryDescConfigService {
    @Resource
    private JoomCategoryDescConfigMapper joomCategoryDescConfigMapper;

    @Override
    public int countByExample(JoomCategoryDescConfigExample example) {
        Assert.notNull(example, "example is null!");
        return joomCategoryDescConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomCategoryDescConfig> search(CQuery<JoomCategoryDescConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomCategoryDescConfigCriteria query = cquery.getSearch();
        JoomCategoryDescConfigExample example = query.getExample();

        // 默认倒叙
        example.setOrderByClause(" id desc ");

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomCategoryDescConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomCategoryDescConfig> joomCategoryDescConfigs = joomCategoryDescConfigMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomCategoryDescConfig> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomCategoryDescConfigs);
        return result;
    }

    @Override
    public JoomCategoryDescConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return joomCategoryDescConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public JoomCategoryDescConfig selectByFullCategoryCode(String fullCategoryCode){
        Assert.notNull(fullCategoryCode, "fullCategoryCode is null");
        return joomCategoryDescConfigMapper.selectByFullCategoryCode(fullCategoryCode);
    }

    @Override
    public List<JoomCategoryDescConfig> selectByExample(JoomCategoryDescConfigExample example) {
        Assert.notNull(example, "example is null!");
        return joomCategoryDescConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomCategoryDescConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return joomCategoryDescConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomCategoryDescConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomCategoryDescConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomCategoryDescConfig record, JoomCategoryDescConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomCategoryDescConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return joomCategoryDescConfigMapper.deleteByPrimaryKey(ids);
    }
}