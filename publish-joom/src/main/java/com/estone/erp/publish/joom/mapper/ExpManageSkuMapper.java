package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.ExpManageSku;
import com.estone.erp.publish.joom.model.ExpManageSkuExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpManageSkuMapper {
    int countByExample(ExpManageSkuExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(ExpManageSku record);

    ExpManageSku selectByPrimaryKey(Integer id);

    List<ExpManageSku> selectByExample(ExpManageSkuExample example);

    int updateByExampleSelective(@Param("record") ExpManageSku record, @Param("example") ExpManageSkuExample example);

    int updateByPrimaryKeySelective(ExpManageSku record);

    /**
     * 最近的促销sku
     * @return
     */
    List<String> listSkuByExpSkus();

    List<ExpManageSku> searchExistRecord(ExpManageSkuExample example);

    void batchUpdateRecord(@Param("updateRecord") List<ExpManageSku> updateRecord);

    void batchInsertRecord(@Param("itemList") List<ExpManageSku> itemList);

    List<ExpManageSku> selectIdByExample(ExpManageSkuExample example);

    void batchResumePromotion(@Param("updateRecord") List<ExpManageSku> expManageSkuList);
}