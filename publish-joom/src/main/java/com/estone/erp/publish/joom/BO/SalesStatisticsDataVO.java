package com.estone.erp.publish.joom.BO;

import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.model.dto.SalesDailyDataDO;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

/**
 * 数据看板-前端展示对象
 * <AUTHOR>
 * @date 2023-01-09 10:08
 */
@Getter
@Setter
@NoArgsConstructor
public class SalesStatisticsDataVO {

    /**
     * 店铺数量
     */
    private Integer saleAccountNum;

    /**
     * 统计月份
     */
    private String month;

    /**
     * 销售额
     */
    private Double salesNum;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 新增链接数
     */
    private Integer addListingNum;

    /**
     * 月销售目标
     */
    private Double monthSaleTarget;

    /**
     * 月新增链接数目标
     */
    private Integer monthAddListingTarget;

    /**
     * smt新品推荐
     */
    private Integer smtNewRecommend;

    /**
     * amazon新品推荐
     */
    private Integer amazonNewRecommend;

    /**
     * 平台热售推荐
     */
    private Integer platformHotRecommend;

    /**
     * 站点热售推荐
     */
    private Integer siteHotRecommend;

    /**
     * 在线链接总数
     */
    private Integer onlineListingNum;

    /**
     * 侵权禁售链接数
     */
    private Integer forbiddenListingNum;

    /**
     * 不及格毛利链接数
     */
    private Integer subGrossProfitListingNum;

    /**
     * 标准毛利
     */
    private Double grossProfitThreshold;

    /**
     * 包含侵权词链接
     */
    private Integer infringementWordListingNum;

    /**
     * 停产存档库存不为0链接
     */
    private Integer stopStatusListingNum;

    /**
     * 库存阈值
     */
    private Integer stockThreshold;

    /**
     * 正常状态库存不足链接
     */
    private Integer notEnoughStockListingNum;

    /**
     * 统计时间
     */
    private String statisticalDate;

    /**
     * 销售每日销售数据
     */
    private List<SalesDailyDataDO> salesDailyDataList;



    public static SalesStatisticsDataVO convent2VO(EsSalesStatisticsData data) {
        if (data == null) {
            return new SalesStatisticsDataVO();
        }
        SalesStatisticsDataVO vo = BeanUtil.copyProperties(data, SalesStatisticsDataVO.class);
        LocalDate now = LocalDate.now();
        String statisticalDate = LocalDate.of(now.getYear(), now.getMonth(), 1) + " ~ " + data.getStatisticalDate();
        vo.setStatisticalDate(statisticalDate);

        // 每日销售数据，排序
        vo.getSalesDailyDataList().sort((Comparator.comparing(o -> LocalDate.parse(o.getStatistDate()))));
        if (vo.getSalesDailyDataList().size() > 7) {
            int totalIndex = vo.getSalesDailyDataList().size() - 1;
            List<SalesDailyDataDO> dataList = vo.getSalesDailyDataList();
            List<SalesDailyDataDO> dailyDataDOS = dataList.subList(Math.max(totalIndex - 7, 0), totalIndex);
            vo.setSalesDailyDataList(dailyDataDOS);
        }
        return vo;
    }


}
