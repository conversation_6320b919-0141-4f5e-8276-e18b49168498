package com.estone.erp.publish.joom.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.joom.util.MapUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Joom获取订单
 * <p>
 * https://merchant.joom.it/documentation/api/v2#list-all-orders
 * 
 * <AUTHOR>
 * @version Bessky V100R001 2015年4月11日
 * @since Bessky V100R001C00
 */
@Slf4j
public class JoomGetAllOrdersCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private static final int PAGE_SIZE = 100;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "order/multi-get";

    /**
     * @param joomAccount
     */
    public JoomGetAllOrdersCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    public void getAllOrders() {
        try {
            getAllOrders(null, null);
        }
        catch (Exception e) {
            log.error(e.getMessage() + "Account" + joomPmsAccount.getAccountNumber() + e);
        }

    }

    public void getAllOrders(Timestamp inputStartDate, Timestamp inputEndDate)
            throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {

        httpClient = HttpClientUtils.createSSLClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();

        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        // 2014-01-20T20:10:20
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

        // 美国时区
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

        if (inputStartDate == null && inputEndDate == null) {
            Calendar startDate = Calendar.getInstance();
            startDate.add(Calendar.HOUR_OF_DAY, -6);

            standardNameValue.add(new BasicNameValuePair("since", sdf.format(startDate.getTime())));
        }
        else {
            standardNameValue.add(new BasicNameValuePair("since", sdf.format(inputStartDate)));
        }

        try {
            getAllOrders(1, standardNameValue);
        }
        catch (Exception e) {
            log.error(e.getMessage() + "Account" + joomPmsAccount.getAccountNumber() + e);
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    private void getAllOrders(int pageNo, List<NameValuePair> standardNameValue) {

        List<NameValuePair> copyNameValue = new ArrayList<NameValuePair>(standardNameValue);

        copyNameValue.add(new BasicNameValuePair("start", String.valueOf((pageNo - 1) * PAGE_SIZE)));
        copyNameValue.add(new BasicNameValuePair("limit", String.valueOf(PAGE_SIZE)));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(copyNameValue, DEFAULT_CHARSET)));
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return;
        }

        nextPage(url);
    }

    private class JoomOrderCallable implements Runnable {

        private HttpGet httpGet;

        private Map<String, Object> paging;

        private CountDownLatch countDownLatch;

        public JoomOrderCallable(HttpGet httpGet, Map<String, Object> paging, CountDownLatch countDownLatch) {
            this.httpGet = httpGet;
            this.paging = paging;
            this.countDownLatch = countDownLatch;
        }

        /*
         * (non-Javadoc)
         * 
         * @see java.util.concurrent.Callable#call()
         */
        @SuppressWarnings("unchecked")
        @Override
        public void run() {
            Map<String, Object> orderDetails = null;

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            try {
                while (retryTimes < 3) {
                    retryTimes++;
                    try {

                        httpResponse = httpClient.execute(httpGet);

                        // 没有订单 直接返回
                        if (httpResponse == null) {
                            return;
                        }

                        // 获取响应消息实体
                        HttpEntity entity = httpResponse.getEntity();

                        String responseJson = EntityUtils.toString(entity);

                        // 判断响应实体是否为空
                        if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                            orderDetails = JSON.parseObject(responseJson, Map.class);
                        }
                        else {
                            JSONObject error = JSON.parseObject(responseJson);

                            String error_code = error.getString("code");

                            // 未授权
                            if ("1016".equals(error_code) || "4000".equals(error_code)) {
                                // 旧的token
                                // String oldAppKey = joomAccount.getAppKey();
                                //
                                // JoomGetRefreshTokenCall refreshTokenCall =
                                // new JoomGetRefreshTokenCall(joomAccount);
                                //
                                // // 强制刷新
                                // boolean success =
                                // refreshTokenCall.refreshToken(true);
                                //
                                // if(success)
                                // {
                                // URI uri = httpGet.getURI();
                                //
                                // String oldUrl = uri.toString();
                                //
                                // String newUrl = oldUrl.replace(oldAppKey,
                                // joomAccount.getAppKey());
                                //
                                // //https://merchant.wish.com/api/v2/order/multi-get?access_token=88500af442dc41af8b46da77f3e76986p&since=2015-11-27T22%3A31%3A37&start=0&limit=150
                                // httpGet.setURI(new URI(newUrl));
                                // continue;
                                // }
                            }

                            log.error("getAllOrders error. " + "Account" + joomPmsAccount.getAccountNumber() + "Message"
                                    + responseJson);
                        }

                        break;

                    }
                    catch (Exception e) {
                        if (isNeedRetry(e)) {
                            log.error("超时重做" + "Account" + joomPmsAccount.getAccountNumber());
                            continue;
                        }

                        // 这个异常不需要打印
                        if (!StringUtils.contains(e.getMessage(), "Connection pool shut down")) {
                            log.error(e.getMessage() + "API" + "getAllOrders" + "Account"
                                    + joomPmsAccount.getAccountNumber() + e);
                        }

                        return;
                    }
                    finally {
                        HttpClientUtils.closeQuietly(httpResponse);
                    }
                } // while

                if (orderDetails == null || orderDetails.isEmpty()) {
                    return;
                }

                List<Object> orderList = (List<Object>) orderDetails.get("data");

                if (orderList == null || orderList.isEmpty()) {
                    return;
                }

                Map<String, Object> paging = (Map<String, Object>) orderDetails.get("paging");

                // 存到下一页
                if (paging != null) {
                    this.paging.putAll(paging);
                }

            }
            finally {
                countDownLatch.countDown();
            }

        }

    }

    /**
     * 
     * <p>
     *
     * @param httpGet
     * @param url
     * @return void
     */

    private void nextPage(StringBuffer url) {
        HttpGet httpGet = new HttpGet();

        try {
            httpGet.setURI(new URI(url.toString()));
        }
        catch (URISyntaxException e1) {
        }

        Map<String, Object> paging = new HashMap<String, Object>();

        int retryTimes = 0;

        ExecutorService service = Executors.newFixedThreadPool(1);

        try {
            while (retryTimes < 5) {
                retryTimes++;

                CountDownLatch countDownLatch = new CountDownLatch(1);

                // 执行
                service.execute(new JoomOrderCallable(httpGet, paging, countDownLatch));

                try {
                    if (countDownLatch.await(1, TimeUnit.MINUTES)) {
                        // 不超时
                        if (paging != null && paging.size() > 0) {
                            String next = MapUtils.getString(paging, "next");

                            if (StringUtils.isNotBlank(next)) {
                                StringBuffer nextUrl = new StringBuffer(next);
                                nextPage(nextUrl);
                            }
                        }

                        return;
                    }
                }
                catch (Exception e) {
                    // ignore exception
                }

                // 来到这里表示超时
                // throw new TimeoutException("send request timeout");
                try {
                    Thread.sleep(5 * 1000);
                }
                catch (InterruptedException e) {
                }
                log.error("send request timeout" + "Account" + joomPmsAccount.getAccountNumber());

                continue;
            }
        }
        finally {
            service.shutdown();
        }

    }

}
