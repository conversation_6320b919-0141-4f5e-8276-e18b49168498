package com.estone.erp.publish.joom.enums;

/**
 * @description: 推送到刊登的保质期标签
 * @date 2022/11/28 17:05
 * @version 1.0
 */
public enum PushPublishExpFlagEnums {
    PROMOTION("促销", "1"),

    CANCEL_PROMOTION("结束促销", "2"),

    ;

    private String code;

    private String name;

    private PushPublishExpFlagEnums(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PushPublishExpFlagEnums[] values = values();
        for (PushPublishExpFlagEnums type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static PushPublishExpFlagEnums build(String code) {
        PushPublishExpFlagEnums[] values = values();

        for (PushPublishExpFlagEnums type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }
}
