package com.estone.erp.publish.joom.BO;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 首页请求体
 * <AUTHOR>
 * @date 2023-01-06 9:45
 */
@Data
public class DashboardRequestDO implements Serializable {

    /**
     * 平台
     */
    private String platform;

    /**
     * 店铺
     */
    private List<String> accountNumber;

    /**
     * 销售
     */
    private List<String> saleManList;

    /**
     * 是否默认查询，不带查询参数
     */
    public Boolean isDefaultQuery() {
        return StringUtils.isBlank(this.platform)
                && CollectionUtils.isEmpty(this.accountNumber)
                && CollectionUtils.isEmpty(this.saleManList);
    }

    /**
     * 是否仅查询平台
     * @return
     */
    public Boolean isOnlyPlatformQuery() {
        return StringUtils.isNotBlank(this.platform)
                && CollectionUtils.isEmpty(this.accountNumber)
                && CollectionUtils.isEmpty(this.saleManList);
    }


    /**
     * 是否仅销售
     * @return
     */
    public Boolean isOnlySaleQuery() {
        return StringUtils.isNotBlank(this.platform)
                && CollectionUtils.isEmpty(this.accountNumber)
                && CollectionUtils.isNotEmpty(this.saleManList);
    }


}
