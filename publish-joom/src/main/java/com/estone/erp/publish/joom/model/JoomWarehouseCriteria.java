package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> joom_warehouse
 * 2020-10-09 17:18:19
 */
public class JoomWarehouseCriteria extends JoomWarehouse {
    private static final long serialVersionUID = 1L;

    public JoomWarehouseExample getExample() {
        JoomWarehouseExample example = new JoomWarehouseExample();
        JoomWarehouseExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (StringUtils.isNotBlank(this.getWarehouseId())) {
            criteria.andWarehouseIdEqualTo(this.getWarehouseId());
        }
        if (StringUtils.isNotBlank(this.getWarehouseName())) {
            criteria.andWarehouseNameEqualTo(this.getWarehouseName());
        }
        if (this.getCreationDate() != null) {
            criteria.andCreationDateEqualTo(this.getCreationDate());
        }
        return example;
    }
}