package com.estone.erp.publish.joom.model.dto;

import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomItemExample;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> joom_item 2019-08-08 10:44:45
 */
@Data
public class JoomItemCriteria extends JoomItem {
    private static final long serialVersionUID = 1L;

    private String productManagerNo;

    private String childSku;

    private String itemIds;

    private Date fromItemUploadedDate;
    private Date toItemUploadedDate;

    private Date fromItemDownDate;
    private Date toItemDownDate;

    private Date fromUpdateDate;
    private Date toUpdateDate;

    private Date fromLastSoldDate;
    private Date toLastSoldDate;

    private Integer priceFlag = 0;// 页面价格筛选条件

    private Boolean tagcheck = false;// 是否过滤标签

    private String skus;

    private List<String> itemSellerList;
    private List<String> tagCodes;
    private List<String> skuStatusList;
    private List<String> stateList;

    /**
     * 可用库存范围
     */
    private Integer fromInventory;
    private Integer toInventory;

    /**
     * 可用库存范围
     */
    private Integer fromWhInventory;
    private Integer toWhInventory;

    /**
     * 可用库存天数范围
     */
    private BigDecimal fromAvailableStockDays;
    private BigDecimal toAvailableStockDays;

    /**
     * 标签集合
     */
    private List<String> tagCodeList;

    /**
     * 禁售平台
     */
    private List<String> forbidChannelList;

    /**
     * 禁售类型
     */
    private List<String> infringementTypeNameList;

    /**
     * 禁售原因
     */
    private List<String> infringementObjList;

    /**
     * 禁售站点
     */
    private List<String> prohibitionSiteList;

    /**
     * 产品系统促销状态
     */
    private Boolean isProductPromotion;

    /**
     * 特殊标签集合
     */
    private List<String> specialGoodsCodeList;

    /**
     * 紫鸟账号
     */
    private String znAccount;

    public JoomItemExample getExample() {
        JoomItemExample example = new JoomItemExample();
        JoomItemExample.Criteria criteria = example.createCriteria();

        if(this.getMeasurementNull() != null){
            criteria.andMeasurementNullEqualTo(this.getMeasurementNull());
        }


        if(this.getSkuDataSource() != null){
            criteria.andSkuDataSourceEqualTo(this.getSkuDataSource());
        }

        if(this.getComposeStatus() != null){
            criteria.andComposeStatusEqualTo(this.getComposeStatus());
        }

        //特殊标签查询
        if(CollectionUtils.isNotEmpty(this.getSpecialGoodsCodeList())){
            criteria.andSpecialGoodsCodesLikeIn(this.getSpecialGoodsCodeList());
        }
        if (CollectionUtils.isNotEmpty(this.getSkuStatusList())) {
            example.setSkuStatuList(this.getSkuStatusList());
        }
        if (CollectionUtils.isNotEmpty(this.getTagCodes())) {
            example.setSkuTagCode(this.getTagCodes());
        }
        if (CollectionUtils.isNotEmpty(this.getItemSellerList())) {
            criteria.andItemSellerIn(this.getItemSellerList());
        }
        if (StringUtils.isNotBlank(this.getProductManagerNo())) {
            example.setManagerNo(this.getProductManagerNo());
        }

        if(CollectionUtils.isNotEmpty(this.getStateList())) {
            criteria.andStateIn(this.getStateList());
        }

        if (StringUtils.isNotBlank(this.getItemIds())) {
            if (StringUtils.contains(this.getItemIds(), ",")) {
                List<Integer> itemIdList = new ArrayList<Integer>();
                for (String itemId : StringUtils.split(this.getItemIds(), ",")) {
                    itemIdList.add(Integer.valueOf(itemId.trim()));
                }
                criteria.andItemIdIn(itemIdList);
            }
            else {
                criteria.andItemIdEqualTo(Long.valueOf(this.getItemIds().trim()));
            }
        }
        if (StringUtils.isNotBlank(this.getJoomItemId())) {
            if (StringUtils.contains(this.getJoomItemId(), ",")) {
                List<String> joomItemIdList = new ArrayList<String>();
                for (String joomItemId : StringUtils.split(this.getJoomItemId(), ",")) {
                    joomItemIdList.add(joomItemId.trim());
                }
                criteria.andJoomItemIdIn(joomItemIdList);
            }
            else {
                criteria.andJoomItemIdEqualTo(this.getJoomItemId().trim());
            }
        }
        if (StringUtils.isNotBlank(this.getItemSeller())) {
            criteria.andItemSellerEqualTo(this.getItemSeller());
        }
        if (StringUtils.isNotBlank(this.getProductManagerNo())) {
            criteria.andProductManagerNoEqualTo(this.getProductManagerNo());
        }
        if (this.getIsOnline() != null) {
            criteria.andIsOnlineEqualTo(this.getIsOnline());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            if (StringUtils.contains(this.getArticleNumber(), ",")) {
                List<String> list = new ArrayList<String>();
                for(String item : StringUtils.split(this.getArticleNumber(), ",")) {
                    list.add(item.trim());
                }
                criteria.andArticleNumberIn(list);
            }
            else {
                criteria.andArticleNumberEqualTo(this.getArticleNumber().trim());
            }
        }
        if (StringUtils.isNotBlank(this.getChildSku())) {
            criteria.andSkuLikeIn(this.getChildSku());
        }
        if (StringUtils.isNotBlank(this.getReviewStatus())) {
            criteria.andReviewStatusEqualTo(this.getReviewStatus());
        }
        if (StringUtils.isNotBlank(this.getItemTitle())) {
            criteria.andItemTitleLike("%" + this.getItemTitle() + "%");
        }
        if (this.getFromUpdateDate() != null) {
            criteria.andLastUpdateDateGreaterThanOrEqualTo(this.getFromUpdateDate());
        }
        if (this.getToUpdateDate() != null) {
            criteria.andLastUpdateDateLessThanOrEqualTo(this.getToUpdateDate());
        }
        if (this.getFromItemUploadedDate() != null) {
            criteria.andItemUploadedDateGreaterThanOrEqualTo(this.getFromItemUploadedDate());
        }
        if (this.getToItemUploadedDate() != null) {
            criteria.andItemUploadedDateLessThanOrEqualTo(this.getToItemUploadedDate());
        }
        if (this.getFromItemDownDate() != null) {
            criteria.andItemDownDateGreaterThanOrEqualTo(this.getFromItemDownDate());
        }
        if (this.getToItemDownDate() != null) {
            criteria.andItemDownDateLessThanOrEqualTo(this.getToItemDownDate());
        }
        if (this.getFromLastSoldDate() != null) {
            criteria.andLastSoldDateGreaterThanOrEqualTo(this.getFromLastSoldDate());
        }
        if (this.getToLastSoldDate() != null) {
            criteria.andLastSoldDateLessThanOrEqualTo(this.getToLastSoldDate());
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            if (StringUtils.contains(this.getSkus(), ",")) {
                List<String> skuList = new ArrayList<>();
                String[] split = StringUtils.split(this.getSkus(), ",");
                for (String item : split) {
                    skuList.add(item.trim());
                }
                criteria.andSkuIn(skuList);
            }
            else {
                criteria.andSkuEqualTo(this.getSkus().trim());
            }
        }

        if (this.getItemId() != null) {
            criteria.andItemIdEqualTo(this.getItemId());
        }
        if (this.getIsPromoted() != null) {
            criteria.andIsPromotedEqualTo(this.getIsPromoted());
        }
        if (this.getNumberSaves() != null) {
            criteria.andNumberSavesEqualTo(this.getNumberSaves());
        }
        if (this.getNumberSold() != null) {
            criteria.andNumberSoldEqualTo(this.getNumberSold());
        }
        if (StringUtils.isNotBlank(this.getParentId())) {
            criteria.andParentIdEqualTo(this.getParentId());
        }
        if (StringUtils.isNotBlank(this.getParentSku())) {
            criteria.andParentSkuEqualTo(this.getParentSku());
        }
        if (this.getIsMultiAttr() != null) {
            criteria.andIsMultiAttrEqualTo(this.getIsMultiAttr());
        }
        if (this.getIsVariation() != null) {
            criteria.andIsVariationEqualTo(this.getIsVariation());
        }
        if (this.getIsJoomExpress() != null) {
            criteria.andIsJoomExpressEqualTo(this.getIsJoomExpress());
        }
        if (StringUtils.isNotBlank(this.getChildId())) {
            criteria.andChildIdEqualTo(this.getChildId());
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (this.getInventory() != null) {
            criteria.andInventoryEqualTo(this.getInventory());
        }
        if (this.getFromInventory() != null) {
            criteria.andInventoryGreaterThanOrEqualTo(this.getFromInventory());
        }
        if (this.getToInventory() != null) {
            criteria.andInventoryLessThanOrEqualTo(this.getToInventory());
        }
        if (this.getLastInventory() != null) {
            criteria.andLastInventoryEqualTo(this.getLastInventory());
        }
        if (this.getIsEnabled() != null) {
            criteria.andIsEnabledEqualTo(this.getIsEnabled());
        }
        if (this.getMsrp() != null) {
            criteria.andMsrpEqualTo(this.getMsrp());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getSystemPrice() != null) {
            criteria.andSystemPriceEqualTo(this.getSystemPrice());
        }
        if (StringUtils.isNotBlank(this.getMultiAttr())) {
            criteria.andMultiAttrEqualTo(this.getMultiAttr());
        }
        if (this.getShippingCost() != null) {
            criteria.andShippingCostEqualTo(this.getShippingCost());
        }
        if (StringUtils.isNotBlank(this.getShippingTime())) {
            criteria.andShippingTimeEqualTo(this.getShippingTime());
        }
        if (StringUtils.isNotBlank(this.getMainImage())) {
            criteria.andMainImageEqualTo(this.getMainImage());
        }
        if (StringUtils.isNotBlank(this.getExtraImages())) {
            criteria.andExtraImagesEqualTo(this.getExtraImages());
        }
        if (StringUtils.isNotBlank(this.getAllImages())) {
            criteria.andAllImagesEqualTo(this.getAllImages());
        }
        if (StringUtils.isNotBlank(this.getAutoTags())) {
            criteria.andAutoTagsEqualTo(this.getAutoTags());
        }
        if (StringUtils.isNotBlank(this.getTags())) {
            criteria.andTagsEqualTo(this.getTags());
        }
        if (StringUtils.isNotBlank(this.getDescription())) {
            criteria.andDescriptionEqualTo(this.getDescription());
        }
        if (this.getIsFirstItem() != null) {
            criteria.andIsFirstItemEqualTo(this.getIsFirstItem());
        }
        if (this.getCreationDate() != null) {
            criteria.andCreationDateEqualTo(this.getCreationDate());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdatedBy())) {
            criteria.andLastUpdatedByEqualTo(this.getLastUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getDangerousKind())) {
            criteria.andDangerousKindEqualTo(this.getDangerousKind());
        }
        if (StringUtils.isNotBlank(this.getParentSkuOnline())) {
            criteria.andParentSkuOnlineEqualTo(this.getParentSkuOnline());
        }
        if (StringUtils.isNotBlank(this.getState())) {
            criteria.andStateEqualTo(this.getState());
        }
        if (null != this.getFromWhInventory()) {
            example.setFromWhInventory(this.getFromWhInventory());
        }
        if (null != this.getToWhInventory()) {
            example.setToWhInventory(this.getToWhInventory());
        }

        if (null != this.getFromAvailableStockDays()) {
            example.setFromAvailableStockDays(this.getFromAvailableStockDays());
        }
        // 单品状态
        if(CollectionUtils.isNotEmpty(this.getSkuStatusList())) {
            criteria.andSkuStatusIn(this.getSkuStatusList());
        }
        // 标签
        if(CollectionUtils.isNotEmpty(this.getTagCodeList())) {
            criteria.andTagCodesIn(this.getTagCodeList());
        }
        // 禁售平台
        if(CollectionUtils.isNotEmpty(this.getForbidChannelList())) {
            criteria.andForbidChannelIn(this.getForbidChannelList());
        }
        // 禁售类型
        if(CollectionUtils.isNotEmpty(this.getInfringementTypeNameList())) {
            criteria.andInfringementTypeNameIn(this.getInfringementTypeNameList());
        }
        // 禁售原因
        if(CollectionUtils.isNotEmpty(this.getInfringementObjList())) {
            criteria.andInfringementObjIn(this.getInfringementObjList());
        }
        // 禁售站点
        if(CollectionUtils.isNotEmpty(this.getProhibitionSiteList())) {
            // 也有平台的时候
            if(CollectionUtils.isNotEmpty(this.getForbidChannelList())) {
                List<String> platformSiteList = new ArrayList<String>();
                for (String prohibitionSite : this.getProhibitionSiteList()) {
                    for (String platform : this.getForbidChannelList()) {
                        if(StringUtils.isNotBlank(platform) && StringUtils.isNotBlank(prohibitionSite)) {
                            platformSiteList.add(platform + "_" + prohibitionSite);
                        }
                    }
                }
                criteria.andProhibitionPlatformSiteIn(platformSiteList);
            } else {
                // 没有平台的时候
                criteria.andProhibitionSiteIn(this.getProhibitionSiteList());
            }
        }
        // 产品系统 是否促销
        if (this.getIsProductPromotion() != null) {
            List<Integer> status = BooleanUtils.isTrue(this.getIsProductPromotion()) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            criteria.andProductIsPromotionIn(status);
        }

        //是否新品
        if(this.getNewState() != null){
            criteria.andNewStateEq(this.getNewState());
        }

        return example;
    }

    public String getProductManagerNo() {
        return productManagerNo;
    }

    public void setProductManagerNo(String productManagerNo) {
        this.productManagerNo = productManagerNo;
    }

    public String getChildSku() {
        return childSku;
    }

    public void setChildSku(String childSku) {
        this.childSku = childSku;
    }

    public Date getFromItemUploadedDate() {
        return fromItemUploadedDate;
    }

    public void setFromItemUploadedDate(Date fromItemUploadedDate) {
        this.fromItemUploadedDate = fromItemUploadedDate;
    }

    public Date getToItemUploadedDate() {
        return toItemUploadedDate;
    }

    public void setToItemUploadedDate(Date toItemUploadedDate) {
        this.toItemUploadedDate = toItemUploadedDate;
    }

    public Date getFromItemDownDate() {
        return fromItemDownDate;
    }

    public void setFromItemDownDate(Date fromItemDownDate) {
        this.fromItemDownDate = fromItemDownDate;
    }

    public Date getToItemDownDate() {
        return toItemDownDate;
    }

    public void setToItemDownDate(Date toItemDownDate) {
        this.toItemDownDate = toItemDownDate;
    }

    public Date getFromLastSoldDate() {
        return fromLastSoldDate;
    }

    public void setFromLastSoldDate(Date fromLastSoldDate) {
        this.fromLastSoldDate = fromLastSoldDate;
    }

    public Date getToLastSoldDate() {
        return toLastSoldDate;
    }

    public void setToLastSoldDate(Date toLastSoldDate) {
        this.toLastSoldDate = toLastSoldDate;
    }

    public String getItemIds() {
        return itemIds;
    }

    public void setItemIds(String itemIds) {
        this.itemIds = itemIds;
    }

    public Integer getPriceFlag() {
        return priceFlag;
    }

    public void setPriceFlag(Integer priceFlag) {
        this.priceFlag = priceFlag;
    }

    public Boolean getTagcheck() {
        return tagcheck;
    }

    public void setTagcheck(Boolean tagcheck) {
        this.tagcheck = tagcheck;
    }

    public String getSkus() {
        return skus;
    }

    public void setSkus(String skus) {
        this.skus = skus;
    }

    public List<String> getItemSellerList() {
        return itemSellerList;
    }

    public void setItemSellerList(List<String> itemSellerList) {
        this.itemSellerList = itemSellerList;
    }

    public List<String> getTagCodes() {
        return tagCodes;
    }

    public void setTagCodes(List<String> tagCodes) {
        this.tagCodes = tagCodes;
    }

    public List<String> getSkuStatusList() {
        return skuStatusList;
    }

    public void setSkuStatusList(List<String> skuStatusList) {
        this.skuStatusList = skuStatusList;
    }
}