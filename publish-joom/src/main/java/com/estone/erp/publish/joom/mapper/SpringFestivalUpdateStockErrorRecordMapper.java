package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.SpringFestivalUpdateStockErrorRecord;
import com.estone.erp.publish.joom.model.SpringFestivalUpdateStockErrorRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SpringFestivalUpdateStockErrorRecordMapper {
    int countByExample(SpringFestivalUpdateStockErrorRecordExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(SpringFestivalUpdateStockErrorRecord record);

    SpringFestivalUpdateStockErrorRecord selectByPrimaryKey(Integer id);

    List<SpringFestivalUpdateStockErrorRecord> selectByExample(SpringFestivalUpdateStockErrorRecordExample example);

    int updateByExampleSelective(@Param("record") SpringFestivalUpdateStockErrorRecord record, @Param("example") SpringFestivalUpdateStockErrorRecordExample example);

    int updateByPrimaryKeySelective(SpringFestivalUpdateStockErrorRecord record);
}