package com.estone.erp.publish.joom.call;

import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.joom.common.JoomProductCommon;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JoomGetItemListCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private JoomItemService joomItemService;

    private static final int PAGE_SIZE = 100;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "product/multi-get";

    public JoomGetItemListCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
        this.joomItemService = SpringUtils.getBean("joomItemService", JoomItemService.class);
    }

    public void getAllJoomItems(Timestamp inputStartDate) {
        long startTime = System.currentTimeMillis();
        log.warn("Joom开始同步产品，账号：" + joomPmsAccount.getAccountNumber());

        httpClient = HttpClientUtils.createSSLClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();

        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        // A date string in the format YYYY-MM-DD.
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 美国时区
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

        // A date string in the format YYYY-MM-DD. If a date is provided, only
        // products updated since the given date will be fetched. Default is to
        // fetch all.
        if (inputStartDate == null) {
//            Calendar startDate = Calendar.getInstance();
//            startDate.add(Calendar.YEAR, -2);
//
//            standardNameValue.add(new BasicNameValuePair("since", sdf.format(startDate.getTime())));
        }
        else {
            standardNameValue.add(new BasicNameValuePair("since", sdf.format(inputStartDate)));
        }

        try {
            getAllProducts(1, standardNameValue);
        }
        catch (Exception e) {
            log.warn("Account" + joomPmsAccount.getAccountNumber() + e.getMessage());
        }
        finally {
            long endTime = System.currentTimeMillis();
            log.info("Joom账号：" + joomPmsAccount.getAccountNumber() + "同步产品完成，耗时：" +  + ((endTime-startTime)/1000L));
            HttpClientUtils.closeQuietly(httpClient);
        }
    }

    private void getAllProducts(int pageNo, List<NameValuePair> standardNameValue) {

        List<NameValuePair> copyNameValue = new ArrayList<NameValuePair>(standardNameValue);

        copyNameValue.add(new BasicNameValuePair("start", String.valueOf((pageNo - 1) * PAGE_SIZE)));
        copyNameValue.add(new BasicNameValuePair("limit", String.valueOf(PAGE_SIZE)));

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(copyNameValue, DEFAULT_CHARSET)));
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return;
        }

        nextPage(url);
    }

    /**
     * 
     * @param url
     * @return void
     */
    private void nextPage(StringBuffer url) {
        HttpGet httpGet = new HttpGet();

        try {
            httpGet.setURI(new URI(url.toString()));
        }
        catch (URISyntaxException e) {
        }

        Map<String, Object> paging = new HashMap<String, Object>();

        int retryTimes = 0;

        // 执行
        Map<String, Object> productDetails = null;
        CloseableHttpResponse httpResponse = null;
        while (retryTimes < 5) {
            retryTimes++;
            try {
                RequestConfig config = RequestConfig.custom().setConnectTimeout(120000).setSocketTimeout(120000).build();
                httpGet.setConfig(config);
                httpResponse = httpClient.execute(httpGet);

                // 没有订单 直接返回
                if (httpResponse == null) {
                    return;
                }

                // 获取响应消息实体
                HttpEntity entity = httpResponse.getEntity();

                String responseJson = EntityUtils.toString(entity);

                // 判断响应实体是否为空
                if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                    productDetails = JSON.parseObject(responseJson, Map.class);
                }else {
                    log.warn("getAllProducts error. Account" + joomPmsAccount.getAccountNumber() + " Message"
                            + responseJson);
                }

                break;
            }catch (Exception e) {
                if (isNeedRetry(e)) {
                    log.warn("Account" + joomPmsAccount.getAccountNumber() + "超时重做");
                    continue;
                }

                // 这个异常不需要打印
                if (!StringUtils.contains(e.getMessage(), "Connection pool shut down")) {
                    log.warn("API getAllProducts Account" + joomPmsAccount.getAccountNumber() + e.getMessage());
                }

                return;
            }finally {
                HttpClientUtils.closeQuietly(httpResponse);
            }
        }

        if (productDetails == null || productDetails.isEmpty()) {
            return;
        }

        List<Object> productList = (List<Object>) productDetails.get("data");

        if (productList == null || productList.isEmpty()) {
            return;
        }

        List<JoomItem> createProducts = new ArrayList<JoomItem>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        TimeZone tz = TimeZone.getTimeZone("Asia/Shanhai");
        sdf.setTimeZone(tz);

        for (Object product : productList) {

            Map<String, Object> productParamObj = (Map<String, Object>) product;

            Map<String, Object> productParam = (Map<String, Object>) productParamObj.get("Product");

            if (productParam == null) {
                continue;
            }

            List<JoomItem> joomProducts = JoomProductCommon.getJoomProducts(productParam, joomPmsAccount);
            createProducts.addAll(joomProducts);

        } // end for item

        joomItemService.batchInsertBySync(createProducts);

        paging = (Map<String, Object>) productDetails.get("paging");

        if (paging != null && paging.size() > 0) {
            String next = paging.get("next").toString();

            if (StringUtils.isNotBlank(next)) {
                StringBuffer nextUrl = new StringBuffer(next);
                nextPage(nextUrl);
            }
        }
    }
}
