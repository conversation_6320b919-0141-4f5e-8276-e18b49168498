package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class JoomCategoryDescConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column joom_category_desc_config.id
     */
    private Integer id;

    /**
     * 二级类目中文名 database column joom_category_desc_config.two_category_cn
     */
    private String twoCategoryCn;

    /**
     * 叶子类目英文名 database column joom_category_desc_config.leaf_category_en
     */
    private String leafCategoryEn;

    /**
     * 叶子类目中文名 database column joom_category_desc_config.leaf_category_cn
     */
    private String leafCategoryCn;

    /**
     * 叶子类目code database column joom_category_desc_config.leaf_category_code
     */
    private String leafCategoryCode;

    /**
     * 类目完整代码 database column joom_category_desc_config.full_category_code
     */
    private String fullCategoryCode;

    /**
     * 新增描述 database column joom_category_desc_config.add_desc
     */
    private String addDesc;

    /**
     * 创建人 database column joom_category_desc_config.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column joom_category_desc_config.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column joom_category_desc_config.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column joom_category_desc_config.update_date
     */
    private Timestamp updateDate;
}