package com.estone.erp.publish.joom.BO;

import com.estone.erp.publish.platform.util.Platform;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/3 10:56
 * @description
 */
@Data
public class RedisSkuInfoBO {

    //sku
    private String s;
    //总销量
    private Long q;
    //分平台7天销量
    private Map<String, Integer> qMap;

    public Map<String, Integer> generateSevenDayMap() {
        Map<String, Integer> map = new HashMap<>(7);
        for (Platform platform : Platform.values()) {
            map.put(platform.name(), 0);
            for (Map.Entry<String, Integer> entry : qMap.entrySet()) {
                if(platform.name().equalsIgnoreCase(entry.getKey())){
                    map.put(platform.name(), entry.getValue());
                    break;
                }
            }
        }
        return map;
    }
}
