package com.estone.erp.publish.joom.handler;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SalesStatisticsUtil;
import com.estone.erp.publish.common.enums.SalesStatisticsRoleTypeEnum;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsSalesStatisticsDataRequest;
import com.estone.erp.publish.elasticsearch2.service.EsSalesStatisticsDataService;
import com.estone.erp.publish.joom.BO.DashboardLoadContext;
import com.estone.erp.publish.joom.BO.SalesStatisticsDataVO;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.pmssalePublicData.api.PublishDataApiHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

/**
 * 首页看板数据处理
 * <AUTHOR>
 * @date 2023-01-06 10:32
 */
@Slf4j
@Component
public class DashboardHandler {

    @Autowired
    private EsSalesStatisticsDataService salesStatisticsDataService;
    @Autowired
    private PublishDataApiHandler publishDataApiHandler;


    /**
     * 看板数据目前支持统计的平台
     */
    private final static List<String> SUPPORT_PLATFORM = SaleChannelEnum.getAllPublishSaleChannel();



    /**
     * 加载默认销售数据
     * @param currentUser   当前登录用户
     */
    public SalesStatisticsDataVO loadDefaultSaleData(NewUser currentUser) {
        // 创建默认查询数据看板加载上下文
        DashboardLoadContext context = createDefaultQueryContext(currentUser);
        // 超级管理员获取所有平台数据
        if (context.getRoleTypeEnum() == SalesStatisticsRoleTypeEnum.SUPER_ADMIN) {
            EsSalesStatisticsData superAdminData = loadSuperAdminData();
            return SalesStatisticsDataVO.convent2VO(superAdminData);
        }
        // 平台主管
        if (context.getRoleTypeEnum() == SalesStatisticsRoleTypeEnum.SUPERVISOR) {
            EsSalesStatisticsData supervisorData = loadSupervisorData(context.getPlatform().get(0));
            return SalesStatisticsDataVO.convent2VO(supervisorData);
        }
        // 销售
        EsSalesStatisticsData salesStatisticsData = loadSalesData(context.getPlatform().get(0), Collections.singletonList(currentUser.getEmployeeId()));
        return SalesStatisticsDataVO.convent2VO(salesStatisticsData);
    }

    /**
     * 获取超级管理员看板数据
     * 获取所有平台的主管数据进行聚合
     */
    private EsSalesStatisticsData loadSuperAdminData() {
        EsSalesStatisticsDataRequest request = new EsSalesStatisticsDataRequest();
        request.setMonth(DateUtils.getYesterdayYearOfMonth(LocalDate.now().toString()));
        request.setSaleId(0);
        request.setRoleType(SalesStatisticsRoleTypeEnum.SUPERVISOR.getCode());
        request.setPlatformList(SUPPORT_PLATFORM);
        List<EsSalesStatisticsData> salesStatisticsDataList = salesStatisticsDataService.listSalesStatisticsData(request);
        if (CollectionUtils.isEmpty(salesStatisticsDataList)) {
            return null;
        }
        // 聚合到一条数据
        return SalesStatisticsUtil.mergeSalesData(salesStatisticsDataList);
    }

    /**
     * 按平台加载主管数据
     * @param platform 平台
     */
    public EsSalesStatisticsData loadSupervisorData(String platform) {
        if (!SUPPORT_PLATFORM.contains(platform)) {
            throw new NoSuchElementException(String.format("看板数据暂时不支持[%s]平台!",platform));
        }
        EsSalesStatisticsDataRequest request = new EsSalesStatisticsDataRequest();
        request.setMonth(DateUtils.getYesterdayYearOfMonth(LocalDate.now().toString()));
        request.setSaleId(0);
        request.setRoleType(SalesStatisticsRoleTypeEnum.SUPERVISOR.getCode());
        request.setPlatform(platform);
        List<EsSalesStatisticsData> salesStatisticsDataList = salesStatisticsDataService.listSalesStatisticsData(request);
        if (CollectionUtils.isEmpty(salesStatisticsDataList)) {
            return null;
        }
        return salesStatisticsDataList.get(0);
    }

    /**
     * 按平台加载销售数据
     * @param platform 平台
     */
    public EsSalesStatisticsData loadSalesData(String platform, List<Integer> saleIds) {
        EsSalesStatisticsDataRequest request = new EsSalesStatisticsDataRequest();
        request.setMonth(DateUtils.getYesterdayYearOfMonth(LocalDate.now().toString()));
        request.setSaleIdList(saleIds);
        request.setRoleType(SalesStatisticsRoleTypeEnum.SALE.getCode());
        request.setPlatform(platform);
        List<EsSalesStatisticsData> salesStatisticsDataList = salesStatisticsDataService.listSalesStatisticsData(request);
        if (CollectionUtils.isEmpty(salesStatisticsDataList)) {
            return null;
        }
        if (salesStatisticsDataList.size() > 1) {
            return SalesStatisticsUtil.mergeSalesData(salesStatisticsDataList);
        }
        return salesStatisticsDataList.get(0);
    }


    public DashboardLoadContext createDefaultQueryContext(NewUser currentUser) {
        SalesStatisticsRoleTypeEnum currentUserRoleType = getCurrentUserRoleType(currentUser);
        DashboardLoadContext context = new DashboardLoadContext();
        context.setCurrentUser(currentUser);
        context.setRoleTypeEnum(getCurrentUserRoleType(currentUser));
        if (currentUserRoleType == SalesStatisticsRoleTypeEnum.SUPER_ADMIN) {
            context.setPlatform(SUPPORT_PLATFORM);
        }else {
            List<String> platform = getPlatformByCurrentUser(currentUser);
            context.setPlatform(platform);
        }
        return context;
    }

    /**
     * 获取当前用户的数据角色类型
     * @return  SalesStatisticsRoleTypeEnum
     */
    public SalesStatisticsRoleTypeEnum getCurrentUserRoleType(NewUser currentUser) {
        if (NewUsermgtUtils.isSuperAdmin(currentUser)) {
            // 超级管理员
            return SalesStatisticsRoleTypeEnum.SUPER_ADMIN;
        }
        // 是否为平台主管
        for (String platform : SUPPORT_PLATFORM) {
            if (NewUsermgtUtils.isSuperAdminOrEquivalent(currentUser, platform)) {
                return SalesStatisticsRoleTypeEnum.SUPERVISOR;
            }
        }
        // 销售
        return SalesStatisticsRoleTypeEnum.SALE;
    }

    /**
     * 获取当前用户所属平台
     * @param currentUser   当前登录用户
     * @return SaleChannel
     */
    private List<String> getPlatformByCurrentUser(NewUser currentUser) {
        List<String> userPlatforms = NewUsermgtUtils.getUserPlatformByEmployeeNo(currentUser.getEmployeeNo());
        if (CollectionUtils.isEmpty(userPlatforms)) {
            throw new NoSuchElementException(String.format("[%s-%s]非超管用户获取用户所属平台为空!",currentUser.getName(), currentUser.getEmployeeNo()));
        }

        List<String> supportPlatform = userPlatforms.stream().filter(SUPPORT_PLATFORM::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supportPlatform)) {
            throw new NoSuchElementException(String.format("[%s-%s]暂时不支持该用户所属平台!",currentUser.getName(), currentUser.getEmployeeNo()));
        }
        return supportPlatform;
    }

    /**
     * 获取店铺数据
     * @param platform        平台
     * @param accountNumbers  店铺
     */
    public SalesStatisticsDataVO loadAccountData(String platform, List<String> accountNumbers) {
        SaleChannelEnum channelEnum = SaleChannelEnum.getSaleChannelByName(platform);
        if (channelEnum == null || !SUPPORT_PLATFORM.contains(platform)) {
            throw new IllegalArgumentException(String.format("看板数据暂时不支持[%s]平台!",platform));
        }
        ApiResult<EsSalesStatisticsData> statisticsDataAccountData = publishDataApiHandler.getStatisticsDataAccountData(channelEnum.getChannelName(), accountNumbers);
        return checkClientResult(statisticsDataAccountData);
    }

    public SalesStatisticsDataVO checkClientResult(ApiResult<EsSalesStatisticsData> result) {
        if (!result.isSuccess()) {
            throw new RuntimeException(result.getErrorMsg());
        }
        EsSalesStatisticsData salesStatisticsData = result.getResult();
        if (salesStatisticsData == null) {
            throw new NoSuchElementException("店铺统计数据为空！！");
        }
        return SalesStatisticsDataVO.convent2VO(salesStatisticsData);
    }

}
