package com.estone.erp.publish.joom.util.modal;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 修改国家运费（页面修改线下国家运费）
 * @Auther yucm
 * @Date 2020/10/12
 */
@Getter
@Setter
public class UpdateCountryShipping {

    /**
     * 平台商品ID
     */
    private String joomItemId;

    /**
     * 平台子sku
     */
    private String sku;

    /**
     * 系统货号
     */
    private String articleNumber;

    /**
     * 状态
     */
    private String status;

    /**
     * 账号
     */
    private String account;

    /**
     * 渠道ID
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 快递等级
     */
    private String tierName;

    /**
     * 承诺到货时间
     */
    private String warrantyDurationDays;

    /**
     * 线下运费
     */
    private Double shippingPrice;

    /**
     * 币种
     */
    private String currency;

    /**
     * 渠道可达到国家
     */
    private List<String> enabledInCountries;
}
