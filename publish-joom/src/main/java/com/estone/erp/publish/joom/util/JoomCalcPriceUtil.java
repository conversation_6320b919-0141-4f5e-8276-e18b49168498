package com.estone.erp.publish.joom.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.joom.util.modal.JoomCalcBean;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/3/2 16:00
 * @description joom 新试算器算价工具类
 */
@Slf4j
public class JoomCalcPriceUtil {

    /**
     * 批量计算价格
     * @param batchList
     */
    public static ApiResult<List<BatchPriceCalculatorResponse>> batchCalcPrice(List<JoomCalcBean> batchList) {
        //参数转化
        String platform = "JOOM";
        List<BatchPriceCalculatorRequest> params = new ArrayList<>(batchList.size());
        for (JoomCalcBean bean : batchList) {
            BatchPriceCalculatorRequest obj = new BatchPriceCalculatorRequest();
            obj.setSaleChannel(platform);
            if(StringUtils.isNotBlank(bean.getPlatform())){
                obj.setSaleChannel(bean.getPlatform());
            }
            obj.setArticleNumber(bean.getArticleNumber());
            obj.setShippingMethod(bean.getShippingMethodCode());
            obj.setGrossProfitRate(bean.getProfitMargin());
            obj.setSite(bean.getCountryCode());
            obj.setCountryCode(bean.getCountryCode());
            obj.setQuantity(1);
            //joom默认运费为0
            obj.setPostFeeIsZero(true);

            params.add(obj);
        }

        //拼接参数
        ApiRequestParam<String> req = new ApiRequestParam<>();
        req.setMethod("batchCalculatePrice");
        req.setArgs(JSON.toJSONString(params));
        //请求
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(req);
        return listApiResult;
    }

    public static ApiResult<?> calcPrice(List<JoomCalcBean> batchList){
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = batchCalcPrice(batchList);
        //处理结果
        if(!listApiResult.isSuccess()){
            return ApiResult.newError(listApiResult.getErrorMsg());
        }

        List<Map<String, Object>> list = new ArrayList<>(batchList.size());
        List<BatchPriceCalculatorResponse> result = listApiResult.getResult();
        Set<String> msg = new HashSet<>();
        for (int i = 0; i < result.size(); i++) {
            JoomCalcBean calcBean = batchList.get(i);
            BatchPriceCalculatorResponse bean = result.get(i);
            if(!bean.getIsSuccess()){
                msg.add(bean.getErrorMsg());
                continue;
            }

            //价格（人民币）
            Double price = bean.getPrice();
            //价格（外币）
            Double foreignPrice = bean.getForeignPrice() == null ? 0.0 : bean.getForeignPrice();
            //发货费（人民币）
            Double shippingCost = bean.getShippingCost();
            //发货费（外币）
            Double foreignShippingCost = bean.getForeignShippingCost() == null ? 0.0 : bean.getForeignShippingCost();

            /*
            // 产品价格=销售成本价/【（1-平台佣金费率-毛利率）*汇率】
            Double prdPrice = foreignPrice / ((1 - 0.15 - calcBean.getProfitMargin()) * bean.getExchangeRate());
            prdPrice = new BigDecimal(prdPrice).setScale(2, RoundingMode.UP).doubleValue();
            // 运费=物流费用/【（1-平台佣金费率-毛利率）*汇率】
            Double shippingProfitMargin = calcBean.getShippingProfitMargin() == null ? 0.18 : calcBean.getShippingProfitMargin();
            Double prdShippingCost = shippingCost / ((1 - 0.15 - shippingProfitMargin) * bean.getExchangeRate());
            prdShippingCost = new BigDecimal(prdShippingCost).setScale(2, RoundingMode.UP).doubleValue();
            log.warn("原始价格==>" + foreignPrice + ",计算后的运费==>" + prdPrice);
            log.warn("原始运费==>" + shippingCost + ",计算后的运费==>" + prdShippingCost);*/

            Map<String, Object> successResult = new HashMap<>(1);
            successResult.put("articleNumber", bean.getArticleNumber());
            foreignPrice = BigDecimal.valueOf(foreignPrice - foreignShippingCost).setScale(2, RoundingMode.UP).doubleValue();
            successResult.put("price", foreignPrice);
            foreignShippingCost = BigDecimal.valueOf(foreignShippingCost).setScale(2, RoundingMode.UP).doubleValue();
            successResult.put("shippingCost", foreignShippingCost);
            list.add(successResult);
        }
        ApiResult<List<Map<String, Object>>> apiResult = ApiResult.newSuccess(list);
        if(!msg.isEmpty()){
            apiResult.setErrorMsg(msg.toString());
            apiResult.setSuccess(false);
        }
        return apiResult;
    }


}
