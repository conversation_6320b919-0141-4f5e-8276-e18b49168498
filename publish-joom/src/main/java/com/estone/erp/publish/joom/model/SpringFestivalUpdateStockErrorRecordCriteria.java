package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2025-01-03 17:53:54
 */
public class SpringFestivalUpdateStockErrorRecordCriteria extends SpringFestivalUpdateStockErrorRecord {
    private static final long serialVersionUID = 1L;

    public SpringFestivalUpdateStockErrorRecordExample getExample() {
        SpringFestivalUpdateStockErrorRecordExample example = new SpringFestivalUpdateStockErrorRecordExample();
        SpringFestivalUpdateStockErrorRecordExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getPlatform())) {
            criteria.andPlatformEqualTo(this.getPlatform());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (StringUtils.isNotBlank(this.getItemId())) {
            criteria.andItemIdEqualTo(this.getItemId());
        }
        if (StringUtils.isNotBlank(this.getErrorMsg())) {
            criteria.andErrorMsgEqualTo(this.getErrorMsg());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        return example;
    }
}