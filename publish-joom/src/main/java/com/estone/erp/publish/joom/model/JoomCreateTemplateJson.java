package com.estone.erp.publish.joom.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.estone.erp.publish.joom.util.modal.JoomSku;

public class JoomCreateTemplateJson {

    private String name;

    private String description;

    /**
     * 包含侵权词
     */
    private Map<String, String> infringingWords;

    private List<String> productImages = new ArrayList<String>();

    private List<JoomSku> skus = new ArrayList<JoomSku>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getProductImages() {
        return productImages;
    }

    public void setProductImages(List<String> productImages) {
        this.productImages = productImages;
    }

    public List<JoomSku> getSkus() {
        return skus;
    }

    public void setSkus(List<JoomSku> skus) {
        this.skus = skus;
    }

    public Map<String, String> getInfringingWords() {
        return infringingWords;
    }

    public void setInfringingWords(Map<String, String> infringingWords) {
        this.infringingWords = infringingWords;
    }
}
