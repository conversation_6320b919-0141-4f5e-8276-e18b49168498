package com.estone.erp.publish.joom.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.enums.SalesStatisticsRoleTypeEnum;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.joom.BO.DashboardRequestDO;
import com.estone.erp.publish.joom.BO.SalesStatisticsDataVO;
import com.estone.erp.publish.joom.handler.DashboardHandler;
import com.estone.erp.publish.joom.service.DashboardService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-01-06 10:34
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private DashboardHandler dashboardHandler;

    /**
     * 加载首页数据
     * @param param        页面入参
     * @param currentUser  当前登录用户
     */
    @Override
    public SalesStatisticsDataVO loadIndexData(DashboardRequestDO param, NewUser currentUser) {
        if (param.isDefaultQuery()) {
            // 默认查询,加载缓存数据
            return dashboardHandler.loadDefaultSaleData(currentUser);
        }
        // 按条件查询
        SalesStatisticsRoleTypeEnum roleTypeEnum = dashboardHandler.getCurrentUserRoleType(currentUser);
        // 仅平台 超管 主管
        if (param.isOnlyPlatformQuery() && roleTypeEnum != SalesStatisticsRoleTypeEnum.SALE) {
            EsSalesStatisticsData salesStatisticsData = dashboardHandler.loadSupervisorData(param.getPlatform());
            return SalesStatisticsDataVO.convent2VO(salesStatisticsData);
        }
        // 仅销售
        if (param.isOnlySaleQuery()) {
            List<String> employeeIds = convent2EmployeeIds(param.getSaleManList());
            List<Integer> saleIds = employeeIds.stream().map(Integer::parseInt).collect(Collectors.toList());
            EsSalesStatisticsData salesStatisticsData = dashboardHandler.loadSalesData(param.getPlatform(), saleIds);
            return SalesStatisticsDataVO.convent2VO(salesStatisticsData);
        }

        // 销售店铺
        List<String> accountNumber = param.getAccountNumber();
        if (CollectionUtils.isEmpty(accountNumber)) {
            List<SaleAccount> saleAccounts = EsAccountUtils.getAccountBySaleId(param.getPlatform(), Lists.newArrayList(String.valueOf(currentUser.getEmployeeId())));
            if (CollectionUtils.isNotEmpty(saleAccounts)) {
                accountNumber = saleAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSaleManList())) {
            List<String> employeeIds = convent2EmployeeIds(param.getSaleManList());
            // 获取销售管理的店铺
            List<SaleAccount> saleAccounts = EsAccountUtils.getAccountBySaleId(param.getPlatform(), employeeIds);
            if (CollectionUtils.isNotEmpty(saleAccounts)) {
                List<String> saleAccountNumbers = saleAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
                // 取交集
                accountNumber = accountNumber.stream().filter(saleAccountNumbers::contains).collect(Collectors.toList());
            }
        }
        return dashboardHandler.loadAccountData(param.getPlatform(), accountNumber);
    }

    private List<String> convent2EmployeeIds(List<String> saleManList) {
        List<String> employeeIds = new ArrayList<>();
        for (String no : saleManList) {
            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(no);
            if (!newUserApiResult.isSuccess()) {
                continue;
            }
            NewUser newUser = newUserApiResult.getResult();
            if (newUser != null) {
                Integer employeeId = newUser.getEmployeeId();
                employeeIds.add(String.valueOf(employeeId));
            }
        }
        if (CollectionUtils.isEmpty(employeeIds)) {
            throw new NoSuchElementException(String.format("未查询到可用销售数据：%s", JSON.toJSONString(saleManList)));
        }
        return employeeIds;
    }
}
