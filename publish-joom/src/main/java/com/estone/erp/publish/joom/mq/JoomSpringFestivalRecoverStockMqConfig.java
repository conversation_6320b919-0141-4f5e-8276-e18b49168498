package com.estone.erp.publish.joom.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class JoomSpringFestivalRecoverStockMqConfig {
    private int joomSpringFestivalRecoverStockMqConsumers;
    private int joomSpringFestivalRecoverStockMqPrefetchCount;
    private boolean joomSpringFestivalRecoverStockMqListener;

    @Bean
    public Queue joomSpringFestivalRecoverStock() {
        return new Queue(PublishQueues.JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE,true);
    }

    @Bean
    public Binding joomSpringFestivalRecoverStockBinding() {
        return new Binding(PublishQueues.JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.JOOM_API_DIRECT_EXCHANGE,
                PublishQueues.JOOM_SPRING_FESTIVAL_RECOVER_STOCK_KEY, null);
    }

    @Bean
    public JoomSpringFestivalRecoverStockMqListener joomSpringFestivalRecoverStockMqListener() {
        return new JoomSpringFestivalRecoverStockMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer joomSpringFestivalRecoverStockMqListenerContainer(
            JoomSpringFestivalRecoverStockMqListener joomSpringFestivalRecoverStockMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE, joomSpringFestivalRecoverStockMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (joomSpringFestivalRecoverStockMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(joomSpringFestivalRecoverStockMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(joomSpringFestivalRecoverStockMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
