package com.estone.erp.publish.joom.mq;


import com.estone.erp.common.mq.PublishQueues;
import lombok.Data;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 产品系统推送管理单品新状态消息队列配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class JoomProductNewStateMqConfig {

    private boolean joomProductSingleNewStatusQueueEnable;
    private int joomProductSingleNewStatusQueueConsumer;
    private int joomProductSingleNewStatusQueuePrefetchCount;

    @Bean
    public Queue productSingleNewStatusJoomQueue() {
        return new Queue(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_JOOM_QUEUE);
    }
    @Bean
    public Binding productSingleNewStatusJoomQueueBinging(Queue productSingleNewStatusJoomQueue, FanoutExchange productSingleNewStatusToPublishFanout) {
        return BindingBuilder
                .bind(productSingleNewStatusJoomQueue)
                .to(productSingleNewStatusToPublishFanout);
    }
    @Bean
    public JoomProductNewStateMqListener joomProductNewStateMqListener() {
        return new JoomProductNewStateMqListener();
    }
    @Bean
    public SimpleMessageListenerContainer joomProductNewStateMQListenerContainer(
            JoomProductNewStateMqListener joomProductNewStateMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (joomProductSingleNewStatusQueueEnable) {
            container.setQueueNames(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_JOOM_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(joomProductSingleNewStatusQueuePrefetchCount);
            container.setConcurrentConsumers(joomProductSingleNewStatusQueueConsumer);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(joomProductNewStateMqListener);
        }
        return container;
    }

}
