package com.estone.erp.publish.joom.call;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.model.Host;
import com.estone.erp.publish.joom.model.HostCriteria;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.service.HostService;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JoomCreateItemCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "product/add";

    /**
     * @param joomAccount
     */
    public JoomCreateItemCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    private String exchangeStr(String oldtitle) {
        List<String> strList = Arrays.asList(StringUtils.split(oldtitle, " ")).stream().distinct()
                .collect(Collectors.toList());
        List<String> last3List = new ArrayList<>();
        StringBuffer pre = new StringBuffer();

        for (int i = 0; i < strList.size(); i++) {
            if (i < strList.size() - 3) {
                if (i == 0) {
                    pre.append(strList.get(i));
                }
                else {
                    pre.append(" ").append(strList.get(i));
                }
            }
            else {

                last3List.add(strList.get(i));
            }

        }

        Collections.shuffle(last3List);
        StringBuffer newStr = new StringBuffer();
        newStr.append(" ").append(last3List.get(0)).append(" ").append(last3List.get(1)).append(" ")
                .append(last3List.get(2));
        return pre.toString() + newStr.toString();
    }

    @SuppressWarnings("unchecked")
    public ResponseJson createItem(JoomItem joomItem, JoomItem childJoomItem) {
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);

        httpClient = HttpClientUtils.createSSLClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        // 打乱标题
        String itemTitle = joomItem.getItemTitle();

        HostService hostService = SpringUtils.getBean("hostService", HostService.class);

        HostCriteria query = new HostCriteria();
        query.setSellerId(joomItem.getItemSeller());

        Host host = null;
        List<Host> queryHosts = hostService.selectByExample(query.getExample());
        if (CollectionUtils.isNotEmpty(queryHosts)) {
            host = queryHosts.get(0);
        }

        if (StringUtils.isNotEmpty(itemTitle)) {
            if (host != null && (host.getHostSite().equals("image.boruite.ltd")
                    || host.getHostSite().equals("image.hengyue.site"))) {
                String newtitle = "";
                boolean flag = true;
                while (flag) {
                    newtitle = this.exchangeStr(itemTitle);
                    if (!itemTitle.equals(newtitle)) {
                        flag = false;
                    }
                }

                // System.out.println(oldLast3Words);
                // System.out.println(newLast3Words);
                // System.out.println(joomItem.getItemTitle().substring(0,
                // itemTitle.length()-3)+newLast3Words);

                joomItem.setItemTitle(newtitle);
            }
        }

        standardNameValue.add(new BasicNameValuePair("name", joomItem.getItemTitle()));

        standardNameValue.add(new BasicNameValuePair("description", joomItem.getDescription()));

        standardNameValue.add(new BasicNameValuePair("tags", joomItem.getTags()));

        standardNameValue.add(new BasicNameValuePair("sku", childJoomItem.getSku()));

        standardNameValue.add(new BasicNameValuePair("parent_sku", joomItem.getSku()));

        if(childJoomItem.getInventory() != null){
            standardNameValue.add(new BasicNameValuePair("inventory", childJoomItem.getInventory().toString()));
        }
        if(childJoomItem.getPrice() != null){
            standardNameValue.add(new BasicNameValuePair("price", childJoomItem.getPrice().toString()));
        }
        if(childJoomItem.getShippingCost() != null){
            standardNameValue.add(new BasicNameValuePair("shipping", childJoomItem.getShippingCost().toString()));
        }
        // 如果产品没有危险，应使用“notDangerous”。
        standardNameValue.add(new BasicNameValuePair("dangerous_kind", joomItem.getDangerousKind()));

        if (joomItem.getMsrp() != null) {
            standardNameValue.add(new BasicNameValuePair("msrp", childJoomItem.getMsrp().toString()));
        }

        if (StringUtils.isNotEmpty(joomItem.getShippingTime())) {
            standardNameValue.add(new BasicNameValuePair("shipping_time", childJoomItem.getShippingTime()));
        }

        standardNameValue.add(new BasicNameValuePair("product_main_image", joomItem.getMainImage()));

        standardNameValue.add(new BasicNameValuePair("variant_main_image", childJoomItem.getMainImage()));

        // if (StringUtils.isNotEmpty(wishItem.getBrand()))
        // {
        // standardNameValue.add(new BasicNameValuePair("brand",
        // wishItem.getBrand()));
        // }

        if (StringUtils.isNotEmpty(joomItem.getLandingPageUrl())) {
            standardNameValue.add(new BasicNameValuePair("landing_page_url", joomItem.getLandingPageUrl()));
        }

        if (StringUtils.isNotEmpty(joomItem.getUpc())) {
            standardNameValue.add(new BasicNameValuePair("upc", joomItem.getUpc()));
        }

        // 特效图片
        if (StringUtils.isNotBlank(joomItem.getExtraImages())) {
            standardNameValue.add(new BasicNameValuePair("extra_images", joomItem.getExtraImages()));
        }

        // 设置第一个变体
        if (StringUtils.isNotEmpty(childJoomItem.getColor())) {
            standardNameValue.add(new BasicNameValuePair("color", childJoomItem.getColor()));
        }

        if (StringUtils.isNotEmpty(childJoomItem.getSize())) {
            standardNameValue.add(new BasicNameValuePair("size", childJoomItem.getSize()));
        }

        if(childJoomItem.getShippingCost() != null){
            standardNameValue.add(new BasicNameValuePair("shipping", childJoomItem.getShippingCost().toString()));
        }

        // 产品重量
        if (childJoomItem.getShippingWeight() != null) {
            standardNameValue
                    .add(new BasicNameValuePair("shipping_weight", childJoomItem.getShippingWeight().toString()));
        }

        if(childJoomItem.getShippingLength() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_length", childJoomItem.getShippingLength().toString()));
        }
        if(childJoomItem.getShippingWidth() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_width", childJoomItem.getShippingWidth().toString()));
        }
        if(childJoomItem.getShippingHeight() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_height", childJoomItem.getShippingHeight().toString()));
        }

        /*
         * if (childJoomItem != null){ String variation =
         * createVariation(childJoomItem); if
         * (StringUtils.isNotBlank(variation)){ Map<String,String> variantMap =
         * new HashMap<>(); variantMap.put("variant", variation); String
         * jsonString = JSON.toJSONString(variantMap); standardNameValue.add(new
         * BasicNameValuePair("variants",jsonString)); } }
         */

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));

            log.debug("url:" + url.toString());

            HttpPost httpPost = new HttpPost();

            try {
                httpPost.setURI(new URI(url.toString()));
            }
            catch (URISyntaxException e) {
            }

            Map<String, Object> productDetails = null;

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            // 重试3次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpPost);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();

                    String responseJson = EntityUtils.toString(entity);

                    rsp.setMessage(responseJson);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        productDetails = JSON.parseObject(responseJson, Map.class);
                    }
                    else {
                        log.error("JoomCreateProdcutCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message" + responseJson);
                    }

                    break;

                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("Account " + joomPmsAccount.getAccountNumber() + " Times" + String.valueOf(retryTimes)
                                + "超时重做" + e.getMessage());
                        continue;
                    }

                    log.error(
                            "API JoomCreateProdcutCall Account " + joomPmsAccount.getAccountNumber() + e.getMessage());
                    rsp.setMessage(e.getMessage());
                    return rsp;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            if (httpResponse == null || productDetails == null || productDetails.isEmpty()) {
                return rsp;
            }

            Integer code = (Integer) productDetails.get("code");

            Map<String, Object> productParam = (Map<String, Object>) productDetails.get("data");

            if (code != null && code == 0) {
                // 创建产品成功
                rsp.setStatus(StatusCode.SUCCESS);
            }
            else {
                String message = (String) productParam.get("message");

                rsp.setMessage(code + message);
            }

            return rsp;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }

    }

}
