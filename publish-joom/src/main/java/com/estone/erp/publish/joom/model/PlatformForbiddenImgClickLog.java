package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class PlatformForbiddenImgClickLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column platform_forbidden_img_click_log.id
     */
    private Long id;

    /**
     * 员工编号 database column platform_forbidden_img_click_log.employee_no
     */
    private Integer employeeNo;

    /**
     * 员工信息 database column platform_forbidden_img_click_log.employee_info
     */
    private String employeeInfo;

    /**
     * 服务平台 database column platform_forbidden_img_click_log.service_platform
     */
    private String servicePlatform;

    /**
     * sku database column platform_forbidden_img_click_log.sku
     */
    private String sku;

    /**
     * 点击次数 database column platform_forbidden_img_click_log.click_num
     */
    private Integer clickNum;

    /**
     * 创建时间 database column platform_forbidden_img_click_log.create_time
     */
    private Timestamp createTime;

    /**
     * 更新时间 database column platform_forbidden_img_click_log.update_time
     */
    private Timestamp updateTime;
}