package com.estone.erp.publish.joom.api.thread;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.call.JoomUpdateVariationCall;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.JoomTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class EditJoomItemSizeThread implements Runnable {
    private CountDownLatch countDownLatch;
    private JoomItem joomItem;
    private ResponseJson response;
    private FeedTaskService feedTaskService;
    private String userName;

    public EditJoomItemSizeThread(JoomItem joomItem, CountDownLatch countDownLatch, ResponseJson response, FeedTaskService feedTaskService, String userName) {
        super();
        this.joomItem = joomItem;
        this.countDownLatch = countDownLatch;
        this.response = response;
        this.feedTaskService = feedTaskService;
        this.userName = userName;
    }

    @Override
    public void run() {
        if (StringUtils.isBlank(joomItem.getItemSeller())) {
            response.getErrors()
                    .add(new ResponseError("帐号为空", joomItem.getJoomItemId() + "," + joomItem.getSku(), "卖家帐号为空！"));
        }

        try {
            SaleAccountAndBusinessResponse joomPmsAccount = 
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, joomItem.getItemSeller());

            JoomUpdateVariationCall call = new JoomUpdateVariationCall(joomPmsAccount);
            ResponseJson json = call.updateVariation(joomItem);

            try {
                FeedTask feedTask = new FeedTask();
                feedTask.setPlatform(Platform.Joom.name());
                feedTask.setTableIndex();
                feedTask.setAssociationId(joomItem.getJoomItemId());
                feedTask.setAccountNumber(joomPmsAccount.getAccountNumber());
                feedTask.setArticleNumber(joomItem.getArticleNumber());
                feedTask.setTaskType(JoomTaskTypeEnum.UPDATE_SIZE.getStatusMsgEn());
                feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                feedTask.setResultStatus(json.isSuccess() ? ResultStatusEnum.RESULT_SUCCESS.getStatusCode() : ResultStatusEnum.RESULT_FAIL.getStatusCode());
                feedTask.setResultMsg(json.getMessage());
                feedTask.setCreatedBy(userName);
                feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                String shippingLength = joomItem.getShippingLength() == null ? "" : joomItem.getShippingLength().toString();
                String shippingWidth = joomItem.getShippingWidth() == null ? "" : joomItem.getShippingWidth().toString();
                String shippingHeight = joomItem.getShippingHeight() == null ? "" : joomItem.getShippingHeight().toString();
                //价格前
                feedTask.setAttribute1(String.format("长:%s,宽:%s,高:%s",shippingLength, shippingWidth, shippingHeight));
                //价格后
                feedTask.setAttribute2(String.format("长:%s,宽:%s,高:%s",joomItem.getInputShippingLength(), joomItem.getInputShippingWidth(), joomItem.getInputShippingHeight()));
                feedTaskService.insert(feedTask);
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }

//            JoomUpdateItemCall updateCall = new JoomUpdateItemCall(joomPmsAccount);
//            updateCall.updateDangerouserKind(joomItem);
            response.addError(new ResponseError(json.getStatus(), joomItem.getJoomItemId() + "," + joomItem.getArticleNumber(),
                    json.getMessage() + "-" + json.getErrors().toString()));
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            countDownLatch.countDown();
        }

    }

}
