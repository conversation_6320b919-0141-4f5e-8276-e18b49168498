package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.mapper.JoomUpdateStockZeroRecordsMapper;
import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecords;
import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecordsCriteria;
import com.estone.erp.publish.joom.model.JoomUpdateStockZeroRecordsExample;
import com.estone.erp.publish.joom.service.JoomUpdateStockZeroRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2025-01-03 14:57:29
 */
@Service("joomUpdateStockZeroRecordsService")
@Slf4j
public class JoomUpdateStockZeroRecordsServiceImpl implements JoomUpdateStockZeroRecordsService {
    @Resource
    private JoomUpdateStockZeroRecordsMapper joomUpdateStockZeroRecordsMapper;

    @Override
    public int countByExample(JoomUpdateStockZeroRecordsExample example) {
        Assert.notNull(example, "example is null!");
        return joomUpdateStockZeroRecordsMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomUpdateStockZeroRecords> search(CQuery<JoomUpdateStockZeroRecordsCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomUpdateStockZeroRecordsCriteria query = cquery.getSearch();
        JoomUpdateStockZeroRecordsExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomUpdateStockZeroRecordsMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomUpdateStockZeroRecords> joomUpdateStockZeroRecordss = joomUpdateStockZeroRecordsMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomUpdateStockZeroRecords> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomUpdateStockZeroRecordss);
        return result;
    }

    @Override
    public JoomUpdateStockZeroRecords selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return joomUpdateStockZeroRecordsMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<JoomUpdateStockZeroRecords> selectByExample(JoomUpdateStockZeroRecordsExample example) {
        Assert.notNull(example, "example is null!");
        return joomUpdateStockZeroRecordsMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomUpdateStockZeroRecords record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return joomUpdateStockZeroRecordsMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomUpdateStockZeroRecords record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomUpdateStockZeroRecordsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomUpdateStockZeroRecords record, JoomUpdateStockZeroRecordsExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return joomUpdateStockZeroRecordsMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return joomUpdateStockZeroRecordsMapper.deleteByPrimaryKey(ids);
    }


    @Override
    public List<JoomUpdateStockZeroRecords> getMinCreateTimeRecordList(List<String> accountList, List<String> articleNumberList, Integer lastId, Integer pageSize) {
        return joomUpdateStockZeroRecordsMapper.selectMinCreateTimeRecordList(accountList,articleNumberList,lastId, pageSize);
    }
}