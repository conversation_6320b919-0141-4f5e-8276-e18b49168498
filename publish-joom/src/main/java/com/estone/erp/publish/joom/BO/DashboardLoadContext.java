package com.estone.erp.publish.joom.BO;

import com.estone.erp.publish.common.enums.SalesStatisticsRoleTypeEnum;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.Data;

import java.util.List;

/**
 * 数据看板加载数据上下文
 * <AUTHOR>
 * @date 2023-01-06 10:43
 */
@Data
public class DashboardLoadContext {

    private NewUser currentUser;

    private SalesStatisticsRoleTypeEnum roleTypeEnum;

    private List<String> platform;

    private List<String> accountNumberList;

    private List<String> saleManList;




}
