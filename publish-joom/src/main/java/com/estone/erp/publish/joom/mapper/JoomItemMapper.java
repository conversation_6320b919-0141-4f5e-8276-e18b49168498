package com.estone.erp.publish.joom.mapper;

import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomItemExample;
import com.estone.erp.publish.system.skuSellAccountAmount.model.SkuSellAccountAmount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JoomItemMapper {
    int countByExample(JoomItemExample example);

    int countByHome(JoomItemExample example);

    int countJoomItemListByExample(JoomItemExample example);

    int deleteByExample(JoomItemExample example);

    int deleteByItemSellerAndProductId(@Param("itemSeller")String itemSeller, @Param("joomItemId")String joomItemId);

    int deleteByPrimaryKey(Integer itemId);

    int insert(JoomItem record);

    int insertSelective(JoomItem record);

    List<JoomItem> selectByExample(JoomItemExample example);

    List<JoomItem> selectJoomItemListByExample(JoomItemExample example);

    JoomItem selectByPrimaryKey(Long itemId);

    int updateByExampleSelective(@Param("record") JoomItem record, @Param("example") JoomItemExample example);

    int updateByExample(@Param("record") JoomItem record, @Param("example") JoomItemExample example);

    int updateByPrimaryKeySelective(JoomItem record);

    int updateByPrimaryKey(JoomItem record);

    void batchUpdateJoomItem(@Param("itemList") List<JoomItem> recordList);

    void batchUpdateProductJoomItem(@Param("itemList") List<JoomItem> recordList);

    void batchInsertJoomItem(@Param("itemList") List<JoomItem> createProducts);

    List<String> selectEmptyCategoryItem();

    int selectJoomOnsellingSkuCount(JoomItem joomItem);

    List<String> selectJoomOnSellingSkuByExample(JoomItemExample example);

    List<SkuSellAccountAmount> getJoomOnSellingSkuListingNum(@Param("articleNumberList")List<String> articleNumberList, @Param("record")JoomItem record, @Param("day")Integer day);

    List<String> listSkuByAccount(@Param("accountNumberList") List<String> accountNumberList, @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<JoomItem> selectItemIdBySku(@Param("skuList") List<String> skuList);

    List<String> pageListJoomSkuBySkuStatus(@Param("statusList") List<String> statusList, @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<JoomItem> selectItemByIdAndStockNotZero(@Param("itemIds") List<Integer> itemIds);
}