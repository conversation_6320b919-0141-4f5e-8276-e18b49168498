package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLog;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLogCriteria;
import com.estone.erp.publish.joom.model.AmazonAuthCaiZongLogExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-07 09:37:29
 */
public interface AmazonAuthCaiZongLogService {
    int countByExample(AmazonAuthCaiZongLogExample example);

    CQueryResult<AmazonAuthCaiZongLog> search(CQuery<AmazonAuthCaiZongLogCriteria> cquery);

    List<AmazonAuthCaiZongLog> selectByExample(AmazonAuthCaiZongLogExample example);

    AmazonAuthCaiZongLog selectByPrimaryKey(Integer id);

    int insert(AmazonAuthCaiZongLog record);

    int updateByPrimaryKeySelective(AmazonAuthCaiZongLog record);

    int updateByExampleSelective(AmazonAuthCaiZongLog record, AmazonAuthCaiZongLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);
}