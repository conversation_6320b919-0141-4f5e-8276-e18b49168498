package com.estone.erp.publish.joom.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.joom.mapper.JoomTemplateMapper;
import com.estone.erp.publish.joom.model.JoomTemplate;
import com.estone.erp.publish.joom.model.JoomTemplateExample;
import com.estone.erp.publish.joom.model.dto.JoomTemplateCriteria;
import com.estone.erp.publish.joom.service.JoomTemplateService;
import com.estone.erp.publish.joom.util.JoomTemplateUtils;
import com.estone.erp.publish.joom.util.modal.JoomSku;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> joom_template 2019-08-08 10:45:07
 */
@Service("joomTemplateService")
@Slf4j
public class JoomTemplateServiceImpl implements JoomTemplateService {
    @Resource
    private JoomTemplateMapper joomTemplateMapper;

    @Override
    public int countByExample(JoomTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return joomTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<JoomTemplate> search(CQuery<JoomTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomTemplateCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        // 数据权限控制
        CQueryResult<JoomTemplate> cQueryResult = new CQueryResult<>();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_JOOM);
        if (!superAdminOrEquivalent.isSuccess()) {
            cQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return cQueryResult;
        }
        Boolean isLock = query.getIsLock();
        String platform = SaleChannel.CHANNEL_JOOM;
        if (BooleanUtils.isNotTrue(isLock)) {
            // 人员权限
            Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, query.getCreatedBy(),
                    query.getCreateByList(), query.getSellerId(), query.getSellerIdList());
            if (BooleanUtils.isTrue(employeePair.getLeft())) {
                query.setCreateByList(employeePair.getRight());
            }
        }
        JoomTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomTemplate> joomTemplates = joomTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomTemplates);
        return result;
    }

    @Override
    public JoomTemplate selectByPrimaryKey(Integer templateId) {
        Assert.notNull(templateId, "templateId is null!");
        return joomTemplateMapper.selectByPrimaryKey(templateId);
    }

    @Override
    public List<JoomTemplate> selectByExample(JoomTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return joomTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(JoomTemplate record) {
        Assert.notNull(record, "record is null!");
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if(StringUtils.isNotBlank(WebUtils.getUserName())){
            record.setCreatedBy(WebUtils.getUserName());
            record.setLastUpdatedBy(WebUtils.getUserName());
        }
        return joomTemplateMapper.insert(record);
    }

    @Override
    public void batchInsert(List<JoomTemplate> recordList) {
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (JoomTemplate record : recordList) {
                record.setCreationDate(new Timestamp(System.currentTimeMillis()));
                record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if(StringUtils.isNotBlank(WebUtils.getUserName())){
                    record.setCreatedBy(WebUtils.getUserName());
                    record.setLastUpdatedBy(WebUtils.getUserName());
                }
            }
            joomTemplateMapper.batchInsertJoomTemplate(recordList);
        }
    }

    @Override
    public int insertSelective(JoomTemplate record) {
        Assert.notNull(record, "record is null!");
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        record.setCreatedBy(WebUtils.getUserName());
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if(StringUtils.isNotBlank(WebUtils.getUserName())){
            record.setLastUpdatedBy(WebUtils.getUserName());
        }
        return joomTemplateMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomTemplate record) {
        Assert.notNull(record, "record is null!");
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if(StringUtils.isNotBlank(WebUtils.getUserName())){
            record.setLastUpdatedBy(WebUtils.getUserName());
        }
        return joomTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateJoomTemplate(JoomTemplate record){
        Assert.notNull(record, "record is null!");
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));

        if(StringUtils.isNotBlank(WebUtils.getUserName())){
            record.setLastUpdatedBy(WebUtils.getUserName());
        }
        return joomTemplateMapper.updateJoomTemplate(record);
    }

    @Override
    public int updateByExampleSelective(JoomTemplate record, JoomTemplateExample example) {
        Assert.notNull(record, "record is null!");
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if(StringUtils.isNotBlank(WebUtils.getUserName())){
            record.setLastUpdatedBy(WebUtils.getUserName());
        }
        return joomTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public void batchUpdate(List<JoomTemplate> recordList) {
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (JoomTemplate record : recordList) {
                record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));

                if(StringUtils.isNotBlank(WebUtils.getUserName())){
                    record.setLastUpdatedBy(WebUtils.getUserName());
                }
            }
            joomTemplateMapper.batchUpdateJoomTemplate(recordList);
        }
    }

    @Override
    public int deleteByPrimaryKey(Integer templateId) {
        Assert.notNull(templateId, "templateId is null!");
        return joomTemplateMapper.deleteByPrimaryKey(templateId);
    }

    @Override
    public int deleteByExample(JoomTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return joomTemplateMapper.deleteByExample(example);
    }

    @Override
    public void batchDelete(List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            joomTemplateMapper.batchDelete(ids);
        }
    }

    @Override
    public String batchCopy(List<JoomTemplate> parentTemplateList) {
        int success = 0;
        int error = 0;
        List<JoomTemplate> updateParentTempList = new ArrayList<JoomTemplate>();
        List<JoomTemplate> copyTemplateUpdate = new ArrayList<JoomTemplate>();
        for (JoomTemplate parentItem : parentTemplateList) {
            // 该母版被copy 的次数
            Integer copyCount = parentItem.getCopyCount();
            if (copyCount == null) {
                copyCount = 0;
            }
            // 规定只能复制3次
            if (copyCount > 2) {
                error++;
                continue;
            }
            if (StringUtils.isNotBlank(parentItem.getSku())) {
                JoomTemplateCriteria query = new JoomTemplateCriteria();
                query.setCreatedBy(WebUtils.getUserName());
                // 查询模板
                query.setIsLock(false);
                query.setSku(parentItem.getSku());
                int i = joomTemplateMapper.countByExample(query.getExample());
                if (i > 0) {
                    error++;
                }
                else {
                    // 母版需要更新copy次数
                    JoomTemplate updateParentTemp = new JoomTemplate();
                    updateParentTemp.setTemplateId(parentItem.getTemplateId());
                    updateParentTemp.setCopyCount(copyCount + 1);
                    updateParentTemp.setIsLock(true);
                    updateParentTempList.add(updateParentTemp);

                    success++;
                    parentItem.setIsLock(false);
                    parentItem.setCreatedBy(null);
                    parentItem.setCreationDate(null);
                    parentItem.setLastUpdatedBy(null);
                    parentItem.setLastUpdateDate(null);

                    // 主图 "|"拼接 (默认模板有3张主图)
                    List<String> mainImageList = parentItem.getMainImageList();
                    int index = copyCount % mainImageList.size();
                    // 分配主图
                    String uploadMainImage = mainImageList.get(index);
                    parentItem.setMainImage(uploadMainImage);
                    // parentItem.setMainImage(JoomTemplateUtils.mainImageSortTemplate(joomTemplate.getMainImageList()));
                    // 分配标题
                    String name = parentItem.getName();
                    name = name + " ";
                    // 标签删除
                    name = StringUtils.replace(name, "2017 ", "");
                    name = StringUtils.replace(name, "new  ", "");
                    name = StringUtils.replace(name, "hot ", "");
                    name = StringUtils.replace(name, "sale ", "");
                    parentItem.setName(JoomTemplateUtils.getUploadName(copyCount, name));
                    parentItem.setCopyCount(0);
                    parentItem.setTags(JoomTemplateUtils.tagSortTemplate(parentItem.getTags())); // 提取十个
                    // 特性图片
                    List<String> extraImagesList = parentItem.getExtraImagesList();
                    // 特性图片去除主图
                    String uploadExtraImages = JoomTemplateUtils.getUploadExtraImages(uploadMainImage, extraImagesList);
                    List<String> splitList = CommonUtils.splitList(uploadExtraImages, "|");
                    parentItem.setExtraImages(JoomTemplateUtils.extraImageSortTemplate(splitList)); // 提取十张

                    copyTemplateUpdate.add(parentItem);
                }
            }
            else {
                error++;
            }
        }
        if (CollectionUtils.isNotEmpty(copyTemplateUpdate)) {
            batchInsert(copyTemplateUpdate);
        }
        if (CollectionUtils.isNotEmpty(updateParentTempList)) {
            batchUpdate(updateParentTempList);
        }
        return "复制成功：" + success + "条, 复制失败： " + error + "条";
    }

    @Override
    public void batchSetWeight(List<JoomTemplate> templateList) {
        for (JoomTemplate joomTemplate : templateList) {
            List<JoomSku> joomSkus = joomTemplate.getJoomSkus();
            if (CollectionUtils.isEmpty(joomSkus)) {
                continue;
            }
            String sku = joomTemplate.getSku();
            if (StringUtils.indexOf(sku, "-666") != -1) {
                sku = StringUtils.substringBefore(sku, "-666");
            }
            if (StringUtils.isEmpty(sku)) {
                continue;
            }

            List<ProductInfo> skus = ProductUtils.findProductInfos(Arrays.asList(sku));
            if (CollectionUtils.isEmpty(skus)) {
                continue;
            }
            for (ProductInfo stockKeepingUnit : skus) {
                String articleNumber = stockKeepingUnit.getSonSku();
                for (JoomSku joomSku : joomSkus) {
                    if (StringUtils.equalsIgnoreCase(joomSku.getSku(), articleNumber)) {
                        // 设置重量
                        BigDecimal shippingWeight = new BigDecimal(3);
                        if(stockKeepingUnit.getProductWeight() != null) {
                            shippingWeight = shippingWeight.add(BigDecimal.valueOf(stockKeepingUnit.getProductWeight()));
                        }
                        if(stockKeepingUnit.getPackingWeight() != null) {
                            shippingWeight = shippingWeight.add(stockKeepingUnit.getPackingWeight());
                        }
                        if(stockKeepingUnit.getMatchWeight() != null) {
                            shippingWeight = shippingWeight.add(stockKeepingUnit.getMatchWeight());
                        }
                        //重量+填充+包装+3g
                        joomSku.setShippingWeight(NumberUtils.format((shippingWeight.doubleValue())/ 1000,"0.###"));
                        break;
                    }
                }
            }
            // 重新设置
            joomTemplate.setVariations(JSONObject.toJSONString(joomSkus));

            updateByPrimaryKeySelective(joomTemplate);
        }
    }


    @Override
    public int deleteParentsBySkuStatus() {
        return joomTemplateMapper.deleteParentsBySkuStatus();
    }
}