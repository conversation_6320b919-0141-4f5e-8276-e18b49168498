package com.estone.erp.publish.joom.mapper.custom;

import com.estone.erp.publish.joom.model.JoomDataView;
import com.estone.erp.publish.joom.model.JoomDataViewExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface CustomJoomDataViewMapper {

    /*
     * 新建模板数量
     */
    Map<String, String> countTemplateAdd();

    /*
     * 模板总数
     */
    Map<String, String> countTemplateTotal();

    /*
     * 新增listing数量
     */
    Map<String, String> countListingAdd();

    /*
     * listing总数
     */
    Map<String, String> countListingTotal();
    
    /*
     * 根据销售和店铺查询listing各状态昨日新增数
     */
    Map<String, Object> countListingGroupByStateAdd(@Param("createdBy") String createdBy, @Param("itemSeller") String itemSeller);

    /*
     * 根据销售和店铺查询listing各状态总数
     */
    Map<String, Object> countListingGroupByStateTotal(@Param("createdBy") String createdBy, @Param("itemSeller") String itemSeller);
}