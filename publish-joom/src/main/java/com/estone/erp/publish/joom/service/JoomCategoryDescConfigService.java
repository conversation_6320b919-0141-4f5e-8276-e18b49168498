package com.estone.erp.publish.joom.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfig;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfigCriteria;
import com.estone.erp.publish.joom.model.JoomCategoryDescConfigExample;
import java.util.List;

/**
 * <AUTHOR> joom_category_desc_config
 * 2022-11-09 17:35:02
 */
public interface JoomCategoryDescConfigService {
    int countByExample(JoomCategoryDescConfigExample example);

    CQueryResult<JoomCategoryDescConfig> search(CQuery<JoomCategoryDescConfigCriteria> cquery);

    List<JoomCategoryDescConfig> selectByExample(JoomCategoryDescConfigExample example);

    JoomCategoryDescConfig selectByPrimaryKey(Integer id);

    JoomCategoryDescConfig selectByFullCategoryCode(String fullCategoryCode);

    int insert(JoomCategoryDescConfig record);

    int updateByPrimaryKeySelective(JoomCategoryDescConfig record);

    int updateByExampleSelective(JoomCategoryDescConfig record, JoomCategoryDescConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);
}