package com.estone.erp.publish.joom.call;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.joom.common.JoomProductCommon;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.joom.util.MapUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JoomGetItemCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private JoomItemService joomItemService;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "product";

    public JoomGetItemCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
        this.joomItemService = SpringUtils.getBean("joomItemService", JoomItemService.class);
    }

    @SuppressWarnings("unchecked")
    public List<JoomItem> getJoomItems(String productId, String parentSku) {

        httpClient = HttpClientUtils.createClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();

        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        if (StringUtils.isNotBlank(productId)) {
            standardNameValue.add(new BasicNameValuePair("id", productId));
        }

        if (StringUtils.isNotBlank(parentSku)) {
            standardNameValue.add(new BasicNameValuePair("parent_sku", parentSku));
        }

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));

            HttpGet httpGet = new HttpGet();

            try {
                httpGet.setURI(new URI(url.toString()));
            }
            catch (URISyntaxException e) {
            }

            Map<String, Object> productDetails = null;

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            // 重试三次
            while (retryTimes < 3) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpGet);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();

                    String responseJson = EntityUtils.toString(entity);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        productDetails = JSON.parseObject(responseJson, Map.class);
                    }
                    else {

                        log.error("JoomRetrieveProductCall error. Account " + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;

                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时重做  Account " + joomPmsAccount.getAccountNumber() + "Times"
                                + String.valueOf(retryTimes) + "--" + e.getMessage(), e);
                        continue;
                    }

                    log.error(e.getMessage() + " API JoomRetrieveProductCall Account "
                            + joomPmsAccount.getAccountNumber(), e);
                    return null;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            if (httpResponse == null || productDetails == null || productDetails.isEmpty()) {
                return null;
            }

            Object productListObject = productDetails.get("data");

            if (productListObject == null) {
                return null;
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            TimeZone tz = TimeZone.getTimeZone("Asia/Shanhai");
            sdf.setTimeZone(tz);

            Map<String, Object> productParamObj = (Map<String, Object>) productListObject;

            Map<String, Object> productParam = (Map<String, Object>) productParamObj.get("Product");

            if (productParam == null) {
                return null;
            }

            // 审核状态为拒绝，不保存到数据库
            /*String review_status = MapUtils.getString(productParam, "review_status");
            if ("rejected".equals(review_status)) {
                return null;
            }*/

            List<JoomItem> createProducts = JoomProductCommon.getJoomProducts(productParam, joomPmsAccount);

            joomItemService.batchInsertBySync(createProducts);

            return createProducts;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }

    }
}
