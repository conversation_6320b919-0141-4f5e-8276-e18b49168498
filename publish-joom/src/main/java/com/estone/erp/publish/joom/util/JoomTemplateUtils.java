package com.estone.erp.publish.joom.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.SkuLifeCyclePhaseCode;
import com.estone.erp.publish.common.executors.JoomExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.joom.model.*;
import com.estone.erp.publish.joom.service.JoomCategoryDescConfigService;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.joom.util.modal.JoomSku;
import com.estone.erp.publish.platform.util.TemplateTitleUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringementWordSource;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
public class JoomTemplateUtils {
    public static final String[] countryCodes = { "RU", "US", "GB", "BR", "AU", "FR", "ES", "CA", "IL", "IT", "DE",
            "CL", "SE", "BY", "NO", "NL", "UA", "CH", "MX", "PL", "HK", "TW", "JP", "IN", "ID", "MY", "SG", "KR", "TH",
            "AT", "BG", "SK", "BE", "CZ", "DK", "FI", "GR", "IE", "PT", "NZ", "TR", "BA", "EE", "LV", "LT", "LU", "PK",
            "PH", "RO", "SA", "SI", "VN", "ZA", "AR", "AE", "JO", "KW", "PE", "AL", "MD", "MC", "RS", "LI", "CO", "EC",
            "EG", "MA", "VE", "BB", "BM", "VG", "CR", "DO", "JM", "PR", "VI", "HR", "HU" };

    public static final String[] countryNames = { "俄罗斯", "美国", "英国", "巴西", "澳大利亚", "法国", "西班牙", "加拿大", "以色列", "意大利",
            "德国", "智利", "瑞典", "白俄罗斯", "挪威", "荷兰", "乌克兰", "瑞士", "墨西哥", "波兰", "中国香港", "中国台湾", "日本", "印度", "印度尼西亚", "马来西亚",
            "新加坡", "韩国", "泰国", "奥地利", "保加利亚", "斯洛伐克", "比利时", "捷克", "丹麦", "芬兰", "希腊", "爱尔兰", "葡萄牙", "新西兰", "土耳其", "波黑",
            "爱沙尼亚", "拉脱维亚", "立陶宛", "卢森堡", "巴基斯坦", "菲律宾", "罗马尼亚", "沙特阿拉伯", "斯洛文尼亚", "越南", "南非", "阿根廷", "阿联酋", "约旦",
            "科威特", "秘鲁", "阿尔巴尼亚", "摩尔多瓦", "摩纳哥", "塞尔维亚", "列支敦士登", "哥伦比亚", "厄瓜多尔", "埃及", "摩洛哥", "委内瑞拉", "巴巴多斯", "百慕大",
            "英属维尔京群岛", "哥斯达黎加", "多米尼加", "牙买加", "波多黎各", "美属维尔京群岛", "克罗地亚", "匈牙利" };

    public static final String[] color = { "WMR", "PK", "PL", "BK", "MW", "GN", "FG", "GG", "AG", "HP", "FY", "WR",
            "DR", "PR", "BG", "KH", "CP", "BL", "DB", "LB", "SB", "AB", "RB", "GY", "LG", "DG", "BZ", "BN", "CF", "GD",
            "ND", "CG", "W", "O", "Y", "R", "T", "C", "N" };

    public static final String[] size = { "2XL", "3XL", "4XL", "5XL", "6XL", "UK", "US", "AU", "EU", "XS", "XL", "S",
            "M", "L" };

    public static final String[] colorName = { "Watermelon red", "Pink", "Purple", "Black", "Ivory", "Green",
            "Fluorescent green", "Grass green", "Army green", "Hot pink", "Fluorescent yellow", "Wine red", "Dark red",
            "Peach", "Beige", "Khaki", "Champagne", "Blue", "Dark blue", "Light blue", "Sky blue", "Acid blue",
            "Royal blue", "Grey", "Light grey", "Dark grey", "Bronze", "Brown", "Coffee", "Gold", "Nude", "Camouflage",
            "White", "Orange", "Yellow", "Red", "Transparent", "Multicolor", "Natural" };

    public static final String[] sizeName = { "XXL", "XXXL", "XXXXL", "XXXXXL", "XXXXXXL", "UK Plug", "US Plug",
            "AU Plug", "EU Plug", "XS", "XL", "S", "M", "L" };
    
    public static final String[] publishProductStatus = {"New", "Waiting", "Normal", "Clearance", "Reduction"};
    
    /**
     * 模板转产品
     * 
     * @param joomTemplate 需要刊登的模版 joomAccount 刊登到的帐号
     * @return joomItem
     */
    public static JoomItem templateToItem(SaleAccountAndBusinessResponse joomPmsAccount, JoomTemplate joomTemplate, List<String> articleNumberList) throws Exception {

        // 主产品，父sku 和sku 一样
        JoomItem joomItem = new JoomItem();
        
        joomItem.setItemSeller(joomPmsAccount.getAccountNumber());

        // 存放模板id
        joomItem.setItemId(Long.valueOf(joomTemplate.getTemplateId()));
        joomItem.setItemTitle(joomTemplate.getName());
        joomItem.setSku(joomTemplate.getSku());
        //sensitiveGoods 对应 接口 DangerousKind
        joomItem.setDangerousKind(joomTemplate.getSensitiveGoods());
        
        //sku刊登 使用用户前缀
//        if (joomPmsAccount.getIsUsePrefix() && StringUtils.isNotEmpty(joomPmsAccount.getArticlenumberprefix())) {
//            joomItem.setSku(joomPmsAccount.getArticlenumberprefix() + joomTemplate.getSku());
//        }

        //描述
        String description = joomTemplate.getDescription();
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        JoomCategoryDescConfigService joomCategoryDescConfigService = SpringUtils.getBean(JoomCategoryDescConfigService.class);
        List<String> fullCodeList = singleItemEsService.getFullpathcodeListBySonSkuList(articleNumberList);

        if(CollectionUtils.isNotEmpty(fullCodeList)){
            JoomCategoryDescConfigExample configExample = new JoomCategoryDescConfigExample();
            configExample.createCriteria().andFullCategoryCodeIn(fullCodeList);
            List<JoomCategoryDescConfig> joomCategoryDescConfigs = joomCategoryDescConfigService.selectByExample(configExample);
            if(CollectionUtils.isNotEmpty(joomCategoryDescConfigs)){
                for (JoomCategoryDescConfig joomCategoryDescConfig : joomCategoryDescConfigs) {
                    if(StringUtils.isNotBlank(joomCategoryDescConfig.getAddDesc())){
                        description += "\r\n" + joomCategoryDescConfig.getAddDesc();
                        break;
                    }
                }
            }
        }else{
            throw new Exception("货号:" + StringUtils.join(articleNumberList, ",") + " es库找不到fullpathcode");
        }

        joomItem.setDescription(description);
        joomItem.setTags(joomTemplate.getTags().replaceAll("\\+", "\'"));

        joomItem.setParentSku(joomTemplate.getSku());
        joomItem.setInventory(joomTemplate.getInventory());
        joomItem.setPrice(joomTemplate.getPrice());
        joomItem.setShippingCost(joomTemplate.getShipping());
        if (joomTemplate.getShipping() !=null && joomTemplate.getShipping() >= 1d) {
            joomItem.setShippingCost(joomTemplate.getShipping());
        }
        joomItem.setMsrp(joomTemplate.getMsrp());
        joomItem.setShippingTime(joomTemplate.getShippingTime());
        joomItem.setMainImage(JoomCommonUtils.transferImageUrlForJoom(joomTemplate.getMainImage(), joomItem.getItemSeller(),joomTemplate.getSku()));
        
//        log.warn("账号：" + joomItem.getItemSeller() + " 主图地址：=======>" + joomItem.getMainImage());
        
        joomItem.setBrand(joomTemplate.getBrand());
        joomItem.setLandingPageUrl(joomTemplate.getLandingPageUrl());
        joomItem.setUpc(joomTemplate.getUpc());

        // 特效图
        joomItem.setExtraImages(JoomCommonUtils.transferImageUrlForJoom(joomTemplate.getExtraImages(),joomItem.getItemSeller(),joomTemplate.getSku()));
        
//        log.warn("账号：" + joomItem.getItemSeller() + " 特效图片地址：=======>" + joomItem.getExtraImages());

        // 多属性
        List<JoomSku> skuList = joomTemplate.getJoomSkus();
        if (CollectionUtils.isNotEmpty(skuList)) {
            List<JoomItem> variations = new ArrayList<JoomItem>();

            for (JoomSku joomSku : skuList) {
                // sku 为空
                if (StringUtils.isEmpty(joomSku.getSku()) || joomSku.getQuantity() == null) {
                    continue;
                }

                JoomItem variation = new JoomItem();
                variation.setParentSku(joomItem.getSku());
                variation.setSku(joomSku.getSku());
                
                variation.setShippingWeight(joomSku.getShippingWeight());
                
//                if (joomPmsAccount.getIsUsePrefix() && StringUtils.isNotEmpty(joomPmsAccount.getArticlenumberprefix())) {
//                    variation.setSku(joomPmsAccount.getArticlenumberprefix() + joomSku.getSku());
//                }

                // 颜色。首字母必须大写
                try {
                    String color = joomSku.getColor();
                    if (StringUtils.isNotBlank(color)) {
                        color = color.trim(); // 去前后空格
                        if (color.indexOf("&") > -1) { // 两个颜色
                            String c = color.split("&")[0].substring(0, 1).toUpperCase()
                                    + color.split("&")[0].substring(1);
                            String b = color.split("&")[1].substring(0, 1).toUpperCase()
                                    + color.split("&")[1].substring(1);
                            variation.setColor(c + "&" + b);
                        }
                        else { // 一个颜色
                            variation.setColor(color.substring(0, 1).toUpperCase() + color.substring(1));
                        }
                    }
                }
                catch (Exception e) {
                    log.warn(e.getMessage(), e);
                    variation.setColor(joomSku.getColor());
                }
                if (skuList.size() == 1 && StringUtils.isBlank(joomSku.getSize())) {
                    variation.setSize("1");
                }
                else {
                    variation.setSize(joomSku.getSize());
                }
                variation.setInventory(joomSku.getQuantity());
                variation.setPrice(joomSku.getPrice());
                variation.setShippingCost(joomSku.getShipping());
                variation.setMsrp(joomSku.getMsrp());
                variation.setShippingTime(joomItem.getShippingTime());
                variation.setIsEnabled(joomSku.getEnabled() == null ? false : joomSku.getEnabled());

                variation.setShippingLength(joomSku.getShippingLength());
                variation.setShippingWidth(joomSku.getShippingWidth());
                variation.setShippingHeight(joomSku.getShippingHeight());

                // 主图 不填子sku主图则选择父sku主图
                if (StringUtils.isNotBlank(joomSku.getMainImage())) {
                    variation.setMainImage(JoomCommonUtils.transferImageUrlForJoom(joomSku.getMainImage(), joomItem.getItemSeller(),joomTemplate.getSku()));
                }
                else {
                    variation.setMainImage(joomItem.getMainImage());
                }
                
                log.warn("账号：" + joomItem.getItemSeller() + " 多属性图片地址：=======>" + variation.getMainImage());
                
                variations.add(variation);
            }

            if (CollectionUtils.isNotEmpty(variations)) {
                joomItem.setVariations(variations);
            }
        }

        return joomItem;
    }
    

    /**
     * 产品转模板
     * 
     * @param joomItem 需要转换的产品
     * @return JoomTemplate 转换后的模版
     */
    public static JoomTemplate itemToTemplate(JoomItem joomItem) {
        JoomTemplate joomTemplate = new JoomTemplate();

        joomTemplate.setIsLock(true);

        joomTemplate.setName(joomItem.getItemTitle());

        joomTemplate.setDescription(joomItem.getDescription());

        joomTemplate.setTags(joomItem.getTags());
        joomTemplate.setSku(joomItem.getParentSku());
        joomTemplate.setInventory(joomItem.getInventory());
        joomTemplate.setPrice(joomItem.getPrice());

        joomTemplate.setMsrp(joomItem.getMsrp());
        joomTemplate.setShippingTime(joomItem.getShippingTime());
        joomTemplate.setShipping(joomItem.getShippingCost());

        joomTemplate.setMainImage(joomItem.getMainImage());
        joomTemplate.setExtraImages(joomItem.getExtraImages());

        // 多属性
        List<JoomItem> skuList = joomItem.getVariations();
        if (CollectionUtils.isNotEmpty(skuList)) {
            List<JoomSku> variations = new ArrayList<JoomSku>();

            for (JoomItem sku : skuList) {
                JoomSku joomSku = new JoomSku();
                joomSku.setSku(sku.getSku());
                String multiAttr = sku.getMultiAttr();
                if (StringUtils.isNotBlank(multiAttr)) {
                    String color = multiAttr.split(",")[0];
                    String size = multiAttr.split(",")[1];
                    // 详情原理看ApiJoomProducts
                    if (color.split(":").length > 1) {
                        joomSku.setColor(color.split(":")[1]);
                    }

                    if (size.split(":").length > 1) {
                        joomSku.setSize(size.split(":")[1]);
                    }
                }
                joomSku.setQuantity(sku.getInventory());
                joomSku.setPrice(sku.getPrice());
                joomSku.setShipping(sku.getShippingCost());
                joomSku.setMsrp(sku.getMsrp());
                joomSku.setShippingTime(sku.getShippingTime());
                joomSku.setEnabled(sku.getIsEnabled() == null ? false : sku.getIsEnabled());
                joomSku.setMainImage(sku.getMainImage());

                List<ProductInfo> list = ProductUtils.findProductInfos(Arrays.asList(sku.getSku()));
                ProductInfo pmsSku = null;
                if(CollectionUtils.isNotEmpty(list)) {
                    pmsSku = list.stream().filter(item-> item.getSonSku().equals(sku.getSku())).findAny().orElse(null);
                }
                if(pmsSku != null) {
                    String attribute = "状态：" + SkuLifeCyclePhaseCode.build(pmsSku.getItemStatus()) + "<br/> 标签："
                            + (StringUtils.isNotBlank(pmsSku.getTag()) ? pmsSku.getTag() : "无");
                    
                    joomSku.setAttribute(attribute); // 仅查看属性
                }
                variations.add(joomSku);
            }

            joomTemplate.setJoomSkus(variations);

        }else{
            joomTemplate.getJoomSkus().add(new JoomSku());
        }

        return joomTemplate;
    }

    /**
     * 乱序提取十张特效图
     * 
     * @param extraImagesList 母板特效图集合
     * @return 十张特效图 |拼接
     */
    public static String extraImageSortTemplate(List<String> extraImagesList) {

        if (CollectionUtils.isEmpty(extraImagesList)) {
            return null;
        }

        if (extraImagesList.size() > 1) {
            // 重新随机排序
            Collections.sort(extraImagesList, new Comparator<String>() {
                public int compare(String x, String y) {
                    return Math.random() > 0.5 ? -1 : 1;
                }
            });
        }

        int size = 10;
        if (extraImagesList.size() < 10) {
            size = extraImagesList.size();
        }
        // 拼接十张图片
        StringBuffer sb = new StringBuffer(extraImagesList.get(0));
        for (int i = 1; i < size; i++) {
            sb.append("|" + extraImagesList.get(i));
        }

        return sb.toString();
    }

    /**
     * 乱序提取一个主图
     * 
     * @param mainImageList 母板主图集合
     * @return 一个主图
     */
    public static String mainImageSortTemplate(List<String> mainImageList) {

        if (mainImageList.size() > 1) {
            // 重新随机排序
            Collections.sort(mainImageList, new Comparator<String>() {
                public int compare(String x, String y) {
                    return Math.random() > 0.5 ? -1 : 1;
                }
            });
        }

        return mainImageList.get(0);
    }

    /**
     * 乱序提取十个tag
     * 
     * @param tag 母板tag集合
     * @return 十个tag集合
     */
    public static String tagSortTemplate(String tag) {
        String[] tags = tag.split(",");

        if (tags.length < 2) {
            return tag;
        }

        // 重新随机排序
        Arrays.sort(tags, new Comparator<String>() {
            public int compare(String x, String y) {
                return Math.random() > 0.5 ? -1 : 1;
            }
        });

        StringBuffer sb = new StringBuffer();
        sb.append(tags[0]);
        // 组合 合并10个
        for (int i = 1; i < (tags.length > 10 ? 10 : tags.length); i++) {
            sb.append("," + tags[i]);
        }

        return sb.toString();
    }

    /**
     * 特性标签 排序 并转化成小写
     * 
     * @param tags
     * @return
     */
    public static String sortTransition(String tags) {

        if (StringUtils.isEmpty(tags)) {
            return tags;
        }

        String[] tagsSplit = StringUtils.split(tags, ",");
        String[] tagsNewSplit = new String[tagsSplit.length];
        for (int i = 0; i < tagsSplit.length; i++) {
            String tag = tagsSplit[i];

            if (StringUtils.isNotBlank(tag)) {
                tagsNewSplit[i] = tag.trim().toLowerCase();
            }
        }

        if (tagsNewSplit != null && tagsNewSplit.length > 0) {
            Arrays.sort(tagsNewSplit);

            String sortTags = StringUtils.join(tagsNewSplit, ",");

            return sortTags;
        }
        else {
            return tags;
        }
    }
    
    
    /**
     * 检测模板  
     * 提取出 模板内的所有sku 查询在线列表对应的账号是否存在
     * 
     * @param joomPmsAccount
     * @param joomTemplate
     * @return
     */
    public static JoomTemplate ckeckTemplate(SaleAccountAndBusinessResponse joomPmsAccount, JoomTemplate joomTemplate) {
        
        //卖家账号
        String sellerId = joomPmsAccount.getAccountNumber();
        
        //一个产品上传不同店铺时产品变主图，主图不设置为随机，按照复制的顺序挑选主图，标题100个字符 且调换前三个英文单词顺序，sku变码
//        Integer uploadCount = joomTemplate.getUploadCount();
//        if(uploadCount == null){
//            uploadCount = 0;
//        }
        
        
//        //UploadCount 先 +1保存，因为是ajax 调用  如果失败，需要 -1  换到外面加锁
//        JoomTemplateService joomTemplateService = SpringUtils.getBean("joomTemplateService", JoomTemplateService.class);
//        JoomTemplate updateJoomTemplate = new JoomTemplate();
//        updateJoomTemplate.setTemplateId(joomTemplate.getTemplateId());
//        updateJoomTemplate.setIsLock(false);
//        updateJoomTemplate.setUploadCount(uploadCount + 1);
//        joomTemplateService.updateJoomTemplate(updateJoomTemplate);
        
        //主图 "|"拼接  (默认模板有3张主图)
//        List<String> mainImageList = joomTemplate.getMainImageList();
//        int index = uploadCount % mainImageList.size();
//        
//        //获取主图
//        String uploadMainImage = mainImageList.get(index);
//        joomTemplate.setMainImage(uploadMainImage);
//        
//        List<String> extraImagesList = joomTemplate.getExtraImagesList();
//        
//        //获取扩展图片
//        String uploadExtraImages = getUploadExtraImages(uploadMainImage, extraImagesList);
//        joomTemplate.setExtraImages(uploadExtraImages);
//        
//        String name = joomTemplate.getName();
//        
//        //获取标题
//        String uploadName = getUploadName(uploadCount, name);
//        joomTemplate.setName(uploadName);
        
        List<EsSkuBind> createSkuBinds = new ArrayList<>();
        
        //获取随机sku
        String sku = joomTemplate.getSku();
        EsSkuBind skuBind = JoomCommonUtils.getRandomSku(sellerId, sku, SaleChannel.CHANNEL_JOOM);
        
        //换成随机sku
        joomTemplate.setSku(skuBind.getBindSku());
        
        createSkuBinds.add(skuBind);
        List<JoomSku> joomSkus = joomTemplate.getJoomSkus();
        for (JoomSku joomSku : joomSkus) {
            String sunSku = joomSku.getSku();

            EsSkuBind sunSkuBind = JoomCommonUtils.getRandomSku(sellerId, sunSku, SaleChannel.CHANNEL_JOOM);
            
            //换成随机sku
            joomSku.setSku(sunSkuBind.getBindSku());
            createSkuBinds.add(sunSkuBind);
        }
        
        //重新设置多属性
        joomTemplate.setVariations(JSONObject.toJSONString(joomSkus));
        
        if(CollectionUtils.isNotEmpty(createSkuBinds)){
            
            try{
                EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);
                esSkuBindService.saveAll(createSkuBinds, SaleChannel.CHANNEL_JOOM);
            }catch(Exception e){
                //绑定sku bind_sku 是唯一索引，报错 直接返回null
                log.error(e.getMessage());
                return null;
            }
        }
        
        return joomTemplate;
    }
    
    /**
     * 检测模板  
     * 1-停产，存档、废弃 状态的产品不允许刊登
     * 2-当前平台禁售，存在侵权词（标题），不允许刊登
     * 3-其他侵权平台，提示，允许刊登。
     * @param joomTemplate
     * @return
     */
    public static String ckeckTemplateSku(JoomTemplate joomTemplate) {
        List<JoomSku> joomSkus = joomTemplate.getJoomSkus();
        String message = null;
        for (JoomSku joomSku : joomSkus) {
            String articlenumber = joomSku.getSku();
            Double shippingLength = joomSku.getShippingLength();
            Double shippingWidth = joomSku.getShippingWidth();
            Double shippingHeight = joomSku.getShippingHeight();
            if(shippingLength == null || shippingWidth == null || shippingHeight == null){
                message = "子属性货号["+articlenumber+"]，长宽高必填！";
                break;
            }
            List<ProductInfo> list = ProductUtils.findProductInfos(Arrays.asList(articlenumber));
            ProductInfo skuObject = null;
            if(CollectionUtils.isNotEmpty(list)) {
                skuObject = list.stream().filter(item-> item.getSonSku().equals(articlenumber)).findAny().orElse(null);
            }
            if(skuObject !=null){
                //1-停产，存档，样品状态的产品不允许刊登
                if(PublishCommonConstant.INTERCEPT_EN_STATE_LIST.contains(skuObject.getItemStatus())){
                    message = "子属性货号["+articlenumber+"]单品最新状态为["+SkuLifeCyclePhaseCode.build(skuObject.getItemStatus())+"]，废弃、停产、存档，不能刊登！请删除该子属性";
                    break;
                }
            }
        }
        //2-当前平台禁售，存在侵权词（标题），不允许刊登
        if(StringUtils.isBlank(message)) {
            //禁售平台
            try {
                Map<String, List<SalesProhibitionsVo>> forbiddenSalesSiteBySku = ProductInfringementForbiddenSaleUtils
                        .getForbiddenSalesSiteBySku(Arrays.asList(joomTemplate.getSku()));
                if(MapUtils.isNotEmpty(forbiddenSalesSiteBySku)){
                    List<SalesProhibitionsVo> salesProhibitionsVos = forbiddenSalesSiteBySku.get(joomTemplate.getSku());
                    if(CollectionUtils.isNotEmpty(salesProhibitionsVos)){
                        List<String> collect = salesProhibitionsVos.stream().map(t -> t.getPlat())
                                .collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect) && collect.contains(SaleChannelEnum.JOOM.getChannelName())){
                            message = "产品在Joom平台被禁售！";
                        }
                    }
                }
            }
            catch (Exception e) {
                log.error("获取产品禁售平台异常:" + e.getMessage(), e);
                return "获取产品禁售平台异常:" + e.getMessage();
            }
        }
        if(StringUtils.isBlank(message)) {
            try {
                Map<String, Boolean> checkInfringementSkuMap = ProductInfringementForbiddenSaleUtils
                        .checkSkuInfringementBySaleChannel(SaleChannelEnum.JOOM.getChannelName(), Arrays.asList(joomTemplate.getSku()));

                Boolean infringementFlag1 = checkInfringementSkuMap.get(joomTemplate.getSku());
                if (infringementFlag1 != null && infringementFlag1) {
                    message = "产品在Joom平台侵权！";
                }
            }
            catch (Exception e) {
                log.error("查询产品侵权异常" + e.getMessage());
                return "查询产品侵权异常" + e.getMessage();
            }
        }
        if(StringUtils.isBlank(message)) {
            // 校验侵权词
            try {
                Map<String, String> templateInfringementWords = JoomTemplateUtils.getTemplateInfringementWords(joomTemplate.getName(), joomTemplate.getDescription());
                message = templateInfringementWords.get("error");
            } catch (Exception e) {
                return "校验侵权词异常：" + e.getMessage();
            }
        }
        return message;
    }
    
    
    public static boolean checkSkuRepeat(String sellerId, JoomTemplate joomTemplate){
        //查询在线列表  该账号是否存在sku
        JoomItemService joomItemService = SpringUtils.getBean("joomItemService", JoomItemService.class);
        
        //模板sku 集合
        List<String> skuList = new ArrayList<String>();
        
        String sku = joomTemplate.getSku();
        skuList.add(sku);
        
        List<JoomSku> joomSkus = joomTemplate.getJoomSkus();
        
        if(CollectionUtils.isNotEmpty(joomSkus)){
            for (JoomSku joomSku : joomSkus) {
                String multiSku = joomSku.getSku();
                skuList.add(multiSku);
            }
        }
        return joomItemService.isExistJoomItem(sellerId, skuList);
    }
    
    //获取扩展图片，不能包含主图
    public static String getUploadExtraImages(String uploadMainImage, List<String> extraImagesList){
        
        StringBuffer sb = new StringBuffer();
        //这里面不能包含主图
        for (String extraImage : extraImagesList) {
            if(!StringUtils.equalsIgnoreCase(uploadMainImage, extraImage)){
                sb.append(extraImage + "|"); 
            }
        }
        
        //上传的扩展图片
        String uploadExtraImages = "";
        
        if(sb.length() > 0){
            uploadExtraImages = sb.substring(0, sb.length() - 1);
        }
        
        return uploadExtraImages;
    }
    
    
    //获取标题
    public static String getUploadName(Integer count, String name){
        String[] split = StringUtils.split(name, " ");
        
        String oneName = "";
        
        String twoName = "";
        
        String threeName = "";
        
        String uploadName = "";
        
        for (int i = 0; i < split.length; i++) {
            if(i == 0){
                oneName = split[i];
            }else if(i == 1){
                twoName = split[i];
            }else if(i == 2){
                threeName = split[i];
            }else{
                uploadName += " " + split[i];
            }
        }
        
        //uploadName 前面 带有空格 //标题 前面3个变动  6种情况 123,132,213,231,312,321
        if((count % 6) == 0){
            uploadName = oneName + " " + twoName + " " + threeName + uploadName;
        }else if((count % 6) == 1){
            uploadName = twoName + " " + oneName + " " + threeName + uploadName;
        }else if((count % 6) == 2){
            uploadName = threeName + " " + twoName + " " + oneName + uploadName;
        }else if((count % 6) == 3){
            uploadName = twoName + " " + threeName + " " + oneName + uploadName;
        }else if((count % 6) == 4){
            uploadName = threeName + " " + oneName + " " + twoName + uploadName;
        }else if((count % 6) == 5){
            uploadName = oneName + " " + threeName + " " + twoName + uploadName;
        }
        return uploadName;
    }

    /**
     * 通过SPU设置标题描述
     * @param joomTemplate
     * @param spu
     */
    public static void setTitleWithDescriptionBySpu(JoomCreateTemplateJson joomTemplate, String spu) {
        if(StringUtils.isBlank(spu) || null == joomTemplate) {
            return;
        }

        SpuOfficial spuOfficial = null;
        String title = null;

        ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(Arrays.asList(spu));
        if(spuTitlesRsp.isSuccess()){
            List<SpuOfficial> spuOfficials = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
            if(CollectionUtils.isNotEmpty(spuOfficials)) {
                spuOfficial = spuOfficials.get(0);
            }
        }else{
            throw new BusinessException("获取SPU标题失败：" + spuTitlesRsp.getMessage());
        }

        if(null == spuOfficial) {
            throw new BusinessException("获取SPU标题为空!");
        }

        title = getJoomTemplateTitleBySpu(spuOfficial);
        // 描述 先取新描述 没有则用旧的
        String description = null;
        if(null != spuOfficial) {
            if(StringUtils.isNotBlank(spuOfficial.getNewDescription())) {
                description = spuOfficial.getNewDescription();
            } else {
                String descEn = spuOfficial.getDescription();
                if(StringUtils.isNotBlank(descEn) && descEn.startsWith("[")){
                    List<String> strings = JSON.parseObject(descEn, new TypeReference<List<String>>() {
                    });
                    description = strings.get(0);
                }else{
                    description = descEn;
                }
            }
        }

        // 设置标题描述 在描述最前面插入标题
        joomTemplate.setName(title);
        joomTemplate.setDescription(title + "\n\n" + (StringUtils.isNotBlank(description) ? description : ""));

        // 校验侵权词
        Map<String, String> infringementWordMap = checkInfringementWords(joomTemplate.getName(), joomTemplate.getDescription());
        joomTemplate.setInfringingWords(infringementWordMap);
    }

    /**
     * 长标题5->短标题10->标题3，
     * @param spuOfficial
     * @return
     */
    public static String getJoomTemplateTitleBySpu(SpuOfficial spuOfficial) {
        if(null == spuOfficial) {
            return null;
        }

        String title = null;

        //长标题
        List<String>  longTitleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<List<String>>() {
        });
        title = TemplateTitleUtils.getRandomStr(longTitleList);
        if(StringUtils.isNotBlank(title)) {
            return title;
        }

        //短标题
        List<String> shortTitleList = JSON.parseObject(spuOfficial.getShortTitleJson(), new TypeReference<List<String>>() {
        });
        title = TemplateTitleUtils.getRandomStr(shortTitleList);
        if(StringUtils.isNotBlank(title)) {
            return title;
        }

        //sku标题
        List<String> titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
        });
        title = TemplateTitleUtils.getRandomStr(titleList);

        return title;
    }

    /**
     * 返回 包含侵权词 ，如果是提示就不要调用这个方法
     * @param title
     * @param description
     * @return
     */
    public static Map<String, String> getTemplateInfringementWords(String title, String description) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, String> infringementWordMap = checkInfringementWords(title, description);
        if (MapUtils.isEmpty(infringementWordMap)) {
            return resultMap;
        }
        StringBuilder errorBuilder = new StringBuilder();
        StringBuilder tipBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : infringementWordMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if(StringUtils.contains(key, "tip")){
                tipBuilder.append(key);
                tipBuilder.append("包含律所代理商标，律所代理商标（A级），律所代理商标（A变体）：");
                tipBuilder.append(value);
                tipBuilder.append(" ");
            }else{
                errorBuilder.append(key);
                errorBuilder.append("包含侵权词：");
                errorBuilder.append(value);
                errorBuilder.append(" ");
            }
        }
        if(errorBuilder.length() > 0){
            resultMap.put("error", errorBuilder.toString());
        }
        if(tipBuilder.length() > 0){
            resultMap.put("tip", tipBuilder.toString());
        }
        return resultMap;
    }

    /**
     * 校验标题描述侵权词
     *
     * 若词汇标识为：律所代理商标，律所代理商标（A级），律所代理商标（A变体），则不进行删除，只提示
     * @param title
     * @param description
     * @return
     */
    public static Map<String, String> checkInfringementWords(String title, String description) {

        List<String> excludeType = Arrays.asList("律所代理商标(A级)", "律所代理商标", "律所代理商标(A变体)");

        Map<String, String> resultMap = new ConcurrentHashMap<>();
        CompletableFuture<Void> titleFuture = JoomExecutors.submitAsyncTortTask(() -> {
            SearchVo searchVo = new SearchVo();
            searchVo.setPlatform(SaleChannelEnum.JOOM.getChannelName());
            searchVo.setText(title);
            Map<String, InfringementWordSource> infringementWordSourceMap = checkInfringementWords(searchVo);

            if(MapUtils.isNotEmpty(infringementWordSourceMap)){
                Set<String> titleInfringementSet = new HashSet<>();
                Set<String> excludeTitleInfringementSet = new HashSet<>();
                for (Map.Entry<String, InfringementWordSource> stringInfringementWordSourceEntry : infringementWordSourceMap.entrySet()) {
                    String key = stringInfringementWordSourceEntry.getKey();
                    InfringementWordSource infringementWordSource = stringInfringementWordSourceEntry.getValue();
                    Set<String> trademarkIdentification = infringementWordSource.getTrademarkIdentification();
                    if(trademarkIdentification == null || trademarkIdentification.isEmpty()){
                        trademarkIdentification = new HashSet<>();
                    }

                    List<String> trademarkList = new ArrayList<>(trademarkIdentification);
                    trademarkList.retainAll(excludeType);

                    if(CollectionUtils.isNotEmpty(trademarkList)){
                        excludeTitleInfringementSet.add(key);
                    }else{
                        titleInfringementSet.add(key);
                    }
                }

                if (CollectionUtils.isNotEmpty(titleInfringementSet)) {
                    resultMap.put("标题", StringUtils.join(titleInfringementSet, ","));
                }
                if(CollectionUtils.isNotEmpty(excludeTitleInfringementSet)){
                    resultMap.put("tip-title", StringUtils.join(excludeTitleInfringementSet, ","));
                }
            }

        });
        CompletableFuture<Void> descriptionFuture = JoomExecutors.submitAsyncTortTask(() -> {
            SearchVo searchVo = new SearchVo();
            searchVo.setPlatform(SaleChannelEnum.JOOM.getChannelName());
            searchVo.setText(description);
            Map<String, InfringementWordSource> infringementWordSourceMap = checkInfringementWords(searchVo);

            if(MapUtils.isNotEmpty(infringementWordSourceMap)){
                Set<String> descInfringementSet = new HashSet<>();
                Set<String> excludeDescInfringementSet = new HashSet<>();
                for (Map.Entry<String, InfringementWordSource> stringInfringementWordSourceEntry : infringementWordSourceMap.entrySet()) {
                    String key = stringInfringementWordSourceEntry.getKey();
                    InfringementWordSource infringementWordSource = stringInfringementWordSourceEntry.getValue();


                    Set<String> trademarkIdentification = infringementWordSource.getTrademarkIdentification();
                    if(trademarkIdentification == null || trademarkIdentification.isEmpty()){
                        trademarkIdentification = new HashSet<>();
                    }

                    List<String> trademarkList = new ArrayList<>(trademarkIdentification);
                    trademarkList.retainAll(excludeType);

                    if(CollectionUtils.isNotEmpty(trademarkList)){
                        excludeDescInfringementSet.add(key);
                    }else{
                        descInfringementSet.add(key);
                    }
                }

                if (CollectionUtils.isNotEmpty(descInfringementSet)) {
                    resultMap.put("描述", StringUtils.join(descInfringementSet, ","));
                }
                if(CollectionUtils.isNotEmpty(excludeDescInfringementSet)){
                    resultMap.put("tip-desc", StringUtils.join(excludeDescInfringementSet, ","));
                }
            }
        });

        // 等待任务全部执行完毕
        CompletableFuture.allOf(titleFuture, descriptionFuture).join();
        if (MapUtils.isNotEmpty(resultMap)) {
            return resultMap;
        }

        return Collections.emptyMap();
    }

    private static Map<String, InfringementWordSource> checkInfringementWords(SearchVo searchVo) {
        ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringmentWordNew(searchVo);
        if (!checkResult.isSuccess()) {
            throw new RuntimeException(checkResult.getErrorMsg());
        }
        InfringmentResponse infringmentResponse = checkResult.getResult();
        Map<String, InfringementWordSource> infringementWordSourceMap = infringmentResponse.getInfringementWordSourceMap();
        return infringementWordSourceMap;
    }
}