package com.estone.erp.publish.joom.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.amqp.rabbit.listener.DirectMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ProductSystemMqConfig {

    private int productSaleSkuMqConsumers;
    private int productSaleSkuMqPrefetchCount;
    private boolean productSaleSkuMqSwitchListener;

    /************ PublishQueues.PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE *******************/
    @Bean
    public Binding productDataChangeToPublishNormalBinding() {
        return new Binding(PublishQueues.PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE, DestinationType.QUEUE, PublishRabbitMqExchange.PRODUCT_DIRECT_EXCHANGE,
                PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_KEY, null);
    }
    @Bean
    public ProductDataChangeToPublishNormalMqListener productDataChangeToPublishNormalMqListener() {
        return new ProductDataChangeToPublishNormalMqListener();
    }
    @Bean
    public DirectMessageListenerContainer productDataChangeToPublishNormalMqListenerContainer(
            ProductDataChangeToPublishNormalMqListener productDataChangeToPublishNormalMqListener, ConnectionFactory connectionFactory) {
        DirectMessageListenerContainer container = new DirectMessageListenerContainer(connectionFactory);
        DirectMessageListenerContainer(container, PublishQueues.PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE, productDataChangeToPublishNormalMqListener);
        return container;
    }
    /*****************************************************************/


    private void DirectMessageListenerContainer(DirectMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (productSaleSkuMqSwitchListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(productSaleSkuMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConsumersPerQueue(productSaleSkuMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

}
