package com.estone.erp.publish.amazon.mq;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.excel.component.AbstractExcelDownloadTemplate;
import com.estone.erp.publish.system.excel.service.DownloadService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

/**
 * Amazon 导出日志队列
 *
 * <AUTHOR>
 * @date 2024-07-25 下午6:10
 */
@Slf4j
@Component
public class AmazonDownloadMqListener extends AbstractExcelDownloadTemplate implements ChannelAwareMessageListener {

    @Override
    public String getPlatform() {
        return SaleChannel.CHANNEL_AMAZON;
    }

    /**
     * 下载导出队列消息处理
     * <p>
     * 具体的实现请查看{@link DownloadService}的具体实现类
     */
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        super.doService(message, channel);
    }
}
