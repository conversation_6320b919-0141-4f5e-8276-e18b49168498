package com.estone.erp.publish.amazon.mq.templatestatus;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Amazon发布状态检查器
 * 负责检查模版发布状态、超时判定、Listing信息同步等功能
 */
@Slf4j
@Component
public class AmazonPublishStatusChecker {

    @Autowired
    private IAmazonListingInfoService amazonListingInfoService;

    /**
     * 检查模版是否超时
     * 
     * @param template 模版信息
     * @param timeoutHours 超时小时数
     * @return true-超时，false-未超时
     */
    public boolean isTemplateTimeout(AmazonTemplateBO template, int timeoutHours) {
        LocalDateTime lastUpdateTime = LocalDateTime.ofInstant(template.getLastUpdateDate().toInstant(), 
                                                               java.time.ZoneId.systemDefault());
        LocalDateTime now = LocalDateTime.now();
        long hours = ChronoUnit.HOURS.between(lastUpdateTime, now);
        
        boolean timeout = hours >= timeoutHours;
        if (timeout) {
            log.info("模版刊登超时（超过{}小时），模版ID：{}，更新时间：{}", 
                    timeoutHours, template.getId(), lastUpdateTime);
        }
        return timeout;
    }

    /**
     * 同步并获取Listing信息
     *
     * @param templates 模版信息
     * @return Listing信息列表，失败时返回null
     */
    public List<AmazonListingInfo> syncListingInfos(List<AmazonTemplateBO> templates) {
        List<String> allSellerSkus = templates.stream()
                .map(AmazonTemplateUtils::getAllSellerSkuSku)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        AmazonTemplateBO amazonTemplateBO = templates.get(0);
        String accountNumber = amazonTemplateBO.getSellerId();

        List<AmazonListingInfo> listingInfoList = new ArrayList<>();

        Lists.partition(allSellerSkus, 10).forEach(sellerSkus -> {
            ApiResult<List<AmazonListingInfo>> listApiResult = amazonListingInfoService.searchListingInfoBySellerSku(accountNumber, sellerSkus);
            if (listApiResult.isSuccess()) {
                listingInfoList.addAll(listApiResult.getResult());
            }
        });
        return listingInfoList;
    }


}