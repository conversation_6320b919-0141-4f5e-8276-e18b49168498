package com.estone.erp.publish.amazon.call.util;

import com.aliyun.oss.OSS;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.*;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.SkuThemeUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.oss.AliOss;
import com.estone.erp.publish.common.oss.OssUtils;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.ImageUtils;
import com.estone.erp.publish.common.util.RetryUtil;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class AmazonUtils {

    private static String FILL_SYSTEM_URl ="10.100.1.200:8888";

    /*
     * 通用日期模板
     * */
    public final static String NORMAL_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static String FILL_TEST_SYSTEM_URl = "172.16.10.51:8888";

    public static final Map<String, Lock> RUNNING_TASK_MAP = new ConcurrentHashMap<>();

    public static final Pattern FILE_PATTERN = Pattern.compile("[\\\\/:*?\"<>|]");

    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        }
        catch (InterruptedException e) {
            log.error("Amazon Thread sleep() occured error.");
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 解析日期字符串为Date对象，支持多种国家和地区的日期格式
     * 支持的国家和地区包括：Singapore、Australia、Japan、Ireland、Spain、United Kingdom、
     * France、Belgium、Netherlands、Germany、Italy、Sweden、South Africa、Poland、Egypt、
     * Turkey、Saudi Arabia、United Arab Emirates、India、Canada、United States of America、
     * Mexico、Brazil等
     *
     * @param date 日期字符串
     * @return 解析后的Date对象，解析失败返回null
     */
    public static Date getDate(String date,String accountNumber) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        // 移除日期字符串中的多余空格
        date = date.trim();

        // 默认日期格式
        String parsePattern = NORMAL_DATE_PATTERN; // "yyyy-MM-dd HH:mm:ss"
        String originalDate = date;

        // 欧洲国家 (UK, Ireland, France, Belgium, Netherlands, Germany, Spain, Italy, Sweden, Poland)
        // 使用 dd/MM/yyyy 或 dd-MM-yyyy 格式
        if (StringUtils.contains(date, "BST") || StringUtils.contains(date, "GMT")
                || StringUtils.contains(date, "WET") || StringUtils.contains(date, "CET")
                || StringUtils.contains(date, "CEST") || StringUtils.contains(date, "EET")
                || StringUtils.contains(date, "EEST") || StringUtils.contains(date, "WEST")
                || StringUtils.contains(date, "MEST") || StringUtils.contains(date, "MET")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }
        // 澳大利亚 (Australia)
        else if (StringUtils.contains(date, "AEDT") || StringUtils.contains(date, "AEST")
                || StringUtils.contains(date, "ACDT") || StringUtils.contains(date, "ACST")
                || StringUtils.contains(date, "AWDT") || StringUtils.contains(date, "AWST")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }
        // 日本 (Japan)
        else if (StringUtils.contains(date, "JST")) {
            parsePattern = "yyyy/MM/dd HH:mm:ss";
            // 移除JST标记以便解析
            date = date.replace(" JST", "");
        }
        // 印度 (India)
        else if (StringUtils.contains(date, "IST")) {
            parsePattern = "dd-MM-yyyy HH:mm:ss";
            // 移除IST标记以便解析
            date = date.replace(" IST", "");
        }
        // 新加坡 (Singapore)
        else if (StringUtils.contains(date, "SGT")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除SGT标记以便解析
            date = date.replace(" SGT", "");
        }
        // 南非 (South Africa)
        else if (StringUtils.contains(date, "SAST")) {
            parsePattern = "yyyy-MM-dd HH:mm:ss";
            // 移除SAST标记以便解析
            date = date.replace(" SAST", "");
        }
        // 埃及 (Egypt)、土耳其 (Turkey)、沙特阿拉伯 (Saudi Arabia)、阿联酋 (UAE)
        else if (StringUtils.contains(date, "EET") || StringUtils.contains(date, "TRT")
                || StringUtils.contains(date, "AST") || StringUtils.contains(date, "GST")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }
        // 北美 (USA, Canada)
        else if (StringUtils.contains(date, "EST") || StringUtils.contains(date, "EDT")
                || StringUtils.contains(date, "CST") || StringUtils.contains(date, "CDT")
                || StringUtils.contains(date, "MST") || StringUtils.contains(date, "MDT")
                || StringUtils.contains(date, "PST") || StringUtils.contains(date, "PDT")) {
            parsePattern = "MM/dd/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }
        // 墨西哥 (Mexico)
        else if (StringUtils.contains(date, "CDT") || StringUtils.contains(date, "CST")
                || StringUtils.contains(date, "MDT") || StringUtils.contains(date, "MST")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }
        // 巴西 (Brazil)
        else if (StringUtils.contains(date, "BRT") || StringUtils.contains(date, "BRST")) {
            parsePattern = "dd/MM/yyyy HH:mm:ss";
            // 移除时区标记以便解析
            date = date.replaceAll("\\s+[A-Z]{3,4}$", "");
        }

        // 尝试解析日期
        try {
            return new SimpleDateFormat(parsePattern).parse(date);
        } catch (ParseException e) {
            // 第一次解析失败，尝试其他常见格式
            String[] commonPatterns = {
                "yyyy-MM-dd HH:mm:ss",  // ISO标准格式
                "dd/MM/yyyy HH:mm:ss",  // 欧洲、澳大利亚等
                "MM/dd/yyyy HH:mm:ss",  // 美国、加拿大
                "yyyy/MM/dd HH:mm:ss",  // 日本等
                "dd-MM-yyyy HH:mm:ss",  // 印度等
                "yyyy-MM-dd'T'HH:mm:ss", // ISO 8601
                "yyyy-MM-dd'T'HH:mm:ssXXX", // ISO 8601 with timezone
                "EEE MMM dd HH:mm:ss zzz yyyy" // 完整日期格式
            };

            for (String pattern : commonPatterns) {
                if (pattern.equals(parsePattern)) {
                    continue; // 跳过已尝试的格式
                }
                try {
                    return new SimpleDateFormat(pattern, Locale.ENGLISH).parse(date);
                } catch (ParseException ex) {
                    // 继续尝试下一个格式
                }
            }

            // 所有格式都解析失败，记录错误
            log.error("Failed to parse date: " + date + "  店铺数据：" + accountNumber+ " , error: " + e.getMessage());
        }

        return null;
    }

    /**
     * 功能描述: 太平洋时间转换成本地时间
     *
     * @param pstDateStr 太平洋时间，yyyy-MM-dd HH:mm:ss
     * @return: java.util.Date 本地时间
     * @Author: Frank
     * @Date: 2018/11/21
     * @Version: 0.0.1
     */
    private Date fromPstToLocal(String pstDateStr) throws Exception {
        SimpleDateFormat sdfUS = new SimpleDateFormat(NORMAL_DATE_PATTERN);
        sdfUS.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        //日本时区
        TimeZone zoneJapan = TimeZone.getTimeZone("JST");
        //中国时区
        TimeZone zoneChina = TimeZone.getTimeZone("Asia/Shanghai");
        return sdfUS.parse(pstDateStr);
    }

    public static <E> void batchResolve(List<E> list, int batchSize, Consumer<List<E>> consumer) {
        if (CollectionUtils.isEmpty(list) || batchSize <= 0 || consumer == null) {
            return;
        }

        int size = list.size();
        if (size <= batchSize) {
            consumer.accept(list);
            return;
        }

        int page = (size - 1) / batchSize + 1;
        int start = 0;
        int end = 0;
        for (int i = 1; i <= page; i++) {
            end = start + (i == page ? (size - batchSize * (i - 1)) : batchSize);
            consumer.accept(list.subList(start, end));
            start = end;
        }
    }

    public static Date getUTCTime() {
        final Calendar cal = Calendar.getInstance();
        final int zoneOffset = cal.get(Calendar.ZONE_OFFSET);
        final int dstOffset = cal.get(Calendar.DST_OFFSET);
        cal.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));

        return cal.getTime();
    }

    public static Date getUTCTime(Timestamp timestamp) {
        final Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestamp.getTime());
        final int zoneOffset = cal.get(Calendar.ZONE_OFFSET);
        final int dstOffset = cal.get(Calendar.DST_OFFSET);
        cal.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));

        return cal.getTime();
    }

    public static Date getUTCTime(Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        final int zoneOffset = cal.get(Calendar.ZONE_OFFSET);
        final int dstOffset = cal.get(Calendar.DST_OFFSET);
        cal.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));

        return cal.getTime();
    }



    public static void deleteFile(File file) {
        try {
            if (file == null || !file.exists()) {
                return;
            }

            FileUtils.forceDelete(file);
        }
        catch (Exception e) {
        }
    }


    public static String fetchHttpUrl(String url) {
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try {
            try {
                response = client.execute(new HttpGet(url));
                if (response.getStatusLine().getStatusCode() == 200) {
                    return IOUtils.toString(response.getEntity().getContent());
                }
            }
            catch (IOException e) {
                log.warn(e.getMessage(), e);
            }
        }
        finally {
            try {
                client.close();
            }
            catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
        }

        return null;
    }

    public static String toHtml(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }

        return str.replaceAll("\r\n", "<br/>").replaceAll("\n", "<br/>");
    }


    public static Map<String, String> copyImagesToAliOSS(List<String> imageUrls, String imagePath) {
        if (CollectionUtils.isEmpty(imageUrls) || StringUtils.isEmpty(imagePath)) {
            return new HashMap<>(0);
        }
        Map<String, String> url2NewUrlMap = new HashMap<>(imageUrls.size());
        Map<String, File> tempMap = new HashMap<>(imageUrls.size());
        for (String url : imageUrls) {
            if (StringUtils.isEmpty(url)) {
                continue;
            }

            FileOutputStream os = null;
            try {
                // 处理亚马逊爬虫爬取的listing图片，缩放最小边在800-1000之间的图片
                File dealFile = dealCrawlerPhoto(url);
                if (dealFile != null) {
                    tempMap.put(url, dealFile);
                    continue;
                }
                String filePath = null;
                if (url.contains(FILL_SYSTEM_URl)) {
                    filePath = url.substring(url.indexOf(FILL_SYSTEM_URl) + FILL_SYSTEM_URl.length());
                }else if (url.contains(FILL_TEST_SYSTEM_URl)) {
                    filePath = url.substring(url.indexOf(FILL_TEST_SYSTEM_URl) + FILL_TEST_SYSTEM_URl.length());
                }
                int position = filePath.lastIndexOf("/");
                if (position < 0) {
                    position = filePath.lastIndexOf("\\");
                }
                String fileName = filePath.substring(position + 1);
                if (fileName.lastIndexOf(".") < 0) {
                    continue;
                }
                String suffix = fileName.substring(fileName.lastIndexOf("."));
                File temp = File.createTempFile("amazon-image", suffix);
                //ftpDownload(ftp, relativePath, fileName, os);
                downloadFile(url, temp);
                tempMap.put(url, temp);
            }
            catch (Exception e) {
                log.warn(e.getMessage(), e);
            }
            finally {
                IOUtils.closeQuietly(os);
            }
        }

        // 上传文件到OSS 或者 复制oss
        if (MapUtils.isNotEmpty(tempMap)) {
            AliOss aliOss = AliOss.Amazon_Image;
            String prefix = aliOss.getPrefix();
            Map<String, Object> fileMap = new HashMap<>(tempMap.size());
            // oss对象Object名称在使用UTF-8编码后长度必须在 1-1023字节之间，而且不能包含回车、换行、以及xml1.0不支持的字符，同时也不能以“/”或者“\”开头
            imagePath = imagePath.substring(1);
            OSS ossClient = OssUtils.getOssClientSingle(aliOss);
            try {

                for (Map.Entry<String, File> entry : tempMap.entrySet()) {
                    File temp = entry.getValue();
                    String suffix = temp.getName().substring(temp.getName().lastIndexOf("."));
                    String copyName = UUID.randomUUID().toString() + suffix;
                    if (!temp.exists() || !temp.canRead() || temp.length() <= 0) {
                        continue;
                    }

                    String newUrl = prefix + imagePath + copyName;
                    // 复制OSS
                    Boolean copyOssFlag = ImageUtils.checkIsOriginalCopyObject(ossClient, entry.getKey(), newUrl, aliOss);
                    if (!copyOssFlag) {
                        fileMap.put(imagePath + copyName, temp);
                    }
                    url2NewUrlMap.put(entry.getKey(), newUrl);
                }
                if (fileMap.size() > 0) {
                    // 上传文件2OSS
                    OssUtils.uploadObj(AliOss.Amazon_Image, fileMap);
                }
                // 删除临时文件
                tempMap.forEach((k, file) -> {
                    file.delete();
                });
            }finally {
                // 必须手动回收
                ossClient.shutdown();
            }
        }

        return url2NewUrlMap;
    }

    public static Map<String, String> copyImagesToAliOSS(String productSku, List<String> imageUrls, String imagePath) {
        if (CollectionUtils.isEmpty(imageUrls) || StringUtils.isEmpty(imagePath)) {
            return new HashMap<>(0);
        }
        Map<String, String> url2NewUrlMap = new HashMap<>(imageUrls.size());
        Map<String, File> tempMap = new HashMap<>(imageUrls.size());
        List<String> urlList = new ArrayList<>() {
        };
        AliOss aliOss = AliOss.Amazon_Image;
        OSS ossClient = OssUtils.getOssClientSingle(aliOss);
        try {
            for (String url : imageUrls) {
                if (StringUtils.isEmpty(url)) {
                    continue;
                }

                FileOutputStream os = null;
                try {
                    // 处理亚马逊爬虫爬取的listing图片，缩放最小边在800-1000之间的图片
                    File dealFile = dealCrawlerPhoto(url);
                    if (dealFile != null) {
                        tempMap.put(url, dealFile);
                        continue;
                    }
                    String filePath = null;
                    if (url.contains(FILL_SYSTEM_URl)) {
                        filePath = url.substring(url.indexOf(FILL_SYSTEM_URl) + FILL_SYSTEM_URl.length());
                    } else if (url.contains(FILL_TEST_SYSTEM_URl)) {
                        filePath = url.substring(url.indexOf(FILL_TEST_SYSTEM_URl) + FILL_TEST_SYSTEM_URl.length());
                    }
                    int position = filePath.lastIndexOf("/");
                    if (position < 0) {
                        position = filePath.lastIndexOf("\\");
                    }
                    String fileName = filePath.substring(position + 1);
                    if (fileName.lastIndexOf(".") < 0) {
                        continue;
                    }
                    String suffix = fileName.substring(fileName.lastIndexOf("."));
                    if (ImageUtils.checkIsOriginal(url)) {
                        // 原图不下载
                    } else {
                        File temp = File.createTempFile("amazon-image", suffix);
                        downloadFile(url, temp);
                        tempMap.put(url, temp);
                    }
                    urlList.add(url);
                } catch (Exception e) {
                    log.warn(e.getMessage(), e);
                } finally {
                    IOUtils.closeQuietly(os);
                }
            }
            // 上传文件到OSS
            if (CollectionUtils.isNotEmpty(urlList)) {
                String prefix = AliOss.Amazon_Image.getPrefix();
                Map<String, Object> fileMap = new HashMap<>(tempMap.size());
                // oss对象Object名称在使用UTF-8编码后长度必须在 1-1023字节之间，而且不能包含回车、换行、以及xml1.0不支持的字符，同时也不能以“/”或者“\”开头
                imagePath = imagePath.substring(1);
                for (String url : urlList) {
                    try {
                        int beginIndex = url.lastIndexOf(".");
                        String suffix = url.substring(beginIndex);
                        String copyName = UUID.randomUUID().toString() + suffix;
                        String newUrl = prefix + imagePath + copyName;
                        if (ImageUtils.checkIsOriginal(url)) {
                            // 复制OSS
                            Boolean copyOssFlag = ImageUtils.checkIsOriginalCopyObject(ossClient, url, newUrl, aliOss);
                            if (copyOssFlag) {
                                url2NewUrlMap.put(url, newUrl);
                            }
                        } else {
                            File temp = tempMap.get(url);
                            fileMap.put(imagePath + copyName, temp);
                            url2NewUrlMap.put(url, newUrl);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
                // 上传文件2OSS
                if (fileMap.size() > 0) {
                    //OSS ossClient = OssUtils.getOssClientSingle(AliOss.Amazon_Image);
                    fileMap.forEach((name, data) -> {
                        int retryCount = 0;
                        while (retryCount++ < 3) {
                            try {
                                if (data instanceof byte[]) {
                                    OssUtils.uploadObj(ossClient, AliOss.Amazon_Image.getBucketname(), name, (byte[]) data);
                                } else if (data instanceof String) {
                                    OssUtils.uploadObj(ossClient, AliOss.Amazon_Image.getBucketname(), name, ((String) data).getBytes());
                                } else if (data instanceof File) {
                                    OssUtils.uploadObj(ossClient, AliOss.Amazon_Image.getBucketname(), name, (File) data);
                                }
                                break;
                            } catch (Exception e) {
                                log.error(String.format("%s 上传阿里云第%s次错误", productSku, retryCount), e);
                            }
                        }
                    });
                }
                // 删除临时文件
                tempMap.forEach((k, file) -> {
                    file.delete();
                });
            }
        } finally {
            // 必须手动回收
            ossClient.shutdown();
        }

        return url2NewUrlMap;
    }


    public static void downloadFile(String relativePath, File  file) {
        try {
            FileOutputStream  os = new FileOutputStream(file);
            URL url = new URL(relativePath);
            URLConnection con = url.openConnection();
            con.setConnectTimeout(40*1000);
            InputStream is=con.getInputStream();
            byte[] data =new byte[1024];
            int len = 0;
            while ((len = is.read(data)) != -1) {
                os.write(data, 0, len);
            }
            os.close();
            is.close();
        }
        catch (IOException e) {
            log.error("从文件系统下载失败路径" + relativePath + e.getMessage());
            e.printStackTrace();
        }
    }


    public static boolean loadImg(String url) {
        try {
            BufferedImage read = ImageIO.read(new URL(url));
            if (read != null) {
                return true;
            }
        }
        catch (IOException e) {
        }
        return false;
    }

    public static List<XsdRouteData> transferAmazonSkuThemes2RouteDatas(AmazonSku amazonSku,
            Map<String, ElementWrapper> themeRoutes) {
        if (amazonSku == null || CollectionUtils.isEmpty(amazonSku.getNameValues()) || MapUtils.isEmpty(themeRoutes)) {
            return new ArrayList<XsdRouteData>(0);
        }

        List<NameValue> nameValues = amazonSku.getNameValues();
        List<XsdRouteData> result = new ArrayList<XsdRouteData>();
        for (NameValue nameValue : nameValues) {
            String name = nameValue.getName();
            ElementWrapper themeWrapper = themeRoutes.get(name);
            if (themeWrapper != null) {
                XsdRouteData xsdRouteData = new XsdRouteData();
                result.add(xsdRouteData);
                xsdRouteData.setRoute(themeWrapper.getRoute());
                // 匹配Color和Size的简写对应的实际值
                xsdRouteData.setValues(
                        Arrays.asList(SkuThemeUtils.getThemeValue(nameValue.getName(), nameValue.getValue())));
                List<Attribute<String, String>> attrs = nameValue.getAttrs();
                if (CollectionUtils.isNotEmpty(attrs)) {
                    Map<String, List<String>> routeAttrs = attrs.stream().collect(Collectors.toMap(attr -> {
                        return attr.getName();
                    }, attr -> {
                        return Arrays.asList(attr.getValue());
                    }));
                    xsdRouteData.setAttrs(routeAttrs);
                }

                // 添加扩展属性，eg：SizeMap，ColorMap
                List<NameValue> extralNameValues = nameValue.getExtralNameValues();
                if (CollectionUtils.isNotEmpty(extralNameValues)) {
                    for (NameValue extralNameValue : extralNameValues) {
                        ElementWrapper extralWrapper = themeRoutes.get(extralNameValue.getName());
                        if (extralWrapper != null && StringUtils.isNotEmpty(extralNameValue.getValue())) {
                            XsdRouteData extralRouteData = new XsdRouteData();
                            result.add(extralRouteData);
                            extralRouteData.setRoute(extralWrapper.getRoute());
                            extralRouteData.setValues(Arrays.asList(extralNameValue.getValue()));
                        }
                    }
                }
            }
        }

        return result;
    }

    public static boolean containsIgnoreCase(Collection<String> collection, String value) {
        if (CollectionUtils.isEmpty(collection) || StringUtils.isEmpty(value)) {
            return false;
        }

        for (String item : collection) {
            if (value.equalsIgnoreCase(item)) {
                return true;
            }
        }

        return false;
    }

    public static String getIgnoreCase(Collection<String> collection, String value) {
        if (CollectionUtils.isEmpty(collection) || StringUtils.isEmpty(value)) {
            return null;
        }

        for (String item : collection) {
            if (value.equalsIgnoreCase(item)) {
                return item;
            }
        }

        return null;
    }

    public static <T> T getListIndexValue(List<T> list, int index) {
        if (CollectionUtils.isEmpty(list) || list.size() <= index) {
            return null;
        }

        return list.get(index);
    }

    public static byte[] HmacSHA256(String data, byte[] key) throws Exception {
        String algorithm = "HmacSHA256";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(key, algorithm));
        return mac.doFinal(data.getBytes("UTF8"));
    }

    public static byte[] getSignatureKey(String key, String... datas) throws Exception {
        byte[] kSecret = ("AWS4" + key).getBytes("UTF8");
        for (String data : datas) {
            kSecret = HmacSHA256(data, kSecret);
        }
        byte[] kSigning = HmacSHA256("aws4_request", kSecret);
        return kSigning;
    }

    public static String filterFileName(String fileName) {
        return StringUtils.isEmpty(fileName) ? null : FILE_PATTERN.matcher(fileName).replaceAll("");
    }

    /**
     * 下载亚马逊爬虫爬取的listing图片，缩放有一条边在800-1000之间的图片
     *
     * @param imageUrl
     * @return java.lang.String
     * @Author: listen
     * @Date 2018/11/26 17:37
     * @Version: 0.0.1
     */
    public static File dealCrawlerPhoto(String imageUrl) {
        // 判断是否为亚马逊网站图片
        if (StringUtils.isEmpty(imageUrl) || !imageUrl.startsWith("https://m.media-amazon.com/")) {
            return null;
        }
        try {
            URL image = new URL(imageUrl);
            String suffix = image.getPath().substring(image.getPath().lastIndexOf("."));
            File temp = File.createTempFile("amazon-image", suffix);
            // 下载图片，判断图片是否有一边边长在800-1000px，适当处理图片
            downloadAndDealImage(image, temp);
            return temp;
        }
        catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 下载图片，判断图片是否有一边边长在800-1000px，适当处理图片
     *
     * @param imageUrl
     * @param temp
     * @return void
     * @Date 2018/11/26 17:21
     * @Version: 0.0.1
     */
    private static void downloadAndDealImage(URL imageUrl, File temp) throws IOException {
        BufferedImage read = ImageIO.read(imageUrl);
        String suffix = imageUrl.getPath().substring(imageUrl.getPath().lastIndexOf(".") + 1).toUpperCase();
        int min = Math.min(read.getWidth(), read.getHeight());
        if ((min >= 800 && min < 1000)) {
            // 计算缩放比例
            double scaling = 1000D / min;

            // 图像的仿射变换——翻转（Flip）、旋转（Rotation）、平移（Translation）、缩放（Scale）和错切（Shear）
            AffineTransformOp ato = new AffineTransformOp(AffineTransform.getScaleInstance(scaling, scaling), null);
            Image itemp = ato.filter(read, null);

            // 保存
            ImageIO.write((BufferedImage) itemp, suffix, temp);
            return;
        }
        ImageIO.write(read, suffix, temp);
    }

    /**
     * 初始化模板平台sku，以及是否upc豁免
     *
     * @param templates
     * @return void
     * @Author: listen
     * @Date 2019/1/3 11:16
     * @Version: 0.0.1
     */
    public static void initAmazonTemplateSellerSKU(List<AmazonTemplateBO> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return;
        }

        AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);
        AmazonProcessReportService amazonProcessReportService = SpringUtils.getBean(AmazonProcessReportService.class);
        Iterator<AmazonTemplateBO> iterator = templates.iterator();
        while (iterator.hasNext()) {
            AmazonTemplateBO template = iterator.next();
            // 线程安全地生成sellerSKU，使用sellerId+parentSku作为key
            String lockKey = template.getSellerId() + template.getParentSku();
            Lock lock = com.estone.erp.publish.amazon.call.util.MapUtils.putIfAbsent(RUNNING_TASK_MAP, lockKey,
                    () -> {
                        return new ReentrantLock();
                    });
            try {
                lock.lock();
                //设置upc豁免标志
                // 单个初始化模板平台货号，单品及变体
                amazonTemplateService.generateAmazonTemplateSellerSKU(template);
                amazonTemplateService.handleAmazonTemplateUpcExempt(template);
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                generateErrorSkuRuleTemplate(template, amazonProcessReportService, e.getMessage());
                // 无sku生成规则的模板移除
                iterator.remove();
            }
            finally {
                RUNNING_TASK_MAP.remove(lockKey);
                lock.unlock();
            }
        }
    }

    private static void generateErrorSkuRuleTemplate(AmazonTemplateWithBLOBs template,
                                                     AmazonProcessReportService amazonProcessReportService, String message) {

        AmazonProcessReport report = new AmazonProcessReport();
        report.setCreationDate(new Date());
        report.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        report.setAccountNumber(template.getSellerId());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(template.getParentSku() + "_ERROR");
        report.setRelationId(template.getId());
        report.setRelationType(ProcessingReportTriggleType.Template.name());
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setStatus(false);
        message = StringUtils.isBlank(message) ? "" : message;
        report.setResultMsg("sku生成规则报错，无法刊登:" + message);
        report.setFinishDate(new Timestamp(System.currentTimeMillis()));
        amazonProcessReportService.insert(report);
    }

    /**
     * 上传GPSR pdf到OSS
     *
     * @param tempFile   pdf文件
     * @param merchantId 套账Id
     * @param path       文件路径
     * @param asin
     * @return
     */
    public static String uploadFileAmazonGPSRPdf2Oss(File tempFile, String merchantId, String path, String asin) {
        // 同一套账到同一bucket merchantId 对 buckets 取余
        String bucket = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "GPSR", "img_bucket", 10);
        String[] bucketArray = StringUtils.split(bucket, ",");
        if (bucketArray == null) {
            throw new BusinessException("GPSR OSS bucket 资源加载异常");
        }
        int hash = merchantId.hashCode();
        int resourcePathIndex = Math.abs(hash % bucketArray.length);
        String bucketName = bucketArray[resourcePathIndex];
        //log.info("店铺:{},对应的资源路径,index:{},{}", merchantId, resourcePathIndex, bucketName);
        String fileName = tempFile.getName();
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        OSS ossClient = OssUtils.getOssClientSingle(AliOss.Amazon_Image);
        try {
            String copyName = asin + ".ps01" + suffix;
            String pdfPath = path.substring(1);
            String key = pdfPath + copyName;
            ApiResult<String> apiResult = RetryUtil.doRetry(() -> {
                OssUtils.uploadObj(ossClient, bucketName, key, tempFile);
                String prefix = AliOss.Amazon_Image.getPrefix(bucketName);
                String newUrl = prefix + key;
                return ApiResult.newSuccess(newUrl);
            }, 3);
            tempFile.deleteOnExit();
            if (apiResult.isSuccess()) {
                return apiResult.getResult();
            }
            //log.error("upload amazon gpsr fail, pdfLink:{},error:{}", pdfLink, apiResult.getResult());
        } catch (Exception e) {
            log.error("upload amazon gpsr fail, pdfLink:{},error:{}", fileName, e.getMessage(), e);
            throw new BusinessException("上传GPSR异常," + e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return "";
    }
}
