package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Amazon 必刊登新品
 * <AUTHOR>
 * @date 2023-10-23 10:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmazonMustPublishNewSpuDO {
    private String spu;
    private String fullpathcode;
    private String categoryPath;
    /**
     * 产品类型 0:单属性 1:多属性
     */
    private Integer productType;
    private Date createdTime;
    private SalesProhibitionsVo salesProhibitionsVo;
}
