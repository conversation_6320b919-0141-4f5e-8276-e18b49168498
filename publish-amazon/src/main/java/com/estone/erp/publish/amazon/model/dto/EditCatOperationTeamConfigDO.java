package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.system.product.bean.ProductCategoryTree;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 类目运营团队配置
 * <AUTHOR>
 * @date 2024-03-11 15:51
 */
@Data
public class EditCatOperationTeamConfigDO {

    /**
     * id
     */
    private Integer id;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 主管
     */
    @NotEmpty(message = "主管不能为空")
    private List<String> saleIds;

    /**
     * 类目配置数据
     */
    @NotEmpty(message = "请勾选类目")
    private List<ProductCategoryTree> dataList;

}
