package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPriceUpdateRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * amazon调价记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface AmazonPriceUpdateRecordMapper extends BaseMapper<AmazonPriceUpdateRecord> {

    List<Map<Object, Object>> getTidbPageMetaMap(@Param(Constants.WRAPPER) LambdaQueryWrapper<AmazonPriceUpdateRecord> wrapper);
}
