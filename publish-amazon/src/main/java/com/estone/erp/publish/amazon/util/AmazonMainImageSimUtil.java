package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.model.AmazonListingMainImageComparison;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog;
import com.estone.erp.publish.amazon.mq.model.MainImageSyncComparisonErrorType;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.platform.bo.ImageSimResult;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 图片相似度工具
 */
public class AmazonMainImageSimUtil {

    @Data
    public static class Request {
        private String mainUrl;
        private List<String> skuUrl;
    }

    /**
     * 过滤有效图片
     *
     * @param mainSku
     * @param articleNumber
     * @param productImages
     * @return
     */
    public static List<String> getFilterImages(String mainSku, String articleNumber, List<String> productImages) {
        String spu = String.format("/%s.", mainSku);
        String sku = String.format("/%s.", articleNumber);
        String sku00 = String.format("/%s.", articleNumber + "-00");
        String sku000 = String.format("/%s.", articleNumber + "-000");
        String skuKd = String.format("/%s.", articleNumber + "-kd");

        return productImages.stream().filter(image ->
                        image.contains(sku)
                                || image.contains(spu)
                                || image.contains(sku00)
                                || image.contains(sku000)
                                || image.contains(skuKd))
                .distinct()
                .sorted((o1, o2) -> {
                    boolean isMainSku = o1.contains(mainSku);
                    boolean isSku = o1.contains(sku);
                    boolean isSku00 = o1.contains(sku00);
                    boolean isSku000 = o1.contains(sku000);
                    boolean isSkukd = o1.contains(skuKd);

                    boolean isMainSkuOther = o2.contains(mainSku);
                    boolean isSkuOther = o2.contains(sku);
                    boolean isSku00Other = o2.contains(sku00);
                    boolean isSku000Other = o2.contains(sku000);
                    boolean isSkukdOther = o2.contains(skuKd);
                    if (isSku && !isSkuOther) {
                        return -1;
                    }
                    if (!isSku && isSkuOther) {
                        return 1;
                    }
                    if (isSku00 && !isSku00Other) {
                        return -1;
                    }
                    if (!isSku00 && isSku00Other) {
                        return 1;
                    }
                    if (isSku000 && !isSku000Other) {
                        return -1;
                    }
                    if (!isSku000 && isSku000Other) {
                        return 1;
                    }
                    if (isSkukd && !isSkukdOther) {
                        return -1;
                    }
                    if (!isSkukd && isSkukdOther) {
                        return 1;
                    }
                    if (isMainSku && !isMainSkuOther) {
                        return -1;
                    }
                    if (!isMainSku && isMainSkuOther) {
                        return 1;
                    }
                    return o1.compareTo(o2);
                })
                .collect(Collectors.toList());
    }


    public static void addFailDataInfo(EsAmazonProductListing esAmazonProductListing, List<AmazonListingMainImageComparison> insertList, String saleId, String systemUrl, BigDecimal systemImage, BigDecimal VALUE_100) {
        List<String> comparisionCharList = esAmazonProductListing.getComparisionCharList();
        String articleNumber = esAmazonProductListing.getArticleNumber();
        AmazonListingMainImageComparison comparison = new AmazonListingMainImageComparison();
        comparison.setListingMainImage(esAmazonProductListing.getMainImage());
        // 判断 systemUrl 是不是sku同名，不是就取规则的comparisionCharList下sku图 comparisionCharList 是排序过的
        if (StringUtils.isNotBlank(systemUrl) && systemImage != null && CollectionUtils.isNotEmpty(comparisionCharList)) {
            boolean contains = systemUrl.contains(String.format("/%s.", articleNumber));
            if (!contains) {
                // 不是sku图像，更换sku图像
                systemUrl = comparisionCharList.get(0);
            }
        }
        if (StringUtils.isBlank(systemUrl) && systemImage == null && CollectionUtils.isNotEmpty(comparisionCharList)) {
            systemUrl = comparisionCharList.get(0);
        }

        comparison.setSystemMainImage(systemUrl);
        if (systemImage != null && VALUE_100 != null) {
            comparison.setSimilarity(systemImage.multiply(VALUE_100));
        }
        comparison.setIsOnline(true);
        comparison.setAccountNumber(esAmazonProductListing.getAccountNumber());
        comparison.setSite(esAmazonProductListing.getSite());
        comparison.setParentAsin(esAmazonProductListing.getParentAsin());
        comparison.setSonAsin(esAmazonProductListing.getSonAsin());
        comparison.setSellerSku(esAmazonProductListing.getSellerSku());
        comparison.setArticleNumber(esAmazonProductListing.getArticleNumber());
        comparison.setSalesId(saleId);
        if (esAmazonProductListing.getOpenDate() != null) {
            comparison.setOpenDate(new Timestamp(esAmazonProductListing.getOpenDate().getTime()));
        }
        Timestamp timestamp = new Timestamp(new Date().getTime());
        comparison.setComparisonTime(timestamp);
        comparison.setMainSku(esAmazonProductListing.getMainSku());
        comparison.setCreateDate(timestamp);
        insertList.add(comparison);
    }

    public static void addSuccessLog(List<AmazonMainImageComparisonLog> insertLog, EsAmazonProductListing esAmazonProductListing, List<ImageSimResult.Result> results, Boolean full, String skuUrl, BigDecimal maxUrlSim) {
        Request requestQuery = new Request();
        requestQuery.setMainUrl(esAmazonProductListing.getMainImage());
        requestQuery.setSkuUrl(esAmazonProductListing.getComparisionCharList());

        AmazonMainImageComparisonLog amazonMainImageComparisonLog = new AmazonMainImageComparisonLog();
        amazonMainImageComparisonLog.setAccountNumber(esAmazonProductListing.getAccountNumber());
        amazonMainImageComparisonLog.setMainSku(esAmazonProductListing.getMainSku());
        amazonMainImageComparisonLog.setArticleNumber(esAmazonProductListing.getArticleNumber());
        amazonMainImageComparisonLog.setSite(esAmazonProductListing.getSite());
        amazonMainImageComparisonLog.setFull(full);
        amazonMainImageComparisonLog.setRequest(JSON.toJSONString(requestQuery));
        amazonMainImageComparisonLog.setResponse(JSON.toJSONString(results));
        amazonMainImageComparisonLog.setStatus(true);
        amazonMainImageComparisonLog.setCreateTime(new Timestamp(new Date().getTime()));
        amazonMainImageComparisonLog.setEsListingId(esAmazonProductListing.getId());
        amazonMainImageComparisonLog.setErrorType(MainImageSyncComparisonErrorType.SUCCESS.getCode());
        amazonMainImageComparisonLog.setMainSkuUrl(esAmazonProductListing.getMainImage());
        amazonMainImageComparisonLog.setMaxSimUrl(skuUrl);
        amazonMainImageComparisonLog.setMaxSim(maxUrlSim);
        insertLog.add(amazonMainImageComparisonLog);
    }

    public static void addErrorLog(EsAmazonProductListing esAmazonProductListing, List<AmazonMainImageComparisonLog> insertLog, Boolean full, MainImageSyncComparisonErrorType typeError) {
        addErrorLog(esAmazonProductListing, insertLog, full, typeError.getCode(), typeError.getDesc(), null, null);
    }

    public static void addErrorLog(EsAmazonProductListing esAmazonProductListing, List<AmazonMainImageComparisonLog> insertLog, Boolean full, Integer errorType, String response, String maxSimUrl, BigDecimal maxSim) {
        AmazonMainImageComparisonLog log = new AmazonMainImageComparisonLog();
        log.setAccountNumber(esAmazonProductListing.getAccountNumber());
        log.setSite(esAmazonProductListing.getSite());
        log.setMainSku(esAmazonProductListing.getMainSku());
        log.setArticleNumber(esAmazonProductListing.getArticleNumber());
        log.setFull(full);
        log.setStatus(false);
        log.setResponse(response);
        Request requestQuery = new Request();
        requestQuery.setMainUrl(esAmazonProductListing.getMainImage());
        requestQuery.setSkuUrl(esAmazonProductListing.getComparisionCharList());
        log.setRequest(JSON.toJSONString(requestQuery));
        log.setCreateTime(new Timestamp(new Date().getTime()));
        log.setEsListingId(esAmazonProductListing.getId());
        log.setErrorType(errorType);
        log.setMainSkuUrl(esAmazonProductListing.getMainImage());
        log.setMaxSim(maxSim);
        log.setMaxSimUrl(maxSimUrl);
        insertLog.add(log);
    }

    public static AmazonListingMainImageComparison initMainImageData(EsAmazonProductListing esAmazonProductListing, ImageSimResult.Result systemImage, BigDecimal VALUE_100, String saleId) {
        String skuUrl = systemImage.getSku_url();
        // 判断
        boolean contains = skuUrl.contains(String.format("/%s.", esAmazonProductListing.getArticleNumber()));
        if (!contains) {
            // 不是sku图像，更换sku图像, comparisionCharList 是排序过的，日志就记录原本的比较的skuUrl，实际入库就设置其他
            skuUrl = esAmazonProductListing.getComparisionCharList().get(0);
        }
        AmazonListingMainImageComparison comparison = new AmazonListingMainImageComparison();
        comparison.setListingMainImage(esAmazonProductListing.getMainImage());
        comparison.setSystemMainImage(skuUrl);
        comparison.setSimilarity(systemImage.getSim().multiply(VALUE_100));
        comparison.setIsOnline(true);
        comparison.setAccountNumber(esAmazonProductListing.getAccountNumber());
        comparison.setSite(esAmazonProductListing.getSite());
        comparison.setParentAsin(esAmazonProductListing.getParentAsin());
        comparison.setSonAsin(esAmazonProductListing.getSonAsin());
        comparison.setSellerSku(esAmazonProductListing.getSellerSku());
        comparison.setArticleNumber(esAmazonProductListing.getArticleNumber());
        comparison.setSalesId(saleId);
        if (esAmazonProductListing.getOpenDate() != null) {
            comparison.setOpenDate(new Timestamp(esAmazonProductListing.getOpenDate().getTime()));
        }
        Timestamp timestamp = new Timestamp(new Date().getTime());
        comparison.setComparisonTime(timestamp);
        comparison.setMainSku(esAmazonProductListing.getMainSku());
        comparison.setCreateDate(timestamp);
        return comparison;
    }

    /**
     * @param articleNumber
     * @param results
     * @param fillterImages 过滤的图片 已经排序过的
     * @return
     */
    private static ImageSimResult.Result getSystemImage(String articleNumber, List<ImageSimResult.Result> results, List<String> fillterImages) {
        Optional<ImageSimResult.Result> first = results.stream().filter(a -> a.getSku_url().contains(String.format("/%s.", articleNumber))).findFirst();
        return first.orElse(results.get(0));
    }

    /**
     * 全量计算相似度时使用，速率快
     *
     * @param collect
     */
    public static void doCalculateImageSim(String imageHttpUrl, List<EsAmazonProductListing> collect) {
        CompletableFuture.allOf(collect.stream().map(esAmazonProductListing -> CompletableFuture.runAsync(() -> {
            String mainImage = esAmazonProductListing.getMainImage();
            List<String> comparisionCharList = esAmazonProductListing.getComparisionCharList();
            int doNumber = 2;
            ApiResult<ImageSimResult> result = null;
            while (doNumber > 0) {
                doNumber--;
                result = AmazonReportSolutionUtil.newCalculateImgsSim(imageHttpUrl, mainImage, comparisionCharList);
                if (checkSuccess(result)) {
                    break;
                }
            }
            esAmazonProductListing.setResult(result);
        }, AmazonExecutors.CALL_SIM_IMAGES_POLL)).toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 增量
     * 调整为最多资源
     *
     * @param collect
     */
    public static void redoCalculateImageSim(String httpUrl, List<EsAmazonProductListing> collect) {
        CompletableFuture.allOf(collect.stream().map(esAmazonProductListing -> CompletableFuture.runAsync(() -> {
            String mainImage = esAmazonProductListing.getMainImage();
            List<String> comparisionCharList = esAmazonProductListing.getComparisionCharList();
            int doNumber = 2;
            ApiResult<ImageSimResult> result = null;
            while (doNumber > 0) {
                doNumber--;
                result = AmazonReportSolutionUtil.newCalculateImgsSim(httpUrl, mainImage, comparisionCharList);
                if (checkSuccess(result)) {
                    break;
                }
            }
            esAmazonProductListing.setResult(result);
        }, AmazonExecutors.CALL_SIM_IMAGES_POLL_NO_GPU)).toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 检查是否为asin图片错误，是的话就重新设置
     *
     * @param result 算法结果
     * @return
     */
    private static boolean checkSuccess(ApiResult<ImageSimResult> result) {
        if (result.isSuccess()) {
            ImageSimResult imageSimResult = result.getResult();
            List<ImageSimResult.Result> response = imageSimResult.getResponse();
            ImageSimResult.Result result1 = response.get(0);
            // 读取不到主图，大概率是被限制了该图片请求，重试
            return StringUtils.isNotBlank(result1.getType()) && result1.getType().contains("asin");
        }
        return false;
    }

    public static boolean checkDataError(EsAmazonProductListing esAmazonProductListing) {
        return StringUtils.isBlank(esAmazonProductListing.getArticleNumber())
                || "匹配不到货号".equals(esAmazonProductListing.getArticleNumber())
                || StringUtils.isBlank(esAmazonProductListing.getMainImage());
    }
}
