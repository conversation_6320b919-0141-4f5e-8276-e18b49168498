package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.componet.AmazonListingInfringementHelper;
import com.estone.erp.publish.amazon.model.AmazonListingAllInfringAccount;
import com.estone.erp.publish.amazon.service.AmazonListingAllInfringAccountService;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.tidb.publishAmazon.service.TidbAmazonProductListingService;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingCheckLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonListingCheckLogService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: ${监听消费临时检测手工标记禁用商标词数据，存储到临时表}
 */
@Slf4j
public class AmazonListingCheckAllBrandMqListener implements ChannelAwareMessageListener {

    public static String fields = "id,accountNumber, site, sellerSku,parentAsin, articleNumber, itemName," +
            " itemDescription, brandName, bulletPoint, searchTerms, colorName, sizeName";
    private static final int limit = 300;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private TidbAmazonProductListingService tidbAmazonProductListingService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonListingCheckLogService amazonListingCheckLogService;
    @Autowired
    private AmazonListingInfringementHelper amazonListingInfringementHelper;

    @Resource
    private AmazonListingAllInfringAccountService amazonListingAllInfringAccountService;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }
        try {
            Boolean isSuccess = doService(body);
            if (isSuccess) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        } catch (Exception e) {
            log.error("消费失败,{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        String accountNumber = null;
        try {
            String bodyStr = JSON.parseObject(body, String.class);
            accountNumber = bodyStr;
            if (StringUtils.isEmpty(accountNumber)) {
                return Boolean.FALSE;
            }
            // 判断当前时间，如果是 凌晨三点-四点，则睡眠一小时，避开大数据写销量时间
            Date time = new Date();
            String startDate = DateUtils.format(time, DateUtils.YMD_FORMAT) + " 05:00:00";
            String endDate = DateUtils.format(time, DateUtils.YMD_FORMAT) + " 06:00:00";
            Boolean flag = false;
            Date dateBegin = DateUtils.parseDate(startDate, DateUtils.STANDARD_DATE_PATTERN);;
            Date dateEnd = DateUtils.parseDate(endDate,DateUtils.STANDARD_DATE_PATTERN);
            flag = DateUtils.betweenStartTimeAndEndTime(time,dateBegin,dateEnd);
            if (flag){
                //睡眠，避开大数据写销量
                log.info(flag +"账号睡眠");
                Thread.sleep(48 * 100 * 1000);
            }
        } catch (Exception e) {
            log.error("解析 mq消息体异常 -> {}", body);
            return Boolean.TRUE;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int offset = 0;
        Long lastId = 0L;
        String site = amazonProductListingService.getSiteByAccount(accountNumber);
        while (true) {
            try {
                AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
                AmazonProductListingExample.Criteria criteria = amazonProductListingExample.createCriteria();
                criteria.andAccountNumberEqualTo(accountNumber)
                        .andIsOnlineEqualTo(true);
                amazonProductListingExample.setTableIndex(amazonProductListingService.getTableIndex(site));
                amazonProductListingExample.setColumns(fields);
                amazonProductListingExample.setLimit(limit);
                amazonProductListingExample.setOrderByClause("id ASC");
                if (lastId != null && lastId > 0) {
                    criteria.andIdGreaterThan(lastId);
                }
                List<AmazonProductListing> amazonProductListingList = tidbAmazonProductListingService.selectCustomColumnByExample(amazonProductListingExample,site);
                if (CollectionUtils.isEmpty(amazonProductListingList)) {
                    log.info("店铺[{}] 侵权词校验执行完毕,累计处理：{}", accountNumber, offset);
                    break;
                }

                HashSet<String> existInfringementWordSellerskus = selectContineInfringementWordData(amazonProductListingList);
                List<CompletableFuture<Void>> futureList = new ArrayList<>();
                for (AmazonProductListing item : amazonProductListingList) {
                    try {
                        String sellersku =item.getSellerSku();
                        String _id = item.getAccountNumber() + "_"+ sellersku;
                        boolean existInfringementWord = existInfringementWordSellerskus.contains(sellersku);
                        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() ->
                                execute(Collections.singletonList(_id), item,existInfringementWord),AmazonExecutors.CHECK_LISTING_INFRINGEMENTWORD_POOL);
                        futureList.add(completableFuture);
                    } catch (Exception e) {
                        log.error("AmazonIncrementTortCheckMqHandler中断异常:", e);
                    }
                }
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
                offset += amazonProductListingList.size();
                lastId = amazonProductListingList.stream().map(AmazonProductListing::getId).max(Long::compareTo).orElseGet(() -> 0L);
            } catch (Exception e) {
                log.error("AmazonIncrementTortCheckMqHandler中断异常:", e);
                break;
            }
        }
        stopWatch.stop();
        AmazonListingAllInfringAccount amazonListingAllInfringAccount = new AmazonListingAllInfringAccount();
        amazonListingAllInfringAccount.setAccountNumber(accountNumber);
        amazonListingAllInfringAccount.setListingSize(String.valueOf(offset));
        amazonListingAllInfringAccount.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        amazonListingAllInfringAccountService.insert(amazonListingAllInfringAccount);
        log.info("店铺listing侵权词校验完成:{},累计处理：{}, 耗时：{}", body, offset, stopWatch);
        return Boolean.TRUE;
    }

    /**
     * 旧数据包含侵权词的
     * @param amazonProductListingList
     * @return
     */
    private HashSet selectContineInfringementWordData(List<AmazonProductListing> amazonProductListingList){
        HashSet<String> existInfringementWordSellerskus = new HashSet<>();
        if (CollectionUtils.isEmpty(amazonProductListingList)){
            return existInfringementWordSellerskus;
        }
        List<String> sellerskuList = amazonProductListingList.stream().map(AmazonProductListing::getSellerSku).collect(Collectors.toList());
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setIsOnline(true);
        esAmazonProductListingRequest.setSellerSkuList(sellerskuList);
        esAmazonProductListingRequest.setIsInfringementWord(true);
        String [] esFields ={"sellerSku"};
        esAmazonProductListingRequest.setFields(esFields);
        Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest,limit,0);
        List<EsAmazonProductListing> listingList = page.getContent();
        if (CollectionUtils.isNotEmpty(listingList)) {
            List<String> sellerskus = listingList.stream()
                    .map(EsAmazonProductListing :: getSellerSku).collect(Collectors.toList());
            existInfringementWordSellerskus =  new HashSet<>(sellerskus);
        }
        return existInfringementWordSellerskus;
    }

    public void execute(List<String> esIds, AmazonProductListing amazonProductListing,boolean existInfringementWord) {
        try {
            amazonListingInfringementHelper.updateListingHandler(esIds, amazonProductListing,existInfringementWord);
        } catch (Exception e) {
            //log.error(amazonProductListing.getAccountNumber() + "执行校验商标词数据异常:" + amazonProductListing.getSellerSku() + e.getMessage());
            //  失败存入标识数据等待重试
            String _id = amazonProductListing.getAccountNumber() + "_"+ amazonProductListing.getSellerSku();
            AmazonListingCheckLog amazonListingCheckLog = new AmazonListingCheckLog();
            amazonListingCheckLog.setRelationId(_id);
            amazonListingCheckLog.setTryFlagArticleNumber(amazonProductListing.getArticleNumber());
            amazonListingCheckLog.setAccountNumber(amazonProductListing.getAccountNumber());
            amazonListingCheckLog.setSite(amazonProductListing.getSite());
            amazonListingCheckLog.setFlagDate(new Timestamp(System.currentTimeMillis()));
            amazonListingCheckLogService.insert(amazonListingCheckLog);
        }
    }

}