package com.estone.erp.publish.amazon.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskErrorTypeEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class ProductTypeTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column product_type_template.id
     */
    @ExcelIgnore
    private Integer id;

    /**
     * 站点 database column product_type_template.site
     */
    @ExcelProperty(value = "站点")
    private String site;

    /**
     * 模板名称 database column product_type_template.template_name
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 产品系统叶子类目code，多个逗号拼接 database column product_type_template.product_category_codes
     */
    @ExcelProperty(value = "产品系统叶子类目code")
    private String productCategoryCodes;

    /**
     * 产品系统二级三级类目名json database column product_type_template.product_category_names
     */
    @ExcelProperty(value = "产品系统二级三级类目名")
    private String productCategoryNames;

    /**
     * product_type database column product_type_template.product_type
     */
    @ExcelProperty(value = "新分类类型")
    private String newProductType;

    /**
     * product_type database column product_type_template.product_type
     */
    @ExcelProperty(value = "分类类型")
    private String productType;

    /**
     * 是否启用（1：启用，0：禁用） database column product_type_template.enable
     */
    @ExcelProperty(value = "状态(true：启用 false：禁用)")
    private Boolean enable;

    /**
     * 创建人 database column product_type_template.create_by
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间 database column product_type_template.create_time
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人 database column product_type_template.update_by
     */
    @ExcelIgnore
    private String updateBy;

    /**
     * 修改时间 database column product_type_template.update_time
     */
    @ExcelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 产品系统二级分类名
     */
    @ExcelIgnore
    private List<String> prodLevel2CategoryNameList;

    public List<String> getProdLevel2CategoryNameList(){
        if (StringUtils.isNotEmpty(this.getProductCategoryNames())){
            prodLevel2CategoryNameList = new ArrayList<>();
            String [] prodLevel2CategoryNames =this.getProductCategoryNames().split("@@@");
            for (String prodCategoryName : prodLevel2CategoryNames ){
                prodLevel2CategoryNameList.add(prodCategoryName.contains(">")? StringUtils.substringBefore(prodCategoryName,">"):prodCategoryName);
            }
        }
        if (CollectionUtils.isNotEmpty(prodLevel2CategoryNameList)){
            prodLevel2CategoryNameList = prodLevel2CategoryNameList.stream().distinct().collect(Collectors.toList());
        }
        return prodLevel2CategoryNameList;
    }
}