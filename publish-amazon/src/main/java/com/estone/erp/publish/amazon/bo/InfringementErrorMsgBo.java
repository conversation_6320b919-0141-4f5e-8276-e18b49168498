package com.estone.erp.publish.amazon.bo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class InfringementErrorMsgBo {

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 标题包含的侵权词
     */
    private String titleInfringement;

    /**
     * 描述包含的侵权词
     */
    private String descInfringement;

    /**
     * 关键词包含的侵权词
     */
    private String searchDataInfringement;

    /**
     * 五点描述包含的侵权词
     */
    private String bullpointInfringement;

    /**
     * 品牌包含的侵权词
     */
    private String brandInfringement;

    /**
     * 属性包含的侵权词
     */
    private String attrInfringement;

    /**
     * 站点
     */
    private String site;
}
