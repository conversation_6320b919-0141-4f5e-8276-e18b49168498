package com.estone.erp.publish.amazon.call.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * @Description: 文件分割模型
 * @Author: listen
 * @Date: 2018/9/27 16:45
 * @Version: 0.0.1
 */
@Data
public class FilePart {

    /**
     * 源文件名(name+suffix)
     */
    private String sourceFileName;

    /**
     * 源文件全路径
     */
    private String sourceFilePath;

    /**
     * 拆分文件父目录，默认项目根目录
     */
    private String fileParent = Thread.currentThread().getContextClassLoader().getResource(".").getPath();

    /**
     * 文件编号
     */
    private int partNumber;

    /**
     * 文件起始复制点
     */
    private Long startPos;

    /**
     * 文件下载长度
     */
    private Long partSize;

    public String getFileName() {
        if (StringUtils.isEmpty(sourceFileName)) {
            return null;
        }
        String fileName = sourceFileName;
        String suffix = "";
        if (sourceFileName.contains(".")) {
            fileName = sourceFileName.substring(0, sourceFileName.lastIndexOf("."));
            suffix = sourceFileName.substring(sourceFileName.lastIndexOf("."));
        }
        return fileName + "_" + partNumber + suffix;
    }

    public String getAbsolutePath() {
        if (StringUtils.isNotEmpty(sourceFileName) && StringUtils.isNotEmpty(fileParent)) {
            return fileParent + File.separator + getFileName();
        }
        return null;
    }
}
