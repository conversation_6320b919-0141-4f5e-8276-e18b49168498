package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.mq.model.ProductTypeKeyResultMessage;
import com.estone.erp.publish.amazon.service.AmazonCategorySpProductTypeService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * AMAZON回调属性KEY队列
 * AMAZON_PROPERTIES_KEY_RESULT_QUEUE
 *
 * <AUTHOR>
 * @date 2025-03-17 10:50
 */
@Slf4j
public class AmazonPropertiesKeyResultMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonCategorySpProductTypeService amazonCategorySpProductTypeService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            String messageData = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isEmpty(messageData)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            ProductTypeKeyResultMessage productTypeKeyResultMessage = JSON.parseObject(messageData, ProductTypeKeyResultMessage.class);
            if (productTypeKeyResultMessage == null) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            amazonCategorySpProductTypeService.updateProductTypeKeys(productTypeKeyResultMessage);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("AmazonPropertiesKeyResultMqListener error", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
