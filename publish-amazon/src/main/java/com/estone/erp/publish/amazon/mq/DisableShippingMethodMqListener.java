package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.enums.AmazonLogFieldEnum;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonCalcPriceRule;
import com.estone.erp.publish.amazon.model.AmazonOperateLog;
import com.estone.erp.publish.amazon.mq.model.ShippingMethodData;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonCalcPriceRuleService;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.common.WebSocketRequestDTO;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.system.erpCommon.ErpCommonWebSocketUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 监听物流推送过来的禁用运输方式
 * <AUTHOR>
 * @date 2021/12/14 14:13
 */
@Slf4j
@Component
public class DisableShippingMethodMqListener {

    @Resource
    private AmazonCalcPriceRuleService amazonCalcPriceRuleService;

    @Resource
    private AmazonOperateLogService amazonOperateLogService;

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private SystemParamService systemParamService;

    @RabbitListener(queues = PublishQueues.TMS_PUBLISH_SHIPPINGMETHOD_QUEQUES, containerFactory = "commonFactory")
    public void clearDisableShippingMethod(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }

        //log.info("amazon TMS_PUBLISH_SHIPPINGMETHOD_QUEQUES message body jsonString -> {}", body);
        Boolean isSuccess = clearDisableShippingMethod(body);
        if(isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean clearDisableShippingMethod(String body) {
        List<ShippingMethodData> dataList;
        try {
            JSONObject bodyJson = JSON.parseObject(body);
            String messageBody = bodyJson.getString("body");
            dataList = JSON.parseObject(messageBody, new TypeReference<List<ShippingMethodData>>() {
            });
            if (CollectionUtils.isEmpty(dataList)) {
                return false;
            }
        } catch (Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
            return false;
        }

        try {
            // 获取禁用物流方式code
            List<String> codeList = dataList.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getCode()))
                    .map(ShippingMethodData::getCode).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(codeList)) {
                return false;
            }

            // 查询存在禁用物流方式的数据
            List<AmazonCalcPriceRule> amazonCalcPriceRules = amazonCalcPriceRuleService.findExistDisableShippingMethod(codeList);

            if (CollectionUtils.isEmpty(amazonCalcPriceRules)) {
                return true;
            }
            // 推送websocket
            Set<String> existCodeDisableList = new HashSet<>();
            for (AmazonCalcPriceRule amazonCalcPriceRule : amazonCalcPriceRules) {
                try {
                    // 如果试算物流方式存在禁用物流
                    String calcLogistics = amazonCalcPriceRule.getCalcLogistics();
                    if (StringUtils.isNotBlank(calcLogistics) && codeList.contains(calcLogistics)) {
                        amazonCalcPriceRule.setCalcLogistics(null);
                        // 记录日志
                        addOperateLog(AmazonLogFieldEnum.CALC_LOGISTICS.getFieldEn(),
                                calcLogistics, amazonCalcPriceRule.getAccountNumber(), amazonCalcPriceRule.getId());
                        existCodeDisableList.add(calcLogistics);
                    }

                    // 如果优选物流方式存在禁用物流
                    String preferLogistics = amazonCalcPriceRule.getPreferLogistics();
                    if (StringUtils.isNotBlank(preferLogistics) && codeList.contains(preferLogistics)) {
                        amazonCalcPriceRule.setPreferLogistics(null);

                        // 记录日志
                        addOperateLog(AmazonLogFieldEnum.PREFER_LOGISTICS.getFieldEn(),
                                preferLogistics, amazonCalcPriceRule.getAccountNumber(), amazonCalcPriceRule.getId());
                        existCodeDisableList.add(preferLogistics);
                    }

                    // 如果备选物流方式存在禁用物流
                    String alternateLogistics = amazonCalcPriceRule.getAlternateLogistics();
                    if (StringUtils.isNotBlank(alternateLogistics) && codeList.contains(alternateLogistics)) {
                        amazonCalcPriceRule.setAlternateLogistics(null);

                        // 记录日志
                        addOperateLog(AmazonLogFieldEnum.ALTERNATE_LOGISTICS.getFieldEn(),
                                alternateLogistics, amazonCalcPriceRule.getAccountNumber(), amazonCalcPriceRule.getId());
                        existCodeDisableList.add(alternateLogistics);
                    }

                    amazonCalcPriceRuleService.clearExistDisableShippingMethod(amazonCalcPriceRule);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            if (CollectionUtils.isNotEmpty(existCodeDisableList)) {
                pushWebMessage(existCodeDisableList);
            }
        } catch (Exception e) {
            return false;
        }

        return true;
    }



    private void pushWebMessage(Set<String> existCodeDisableList) {
        SystemParam systemParam = systemParamService.querySystemParamByCodeKey("amazon_param.disable_shipping_method_employeeId");
        if (null == systemParam || StringUtils.isBlank(systemParam.getParamValue())) {
            log.error("推送 亚马逊算价配置的物流被禁用 配置获取系统参数人员为空，key： disable_shipping_method_employeeId");
            return;
        }
        String employeeIds = systemParam.getParamValue();
        List<String> employeeIdList = CommonUtils.splitList(employeeIds, ",");
        Date expiredDate= DateUtils.addDays(new Date(),15);
        String codeStr = String.join(",", existCodeDisableList);
        for (String employeeId : employeeIdList) {
            try {
                WebSocketRequestDTO webMessage = new WebSocketRequestDTO();
                webMessage.setType("read_亚马逊算价配置的物流被禁用");
                webMessage.setUserId(Math.toIntExact(Long.parseLong(employeeId)));
                webMessage.setContent("亚马逊算价配置以下物流被禁用：" + codeStr);
                webMessage.setExpiredDate(expiredDate);
                ErpCommonWebSocketUtils.sendMsg(webMessage);
                log.warn("推送 亚马逊算价配置的物流被禁用 消息成功，员工ID：{}, 过期时间：{}", employeeId, expiredDate);
            } catch (Exception e) {
                XxlJobLogger.log("推送亚马逊算价配置的物流被禁用消息失败，员工ID：{}, 过期时间：{}, 原因：{}", employeeId, expiredDate, e.getMessage());
            }
        }

    }

    private void addOperateLog(String logisticsType, String oldValue, String account, Integer id) {
        AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
        amazonOperateLog.setBefore(oldValue);
        amazonOperateLog.setAfter("该物流方式被禁用，系统默认将其置空");
        amazonOperateLog.setCreateBy("admin");
        amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

        // 如果账号不为空 则为私有算价规则配置
        if (StringUtils.isNotBlank(account)) {
            AmazonAccountRelation relation = amazonAccountRelationService.selectByAccount(account);
            Integer accountId = relation.getId();
            amazonOperateLog.setBusinessId(accountId);
            amazonOperateLog.setType(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name());
            amazonOperateLog.setFieldName(String.format("序号%s算价规则-%s", id, logisticsType));
            amazonOperateLog.setAccountNumber(account);
        } else {
            amazonOperateLog.setBusinessId(id);
            amazonOperateLog.setType(AmazonOperateLogEnum.UPDATE_CALC_PRICE_RULE.name());
            amazonOperateLog.setFieldName(logisticsType);
        }

        amazonOperateLogService.insert(amazonOperateLog);
    }
}
