package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonShippingCostModelService;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.tidb.publishAmazon.service.TidbAmazonProductListingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步在线列表的运费模板和运费到店铺配置
 * <AUTHOR>
 * @date 2021/12/18 16:05
 */
@Component
@Slf4j
public class AmazonSyncShippingCostModel2AccountJobHandler extends AbstractJobHandler {

    @Getter
    @Setter
    static class InnerParam {
        /**
         * 是否同步全量
         */
        private Boolean isSyncAll;

        /**
         * 账号list
         */
        private List<String> accountList;
    }

    public AmazonSyncShippingCostModel2AccountJobHandler() {
        super("AmazonSyncShippingCostModel2AccountJobHandler");
    }

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private TidbAmazonProductListingService tidbAmazonProductListingService;

    @Resource
    private AmazonShippingCostModelService amazonShippingCostModelService;

    @Override
    @XxlJob("AmazonSyncShippingCostModel2AccountJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("同步开始");

        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数报错：" + e.getMessage());
                return ReturnT.SUCCESS;
            }
        }

        // 传参 全量同步,不传参 同步正常账号
        List<AmazonAccountRelation> accountList = new ArrayList<>();
        AmazonAccountRelationExample relationExample = new AmazonAccountRelationExample();
        String columns = "account_number,account_country";
        relationExample.setFiledColumns(columns);
        if (null != innerParam && null != innerParam.getIsSyncAll() && innerParam.getIsSyncAll()) {
            List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(relationExample);
            accountList.addAll(relations);
        } else {
            relationExample.createCriteria().andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());
            List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(relationExample);
            accountList.addAll(relations);
        }

        // 传参同步部分店铺
        if (null != innerParam && CollectionUtils.isNotEmpty(innerParam.getAccountList())) {
            accountList.clear();
            relationExample.createCriteria().andAccountNumberIn(innerParam.getAccountList());
            List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(relationExample);
            accountList.addAll(relations);
        }


        for (AmazonAccountRelation account : accountList) {
            if (StringUtils.isBlank(account.getAccountCountry())){
                XxlJobLogger.log("账号数据站点为空：" + account.getAccountNumber());
                continue;
            }
            // 查询在线列表
            AmazonProductListingExample example = new AmazonProductListingExample();
            AmazonProductListingExample.Criteria createCriteria = example.createCriteria();
            createCriteria.andIsOnlineEqualTo(true);
            createCriteria.andMerchantShippingGroupIsNotNull();
            createCriteria.andAccountNumberEqualTo(account.getAccountNumber());
            createCriteria.andSiteEqualTo(account.getAccountCountry());
            List<String> merchantShippingGroupList = new ArrayList<>();
            try {
                merchantShippingGroupList = tidbAmazonProductListingService.selectMerchantShippingGroupByExample(example,account.getAccountCountry());
            } catch (Exception e) {
                XxlJobLogger.log(String.format("查询时报错:%s", e.getMessage()));
                log.error(String.format("查询时报错:%s", e.getMessage()));
            }

            if (CollectionUtils.isEmpty(merchantShippingGroupList)) {
                XxlJobLogger.log(String.format("账号%s查询无数据", account.getAccountNumber()));
                continue;
            }

            // 需要保存的运费模板的集合
            List<AmazonShippingCostModel> amazonShippingCostModels = new ArrayList<>();

            // 根据运费模板分组
            try {
                for (String merchantShippingGroup : merchantShippingGroupList) {
                    // 创建对象
                    AmazonShippingCostModel amazonShippingCostModel = new AmazonShippingCostModel();
                    amazonShippingCostModel.setAccountNumber(account.getAccountNumber());
                    amazonShippingCostModel.setIsDefault(false);
//                    amazonShippingCostModel.setShippingCost(shippingCost);
                    amazonShippingCostModel.setShippingGroup(merchantShippingGroup);

                    amazonShippingCostModels.add(amazonShippingCostModel);
                }

                // 插入运费模板表
                amazonShippingCostModelService.batchSave(amazonShippingCostModels);
                XxlJobLogger.log(String.format("账号%s已同步", account.getAccountNumber()));

            } catch (Exception e) {
                XxlJobLogger.log(String.format("同步店铺%s时报错:%s", account, e.getMessage()));
                log.error(String.format("同步店铺%s时报错:%s", account, e.getMessage()));
            }
        }

        XxlJobLogger.log("同步结束");
        return ReturnT.SUCCESS;
    }
}
