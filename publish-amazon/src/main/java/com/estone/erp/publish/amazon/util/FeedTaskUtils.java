package com.estone.erp.publish.amazon.util;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 创建记录
 */
@Slf4j
public class FeedTaskUtils {

    private static FeedTaskService feedTaskService = SpringUtils.getBean(FeedTaskService.class);


    /**
     * 处理亚马逊创建任务
     *
     * @param accountNumber
     * @param taskType
     * @return
     */
    public static Long handleCreateAmazonFeedtask(String accountNumber, String taskType, TaskStatusEnum taskStatusEnum,String createdBy,String marketplaceId) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAccountNumber(accountNumber);
        feedTask.setTaskType(taskType);
        feedTask.setTaskStatus(taskStatusEnum.getStatusCode());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setPlatform(Platform.Amazon.name());
        feedTask.setTableIndex();
        if (StringUtils.isBlank(createdBy)){
            createdBy = "admin";
        }
        feedTask.setAssociationId(marketplaceId);
        feedTask.setCreatedBy(createdBy);
        feedTaskService.insertSelective(feedTask);
        return feedTask.getId();
    }

    /**
     * 亚马逊任务更新
     *
     * @param feedId
     * @return
     */
    public static int handleUpdateAmazonFeedtaskFail(Long feedId, String msg) {
        return handleUpdateAmazonFeedtask(feedId,msg,ResultStatusEnum.RESULT_FAIL.getStatusCode());
    }

    /**
     * 亚马逊任务更新
     * @param feedId
     * @return
     */
    public static int handleUpdateAmazonFeedtask(Long feedId, String msg,Integer resultStatus){
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedId);
        feedTask.setPlatform(Platform.Amazon.name());
        feedTask.setTableIndex();
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        feedTask.setResultStatus(resultStatus);
        if(StringUtils.isNotEmpty(msg)){
            feedTask.setResultMsg(msg);
        }
        return feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * amazon查询同步报告成功的完成时间
     * @param accountNumberList
     * @return
     */
    public static Map<String, Date> selectAmazonAccountSyncDate(List<String> accountNumberList){
        Map<String,Date> accountSyncDateMap = new HashMap<>();
        try {
            FeedTaskExample example = new FeedTaskExample();
            example.setCustomColumn("account_number, finish_time");
            example.createCriteria().andAccountNumberIn(accountNumberList)
                    .andTaskTypeEqualTo(TaskTypeEnum.NEW_SYNC_PRODUCT.getStatusMsgEn())
                    .andResultStatusEqualTo(ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
            example.setOrderByClause("finish_time desc");
            List<FeedTask> feedTaskList = feedTaskService.selectByExample(example,Platform.Amazon.name());
            if (CollectionUtils.isNotEmpty(feedTaskList)) {
                accountSyncDateMap = feedTaskList.stream()
                        .collect(Collectors.toMap(FeedTask::getAccountNumber, FeedTask::getFinishTime, (o1, o2) -> o1));
            }
        }catch (Exception e){
            log.error("查询同步报告最新完成时间出错：" ,e.getMessage());
        }
        return accountSyncDateMap;
    }
}
