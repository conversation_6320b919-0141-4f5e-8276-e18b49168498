package com.estone.erp.publish.amazon.call.process.product;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.BaseSpReportTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.GeneralListingSpReportTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.JpSpListingSpReportTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.base.pms.model.AmazonAccount;

/**
 * 
 * @Description: BaseReportTypeWithCsvStrategy工厂类
 * 
 * @ClassName: BaseReportTypeWithCsvStrategyFactory
 * @Author: Kevin
 * @Date: 2018/10/19
 * @Version: 0.0.1
 */
public class BaseReportTypeWithCsvStrategyFactory {

    public static BaseSpReportTypeWithCsvStrategy newInstance(AmazonAccount account, String reportType) {
        if (account == null) {
            return null;
        }

        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        String marketplace = amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId().trim()).getMarketplace();
        if (GeneralListingSpReportTypeWithCsvStrategy.MARKETPLACES.contains(marketplace)
            && GeneralListingSpReportTypeWithCsvStrategy.REPORT_TYPE.equals(reportType)) {
            return new GeneralListingSpReportTypeWithCsvStrategy(account, reportType);
        } else if (JpSpListingSpReportTypeWithCsvStrategy.MARKETPLACES.contains(marketplace)
            && JpSpListingSpReportTypeWithCsvStrategy.REPORT_TYPE.equals(reportType)) {
            return new JpSpListingSpReportTypeWithCsvStrategy(account, reportType);
        }

        return null;
    }
}
