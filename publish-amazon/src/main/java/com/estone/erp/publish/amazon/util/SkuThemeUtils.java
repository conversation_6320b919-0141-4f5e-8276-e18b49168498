package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.util.model.Color;
import com.estone.erp.publish.amazon.util.model.Size;
import com.estone.erp.publish.amazon.util.model.SkuTheme;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.system.product.ProductInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * 解析sku货号属性的工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SkuThemeUtils {

    private static Map<String, Map<String, String>> themeTypeCode2Names = new HashMap<String, Map<String, String>>(2);

    static {
        Color[] colors = Color.values();
        Map<String, String> colorCode2Names = new HashMap<String, String>(colors.length);
        for (Color color : colors) {
            colorCode2Names.put(color.getCode(), color.getName());
        }
        themeTypeCode2Names.put("Color", colorCode2Names);

        Size[] sizes = Size.values();
        Map<String, String> sizeCode2Names = new HashMap<String, String>(sizes.length);
        for (Size size : sizes) {
            sizeCode2Names.put(size.getCode(), size.getName());
        }
        themeTypeCode2Names.put("Size", sizeCode2Names);
    }

    /**
     * 解析产品货号包含的所有单品，获取产品货号的变体属性
     *
     * @param skus
     * @return
     */
    public static SkuTheme parse(List<StockKeepingUnitWithBLOBs> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return null;
        }
        SkuTheme result = null;
        List<String> sizes = Size.getCodes();
        // 添加判断规则：货号-color-size
        result = getDoubleSplitSkuTheme(skus);
        if (result != null) {
            return result;
        }
        int countColor = 0, countSize = 0;
        // 先统计一遍color和size，确定包含的变体属性
        // 将货号按-切割，color和size的属性顺序——先color后size
        for (StockKeepingUnitWithBLOBs item : skus) {
            String itemArticleNumber = item.getArticleNumber();
            // 若货号和产品货号相同，则为单品，跳过，直接增加计数
            if (itemArticleNumber.equals(item.getProductarticlenumber())) {
                countColor++;
                countSize++;
                continue;
            }
            String[] splits = StringUtils.split(itemArticleNumber, "-");
            if (isSkuHasProductArticleNumber(item) && splits.length > 1 && StringUtils.isNotBlank(splits[1])) {
                // 获取产品的变体属性
                String themeValue = splits[1];
                String sizeTheme = getSingleTheme(sizes, themeValue, false);
                if (StringUtils.isEmpty(sizeTheme)) {
                    countColor++;
                } else {
                    countSize++;
                    String colorTheme = themeValue.substring(0, themeValue.length() - sizeTheme.length());
                    if (StringUtils.isNotEmpty(colorTheme)) {
                        countColor++;
                    }
                }
            }
            // 不可正常切割的货号，跳过
            else {
               // log.warn("error articleNumber {}---error productArticleNumber {}", itemArticleNumber,item.getProductarticlenumber());
                continue;
            }
        }

        boolean containsColor = false;
        boolean containsSize = false;
        // 对统计结果分析
        if (countSize > 0 && countSize == skus.size()) {
            containsSize = true;
        }
        if (countColor > 0 && countColor == skus.size()) {
            containsColor = true;
        }
        // 若解析不出，则默认只含color
        if (!containsColor && !containsSize) {
            containsColor = true;
        }

        for (StockKeepingUnitWithBLOBs item : skus) {
            String itemArticleNumber = item.getArticleNumber();
            String[] splits = StringUtils.split(itemArticleNumber, "-");
            if (isSkuHasProductArticleNumber(item) && splits.length > 1 && StringUtils.isNotBlank(splits[1])) {
                if (result == null) {
                    result = new SkuTheme();
                    result.setProductArticleNumber(item.getProductarticlenumber());
                }

                // 获取产品的变体属性
                String themeValue = splits[1];
                List<NameValue> nameValues = new ArrayList<NameValue>(2);
                result.getVariants().put(themeValue, nameValues);
                if (containsSize && !containsColor) {
                    nameValues.add(new NameValue("Size", themeValue, themeValue));
                } else if (containsColor && !containsSize) {
                    nameValues.add(new NameValue("Color", themeValue, themeValue));
                } else if (containsColor && containsSize) {
                    String sizeTheme = getSingleTheme(sizes, themeValue, false);
                    String colorTheme = themeValue.substring(0, themeValue.length() - sizeTheme.length());
                    nameValues.add(new NameValue("Size", sizeTheme, sizeTheme));
                    nameValues.add(new NameValue("Color", colorTheme, colorTheme));
                }
            }
        }

        if (containsColor && result != null) {
            result.setVariationThemes(containsSize ? "Color-Size" : "Color");
        } else if (containsSize && result != null) {
            result.setVariationThemes("Size");
        }

        return result;
    }

    /**
     * 处理特殊的产品新名称规则——货号-color-size
     *
     * @param skus
     * @return
     * @Author: listen
     * @Date 2019/5/13 10:44
     * @Version: 0.0.1
     */
    private static SkuTheme getDoubleSplitSkuTheme(List<StockKeepingUnitWithBLOBs> skus) {
        SkuTheme result = null;
        int count = 0;
        for (StockKeepingUnitWithBLOBs item : skus) {
            String itemArticleNumber = item.getArticleNumber();
            String[] splits = StringUtils.split(itemArticleNumber, "-");
            if (isSkuHasProductArticleNumber(item) && splits.length > 2 && StringUtils.isNotBlank(splits[1])
                    && StringUtils.isNotBlank(splits[2])) {
                count++;
            }
        }
        if (count == skus.size()) {
            result = new SkuTheme();
            result.setVariationThemes("Color-Size");
            for (StockKeepingUnitWithBLOBs item : skus) {
                if (StringUtils.isBlank(result.getProductArticleNumber())) {
                    result.setProductArticleNumber(item.getProductarticlenumber());
                }
                String[] splits = StringUtils.split(item.getArticleNumber(), "-");
                String color = splits[1];
                String size = splits[2];
                List<NameValue> nameValues = new ArrayList<>(2);
                // map 的key名称存疑
                result.getVariants().put(color + "-" + size, nameValues);
                nameValues.add(new NameValue("Size", size, size));
                nameValues.add(new NameValue("Color", color, color));
            }
        }
        return result;
    }

    /**
     * 修改选取方法，选择长度最长的匹配项，可选头匹配还是尾匹配
     *
     * @param values
     * @param themeValue
     * @param startWith
     * @return java.lang.String
     * @Author: listen
     * @Date 2018/8/31 16:41
     * @Version: 0.0.1
     */
    private static String getSingleTheme(List<String> values, String themeValue, boolean startWith) {
        int maxLength = 0;
        String maxLengthValue = null;
        for (String value : values) {
            if (startWith) {
                if (themeValue.startsWith(value)) {
                    if (value.length() > maxLength) {
                        maxLength = value.length();
                        maxLengthValue = value;
                    }
                }
            } else {
                if (themeValue.endsWith(value)) {
                    if (value.length() > maxLength) {
                        maxLength = value.length();
                        maxLengthValue = value;
                    }
                }
            }
        }

        return maxLengthValue;
    }

    public static String getThemeValue(String type, String code) {
        if (themeTypeCode2Names.containsKey(type)) {
            Map<String, String> code2Names = themeTypeCode2Names.get(type);
            if (code2Names.containsKey(code)) {
                String result = code2Names.get(code);
                return StringUtils.isNotEmpty(result) ? result : code;
            }
        }

        return code;
    }

    public static boolean isSkuHasProductArticleNumber(StockKeepingUnitWithBLOBs sku) {
        return sku != null && !sku.getArticleNumber().endsWith("-")
                && StringUtils.isNotEmpty(sku.getProductarticlenumber());
    }

    /**
     * 探雅产品货号包含的所有单品，获取产品货号的变体属性颜色、尺寸
     *
     * @param productInfoList
     * @return
     */
    public static SkuTheme parseTanYa(List<ProductInfo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        SkuTheme result = null;
        int countColor = 0, countSize = 0;
        // 先统计一遍color和size，确定包含的变体属性
        // 将货号按-切割，color和size的属性顺序——先color后size
        for (ProductInfo item : productInfoList) {
            // 若为单品，跳过，直接增加计数
            if (0 == item.getType()) {
                countColor++;
                countSize++;
                continue;
            }
            String color = item.getColor();
            String size = item.getSize();
            if (StringUtils.isNotEmpty(color) || StringUtils.isNotEmpty(size)) {
                // 获取产品的变体属性
                if (StringUtils.isNotEmpty(size)) {
                    countSize++;
                }
                if (StringUtils.isNotEmpty(color)) {
                    countColor++;
                }
            } else {
                log.warn("error articleNumber {}---error productArticleNumber {}", item.getMainSku(),
                        item.getSonSku());
                continue;
            }
        }

        boolean containsColor = false;
        boolean containsSize = false;
        // 对统计结果分析
        if (countSize > 0) {
            containsSize = true;
        }
        if (countColor > 0) {
            containsColor = true;
        }
        for (ProductInfo item : productInfoList) {
            String mainSku = item.getMainSku();
            if (null != item && StringUtils.isNotEmpty(mainSku)) {
                if (result == null) {
                    result = new SkuTheme();
                    result.setProductArticleNumber(mainSku);
                }

                // 获取产品的变体属性
                String sonSku = item.getSonSku();
                String color = item.getColor();
                String size = item.getSize();
                List<NameValue> nameValues = new ArrayList<NameValue>(2);
                result.getVariants().put(sonSku, nameValues);
                if (containsSize) {
                    nameValues.add(new NameValue("Size", size, size));
                }
                if (containsColor) {
                    nameValues.add(new NameValue("Color", color, color));
                }
            }
        }

        if (containsColor && result != null) {
            result.setVariationThemes(containsSize ? "Color-Size" : "Color");
        } else if (containsSize && result != null) {
            result.setVariationThemes("Size");
        }

        return result;
    }

    /**
     * 解析产品属性获取 Color-Size
     */
    public static SkuTheme parseProductAttrs(List<ProductInfo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        SkuTheme skuTheme = new SkuTheme();
        String articleNumber = null;
        String theme = null;
        for (ProductInfo product : productInfoList) {
            if (null == articleNumber && StringUtils.isNotEmpty(product.getMainSku())) {
                articleNumber = product.getMainSku();
            }
            // 获取产品的变体属性
            String sonSku = product.getSonSku();

            String saleAttrs = product.getSaleAtts();
            // 解析产品属性,过滤掉空串
            Map<String, String> attrMap = resolveAttrMap(saleAttrs);

            // 从产品属性中取指定的属性 color size
            String color = product.getColor() != null ? product.getColor() : attrMap.get("Color");
            String size = product.getSize() != null ? product.getSize() : attrMap.get("Size");

            // 属性中不包含指定的属性,默认取对应的属性中的第一第二项  color:0 size:1
            if (StringUtils.isBlank(color) && StringUtils.isBlank(size) && StringUtils.isNotBlank(saleAttrs)) {
                List<String> valueList = new ArrayList<>(attrMap.values());
                if (valueList.size() > 0) {
                    color = valueList.get(0);
                }
                if (valueList.size() > 1) {
                    size = valueList.get(1);
                }
            }

            // 如果属性层级2层以上 color或size缺少一个属性则补充其它属性项
            if (attrMap.size() >= 2 && (StringUtils.isBlank(color) || StringUtils.isBlank(size))) {
                // color 不为空
                if (StringUtils.isNotBlank(color)) {
                    // color-size
                    for (Map.Entry<String, String> entry : attrMap.entrySet()) {
                        String value = entry.getValue();
                        if (!color.equalsIgnoreCase(value) && StringUtils.isBlank(size)) {
                            size = value;
                            theme = "Color-Size";
                        }
                    }
                }
                // size 不为空
                if (StringUtils.isNotBlank(size)) {
                    // size-color
                    for (Map.Entry<String, String> entry : attrMap.entrySet()) {
                        String value = entry.getValue();
                        if (!size.equalsIgnoreCase(value) && StringUtils.isBlank(color)) {
                            color = value;
                            theme = "Size-Color";
                        }
                    }
                }
            }

            List<NameValue> nameValues = new ArrayList<>();
            skuTheme.getVariants().put(sonSku, nameValues);

            if (StringUtils.isNotBlank(color) && StringUtils.isNotBlank(size)) {
                nameValues.add(new NameValue("Color", color, color));
                nameValues.add(new NameValue("Size", size, size));
                theme = theme != null ? theme: "Color-Size";
                continue;
            }

            if (StringUtils.isNotBlank(color) && StringUtils.isBlank(size)) {
                nameValues.add(new NameValue("Color", color, color));
                theme = "Color";
                continue;
            }

            if (StringUtils.isNotBlank(size) && StringUtils.isBlank(color)) {
                nameValues.add(new NameValue("Size", size, size));
                theme = "Size";
            }
        }
        skuTheme.setVariationThemes(theme);
        skuTheme.setProductArticleNumber(articleNumber);
        return skuTheme;
    }

    private static Map<String,String> resolveAttrMap(String attributes) {
        Map<String,String> attrMap = new HashMap<>();
        if (StringUtils.isBlank(attributes)) {
            return attrMap;
        }
        JSONArray attributeArray = JSON.parseArray(attributes);
        for (int i = 0; i < attributeArray.size(); i++) {
            // 属性中包含指定的属性
            JSONObject attributeJson = attributeArray.getJSONObject(i);
            String enValue = attributeJson.getString("enValue");
            if (StringUtils.isNotBlank(enValue)) {
                attrMap.put(attributeJson.getString("enName"),enValue);
            }
        }
        return attrMap;
    }



//    public static void main(String[] args) {
//
//        List<ProductInfo> productInfoList = new ArrayList<>();
//        ProductInfo productInfo = new ProductInfo();
//        productInfo.setMainSku("111");
//        productInfo.setSonSku("111-1");
//        productInfo.setSaleAtts("[{\"enName\":\"Size\",\"enValue\":\"xxl\"},{\"enName\":\"材质\",\"enValue\":\"不锈钢\"}]");
//
//        ProductInfo productInfo1 = new ProductInfo();
//        productInfo1.setMainSku("111");
//        productInfo1.setSonSku("111-2");
//        productInfo1.setSaleAtts("[{\"enName\":\"Size\",\"enValue\":\"M\"},{\"enName\":\"材质\",\"enValue\":\"铁\"}]");
//
//        productInfoList.add(productInfo);
//        productInfoList.add(productInfo1);
//
//        SkuTheme skuTheme = parseProductAttrs(productInfoList);
//        System.out.println("skuTheme," + JSON.toJSONString(skuTheme));
//    }
}
