package com.estone.erp.publish.amazon.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.erp.common.util.StrUtil;
import lombok.Data;

@Data
public class AmazonVariant implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_variant.id
     */
    private Integer id;

    /**
     * 账号 database column amazon_variant.account_number
     */
    private String accountNumber;

    /**
     * 名称 database column amazon_variant.item_name
     */
    private String itemName;

    /**
     * 描述 database column amazon_variant.item_description
     */
    private String itemDescription;

    /**
     * 五点描述(数组)
     */
    private String bulletPoint;

    /**
     * 关键词
     */
    private String searchTerms;

    /**
     * listing id database column amazon_variant.listing_id
     */
    private String listingId;

    /**
     * asin database column amazon_variant.asin
     */
    private String asin;

    /**
     * 卖家sku database column amazon_variant.seller_sku
     */
    private String sellerSku;

    /**
     * 价格 database column amazon_variant.price
     */
    private Double price;

    /**
     * 数量 database column amazon_variant.quantity
     */
    private Integer quantity;

    /**
     * 备货期
     */
    private Integer fulfillmentLatency;

    /**
     * 是否为marketplace database column amazon_variant.item_is_marketplace
     */
    private Boolean itemIsMarketplace;

    /**
     * 条件 database column amazon_variant.item_condition
     */
    private String itemCondition;

    /**
     * 类别 database column amazon_variant.zshop_category
     */
    private String zshopCategory;

    /**
     * merchant shipping group database column amazon_variant.merchant_shipping_group
     */
    private String merchantShippingGroup;

    /**
     * 是否在售 database column amazon_variant.is_online
     */
    private Boolean isOnline;

    /**
     * 产品id database column amazon_variant.product_id
     */
    private String productId;

    /**
     * 主图 database column amazon_variant.main_image
     */
    private String mainImage;

    /**
     * 特效图 database column amazon_variant.extra_images
     */
    private String extraImages;

    /**
     * 创建时间 database column amazon_variant.create_date
     */
    private Timestamp createDate;

    /**
     * 下线时间 database column amazon_variant.offline_date
     */
    private Timestamp offlineDate;

    /**
     * amazon_product表id database column amazon_variant.amazon_product_id
     */
    private Integer amazonProductId;

    /**
     * 货号 database column amazon_variant.article_number
     */
    private String articleNumber;

    /**
     * 促销价 database column amazon_variant.sale_price
     */
    private Double salePrice;

    /**
     * 促销起始日期 database column amazon_variant.sale_start_date
     */
    private Timestamp saleStartDate;

    /**
     * 促销结束日期 database column amazon_variant.sale_end_date
     */
    private Timestamp saleEndDate;

    /**
     * 样品图 database column amazon_variant.sample_image
     */
    private String sampleImage;

    /**
     * Amazon跟卖删除 database column amazon_variant.is_follow_sell_delete
     */
    private Boolean isFollowSellDelete;

    /**
     * 起售时间 database column amazon_variant.open_date
     */
    private Timestamp openDate;

    /**
     * 是否爆款 database column amazon_variant.is_popular
     */
    private Boolean isPopular;

    /**
     * 标题包含的侵权词 database column amazon_variant.infringement_word
     */
    private String infringementWord;

    /**
     * 是否多站点刊登
     */
    private Boolean isSitePublish;

    /**
     * 促销结束日期 database column amazon_variant.sale_end_date
     */
    private Timestamp autoUpdateMsgDate;

    /**
     * 关联的模板id
     */
    private Integer relationTemplateId;

    /**
     * 国家(站点)，历史数据为空
     */
    private String country;

    /**
     * 是否是试卖sku (1:是 0：否)
     */
    private Boolean spFlag;

    public String getArticleNumber() {
        //货号去除空格转大写
        return StrUtil.strTrimToUpperCase(articleNumber);
    }
}