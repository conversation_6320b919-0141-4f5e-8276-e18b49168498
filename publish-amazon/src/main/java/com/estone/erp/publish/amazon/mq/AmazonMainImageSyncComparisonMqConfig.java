package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 新版同步产品MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class AmazonMainImageSyncComparisonMqConfig {

    private int amazonMainImageSyncComparisonMqConfigMqConsumers;
    private int amazonMainImageSyncComparisonMqPrefetchCount;
    private boolean amazonMainImageSyncComparisonMqListener;

    @Bean
    public Queue amazonMainImageSyncComparisonQueue() {
        // 队列持久化
        Map<String, Object> argsMap = new HashMap<>();
        argsMap.put("x-consumer-timeout", 1000 * 60 * 60 * 24);
        return new Queue(PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE, true, false, false, argsMap);
    }

    @Bean
    public Binding amazonMainImageSyncComparisonBinding() {
        return new Binding(PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE, DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_KEY, null);
    }

    @Bean
    public AmazonMainImageSyncComparisonMqListener amazonMainImageSyncComparisonMqListener() {
        return new AmazonMainImageSyncComparisonMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonMainImageSyncComparisonMqListenerContainer(
            AmazonMainImageSyncComparisonMqListener amazonMainImageSyncComparisonMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE, amazonMainImageSyncComparisonMqListener);
        return container;
    }

    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonMainImageSyncComparisonMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonMainImageSyncComparisonMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonMainImageSyncComparisonMqConfigMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}