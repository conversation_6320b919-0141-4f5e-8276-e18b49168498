package com.estone.erp.publish.amazon.enums;

public enum AmazonReqAttrAdapterTypeEnums {

    SITE_MATCH(1, "站点匹配"),
    CATEGORY_TYPE_MATCH(2, "分类类型匹配"),
    SITE_CATEGORY_TYPE_MATCH(3, "站点+分类类型匹配"),
    GENERAL_ATTR(4, "通用属性");
    private final int code;
    private final String desc;

    AmazonReqAttrAdapterTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        for (AmazonReqAttrAdapterTypeEnums e : AmazonReqAttrAdapterTypeEnums.values()) {
            if (e.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}
