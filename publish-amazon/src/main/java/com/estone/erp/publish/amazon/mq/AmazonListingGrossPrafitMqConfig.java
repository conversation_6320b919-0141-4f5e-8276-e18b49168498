package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Amazon 在线列表更新毛利毛利率MQ消费配置
 *
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class AmazonListingGrossPrafitMqConfig {
    private static final String [] FILEDS = {"id","accountNumber","site","sellerSku","articleNumber","price"};
    private static final String USERNAME ="admin";

    private int amazonListingGrossPrafitMqConsumers;
    private int amazonListingGrossPrafitMqPrefetchCount;
    private boolean amazonListingGrossPrafitMqListener;

    /**
     * 全量重算毛利配置
     */
    private int amazonAllListingGrossPrafitMqConsumers;
    private int amazonAllListingGrossPrafitMqPrefetchCount;
    private boolean amazonAllListingGrossPrafitMqListener;


    @Bean
    public AmazonListingGrossPrafitMqListener amazonListingGrossPrafitMqListener() {
        AmazonListingGrossPrafitMqListener listener = new AmazonListingGrossPrafitMqListener();
        listener.setFileds(FILEDS);
        listener.setUserName(USERNAME);
        return listener;
    }

    @Bean
    public SimpleMessageListenerContainer amazonListingGrossPrafitListenerContainer(
            AmazonListingGrossPrafitMqListener amazonListingGrossPrafitMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.PUBLISH_AMAZON_LISTING_GROSS_PRAFIT_QUEUE, amazonListingGrossPrafitMqListener);
        return container;
    }


    /**
     * Amazon 在线列表全量更新毛利毛利消费类
     */
    @Bean
    public AmazonAllListingGrossProfitMqListener amazonAllListingGrossProfitMqListener() {
        AmazonAllListingGrossProfitMqListener listener = new AmazonAllListingGrossProfitMqListener();
        listener.setFileds(FILEDS);
        listener.setUserName(USERNAME);
        return listener;
    }

    /**
     * 在线列表全量毛利重算MQ消费配置
     */
    @Bean
    public SimpleMessageListenerContainer amazonAllListingGrossProfitMqListenerContainer(
            AmazonAllListingGrossProfitMqListener amazonAllListingGrossProfitMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonAllListingGrossPrafitMqListener) {
            container.setQueueNames(PublishQueues.AMAZON_LISTING_UPDATE_GROSS_PROFIT_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonAllListingGrossPrafitMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonAllListingGrossPrafitMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(amazonAllListingGrossProfitMqListener);// 监听处理类
        }
        return container;
    }


    /**
     * 在线列表不存在毛利MQ消费配置
     */
    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonListingGrossPrafitMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonListingGrossPrafitMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonListingGrossPrafitMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}