package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_process_report
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:03:49 CST 2019
 */
@Data
public class AmazonProcessReport {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   上传数据类型
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.feed_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String feedType;

    /**
     * Database Column Remarks:
     *   卖家帐号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.account_number
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String accountNumber;

    /**
     * Database Column Remarks:
     *   事务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.doc_trans_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String docTransId;

    /**
     * Database Column Remarks:
     *   状态代码
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.status_code
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String statusCode;

    /**
     * Database Column Remarks:
     *   处理数据类型
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.data_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String dataType;

    /**
     * Database Column Remarks:
     *   处理数据值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.data_value
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String dataValue;

    /**
     * Database Column Remarks:
     *   是否上传成功
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.status
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private Boolean status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.creation_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private Date creationDate;

    /**
     * Database Column Remarks:
     *   完成时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.finish_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private Date finishDate;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.created_by
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String createdBy;

    /**
     * Database Column Remarks:
     *   相关id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.relation_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private Integer relationId;

    /**
     * Database Column Remarks:
     *   相关类型
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.relation_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String relationType;

    /**
     * Database Column Remarks:
     *   自定义任务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.task_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String taskId;

    /**
     * Database Column Remarks:
     *   结果信息
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_process_report.result_msg
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    private String resultMsg;


    /**
     * 改前值 备货期
     */
    private String previousFulfillmentLatencyValue;

    /**
     * 改后值 备货期
     */
    private String afterFulfillmentLatencyValue;

    /**
     * 改前值 库存
     */
    private String previousQuantityValue;

    /**
     * 改后值 库存
     */
    private String afterQuantityValue;


    /**
     * 改前值 价格
     */
    private String previousPriceValue;

    /**
     * 改后值 价格
     */
    private String afterPriceValue;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.id
     *
     * @return the value of amazon_process_report.id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.id
     *
     * @param id the value for amazon_process_report.id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.feed_type
     *
     * @return the value of amazon_process_report.feed_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getFeedType() {
        return feedType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.feed_type
     *
     * @param feedType the value for amazon_process_report.feed_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setFeedType(String feedType) {
        this.feedType = feedType == null ? null : feedType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.account_number
     *
     * @return the value of amazon_process_report.account_number
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.account_number
     *
     * @param accountNumber the value for amazon_process_report.account_number
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber == null ? null : accountNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.doc_trans_id
     *
     * @return the value of amazon_process_report.doc_trans_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getDocTransId() {
        return docTransId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.doc_trans_id
     *
     * @param docTransId the value for amazon_process_report.doc_trans_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setDocTransId(String docTransId) {
        this.docTransId = docTransId == null ? null : docTransId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.status_code
     *
     * @return the value of amazon_process_report.status_code
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getStatusCode() {
        return statusCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.status_code
     *
     * @param statusCode the value for amazon_process_report.status_code
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode == null ? null : statusCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.data_type
     *
     * @return the value of amazon_process_report.data_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.data_type
     *
     * @param dataType the value for amazon_process_report.data_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setDataType(String dataType) {
        this.dataType = dataType == null ? null : dataType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.data_value
     *
     * @return the value of amazon_process_report.data_value
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getDataValue() {
        return dataValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.data_value
     *
     * @param dataValue the value for amazon_process_report.data_value
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setDataValue(String dataValue) {
        this.dataValue = dataValue == null ? null : dataValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.status
     *
     * @return the value of amazon_process_report.status
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public Boolean getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.status
     *
     * @param status the value for amazon_process_report.status
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setStatus(Boolean status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.creation_date
     *
     * @return the value of amazon_process_report.creation_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.creation_date
     *
     * @param creationDate the value for amazon_process_report.creation_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.finish_date
     *
     * @return the value of amazon_process_report.finish_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public Date getFinishDate() {
        return finishDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.finish_date
     *
     * @param finishDate the value for amazon_process_report.finish_date
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.created_by
     *
     * @return the value of amazon_process_report.created_by
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.created_by
     *
     * @param createdBy the value for amazon_process_report.created_by
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.relation_id
     *
     * @return the value of amazon_process_report.relation_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public Integer getRelationId() {
        return relationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.relation_id
     *
     * @param relationId the value for amazon_process_report.relation_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setRelationId(Integer relationId) {
        this.relationId = relationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.relation_type
     *
     * @return the value of amazon_process_report.relation_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getRelationType() {
        return relationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.relation_type
     *
     * @param relationType the value for amazon_process_report.relation_type
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setRelationType(String relationType) {
        this.relationType = relationType == null ? null : relationType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.task_id
     *
     * @return the value of amazon_process_report.task_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.task_id
     *
     * @param taskId the value for amazon_process_report.task_id
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_process_report.result_msg
     *
     * @return the value of amazon_process_report.result_msg
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public String getResultMsg() {
        return resultMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_process_report.result_msg
     *
     * @param resultMsg the value for amazon_process_report.result_msg
     *
     * @mbg.generated Thu Jul 18 16:03:49 CST 2019
     */
    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg == null ? null : resultMsg.trim();
    }
}