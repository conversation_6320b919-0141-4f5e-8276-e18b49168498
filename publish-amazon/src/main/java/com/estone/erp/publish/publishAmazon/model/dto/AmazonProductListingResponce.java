package com.estone.erp.publish.publishAmazon.model.dto;

import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.publishAmazon.vo.AccountInfoVO;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2021/1/28 14:21
 * @Version: 1.0.0
 */
@Data
public class AmazonProductListingResponce {

    /**
     * es List
     */
    private Page<EsAmazonProductListing> esProductListingPage ;

    /**
     * 账号对应销售、账号状态、异常状态
     */
    private Map<String, AccountInfoVO> accountSaleManMap;

}
