package com.estone.erp.publish.amazon.enums;

/**
 * @Description amazon跟卖数据状态值
 *  数据状态（0：待刊登，1：刊登中，2：刊登成功，3：刊登失败，4：下架中，5：下架成功，6：下架失败，7：删除成功，8：删除失败）
 *  待刊登(下架成功) -> 刊登中 -> 刊登成功
 *  待刊登 -> 刊登中 -> 刊登失败
 *  刊登成功 -> 下架中 -> 下架成功
 *  刊登成功 -> 下架中 -> 下架失败
 * <AUTHOR>
 * @Date 2019/11/8 11:21
 **/
public enum FollowSellDataStatusEnum {

    _0(0, "待刊登"),
    _1(1, "刊登中"),
    _2(2, "刊登成功"),
    _3(3, "刊登失败"),
    _4(4, "下架中"),
    _5(5, "下架成功"),
    _6(6, "下架失败"),
    _7(7, "删除成功"),
    _8(8, "删除失败"),
    ;
    FollowSellDataStatusEnum(Integer dataStatus, String desc){
        this.setDataStatus(dataStatus);
        this.setDesc(desc);
    }
    /**
     * 状态值
     */
    private Integer dataStatus;
    /**
     * 描述
     */
    private String desc;

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        for (FollowSellDataStatusEnum value : FollowSellDataStatusEnum.values()) {
            if(value.getDataStatus().equals(code)){
                return value.getDesc();
            }
        }
        return "";
    }
}


