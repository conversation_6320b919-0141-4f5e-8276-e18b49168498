package com.estone.erp.publish.amazon.mq;

/**
 * @Description: 刊登amazon队列声明
 */
public class AmazonQueues {

    /** 在线列表下架数据处理备份至tidb 队列 */
    public static final  String PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_QUEUE = "PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_QUEUE";

    /** 在线列表下架数据处理备份至tidb 队列key */
    public static final  String PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_KEY = "PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_KEY";


    /**
     * 待刊登模板重新刊登
     */
    public static final String AMAZON_REPUBLISH_WAIT_TEMPLATE_QUEUE = "AMAZON_REPUBLISH_WAIT_TEMPLATE_QUEUE";
    public static final String AMAZON_REPUBLISH_WAIT_TEMPLATE_KEY = "AMAZON_REPUBLISH_WAIT_TEMPLATE_KEY";


    /**
     * Amazon亏损订单消息
     */
    public static final String AMAZON_DEFICIT_SEND_PUBLISH = "AMAZON_DEFICIT_SEND_PUBLISH";

    /**
     * Amazon导出队列
     */
    public static final String AMAZON_DOWNLOAD_QUEUE = "AMAZON_DOWNLOAD_QUEUE";
    public static final String AMAZON_DOWNLOAD_QUEUE_KEY = "AMAZON_DOWNLOAD_QUEUE_KEY";


    /**
     * AmazonGPSR - 生成PDF
     */
    public static final String AMAZON_UPDATE_GPSR_QUEUE = "AMAZON_UPDATE_GPSR_QUEUE";
    public static final String AMAZON_UPDATE_GPSR_QUEUE_KEY = "AMAZON_UPDATE_GPSR_QUEUE_KEY";

    /**
     * Amazon 任务作业调度队列
     */
    public static final String AMAZON_TASK_JOB_SCHEDULING_QUEUE = "AMAZON_TASK_JOB_SCHEDULING_QUEUE";
    public static final String AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY = "AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY";


    /**
     * Amazon 模板刊登队列
     */
    public static final String AMAZON_TEMPLATE_PUBLISH_QUEUE = "AMAZON_TEMPLATE_PUBLISH_QUEUE";
    public static final String AMAZON_TEMPLATE_PUBLISH_QUEUE_KEY = "AMAZON_TEMPLATE_PUBLISH_QUEUE_KEY";


    /**
     * Amazon spu 刊登队列
     */
    public static final String AMAZON_SPU_PUBLISH_QUEUE = "AMAZON_SPU_PUBLISH_QUEUE";
    public static final String AMAZON_SPU_PUBLISH_QUEUE_KEY = "AMAZON_SPU_PUBLISH_QUEUE_KEY";

    /**
     * Amazon 刊登完成后置理队列
     */
    public static final String AMAZON_PUBLISH_AFTER_PROCESS_QUEUE = "AMAZON_PUBLISH_AFTER_PROCESS_QUEUE";
    public static final String AMAZON_PUBLISH_AFTER_PROCESS_QUEUE_KEY = "AMAZON_PUBLISH_AFTER_PROCESS_QUEUE_KEY";

    /**
     * Amazon offline config 下架策略配置队列 队列
     */
    public static final String AMAZON_OFFLINE_CONFIG_QUEUE = "AMAZON_OFFLINE_CONFIG_QUEUE";
    public static final String AMAZON_OFFLINE_CONFIG_QUEUE_KEY = "AMAZON_OFFLINE_CONFIG_QUEUE_KEY";

    /**
     * Amazon offline execute 队列
     */
    public static final String AMAZON_OFFLINE_EXECUTE_QUEUE = "AMAZON_OFFLINE_EXECUTE_QUEUE";
    public static final String AMAZON_OFFLINE_EXECUTE_QUEUE_KEY = "AMAZON_OFFLINE_EXECUTE_QUEUE_KEY";

    /**
     * 产品系统-新品修图完成推送刊登队列
     */
    public static final String NEW_PRODUCT_PS_CONFIRM_TO_PUBLISH_QUEUE = "NEW_PRODUCT_PS_CONFIRM_TO_PUBLISH_QUEUE";

    /**
     * AMAZON同步属性KEY队列
     */
    public static final String AMAZON_PROPERTIES_KEY_SYNC_QUEUE = "AMAZON_PROPERTIES_KEY_SYNC_QUEUE";
    public static final String AMAZON_PROPERTIES_KEY_SYNC_KEY = "AMAZON_PROPERTIES_KEY_SYNC_KEY";

    /**
     * AMAZON回调属性KEY队列
     */
    public static final String AMAZON_PROPERTIES_KEY_RESULT_QUEUE = "AMAZON_PROPERTIES_KEY_RESULT_QUEUE";
    public static final String AMAZON_PROPERTIES_KEY_RESULT_KEY = "AMAZON_PROPERTIES_KEY_RESULT_KEY";

    /**
     * Amazon JSON Feeds 结果队列
     */
    public static final String PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE = "PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE";

    /**
     * Amazon Template 刊登状态队列
     */
    public static final String AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE = "AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE";
    public static final String AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE_KEY = "AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE_KEY";

}

