package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecordCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecordExample;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-09 16:19:01
 */
public interface AmazonUpcRecordService {
    int countByExample(AmazonUpcRecordExample example);

    CQueryResult<AmazonUpcRecord> search(CQuery<AmazonUpcRecordCriteria> cquery);

    List<AmazonUpcRecord> selectByExample(AmazonUpcRecordExample example);

    AmazonUpcRecord selectByPrimaryKey(Long id);

    int insert(AmazonUpcRecord record);

    int updateByPrimaryKeySelective(AmazonUpcRecord record);

    int updateByExampleSelective(AmazonUpcRecord record, AmazonUpcRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);

    /**
     * 查询已存在的upc
     * @param upcList
     * @return
     */
    List<String>selectExistUpc(List<String> upcList);

    int batchInsert(List<AmazonUpcRecord> amazonUpcRecordList);
}