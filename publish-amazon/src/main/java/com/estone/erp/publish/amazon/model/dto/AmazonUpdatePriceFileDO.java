package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Amazon 批量文件改价
 * <AUTHOR>
 * @date 2023-12-04 11:39
 */
@Data
public class AmazonUpdatePriceFileDO {
    @ExcelProperty("账号")
    private String accountNumber;

    @ExcelProperty("子asin码")
    private String sonAsin;

    @ExcelProperty("sellerSku")
    private String sellerSku;

    @ExcelProperty("SKU")
    private String articleNumber;

    @ExcelProperty("价格")
    private String beforePrice;

    @ExcelProperty("asin状态")
    private String itemStatus;

    @ExcelProperty("是否在线")
    private String isOnline;

    @ExcelProperty("单品状态")
    private String skuStatus;

    @ExcelProperty("库存")
    private Integer quantity;

    @ExcelProperty("最新listing价格")
    private String afterPrice;

    @ExcelProperty("日期")
    private Date pushDate;
}
