package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AmazonTidbSubLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_tidb_sub_log.id
     */
    private Long id;

    /**
     * 账号 database column amazon_tidb_sub_log.account_number
     */
    private String accountNumber;

    /**
     * sellersku database column amazon_tidb_sub_log.seller_sku
     */
    private String sellerSku;

    /**
     * 操作类型 database column amazon_tidb_sub_log.operation_type
     */
    private String operationType;

    /**
     * 报错信息 database column amazon_tidb_sub_log.remark
     */
    private String remark;

    /**
     *  database column amazon_tidb_sub_log.create_time
     */
    private Date createTime;
}