package com.estone.erp.publish.config;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.upload.SeaweedFSUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.AmazonFollowSellPublishCall;
import com.estone.erp.publish.amazon.call.AmazonProductPublishCall;
import com.estone.erp.publish.amazon.call.model.CategoryTree;
import com.estone.erp.publish.amazon.call.process.submit.AbstractPublishProcesser;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.constant.AmazonJobConstant;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 
 * @Description: 初始化服务类
 * 
 * @ClassName: InitService
 * @Author: Kevin
 * @Date: 2018/08/08
 * @Version: 0.0.1
 */
@Service
@Slf4j
public class InitService {
    @Resource
    private AmazonCallService amazonCallService;
    @Resource
    private AmazonConstantMarketHelper amazonConstantMarketHelper;

    /**
     * 
     * @Description: 初始化方法
     *
     * @Author: Kevin
     * @Date: 2018/08/08
     * @Version: 0.0.1
     */
    public void start() {
        AbstractPublishProcesser.delayTaskDispatcher.start();
        AmazonFollowSellPublishCall.delayTaskDispatcher.start();
        AmazonProductPublishCall.delayTaskDispatcher.start();
        // 初始化各站点分类数据到缓存
        AmazonExecutors.submit(responseJson -> {
            long start = System.currentTimeMillis();
            List<String> accounts = EnvironmentSupplierWrapper.execute(List.of("local", "dev"),
                    () -> {
                        return List.of("US-rtrty489");
                    },
                    () -> {

                        Map<String, String> marketplaceIdAccountList = amazonConstantMarketHelper.getMarketplaceIdAccountList();
                        if (MapUtils.isEmpty(marketplaceIdAccountList)) {
                            return List.of();
                        }
                        return marketplaceIdAccountList.values().stream().distinct().collect(Collectors.toList());
                    });
            log.warn("start init category map.");
            accounts.forEach(account -> {
                if (StringUtils.isNotBlank(account)) {
                    long t1 = System.currentTimeMillis();
                    Map<String, BrowseTreeReport.Node> categoryMap = AmazonConstant.getCategoryMapByAccount(account);
                    CategoryTree root = amazonCallService.getAmazonCategoryTree(account, true);
                    resolveCategory(root.getNode(), -1, categoryMap);
                    long t2 = System.currentTimeMillis();
                    AmazonConstant.ACCOUNT_CATEGORY_INIT_FINISH_MAP.put(account, true);
                    log.warn("init [{}] category map, size:[{}] cost time[{}]ms", account, categoryMap.size(), t2 - t1);
                }
            });
            long end = System.currentTimeMillis();
            log.warn("init category map end, total cost time[{}]ms", end - start);
            AmazonConstant.ACCOUNT_CATEGORY_INIT_FINISH.set(true);
        });
    }

    private static void loadingSonskuCategoryIdMap (){
        List<String> data = new ArrayList<>();
        try {
            //url = "http://172.16.10.51:8888/fixed/刊登/2022-08/29-17-42-10-456/skuAndCatId.txt";
            //http://10.100.1.200:8888/fixed/刊登/2022-08/29-17-42-10-456/skuAndCatId.txt";
            SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
            String fileUrl = systemParamService.querySystemParamByCodeKey("SKU_AND_CATEGORYID.SKU_AND_CATEGORYID_FILE_URL").getParamValue();
            log.warn("文件地址：" + fileUrl);
            String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
            File file = File.createTempFile("skuAndCatId" ,suffix );
            if (null == file) {
                log.info("文件生成失败" + file.getPath());
            }
            SeaweedFSUtils.downloadFile(fileUrl, new FileOutputStream(file));
            data = FileUtils.readLines(file, "UTF-8");
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        if(CollectionUtils.isEmpty(data)){
            log.error("读取文件加载分类数据为空");
            // 若必须加载自行抛异常
            return;
        }
        data.forEach(str -> {
                    try {
                        str = str.replace(",", ":");
                        Map<String, Integer> sonskuMap = JSON.parseObject(str, HashMap.class);
                        AmazonJobConstant.SONSKU_CATEGORYID_MAP.putAll(sonskuMap);
                    } catch (Exception e) {
                        log.error("转换分类出错数据：" + str);
                    }
                }
        );
        log.info("加载子sku分类Id关系map对应的size：" + AmazonJobConstant.SONSKU_CATEGORYID_MAP.size());
    }

    private static void resolveCategory(CategoryTree.Node node, int i, Map<String, BrowseTreeReport.Node> categoryMap) {
        Map<String, CategoryTree.Node> children = node.getChildren();
        if (MapUtils.isNotEmpty(children)) {
            for (Map.Entry<String, CategoryTree.Node> entry : children.entrySet()) {
                CategoryTree.Node v = entry.getValue();
                BrowseTreeReport.Node node1 = v.getNode();
                String s = node1.getBrowsePathByName();
                categoryMap.put(s.toLowerCase(), node1);
                resolveCategory(v, i + 1, categoryMap);
            }
        }
    }
}
