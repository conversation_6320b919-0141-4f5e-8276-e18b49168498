package com.estone.erp.publish.amazon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.amazon.enums.PublishTypeEnum;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateCriteria;
import com.estone.erp.publish.amazon.service.AmazonAutoPublishReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateAutoService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.util.POIUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> amazon_auto_publish_report
 * @Date 2020-08-06 10:24:09
 * @Description Amazon 自动刊登数据统计
 */
@Slf4j
@RestController
@RequestMapping("amazonAutoPublishReport")
public class AmazonAutoPublishReportController {

    @Resource
    private AmazonAutoPublishReportService amazonAutoPublishReportService;
    @Resource
    private AmazonTemplateAutoService amazonTemplateAutoService;

    private final String[] headers = {"SPU","图片","标题","系统类目","自动刊登范本数量","关联模板数量","刊登成功模板数量","刊登成功率","出单数量","出单转化率"};

    /**
     * http://************/web/#/31?page_id=3246
     * @param requestParam
     * @return
     */
    @PostMapping
    public ApiResult<?> postAmazonAutoPublishReport(@RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonAutoPublishReport": // 查询列表
                    CQuery<AmazonAutoPublishReportCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonAutoPublishReportCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonAutoPublishReport> results = amazonAutoPublishReportService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 关联范本
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/getSpuTemplate")
    public ApiResult<?> getSpuTemplate(@RequestBody(required = true) ApiRequestParam<String> requestParam){
        CQuery<AmazonTemplateAutoCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonTemplateAutoCriteria>>() {});
        Assert.notNull(cquery, "cquery is null!");
        AmazonTemplateAutoCriteria query = cquery.getSearch();
        if(StringUtils.isBlank(query.getParentSku())){
            return ApiResult.newError("parentSku is null");
        }
        query.setIsLock(true);
        query.setPublishType(PublishTypeEnum.AUTO_PUBLISH.getCode());
        CQueryResult<AmazonTemplateAuto> search = amazonTemplateAutoService.search(cquery);

        return search;
    }

    /**
     * spu关联模板信息
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/spuRelationTemplate")
    public ApiResult<?> putAmazonAutoPublishReport(@RequestBody ApiRequestParam<String> requestParam){
        CQuery<AmazonTemplateCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonTemplateCriteria>>() {});
        if(cquery == null){
            return ApiResult.newError("cquery is null");
        }
        AmazonTemplateCriteria search = cquery.getSearch();
        String parentSku = search.getParentSku();
        if(StringUtils.isBlank(parentSku)){
            return ApiResult.newError("parentSku is null");
        }

        return amazonAutoPublishReportService.spuRelationTemplate(cquery);
    }

    /**
     * 导出
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/export")
    public ApiResult<?> putAmazonAutoPublishReport(@RequestBody ApiRequestParam<String> requestParam, HttpServletRequest request, HttpServletResponse response) {
        AmazonAutoPublishReportCriteria query = requestParam.getArgsValue(new TypeReference<AmazonAutoPublishReportCriteria>() {
        });
        /*AmazonAutoPublishReportExample example = query.getExample();
        List<AmazonAutoPublishReport> list = amazonAutoPublishReportService.selectByExample(example);
        if(CollectionUtils.isNotEmpty(list) && list.size() > 100000){
            return ApiResult.newError("最多导出100000条数据！");
        }*/


        // 分页 避免数据库大数据查询
        int offset = 0;
        int limit = 10000;
        AmazonAutoPublishReportExample example = query.getExample();
        example.setLimit(limit);

        int count = 0;
        List<AmazonAutoPublishReport> list = new ArrayList<>();
        while (true) {
            example.setOffset(offset);
            List<AmazonAutoPublishReport> pageAmazonAutoPublishReport = amazonAutoPublishReportService.selectByExample(example);
            if(CollectionUtils.isEmpty(pageAmazonAutoPublishReport)) {
                break;
            }

            count += pageAmazonAutoPublishReport.size();
            if(count > 100000){
                return ApiResult.newError("最多导出100000条数据！");
            }

            list.addAll(pageAmazonAutoPublishReport);
            offset += limit;
            if(pageAmazonAutoPublishReport.size() < limit) {
                break;
            }
        }

        OutputStream os = null;
        try {
            os = response.getOutputStream();

            String fileName = "亚马逊自动刊登成功率"+ POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
            fileName = POIUtils.getEncodeFileName(request, fileName);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            final List<List<String>> awLists = new ArrayList<>(list.size());
            POIUtils.createExcel(headers, list, item -> {
                awLists.clear();
                List<String> awList = new ArrayList<>(headers.length);
                awList.add(POIUtils.transferObj2Str(item.getSpu()));
                awList.add(POIUtils.transferObj2Str(item.getImage()));
                awList.add(POIUtils.transferObj2Str(item.getTitle()));
                awList.add(POIUtils.transferObj2Str(item.getSysCategoryName()));
                awList.add(POIUtils.transferObj2Str(item.getParentTemplateCount()));
                awList.add(POIUtils.transferObj2Str(item.getRelationCount()));
                awList.add(POIUtils.transferObj2Str(item.getSuccessCount()));
                awList.add(POIUtils.transferObj2Str(null != item.getSuccessRate() ? ((item.getSuccessRate() * 100) + "%") : "0%"));
                awList.add(POIUtils.transferObj2Str(item.getOrderCount()));
                awList.add(POIUtils.transferObj2Str(null != item.getOutOrderRate() ? ((item.getOutOrderRate() * 100) + "%") : "0%"));
                awLists.add(awList);
                return awLists;

            }, true , os);

        } catch (Exception e) {
            log.error("导出出错", e);
        }finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

}