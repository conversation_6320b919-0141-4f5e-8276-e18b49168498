package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.amazon.mq.feed.SpFeedsResultMqListener;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 新版同步产品MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class SyncAmazonReportFileMqConfig {

    private int syncAmazonReportFileMqConsumers;
    private int syncAmazonReportFileMqPrefetchCount;
    private boolean syncAmazonReportFileMqListener;

    @Bean
    public Queue syncAmazonReportFile() {
        return new Queue(PublishQueues.PUBLISH_AMAZON_REQUEST_REPORT_RESULT_QUEUE);
    }

    @Bean
    public Binding syncAmazonReportFileBinding() {
        return new Binding(PublishQueues.PUBLISH_AMAZON_REQUEST_REPORT_RESULT_QUEUE, DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.PUBLISH_AMAZON_REQUEST_REPORT_RESULT_KEY, null);
    }
    @Bean
    public SyncAmazonReportFileMqListener syncAmazonReportFileMqListener() {
        return new SyncAmazonReportFileMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer syncAmazonReportFileListenerContainer(
            SyncAmazonReportFileMqListener syncAmazonReportFileMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.PUBLISH_AMAZON_REQUEST_REPORT_RESULT_QUEUE, syncAmazonReportFileMqListener);
        return container;
    }

    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (syncAmazonReportFileMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(syncAmazonReportFileMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(syncAmazonReportFileMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    //  监听feed结果
    private int amazonRequestFeedResultMqConsumers;
    private int amazonRequestFeedResultMqPrefetchCount;
    private boolean amazonRequestFeedResultMqListener;


    @Bean
    public SpFeedsResultMqListener spFeedsResultMqListener() {
        return new SpFeedsResultMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer spFeedsResultMqListenerContainer(
            SpFeedsResultMqListener spFeedsResultMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        feedSimpleMessageListenerContainer(container, PublishQueues.PUBLISH_AMAZON_REQUEST_FEEDS_RESULT_QUEUE, spFeedsResultMqListener);
        return container;
    }

    private void feedSimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                    SpFeedsResultMqListener spFeedsResultMqListener) {
        if (amazonRequestFeedResultMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonRequestFeedResultMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonRequestFeedResultMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(spFeedsResultMqListener);// 监听处理类
        }
    }
}