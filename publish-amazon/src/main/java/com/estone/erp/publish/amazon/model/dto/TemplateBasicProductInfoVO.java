package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 模板产品基础信息
 *
 * <AUTHOR>
 * @date 2023-09-06 10:17
 */
@Data
public class TemplateBasicProductInfoVO {

    /**
     * spu
     */
    private String spu;

    /**
     * 平台分类ID
     */
    private String platformCategoryId;

    /**
     * 平台分类类型
     */
    private String platformProductType;

    /**
     * 分类类型大类
     */
    private String parentProductType;

    /**
     * 分类模板名称
     */
    private String platformCategoryTemplateName;

    /**
     * 中文标题
     */
    private String chineseTitle;

    /**
     * 系统类目
     */
    private String systemCategoryNamePath;
    private String systemCategoryCodePath;

    /**
     * 状态
     */
    private String productStatus;

    /**
     * 禁售信息
     */
    private Map<String, Set<String>> forbiddenInfoMap;

    /**
     * 产品标签
     */
    private String productLabels;

    /**
     * 刊登备注
     */
    private String postRemark;

    /**
     * 预估包装重量
     */
    private Double packageWeight;

    /**
     * 子SKU信息
     */
    private List<SubSkuInfo> skusInfo;


    @Data
    public static class SubSkuInfo {

        /**
         * sku
         */
        private String sku;

        /**
         * 单品状态
         */
        private String itemStatus;

        /**
         * 尺码
         */
        private String size;

        /**
         * 是否禁售
         */
        private Boolean isForbidChannel;
        /**
         * 包裹尺寸-长
         */
        private BigDecimal packLength;

        /**
         * 包裹尺寸-宽
         */
        private BigDecimal packWidth;

        /**
         * 包裹尺寸-高
         */
        private BigDecimal packHeight;

        /**
         * 预估包装重量
         */
        private Double packageWeight;
    }

    public void addSubSkuInfo(String sku, String itemStatus, String size, Boolean isForbidChannel, BigDecimal packLength, BigDecimal packWidth, BigDecimal packHeight, Double packageWeight) {
        if (CollectionUtils.isEmpty(this.getSkusInfo())) {
            this.skusInfo = new ArrayList<>();
        }
        SubSkuInfo subSkuInfo = new SubSkuInfo();
        subSkuInfo.setSku(sku);
        subSkuInfo.setItemStatus(itemStatus);
        subSkuInfo.setSize(size);
        subSkuInfo.setIsForbidChannel(isForbidChannel);
        subSkuInfo.setPackLength(packLength);
        subSkuInfo.setPackWidth(packWidth);
        subSkuInfo.setPackHeight(packHeight);
        subSkuInfo.setPackageWeight(packageWeight);
        this.skusInfo.add(subSkuInfo);
    }
}
