package com.estone.erp.publish.amazon.call;

import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.model.AmazonListingUpdateMsgLog;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonPublishImagePath;
import com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample;
import com.estone.erp.publish.amazon.service.AmazonListingUpdateMsgLogService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description amazon产品列表刊登 和 定时把停产，存档的产品库存改为0，辅助类
 * <AUTHOR>
 * @Date 2019/10/25 15:32
 **/
@Slf4j
@Component
public class AmazonProductPublishCallHelper {

    @Autowired
    private AmazonPublishImagePathService amazonPublishImagePathService;
    @Autowired
    private AmazonProcessReportService amazonProcessReportService;
    @Autowired
    private AmazonListingUpdateMsgLogService amazonListingUpdateMsgLogService;
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;


    /**
     *
     * @Description: 获取图片路径
     *
     * @param accountNumber 账号名
     * @return 图片路径
     * @Author: Kevin
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    public synchronized String getImagePath(String accountNumber) {
        AmazonPublishImagePathExample example = new AmazonPublishImagePathExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<AmazonPublishImagePath> amazonPublishImagePaths = amazonPublishImagePathService
                .findByExample(example);
        if (amazonPublishImagePaths != null && CollectionUtils.isNotEmpty(amazonPublishImagePaths)) {
            return amazonPublishImagePaths.get(0).getImagePath();
        }

        AmazonPublishImagePath amazonPublishImagePath = null;

        String imagePath = null;
        // 给账号设置图片路径
        AmazonPublishImagePathExample query = new AmazonPublishImagePathExample();
        do {
            imagePath = "/" + RandomStringUtils.randomAlphanumeric(8) + "/" + RandomStringUtils.randomAlphanumeric(16)
                    + "/";
            query.createCriteria().andImagePathEqualTo(imagePath);
            if (CollectionUtils.isEmpty(amazonPublishImagePathService.findByExample(query))) {
                amazonPublishImagePath = new AmazonPublishImagePath();
                amazonPublishImagePath.setAccountNumber(accountNumber);
                amazonPublishImagePath.setImagePath(imagePath);
                amazonPublishImagePathService.insert(amazonPublishImagePath);
                break;
            }
        }
        while (true);

        return imagePath;
    }

    public List<AmazonProcessReport> initProcessReports(PublishData<AmazonVariantBO> publishData) {
        List<AmazonVariantBO> amazonVariants = publishData.getUnitDatas();
        String feedType = publishData.getFeedType();
        List<AmazonProcessReport> reports = new ArrayList<AmazonProcessReport>(1);
        for (AmazonVariantBO variant : amazonVariants) {
            AmazonProcessReport report = new AmazonProcessReport();
            report.setFeedType(feedType);
            report.setAccountNumber(variant.getAccountNumber());
            report.setStatusCode(ProcessingReportStatusCode.Init.name());
            report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
            report.setDataValue(variant.getSellerSku());
            report.setRelationId(variant.getId());
            report.setRelationType(ProcessingReportTriggleType.Listing.name());

            // 记录改前改后值
            report.setPreviousFulfillmentLatencyValue(variant.getPreviousFulfillmentLatencyValue());
            report.setAfterFulfillmentLatencyValue(variant.getAfterFulfillmentLatencyValue());

            report.setPreviousQuantityValue(variant.getPreviousQuantityValue());
            report.setAfterQuantityValue(variant.getAfterQuantityValue());

            report.setPreviousPriceValue(variant.getPreviousPriceValue());
            report.setAfterPriceValue(variant.getAfterPriceValue());
            amazonProcessReportService.insert(report);
            reports.add(report);
        }

        return reports;
    }


    /**
     *
     * @Description: 结束禁售或下线的sku
     *
     * @param unitPublishData
     * @Author: Kevin
     * @Date: 2019/01/16
     * @Version: 0.0.1
     */
    public void finishForbidOrOffLineSkus(PublishData<AmazonVariantBO> unitPublishData) {

// 产品上传不走这个逻辑

//        Iterator<AmazonVariantBO> iterator = unitPublishData.getUnitDatas().iterator();
//        Map<String, AmazonProcessReport> sku2ReportMap = unitPublishData.getSku2ReportMap();
//        // 查询禁售货号
////        Set<String> forbidSkuSet = new HashSet<>(ProductServiceUtils.getAllForbiddenSaleSku());
//        //Set<String> forbidSkuSet = new HashSet<>(forbidChannelService.getAllForbidSkus());
//        List<AmazonProcessReport> updateReports = new ArrayList<>(1);
//        while (iterator.hasNext()) {
//            AmazonVariantBO unitData = iterator.next();
//            Map<String, String> sellerSku2skuMap = getUnitDataSellerSku2SkuMap(unitData);
//            String country = unitData.getCountry();
//            if (MapUtils.isNotEmpty(sellerSku2skuMap) && StringUtils.isNotBlank(country)) {
//                List<String> skus = new ArrayList<>(sellerSku2skuMap.values());
//                String msg = null;
//                // 根据sku和站点判断是否禁售
//                try {
//                    for (String sku : skus) {
//                        Map<String,String> skuSite = new HashMap<>();
//                        skuSite.put(sku,country);
//
//                        Map<String, Boolean> checkSku = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySite(SaleChannel.CHANNEL_AMAZON,skuSite);
//                        Boolean check = checkSku.get(sku);
//                        if (check) {
//                            msg = sku + "禁售，不允许刊登";
//                            break;
//                        }
//                    }
//                } catch (Exception e) {
//                    msg = "调用检查禁售方法报错：" + e.getMessage();
//                    e.printStackTrace();
//                }
//                // 无禁售货号，再次过滤下线货号
//                if (StringUtils.isEmpty(msg)) {
//                    //List<String> offLineSkus = stockKeepingUnitService.selectOfferLineSkus(skus);
//                    //根据sku集合查询单品状态为'Stop', 'Archived'的sku
//                    List<String> offLineSkus = ProductServiceUtils.findStopAndArchived(skus);
//                    if (CollectionUtils.isNotEmpty(offLineSkus)) {
//                        msg = String.join(", ", offLineSkus) + "状态为停产，存档时，不允许刊登";
//                    }
//                }
//
//                // 存在禁售或下线的sku，则该单元数据不再刊登，并标记处理报告为失败
//                if (StringUtils.isNotEmpty(msg)) {
//                    for (Map.Entry<String, String> entry : sellerSku2skuMap.entrySet()) {
//                        AmazonProcessReport report = sku2ReportMap.get(entry.getKey());
//                        updateReports.add(report);
//                        finshProcessReport(report, false, msg);
//                    }
//
//                    iterator.remove();
//                }
//            }
//        }
//        if (CollectionUtils.isNotEmpty(updateReports)) {
//            amazonProcessReportService.update(updateReports);
//        }
    }

    public Map<String, String> getUnitDataSellerSku2SkuMap(AmazonVariantBO unitData) {
        Map<String, String> result = new HashMap<>(1);
        result.put(unitData.getSellerSku(), unitData.getAccountNumber());
        return result;
    }

    public boolean finshProcessReport(AmazonProcessReport report, boolean status, String msg) {
        if (report == null || ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
            return false;
        }

        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setStatus(status);
        report.setResultMsg(msg);
        report.setFinishDate(new Timestamp(System.currentTimeMillis()));
        return true;
    }

    /**
     *
     * @Description: 设置错误的处理报告为结束状态
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void finishErrorProcessReports(PublishData<AmazonVariantBO> publishData) {
        List<AmazonProcessReport> reports = publishData.getReports();
        Map<String, String> errorSku2MsgMap = publishData.getErrorSku2MsgMap();
        if (MapUtils.isNotEmpty(errorSku2MsgMap)) {
            List<AmazonProcessReport> errorReports = new ArrayList<AmazonProcessReport>(errorSku2MsgMap.size());
            for (AmazonProcessReport report : reports) {
                String sku = report.getDataValue();
                if (errorSku2MsgMap.containsKey(sku)) {
                    errorReports.add(report);
                    finshProcessReport(report, false, errorSku2MsgMap.get(sku));
                }
            }

            amazonProcessReportService.update(errorReports);
        }
    }

    /**
     *
     * @Description: 设置处理报告为进行状态
     *
     * @param reports 报告list
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void runProcessReports(List<AmazonProcessReport> reports) {
        reports.forEach(report -> {
            // 已经结束的处理报告不处理
            if (ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
                return;
            }

            report.setStatusCode(ProcessingReportStatusCode.Processing.name());
        });
        amazonProcessReportService.update(reports);
    }

    /**
     *
     * @Description: 更新处理报告的taskId
     *
     * @param publishData 刊登数据
     * @param taskId taskId
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void updatePublishDataTaskId(PublishData<AmazonVariantBO> publishData, String taskId) {
        publishData.setTaskId(taskId);
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            report.setTaskId(taskId);
        });
        amazonProcessReportService.update(reports);
    }

    /**
     * 添加日志
     * @param publishData
     * @param xml
     */
    public void recordListingBrand(PublishData<AmazonVariantBO> publishData, String xml) {
        for (AmazonVariantBO amazonVariantBO : publishData.getUnitDatas()) {
            try {
                String accountNumber = amazonVariantBO.getAccountNumber();
                String sellersku = amazonVariantBO.getSellerSku();
                AmazonListingUpdateMsgLog amazonListingUpdateMsgLog = new AmazonListingUpdateMsgLog();
                amazonListingUpdateMsgLog.setAccountNumber(accountNumber);
                amazonListingUpdateMsgLog.setSellerSku(sellersku);
                amazonListingUpdateMsgLog.setAfterXml(xml);
                String _id = accountNumber + "_"+ sellersku;
                EsAmazonProductListing esAmazonProductListing =  esAmazonProductListingService.findAllById(_id);
                if (null != esAmazonProductListing){
                    amazonListingUpdateMsgLog.setBeforeBrand(esAmazonProductListing.getBrandName());
                }
                amazonListingUpdateMsgLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                amazonListingUpdateMsgLogService.insert(amazonListingUpdateMsgLog);
            }catch (Exception e){
                log.error(e.getMessage());
            }
        }
    }

}
