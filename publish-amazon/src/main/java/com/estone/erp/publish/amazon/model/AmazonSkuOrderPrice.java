package com.estone.erp.publish.amazon.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
public class AmazonSkuOrderPrice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_sku_order_price.id
     */
    private Integer id;

    /**
     * 店铺 database column amazon_sku_order_price.account_number
     */
    @ExcelProperty("account_number")
    private String accountNumber;

    /**
     * 站点 database column amazon_sku_order_price.site
     */
    @ExcelProperty("站点")
    private String site;

    /**
     * sellerSku database column amazon_sku_order_price.seller_sku
     */
    @ExcelProperty("sellerSku")
    private String sellerSku;

    /**
     * 父asin database column amazon_sku_order_price.parent_asin
     */
    private String parentAsin;

    /**
     * 子asin database column amazon_sku_order_price.son_asin
     */
    private String sonAsin;

    /**
     * 是否在线 database column amazon_sku_order_price.is_online
     */
    private Boolean isOnline;

    /**
     * 货号 sku database column amazon_sku_order_price.article_number
     */
    @ExcelProperty("系统sku")
    private String articleNumber;

    /**
     * 产品标签 database column amazon_sku_order_price.tag_names
     */
    private String tagNames;

    /**
     * 币种 database column amazon_sku_order_price.currency_code
     */
    @ExcelProperty("币种")
    private String currencyCode;

    /**
     * 订单物流 database column amazon_sku_order_price.order_logistics
     */
    @ExcelProperty("物流方式")
    private String orderLogistics;

    /**
     * 订单金额 database column amazon_sku_order_price.order_price
     */
    @ExcelProperty("订单金额")
    private Double orderPrice;

    /**
     * 订单毛利率 database column amazon_sku_order_price.order_profit
     */
    @ExcelProperty("毛利率")
    private Double orderProfit;

    /**
     * 包裹重量 database column amazon_sku_order_price.package_weight
     */
    @ExcelProperty("预估重量(g)")
    private Double packageWeight;

    /**
     * 价格 database column amazon_sku_order_price.price
     */
    private Double price;

    /**
     * 运费 database column amazon_sku_order_price.shipping_cost
     */
    private Double shippingCost;

    /**
     * 总价 = 价格+运费 database column amazon_sku_order_price.total_price
     */
    private Double totalPrice;

    /**
     * 改价规则code database column amazon_sku_order_price.rule_code
     */
    private Integer ruleCode;

    /**
     * 改价规则 database column amazon_sku_order_price.rule
     */
    private String rule;

    /**
     * 改后价格 database column amazon_sku_order_price.update_price
     */
    private Double updatePrice;

    /**
     * 改后总价 database column amazon_sku_order_price.update_total_price
     */
    private Double updateTotalPrice;

    /**
     * 改后毛利率 database column amazon_sku_order_price.update_profit
     */
    private Double updateProfit;

    /**
     * 销售成本价
     */
    private BigDecimal saleCost;

    /**
     * 算价物流 database column amazon_sku_order_price.calc_logistics
     */
    private String calcLogistics;

    /**
     * 毛利率计算物流 database column amazon_sku_order_price.profit_logistics
     */
    private String profitLogistics;

    /**
     * 状态 database column amazon_sku_order_price.status
     */
    private Integer status;

    /**
     * 备注 database column amazon_sku_order_price.result_msg
     */
    private String resultMsg;

    /**
     * 扩展数据 database column amazon_sku_order_price.extra_data
     */
    private String extraData;

    /**
     * 创建时间 database column amazon_sku_order_price.create_time
     */
    private Timestamp createTime;

    /**
     * 修改时间 database column amazon_sku_order_price.update_time
     */
    private Timestamp updateTime;
}