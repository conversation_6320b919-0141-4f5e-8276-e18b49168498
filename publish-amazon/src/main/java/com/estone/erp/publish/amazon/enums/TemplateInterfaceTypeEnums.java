package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Amazon 模版 接口类型
 */
@Getter
@AllArgsConstructor
public enum TemplateInterfaceTypeEnums {
    XSD(1, "XSD"),
    JSON(2, "JSON");

    private final int code;
    private final String type;

    public boolean isTrue(Integer code) {
        return code != null && code.equals(this.code);
    }
}
