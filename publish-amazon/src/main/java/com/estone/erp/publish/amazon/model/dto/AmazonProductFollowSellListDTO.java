package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.common.annotation.CheckColumn;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 产品列表 批量跟卖数据明细
 * <AUTHOR>
 * @Date 2019/10/10 10:30
 **/
@Data
public class AmazonProductFollowSellListDTO {

    /**
     * 产品列表id
     */
    @CheckColumn(columnName = "id")
    private Integer id;

    /**
     * 变体id
     */
    //@CheckColumn(columnName = "variantId")
    private Integer variantId;

    /**
     * 关联key(账号_平台Sku(accountNumber_sellerSku))
     */
    @CheckColumn(columnName = "关联key", maxLength = 200)
    private String relationKey;

    /**
     * parentSku
     */
    @CheckColumn(columnName = "货号", maxLength = 255)
    private String parentSku;
    /**
     * sellerSku
     */
    @CheckColumn(columnName = "sellerSku", maxLength = 255)
    private String sellerSku;
    /**
     * 产品id类型
     */
    @CheckColumn(columnName = "产品ID类型", maxLength = 20)
    private String standardProdcutIdType;

    /**
     * 产品id值
     */
    @CheckColumn(columnName = "产品id值（ASINS）", maxLength = 200)
    private String standardProdcutIdValue;

    /**
     * 价格
     */
    @CheckColumn(columnName = "价格", isNumber = true, minLimit = 0, maxLimit = ********)
    private Double standardPrice;
    /**
     * 库存
     */
    @CheckColumn(columnName = "库存",isNumeric = true, minLimit = 0, maxLimit = ********)
    private Integer quantity;
    /**
     * 币别
     */
    @CheckColumn(columnName = "币别", maxLength = 10)
    private String currency;
}
