package com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Description: SP库存报告类型为CSV格式的抽象策略类
 */
@Slf4j
public abstract class BaseInventoryReportSpTypeWithCsvStrategy {

    public BaseInventoryReportSpTypeWithCsvStrategy(AmazonAccount account, String reportType, List<String> marketplaceIds) {
        this.syncSpProductData = new SyncSpProductData(account);
        this.reportType = reportType;
        this.marketplaceIds = marketplaceIds;
    }

    /**
     * 同步产品数据
     */
    private SyncSpProductData syncSpProductData;

    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 站点类型列表
     */
    private List<String> marketplaceIds;



    public SyncSpProductData getSyncSpProductData() {
        return syncSpProductData;
    }

    public void setSyncSpProductData(SyncSpProductData syncSpProductData) {
        this.syncSpProductData = syncSpProductData;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public List<String> getMarketplaceIds() {
        return marketplaceIds;
    }

    public void setMarketplaceIds(List<String> marketplaceIds) {
        this.marketplaceIds = marketplaceIds;
    }

    protected boolean checkParams(AmazonProductListing t, String[] splits) {
        return t != null && ArrayUtils.isNotEmpty(splits);
    }

    /**
     * @param lineSplits 过滤函数
     * @Description: 构建SyncProductData数据
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    public void constructSyncProductData(List<String[]> lineSplits) {
        if (CollectionUtils.isEmpty(lineSplits)) {
            return;
        }
        // 清空SyncProductData数据
        syncSpProductData.clear();

        // 初始化表头 各个值对应的下标
        Map<String, Integer> headerMap = initHeaderMap(lineSplits.get(0));
        syncSpProductData.setHeaderMap(headerMap);
        if(MapUtils.isEmpty(headerMap)) {
            return;
        }

        // 构建sellerSku 与行分割数据的map
        constructSellerSku2SplitsMap(lineSplits);

        // 构建 AmazonProductListing 行数据对象
        constractProductListingData(syncSpProductData.getSellerSku2SplitsMap());
    }



    protected abstract String getTableSellerSku();

    /**
     * @Description: 构建SyncProductData SellerSku2SplitsMap
     */
    public void constructSellerSku2SplitsMap(List<String[]> lineSplits) {
        if (CollectionUtils.isEmpty(lineSplits)) {
            syncSpProductData.getSellerSku2SplitsMap().clear();
            return;
        }

        Map<String, String[]> sellerSku2SplitsMap = new HashMap<String, String[]>(lineSplits.size());
        lineSplits.forEach(splits -> {
            sellerSku2SplitsMap.put(getColumnValue(getTableSellerSku(), splits), splits);
        });
        syncSpProductData.setSellerSku2SplitsMap(sellerSku2SplitsMap);
    }

    /**
     * 初始化NewSyncProductData
     *
     * @param sellerSku2SplitsMap
     */
    public void constractProductListingData(Map<String, String[]> sellerSku2SplitsMap) {
        AmazonAccount amazonAccount = syncSpProductData.getAccount();
        if (MapUtils.isEmpty(sellerSku2SplitsMap) || null == amazonAccount) {
            return;
        }

        String accountNumber = amazonAccount.getAccountNumber();
        String site = getCountry(amazonAccount);
        syncSpProductData.setSite(site);
        int size = sellerSku2SplitsMap.size();
        List<AmazonProductListing> amazonProductListingList = new ArrayList<>(size);
        for (Map.Entry<String, String[]> selleSkuMap : sellerSku2SplitsMap.entrySet()) {
            String sellerSku = selleSkuMap.getKey();

            // sellerSku 为空 或者当前行是表头则跳出
            String headerSellerSku = this.getTableSellerSku();
            if(StringUtils.isBlank(sellerSku) || sellerSku.equals(headerSellerSku)) {
                continue;
            }

            try {
                String[] splits = selleSkuMap.getValue();

                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(accountNumber);
                amazonProductListing.setSite(site);
                amazonProductListing.setSellerSku(sellerSku);
                // 文件数据解析为产品对象
                fillLineData2AmazonProductListing(amazonProductListing, splits);
                syncSpProductData.addSellerSku2AmazonProductListingMap(sellerSku, amazonProductListing);
                amazonProductListingList.add(amazonProductListing);
            } catch (Exception e) {
                log.error("构建单条数据异常" + sellerSku + accountNumber);
            }
        }
        syncSpProductData.setAmazonProductListingList(amazonProductListingList);
    }

    /**
     * @param lines
     * @param filter 过滤函数
     * @return
     * @Description: 过滤行数据，并转换为按制表符分割的数组
     * @Author: Kevin
     * @Date: 2018/10/15
     * @Version: 0.0.1
     */
    public abstract List<String[]> filterAndTransferLines(List<String> lines, Function<String[], Boolean> filter);

    /**
     * @param t      变体对象
     * @param splits 分割的行数据
     * @Description: 将行数据转换为对象T
     * @Author: Kevin
     * @Date: 2018/10/12
     * @Version: 0.0.1
     */
    public abstract void fillLineData2AmazonProductListing(AmazonProductListing t, String[] splits);

    /**
     * 初始化表头对应下标map
     * @param splits
     * @return
     */
    public Map<String, Integer> initHeaderMap(String[] splits) {
        Map<String, Integer> headerMap = new HashMap<>();

        int length = splits.length;
        for (int i = 0; i < length ; i++) {
            headerMap.put(splits[i], i);
        }

        return headerMap;
    }

    public String getCountry(AmazonAccount account) {
        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        return amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId()).getMarketplace();
    }

    /**
     * 获取某列 在单前行的数据
     * @param columnName
     * @param splits
     * @return
     */
    protected String getColumnValue(String columnName, String[] splits) {
        if(StringUtils.isBlank(columnName)) {
            return null;
        }

        Map<String, Integer> headerMap = syncSpProductData.getHeaderMap();
        if(MapUtils.isEmpty(headerMap)) {
            return null;
        }

        if(!headerMap.containsKey(columnName)) {
            // 第一个字符可能会多一个\uFEFF 若获取不到表头对应下标则 添加上﻿﻿﻿﻿\uFEFF 再次获取
            columnName = "\uFEFF" + columnName;
            if(!headerMap.containsKey(columnName)) {
                return null;
            }
        }

        Integer index = headerMap.get(columnName);
        if(null == index) {
            log.error("存在异常数据" + columnName + "，表头" + JSON.toJSONString(headerMap) + "，单前行" + JSON.toJSONString(splits));
            return null;
        } else {
            String columnValue = splits[index];
            return StringUtils.isNotBlank(columnValue) ? columnValue : null;
        }
    }
}
