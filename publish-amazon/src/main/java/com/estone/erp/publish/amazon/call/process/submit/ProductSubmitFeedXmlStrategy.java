package com.estone.erp.publish.amazon.call.process.submit;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.xsd.model.AttributeWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.TypeWrapper;
import com.estone.erp.publish.amazon.model.AmazonVariant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 
 * @Description: 产品上传数据xml策略实现类
 * 
 * @ClassName: ProductSubmitFeedXmlStrategy
 * @Author: Kevin
 * @Date: 2018/08/22
 * @Version: 0.0.1
 */
@Component
public class ProductSubmitFeedXmlStrategy extends AbstractSubmitFeedXmlStrategy<AmazonVariantBO> {

    @Override
    public String transferProductPrice2Xml(PublishData<AmazonVariantBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Price", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        List<AmazonVariantBO> amazonVariants = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        String currency = publishData.getCurrency();
        OperationType operationType = publishData.getOperationType();
        for (AmazonVariantBO variant : amazonVariants) {
            String sellerSku = variant.getSellerSku();
            Double standardPrice = variant.getPrice();
            if (standardPrice == null || standardPrice <= 0d) {
                publishData.addErrorSku(sellerSku, "产品价格不能为空和0");
                continue;
            }

            msgId2SkuMap.put(messageId, sellerSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Price
            Element price = message.create("Price");
            price.create("SKU", sellerSku);
            price.create("StandardPrice", String.valueOf(standardPrice)).addAttr("currency", currency).addAttr("zero",
                    "false");
            Double salePrice = variant.getSalePrice();
            if (salePrice != null && salePrice > 0d && variant.getSaleStartDate() != null
                    && variant.getSaleEndDate() != null) {
                Element sale = price.create("Sale");
                sale.create("StartDate", sdf.format(AmazonUtils.getUTCTime(variant.getSaleStartDate())));
                sale.create("EndDate", sdf.format(AmazonUtils.getUTCTime(variant.getSaleEndDate())));
                sale.create("SalePrice", String.valueOf(salePrice)).addAttr("currency", currency).addAttr("zero",
                        "false");
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductInventory2Xml(PublishData<AmazonVariantBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Inventory", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonVariantBO> amazonVariants = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonVariant variant : amazonVariants) {
            String sellerSku = variant.getSellerSku();
            Integer quantity = variant.getQuantity();
            Integer fulfillmentLatency = variant.getFulfillmentLatency();
            if (null == quantity && null == fulfillmentLatency) {
                publishData.addErrorSku(sellerSku, "库存数量和备货期不能同时为空");
                continue;
            }

            msgId2SkuMap.put(messageId, sellerSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Inventory
            Element inventory = message.create("Inventory");
            inventory.create("SKU", sellerSku);
            if (null != quantity) {
                inventory.create("Quantity", String.valueOf(quantity));
            }
            if (null != fulfillmentLatency) {
                inventory.create("FulfillmentLatency", String.valueOf(fulfillmentLatency));
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductImage2Xml(PublishData<AmazonVariantBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("ProductImage", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonVariantBO> amazonVariants = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        String imagePath = publishData.getImagePath();
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        Set<String> allImages = new HashSet<>();
        amazonVariants.forEach(variant->{
            String mainImage = variant.getMainImage();
            if (StringUtils.isNotEmpty(mainImage)) {
                allImages.add(mainImage);
            }
            String sampleImage = variant.getSampleImage();
            if (StringUtils.isNotEmpty(sampleImage)) {
                allImages.add(sampleImage);
            }
            List<String> extraImages = variant.getExtraImagesList();
            if (CollectionUtils.isNotEmpty(extraImages)) {
                allImages.addAll(extraImages);
            }
        });
        // 集中处理图片复制
        //Map<String, String> url2NewUrlMap = AmazonUtils.retryCopyImage(null, new ArrayList<>(allImages), imagePath, 2);
        Map<String, String> url2NewUrlMap = AmazonUtils.copyImagesToAliOSS(new ArrayList<>(allImages), imagePath);
        for (AmazonVariantBO amazonVariant : amazonVariants) {
            if (StringUtils.isEmpty(amazonVariant.getMainImage())
                    && StringUtils.isEmpty(amazonVariant.getExtraImages())) {
                publishData.addErrorSku(amazonVariant.getArticleNumber(), "产品图片不能为空");
                continue;
            }

            // 平台货号
            String sellerSku = amazonVariant.getSellerSku();

            // 记录主图
            String imageUrl = url2NewUrlMap.get(amazonVariant.getMainImage());
            if (StringUtils.isNotEmpty(imageUrl)) {
                publishData.addImageUrl(imageUrl);
                generateImageMessage(operationType, root, messageId, sellerSku, "Main", imageUrl);
                msgId2SkuMap.put(messageId, sellerSku);
                messageId++;
            }
            // 记录样品图
            if (StringUtils.isNotEmpty(amazonVariant.getSampleImage())) {
                imageUrl = url2NewUrlMap.get(amazonVariant.getSampleImage());
                if (StringUtils.isNotEmpty(imageUrl)) {
                    publishData.addImageUrl(imageUrl);
                    generateImageMessage(operationType, root, messageId, sellerSku, "Swatch", imageUrl);
                    msgId2SkuMap.put(messageId, sellerSku);
                    messageId++;
                }
            }
            // 记录附图 若选择了附图，则覆盖，若未选择，则忽略
            List<String> extraImagesList = amazonVariant.getExtraImagesList();
            if (CollectionUtils.isNotEmpty(extraImagesList) && extraImagesList.size() <= 8) {
                int extraImagesSize = extraImagesList.size();
                for (int i = 0; i < extraImagesSize; i++) {
                    String url = extraImagesList.get(i);
                    String newUrl = url2NewUrlMap.get(url);
                    if (StringUtils.isNotEmpty(newUrl)) {
                        generateImageMessage(operationType, root, messageId, sellerSku, "PT" + (i + 1), newUrl);
                        msgId2SkuMap.put(messageId, sellerSku);
                        publishData.addImageUrl(newUrl);
                        messageId++;
                    }
                }
                // 附图最多八张，不足八张则将剩余附图删除
                if (extraImagesSize < 8) {
                    for (int i = extraImagesSize; i < 8; i++) {
                        generateImageMessage(OperationType.Delete, root, messageId, sellerSku, "PT" + (i + 1), null);
                        msgId2SkuMap.put(messageId, sellerSku);
                        messageId++;
                    }
                }
            }
        }

        return xmlBuilder.builder();
    }

    private void generateImageMessage(OperationType operationType, Element root, int messageId, String sku,
            String imageType, String imageUrl) {
        // Message
        Element message = root.create("Message");
        message.create("MessageID", String.valueOf(messageId));
        message.create("OperationType", operationType.name());
        // ProductImage
        Element image = message.create("ProductImage");
        image.create("SKU", sku);
        image.create("ImageType", imageType);
        if (operationType != OperationType.Delete) {
            image.create("ImageLocation", imageUrl);
        }
    }

    @Override
    public String transferProduct2Xml(PublishData<AmazonVariantBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Product", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonVariantBO> amazonVariantBOList = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonVariantBO amazonVariantBO : amazonVariantBOList) {
            String parentSku = amazonVariantBO.getSellerSku();
            msgId2SkuMap.put(messageId, parentSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Product
            Element product = message.create("Product");
            product.create("SKU", parentSku);
            if (StringUtils.isNotEmpty(amazonVariantBO.getItemName())) {
                generateDescriptionDataElement(product, amazonVariantBO);
            }
            if (StringUtils.isNotEmpty(amazonVariantBO.getStandardProdcutIdValue())) {
                Element standardProductID = product.create("StandardProductID");
                standardProductID.create("Type", amazonVariantBO.getStandardProdcutIdType());
                standardProductID.create("Value", amazonVariantBO.getStandardProdcutIdValue());
            }
        }

        return xmlBuilder.builder();
    }

    private void generateDescriptionDataElement(Element product, AmazonVariantBO amazonVariantBO) {
        ElementWrapper descDataWrapper = ProductWrapper.fillElementWrapperValues(ProductWrapper.descriptionData,
                null);
        if (StringUtils.isNotEmpty(amazonVariantBO.getItemName())) {
            descDataWrapper.setItemWrapperValue("Title", amazonVariantBO.getItemName());
        }
        if (StringUtils.isNotEmpty(amazonVariantBO.getItemDescription())) {
            descDataWrapper.setItemWrapperValue("Description", amazonVariantBO.getItemDescription());
        }
        // 五点描述
        if (StringUtils.isNotEmpty(amazonVariantBO.getBulletPoint())) {
            descDataWrapper.setItemWrapperValues("BulletPoint",
                    JSON.parseArray(amazonVariantBO.getBulletPoint(), String.class));
        }
        //关键词设置
        if(StringUtils.isNotEmpty(amazonVariantBO.getSearchTerms())){
            descDataWrapper.setItemWrapperValue("SearchTerms",amazonVariantBO.getSearchTerms());
        }
        generateElementByElementWrapper(product, descDataWrapper);
    }

    private void generateElementByElementWrapper(Element element, ElementWrapper elementWrapper) {
        boolean flag = elementWrapper.getIgnore() || elementWrapper.getSelected() || elementWrapper.getRequired();
        if (!flag) {
            return;
        }
        if (!elementWrapper.getIsLeaf()) {
            Element sub = element.create(elementWrapper.getName());
            for (Map.Entry<String, ElementWrapper> entry : elementWrapper.getItems().entrySet()) {
                if (entry.getValue() != null) {
                    generateElementByElementWrapper(sub, entry.getValue());
                }
            }
        }else {
            int occurs = elementWrapper.getOccurs();
            List<AttributeWrapper> attrs = elementWrapper.getAttrs();
            TypeWrapper type = elementWrapper.getType();
            for (int i = 0; i < occurs; i++) {
                String value = AmazonUtils.getListIndexValue(elementWrapper.getValues(), i);
                if (StringUtils.isNotEmpty(value)) {
                    if (type != null && "xsd:normalizedString".equals(type.getName())) {
                        value = AmazonUtils.toHtml(value);
                    }
                    Element sub = element.create(elementWrapper.getName(), value);
                    if (CollectionUtils.isNotEmpty(attrs)) {
                        for (AttributeWrapper attrWrapper : attrs) {
                            String attrValue = AmazonUtils.getListIndexValue(attrWrapper.getValues(), i);
                            if (StringUtils.isNotEmpty(attrValue)) {
                                sub.addAttr(attrWrapper.getName(), attrValue);
                            }
                        }
                    }
                }
            }
        }
    }
}
