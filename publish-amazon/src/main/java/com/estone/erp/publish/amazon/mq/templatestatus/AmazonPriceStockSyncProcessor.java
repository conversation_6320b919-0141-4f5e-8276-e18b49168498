package com.estone.erp.publish.amazon.mq.templatestatus;

import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Amazon价格库存同步处理器
 * 负责价格库存同步相关的处理逻辑
 */
@Slf4j
@Component
public class AmazonPriceStockSyncProcessor {

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;

    /**
     * 创建价格库存同步处理报告
     * 
     * @param template 模版信息
     * @return 创建的处理报告
     */
    public AmazonProcessReport createPriceStockSyncReport(AmazonTemplateBO template) {
        log.info("创建价格库存同步处理报告，模版ID：{}", template.getId());
        if (template.getStepTemplateStatus() != null) {
            return null;
        }
        
        AmazonProcessReport report = new AmazonProcessReport();
        report.setRelationId(template.getId());
        report.setRelationType(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name());
        report.setAccountNumber(template.getSellerId());
        report.setStatusCode("Processing");
        report.setStatus(null);
        report.setCreationDate(new Date());
        report.setFeedType(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name());
        report.setDataValue(template.getSellerSKU());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setCreatedBy(template.getCreatedBy());
        
        amazonProcessReportService.insert(report);
        return report;
    }

    /**
     * 标记价格库存同步报告为超时失败
     * 
     * @param report 处理报告
     * @param timeoutHours 超时小时数
     */
    public void markPriceStockSyncTimeout(AmazonProcessReport report, int timeoutHours) {
        log.info("价格库存同步超时，报告ID：{}，模版ID：{}", report.getId(), report.getRelationId());
        
        report.setStatusCode("Complete");
        report.setStatus(false);
        report.setFinishDate(new Date());
        report.setResultMsg("价格库存同步超时失败（超过" + timeoutHours + "小时）");
        
        amazonProcessReportService.update(report);
    }

    /**
     * 检查价格库存同步是否应该触发
     * 
     * @param template 模版信息
     * @return true-应该触发，false-不需要触发
     */
    public boolean shouldTriggerPriceStockSync(AmazonTemplateBO template) {
        // 这里可以添加业务规则判断是否需要触发价格库存同步
        // 比如检查商品类型、账号配置等
        return true;
    }
} 