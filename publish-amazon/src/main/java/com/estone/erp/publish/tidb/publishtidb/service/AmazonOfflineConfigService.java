package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonUpdateStatusDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.SaleCountRuleConfigDO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.CurrentAccountRequest;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfig;

import java.util.List;
import java.util.function.Predicate;

/**
 * <p>
 * Amazon 下架配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface AmazonOfflineConfigService extends IService<AmazonOfflineConfig> {

    ApiResult<String> saveOrUpdate(AmazonOfflineConfigVO editParam);

    ApiResult<AmazonOfflineConfigVO> editConfig(Integer id);

    ApiResult<List<String>> getCurrentUserAccounts(CurrentAccountRequest currentAccountRequest);

    ApiResult<IPage<AmazonOfflineConfigVO>> search(AmazonOfflineSearchDTO searchParam);

    ApiResult<?> updateStatus(AmazonUpdateStatusDTO requestParam);

    List<Predicate<AmazonAsinSaleCountDO>> transferToSaleCountCompareRules(SaleCountRuleConfigDO saleCountRuleConfig);
}
