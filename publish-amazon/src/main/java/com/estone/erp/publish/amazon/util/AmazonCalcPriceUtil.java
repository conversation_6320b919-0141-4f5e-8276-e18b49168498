package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonCalcPriceRule;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.model.request.AmazonBatchPriceCalculatorResponse;
import com.estone.erp.publish.amazon.model.dto.AmazonCalcPriceBean;
import com.estone.erp.publish.amazon.model.dto.AmazonListingCalcProfitBean;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonCalcPriceRuleService;
import com.estone.erp.publish.amazon.service.AmazonShippingCostModelService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/12/13
 */
@Slf4j
public class AmazonCalcPriceUtil {

    /**
     * 普货标签
     */
    private static final String normalGoods = "normal goods";

    /**
     * 刊登默认价格（总价小于等于运费时候需要获取）
     */
    private static final String PUBLISH_DEFAULT_PRICE = "PUBLISH_DEFAULT_PRICE";

    /**
     * 数据库默认配置的站点 其他站点需要使用汇率转换
     */
    private static final String PUBLISH_DEFAULT_PRICE_SITE = "US";

    /**
     * 数据库默认配置的站点 对应的币种
     */
    private static final String PUBLISH_DEFAULT_PRICE_CURRENCY = "USD";


    static AmazonCalcPriceRuleService amazonCalcPriceRuleService = SpringUtils.getBean(AmazonCalcPriceRuleService.class);
    static AmazonAccountRelationService amazonAccountRelationService = SpringUtils.getBean(AmazonAccountRelationService.class);
    static AmazonShippingCostModelService amazonShippingCostModelService = SpringUtils.getBean(AmazonShippingCostModelService.class);
    static AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
    static AmazonTemplateBuilderHelper amazonTemplateBuilderHelper = SpringUtils.getBean(AmazonTemplateBuilderHelper.class);

    /**
     * 根据账号配置算价
     *  <a href="http://************:8080/browse/ES-6467">店铺默认算价规则</a>
     * @param calcPriceBean
     * @return
     * @throws Exception
     */
    public static List<AmazonBatchPriceCalculatorResponse> calcPrice(AmazonCalcPriceBean calcPriceBean, AmazonAccountRelation amazonAccountRelation) throws Exception {
        if(null == calcPriceBean) {
            throw new Exception("算价对象为空！");
        }

        String accountNumber = calcPriceBean.getAccountNumber();
        List<String> articleNumbers = calcPriceBean.getArticleNumbers();
        Double grossProfitRate = calcPriceBean.getGrossProfitRate();
        if(StringUtils.isBlank(accountNumber) || CollectionUtils.isEmpty(articleNumbers) || null == grossProfitRate) {
            throw new BusinessException("账号,毛利润,货号集合不可以为空！");
        }

        if (Boolean.TRUE.equals(calcPriceBean.getIsAppointShippingMethod())) {
            // 使用 calcPriceBean 对象中指定的物流方式
            calcPriceBean.setUseShippingGroup(true);
            log.debug("店铺:{}, sku:{},指定的物流:{}", accountNumber, calcPriceBean.getArticleNumbers().get(0), calcPriceBean.getShippingMethodCode());
            return amazonCalcPrice(calcPriceBean);

        }

        // 根据店铺配置算价规则进行算价
        if (amazonAccountRelation == null) {
            amazonAccountRelation = amazonAccountRelationService.selectByAccount(accountNumber);
        }
        if (amazonAccountRelation == null) {
            throw new BusinessException(accountNumber + "未查询到账号配置");
        }
        // 店铺算价规则
        List<AmazonCalcPriceRule> amazonCalcPriceRules = amazonCalcPriceRuleService.selectByAccountNumber(calcPriceBean.getAccountNumber());
        if (CollectionUtils.isEmpty(amazonCalcPriceRules)) {
            // 刊登价格配置物流算价
            calcPriceBean.setShippingMethodCode(amazonAccountRelation.getLogisticsCode());
            calcPriceBean.setUseShippingGroup(true);
            log.debug("店铺:{}, sku:{},刊登价格配置物流算价:{}", accountNumber, calcPriceBean.getArticleNumbers().get(0), calcPriceBean.getShippingMethodCode());
            return amazonCalcPrice(calcPriceBean);
        }

        // 产品标签匹配试算物流
        String skuTagStr = ProductUtils.getSkuProductTagWithSameSpu(calcPriceBean.getArticleNumbers());
        if (StringUtils.isBlank(skuTagStr)) {
            // 单品获取失败使用 套装/组合产品
            skuTagStr = amazonTemplateBuilderHelper.matchSkuTags(articleNumbers, SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        }

        String calcLogisticsCode = matchCalcLogisticsWithSkuTag(skuTagStr, amazonCalcPriceRules);
        if (StringUtils.isBlank(calcLogisticsCode)) {
            log.error("店铺:{}, sku:{},skuTag:%s:{}, 试算物流匹配失败", accountNumber, calcPriceBean.getArticleNumbers().get(0), skuTagStr);
            throw new BusinessException(String.format("%s, 试算物流匹配失败, skuTag:%s", accountNumber, skuTagStr));
        }

        // 根据试算物流计算总价
        log.debug("店铺:{}, sku:{},试算物流:{}", accountNumber, calcPriceBean.getArticleNumbers().get(0), calcLogisticsCode);
        calcPriceBean.setShippingMethodCode(calcLogisticsCode);
        calcPriceBean.setUseShippingGroup(false);
        List<AmazonBatchPriceCalculatorResponse> calcResult = amazonCalcPrice(calcPriceBean);
        // 试算物流全部失败 则直接返回
        List<AmazonBatchPriceCalculatorResponse> successCalcResult = calcResult.stream()
                .filter(BatchPriceCalculatorResponse::getIsSuccess)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(successCalcResult)) {
            return calcResult;
        }

        // 获取试算成功中的最高价格为总价
        Double maxPrice = calcResult.stream()
                .filter(BatchPriceCalculatorResponse::getIsSuccess)
                .map(BatchPriceCalculatorResponse::getForeignPrice)
                .max(Double::compareTo).orElseGet(() -> null);
        if (maxPrice == null) {
            log.error("店铺:{}, sku:{},试算物流:{}, 试算物流获取总价失败:{}", accountNumber, calcPriceBean.getArticleNumbers().get(0), calcLogisticsCode, JSON.toJSONString(calcResult));
            throw new BusinessException(String.format("%s, 试算物流获取总价失败, skuTag:%s, calcLogisticsCode:%s", accountNumber, skuTagStr, calcLogisticsCode));
        }

        // 根据标签 + 总价 + 试算物流 匹配优选物流
        AmazonCalcPriceRule amazonCalcPriceRule = getAmazonCalcPriceRule(maxPrice, skuTagStr, calcLogisticsCode, amazonCalcPriceRules);
        if (amazonCalcPriceRule == null || StringUtils.isBlank(StringUtils.defaultIfBlank(amazonCalcPriceRule.getPreferLogistics(), amazonCalcPriceRule.getAlternateLogistics()))) {
            log.error("店铺:{}, sku:{},试算物流:{}, 总价:{}, 产品标签:{}, 匹配优选物流失败", accountNumber, calcPriceBean.getArticleNumbers().get(0), calcLogisticsCode, maxPrice, skuTagStr);
            throw new BusinessException(String.format("%s, 匹配优选物流失败, skuTag:%s, 总价:%s", accountNumber, skuTagStr, maxPrice));
        }

        String preferLogistics = StringUtils.defaultIfBlank(amazonCalcPriceRule.getPreferLogistics(), amazonCalcPriceRule.getAlternateLogistics());
        log.debug("店铺:{}, sku:{}, 总价:{}, 优选物流方式:{}", accountNumber, calcPriceBean.getArticleNumbers().get(0), maxPrice, preferLogistics);
        calcPriceBean.setShippingMethodCode(preferLogistics);
        calcPriceBean.setUseShippingGroup(true);
        List<AmazonBatchPriceCalculatorResponse> calculatorResponses = amazonCalcPrice(calcPriceBean);
        boolean allSuccess = calculatorResponses.stream()
                .allMatch(AmazonBatchPriceCalculatorResponse::getIsSuccess);
        if (allSuccess) {
            return calculatorResponses;
        }

        // 失败的sku使用备选物流再次算价
        Map<Boolean, List<AmazonBatchPriceCalculatorResponse>> calculatorMap = calculatorResponses.stream()
                .collect(Collectors.groupingBy(BatchPriceCalculatorResponse::getIsSuccess));
        List<AmazonBatchPriceCalculatorResponse> failSkus = calculatorMap.get(Boolean.FALSE);
        List<String> failSkuList = failSkus.stream().map(BatchPriceCalculatorResponse::getArticleNumber).collect(Collectors.toList());
        log.debug("店铺:{}, sku:{}, 备选物流方式:{}", accountNumber, JSON.toJSONString(failSkuList), amazonCalcPriceRule.getAlternateLogistics());
        calcPriceBean.setShippingMethodCode(amazonCalcPriceRule.getAlternateLogistics());
        calcPriceBean.setArticleNumbers(failSkuList);
        calcPriceBean.setUseShippingGroup(true);
        List<AmazonBatchPriceCalculatorResponse> alternateLogisticsCalculators = amazonCalcPrice(calcPriceBean);
        List<AmazonBatchPriceCalculatorResponse> successResponse = calculatorMap.get(Boolean.TRUE);
        if (CollectionUtils.isEmpty(successResponse)) {
            return alternateLogisticsCalculators;
        }
        successResponse.addAll(alternateLogisticsCalculators);
        return successResponse;
    }

    /**
     * 根据sku标签匹配试算物流
     * @param skuTagStr             sku产品标签
     * @param amazonCalcPriceRules  店铺算价规则
     */
    private static String matchCalcLogisticsWithSkuTag(String skuTagStr, List<AmazonCalcPriceRule> amazonCalcPriceRules) {
        // 产品标签为空 则默认为普货
        List<String> skuTagList = CommonUtils.splitList(skuTagStr, ",");
        if(CollectionUtils.isEmpty(skuTagList)) {
            skuTagList.add(normalGoods);
        }

        // 根据sku标签匹配试算物流
        AmazonCalcPriceRule calcPriceRule = amazonCalcPriceRules.stream()
                .filter(rule -> CollectionUtils.isNotEmpty(rule.getLabelList()))
                .filter(rule -> rule.getLabelList().stream().anyMatch(skuTagList::contains))
                .findFirst().orElseGet(() -> null);
        if (calcPriceRule != null) {
            return calcPriceRule.getCalcLogistics();
        }

        // 标签未匹配上的,取标签为空的算价规则试算物流
        return amazonCalcPriceRules.stream()
                .filter(rule -> CollectionUtils.isEmpty(rule.getLabelList()))
                .map(AmazonCalcPriceRule::getCalcLogistics)
                .findFirst().orElseGet(() -> null);
    }

    public static List<AmazonListingCalcProfitBean> initPriceCalculatorRequest(List<EsAmazonProductListing> esAmazonProductListingList, AmazonProductListingDto amazonProductListingDto) throws Exception {
        if(CollectionUtils.isEmpty(esAmazonProductListingList) || null == esAmazonProductListingList) {
            return null;
        }

        String accountNumber = esAmazonProductListingList.get(0).getAccountNumber();
        String site = esAmazonProductListingList.get(0).getSite();
        Boolean isAppointShippingMethod = amazonProductListingDto.getIsAppointShippingMethod();

        Map<String, String> sonSkuTagCodeMap = null;
        List<AmazonCalcPriceRule> amazonCalcPriceRules = null;
        // 没有指定物流则使用账号配置算价物流方法
        if(BooleanUtils.isNotTrue(isAppointShippingMethod)) {
            amazonCalcPriceRules = amazonCalcPriceRuleService.selectByAccountNumber(accountNumber);
            if(CollectionUtils.isEmpty(amazonCalcPriceRules)) {
                throw new Exception("店铺算价配置为空，请优先配置或指定物流!");
            }

            List<String> articleNumbers = esAmazonProductListingList.stream().map(EsAmazonProductListing::getArticleNumber).collect(Collectors.toList());

            // 获取产品标签 冠通 按普货处理
            String[] fields = {"mainSku", "sonSku", "tagCode", "tag"};
            List<ProductInfo> productInfoList = ProductUtils.findSkuInfosByEs(articleNumbers, fields);
            if(CollectionUtils.isEmpty(productInfoList)) {
                throw new Exception("产品es未找到 请检查SKU!");
            }
            //sonSkuTagCodeMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getSonSku, ProductInfo::getEnTag));
            sonSkuTagCodeMap = productInfoList.stream().collect(Collectors.toMap(productInfo -> productInfo.getSonSku(),productInfo -> productInfo.getEnTag()+""));
        }

        int size = esAmazonProductListingList.size();
        List<AmazonListingCalcProfitBean> listingCalcProfitBeanList = new ArrayList<>(size);
        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
            AmazonListingCalcProfitBean listingCalcProfitBean = new AmazonListingCalcProfitBean();
            BatchPriceCalculatorRequest batchPriceCalculatorRequest = new BatchPriceCalculatorRequest();
            listingCalcProfitBean.setRequest(batchPriceCalculatorRequest);
            listingCalcProfitBean.setEsAmazonProductListing(esAmazonProductListing);
            listingCalcProfitBeanList.add(listingCalcProfitBean);

            String articleNumber = esAmazonProductListing.getArticleNumber();
            batchPriceCalculatorRequest.setSite(site);
            batchPriceCalculatorRequest.setCountryCode(site);
            batchPriceCalculatorRequest.setSaleChannel(Platform.Amazon.name());
            batchPriceCalculatorRequest.setArticleNumber(articleNumber);
            batchPriceCalculatorRequest.setQuantity(1);
            Double price = esAmazonProductListing.getPrice();
            Double shippingCost = esAmazonProductListing.getShippingCost();
            price = null == price ? 0.0 : price;
            shippingCost = null == shippingCost ? 0.0 : shippingCost;
            Double totlePrice = BigDecimal.valueOf(shippingCost).add(BigDecimal.valueOf(price)).doubleValue();
            batchPriceCalculatorRequest.setSalePrice(totlePrice);
            String shippingMethodCode = null;
            if(BooleanUtils.isTrue(isAppointShippingMethod)) { // 指定物流方式
                shippingMethodCode = amazonProductListingDto.getShippingMethodCode();
            } else { // 根据价格标签获取价格配置物流方式
                String skuTagStr = MapUtils.isNotEmpty(sonSkuTagCodeMap) ? sonSkuTagCodeMap.get(articleNumber) : null;
                AmazonCalcPriceRule amazonCalcPriceRule = getAmazonCalcPriceRule(totlePrice, skuTagStr, null, amazonCalcPriceRules);
                shippingMethodCode = StringUtils.defaultIfBlank(amazonCalcPriceRule.getPreferLogistics(), amazonCalcPriceRule.getAlternateLogistics());
            }
            batchPriceCalculatorRequest.setShippingMethod(shippingMethodCode);
        }

        return listingCalcProfitBeanList;
    }

    /**
     * 亚马逊调用试算器算价
     * @param calcPriceBean
     * @return
     * @throws Exception
     */
    public static List<AmazonBatchPriceCalculatorResponse> amazonCalcPrice(AmazonCalcPriceBean calcPriceBean) throws Exception {
        if(null == calcPriceBean || CollectionUtils.isEmpty(calcPriceBean.getArticleNumbers())) {
            throw new Exception("货号集合不可以为空！");
        }

        if(StringUtils.isBlank(calcPriceBean.getShippingMethodCode())) {
            throw new Exception("物流方式不可以为空！");
        }

        List<String> articleNumbers = calcPriceBean.getArticleNumbers();
        List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = new ArrayList<>(calcPriceBean.getArticleNumbers().size());
        BatchPriceCalculatorRequest batchPriceCalculatorRequest = null;
        for (String sku : articleNumbers) {
            batchPriceCalculatorRequest = new BatchPriceCalculatorRequest();
            batchPriceCalculatorRequest.setId(calcPriceBean.getId());
            batchPriceCalculatorRequest.setSite(calcPriceBean.getSite());
            batchPriceCalculatorRequest.setCountryCode(batchPriceCalculatorRequest.getSite());
            batchPriceCalculatorRequest.setGrossProfitRate(calcPriceBean.getGrossProfitRate());
            batchPriceCalculatorRequest.setShippingMethod(calcPriceBean.getShippingMethodCode());
            batchPriceCalculatorRequest.setSaleChannel(Platform.Amazon.name());
            batchPriceCalculatorRequest.setArticleNumber(sku);
            batchPriceCalculatorRequest.setQuantity(1);
            batchPriceCalculatorRequestList.add(batchPriceCalculatorRequest);
        }

        // 请求
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequestList, calcPriceBean.getRestNum());
        if(!listApiResult.isSuccess() || StringUtils.isNotBlank(listApiResult.getErrorMsg())) {
            throw new Exception("算价失败" + listApiResult.getErrorMsg());
        }

        // 转换对象
        List<AmazonBatchPriceCalculatorResponse> responseList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(listApiResult.getResult())) {
            listApiResult.getResult().forEach(o->{
                AmazonBatchPriceCalculatorResponse amazonBatchPriceCalculatorResponse = new AmazonBatchPriceCalculatorResponse(o);
                amazonBatchPriceCalculatorResponse.setShippingMethodCode(calcPriceBean.getShippingMethodCode());
                responseList.add(amazonBatchPriceCalculatorResponse);
            });
        }

        // 设置运费模板
        setShipingCostModel(responseList, calcPriceBean);

        return responseList;
    }

    /**
     * 根据价格 标签 试算物流 匹配合适的算价规则对应优先物流方式
     * @return
     */
    public static AmazonCalcPriceRule getAmazonCalcPriceRule(Double price, String skuTagStr, String calcLogisticsCode, List<AmazonCalcPriceRule> amazonCalcPriceRules) {
        if(null == price || CollectionUtils.isEmpty(amazonCalcPriceRules)) {
            return null;
        }

        // 产品标签为空 则默认为普货
        List<String> skuTagList = CommonUtils.splitList(skuTagStr, ",");
        if(CollectionUtils.isEmpty(skuTagList)) {
            skuTagList.add(normalGoods);
        }

        // 标签不为空且符合的规则
        List<AmazonCalcPriceRule> calcPriceRules = amazonCalcPriceRules.stream()
                .filter(rule->{
                    if(CollectionUtils.isEmpty(rule.getLabelList())) {
                        return false;
                    }
                    for (String tag : skuTagList) {
                        if(rule.getLabelList().contains(tag)) {
                            return true;
                        }
                    }
                   return false;
                })
                .filter(rule->(null == rule.getFromPrice() || rule.getFromPrice() <= price) && (null == rule.getToPrice() || price < rule.getToPrice()))
                .filter(rule-> {
                    if (StringUtils.isBlank(calcLogisticsCode)) {
                        return true;
                    }
                    return StringUtils.equals(calcLogisticsCode, rule.getCalcLogistics());
                })
                .collect(Collectors.toList());

        // 标签匹配不上 则使用标签为空的规则
        if(CollectionUtils.isEmpty(calcPriceRules)) {
            calcPriceRules = amazonCalcPriceRules.stream()
                    .filter(rule->CollectionUtils.isEmpty(rule.getLabelList()))
                    .filter(rule->(null == rule.getFromPrice() || rule.getFromPrice() <= price) && (null == rule.getToPrice() || price < rule.getToPrice()))
                    .filter(rule-> {
                        if (StringUtils.isBlank(calcLogisticsCode)) {
                            return true;
                        }
                        return StringUtils.equals(calcLogisticsCode, rule.getCalcLogistics());
                    })
                    .collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(calcPriceRules)) {
           return calcPriceRules.get(0);
        }

        return null;
    }

    /**
     * 设置运费模板
     * @param responseList 数据不全时候不设置
     * @param calcPriceBean UseShippingGroup 为false 不设置
     */
    private static void setShipingCostModel(List<AmazonBatchPriceCalculatorResponse> responseList, AmazonCalcPriceBean calcPriceBean) throws Exception {
        if(CollectionUtils.isEmpty(responseList) || null == calcPriceBean || !calcPriceBean.getUseShippingGroup() || StringUtils.isBlank(calcPriceBean.getAccountNumber()) || Boolean.TRUE.equals(calcPriceBean.getUseTotalPrice())) {
            return;
        }

        List<AmazonShippingCostModel> amazonShippingCostModels = amazonShippingCostModelService.selectByAccountNumber(calcPriceBean.getAccountNumber());

        // 过滤名称或运费为空数据 使用默认为是的 如果没有默认为是的 则运费默认为0
        amazonShippingCostModels = amazonShippingCostModels.stream()
                .filter(o -> StringUtils.isNotBlank(o.getShippingGroup()) && null != o.getShippingCost() && BooleanUtils.isTrue(o.getIsDefault())).collect(Collectors.toList());

        AmazonShippingCostModel amazonShippingCostModel = CollectionUtils.isNotEmpty(amazonShippingCostModels) ? amazonShippingCostModels.get(0) : null;
        for (AmazonBatchPriceCalculatorResponse response : responseList) {
            // 试算器算出的总价
            String site = response.getSite();
            Double foreignPrice = response.getForeignPrice();
            if(null == foreignPrice) {
                continue;
            }
            response.setForeignTotlePrice(foreignPrice);

            // 未找到运费模板 运费设置为0
            if(null == amazonShippingCostModel) {
                response.setShippingCost(0.0);
                continue;
            }

            Double shippingCost = amazonShippingCostModel.getShippingCost();
            response.setShippingGroup(amazonShippingCostModel.getShippingGroup());
            response.setShippingCost(shippingCost);

            Double price = BigDecimal.valueOf(foreignPrice).subtract(BigDecimal.valueOf(shippingCost)).doubleValue();
            if(price <= 0) {
                try{
                    // 使用默认 总价-默认运费为负数，则价格就默认取1.99美金（做成配置）这个值，其他站点按汇率换算
                    // 使用默认价格时  总价不变（仍为试算器算出总价 否则前端页面换运费模板后价格有问题）
                    price = getAmazonPublishDefaultPrice(site);
                } catch (Exception e) {
                    throw new Exception(String.format("总价减去运费模板运费后为负数获取默认价格时异常%s", e.getMessage()));
                }
            }
            response.setForeignPrice(price);
        }
    }

    /**
     * 获取刊登的默认价格
     * 美元获取系统配置 其他币种需要使用美元转人民币再转其他币种 计算后存入redis 缓存12小时
     * @return
     */
    public static Double getAmazonPublishDefaultPrice(String site) throws Exception {
        String key = RedisConstant.PUBLISH_SYSTEM_NAME + ":" + SaleChannel.CHANNEL_AMAZON + ":" + PUBLISH_DEFAULT_PRICE + ":";

        // 查询redis 存在则直接返回
        String value = PublishRedisClusterUtils.get(key + site);
        if(StringUtils.isNotBlank(value)) {
            return Double.parseDouble(value);
        }

        // 不存在先查询US站点 数据库只存了US站点最低值  其他站点需要调用汇率转换
        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, PUBLISH_DEFAULT_PRICE, PUBLISH_DEFAULT_PRICE_SITE);
        if(null == systemParam || StringUtils.isBlank(systemParam.getParamValue())) {
            throw new Exception("未配置默认价格");
        }

        value = systemParam.getParamValue();
        Double doubleValue = Double.parseDouble(value);
        PublishRedisClusterUtils.set(key + PUBLISH_DEFAULT_PRICE_SITE, value, 12, TimeUnit.HOURS);

        // 非US站点需要转换汇率
        if(!PUBLISH_DEFAULT_PRICE_SITE.equalsIgnoreCase(site)) {
            Double cnyPrice = null;
            ApiResult<Double> cnyRateResult = PriceCalculatedUtil.getExchangeRate(PUBLISH_DEFAULT_PRICE_CURRENCY, CurrencyConstant.CNY);
            if(cnyRateResult.isSuccess()){
                Double cnyRate = cnyRateResult.getResult();
                cnyPrice =  doubleValue * cnyRate;
            }else{
                throw new Exception(String.format("%s获取汇率失败：%s", PUBLISH_DEFAULT_PRICE_CURRENCY, cnyRateResult.getErrorMsg()));
            }

            String currency = amazonConstantMarketHelper.getCurrencyByMarketplace(site);
            if(StringUtils.isBlank(currency)) {
                throw new Exception(String.format("未配置%S站点币种", currency));
            }
            ApiResult<Double> foreignRateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.CNY, currency);
            if(foreignRateResult.isSuccess()){
                Double foreignRate = foreignRateResult.getResult();
                doubleValue = cnyPrice * foreignRate;
            }else{
                throw new Exception(String.format("%s获取汇率失败：%s", currency, foreignRateResult.getErrorMsg()));
            }

            PublishRedisClusterUtils.set(key + site, doubleValue, 12, TimeUnit.HOURS);
        }

        return doubleValue;
    }
}
