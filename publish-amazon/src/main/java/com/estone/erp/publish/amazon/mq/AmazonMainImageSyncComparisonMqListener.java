package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.LogPrintUtil;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMainImageSimService;
import com.estone.erp.publish.amazon.mq.model.AmazonMainImageSyncComparisonData;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.nio.charset.StandardCharsets;

@Slf4j
public class AmazonMainImageSyncComparisonMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonMainImageSimService amazonMainImageSimService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean isSuccess = false;
        String accountNumber = null;
        try {
            AmazonMainImageSyncComparisonData data = JSON.parseObject(body, AmazonMainImageSyncComparisonData.class);
            accountNumber = data.getAccountNumber();
            if (data.isFull()) {
                amazonMainImageSimService.doImageSim(true, data.getAccountNumber(), data.getSkuList(), null, data.getEndDate(), false);
                AmazonAccountRelation accountRelation = new AmazonAccountRelation();
                accountRelation.setAccountNumber(data.getAccountNumber());
                accountRelation.setAccountCountry(data.getAccountCountry());
                amazonMainImageSimService.doErrorSim(accountRelation);
                isSuccess = true;
            } else {
                String openDate = data.getOpenDate();
                String endDate = data.getEndDate();
                if (StringUtils.isBlank(openDate) || StringUtils.isBlank(endDate)) {
                    return;
                }
                amazonMainImageSimService.doImageSim(false, data.getAccountNumber(), data.getSkuList(), openDate, endDate, false);
                isSuccess = true;
            }
        } catch (Exception e) {
            log.error("AmazonMainImageSyncComparisonMqListener异常：" + LogPrintUtil.getMinimumReverseStackCause(e));
        }
        stopWatch.stop();
        log.info("AmazonMainImageSyncComparisonMqListener: accountNumber:{}, 一共耗时： {}s", accountNumber, stopWatch.getLastTaskTimeMillis() / 1000);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

}
