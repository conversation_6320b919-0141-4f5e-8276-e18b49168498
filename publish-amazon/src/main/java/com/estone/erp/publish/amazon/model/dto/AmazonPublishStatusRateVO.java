package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.controller.AmazonPublishStatusKanbanController;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanban;
import com.estone.erp.publish.amazon.model.AmazonPublishStatusKanban;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Amazon 刊登成功率
 *
 * <AUTHOR>
 * @date 2023-12-20 16:23
 */
@Data
public class AmazonPublishStatusRateVO {

    /**
     * 日期
     */
    private String date;

    /**
     * 刊登成功率数据
     */
    private List<PublishStatusData> publishStatusData;

    /**
     * 错误分类数据
     */
    private List<ErrorTypeData> errorTypeData;


    @Data
    static class ErrorTypeData {
        /**
         * 错误类型
         */
        private String type;

        /**
         * 数量
         */
        private Integer count;

    }


    @Data
    static class PublishStatusData {
        /**
         * 类型 ALL, ADMIN, SALE
         */
        private String type;

        /**
         * 成功数
         */
        private Integer successCount = 0;

        /***
         * 失败数
         */
        private Integer failCount = 0;
    }

    public void setKanbanData(List<AmazonPublishStatusKanban> kanbans) {
        if (CollectionUtils.isEmpty(kanbans)) {
            setPublishStatusData(Collections.emptyList());
            return;
        }
        Map<Integer, List<AmazonPublishStatusKanban>> ruleDataMap = kanbans.stream()
                .collect(Collectors.groupingBy(AmazonPublishStatusKanban::getPublishRole));


        List<PublishStatusData> dataList = new ArrayList<>();
        ruleDataMap.forEach((rule, data) -> {
            PublishStatusData publishStatusData = new PublishStatusData();
            PublishRoleEnum enumByCode = PublishRoleEnum.getEnumByCode(rule);
            publishStatusData.setType(enumByCode.name());
            int successNumber = data.stream()
                    .filter(datum -> AmaoznPublishStatusEnum.PUBLISH_SUCCESS.isTrue(datum.getPublishStatus()))
                    .map(AmazonPublishStatusKanban::getCountNumber).mapToInt(Integer::intValue).sum();

            int failNumber = data.stream()
                    .filter(datum -> AmaoznPublishStatusEnum.PUBLISH_FAIL.isTrue(datum.getPublishStatus()))
                    .map(AmazonPublishStatusKanban::getCountNumber).mapToInt(Integer::intValue).sum();

            publishStatusData.setSuccessCount(successNumber);
            publishStatusData.setFailCount(failNumber);
            dataList.add(publishStatusData);
        });

        long successNumber = dataList.stream().map(PublishStatusData::getSuccessCount).mapToInt(Integer::intValue).sum();
        long failNumber = dataList.stream().map(PublishStatusData::getFailCount).mapToInt(Integer::intValue).sum();
        PublishStatusData publishStatusData = new PublishStatusData();
        publishStatusData.setType("ALL");
        publishStatusData.setSuccessCount(Long.valueOf(successNumber).intValue());
        publishStatusData.setFailCount(Long.valueOf(failNumber).intValue());

        dataList.add(publishStatusData);

        this.setPublishStatusData(dataList);
    }

    public void setFailTypeKanbanData(List<AmazonPublishFailTypeKanban> failTypeKanbans) {
        if (CollectionUtils.isEmpty(failTypeKanbans)) {
            setErrorTypeData(Collections.emptyList());
            return;
        }
        Map<String, Integer> failTypeMap = failTypeKanbans.stream()
                .collect(Collectors.toMap(AmazonPublishFailTypeKanban::getErrorType,
                        AmazonPublishFailTypeKanban::getCountNumber, (o1, o2) -> o1));

        List<ErrorTypeData> errorTypeDataList = AmazonPublishStatusKanbanController.BASIE_ERROR_TYPE.stream().map(errorType -> {
            ErrorTypeData errorTypeData = new ErrorTypeData();
            errorTypeData.setType(errorType);
            Integer data = failTypeMap.getOrDefault(errorType, 0);
            errorTypeData.setCount(data);
            return errorTypeData;
        }).collect(Collectors.toList());

        setErrorTypeData(errorTypeDataList);
    }
}
