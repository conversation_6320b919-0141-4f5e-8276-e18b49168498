package com.estone.erp.publish.amazon.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-09 16:05
 */
@Data
public class ReportSolutionResponseDO implements Serializable {
    private static final long serialVersionUID = -1409674624755151472L;

    private String msg;
    @JsonProperty("report_solution")
    private List<String> reportSolution;
    private List<SolutionInfoDTO> response;

    @Data
    public static class SolutionInfoDTO {

        /**
         * id
         */
        private Integer id;

        /**
         * 解决方案
         */
        private String label;

        /**
         * 问题类型
         */
        @JsonProperty("solution_type")
        private String solutionType;
    }
}
