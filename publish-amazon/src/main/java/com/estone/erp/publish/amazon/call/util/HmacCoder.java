package com.estone.erp.publish.amazon.call.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * hmac加密工具类
 * 
 * <AUTHOR>
 *
 */
public class HmacCoder {
    public static final String SHA1 = "HmacSHA1";

    public static final String SHA256 = "HmacSHA256";

    /**
     * 展示了一个生成指定算法密钥的过程 初始化HMAC密钥
     * 
     * @return
     * 
     * @throws Exception
     */
    public static String initMacKey(String algorithm) throws Exception {
        // 得到一个 指定算法密钥的密钥生成器
        KeyGenerator keyGenerator = KeyGenerator.getInstance(algorithm);
        // 生成一个密钥
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.encodeBase64String(secretKey.getEncoded());
    }

    /**
     * 使用 HMAC-SHA1 签名方法对对encryptText进行签名
     * 
     * @param encryptText 被签名的字符串
     * @param encryptKey 密钥
     * @return
     * @throws Exception
     */
    public static byte[] encrypt(String algorithm, byte[] text, byte[] key) throws Exception {
        // 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(key, algorithm);
        // 生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(algorithm);
        // 用给定密钥初始化 Mac 对象
        mac.init(secretKey);

        // 完成 Mac 操作
        return mac.doFinal(text);
    }
}
