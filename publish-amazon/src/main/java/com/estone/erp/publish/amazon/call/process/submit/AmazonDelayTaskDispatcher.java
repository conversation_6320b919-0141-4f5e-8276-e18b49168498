package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.common.model.ERPInvoker;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;

import java.util.concurrent.DelayQueue;
import java.util.concurrent.TimeUnit;

/**
 * 
 * @Description: amazon延时任务转发线程类
 * 
 * @ClassName: AmazonDelayTaskDispatcher
 * @Author: Kevin
 * @Date: 2019/03/28
 * @Version: 0.0.1
 */
@Slf4j
public class AmazonDelayTaskDispatcher extends Thread {

    /**
     * 任务等待超时时间，毫秒值
     */
    private int timeout;

    /**
     * 队列为空时的等待时间
     */
    private long sleepTimeIfEmpty;

    /**
     * 延时队列
     */
    private DelayQueue<DelayTask> delayQueue;

    /**
     * 判断队列大小
     * @return
     */
    public int delayQueueSize(){
        if(delayQueue != null){
            return delayQueue.size();
        }
        return 0;
    }

    /**
     * 
     * @Constructor: 构造器
     *
     * @param timeout 超时时间，毫秒值
     * @param sleepTimeIfEmpty 队列为空时的等待时间
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public AmazonDelayTaskDispatcher(int timeout, long sleepTimeIfEmpty) {
        Asserts.check(timeout > 0, "timeout must be greater than 0.");
        Asserts.check(sleepTimeIfEmpty > 0, "sleepTimeIfEmpty must be greater than 0.");
        setName("amazon-delay-task-dispacher");
        this.timeout = timeout;
        this.sleepTimeIfEmpty = sleepTimeIfEmpty;
        this.delayQueue = new DelayQueue<>();
    }

    /**
     * 
     * @Description: 添加延时任务
     *
     * @param delay 延时时间
     * @param callBack 回调函数
     * @return 是否添加成功
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public boolean addDelayTask(long delay, ERPInvoker callBack) {
        DelayTask task = new DelayTask(delay, callBack);
        return delayQueue.offer(task);
    }

    @Override
    public void run() {
        long startTime = System.currentTimeMillis();
        while (true) {
            try {
                // 等待任务设置0.2秒超时
                DelayTask task = delayQueue.poll(timeout, TimeUnit.MILLISECONDS);
                // 任务为空，则表示队列为空，继续线程等待2秒再检查
                if (task == null) {
                    AmazonUtils.sleep(sleepTimeIfEmpty);
                    continue;
                }

                // 添加延时任务到线程池中
                AmazonExecutors.submitSubmitFeedProgress((rsp) -> {
                    task.getCallBack().invoke();
                });
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            finally {
                long currentTime = System.currentTimeMillis();
                // 每隔60秒打印一次延迟队列的信息
                if ((currentTime - startTime) > 60 * 1000) {
                    startTime = currentTime;
                    log.warn("amazon delay task queue size: " + delayQueue.size());
                }
            }
        }
    }
}
