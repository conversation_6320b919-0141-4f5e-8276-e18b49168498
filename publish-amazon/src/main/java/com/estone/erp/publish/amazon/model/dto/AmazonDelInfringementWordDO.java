package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18 9:38
 */
@Data
public class AmazonDelInfringementWordDO {

    /**
     * 店铺
     */
    @NotBlank(message = "店铺不能为空")
    private String accountNumber;


    /**
     * site
     */
    @NotBlank(message = "站点不能为空")
    private String site;

    /**
     * 待删除文本
     */
    @NotEmpty(message = "待删除文本不能为空")
    private List<String>  sourceTexts;

    /**
     * 文案类型
     */
    private Integer wenAnType;

    /**
     * sonSku
     */
    private String sonSku;
}
