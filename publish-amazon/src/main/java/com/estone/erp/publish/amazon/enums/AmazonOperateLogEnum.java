package com.estone.erp.publish.amazon.enums;

/**
 * <AUTHOR>
 * @date 2021/2/4 15:14
 * @description
 */
public enum AmazonOperateLogEnum {
    // Amazon admin范本
    ADMIN_TEMPLATE,

    // json Amazon admin范本
    JSON_ADMIN_TEMPLATE,

    // 修改店铺配置
    UPDATE_ACCOUNT_RELATION_CONFIG,

    // 修改推荐规则
    UPDATE_RECOMMEND,

    // 修改算价规则
    UPDATE_CALC_PRICE_RULE,

    // 修改分类类型模板配置
    UPDATE_PRODUCT_TYPE_TEMPLATE,

    // 修改分类类型模板属性
    UPDATE_PRODUCT_TYPE_TEMPLATE_ATTR,

    // 修改技术部处理模板必填属性
    UPDATE_PRODUCT_TYPE_SYSTEM_ATTR,

    // 修改商品属性配置
    UPDATE_PRODUCT_TYPE_PROPERTY_TYPE,

    // 修改主管可刊登分类配置
    UPDATE_CATEGORY_OPERATIONS_TEAM_CONFIG,

    // 修改刊登分类次数配置
    UPDATE_CATEGORY_OPERATIONS_PUBLISH_CONFIG,

    // 模版必填属性配置新
    TEMPLATE_REQUIRED_ATTR_CONFIG,
    ;
}
