package com.estone.erp.publish.config;

import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/4 15:08
 * @description
 */
@Slf4j
@Getter
@Setter
@Deprecated
//@Configuration
//@ConfigurationProperties(prefix = "hbase.zookeeper")
//@MapperScan(basePackages = "com.estone.erp.publish.amazon.hbase.dao", sqlSessionFactoryRef= "phoenixSqlSessionFactory")
public class PhoenixForMybatisConfiguration extends AbstractDataSourceConfig {

    /*private String host;

    private Integer port;

    @Bean
    public DataSource phoenixDataSource() {
        log.info("===========加载数据源:phoenix-hbase========");
        DruidDataSource druidDataSource = new DruidDataSource();

        //设置Phoenix驱动
        druidDataSource.setDriverClassName(Driver.class.getName());
        // ******************************************************************
        // hbase.zookeeper.host: hadoop101
        // hbase.zookeeper.port: 8765
        String connectionUrl = ThinClientUtil.getConnectionUrl(host, port);
        druidDataSource.setUrl(connectionUrl);
        return druidDataSource;
    }

    @Bean
    public SqlSessionFactory phoenixSqlSessionFactory() {
        return getSqlSessionFactory(phoenixDataSource());
    }

    @Bean
    public SqlSessionTemplate phoenixSqlSessionTemplate() {
        return getSqlSessionTemplate(phoenixSqlSessionFactory());
    }*/
}
