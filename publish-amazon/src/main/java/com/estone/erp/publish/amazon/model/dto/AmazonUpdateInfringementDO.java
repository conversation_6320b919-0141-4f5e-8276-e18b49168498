package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-02 11:34
 */
@Getter
@Setter
public class AmazonUpdateInfringementDO {

    private String id;

    /**
     * 侵权词 商标词 违禁词
     */
    private String infringingBrandWord;

    /**
     * 词 标识
     */
    private List<String> trademarkIdentification;

    /**
     * 所有词 详情
     */
    private List<WordValidateResult> infringementWordInfos;
    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateInfringementTime;

    private AmazonUpdateInfringementDO() {

    }
    public AmazonUpdateInfringementDO(String id, Date updateDate) {
        this.id = id;
        this.updateInfringementTime = updateDate;
    }
}
