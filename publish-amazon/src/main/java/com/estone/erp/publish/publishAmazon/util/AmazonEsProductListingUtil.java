package com.estone.erp.publish.publishAmazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.enums.AmazonListingDownFieldEnum;
import com.estone.erp.publish.amazon.enums.AmazonListingItemStatus;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.util.FeedTaskUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.response.ComposeAndSuiteSkuResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.domain.Page;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 在线列表业务逻辑
 */
@Slf4j
public class AmazonEsProductListingUtil {

    private static final SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);

    private static final EsAmazonProductListingService esAmazonProductListingService = SpringUtils.getBean(EsAmazonProductListingService.class);

    private static final SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

    /**
     * 处理查询条件
     * @param esAmazonProductListingRequest
     * @return
     */
    public static String handleEsAmazonProductListingRequest(EsAmazonProductListingRequest esAmazonProductListingRequest){
        String msg =null;
        //平台货号
        String sellerSku = esAmazonProductListingRequest.getSellerSku();
        List<String> sellerSkuList = null;
        if (StringUtils.isNotEmpty(sellerSku) && sellerSku.contains(",")){
            sellerSkuList = CommonUtils.splitList(sellerSku,",");
        }
        if (CollectionUtils.isNotEmpty(sellerSkuList)) {
            if (sellerSkuList.size()>1024){
                msg = "参数超长，请检查参数!" ;
            }
            esAmazonProductListingRequest.setSellerSkuList(sellerSkuList);
            esAmazonProductListingRequest.setSellerSku(null);
        }
        // 2024-1-24 10:28:00 sku 查询特殊处理 http://172.16.2.103:8080/browse/ES-7485
        String mainSku = esAmazonProductListingRequest.getMainSku();
        if (StringUtils.isNotBlank(mainSku)) {
            List<String> mainSkuList = CommonUtils.splitList(mainSku,",");
            esAmazonProductListingRequest.setMainSkuList(mainSkuList);
            esAmazonProductListingRequest.setMainSku(null);
        }
        String articleNumber = esAmazonProductListingRequest.getArticleNumber();
        if (StringUtils.isNotEmpty(articleNumber)) {
            List<String> articleNumberList = CommonUtils.splitList(articleNumber,",");
            esAmazonProductListingRequest.setArticleNumberList(articleNumberList);
            esAmazonProductListingRequest.setArticleNumber(null);
        }
        List<String> mainSkuList = esAmazonProductListingRequest.getMainSkuList();
        if (CollectionUtils.isNotEmpty(mainSkuList)) {
            esAmazonProductListingRequest.setMainSkuList(null);
            esAmazonProductListingRequest.setMainSku(null);
            List<String> sonSkuListByMainSku = singleItemEsService.getSonSkuListByMainSku(mainSkuList);
            sonSkuListByMainSku.addAll(mainSkuList);
            List<String> articleNumberList = esAmazonProductListingRequest.getArticleNumberList();
            if (CollectionUtils.isNotEmpty(articleNumberList)) {
                sonSkuListByMainSku.addAll(articleNumberList);
            }
            esAmazonProductListingRequest.setArticleNumberList(sonSkuListByMainSku);
        }
        // 组合sku
        String composeSku = esAmazonProductListingRequest.getComposeSku();
        if (StringUtils.isNotBlank(composeSku)) {
            List<String> composeSkuList = CommonUtils.splitList(composeSku,",");
            esAmazonProductListingRequest.setComposeSkuList(composeSkuList);
            esAmazonProductListingRequest.setComposeSku(null);
        }
        List<String> composeSkuList = esAmazonProductListingRequest.getComposeSkuList();
        if (CollectionUtils.isNotEmpty(composeSkuList)) {
            ComposeAndSuiteSkuResponse composeAndSuiteSku = ProductUtils.getComposeAndSuiteSku(composeSkuList);
            Map<String, List<String>> suiteSkuMap = composeAndSuiteSku.getSuiteSkuMap();
            Map<String, List<String>> composeSkuMap = composeAndSuiteSku.getComposeSkuMap();
            List<String> suiteSonSkuList = new ArrayList<>();
            List<String> composeSonSkuList = new ArrayList<>();
            if (MapUtils.isNotEmpty(suiteSkuMap)) {
                Set<String> set = suiteSkuMap.keySet();
//                Set<String> collect = suiteSkuMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
                suiteSonSkuList.addAll(set);
//                suiteSonSkuList.addAll(collect);
            }
            if (MapUtils.isNotEmpty(composeSkuMap)) {
                Set<String> set = composeSkuMap.keySet();
//                Set<String> collect = composeSkuMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
                composeSonSkuList.addAll(set);
//                composeSonSkuList.addAll(collect);
            }
            suiteSonSkuList.addAll(composeSonSkuList);
            if (CollectionUtils.isEmpty(suiteSonSkuList)) {
                return "组合sku不存在";
            }
            List<String> articleNumberList = esAmazonProductListingRequest.getArticleNumberList();
            if (CollectionUtils.isNotEmpty(articleNumberList)) {
                suiteSonSkuList.addAll(articleNumberList);
            }
            esAmazonProductListingRequest.setArticleNumberList(suiteSonSkuList);
        }
        // 校验个数 articleNumber
        List<String> articleNumberList = esAmazonProductListingRequest.getArticleNumberList();
        if (CollectionUtils.isNotEmpty(articleNumberList)) {
            articleNumberList = articleNumberList.stream().distinct().collect(Collectors.toList());
            if (articleNumberList.size()>1024){
                return "参数超长，请检查参数!";
            }
            esAmazonProductListingRequest.setArticleNumberList(articleNumberList);
        }
        // 2024-1-24 10:28:00 asin 查询特殊处理 http://172.16.2.103:8080/browse/ES-7485
        //parentasin
        String parentAsin = esAmazonProductListingRequest.getParentAsin();
        if (StringUtils.isNotEmpty(parentAsin)){
            List<String> parentAsinList = CommonUtils.splitList(parentAsin,",");
            esAmazonProductListingRequest.setParentAsinList(parentAsinList);
            esAmazonProductListingRequest.setParentAsin(null);
        }
        //sonAsin
        String sonAsin = esAmazonProductListingRequest.getSonAsin();
        if (StringUtils.isNotEmpty(sonAsin)){
            List<String> sonAsinList = CommonUtils.splitList(sonAsin,",");
            esAmazonProductListingRequest.setSonAsinList(sonAsinList);
            esAmazonProductListingRequest.setSonAsin(null);
        }
        // 处理，parentAsin 也要作为sonAsin 参数来传
        List<String> parentAsinList = esAmazonProductListingRequest.getParentAsinList();
        if (CollectionUtils.isNotEmpty(parentAsinList)) {
            esAmazonProductListingRequest.setParentAsinList(null);
            esAmazonProductListingRequest.setParentAsin(null);
            // 获取父Asin下所有的子asin
            esAmazonProductListingRequest.setParentOrSonAsinList(parentAsinList);
        }
        // 校验子asin个数
        List<String> sonAsinList = esAmazonProductListingRequest.getSonAsinList();
        if (CollectionUtils.isNotEmpty(sonAsinList)) {
            sonAsinList = sonAsinList.stream().distinct().collect(Collectors.toList());
            if (sonAsinList.size() > 100) {
                return "查询子asin个数过多，不可超过100个";
            }
            esAmazonProductListingRequest.setSonAsinList(sonAsinList);
        }

        //单品状态
        String skuStatus = esAmazonProductListingRequest.getSkuStatus();
        List<String> skuStatusList = null;
        if (StringUtils.isNotEmpty(skuStatus) && skuStatus.contains(",")){
            skuStatusList = CommonUtils.splitList(skuStatus,",");
        }
        if (CollectionUtils.isNotEmpty(skuStatusList)) {
            esAmazonProductListingRequest.setSkuStatusList(skuStatusList);
            esAmazonProductListingRequest.setSkuStatus(null);
        }
        //禁售平台
        String forbidChannel = esAmazonProductListingRequest.getForbidChannel();
        List<String> forbidChannelList = null;
        if (StringUtils.isNotEmpty(forbidChannel) && forbidChannel.contains(",")){
            forbidChannelList = CommonUtils.splitList(forbidChannel,",");
        }
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            esAmazonProductListingRequest.setForbidChannelList(forbidChannelList);
            esAmazonProductListingRequest.setForbidChannel(null);
        }
        //平台库存是否为0
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getQuantityIsZero()) && esAmazonProductListingRequest.getQuantityIsZero()){
            esAmazonProductListingRequest.setQuantity(0);
        }else if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getQuantityIsZero()) && !esAmazonProductListingRequest.getQuantityIsZero()){
            esAmazonProductListingRequest.setQuantityGt(0);
        }

        // 账号等级
        String accountLevel = esAmazonProductListingRequest.getAccountLevel();
        List<String> accountLevelList = new ArrayList<>();
        if (StringUtils.isNotEmpty(accountLevel) && accountLevel.contains(",")){
            accountLevelList = CommonUtils.splitList(accountLevel,",");
        }
        if (CollectionUtils.isNotEmpty(accountLevelList)) {
            esAmazonProductListingRequest.setAccountLevelList(accountLevelList);
            esAmazonProductListingRequest.setAccountLevel(null);
        }

        return msg;
    }

    public static void doReplaceAccount(Map<String, SalesmanAccountDetail> accountMap) {
        for (Map.Entry<String, SalesmanAccountDetail> stringListEntry : accountMap.entrySet()) {
            SalesmanAccountDetail value = stringListEntry.getValue();
            if (!ObjectUtils.isEmpty(value) && CollectionUtils.isNotEmpty(value.getSalesmanSet())) {
                Set<String> collect = value.getSalesmanSet().stream().map(a -> {
                    if (StringUtils.isNotBlank(a)) {
                        String[] split = a.split("-");
                        if (split.length == 2) {
                            return split[1] +"-" + split[0];
                        }
                    }
                    return a;
                }).collect(Collectors.toSet());
                value.setSalesmanSet(collect);
            }
        }
    }

    public static List<List<String>> getDataLists(String[] fieldArrayList, List<String> downFields, Map<String, SalesmanAccountDetail> saleManAccountMap, Map<String, SaleAccount> saleAccountMap, List<List<String>> awLists,Map<String,Date> accountSyncDateMap,Map<Integer,String> riskLevelIdAndNameMap, EsAmazonProductListing product) {
        awLists.clear();
        List<String> awList = new ArrayList<>(fieldArrayList.length);

        if (downFields.contains(AmazonListingDownFieldEnum.MAIN_IMAGE.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getMainImage()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.PARENT_ASIN.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getParentAsin()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SON_ASIN.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getSonAsin()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ITEM_TYPE.getCode())) {
            Integer itemType = product.getItemType();
            String statusMsg = AmazonListingitemtypeEnum.getStatusMsg(itemType);
            awList.add(POIUtils.transferStr2Str(statusMsg));
        }
        Boolean isDownSaleMan = downFields.contains(AmazonListingDownFieldEnum.SALE_MAN_LIST.getCode());
        if (isDownSaleMan) {
            String saleMans = ObjectUtils.isEmpty(saleManAccountMap.get(product.getAccountNumber())) ?
                    "" : POIUtils.transferObj2Str(StringUtils.join(saleManAccountMap.get(product.getAccountNumber()).getSalesmanSet(), ","));
            awList.add(saleMans);
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ITEM_NAME.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getItemName()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.INFRINGEMENT_WORD.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getInfringingBrandWord()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ACCOUNT_NUMBER.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getAccountNumber()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ACCOUNT_STATUS.getCode())) {
            String accountNumber = product.getAccountNumber();
            String accountStatus = null;
            if (StringUtils.isNotBlank(accountNumber)) {
                SaleAccount saleAccount = saleAccountMap.get(accountNumber);
                if (saleAccount != null) {
                    accountStatus = saleAccount.getAccountStatus();
                    accountStatus = SaleAccountStastusEnum.getNameByCode(accountStatus);
                }
            }
            awList.add(POIUtils.transferStr2Str(accountStatus));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.EXCEPTION_STATUS.getCode())) {
            String accountNumber = product.getAccountNumber();
            String exceptionStatus = null;
            if (StringUtils.isNotBlank(accountNumber)) {
                SaleAccount saleAccount = saleAccountMap.get(accountNumber);
                if (saleAccount != null) {
                    exceptionStatus = saleAccount.getAbnormalCause();
                }
            }
            awList.add(POIUtils.transferStr2Str(exceptionStatus));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.BRAND_NAME.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getBrandName()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SELLER_SKU.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getSellerSku()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ARTICLE_NUMBER.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getArticleNumber()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.PRICE.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getPrice()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.MERCHANT_SHIPPING_GROUP.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getMerchantShippingGroup()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SHIPPING_COST.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getShippingCost()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.TOTAL_PRICE.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getTotalPrice()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.QUANTITY.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getQuantity()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.GROSS_PROFIT.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getGrossProfit()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.GROSS_PROFIT_RATE.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getGrossProfitRate()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SKU_STATUS.getCode())) {
            if (StringUtils.isNotBlank(product.getSkuStatus())) {
                String s = SkuStatusEnum.buildName(product.getSkuStatus());
                awList.add(POIUtils.transferStr2Str(s));
            } else {
                awList.add("");
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.PUBLISH_ROLE.getCode())) {
            String enumByCode = PublishRoleEnum.getNameByCode(product.getPublishRole());
            awList.add(POIUtils.transferStr2Str(enumByCode));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SKU_DATA_SOURCE.getCode())) {
            String skuDataSource = null;
            if (product.getSkuDataSource() != null && 1 == product.getSkuDataSource()) {
                skuDataSource = "管理单品";
            } else {
                skuDataSource = SkuDataSourceEnum.getNameByCode(product.getSkuDataSource());
            }
            awList.add(POIUtils.transferStr2Str(skuDataSource));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.COMPOSE_STATUS.getCode())) {
            Integer composeStatus = product.getComposeStatus();
            String status = null;
            if (composeStatus != null && composeStatus == 8003) {
                status =  "启用";
            } else if (composeStatus != null && composeStatus == 8004) {
                status =  "禁用" ;
            }
            awList.add(POIUtils.transferStr2Str(status));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.FULFILLMENT_LATENCY.getCode())) {
            Integer fulfillmentLatency = product.getFulfillmentLatency();
            awList.add(POIUtils.transferObj2Str(fulfillmentLatency));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.PROMOTION.getCode())) {
            Integer promotion = product.getPromotion();
            String promotionValue = null;
            if (promotion != null) {
                if (promotion == 0 || promotion == 2) {
                    promotionValue =  "否";
                }else if(promotion == 1){
                    promotionValue =  "是" ;
                }
            }
            awList.add(POIUtils.transferStr2Str(promotionValue));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.NEW_STATE.getCode())) {
            Boolean newState = product.getNewState();
            String status = newState == null ? null: newState ? "是":"否";
            awList.add(POIUtils.transferStr2Str(status));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SPECIAL_GOODS_CODE.getCode())) {
            String specialGoodsCode = product.getSpecialGoodsCode();
            String specialGoodsName = null;
            if (StringUtils.isNotBlank(specialGoodsCode)) {
                List<Integer> collect = Stream.of(specialGoodsCode.split(",")).filter(StringUtils::isNotBlank).filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList());
                StringJoiner stringJoiner = new StringJoiner(",");
                for (Integer code : collect) {
                    String nameByCode = SpecialTagEnum.getNameByCode(code);
                    if (StringUtils.isNotBlank(nameByCode)) {
                        stringJoiner.add(nameByCode);
                    }
                }
                specialGoodsName = stringJoiner.toString();
            }
            awList.add(POIUtils.transferStr2Str(specialGoodsName));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.TRADEMARK_IDENTIFICATION.getCode())) {
            List<String> trademarkIdentification = product.getTrademarkIdentification();
            String trademarkIdentificationStr = null;
            if (CollectionUtils.isNotEmpty(trademarkIdentification)) {
                trademarkIdentificationStr = String.join(",", trademarkIdentification);
            }
            awList.add(POIUtils.transferStr2Str(trademarkIdentificationStr));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.FORBID_CHANNEL.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getForbidChannel()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.INFRINGEMENT_TYPENAME.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getInfringementTypename()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.INFRINGEMENT_OBJ.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getInfringementObj()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.NORMAL_SALE.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getNormalSale()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.IS_ONLINE.getCode())) {
            if (product.getIsOnline() != null) {
                awList.add(POIUtils.transferObj2Str(product.getIsOnline() ? "是" : "否"));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ITEM_STATUS.getCode())) {
            String statusMsgByStatusCode = null;
            if (StringUtils.isNotBlank(product.getItemStatus())) {
                if ("Inactive".equals(product.getItemStatus())) {
                    statusMsgByStatusCode = "不可售";
                } else {
                    statusMsgByStatusCode = AmazonListingItemStatus.getStatusMsgByStatusCode(product.getItemStatus());
                }
            }
            awList.add(POIUtils.transferStr2Str(statusMsgByStatusCode));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ISSUES_SEVERITY.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getIssuesSeverity()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ATTRIBUTE3.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getAttribute3()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ATTRIBUTE1.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getAttribute1()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.OPEN_DATE.getCode())) {
            if (product.getOpenDate() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getOpenDate()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.OFFLINE_DATE.getCode())) {
            if (product.getOfflineDate() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getOfflineDate()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.FIRST_OPEN_DATE.getCode())) {
            if (product.getFirstOpenDate() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getFirstOpenDate()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.FIRST_OFFLINE_DATE.getCode())) {
            if (product.getFirstOfflineDate() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getFirstOfflineDate()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_24H_COUNT.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_24H_count()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_7D_COUNT.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_last_7d_count()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_14D_COUNT.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_last_14d_count()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_30D_COUNT.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_last_30d_count()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_NUM_TOTAL.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_num_total()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_7D_AVG.getCode())) {
            Integer order_last_7d_count = product.getOrder_last_7d_count();
            String avg = null;
            if (order_last_7d_count != null && order_last_7d_count != 0) {
                avg = BigDecimal.valueOf(order_last_7d_count).divide(BigDecimal.valueOf(7), 2, RoundingMode.HALF_UP).toString();
            }
            awList.add(POIUtils.transferStr2Str(avg));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_14D_AVG.getCode())) {
            Integer order_last_14d_count = product.getOrder_last_14d_count();
            String avg = null;
            if (order_last_14d_count != null && order_last_14d_count != 0) {
                avg = BigDecimal.valueOf(order_last_14d_count).divide(BigDecimal.valueOf(14), 2, RoundingMode.HALF_UP).toString();
            }
            awList.add(POIUtils.transferStr2Str(avg));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_30D_AVG.getCode())) {
            Integer order_last_30d_count = product.getOrder_last_30d_count();
            String avg = null;
            if (order_last_30d_count != null && order_last_30d_count != 0) {
                avg = BigDecimal.valueOf(order_last_30d_count).divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP).toString();
            }
            awList.add(POIUtils.transferStr2Str(avg));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.order_days_within_30d.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getOrder_days_within_30d()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ORDER_LAST_7D_AND_30D_AVG.getCode())) {
            String avg = null;
            Integer order_last_30d_count = product.getOrder_last_30d_count();
            Integer order_last_7d_count = product.getOrder_last_7d_count();
            if (order_last_7d_count != null && order_last_7d_count != 0 && order_last_30d_count != null && order_last_30d_count != 0) {
                avg = BigDecimal.valueOf(order_last_30d_count).divide(BigDecimal.valueOf(order_last_7d_count), 2, RoundingMode.HALF_UP).toString();
            }
            awList.add(POIUtils.transferStr2Str(avg));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.UPDATE_DATE.getCode())) {
            if (product.getUpdateDate() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getUpdateDate()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.SYNC_DATE.getCode())) {
            // 处理同步时间
            Date syncDate = accountSyncDateMap.containsKey(product.getAccountNumber()) ? accountSyncDateMap.get(product.getAccountNumber()) : product.getSyncDate();
            if ( syncDate != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(syncDate)));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ATTRIBUTE4.getCode())) {
            awList.add(POIUtils.transferStr2Str(product.getAttribute4()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.ATTRIBUTE5.getCode())) {
            awList.add(POIUtils.transferObj2Str(product.getAttribute5()));
        }
        if (downFields.contains(AmazonListingDownFieldEnum.UPDATE_INFRINGEMENT_TIME.getCode())) {
            if (product.getUpdateInfringementTime() != null) {
                awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((product.getUpdateInfringementTime()))));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }
        if (downFields.contains(AmazonListingDownFieldEnum.RISKLEVELID.getCode())) {
            if (product.getRiskLevelId() != null && StringUtils.isNotBlank(riskLevelIdAndNameMap.get(product.getRiskLevelId()))) {
                awList.add(POIUtils.transferObj2Str(riskLevelIdAndNameMap.get(product.getRiskLevelId())));
            } else {
                awList.add(POIUtils.transferObj2Str(""));
            }
        }

        awLists.add(awList);
        return awLists;
    }

    public static void batchInsertTableSheet(EsAmazonProductListingRequest esAmazonProductListingRequest, int total, int batchSize, int index, AtomicInteger atomicInteger, List<String> downFields, Workbook wb) throws IOException {
        int pageTotal = total;
        if (total > batchSize) {
            pageTotal = total - batchSize * (index - 1) > batchSize ? batchSize : total - batchSize * (index - 1);
        }
        int pageSize = 10000;
        int pageIndex = 1;
        if (pageTotal > pageSize) {
            pageIndex = pageTotal % pageSize == 0 ? pageTotal / pageSize : pageTotal / pageSize + 1;
        }
        CountDownLatch countDownLatch = new CountDownLatch(pageIndex);

        // 确定查询起始页
        int startPage = batchSize / pageSize * (index - 1);

        List<Page<EsAmazonProductListing>> pageList = new ArrayList<>();
        for (int i = startPage; i < startPage + pageIndex; i++) {
            int finalI = i;
            AmazonExecutors.executeQuery(() -> {
                try {
                    // 查询
                    Page<EsAmazonProductListing> listingPage = esAmazonProductListingService.page(esAmazonProductListingRequest, pageSize, finalI);
                    pageList.add(listingPage);
                } catch (Exception e) {
                    log.error(e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
            if (CollectionUtils.isNotEmpty(pageList)) {
                List<EsAmazonProductListing> amazonProductListings = new ArrayList<>();
                for (Page<EsAmazonProductListing> listingPage : pageList) {
                    amazonProductListings.addAll(listingPage.getContent());
                }
                // 如果查询账号状态或查询异常状态
                List<SaleAccount> saleAccounts = new ArrayList<>();
                // 获取销售信息
                Map<String, SalesmanAccountDetail> accountMap = new HashMap<>();
                // 获取处理报告同步时间
                Map<String,Date> accountSyncDateMap = new HashMap<>();
                List<String> accountNumberList = amazonProductListings.stream().map(EsAmazonProductListing::getAccountNumber).distinct().collect(Collectors.toList());
                Boolean syncDate = downFields.contains(AmazonListingDownFieldEnum.SYNC_DATE.getCode());
                if (syncDate){
                    accountSyncDateMap = FeedTaskUtils.selectAmazonAccountSyncDate(accountNumberList);
                }
                Boolean isDownSaleMan = downFields.contains(AmazonListingDownFieldEnum.SALE_MAN_LIST.getCode());
                if (isDownSaleMan) {
                    try {
                        String saleManParam = "{\"accountNumberList\":" + JSON.toJSONString(accountNumberList) + ",\"saleChannel\":\"amazon\"}";
                        accountMap = AccountUtils.listSalesmanAccountInfo(saleManParam);
                        if (MapUtils.isEmpty(accountMap) && accountMap.size() == 0) {
                            log.error("调用销售订单系统获取销售信息报错，请重试或取消勾选销售字段");
                        }
                        AmazonEsProductListingUtil.doReplaceAccount(accountMap);
                    } catch (Exception e) {
                        log.error("调用销售订单系统获取销售信息报错，请重试或取消勾选销售字段");
                    }
                }
                if (accountMap == null) {
                    accountMap = new HashMap<>();
                }
                boolean containsExceptionStatus = downFields.contains(AmazonListingDownFieldEnum.EXCEPTION_STATUS.getCode());
                boolean containsAccountStatus = downFields.contains(AmazonListingDownFieldEnum.ACCOUNT_STATUS.getCode());
                if (containsAccountStatus || containsExceptionStatus) {
                    if (CollectionUtils.isNotEmpty(accountNumberList)) {
                        String[] withFields = {"accountNumber","accountStatus","abnormalCause"};
                        ApiResult<List<SaleAccount>> saleAccountResult = EsAccountUtils.getSaleAccountsByAccounts(accountNumberList, SaleChannel.CHANNEL_AMAZON, withFields);
                        if (!saleAccountResult.isSuccess()) {
                            log.error("根据店铺账号获取saleAccount信息报错，错误信息：{}", saleAccountResult.getErrorMsg());
                        }
                        saleAccounts = saleAccountResult.getResult();
                    }
                }

                Map<Integer,String> riskLevelIdAndNameMap = ProductUtils.getRiskLevelNameMap();
                // excel 表头
                List<String> nameList = AmazonListingDownFieldEnum.getNameList(downFields);
                String[] fieldArrayList = nameList.toArray(new String[nameList.size()]);

                final List<List<String>> awLists = new ArrayList<>();
                final Map<String, SalesmanAccountDetail> saleManAccountMap = new HashMap<>(accountMap);
                final Map<String, SaleAccount> saleAccountMap = saleAccounts.stream().collect(Collectors.toMap(SaleAccount::getAccountNumber, Function.identity(), (oldV, newV)-> newV));
                int sheetIndex = atomicInteger.getAndIncrement();

                // 导出数据
                Map<String, Date> finalAccountSyncDateMap = accountSyncDateMap;
                POIUtils.createLocalExcel(fieldArrayList, amazonProductListings,
                        esAmazonProductListing -> AmazonEsProductListingUtil.getDataLists(fieldArrayList, downFields, saleManAccountMap, saleAccountMap, awLists, finalAccountSyncDateMap, riskLevelIdAndNameMap,esAmazonProductListing), sheetIndex, wb);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 处理账号状态和异常状态
     * @param esAmazonProductListingRequest
     * @return errorMsg
     */
    public static List<SaleAccount> handleStatus(EsAmazonProductListingRequest esAmazonProductListingRequest) {
        List<SaleAccount> saleAccounts = new ArrayList<>();

        List<String> accountStatusList = esAmazonProductListingRequest.getAccountStatusList();
        List<String> exceptionStatusList = esAmazonProductListingRequest.getExceptionStatusList();
        List<String> accounts = esAmazonProductListingRequest.getAccountNumberList();

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isNotEmpty(accounts)) {
            request.setAccountNumberList(accounts);
        }
        if (CollectionUtils.isNotEmpty(accountStatusList)) {
            request.setAccountStatusList(accountStatusList);
        }
        if (CollectionUtils.isNotEmpty(exceptionStatusList)) {
            request.setExceptionStatusList(exceptionStatusList);
        }
        String[] withFields = {"accountNumber","accountStatus","abnormalCause"};
        try {
            saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(saleAccounts)) {
            accounts = saleAccounts.stream().map(SaleAccount::getAccountNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            esAmazonProductListingRequest.setAccountNumberList(accounts);
        }

        return saleAccounts;
    }

}
