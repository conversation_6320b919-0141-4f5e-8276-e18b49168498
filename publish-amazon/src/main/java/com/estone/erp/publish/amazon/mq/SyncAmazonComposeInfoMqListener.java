package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.amazon.constant.AmazonJobConstant;
import com.estone.erp.publish.amazon.util.AmazonListingUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 组合套装产品信息变更同步
 * <AUTHOR>
 * @date 2022-10-26 11:50
 */
@Slf4j
@Component
public class SyncAmazonComposeInfoMqListener {
    @Autowired
    private AmazonProductListingService amazonProductListingService;

    @RabbitListener(queues = PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE, containerFactory = "publishCommonFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            return;
        }
       // log.info("套装产品更新：{}",body);
        try {
            String spu = JSON.parseObject(body, String.class);
            syncComposeProductInfo(spu);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            //log.error("套装产品更新 Exception: {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void syncComposeProductInfo(String spu) {
        if (StringUtils.isBlank(spu)) {
            return;
        }

        for (String site : AmazonJobConstant.SITES) {
            // 同步产品信息更新在线列表
            syncUpdateProductListing(site, spu);
        }
    }

    private void syncUpdateProductListing(String site,String spu) {
        AmazonProductListingExample example = new AmazonProductListingExample();
        String columns = "id,articleNumber,infringementTypename,infringementObj,normalSale";
        example.setTableIndex(amazonProductListingService.getTableIndex(site));
        example.setColumns(columns);
        AmazonProductListingExample.Criteria criteria = example.createCriteria();
        criteria.andArticleNumberEqualTo(spu);
        List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example, site);
        if (CollectionUtils.isEmpty(amazonProductListings)) {
            return;
        }

        for (AmazonProductListing amazonProductListing : amazonProductListings) {
            // 组装数据
            boolean match = AmazonListingUtils.matchComposeProduct(amazonProductListing.getArticleNumber(), amazonProductListing);
            if (match) {
                amazonProductListing.setSite(site);
                AmazonProductListingExample updateExample = new AmazonProductListingExample();
                updateExample.createCriteria().andArticleNumberEqualTo(spu);
                amazonProductListingService.updateProductMsgByExampleSelective(amazonProductListing, updateExample);
            }
        }
    }
}
