package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.rabbitmq.client.Channel;
import io.swagger.client.enums.ReportType;
import io.swagger.client.model.Report;
import io.swagger.client.response.AmazonReportsDownloadProgress;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @Description: ${监听消费新版Amazon同步产品txt数据}
 * @Author: yjy
 * @Date: 2021/1/19 11:48
 * @Version: 1.0.0
 */
@Slf4j
public class SyncAmazonReportFileMqListener implements ChannelAwareMessageListener {

    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonCategoryService amazonCategoryService;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }

        Boolean isSuccess = doService(body);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        Boolean isSuccess = false;
        AmazonReportsDownloadProgress amazonReportsDownloadProgress = null;
        try {
            String bodyStr = JSON.parseObject(body, String.class);
            amazonReportsDownloadProgress = JSON.parseObject(bodyStr, AmazonReportsDownloadProgress.class);
            if (null == amazonReportsDownloadProgress) {
                return false;
            }
        } catch (Exception e) {
            log.error("解析AmazonReportsDownloadProgress mq消息体异常 -> {}", body);
        }

        String accountNumber = amazonReportsDownloadProgress.getAccountNumber();
        String reportType = amazonReportsDownloadProgress.getReportType();
        try {
            String errorMsg = null;
            if (Report.ProcessingStatusEnum.FATAL.equals(amazonReportsDownloadProgress.getProcessingStatus())) {
                errorMsg = "报告接口请求成功，平台返回状态为失败！";
                log.error(errorMsg + "body:" + body);
            }

            if (!amazonReportsDownloadProgress.getSuccess()) {
                errorMsg = amazonReportsDownloadProgress.getErrorMsg();
                handleResult(accountNumber, reportType, errorMsg);
                return isSuccess;
            }

            // 根据报告类型 解析文件 更新对应数据
            // Listing
            if(ReportType.GET_MERCHANT_LISTINGS_ALL_DATA.getName().equals(reportType)) {
                SyncSpProductData syncSpProductData  = AmazonSpLocalUtils.handleListingReportFile(amazonReportsDownloadProgress);
                amazonProductListingService.batchRefreshAmazonListingsForReport(syncSpProductData);
            }
            // 库存
            else if(ReportType.GET_FLAT_FILE_OPEN_LISTINGS_DATA.getName().equals(reportType)) {
                SyncSpProductData syncSpProductData  = AmazonSpLocalUtils.handleListingInventoryReportFile(amazonReportsDownloadProgress);
                if (null != syncSpProductData && MapUtils.isEmpty(syncSpProductData.getSellerSku2SplitsMap())) {
                    // 同步在线列表 只拿到amazon给出文件的表头，将店铺数据都处理为下架
                    // 更新处理报告
                    errorMsg = "实际同步库存报告成功，amazon返回的文件没有数据";
                    handleAmazonFeedtaskResult(accountNumber, errorMsg,reportType);
                }else {
                    amazonProductListingService.batchRefreshAmazonInventoryForReport(syncSpProductData);
                }
            }
            // 分类
            else if (ReportType.GET_XML_BROWSE_TREE_DATA.getName().equals(reportType)) {
                AmazonAccountRelation amazonAccountRelation = amazonAccountRelationService.selectByAccount(accountNumber);
                if(null != amazonAccountRelation && StringUtils.isNotBlank(amazonAccountRelation.getAccountCountry())) {
                    BrowseTreeReport browseTreeReport = AmazonSpLocalUtils.handleCategoryReportFile(amazonReportsDownloadProgress);
                    amazonCategoryService.batchRefreshAmazonCategoryForReport(browseTreeReport, accountNumber, amazonAccountRelation.getAccountCountry());

                }else {
                    errorMsg = "站点为空";
                }
            }

            // 更新处理报告
            handleResult(accountNumber, reportType, errorMsg);
        } catch (Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
            handleResult(accountNumber, reportType, e.getMessage());
        }

        return isSuccess;
    }

    /**
     * 亚马逊任务更新
     *
     * @param accountNumber
     * @param errorMsg
     * @return
     */
    private void handleResult(String accountNumber, String reportType, String errorMsg) {
        // Listing
        String taskType = null;
        if(ReportType.GET_MERCHANT_LISTINGS_ALL_DATA.getName().equals(reportType)) {
            taskType = TaskTypeEnum.NEW_SYNC_PRODUCT.getStatusMsgEn();
        }
        // 分类
        else if (ReportType.GET_XML_BROWSE_TREE_DATA.getName().equals(reportType)) {
            taskType = TaskTypeEnum.SYNC_CATEGORY.getStatusMsgEn();

        }
        else if (ReportType.GET_FLAT_FILE_OPEN_LISTINGS_DATA.getName().equals(reportType)) {
            taskType = TaskTypeEnum.NEW_SYNC_PRODUCT_INVENTORY.getStatusMsgEn();
        }
        handleAmazonFeedtaskResult(accountNumber, errorMsg,taskType);
    }

    /**
     * 更新同步listing报告
     *
     * @param accountNumber
     * @param errorMsg
     * @return
     */
    private void handleAmazonFeedtaskResult(String accountNumber, String errorMsg,String taskType) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(taskType)){
            log.error("accountNumber or taskType is null");
        }
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andTaskTypeEqualTo(taskType)
                .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode());
        List<FeedTask> feedTaskList = feedTaskService.selectByExample(example, Platform.Amazon.name());

        if (CollectionUtils.isEmpty(feedTaskList)) {
            return;
        }

        for (FeedTask feedTask : feedTaskList) {
            FeedTask newFeedTask = new FeedTask();
            newFeedTask.setId(feedTask.getId());
            newFeedTask.setPlatform(Platform.Amazon.name());
            newFeedTask.setTableIndex();
            newFeedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            newFeedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            if (StringUtils.isBlank(errorMsg)) {
                newFeedTask.setResultStatus(ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
            } else {
                newFeedTask.setResultMsg(errorMsg);
                newFeedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
            }

            feedTaskService.updateByPrimaryKeySelective(newFeedTask);
        }
    }

}