package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * 删除Listing 标题侵权词
 * <AUTHOR>
 * @date 2023-12-18 9:38
 */
@Data
public class AmazonDelTitleBanWordRequest {

    /**
     * id
     */
    private String id;

    /**
     * 店铺
     */
    @NotBlank(message = "店铺不能为空")
    private String accountNumber;

    /**
     * 待校验文本
     */
    @NotEmpty(message = "待校验文本不能为空")
    private String  text;

    /**
     * 文案类型
     */
    private Integer wenAnType = 1;

    /**
     * 子SKU
     */
    @NotEmpty(message = "子sku不能为空")
    private String sonSku;
}
