package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonLinkManagementConfigRequest;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * Amazon链接管理配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/amazonLinkManagementConfig")
public class AmazonLinkManagementConfigController {
    @Resource
    private AmazonLinkManagementConfigService amazonLinkManagementConfigService;

    /**
     * 分页查询
     * 根据需求文档4.查询功能与说明实现条件查询
     * 支持规则名称、店铺、站点、状态、调整方式、优先级、创建人等多条件查询
     */
    @PostMapping("/queryPage")
    public CQueryResult<AmazonLinkManagementConfig> queryPage(@RequestBody CQuery<AmazonLinkManagementConfigRequest> query) {
        try {
            log.info("收到分页查询请求，页码：{}，页大小：{}", query.getPage(), query.getLimit());
            return amazonLinkManagementConfigService.queryPage(query);
        } catch (Exception e) {
            log.error("分页查询失败", e);
            return CQueryResult.failResult("查询失败：" + e.getMessage());
        }
    }


    /**
     * 根据id查询详情
     */
    @GetMapping("/getDetail/{id}")
    public ApiResult<AmazonLinkManagementConfig> getDetailById(@PathVariable Serializable id) {
        AmazonLinkManagementConfig detail = amazonLinkManagementConfigService.getById(id);
        return ApiResult.newSuccess(detail);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody AmazonLinkManagementConfig entity) {
        boolean success = amazonLinkManagementConfigService.updateById(entity);
        return ApiResult.newSuccess(success ? "更新成功" : "更新失败");
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    public ApiResult<String> save(@RequestBody AmazonLinkManagementConfig entity) {
        boolean success = amazonLinkManagementConfigService.save(entity);
        return ApiResult.newSuccess(success ? "新增成功" : "新增失败");
    }

    /**
     * 根据id列表删除
     */
    @PostMapping("/delete")
    public ApiResult<String> deleteByIds(@RequestBody List<Serializable> ids) {
        boolean success = amazonLinkManagementConfigService.removeByIds(ids);
        return ApiResult.newSuccess(success ? "删除成功" : "删除失败");
    }


}
