package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecordCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonUpcRecordService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2024-07-09 16:19:01
 */
@RestController
@RequestMapping("amazonUpcRecord")
public class AmazonUpcRecordController {
    @Resource
    private AmazonUpcRecordService amazonUpcRecordService;

    @PostMapping
    public ApiResult<?> postAmazonUpcRecord(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonUpcRecord": // 查询列表
                    CQuery<AmazonUpcRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonUpcRecordCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonUpcRecord> results = amazonUpcRecordService.search(cquery);
                    return results;
                case "addAmazonUpcRecord": // 添加
                    AmazonUpcRecord amazonUpcRecord = requestParam.getArgsValue(new TypeReference<AmazonUpcRecord>() {});
                    amazonUpcRecordService.insert(amazonUpcRecord);
                    return ApiResult.newSuccess(amazonUpcRecord);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAmazonUpcRecord(@PathVariable(value = "id", required = true) long id) {
        AmazonUpcRecord amazonUpcRecord = amazonUpcRecordService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(amazonUpcRecord);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAmazonUpcRecord(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAmazonUpcRecord": // 单个修改
                    AmazonUpcRecord amazonUpcRecord = requestParam.getArgsValue(new TypeReference<AmazonUpcRecord>() {});
                    amazonUpcRecordService.updateByPrimaryKeySelective(amazonUpcRecord);
                    return ApiResult.newSuccess(amazonUpcRecord);
                }
        }
        return ApiResult.newSuccess();
    }
}