package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.AmazonProductPublishCallHelper;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class PublishRestoreListingRelationShipProcesser extends AbstractPublishProcesser<AmazonListingParentRelationshipTemp> {


    private AmazonProductPublishCallHelper amazonProductPublishCallHelper = SpringUtils.getBean(AmazonProductPublishCallHelper.class);

    public PublishRestoreListingRelationShipProcesser(String accountNumber) {
        super(accountNumber, SpringUtils.getBean(RestoreListingRelationShipSubmitFeedXmlStrategy.class));
    }

    public PublishRestoreListingRelationShipProcesser(AmazonAccount amazonAccount) {
        super(amazonAccount, SpringUtils.getBean(RestoreListingRelationShipSubmitFeedXmlStrategy.class));
    }

    /**
     * 账号
     */
    protected AmazonAccount account;

    public ApiResult<String> getXmlByData(String feedType, AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp, AmazonAccount account){
        PublishData<AmazonListingParentRelationshipTemp> unitPublishData = new PublishData<>();
        unitPublishData.setAccount(account);
        unitPublishData.setFeedType(feedType);
        unitPublishData.setOperationType(OperationType.PartialUpdate);
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(amazonListingParentRelationshipTemp));
        String templateXml = getXsdXml(unitPublishData);
        if (org.apache.commons.lang3.StringUtils.isEmpty(templateXml)) {
            return ApiResult.newError("xml文件生成失败");
        }
        String xml = getXsdXml(unitPublishData);
        return ApiResult.newSuccess(xml);
    }

    public String getXsdXml(PublishData<AmazonListingParentRelationshipTemp> publishData) {
        String xml = null;
        String feedType = publishData.getFeedType();
        switch (SpFeedType.of(feedType)) {
            case POST_PRODUCT_DATA:
                xml = xmlStrategy.transferProduct2Xml(publishData);
                break;
            case POST_PRODUCT_RELATIONSHIP_DATA:
                xml = xmlStrategy.transferProductRelationship2Xml(publishData);
                break;
            default:
                break;
        }
        return xml;
    }


    @Override
    public void afterFinishProcessReports(PublishData<AmazonListingParentRelationshipTemp> publishData) {
    }

    @Override
    public Map<String, String> getUnitDataSellerSku2SkuMap(AmazonListingParentRelationshipTemp unitData) {
        return null;
    }

    @Override
    public Map<String, String> getVariantParentSku(AmazonListingParentRelationshipTemp unitData) {
        return null;
    }


    @Override
    public List<AmazonProcessReport> initProcessReports(PublishData<AmazonListingParentRelationshipTemp> publishData) {
        return null;
    }

}