package com.estone.erp.publish.publishAmazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;

import java.util.List;

public interface AmazonJSONListingFeedService {


    ApiResult<String> batchUpdatePriceAndInventory(AmazonProductListingDto requestParam);

    ApiResult<String> updateImageAndPublishImage(AmazonProductListing requestParam);

    ApiResult<String> batchProductListingImage(List<AmazonProductListing> amazonProductListingList);

    ApiResult<String> batchProductListingTitle(List<AmazonProductListing> amazonProductListingList);

    ApiResult<String> batchListingFulfillmentLatency(AmazonProductListingDto requestParam);

    ApiResult<String> batchUpdatePrice(List<AmazonProductListing> amazonProductListingList);

    ApiResult<String> batchUpdateInventory(List<AmazonProductListing> amazonProductListingList);

    void updateListingImages(AmazonProductListing amazonProductListing);
}

