package com.estone.erp.publish.amazon.mq.feed;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.jsonfeed.FeedsResult;
import com.estone.erp.publish.amazon.componet.feedresult.AmazonJsonFeedResultFactory;
import com.estone.erp.publish.amazon.componet.feedresult.AmazonJsonFeedResultHandler;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;

/**
 * PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE
 *
 * <AUTHOR>
 * @date 2025-04-07 9:34
 */
@Slf4j
public class SpJSONFeedsResultMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonPublishOperationLogService amazonPublishOperationLogService;
    @Autowired
    private AmazonJsonFeedResultFactory amazonJsonFeedResultFactory;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            // 获取消息体
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            FeedsResult feedsResult = JSON.parseObject(body, FeedsResult.class);
            AmazonJsonFeedResultHandler resultHandler = amazonJsonFeedResultFactory.getService(feedsResult.getSystemFeedType());
            if (resultHandler == null) {
                log.error("No handler found for feedType: {}", feedsResult.getFeedType());
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            resultHandler.execute(feedsResult);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE MQ Listener Error, message: {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            saveConsumeErrorOperationLog(body);
        }
    }

    private void saveConsumeErrorOperationLog(String message) {
        AmazonPublishOperationLog operationLog = new AmazonPublishOperationLog();
        operationLog.setModId("MQ");
        operationLog.setOpType("PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE");
        operationLog.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
        operationLog.setUser("admin");
        operationLog.setMetaObj(message);
        operationLog.setState(1);
        operationLog.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        amazonPublishOperationLogService.insert(operationLog);
    }
}
