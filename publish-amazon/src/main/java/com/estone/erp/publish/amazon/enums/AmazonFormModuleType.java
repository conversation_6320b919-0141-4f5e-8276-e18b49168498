package com.estone.erp.publish.amazon.enums;

/**
 * <AUTHOR>
 * @date 2020/9/11 18:29
 * @description
 */
public enum AmazonFormModuleType {

    //分类路径导入
    US_TYPE_PATH,

    //US分类和其他站点分类映射
    US_TYPE_OTHER_MAPPING,

    //US站点分类与系统分类映射
    US_TYPE_SYS_REL,

    //分类路径与模板关联关系
    TYPE_TEMPLATE_REL,

    //分类路径和ProductType配置
    TYPE_PRODUCT_TYPE_CONF,

    //模板类型和ProductType配置
    TEMPLATE_PRODUCT_TYPE_CONF,

    //ProductType和VariationTheme关系配置
    PRODUCT_TYPE_VT_REL,

    //模板表头导出字段配置
    TEMPLATE_HEADER_CONF,

    //分类路径导出表头字段配置
    TYPE_HEADER_CONF,

    //分类路径ProductType默认属性配置
    TYPE_PRODUCT_TYPE_VALUE_CONF,

    //模板类型和ProductType默认属性值配置
    TEMPLATE_PRODUCT_TYPE_VALUE_CONF,
    ;

}
