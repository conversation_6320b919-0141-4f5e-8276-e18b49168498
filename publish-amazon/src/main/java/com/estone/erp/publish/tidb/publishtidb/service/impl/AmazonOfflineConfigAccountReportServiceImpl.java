package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigAccountReport;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonOfflineConfigAccountReportMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigAccountReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * amazon任务下架链接报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Service
public class AmazonOfflineConfigAccountReportServiceImpl extends ServiceImpl<AmazonOfflineConfigAccountReportMapper, AmazonOfflineConfigAccountReport> implements AmazonOfflineConfigAccountReportService {

    @Override
    public List<String> aggregateTotalTable(Integer configId, Integer status, LocalDateTime latestTime) {
        if (configId == null || latestTime == null || status == null) {
            return Collections.emptyList();
        }
        return baseMapper.aggregateTotalTable(configId, status, latestTime);
    }
}
