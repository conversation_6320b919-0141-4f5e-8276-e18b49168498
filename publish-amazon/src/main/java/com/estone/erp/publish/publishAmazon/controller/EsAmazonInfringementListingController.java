package com.estone.erp.publish.publishAmazon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonPerformanceInfringementInfo;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonPerformanceInfringementInfoRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonPerformanceInfringementUpdateRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonPerformanceInfringementInfoService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效侵权
 */
@RestController
@RequestMapping("esInfringementListing")
@Slf4j
public class EsAmazonInfringementListingController {

    @Autowired
    private EsAmazonPerformanceInfringementInfoService esAmazonPerformanceInfringementInfoService;
    @Resource
    private AmazonProductListingService amazonProductListingService;

    @PostMapping
    public ApiResult<?> postEsAmazonProductListing(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        try {
            switch (method) {
                case "searchEsAmazonInfringementListing":
                    CQuery<EsAmazonPerformanceInfringementInfoRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsAmazonPerformanceInfringementInfoRequest>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    EsAmazonPerformanceInfringementInfoRequest search = cquery.getSearch();

                    /*权限控制----start*/
                    //如果传入店铺为空并且不是超管或者最高权限者，则查询当前登录人下级列表
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
                    if (!superAdminOrEquivalent.isSuccess()) {
                        return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                    }
                    if (CollectionUtils.isEmpty(cquery.getSearch().getSaleAccount()) && !superAdminOrEquivalent.getResult()) {
                        //查询销售对应店铺列表
                        ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                        if (!authorAccountListResult.isSuccess()) {
                            return ApiResult.newError(authorAccountListResult.getErrorMsg());
                        }
                        //查询销售对应店铺列表
                        List<String> authorAccountList = authorAccountListResult.getResult();
                        if (CollectionUtils.isEmpty(authorAccountList)) {
                            return ApiResult.newError("未查询到可用店铺列表！");
                        }
                        cquery.getSearch().setSaleAccount(authorAccountList);
                    }
                    /*权限控制----end*/

                    Page<EsAmazonPerformanceInfringementInfo> page = esAmazonPerformanceInfringementInfoService.page(search, cquery.getLimit(), cquery.getOffset());
                    List<EsAmazonPerformanceInfringementInfo> content = page.getContent();
                    if (CollectionUtils.isNotEmpty(content)) {
                        List<String> accountNumbers = content.stream().map(EsAmazonPerformanceInfringementInfo::getSaleAccount).collect(Collectors.toList());
                        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);
                        for (EsAmazonPerformanceInfringementInfo infringementInfo : content) {
                            Triple<String, String, String> triple = saleSuperiorMap.get(infringementInfo.getSaleAccount());
                            if (triple != null) {
                                infringementInfo.setSalesMan(triple.getLeft());
                                infringementInfo.setSalesTeamLeader(triple.getMiddle());
                                infringementInfo.setSalesSupervisorName(triple.getRight());
                            }
                        }
                    }
                    return ApiResult.newSuccess(page);
                case "confirmEsAmazonInfringementListing":
                    CQuery<List<EsAmazonPerformanceInfringementUpdateRequest>> confirm = requestParam.getArgsValue(new TypeReference<CQuery<List<EsAmazonPerformanceInfringementUpdateRequest>>>() {
                    });
                    Asserts.isTrue(confirm != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(confirm.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    List<EsAmazonPerformanceInfringementUpdateRequest> confirmSearch = confirm.getSearch();
                    if (StringUtils.isBlank(confirmSearch.get(0).getRemark())) {
                        return ApiResult.newError("请填写备注！");
                    }
                    //获取操作人
                    String userName = WebUtils.getUserName();
                    //从Redis获取登录人信息
                    NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
                    }, userName);
                    String user;
                    if (newUser == null) {
                        user = "(查询用户系统失败：" + userName + ")";
                    } else {
                        user = newUser.getEmployeeNo() + "-" + newUser.getName();
                    }
                    for (EsAmazonPerformanceInfringementUpdateRequest confirmRequest : confirmSearch) {
                        //查询侵权绩效数据
                        List<EsAmazonPerformanceInfringementInfo> allById = esAmazonPerformanceInfringementInfoService.getAllById(confirmRequest.getId());
                        if (CollectionUtils.isEmpty(allById)) {
                            return ApiResult.newError("未查询出绩效侵权有效数据！");
                        }
                        //逐条处理
                        for (EsAmazonPerformanceInfringementInfo infringementInfo : allById) {
                            if (infringementInfo.getConfirmStatus()) {
                                continue;
                            }
                            AmazonProductListingExample example = new AmazonProductListingExample();
                            AmazonProductListingExample.Criteria criteria = example.createCriteria();
                            //根据店铺加sellerSku确定一条数据
                            criteria.andAccountNumberEqualTo(infringementInfo.getSaleAccount());
                            criteria.andSellerSkuEqualTo(infringementInfo.getSellerSku());
                            //查询产品数据
                            List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectByExample(example, infringementInfo.getSite());
                            if (CollectionUtils.isEmpty(amazonProductListings)) {
                                return ApiResult.newError("未查询出有效产品数据！店铺:" + infringementInfo.getSaleAccount() + ",sellerSku:" + infringementInfo.getSellerSku());
                            }
                            AmazonProductListing confirmListing = amazonProductListings.get(0);
                            if (StringUtils.isBlank(confirmListing.getSite()) || StringUtils.isBlank(confirmListing.getSellerSku()) || StringUtils.isBlank(confirmListing.getAccountNumber())) {
                                return ApiResult.newError("请求数据不完整");
                            }

                            //下架产品
                            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Infringement_Listing_Delete);
                            deleteAmazonListingDto.setRemarkParam(user);
                            String retireMsg = DeleteAmazonListingUtils.batchRetireProduct(new ArrayList<>(Arrays.asList(confirmListing)), null, deleteAmazonListingDto);
                            if (StringUtils.isNotBlank(retireMsg)) {
                                return ApiResult.newError(retireMsg);
                            }

                            //删除成功 TODO
                            //if (result.isSuccess()) {
                                //修改确认状态
                                infringementInfo.setConfirmStatus(true);
                                //修改下架状态
                                infringementInfo.setIsOnline(false);
                                infringementInfo.setConfirmMan(user);
                                infringementInfo.setConfirmTime(new Date());
                                infringementInfo.setRemark(confirmRequest.getRemark());
                                esAmazonPerformanceInfringementInfoService.save(infringementInfo);
                            //}
                        }
                    }
                    return ApiResult.newSuccess("确认完成！");

            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            return ApiResult.newError("参数解析错误");
        }

        return ApiResult.newSuccess();
    }

}
