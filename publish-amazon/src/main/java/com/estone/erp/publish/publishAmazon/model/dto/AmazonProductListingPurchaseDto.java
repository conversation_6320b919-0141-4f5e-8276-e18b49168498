package com.estone.erp.publish.publishAmazon.model.dto;

import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import lombok.Data;

/**
 * @Auther yucm
 * @Date 2023/6/13
 */
@Data
public class AmazonProductListingPurchaseDto {

    /**
     * 子asin码
     */
    private String sonAsin;

    /**
     * 卖家sku
     */
    private String sellerSku;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 名称（标题）
     */
    private String itemName;

    public AmazonProductListingPurchaseDto(){}

    public AmazonProductListingPurchaseDto(EsAmazonProductListing esAmazonProductListing){
        this.sonAsin = esAmazonProductListing.getSonAsin();
        this.sellerSku = esAmazonProductListing.getSellerSku();
        this.articleNumber = esAmazonProductListing.getArticleNumber();
        this.itemName = esAmazonProductListing.getItemName();
    }
}
