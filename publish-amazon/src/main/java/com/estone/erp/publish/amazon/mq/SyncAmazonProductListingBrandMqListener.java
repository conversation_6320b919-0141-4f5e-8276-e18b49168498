package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.enums.AmazonListingItemStatus;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * @Description: ${同步品牌}
 */
@Slf4j
public class SyncAmazonProductListingBrandMqListener implements ChannelAwareMessageListener {

    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    private static final String[] fileds = {"sellerSku", "sonAsin", "site"};
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            return;
        }
        //log.info("AmazonListingGrossPrafitMqListener message body -> {}", body);

        Boolean isSuccess = doService(body);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        Boolean isSuccess = true;
        try {
            AmazonProductListing amazonProductListing = JSON.parseObject(body, AmazonProductListing.class);
            String accountNumber= amazonProductListing.getAccountNumber();
            String site = amazonProductListing.getSite();
            if (StringUtils.isEmpty(accountNumber) || StringUtils.isEmpty(site)){
                log.warn("账号或者站点信息为空" + accountNumber + site);
                return false;
            }

            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(accountNumber);
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setItemStatus(AmazonListingItemStatus.Active.getStatusCode());
            esAmazonProductListingRequest.setIsExistBrandName(false);
            esAmazonProductListingRequest.setFields(fileds);
            int executorTask = esAmazonProductListingService.scrollQueryExecutorTask(esAmazonProductListingRequest, listings -> {
                try {
                    amazonProductListingService.syncAmazonListingDetail(accountNumber, listings);
                } catch (Exception e) {
                    log.error("同步品牌信息异常 -> {}", e.getMessage());
                }
            });
            log.info("正在后台同步" + accountNumber + "账号品牌相关信息" + "size:" + executorTask);
        } catch (Exception e) {
            log.error("同步品牌信息异常 -> {}", body, e);
        }
        return isSuccess;
    }
}