package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.common.util.LogPrintUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.bo.ImageSimResult;
import com.estone.erp.publish.amazon.model.request.ReportSolutionResponseDO;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.HttpUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpMethod;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-08-02 16:54
 */
public class AmazonReportSolutionUtil extends AbstractHttpClient {

    public static ApiResult<ReportSolutionResponseDO> matchReportSolution(List<String> errorMassages) {
        try {
            String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "AMAZON_REPORT2", 60);
            JSONObject param = new JSONObject();
            param.put("report_describe", errorMassages);
            HttpParams<String> httpParams = new HttpParams<>();
            httpParams.setUrl(systemParamValue);
            httpParams.setHttpMethod(HttpMethod.POST);
            httpParams.setBody(param.toJSONString());
            ReportSolutionResponseDO solutionResponse = HttpUtils.exchange(httpParams, ReportSolutionResponseDO.class);
            if (solutionResponse != null) {
                return ApiResult.newSuccess(solutionResponse);
            }
            return ApiResult.newError("response is null");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    public static ApiResult<ImageSimResult> newCalculateImgsSim(String httpUtl, String url, List<String> image) {
        try {
            JSONObject param = new JSONObject();
            param.put("asin_url", url);
            param.put("sku_url", image);
            HttpPost httpPost = new HttpPost();
            httpPost.setURI(new URI(httpUtl));
            StringEntity entity = new StringEntity(param.toJSONString(), StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            // 阻塞了 6s等待，看数据，成功数据
            return getResultString(getHttpClient(6000, 2).execute(httpPost), new TypeReference<ImageSimResult>() {
            });
        } catch (Exception e) {
            return ApiResult.newError(LogPrintUtil.getMinimumReverseStackCause(e));
        }
    }

    public static String getAllImageHttpUrl(){
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "AMAZON_IMGS_SIM", null);
        if (StringUtils.isNotBlank(systemParamValue)) {
            return systemParamValue.trim();
        } else {
            // 避免缓存不存在
            return "http://192.168.4.252:5209/ai_publish/calculate_imgs_sim";
        }
    }

    /**
     *  http://192.168.4.252:5209/ai_publish/calculate_imgs_sim 资源更多，优先保证增量 算法15个进程
     *   http://192.168.4.253:5201/ai_publish/calculate_imgs_sim 算法10个进程
     * @return
     */
    public static String getAddImageHttpUrl(){
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "AMAZON_IMGS_SIM_ADD", null);
        if (StringUtils.isNotBlank(systemParamValue)) {
            return systemParamValue.trim();
        } else {
            // 避免缓存不存在
            return "http://192.168.4.253:5201/ai_publish/calculate_imgs_sim";
        }
    }

    private static  <T> ApiResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<T> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        try {
            try {
                String result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    return ApiResult.newError(result);
                }
                T t = JSON.parseObject(result, reference);
                return ApiResult.newSuccess(t);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }
}
