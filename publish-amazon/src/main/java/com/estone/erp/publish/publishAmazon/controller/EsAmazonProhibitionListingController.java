package com.estone.erp.publish.publishAmazon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProhibitionInfringementInfo;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonPerformanceInfringementUpdateRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProhibitionInfringementInfoRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProhibitionInfringementInfoService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 禁售侵权
 */
@RestController
@RequestMapping("esProhibitionListing")
@Slf4j
public class EsAmazonProhibitionListingController {

    @Autowired
    private EsAmazonProhibitionInfringementInfoService esAmazonProhibitionInfringementInfoService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private SaleAccountService saleAccountService;

    @PostMapping
    public ApiResult<?> postEsAmazonProductListing(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        try {
            switch (method) {
                case "searchEsAmazonProhibitionListing":
                    CQuery<EsAmazonProhibitionInfringementInfoRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsAmazonProhibitionInfringementInfoRequest>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    EsAmazonProhibitionInfringementInfoRequest search = cquery.getSearch();

                    /*权限控制----start*/
                    //如果传入店铺为空并且不是超管或者最高权限者，则查询当前登录人下级列表
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
                    if (!superAdminOrEquivalent.isSuccess()) {
                        return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                    }
                    if (CollectionUtils.isEmpty(cquery.getSearch().getSaleAccount()) && !superAdminOrEquivalent.getResult()) {
                        //查询销售对应店铺列表
                        ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                        if (!authorAccountListResult.isSuccess()) {
                            return ApiResult.newError(authorAccountListResult.getErrorMsg());
                        }
                        //查询销售对应店铺列表
                        List<String> authorAccountList = authorAccountListResult.getResult();
                        if (CollectionUtils.isEmpty(authorAccountList)) {
                            return ApiResult.newError("未查询到可用店铺列表！");
                        }
                        cquery.getSearch().setSaleAccount(authorAccountList);
                    }
                    // 如果查询账号状态
                    List<SaleAccount> saleAccounts = new ArrayList<>();
                    List<String> accountStatusList = search.getAccountStatusList();
                    if (CollectionUtils.isNotEmpty(accountStatusList)) {
                        saleAccounts = this.handleStatus(search);
                        if (CollectionUtils.isEmpty(saleAccounts)) {
                            return ApiResult.newError("es查询不到账号状态信息");
                        }
                    }
                    /*权限控制----end*/

                    Page<EsAmazonProhibitionInfringementInfo> page = esAmazonProhibitionInfringementInfoService.page(search, cquery.getLimit(), cquery.getOffset());
                    List<EsAmazonProhibitionInfringementInfo> content = page.getContent();
                    if (CollectionUtils.isNotEmpty(content)) {
                        List<String> accountNumbers = content.stream().map(EsAmazonProhibitionInfringementInfo::getSaleAccount).collect(Collectors.toList());
                        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);
                        for (EsAmazonProhibitionInfringementInfo infringementInfo : content) {
                            Triple<String, String, String> triple = saleSuperiorMap.get(infringementInfo.getSaleAccount());
                            if (triple != null) {
                                infringementInfo.setSalesMan(triple.getLeft());
                                infringementInfo.setSalesTeamLeader(triple.getMiddle());
                                infringementInfo.setSalesSupervisorName(triple.getRight());
                            }
                        }
                    }
                    return ApiResult.newSuccess(page);
                case "confirmEsAmazonProhibitionListing":
                    CQuery<List<EsAmazonPerformanceInfringementUpdateRequest>> confirm = requestParam.getArgsValue(new TypeReference<CQuery<List<EsAmazonPerformanceInfringementUpdateRequest>>>() {
                    });
                    Asserts.isTrue(confirm != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(confirm.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    List<EsAmazonPerformanceInfringementUpdateRequest> confirmSearch = confirm.getSearch();
                    if (StringUtils.isBlank(confirmSearch.get(0).getRemark())) {
                        return ApiResult.newError("请填写备注！");
                    }
                    //获取操作人
                    String userName = WebUtils.getUserName();
                    //从Redis获取登录人信息
                    NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
                    }, userName);
                    String user;
                    if (newUser == null) {
                        user = "(查询用户系统失败：" + userName + ")";
                    } else {
                        user = newUser.getEmployeeNo() + "-" + newUser.getName();
                    }
                    for (EsAmazonPerformanceInfringementUpdateRequest confirmRequest : confirmSearch) {
                        //查询侵权绩效数据
                        List<EsAmazonProhibitionInfringementInfo> allById = esAmazonProhibitionInfringementInfoService.getAllById(confirmRequest.getId());
                        if (CollectionUtils.isEmpty(allById)) {
                            return ApiResult.newError("未查询出禁售侵权有效数据！");
                        }
                        //逐条处理
                        for (EsAmazonProhibitionInfringementInfo infringementInfo : allById) {
                            if (infringementInfo.getConfirmStatus()) {
                                continue;
                            }
                            AmazonProductListingExample example = new AmazonProductListingExample();
                            AmazonProductListingExample.Criteria criteria = example.createCriteria();
                            //根据店铺加sellerSku确定一条数据
                            criteria.andAccountNumberEqualTo(infringementInfo.getSaleAccount());
                            criteria.andSellerSkuEqualTo(infringementInfo.getSellerSku());
                            List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectByExample(example, infringementInfo.getSite());
                            if (CollectionUtils.isEmpty(amazonProductListings)) {
                                return ApiResult.newError("未查询出有效产品数据！店铺:" + infringementInfo.getSaleAccount() + ",sellerSku:" + infringementInfo.getSellerSku());
                            }
                            AmazonProductListing confirmListing = amazonProductListings.get(0);
                            if (StringUtils.isBlank(confirmListing.getSite()) || StringUtils.isBlank(confirmListing.getSellerSku()) || StringUtils.isBlank(confirmListing.getAccountNumber())) {
                                return ApiResult.newError("请求数据不完整");
                            }
                            //下架产品
                            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Forbidden_Listing_Delete);
                            deleteAmazonListingDto.setRemarkParam(user);
                            String retireMsg = DeleteAmazonListingUtils.batchRetireProduct(new ArrayList<>(Arrays.asList(confirmListing)), null, deleteAmazonListingDto);
                            if (StringUtils.isNotBlank(retireMsg)) {
                                return ApiResult.newError(retireMsg);
                            }
                            //删除成功
                            //if (result.isSuccess()) {
                                //修改确认状态
                                infringementInfo.setConfirmStatus(true);
                                infringementInfo.setConfirmStatusCode(1);
                                //修改下架状态
                                infringementInfo.setIsOnline(false);
                                infringementInfo.setConfirmMan(user);
                                infringementInfo.setConfirmTime(new Date());
                                infringementInfo.setRemark(confirmRequest.getRemark());
                                esAmazonProhibitionInfringementInfoService.save(infringementInfo);
                          //  }
                        }
                    }
                    return ApiResult.newSuccess("确认完成！");

            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            return ApiResult.newError("参数解析错误");
        }

        return ApiResult.newSuccess();
    }

    /**
     * 处理账号状态
     * @return errorMsg
     */
    private List<SaleAccount> handleStatus(EsAmazonProhibitionInfringementInfoRequest esAmazonProhibitionInfringementInfoRequest) {
        List<SaleAccount> saleAccounts = new ArrayList<>();

        List<String> accountStatusList = esAmazonProhibitionInfringementInfoRequest.getAccountStatusList();
        List<String> accounts = esAmazonProhibitionInfringementInfoRequest.getSaleAccount();

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isNotEmpty(accounts)) {
            request.setAccountNumberList(accounts);
        }
        if (CollectionUtils.isNotEmpty(accountStatusList)) {
            request.setAccountStatusList(accountStatusList);
        }
        String[] withFields = {"accountNumber","accountStatus"};
        try {
            saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(saleAccounts)) {
            accounts = saleAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            esAmazonProhibitionInfringementInfoRequest.setSaleAccount(accounts);
            esAmazonProhibitionInfringementInfoRequest.setAccountStatusList(null);
        }

        return saleAccounts;
    }

    /**
     * 临时导出 禁售侵权数据 数据
     * @param response
     * @return
     */
    @RequestMapping(value = "/exportListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportListing( HttpServletResponse response) {

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "禁售侵权数据.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
            List<String> sku = new ArrayList<>(Arrays.asList("2SS310793","2SS310781-1","2SS310781-2","2SS310781-4","2SS310781-3","2SS310781-A","2SS310902-1","2SS310902-2","2SS310902-3","2SS310902-4","2SS310902-5","2SS310902-A","2SS310992-1","2SS310992-2","2SS310992-3","2SS310992-4","2SS310992-5","2SS310992-6","2SS310992-7","2SS310992-8","2SS310992-9","2SS310992-A","2SS310994","11JJ101717-6","11JJ101717-4","11JJ101717-7","11JJ101717-5","11JJ101717-14","11JJ101717-8","11JJ101717-11","11JJ101717-12","11JJ101717-13","11JJ101717-1","11JJ101717-3","11JJ101717-2","2SS311143-1","2SS311143-2","2SS311273-2","2SS311273-1","2SS311329-13","2SS311329-8","2SS311329-7","2SS311329-10","2SS311329-9","2SS311329-12","2SS311329-11","2SS311329-A","2SS311329-2","2SS311329-1","2SS311329-4","2SS311329-3","2SS311329-6","2SS311329-5","2SS311306-A","2SS311306-15","2SS311306-14","2SS311306-17","2SS311306-16","2SS311306-2","2SS311306-7","2SS311306-9","2SS311306-8","2SS311306-11","2SS311306-10","2SS311306-13","2SS311306-12","2SS311306-1","2SS311306-4","2SS311306-3","2SS311306-6","2SS311306-5","2SS311362-2","2SS311362-1","2SS311362-4","2SS311362-3","2SS311362-6","2SS311362-5","2SS311362-A","2SS311362-7","2SS311464-A","2SS311464-2","2SS311464-1","11JJ104256","11JJ501356-6","11JJ501356-5","11JJ501356-S","11JJ501356-2","11JJ501356-1","11JJ501356-4","11JJ501356-3","7AK201937-5","7AK201937-4","7AK201937-6","7AK201937-1","7AK201937-3","7AK201937-2","13AC2001621","5AC1106744-BL","5AC1106744-OR","5AC1106744-GN","5AC1106727-BL","5AC1106727-OR","5AC1106727-GN","13AC2001781-B","13AC2001781-A","13AC2001781-D","13AC2001781-C","13AC2001781-F","13AC2001781-E","2SS313427-7","2SS313427-8","2SS313427-5","2SS313427-6","2SS313427-3","2SS313427-4","2SS313427-1","2SS313427-2","2SS313446-A","2SS313485-3","2SS313485-4","2SS313485-1","2SS313485-2","2SS313485-5","2SS313668-A","2SS313668-1","2SS313668-2","13AC2002549-B","13AC2002549-A","11JJ502433-11","11JJ502433-12","11JJ502433-13","11JJ502433-14","11JJ502433-15","11JJ502433-16","11JJ502433-1","11JJ502433-2","11JJ502433-3","11JJ502433-4","11JJ502433-5","11JJ502433-6","11JJ502433-7","11JJ502433-8","11JJ502433-9","11JJ502433-10","2SS316710-T","2SS316710-BL","2SS317478-BL","2SS317478-T","13AC2003747-D","13AC2003747-E","13AC2003747-B","13AC2003747-C","13AC2003747-A","11JJ107585-B","11JJ107585-A","13AC2003887","2SS317771-BK-L","2SS317771-BK-S","2SS317771-BL-L","2SS317771-BL-S","2SS317771-T-L","2SS317771-T-S","2SS317814-A","2SS317814-6","2SS317814-5","2SS317823-BL-2","2SS317823-BL-1","2SS317823-BK-2","2SS317823-BK-1","2SS317823-T-2","2SS317823-T-1","2SS317901-BL-1","2SS317901-BL-2","2SS317901-BK-1","2SS317901-BK-2","2SS317901-T-1","2SS317901-T-2","2SS317898-T","2SS317898-BL","2SS317898-BK","7AK204758","11JJ108753-5","11JJ108753-1","11JJ108753-2","11JJ108753-3","11JJ108753-4","5AC706417-A","5AC706417-B","5AC706460-W","5AC706460-GN","5AC706460-A","5AC706460-PK"));
            request.setSku(sku);
            //未确认
            request.setConfirmStatus(false);
            request.setConfirmStatusCode(0);
            request.setForbidType(new ArrayList<>(Arrays.asList("侵权")));
            // 查询需导出数据的总数
            Page<EsAmazonProhibitionInfringementInfo> pageResult = esAmazonProhibitionInfringementInfoService.page(request, 1000, 0);
            long total = pageResult.getTotalElements();
            log.warn("============total=========" + total);
            String[] headers = {"店铺名","站点","子asin","sellerSku","sku","禁售类型","禁售原因","30天销量","在线列表状态",
                    "系统标记禁售平台","确认状态","产品开发侵权审核人员","销售","销售组长","销售主管","首次上架时间","匹配规则时间"};
            int pageSize = 1000;
            long totalPages = (long) Math.ceil((double) total / pageSize);
            List<EsAmazonProhibitionInfringementInfo> esAmazonProhibitionInfringementInfoList = new ArrayList<>();
            List<String> idList = new ArrayList<>();
            List<String> sellerSkuList = new ArrayList<>();
            /*for (int i = 0;i< totalPages;i++){
                Page<EsAmazonProhibitionInfringementInfo> result = esAmazonProhibitionInfringementInfoService.pageByCondition(request, pageSize,i);
                if (CollectionUtils.isNotEmpty(result.getContent())) {
                    esAmazonProhibitionInfringementInfoList.addAll(result.getContent());
                    idList.addAll(esAmazonProhibitionInfringementInfoList.stream().map(o -> o.getId()).collect(Collectors.toList()));
                    sellerSkuList.addAll(esAmazonProhibitionInfringementInfoList.stream().map(o -> o.getSellerSku()).collect(Collectors.toList()));
                }
            }*/

            List<String> removeIdList = new ArrayList<>(Arrays.asList("US-qyshmxw_11JJ101717-11_FBMUSBG","UK-lijeihau2s2_11JJ101717-11_TEMOOUESPXXA","UK-lijeihau2s2_11JJ101717-13_TEMOOUESPXXA","US-qyshmxw_11JJ101717-2_FBMUSBG","US-qyshmxw_11JJ101717-7_FBMUSBG","UK-lijeihau2s2_11JJ101717-7_TEMOOUESPXXA","UK-zhang1ding2uk_11JJ104256_HINARearr","UK-lijeihau2s2_11JJ107585-A_TEMOOUESPXXA","UK-lijeihau2s2_11JJ107585-B_TEMOOUESPXXA","US-gzjiawei12_11JJ108753-1_DYZUSBG","US-gzjiawei12_11JJ108753-4_DYZUSBG","FR-zhang1ding2uk_11JJ108753-4_HINARearr","UK-yikangliu2024_11JJ501356-1_UKLYK1","ES-lijeihau2s2_11JJ502433-10_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-11_TEMOOUESPXXA","UK-caoheyaidius_11JJ502433-13_CHYJP","ES-lijeihau2s2_11JJ502433-13_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-15_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-16_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-2_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-3_TEMOOUESPXXA","IT-caoheyaidius_11JJ502433-5_CHYJP","ES-lijeihau2s2_11JJ502433-5_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-6_TEMOOUESPXXA","JP-caoheyaidius_11JJ502433-7_CHYJP","FR-zhang1ding2uk_11JJ502433-7_HINARearr","ES-lijeihau2s2_11JJ502433-7_TEMOOUESPXXA","UK-caoheyaidius_11JJ502433-9_CHYJP","ES-lijeihau2s2_11JJ502433-9_TEMOOUESPXXA","IT-5fuyouzhang_13AC2001781-A_ZYFuk","UK-lijeihau2s2_13AC2001781-B_TEMOOUESPXXA","UK-lijeihau2s2_13AC2001781-D_TEMOOUESPXXA","FR-yikangliu2024_13AC2001781-D_UKLYK1","FR-5fuyouzhang_13AC2001781-D_ZYFuk","UK-lijeihau2s2_13AC2001781-E_TEMOOUESPXXA","UK-lijeihau2s2_13AC2001781-F_TEMOOUESPXXA","DE-zhouwentao0109_13AC2002549-A_zwtde0109","ES-zhouwentao0109_13AC2002549-B_zwtde0109","US-CAIyan8ew_13AC2003747-A_PyanhdUSGM","UK-zhang_ruii3_13AC2003747-E_zhGrui1","US-zhaoshaou8sd_13AC2003887_ZShao1","DE-zhouwentao0109_13AC2003887_zwtde0109","IT-5fuyouzhang_2SS310781-1_ZYFuk","DE-5fuyouzhang_2SS310781-2_ZYFuk","DE-5fuyouzhang_2SS310781-A_ZYFuk","DE-fangxin_2233_2SS310793_999ljxGM","UK-5fuyouzhang_2SS310793_ZYFuk","UK-lijeihau2s2_2SS310902-4_TEMOOUESPXXA","FR-kilbackbostwickbidl19_2SS310992-4_haosyg","UK-lijeihau2s2_2SS310992-5_TEMOOUESPXXA","UK-lijeihau2s2_2SS310992-7_TEMOOUESPXXA","FR-yikangliu2024_2SS310992-7_UKLYK1","UK-lijeihau2s2_2SS311143-1_TEMOOUESPXXA","FR-5fuyouzhang_2SS311143-1_ZYFuk","CA-qqwqqw87_2SS311306-1_OIJNB","CA-qqwqqw87_2SS311306-4_OIJNB","UK-lijeihau2s2_2SS311464-1_TEMOOUESPXXA","UK-lijeihau2s2_2SS311464-A_TEMOOUESPXXA","DE-futao2121_2SS311464-A_Wkijb","IT-5fuyouzhang_2SS311464-A_ZYFuk","UK-lxm2408_2SS313427-2_UKchxinqu2024","UK-huaanguasong_2SS313427-6_HHS0929","UK-lijeihau2s2_2SS313427-6_TEMOOUESPXXA","FR-yikangliu2024_2SS313427-6_UKLYK1","UK-5fuyouzhang_2SS313427-6_ZYFuk","CA-qqwqqw87_2SS313446-A_OIJNB","UK-haoqINglang4412_2SS313485-1_vsilayPXXA","UK-haoqINglang4412_2SS313485-3_vsilayPXXA","CA-qqwqqw87_2SS316710-BL_OIJNB","UK-lijeihau2s2_2SS316710-BL_TEMOOUESPXXA","FR-5fuyouzhang_2SS316710-BL_ZYFuk","CA-qqwqqw87_2SS316710-T_OIJNB","UK-lijeihau2s2_2SS316710-T_TEMOOUESPXXA","CA-qqwqqw87_2SS317771-BK-L_OIJNB","CA-qqwqqw87_2SS317771-BK-S_OIJNB","CA-qqwqqw87_2SS317771-BL-L_OIJNB","CA-qqwqqw87_2SS317771-BL-S_OIJNB","CA-qqwqqw87_2SS317771-T-L_OIJNB","CA-qqwqqw87_2SS317771-T-S_OIJNB","FR-xi_anchida33_2SS317823-BL-1_ZhouXiaoYU","FR-xi_anchida33_2SS317823-T-2_ZhouXiaoYU","MX-meidongjun_5AC1106727-BL_meidongjun288","ES-5fuyouzhang_5AC1106727-BL_ZYFuk","US-meidongjun_5AC1106727-OR_meidongjun288","MX-meidongjun_5AC1106744-BL_meidongjun288","MX-meidongjun_5AC1106744-GN_meidongjun288","ES-5fuyouzhang_5AC1106744-GN_ZYFuk","MX-meidongjun_5AC1106744-OR_meidongjun288","FR-luo-yi-gs_5AC706460-A_LQUK1009","JP-xi_anchida33_5AC706460-A_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-GN_LQUK1009","US-xi_anchida33_5AC706460-GN_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-PK_LQUK1009","JP-xi_anchida33_5AC706460-PK_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-W_LQUK1009","DE-xi_anchida33_5AC706460-W_ZhouXiaoYU","UK-yikangliu2024_7AK201937-2_UKLYK1","IT-5fuyouzhang_7AK201937-2_ZYFuk","IT-5fuyouzhang_7AK201937-3_ZYFuk","IT-5fuyouzhang_7AK201937-5_ZYFuk","UK-5fuyouzhang_7AK201937-6_ZYFuk","IT-mimo1245_911zbfr_2SS317771-BK-L_XBL","IT-mimo1245_911zbfr_2SS317771-BK-S_HKo","IT-mimo1245_911zbfr_2SS317771-BL-S_q_b","IT-mimo1245_911zbfr_2SS317771-T-L_ohO","IT-mimo1245_911zbfr_2SS317901-BL-1_Dha","IT-mimo1245_911zbfr_2SS317901-BL-2_4I3","IT-mimo1245_911zbfr_2SS317901-T-1_u7q","IT-mimo1245_911zbfr_2SS317901-T-2_CGc","DE-jcbhd2547_95kczj_11JJ502433-12_Mp","DE-jcbhd2547_95kczj_11JJ502433-14_Xa","DE-jcbhd2547_95kczj_11JJ502433-1_re","DE-jcbhd2547_95kczj_11JJ502433-3_R7","DE-jcbhd2547_95kczj_11JJ502433-4_3R","DE-jcbhd2547_95kczj_11JJ502433-6_e3","DE-jcbhd2547_95kczj_11JJ502433-8_ud"));

            esAmazonProhibitionInfringementInfoList.addAll(pageResult.getContent());
            idList.addAll(esAmazonProhibitionInfringementInfoList.stream().map(o -> o.getId()).collect(Collectors.toList()));
            idList.removeAll(removeIdList);
            sellerSkuList.addAll(esAmazonProhibitionInfringementInfoList.stream().map(o -> o.getSellerSku()).collect(Collectors.toList()));
            log.warn("============esAmazonProhibitionInfringementInfoList=========" + esAmazonProhibitionInfringementInfoList.size());
            String sellerskuJson = JSON.toJSONString(sellerSkuList);
            final List<List<String>> awLists = new ArrayList<List<String>>();
            String columns = "isOnline";
            POIUtils.createExcel(headers, esAmazonProhibitionInfringementInfoList, esAmazonProhibitionInfringementInfo -> {
                String accountNumber = esAmazonProhibitionInfringementInfo.getSaleAccount();
                String sellerSku = esAmazonProhibitionInfringementInfo.getSellerSku();
                String site = esAmazonProhibitionInfringementInfo.getSite();
                AmazonProductListingExample example = new AmazonProductListingExample();
                AmazonProductListingExample.Criteria criteria = example.createCriteria();
                criteria.andAccountNumberEqualTo(accountNumber);
                criteria.andSellerSkuEqualTo(sellerSku);
                example.setLimit(1);
                example.setColumns(columns);

                List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectCustomColumnByExample(example, site);
                Boolean isOnline = esAmazonProhibitionInfringementInfo.getIsOnline();
                if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
                    isOnline = amazonProductListingList.get(0).getIsOnline();
                }
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    awList.add(accountNumber);
                    awList.add(site);
                    awList.add(esAmazonProhibitionInfringementInfo.getAsin());
                    awList.add(POIUtils.transferStr2Str(sellerSku));
                    awList.add(esAmazonProhibitionInfringementInfo.getSku());
                    awList.add(esAmazonProhibitionInfringementInfo.getForbidType());
                    awList.add(esAmazonProhibitionInfringementInfo.getCause());
                    awList.add(POIUtils.transferStr2Str(null == esAmazonProhibitionInfringementInfo.getOrder_last_30d_count()? "":esAmazonProhibitionInfringementInfo.getOrder_last_30d_count().toString()));
                    awList.add(isOnline.toString());
                    awList.add(esAmazonProhibitionInfringementInfo.getForbidChannel());
                    awList.add(esAmazonProhibitionInfringementInfo.getConfirmStatus().toString());
                    awList.add(esAmazonProhibitionInfringementInfo.getInfringementCheckMan());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesMan());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesTeamLeader());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesSupervisorName());
                    awList.add(POIUtils.transferStr2Str(esAmazonProhibitionInfringementInfo.getPublishTime()));
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProhibitionInfringementInfo.getMatchTime()))));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 临时导出 禁售侵权数据 数据
     * @param response
     * @return
     */
    @RequestMapping(value = "/exportDataListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportDataListing( HttpServletResponse response) {

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "临时导出禁售侵权数据和在线差异化数据.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
            //未确认
            request.setConfirmStatus(false);
            request.setConfirmStatusCode(0);
            request.setForbidType(new ArrayList<>(Arrays.asList("侵权")));
            // 查询需导出数据的总数
            Page<EsAmazonProhibitionInfringementInfo> pageResult = esAmazonProhibitionInfringementInfoService.page(request, 40000, 0);
            long total = pageResult.getTotalElements();
            log.warn("============total=========" + total);
            String[] headers = {"店铺名","站点","子asin","sellerSku","sku","禁售类型","禁售原因","30天销量","在线列表状态",
                    "系统标记禁售平台","确认状态","产品开发侵权审核人员","销售","销售组长","销售主管","首次上架时间","匹配规则时间"};
            //int pageSize = 1000;
            //long totalPages = (long) Math.ceil((double) total / pageSize);
            /*List<EsAmazonProhibitionInfringementInfo> esAmazonProhibitionInfringementInfoList = new ArrayList<>();
            for (int i = 0;i<= totalPages;i++){
                Page<EsAmazonProhibitionInfringementInfo> result = esAmazonProhibitionInfringementInfoService.pageByCondition(request, pageSize,i);
                if (CollectionUtils.isNotEmpty(result.getContent())) {
                    esAmazonProhibitionInfringementInfoList.addAll(result.getContent());
                }
            }*/
           // log.warn("============esAmazonProhibitionInfringementInfoList=========" + esAmazonProhibitionInfringementInfoList.size());
            List<String> ids = new ArrayList<>(pageResult.getContent().stream().map(o -> o.getId()).collect(Collectors.toList()));
            List<String> fields = new ArrayList<>(Arrays.asList("id"));
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setDownFields(fields);
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setInfringementTypename("侵权");
            esAmazonProductListingRequest.setNormalSale("US,UK,IT,CA,MX,JP,AU,IN,AE,DE,FR,SG,ES,NL,PL,SE,BR,EG,SA,TR");
            EsSaleAccountRequest esSaleAccountRequest = new EsSaleAccountRequest();
            esSaleAccountRequest.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
            List<String> statusList = new ArrayList<>();
            statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
            esSaleAccountRequest.setAccountStatusList(statusList);
            List<String> accountNumberList = EsAccountUtils.getAccountListByEs(esSaleAccountRequest);
            esAmazonProductListingRequest.setAccountNumberList(accountNumberList);
            Page<EsAmazonProductListing> results = esAmazonProductListingService.page(esAmazonProductListingRequest, 40000, 0);
            log.warn("============EsAmazonProductListing=========" + results.getTotalElements());
            List<String> idList = new ArrayList<>(results.getContent().stream().map(o -> o.getId()).collect(Collectors.toList()));
            ids.removeAll(idList);

            final List<List<String>> awLists = new ArrayList<List<String>>();
            List<EsAmazonProhibitionInfringementInfo> amazonProhibitionInfringementInfoList = esAmazonProhibitionInfringementInfoService.getAllById(ids);
            String columns = "isOnline";
            POIUtils.createExcel(headers, amazonProhibitionInfringementInfoList, esAmazonProhibitionInfringementInfo -> {
                String accountNumber = esAmazonProhibitionInfringementInfo.getSaleAccount();
                String sellerSku = esAmazonProhibitionInfringementInfo.getSellerSku();
                String site = esAmazonProhibitionInfringementInfo.getSite();
                AmazonProductListingExample example = new AmazonProductListingExample();
                AmazonProductListingExample.Criteria criteria = example.createCriteria();
                criteria.andAccountNumberEqualTo(accountNumber);
                criteria.andSellerSkuEqualTo(sellerSku);
                example.setLimit(1);
                example.setColumns(columns);

                List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectCustomColumnByExample(example, site);
                Boolean isOnline = esAmazonProhibitionInfringementInfo.getIsOnline();
                if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
                    isOnline = amazonProductListingList.get(0).getIsOnline();
                }
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    awList.add(accountNumber);
                    awList.add(site);
                    awList.add(esAmazonProhibitionInfringementInfo.getAsin());
                    awList.add(POIUtils.transferStr2Str(sellerSku));
                    awList.add(esAmazonProhibitionInfringementInfo.getSku());
                    awList.add(esAmazonProhibitionInfringementInfo.getForbidType());
                    awList.add(esAmazonProhibitionInfringementInfo.getCause());
                    awList.add(POIUtils.transferStr2Str(null == esAmazonProhibitionInfringementInfo.getOrder_last_30d_count()? "":esAmazonProhibitionInfringementInfo.getOrder_last_30d_count().toString()));
                    awList.add(isOnline.toString());
                    awList.add(esAmazonProhibitionInfringementInfo.getForbidChannel());
                    awList.add(esAmazonProhibitionInfringementInfo.getConfirmStatus().toString());
                    awList.add(esAmazonProhibitionInfringementInfo.getInfringementCheckMan());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesMan());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesTeamLeader());
                    awList.add(esAmazonProhibitionInfringementInfo.getSalesSupervisorName());
                    awList.add(POIUtils.transferStr2Str(esAmazonProhibitionInfringementInfo.getPublishTime()));
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProhibitionInfringementInfo.getMatchTime()))));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量驳回
     * @param ids 勾选数据id
     */
    @PostMapping("/batchReject")
    public ApiResult<String> batchReject(@RequestBody List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("请选择要驳回的记录");
        }
        //获取操作人
        String userName = WebUtils.getUserName();
        //从Redis获取登录人信息
        NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
        }, userName);

        if (newUser == null) {
            return ApiResult.newError("查询用户系统失败：" + userName);
        }
        String user = newUser.getEmployeeNo() + "-" + newUser.getName();
        esAmazonProhibitionInfringementInfoService.batchReject(ids, user);
        return ApiResult.newSuccess("批量驳回成功");
    }



    /**
     * 临时导出 禁售侵权数据 数据
     * @param response
     * @return
     */
   /* @RequestMapping(value = "/checkExportListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> checkExportListing( HttpServletResponse response) {

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "禁售侵权数据.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
            List<String> sku = new ArrayList<>(Arrays.asList("2SS310793","2SS310781-1","2SS310781-2","2SS310781-4","2SS310781-3","2SS310781-A","2SS310902-1","2SS310902-2","2SS310902-3","2SS310902-4","2SS310902-5","2SS310902-A","2SS310992-1","2SS310992-2","2SS310992-3","2SS310992-4","2SS310992-5","2SS310992-6","2SS310992-7","2SS310992-8","2SS310992-9","2SS310992-A","2SS310994","11JJ101717-6","11JJ101717-4","11JJ101717-7","11JJ101717-5","11JJ101717-14","11JJ101717-8","11JJ101717-11","11JJ101717-12","11JJ101717-13","11JJ101717-1","11JJ101717-3","11JJ101717-2","2SS311143-1","2SS311143-2","2SS311273-2","2SS311273-1","2SS311329-13","2SS311329-8","2SS311329-7","2SS311329-10","2SS311329-9","2SS311329-12","2SS311329-11","2SS311329-A","2SS311329-2","2SS311329-1","2SS311329-4","2SS311329-3","2SS311329-6","2SS311329-5","2SS311306-A","2SS311306-15","2SS311306-14","2SS311306-17","2SS311306-16","2SS311306-2","2SS311306-7","2SS311306-9","2SS311306-8","2SS311306-11","2SS311306-10","2SS311306-13","2SS311306-12","2SS311306-1","2SS311306-4","2SS311306-3","2SS311306-6","2SS311306-5","2SS311362-2","2SS311362-1","2SS311362-4","2SS311362-3","2SS311362-6","2SS311362-5","2SS311362-A","2SS311362-7","2SS311464-A","2SS311464-2","2SS311464-1","11JJ104256","11JJ501356-6","11JJ501356-5","11JJ501356-S","11JJ501356-2","11JJ501356-1","11JJ501356-4","11JJ501356-3","7AK201937-5","7AK201937-4","7AK201937-6","7AK201937-1","7AK201937-3","7AK201937-2","13AC2001621","5AC1106744-BL","5AC1106744-OR","5AC1106744-GN","5AC1106727-BL","5AC1106727-OR","5AC1106727-GN","13AC2001781-B","13AC2001781-A","13AC2001781-D","13AC2001781-C","13AC2001781-F","13AC2001781-E","2SS313427-7","2SS313427-8","2SS313427-5","2SS313427-6","2SS313427-3","2SS313427-4","2SS313427-1","2SS313427-2","2SS313446-A","2SS313485-3","2SS313485-4","2SS313485-1","2SS313485-2","2SS313485-5","2SS313668-A","2SS313668-1","2SS313668-2","13AC2002549-B","13AC2002549-A","11JJ502433-11","11JJ502433-12","11JJ502433-13","11JJ502433-14","11JJ502433-15","11JJ502433-16","11JJ502433-1","11JJ502433-2","11JJ502433-3","11JJ502433-4","11JJ502433-5","11JJ502433-6","11JJ502433-7","11JJ502433-8","11JJ502433-9","11JJ502433-10","2SS316710-T","2SS316710-BL","2SS317478-BL","2SS317478-T","13AC2003747-D","13AC2003747-E","13AC2003747-B","13AC2003747-C","13AC2003747-A","11JJ107585-B","11JJ107585-A","13AC2003887","2SS317771-BK-L","2SS317771-BK-S","2SS317771-BL-L","2SS317771-BL-S","2SS317771-T-L","2SS317771-T-S","2SS317814-A","2SS317814-6","2SS317814-5","2SS317823-BL-2","2SS317823-BL-1","2SS317823-BK-2","2SS317823-BK-1","2SS317823-T-2","2SS317823-T-1","2SS317901-BL-1","2SS317901-BL-2","2SS317901-BK-1","2SS317901-BK-2","2SS317901-T-1","2SS317901-T-2","2SS317898-T","2SS317898-BL","2SS317898-BK","7AK204758","11JJ108753-5","11JJ108753-1","11JJ108753-2","11JJ108753-3","11JJ108753-4","5AC706417-A","5AC706417-B","5AC706460-W","5AC706460-GN","5AC706460-A","5AC706460-PK"));
            request.setSku(sku);
            //未确认
            request.setConfirmStatus(false);
            request.setConfirmStatusCode(0);
            request.setForbidType(new ArrayList<>(Arrays.asList("侵权")));
            // 查询需导出数据的总数
            Page<EsAmazonProhibitionInfringementInfo> pageResult = esAmazonProhibitionInfringementInfoService.page(request, 1000, 0);
            long total = pageResult.getTotalElements();
            log.warn("============total=========" + total);
            int pageSize = 1000;
            long totalPages = (long) Math.ceil((double) total / pageSize);
            List<EsAmazonProhibitionInfringementInfo> esAmazonProhibitionInfringementInfoList = new ArrayList<>();
            List<String> idList = new ArrayList<>();
            Set<String> accountNumbers = new HashSet<>();
            List<String> removeIdList = new ArrayList<>(Arrays.asList("US-qyshmxw_11JJ101717-11_FBMUSBG","UK-lijeihau2s2_11JJ101717-11_TEMOOUESPXXA","UK-lijeihau2s2_11JJ101717-13_TEMOOUESPXXA","US-qyshmxw_11JJ101717-2_FBMUSBG","US-qyshmxw_11JJ101717-7_FBMUSBG","UK-lijeihau2s2_11JJ101717-7_TEMOOUESPXXA","UK-zhang1ding2uk_11JJ104256_HINARearr","UK-lijeihau2s2_11JJ107585-A_TEMOOUESPXXA","UK-lijeihau2s2_11JJ107585-B_TEMOOUESPXXA","US-gzjiawei12_11JJ108753-1_DYZUSBG","US-gzjiawei12_11JJ108753-4_DYZUSBG","FR-zhang1ding2uk_11JJ108753-4_HINARearr","UK-yikangliu2024_11JJ501356-1_UKLYK1","ES-lijeihau2s2_11JJ502433-10_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-11_TEMOOUESPXXA","UK-caoheyaidius_11JJ502433-13_CHYJP","ES-lijeihau2s2_11JJ502433-13_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-15_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-16_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-2_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-3_TEMOOUESPXXA","IT-caoheyaidius_11JJ502433-5_CHYJP","ES-lijeihau2s2_11JJ502433-5_TEMOOUESPXXA","ES-lijeihau2s2_11JJ502433-6_TEMOOUESPXXA","JP-caoheyaidius_11JJ502433-7_CHYJP","FR-zhang1ding2uk_11JJ502433-7_HINARearr","ES-lijeihau2s2_11JJ502433-7_TEMOOUESPXXA","UK-caoheyaidius_11JJ502433-9_CHYJP","ES-lijeihau2s2_11JJ502433-9_TEMOOUESPXXA","IT-5fuyouzhang_13AC2001781-A_ZYFuk","UK-lijeihau2s2_13AC2001781-B_TEMOOUESPXXA","UK-lijeihau2s2_13AC2001781-D_TEMOOUESPXXA","FR-yikangliu2024_13AC2001781-D_UKLYK1","FR-5fuyouzhang_13AC2001781-D_ZYFuk","UK-lijeihau2s2_13AC2001781-E_TEMOOUESPXXA","UK-lijeihau2s2_13AC2001781-F_TEMOOUESPXXA","DE-zhouwentao0109_13AC2002549-A_zwtde0109","ES-zhouwentao0109_13AC2002549-B_zwtde0109","US-CAIyan8ew_13AC2003747-A_PyanhdUSGM","UK-zhang_ruii3_13AC2003747-E_zhGrui1","US-zhaoshaou8sd_13AC2003887_ZShao1","DE-zhouwentao0109_13AC2003887_zwtde0109","IT-5fuyouzhang_2SS310781-1_ZYFuk","DE-5fuyouzhang_2SS310781-2_ZYFuk","DE-5fuyouzhang_2SS310781-A_ZYFuk","DE-fangxin_2233_2SS310793_999ljxGM","UK-5fuyouzhang_2SS310793_ZYFuk","UK-lijeihau2s2_2SS310902-4_TEMOOUESPXXA","FR-kilbackbostwickbidl19_2SS310992-4_haosyg","UK-lijeihau2s2_2SS310992-5_TEMOOUESPXXA","UK-lijeihau2s2_2SS310992-7_TEMOOUESPXXA","FR-yikangliu2024_2SS310992-7_UKLYK1","UK-lijeihau2s2_2SS311143-1_TEMOOUESPXXA","FR-5fuyouzhang_2SS311143-1_ZYFuk","CA-qqwqqw87_2SS311306-1_OIJNB","CA-qqwqqw87_2SS311306-4_OIJNB","UK-lijeihau2s2_2SS311464-1_TEMOOUESPXXA","UK-lijeihau2s2_2SS311464-A_TEMOOUESPXXA","DE-futao2121_2SS311464-A_Wkijb","IT-5fuyouzhang_2SS311464-A_ZYFuk","UK-lxm2408_2SS313427-2_UKchxinqu2024","UK-huaanguasong_2SS313427-6_HHS0929","UK-lijeihau2s2_2SS313427-6_TEMOOUESPXXA","FR-yikangliu2024_2SS313427-6_UKLYK1","UK-5fuyouzhang_2SS313427-6_ZYFuk","CA-qqwqqw87_2SS313446-A_OIJNB","UK-haoqINglang4412_2SS313485-1_vsilayPXXA","UK-haoqINglang4412_2SS313485-3_vsilayPXXA","CA-qqwqqw87_2SS316710-BL_OIJNB","UK-lijeihau2s2_2SS316710-BL_TEMOOUESPXXA","FR-5fuyouzhang_2SS316710-BL_ZYFuk","CA-qqwqqw87_2SS316710-T_OIJNB","UK-lijeihau2s2_2SS316710-T_TEMOOUESPXXA","CA-qqwqqw87_2SS317771-BK-L_OIJNB","CA-qqwqqw87_2SS317771-BK-S_OIJNB","CA-qqwqqw87_2SS317771-BL-L_OIJNB","CA-qqwqqw87_2SS317771-BL-S_OIJNB","CA-qqwqqw87_2SS317771-T-L_OIJNB","CA-qqwqqw87_2SS317771-T-S_OIJNB","FR-xi_anchida33_2SS317823-BL-1_ZhouXiaoYU","FR-xi_anchida33_2SS317823-T-2_ZhouXiaoYU","MX-meidongjun_5AC1106727-BL_meidongjun288","ES-5fuyouzhang_5AC1106727-BL_ZYFuk","US-meidongjun_5AC1106727-OR_meidongjun288","MX-meidongjun_5AC1106744-BL_meidongjun288","MX-meidongjun_5AC1106744-GN_meidongjun288","ES-5fuyouzhang_5AC1106744-GN_ZYFuk","MX-meidongjun_5AC1106744-OR_meidongjun288","FR-luo-yi-gs_5AC706460-A_LQUK1009","JP-xi_anchida33_5AC706460-A_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-GN_LQUK1009","US-xi_anchida33_5AC706460-GN_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-PK_LQUK1009","JP-xi_anchida33_5AC706460-PK_ZhouXiaoYU","FR-luo-yi-gs_5AC706460-W_LQUK1009","DE-xi_anchida33_5AC706460-W_ZhouXiaoYU","UK-yikangliu2024_7AK201937-2_UKLYK1","IT-5fuyouzhang_7AK201937-2_ZYFuk","IT-5fuyouzhang_7AK201937-3_ZYFuk","IT-5fuyouzhang_7AK201937-5_ZYFuk","UK-5fuyouzhang_7AK201937-6_ZYFuk","IT-mimo1245_911zbfr_2SS317771-BK-L_XBL","IT-mimo1245_911zbfr_2SS317771-BK-S_HKo","IT-mimo1245_911zbfr_2SS317771-BL-S_q_b","IT-mimo1245_911zbfr_2SS317771-T-L_ohO","IT-mimo1245_911zbfr_2SS317901-BL-1_Dha","IT-mimo1245_911zbfr_2SS317901-BL-2_4I3","IT-mimo1245_911zbfr_2SS317901-T-1_u7q","IT-mimo1245_911zbfr_2SS317901-T-2_CGc","DE-jcbhd2547_95kczj_11JJ502433-12_Mp","DE-jcbhd2547_95kczj_11JJ502433-14_Xa","DE-jcbhd2547_95kczj_11JJ502433-1_re","DE-jcbhd2547_95kczj_11JJ502433-3_R7","DE-jcbhd2547_95kczj_11JJ502433-4_3R","DE-jcbhd2547_95kczj_11JJ502433-6_e3","DE-jcbhd2547_95kczj_11JJ502433-8_ud"));

            esAmazonProhibitionInfringementInfoList.addAll(pageResult.getContent());
            idList.addAll(esAmazonProhibitionInfringementInfoList.stream().map(o -> o.getId()).collect(Collectors.toList()));
            idList.removeAll(removeIdList);

            log.warn("============esAmazonProhibitionInfringementInfoList=========" + esAmazonProhibitionInfringementInfoList.size());
            List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
            List<String> sonAsins = new ArrayList<>();
            for (String id : idList) {
                EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(id);
                esAmazonProductListingList.add(esAmazonProductListing);
                accountNumbers.add(esAmazonProductListing.getAccountNumber());
                sonAsins.add(esAmazonProductListing.getSonAsin());
            }
            HashMap<String,String> accountMap = new HashMap<>();

            for (String accountNumber :accountNumbers) {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
                AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
                // 过滤未授权到sp-api的账号
                if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                    accountMap.put(accountNumber,"下架不执行，店铺未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:" + accountNumber);
                }
            }



            String[] headers = {"店铺名","站点","子asin","父asin","sellerSku","sku","禁售类型","禁售原因","总销量","在线列表状态",
                    "首次上架时间","是否fba","itemType","未下架原因"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            String columns = "isOnline";
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                String accountNumber = esAmazonProductListing.getAccountNumber();
                String sonAsin = esAmazonProductListing.getSonAsin();
                String parentAsin = esAmazonProductListing.getParentAsin();
                String site = esAmazonProductListing.getSite();
                String articleNumber = esAmazonProductListing.getArticleNumber();
                String sellerSku = esAmazonProductListing.getSellerSku();
                String id = accountNumber+ "_" + sellerSku;
                Boolean isOnline = esAmazonProductListing.getIsOnline();
                Integer itemType = esAmazonProductListing.getItemType();
                String msg = null;

                if (accountMap.containsKey(accountNumber)){
                    msg = accountMap.get(accountNumber);

                }

                // sellersku解析规则为 匹配不到货号 的不进行下架
                if (StringUtils.isEmpty(articleNumber) || articleNumber.equals(NOT_ARTICLENUMER)){
                    msg = "货号为空或者 匹配不到货号，不下架";

                }

                // 校验总销量写入时间
                String sale_id = SaleChannel.CHANNEL_AMAZON + "_" + id;
                boolean canDeleted = CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(sale_id);
                if (BooleanUtils.isFalse(canDeleted)) {
                    msg = "总销量写入时间不符合，不下架";

                }

                // 排除FBAasin，不区分店铺，只要是FBA的asin，则不进行下架
                String fbaFlag = "否";
                EsAmazonProductListing esAmazonProductListing1 = new EsAmazonProductListing();
                esAmazonProductListing1.setAccountNumber(accountNumber);
                esAmazonProductListing1.setSonAsin(sonAsin);
                List<EsAmazonProductListing> listings = AmazonListingUtils.filterFBAExistAsin(CommonUtils.arrayAsList(esAmazonProductListing1));
                if (CollectionUtils.isEmpty(listings)) {
                    msg =  String.format("Asin: %s 存在订单FBA库存管理中，不下架", sonAsin);
                    fbaFlag= "是";

                }

                // 公司自注册sku，且是us站点，不下架 （5AC407513,5AC407511-B）
                boolean selfRegisteredSku = DeleteAmazonListingUtils.checkSelfRegisteredSku(articleNumber, site);
                if (selfRegisteredSku) {
                    msg = "公司自注册SKU，US站点不下架";

                }

                // sellerSku 包含handmade 免佣金不下架
                if (StringUtils.containsIgnoreCase(sellerSku, "handmade")) {
                    msg = "sellerSku 包含handmade 免佣金不下架";

                }

                // 排除指定配置的sellerSku
                boolean fixedSellerSku = DeleteAmazonListingUtils.checkConfiguredSellerSku(sellerSku);
                if (fixedSellerSku) {
                    msg = "配置指定sellerSku不下架";

                }

                if (null == itemType) {
                    // itemType 为空不下架
                    msg = String.format("Asin: %s itemType 是空无法判定是否为父体，不下架", sonAsin);

                }else if (AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode() == itemType){
                    // 下架子体、单体
                    msg =  String.format("Asin: %s 是父体，不下架", sonAsin);

                }

                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    awList.add(accountNumber);
                    awList.add(site);
                    awList.add(sonAsin);
                    awList.add(parentAsin);
                    awList.add(POIUtils.transferStr2Str(sellerSku));
                    awList.add(esAmazonProductListing.getArticleNumber());
                    awList.add(esAmazonProductListing.getInfringementTypename());
                    awList.add(esAmazonProductListing.getInfringementObj());
                    awList.add(POIUtils.transferStr2Str(null == esAmazonProductListing.getOrder_num_total()? "":esAmazonProductListing.getOrder_num_total().toString()));
                    awList.add(isOnline.toString());
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOpenDate()))));
                    awList.add(fbaFlag);
                    awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getItemType())));
                    awList.add(msg);


                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }*/
}
