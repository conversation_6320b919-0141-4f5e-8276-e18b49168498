package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTaskOffLinkListingReport;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTaskOffLinkListingReportMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTaskOffLinkListingReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 超额刊登-店铺下架报告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class AmazonTaskOffLinkListingReportServiceImpl extends ServiceImpl<AmazonTaskOffLinkListingReportMapper, AmazonTaskOffLinkListingReport> implements AmazonTaskOffLinkListingReportService {

}
