package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.StringUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SaleSuperiorReidsUtils;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonMustPublishNewProductStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.Data;

import java.util.Date;

/**
 * 必刊登新品
 * <AUTHOR>
 * @date 2023-10-24 10:52
 */
@Data
public class AmazonMustPublishNewProductExVO {

    /**
     * spu
     */
    @ExcelProperty("spu")
    private String spu;

    /**
     * 产品类型 0:单属性, 1:多属性
     */
    @ExcelProperty("产品类型")
    private String productType;

    /**
     * 分配主管
     */
    @ExcelProperty("分配主管")
    private String supervisor;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String saleId;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String saleManager;

    /**
     * 站点详情
     */
    @ExcelProperty({"US","模板状态"})
    private String usPublishStatus;

    @ExcelProperty({"US","禁售"})
    private String usBan;

    /**
     * 站点详情
     */
    @ExcelProperty({"DE","模板状态"})
    private String dePublishStatus;
    @ExcelProperty({"DE","禁售"})
    private String deBan;

    /**
     * 站点详情
     */
    @ExcelProperty({"FR","模板状态"})
    private String frPublishStatus;
    @ExcelProperty({"FR","禁售"})
    private String frBan;

    /**
     * 站点详情
     */
    @ExcelProperty({"UK","模板状态"})
    private String ukPublishStatus;
    @ExcelProperty({"UK","禁售"})
    private String ukBan;


    /**
     * 站点详情
     */
    @ExcelProperty({"IT","模板状态"})
    private String itPublishStatus;
    @ExcelProperty({"IT","禁售"})
    private String itBan;


    /**
     * 站点详情
     */
    @ExcelProperty({"ES","模板状态"})
    private String esPublishStatus;
    @ExcelProperty({"ES","禁售"})
    private String esBan;


    /**
     * 站点详情
     */
    @ExcelProperty({"JP","模板状态"})
    private String jpPublishStatus;
    @ExcelProperty({"JP","禁售"})
    private String jpBan;


    /**
     * 站点详情
     */
    @ExcelProperty({"CA","模板状态"})
    private String caPublishStatus;
    @ExcelProperty({"CA","禁售"})
    private String caBan;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remarks;

    /**
     * 推送时间
     */
    @ExcelProperty("推送时间")
    private Date createdTime;

    /**
     * 进入单品时间
     */
    @ExcelProperty("进入单品时间")
    private Date spuCreatedTime;


    public static AmazonMustPublishNewProductExVO conventToVO(AmazonMustPublishNewProduct record) {
        AmazonMustPublishNewProductExVO vo = new AmazonMustPublishNewProductExVO();
        vo.setSpu(record.getSpu());
        if (record.getProductType() != null) {
            vo.setProductType(record.getProductType() == 0 ? "单属性" : "多属性");
        }

        // 历史数据没有分配主管id
        if (StringUtils.isNotBlank(record.getSupervisorId())) {
            NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
            }, record.getSupervisorId());
            if (newUser != null) {
                String name = newUser.getName();
                vo.setSupervisor(name);
            }
        }

        // 未分配时销售会为空
        if (!StringUtils.isEmpty(record.getSaleId())) {
            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(record.getSaleId());
            if (newUserApiResult.isSuccess()) {
                vo.setSaleId(newUserApiResult.getResult().getName() +"-"+ record.getSaleId());
            }else {
                vo.setSaleId(record.getSaleId());
            }
            vo.setSaleLeader(SaleSuperiorReidsUtils.getSaleSuperior(record.getSaleId(), true));
            vo.setSaleManager(SaleSuperiorReidsUtils.getSaleSuperior(vo.getSaleLeader(), true));
        }

        vo.setUsPublishStatus(getPublishStatusDesc(record.getUsPublishStatus()));
        vo.setUsBan(Boolean.TRUE.equals(record.getUsIsBand()) ? "是" : "否");

        vo.setDePublishStatus(getPublishStatusDesc(record.getDePublishStatus()));
        vo.setDeBan(Boolean.TRUE.equals(record.getDeIsBand()) ? "是" : "否");

        vo.setFrPublishStatus(getPublishStatusDesc(record.getFrPublishStatus()));
        vo.setFrBan(Boolean.TRUE.equals(record.getFrIsBand()) ? "是" : "否");

        vo.setUkPublishStatus(getPublishStatusDesc(record.getUkPublishStatus()));
        vo.setUkBan(Boolean.TRUE.equals(record.getUkIsBand()) ? "是" : "否");

        vo.setItPublishStatus(getPublishStatusDesc(record.getItPublishStatus()));
        vo.setItBan(Boolean.TRUE.equals(record.getItIsBand()) ? "是" : "否");

        vo.setEsPublishStatus(getPublishStatusDesc(record.getEsPublishStatus()));
        vo.setEsBan(Boolean.TRUE.equals(record.getEsIsBand()) ? "是" : "否");

        vo.setJpPublishStatus(getPublishStatusDesc(record.getJpPublishStatus()));
        vo.setJpBan(Boolean.TRUE.equals(record.getJpIsBand()) ? "是" : "否");

        vo.setCaPublishStatus(getPublishStatusDesc(record.getCaPublishStatus()));
        vo.setCaBan(Boolean.TRUE.equals(record.getCaIsBand()) ? "是" : "否");

        vo.setRemarks(record.getRemarks());
        vo.setCreatedTime(record.getCreatedTime());
        vo.setSpuCreatedTime(record.getSpuCreatedTime());
        return vo;
    }
    
    private static String getPublishStatusDesc(Integer publishStatus) {
        if (publishStatus == null) {
            return AmazonMustPublishNewProductStatusEnum.NO_TEMPLATE.getDesc();
        }
        for (AmaoznPublishStatusEnum value : AmaoznPublishStatusEnum.values()) {
            boolean match = value.getStatusCode() == publishStatus;
            if (match) {
                return value.getStatusMsgCn();
            }
        }
        return AmazonMustPublishNewProductStatusEnum.NO_TEMPLATE.getDesc();
    }
}
