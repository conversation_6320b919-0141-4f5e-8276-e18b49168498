package com.estone.erp.publish.amazon.model.dto;

import java.sql.Timestamp;
public class AmazonProcessReportCriteria {
    private String account;
    
    private String skuList;
    
    private String requestType;
    
    private String reportStatus;
    
    private Boolean requestStatus;
    
    private String taskId;
    
    private String createdBy;

    private Integer relationId;

    private String relationType;

    private Timestamp fromCreationDate;

    private Timestamp toCreationDate;

    private Timestamp fromFinishDate;

    private Timestamp toFinishDate;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSkuList() {
        return skuList;
    }

    public void setSkuList(String skuList) {
        this.skuList = skuList;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }


    public String getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus) {
        this.reportStatus = reportStatus;
    }

    public Boolean getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(Boolean requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getRelationId() {
        return relationId;
    }

    public void setRelationId(Integer relationId) {
        this.relationId = relationId;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public Timestamp getFromCreationDate() {
        return fromCreationDate;
    }

    public void setFromCreationDate(Timestamp fromCreationDate) {
        this.fromCreationDate = fromCreationDate;
    }

    public Timestamp getToCreationDate() {
        return toCreationDate;
    }

    public void setToCreationDate(Timestamp toCreationDate) {
        this.toCreationDate = toCreationDate;
    }

    public Timestamp getFromFinishDate() {
        return fromFinishDate;
    }

    public void setFromFinishDate(Timestamp fromFinishDate) {
        this.fromFinishDate = fromFinishDate;
    }

    public Timestamp getToFinishDate() {
        return toFinishDate;
    }

    public void setToFinishDate(Timestamp toFinishDate) {
        this.toFinishDate = toFinishDate;
    }
}
