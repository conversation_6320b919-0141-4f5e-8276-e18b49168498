package com.estone.erp.publish.amazon.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 开发者 AppName 的枚举
 * @Version: 1.0.0
 */
public enum AppNameEnum {
    TUOXINSOFT_APP_NAME("Tuoxinsoft","Tuoxinsoft （jerry）"),
    JINGYUAMZ_APP_NAME("JingYuAmz","JingYuAmz（蔡总）"),
    HAIQITECH_APP_NAME("HAIQI","Haiqitech"),
    TONGTU_APP_NAME("TongTu","通途"),
    ;

    //状态英文
    private String appName;
    //状态中文
    private String appNameCn;

    private AppNameEnum(String appName, String appNameCn) {

        this.appName = appName;
        this.appNameCn = appNameCn;
    }

    public String getAppName() {
        return appName;
    }

    public String getAppNameCn() {
        return appNameCn;
    }

    public static Boolean checkIsExistAppName(String appName){
        if (StringUtils.isBlank(appName)){
            return false;
        }
        AppNameEnum[] appNameEnums = AppNameEnum.values();
        for (AppNameEnum appNameEnum : appNameEnums) {
            if (appName.equalsIgnoreCase(appNameEnum.getAppName())){
                return true;
            }
        }
        return false;
    }
}
