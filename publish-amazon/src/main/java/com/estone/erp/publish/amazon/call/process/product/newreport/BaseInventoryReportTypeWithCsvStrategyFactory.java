package com.estone.erp.publish.amazon.call.process.product.newreport;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.BaseInventoryReportSpTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.InventoryReportSpTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.JPInventoryReportSpTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.base.pms.model.AmazonAccount;

/**
 * 
 * @Description: BaseReportTypeWithCsvStrategy工厂类
 * 
 * @ClassName: BaseReportTypeWithCsvStrategyFactory
 * @Author: Kevin
 * @Date: 2018/10/19
 * @Version: 0.0.1
 */
public class BaseInventoryReportTypeWithCsvStrategyFactory {

    public static BaseInventoryReportSpTypeWithCsvStrategy newInstance(AmazonAccount account, String reportType) {
        if (account == null) {
            return null;
        }

        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        String marketplace = amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId().trim()).getMarketplace();
        if (JPInventoryReportSpTypeWithCsvStrategy.MARKETPLACES.contains(marketplace)
                && JPInventoryReportSpTypeWithCsvStrategy.REPORT_TYPE.equals(reportType)) {
            return new JPInventoryReportSpTypeWithCsvStrategy(account, reportType);
        }else if (InventoryReportSpTypeWithCsvStrategy.REPORT_TYPE.equals(reportType))
            return new InventoryReportSpTypeWithCsvStrategy(account, reportType);
        return null;
    }
}
