package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.amazon.model.dto
 * @Author: sj
 * @CreateTime: 2025-05-14  16:43
 * @Description: TODO
 */
@Data
public class AmazonGetProductTitleAndDescriptionDto {
    /**
     * 子asin，最多一次传20个
     */
    private List<String> asins;
    /**
     * 站点
     */
    private String accountSite;
}
