package com.estone.erp.publish.amazon.enums;

import lombok.Getter;

@Getter
public enum HandlingTimeRuleTypeEnums {
    UPDATE(1, "修改发货期"),
    RESTORE(2, "恢复发货期");

    private final Integer code;
    private final String desc;

    HandlingTimeRuleTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public boolean isTrue(Integer type) {
        return this.getCode().equals(type);
    }
}