package com.estone.erp.publish.amazon.mq.model;

import com.estone.erp.publish.amazon.bo.AmazonProductBO;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.Product;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单个产品数据类
 */
@Data
public class SyncProductMqData {

    /**
     * 标题
     */
    private String title;

    /**
     * 主图图片url
     */
    private String url;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * SellerSku
     */
    private String sellerSku;

    /**
     * 该帐号所有ASIN码
     **/
    private List<String> asins;

    /**
     * 父ASIN
     */
    private String asin;

    /**
     * 是否是多属性
     */
    private boolean isVariant;

    /**
     * AmazonProductBO转换数据
     */
    private AmazonProductBO amazonProductBO;

    /**
     * amazonVariantBOs转换数据
     */
    private Map<String, AmazonVariantBO> variantBOMap = new HashMap<>();

    /**
     * 子属性报告切割数据
     * key：lineId
     * value：报告切割数据
     */
    private Map<String, String[]> variantReportMap = new HashMap<>();

    /**
     * 使用完后清理数据，减轻内存压力
     */
    public void clear() {
        title = null;
        url = null;
        accountNumber = null;
        sellerSku = null;
        asins = null;
        asin = null;
        amazonProductBO = null;
        variantBOMap = null;
        variantReportMap = null;
    }
}
