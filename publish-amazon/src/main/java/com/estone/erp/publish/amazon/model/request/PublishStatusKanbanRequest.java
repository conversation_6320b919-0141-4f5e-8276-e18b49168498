package com.estone.erp.publish.amazon.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-20 16:12
 */
@Data
public class PublishStatusKanbanRequest {
    /**
     * 站点
     */
    private List<String> sites;

    /**
     * 日期
     */
    private String date;

    /**
     * 按最大数量返回多少条
     */
    private Integer topLimit = Integer.MAX_VALUE;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime formTime;

    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime toTime;
}
