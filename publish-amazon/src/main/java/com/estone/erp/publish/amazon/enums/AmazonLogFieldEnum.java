package com.estone.erp.publish.amazon.enums;

/**
 * Amazon操作日志字段名称
 * <AUTHOR>
 * @date 2021/12/17 11:45
 */
public enum AmazonLogFieldEnum {
    PRICE_SECTION("priceSection", "价格区间"),
    CALC_LOGISTICS("calcLogistics", "试算物流方式"),
    PREFER_LOGISTICS("preferLogistics", "优选物流方式"),
    ALTERNATE_LOGISTICS("alternateLogistics", "备选物流方式"),
    LOGISTICS_CODE("logisticsCode", "物流方式"),
    APPLY_TO_ACCOUNT_CONFIG("applyToAccountConfig", "应用到店铺配置"),
    SHIPPING_COST("shippingCost", "运费"),
    LABEL("label", "标签"),
    PRODUCT_WEIGHT("productWeight", "产品重量限制"),
    SALE_PRICE("salePrice", "销售成本价"),
    SALES_SECTION("salesSection", "指定销量区间"),
    PRODUCT_INVENTORY("productInventory", "库存限制"),
    PRODUCT_INPUT_TIME("productInputTime", "产品录入时间"),
    MONTH_INPUT_TIME("monthInputTime", "按月产品录入时间"),
    ;

    private String fieldEn;
    private String fieldCn;

    private AmazonLogFieldEnum(String fieldEn, String fieldCn) {
        this.fieldEn = fieldEn;
        this.fieldCn = fieldCn;
    }

    public String getFieldEn() {
        return fieldEn;
    }

    public String getFieldCn() {
        return fieldCn;
    }
}
