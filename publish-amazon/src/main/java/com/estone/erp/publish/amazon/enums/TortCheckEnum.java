package com.estone.erp.publish.amazon.enums;

/**
 * <AUTHOR>
 * @date 2022/11/9 17:43
 * @description 侵权校验状态
 */
public enum TortCheckEnum {

    PENDING("pending"," 待处理"),
    PROCESS("process"," 处理中"),
    SUCCESS("success"," 成功"),
    FAILED("failed"," 失败"),
    ;

    private String code;

    private String name;

    TortCheckEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
