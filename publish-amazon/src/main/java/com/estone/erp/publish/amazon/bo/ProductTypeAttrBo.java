package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/22 18:02
 * @description
 */
@Getter
@Setter
public class ProductTypeAttrBo {

    private String route;

    private  List<Object> values;

    private Map<String, Object> attrs;

    private boolean required;

    //适用美国US站点和加拿大CA站点
//    private String defaultType = "{\"descriptionData\":[{\"route\":\"DescriptionData--ShippingWeight\",\"values\":[0],\"attrs\":{\"unitOfMeasure\":[\"GR\"]}},{\"route\":\"DescriptionData--UsedFor\",\"values\":[null,null,null,null,null],\"attrs\":null},{\"route\":\"DescriptionData--TargetAudience\",\"values\":[null,null,null,null],\"attrs\":null},{\"route\":\"DescriptionData--IsGiftWrapAvailable\",\"values\":[true],\"attrs\":null}]}";
    //其他站点加上这个
//    private String defaultTypeSpecial = "{\"route\":\"DescriptionData--RecommendedBrowseNode\",\"values\":[null,null],\"attrs\":null}";

    private static final List<ProductTypeAttrBo> defaultList ;
    private static List<String> defaultNameList;

    public static void main(String[] args) {
        System.out.println(Arrays.asList(null, null));
    }
    static {
        defaultList = new ArrayList<>(4);

        // {"route":"DescriptionData--ShippingWeight","values":[0],"attrs":{"unitOfMeasure":["GR"]}}
        ProductTypeAttrBo bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--ShippingWeight");
        bo.setValues(JSON.parseArray("[0]", Object.class));
        bo.setAttrs(JSON.parseObject("{\"unitOfMeasure\":[\"GR\"]}", HashMap.class));
        bo.setRequired(true);
        defaultList.add(bo);

        // {"route":"DescriptionData--UsedFor","values":[null,null,null,null,null],"attrs":null}
        /*bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--UsedFor");
        bo.setValues(JSON.parseArray("[null,null,null,null,null]", Object.class));
        bo.setAttrs(null);
        defaultList.add(bo);*/

        //{"route":"DescriptionData--TargetAudience","values":[null,null,null,null],"attrs":null}
       /* bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--TargetAudience");
        bo.setValues(JSON.parseArray("[null,null,null,null]", Object.class));
        bo.setAttrs(null);
         defaultList.add(bo);*/


        //{"route":"DescriptionData--IsGiftWrapAvailable","values":[false],"attrs":null}]}
        bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--IsGiftWrapAvailable");
        bo.setValues(JSON.parseArray("[false]", Object.class));
        bo.setAttrs(null);
        bo.setRequired(true);
        defaultList.add(bo);

        //{"route":"DescriptionData--IsExpirationDatedProduct","values":[false],"attrs":null}]}
        bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--IsExpirationDatedProduct");
        bo.setValues(JSON.parseArray("[false]", Object.class));
        bo.setAttrs(null);
        bo.setRequired(true);
        defaultList.add(bo);

        //其他站点加上这个
        //{"route":"DescriptionData--RecommendedBrowseNode","values":[null,null],"attrs":null}
        bo = new ProductTypeAttrBo();
        bo.setRoute("DescriptionData--RecommendedBrowseNode");
        bo.setValues(JSON.parseArray("[null,null]", Object.class));
        bo.setAttrs(null);
        bo.setRequired(true);
        defaultList.add(bo);

        defaultNameList = defaultList.stream().map(o -> o.getRoute()).collect(Collectors.toList());
    }

    public static List<ProductTypeAttrBo> getDefaultList(String site){
        List<ProductTypeAttrBo> result = JSON.parseArray(JSON.toJSONString(defaultList), ProductTypeAttrBo.class);

        //这2个站点去掉这个属性
        if("US".equalsIgnoreCase(site) || "CA".equalsIgnoreCase(site)){
            Iterator<ProductTypeAttrBo> iterator = result.iterator();
            while (iterator.hasNext()){
                if("DescriptionData--RecommendedBrowseNode".equalsIgnoreCase( iterator.next().getRoute())){
                    iterator.remove();
                    break;
                }
            }
        }
        result = addDefaultListBySite(site,result);
        return result;
    }

    /**
     * 根据站点增加属性
     * @param site
     * @param result
     * @return
     */
    public static List<ProductTypeAttrBo> addDefaultListBySite(String site,List<ProductTypeAttrBo> result) {
        if (StringUtils.isBlank(site)){
            return result;
        }
        if (CollectionUtils.isEmpty(result)){
            result = new ArrayList<>();
        }
        //根据站点添加属性名和属性值，属性值优先级最低
        if ("US".equalsIgnoreCase(site) || "UK".equalsIgnoreCase(site) || "DE".equalsIgnoreCase(site)) {

            ProductTypeAttrBo bo1 = new ProductTypeAttrBo();
            bo1.setRoute("DescriptionData--CountryOfOrigin");
            bo1.setValues(JSON.parseArray("[\"CN\"]", Object.class));
            bo1.setAttrs(null);
            bo1.setRequired(true);
            result.add(bo1);

            ProductTypeAttrBo bo2 = new ProductTypeAttrBo();
            bo2.setRoute("DescriptionData--UnitCount");
            bo2.setValues(JSON.parseArray("[\"1\"]", Object.class));
            bo2.setAttrs(null);
            bo2.setRequired(true);
            result.add(bo2);

            ProductTypeAttrBo bo3 = new ProductTypeAttrBo();
            bo3.setRoute("DescriptionData--PPUCountType");
            if ("DE".equalsIgnoreCase(site)) {
                bo3.setValues(JSON.parseArray("[\"Stück\"]", Object.class));
            } else {
                bo3.setValues(JSON.parseArray("[\"count\"]", Object.class));
            }
            bo3.setAttrs(null);
            bo3.setRequired(true);
            result.add(bo3);
        }
        return result;
    }


    public static boolean skipNode(String node){
        return defaultNameList.contains(node);
    }
 
    //递归构建
    public static void generateAttr(ElementWrapper node, List<ProductTypeAttrBo> list, Set<String> productTypeSet, boolean isAdultCategory){
        if(node == null || productTypeSet.contains(node.getRoute())){
            return;
        }
        if(BooleanUtils.isFalse(node.getRequired()) && !(isAdultCategory && node.getRoute().contains("IsAdultProduct"))) {
            // 非必填 不为成人分类默认属性
            return;
        }


        ProductTypeAttrBo bo = new ProductTypeAttrBo();
        list.add(bo);

        String route = node.getRoute();
        bo.setRoute(route);
        //value如何构建
        List<Object> values = new ArrayList<>();
        Integer occurs = node.getOccurs() == null ? 1 : node.getOccurs();
        for (Integer i = 0; i < occurs; i++) {
            if (isAdultCategory && route.contains("IsAdultProduct")){
                values.add(Boolean.TRUE);
            }else {
                values.add(null);
            }
        }
        bo.setValues(values);
        bo.setAttrs(null);
        bo.setRequired(true);
        LinkedHashMap<String, ElementWrapper> items = node.getItems();
        if(items != null && items.size() > 0){
            for (Map.Entry<String, ElementWrapper> entry : items.entrySet()) {
                if(ProductTypeAttrBo.skipNode(entry.getValue().getRoute())){
                    continue;
                }
                generateAttr(entry.getValue(), list,productTypeSet, isAdultCategory);
            }
        }
    }

}
