package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-22 14:46
 */
@Data
public class CatOperationPublishConfigDO {

    private List<Integer> ids;

    private Integer id;

    /**
     * 主管id
     */
    private String saleId;

    /**
     * 主管名称
     */
    private String saleName;

    /**
     * 分类
     */
    private String categoryName;

    /**
     * 刊登次数
     */
    private Integer publishNumber;

    /**
     * 刊登总次数
     */
    private Integer publishTotalNumber;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * 类目全路径
     */
    private String categoryCodePath;


    /**
     * 状态 true 启用, false 禁用
     */
    private Boolean status;

    /**
     * 启用配置的主管
     */
    private List<String> enabledSaleIds;



}
