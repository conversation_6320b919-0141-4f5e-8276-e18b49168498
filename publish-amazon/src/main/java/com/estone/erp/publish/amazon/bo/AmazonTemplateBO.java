package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.amazon.componet.publish.domain.OSSImageData;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonMarketplaceEnums;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class AmazonTemplateBO extends AmazonTemplateWithBLOBs {

    private List<AmazonSku> amazonSkus;

    private AmazonExtralData amazonExtralData;

    private List<String> extraImagesList;
    
    private List<String> tortList;
    
    private Double ProfitMargin;
    
    private Double shippingWeight;

    /**
     * 单品状态
     */
    private String skulifecyclephase;

    /**
     * 产品全类目路径
     */
    private String fullPathCode;

    /**
     * 冠通用到，站点对应的库存
     */
    private Map<String, Integer> site2Stock;

    /**
     * sku的产品系统分类code
     */
    private String skuProductCategoryCode;

    /**
     * upc豁免标志
     */
    private Boolean upcExempt = false;

    /**
     * 语言标签
     */
    private String languageTag;

    /**
     * 图片映射
     */
    private Map<String, OSSImageData> imageMapping;

    /**
     * 产品分类搜索词
     */
    private String classificationsSearchTerms;

    /**
     * JSON feed 刊登
     */
    private Boolean jsonFeedPublish;

    public List<AmazonSku> getAmazonSkus() {
        if (amazonSkus != null) {
            return amazonSkus;
        }

        if (StringUtils.isNotBlank(getVariations())) {
            try {
                amazonSkus = JSON.parseArray(getVariations(), AmazonSku.class);
            }catch (Exception e){
                amazonSkus = new ArrayList<>(0);
                log.error(String.format("%s 转换失败 ", getVariations()), e);
            }
        }
        else {
            amazonSkus = new ArrayList<AmazonSku>(0);
        }

        return amazonSkus;
    }

    public List<String> getExtraImagesList() {
        if (extraImagesList != null) {
            return extraImagesList;
        }

        if (StringUtils.isNotBlank(getExtraImages())) {
            try {
                extraImagesList = JSON.parseArray(getExtraImages(), String.class);
            }catch (Exception e){
                extraImagesList = new ArrayList<>(0);
                log.error(String.format("%s 转换失败 ", getExtraImages()), e);
            }
        }
        else {
            extraImagesList = new ArrayList<String>(0);
        }

        return extraImagesList;
    }

    public AmazonExtralData getAmazonExtralData() {
        if (amazonExtralData != null) {
            return amazonExtralData;
        }

        if(StringUtils.isBlank(getExtraData())){
            amazonExtralData = new AmazonExtralData();
        }else{
            try {
                amazonExtralData = JSON.parseObject(getExtraData(), AmazonExtralData.class);
            }catch (Exception e){
                amazonExtralData = new AmazonExtralData();
                log.error(String.format("%s 转换失败 ", getExtraData()), e);
            }
        }
        return amazonExtralData;
    }

    public String getSellerSKU() {
        return this.getSellerSku();
    }

    @Deprecated
    public void setSellerSKU(String sellerSKU) {
        this.setSellerSku(sellerSKU);
    }

    /**
     * 更新sku与变体信息
     * @param updateAmazonSkus 更新的sku
     */
    public void updateVariations(List<AmazonSku> updateAmazonSkus) {
        this.setAmazonSkus(updateAmazonSkus);
        this.setVariations(JSON.toJSONString(updateAmazonSkus));
    }

    /**
     * 更新sku与变体信息
     *
     * @param variations variations
     */
    public void updateAmazonSkus(String variations) {
        if (StringUtils.isNotBlank(variations)) {
            try {
                List<AmazonSku> amazonSkusData = JSON.parseArray(getVariations(), AmazonSku.class);
                this.setAmazonSkus(amazonSkusData);
            } catch (Exception e) {
                amazonSkus = new ArrayList<>(0);
                log.error(String.format("%s 转换失败 ", getVariations()), e);
            }
        }
    }

    /**
     * 获取图片映射
     *
     * @return
     */
    public Map<String, OSSImageData> getImageMapping() {
        if (org.apache.commons.lang.StringUtils.isBlank(this.getOssImageData())) {
            return new HashMap<>();
        }
        return JSON.parseObject(this.getOssImageData(), new TypeReference<Map<String, OSSImageData>>() {
        });
    }

    public void setImageMapping(Map<String, OSSImageData> imageMapping) {
        this.imageMapping = imageMapping;
        this.setOssImageData(JSON.toJSONString(imageMapping));
    }

    public String getLanguageTag() {
        if (StringUtils.isNotBlank(languageTag)) {
            return languageTag;
        }
        if (StringUtils.isNotBlank(getConditionNote())) {
            return getConditionNote();
        }
        AmazonMarketplaceEnums marketplaceEnums = AmazonMarketplaceEnums.fromSiteName(getCountry());
        return marketplaceEnums.getLocale();
    }
}
