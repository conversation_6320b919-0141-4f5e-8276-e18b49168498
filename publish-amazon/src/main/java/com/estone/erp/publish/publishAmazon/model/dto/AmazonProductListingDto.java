package com.estone.erp.publish.publishAmazon.model.dto;

import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import lombok.Data;

import java.util.List;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2021/1/26 17:49
 * @Version: 1.0.0
 */
@Data
public class AmazonProductListingDto {

    private List<AmazonProductListing> amazonProductListingList;

    private List<String> esAmazonProductListingIdList;

    private List<String> feedTypeList;

    /**
     * 物流方式Code
     */
    private String shippingMethodCode;

    /**
     * 是否指定物流方式 true 使用指定物流直接算价 false使用店铺配置（先算价规则 无匹配则使用店铺配置物流方式）
     */
    private Boolean isAppointShippingMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备货期
     */
    private Integer fulfillmentLatency;
}
