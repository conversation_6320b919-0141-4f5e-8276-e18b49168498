package com.estone.erp.publish.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import com.estone.erp.publish.mybatis.DataSources;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;

import javax.sql.DataSource;

/***
 * publish_tidb数据库配置类
 */
@Configuration
@Order(2)
@Slf4j
@MapperScan(basePackages = {"com.estone.erp.publish.tidb.publishtidb.mapper"},
        sqlSessionFactoryRef = DataSources.PUBLISH_TIDB_MP_FAC)
@ConfigurationProperties(prefix = "mybatis.tidb.publishtidb")
public class PublishTidbDataSourceConfig extends AbstractDataSourceConfig {

    @Bean(DataSources.PUBLISH_TIDB_DS)
    @ConfigurationProperties(prefix = "spring.datasource.tidb.publishtidb")
    public DataSource dataSource() {
        log.info("===========加载数据源:spring.datasource.publish-tidb========");
        return getDataSource();
    }

    /**
     * 将SqlSessionFactory配置为MybatisPlus的MybatisSqlSessionFactoryBean
     *
     * @return MybatisSqlSessionFactoryBean
     */
    @Bean(DataSources.PUBLISH_TIDB_MP_FAC)
    public MybatisSqlSessionFactoryBean setSqlSessionFactory() {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        // 设置数据源
        bean.setDataSource(dataSource());
        // xml扫描路径
        Resource[] mapperResources = getMapperResources();
        bean.setGlobalConfig(this.globalConfig());
        bean.setMapperLocations(mapperResources);
        // 插件
        Interceptor[] plugins = {paginationInterceptor()};
        bean.setPlugins(plugins);
        return bean;
    }

    /**
     * 设置全局配置
     *
     * @return 全局配置
     */
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        globalConfig.setDbConfig(dbConfig);
        return globalConfig;
    }


    /**
     * mybatis-plus分页插件
     */
    @Bean(name = "paginationInterceptor")
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setDialectType(DbType.MYSQL.getDb());
        return paginationInterceptor;
    }

}
