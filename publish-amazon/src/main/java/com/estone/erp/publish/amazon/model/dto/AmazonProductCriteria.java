package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class AmazonProductCriteria {
    private String accountNumber;
    
    private String productAsin;
    
    private String asin;
    
    private String skus;
    
    private Boolean isOnline;
    
    private String itemStatus;
    
    private Integer  forbidStatus;
    
    private String title;
    
    private Boolean containsInfringementWord;
    
    private String orderBy;
    
    private Integer limit;
    
    private Integer offset;

    private String sellerSkus;

    //true：查询库存为0数据，false：查询库存不为0数据
    private Boolean queryStockZero;

    //true: 查询试卖listing, false:不查询
    private Boolean querySpFlag;

    /**
     * variantIdList
     */
    private List<Integer> variantIdList;
}
