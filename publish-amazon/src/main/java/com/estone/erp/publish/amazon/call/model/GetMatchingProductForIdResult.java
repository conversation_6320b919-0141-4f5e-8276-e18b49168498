package com.estone.erp.publish.amazon.call.model;

import java.util.List;

/**
 * @Description: 自定义亚马逊产品结果类
 * @Author: listen
 * @Date: 2018/12/29 11:19
 * @Version: 0.0.1
 */
public class GetMatchingProductForIdResult {

    private String id;

    private String idType;

    private String status;

    private String error;

    private List<Product> products;

    public GetMatchingProductForIdResult() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public List<Product> getProducts() {
        return products;
    }

    public void setProducts(List<Product> products) {
        this.products = products;
    }
}
