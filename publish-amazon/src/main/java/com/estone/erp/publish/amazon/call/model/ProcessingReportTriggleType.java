package com.estone.erp.publish.amazon.call.model;

/**
 * 处理报告触发类型枚举
 */
public enum ProcessingReportTriggleType {
    /**
     * 模板
     */
    Template,

    /**
     * Listing
     */
    Listing,

    /**
     * 新在线列表相关操作
     */
    NewListing,

    /**
     * 跟卖
     */
    Follow_Sell,

    /**
     * 产品列表批量跟卖
     */
    PRODUCT_FOLLOW_SELL,

    /**
     * 产品分类
     */
    Category,

    /**
     * 属于清仓SKU模块
     */
    CLEARANCE_SKU,

    /**
     * Listing更新标题
     */
    Listing_Update_Title,

    /**
     * Listing更新描述
     */
    Listing_Update_Desc,

    /**
     * Listing更新图片（主图、样品图、附图）
     */
    Listing_Update_Image,

    /**
     * 超过6个月没销量自动更新EAN
     */
    Listing_Auto_Update_EAN,

    /**
     * Listing更新备货期
     */
    Listing_Fulfillment_Latency,

    /**
     * listing加库存
     */
    LISTING_ADD_STOCK,

    /**
     * 移除禁售信息列表数据
     */
    Remove_Prohibition_NewListing,

    /**
     * Listing美容健康-成人用品类目系统自动删除
     */
    Listing_Category_Auto_Delete,

    /**
     * Listing无总销量系统自动删除
     */
    Listing_Order_Num_Auto_Delete,

    /**
     * Listing无总销量系统自动删除
     */
    Listing_Infringement_Type_ForbidChannel_Auto_Delete,

    /**
     * Listing停产、存档产品，无历史销量的，直接删除
     */
    Listing_Stop_Archived_Auto_Delete,

    /**
     * Listing停产30天无历史销量的，直接删除
     */
    Listing_Stop_30_Auto_Delete,

    /**
     * Listing存档7天无历史销量的，直接删除
     */
    Listing_Archived_7_Auto_Delete,

    /**
     * 子ASIN为空的父asin，系统自动下架
     */
    Listing_Not_SonAsin_ParentAsin_Auto_Delete,
    /**
     * TRO店铺库存改0
     */
    GBC_STOCK_ZERO,

    /**
     * Listing下架
     */
    Listing_Delete,

    /**
     * SKU 停产存档下架
     */
    LISTING_SKU_STOP_ARCHIVED_DELETE,

    /**
     * 运费调价 重试
     */
    RE_CALL_CALC_SHIPPING_COST,

    /**
     * 文件调价
     */
    FILE_UPDATE_PRICE,


    /**
     * Listing更新标题描述定时
     */
    Listing_UPDATE_TITLE_DESC_TASK,


    /**
     * 恢复Listing绑定关系
     */
    Restore_Listing_Relationship,

    /**
     * 绑定Listing绑定关系-父体上传
     */
    BIND_LISTING_PRENT_UPLOAD,


    /**
     * 绑定Listing绑定关系
     */
    BIND_LISTING_RELATIONSHIP,

    /**
     * 亏损订单调0
     */
    LISTING_CLOSE_STOCK,

    /**
     * 修改GPSR
     */
    LISTING_UPDATE_GPSR,

    /**
     * 模板刊登
     */
    PUBLISH_TEMPLATE,

    /**
     * 修改listing信息
     */
    UPDATE_LISTING,

    /**
     * 指定物流算价
     */
    logistics_code_Price_Listing,

    /**
     * 价格库存同步
     */
    PRICE_STOCK_SYNC,

    ;
}
