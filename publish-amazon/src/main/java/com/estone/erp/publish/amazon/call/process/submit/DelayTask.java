package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.common.model.ERPInvoker;
import org.apache.http.util.Asserts;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * 
 * @Description: 延时任务类
 * 
 * @ClassName: DelayTask
 * @Author: Kevin
 * @Date: 2019/03/28
 * @Version: 0.0.1
 */
public class DelayTask implements Delayed {

    /**
     * 启动时间
     */
    private long startTime;

    /**
     * 回调函数
     */
    private ERPInvoker callBack;

    /**
     * 
     * @Constructor: 构造函数
     *
     * @param delay 延时时间，毫秒值
     * @param callBack 回调函数
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public DelayTask(long delay, ERPInvoker callBack) {
        Asserts.notNull(callBack, "callBack funtion must not be null.");
        setDelay(delay);
        this.callBack = callBack;
    }

    @Override
    public int compareTo(Delayed delayed) {
        final long diff = this.startTime - ((DelayTask) delayed).startTime;
        return Long.signum(diff);
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(startTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public ERPInvoker getCallBack() {
        return callBack;
    }

    public void setCallBack(ERPInvoker callBack) {
        this.callBack = callBack;
    }

    public void setDelay(final long delay) {
        Asserts.check(delay > 0, "delay time must be greater than 0.");
        this.startTime = delay + System.currentTimeMillis();
    }
}
