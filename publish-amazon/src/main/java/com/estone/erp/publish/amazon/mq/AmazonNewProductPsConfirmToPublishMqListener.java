package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025-03-10 17:32
 */
public class AmazonNewProductPsConfirmToPublishMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String spu = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(spu)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String spuCode = spu.replaceAll("\"", "");
        ApiResult<String> apiResult = amazonMustPublishNewProductService.psConfirmToPublish(spuCode);
        if (apiResult.isSuccess()) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
