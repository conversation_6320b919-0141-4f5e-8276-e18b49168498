package com.estone.erp.publish.amazon.call.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

/**
 * AES加密解密工具类
 * 
 * <AUTHOR>
 *
 */
public class AESCoder {
    private static final Logger log = LoggerFactory.getLogger(AmazonUtils.class);

    /**
     * AES加密
     * 
     * @param data 需要被加密的数据
     * @param pwd 密码
     * @return 密文
     */
    public static byte[] encrypt(byte[] data, byte[] pwd) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(pwd);
            kgen.init(128, secureRandom);// 利用用户密码作为随机数初始化出128位的key生产者
            SecretKey secretKey = kgen.generateKey();// 根据用户密码，生成一个密钥
            byte[] enCodeFormat = secretKey.getEncoded();// 返回基本编码格式的密钥，如果此密钥不支持编码，则返回null。
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");// 转换为AES专用密钥
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化为加密模式的密码器
            byte[] result = cipher.doFinal(data);// 加密

            return result;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * AES解密
     * 
     * @param encryptedData AES加密过过的内容
     * @param pwd 密码
     * @return 明文
     */
    public static byte[] decrypt(byte[] encryptedData, byte[] pwd) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(pwd);
            kgen.init(128, secureRandom);// 利用用户密码作为随机数初始化出128位的key生产者
            SecretKey secretKey = kgen.generateKey();// 根据用户密码，生成一个密钥
            byte[] enCodeFormat = secretKey.getEncoded();// 返回基本编码格式的密钥
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");// 转换为AES专用密钥
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, key);// 初始化为解密模式的密码器
            byte[] result = cipher.doFinal(encryptedData);

            return result; // 明文
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }
}
