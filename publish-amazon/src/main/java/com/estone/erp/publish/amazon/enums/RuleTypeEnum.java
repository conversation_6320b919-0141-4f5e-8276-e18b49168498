package com.estone.erp.publish.amazon.enums;

/**
 * 店铺生成规则类型枚举
 * 规则类型：FBM、FBA
 */
public enum RuleTypeEnum {
    FBM("FBM","FBM生成规则"),
    FBA("FBA","FBA生成规则");


    private String code;
    private String name;

    private RuleTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
