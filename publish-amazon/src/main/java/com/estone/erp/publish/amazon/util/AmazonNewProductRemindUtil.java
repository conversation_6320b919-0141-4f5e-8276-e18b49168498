package com.estone.erp.publish.amazon.util;

import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonNewProductRemindTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonNewRemind;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.model.UnpublishCategory;
import com.estone.erp.publish.platform.model.UnpublishCategoryExample;
import com.estone.erp.publish.platform.service.UnpublishCategoryService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.NewProductRemind;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Amazon新品推荐工具类
 * <AUTHOR>
 * @date 2022/1/12 10:45
 */
@Slf4j
public class AmazonNewProductRemindUtil {

    private static final AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);

    /**
     * 过滤不推荐分类
     * @param newProductRemindList 新品list
     * @return newProductRemindList
     */
    public static List<NewProductRemind> filterUnPublishCategory(List<NewProductRemind> newProductRemindList) {
        UnpublishCategoryService unpublishCategoryService = SpringUtils.getBean(UnpublishCategoryService.class);
        UnpublishCategoryExample categoryExample = new UnpublishCategoryExample();
        categoryExample.createCriteria()
                .andPlatformEqualTo(SaleChannel.CHANNEL_AMAZON)
                .andApplyStateEqualTo(ApplyStatusEnum.YES.getCode());
        List<UnpublishCategory> unpublishCategories = unpublishCategoryService.selectByExample(categoryExample);

        // 不推荐的分类
        List<String> unPublishCategoryList = unpublishCategories.stream()
                .map(UnpublishCategory::getFullPathCode)
                .collect(Collectors.toList());

        newProductRemindList = newProductRemindList.stream()
                .filter(o -> !unPublishCategoryList.contains(o.getFullpathcode()))
                .collect(Collectors.toList());

        return newProductRemindList;
    }

    /**
     * 不分配的账号
     * @param accounts 账号
     * @param salesmanAccountDetailList 销售信息
     * @return
     */
    public static ApiResult<List<String>> getNeedFilterAccount(List<String> accounts,  Map<String, SalesmanAccountDetail> salesmanAccountDetailList) {
        List<String> needFilterAccountList = new ArrayList<>();

        // 没有销售信息的账号
        for (String account : salesmanAccountDetailList.keySet()) {
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailList.get(account);
            if (CollectionUtils.isEmpty(salesmanAccountDetail.getSalesmanSet())){
                needFilterAccountList.add(account);
            }

        }


        // 销售主管的账号
        List<String> salesSupervisorAccountList = AmazonAccountUtil.getSalesSupervisorAccountList(salesmanAccountDetailList);
        needFilterAccountList.addAll(salesSupervisorAccountList);

        // 勾选海外仓的账号
        List<String> overseasBusinessAccountList = new ArrayList<>();
        ApiResult<List<SaleAccount>> listApiResult = EsAccountUtils.getSaleAccountsByAccounts(accounts,SaleChannel.CHANNEL_AMAZON);
        if (!listApiResult.isSuccess()) {
            XxlJobLogger.log("根据账号查询ES获取账号信息时报错：" + listApiResult.getErrorMsg());
            return ApiResult.newError(listApiResult.getErrorMsg());
        }
        List<SaleAccount> saleAccountList = listApiResult.getResult();
        for (SaleAccount saleAccount : saleAccountList) {
            Boolean overseasBusiness = saleAccount.getOverseaWarehouse();
            if (overseasBusiness != null && overseasBusiness) {
                String accountNum = saleAccount.getAccountNumber();
                overseasBusinessAccountList.add(accountNum);
            }
        }
        needFilterAccountList.addAll(overseasBusinessAccountList);
        return ApiResult.newSuccess(needFilterAccountList);
    }

    /**
     * 获取适合分配的账号配置信息
     * @param newProductRemind 新品
     * @param needFilterAccountList 需要过滤的账号
     * @param forbiddenMap 禁售站点
     * @param isReassign 是否是重分配
     * @param type 数据源类型
     * @return
     */
    public static List<AmazonAccountRelation> getAssignableAccount(NewProductRemind newProductRemind, List<String> needFilterAccountList,
                                                                   Map<String, List<SalesProhibitionsVo>> forbiddenMap, Boolean isReassign, Integer type) {
        // 获取新品spu对应的禁售站点
        List<String> prohibitionSites = getProhibitionSites(forbiddenMap, newProductRemind.getSpu());

        // 如果是7天前编辑完成的产品，仅可分配指定站点
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "NEW_REMIND_SITE", null);
        List<String> siteList = CommonUtils.splitList(systemParamValue, ",");
        if (AmazonNewProductRemindTypeEnum.ASSIGN_SEVEN_DAY_BEFORE.getCode().equals(type)) {
            siteList.removeAll(prohibitionSites);
            if (CollectionUtils.isEmpty(siteList)) {
                return null;
            }
        }

        // 获取产品完整类目code
        String fullPathCode = newProductRemind.getFullpathcode();
        String code = "," + StringUtils.substringAfterLast(fullPathCode,"_") + ",";
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        // 查询符合条件的店铺配置
        AmazonAccountRelationService amazonAccountRelationService = SpringUtils.getBean(AmazonAccountRelationService.class);
        AmazonAccountRelationExample accountRelationExample = new AmazonAccountRelationExample();
        AmazonAccountRelationExample.Criteria criteria = accountRelationExample.createCriteria();
        criteria.andProdCategoryCodesLike(code)
                .andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode())
                .andIsDistributeNewEqualTo(true);
        if (CollectionUtils.isNotEmpty(needFilterAccountList)) {
            criteria.andAccountNumberNotIn(needFilterAccountList);
        }
        if (AmazonNewProductRemindTypeEnum.ASSIGN_YESTERDAY.getCode().equals(type)) {
            if (CollectionUtils.isNotEmpty(prohibitionSites)) {
                criteria.andAccountCountryNotIn(prohibitionSites);
            }
        } else if (AmazonNewProductRemindTypeEnum.ASSIGN_SEVEN_DAY_BEFORE.getCode().equals(type)) {
            criteria.andAccountCountryIn(siteList);
        }
        String twoColumns = "id, account_number, relation_account, account_country";
        accountRelationExample.setFiledColumns(twoColumns);
        List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(accountRelationExample);
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }

        // 查询redis 过滤因报错不分配新品的店铺
        filterErrorAccount(relations);
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }

        // 如果是重分配，优先按照7天/14天/30天刊登成功的店铺进行分配
        if (isReassign) {
            Boolean flag;

            // 优先查询7天内当前分类刊登成功的店铺
            Date day7Before = DateUtils.addDays(new Date(), -7);
            flag = findPublishSuccessAccount(relations, fullPathCode, day7Before, null);
            if (flag) {
                return relations;
            }

            // 查询7天-14天当前分类刊登成功的店铺
            Date day14Before = DateUtils.addDays(new Date(), -14);
            flag = findPublishSuccessAccount(relations, fullPathCode, day14Before, day7Before);
            if (flag) {
                return relations;
            }

            // 查询14天-30天当前分类刊登成功的店铺
            Date day30Before = DateUtils.addDays(new Date(), -30);
            flag = findPublishSuccessAccount(relations, fullPathCode, day30Before, day14Before);
            if (flag) {
                return relations;
            }

            //log.error("30天内该SPU对应分类无刊登成功店铺，不进行分配");
            return null;
        }

        return relations;
    }

    private static Boolean findPublishSuccessAccount(List<AmazonAccountRelation> relations, String fullPathCode, Date before, Date end) {
        List<String> accountNumbers = relations.stream().map(AmazonAccountRelation::getAccountNumber).collect(Collectors.toList());

        List<String> accountNumberList = new ArrayList<>();
        List<AmazonTemplateBO> amazonTemplateList = new ArrayList<>();
        String fieldColumns = "id, parent_SKU, seller_id";
        for (String accountNumber : accountNumbers) {
            AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample();
            amazonTemplateExample.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
            amazonTemplateExample.setOrderByClause("last_update_date DESC");
            amazonTemplateExample.setColumns(fieldColumns);
            AmazonTemplateExample.Criteria amazonTemplateExampleCriteria = amazonTemplateExample.createCriteria();
            amazonTemplateExampleCriteria
                    .andSellerIdEqualTo(accountNumber)
                    .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
            if (null != before) {
                amazonTemplateExampleCriteria.andLastUpdateDateGreaterThanOrEqualTo(before);
            }
            if (null != end) {
                amazonTemplateExampleCriteria.andLastUpdateDateLessThan(end);
            }
            List<AmazonTemplateBO> amazonTemplateBOList = amazonTemplateService.selectFiledColumnsByExample(amazonTemplateExample);
            if (CollectionUtils.isEmpty(amazonTemplateBOList)) {
                break;
            }
            amazonTemplateList.addAll(amazonTemplateBOList);
        }
        if (CollectionUtils.isEmpty(amazonTemplateList)) {
            return false;
        }
        for (AmazonTemplateBO amazonTemplateBO : amazonTemplateList) {
            try {
                if (accountNumberList.contains(amazonTemplateBO.getSellerId())) {
                    continue;
                }
                String templateFullPathCode = ProductUtils.getFullpathcodeBySpu(amazonTemplateBO.getParentSku());
                if (fullPathCode.equals(templateFullPathCode)) {
                    accountNumberList.add(amazonTemplateBO.getSellerId());
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(accountNumberList)) {
            return false;
        }
        relations.removeIf(relation -> !accountNumberList.contains(relation.getAccountNumber()));
        return true;
    }

    /**
     * 过滤因报错不分配新品的店铺
     * @param relations
     */
    private static void filterErrorAccount(List<AmazonAccountRelation> relations) {
        Iterator<AmazonAccountRelation> it = relations.iterator();
        while (it.hasNext()) {
            try {
                AmazonAccountRelation relation = it.next();
                String key = RedisConstant.AMAZON_UN_ASSIGN_ACCOUNT + relation.getAccountNumber();
                String value = PublishRedisClusterUtils.get(key);
                if (StringUtils.isNotBlank(value)) {
                    it.remove();
                }
            } catch (Exception e) {
                XxlJobLogger.log("查询redis报错：" + e.getMessage());
                log.error("查询redis报错：" + e.getMessage());
            }
        }
    }

    /**
     * 获取SPU禁售站点
     * @param forbiddenMap
     * @param spu
     * @return
     */
    private static List<String> getProhibitionSites(Map<String, List<SalesProhibitionsVo>> forbiddenMap, String spu) {
        if (MapUtils.isEmpty(forbiddenMap)) {
            return Collections.emptyList();
        }

        List<SalesProhibitionsVo> salesProhibitionsVos = forbiddenMap.get(spu);
        if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
            return Collections.emptyList();
        }

        Set<String> siteSet = new HashSet<>();
        for (SalesProhibitionsVo salesProhibitionsVo : salesProhibitionsVos) {
            String plat = salesProhibitionsVo.getPlat();
            List<Sites> sites = salesProhibitionsVo.getSites();
            if (!SaleChannel.CHANNEL_AMAZON.equals(plat) || CollectionUtils.isEmpty(sites)) {
                continue;
            }
            for (Sites str : sites) {
                String site = str.getSite();
                if (StringUtils.isBlank(site)) {
                    continue;
                }
                siteSet.add(site);
            }
        }
        if (CollectionUtils.isNotEmpty(siteSet)) {
            return new ArrayList<>(siteSet);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 执行分配 组装新品数据返回
     * @param amazonNewRemind 新品
     * @param accountNumberList 可分配账号
     * @param relationMap 账号配置
     * @param accountCountMap 记录账号分配的spu数量
     * @return
     */
    public static List<AmazonNewRemind> executeAssignment(AmazonNewRemind amazonNewRemind, List<String> accountNumberList,
                                                          Map<String, AmazonAccountRelation> relationMap, Map<String ,Integer> accountCountMap) {
        // 未分配的账号
        List<String> notInMapList = accountNumberList.stream()
                .filter(t -> !accountCountMap.containsKey(t)).collect(Collectors.toList());

        // 关联店铺账号
        List<String> relationAccountList;

        // 新品推荐对象集合
        List<AmazonNewRemind> amazonNewRemindList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(notInMapList)) {
            Collections.shuffle(notInMapList);
            String account = notInMapList.get(0);
            accountCountMap.put(account, 1);
            amazonNewRemind.setAccount(account);
            amazonNewRemindList.add(amazonNewRemind);

            // 根据账号获取关联店铺
            relationAccountList = findRelationAccount(relationMap, account);
        } else {
            Map<Integer, List<String>> numberAccountMap = new HashMap<>(16);
            for (String accountNumber : accountNumberList) {
                Integer count = accountCountMap.get(accountNumber);
                if (count != null) {
                    numberAccountMap.computeIfAbsent(count, k -> new ArrayList<>()).add(accountNumber);
                }
            }
            Integer number = null;
            //寻找最小的key值
            for (Map.Entry<Integer, List<String>> integerListEntry : numberAccountMap.entrySet()) {
                Integer key = integerListEntry.getKey();
                if (number == null || key < number) {
                    number = key;
                }
            }
            // 最少分配账号列表
            List<String> minCountAccountList = numberAccountMap.get(number);

            Collections.shuffle(minCountAccountList);
            String account = minCountAccountList.get(0);
            accountCountMap.put(account, number + 1);
            amazonNewRemind.setAccount(account);
            amazonNewRemindList.add(amazonNewRemind);

            // 根据账号获取关联店铺
            relationAccountList = findRelationAccount(relationMap, account);
        }

        // 为spu分配关联店铺
        if (CollectionUtils.isNotEmpty(relationAccountList)) {
            for (String relationAccount : relationAccountList) {
                AmazonNewRemind newRemind = new AmazonNewRemind();
                BeanCopier beanCopier = BeanCopier.create(AmazonNewRemind.class, AmazonNewRemind.class, false);
                beanCopier.copy(amazonNewRemind, newRemind, null);
                newRemind.setAccount(relationAccount);
                amazonNewRemindList.add(newRemind);

                // 账号计数加1
                accountCountMap.merge(relationAccount, 1, Integer::sum);
            }
        }

        return amazonNewRemindList;
    }

    private static List<String> findRelationAccount(Map<String, AmazonAccountRelation> relationMap, String account) {
        List<String> relationAccountList = new ArrayList<>();
        AmazonAccountRelation amazonAccountRelation = relationMap.get(account);
        String relationAccount = amazonAccountRelation.getRelationAccount();
        for (String accountNumber : CommonUtils.splitList(relationAccount, ",")) {
            AmazonAccountRelation relation = relationMap.get(accountNumber);
            if (relation != null) {
                relationAccountList.add(accountNumber);
            }
        }
        return relationAccountList;
    }

    /**
     * 设置销售，销售组长，站点信息
     * @param amazonNewRemindList 新品
     * @param salesmanAccountDetailMap 销售信息
     * @param relationMap 账号配置
     */
    public static void setExtraMessage(List<AmazonNewRemind> amazonNewRemindList,
                                       Map<String, SalesmanAccountDetail> salesmanAccountDetailMap,
                                       Map<String, AmazonAccountRelation> relationMap) {
        for (AmazonNewRemind remind : amazonNewRemindList) {
            // 获取销售，销售组长
            String account = remind.getAccount();
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(account);
            if (null == salesmanAccountDetail) {
                continue;
            }

            Set<String> salesmanSet = salesmanAccountDetail.getSalesmanSet();
            String salesTeamLeader = salesmanAccountDetail.getSalesTeamLeaderName();

            // 如果销售为空，不保存该店铺
            if (CollectionUtils.isEmpty(salesmanSet)) {
                continue;
            }

            if (StringUtils.isNotBlank(salesTeamLeader) && salesTeamLeader.contains("-")) {
                String[] split = salesTeamLeader.split("-");
                salesTeamLeader = split[0];
            }
            remind.setSaleLeaderMan(salesTeamLeader);
            if (salesmanSet.size() > 1) {
                salesmanSet.remove(salesTeamLeader);
            }
            for (String s : salesmanSet) {
                if (StringUtils.isNotBlank(s) && s.contains("-")) {
                    String[] split = s.split("-");
                    s = split[0];
                    remind.setSaleMan(s);
                    break;
                }
            }

            // 获取站点
            AmazonAccountRelation amazonAccountRelation = relationMap.get(account);
            String accountCountry = amazonAccountRelation.getAccountCountry();
            if (StringUtils.isNotBlank(accountCountry)) {
                remind.setAccountCountry(accountCountry);
            }
        }
    }

    /**
     * 校验spu是否可重分配
     *
     * @param amazonNewRemindList
     * @return
     */
    public static boolean checkPublishTemplate(List<AmazonNewRemind> amazonNewRemindList) {
        if (CollectionUtils.isEmpty(amazonNewRemindList)) {
            return false;
        }

        String spu = amazonNewRemindList.get(0).getSpu();
        List<String> accountList = amazonNewRemindList.stream().map(AmazonNewRemind::getAccount).collect(Collectors.toList());

        AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample();
        String filed = "id";
        amazonTemplateExample.setColumns(filed);
        amazonTemplateExample.createCriteria()
                .andParentSkuEqualTo(spu)
                .andSellerIdIn(accountList)
                .andIsLockEqualTo(false)
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
        List<AmazonTemplateBO> templateBOList = amazonTemplateService.selectFiledColumnsByExample(amazonTemplateExample);

        return CollectionUtils.isEmpty(templateBOList);
    }
}
