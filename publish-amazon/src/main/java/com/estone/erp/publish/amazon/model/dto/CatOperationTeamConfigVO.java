package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.EnableBooleanConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 类目运营团队配置
 * <AUTHOR>
 * @date 2024-03-11 15:51
 */
@NoArgsConstructor
@Data
public class CatOperationTeamConfigVO {
    /**
     * id
     */
    private Integer id;

    private String logId;

    /**
     * 主管
     */
    private String saleId;

    /**
     * 主管-姓名
     */
    @ExcelProperty("主管")
    private String saleName;

    /**
     * 分类
     */
    @ExcelProperty("分类")
    private String categoryName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = EnableBooleanConverter.class)
    private Boolean status;

    /**
     * 类目Code
     */
    private String categoryCode;

    /**
     * 类目路径Code
     */
    private String categoryFullPathCode;

    /**
     * 关联店铺
     */
    private String accountNumbers;

    /**
     * 关联店铺总数
     */
    @ExcelProperty("关联店铺总数")
    private Integer accountNumberCount = 0;

    /**
     * 添加时间
     */
    @ExcelProperty(value = "添加时间", converter = TimestampFormatConverter.class)
    private Timestamp createdTime;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间", converter = TimestampFormatConverter.class)
    private Timestamp updatedTime;
}
