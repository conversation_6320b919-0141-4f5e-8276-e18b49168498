package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.sku.model.SellerSkuRule;
import com.estone.erp.publish.amazon.call.sku.model.UnitRuleValue;
import com.estone.erp.publish.amazon.model.AmazonProduct;
import com.estone.erp.publish.amazon.model.AmazonSellerSkuRule;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AmazonSellerSkuRuleBO extends AmazonSellerSkuRule {

    private SellerSkuRule sellerSkuRule;

    private List<String> accountNumberList;

    public List<String> getAccountNumberList() {
        return accountNumberList;
    }

    public void setAccountNumberList(List<String> accountNumberList) {
        this.accountNumberList = accountNumberList;
    }

    public SellerSkuRule getSellerSkuRule() {
        if (sellerSkuRule == null) {
            sellerSkuRule = new SellerSkuRule();
            if (StringUtils.isNotEmpty(getPrefixRuleValues())) {
                sellerSkuRule.setPrefixRuleValues(JSON.parseArray(getPrefixRuleValues(), UnitRuleValue.class));
            }
            if (StringUtils.isNotEmpty(getPrefixSplitRuleValue())) {
                sellerSkuRule.setPrefixSplitRuleValue(JSON.parseObject(getPrefixSplitRuleValue(), UnitRuleValue.class));
            }
            if (StringUtils.isNotEmpty(getSuffixSplitRuleValue())) {
                sellerSkuRule.setSuffixSplitRuleValue(JSON.parseObject(getSuffixSplitRuleValue(), UnitRuleValue.class));
            }
            if (StringUtils.isNotEmpty(getSuffixRuleValues())) {
                sellerSkuRule.setSuffixRuleValues(JSON.parseArray(getSuffixRuleValues(), UnitRuleValue.class));
            }
        }
        return sellerSkuRule;
    }
}
