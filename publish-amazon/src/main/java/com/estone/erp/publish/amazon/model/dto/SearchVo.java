package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @Description: 调用侵权词、商标词 检测传参
 */
@Data
public class SearchVo {

    /**
     * 多个话，就空格隔开 （必传）
     */
    private String text;

    /**
     * 站点（必传）
     */
    private String site;

    /**
     * JSON 数组格式
     * 多个就英文逗号隔开，有就传，没有检测服务会通过sku查询分类id
     */
    private String categoryIds;

    /**
     * 子sku或者主sku
     */
    private String sonSku;

    public SearchVo() {
    }

    public SearchVo(String text, String site) {
        if (StringUtils.isBlank(text)) {
            throw new IllegalArgumentException("txt 不能为空");
        }

        if (StringUtils.isBlank(site)) {
            throw new IllegalArgumentException("txt 不能为空");
        }
        this.text = text;
        this.site = site;
    }

    public SearchVo(String text, String site, List<Integer> categoryIds,String sonSku) {
        if (StringUtils.isBlank(text)) {
            throw new IllegalArgumentException("txt 不能为空");
        }

        if (StringUtils.isBlank(site)) {
            throw new IllegalArgumentException("site 不能为空");
        }
        this.text = text;
        this.site = site;
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            this.categoryIds = JSON.toJSONString(categoryIds);
        }
        if (StringUtils.isNotBlank(sonSku)){
            this.sonSku = sonSku;
        }
    }
}