package com.estone.erp.publish.amazon.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Auther yucm
 * @Date 2021/12/13
 */
@Getter
@Setter
public class AmazonCalcPriceBean {

    private String id;
    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 站点
     */
    private String site;

    /**
     * 货号列表
     */
    private List<String> articleNumbers;

    /**
     * 利润率、促销利润率
     */
    private Double grossProfitRate;

    /**
     * 物流方式code
     */
    private String shippingMethodCode;

    /**
     * 重试次数
     */
    private Integer restNum = 1;

    /**
     * 是否指定物流方式 true 使用指定物流直接算价 false使用店铺配置（先算价规则 无匹配则使用店铺配置物流方式）
     */
    private Boolean isAppointShippingMethod;

    /**
     * 使用运费模板
     */
    private Boolean useShippingGroup;

    /**
     * 使用总价
     */
    private Boolean useTotalPrice;

    /**
     * 价格
     */
    private Double price;
}