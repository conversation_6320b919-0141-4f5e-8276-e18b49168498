package com.estone.erp.publish.amazon.enums;

/**
 * Amazon报告问题分类
 *
 * <AUTHOR>
 * @date 2023/9/8 14:45
 */
public enum AmazonReportSolutionTypeEnum {

    SYSTEM_PROBLEM("system_problem","系统问题"),

    PRODUCT_TYPE_PROBLEM("product_type_problem","分类类型错误"),

    AMOUNT_LIMIT_PROBLEM("amount_limit_problem","刊登数量受限"),

    //用于模板判定
    REPEAT_PUBLISH("repeat_publish","重复刊登"),
    //用于模板判定
    SP_FEED_TYPE_LACK("report feed type lack","处理报告缺少上传类型"),
    // 生成EAN失败
    MISSING_EAN("missing_ean", "UPC/EAN问题"),
    CATEGORY_LIMIT("category_limit", "店铺状态/店铺分类设置问题"),
    // 717
    PRODUCT_STATUS("product_status", "产品状态问题"),
    // 496
    ACCOUNT_CONFIG("account_config", "产品不满足店铺配置限制条件"),
    // 777
    CATEGORY_MISS("category_miss", "产品类型缺失"),

    ;

    private final String code;
    private final String name;

    AmazonReportSolutionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
