package com.estone.erp.publish.amazon.model;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_category
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:05:48 CST 2019
 */
public class AmazonCategory {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private Integer id;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 站点
     */
    private String accountSite;

    /**
     * Database Column Remarks:
     *   卖家帐号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.browse_node_id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private String browseNodeId;

    /**
     * Database Column Remarks:
     *   分类关联
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.browse_node_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private String browseNodeName;

    /**
     * Database Column Remarks:
     *   产品分类
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.browse_node_store_context_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private String browseNodeStoreContextName;

    /**
     * Database Column Remarks:
     *   ParentSKU
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.has_children
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private Boolean hasChildren;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_category.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    private Date lastUpdateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 状态：1 启用 0 禁用
     */
    private Boolean enable;
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.id
     *
     * @return the value of amazon_category.id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.id
     *
     * @param id the value for amazon_category.id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setId(Integer id) {
        this.id = id;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public String getAccountSite() {
        return accountSite;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public void setAccountSite(String accountSite) {
        this.accountSite = accountSite;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.browse_node_id
     *
     * @return the value of amazon_category.browse_node_id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public String getBrowseNodeId() {
        return browseNodeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.browse_node_id
     *
     * @param browseNodeId the value for amazon_category.browse_node_id
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setBrowseNodeId(String browseNodeId) {
        this.browseNodeId = browseNodeId == null ? null : browseNodeId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.browse_node_name
     *
     * @return the value of amazon_category.browse_node_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public String getBrowseNodeName() {
        return browseNodeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.browse_node_name
     *
     * @param browseNodeName the value for amazon_category.browse_node_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setBrowseNodeName(String browseNodeName) {
        this.browseNodeName = browseNodeName == null ? null : browseNodeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.browse_node_store_context_name
     *
     * @return the value of amazon_category.browse_node_store_context_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public String getBrowseNodeStoreContextName() {
        return browseNodeStoreContextName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.browse_node_store_context_name
     *
     * @param browseNodeStoreContextName the value for amazon_category.browse_node_store_context_name
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setBrowseNodeStoreContextName(String browseNodeStoreContextName) {
        this.browseNodeStoreContextName = browseNodeStoreContextName == null ? null : browseNodeStoreContextName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.has_children
     *
     * @return the value of amazon_category.has_children
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public Boolean getHasChildren() {
        return hasChildren;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.has_children
     *
     * @param hasChildren the value for amazon_category.has_children
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setHasChildren(Boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_category.last_update_date
     *
     * @return the value of amazon_category.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_category.last_update_date
     *
     * @param lastUpdateDate the value for amazon_category.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:48 CST 2019
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}