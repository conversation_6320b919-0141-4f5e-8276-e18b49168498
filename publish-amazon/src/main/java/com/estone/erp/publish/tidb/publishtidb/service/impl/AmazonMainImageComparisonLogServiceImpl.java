package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.util.AmazonDBRoutUtil;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonMainImageComparisonLogMapper;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogCriteria;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMainImageComparisonLogService;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;

import com.estone.erp.publish.base.pms.enums.CountryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> amazon_main_image_comparison_log
 * 2024-02-28 16:40:33
 */
@Service("amazonMainImageComparisonLogService")
@Slf4j
public class AmazonMainImageComparisonLogServiceImpl implements AmazonMainImageComparisonLogService {
    @Resource
    private AmazonMainImageComparisonLogMapper amazonMainImageComparisonLogMapper;

    @Override
    public int countByExample(AmazonMainImageComparisonLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonMainImageComparisonLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonMainImageComparisonLog> search(CQuery<AmazonMainImageComparisonLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonMainImageComparisonLogCriteria query = cquery.getSearch();
        AmazonMainImageComparisonLogExample example = query.getExample();
        // 排序
        if (StringUtils.isNotBlank(cquery.getSort())) {
            example.setOrderByClause(cquery.getSort() + " " + cquery.getOrder());
        } else {
            example.setOrderByClause("create_time");
        }
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonMainImageComparisonLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonMainImageComparisonLog> amazonMainImageComparisonLogs = amazonMainImageComparisonLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonMainImageComparisonLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonMainImageComparisonLogs);
        return result;
    }

    @Override
    public AmazonMainImageComparisonLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonMainImageComparisonLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonMainImageComparisonLog> selectByExample(AmazonMainImageComparisonLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonMainImageComparisonLogMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonMainImageComparisonLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonMainImageComparisonLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonMainImageComparisonLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonMainImageComparisonLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonMainImageComparisonLog record, AmazonMainImageComparisonLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonMainImageComparisonLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonMainImageComparisonLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchInsert(List<AmazonMainImageComparisonLog> list, String site) {
        String s = AmazonDBRoutUtil.getTableIndex(site);
        if (StringUtils.isNotBlank(s) && CollectionUtils.isNotEmpty(list)) {
            amazonMainImageComparisonLogMapper.batchInsert(list, s);
        }
    }

    @Override
    public Boolean existFullAccountLog(String accountNumber, String site, String openDate) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(site)) {
            return null;
        }
        String tableIndex = AmazonDBRoutUtil.getTableIndex(site);
        int i = amazonMainImageComparisonLogMapper.existFullAccountLog(accountNumber, tableIndex, openDate);
        return i > 0;
    }

    @Override
    public Set<String> existListingId(String site, List<String> allIds) {
        if (CollectionUtils.isEmpty(allIds)) {
            return new HashSet<>();
        }
        String tableIndex = AmazonDBRoutUtil.getTableIndex(site);
        return amazonMainImageComparisonLogMapper.existListingId(tableIndex, allIds);
    }

}