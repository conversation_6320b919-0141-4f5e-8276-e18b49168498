package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPriceUpdateRecord;

import java.util.List;

/**
 * <p>
 * amazon调价记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface AmazonPriceUpdateRecordService extends IService<AmazonPriceUpdateRecord> {

    /**
     * 根据处理报告更新价格记录状态
     *
     * @param processReport        处理报告
     * @param amazonProductListing listing
     */
    void updatePriceRecordByProcessReport(AmazonProcessReport processReport, AmazonProductListing amazonProductListing);

    List<String> getExistSellerSkuList(String accountNumber, List<String> sellerSkuList);

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AmazonPriceUpdateRecord> wrapper);

}
