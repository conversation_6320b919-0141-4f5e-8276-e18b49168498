package com.estone.erp.publish.amazon.enums;

/**
 * 搜索类型
 * <AUTHOR>
 * @date 2023/6/5 9:39
 */
public enum AmazonSearchTypeEnum {

    ENGLISH_SEARCH(1, "英文搜索"),
    CHINESE_SEARCH(2, "中文搜索"),
    ID_SEARCH(3, "id搜索"),
    ;

    AmazonSearchTypeEnum(Integer code, String name){
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
