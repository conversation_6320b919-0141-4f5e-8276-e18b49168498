package com.estone.erp.publish.amazon.bo;

import com.estone.erp.publish.amazon.model.AmazonFollowSell;
import lombok.Data;

import java.util.Map;

/**
 * @Description 辅助：amazon跟卖模块、amazon产品列表批量跟卖。
 * <AUTHOR>
 * @Date 2019/10/17 11:12
 **/
@Data
public class AmazonFollowSellSuperiorBO extends AmazonFollowSell {

    /**
     * 变体id
     */
    private Integer variantId;
    /**
     * 是否是批量跟卖(刊登产品、价格、库存)
     */
    private boolean rightBatchFollow = false;

    /**
     * 产品列表跟卖
     * 关联key(账号_平台Sku(accountNumber_sellerSku))
     */

    private String relationKey;

    /**
     * 批量跟卖成功的FeedType,包含产品、价格、数量才算成功
     * @see com.estone.erp.publish.amazon.call.submit.FeedType
     */
    private Map<String,Boolean> batchFollowSuccessFeedType;

    /**
     * 刊登是否成功临时标记
     */
    private boolean publishStatus;

    /**
     * 导入时候用到，记录行号
     */
    private Integer rowNum;
    /**
     * 导入时候消息反馈
     */
    private String msg;
}
