package com.estone.erp.publish.amazon.util.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 尺码枚举
 * 
 * <AUTHOR>
 *
 */
public enum Size {
    S("S", "Small", "小号"), M("M", "Medium", "中号"), L("L", "Large", "大号"), XL("XL", "X-Large", "加大号"), _2XL("2XL",
            "2X-Large", "XXL"), _3XL("3XL", "3X-Large",
                    "XXXL"), _4XL("4XL", "4XL-Large", "XXXXL"), _5XL("5XL", "5XL-Large", "XXXXXL");

    public static List<String> codes;

    private String code;

    private String name;

    private String desc;

    private Size(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<String> getCodes() {
        if (codes == null) {
            codes = Size.getSizes().stream().map(size -> {
                return size.getCode();
            }).collect(Collectors.toList());
        }

        return codes;
    }

    public static Size getSize(String code) {
        for (Size size : Size.values()) {
            if (size.getCode().equals(code)) {
                return size;
            }
        }

        return null;
    }

    public static List<Size> getSizes() {
        List<Size> sizes = Arrays.asList(Size.values());
        Collections.sort(sizes, (size1, size2) -> {
            return size2.code.length() - size1.code.length();
        });

        return sizes;
    }
}
