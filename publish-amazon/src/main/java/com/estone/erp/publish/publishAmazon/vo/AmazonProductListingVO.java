package com.estone.erp.publish.publishAmazon.vo;

import com.estone.erp.publish.amazon.model.AmazonVariant;
import com.estone.erp.publish.amazon.model.AmazonVariantOffline;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AmazonProductListingVO {

    private Integer id;

    private String accountNumber;

    private String skuLifeCyclePhase;

    private String asin;

    private String category;

    private String name;

    private Double lowestPrice;

    private String mainImage;

    private Date createDate;

    private Boolean isOnline;

    private String skuProductManagers;

    private String skuSaleManagers;

    private String amazonSite;

    private Date lastAdjustPriceDate;

    private String createdBy;


    private Date lastUpdateDate;


    private String lastUpdatedBy;


    private Boolean isFollowSellDelete;


    private String description;

    private List<AmazonVariantOffline> offlines;

    private List<AmazonVariant> variants;

}
