package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.model.AmazonTemplateAuto;
import com.estone.erp.publish.amazon.model.AmazonTemplateAutoExample;
import com.estone.erp.publish.amazon.service.AmazonTemplateAutoService;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.platform.enums.BulletPointFilterEnum;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonSpuTitleForUpdateDto;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.util.CommonMatchProdInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/20 17:24
 * @description
 */
@Slf4j
public class AmazonMatchProdInfoUtil {
    private static final AmazonTemplateAutoService amazonTemplateAutoService = SpringUtils.getBean(AmazonTemplateAutoService.class);

    private static final Set<Character> PUNCTUATION_MARKS = new HashSet<>(Arrays.asList('“', '”', '‘', '–', '’', ' ', '.', ',', '、', ';', ':', '"', '"', '\'', '(', ')', '[', ']', '?', '!', '—', '–', '~', '…'));


    /**
     * 根据titleRule重新生成标题描述
     * 自动刊登会用到
     */
    public static void matchTemplateInfoWithTitleRule(AmazonTemplateBO template, SpuInfo spuInfo, String randomKeyWord) {
//        // title new rule README: 通用文案Amazon文案规则 刊登成功的sku产品系统不维护了 不用重新取了
//        Map<String, Object> spuTitleRuleMap = new HashMap<>(0);
//        ResponseJson resp = ProductUtils.getSpuTitles(Collections.singletonList(spuInfo.getSpu()));
//        SpuOfficial spuOfficial = null;
//        if (resp.isSuccess()){
//            List<SpuOfficial> spuOfficials = (List<SpuOfficial>)resp.getBody().get(ProductUtils.resultKey);
//            if(CollectionUtils.isNotEmpty(spuOfficials)){
//                spuOfficial = spuOfficials.get(0);
//                ApiResult<Map<String, Object>> apiResult = SpuTitleRuleUtil.generateTitle(Platform.Amazon, spuOfficial, (title) -> ApiResult.newSuccess(title + " " + randomKeyWord));
//                if(apiResult.isSuccess() && apiResult.getResult() != null && apiResult.getResult().size() > 0){
//                    spuTitleRuleMap = apiResult.getResult();
//                }else{
//                    log.warn(String.format("店铺[%s]获取spu[%s]新标题返回失败: %s", template.getSellerId(), spuInfo.getSpu(), JSON.toJSONString(apiResult)));
//                }
//            }
//        }else{
//            log.warn(String.format("店铺[%s]获取spu[%s]新标题失败: %s", template.getSellerId(), spuInfo.getSpu(), resp.getMessage()));
//        }
//        //记录标题的取值规则
//        Object titleRuleVal = spuTitleRuleMap.get(SpuTitleRuleUtil.titleRuleKey);
//        if(titleRuleVal != null){
//            SpuTitleRule rule = (SpuTitleRule)titleRuleVal;
//            rule.setIsFirst(true);
//            template.setTitleRule(JSON.toJSONString(rule));
//        }
//        //标题
//        String title = "";
//        Object titleNew = spuTitleRuleMap.get(SpuTitleRuleUtil.titleKey);
//        if(titleNew != null && StringUtils.isNotBlank(titleNew.toString())){
//            title = titleNew.toString().trim();
//            template.setTitle(title);
//        }
//
//        /* 描述取值优先级：
//            1-SKU描述新，在描述最前面插入标题。
//            2-SKU描述，在描述最前面插入标题
//         */
//        if(spuOfficial != null){
//            String newDescription = spuOfficial.getNewDescription();
//            if(StringUtils.isNotBlank(newDescription)){
//                template.setDescription( title + "\n" + newDescription);
//            }
//        }
//        if(StringUtils.isBlank(template.getDescription())){
//            String desc = StrUtil.objectToStr(spuInfo.getDescEn());
//            try {
//                List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {
//                });
//                for (String s : list) {
//                    if(StringUtils.isNotBlank(s)){
//                        desc = title + "\n"+  s;
//                        template.setDescription(desc);
//                        break;
//                    }
//                }
//            }catch (Exception e){
//                template.setDescription( title + "\n" + desc);
//            }
//        }
//        if (StringUtils.isNotBlank(template.getDescription())) {
//            // 指定单词加换行处理
//            String description = template.getDescription();
//            if (!StringUtils.containsIgnoreCase(description, "Features:\n")) {
//                description = description.replaceAll("(?i)Features:", "Features:\n");
//            }
//            if (!StringUtils.containsIgnoreCase(description, "Specifications:\n")) {
//                description = description.replaceAll("(?i)Specifications:", "Specifications:\n");
//            }
//            if (!StringUtils.containsIgnoreCase(description, "Package Includes:\n")) {
//                description = description.replaceAll("(?i)Package Includes:", "Package Includes:\n");
//            }
//            if (!StringUtils.containsIgnoreCase(description, "Note:\n")) {
//                description = description.replaceAll("(?i)Note:", "Note:\n");
//            }
//            template.setDescription(description);
//        }
//
//        /*  五点描述,取值优先级：
//            1-从Features五点描述中获取，打乱顺序后上传。
//            2、ES-6637 【Amazon】五点描述为空的情况下，取SPU对应admin范本的五点描述
//         */
//        if(spuOfficial != null){
//            List<String> features = JSON.parseObject(spuOfficial.getFeaturesJson(), new TypeReference<List<String>>() {});
//            if(CollectionUtils.isNotEmpty(features)) {
//                features = features.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
//                if (features.size() >= 5) {
//                    // 如果描述为100% brand new and high quality则过滤
//                    for (int i = 0; i < features.size(); i++) {
//                        String bulletPoint = AmazonMatchProdInfoUtil.pointFilter(features.get(i));
//                        if (StringUtils.isBlank(bulletPoint)) {
//                            features.remove(i);
//                            break;
//                        }
//                        features.set(i, bulletPoint);
//                    }
//
//                    Collections.shuffle(features);
//                    if (features.size() >= 5) {
//                        features = features.subList(0, 5);
//                    }
//
//                    template.setBulletPoint(JSON.toJSONString(features));
//                }
//            }
//        }
        //五点描述为空的情况下，取SPU对应admin范本的五点描述
        if (StringUtils.isBlank(template.getBulletPoint())) {
            AmazonTemplateAutoExample example = new AmazonTemplateAutoExample();
            example.setColumns("id,bullet_point");
            example.createCriteria().andParentSkuEqualTo(template.getParentSku())
                    .andCountryEqualTo(template.getCountry())
            ;
            example.setLimit(1);
            List<AmazonTemplateAuto> amazonTemplateAutos = amazonTemplateAutoService.selectColumnsColumnsByExample(example);
            if (CollectionUtils.isNotEmpty(amazonTemplateAutos)) {
                template.setBulletPoint(amazonTemplateAutos.get(0).getBulletPoint());
            }
        }
    }

    /**
     * 匹配模板字段信息
     */
    public static void matchTemplateTitleDescInfo(String mainSku, AmazonTemplateBO templateBO, ProductInfo productInfo) {
        // 匹配文案信息
        SpuOfficial spuOfficial = CommonMatchProdInfoUtil.matchSpuOfficial(mainSku);
        //标题 1-SPU长标题 2 - SKU标题
        List<String> titleList = null;
        if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
            titleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(titleList) && StringUtils.isNotBlank(spuOfficial.getTitle())) {
            titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(titleList) && StringUtils.isNotBlank(productInfo.getTitleEn())) {
            titleList = CommonUtils.arrayAsList(productInfo.getTitleEn());
        }
        if (CollectionUtils.isNotEmpty(titleList)) {
            Collections.shuffle(titleList);
            String title = titleList.get(0);
            templateBO.setTitle(title);
        }
        String title = templateBO.getTitle();
        title = StringUtils.isBlank(title) ? "" : title;
        //描述
        String description = spuOfficial.getNewDescription();
        if (StringUtils.isBlank(description)) {
            String desc = StrUtil.objectToStr(spuOfficial.getDescription());
            try {
                List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    for (String s : list) {
                        if (StringUtils.isNotBlank(s)) {
                            description = s;
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析描述出错:", e);
            }
        }
        if (StringUtils.isBlank(description)) {
            description = productInfo.getDesEn();
        }
        if (StringUtils.isNotBlank(description)) {
            description = formatDescription(description);
            if (StringUtils.isNotBlank(title)) {
                templateBO.setDescription(title + "\n\n" + description);
            } else {
                templateBO.setDescription(description);
            }
        }

        // 五点描述,取值优先级： 1-从Features五点描述中获取，打乱顺序后上传
        if (CollectionUtils.isNotEmpty(spuOfficial.getFeaturesList())) {
            List<String> featuresList = spuOfficial.getFeaturesList();
            List<String> features = featuresList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (features.size() >= 5) {
                // 如果描述为100% brand new and high quality则过滤
                for (int i = 0; i < features.size(); i++) {
                    String bulletPoint = pointFilter(features.get(i));
                    if (StringUtils.isBlank(bulletPoint)) {
                        features.remove(i);
                        break;
                    }
                    features.set(i, bulletPoint);
                }

                Collections.shuffle(features);
                if (features.size() >= 5) {
                    features = features.subList(0, 5);
                }
                templateBO.setBulletPoint(JSON.toJSONString(features));
            }
        }
        // 获取产品分类搜索词
        String packageIncludes = spuOfficial.getPackageIncludes();
        if (StringUtils.isNotBlank(packageIncludes)) {
            String[] split = packageIncludes.split("\n");
            templateBO.setClassificationsSearchTerms(split[0]);
        }
        templateBO.setWenAnType(spuOfficial.getNeedWenAnType());
    }

    /**
     * 批量修改标题组装数据
     *
     * @param dtos
     * @param skuMap
     * @param spuTitleResp
     */
    public static void listingMatchSpuTitle(List<AmazonSpuTitleForUpdateDto> dtos, Map<String, String> skuMap, ResponseJson spuTitleResp) {
        List<SpuOfficial> spuInfos = (List<SpuOfficial>) spuTitleResp.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(spuInfos)) {
            return;
        }

        Map<String, SpuOfficial> spuOfficialMap = spuInfos.stream().collect(Collectors.toMap(o -> o.getSpu(), o -> o));

        for (AmazonSpuTitleForUpdateDto dto : dtos) {
            String sku = dto.getArticleNumber();
            if (StringUtils.isBlank(sku)) {
                continue;
            }

            String spu = skuMap.get(sku);
            if (StringUtils.isBlank(spu)) {
                continue;
            }

            // Amazon 标题
            SpuOfficial spuOfficial = ProductUtils.getAmazonOfficial(spu);
            if (spuOfficial == null || !titleIsComplete(spuOfficial)) {
                spuOfficial = spuOfficialMap.get(spu);
                if (spuOfficial == null) {
                    continue;
                }
            }


            //标题 1-SPU长标题 2-SKU标题
            List<String> titleList = null;
            if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
                titleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<List<String>>() {
                });
                titleList = titleList.stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(titleList) && StringUtils.isNotBlank(spuOfficial.getTitle())) {
                titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
                });
                titleList = titleList.stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                dto.setItemName(titleList.get(0));
                dto.setWenAnType(spuOfficial.getNeedWenAnType());
            }
        }
    }

    /**
     * 如果描述为100% brand new and high quality则过滤
     */
    public static String pointFilter(String bulletPoint) {
        String removedBulletPoint = removeEndPunctuation(bulletPoint);
        return removedBulletPoint
                .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP.getName(), "")
                .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP.getName(), "")
                .replace(BulletPointFilterEnum.ORIGINAL_FIELD.getName(), "")
                .replace(BulletPointFilterEnum.FIELD.getName(), "")
                .replace(BulletPointFilterEnum.UPPERCASE_FIELD.getName(), "")
                .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                .replace(BulletPointFilterEnum.ORIGINAL_FIELD_NO_100.getName(), "")
                .replace(BulletPointFilterEnum.FIELD_NO_100.getName(), "")
                .replace(BulletPointFilterEnum.UPPERCASE_FIELD_NO_100.getName(), "");
    }

    /**
     * 五点描述结尾不能包含特殊字符
     */
    public static String removeEndPunctuation(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        StringBuilder sb = new StringBuilder(text);
        while (sb.length() > 0 && PUNCTUATION_MARKS.contains(sb.charAt(sb.length() - 1))) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }





    private static Boolean titleIsComplete(SpuOfficial spuOfficial) {
        return StringUtils.isNotBlank(spuOfficial.getLongTitleJson())
                || (StringUtils.isNotBlank(spuOfficial.getTitle())
                && CollectionUtils.isNotEmpty(spuOfficial.getStringList(spuOfficial.getTitle())));
    }

    private static Boolean descIsComplete(SpuOfficial spuOfficial) {
        return StringUtils.isNotBlank(spuOfficial.getDescription())
                && CollectionUtils.isNotEmpty(spuOfficial.getStringList(spuOfficial.getDescription()));
    }

    private static Boolean newDescIsComplete(SpuOfficial spuOfficial) {
        return StringUtils.isNotBlank(spuOfficial.getNewDescription());
    }

    public static SpuOfficial getDefalutSpuOfficial(String spu) {
        ResponseJson resp = ProductUtils.getSpuTitles(List.of(spu));
        SpuOfficial spuOfficial = null;
        if (resp.isSuccess()) {
            List<SpuOfficial> spuInfos = (List<SpuOfficial>) resp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isNotEmpty(spuInfos)) {
                spuOfficial = spuInfos.get(0);
            }
        }
        if (spuOfficial == null) {
            spuOfficial = new SpuOfficial();
        }
        return spuOfficial;
    }

    public static String formatDescription(String description) {
        // 指定单词加换行处理
        if (!StringUtils.containsIgnoreCase(description, "Features:\n")) {
            description = description.replaceAll("(?i)Features:", "Features:\n");
        }
        if (!StringUtils.containsIgnoreCase(description, "Specifications:\n")) {
            description = description.replaceAll("(?i)Specifications:", "Specifications:\n");
        }
        if (!StringUtils.containsIgnoreCase(description, "Package Includes:\n")) {
            description = description.replaceAll("(?i)Package Includes:", "Package Includes:\n");
        }
        if (!StringUtils.containsIgnoreCase(description, "Note:\n")) {
            description = description.replaceAll("(?i)Note:", "Note:\n");
        }
        return description;
    }

    public static String getAmazonSpuOfficialDesc(String spu) {
        SpuOfficial amazonOfficial = ProductUtils.getAmazonOfficial(spu);
        if (null == amazonOfficial) {
            return null;
        }

        if (newDescIsComplete(amazonOfficial)) {
            return amazonOfficial.getNewDescription();
        }

        if (descIsComplete(amazonOfficial)) {
            return amazonOfficial.getDescription();
        }
        return null;
    }
}
