package com.estone.erp.publish.amazon.jobHandler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.submit.FeedType;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.AmazonListingInfringementHelper;
import com.estone.erp.publish.amazon.componet.AmazonProductListingEsBulkProcessor;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.componet.validation.executor.TemplateDataValidationExecutor;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.dto.AmazonUpdateInfringementDO;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonListingUtils;
import com.estone.erp.publish.amazon.util.AmazonTranslateUtils;
import com.estone.erp.publish.amazon.util.KeyWordUtils;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLogExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 定时任务：临时任务检测Amazon在线listing侵权词
 **/
@Slf4j
@Component
public class TempSendUpdateListingItemNameAccountJobHandler extends AbstractJobHandler {

    private static String[] esFields = {"accountNumber", "site", "sellerSku", "mainSku", "skuDataSource"};


    @Autowired
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Autowired
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private AmazonListingInfringementHelper amazonListingInfringementHelper;
    @Autowired
    private AmazonProductListingEsBulkProcessor amazonProductListingEsBulkProcessor;
    @Autowired
    private AmazonPublishOperationLogService amazonPublishOperationLogService;
    @Resource
    private AmazonInfringementWordHelper amazonInfringementWordHelper;
    @Resource
    private AmazonTemplateBuilderHelper amazonTemplateBuilderHelper;
    @Resource
    private TemplateDataValidationExecutor templateDataValidationExecutor;

    @Resource
    private AmazonCallService amazonCallService;

    public TempSendUpdateListingItemNameAccountJobHandler() {
        super("TempSendUpdateListingItemNameAccountJobHandler");
    }

    @Data
    static class InnerParam {
        private String type;
        private List<String> sites;
        private List<String> dataList;
    }

    private static final String PARSING_ERROR = "XML Parsing Error";

    @Override
    @XxlJob("TempSendUpdateListingItemNameAccountJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("执行失败，入参不能为空");
            return ReturnT.SUCCESS;
        }

        // 确认处理数据
        if ("confirm".equals(innerParam.getType())) {
            confirmData();
            return ReturnT.SUCCESS;
        }

        if (CollectionUtils.isEmpty(innerParam.getDataList())) {
            String paramValue = CacheUtils.SystemParamGet("amazon_param.update_listing_title_" + innerParam.getType()).getParamValue();
            if (StringUtils.isBlank(paramValue)) {
                // 未配置走店铺配置
                loadAmazonAccountRelationAccount(innerParam);
                if (CollectionUtils.isEmpty(innerParam.getDataList())) {
                    XxlJobLogger.log("执行失败，待处理数据为空");
                    return ReturnT.SUCCESS;
                }
            } else {
                List<String> dataList = JSON.parseArray(paramValue, String.class);
                innerParam.setDataList(dataList);
            }
        }

        if (CollectionUtils.isEmpty(innerParam.getDataList())) {
            XxlJobLogger.log("执行失败，待处理数据为空");
            return ReturnT.SUCCESS;
        }

        innerParam.getDataList().forEach(data -> {
            JSONObject message = new JSONObject();
            message.put("type", innerParam.getType());
            message.put("data", data);
            rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE_KEY, message);
        });
        XxlJobLogger.log("***************** 发送校验账号到队列结束 *****************");
        return ReturnT.SUCCESS;
    }

    private void loadAmazonAccountRelationAccount(InnerParam innerParam) {
        if ("account".equals(innerParam.getType())) {
            AmazonAccountRelationExample example = new AmazonAccountRelationExample();
            String columns = "account_number, account_country";
            example.setFiledColumns(columns);
            AmazonAccountRelationExample.Criteria criteria = example.createCriteria();
            if (CollectionUtils.isNotEmpty(innerParam.getSites())) {
                criteria.andAccountCountryIn(innerParam.getSites());
            }
            criteria.andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());

            List<AmazonAccountRelation> amazonAccountRelationList = amazonAccountRelationService.selectFiledColumnsByExample(example);
            if (CollectionUtils.isEmpty(amazonAccountRelationList)) {
                throw new BusinessException("未获取到账号数据");
            }
            List<String> accountNumbers = amazonAccountRelationList.stream().map(AmazonAccountRelation::getAccountNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            innerParam.setDataList(accountNumbers);
        }
    }

    /**
     * 确认同步完成后的listing，成功的数据更新侵权信息
     * T-1
     */
    private void confirmData() {
        LocalDate now = LocalDate.now().minusDays(1);
        LocalDateTime starTime = LocalDateTime.of(now, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        ZoneId zone = ZoneId.systemDefault();
        Date starDate = Date.from(starTime.atZone(zone).toInstant());
        Date endDate = Date.from(endTime.atZone(zone).toInstant());
        XxlJobLogger.log("confirmData time range {}-{}", starTime, endTime);


        int total = 0;
        int successNum = 0;
        int failNum = 0;

        Long lastId = null;
        while (true) {
            AmazonProcessReportExample example = new AmazonProcessReportExample();
            example.setFiledColumns("id,account_number,relation_id,status,status_code, data_value, finish_date, result_msg");
            example.setLimit(500);
            example.setOrderByClause("id");
            AmazonProcessReportExample.Criteria criteria = example.createCriteria();
            criteria.andFeedTypeEqualTo(FeedType.POST_PRODUCT_DATA);
            criteria.andRelationTypeEqualTo(ProcessingReportTriggleType.Listing_UPDATE_TITLE_DESC_TASK.name());
            criteria.andCreationDateBetween(starDate, endDate);
            criteria.andCreatedByEqualTo("admin");
            if (lastId != null) {
                criteria.andIdGreaterThan(lastId);
            }

            List<AmazonProcessReport> amazonProcessReports = amazonProcessReportService.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(amazonProcessReports)) {
                break;
            }

            for (AmazonProcessReport amazonProcessReport : amazonProcessReports) {
                AmazonPublishOperationLog operationLog = searchOperationLog(amazonProcessReport.getAccountNumber(), amazonProcessReport.getDataValue(), starTime, endTime);
                if (operationLog != null) {
                    updateOperationLog(operationLog, amazonProcessReport);
                }
                Boolean status = amazonProcessReport.getStatus();
                if (Boolean.TRUE.equals(status)) {
                    successNum++;
                } else {
                    // 如果报错信息存在特殊字符提交失败，取产品系统的最新标题描述重新提交
                    try {
                        resubmitNewTitleDescription(amazonProcessReport);
                    } catch (Exception e) {
                        XxlJobLogger.log("重新提交报错：" + e.getMessage());
                    }

                    failNum++;
                }
                lastId = amazonProcessReport.getId();
            }
            // 更新Listing侵权信息
            updateListingInfringementWordInfo(amazonProcessReports);
            total += amazonProcessReports.size();
        }
        XxlJobLogger.log("total:{},successNum:{},failNum:{}", total, successNum, failNum);
    }

    private void resubmitNewTitleDescription(AmazonProcessReport amazonProcessReport) {
        if (StringUtils.isBlank(amazonProcessReport.getResultMsg())
                || !amazonProcessReport.getResultMsg().contains(PARSING_ERROR)) {
            return;
        }

        // 获取listing
        String accountNumber = amazonProcessReport.getAccountNumber();
        String sellerSku = amazonProcessReport.getDataValue();
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setAccountNumber(accountNumber);
        request.setSellerSku(sellerSku);
        request.setTrademarkIdentification(AmazonListingInfringementHelper.BAD_TRADEMARK);
        request.setFields(AmazonListingInfringementHelper.fields);
        Page<EsAmazonProductListing> page = esAmazonProductListingService.page(request, 2, 0);
        if (null == page || CollectionUtils.isEmpty(page.getContent())) {
            return;
        }
        EsAmazonProductListing esAmazonProductListing = page.getContent().get(0);

        // 获取产品系统的标题描述
        AmazonTemplateBO templateInfo = amazonTemplateBuilderHelper.createTemplateInfo(esAmazonProductListing.getMainSku(), esAmazonProductListing.getSite(), esAmazonProductListing.getSkuDataSource(), accountNumber, 1);
        if (null == templateInfo
                || (StringUtils.isBlank(templateInfo.getTitle())
                && StringUtils.isBlank(templateInfo.getDescription()))) {
            throw new RuntimeException(String.format("获取产品标题描述失败，账号%s，sellerSku%s", accountNumber, sellerSku));
        }
        String srcLang = GoogleTranslateUtils.changeDestLang("us");
        String destLang = GoogleTranslateUtils.changeDestLang(esAmazonProductListing.getSite());
        // 除英语外翻译前 删除侵权词
       /* if (!"en".equals(destLang)) {
            ApiResult<String> delResult = templateDataValidationExecutor.delTemplateInfringementWords(templateInfo);
            if (!delResult.isSuccess()){
                templateDataValidationExecutor.delTemplateInfringementWords(templateInfo);
            }
        }*/

        // jp站点翻译前 删除侵权词
        if ("jp".equalsIgnoreCase(destLang)) {
            ApiResult<String> delResult = templateDataValidationExecutor.delTemplateInfringementWords(templateInfo);
            if (!delResult.isSuccess()) {
                templateDataValidationExecutor.delTemplateInfringementWords(templateInfo);
            }
        }

        // 翻译标题描述
        String title = templateInfo.getTitle();
        String description = templateInfo.getDescription();
        if (!srcLang.equals(destLang)) {
            List<String> stringList = new ArrayList<>();
            if (StringUtils.isNotBlank(title)) {
                stringList.add(title);
            }
            if (StringUtils.isNotBlank(description)) {
                stringList.add(description);
            }
            String var = AmazonTranslateUtils.translate(srcLang, destLang, stringList, 3);
            if (StringUtils.isBlank(var)) {
                throw new RuntimeException(String.format("翻译失败，账号%s，sellerSku%s", accountNumber, sellerSku));
            }
            List<String> transList = JSON.parseArray(var, String.class);
            if (transList.size() != stringList.size()) {
                throw new RuntimeException(String.format("翻译结果数量不一致，账号%s，sellerSku%s", accountNumber, sellerSku));
            }
            if (StringUtils.isNotBlank(title)) {
                title = transList.get(0);
            }
            if (StringUtils.isNotBlank(description)) {
                if (transList.size() == 1) {
                    description = transList.get(0);
                } else {
                    description = transList.get(1);
                }
            }
        }

        esAmazonProductListing.setItemName(title);
        esAmazonProductListing.setItemDescription(description);

        List<AmazonVariantBO> amazonVariantTitleList = new ArrayList<>();
        List<AmazonPublishOperationLog> operationLogs = new ArrayList<>();

        // 过滤侵权词，构建更新对象
        AmazonListingUtils.updateTitleDelInfringementWord(Lists.newArrayList(esAmazonProductListing), amazonVariantTitleList, operationLogs, false);

        // 截取标题描述长度
        lengthSplit(amazonVariantTitleList);

        if (CollectionUtils.isNotEmpty(operationLogs)) {
            //esPublishOperationLogService.saveAll(operationLogs);
        }
        if (CollectionUtils.isNotEmpty(amazonVariantTitleList)) {
            amazonCallService.publishVariantProductPartialUpdate(amazonVariantTitleList, Collections.singletonList(SpFeedType.POST_PRODUCT_DATA.getValue()));
        }
    }

    private void lengthSplit(List<AmazonVariantBO> amazonVariantTitleList) {
        if (CollectionUtils.isEmpty(amazonVariantTitleList)) {
            return;
        }

        for (AmazonVariantBO amazonVariantBO : amazonVariantTitleList) {
            // 标题
            String itemName = amazonVariantBO.getItemName();
            if (StringUtils.isNotEmpty(itemName)) {
                String title = itemName.replaceAll(",", " ").replaceAll("，", " ").replace("\n", " ").trim();
                amazonVariantBO.setItemName(StrUtil.changTitleLimit(title, 200));
            }

            // 描述
            String itemDescription = amazonVariantBO.getItemDescription();
            if (StringUtils.isNotEmpty(itemDescription)) {
                String descStr = itemDescription.replaceAll("\n", "<br/>");
                String newDescStr = KeyWordUtils.truncateText(descStr, 2000);
                amazonVariantBO.setItemDescription(newDescStr.replaceAll("<br/>", "\n"));
            }
        }
    }

    private void updateOperationLog(AmazonPublishOperationLog operationLog, AmazonProcessReport amazonProcessReport) {
        Map<String, Object> metaObj = JSON.parseObject(operationLog.getMetaObj(), Map.class);
        Object bardWord = metaObj.get("bardWord");
        if (bardWord != null) {
            List<WordValidateResult> dataWordList = (List<WordValidateResult>) bardWord;
            List<String> bardWords = dataWordList.stream().map(WordValidateResult::getOriginWord).collect(Collectors.toList());
            List<String> trademark = dataWordList.stream().map(WordValidateResult::getTrademarkIdentification).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            metaObj.put("bardWords", bardWords);
            metaObj.put("trademark", trademark);
        }
        metaObj.put("status", amazonProcessReport.getStatus());
        operationLog.setState(Boolean.TRUE.equals(amazonProcessReport.getStatus()) ? 1 : 0);
        metaObj.put("resultMsg", amazonProcessReport.getResultMsg());
        amazonPublishOperationLogService.updateByPrimaryKeySelective(operationLog);
    }

    private void updateListingInfringementWordInfo(List<AmazonProcessReport> updateListing) {
        if (CollectionUtils.isEmpty(updateListing)) {
            return;
        }
        Map<String, Date> finishDateMap = new HashMap<>();
        List<String> esIds = updateListing.stream()
                .filter(report -> Boolean.TRUE.equals(report.getStatus()))
                .filter(report -> StringUtils.isNotBlank(report.getAccountNumber()))
                .filter(report -> StringUtils.isNotBlank(report.getDataValue()))
                .map(report -> {
                    String esId = report.getAccountNumber() + "_" + report.getDataValue();
                    finishDateMap.put(esId, report.getFinishDate());
                    return esId;
                })
                .collect(Collectors.toList());


        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setIdList(esIds);
        request.setFields(AmazonListingInfringementHelper.fields);
        request.setTrademarkIdentification(AmazonListingInfringementHelper.BAD_TRADEMARK);
        Page<EsAmazonProductListing> page = esAmazonProductListingService.page(request, esIds.size() + 1, 0);
        List<EsAmazonProductListing> listingList = page.getContent();
        if (CollectionUtils.isEmpty(listingList)) {
            return;
        }
        // 相同ASIN认为数据一致合并为一次请求
        Map<String, List<EsAmazonProductListing>> asinListingMap = listingList.stream().collect(
                Collectors.groupingBy(item -> StringUtils.defaultString(item.getParentAsin(), "nonAsin")
                ));
        asinListingMap.forEach((asin, items) -> {
            AmazonExecutors.checkListingInfringementword(() -> {
                try {
                    if ("nonAsin".equals(asin)) {
                        // 没有ASIN的每一条单独执行
                        for (EsAmazonProductListing item : items) {
                            Date syncDate = item.getSyncDate();
                            Date finishDate = finishDateMap.get(item.getId());
                            if (finishDate != null && syncDate.compareTo(finishDate) > 0) {
                                AmazonProductListing amazonProductListing = AmazonListingUtils.getAmazonProductListing(item);
                                Optional<AmazonProductListing> optional = Optional.ofNullable(amazonProductListing);
                                if (optional.isPresent()) {
                                    amazonListingInfringementHelper.updateListingHandler(Collections.singletonList(item.getId()), amazonProductListing, true);
                                }
                            } else {
                                // 根据上次侵权校验信息更新本地数据
                                updateLocalEsListingHandler(Collections.singletonList(item.getId()), item);
                                updateLocalDBTitleHandler(Collections.singletonList(item.getId()), item);
                            }
                        }
                        return;
                    }
                    List<String> updateEsIds = items.stream().map(EsAmazonProductListing::getId).collect(Collectors.toList());
                    EsAmazonProductListing item = items.get(0);
                    Date syncDate = item.getSyncDate();
                    Date finishDate = finishDateMap.get(item.getId());
                    if (finishDate != null && syncDate.compareTo(finishDate) > 0) {
                        AmazonProductListing amazonProductListing = AmazonListingUtils.getAmazonProductListing(item);
                        Optional<AmazonProductListing> optional = Optional.ofNullable(amazonProductListing);
                        if (optional.isPresent()) {
                            amazonListingInfringementHelper.updateListingHandler(updateEsIds, amazonProductListing, true);
                        }
                    } else {
                        // 根据上次侵权校验信息更新本地数据
                        updateLocalEsListingHandler(updateEsIds, item);
                        updateLocalDBTitleHandler(updateEsIds, item);
                    }
                } catch (Exception e) {
                    log.error("AmazonIncrementTortCheckMqHandler中断异常:", e);
                }
            });
        });
    }

    private void updateLocalDBTitleHandler(List<String> esIds, EsAmazonProductListing item) {

    }

    public void updateLocalEsListingHandler(List<String> esIds, EsAmazonProductListing item) {
        List<WordValidateResult> infringementWordInfos = item.getInfringementWordInfos();
        if (CollectionUtils.isEmpty(infringementWordInfos)) {
            return;
        }
        List<String> brandWhiteListWords = amazonInfringementWordHelper.getBrandWhiteListWords();
        infringementWordInfos.removeIf(infringementWordInfo -> {
            String originWord = infringementWordInfo.getOriginWord();
            boolean match = brandWhiteListWords.stream().anyMatch(whiteWord -> StringUtils.equalsIgnoreCase(originWord, whiteWord));
            if (match) {
                return false;
            }
            Set<String> trademarkIdentification = infringementWordInfo.getTrademarkIdentification();
            if (CollectionUtils.isEmpty(trademarkIdentification)) {
                return false;
            }
            return trademarkIdentification.stream().anyMatch(AmazonListingInfringementHelper.BAD_TRADEMARK::contains);
        });
        AmazonUpdateInfringementDO updateInfringementDO = new AmazonUpdateInfringementDO(item.getId(), new Date());
        String infringBrandWord = null;
        List<String> trademarkIdentification = null;
        if (CollectionUtils.isNotEmpty(infringementWordInfos)) {
            HashSet<String> wordSet = infringementWordInfos.stream()
                    .map(WordValidateResult::getOriginWord)
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            infringBrandWord = StringUtils.join(wordSet, ",");
            updateInfringementDO.setInfringingBrandWord(infringBrandWord);

            trademarkIdentification = infringementWordInfos.stream()
                    .filter(o -> o != null && CollectionUtils.isNotEmpty(o.getTrademarkIdentification()))
                    .map(WordValidateResult::getTrademarkIdentification)
                    .collect(HashSet::new, HashSet::addAll, HashSet::addAll)
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            updateInfringementDO.setTrademarkIdentification(trademarkIdentification);
        }
        updateInfringementDO.setInfringementWordInfos(infringementWordInfos);
        for (String esId : esIds) {
            updateInfringementDO.setId(esId);
            amazonProductListingEsBulkProcessor.syncUpdateInfringementWord(updateInfringementDO);
        }
    }

    private AmazonPublishOperationLog searchOperationLog(String accountNumber, String sellerSku, LocalDateTime starTime, LocalDateTime endTime) {
        AmazonPublishOperationLogExample example = new AmazonPublishOperationLogExample();
        example.createCriteria()
                .andOpTypeEqualTo(ProcessingReportTriggleType.Listing_UPDATE_TITLE_DESC_TASK.name())
                .andPlatformEqualTo(SaleChannelEnum.AMAZON.getChannelName())
                .andUserEqualTo("admin")
                .andObjectEqualTo(accountNumber)
                .andObject1EqualTo(sellerSku)
                .andCreatedTimeGreaterThanOrEqualTo(Timestamp.valueOf(starTime))
                .andCreatedTimeLessThanOrEqualTo(Timestamp.valueOf(endTime));
        List<AmazonPublishOperationLog> operationLogs = amazonPublishOperationLogService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(operationLogs)) {
            return operationLogs.get(0);
        }
        return null;
    }
}
