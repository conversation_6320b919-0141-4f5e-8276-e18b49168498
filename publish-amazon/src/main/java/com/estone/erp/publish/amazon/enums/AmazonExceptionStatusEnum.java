package com.estone.erp.publish.amazon.enums;

/**
 * Amazon账号异常状态
 * <AUTHOR>
 * @date 2022/4/14 11:03
 */
public enum AmazonExceptionStatusEnum {
    KYC("KYC", "KYC"),
    TRO("TRO", "TRO"),
    HOLIDAY("H<PERSON><PERSON>AY", "休假");


    private String code;
    private String name;

    AmazonExceptionStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
