package com.estone.erp.publish.amazon.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-02 16:37
 */
@Data
public class GenerateGPSRImageRequest {
    /**
     * 店铺
     */
    @NotBlank(message = "店铺不能为空")
    private String accountNumber;

    /**
     * 产品系统货号
     */
    @NotBlank(message = "产品系统货号不能为空")
    private String articleNumber;

    /**
     * 数据来源
     */
    @NotNull(message = "数据来源不能为空")
    private Integer skuDataSource;

    /**
     * sellerSkuList
     */
    @NotEmpty(message = "sellerSkuList不能为空")
    private List<String> sellerSkuList;
}
