package com.estone.erp.publish.amazon.hbase.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 15:14
 * @description 属性注释参看 {@link com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing}
 */
@Data
public class AmazonListingCheckword {

    private String id;

    private String accountNumber;

    private String site;

    private String sellerSku;

    private String mainSku;

    private String articleNumber;

    private String itemName;

    private String itemDescription;

    private String brandName;

    private String infringingBrandWord;

    /**
     * 商标词标识 集合字符串
     */
    private String trademarkIdentification;
}
