package com.estone.erp.publish.amazon.call.parse;

import com.estone.erp.publish.amazon.call.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ProductParser {
    @SuppressWarnings("unchecked")
    public static Map<String, String> parseAttrs(Product product) {
        if (product == null) {
            return new HashMap<String, String>(0);
        }

        Map<String, String> result = new HashMap<String, String>(13);
        try {
            Document doc = DocumentHelper.parseText(product.getAttributeSetsXml());
            Element root = doc.getRootElement();
            Element item = root.element("ItemAttributes");
            result.put("Binding", item.elementTextTrim("Binding"));
            result.put("Brand", item.elementTextTrim("Brand"));
            List<Element> featureEles = item.elements("Feature");
            String features = String.join("<br />",
                    featureEles.stream().map(feature -> feature.getTextTrim()).collect(Collectors.toList()));
            result.put("Feature", features);
            result.put("Label", item.elementTextTrim("Label"));
            result.put("Manufacturer", item.elementTextTrim("Manufacturer"));
            result.put("MaterialType", item.elementTextTrim("MaterialType"));
            result.put("PartNumber", item.elementTextTrim("PartNumber"));
            result.put("ProductGroup", item.elementTextTrim("ProductGroup"));
            result.put("ProductTypeName", item.elementTextTrim("ProductTypeName"));
            result.put("Publisher", item.elementTextTrim("Publisher"));
            result.put("URL", item.element("SmallImage").elementTextTrim("URL"));
            result.put("Studio", item.elementTextTrim("Studio"));
            result.put("Title", item.elementTextTrim("Title"));
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        return result;
    }

    public static String parseRelationParent(Product product) {
        String result = null;
        if (product == null) {
            return result;
        }

        try {
            Document doc = DocumentHelper.parseText(product.getRelationshipsXml());
            Element root = doc.getRootElement();
            Element variationParent = root.element("VariationParent");
            if (variationParent != null) {
                result = variationParent.element("Identifiers").element("MarketplaceASIN").elementTextTrim("ASIN");
            }
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    public static List<String> parseRelationChildren(Product product) {
        List<String> result = new ArrayList<String>(0);
        if (product == null) {
            return result;
        }

        try {
            Document doc = DocumentHelper.parseText(product.getRelationshipsXml());
            Element root = doc.getRootElement();
            List<Element> variationChilds = root.elements("VariationChild");
            if (CollectionUtils.isNotEmpty(variationChilds)) {
                result = variationChilds.stream()
                        .map(child -> child.element("Identifiers").element("MarketplaceASIN").elementTextTrim("ASIN"))
                        .collect(Collectors.toList());
            }
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        return result;
    }
}
