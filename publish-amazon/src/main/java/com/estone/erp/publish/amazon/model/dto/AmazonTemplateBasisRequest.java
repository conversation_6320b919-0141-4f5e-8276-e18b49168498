package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-09 18:18
 */
@Data
public class AmazonTemplateBasisRequest {
    private String accountNumber;
    private String spu;
    private String site;
    private String productType;
    private Integer skuDataSource;
    private Integer interfaceType = TemplateInterfaceTypeEnums.XSD.getCode();
    private List<String> sonSkus;

    private List<String> accountNumberList;

    private String sellersku;

}
