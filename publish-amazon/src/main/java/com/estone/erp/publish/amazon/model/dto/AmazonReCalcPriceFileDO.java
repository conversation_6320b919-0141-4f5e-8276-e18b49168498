package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Amazon 批量文件算价
 * <AUTHOR>
 * @date 2023-12-04 11:39
 */
@Data
public class AmazonReCalcPriceFileDO {
    @ExcelProperty("账号")
    private String accountNumber;

    @ExcelProperty("子asin码")
    private String sonAsin;

    @ExcelProperty("sellerSku")
    private String sellerSku;

    @ExcelProperty("父asin码")
    private String asin;

    @ExcelProperty("毛利率")
    private String grossProfit;

    @ExcelProperty("链接价格")
    private String linkPrice;

    @ExcelProperty("算后价格")
    private String calcPrice;


}
