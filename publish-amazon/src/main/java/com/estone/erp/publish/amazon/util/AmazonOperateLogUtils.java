package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonAccountRelationBO;
import com.estone.erp.publish.amazon.enums.AmazonLogFieldEnum;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.enums.SkuCreateTimeTypeEnum;
import com.estone.erp.publish.amazon.model.AmazonCalcPriceRule;
import com.estone.erp.publish.amazon.model.AmazonOperateLog;
import com.estone.erp.publish.amazon.model.AmazonSellerSkuRule;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.util.model.SalesTypeEnum;
import com.estone.erp.publish.common.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 操作日志工具类
 * <AUTHOR>
 * @Date 2021/6/4
 */
@Slf4j
public class AmazonOperateLogUtils {

    /**
     * 忽略字段
     */
    private static List<String> filterKey = Arrays.asList("createdBy", "creationDate", "syncTime", "accountCountry",
            "accountStatus", "createTime", "createDate", "updateBy", "updatedBy","lastUpdatedBy", "updateTime", "updateDate",
            "lastUpdateDate", "amazonSellerSkuRuleList", "accountLevel", "prodCategoryNames", "fromPrice",
            "toPrice", "lastUpdateBy", "createBy","amazonCalcPriceRuleList", "amazonShippingCostModelList",
            "labelList", "isCompleteConfig", "onlineItemNum","fromWeight", "toWeight", "salesType", "fromSales",
            "toSales", "fromInventory", "toInventory", "fromInputTime", "toInputTime","productCategoryNames",
            "prodLevel2CategoryNameList","attributeNameRoute","attributeNameType","updatedTime","createdTime",
            "authBrand", "recordBrand", "titleRule", "accountOfflineConfig", "manufacturer", "manufacturerAddress",
            "manufacturerCn", "manufacturerEmail", "manufacturerEn", "manufacturerTel", "merchantId");

    /**
     * 空值
     */
    private static final String NULL_VALUE = "null";

    /**
     * 分类
     */
    private static final String CATEGORY_CODE = "prodCategoryCodes";

    /**
     * ProductTypeTemplate 分类
     */
    private static final String PRODUCT_TYPE_CATEGORY_CODE = "productCategoryCodes";

    /**
     * 分类全选
     */
    public static final String SELECT_ALL_CATEGORY = "分类全选";


    /**
     * 更新操作日志
     */
    public static List<AmazonOperateLog> buildUpdateLog(Object oldBean, Object newBean, AmazonOperateLogEnum operateLogEnum, String businessId, Predicate<String> filterFiledPredicate) {
        if (oldBean == null || newBean == null) {
            return null;
        }
        Map<String, String> oldFieldMap = getFieldMap(oldBean, false, businessId);

        // 新对象忽略null 取的是传入修改的数据 null 数据库忽略修改
        Map<String, String> newFieldMap = getFieldMap(newBean, true, businessId);

        List<AmazonOperateLog> amazonOperateLogArrayList = new ArrayList<>();
        for (Map.Entry<String, String> entry : oldFieldMap.entrySet()) {
            String key = entry.getKey();
            // 忽略的字段不比较 新对象不存在 为null忽略
            if (filterFiledPredicate.test(key)) {
                continue;
            }

            // 相等不记录日志
            if (entry.getValue().equals(newFieldMap.get(key))) {
                continue;
            }

            // 为空也会保存，均为空不修改
            if (isEmpty(entry.getValue()) && isEmpty(newFieldMap.get(key))) {
                continue;
            }

            // 记录修改日志
            AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
            amazonOperateLogArrayList.add(amazonOperateLog);

            if (StringUtils.isNotBlank(businessId)) {
                try {
                    String businessIdValue = oldFieldMap.get(businessId);
                    amazonOperateLog.setBusinessId(Integer.valueOf(businessIdValue));
                } catch (Exception e) {}
            }

            String accountNumberKey = "accountNumber";
            amazonOperateLog.setAccountNumber(newFieldMap.get(accountNumberKey));
            amazonOperateLog.setType(operateLogEnum.name());
            amazonOperateLog.setFieldName(key);
            amazonOperateLog.setBefore(entry.getValue());
            amazonOperateLog.setAfter(newFieldMap.get(key));
            amazonOperateLog.setCreateBy(WebUtils.getUserName());
            amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        }
        return amazonOperateLogArrayList;
    }

    /**
     * 更新操作日志
     */
    public static List<AmazonOperateLog> buildUpdateLog(Object oldBean, Object newBean, AmazonOperateLogEnum operateLogEnum, String businessId) {
        return buildUpdateLog(oldBean, newBean, operateLogEnum, businessId, filterKey::contains);
    }

    /**
     * 新增操作日志
     */
    public static AmazonOperateLog buildAddLog(Object bean, AmazonOperateLogEnum operateLogEnum, Integer businessId) {

        AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
        amazonOperateLog.setType(operateLogEnum.name());
        amazonOperateLog.setBusinessId(businessId);
        String fieldName = null;
        if (bean instanceof AmazonSellerSkuRule) {
            fieldName = "SellerSKU生成规则";
        }
        if (bean instanceof AmazonCalcPriceRule) {
            fieldName = String.format("序号%s算价规则", ((AmazonCalcPriceRule) bean).getId());
        }
        if (bean instanceof AmazonShippingCostModel) {
            fieldName = String.format("序号%s运费模板配置", ((AmazonShippingCostModel) bean).getId());
        }
        amazonOperateLog.setFieldName(fieldName);
        amazonOperateLog.setBefore("");
        amazonOperateLog.setAfter("新增");
        amazonOperateLog.setMessage(JSON.toJSONString(bean));
        amazonOperateLog.setCreateBy(WebUtils.getUserName());
        amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

        return amazonOperateLog;
    }

    /**
     * 删除操作日志
     */
    public static AmazonOperateLog buildDelLog(Object bean, AmazonOperateLogEnum operateLogEnum, Integer businessId) {
        AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
        amazonOperateLog.setType(operateLogEnum.name());
        amazonOperateLog.setBusinessId(businessId);
        String fieldName = null;
        if (bean instanceof AmazonSellerSkuRule) {
            fieldName = "SellerSKU生成规则";
        }
        if (bean instanceof AmazonCalcPriceRule) {
            fieldName = String.format("序号%s算价规则", ((AmazonCalcPriceRule) bean).getId());
        }
        if (bean instanceof AmazonShippingCostModel) {
            fieldName = String.format("序号%s运费模板配置", ((AmazonShippingCostModel) bean).getId());
        }
        amazonOperateLog.setFieldName(fieldName);
        amazonOperateLog.setBefore("");
        amazonOperateLog.setAfter("刪除");
        amazonOperateLog.setMessage(JSON.toJSONString(bean));
        amazonOperateLog.setCreateBy(WebUtils.getUserName());
        amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

        return amazonOperateLog;
    }

    /**
     *  获取map对象
     */
    private static Map<String, String> getFieldMap(Object bean, boolean ignoreNull, String businessId) {
        Map<String, String> fieldMap = new HashMap<>();
        List<Field> oldFieldsList = FieldUtils.getAllFieldsList(bean.getClass());
        for (Field o : oldFieldsList) {
            if(filterKey.contains(o.getName())){
                continue;
            }

            try {
                o.setAccessible(true);
                Object val = o.get(bean);

                if(ignoreNull && null == val) {
                    continue;
                }

                fieldMap.put(o.getName(), val == null ? "" : val.toString());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return fieldMap;
    }

    /**
     *  判断object是否为空
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if ((obj instanceof List)) {
            return ((List) obj).size() == 0;
        }
        if ((obj instanceof String)) {
            return ((String) obj).trim().equals("");
        }
        return false;
    }

    /**
     * 处理特殊的日志
     * @param configLogs
     */
    public static void tranSpecialLog(List<AmazonOperateLog> configLogs) {
        if(CollectionUtils.isEmpty(configLogs)) {
            return;
        }

        Iterator<AmazonOperateLog> it = configLogs.iterator();
        while(it.hasNext()) {
            try {
                AmazonOperateLog operateLog = it.next();
                String fieldName = operateLog.getFieldName();
                String after = operateLog.getAfter();
                if (CATEGORY_CODE.equals(fieldName) && !SELECT_ALL_CATEGORY.equals(after)) {
                    changeCategoryLog(operateLog);
                    // 可能只是字符顺序发生了变化 实际未改变
                    if (StringUtils.isBlank(operateLog.getBefore()) && StringUtils.isBlank(operateLog.getAfter())) {
                        it.remove();
                    }
                }else if (PRODUCT_TYPE_CATEGORY_CODE.equals(fieldName) && !SELECT_ALL_CATEGORY.equals(after)){
                    changeCategoryLog(operateLog);
                    // 可能只是字符顺序发生了变化 实际未改变
                    if (StringUtils.isBlank(operateLog.getBefore()) && StringUtils.isBlank(operateLog.getAfter())) {
                        it.remove();
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 分类日志特殊处理
     * @param operateLog
     */
    private static void changeCategoryLog(AmazonOperateLog operateLog) {
        String before = operateLog.getBefore();
        List<String> beforeCategoryIds = CommonUtils.splitList(before, ",");

        String after = operateLog.getAfter();
        List<String> afterCategoryIds =CommonUtils.splitList(after, ",");

        // 之前有 且不存在修改之后中则为删除
        List<String> delCategaryIds = beforeCategoryIds.stream()
                .filter(o -> !afterCategoryIds.contains(o)).collect(Collectors.toList());

        // 之前无 且存在修改之后中则为新增
        List<String> addCategaryIds = afterCategoryIds.stream()
                .filter(o -> !beforeCategoryIds.contains(o)).collect(Collectors.toList());

        List<String> afterArr = new ArrayList<>();
        StringBuffer deStringBuffer= new StringBuffer();

        if(CollectionUtils.isNotEmpty(delCategaryIds)) {
            deStringBuffer.append("删除：" +  StringUtils.join(delCategaryIds, ","));
            String delStr = deStringBuffer.toString();
            afterArr.add(delStr);
        }

        StringBuffer addStringBuffer = new StringBuffer();
        if(CollectionUtils.isNotEmpty(addCategaryIds)) {
            addStringBuffer.append("增加：" + StringUtils.join(addCategaryIds, ","));
            String addStr = addStringBuffer.toString();
            afterArr.add(addStr);
        }
        operateLog.setBefore(before);
        operateLog.setAfter(CollectionUtils.isNotEmpty(afterArr) ? JSON.toJSONString(afterArr) : "");
    }

    /**
     * 算价规则价格区间日志特殊处理
     * @param oldValue
     * @param newValue
     * @param logType
     * @return
     */
    public static AmazonOperateLog changePriceSectionLog(AmazonCalcPriceRule oldValue, AmazonCalcPriceRule newValue, String logType) {
        Double oldFromPrice = oldValue.getFromPrice();
        Double oldToPrice = oldValue.getToPrice();
        Double newFromPrice = newValue.getFromPrice();
        Double newToPrice = newValue.getToPrice();
        if (!StringUtils.equalsIgnoreCase(String.valueOf(oldFromPrice), String.valueOf(newFromPrice))
                || !StringUtils.equalsIgnoreCase(String.valueOf(oldToPrice), String.valueOf(newToPrice))) {
            AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
            amazonOperateLog.setType(logType);
            amazonOperateLog.setBusinessId(newValue.getId());
            amazonOperateLog.setFieldName(AmazonLogFieldEnum.PRICE_SECTION.getFieldEn());
            amazonOperateLog.setBefore(oldFromPrice + "<=产品总价<" + oldToPrice);
            amazonOperateLog.setAfter(newFromPrice + "<=产品总价<" + newToPrice);
            amazonOperateLog.setCreateBy(WebUtils.getUserName());
            amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

            return amazonOperateLog;
        }

        return null;
    }

    /**
     * 处理店铺配置区间字段
     * @param oldValue
     * @param newValue
     * @return
     */
    public static List<AmazonOperateLog> handleSectionLog(AmazonAccountRelationBO oldValue, AmazonAccountRelationBO newValue) {
        List<AmazonOperateLog> amazonOperateLogs = new ArrayList<>();

        // 产品重量限制区间
        Double oldFromWeight = oldValue.getFromWeight();
        Double oldToWeight = oldValue.getToWeight();
        Double newFromWeight = newValue.getFromWeight();
        Double newToWeight = newValue.getToWeight();
        AmazonOperateLog log1 = generateSessionLog(String.valueOf(oldFromWeight), String.valueOf(oldToWeight), String.valueOf(newFromWeight),
                String.valueOf(newToWeight), newValue.getId(), AmazonLogFieldEnum.PRODUCT_WEIGHT.getFieldEn());
        if (null != log1) {
            amazonOperateLogs.add(log1);
        }

        // 销售成本价区间
        Double oldFromPrice = oldValue.getFromPrice();
        Double oldToPrice = oldValue.getToPrice();
        Double newFromPrice = newValue.getFromPrice();
        Double newToPrice = newValue.getToPrice();
        AmazonOperateLog log2 = generateSessionLog(String.valueOf(oldFromPrice), String.valueOf(oldToPrice), String.valueOf(newFromPrice),
                String.valueOf(newToPrice), newValue.getId(), AmazonLogFieldEnum.SALE_PRICE.getFieldEn());
        if (null != log2) {
            amazonOperateLogs.add(log2);
        }

        // 库存限制区间
        Integer oldFromInventory = oldValue.getFromInventory();
        Integer oldToInventory = oldValue.getToInventory();
        Integer newFromInventory = newValue.getFromInventory();
        Integer newToInventory = newValue.getToInventory();
        AmazonOperateLog log3 = generateSessionLog(String.valueOf(oldFromInventory), String.valueOf(oldToInventory), String.valueOf(newFromInventory),
                String.valueOf(newToInventory), newValue.getId(), AmazonLogFieldEnum.PRODUCT_INVENTORY.getFieldEn());
        if (null != log3) {
            amazonOperateLogs.add(log3);
        }

        // 产品录入时间区间
        Integer oldSkuCreateTimeType = oldValue.getSkuCreateTimeType();
        Integer newSkuCreateTimeType = newValue.getSkuCreateTimeType();
        if (SkuCreateTimeTypeEnum.MONTH.getCode().equals(oldSkuCreateTimeType) || SkuCreateTimeTypeEnum.MONTH.getCode().equals(newSkuCreateTimeType)){
            Integer oldFromInputTime = oldValue.getFromInputTime();
            Integer oldToInputTime = oldValue.getToInputTime();
            Integer newFromInputTime = newValue.getFromInputTime();
            Integer newToInputTime = newValue.getToInputTime();
            AmazonOperateLog log4 =generateSessionLog(String.valueOf(oldFromInputTime), String.valueOf(oldToInputTime), String.valueOf(newFromInputTime),
                    String.valueOf(newToInputTime), newValue.getId(), AmazonLogFieldEnum.PRODUCT_INPUT_TIME.getFieldEn());
            if (null != log4) {
                amazonOperateLogs.add(log4);
            }
        }

        // 指定销量区间
        Integer oldFromSales = oldValue.getFromSales();
        Integer oldToSales = oldValue.getToSales();
        Integer newFromSales = newValue.getFromSales();
        Integer newToSales = newValue.getToSales();
        Integer oldSalesType = oldValue.getSalesType();
        Integer newSalesType = newValue.getSalesType();
        if (!StringUtils.equalsIgnoreCase(String.valueOf(oldFromSales), String.valueOf(newFromSales))
                || !StringUtils.equalsIgnoreCase(String.valueOf(oldToSales), String.valueOf(newToSales))) {
            AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
            amazonOperateLog.setType(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name());
            amazonOperateLog.setBusinessId(newValue.getId());
            amazonOperateLog.setFieldName(AmazonLogFieldEnum.SALES_SECTION.getFieldEn());
            if (null == oldFromSales && null == oldToSales && null == oldSalesType) {
                amazonOperateLog.setBefore("新增");
            } else {
                amazonOperateLog.setBefore((oldSalesType == null ? "" : SalesTypeEnum.getNameByCode(oldSalesType) + "：") + oldFromSales + "——" + oldToSales);
            }
            if (null == newFromSales && null == newToSales && null == newSalesType) {
                amazonOperateLog.setAfter("删除");
            } else {
                amazonOperateLog.setAfter((newSalesType == null ? "" : SalesTypeEnum.getNameByCode(newSalesType) + "：") + newFromSales + "——" + newToSales);
            }
            amazonOperateLog.setCreateBy(WebUtils.getUserName());
            amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

            amazonOperateLogs.add(amazonOperateLog);
        }

        return amazonOperateLogs;
    }

    /**
     * 生成区间字段日志
     * @param oldFromValue
     * @param oldToValue
     * @param newFromValue
     * @param newToValue
     * @param id
     * @param type
     * @return
     */
    private static AmazonOperateLog generateSessionLog(String oldFromValue, String oldToValue, String newFromValue, String newToValue, Integer id, String type) {
        if (!StringUtils.equalsIgnoreCase(oldFromValue, newFromValue)
                || !StringUtils.equalsIgnoreCase(oldToValue, newToValue)) {
            AmazonOperateLog amazonOperateLog = new AmazonOperateLog();
            amazonOperateLog.setType(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name());
            amazonOperateLog.setBusinessId(id);
            amazonOperateLog.setFieldName(type);
            if (NULL_VALUE.equals(oldFromValue) && NULL_VALUE.equals(oldToValue)) {
                amazonOperateLog.setBefore("新增");
            } else {
                amazonOperateLog.setBefore(oldFromValue + "——" + oldToValue);
            }
            if (NULL_VALUE.equals(newFromValue) && NULL_VALUE.equals(newToValue)) {
                amazonOperateLog.setAfter("删除");
            } else {
                amazonOperateLog.setAfter(newFromValue + "——" + newToValue);
            }
            amazonOperateLog.setCreateBy(WebUtils.getUserName());
            amazonOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

            return amazonOperateLog;
        }

        return null;
    }
}