package com.estone.erp.publish.amazon.call.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;

/**
 * class工具类
 */
@Slf4j
public class ClassUtils {
    /**
     * java八种基本类型
     */
    public static final List<Type> BASE_TYPES = Arrays.asList(Integer.class, Short.class, Byte.class, Long.class,
            Double.class, Float.class, Character.class, Boolean.class);

    /**
     * 是否指定的类型
     * 
     * @param srcType
     * @param destType
     * @return
     */
    public static boolean isSpecifiedType(Type srcType, Type destType) {
        return srcType != null && srcType == destType;
    }

    /**
     * 是否为java八种基础类型
     * 
     * @param type
     * @return
     */
    public static boolean isBaseType(Type type) {
        for (Type baseType : BASE_TYPES) {
            if (isSpecifiedType(type, baseType)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否为java八种基础类型
     * 
     * @param obj
     * @return
     */
    public static boolean isBaseType(Object obj) {
        if (obj == null) {
            return false;
        }

        return isBaseType(obj.getClass());
    }

    /**
     * 将字符串转换为基础类型的值对象
     * 
     * @param type
     * @param value
     * @return
     */
    public static Object getBaseTypeObj(Type type, String value) {
        if (type == null || StringUtils.isEmpty(value)) {
            return null;
        }

        String typeName = type.getTypeName();
        switch (typeName) {
            case "java.lang.Integer":
                return Integer.valueOf(value);
            case "java.lang.Boolean":
                return Boolean.valueOf(value);
            case "java.lang.Short":
                return Short.valueOf(value);
            case "java.lang.Byte":
                return Byte.valueOf(value);
            case "java.lang.Long":
                return Long.valueOf(value);
            case "java.lang.Double":
                return Double.valueOf(value);
            case "java.lang.Float":
                return Float.valueOf(value);
            case "java.lang.Character":
                return Character.valueOf(value.charAt(0));
            default:
                break;
        }

        return null;
    }

    /**
     * 获取Obj的string值
     * 
     * @param obj
     * @return
     */
    public static String getStringValue(Object obj) {
        if (obj == null) {
            return null;
        }

        if (isSpecifiedType(obj.getClass(), String.class) || ClassUtils.isBaseType(obj.getClass())) {
            return String.valueOf(obj);
        }

        return JSON.toJSONString(obj);
    }

    /**
     * 根据父类获取指定类的泛型类型
     * 
     * @param clazz 类
     * @param index 泛型索引位置
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getParameterizedTypeBySuper(Class<?> clazz, int index) {
        if (clazz == null) {
            return null;
        }

        Type genericSuperclass = clazz.getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            return (Class<T>) parameterizedType.getActualTypeArguments()[index];
        }

        log.warn("get ({}) parameterizedType index({}) failed,please check this class.", clazz, index);
        return null;
    }

    /**
     * 根据接口获取指定类的泛型类型
     * 
     * @param clazz 类
     * @param ifIndex 接口索引位置
     * @param typeIndex 泛型索引位置
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getParameterizedTypeByIf(Class<?> clazz, int ifIndex, int typeIndex) {
        if (clazz == null) {
            return null;
        }

        Type[] types = clazz.getGenericInterfaces();
        if (types.length > ifIndex) {
            Type type = types[ifIndex];
            if (type instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) type;
                return (Class<T>) parameterizedType.getActualTypeArguments()[typeIndex];
            }
        }

        log.warn("get ({}) parameterizedType ifIndex({}) typeIndex({}) failed,please check this class.", clazz, ifIndex,
                typeIndex);
        return null;
    }

    /**
     * 满足指定类的子类，并返回class
     * 
     * @param classVal
     * @param destClazz
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <E> Class<? extends E> getClassSatifyConClass(String classVal, Class<E> destClazz) {
        if (StringUtils.isBlank(classVal) || destClazz == null) {
            return null;
        }

        try {
            Class<?> clazz = Class.forName(classVal);
            if (destClazz.isAssignableFrom(clazz)) {
                return (Class<? extends E>) clazz;
            }
        }
        catch (ClassNotFoundException e) {
            log.warn(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取json值的对象，基础类型和string类型
     * 
     * @param value
     * @param clazz
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getJsonObj(String value, Class<T> clazz) {
        if (value == null || clazz == null) {
            return null;
        }

        if (ClassUtils.isBaseType(clazz)) {
            return (T) ClassUtils.getBaseTypeObj(clazz, value);
        }

        if (ClassUtils.isSpecifiedType(clazz, String.class)) {
            return (T) value;
        }

        return JSON.parseObject(value, clazz);
    }

    /**
     * 获取对象的json值，基础类型和string类型
     * 
     * @param value
     * @param typerRefer
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getJsonObj(String value, TypeReference<T> typerRefer) {
        if (value == null || typerRefer == null) {
            return null;
        }

        Type type = typerRefer.getType();
        if (ClassUtils.isBaseType(type)) {
            return (T) ClassUtils.getBaseTypeObj(type, value);
        }

        if (ClassUtils.isSpecifiedType(type, String.class)) {
            return (T) value;
        }

        return JSON.parseObject(value, typerRefer);
    }
}
