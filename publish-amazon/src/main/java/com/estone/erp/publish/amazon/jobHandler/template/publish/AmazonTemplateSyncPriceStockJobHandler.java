package com.estone.erp.publish.amazon.jobHandler.template.publish;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.componet.templatestatus.AmazonTemplatePublishStatusHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 价格库存同步定时任务
 * @ClassName: PriceStockSyncScheduledJobHandler
 * @Author: System
 * @Date: 2024年1月1日
 * @Version: 1.0.0
 */
@Component
public class AmazonTemplateSyncPriceStockJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonTemplatePublishStatusHelper amazonTemplatePublishStatusHelper;

    public AmazonTemplateSyncPriceStockJobHandler() {
        super("AmazonTemplateSyncPriceStockJobHandler");
    }

    @Data
    private static class InnerParam {
        /**
         * 增量处理开始时间（距离当前时间的分钟数）
         */
        private int incrementalBeforeMinutes = 0;

        /**
         * 增量处理结束时间（距离当前时间的分钟数）
         */
        private int incrementalAfterMinutes = 30;

        /**
         * 全量检查时间段（小时）
         */
        private List<Integer> fullTimeHour;

        /**
         * 全量检查时间范围（小时）
         */
        private int fullTimeRangeHours = 72;

        /**
         * 全量检查超时时间（小时）
         */
        private int fullTimeoutHours = 48;
    }

    @Override
    @XxlJob("AmazonTemplateSyncPriceStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数错误");
            return ReturnT.FAIL;
        }

        LocalDateTime executeTime = LocalDateTime.now();
        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("*****************定时处理Amazon价格库存同步任务开始*****************");

        // 判断是否为全量检查时间
        int currentHour24 = executeTime.getHour();
        if (innerParam.getFullTimeHour() != null && innerParam.getFullTimeHour().contains(currentHour24)) {
            // 全量检查模式
            XxlJobLogger.log("*****************执行全量价格库存同步检查*****************");
            return executeFullCheck(innerParam, startTime);
        } else {
            // 增量检查模式
            XxlJobLogger.log("*****************执行增量价格库存同步检查*****************");
            return executeIncrementalCheck(innerParam, startTime);
        }
    }

    /**
     * 执行增量检查
     *
     * @param innerParam 参数
     * @param startTime  开始时间
     * @return ReturnT<String> 执行结果
     */
    private ReturnT<String> executeIncrementalCheck(InnerParam innerParam, long startTime) {
        try {
            int total = amazonTemplatePublishStatusHelper.handlePriceStockSync(
                    innerParam.getIncrementalBeforeMinutes(),
                    innerParam.getIncrementalAfterMinutes()
            );
            
            long endTime = System.currentTimeMillis();
            XxlJobLogger.log("增量价格库存同步任务执行完成，处理数量：{}，耗时：{}秒", 
                    total, (endTime - startTime) / 1000L);
            
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log("增量价格库存同步任务执行失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 执行全量检查（超时处理）
     *
     * @param innerParam 参数
     * @param startTime  开始时间
     * @return ReturnT<String> 执行结果
     */
    private ReturnT<String> executeFullCheck(InnerParam innerParam, long startTime) {
        try {

            // 处理超时的价格库存同步（超过48小时的）
            int timeoutTotal = amazonTemplatePublishStatusHelper.handleFullPriceStockSync(
                    innerParam.getFullTimeoutHours()
            );
            XxlJobLogger.log("全量价格库存同步超时处理完成，处理数量：{}", timeoutTotal);

            // 处理正常的价格库存同步（最近三天内的）
            int normalTotal = amazonTemplatePublishStatusHelper.handlePriceStockSync(
                    0,
                    innerParam.getFullTimeRangeHours() * 60
            );
            XxlJobLogger.log("全量价格库存同步处理完成，处理数量：{}", normalTotal);


            long endTime = System.currentTimeMillis();
            XxlJobLogger.log("全量价格库存同步任务执行完成，正常处理：{}，超时处理：{}，耗时：{}秒", 
                    normalTotal, timeoutTotal, (endTime - startTime) / 1000L);
            
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log("全量价格库存同步任务执行失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }
} 