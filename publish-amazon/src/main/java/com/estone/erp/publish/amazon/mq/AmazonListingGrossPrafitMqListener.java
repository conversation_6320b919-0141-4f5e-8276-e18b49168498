package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.model.AmazonCalcPriceRule;
import com.estone.erp.publish.amazon.model.dto.AmazonListingCalcProfitBean;
import com.estone.erp.publish.amazon.service.AmazonCalcPriceRuleService;
import com.estone.erp.publish.amazon.util.AmazonCalcPriceUtil;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ${监听消费新版Amazon同步产品txt数据}
 */
@Slf4j
public class AmazonListingGrossPrafitMqListener implements ChannelAwareMessageListener {

    private String [] fileds;
    private String userName;

    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonCalcPriceRuleService amazonCalcPriceRuleService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }
        //log.info("AmazonListingGrossPrafitMqListener message body -> {}", body);

        Boolean isSuccess = doService(body);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        String accountNumber = null;
        try {
            String bodyStr = JSON.parseObject(body, String.class);
            accountNumber = bodyStr;
            if (StringUtils.isEmpty(accountNumber)) {
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            log.error("解析 mq消息体异常 -> {}", body);
            return Boolean.FALSE;
        };
        try {
            List<AmazonCalcPriceRule> amazonCalcPriceRules = amazonCalcPriceRuleService.selectByAccountNumber(accountNumber);
            if(CollectionUtils.isEmpty(amazonCalcPriceRules)) {
                log.warn(accountNumber + "店铺算价配置为空，不计算在线listing毛利，毛利率!");
                return Boolean.TRUE;
            }
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setFields(fileds);
            esAmazonProductListingRequest.setAccountNumber(accountNumber);
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setIsExistGrossProfit(false);
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            if (CollectionUtils.isNotEmpty(esAmazonProductListingList)){
                esAmazonProductListingList = esAmazonProductListingList.stream().filter(o ->
                        (!o.getArticleNumber().equalsIgnoreCase("匹配不到货号") && null != o.getPrice())).collect(Collectors.toList());
                AmazonProductListingDto amazonProductListingDto = new AmazonProductListingDto();
                amazonProductListingDto.setIsAppointShippingMethod(false);
                List<AmazonListingCalcProfitBean> listingCalcProfitBeanList = AmazonCalcPriceUtil.initPriceCalculatorRequest(esAmazonProductListingList, amazonProductListingDto);
                long start = System.currentTimeMillis();
                //需要拆分请求
                List<List<AmazonListingCalcProfitBean>> lists = PagingUtils.pagingList(listingCalcProfitBeanList, 100);
                for (List<AmazonListingCalcProfitBean> listingCalcProfitBeans : lists) {
                    try {
                        amazonProductListingService.amazonProductListingProfit(listingCalcProfitBeans, userName);
                    }catch (Exception e){
                        log.error("计算毛利、毛利率出错" + e.getMessage());
                    }
                }
                long end = System.currentTimeMillis();
                log.info(String.format("店铺%s一共%s个产品需要计算毛利，耗时%s 毫秒", accountNumber,esAmazonProductListingList.size(),end-start));
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("%s计算毛利、毛利率出错:%s" ,accountNumber,e.getMessage());
        }
        return Boolean.FALSE;
    }

    public void setFileds(String[] fileds) {
        this.fileds = fileds;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}