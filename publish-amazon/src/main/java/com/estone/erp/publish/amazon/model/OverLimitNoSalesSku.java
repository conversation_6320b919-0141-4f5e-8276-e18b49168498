package com.estone.erp.publish.amazon.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class OverLimitNoSalesSku implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column over_limit_no_sales_sku.id
     */
    private Integer id;

    /**
     * 货号 database column over_limit_no_sales_sku.article_number
     */
    private String articleNumber;

    /**
     * 统计时间 database column over_limit_no_sales_sku.statistical_date
     */
    private Timestamp statisticalDate;
}