package com.estone.erp.publish.publishAmazon.model;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AmazonProductListingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String tableIndex;

    //自定义查询字段
    private String columns;

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public String getTableIndex() {
        return tableIndex;
    }

    public void setTableIndex(String tableIndex) {
        this.tableIndex = tableIndex;
    }

    public AmazonProductListingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("accountNumber is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("accountNumber is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("accountNumber =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("accountNumber <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("accountNumber >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("accountNumber >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("accountNumber <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("accountNumber <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("accountNumber like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("accountNumber not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("accountNumber in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("accountNumber not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("accountNumber between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("accountNumber not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNull() {
            addCriterion("parentAsin is null");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNotNull() {
            addCriterion("parentAsin is not null");
            return (Criteria) this;
        }

        public Criteria andParentAsinEqualTo(String value) {
            addCriterion("parentAsin =", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotEqualTo(String value) {
            addCriterion("parentAsin <>", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThan(String value) {
            addCriterion("parentAsin >", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThanOrEqualTo(String value) {
            addCriterion("parentAsin >=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThan(String value) {
            addCriterion("parentAsin <", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThanOrEqualTo(String value) {
            addCriterion("parentAsin <=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLike(String value) {
            addCriterion("parentAsin like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotLike(String value) {
            addCriterion("parentAsin not like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinIn(List<String> values) {
            addCriterion("parentAsin in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotIn(List<String> values) {
            addCriterion("parentAsin not in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinBetween(String value1, String value2) {
            addCriterion("parentAsin between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotBetween(String value1, String value2) {
            addCriterion("parentAsin not between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNull() {
            addCriterion("sonAsin is null");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNotNull() {
            addCriterion("sonAsin is not null");
            return (Criteria) this;
        }

        public Criteria andSonAsinEqualTo(String value) {
            addCriterion("sonAsin =", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotEqualTo(String value) {
            addCriterion("sonAsin <>", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThan(String value) {
            addCriterion("sonAsin >", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThanOrEqualTo(String value) {
            addCriterion("sonAsin >=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThan(String value) {
            addCriterion("sonAsin <", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThanOrEqualTo(String value) {
            addCriterion("sonAsin <=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLike(String value) {
            addCriterion("sonAsin like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotLike(String value) {
            addCriterion("sonAsin not like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIn(List<String> values) {
            addCriterion("sonAsin in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotIn(List<String> values) {
            addCriterion("sonAsin not in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinBetween(String value1, String value2) {
            addCriterion("sonAsin between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotBetween(String value1, String value2) {
            addCriterion("sonAsin not between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("sellerSku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("sellerSku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("sellerSku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("sellerSku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("sellerSku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sellerSku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("sellerSku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("sellerSku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("sellerSku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("sellerSku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("sellerSku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("sellerSku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("sellerSku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("sellerSku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("brandName is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("brandName is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("brandName =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("brandName <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("brandName >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("brandName >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("brandName <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("brandName <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("brandName like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("brandName not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("brandName in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("brandName not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("brandName between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("brandName not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andMainSkuEqualTo(String value) {
            addCriterion("mainSku =", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("articleNumber is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("articleNumber is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("articleNumber =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("articleNumber <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("articleNumber >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("articleNumber >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("articleNumber <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("articleNumber <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("articleNumber like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("articleNumber not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("articleNumber in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("articleNumber not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("articleNumber between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("articleNumber not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("skuDataSource is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("skuDataSource is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("skuDataSource =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("skuDataSource <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("skuDataSource >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("skuDataSource >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("skuDataSource <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("skuDataSource <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("skuDataSource in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("skuDataSource not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("skuDataSource between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("skuDataSource not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andItemStatusEqualTo(String value) {
            addCriterion("itemStatus =", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNull() {
            addCriterion("isOnline is null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNotNull() {
            addCriterion("isOnline is not null");
            return (Criteria) this;
        }

        public Criteria andCustomSql(String value) {
            addCriterion(value);
            return (Criteria) this;
        }

        public Criteria andIsOnlineEqualTo(Boolean value) {
            addCriterion("isOnline =", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotEqualTo(Boolean value) {
            addCriterion("isOnline <>", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThan(Boolean value) {
            addCriterion("isOnline >", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isOnline >=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThan(Boolean value) {
            addCriterion("isOnline <", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThanOrEqualTo(Boolean value) {
            addCriterion("isOnline <=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIn(List<Boolean> values) {
            addCriterion("isOnline in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotIn(List<Boolean> values) {
            addCriterion("isOnline not in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineBetween(Boolean value1, Boolean value2) {
            addCriterion("isOnline between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isOnline not between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("itemName is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("itemName is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("itemName =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("itemName <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("itemName >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("itemName >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("itemName <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("itemName <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("itemName like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("itemName not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("itemName in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("itemName not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("itemName between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("itemName not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIsNull() {
            addCriterion("itemDescription is null");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIsNotNull() {
            addCriterion("itemDescription is not null");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionEqualTo(String value) {
            addCriterion("itemDescription =", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotEqualTo(String value) {
            addCriterion("itemDescription <>", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionGreaterThan(String value) {
            addCriterion("itemDescription >", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("itemDescription >=", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLessThan(String value) {
            addCriterion("itemDescription <", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLessThanOrEqualTo(String value) {
            addCriterion("itemDescription <=", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLike(String value) {
            addCriterion("itemDescription like", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotLike(String value) {
            addCriterion("itemDescription not like", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIn(List<String> values) {
            addCriterion("itemDescription in", values, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotIn(List<String> values) {
            addCriterion("itemDescription not in", values, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionBetween(String value1, String value2) {
            addCriterion("itemDescription between", value1, value2, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotBetween(String value1, String value2) {
            addCriterion("itemDescription not between", value1, value2, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNull() {
            addCriterion("infringementWord is null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNotNull() {
            addCriterion("infringementWord is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordEqualTo(String value) {
            addCriterion("infringementWord =", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotEqualTo(String value) {
            addCriterion("infringementWord <>", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordGreaterThan(String value) {
            addCriterion("infringementWord >", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordGreaterThanOrEqualTo(String value) {
            addCriterion("infringementWord >=", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLessThan(String value) {
            addCriterion("infringementWord <", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLessThanOrEqualTo(String value) {
            addCriterion("infringementWord <=", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLike(String value) {
            addCriterion("infringementWord like", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotLike(String value) {
            addCriterion("infringementWord not like", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIn(List<String> values) {
            addCriterion("infringementWord in", values, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotIn(List<String> values) {
            addCriterion("infringementWord not in", values, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordBetween(String value1, String value2) {
            addCriterion("infringementWord between", value1, value2, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotBetween(String value1, String value2) {
            addCriterion("infringementWord not between", value1, value2, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNull() {
            addCriterion("forbidChannel is null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNotNull() {
            addCriterion("forbidChannel is not null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelEqualTo(String value) {
            addCriterion("forbidChannel =", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotEqualTo(String value) {
            addCriterion("forbidChannel <>", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThan(String value) {
            addCriterion("forbidChannel >", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThanOrEqualTo(String value) {
            addCriterion("forbidChannel >=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThan(String value) {
            addCriterion("forbidChannel <", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThanOrEqualTo(String value) {
            addCriterion("forbidChannel <=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLike(String value) {
            addCriterion("forbidChannel like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotLike(String value) {
            addCriterion("forbidChannel not like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIn(List<String> values) {
            addCriterion("forbidChannel in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotIn(List<String> values) {
            addCriterion("forbidChannel not in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelBetween(String value1, String value2) {
            addCriterion("forbidChannel between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotBetween(String value1, String value2) {
            addCriterion("forbidChannel not between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNull() {
            addCriterion("skuStatus is null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNotNull() {
            addCriterion("skuStatus is not null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusEqualTo(String value) {
            addCriterion("skuStatus =", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotEqualTo(String value) {
            addCriterion("skuStatus <>", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThan(String value) {
            addCriterion("skuStatus >", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThanOrEqualTo(String value) {
            addCriterion("skuStatus >=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThan(String value) {
            addCriterion("skuStatus <", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThanOrEqualTo(String value) {
            addCriterion("skuStatus <=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLike(String value) {
            addCriterion("skuStatus like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotLike(String value) {
            addCriterion("skuStatus not like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIn(List<String> values) {
            addCriterion("skuStatus in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotIn(List<String> values) {
            addCriterion("skuStatus not in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusBetween(String value1, String value2) {
            addCriterion("skuStatus between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotBetween(String value1, String value2) {
            addCriterion("skuStatus not between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andTagCodesIsNull() {
            addCriterion("tagCodes is null");
            return (Criteria) this;
        }

        public Criteria andTagCodesIsNotNull() {
            addCriterion("tagCodes is not null");
            return (Criteria) this;
        }

        public Criteria andTagCodesEqualTo(String value) {
            addCriterion("tagCodes =", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotEqualTo(String value) {
            addCriterion("tagCodes <>", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesGreaterThan(String value) {
            addCriterion("tagCodes >", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesGreaterThanOrEqualTo(String value) {
            addCriterion("tagCodes >=", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLessThan(String value) {
            addCriterion("tagCodes <", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLessThanOrEqualTo(String value) {
            addCriterion("tagCodes <=", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLike(String value) {
            addCriterion("tagCodes like", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotLike(String value) {
            addCriterion("tagCodes not like", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesIn(List<String> values) {
            addCriterion("tagCodes in", values, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotIn(List<String> values) {
            addCriterion("tagCodes not in", values, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesBetween(String value1, String value2) {
            addCriterion("tagCodes between", value1, value2, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotBetween(String value1, String value2) {
            addCriterion("tagCodes not between", value1, value2, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagNamesIsNull() {
            addCriterion("tagNames is null");
            return (Criteria) this;
        }

        public Criteria andTagNamesIsNotNull() {
            addCriterion("tagNames is not null");
            return (Criteria) this;
        }

        public Criteria andTagNamesEqualTo(String value) {
            addCriterion("tagNames =", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesNotEqualTo(String value) {
            addCriterion("tagNames <>", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesGreaterThan(String value) {
            addCriterion("tagNames >", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesGreaterThanOrEqualTo(String value) {
            addCriterion("tagNames >=", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesLessThan(String value) {
            addCriterion("tagNames <", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesLessThanOrEqualTo(String value) {
            addCriterion("tagNames <=", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesLike(String value) {
            addCriterion("tagNames like", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesNotLike(String value) {
            addCriterion("tagNames not like", value, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesIn(List<String> values) {
            addCriterion("tagNames in", values, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesNotIn(List<String> values) {
            addCriterion("tagNames not in", values, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesBetween(String value1, String value2) {
            addCriterion("tagNames between", value1, value2, "tagNames");
            return (Criteria) this;
        }

        public Criteria andTagNamesNotBetween(String value1, String value2) {
            addCriterion("tagNames not between", value1, value2, "tagNames");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIsNull() {
            addCriterion("specialGoodsCode is null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIsNotNull() {
            addCriterion("specialGoodsCode is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeEqualTo(String value) {
            addCriterion("specialGoodsCode =", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotEqualTo(String value) {
            addCriterion("specialGoodsCode <>", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeGreaterThan(String value) {
            addCriterion("specialGoodsCode >", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("specialGoodsCode >=", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLessThan(String value) {
            addCriterion("specialGoodsCode <", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLessThanOrEqualTo(String value) {
            addCriterion("specialGoodsCode <=", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLike(String value) {
            addCriterion("specialGoodsCode like", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotLike(String value) {
            addCriterion("specialGoodsCode not like", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIn(List<String> values) {
            addCriterion("specialGoodsCode in", values, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotIn(List<String> values) {
            addCriterion("specialGoodsCode not in", values, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeBetween(String value1, String value2) {
            addCriterion("specialGoodsCode between", value1, value2, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotBetween(String value1, String value2) {
            addCriterion("specialGoodsCode not between", value1, value2, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameIsNull() {
            addCriterion("specialGoodsName is null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameIsNotNull() {
            addCriterion("specialGoodsName is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameEqualTo(String value) {
            addCriterion("specialGoodsName =", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameNotEqualTo(String value) {
            addCriterion("specialGoodsName <>", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameGreaterThan(String value) {
            addCriterion("specialGoodsName >", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("specialGoodsName >=", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameLessThan(String value) {
            addCriterion("specialGoodsName <", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("specialGoodsName <=", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameLike(String value) {
            addCriterion("specialGoodsName like", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameNotLike(String value) {
            addCriterion("specialGoodsName not like", value, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameIn(List<String> values) {
            addCriterion("specialGoodsName in", values, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameNotIn(List<String> values) {
            addCriterion("specialGoodsName not in", values, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameBetween(String value1, String value2) {
            addCriterion("specialGoodsName between", value1, value2, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsNameNotBetween(String value1, String value2) {
            addCriterion("specialGoodsName not between", value1, value2, "specialGoodsName");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIsNull() {
            addCriterion("itemIsMarketplace is null");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIsNotNull() {
            addCriterion("itemIsMarketplace is not null");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceEqualTo(String value) {
            addCriterion("itemIsMarketplace =", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotEqualTo(String value) {
            addCriterion("itemIsMarketplace <>", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceGreaterThan(String value) {
            addCriterion("itemIsMarketplace >", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceGreaterThanOrEqualTo(String value) {
            addCriterion("itemIsMarketplace >=", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceLessThan(String value) {
            addCriterion("itemIsMarketplace <", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceLessThanOrEqualTo(String value) {
            addCriterion("itemIsMarketplace <=", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceLike(String value) {
            addCriterion("itemIsMarketplace like", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotLike(String value) {
            addCriterion("itemIsMarketplace not like", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIn(List<String> values) {
            addCriterion("itemIsMarketplace in", values, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotIn(List<String> values) {
            addCriterion("itemIsMarketplace not in", values, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceBetween(String value1, String value2) {
            addCriterion("itemIsMarketplace between", value1, value2, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotBetween(String value1, String value2) {
            addCriterion("itemIsMarketplace not between", value1, value2, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemConditionIsNull() {
            addCriterion("itemCondition is null");
            return (Criteria) this;
        }

        public Criteria andItemConditionIsNotNull() {
            addCriterion("itemCondition is not null");
            return (Criteria) this;
        }

        public Criteria andItemConditionEqualTo(String value) {
            addCriterion("itemCondition =", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotEqualTo(String value) {
            addCriterion("itemCondition <>", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionGreaterThan(String value) {
            addCriterion("itemCondition >", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionGreaterThanOrEqualTo(String value) {
            addCriterion("itemCondition >=", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLessThan(String value) {
            addCriterion("itemCondition <", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLessThanOrEqualTo(String value) {
            addCriterion("itemCondition <=", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLike(String value) {
            addCriterion("itemCondition like", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotLike(String value) {
            addCriterion("itemCondition not like", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionIn(List<String> values) {
            addCriterion("itemCondition in", values, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotIn(List<String> values) {
            addCriterion("itemCondition not in", values, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionBetween(String value1, String value2) {
            addCriterion("itemCondition between", value1, value2, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotBetween(String value1, String value2) {
            addCriterion("itemCondition not between", value1, value2, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIsNull() {
            addCriterion("zshopCategory is null");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIsNotNull() {
            addCriterion("zshopCategory is not null");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryEqualTo(String value) {
            addCriterion("zshopCategory =", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotEqualTo(String value) {
            addCriterion("zshopCategory <>", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryGreaterThan(String value) {
            addCriterion("zshopCategory >", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("zshopCategory >=", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLessThan(String value) {
            addCriterion("zshopCategory <", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLessThanOrEqualTo(String value) {
            addCriterion("zshopCategory <=", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLike(String value) {
            addCriterion("zshopCategory like", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotLike(String value) {
            addCriterion("zshopCategory not like", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIn(List<String> values) {
            addCriterion("zshopCategory in", values, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotIn(List<String> values) {
            addCriterion("zshopCategory not in", values, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryBetween(String value1, String value2) {
            addCriterion("zshopCategory between", value1, value2, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotBetween(String value1, String value2) {
            addCriterion("zshopCategory not between", value1, value2, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("productId is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("productId is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("productId =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("productId <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("productId >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("productId >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("productId <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("productId <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("productId like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("productId not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("productId in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("productId not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("productId between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("productId not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNull() {
            addCriterion("mainImage is null");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNotNull() {
            addCriterion("mainImage is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageEqualTo(String value) {
            addCriterion("mainImage =", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotEqualTo(String value) {
            addCriterion("mainImage <>", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThan(String value) {
            addCriterion("mainImage >", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThanOrEqualTo(String value) {
            addCriterion("mainImage >=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThan(String value) {
            addCriterion("mainImage <", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThanOrEqualTo(String value) {
            addCriterion("mainImage <=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLike(String value) {
            addCriterion("mainImage like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotLike(String value) {
            addCriterion("mainImage not like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageIn(List<String> values) {
            addCriterion("mainImage in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotIn(List<String> values) {
            addCriterion("mainImage not in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageBetween(String value1, String value2) {
            addCriterion("mainImage between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotBetween(String value1, String value2) {
            addCriterion("mainImage not between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNull() {
            addCriterion("sampleImage is null");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNotNull() {
            addCriterion("sampleImage is not null");
            return (Criteria) this;
        }

        public Criteria andSampleImageEqualTo(String value) {
            addCriterion("sampleImage =", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotEqualTo(String value) {
            addCriterion("sampleImage <>", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThan(String value) {
            addCriterion("sampleImage >", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThanOrEqualTo(String value) {
            addCriterion("sampleImage >=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThan(String value) {
            addCriterion("sampleImage <", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThanOrEqualTo(String value) {
            addCriterion("sampleImage <=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLike(String value) {
            addCriterion("sampleImage like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotLike(String value) {
            addCriterion("sampleImage not like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageIn(List<String> values) {
            addCriterion("sampleImage in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotIn(List<String> values) {
            addCriterion("sampleImage not in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageBetween(String value1, String value2) {
            addCriterion("sampleImage between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotBetween(String value1, String value2) {
            addCriterion("sampleImage not between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNull() {
            addCriterion("extraImages is null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNotNull() {
            addCriterion("extraImages is not null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesEqualTo(String value) {
            addCriterion("extraImages =", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotEqualTo(String value) {
            addCriterion("extraImages <>", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThan(String value) {
            addCriterion("extraImages >", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThanOrEqualTo(String value) {
            addCriterion("extraImages >=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThan(String value) {
            addCriterion("extraImages <", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThanOrEqualTo(String value) {
            addCriterion("extraImages <=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLike(String value) {
            addCriterion("extraImages like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotLike(String value) {
            addCriterion("extraImages not like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIn(List<String> values) {
            addCriterion("extraImages in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotIn(List<String> values) {
            addCriterion("extraImages not in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesBetween(String value1, String value2) {
            addCriterion("extraImages between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotBetween(String value1, String value2) {
            addCriterion("extraImages not between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityIsNull() {
            addCriterion("saleQuantity is null");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityIsNotNull() {
            addCriterion("saleQuantity is not null");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityEqualTo(Integer value) {
            addCriterion("saleQuantity =", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityNotEqualTo(Integer value) {
            addCriterion("saleQuantity <>", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityGreaterThan(Integer value) {
            addCriterion("saleQuantity >", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("saleQuantity >=", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityLessThan(Integer value) {
            addCriterion("saleQuantity <", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("saleQuantity <=", value, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityIn(List<Integer> values) {
            addCriterion("saleQuantity in", values, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityNotIn(List<Integer> values) {
            addCriterion("saleQuantity not in", values, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("saleQuantity between", value1, value2, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("saleQuantity not between", value1, value2, "saleQuantity");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("salePrice is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("salePrice is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(Double value) {
            addCriterion("salePrice =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(Double value) {
            addCriterion("salePrice <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(Double value) {
            addCriterion("salePrice >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(Double value) {
            addCriterion("salePrice >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(Double value) {
            addCriterion("salePrice <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(Double value) {
            addCriterion("salePrice <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<Double> values) {
            addCriterion("salePrice in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<Double> values) {
            addCriterion("salePrice not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(Double value1, Double value2) {
            addCriterion("salePrice between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(Double value1, Double value2) {
            addCriterion("salePrice not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNull() {
            addCriterion("saleStartDate is null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNotNull() {
            addCriterion("saleStartDate is not null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateEqualTo(Date value) {
            addCriterion("saleStartDate =", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotEqualTo(Date value) {
            addCriterion("saleStartDate <>", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThan(Date value) {
            addCriterion("saleStartDate >", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("saleStartDate >=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThan(Date value) {
            addCriterion("saleStartDate <", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThanOrEqualTo(Date value) {
            addCriterion("saleStartDate <=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIn(List<Date> values) {
            addCriterion("saleStartDate in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotIn(List<Date> values) {
            addCriterion("saleStartDate not in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateBetween(Date value1, Date value2) {
            addCriterion("saleStartDate between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotBetween(Date value1, Date value2) {
            addCriterion("saleStartDate not between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNull() {
            addCriterion("saleEndDate is null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNotNull() {
            addCriterion("saleEndDate is not null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateEqualTo(Date value) {
            addCriterion("saleEndDate =", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotEqualTo(Date value) {
            addCriterion("saleEndDate <>", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThan(Date value) {
            addCriterion("saleEndDate >", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("saleEndDate >=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThan(Date value) {
            addCriterion("saleEndDate <", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThanOrEqualTo(Date value) {
            addCriterion("saleEndDate <=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIn(List<Date> values) {
            addCriterion("saleEndDate in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotIn(List<Date> values) {
            addCriterion("saleEndDate not in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateBetween(Date value1, Date value2) {
            addCriterion("saleEndDate between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotBetween(Date value1, Date value2) {
            addCriterion("saleEndDate not between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andLowestPriceIsNull() {
            addCriterion("lowestPrice is null");
            return (Criteria) this;
        }

        public Criteria andLowestPriceIsNotNull() {
            addCriterion("lowestPrice is not null");
            return (Criteria) this;
        }

        public Criteria andLowestPriceEqualTo(Double value) {
            addCriterion("lowestPrice =", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceNotEqualTo(Double value) {
            addCriterion("lowestPrice <>", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceGreaterThan(Double value) {
            addCriterion("lowestPrice >", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("lowestPrice >=", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceLessThan(Double value) {
            addCriterion("lowestPrice <", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceLessThanOrEqualTo(Double value) {
            addCriterion("lowestPrice <=", value, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceIn(List<Double> values) {
            addCriterion("lowestPrice in", values, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceNotIn(List<Double> values) {
            addCriterion("lowestPrice not in", values, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceBetween(Double value1, Double value2) {
            addCriterion("lowestPrice between", value1, value2, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andLowestPriceNotBetween(Double value1, Double value2) {
            addCriterion("lowestPrice not between", value1, value2, "lowestPrice");
            return (Criteria) this;
        }

        public Criteria andIsPopularIsNull() {
            addCriterion("isPopular is null");
            return (Criteria) this;
        }

        public Criteria andIsPopularIsNotNull() {
            addCriterion("isPopular is not null");
            return (Criteria) this;
        }

        public Criteria andIsPopularEqualTo(String value) {
            addCriterion("isPopular =", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotEqualTo(String value) {
            addCriterion("isPopular <>", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularGreaterThan(String value) {
            addCriterion("isPopular >", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularGreaterThanOrEqualTo(String value) {
            addCriterion("isPopular >=", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularLessThan(String value) {
            addCriterion("isPopular <", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularLessThanOrEqualTo(String value) {
            addCriterion("isPopular <=", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularLike(String value) {
            addCriterion("isPopular like", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotLike(String value) {
            addCriterion("isPopular not like", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularIn(List<String> values) {
            addCriterion("isPopular in", values, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotIn(List<String> values) {
            addCriterion("isPopular not in", values, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularBetween(String value1, String value2) {
            addCriterion("isPopular between", value1, value2, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotBetween(String value1, String value2) {
            addCriterion("isPopular not between", value1, value2, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIsNull() {
            addCriterion("isFollowSellDelete is null");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIsNotNull() {
            addCriterion("isFollowSellDelete is not null");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteEqualTo(Boolean value) {
            addCriterion("isFollowSellDelete =", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotEqualTo(Boolean value) {
            addCriterion("isFollowSellDelete <>", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteGreaterThan(Boolean value) {
            addCriterion("isFollowSellDelete >", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isFollowSellDelete >=", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteLessThan(Boolean value) {
            addCriterion("isFollowSellDelete <", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("isFollowSellDelete <=", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIn(List<Boolean> values) {
            addCriterion("isFollowSellDelete in", values, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotIn(List<Boolean> values) {
            addCriterion("isFollowSellDelete not in", values, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("isFollowSellDelete between", value1, value2, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isFollowSellDelete not between", value1, value2, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagIsNull() {
            addCriterion("followSaleFlag is null");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagIsNotNull() {
            addCriterion("followSaleFlag is not null");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagEqualTo(String value) {
            addCriterion("followSaleFlag =", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagNotEqualTo(String value) {
            addCriterion("followSaleFlag <>", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagGreaterThan(String value) {
            addCriterion("followSaleFlag >", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagGreaterThanOrEqualTo(String value) {
            addCriterion("followSaleFlag >=", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagLessThan(String value) {
            addCriterion("followSaleFlag <", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagLessThanOrEqualTo(String value) {
            addCriterion("followSaleFlag <=", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagLike(String value) {
            addCriterion("followSaleFlag like", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagNotLike(String value) {
            addCriterion("followSaleFlag not like", value, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagIn(List<String> values) {
            addCriterion("followSaleFlag in", values, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagNotIn(List<String> values) {
            addCriterion("followSaleFlag not in", values, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagBetween(String value1, String value2) {
            addCriterion("followSaleFlag between", value1, value2, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andFollowSaleFlagNotBetween(String value1, String value2) {
            addCriterion("followSaleFlag not between", value1, value2, "followSaleFlag");
            return (Criteria) this;
        }

        public Criteria andListingIdIsNull() {
            addCriterion("listingId is null");
            return (Criteria) this;
        }

        public Criteria andListingIdIsNotNull() {
            addCriterion("listingId is not null");
            return (Criteria) this;
        }

        public Criteria andListingIdEqualTo(String value) {
            addCriterion("listingId =", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotEqualTo(String value) {
            addCriterion("listingId <>", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdGreaterThan(String value) {
            addCriterion("listingId >", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdGreaterThanOrEqualTo(String value) {
            addCriterion("listingId >=", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLessThan(String value) {
            addCriterion("listingId <", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLessThanOrEqualTo(String value) {
            addCriterion("listingId <=", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLike(String value) {
            addCriterion("listingId like", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotLike(String value) {
            addCriterion("listingId not like", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdIn(List<String> values) {
            addCriterion("listingId in", values, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotIn(List<String> values) {
            addCriterion("listingId not in", values, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdBetween(String value1, String value2) {
            addCriterion("listingId between", value1, value2, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotBetween(String value1, String value2) {
            addCriterion("listingId not between", value1, value2, "listingId");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIsNull() {
            addCriterion("skuLifeCyclePhase is null");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIsNotNull() {
            addCriterion("skuLifeCyclePhase is not null");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseEqualTo(String value) {
            addCriterion("skuLifeCyclePhase =", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotEqualTo(String value) {
            addCriterion("skuLifeCyclePhase <>", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseGreaterThan(String value) {
            addCriterion("skuLifeCyclePhase >", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseGreaterThanOrEqualTo(String value) {
            addCriterion("skuLifeCyclePhase >=", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLessThan(String value) {
            addCriterion("skuLifeCyclePhase <", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLessThanOrEqualTo(String value) {
            addCriterion("skuLifeCyclePhase <=", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLike(String value) {
            addCriterion("skuLifeCyclePhase like", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotLike(String value) {
            addCriterion("skuLifeCyclePhase not like", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIn(List<String> values) {
            addCriterion("skuLifeCyclePhase in", values, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotIn(List<String> values) {
            addCriterion("skuLifeCyclePhase not in", values, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseBetween(String value1, String value2) {
            addCriterion("skuLifeCyclePhase between", value1, value2, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotBetween(String value1, String value2) {
            addCriterion("skuLifeCyclePhase not between", value1, value2, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIsNull() {
            addCriterion("merchantShippingGroup is null");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIsNotNull() {
            addCriterion("merchantShippingGroup is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupEqualTo(String value) {
            addCriterion("merchantShippingGroup =", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotEqualTo(String value) {
            addCriterion("merchantShippingGroup <>", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupGreaterThan(String value) {
            addCriterion("merchantShippingGroup >", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupGreaterThanOrEqualTo(String value) {
            addCriterion("merchantShippingGroup >=", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLessThan(String value) {
            addCriterion("merchantShippingGroup <", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLessThanOrEqualTo(String value) {
            addCriterion("merchantShippingGroup <=", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLike(String value) {
            addCriterion("merchantShippingGroup like", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotLike(String value) {
            addCriterion("merchantShippingGroup not like", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIn(List<String> values) {
            addCriterion("merchantShippingGroup in", values, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotIn(List<String> values) {
            addCriterion("merchantShippingGroup not in", values, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupBetween(String value1, String value2) {
            addCriterion("merchantShippingGroup between", value1, value2, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotBetween(String value1, String value2) {
            addCriterion("merchantShippingGroup not between", value1, value2, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andShippingCostIsNull() {
            addCriterion("shippingCost is null");
            return (Criteria) this;
        }

        public Criteria andShippingCostEqualTo(Double value) {
            addCriterion("shippingCost =", value, "shippingCost");
            return (Criteria) this;
        }
        public Criteria andShippingCostGreaterThan(Double value) {
            addCriterion("shippingCost >", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostGreaterThanOrEqualTo(Double value) {
            addCriterion("shippingCost >=", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostLessThan(Double value) {
            addCriterion("shippingCost <", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostLessThanOrEqualTo(Double value) {
            addCriterion("shippingCost <=", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("totalPrice is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(Double value) {
            addCriterion("totalPrice =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(Double value) {
            addCriterion("totalPrice >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("totalPrice >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(Double value) {
            addCriterion("totalPrice <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(Double value) {
            addCriterion("totalPrice <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("categoryId is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("categoryId is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(String value) {
            addCriterion("categoryId =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(String value) {
            addCriterion("categoryId <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(String value) {
            addCriterion("categoryId >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("categoryId >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(String value) {
            addCriterion("categoryId <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("categoryId <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLike(String value) {
            addCriterion("categoryId like", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotLike(String value) {
            addCriterion("categoryId not like", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<String> values) {
            addCriterion("categoryId in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<String> values) {
            addCriterion("categoryId not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(String value1, String value2) {
            addCriterion("categoryId between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(String value1, String value2) {
            addCriterion("categoryId not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameIsNull() {
            addCriterion("categoryCnName is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameIsNotNull() {
            addCriterion("categoryCnName is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameEqualTo(String value) {
            addCriterion("categoryCnName =", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameNotEqualTo(String value) {
            addCriterion("categoryCnName <>", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameGreaterThan(String value) {
            addCriterion("categoryCnName >", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameGreaterThanOrEqualTo(String value) {
            addCriterion("categoryCnName >=", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameLessThan(String value) {
            addCriterion("categoryCnName <", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameLessThanOrEqualTo(String value) {
            addCriterion("categoryCnName <=", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameLike(String value) {
            addCriterion("categoryCnName like", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameNotLike(String value) {
            addCriterion("categoryCnName not like", value, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameIn(List<String> values) {
            addCriterion("categoryCnName in", values, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameNotIn(List<String> values) {
            addCriterion("categoryCnName not in", values, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameBetween(String value1, String value2) {
            addCriterion("categoryCnName between", value1, value2, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnNameNotBetween(String value1, String value2) {
            addCriterion("categoryCnName not between", value1, value2, "categoryCnName");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdIsNull() {
            addCriterion("relationTemplateId is null");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdIsNotNull() {
            addCriterion("relationTemplateId is not null");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdEqualTo(Integer value) {
            addCriterion("relationTemplateId =", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdNotEqualTo(Integer value) {
            addCriterion("relationTemplateId <>", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdGreaterThan(Integer value) {
            addCriterion("relationTemplateId >", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("relationTemplateId >=", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdLessThan(Integer value) {
            addCriterion("relationTemplateId <", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("relationTemplateId <=", value, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdIn(List<Integer> values) {
            addCriterion("relationTemplateId in", values, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdNotIn(List<Integer> values) {
            addCriterion("relationTemplateId not in", values, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("relationTemplateId between", value1, value2, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andRelationTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("relationTemplateId not between", value1, value2, "relationTemplateId");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateIsNull() {
            addCriterion("autoUpdateMsgDate is null");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateIsNotNull() {
            addCriterion("autoUpdateMsgDate is not null");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateEqualTo(Date value) {
            addCriterion("autoUpdateMsgDate =", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateNotEqualTo(Date value) {
            addCriterion("autoUpdateMsgDate <>", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateGreaterThan(Date value) {
            addCriterion("autoUpdateMsgDate >", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateGreaterThanOrEqualTo(Date value) {
            addCriterion("autoUpdateMsgDate >=", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateLessThan(Date value) {
            addCriterion("autoUpdateMsgDate <", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateLessThanOrEqualTo(Date value) {
            addCriterion("autoUpdateMsgDate <=", value, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateIn(List<Date> values) {
            addCriterion("autoUpdateMsgDate in", values, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateNotIn(List<Date> values) {
            addCriterion("autoUpdateMsgDate not in", values, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateBetween(Date value1, Date value2) {
            addCriterion("autoUpdateMsgDate between", value1, value2, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateMsgDateNotBetween(Date value1, Date value2) {
            addCriterion("autoUpdateMsgDate not between", value1, value2, "autoUpdateMsgDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIsNull() {
            addCriterion("lastAdjustPriceDate is null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIsNotNull() {
            addCriterion("lastAdjustPriceDate is not null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateEqualTo(Date value) {
            addCriterion("lastAdjustPriceDate =", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotEqualTo(Date value) {
            addCriterion("lastAdjustPriceDate <>", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateGreaterThan(Date value) {
            addCriterion("lastAdjustPriceDate >", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("lastAdjustPriceDate >=", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateLessThan(Date value) {
            addCriterion("lastAdjustPriceDate <", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateLessThanOrEqualTo(Date value) {
            addCriterion("lastAdjustPriceDate <=", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIn(List<Date> values) {
            addCriterion("lastAdjustPriceDate in", values, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotIn(List<Date> values) {
            addCriterion("lastAdjustPriceDate not in", values, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateBetween(Date value1, Date value2) {
            addCriterion("lastAdjustPriceDate between", value1, value2, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotBetween(Date value1, Date value2) {
            addCriterion("lastAdjustPriceDate not between", value1, value2, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateIsNull() {
            addCriterion("reportOpenDate is null");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateIsNotNull() {
            addCriterion("reportOpenDate is not null");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateEqualTo(String value) {
            addCriterion("reportOpenDate =", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateNotEqualTo(String value) {
            addCriterion("reportOpenDate <>", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateGreaterThan(String value) {
            addCriterion("reportOpenDate >", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateGreaterThanOrEqualTo(String value) {
            addCriterion("reportOpenDate >=", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateLessThan(String value) {
            addCriterion("reportOpenDate <", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateLessThanOrEqualTo(String value) {
            addCriterion("reportOpenDate <=", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateLike(String value) {
            addCriterion("reportOpenDate like", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateNotLike(String value) {
            addCriterion("reportOpenDate not like", value, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateIn(List<String> values) {
            addCriterion("reportOpenDate in", values, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateNotIn(List<String> values) {
            addCriterion("reportOpenDate not in", values, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateBetween(String value1, String value2) {
            addCriterion("reportOpenDate between", value1, value2, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andReportOpenDateNotBetween(String value1, String value2) {
            addCriterion("reportOpenDate not between", value1, value2, "reportOpenDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNull() {
            addCriterion("openDate is null");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNotNull() {
            addCriterion("openDate is not null");
            return (Criteria) this;
        }

        public Criteria andOpenDateEqualTo(Date value) {
            addCriterion("openDate =", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotEqualTo(Date value) {
            addCriterion("openDate <>", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThan(Date value) {
            addCriterion("openDate >", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThanOrEqualTo(Date value) {
            addCriterion("openDate >=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThan(Date value) {
            addCriterion("openDate <", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThanOrEqualTo(Date value) {
            addCriterion("openDate <=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateIn(List<Date> values) {
            addCriterion("openDate in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotIn(List<Date> values) {
            addCriterion("openDate not in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateBetween(Date value1, Date value2) {
            addCriterion("openDate between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotBetween(Date value1, Date value2) {
            addCriterion("openDate not between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateIsNull() {
            addCriterion("firstOpenDate is null");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateIsNotNull() {
            addCriterion("firstOpenDate is not null");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateEqualTo(Date value) {
            addCriterion("firstOpenDate =", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateNotEqualTo(Date value) {
            addCriterion("firstOpenDate <>", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateGreaterThan(Date value) {
            addCriterion("firstOpenDate >", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateGreaterThanOrEqualTo(Date value) {
            addCriterion("firstOpenDate >=", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateLessThan(Date value) {
            addCriterion("firstOpenDate <", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateLessThanOrEqualTo(Date value) {
            addCriterion("firstOpenDate <=", value, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateIn(List<Date> values) {
            addCriterion("firstOpenDate in", values, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateNotIn(List<Date> values) {
            addCriterion("firstOpenDate not in", values, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateBetween(Date value1, Date value2) {
            addCriterion("firstOpenDate between", value1, value2, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andFirstOpenDateNotBetween(Date value1, Date value2) {
            addCriterion("firstOpenDate not between", value1, value2, "firstOpenDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNull() {
            addCriterion("offlineDate is null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNotNull() {
            addCriterion("offlineDate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateEqualTo(Date value) {
            addCriterion("offlineDate =", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotEqualTo(Date value) {
            addCriterion("offlineDate <>", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThan(Date value) {
            addCriterion("offlineDate >", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThanOrEqualTo(Date value) {
            addCriterion("offlineDate >=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThan(Date value) {
            addCriterion("offlineDate <", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThanOrEqualTo(Date value) {
            addCriterion("offlineDate <=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIn(List<Date> values) {
            addCriterion("offlineDate in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotIn(List<Date> values) {
            addCriterion("offlineDate not in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateBetween(Date value1, Date value2) {
            addCriterion("offlineDate between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotBetween(Date value1, Date value2) {
            addCriterion("offlineDate not between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateIsNull() {
            addCriterion("firstOfflineDate is null");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateIsNotNull() {
            addCriterion("firstOfflineDate is not null");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateEqualTo(Date value) {
            addCriterion("firstOfflineDate =", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateNotEqualTo(Date value) {
            addCriterion("firstOfflineDate <>", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateGreaterThan(Date value) {
            addCriterion("firstOfflineDate >", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateGreaterThanOrEqualTo(Date value) {
            addCriterion("firstOfflineDate >=", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateLessThan(Date value) {
            addCriterion("firstOfflineDate <", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateLessThanOrEqualTo(Date value) {
            addCriterion("firstOfflineDate <=", value, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateIn(List<Date> values) {
            addCriterion("firstOfflineDate in", values, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateNotIn(List<Date> values) {
            addCriterion("firstOfflineDate not in", values, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateBetween(Date value1, Date value2) {
            addCriterion("firstOfflineDate between", value1, value2, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andFirstOfflineDateNotBetween(Date value1, Date value2) {
            addCriterion("firstOfflineDate not between", value1, value2, "firstOfflineDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNull() {
            addCriterion("syncDate is null");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNotNull() {
            addCriterion("syncDate is not null");
            return (Criteria) this;
        }

        public Criteria andSyncDateEqualTo(Date value) {
            addCriterion("syncDate =", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotEqualTo(Date value) {
            addCriterion("syncDate <>", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThan(Date value) {
            addCriterion("syncDate >", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Date value) {
            addCriterion("syncDate >=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThan(Date value) {
            addCriterion("syncDate <", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Date value) {
            addCriterion("syncDate <=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIn(List<Date> values) {
            addCriterion("syncDate in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotIn(List<Date> values) {
            addCriterion("syncDate not in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateBetween(Date value1, Date value2) {
            addCriterion("syncDate between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotBetween(Date value1, Date value2) {
            addCriterion("syncDate not between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("createdBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("createdBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("createdBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("createdBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("createdBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("createdBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("createdBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("createdBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("createdBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("createdBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("createdBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("createdBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("createdBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("createdBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("createDate is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("createDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterion("createDate =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterion("createDate <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterion("createDate >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("createDate >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterion("createDate <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterion("createDate <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Date> values) {
            addCriterion("createDate in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Date> values) {
            addCriterion("createDate not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Date value1, Date value2) {
            addCriterion("createDate between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Date value1, Date value2) {
            addCriterion("createDate not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("updateDate is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("updateDate is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Date value) {
            addCriterion("updateDate =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Date value) {
            addCriterion("updateDate <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Date value) {
            addCriterion("updateDate >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("updateDate >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Date value) {
            addCriterion("updateDate <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("updateDate <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Date> values) {
            addCriterion("updateDate in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Date> values) {
            addCriterion("updateDate not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Date value1, Date value2) {
            addCriterion("updateDate between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("updateDate not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updatedBy is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updatedBy =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updatedBy <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updatedBy >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updatedBy >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updatedBy <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updatedBy <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updatedBy like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updatedBy not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updatedBy in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updatedBy not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updatedBy between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updatedBy not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNull() {
            addCriterion("attribute4 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNotNull() {
            addCriterion("attribute4 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute4EqualTo(String value) {
            addCriterion("attribute4 =", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotEqualTo(String value) {
            addCriterion("attribute4 <>", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThan(String value) {
            addCriterion("attribute4 >", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThanOrEqualTo(String value) {
            addCriterion("attribute4 >=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThan(String value) {
            addCriterion("attribute4 <", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThanOrEqualTo(String value) {
            addCriterion("attribute4 <=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Like(String value) {
            addCriterion("attribute4 like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotLike(String value) {
            addCriterion("attribute4 not like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4In(List<String> values) {
            addCriterion("attribute4 in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotIn(List<String> values) {
            addCriterion("attribute4 not in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Between(String value1, String value2) {
            addCriterion("attribute4 between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotBetween(String value1, String value2) {
            addCriterion("attribute4 not between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNull() {
            addCriterion("attribute5 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNotNull() {
            addCriterion("attribute5 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute5EqualTo(String value) {
            addCriterion("attribute5 =", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotEqualTo(String value) {
            addCriterion("attribute5 <>", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThan(String value) {
            addCriterion("attribute5 >", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThanOrEqualTo(String value) {
            addCriterion("attribute5 >=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThan(String value) {
            addCriterion("attribute5 <", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThanOrEqualTo(String value) {
            addCriterion("attribute5 <=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Like(String value) {
            addCriterion("attribute5 like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotLike(String value) {
            addCriterion("attribute5 not like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5In(List<String> values) {
            addCriterion("attribute5 in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotIn(List<String> values) {
            addCriterion("attribute5 not in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Between(String value1, String value2) {
            addCriterion("attribute5 between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotBetween(String value1, String value2) {
            addCriterion("attribute5 not between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute6IsNull() {
            addCriterion("attribute6 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute6IsNotNull() {
            addCriterion("attribute6 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute6EqualTo(String value) {
            addCriterion("attribute6 =", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotEqualTo(String value) {
            addCriterion("attribute6 <>", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6GreaterThan(String value) {
            addCriterion("attribute6 >", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6GreaterThanOrEqualTo(String value) {
            addCriterion("attribute6 >=", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6LessThan(String value) {
            addCriterion("attribute6 <", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6LessThanOrEqualTo(String value) {
            addCriterion("attribute6 <=", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6Like(String value) {
            addCriterion("attribute6 like", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotLike(String value) {
            addCriterion("attribute6 not like", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6In(List<String> values) {
            addCriterion("attribute6 in", values, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotIn(List<String> values) {
            addCriterion("attribute6 not in", values, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6Between(String value1, String value2) {
            addCriterion("attribute6 between", value1, value2, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotBetween(String value1, String value2) {
            addCriterion("attribute6 not between", value1, value2, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute7IsNull() {
            addCriterion("attribute7 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute7IsNotNull() {
            addCriterion("attribute7 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute7EqualTo(String value) {
            addCriterion("attribute7 =", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7NotEqualTo(String value) {
            addCriterion("attribute7 <>", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7GreaterThan(String value) {
            addCriterion("attribute7 >", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7GreaterThanOrEqualTo(String value) {
            addCriterion("attribute7 >=", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7LessThan(String value) {
            addCriterion("attribute7 <", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7LessThanOrEqualTo(String value) {
            addCriterion("attribute7 <=", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7Like(String value) {
            addCriterion("attribute7 like", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7NotLike(String value) {
            addCriterion("attribute7 not like", value, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7In(List<String> values) {
            addCriterion("attribute7 in", values, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7NotIn(List<String> values) {
            addCriterion("attribute7 not in", values, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7Between(String value1, String value2) {
            addCriterion("attribute7 between", value1, value2, "attribute7");
            return (Criteria) this;
        }

        public Criteria andAttribute7NotBetween(String value1, String value2) {
            addCriterion("attribute7 not between", value1, value2, "attribute7");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIsNull() {
            addCriterion("publishRole is null");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityIsNull() {
            addCriterion("issuesSeverity is null");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityIsNotNull() {
            addCriterion("issuesSeverity is not null");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityEqualTo(String value) {
            addCriterion("issuesSeverity =", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityNotEqualTo(String value) {
            addCriterion("issuesSeverity <>", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityGreaterThan(String value) {
            addCriterion("issuesSeverity >", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityGreaterThanOrEqualTo(String value) {
            addCriterion("issuesSeverity >=", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityLessThan(String value) {
            addCriterion("issuesSeverity <", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityLessThanOrEqualTo(String value) {
            addCriterion("issuesSeverity <=", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityLike(String value) {
            addCriterion("issuesSeverity like", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityNotLike(String value) {
            addCriterion("issuesSeverity not like", value, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityIn(List<String> values) {
            addCriterion("issuesSeverity in", values, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityNotIn(List<String> values) {
            addCriterion("issuesSeverity not in", values, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityBetween(String value1, String value2) {
            addCriterion("issuesSeverity between", value1, value2, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andIssuesSeverityNotBetween(String value1, String value2) {
            addCriterion("issuesSeverity not between", value1, value2, "issuesSeverity");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusIsNull() {
            addCriterion("itemSummariesStastus is null");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusIsNotNull() {
            addCriterion("itemSummariesStastus is not null");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusEqualTo(String value) {
            addCriterion("itemSummariesStastus =", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusNotEqualTo(String value) {
            addCriterion("itemSummariesStastus <>", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusGreaterThan(String value) {
            addCriterion("itemSummariesStastus >", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusGreaterThanOrEqualTo(String value) {
            addCriterion("itemSummariesStastus >=", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusLessThan(String value) {
            addCriterion("itemSummariesStastus <", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusLessThanOrEqualTo(String value) {
            addCriterion("itemSummariesStastus <=", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusLike(String value) {
            addCriterion("itemSummariesStastus like", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusNotLike(String value) {
            addCriterion("itemSummariesStastus not like", value, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusIn(List<String> values) {
            addCriterion("itemSummariesStastus in", values, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusNotIn(List<String> values) {
            addCriterion("itemSummariesStastus not in", values, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusBetween(String value1, String value2) {
            addCriterion("itemSummariesStastus between", value1, value2, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andItemSummariesStastusNotBetween(String value1, String value2) {
            addCriterion("itemSummariesStastus not between", value1, value2, "itemSummariesStastus");
            return (Criteria) this;
        }

        public Criteria andConditionTypeIsNull() {
            addCriterion("conditionType is null");
            return (Criteria) this;
        }

        public Criteria andConditionTypeIsNotNull() {
            addCriterion("conditionType is not null");
            return (Criteria) this;
        }

        public Criteria andConditionTypeEqualTo(String value) {
            addCriterion("conditionType =", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeNotEqualTo(String value) {
            addCriterion("conditionType <>", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeGreaterThan(String value) {
            addCriterion("conditionType >", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("conditionType >=", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeLessThan(String value) {
            addCriterion("conditionType <", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeLessThanOrEqualTo(String value) {
            addCriterion("conditionType <=", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeLike(String value) {
            addCriterion("conditionType like", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeNotLike(String value) {
            addCriterion("conditionType not like", value, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeIn(List<String> values) {
            addCriterion("conditionType in", values, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeNotIn(List<String> values) {
            addCriterion("conditionType not in", values, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeBetween(String value1, String value2) {
            addCriterion("conditionType between", value1, value2, "conditionType");
            return (Criteria) this;
        }

        public Criteria andConditionTypeNotBetween(String value1, String value2) {
            addCriterion("conditionType not between", value1, value2, "conditionType");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateIsNull() {
            addCriterion("IteamLastUpdatedDate is null");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateIsNotNull() {
            addCriterion("IteamLastUpdatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateEqualTo(Date value) {
            addCriterion("IteamLastUpdatedDate =", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateNotEqualTo(Date value) {
            addCriterion("IteamLastUpdatedDate <>", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateGreaterThan(Date value) {
            addCriterion("IteamLastUpdatedDate >", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("IteamLastUpdatedDate >=", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateLessThan(Date value) {
            addCriterion("IteamLastUpdatedDate <", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateLessThanOrEqualTo(Date value) {
            addCriterion("IteamLastUpdatedDate <=", value, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateIn(List<Date> values) {
            addCriterion("IteamLastUpdatedDate in", values, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateNotIn(List<Date> values) {
            addCriterion("IteamLastUpdatedDate not in", values, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateBetween(Date value1, Date value2) {
            addCriterion("IteamLastUpdatedDate between", value1, value2, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andIteamLastUpdatedDateNotBetween(Date value1, Date value2) {
            addCriterion("IteamLastUpdatedDate not between", value1, value2, "iteamLastUpdatedDate");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNull() {
            addCriterion("itemType is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNotNull() {
            addCriterion("itemType is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualTo(Integer value) {
            addCriterion("itemType =", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualTo(Integer value) {
            addCriterion("itemType <>", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualToOrNull(Integer value) {
            addCriterion(String.format("(itemType <> %s or itemType is null)", value));
            return (Criteria) this;
        }


        public Criteria andItemTypeGreaterThan(Integer value) {
            addCriterion("itemType >", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("itemType >=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThan(Integer value) {
            addCriterion("itemType <", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualTo(Integer value) {
            addCriterion("itemType <=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIn(List<Integer> values) {
            addCriterion("itemType in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotIn(List<Integer> values) {
            addCriterion("itemType not in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeBetween(Integer value1, Integer value2) {
            addCriterion("itemType between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("itemType not between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andChildAsinsIsNull() {
            addCriterion("childAsins is null");
            return (Criteria) this;
        }

        public Criteria andChildAsinsIsNotNull() {
            addCriterion("childAsins is not null");
            return (Criteria) this;
        }

        public Criteria andChildAsinsEqualTo(String value) {
            addCriterion("childAsins =", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsNotEqualTo(String value) {
            addCriterion("childAsins <>", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsGreaterThan(String value) {
            addCriterion("childAsins >", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsGreaterThanOrEqualTo(String value) {
            addCriterion("childAsins >=", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsLessThan(String value) {
            addCriterion("childAsins <", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsLessThanOrEqualTo(String value) {
            addCriterion("childAsins <=", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsLike(String value) {
            addCriterion("childAsins like", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsNotLike(String value) {
            addCriterion("childAsins not like", value, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsIn(List<String> values) {
            addCriterion("childAsins in", values, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsNotIn(List<String> values) {
            addCriterion("childAsins not in", values, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsBetween(String value1, String value2) {
            addCriterion("childAsins between", value1, value2, "childAsins");
            return (Criteria) this;
        }

        public Criteria andChildAsinsNotBetween(String value1, String value2) {
            addCriterion("childAsins not between", value1, value2, "childAsins");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityIsNull() {
            addCriterion("packageQuantity is null");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityIsNotNull() {
            addCriterion("packageQuantity is not null");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityEqualTo(Integer value) {
            addCriterion("packageQuantity =", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityNotEqualTo(Integer value) {
            addCriterion("packageQuantity <>", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityGreaterThan(Integer value) {
            addCriterion("packageQuantity >", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("packageQuantity >=", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityLessThan(Integer value) {
            addCriterion("packageQuantity <", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("packageQuantity <=", value, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityIn(List<Integer> values) {
            addCriterion("packageQuantity in", values, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityNotIn(List<Integer> values) {
            addCriterion("packageQuantity not in", values, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityBetween(Integer value1, Integer value2) {
            addCriterion("packageQuantity between", value1, value2, "packageQuantity");
            return (Criteria) this;
        }

        public Criteria andPackageQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("packageQuantity not between", value1, value2, "packageQuantity");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}