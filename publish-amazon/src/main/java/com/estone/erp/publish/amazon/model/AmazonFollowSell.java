package com.estone.erp.publish.amazon.model;

import com.estone.erp.publish.amazon.enums.FollowSellDataStatusEnum;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_follow_sell
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:05:06 CST 2019
 */
public class AmazonFollowSell {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   卖家帐号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.seller_id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String sellerId;

    /**
     * Database Column Remarks:
     *   国家
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.country
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String country;

    /**
     * Database Column Remarks:
     *   ParentSKU
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.parent_SKU
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String parentSku;

    /**
     * sellerSku
     */
    private String sellerSku;

    /**
     * Database Column Remarks:
     *   标准产品id类型
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.standard_prodcut_id_type
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String standardProdcutIdType;

    /**
     * Database Column Remarks:
     *   标准产品id值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.standard_prodcut_id_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String standardProdcutIdValue;

    /**
     * Database Column Remarks:
     *   价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.standard_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Double standardPrice;

    /**
     * Database Column Remarks:
     *   币种
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.currency
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String currency;

    /**
     * Database Column Remarks:
     *   上架日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.on_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date onLineDate;

    /**
     * Database Column Remarks:
     *   下架日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.off_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date offLineDate;

    /**
     * Database Column Remarks:
     *   数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.quantity
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.creation_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date creationDate;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.created_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String createdBy;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date lastUpdateDate;

    /**
     * Database Column Remarks:
     *   修改人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String lastUpdatedBy;

    /**
     * Database Column Remarks:
     *   步骤刊登
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.step_publish_status
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Boolean stepPublishStatus;

    /**
     * 数据状态（0：待刊登，1：刊登中，2：刊登成功，3：刊登失败，4：下架中，5：下架成功，6：下架失败，7：删除成功，8：删除失败）
     */
    private Integer dataStatus;

    /**
     * Database Column Remarks:
     *   sku后缀
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.sku_suffix
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private String skuSuffix;

    /**
     * Database Column Remarks:
     *   促销价
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.latest_page_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Double latestPagePrice;

    /**
     * Database Column Remarks:
     *   下架是否删除产品
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.is_off_line_delete
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Boolean isOffLineDelete;

    /**
     * Database Column Remarks:
     *   最低价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.min_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Double minPrice;

    /**
     * Database Column Remarks:
     *   最高价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.max_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Double maxPrice;

    /**
     * Database Column Remarks:
     *   销售价调整值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.adjust_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Double adjustValue;

    /**
     * Database Column Remarks:
     *   页面刷新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.refresh_page_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date refreshPageDate;

    /**
     * Database Column Remarks:
     *   是否已下架
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.is_off_line
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Boolean isOffLine;

    /**
     * Database Column Remarks:
     *   最新刊登时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.latest_publish_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date latestPublishDate;

    /**
     * Database Column Remarks:
     *   是否跟卖过
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.is_followed
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Boolean isFollowed;

    /**
     * Database Column Remarks:
     *   第一次跟卖时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_follow_sell.first_follow_sell_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    private Date firstFollowSellDate;

    private String operateType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.id
     *
     * @return the value of amazon_follow_sell.id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.id
     *
     * @param id the value for amazon_follow_sell.id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.seller_id
     *
     * @return the value of amazon_follow_sell.seller_id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.seller_id
     *
     * @param sellerId the value for amazon_follow_sell.seller_id
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.country
     *
     * @return the value of amazon_follow_sell.country
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getCountry() {
        return country;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.country
     *
     * @param country the value for amazon_follow_sell.country
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.parent_SKU
     *
     * @return the value of amazon_follow_sell.parent_SKU
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getParentSku() {
        return parentSku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.parent_SKU
     *
     * @param parentSku the value for amazon_follow_sell.parent_SKU
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setParentSku(String parentSku) {
        this.parentSku = parentSku == null ? null : parentSku.trim();
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.standard_prodcut_id_type
     *
     * @return the value of amazon_follow_sell.standard_prodcut_id_type
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getStandardProdcutIdType() {
        return standardProdcutIdType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.standard_prodcut_id_type
     *
     * @param standardProdcutIdType the value for amazon_follow_sell.standard_prodcut_id_type
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setStandardProdcutIdType(String standardProdcutIdType) {
        this.standardProdcutIdType = standardProdcutIdType == null ? null : standardProdcutIdType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.standard_prodcut_id_value
     *
     * @return the value of amazon_follow_sell.standard_prodcut_id_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getStandardProdcutIdValue() {
        return standardProdcutIdValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.standard_prodcut_id_value
     *
     * @param standardProdcutIdValue the value for amazon_follow_sell.standard_prodcut_id_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setStandardProdcutIdValue(String standardProdcutIdValue) {
        this.standardProdcutIdValue = standardProdcutIdValue == null ? null : standardProdcutIdValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.standard_price
     *
     * @return the value of amazon_follow_sell.standard_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Double getStandardPrice() {
        return standardPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.standard_price
     *
     * @param standardPrice the value for amazon_follow_sell.standard_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setStandardPrice(Double standardPrice) {
        this.standardPrice = standardPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.currency
     *
     * @return the value of amazon_follow_sell.currency
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.currency
     *
     * @param currency the value for amazon_follow_sell.currency
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.on_line_date
     *
     * @return the value of amazon_follow_sell.on_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getOnLineDate() {
        return onLineDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.on_line_date
     *
     * @param onLineDate the value for amazon_follow_sell.on_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setOnLineDate(Date onLineDate) {
        this.onLineDate = onLineDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.off_line_date
     *
     * @return the value of amazon_follow_sell.off_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getOffLineDate() {
        return offLineDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.off_line_date
     *
     * @param offLineDate the value for amazon_follow_sell.off_line_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setOffLineDate(Date offLineDate) {
        this.offLineDate = offLineDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.quantity
     *
     * @return the value of amazon_follow_sell.quantity
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.quantity
     *
     * @param quantity the value for amazon_follow_sell.quantity
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.creation_date
     *
     * @return the value of amazon_follow_sell.creation_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.creation_date
     *
     * @param creationDate the value for amazon_follow_sell.creation_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.created_by
     *
     * @return the value of amazon_follow_sell.created_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.created_by
     *
     * @param createdBy the value for amazon_follow_sell.created_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.last_update_date
     *
     * @return the value of amazon_follow_sell.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.last_update_date
     *
     * @param lastUpdateDate the value for amazon_follow_sell.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.last_updated_by
     *
     * @return the value of amazon_follow_sell.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.last_updated_by
     *
     * @param lastUpdatedBy the value for amazon_follow_sell.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.step_publish_status
     *
     * @return the value of amazon_follow_sell.step_publish_status
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Boolean getStepPublishStatus() {
        return stepPublishStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.step_publish_status
     *
     * @param stepPublishStatus the value for amazon_follow_sell.step_publish_status
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setStepPublishStatus(Boolean stepPublishStatus) {
        this.stepPublishStatus = stepPublishStatus;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.sku_suffix
     *
     * @return the value of amazon_follow_sell.sku_suffix
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public String getSkuSuffix() {
        return skuSuffix;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.sku_suffix
     *
     * @param skuSuffix the value for amazon_follow_sell.sku_suffix
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setSkuSuffix(String skuSuffix) {
        this.skuSuffix = skuSuffix == null ? null : skuSuffix.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.latest_page_price
     *
     * @return the value of amazon_follow_sell.latest_page_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Double getLatestPagePrice() {
        return latestPagePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.latest_page_price
     *
     * @param latestPagePrice the value for amazon_follow_sell.latest_page_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setLatestPagePrice(Double latestPagePrice) {
        this.latestPagePrice = latestPagePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.is_off_line_delete
     *
     * @return the value of amazon_follow_sell.is_off_line_delete
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Boolean getIsOffLineDelete() {
        return isOffLineDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.is_off_line_delete
     *
     * @param isOffLineDelete the value for amazon_follow_sell.is_off_line_delete
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setIsOffLineDelete(Boolean isOffLineDelete) {
        this.isOffLineDelete = isOffLineDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.min_price
     *
     * @return the value of amazon_follow_sell.min_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Double getMinPrice() {
        return minPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.min_price
     *
     * @param minPrice the value for amazon_follow_sell.min_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setMinPrice(Double minPrice) {
        this.minPrice = minPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.max_price
     *
     * @return the value of amazon_follow_sell.max_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Double getMaxPrice() {
        return maxPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.max_price
     *
     * @param maxPrice the value for amazon_follow_sell.max_price
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setMaxPrice(Double maxPrice) {
        this.maxPrice = maxPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.adjust_value
     *
     * @return the value of amazon_follow_sell.adjust_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Double getAdjustValue() {
        return adjustValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.adjust_value
     *
     * @param adjustValue the value for amazon_follow_sell.adjust_value
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setAdjustValue(Double adjustValue) {
        this.adjustValue = adjustValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.refresh_page_date
     *
     * @return the value of amazon_follow_sell.refresh_page_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getRefreshPageDate() {
        return refreshPageDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.refresh_page_date
     *
     * @param refreshPageDate the value for amazon_follow_sell.refresh_page_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setRefreshPageDate(Date refreshPageDate) {
        this.refreshPageDate = refreshPageDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.is_off_line
     *
     * @return the value of amazon_follow_sell.is_off_line
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Boolean getIsOffLine() {
        return isOffLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.is_off_line
     *
     * @param isOffLine the value for amazon_follow_sell.is_off_line
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setIsOffLine(Boolean isOffLine) {
        this.isOffLine = isOffLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.latest_publish_date
     *
     * @return the value of amazon_follow_sell.latest_publish_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getLatestPublishDate() {
        return latestPublishDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.latest_publish_date
     *
     * @param latestPublishDate the value for amazon_follow_sell.latest_publish_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setLatestPublishDate(Date latestPublishDate) {
        this.latestPublishDate = latestPublishDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.is_followed
     *
     * @return the value of amazon_follow_sell.is_followed
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Boolean getIsFollowed() {
        return isFollowed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.is_followed
     *
     * @param isFollowed the value for amazon_follow_sell.is_followed
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setIsFollowed(Boolean isFollowed) {
        this.isFollowed = isFollowed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_follow_sell.first_follow_sell_date
     *
     * @return the value of amazon_follow_sell.first_follow_sell_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public Date getFirstFollowSellDate() {
        return firstFollowSellDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_follow_sell.first_follow_sell_date
     *
     * @param firstFollowSellDate the value for amazon_follow_sell.first_follow_sell_date
     *
     * @mbg.generated Thu Jul 18 16:05:06 CST 2019
     */
    public void setFirstFollowSellDate(Date firstFollowSellDate) {
        this.firstFollowSellDate = firstFollowSellDate;
    }

    public String getDataStatusText(){
        return FollowSellDataStatusEnum.getDescByCode(getDataStatus());
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }
}