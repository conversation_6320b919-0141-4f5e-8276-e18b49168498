package com.estone.erp.publish.amazon.util.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 颜色枚举
 * 
 * <AUTHOR>
 *
 */
public enum Color {
    BK("BK", "Black", "黑色"), PK("PK", "Pink", "粉色"), PL("PL", "Purple", "紫色"), W("W", "White", "白色"), MW("MW", "Ivory",
            "奶白"), O("O", "Orange", "橘色/橙色"), GN("GN", "Green", "绿色"), FG("FG", "Fluorescent Green", "荧光绿"),

    GG("GG", "Grass Green", "草绿"), AG("AG", "Army Green", "军绿"), HP("HP", "Rose Pink", "玫红色"), Y("Y", "Yellow",
            "黄色"), FY("FY", "Fluorescent Yellow", "荧光黄"), R("R", "<PERSON>", "红色"), WR("WR", "Burgundy", "酒红"),

    WMR("WMR", "Watermelon Red", "西瓜红"), DR("DR", "Crimson", "深红"), PR("PR", "Peachpuff", "桃红"), T("T", "Transparent",
            "透明"), BG("BG", "Beige", "米白/米黄"), KH("KH", "Khaki", "卡其"), CP("CP", "Champagne", "香槟色"),

    BL("BL", "Blue", "蓝色"), DB("DB", "Navy", "藏蓝/深蓝"), LB("LB", "Light Blue", "浅蓝"), SB("SB", "Sky Blue",
            "天蓝"), AB("AB", "Acid blue", "湖蓝"), RB("RB", "Royal Blue", "宝蓝色"), GY("GY", "Grey", "灰色"),

    LG("LG", "Light Grey", "浅灰"), DG("DG", "Dark Grey", "深灰/数码"), BZ("BZ", "Bronze", "古铜"), BN("BN", "Brown",
            "棕色"), CF("CF", "Coffee", "咖啡"), C("C", "Multi-Color", "彩色"), S("S", "Silver", "银色"),

    GD("GD", "Gold", "金色"), ND("ND", "Nude Color", "裸色"), FC("FC", "Fleshcolor", "肉色"), CG("CG", "Camouflage",
            "迷彩"), N("N", "Nature", "自然");

    public static List<String> codes;

    private String code;

    private String name;

    private String desc;

    private Color(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<String> getCodes() {
        if (codes == null) {
            codes = Color.getColors().stream().map(color -> {
                return color.getCode();
            }).collect(Collectors.toList());
        }

        return codes;
    }

    public static Color getColor(String code) {
        for (Color color : Color.values()) {
            if (color.getCode().equals(code)) {
                return color;
            }
        }

        return null;
    }

    public static List<Color> getColors() {
        List<Color> colors = Arrays.asList(Color.values());
        Collections.sort(colors, (color1, color2) -> {
            return color2.code.length() - color1.code.length();
        });

        return colors;
    }
}
