package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonProductListingOfflineMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonProductListingOfflineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-03 10:38:37
 */
@Service("amazonProductListingOfflineService")
@Slf4j
public class AmazonProductListingOfflineServiceImpl implements AmazonProductListingOfflineService {
    @Resource
    private AmazonProductListingOfflineMapper amazonProductListingOfflineMapper;

    @Override
    public int countByExample(AmazonProductListingOfflineExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProductListingOfflineMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonProductListingOffline> search(CQuery<AmazonProductListingOfflineCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonProductListingOfflineCriteria query = cquery.getSearch();
        AmazonProductListingOfflineExample example = query.getExample();
        example.setOrderByClause("sellerSku desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonProductListingOfflineMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonProductListingOffline> amazonProductListingOfflines = amazonProductListingOfflineMapper.selectByExample(example);
        amazonProductListingOfflines.forEach(amazonProductListingOffline -> {
            // 获取账户状态
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, amazonProductListingOffline.getAccountNumber(), true);
            if (account != null) {
                    amazonProductListingOffline.setAccountStatus(account.getAccountStatus());
            }
        });
        // 组装结果
        CQueryResult<AmazonProductListingOffline> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonProductListingOfflines);
        return result;
    }

    @Override
    public AmazonProductListingOffline selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return amazonProductListingOfflineMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonProductListingOffline> selectByExample(AmazonProductListingOfflineExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProductListingOfflineMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonProductListingOffline record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonProductListingOfflineMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonProductListingOffline record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonProductListingOfflineMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonProductListingOffline record, AmazonProductListingOfflineExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonProductListingOfflineMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonProductListingOfflineMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<Long> selectIdListByExample(AmazonProductListingOfflineExample example){
        Assert.notNull(example, "example is null!");
        return amazonProductListingOfflineMapper.selectIdListByExample(example);
    }

    @Override
    public List<AmazonProductListingOffline> selectFiledColumnsByExample(AmazonProductListingOfflineExample example){
        Assert.notNull(example, "example is null!");
        return amazonProductListingOfflineMapper.selectFiledColumnsByExample(example);
    }

    @Override
    public int batchInsert(List<AmazonProductListingOffline> amazonProductListingOfflineList){
        Assert.notNull(amazonProductListingOfflineList, "amazonProductListingOfflineList is null!");
        return amazonProductListingOfflineMapper.batchInsert(amazonProductListingOfflineList);
    }
}