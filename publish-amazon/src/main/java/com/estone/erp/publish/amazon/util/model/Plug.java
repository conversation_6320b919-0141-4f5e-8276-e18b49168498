package com.estone.erp.publish.amazon.util.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 插头规格枚举
 * 
 * <AUTHOR>
 *
 */
public enum Plug {
    UK("UK", "英规"), US("US", "美规"), AU("AU", "澳规"), EU("EU", "欧规");

    public static List<String> codes;

    private String code;

    private String desc;

    private Plug(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<String> getCodes() {
        if (codes == null) {
            codes = Plug.getPlugs().stream().map(plug -> {
                return plug.getCode();
            }).collect(Collectors.toList());
        }

        return codes;
    }

    public static Plug getPlug(String code) {
        for (Plug plug : Plug.values()) {
            if (plug.getCode().equals(code)) {
                return plug;
            }
        }

        return null;
    }

    public static List<Plug> getPlugs() {
        List<Plug> plugs = Arrays.asList(Plug.values());
        Collections.sort(plugs, (plug1, plug2) -> {
            return plug2.code.length() - plug1.code.length();
        });

        return plugs;
    }
}
