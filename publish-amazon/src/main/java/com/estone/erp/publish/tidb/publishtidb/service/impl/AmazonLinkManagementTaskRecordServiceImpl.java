package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementTaskRecord;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonLinkManagementTaskRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementTaskRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Amazon链接管理任务执行记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class AmazonLinkManagementTaskRecordServiceImpl extends ServiceImpl<AmazonLinkManagementTaskRecordMapper, AmazonLinkManagementTaskRecord> implements AmazonLinkManagementTaskRecordService {
    @Override
    public CQueryResult<AmazonLinkManagementTaskRecord> queryPage(CQuery<AmazonLinkManagementTaskRecord> query) {
        IPage<AmazonLinkManagementTaskRecord> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<AmazonLinkManagementTaskRecord> wrapper = new LambdaQueryWrapper<>();
        // TODO: build query wrapper by query.getSearch()
        
        IPage<AmazonLinkManagementTaskRecord> pageResult = page(page, wrapper);
        
        CQueryResult<AmazonLinkManagementTaskRecord> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }
}
