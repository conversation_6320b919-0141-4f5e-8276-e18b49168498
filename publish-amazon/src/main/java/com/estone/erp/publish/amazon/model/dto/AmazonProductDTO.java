package com.estone.erp.publish.amazon.model.dto;

import java.util.Date;
import java.util.List;

public class AmazonProductDTO{
    
    private Integer id;
    private String articleNumber;
    private String sellerSku;
    private String accountNumber;
    private Integer variantId;
    private String asin;
    private String category;
    private String name;
    private String infringementWord;
    private String mainImage;
    private Date createDate;
    private Date updateDate;
    private Date offlineDate;
    private String asins;
    private Double price;
    private String forbidChannel;
    private String skuStatus;
    private Boolean isOnline;
    private Boolean isPopular;
    private Boolean isFollowSellDelete;
    private Integer quantity;
    private String country;
    private String currency;
    private Boolean isSitePublish;
    private boolean existFollowSellLog;
    private Boolean spFlag;

    /**
     * 销售人员集合
     */
    private List<String> saleManList;

    public String getCountry() {
        return country;
    }
    public void setCountry(String country) {
        this.country = country;
    }
    public String getCurrency() {
        return currency;
    }
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    public String getInfringementWord() {
        return infringementWord;
    }
    public void setInfringementWord(String infringementWord) {
        this.infringementWord = infringementWord;
    }
    public String getForbidChannel() {
        return forbidChannel;
    }
    public void setForbidChannel(String forbidChannel) {
        this.forbidChannel = forbidChannel;
    }
    public String getSellerSku() {
        return sellerSku;
    }
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }
    public String getSkuStatus() {
        return skuStatus;
    }
    public void setSkuStatus(String skuStatus) {
        this.skuStatus = skuStatus;
    }
    public String getArticleNumber() {
        return articleNumber;
    }
    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }
    public String getAccountNumber() {
        return accountNumber;
    }
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
    public Date getUpdateDate() {
        return updateDate;
    }
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public Date getOfflineDate() {
        return offlineDate;
    }
    public void setOfflineDate(Date offlineDate) {
        this.offlineDate = offlineDate;
    }
    public Boolean getIsFollowSellDelete() {
        return isFollowSellDelete;
    }
    public void setIsFollowSellDelete(Boolean isFollowSellDelete) {
        this.isFollowSellDelete = isFollowSellDelete;
    }
    public Integer getQuantity() {
        return quantity;
    }
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getVariantId() {
        return variantId;
    }
    public void setVariantId(Integer variantId) {
        this.variantId = variantId;
    }
    public String getAsin() {
        return asin;
    }
    public void setAsin(String asin) {
        this.asin = asin;
    }
    public String getCategory() {
        return category;
    }
    public void setCategory(String category) {
        this.category = category;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getMainImage() {
        return mainImage;
    }
    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }
    public Date getCreateDate() {
        return createDate;
    }
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getAsins() {
        return asins;
    }
    public void setAsins(String asins) {
        this.asins = asins;
    }
    public Double getPrice() {
        return price;
    }
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public Boolean getIsOnline() {
        return isOnline;
    }
    public void setIsOnline(Boolean isOnline) {
        this.isOnline = isOnline;
    }
    public Boolean getIsPopular() {
        return isPopular;
    }
    public void setIsPopular(Boolean isPopular) {
        this.isPopular = isPopular;
    }

    public Boolean getIsSitePublish() {
        return isSitePublish;
    }

    public void setIsSitePublish(Boolean IsSitePublish) {
        isSitePublish = IsSitePublish;
    }

    public boolean isExistFollowSellLog() {
        return existFollowSellLog;
    }

    public void setExistFollowSellLog(boolean existFollowSellLog) {
        this.existFollowSellLog = existFollowSellLog;
    }

    public List<String> getSaleManList() {
        return saleManList;
    }

    public void setSaleManList(List<String> saleManList) {
        this.saleManList = saleManList;
    }

    public Boolean getSpFlag() {
        return spFlag;
    }

    public void setSpFlag(Boolean spFlag) {
        this.spFlag = spFlag;
    }
}
