package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.AmazonStaticInterfaceImpl;
import com.estone.erp.publish.amazon.componet.AmazonTemplateForbiddenSaleChannelHelper;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProhibitionInfringementInfo;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProhibitionInfringementInfoRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProhibitionInfringementInfoService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2022/3/11 17:17
 * @description http://************:8080/browse/ES-3213
 *  http://************:8080/browse/ES-7469
 * 最新需求 http://************:8080/browse/ES-8635
 *         禁售类型侵权且超过24小时未确认，系统自动确认功能
 *         数据禁售类型为侵权,侵权类型的禁售信息标记了amazon和当前链接站点，且在匹配规则时间超过24小时后还仍是待确认状态的数据，系统将自动确认；
 *         自动确认后进行以下操作：
 *         1.将确认状态改为已确认，确认人为admin，确认备注为"禁售类型侵权且24小时未确认，系统自动确认"；
 *         2.删除对应listing并记录处理报告（类型：下架产品），
 *         3.下架成功后，在线列表的对应listing备注改为：禁售类型侵权且24小时未确认，系统自动确认
 *
 *  http://************:8080/browse/ES-10697
 */
@Slf4j
@Component
public class SyncAmazonProhibitionListingDeleteJobHandler {

    @Getter
    @Setter
    public static class InnerParam {
        //禁售类型
        private List<String> forbidTypes;
        //禁售原因
        private List<String> causes;

        //skus
        private List<String> sellerSku;
        //店铺
        private List<String> accounts;
    }

    @Resource
    private EsAmazonProhibitionInfringementInfoService esAmazonProhibitionInfringementInfoService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private Environment environment;
    @Resource
    private AmazonTemplateForbiddenSaleChannelHelper amazonTemplateForbiddenSaleChannelHelper;

    @XxlJob("SyncAmazonProhibitionListingDeleteJobHandler")
    public ReturnT<String> run(String param) {
        //获取参数供测试使用
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("参数解析错误！");
                XxlJobLogger.log("参数解析错误！");
                return ReturnT.FAIL;
            }
        }
        List<String> accountNumberList = null;
        List<String> sellerSkuList = null;
        if(ObjectUtils.isNotEmpty(innerParam)){
            accountNumberList = innerParam.getAccounts();
             sellerSkuList = innerParam.getSellerSku();
        }

        if(CollectionUtils.isEmpty(accountNumberList) || CollectionUtils.isEmpty(sellerSkuList)){
            String[] activeProfiles = environment.getActiveProfiles();
            if(activeProfiles == null ||(!ArrayUtils.contains(activeProfiles, "k8stest") && !ArrayUtils.contains(activeProfiles, "prod"))){
                XxlJobLogger.log("当前环境为:{}, 必须指定店铺和sku执行", JSON.toJSONString(activeProfiles));

                return ReturnT.FAIL;
            }
        }

        //禁售类型
        List<String> forbidTypes = innerParam.getForbidTypes();
        if(CollectionUtils.isEmpty(forbidTypes)){
            ReturnT t = new ReturnT(500, "禁售类型配置不能为空！");
            return t;
        }

        // 禁售原因
        List<String> causes = innerParam.getCauses();

        //匹配时间24小时之前
        Date _24Before = DateUtils.addHours(new Date(), -24);
        String matchTime24Before = DateFormatUtils.format(_24Before, "yyyy-MM-dd HH:mm:ss");
        ArrayList<String> matchTimeList = new ArrayList<>(2);
        matchTimeList.add(null);
        matchTimeList.add(matchTime24Before);


        if (CollectionUtils.isEmpty(accountNumberList)) {
            EsAmazonProhibitionInfringementInfoRequest esAmazonProhibitionInfringementInfoRequest = new EsAmazonProhibitionInfringementInfoRequest();
            esAmazonProhibitionInfringementInfoRequest.setMatchTime(matchTimeList);
            esAmazonProhibitionInfringementInfoRequest.setIsOnline(true);
            // 未确认的数据
            esAmazonProhibitionInfringementInfoRequest.setConfirmStatus(false);
            esAmazonProhibitionInfringementInfoRequest.setConfirmStatusCode(0);
            esAmazonProhibitionInfringementInfoRequest.setCause(causes);
            esAmazonProhibitionInfringementInfoRequest.setForbidType(forbidTypes);
            // 聚合查所有账号
            accountNumberList = esAmazonProhibitionInfringementInfoService.getAccountNumberListByRequest(esAmazonProhibitionInfringementInfoRequest);
        }
        XxlJobLogger.log("匹配规则时间超过24小时的数据账号条数：{}",accountNumberList.size());
        if (CollectionUtils.isEmpty(accountNumberList)){
            XxlJobLogger.log("匹配规则时间超过24小时的数据未下架账号是空");
            return ReturnT.FAIL;
        }
        DataContextHolder.setUsername(StrConstant.ADMIN);
        int failCount = 0;
        int deleteTotal= 0;
        for (String accountNumber : accountNumberList) {
            XxlJobLogger.log("下架店铺 ：{}", accountNumber);
            EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
            request.setSaleAccount(new ArrayList<>(Arrays.asList(accountNumber)));
            if(CollectionUtils.isNotEmpty(sellerSkuList)){
                request.setSellerSku(sellerSkuList);
            }
            request.setForbidType(forbidTypes);
            //禁售原因
            if (CollectionUtils.isNotEmpty(causes)){
                request.setCause(innerParam.getCauses());
            }
            //未确认
            request.setConfirmStatus(false);
            request.setConfirmStatusCode(0);
            request.setMatchTime(matchTimeList);

            //页面大小
            int pageSize = 1000;
            //页码
            int pageIndex = 0;
            String accountMsg = null;
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
            if (account == null || !SaleAccountStastusEnum.NORMAL.getCode().equals(account.getAccountStatus())) {
                accountMsg = "下架不执行，店铺信息查不到或者状态是冻结,请检查 accountNumber:" + accountNumber;
            }else {
                AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
                // 过滤未授权到sp-api的账号
                if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                    accountMsg = "下架不执行，店铺未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:" + accountNumber;
                }
            }

            do {
                Page<EsAmazonProhibitionInfringementInfo> page = esAmazonProhibitionInfringementInfoService.page(request, pageSize, pageIndex++);
                if(page == null || CollectionUtils.isEmpty(page.getContent())){
                    break;
                }

                List<EsAmazonProhibitionInfringementInfo> esProhibitionList = page.getContent();
                for (EsAmazonProhibitionInfringementInfo bean : esProhibitionList) {
                    try {
                        AmazonProductListing listing = amazonProductListingService.selectBySkuAndAccountNumber(bean.getSellerSku(), accountNumber, bean.getSite());
                        if(listing != null){
                            BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer = AmazonStaticInterfaceImpl::consumerListingDelete;
                            ApiResult<ListingsItemSubmissionResponse> callBackResult = new ApiResult<>();
                            if (StringUtils.isNotBlank(accountMsg)){
                                callBackResult.ofFail(1001L, accountMsg);
                                biConsumer.accept(listing, callBackResult);
                                continue;
                            }

                            String sonAsin = listing.getSonAsin();
                            String sonsku = bean.getSku();
                            String country = bean.getSite();
                            String msg = null;
                            if (StringUtils.isBlank(sonsku) || StringUtils.isBlank(country) || StringUtils.isBlank(accountNumber)){
                                msg = String.format("Asin: %s  sku或者站点数据为空，不下架", sonAsin);
                                callBackResult.ofFail(1001L, msg);
                                biConsumer.accept(listing, callBackResult);
                                continue;
                            }
                            //在调用产品系统接口校验一下
                            ApiResult<Map<String, Boolean>> result = amazonTemplateForbiddenSaleChannelHelper.prohibitionListingcheckForbiddenSaleChannel(bean,forbidTypes,true);
                            if (!result.isSuccess()) {
                                msg = String.format("Asin: %s  校验禁售信息失败，不下架 %s", sonAsin,result.getErrorMsg());
                                callBackResult.ofFail(1001L, msg);
                                biConsumer.accept(listing, callBackResult);
                                continue;
                            }
                            Map<String, Boolean> checkSku = result.getResult();
                            if (BooleanUtils.isFalse(checkSku.get(sonsku))) {
                                msg = String.format("Asin: %s  校验禁售信息不是侵权，不下架", sonAsin);
                                callBackResult.ofFail(1001L, msg);
                                biConsumer.accept(listing, callBackResult);
                                continue;
                            }
                            deleteTotal++;
                            // 回写es
                            listing.setAttribute3(AmazonOfflineEnums.Type.Forbidden_Delete.getDesc());
                            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Forbidden_Delete);
                            DeleteAmazonListingUtils.systemBatchRetireProduct(CommonUtils.arrayAsList(listing), deleteAmazonListingDto, biConsumer);
                        }
                    }catch (Exception e){
                        log.error("执行错误:", e);
                        failCount++;
                    }
                }
            }while (true);
        }

        XxlJobLogger.log("exec下架数量：{};  下架错误sku数量:{}",deleteTotal, failCount);
        DataContextHolder.setUsername(null);
        return ReturnT.SUCCESS;
    }

}
