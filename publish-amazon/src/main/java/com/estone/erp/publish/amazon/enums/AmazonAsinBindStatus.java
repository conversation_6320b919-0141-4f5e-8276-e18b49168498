package com.estone.erp.publish.amazon.enums;

public enum AmazonAsinBindStatus {
    // 0:待绑定 1:待上架产品, 2:待上传关系, 3:已上传关系, 4:绑定成功
    UN_BIND(0, "未绑定"),
    WAITING_UPLOAD(1, "待上传产品信息"),
    UPLOAD_PRODUCT_INFO(2, "已上传产品信息"),
    WAITING_RELATIONSHIP(3, "待上传关系"),
    UPLOAD_RELATIONSHIP(4, "已上传关系"),
    BIND_SUCCESS(5, "绑定成功"),
    BIND_FAIL(6, "绑定失败");
    private final Integer code;
    private final String desc;

    private AmazonAsinBindStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
