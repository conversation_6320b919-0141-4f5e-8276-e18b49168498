package com.estone.erp.publish.amazon.model;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_keywords
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:04:32 CST 2019
 */
public class AmazonKeywords {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_keywords.id
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   关键词
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_keywords.keyword
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    private String keyword;

    /**
     * Database Column Remarks:
     *   地区
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_keywords.country
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    private String country;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_keywords.create_time
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   关键词短语
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_keywords.keyword_value
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    private String keywordValue;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_keywords.id
     *
     * @return the value of amazon_keywords.id
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_keywords.id
     *
     * @param id the value for amazon_keywords.id
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_keywords.keyword
     *
     * @return the value of amazon_keywords.keyword
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public String getKeyword() {
        return keyword;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_keywords.keyword
     *
     * @param keyword the value for amazon_keywords.keyword
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public void setKeyword(String keyword) {
        this.keyword = keyword == null ? null : keyword.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_keywords.country
     *
     * @return the value of amazon_keywords.country
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public String getCountry() {
        return country;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_keywords.country
     *
     * @param country the value for amazon_keywords.country
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_keywords.create_time
     *
     * @return the value of amazon_keywords.create_time
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_keywords.create_time
     *
     * @param createTime the value for amazon_keywords.create_time
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_keywords.keyword_value
     *
     * @return the value of amazon_keywords.keyword_value
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public String getKeywordValue() {
        return keywordValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_keywords.keyword_value
     *
     * @param keywordValue the value for amazon_keywords.keyword_value
     *
     * @mbg.generated Thu Jul 18 16:04:32 CST 2019
     */
    public void setKeywordValue(String keywordValue) {
        this.keywordValue = keywordValue == null ? null : keywordValue.trim();
    }
}