package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigAccountReport;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * amazon任务下架链接报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
public interface AmazonOfflineConfigAccountReportService extends IService<AmazonOfflineConfigAccountReport> {

    /**
     * 获取配置店铺数据
     *
     * @param configId
     * @param status
     * @param latestTime
     * @return
     */
    List<String> aggregateTotalTable(Integer configId, Integer status, LocalDateTime latestTime);
}
