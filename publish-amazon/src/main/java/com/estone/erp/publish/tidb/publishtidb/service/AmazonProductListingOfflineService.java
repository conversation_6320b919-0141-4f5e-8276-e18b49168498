package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-03 10:38:37
 */
public interface AmazonProductListingOfflineService {
    int countByExample(AmazonProductListingOfflineExample example);

    CQueryResult<AmazonProductListingOffline> search(CQuery<AmazonProductListingOfflineCriteria> cquery);

    List<AmazonProductListingOffline> selectByExample(AmazonProductListingOfflineExample example);

    AmazonProductListingOffline selectByPrimaryKey(Long id);

    int insert(AmazonProductListingOffline record);

    int updateByPrimaryKeySelective(AmazonProductListingOffline record);

    int updateByExampleSelective(AmazonProductListingOffline record, AmazonProductListingOfflineExample example);

    int deleteByPrimaryKey(List<Long> ids);

    List<Long> selectIdListByExample(AmazonProductListingOfflineExample example);

    List<AmazonProductListingOffline> selectFiledColumnsByExample(AmazonProductListingOfflineExample example);

    int batchInsert(List<AmazonProductListingOffline> amazonProductListingOfflineList);
}