package com.estone.erp.publish.amazon.jobHandler.template.publish;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.componet.templatestatus.AmazonTemplatePublishStatusHelper;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: ${description}
 * 修改模版状态
 */
@Component
public class AmazonTemplateSyncPublishStatusJobHandler extends AbstractJobHandler {

    private AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);

    @Autowired
    private AmazonTemplatePublishStatusHelper amazonTemplatePublishStatusHelper;

    public AmazonTemplateSyncPublishStatusJobHandler() {
        super("AmazonTemplateSyncPublishStatusJobHandler");
    }

    @Data
    private static class InnerParam {
        private int beforeMinutes;
        private int afterMinutes;
        /**
         * 全量检查时间段
         */
        private List<Integer> fullTimeHour;

        /**
         * 全量检查时间
         */
        private int fullTimeRangeMinutes;

        private int interfaceType = TemplateInterfaceTypeEnums.XSD.getCode();
    }

    @Override
    @XxlJob("AmazonTemplateSyncPublishStatusJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数错误");
            return ReturnT.FAIL;
        }



        LocalDateTime executeTime = LocalDateTime.now();
        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("*****************定时处理AmazonTemplate刊登状态*****************");

        int beforeMinutes = innerParam.getBeforeMinutes();
        int afterMinutes = innerParam.getAfterMinutes();

        // 是否全量更新模板状态
        int current_hour_24 = executeTime.getHour();
        if (innerParam.getFullTimeHour().contains(current_hour_24)) {
            XxlJobLogger.log("*****************定时处理AmazonTemplate全量检查刊登状态*");
            if (TemplateInterfaceTypeEnums.JSON.isTrue(innerParam.getInterfaceType())) {
                int total = amazonTemplatePublishStatusHelper.handleAmazonTemplatePublishStatus(60 * 6, 60 * 48);
                XxlJobLogger.log("定时处理AmazonTemplate刊登状态接口类型为JSON 总数：{}", total);
            } else {
                try {
                    int fullTimeRangeMinutes = innerParam.getFullTimeRangeMinutes();
                    //全量状态判断&模版刊登超时状态处理
                    amazonTemplateService.handleAmazonTemplateAllPublishingStatus(fullTimeRangeMinutes);
                } catch (Exception e) {
                    XxlJobLogger.log("定时处理AmazonTemplate全量检查刊登状态 模板更新失败请检查");
                }
            }
            return ReturnT.SUCCESS;

        }
        if (TemplateInterfaceTypeEnums.JSON.isTrue(innerParam.getInterfaceType())) {
            XxlJobLogger.log("定时处理AmazonTemplate刊登状态接口类型为JSON");
            int total = amazonTemplatePublishStatusHelper.handleAmazonTemplatePublishStatus(innerParam.getBeforeMinutes(), innerParam.getAfterMinutes());
            XxlJobLogger.log("定时处理AmazonTemplate刊登状态接口类型为JSON 总数：{}", total);
            return ReturnT.SUCCESS;
        } else {
            amazonTemplateService.handleAmazonTemplatePublishStatus(beforeMinutes, afterMinutes);
            long endTime = System.currentTimeMillis();
            XxlJobLogger.log("定时处理AmazonTemplate刊登状态SyncAmazonTemplatePublishStatusJobHandler cost time is "
                    + ((endTime - startTime) / 1000L));
        }
        return ReturnT.SUCCESS;
    }
}
