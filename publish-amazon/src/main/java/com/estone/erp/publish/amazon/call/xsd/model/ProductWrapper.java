package com.estone.erp.publish.amazon.call.xsd.model;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.XsdRouteData;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.Map.Entry;

public class ProductWrapper {
    public static ElementWrapper descriptionData;

    public static Map<String, ElementWrapper> productDatas = new LinkedHashMap<String, ElementWrapper>();

    public static Map<String, ElementWrapper> productTypes = new LinkedHashMap<String, ElementWrapper>();

    public static List<String> productTypeKeys = new ArrayList<String>();

    public static ElementWrapper getProductData(String productType) {
        if (StringUtils.isEmpty(productType)) {
            return null;
        }

        ElementWrapper result = null;
        if (!productType.contains(".")) {
            result = productDatas.get(productType);
            if(result != null){
                result.setRequired(true);
            }
        }
        else {
            String[] splits = StringUtils.split(productType, ".");
            ElementWrapper productDataWrapper = productDatas.get(splits[0]);
            ElementWrapper productTypeWrapper = productTypes.get(productType);
            Map<String, ElementWrapper> items = productDataWrapper.getItems();
            if (items.containsKey("ProductType")) {
                items.put("ProductType", productTypeWrapper);
            }

            return productDataWrapper;
        }

        return result;
    }

    public static ElementWrapper getVariationTheme(ElementWrapper productDataWrapper, String productType) {
        if (productDataWrapper == null || StringUtils.isEmpty(productType) || !productTypeKeys.contains(productType)) {
            return null;
        }

        ElementWrapper result = null;
        ElementWrapper typeWrapper = null;
        if (productType.contains(".") && productDataWrapper.getItemWrapper("ProductType") != null
                && !(typeWrapper = productDataWrapper.getItemWrapper("ProductType")).getIsLeaf()) {
            ElementWrapper variationData = XsdUtils.getFirstElementWrapper(typeWrapper.getItems())
                    .getItemWrapper("VariationData");
            if (variationData != null) {
                result = variationData.getItemWrapper("VariationTheme");
            }
        }
        if (result == null) {
            ElementWrapper variationData = productDataWrapper.getItemWrapper("VariationData");
            if (variationData != null) {
                result = variationData.getItemWrapper("VariationTheme");
            }
        }

        return result;
    }

    public static void addProductTypePrefixRoute() {
        if (MapUtils.isEmpty(productTypes)) {
            return;
        }

        for (Entry<String, ElementWrapper> entry : productTypes.entrySet()) {
            String productType = entry.getKey();
            if (entry.getKey().contains(".")) {
                String[] splits = StringUtils.split(productType, ".");
                ElementWrapper productDataWrapper = productDatas.get(splits[0]);
                productDataWrapper.setRequired(true);
                ElementWrapper productTypeWrapper = entry.getValue();
                productTypeWrapper.setRequired(true);
                Map<String, ElementWrapper> items = productDataWrapper.getItems();
                if (items.containsKey("ProductType")) {
                    items.put("ProductType", productTypeWrapper);
                    if (MapUtils.isNotEmpty(productTypeWrapper.getItems())) {
                        XsdUtils.getFirstElementWrapper(productTypeWrapper.getItems()).setRequired(true);
                    }
                    setProductTypePrefixRoute(productTypeWrapper, splits[0] + AmazonConstant.ROUTE_JOIN);
                }
            }
        }
    }

    private static void setProductTypePrefixRoute(ElementWrapper productTypeWrapper, String prefixRoute) {
        if (productTypeWrapper == null) {
            return;
        }

        productTypeWrapper.setRoute(prefixRoute + productTypeWrapper.getRoute());
        Map<String, ElementWrapper> items = productTypeWrapper.getItems();
        if (MapUtils.isNotEmpty(items)) {
            for (ElementWrapper item : items.values()) {
                setProductTypePrefixRoute(item, prefixRoute);
            }
        }
    }

    public static ElementWrapper fillElementWrapperValues(ElementWrapper elementWrapper,
            List<XsdRouteData> routeDatas) {
        if (elementWrapper == null) {
            return elementWrapper;
        }

        ElementWrapper clone = XsdUtils.clone(elementWrapper);
        if (CollectionUtils.isEmpty(routeDatas)){
            return clone;
        }
        for (XsdRouteData routeData : routeDatas) {
            setElementWrapperValues(clone, routeData);
        }
        return clone;
    }

    private static void setElementWrapperValues(ElementWrapper elementWrapper, XsdRouteData routeData) {
        List<String> routes = Arrays.asList(StringUtils.split(routeData.getRoute(), AmazonConstant.ROUTE_JOIN));
        Iterator<String> iterator = routes.iterator();
        String singleRoute = iterator.next();
        if (!singleRoute.equals(elementWrapper.getName())) {
            return;
        }

        ElementWrapper routeElementWrapper = elementWrapper;
        do {
            routeElementWrapper.setSelected(true);
        }
        while (iterator.hasNext()
                && (routeElementWrapper = routeElementWrapper.getItemWrapper(singleRoute = iterator.next())) != null);

        if (singleRoute.equals(routes.get(routes.size() - 1)) && routeElementWrapper != null) {
            routeElementWrapper.setValues(routeData.getValues());
            List<AttributeWrapper> attrs = routeElementWrapper.getAttrs();
            if (CollectionUtils.isNotEmpty(attrs)) {
                for (AttributeWrapper attributeWrapper : attrs) {
                    attributeWrapper.setValues(routeData.getAttrValuesByName(attributeWrapper.getName()));
                }
            }
        }
    }

    public static Map<String, ElementWrapper> getThemeRoutes(ElementWrapper productData, List<String> variationThemes) {
        if (productData == null || CollectionUtils.isEmpty(variationThemes)) {
            return null;
        }

        Set<String> singleThemes = new HashSet<String>();
        for (String theme : variationThemes) {
            String[] splits = StringUtils.split(theme, "-");
            for (String split : splits) {
                singleThemes.add(split);
            }
        }
        // 为变体添加SizeMap和ColorMap节点
        singleThemes.add("SizeMap");
        singleThemes.add("ColorMap");
        Map<String, ElementWrapper> result = new HashMap<String, ElementWrapper>(singleThemes.size());
        LinkedHashMap<String, ElementWrapper> items = productData.getItems();
        setThemeRouteValue(items, singleThemes, result);

        return result;
    }

    private static void setThemeRouteValue(Map<String, ElementWrapper> items, Set<String> singleThemes,
            Map<String, ElementWrapper> result) {
        if (MapUtils.isEmpty(items)) {
            return;
        }

        for (Entry<String, ElementWrapper> entry : items.entrySet()) {
            ElementWrapper value = entry.getValue();
            if (value == null) {
                continue;
            }

            if (!BooleanUtils.toBoolean(value.getIsLeaf())) {
                setThemeRouteValue(value.getItems(), singleThemes, result);
            }
            else if (AmazonUtils.containsIgnoreCase(singleThemes, value.getName())) {
                String singleTheme = AmazonUtils.getIgnoreCase(singleThemes, value.getName());
                // 比较route的深度
                ElementWrapper routeWrapper = result.get(singleTheme);
                if (routeWrapper != null
                        && StringUtils.countMatches(routeWrapper.getRoute(), AmazonConstant.ROUTE_JOIN) >= StringUtils
                                .countMatches(value.getRoute(), AmazonConstant.ROUTE_JOIN)) {
                    continue;
                }
                result.put(singleTheme, value);
            }
        }
    }

    public static ElementWrapper selectParentage(ElementWrapper productDataWrapper, String route) {
        if (productDataWrapper == null || StringUtils.isEmpty(route)) {
            return null;
        }

        ElementWrapper parentage = productDataWrapper.getItemWrapper("Parentage");
        if (parentage == null) {
            List<String> singleRoutes = Arrays.asList(StringUtils.split(route, AmazonConstant.ROUTE_JOIN));
            singleRoutes.set(singleRoutes.size() - 1, "Parentage");
            parentage = getElementWrapperByRoutes(productDataWrapper, singleRoutes);
        }

        if (parentage != null) {
            setElementWrapperSelected(productDataWrapper,
                    Arrays.asList(StringUtils.split(parentage.getRoute(), AmazonConstant.ROUTE_JOIN)));
        }

        return parentage;
    }

    public static ElementWrapper getElementWrapperByRoutes(ElementWrapper rootWrapper, List<String> routes) {
        if (rootWrapper == null || CollectionUtils.isEmpty(routes)) {
            return null;
        }

        ElementWrapper wrapper = null;
        for (int i = 0; i < routes.size(); i++) {
            String route = routes.get(i);
            if (i == 0) {
                if (!rootWrapper.getRoute().equals(route)) {
                    return null;
                }

                wrapper = rootWrapper;
            }
            else {
                wrapper = wrapper.getItemWrapper(route);
                if (rootWrapper == null) {
                    return null;
                }
            }
        }

        return wrapper;
    }

    public static void setElementWrapperSelected(ElementWrapper rootWrapper, List<String> routes) {
        if (rootWrapper == null || CollectionUtils.isEmpty(routes)) {
            return;
        }

        ElementWrapper wrapper = null;
        for (int i = 0; i < routes.size(); i++) {
            String route = routes.get(i);
            if (i == 0) {
                if (!rootWrapper.getRoute().equals(route)) {
                    return;
                }

                wrapper = rootWrapper;
                wrapper.setSelected(true);
            }
            else {
                wrapper = wrapper.getItemWrapper(route);
                if (rootWrapper == null) {
                    return;
                }

                wrapper.setSelected(true);
            }
        }
    }
}
