package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhExchange;
import com.estone.erp.common.mq.model.VhQueue;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 同步品牌MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class SyncAmazonProductListingBrandMqConfig {

    /**
     * 同步品牌
     */
    private int amazonListingSyncBrandMqConsumers;
    private int amazonListingSyncBrandMqPrefetchCount;
    private boolean amazonListingSyncBrandMqListener;

    /**
     * 同步父asin
     */
    private int amazonListingSyncParentAsinMqConsumers;
    private int amazonListingSyncParentAsinMqPrefetchCount;
    private boolean amazonListingSyncParentAsinMqListener;

    /**
     * 同步ListingsItem
     */
    private int syncListingItemMsgMqConsumers;
    private int syncListingItemMsgMqPrefetchCount;
    private boolean syncListingItemMsgMqListener;

    /**
     * 检测商标词，侵权词全量配置
     */
    private int amazonListingCheckAllBrandMqConsumers;
    private int amazonListingCheckAllBrandMqPrefetchCount;
    private boolean amazonListingCheckAllBrandMqListener;

    @Bean
    public SyncAmazonProductListingBrandMqListener amazonListingSyncBrandMqListener() {
        return new SyncAmazonProductListingBrandMqListener();
    }

    @Bean
    public VhQueue demotestqueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, PublishQueues.PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_QUEUE, true, false, false, null);
    }

    @Bean
    public VhExchange demoTestExchange() {
        return new VhExchange(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, true, false, ExchangeTypes.DIRECT, null);
    }

    @Bean
    public VhBinding demoTestBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, PublishQueues.PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer amazonListingSyncBrandListenerContainer(
            SyncAmazonProductListingBrandMqListener syncAmazonProductListingBrandMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.PUBLISH_AMAZON_SYNC_LISTING_BRAND_QUEUE, syncAmazonProductListingBrandMqListener);
        return container;
    }

    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonListingSyncBrandMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonListingSyncBrandMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonListingSyncBrandMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    @Bean
    public SyncAmazonProductListingParentAsinMqListener syncAmazonProductListingParentAsinMqListener() {
        return new SyncAmazonProductListingParentAsinMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer syncListingParentAsinListenerContainer(
            SyncAmazonProductListingParentAsinMqListener syncAmazonProductListingParentAsinMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        syncListingParentAsinListenerContainer(container, PublishQueues.PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_QUEUE, syncAmazonProductListingParentAsinMqListener);
        return container;
    }

    private void syncListingParentAsinListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonListingSyncParentAsinMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonListingSyncParentAsinMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonListingSyncParentAsinMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    @Bean
    public SyncListingsItemMsgMqListener syncListingsItemMqListener() {
        return new SyncListingsItemMsgMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer syncListingsItemListenerContainer(
            SyncListingsItemMsgMqListener syncListingsItemMsgMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        syncListingsItemListenerContainer(container, PublishQueues.PUBLISH_AMAZON_SYNC_LISTINGS_ITEM_MSG_QUEUE, syncListingsItemMsgMqListener);
        return container;
    }

    private void syncListingsItemListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                        ChannelAwareMessageListener channelAwareMessageListener) {
        if (syncListingItemMsgMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(syncListingItemMsgMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(syncListingItemMsgMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    // amazon更新标题描述
    @Bean
    public Queue tempAmazonListingUpdateItemName() {
        return new Queue(PublishQueues.AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE);
    }

    @Bean
    public Binding tempAmazonListingUpdateItemNameBinding() {
        return new Binding(PublishQueues.AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE, DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE_KEY, null);
    }


    @Bean
    public AmazonListingCheckAllBrandMqListener amazonListingCheckAllBrandMqListener() {
        return new AmazonListingCheckAllBrandMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonListingCheckAllBrandMqListenerContainer(
            AmazonListingCheckAllBrandMqListener amazonListingCheckAllBrandMqListener , RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        amazonListingCheckAllBrandMqListenerContainer(container, PublishQueues.AMAZON_LISTING_CHECK_ALL_BRAND_QUEUE, amazonListingCheckAllBrandMqListener);
        return container;
    }


    private void amazonListingCheckAllBrandMqListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonListingCheckAllBrandMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonListingCheckAllBrandMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonListingCheckAllBrandMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    @Bean
    public Queue pushOrderActiveSkuData() {
        return new Queue(PublishQueues.PUSH_PUBLISH_ACTIVE_SKU_DATA_QUEUE);
    }

    @Bean
    public Binding pushOrderActiveSkuDataBinding() {
        return new Binding(PublishQueues.PUSH_PUBLISH_ACTIVE_SKU_DATA_QUEUE, DestinationType.QUEUE, PublishRabbitMqExchange.SALE_ORDER_DIRECT_EXCHANGE,
                PublishQueues.PUSH_PUBLISH_ACTIVE_SKU_DATA_KEY, null);
    }
}