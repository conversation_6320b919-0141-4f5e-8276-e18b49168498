package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AmazonDeleteAuditEnums {


    /**
     * 页面确认按钮枚举
     */
    @Getter
    @AllArgsConstructor
    enum Confirm {
        YES(1, "审核通过"),
        NO(2, "审核不通过");


        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }
    }


    /**
     * 审核状态枚举
     */
    @Getter
    @AllArgsConstructor
    enum AuditStatus {
        WAIT_LEADER(1, "待组长审核"),
        WAIT_SUPERVISOR(2, "待主管审核"),
        PASS_OFF(3, "审核通过已下架"),
        REJECT(4, "审核不通过"),
        AUDITING(5, "下架中"),
        DEFAULT_PASS(6, "默认通过");

        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }

        public static AuditStatus getEnumByCode(Integer code) {
            for (AuditStatus auditStatus : AuditStatus.values()) {
                if (auditStatus.code == code) {
                    return auditStatus;
                }
            }
            return null;
        }
    }
}
