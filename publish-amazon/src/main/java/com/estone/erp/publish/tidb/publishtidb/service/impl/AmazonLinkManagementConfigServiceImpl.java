package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;

import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonLinkManagementConfigMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * Amazon链接管理配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
public class AmazonLinkManagementConfigServiceImpl extends ServiceImpl<AmazonLinkManagementConfigMapper, AmazonLinkManagementConfig> implements AmazonLinkManagementConfigService {
    @Override
    public CQueryResult<AmazonLinkManagementConfig> queryPage(CQuery<AmazonLinkManagementConfig> query) {
        IPage<AmazonLinkManagementConfig> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<AmazonLinkManagementConfig> wrapper = new LambdaQueryWrapper<>();
        // TODO: build query wrapper by query.getSearch()
        
        IPage<AmazonLinkManagementConfig> pageResult = page(page, wrapper);
        
        CQueryResult<AmazonLinkManagementConfig> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }

    @Override
    public CQueryResult<AmazonLinkManagementConfig> queryPageByConditions(
            AmazonLinkManagementConfigQueryRequest queryRequest, 
            Integer pageNum, 
            Integer pageSize) {
        
        log.info("开始执行亚马逊链接管理配置查询，查询条件：{}", queryRequest);
        
        try {
            // 默认分页参数
            if (pageNum == null || pageNum <= 0) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20; // 默认每页20条记录，符合需求文档4.2要求
            }
            
            // 数据量限制检查，符合需求文档4.3要求
            if (pageSize > 200) {
                pageSize = 200; // 限制单页最大数据量，避免性能问题
            }
            
            // 创建分页对象
            IPage<AmazonLinkManagementConfig> page = new Page<>(pageNum, pageSize);
            
            // 构建查询条件
            LambdaQueryWrapper<AmazonLinkManagementConfig> wrapper = buildQueryWrapper(queryRequest);
            
            // 执行查询
            IPage<AmazonLinkManagementConfig> pageResult = page(page, wrapper);
            
            // 构建返回结果
            CQueryResult<AmazonLinkManagementConfig> result = new CQueryResult<>();
            result.setTotal(pageResult.getTotal());
            result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
            result.setRows(pageResult.getRecords());
            result.setSuccess(true);
            
            log.info("亚马逊链接管理配置查询完成，共查询到{}条记录", pageResult.getTotal());
            return result;
            
        } catch (Exception e) {
            log.error("亚马逊链接管理配置查询失败", e);
            CQueryResult<AmazonLinkManagementConfig> result = new CQueryResult<>();
            result.setSuccess(false);
            result.setMessage("查询失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 构建查询条件
     * 根据需求文档4.1查询条件设计和4.2查询逻辑实现
     * 
     * @param queryRequest 查询请求
     * @return 查询包装器
     */
    private LambdaQueryWrapper<AmazonLinkManagementConfig> buildQueryWrapper(AmazonLinkManagementConfigQueryRequest queryRequest) {
        LambdaQueryWrapper<AmazonLinkManagementConfig> wrapper = new LambdaQueryWrapper<>();
        
        if (queryRequest == null) {
            // 默认按创建时间倒序排列，避免使用主键排序（TiDB优化）
            wrapper.orderByDesc(AmazonLinkManagementConfig::getCreatedTime);
            return wrapper;
        }
        
        // 1. 规则名称 - 支持模糊查询
        if (StringUtils.hasText(queryRequest.getRuleName())) {
            wrapper.like(AmazonLinkManagementConfig::getRuleName, queryRequest.getRuleName().trim());
        }
        
        // 2. 店铺权限过滤 - 查询结果自动按用户权限范围过滤
        if (!CollectionUtils.isEmpty(queryRequest.getUserAuthorizedAccounts())) {
            // 用户只能查看权限范围内的配置规则
            wrapper.and(w -> {
                for (String account : queryRequest.getUserAuthorizedAccounts()) {
                    w.or().like(AmazonLinkManagementConfig::getAccounts, account);
                }
            });
        }
        
        // 3. 指定店铺查询 - 多选店铺查询
        if (!CollectionUtils.isEmpty(queryRequest.getAccounts())) {
            wrapper.and(w -> {
                for (String account : queryRequest.getAccounts()) {
                    w.or().like(AmazonLinkManagementConfig::getAccounts, account);
                }
            });
        }
        
        // 4. 站点查询 - 多选站点查询
        if (!CollectionUtils.isEmpty(queryRequest.getSites())) {
            wrapper.and(w -> {
                for (String site : queryRequest.getSites()) {
                    w.or().like(AmazonLinkManagementConfig::getAccountOption, site);
                }
            });
        }
        
        // 5. 启用状态查询 - 单选
        if (queryRequest.getStatus() != null) {
            wrapper.eq(AmazonLinkManagementConfig::getStatus, queryRequest.getStatus());
        }
        
        // 6. 调整方式查询 - 单选
        if (queryRequest.getRuleType() != null) {
            wrapper.eq(AmazonLinkManagementConfig::getRuleType, queryRequest.getRuleType());
        }
        
        // 7. 优先级查询 - 精确查询
        if (queryRequest.getLevel() != null) {
            wrapper.eq(AmazonLinkManagementConfig::getLevel, queryRequest.getLevel());
        }
        
        // 8. 创建时间范围查询
        if (queryRequest.getCreateTimeStart() != null) {
            wrapper.ge(AmazonLinkManagementConfig::getCreatedTime, queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            wrapper.le(AmazonLinkManagementConfig::getCreatedTime, queryRequest.getCreateTimeEnd());
        }
        
        // 9. 创建人查询 - 多选
        if (!CollectionUtils.isEmpty(queryRequest.getCreatedByList())) {
            wrapper.in(AmazonLinkManagementConfig::getCreatedBy, queryRequest.getCreatedByList());
        }
        
        // 排序规则：默认按创建时间倒序排列，避免使用主键排序（TiDB优化）
        wrapper.orderByDesc(AmazonLinkManagementConfig::getCreatedTime);
        
        return wrapper;
    }
}
