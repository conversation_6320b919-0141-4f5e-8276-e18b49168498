package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonProductMonitor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键自增 database column amazon_product_monitor.id
     */
    private Integer id;

    /**
     * 紫鸟名 database column amazon_product_monitor.purple_bird_name
     */
    private String purpleBirdName;

    /**
     * 店铺账号 database column amazon_product_monitor.account_number
     */
    private String accountNumber;

    /**
     * 销售 database column amazon_product_monitor.sale_id
     */
    private String saleId;

    /**
     * 站点 database column amazon_product_monitor.site
     */
    private String site;

    /**
     * 父asin database column amazon_product_monitor.parent_asin
     */
    private String parentAsin;

    /**
     * 子asin database column amazon_product_monitor.son_asin
     */
    private String sonAsin;

    /**
     * 主sku database column amazon_product_monitor.main_sku
     */
    private String mainSku;

    /**
     * 子sku database column amazon_product_monitor.article_number
     */
    private String articleNumber;

    /**
     * sellersku database column amazon_product_monitor.seller_sku
     */
    private String sellerSku;

    /**
     * esListingId 快速查询 database column amazon_product_monitor.es_listing_id
     */
    private String esListingId;

    /**
     * 监控id database column amazon_product_monitor.monitor_id
     */
    private Integer monitorId;

    /**
     * 监控类型 database column amazon_product_monitor.monitor_type
     */
    private Integer monitorType;

    /**
     * 监控条件 database column amazon_product_monitor.monitor_content
     */
    private String monitorContent;

    /**
     * 下架原因 database column amazon_product_monitor.offline_remark
     */
    private String offlineRemark;

    /**
     * 禁售平台 database column amazon_product_monitor.forbid_channel
     */
    private String forbidChannel;

    /**
     * 禁售类型 database column amazon_product_monitor.infringement_typename
     */
    private String infringementTypename;

    /**
     * 禁售原因 database column amazon_product_monitor.infringement_obj
     */
    private String infringementObj;

    /**
     * 禁售站点 database column amazon_product_monitor.prohibition_site
     */
    private String prohibitionSite;

    /**
     * 订单24小时销量 database column amazon_product_monitor.order_last_24h_count
     */
    private Integer orderLast24hCount;

    /**
     * 订单7天销量 database column amazon_product_monitor.order_last_7d_count
     */
    private Integer orderLast7dCount;

    /**
     * 订单14天销量 database column amazon_product_monitor.order_last_14d_count
     */
    private Integer orderLast14dCount;

    /**
     * 订单30天销量 database column amazon_product_monitor.order_last_30d_count
     */
    private Integer orderLast30dCount;

    /**
     * 订单总销量 database column amazon_product_monitor.order_num_total
     */
    private Integer orderNumTotal;

    /**
     * 上架时间 database column amazon_product_monitor.open_date
     */
    private Timestamp openDate;

    /**
     * 下架时间 database column amazon_product_monitor.offline_date
     */
    private Timestamp offlineDate;

    /**
     * 创建时间/跟踪时间 database column amazon_product_monitor.create_time
     */
    private Timestamp createTime;
}