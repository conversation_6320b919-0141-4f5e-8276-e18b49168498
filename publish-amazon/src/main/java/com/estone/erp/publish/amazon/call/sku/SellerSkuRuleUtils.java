package com.estone.erp.publish.amazon.call.sku;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonSellerSkuRuleBO;
import com.estone.erp.publish.amazon.call.sku.model.SellerSkuRule;
import com.estone.erp.publish.amazon.call.sku.model.UnitRule;
import com.estone.erp.publish.amazon.call.sku.model.UnitRuleValue;
import com.estone.erp.publish.amazon.model.AmazonSellerSkuRule;
import com.estone.erp.publish.amazon.service.AmazonSellerSkuRuleService;
import com.estone.erp.publish.common.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 
 * @Description: sellerSKU生成规则工具类
 * 
 * @ClassName: SellerSKURulerUtils
 * @Author: Kevin
 * @Date: 2018/10/18
 * @Version: 0.0.1
 */
@Component
@Slf4j
@DependsOn({ "springUtils" })
public class SellerSkuRuleUtils {

    /**
     * 字母集
     */
    public static final char[] LETTER_RANGE = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
            'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I',
            'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

    /**
     * 字符集
     */
    public static final char[] CHAR_RANGE = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '_', '-', 'a', 'b', 'c',
            'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x',
            'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S',
            'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

    private static AmazonSellerSkuRuleService amazonSellerSkuRuleService;

    @PostConstruct
    public void init() {
        amazonSellerSkuRuleService = SpringUtils.getBean(AmazonSellerSkuRuleService.class);
    }

    /**
     * 
     * @Description: 解析sellerSKU，获取sku
     *
     * @param sellerSKU
     * @param sellerSkuRule
     * @return
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    public static String parse(String sellerSKU, SellerSkuRule sellerSkuRule) {

        return null;
    }

    /**
     * 
     * @Description: 根据sku和生成规则，生成sellerSKU
     *
     * @param sku
     * @param sellerSkuRule
     * @return
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    public static String generate(String sku, SellerSkuRule sellerSkuRule) {
        if (sellerSkuRule == null) {
            return sku;
        }

        StringBuilder sb = new StringBuilder();
        // 添加前缀
        if (CollectionUtils.isNotEmpty(sellerSkuRule.getPrefixRuleValues())) {
            sellerSkuRule.getPrefixRuleValues().forEach(rulerValue -> {
                append(sb, generateUnitRulerValue(rulerValue));
            });
        }

        // 添加前分隔符
        append(sb, generateUnitRulerValue(sellerSkuRule.getPrefixSplitRuleValue()));

        // 添加sku
        append(sb, sku);

        // 添加后分隔符
        append(sb, generateUnitRulerValue(sellerSkuRule.getSuffixSplitRuleValue()));

        // 添加后缀
        if (CollectionUtils.isNotEmpty(sellerSkuRule.getSuffixRuleValues())) {
            sellerSkuRule.getSuffixRuleValues().forEach(rulerValue -> {
                append(sb, generateUnitRulerValue(rulerValue));
            });
        }

        return sb.toString();
    }

    /**
     * 
     * @Description: 追加StringBuilder
     *
     * @param sb
     * @param value
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    private static void append(StringBuilder sb, String value) {
        if (StringUtils.isNotEmpty(value)) {
            sb.append(value);
        }
    }

    public static String transfer(SellerSkuRule sellerSkuRule) {
        return null;
    }

    public static SellerSkuRule transfer(String ruler) {
        return null;
    }

    /**
     * 
     * @Description: 生成单元规则值
     *
     * @param unitRulerValue
     * @return
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    public static String generateUnitRulerValue(UnitRuleValue unitRulerValue) {
        if (unitRulerValue == null) {
            return null;
        }

        UnitRule unitRuler = unitRulerValue.getUnitRule();
        switch (unitRuler) {
            case Fix:
                return unitRulerValue.getFixValue();
            default:
                return randomUnitRulerValue(unitRuler, unitRulerValue.getLength());
        }
    }

    /**
     * 
     * @Description: 随机指定长度的单元规则值
     *
     * @param unitRuler
     * @param length
     * @return
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    private static String randomUnitRulerValue(UnitRule unitRuler, Integer length) {
        Random random = ThreadLocalRandom.current();
        StringBuilder sb = new StringBuilder();
        switch (unitRuler) {
            case Number:
                for (int i = 0; i < length; i++) {
                    sb.append(random.nextInt(10));
                }
                break;
            case Letter:
                int letterLength = LETTER_RANGE.length;
                for (int i = 0; i < length; i++) {
                    sb.append(LETTER_RANGE[random.nextInt(letterLength)]);
                }
                break;
            case Char:
                int charLength = CHAR_RANGE.length;
                for (int i = 0; i < length; i++) {
                    sb.append(CHAR_RANGE[random.nextInt(charLength)]);
                }
                break;
            default:
                break;
        }

        return sb.toString();
    }

    /**
     * 根据创建人和账号查找生成规则，按规则生成sellerSKU
     * 
     * @param sku
     * @return java.lang.String
     * @Author: listen
     * @Date 2019/1/2 18:42
     * @Version: 0.0.1
     */
    public static String generate(String sku ,AmazonSellerSkuRule sellerSku) {

        if(sellerSku == null){
            log.error("{},未找到sku生成规则",sku);
            throw new BusinessException("未找到sku生成规则");
        }

        AmazonSellerSkuRuleBO amazonSellerSkuRule = CommonUtils.objTransform(sellerSku, AmazonSellerSkuRuleBO.class);
        if (null == amazonSellerSkuRule) {
            log.error("{},未找到sku生成规则",sku);
            throw new BusinessException("未找到sku生成规则");
        }
        SellerSkuRule sellerSkuRule = amazonSellerSkuRule.getSellerSkuRule();
        return generate(sku, sellerSkuRule);
    }

    /**
     * 获取sellerSku规则
     * @param sellerId
     * @param createdBy
     * @return
     */
    public static AmazonSellerSkuRule getAmazonSellerSku(String sellerId, String createdBy) {
        AmazonSellerSkuRule amazonSellerSkuRule=null;
        if(StringUtils.isNotEmpty(createdBy) && !StringUtils.equalsIgnoreCase(createdBy, "admin")){
            amazonSellerSkuRule = amazonSellerSkuRuleService.getAmazonSellerSkuRule4Publish(createdBy,
                    sellerId);
        }
        if (null == amazonSellerSkuRule) {
            amazonSellerSkuRule = amazonSellerSkuRuleService.getAmazonSellerSkuRule4Publish(sellerId);
        }
        return amazonSellerSkuRule;
    }

}
