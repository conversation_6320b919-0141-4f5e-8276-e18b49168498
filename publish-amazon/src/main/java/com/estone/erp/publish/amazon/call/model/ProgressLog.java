package com.estone.erp.publish.amazon.call.model;

import com.alibaba.druid.util.StringUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * @Description: 进度日志类
 * 
 * @ClassName: ProgressLog
 * @Author: Kevin
 * @Date: 2018/10/19
 * @Version: 0.0.1
 */
public class ProgressLog {
    private Logger log = LoggerFactory.getLogger(ProgressLog.class);

    public ProgressLog(String name, int totalCount, int factor) {
        this.name = StringUtils.isEmpty(name) ? "DEFAULT" : name;
        this.totalCount = totalCount < 1 ? 1 : totalCount;
        this.factor = factor < 1 ? 20 : factor;
        log.warn("------------>Log[{}] execute start.<------------", name);
    }

    /**
     * log名称
     */
    private String name;

    /**
     * 总计数
     */
    private int totalCount;

    /**
     * 当前计数
     */
    private int currentCount = 0;

    /**
     * 触发因子：达到计数则打印log
     */
    private int factor = 20;

    /**
     * 已打印标记的计数
     */
    private int logCount = 0;

    private long startTime = System.currentTimeMillis();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(int currentCount) {
        this.currentCount = currentCount;
    }

    public int getLogCount() {
        return logCount;
    }

    public void setLogCount(int logCount) {
        this.logCount = logCount;
    }

    public int getFactor() {
        return factor;
    }

    public void setFactor(int factor) {
        this.factor = factor;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    /**
     * 
     * @Description: 添加到当前计数，并打印日志
     *
     * @param addValue
     * @Author: Kevin
     * @Date: 2018/10/19
     * @Version: 0.0.1
     */
    public void addAndLog(int addValue) {
        if (addValue < 1) {
            return;
        }

        currentCount += addValue;
        if ((currentCount - logCount) >= factor || currentCount >= totalCount) {
            logCount = currentCount;
            log.warn("------------>Log[{}] current/ total: {}/{}, progress: {}%, cost time: {}ms.<------------", name,
                    currentCount, totalCount, CommonUtils.round(currentCount * 100.0 / totalCount, 2),
                    System.currentTimeMillis() - startTime);
            if (currentCount >= totalCount) {
                log.warn("------------>Log[{}] execute end.<------------", name);
            }
        }
    }
}
