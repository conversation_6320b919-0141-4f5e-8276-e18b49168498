package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-10 14:12
 */
@Data
public class AmazonListingAsinBindDO {


    /**
     * accountNumber
     */
    @NotBlank(message = "accountNumber不能为空")
    private String accountNumber;

    /**
     * articleNumber
     */
    private String articleNumber;

    /**
     * site
     */
    @NotBlank(message = "site不能为空")
    private String site;

    /**
     * sellerSku
     */
    @NotBlank(message = "sellerSku不能为空")
    private String sellerSku;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String itemName;

    /**
     * 原始ASIN
     */
    private String originAsin;

    /**
     * 品牌
     */
    @NotBlank(message = "品牌不能为空")
    private String brand;

    /**
     * 制造商
     */
    @NotBlank(message = "制造商不能为空")
    private String manufacturer;

    /**
     * 制造商编号
     */
    @NotBlank(message = "制造商编号不能为空")
    private String modelNumber;

    /**
     * 平台类目id
     */
    @NotBlank(message = "平台类目id不能为空")
    private String recommendedBrowseNode;

    /**
     * 分类类型
     */
    @NotBlank(message = "分类类型不能为空")
    private String productType;

    /**
     * 变体主提
     */
    @NotBlank(message = "属性不能为空")
    private String variationTheme;

    /**
     * 产地
     */
    @NotBlank(message = "产地不能为空")
    private String countryOfOrigin;


    /**
     * listingIds
     */
    private List<String> listingIds;

    /**
     * sellerSkus
     */
    @NotEmpty(message = "sellerSkus不能为空")
    private List<String> sellerSkus;

    /**
     * sonAsins
     */
    @NotEmpty(message = "sonAsins不能为空")
    private List<String> sonAsins;

    /**
     * 变体属性项
     */
    private List<VariationTheme> variationThemes;

    @Data
    @NoArgsConstructor
    public static class VariationTheme {
        private String sellerSku;
        private Map<String, String> values;
    }

}
