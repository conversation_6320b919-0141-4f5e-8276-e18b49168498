package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.service.AmazonRestrictedCategoryService;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 模板相关工具类
 * @Auther yucm
 * @Date 2021/11/30
 */
@Slf4j
public class AmazonTemplateUtils {

    /**
     * 范本模板分表
     * @param isLock
     * @return
     */
    public static String getAmazonTemplateTable(Boolean isLock) {
        String table = null;
        if(BooleanUtils.isTrue(isLock)) {
            table = AmazonTemplateTableEnum.AMAZON_TEMPLATE_MODEL.getCode();
        } else {
            table = AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode();
        }
        return table;
    }

    public static List<String> getAllSku(List<AmazonTemplateBO> templateBOList) {
        if (CollectionUtils.isEmpty(templateBOList)) {
            return Lists.newArrayList();
        }
        List<String> skus = new ArrayList<>();
        for (AmazonTemplateBO template : templateBOList) {
            List<String> allSku = getAllSku(template);
            if (CollectionUtils.isNotEmpty(allSku)) {
                skus.addAll(allSku);
            }
        }
        return skus;
    }

    public static Boolean isSaleVariant(AmazonTemplateBO template) {
        return !(Boolean.FALSE.equals(template.getSaleVariant()) || template.getSaleVariant() == null);
    }

    public static List<String> getAllSku(AmazonTemplateBO template) {
        List<String> skus = new ArrayList<>();
        if (Boolean.FALSE.equals(template.getSaleVariant()) || template.getSaleVariant() == null){
            skus.add(template.getParentSku());
        }else {
            skus.add(template.getParentSku());
            if (StringUtils.isBlank(template.getVariations())) {
                return skus;
            }
            List<AmazonSku> amazonSkuList = JSON.parseArray(template.getVariations(), AmazonSku.class);
            List<String> sonSkus = amazonSkuList.stream().map(AmazonSku::getSku).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sonSkus)) {
                skus.addAll(sonSkus);
            }
        }
        return skus;
    }

    public static List<String> getAllSellerSkuSku(AmazonTemplateBO template) {
        List<String> skus = new ArrayList<>();
        if (Boolean.FALSE.equals(template.getSaleVariant()) || template.getSaleVariant() == null){
            skus.add(template.getSellerSKU());
        }else {
            skus.add(template.getSellerSKU());
            if (StringUtils.isBlank(template.getVariations())) {
                return skus;
            }
            List<AmazonSku> amazonSkuList = JSON.parseArray(template.getVariations(), AmazonSku.class);
            List<String> sonSkus = amazonSkuList.stream().map(AmazonSku::getSellerSKU).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sonSkus)) {
                skus.addAll(sonSkus);
            }
        }
        return skus;
    }

    public static Map<String, String> getAllSellerSkuSkuMapping(AmazonTemplateBO template) {
        Map<String, String> skus = new HashMap<>();
        if (Boolean.FALSE.equals(template.getSaleVariant()) || template.getSaleVariant() == null) {
            skus.put(template.getSellerSKU(), template.getParentSku());
        } else {
            skus.put(template.getSellerSKU(), template.getParentSku());
            if (StringUtils.isBlank(template.getVariations())) {
                return skus;
            }
            List<AmazonSku> amazonSkuList = JSON.parseArray(template.getVariations(), AmazonSku.class);
            for (AmazonSku amazonSku : amazonSkuList) {
                skus.put(amazonSku.getSellerSKU(), amazonSku.getSku());
            }
        }
        return skus;
    }


    public static List<String> getAllSonSku(AmazonTemplateBO template) {
        List<String> skus = new ArrayList<>();
        if (Boolean.FALSE.equals(template.getSaleVariant()) || template.getSaleVariant() == null){
            skus.add(template.getParentSku());
        }else {
            if (StringUtils.isBlank(template.getVariations())) {
                return skus;
            }
            List<AmazonSku> amazonSkuList = JSON.parseArray(template.getVariations(), AmazonSku.class);
            List<String> sonSkus = amazonSkuList.stream().map(AmazonSku::getSku).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sonSkus)) {
                skus.addAll(sonSkus);
            }
        }
        return skus;
    }

    public static AmazonTemplateBO copyTemplateBO(AmazonTemplateBO amazonTemplate) {
        AmazonTemplateBO template = new AmazonTemplateBO();
        BeanUtils.copyProperties(amazonTemplate, template);
        template.setId(null);
        template.setIsLock(false);
        template.setStepTemplateStatus(null);
        template.setPublishStatus(null);
        template.setIsSitePublish(null);
        template.setCreationDate(new Date());
        template.setLastUpdateDate(new Date());
        template.setLastUpdatedBy(null);
        template.setListingRelationTimes(0);
        template.setTitleRule(null);
        if (template.getSearchData() != null) {
            try {
                Object keyWord = KeyWordUtils.packagingKeyWord(template);
                template.setSearchTerms(JSON.toJSONString(keyWord));
            } catch (Exception e) {
                log.error("处理关键词出错：", e);
            }
        }
        return template;
    }

    /**
     * 标题最后一个单词为for 去掉for
     *
     * @param title
     * @return
     */
    public static String replaceLastFor(String title) {
        if (StringUtils.isBlank(title)) {
            return title;
        }

        String[] replaceWords = {" for", " für", " pour", " per", " para", " voor", " för" ," dla"};
        for (String replaceWord : replaceWords) {
            if (title.toLowerCase().endsWith(replaceWord.toLowerCase())) {
                int endIndex = title.toLowerCase().lastIndexOf(replaceWord.toLowerCase());
                title = title.substring(0, endIndex);
                break;
            }
        }

        return title;
    }

    /**
     * 每个单词首字母处理为大写
     *
     * @param title
     * @return
     */
    public static String uppercaseLetter(String title) {
        if (StringUtils.isBlank(title)) {
            return title;
        }

        String[] words = title.split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            result.append(capitalizeFirstLetter(convertToTitleCase(word)));
            result.append(" ");
        }
        return result.toString().trim();
    }

    public static String capitalizeFirstLetter(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    public static String convertToTitleCase(String inputString) {
        StringBuilder titleCase = new StringBuilder();
        boolean nextTitleCase = true;

        for (char c : inputString.toCharArray()) {
            if (Character.isWhitespace(c)) {
                nextTitleCase = true;
            } else if (nextTitleCase) {
                c = Character.toTitleCase(c);
                nextTitleCase = false;
            }
            titleCase.append(c);
        }

        return titleCase.toString();
    }

    /**
     * 美国服装类目的标题最大限制字数125
     *
     * @param title
     * @param sellerId
     * @param country
     * @param categoryId
     * @return
     */
    public static String usClothingLimitation(String title, String sellerId, String country, String categoryId) {
        if (StringUtils.isBlank(title)
                || title.length() <= 125
                || StringUtils.isBlank(sellerId)
                || !"US".equalsIgnoreCase(country)
                || StringUtils.isBlank(categoryId)) {
            return title;
        }
        AmazonRestrictedCategoryService bean = SpringUtils.getBean(AmazonRestrictedCategoryService.class);
        Set<String> usTitleLengthLimitCid = bean.listUsTitleLengthLimitCid();
        if (CollectionUtils.isEmpty(usTitleLengthLimitCid)) {
            return title;
        }

        for (String cid : usTitleLengthLimitCid) {
            if (cid.contains(categoryId)) {
                return title.substring(0, 125);
            }
        }
        return title;
    }

    /**
     * 属性替换
     * @param nameValueList
     * @return
     */
    public static List<NameValue> handleValueReplace(List<NameValue> nameValueList,Set<String> valueSet){
        if (CollectionUtils.isEmpty(nameValueList)){
            return nameValueList;
        }

        for (NameValue nameValue : nameValueList){
            if (StrUtil.checkIsNum(nameValue.getValue())){
                String replaceValue = StrUtil.replacLettre(valueSet);
                nameValue.setValue(replaceValue);
                valueSet.add(replaceValue.toUpperCase());
            }
        }
        return nameValueList;
    }

    /**
     * 获取产品最早的录入时间
     * @param spu         主货号
     * @param dataSource  数据来源
     * @return 产品最早的录入时间
     */
    public static String getProductFirstCreateDate(String spu, Integer dataSource) {
        // 获取产品最早的录入时间
        String  firstCreateTime = null;
        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(dataSource)) {
            firstCreateTime = ProductUtils.getComposeProductFirstCreateTime(spu);
        }else {
            firstCreateTime = ProductUtils.getProductFirstCreateTime(spu);
        }
        return firstCreateTime;
    }

    /**
     * ES-8868 判定某些词汇不可写在标题首位
     * @param templateInfo 模版
     */
    public static void removeTitleFirstWord(AmazonTemplateBO templateInfo) {
        String title = templateInfo.getTitle();
        if (StringUtils.isBlank(title)) {
            return;
        }

        List<String> firstInfringementWords = ProductUtils.queryAmzFirstInfringementWords();
        Map<String, String> wordsMap = firstInfringementWords.stream().collect(Collectors.toMap(StringUtils::lowerCase, word -> word, (k1, k2) -> k1));
        // 获取标题中的第一个空格前的词
        String firstWord = title.split(" ")[0];
        if (wordsMap.containsKey(firstWord.toLowerCase())) {
            // 标题第一个词为不可写词，则将其删除
            String newTitle = title.replaceFirst(firstWord, "");
            // 去掉首位空格
            newTitle = newTitle.trim();
            templateInfo.setTitle(newTitle);
        }
    }

    public static List<String> filterNoDomainLinks(List<String> images) {
        List<String> result = new ArrayList<>();
        Pattern pattern = Pattern.compile("http://\\d+\\.\\d+\\.\\d+\\.\\d+(:\\d+)?/.*");
        for (String link : images) {
            Matcher matcher = pattern.matcher(link);
            if (matcher.matches()) {
                result.add(link);
            }
        }
        return result;
    }

    public static List<String> getAllImages(List<AmazonTemplateBO> amazonTemplates) {
        Set<String> allImages = new HashSet<>();
        amazonTemplates.forEach(template -> {
            if (isSaleVariant(template)) {
                List<AmazonSku> amazonSkus = template.getAmazonSkus();
                amazonSkus.forEach(sku -> {
                    String mainImage = sku.getMainImage();
                    if (StringUtils.isNotEmpty(mainImage)) {
                        allImages.add(mainImage);
                    }
                    String sampleImage = sku.getSampleImage();
                    if (StringUtils.isNotEmpty(sampleImage)) {
                        allImages.add(sampleImage);
                    }
                    String gpsrImage = sku.getGpsrImage();
                    if (StringUtils.isNotEmpty(gpsrImage)) {
                        allImages.add(gpsrImage);
                    }
                    List<String> extraImagesList = sku.getExtraImagesList();
                    if (CollectionUtils.isNotEmpty(extraImagesList)) {
                        allImages.addAll(extraImagesList);
                    }
                });
            } else {
                if (StringUtils.isNotEmpty(template.getMainImage())) {
                    allImages.add(template.getMainImage());
                }
                if (StringUtils.isNotEmpty(template.getSampleImage())) {
                    allImages.add(template.getSampleImage());
                }
                List<String> extraImagesList = template.getExtraImagesList();
                if (CollectionUtils.isNotEmpty(extraImagesList)) {
                    allImages.addAll(extraImagesList);
                }
            }
        });
        return new ArrayList<>(allImages);
    }


    public static List<String> getAttrValueList(List<AmazonSku> amazonSkus, Integer interfaceType) {
        if (TemplateInterfaceTypeEnums.JSON.isTrue(interfaceType)) {
            return getAttrValueListJson(amazonSkus);
        }
        return getAttrValueListString(amazonSkus);
    }

    private static List<String> getAttrValueListString(List<AmazonSku> amazonSkus) {
        List<String> attrList = new ArrayList<>();
        for (AmazonSku amazonSku : amazonSkus) {
            List<NameValue> nameValueList = amazonSku.getNameValues();
            if (CollectionUtils.isNotEmpty(nameValueList)) {
                List<String> attrs = nameValueList.stream().filter(Objects::nonNull).map(NameValue::getValue).collect(Collectors.toList());
                attrList.addAll(attrs);
            }
        }
        return attrList;
    }

    private static List<String> getAttrValueListJson(List<AmazonSku> amazonSkus) {
        List<String> attrList = new ArrayList<>();
        for (AmazonSku amazonSku : amazonSkus) {
            List<NameValue> nameValueList = amazonSku.getNameValues();
            if (CollectionUtils.isNotEmpty(nameValueList)) {
                nameValueList.stream().filter(Objects::nonNull).forEach(nameValue -> {
                    if (StringUtils.isNotBlank(nameValue.getValue())) {
                        attrList.add(nameValue.getValue());
                    }
                });

            }
            Map<String, Object> variantAttribute = amazonSku.getVariantAttribute();
            if (MapUtils.isEmpty(variantAttribute)) {
                continue;
            }
            variantAttribute.keySet().forEach(key -> {
                JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(variantAttribute.get(key)));
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject attrObj = jsonArray.getJSONObject(i);
                    String value = attrObj.getString("value");
                    if (StringUtils.isNotBlank(value)) {
                        attrList.add(value);
                    }
                }
            });
        }
        return attrList;
    }

    public static String getSearchTerms(AmazonTemplateBO template, String skuNumber) {
        if (StringUtils.isBlank(template.getSearchTerms()) && Boolean.TRUE.equals(template.getIsLock())) {
            return template.getSearchData();
        }
        if (!isSaleVariant(template)) {
            List<String> searchTerms = JSON.parseArray(template.getSearchTerms(), String.class);
            return searchTerms.get(0);
        }


        Map<String, List<String>> searchDataMap = JSON.parseObject(template.getSearchTerms(), new TypeReference<>() {
        });
        if (MapUtils.isEmpty(searchDataMap)) {
            return "";
        }
        List<String> searchTerms = searchDataMap.get(skuNumber);
        if (CollectionUtils.isEmpty(searchTerms)) {
            return "";
        }
        return searchTerms.get(0);

    }


}