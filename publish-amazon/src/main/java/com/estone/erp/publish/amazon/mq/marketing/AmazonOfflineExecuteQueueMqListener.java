package com.estone.erp.publish.amazon.mq.marketing;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigListingQueueService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * AMAZON_OFFLINE_EXECUTE_QUEUE
 * <AUTHOR>
 * @date 2025-01-13 9:16
 */
@Slf4j
public class AmazonOfflineExecuteQueueMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonOfflineConfigListingQueueService amazonOfflineConfigListingQueueService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 获取消息体
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            AmazonOfflineConfigMessage lossMakingOrdersMsg = JSON.parseObject(body, AmazonOfflineConfigMessage.class);
            amazonOfflineConfigListingQueueService.executeOfflineConfigLink(lossMakingOrdersMsg);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("Amazon-offline-config queue listener body：{} error：", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
