package com.estone.erp.publish.amazon.call.xsd;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.call.xsd.model.AttributeWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.TypeWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.XsdAttr;
import com.estone.erp.publish.amazon.call.xsd.model.XsdElement;
import com.estone.erp.publish.amazon.call.xsd.model.XsdType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * XsdElement包装类 将XsdElement转换为数据包装类，满足前台数据展示，和接口数据转化
 * 
 * <AUTHOR>
 *
 */
public class XsdWrapperAdapter {
    public static ElementWrapper wrapXsdElement(XsdElement xsdElement, Function<XsdElement, Boolean> filterFun,
                                                String route) {
        if (xsdElement == null) {
            return null;
        }

        // 设置节点路由
        String eleRoute = xsdElement.getName();
        if (StringUtils.isNotEmpty(route)) {
            eleRoute = route + AmazonConstant.ROUTE_JOIN + eleRoute;
        }

        XsdType xsdType = xsdElement.getType();
        ElementWrapper elementWrapper = new ElementWrapper();
        elementWrapper.setName(xsdElement.getName());
        elementWrapper.setRoute(eleRoute);
        if (xsdType == null) {
            elementWrapper.setIsLeaf(true);
            return elementWrapper;
        }

        elementWrapper.setSingleValue(xsdElement.isSingle());
        elementWrapper.setOccurs(elementWrapper.getSingleValue() ? 1 : xsdElement.getMaxOccurs());
        elementWrapper.setRequired(xsdElement.getRequired());

        List<XsdAttr> xsdAttrs = XsdUtils.mergeNewList(xsdElement.getAttrs(), xsdType.getAttrs());
        if (CollectionUtils.isNotEmpty(xsdAttrs)) {
            List<AttributeWrapper> attrs = xsdAttrs.stream().map(xsdAttr -> {
                AttributeWrapper attr = new AttributeWrapper();
                attr.setName(xsdAttr.getName());
                attr.setRequired("required".equals(xsdAttr.getUse()));
                attr.setType(wrapXsdType(xsdAttr.getType()));
                return attr;
            }).collect(Collectors.toList());
            elementWrapper.setAttrs(attrs);
        }

        List<XsdElement> elements = xsdType.getElements();
        if (CollectionUtils.isNotEmpty(elements)) {
            LinkedHashMap<String, ElementWrapper> items = new LinkedHashMap<String, ElementWrapper>(elements.size());
            for (XsdElement subEle : elements) {
                boolean filter = filterFun == null || filterFun.apply(subEle);
                if (!filter) {
                    items.put(subEle.getName(), null);
                }
                else {
                    ElementWrapper itemWrapper = wrapXsdElement(subEle, filterFun, eleRoute);
                    items.put(itemWrapper.getName(), itemWrapper);
                }
            }
            elementWrapper.setItems(items);
            elementWrapper.setIsLeaf(false);
        }
        else {
            elementWrapper.setType(wrapXsdType(xsdType));
            elementWrapper.setIsLeaf(true);
        }

        return elementWrapper;
    }

    private static TypeWrapper wrapXsdType(XsdType xsdType) {
        if (xsdType == null) {
            return null;
        }

        TypeWrapper typeWrapper = new TypeWrapper();
        typeWrapper.setName(xsdType.getName());
        typeWrapper.setHasRestriction(xsdType.isHasRestriction());
        typeWrapper.setRestriction(xsdType.getRestriction());

        return typeWrapper;
    }
}
