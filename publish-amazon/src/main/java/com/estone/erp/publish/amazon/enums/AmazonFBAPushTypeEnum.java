package com.estone.erp.publish.amazon.enums;

/**
 * FBA产品推荐规则推送类型
 * SYSTEM_PUSH系统推送 ARTIFICIAL_PUSH人工推送
 * <AUTHOR>
 * @date 2021/12/28 16:16
 */
public enum AmazonFBAPushTypeEnum {
    SYSTEM_PUSH("SYSTEM_PUSH", "系统推送"),
    ARTIFICIAL_PUSH("ARTIFICIAL_PUSH", "人工推送");


    private String code;
    private String name;

     AmazonFBAPushTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
