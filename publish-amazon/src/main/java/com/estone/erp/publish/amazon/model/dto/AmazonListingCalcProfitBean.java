package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther yucm
 * @Date 2021/12/17
 */
@Getter
@Setter
public class AmazonListingCalcProfitBean {

    private BatchPriceCalculatorRequest request;

    private EsAmazonProductListing EsAmazonProductListing;
}
