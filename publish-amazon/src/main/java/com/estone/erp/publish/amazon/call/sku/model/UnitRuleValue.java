package com.estone.erp.publish.amazon.call.sku.model;

import lombok.Data;

/**
 * 
 * @Description: 单元规则值类
 * 
 * @ClassName: UnitRulerValue
 * @Author: Kevin
 * @Date: 2018/10/18
 * @Version: 0.0.1
 */
@Data
public class UnitRuleValue {

    /**
     * 单元规则
     */
    private UnitRule unitRule;

    /**
     * 固定值: for UnitRule.Fix
     */
    private String fixValue;

    /**
     * 数据长度: for UnitRule.Number, Letter, Char
     */
    private Integer length;

}
