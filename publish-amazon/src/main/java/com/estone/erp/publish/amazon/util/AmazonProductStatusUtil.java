package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateForbiddenSaleChannelHelper;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.bean.forbidden.ForbiddensAndItemStatusVO;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Amazon侵权禁售，产品状态相关工具类
 * http://172.16.2.103:8080/browse/ES-7468
 * http://172.16.2.103:8080/browse/ES-7464
 * <AUTHOR>
 * @Date 2021/11/10
 */
@Slf4j
public class AmazonProductStatusUtil {

    private static AmazonTemplateForbiddenSaleChannelHelper amazonTemplateForbiddenSaleChannelHelper;

    @PostConstruct
    public void init() {
        amazonTemplateForbiddenSaleChannelHelper = SpringUtils.getBean(AmazonTemplateForbiddenSaleChannelHelper.class);
    }

    /**
     * 拦截状态 拦截停产 存档 废弃 true拦截（String类型）
     *
     * @param itemStatus
     * @return
     */
    public static Boolean interceptSkuStatus(String itemStatus) {

        List<String> interceptStatus = new ArrayList<>();
        interceptStatus.add(SkuStatusEnum.STOP.getCode());
        interceptStatus.add(SkuStatusEnum.ARCHIVED.getCode());
        interceptStatus.add(SkuStatusEnum.DISCARD.getCode());

        if (interceptStatus.contains(itemStatus)) {
            return true;
        }

        return false;
    }

    /**
     * 拦截状态 拦截停产 存档 废弃 true拦截（Integer类型）
     *
     * @param itemStatus
     * @return
     */
    public static Boolean interceptStatus(Integer itemStatus) {

        Integer STOP = 7007;
        Integer ARCHIVED = 7008;
        Integer DISCARD = 7010;

        List<Integer> interceptStatus = new ArrayList<>();
        interceptStatus.add(STOP);
        interceptStatus.add(ARCHIVED);
        interceptStatus.add(DISCARD);

        if (interceptStatus.contains(itemStatus)) {
            return true;
        }

        return false;
    }

    /**
     * SPU 校验禁售和SKU状态 (根据skuListAndCode和站点)
     * @param skuListAndCode
     * @param country
     * @return
     */
    public static List<String> checkForbiddenAndItemStatus(SkuListAndCode skuListAndCode, String country,String accountNumber) {

        if (null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuListAndCode.getSkuList())) {
            return null;
        }

        List<String> skuList = skuListAndCode.getSkuList();

        // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
        List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
        if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
            return skuList;
        }

        Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
        while (it.hasNext()) {
            SonSkuFewInfo sonSkuFewInfo = it.next();
            String itemStatus = sonSkuFewInfo.getItemStatus();
            if(interceptSkuStatus(itemStatus)) {
                skuList = skuList.stream().filter(o -> !o.contains(sonSkuFewInfo.getSonSku())).collect(Collectors.toList());
                continue;
            }
        }
        //传入产品信息不包含特殊标签，故需要重新校验禁售信息
        ApiResult<Map<String, Boolean>> apiResult =  amazonTemplateForbiddenSaleChannelHelper.checkArticleNumberIsForbiddenSaleChannel(skuList,country,accountNumber);
        if (!apiResult.isSuccess()) {
            log.error("查询禁售信息不成功：" + apiResult.getErrorMsg());
            return null;
        }
        Map<String, Boolean> checkSku = apiResult.getResult();
        skuList.removeIf(sku -> (checkSku.containsKey(sku) && checkSku.get(sku)));
        return skuList;
    }

    /**
     * 校验禁售类型，禁售原因，禁售平台站点和SKU状态 (根据模板)
     * @param amazonTemplateBOS
     * @return
     */
    public static ApiResult<Map<String, Boolean>> checkForbiddenAndItemStatus(List<AmazonTemplateBO> amazonTemplateBOS) {
        // 返回结果
        Map<String, Boolean> resultMap = new HashMap<>();
        // 组合套装与管理单品区分
        List<AmazonTemplateBO> composeTemplates = new ArrayList<>();
        List<AmazonTemplateBO> productTemplates = new ArrayList<>();
        for (AmazonTemplateBO template : amazonTemplateBOS) {
            if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(template.getSkuDataSource())) {
                composeTemplates.add(template);
            }else if (SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode().intValue() != template.getSkuDataSource()){
                productTemplates.add(template);
            }
        }
        if (CollectionUtils.isNotEmpty(composeTemplates)) {
            AmazonTemplateBuilderHelper templateBuilderHelper = SpringUtils.getBean(AmazonTemplateBuilderHelper.class);
            for (AmazonTemplateBO composeTemplate : composeTemplates) {
                ApiResult<Boolean> result = templateBuilderHelper.checkCanPublish(composeTemplate);
                if (!result.isSuccess()) {
                    return ApiResult.newError(result.getErrorMsg());
                }
                Boolean canPublish = result.getResult();
                resultMap.put(composeTemplate.getParentSku(), !canPublish);
            }
        }
        if (CollectionUtils.isEmpty(productTemplates)) {
            return ApiResult.newSuccess(resultMap);
        }

        List<String> sonskuList = new ArrayList<>();
        for (AmazonTemplateBO amazonTemplateBO : productTemplates) {
            if (!amazonTemplateBO.getSaleVariant()) {
                sonskuList.add(amazonTemplateBO.getParentSku());
            } else {
                // 变体
                List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplateBO.getVariations(), AmazonSku.class);
                List<String> skuList = amazonSkuList.stream().map(amazonSku -> amazonSku.getSku()).collect(Collectors.toList());
                sonskuList.addAll(skuList);
            }
        }
        Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap = null;
        try {
            sonskuForbiddenAndSpecicalMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSku(sonskuList);
        } catch (Exception e) {
            return ApiResult.newError("getForbiddenAndSpecicalBySonSku方法报错" + e.getMessage());
        }
        if (MapUtils.isEmpty(sonskuForbiddenAndSpecicalMap)) {
            return ApiResult.newSuccess();
        }
        for (AmazonTemplateBO amazonTemplateBO : productTemplates) {
            try {
                // 校验产品状态和禁售
                if (!amazonTemplateBO.getSaleVariant()) {
                    // 单体
                    String parentSku = amazonTemplateBO.getParentSku();
                    Boolean flag = false;
                    ForbiddenAndSpecical vo = sonskuForbiddenAndSpecicalMap.get(parentSku);
                    if (null != vo) {
                        Integer itemStatus = vo.getItemStatus();
                        if (null != itemStatus && interceptStatus(itemStatus)) {
                            flag = true;
                            resultMap.put(parentSku, flag);
                            continue;
                        }
                        String infringementSaleProhibition = vo.getInfringementSaleProhibition();
                        if (StringUtils.isEmpty(infringementSaleProhibition)) {
                            resultMap.put(parentSku, flag);
                            continue;
                        } else {
                            List<Integer> specialGoodsTypes = vo.getSpecialGoodsTypes();
                            flag = amazonTemplateForbiddenSaleChannelHelper.isForbidChannelSingleItemEsByAmazon(infringementSaleProhibition, amazonTemplateBO.getSellerId(), amazonTemplateBO.getCountry(), specialGoodsTypes,Collections.emptyList());
                        }
                    }
                    resultMap.put(parentSku, flag);
                } else {
                    // 变体
                    List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplateBO.getVariations(), AmazonSku.class);
                    List<String> skuList = amazonSkuList.stream().map(amazonSku -> amazonSku.getSku()).collect(Collectors.toList());
                    for (String sku : skuList) {
                        Boolean flag = false;
                        ForbiddenAndSpecical vo = sonskuForbiddenAndSpecicalMap.get(sku);
                        if (null != vo) {
                            Integer itemStatus = vo.getItemStatus();
                            if (null != itemStatus && interceptStatus(itemStatus)) {
                                flag = true;
                                resultMap.put(sku, flag);
                                continue;
                            }
                            String infringementSaleProhibition = vo.getInfringementSaleProhibition();
                            if (StringUtils.isEmpty(infringementSaleProhibition)) {
                                resultMap.put(sku, flag);
                                continue;
                            } else {
                                List<Integer> specialGoodsTypes = vo.getSpecialGoodsTypes();
                                flag = amazonTemplateForbiddenSaleChannelHelper.isForbidChannelSingleItemEsByAmazon(infringementSaleProhibition, amazonTemplateBO.getSellerId(), amazonTemplateBO.getCountry(), specialGoodsTypes,Collections.emptyList());
                            }
                        }
                        resultMap.put(sku, flag);
                    }
                }
            } catch (Exception e) {
                log.error(amazonTemplateBO.getId() + "校验禁售和单品状态出错：" + e.getMessage());
            }
        }

        return ApiResult.newSuccess(resultMap);
    }


    /**
     * 校验禁售和SKU状态 (跟卖),跟卖暂时不需要变更，需求只要求更改模板
     * @param followSellList
     * @return
     */
    public static ApiResult<Map<String, Boolean>> checkFollowSellForbiddenAndItemStatus(List<AmazonFollowSellSuperiorBO> followSellList) {

        List<String> spuList = followSellList.stream().map(AmazonFollowSellSuperiorBO :: getParentSku).collect(Collectors.toList());

        // 查询ES
        List<SingleItemEs> singleItemEsList;
        SingleItemEsRequest singleItemEsRequest = new SingleItemEsRequest();
        String[] fields = {"mainSku", "sonSku", "salesProhibition", "itemStatus"};
        singleItemEsRequest.setSkuList(spuList);
        singleItemEsRequest.setFields(fields);
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        try {
            singleItemEsList = singleItemEsService.getSingleItemEsList(singleItemEsRequest);
        } catch (Exception e) {
            return ApiResult.newError("调用产品ES报错：" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(singleItemEsList)) {
            return ApiResult.newError("查询ES无结果");
        }

        Map<String, ForbiddensAndItemStatusVO> forbiddenSalesAndItemStatusMap = getForbiddenSalesAndItemStatusMap(singleItemEsList);

        if (MapUtils.isEmpty(forbiddenSalesAndItemStatusMap)) {
            return ApiResult.newError("getForbiddenSalesAndItemStatusMap方法报错");
        }

        // 返回结果
        Map<String, Boolean> resultMap = new HashMap<>();

        for (AmazonFollowSellSuperiorBO amazonFollowSellSuperiorBO : followSellList) {
            try {
                String parentSku = amazonFollowSellSuperiorBO.getParentSku();
                Boolean flag = false;
                ForbiddensAndItemStatusVO vo = forbiddenSalesAndItemStatusMap.get(parentSku);
                if (null != vo) {
                    Integer itemStatus = vo.getItemStatus();
                    if (null != itemStatus) {
                        if (interceptStatus(itemStatus)) {
                            flag = true;
                        }
                    }
                    List<SalesProhibitionsVo> salesProhibitionsVoList = vo.getSalesProhibitionsVoList();
                    if (CollectionUtils.isEmpty(salesProhibitionsVoList)) {
                        resultMap.put(parentSku, flag);
                        continue;
                    } else {
                        for (SalesProhibitionsVo salesProhibitionsVo : salesProhibitionsVoList) {
                            if (flag) {
                                break;
                            }
                            // 如果平台相等
                            if (SaleChannel.CHANNEL_AMAZON.equalsIgnoreCase(salesProhibitionsVo.getPlat())) {
                                List<Sites> sites = salesProhibitionsVo.getSites();
                                for (Sites site : sites) {
                                    if (site.getSite().equals(amazonFollowSellSuperiorBO.getCountry())) {
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                resultMap.put(parentSku, flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return ApiResult.newSuccess(resultMap);
    }


    /**
     * 根据ES查询禁售，状态结果整合结果返参
     *
     * @param totalSingleItemEsList
     * @return
     */
    private static Map<String, ForbiddensAndItemStatusVO> getForbiddenSalesAndItemStatusMap(List<SingleItemEs> totalSingleItemEsList) {
        //主sku / 子sku -> 禁售平台
        Map<String, ForbiddensAndItemStatusVO> mainMap = new HashMap<>();
        if (CollectionUtils.isEmpty(totalSingleItemEsList)) {
            return mainMap;
        }

        Map<String, List<SingleItemEs>> mainSkuSingleItemsMap = totalSingleItemEsList
                .stream().filter(o -> o.getMainSku() != null).collect(Collectors.groupingBy(SingleItemEs::getMainSku));
        for (String mainSku : mainSkuSingleItemsMap.keySet()) {
            List<SalesProhibitionsVo> salesProhibitionsVos = new ArrayList<>();
            for (SingleItemEs singleItemEs : mainSkuSingleItemsMap.get(mainSku)) {
                String salesProhibition = singleItemEs.getSalesProhibition();
                Integer itemStatus = singleItemEs.getItemStatus();
                List<SalesProhibitionsVo> salesProhibitionsVoList = new ArrayList<>();

                if (StringUtils.isNotBlank(salesProhibition) || null != itemStatus) {
                    List<SalesProhibitionsVo> prohibitionsVoList = JSON.parseObject(salesProhibition, new TypeReference<List<SalesProhibitionsVo>>() {
                    });
                    if (CollectionUtils.isNotEmpty(prohibitionsVoList)) {
                        salesProhibitionsVoList = new ArrayList<>(prohibitionsVoList);
                    }

                    ForbiddensAndItemStatusVO vo = new ForbiddensAndItemStatusVO();
                    vo.setItemStatus(itemStatus);
                    vo.setSalesProhibitionsVoList(salesProhibitionsVoList);
                    mainMap.put(singleItemEs.getSonSku(), vo);

                    salesProhibitionsVos.addAll(salesProhibitionsVoList);
                }
            }
            if (CollectionUtils.isNotEmpty(salesProhibitionsVos)) {
                List<SalesProhibitionsVo> list = ProductInfringementForbiddenSaleUtils.platMainSkuMap(salesProhibitionsVos);
                if (CollectionUtils.isNotEmpty(list)) {
                    ForbiddensAndItemStatusVO vo = new ForbiddensAndItemStatusVO();
                    vo.setSalesProhibitionsVoList(list);
                    mainMap.put(mainSku, vo);
                }
            }
        }

        return mainMap;
    }
}
