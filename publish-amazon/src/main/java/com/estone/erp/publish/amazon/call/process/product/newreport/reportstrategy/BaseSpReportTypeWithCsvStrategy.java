package com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Description: SP报告类型为CSV格式的抽象策略类
 * @ClassName: BaseSpReportTypeWithCsvStrategy
 */
@Slf4j
public abstract class BaseSpReportTypeWithCsvStrategy {
    private EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);

    public BaseSpReportTypeWithCsvStrategy(AmazonAccount account, String reportType, List<String> marketplaceIds) {
        this.syncSpProductData = new SyncSpProductData(account);
        this.reportType = reportType;
        this.marketplaceIds = marketplaceIds;
    }

    /**
     * 同步产品数据
     */
    private SyncSpProductData syncSpProductData;

    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 站点类型列表
     */
    private List<String> marketplaceIds;

    /**
     * {sellerSku, sku}
     */
    private Map<String, EsSkuBind> sellerSku2SkuMap = new HashMap<>();

    public SyncSpProductData getSyncSpProductData() {
        return syncSpProductData;
    }

    public void setSyncSpProductData(SyncSpProductData syncSpProductData) {
        this.syncSpProductData = syncSpProductData;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public List<String> getMarketplaceIds() {
        return marketplaceIds;
    }

    public void setMarketplaceIds(List<String> marketplaceIds) {
        this.marketplaceIds = marketplaceIds;
    }

    protected boolean checkParams(AmazonProductListing t, String[] splits) {
        return t != null && ArrayUtils.isNotEmpty(splits);
    }

    /**
     * @param lineSplits 过滤函数
     * @Description: 构建SyncProductData数据
     * @Author: Kevin
     * @Date: 2018/10/18
     * @Version: 0.0.1
     */
    public void constructSyncProductData(List<String[]> lineSplits) {
        if (CollectionUtils.isEmpty(lineSplits)) {
            return;
        }

        // 清空SyncProductData数据
        syncSpProductData.clear();

        // 初始化表头 各个值对应的下标
        Map<String, Integer> headerMap = initHeaderMap(lineSplits.get(0));
        syncSpProductData.setHeaderMap(headerMap);
        if(MapUtils.isEmpty(headerMap)) {
            return;
        }

        // 构建sellerSku 与行分割数据的map
        constructSellerSku2SplitsMap(lineSplits);

        // 构建sellerSku2SkuMap
        constructSellerSku2TSkuBindMap();

        // 构建 AmazonProductListing 行数据对象
        constractProductListingData(syncSpProductData.getSellerSku2SplitsMap());
    }

    /**
     * 构建sellerSKU的集合
     */
    private void constructSellerSku2TSkuBindMap() {
        List<String> sellerSkus = new ArrayList<>(syncSpProductData.getSellerSku2SplitsMap().keySet());
        if (CollectionUtils.isEmpty(sellerSkus)){
            return;
        }
        List<List<String>> lists = PagingUtils.pagingList(sellerSkus,1000);
        for (List<String> list : lists) {
            try {
                EsSkuBindRequest request = new EsSkuBindRequest();
                request.setPlatform(Platform.Amazon.name());
                request.setBindSkus(list);
                List<EsSkuBind> skuBinds = esSkuBindService.getEsSkuBinds(request);
                if (CollectionUtils.isNotEmpty(skuBinds)) {
                    skuBinds.forEach(skuBind -> {
                        if (!sellerSku2SkuMap.containsKey(skuBind.getBindSku())) {
                            sellerSku2SkuMap.put(skuBind.getBindSku(), skuBind);
                        }
                    });
                }
            }catch (Exception e) {
               log.error("解析文件查询，sellersku关系出错：" + e.getMessage());
            }
        }
    }

    protected abstract String getTableSellerSku();

    /**
     * @Description: 构建SyncProductData SellerSku2SplitsMap
     */
    public void constructSellerSku2SplitsMap(List<String[]> lineSplits) {
        if (CollectionUtils.isEmpty(lineSplits)) {
            syncSpProductData.getSellerSku2SplitsMap().clear();
            return;
        }

        Map<String, String[]> sellerSku2SplitsMap = new HashMap<String, String[]>(lineSplits.size());
        lineSplits.forEach(splits -> {
            sellerSku2SplitsMap.put(getColumnValue(getTableSellerSku(), splits), splits);
        });
        syncSpProductData.setSellerSku2SplitsMap(sellerSku2SplitsMap);
    }

    /**
     * 初始化NewSyncProductData
     *
     * @param sellerSku2SplitsMap
     */
    public void constractProductListingData(Map<String, String[]> sellerSku2SplitsMap) {
        AmazonAccount amazonAccount = syncSpProductData.getAccount();
        if (MapUtils.isEmpty(sellerSku2SplitsMap) || null == amazonAccount) {
            return;
        }

        String accountNumber = amazonAccount.getAccountNumber();
        String site = getCountry(amazonAccount);

        String accountLevel = amazonAccount.getAccountLevel();
        syncSpProductData.setSite(site);
        int size = sellerSku2SplitsMap.size();
        List<AmazonProductListing> amazonProductListingList = new ArrayList<>(size);
        for (Map.Entry<String, String[]> selleSkuMap : sellerSku2SplitsMap.entrySet()) {
            String sellerSku = selleSkuMap.getKey();

            // sellerSku 为空 或者当前行是表头则跳出
            String headerSellerSku = this.getTableSellerSku();
            if(StringUtils.isBlank(sellerSku) || sellerSku.equals(headerSellerSku)) {
                continue;
            }

            try {
                String[] splits = selleSkuMap.getValue();

                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(accountNumber);
                amazonProductListing.setSite(site);
                amazonProductListing.setSellerSku(sellerSku);
                amazonProductListing.setAttribute2(sellerSku.toUpperCase());
                amazonProductListing.setIsFollowSellDelete(false);
                amazonProductListing.setFollowSaleFlag("2");
                String userName = StringUtils.isNotEmpty(WebUtils.getUserName()) ? WebUtils.getUserName() : "admin";
                amazonProductListing.setCreatedBy(userName);

                // 货号以及数据来源,如果是匹配不到货号在处理产品信息时还会再次处理
                getArticleNumberAndDataSourceBySellerSku(amazonProductListing);

                // 文件数据解析为产品对象
                fillLineData2AmazonProductListing(amazonProductListing, splits);

                // 不可售、在售、内容不完整解析为在线，  获取不到（类似后台删除数据）也会改为不在线
                String itemStatus = amazonProductListing.getItemStatus();
               /* if(AmazonListingItemStatus.Active.getStatusCode().equals(itemStatus)
                    || AmazonListingItemStatus.Incomplete.getStatusCode().equals(itemStatus) || AmazonListingItemStatus.Inactive.getStatusCode().equals(itemStatus)) {
                    amazonProductListing.setIsOnline(true);
                } else {
                    amazonProductListing.setIsOnline(false);
                }*/

                amazonProductListing.setIsOnline(true);

                // 子ASIN如果为空 尝试根据product-id设置ASIN product-id-type 为4 对应EAN/UPC,为1对应ASIN 1，4含义未确切 且不知道其他值 暂未设置枚举
                Integer productIdType = amazonProductListing.getProductIdType();
                if(StringUtils.isBlank(amazonProductListing.getSonAsin()) && null != productIdType && 1 == productIdType.intValue()) {
                    amazonProductListing.setSonAsin(amazonProductListing.getProductId());
                }

                // 账号等级
                if (StringUtils.isNotEmpty(accountLevel)){
                    amazonProductListing.setAttribute1(accountLevel);
                }

                syncSpProductData.addSellerSku2AmazonProductListingMap(sellerSku, amazonProductListing);
                amazonProductListingList.add(amazonProductListing);
            } catch (Exception e) {
                log.error("构建单条数据异常" + sellerSku + accountNumber);
            }
        }
        syncSpProductData.setAmazonProductListingList(amazonProductListingList);
    }

    /**
     * @param lines
     * @param filter 过滤函数
     * @return
     * @Description: 过滤行数据，并转换为按制表符分割的数组
     * @Author: Kevin
     * @Date: 2018/10/15
     * @Version: 0.0.1
     */
    public abstract List<String[]> filterAndTransferLines(List<String> lines, Function<String[], Boolean> filter);

    /**
     * @param t      变体对象
     * @param splits 分割的行数据
     * @Description: 将行数据转换为对象T
     * @Author: Kevin
     * @Date: 2018/10/12
     * @Version: 0.0.1
     */
    public abstract void fillLineData2AmazonProductListing(AmazonProductListing t, String[] splits);

    /**
     * 初始化表头对应下标map
     * @param splits
     * @return
     */
    public Map<String, Integer> initHeaderMap(String[] splits) {
        Map<String, Integer> headerMap = new HashMap<>();

        int length = splits.length;
        for (int i = 0; i < length ; i++) {
            headerMap.put(splits[i], i);
        }

        return headerMap;
    }

    public String getCountry(AmazonAccount account) {
        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        return amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId()).getMarketplace();
    }

    /**
     * 根据平台货号获取系统sku，兼容老方案,数据来源取绑定sku
     *
     * @param amazonProductListing
     */
    protected void getArticleNumberAndDataSourceBySellerSku(AmazonProductListing amazonProductListing) {
        String sellerSku = amazonProductListing.getSellerSku();
        EsSkuBind esSkuBind = sellerSku2SkuMap.get(sellerSku);
        Integer skuDataSource = SkuDataSourceEnum.PRODUCT_SYSTEM.getCode();
        if (null != esSkuBind) {
            String articleNumber = StringUtils.isNotEmpty(esSkuBind.getSystemSku()) ? esSkuBind.getSystemSku() : esSkuBind.getSku();
            if (StringUtils.isNotEmpty(articleNumber)) {
                amazonProductListing.setArticleNumber(articleNumber);
            }
            // 数据来源
            if (articleNumber.startsWith("GT")) {
                skuDataSource = SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode();
            } else if (articleNumber.startsWith("TY")) {
                skuDataSource = SkuDataSourceEnum.TAN_YA_SYSTEM.getCode();
            }

            // 优先取绑定sku数据来源
            if (null != esSkuBind.getSkuDataSource()) {
                skuDataSource = esSkuBind.getSkuDataSource();
            }
            amazonProductListing.setSkuDataSource(skuDataSource);
            return;
        } else if (sellerSku.startsWith("FS#")){
            amazonProductListing.setArticleNumber(sellerSku.replaceFirst("FS#","").trim().toUpperCase());
        }
        else if (!sellerSku.contains("_") || sellerSku.split("_")[0].length() < 5) {
            amazonProductListing.setArticleNumber("匹配不到货号");
            return;
        } else {
            amazonProductListing.setArticleNumber(sellerSku.substring(0, sellerSku.indexOf("_")));
        }
        amazonProductListing.setSkuDataSource(skuDataSource);
    }

    /**
     * 获取某列 在单前行的数据
     * @param columnName
     * @param splits
     * @return
     */
    protected String getColumnValue(String columnName, String[] splits) {
        if(StringUtils.isBlank(columnName)) {
            return null;
        }

        Map<String, Integer> headerMap = syncSpProductData.getHeaderMap();
        if(MapUtils.isEmpty(headerMap)) {
            return null;
        }

        if(!headerMap.containsKey(columnName)) {
            // 第一个字符可能会多一个\uFEFF 若获取不到表头对应下标则 添加上﻿﻿﻿﻿\uFEFF 再次获取
            columnName = "\uFEFF" + columnName;
            if(!headerMap.containsKey(columnName)) {
                return null;
            }
        }

        Integer index = headerMap.get(columnName);
        if(null == index) {
            log.error("存在异常数据" + columnName + "，表头" + JSON.toJSONString(headerMap) + "，单前行" + JSON.toJSONString(splits));
            return null;
        } else {
            String columnValue = splits[index];
            return StringUtils.isNotBlank(columnValue) ? columnValue : null;
        }
    }
}
