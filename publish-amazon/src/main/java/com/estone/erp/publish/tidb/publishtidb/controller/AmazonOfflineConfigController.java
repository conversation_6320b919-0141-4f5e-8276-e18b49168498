package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonUpdateStatusDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.CurrentAccountRequest;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* <p>
* Amazon 下架配置 前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-01-07
*/
@RestController
@RequestMapping("/amazonOfflineConfig")
public class AmazonOfflineConfigController {

    @Autowired
    private AmazonOfflineConfigService amazonOfflineConfigService;


    /**
     * 获取当前用户所管理的店铺
     * @return
     */
    @RequestMapping("getCurrentUserAccounts")
    public ApiResult<List<String>> getCurrentUserAccounts(@RequestBody CurrentAccountRequest currentAccountRequest) {
        return amazonOfflineConfigService.getCurrentUserAccounts(currentAccountRequest);
    }


    /**
     * 更新或者保存
     */
    @RequestMapping("saveOrUpdate")
    public ApiResult<String> saveOrUpdate(@RequestBody @Validated AmazonOfflineConfigVO editParam) {
        return amazonOfflineConfigService.saveOrUpdate(editParam);
    }

    /**
     * 获取配置详情
     */
    @RequestMapping("edit/{id}")
    public ApiResult<AmazonOfflineConfigVO> editConfig(@PathVariable Integer id) {
        return amazonOfflineConfigService.editConfig(id);
    }

    /**
     * 查询配置列表
     */
    @PostMapping("/search")
    public ApiResult<IPage<AmazonOfflineConfigVO>> search(@RequestBody AmazonOfflineSearchDTO searchParam) {
        return amazonOfflineConfigService.search(searchParam);
    }


    /**
     * 批量启用、禁用
     */
    @PostMapping("/updateStatus")
    public ApiResult<?> updateStatus(@RequestBody @Valid AmazonUpdateStatusDTO requestParam) {
        return amazonOfflineConfigService.updateStatus(requestParam);
    }

}
