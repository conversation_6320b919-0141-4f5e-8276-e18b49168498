package com.estone.erp.publish.amazon.call.sku.model;

/**
 * 
 * @Description: 单元规则枚举
 * 
 * @ClassName: UnitRulerValue
 * @Author: Kevin
 * @Date: 2018/10/18
 * @Version: 0.0.1
 */
public enum UnitRule {
    /**
     * 数字
     */
    Number("[0-9]", "随机数"),

    /**
     * 字母
     */
    Letter("[a-zA-Z]", "随机字母"),

    /**
     * 字符
     */
    Char("[a-zA-Z0-9_-]", "随机字符"),

    Fix("[a-zA-Z0-9_-]", "固定字符");

    private UnitRule(String rangeRegex, String desc) {
        this.rangeRegex = rangeRegex;
        this.desc = desc;
    }

    /**
     * 值范围正则
     */
    private String rangeRegex;

    /**
     * 描述
     */
    private String desc;

    public String getRangeRegex() {
        return rangeRegex;
    }

    public void setRangeRegex(String rangeRegex) {
        this.rangeRegex = rangeRegex;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
