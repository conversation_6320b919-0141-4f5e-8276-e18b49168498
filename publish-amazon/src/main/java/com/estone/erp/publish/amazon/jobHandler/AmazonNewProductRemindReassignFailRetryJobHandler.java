package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.enums.AmazonNewProductRemindTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonReassignStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonNewRemind;
import com.estone.erp.publish.amazon.model.AmazonNewRemindExample;
import com.estone.erp.publish.amazon.service.AmazonNewRemindService;
import com.estone.erp.publish.amazon.util.AmazonNewProductRemindUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Amazon新品推荐重分配失败重试
 *
 * <AUTHOR>
 * @date 2023/6/19 16:41
 */
@Component
public class AmazonNewProductRemindReassignFailRetryJobHandler extends AbstractJobHandler {

    private static final String MANAGER_MARK = "主管标记已完成";

    @Resource
    private AmazonNewRemindService amazonNewRemindService;

    public AmazonNewProductRemindReassignFailRetryJobHandler() {
        super(AmazonNewProductRemindReassignFailRetryJobHandler.class.getName());
    }

    @Override
    @XxlJob("AmazonNewProductRemindReassignFailRetryJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------开始执行--------");

        // 查询分配失败且重试次数小于五次的数据
        AmazonNewRemindExample example = new AmazonNewRemindExample();
        AmazonNewRemindExample.Criteria criteria = example.createCriteria();
        criteria.andDataSourceTypeEqualTo(AmazonNewProductRemindTypeEnum.ASSIGN_YESTERDAY.getCode())
                .andReassignStatusEqualTo(AmazonReassignStatusEnum.REASSIGN_FAIL.getCode())
                .andReassignRetryCountLessThan(5);
        if (StringUtils.isNotBlank(param)) {
            criteria.andSpuEqualTo(param);
        }
        List<AmazonNewRemind> amazonNewReminds = amazonNewRemindService.selectByExample(example);
        if (CollectionUtils.isEmpty(amazonNewReminds)) {
            XxlJobLogger.log("没有分配失败的数据");
            return ReturnT.SUCCESS;
        }

        // 可重分配的新品
        List<AmazonNewRemind> canReassignList = new ArrayList<>();

        Map<String, List<AmazonNewRemind>> anrMap = amazonNewReminds
                .stream().collect(Collectors.groupingBy(AmazonNewRemind::getSpu));
        for (String spu : anrMap.keySet()) {
            List<AmazonNewRemind> amazonNewRemindList = anrMap.get(spu);
            try {
                // 校验spu是否可分配
                boolean canReassign = AmazonNewProductRemindUtil.checkPublishTemplate(amazonNewRemindList);
                if (canReassign) {
                    amazonNewRemindList = amazonNewRemindList.stream().filter(o -> !MANAGER_MARK.equals(o.getRemarks())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(amazonNewRemindList)) {
                        continue;
                    }
                    canReassignList.addAll(amazonNewRemindList);
                }
            } catch (Exception e) {
                XxlJobLogger.log(String.format("SPU：%s分配失败：%s", spu, e.getMessage()));
                amazonNewRemindService.handleReassignFail(amazonNewRemindList, e.getMessage());
            }
        }

        try {
            amazonNewRemindService.reassign(canReassignList,
                    AmazonNewProductRemindTypeEnum.ASSIGN_YESTERDAY.getCode(), StrConstant.ADMIN);
        } catch (Exception e) {
            amazonNewRemindService.handleReassignFail(canReassignList, e.getMessage());
        }

        XxlJobLogger.log("-------结束执行--------");
        return ReturnT.SUCCESS;
    }
}
