package com.estone.erp.publish.amazon.bo;

import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/11 17:44
 * @description
 */
@Data
public class AmazonAccountRelationBO extends AmazonAccountRelation {

    /**
     * sellerSku 生成规则list
     */
    private List<AmazonSellerSkuRule> amazonSellerSkuRuleList;

    /**
     * 算价规则配置list
     */
    private List<AmazonCalcPriceRule> amazonCalcPriceRuleList;

    /**
     * 运费模板list
     */
    private List<AmazonShippingCostModel> amazonShippingCostModelList;

    /**
     * 店铺list
     */
    private List<String> accountNumbers;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 修改开始时间
     */
    private Timestamp startLastUpdateDate;
    /**
     * 修改结束时间
     */
    private Timestamp endLastUpdateDate;
    /**
     * 销售list
     */
    private List<String> saleIds;

    /**
     * 销售
     */
    private String saleId;

    /**
     * 主管
     */
    private String supervisorId;

    /**
     * 产品系统二级分类名
     */
    private List<String> prodLevel2CategoryNameList;

    /**
     * 排除产品标签列表
     */
    private List<String> excludeLabelList;


    /**
     * 订单系统状态
     */
    private List<Integer> saleAccountStatusList;

    /**
     * 异常状态
     */
    private List<String> exceptionStatusList;

    /**
     * 下架配置
     */
    private AmazonAccountOfflineConfig accountOfflineConfig;

    public List<String> getProdLevel2CategoryNameList(){
        if (StringUtils.isNotEmpty(this.getProdCategoryNames())){
            prodLevel2CategoryNameList = new ArrayList<>();
            String [] prodLevel2CategoryNames =this.getProdCategoryNames().split("@@@");
            for (String prodCategoryName : prodLevel2CategoryNames ){
                prodLevel2CategoryNameList.add(prodCategoryName.contains(">")? StringUtils.substringBefore(prodCategoryName,">"):prodCategoryName);
            }
        }
        if (CollectionUtils.isNotEmpty(prodLevel2CategoryNameList)){
            prodLevel2CategoryNameList = prodLevel2CategoryNameList.stream().distinct().collect(Collectors.toList());
        }
        return prodLevel2CategoryNameList;
    }

    public List<String> getExcludeLabelList() {
        if (StringUtils.isNotEmpty(this.getExcludeLabel())) {
            excludeLabelList = CommonUtils.splitList(this.getExcludeLabel(), ",");
        }
        return excludeLabelList;
    }
}
