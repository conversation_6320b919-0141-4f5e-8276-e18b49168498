package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AmazonRestrictedCategoryEnums {

    @Getter
    @AllArgsConstructor
    enum BizCode {
        US_TITLE_LENGTH_LIMIT(10001, "US站点标题长度限制");
        private final int code;
        private final String desc;
    }


    @Getter
    @AllArgsConstructor
    enum Status {
        DISABLE(0, "禁用"),
        ACTIVE(1, "启用");

        private final int code;
        private final String desc;
    }
}
