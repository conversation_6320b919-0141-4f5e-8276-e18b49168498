package com.estone.erp.publish.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import com.estone.erp.common.redis.util.CommonUtils;
import com.estone.erp.publish.mybatis.DataSources;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;

import javax.sql.DataSource;

/***
 *  tidb  publish_amazon数据库配置类
 */
@Data
@Slf4j
@Order(2)
@Configuration
@ConfigurationProperties(prefix = "mybatis.tidb.publish-amazon")
@MapperScan(basePackages = {"com.estone.erp.publish.tidb.publishAmazon.mapper"}, sqlSessionFactoryRef = DataSources.TIDB_PUBLISH_AMAZON_FAC)
public class TidbPublishAmazonDataSourceConfig extends AbstractDataSourceConfig {
    /**
     * mapper xml的classpath路径
     */
    private String[] mapperLocations;

    /**
     * 获取mapper xml的资源
     *
     * @return
     */
    public Resource[] getMapperResources() {
        return CommonUtils.getResouces(mapperLocations);
    }

    @Bean(DataSources.TIDB_PUBLISH_AMAZON_DS)
    @ConfigurationProperties(prefix = "spring.datasource.tidb.publish-amazon")
    public DataSource dataSource() {
        log.info("===========加载数据源:spring.datasource.tidb.publish_amazon========");
        return new DruidDataSource(true);
    }

    /**
     * 将SqlSessionFactory配置为MybatisPlus的MybatisSqlSessionFactoryBean
     *
     * @return MybatisSqlSessionFactoryBean
     */
    @Bean(DataSources.TIDB_PUBLISH_AMAZON_FAC)
    @ConditionalOnBean(name = DataSources.TIDB_PUBLISH_AMAZON_DS)
    public MybatisSqlSessionFactoryBean setSqlSessionFactory() {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        // 设置数据源
        bean.setDataSource(dataSource());
        // xml扫描路径
        Resource[] mapperResources = getMapperResources();
        bean.setGlobalConfig(this.globalConfig());
        bean.setMapperLocations(mapperResources);
        // 插件
        Interceptor[] plugins = {paginationInterceptor()};
        bean.setPlugins(plugins);
        return bean;
    }

    /**
     * 设置全局配置
     *
     * @return 全局配置
     */
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        globalConfig.setDbConfig(dbConfig);
        return globalConfig;
    }


    /**
     * mybatis-plus分页插件
     */
    @Bean(name = "paginationInterceptor")
    @ConditionalOnBean(name = DataSources.TIDB_PUBLISH_AMAZON_DS)
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setDialectType(DbType.MYSQL.getDb());
        return paginationInterceptor;
    }
}
