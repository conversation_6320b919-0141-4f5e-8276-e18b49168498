package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.model.FilePart;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @Description: 报告文件下载进度模型
 * @Author: listen
 * @Date: 2018/9/28 10:57
 * @Version: 0.0.1
 */
@Data
public class AmazonReportDownloadProgressBO {

    /**
     * id
     */
    private Long id;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 报告请求ID
     */
    private String reportRequestId;

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 文件大小
     */
    private Long fileLength;

    /**
     * 文件相对路径
     */
    private String fileRelativePath;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * sha1值
     */
    private String sha1;

    /**
     * 生成时间（请求亚马逊时间）
     */
    private Date generationTime;

    /**
     * 状态(完成、未完成)
     */
    private Boolean success;

    /**
     * 下载完成部分[1,2,...]
     */
    private String successParts;

    /**
     * 所有filePart的json字符串
     */
    private String fileParts;

    /**
     * 完成时间（下载成功才有）
     */
    private Date successTime;

    /**
     * 结束状态（结束、未结束）
     */
    private Boolean finish;

    /**
     * 错误信息
     */
    private String errorMsg;

    public List<FilePart> ofFilePartList() {
        if (StringUtils.isNotEmpty(fileParts)) {
            return JSON.parseArray(this.fileParts, FilePart.class);
        }
        return null;
    }
}