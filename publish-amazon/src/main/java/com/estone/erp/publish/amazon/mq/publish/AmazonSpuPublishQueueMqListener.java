package com.estone.erp.publish.amazon.mq.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishContext;
import com.estone.erp.publish.amazon.componet.publish.PublishExecutor;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonPublishTypeEnums;
import com.estone.erp.publish.amazon.mq.model.AmazonPublishMessage;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * amazon 刊登队列
 * AMAZON_SPU_PUBLISH_QUEUE
 *
 * <AUTHOR>
 * @date 2022-05-30 17:31
 */
@Component
public class AmazonSpuPublishQueueMqListener implements ChannelAwareMessageListener {
    private PublishLogger logger = PublishLoggerFactory.getLogger(AmazonSpuPublishQueueMqListener.class);
    @Autowired
    public Map<String, PublishExecutor> publishExecutors;

    @Override
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            AmazonPublishMessage publishMessage = JSON.parseObject(body, AmazonPublishMessage.class);
            if (publishMessage == null) {
                // 空消息确认
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            AmazonPublishTypeEnums publishTypeEnums = AmazonPublishTypeEnums.getEnum(publishMessage.getPublishType());
            if (publishTypeEnums == null) {
                //logger.errorForPolicy("不支持的刊登类型: {},message:{}", "AMAZON_PUBLISH_SCHEDULE_QUEUE", new DefaultAlertPolicy(), publishMessage.getPublishType(), body);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            AmazonPublishContext publishContext = new AmazonPublishContext(publishMessage, publishTypeEnums);
            PublishExecutor publishExecutor = getPublishExecutor(publishTypeEnums.getServiceName());
            ApiResult<String> result = publishExecutor.publish(publishContext);
            // 无论成功失败都确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.errorForPolicy("Amazon SPU刊登队列消费失败, body: {}, error: {}", "AMAZON_PUBLISH_SCHEDULE_QUEUE", new DefaultAlertPolicy(), body, e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 获取刊登执行器
     *
     * @param serviceName 服务名称
     * @return PublishExecutor
     */
    private PublishExecutor getPublishExecutor(String serviceName) {
        PublishExecutor publishExecutor = publishExecutors.get(serviceName);
        if (publishExecutor == null) {
            throw new BusinessException("不支持的刊登类型: " + serviceName);
        }
        return publishExecutor;
    }

}
