package com.estone.erp.publish.amazon.util;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.model.AmazonCalcPriceRule;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/19 11:06
 * @description
 */
@Slf4j
public class AmazonAccountUtil {

    private static AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
    private static SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);

    public static SaleAccountAndBusinessResponse findAccount(String accountNumber, int retryNum) {
        SaleAccountAndBusinessResponse account = null;
        do {
            try {
                account = AccountUtils.getSaleAccountByAccountNumber(Platform.Amazon.name(), accountNumber);
                break;
            }catch (Exception e){
                ThreadUtil.sleep(2000);
                log.error("获取账号失败", e);
            }
        }while (--retryNum > 0);

        return account;
    }

    public static String findAccount2Site(String accountNumber, int retryNum){
        SaleAccountAndBusinessResponse account = findAccount(accountNumber, retryNum);
        if(account == null){
            return "";
        }
        List<AmazonMarketplace> marketplaceList = null;
        do {
            try {
                marketplaceList = amazonConstantMarketHelper.getMarketplaceList();
                break;
            }catch (Exception e){
                log.error("获取站点出错：", e);
            }
        }while (--retryNum > 0);

        if(CollectionUtils.isNotEmpty(marketplaceList)){
            String marketplaceId = account.getMarketplaceId();
            AmazonMarketplace marketplace = marketplaceList.stream()
                    .filter(t -> t.getMarketplaceId().equals(marketplaceId))
                    .findFirst()
                    .orElseGet(() -> new AmazonMarketplace());

            //站点
            return marketplace.getMarketplace();
        }

        return "";
    }

    /**
     * 根据亚马逊 marketplaceId 获取对应站点
     * @param marketplaceId
     * @param retryNum
     * @return
     */
    public static String findMarketplaceId2Site(String marketplaceId, int retryNum){
        List<AmazonMarketplace> marketplaceList = null;
        do {
            try {
                marketplaceList = amazonConstantMarketHelper.getMarketplaceList();
                break;
            }catch (Exception e){
                log.error("获取站点出错：", e);
            }
        }while (--retryNum > 0);

        if(CollectionUtils.isNotEmpty(marketplaceList)){
            AmazonMarketplace marketplace = marketplaceList.stream()
                    .filter(t -> t.getMarketplaceId().equals(marketplaceId))
                    .findFirst()
                    .orElseGet(() -> new AmazonMarketplace());

            //站点
            return marketplace.getMarketplace();
        }

        return "";
    }

    /**
     * 获取销售主管的账号
     * @param salesmanAccountDetailList
     * @return
     */
    public static List<String> getSalesSupervisorAccountList(Map<String, SalesmanAccountDetail> salesmanAccountDetailList) {
        List<String> salesSupervisorAccountList = new ArrayList<>();

        if (MapUtils.isEmpty(salesmanAccountDetailList)) {
            return salesSupervisorAccountList;
        }

        for (String account : salesmanAccountDetailList.keySet()) {
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailList.get(account);
            Set<String> salemanSet = salesmanAccountDetail.getSalesmanSet();
            String saleSupervisor = salesmanAccountDetail.getSalesSupervisorName();

            if (CollectionUtils.isNotEmpty(salemanSet) && StringUtils.isNotBlank(saleSupervisor) && salemanSet.size() == 1 && salemanSet.contains(saleSupervisor)) {
                salesSupervisorAccountList.add(account);
            }
        }
        return salesSupervisorAccountList;
    }

    /**
     * 检查算价规则同标签的价格区间是否重合
     * @param amazonCalcPriceRuleList
     * @return
     */
    public static Boolean checkPriceSection(List<AmazonCalcPriceRule> amazonCalcPriceRuleList) {
        if (CollectionUtils.isEmpty(amazonCalcPriceRuleList)) {
            return true;
        }
        
        // 根据标签分组
        Map<String, List<AmazonCalcPriceRule>> labelMap = new HashMap<>();
        for (AmazonCalcPriceRule amazonCalcPriceRule : amazonCalcPriceRuleList) {
            List<String> labelList = amazonCalcPriceRule.getLabelList();
            String key = null;

            // 标签为空时分为一组
            if (CollectionUtils.isEmpty(labelList)) {
                key = "labelIsNull";
            } else { // 标签不为空时将标签排序
                Collections.sort(labelList);
                key = StringUtils.join(labelList, ",");
            }

            if (labelMap.containsKey(key)) {
                List<AmazonCalcPriceRule> subAmazonCalcPriceRules = labelMap.get(key);
                subAmazonCalcPriceRules.add(amazonCalcPriceRule);
            } else {
                List<AmazonCalcPriceRule> subAmazonCalcPriceRules = new ArrayList<>();
                subAmazonCalcPriceRules.add(amazonCalcPriceRule);
                labelMap.put(key, subAmazonCalcPriceRules);
            }
        }

        for (Map.Entry<String, List<AmazonCalcPriceRule>> entry : labelMap.entrySet()) {
            List<AmazonCalcPriceRule> priceRules = entry.getValue();
            if (priceRules.size() == 1) {
                continue;
            }
            priceRules = priceRules.stream()
                    .sorted(Comparator.comparing(AmazonCalcPriceRule::getFromPrice, Comparator.nullsFirst(Double::compareTo)))
                    .collect(Collectors.toList());

            // 将价格由小到大放入集合
            List<Double> priceList = new ArrayList<>();
            for (AmazonCalcPriceRule priceRule : priceRules) {
                priceList.add(priceRule.getFromPrice());
                priceList.add(priceRule.getToPrice());
            }

            // 判断价格区间是否正常
            for (int i=0; i<priceList.size()-2; i++) {
                Double prevPrice = priceList.get(i);
                Double nextPrice = priceList.get(i + 1);
                if((i == 0 && null == prevPrice) || (i == priceList.size()-2 && null == nextPrice)) {
                    continue;
                }

                // 如果在中间出现空值
                if(null == prevPrice || null == nextPrice) {
                    return false;
                }

                // 比较前一个值和后一个值的大小
                if (i % 2 == 0) {
                    if (prevPrice >= nextPrice) {
                        return false;
                    }
                } else {
                    if (prevPrice > nextPrice) {
                        return false;
                    }
                }
            }
        }
        return true;
    }


    /**
     * 获取Amazon套账EAN前缀列表
     *
     * @param merchantId 商户ID
     */
    public static List<String> getMerchantIdEanPrefixList(String merchantId) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        request.setMerchantId(merchantId);
        List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, "accountNumber", "merchantId", "colStr6");
        if (CollectionUtils.isEmpty(saleAccountList)) {
            log.error("获取商户信息失败，merchantId：{}", merchantId);
            return Collections.emptyList();
        }
        return saleAccountList.stream()
                .map(SaleAccount::getColStr6)
                .filter(StringUtils::isNotBlank)
                .map(eanPrefix -> CommonUtils.splitList(eanPrefix, ","))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
