package com.estone.erp.publish.amazon.call.jsonfeed;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class FeedsResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * seller_id
     */
    private String sellerId;

    /**
     * marketplace_id
     */
    private String marketplaceId;

    /**
     * doc_id
     */
    private String docId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * amazon上传feed数据编号message_id
     */
    private Integer messageId;

    /**
     * 上传类型
     */
    private String feedType;

    /**
     * 系统自定义上传类型
     */
    private String systemFeedType;

    /**
     * 亚马逊任务ID
     */
    private String feedId;

    /**
     * amazon上传feed报告结果状态码status_code
     */
    private String statusCode;

    /**
     * 接口返回信息（有错误时存的是json<list>）
     */
    private String errorMsg;

    /**
     * 请求时间
     */
    private Date requestResultTime;

    /**
     * 完成状态（完成、未完成）
     */
    private Boolean finish;

    /**
     * 接口处理状态
     */
    private Boolean status;

    /**
     * 完成时间
     */
    private Date successTime;

    /**
     * docId,status,errorMsg
     */
    private List<DocIdFeedsResult> docIdFeedsResultList;

    @Data
    public static class DocIdFeedsResult {
        private String docId;
        private Boolean status;
        private String errorMsg;

    }
}