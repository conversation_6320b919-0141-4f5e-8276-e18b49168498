package com.estone.erp.publish.amazon.util.model;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.model.NameValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AmazonSku {

    /**
     * 系统sku
     */
    private String sku;

    /**
     * 平台sku（按sku生成规则生成的sku，生成后不可更改）
     */
    private String sellerSKU;

    /**
     * 产品Id类型
     */
    private String standardProdcutIdType;

    /**
     * 产品Id值
     */
    private String standardProdcutIdValue;

    /**
     * 主图
     */
    private String mainImage;

    /**
     * GSPR图
     */
    private String gpsrImage;

    /**
     * 样品图
     */
    private String sampleImage;

    /**
     * 附图
     */
    private String extraImages;

    private List<String> extraImagesList;

    private String condition;

    private String conditionNote;

    /**
     * 价格
     */
    private Double standardPrice;

    /**
     * 促销价
     */
    private Double salePrice;

    /**
     * 促销开始时间
     */
    private Timestamp saleStartDate;

    /**
     * 促销结束时间
     */
    private Timestamp saleEndDate;

    /**
     * 总价 = 价格+运费
     */
    private Double totalPrice;

    /**
     * 促销总价 = 促销价+运费
     */
    private Double totalSalePrice;

    /**
     * 运费
     */
    private Double shippingCost;

    /**
     * 运费模板
     */
    private String shippingGroup;

    /**
     * 数量
     */
    private Integer quantity;

    private String extraData;

    /**
     * 属性集合
     */
    private List<NameValue> nameValues;

    /**
     * JSON格式的属性集合
     */
    private Map<String, Object> variantAttribute;

    /**
     * 合并属性的名称
     */
    private String name;

    private Boolean stepTemplateStatus;

    private String skulifecyclephase;

    /**
     * 重复子sku标识
     */
    private Boolean repeatFlag;

    public String getSku() {
        return sku == null ? sku : sku.trim();
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSellerSKU() {
        return sellerSKU;
    }

    public void setSellerSKU(String sellerSKU) {
        this.sellerSKU = sellerSKU;
    }

    public String getStandardProdcutIdType() {
        return standardProdcutIdType;
    }

    public void setStandardProdcutIdType(String standardProdcutIdType) {
        this.standardProdcutIdType = standardProdcutIdType;
    }

    public String getStandardProdcutIdValue() {
        return standardProdcutIdValue;
    }

    public void setStandardProdcutIdValue(String standardProdcutIdValue) {
        this.standardProdcutIdValue = standardProdcutIdValue;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public String getSampleImage() {
        return sampleImage;
    }

    public void setSampleImage(String sampleImage) {
        this.sampleImage = sampleImage;
    }

    public String getExtraImages() {
        return extraImages;
    }

    public void setExtraImages(String extraImages) {
        this.extraImages = extraImages;
    }

    public List<String> getExtraImagesList() {
        if (CollectionUtils.isNotEmpty(extraImagesList)) {
            return extraImagesList;
        }

        if (StringUtils.isNotBlank(extraImages)) {
            try {
                extraImagesList = JSON.parseArray(extraImages, String.class);
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
        else {
            extraImagesList = new ArrayList<String>(0);
        }

        return extraImagesList;
    }

    public void setExtraImagesList(List<String> extraImagesList) {
        this.extraImagesList = extraImagesList;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getConditionNote() {
        return conditionNote;
    }

    public void setConditionNote(String conditionNote) {
        this.conditionNote = conditionNote;
    }

    public Double getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(Double standardPrice) {
        this.standardPrice = standardPrice;
    }

    public Double getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(Double salePrice) {
        this.salePrice = salePrice;
    }

    public Timestamp getSaleStartDate() {
        return saleStartDate;
    }

    public void setSaleStartDate(Timestamp saleStartDate) {
        this.saleStartDate = saleStartDate;
    }

    public Timestamp getSaleEndDate() {
        return saleEndDate;
    }

    public void setSaleEndDate(Timestamp saleEndDate) {
        this.saleEndDate = saleEndDate;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Double getTotalSalePrice() {
        return totalSalePrice;
    }

    public void setTotalSalePrice(Double totalSalePrice) {
        this.totalSalePrice = totalSalePrice;
    }

    public Double getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(Double shippingCost) {
        this.shippingCost = shippingCost;
    }

    public String getShippingGroup() {
        return shippingGroup;
    }

    public void setShippingGroup(String shippingGroup) {
        this.shippingGroup = shippingGroup;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public List<NameValue> getNameValues() {
        return nameValues;
    }

    public void setNameValues(List<NameValue> nameValues) {
        this.nameValues = nameValues;
    }

    public String getName() {
        if (StringUtils.isEmpty(name)) {
            if (CollectionUtils.isNotEmpty(nameValues)) {
                name = String.join("", nameValues.stream().map(nameValue -> {
                    return nameValue.getDesc();
                }).collect(Collectors.toList()));
            }
        }

        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameValuesJson() {
        if (CollectionUtils.isNotEmpty(nameValues)) {
            return JSON.toJSONString(nameValues);
        }
        return null;
    }

    public Boolean getStepTemplateStatus() {
        return stepTemplateStatus;
    }

    public void setStepTemplateStatus(Boolean stepTemplateStatus) {
        this.stepTemplateStatus = stepTemplateStatus;
    }

    public String getSkulifecyclephase() {
        return skulifecyclephase;
    }

    public void setSkulifecyclephase(String skulifecyclephase) {
        this.skulifecyclephase = skulifecyclephase;
    }

    public Boolean getRepeatFlag() {
        return repeatFlag;
    }

    public void setRepeatFlag(Boolean repeatFlag) {
        this.repeatFlag = repeatFlag;
    }

    public Map<String, Object> getVariantAttribute() {
        return variantAttribute;
    }

    public void setVariantAttribute(Map<String, Object> variantAttribute) {
        this.variantAttribute = variantAttribute;
    }

    public String getGpsrImage() {
        return gpsrImage;
    }

    public void setGpsrImage(String gpsrImage) {
        this.gpsrImage = gpsrImage;
    }
}
