package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.mq.model.ProductSpSkuMSg;
import com.estone.erp.publish.amazon.mq.model.ProductSpSonSku;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监听产品系统推送试卖sku转正常维护的系统sku
 * <AUTHOR>
 *
 */
@Slf4j
public class ProductSystemSkuStatusNormalMqListener implements ChannelAwareMessageListener {

    @Resource
    private EsSkuBindService esSkuBindService;


    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }
        //log.info("ProductSystemSkuStatusNormalMqListener message body -> {}", body);

        Boolean isSuccess = doService(body);

        if(isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        Boolean isSuccess = false;
        ProductSpSkuMSg productSpSkuMSg = null;
        try {
            productSpSkuMSg = JSON.parseObject(body,ProductSpSkuMSg.class);
            if (null == productSpSkuMSg || StringUtils.isEmpty(productSpSkuMSg.getMainSku())
                    || CollectionUtils.isEmpty(productSpSkuMSg.getSkuList())) {
                return false;
            }
        }catch(Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
        }
        try {
            String mainSku = productSpSkuMSg.getMainSku();

            List<ProductSpSonSku> productSpSkuMSgSkuList=  productSpSkuMSg.getSkuList();
            Map<String, String> saleSkuAndSonSkuMap = productSpSkuMSgSkuList.stream()
                    .filter(o->StringUtils.isNotBlank(o.getSaleSku()))
                    .collect(Collectors.toMap(o -> o.getSaleSku(), o ->o.getSonSku(), (k1,k2)->k1));

            EsSkuBindRequest request = new EsSkuBindRequest();
            request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
            request.setPlatform(Platform.Amazon.name());
            request.setSkus(new ArrayList<>(saleSkuAndSonSkuMap.keySet()));
            List<EsSkuBind> esSkuBinds = esSkuBindService.getEsSkuBinds(request);
            if(CollectionUtils.isNotEmpty(esSkuBinds)) {
                for (EsSkuBind esSkuBind : esSkuBinds) {
                    String sku = esSkuBind.getSku();
                    String sonSku = saleSkuAndSonSkuMap.get(sku);
                    esSkuBind.setSystemSku(sonSku);
                    esSkuBind.setMainSku(mainSku);
                }
                esSkuBindService.saveAll(esSkuBinds, Platform.Amazon.name());

                RabbitMqSender rabbitMqSender = SpringUtils.getBean(RabbitMqSender.class);
                rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                        PublishMqConfig.PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE, JSON.toJSON(esSkuBinds));
            }
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }
        return isSuccess;
    }
}