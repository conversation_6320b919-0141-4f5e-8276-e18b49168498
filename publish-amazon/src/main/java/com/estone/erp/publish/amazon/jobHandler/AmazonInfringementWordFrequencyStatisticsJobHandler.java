package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyLogMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyLogService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 词频统计任务
 */
@Slf4j
@Component
public class AmazonInfringementWordFrequencyStatisticsJobHandler extends AbstractJobHandler {

    @Resource
    private AmazonInfringementWordFrequencyLogService amazonInfringementWordFrequencyLogService;

    @Resource
    private AmazonInfringementWordFrequencyStatisticsService amazonInfringementWordFrequencyStatisticsService;

    @Resource
    private AmazonInfringementWordFrequencyLogMapper amazonInfringementWordFrequencyLogMapper;

    public AmazonInfringementWordFrequencyStatisticsJobHandler() {
        super(AmazonInfringementWordFrequencyStatisticsJobHandler.class.getName());
    }

    @Data
    public static class InnerParam {
        private Integer hours = 0;

        private String startTime;

        private String endTime;
    }

    @XxlJob("amazonInfringementWordFrequencyStatisticsJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        AmazonInfringementWordFrequencyStatisticsJobHandler.InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, AmazonInfringementWordFrequencyStatisticsJobHandler.InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new AmazonInfringementWordFrequencyStatisticsJobHandler.InnerParam();
        }
        Date time = new Date();
        Integer hour = innerParam.getHours().intValue() <= 0 ? 8 : innerParam.getHours();
        Date beforeHourDateTime = com.estone.erp.publish.common.util.DateUtils.getBeforeHourDate(time,hour);
        String endTime = StringUtils.isBlank(innerParam.getEndTime())? DateUtils.format(time, DateUtils.STANDARD_DATE_PATTERN):innerParam.getEndTime();
        String startTime = StringUtils.isBlank(innerParam.getStartTime())? DateUtils.format(beforeHourDateTime, DateUtils.STANDARD_DATE_PATTERN):innerParam.getStartTime();

        AmazonInfringementWordFrequencyLogExample frequencyLogExample = new AmazonInfringementWordFrequencyLogExample();
        frequencyLogExample.createCriteria().andCreateTimeLessThanOrEqualTo(Timestamp.valueOf(endTime)).andCreateTimeGreaterThanOrEqualTo(Timestamp.valueOf(startTime));
        // 计算耗时
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<TidbPageMeta<Long>> tidbPageMetaList = amazonInfringementWordFrequencyLogService.getPageMetaListByExample(frequencyLogExample);
        // 查询词频日志

        long count = 0L;
        for (TidbPageMeta<Long> tidbPageMeta : tidbPageMetaList){
            List<Long> deleteIds = new ArrayList<>();
            // 查询词频日志
            AmazonInfringementWordFrequencyLogExample logExample = new AmazonInfringementWordFrequencyLogExample();
            logExample.createCriteria().andIdGreaterThanOrEqualTo(tidbPageMeta.getStartKey()).andIdLessThanOrEqualTo(tidbPageMeta.getEndKey()).andCreateTimeLessThanOrEqualTo(Timestamp.valueOf(endTime)).andCreateTimeGreaterThanOrEqualTo(Timestamp.valueOf(startTime));
            List<AmazonInfringementWordFrequencyLog> frequencyLogList = amazonInfringementWordFrequencyLogService.selectByExample(logExample);
            if (CollectionUtils.isEmpty(frequencyLogList)) {
                continue;
            }
            count +=  tidbPageMeta.getPageSize();
            // 保存词频流水
            deleteIds.addAll(frequencyLogList.stream().map(AmazonInfringementWordFrequencyLog::getId).collect(Collectors.toList()));
            amazonInfringementWordFrequencyStatisticsService.saveOrUpdateQuantity(frequencyLogList);
            amazonInfringementWordFrequencyLogMapper.deleteByPrimaryKey(deleteIds);
        }

       /* int currentPage = 0;
        int pageSize = 200;
        while (true) {
            // 查询词频日志
            AmazonInfringementWordFrequencyLogExample frequencyLogExample = new AmazonInfringementWordFrequencyLogExample();
            frequencyLogExample.createCriteria().andCreateTimeLessThan(timestamp);
            frequencyLogExample.setLimit(pageSize);
            frequencyLogExample.setOffset((currentPage++) * pageSize);
            List<AmazonInfringementWordFrequencyLog> frequencyLogList = amazonInfringementWordFrequencyLogService.selectByExample(frequencyLogExample);
            if (CollectionUtils.isEmpty(frequencyLogList)) {
                break;
            }
            // 保存词频流水
            deleteIds.addAll(frequencyLogList.stream().map(AmazonInfringementWordFrequencyLog::getId).collect(Collectors.toList()));
            amazonInfringementWordFrequencyStatisticsService.saveOrUpdateQuantity(frequencyLogList);

        }

        // 删除词频日志
        List<List<Long>> partition = Lists.partition(deleteIds, 200);
        partition.forEach(ids -> AmazonExecutors.executeInfringementWordFrequencyBatchUpdateTask(() -> {
            amazonInfringementWordFrequencyLogMapper.deleteByPrimaryKey(ids);
        }));*/
        stopWatch.stop();
        XxlJobLogger.log("词频统计任务结束，统计日志数量为：{}，任务耗时：{}s", count, stopWatch.getTotalTimeMillis() / 1000);
        return ReturnT.SUCCESS;
    }
}
