package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.common.util.upload.SeaweedFSUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.process.product.BaseReportTypeWithCsvStrategyFactory;
import com.estone.erp.publish.amazon.call.process.product.newreport.BaseInventoryReportTypeWithCsvStrategyFactory;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.BaseInventoryReportSpTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy.BaseSpReportTypeWithCsvStrategy;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import com.estone.erp.publish.amazon.call.xsd.parse.BrowseTreeReportParser;
import com.estone.erp.publish.amazon.componet.publish.domain.*;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.AppNameEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.param.service.SystemParamService;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.StringUtil;
import io.swagger.client.enums.IdentifiersTypeEnum;
import io.swagger.client.model.*;
import io.swagger.client.model.catalogItems.ItemIdentifiers;
import io.swagger.client.model.catalogItems.ItemImage;
import io.swagger.client.model.catalogItems.ItemProductTypeByMarketplace;
import io.swagger.client.model.catalogItems.ItemProductTypes;
import io.swagger.client.model.catalogItems.ItemRelationship;
import io.swagger.client.model.catalogItems.ItemRelationships;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import io.swagger.client.model.catalogItems.ItemSummaries;
import io.swagger.client.model.catalogItems.ItemSummaryByMarketplace;
import io.swagger.client.model.catalogItems.*;
import io.swagger.client.model.listings.Item;
import io.swagger.client.model.listings.*;
import io.swagger.client.response.AmazonReportsDownloadProgress;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/8/11
 */
@Slf4j
public class AmazonSpLocalUtils {

    public static  List<String> removeAccountNumberList = new ArrayList<>();
    public static Long staticRemoveAccountNumberExpirationTime = 0L;

    public static Boolean checkAmazonSpAccountMsgError( AmazonSpAccount amazonSpAccount){
        if (null == amazonSpAccount
                ||(StringUtils.isNotBlank(amazonSpAccount.getAccountStatus()) && !amazonSpAccount.getAccountStatus().equals(SaleAccountStastusEnum.NORMAL.getCode()))
                || StringUtils.isBlank(amazonSpAccount.getAccountNumber())
                || StringUtils.isBlank(amazonSpAccount.getMarketplaceId())
                || StringUtil.isEmpty(amazonSpAccount.getMerchantId())
                || StringUtil.isEmpty(amazonSpAccount.getAppName())
                || (!AppNameEnum.TONGTU_APP_NAME.getAppName().equalsIgnoreCase(amazonSpAccount.getAppName()) && StringUtil.isEmpty(amazonSpAccount.getRefreshToken()))
                || (AppNameEnum.TONGTU_APP_NAME.getAppName().equalsIgnoreCase(amazonSpAccount.getAppName()) && (StringUtils.isBlank(amazonSpAccount.getTongTuAuthAccountId()) || StringUtils.isNotBlank(amazonSpAccount.getRefreshToken())))
                || getCatchPublishRemoveAccountNumberList().contains(amazonSpAccount.getAccountNumber())) {
           return true;
        }
        return false;
    }

    public static Boolean checkAmazonAccountMsgError(AmazonAccount amazonSpAccount) {
        if (null == amazonSpAccount
                ||(StringUtils.isNotBlank(amazonSpAccount.getAccountStatus()) && !amazonSpAccount.getAccountStatus().equals(SaleAccountStastusEnum.NORMAL.getCode()))
                || StringUtils.isBlank(amazonSpAccount.getAccountNumber())
                || StringUtils.isBlank(amazonSpAccount.getMarketplaceId())
                || StringUtils.isBlank(amazonSpAccount.getMerchantId())
                || StringUtils.isBlank(amazonSpAccount.getAppName())
                || (!AppNameEnum.TONGTU_APP_NAME.getAppName().equalsIgnoreCase(amazonSpAccount.getAppName()) && StringUtils.isBlank(amazonSpAccount.getRefreshToken()))
                || (AppNameEnum.TONGTU_APP_NAME.getAppName().equalsIgnoreCase(amazonSpAccount.getAppName()) && (StringUtils.isBlank(amazonSpAccount.getAccessToken()) || StringUtils.isNotBlank(amazonSpAccount.getRefreshToken())))
                || getCatchPublishRemoveAccountNumberList().contains(amazonSpAccount.getAccountNumber())) {
            return true;
        }
        return false;
    }

    /**
     * 查询排除店铺
     * @return
     */
    private static List<String> getPublishRemoveAccountNumberList() {
        List<String> queryList = null;
        try {
            String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "publish_remove_account_number_operate", 30);
            List<String> selfRegisteredSkuList = CommonUtils.splitList(systemParamValue, ",");
            queryList = selfRegisteredSkuList;
        }catch (Exception e){
            log.error("从redis获取缓存失败", e);
        }
        if (CollectionUtils.isEmpty(queryList)){
            // 未查询到暂时返回空集合
            queryList = new ArrayList<>();
        }
        return queryList;
    }

    public static List<String> getCatchPublishRemoveAccountNumberList() {
        // 当前时间
        Long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtils.isEmpty(removeAccountNumberList) || currentTimeMillis > staticRemoveAccountNumberExpirationTime) {
            synchronized (staticRemoveAccountNumberExpirationTime) {
                if(CollectionUtils.isEmpty(removeAccountNumberList) || currentTimeMillis > staticRemoveAccountNumberExpirationTime) {
                    removeAccountNumberList = getPublishRemoveAccountNumberList();
                    // 30分钟后过期
                    staticRemoveAccountNumberExpirationTime = currentTimeMillis + 60 * 60 * 1000;
                }
            }
        }
        return removeAccountNumberList;
    }

    /**
     * 转换 AmazonSpAccount
     *
     * @param account
     * @return
     */
    public static AmazonSpAccount ToAmazonSpAccount(SaleAccount account) {
        AmazonSpAccount amazonSpAccount = new AmazonSpAccount();
        if (null == account) {
            return amazonSpAccount;
        }

        amazonSpAccount.setAccountNumber(account.getAccountNumber());
        amazonSpAccount.setMarketplaceId(account.getMarketplaceId());
        amazonSpAccount.setRefreshToken(account.getRefreshToken());
        amazonSpAccount.setMerchantId(account.getMerchantId());
        amazonSpAccount.setAppName(account.getErpAppName());
//        amazonSpAccount.setServiceUrl(account.getServiceUrl());
        amazonSpAccount.setAppVersion(account.getColStr1());
        amazonSpAccount.setTongTuAuthAccountId(account.getAccessToken());
        amazonSpAccount.setAccountStatus(account.getAccountStatus());
        return amazonSpAccount;
    }

    /**
     * 转换 AmazonSpAccount
     *
     * @param account
     * @return
     */
    public static AmazonSpAccount ToAmazonSpAccount(SaleAccountAndBusinessResponse account) {
        AmazonSpAccount amazonSpAccount = new AmazonSpAccount();
        if (null == account) {
            return amazonSpAccount;
        }

        amazonSpAccount.setAccountNumber(account.getAccountNumber());
        amazonSpAccount.setMarketplaceId(account.getMarketplaceId());
        amazonSpAccount.setRefreshToken(account.getRefreshToken());
        amazonSpAccount.setMerchantId(account.getMerchantId());
        amazonSpAccount.setAppName(account.getErpAppName());
//        amazonSpAccount.setServiceUrl(account.getServiceUrl());
        amazonSpAccount.setAppVersion(account.getColStr1());
        amazonSpAccount.setTongTuAuthAccountId(account.getAccessToken());
        amazonSpAccount.setAccountStatus(account.getAccountStatus());
        return amazonSpAccount;
    }

    /**
     * 转换 AmazonSpAccount
     *
     * @param account
     * @return
     */
    public static AmazonSpAccount ToAmazonSpAccount(AmazonAccount account) {
        AmazonSpAccount amazonSpAccount = new AmazonSpAccount();
        if (null == account) {
            return amazonSpAccount;
        }

        amazonSpAccount.setAccountNumber(account.getAccountNumber());
        amazonSpAccount.setMarketplaceId(account.getMarketplaceId());
        amazonSpAccount.setRefreshToken(account.getRefreshToken());
        amazonSpAccount.setMerchantId(account.getMerchantId());
        amazonSpAccount.setAppName(account.getAppName());
        amazonSpAccount.setServiceUrl(account.getServiceurl());
        amazonSpAccount.setAppVersion(account.getAppVersion());
        amazonSpAccount.setTongTuAuthAccountId(account.getAccessToken());
        amazonSpAccount.setAccountStatus(account.getAccountStatus());
        return amazonSpAccount;
    }

    /**
     * 处理Listing报告文件
     *
     * @param progress
     */
    public static SyncSpProductData handleListingReportFile(AmazonReportsDownloadProgress progress) throws Exception {

        try {
            ApiResult<File> downloadReportApi = AmazonSpLocalUtils.downloadReportByProgress(progress);
            if (null == downloadReportApi || !downloadReportApi.isSuccess()) {
                throw new Exception("下载文件异常" + downloadReportApi.getErrorMsg());
            }

            // 获取账号对象
            String accountNumber = progress.getAccountNumber();
            AmazonAccount account = SpringUtils.getBean(AmazonAccountService.class).queryAmazonAccountByAccountNumber(accountNumber);
            if (null == account) {
                throw new Exception("获取账号失败accountNumber:" + accountNumber);
            }

            // 根据类型 站点获取对应的CSV解析类
            String reportType = progress.getReportType();
            BaseSpReportTypeWithCsvStrategy spReportCsvStrategy = BaseReportTypeWithCsvStrategyFactory.newInstance(account, reportType);
            if (null == spReportCsvStrategy) {
                throw new Exception("该站点报告类型未配置文件解析规则accountNumber:" + accountNumber + "，reportType:" + reportType);
            }

            // 解析csv文件
            File file = downloadReportApi.getResult();
            List<String> lines = FileUtils.readLines(file, AmazonSpLocalUtils.getCsvCharset(progress.getMarketplaceId()));
            List<String[]> lineSplits = spReportCsvStrategy.filterAndTransferLines(lines, null);

            // 根据csv解析数据构建产品数据
            spReportCsvStrategy.constructSyncProductData(lineSplits);
            SyncSpProductData syncSpProductData = spReportCsvStrategy.getSyncSpProductData();

            return syncSpProductData;
        } catch (Exception e) {
            log.error("发生异常 ：" + e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 处理Category报告文件
     *
     * @param progress
     */
    public static BrowseTreeReport handleCategoryReportFile(AmazonReportsDownloadProgress progress) throws Exception {
        try {
            ApiResult<File> downloadReportApi = AmazonSpLocalUtils.downloadReportByProgress(progress);
            if (null == downloadReportApi || !downloadReportApi.isSuccess()) {
                throw new Exception("下载文件异常" + downloadReportApi.getErrorMsg());
            }

            File file = downloadReportApi.getResult();
            if (!file.isFile() || !file.exists()) {
                throw new Exception("从fms文件系统服务器下载文件异常");
            }

            // 解析文件
            BrowseTreeReport report = BrowseTreeReportParser.parseReport(FileUtils.readFileToString(file));
            if (report == null) {
                throw new Exception("解析产品分类文件失败");
            }

            return report;
        } catch (Exception e) {
            log.error("发生异常 ：" + e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 处理Listing报告文件
     *
     * @param progress
     */
    public static SyncSpProductData handleListingInventoryReportFile(AmazonReportsDownloadProgress progress) throws Exception {

        try {
            ApiResult<File> downloadReportApi = AmazonSpLocalUtils.downloadReportByProgress(progress);
            if (null == downloadReportApi || !downloadReportApi.isSuccess()) {
                throw new Exception("下载文件异常" + downloadReportApi.getErrorMsg());
            }

            // 获取账号对象
            String accountNumber = progress.getAccountNumber();
            AmazonAccount account = SpringUtils.getBean(AmazonAccountService.class).queryAmazonAccountByAccountNumber(accountNumber);
            if (null == account) {
                throw new Exception("获取账号失败accountNumber:" + accountNumber);
            }

            // 根据类型 站点获取对应的CSV解析类
            String reportType = progress.getReportType();
            BaseInventoryReportSpTypeWithCsvStrategy baseInventoryReportSpTypeWithCsvStrategy = BaseInventoryReportTypeWithCsvStrategyFactory.newInstance(account, reportType);
            if (null == baseInventoryReportSpTypeWithCsvStrategy) {
                throw new Exception("该站点报告类型未配置文件解析规则accountNumber:" + accountNumber + "，reportType:" + reportType);
            }

            // 解析csv文件
            File file = downloadReportApi.getResult();
            List<String> lines = FileUtils.readLines(file, AmazonSpLocalUtils.getCsvCharset(progress.getMarketplaceId()));
            List<String[]> lineSplits = baseInventoryReportSpTypeWithCsvStrategy.filterAndTransferLines(lines, null);

            // 根据csv解析数据构建产品数据
            baseInventoryReportSpTypeWithCsvStrategy.constructSyncProductData(lineSplits);
            SyncSpProductData syncSpProductData = baseInventoryReportSpTypeWithCsvStrategy.getSyncSpProductData();

            return syncSpProductData;
        } catch (Exception e) {
            log.error("发生异常 ：" + e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 下载文件
     *
     * @param progress
     * @return
     */
    public static ApiResult<File> downloadReportByProgress(AmazonReportsDownloadProgress progress) {
        try {
            // 从文件系统下载报告文件
            String relativePath = progress.getFileRelativePath().replaceAll("\\\\", "\\/");
            String suffix = progress.getFileName().substring(progress.getFileName().lastIndexOf("."));
            File file = File.createTempFile("amazon-sp-report-" + progress.getAccountNumber(), suffix);
            if (null == file) {
                log.info("文件生成失败" + file.getPath());
            }

            // 下载文件
            SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
            String fileParamUrl = systemParamService.querySystemParamByCodeKey("AMAZON.AMAZON_REPORT_URL_ROUTE").getParamValue();
            String fileUrl = fileParamUrl + relativePath;
            SeaweedFSUtils.downloadFile(fileUrl, new FileOutputStream(file));
            if (file == null || !file.isFile() || !file.exists() || file.length() == 0) {
                log.info(relativePath + "|" + progress.getFileName());
                log.info(file.isFile() + "|" + file.exists() + "|" + file.length());
                return ApiResult.newError("从fms文件系统下载文件异常");
            }

            return ApiResult.newSuccess(file);
        } catch (IOException e) {
            log.error("文件生成失败 ：" + e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        } catch (Exception e) {
            log.error("文件生成失败 ：" + e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 转 AmazonProductListing 集合
     * @param itemSearchResults
     * @param accountNumber
     * @param site
     * @return
     */
    public static List<AmazonProductListing> toAmazonProductListingDetailByItemSearchResults(ItemSearchResults itemSearchResults, String accountNumber, String site){
        List<AmazonProductListing> amazonProductListingList = new ArrayList<>();
        List<io.swagger.client.model.catalogItems.Item> categoryItemList = itemSearchResults.getItems();
        for (io.swagger.client.model.catalogItems.Item categoryItem : categoryItemList) {
            AmazonProductListing amazonProductListing = toAmazonProductListingDetail(categoryItem);
            if (null != amazonProductListing) {
                amazonProductListing.setAccountNumber(accountNumber);
                amazonProductListing.setSite(site);
                amazonProductListingList.add(amazonProductListing);
            }
        }
        return amazonProductListingList;
    }

    /**
     * 解析Item 至Listing详情
     *
     * @param item
     * @return
     */
    public static AmazonProductListing toAmazonProductListingDetail(io.swagger.client.model.catalogItems.Item item) {
        try {
            AmazonProductListing amazonProductListing = new AmazonProductListing();
            amazonProductListing.setSonAsin(item.getAsin());
            // identifiers
            ItemIdentifiers itemIdentifiers = item.getIdentifiers();
            if (CollectionUtils.isNotEmpty(itemIdentifiers) && null != itemIdentifiers.get(0)
                    && CollectionUtils.isNotEmpty(itemIdentifiers.get(0).getIdentifiers())) {
                List<ItemIdentifier> identifiers = itemIdentifiers.get(0).getIdentifiers();
                for (ItemIdentifier itemIdentifier : identifiers) {
                    if (itemIdentifier.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.UPC.getValue())) {
                        // 标识符类型 EAN UPC
                        amazonProductListing.setIdentifierType(itemIdentifier.getIdentifierType());
                        // 标识符
                        amazonProductListing.setIdentifier(itemIdentifier.getIdentifier());
                    } else if (itemIdentifier.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.EAN.getValue())
                            && (StringUtils.isNotBlank(amazonProductListing.getIdentifierType()) && !amazonProductListing.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.UPC.getValue()))) {
                        // 标识符类型 EAN UPC
                        amazonProductListing.setIdentifierType(itemIdentifier.getIdentifierType());
                        // 标识符
                        amazonProductListing.setIdentifier(itemIdentifier.getIdentifier());
                    } else if (itemIdentifier.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.SKU.getValue())) {
                        amazonProductListing.setSellerSku(itemIdentifier.getIdentifier());
                    }
                }
            }

            // images
            ItemImages itemImages = item.getImages();
            if (CollectionUtils.isNotEmpty(itemImages)) {
                List<ItemImage> itemImageList = itemImages.get(0).getImages();
                if (CollectionUtils.isNotEmpty(itemImageList)) {
                    // 主图
                    amazonProductListing.setMainImage(itemImageList.get(0).getLink());
                }
            }

            // productTypes
            ItemProductTypes itemProductTypes = item.getProductTypes();
            if (CollectionUtils.isNotEmpty(itemProductTypes)) {
                // 产品类型
                amazonProductListing.setProductType(itemProductTypes.get(0).getProductType());
            }

            // summaries
            ItemSummaries itemSummaries = item.getSummaries();
            if (CollectionUtils.isNotEmpty(itemSummaries)) {
                ItemSummaryByMarketplace itemSummaryByMarketplace = itemSummaries.get(0);
                String brandName = StringUtils.isBlank(itemSummaryByMarketplace.getBrand()) ? itemSummaryByMarketplace.getManufacturer() : itemSummaryByMarketplace.getBrand();
                amazonProductListing.setBrandName(brandName);
                // 平台节点Node
                amazonProductListing.setBrowseNode(null != itemSummaryByMarketplace.getBrowseClassification() ? itemSummaryByMarketplace.getBrowseClassification().getClassificationId() : null);
                // 颜色
                amazonProductListing.setColorName(itemSummaryByMarketplace.getColor());
                // 制造商
                amazonProductListing.setManufacturer(itemSummaryByMarketplace.getManufacturer());
                // 制造商零件编号
                amazonProductListing.setModelNumber(itemSummaryByMarketplace.getModelNumber());
                // 尺寸
                amazonProductListing.setSizeName(itemSummaryByMarketplace.getSize());
                // 样式
                amazonProductListing.setStyleName(itemSummaryByMarketplace.getStyle());
                amazonProductListing.setItemName(itemSummaryByMarketplace.getItemName());
            }
            //关系
            ItemRelationships itemRelationships = item.getRelationships();
            if (CollectionUtils.isNotEmpty(itemRelationships)) {
                List<ItemRelationship> itemRelationshipList = itemRelationships.get(0).getRelationships();
                if (CollectionUtils.isNotEmpty(itemRelationshipList)) {
                    List<String> parentAsins = itemRelationshipList.get(0).getParentAsins();
                    List<String> childAsins = itemRelationshipList.get(0).getChildAsins();
                    if (CollectionUtils.isNotEmpty(parentAsins)) {
                        //变体子体
                        amazonProductListing.setParentAsin(parentAsins.get(0));
                        amazonProductListing.setItemType(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode());
                    } else if (CollectionUtils.isNotEmpty(childAsins)) {
                        //变体主体  有子asin集合
                        amazonProductListing.setChildAsins(JSON.toJSONString(childAsins));
                        amazonProductListing.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
                    }
                    if (StringUtils.isNotBlank(item.getAsin())) {
                        amazonProductListing.setSonAsin(item.getAsin());
                    }
                } else {
                    // 单体：主子asin相同
                    String asin = item.getAsin();
                    amazonProductListing.setParentAsin(asin);
                    amazonProductListing.setItemType(AmazonListingitemtypeEnum.Monomer_Item.getStatusCode());
                }
            }
            return amazonProductListing;
        } catch (Exception e) {
            log.error("toAmazonProductListingDetail error,{}", e.getMessage(),e);
            return null;
        }
    }

    /**
     * 获取子ASIN集合
     *
     * @param itemSearchResults
     * @return
     */
    public static List<String> getSonAsinList(ItemSearchResults itemSearchResults) {
        try {
            List<String> sonAsinList = new ArrayList<>();

            List<io.swagger.client.model.catalogItems.Item> items = itemSearchResults.getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                io.swagger.client.model.catalogItems.Item categoryItem = items.get(0);
                String asin = categoryItem.getAsin();
                ItemRelationships relationships = categoryItem.getRelationships();
                Optional<ItemRelationships> optional = Optional.ofNullable(relationships);
                if (optional.isPresent()) {
                    List<ItemRelationship> itemRelationshipList = relationships.get(0).getRelationships();
                    if (CollectionUtils.isNotEmpty(itemRelationshipList)){
                        List<String> childAsins = itemRelationshipList.get(0).getChildAsins();
                        List<String> parentAsins = itemRelationshipList.get(0).getParentAsins();
                        if (CollectionUtils.isNotEmpty(childAsins) && CollectionUtils.isEmpty(parentAsins)) {
                            sonAsinList = childAsins;
                        }
                    }
                }
             // 此接口还有其余信息，根据传参来确定
                return sonAsinList;
            }
        } catch (Exception e) {
            log.error("getSonAsinList error,{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取listing运费
     * @param getPricingResponse
     * @return
     */
    public static Map<String, Double> getListingShippingCost(CompetitiveSummaryBatchResponse getPricingResponse) {
        Map<String, Double> shippingCostMap = new HashMap<>();
        try {
            CompetitiveSummaryResponseList priceList = getPricingResponse.getResponses();
            if(CollectionUtils.isEmpty(priceList)) {
                return shippingCostMap;
            }

            for (CompetitiveSummaryResponse price : priceList) {
                CompetitiveSummaryResponseBody product = price.getBody();
                HttpStatusLine status = price.getStatus();
                if(null == product || null == status|| !"Success".equalsIgnoreCase(status.getReasonPhrase())) {
                    log.error("getListingShippingCost , {}", JSON.toJSONString(price));
                    continue;
                }
                // 预测价格接口变动，需要重新对接新业务

               /* OffersList offersList = product.get();
                if(CollectionUtils.isEmpty(offersList)) {
                    continue;
                }

                for (OfferType offerType : offersList) {
                    String sellerSku = offerType.getSellerSKU();
                    PriceType priceType = offerType.getBuyingPrice();
                    if(StringUtils.isNotBlank(sellerSku) && null != priceType) {
                        MoneyType shippingMoneyType = priceType.getShipping();
                        if(null == shippingMoneyType) {
                            continue;
                        }
                        BigDecimal amount = shippingMoneyType.getAmount();
                        shippingCostMap.put(sellerSku, amount.doubleValue());
                    }
                }*/

            }
        } catch (Exception e) {
            log.error("getListingShippingCost error,{}", e.getMessage());
        }
        return shippingCostMap;
    }

    /**
     * 解析Item 至Listing详情
     *
     * @param item
     * @return
     */
    public static AmazonProductListing toAmazonProductListingDetailByItem(Item item) {
        String sellerSku = item.getSku();
        try {
            AmazonProductListing amazonProductListing = new AmazonProductListing();
            //sellersku
            amazonProductListing.setSellerSku(sellerSku);
            // summaries
            io.swagger.client.model.listings.ItemSummaries itemSummaries = item.getSummaries();
            if (CollectionUtils.isNotEmpty(itemSummaries)) {
                io.swagger.client.model.listings.ItemSummaryByMarketplace itemSummaryByMarketplace = itemSummaries.get(0);
                //子asin
                amazonProductListing.setSonAsin(itemSummaryByMarketplace.getAsin());
                //ProductType
                amazonProductListing.setProductType(itemSummaryByMarketplace.getProductType());
                //ConditionType
                amazonProductListing.setConditionType(null != itemSummaryByMarketplace.getConditionType() ? itemSummaryByMarketplace.getConditionType().getValue() : null);
                // status
                if (CollectionUtils.isNotEmpty(itemSummaryByMarketplace.getStatus())) {
                    List<String> statusList = itemSummaryByMarketplace.getStatus().stream().map(o -> o.getValue()).collect(Collectors.toList());
                    amazonProductListing.setItemSummariesStastus(StrUtil.strAddComma(StringUtils.join(statusList,",")));
                }
                amazonProductListing.setItemName(itemSummaryByMarketplace.getItemName());
                amazonProductListing.setMainImage(null != itemSummaryByMarketplace.getMainImage() ? itemSummaryByMarketplace.getMainImage().getLink() : null);
                //此处有平台的创建时间和修改时间，后续可根据业务处理格式

            }
            Map<String,List<Map<String,Object>>> attributes = item.getAttributes();
            if (null != attributes){
                // website_shipping_weight
                // item_type_name
                // is_expiration_dated_product
                //is_heat_sensitive
                //country_of_origin
                //gift_options
                //product_tax_code
                //item_type_keyword
                //product_site_launch_date
                // part_number
                // unit_count
                //五点描述
                List<Map<String, Object>> bulletPointList = attributes.get(ListingBulletPointData.bullet_point);
                if (CollectionUtils.isNotEmpty(bulletPointList)){
                    List<Object> bulletPoints = bulletPointList.stream().map(o -> o.get(ListingFiledConstants.VALUE)).collect(Collectors.toList());
                    amazonProductListing.setBulletPoint(JSON.toJSONString(bulletPoints));
                }
                //关键词
                List<Map<String, Object>> genericKeywordList = attributes.get(ListingProductData.generic_keyword);
                if (CollectionUtils.isNotEmpty(genericKeywordList)){
                    String searchTerms = genericKeywordList.get(0).get(ListingFiledConstants.VALUE).toString();
                    amazonProductListing.setSearchTerms(searchTerms);
                }
                //品牌
                List<Map<String, Object>> brandList = attributes.get(ListingProductData.brand);
                if (CollectionUtils.isNotEmpty(brandList)){
                    String brand = (String) brandList.get(0).get(ListingFiledConstants.VALUE);
                    amazonProductListing.setBrandName(brand);
                }
                // variation_theme
                //color
                List<Map<String, Object>> colorList = attributes.get(ListingFiledConstants.COLOR);
                if (CollectionUtils.isNotEmpty(colorList)){
                    String colorName = (String) colorList.get(0).get(ListingFiledConstants.VALUE);
                    amazonProductListing.setColorName(colorName);
                }
                List<Map<String, Object>> manufacturerList = attributes.get(ListingProductData.manufacturer);
                if (CollectionUtils.isNotEmpty(manufacturerList)){
                    String manufacturerName = (String) manufacturerList.get(0).get(ListingFiledConstants.VALUE);
                    amazonProductListing.setManufacturer(manufacturerName);
                }
                List<Map<String, Object>> productDescriptionList = attributes.get(ListingProductData.product_description);
                if (CollectionUtils.isNotEmpty(productDescriptionList)){
                    String productDescription = (String) productDescriptionList.get(0).get(ListingFiledConstants.VALUE);
                    amazonProductListing.setItemDescription(productDescription);
                }

                List<Map<String, Object>> itemNameList = attributes.get(ListingFiledConstants.ITEM_NAME);
                if (CollectionUtils.isNotEmpty(itemNameList)){
                    String itemName = (String) itemNameList.get(0).get(ListingFiledConstants.VALUE);
                    amazonProductListing.setItemName(itemName);
                }

                // 库存相关
                List<Map<String, Object>> quentityList = attributes.get(ListingProductData.fulfillment_availability);
                if (CollectionUtils.isNotEmpty(quentityList)){
                    Double quantity = (Double) quentityList.get(0).get(ListingQuantityData.quantity);
                    amazonProductListing.setQuantity(null != quantity?quantity.intValue():null);
                    try {
                        //  处理天数
                        if (null != quentityList.get(0).get("lead_time_to_ship_max_days")) {
                            Double lead_time_to_ship_max_days = (Double) quentityList.get(0).get("lead_time_to_ship_max_days");
                            amazonProductListing.setFulfillmentLatency(lead_time_to_ship_max_days.intValue());
                        }
                    } catch (Exception e) {
                        log.error("处理天数数据异常：" + JSON.toJSONString(quentityList));
                    }
                }

                //EAN 码
                List<Map<String, Object>> externallyProductIdentifierList = attributes.get(ListingProductData.externally_assigned_product_identifier);
                if (CollectionUtils.isNotEmpty(externallyProductIdentifierList)){
                    String identifierValue = (String) externallyProductIdentifierList.get(0).get(ListingFiledConstants.VALUE);
                    String identifierType = (String) externallyProductIdentifierList.get(0).get(ListingFiledConstants.TYPE);
                    amazonProductListing.setIdentifier(identifierValue);
                    amazonProductListing.setIdentifierType(identifierType);
                }

            }
            ItemIssues itemIssues = item.getIssues();
            if (CollectionUtils.isNotEmpty(itemIssues)) {
                Issue issue = itemIssues.get(0);
                amazonProductListing.setAttribute6(JSON.toJSONString(issue));
                amazonProductListing.setIssuesSeverity(issue.getSeverity().getValue());
            }
            // identifiers
            List<FulfillmentAvailability> fulfillmentAvailabilities = item.getFulfillmentAvailability();
            if (CollectionUtils.isNotEmpty(fulfillmentAvailabilities)) {
                FulfillmentAvailability fulfillmentAvailability = fulfillmentAvailabilities.get(0);
                if (null != fulfillmentAvailability) {
                    amazonProductListing.setQuantity(fulfillmentAvailability.getQuantity());
                }
            }

            //offers 此处暂不处理，价格变动会涉及总价的更改，需要对比原始价格，通过同步报告处理
            return amazonProductListing;
        } catch (Exception e) {
            log.error("toAmazonProductListingDetailByItem error,{}", e.getMessage(),sellerSku);
            return null;
        }
    }

    /**
     * listing.item 转换 listing编辑对象
     *
     * @param item listing.item
     */
    public static AmazonTemplateBO toEditListingItemDetailByItem(io.swagger.client.model.listings.Item item) {
        AmazonTemplateBO amazonEditListingDO = new AmazonTemplateBO();
        amazonEditListingDO.setSellerSku(item.getSku());
        amazonEditListingDO.setIsLock(false);
        amazonEditListingDO.setSaleVariant(false);

        io.swagger.client.model.listings.ItemSummaries summaries = item.getSummaries();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(summaries) && summaries.get(0) != null) {
            io.swagger.client.model.listings.ItemSummaryByMarketplace summary = summaries.get(0);
            amazonEditListingDO.setProductType(summary.getProductType());
            amazonEditListingDO.setCondition(summary.getConditionType().getValue());
            amazonEditListingDO.setTitle(summary.getItemName());
            amazonEditListingDO.setMainImage(summary.getMainImage().getLink());
        }

        ItemOffers offers = item.getOffers();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(offers) && offers.get(0) != null) {
            ItemOfferByMarketplace itemOffer = offers.get(0);
            amazonEditListingDO.setStandardPrice(Double.parseDouble(itemOffer.getPrice().getAmount()));
            amazonEditListingDO.setCurrency(itemOffer.getPrice().getCurrencyCode());
        }

        List<FulfillmentAvailability> fulfillmentAvailabilityList = item.getFulfillmentAvailability();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fulfillmentAvailabilityList) && fulfillmentAvailabilityList.get(0) != null) {
            FulfillmentAvailability fulfillmentAvailability = fulfillmentAvailabilityList.get(0);
            amazonEditListingDO.setQuantity(fulfillmentAvailability.getQuantity());
        }
        // attributes
        setEditListingAttributes(amazonEditListingDO, JSON.toJSONString(item.getAttributes()));
        return amazonEditListingDO;
    }

    public static void setEditListingAttributes(AmazonTemplateBO amazonEditListingDO, String attributes) {
        if (StringUtils.isBlank(attributes)) {
            return;
        }

        Map<String, List<JSONObject>> attributeJsonMap = JSON.parseObject(attributes, new TypeReference<>() {
        });
        if (MapUtils.isEmpty(attributeJsonMap)) {
            return;
        }

        List<JSONObject> descriptionList = attributeJsonMap.get(ListingProductData.product_description);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(descriptionList)) {
            amazonEditListingDO.setDescription(descriptionList.get(0).getString(ListingFiledConstants.VALUE));
            attributeJsonMap.remove(ListingProductData.product_description);
        }

        List<JSONObject> bulletPointList = attributeJsonMap.get(ListingBulletPointData.bullet_point);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bulletPointList)) {
            List<String> bulletPoints = bulletPointList.stream()
                    .map(jsonObject -> jsonObject.getString(ListingFiledConstants.VALUE))
                    .collect(Collectors.toList());
            amazonEditListingDO.setBulletPoint(JSON.toJSONString(bulletPoints));
            attributeJsonMap.remove(ListingBulletPointData.bullet_point);
        }

        List<JSONObject> keywordList = attributeJsonMap.get(ListingProductData.generic_keyword);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(keywordList)) {
            amazonEditListingDO.setSearchTerms(keywordList.get(0).getString(ListingFiledConstants.VALUE));
            attributeJsonMap.remove(ListingProductData.generic_keyword);
        }

        List<JSONObject> identifierList = attributeJsonMap.get(ListingProductData.externally_assigned_product_identifier);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(identifierList)) {
            String id = identifierList.get(0).getString(ListingFiledConstants.VALUE);
            String type = identifierList.get(0).getString(ListingFiledConstants.TYPE);
            amazonEditListingDO.setStandardProdcutIdValue(id);
            amazonEditListingDO.setStandardProdcutIdType(type);
            attributeJsonMap.remove(ListingProductData.externally_assigned_product_identifier);
        }

        List<JSONObject> brandList = attributeJsonMap.get(ListingProductData.brand);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(brandList)) {
            amazonEditListingDO.setBrand(brandList.get(0).getString(ListingFiledConstants.VALUE));
            attributeJsonMap.remove(ListingProductData.brand);
        }

        List<JSONObject> manufacturerList = attributeJsonMap.get(ListingProductData.manufacturer);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(manufacturerList)) {
            amazonEditListingDO.setManufacturer(manufacturerList.get(0).getString(ListingFiledConstants.VALUE));
            attributeJsonMap.remove(ListingProductData.manufacturer);
        }

        List<JSONObject> modelNumberList = attributeJsonMap.get(ListingProductData.model_number);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(modelNumberList)) {
            amazonEditListingDO.setMfrPartNumber(modelNumberList.get(0).getString(ListingFiledConstants.VALUE));
            attributeJsonMap.remove(ListingProductData.model_number);
        }

        List<JSONObject> swatchImageList = attributeJsonMap.get(ListingImageData.swatch_product_image_locator);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(swatchImageList)) {
            amazonEditListingDO.setSampleImage(swatchImageList.get(0).getString(ListingImageData.media_location));
            attributeJsonMap.remove(ListingImageData.swatch_product_image_locator);
        }

        List<String> extraImages = new ArrayList<>();
        for (int i = 1; i <= 8; i++) {
            List<JSONObject> ptImageList = attributeJsonMap.get(ListingImageData.other_product_image_locator_ + i);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(ptImageList)) {
                extraImages.add(ptImageList.get(0).getString(ListingImageData.media_location));
                attributeJsonMap.remove(ListingImageData.other_product_image_locator_ + i);
            }
        }
        attributeJsonMap.remove(ListingProductData.brand);
        attributeJsonMap.remove(ListingFiledConstants.ITEM_NAME);
        attributeJsonMap.remove(ListingProductData.generic_keyword);
        attributeJsonMap.remove(ListingProductData.product_description);
        attributeJsonMap.remove(ListingBulletPointData.bullet_point);
        attributeJsonMap.remove(ListingProductData.fulfillment_availability);
        attributeJsonMap.remove(ListingProductData.purchasable_offer);
        attributeJsonMap.remove(ListingProductData.condition_type);
        attributeJsonMap.remove(ListingImageData.main_product_image_locator);
        attributeJsonMap.remove(ListingImageData.swatch_product_image_locator);
        amazonEditListingDO.setExtraImages(JSON.toJSONString(extraImages));
        amazonEditListingDO.setExtraImagesList(extraImages);
        amazonEditListingDO.setExtraData(JSON.toJSONString(attributeJsonMap));
    }


    public static void setEditListingIdentifiers(AmazonTemplateBO amazonEditListingDO, String identifiersJson) {
        if (StringUtils.isBlank(identifiersJson)) {
            return;
        }
        ItemIdentifiers itemIdentifiers = JSON.parseObject(identifiersJson, ItemIdentifiers.class);
        if (CollectionUtils.isNotEmpty(itemIdentifiers) && null != itemIdentifiers.get(0)
                && CollectionUtils.isNotEmpty(itemIdentifiers.get(0).getIdentifiers())) {
            List<ItemIdentifier> identifiers = itemIdentifiers.get(0).getIdentifiers();
            for (ItemIdentifier itemIdentifier : identifiers) {
                if (itemIdentifier.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.UPC.getValue())
                        || itemIdentifier.getIdentifierType().equalsIgnoreCase(IdentifiersTypeEnum.EAN.getValue())) {
                    // 标识符类型 EAN UPC
                    amazonEditListingDO.setStandardProdcutIdType(itemIdentifier.getIdentifierType());
                    // 标识符
                    amazonEditListingDO.setStandardProdcutIdValue(itemIdentifier.getIdentifier());
                }
            }
        }
    }

    public static void setEditListingProductTypes(AmazonTemplateBO amazonEditListingDO, String productTypes) {
        if (StringUtils.isBlank(productTypes)) {
            return;
        }
        ItemProductTypes itemProductTypes = JSON.parseObject(productTypes, ItemProductTypes.class);
        if (CollectionUtils.isNotEmpty(itemProductTypes) && itemProductTypes.get(0) != null) {
            ItemProductTypeByMarketplace itemProductTypeByMarketplace = itemProductTypes.get(0);
            amazonEditListingDO.setProductType(itemProductTypeByMarketplace.getProductType());
        }
    }

    public static void setEditListingSummaries(AmazonTemplateBO amazonEditListingDO, String summaries) {
        if (StringUtils.isBlank(summaries)) {
            return;
        }
        ItemSummaries itemSummaries = JSON.parseObject(summaries, ItemSummaries.class);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemSummaries) && itemSummaries.get(0) != null) {
            ItemSummaryByMarketplace itemSummaryByMarketplace = itemSummaries.get(0);
            String brandName = StringUtils.isBlank(itemSummaryByMarketplace.getBrand()) ? itemSummaryByMarketplace.getManufacturer() : itemSummaryByMarketplace.getBrand();
            amazonEditListingDO.setBrand(brandName);
            // 平台节点Node
            amazonEditListingDO.setBrowsePathById(null != itemSummaryByMarketplace.getBrowseClassification() ? itemSummaryByMarketplace.getBrowseClassification().getClassificationId() : null);
            // 制造商
            amazonEditListingDO.setManufacturer(itemSummaryByMarketplace.getManufacturer());
            // 制造商零件编号
            amazonEditListingDO.setMfrPartNumber(itemSummaryByMarketplace.getModelNumber());
        }

    }


    /**
     * csv 字符集
     *
     * @param marketplaceId
     * @return
     */
    public static String getCsvCharset(String marketplaceId) {
        // JP: A1VC38T7YXB528 TR: A33AVAJ2PDY3EV
        if ("A1VC38T7YXB528".equals(marketplaceId) || "A33AVAJ2PDY3EV".equals(marketplaceId)) {
            return "UTF-8";
        }

        return "ISO8859-1";
    }


}
