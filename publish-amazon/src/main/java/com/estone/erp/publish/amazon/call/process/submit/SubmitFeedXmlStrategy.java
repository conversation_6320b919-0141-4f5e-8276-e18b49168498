package com.estone.erp.publish.amazon.call.process.submit;

/**
 * 
 * @Description: amazon上传数据xml策略接口
 * 
 * @ClassName: AmazonSubmitFeedXml
 * @Author: Kevin
 * @Date: 2018/08/21
 * @Version: 0.0.1
 */
public interface SubmitFeedXmlStrategy<T> {

    /**
     * 
     * @Description: 将刊登数据转换为FeedType = POST_PRODUCT_DATA的xml
     *
     * @param publishData
     * @return xml
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    String transferProduct2Xml(PublishData<T> publishData);

    /**
     * 
     * @Description: 将刊登数据转换为FeedType = POST_PRODUCT_RELATIONSHIP_DATA的xml
     *
     * @param publishData
     * @return xml
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    String transferProductRelationship2Xml(PublishData<T> publishData);

    /**
     * 
     * @Description: 将刊登数据转换为FeedType = POST_PRODUCT_PRICING_DATA的xml
     *
     * @param publishData
     * @return xml
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    String transferProductPrice2Xml(PublishData<T> publishData);

    /**
     * 
     * @Description: 将刊登数据转换为FeedType = POST_INVENTORY_AVAILABILITY_DATA的xml
     *
     * @param publishData
     * @return xml
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    String transferProductInventory2Xml(PublishData<T> publishData);

    /**
     * 
     * @Description: 将刊登数据转换为FeedType = POST_PRODUCT_IMAGE_DATA的xml
     *
     * @param publishData
     * @return xml
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    String transferProductImage2Xml(PublishData<T> publishData);
}
