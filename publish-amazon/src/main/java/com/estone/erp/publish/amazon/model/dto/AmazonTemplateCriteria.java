package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.amazon.model.AmazonTemplate;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class AmazonTemplateCriteria extends AmazonTemplate {

    /**
     *  查询问题分类数据统计
     */
    private Boolean solutionTypeMount =false;

    /**
     * copy数量
     */
    private Integer num;

    private List<Integer> ids;

    private String idsStr;

    private String skus;

    private List<String> sellerIds;

    private Date creationDateStart;

    private Date creationDateEnd;

    private Date lastUpdateDateStart;

    private Date lastUpdateDateEnd;

    // 排序类型
    private String orderType;

    // 关键词长度
    private Integer leng;

    private Boolean isUpload;

    private String keyword;

    private List<String> articleNumbers;

    private List<String> images;
    
    private Integer size;
    
    private String cardCodeType;
    
    private Boolean isParentSku;

    private String amazonTemplateBOList;

    // 刊登状态:none 待刊登, ture 成功, false 失败,publishing 刊登中
    private String publishStepStatus;

    // 刊登流程(template,price,inventory,image)
    private String templatePublishProcess;

    /**
     * 利润率/促销利润率
     */
    private Double grossProfitRate;

    /**
     * 物流方式Code
     */
    private String shippingMethodCode;

    /**
     * 站点
     */
    private String site;

    private List<String> siteList;

    /**
     * 是否删除标题、关键词 包含的侵权词？ 默认false不删除
     */
    private boolean whetherInfringingWordsDel = false;

    /**
     * 不过滤侵权词保存模板信息
     */
    private Boolean needFilter = false;

    /**
     * 刊登类型
     */
    private List<Integer> publishTypes;

    /**
     * 重复sku刊登拦截
     */
    private Boolean repeatSkuInterception = true ;

    /**
     * 问题类型
     */
    private String reportSolutionType;

    public Integer getNum() {
        if (num == null) {
            num = 0;
        }
        return num;
    }

    public List<Integer> getIdList() {
        List<Integer> idList = new ArrayList<>();
        if (StringUtils.isNotBlank(idsStr)) {
            try {
                String[] idsArr = idsStr.split(",");
                for (String id : idsArr) {
                    idList.add(Integer.valueOf(id.trim()));
                }
            } catch (Exception e) {
                throw new BusinessException("参数错误:" + idsStr);
            }
        }
        return idList;
    }

    public List<String> getSkuList() {
        List<String> skuList = new ArrayList<>();
        if (StringUtils.isNotBlank(skus)) {
            String[] skusArr = skus.split(",");
            for (String sku : skusArr) {
                skuList.add(sku.trim());
            }
        }
        return skuList;
    }
}
