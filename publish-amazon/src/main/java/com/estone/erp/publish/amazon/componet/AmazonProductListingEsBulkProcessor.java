package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.model.dto.AmazonUpdateInfringementDO;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.*;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-02 11:22
 */
@Slf4j
@Component
public class AmazonProductListingEsBulkProcessor implements ApplicationContextAware, InitializingBean {
    private BulkProcessor bulkProcessor;
    private ApplicationContext context;

    @Override
    public void afterPropertiesSet() throws Exception {
        RestHighLevelClient restHighLevelClient = context.getBean("restHighLevelClient1", RestHighLevelClient.class);
        BulkProcessor.Listener listener = new BulkProcessor.Listener() {
            @Override
            public void beforeBulk(long executionId, BulkRequest request) {
                int numberOfActions = request.numberOfActions();
                //log.warn("Executing bulk [{}] with {} requests", executionId, numberOfActions);
            }

            @Override
            public void afterBulk(long executionId, BulkRequest request,
                                  BulkResponse response) {
                if (response.hasFailures()) {
                    Iterator<BulkItemResponse> iterator = response.iterator();
                    while (iterator.hasNext()) {
                        BulkItemResponse itemResponse = iterator.next();
                        if (itemResponse.isFailed()) {
                            log.error("Bulk [{}] executed with failures,error:{}", executionId, JSON.toJSONString(itemResponse));
                        }
                    }
                } else {
                    //log.warn("Bulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
                }
            }

            @Override
            public void afterBulk(long executionId, BulkRequest request,
                                  Throwable failure) {
                log.error("写入ES 重新消费");
            }
        };
        bulkProcessor = BulkProcessor.builder(
                        (request, bulkListener) ->
                                restHighLevelClient.bulkAsync(request, RequestOptions.DEFAULT, bulkListener),
                        listener)
                .setBulkActions(1000) // 达到刷新的条数
                .setBulkSize(new ByteSizeValue(5, ByteSizeUnit.MB)) // 达到 刷新的大小
                .setFlushInterval(TimeValue.timeValueSeconds(2)) // 固定刷新的时间频率
                .setConcurrentRequests(1) //并发线程数
                .setBackoffPolicy(BackoffPolicy.exponentialBackoff(TimeValue.timeValueMillis(100), 3)) // 重试补偿策略
                .build();
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }

    /**
     * 异步修改侵权词
     */
    public void syncUpdateInfringementWord(AmazonUpdateInfringementDO updateInfringement)  {
        if (updateInfringement == null || StringUtils.isBlank(updateInfringement.getId())) {
            return;
        }

        if (CollectionUtils.isNotEmpty(updateInfringement.getInfringementWordInfos())) {
            updateInfringement.getInfringementWordInfos().forEach(wordValidateResult -> {
                // 转小写方便查询
                wordValidateResult.setOriginWord(StringUtils.lowerCase(wordValidateResult.getOriginWord()));
            });
        }
        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", updateInfringement.getId());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String jsonString = objectMapper.writeValueAsString(updateInfringement);
            updateRequest.doc(jsonString, XContentType.JSON);
            bulkProcessor.add(updateRequest);
        } catch (Exception e) {
            log.error("修改失败：{},{}", JSON.toJSON(updateInfringement), e.getMessage());
        }
    }

    /**
     * 改本地es下架
     * @param listings
     */
    public void batchUpdateProductListingLocalOffline(List<EsAmazonProductListing> listings) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        for (EsAmazonProductListing listing : listings) {
            updateProductListingLocalOffline(listing);
        }
    }

    public void updateProductListingLocalOffline(EsAmazonProductListing listing) {
        if (listing == null || StringUtils.isBlank(listing.getId())) {
            return;
        }

        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", listing.getId());
        updateRequest.doc(XContentType.JSON,
                "isOnline", listing.getIsOnline(),
                "attribute3", listing.getAttribute3(),
                "attribute4", listing.getAttribute4(),
                "offlineDate", DateUtils.getCurrentTimeStr(null)
                );
        bulkProcessor.add(updateRequest);
    }

    public void updateProductListingPrice(AmazonProductListing listing) {
        if (listing == null
                || StringUtils.isBlank(listing.getAccountNumber())
                || StringUtils.isBlank(listing.getSellerSku())) {
            return;
        }
        String esId = listing.getAccountNumber()+"_"+listing.getSellerSku();
        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", esId);
        updateRequest.doc(XContentType.JSON,
                "price", listing.getPrice(),
                "updateDate", DateUtils.getCurrentTimeStr(null)
        );
        bulkProcessor.add(updateRequest);
    }

    public void updateProductListingQuantity(AmazonProductListing listing) {
        if (listing == null
                || StringUtils.isBlank(listing.getAccountNumber())
                || StringUtils.isBlank(listing.getSellerSku())) {
            return;
        }
        String esId = listing.getAccountNumber()+"_"+listing.getSellerSku();
        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", esId);
        updateRequest.doc(XContentType.JSON,
                "quantity", listing.getQuantity(),
                "updateDate", DateUtils.getCurrentTimeStr(null)
        );
        bulkProcessor.add(updateRequest);
    }

    public void updateProductListingGpsr(AmazonListingGpsrInfo amazonListingGpsrInfo) {
        String esId = amazonListingGpsrInfo.getAccountNumber() + "_" + amazonListingGpsrInfo.getSellerSku();
        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", esId);
        updateRequest.doc(XContentType.JSON,
                "existGpsr", amazonListingGpsrInfo.getUpload(),
                "updateDate", DateUtils.getCurrentTimeStr(null)
        );
        bulkProcessor.add(updateRequest);
    }

    public void updateListingBindSku(String key, String sku) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(sku)) {
            return;
        }
        UpdateRequest updateRequest = new UpdateRequest("amazon_product_listing", key);
        updateRequest.doc(XContentType.JSON,
                "articleNumber", sku,
                "updateDate", DateUtils.getCurrentTimeStr(null)
        );
        bulkProcessor.add(updateRequest);
    }
}
