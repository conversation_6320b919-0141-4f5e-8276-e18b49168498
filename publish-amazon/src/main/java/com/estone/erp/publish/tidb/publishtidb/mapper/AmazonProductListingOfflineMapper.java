package com.estone.erp.publish.tidb.publishtidb.mapper;


import java.util.List;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample;
import org.apache.ibatis.annotations.Param;

public interface AmazonProductListingOfflineMapper {
    int countByExample(AmazonProductListingOfflineExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(AmazonProductListingOffline record);

    AmazonProductListingOffline selectByPrimaryKey(Long id);

    List<AmazonProductListingOffline> selectByExample(AmazonProductListingOfflineExample example);

    int updateByExampleSelective(@Param("record") AmazonProductListingOffline record, @Param("example") AmazonProductListingOfflineExample example);

    int updateByPrimaryKeySelective(AmazonProductListingOffline record);

    List<Long> selectIdListByExample(AmazonProductListingOfflineExample example);

    List<AmazonProductListingOffline> selectFiledColumnsByExample(AmazonProductListingOfflineExample example);

    int batchInsert(@Param("list")List<AmazonProductListingOffline> amazonProductListingOfflineList);
}