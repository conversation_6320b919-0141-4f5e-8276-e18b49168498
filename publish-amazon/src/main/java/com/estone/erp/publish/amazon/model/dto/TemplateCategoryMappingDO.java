package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

/**
 * 模板分类映射数据
 * <AUTHOR>
 * @date 2023-09-06 18:07
 */
@Data
public class TemplateCategoryMappingDO {

    /**
     * 产品系统分类
     */
    private String systemCategoryPathCode;

    /**
     * 站点
     */
    private String site;

    /**
     * 平台分类ID
     */
    private String platformCategoryId;

    /**
     * 分类全路径id
     */
    private String browsePathById;

    /**
     * 平台分类类型
     */
    private String productType;

    /**
     * 分类类型大类
     */
    private String parentProductType;

    /**
     * 分类模板名称
     */
    private String platformCategoryTemplateName;
}
