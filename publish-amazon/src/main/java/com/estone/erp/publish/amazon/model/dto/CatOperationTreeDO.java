package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-11 15:53
 */
@Data
public class CatOperationTreeDO {

    /**
     * 类目Code
     */
    private Integer code;

    /**
     * 父级类目code
     */
    private Integer parentCode;

    /**
     * 当前级别
     */
    private Integer level;

    /**
     * 类目路径code
     */
    private String pathCode;

    /**
     * 类目路径名称
     */
    private String pathName;


    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 子类别树
     */
    private List<CatOperationTreeDO> subCategories;
}
