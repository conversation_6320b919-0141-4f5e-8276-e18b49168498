package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-07-07 10:14
 */
@Data
public class TemplateInfringementWordDTO implements Serializable {
    private Integer id;
    private String spu;
    private String title;
    private String brand;
    private String description;
    private String bulletPoint;
    private String searchData;
    private Boolean isInfringingWordsError;


    /**
     * 存在侵权词
     */
    public Boolean existInfringementWord() {
        return StringUtils.isNotBlank(title)
                || StringUtils.isNotBlank(brand)
                || StringUtils.isNotBlank(description)
                || StringUtils.isNotBlank(bulletPoint)
                || StringUtils.isNotBlank(searchData);
    }


    public String formatMsg() {
        return String.format("标题或关键词存在侵权词【产品标题：%s; 品牌%s; 关键词信息：%s; 产品描述：%s; 五点描述：%s】，请修改后重新保存刊登。",
                (title == null ? "" : title),(brand == null ? "" : brand),(searchData == null ? "" : searchData), (description == null ? "" : description), (bulletPoint == null ? "" : bulletPoint));
    }


}
