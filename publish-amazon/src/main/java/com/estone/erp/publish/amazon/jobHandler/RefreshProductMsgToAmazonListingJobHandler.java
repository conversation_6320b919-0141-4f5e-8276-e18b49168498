package com.estone.erp.publish.amazon.jobHandler;


import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.platform.enums.AmazonCommonlyUsedSiteEnum;
import com.estone.erp.publish.platform.model.PmsSkuExample;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.forbidden.ProductInfringementVO;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 刷数据
 * <AUTHOR>
 * @Date 2022/02/17
 **/
@Slf4j
@Component
public class RefreshProductMsgToAmazonListingJobHandler extends AbstractJobHandler {

    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private PmsSkuService pmsSkuService;

    public RefreshProductMsgToAmazonListingJobHandler() {
        super(RefreshProductMsgToAmazonListingJobHandler.class.getName());
    }

    @Override
    @XxlJob("RefreshProductMsgToAmazonListingJobHandler")
    public ReturnT<String> run(String param) {
        //获取参数供测试使用
        log.info("开始刷数据！");
        XxlJobLogger.log("开始刷数据,param: {}", param);
        if (StringUtils.isNotBlank(param)) {
            List<String> skuList = Arrays.asList(param.split(","));
            for (String articleNumber : skuList) {
                AmazonExecutors.executeRefreshListing(() -> {
                    updateListing(articleNumber);
                });
            }
            return ReturnT.SUCCESS;
        }

        int offset = 0;
        int limit = 5000;
        PmsSkuExample example = new PmsSkuExample();
        example.setLimit(limit);
        example.setOrderByClause("id asc ");
        while (true) {
            String msg = String.format("开始处理第 %s 条至第 %s 条数据！", offset, offset + limit);
            log.info(msg);
            XxlJobLogger.log(msg);
            //偏移量
            example.setOffset(offset);
            List<String> articleNumberList = pmsSkuService.selectSkuByExample(example);
            if (CollectionUtils.isEmpty(articleNumberList)) {
                break;
            }
            offset += limit;
            for (String articleNumber : articleNumberList) {
                //AmazonExecutors.executeRefreshListing(() -> {
                updateListing(articleNumber);
                //});
            }
        }
        return ReturnT.SUCCESS;
    }

    public void updateListing(String articleNumber) {
        try {
            ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
            /*List<ProductInfringementVO> infringementInfoBySonSku = ProductUtils.getInfringementInfoBySonSku(articleNumber);
            if (CollectionUtils.isNotEmpty(infringementInfoBySonSku)) {
                productInfoVO.setProductInfringementVO(infringementInfoBySonSku.get(0));
            }*/
            /*//三个值至少要有一个才能去修改
            if (StringUtils.isNotBlank(productInfoVO.getInfringementTypeName()) ||
                    StringUtils.isNotBlank(productInfoVO.getInfringementObj()) ||
                    MapUtils.isNotEmpty(productInfoVO.getSalesProhibition())
            ) {
                AmazonProductListing amazonProductListing = new AmazonProductListing();

                //禁售类型
                amazonProductListing.setInfringementTypename(productInfoVO.getInfringementTypeName());

                //禁售原因
                amazonProductListing.setInfringementObj(productInfoVO.getInfringementObj());

                //如果禁售平台数据不为空，才获取判断
                if (MapUtils.isNotEmpty(productInfoVO.getSalesProhibition())) {
                    //获取禁售站点
                    Map<String, List<Sites>> salesProhibition = productInfoVO.getSalesProhibition();
                    List<Sites> sites = salesProhibition.get(SaleChannel.CHANNEL_AMAZON);
                    //如果取到禁售平台
                    if (CollectionUtils.isNotEmpty(sites)) {
                        //禁售站点，过滤其中站点为空字符串的
                        List<String> site = sites.stream().filter(o -> StringUtils.isNotBlank(o.getSite())).map(o -> o.getSite()).collect(Collectors.toList());
                        //为空即为全禁售
                        if (CollectionUtils.isEmpty(site)) {
                            //所有禁售站点
                            amazonProductListing.setNormalSale("," + AmazonCommonlyUsedSiteEnum.getSiteList(null).stream().collect(Collectors.joining(",")) + ",");
                        } else {
                            //产品系统禁售站点
                            amazonProductListing.setNormalSale("," + site.stream().collect(Collectors.joining(",")) + ",");
                        }
                    }
                }*/

            AmazonProductListing amazonProductListing = amazonProductListingService.assembleProductListing(null, null, productInfoVO);
            AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
            amazonProductListingExample.createCriteria().andArticleNumberEqualTo(articleNumber).andIsOnlineEqualTo(true);
            String[] siteList = {"DE", "ES", "FR", "IT", "UK", "US"};
            for (String site : siteList) {
                try {
                    amazonProductListing.setSite(site);
                    amazonProductListingService.updateProductMsgByExampleSelective(amazonProductListing, amazonProductListingExample);
                } catch (Exception e) {
                    String msg = String.format("修改失败货号: %s ,对应站点表：%s", articleNumber, site);
                    log.error(msg);
                    XxlJobLogger.log(msg);
                }
            }
            // }
        } catch (Exception e) {
            log.error("----------------:" + articleNumber + ":" + e.getMessage());
        }
    }
}
