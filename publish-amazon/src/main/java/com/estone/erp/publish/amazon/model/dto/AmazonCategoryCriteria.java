package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.AmazonCategory;


public class AmazonCategoryCriteria extends AmazonCategory {

    private String accountNumber;
    
    private String category;
    
    private String nodeName;

    /**
     * 查询类型
     */
    private Integer searchType;

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }
}
