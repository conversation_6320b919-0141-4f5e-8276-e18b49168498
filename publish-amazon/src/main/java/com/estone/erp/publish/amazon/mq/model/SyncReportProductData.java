package com.estone.erp.publish.amazon.mq.model;

import com.estone.erp.publish.base.pms.model.AmazonAccount;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: api_amazon
 * @description: 同步报告+产品数据类
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-11-09 16:57
 **/
@Data
public class SyncReportProductData {
    /**
     * 账号
     */
    private AmazonAccount account;

    /**
     * 所有ASIN码
     */
    private List<String> asins = new ArrayList<>();

    /**
     * 单属性数据
     */
    private List<SyncProductMqData> syncProductDatas = new ArrayList<>();

    /**
     * 多属性数据
     */
    private List<SyncProductMqData> syncProduct2Datas = new ArrayList<>();
}
