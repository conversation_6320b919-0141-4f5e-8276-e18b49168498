package com.estone.erp.publish.amazon.call.xsd.model;

import java.io.Serializable;
import java.util.List;

/**
 * xsd节点属性
 * 
 * <AUTHOR>
 *
 */
public class XsdAttr implements Serializable {
    private static final long serialVersionUID = -619350158537849915L;

    private String name;

    private XsdType type;

    private String use;

    private List<String> documentations;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public XsdType getType() {
        return type;
    }

    public void setType(XsdType type) {
        this.type = type;
    }

    public String getUse() {
        return use;
    }

    public void setUse(String use) {
        this.use = use;
    }

    public List<String> getDocumentations() {
        return documentations;
    }

    public void setDocumentations(List<String> documentations) {
        this.documentations = documentations;
    }
}
