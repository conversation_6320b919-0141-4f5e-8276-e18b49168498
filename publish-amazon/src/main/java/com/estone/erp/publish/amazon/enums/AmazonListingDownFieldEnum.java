package com.estone.erp.publish.amazon.enums;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Amazon在线列表下载导出字段
 *
 * <AUTHOR>
 * @date 2022/2/24 18:21
 */
public enum AmazonListingDownFieldEnum {
    MAIN_IMAGE("mainImage", "主图", new String[]{"mainImage"}),
    PARENT_ASIN("parentAsin", "父asin码", new String[]{"parentAsin"}),
    SON_ASIN("sonAsin", "子asin码", new String[]{"sonAsin"}),
    ITEM_TYPE("itemType", "item类型", new String[]{"itemType"}),
    SALE_MAN_LIST("saleManList", "店铺关联销售", new String[]{"accountNumber"}),
    ITEM_NAME("itemName", "名称", new String[]{"itemName"}),
    INFRINGEMENT_WORD("infringingBrandWord", "侵权词", new String[]{"infringingBrandWord"}),
    ACCOUNT_NUMBER("accountNumber", "账号", new String[]{"accountNumber"}),
    // 单独查询 店铺状态
    ACCOUNT_STATUS("accountStatus", "店铺状态", new String[]{"accountNumber"}),
    // 单独查询 异常状态
    EXCEPTION_STATUS("exceptionStatus", "异常状态", new String[]{"accountNumber"}),
    BRAND_NAME("brandName", "品牌", new String[]{"brandName"}),
    SELLER_SKU("sellerSku", "sellerSKU", new String[]{"sellerSku"}),
    ARTICLE_NUMBER("articleNumber", "SKU", new String[]{"articleNumber"}),
    PRICE("price", "价格", new String[]{"price"}),
    MERCHANT_SHIPPING_GROUP("merchantShippingGroup", "运费模板", new String[]{"merchantShippingGroup"}),
    SHIPPING_COST("shippingCost", "运费", new String[]{"shippingCost"}),
    TOTAL_PRICE("totalPrice", "总价", new String[]{"totalPrice"}),
    QUANTITY("quantity", "库存", new String[]{"quantity"}),
    GROSS_PROFIT("grossProfit", "毛利", new String[]{"grossProfit"}),
    GROSS_PROFIT_RATE("grossProfitRate", "毛利率", new String[]{"grossProfitRate"}),
    SKU_STATUS("skuStatus", "单品状态", new String[]{"skuStatus"}),
    PUBLISH_ROLE("publishRole", "刊登角色", new String[]{"publishRole"}),
    SKU_DATA_SOURCE("skuDataSource", "数据来源", new String[]{"skuDataSource"}),
    COMPOSE_STATUS("composeStatus", "组合状态", new String[]{"composeStatus"}),
    FULFILLMENT_LATENCY("fulfillmentLatency", "备货期", new String[]{"fulfillmentLatency"}),
    PROMOTION("promotion", "是否促销", new String[]{"promotion"}),
    NEW_STATE("newState", "是否新品", new String[]{"newState"}),
    SPECIAL_GOODS_CODE("specialGoodsCode", "特殊标签", new String[]{"specialGoodsCode"}),
    TRADEMARK_IDENTIFICATION("trademarkIdentification", "商标词标识", new String[]{"trademarkIdentification"}),
    FORBID_CHANNEL("forbidChannel", "禁售平台", new String[]{"forbidChannel"}),
    INFRINGEMENT_TYPENAME("infringementTypename", "禁售类型", new String[]{"infringementTypename"}),
    INFRINGEMENT_OBJ("infringementObj", "禁售原因", new String[]{"infringementObj"}),
    NORMAL_SALE("normalSale", "Amazon禁售站点", new String[]{"normalSale"}),
    IS_ONLINE("isOnline", "是否在线", new String[]{"isOnline"}),
    ITEM_STATUS("itemStatus", "asin状态", new String[]{"itemStatus"}),
    ISSUES_SEVERITY("issuesSeverity", "问题类型", new String[]{"issuesSeverity"}),
    ATTRIBUTE3("attribute3", "备注", new String[]{"attribute3"}),
    ATTRIBUTE1("attribute1", "账号等级", new String[] {"attribute1"}),
    OPEN_DATE("openDate", "最新上架时间", new String[] {"openDate"}),
    OFFLINE_DATE("offlineDate", "最新下架时间", new String[] {"offlineDate"}),
    FIRST_OPEN_DATE("firstOpenDate", "首次上架时间", new String[] {"firstOpenDate"}),
    FIRST_OFFLINE_DATE("firstOfflineDate", "首次下架时间", new String[]{"firstOfflineDate"}),
    ORDER_24H_COUNT("order_24H_count", "24H销量", new String[] {"order_24H_count"}),
    ORDER_LAST_7D_COUNT("order_last_7d_count", "7天销量", new String[] {"order_last_7d_count"}),
    ORDER_LAST_14D_COUNT("order_last_14d_count", "14天销量", new String[]{"order_last_14d_count"}),
    ORDER_LAST_30D_COUNT("order_last_30d_count", "30天销量", new String[]{"order_last_30d_count"}),
    ORDER_NUM_TOTAL("order_num_total", "总销量", new String[]{"order_num_total"}),
    ORDER_LAST_7D_AVG("order_last_7d_avg", "7天日均销量", new String[]{"order_last_7d_avg"}),
    ORDER_LAST_14D_AVG("order_last_14d_avg", "14天日均销量", new String[]{"order_last_14d_avg"}),
    ORDER_LAST_30D_AVG("order_last_30d_avg", "30天日均销量", new String[]{"order_last_30d_avg"}),
    order_days_within_30d("order_days_within_30d", " 30天动销天数", new String[]{"order_days_within_30d"}),
    ORDER_LAST_7D_AND_30D_AVG("order_last_7d_and_30d_avg", " 7天/30天日均销量", new String[]{"order_last_7d_avg", "order_last_30d_avg"}),
    UPDATE_DATE("updateDate", "修改时间", new String[]{"updateDate"}),
    SYNC_DATE("syncDate", "同步时间", new String[]{"syncDate"}),
    ATTRIBUTE4("attribute4", "毛利修改人", new String[]{"attribute4"}),
    ATTRIBUTE5("attribute5", "毛利修改时间", new String[]{"attribute5"}),
    UPDATE_INFRINGEMENT_TIME("updateInfringementTime", "侵权词校验时间", new String[]{"updateInfringementTime"}),
    RISKLEVELID("riskLevelId", "风险等级", new String[] {"riskLevelId"}),

    ;

    private String code;
    private String name;

    private String[] searchField;

    private AmazonListingDownFieldEnum(String code, String name, String[] searchField) {
        this.code = code;
        this.name = name;
        this.searchField = searchField;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static List<String> getNameList(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return null;
        }
        List<String> nameList = new ArrayList<>();
        AmazonListingDownFieldEnum[] values = values();
        for (AmazonListingDownFieldEnum type : values) {
            String code = type.getCode();
            if (codeList.contains(code)) {
                nameList.add(type.name);
            }
        }
        return nameList;
    }

    public static List<String> getSearchField(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return null;
        }
        List<String> needSearchList = new ArrayList<>();
        AmazonListingDownFieldEnum[] values = values();
        for (AmazonListingDownFieldEnum type : values) {
            String code = type.getCode();
            if (codeList.contains(code)) {
                needSearchList.addAll(Arrays.asList(type.searchField));
            }
        }
        needSearchList = needSearchList.stream().distinct().collect(Collectors.toList());
        return needSearchList;
    }
}
