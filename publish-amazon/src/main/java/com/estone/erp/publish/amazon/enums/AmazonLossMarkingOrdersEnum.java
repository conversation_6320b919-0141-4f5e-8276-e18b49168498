package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-07-29 下午6:06
 */
@Getter
@AllArgsConstructor
public enum AmazonLossMarkingOrdersEnum {

    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    EXECUTING(2, "执行中");


    private final int code;
    private final String desc;

    public static AmazonLossMarkingOrdersEnum getEnumByCode(Integer value) {
        if (value == null) {
            return null;
        }
        for (AmazonLossMarkingOrdersEnum amazonLossMarkingOrdersEnum : values()) {
            if (amazonLossMarkingOrdersEnum.getCode() == value) {
                return amazonLossMarkingOrdersEnum;
            }
        }

        return null;
    }

    public Boolean isTrue(int code) {
        return this.code == code;
    }
}
