package com.estone.erp.publish.amazon.call.xsd.model;

import com.estone.erp.publish.amazon.call.model.Attribute;

import java.util.List;

public class BrowseTreeReport {
    private String query;

    private List<Node> nodes;

    public void setQuery(String query) {
        this.query = query;
    }

    public void setNodes(List<Node> nodes) {
        this.nodes = nodes;
    }

    public String getQuery() {
        return query;
    }

    public List<Node> getNodes() {
        return nodes;
    }

    public Node createNode() {
        return new Node();
    }

    public static class Node {
        private Integer id;

        private String browseNodeId;

        private List<Attribute<String, String>> browseNodeAttributes;

        private String browseNodeName;

        private String browseNodeStoreContextName;

        private String browsePathById;

        private String browsePathByName;

        private String browsePathByNameCn;

        private boolean hasChildren;

        private List<String> childNodes;

        private List<String> itemTypeKeyWords;

        private String productTypeDefinitions;

        private RefinementsInformation refinementsInformation;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getBrowseNodeId() {
            return browseNodeId;
        }

        public void setBrowseNodeId(String browseNodeId) {
            this.browseNodeId = browseNodeId;
        }

        public List<Attribute<String, String>> getBrowseNodeAttributes() {
            return browseNodeAttributes;
        }

        public void setBrowseNodeAttributes(List<Attribute<String, String>> browseNodeAttributes) {
            this.browseNodeAttributes = browseNodeAttributes;
        }

        public String getBrowseNodeName() {
            return browseNodeName;
        }

        public void setBrowseNodeName(String browseNodeName) {
            this.browseNodeName = browseNodeName;
        }

        public String getBrowseNodeStoreContextName() {
            return browseNodeStoreContextName;
        }

        public void setBrowseNodeStoreContextName(String browseNodeStoreContextName) {
            this.browseNodeStoreContextName = browseNodeStoreContextName;
        }

        public String getBrowsePathById() {
            return browsePathById;
        }

        public void setBrowsePathById(String browsePathById) {
            this.browsePathById = browsePathById;
        }

        public String getBrowsePathByName() {
            return browsePathByName;
        }

        public void setBrowsePathByName(String browsePathByName) {
            this.browsePathByName = browsePathByName;
        }

        public String getBrowsePathByNameCn() {
            return browsePathByNameCn;
        }

        public void setBrowsePathByNameCn(String browsePathByNameCn) {
            this.browsePathByNameCn = browsePathByNameCn;
        }

        public boolean isHasChildren() {
            return hasChildren;
        }

        public void setHasChildren(boolean hasChildren) {
            this.hasChildren = hasChildren;
        }

        public List<String> getChildNodes() {
            return childNodes;
        }

        public void setChildNodes(List<String> childNodes) {
            this.childNodes = childNodes;
        }

        public String getProductTypeDefinitions() {
            return productTypeDefinitions;
        }

        public void setProductTypeDefinitions(String productTypeDefinitions) {
            this.productTypeDefinitions = productTypeDefinitions;
        }

        public RefinementsInformation getRefinementsInformation() {
            return refinementsInformation;
        }

        public void setRefinementsInformation(RefinementsInformation refinementsInformation) {
            this.refinementsInformation = refinementsInformation;
        }

        public RefinementsInformation createRefinementsInformation() {
            return new RefinementsInformation();
        }

        @Override
        public String toString() {
            return String.valueOf(browseNodeId) + ": " + String.valueOf(browseNodeName);
        }

        public List<String> getItemTypeKeyWords() {
            return itemTypeKeyWords;
        }

        public void setItemTypeKeyWords(List<String> itemTypeKeyWords) {
            this.itemTypeKeyWords = itemTypeKeyWords;
        }
    }

    public static class RefinementsInformation {
        private List<RefinementName> refinementNames;

        public List<RefinementName> getRefinementNames() {
            return refinementNames;
        }

        public void setRefinementNames(List<RefinementName> refinementNames) {
            this.refinementNames = refinementNames;
        }

        public RefinementName createRefinementName() {
            return new RefinementName();
        }
    }

    public static class RefinementName {
        private Attribute<String, List<RefinementField>> refinementFields;

        public Attribute<String, List<RefinementField>> getRefinementFields() {
            return refinementFields;
        }

        public void setRefinementFields(Attribute<String, List<RefinementField>> refinementFields) {
            this.refinementFields = refinementFields;
        }

        public RefinementField createRefinementField() {
            return new RefinementField();
        }
    }

    public static class RefinementField {
        private List<String> acceptedValues;

        private String hasModifier;

        private String modifiers;

        private String refinementAttribute;

        public List<String> getAcceptedValues() {
            return acceptedValues;
        }

        public void setAcceptedValues(List<String> acceptedValues) {
            this.acceptedValues = acceptedValues;
        }

        public String getHasModifier() {
            return hasModifier;
        }

        public void setHasModifier(String hasModifier) {
            this.hasModifier = hasModifier;
        }

        public String getModifiers() {
            return modifiers;
        }

        public void setModifiers(String modifiers) {
            this.modifiers = modifiers;
        }

        public String getRefinementAttribute() {
            return refinementAttribute;
        }

        public void setRefinementAttribute(String refinementAttribute) {
            this.refinementAttribute = refinementAttribute;
        }
    }

}