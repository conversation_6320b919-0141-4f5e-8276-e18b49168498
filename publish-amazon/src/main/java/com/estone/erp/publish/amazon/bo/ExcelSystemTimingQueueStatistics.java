package com.estone.erp.publish.amazon.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.model.SystemTimingQueueStatistics;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ExcelSystemTimingQueueStatistics {

    public static List<String> getDownLoadFiled() {
        return List.of("day", "accountNumber", "site", "publishQuantity", "minPublishMount",
                "publishIntervalTime", "timingQueueCount", "publishCount", "publishSuccessCount", "publishFailCount");
    }

    public ExcelSystemTimingQueueStatistics(SystemTimingQueueStatistics systemTimingQueueStatistics) {
        Date currentDay = systemTimingQueueStatistics.getDay();
        if (currentDay != null) {
            String format = DateUtils.format(currentDay, "yyyy-MM-dd");
            this.setDay(format);
        }

        this.setAccountNumber(systemTimingQueueStatistics.getAccountNumber());
        this.setSite(systemTimingQueueStatistics.getSite());
        this.setPublishQuantity(systemTimingQueueStatistics.getPublishQuantity());
        this.setMinPublishMount(systemTimingQueueStatistics.getMinPublishMount());
        this.setPublishIntervalTime(systemTimingQueueStatistics.getPublishIntervalTime());
        this.setTimingQueueCount(systemTimingQueueStatistics.getTimingQueueCount());
        this.setPublishCount(systemTimingQueueStatistics.getPublishCount());
        this.setPublishSuccessCount(systemTimingQueueStatistics.getPublishSuccessCount());
        this.setPublishFailCount(systemTimingQueueStatistics.getPublishFailCount());
    }

    /**
     * 统计的时间 database column system_timing_queue_statistics.day
     */
    @ExcelProperty(value = "日期", index = 0)
    private String day;

    /**
     * 店铺账号 database column system_timing_queue_statistics.account_number
     */
    @ExcelProperty(value = "店铺", index = 1)
    private String accountNumber;

    /**
     * 站点 database column system_timing_queue_statistics.site
     */
    @ExcelProperty(value = "站点", index = 2)
    private String site;

    /**
     * 最大刊登数量 database column system_timing_queue_statistics.publish_quantity
     */
    @ExcelProperty(value = "最大刊登数量", index = 3)
    private Integer publishQuantity;

    /**
     * 每分钟刊登spu数量 database column system_timing_queue_statistics.min_publish_mount
     */
    @ExcelProperty(value = "每分钟刊登SPU数量", index = 4)
    private Integer minPublishMount;

    /**
     * 上架时间间隔 database column system_timing_queue_statistics.publish_interval_time
     */
    @ExcelProperty(value = "上架时间间隔", index = 5)
    private Integer publishIntervalTime;


    /**
     * 生成定时队列数量 database column system_timing_queue_statistics.timing_queue_count
     */
    @ExcelProperty(value = "生成定时队列数量", index = 6)
    private Integer timingQueueCount;

    /**
     * 刊登数量 database column system_timing_queue_statistics.publish_count
     */
    @ExcelProperty(value = "刊登数量", index = 7)
    private Integer publishCount;

    /**
     * 刊登成功数量 database column system_timing_queue_statistics.publish_success_count
     */
    @ExcelProperty(value = "刊登成功数量", index = 8)
    private Integer publishSuccessCount;

    /**
     * 刊登失败数量 database column system_timing_queue_statistics.publish_fail_count
     */
    @ExcelProperty(value = "刊登失败数量", index = 9)
    private Integer publishFailCount;

}
