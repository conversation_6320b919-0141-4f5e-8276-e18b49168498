package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonUpcRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecordCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonUpcRecordExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonUpcRecordService;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * 2024-07-09 16:19:01
 */
@Service("amazonUpcRecordService")
@Slf4j
public class AmazonUpcRecordServiceImpl implements AmazonUpcRecordService {
    @Resource
    private AmazonUpcRecordMapper amazonUpcRecordMapper;

    @Override
    public int countByExample(AmazonUpcRecordExample example) {
        Assert.notNull(example, "example is null!");
        return amazonUpcRecordMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonUpcRecord> search(CQuery<AmazonUpcRecordCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonUpcRecordCriteria query = cquery.getSearch();
        AmazonUpcRecordExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonUpcRecordMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonUpcRecord> amazonUpcRecords = amazonUpcRecordMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonUpcRecord> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonUpcRecords);
        return result;
    }

    @Override
    public AmazonUpcRecord selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return amazonUpcRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonUpcRecord> selectByExample(AmazonUpcRecordExample example) {
        Assert.notNull(example, "example is null!");
        return amazonUpcRecordMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonUpcRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonUpcRecordMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonUpcRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonUpcRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonUpcRecord record, AmazonUpcRecordExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonUpcRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonUpcRecordMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<String>selectExistUpc(List<String> upcList){
        if (CollectionUtils.isEmpty(upcList)){
            return null;
        }
        AmazonUpcRecordExample amazonUpcRecordExample = new AmazonUpcRecordExample();
        amazonUpcRecordExample.createCriteria().andUpcIn(upcList);
        return amazonUpcRecordMapper.selectExistUpcByExample(amazonUpcRecordExample);
    }

    @Override
    public int batchInsert(List<AmazonUpcRecord> amazonUpcRecordList){
        if (CollectionUtils.isEmpty(amazonUpcRecordList)){
            return 0;
        }
        return amazonUpcRecordMapper.batchInsert(amazonUpcRecordList);
    }
}