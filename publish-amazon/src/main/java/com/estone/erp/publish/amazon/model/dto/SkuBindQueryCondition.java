package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.SkuBind;

import java.util.List;

public class SkuBindQueryCondition extends SkuBind {
    private static final long serialVersionUID = 1L;

    private List<String> platforms;

    private List<String> sellerIds;

    private List<String> skus;

    private List<String> bindSkus;

    private String orderBy;

    public List<String> getPlatforms() {
        return platforms;
    }

    public void setPlatforms(List<String> platforms) {
        this.platforms = platforms;
    }

    public List<String> getSellerIds() {
        return sellerIds;
    }

    public void setSellerIds(List<String> sellerIds) {
        this.sellerIds = sellerIds;
    }

    public List<String> getSkus() {
        return skus;
    }

    public void setSkus(List<String> skus) {
        this.skus = skus;
    }

    public List<String> getBindSkus() {
        return bindSkus;
    }

    public void setBindSkus(List<String> bindSkus) {
        this.bindSkus = bindSkus;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }
}