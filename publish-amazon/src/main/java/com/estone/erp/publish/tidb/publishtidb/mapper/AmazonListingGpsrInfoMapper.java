package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Amazon listing GPSR 信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface AmazonListingGpsrInfoMapper extends BaseMapper<AmazonListingGpsrInfo> {
    List<Map<Object, Object>> getTidbPageMetaMap(@Param(Constants.WRAPPER) LambdaQueryWrapper<AmazonListingGpsrInfo> wrapper);
}
