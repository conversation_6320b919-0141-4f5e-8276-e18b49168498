package com.estone.erp.publish.amazon.call.model;

/**
 * @Description: 自定义亚马逊产品类
 * @Author: listen
 * @Date: 2018/12/29 11:22
 * @Version: 0.0.1
 */
public class Product {

    private String marketplaceId;

    private String asin;

    private String sellerId;

    private String sellerSKU;

    private String attributeSetsXml;

    private String relationshipsXml;

    private String competitivePricingXml;

    private String salesRankingsXml;

    private String lowestOfferListingsXml;

    private String offersXml;

    public Product() {
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public String getAsin() {
        return asin;
    }

    public void setAsin(String asin) {
        this.asin = asin;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerSKU() {
        return sellerSKU;
    }

    public void setSellerSKU(String sellerSKU) {
        this.sellerSKU = sellerSKU;
    }

    public String getAttributeSetsXml() {
        return attributeSetsXml;
    }

    public void setAttributeSetsXml(String attributeSetsXml) {
        this.attributeSetsXml = attributeSetsXml;
    }

    public String getRelationshipsXml() {
        return relationshipsXml;
    }

    public void setRelationshipsXml(String relationshipsXml) {
        this.relationshipsXml = relationshipsXml;
    }

    public String getCompetitivePricingXml() {
        return competitivePricingXml;
    }

    public void setCompetitivePricingXml(String competitivePricingXml) {
        this.competitivePricingXml = competitivePricingXml;
    }

    public String getSalesRankingsXml() {
        return salesRankingsXml;
    }

    public void setSalesRankingsXml(String salesRankingsXml) {
        this.salesRankingsXml = salesRankingsXml;
    }

    public String getLowestOfferListingsXml() {
        return lowestOfferListingsXml;
    }

    public void setLowestOfferListingsXml(String lowestOfferListingsXml) {
        this.lowestOfferListingsXml = lowestOfferListingsXml;
    }

    public String getOffersXml() {
        return offersXml;
    }

    public void setOffersXml(String offersXml) {
        this.offersXml = offersXml;
    }
}
