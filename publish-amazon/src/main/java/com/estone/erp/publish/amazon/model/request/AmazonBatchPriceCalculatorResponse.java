package com.estone.erp.publish.amazon.model.request;

import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import lombok.Data;

/**
 * @Auther yucm
 * @Date 2021/12/15
 */
@Data
public class AmazonBatchPriceCalculatorResponse extends BatchPriceCalculatorResponse {

    /**
     * 总价
     */
    private Double foreignTotlePrice;

    /**
     * 运费模板
     */
    private String shippingGroup;

    /**
     * 运费模板对应运费
     */
    private Double shippingCost;

    /**
     * 物流方式
     */
    private String shippingMethodCode;

    /**
     * 默认构造器
     */
    public AmazonBatchPriceCalculatorResponse(){};

    public AmazonBatchPriceCalculatorResponse(BatchPriceCalculatorResponse parent){
        this.setId(parent.getId());
        this.setIsSuccess(parent.getIsSuccess());
        this.setSite(parent.getSite());
        this.setArticleNumber(parent.getArticleNumber());
        this.setExchangeRate(parent.getExchangeRate());
        this.setCurrencyCode(parent.getCurrencyCode());
        this.setPrice(parent.getPrice());
        this.setForeignPrice(parent.getForeignPrice());
        this.setRetailPrice(parent.getRetailPrice());
        this.setRetailPriceCny(parent.getRetailPriceCny());
        this.setPostageFee(parent.getPostageFee());
        this.setForeignPostageFee(parent.getForeignPostageFee());
        this.setTransactionCommissionFee(parent.getTransactionCommissionFee());
        this.setForeignTransactionCommissionFee(parent.getForeignTransactionCommissionFee());
        this.setPaymentCommissionFee(parent.getPaymentCommissionFee());
        this.setForeignPaymentCommissionFee(parent.getForeignPaymentCommissionFee());
        this.setShippingCost(parent.getShippingCost());
        this.setForeignShippingCost(parent.getForeignShippingCost());
        this.setTotalCost(parent.getTotalCost());
        this.setForeignTotalCost(parent.getForeignTotalCost());
        this.setGrossProfit(parent.getGrossProfit());
        this.setForeignGrossProfit(parent.getForeignGrossProfit());
        this.setGrossProfitRate(parent.getGrossProfitRate());
        this.setErrorMsg(parent.getErrorMsg());
        this.setCountryCode(parent.getCountryCode());
    };
}
