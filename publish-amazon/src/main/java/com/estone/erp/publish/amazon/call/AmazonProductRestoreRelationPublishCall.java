package com.estone.erp.publish.amazon.call;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.ERPInvoker;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.process.submit.AmazonDelayTaskDispatcher;
import com.estone.erp.publish.amazon.call.process.submit.ProductRestoreRelationFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.call.process.submit.SubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecord;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.request.RequestFeedsApiParam;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;

/**
 * @Description 使用：amazon在线listing 绑定关系
 * <AUTHOR>
 * @Date 2019/10/25 15:32
 **/
@Slf4j
@Setter
public class AmazonProductRestoreRelationPublishCall {

    private AmazonProductPublishCallHelper amazonProductPublishCallHelper = SpringUtils.getBean(AmazonProductPublishCallHelper.class);
    protected SubmitFeedXmlStrategy<AmazonListingAsinBindRecord> xmlStrategy = SpringUtils.getBean(ProductRestoreRelationFeedXmlStrategy.class);
    protected AmazonProcessReportService amazonProcessReportService = SpringUtils.getBean(AmazonProcessReportService.class);
    /**
     * 延时任务转发线程
     */
    public static final AmazonDelayTaskDispatcher delayTaskDispatcher = new AmazonDelayTaskDispatcher(200, 2000);

    /**
     * 账号
     */
    protected AmazonAccount account;
    /**
     * 为线程之间切换保留用户数据
     */
    private String username;
    /**
     * 添加任务时间
     */
    private long addTimeValue = System.nanoTime();


    public AmazonProductRestoreRelationPublishCall(String accountNumber){
        this(accountNumber, SpringUtils.getBean(AmazonAccountService.class).queryAmazonAccountByAccountNumber(accountNumber));
    }

    public AmazonProductRestoreRelationPublishCall(String accountNumber, AmazonAccount account){
        Assert.notNull(account, String.format("帐号【%s】为空！", accountNumber));
        Assert.notNull(xmlStrategy, "XML组装策略为空！");
        this.account = account;
        saveThreadLocalData();
        if(StringUtils.isBlank(username)){
            username = DataContextHolder.getUsername();
        }
    }

    public void batchPublish(List<AmazonListingAsinBindRecord> amazonListingAsinBindRecords, List<String> feedTypes) {
        batchPublish(feedTypes, item -> {
            item.setUnitDatas(amazonListingAsinBindRecords);
            return true;
        }, null);
    }

    /**
     *
     * @Description: 批量刊登上传数据
     *
     * @param feedTypes 上传数据类型列表
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @param finishCallBack 结束回调函数
     */
    public void batchPublish(List<String> feedTypes, Function<PublishData<AmazonListingAsinBindRecord>, Boolean> fillPublishData,
                             ERPInvoker finishCallBack) {
        if (CollectionUtils.isEmpty(feedTypes)) {
            log.warn("invoke batchPublish() error, params not satisfy.");
            return;
        }

        List<PublishData<AmazonListingAsinBindRecord>> totalUnitPublishDatas = new ArrayList<>();
        for (String feedType : feedTypes) {
            List<PublishData<AmazonListingAsinBindRecord>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, fillPublishData);
            totalUnitPublishDatas.addAll(unitPublishDatas);
        }
    }

    /**
     *
     * @Description: 构建刊登数据
     *
     * @param feedType 上传数据类型
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     */
    public List<PublishData<AmazonListingAsinBindRecord>> getSuccessUnitPublishDatas(String feedType,
                                                           Function<PublishData<AmazonListingAsinBindRecord>, Boolean> fillPublishData) {
        PublishData<AmazonListingAsinBindRecord> publishData = new PublishData<>();
        publishData.setAccount(account);
        publishData.setFeedType(feedType);
        if (fillPublishData != null && !BooleanUtils.toBoolean(fillPublishData.apply(publishData))) {
            return CommonUtils.emptyList();
        }

        List<PublishData<AmazonListingAsinBindRecord>> unitPublishDatas = new ArrayList<>(publishData.getUnitDatas().size());
        for (AmazonListingAsinBindRecord unitData : publishData.getUnitDatas()) {
            Optional<PublishData<AmazonListingAsinBindRecord>> optional = getSuccessUnitPublishData(publishData, unitData);
            if (optional.isPresent()) {
                unitPublishDatas.add(optional.get());
            }
        }

        return unitPublishDatas;
    }

    public Optional<PublishData<AmazonListingAsinBindRecord>> getSuccessUnitPublishData(PublishData<AmazonListingAsinBindRecord> publishData, AmazonListingAsinBindRecord unitData) {
        PublishData<AmazonListingAsinBindRecord> unitPublishData = new PublishData<>();
        unitPublishData.setAccount(account);
        unitPublishData.setCurrency(publishData.getCurrency());
        String feedType = publishData.getFeedType();
        unitPublishData.setFeedType(feedType);
        unitPublishData.setOperationType(publishData.getOperationType());
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(unitData));

        List<AmazonProcessReport> reports = initProcessReports(unitPublishData);
        unitPublishData.setReports(reports);

        Optional<PublishData<AmazonListingAsinBindRecord>> empty = Optional.empty();

        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return empty;
        }

        if (account == null || "0".equals(account.getAccountStatus())) {
            String msg = account == null ? "账号没有获取到授权信息" : "账号状态为禁用，不执行。";
            finishProcessReports(unitPublishData, false, msg);
            return empty;
        }

        String xml = null;
        try {
            xml = null;
            if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType)){
                xml = getXsdXml(unitPublishData);
            }else if (SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue().equals(feedType)){
                xml = getXsdXml(unitPublishData);
            }
        } catch (Exception e) {
            finishProcessReports(unitPublishData, false, e.getMessage());
            return empty;
        }
        if (StringUtils.isEmpty(xml)) {
            finishProcessReports(unitPublishData, false, "xml文件获取失败");
            return empty;
        }

        // 处理错误的sku报告
        finishErrorProcessReports(unitPublishData);
        // 没有Message和sku的关系，则退出
        if (MapUtils.isEmpty(unitPublishData.getMsgId2SkuMap())) {
            return empty;
        }

        // 设置处理报告为running状态
        amazonProductPublishCallHelper.runProcessReports(reports);

        // 调用SP接口
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        String feedTypeStr = unitPublishData.getFeedType();

        RequestFeedsApiParam request = new RequestFeedsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSpFeedType(SpFeedType.of(feedTypeStr));
        request.setDocId2MessageXmlMap(getDocId2MessageXml(xml, unitPublishData));
        request.setAddTimeValue(addTimeValue);

        ApiResult<String> apiResult = AmazonSpLocalServiceUtils.addFeedsTask(request);

        String taskId = apiResult.getResult();
        if (!apiResult.isSuccess()) {
            // 对于重复添加的任务，记录taskId
            if (StringUtils.isNotEmpty(taskId)) {
                reports.forEach(report -> {
                    report.setTaskId(taskId);
                });
            }
            finishProcessReports(unitPublishData, false, apiResult.getErrorMsg());
            return empty;
        }

        if (StringUtils.isEmpty(taskId)) {
            finishProcessReports(unitPublishData, false, "添加任务失败，taskId为空");
            return empty;
        }

        // 更新处理报告的taskId
        updatePublishDataTaskId(unitPublishData, taskId);

        return Optional.of(unitPublishData);
    }

    public List<AmazonProcessReport> initProcessReports(PublishData<AmazonListingAsinBindRecord> publishData) {
        String feedType = publishData.getFeedType();
        List<AmazonListingAsinBindRecord> amazonVariants = publishData.getUnitDatas();
        List<AmazonProcessReport> reports = new ArrayList<>(1);
        for (AmazonListingAsinBindRecord variant : amazonVariants) {
            Date time = new Date();
            AmazonProcessReport report = new AmazonProcessReport();
            report.setFeedType(feedType);
            report.setAccountNumber(variant.getAccountNumber());
            report.setStatusCode(ProcessingReportStatusCode.Init.name());
            report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
            report.setDataValue(variant.getSellerSku());
            if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType)) {
                report.setRelationType(ProcessingReportTriggleType.BIND_LISTING_PRENT_UPLOAD.name());
            }else {
                report.setRelationType(ProcessingReportTriggleType.BIND_LISTING_RELATIONSHIP.name());
            }
            report.setRelationId(variant.getId().intValue());
            report.setCreationDate(time);
            report.setCreatedBy(StringUtils.defaultIfBlank(username, "admin"));
            amazonProcessReportService.insert(report);
            reports.add(report);
            String[] sellerSkuArray = variant.getChildSellerSkus().split(",");
            if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType)) {
                AmazonProcessReport amazonProcessReport = new AmazonProcessReport();
                amazonProcessReport.setFeedType(feedType);
                amazonProcessReport.setAccountNumber(variant.getAccountNumber());
                amazonProcessReport.setStatusCode(ProcessingReportStatusCode.Init.name());
                amazonProcessReport.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
                amazonProcessReport.setDataValue(sellerSkuArray[0]);
                amazonProcessReport.setRelationType(ProcessingReportTriggleType.BIND_LISTING_RELATIONSHIP.name());
                amazonProcessReport.setRelationId(variant.getId().intValue());
                amazonProcessReport.setCreationDate(time);
                amazonProcessReport.setCreatedBy(StringUtils.defaultIfBlank(username, "admin"));
                amazonProcessReportService.insert(amazonProcessReport);
                reports.add(amazonProcessReport);
            }
        }
        return reports;
    }

    /**
     *
     * @Description: 更新处理报告的taskId
     */
    public void updatePublishDataTaskId(PublishData<AmazonListingAsinBindRecord> publishData, String taskId) {
        publishData.setTaskId(taskId);
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            report.setTaskId(taskId);
        });
        amazonProcessReportService.update(reports);
    }

    /**
     * @Description: 批量设置处理报告为结束状态
     */
    private void finishProcessReports(PublishData<AmazonListingAsinBindRecord> publishData, boolean status, String msg) {
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            amazonProductPublishCallHelper.finshProcessReport(report, status, msg);
        });
        amazonProcessReportService.update(reports);
    }

    /**
     *
     * @Description: 设置错误的处理报告为结束状态
     */
    public void finishErrorProcessReports(PublishData<AmazonListingAsinBindRecord> publishData) {
        List<AmazonProcessReport> reports = publishData.getReports();
        Map<String, String> errorSku2MsgMap = publishData.getErrorSku2MsgMap();
        if (MapUtils.isNotEmpty(errorSku2MsgMap)) {
            List<AmazonProcessReport> errorReports = new ArrayList<AmazonProcessReport>(errorSku2MsgMap.size());
            for (AmazonProcessReport report : reports) {
                String sku = report.getDataValue();
                if (errorSku2MsgMap.containsKey(sku)) {
                    errorReports.add(report);
                    amazonProductPublishCallHelper.finshProcessReport(report, false, errorSku2MsgMap.get(sku));
                }
            }

            amazonProcessReportService.update(errorReports);
        }
    }

    public Map<String, String> getDocId2MessageXml(String xml, PublishData<AmazonListingAsinBindRecord> unitPublishData) {
        Map<Integer, String> msgId2SkuMap = unitPublishData.getMsgId2SkuMap();
        Map<Integer, String> msgId2MessageXmlMap = XsdUtils.splitXmlByMessage(xml);
        Map<String, List<String>> docId2MessageXmlsMap = new HashMap<>(unitPublishData.getSku2ReportMap().size());
        int size = SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue().equals(unitPublishData.getFeedType()) ? 9 : 1;
        msgId2MessageXmlMap.forEach((msgId, messageXml) -> {
            String sku = msgId2SkuMap.get(msgId);
            if (!docId2MessageXmlsMap.containsKey(sku)) {
                docId2MessageXmlsMap.put(sku, new ArrayList<>(size));
            }
            docId2MessageXmlsMap.get(sku).add(messageXml);
        });

        Map<String, String> docId2MessageXmlMap = new HashMap<>(docId2MessageXmlsMap.size());
        docId2MessageXmlsMap.forEach((k, v) -> {
            docId2MessageXmlMap.put(k, JSON.toJSONString(v));
        });

        return docId2MessageXmlMap;
    }

    /**
     * @Description: 保存ThreadLocal数据
     */
    private void saveThreadLocalData() {
        if (StringUtils.isEmpty(username)) {
            this.username = WebUtils.getUserName();
        }
    }

    public String getXsdXml(PublishData<AmazonListingAsinBindRecord> publishData) {
        String xml = null;
        String feedType = publishData.getFeedType();
        switch (SpFeedType.of(feedType)) {
            // 主产品使用Update，需要绑定的子产品使用PartialUpdate
            case POST_PRODUCT_DATA:
                publishData.setOperationType(OperationType.Update);
                xml = xmlStrategy.transferProduct2Xml(publishData);
                break;
            case POST_PRODUCT_RELATIONSHIP_DATA:
                publishData.setOperationType(OperationType.PartialUpdate);
                xml = xmlStrategy.transferProductRelationship2Xml(publishData);
                break;
            default:
                break;
        }
       // log.info("xml:{}",xml);
        return xml;
    }
}
