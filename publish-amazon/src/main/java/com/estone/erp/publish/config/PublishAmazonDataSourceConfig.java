package com.estone.erp.publish.config;

import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import com.estone.erp.publish.mybatis.DataSources;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.sql.DataSource;

/***
 * publish_amazon数据库配置类
 */
@Configuration
@Order(2)
@Slf4j
@MapperScan(basePackages = { "com.estone.erp.publish.publishAmazon.mapper" ,"com.estone.erp.publish.amazon.**.mapper",
        "com.estone.erp.publish.base.**.mapper","com.estone.erp.publish.feginService.**.mapper",
        "com.estone.erp.publish.platform.**.mapper","com.estone.erp.publish.system.**.mapper" },
        sqlSessionFactoryRef = DataSources.PUBLISH_AMAZON_FAC)
//@ComponentScan(excludeFilters = @ComponentScan.Filter(LocalAmazonMapper.class))
@ConfigurationProperties(prefix = "mybatis.publish-amazon")
public class PublishAmazonDataSourceConfig extends AbstractDataSourceConfig {

    @Bean(DataSources.PUBLISH_AMAZON_DS)
    @ConfigurationProperties(prefix = "spring.datasource.publish-amazon")
    public DataSource dataSource() {
        log.info("===========加载数据源:spring.datasource.publish-amazon========");
        return getDataSource();
    }

    @Bean(DataSources.PUBLISH_AMAZON_FAC)
    public SqlSessionFactory sqlSessionFactory() {
        return getSqlSessionFactory(dataSource());
    }

    @Bean(DataSources.PUBLISH_AMAZON_TEMP)
    public SqlSessionTemplate sqlSessionTemplate() {
        return getSqlSessionTemplate(sqlSessionFactory());
    }
}
