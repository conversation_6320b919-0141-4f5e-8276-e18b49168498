package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonProductMonitorExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String columns;

    public AmazonProductMonitorExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameIsNull() {
            addCriterion("purple_bird_name is null");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameIsNotNull() {
            addCriterion("purple_bird_name is not null");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameEqualTo(String value) {
            addCriterion("purple_bird_name =", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameNotEqualTo(String value) {
            addCriterion("purple_bird_name <>", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameGreaterThan(String value) {
            addCriterion("purple_bird_name >", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameGreaterThanOrEqualTo(String value) {
            addCriterion("purple_bird_name >=", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameLessThan(String value) {
            addCriterion("purple_bird_name <", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameLessThanOrEqualTo(String value) {
            addCriterion("purple_bird_name <=", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameLike(String value) {
            addCriterion("purple_bird_name like", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameNotLike(String value) {
            addCriterion("purple_bird_name not like", value, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameIn(List<String> values) {
            addCriterion("purple_bird_name in", values, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameNotIn(List<String> values) {
            addCriterion("purple_bird_name not in", values, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameBetween(String value1, String value2) {
            addCriterion("purple_bird_name between", value1, value2, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andPurpleBirdNameNotBetween(String value1, String value2) {
            addCriterion("purple_bird_name not between", value1, value2, "purpleBirdName");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNull() {
            addCriterion("sale_id is null");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNotNull() {
            addCriterion("sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andSaleIdEqualTo(String value) {
            addCriterion("sale_id =", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotEqualTo(String value) {
            addCriterion("sale_id <>", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThan(String value) {
            addCriterion("sale_id >", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThanOrEqualTo(String value) {
            addCriterion("sale_id >=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThan(String value) {
            addCriterion("sale_id <", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThanOrEqualTo(String value) {
            addCriterion("sale_id <=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLike(String value) {
            addCriterion("sale_id like", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotLike(String value) {
            addCriterion("sale_id not like", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdIn(List<String> values) {
            addCriterion("sale_id in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotIn(List<String> values) {
            addCriterion("sale_id not in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdBetween(String value1, String value2) {
            addCriterion("sale_id between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotBetween(String value1, String value2) {
            addCriterion("sale_id not between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNull() {
            addCriterion("parent_asin is null");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNotNull() {
            addCriterion("parent_asin is not null");
            return (Criteria) this;
        }

        public Criteria andParentAsinEqualTo(String value) {
            addCriterion("parent_asin =", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotEqualTo(String value) {
            addCriterion("parent_asin <>", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThan(String value) {
            addCriterion("parent_asin >", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThanOrEqualTo(String value) {
            addCriterion("parent_asin >=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThan(String value) {
            addCriterion("parent_asin <", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThanOrEqualTo(String value) {
            addCriterion("parent_asin <=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLike(String value) {
            addCriterion("parent_asin like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotLike(String value) {
            addCriterion("parent_asin not like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinIn(List<String> values) {
            addCriterion("parent_asin in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotIn(List<String> values) {
            addCriterion("parent_asin not in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinBetween(String value1, String value2) {
            addCriterion("parent_asin between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotBetween(String value1, String value2) {
            addCriterion("parent_asin not between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNull() {
            addCriterion("son_asin is null");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNotNull() {
            addCriterion("son_asin is not null");
            return (Criteria) this;
        }

        public Criteria andSonAsinEqualTo(String value) {
            addCriterion("son_asin =", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotEqualTo(String value) {
            addCriterion("son_asin <>", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThan(String value) {
            addCriterion("son_asin >", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThanOrEqualTo(String value) {
            addCriterion("son_asin >=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThan(String value) {
            addCriterion("son_asin <", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThanOrEqualTo(String value) {
            addCriterion("son_asin <=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLike(String value) {
            addCriterion("son_asin like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotLike(String value) {
            addCriterion("son_asin not like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIn(List<String> values) {
            addCriterion("son_asin in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotIn(List<String> values) {
            addCriterion("son_asin not in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinBetween(String value1, String value2) {
            addCriterion("son_asin between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotBetween(String value1, String value2) {
            addCriterion("son_asin not between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNull() {
            addCriterion("main_sku is null");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNotNull() {
            addCriterion("main_sku is not null");
            return (Criteria) this;
        }

        public Criteria andMainSkuEqualTo(String value) {
            addCriterion("main_sku =", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotEqualTo(String value) {
            addCriterion("main_sku <>", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThan(String value) {
            addCriterion("main_sku >", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThanOrEqualTo(String value) {
            addCriterion("main_sku >=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThan(String value) {
            addCriterion("main_sku <", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThanOrEqualTo(String value) {
            addCriterion("main_sku <=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLike(String value) {
            addCriterion("main_sku like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotLike(String value) {
            addCriterion("main_sku not like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuIn(List<String> values) {
            addCriterion("main_sku in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotIn(List<String> values) {
            addCriterion("main_sku not in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuBetween(String value1, String value2) {
            addCriterion("main_sku between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotBetween(String value1, String value2) {
            addCriterion("main_sku not between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andEsListingIdIsNull() {
            addCriterion("es_listing_id is null");
            return (Criteria) this;
        }

        public Criteria andEsListingIdIsNotNull() {
            addCriterion("es_listing_id is not null");
            return (Criteria) this;
        }

        public Criteria andEsListingIdEqualTo(String value) {
            addCriterion("es_listing_id =", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdNotEqualTo(String value) {
            addCriterion("es_listing_id <>", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdGreaterThan(String value) {
            addCriterion("es_listing_id >", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdGreaterThanOrEqualTo(String value) {
            addCriterion("es_listing_id >=", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdLessThan(String value) {
            addCriterion("es_listing_id <", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdLessThanOrEqualTo(String value) {
            addCriterion("es_listing_id <=", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdLike(String value) {
            addCriterion("es_listing_id like", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdNotLike(String value) {
            addCriterion("es_listing_id not like", value, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdIn(List<String> values) {
            addCriterion("es_listing_id in", values, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdNotIn(List<String> values) {
            addCriterion("es_listing_id not in", values, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdBetween(String value1, String value2) {
            addCriterion("es_listing_id between", value1, value2, "esListingId");
            return (Criteria) this;
        }

        public Criteria andEsListingIdNotBetween(String value1, String value2) {
            addCriterion("es_listing_id not between", value1, value2, "esListingId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdIsNull() {
            addCriterion("monitor_id is null");
            return (Criteria) this;
        }

        public Criteria andMonitorIdIsNotNull() {
            addCriterion("monitor_id is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorIdEqualTo(Integer value) {
            addCriterion("monitor_id =", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdNotEqualTo(Integer value) {
            addCriterion("monitor_id <>", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdGreaterThan(Integer value) {
            addCriterion("monitor_id >", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("monitor_id >=", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdLessThan(Integer value) {
            addCriterion("monitor_id <", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdLessThanOrEqualTo(Integer value) {
            addCriterion("monitor_id <=", value, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdIn(List<Integer> values) {
            addCriterion("monitor_id in", values, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdNotIn(List<Integer> values) {
            addCriterion("monitor_id not in", values, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdBetween(Integer value1, Integer value2) {
            addCriterion("monitor_id between", value1, value2, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("monitor_id not between", value1, value2, "monitorId");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNull() {
            addCriterion("monitor_type is null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNotNull() {
            addCriterion("monitor_type is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeEqualTo(Integer value) {
            addCriterion("monitor_type =", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotEqualTo(Integer value) {
            addCriterion("monitor_type <>", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThan(Integer value) {
            addCriterion("monitor_type >", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("monitor_type >=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThan(Integer value) {
            addCriterion("monitor_type <", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThanOrEqualTo(Integer value) {
            addCriterion("monitor_type <=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIn(List<Integer> values) {
            addCriterion("monitor_type in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotIn(List<Integer> values) {
            addCriterion("monitor_type not in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeBetween(Integer value1, Integer value2) {
            addCriterion("monitor_type between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("monitor_type not between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorContentIsNull() {
            addCriterion("monitor_content is null");
            return (Criteria) this;
        }

        public Criteria andMonitorContentIsNotNull() {
            addCriterion("monitor_content is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorContentEqualTo(String value) {
            addCriterion("monitor_content =", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentNotEqualTo(String value) {
            addCriterion("monitor_content <>", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentGreaterThan(String value) {
            addCriterion("monitor_content >", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentGreaterThanOrEqualTo(String value) {
            addCriterion("monitor_content >=", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentLessThan(String value) {
            addCriterion("monitor_content <", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentLessThanOrEqualTo(String value) {
            addCriterion("monitor_content <=", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentLike(String value) {
            addCriterion("monitor_content like", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentNotLike(String value) {
            addCriterion("monitor_content not like", value, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentIn(List<String> values) {
            addCriterion("monitor_content in", values, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentNotIn(List<String> values) {
            addCriterion("monitor_content not in", values, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentBetween(String value1, String value2) {
            addCriterion("monitor_content between", value1, value2, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andMonitorContentNotBetween(String value1, String value2) {
            addCriterion("monitor_content not between", value1, value2, "monitorContent");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkIsNull() {
            addCriterion("offline_remark is null");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkIsNotNull() {
            addCriterion("offline_remark is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkEqualTo(String value) {
            addCriterion("offline_remark =", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkNotEqualTo(String value) {
            addCriterion("offline_remark <>", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkGreaterThan(String value) {
            addCriterion("offline_remark >", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("offline_remark >=", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkLessThan(String value) {
            addCriterion("offline_remark <", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkLessThanOrEqualTo(String value) {
            addCriterion("offline_remark <=", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkLike(String value) {
            addCriterion("offline_remark like", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkNotLike(String value) {
            addCriterion("offline_remark not like", value, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkIn(List<String> values) {
            addCriterion("offline_remark in", values, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkNotIn(List<String> values) {
            addCriterion("offline_remark not in", values, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkBetween(String value1, String value2) {
            addCriterion("offline_remark between", value1, value2, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andOfflineRemarkNotBetween(String value1, String value2) {
            addCriterion("offline_remark not between", value1, value2, "offlineRemark");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNull() {
            addCriterion("forbid_channel is null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNotNull() {
            addCriterion("forbid_channel is not null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelEqualTo(String value) {
            addCriterion("forbid_channel =", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotEqualTo(String value) {
            addCriterion("forbid_channel <>", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThan(String value) {
            addCriterion("forbid_channel >", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_channel >=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThan(String value) {
            addCriterion("forbid_channel <", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThanOrEqualTo(String value) {
            addCriterion("forbid_channel <=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLike(String value) {
            addCriterion("forbid_channel like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotLike(String value) {
            addCriterion("forbid_channel not like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIn(List<String> values) {
            addCriterion("forbid_channel in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotIn(List<String> values) {
            addCriterion("forbid_channel not in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelBetween(String value1, String value2) {
            addCriterion("forbid_channel between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotBetween(String value1, String value2) {
            addCriterion("forbid_channel not between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameIsNull() {
            addCriterion("infringement_typename is null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameIsNotNull() {
            addCriterion("infringement_typename is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameEqualTo(String value) {
            addCriterion("infringement_typename =", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotEqualTo(String value) {
            addCriterion("infringement_typename <>", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameGreaterThan(String value) {
            addCriterion("infringement_typename >", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_typename >=", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLessThan(String value) {
            addCriterion("infringement_typename <", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLessThanOrEqualTo(String value) {
            addCriterion("infringement_typename <=", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLike(String value) {
            addCriterion("infringement_typename like", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotLike(String value) {
            addCriterion("infringement_typename not like", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameIn(List<String> values) {
            addCriterion("infringement_typename in", values, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotIn(List<String> values) {
            addCriterion("infringement_typename not in", values, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameBetween(String value1, String value2) {
            addCriterion("infringement_typename between", value1, value2, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotBetween(String value1, String value2) {
            addCriterion("infringement_typename not between", value1, value2, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNull() {
            addCriterion("infringement_obj is null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNotNull() {
            addCriterion("infringement_obj is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjEqualTo(String value) {
            addCriterion("infringement_obj =", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotEqualTo(String value) {
            addCriterion("infringement_obj <>", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThan(String value) {
            addCriterion("infringement_obj >", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_obj >=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThan(String value) {
            addCriterion("infringement_obj <", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThanOrEqualTo(String value) {
            addCriterion("infringement_obj <=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLike(String value) {
            addCriterion("infringement_obj like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotLike(String value) {
            addCriterion("infringement_obj not like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIn(List<String> values) {
            addCriterion("infringement_obj in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotIn(List<String> values) {
            addCriterion("infringement_obj not in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjBetween(String value1, String value2) {
            addCriterion("infringement_obj between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotBetween(String value1, String value2) {
            addCriterion("infringement_obj not between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIsNull() {
            addCriterion("prohibition_site is null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIsNotNull() {
            addCriterion("prohibition_site is not null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteEqualTo(String value) {
            addCriterion("prohibition_site =", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotEqualTo(String value) {
            addCriterion("prohibition_site <>", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteGreaterThan(String value) {
            addCriterion("prohibition_site >", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteGreaterThanOrEqualTo(String value) {
            addCriterion("prohibition_site >=", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLessThan(String value) {
            addCriterion("prohibition_site <", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLessThanOrEqualTo(String value) {
            addCriterion("prohibition_site <=", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLike(String value) {
            addCriterion("prohibition_site like", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotLike(String value) {
            addCriterion("prohibition_site not like", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIn(List<String> values) {
            addCriterion("prohibition_site in", values, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotIn(List<String> values) {
            addCriterion("prohibition_site not in", values, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteBetween(String value1, String value2) {
            addCriterion("prohibition_site between", value1, value2, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotBetween(String value1, String value2) {
            addCriterion("prohibition_site not between", value1, value2, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountIsNull() {
            addCriterion("order_last_24h_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountIsNotNull() {
            addCriterion("order_last_24h_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountEqualTo(Integer value) {
            addCriterion("order_last_24h_count =", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountNotEqualTo(Integer value) {
            addCriterion("order_last_24h_count <>", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountGreaterThan(Integer value) {
            addCriterion("order_last_24h_count >", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_24h_count >=", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountLessThan(Integer value) {
            addCriterion("order_last_24h_count <", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_24h_count <=", value, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountIn(List<Integer> values) {
            addCriterion("order_last_24h_count in", values, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountNotIn(List<Integer> values) {
            addCriterion("order_last_24h_count not in", values, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_24h_count between", value1, value2, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast24hCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_24h_count not between", value1, value2, "orderLast24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNull() {
            addCriterion("order_last_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNotNull() {
            addCriterion("order_last_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountEqualTo(Integer value) {
            addCriterion("order_last_7d_count =", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotEqualTo(Integer value) {
            addCriterion("order_last_7d_count <>", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThan(Integer value) {
            addCriterion("order_last_7d_count >", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count >=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThan(Integer value) {
            addCriterion("order_last_7d_count <", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count <=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIn(List<Integer> values) {
            addCriterion("order_last_7d_count in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotIn(List<Integer> values) {
            addCriterion("order_last_7d_count not in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count not between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNull() {
            addCriterion("order_last_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNotNull() {
            addCriterion("order_last_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountEqualTo(Integer value) {
            addCriterion("order_last_14d_count =", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotEqualTo(Integer value) {
            addCriterion("order_last_14d_count <>", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThan(Integer value) {
            addCriterion("order_last_14d_count >", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count >=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThan(Integer value) {
            addCriterion("order_last_14d_count <", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count <=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIn(List<Integer> values) {
            addCriterion("order_last_14d_count in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotIn(List<Integer> values) {
            addCriterion("order_last_14d_count not in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count not between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNull() {
            addCriterion("order_last_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNotNull() {
            addCriterion("order_last_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountEqualTo(Integer value) {
            addCriterion("order_last_30d_count =", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotEqualTo(Integer value) {
            addCriterion("order_last_30d_count <>", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThan(Integer value) {
            addCriterion("order_last_30d_count >", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count >=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThan(Integer value) {
            addCriterion("order_last_30d_count <", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count <=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIn(List<Integer> values) {
            addCriterion("order_last_30d_count in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotIn(List<Integer> values) {
            addCriterion("order_last_30d_count not in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count not between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNull() {
            addCriterion("order_num_total is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNotNull() {
            addCriterion("order_num_total is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalEqualTo(Integer value) {
            addCriterion("order_num_total =", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotEqualTo(Integer value) {
            addCriterion("order_num_total <>", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThan(Integer value) {
            addCriterion("order_num_total >", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_total >=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThan(Integer value) {
            addCriterion("order_num_total <", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_total <=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIn(List<Integer> values) {
            addCriterion("order_num_total in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotIn(List<Integer> values) {
            addCriterion("order_num_total not in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total not between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNull() {
            addCriterion("open_date is null");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNotNull() {
            addCriterion("open_date is not null");
            return (Criteria) this;
        }

        public Criteria andOpenDateEqualTo(Timestamp value) {
            addCriterion("open_date =", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotEqualTo(Timestamp value) {
            addCriterion("open_date <>", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThan(Timestamp value) {
            addCriterion("open_date >", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("open_date >=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThan(Timestamp value) {
            addCriterion("open_date <", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("open_date <=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateIn(List<Timestamp> values) {
            addCriterion("open_date in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotIn(List<Timestamp> values) {
            addCriterion("open_date not in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_date between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_date not between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNull() {
            addCriterion("offline_date is null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNotNull() {
            addCriterion("offline_date is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateEqualTo(Timestamp value) {
            addCriterion("offline_date =", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotEqualTo(Timestamp value) {
            addCriterion("offline_date <>", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThan(Timestamp value) {
            addCriterion("offline_date >", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date >=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThan(Timestamp value) {
            addCriterion("offline_date <", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date <=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIn(List<Timestamp> values) {
            addCriterion("offline_date in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotIn(List<Timestamp> values) {
            addCriterion("offline_date not in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date not between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}