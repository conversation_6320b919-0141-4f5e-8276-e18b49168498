package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 同步单品信息到listing
 * <AUTHOR>
 * @date 2022/5/30 15:45
 */
@Slf4j
@Component
public class SyncAmazonProductInfoMqListener {

    @Resource
    private AmazonProductListingService amazonProductListingService;

    @Resource
    private ChangeSkuLogService changeSkuLogService;

    private static final int MAX_RETRY_COUNT = 3;

    @RabbitListener(queues = PublishQueues.AMAZON_SYNC_PRODUCT_INFO_QUEUE, containerFactory = "batchAntifollowConsumeFactory")
    public void syncAmazonProductInfo(Message message, Channel channel) throws IOException {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }

            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_AMAZON);
            }
        } catch (Exception e) {
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_AMAZON);
            log.error("amazon AMAZON_SYNC_PRODUCT_INFO_QUEUE Exception error: {}", e.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        List<String> accountNumberList = changeSku.getAccountNumberList();
        amazonProductListingService.syncProductInfo(skuList, accountNumberList);

        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }
}
