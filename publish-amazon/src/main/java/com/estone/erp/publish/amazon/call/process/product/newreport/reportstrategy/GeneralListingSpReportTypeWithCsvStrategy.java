package com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import io.swagger.client.enums.ReportType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *  普通多数站点的report解析
 */
@Slf4j
public class GeneralListingSpReportTypeWithCsvStrategy extends BaseSpReportTypeWithCsvStrategy {

    public static final List<String> MARKETPLACES = Arrays.asList("US", "CA", "UK", "DE", "IT", "ES", "MX", "AE", "NL", "BR", "TR", "SE", "PL", "FR","AU","BE","IE");

    public static final String REPORT_TYPE = ReportType.GET_MERCHANT_LISTINGS_ALL_DATA.getName();

    public static final String SELLER_SKU  = "seller-sku";

    public GeneralListingSpReportTypeWithCsvStrategy(AmazonAccount account, String reportType) {
        super(account, reportType, AmazonConstant.getMarketplaceIdsByMarketplaces(MARKETPLACES.toArray(new String[MARKETPLACES.size()])));
    }

    @Override
    public List<String[]> filterAndTransferLines(List<String> lines, Function<String[], Boolean> filter) {
        if (CollectionUtils.isEmpty(lines)) {
            return CommonUtils.emptyList();
        }

        try {
            String accountNumber = getSyncSpProductData().getAccount().getAccountNumber();
            log.warn("[{}] has {} reportLines.", accountNumber, lines.size());
            // 过滤数据
            List<String[]> lineSplits = lines.stream()
                    .filter(row -> StringUtils.isNotBlank(row))
                    .map(row -> StringUtils.splitPreserveAllTokens(row, "\t"))
                    .filter(splits -> {
                        return filter == null ? true : BooleanUtils.toBoolean(filter.apply(splits));
                    }).collect(Collectors.toList());
            log.warn("[{}]  has {} reportLines after fitler.", accountNumber, lineSplits.size());
            return lineSplits;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return CommonUtils.emptyList();
    }

    @Override
    public void fillLineData2AmazonProductListing(AmazonProductListing amazonProductListing, String[] splits) {
        if (!checkParams(amazonProductListing, splits)) {
            return;
        }
        amazonProductListing.setItemName(getColumnValue("item-name", splits));
        amazonProductListing.setItemDescription(getColumnValue("item-description", splits));
        amazonProductListing.setListingId(getColumnValue("listing-id", splits));
        String sellerSku = getColumnValue("seller-sku", splits);
        amazonProductListing.setSellerSku(sellerSku);
        amazonProductListing.setAttribute2(StringUtils.upperCase(sellerSku));
        String price = getColumnValue("price", splits);
        if (StringUtils.isNotEmpty(price)) {
            if (price.contains(",")) {
                price = price.replaceAll(",", ".");
            }
            amazonProductListing.setPrice(Double.valueOf(price));
        }
        String quantity = getColumnValue("quantity", splits);
        if (StringUtils.isNotEmpty(quantity)) {
            amazonProductListing.setQuantity(Integer.valueOf(quantity));
        }
        if (ObjectUtils.isEmpty(amazonProductListing.getQuantity())) {
            amazonProductListing.setQuantity(0);
        }

        String dataStr = getColumnValue("open-date", splits);
        Date date = AmazonUtils.getDate(dataStr,amazonProductListing.getAccountNumber());
        amazonProductListing.setReportOpenDate(dataStr);
        if (date != null) {
            amazonProductListing.setOpenDate(date);
        }
        amazonProductListing.setMainImage(getColumnValue("image-url", splits));
        amazonProductListing.setItemIsMarketplace(getColumnValue("item-is-marketplace", splits));

        Integer productIdType = null;
        String productIdTypeStr = getColumnValue("product-id-type", splits);
        try {
            productIdType = Integer.valueOf(productIdTypeStr);
        } catch (Exception e) {
            //log.error("构建单条数据productIdType异常" + amazonProductListing.getSellerSku() + amazonProductListing.getAccountNumber()+ "productIdType " + productIdTypeStr );
        }
        amazonProductListing.setProductIdType(productIdType);
        amazonProductListing.setItemCondition(getColumnValue("item-condition", splits));
        amazonProductListing.setZshopCategory(getColumnValue("zshop-category1", splits));
        amazonProductListing.setSonAsin(getColumnValue("asin1", splits));
        amazonProductListing.setProductId(getColumnValue("product-id", splits));
        amazonProductListing.setMerchantShippingGroup(getColumnValue("merchant-shipping-group", splits));
        amazonProductListing.setItemStatus(getColumnValue("status", splits));
    }

    @Override
    protected String getTableSellerSku() {
        return SELLER_SKU;
    }
}
