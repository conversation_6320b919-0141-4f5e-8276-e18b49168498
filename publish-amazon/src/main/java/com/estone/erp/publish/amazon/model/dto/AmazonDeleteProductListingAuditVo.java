package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.amazon.model.converter.AuditStatusConverter;
import com.estone.erp.publish.component.converter.SkuStatusCodeConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class AmazonDeleteProductListingAuditVo {

    /**
     *
     */
    @ExcelIgnore
    private Integer id;

    /**
     * 账号
     */
    @ExcelProperty("店铺")
    private String accountNumber;

    /**
     * 父asin码
     */
    @ExcelProperty("父asin码")
    private String parentAsin;

    /**
     * 子asin码
     */
    @ExcelProperty("子asin码")
    private String sonAsin;

    /**
     * 卖家sku
     */
    @ExcelProperty("sellerSku")
    private String sellerSku;

    /**
     * 主sku
     */
    @ExcelProperty("主sku")
    private String mainSku;

    /**
     * 货号
     */
    @ExcelProperty("货号")
    private String articleNumber;

    /**
     * 价格
     */
    @ExcelProperty("价格")
    private Double price;

    /**
     * 在售 true下架 false
     */
    @ExcelProperty("是否在线")
    private Boolean isOnline;

    /**
     * 禁售平台(逗号拼接)
     */
    @ExcelProperty("禁售平台")
    private String forbidChannel;

    /**
     * 禁售类型
     */
    @ExcelProperty("禁售类型")
    private String infringementTypename;

    /**
     * 禁售原因
     */
    @ExcelProperty("禁售原因")
    private String infringementObj;

    /**
     * 禁售站点
     */
    @ExcelProperty("禁售站点")
    private String prohibitionSite;

    /**
     * 单品状态
     */
    @ExcelProperty(value = "单品状态", converter = SkuStatusCodeConverter.class)
    private String skuStatus;

    /**
     * 主图
     */
    @ExcelIgnore
    private String mainImage;

    /**
     * 24小时销量
     */
    @ExcelProperty("24小时销量")
    private Integer order24hCount;

    /**
     * 7天销量
     */
    @ExcelProperty("7天销量")
    private Integer orderLast7dCount;

    /**
     * 14天销量
     */
    @ExcelProperty("14天销量")
    private Integer orderLast14dCount;

    /**
     * 30天销量
     */
    @ExcelProperty("30天销量")
    private Integer orderLast30dCount;

    /**
     * 总销量
     */
    @ExcelProperty("总销量")
    private Integer orderNumTotal;

    /**
     * 最新下线时间
     */
    @ExcelProperty(value = "最新下线时间", converter = TimestampFormatConverter.class)
    private Timestamp offlineDate;

    /**
     * 审核状态
     *
     * 1 待组长审核 2 代主管审核 3 审核通过已下架 4 审核不通过
     */
    @ExcelProperty(value = "审核状态", converter = AuditStatusConverter.class)
    private Integer status;

    /**
     * 下架的原因
     */
    @ExcelProperty("下架原因")
    private String offlineReason;

    /**
     * 店铺销售id
     */
    @ExcelProperty("店铺销售id")
    private String salesId;

    /**
     * 提交审核时间
     */
    @ExcelProperty(value = "提交审核时间", converter = TimestampFormatConverter.class)
    private Timestamp submitTime;

    /**
     * 提交人
     */
    @ExcelProperty("提交人")
    private String submitBy;

    /**
     * 销售组长审核人
     */
    @ExcelProperty("销售组长审核人")
    private String salesTeamLeaderAuditBy;

    /**
     * 销售组长审核时间
     */
    @ExcelProperty(value = "销售组长审核时间", converter = TimestampFormatConverter.class)
    private Timestamp salesTeamLeaderAuditTime;

    /**
     * 销售主管审核人
     */
    @ExcelProperty("销售主管审核人")
    private String salesSupervisorAuditBy;

    /**
     * 销售主管审核时间
     */
    @ExcelProperty(value = "销售主管审核时间", converter = TimestampFormatConverter.class)
    private Timestamp salesSupervisorAuditTime;

    /**
     * 审核不通过原因
     */
    @ExcelProperty("审核不通过原因")
    private String auditReason;

    /**
     * es listing同步到删除审核列表的时间
     */
    @ExcelProperty(value = "listing同步到删除审核列表的时间", converter = TimestampFormatConverter.class)
    private Timestamp syncDate;

    /**
     * 创建人
     */
    @ExcelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", converter = TimestampFormatConverter.class)
    private Timestamp createDate;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间", converter = TimestampFormatConverter.class)
    private Timestamp updateDate;

    /**
     * 修改人
     */
    @ExcelProperty("修改人")
    private String updatedBy;

    /**
     * 销售
     */
    @ExcelProperty(value = "销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty(value = "销售组长")
    private String salesTeamLeader;

    /**
     * 主管
     */
    @ExcelProperty(value = "主管")
    private String salesSupervisorName;

}
