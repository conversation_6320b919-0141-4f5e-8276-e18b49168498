package com.estone.erp.publish.amazon.hbase.dao;

import com.estone.erp.publish.amazon.hbase.model.StudentModel;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/4 15:57
 * @description
 */
/* public interface StudentDao {

   @Select("select * from \"student\"")
    List<StudentModel> selectAll();

    @Select("select * from \"student\" where \"name\"=#{name}")
    List<StudentModel> selectStudent(String name);

}*/
