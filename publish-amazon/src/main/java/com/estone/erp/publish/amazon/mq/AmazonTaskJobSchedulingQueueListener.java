package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.scheduler.AmazonSchedulerTaskFactory;
import com.estone.erp.publish.amazon.componet.scheduler.AmazonSchedulerTaskService;
import com.estone.erp.publish.amazon.mq.model.AmazonSchedulerTaskJobMessage;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Amazon 任务作业调度队列
 *
 * <AUTHOR>
 * @date 2024-10-29 上午11:03
 */
@Slf4j
@Component
public class AmazonTaskJobSchedulingQueueListener implements ChannelAwareMessageListener {
    @Autowired
    private AmazonSchedulerTaskFactory amazonSchedulerTaskFactory;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        AmazonSchedulerTaskJobMessage messageData = JSON.parseObject(new String(message.getBody()), AmazonSchedulerTaskJobMessage.class);
        try {
            AmazonSchedulerTaskService service = amazonSchedulerTaskFactory.getService(messageData.getScheduleTaskType());
            ApiResult<String> taskResult = service.executeTask(messageData);
            if (taskResult.isSuccess()) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        } catch (Exception e) {
            log.error("Exception error: {}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

}
