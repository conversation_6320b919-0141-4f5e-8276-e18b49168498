package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.configlog.AmazonMarketingConfigLogSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMarketingConfigLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * amazon市场更改配置日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/amazonMarketingConfigLog")
public class AmazonMarketingConfigLogController {
    @Resource
    private AmazonMarketingConfigLogService amazonMarketingConfigLogService;

    /**
     * 查询操作日志列表
     */
    @PostMapping("/search")
    public ApiResult<IPage<AmazonMarketingConfigLog>> search(@RequestBody AmazonMarketingConfigLogSearchDTO searchParam) {
        return amazonMarketingConfigLogService.search(searchParam);
    }
}
