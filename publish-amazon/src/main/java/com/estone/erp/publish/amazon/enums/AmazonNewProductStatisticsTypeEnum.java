package com.estone.erp.publish.amazon.enums;

public enum AmazonNewProductStatisticsTypeEnum {

    sale(1, "销售"),

    platform(2, "平台"),

    editor(3, "文案");

    private int code;

    private String name;

    private AmazonNewProductStatisticsTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AmazonNewProductStatisticsTypeEnum build(int code) {
        AmazonNewProductStatisticsTypeEnum[] values = values();
        for (AmazonNewProductStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AmazonNewProductStatisticsTypeEnum[] values = values();
        for (AmazonNewProductStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }

}
