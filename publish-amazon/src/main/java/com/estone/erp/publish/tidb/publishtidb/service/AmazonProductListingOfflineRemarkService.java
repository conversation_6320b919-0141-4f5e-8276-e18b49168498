package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemark;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemarkCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemarkExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-16 16:46:32
 */
public interface AmazonProductListingOfflineRemarkService {
    int countByExample(AmazonProductListingOfflineRemarkExample example);

    CQueryResult<AmazonProductListingOfflineRemark> search(CQuery<AmazonProductListingOfflineRemarkCriteria> cquery);

    List<AmazonProductListingOfflineRemark> selectByExample(AmazonProductListingOfflineRemarkExample example);

    AmazonProductListingOfflineRemark selectByPrimaryKey(Long id);

    int insert(AmazonProductListingOfflineRemark record);

    int updateByPrimaryKeySelective(AmazonProductListingOfflineRemark record);

    int updateByExampleSelective(AmazonProductListingOfflineRemark record, AmazonProductListingOfflineRemarkExample example);

    int deleteByPrimaryKey(List<Long> ids);
}