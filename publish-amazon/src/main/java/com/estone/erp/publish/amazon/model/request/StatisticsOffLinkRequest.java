package com.estone.erp.publish.amazon.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StatisticsOffLinkRequest {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime statisticalDate;

    private List<String> accountNumbers;

    private Integer taskType;

    private Double limitRatio;
}
