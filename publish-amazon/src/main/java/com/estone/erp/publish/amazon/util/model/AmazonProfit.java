package com.estone.erp.publish.amazon.util.model;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonSkuGrossProfitParam;
import com.estone.erp.publish.common.util.Env;
import com.estone.erp.publish.common.util.HttpUtils;
import org.springframework.http.HttpMethod;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AmazonProfit {
    public static Map<String, String> countryMap;
    static {
        countryMap = new HashMap<String, String>();
        countryMap.put("US", "USD");
        countryMap.put("CA", "CAD");
        countryMap.put("UK", "GBP");
        countryMap.put("FR", "EUR");
        countryMap.put("DE", "EUR");
        countryMap.put("IT", "EUR");
        countryMap.put("ES", "EUR");
        countryMap.put("JP", "JPY");
        countryMap.put("IN", "INR");
        countryMap.put("AU", "AUD");
        countryMap.put("CN", "CNY");
        countryMap.put("MX", "USD");
        countryMap.put("NL", "EUR");
    }
    public static Map<String, String> shippingMethodMap;
    static {
        shippingMethodMap = new HashMap<String, String>();
        shippingMethodMap.put("US", "WPPY-SZ");
        shippingMethodMap.put("CA", "OFFLINE_EUB_CS");
        shippingMethodMap.put("UK", "CNDWA");
        shippingMethodMap.put("FR", "CNDWA");
        shippingMethodMap.put("DE", "DEZXA");
        shippingMethodMap.put("IT", "4XP-ITZX");
        shippingMethodMap.put("ES", "4XP-SPPY");
        shippingMethodMap.put("JP", "CNDWA");
        shippingMethodMap.put("IN", "CNDWA");
    }
    public static ApiResult<String> calAmazonSkuGrossProfit(AmazonSkuGrossProfitParam grossProfitParam) {
        HttpParams<String> httpParams = new HttpParams<>();
        // 调pms接口
        httpParams.setUrl(Env.PRODUCT_JSON_SERVICE);
        httpParams.setHttpMethod(HttpMethod.POST);
        List<String> params = new ArrayList<>(5);
        params.add(grossProfitParam.getArticleNumber());
        params.add(grossProfitParam.getTotalCost());
        params.add(grossProfitParam.getLogisticsType());
        params.add(grossProfitParam.getCountry());
        params.add(grossProfitParam.getCurrencyCode());
        String body = "{'method':'calPmsSaleAmazonGrossProfit','params':" + JSON.toJSONString(params) + "}";
        httpParams.setBody(body);

        return HttpUtils.exchange2ApiResult(httpParams, String.class);
    }

}
