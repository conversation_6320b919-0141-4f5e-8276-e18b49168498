package com.estone.erp.publish.amazon.util.model;

import com.estone.erp.publish.amazon.call.model.NameValue;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * 产品货号的变体属性
 * 
 * <AUTHOR>
 *
 */
public class SkuTheme {
    /**
     * 产品货号
     */
    private String productArticleNumber;

    /**
     * sku风格
     */
    private String variationThemes;

    /*
     * sku变体属性
     */
    private Map<String, List<NameValue>> variants = new LinkedHashMap<String, List<NameValue>>();

    public String getProductArticleNumber() {
        return productArticleNumber;
    }

    public void setProductArticleNumber(String productArticleNumber) {
        this.productArticleNumber = productArticleNumber;
    }

    public String getVariationThemes() {
        return variationThemes;
    }

    public void setVariationThemes(String variationThemes) {
        this.variationThemes = variationThemes;
    }

    public Map<String, List<NameValue>> getVariants() {
        return variants;
    }

    public void setVariants(Map<String, List<NameValue>> variants) {
        this.variants = variants;
    }
}
