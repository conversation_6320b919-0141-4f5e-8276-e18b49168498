package com.estone.erp.publish.amazon.util.request;

import com.alibaba.fastjson.JSON;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-02 11:40
 */
@Data
public class JSONListingFeedRequest {
    @SerializedName("sku")
    private String sku = null;
    @SerializedName("operationType")
    private String operationType = null;
    @SerializedName("productType")
    private String productType = null;
    @SerializedName("requirements")
    private String requirements = null;
    @SerializedName("attributes")
    private Map<String, Object> attributes = null;


    public String toJSONData() {
        return JSON.toJSONString(this);
    }

}
