package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanban;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Amazon 刊登成功率
 *
 * <AUTHOR>
 * @date 2023-12-20 16:23
 */
@Data
public class AmazonPublishFailTypeRateVO {

    /**
     * 错误类型
     */
    private String errorType;

    /**
     * 错误分类数据
     */
    private List<ErrorTypeData> errorTypeData;


    @Data
    static class ErrorTypeData {
        /**
         * 问题分类
         */
        private String type;

        /**
         * 数量
         */
        private Integer count;

    }


    public void setFailTypeKanbanData(List<AmazonPublishFailTypeKanban> failTypeKanbans, Integer topLimit) {
        if (CollectionUtils.isEmpty(failTypeKanbans)) {
            setErrorTypeData(Collections.emptyList());
            return;
        }
        List<ErrorTypeData> typeData = failTypeKanbans.stream()
                .map(failTypeKanban -> {
                    ErrorTypeData errorTypeData = new ErrorTypeData();
                    errorTypeData.setType(failTypeKanban.getSolutionType());
                    errorTypeData.setCount(failTypeKanban.getCountNumber());
                    return errorTypeData;})
                .sorted(Comparator.comparingInt(ErrorTypeData::getCount).reversed())
                .limit(topLimit)
                .collect(Collectors.toList());
        setErrorTypeData(typeData);
    }
}
