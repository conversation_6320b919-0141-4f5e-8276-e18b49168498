package com.estone.erp.publish.amazon.model.request;

import lombok.Data;

import java.util.List;

@Data
public class SaleUserCatOperationRequest {

    /**
     * 勾选的数据ID
     */
    private List<Integer> ids;

    /**
     * 主管次数配置
     */
    private List<SaleUserPublishConfig> saleUserPublishConfigs;

    @Data
    public static class SaleUserPublishConfig {
        /**
         * 主管id
         */
        private String saleId;

        /**
         * 主管名称
         */
        private String saleName;

        /**
         * 刊登次数
         */
        private Integer publishNumber;

    }
}
