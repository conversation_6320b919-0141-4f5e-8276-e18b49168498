package com.estone.erp.publish.amazon.model.dto;

public class AmazonSellerSkuRuleCriteria {
    private String ruleName;
    private String amazonAccountList;
    private String createdBy;
    private Boolean status;
    private String orderBy;
    public String getRuleName() {
        return ruleName;
    }
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    public String getAmazonAccountList() {
        return amazonAccountList;
    }
    public void setAmazonAccountList(String amazonAccountList) {
        this.amazonAccountList = amazonAccountList;
    }
    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public Boolean getStatus() {
        return status;
    }
    public void setStatus(Boolean status) {
        this.status = status;
    }
    public String getOrderBy() {
        return orderBy;
    }
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

}
