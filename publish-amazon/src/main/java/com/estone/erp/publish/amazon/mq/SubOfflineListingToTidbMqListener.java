package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.usermgt_n.EmployeeInfo;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.amazon.model.AmazonTidbSubLog;
import com.estone.erp.publish.amazon.mq.model.SubAmazonOfflineListingMq;
import com.estone.erp.publish.amazon.service.AmazonTidbSubLogService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinInfoResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonProductListingOfflineService;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.data.domain.Page;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: ${监听消费临时检测手工标记禁用商标词数据，存储到临时表}
 */
@Slf4j
public class SubOfflineListingToTidbMqListener implements ChannelAwareMessageListener {

    private static final String [] fields = {"accountNumber","site","sellerSku","offlineDate", "infringingBrandWord","infringementWordInfos","updateInfringementTime",
            "order_24H_count","order_last_7d_count","order_last_14d_count","order_last_30d_count", "order_num_total","order_days_within_30d"};
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonProductListingOfflineService amazonProductListingOfflineService;
    @Resource
    private AmazonTidbSubLogService amazonTidbSubLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }
        try {
            Boolean isSuccess = doService(body);
            if (isSuccess) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        } catch (Exception e) {
            log.error("消费失败,{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        try {
            SubAmazonOfflineListingMq subAmazonOfflineListingMq = JSON.parseObject(body, SubAmazonOfflineListingMq.class);
            if (null == subAmazonOfflineListingMq || StringUtils.isBlank(subAmazonOfflineListingMq.getAccountNumber())
              ||(StringUtils.isBlank(subAmazonOfflineListingMq.getEndOfflineDate()) && BooleanUtils.isFalse(subAmazonOfflineListingMq.getFirstHandleData()))) {
                return Boolean.FALSE;
            }
            executeSub(subAmazonOfflineListingMq);
        }catch (Exception e){
            log.error("执行下架备份数据到tidb报错：" + body);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

        /**
         *  tidb 最佳实践的 batch size 为 100~300 之间
         *  batch insert 的并发可以调整到 64 或更高
         * @param subAmazonOfflineListingMq
         */
        private void executeSub(SubAmazonOfflineListingMq subAmazonOfflineListingMq) {
            String accountNumber = subAmazonOfflineListingMq.getAccountNumber();
            String startOfflineDate = subAmazonOfflineListingMq.getStartOfflineDate();
            String endOfflineDate = subAmazonOfflineListingMq.getEndOfflineDate();
            Boolean firstHandleData = subAmazonOfflineListingMq.getFirstHandleData();
            String saleName = null;
            String employeeNo = null;
            String accountLevel = null;
            try {
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
                String saleId = saleAccountAndBusinessResponse.getSalesperson();
                if (org.apache.commons.lang.StringUtils.isNotBlank(saleId) && saleId.contains(",")) {
                    saleId = CommonUtils.splitList(saleId, ",").get(0);
                }

                EmployeeInfo employeeInfo = NewUsermgtUtils.getEmployeeInfoByEmployeeId(Long.valueOf(saleId));
                if (null == employeeInfo) {
                    log.error("备份下架数据至tidb %s获取用户信息为空，请检查", accountNumber);
                } else {
                    saleName = employeeInfo.getName();
                    employeeNo = employeeInfo.getEmployeeNo();
                    accountLevel = saleAccountAndBusinessResponse.getAccountLevel();
                }
            }catch (Exception e){
                log.error("备份下架数据至tidb %s获取用户信息失败，请检查 %s",accountNumber,e.getMessage());
            }

            ApiResult<List<AsinInfoResponse>> allAsinInfo = OrderUtils.getAllAsinInfo();
            List<AsinInfoResponse> result = allAsinInfo.getResult();
            Boolean checkFba = true;
            if (ObjectUtils.isEmpty(allAsinInfo) && CollectionUtils.isEmpty(result)) {
                log.error("获取订单系统FBA库存管理所有asin的信息异常");
                checkFba = false;
            }
            // 获取asin集合数据
            List<String> asinList = result.stream().map(AsinInfoResponse::getAsin).distinct().collect(Collectors.toList());
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            int pageIndex = 0;
            int pageSize = 200;
            long total = 0;
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(accountNumber);
            esAmazonProductListingRequest.setIsOnline(false);
            if (BooleanUtils.isTrue(firstHandleData)) {
                esAmazonProductListingRequest.setIsExistOfflineDate(false);
                esAmazonProductListingRequest.setOrderBy("id");
            } else {
                if (StringUtils.isNotBlank(startOfflineDate)) {
                    esAmazonProductListingRequest.setStartOfflineDate(startOfflineDate);
                }
                if (StringUtils.isNotBlank(endOfflineDate)) {
                    esAmazonProductListingRequest.setEndOfflineDate(endOfflineDate);
                }
                esAmazonProductListingRequest.setOrderBy("offlineDate");
            }
            esAmazonProductListingRequest.setSequence("ASC");
            esAmazonProductListingRequest.setFields(fields);
            while (true) {
                List<AmazonTidbSubLog> amazonTidbSubLogList = new ArrayList<>();
                try {
                    Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest, pageSize, pageIndex);
                    if (page == null || CollectionUtils.isEmpty(page.getContent())) {
                        break;
                    }
                    List<EsAmazonProductListing> esAmazonProductListingList = page.getContent();
                    List<String> sellerskuList = esAmazonProductListingList.stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
                    AmazonProductListingOfflineExample amazonProductListingOfflineExample = new AmazonProductListingOfflineExample();
                    amazonProductListingOfflineExample.createCriteria().andAccountNumberEqualTo(accountNumber).andSellerSkuIn(sellerskuList);
                    List<Long> idList = amazonProductListingOfflineService.selectIdListByExample(amazonProductListingOfflineExample);
                    if (CollectionUtils.isNotEmpty(idList)) {
                        amazonProductListingOfflineService.deleteByPrimaryKey(idList);
                    }

                    String site = esAmazonProductListingList.get(0).getSite();
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    example.createCriteria().andAccountNumberEqualTo(accountNumber).andSellerSkuIn(sellerskuList);
                    List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectByExample(example, site);
                    Map<String, AmazonProductListing> sellerskuAmazonProductListingMap = amazonProductListingList.stream().collect(Collectors.toMap(o -> o.getSellerSku(), o -> o));
                    List<AmazonProductListingOffline> amazonProductListingOfflineList = new ArrayList<>(esAmazonProductListingList.size());
                    Date logTime = new Date();
                    for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
                        String sellersku = esAmazonProductListing.getSellerSku();
                        AmazonTidbSubLog amazonTidbSubLog = new AmazonTidbSubLog();
                        amazonTidbSubLog.setAccountNumber(accountNumber);
                        amazonTidbSubLog.setSellerSku(sellersku);
                        amazonTidbSubLog.setCreateTime(logTime);
                        AmazonProductListingOffline amazonProductListingOffline = null;
                        if (sellerskuAmazonProductListingMap.containsKey(sellersku)) {
                            AmazonProductListing amazonProductListing = sellerskuAmazonProductListingMap.get(esAmazonProductListing.getSellerSku());
                            amazonProductListingOffline = BeanUtil.copyProperties(amazonProductListing, AmazonProductListingOffline.class);
                            amazonProductListingOffline.setOrder_24H_count(esAmazonProductListing.getOrder_24H_count());
                            amazonProductListingOffline.setOrder_last_7d_count(esAmazonProductListing.getOrder_last_7d_count());
                            amazonProductListingOffline.setOrder_last_14d_count(esAmazonProductListing.getOrder_last_14d_count());
                            amazonProductListingOffline.setOrder_last_30d_count(esAmazonProductListing.getOrder_last_30d_count());
                            amazonProductListingOffline.setOrder_num_total(esAmazonProductListing.getOrder_num_total());
                            amazonProductListingOffline.setOrder_days_within_30d(esAmazonProductListing.getOrder_days_within_30d());

                        } else {
                            EsAmazonProductListing esAmazonProductListingMsg = esAmazonProductListingService.findAllById(esAmazonProductListing.getId());
                            amazonProductListingOffline = BeanUtil.copyProperties(esAmazonProductListingMsg, AmazonProductListingOffline.class);
                        }
                        if (null == amazonProductListingOffline.getOfflineDate() && null != amazonProductListingOffline.getFirstOfflineDate()) {
                            amazonProductListingOffline.setOfflineDate(amazonProductListingOffline.getFirstOfflineDate());
                        }
                        if (null == amazonProductListingOffline.getFirstOfflineDate() && null != amazonProductListingOffline.getOfflineDate()) {
                            amazonProductListingOffline.setFirstOfflineDate(amazonProductListingOffline.getOfflineDate());
                        }
                        if (null == amazonProductListingOffline.getFirstOpenDate() && null != amazonProductListingOffline.getOpenDate()) {
                            amazonProductListingOffline.setFirstOpenDate(amazonProductListingOffline.getOpenDate());
                        }
                        if (null == amazonProductListingOffline.getOpenDate() && null != amazonProductListingOffline.getFirstOpenDate()) {
                            amazonProductListingOffline.setOpenDate(amazonProductListingOffline.getFirstOpenDate());
                        }
                        amazonProductListingOffline.setInfringementWord(esAmazonProductListing.getInfringingBrandWord());
                        String infringementWordInfos = CollectionUtils.isNotEmpty(esAmazonProductListing.getInfringementWordInfos()) ? JSON.toJSONString(esAmazonProductListing.getInfringementWordInfos()) : null;
                        amazonProductListingOffline.setInfringementWordInfos(infringementWordInfos);
                        amazonProductListingOffline.setUpdateInfringementTime(esAmazonProductListing.getUpdateInfringementTime());
                        amazonProductListingOffline.setAccountLevel(accountLevel);
                        amazonProductListingOffline.setSaleNo(employeeNo);
                        amazonProductListingOffline.setSaleName(saleName);
                        //  fba
                        if (checkFba) {
                            amazonProductListingOffline.setFba(asinList.contains(amazonProductListingOffline.getSonAsin()) ? true : false);
                        }
                        amazonProductListingOfflineList.add(amazonProductListingOffline);
                        amazonTidbSubLogList.add(amazonTidbSubLog);
                    }
                    int insertCount = amazonProductListingOfflineService.batchInsert(amazonProductListingOfflineList);
                    pageIndex++;
                    total = total + insertCount;
                }catch (Exception e){
                    String erorMsg = e.getMessage();
                    amazonTidbSubLogList.forEach(o ->{o.setRemark(erorMsg); o.setOperationType("subAmazonOfflineListingToTidb");});
                    int insertCount = amazonTidbSubLogService.batchInsert(amazonTidbSubLogList);
                    log.error("店铺%s的产品下架数据备份报错：%s",accountNumber,e.getMessage());
                }
            }
            stopWatch.stop();
            XxlJobLogger.log(String.format("店铺%s的产品下架数据备份成功,总条数：%s, 耗时：%s 秒", accountNumber,total,stopWatch.getTime(TimeUnit.SECONDS)));
        }
    }