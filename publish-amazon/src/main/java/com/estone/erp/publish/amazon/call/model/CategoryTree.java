package com.estone.erp.publish.amazon.call.model;

import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class CategoryTree {
    Node node = new Node();

    public static CategoryTree get(BrowseTreeReport report) {
        CategoryTree tree = new CategoryTree();
        if (report == null || CollectionUtils.isEmpty(report.getNodes())) {
            return tree;
        }

        Map<String, BrowseTreeReport.Node> map = new LinkedHashMap<String, BrowseTreeReport.Node>();
        List<BrowseTreeReport.Node> reportNodes = report.getNodes();
        reportNodes.stream().sorted(new Comparator<BrowseTreeReport.Node>() {
            @Override
            public int compare(BrowseTreeReport.Node node1, BrowseTreeReport.Node node2) {
                String pathId1 = node1.getBrowsePathById();
                String pathId2 = node2.getBrowsePathById();

                return pathId1.compareTo(pathId2);
            }
        }).forEachOrdered(node -> {
            map.put(node.getBrowsePathById(), node);
        });

        Node root = tree.getNode();
        BrowseTreeReport.Node rootReportNode = new BrowseTreeReport.Node();
        rootReportNode.setBrowseNodeId("0");
        rootReportNode.setBrowsePathById("0");
        root.setNode(rootReportNode);

        List<String> browsePathByIds = new ArrayList<String>(map.keySet());
        Map<String, Node> nodeMap = new HashMap<String, Node>();
        for (String browsePathById : browsePathByIds) {
            BrowseTreeReport.Node reportNode = map.get(browsePathById);
            Node node = new Node(reportNode);
            nodeMap.put(browsePathById, node);
            List<String> ids = Arrays.asList(browsePathById.split(","));
            if (isRootNode(ids, map)) {
                root.addChild(node);
            }
            else {
                Node parentNode = nodeMap.get(String.join(",", ids.subList(0, ids.size() - 1)));
                parentNode.addChild(node);
            }
        }

        return tree;
    }

    public static boolean isRootNode(List<String> ids, Map<String, BrowseTreeReport.Node> map) {
        return !map.containsKey(String.join(",", ids.subList(0, ids.size() - 1)));
    }

    public static Map<String, Node> getSiblingNodes(BrowseTreeReport report) {
        if (report == null || CollectionUtils.isEmpty(report.getNodes())) {
            return null;
        }

        Map<String, Node> children = new HashMap<String, Node>();
        for (BrowseTreeReport.Node reportNode : report.getNodes()) {
            Node node = new Node();
            node.setNode(reportNode);
            children.put(node.getNode().getBrowseNodeId(), node);
        }

        return children;
    }

    public Node getNode() {
        return node;
    }

    public void setNode(Node node) {
        this.node = node;
    }

    public static class Node {
        private BrowseTreeReport.Node node;

        private Map<String, Node> children;

        public Node() {
        }

        public Node(com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.Node node) {
            this.node = node;
        }

        public BrowseTreeReport.Node getNode() {
            return node;
        }

        public void setNode(BrowseTreeReport.Node node) {
            this.node = node;
        }

        public Map<String, Node> getChildren() {
            return children;
        }

        public void setChildren(Map<String, Node> children) {
            this.children = children;
        }

        public void addChild(Node node) {
            if (children == null) {
                children = new LinkedHashMap<String, Node>();
            }

            children.put(node.getNode().getBrowseNodeId(), node);
        }
    }
}
