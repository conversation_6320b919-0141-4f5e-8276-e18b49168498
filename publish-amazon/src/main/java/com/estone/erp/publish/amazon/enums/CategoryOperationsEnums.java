package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface CategoryOperationsEnums {

    @Getter
    @AllArgsConstructor
    enum ConfigStatus {
        ENABLE(1, "启用"),
        DISABLE(0, "禁用");

        private final Integer code;
        private final String desc;

        public boolean isTrue(Integer code) {
            return this.code.equals(code);
        }
        public boolean getBooleanValue() {
            return this.code.equals(1);
        }
    }

    @Getter
    @AllArgsConstructor
    enum PublishType {
        ROOT(1, "主配置"),
        SUPERVISOR(2, "主管配置");

        private final Integer code;
        private final String desc;

        public boolean isTrue(Integer code) {
            return this.code.equals(code);
        }
    }
}
