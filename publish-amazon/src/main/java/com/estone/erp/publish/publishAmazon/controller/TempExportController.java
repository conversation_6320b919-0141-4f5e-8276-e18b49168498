package com.estone.erp.publish.publishAmazon.controller;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonListingSyncBrandRecordExample;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonListingSyncBrandRecordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description: 临时导出数据
 */
@RestController
@Slf4j
@RequestMapping("tempExport")
public class TempExportController {

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private AmazonListingSyncBrandRecordService amazonListingSyncBrandRecordService;

    public List<String> loading1Id1List() {
        log.info("开始加载ID............");
        List<String> idList1 = new ArrayList<>();
        File file = new File("C:\\1.txt");
        try {
            idList1.addAll(FileUtils.readLines(file,"UTF-8"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("加载1Id对应的size：" + idList1.size());
        return idList1;
    }

    @RequestMapping(value = "amazon/exportAmazonListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportAmazonListing(HttpServletResponse response) {
        List<String> idList1 = loading1Id1List();
        List<List<String>> idList = Lists.partition(idList1, 1000);
        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        String[] fields = {"accountNumber", "site", "sonAsin", "sellerSku", "articleNumber", "brandName", "order_num_total", "order_last_30d_count", "openDate"};
        for (List<String> idList4 : idList) {
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setIdList(idList4);
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            esAmazonProductListingList.addAll(esAmazonProductListings);
        }
        List<String> accountNumberList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getAccountNumber).distinct().collect(Collectors.toList());
        String[] withFields = {"accountNumber","accountStatus","abnormalCause"};
        ApiResult<List<SaleAccount>> saleAccountResult = EsAccountUtils.getSaleAccountsByAccounts(accountNumberList, SaleChannel.CHANNEL_AMAZON, withFields);
        if (!saleAccountResult.isSuccess()) {
            return ApiResult.newError(saleAccountResult.getErrorMsg());
        }
        List<SaleAccount> saleAccounts = saleAccountResult.getResult();
        Map<String, SaleAccount> infoMap = new HashMap<>();
        for (SaleAccount account : saleAccounts) {
            infoMap.put(account.getAccountNumber(), account);
        }
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "包含generic品牌词的所有listing1.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============esAmazonProductListingList=========" + esAmazonProductListingList.size());
            String[] headers = {"店铺","站点","子ASIN ","sellerSku","SKU","品牌","店铺状态","总销量","最近30天销量","最新上架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSite()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getArticleNumber()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getBrandName()));
                    awList.add(POIUtils.transferStr2Str(SaleAccountStastusEnum.getNameByCode(infoMap.get(account).getAccountStatus())));

                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOrder_days_within_30d()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_days_within_30d())));
                    }else {
                        awList.add("");
                    }
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOpenDate()))));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    public List<String> loadingTaoZhuangList() {
        log.info("开始加载套装货号............");
        List<String> articleNumberList = new ArrayList<>();
        File file = new File("C:\\taozhuang.txt");
        try {
            articleNumberList.addAll(FileUtils.readLines(file,"UTF-8"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("加载套装货号对应的size：" + articleNumberList.size());
        return articleNumberList;
    }

    @RequestMapping(value = "amazon/byFileExportListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> byFileExportListing(HttpServletResponse response) {
        List<String> articleNumberList = loadingTaoZhuangList();
        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        String[] fields = { "articleNumber",  "order_num_total", "order_last_30d_count"};
        List<EsAmazonProductListing> amazonProductListingList = new ArrayList<>();
        for (String articleNumber : articleNumberList) {
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            if (articleNumber.contains(" & ")){
                List<String> skus = Arrays.asList(StringUtils.split(articleNumber, " & "));
                esAmazonProductListingRequest.setArticleNumberList(skus);
            }else if (articleNumber.contains("&")){
                List<String> skus = Arrays.asList(StringUtils.split(articleNumber, "&"));
                esAmazonProductListingRequest.setArticleNumberList(skus);
            }else {
                esAmazonProductListingRequest.setArticleNumber(articleNumber);
            }
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            EsAmazonProductListing amazonProductListing = new EsAmazonProductListing();
            amazonProductListing.setArticleNumber(articleNumber);
            if (CollectionUtils.isNotEmpty(esAmazonProductListings)){
                amazonProductListing.setQuantity(esAmazonProductListings.size());
                final int[] order30Count = {0};
                final int[] orderCount = {0};
                esAmazonProductListings.forEach(o ->{
                    int number30 = Optional.ofNullable(o.getOrder_last_30d_count()).orElseGet(() -> 0);
                    order30Count[0] = order30Count[0] + number30;

                    int number = Optional.ofNullable(o.getOrder_num_total()).orElseGet(() -> 0);
                    orderCount[0] = orderCount[0] + number;
                });
                amazonProductListing.setOrder_last_30d_count(order30Count[0]);
                amazonProductListing.setOrder_num_total(orderCount[0]);

            }else {
                amazonProductListing.setQuantity(0);
                amazonProductListing.setOrder_last_30d_count(0);
                amazonProductListing.setOrder_num_total(0);
            }
            amazonProductListingList.add(amazonProductListing);
        }

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "附件数据统计在线数量和销量.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============esAmazonProductListingList=========" + amazonProductListingList.size());
            String[] headers = {"套装货号","在线listing数量","总销量 ","30天销量"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, amazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getArticleNumber()));
                    awList.add(String.valueOf(esAmazonProductListing.getQuantity()));
                    awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_last_30d_count())));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    @RequestMapping(value = "amazon/exportListings", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportListings(HttpServletResponse response) {

        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        String[] fields = {"accountNumber", "sonAsin","parentAsin", "sellerSku", "articleNumber",  "order_num_total", "openDate", "offlineDate"};
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setArticleNumber("2SS306843-BL");
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            esAmazonProductListingList.addAll(esAmazonProductListings);

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "包含2SS306843-BL的所有listing.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============esAmazonProductListingList=========" + esAmazonProductListingList.size());
            String[] headers = {"店铺","子ASIN ","主ASIN ","sellerSku","SKU","总销量","最新上架时间","最新下架时间","在线状态"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getArticleNumber()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOpenDate()))));
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    awList.add(POIUtils.transferStr2Str(Boolean.TRUE.equals(esAmazonProductListing.getIsOnline()) ? "是" : "否"));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }


    @RequestMapping(value = "amazon/exportBrandAmazonListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportBrandAmazonListing(HttpServletResponse response) {
        String columns = "account_number, account_country,brand";
        AmazonAccountRelationExample accountExample = new AmazonAccountRelationExample();
        accountExample.setFiledColumns(columns);
        accountExample.setOrderByClause("account_number asc");
        AmazonAccountRelationExample.Criteria criteria = accountExample.createCriteria()
                .andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode())
               // .andAccountNumberIn(Arrays.asList("UK-chenpeiyuns","DE-lcnbhc036"))
                ;
        List<AmazonAccountRelation> amazonAccountRelations = amazonAccountRelationService.selectFiledColumnsByExample(accountExample);
        if (CollectionUtils.isEmpty(amazonAccountRelations)) {
            return ApiResult.newError("无账号");
        }
        AmazonListingSyncBrandRecordExample amazonListingSyncBrandRecordExample = new AmazonListingSyncBrandRecordExample();
        String timeStr = "2024-01-24 18:13:35";
        amazonListingSyncBrandRecordExample.createCriteria().andCreateTimeGreaterThan(new Timestamp(DateUtils.formatStringToDate(timeStr,DateUtils.STANDARD_DATE_PATTERN).getTime()));
        List<String> sellerskuList = amazonListingSyncBrandRecordService.selectSellerSkuByExample(amazonListingSyncBrandRecordExample);

        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        String[] fields = {"accountNumber", "site", "sonAsin", "sellerSku", "articleNumber", "brandName", "order_num_total"};
        for (AmazonAccountRelation amazonAccountRelation: amazonAccountRelations) {
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(amazonAccountRelation.getAccountNumber());
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setFields(fields);
            esAmazonProductListingRequest.setSaleQuantityBean("order_num_total");
            esAmazonProductListingRequest.setFromSaleQuantity(0L);
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            if (CollectionUtils.isNotEmpty(esAmazonProductListings)) {
                esAmazonProductListingList.addAll(esAmazonProductListings);
            }
        }
        amazonAccountRelations= amazonAccountRelations.stream().filter( o -> StringUtils.isNotBlank(o.getBrand()) && StringUtils.isNotEmpty(o.getAccountNumber())).collect(Collectors.toList());
        Map<String,String> accountBrandMap= amazonAccountRelations.stream().collect(Collectors.toMap(o ->o.getAccountNumber(),o ->o.getBrand()));
        String fileName = "包含销量的品牌词数据的所有listing.xlsx";
        File file = null;
        try {
            String suffix = ".xlsx";
            file = File.createTempFile(fileName.trim(), suffix);
            log.warn("开始" + file.getPath());
        OutputStream os = new FileOutputStream(file, true);
        //try {
           //os = response.getOutputStream();
            //String fileName = "包含销量的品牌词数据的所有listing1.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============esAmazonProductListingList=========" + esAmazonProductListingList.size());
            //店铺，sellersku，asin，sku，品牌，店铺配置的品牌，品牌同步是否成功
            String[] headers = {"店铺","sellersku","子ASIN ","SKU","品牌","店铺配置品牌","总销量","品牌同步是否失败"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            AtomicInteger i= new AtomicInteger();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    //递增1
                    i.incrementAndGet();
                    String account =esAmazonProductListing.getAccountNumber();
                    String sellersku = esAmazonProductListing.getSellerSku();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getArticleNumber()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getBrandName()));
                    awList.add(POIUtils.transferStr2Str(accountBrandMap.get(account)));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (sellerskuList.contains(sellersku)) {
                        awList.add(POIUtils.transferStr2Str("失败"));
                    }else {
                        awList.add(POIUtils.transferStr2Str("成功"));
                    }
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(file.length() > 0){
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName , "amazon_module", StrConstant.ADMIN);
            file.delete();
            if(uploadResult.isSuccess()) {
                SeaweedFile result = uploadResult.getResult();
                if (null != result) {
                    String url2 = result.getUrl2();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(url2)) {
                        //路径
                        rsp.setStatus(StatusCode.SUCCESS);
                        log.warn("销量数据导出完成：============================");
                        log.warn(url2);
                        rsp.setMessage(url2);
                    }
                    if(!org.apache.commons.lang.StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()) && org.apache.commons.lang.StringUtils.isBlank(rsp.getMessage())){
                        rsp.setMessage(JSON.toJSONString(result));
                    }
                }
            }else {
                rsp.setMessage(uploadResult.getErrorMsg());
            }
        }else {
            rsp.setMessage("生成Excel失败");
        }




        log.warn("完成");
        return ApiResult.newSuccess(rsp);
    }

    @RequestMapping(value = "amazon/exporAsinListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exporAsinListing(HttpServletResponse response) {
        String[] fields = {"accountNumber", "site",  "sellerSku", "parentAsin","sonAsin","order_num_total","order_last_30d_count","mainSku","articleNumber","offlineDate"};
        File file = null;
        /*EsAmazonProductListingRequest esAmazonProductListingRequest1 = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest1.setIsOnline(false);
        esAmazonProductListingRequest1.setAttribute3("子ASIN为空的父asin，系统自动下架");
        esAmazonProductListingRequest1.setStartOfflineDate("2024-03-06 00:00:00");
        //esAmazonProductListingRequest1.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());

        esAmazonProductListingRequest1.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest1);
        log.warn("============esAmazonProductListings=========" + esAmazonProductListings.size());

        String fileName = "2024-03-06下架的父asin产品相关信息.xlsx";

        try {
            String suffix = ".xlsx";
            file = File.createTempFile(fileName.trim(), suffix);
            log.warn("开始" + file.getPath());
            OutputStream os = new FileOutputStream(file, true);
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============esAmazonProductListings=========" + esAmazonProductListings.size());
            String[] headers = {"店铺","sellersku","父ASIN ","子ASIN ","站点","总销量","30天销量","下架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            AtomicInteger i= new AtomicInteger();
            POIUtils.createExcel(headers, esAmazonProductListings, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    //递增1
                    i.incrementAndGet();
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSite()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOrder_last_30d_count()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_last_30d_count())));
                    }else {
                        awList.add("");
                    }
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }*/

        EsAmazonProductListingRequest esAmazonProductListingRequest2 = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest2.setIsOnline(false);
        esAmazonProductListingRequest2.setAttribute3("子ASIN为空的父asin，系统自动下架");
        /*esAmazonProductListingRequest2.setSaleQuantityBean("order_num_total");
        esAmazonProductListingRequest2.setFromSaleQuantity(1L);*/
        esAmazonProductListingRequest2.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
        esAmazonProductListingRequest2.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest2);
        log.warn("============esAmazonProductListingList=========" + esAmazonProductListingList.size());


        String fileName1 = "销量大于0的下架的父asin产品相关信息.xlsx";
        File file1 = null;
        try {
            String suffix = ".xlsx";
            file1 = File.createTempFile(fileName1.trim(), suffix);
            log.warn("开始" + file1.getPath());
            OutputStream os = new FileOutputStream(file1, true);
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName1, "UTF-8"));
            String[] headers = {"店铺","sellersku","父ASIN ","子ASIN ","站点","总销量","30天销量","下架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            AtomicInteger i= new AtomicInteger();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    //递增1
                    i.incrementAndGet();
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSite()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOrder_last_30d_count()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_last_30d_count())));
                    }else {
                        awList.add("");
                    }
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }


        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(file.length() > 0){
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, null , "amazon_module1", StrConstant.ADMIN);
            ApiResult<SeaweedFile> uploadResult1 = FmsApiUtil.publishFileUpload(file1, null , "amazon_module1", StrConstant.ADMIN);
            file.delete();
            file1.delete();
            if(uploadResult.isSuccess()) {
                SeaweedFile result = uploadResult.getResult();
                if (null != result) {
                    String url2 = result.getUrl2();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(url2)) {
                        //路径
                        rsp.setStatus(StatusCode.SUCCESS);
                        log.warn("file导出完成：============================");
                        log.warn(url2);
                        log.warn("file1导出完成：============================");
                        log.warn(uploadResult1.getResult().getUrl2());
                        rsp.setMessage(" file导出完成：" + url2 +   "            file1导出完成："+ uploadResult1.getResult().getUrl2());
                    }
                    if(!org.apache.commons.lang.StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()) && org.apache.commons.lang.StringUtils.isBlank(rsp.getMessage())){
                        rsp.setMessage(JSON.toJSONString(result));
                    }
                }
            }else {
                rsp.setMessage(uploadResult.getErrorMsg());
            }
        }else {
            rsp.setMessage("生成Excel失败");
        }

        log.warn("完成");
        return ApiResult.newSuccess(rsp);
    }

    @RequestMapping(value = "amazon/exportParentAsinAmazonListing1", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportAsinAmazonListing1(HttpServletResponse response) {
      /*  EsAmazonProductListingRequest esAmazonProductListingRequest1 = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest1.setIsOnline(false);
        esAmazonProductListingRequest1.setAttribute3("子ASIN为空的父asin，系统自动下架");
        esAmazonProductListingRequest1.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
        List<String>amazonAccountRelations =  esAmazonProductListingService.getAccountNumberListByRequest(esAmazonProductListingRequest1);*/
        List<String> amazonAccountRelations = new ArrayList<>();
        amazonAccountRelations= Arrays.asList("UK-mimo1245","DE-bhgjkh1141","US-weuw5e","UK-1hn1v315nj3tt","JP-lvqinjie33","UK-wowi24wu","UK-sssssswim","UK-dddddd2203","DE-asasDErtyuiop22","JP-shuyimaoyi1","US-taisiliuweixiong","JP-jun_5_lun_my","UK-ououou221456","US-enenen6699","ES-liangliang74","CA-crecre8974","DE-ucbieceic","UK-guoichuling","UK-yuyubingkj","UK-gjngiaiok","US-yiyiyi1234561","JP-qso9856","US-renqiu_1","US-hudiequan558","JP-YaNG_LIn9","US-fensoukuaile66","US-swenbend","US-buyaohaipa63","UK-jiejiaokoi","US-wutataka45","UK-vwew80euwc","UK-bd16s416a1ws","ES-iwiwiw908","UK-penelopemichael44425418","US-ejiangshangm","US-jihuloop","ES-guoichuling","CA-juhyrt417","UK-chenpeiyuns","ES-ikju4754","DE-wenshili25","FR-iwiwiw908","UK-yiwang80fkiaf","US-vonniepinkie6232","JP-zengmin202401","US-nihaoma555","US-youmeiyou1242","US-GailMTeeter","JP-zi7_ying_yxgs","US-hn1v315nj3tt","UK-shanwxINg478","US-crecre8974","US-rtuiuiuy","JP-***********","IT-mimo1245","CA-chenjiaming1103","UK-erykxcewbc","FR-ioqwhgajgbg","US-niu55wawa","CA-kilbackbostwickbidl19","ES-chenxiugeng65","US-djisewpoespkf","NL-guilian123_a","US-cITy452cITy","JP-jiayiye2023","CA-mxucssr5961","US-koefhf214","CA-zgmnqwt","US-chen_yong_zhang","UK-douyi_maoyi","CA-zeng_zhi_yong1","US-kilbackbostwickbidl19","CA-hi8ujhuy","FR-bubu449","US-nbvhujh631","US-ocnweewwe","IT-bge19980624","FR-anghhnb","ES-erykxcewbc","US-klvbhor15565","CA-GailMTeeter","UK-yuaxy783241","US-mnjrfewtfs","IT-d17188991462","UK-inq86212rm","JP-vonniepinkie6232","DE-nv12nfgg5d","US-obvctdcwx","CA-hf63hd5e64rg4s","CA-tttyyw2210","CA-ssliu644","ES-hikz14019fod","DE-dgyzsiv7lmwm","IT-hikz14019fod");

        log.warn("============店铺amazonAccountRelations=========" + amazonAccountRelations.size());

        List<EsAmazonProductListing> sonEsAmazonProductListingList = new ArrayList<>();
        List<EsAmazonProductListing> parEsAmazonProductListingList = new ArrayList<>();
        String[] fields = {"accountNumber", "site",  "sellerSku", "parentAsin","sonAsin","order_num_total","order_last_30d_count","mainSku","articleNumber","offlineDate"};
        int j=0;
        for (String accountNumber: amazonAccountRelations) {
            j++;
            Set<String> parentAsinList = new HashSet<>();
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(accountNumber);
            esAmazonProductListingRequest.setIsOnline(false);
            esAmazonProductListingRequest.setFields(fields);
            esAmazonProductListingRequest.setAttribute3("子ASIN为空的父asin，系统自动下架");
            esAmazonProductListingRequest.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            if (CollectionUtils.isNotEmpty(esAmazonProductListings)) {
                List<String> parntAsinList = esAmazonProductListings.stream().map(o->o.getSonAsin()).collect(Collectors.toList());
                List<List<String>> asins = Lists.partition(parntAsinList,200);
                for (List<String> parentAsins: asins) {
                    EsAmazonProductListingRequest sonAmazonProductListingRequest = new EsAmazonProductListingRequest();
                    sonAmazonProductListingRequest.setAccountNumber(accountNumber);
                    sonAmazonProductListingRequest.setIsOnline(true);
                    sonAmazonProductListingRequest.setSaleQuantityBean("order_num_total");
                    sonAmazonProductListingRequest.setFromSaleQuantity(1L);
                    sonAmazonProductListingRequest.setFields(fields);
                    sonAmazonProductListingRequest.setParentAsinList(parentAsins);
                    sonAmazonProductListingRequest.setItemType(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode());
                    Page<EsAmazonProductListing> page = esAmazonProductListingService.page(sonAmazonProductListingRequest, 1000, 0);
                    if (page != null && CollectionUtils.isNotEmpty(page.getContent())){
                        List<EsAmazonProductListing> esAmazonProductListingList = page.getContent();
                        int count = esAmazonProductListingList.size();
                        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
                            log.error(String.format("账号%s的Asin存在订单FBA库存管理中，被过滤", accountNumber));
                        }else {
                            sonEsAmazonProductListingList.addAll(esAmazonProductListingList);
                            List<String> checkParentAsinList = esAmazonProductListingList.stream().map(o -> o.getParentAsin()).collect(Collectors.toList());
                            parentAsinList.addAll(checkParentAsinList);
                        }
                    }
                }
                for (EsAmazonProductListing esAmazonProductListing :esAmazonProductListings){
                    if (parentAsinList.contains(esAmazonProductListing.getSonAsin())){
                        parEsAmazonProductListingList.add(esAmazonProductListing);
                    }
                }
            }
        }
        log.warn("============parEsAmazonProductListingList=========" + parEsAmazonProductListingList.size());
        log.warn("============sonEsAmazonProductListingList=========" + sonEsAmazonProductListingList.size());

        String fileName = "还存在在线子asin的父asin被下架父产品相关信息";
        File file = null;
        try {
            String suffix = ".xlsx";
            file = File.createTempFile(fileName.trim(), suffix);
            log.warn("开始" + file.getPath());
            OutputStream os = new FileOutputStream(file, true);
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("============parentAsinListings=========" + parEsAmazonProductListingList.size());
            String[] headers = {"店铺","sellersku","父ASIN ","子ASIN ","站点","总销量","30天销量","下架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            AtomicInteger i= new AtomicInteger();
            POIUtils.createExcel(headers, parEsAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    //递增1
                    i.incrementAndGet();
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSite()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOrder_last_30d_count()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_last_30d_count())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOfflineDate()) {
                        awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    }

                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        String fileName1 = "还存在在线子asin的父asin被下架相关子asin信息";
        File file1 = null;
        try {
            String suffix = ".xlsx";
            file1 = File.createTempFile(fileName1.trim(), suffix);
            log.warn("开始" + file1.getPath());
            OutputStream os = new FileOutputStream(file1, true);
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName1, "UTF-8"));
            log.warn("============sonEsAmazonProductListingList=========" + sonEsAmazonProductListingList.size());
            String[] headers = {"店铺","sellersku","父ASIN ","子ASIN ","站点","总销量","30天销量","下架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            AtomicInteger i= new AtomicInteger();
            POIUtils.createExcel(headers, sonEsAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    //递增1
                    i.incrementAndGet();
                    String account =esAmazonProductListing.getAccountNumber();
                    awList.add(account);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSite()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOrder_last_30d_count()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_last_30d_count())));
                    }else {
                        awList.add("");
                    }
                    if (null != esAmazonProductListing.getOfflineDate()) {
                        awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    }

                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }


        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(file.length() > 0){
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName , "amazon_module1", StrConstant.ADMIN);
            ApiResult<SeaweedFile> uploadResult1 = FmsApiUtil.publishFileUpload(file1, fileName1 , "amazon_module1", StrConstant.ADMIN);
            file.delete();
            file1.delete();
            if(uploadResult.isSuccess()) {
                SeaweedFile result = uploadResult.getResult();
                if (null != result) {
                    String url2 = result.getUrl2();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(url2)) {
                        //路径
                        rsp.setStatus(StatusCode.SUCCESS);
                        log.warn("file导出完成：============================");
                        log.warn(url2);
                        log.warn("file1导出完成：============================");
                        log.warn(uploadResult1.getResult().getUrl2());
                        rsp.setMessage(" file导出完成：" + url2 +   "            file1导出完成："+ uploadResult1.getResult().getUrl2());
                    }
                    if(!org.apache.commons.lang.StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()) && org.apache.commons.lang.StringUtils.isBlank(rsp.getMessage())){
                        rsp.setMessage(JSON.toJSONString(result));
                    }
                }
            }else {
                rsp.setMessage(uploadResult.getErrorMsg());
            }
        }else {
            rsp.setMessage("生成Excel失败");
        }

        log.warn("完成");
        return ApiResult.newSuccess(rsp);
    }

}
