package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.common.context.DataContextHolder;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * amazon批量调整价格和数量
 */
@Slf4j
public class PublishAmazonProductProcesser extends AbstractPublishProcesser<AmazonVariantBO> {
    /**
     * 图片路径
     */
    private String imagePath;

    public PublishAmazonProductProcesser(String accountNumber) {
        super(accountNumber, SpringUtils.getBean(ProductSubmitFeedXmlStrategy.class));
        imagePath = getImagePath(account.getAccountNumber());
    }

    public boolean publish(List<AmazonVariantBO> amazonVariants, String feedType) {
        return publish(feedType, item -> {
            item.setUnitDatas(amazonVariants);
            item.setImagePath(imagePath);

            return true;
        }, null);
    }

    @Override
    public void filterRepeatProductSku(PublishData<AmazonVariantBO> publishData){
        // 仅模板校验
    }

    @Override
    public List<AmazonProcessReport> initProcessReports(PublishData<AmazonVariantBO> publishData) {
        List<AmazonVariantBO> amazonVariants = publishData.getUnitDatas();
        String feedType = publishData.getFeedType();
        List<AmazonProcessReport> reports = new ArrayList<AmazonProcessReport>();
        for (AmazonVariantBO variant : amazonVariants) {
            AmazonProcessReport report = new AmazonProcessReport();
            report.setFeedType(feedType);
            report.setAccountNumber(variant.getAccountNumber());
            report.setStatusCode(ProcessingReportStatusCode.Init.name());
            report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
            report.setDataValue(variant.getSellerSku());
            report.setRelationId(variant.getId());
            if (StringUtils.isEmpty(variant.getRelationType())) {
                report.setRelationType(ProcessingReportTriggleType.Listing.name());
            }else {
                report.setRelationType(variant.getRelationType());
            }
            if (SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.name().equals(feedType)){
                if (ProcessingReportTriggleType.Listing_Fulfillment_Latency.name().equals(variant.getRelationType())) {
                    //备货期(跟修改库存是同一个类型)
                    report.setAfterFulfillmentLatencyValue(variant.getAfterFulfillmentLatencyValue());
                    report.setPreviousFulfillmentLatencyValue(variant.getPreviousFulfillmentLatencyValue());
                }
                //库存
                report.setAfterQuantityValue(variant.getAfterQuantityValue());
                report.setPreviousQuantityValue(variant.getPreviousQuantityValue());
            }else if (SpFeedType.POST_PRODUCT_PRICING_DATA.name().equals(feedType)){
                // 价格
                report.setPreviousPriceValue(variant.getPreviousPriceValue());
                report.setAfterPriceValue(variant.getAfterPriceValue());
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(WebUtils.getUserName())) {
                report.setCreatedBy(WebUtils.getUserName());
            } else if (org.apache.commons.lang.StringUtils.isNotBlank(DataContextHolder.getUsername())) {
                report.setCreatedBy(DataContextHolder.getUsername());
            }
            amazonProcessReportService.insert(report);
            reports.add(report);
        }

        return reports;
    }

    @Override
    public void afterFinishProcessReports(PublishData<AmazonVariantBO> publishData) {

    }

    public void batchPublish(List<AmazonVariantBO> amazonVariants, List<String> feedTypes) {
        batchPublish(feedTypes, item -> {
            item.setUnitDatas(amazonVariants);
            item.setImagePath(imagePath);
            item.setOperationType(OperationType.PartialUpdate);
            return true;
        }, null);
    }

    @Override
    public Map<String, String> getUnitDataSellerSku2SkuMap(AmazonVariantBO unitData) {
        Map<String, String> result = new HashMap<>(1);
        result.put(unitData.getSellerSku(), unitData.getAccountNumber());

        return result;
    }

    @Override
    public Map<String, String> getVariantParentSku(AmazonVariantBO unitData) {
        return null;
    }
}