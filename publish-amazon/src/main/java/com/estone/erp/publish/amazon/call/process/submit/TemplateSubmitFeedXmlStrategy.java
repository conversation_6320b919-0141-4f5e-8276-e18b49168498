package com.estone.erp.publish.amazon.call.process.submit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import com.estone.erp.publish.amazon.call.model.XsdRouteData;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.call.xsd.model.AttributeWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.TypeWrapper;
import com.estone.erp.publish.amazon.componet.publish.domain.OSSImageData;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.KeyWordUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模板上传数据xml策略实现类
 */
@Component
@Slf4j
public class TemplateSubmitFeedXmlStrategy extends AbstractSubmitFeedXmlStrategy<AmazonTemplateBO> {

    @Autowired
    private AmazonCategoryService amazonCategoryService;
    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Override
    public String transferProduct2Xml(PublishData<AmazonTemplateBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Product", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        List<AmazonTemplateBO> amazonTemplates = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonTemplateBO template : amazonTemplates) {
            AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId(template.getCategoryId(),template.getCountry());
            List<AmazonSku> amazonSkus = null;
            String parentSku = template.getSellerSKU();
            //父sku 数据来源
            publishData.addSkuSpFlagMap(template.getParentSku(),template.getSkuDataSource());
            if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
                msgId2SkuMap.put(messageId, parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Product
                Element product = message.create("Product");
                product.create("SKU", parentSku);
                publishData.addSku2SellerSku(template.getParentSku(), parentSku);
                //UPC豁免
                if (null != template.getUpcExempt() && BooleanUtils.isFalse(template.getUpcExempt())) {
                    Element standardProductID = product.create("StandardProductID");
                    standardProductID.create("Type", template.getStandardProdcutIdType());
                    standardProductID.create("Value", template.getStandardProdcutIdValue());
                }

                product.create("ProductTaxCode", getTaxCode(template.getProductTaxCode()));
                product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                Element condition = product.create("Condition");
                condition.create("ConditionType", template.getCondition());
                if (StringUtils.isNotBlank(template.getCondition())
                        && StringUtils.isNotBlank(template.getConditionNote()) && !AmazonConstant.DEFAULT_CONDITION_TYPE.equals(template.getCondition())) {
                    condition.create("ConditionNote", AmazonUtils.toHtml(template.getConditionNote()));
                }

                // DescriptionData
                generateDescriptionDataElement(product, template, category, null);
                // ProductData
                ElementWrapper productDataWrapper = ProductWrapper.fillElementWrapperValues(
                        ProductWrapper.getProductData(template.getProductType()),
                        template.getAmazonExtralData().getProductData());
                generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);
                if (BooleanUtils.isTrue(generateIsHeatSensitiveElement(template.getProductType()))) {
                    product.create("IsHeatSensitive", "false");
                }else {
                    Optional<Boolean> optionalBool = Optional.ofNullable(template.getHeatSensitiveValue());
                    if (StringUtils.isNotBlank(template.getHeatSensitive()) && optionalBool.isPresent()) {
                        product.create("IsHeatSensitive", BooleanUtils.toStringTrueFalse(template.getHeatSensitiveValue()));
                    }
                }
            }
            else if (CollectionUtils.isNotEmpty((amazonSkus = template.getAmazonSkus()))) {
                String productType = template.getProductType();
                ElementWrapper productDataWrapper = null;
                try {
                    productDataWrapper = ProductWrapper.getProductData(productType);
                } catch (Exception e) {
                    String msg = "get productData fail type: " + productType;
                    publishData.addErrorSku(parentSku, msg);
                    continue;
                }

                ElementWrapper variationTheme = ProductWrapper.getVariationTheme(productDataWrapper, productType);
                if (variationTheme == null || StringUtils.isEmpty(template.getVariationThemes())) {
                    //log.error("amazon template[{}]--productType[{}] has no variationTheme", parentSku, productType);
                    String msg = "not set variationTheme Value";
                    publishData.addErrorSku(parentSku, msg);
                    for (AmazonSku amazonSku : amazonSkus) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), msg);
                    }
                    continue;
                }

                XsdRouteData variantThemeData = new XsdRouteData();
                variantThemeData.setRoute(variationTheme.getRoute());
                variantThemeData.setValues(Arrays.asList(template.getVariationThemes()));
                List<XsdRouteData> routeDatas = XsdUtils.mergeNewList(template.getAmazonExtralData().getProductData(),
                        Arrays.asList(variantThemeData));
                productDataWrapper = ProductWrapper.fillElementWrapperValues(productDataWrapper, routeDatas);
                ElementWrapper parentage = ProductWrapper.selectParentage(productDataWrapper,
                        variationTheme.getRoute());
                if (parentage == null) {
                    //log.error("amazon template[{}]--productType[{}] has no parentage", parentSku, productType);
                    String msg = String.format("productType[%s]--has no the xsd element: Parentage", productType);
                    publishData.addErrorSku(parentSku, msg);
                    for (AmazonSku amazonSku : amazonSkus) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), msg);
                    }
                    continue;
                }

                parentage.setValues(Arrays.asList("parent"));
                Map<String, ElementWrapper> themeRoutes = ProductWrapper.getThemeRoutes(productDataWrapper,
                        variationTheme.getType().getRestriction().getEnumerations());

                msgId2SkuMap.put(messageId, parentSku);
                publishData.addSku2SellerSku(template.getParentSku(), parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Product
                Element product = message.create("Product");
                product.create("SKU", parentSku);
                product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                // DescriptionData
                //变体设置关键字的时候，parentSku设置为任意一个子sku的关键字
                generateDescriptionDataElement(product, template, category, amazonSkus.get(0).getSku());
                // ProductData
                generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);
                for (AmazonSku amazonSku : amazonSkus) {
                    msgId2SkuMap.put(messageId, amazonSku.getSellerSKU());
                    // Message
                    message = root.create("Message");
                    message.create("MessageID", String.valueOf(messageId));
                    messageId++;
                    message.create("OperationType", operationType.name());
                    // Product
                    product = message.create("Product");
                    String sellerSku = amazonSku.getSellerSKU();
                    product.create("SKU", sellerSku);
                    publishData.addSku2SellerSku(amazonSku.getSku(), sellerSku);
                    publishData.addSkuSpFlagMap(amazonSku.getSku(),template.getSkuDataSource());
                    //upc豁免
                    if (null != template.getUpcExempt() && BooleanUtils.isFalse(template.getUpcExempt())) {
                        Element standardProductID = product.create("StandardProductID");
                        standardProductID.create("Type", amazonSku.getStandardProdcutIdType());
                        standardProductID.create("Value", amazonSku.getStandardProdcutIdValue());
                    }
                    product.create("ProductTaxCode", getTaxCode(template.getProductTaxCode()));
                    product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                    Element condition = product.create("Condition");
                    condition.create("ConditionType", amazonSku.getCondition());
                    if (StringUtils.isNotBlank(amazonSku.getCondition())
                            && StringUtils.isNotBlank(amazonSku.getConditionNote()) && !AmazonConstant.DEFAULT_CONDITION_TYPE.equals(template.getCondition())) {
                        condition.create("ConditionNote", AmazonUtils.toHtml(amazonSku.getConditionNote()));
                    }

                    // DescriptionData
                    generateDescriptionDataElement(product, template, category, amazonSku.getSku());
                    // ProductData
                    parentage.setValues(Arrays.asList("child"));
                    routeDatas = AmazonUtils.transferAmazonSkuThemes2RouteDatas(amazonSku, themeRoutes);
                    productDataWrapper = ProductWrapper.fillElementWrapperValues(productDataWrapper, routeDatas);
                    generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);
                    if (BooleanUtils.isTrue(generateIsHeatSensitiveElement(template.getProductType()))) {
                        product.create("IsHeatSensitive", "false");
                    }else {
                        Optional<Boolean> optionalBool = Optional.ofNullable(template.getHeatSensitiveValue());
                        if (StringUtils.isNotBlank(template.getHeatSensitive()) && optionalBool.isPresent()) {
                            product.create("IsHeatSensitive", BooleanUtils.toStringTrueFalse(template.getHeatSensitiveValue()));
                        }
                    }
                }
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductRelationship2Xml(PublishData<AmazonTemplateBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Relationship", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonTemplateBO> amazonTemplates = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonTemplateBO template : amazonTemplates) {
            String parentSku = template.getSellerSKU();
            List<AmazonSku> amazonSkus = template.getAmazonSkus();
            if (BooleanUtils.toBoolean(template.getSaleVariant()) && CollectionUtils.isNotEmpty(amazonSkus)) {
                msgId2SkuMap.put(messageId, parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Relationship
                Element relationship = message.create("Relationship");
                relationship.create("ParentSKU", parentSku);
                amazonSkus.forEach(amazonSku -> {
                    // 刊登失败的多属性不绑定
                    /*if (!BooleanUtils.toBoolean(amazonSku.getStepTemplateStatus())) {
                        return;
                    }*/

                    Element relation = relationship.create("Relation");
                    relation.create("SKU", amazonSku.getSellerSKU());
                    relation.create("Type", "Variation");
                });
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductPrice2Xml(PublishData<AmazonTemplateBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Price", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        List<AmazonTemplateBO> amazonTemplates = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        String currency = publishData.getCurrency();
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonTemplateBO template : amazonTemplates) {
            String parentSku = template.getSellerSKU();
            List<AmazonSku> amazonSkus = null;
            if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
                Double standardPrice = template.getStandardPrice();
                if (standardPrice == null || standardPrice <= 0d) {
                    publishData.addErrorSku(parentSku, "产品价格不能为空和0");
                    continue;
                }

                msgId2SkuMap.put(messageId, parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Price
                Element price = message.create("Price");
                price.create("SKU", parentSku);
                price.create("StandardPrice", String.valueOf(standardPrice)).addAttr("currency", currency)
                        .addAttr("zero", "false");
                Double salePrice = template.getSalePrice();
                if (salePrice != null && salePrice > 0d && template.getSaleStartDate() != null
                        && template.getSaleEndDate() != null) {
                    Element sale = price.create("Sale");
                    sale.create("StartDate", sdf.format(AmazonUtils.getUTCTime(template.getSaleStartDate())));
                    sale.create("EndDate", sdf.format(AmazonUtils.getUTCTime(template.getSaleEndDate())));
                    sale.create("SalePrice", String.valueOf(salePrice)).addAttr("currency", currency).addAttr("zero",
                            "false");
                }
            }
            else if (CollectionUtils.isNotEmpty((amazonSkus = template.getAmazonSkus()))) {
                for (AmazonSku amazonSku : amazonSkus) {
                    Double standardPrice = amazonSku.getStandardPrice();
                    if (standardPrice == null || standardPrice <= 0d) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), "产品价格不能为空和0");
                        continue;
                    }

                    msgId2SkuMap.put(messageId, amazonSku.getSellerSKU());
                    // Message
                    Element message = root.create("Message");
                    message.create("MessageID", String.valueOf(messageId));
                    messageId++;
                    message.create("OperationType", operationType.name());
                    // Price
                    Element price = message.create("Price");
                    price.create("SKU", amazonSku.getSellerSKU());
                    price.create("StandardPrice", String.valueOf(standardPrice)).addAttr("currency", currency)
                            .addAttr("zero", "false");
                    Double salePrice = amazonSku.getSalePrice();
                    if (salePrice != null && salePrice > 0d && amazonSku.getSaleStartDate() != null
                            && amazonSku.getSaleEndDate() != null) {
                        Element sale = price.create("Sale");
                        sale.create("StartDate", sdf.format(AmazonUtils.getUTCTime(amazonSku.getSaleStartDate())));
                        sale.create("EndDate", sdf.format(AmazonUtils.getUTCTime(amazonSku.getSaleEndDate())));
                        sale.create("SalePrice", String.valueOf(salePrice)).addAttr("currency", currency)
                                .addAttr("zero", "false");
                    }
                }
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductInventory2Xml(PublishData<AmazonTemplateBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Price", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonTemplateBO> amazonTemplates = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonTemplateBO template : amazonTemplates) {
            List<AmazonSku> amazonSkus = null;
            if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
                String parentSku = template.getSellerSKU();
                Integer quantity = template.getQuantity();
                if (quantity == null) {
                    publishData.addErrorSku(parentSku, "库存数量不能为空");
                    continue;
                }

                msgId2SkuMap.put(messageId, parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Inventory
                Element inventory = message.create("Inventory");
                inventory.create("SKU", parentSku);
                inventory.create("Quantity", String.valueOf(template.getQuantity()));
                inventory.create("FulfillmentLatency", String.valueOf(2));
            }
            else if (CollectionUtils.isNotEmpty((amazonSkus = template.getAmazonSkus()))) {
                for (AmazonSku amazonSku : amazonSkus) {
                    Integer quantity = amazonSku.getQuantity();
                    if (quantity == null) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), "库存数量不能为空");
                        continue;
                    }

                    msgId2SkuMap.put(messageId, amazonSku.getSellerSKU());
                    // Message
                    Element message = root.create("Message");
                    message.create("MessageID", String.valueOf(messageId));
                    messageId++;
                    message.create("OperationType", operationType.name());
                    // Inventory
                    Element inventory = message.create("Inventory");
                    inventory.create("SKU", amazonSku.getSellerSKU());
                    inventory.create("Quantity", String.valueOf(amazonSku.getQuantity()));
                    inventory.create("FulfillmentLatency", String.valueOf(2));
                }
            }
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductImage2Xml(PublishData<AmazonTemplateBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("ProductImage", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonTemplateBO> amazonTemplates = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        String imagePath = publishData.getImagePath();
        int messageId = 1;
        for (AmazonTemplateBO template : amazonTemplates) {
            // 图片外网映射
            imageMappingHandle(template, imagePath);
            Map<String, OSSImageData> imageMapping = template.getImageMapping();
            if (MapUtils.isEmpty(imageMapping)) {
                continue;
            }
            Function<String, String> url2NewUrlMap = url -> {
                if (StringUtils.isEmpty(url)) {
                    return null;
                }
                OSSImageData ossImageData = imageMapping.get(url);
                if (ossImageData == null) {
                    return null;
                }
                if (ossImageData.getExpireTime().isBefore(LocalDateTime.now())) {
                    // 图片过期
                    return null;
                }
                return ossImageData.getOssUrl();
            };

            List<AmazonSku> amazonSkus = null;
            if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
                String parentSku = template.getSellerSKU();
                if (StringUtils.isEmpty(template.getMainImage())
                        && CollectionUtils.isEmpty(template.getExtraImagesList())) {
                    publishData.addErrorSku(parentSku, "产品图片不能为空");
                    continue;
                }

                String imageUrl = url2NewUrlMap.apply(template.getMainImage());
                if (StringUtils.isNotEmpty(imageUrl)) {
                    publishData.addImageUrl(imageUrl);
                    generateImageMessage(root, messageId, parentSku, "Main", imageUrl);
                    msgId2SkuMap.put(messageId, parentSku);
                    messageId++;
                }

                List<String> extraImagesList = template.getExtraImagesList();
                if (CollectionUtils.isNotEmpty(extraImagesList)) {
                    for (int i = 0; i < extraImagesList.size(); i++) {
                        String url = extraImagesList.get(i);
                        String newUrl = url2NewUrlMap.apply(url);
                        if (StringUtils.isNotEmpty(newUrl)) {
                            msgId2SkuMap.put(messageId, parentSku);
                            generateImageMessage(root, messageId, parentSku, "PT" + (i + 1), newUrl);
                            msgId2SkuMap.put(messageId, parentSku);
                            publishData.addImageUrl(newUrl);
                            messageId++;
                        }
                    }
                }
            }
            else if (CollectionUtils.isNotEmpty((amazonSkus = template.getAmazonSkus()))) {
                for (AmazonSku amazonSku : amazonSkus) {
                    String sku = amazonSku.getSellerSKU();
                    if (StringUtils.isEmpty(amazonSku.getMainImage()) && StringUtils.isEmpty(amazonSku.getSampleImage())
                            && CollectionUtils.isEmpty(amazonSku.getExtraImagesList())) {
                        publishData.addErrorSku(sku, "产品图片不能为空");
                        continue;
                    }

                    String imageUrl = url2NewUrlMap.apply(amazonSku.getMainImage());
                    if (StringUtils.isNotEmpty(imageUrl)) {
                        publishData.addImageUrl(imageUrl);
                        generateImageMessage(root, messageId, sku, "Main", imageUrl);
                        msgId2SkuMap.put(messageId, sku);
                        messageId++;
                    }
                    imageUrl = url2NewUrlMap.apply(amazonSku.getSampleImage());
                    if (StringUtils.isNotEmpty(imageUrl)) {
                        publishData.addImageUrl(imageUrl);
                        generateImageMessage(root, messageId, sku, "Swatch", imageUrl);
                        msgId2SkuMap.put(messageId, sku);
                        messageId++;
                    }

                    List<String> extraImagesList = amazonSku.getExtraImagesList();
                    if (CollectionUtils.isNotEmpty(extraImagesList)) {
                        for (int i = 0; i < extraImagesList.size(); i++) {
                            String url = extraImagesList.get(i);
                            String newUrl = url2NewUrlMap.apply(url);
                            if (StringUtils.isNotEmpty(newUrl)) {
                                msgId2SkuMap.put(messageId, sku);
                                generateImageMessage(root, messageId, sku, "PT" + (i + 1), newUrl);
                                msgId2SkuMap.put(messageId, sku);
                                publishData.addImageUrl(newUrl);
                                messageId++;
                            }
                        }
                    }
                }
            }
        }

        return xmlBuilder.builder();
    }

    /**
     * 图片处理, 检查模版是否存在OSS图片，不存在则上传
     *
     * @param template
     * @param imagePath
     */
    private void imageMappingHandle(AmazonTemplateBO template, String imagePath) {
        List<String> templateImages = AmazonTemplateUtils.getAllImages(List.of(template));
        if (CollectionUtils.isEmpty(templateImages)) {
            return;
        }

        Map<String, OSSImageData> imageMapping = template.getImageMapping();
        if (MapUtils.isEmpty(imageMapping)) {
            // 空 上传OSS图片
            uploadImageToAliOSS(templateImages, imagePath, imageMapping, template);
            return;
        }

        // 处理临期图片 临期时间近6小时删除
        List<String> deleteImages = imageMapping.entrySet().stream()
                .filter(entry -> entry.getValue().getExpireTime().isBefore(LocalDateTime.now().minusHours(6)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deleteImages)) {
            // 删除过期图片
            imageMapping.entrySet().removeIf(entry -> deleteImages.contains(entry.getKey()));
        }

        // templateImages 中获取需要上传的图片
        List<String> needUploadImages = templateImages.stream()
                .filter(image -> !imageMapping.containsKey(image))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUploadImages)) {
            // 无需上传
            return;
        }
        // 重新上传图片
        uploadImageToAliOSS(needUploadImages, imagePath, imageMapping, template);
    }

    private void uploadImageToAliOSS(List<String> templateImages, String imagePath, Map<String, OSSImageData> imageMapping, AmazonTemplateBO template) {
        Map<String, String> map = AmazonUtils.copyImagesToAliOSS(templateImages, imagePath);
        // 更新图片映射，图片过期时间为5天
        LocalDateTime expireTime = LocalDateTime.now().plusDays(5);
        for (String key : map.keySet()) {
            imageMapping.put(key, new OSSImageData(map.get(key), expireTime));
        }
        template.setImageMapping(imageMapping);
        // 更新模版图片信息
        amazonTemplateService.updateImageMapping(template);
    }

    private List<String> getAllImages(List<AmazonTemplateBO> amazonTemplates) {
        Set<String> allImages = new HashSet<>();
        amazonTemplates.forEach(template->{
            if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
                if (StringUtils.isNotEmpty(template.getMainImage())) {
                    allImages.add(template.getMainImage());
                }
                List<String> extraImagesList = template.getExtraImagesList();
                if (CollectionUtils.isNotEmpty(extraImagesList)) {
                    allImages.addAll(extraImagesList);
                }
            }
            else if (CollectionUtils.isNotEmpty(template.getAmazonSkus())) {
                List<AmazonSku> amazonSkus = template.getAmazonSkus();
                amazonSkus.forEach(sku->{
                    String mainImage = sku.getMainImage();
                    if (StringUtils.isNotEmpty(mainImage)) {
                        allImages.add(mainImage);
                    }
                    String sampleImage = sku.getSampleImage();
                    if (StringUtils.isNotEmpty(sampleImage)) {
                        allImages.add(sampleImage);
                    }
                    List<String> extraImagesList = sku.getExtraImagesList();
                    if (CollectionUtils.isNotEmpty(extraImagesList)) {
                        allImages.addAll(extraImagesList);
                    }
                });
            }
        });
        return new ArrayList<>(allImages);
    }

    private void generateImageMessage(Element root, int messageId, String sku, String imageType, String imageUrl) {
        // Message
        Element message = root.create("Message");
        message.create("MessageID", String.valueOf(messageId));
        message.create("OperationType", OperationType.Update.name());
        // ProductImage
        Element image = message.create("ProductImage");
        image.create("SKU", sku);
        image.create("ImageType", imageType);
        image.create("ImageLocation", imageUrl);
    }

    private void generateDescriptionDataElement(Element product, AmazonTemplateBO template, AmazonCategoryWithBLOBs category, String sku) {
        ElementWrapper descDataWrapper = ProductWrapper.fillElementWrapperValues(ProductWrapper.descriptionData,
                template.getAmazonExtralData().getDescriptionData());
        descDataWrapper.setItemWrapperValue("Title", template.getTitle());
        descDataWrapper.setItemWrapperValue("Brand", template.getBrand());
        descDataWrapper.setItemWrapperValue("Description", template.getDescription());
        if (StringUtils.isNotEmpty(template.getBulletPoint())) {
            descDataWrapper.setItemWrapperValues("BulletPoint",
                    JSON.parseArray(template.getBulletPoint(), String.class));
        }
        descDataWrapper.setItemWrapperValue("Manufacturer", template.getManufacturer());
        descDataWrapper.setItemWrapperValue("MfrPartNumber", template.getMfrPartNumber());
        String site = template.getCountry();
        if (StringUtils.isNotEmpty(template.getSearchTerms())) {
            //单品的关键词设置
            if(!template.getSaleVariant()){
                // UTF-8 字节
                String searchTerms = template.getSearchTerms();
                if (site.equalsIgnoreCase("JP")){

                     String search = KeyWordUtils.subStringByte(JSON.parseArray(searchTerms, String.class).get(0),249);
                    List<String> list = new ArrayList<>();
                    list.add(search);
                     searchTerms = JSON.toJSONString(list);
                }
                descDataWrapper.setItemWrapperValues("SearchTerms",
                        JSON.parseArray(searchTerms, String.class));
            }
            //变体的关键词设置
            else{
                JSONObject sku2SearchParams = JSONObject.parseObject(template.getSearchTerms());
                String SearchParamsStr = sku2SearchParams.getString(sku);
                // UTF-8 字节
                if (site.equalsIgnoreCase("JP")){

                    String search = KeyWordUtils.subStringByte(JSON.parseArray(SearchParamsStr, String.class).get(0),249);
                    List<String> list = new ArrayList<>();
                    list.add(search);
                    SearchParamsStr = JSON.toJSONString(list);
                }
                descDataWrapper.setItemWrapperValues("SearchTerms", JSON.parseArray(SearchParamsStr, String.class));
            }
        }
        descDataWrapper.setItemWrapperValue("ItemType", XsdUtils.getItemTypeByAmazonCategory(category));

        generateElementByElementWrapper(product, descDataWrapper);
    }


    private Boolean generateIsHeatSensitiveElement(String productType) {
        if (StringUtils.isNotBlank(productType) && AmazonConstant.PRODUCT_TYPE_ATTR_LIST.contains(productType)) {
            return true;
        }
        return false;
    }

    private void generateElementByElementWrapper(Element element, ElementWrapper elementWrapper) {
        if (elementWrapper == null) {
            return;
        }
        boolean flag =  elementWrapper.getIgnore() || elementWrapper.getSelected() || elementWrapper.getRequired();
        if (!flag) {
            return;
        }

        if (!elementWrapper.getIsLeaf()) {
            Element sub = element.create(elementWrapper.getName());
            for (Entry<String, ElementWrapper> entry : elementWrapper.getItems().entrySet()) {
                if (entry.getValue() != null) {
                    generateElementByElementWrapper(sub, entry.getValue());
                }
            }
        }
        else {
            int occurs = elementWrapper.getOccurs();
            List<AttributeWrapper> attrs = elementWrapper.getAttrs();
            TypeWrapper type = elementWrapper.getType();
            for (int i = 0; i < occurs; i++) {
                String value = AmazonUtils.getListIndexValue(elementWrapper.getValues(), i);
                if (StringUtils.isNotEmpty(value)) {
                    if (type != null && "xsd:normalizedString".equals(type.getName())) {
                        value = AmazonUtils.toHtml(value);
                    }
                    Element sub = element.create(elementWrapper.getName(), value);
                    if (CollectionUtils.isNotEmpty(attrs)) {
                        for (AttributeWrapper attrWrapper : attrs) {
                            String attrValue = AmazonUtils.getListIndexValue(attrWrapper.getValues(), i);
                            if (StringUtils.isNotEmpty(attrValue)) {
                                sub.addAttr(attrWrapper.getName(), attrValue);
                            }
                        }
                    }
                }
            }
        }
    }
}
