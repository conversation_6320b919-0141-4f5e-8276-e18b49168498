package com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import io.swagger.client.enums.ReportType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * JP站点 report解析 根据表头解析JP表头语种不一样所以单独处理
 */
@Slf4j
public class InventoryReportSpTypeWithCsvStrategy extends BaseInventoryReportSpTypeWithCsvStrategy {

    public static final List<String> MARKETPLACES = Arrays.asList("US", "CA", "UK", "DE", "IT", "ES", "MX", "AE", "NL", "BR", "TR", "SE", "PL", "FR","AU","BE","IE");

    public static final String REPORT_TYPE = ReportType.GET_FLAT_FILE_OPEN_LISTINGS_DATA.getName();

    public static final String SELLER_SKU  = "sku";

    public InventoryReportSpTypeWithCsvStrategy(AmazonAccount account, String reportType) {
        super(account, reportType, AmazonConstant.getMarketplaceIdsByMarketplaces(MARKETPLACES.toArray(new String[MARKETPLACES.size()])));
    }

    @Override
    public List<String[]> filterAndTransferLines(List<String> lines, Function<String[], Boolean> filter) {
        if (CollectionUtils.isEmpty(lines)) {
            return CommonUtils.emptyList();
        }
        try {
            String accountNumber = getSyncSpProductData().getAccount().getAccountNumber();
            log.warn("[{}] has {} reportLines.", accountNumber, lines.size());
            if (lines.size() ==1) {
                log.warn("[{}]  实际同步库存报告成功，amazon返回的文件没有数据.", accountNumber);
                return CommonUtils.emptyList();
            }
            // 过滤数据
            List<String[]> lineSplits = lines.stream()
                    .filter(row -> StringUtils.isNotBlank(row))
                    .map(row -> StringUtils.splitPreserveAllTokens(row, "\t"))
                    .filter(splits -> {
                        return filter == null ? true : BooleanUtils.toBoolean(filter.apply(splits));
                    }).collect(Collectors.toList());
            log.warn("[{}]  has {} reportLines after fitler.", accountNumber, lineSplits.size());
            return lineSplits;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return CommonUtils.emptyList();
    }

    @Override
    public void fillLineData2AmazonProductListing(AmazonProductListing amazonProductListing, String[] splits) {
        if (!checkParams(amazonProductListing, splits)) {
            return;
        }

        String sellerSku = getColumnValue("sku", splits);
        amazonProductListing.setSellerSku(sellerSku);
        amazonProductListing.setAttribute2(StringUtils.upperCase(sellerSku));
        String price = getColumnValue("price", splits);
        if (StringUtils.isNotEmpty(price)) {
            if (price.contains(",")) {
                price = price.replaceAll(",", ".");
            }
            amazonProductListing.setPrice(Double.valueOf(price));
        }
        String quantity = getColumnValue("quantity", splits);
        if (StringUtils.isNotEmpty(quantity)) {
            amazonProductListing.setQuantity(Integer.valueOf(quantity));
        }
        if (ObjectUtils.isEmpty(amazonProductListing.getQuantity())) {
            amazonProductListing.setQuantity(0);
        }
        amazonProductListing.setSonAsin(getColumnValue("asin", splits));
    }

    @Override
    protected String getTableSellerSku() {
        return SELLER_SKU;
    }
}
