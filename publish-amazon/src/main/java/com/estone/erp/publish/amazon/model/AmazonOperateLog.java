package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonOperateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column amazon_operate_log.id
     */
    private Long id;

    /**
     * 类型 database column amazon_operate_log.type
     */
    private String type;

    /**
     * 账号 database column amazon_operate_log.account_number
     */
    private String accountNumber;

    /**
     * 业务编号 database column amazon_operate_log.business_id
     */
    private Integer businessId;

    /**
     * 字段名 database column amazon_operate_log.field_name
     */
    private String fieldName;

    /**
     * 改后 database column amazon_operate_log.before
     */
    private String before;

    /**
     * 改前 database column amazon_operate_log.after
     */
    private String after;

    /**
     * 记录信息 database column amazon_operate_log.message
     */
    private String message;

    /**
     * 创建人 database column amazon_operate_log.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column amazon_operate_log.create_date
     */
    private Timestamp createDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AmazonOperateLog that = (AmazonOperateLog) o;

        if (!type.equals(that.type)) return false;
        if (!businessId.equals(that.businessId)) return false;
        if (!fieldName.equals(that.fieldName)) return false;
        if (!before.equals(that.before)) return false;
        return after.equals(that.after);
    }

    @Override
    public int hashCode() {
        int result = type.hashCode();
        result = 31 * result + businessId.hashCode();
        result = 31 * result + fieldName.hashCode();
        result = 31 * result + before.hashCode();
        result = 31 * result + after.hashCode();
        return result;
    }
}