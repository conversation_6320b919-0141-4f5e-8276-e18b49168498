package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.request.StatisticsOffLinkRequest;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTaskOffLinkListingLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
* <p>
* amazon任务下架链接报告表 前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-02-12
*/
@Slf4j
@RestController
@RequestMapping("/amazonOfflineConfigAccountReport")
public class AmazonOfflineConfigAccountReportController {

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private AmazonTaskOffLinkListingLogService amazonTaskOffLinkListingLogService;


    /**
     * 按店铺统计可下架链接
     */
    @RequestMapping("/statisticsOffLinkByAccountNumber")
    public ApiResult<String> statisticsOffLinkByAccountNumber(@RequestBody StatisticsOffLinkRequest request) {
        if (CollectionUtils.isEmpty(request.getAccountNumbers())) {
            return ApiResult.newSuccess("查询Amazon所有启用的店铺为空");
        }
        List<AmazonAccountRelation> amazonAccountRelationList = getAmazonAccount(request.getAccountNumbers());
        if (CollectionUtils.isEmpty(amazonAccountRelationList)) {
            return ApiResult.newSuccess("查询Amazon所有启用的店铺为空");
        }
        Integer taskType = request.getTaskType();
        AmazonOfflineEnums.Type codeType = AmazonOfflineEnums.Type.getCodeType(taskType);
        if (codeType == null) {
            return ApiResult.newSuccess("任务类型错误");
        }
        for (AmazonAccountRelation accountRelation : amazonAccountRelationList) {
            AmazonExecutors.ACCOUNT_LIMIT_POOL.execute(() -> {
                try {
                    amazonTaskOffLinkListingLogService.statisticsAccountOffLinkLimitRate(accountRelation, request.getStatisticalDate(), codeType.name(), request.getLimitRatio());
                } catch (Exception e) {
                    log.error("{},按店铺统计可下架链接异常：{}", accountRelation.getAccountNumber(), e.getMessage());
                }
            });
        }
        return ApiResult.newSuccess("按店铺统计可下架链接成功");
    }

    /**
     * 查询Amazon所有启用的店铺
     *
     * @return
     */
    private List<AmazonAccountRelation> getAmazonAccount(List<String> accountNumbers) {
        // 获取全部账号
        AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
        AmazonAccountRelationExample.Criteria criteria = amazonAccountRelationExample.createCriteria()
                .andSaleAccountStatusEqualTo(1);
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            criteria.andAccountNumberIn(accountNumbers);
        }
        String oneColumns = "account_number, account_status, account_country";
        amazonAccountRelationExample.setFiledColumns(oneColumns);
        return amazonAccountRelationService.selectFiledColumnsByExample(amazonAccountRelationExample);
    }


}
