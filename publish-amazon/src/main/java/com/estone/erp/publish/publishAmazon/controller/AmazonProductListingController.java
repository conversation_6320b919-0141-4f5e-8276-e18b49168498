package com.estone.erp.publish.publishAmazon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.product.IdType;
import com.estone.erp.publish.amazon.call.submit.FeedType;
import com.estone.erp.publish.amazon.cardcode.CardCodeType;
import com.estone.erp.publish.amazon.cardcode.util.CardCodeUtils;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.AmazonProductListingEsBulkProcessor;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.AmazonDelInfringementWordDO;
import com.estone.erp.publish.amazon.model.dto.AmazonDelTitleBanWordRequest;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonDeleteProductListingAuditService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.*;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAmazonProductListingUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingCriteria;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingMsgDto;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonSpuTitleForUpdateDto;
import com.estone.erp.publish.publishAmazon.service.AmazonJSONListingFeedService;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.google.common.collect.Lists;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.CompetitiveSummaryBatchResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> amazon_product_listing
 *         2020-12-19 16:28:50
 */
@RestController
@RequestMapping("amazonProductListing")
@Slf4j
public class AmazonProductListingController {
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonCallService amazonCallService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonProductListingEsBulkProcessor amazonProductListingEsBulkProcessor;
    @Autowired
    private AmazonInfringementWordHelper amazonInfringementWordHelper;

    @Autowired
    private AmazonDeleteProductListingAuditService amazonDeleteProductListingAuditService;
    @Autowired
    private AmazonJSONListingFeedService amazonJSONListingFeedService;

    @PostMapping
    public ApiResult<?> postAmazonProductListing(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        String userName = null;
        if (StringUtils.isNotBlank(WebUtils.getUserName())) {
            userName = WebUtils.getUserName();
        }
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonProductListing": // 查询列表
                    CQuery<AmazonProductListingCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonProductListingCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonProductListing> results = amazonProductListingService.search(cquery);
                    return results;
                case "addAmazonProductListing": // 添加
                    AmazonProductListing amazonProductListing = requestParam.getArgsValue(new TypeReference<AmazonProductListing>() {
                    });
                    amazonProductListingService.insert(amazonProductListing);
                    return ApiResult.newSuccess(amazonProductListing);
                case "updateAmazonProductListing": // 单个修改
                    AmazonProductListing updateAmazonProductListing = requestParam.getArgsValue(new TypeReference<AmazonProductListing>() {
                    });
                    Asserts.isTrue(updateAmazonProductListing != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updateAmazonProductListing.getAccountNumber() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updateAmazonProductListing.getSite() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updateAmazonProductListing.getSellerSku() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(userName != null, ErrorCode.PARAM_EMPTY_ERROR);
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    AmazonProductListingExample.Criteria criteria = example.createCriteria();
                    criteria.andSellerSkuEqualTo(updateAmazonProductListing.getSellerSku());
                    criteria.andAccountNumberEqualTo(updateAmazonProductListing.getAccountNumber());
                    updateAmazonProductListing.setUpdatedBy(userName);
                    amazonProductListingService.updateByExampleSelective(updateAmazonProductListing, example);
                    this.handleUpdateEsAmazonProductListing(example, updateAmazonProductListing);
                    return ApiResult.newSuccess(updateAmazonProductListing);
                case "updateImageAndPublishImage": // 单个修改
                    AmazonProductListing updatePublishAmazonProductListing = requestParam.getArgsValue(new TypeReference<AmazonProductListing>() {
                    });
                    Asserts.isTrue(updatePublishAmazonProductListing != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updatePublishAmazonProductListing.getAccountNumber() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updatePublishAmazonProductListing.getSite() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(updatePublishAmazonProductListing.getSellerSku() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(userName != null, ErrorCode.PARAM_EMPTY_ERROR);
                    updatePublishAmazonProductListing.setUpdatedBy(userName);
                    AmazonProductListingExample productListingExample = new AmazonProductListingExample();
                    AmazonProductListingExample.Criteria productListingExampleCriteria = productListingExample.createCriteria();
                    productListingExampleCriteria.andSellerSkuEqualTo(updatePublishAmazonProductListing.getSellerSku());
                    productListingExampleCriteria.andAccountNumberEqualTo(updatePublishAmazonProductListing.getAccountNumber());
                    amazonProductListingService.updateByExampleSelective(updatePublishAmazonProductListing, productListingExample);
                    this.handleUpdateEsAmazonProductListing(productListingExample, updatePublishAmazonProductListing);
                    AmazonVariantBO newAmazonVariant = new AmazonVariantBO();
                    newAmazonVariant.setRelationType(ProcessingReportTriggleType.NewListing.name());
                    newAmazonVariant.setAccountNumber(updatePublishAmazonProductListing.getAccountNumber());
                    newAmazonVariant.setArticleNumber(updatePublishAmazonProductListing.getArticleNumber());
                    newAmazonVariant.setSellerSku(updatePublishAmazonProductListing.getSellerSku());
                    String mainImage = updatePublishAmazonProductListing.getMainImage();
                    if (StringUtils.isNotEmpty(mainImage)) {
                        newAmazonVariant.setMainImage(mainImage);
                    }
                    String sampleImage = updatePublishAmazonProductListing.getSampleImage();
                    if (StringUtils.isNotEmpty(sampleImage)) {
                        newAmazonVariant.setSampleImage(sampleImage);
                    }
                    String extraImages = updatePublishAmazonProductListing.getExtraImages();
                    if (StringUtils.isNotBlank(extraImages)) {
                        List<String> extraImagesList = JSON.parseArray(extraImages, String.class);
                        newAmazonVariant.setExtraImagesList(extraImagesList);
                        newAmazonVariant.setExtraImages(extraImages);
                    }

                    amazonCallService.publishAmazonVariantImage(newAmazonVariant, SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue());
                    return ApiResult.newSuccess(updatePublishAmazonProductListing);
                case "batchRetireProduct":
                    AmazonProductListingDto amazonProductListingDto = requestParam
                            .getArgsValue(new TypeReference<AmazonProductListingDto>() {});
                    Asserts.isTrue(amazonProductListingDto != null, ErrorCode.PARAM_EMPTY_ERROR);
                    String remark = amazonProductListingDto.getRemark();
                    if (StringUtils.isEmpty(remark)){
                        return ApiResult.newError("请填写删除原因");
                    }
                    List<AmazonProductListing> batchRetireProductListing = amazonProductListingDto.getAmazonProductListingList();
                    if (CollectionUtils.isEmpty(batchRetireProductListing)) {
                        return ApiResult.newError("请选择需要下架的数据");
                    }

                    // 过滤单品状态为清仓，甩卖，且库存+在途-待发>0，且SKU在对应店铺不存在禁售站点的产品
                    Iterator<AmazonProductListing> it = batchRetireProductListing.iterator();
                    while (it.hasNext()) {
                        try {
                            AmazonProductListing productListing = it.next();
                            String skuStatus = productListing.getSkuStatus();
                            Integer skuStock = SkuStockUtils.getSkuSystemStock(productListing.getArticleNumber());
                            String normalSale = productListing.getNormalSale();
                            String site = productListing.getSite();
                            Boolean needFilter = AmazonListingUtils.checkClearanceReductionListing(skuStatus, skuStock, normalSale, site);
                            if (needFilter) {
                                // 添加处理报告
                                AmazonProcessReport report = DeleteAmazonListingUtils.newAmazonProcessReport(productListing, FeedType.DELETET_LISTINGS_DATA, WebUtils.getUserName());
                                report.setResultMsg("该SKU单品状态为清仓，甩卖，且SKU在对应店铺不禁售，不允许下架");
                                report.setStatusCode(ProcessingReportStatusCode.Complete.name());
                                report.setFinishDate(new Date());
                                amazonProcessReportService.insert(report);
                                it.remove();
                            }
                        } catch (Exception e) {
                            log.error("清仓甩卖SKU下架限制报错：" + e.getMessage());
                        }
                    }

                    String retireMsg = "";
                    for (AmazonProductListing retireProductListing : batchRetireProductListing) {
                        String site = retireProductListing.getSite();
                        String sellerSku = retireProductListing.getSellerSku();
                        String accountNumber = retireProductListing.getAccountNumber();
                        if (StringUtils.isBlank(site) || StringUtils.isBlank(sellerSku) || StringUtils.isBlank(accountNumber)) {
                            retireMsg = retireMsg + "site: " + site + " sellerSku: " + sellerSku + " accountNumber: " + accountNumber + "\n";
                        }
                    }
                    if (StringUtils.isNotBlank(retireMsg)) {
                        return ApiResult.newError("请求数据不完整" + retireMsg);
                    }

                    List<EsAmazonProductListing> esAmazonProductListings = batchRetireProductListing.stream().map(listing -> {
                        String accountNumber = listing.getAccountNumber();
                        String sonAsin = listing.getSonAsin();
                        EsAmazonProductListing esAmazonProductListing = new EsAmazonProductListing();
                        esAmazonProductListing.setAccountNumber(accountNumber);
                        esAmazonProductListing.setSonAsin(sonAsin);
                        return esAmazonProductListing;
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(esAmazonProductListings)) {
                        return ApiResult.newSuccess("请求成功，请稍后查看处理报告");
                    }


                    List<EsAmazonProductListing> listingList = AmazonListingUtils.filterFBAExistAsin(esAmazonProductListings);
                    if (CollectionUtils.isEmpty(listingList)) {
                        return ApiResult.newSuccess("过滤FBA Asin后无可下架数据");
                    }
                    Map<String, EsAmazonProductListing> filterListingMap = listingList.stream().collect(Collectors.toMap(listing-> listing.getAccountNumber() + listing.getSonAsin(), Function.identity(), (o1, o2) -> o1));
                    batchRetireProductListing.removeIf(listing -> {
                        String key = listing.getAccountNumber() + listing.getSonAsin();
                        EsAmazonProductListing esAmazonProductListing = filterListingMap.get(key);
                        return esAmazonProductListing == null;
                    });

                    if (CollectionUtils.isEmpty(batchRetireProductListing)) {
                        return ApiResult.newSuccess("过滤FBA Asin后无可下架数据");
                    }
                    // 加入权限判断 如果是主管或超级管理员，则直接删除，如果是 组长或者销售员，则生成删除列表
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);

                    if (superAdminOrEquivalent.getResult() || currentIsSupervisor()) {
                        DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                        deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Operate_Listing_Delete);
                        deleteAmazonListingDto.setRemarkParam(remark);
                        retireMsg = DeleteAmazonListingUtils.batchRetireProduct(batchRetireProductListing,null, deleteAmazonListingDto);
                    } else {
                        amazonDeleteProductListingAuditService.batchGenerationReview(batchRetireProductListing, remark);
                    }

                    if (StringUtils.isNotBlank(retireMsg)) {
                        return ApiResult.newError(retireMsg);
                    }
                    return ApiResult.newSuccess("请求成功，请稍后查看处理报告");
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 当前用户是否为主管
     * 所有主管类型
     * @return
     */
    public boolean currentIsSupervisor(){
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.tokenUser();
        if (newUserApiResult.isSuccess()) {
            NewUser result = newUserApiResult.getResult();
            String positionName = result.getPositionName();
            return StringUtils.containsIgnoreCase(positionName, "主管");
        }
        return false;
    }

    /**
     * 更新Es数据
     *
     * @param productListingExample
     * @param amazonProductListing
     */
    private void handleUpdateEsAmazonProductListing(AmazonProductListingExample productListingExample, AmazonProductListing amazonProductListing) {
        try {
            List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectByExample(productListingExample, amazonProductListing.getSite());
            if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
                AmazonProductListing dbProductListing = amazonProductListingList.get(0);
                EsAmazonProductListing updateEsAmazonProductListing = new EsAmazonProductListing();
                BeanUtils.copyProperties(dbProductListing, updateEsAmazonProductListing);
                updateEsAmazonProductListing.setId(dbProductListing.getAccountNumber() + "_" + dbProductListing.getSellerSku());

                // 查询es 设置扩展信息（es有db无的字段数据）
                EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(dbProductListing.getAccountNumber() + "_" + dbProductListing.getSellerSku());
                EsAmazonProductListingUtils.setEsAmazonProductExtends(updateEsAmazonProductListing, esAmazonProductListing);
                // 毛利，毛利率
                if (null != amazonProductListing.getGrossProfit()) {
                    updateEsAmazonProductListing.setGrossProfit(amazonProductListing.getGrossProfit());
                }
                if (null != amazonProductListing.getGrossProfitRate()) {
                    updateEsAmazonProductListing.setGrossProfitRate(amazonProductListing.getGrossProfitRate());
                }
                esAmazonProductListingService.save(updateEsAmazonProductListing);
            }
        } catch (Exception e) {
            log.error(String.format("更新Es数据失败 站点： %s 账号： %s sellerSku： %s errorMsg：%s", amazonProductListing.getSite(),
                    amazonProductListing.getAccountNumber(), amazonProductListing.getSellerSku(), e.getMessage()));
        }
    }

    @GetMapping(value = "/{id}/{site}")
    public ApiResult<?> getAmazonProductListing(@PathVariable(value = "id", required = true) Long id, @PathVariable(value = "site", required = true) String site) {
        AmazonProductListing amazonProductListing = amazonProductListingService.selectByPrimaryKey(id, site);
        return ApiResult.newSuccess(amazonProductListing);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAmazonProductListing(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAmazonProductListing": // 单个修改
                    AmazonProductListing amazonProductListing = requestParam.getArgsValue(new TypeReference<AmazonProductListing>() {
                    });
                    amazonProductListingService.updateByPrimaryKeySelective(amazonProductListing);
                    return ApiResult.newSuccess(amazonProductListing);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * amazonProductListing 数据初始化
     *
     * @return
     */
    @GetMapping(value = "/init")
    public ApiResult<?> init() {
        amazonProductListingService.init();
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/getInfo")
    public ApiResult<?> getInfo() {
        ProductUtils.getSkuInfo("9SD800092-2");
        return ApiResult.newSuccess();
    }

    /**
     * amazonProductListing 刷新未拿到sku数据的信息
     *
     * @return
     */
    @PostMapping(value = "/refreshListingBySku")
    public ApiResult<?> refreshListingBySku(MultipartFile file) {
        amazonProductListingService.refreshListingBySku(file);
        return ApiResult.newSuccess();
    }

    /**
     * 获取SPU标题 批量修改标题的默认值
     *
     * @return
     */
    @PostMapping(value = "/getSpuTitleForUpdate")
    public ApiResult<?> getSpuTitleForUpdate(@RequestBody List<AmazonSpuTitleForUpdateDto> dtos) {
        List<String> skus = dtos.stream().map(o -> o.getArticleNumber()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skus)) {
            ApiResult.newSuccess(dtos);
        }

        // 根据子sku获取主SKU
        Map<String, String> skuMap = ProductUtils.getMainSkuBySubSku(skus);

        // SPU集合 获取标题信息
        List<String> spus = new ArrayList<String>(skuMap.values());
        if (CollectionUtils.isNotEmpty(spus)) {
            ResponseJson spuTitleResp = ProductUtils.getSpuTitles(spus);
            if (spuTitleResp.isSuccess()) {
                // 匹配标题
                AmazonMatchProdInfoUtil.listingMatchSpuTitle(dtos, skuMap, spuTitleResp);
            }
        }

        return ApiResult.newSuccess(dtos);
    }

    /**
     * 删除标题侵权词
     * @param delTitleBanWordRequest request
     * @return id:text
     */
    @PostMapping("delTitleBanWord")
    public ApiResult<Map<String, String>> delTitleBanWord(@RequestBody List<AmazonDelTitleBanWordRequest> delTitleBanWordRequest) {
        Map<String,String> resultMap = new HashMap<>();

        Map<String, String> accountSiteMap = new HashMap<>();
        for (AmazonDelTitleBanWordRequest request : delTitleBanWordRequest) {
            String accountNumber = request.getAccountNumber();
            String site = accountSiteMap.get(accountNumber);
            if (site == null) {
                String accountSite = amazonProductListingService.getSiteByAccount(accountNumber);
                accountSiteMap.put(accountNumber, accountSite);
                site = accountSite;
            }
            AmazonDelInfringementWordDO infringementWordDO = new AmazonDelInfringementWordDO();
            infringementWordDO.setAccountNumber(accountNumber);
            infringementWordDO.setSite(site);
            infringementWordDO.setSonSku(request.getSonSku());
            infringementWordDO.setWenAnType(request.getWenAnType());
            infringementWordDO.setSourceTexts(Lists.newArrayList(request.getText()));
            amazonInfringementWordHelper.delTextInfringementWords(infringementWordDO);
            resultMap.put(request.getId(), infringementWordDO.getSourceTexts().get(0));
        }
        return ApiResult.newSuccess(resultMap);
    }

    @PostMapping(value = "/publishProductListinPartialUpdate")
    public ApiResult<Object> batchPublishVariantPartialUpdate(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        AmazonProductListingDto amazonProductListingDto = requestParam
                .getArgsValue(new TypeReference<AmazonProductListingDto>() {
                });
        Asserts.isTrue(amazonProductListingDto != null, ErrorCode.PARAM_EMPTY_ERROR);
        String userName = null;
        if (StringUtils.isNotBlank(WebUtils.getUserName())) {
            userName = WebUtils.getUserName();
        }
        List<AmazonProductListing> amazonProductListingList = amazonProductListingDto.getAmazonProductListingList();
        if (CollectionUtils.isEmpty(amazonProductListingList) || StringUtils.isEmpty(userName)
                || StringUtils.isEmpty(method)) {
            return ApiResult.newError("参数不能为空");
        }
        switch (method) {
            case "batchProductListingPriceInventory":
                List<String> feedTypeList = amazonProductListingDto.getFeedTypeList();
                if (CollectionUtils.isEmpty(feedTypeList)) {
                    return ApiResult.newError("修改类型不能为空");
                }

                List<AmazonVariantBO> amazonVariants = new ArrayList<>(amazonProductListingList.size());
                for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                    AmazonProductListing localData = amazonProductListingService.selectPriceInventoryListingData(amazonProductListing.getAccountNumber(), amazonProductListing.getSellerSku());

                    AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                    amazonVariantBO.setAccountNumber(amazonProductListing.getAccountNumber());
                    amazonVariantBO.setArticleNumber(amazonProductListing.getArticleNumber());
                    amazonVariantBO.setSellerSku(amazonProductListing.getSellerSku());
                    amazonVariantBO.setRelationType(ProcessingReportTriggleType.NewListing.name());
                    amazonVariantBO.setPrice(amazonProductListing.getPrice());
                    amazonVariantBO.setSalePrice(amazonProductListing.getSalePrice());
                    amazonVariantBO.setQuantity(amazonProductListing.getQuantity());

                    try {
                        amazonVariantBO.setSaleStartDate(new Timestamp(amazonProductListing.getSaleStartDate().getTime()));
                        amazonVariantBO.setSaleEndDate(new Timestamp(amazonProductListing.getSaleEndDate().getTime()));
                    } catch (Exception e) {
                    }
                    if (localData != null) {
                        amazonVariantBO.setPreviousPriceValue(String.valueOf(localData.getPrice()));
                        amazonVariantBO.setAfterPriceValue(String.valueOf(amazonProductListing.getPrice()));

                        amazonVariantBO.setPreviousQuantityValue(String.valueOf(localData.getQuantity()));
                        amazonVariantBO.setAfterQuantityValue(String.valueOf(amazonProductListing.getQuantity()));
                    }
                    amazonVariants.add(amazonVariantBO);

                    // 修改本地ES数据
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    AmazonProductListingExample.Criteria criteria = example.createCriteria();
                    criteria.andSellerSkuEqualTo(amazonProductListing.getSellerSku());
                    criteria.andAccountNumberEqualTo(amazonProductListing.getAccountNumber());
                    amazonProductListing.setUpdatedBy(userName);
                    this.handleUpdateEsAmazonProductListing(example, amazonProductListing);
                }
                amazonCallService.publishAmazonVariants(amazonVariants, feedTypeList);
                return ApiResult.newSuccess();
            case "batchProductListingImage":
                // 过滤主图、附图、样品图全为空的变体
                Map<String, List<AmazonProductListing>> accountAmazonProductListingMap = new HashMap<>();
                accountAmazonProductListingMap = amazonProductListingList.stream().collect(Collectors.groupingBy(obj -> obj.getAccountNumber()));
                String columns = "accountNumber,sellerSku,articleNumber,mainImage,sampleImage,extraImages";
                for (Map.Entry<String, List<AmazonProductListing>> entry : accountAmazonProductListingMap.entrySet()) {
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    List<String> sellerSKuList = entry.getValue().stream().map(amazonProductListing -> amazonProductListing.getSellerSku()).collect(Collectors.toList());
                    AmazonProductListingExample.Criteria criteria = example.createCriteria();
                    criteria.andAccountNumberEqualTo(entry.getKey());
                    criteria.andSellerSkuIn(sellerSKuList);
                    criteria.andIsOnlineEqualTo(true);
                    String site = entry.getValue().get(0).getSite();
                    example.setColumns(columns);
                    List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example, site);
                    List<AmazonProductListing> newAmazonProductListingList = amazonProductListings.stream().filter(amazonProductListing -> {
                        if (StringUtils.isEmpty(amazonProductListing.getMainImage())
                                && StringUtils.isEmpty(amazonProductListing.getExtraImages())) {
                            return false;
                        }
                        return true;
                    }).collect(Collectors.toList());
                    List<AmazonVariantBO> amazonVariantImageList = new ArrayList<>();
                    for (AmazonProductListing amazonProductListing : newAmazonProductListingList) {
                        AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                        amazonVariantBO.setRelationType(ProcessingReportTriggleType.NewListing.name());
                        amazonVariantBO.setAccountNumber(amazonProductListing.getAccountNumber());
                        amazonVariantBO.setSellerSku(amazonProductListing.getSellerSku());
                        String mainImage = amazonProductListing.getMainImage();
                        if (StringUtils.isNotEmpty(mainImage)) {
                            amazonVariantBO.setMainImage(mainImage);
                        }
                        String sampleImage = amazonProductListing.getSampleImage();
                        if (StringUtils.isNotEmpty(sampleImage)) {
                            amazonVariantBO.setSampleImage(sampleImage);
                        }
                        String extraImages = amazonProductListing.getExtraImages();
                        if (StringUtils.isNotBlank(extraImages)) {
                            List<String> extraImagesList = JSON.parseArray(extraImages, String.class);
                            amazonVariantBO.setExtraImagesList(extraImagesList);
                            amazonVariantBO.setExtraImages(extraImages);
                        }
                        amazonVariantImageList.add(amazonVariantBO);
                    }
                    amazonCallService.publishAmazonVariants(amazonVariantImageList, Arrays.asList(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue()));
                }
                return ApiResult.newSuccess();
            case "batchProductListingEAN":
                String prefixStr = null;
                Map<String, List<AmazonProductListing>> accountAmazonListingMap = new HashMap<>();
                accountAmazonListingMap = amazonProductListingList.stream().collect(Collectors.groupingBy(obj -> obj.getAccountNumber()));
                List<String> eanList = new ArrayList<>(amazonProductListingList.size());
                for (Map.Entry<String, List<AmazonProductListing>> entry : accountAmazonListingMap.entrySet()) {
                    SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = null;
                    List<AmazonProductListing> amazonProductListings = entry.getValue();
                    String accountNumber = entry.getKey();
                    try {
                        saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(
                                Platform.Amazon.name(), accountNumber);
                    } catch (Exception e) {
                        return ApiResult.newError("调用销售订单系统获取账号失败" + accountNumber);
                    }
                    if (null != saleAccountAndBusinessResponse || StringUtils.isNotEmpty(saleAccountAndBusinessResponse.getColStr6())) {
                        prefixStr = saleAccountAndBusinessResponse.getColStr6();
                    }
                    eanList = CardCodeUtils.generateCardCodes(CardCodeType.EAN, amazonProductListings.size(), prefixStr, accountNumber, "batchProductListingEAN");
                    int eanSize = 0;
                    List<AmazonVariantBO> amazonVariantEanList = new ArrayList<>();
                    for (AmazonProductListing amazonProductListing : amazonProductListings) {
                        AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                        amazonVariantBO.setRelationType(ProcessingReportTriggleType.NewListing.name());
                        amazonVariantBO.setAccountNumber(amazonProductListing.getAccountNumber());
                        amazonVariantBO.setSellerSku(amazonProductListing.getSellerSku());
                        amazonVariantBO.setStandardProdcutIdType(IdType.EAN);
                        amazonVariantBO.setItemName(null);
                        amazonVariantBO.setItemDescription(null);
                        amazonVariantBO.setStandardProdcutIdValue(eanList.get(eanSize));
                        eanSize++;
                        amazonVariantEanList.add(amazonVariantBO);
                    }
                    amazonCallService.publishVariantProductPartialUpdate(amazonVariantEanList, Arrays.asList(SpFeedType.POST_PRODUCT_DATA.getValue()));
                }
                return ApiResult.newSuccess();
            case "batchProductListingTitle":
                List<AmazonVariantBO> amazonVariantTitleList = new ArrayList<>();
                for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                    AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                    amazonVariantBO.setRelationType(ProcessingReportTriggleType.Listing_Update_Title.name());
                    amazonVariantBO.setAccountNumber(amazonProductListing.getAccountNumber());
                    amazonVariantBO.setSellerSku(amazonProductListing.getSellerSku());

                    AmazonDelInfringementWordDO infringementWordDO = new AmazonDelInfringementWordDO();
                    infringementWordDO.setAccountNumber(amazonProductListing.getAccountNumber());
                    infringementWordDO.setSite(amazonProductListing.getSite());
                    infringementWordDO.setSonSku(amazonProductListing.getArticleNumber());
                    infringementWordDO.setSourceTexts(List.of(amazonProductListing.getItemName()));
                    ApiResult<String> apiResult = amazonInfringementWordHelper.delTextInfringementWords(infringementWordDO);
                    if (!apiResult.isSuccess()) {
                        return ApiResult.newError("删除侵权词异常："+apiResult.getErrorMsg());
                    }
                    String newTitle = infringementWordDO.getSourceTexts().get(0);
                    amazonProductListing.setItemName(newTitle);
                    amazonVariantBO.setItemName(newTitle);
                    amazonVariantTitleList.add(amazonVariantBO);

                    // 修改本地ES数据
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    AmazonProductListingExample.Criteria criteria = example.createCriteria();
                    criteria.andSellerSkuEqualTo(amazonProductListing.getSellerSku());
                    criteria.andAccountNumberEqualTo(amazonProductListing.getAccountNumber());
                    amazonProductListing.setUpdatedBy(userName);
                    this.handleUpdateEsAmazonProductListing(example, amazonProductListing);
                }
                // 修改本地mysql数据库数据
                amazonProductListingService.batchUpdateBySellerSkuAndAccountNumber(amazonProductListingList);
                amazonCallService.publishVariantProductPartialUpdate(amazonVariantTitleList, Collections.singletonList(SpFeedType.POST_PRODUCT_DATA.getValue()));

                return ApiResult.newSuccess();
            case "batchListingFulfillmentLatency":
                Integer fulfillmentLatency = amazonProductListingDto.getFulfillmentLatency();
                if (null == fulfillmentLatency) {
                    return ApiResult.newError("备货期值不能为空");
                }
                Map<String, List<AmazonProductListing>> accountListingMap = new HashMap<>();
                accountListingMap = amazonProductListingList.stream().collect(Collectors.groupingBy(obj -> obj.getAccountNumber()));
                for (Map.Entry<String, List<AmazonProductListing>> entry : accountListingMap.entrySet()) {
                    List<AmazonProductListing> amazonProductListings = entry.getValue();
                    List<AmazonVariantBO> amazonVariantBOs = new ArrayList<>();
                    for (AmazonProductListing amazonProductListing : amazonProductListings) {
                        AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                        Integer quantity = amazonProductListing.getQuantity();
                        if (null == quantity) {
                            return ApiResult.newError( amazonProductListing.getSellerSku() + "库存值不能为空");
                        }
                        amazonVariantBO.setRelationType(ProcessingReportTriggleType.Listing_Fulfillment_Latency.name());
                        amazonVariantBO.setAccountNumber(amazonProductListing.getAccountNumber());
                        amazonVariantBO.setSellerSku(amazonProductListing.getSellerSku());
                        amazonVariantBO.setPreviousFulfillmentLatencyValue(amazonProductListing.getOldFulfillmentLatencyValue());
                        amazonVariantBO.setAfterFulfillmentLatencyValue(String.valueOf(fulfillmentLatency));
                        amazonVariantBO.setFulfillmentLatency(fulfillmentLatency);
                        amazonVariantBO.setAfterQuantityValue(quantity.toString());
                        amazonVariantBO.setPreviousQuantityValue(quantity.toString());
                        amazonVariantBO.setQuantity(quantity);
                        amazonVariantBOs.add(amazonVariantBO);
                    }
                    amazonCallService.publishAmazonVariants(amazonVariantBOs, Arrays.asList(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue()));
                }
                return ApiResult.newSuccess();
            case "batchProductListingLocalOffline": // 修改本地数据为下架状态
                List<EsAmazonProductListing> esAmazonProductListings = new ArrayList<>();
                Date dateofflineDate = new Date();
                String remark = AmazonOfflineEnums.Type.Sale_Handle_Status.getDesc();
                for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                    amazonProductListing.setIsOnline(false);
                    amazonProductListing.setAttribute3(remark);
                    amazonProductListing.setAttribute4(userName);
                    amazonProductListing.setOfflineDate(dateofflineDate);
                    amazonProductListing.setUpdateDate(dateofflineDate);

                    EsAmazonProductListing esAmazonProductListing = new EsAmazonProductListing();
                    esAmazonProductListings.add(esAmazonProductListing);
                    esAmazonProductListing.setId(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
                    esAmazonProductListing.setIsOnline(false);
                    esAmazonProductListing.setAttribute3(remark);
                    esAmazonProductListing.setOfflineDate(dateofflineDate);
                    esAmazonProductListing.setAttribute4(userName);
                }

                // 修改本地mysql数据库数据
                amazonProductListingService.batchUpdateBySellerSkuAndAccountNumber(amazonProductListingList);
                // 修改本地es数据
                amazonProductListingEsBulkProcessor.batchUpdateProductListingLocalOffline(esAmazonProductListings);
                return ApiResult.newSuccess();
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/testShippingCost")
    public ApiResult<?> testShippingCost(@RequestParam("sku") String sku,@RequestParam("accountNumber") String accountNumber) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("账号信息不全");
        }

        ApiResult<CompetitiveSummaryBatchResponse> syncShippingCostApiResult = AmazonSpLocalServiceUtils.getCompetitivePricing(Arrays.asList(sku), amazonSpAccount);
        return syncShippingCostApiResult;
    }

    /**
     * 提供给产品系统,根据子Asin查询标题描述信息
     * @param sonAsin 子Asin
     * @return
     */
    @GetMapping(value = "/api/toProduct/listingMsg")
    public ApiResult<AmazonProductListingMsgDto> apiToProductListingMsgDto(@RequestParam("sonAsin") String sonAsin){
        return amazonProductListingService.apiToProductListingMsgDto(sonAsin);
    }

    /**
     * 批量更新价格和库存
     *
     * @param requestParam
     * @return
     */
    @PostMapping("batchUpdatePriceAndInventory")
    public ApiResult<String> batchUpdatePriceAndInventory(@RequestBody AmazonProductListingDto requestParam) {
        return amazonJSONListingFeedService.batchUpdatePriceAndInventory(requestParam);
    }


    /**
     * 批量更新图片和发布图片
     */
    @PostMapping("updateImageAndPublishImage")
    public ApiResult<String> updateImageAndPublishImage(@RequestBody AmazonProductListing requestParam) {
        if (StringUtils.isBlank(requestParam.getAccountNumber())) {
            return ApiResult.newError("账号不能为空");
        }
        if (StringUtils.isBlank(requestParam.getSite())) {
            return ApiResult.newError("站点不能为空");
        }
        if (StringUtils.isBlank(requestParam.getSellerSku())) {
            return ApiResult.newError("sellerSku不能为空");
        }
        if (StringUtils.isBlank(requestParam.getMainImage())) {
            return ApiResult.newError("主图不能为空");
        }
        if (StringUtils.isBlank(requestParam.getExtraImages())) {
            return ApiResult.newError("附图不能为空");
        }
        if (StringUtils.isBlank(requestParam.getSampleImage())) {
            return ApiResult.newError("样品图不能为空");
        }
        return amazonJSONListingFeedService.updateImageAndPublishImage(requestParam);
    }


    /**
     * 批量更新listing图片
     */
    @PostMapping("batchProductListingImage")
    public ApiResult<String> batchProductListingImage(@RequestBody List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("参数不能为空");
        }
        return amazonJSONListingFeedService.batchProductListingImage(amazonProductListingList);
    }

    /**
     * 批量更新listing标题
     */
    @PostMapping("batchProductListingTitle")
    public ApiResult<String> batchProductListingTitle(@RequestBody List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("参数不能为空");
        }
        return amazonJSONListingFeedService.batchProductListingTitle(amazonProductListingList);
    }

    /**
     * 批量更新备货期
     */
    @PostMapping("batchListingFulfillmentLatency")
    public ApiResult<String> batchListingFulfillmentLatency(@RequestBody AmazonProductListingDto requestParam) {
        if (requestParam == null) {
            return ApiResult.newError("参数不能为空");
        }
        Integer fulfillmentLatency = requestParam.getFulfillmentLatency();
        if (null == fulfillmentLatency) {
            return ApiResult.newError("备货期值不能为空");
        }
        return amazonJSONListingFeedService.batchListingFulfillmentLatency(requestParam);
    }

}
