package com.estone.erp.publish.amazon.mq.templatestatus;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishHandler;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.TemplatePublishStatusMessage;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * JSON 模版刊登状态处理类
 */
@Slf4j
@Component
public class AmazonTemplatePublishStatusHelper {

    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;

    @Autowired
    private AmazonPublishStatusChecker statusChecker;

    @Autowired
    private AmazonPublishStatusUpdater statusUpdater;

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Autowired
    private SaleAccountService saleAccountService;

    @Autowired
    private AmazonPublishHandler amazonPublishHandler;

    @Autowired
    private RabbitMqSender rabbitMqSender;


    /**
     * 执行指定分钟内的刊登中模版数据
     *
     * @return int 处理模版数量
     */
    public int handleAmazonTemplatePublishStatus(int beforeMinutes, int afterMinutes) {
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime afterDateTime = currentDateTime.minusMinutes(afterMinutes);

        AmazonTemplateExample example = new AmazonTemplateExample();
        AmazonTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(afterDateTime))
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.JSON.getCode());
        example.setOrderByClause("last_update_date");
        List<Integer> amazonTemplateIdList = amazonTemplateService.batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return 0;
        }
        XxlJobLogger.log("处理模版刊登状态，模版数量：{}", amazonTemplateIdList.size());
        Lists.partition(amazonTemplateIdList, 300).forEach(this::updatedTemplatePublishStatus);
        return amazonTemplateIdList.size();
    }

    /**
     * 更新模版刊登状态
     *
     * @param amazonTemplateIdList 模版ID列表
     */
    private void updatedTemplatePublishStatus(List<Integer> amazonTemplateIdList) {
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return;
        }

        // 批量查询模版信息
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria().andIdIn(amazonTemplateIdList);
        example.setColumns("id,step_template_status,seller_id,creation_date,last_update_date");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());

        List<AmazonTemplateBO> templateList = amazonTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(templateList)) {
            log.warn("未找到模版信息，模版ID列表：{}", amazonTemplateIdList);
            return;
        }

        String publishTimeOutTime = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "TEMPLATE_TIME_OUT", 10);
        if (StringUtils.isBlank(publishTimeOutTime)) {
            publishTimeOutTime = "48";
        }
        int timeoutHours = Integer.parseInt(publishTimeOutTime);

        Map<String, List<AmazonTemplateBO>> sellerTemplateMap = templateList.stream()
                .filter(template -> {
                    // 过滤超时模板
                    if (statusChecker.isTemplateTimeout(template, timeoutHours)) {
                        List<AmazonProcessReport> reportList = getProcessReport(template.getId());
                        statusUpdater.updateTimeoutFail(template, reportList, timeoutHours);
                        return false;
                    }
                    return true;
                })
                .filter(template -> !Optional.ofNullable(template.getStepTemplateStatus()).orElse(false))
                .collect(Collectors.groupingBy(AmazonTemplateBO::getSellerId));

        // 按店铺发送消息到模版状态队列
        sellerTemplateMap.forEach((accountNumber, templates) -> {
            List<Integer> templateIds = templates.stream().map(AmazonTemplate::getId).collect(Collectors.toList());
            // 5个模板一组 查询item接口限制20个SKU一个请求
            sendTemplatePublishStatusMessage(accountNumber, templateIds);
        });
    }

    private void publishSameAsinTemplate(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        Map<String, String> sellerSkuAsinMap = listingInfoList.stream().collect(Collectors.toMap(AmazonListingInfo::getSellerSku, AmazonListingInfo::getAsin, (k1, k2) -> k1));
        if (MapUtils.isEmpty(sellerSkuAsinMap)) {
            return;
        }

        String sellerId = template.getSellerId();
        AmazonAccountRelation accountManufacturerInfo = amazonAccountRelationService.getAccountManufacturerInfo(sellerId);
        if (accountManufacturerInfo == null) {
            return;
        }
        String merchantId = accountManufacturerInfo.getMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            return;
        }
        // 查询同套账 asin 刊登
        List<String> sameMerchantAccountNumber = saleAccountService.getSameMerchantAccountNumber(merchantId, SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isEmpty(sameMerchantAccountNumber)) {
            return;
        }
        sameMerchantAccountNumber.forEach(accountNumber -> {
            if (accountNumber.equals(sellerId)) {
                return;
            }
            log.info("同套账 asin 刊登，商家账号：{}，模版ID：{}", accountNumber, template.getId());
            amazonPublishHandler.sameAsinTemplatePublish(template, accountNumber, sellerSkuAsinMap);
        });

    }


    private List<AmazonProcessReport> getProcessReport(Integer templateId) {
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.setFiledColumns("id,status,status_code,relation_id,data_value,feed_type,account_number,relation_type");
        example.createCriteria()
                .andRelationIdEqualTo(templateId)
                .andStatusCodeEqualTo("Processing")
                .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PUBLISH_TEMPLATE.name());

        return amazonProcessReportService.selectFiledColumnsByExample(example);
    }

    /**
     * 处理价格库存同步任务
     *
     * @param beforeMinutes 开始时间（距离当前时间的分钟数）
     * @param afterMinutes  结束时间（距离当前时间的分钟数）
     * @return int 处理的处理报告数量
     */
    public int handlePriceStockSync(int beforeMinutes, int afterMinutes) {
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime afterDateTime = currentDateTime.minusMinutes(afterMinutes);

        // 查询价格库存同步处理报告
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        AmazonProcessReportExample.Criteria criteria = example.createCriteria();
        criteria.andStatusCodeEqualTo("Processing")
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name())
                .andCreationDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andCreationDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(afterDateTime));
        example.setOrderByClause("creation_date");

        List<AmazonProcessReport> reportList = amazonProcessReportService.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(reportList)) {
            return 0;
        }

        log.info("开始处理价格库存同步任务，处理报告数量：{}", reportList.size());

        // 按店铺分组处理
        reportList.stream()
                .collect(Collectors.groupingBy(AmazonProcessReport::getAccountNumber))
                .forEach((accountNumber, reportLists) -> {
                    List<Integer> templateIds = reportLists.stream().map(AmazonProcessReport::getRelationId).distinct().collect(Collectors.toList());
                    sendTemplatePublishStatusMessage(accountNumber, templateIds);
                });
        return reportList.size();
    }

    private void sendTemplatePublishStatusMessage(String accountNumber, List<Integer> templateIds) {
        // 5个模板一组 查询item接口限制20个SKU一个请求
        Lists.partition(templateIds, 5).forEach(ids -> {
            try {
                TemplatePublishStatusMessage message = new TemplatePublishStatusMessage();
                message.setAccountNumber(accountNumber);
                message.setTemplateIds(ids);
                rabbitMqSender.publishSyncVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE_KEY, JSON.toJSON(message));
            } catch (Exception e) {
                XxlJobLogger.log("send message fail {}", accountNumber, e);
            }
        });
    }

    /**
     * 处理全量价格库存同步（超时处理）
     *
     * @param timeoutHours 超时小时数
     * @return int 处理的处理报告数量
     */
    public int handleFullPriceStockSync(int timeoutHours) {
        LocalDateTime timeoutDateTime = LocalDateTime.now().minusHours(timeoutHours);

        // 查询超时的价格库存同步处理报告
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        AmazonProcessReportExample.Criteria criteria = example.createCriteria();
        criteria.andStatusCodeEqualTo("Processing")
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name())
                .andCreationDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(timeoutDateTime));

        List<AmazonProcessReport> reportList = amazonProcessReportService.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(reportList)) {
            return 0;
        }

        log.info("开始处理全量价格库存同步（超时处理），处理报告数量：{}", reportList.size());
        // 使用状态更新器批量处理超时报告
        statusUpdater.updateReportStatus(reportList, false, "价格库存同步超时失败（超过" + timeoutHours + "小时）");
        return reportList.size();
    }
}
