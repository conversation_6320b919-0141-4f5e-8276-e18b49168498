package com.estone.erp.publish.publishAmazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.model.dto.AmazonListingCalcProfitBean;
import com.estone.erp.publish.amazon.model.dto.AmazonListingUpdateQuantityDO;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateBasisRequest;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingCriteria;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingMsgDto;
import com.estone.erp.publish.system.product.ProductInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> amazon_product_listing
 * 2020-12-19 16:28:50
 */
public interface AmazonProductListingService {

    String getTableIndex(String site);

    int countByExample(AmazonProductListingExample example,String site);

    CQueryResult<AmazonProductListing> search(CQuery<AmazonProductListingCriteria> cquery);

    List<AmazonProductListing> selectByExample(AmazonProductListingExample example , String site);

    /**
     * 查询自定义字段listing
     * @param example
     * @param site
     * @return
     */
    List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example, String site);

    /**
     * 查询自定义字段listing (example需要设置table)
     * @param example
     * @return
     */
    List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example);

    AmazonProductListing selectByPrimaryKey(Long id,String site);

    AmazonProductListing selectBySkuAndAccountNumber(String sellerSku, String accountNumber, String site);

    int insert(AmazonProductListing record);

    int updateByPrimaryKeySelective(AmazonProductListing record);

    int updateByExampleSelective(AmazonProductListing record, AmazonProductListingExample example);

    int deleteByPrimaryKey(List<Long> ids,String site);

    /**
     * 根据sellerSkuList 和账号，修改下架状态以及下架时间
     * @param record
     * @param selllerSkuList
     */
    void updateProductsOffSaleBySellerSkuList( AmazonProductListing record, List<String> selllerSkuList);


    /**
     * 批量插入
     * @param amazonProductListingList
     */
    void batchInsertProductListing(List<AmazonProductListing> amazonProductListingList,String site);

    /**
     * 批量插入下架数据到备份表
     * @param amazonProductListingList
     * @param tableIndex
     */
    void batchInsertOnlineFalseProductListing(List<AmazonProductListing> amazonProductListingList, String tableIndex);

    /**
     * 已知站点，批量更新不为 null 的数据
     * @param amazonProductListingList
     * @param site
     */
    void batchUpdateBySellerSkuAndAccountNumber(List<AmazonProductListing> amazonProductListingList,String site);

    /**
     * 按照站点分组批量更新不为null的数据
     * @param amazonProductListingList
     */
    void batchUpdateBySellerSkuAndAccountNumber(List<AmazonProductListing> amazonProductListingList);

    /**
     * 更新产品系统获取到的信息到listing
     * @param record
     * @param example
     * @return
     */
    int updateProductMsgByExampleSelective(AmazonProductListing record, AmazonProductListingExample example);

    /**
     * 批量更新运费
     * @param list
     * @param accountCountry
     */
    void batchUpdateShippingCostBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String accountCountry);

    /**
     * 更新报表数据，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * 批量更新报表数据，非同步报表业务不得调用
     * @param amazonProductListing
     */
    void updateSyncReportBySellerSkuAndAccountNumber(AmazonProductListing amazonProductListing);

    /**
     * 更新库存报表数据，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * 批量更新库存报表数据，非库存同步报表业务不得调用
     * @param list
     */
    void batchUpdateInventoryReportBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site);

    /**
     * 更新同步产品的详细信息，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * @param list
     */
    void batchUpdateListingDetailMsgBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site);

    /**
     *  从平台ListingItem 接口单个同步更新，对部分字段有特殊处理
     * @param amazonProductListing
     */
    void updateListingItemMsgBySellersku(AmazonProductListing amazonProductListing);

    /**
     * 更新详情通过ASIN
     * @param amazonProductListing
     */
    void updateProducDetailByAsin(AmazonProductListing amazonProductListing);

    void init();

    void refreshListingBySku(MultipartFile file);

    /**
     * 查询需要同步详情的账号
     * @param tableIndex
     * @return
     */
    List<AmazonProductListing> selectLackParentAsinAccountList(String tableIndex);


    void syncProductInfo(List<String> list);

    /**
     * 根据sku和账号更新产品信息
     * @param skuList
     * @param accountNumberList
     */
    void syncProductInfo(List<String> skuList, List<String> accountNumberList);

    /**
     * 更新sku缺失的单品状态等信息
     */
    void updateMissingProductInfoListing();

    void batchUpdateRelationTemplateId(List<AmazonProductListing> amazonProductListingList,String site);

    /**
     * 根据账号获取站点
     * @param account
     * @return
     */
    String getSiteByAccount(String account);

    /**
     * 根据账号和sellerSku删除下架数据
     * @param esAmazonProductListing
     * @return
     */
    int deleteByCondition(EsAmazonProductListing esAmazonProductListing);

    int batchDeleteBySellerSkuAndAccountNumber(String accountNumber, List<String> sellerSkuList,String site);


    /**
     * 修改数据库和ES
     * @param amazonProductListing
     */
    void updateDbAndEsBySellerSkuAndAccountNumber(AmazonProductListing amazonProductListing);

    void syncAmazonProductListing2ES(AmazonProductListing amazonProductListing);

    /**
     * 批量更新listing数据
     * @param syncSpProductData
     */
    void batchRefreshAmazonListingsForReport(SyncSpProductData syncSpProductData);

    /**
     * 批量更新listing库存数据
     * @param syncSpProductData
     */
    void batchRefreshAmazonInventoryForReport(SyncSpProductData syncSpProductData);

    /**
     * 同步listing详情
     * @param accountNumber
     * @param esAmazonProductListingList
     */
    void syncAmazonListingDetail(String accountNumber, List<EsAmazonProductListing> esAmazonProductListingList);

    /**
     * 同步listing详情 通过sellersku,批量
     * @param accountNumber
     * @param esAmazonProductListingList
     */
    void syncListingDetailForBatchSellersku(String accountNumber, List<EsAmazonProductListing> esAmazonProductListingList,boolean recordFlag);

    /**
     * 同步listing详情 通过keywords
     * @param accountNumber
     * @param amazonProductListingList
     */
    void syncAmazonListingDetailForKeywords(String accountNumber, List<AmazonProductListing> amazonProductListingList);

    /**
     * 同步 listing运费
     * @param amazonShippingCostModels
     * @param amazonProductListings
     */
    void syncListingShippingCost(List<AmazonShippingCostModel> amazonShippingCostModels, List<AmazonProductListing> amazonProductListings);

    /**
     * 根据账号状态下架产品
     * @param amazonProductListing
     */
    void deleteProductByAccountStatus(AmazonProductListing amazonProductListing);

    /**
     * 同步ListingsItem 信息 通过sellersku
     * @param accountNumber
     * @param esAmazonProductListingList
     */
    void syncListingsItemForSellersku(String accountNumber, List<EsAmazonProductListing> esAmazonProductListingList);

    /**
     * 下架店铺在线产品
     * @param accountNumber
     * @param userName
     */
    void deleteProductByAccount(String accountNumber, String userName);

    /**
     * 批量更新刊登角色
     * @param listingList
     * @param site
     */
    void batchUpdatePublishRoleByTemplate(List<AmazonProductListing> listingList, String site);

    /**
     * 根据id批量更新刊登角色
     * @param listingList
     * @param table
     */
    void batchUpdatePublishRoleById(List<AmazonProductListing> listingList, String table);

    /**
     * 产品系统信息组装到listing
     * @param id
     * @param site
     * @param productInfoVO
     * @return
     */
    AmazonProductListing assembleProductListing(Long id, String site, ProductInfoVO productInfoVO);

    /**
     * 根据条件账号、货号， 清空毛利、毛利率
     * @param amazonProductListing
     */
    void updateGrossProfitNullByAccountNumber(AmazonProductListing amazonProductListing);

    /**
     * 批量更新毛利、毛利率
     * @param list
     * @param accountCountry
     */
    void batchUpdateGrossProfitBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String accountCountry);

    /**
     * 毛利、毛利率计算
     * @param listingCalcProfitBeans
     * @param userName
     * @return
     */
    Map<String,EsAmazonProductListing> amazonProductListingProfit(List<AmazonListingCalcProfitBean> listingCalcProfitBeans, String userName);

    /**
     * 根据articleNumber批量更新主sku
     * @param listingList
     * @param site
     */
    void batchUpdateMainSkuByArticleNumber(List<AmazonProductListing> listingList, String site);

    /**
     * sku重复刊登下架
     * @param templateBOList
     * @return
     */
    ApiResult<String> repeatSkuOffLineWithTemplate(List<AmazonTemplateBO> templateBOList);

    /**
     * sku重复刊登下架
     * @param checkParam
     * @return
     */
    ApiResult<String> offlineRepeatBadAsin(AmazonTemplateBasisRequest checkParam);

    AmazonProductListing selectPriceInventoryListingData(String accountNumber, String sellerSku);

    /**
     * 查询指定日期前新建的产品数据
     * @param example
     * @return
     */
    List<AmazonProductListing> selectNewProductsByOpenDate(AmazonProductListingExample example);

    List<String> selectAccountNumberByExample(AmazonProductListingExample example,String site);

    /**
     * 根据id清除asin关系
     * @param ids
     * @param site
     */
    void clearAsinRelationByIds(List<Long> ids, String site);

    /**
     * 提供给产品系统,根据子Asin查询标题描述信息
     * @param sonAsin
     * @return
     */
    ApiResult<AmazonProductListingMsgDto> apiToProductListingMsgDto(String sonAsin);

    /**
     * 更新Listing的库存
     *
     * @param updateQuantityDO
     * @return
     */
    ApiResult<AmazonProcessReport> updateListingQuantity(AmazonListingUpdateQuantityDO updateQuantityDO);

    /**
     * 获取Asin销量
     *
     * @param sonAsin 子Asin
     * @return key: asin value: 销量数据
     */
    Map<String, AmazonAsinSaleCountDO> getAsinSaleCount(List<String> sonAsin);

    AmazonProductListing getOfflineProductListing(String sellerSku, String accountNumber, String site);
}