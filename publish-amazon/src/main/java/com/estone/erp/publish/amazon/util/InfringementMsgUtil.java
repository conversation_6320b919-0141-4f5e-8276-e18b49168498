package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/27 17:23
 * @description
 */
@Slf4j
public class InfringementMsgUtil {

    public static Map<String, Object> getInfringementMap(String skuMap) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject json = JSON.parseObject(skuMap);
            for (String sku : json.keySet()) {
                try {
                    //[{\"infringementTypeName\":[\"知识产权\"],\"plat\":\"Wish\",\"site\":[\"CN\",\"US\"]}]
                    JSONArray jsonArray = json.getJSONArray(sku);
                    if(jsonArray == null || jsonArray.size() == 0){
                        continue;
                    }

                    StringBuilder msg = new StringBuilder();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject tortJson = jsonArray.getJSONObject(i);
                        msg.append(String.format("禁售平台: %s,", tortJson.getString("plat")));
                        String site = tortJson.getString("site");
                        if(StringUtils.isNotBlank(site) && !"[]".equals(site)){
                            site = site.replaceAll("\\\"", "").replaceAll("\\[", "").replaceAll("\\]", "");
                            msg.append(String.format("禁售站点: %s,", site));
                        }
                        String infringementTypeName = tortJson.getString("infringementTypeName");
                        if(StringUtils.isNotBlank(infringementTypeName) && !"[]".equals(infringementTypeName)){
                            infringementTypeName = infringementTypeName.replaceAll("\\\"", "").replaceAll("\\[", "").replaceAll("\\]", "");
                            msg.append(String.format("侵权类型: %s,", infringementTypeName));
                        }
                        msg.append("。");
                    }
                    map.put(sku, msg.toString().replaceAll(",。","。"));
                }catch (Exception e){
                    map.put(sku, json.get(sku));
                    log.error(String.format("sku %s,转换错误：", sku),e);
                }
            }
        }catch (Exception e){
            map.put("请手动解析查看侵权信息", skuMap);
            log.error("侵权信息转换失败：", e);
        }
        return map;
    }

    //解析map值
    public static Map<String, Object> resolveDisableMap(Map<String, String> skuMap) {
        Map<String, Object> map = new HashMap<>();
        if(skuMap == null || skuMap.isEmpty()){
            return map;
        }
        /*
        "8YY901396": "[{\"plat\":\"Amazon\",\"remark\":\"test\",\"sites\":[{\"site\":\"US\",\"isAuthority\":1},{\"site\":\"UK\",\"isAuthority\":0}]}]"
         */

        try {
            for (String sku : skuMap.keySet()) {
                try {
                    //"[{\"plat\":\"Amazon\",\"remark\":\"test\",\"sites\":[{\"site\":\"US\",\"isAuthority\":1},{\"site\":\"UK\",\"isAuthority\":0}]}]"
                    String val = skuMap.get(sku);
                    if(StringUtils.isBlank(val) || "[]".equals(val)){
                        continue;
                    }

                    JSONArray jsonArray = JSON.parseArray(val);
                    StringBuilder msg = new StringBuilder();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject json = jsonArray.getJSONObject(i);
                        if(json.isEmpty()){
                            continue;
                        }
                        if(msg.length() > 0){
                            msg.append("\n");
                        }

                        msg.append(String.format("平台: %s, ", json.getString("plat")));
                        JSONArray sites = json.getJSONArray("sites");
                        if(sites != null && sites.size() > 0){
                            List<String> authSite = new ArrayList<>();
                            List<String> noneAuthSite = new ArrayList<>();
                            for (int j = 0; j < sites.size(); j++) {
                                JSONObject jsonObject = sites.getJSONObject(j);
                                if(jsonObject == null){
                                    continue;
                                }
                                String site = jsonObject.getString("site");
                                if(BooleanUtils.isTrue(jsonObject.getBoolean("isAuthority"))){
                                    if(StringUtils.isBlank(site)){
                                        authSite.add("全部站点");
                                    }else{
                                        authSite.add(site);
                                    }
                                }else{
                                    if(StringUtils.isBlank(site)){
                                        noneAuthSite.add("全部站点");
                                    }else{
                                        noneAuthSite.add(site);
                                    }
                                }
                            }
                            if(authSite.size() > 0){
                                msg.append(String.format("侵权站点: %s, ", authSite));
                            }
                            if(noneAuthSite.size() > 0){
                                msg.append(String.format("不侵权站点: %s, ", noneAuthSite));
                            }
                        }

                        String remark = json.getString("remark");
                        if(StringUtils.isNotBlank(remark)){
                            msg.append(String.format("备注: %s, ", remark.replaceAll("\\\"", "")));
                        }
                        msg.append("。");
                    }
                    map.put(sku, msg.toString().replaceAll(", 。","。"));
                }catch (Exception e){
                    map.put(sku, skuMap.get(sku));
                    log.error(String.format("sku %s,转换错误：", sku),e);
                }
            }
        }catch (Exception e){
            map.clear();
            map.put("请手动解析查看禁售信息", skuMap);
            log.error("禁售信息转换失败：", e);
        }

        return map;
    }
}
