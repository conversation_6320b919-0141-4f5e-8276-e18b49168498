package com.estone.erp.publish.amazon.enums;

/**
 * <AUTHOR>
 * @date 2020/3/24 15:22
 * @description amazon站点对应物流方式默认关系
 */
public enum AmazonSiteShippingEnum {

    //美国
    US("US","WPPY-SZ"),
    //加拿大   线下EUB-长沙
    CA("CA","OFFLINE_EUB_CS_NEW"),
    //英国 UK=GB   云途
    UK("UK","WPPY-SZ"),
    //法国    云途
    FR("FR","THPHU_P"),
    //德国
    DE("DE","THPHU_P"),
    //意大利
    IT("IT","THPHU_P"),
    //西班牙
    ES("ES","THPHU_P"),
    //日本    线下EUB-长沙
    JP("JP","OFFLINE_EUB_CS_NEW"),
    //中东    阿联酋
    AE("AE","JCAER"),
    //印度    华南快速小包挂号
    IN("IN","CNDWR"),
    //荷兰
    NL("NL","THPHR_P"),
    ;

    AmazonSiteShippingEnum(String site, String shipping){
        this.site = site;
        this.shipping = shipping;
    }
    private String site;

    private String shipping;

    public String getSite() {
        return site;
    }

    public String getShipping() {
        return shipping;
    }

    public static String getShippingBySite(String site){
        for (AmazonSiteShippingEnum anEnum : AmazonSiteShippingEnum.values()) {
            if(anEnum.getSite().equalsIgnoreCase(site)){
                return anEnum.getShipping();
            }
        }
        return null;
    }


    /**
     * 根据价格得到物流方式
     * @param costPrice
     * @return
     */
    public static String getShippingByPrice(Double costPrice) {
        /*
        1、试算物流方式为销售成本价大于等于30，采用 4XP-GHSJ    4XP挂号算价专用
        2、试算物流方式为销售成本价小于30，采用 4XP-4XPPY     4XP平邮算价专用
        */
        if(costPrice == null || costPrice < 30){
            return "4XP-4XPPY";
        }
        return "4XP-GHSJ";
    }
}
