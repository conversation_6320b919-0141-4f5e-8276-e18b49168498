package com.estone.erp.publish.amazon.call.process.submit;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecord;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 产品列表 重绑Asin关联关系 Feed XML 策略
 */
@Component
public class ProductRestoreRelationFeedXmlStrategy extends AbstractSubmitFeedXmlStrategy<AmazonListingAsinBindRecord> {

    @Override
    public String transferProduct2Xml(PublishData<AmazonListingAsinBindRecord> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        XmlBuilder xmlBuilder = buildAmazonEnvelope("Product", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonListingAsinBindRecord> bindRecords = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonListingAsinBindRecord bindRecord : bindRecords) {
            String parentSku = bindRecord.getSellerSku();
            String productType = bindRecord.getProductType();
            String variationTheme = bindRecord.getVariationTheme();
            publishData.addSku2SellerSku(parentSku, parentSku);
            msgId2SkuMap.put(messageId, parentSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));

            message.create("OperationType", operationType.name());
            // Product
            Element product = message.create("Product");
            product.create("SKU", parentSku);
            product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
            // DescriptionData
            Element descriptionData = product.create("DescriptionData");
            descriptionData.create("Title", bindRecord.getItemName());
            descriptionData.create("Brand", bindRecord.getBrandName());
            descriptionData.create("Manufacturer", bindRecord.getManufacturer());
            descriptionData.create("MfrPartNumber", bindRecord.getModelNumber());
            if (StringUtils.isNotEmpty(bindRecord.getBrowseNodeId())) {
                List<String> nodeIds = JSON.parseArray(bindRecord.getBrowseNodeId(), String.class);
                descriptionData.create("RecommendedBrowseNode", nodeIds.get(0));
            }
            // ProductData
            Element productData = product.create("ProductData");
            ElementWrapper productDataWrapper = ProductWrapper.getProductData(productType);
            if (productDataWrapper != null) {
                addVariationTheme(productData, productType, variationTheme, null);
                addProductDataElement(productDataWrapper, productData, "Parentage", "parent");
                addProductDataElement(productDataWrapper, productData, "CountryOfOrigin", "CN");
            }
            String[] childSellerSkusArray = bindRecord.getChildSellerSkus().split(",");
            for (String sellerSku : childSellerSkusArray) {
                messageId++;
                Element subMessage = root.create("Message");
                subMessage.create("MessageID", String.valueOf(messageId));
                subMessage.create("OperationType", "PartialUpdate");
                Element subProduct = subMessage.create("Product");
                subProduct.create("SKU", sellerSku);
                msgId2SkuMap.put(messageId, sellerSku);
                subProduct.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                if (productDataWrapper != null) {
                    Element subProductData = subProduct.create("ProductData");
                    Map<String, String> themeValueMap = getSellerSkuVariationThemeValueMap(sellerSku, bindRecord.getExtraData());
                    addVariationTheme(subProductData, productType, variationTheme, themeValueMap);
                    addProductDataElement(productDataWrapper, subProductData, "Parentage", "child");
                    addProductDataElement(productDataWrapper, subProductData, "CountryOfOrigin", "CN");
                }
            }
        }
        return xmlBuilder.builder();
    }

    private void addProductDataElement(ElementWrapper productDataWrapper, Element rootElement, String itemName, String itemValue) {
        ElementWrapper itemWrapper = productDataWrapper.getItemWrapper(itemName);
        if (itemWrapper == null || StringUtils.isEmpty(itemWrapper.getRoute())) {
            if ("Parentage".equals(itemName)) {
                throw new IllegalArgumentException("The product data provided was insufficient for creating a variation (parent/child) relationship for SKU .");
            }
        }
        String[] routesPath = itemWrapper.getRoute().split(AmazonConstant.ROUTE_JOIN);
        Element currentElement = rootElement;
        for (String name : routesPath) {
            List<Element> elements = currentElement.getElements();
            if (elements == null) {
                elements = new ArrayList<>();
            }
            Element elementData = elements.stream().filter(element -> element.getName().equals(name)).findFirst().orElse(null);
            if (elementData == null) {
                if (itemName.equals(name)) {
                    currentElement.create(name, itemValue);
                } else {
                    currentElement = currentElement.create(name);
                }
                continue;
            }
            currentElement = elementData;
        }
    }

    private void addVariationTheme(Element productData, String productType, String variationThemeName, Map<String, String> themeValueMap) {
        ElementWrapper productDataWrapper = ProductWrapper.getProductData(productType);
        if (productDataWrapper == null) {
            return;
        }
        ElementWrapper variationTheme = ProductWrapper.getVariationTheme(productDataWrapper, productType);
        if (variationTheme == null || StringUtils.isEmpty(variationTheme.getRoute())) {
            return;
        }
        String[] routesPath = variationTheme.getRoute().split(AmazonConstant.ROUTE_JOIN);
        Element currentElement = productData;
        for (int i = 0; i < routesPath.length; i++) {
            String themeName = routesPath[i];
            if (i == routesPath.length - 1) {
                // 最后一个主题节点
                currentElement.create(themeName, variationThemeName);
                if (MapUtils.isNotEmpty(themeValueMap)) {
                    themeValueMap.forEach(currentElement::create);
                }
                break;
            }
            currentElement = currentElement.create(themeName);
        }
    }


    @Override
    public String transferProductRelationship2Xml(PublishData<AmazonListingAsinBindRecord> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }
        XmlBuilder xmlBuilder = buildAmazonEnvelope("Relationship", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        OperationType operationType = publishData.getOperationType();
        AmazonListingAsinBindRecord bindRecord = publishData.getUnitDatas().get(0);
        String childSellerSkus = bindRecord.getChildSellerSkus();
        String[] sellerSkuArray = childSellerSkus.split(",");
        msgId2SkuMap.put(1, bindRecord.getSellerSku());
        Element message = root.create("Message");
        message.create("MessageID", "1");
        message.create("OperationType", operationType.name());
        Element relationship = message.create("Relationship");
        relationship.create("ParentSKU", bindRecord.getSellerSku());
        String productType = bindRecord.getProductType();
        String variationTheme = bindRecord.getVariationTheme();
        for (String sellerSku : sellerSkuArray) {
            Element relation = relationship.create("Relation");
            relation.create("SKU", sellerSku);
            relation.create("Type", "Variation");
            Element subProductData = relation.create("ProductData");
            ElementWrapper productDataWrapper = ProductWrapper.getProductData(productType);
            if (productDataWrapper != null) {
                Map<String, String> themeValueMap = getSellerSkuVariationThemeValueMap(sellerSku, bindRecord.getExtraData());
                addVariationTheme(subProductData, productType, variationTheme, themeValueMap);
            }

        }
        return xmlBuilder.builder();
    }

    private Map<String, String> getSellerSkuVariationThemeValueMap(String sellerSku, String extraData) {
        Map<String, String> sellerSkuVariationThemeValueMap = Maps.newHashMap();
        if (StringUtils.isEmpty(extraData)) {
            return sellerSkuVariationThemeValueMap;
        }
        JSONObject jsonObject = JSON.parseObject(extraData);
        if (jsonObject == null) {
            return sellerSkuVariationThemeValueMap;
        }
        JSONArray jsonArray = jsonObject.getJSONArray("variationThemes");
        if (jsonArray == null) {
            return sellerSkuVariationThemeValueMap;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject themeObject = jsonArray.getJSONObject(i);
            if (themeObject == null) {
                continue;
            }
            String sku = themeObject.getString("sellerSku");
            if (StringUtils.isEmpty(sku) || !sku.equals(sellerSku)) {
                continue;
            }

            String values = themeObject.getString("values");
            Map<String, String> valuesMap = JSON.parseObject(values, new TypeReference<>(){});
            valuesMap.forEach((k,v)->{
                sellerSkuVariationThemeValueMap.put(k, v.toString());
            });
        }
        return sellerSkuVariationThemeValueMap;
    }

}
