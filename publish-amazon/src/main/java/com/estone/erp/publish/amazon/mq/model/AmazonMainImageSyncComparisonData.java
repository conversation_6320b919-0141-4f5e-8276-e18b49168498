package com.estone.erp.publish.amazon.mq.model;

import lombok.Data;

import java.util.List;

@Data
public class AmazonMainImageSyncComparisonData {

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 是否全量
     */
    private boolean isFull;

    /**
     * 开始时间 开始时间不包含
     */
    private String openDate;

    /**
     * 结束时间 结束时间包含
     */
    private String endDate;

    /**
     * 条件查询使用，不管是否全量
     */
    private List<String> skuList;

    /**
     * 站点
     */
    private String accountCountry;
}
