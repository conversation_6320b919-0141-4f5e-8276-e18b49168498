package com.estone.erp.publish.amazon.call.process.product.newreport;

import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步产品数据类
 */
@Data
public class SyncSpProductData {

    public SyncSpProductData(AmazonAccount account) {
        Assert.notNull(account);
        this.account = account;
    }

    /**
     * 账号
     */
    private AmazonAccount account;

    /**
     * 站点
     */
    private String site;

    /**
     *sellerSku 与报表行数据对应
     */
    private Map<String, String[]> sellerSku2SplitsMap = new HashMap<>();

    /**
     * sellerSku 与 AmazonProductListing 的map
     */
    private Map<String, AmazonProductListing> sellerSku2ProductListingMap = new HashMap<>();

    private List<AmazonProductListing> amazonProductListingList = new ArrayList<>();

    /**
     *  表头map key:表头列名称 value:对应的下标序号
     */
    private Map<String, Integer> headerMap  = new HashMap<>();

    /**
     * @Description: 添加asin2VariantMap元素
     */
    public void addSellerSku2AmazonProductListingMap(String sellerSku, AmazonProductListing amazonProductListing) {
        sellerSku2ProductListingMap.put(sellerSku, amazonProductListing);
    }

    public void clear() {
        this.sellerSku2SplitsMap.clear();
        this.sellerSku2ProductListingMap.clear();
    }
}