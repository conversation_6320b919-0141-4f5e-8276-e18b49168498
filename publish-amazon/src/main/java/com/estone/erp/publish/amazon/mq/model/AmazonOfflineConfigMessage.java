package com.estone.erp.publish.amazon.mq.model;

import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-01-13 9:21
 */
@Getter
@Setter
public class AmazonOfflineConfigMessage {
    /**
     * 配置ID
     */
    private Integer configId;
    /**
     * 下架类型
     */
    private String offlineType;
    /**
     * 店铺
     */
    private String accountNumber;
    /**
     * skus
     */
    private String skus;
    /**
     * 调度时间
     */
    private LocalDateTime scheduleTime;
    /**
     * 确认时间
     */
    private LocalDateTime confirmedTime;

    public AmazonOfflineConfigMessage() {
    }

    public AmazonOfflineConfigMessage(Integer configId, String offlineType, String accountNumber, LocalDateTime scheduleTime, LocalDateTime confirmedTime) {
        this.configId = configId;
        this.offlineType = offlineType;
        this.accountNumber = accountNumber;
        this.scheduleTime = scheduleTime;
        this.confirmedTime = confirmedTime;
    }

    @Override
    public String toString() {
        return "AmazonOfflineConfigMessage{" +
                "configId=" + configId +
                ", offlineType='" + offlineType + '\'' +
                ", accountNumber='" + accountNumber + '\'' +
                ", skus=" + skus +
                ", scheduleTime=" + scheduleTime +
                ", confirmedTime=" + confirmedTime +
                '}';
    }
}
