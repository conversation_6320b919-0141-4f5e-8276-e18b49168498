package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.componet.AmazonAutoPublishProductTypeHelper;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.service.*;
import com.estone.erp.publish.amazon.service.impl.AmazonRepublishReportServiceImpl;
import com.rabbitmq.client.Channel;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * amazon 修改分类类型，重新刊登队列
 */
@Slf4j
@Component
public class UpdateProductTypeRepeatPublishMqListener {

    //处理报告错误信息
    private static final String RESULT_MSG_PREFIX = "Having the most applicable product type for a product enhances both the seller and customer experiences; therefore, we have amended the product_type of your submission from ";
    private static final String RESULT_MSG_STUFFIX =". To remove the warning attached to your product, simply accept our suggested Product Type and resubmit the SKU. Alternatively, if the original Product Type you selected is correct, you may change the SKU attributes, such as title, product description, and bullet points, so that your product more clearly matches that original Product Type. If you feel both the Product Type you had selected and the original SKU attributes are correct, please contact seller support.. To remove the warning attached to your product, simply accept our suggested product type and resubmit the SKU. Alternatively, if the original product type you selected is correct, you can change the SKU attributes such as title, product description and bullet points, so that your product matches the original product type more closely. If you feel both the product type you had selected and the original SKU attributes are correct, please contact Seller Support/Selling Partner Support.";

    //处理报告错误信息
    private static final String JP_RESULT_MSG_PREFIX = "商品に最も当てはまる商品タイプを設定すると、出品者と購入者の両方のエクスペリエンスが向上します。そのため、出品者様の送信内容product_typeを";
    private static final String JP_RESULT_MSG_STUFFIX =". To remove the warning attachedからyour product, simply accept our suggested Product Type and resubmit the SKU. Alternatively, if the original Product Type you selected is correct, you may change the SKU attributes, such as title, product description, and bullet points, so that your product more clearly matches that original Product Type. If you feel both the Product Type you had selected and the original SKU attributes are correct, please contact seller support.に変更しました。商品に添付された警告を削除するには、推奨商品タイプを受け入れてSKUを再送信してください。また、選択した元の商品タイプが正しければ、商品が元の商品タイプとより明確に一致するように、タイトル、商品説明、商品仕様などのSKU属性を変更することもできます。選択した商品タイプと元のSKU項目の両方が正しいと思われる場合は、Amazonテクニカルサポートにお問い合わせください。";

    @Resource
    private AmazonTemplateService amazonTemplateService;

    @Resource
    private AmazonAutoPublishProductTypeHelper amazonAutoPublishProductTypeHelper;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonRepublishReportServiceImpl amazonRepublishReportService;
    @Resource
    private ProductTypeTemplateService productTypeTemplateService;
    @Resource
    private PlatfromProductTypeLogService platfromProductTypeLogService;

    private AmazonCallService amazonCallService = SpringUtils.getBean(AmazonCallService.class);

   @RabbitListener(queues = PublishQueues.PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE, containerFactory = "batchConsumeFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Integer templateId = JSON.parseObject(body, Integer.class);
            if (templateId == null) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 处理刊登队列
            Boolean isSuccess = updateProductTypePublishTemplate(templateId);
            if (isSuccess) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                // 重新投递
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            }
        } catch (Exception e) {
            log.error("consumer fail:body {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            throw new RuntimeException(e);
        }
    }


    private Boolean updateProductTypePublishTemplate(Integer templateId) {
        try {
            AmazonTemplateExample example = new AmazonTemplateExample();
            example.createCriteria()
                    .andIdEqualTo(templateId)
                    .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode())
                    .andReportSolutionTypeEqualTo(AmazonReportSolutionTypeEnum.PRODUCT_TYPE_PROBLEM.getName())
                    ;
            List<AmazonTemplateBO> templates = amazonTemplateService.selectAmazonTemplateBOsByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(templates)) {
                return true;
            }

            AmazonTemplateBO amazonTemplateBO = templates.get(0);
            String country = amazonTemplateBO.getCountry();
            // 查询处理报告，匹配分类类型，匹处理属性
            AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
            amazonProcessReportExample.createCriteria()
                    .andRelationIdEqualTo(amazonTemplateBO.getId())
                    .andDataValueEqualTo(amazonTemplateBO.getSellerSKU())
                    .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue());
            amazonProcessReportExample.setOrderByClause("id desc");
            amazonProcessReportExample.setLimit(1);
            List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectByExampleWithBLOBs(amazonProcessReportExample);
            if (CollectionUtils.isEmpty(amazonProcessReportList)){
                // 记录重试日志
               log.warn(String.format("%s 模板未查到父产品产品上传的处理报告",amazonTemplateBO.getId()));
               return true;
            }

            List<String> productTypeList = ProductWrapper.productTypeKeys;
            String msg = amazonProcessReportList.get(0).getResultMsg();
            if (StringUtils.isBlank(msg)){
                // 记录重试但未匹配到分类类型的日志
                //amazonRepublishReportService.recordReTryReport(templates);
                log.warn(String.format("%s 模板未查到父产品产品上传的处理报告错误信息 %s",templateId,amazonProcessReportList.get(0).getId()));
                return true;
            }
            String adviceProduxtType = msg.replace(RESULT_MSG_PREFIX,"").replace(RESULT_MSG_STUFFIX,"");
            if (country.equalsIgnoreCase("JP")){
                adviceProduxtType = msg.replace(JP_RESULT_MSG_PREFIX,"").replace(JP_RESULT_MSG_STUFFIX,"");
            }
            adviceProduxtType = StringUtils.substringAfterLast(adviceProduxtType,"to ").trim();
            String adviceProduxtType1 =  adviceProduxtType.replaceAll("_","").trim().toLowerCase();
            String updateProductType = null;
            for (String productType : productTypeList){
                if (productType.toLowerCase().contains(adviceProduxtType1)){
                    updateProductType = productType;
                    break;
                }
            }
            //截取 _ 后匹配
            List<String> adviceProduxtTypes = new ArrayList<>(Arrays.asList(StringUtils.split(adviceProduxtType,"_")));
            if (StringUtils.isBlank(updateProductType) && CollectionUtils.isNotEmpty(adviceProduxtTypes)){
                for (int i = adviceProduxtTypes.size() - 1; i >= 0; i--) {
                    for (String productType : productTypeList){
                        if (productType.toLowerCase().contains(adviceProduxtTypes.get(i).toLowerCase())){
                            updateProductType = productType;
                            break;
                        }
                    }
                    if (StringUtils.isNotBlank(updateProductType)){
                        break;
                    }
                }
            }
            handlePublishTypeRecord(adviceProduxtType,updateProductType,amazonTemplateBO);
            if (StringUtils.isBlank(updateProductType)) {
                // 记录重试但未匹配到分类类型的日志
                amazonRepublishReportService.recordReTryReport(templates);
                return true;
            }
            // 处理属性
            amazonTemplateBO.setProductType(updateProductType);
            amazonTemplateBO.setParentProductType(StringUtils.substringBefore(updateProductType, "."));

            String site = amazonTemplateBO.getCountry();
            ProductTypeTemplateExample typeTemplateExample = new ProductTypeTemplateExample();
            typeTemplateExample.createCriteria()
                    .andEnableEqualTo(true)
                    .andSiteEqualTo(site)
                    .andProductTypeEqualTo(updateProductType);
            List<ProductTypeTemplate> productTypeTemplates = productTypeTemplateService.selectByExample(typeTemplateExample);
            if (CollectionUtils.isNotEmpty(productTypeTemplates)) {
                ProductTypeTemplate productTypeTemplate = productTypeTemplates.get(0);
                amazonTemplateBO.setCategoryTemplateName(productTypeTemplate.getTemplateName());
                amazonTemplateBO.setProduceAttributeMode("[1,2]");
                // 查必填属性
            }else {
                amazonTemplateBO.setCategoryTemplateName(null);
                amazonTemplateBO.setProduceAttributeMode("[2]");

            }
            // 分类属性优化
            String oldExtraData = amazonTemplateBO.getExtraData();
            amazonAutoPublishProductTypeHelper.generateProductType(amazonTemplateBO);
            //设置分类属性 固定值
            amazonAutoPublishProductTypeHelper.setProductDefaultValue(amazonTemplateBO,oldExtraData);
            //添加默认属性
            amazonAutoPublishProductTypeHelper.addDefaultAttr(amazonTemplateBO);
            // 记录重试日志
            templates.forEach(o ->
                    o.setReportSolutionType(AmazonReportSolutionTypeEnum.PRODUCT_TYPE_PROBLEM.getName() + "已重试"));
            amazonRepublishReportService.recordReTryReport(templates);
            // 刊登
            amazonTemplateService.batchUpdateAmaozntemplatePublishStatus(templates,SpFeedType.POST_PRODUCT_DATA.getValue());
            amazonCallService.publishTemplates(templates);

            return true;
        } catch (Exception e) {
            log.error("更换分类类型重刊登失败：{}", templateId, e);
        }
        return false;
    }

    /**
     * 记录推荐类型和分类类型日志
     * @param adviceProduxtType
     * @param updateProductType
     * @param amazonTemplateBO
     */
    private void handlePublishTypeRecord(String adviceProduxtType,String updateProductType,AmazonTemplateBO amazonTemplateBO){
       try {
           PlatfromProductTypeLog record = new PlatfromProductTypeLog();
           record.setPlatformPublishType(adviceProduxtType);
           record.setPublishType(updateProductType);
           record.setTemplateId(amazonTemplateBO.getId());
           record.setSite(amazonTemplateBO.getCountry());
           record.setMainSku(amazonTemplateBO.getParentSku());
           record.setCreateTime(new Timestamp(System.currentTimeMillis()));
           record.setOldPublishType(amazonTemplateBO.getProductType());
           record.setSaleVariant(amazonTemplateBO.getSaleVariant());
           platfromProductTypeLogService.insert(record);
       }catch (Exception e){
           log.error(e.getMessage());
       }
    }

}
