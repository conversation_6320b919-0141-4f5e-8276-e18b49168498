package com.estone.erp.publish.amazon.util.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 销量类型
 * <AUTHOR>
 * @date 2022/5/5 11:16
 */
public enum SalesTypeEnum {
    ORDER_LAST_30D_COUNT(1, "30天销量", "orderLast30dCount"),
    ORDER_LAST_90D_COUNT(2, "90天销量", "orderLast90dCount");

    private int code;
    private String name;
    private String enName;

    SalesTypeEnum(int code, String name, String enName) {
        this.code = code;
        this.name = name;
        this.enName = enName;
    }

    public static String getNameByCode(int code) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByEnName(String enName) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getEnName().equals(enName)) {
                return value.getCode();
            }
        }
        return null;
    }


    public static List<Integer> getCodeListByEnNames(List<String> enNameList) {
        SalesTypeEnum[] values = SalesTypeEnum.values();

        List<Integer> statusList = new ArrayList<>();

        for (String enName : enNameList) {
            for (SalesTypeEnum value : values) {
                if (value.getEnName().equals(enName)) {
                    statusList.add(value.getCode());
                }
            }
        }
        return statusList;
    }

    public static String getEnNameByCode(int code) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getEnName();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public String getEnName() {
        return enName;
    }
}
