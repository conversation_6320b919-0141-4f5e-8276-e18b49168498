package com.estone.erp.publish.amazon.call;

import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.util.MapUtils;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import com.estone.erp.publish.common.util.CommonUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class AmazonConstant {
    public static final List<AmazonMarketplace> MARKETPLACES = new ArrayList<AmazonMarketplace>() {
        private static final long serialVersionUID = -7383871928707039732L;
        {
            add(new AmazonMarketplace("US", "ATVPDKIKX0DER", "https://mws.amazonservices.com", "美国", "USD"));
            add(new AmazonMarketplace("CA", "A2EUQ1WTGCTBG2", "https://mws.amazonservices.ca", "加拿大", "CAD"));
            add(new AmazonMarketplace("BR", "A2Q3Y263D00KWC", "https://mws.amazonservices.com", "巴西", "BRL"));
            add(new AmazonMarketplace("UK", "A1F83G8C2ARO7P", "https://mws-eu.amazonservices.com", "英国", "GBP"));
            add(new AmazonMarketplace("FR", "A13V1IB3VIYZZH", "https://mws-eu.amazonservices.com", "法国", "EUR"));
            add(new AmazonMarketplace("DE", "A1PA6795UKMFR9", "https://mws-eu.amazonservices.com", "德国", "EUR"));
            add(new AmazonMarketplace("IT", "APJ6JRA9NG5V4", "https://mws-eu.amazonservices.com", "意大利", "EUR"));
            add(new AmazonMarketplace("ES", "A1RKKUPIHCS9HS", "https://mws-eu.amazonservices.com", "西班牙", "EUR"));
            add(new AmazonMarketplace("JP", "A1VC38T7YXB528", "https://mws.amazonservices.jp", "日本", "JPY"));
            add(new AmazonMarketplace("IN", "A21TJRUUN4KGV", "https://mws.amazonservices.in", "印度", "INR"));
            add(new AmazonMarketplace("AU", "A39IBJ37TRP1C6", "https://mws.amazonservices.com.au", "澳大利亚", "AUD"));
            add(new AmazonMarketplace("CN", "AAHKV2X7AFYLW", "https://mws.amazonservices.com.cn", "中国", "CNY"));
            add(new AmazonMarketplace("MX", "A1AM78C64UM0Y8", "https://mws.amazonservices.com.mx", "墨西哥", "USD"));
            add(new AmazonMarketplace("AE", "A2VIGQ35RCS4UG", "https://mws.amazonservices.com.ae", "中东站", "AED"));
            add(new AmazonMarketplace("SG", "A19VAU5U5O7RUS", "https://mws-fe.amazonservices.com", "新加坡", "SGD"));
            add(new AmazonMarketplace("NL", "A1805IZSGTT6HS", "https://mws-eu.amazonservices.com", "荷兰", "EUR"));
            add(new AmazonMarketplace("SA", "A17E79C6D8DWNP", "https://mws-eu.amazonservices.com", "沙特阿拉伯", "SAR"));
            add(new AmazonMarketplace("TR", "A33AVAJ2PDY3EV", "https://mws-eu.amazonservices.com", "土耳其", "TRY"));
            add(new AmazonMarketplace("EG", "ARBP9OOSHTCHU", "https://mws-eu.amazonservices.com", "埃及", "EGP"));
            add(new AmazonMarketplace("SE", "A2NODRKZP88ZB9", "https://mws-eu.amazonservices.com", "瑞典", "SEK"));
        }
    };

    // 分类缓存map
    public static final Map<String, Map<String, BrowseTreeReport.Node>> ACCOUNT_CATEGORY_MAP = new HashMap<>();

    // 分类缓存初始化成功与否map
    public static final Map<String, Boolean> ACCOUNT_CATEGORY_INIT_FINISH_MAP = new HashMap<>();
    public static final AtomicBoolean ACCOUNT_CATEGORY_INIT_FINISH = new AtomicBoolean(false);


    // 产品状态
    public static final List<String> CONDITIONS = Arrays.asList("New", "UsedLikeNew", "UsedVeryGood", "UsedGood",
            "UsedAcceptable", "CollectibleLikeNew", "CollectibleVeryGood", "CollectibleGood", "CollectibleAcceptable",
            "Refurbished", "Club");

    // 默认税码
    public static final String DEFAULT_PRODUCT_TAX_CODE = "A_GEN_NOTAX";

    //
    public static final String DEFAULT_CONDITION_TYPE = "New";


    /**
     * 分类类型 需要特殊处理 is_heat_sensitive，默认false
     */
    public static final Set<String> PRODUCT_TYPE_ATTR_LIST =new HashSet<>(Arrays.asList("Health.HealthMisc", "Beauty.BeautyMisc","Baby.BabyProducts"));

    public static final List<String> TEMPLATE_IMAGE_SUFFIXS = Arrays.asList("jpg", "jpeg", "gif", "png");

    // 刊登模板流程
    public static final String PUBLISH_STEP_TEMPLATE = "template";

    public static final String PUBLISH_STEP_PRICE = "price";

    public static final String PUBLISH_STEP_INVENTORY = "inventory";

    public static final String PUBLISH_STEP_IMAGE = "image";

    public static final String PUBLISH_STEP_FOLLOW_SELL = "followSell";

    public static final String PUBLISH_STEP_FOLLOW_SELL_BATCH = "followSellBatch";

    public static final String PUBLISH_STEP_DELETE = "delete";

    // amazon xsd目录和URL
    public static final String AMAZON_API_XSD_PATH = "amazonApiXsd_4_1";

    public static final String AMAZON_API_XSD_URL = "https://images-na.ssl-images-amazon.com/images/G/01/rainier/help/xsd/release_4_1/";

    // ProcessReport DataType
    public static final String PROCESS_REPORT_TYPE_SKU = "SKU";
    public static final String PROCESS_REPORT_TYPE_ASIN = "ASIN";

    // 在线列表listing侵权校验调用限流 Publish:Amazon:LISTING_C_B_W_RATE
    public static final String LISTING_CHECK_BRAND_WORD_LIMITER = RedisConstant.AMAZON_PREFIX_SYSTEM + "LISTING_C_B_W_RATE";
    // 模板侵权校验调用限流 Publish:Amazon:TEMPLATE_C_B_W_RATE
    public static final String TEMPLATE_CHECK_BRAND_WORD_LIMITER = RedisConstant.AMAZON_PREFIX_SYSTEM + "TEMPLATE_C_B_W_RATE";
    // AMAZON API调用限流 Publish:Amazon:API_RATE_LIMITER
    public static final String UPDATE_GPSR_API_LIMITER = "UPDATE_GPSR_API_LIMITER_";

    // sku后缀连接符
    public static final String SKU_SUFFIX_JOIN = "_";

    // xsd节点路由连接符
    public static final String ROUTE_JOIN = "--";

    public static final String DEFAULT_COUNTRY_CURRENCY = "DEFAULT";

    public static final String LISTING_CHECK_BRAND_WORD_SPLIT = " ㊍㊍ ";

    /**
     * 跟卖sku前缀
     */
    public static final String FOLLOW_SELL_SKU_PREFIX = "FS#";

    public static final List<String> identifiersIncludedData = Arrays.asList("attributes", "classifications", "dimensions",
            "identifiers", "productTypes", "relationships", "salesRanks", "summaries");



    public static Map<String, AmazonMarketplace> marketplaceIdMap = new HashMap<>(MARKETPLACES.size());
    static {
        for (AmazonMarketplace marketplace : MARKETPLACES) {
            marketplaceIdMap.put(marketplace.getMarketplaceId(), marketplace);
        }
    }

    public static List<String> getMarketplaceIdsByMarketplaces(String... marketplaces) {
        if (ArrayUtils.isEmpty(marketplaces)) {
            return CommonUtils.emptyList();
        }

        List<String> marketplaceIds = new ArrayList<>(marketplaces.length);
        for (AmazonMarketplace marketplace : MARKETPLACES) {
            if (ArrayUtils.contains(marketplaces, marketplace.getMarketplace())) {
                marketplaceIds.add(marketplace.getMarketplaceId());
            }
        }

        return marketplaceIds;
    }

    public static Map<String, BrowseTreeReport.Node> getCategoryMapByAccount(String account) {
        return MapUtils.putIfAbsent(ACCOUNT_CATEGORY_MAP, account, () -> {
            return new HashMap<>();
        });
    }

}
