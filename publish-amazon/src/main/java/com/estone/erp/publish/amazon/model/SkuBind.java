package com.estone.erp.publish.amazon.model;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_sku_bind
 *
 * @mbg.generated do_not_delete_during_merge Mon Jul 22 16:30:26 CST 2019
 */
public class SkuBind {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.bind_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private Integer bindId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private String sku;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.bind_sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private String bindSku;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.platform
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private String platform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.seller_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private String sellerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.create_date
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private Date createDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sku_bind.extend
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    private String extend;

    /**
     * 系统sku
     */
    private String systemSku;

    /**
     * 主sku
     */
    private String mainSku;

    /**
     * SKU数据来源(1.产品系统;2.数据分析系统;3.冠通;4.探雅)
     */
    private Integer skuDataSource;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.bind_id
     *
     * @return the value of t_sku_bind.bind_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public Integer getBindId() {
        return bindId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.bind_id
     *
     * @param bindId the value for t_sku_bind.bind_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setBindId(Integer bindId) {
        this.bindId = bindId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.sku
     *
     * @return the value of t_sku_bind.sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public String getSku() {
        return sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.sku
     *
     * @param sku the value for t_sku_bind.sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.bind_sku
     *
     * @return the value of t_sku_bind.bind_sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public String getBindSku() {
        return bindSku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.bind_sku
     *
     * @param bindSku the value for t_sku_bind.bind_sku
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setBindSku(String bindSku) {
        this.bindSku = bindSku == null ? null : bindSku.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.platform
     *
     * @return the value of t_sku_bind.platform
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public String getPlatform() {
        return platform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.platform
     *
     * @param platform the value for t_sku_bind.platform
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.seller_id
     *
     * @return the value of t_sku_bind.seller_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.seller_id
     *
     * @param sellerId the value for t_sku_bind.seller_id
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId == null ? null : sellerId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.create_date
     *
     * @return the value of t_sku_bind.create_date
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.create_date
     *
     * @param createDate the value for t_sku_bind.create_date
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sku_bind.extend
     *
     * @return the value of t_sku_bind.extend
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public String getExtend() {
        return extend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sku_bind.extend
     *
     * @param extend the value for t_sku_bind.extend
     *
     * @mbg.generated Mon Jul 22 16:30:26 CST 2019
     */
    public void setExtend(String extend) {
        this.extend = extend == null ? null : extend.trim();
    }

    public String getSystemSku() {
        return systemSku;
    }

    public void setSystemSku(String systemSku) {
        this.systemSku = systemSku;
    }

    public Integer getSkuDataSource() {
        return skuDataSource;
    }

    public void setSkuDataSource(Integer skuDataSource) {
        this.skuDataSource = skuDataSource;
    }

    public String getMainSku() {
        return mainSku;
    }

    public void setMainSku(String mainSku) {
        this.mainSku = mainSku;
    }
}