package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.call.TempAmazonProductRestoreRelationPublishCall;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTempExample;
import com.estone.erp.publish.amazon.service.AmazonListingParentRelationshipTempService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description 绑定父子关系
 * @see SkuStatusEnum
 **/
@Slf4j
@Component
public class PublishAmazonListingRelationshipTempJobHandler extends AbstractJobHandler {



    @Data
    static class InnerParam{
        //店鋪账号
        private List<String>  accountNumberList;

        /**
         * 执行提交类型
         */
        private String feedtype;
    }

    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private AmazonListingParentRelationshipTempService amazonListingParentRelationshipTempService;

    public PublishAmazonListingRelationshipTempJobHandler() {
        super("PublishAmazonListingRelationshipTempJobHandler");
    }

    @Override
    @XxlJob("PublishAmazonListingRelationshipTempJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        long startTime = System.currentTimeMillis();
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
                XxlJobLogger.log("本次执行的参数信息 {}, uuid->{}", JSON.toJSONString(innerParam));
            }catch (Exception e){
                ReturnT fail = new ReturnT(500, "参数错误。例子：{\"accountNumberList\": [\"xxx\",\"aaa\"]}");
                fail.setContent(e.getMessage());
                return fail;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        List<String> accountNumberList = innerParam.getAccountNumberList();
        if (CollectionUtils.isEmpty(accountNumberList)){
            accountNumberList =amazonListingParentRelationshipTempService.getAccountNumberList();
        }
        log.warn("============超额刊登，系统自动下架父体包含的店铺数量=========" + accountNumberList.size());
        updateRelationship(accountNumberList,innerParam.getFeedtype());
        long endTime = System.currentTimeMillis();
        log.info("AmazonUnqualifiedSku2ZeroJobHandler cost time is {}", ((endTime-startTime)/1000L));
        return ReturnT.SUCCESS;
    }

    private void updateRelationship(List<String> accountNumberList,String feedtype) {
        for (String accountNumber : accountNumberList) {
           // AmazonExecutors.executeUpdateListingRelationShip( () -> {
                try {
                    log.info("账号{} 开始执行超额刊登，系统自动下架父体", accountNumber);
                    AmazonAccount account = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
                    if((account != null && "0".equals(account.getAccountStatus()))
                            || AmazonSpLocalUtils.checkAmazonAccountMsgError(account)) {
                        XxlJobLogger.log("店铺: {} 修改在线listing绑定关系, 账号状态为禁用或者未授权到sp-api ，不执行。", accountNumber);
                        continue;
                    }



                    AmazonListingParentRelationshipTempExample amazonListingParentRelationshipTempExample = new AmazonListingParentRelationshipTempExample();
                    AmazonListingParentRelationshipTempExample.Criteria criteria = amazonListingParentRelationshipTempExample.createCriteria();
                    criteria.andAccountNumberEqualTo(accountNumber)
                            .andOrderSaleEqualTo(false)
                            .andFbaEqualTo(false);
                    if (feedtype.equals(SpFeedType.POST_PRODUCT_DATA.getValue())){
                        criteria.andProductSuccessStatusIsNull().andProductMessageXmlIsNotNull();
                    }else if (feedtype.equals(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue())){
                        criteria.andProductSuccessStatusEqualTo(true).andRelationshipSuccessStatusIsNull().andRelationshipMessageXmlIsNotNull();
                    }
                    //优先处理未处理过得
                    List<AmazonListingParentRelationshipTemp> amazonListingParentRelationshipTempList = amazonListingParentRelationshipTempService.selectByExample(amazonListingParentRelationshipTempExample);
                    if (feedtype.equals(SpFeedType.POST_PRODUCT_DATA.getValue()) && CollectionUtils.isEmpty(amazonListingParentRelationshipTempList)){
                        //重复处理失败的
                        AmazonListingParentRelationshipTempExample repeatParentRelationshipTempExample = new AmazonListingParentRelationshipTempExample();
                        AmazonListingParentRelationshipTempExample.Criteria repeatcriteria = repeatParentRelationshipTempExample.createCriteria();
                        repeatcriteria.andAccountNumberEqualTo(accountNumber)
                                .andOrderSaleEqualTo(false)
                                .andFbaEqualTo(false);
                        repeatcriteria.andRelationshipSuccessStatusEqualTo(false);
                        amazonListingParentRelationshipTempList = amazonListingParentRelationshipTempService.selectByExample(repeatParentRelationshipTempExample);
                    }
                    if (feedtype.equals(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue()) && CollectionUtils.isEmpty(amazonListingParentRelationshipTempList)){
                        //重复处理失败的
                        AmazonListingParentRelationshipTempExample repeatParentRelationshipTempExample = new AmazonListingParentRelationshipTempExample();
                        AmazonListingParentRelationshipTempExample.Criteria repeatcriteria = repeatParentRelationshipTempExample.createCriteria();
                        repeatcriteria.andAccountNumberEqualTo(accountNumber)
                                .andOrderSaleEqualTo(false)
                                .andFbaEqualTo(false);
                        repeatcriteria.andProductSuccessStatusEqualTo(true).andRelationshipSuccessStatusEqualTo(false);
                        amazonListingParentRelationshipTempList = amazonListingParentRelationshipTempService.selectByExample(repeatParentRelationshipTempExample);
                    }
                    if (CollectionUtils.isEmpty(amazonListingParentRelationshipTempList)){
                        continue;
                    }

                    //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
                    DataContextHolder.setUsername(StrConstant.ADMIN);
                    TempAmazonProductRestoreRelationPublishCall call = new TempAmazonProductRestoreRelationPublishCall(accountNumber);
                    call.batchPublish(amazonListingParentRelationshipTempList, new ArrayList<>(Arrays.asList(feedtype)));
                }catch (Exception e){
                    log.error(String.format("账号%s 出错", accountNumber), e);
                }
           // });
        }
        XxlJobLogger.log("执行结束");
    }

}
