package com.estone.erp.publish.amazon.call.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.Attribute;
import com.estone.erp.publish.amazon.call.xsd.XsdBuilder;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.Node;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.XsdElement;
import com.estone.erp.publish.amazon.call.xsd.parse.BrowseTreeReportParser;
import com.estone.erp.publish.amazon.call.xsd.parse.ProductXsdParser;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.POIUtils;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.util.Assert;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class XsdUtils {

    public static String xsdPath;
    private volatile static Schema ENVELOPE_XSD_SCHEMA;

    public static final List<String> BASE_TYPES = Arrays.asList("xsd:string", "xsd:decimal", "xsd:integer",
            "xsd:boolean", "xsd:date", "xsd:time", "xsd:dateTime");

    public static void init() {
        ProductXsdParser.parse();
    }

    public static boolean downloadXsds(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                FileUtils.cleanDirectory(file);
            }
            Set<String> xsds = new HashSet<String>();
            recursiveXsd(xsds, filePath, "amzn-envelope.xsd");
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
            return false;
        }

        return true;
    }

    @SuppressWarnings("unchecked")
    private static void recursiveXsd(Set<String> xsds, String path, String xsdName) throws Exception {
        String content = AmazonUtils.fetchHttpUrl(AmazonConstant.AMAZON_API_XSD_URL + xsdName);
        if (StringUtils.isNotEmpty(content)) {
            log.warn("fetch success: " + xsdName);
            xsds.add(xsdName);
            File file = POIUtils.createFile(path + xsdName);
            FileUtils.write(file, content);
            Document doc = DocumentHelper.parseText(FileUtils.readFileToString(file));
            Element root = doc.getRootElement();
            List<Element> includes = root.elements("include");
            for (Element include : includes) {
                String schemaLocation = include.attributeValue("schemaLocation");
                if (StringUtils.isNotEmpty(schemaLocation) && !xsds.contains(schemaLocation)) {
                    AmazonUtils.sleep(ThreadLocalRandom.current().nextInt(3) * 1000);
                    recursiveXsd(xsds, path, schemaLocation);
                }
            }
        }
        else {
            log.warn("404 not found: " + xsdName);
        }
    }

    public static String attrValue2Str(Element element, String attr) {
        if (element != null && StringUtils.isNotEmpty(attr)) {
            return element.attributeValue(attr);
        }

        return null;
    }

    public static Integer attrValue2Int(Element element, String attr) {
        String value = attrValue2Str(element, attr);
        if (StringUtils.isNotEmpty(value)) {
            return Integer.valueOf(value);
        }

        return null;
    }

    public static Double attrValue2Double(Element element, String attr) {
        String value = attrValue2Str(element, attr);
        if (StringUtils.isNotEmpty(value)) {
            return Double.valueOf(value);
        }

        return null;
    }

    public static String subAttrValue2Str(Element element, String subEleName, String attr) {
        if (element != null && StringUtils.isNotEmpty(subEleName) && StringUtils.isNotEmpty(attr)) {
            Element sub = element.element(subEleName);
            if (sub != null) {
                return sub.attributeValue(attr);
            }
        }

        return null;
    }

    public static Integer subAttrValue2Int(Element element, String subEleName, String attr) {
        String value = subAttrValue2Str(element, subEleName, attr);
        if (StringUtils.isNotEmpty(value)) {
            return Integer.valueOf(value.trim());
        }

        return null;
    }

    public static Double subAttrValue2Double(Element element, String subEleName, String attr) {
        String value = subAttrValue2Str(element, subEleName, attr);
        if (StringUtils.isNotEmpty(value)) {
            return Double.valueOf(value.trim());
        }

        return null;
    }

    public static boolean isSimpleType(Element element) {
        String type = attrValue2Str(element, "type");

        boolean result = false;
        if (StringUtils.isNotEmpty(type)) {
            result = type.startsWith("xsd:");
        }
        if (!result) {
            result = element.element("simpleType") != null;
        }

        return result;
    }

    public static String getType(boolean isSimple) {
        return BooleanUtils.toBoolean(isSimple) ? "simpleType" : "complexType";
    }

    @SuppressWarnings("unchecked")
    public static <T> T clone(T t) {
        String json = JSON.toJSONString(t);
        return (T) JSON.parseObject(json, t.getClass());
    }

    public static <T> List<T> mergeNewList(List<T> list1, List<T> list2) {
        List<T> result = null;
        if (list1 != null) {
            result = new ArrayList<T>(list1);
            if (list2 != null) {
                result.addAll(new ArrayList<T>(list2));
            }
        }
        else if (list2 != null) {
            result = new ArrayList<T>(list2);
        }

        return result;
    }

    public static ResponseJson validateXMLSchema(File xmlFile) {
        ResponseJson rsp = new ResponseJson();
        try {
            Validator validator = getEnvelopeXsdSchemaInstance().newValidator();
            validator.validate(new StreamSource(xmlFile));
        }
        catch (Exception e) {
            //log.warn(e.getMessage(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage(e.getClass() + ": " + e.getMessage());
        }

        return rsp;
    }

    public static ResponseJson validateAmazonXmlSchema(File amazonXmlFile) {
        return validateXMLSchema(amazonXmlFile);
    }

    public static String getFinalStatus(Element type) {
        if (type == null) {
            return null;
        }

        if (type.element("sequence") != null) {
            return "sequence";
        }

        if (type.element("choice") != null) {
            return "choice";
        }

        return null;
    }

    public static ElementWrapper getFirstElementWrapper(Map<String, ElementWrapper> items) {
        if (MapUtils.isEmpty(items)) {
            return null;
        }

        return items.get(items.keySet().iterator().next());
    }

    public static String getItemTypeByAmazonCategory(AmazonCategoryWithBLOBs category) {
        if (category == null) {
            return null;
        }

        Node node = BrowseTreeReportParser.transferAmazonCategory(category);
        List<Attribute<String, String>> nodeAttrs = node.getBrowseNodeAttributes();
        if (CollectionUtils.isNotEmpty(nodeAttrs)) {
            for (Attribute<String, String> attribute : nodeAttrs) {
                String attrValue = attribute.getValue();
                if (!(StringUtils.isEmpty(attrValue) || (attrValue.length() > 6
                        && attrValue.substring(attrValue.length() - 6).matches("^[0-9]*$")))) {
                    return attrValue;
                }
            }
        }

        return node.getBrowseNodeName();
    }

    public static String getXsdPath() {
        return xsdPath;
    }

    public static void setXsdPath(String xsdPath) {
        XsdUtils.xsdPath = xsdPath;
        getEnvelopeXsdSchemaInstance();
    }

    private static Schema getEnvelopeXsdSchemaInstance() {
        if (ENVELOPE_XSD_SCHEMA == null) {
            synchronized (XsdUtils.class) {
                if (ENVELOPE_XSD_SCHEMA == null) {
                    try {
                        SchemaFactory factory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
                        factory.setFeature("http://apache.org/xml/features/internal/tolerate-duplicates", true);
                        File xsdFile = new File(xsdPath + "amzn-envelope.xsd");
                        ENVELOPE_XSD_SCHEMA = factory.newSchema(xsdFile);
                    } catch (SAXException e) {
                        log.error("获取EnvelopeXsdSchema 异常:", e);
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return ENVELOPE_XSD_SCHEMA;
    }

    public static Map<Integer, String> splitXmlByMessage(String xml) {
        if (StringUtils.isEmpty(xml)) {
            return CommonUtils.emptyMap();
        }

        int msgStartIndex = -1;
        List<String> messageXmls = new ArrayList<>(4);
        String msgItem = "<Message>";
        while ((msgStartIndex = xml.indexOf(msgItem, msgStartIndex + 1)) > -1) {
            messageXmls
                    .add(xml.substring(msgStartIndex, xml.indexOf("</Message>", msgStartIndex) + msgItem.length() + 1));
        }

        String msgIdItem = "<MessageID>";
        Map<Integer, String> msgId2MessageXmlMap = new HashMap<>(messageXmls.size());
        for (String messageXml : messageXmls) {
            int msgIdIndex = messageXml.indexOf(msgIdItem);
            if (msgIdIndex < 0) {
                throw new RuntimeException("xml中不包含MessageID元素，请检查xml格式是否正确！");
            }
            int length = msgIdItem.length();
            int startIndex = msgIdIndex + length;
            int endIndex = messageXml.indexOf("<", startIndex);
            Integer msgId = Integer.valueOf(messageXml.substring(startIndex, endIndex));
            msgId2MessageXmlMap.put(msgId,
                    messageXml.substring(endIndex + length + 1, messageXml.length() - length + 1));
        }

        return msgId2MessageXmlMap;
    }

    /**
     *  获取分类类型
     * @param xsdName
     * @return
     */
    public static List<String> resolutionXsds(String xsdName){
        List<String> productType = new ArrayList<>();
        String fileName;
        if (StringUtils.isNotEmpty(xsdName)) {
            XsdBuilder builder = XsdBuilder.getXsdBuilder(xsdName);
            Assert.notNull(builder);
            fileName = xsdName.substring(0, xsdName.lastIndexOf("."));
            if(fileName.equals("SWVG")){    //兼容文件名称缩写
                fileName = "SoftwareVideoGames";
            }
            if(fileName.equals("ProductClothing")){
                fileName = "Clothing";
            }
            XsdElement headElement = builder.getXsdElement(fileName);
            List<XsdElement> elements = headElement.getType().getElements();
            for (XsdElement xsdElement : elements) {
                String itemName = xsdElement.getName();
                if(itemName.equals("ClothingType") || itemName.equals("ProductType")){
                    if(xsdElement.getType().getIsSimple()){
                        List<String> enumerations = xsdElement.getType().getRestriction().getEnumerations();
                        for (String enumeration : enumerations) {
                            productType.add(fileName + "." + enumeration);
                        }
                    }else {
                        List<XsdElement> xsdElements = xsdElement.getType().getElements();
                        for (XsdElement xsd : xsdElements) {
                            productType.add(fileName + "." + xsd.getName());
                        }
                    }
                }
                if(itemName.equals("ClassificationData") && CollectionUtils.isEmpty(productType)){
                    List<XsdElement> elementList = xsdElement.getType().getElements();
                    for (XsdElement element : elementList) {
                        String elementName = element.getName();
                        if(elementName.equals("ClothingType")){
                                if(element.getType().getIsSimple()){
                                    List<String> enumerations = element.getType().getRestriction().getEnumerations();
                                    for (String enumeration : enumerations) {
                                        productType.add(fileName + "." + enumeration);
                                    }
                                }else {
                                    List<XsdElement> xsdElements = element.getType().getElements();
                                    for (XsdElement xsd : xsdElements) {
                                        productType.add(fileName + "." + xsd.getName());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        return productType;
    }


    public static String buildXmlWithMessage(String merchantId, String feedType, List<String> messageXmls) {
        if (StringUtils.isEmpty(merchantId) || feedType == null || CollectionUtils.isEmpty(messageXmls)) {
            return null;
        }

        StringBuilder sb = new StringBuilder().append("<?xml version=\"1.0\" ?>").append(
                "<AmazonEnvelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:noNamespaceSchemaLocation=\"amzn-envelope.xsd\">")
                .append("<Header><DocumentVersion>1.01</DocumentVersion><MerchantIdentifier>").append(merchantId)
                .append("</MerchantIdentifier></Header><MessageType>").append(feedTypeMessageType(feedType))
                .append("</MessageType><PurgeAndReplace>false</PurgeAndReplace>");

        int index = 0;
        for (String messageXml : messageXmls) {
            sb.append("<Message><MessageID>").append(++index).append("</MessageID>").append(messageXml)
                    .append("</Message>");
        }
        sb.append("</AmazonEnvelope>");

        return sb.toString();
    }

    public static String feedTypeMessageType(String feedType){
        if (null == feedType){
            return null;
        }
        if (feedType.equals(SpFeedType.POST_PRODUCT_DATA.getValue())){
            return "Product";
        }
        if (feedType.equals(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue())){
            return "Relationship";
        }
        if (feedType.equals(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue())){
            return "ProductImage";
        }
        if (feedType.equals(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue())){
            return "Price";
        }
        if (feedType.equals(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue())){
            return "Inventory";
        }
        return null;
    }
}
