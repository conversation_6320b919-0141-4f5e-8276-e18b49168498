package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.configlog.AmazonMarketingConfigLogSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonMarketingConfigLogMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMarketingConfigLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * amazon市场更改配置日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class AmazonMarketingConfigLogServiceImpl extends ServiceImpl<AmazonMarketingConfigLogMapper, AmazonMarketingConfigLog> implements AmazonMarketingConfigLogService {

    @Override
    public ApiResult<IPage<AmazonMarketingConfigLog>> search(AmazonMarketingConfigLogSearchDTO searchParam) {
        LambdaQueryWrapper<AmazonMarketingConfigLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonMarketingConfigLog::getMarketingId, searchParam.getMarketingId());
        wrapper.eq(AmazonMarketingConfigLog::getType, searchParam.getType());
        wrapper.orderByDesc(StringUtils.isBlank(searchParam.getSortField()), AmazonMarketingConfigLog::getOperateTime);

        Page<AmazonMarketingConfigLog> page = new Page<>(searchParam.getPageIndex(), searchParam.getPageSize());
        IPage<AmazonMarketingConfigLog> iPage = page(page, wrapper);
        return ApiResult.newSuccess(iPage);
    }
}
