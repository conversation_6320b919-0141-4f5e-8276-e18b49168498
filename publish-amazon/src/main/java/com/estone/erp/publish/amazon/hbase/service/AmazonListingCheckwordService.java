package com.estone.erp.publish.amazon.hbase.service;

import com.estone.erp.publish.amazon.hbase.model.AmazonListingCheckword;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 16:18
 * @description
 */
@Slf4j
@Service
public class AmazonListingCheckwordService {

    /*@Resource
    private AmazonListingCheckwordDao amazonListingCheckwordDao;*/


    /**
     * 根据账号获取listing数据
     * @param account
     * @return
     */
    /*public List<AmazonListingCheckword> selectPageByAccount(String account){
        String prefix = account.substring(0, account.length() - 1);
        CharSequence suffix = account.subSequence(account.length() - 1, account.length());
        String gtAccount = prefix + (char)(suffix.charAt(0)-1);
        String ltAccount = prefix + (char)(suffix.charAt(0)+1);

        long start = System.currentTimeMillis();
        String gtId = "0";
        int limit = 10000;
        List<AmazonListingCheckword> result =new ArrayList<>();
        do {
            List<AmazonListingCheckword> list = amazonListingCheckwordDao.selectPageByAccount(gtAccount, ltAccount, account, gtId, limit);
            log.debug("size :{}", list.size());
            if(CollectionUtils.isEmpty(list)){
                break;
            }
            gtId = list.get(list.size()-1).getId();

            result.addAll(list);
            if(list.size() < limit){
                break;
            }

        }while (true);

        double time = (System.currentTimeMillis() - start)/1000.0;
        if(time > 5){
            log.info("account :{}, size:{}, costTime:{}s", account, result.size(), time);
        }

        return result;
    }*/
}
