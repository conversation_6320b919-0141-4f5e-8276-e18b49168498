package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.EnableBooleanConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-03-22 14:46
 */
@Data
public class CatOperationPublishConfigExcelDO {

    @ExcelProperty("分类")
    private String categoryPathName;

    @ExcelProperty("总次数")
    private Integer publishNumber;

    @ExcelProperty("主管")
    private String saleName;

    @ExcelProperty("刊登次数")
    private Integer salePublishNumber;

    @ExcelProperty(value = "状态",converter = EnableBooleanConverter.class)
    private Boolean status;

    @ExcelProperty(value = "添加时间", converter = TimestampFormatConverter.class)
    private Timestamp createdTime;

    @ExcelProperty(value = "修改时间", converter = TimestampFormatConverter.class)
    private Timestamp updatedTime;



}
