package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsLogService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsService;
import com.estone.erp.publish.tidb.publishtidb.domain.AmazonInfringementWordFrequencyStatisticVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-13 16:25:34
 */
@RestController
@RequestMapping("amazonInfringementWordFrequencyStatistics")
public class AmazonInfringementWordFrequencyStatisticsController {

    @Resource
    private AmazonInfringementWordFrequencyStatisticsService amazonInfringementWordFrequencyStatisticsService;

    @Resource
    private AmazonInfringementWordFrequencyStatisticsLogService amazonInfringementWordFrequencyStatisticsLogService;

    @PostMapping
    public ApiResult<?> postAmazonInfringementWordFrequencyStatistics(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonInfringementWordFrequencyStatistics": // 查询列表
                    CQuery<AmazonInfringementWordFrequencyStatisticsCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonInfringementWordFrequencyStatisticsCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonInfringementWordFrequencyStatisticVO> results = amazonInfringementWordFrequencyStatisticsService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("exportFile")
    public void download(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) throws IOException {
        // 下载数量限制
        int downloadLimitSize = 500000;
        ExcelWriter excelWriter = null;
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        int pageIndex = 0;
        int pageSize = 200;
        long total = 0;
        while (true) {
            CQuery<AmazonInfringementWordFrequencyStatisticsCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonInfringementWordFrequencyStatisticsCriteria>>() {
            });
            cquery.setOffset(pageIndex * pageSize);
            cquery.setLimit(pageSize);
            List<AmazonInfringementWordFrequencyStatisticVO> resultList = amazonInfringementWordFrequencyStatisticsService.download(cquery);
            if (CollectionUtils.isEmpty(resultList)) {
                break;
            }
            if (excelWriter == null) {
                excelWriter = EasyExcel.write(response.getOutputStream(), AmazonInfringementWordFrequencyStatisticVO.class)
                        .autoCloseStream(false)
                        .includeColumnFiledNames(List.of("originWord", "trademarkWord", "totalNumber", "amazonCopywritingNumber", "universalCopywritingNumber"))
                        .build();
            }
            excelWriter.write(resultList, writeSheet);
            pageIndex++;
            total += resultList.size();
            if (total >= downloadLimitSize || resultList.size() < pageSize) {
                break;
            }
        }

        if (total == 0) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError("当前筛选条件没有符合条件的结果")));
            return;
        }
        if (total > downloadLimitSize) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError(String.format("导出数据超过最大%s行", downloadLimitSize))));
            return;
        }

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("amazonInfringementWordFrequencyStatisticsAudit_" + System.currentTimeMillis(), StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        excelWriter.finish();
    }

    @GetMapping(value = "/{relationId}")
    public ApiResult<?> getAmazonInfringementWordFrequencyStatisticsLog(@PathVariable(value = "relationId", required = true) Long relationId,
                                                                        @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit,
                                                                        @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset) {
        CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> results =  amazonInfringementWordFrequencyStatisticsLogService.getAmazonInfringementWordFrequencyStatisticsLogPage(relationId, limit, offset);
        return results;
    }

}