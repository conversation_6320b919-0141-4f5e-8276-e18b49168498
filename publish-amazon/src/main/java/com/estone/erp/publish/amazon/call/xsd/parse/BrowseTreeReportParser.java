package com.estone.erp.publish.amazon.call.xsd.parse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.amazon.call.model.Attribute;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.Node;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.RefinementField;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.RefinementName;
import com.estone.erp.publish.amazon.call.xsd.model.BrowseTreeReport.RefinementsInformation;
import com.estone.erp.publish.amazon.model.AmazonCategory;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class BrowseTreeReportParser {
    @SuppressWarnings("unchecked")
    public static BrowseTreeReport parseReport(String xml) throws DocumentException {
        if (StringUtils.isEmpty(xml)) {
            return null;
        }

        BrowseTreeReport report = new BrowseTreeReport();

        Document doc = DocumentHelper.parseText(xml);
        Element root = doc.getRootElement();
        report.setQuery(root.elementTextTrim("query"));
        List<Element> nodeEls = root.elements("Node");
        if (CollectionUtils.isNotEmpty(nodeEls)) {
            List<Node> nodes = new ArrayList<Node>(nodeEls.size());
            report.setNodes(nodes);

            nodeEls.forEach(nodeEl -> {
                Node node = report.createNode();
                nodes.add(node);

                node.setBrowseNodeId(nodeEl.elementTextTrim("browseNodeId"));
                Element browseNodeAttributes = nodeEl.element("browseNodeAttributes");
                if (browseNodeAttributes != null) {
                    List<Element> attributes = browseNodeAttributes.elements("attribute");
                    node.setBrowseNodeAttributes(attributes.stream().map(attribute -> {
                        return Attribute.of(attribute.attribute("name").getName(), attribute.getTextTrim());
                    }).collect(Collectors.toList()));
                }

                node.setBrowseNodeName(nodeEl.elementTextTrim("browseNodeName"));
                node.setBrowseNodeStoreContextName(nodeEl.elementTextTrim("browseNodeStoreContextName"));
                node.setBrowsePathById(nodeEl.elementTextTrim("browsePathById"));
                node.setBrowsePathByName(nodeEl.elementTextTrim("browsePathByName"));
                node.setHasChildren(Boolean.valueOf(nodeEl.elementTextTrim("hasChildren")));

                Element childNodesEl = nodeEl.element("childNodes");
                if (childNodesEl != null) {
                    List<Element> idEls = childNodesEl.elements("id");
                    node.setChildNodes(idEls.stream().map(idEl -> {
                        return idEl.getTextTrim();
                    }).collect(Collectors.toList()));
                }

                node.setProductTypeDefinitions(nodeEl.elementTextTrim("productTypeDefinitions"));

                Element infoEl = nodeEl.element("refinementsInformation");
                if (infoEl != null) {
                    RefinementsInformation info = node.createRefinementsInformation();
                    node.setRefinementsInformation(info);

                    List<Element> nameEls = infoEl.elements("refinementName");
                    info.setRefinementNames(nameEls.stream().map(nameEl -> {
                        List<Element> fieldEls = nameEl.elements("refinementField");
                        RefinementName refinementName = info.createRefinementName();
                        List<RefinementField> fields = fieldEls.stream().map(fieldEl -> {
                            RefinementField field = refinementName.createRefinementField();
                            field.setAcceptedValues(Arrays
                                    .asList(StringUtils.split(fieldEl.elementTextTrim("acceptedValues"), ",")));
                            field.setHasModifier(fieldEl.elementTextTrim("hasModifier"));
                            field.setModifiers(fieldEl.elementTextTrim("modifiers"));
                            field.setRefinementAttribute(fieldEl.elementTextTrim("refinementAttribute"));

                            return field;
                        }).collect(Collectors.toList());

                        refinementName
                                .setRefinementFields(Attribute.of(nameEl.attribute("name").getText(), fields));
                        return refinementName;
                    }).collect(Collectors.toList()));
                }
            });
        }

        return report;
    }

    public static List<AmazonCategory> transferReport(BrowseTreeReport report) {
        if (report == null || report.getNodes() == null) {
            new ArrayList<AmazonCategory>(0);
        }

        return report.getNodes().stream().map(node -> {
            return transferReportNode(node, null, null);
        }).collect(Collectors.toList());
    }

    public static AmazonCategoryWithBLOBs transferReportNode(Node node, String accountNumber, String accountSite) {
        if (node == null) {
            return null;
        }

        AmazonCategoryWithBLOBs category = new AmazonCategoryWithBLOBs();
        category.setAccountNumber(accountNumber);
        category.setAccountSite(accountSite);
        category.setBrowseNodeId(node.getBrowseNodeId());
        category.setBrowseNodeAttributes(JSON.toJSONString(node.getBrowseNodeAttributes()));
        category.setBrowseNodeName(node.getBrowseNodeName());
        category.setBrowseNodeStoreContextName(node.getBrowseNodeStoreContextName());
        category.setBrowsePathById(node.getBrowsePathById());
        category.setBrowsePathByName(node.getBrowsePathByName());
        category.setHasChildren(node.isHasChildren());
        category.setChildNodes(JSON.toJSONString(node.getChildNodes()));
        category.setProductTypeDefinitions(node.getProductTypeDefinitions());
        category.setRefinementsInformation(JSON.toJSONString(node.getRefinementsInformation()));

        return category;
    }

    public static BrowseTreeReport transferAmazonCategorys(List<AmazonCategoryWithBLOBs> categorys) {
        BrowseTreeReport report = new BrowseTreeReport();
        if (CollectionUtils.isEmpty(categorys)) {
            return report;
        }

        List<Node> nodes = categorys.stream().sorted(new Comparator<AmazonCategoryWithBLOBs>() {
            @Override
            public int compare(AmazonCategoryWithBLOBs c1, AmazonCategoryWithBLOBs c2) {
                return c1.getBrowsePathById().compareTo(c2.getBrowsePathById());
            }
        }).map(category -> {
            return transferAmazonCategory(category);
        }).collect(Collectors.toList());

        report.setNodes(nodes);
        return report;
    }

    public static Node transferAmazonCategory(AmazonCategoryWithBLOBs category) {
        if (category == null) {
            return null;
        }

        Node node = new Node();
        node.setId(category.getId());
        node.setBrowseNodeId(category.getBrowseNodeId());
        node.setBrowseNodeAttributes(JSON.parseObject(category.getBrowseNodeAttributes(),
                new TypeReference<List<Attribute<String, String>>>() {
                }));
        node.setBrowseNodeName(category.getBrowseNodeName());
        node.setBrowseNodeStoreContextName(category.getBrowseNodeStoreContextName());
        node.setBrowsePathById(category.getBrowsePathById());
        node.setBrowsePathByName(category.getBrowsePathByName());
        node.setBrowsePathByNameCn(category.getBrowsePathByNameCn());
        node.setHasChildren(category.getHasChildren());
        node.setChildNodes(JSON.parseArray(category.getChildNodes(), String.class));
        node.setProductTypeDefinitions(category.getProductTypeDefinitions());
        node.setRefinementsInformation(
                JSON.parseObject(category.getRefinementsInformation(), new TypeReference<RefinementsInformation>() {
                }));

        return node;
    }
}
