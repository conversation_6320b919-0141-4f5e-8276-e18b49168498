package com.estone.erp.publish.amazon.hbase.dao;

import com.estone.erp.publish.amazon.hbase.model.AmazonListingCheckword;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 15:17
 * @description
 */
/*
public interface AmazonListingCheckwordDao {

    */
/**
     * 根据账号获取listing数据.  gtAccount、ltAccount 目的是为了命中索引，提高查询效率
     * @param gtAccount
     * @param ltAccount
     * @param account
     * @param gtId 大于id
     * @return
     *//*

    @Select("select * from \"amazon_listing_checkword\" " +
            "where \"id\" > #{gtAccount} and \"id\" < #{ltAccount} and \"accountNumber\"=#{account} and \"id\" > #{gtId} order by \"id\" asc limit ${limit}")
    List<AmazonListingCheckword> selectPageByAccount(@Param("gtAccount") String gtAccount, @Param("ltAccount") String ltAccount,
                                                     @Param("account") String account, @Param("gtId") String gtId, @Param("limit")int limit);

}
*/
