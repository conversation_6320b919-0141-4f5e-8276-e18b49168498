package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.estone.erp.publish.component.converter.ExcelMonitorTypeConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@ContentRowHeight(16)
@HeadRowHeight(20)
@ColumnWidth(20)
public class AmazonProductMonitorVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 紫鸟名
     */
    @ExcelProperty("紫鸟名")
    private String purpleBirdName;

    /**
     * 店铺账号
     */
    @ExcelProperty("店铺账号")
    private String accountNumber;


    /**
     * 站点
     */
    @ExcelProperty("站点")
    private String site;

    /**
     * 父asin
     */
    @ExcelProperty("父asin")
    private String parentAsin;

    /**
     * 子asin
     */
    @ExcelProperty("子asin")
    private String sonAsin;

    /**
     * 主sku
     */
    @ExcelProperty("主sku")
    private String mainSku;

    /**
     * 子sku
     */
    @ExcelProperty("子sku")
    private String articleNumber;

    /**
     * sellerSku
     */
    @ExcelProperty("sellerSku")
    private String sellerSku;

    /**
     * 监控类型
     */
    @ExcelProperty(value = "监控类型", converter = ExcelMonitorTypeConverter.class)
    private Integer monitorType;

    /**
     * 监控类型
     */
    @ExcelProperty("监控条件")
    private String monitorContent;

    /**
     * 下架原因
     */
    @ExcelProperty("下架原因")
    private String offlineRemark;

    /**
     * 禁售平台
     */
    @ExcelProperty("禁售平台")
    private String forbidChannel;

    /**
     * 禁售类型
     */
    @ExcelProperty("禁售类型")
    private String infringementTypename;

    /**
     * 禁售原因
     */
    @ExcelProperty("禁售原因")
    private String infringementObj;

    /**
     * 禁售站点
     */
    @ExcelProperty("禁售站点")
    private String prohibitionSite;

    /**
     * 订单24小时销量
     */
    @ExcelProperty("订单24小时销量")
    private Integer orderLast24hCount;

    /**
     * 订单7天销量
     */
    @ExcelProperty("订单7天销量")
    private Integer orderLast7dCount;

    /**
     * 订单14天销量
     */
    @ExcelProperty("订单14天销量")
    private Integer orderLast14dCount;

    /**
     * 订单30天销量
     */
    @ExcelProperty("订单30天销量")
    private Integer orderLast30dCount;

    /**
     * 订单总销量
     */
    @ExcelProperty("总销量")
    private Integer orderNumTotal;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String saleId;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String saleManager;

    /**
     * 上架时间
     */
    @ExcelProperty(value = "上架时间", converter = TimestampFormatConverter.class)
    private Timestamp openDate;

    /**
     * 下架时间
     */
    @ExcelProperty(value = "下架时间", converter = TimestampFormatConverter.class)
    private Timestamp offlineDate;

    /**
     * 创建时间/跟踪时间
     */
    @ExcelProperty(value = "跟踪时间", converter = TimestampFormatConverter.class)
    private Timestamp createTime;
}