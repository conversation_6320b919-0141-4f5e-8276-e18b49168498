package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class AmazonImageDownloadCriteria {

    private String productArticleNumber;

    private String articleNumber;

    private String accountNumber;

    private List<String> productArticleNumbers;

    /**
     * 组合sku
     */
    private String suitArticleNumber;

    /**
     * 组合sku
     */
    private List<String> suitArticleNumberList;

    /**
     * 试卖spu
     */
    private String spArticleNumber;
    /**
     * 试卖spu
     */
    private List<String> spArticleNumbers;

    /**
     * 下载类型： 5 组合，1 spu，2 试卖spu
     */
    private String downloadType;

    /**
     * 图片类型： PictureTypeEnum
     */
    private String pictureType;
}
