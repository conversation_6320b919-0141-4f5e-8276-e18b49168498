package com.estone.erp.publish.amazon.enums;

import lombok.Getter;

/**
 * 刊登类型枚举
 * 刊登类型：1 多站点刊登、2 自动刊登、3 普通刊登、 4 自动刊登-生成模板
 */
@Getter
public enum PublishTypeEnum {
    MULTISTATION_PUBLISH(1,"多站点刊登"),
    AUTO_PUBLISH(2,"自动刊登"),
    COMMON_PUBLISH(3, "生成模版"),
    AUTO_PUBLISH_TEMPLATE(4, "直接刊登"),
    AUTO_PUBLISH_TEMPLATE_TIME(5, "定时刊登"),
    ;


    private Integer code;
    private String name;

    private PublishTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isTrue(Integer val) {
        return this.code.equals(val);
    }

    public static boolean isAutoPublish(Integer val) {
        if (val == null) {
            return false;
        }
        return AUTO_PUBLISH.code.equals(val) || AUTO_PUBLISH_TEMPLATE.code.equals(val);
    }

    public static boolean jsonFeedPublish(Integer val) {
        if (val == null) {
            return false;
        }
        return AUTO_PUBLISH.code.equals(val) || AUTO_PUBLISH_TEMPLATE_TIME.code.equals(val);
    }

}
