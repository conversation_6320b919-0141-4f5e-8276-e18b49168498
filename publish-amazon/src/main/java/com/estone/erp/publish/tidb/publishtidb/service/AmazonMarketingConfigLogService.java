package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.configlog.AmazonMarketingConfigLogSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;

/**
 * <p>
 * amazon市场更改配置日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface AmazonMarketingConfigLogService extends IService<AmazonMarketingConfigLog> {
    ApiResult<IPage<AmazonMarketingConfigLog>> search(AmazonMarketingConfigLogSearchDTO searchParam);

}
