package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DynamicLimiter;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.client.AmazonSpLocalServiceClient;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.publish.domain.*;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonMarketplaceEnums;
import com.estone.erp.publish.amazon.componet.publish.util.AmazonListingApiUtil;
import com.estone.erp.publish.amazon.enums.AmazonGPSRLocalEnums;
import com.estone.erp.publish.amazon.enums.SystemFeedTypeEnum;
import com.estone.erp.publish.amazon.model.dto.AmazonListingUpdateQuantityDO;
import com.estone.erp.publish.amazon.model.request.DefinitionsProductTypeRequest;
import com.estone.erp.publish.amazon.util.request.JSONListingFeedRequest;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import com.google.common.util.concurrent.RateLimiter;
import enums.Requirements;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.ItemType;
import io.swagger.client.enums.ReportType;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.CompetitiveSummaryBatchResponse;
import io.swagger.client.model.GetFeaturedOfferExpectedPriceBatchResponse;
import io.swagger.client.model.ReportOptions;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import io.swagger.client.model.listings.*;
import io.swagger.client.request.*;
import io.swagger.client.response.AmazonReportsDownloadProgress;
import lombok.extern.slf4j.Slf4j;
import model.ProductTypeDefinition;
import model.ProductTypeList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.data.util.Pair;
import request.RequestDefinitionsApiParam;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Auther yucm
 * @Date 2021/8/11
 */
@Slf4j
public class AmazonSpLocalServiceUtils {

    private final static String COMPETITIVE_PRICE__RATE_KEY = "COMPETITIVE_PRICE_";

    private final static String CATALOG_ITEM_RATE_KEY = "CATALOG_ITEM_";
    private final static String DELETE_LISTINGS_ITEM_RATE_KEY = "DELETE_LISTINGS_ITEM_";

    private final static String SEARCH_PRODUCT_TYPE_BY_ITEM_NAME_RATE_KEY = "SEARCH_PRODUCT_TYPE_BY_ITEM_NAME_RATE_";

    private final static String SEARCH_CATALOG_ITEM_RATE_KEY = "SEARCH_CATALOG_ITEM_";

    private static AmazonSpLocalServiceClient amazonSpLocalServiceClient;

    static {
        try {
            amazonSpLocalServiceClient = SpringUtils.getBean(AmazonSpLocalServiceClient.class);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
    }

    /**
     * 下架产品
     *
     * @param sellerSku
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> deleteListingsItem(String sellerSku, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (StringUtils.isBlank(sellerSku)) {
            return ApiResult.newError("sellerSku为空！");
        }

        RequestListingsItemsApiParam request = new RequestListingsItemsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSellerSku(sellerSku);

        Long t1 = System.currentTimeMillis();

        try {
            // 按套账限流 1d
            RateLimiter rateLimiter = DynamicLimiter.getInstance(DELETE_LISTINGS_ITEM_RATE_KEY + amazonSpAccount.getMerchantId(), 3d);
            rateLimiter.acquire();
            return EnvironmentSupplierWrapper.execute(
                    () -> amazonSpLocalServiceClient.deleteListingsItem(request),
                    () -> {
                        log.info("非正式环境，下架产品:店铺{}, sellerSku:{}", amazonSpAccount.getAccountNumber(), sellerSku);
                        ListingsItemSubmissionResponse listingsItemSubmissionResponse = new ListingsItemSubmissionResponse();
                        listingsItemSubmissionResponse.setSubmissionId("mock-submissionId");
                        listingsItemSubmissionResponse.setStatus(ListingsItemSubmissionResponse.StatusEnum.ACCEPTED);
                        listingsItemSubmissionResponse.setSku(sellerSku);
                        return ApiResult.newSuccess(listingsItemSubmissionResponse);
                    });
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "deleteListingsItem error " + e.getMessage());
            return ApiResult.newError("deleteListingsItem error " + e.getMessage());
        }
    }

    /**
     * 获取Listing详情 根据sellersku
     *
     * @param sellersku
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<Item> getListingsItem(String sellersku, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (StringUtils.isBlank(sellersku)) {
            return ApiResult.newError("sellersku为空！");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 5d);
        rateLimiter.acquire();
        RequestListingsItemsApiParam request = new RequestListingsItemsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSellerSku(sellersku);
        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.getListingsItem(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "getListingsItem error " + e.getMessage());
            return ApiResult.newError("getListingsItem error " + e.getMessage());
        }
    }


    /**
     * 获取Listing详情 根据sellersku
     *
     * @param request
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<io.swagger.client.model.listings.ItemSearchResults> searchListingsItems(RequestListingsItemsApiParam request, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 5d);
        rateLimiter.acquire();
        request.setAmazonSpAccount(amazonSpAccount);
        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.searchListingsItems(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "searchListingsItems error " + e.getMessage());
            return ApiResult.newError("searchListingsItems error " + e.getMessage());
        }
    }

    /**
     * 请求下载报告 (支持等待结果 和异步接受MQ消息处理报告数据)
     *
     * @param reportType
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<AmazonReportsDownloadProgress> getReportFileResponse(ReportType reportType, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (null == reportType) {
            return ApiResult.newError("请正确设置reportType");
        }

        ReportOptions reportOptions = new ReportOptions();
        reportOptions.put("custom", "true");

        RequestReportsApiParam request = new RequestReportsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setReportOptions(reportOptions);
        request.setReportType(reportType);

        Long t1 = System.currentTimeMillis();
        ApiResult<AmazonReportsDownloadProgress> apiResult = null;
        try {
            apiResult = amazonSpLocalServiceClient.getReportFileResponse(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            apiResult = apiResult.newError("getReportFileResponse error " + e.getMessage());
            //log.error("耗时" + (t2 - t1) + "getReportFileResponse error " + e.getMessage());
        }
        return apiResult;
    }

    /**
     * 获取Listing详情 根据ASIN
     *
     * @param asin
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<io.swagger.client.model.catalogItems.Item> getCatalogItem(String asin, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (StringUtils.isBlank(asin)) {
            return ApiResult.newError("asin为空！");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 5d);
        rateLimiter.acquire();
        RequestCatalogApiParam request = new RequestCatalogApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setAsin(asin);

        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.getCatalogItem(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
           // log.error("耗时" + (t2 - t1) + "getCatalogItem error " + e.getMessage());
            return ApiResult.newError("getCatalogItem error " + e.getMessage());
        }
    }

    /**
     * 查询listing详情 根据keywords （EAN/UPC/ASIN）
     *
     * @param keywords
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<ItemSearchResults> searchCatalogItems(List<String> keywords, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (CollectionUtils.isEmpty(keywords)) {
            return ApiResult.newError("keywords为空！");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(SEARCH_CATALOG_ITEM_RATE_KEY + key, 3d);
        rateLimiter.acquire();

        RequestCatalogApiParam request = new RequestCatalogApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setKeywords(keywords);

        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.searchCatalogItems(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
           // log.error("耗时" + (t2 - t1) + "searchCatalogItems error " + e.getMessage());
            return ApiResult.newError("searchCatalogItems error " + e.getMessage());
        }
    }

    /**
     * 查询listing详情 根据identifiers
     *
     * @param identifiers
     * @param amazonSpAccount
     * @param identifiersType
     * @return
     */
    public static ApiResult<ItemSearchResults> searchCatalogItemsByIdentifiers(List<String> identifiers, AmazonSpAccount amazonSpAccount, String identifiersType, List<String> includedData) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (CollectionUtils.isEmpty(identifiers) || StringUtils.isBlank(identifiersType)) {
            return ApiResult.newError("identifiers为空或类型为空！");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(SEARCH_CATALOG_ITEM_RATE_KEY + key, 1d);
        rateLimiter.acquire();

        RequestCatalogApiParam request = new RequestCatalogApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setIdentifiers(identifiers);
        request.setIdentifiersType(identifiersType);
        request.setSearchIncludedData(includedData);
        Long t1 = System.currentTimeMillis();
        try {
            //log.info("searchCatalogItemsByIdentifiersV2 request {}", JSON.toJSONString(request));
            return amazonSpLocalServiceClient.searchCatalogItems(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
           // log.error("耗时" + (t2 - t1) + "searchCatalogItems error " + e.getMessage());
            return ApiResult.newError("searchCatalogItems error " + e.getMessage());
        }
    }

    public static ApiResult<ItemSearchResults> searchCatalogItemsByIdentifiersV2(List<String> identifiers, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(SEARCH_CATALOG_ITEM_RATE_KEY + key, 1d);
        rateLimiter.acquire();
        RequestCatalogApiParam request = new RequestCatalogApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setKeywords(identifiers);
        request.setSearchIncludedData(List.of("classifications", "productTypes"));
        Long t1 = System.currentTimeMillis();

        return RetryUtil.doRetry(() -> {
            ApiResult<ItemSearchResults> apiResult = amazonSpLocalServiceClient.searchCatalogItems(request);
            if (apiResult.isSuccess()) {
                return apiResult;
            }

            // 限流重试
            String errorMsg = apiResult.getErrorMsg();
            if (errorMsg.contains("QuotaExceeded")) {
                throw new RuntimeException("QuotaExceeded:" + apiResult.getErrorMsg());
            }
            // 网络异常
            if (errorMsg.contains("Connection reset") || errorMsg.contains("I/O error") || errorMsg.contains("502 Bad Gateway")) {
                throw new RuntimeException("网络异常:" + apiResult.getErrorMsg());
            }
            if (errorMsg.contains("Internal Server Error")) {
                throw new RuntimeException("Internal Server Error:" + apiResult.getErrorMsg());
            }

            return apiResult;
        }, 3);
    }


    /**
     * 添加数据上传任务
     *
     * @param request s     * @return
     */
    public static ApiResult<String> addFeedsTask(RequestFeedsApiParam request) {
        AmazonSpAccount amazonSpAccount = request.getAmazonSpAccount();
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        SpFeedType feedType = request.getSpFeedType();
        Map<String, String> docId2MessageXmlMap = request.getDocId2MessageXmlMap();
        if (null == feedType || null == docId2MessageXmlMap) {
            return ApiResult.newError("feedType or docId2MessageXmlMap message is null");
        }

        Long t1 = System.currentTimeMillis();
        try {
            if (SpFeedType.POST_PRODUCT_PRICING_DATA == feedType) {
                return EnvironmentSupplierWrapper.execute(() -> {
                    return amazonSpLocalServiceClient.addFeedsTask(request, feedType.getValue());
                }, () -> {
                    log.info("非正式环境不进行调价/调库存,param:{}", JSON.toJSONString(request));
                    return ApiResult.newSuccess("非正式环境不进行调价/调库存");
                });
            }
            return amazonSpLocalServiceClient.addFeedsTask(request, feedType.getValue());
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "addFeedsTask error " + e.getMessage());
            return ApiResult.newError("addFeedsTask error " + e.getMessage());
        }

    }

    /**
     * 获取listing运费
     *
     * @param sellerSkus
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<GetFeaturedOfferExpectedPriceBatchResponse> getFeaturedOfferExpectedPriceBatch(List<String> sellerSkus, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (CollectionUtils.isEmpty(sellerSkus)) {
            return ApiResult.newError("sellerSkus为空！");
        }

        RequestProductPriceApiParam request = new RequestProductPriceApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSkus(sellerSkus);
        request.setItemType(ItemType.SKU);

        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.getFeaturedOfferExpectedPriceBatch(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
           // log.error("耗时" + (t2 - t1) + "getPricing error " + e.getMessage());
            return ApiResult.newError("getPricing error " + e.getMessage());
        }
    }

    /**
     * 获取listing运费
     *
     * @param sellerSkus
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<CompetitiveSummaryBatchResponse> getCompetitivePricing(List<String> sellerSkus, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }

        if (CollectionUtils.isEmpty(sellerSkus)) {
            return ApiResult.newError("sellerSkus为空！");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 0.5d
        RateLimiter rateLimiter = DynamicLimiter.getInstance(COMPETITIVE_PRICE__RATE_KEY + key, 0.5d);
        rateLimiter.acquire();

        RequestProductPriceApiParam request = new RequestProductPriceApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSkus(sellerSkus);
        request.setItemType(ItemType.SKU);

        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.getCompetitivePricing(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "getCompetitivePricing error " + e.getMessage());
            return ApiResult.newError("getCompetitivePricing error " + e.getMessage());
        }
    }

    public static ApiResult<ProductTypeDefinition> getDefinitionsProductType(DefinitionsProductTypeRequest param, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        RequestDefinitionsApiParam request = new RequestDefinitionsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        String productType = param.getProductType();
        // 优先 productType -> itemName -> keywords
        if (StringUtils.isBlank(param.getRequirements())) {
            request.setRequirements(Requirements.LISTING.getValue());
        }else {
            request.setRequirements(param.getRequirements());
        }
        if (StringUtils.isNotBlank(productType)) {
            request.setProductType(productType);
        } else if (StringUtils.isNotBlank(param.getItemName())) {
            request.setItemName(param.getItemName());
        } else if (CollectionUtils.isNotEmpty(param.getKeyWords())) {
            request.setKeywords(param.getKeyWords());
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 5d).acquire();
        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.getDefinitionsProductType(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
           // log.error("耗时" + (t2 - t1) + "getListingsItem error " + e.getMessage());
            return ApiResult.newError("getListingsItem error " + e.getMessage());
        }
    }


    public static ApiResult<ProductTypeList> searchDefinitionsProductTypes(DefinitionsProductTypeRequest param, AmazonSpAccount amazonSpAccount) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        RequestDefinitionsApiParam request = new RequestDefinitionsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
         if (StringUtils.isNotBlank(param.getItemName())) {
            request.setItemName(param.getItemName());
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        DynamicLimiter.getInstance(SEARCH_PRODUCT_TYPE_BY_ITEM_NAME_RATE_KEY + key, 5d).acquire();
        Long t1 = System.currentTimeMillis();
        try {
            return amazonSpLocalServiceClient.searchDefinitionsProductTypes(request);
        } catch (Exception e) {
            Long t2 = System.currentTimeMillis();
            //log.error("耗时" + (t2 - t1) + "searchDefinitionsProductTypes error " + e.getMessage());
            return ApiResult.newError("searchDefinitionsProductTypes error " + e.getMessage());
        }
    }



    /**
     * 部分更新
     *
     * @param amazonSpAccount
     * @param sellerSku
     * @param patchRequest
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> patchListingsItem(AmazonSpAccount amazonSpAccount, String sellerSku, ListingsItemPatchRequest patchRequest) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 3d).acquire();
        RequestListingsItemsApiParam apiParam = new RequestListingsItemsApiParam();
        apiParam.setAmazonSpAccount(amazonSpAccount);
        apiParam.setSellerSku(sellerSku);
        apiParam.setListingsItemPatchRequest(patchRequest);
        try {
            return amazonSpLocalServiceClient.patchListingsItem(apiParam);
        } catch (Exception e) {
            return ApiResult.newError("patchListingsItem error " + e.getMessage());
        }
    }

    /**
     * 更新Listing库存
     *
     * @param updateQuantityDO
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> updateListingQuantity(AmazonListingUpdateQuantityDO updateQuantityDO) {
        String accountNumber = updateQuantityDO.getAccountNumber();
        String sellerSku = updateQuantityDO.getSellerSku();
        Integer updateQuantity = updateQuantityDO.getUpdateQuantity();

        // 请求api修改库存
        ListingQuantityData quantityData = ListingQuantityData.of(updateQuantity);

        PatchOperation patchOperation = new PatchOperation();
        patchOperation.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation.setPath("/attributes/fulfillment_availability");
        patchOperation.setValue(quantityData.getBaseValue());

        ListingsItemPatchRequest patchRequest = new ListingsItemPatchRequest();
        patchRequest.setProductType("PRODUCT");
        patchRequest.setPatches(List.of(patchOperation));

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + account.getMerchantId(), 5d).acquire();
        return EnvironmentSupplierWrapper.execute(() -> RetryUtil.doRetry(() -> {
            ApiResult<ListingsItemSubmissionResponse> apiResult = AmazonSpLocalServiceUtils.patchListingsItem(amazonSpAccount, sellerSku, patchRequest);
            if (apiResult.isSuccess()) {
                return apiResult;
            }

            // 限流重试
            String errorMsg = apiResult.getErrorMsg();
            if (errorMsg.contains("QuotaExceeded")) {
                throw new RuntimeException("QuotaExceeded:" + apiResult.getErrorMsg());
            }
            // 网络异常
            if (errorMsg.contains("Connection reset") || errorMsg.contains("I/O error") || errorMsg.contains("502 Bad Gateway")) {
                throw new RuntimeException("网络异常:" + apiResult.getErrorMsg());
            }

            return apiResult;
        }, 4), () -> {
            ListingsItemSubmissionResponse listingsItemSubmissionResponse = new ListingsItemSubmissionResponse();
            listingsItemSubmissionResponse.setStatus(ListingsItemSubmissionResponse.StatusEnum.ACCEPTED);
            return ApiResult.newSuccess(listingsItemSubmissionResponse);
        });
    }

    /**
     * 修改listing 原产地
     *
     * @param accountNumber 店铺1
     * @param sellerSku     sellerSku
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> updateListingCountryOfOrigin(String accountNumber, String sellerSku) {
        ListingsItemPatchRequest patchRequest = new ListingsItemPatchRequest();
        patchRequest.setProductType("PRODUCT");
        List<PatchOperation> patchOperations = new ArrayList<>();
        PatchOperation patchOperation = new PatchOperation();
        patchOperation.setPath("/attributes/country_of_origin");
        patchOperation.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation.setValue(List.of(Maps.of("value", "CN")));
        patchOperations.add(patchOperation);
        patchRequest.setPatches(patchOperations);
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        return AmazonSpLocalServiceUtils.patchListingsItem(amazonSpAccount, sellerSku, patchRequest);
    }


    /**
     * 修改listing GPSR 与原产地
     *
     * @param amazonListingGpsrInfo
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> updateListingGPSRAndOriginOfCountry(AmazonListingGpsrInfo amazonListingGpsrInfo) {
        String accountNumber = amazonListingGpsrInfo.getAccountNumber();
        String sellerSku = amazonListingGpsrInfo.getSellerSku();
        String site = amazonListingGpsrInfo.getSite();
        ListingsItemPatchRequest patchRequest = new ListingsItemPatchRequest();
        patchRequest.setProductType("PRODUCT");

        AmazonGPSRLocalEnums amazonGPSRLocalEnums = AmazonGPSRLocalEnums.valueOf(site);
        List<PatchOperation> patchOperations = new ArrayList<>();
        PatchOperation patchOperation1 = new PatchOperation();
        patchOperation1.setPath("/attributes/compliance_media");
        patchOperation1.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation1.setValue(List.of(Maps.of(
                "content_language", amazonGPSRLocalEnums.getContentLang(),
                "content_type", "safety_information",
                "source_location", amazonListingGpsrInfo.getGpsrPdfLink())));
        patchOperations.add(patchOperation1);

        PatchOperation patchOperation2 = new PatchOperation();
        patchOperation2.setPath("/attributes/gpsr_safety_attestation");
        patchOperation2.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation2.setValue(List.of(Maps.of("value", Boolean.TRUE)));
        patchOperations.add(patchOperation2);

        PatchOperation patchOperation3 = new PatchOperation();
        patchOperation3.setPath("/attributes/country_of_origin");
        patchOperation3.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation3.setValue(List.of(Maps.of("value", "CN")));
        patchOperations.add(patchOperation3);

        patchRequest.setPatches(patchOperations);
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        return AmazonSpLocalServiceUtils.patchListingsItem(amazonSpAccount, sellerSku, patchRequest);
    }

    /**
     * 修改listing GPSR 与原产地
     *
     * @param amazonListingGpsrInfo
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> updateListingGPSRImageAndOriginOfCountry(AmazonListingGpsrInfo amazonListingGpsrInfo) {
        String accountNumber = amazonListingGpsrInfo.getAccountNumber();
        String sellerSku = amazonListingGpsrInfo.getSellerSku();

        ListingsItemPatchRequest patchRequest = new ListingsItemPatchRequest();
        patchRequest.setProductType("PRODUCT");

        List<PatchOperation> patchOperations = new ArrayList<>();
        PatchOperation patchOperation1 = new PatchOperation();
        patchOperation1.setPath("/attributes/image_locator_ps01");
        patchOperation1.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation1.setValue(List.of(Maps.of(
                "media_location", amazonListingGpsrInfo.getGpsrImgLink())));
        patchOperations.add(patchOperation1);

        PatchOperation patchOperation2 = new PatchOperation();
        patchOperation2.setPath("/attributes/gpsr_safety_attestation");
        patchOperation2.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation2.setValue(List.of(Maps.of("value", Boolean.TRUE)));
        patchOperations.add(patchOperation2);

        PatchOperation patchOperation3 = new PatchOperation();
        patchOperation3.setPath("/attributes/country_of_origin");
        patchOperation3.setOp(PatchOperation.OpEnum.REPLACE);
        patchOperation3.setValue(List.of(Maps.of("value", "CN")));
        patchOperations.add(patchOperation3);

//        PatchOperation patchOperation4 = new PatchOperation();
//        patchOperation4.setPath("/attributes/compliance_media");
//        patchOperation4.setOp(PatchOperation.OpEnum.DELETE);
//        patchOperation4.setValue(List.of(Maps.of(
//                "content_language", null,
//                "content_type", null,
//                "source_location", null)));
//        patchOperations.add(patchOperation4);

        patchRequest.setPatches(patchOperations);
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        return AmazonSpLocalServiceUtils.patchListingsItem(amazonSpAccount, sellerSku, patchRequest);
    }


    /**
     * 新增Listing
     *
     * @param amazonSpAccount
     * @return
     */
    public static ApiResult<ListingsItemSubmissionResponse> putListingsItem(AmazonSpAccount amazonSpAccount, String sellerSku, ListingsItemPutRequest request) {
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazonAccount message is null");
        }
        String accountNumber = amazonSpAccount.getAccountNumber();
        String key = accountNumber.substring(accountNumber.lastIndexOf("-") + 1);
        // 按套账限流 1d
        DynamicLimiter.getInstance(CATALOG_ITEM_RATE_KEY + key, 4d).acquire();
        RequestListingsItemsApiParam apiParam = new RequestListingsItemsApiParam();
        apiParam.setAmazonSpAccount(amazonSpAccount);
        apiParam.setSellerSku(sellerSku);
        apiParam.setListingsItemPutRequest(request);
        try {
            return amazonSpLocalServiceClient.putListingsItem(apiParam);
        } catch (Exception e) {
            return ApiResult.newError("putListingsItem error " + e.getMessage());
        }
    }

    /**
     * JSON_LISTINGS_FEED 更新库存或备货期
     *
     * @param amazonProductListing listing
     * @return ApiResult
     */
    public static ApiResult<String> updateJsonListingFeedQuantity(AmazonProductListing amazonProductListing) {
        String accountNumber = amazonProductListing.getAccountNumber();
        String sellerSku = amazonProductListing.getSellerSku();
        Integer updateQuantity = amazonProductListing.getQuantity();

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);


        JSONListingFeedRequest feedRequest = new JSONListingFeedRequest();
        feedRequest.setOperationType("PARTIAL_UPDATE");
        feedRequest.setProductType("PRODUCT");
        feedRequest.setSku(sellerSku);
        boolean isFulfillmentLatency = amazonProductListing.getFulfillmentLatency() != null;
        if (isFulfillmentLatency) {
            ListingQuantityData quantityData = ListingQuantityData.of(updateQuantity, amazonProductListing.getFulfillmentLatency());
            feedRequest.setAttributes(quantityData.getData());
        } else {
            ListingQuantityData quantityData = ListingQuantityData.of(updateQuantity);
            feedRequest.setAttributes(quantityData.getData());
        }


        RequestFeedsApiParam requestFeedsApiParam = new RequestFeedsApiParam();
        requestFeedsApiParam.setAmazonSpAccount(amazonSpAccount);
        requestFeedsApiParam.setDocId(sellerSku);
        requestFeedsApiParam.setSpFeedType(SpFeedType.JSON_LISTINGS_FEED);
        requestFeedsApiParam.setSystemFeedType(isFulfillmentLatency ? SystemFeedTypeEnum.LISTING_HANDING_TIME_FEED_DOCUMENT.getSystemFeedType() : SystemFeedTypeEnum.LISTING_INVENTORY_FEED_DOCUMENT.getSystemFeedType());
        requestFeedsApiParam.setJsonData(feedRequest.toJSONData());
        return callJsonAddFeedsTask(requestFeedsApiParam);
    }

    public static ApiResult<String> updateJsonListingFeedPrice(AmazonProductListing amazonProductListing) {
        String accountNumber = amazonProductListing.getAccountNumber();
        String sellerSku = amazonProductListing.getSellerSku();
        Double price = amazonProductListing.getPrice();
        Double salePrice = amazonProductListing.getSalePrice();
        Date saleStartDate = amazonProductListing.getSaleStartDate();
        Date saleEndDate = amazonProductListing.getSaleEndDate();

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        AmazonConstantMarketHelper marketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        AmazonMarketplace marketplace = marketHelper.getMarketplaceIdMap().get(account.getMarketplaceId());
        String currencyCode = marketplace != null ? marketplace.getCurrency() : AmazonConstant.DEFAULT_COUNTRY_CURRENCY;

        ListingPriceData priceData = ListingPriceData.of(currencyCode, BigDecimal.valueOf(price), salePrice, saleStartDate, saleEndDate);
        JSONListingFeedRequest feedRequest = new JSONListingFeedRequest();
        feedRequest.setOperationType("PARTIAL_UPDATE");
        feedRequest.setProductType("PRODUCT");
        feedRequest.setSku(sellerSku);
        feedRequest.setAttributes(priceData.getData());


        RequestFeedsApiParam requestFeedsApiParam = new RequestFeedsApiParam();
        requestFeedsApiParam.setAmazonSpAccount(amazonSpAccount);
        requestFeedsApiParam.setDocId(sellerSku);
        requestFeedsApiParam.setSpFeedType(SpFeedType.JSON_LISTINGS_FEED);
        requestFeedsApiParam.setSystemFeedType(SystemFeedTypeEnum.LISTING_PRICE_FEED_DOCUMENT.getSystemFeedType());
        requestFeedsApiParam.setJsonData(feedRequest.toJSONData());
        return callJsonAddFeedsTask(requestFeedsApiParam);
    }

    /**
     * 更新Listing图片
     *
     * @param amazonProductListing listing
     * @param imageMapping         图片映射
     * @return ApiResult
     */
    public static ApiResult<String> updateJsonListingFeedImages(AmazonProductListing amazonProductListing, Map<String, String> imageMapping) {
        String accountNumber = amazonProductListing.getAccountNumber();
        String sellerSku = amazonProductListing.getSellerSku();

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);

        ListingImageData listingImageData = ListingImageData.of(amazonProductListing, imageMapping);

        JSONListingFeedRequest patchRequest = new JSONListingFeedRequest();
        patchRequest.setOperationType("PARTIAL_UPDATE");
        patchRequest.setProductType("PRODUCT");
        patchRequest.setSku(sellerSku);
        patchRequest.setAttributes(listingImageData.getData());

        RequestFeedsApiParam requestFeedsApiParam = new RequestFeedsApiParam();
        requestFeedsApiParam.setAmazonSpAccount(amazonSpAccount);
        requestFeedsApiParam.setDocId(sellerSku);
        requestFeedsApiParam.setSpFeedType(SpFeedType.JSON_LISTINGS_FEED);
        requestFeedsApiParam.setSystemFeedType(SystemFeedTypeEnum.LISTING_IMAGE_FEED_DOCUMENT.getSystemFeedType());
        requestFeedsApiParam.setJsonData(patchRequest.toJSONData());
        return callJsonAddFeedsTask(requestFeedsApiParam);
    }


    /**
     * JSON FEED 模版批量刊登
     */
    public static ApiResult<String> publishTemplateJsonFeed(List<ListingProductData> productDataList) {
        if (CollectionUtils.isEmpty(productDataList)) {
            return ApiResult.newError("刊登数据为空");
        }
        ListingProductData baseProductData = productDataList.get(0);
        String accountNumber = baseProductData.getAccountNumber();
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);

        RequestFeedsApiParam requestFeedsApiParam = new RequestFeedsApiParam();
        requestFeedsApiParam.setAmazonSpAccount(amazonSpAccount);
        requestFeedsApiParam.setSpFeedType(SpFeedType.JSON_LISTINGS_FEED);
        requestFeedsApiParam.setSystemFeedType(SystemFeedTypeEnum.TEMPLATE_PUBLISH_FEED_DOCUMENT.getSystemFeedType());
        Map<String, String> sellerskuAndJsonMessageMap = new HashMap<>();
        for (ListingProductData productData : productDataList) {
            JSONListingFeedRequest request = new JSONListingFeedRequest();
            request.setSku(productData.getSellerSku());
            request.setOperationType("UPDATE");
            request.setProductType(productData.getProductType());
            request.setRequirements(productData.getRequirements());
            request.setAttributes(productData.getData());
            sellerskuAndJsonMessageMap.put(productData.getSellerSku(), request.toJSONData());
        }
        requestFeedsApiParam.setSellerskuAndJsonMessageMap(sellerskuAndJsonMessageMap);
        return callJsonAddFeedsTask(requestFeedsApiParam);
    }

    public static ApiResult<String> updateJsonListingFeedTitle(AmazonProductListing amazonProductListing) {
        String accountNumber = amazonProductListing.getAccountNumber();
        String sellerSku = amazonProductListing.getSellerSku();
        String title = amazonProductListing.getItemName();
        String site = amazonProductListing.getSite();
        AmazonMarketplaceEnums marketplaceEnums = AmazonMarketplaceEnums.fromSiteName(site);

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        JSONListingFeedRequest patchRequest = new JSONListingFeedRequest();
        patchRequest.setOperationType("PARTIAL_UPDATE");
        patchRequest.setProductType("PRODUCT");
        patchRequest.setSku(sellerSku);
        Map<String, Object> nameAttribute = AmazonListingApiUtil.buildNestedArrayStructure(ListingFiledConstants.ITEM_NAME,
                Pair.of(ListingFiledConstants.LANGUAGE_TAG, marketplaceEnums.getLocale()),
                Pair.of(ListingFiledConstants.VALUE, title)
        );
        patchRequest.setAttributes(nameAttribute);

        RequestFeedsApiParam requestFeedsApiParam = new RequestFeedsApiParam();
        requestFeedsApiParam.setAmazonSpAccount(amazonSpAccount);
        requestFeedsApiParam.setDocId(sellerSku);
        requestFeedsApiParam.setSpFeedType(SpFeedType.JSON_LISTINGS_FEED);
        requestFeedsApiParam.setSystemFeedType(SystemFeedTypeEnum.LISTING_PRODUCT_TITLE_FEED_DOCUMENT.getSystemFeedType());
        requestFeedsApiParam.setJsonData(patchRequest.toJSONData());
        return callJsonAddFeedsTask(requestFeedsApiParam);
    }

    private static ApiResult<String> callJsonAddFeedsTask(RequestFeedsApiParam requestFeedsApiParam) {
//        return EnvironmentSupplierWrapper.execute(List.of("dev", "local"),
//                () -> {
//                    try {
//                        log.info("call sp local /feeds/jsonAddFeedsTask, req:{}", JSON.toJSONString(requestFeedsApiParam));
//                        return amazonSpLocalServiceClient.jsonAddFeedsTask(requestFeedsApiParam);
//                    } catch (Exception e) {
//                        log.error("call sp local /feeds/jsonAddFeedsTask fail, req:{}, error:{}", JSON.toJSONString(requestFeedsApiParam), e.getMessage(), e);
//                        return ApiResult.newError("call sp local /feeds/jsonAddFeedsTask fail " + e.getMessage());
//                    }
//                },
//                () -> {
//                    return ApiResult.newError("测试环境不支持调用修改listing接口");
//                });

        try {
            return amazonSpLocalServiceClient.jsonAddFeedsTask(requestFeedsApiParam);
        } catch (Exception e) {
            log.error("call sp local /feeds/jsonAddFeedsTask fail, req:{}, error:{}", JSON.toJSONString(requestFeedsApiParam), e.getMessage(), e);
            return ApiResult.newError("call sp local /feeds/jsonAddFeedsTask fail " + e.getMessage());
        }
    }

}
