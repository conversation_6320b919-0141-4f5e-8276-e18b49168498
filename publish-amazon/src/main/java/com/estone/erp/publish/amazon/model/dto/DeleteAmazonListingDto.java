package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: amazon下架listing
 */
@Data
public class DeleteAmazonListingDto {

    private AmazonOfflineEnums.Type amazonOfflineEnumType;

    private String remarkParam;

    private String userName;

    private Boolean deleteParentAsin = false;

    /**
     * 统一备注
     * 默认使用{@link AmazonOfflineEnums.Type#getDesc()} + getDeleteRemark 中的备注
     * false: 不使用默认备注，在需要下架的listing中先添加备注
     */
    private Boolean useDefaultRemark = true;

    private int quotaExceededRetry = 3;

    public String getDeleteRemark(){
        String deleteRemark = amazonOfflineEnumType.getDesc();
        if (StringUtils.isNotEmpty(getRemarkParam())){
            deleteRemark = amazonOfflineEnumType.getFormatDesc(getRemarkParam());
        }
        return deleteRemark;
    }

}
