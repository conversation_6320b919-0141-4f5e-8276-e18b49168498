package com.estone.erp.publish.tidb.publishAmazon.service.impl;

import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.tidb.publishAmazon.mapper.TidbAmazonProductListingMapper;
import com.estone.erp.publish.tidb.publishAmazon.service.TidbAmazonProductListingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * 只读
 */
@Service("tidbAmazonProductListingService")
@Slf4j
public class TidbAmazonProductListingServiceImpl implements TidbAmazonProductListingService {

    @Resource
    private TidbAmazonProductListingMapper tidbAmazonProductListingMapper;
    @Resource
    private AmazonProductListingService amazonProductListingService;

    @Override
    public List<AmazonProductListing> selectByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "site is null!");
        example.setTableIndex(amazonProductListingService.getTableIndex(site));
        return tidbAmazonProductListingMapper.selectByExample(example);
    }

    /**
     * 查询自定义字段listing
     *
     * @param example
     * @param site
     * @return
     */
    @Override
    public List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "site is null!");
        example.setTableIndex(amazonProductListingService.getTableIndex(site));
        return tidbAmazonProductListingMapper.selectCustomColumnByExample(example);
    }

    /**
     * 查询自定义字段listing
     * @param example 需要设置Table
     * @return
     */
    @Override
    public List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example) {
        Assert.notNull(example, "example is null!");
        return tidbAmazonProductListingMapper.selectCustomColumnByExample(example);
    }

    @Override
    public List<String> selectMerchantShippingGroupByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "site is null!");
        example.setTableIndex(amazonProductListingService.getTableIndex(site));
        return tidbAmazonProductListingMapper.selectMerchantShippingGroupByExample(example);
    }

}