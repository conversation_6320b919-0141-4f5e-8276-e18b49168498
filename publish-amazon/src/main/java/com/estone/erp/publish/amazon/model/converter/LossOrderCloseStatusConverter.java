package com.estone.erp.publish.amazon.model.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.estone.erp.publish.amazon.enums.AmazonLossMarkingOrdersEnum;

/**
 * <AUTHOR>
 * @date 2024-06-14 下午3:20
 */
public class LossOrderCloseStatusConverter implements Converter<Integer> {
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

        AmazonLossMarkingOrdersEnum build = AmazonLossMarkingOrdersEnum.getEnumByCode(value);
        if (build != null) {
            return new WriteCellData<>(build.getDesc());
        }
        return new WriteCellData<>("未知");
    }
}
