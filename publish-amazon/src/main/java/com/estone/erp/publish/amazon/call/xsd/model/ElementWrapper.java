package com.estone.erp.publish.amazon.call.xsd.model;

import com.alibaba.druid.util.StringUtils;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

public class ElementWrapper implements Serializable {
    private static final long serialVersionUID = -1861001744679131584L;
    private String name;

    private Boolean singleValue;

    private Integer occurs;

    private LinkedHashMap<String, ElementWrapper> items;

    private Boolean isLeaf;

    private Boolean selected = false;

    private String route;

    private TypeWrapper type;

    private List<String> values;

    private List<AttributeWrapper> attrs;

    /**
     * 元素是否可以忽略
     */
    private Boolean ignore = false;

    /**
     * 元素是否必需
     */
    private Boolean required = false;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getSingleValue() {
        return singleValue;
    }

    public void setSingleValue(Boolean singleValue) {
        this.singleValue = singleValue;
    }

    public Integer getOccurs() {
        return occurs == null ? 1 : occurs;
    }

    public void setOccurs(Integer occurs) {
        this.occurs = occurs;
    }

    public LinkedHashMap<String, ElementWrapper> getItems() {
        return items;
    }

    public void setItems(LinkedHashMap<String, ElementWrapper> items) {
        this.items = items;
    }

    public ElementWrapper getItemWrapper(String itemName) {
        if (MapUtils.isEmpty(items) || StringUtils.isEmpty(itemName)) {
            return null;
        }

        return items.get(itemName);
    }

    public boolean containsItemWrapper(String itemName) {
        if (MapUtils.isEmpty(items) || StringUtils.isEmpty(itemName)) {
            return false;
        }

        return items.containsKey(itemName);
    }

    public void setItemWrapperValue(String itemName, String value) {
        ElementWrapper itemWrapper = getItemWrapper(itemName);
        if (itemWrapper != null) {
            itemWrapper.setValues(Arrays.asList(value));
        }
    }

    public void setItemWrapperValues(String itemName, List<String> values) {
        ElementWrapper itemWrapper = getItemWrapper(itemName);
        if (itemWrapper != null) {
            itemWrapper.setValues(values);
        }
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public TypeWrapper getType() {
        return type;
    }

    public void setType(TypeWrapper type) {
        this.type = type;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    public List<AttributeWrapper> getAttrs() {
        return attrs;
    }

    public void setAttrs(List<AttributeWrapper> attrs) {
        this.attrs = attrs;
    }

    public Boolean getIgnore() {
        return ignore;
    }

    public void setIgnore(Boolean ignore) {
        this.ignore = ignore;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }
}
