package com.estone.erp.publish.amazon.mq.feed;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.submit.FeedType;
import com.estone.erp.publish.amazon.enums.AmazonSolutionTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.request.ReportSolutionResponseDO;
import com.estone.erp.publish.amazon.service.*;
import com.estone.erp.publish.amazon.util.AmazonReportSolutionUtil;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.rabbitmq.client.Channel;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.response.AmazonFeedsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Feed
 */
@Component @Slf4j
public class SpFeedsResultMqListener implements ChannelAwareMessageListener {

    private static final String ERROR_MSG ="We have temporarily removed your ability to create new ASINS because an unusually high number of ASINs have been created from your account";

    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonTemplateService amazonTemplateService;
    @Resource
    private AmazonTemplateFailLogService amazonTemplateFailLogService;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AmazonProcessReportSolutionTypeService amazonProcessReportSolutionTypeService;

    /**
     * 亚马逊监听提交刊登数据对应的任务
     */
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            String str = new String(message.getBody());
            AmazonFeedsResult feedResult = JSON.parseObject(str, AmazonFeedsResult.class);

            // 处理结果
            AmazonProcessReport amazonProcessReport = new AmazonProcessReport();
            amazonProcessReport.setAccountNumber(feedResult.getAccountNumber());
            amazonProcessReport.setFinishDate(new Date());
            if (StringUtils.isNotEmpty(feedResult.getTaskId())) {
                amazonProcessReport.setTaskId(feedResult.getTaskId());
            }
            amazonProcessReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
            amazonProcessReport.setDataValue(feedResult.getDocId());
            amazonProcessReport.setStatus(feedResult.getStatus());
            if (StringUtils.isNotEmpty(feedResult.getErrorMsg())) {
                amazonProcessReport.setResultMsg(feedResult.getErrorMsg());
                AmazonExecutors.executeQueryTemplate(() -> {
                    // 失败或者警告信息回写模板问题分类
                    updateErrorMsg2Template(feedResult);
                });

                // 如果报错原因是指定信息 将相关店铺设置为10天内不推荐新品
                if (feedResult.getErrorMsg().contains(ERROR_MSG)) {
                    updateReportErrorsAccountUnAssign(amazonProcessReport.getAccountNumber(), amazonProcessReport.getFinishDate());
                }
            }
            amazonProcessReport.setFeedType(feedResult.getFeedType());
            amazonProcessReportService.updateAmazonProcessReportResult(amazonProcessReport);
            String filed = "relation_id";
            if (feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_DATA.getValue()) && StringUtils.isNotEmpty(feedResult.getTaskId())){
                // 查询产品类型上传平台的模板的处理报告信息，回写 绑定关系
                AmazonProcessReportExample example = new AmazonProcessReportExample();
                example.setFiledColumns(filed);
                example.createCriteria()
                        .andAccountNumberEqualTo(feedResult.getAccountNumber())
                        .andTaskIdEqualTo(feedResult.getTaskId())
                        .andFeedTypeEqualTo(FeedType.POST_PRODUCT_DATA)
                        .andRelationTypeEqualTo(ProcessingReportTriggleType.Template.name());
                List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectFiledColumnsByExample(example);
                if (CollectionUtils.isNotEmpty(amazonProcessReportList)){
                    List<Integer> templateIds = amazonProcessReportList.stream().map(AmazonProcessReport::getRelationId).collect(Collectors.toList());
                    rabbitMqSender.publishSyncVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_KEY, templateIds);
                }else {
                    // 成功的处理报告回写 Listing
                    rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_PROCESS_REPORT_LISTING_KEY, JSON.toJSON(feedResult));
                }
            }else if(feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue()) && feedResult.getStatus() && StringUtils.isNotEmpty(feedResult.getTaskId())){
                //  成功的处理报告回写 Listing
                rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_PROCESS_REPORT_LISTING_KEY, JSON.toJSON(feedResult));
            }else if (needUpdateLocalData(feedResult)) {
                // 修改本地Listing数据, 调价、调库存
                rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA_KEY, JSON.toJSON(feedResult));
            }else  if (feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue()) && StringUtils.isNotEmpty(feedResult.getTaskId())) {
                // 关系上传校验,更新asin绑定记录
                rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_PROCESS_REPORT_LISTING_KEY, JSON.toJSON(feedResult));
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }catch (Exception e) {
            log.warn(e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.warn(ioe.getMessage() + "消费成功，通知服务器移除mq时异常，异常信息", ioe);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        }
    }

    /**
     * 处理报告成功是否需要立即更新本地数据，Amazon存在24小时延迟
     * 调价、调库存
     * @param feedsResult 处理报告信息
     * @return 是否更新本地数据
     */
    private boolean needUpdateLocalData(AmazonFeedsResult feedsResult) {
        String feedType = feedsResult.getFeedType();
        if (feedType.equals(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue())) {
            return true;
        }
        if (feedType.equals(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue()) && feedsResult.getStatus()) {
            return true;
        }
        return false;
    }

    private void updateErrorMsg2Template(AmazonFeedsResult feedResult) {
        try {
            // 获取对应处理报告
            AmazonProcessReportExample example = new AmazonProcessReportExample();
            example.setFiledColumns("id,account_number,relation_id,relation_type");
            AmazonProcessReportExample.Criteria criteria = example.createCriteria()
                    .andAccountNumberEqualTo(feedResult.getAccountNumber())
                    .andDataValueEqualTo(feedResult.getDocId());
            if (StringUtils.isNotEmpty(feedResult.getTaskId())) {
                criteria.andTaskIdEqualTo(feedResult.getTaskId());
            }
            List<AmazonProcessReport> processReports = amazonProcessReportService.selectFiledColumnsByExample(example);
            if (CollectionUtils.isEmpty(processReports)) {
                return;
            }
            AmazonProcessReport amazonProcessReport = processReports.get(0);
            if (!ProcessingReportTriggleType.Template.name().equals(amazonProcessReport.getRelationType()) || amazonProcessReport.getRelationId() == null) {
                return;
            }
            Integer templateId = amazonProcessReport.getRelationId();
            // 调用接口匹配错误解决方案
            ApiResult<ReportSolutionResponseDO> apiResult = AmazonReportSolutionUtil.matchReportSolution(List.of(feedResult.getErrorMsg()));
            if (!apiResult.isSuccess()) {
                amazonTemplateFailLogService.addFailLog(templateId, amazonProcessReport.getId(), apiResult.getErrorMsg());
                log.error("调用接口匹配错误解决方案失败：{}", apiResult.getErrorMsg());
                return;
            }

            ReportSolutionResponseDO result = apiResult.getResult();
            List<ReportSolutionResponseDO.SolutionInfoDTO> response = result.getResponse();
            if (CollectionUtils.isEmpty(response)) {
                amazonTemplateFailLogService.addFailLog(templateId, amazonProcessReport.getId(), JSON.toJSONString(apiResult));
                return;
            }

            ReportSolutionResponseDO.SolutionInfoDTO solutionInfoDTO = response.get(0);
            if (solutionInfoDTO.getId() == null) {
                return;
            }
            String solutionType = StringUtils.isBlank(solutionInfoDTO.getSolutionType()) ? AmazonSolutionTypeEnum.CATEGORY_NOT_FOUND.getValue() : solutionInfoDTO.getSolutionType();
            // 判断分类类型，为提交图片与现有图片不符时记录相关信息
            if (solutionType.equals(AmazonSolutionTypeEnum.SUBMIT_IMAGE_NOT_MATCH.getValue())) {
                log.info("亚马逊监听提交刊登数据对应的任务，分类类型为提交图片与现有图片不符-记录流程报告解决方案类型记录表");
                this.saveCategoryInfo(templateId,feedResult);
            }

            // ES-6206 问题分类为分类类型错误，则将模板状态改为失败。
            /*Integer publishStatus = null;
            if (BAD_REPORT_SOLUTION.equals(solutionType)) {
                publishStatus = AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode();
            }*/

            // 写到模板中
            amazonTemplateService.updateTemplateReportSolution(templateId, solutionInfoDTO.getId(), solutionType, null);
        } catch (Exception e) {
            log.error("回写错误信息到模板异常：{}",e.getMessage());
        }

    }

    /**
     * 获取分类信息并记录
     * @param templateId 模板id
     * @param feedResult
     */
    private void saveCategoryInfo(Integer templateId, AmazonFeedsResult feedResult) {
        //log.info("amazon流程报告解决方案类型信息-模板id：{}，店铺id：{}", templateId, feedResult.getAccountNumber());

        // 获取店铺
        String accountNumber = feedResult.getAccountNumber();

        // 获取紫鸟名
        Map<String, String> cacheMap = new HashMap<>();
        String vmUsername = AccountUtils.getVmUserNameByAccount(cacheMap,accountNumber);

        // 获取模板信息
        AmazonTemplateBO amazonTemplateBO = getAmazonTemplateBO(templateId);

        // 创建 AmazonProcessReportSolutionType 对象
        AmazonProcessReportSolutionType amazonProcessReportSolutionType = new AmazonProcessReportSolutionType();
        amazonProcessReportSolutionType.setSolutionType(AmazonSolutionTypeEnum.SUBMIT_IMAGE_NOT_MATCH.getValue());
        amazonProcessReportSolutionType.setCreatedDate(Timestamp.valueOf(LocalDateTime.now()));
        amazonProcessReportSolutionType.setAccountNumber(accountNumber);
        amazonProcessReportSolutionType.setPurpleBirdName(vmUsername);
        amazonProcessReportSolutionType.setParentSku(amazonTemplateBO.getParentSku());
        amazonProcessReportSolutionType.setSellerSku(amazonTemplateBO.getSellerSku());
        amazonProcessReportSolutionType.setStandardProdcutIdType(amazonTemplateBO.getStandardProdcutIdType());
        String skus = amazonTemplateBO.getAmazonSkus().stream().map(AmazonSku::getSku).collect(Collectors.joining(","));
        amazonProcessReportSolutionType.setSku(skus);

        // 查询亚马逊帐号关联信息
        List<AmazonAccountRelation> relationList = getAmazonAccountRelationList(accountNumber);

        // 判空处理
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(relationList)) {
            // 获取销售信息
            String salesmans = getSalesmans(relationList);
            amazonProcessReportSolutionType.setSalesman(salesmans);
        }

        // 插入数据
        //log.info("amazon流程报告解决方案类型信息：{}", JSON.toJSONString(amazonProcessReportSolutionType));
        amazonProcessReportSolutionTypeService.insert(amazonProcessReportSolutionType);
    }

    /**
     * 获取模板信息
     * @param templateId
     * @return
     */
    private AmazonTemplateBO getAmazonTemplateBO(Integer templateId) {
        return amazonTemplateService.selectBoById(templateId, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
    }

    /**
     * 查询亚马逊帐号关联信息
     * @param accountNumber
     * @return
     */
    private List<AmazonAccountRelation> getAmazonAccountRelationList(String accountNumber) {
        AmazonAccountRelationExample example = new AmazonAccountRelationExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber);
        return amazonAccountRelationService.selectByExample(example);
    }

    /**
     * 获取销售信息
     * @param relationList
     * @return
     */
    private String getSalesmans(List<AmazonAccountRelation> relationList) {
        try {
            List<String> accountNumbers = relationList.stream()
                    .map(AmazonAccountRelation::getAccountNumber)
                    .collect(Collectors.toList());
            String requestParam = "{\"accountNumberList\":" + JSON.toJSONString(accountNumbers) + ",\"saleChannel\":\"Amazon\"}";
            Map<String, SalesmanAccountDetail> stringSalesmanAccountDetailMap = AccountUtils.listSalesmanAccountInfo(requestParam);
            return stringSalesmanAccountDetailMap.values().stream()
                    .flatMap(salesmanAccountDetail -> salesmanAccountDetail.getSalesmanSet().stream())
                    .distinct()
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            log.error("获取销售信息失败,失败原因为；{}", e.getMessage());
            throw new RuntimeException("获取销售信息失败");
        }
    }

    private void updateReportErrorsAccountUnAssign(String accountNumber, Date date) {
        try {
            String key = RedisConstant.AMAZON_UN_ASSIGN_ACCOUNT + accountNumber;
            String value = PublishRedisClusterUtils.get(key);
            if (StringUtils.isBlank(value)) {
                Date tenDaysLater = DateUtils.addDays(date, 10);
                long timeout = tenDaysLater.getTime() - System.currentTimeMillis();
                PublishRedisClusterUtils.set(key, accountNumber, timeout, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}