package com.estone.erp.publish.amazon.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 试卖数据生成模板请求
 *
 * <AUTHOR>
 * @date 2025-03-26 9:10
 */
@Data
public class AmazonSPTemRequest {

    /**
     * 试卖ID
     */
    @NotBlank(message = "试卖ID不能为空")
    private String spId;

    /**
     * 主SKU
     */
    @NotBlank(message = "主SKU不能为空")
    private String mainSku;

    /**
     * 店铺
     */
    @NotBlank(message = "店铺不能为空")
    private String accountNumber;
}
