package com.estone.erp.publish.amazon.call.model;

public class Attribute<T, R> {
    private T name;

    private R value;

    public Attribute() {
    }

    public static <K, V> Attribute<K, V> of(K k, V v) {
        return new Attribute<>(k, v);
    }

    public Attribute(T name, R value) {
        super();
        this.name = name;
        this.value = value;
    }

    public T getName() {
        return name;
    }

    public void setName(T name) {
        this.name = name;
    }

    public R getValue() {
        return value;
    }

    public void setValue(R value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return String.valueOf(name) + ": " + String.valueOf(value);
    }
}
