package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.publish.amazon.model.request.GatewayProxyTotal;
import com.estone.erp.publish.common.util.HttpUtils;
import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-08-02 16:54
 */
@Slf4j
public class AmazonGatewayProxyUtil extends AbstractHttpClient{


    public static ApiResult<GatewayProxyTotal> matchReportSolutionHttp(String code,Date startTime,Date endTime) {
        try {
            // 基础URL
            String baseUrl = "http://172.16.8.8:9090/api/v1/query";
            // 默认构建查询参数
            String queryParam = "sort_desc(increase(response_url_counter_timer_seconds_count{code=\"403\"}[24h]))";
            if (StringUtils.isNotBlank(code)) {
                queryParam = "sort_desc(increase(response_url_counter_timer_seconds_count{code=\"" + code +"\"}[24h]))";
            }

            // 构建完整URL
            String fullUrl = baseUrl + "?query=" + java.net.URLEncoder.encode(queryParam, "UTF-8");
          /*  if (StringUtils.isNotBlank(code)) {
                String query403 = "response_url_counter_timer_seconds_count{code=\"" + code + "\"}";
                fullUrl =  baseUrl + "?query=" + java.net.URLEncoder.encode(query403, "UTF-8");
                if (startTime != null && endTime != null) {
                    fullUrl += "&start=" + java.net.URLEncoder.encode(DateUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"), "UTF-8");
                    fullUrl += "&end=" + java.net.URLEncoder.encode(DateUtils.format(endTime, "yyyy-MM-dd HH:mm:ss"), "UTF-8");
                }
                //String query403 = "sort_desc(increase(response_url_counter_timer_seconds_count{code='" + code + "'}[24h]))";
                //fullUrl =  baseUrl + "?query=" + java.net.URLEncoder.encode(query403, "UTF-8");
            }*/

            log.info("准备发送请求到URL: {}", fullUrl);

            // 创建OkHttpClient
            OkHttpClient client = new OkHttpClient();
            client.setConnectTimeout(60, TimeUnit.SECONDS);
            client.setReadTimeout(120, TimeUnit.SECONDS);
            client.setWriteTimeout(10, TimeUnit.SECONDS);

            // 创建请求
            Request request = new Request.Builder()
                    .url(fullUrl)
                    .get()
                    .build();

            Response response = client.newCall(request).execute();
            GatewayProxyTotal gatewayProxyTotal =   JSONObject.parseObject(response.body().string(),GatewayProxyTotal.class);
           return ApiResult.newSuccess(gatewayProxyTotal);
        } catch (Exception e) {
            log.error("请求异常：", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 存在 转码异常
     * @return
     */
    public static ApiResult<String> testmatchReportSolution() {
        try {
            String query = "sort_desc(increase(response_url_counter_timer_seconds_count{code=\"403\"}[24h]))";
            String baseUrl = "http://192.168.8.193:8082/api/v1/query";

            // 手动编码查询参数
            String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8.toString());
            String url = baseUrl + "?query=" + encodedQuery;

            HttpParams<Map<String, Object>> httpParams = new HttpParams<>();
            httpParams.setUrl(url);
            httpParams.setHttpMethod(HttpMethod.GET);

            // 设置 Headers
            httpParams.addHeader("Accept", "application/json");
            //httpParams.addHeader("Accept", "application/json");

            String gatewayProxyTotal = HttpUtils.exchange(httpParams, String.class);
            return ApiResult.newSuccess(gatewayProxyTotal);
        } catch (Exception e) {
            log.error("请求异常：" + e.getMessage(), e);
            return ApiResult.newError("请求失败: " + e.getMessage());
        }
    }

}
