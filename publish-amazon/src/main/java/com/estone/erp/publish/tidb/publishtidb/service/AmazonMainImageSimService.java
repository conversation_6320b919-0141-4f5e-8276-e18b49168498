package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.amazon.model.AmazonAccountRelation;

import java.util.List;

/**
 * 图片相似度处理
 */
public interface AmazonMainImageSimService {

    /**
     * 图片相似度
     *
     * @param full          全量是否
     * @param accountNumber 账号
     * @param skuList       skuList
     * @param startDateStr  开始时间
     * @param endDateStr    结束时间
     */
    void doImageSim(boolean full, String accountNumber, List<String> skuList, String startDateStr, String endDateStr, boolean isSlow);

    /**
     * 图片相似度 相似度为空的 算法错误导致
     *
     * @param accountRelation
     */
    void doErrorSim(AmazonAccountRelation accountRelation);

}
