package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.erpDas.ErpDasUtils;
import com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 添加成功模板绑定sku关系到es
 */
@Slf4j
@Component
public class AddTemplateBindSkuToEsMqListener {
    @Resource
    private AmazonTemplateService amazonTemplateService;
    @Resource
    private EsSkuBindService esSkuBindService;

    @RabbitListener(queues = PublishQueues.AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_QUEUE, containerFactory = "vhPublishSyncContainerFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            return;
        }
        //log.warn("绑定sku更新id集合 body: {}", body);
        try {
            List<Integer> templateIds = JSON.parseObject(body,  List.class);
            syncSkuBindRelationToEs(templateIds);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("绑定sku更新 Exception: {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void syncSkuBindRelationToEs(List<Integer> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return;
        }
        String table = AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode();
        String columns = "id,seller_id,variations,parent_sku,seller_sku,sale_variant,sku_data_source";
        AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample(table);
        amazonTemplateExample.createCriteria().andIdIn(templateIds);
        amazonTemplateExample.setColumns(columns);
        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateService.selectFiledColumnsByExample(amazonTemplateExample);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return;
        }
        for (AmazonTemplateBO amazonTemplateBO : amazonTemplateBOS) {
            // 若是变体，则初始化变体sellerSKU
            Map<String,String> sku2SellerSkuMap = new HashMap<>();
            Map<String, Integer> skuDadaSourceMap = new HashMap<>();
            String parentSku = amazonTemplateBO.getParentSku();
            Integer skuDadaSource = amazonTemplateBO.getSkuDataSource();
            skuDadaSourceMap.put(parentSku, skuDadaSource);
            sku2SellerSkuMap.put(parentSku, amazonTemplateBO.getSellerSKU());
            if (BooleanUtils.toBoolean(amazonTemplateBO.getSaleVariant())) {
                List<AmazonSku> amazonSkus = amazonTemplateBO.getAmazonSkus();
                if (CollectionUtils.isNotEmpty(amazonSkus)) {
                    for (AmazonSku amazonSku : amazonSkus) {
                        String variantSellerSKU = amazonSku.getSellerSKU();
                        String sku = amazonSku.getSku();
                        sku2SellerSkuMap.put(sku,variantSellerSKU);
                        skuDadaSourceMap.put(sku,skuDadaSource);
                    }
                }
            }

            EsSkuBind skuBind = new EsSkuBind();
            skuBind.setPlatform(Platform.Amazon.name());
            skuBind.setSellerId(amazonTemplateBO.getSellerId());
            skuBind.setMainSku(parentSku);
            esSkuBindService.batchBindSkus(skuBind, sku2SellerSkuMap,skuDadaSourceMap);
            // 试卖推送
            if (SkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(skuDadaSource)) {
                // 试卖sku数据对象
                SpProductSaleMsg spProductSaleMsg = new SpProductSaleMsg();
                spProductSaleMsg.setOriginPlatform(skuBind.getPlatform());
                spProductSaleMsg.setMainSku(skuBind.getMainSku());
                List<String> skus = skuDadaSourceMap.keySet().stream().filter(sku -> {
                    if (AmazonTemplateUtils.isSaleVariant(amazonTemplateBO)) {
                        return !sku.equals(skuBind.getMainSku());
                    }
                    return true;
                }).collect(Collectors.toList());
                spProductSaleMsg.setSonSkus(JSON.toJSONString(skus));
                ErpDasUtils.pushSpSkuToDas(spProductSaleMsg);
            }
        }
    }

}
