package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/25 17:51
 * @description 关键词工具类
 */
@Slf4j
public class KeyWordUtils {

    /**
     * 分类模板名称
     */
    private static final List<String> TEMPLATE_NAME_LIST =
            Lists.newArrayList("Flat.File.Clothing.jp", "Flat.File.Jewelry.jp");

    /**
     * 根据站点和模板名称获取该站点关键词最大长度
     * @param site
     * @param templateName
     * @return
     */
    public static int getKeyWordMaxLengthBySite(String site, String templateName) {
        int maxCount;
        if ("JP".equals(site)) {
            if (TEMPLATE_NAME_LIST.contains(templateName)) {
                maxCount = 250;
            } else {
                maxCount = 500;
            }
        }
        else if ("IN".equals(site)) {
            maxCount = 200;
        }
//        else if ("FR,DE,IT,ES,UK".contains(site)) {
//            maxCount = 230;
//        }
//        else if("US".equals(site)){
//            maxCount = 240;
//        }
        else if ("FR,DE,IT,ES,UK,US,CA".contains(site)) {
            maxCount = 249;
        }
        else {
            //默认250
            maxCount = 250;
        }
        return maxCount;
    }

    /**
     * 验证模板关键词并更新
     * @param template
     */
    public static void checkAndUpdateKeyWord(AmazonTemplateWithBLOBs template) throws UnsupportedEncodingException {
        //不处理范本
        if(BooleanUtils.isTrue(template.getIsLock())){
            return;
        }
        String searchTerms = template.getSearchTerms();
        if(StringUtils.isBlank(searchTerms)){
            return;
        }

        int maxByteLength = getKeyWordMaxLengthBySite(template.getCountry(), template.getCategoryTemplateName());

        if(BooleanUtils.isTrue(template.getSaleVariant())){
            //变体
            JSONObject json = JSON.parseObject(searchTerms);
            for (String skuKey : json.keySet()) {
                if(StringUtils.isBlank(json.getString(skuKey))){
                    continue;
                }

                List<String> list = JSON.parseArray(json.getString(skuKey), String.class);
                int length = list.stream()
                        .filter(StringUtils::isNotBlank)
                        .mapToInt(String::length)
                        .sum();
                if(length > maxByteLength){
                    Object keyWord = packagingKeyWord(template.getCountry(), false, template.getCategoryTemplateName(), null, list);
                    json.put(skuKey, keyWord);
                }
            }
            template.setSearchTerms(json.toString());
        }
        else{
            //单体
            List<String> list = JSON.parseArray(searchTerms, String.class);
            int length = list.stream()
                    .filter(StringUtils::isNotBlank)
                    .mapToInt(String::length)
                    .sum();
            if(length > maxByteLength){
                Object keyWord = packagingKeyWord(template.getCountry(), false, template.getCategoryTemplateName(), null, list);
                template.setSearchTerms(JSON.toJSONString(keyWord));
            }
        }
    }

    /**
     * 验证模板关键词并更新
     * @param template
     */
    public static void checkAndUpdateKeyWord(AmazonTemplateBO template) throws UnsupportedEncodingException {
        //不处理范本
        if(BooleanUtils.isTrue(template.getIsLock())){
            return;
        }
        String searchTerms = template.getSearchTerms();
        if(StringUtils.isBlank(searchTerms)){
            return;
        }

        int maxByteLength = getKeyWordMaxLengthBySite(template.getCountry(), template.getCategoryTemplateName());

        if(BooleanUtils.isTrue(template.getSaleVariant())){
            //变体
            JSONObject json = JSON.parseObject(searchTerms);
            for (String skuKey : json.keySet()) {
                if(StringUtils.isBlank(json.getString(skuKey))){
                    continue;
                }

                List<String> list = JSON.parseArray(json.getString(skuKey), String.class);
                int byteLength = list.stream()
                        .filter(StringUtils::isNotBlank)
                        .mapToInt(String::length)
                        .sum();
                if(byteLength > maxByteLength){
                    Object keyWord = packagingKeyWord(template.getCountry(), false, template.getCategoryTemplateName(), null, list);
                    json.put(skuKey, keyWord);
                }
            }
            template.setSearchTerms(json.toString());
        }
        else{
            //单体
            List<String> list = JSON.parseArray(searchTerms, String.class);
            int byteLength = list.stream()
                    .filter(StringUtils::isNotBlank)
                    .mapToInt(String::length)
                    .sum();
            if(byteLength > maxByteLength){
                Object keyWord = packagingKeyWord(template.getCountry(), false, template.getCategoryTemplateName(), null, list);
                template.setSearchTerms(JSON.toJSONString(keyWord));
            }
        }
    }

    /**
     * 组成关键词
     * @param template
     * @return
     */
    public static Object packagingKeyWord(AmazonTemplateWithBLOBs template) throws UnsupportedEncodingException {
        List<String> skus = new ArrayList<>();
        if (template.getSaleVariant()) {
            String variations = template.getVariations();
            JSONArray jsonArray = JSON.parseArray(variations);
            for (int j = 0; j < jsonArray.size(); j++) {
                String sku = jsonArray.getJSONObject(j).getString("sku");
                skus.add(sku);
            }
        }

        String[] splits = template.getSearchData().split(",");
        List<String> searchDatas = new ArrayList<>();
        for (String split : splits) {
            if (!split.contains(":")) {
                searchDatas.add(split);
            }
        }

        return packagingKeyWord(template.getCountry(), template.getSaleVariant(), template.getCategoryTemplateName(), skus, searchDatas);
    }

    /**
     * 组成关键词
     * @param country
     * @param isVariant
     * @param templateName
     * @param skus
     * @param searchDatas
     * @return
     */
    public static Object packagingKeyWord(String country, Boolean isVariant, String templateName, List<String> skus, List<String> searchDatas) throws UnsupportedEncodingException {
        //最大字节长度
        int maxByteLength = getKeyWordMaxLengthBySite(country, templateName);
        if(BooleanUtils.isTrue(isVariant)){
            //变体
            if(CollectionUtils.isEmpty(skus)){
                return new HashMap<>(0);
            }
            JSONObject json = new JSONObject();
            for (String sku : skus) {
                String result = generateStr(searchDatas, maxByteLength);
                json.put(sku, Arrays.asList(result));
            }
            return json;
        } else{
            //单体
            String result = generateStr(searchDatas, maxByteLength);

            return Arrays.asList(result);
        }
    }

    public static String generateStr(List<String> searchDatas, int maxByteLength) throws UnsupportedEncodingException {
        if(CollectionUtils.isEmpty(searchDatas)){
            return "";
        }

        Collections.shuffle(searchDatas);
        int count;
        String result = "";
        for (String sd : searchDatas) {
            if(StringUtils.isBlank(sd)){
                continue;
            }

            count = (result.trim() + " " + sd.trim()).trim().length();
            if (count < maxByteLength) {
                result = (result.trim() + " " + sd.trim()).trim();
            }else if (searchDatas.size() == 1) {
                result = handleStringSpaceSplit(sd, maxByteLength);
            }
        }
        return result.trim();
    }

    //按空格拆分
    private static String handleStringSpaceSplit(String str,int maxCount) {
        str = str.trim();
        int length = str.length();
        while(length > maxCount) {
            int index = str.trim().lastIndexOf(" ");
            if(index != -1){
                str = str.substring(0, index);
            }else{
                str = str.substring(0, maxCount);
            }
            length = str.length();
        }
        return str;
    }

    public static int StringCount(String str,String substr) {
        int count = 0;
        int strLen = str.length();
        int substrLen = substr.length();
        if (!str.contains(substr))
            return count;
        for (int i = 0; i < strLen - substrLen; i++) {
            if (substr.equals(str.substring(i, i + substrLen))) {
                count++;
            }
        }
        return count;
    }

    public static void main(String[] args) {

    }

    public static String subStringByte(String source, int length) {

        StringBuilder buffer = new StringBuilder();
        char[] chars = source.toCharArray();
        char c;
        int j = source.getBytes(StandardCharsets.UTF_8).length;
        if (j < length) {
            return source;
        }
        for (int i = 0;i<j; i++) {
            try {
                if (length <= 0) {
                    break;
                }
                c = source.charAt(i);
                buffer.append(c);
                length -= String.valueOf(c).getBytes().length;
                if (i + 1 < chars.length) {
                    if (String.valueOf(chars[i + 1]).getBytes().length > length) {
                        break;
                    }
                    ;
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        }
        return buffer.toString();
    }

    /**
     * 截取指定长度字符串
     * @param text       字符文本
     * @param maxLength   长度
     */
    public static String truncateText(String text, int maxLength) {
        if (text.length() <= maxLength) {
            return text;
        } else {
            return text.substring(0, maxLength);
        }
    }

}
