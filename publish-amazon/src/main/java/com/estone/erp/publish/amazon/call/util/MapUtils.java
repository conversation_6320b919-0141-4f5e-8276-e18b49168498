package com.estone.erp.publish.amazon.call.util;

import java.util.Map;
import java.util.function.Supplier;

/**
 * 
 * @Description: map工具类
 * 
 * @ClassName: MapUtils
 * @Author: Kevin
 * @Date: 2018/09/13
 * @Version: 0.0.1
 */
public class MapUtils {

    /**
     * 
     * @Description: 线程安全的 putIfAbsent方法, 仅针对线程安全的map
     * 
     * @param map map
     * @param key key
     * @param supplier value constructor
     * @return value
     * @Author: Kevin
     * @Date: 2018/09/13
     * @Version: 0.0.1
     */
    public static <K, V> V putIfAbsent(Map<K, V> map, K key, Supplier<V> supplier) {
        V v = map.get(key);
        if (v == null) {
            V value = supplier.get();
            v = map.putIfAbsent(key, value);
            if (v == null) {
                v = value;
            }
        }

        return v;
    }
}
