package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * Amazon链接管理配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface AmazonLinkManagementConfigService extends IService<AmazonLinkManagementConfig> {
    /**
     * 分页查询
     * 根据需求文档4.查询功能与说明实现条件查询
     * 
     * @param query CQuery对象，包含分页信息和查询条件
     * @return 分页查询结果
     */
    CQueryResult<AmazonLinkManagementConfig> queryPage(CQuery<AmazonLinkManagementConfig> query);
}
