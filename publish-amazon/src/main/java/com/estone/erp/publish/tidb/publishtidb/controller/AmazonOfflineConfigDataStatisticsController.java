package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.*;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigDataStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * Amazon 下架数据统计 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Slf4j
@RestController
@RequestMapping("/amazonOfflineConfigDataStatistics")
public class AmazonOfflineConfigDataStatisticsController {

    @Resource
    private AmazonOfflineConfigDataStatisticsService amazonOfflineConfigDataStatisticsService;


    /**
     * 分页查询
     */
    @PostMapping("/search")
    public ApiResult<IPage<AmazonOfflineConfigDataStatisticsVO>> search(@RequestBody AmazonOfflineConfigDataStatisticsSearchDTO searchParam) {
        IPage<AmazonOfflineConfigDataStatisticsVO> search = null;
        try {
            search = amazonOfflineConfigDataStatisticsService.search(searchParam);
        } catch (Exception e) {
            log.error("分页查询失败", e);
            throw new RuntimeException(e);
        }
        return ApiResult.newSuccess(search);
    }

    /**
     * 下载总表数据
     */
    @PostMapping("/downloadCount")
    public ApiResult<String> downloadCount(@RequestBody AmazonOfflineConfigDataStatisticsSearchDTO searchDTO) {
        String msg = null;
        try {
            msg = amazonOfflineConfigDataStatisticsService.downloadCount(searchDTO);
        } catch (Exception e) {
            log.error("下载总表数据失败", e);
            throw new BusinessException("下载总表数据失败:"+e.getMessage());
        }
        return ApiResult.newSuccess(msg);
    }

    /**
     * 配置测试店铺
     */
    @PostMapping("/configTestAccount")
    public ApiResult<AmazonOfflineConfigDataStatisticsUpdateTestAccountVO> configTestAccount(@RequestBody AmazonOfflineConfigDataStatisticsUpdateTestAccountDTO dto) {
        AmazonOfflineConfigDataStatisticsUpdateTestAccountVO vo;
        try {
            vo = amazonOfflineConfigDataStatisticsService.configTestAccount(dto);
        } catch (Exception e) {
            log.error("配置测试店铺失败", e);
            throw new BusinessException("配置测试店铺失败"+ e.getMessage());
        }
        return ApiResult.newSuccess(vo);
    }

    /**
     * 下载测试店铺数据
     */
    @PostMapping("/downloadTestAccount")
    public ApiResult<String> downloadTestAccount(@RequestBody AmazonOfflineConfigDataStatisticsSearchDTO searchDTO) {
        String msg = null;
        try {
            msg = amazonOfflineConfigDataStatisticsService.downloadTestAccount(searchDTO);
        } catch (Exception e) {
            log.error("导出失败", e);
            throw new BusinessException("导出失败:" + e.getMessage());
        }
        return ApiResult.newSuccess(msg);
    }

    /**
     * 确认操作
     */
    @PostMapping("/confirm/status")
    public ApiResult<String> confirm(@RequestBody AmazonOfflineConfigDataStatisticsConfirmDTO dto) {
        try {
            return amazonOfflineConfigDataStatisticsService.confirmStatus(dto);
        } catch (Exception e) {
            return ApiResult.newError("操作失败！");
        }
    }
}
