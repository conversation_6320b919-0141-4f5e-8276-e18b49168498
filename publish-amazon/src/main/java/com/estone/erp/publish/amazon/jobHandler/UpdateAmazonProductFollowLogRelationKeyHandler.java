package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.model.AmazonProductFollowSellLog;
import com.estone.erp.publish.amazon.model.AmazonProductFollowSellLogExample;
import com.estone.erp.publish.amazon.model.AmazonVariantExample;
import com.estone.erp.publish.amazon.service.AmazonProductFollowSellLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/01/05 18:44
 * @description 修改amazon产品跟卖日志的关联key为原数据 账号_平台Sku (已废弃)
 */
@Slf4j
@Component
public class UpdateAmazonProductFollowLogRelationKeyHandler extends AbstractJobHandler {

    @Resource
    private AmazonProductFollowSellLogService amazonProductFollowSellLogService;
    /*@Resource
    private AmazonVariantService amazonVariantService;*/

    public UpdateAmazonProductFollowLogRelationKeyHandler() {
        super(UpdateAmazonProductFollowLogRelationKeyHandler.class.getName());
    }

    @Override
    @XxlJob("UpdateAmazonProductFollowLogRelationKeyHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("修改amazon产品跟卖日志的关联key为原数据 账号_平台Sku start");
        AmazonProductFollowSellLogExample example = new AmazonProductFollowSellLogExample();
        example.setOrderByClause(" id desc");
        List<AmazonProductFollowSellLog> amazonProductFollowSellLogList = amazonProductFollowSellLogService.selectByExample(example);
        List<Integer> variantIdList = new ArrayList<>();
        for (AmazonProductFollowSellLog amazonProductFollowSellLog : amazonProductFollowSellLogList ){
            try {
                Integer variantId = Integer.valueOf(amazonProductFollowSellLog.getRelationKey());
                variantIdList.add(variantId);
            }catch (Exception e){
               log.error("==========" + amazonProductFollowSellLog.getRelationKey() + " 转换出错====");
            }
        }
        if (CollectionUtils.isEmpty(variantIdList)){
            return ReturnT.SUCCESS;
        }
        amazonProductFollowSellLogList.clear();
        variantIdList = variantIdList.stream().distinct().collect(Collectors.toList());
        AmazonVariantExample variantExample = new AmazonVariantExample();
        AmazonVariantExample.Criteria criteria = variantExample.createCriteria();
        criteria.andIdIn(variantIdList);
        //List<AmazonVariant> amazonVariantList = amazonVariantService.findByExample(variantExample);
        /*for (AmazonVariant amazonVariant :amazonVariantList){
            try {
                AmazonProductFollowSellLog amazonProductFollowSellLog = new AmazonProductFollowSellLog();
                amazonProductFollowSellLog.setRelationKey(amazonVariant.getId().toString());
                // 一次性任务更改关联key
                amazonProductFollowSellLog.setAccountNumber(amazonVariant.getAccountNumber() + "_" + amazonVariant.getSellerSku());
                amazonProductFollowSellLogService.updateRelationKey(amazonProductFollowSellLog);
            }catch (Exception e){
                log.error(e.getMessage() +"=============" + amazonVariant.getId() );
            }
        }*/
        log.info("修改amazon产品跟卖日志的关联key为原数据 账号_平台Sku end");
        return ReturnT.SUCCESS;
    }


}
