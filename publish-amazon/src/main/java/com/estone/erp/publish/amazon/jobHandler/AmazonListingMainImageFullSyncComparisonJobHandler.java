package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.mq.model.AmazonMainImageSyncComparisonData;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMainImageComparisonLogService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Amazon在线主图对比
 * 首次全量跑能匹配到sku的全部在线链接数据
 */
@Component
public class AmazonListingMainImageFullSyncComparisonJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private AmazonMainImageComparisonLogService amazonMainImageComparisonLogService;

    public AmazonListingMainImageFullSyncComparisonJobHandler() {
        super("amazonListingMainImageFullSyncComparisonJobHandler");
    }

    @Data
    public static class InnerParam {

        private List<String> accountNumberList;

        private List<String> skuList;

        private String endTime;

        private String checkTime = "2024-03-15 00:00:00";
    }

    @XxlJob("amazonListingMainImageFullSyncComparisonJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        String endTime = innerParam.getEndTime();
        if (StringUtils.isNotBlank(endTime)) {
            Date date = DateUtils.parseDate(endTime, "yyyy-MM-dd HH:mm:ss");
            endTime = com.estone.erp.publish.common.util.DateUtils.dateToString(date, "yyyy-MM-dd HH:mm:ss");
        } else {
            // 加上时间，避免数据错误
            endTime = com.estone.erp.publish.common.util.DateUtils.dateToString(com.estone.erp.publish.common.util.DateUtils.getDateEnd(0), "yyyy-MM-dd HH:mm:ss");
        }

        // 传参 同步正常账号
        AmazonAccountRelationExample relationExample = new AmazonAccountRelationExample();
        String columns = "id, account_number, account_country, account_level";
        relationExample.setFiledColumns(columns);
        AmazonAccountRelationExample.Criteria criteria = relationExample.createCriteria()
                .andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode())
                .andAccountNumberIsNotNull();
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            criteria.andAccountNumberIn(innerParam.getAccountNumberList());
        }
        List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(relationExample);
        if (CollectionUtils.isEmpty(relations)) {
            XxlJobLogger.log("无店铺数据");
            return ReturnT.SUCCESS;
        }

        List<AmazonAccountRelation> collect = relations.stream().peek(a -> {
            String accountLevel = a.getAccountLevel();
            if (StringUtils.isBlank(accountLevel)) {
                a.setAccountLevel("Z");
            }
        }).sorted(Comparator.comparing(AmazonAccountRelation::getAccountLevel)).collect(Collectors.toList());

        List<String> skuList = innerParam.getSkuList();
        String checkTime = innerParam.getCheckTime();
        if (StringUtils.isBlank(checkTime)) {
            checkTime = "2024-03-15 00:00:00";
        }
        // 查询
        for (AmazonAccountRelation relation : collect) {
            String accountNumber = relation.getAccountNumber();
            String accountCountry = relation.getAccountCountry();
            if (ifRun(accountNumber, accountCountry, checkTime)) {
                send(accountNumber, accountCountry, skuList, endTime);
            } else {
                XxlJobLogger.log("accountNumber:{},已经执行过", accountNumber);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void send(String accountNumber, String accountCountry, List<String> skuList, String endTime) {
        AmazonMainImageSyncComparisonData data = new AmazonMainImageSyncComparisonData();
        data.setFull(true);
        data.setAccountNumber(accountNumber);
        data.setSkuList(skuList);
        data.setEndDate(endTime);
        data.setAccountCountry(accountCountry);
        XxlJobLogger.log("发送数据到队列：AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE，data: {}", JSON.toJSONString(data));
        String msgId = PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE + ":" + UUID.randomUUID();
        rabbitTemplate.convertAndSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_KEY, data, (message) -> {
            message.getMessageProperties().setHeader("msg-id", msgId);
            return message;
        });
    }

    public boolean ifRun(String account, String site, String checkTime) {
//        Boolean exist = amazonMainImageComparisonLogService.existFullAccountLog(account, site, checkTime);
        return true;
    }

}
