package com.estone.erp.publish.publishAmazon.model;

import com.estone.erp.publish.base.pms.enums.CountryEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class AmazonProductListing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_product_listing.id
     */
    private Long id;

    /**
     * 账号 database column amazon_product_listing.accountNumber
     */
    private String accountNumber;

    /**
     * 站点 database column amazon_product_listing.site
     */
    private String site;

    /**
     * 父asin码 database column amazon_product_listing.parentAsin
     */
    private String parentAsin;

    /**
     * 子asin码 database column amazon_product_listing.sonAsin
     */
    private String sonAsin;

    /**
     * 卖家sku database column amazon_product_listing.sellerSku
     */
    private String sellerSku;

    /**
     * 主SKU database column amazon_product_listing.mainSku
     */
    private String mainSku;

    /**
     * 货号 database column amazon_product_listing.articleNumber
     */
    private String articleNumber;

    /**
     * SKU数据来源(1.产品系统;2.数据分析系统(试卖);3.冠通;4.探雅) database column amazon_product_listing.skuDataSource
     *   (1,"产品系统")
     *     (2,"数据分析系统")
     *     (3,"冠通产品")
     *     (4,"探雅科技")
     *     (5,"组合产品")
     */
    private Integer skuDataSource;

    /**
     * item状态 (Inactive:不可售 Incomplete:内容不完整 Active:在售)
     */
    private String itemStatus;

    /**
     * 在售 true下架 false database column amazon_product_listing.isOnline
     */
    private Boolean isOnline;

    /**
     * 标题（主产品标题） database column amazon_product_listing.name
     */
    private String name;

    /**
     * 名称（子产品标题） database column amazon_product_listing.itemName
     */
    private String itemName;

    /**
     * 描述 database column amazon_product_listing.itemDescription
     */
    private String itemDescription;

    /**
     * 标题和描述包含的侵权词 database column amazon_product_listing.infringementWord
     */
    private String infringementWord;

    /**
     * 禁售平台(逗号拼接) database column amazon_product_listing.forbidChannel
     */
    private String forbidChannel;

    /**
     * 单品状态 database column amazon_product_listing.skuStatus
     */
    private String skuStatus;

    /**
     * 产品标签code database column amazon_product_listing.tagCodes
     */
    private String tagCodes;

    /**
     * 产品标签 database column amazon_product_listing.tagNames
     */
    private String tagNames;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsCode
     */
    private String specialGoodsCode;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsName
     */
    private String specialGoodsName;

    /**
     * marketplace egg：Y 表示是marketplace database column amazon_product_listing.itemIsMarketplace
     */
    private String itemIsMarketplace;

    /**
     * amazon条件 database column amazon_product_listing.itemCondition
     */
    private String itemCondition;

    /**
     * amazon类别 database column amazon_product_listing.zshopCategory
     */
    private String zshopCategory;

    /**
     * 产品id类型 产品id类型 1:ASIN 4：EAN/UPC
     */
    private Integer productIdType;

    /**
     * 产品id database column amazon_product_listing.productId
     */
    private String productId;

    /**
     * 主图 database column amazon_product_listing.mainImage
     */
    private String mainImage;

    /**
     * 样品图 database column amazon_product_listing.sampleImage
     */
    private String sampleImage;

    /**
     * 附图 database column amazon_product_listing.extraImages
     */
    private String extraImages;

    /**
     * 价格 database column amazon_product_listing.price
     */
    private Double price;

    /**
     * 利润率
     */
    private Double grossProfitRate;

    /**
     * 利润
     */
    private Double grossProfit;

    /**
     * 平台库存数量 database column amazon_product_listing.quantity
     */
    private Integer quantity;

    /**
     * 订单系统销量 database column amazon_product_listing.saleQuantity
     */
    private Integer saleQuantity;

    /**
     * 促销价 database column amazon_product_listing.salePrice
     */
    private Double salePrice;

    /**
     * 促销起始日期 database column amazon_product_listing.saleStartDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saleStartDate;

    /**
     * 促销结束日期 database column amazon_product_listing.saleEndDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saleEndDate;

    /**
     * 最低价 database column amazon_product_listing.lowestPrice
     */
    private Double lowestPrice;

    /**
     * 是否爆款(是：1  否：2) database column amazon_product_listing.isPopular
     */
    private String isPopular;

    /**
     * Amazon跟卖删除 database column amazon_product_listing.isFollowSellDelete
     */
    private Boolean isFollowSellDelete;

    /**
     * 跟卖标志(是：1  否：2) database column amazon_product_listing.followSaleFlag
     */
    private String followSaleFlag;

    /**
     * listing id database column amazon_product_listing.listingId
     */
    private String listingId;

    /**
     * sku生命周期 database column amazon_product_listing.skuLifeCyclePhase
     */
    private String skuLifeCyclePhase;

    /**
     * merchant shipping group database column amazon_product_listing.merchantShippingGroup
     */
    private String merchantShippingGroup;

    /**
     * 运费
     */
    private Double shippingCost;

    /**
     * 总价 = 价格 + 运费
     */
    private Double totalPrice;

    /**
     * identifierType 标识符类型 EAN UPC
     */
    private String identifierType;

    /**
     * identifier 标识符
     */
    private String identifier;

    /**
     * productType 产品类型
     */
    private String productType;

    /**
     * brandName 品牌
     */
    private String brandName;

    /**
     * browseNode 平台节点Node
     */
    private String browseNode;

    /**
     * colorName 颜色
     */
    private String colorName;

    /**
     * manufacturer 制造商
     */
    private String manufacturer;

    /**
     * modelNumber 制造商零件编号
     */
    private String modelNumber;

    /**
     * sizeName 尺寸
     */
    private String sizeName;

    /**
     * styleName 样式
     */
    private String styleName;

    /**
     * 类别id database column amazon_product_listing.categoryId
     */
    private String categoryId;

    /**
     * 类别中文名 database column amazon_product_listing.categoryCnName
     */
    private String categoryCnName;

    /**
     * 关联刊登成功的模板id database column amazon_product_listing.relationTemplateId
     */
    private Integer relationTemplateId;

    /**
     * 自动更新信息(标题或描述)到亚马逊的时间 database column amazon_product_listing.autoUpdateMsgDate
     */
    private Date autoUpdateMsgDate;

    /**
     * 最后调价日期 database column amazon_product_listing.lastAdjustPriceDate
     */
    private Date lastAdjustPriceDate;

    /**
     * report原始上架时间 database column amazon_product_listing.reportOpenDate
     */
    private String reportOpenDate;

    /**
     * 最新上架时间 database column amazon_product_listing.openDate
     */
    private Date openDate;

    /**
     * 第一次上架时间 database column amazon_product_listing.firstOpenDate
     */
    private Date firstOpenDate;

    /**
     * 最新下线时间 database column amazon_product_listing.offlineDate
     */
    private Date offlineDate;

    /**
     * 第一次下架时间 database column amazon_product_listing.firstOfflineDate
     */
    private Date firstOfflineDate;

    /**
     * 同步时间 database column amazon_product_listing.syncDate
     */
    private Date syncDate;

    /**
     * 创建人 database column amazon_product_listing.createdBy
     */
    private String createdBy;

    /**
     * 创建时间 database column amazon_product_listing.createDate
     */
    private Date createDate;

    /**
     * 修改时间 database column amazon_product_listing.updateDate
     */
    private Date updateDate;

    /**
     * 修改人 database column amazon_product_listing.updatedBy
     */
    private String updatedBy;

    /**
     * 存储：账号等级
     * 属性1(预留) database column amazon_product_listing.attribute1
     */
    private String attribute1;

    /**
     * 存储：selersku大写
     * 属性2 database column amazon_product_listing.attribute2
     */
    private String attribute2;

    /**
     * 存储： 备注(egg: 成人用品系统自动删除)
     * 属性3 database column amazon_product_listing.attribute3
     */
    private String attribute3;

    /**
     * 存储：毛利、毛利率变更操作人
     * 属性4 database column amazon_product_listing.attribute4
     */
    private String attribute4;

    /**
     * 存储：毛利、毛利率变更操作时间
     * 属性5 database column amazon_product_listing.attribute5
     */
    private String attribute5;

    /**
     * 存储 平台getCatalogItem 接口 item的issues是Json
     * 属性6 database column amazon_product_listing.attribute6
     */
    private String attribute6;

    /**
     * 属性7 database column amazon_product_listing.attribute7
     */
    private String attribute7;

    /**
     * 根据站点分表
     */
    private String tableIndex;

    /**
     * 禁售类型，会有多个值，首尾|分隔
     */
    private String infringementTypename;

    /**
     * 禁售原因，会有多个值，首尾|分隔
     */
    private String infringementObj;

    /**
     * 禁售站点
     */
    private String normalSale;

    /**
     * 风险等级
     */
    private Integer riskLevelId;

    /**
     * 刊登角色 0系统刊登 1销售刊登 2文案刊登 3主管刊登
     */
    private Integer publishRole;

    /**
     * 备货期
     */
    private Integer fulfillmentLatency;

    /**
     * 改前备货期：临时字段
     */
    private String oldFulfillmentLatencyValue;

    /**
     * 改前库存：临时字段
     */
    private String oldQuantityValue;

    /**
     * 改前价格 ：临时字段
     */
    private String oldPriceValue;

    /**
     * 组合状态 8003 启用 8004 禁用
     * @see ComposeCheckStepEnum
     */
    private Integer composeStatus;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    private Integer promotion;

    /**
     * 新品状态
     */
    private Boolean newState;

    /**
     * issues对应的severity值value
     */
    private String issuesSeverity;

    /**
     * item的summaries对应的status
     */
    private String itemSummariesStastus;

    /**
     * item的summaries对应的conditionType
     */
    private String conditionType;

    /**
     * item的summaries对应的最后修改时间
     */
    private Date iteamLastUpdatedDate;

    /**
     * item的类型：1 单体、2 父体、3 变体
     */
    private Integer itemType;

    /**
     * 父体下的子asin集合
     */
    private String childAsins;

    /**
     * item的packageQuantity
     */
    private Integer packageQuantity;

    /**
     * 五点描述(数组)
     */
    private String bulletPoint;

    /**
     * 关键词
     */
    private String searchTerms;

    public void setTableIndex() {
        String site = this.site;
        // 按照站点
        if (StringUtils.isNotEmpty(site)) {
            if ((CountryEnum.FR.getSite()).equalsIgnoreCase(site) || (CountryEnum.SG.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.TR.getSite()).equalsIgnoreCase(site) || (CountryEnum.BE.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.IE.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_frsgtr";
            } else if ((CountryEnum.UK.getSite()).equalsIgnoreCase(site) || (CountryEnum.IN.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.BR.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_ukinbr";
            } else if ((CountryEnum.DE.getSite()).equalsIgnoreCase(site) || (CountryEnum.MX.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AU.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_demxau";
            } else if ((CountryEnum.US.getSite()).equalsIgnoreCase(site) || (CountryEnum.JP.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AE.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_usjpae";
            } else if ((CountryEnum.IT.getSite()).equalsIgnoreCase(site) || (CountryEnum.NL.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.SE.getSite()).equalsIgnoreCase(site) || (CountryEnum.EG.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_itnlseeg";
            } else if ((CountryEnum.ES.getSite()).equalsIgnoreCase(site) || (CountryEnum.CA.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.NP.getSite()).equalsIgnoreCase(site)|| (CountryEnum.PL.getSite()).equalsIgnoreCase(site)) {
                this.tableIndex = "_escasa";
            }
        }
    }
}