package com.estone.erp.publish.amazon.mq.publish;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AMAZON_PUBLISH_AFTER_PROCESS_QUEUE 刊登后置处理MQ
 *
 * <AUTHOR>
 * @date 2024-11-27 11:09
 */
@Slf4j
public class AmazonPublishAfterProcessMqListener implements ChannelAwareMessageListener {
    @Autowired
    private AmazonTemplateService amazonTemplateService;
    @Resource
    private CategoryMappingService categoryMappingService;
    @Resource
    private IAmazonListingInfoService amazonListingInfoService;
    @Resource
    private ProductClient productClient;
    @Resource
    private RabbitMqSender rabbitMqSender;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            int templateId = Integer.parseInt(body);
            AmazonTemplateBO template = amazonTemplateService.selectBoById(templateId, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
            if (template == null) {
                log.error("Amazon 刊登后置处理MQ，模板不存在：{}", templateId);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            if (!template.getPublishStatus().equals(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode())) {
                log.error("Amazon 刊登后置处理MQ，模板状态不正确：{}, {}", templateId, template.getPublishStatus());
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 推送刊登成功模板sku 到产品系统
            pushSkuToProductSystem(template);
            // 刊登成功模板生成类目映射
//            generateCategoryMapping(template);
            // 刊登成功模板绑定SKU
            addBindSku(templateId);
            // 刊登成功模板生成Admin范本
            generateAdminTemplate(template);
            // 刊登成功模版同步至在线列表
            syncListing(template);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("Amazon 刊登后置处理MQ，处理异常：", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 刊登成功模版同步至在线列表
     *
     * @param template template
     */
    private void syncListing(AmazonTemplateBO template) {
        try {
            String accountNumber = template.getSellerId();
            String site = template.getCountry();
            Map<String, String> allSellerSkuSkuMapping = AmazonTemplateUtils.getAllSellerSkuSkuMapping(template);
            if (MapUtils.isEmpty(allSellerSkuSkuMapping)) {
                return;
            }

            // 获取Listing明细并同步至DB
            amazonListingInfoService.syncListingInfo(accountNumber, site, allSellerSkuSkuMapping, template.getSkuDataSource());
        } catch (Exception e) {
            log.error("{},刊登成功模版同步至在线列表异常：", template.getId(), e);
        }
    }

    /**
     * 刊登成功模版绑定SKU到ES
     *
     * @param templateId
     */
    private void addBindSku(Integer templateId) {
        rabbitMqSender.publishSyncVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_KEY, List.of(templateId));
        log.info("Amazon 刊登成功模板绑定SKU到ES：{}", templateId);
    }

    /**
     * 刊登成功模板生成类目映射
     *
     * @param template
     */
    @Deprecated
    private void generateCategoryMapping(AmazonTemplateBO template) {
        try {
            // 平台（Amazon），站点，系统类目ID，平台分类  确定唯一
            List<String> skuList = AmazonTemplateUtils.getAllSku(template);
            Map<String, String> mainSku = ProductUtils.getMainSkuBySubSku(skuList);
            if (MapUtils.isEmpty(mainSku)) {
                log.error("Amazon 刊登成功模板生成类目映射，产品系统无主sku：{}", skuList);
                return;
            }

            List<ProductInfo> productInfos = ProductUtils.findProductInfos(skuList);
            if (CollectionUtils.isEmpty(productInfos)) {
                log.error("Amazon 刊登成功模板生成类目映射，产品系统无sku：{}", skuList);
                return;
            }

            Platform platformByName = Platform.getPlatformByName(Platform.Amazon.name());
            Map<String, String> platformCategoryMap = categoryMappingService.selectCategoryById(List.of(template.getCategoryId()), platformByName);

            String systemCategoryId = productInfos.get(0).getFullpathcode();
            String systemCategoryName = productInfos.get(0).getFullpath();

            // 查询是否存在类目映射
            CategoryMappingExample mappingExample = new CategoryMappingExample();
            mappingExample.createCriteria().andPlatformEqualTo("Amazon")
                    .andSiteEqualTo(template.getCountry())
                    .andSystemCategoryIdEqualTo(systemCategoryId)
                    .andPlatformCategoryIdEqualTo(template.getCategoryId());
            List<CategoryMapping> categoryMappings = categoryMappingService.selectByExample(mappingExample);
            if (CollectionUtils.isEmpty(categoryMappings)) {
                // 新增类目映射
                CategoryMapping category = new CategoryMapping();
                category.setPlatform(Platform.Amazon.name());
                category.setSite(template.getCountry());
                category.setMainSku(mainSku.get(skuList.get(0)));
                //品类id__分类类型 拼接
                category.setPlatformCategoryId(template.getCategoryId());
                category.setPlatformCategoryName(platformCategoryMap.get(template.getCategoryId()));
                //分类类型
                category.setProductTypeName(template.getProductType());
                category.setApplyState(ApplyStatusEnum.YES.getCode());
                // 系统分类
                category.setSystemCategoryId(systemCategoryId);
                category.setSystemCategoryName(systemCategoryName);
                category.setCreateBy("system");
                category.setCreateTime(new Date());
                categoryMappingService.insert(category);
            }
        } catch (Exception e) {
            log.error("{},刊登成功模板生成类目映射异常：", template.getId(), e);
        }
    }

    /**
     * 刊登成功模板生成Admin范本
     *
     * @param template
     */
    private void generateAdminTemplate(AmazonTemplateBO template) {
        try {
            String parentSku = template.getParentSku();
            String site = template.getCountry();
            String productType = template.getProductType();
            String categoryId = template.getCategoryId();

            // 站点、货号、分类、分类类型 唯一
            AmazonTemplateExample example = new AmazonTemplateExample();
            example.setColumns("id,parent_sku,product_type,category_id,country,publish_status");
            example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE_ADMIN.getCode());
            example.createCriteria().andParentSkuEqualTo(parentSku)
                    .andProductTypeEqualTo(productType)
                    .andCategoryIdEqualTo(categoryId)
                    .andCountryEqualTo(site);

            List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateService.selectFiledColumnsByExample(example);
            if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
                // 新增admin范本
                template.setId(null);
                template.setStatus(1);
                template.setPublishStatus(1);
                template.setCreationDate(new Date());
                template.setLastUpdateDate(new Date());
                template.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE_ADMIN.getCode());
                amazonTemplateService.insert(template);
                return;
            }

            // 更新admin范本
            AmazonTemplateBO adminTemplate = amazonTemplateBOS.get(0);
            template.setId(adminTemplate.getId());
            template.setStatus(1);
            template.setPublishStatus(1);
            template.setCreationDate(adminTemplate.getCreationDate());
            template.setLastUpdateDate(new Date());
            template.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE_ADMIN.getCode());
            amazonTemplateService.updateByPrimaryKeySelective(template);
        } catch (Exception e) {
            log.error("{},刊登成功模板生成Admin范本异常：", template.getId(), e);
        }
    }

    /**
     * 推送刊登成功模板sku 到产品系统
     *
     * @param template
     */
    private void pushSkuToProductSystem(AmazonTemplateBO template) {
        try {
            List<Map<String, String>> pushSpuList = List.of(
                    Map.of("spu", template.getParentSku(),
                            "publishTime", DateUtils.format(template.getLastUpdateDate(), DateUtils.STANDARD_DATE_PATTERN)));

            RetryUtil.doRetry(() -> {
                return productClient.publishSuccess(pushSpuList);
            }, 3);
        } catch (Exception e) {
            log.error("{},推送刊登成功模板sku 到产品系统异常：", template.getId(), e);
        }

    }
}
