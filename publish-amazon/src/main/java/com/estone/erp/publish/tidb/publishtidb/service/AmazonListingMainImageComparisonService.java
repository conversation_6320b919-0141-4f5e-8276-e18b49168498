package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonListingMainImageComparison;
import com.estone.erp.publish.amazon.model.AmazonListingMainImageComparisonCriteria;
import com.estone.erp.publish.amazon.model.AmazonListingMainImageComparisonExample;
import java.util.List;
import java.util.Set;

import com.estone.erp.publish.amazon.model.request.ComparisonOfflineRequest;

/**
 * <AUTHOR> amazon_listing_main_image_comparison
 * 2024-02-23 15:43:57
 */
public interface AmazonListingMainImageComparisonService {
    int countByExample(AmazonListingMainImageComparisonExample example);

    CQueryResult<AmazonListingMainImageComparison> search(CQuery<AmazonListingMainImageComparisonCriteria> cquery);

    List<AmazonListingMainImageComparison> selectByExample(AmazonListingMainImageComparisonExample example);

    AmazonListingMainImageComparison selectByPrimaryKey(Integer id);

    int insert(AmazonListingMainImageComparison record);

    int updateByPrimaryKeySelective(AmazonListingMainImageComparison record);

    int updateByExampleSelective(AmazonListingMainImageComparison record, AmazonListingMainImageComparisonExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    /**
     * 下架
     * @param request
     * @return
     */
    ApiResult<?> offline(ComparisonOfflineRequest request);

    void batchInsert(List<AmazonListingMainImageComparison> list);

    /**
     * 判断是否存在
     * @param accountNumber
     * @param sellerSkuList
     * @return
     */
    Set<String> getExistSellerSkuByAccount(String accountNumber, List<String> sellerSkuList);

    void batchUpdateSimilarity(List<AmazonListingMainImageComparison> list);
}