package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @Description: 产品列表 批量跟卖上传数据xml策略实现类
 * 
 * @ClassName: ProductFollowSellSubmitFeedXmlStrategy
 * @Author: Kevin
 * @Date: 2018/08/22
 * @Version: 0.0.1
 */
@Component
public class ProductFollowSellSubmitFeedXmlStrategy extends AbstractSubmitFeedXmlStrategy<AmazonFollowSellSuperiorBO> {

    @Override
    public String transferProduct2Xml(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Product", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonFollowSellSuperiorBO followSell : amazonFollowSells) {
            String parentSku = followSell.getSellerSku();
            msgId2SkuMap.put(messageId, parentSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Product
            Element product = message.create("Product");
            product.create("SKU", parentSku);
            Element standardProductID = product.create("StandardProductID");
            standardProductID.create("Type", followSell.getStandardProdcutIdType());
            standardProductID.create("Value", followSell.getStandardProdcutIdValue());
            publishData.addSku2SellerSku(followSell.getParentSku(), parentSku);
            publishData.addSkuSpFlagMap(followSell.getParentSku(), SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductPrice2Xml(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Price", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonFollowSellSuperiorBO followSell : amazonFollowSells) {
            String parentSku = followSell.getSellerSku();
            Double standardPrice = followSell.getStandardPrice();
            if (standardPrice == null || standardPrice <= 0d) {
                publishData.addErrorSku(parentSku, "产品价格不能为空和0");
                continue;
            }

            msgId2SkuMap.put(messageId, parentSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Price
            Element price = message.create("Price");
            price.create("SKU", parentSku);
            price.create("StandardPrice", String.valueOf(standardPrice)).addAttr("currency", publishData.getCurrency())
                    .addAttr("zero", "false");
        }

        return xmlBuilder.builder();
    }

    @Override
    public String transferProductInventory2Xml(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Inventory", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonFollowSellSuperiorBO followSell : amazonFollowSells) {
            String parentSku = followSell.getSellerSku();
            Integer quantity = followSell.getQuantity();
            if (quantity == null) {
                publishData.addErrorSku(parentSku, "库存数量不能为空");
                continue;
            }

            msgId2SkuMap.put(messageId, parentSku);
            // Message
            Element message = root.create("Message");
            message.create("MessageID", String.valueOf(messageId));
            messageId++;
            message.create("OperationType", operationType.name());
            // Inventory
            Element inventory = message.create("Inventory");
            inventory.create("SKU", parentSku);
            inventory.create("Quantity", String.valueOf(followSell.getQuantity()));
            inventory.create("FulfillmentLatency", String.valueOf(2));
        }

        return xmlBuilder.builder();
    }
}
