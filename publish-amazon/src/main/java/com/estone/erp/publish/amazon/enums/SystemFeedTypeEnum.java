package com.estone.erp.publish.amazon.enums;

/**
 * @Description: systemFeedType 的枚举
 * @Version: 1.0.0
 */
public enum SystemFeedTypeEnum {
    LISTING_PRICE_FEED_DOCUMENT("LISTING_PRICE_FEED_DOCUMENT","修改价格"),
    LISTING_INVENTORY_FEED_DOCUMENT("LISTING_INVENTORY_FEED_DOCUMENT","修改库存"),
    LISTING_HANDING_TIME_FEED_DOCUMENT("LISTING_HANDING_TIME_FEED_DOCUMENT", "修改备货期"),
    LISTING_IMAGE_FEED_DOCUMENT("LISTING_IMAGE_FEED_DOCUMENT","修改图片"),
    LISTING_PRODUCT_TITLE_FEED_DOCUMENT("LISTING_PRODUCT_TITLE_FEED_DOCUMENT", "修改产品标题"),
    TEMPLATE_PUBLISH_FEED_DOCUMENT("TEMPLATE_PUBLISH_FEED_DOCUMENT", "模版刊登"),
     // 暂不开启，会删除全球产品
    //LISTING_DELETE_FEED_DOCUMENT("LISTING_DELETE_FEED_DOCUMENT","删除"),
   ;

    //系统定义feed类型
    private String systemFeedType;
    //类型注释
    private String systemFeedTypeMsgCn;

    private SystemFeedTypeEnum(String systemFeedType, String systemFeedTypeMsgCn) {

        this.systemFeedType = systemFeedType;
        this.systemFeedTypeMsgCn = systemFeedTypeMsgCn;
    }

    public String getSystemFeedType() {
        return systemFeedType;
    }

}
