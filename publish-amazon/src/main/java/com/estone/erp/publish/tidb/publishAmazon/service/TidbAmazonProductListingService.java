package com.estone.erp.publish.tidb.publishAmazon.service;

import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;

import java.util.List;

/**
 * <AUTHOR> amazon_product_listing
 * 2020-12-19 16:28:50
 */
public interface TidbAmazonProductListingService {

    List<AmazonProductListing> selectByExample(AmazonProductListingExample example , String site);

    /**
     * 查询自定义字段listing
     * @param example
     * @param site
     * @return
     */
    List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example, String site);

    /**
     * 查询自定义字段listing
     * @param example
     * @return
     */
    List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example);

    /**
     * 查询运费模板
     * @param example
     * @param site
     * @return
     */
    List<String> selectMerchantShippingGroupByExample(AmazonProductListingExample example, String site);

}