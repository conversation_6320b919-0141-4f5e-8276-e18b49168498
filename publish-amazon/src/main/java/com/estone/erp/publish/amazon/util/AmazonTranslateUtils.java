package com.estone.erp.publish.amazon.util;

import com.estone.erp.common.util.GoogleTranslateUtils;
import org.apache.commons.lang.StringUtils;

/**
 * 翻译工具类
 *
 * <AUTHOR>
 * @date 2023/5/31 10:29
 */
public class AmazonTranslateUtils {

    /**
     * 德国
     */
    private final static String GERMANY = "de";

    /**
     * 翻译 (针对德语翻译数据做了特殊处理)
     *
     * @param srcLang 原语言
     * @param destLang 翻译语言
     * @param text 文本
     * @param retry 重试次数
     * @return 翻译后文本
     */
    public static String translate(String srcLang, String destLang, Object text, int retry) {
        if (null == text) {
            return null;
        }
        String translateText = GoogleTranslateUtils.translate(srcLang, destLang, text, retry);
        if (StringUtils.isBlank(translateText)) {
            return null;
        }
        if (GERMANY.equals(destLang)) {
            // 将单词之间的连字符替换为空格，如果连字符两边存在数字不需要处理
            translateText = translateText.replaceAll("(?<![0-9])-(?![0-9])", " ");
            return translateText;
        }

        return translateText;
    }
}
