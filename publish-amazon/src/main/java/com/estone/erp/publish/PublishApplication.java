package com.estone.erp.publish;

import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = { "com.estone.erp" }, exclude = { SecurityAutoConfiguration.class})
@EnableAsync
@EnableCircuitBreaker
@EnableFeignClients(basePackages = { "com.estone.erp" })
@ImportResource(value = { "classpath:application-mybatis.xml" })
public class PublishApplication {
    private static final PublishLogger log = PublishLoggerFactory.getLogger(PublishApplication.class);

    public static void main(String[] args) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        System.setProperty("com.alibaba.nacos.client.naming.ctimeout", "50000");
        SpringApplication.run(PublishApplication.class, args);
        log.alertMessage("Amazon服务, 启动完成");
    }
}
