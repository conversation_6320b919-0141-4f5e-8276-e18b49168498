package com.estone.erp.publish.amazon.call;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.ERPInvoker;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.process.submit.AmazonDelayTaskDispatcher;
import com.estone.erp.publish.amazon.call.process.submit.ProductFollowSellSubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.call.process.submit.SubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.enums.FollowSellOperationType;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.amazonImageGenerateRecord.service.AmazonImageGenerateRecordService;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.request.RequestFeedsApiParam;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;

/**
 * @Description 用于：amazon跟卖、amazon产品列表批量跟卖
 * <AUTHOR>
 * @Date 2019/10/17 11:25
 **/
@Slf4j
@Getter
public class AmazonFollowSellPublishCall {

    /**
     * 从哪个模块调用，用于走不同的逻辑
     */
    private FollowSellOperationType followSellOperationType;
    /**
     * 账号
     */
    protected AmazonAccount account;
    /**
     * 上传数据xml策略
     */
    protected SubmitFeedXmlStrategy<AmazonFollowSellSuperiorBO> xmlStrategy = SpringUtils.getBean(ProductFollowSellSubmitFeedXmlStrategy.class);
    /**
     * 添加任务时间
     */
    protected long addTimeValue = System.nanoTime();
    /**
     * 为线程之间切换保留用户数据
     */
    protected String username;
    /**
     * 更新为3小时超时过期
     */
    protected static final int MAX_WAIT_DATA_TIME = 3 * 60 * 60 * 1000;
    /**
     * 延时任务转发线程
     */
    public static final AmazonDelayTaskDispatcher delayTaskDispatcher = new AmazonDelayTaskDispatcher(200, 2000);

    protected AmazonProcessReportService amazonProcessReportService = SpringUtils.getBean(AmazonProcessReportService.class);
    protected EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);
    protected AmazonImageGenerateRecordService amazonImageGenerateRecordService = SpringUtils.getBean(AmazonImageGenerateRecordService.class);
    protected AmazonFollowSellPublishCallHelper amazonFollowSellPublishCallHelper = SpringUtils.getBean(AmazonFollowSellPublishCallHelper.class);


    public AmazonFollowSellPublishCall(String accountNumber, FollowSellOperationType followSellOperationType){
        this(SpringUtils.getBean(AmazonAccountService.class).queryAmazonAccountByAccountNumber(accountNumber), followSellOperationType);
    }

    public AmazonFollowSellPublishCall(AmazonAccount account, FollowSellOperationType followSellOperationType){
        Assert.notNull(account, "帐号为空！");
        Assert.notNull(xmlStrategy, "XML组装策略为空！");
        this.account = account;
        this.followSellOperationType = followSellOperationType;
        saveThreadLocalData();
        if(StringUtils.isBlank(username)){
            username = DataContextHolder.getUsername();
        }
    }

    /**
     * 从平台下架或删除跟卖产品
     * @param amazonFollowSells
     */
    public void deleteOrLowerShelfFollowSells(List<AmazonFollowSellSuperiorBO> amazonFollowSells) {
        if (CollectionUtils.isEmpty(amazonFollowSells)) {
            return;
        }

        List<AmazonFollowSellSuperiorBO> emptyQuantityFollowSells = new ArrayList<>();
        List<AmazonFollowSellSuperiorBO> deleteFollowSells = new ArrayList<>();
        for (AmazonFollowSellSuperiorBO amazonFollowSell : amazonFollowSells) {
            if (BooleanUtils.toBoolean(amazonFollowSell.getIsOffLineDelete())) {
                deleteFollowSells.add(amazonFollowSell);
            }
            else {
                amazonFollowSell.setQuantity(0);
                emptyQuantityFollowSells.add(amazonFollowSell);
            }
        }

        // 下架跟卖产品
        if(CollectionUtils.isNotEmpty(emptyQuantityFollowSells)){
            log.warn("account[{}]---deleteFollowSells [empty quantity] size: {}.", account.getAccountNumber(), emptyQuantityFollowSells.size());
            publish(emptyQuantityFollowSells, SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue(), false);
        }

        // 删除跟卖产品
        if(CollectionUtils.isNotEmpty(deleteFollowSells)){
            log.warn("account[{}]---deleteFollowSells [delete] size: {}.", account.getAccountNumber(), deleteFollowSells.size());
            publish(deleteFollowSells, SpFeedType.POST_PRODUCT_DATA.getValue(), true);
        }
    }

    public boolean publish(List<AmazonFollowSellSuperiorBO> amazonFollowSells, String feedType, boolean delete) {
        if (StringUtils.isEmpty(feedType)) {
            log.warn("invoke publish() error, params not satisfy.");
            return false;
        }
        List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, item -> {
            item.setUnitDatas(amazonFollowSells);
            if (delete) {
                item.setOperationType(OperationType.Delete);
            }
            return CollectionUtils.isNotEmpty(amazonFollowSells);
        });

        return true;
    }

    /**
     * 跟卖方法
     * @param amazonFollowSells
     */
    public void publishAmazonFollowSells(List<AmazonFollowSellSuperiorBO> amazonFollowSells) {
        if (CollectionUtils.isEmpty(amazonFollowSells)) {
            return;
        }
        // 刊登amazon跟卖产品，对刊登成功的产品更新库存，价格
        publishAmazonFollowSells(amazonFollowSells, SpFeedType.POST_PRODUCT_DATA.getValue(), false);
    }

    public boolean publishAmazonFollowSells(List<AmazonFollowSellSuperiorBO> amazonFollowSells, String feedType, boolean delete) {
        if (StringUtils.isEmpty(feedType)) {
            log.warn("invoke publish() error, params not satisfy.");
            return false;
        }
        List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, item -> {
            item.setUnitDatas(amazonFollowSells);
            if (delete) {
                item.setOperationType(OperationType.Delete);
            }
            return CollectionUtils.isNotEmpty(amazonFollowSells);
        });

//        boolean result = waitFinish(unitPublishDatas, () ->{
//            //如果产品上传成功则 跟卖价格和库存
//            followSellPriceAndInventory(unitPublishDatas);
//        });

        return true;
    }

    /**
     * 跟卖价格和库存
     * @param unitPublishDatas
     */
    public void followSellPriceAndInventory(List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas) {
        if(CollectionUtils.isEmpty(unitPublishDatas)){
            return;
        }
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = new ArrayList<>(8);
        for (PublishData<AmazonFollowSellSuperiorBO> unitPublishData : unitPublishDatas) {
            boolean success = unitPublishData.isSuccess();
            if(success){
                List<AmazonFollowSellSuperiorBO> unitDatas = unitPublishData.getUnitDatas();
                amazonFollowSells.addAll(unitDatas);
            }
        }
        if(amazonFollowSells.size() > 0){
            //对上传成功的产品进行批量上传价格和库存
            batchPublish(amazonFollowSells, Arrays.asList(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue(), SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue()));
        }
    }

    public void batchPublish(List<AmazonFollowSellSuperiorBO> amazonFollowSells, List<String> feedTypes) {
        batchPublish(feedTypes, item -> {
            item.setUnitDatas(amazonFollowSells);
            return true;
        }, null);
    }

    /**
     * @Description: 批量刊登上传数据
     * @param feedTypes 上传数据类型列表
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @param finishCallBack 结束回调函数
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public void batchPublish(List<String> feedTypes, Function<PublishData<AmazonFollowSellSuperiorBO>, Boolean> fillPublishData, ERPInvoker finishCallBack) {
        if (CollectionUtils.isEmpty(feedTypes)) {
            log.warn("invoke batchPublish() error, params not satisfy.");
            return;
        }

        List<PublishData<AmazonFollowSellSuperiorBO>> totalUnitPublishDatas = new ArrayList<>();
        for (String feedType : feedTypes) {
            List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, fillPublishData);
            totalUnitPublishDatas.addAll(unitPublishDatas);
        }
//        waitFinish(totalUnitPublishDatas, () -> {
//            if(FollowSellOperationType.AMAZON_PRODUCT_BATCH_FOLLOW_SELL.equals(followSellOperationType)){
//                //对产品列表模块 上传成功的数据进行记录跟卖日志
//                amazonFollowSellPublishCallHelper.insertFollowSellLog(totalUnitPublishDatas);
//            }else if(FollowSellOperationType.AMAZON_FOLLOW_SELL.equals(followSellOperationType)){
//
//            }
//        });
    }

    public List<PublishData<AmazonFollowSellSuperiorBO>> getSuccessUnitPublishDatas(String feedType, Function<PublishData<AmazonFollowSellSuperiorBO>, Boolean> fillPublishData) {
        PublishData<AmazonFollowSellSuperiorBO> publishData = new PublishData<>();
        publishData.setAccount(account);
        publishData.setFeedType(feedType);
        if (fillPublishData != null && !BooleanUtils.toBoolean(fillPublishData.apply(publishData))) {
            return CommonUtils.emptyList();
        }

        List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas = new ArrayList<>(publishData.getUnitDatas().size());
        for (AmazonFollowSellSuperiorBO unitData : publishData.getUnitDatas()) {
            Optional<PublishData<AmazonFollowSellSuperiorBO>> optional = getSuccessUnitPublishData(publishData, unitData);
            if (optional.isPresent()) {
                unitPublishDatas.add(optional.get());
            }

            switch (followSellOperationType){
                case AMAZON_FOLLOW_SELL:
                    amazonFollowSellPublishCallHelper.setFollowSellDataStatus(publishData.getOperationType(), feedType, unitData, optional.isPresent(), false);
                    break;
                case AMAZON_PRODUCT_BATCH_FOLLOW_SELL:
                    break;
            }
        }
        return unitPublishDatas;
    }

    public Optional<PublishData<AmazonFollowSellSuperiorBO>> getSuccessUnitPublishData(PublishData<AmazonFollowSellSuperiorBO> publishData, AmazonFollowSellSuperiorBO unitData) {
        PublishData<AmazonFollowSellSuperiorBO> unitPublishData = new PublishData<>();
        unitPublishData.setAccount(account);
        unitPublishData.setCurrency(publishData.getCurrency());
        unitPublishData.setFeedType(publishData.getFeedType());
        unitPublishData.setImagePath(publishData.getImagePath());
        unitPublishData.setOperationType(publishData.getOperationType());
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(unitData));

        List<AmazonProcessReport> reports = amazonFollowSellPublishCallHelper.initProcessReports(unitPublishData, followSellOperationType);
        unitPublishData.setReports(reports);

        Optional<PublishData<AmazonFollowSellSuperiorBO>> empty = Optional.empty();
        // 当sku为侵权产品或状态为停产、存档时，不允许刊登
        if (publishData.getFeedType().equals(SpFeedType.POST_PRODUCT_DATA.getValue())) {
            amazonFollowSellPublishCallHelper.finishForbidOrOffLineSkus(unitPublishData);
        }
        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return empty;
        }

        String xml = getXsdXml(unitPublishData);
        if (StringUtils.isEmpty(xml)) {
            finishProcessReports(unitPublishData, false, "xml文件生成失败");
            return empty;
        }

        // 处理错误的sku报告
        amazonFollowSellPublishCallHelper.finishErrorProcessReports(unitPublishData);
        // 没有Message和sku的关系，则退出
        if (MapUtils.isEmpty(unitPublishData.getMsgId2SkuMap())) {
            return empty;
        }

        // 保存sku与sellerSku的映射关系
        try {
            if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(unitPublishData.getFeedType())) {
                EsSkuBind skuBind = new EsSkuBind();
                skuBind.setPlatform(Platform.Amazon.name());
                skuBind.setSellerId(account.getAccountNumber());
                skuBind.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                esSkuBindService.batchBindSkus(skuBind, unitPublishData.getSku2SellerSkuMap(),null);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            finishProcessReports(unitPublishData, false, "amazon SellerSKU重复，请修改数据。" + e.getMessage()
                    + JSON.toJSONString(unitPublishData.getSku2SellerSkuMap()));
            return empty;
        }

        // 设置处理报告为running状态
        amazonFollowSellPublishCallHelper.runProcessReports(reports);

        // 调用SP接口
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        String feedTypeStr = unitPublishData.getFeedType();

        RequestFeedsApiParam request = new RequestFeedsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSpFeedType(SpFeedType.of(feedTypeStr));
        request.setDocId2MessageXmlMap(getDocId2MessageXml(xml, unitPublishData));
        request.setAddTimeValue(addTimeValue);

        ApiResult<String> apiResult = AmazonSpLocalServiceUtils.addFeedsTask(request);
        String taskId = apiResult.getResult();
        if (!apiResult.isSuccess()) {
            // 对于重复添加的任务，记录taskId
            if (StringUtils.isNotEmpty(taskId)) {
                reports.forEach(report -> {
                    report.setTaskId(taskId);
                });
            }
            finishProcessReports(unitPublishData, false, apiResult.getErrorMsg());
            return empty;
        }

        if (StringUtils.isEmpty(taskId)) {
            finishProcessReports(unitPublishData, false, "添加任务失败，taskId为空");
            return empty;
        }
        // 更新处理报告的taskId
        amazonFollowSellPublishCallHelper.updatePublishDataTaskId(unitPublishData, taskId);

        return Optional.of(unitPublishData);
    }

    private String getXsdXml(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        String xml = null;
        String feedType = publishData.getFeedType();
        switch (SpFeedType.of(feedType)) {
            case POST_PRODUCT_DATA:
                xml = xmlStrategy.transferProduct2Xml(publishData);
                break;
            case POST_PRODUCT_RELATIONSHIP_DATA:
                xml = xmlStrategy.transferProductRelationship2Xml(publishData);
                break;
            case POST_PRODUCT_PRICING_DATA:
                xml = xmlStrategy.transferProductPrice2Xml(publishData);
                break;
            case POST_INVENTORY_AVAILABILITY_DATA:
                xml = xmlStrategy.transferProductInventory2Xml(publishData);
                break;
            case POST_PRODUCT_IMAGE_DATA:
                xml = xmlStrategy.transferProductImage2Xml(publishData);
                break;
            default:
                break;
        }
        return xml;
    }

    /**
     * @Description: 批量设置处理报告为结束状态
     * @param publishData 刊登数据
     * @param status 成功状态
     * @param msg 报告信息
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void finishProcessReports(PublishData<AmazonFollowSellSuperiorBO> publishData, boolean status, String msg) {
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            amazonFollowSellPublishCallHelper.finshProcessReport(report, status, msg);
        });
        amazonProcessReportService.update(reports);
        if (status) {
            afterFinishProcessReports(publishData);
        }
    }

    public void afterFinishProcessReports(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        if(followSellOperationType == null){
            return;
        }
        switch (followSellOperationType){
            case AMAZON_FOLLOW_SELL:
                amazonFollowSellPublishCallHelper.afterFinishProcessReportsFollowSell(publishData);
                break;

            case AMAZON_PRODUCT_BATCH_FOLLOW_SELL:
                break;
        }
    }

    public Map<String, String> getDocId2MessageXml(String xml, PublishData<AmazonFollowSellSuperiorBO> unitPublishData) {
        Map<Integer, String> msgId2SkuMap = unitPublishData.getMsgId2SkuMap();
        Map<Integer, String> msgId2MessageXmlMap = XsdUtils.splitXmlByMessage(xml);
        Map<String, List<String>> docId2MessageXmlsMap = new HashMap<>(unitPublishData.getSku2ReportMap().size());
        int size = SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue().equals(unitPublishData.getFeedType()) ? 9 : 1;
        msgId2MessageXmlMap.forEach((msgId, messageXml) -> {
            String sku = msgId2SkuMap.get(msgId);
            if (!docId2MessageXmlsMap.containsKey(sku)) {
                docId2MessageXmlsMap.put(sku, new ArrayList<>(size));
            }
            docId2MessageXmlsMap.get(sku).add(messageXml);
        });

        Map<String, String> docId2MessageXmlMap = new HashMap<>(docId2MessageXmlsMap.size());
        docId2MessageXmlsMap.forEach((k, v) -> {
            docId2MessageXmlMap.put(k, JSON.toJSONString(v));
        });

        return docId2MessageXmlMap;
    }


    /**
     * @Description: 保存ThreadLocal数据
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    private void saveThreadLocalData() {
        if (StringUtils.isEmpty(username)) {
            this.username = WebUtils.getUserName();
        }
    }

    /**
     * @Description: 初始化ThreadLocal数据
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public void initThreadLocalData() {
        if (StringUtils.isNotEmpty(username)) {
            DataContextHolder.setUsername(this.username);
        }
    }
}


