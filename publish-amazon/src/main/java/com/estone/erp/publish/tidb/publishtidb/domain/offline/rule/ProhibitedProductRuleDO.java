package com.estone.erp.publish.tidb.publishtidb.domain.offline.rule;

import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.service.impl.AmazonOfflineConfigServiceImpl;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 禁售产品下架配置
 *
 * <AUTHOR>
 * @date 2025-01-07 11:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProhibitedProductRuleDO extends BaseOfflineRuleConfig {

    /**
     * 禁售信息
     */
    @NotEmpty(message = "禁售信息不能为空")
    private List<ProhibitedRuleConfigDO> prohibitedRules;


    public ProhibitedProductRuleDO matchListingRule(EsAmazonProductListing listing, AmazonAsinSaleCountDO saleCount) {
        ProhibitedProductRuleDO ruleDO = new ProhibitedProductRuleDO();
        ruleDO.setAsinStatus(listing.getItemStatus());

        ProhibitedRuleConfigDO prohibitedRule = new ProhibitedRuleConfigDO();
        prohibitedRule.setInfringementObj(listing.getInfringementObj());
        prohibitedRule.setInfringementTypeName(listing.getInfringementTypename());

        ProhibitedRuleConfigDO.PlatformData platformData = new ProhibitedRuleConfigDO.PlatformData();
        platformData.setPlatform(listing.getForbidChannel());
        if (StringUtils.isNotBlank(listing.getNormalSale())) {
            platformData.setSites(Lists.newArrayList(listing.getNormalSale()));
        }
        prohibitedRule.setPlatformData(List.of(platformData));
        ruleDO.setProhibitedRules(List.of(prohibitedRule));

        Date openDate = Optional.ofNullable(listing.getOpenDate())
                .orElseGet(() -> Optional.ofNullable(listing.getFirstOpenDate()).orElseGet(Date::new));
        LocalDateTime localDateTime = LocalDateTimeUtil.of(openDate);
        long betweenDays = LocalDateTimeUtil.betweenDays(localDateTime, LocalDateTime.now());
        ruleDO.setLastOpenDateExceeding(Long.valueOf(betweenDays).intValue());
        SaleCountRuleConfigDO saleCountRule = this.getSaleCount();
        if (saleCountRule != null) {
            SaleCountRuleConfigDO saleCountRuleConfigDO = new SaleCountRuleConfigDO();
            saleCountRuleConfigDO.setType(saleCountRule.getType());
            Integer saleCountNumber = AmazonOfflineConfigServiceImpl.RULE_CONFIG_FUNC.apply(saleCountRule, saleCount);
            saleCountRuleConfigDO.setListingValue(saleCountNumber);
            ruleDO.setSaleCount(saleCountRuleConfigDO);
        }
        return ruleDO;
    }
}

