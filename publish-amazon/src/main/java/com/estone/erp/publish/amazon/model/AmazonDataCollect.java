package com.estone.erp.publish.amazon.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AmazonDataCollect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column amazon_data_collect.id
     */
    private Integer id;

    /**
     * 店铺 database column amazon_data_collect.account_number
     */
    private String accountNumber;

    /**
     * 类型（1：模版类型，2：listing类型）
     */
    private Integer type;

    /**
     * 类型 database column amazon_data_collect.status
     */
    private Integer status;

    /**
     * 模版类型：模板数量
     */
    private Integer quantity;

    /**
     * 模版类型: 刊登sku数量，listing类型: listing数量
     */
    private Integer skuQuantity;

    /**
     * 销售 database column amazon_data_collect.sale_user
     */
    private String saleUser;
    /**
     * 用户及名称
     */
    private String username;

    /**
     * 备注 database column amazon_data_collect.remark
     */
    private String remark;

    /**
     * 创建时间 database column amazon_data_collect.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column amazon_data_collect.created_by
     */
    private String createdBy;

    /**
     * 统计数据1 database column amazon_data_collect.attribute1
     */
    private String attribute1;

    /**
     * 统计数据2 database column amazon_data_collect.attribute2
     */
    private String attribute2;

    /**
     * 统计数据3 database column amazon_data_collect.attribute3
     */
    private String attribute3;

    /**
     * 统计数据4 database column amazon_data_collect.attribute4
     */
    private String attribute4;

    /**
     * 统计数据5 database column amazon_data_collect.attribute5
     */
    private String attribute5;
}