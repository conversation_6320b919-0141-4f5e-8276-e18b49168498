package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.call.process.submit.PublishRestoreListingRelationShipProcesser;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp;
import com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTemp;
import com.estone.erp.publish.amazon.service.AmazonListingParentRelationshipTempService;
import com.estone.erp.publish.amazon.service.AmazonListingSonRelationshipTempService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.system.order.OrderUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description 绑定父子关系
 * @see SkuStatusEnum
 **/
@Slf4j
@Component
public class UpdateAmazonListingRelationshipTempJobHandler extends AbstractJobHandler {

    private static  final String[] fields = {"accountNumber","site","sellerSku","parentAsin","sonAsin","order_num_total",
            "order_last_30d_count","mainSku","articleNumber","sizeName","colorName","productType","browseNode","itemName","itemStatus","brandName","manufacturer","modelNumber"};


    @Data
    static class InnerParam{
        //店鋪账号
        private List<String>  accountNumberList;
    }

    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private AmazonListingParentRelationshipTempService amazonListingParentRelationshipTempService;
    @Autowired
    private AmazonListingSonRelationshipTempService amazonListingSonRelationshipTempService;

    public UpdateAmazonListingRelationshipTempJobHandler() {
        super("UpdateAmazonListingRelationshipTempJobHandler");
    }

    @Override
    @XxlJob("UpdateAmazonListingRelationshipTempJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        long startTime = System.currentTimeMillis();
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
                XxlJobLogger.log("本次执行的参数信息 {}, uuid->{}", JSON.toJSONString(innerParam));
            }catch (Exception e){
                ReturnT fail = new ReturnT(500, "参数错误。例子：{\"accountNumberList\": [\"xxx\",\"aaa\"]}");
                fail.setContent(e.getMessage());
                return fail;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        List<String> accountNumberList = innerParam.getAccountNumberList();
        if (CollectionUtils.isEmpty(accountNumberList)){
            accountNumberList = new ArrayList<>();
            EsAmazonProductListingRequest esAmazonProductListingRequest1 = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest1.setIsOnline(false);
            esAmazonProductListingRequest1.setAttribute3("超额刊登，系统自动下架");
            esAmazonProductListingRequest1.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
            accountNumberList =  esAmazonProductListingService.getAccountNumberListByRequest(esAmazonProductListingRequest1);
        }

        log.warn("============超额刊登，排除后系统自动下架父体包含的店铺数量=========" + accountNumberList.size());
        updateRelationship(accountNumberList);
        long endTime = System.currentTimeMillis();
        log.info("AmazonUnqualifiedSku2ZeroJobHandler cost time is {}", ((endTime-startTime)/1000L));
        return ReturnT.SUCCESS;
    }

    private void updateRelationship(List<String> accountNumberList) {
        int j =0;
        for (String accountNumber : accountNumberList) {
            j++;
           // AmazonExecutors.executeUpdateListingRelationShip( () -> {
                try {
                    log.info("账号{} 开始执行超额刊登，系统自动下架父体", accountNumber);
                    AmazonAccount account = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
                    if((account != null && "0".equals(account.getAccountStatus()))
                            || AmazonSpLocalUtils.checkAmazonAccountMsgError(account)) {
                        XxlJobLogger.log("店铺: {} 修改在线listing绑定关系, 账号状态为禁用或者未授权到sp-api ，不执行。", accountNumber);
                        continue;
                    }

                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
                    esAmazonProductListingRequest.setAccountNumber(accountNumber);
                    esAmazonProductListingRequest.setIsOnline(false);
                    esAmazonProductListingRequest.setFields(fields);
                    esAmazonProductListingRequest.setAttribute3("子ASIN为空的父asin，系统自动下架");
                    esAmazonProductListingRequest.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
                    esAmazonProductListingRequest.setStartOfflineDate("2024-05-09 15:00:00");
                    List<EsAmazonProductListing> allParentAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
                    if(CollectionUtils.isEmpty(allParentAmazonProductListings)){
                        XxlJobLogger.log("店铺: {} 查询超额刊登，系统自动下架数据为空，不执行请检查。", accountNumber);
                        continue;
                    }
                    List<String> parntAsinList = allParentAmazonProductListings.stream().map(o->o.getSonAsin()).collect(Collectors.toList());
                    List<List<String>> asins = Lists.partition(parntAsinList,500);
                    Set<String> fbaParentAsinSet = new HashSet<>();
                    Set<String> parentAsinSet = new HashSet<>();
                    List<EsAmazonProductListing> allSonProductListingList = new ArrayList<>();
                    for (List<String> parentAsins: asins) {
                        EsAmazonProductListingRequest sonAmazonProductListingRequest = new EsAmazonProductListingRequest();
                        sonAmazonProductListingRequest.setAccountNumber(accountNumber);
                        sonAmazonProductListingRequest.setIsOnline(true);
                        sonAmazonProductListingRequest.setFields(fields);
                        sonAmazonProductListingRequest.setParentAsinList(parentAsins);
                        sonAmazonProductListingRequest.setItemType(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode());
                        Page<EsAmazonProductListing> page = esAmazonProductListingService.page(sonAmazonProductListingRequest, 3000, 0);
                        if (page != null && CollectionUtils.isNotEmpty(page.getContent())){
                            List<EsAmazonProductListing> esAmazonProductListingList = page.getContent();
                            checkFbaDataFlag(fbaParentAsinSet,esAmazonProductListingList);
                            checkExistSonListingData(parentAsinSet,esAmazonProductListingList);
                            allSonProductListingList.addAll(esAmazonProductListingList);
                        }
                    }
                    handelParntAsinData(parentAsinSet,fbaParentAsinSet,allParentAmazonProductListings,allSonProductListingList,account);
                }catch (Exception e){
                    log.error(String.format("账号%s 出错", accountNumber), e);
                }
           // });
        }
        XxlJobLogger.log("执行结束");
    }

    /**
     * 判定是否是fba标识
     * @param fbaParentAsinSet
     * @param esAmazonProductListingList
     */
    private void checkFbaDataFlag(Set<String> fbaParentAsinSet,List<EsAmazonProductListing> esAmazonProductListingList){
        // 判定FBA库存管理存在的asin
        List<String> sonAsinList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getSonAsin).collect(Collectors.toList());
        // 获取FBA asin
        List<String> existSonAsinList = OrderUtils.getFBAExistAsins(sonAsinList);
        if (CollectionUtils.isNotEmpty(existSonAsinList)) {
            // 全是FBA
            fbaParentAsinSet.addAll(existSonAsinList);
        }
    }

    /**
     * 收集还存在子产品的父asin信息
     * @param parentAsinSet
     * @param esAmazonProductListingList
     */
    private void checkExistSonListingData(Set<String> parentAsinSet,List<EsAmazonProductListing> esAmazonProductListingList){
        // 判定FBA库存管理存在的asin
        List<String> parAsinList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getParentAsin).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parAsinList)) {
            // 全是FBA
            parentAsinSet.addAll(parAsinList);
        }
    }

    /**
     *  处理父体产品数据记录表
     * @param parentAsinSet
     * @param parentAmazonProductListingList
     */
    private void handelParntAsinData(Set<String> parentAsinSet,Set<String> fbaParentAsinSet,List<EsAmazonProductListing> parentAmazonProductListingList,List<EsAmazonProductListing> sonAmazonProductListingList, AmazonAccount account) {
        Map<String,List<EsAmazonProductListing>> parentAsinAndSonAmazonProductListingsMap = sonAmazonProductListingList.stream().collect(Collectors.groupingBy(o ->o.getParentAsin()));
        // 父体数据入记录表
        for (EsAmazonProductListing esAmazonProductListing : parentAmazonProductListingList) {
            String parntAsin = esAmazonProductListing.getSonAsin();
            AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp = new AmazonListingParentRelationshipTemp();
            amazonListingParentRelationshipTemp.setAccountNumber(esAmazonProductListing.getAccountNumber());
            amazonListingParentRelationshipTemp.setSite(esAmazonProductListing.getSite());
            amazonListingParentRelationshipTemp.setSonAsin(parntAsin);
            amazonListingParentRelationshipTemp.setSellerSku(esAmazonProductListing.getSellerSku());
            amazonListingParentRelationshipTemp.setArticleNumber(esAmazonProductListing.getArticleNumber());
            amazonListingParentRelationshipTemp.setCreateDate(new Timestamp(System.currentTimeMillis()));
            amazonListingParentRelationshipTemp.setItemStatus(esAmazonProductListing.getItemStatus());
            amazonListingParentRelationshipTemp.setProductType("Home.Home");
            try {
                List<EsAmazonProductListing> sonAmazonProductListings = parentAsinAndSonAmazonProductListingsMap.get(parntAsin);
                if (parentAsinSet.contains(parntAsin) && CollectionUtils.isNotEmpty(sonAmazonProductListings)) {
                    AtomicInteger orderSaleNum = new AtomicInteger(0);
                    Boolean colorNameFlag = false;
                    Boolean sizeNameFlag = false;
                    List<String> sonAsins = new ArrayList<>(sonAmazonProductListings.size());
                    List<String> sonSellerskus = new ArrayList<>(sonAmazonProductListings.size());
                    String brand = esAmazonProductListing.getBrandName();
                    String manufacturer = esAmazonProductListing.getManufacturer();
                    String modelNumber = esAmazonProductListing.getModelNumber();
                    String browseNodeId = esAmazonProductListing.getBrowseNode();
                    String itemName = esAmazonProductListing.getItemName();
                    List<AmazonListingSonRelationshipTemp> amazonListingSonRelationshipTempList = new ArrayList<>(sonAmazonProductListings.size());
                    for (EsAmazonProductListing sonEsAmazonProductListing : sonAmazonProductListings) {
                        if (null != sonEsAmazonProductListing.getOrder_num_total()) {
                            orderSaleNum.set(orderSaleNum.get() + sonEsAmazonProductListing.getOrder_num_total().intValue());
                        }
                        if (BooleanUtils.isFalse(colorNameFlag) && StringUtils.isNotBlank(sonEsAmazonProductListing.getColorName())) {
                            colorNameFlag = true;
                        }
                        if (BooleanUtils.isFalse(sizeNameFlag) && StringUtils.isNotBlank(sonEsAmazonProductListing.getSizeName())) {
                            sizeNameFlag = true;
                        }
                        if (StringUtils.isBlank(brand) && StringUtils.isNotBlank(sonEsAmazonProductListing.getBrandName())) {
                            brand = sonEsAmazonProductListing.getBrandName();
                        }
                        if (StringUtils.isBlank(manufacturer) && StringUtils.isNotBlank(sonEsAmazonProductListing.getManufacturer())) {
                            manufacturer = sonEsAmazonProductListing.getManufacturer();
                        }
                        if (StringUtils.isBlank(modelNumber) && StringUtils.isNotBlank(sonEsAmazonProductListing.getModelNumber())) {
                            modelNumber = sonEsAmazonProductListing.getModelNumber();
                        }
                        if (StringUtils.isBlank(browseNodeId) && StringUtils.isNotBlank(sonEsAmazonProductListing.getBrowseNode())) {
                            browseNodeId = sonEsAmazonProductListing.getBrowseNode();
                        }
                        if (StringUtils.isBlank(itemName) && StringUtils.isNotBlank(sonEsAmazonProductListing.getItemName())) {
                            itemName = sonEsAmazonProductListing.getItemName();
                        }
                        sonAsins.add(sonEsAmazonProductListing.getSonAsin());
                        sonSellerskus.add(sonEsAmazonProductListing.getSellerSku());
                        // 处理子产品记录
                        handelSonAsinData(fbaParentAsinSet, sonEsAmazonProductListing, amazonListingSonRelationshipTempList);
                    }

                    amazonListingParentRelationshipTemp.setOrderSale(orderSaleNum.get() > 0 ? true : false);
                    amazonListingParentRelationshipTemp.setFba(fbaParentAsinSet.contains(parntAsin) ? true : false);
                    //amazonListingParentRelationshipTemp.setOnline(esAmazonProductListing.getIsOnline());
                    amazonListingParentRelationshipTemp.setItemName(itemName);

                    amazonListingParentRelationshipTemp.setBrowseNodeId(browseNodeId);
                    // 双变体的：Size-Color
                    String variationTheme = getVariationTheme(colorNameFlag, sizeNameFlag);
                    amazonListingParentRelationshipTemp.setVariationTheme(variationTheme);
                    amazonListingParentRelationshipTemp.setBrandName(brand);
                    amazonListingParentRelationshipTemp.setManufacturer(manufacturer);
                    amazonListingParentRelationshipTemp.setModelNumber(modelNumber);
                    amazonListingParentRelationshipTemp.setChildAsins(JSON.toJSONString(sonAsins));
                    amazonListingParentRelationshipTemp.setChildSellerskus(JSON.toJSONString(sonSellerskus));

                    String remark = StringUtils.isBlank(itemName) ? "标题为空，暂不处理": StringUtils.isBlank(brand) ? "品牌为空，暂不处理" : StringUtils.isBlank(variationTheme) ? "属性为空，暂不处理" : null;
                    if (StringUtils.isNotBlank(remark)) {
                        amazonListingParentRelationshipTemp.setRemark(remark);
                        amazonListingParentRelationshipTemp.setProductSuccessStatus(false);
                    } else {
                        PublishRestoreListingRelationShipProcesser publishRestoreListingRelationShipProcesser = new PublishRestoreListingRelationShipProcesser(account);
                       ApiResult<String> productMessageXmlResult = publishRestoreListingRelationShipProcesser.getXmlByData(SpFeedType.POST_PRODUCT_DATA.getValue(),amazonListingParentRelationshipTemp,account);
                        if (productMessageXmlResult.isSuccess()){
                            amazonListingParentRelationshipTemp.setProductMessageXml(productMessageXmlResult.getResult());
                        }else {
                            amazonListingParentRelationshipTemp.setProductSuccessStatus(false);
                            amazonListingParentRelationshipTemp.setProductErrormsg(productMessageXmlResult.getErrorMsg());
                            amazonListingParentRelationshipTemp.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                        }
                        ApiResult<String> relationshipMessagXMlResult = publishRestoreListingRelationShipProcesser.getXmlByData(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue(),amazonListingParentRelationshipTemp,account);
                        if (relationshipMessagXMlResult.isSuccess()){
                            amazonListingParentRelationshipTemp.setRelationshipMessageXml(relationshipMessagXMlResult.getResult());
                        }else {
                            amazonListingParentRelationshipTemp.setProductSuccessStatus(false);
                            amazonListingParentRelationshipTemp.setRelationshipErrormsg(relationshipMessagXMlResult.getErrorMsg());
                            amazonListingParentRelationshipTemp.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                        }
                    }
                    amazonListingParentRelationshipTempService.insert(amazonListingParentRelationshipTemp);
                    amazonListingSonRelationshipTempService.batchInsert(amazonListingSonRelationshipTempList);
                }
            }catch (Exception e){
                String errorMsg = e.getMessage().substring(0, 240);;
                log.error("处理数据出错:" + errorMsg);
                //amazonListingParentRelationshipTempService.insert(amazonListingParentRelationshipTemp);
            }
        }
    }

    /**
     * 处理子体产品数据记录表
     * @param fbaParentAsinSet
     * @param esAmazonProductListing
     */
    private void handelSonAsinData(Set<String> fbaParentAsinSet, EsAmazonProductListing esAmazonProductListing,List<AmazonListingSonRelationshipTemp> amazonListingSonRelationshipTempList) {
        // 子体数据入记录表
        String parntAsin = esAmazonProductListing.getParentAsin();
        AmazonListingSonRelationshipTemp amazonListingSonRelationshipTemp = new AmazonListingSonRelationshipTemp();
        amazonListingSonRelationshipTemp.setAccountNumber(esAmazonProductListing.getAccountNumber());
        amazonListingSonRelationshipTemp.setSite(esAmazonProductListing.getSite());
        amazonListingSonRelationshipTemp.setParentAsin(parntAsin);
        amazonListingSonRelationshipTemp.setSonAsin(esAmazonProductListing.getSonAsin());
        amazonListingSonRelationshipTemp.setSellerSku(esAmazonProductListing.getSellerSku());
        amazonListingSonRelationshipTemp.setMainSku(esAmazonProductListing.getMainSku());
        amazonListingSonRelationshipTemp.setArticleNumber(esAmazonProductListing.getArticleNumber());
        amazonListingSonRelationshipTemp.setOrderSale(esAmazonProductListing.getOrder_num_total());
        amazonListingSonRelationshipTemp.setOrder30Sale(esAmazonProductListing.getOrder_last_30d_count());
        amazonListingSonRelationshipTemp.setFba(fbaParentAsinSet.contains(parntAsin) ? true : false);
        amazonListingSonRelationshipTemp.setItemStatus(esAmazonProductListing.getItemStatus());
        amazonListingSonRelationshipTemp.setOnline(esAmazonProductListing.getIsOnline());
        amazonListingSonRelationshipTemp.setItemName(esAmazonProductListing.getItemName());
        amazonListingSonRelationshipTemp.setProductType(esAmazonProductListing.getProductType());
        amazonListingSonRelationshipTemp.setBrowseNodeId(esAmazonProductListing.getBrowseNode());
        amazonListingSonRelationshipTemp.setColorName(esAmazonProductListing.getColorName());
        amazonListingSonRelationshipTemp.setSizeName(esAmazonProductListing.getSizeName());
        amazonListingSonRelationshipTemp.setBrandName(esAmazonProductListing.getBrandName());
        amazonListingSonRelationshipTemp.setManufacturer(esAmazonProductListing.getManufacturer());
        amazonListingSonRelationshipTemp.setModelNumber(esAmazonProductListing.getModelNumber());
        amazonListingSonRelationshipTemp.setCreateDate(new Timestamp(System.currentTimeMillis()));
        amazonListingSonRelationshipTempList.add(amazonListingSonRelationshipTemp);
    }

    private String getVariationTheme(Boolean colorNameFlag,Boolean sizeNameFlag){
        String variationTheme = null;
       if (colorNameFlag && BooleanUtils.isFalse(sizeNameFlag)){
           variationTheme="Color";
       }else if (sizeNameFlag && BooleanUtils.isFalse(colorNameFlag)){
            variationTheme="Size";
        }else if (colorNameFlag && sizeNameFlag){
           variationTheme = "Size-Color";
       }
       return variationTheme;
    }

}
