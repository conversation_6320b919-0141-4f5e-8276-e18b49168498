package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.amazon.componet.scheduler.model.AmazonTaskOfflineMessage;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTaskOffLinkListingLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Predicate;

/**
 * <p>
 * amazon任务下架链接记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface AmazonTaskOffLinkListingLogService extends IService<AmazonTaskOffLinkListingLog> {

    List<AmazonTaskOffLinkListingLog> addListingToOffLinkPool(List<EsAmazonProductListing> offLink, LocalDateTime statisticalDate, Integer taskType, BiConsumer<EsAmazonProductListing, AmazonTaskOffLinkListingLog> func);

    void statisticsAccountOffLinkLimitRate(AmazonAccountRelation accountRelation, LocalDateTime statisticalDate, String statisticsType, Double limitRatio);

    void offlineAccountLink(AmazonAccountRelation accountRelation, String taskType, LocalDateTime statisticalDate);

    void removeHasTodaySalesAsinsOffLinks(List<AmazonTaskOffLinkListingLog> offLinkList, List<Predicate<AmazonAsinSaleCountDO>> asinSaleCountCompareRules);

    void addExtraData(AmazonTaskOffLinkListingLog offLinkLog, Map<String, Object> addData);

    void offlineTask(AmazonTaskOfflineMessage message);
}
