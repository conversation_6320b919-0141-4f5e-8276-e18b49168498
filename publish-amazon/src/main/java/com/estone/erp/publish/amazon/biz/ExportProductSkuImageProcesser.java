package com.estone.erp.publish.amazon.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.IOUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.model.PoiCell;
import com.estone.erp.common.util.model.PoiCellStyle;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.model.AmazonPublishImagePath;
import com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample;
import com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample.Criteria;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.util.AmazonAutoPublishUtil;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.amazonImageGenerateRecord.model.AmazonImageGenerateRecord;
import com.estone.erp.publish.system.amazonImageGenerateRecord.service.AmazonImageGenerateRecordService;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.ComposeAndSuiteInfoVO;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * @Description: 导出产品货号的图片处理类
 * @ClassName: ExportProductSkuImageProcesser
 * @Author: Kevin
 * @Date: 2019/02/21
 * @Version: 0.0.1
 */
public class ExportProductSkuImageProcesser {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private AmazonAccount account;

    private List<String> productSkus;

    /**
     * 下载类型： 5 组合，1 spu
     */
    private String downloadType;

    /**
     * 取图类型
     */
    private String pictureType;

    private Map<String, List<StockKeepingUnitWithBLOBs>> productSku2SkusMap = new ConcurrentHashMap<>();

    private String imagePath;

    //图片单元格样式
    private PoiCellStyle imageCellStyle;

    //货号名称单元格样式
    private PoiCellStyle nameCellStyle;

    //产品货号单元格样式
    private PoiCellStyle productSkuCellStyle;

    //对象锁
    private Lock lock = new ReentrantLock();

    {
        imageCellStyle = new PoiCellStyle();
        imageCellStyle.setRowHeightInPoints(100f);
        imageCellStyle.setColumnWidth(4296);

        nameCellStyle = new PoiCellStyle();
        nameCellStyle.setColumnWidth(20 * 256);

        productSkuCellStyle = new PoiCellStyle();
        productSkuCellStyle.setColumnWidth(20 * 256);
        productSkuCellStyle.setRowHeightInPoints(45f);
    }

    private AmazonPublishImagePathService amazonPublishImagePathService = SpringUtils
            .getBean(AmazonPublishImagePathService.class);

    private AmazonImageGenerateRecordService amazonImageGenerateRecordService = SpringUtils
            .getBean(AmazonImageGenerateRecordService.class);

    public ExportProductSkuImageProcesser(AmazonAccount account, List<String> productSkus, String downloadType, String pictureType) {
        Asserts.notNull(account, "amazon account");
        this.account = account;
        this.imagePath = getImagePath(account.getAccountNumber());
        this.productSkus = productSkus;
        this.downloadType = downloadType;
        this.pictureType = pictureType;
    }

    public void exportAmazonImages(OutputStream os) {
        if (CollectionUtils.isEmpty(productSkus) || os == null) {
            log.warn("exportAmazonImages() param is not satisfy.");
            return;
        }

        Map<String, String> newUrl2NameMap = new ConcurrentHashMap<>();
        try {
            log.info("[{}] start export Amazon Images:[{}] -- .", account.getAccountNumber(), productSkus.size());
            long start = System.currentTimeMillis();
            // 复制图片
            Map<String, List<String>> productSKuImageMap = new LinkedHashMap<>(productSkus.size());
            Map<String, Map<String, String>> newProductSKuImageMap = new LinkedHashMap<>(productSkus.size());
            CountDownLatch countDownLatch = new CountDownLatch(productSkus.size());
            for (String productSku : productSkus) {
                AmazonExecutors.executeDownloadImage(() -> {
                    try {
                        List<String> images = new ArrayList<>();
                        if (StringUtils.isNotBlank(pictureType)) {
                            images = FmsUtils.getPictureUrlBySkuAndType(productSku, pictureType);
                        } else if (StringUtils.isNotBlank(downloadType) && downloadType.equals(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode().toString())) {
                            ApiResult<Map<String, List<String>>> apiResult = ProductUtils.getComposeSkuImageBySpu(List.of(productSku));
                            if (apiResult.isSuccess()) {
                                Map<String, List<String>> spImageMap = apiResult.getResult();
                                List<String> spImages = spImageMap.get(productSku);
                                if (CollectionUtils.isNotEmpty(spImages)) {
                                    images.addAll(spImages);
                                }
                            }
                        } else {
                            String type = PictureTypeEnum.AMAZON_EXCLUSIVE_IMAGE.getName();
                            images = FmsUtils.getPictureUrlBySkuAndType(productSku, type);
                            if (CollectionUtils.isEmpty(images)) {
                                type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
                                images = FmsUtils.getPictureUrlBySkuAndType(productSku, type);
                            }
                            if (CollectionUtils.isEmpty(images)) {
                                type = PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName();
                                images = FmsUtils.getPictureUrlBySkuAndType(productSku, type);
                            }
                        }

                        if (CollectionUtils.isNotEmpty(images)) {
                            List<String> imageUrls = images.stream().filter((this::isImgFormat)).collect(Collectors.toList());
                            Map<String, String> tempMap = AmazonUtils.copyImagesToAliOSS(productSku, imageUrls, imagePath);
                            if (MapUtils.isNotEmpty(tempMap)) {
                                tempMap.forEach((k, v) -> {
                                    if (StringUtils.isNotEmpty(v)) {
                                        newUrl2NameMap.put(v, getImageName(k));
                                    }
                                });
                            }

                            productSKuImageMap.put(productSku, new ArrayList<>(tempMap.values()));
                            newProductSKuImageMap.put(productSku, tempMap);
                        }
                        List<String> articleNumberList = new ArrayList<>(1);
                        articleNumberList.add(productSku);
                        // 组合 套装单独调用
                        List<ProductInfo> productInfoList = new ArrayList<>();
                        if (StringUtils.isNotBlank(downloadType) && downloadType.equals(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode().toString())) {
                            // 调用套装接口
                            List<ComposeAndSuiteInfoVO> composeAndSuiteInfoVOList = ProductUtils.getComposeAndSuitePublishInfoVo(new ArrayList<>(Arrays.asList(productSku)));
                            ProductInfo productInfo = new ProductInfo();
                            productInfo.setMainSku(productSku);
                            productInfo.setSonSku(productSku);
                            if (CollectionUtils.isEmpty(composeAndSuiteInfoVOList)) {
                               // log.error(productSku + " :getComposeAndSuitePublishInfoVo 查询组合sku信息为空");
                            } else {
                                ComposeAndSuiteInfoVO composeAndSuiteInfoVO = composeAndSuiteInfoVOList.get(0);
                                productInfo.setFirstImage(composeAndSuiteInfoVO.getImage());
                                productInfo.setName(composeAndSuiteInfoVO.getName());
                            }
                            productInfoList.add(productInfo);
                        } else if (StringUtils.isNotBlank(downloadType) && downloadType.equals(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode().toString())) {
                            // 处理试卖Spu图片下载
                            List<String> spuList = new ArrayList<>(Arrays.asList(productSku));
                            ApiResult<Map<String, List<String>>> apiResult = ProductUtils.getComposeSkuQuerySonSku(spuList);
                            if (apiResult.isSuccess()) {
                                Map<String, List<String>> sonSkuMap = apiResult.getResult();
                                List<String> spuSonSkus = sonSkuMap.get(productSku);
                                spuSonSkus.forEach(spuSonSku -> {
                                    ProductInfo productInfo = new ProductInfo();
                                    productInfo.setMainSku(productSku);
                                    productInfo.setSonSku(spuSonSku);
                                    productInfo.setName(spuSonSku);
                                    productInfoList.add(productInfo);
                                });

                            } else {
                                log.error(productSku + " :getComposeSkuImageBySpu 查询试卖spu图片信息失败: " + apiResult.getErrorMsg());
                                productInfoList.addAll(ProductUtils.findSkuImage(articleNumberList));
                            }
                        } else {
                            productInfoList.addAll(ProductUtils.findSkuImage(articleNumberList));
                        }
                        List<StockKeepingUnitWithBLOBs> skuList = this.handleStockKeepingUnitWithBLOBsInfo(productInfoList);
                        if (CollectionUtils.isNotEmpty(skuList)) {
                            if (StringUtils.isNotBlank(downloadType) && downloadType.equals(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode().toString())) {
                                skuList.forEach(sku -> {
                                    // 根据货号找主图
                                    sku.setProductImage(getKey(newUrl2NameMap, sku.getArticleNumber()));
                                });
                                productSku2SkusMap.put(productSku, skuList);
                            } else {
                                skuList.forEach(sku -> {
                                    // 根据货号找主图
                                    sku.setProductImage(getKey(newUrl2NameMap, sku.getArticleNumber()));
                                });
                                productSku2SkusMap.put(productSku, skuList);
                            }

                        }
                    } catch (Exception e) {
                        log.error("执行错误", e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                if (!countDownLatch.await(30, TimeUnit.MINUTES)) {
                    log.error("等待图片下载任务完成时发生超时。");
                    // 可以选择抛出异常或采取其他措施
                    throw new RuntimeException("等待图片下载任务完成时发生超时。");
                }
            } catch (InterruptedException e) {
                log.error("等待图片下载任务完成时被中断。", e);
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待图片下载任务完成时被中断。", e);
            }



            // 添加图片记录
            AmazonImageGenerateRecord record = new AmazonImageGenerateRecord();
            record.setCreationtime(new Date());
            record.setImageurl(JSON.toJSONString(newUrl2NameMap.keySet()));
            amazonImageGenerateRecordService.insertImageRecord(record);

            long t1 = System.currentTimeMillis();
            log.info("Copy image cost time:[{}] ms, img size:[{}]", t1 - start, newUrl2NameMap.size());
            //Map<String, PoiCell> url2PoiCellMap = getUrl2ImageMap(newUrl2NameMap.keySet());
            //long t2 = System.currentTimeMillis();
            //log.info("Get image poi cost time:[{}] ms", t2 - t1);

            final List<List<PoiCell>> row = new ArrayList<>(1);
            POIUtils.createImageExcel(getSidewardsHeaders(getMaxLength(productSKuImageMap) + 3),
                    getSidewardsExcelListData(newProductSKuImageMap, /*url2PoiCellMap, newUrl2NameMap,*/ productSku2SkusMap),
                    items -> {
                        row.clear();
                        row.add(items);
                        return row;
                    }, true, os);
            log.info("[{}] finish export Amazon Images: {}.", account.getAccountNumber(), productSkus.size());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //AmazonUtils.deleteImages(newUrl2NameMap.keySet());
            //log.warn("delete all generate image success, sku{}", productSkus);
        }
    }

    private List<StockKeepingUnitWithBLOBs> handleStockKeepingUnitWithBLOBsInfo(List<ProductInfo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        List<StockKeepingUnitWithBLOBs> skus = new ArrayList<>();
        StockKeepingUnitWithBLOBs sku = null;
        for (ProductInfo productInfo : productInfoList) {
            sku = new StockKeepingUnitWithBLOBs();
            sku.setProductarticlenumber(productInfo.getMainSku());
            sku.setArticleNumber(productInfo.getSonSku());
            //主图
            if (StringUtils.isNotEmpty(productInfo.getFirstImage())) {
                sku.setProductImage(productInfo.getFirstImage());
            }
            skus.add(sku);
        }
        return skus;
    }

    /**
     * 读取图片资料，压缩图片
     *
     * @param url   图片url
     * @param retry 图片读取重试次数
     * @return
     * @Author: listen
     * @Date 2019/3/12 10:01
     * @Version: 0.0.1
     */
    private PoiCell readImage2PoiCell(String url, int retry) {
        PoiCell poiCell = null;
        if (retry < 0) {
            return null;
        }
        ByteArrayOutputStream byteArrayOut = null;
        try {
            BufferedImage read = ImageIO.read(new URL(url));
            if (read == null) {
                return readImage2PoiCell(url, retry - 1);
            }
            // 缩放，最大边缩放到100，另一边等比缩放
            double min = Math.min(100d / read.getHeight(), 100d / read.getWidth());
            int width = (int) (read.getWidth() * min), height = (int) (read.getHeight() * min);
            Image image = read.getScaledInstance(width, height, Image.SCALE_DEFAULT);
            BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics g = tag.getGraphics();
            g.drawImage(image, 0, 0, null);
            g.dispose();
            byteArrayOut = new ByteArrayOutputStream();
            //ImageIO.write(tag, "BMP", byteArrayOut);
            ImageIO.write(tag, "JPEG", byteArrayOut);
            poiCell = new PoiCell(byteArrayOut.toByteArray());
            poiCell.setPoiCellStyle(imageCellStyle);
            byteArrayOut.reset();
            return poiCell;
        } catch (IOException e) {
           // log.warn("residue time:[{}], error:[{}], img:[{}],", retry, e.getMessage(), url);
            return readImage2PoiCell(url, retry - 1);
        } finally {
            IOUtils.close(byteArrayOut);
        }
    }

    /**
     * 横版排版数据
     *
     * @param productSKuImageMap 产品货号2图片list的map
     * @param productSku2SkusMap 产品货号2货号list的map
     * @return
     * @Author: listen
     * @Date 2019/3/12 9:16
     * @Version: 0.0.1
     */
    private List<List<PoiCell>> getSidewardsExcelListData(Map<String, Map<String, String>> productSKuImageMap,
                                                          //Map<String, PoiCell> url2PoiCellMap, Map<String, String> newUrl2NameMap,
                                                          Map<String, List<StockKeepingUnitWithBLOBs>> productSku2SkusMap) {
        List<List<PoiCell>> listData = new ArrayList<>();
        for (Entry<String, Map<String, String>> stringMapEntry : productSKuImageMap.entrySet()) {
            List<String> skuList = productSku2SkusMap.get(stringMapEntry.getKey()).stream().map(StockKeepingUnitWithBLOBs::getArticleNumber).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuList)) {
                List<PoiCell> productSkuRowData = new ArrayList<>(1);
                PoiCell productSkuCell = new PoiCell(stringMapEntry.getKey());
                productSkuCell.setPoiCellStyle(productSkuCellStyle);
                productSkuRowData.add(productSkuCell);
                listData.add(productSkuRowData);
                // 遍历sku列表，添加货号
                Collection<String> images = stringMapEntry.getValue().keySet();
                for (int i = 0; i < skuList.size(); i++) {
                    // 通过sku获取图片信息
                    List<String> skuReferImages = AmazonAutoPublishUtil.getSpuReferImages(stringMapEntry.getKey(), skuList.get(i), skuList, images);
                    if (CollectionUtils.isEmpty(skuReferImages)) {
                        continue;
                    }
                    // 图片地址
                    List<PoiCell> rowData = new ArrayList<>(skuReferImages.size() + 2);
                    //第一列是货号
                    PoiCell cell = new PoiCell(skuList.get(i));
                    cell.setPoiCellStyle(nameCellStyle);
                    rowData.add(cell);
                    //第二列是主图地址和样品图
                    String sku = skuList.get(i);
                    String productFirstCreateTime = ProductUtils.getProductFirstCreateTime(sku);
                    if (productFirstCreateTime == null) {
                        productFirstCreateTime = ProductUtils.getComposeProductFirstCreateTime(sku);
                    }
                    String skuMainImage = AmazonAutoPublishUtil.getSkuMainImage(sku, new ArrayList<>(images), productFirstCreateTime);
                    if (StringUtils.isNotBlank(skuMainImage)) {
                        Map<String, String> values = stringMapEntry.getValue();
                        String mapping = values.get(skuMainImage);
                        //log.info("sku:[{}], image:[{}], mapping:[{}]", sku, skuMainImage, mapping);
                        PoiCell poiCell = new PoiCell(mapping);
                        rowData.add(poiCell);
                    }
                    //后面全都是附图地址
                    for (String skuReferImage : skuReferImages) {
                        Map<String, String> values = stringMapEntry.getValue();
                        rowData.add(new PoiCell(values.get(skuReferImage)));
                    }
                    listData.add(rowData);
                }
            }
        }
        return listData;
    }

    private int getMaxLength(Map<String, List<String>> productSKuImageMap) {
        int max = 0;
        for (List<String> value : productSKuImageMap.values()) {
            int length = CollectionUtils.size(value);
            if (length > max) {
                max = length;
            }
        }

        return max;
    }

    /**
     * 横版排版表头  -空着
     *
     * @param headerSize
     * @return
     * @Author: listen
     * @Date 2019/3/12 9:15
     * @Version: 0.0.1
     */
    private String[] getSidewardsHeaders(int headerSize) {
        String[] headers = new String[headerSize];
        for (int i = 0; i < headerSize; i++) {
            headers[i] = "";
        }

        return headers;
    }

    /**
     * @param accountNumber 账号名
     * @return 图片路径
     * @Description: 获取图片路径
     * @Author: Kevin
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    public synchronized String getImagePath(String accountNumber) {
        AmazonPublishImagePath amazonPublishImagePath = amazonPublishImagePathService
                .getAmazonPublishImagePath(accountNumber);
        if (amazonPublishImagePath != null) {
            return amazonPublishImagePath.getImagePath();
        }

        String imagePath = null;
        // 给账号设置图片路径
        AmazonPublishImagePathExample example = new AmazonPublishImagePathExample();
        Criteria criteria = example.createCriteria();
        do {
            imagePath = "/" + RandomStringUtils.randomAlphanumeric(8) + "/" + RandomStringUtils.randomAlphanumeric(16)
                    + "/";
            criteria.andImagePathEqualTo(imagePath);
            if (CollectionUtils.isEmpty(amazonPublishImagePathService.findByExample(example))) {
                amazonPublishImagePath = new AmazonPublishImagePath();
                amazonPublishImagePath.setAccountNumber(accountNumber);
                amazonPublishImagePath.setImagePath(imagePath);
                amazonPublishImagePathService.insert(amazonPublishImagePath);
                break;
            }
        }
        while (true);

        return imagePath;
    }

    /**
     * 判断图片名称是否合规，排除无后缀及GIF文件
     *
     * @param name
     * @return
     * @Author: listen
     * @Date 2019/3/8 20:00
     * @Version: 0.0.1
     */
    private boolean isImgFormat(String name) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        if (name.lastIndexOf(".") < name.length() - 2) {
            String suffixUpp = name.substring(name.lastIndexOf(".") + 1);
            if (suffixUpp.toUpperCase().matches("^[(JPG)|(JPEG)|(PNG)|(BMP)]+$")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取图片名称
     *
     * @param url
     * @return
     * @Author: listen
     * @Date 2019/3/18 17:25
     * @Version: 0.0.1
     */
    private String getImageName(String url) {
        String name = "";
        if (StringUtils.isEmpty(url)) {
            return name;
        }
        if (url.lastIndexOf("/") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1);
        }
        if (name.lastIndexOf(".") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."));
        }
        return name;
    }

    /**
     * 根据map的value获取map的第一个key
     *
     * @param map
     * @param value
     * @return
     * @Author: listen
     * @Date 2019/3/21 14:59
     * @Version: 0.0.1
     */
    private String getKey(Map<String, String> map, String value) {
        String key = "";
        if (MapUtils.isEmpty(map) || StringUtils.isEmpty(value)) {
            return key;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
//            String skuKDLow = value + "-kd-";
//            String skuKDUp = value + "-KD-";
            String sku00 = value + "-00.";
            String sku000 = value + "-000.";
            if (value.equals(entry.getValue())
                    || entry.getValue().contains(sku00) || entry.getValue().contains(sku000)) {
                key = entry.getKey();
                break;
            }
        }
        return key;
    }

    private Random rand = ThreadLocalRandom.current();

    /**
     * 随机排序数组
     *
     * @param list
     * @return
     * @Author: listen
     * @Date 2019/3/12 9:14
     * @Version: 0.0.1
     */
    private List<String> randomList(List<String> list) {
        int max = list.size();
        Set<Integer> set = new LinkedHashSet<>(max);
        while (set.size() < max) {
            set.add(rand.nextInt(max));
        }
        List<Integer> tl = new ArrayList<>(set);
        List<IntSort> tt = new ArrayList<>(max);
        for (int i = 0; i < max; i++) {
            IntSort sort = new IntSort();
            sort.setId(tl.get(i));
            sort.setValue(list.get(i));
            tt.add(sort);
        }
        return tt.stream().sorted(Comparator.comparing(IntSort::getId)).map(IntSort::getValue)
                .collect(Collectors.toList());
    }

}

@Data
class IntSort {

    Integer id;

    String value;
}
