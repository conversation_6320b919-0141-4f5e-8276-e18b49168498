package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.data.domain.Page;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: ${同步父asin}
 */
@Slf4j
public class SyncAmazonProductListingParentAsinMqListener implements ChannelAwareMessageListener {

    private static final List<String> recordAccountNumberList = new ArrayList<>(Arrays.asList("US-lokio854","US-swenbend","ES-iwutoa","US-xdxdxdwww87"));

    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    private static final List<String> fileds = Arrays.asList("sellerSku","site");

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }

        Boolean isSuccess = doService(body);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private Boolean doService(String body) {
        Boolean isSuccess = true;
        try {
            EsAmazonProductListingRequest esAmazonProductListingRequest = JSON.parseObject(body, EsAmazonProductListingRequest.class);
            String accountNumber= esAmazonProductListingRequest.getAccountNumber();
            if (StringUtils.isEmpty(accountNumber)){
                log.warn("账号信息为空" + accountNumber );
                return false;
            }

            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setDownFields(fileds);
            log.info("正在后台同步" + accountNumber + "账号父asin相关信息");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            int pageNumber = 0;
            int limit = 1000;
            int total = 0;
            boolean recordFlag = recordAccountNumberList.contains(accountNumber) || (StringUtils.isNotBlank(esAmazonProductListingRequest.getSaleQuantityBean()))? true: false;
            while (true) {
                try {
                    Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest, limit, pageNumber);
                    List<EsAmazonProductListing> listingList = page.getContent();
                    amazonProductListingService.syncListingDetailForBatchSellersku(accountNumber,listingList,recordFlag);
                    total += listingList.size();
                    if (CollectionUtils.isEmpty(listingList) || total < limit) {
                        break;
                    }
                    pageNumber++;
                } catch (Exception e) {
                    log.error("同步父asin异常:", e);
                    break;
                }
            }
            stopWatch.stop();
            log.info("店铺listing同步父asin完成:{},累计处理：{}条, 耗时：{}", accountNumber, total, stopWatch);
        } catch (Exception e) {
            log.error("同步父asin解析 mq消息体异常 -> {}", body);
        };
        return isSuccess;
    }
}