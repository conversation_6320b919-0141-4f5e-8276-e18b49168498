package com.estone.erp.publish.amazon.mq.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Amazon 刊登消息
 *
 * <AUTHOR>
 * @date 2024-11-14 14:53
 */
@Data
public class AmazonPublishMessage {

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 刊登用户
     */
    private String user;

    /**
     * 货号
     */
    private String spu;

    /**
     * FollowSellId 跟卖ID
     */
    private Integer followSellId;

    /**
     * 站点
     */
    private String site;

    /**
     * 刊登角色
     * @see com.estone.erp.publish.common.enums.PublishRoleEnum
     */
    private Integer publishRole;

    /**
     * 模版ID
     */
    private Integer templateId;

    /**
     * 定时队列Id
     */
    private Integer timePublishQueueId;

    /**
     * 处理报告Id
     */
    private Long reportId;

    /**
     * 数据来源
     *
     * @see com.estone.erp.publish.platform.enums.SkuDataSourceEnum
     */
    private Integer skuDataSource;

    /**
     * 刊登类型
     *
     * @see com.estone.erp.publish.amazon.componet.publish.enums.AmazonPublishTypeEnums
     */
    private Integer publishType;


    /**
     * asin 刊登
     */
    private Map<String, String> skuAsinMap = new HashMap<>();

    /**
     * 指定产品类型
     */
    private String productType;

    /**
     * 刊登时间
     */
    private LocalDateTime publishTime;

}
