package com.estone.erp.publish.amazon.call.xsd;

import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.call.xsd.model.XsdAttr;
import com.estone.erp.publish.amazon.call.xsd.model.XsdElement;
import com.estone.erp.publish.amazon.call.xsd.model.XsdRestriction;
import com.estone.erp.publish.amazon.call.xsd.model.XsdType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/***
 * xsd模型构建类
 */
@Slf4j
public class XsdBuilder {
    /**
     * 全局静态xsd文件的XsdBuilder map
     */
    public static final Map<String, XsdBuilder> XSD_BUILDERS = new LinkedHashMap<String, XsdBuilder>();

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 引用的builder list
     */
    private List<XsdBuilder> includeBuilders = new ArrayList<XsdBuilder>();
    /**
     * xsd中元素Element map
     */
    private Map<String, Element> elements = new LinkedHashMap<String, Element>();

    /**
     * xsd中元素Type(包括BaseType，SimpleType，ComplexType) map
     */
    private Map<String, Element> types = new LinkedHashMap<String, Element>();

    /**
     * 自定义XsdElement map
     */
    public Map<String, XsdElement> xsdElements = new LinkedHashMap<String, XsdElement>();

    /**
     * 自定义XsdType map
     */
    public Map<String, XsdType> xsdTypes = new LinkedHashMap<String, XsdType>();

    /**
     * 
     * @Constructor: 根据文件名创建builder
     *
     * @param fileName 文件名
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdBuilder(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 
     * @Description: 根据文件名获取Xsd Builder
     *
     * @param fileName 文件名
     * @return XsdBuilder
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public static XsdBuilder getXsdBuilder(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return null;
        }

        XsdBuilder builder = XSD_BUILDERS.get(fileName);
        if (builder == null) {
            new XsdBuilder(fileName).build();
            builder = XSD_BUILDERS.get(fileName);
        }

        return builder;
    }

    /**
     * 
     * @Description: 解析xsd文件并构建Xsdbuilder对象
     *
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    @SuppressWarnings("unchecked")
    public void build() {
        try {
            xsdElements.clear();
            xsdTypes.clear();

            // 判断文件是否存在
            File file = new File(XsdUtils.xsdPath + fileName);
            if (!file.exists()) {
                throw new RuntimeException(file.getPath() + "---文件不存在");
            }
            Document doc = DocumentHelper.parseText(FileUtils.readFileToString(file));
            // 获取根节点元素和其子元素
            Element root = doc.getRootElement();
            List<Element> rootSub = root.elements();
            for (Element element : rootSub) {
                String name = element.getName();
                String nameAttr = XsdUtils.attrValue2Str(element, "name");
                // 解析依赖的xsd文件，并设置Element map和Type map
                if ("include".equals(name)) {
                    String schemaLaction = XsdUtils.attrValue2Str(element, "schemaLocation");
                    XsdBuilder builder = XSD_BUILDERS.get(schemaLaction);
                    if (builder == null) {
                        builder = new XsdBuilder(schemaLaction);
                        builder.build();
                    }
                    includeBuilders.add(builder);
                }
                else if ("complexType".equals(name) || "simpleType".equals(name)) {
                    types.put(nameAttr, element);
                }
                else if ("element".equals(name)) {
                    elements.put(nameAttr, element);
                }
            }

            // 根据Type map解析XsdType map
            for (Entry<String, Element> entry : types.entrySet()) {
                String name = entry.getKey();
                XsdType xsdType = recursiveXsdType(name);
                xsdTypes.put(name, xsdType);
            }
            // 根据Element map解析XsdElement map
            int eleSeqIndex = 1;
            for (Entry<String, Element> entry : elements.entrySet()) {
                String name = entry.getKey();
                Element element = entry.getValue();
                XsdElement xsdEle = recursiveXsdElement(element);
                xsdEle.setSeqIndex(eleSeqIndex++);
                xsdElements.put(name, xsdEle);
            }
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }

        XSD_BUILDERS.put(fileName, this);
    }

    /**
     * 
     * @Description: 递归获取XsdElement
     *
     * @param element xml element元素
     * @return
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdElement recursiveXsdElement(Element element) {
        String ref = XsdUtils.attrValue2Str(element, "ref");
        XsdElement xsdEle = null;
        // 判断是否为引用元素，是则从Element map中获取引用元素的xml Element，并返回
        if (StringUtils.isNotEmpty(ref)) {
            xsdEle = getXsdElement(ref);
            if (xsdEle == null) {
                xsdEle = recursiveXsdElement(elements.get(ref));
            }
            xsdEle.setMinOccurs(XsdUtils.attrValue2Int(element, "minOccurs"));
            String maxOccurs = XsdUtils.attrValue2Str(element, "maxOccurs");
            xsdEle.setRequired(xsdEle.getMinOccurs() == null && StringUtils.isEmpty(maxOccurs));
            if (StringUtils.isNotEmpty(maxOccurs)) {
                xsdEle.setMaxOccurs("unbounded".equals(maxOccurs) ? -1 : Integer.valueOf(maxOccurs));
            }

            return xsdEle;
        }

        // 解析Element的值
        xsdEle = new XsdElement();
        xsdEle.setName(XsdUtils.attrValue2Str(element, "name"));
        xsdEle.setMinOccurs(XsdUtils.attrValue2Int(element, "minOccurs"));
        String maxOccurs = XsdUtils.attrValue2Str(element, "maxOccurs");
        xsdEle.setRequired(xsdEle.getMinOccurs() == null && StringUtils.isEmpty(maxOccurs));
        if (StringUtils.isNotEmpty(maxOccurs)) {
            xsdEle.setMaxOccurs("unbounded".equals(maxOccurs) ? -1 : Integer.valueOf(maxOccurs));
        }
        // 设置 XsdElement的XsdType
        xsdEle.setType(recursiveInnerXsdType(element));
        // 设置 XsdElement的Documentation
        xsdEle.setDocumentations(getDocumentations(element));
        // 设置 XsdElement的Attribute
        xsdEle.setAttrs(getAttributes(element));

        return xsdEle;
    }

    /**
     * 
     * @Description: 递归获取XsdType
     *
     * @param type Type的type值
     * @return XsdType
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdType recursiveXsdType(String type) {
        XsdType xsdType = getXsdType(type);
        if (xsdType != null) {
            return xsdType;
        }
        // 如果是xml element是BaseType，则直接构建XsdType
        if (type.startsWith("xsd:")) {
            xsdType = new XsdType();
            xsdType.setAttrs(null);
            xsdType.setHasRestriction(false);
            xsdType.setName(type);
            xsdTypes.put(type, xsdType);

            return XsdUtils.clone(xsdType);
        }

        Element element = types.get(type);
        xsdType = new XsdType();
        xsdType.setName(type);
        xsdType = recursiveType(xsdType, element);
        xsdTypes.put(type, xsdType);
        if (xsdType != null) {
            return XsdUtils.clone(xsdType);
        }

        return null;
    }

    /**
     * 
     * @Description: 递归获取XsdType
     *
     * @param element xml element元素
     * @return XsdType
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdType recursiveInnerXsdType(Element element) {
        String type = XsdUtils.attrValue2Str(element, "type");
        if (StringUtils.isNotEmpty(type)) {
            return recursiveXsdType(type);
        }
        else {
            XsdType xsdType = new XsdType();
            xsdType = recursiveType(xsdType,
                    element.element(element.element("simpleType") != null ? "simpleType" : "complexType"));
            return xsdType;
        }
    }

    /**
     * 
     * @Description: 递归获取XsdType
     *
     * @param xsdType xsdType
     * @param element xml element元素
     * @return XsdType
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    @SuppressWarnings("unchecked")
    public XsdType recursiveType(XsdType xsdType, Element element) {
        if (element == null) {
            return null;
        }

        // 处理SimpleType
        if ("simpleType".equals(element.getName())) {
            Element restriction = element.element("restriction");
            xsdType.setHasRestriction(restriction != null);
            if (xsdType.isHasRestriction()) {
                XsdType restrictionXsdType = recursiveXsdType(XsdUtils.attrValue2Str(restriction, "base"));
                restrictionXsdType.setHasRestriction(true);
                restrictionXsdType.setRestriction(recursiveRestriction(restriction));

                return restrictionXsdType;
            }
        }
        else {
            // 处理ComplexType
            Element simpleContent = element.element("simpleContent");
            if (simpleContent != null) {
                // 处理继承属性extension
                Element extension = simpleContent.element("extension");
                if (extension != null) {
                    XsdType extensionXsdType = recursiveXsdType(XsdUtils.attrValue2Str(extension, "base"));
                    extensionXsdType
                            .setAttrs(XsdUtils.mergeNewList(getAttributes(extension), extensionXsdType.getAttrs()));

                    return extensionXsdType;
                }
                else {
                    // 处理限定属性restriction
                    Element restriction = simpleContent.element("restriction");
                    xsdType.setHasRestriction(restriction != null);
                    if (xsdType.isHasRestriction()) {
                        XsdType restrictionXsdType = recursiveXsdType(XsdUtils.attrValue2Str(restriction, "base"));
                        restrictionXsdType.setRestriction(recursiveRestriction(restriction));
                        restrictionXsdType.setHasRestriction(true);

                        return restrictionXsdType;
                    }
                }
            }
            else {
                // 处理元素sequence， choice
                String finalStatus = XsdUtils.getFinalStatus(element);
                xsdType.setFinalStatus(finalStatus);
                if (StringUtils.isNotEmpty(finalStatus)) {
                    Element finalEle = element.element(finalStatus);
                    List<Element> subEles = finalEle.elements();
                    List<XsdElement> xsdEles = new ArrayList<XsdElement>();
                    xsdType.setElements(xsdEles);
                    xsdType.setIsSimple(false);
                    for (Element sub : subEles) {
                        String subName = sub.getName();
                        if ("element".equals(subName)) {
                            xsdEles.add(recursiveXsdElement(sub));
                        }
                    }
                }
            }
        }

        // 设置元素Attribute
        xsdType.setAttrs(getAttributes(element));
        return xsdType;
    }

    /**
     * 
     * @Description: 递归获取限定属性 restriction
     *
     * @param element xml element元素
     * @return
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    @SuppressWarnings("unchecked")
    public XsdRestriction recursiveRestriction(Element element) {
        XsdRestriction xsdRestriction = new XsdRestriction();
        // xsdRestriction.setBase(XsdUtils.attrValue2Str(element, "base"));
        List<Element> enumerations = element.elements("enumeration");
        if (CollectionUtils.isNotEmpty(enumerations)) {
            xsdRestriction.setEnumerations(enumerations.stream().map(enumeration -> {
                return XsdUtils.attrValue2Str(enumeration, "value");
            }).collect(Collectors.toList()));
        }
        xsdRestriction.setFractionDigits(XsdUtils.subAttrValue2Int(element, "fractionDigits", "value"));
        xsdRestriction.setLength(XsdUtils.subAttrValue2Int(element, "length", "value"));
        xsdRestriction.setMaxExclusive(XsdUtils.subAttrValue2Double(element, "maxExclusive", "value"));
        xsdRestriction.setMaxInclusive(XsdUtils.subAttrValue2Double(element, "maxInclusive", "value"));
        xsdRestriction.setMaxLength(XsdUtils.subAttrValue2Int(element, "maxLength", "value"));
        xsdRestriction.setMinExclusive(XsdUtils.subAttrValue2Double(element, "minExclusive", "value"));
        xsdRestriction.setMinInclusive(XsdUtils.subAttrValue2Double(element, "minInclusive", "value"));
        xsdRestriction.setMinLength(XsdUtils.subAttrValue2Int(element, "minLength", "value"));
        xsdRestriction.setPattern(XsdUtils.subAttrValue2Str(element, "pattern", "value"));
        xsdRestriction.setTotalDigits(XsdUtils.subAttrValue2Int(element, "totalDigits", "value"));
        xsdRestriction.setWhiteSpace(XsdUtils.subAttrValue2Str(element, "whiteSpace", "value"));

        return xsdRestriction;
    }

    /**
     * 
     * @Description: 获取xml element元素的 attribute
     *
     * @param element
     * @return XsdAttr集合
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    @SuppressWarnings("unchecked")
    private List<XsdAttr> getAttributes(Element element) {
        List<Element> attributes = element.elements("attribute");
        if (CollectionUtils.isNotEmpty(attributes)) {
            return attributes.stream().map(attribute -> {
                XsdAttr xsdAttr = new XsdAttr();
                xsdAttr.setName(XsdUtils.attrValue2Str(attribute, "name"));
                xsdAttr.setUse(XsdUtils.attrValue2Str(attribute, "use"));
                xsdAttr.setType(recursiveInnerXsdType(attribute));

                return xsdAttr;
            }).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * 
     * @Description: 获取xml element元素的 documentation
     *
     * @param element
     * @return String集合
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    @SuppressWarnings("unchecked")
    private List<String> getDocumentations(Element element) {
        Element annotation = element.element("annotation");
        if (annotation != null) {
            List<Element> documentations = annotation.elements("documentation");
            if (CollectionUtils.isNotEmpty(documentations)) {
                return documentations.stream().map(enumeration -> {
                    return enumeration.getTextTrim();
                }).collect(Collectors.toList());
            }
        }

        return null;
    }

    /**
     * 
     * @Description: 根据name获取XsdElement
     *
     * @param name xsdElement name
     * @return XsdElement
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdElement getXsdElement(String name) {
        XsdElement xsdElement = xsdElements.get(name);
        if (xsdElement == null && CollectionUtils.isNotEmpty(includeBuilders)) {
            for (XsdBuilder builder : includeBuilders) {
                xsdElement = builder.getXsdElement(name);
                if (xsdElement != null) {
                    break;
                }
            }
        }
        if (xsdElement != null) {
            xsdElement = XsdUtils.clone(xsdElement);
        }

        return xsdElement;
    }

    /**
     * 
     * @Description: 根据name获取XsdType
     *
     * @param type XsdType name
     * @return XsdType
     * @Author: Kevin
     * @Date: 2018/08/15
     * @Version: 0.0.1
     */
    public XsdType getXsdType(String type) {
        XsdType xsdType = xsdTypes.get(type);
        // 先查询依赖的xsdBuilder，再查询本身
        if (xsdType == null && CollectionUtils.isNotEmpty(includeBuilders)) {
            for (XsdBuilder builder : includeBuilders) {
                xsdType = builder.getXsdType(type);
                if (xsdType != null) {
                    break;
                }
            }
        }
        if (xsdType != null) {
            xsdType = XsdUtils.clone(xsdType);
        }

        return xsdType;
    }

    public String getFileName() {
        return fileName;
    }

    public List<XsdBuilder> getIncludeBuilders() {
        return includeBuilders;
    }

    public Map<String, Element> getElements() {
        return elements;
    }

    public Map<String, Element> getTypes() {
        return types;
    }

    public Map<String, XsdElement> getXsdElements() {
        return xsdElements;
    }

    public Map<String, XsdType> getXsdTypes() {
        return xsdTypes;
    }
}
