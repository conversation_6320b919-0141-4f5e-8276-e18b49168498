package com.estone.erp.publish.amazon.enums;

import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * amazon 下架任务枚举
 */
public interface AmazonOfflineEnums {

    @Getter
    @AllArgsConstructor
    enum LinkType {
        // 不可下架链接 链接类型
        ORDER(1, "有销量"),
        FBA(2, "FBA"),
        PRIORITY_SITE(3, "优先站点"),
        SKU_LOW_THREADS(4, "超量下架-链接总量低于阈值"),
        SKU_HIGH_THREADS(5, "超量下架-链接总量高于阈值"),
        EXIST_BASIC_SITE_SALE(6, "四大站点有销量"),
        PARENT_ASIN_IS_AFTER_DAY(7, "父ASIN最新上架时间不满足");

        private final int code;
        private final String desc;
    }



    @Getter
    @AllArgsConstructor
    enum Type {
        OVER_LIMIT(1, "超额刊登，系统自动下架",ProcessingReportTriggleType.NewListing.name()),
        NO_SALES_FOR_DAY(2, "%s天无销量，系统自动下架", ProcessingReportTriggleType.NewListing.name()),
        STOP_DELETE(3, "停产30天系统自动删除",ProcessingReportTriggleType.Listing_Stop_30_Auto_Delete.name()),
        ARCHIVED_DELETE(4, "存档7天系统自动删除", ProcessingReportTriggleType.Listing_Archived_7_Auto_Delete.name()),
        ParentAsin_Auto_Delete(5,"子ASIN为空的父asin，系统自动下架",ProcessingReportTriggleType.Listing_Not_SonAsin_ParentAsin_Auto_Delete.name()),
        Infringement_Type_ForbidChannel_Delete(6,"禁售类型为侵权，禁售平台为亚马逊的在线产品系统自动下架",ProcessingReportTriggleType.Listing_Infringement_Type_ForbidChannel_Auto_Delete.name()),
        Category_Auto_Delete(7,"成人用品系统自动删除",ProcessingReportTriggleType.Listing_Category_Auto_Delete.name()),
        Order_Num_Delete(8,"180天无销量系统自动下架",ProcessingReportTriggleType.Listing_Order_Num_Auto_Delete.name()),
        Order_Num_Delete1(9,"包含利朗达侵权词90万无销量系统自动下架",ProcessingReportTriggleType.Listing_Order_Num_Auto_Delete.name()),
        Stop_Archived_Auto_Delete(10,"SKU停产存档,系统自动下架",ProcessingReportTriggleType.Listing_Stop_Archived_Auto_Delete.name()),
        Repeat_Publish_Delete(11,"重复刊登,系统自动下架",ProcessingReportTriggleType.NewListing.name()),
        Incomplete_Delete(12,"系统自动删除一周前内容不完整listing",ProcessingReportTriggleType.NewListing.name()),
        Forbidden_Delete(13,"禁售类型匹配且24小时未确认，系统自动确认",ProcessingReportTriggleType.NewListing.name()),
        INFRINGEMENT_WORD_DELETE(22,"包含侵权词:%s, 系统自动下架",ProcessingReportTriggleType.NewListing.name()),
        SPECIFIED_SPU_DELETE(23,"业务指定SPU下架",ProcessingReportTriggleType.NewListing.name()),
        ACCOUNT_CONFIG_AUTO_DELETE(24,"店铺配置规则自动下架",ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_CONFIG_CONTAINS_INFRINGING_WORD(25, "下架配置-链接包含侵权词", ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_CONFIG_DESIGNATED_SKU(26, "下架配置-指定sku下架", ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_CONFIG_PROHIBITED_PRODUCT(27, "下架配置-禁售产品下架", ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_CONFIG_RESERVED_COUNT(28, "下架配置-保留链接数", ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_CONFIG_RESERVED_SKU_COUNT(29, "下架配置-SKU保留链接数", ProcessingReportTriggleType.NewListing.name()),
        OFFLINE_EXCLUSIVE_PRODUCTS(30, "非特供店铺上架特供产品，系统自动下架", ProcessingReportTriggleType.NewListing.name()),

        //销售操作删除,
        Sale_Account_Number_Delete(14,"%s-操作店铺下架",ProcessingReportTriggleType.Listing_Delete.name()),
        Sale_Operate_Listing_Delete(15,"%s",ProcessingReportTriggleType.NewListing.name()),
        Sale_Forbidden_Listing_Delete(16,"后台禁售侵权产品，%s确认删除",ProcessingReportTriggleType.NewListing.name()),
        Sale_Infringement_Listing_Delete(17,"后台绩效侵权产品，%s确认删除",ProcessingReportTriggleType.NewListing.name()),
        Sale_Mainimage_Listing_Delete(18,"%s",ProcessingReportTriggleType.NewListing.name()),

        //不调用下架接口，只是标记系统数据不在线
        Account_Not_Exist(19,"店铺在订单系统不存在，系统改为下架",null),
        Account_Status_Not_Use(20,"店铺为非正常或非疑似冻结店铺，系统改为下架",null),
        Sale_Handle_Status(21,"销售处理下架",null),
                ;


        private final int code;
        private final String desc;
        private final  String relationType;

        public String getFormatDesc(Object... args) {
            return String.format(desc, args);
        }

        public static Type getCodeType(int code) {
            for (Type type : Type.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }


}
