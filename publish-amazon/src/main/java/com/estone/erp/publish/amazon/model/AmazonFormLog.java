package com.estone.erp.publish.amazon.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AmazonFormLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_form_log.id
     */
    private Long id;

    /**
     * 关联key
     */
    private Integer relationId;

    /**
     * 所属模块 database column amazon_form_log.module_type
     */
    private String moduleType;

    /**
     * 操作类型 database column amazon_form_log.operation_type
     */
    private String operationType;

    /**
     * 修改内容 database column amazon_form_log.content
     */
    private String content;

    /**
     * 导入的文件路径 database column amazon_form_log.file_path
     */
    private String filePath;

    /**
     * 创建人 database column amazon_form_log.create_by
     */
    private String createBy;

    /**
     *  database column amazon_form_log.create_time
     */
    private Timestamp createTime;
}