package com.estone.erp.publish.tidb.publishtidb.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 亚马逊链接管理配置查询请求对象
 * 根据需求文档4.1 查询条件设计实现
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AmazonLinkManagementConfigQueryRequest {

    /**
     * 规则名称 - 支持模糊查询
     */
    private String ruleName;

    /**
     * 店铺列表 - 多选，根据用户权限动态加载
     */
    private List<String> accounts;

    /**
     * 站点列表 - 多选，如：US,UK,DE,FR,IT,ES,JP等
     */
    private List<String> sites;

    /**
     * 启用状态 - 单选，枚举值：null(全部)、1(启用)、0(禁用)
     */
    private Integer status;

    /**
     * 调整方式 - 单选，枚举值：null(全部)、1(价格区间)、2(指定SKU)、3(价格+重量+标签)
     */
    private Integer ruleType;

    /**
     * 优先级 - 精确查询
     */
    private Integer level;

    /**
     * 创建时间范围 - 开始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private LocalDateTime createTimeEnd;

    /**
     * 创建人列表 - 多选
     */
    private List<String> createdByList;

    /**
     * 当前用户 - 用于权限过滤（系统内部使用）
     */
    private String currentUser;

    /**
     * 用户权限范围内的店铺列表 - 用于权限过滤（系统内部使用）
     */
    private List<String> userAuthorizedAccounts;
} 