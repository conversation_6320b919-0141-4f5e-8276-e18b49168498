package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ProductSystemMqConfig {

    private int productSaleSkuMqConsumers;
    private int productSaleSkuMqPrefetchCount;
    private boolean productSaleSkuMqSwitchListener;

    @Bean
    public Queue productSystemSkuStatusNormal() {
        return new Queue(PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE);
    }

    @Bean
    public Binding productSystemSkuStatusNormalBinding() {
        return new Binding(PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE, DestinationType.QUEUE, PublishRabbitMqExchange.PRODUCT_DIRECT_EXCHANGE,
                PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE, null);
    }
    @Bean
    public ProductSystemSkuStatusNormalMqListener productSystemSkuStatusNormalMqListener() {
        return new ProductSystemSkuStatusNormalMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer productSystemSkuStatusNormalMqListenerContainer(
            ProductSystemSkuStatusNormalMqListener productSystemSkuStatusNormalMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE, productSystemSkuStatusNormalMqListener);
        return container;
    }


    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (productSaleSkuMqSwitchListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(productSaleSkuMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(productSaleSkuMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    @Bean
    public Queue amazonProcessReportListingQueue() {
        return new Queue(PublishQueues.AMAZON_PROCESS_REPORT_LISTING_QUEUE);
    }

    @Bean
    public Binding amazonProcessReportListingQueueBinding() {
        return new Binding(PublishQueues.AMAZON_PROCESS_REPORT_LISTING_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_PROCESS_REPORT_LISTING_KEY, null);
    }
}
