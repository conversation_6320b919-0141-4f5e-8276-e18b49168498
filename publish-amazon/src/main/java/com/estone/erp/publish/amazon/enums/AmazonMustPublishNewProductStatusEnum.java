package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-10-23 11:44
 */
@Getter
@AllArgsConstructor
public enum AmazonMustPublishNewProductStatusEnum {

    NO_TEMPLATE(0, "无模板"),
    UN_PUBLISH(1, "未刊登"),
    PART_PUBLISH(2, "部分刊登"),
    ALL_PUBLISH(3, "全部刊登"),
    DISABLE(99, "禁用");


    private final int code;
    private final String desc;

    public static String getDescByCode(int code) {
        for (AmazonMustPublishNewProductStatusEnum value : values()) {
            if (code == value.code) {
                return value.desc;
            }
        }
        return null;
    }


}
