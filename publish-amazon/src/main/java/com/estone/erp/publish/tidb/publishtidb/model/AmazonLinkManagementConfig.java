package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Amazon链接管理配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_link_management_config")
public class AmazonLinkManagementConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置类型：1-修改备货期
     */
    private Integer type;

    /**
     * 规则名称，配置规则的唯一标识名称
     */
    private String ruleName;

    /**
     * 账户类型：1-指定账户，2-站点账户
     */
    private Integer accountType;

    /**
     * 账户选项，存储站点信息如US,UK,DE等
     */
    private String accountOption;

    /**
     * 适用店铺账号JSON数组
     */
    private String accounts;

    /**
     * 优先级，数字越小优先级越高，范围1-999
     */
    private Integer level;

    /**
     * 规则类型：1-价格区间，2-指定SKU，3-价格+重量+标签
     */
    private Integer ruleType;

    /**
     * 规则配置JSON，包含筛选条件、执行策略、新备货期天数、防重复调整天数等
     */
    private String rule;

    /**
     * 启用状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 执行频率：day-每日，week-每周，month-每月
     */
    private String exeFrequency;

    /**
     * 执行时间，格式如：09:00
     */
    private String exeTime;

    /**
     * 策略开始生效时间
     */
    private LocalDateTime strategyStartTime;

    /**
     * 策略结束时间，NULL表示永久有效
     */
    private LocalDateTime strategyEndTime;

    /**
     * 最近执行时间
     */
    private LocalDateTime lastExecuteTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


}
