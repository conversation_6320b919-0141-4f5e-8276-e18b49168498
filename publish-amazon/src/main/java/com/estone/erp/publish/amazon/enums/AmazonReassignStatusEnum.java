package com.estone.erp.publish.amazon.enums;

/**
 * Amazon新品推荐重分配状态
 *
 * <AUTHOR>
 * @date 2023/6/16 15:23
 */
public enum AmazonReassignStatusEnum {

    NO_REASSIGN(0, "未重分配"),

    REASSIGNING(1, "分配中"),

    REASSIGN_SUCCESS(2, "分配成功"),

    REASSIGN_FAIL(3, "分配失败");

    private int code;

    private String name;

    AmazonReassignStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
