package com.estone.erp.publish.amazon.call.xsd.model;

public class TypeWrapper {
    private String name;

    private String value;

    private Boolean hasRestriction;

    private XsdRestriction restriction;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Boolean getHasRestriction() {
        return hasRestriction;
    }

    public void setHasRestriction(Boolean hasRestriction) {
        this.hasRestriction = hasRestriction;
    }

    public XsdRestriction getRestriction() {
        return restriction;
    }

    public void setRestriction(XsdRestriction restriction) {
        this.restriction = restriction;
    }
}
