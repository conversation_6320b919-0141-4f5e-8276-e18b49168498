package com.estone.erp.publish.amazon.call.service;

import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.util.Env;
import com.estone.erp.publish.common.util.HttpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @Description: amazon upc服务类
 * 
 * @ClassName: AmazonUpcService
 * @Author: Kevin
 * @Date: 2018/08/13
 * @Version: 0.0.1
 */
@Service
public class AmazonUpcService {
    private static final Logger log = LoggerFactory.getLogger(AmazonUpcService.class);

    /**
     * 
     * @Description: 验证upc是否存在
     *
     * @param marketplaceId amazon市场id
     * @param upcs upc列表
     * @return apiResult
     * @Author: Kevin
     * @Date: 2018/08/13
     * @Version: 0.0.1
     */
    public ApiResult<List<String>> isExists(String marketplaceId, List<String> upcs) {
        if (StringUtils.isEmpty(marketplaceId) || CollectionUtils.isEmpty(upcs)) {
            return ApiResult.newError("check upc exists param not satisfy.");
        }

        long startTime = System.currentTimeMillis();
        HttpParams<Map<String, Object>> httpParams = new HttpParams<>();
        httpParams.setUrl(getApiUrl("/api/v1/amazon/upc"));
        httpParams.setHttpMethod(HttpMethod.POST);
        Map<String, Object> body = new HashMap<>(2);
        body.put("marketplaceId", marketplaceId);
        body.put("upcs", upcs);

        httpParams.setBody(body);
        ApiResult<List<String>> apiResult = HttpUtils.exchangeWithApiResultRep(httpParams);
        log.warn("{}--check upcs {} exists, result: {}, cost {}ms.", httpParams.getUrl(), upcs, apiResult,
                System.currentTimeMillis() - startTime);
        return apiResult;
    }

    private static String getApiUrl(String api) {
        return getSupportUrl() + api;
    }

    private static String getSupportUrl() {
        return Env.AMAZON_SUPPORT_URL;
    }
}
