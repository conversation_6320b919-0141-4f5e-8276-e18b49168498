package com.estone.erp.publish.amazon.enums;

/**
 * 新品推荐数据源类型枚举
 * <AUTHOR>
 * @date 2023/4/20 17:31
 */
public enum AmazonNewProductRemindTypeEnum {

    ASSIGN_YESTERDAY(1, "编辑完成时间为昨天的产品"),
    ASSIGN_SEVEN_DAY_BEFORE(2, "编辑完成时间为7天前的产品"),
    ;

    AmazonNewProductRemindTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}