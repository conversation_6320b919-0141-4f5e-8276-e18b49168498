package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.enums.AmazonAsinBindStatus;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.service.AmazonListingAsinBindRecordService;
import com.estone.erp.publish.amazon.service.AmazonListingUpdateDescService;
import com.estone.erp.publish.amazon.service.AmazonListingUpdateImageService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.rabbitmq.client.Channel;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.response.AmazonFeedsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Component @Slf4j
public class ListingAmazonProcessReportMqListener {

    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonListingUpdateImageService amazonListingUpdateImageService;
    @Resource
    private AmazonListingUpdateDescService amazonListingUpdateDescService;
    @Resource
    private AmazonListingAsinBindRecordService amazonListingAsinBindRecordService;

    /**
     * 亚马逊监听提交刊登数据对应的任务
     */
    @RabbitListener(queues = PublishQueues.AMAZON_PROCESS_REPORT_LISTING_QUEUE, containerFactory = "consumeFactory")
    public void submitTaskReturnResultMessage(Message message, Channel channel) {
        String str = new String(message.getBody());
        try {
            AmazonFeedsResult feedResult = JSON.parseObject(str, AmazonFeedsResult.class);

            //查询处理报告信息，回写 Listng
            if (feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_DATA.getValue()) && StringUtils.isNotEmpty(feedResult.getTaskId())) {
                if (feedResult.getStatus()) {
                    // 成功更新描述
                    updateDesc(feedResult);
                }
                // 更新bindAsin关系
                updateAsinProductRelation(feedResult);
            }else if(feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue()) && feedResult.getStatus() && StringUtils.isNotEmpty(feedResult.getTaskId())){
                AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
                String filed = "relation_id";
                amazonProcessReportExample.setFiledColumns(filed);
                amazonProcessReportExample.createCriteria()
                        .andAccountNumberEqualTo(feedResult.getAccountNumber())
                        .andTaskIdEqualTo(feedResult.getTaskId())
                        .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue())
                        .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing_Update_Image.name());
                List<AmazonProcessReport> updateImageList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
                if (CollectionUtils.isNotEmpty(updateImageList)){
                    // 回写状态
                    List<AmazonListingUpdateImage> amazonListingUpdateImageList = new ArrayList<>(updateImageList.size());
                    updateImageList.stream().forEach( o ->{
                        AmazonListingUpdateImage amazonListingUpdateImage = new AmazonListingUpdateImage();
                        amazonListingUpdateImage.setId(o.getRelationId());
                        amazonListingUpdateImage.setStatus(true);
                        amazonListingUpdateImageList.add(amazonListingUpdateImage);
                    });
                    amazonListingUpdateImageService.batchUpdateStatus(amazonListingUpdateImageList);
                }
            }

            if (feedResult.getFeedType().equals(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue())) {
                // 关系上传,更新asin绑定记录
                updateAsinRelation(feedResult);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }catch (Exception e) {
            log.error("AMAZON_PROCESS_REPORT_LISTING_QUEUE 解析str出错：--------", str);
            //log.warn(e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.warn(ioe.getMessage() + "消费成功，通知服务器移除mq时异常，异常信息", ioe);
            }
        }
    }

    /**
     * 关系上传,更新asin绑定记录
     * @param feedResult
     */
    private void updateAsinRelation(AmazonFeedsResult feedResult) {
        try {
            AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
            String filed = "relation_id";
            amazonProcessReportExample.setFiledColumns(filed);
            amazonProcessReportExample.setOrderByClause("id desc");
            amazonProcessReportExample.setLimit(1);
            amazonProcessReportExample.createCriteria()
                    .andAccountNumberEqualTo(feedResult.getAccountNumber())
                    .andTaskIdEqualTo(feedResult.getTaskId())
                    .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                    .andRelationTypeEqualTo(ProcessingReportTriggleType.BIND_LISTING_RELATIONSHIP.name());
            List<AmazonProcessReport> bindAsinList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
            if (CollectionUtils.isEmpty(bindAsinList)) {
                return;
            }
            AmazonProcessReport amazonProcessReport = bindAsinList.get(0);
            //log.info("ASIN绑定关系更新：record: {}, task:{}", feedResult.getTaskId(), amazonProcessReport.getRelationId());
            AmazonListingAsinBindRecord bindRecord = amazonListingAsinBindRecordService.selectByPrimaryKey(Long.valueOf(amazonProcessReport.getRelationId()));
            if (bindRecord == null) {
                return;
            }

            bindRecord.setUploadSuccess(feedResult.getStatus());
            bindRecord.setUpdateDate(Timestamp.valueOf(LocalDateTime.now()));
            String extraData = bindRecord.getExtraData();
            if (StringUtils.isNotEmpty(extraData)) {
                JSONObject extraDataJson = JSON.parseObject(extraData);
                extraDataJson.put("last_status", bindRecord.getStatus());
                extraDataJson.put("task_id", feedResult.getTaskId());
                extraDataJson.put("result_message", feedResult.getErrorMsg());
                bindRecord.setExtraData(extraDataJson.toJSONString());
            }
            bindRecord.setUploadSuccess(feedResult.getStatus());
            if (feedResult.getStatus()) {
                bindRecord.setExtraData(null);
                // 成功则继续上传关系
                bindRecord.setStatus(AmazonAsinBindStatus.BIND_SUCCESS.getCode());
                amazonListingAsinBindRecordService.updateRelationAfterSuccess(bindRecord);
            }else {
                bindRecord.setStatus(AmazonAsinBindStatus.BIND_FAIL.getCode());
            }
            amazonListingAsinBindRecordService.updateByPrimaryKeySelective(bindRecord);
        }catch (Exception e) {
            log.error("关系上传,更新asin绑定记录异常: task_id={}, error_msg={}", feedResult.getTaskId(),  e.getMessage(), e);
        }

    }

    /**
     *  更新ASIN绑定关系
     * @param feedResult
     */
    private void updateAsinProductRelation(AmazonFeedsResult feedResult) {
        try {
            AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
            String filed = "relation_id";
            amazonProcessReportExample.setFiledColumns(filed);
            amazonProcessReportExample.setOrderByClause("id desc");
            amazonProcessReportExample.setLimit(1);
            amazonProcessReportExample.createCriteria()
                    .andAccountNumberEqualTo(feedResult.getAccountNumber())
                    .andTaskIdEqualTo(feedResult.getTaskId())
                    .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                    .andRelationTypeEqualTo(ProcessingReportTriggleType.BIND_LISTING_PRENT_UPLOAD.name());
            List<AmazonProcessReport> bindAsinList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
            if (CollectionUtils.isEmpty(bindAsinList)) {
                return;
            }
            AmazonProcessReport amazonProcessReport = bindAsinList.get(0);
            //log.info("ASIN绑定父体上传：record: {}, task:{}", feedResult.getTaskId(), amazonProcessReport.getRelationId());
            AmazonListingAsinBindRecord bindRecord = amazonListingAsinBindRecordService.selectByPrimaryKey(Long.valueOf(amazonProcessReport.getRelationId()));
            if (bindRecord == null) {
                return;
            }

            bindRecord.setUploadSuccess(feedResult.getStatus());
            bindRecord.setUpdateDate(Timestamp.valueOf(LocalDateTime.now()));
            String extraData = bindRecord.getExtraData();
            if (StringUtils.isNotEmpty(extraData)) {
                JSONObject extraDataJson = JSON.parseObject(extraData);
                extraDataJson.put("last_status", bindRecord.getStatus());
                extraDataJson.put("task_id", feedResult.getTaskId());
                extraDataJson.put("result_message", feedResult.getErrorMsg());
                bindRecord.setExtraData(extraDataJson.toJSONString());
            }
            bindRecord.setUploadSuccess(feedResult.getStatus());
            if (feedResult.getStatus()) {
                // 成功则继续上传关系
                log.info("ASIN绑定父体成功继续上传关系：record: {}, task:{}", feedResult.getTaskId(), amazonProcessReport.getRelationId());
                DataContextHolder.setUsername(bindRecord.getLastUpdateBy());
                amazonListingAsinBindRecordService.uploadRelationship(bindRecord);
            }else {
                bindRecord.setStatus(AmazonAsinBindStatus.BIND_FAIL.getCode());
            }
            amazonListingAsinBindRecordService.updateByPrimaryKeySelective(bindRecord);
        } catch (Exception e) {
            log.error("ASIN绑定关系更新异常: task_id={}, error_msg={}", feedResult.getTaskId(),  e.getMessage(), e);
        }
    }


    /**
     * 更新描述
     * @param feedResult
     */
    private void updateDesc(AmazonFeedsResult feedResult){
        AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
        String filed = "relation_id";
        amazonProcessReportExample.setFiledColumns(filed);
        amazonProcessReportExample.createCriteria()
                .andAccountNumberEqualTo(feedResult.getAccountNumber())
                .andTaskIdEqualTo(feedResult.getTaskId())
                .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing_Update_Desc.name());
        List<AmazonProcessReport> updateDescList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
        if (CollectionUtils.isNotEmpty(updateDescList)){
            // 回写状态
            List<AmazonListingUpdateDesc> amazonListingUpdateDescList = new ArrayList<>(updateDescList.size());
            updateDescList.stream().forEach( o ->{
                AmazonListingUpdateDesc amazonListingUpdateDesc = new AmazonListingUpdateDesc();
                amazonListingUpdateDesc.setId(o.getRelationId());
                amazonListingUpdateDesc.setStatus(true);
                amazonListingUpdateDescList.add(amazonListingUpdateDesc);
            });
            amazonListingUpdateDescService.batchUpdateStatus(amazonListingUpdateDescList);
        }
    }
}
