package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.enums.AmazonTemplateValidationEnum;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 * @date 2023-08-25 9:11
 */
@Getter
@Setter
public class AmazonTemplateValidationDO {

    /**
     * 模板错误码
     * @see AmazonTemplateValidationEnum
     */
    private Integer code;
    private String  errorMsg;
    private Boolean success;
    private Object data;

    public AmazonTemplateValidationDO() {
    }

    public AmazonTemplateValidationDO(AmazonTemplateValidationEnum errorEnum, Boolean success) {
        this.code = errorEnum.getCode();
        this.success = success;
    }

    public AmazonTemplateValidationDO(AmazonTemplateValidationEnum errorEnum, Boolean success, Object data) {
        this.code = errorEnum.getCode();
        this.success = success;
        this.data = data;
    }

    public static AmazonTemplateValidationDO successOf(AmazonTemplateValidationEnum errorEnum) {
        AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
        validationDO.setCode(errorEnum.getCode());
        validationDO.setSuccess(true);
        return validationDO;
    }

    public static AmazonTemplateValidationDO failOf(AmazonTemplateValidationEnum errorEnum, String message, Object data) {
        AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
        validationDO.setCode(errorEnum.getCode());
        validationDO.setErrorMsg(message);
        validationDO.setSuccess(false);
        validationDO.setData(data);
        return validationDO;
    }
}
