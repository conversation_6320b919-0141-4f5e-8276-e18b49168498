package com.estone.erp.publish.tidb.publishtidb.domain.offline.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-01-07 11:10
 */
public interface OfflineConfigEnums {
    /**
     * 配置状态
     */
    @Getter
    @AllArgsConstructor
    enum ConfigStatus {
        ENABLE(1, "启用"),
        DISABLE(0, "禁用");
        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }
    }


    @Getter
    @AllArgsConstructor
    enum ConfirmStatus {
        PENDING_CONFIRMATION(0, "待确认"),
        CONFIRMING(1, "确认中"),
        CONFIRMED(2, "已确认");

        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }
    }


    /**
     * 店铺类型
     */
    @Getter
    @AllArgsConstructor
    enum AccountType {
        ACCOUNTS(1, "指定店铺"),
        SITE(2, "站点下属"),
        SUPERVISOR(3, "主管下属");
        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }
    }


    /**
     * 店铺类型
     */
    @Getter
    @AllArgsConstructor
    enum OfflineType {
        CONTAINS_INFRINGING_WORD(1, 25, "链接包含侵权词"),
        DESIGNATED_SKU(2, 26, "指定sku下架"),
        PROHIBITED_PRODUCT(3, 27, "禁售产品下架"),
        RESERVED_COUNT(4, 28, "保留链接数"),
        SKU_RESERVED_COUNT(5, 29, "SKU保留链接数");
        private final int code;
        private final int offlineTypeCode;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }

        public static OfflineType getByCode(Integer code) {
            for (OfflineType offlineType : OfflineType.values()) {
                if (offlineType.isTrue(code)) {
                    return offlineType;
                }
            }
            return null;
        }

        public static OfflineType getByName(String name) {
            for (OfflineType offlineType : OfflineType.values()) {
                if (offlineType.name().equals(name)) {
                    return offlineType;
                }
            }
            return null;
        }
    }

    /**
     * 执行状态
     */
    @Getter
    @AllArgsConstructor
    enum ExecutionStatus {
        PENDING_EXECUTION(0, "待执行"),
        GENERATING_TOTAL_TABLE_DATA(1, "总表数据生成中"),
        PENDING_CONFIRMATION_TOTAL_TABLE_DATA(2, "待确认总表数据"),
        TOTAL_TABLE_DATA_CONFIRMATION_FAILED(3, "总表数据确认失败"),
        PENDING_TEST_ACCOUNT_DATA_CONFIGURATION(4, "待配置测试店铺数据"),
        PENDING_CONFIRMATION_TEST_ACCOUNT_DETAIL_DATA(5, "待确认测试店铺明细数据"),
        TEST_ACCOUNT_DETAIL_DATA_CONFIRMATION_FAILED(6, "测试店铺明细数据确认失败"),
        OFFLINE_TEST_ACCOUNT_DATA(7, "下架测试店铺数据"),
        PENDING_CONFIRMATION_OFFLINE_TEST_ACCOUNT_DATA(8, "待确认下架测试店铺数据"),
        OFFLINE_TEST_ACCOUNT_DATA_CONFIRMATION_FAILED(9, "下架测试店铺数据确认失败"),
        PENDING_CONFIRMATION_OFFLINE(10, "待确认下架"),
        OFFLINE_CONFIRMED(11, "已确认下架");
        private final int code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return this.code == code;
        }

        public static ExecutionStatus getByCode(Integer code) {
            for (ExecutionStatus executionStatus : ExecutionStatus.values()) {
                if (executionStatus.isTrue(code)) {
                    return executionStatus;
                }
            }
            return null;
        }
    }

    /**
     * 执行时间
     */
    @Getter
    @AllArgsConstructor
    enum ExecutionTime {
        TOTAL_DATA_GENERATION_TIME("total_data_generation_time", "总表数据生成时间"),
        TOTAL_DATA_COMPLETION_TIME("total_data_completion_time", "总表数据生成完成时间"),
        TOTAL_DATA_DOWNLOAD_TIME("total_data_download_time", "总表数据下载时间"),
        TOTAL_DATA_CONFIRMATION_TIME("total_data_confirmation_time", "总表数据确认时间"),
        TEST_ACCOUNT_CONFIG_TIME("test_account_config_time", "测试店铺数据配置时间"),
        TEST_ACCOUNT_DATA_DOWNLOAD_TIME("test_account_data_download_time", "测试店铺数据下载时间"),
        TEST_ACCOUNT_DATA_CONFIRMATION_TIME("test_account_data_confirmation_time", "测试店铺数据确认时间"),
        TEST_ACCOUNT_OFFLINE_TIME("test_account_offline_time", "测试店铺数据下架时间"),
        TEST_ACCOUNT_OFFLINE_COMPLETION_TIME("test_account_offline_completion_time", "测试店铺数据下架完成时间"),
        TEST_ACCOUNT_OFFLINE_CONFIRMATION_TIME("test_account_offline_confirmation_time", "测试店铺数据下架确认时间"),
        CONFIRMED_DELETION_TIME("confirmed_deletion_time", "已确认下架时间");
        private final String name;
        private final String desc;

    }

}
