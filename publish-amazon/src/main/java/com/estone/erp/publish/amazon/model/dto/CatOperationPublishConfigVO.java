package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfig;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-22 14:46
 */
@Data
public class CatOperationPublishConfigVO {

    private Integer id;

    /**
     * 类目全路径
     */
    private String categoryFullPathCode;

    /**
     * 分类
     */
    private String categoryPathName;
    /**
     * 总次数
     */
    private Integer publishNumber;

    /**
     * 销售主管配置
     */
    private List<SalePublishConfig> salePublishConfigList;

    /**
     * 状态
     */
    private Boolean status;
    /**
     * 添加时间
     */
    private Timestamp createdTime;

    /**
     * 修改时间
     */
    private Timestamp updatedTime;



    @Data
    static class SalePublishConfig {

        private Integer id;
        /**
         * 主管id
         */
        private String saleId;
        /**
         * 刊登次数
         */
        private Integer publishNumber;
        /**
         * 状态
         */
        private Boolean status;
    }

    public void addSalePublishConfigList(List<CategoryOperationsPublishConfig> publishConfigs) {
        this.salePublishConfigList = publishConfigs.stream().map(config -> {
            SalePublishConfig salePublishConfig = new SalePublishConfig();
            salePublishConfig.setId(config.getId());
            salePublishConfig.setSaleId(config.getSaleId());
            salePublishConfig.setStatus(config.getStatus());
            salePublishConfig.setPublishNumber(config.getPublishNumber());
            return salePublishConfig;
        }).collect(Collectors.toList());
    }

    public List<CatOperationPublishConfigExcelDO> convert2ExcelDO(Map<String, String> userMap) {
        if (CollectionUtils.isEmpty(this.getSalePublishConfigList())) {
            return Collections.emptyList();
        }
        List<CatOperationPublishConfigExcelDO> excelDOS = new ArrayList<>();
        for (SalePublishConfig salePublishConfig : this.getSalePublishConfigList()) {
            String saleId = salePublishConfig.getSaleId();
            String userName = NewUsermgtUtils.getUserNameByEmployeeNo(userMap, String.valueOf(saleId));
            salePublishConfig.setSaleId(saleId+"-"+userName);

            CatOperationPublishConfigExcelDO bean = getCatOperationPublishConfigExcelDO(salePublishConfig);
            excelDOS.add(bean);
        }
        return excelDOS;
    }

    private CatOperationPublishConfigExcelDO getCatOperationPublishConfigExcelDO(SalePublishConfig salePublishConfig) {
        CatOperationPublishConfigExcelDO bean = new CatOperationPublishConfigExcelDO();
        bean.setCategoryPathName(this.getCategoryPathName());
        bean.setPublishNumber(this.getPublishNumber());
        bean.setSaleName(salePublishConfig.getSaleId());
        bean.setSalePublishNumber(salePublishConfig.getPublishNumber());
        bean.setStatus(salePublishConfig.getStatus());
        bean.setCreatedTime(this.getCreatedTime());
        bean.setUpdatedTime(this.getUpdatedTime());
        return bean;
    }

    /**
     * 获取主管配置是否启用
     * @param saleId saleId
     * @return 启用 禁用
     */
    public Boolean isEnableSaleConfig(String saleId) {
        if (Boolean.FALSE.equals(this.getStatus())) {
            return false;
        }

        return this.getSalePublishConfigList().stream()
                .anyMatch(config -> config.getSaleId().equals(saleId) && Boolean.TRUE.equals(config.getStatus()));
    }

    /**
     * 获取主管配置次数
     * @param saleId saleId
     * @return 主管刊登次数
     */
    public Integer getSalePublishNumber(String saleId) {
        Optional<SalePublishConfig> salePublishConfig = this.getSalePublishConfigList().stream()
                .filter(config -> config.getSaleId().equals(saleId))
                .findFirst();
        return salePublishConfig.map(SalePublishConfig::getPublishNumber).orElse(null);
    }


    /**
     * 获取配置下启用配置的主管ID
     * @return 主管刊登次数
     */
    public List<String> getAllEnableSaleId() {
        return this.getSalePublishConfigList().stream()
                .filter(config -> Boolean.TRUE.equals(config.getStatus()))
                .map(SalePublishConfig::getSaleId)
                .collect(Collectors.toList());

    }

}
