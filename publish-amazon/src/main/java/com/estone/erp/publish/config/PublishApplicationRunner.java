package com.estone.erp.publish.config;

import com.estone.erp.common.alert.policy.AlertPolicyPool;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.common.util.Env;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 项目启动后执行的方法
 *
 * @Description:
 * @ClassName: ApplicationStartup
 * @Author: wuhuiqiang
 * @Date: 2019/05/07
 * @Version: 0.0.1
 */
@Component
@ConfigurationProperties(prefix = "yml-config")
@Data
@Slf4j
public class PublishApplicationRunner implements ApplicationRunner {

    private String localPath;

    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
//        String path = "D:\\projects\\publish2\\publish-amazon\\src\\main\\resources\\amazonApiXsd_4_1\\";
//        XsdUtils.setXsdPath(path);
        // 分类树初始化
        SpringUtils.getBean(InitService.class).start();
        EnvironmentSupplierWrapper.execute(List.of("local", "dev"), () -> {
                    log.warn("本地环境不加载XSD文件");
                    return "";
                },
                () -> {
                    log.info("加载XSD文件....");
                    XsdUtils.setXsdPath(getResourcePath(localPath + AmazonConstant.AMAZON_API_XSD_PATH) + File.separator);
                    Env.init(null);
                    XsdUtils.init();
                    log.warn("========XSD文件加载完毕=======");
                    return "";
                });
        AlertPolicyPool.init();
    }

    public static String getResourcePath(String loaction) {
        try {
            return SpringUtils.getApplicationContext().getResource(loaction).getFile().getAbsolutePath();

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}