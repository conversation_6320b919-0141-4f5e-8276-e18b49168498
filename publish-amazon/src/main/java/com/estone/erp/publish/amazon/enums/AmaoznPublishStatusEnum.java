package com.estone.erp.publish.amazon.enums;

/**
 * @Description: ${亚马逊刊登详细状态枚举}
 * @Author: yjy
 * @Date: 2019/11/5 12:15
 * @Version: 1.0.0
 */
public enum AmaoznPublishStatusEnum {
    //刊登详细状态: 1 待刊登 2 刊登中 8刊登成功 9刊登失败
    WAIT_PUBLISH(1,"wait publish","待刊登"),
    PUBLISHING(2,"publish","刊登中"),
    PUBLISH_SUCCESS(8,"publish success","刊登成功"),
    PUBLISH_FAIL(9,"publish fail","刊登失败");

    //状态码
    private int statusCode;
    //状态英文
    private String statusMsgEn;
    //状态中文
    private String statusMsgCn;

    private AmaoznPublishStatusEnum(int statusCode, String statusMsgEn, String statusMsgCn) {
        this.statusCode = statusCode;
        this.statusMsgEn = statusMsgEn;
        this.statusMsgCn = statusMsgCn;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getStatusMsgEn() {
        return statusMsgEn;
    }

    public String getStatusMsgCn() {
        return statusMsgCn;
    }

    public boolean isTrue(Integer statusCode) {
        if (statusCode == null) {
            return false;
        }
        return this.statusCode == statusCode;

    }

    public static String getStatusMsgCnByStatusCode(int statusCode) {
        for (AmaoznPublishStatusEnum statusEnum : AmaoznPublishStatusEnum.values()) {
            if (statusEnum.getStatusCode() == statusCode) {
                return statusEnum.getStatusMsgCn();
            }
        }
        return "UNKNOWN STATUS CODE " + statusCode;
    }
}
