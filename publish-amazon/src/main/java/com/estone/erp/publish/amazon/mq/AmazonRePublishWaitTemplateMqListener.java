package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.bo.AmazonAutoPublishParam;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.AmazonAutoPublishHelper;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023-06-13 18:05
 */
@Slf4j
public class AmazonRePublishWaitTemplateMqListener implements ChannelAwareMessageListener {

    @Resource
    private AmazonTemplateService amazonTemplateService;
    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;
    @Autowired
    private AmazonAutoPublishHelper amazonAutoPublishHelper;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if(StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            List<Integer> ids = JSON.parseArray(body, Integer.class);
            if (CollectionUtils.isEmpty(ids)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                log.error("消息为空");
                return;
            }
            executeHandler(ids);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }catch (Exception e) {
            log.error("reloadWaitPublishTemplate 异常：{},{}", body, e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void executeHandler(List<Integer> ids) {
        // 过滤刊登中的
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.setColumns("id, seller_id,parent_SKU, country, category_id, sku_data_source, publish_role, publish_status, created_by");
        example.createCriteria().andIdIn(ids).andPublishStatusEqualTo(AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode());
        List<AmazonTemplateBO> templateBOList = amazonTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(templateBOList)) {
            return;
        }
        AmazonTemplateBO templateBO = templateBOList.get(0);
        if (templateBO == null) {
            return;
        }
        String accountNumber = templateBO.getSellerId();
        AmazonAccount account = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
        if(account == null){
            return;
        }
        // 查询店铺配置设置一些信息
        AmazonAccountRelationExample accountRelationExample = new AmazonAccountRelationExample();
        accountRelationExample.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<AmazonAccountRelation> accountRelationList = amazonAccountRelationService.selectByExample(accountRelationExample);
        AmazonAccountRelation accountRelation;
        if(CollectionUtils.isNotEmpty(accountRelationList)){
            accountRelation = accountRelationList.get(0);
        }else{
            accountRelation = new AmazonAccountRelation();
        }
        log.info("reloadWaitPublishTemplate 重刊登模板数量：{}", templateBOList.size());
        AtomicInteger increment = new AtomicInteger(0);
        for (AmazonTemplateBO amazonTemplateBO : templateBOList) {
            rePublishTemplate(amazonTemplateBO, account, accountRelation, increment);
        }
        log.info("reloadWaitPublishTemplate 处理完成：{}", ids.size());
    }

    private void rePublishTemplate(AmazonTemplateBO amazonTemplateBO, AmazonAccount account, AmazonAccountRelation accountRelation, AtomicInteger increment) {
        try {
            if (!amazonTemplateBO.getPublishStatus().equals(AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode())) {
                log.info("模板已刊登：{},状态：{}", amazonTemplateBO.getId(), amazonTemplateBO.getPublishStatus());
                return;
            }
            AmazonAutoPublishParam amazonAutoPublishParam = new AmazonAutoPublishParam();
            amazonAutoPublishParam.setProductSource(Optional.ofNullable(amazonTemplateBO.getSkuDataSource()).orElse(1));
            amazonAutoPublishParam.setAccountNumber(account.getAccountNumber());
            amazonAutoPublishParam.setCountry(amazonTemplateBO.getCountry());
            amazonAutoPublishParam.setParentSku(amazonTemplateBO.getParentSku());
            amazonAutoPublishHelper.executorAutoPublishTemplate(amazonTemplateBO, 1, account, accountRelation, amazonAutoPublishParam, false, increment);
        } catch (Exception e) {
            log.error("reloadWaitPublishTemplate templateId:{},处理异常：{}", amazonTemplateBO.getId(), e.getMessage(), e);
        }
    }
}
