package com.estone.erp.publish.amazon.mq;


import com.estone.erp.common.mq.PublishQueues;
import lombok.Data;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 产品系统推送管理单品新状态消息队列配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class AmazonProductNewStateMqConfig {

    private boolean amazonProductSingleNewStatusQueueEnable;
    private int amazonProductSingleNewStatusQueueConsumer;
    private int amazonProductSingleNewStatusQueuePrefetchCount;

    @Bean
    public Queue productSingleNewStatusAmazonQueue() {
        return new Queue(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_AMAZON_QUEUE);
    }
    @Bean
    public Binding productSingleNewStatusAmazonQueueBinging(Queue productSingleNewStatusAmazonQueue, FanoutExchange productSingleNewStatusToPublishFanout) {
        return BindingBuilder
                .bind(productSingleNewStatusAmazonQueue)
                .to(productSingleNewStatusToPublishFanout);
    }
    @Bean
    public AmazonProductNewStateMqListener amazonProductNewStateMqListener() {
        return new AmazonProductNewStateMqListener();
    }
    @Bean
    public SimpleMessageListenerContainer amazonProductNewStateMQListenerContainer(
            AmazonProductNewStateMqListener amazonProductNewStateMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonProductSingleNewStatusQueueEnable) {
            container.setQueueNames(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_AMAZON_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonProductSingleNewStatusQueuePrefetchCount);
            container.setConcurrentConsumers(amazonProductSingleNewStatusQueueConsumer);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(amazonProductNewStateMqListener);
        }
        return container;
    }

}
