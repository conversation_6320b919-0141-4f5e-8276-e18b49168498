package com.estone.erp.publish.amazon.util.request;

import com.alibaba.fastjson.JSON;
import com.google.gson.annotations.SerializedName;
import io.swagger.client.model.listings.PatchOperation;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-02 11:40
 */
@Data
public class JSONListingFeedPatchRequest {
    @SerializedName("sku")
    private String sku = null;
    @SerializedName("operationType")
    private String operationType = null;
    @SerializedName("productType")
    private String productType = null;
    @SerializedName("patches")
    private List<PatchOperation> patches = new ArrayList();


    public String toJSONData() {
        return JSON.toJSONString(this);
    }

}
