package com.estone.erp.publish.amazon.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> amazon_listing_update_desc
 * 2023-12-08 14:34:43
 */
@Data
public class AmazonListingUpdateDescCriteria extends AmazonListingUpdateDesc {
    private static final long serialVersionUID = 1L;

    /**
     * 店铺
     */
    private List<String> accountNumbers;

    /**
     * skuStr 多个情况用 逗号分隔
     */
    private String skuStr;

    /**
     * sellerSkuStr 多个按逗号隔开
     */
    private String sellerSkuStr;

    /**
     * asinStr 多个按照逗号隔开
     */
    private String asinStr;

    /**
     * 多个站点
     */
    private List<String> sites;

    /**
     * 确认状态
     */
    private List<Integer> confirmStatusList;

    /**
     * 匹配时间 从
     */
    private Date fromCreateTime;

    /**
     * 匹配时间 到
     */
    private Date toCreateTime;

    /**
     * 确认时间 从
     */
    private Date fromConfirmTime;

    /**
     * 确认时间 到
     */
    private Date toConfirmTime;

    public AmazonListingUpdateDescExample getExample() {
        AmazonListingUpdateDescExample example = new AmazonListingUpdateDescExample();
        AmazonListingUpdateDescExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (CollectionUtils.isNotEmpty(this.getAccountNumbers())) {
            criteria.andAccountNumberIn(this.getAccountNumbers());
        }
        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (CollectionUtils.isNotEmpty(this.getSites())) {
            criteria.andSiteIn(this.getSites());
        }
        if (StringUtils.isNotBlank(this.getAsin())) {
            criteria.andAsinEqualTo(this.getAsin());
        }
        if (StringUtils.isNotBlank(this.getAsinStr())) {
            String[] split = this.getAsinStr().split(",");
            criteria.andAsinIn(List.of(split));
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getSkuStr())) {
            String[] split = this.getSkuStr().split(",");
            criteria.andSkuIn(List.of(split));
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (StringUtils.isNotBlank(this.getSellerSkuStr())) {
            String[] split = this.getSellerSkuStr().split(",");
            criteria.andSellerSkuIn(List.of(split));
        }
        if (StringUtils.isNotBlank(this.getBeforeValue())) {
            criteria.andBeforeValueEqualTo(this.getBeforeValue());
        }
        if (StringUtils.isNotBlank(this.getAfterValue())) {
            criteria.andAfterValueEqualTo(this.getAfterValue());
        }
        if (this.getConfirmStatus() != null) {
            criteria.andConfirmStatusEqualTo(this.getConfirmStatus());
        }
        if (CollectionUtils.isNotEmpty(this.getConfirmStatusList())) {
            criteria.andConfirmStatusIn(this.getConfirmStatusList());
        }
        if (StringUtils.isNotBlank(this.getConfirmBy())) {
            criteria.andConfirmByEqualTo(this.getConfirmBy());
        }
        if (this.getConfirmTime() != null) {
            criteria.andConfirmTimeEqualTo(this.getConfirmTime());
        }
        if (this.getFromConfirmTime() != null) {
            criteria.andConfirmTimeGreaterThanOrEqualTo(new Timestamp(this.getFromConfirmTime().getTime()));
        }
        if (this.getToConfirmTime() != null) {
            criteria.andConfirmTimeLessThanOrEqualTo(new Timestamp(this.getToConfirmTime().getTime()));
        }
        if (StringUtils.isNotBlank(this.getConfirmRemark())) {
            criteria.andConfirmRemarkEqualTo(this.getConfirmRemark());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (StringUtils.isNotBlank(this.getRemark())) {
            criteria.andRemarkEqualTo(this.getRemark());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getFromCreateTime() != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(new Timestamp(this.getFromCreateTime().getTime()));
        }
        if (this.getToCreateTime() != null) {
            criteria.andCreateTimeLessThanOrEqualTo(new Timestamp(this.getToCreateTime().getTime()));
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        return example;
    }
}