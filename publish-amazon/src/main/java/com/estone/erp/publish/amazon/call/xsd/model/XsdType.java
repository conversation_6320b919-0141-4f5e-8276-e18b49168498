package com.estone.erp.publish.amazon.call.xsd.model;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * xsd节点类型
 * 
 * <AUTHOR>
 *
 */
public class XsdType implements Serializable {
    private static final long serialVersionUID = 4431041242402426653L;

    /**
     * Type类型：true: simple, false: complex
     */
    private Boolean isSimple = true;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 复杂类型的子元素
     */
    private List<XsdElement> elements;

    /**
     * 是否包含限定条件
     */
    private Boolean hasRestriction;

    /**
     * 节点限定条件
     */
    private XsdRestriction restriction;

    private List<XsdAttr> attrs;

    private String finalStatus;

    public Boolean getIsSimple() {
        return isSimple;
    }

    public void setIsSimple(Boolean isSimple) {
        this.isSimple = isSimple;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<XsdElement> getElements() {
        return elements;
    }

    public void setElements(List<XsdElement> elements) {
        this.elements = elements;
    }

    public Boolean isHasRestriction() {
        return hasRestriction;
    }

    public void setHasRestriction(Boolean hasRestriction) {
        this.hasRestriction = hasRestriction;
    }

    public XsdRestriction getRestriction() {
        return restriction;
    }

    public void setRestriction(XsdRestriction restriction) {
        this.restriction = restriction;
    }

    public List<XsdAttr> getAttrs() {
        return attrs;
    }

    public void setAttrs(List<XsdAttr> attrs) {
        this.attrs = attrs;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public void setFinalStatus(String finalStatus) {
        this.finalStatus = finalStatus;
    }

    public XsdElement getElementByName(String name) {
        if (StringUtils.isNotEmpty(name) && CollectionUtils.isNotEmpty(elements)) {
            for (XsdElement xsdElement : elements) {
                if (name.equals(xsdElement.getName())) {
                    return xsdElement;
                }
            }
        }

        return null;
    }
}
