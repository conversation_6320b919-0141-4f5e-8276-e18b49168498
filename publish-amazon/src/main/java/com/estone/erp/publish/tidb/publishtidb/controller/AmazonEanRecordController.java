package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecordCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonEanRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2024-07-09 16:21:02
 */
@RestController
@RequestMapping("amazonEanRecord")
public class AmazonEanRecordController {
    @Resource
    private AmazonEanRecordService amazonEanRecordService;

    @PostMapping
    public ApiResult<?> postAmazonEanRecord(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonEanRecord": // 查询列表
                    CQuery<AmazonEanRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonEanRecordCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonEanRecord> results = amazonEanRecordService.search(cquery);
                    return results;
                case "addAmazonEanRecord": // 添加
                    AmazonEanRecord amazonEanRecord = requestParam.getArgsValue(new TypeReference<AmazonEanRecord>() {});
                    amazonEanRecordService.insert(amazonEanRecord);
                    return ApiResult.newSuccess(amazonEanRecord);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAmazonEanRecord(@PathVariable(value = "id", required = true) long id) {
        AmazonEanRecord amazonEanRecord = amazonEanRecordService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(amazonEanRecord);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAmazonEanRecord(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAmazonEanRecord": // 单个修改
                    AmazonEanRecord amazonEanRecord = requestParam.getArgsValue(new TypeReference<AmazonEanRecord>() {});
                    amazonEanRecordService.updateByPrimaryKeySelective(amazonEanRecord);
                    return ApiResult.newSuccess(amazonEanRecord);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "api/toOrder/{eanPrefix}")
    public ApiResult<?> checkExistEanPrefixRecord(@PathVariable(value = "eanPrefix", required = true) String eanPrefix) {
        int recordCount= amazonEanRecordService.checkExistEanPrefixRecord(eanPrefix.trim());
        return ApiResult.newSuccess(recordCount);
    }
}