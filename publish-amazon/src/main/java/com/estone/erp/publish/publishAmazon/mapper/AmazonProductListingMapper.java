package com.estone.erp.publish.publishAmazon.mapper;


import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AmazonProductListingMapper {
    int countByExample(AmazonProductListingExample example);

    int deleteByPrimaryKey(@Param("list") List<Long> ids, @Param("tableIndex") String tableIndex);

    int insert(AmazonProductListing record);

    AmazonProductListing selectByPrimaryKey(@Param("id") Long id, @Param("tableIndex") String tableIndex);

    AmazonProductListing selectBySkuAndAccountNumber(@Param("sellerSku") String sellerSku, @Param("accountNumber") String accountNumber, @Param("tableIndex") String tableIndex);

    List<AmazonProductListing> selectByExample(AmazonProductListingExample example);

    List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example);

    List<String> selectMerchantShippingGroupByExample(AmazonProductListingExample example);

    int updateByExampleSelective(@Param("record") AmazonProductListing record, @Param("example") AmazonProductListingExample example);

    int updateByPrimaryKeySelective(AmazonProductListing record);

    int updateProductMsgByExampleSelective(@Param("record") AmazonProductListing record, @Param("example") AmazonProductListingExample example);

    int updateBySellerSkuAndAccountNumber(AmazonProductListing record);

    /**
     * 根据sellerSkuList 和账号，修改下架状态以及下架时间
     *
     * @param record
     * @param selllerSkuList
     */
    void updateProductsOffSaleBySellerSkuList(@Param("record") AmazonProductListing record, @Param("selllerSkuList") List<String> selllerSkuList);

    /**
     * 根据ID列表批量更新产品下架状态（一次SQL完成，包含firstOfflineDate逻辑）
     * 使用 IFNULL(firstOfflineDate, NOW()) 确保只有为NULL时才设置首次下架时间
     *
     * @param idsToUpdate 需要更新的产品ID列表
     * @param tableIndex 表索引
     */
    void batchUpdateProductsOffSaleByIds(@Param("idsToUpdate") List<Long> idsToUpdate, @Param("tableIndex") String tableIndex);

    List<String> selectSellerSkuList(AmazonProductListingExample example);

    void batchInsertProductListing(@Param("list") List<AmazonProductListing> amazonProductListingList, @Param("tableIndex") String tableIndex);

    void batchUpdateBySellerSkuAndAccountNumber(@Param("list") List<AmazonProductListing> amazonProductListingList, @Param("tableIndex") String tableIndex);

    /**
     * 批量更新运费
     *
     * @param list
     * @param tableIndex
     */
    void batchUpdateShippingCostBySellerSkuAndAccountNumber(@Param("list") List<AmazonProductListing> list, @Param("tableIndex") String tableIndex);

    /**
     * 批量更新毛利、毛利率
     *
     * @param list
     * @param tableIndex
     */
    void batchUpdateGrossProfitBySellerSkuAndAccountNumber(@Param("list") List<AmazonProductListing> list, @Param("tableIndex") String tableIndex);

    /**
     * 只给从amazon平台同步方法使用，对部分字段有特殊处理
     *
     * @param amazonProductListing
     */
    void updateSyncReportBySellerSkuAndAccountNumber(AmazonProductListing amazonProductListing);

    /**
     * 只给从amazon平台同步方法使用，对部分字段有特殊处理
     *
     * @param list
     * @param tableIndex
     */
    void batchUpdateInventoryReportBySellerSkuAndAccountNumber(@Param("list") List<AmazonProductListing> list, @Param("tableIndex") String tableIndex);

    /**
     * 从amazon平台同步详情方法使用
     *
     * @param list
     * @param tableIndex
     */
    void batchUpdateListingDetailMsgBySellerSkuAndAccountNumber(@Param("list") List<AmazonProductListing> list, @Param("tableIndex") String tableIndex);

    /**
     * 从平台ListingItem 接口单个同步更新，对部分字段有特殊处理
     *
     * @param amazonProductListing
     */
    void updateListingItemMsgBySellersku(AmazonProductListing amazonProductListing);

    void updateProducDetailByAsin(AmazonProductListing amazonProductListing);

    List<AmazonProductListing> selectPageList(AmazonProductListingExample example);

    List<AmazonProductListing> selectLackParentAsinAccountList(@Param("tableIndex") String tableIndex);

    /**
     * 批量更新关联模板id
     *
     * @param amazonProductListingList
     * @param tableIndex
     */
    void batchUpdateRelationTemplateId(@Param("list") List<AmazonProductListing> amazonProductListingList, @Param("tableIndex") String tableIndex);

    int deleteBySellerSkuAndAccountNumber(@Param("accountNumber") String accountNumber, @Param("sellerSku") String sellerSku, @Param("tableIndex") String tableIndex);

    int batchDeleteBySellerSkuAndAccountNumber(@Param("accountNumber") String accountNumber, @Param("list") List<String> sellerSku, @Param("tableIndex") String tableIndex);


    /**
     * 根据账号状态下架产品
     *
     * @param amazonProductListing
     */
    void deleteProductByAccountStatus(AmazonProductListing amazonProductListing);

    /**
     * 批量更新刊登角色
     *
     * @param listingList
     */
    void batchUpdatePublishRoleByTemplate(@Param("list") List<AmazonProductListing> listingList, @Param("tableIndex") String tableIndex);

    /**
     * 根据id批量更新刊登角色
     *
     * @param listingList
     * @param tableIndex
     */
    void batchUpdatePublishRoleById(@Param("list") List<AmazonProductListing> listingList, @Param("tableIndex") String tableIndex);

    /**
     * 根据articleNumber批量更新主sku
     *
     * @param listingList
     * @param tableIndex
     */
    void batchUpdateMainSkuByArticleNumber(@Param("list") List<AmazonProductListing> listingList, @Param("tableIndex") String tableIndex);

    /**
     * 清空毛利、毛利率
     *
     * @param amazonProductListing
     */
    void updateGrossProfitNullByAccountNumber(AmazonProductListing amazonProductListing);

    /**
     * 查询指定日期前新建的产品数据
     *
     * @param example
     * @return
     */
    List<AmazonProductListing> selectNewProductsByOpenDate(AmazonProductListingExample example);

    List<String> selectAccountNumberByExample(AmazonProductListingExample example);

    /**
     * 根据id清除asin关系
     *
     * @param ids
     * @param tableIndex
     */
    void clearAsinRelationByIds(@Param("ids") List<Long> ids, @Param("tableIndex") String tableIndex);
}
