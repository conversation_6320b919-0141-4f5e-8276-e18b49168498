package com.estone.erp.publish.amazon.enums;

/**
 * @Description 自动刊登错误原因备注
 **/
public enum AmazonAutoPublishLogReasonEnum {

    NOT_PUBLISH_CATEGORY("NOT_PUBLISH_CATEGORY","不刊登分类：该SPU分类系统不做刊登"),
    SPU_NOT_COUNTRY_TEMPLATE("SPU_NOT_COUNTRY_TEMPLATE","范本：该SPU无刊登范本"),
    SPU_INFRINGEMENT_FORBIDDEN("SPU_INFRINGEMENT_FORBIDDEN","侵权禁售：该SPU侵权禁售"),
    SPU_STOP_ARCHIVED("SPU_STOP_ARCHIVED","停产存档：该SPU停产存档"),
    ACCOUNT_REPEAT_PUBLISH("ACCOUNT_REPEAT_PUBLISH","重复刊登：该SPU已刊登"),
    ACCOUNT_NOT_SET_CATEGORY("ACCOUNT_NOT_SET_CATEGORY","分类：店铺无配置该SPU分类"),
    ACCOUNT_MISSING_AUTO_MSG("ACCOUNT_MISSING_AUTO_MSG","店铺问题：店铺配置不全"),
    ACCOUNT_NOT_NORMALE("ACCOUNT_NOT_NORMALE","账号问题：店铺非正常状态"),
    ACCOUNT_LIMIT_MOUNT_BEYOND("ACCOUNT_LIMIT_MOUNT_BEYOND","账号：刊登数量超出"),
    GET_PRODUCT_SYSYTEM_SPU_STATUS_FAIL("GET_PRODUCT_SYSYTEM_SPU_STATUS_FAIL","调用产品系统查询 sku状态失败"),
    ;

    AmazonAutoPublishLogReasonEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
