package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogExample;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-17 15:12:00
 */
public interface AmazonInfringementWordFrequencyStatisticsLogService {
    int countByExample(AmazonInfringementWordFrequencyStatisticsLogExample example);

    CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> search(CQuery<AmazonInfringementWordFrequencyStatisticsLogCriteria> cquery);

    List<AmazonInfringementWordFrequencyStatisticsLog> selectByExample(AmazonInfringementWordFrequencyStatisticsLogExample example);

    AmazonInfringementWordFrequencyStatisticsLog selectByPrimaryKey(Long id);

    int insert(AmazonInfringementWordFrequencyStatisticsLog record);

    int updateByPrimaryKeySelective(AmazonInfringementWordFrequencyStatisticsLog record);

    int updateByExampleSelective(AmazonInfringementWordFrequencyStatisticsLog record, AmazonInfringementWordFrequencyStatisticsLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> getAmazonInfringementWordFrequencyStatisticsLogPage(Long relationId, Integer limit, Integer offset);
}