package com.estone.erp.publish.amazon.bo;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.common.enums.AutoPublishEnums;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/3 11:52
 * @description
 */
@Getter
@Setter
public class AmazonAutoPublishParam {

    //根据范本id刊登
    private Integer byTemplateId;
    //根据类目映射id刊登
    private Integer byCategoryMappingId;

    //模板id
    private Integer id;
    //主sku
    private String parentSku;
    //账号
    private String accountNumber;
    //站点
    private String country;
    //上架开始时间
    private String startTime;
    //上架时间间隔
    private Integer intervalTime;
    // 刊登角色
    private Integer publishRole;
    // 刊登类型
    private Integer publishType;
    // 数据来源
    private Integer skuDataSource;
    /**
     * 1: spu刊登 2: 组合刊登 3: 冠通大健云仓
     * @see AutoPublishEnums
     */
    private Integer productSource;

    /**
     * 模板类型：1:XSD 2:JSON
     *
     * @see com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums
     */
    private Integer interfaceType = TemplateInterfaceTypeEnums.XSD.getCode();

    /**
     * 单批次刊登数量
     */
    private Integer number;

    /**
     * 临时数值
     */
    private Integer temporaryNum;

    public Date parseStartTime() {
        return  DateUtils.parseDate(this.getStartTime(), DateUtils.STANDARD_DATE_PATTERN);
    }
}
