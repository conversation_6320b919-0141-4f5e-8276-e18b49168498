package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 上传数据xml策略抽象类
 */
@Slf4j
public abstract class AbstractSubmitFeedXmlStrategy<T> implements SubmitFeedXmlStrategy<T> {

    /**
     * 
     * @Description: 创建结构为AmazonEnvelope的XmlBuilder
     *
     * @param messageType 消息类型
     * @param purgeAndReplace 是否清除并替换
     * @param merchantId merchantId
     * @return XmlBuilder
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public XmlBuilder buildAmazonEnvelope(String messageType, boolean purgeAndReplace, String merchantId) {
        XmlBuilder xmlBuilder = new XmlBuilder();
        Element root = new Element("AmazonEnvelope");
        root.addAttr("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance").addAttr("xsi:noNamespaceSchemaLocation",
                "amzn-envelope.xsd");
        // Header
        Element header = root.create("Header");
        header.create("DocumentVersion", "1.01");
        header.create("MerchantIdentifier", merchantId);
        root.create("MessageType", messageType);
        root.create("PurgeAndReplace", String.valueOf(purgeAndReplace));

        xmlBuilder.setRoot(root);
        return xmlBuilder;
    }

    /**
     * 
     * @Description: 检查参数是否满足条件
     *
     * @param publishData 刊登数据
     * @return boolean
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    public boolean checkParams(PublishData<T> publishData) {
        if (publishData == null || CollectionUtils.isEmpty(publishData.getUnitDatas())) {
            log.warn("param[publishData] must not be null, please check args.");
            return false;
        }

        return true;
    }

    /**
     * 
     * @Description: 获取税码
     *
     * @param taxCode
     * @return 税码
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    public String getTaxCode(String taxCode) {
        if (StringUtils.isEmpty(taxCode)) {
            return AmazonConstant.DEFAULT_PRODUCT_TAX_CODE;
        }

        return taxCode;
    }

    @Override
    public String transferProduct2Xml(PublishData<T> publishData) {
        log.warn("empty implement, please check this method[transferProduct2Xml].");
        return null;
    }

    @Override
    public String transferProductRelationship2Xml(PublishData<T> publishData) {
        log.warn("empty implement, please check this method[transferProductRelationship2Xml].");
        return null;
    }

    @Override
    public String transferProductPrice2Xml(PublishData<T> publishData) {
        log.warn("empty implement, please check this method[transferProductPrice2Xml].");
        return null;
    }

    @Override
    public String transferProductInventory2Xml(PublishData<T> publishData) {
        log.warn("empty implement, please check this method[transferProductInventory2Xml].");
        return null;
    }

    @Override
    public String transferProductImage2Xml(PublishData<T> publishData) {
        log.warn("empty implement, please check this method[transferProductImage2Xml].");
        return null;
    }
}
