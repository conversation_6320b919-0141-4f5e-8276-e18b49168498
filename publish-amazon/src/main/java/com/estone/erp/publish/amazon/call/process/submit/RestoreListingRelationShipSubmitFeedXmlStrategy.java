package com.estone.erp.publish.amazon.call.process.submit;

import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.XmlBuilder;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.xsd.model.AttributeWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.TypeWrapper;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 还原产品关系上传数据xml策略实现类
 */
@Component
@Slf4j
public class RestoreListingRelationShipSubmitFeedXmlStrategy extends AbstractSubmitFeedXmlStrategy<AmazonListingParentRelationshipTemp> {

    private static String XML_VERSION ="<?xml version=\"1.0\" ?><AmazonEnvelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:noNamespaceSchemaLocation=\"amzn-envelope.xsd\">";

    public String transfer2Xml(PublishData<AmazonListingParentRelationshipTemp> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        String productType ="Home.Home";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");


       XmlBuilder xmlBuilder = buildAmazonEnvelope("Product", false, publishData.getAccount().getMerchantId());
        /* Element root = xmlBuilder.getRoot();

        List<AmazonListingParentRelationshipTemp> amazonListingParentRelationshipTempList = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp : amazonListingParentRelationshipTempList) {
            String parentSellersku = amazonListingParentRelationshipTemp.getSellerSku();
            List<String> sonSellerskuList = amazonListingParentRelationshipTemp.getSellerskuList();
            if (CollectionUtils.isNotEmpty(sonSellerskuList)) {
                msgId2SkuMap.put(messageId, parentSellersku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Product
                Element product = message.create("Product");
                product.create("SKU", parentSellersku);
                //原Asin码
                Element standardProductID = product.create("StandardProductID");
                standardProductID.create("Type", "ASIN");
                standardProductID.create("Value", amazonListingParentRelationshipTemp.getSonAsin());


                String productType = template.getProductType();
                ElementWrapper productDataWrapper = ProductWrapper.getProductData(productType);
                ElementWrapper variationTheme = ProductWrapper.getVariationTheme(productDataWrapper, productType);
                if (variationTheme == null || StringUtils.isEmpty(template.getVariationThemes())) {
                    //log.error("amazon template[{}]--productType[{}] has no variationTheme", parentSku, productType);
                    String msg = "not set variationTheme Value";
                    publishData.addErrorSku(parentSku, msg);
                    for (AmazonSku amazonSku : amazonSkus) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), msg);
                    }
                    continue;
                }

                XsdRouteData variantThemeData = new XsdRouteData();
                variantThemeData.setRoute(variationTheme.getRoute());
                variantThemeData.setValues(Arrays.asList(template.getVariationThemes()));
                List<XsdRouteData> routeDatas = XsdUtils.mergeNewList(template.getAmazonExtralData().getProductData(),
                        Arrays.asList(variantThemeData));
                productDataWrapper = ProductWrapper.fillElementWrapperValues(productDataWrapper, routeDatas);
                ElementWrapper parentage = ProductWrapper.selectParentage(productDataWrapper,
                        variationTheme.getRoute());
                if (parentage == null) {
                    //log.error("amazon template[{}]--productType[{}] has no parentage", parentSku, productType);
                    String msg = String.format("productType[%s]--has no the xsd element: Parentage", productType);
                    publishData.addErrorSku(parentSku, msg);
                    for (AmazonSku amazonSku : amazonSkus) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), msg);
                    }
                    continue;
                }

                parentage.setValues(Arrays.asList("parent"));
                Map<String, ElementWrapper> themeRoutes = ProductWrapper.getThemeRoutes(productDataWrapper,
                        variationTheme.getType().getRestriction().getEnumerations());

                msgId2SkuMap.put(messageId, parentSku);
                publishData.addSku2SellerSku(template.getParentSku(), parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Product
                Element product = message.create("Product");
                product.create("SKU", parentSku);
                product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                // DescriptionData
                //变体设置关键字的时候，parentSku设置为任意一个子sku的关键字
                generateDescriptionDataElement(product, template, category, amazonSkus.get(0).getSku());
                // ProductData
                generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);
                for (AmazonSku amazonSku : amazonSkus) {
                    msgId2SkuMap.put(messageId, amazonSku.getSellerSKU());
                    // Message
                    message = root.create("Message");
                    message.create("MessageID", String.valueOf(messageId));
                    messageId++;
                    message.create("OperationType", operationType.name());
                    // Product
                    product = message.create("Product");
                    String sellerSku = amazonSku.getSellerSKU();
                    product.create("SKU", sellerSku);
                    publishData.addSku2SellerSku(amazonSku.getSku(), sellerSku);
                    publishData.addSkuSpFlagMap(amazonSku.getSku(),template.getSkuDataSource());
                    //upc豁免
                    if (null != template.getUpcExempt() && BooleanUtils.isFalse(template.getUpcExempt())) {
                        Element standardProductID = product.create("StandardProductID");
                        standardProductID.create("Type", amazonSku.getStandardProdcutIdType());
                        standardProductID.create("Value", amazonSku.getStandardProdcutIdValue());
                    }
                    product.create("ProductTaxCode", getTaxCode(template.getProductTaxCode()));
                    product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                    Element condition = product.create("Condition");
                    condition.create("ConditionType", amazonSku.getCondition());
                    if (StringUtils.isNotBlank(amazonSku.getCondition())
                            && StringUtils.isNotBlank(amazonSku.getConditionNote()) && !AmazonConstant.DEFAULT_CONDITION_TYPE.equals(template.getCondition())) {
                        condition.create("ConditionNote", AmazonUtils.toHtml(amazonSku.getConditionNote()));
                    }

                    // DescriptionData
                    generateDescriptionDataElement(product, template, category, amazonSku.getSku());
                    // ProductData
                    parentage.setValues(Arrays.asList("child"));
                    routeDatas = AmazonUtils.transferAmazonSkuThemes2RouteDatas(amazonSku, themeRoutes);
                    productDataWrapper = ProductWrapper.fillElementWrapperValues(productDataWrapper, routeDatas);
                    generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);
                    if (BooleanUtils.isTrue(generateIsHeatSensitiveElement(template.getProductType()))) {
                        product.create("IsHeatSensitive", "false");
                    }else {
                        Optional<Boolean> optionalBool = Optional.ofNullable(template.getHeatSensitiveValue());
                        if (StringUtils.isNotBlank(template.getHeatSensitive()) && optionalBool.isPresent()) {
                            product.create("IsHeatSensitive", BooleanUtils.toStringTrueFalse(template.getHeatSensitiveValue()));
                        }
                    }
                }
            }



            }
            else if (CollectionUtils.isNotEmpty((amazonSkus = template.getAmazonSkus()))) {
                ElementWrapper productDataWrapper = ProductWrapper.getProductData(productType);
                ElementWrapper variationTheme = ProductWrapper.getVariationTheme(productDataWrapper, productType);
                if (variationTheme == null || StringUtils.isEmpty(amazonListingParentRelationshipTemp.getVariationTheme())) {
                    String msg = "not set variationTheme Value";
                    publishData.addErrorSku(parentSellersku, msg);
                    for (String sonSellerSku : sonSellerskuList) {
                        publishData.addErrorSku(sonSellerSku, msg);
                    }
                    continue;
                }

                XsdRouteData variantThemeData = new XsdRouteData();
                variantThemeData.setRoute(variationTheme.getRoute());
                variantThemeData.setValues(Arrays.asList(template.getVariationThemes()));
                List<XsdRouteData> routeDatas = XsdUtils.mergeNewList(template.getAmazonExtralData().getProductData(),
                        Arrays.asList(variantThemeData));
                productDataWrapper = ProductWrapper.fillElementWrapperValues(productDataWrapper, routeDatas);
                ElementWrapper parentage = ProductWrapper.selectParentage(productDataWrapper,
                        variationTheme.getRoute());
                if (parentage == null) {
                    //log.error("amazon template[{}]--productType[{}] has no parentage", parentSku, productType);
                    String msg = String.format("productType[%s]--has no the xsd element: Parentage", productType);
                    publishData.addErrorSku(parentSku, msg);
                    for (AmazonSku amazonSku : amazonSkus) {
                        publishData.addErrorSku(amazonSku.getSellerSKU(), msg);
                    }
                    continue;
                }

                parentage.setValues(Arrays.asList("parent"));
                Map<String, ElementWrapper> themeRoutes = ProductWrapper.getThemeRoutes(productDataWrapper,
                        variationTheme.getType().getRestriction().getEnumerations());

                msgId2SkuMap.put(messageId, parentSku);
                publishData.addSku2SellerSku(template.getParentSku(), parentSku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Product
                Element product = message.create("Product");
                product.create("SKU", parentSku);
                product.create("LaunchDate", sdf.format(AmazonUtils.getUTCTime()));
                // DescriptionData
                //变体设置关键字的时候，parentSku设置为任意一个子sku的关键字
                generateDescriptionDataElement(product, template, category, amazonSkus.get(0).getSku());
                // ProductData
                generateElementByElementWrapper(product.create("ProductData"), productDataWrapper);

            }
        }*/
        return xmlBuilder.builder();
    }

    @Override
    public String transferProduct2Xml(PublishData<AmazonListingParentRelationshipTemp> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp = publishData.getUnitDatas().get(0);
        String brand = amazonListingParentRelationshipTemp.getBrandName();
        String variationTheme = amazonListingParentRelationshipTemp.getVariationTheme();
        String title = amazonListingParentRelationshipTemp.getItemName();
        if (StringUtils.isBlank(brand) || StringUtils.isBlank(variationTheme) || StringUtils.isBlank(title)){
            return null;
        }
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        msgId2SkuMap.put(messageId, amazonListingParentRelationshipTemp.getSellerSku());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        // 默认拼Home.Home
        String productType = "Home.Home";
        String parentOperationType = OperationType.Update.name();
        String countryOfOrigin = "CN";
        String parentParentage = "parent";
        String sonOperationType = OperationType.PartialUpdate.name();
        String sonParentage = "child";
        StringBuilder sb = new StringBuilder();
        sb.append(XML_VERSION);
        String merchantIdAndParentOperationXml = "<Header><DocumentVersion>1.01</DocumentVersion><MerchantIdentifier>"
                + publishData.getAccount().getMerchantId()
                + "</MerchantIdentifier></Header><MessageType>Product</MessageType><PurgeAndReplace>false</PurgeAndReplace><Message><MessageID>1</MessageID><OperationType>Update</OperationType>";
        sb.append(merchantIdAndParentOperationXml);
        String parentSellerSkuXml = "<Product><SKU>" + amazonListingParentRelationshipTemp.getSellerSku() + "</SKU>";
        sb.append(parentSellerSkuXml);
        //String standardProductIDXml = "<StandardProductID><Type>ASIN</Type><Value>" + amazonListingParentRelationshipTemp.getSonAsin() + "</Value></StandardProductID>";
        //sb.append(standardProductIDXml);
        String launchDateXml = "<LaunchDate>" + sdf.format(AmazonUtils.getUTCTime()) + "</LaunchDate>";
        sb.append(launchDateXml);
        title = title.replaceAll("&","&amp;");
        String itemNameXml = "<DescriptionData><Title>" + title + "</Title>";
        sb.append(itemNameXml);
        String brandNameXml = "<Brand>" + amazonListingParentRelationshipTemp.getBrandName() + "</Brand>";
        sb.append(brandNameXml);
        if (StringUtils.isNotBlank(amazonListingParentRelationshipTemp.getManufacturer())) {
            String manufacturerXml = "<Manufacturer>" + amazonListingParentRelationshipTemp.getManufacturer() + "</Manufacturer>";
            sb.append(manufacturerXml);
        }

        if (StringUtils.isNotBlank(amazonListingParentRelationshipTemp.getModelNumber())) {
            String mfrPartNumberXml = "<MfrPartNumber>" + amazonListingParentRelationshipTemp.getModelNumber() + "</MfrPartNumber>";
            sb.append(mfrPartNumberXml);
        }

        if (StringUtils.isNotBlank(amazonListingParentRelationshipTemp.getBrowseNodeId())) {
            String recommendedBrowseNodeXml = "<RecommendedBrowseNode>" + amazonListingParentRelationshipTemp.getBrowseNodeId() + "</RecommendedBrowseNode><IsExpirationDatedProduct>false</IsExpirationDatedProduct></DescriptionData>";
            sb.append(recommendedBrowseNodeXml);
        }

        String variationThemeXml = "<ProductData><Home><ProductType><Home><VariationData><VariationTheme>" + amazonListingParentRelationshipTemp.getVariationTheme() + "</VariationTheme></VariationData></Home></ProductType>";
        sb.append(variationThemeXml);

        String parentageXml = "<Parentage>" + parentParentage + "</Parentage>";
        sb.append(parentageXml);

        String countryOfOriginXml = "<CountryOfOrigin>" + countryOfOrigin + "</CountryOfOrigin></Home></ProductData></Product></Message>";
        sb.append(countryOfOriginXml);

        //子产品拼接
        for (String sonSellersku : amazonListingParentRelationshipTemp.getSellerskuList()){
            messageId++;
            String sonMessgeXml = "<Message><MessageID>" + messageId + "</MessageID><OperationType>" + sonOperationType +
                    "</OperationType><Product><SKU>" + sonSellersku + "</SKU>"
                    + launchDateXml + "<ProductData><Home><Parentage>" + sonParentage + "</Parentage><CountryOfOrigin>"
                    + countryOfOrigin + "</CountryOfOrigin></Home></ProductData></Product></Message>";
            sb.append(sonMessgeXml);
            msgId2SkuMap.put(messageId, sonSellersku);
        }

        String endXml="</AmazonEnvelope>";
        sb.append(endXml);
        return sb.toString();
    }

    @Override
    public String transferProductRelationship2Xml(PublishData<AmazonListingParentRelationshipTemp> publishData) {
        if (!checkParams(publishData)) {
            return null;
        }

        XmlBuilder xmlBuilder = buildAmazonEnvelope("Relationship", false, publishData.getAccount().getMerchantId());
        Element root = xmlBuilder.getRoot();
        List<AmazonListingParentRelationshipTemp> amazonListingParentRelationshipTempList = publishData.getUnitDatas();
        Map<Integer, String> msgId2SkuMap = new HashMap<Integer, String>();
        publishData.setMsgId2SkuMap(msgId2SkuMap);
        int messageId = 1;
        OperationType operationType = publishData.getOperationType();
        for (AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp : amazonListingParentRelationshipTempList) {
            String parentSellersku = amazonListingParentRelationshipTemp.getSellerSku();
            List<String> sonSellerskuList = amazonListingParentRelationshipTemp.getSellerskuList();
            if (CollectionUtils.isNotEmpty(sonSellerskuList)) {
                msgId2SkuMap.put(messageId, parentSellersku);
                // Message
                Element message = root.create("Message");
                message.create("MessageID", String.valueOf(messageId));
                messageId++;
                message.create("OperationType", operationType.name());
                // Relationship
                Element relationship = message.create("Relationship");
                relationship.create("ParentSKU", parentSellersku);
                sonSellerskuList.forEach(sonSellersku -> {
                    Element relation = relationship.create("Relation");
                    relation.create("SKU", sonSellersku);
                    relation.create("Type", "Variation");
                });
            }
        }

        return xmlBuilder.builder();
    }

    private void generateDescriptionDataElement(Element product, AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp) {
        ElementWrapper descDataWrapper = ProductWrapper.fillElementWrapperValues(ProductWrapper.descriptionData,
                null);
        descDataWrapper.setItemWrapperValue("Title", amazonListingParentRelationshipTemp.getItemName());
        descDataWrapper.setItemWrapperValue("Brand", amazonListingParentRelationshipTemp.getBrandName());
        descDataWrapper.setItemWrapperValue("Manufacturer", amazonListingParentRelationshipTemp.getManufacturer());
        descDataWrapper.setItemWrapperValue("MfrPartNumber", amazonListingParentRelationshipTemp.getModelNumber());
        generateElementByElementWrapper(product, descDataWrapper);
    }

    private void generateElementByElementWrapper(Element element, ElementWrapper elementWrapper) {
        if (elementWrapper == null) {
            return;
        }
        boolean flag =  elementWrapper.getIgnore() || elementWrapper.getSelected() || elementWrapper.getRequired();
        if (!flag) {
            return;
        }

        if (!elementWrapper.getIsLeaf()) {
            Element sub = element.create(elementWrapper.getName());
            for (Entry<String, ElementWrapper> entry : elementWrapper.getItems().entrySet()) {
                if (entry.getValue() != null) {
                    generateElementByElementWrapper(sub, entry.getValue());
                }
            }
        }
        else {
            int occurs = elementWrapper.getOccurs();
            List<AttributeWrapper> attrs = elementWrapper.getAttrs();
            TypeWrapper type = elementWrapper.getType();
            for (int i = 0; i < occurs; i++) {
                String value = AmazonUtils.getListIndexValue(elementWrapper.getValues(), i);
                if (StringUtils.isNotEmpty(value)) {
                    if (type != null && "xsd:normalizedString".equals(type.getName())) {
                        value = AmazonUtils.toHtml(value);
                    }
                    Element sub = element.create(elementWrapper.getName(), value);
                    if (CollectionUtils.isNotEmpty(attrs)) {
                        for (AttributeWrapper attrWrapper : attrs) {
                            String attrValue = AmazonUtils.getListIndexValue(attrWrapper.getValues(), i);
                            if (StringUtils.isNotEmpty(attrValue)) {
                                sub.addAttr(attrWrapper.getName(), attrValue);
                            }
                        }
                    }
                }
            }
        }
    }
}
