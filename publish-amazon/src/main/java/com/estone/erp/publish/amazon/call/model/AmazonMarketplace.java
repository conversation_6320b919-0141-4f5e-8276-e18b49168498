package com.estone.erp.publish.amazon.call.model;

/**
 * amazon端点
 * 
 * <AUTHOR>
 *
 */
public class AmazonMarketplace {
    private String marketplace;

    private String marketplaceId;

    private String endpoint;

    private String desc;

    private String currency;

    public AmazonMarketplace() {

    }

    public AmazonMarketplace(String marketplace, String marketplaceId, String endpoint, String desc, String currency) {
        super();
        this.marketplace = marketplace;
        this.marketplaceId = marketplaceId;
        this.endpoint = endpoint;
        this.desc = desc;
        this.currency = currency;
    }

    public String getMarketplace() {
        return marketplace;
    }

    public void setMarketplace(String marketplace) {
        this.marketplace = marketplace;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
