package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonPriceUpdateRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPriceUpdateRecord;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPriceUpdateRecordService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * amazon调价记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Service
public class AmazonPriceUpdateRecordServiceImpl extends ServiceImpl<AmazonPriceUpdateRecordMapper, AmazonPriceUpdateRecord> implements AmazonPriceUpdateRecordService {

    /**
     * 根据处理报告更新价格记录状态
     *
     * @param processReport        处理报告
     * @param amazonProductListing listing
     */
    @Override
    public void updatePriceRecordByProcessReport(AmazonProcessReport processReport, AmazonProductListing amazonProductListing) {
        LambdaUpdateWrapper<AmazonPriceUpdateRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmazonPriceUpdateRecord::getAccountNumber, processReport.getAccountNumber());
        updateWrapper.eq(AmazonPriceUpdateRecord::getSellerSku, amazonProductListing.getSellerSku());
        updateWrapper.set(AmazonPriceUpdateRecord::getStatus, processReport.getStatus() ? 1 : 2);
        updateWrapper.set(AmazonPriceUpdateRecord::getRemark, processReport.getResultMsg());
        updateWrapper.set(AmazonPriceUpdateRecord::getReportId, processReport.getId());
        updateWrapper.set(AmazonPriceUpdateRecord::getUpdatedTime, LocalDateTime.now());
        update(updateWrapper);

    }

    @Override
    public List<String> getExistSellerSkuList(String accountNumber, List<String> sellerSkuList) {
        LambdaQueryWrapper<AmazonPriceUpdateRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonPriceUpdateRecord::getAccountNumber, accountNumber);
        queryWrapper.in(AmazonPriceUpdateRecord::getSellerSku, sellerSkuList);
        queryWrapper.select(AmazonPriceUpdateRecord::getSellerSku);
        List<AmazonPriceUpdateRecord> amazonPriceUpdateRecordList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(amazonPriceUpdateRecordList)) {
            return Lists.newArrayList();
        }
        return amazonPriceUpdateRecordList.stream().map(AmazonPriceUpdateRecord::getSellerSku).distinct().collect(Collectors.toList());
    }

    /**
     * @param wrapper
     * @return
     */
    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AmazonPriceUpdateRecord> wrapper) {
        List<Map<Object, Object>> mapList = baseMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }


}
