package com.estone.erp.publish.amazon.util;

import com.estone.erp.publish.base.pms.enums.CountryEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-03 18:03
 */
public class AmazonDBRoutUtil {

    /**
     * 获取产品Listing表后缀集合列表
     * @return
     */
    public static List<String> getTableIndexList() {
        return Arrays.asList("_frsgtr", "_ukinbr", "_demxau", "_usjpae", "_itnlseeg", "_escasa");
    }

    public static String getTableIndex(String site) {
        // 按照站点
        if (StringUtils.isNotEmpty(site)) {
            if ((CountryEnum.FR.getSite()).equalsIgnoreCase(site) || (CountryEnum.SG.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.TR.getSite()).equalsIgnoreCase(site) || (CountryEnum.BE.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.IE.getSite()).equalsIgnoreCase(site)) {
                return "_frsgtr";
            } else if ((CountryEnum.UK.getSite()).equalsIgnoreCase(site) || (CountryEnum.IN.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.BR.getSite()).equalsIgnoreCase(site)) {
                return "_ukinbr";
            } else if ((CountryEnum.DE.getSite()).equalsIgnoreCase(site) || (CountryEnum.MX.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AU.getSite()).equalsIgnoreCase(site)) {
                return "_demxau";
            } else if ((CountryEnum.US.getSite()).equalsIgnoreCase(site) || (CountryEnum.JP.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AE.getSite()).equalsIgnoreCase(site)) {
                return "_usjpae";
            } else if ((CountryEnum.IT.getSite()).equalsIgnoreCase(site) || (CountryEnum.NL.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.SE.getSite()).equalsIgnoreCase(site) || (CountryEnum.EG.getSite()).equalsIgnoreCase(site)) {
                return "_itnlseeg";
            } else if ((CountryEnum.ES.getSite()).equalsIgnoreCase(site) || (CountryEnum.CA.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.NP.getSite()).equalsIgnoreCase(site)|| (CountryEnum.PL.getSite()).equalsIgnoreCase(site)) {
                return "_escasa";
            }
        }
        throw new IllegalArgumentException(site+",无对应站点分表");
    }
}
