package com.estone.erp.publish.amazon.enums;

public enum FollowSellSpecificOperationType {

    /**
     * 批量刊登 刊登完产品定时任务需刊登价格 库存
     */
    BATCH_PUBLISH("batch_publish", "批量刊登"),

    /**
     * 刊登产品
     */
    PUBLISH_PRODUCT("publish_product", "刊登产品"),

    /**
     * 刊登价格
     */
    PUBLISH_PRICE("publish_price", "刊登价格"),

    /**
     * 刊登库存
     */
    PUBLISH_INVENTORY("publish_inventory", "刊登库存"),

    /**
     * 下架/删除
     */
    DELETE("delete", "下架/删除"),
   ;

    FollowSellSpecificOperationType(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    /**
     * 状态code
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
