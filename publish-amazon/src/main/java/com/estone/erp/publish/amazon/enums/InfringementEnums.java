package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface InfringementEnums {


    @Getter
    @AllArgsConstructor
    enum InfringementType {
        INFRINGEMENT("侵权"),
        PLATFORM_RULE("平台规则"),
        PRODUCT_LIMIT_SELL("产品限售"),
        LOGISTICS_RULE("物流规则");

        private final String desc;

        public boolean isTrue(String type) {
            return this.desc.equals(type);
        }
    }


    @Getter
    @AllArgsConstructor
    enum InfringementWord {
        COPYRIGHT("版权侵权"),
        TRADEMARK("商标侵权"),
        DESIGN_PATENT("外观专利侵权"),
        INVENTION_PATENT("发明专利侵权"),
        LAW_FIRMS_AGENT("律所代理"),
        IMG_TRADEMARK("图片包含商标");
        private final String desc;

        public boolean isTrue(String type) {
            return this.desc.equals(type);
        }
    }

}
