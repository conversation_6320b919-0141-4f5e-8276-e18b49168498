package com.estone.erp.publish.amazon.enums;

public enum AmazonListingitemtypeEnum {

    Monomer_Item(1,"单体"),
    Maleparent_Item(2,"父体"),
    Vriant_Item(3,"变体"),
    ;

    AmazonListingitemtypeEnum(int statusCode, String statusMsg){
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }
    private int statusCode;

    private String statusMsg;

    public int getStatusCode() {
        return statusCode;
    }

    public Boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return code == this.getStatusCode();
    }

    public String getStatusMsg() {
        return statusMsg;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public void setStatusMsg(String statusMsg) {
        this.statusMsg = statusMsg;
    }

    public static String getStatusMsg(Integer statusCode) {
        if (statusCode == null) {
            return null;
        }
        for (AmazonListingitemtypeEnum value : values()) {
            if (value.getStatusCode() == statusCode) {
                return value.getStatusMsg();
            }
        }
        return null;
    }
}
