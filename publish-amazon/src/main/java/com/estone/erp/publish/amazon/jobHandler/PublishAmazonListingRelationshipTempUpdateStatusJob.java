package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.service.AmazonListingParentRelationshipTempService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时更新超额下架父体提交产品和关系数据状态
 */
@Slf4j
@Component
public class PublishAmazonListingRelationshipTempUpdateStatusJob extends AbstractJobHandler {

    public PublishAmazonListingRelationshipTempUpdateStatusJob() {
        super("PublishAmazonListingRelationshipTempUpdateStatusJob");
    }

    @Resource
    private AmazonListingParentRelationshipTempService amazonListingParentRelationshipTempService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;

    @Data
    static class InnerParam{
        //店鋪账号
        private List<String>  accountNumberList;
    }


    @XxlJob("PublishAmazonListingRelationshipTempUpdateStatusJob")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        PublishAmazonListingRelationshipTempJobHandler.InnerParam innerParam = null;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, PublishAmazonListingRelationshipTempJobHandler.InnerParam.class);
                XxlJobLogger.log("本次执行的参数信息 {}, uuid->{}", JSON.toJSONString(innerParam));
            }catch (Exception e){
                ReturnT fail = new ReturnT(500, "参数错误。例子：{\"accountNumberList\": [\"xxx\",\"aaa\"]}");
                fail.setContent(e.getMessage());
                return fail;
            }
        }
        if(innerParam == null){
            innerParam = new PublishAmazonListingRelationshipTempJobHandler.InnerParam();
        }

        List<String> accountNumberList = innerParam.getAccountNumberList();
        if (CollectionUtils.isEmpty(accountNumberList)){
            accountNumberList = amazonListingParentRelationshipTempService.getAccountNumberList();
        }
        for (String accountNumber : accountNumberList) {
            amazonListingImageUpdateStatus(accountNumber);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 修改Listing图片 状态回写
     */
    private void amazonListingImageUpdateStatus(String accountNumber) {

        //Integer relationId = SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType) ? 54 : 55;
        String filed = "account_number,data_value,relation_id,result_msg,status,finish_date";
        try{
            List<AmazonListingParentRelationshipTemp> selectProductStatusIsNullList = amazonListingParentRelationshipTempService.selectProductStatusIsNullByAccountNumber(accountNumber);
            if (CollectionUtils.isNotEmpty(selectProductStatusIsNullList)) {
                List<String> updateSellerskuList = selectProductStatusIsNullList.stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
                AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
                amazonProcessReportExample.createCriteria()
                        .andAccountNumberEqualTo(accountNumber)
                        .andRelationIdEqualTo(54)
                        .andDataValueIn(updateSellerskuList)
                        .andRelationTypeEqualTo(ProcessingReportTriggleType.Restore_Listing_Relationship.name())
                        .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                        .andStatusCodeEqualTo("Complete");
                amazonProcessReportExample.setFiledColumns(filed);
                List<AmazonProcessReport> updateStatusList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
                // 回写状态
                if (CollectionUtils.isNotEmpty(updateStatusList)) {
                    // 回写状态
                    List<AmazonListingParentRelationshipTemp> amazonListingParentRelationshipTempList = new ArrayList<>(updateStatusList.size());
                    updateStatusList.stream().forEach(o -> {
                        AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp = new AmazonListingParentRelationshipTemp();
                        amazonListingParentRelationshipTemp.setSellerSku(o.getDataValue());
                        amazonListingParentRelationshipTemp.setAccountNumber(o.getAccountNumber());
                        amazonListingParentRelationshipTemp.setProductSuccessStatus(o.getStatus());
                        amazonListingParentRelationshipTemp.setProductErrormsg(o.getResultMsg());
                        amazonListingParentRelationshipTemp.setProductSuccessTime(new Timestamp(o.getFinishDate().getTime()));
                        amazonListingParentRelationshipTempList.add(amazonListingParentRelationshipTemp);
                    });
                    amazonListingParentRelationshipTempService.batchUpdateProductStatus(amazonListingParentRelationshipTempList);
                }
            }
        }catch (Exception e){
            log.error(accountNumber + " 更新产品状态报错：" + e.getMessage());
        }

        try{
            // 处理关系
            List<AmazonListingParentRelationshipTemp> selecRelationStatusIsNullList = amazonListingParentRelationshipTempService.selectRelationStatusIsNullByAccountNumber(accountNumber);
            if (CollectionUtils.isNotEmpty(selecRelationStatusIsNullList)) {
                List<String> updateSellerskuList = selecRelationStatusIsNullList.stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
                AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
                amazonProcessReportExample.createCriteria()
                        .andAccountNumberEqualTo(accountNumber)
                        .andRelationIdEqualTo(55)
                        .andDataValueIn(updateSellerskuList)
                        .andRelationTypeEqualTo(ProcessingReportTriggleType.Restore_Listing_Relationship.name())
                        .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue())
                        .andStatusCodeEqualTo("Complete");
                amazonProcessReportExample.setFiledColumns(filed);
                amazonProcessReportExample.setOrderByClause("finish_date asc");
                List<AmazonProcessReport> updateStatusList = amazonProcessReportService.selectFiledColumnsByExample(amazonProcessReportExample);
                // 回写状态
                if (CollectionUtils.isNotEmpty(updateStatusList)) {
                    // 回写状态
                    List<AmazonListingParentRelationshipTemp> amazonListingParentRelationshipTempList = new ArrayList<>(updateStatusList.size());
                    updateStatusList.stream().forEach(o -> {
                        AmazonListingParentRelationshipTemp amazonListingParentRelationshipTemp = new AmazonListingParentRelationshipTemp();
                        amazonListingParentRelationshipTemp.setSellerSku(o.getDataValue());
                        amazonListingParentRelationshipTemp.setAccountNumber(o.getAccountNumber());
                        amazonListingParentRelationshipTemp.setRelationshipSuccessStatus(o.getStatus());
                        amazonListingParentRelationshipTemp.setRelationshipErrormsg(o.getResultMsg());
                        amazonListingParentRelationshipTemp.setRelationshipSuccessTime(new Timestamp(o.getFinishDate().getTime()));
                        amazonListingParentRelationshipTempList.add(amazonListingParentRelationshipTemp);
                    });
                    amazonListingParentRelationshipTempService.batchUpdateRelationStatus(amazonListingParentRelationshipTempList);
                }
            }
        }catch (Exception e){
            log.error(accountNumber + " 更新关系状态报错：" + e.getMessage());
        }
    }

}
