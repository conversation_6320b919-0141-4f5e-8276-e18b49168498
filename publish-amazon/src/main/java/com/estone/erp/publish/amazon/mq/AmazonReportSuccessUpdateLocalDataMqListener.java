package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.AmazonProductListingEsBulkProcessor;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonHolidayUpdateRecordService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPriceUpdateRecordService;
import com.rabbitmq.client.Channel;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.response.AmazonFeedsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-28 17:01
 */
@Slf4j
public class AmazonReportSuccessUpdateLocalDataMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;
    @Autowired
    private AmazonProductListingService amazonProductListingService;
    @Autowired
    private AmazonProductListingEsBulkProcessor productListingEsBulkProcessor;
    @Autowired
    private AmazonHolidayUpdateRecordService amazonHolidayUpdateRecordService;
    @Autowired
    private AmazonPriceUpdateRecordService amazonPriceUpdateRecordService;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        Boolean isSuccess = executeHandler(body);
        if (isSuccess) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * @param body messageBody
     */
    private Boolean executeHandler(String body) {
        try {
            AmazonFeedsResult feedResult = JSON.parseObject(body, AmazonFeedsResult.class);
            if (!feedResult.getStatus()) {
                failCallback(feedResult);
                // 失败退出
                return false;
            }
            // 查询处理报告
            AmazonProcessReport processReport = getProcessReport(feedResult);
            if (processReport == null) {
                log.info("处理报告为空:{},{},{}", feedResult.getFeedType(), feedResult.getAccountNumber(), feedResult.getDocId());
                return true;
            }
            // 根据处理报告记录的改前改后值更新本地数据, 先更新MySQL再更新ES
            updateLocalDataValue(processReport);
            return true;
        } catch (Exception e) {
            log.error("异常,Body:{}", body, e);
            return false;
        }
    }

    private void failCallback(AmazonFeedsResult feedResult) {
//        if (SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue().equals(feedResult.getFeedType())) {
//            // 修改备货期状态
//            updateFailFulfillmentStatus(feedResult);
//            return;
//        }

        if (SpFeedType.POST_PRODUCT_PRICING_DATA.getValue().equals(feedResult.getFeedType())) {
            // 修改价格
            AmazonProcessReport processReport = getProcessReport(feedResult);
            if (processReport == null) {
                return;
            }
            AmazonProductListing amazonProductListing = getAmazonProductListing(processReport);
            if (amazonProductListing == null) {
                return;
            }
            amazonPriceUpdateRecordService.updatePriceRecordByProcessReport(processReport, amazonProductListing);
        }
    }

    private void updateFailFulfillmentStatus(AmazonFeedsResult feedResult) {
        AmazonProcessReport processReport = getProcessReport(feedResult);
        if (processReport == null) {
            return;
        }
        if (ProcessingReportTriggleType.Listing_Fulfillment_Latency.name().equals(processReport.getRelationType())) {
            // 发货期
            updateFulfillmentLatency(processReport);
        }
    }

    private void updateLocalDataValue(AmazonProcessReport processReport) {
        if (SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue().equals(processReport.getFeedType())) {
            // 更新库存
            updateLocalInventory(processReport);
            if (ProcessingReportTriggleType.Listing_Fulfillment_Latency.name().equals(processReport.getRelationType())) {
                // 发货期
                updateFulfillmentLatency(processReport);
            }
            return;
        }
        if (SpFeedType.POST_PRODUCT_PRICING_DATA.getValue().equals(processReport.getFeedType())) {
            // 更新价格
            updateLocalPrice(processReport);
        }
    }

    private void updateFulfillmentLatency(AmazonProcessReport processReport) {
        String afterFulfillmentLatencyValue = processReport.getAfterFulfillmentLatencyValue();
        if (StringUtils.isBlank(afterFulfillmentLatencyValue)) {
            return;
        }

        if (Boolean.TRUE.equals(processReport.getStatus())) {
            AmazonProductListing amazonProductListing = getAmazonProductListing(processReport);
            if (amazonProductListing == null) {
                return;
            }
            amazonProductListing.setPrice(null);
            amazonProductListing.setQuantity(null);
            amazonProductListing.setFulfillmentLatency(Integer.valueOf(afterFulfillmentLatencyValue));
            amazonProductListing.setUpdatedBy(processReport.getCreatedBy());
            updateDBData(amazonProductListing);
        }

        // 春节任务记录 春节任务期间才需要开启
//        amazonHolidayUpdateRecordService.updateHolidayReportStatus(processReport);
    }

    private void updateLocalPrice(AmazonProcessReport processReport) {
        String afterPriceValue = processReport.getAfterPriceValue();
        if (StringUtils.isBlank(afterPriceValue)) {
            return;
        }

        AmazonProductListing amazonProductListing = getAmazonProductListing(processReport);
        if (amazonProductListing == null) {
            return;
        }
        amazonProductListing.setQuantity(null);
        amazonProductListing.setPrice(Double.valueOf(afterPriceValue));
        amazonProductListing.setUpdatedBy(processReport.getCreatedBy());
        // 更新数据库
        updateDBData(amazonProductListing);
        productListingEsBulkProcessor.updateProductListingPrice(amazonProductListing);
        // ES-11278 amazon调价记录
        amazonPriceUpdateRecordService.updatePriceRecordByProcessReport(processReport, amazonProductListing);
    }


    private void updateLocalInventory(AmazonProcessReport processReport) {
        String afterQuantityValue = processReport.getAfterQuantityValue();
        if (StringUtils.isBlank(afterQuantityValue)) {
            return;
        }

        AmazonProductListing amazonProductListing = getAmazonProductListing(processReport);
        if (amazonProductListing == null) {
            return;
        }
        amazonProductListing.setPrice(null);
        amazonProductListing.setQuantity(Integer.valueOf(afterQuantityValue));
        amazonProductListing.setUpdatedBy(processReport.getCreatedBy());
        updateDBData(amazonProductListing);
        productListingEsBulkProcessor.updateProductListingQuantity(amazonProductListing);
    }


    private void updateDBData(AmazonProductListing amazonProductListing) {
        try {
            amazonProductListingService.updateByPrimaryKeySelective(amazonProductListing);
        } catch (Exception e) {
            log.error("更新本地数据异常：{}",JSON.toJSONString(amazonProductListing), e);
        }
    }

    private AmazonProductListing getAmazonProductListing(AmazonProcessReport processReport) {
        String site = amazonProductListingService.getSiteByAccount(processReport.getAccountNumber());
        if (StringUtils.isBlank(site)) {
            return null;
        }
        String tableIndex = amazonProductListingService.getTableIndex(site);
        AmazonProductListingExample example = new AmazonProductListingExample();
        example.setColumns("id,accountNumber,site,sellerSku,price,quantity,updateDate,updatedBy");
        example.setTableIndex(tableIndex);
        example.createCriteria()
                .andAccountNumberEqualTo(processReport.getAccountNumber())
                .andSellerSkuEqualTo(processReport.getDataValue());

        List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example);
        if (CollectionUtils.isEmpty(amazonProductListings)) {
            return null;
        }
        return amazonProductListings.get(0);
    }

    private AmazonProcessReport getProcessReport(AmazonFeedsResult feedResult) {
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.setFiledColumns("id,status,data_value,feed_type,account_number,relation_type,previous_price_value,after_price_value,previous_quantity_value,after_quantity_value,after_fulfillmentLatency_value");
        AmazonProcessReportExample.Criteria criteria = example.createCriteria()
                .andAccountNumberEqualTo(feedResult.getAccountNumber())
                .andFeedTypeEqualTo(feedResult.getFeedType())
                .andDataValueEqualTo(feedResult.getDocId());
        if (StringUtils.isNotEmpty(feedResult.getTaskId())) {
            criteria.andTaskIdEqualTo(feedResult.getTaskId());
        }
        List<AmazonProcessReport> processReports = amazonProcessReportService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(processReports)) {
            return null;
        }
        return processReports.get(0);
    }
}
