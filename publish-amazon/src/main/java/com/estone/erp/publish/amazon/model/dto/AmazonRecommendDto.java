package com.estone.erp.publish.amazon.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class AmazonRecommendDto implements Serializable {

    private Integer id;

    /**
     * 站点 database column amazon_recommend.site
     */
    private String site;

    /**
     * 价格 database column amazon_recommend.price
     */
    private String price;

    /**
     * 7天销量 database column amazon_recommend.order_last_7d_count
     */
    private String orderLast7dCount;

    /**
     * 7天日均销量 database column amazon_recommend.order_last_7d_count_day
     */
    private String orderLast7dCountDay;

    /**
     * 14天销量 database column amazon_recommend.order_last_14d_count
     */
    private String orderLast14dCount;

    /**
     * 14天日均销量 database column amazon_recommend.order_last_14d_count_day
     */
    private String orderLast14dCountDay;

    /**
     * 30天销量 database column amazon_recommend.order_last_30d_count
     */
    private String orderLast30dCount;

    /**
     * 30天日均销量 database column amazon_recommend.order_last_30d_count_day
     */
    private String orderLast30dCountDay;

    /**
     * 30天动销天数 database column amazon_recommend.order_days_within_30d
     */
    private String orderDaysWithin30d;

    /**
     * 7天/30天日均销量 database column amazon_recommend.order_last_7d_30d_count_day
     */
    private String orderLast7d30dCountDay;

    /**
     * 状态。是否启用（1 是 0 否） database column amazon_recommend.status
     */
    private Integer status;

    /**
     * 创建人 database column amazon_recommend.created_by
     */
    private String createdBy;

    /**
     * 创建时间 database column amazon_recommend.create_date
     */
    private String createDate;

    /**
     * 修改人 database column amazon_recommend.updated_by
     */
    private String updatedBy;

    /**
     * 修改时间 database column amazon_recommend.update_date
     */
    private Timestamp updateDate;

}