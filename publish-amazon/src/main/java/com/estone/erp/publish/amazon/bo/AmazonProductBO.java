package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.model.AmazonProduct;
import lombok.Data;

import java.util.List;

@Data
public class AmazonProductBO extends AmazonProduct {
    private List<AmazonVariantBO> amazonVariants;

    public static AmazonProductBO transform(AmazonProduct product){
        return JSON.parseObject(JSON.toJSONString(product), AmazonProductBO.class);
    }

    public static List<AmazonProductBO> transform(List<AmazonProduct> product){
        return JSON.parseArray(JSON.toJSONString(product), AmazonProductBO.class);
    }
}
