package com.estone.erp.publish.amazon.call.xsd.parse;

import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.call.xsd.XsdBuilder;
import com.estone.erp.publish.amazon.call.xsd.XsdWrapperAdapter;
import com.estone.erp.publish.amazon.call.xsd.model.ElementWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.ProductWrapper;
import com.estone.erp.publish.amazon.call.xsd.model.XsdElement;
import com.estone.erp.publish.amazon.call.xsd.model.XsdType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ProductXsdParser {
    private static Set<String> descriptionDataIgnoreFields = new HashSet<String>();

    //后端默认处理取模板数据
    public static void initIgnoreFields() {
        descriptionDataIgnoreFields.add("Title");
        descriptionDataIgnoreFields.add("Brand");
        descriptionDataIgnoreFields.add("Description");
        descriptionDataIgnoreFields.add("BulletPoint");
        descriptionDataIgnoreFields.add("Manufacturer");
        descriptionDataIgnoreFields.add("MfrPartNumber");
        descriptionDataIgnoreFields.add("SearchTerms");
        descriptionDataIgnoreFields.add("ItemType");
    }

    public static void parse() {
        long startTime = System.currentTimeMillis();
        XsdBuilder builder = XsdBuilder.getXsdBuilder("Product.xsd");
        Assert.notNull(builder);
        if (descriptionDataIgnoreFields.isEmpty()) {
            initIgnoreFields();
        }

        XsdType productXsdType = builder.getXsdElement("Product").getType();
        ProductWrapper.descriptionData = XsdWrapperAdapter
                .wrapXsdElement(productXsdType.getElementByName("DescriptionData"), null, null);
        ProductWrapper.descriptionData.setRequired(true);
        for (String field : descriptionDataIgnoreFields) {
            setItemIgnore(ProductWrapper.descriptionData, field);
        }

        List<XsdElement> productDataEles = productXsdType.getElementByName("ProductData").getType().getElements();
        for (XsdElement xsdElement : productDataEles) {
            String itemName = xsdElement.getName();

            ElementWrapper productDataWrapper = XsdWrapperAdapter.wrapXsdElement(xsdElement, ele -> {
                if ("ProductType".equals(ele.getName()) || "ClothingType".equals(ele.getName())) {  //兼容"ClothingType"
                    XsdType type = ele.getType();
                    ElementWrapper productTypeWrapper = XsdWrapperAdapter.wrapXsdElement(ele, null, null);
                    productTypeWrapper.setRequired(true);
                    if (type.getIsSimple()) {
                        for (String enumeration : type.getRestriction().getEnumerations()) {
                            productTypeWrapper.getType().setValue(enumeration);
                            ProductWrapper.productTypes.put(itemName + "." + enumeration,
                                    XsdUtils.clone(productTypeWrapper));
                        }
                    }
                    else {
                        Map<String, ElementWrapper> productTypeItems = productTypeWrapper.getItems();
                        productTypeWrapper.setItems(null);
                        for (Entry<String, ElementWrapper> entry : productTypeItems.entrySet()) {
                            LinkedHashMap<String, ElementWrapper> productTypeNewItems = new LinkedHashMap<String, ElementWrapper>();
                            productTypeNewItems.put(entry.getKey(), entry.getValue());

                            ElementWrapper productTypeClone = XsdUtils.clone(productTypeWrapper);
                            productTypeClone.setItems(productTypeNewItems);
                            ProductWrapper.productTypes.put(itemName + "." + entry.getValue().getName(),
                                    productTypeClone);
                        }
                    }
                    if ("ClothingType".equals(ele.getName())) {
                        return true;
                    }
                    return false;
                }

                return true;
            }, null);

            productDataWrapper.setRequired(true);
            ProductWrapper.productDatas.put(itemName, productDataWrapper);
        }

        ProductWrapper.addProductTypePrefixRoute();
        Set<String> productDataKeys = ProductWrapper.productDatas.keySet();
        Set<String> productTypeKeys = ProductWrapper.productTypes.keySet();
        Map<String, List<String>> groups = productTypeKeys.stream().collect(Collectors.groupingBy(key -> {
            return StringUtils.split(key, ".")[0];
        }));

        for (String key : productDataKeys) {
            List<String> items = groups.get(key);
            if (CollectionUtils.isNotEmpty(items)) {
                ProductWrapper.productTypeKeys.addAll(items);
            }
            else {
                ProductWrapper.productTypeKeys.add(key);
            }
        }

        log.warn("[Amazon]parse Product.xsd completed in " + (System.currentTimeMillis() - startTime) + "ms");
    }

    private static void setItemIgnore(ElementWrapper elementWrapper, String itemName) {
        ElementWrapper itemWrapper = elementWrapper.getItemWrapper(itemName);
        if (itemWrapper != null) {
            itemWrapper.setIgnore(true);
        }
    }
}
