package com.estone.erp.publish.amazon.call.model;

import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public class XsdRouteData {
    private String route;

    private List<String> values;

    private Map<String, List<String>> attrs;

    private boolean required;

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    public Map<String, List<String>> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, List<String>> attrs) {
        this.attrs = attrs;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public List<String> getAttrValuesByName(String attrName) {
        if (MapUtils.isEmpty(attrs)) {
            return null;
        }

        return attrs.get(attrName);
    }

    @Override
    public int hashCode() {
        if (route == null) {
            return super.hashCode();
        }

        return route.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof XsdRouteData) {
            return route.equals(((XsdRouteData) obj).getRoute());
        }

        return false;
    }
}
