package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-19 下午2:37
 */
@Getter
@AllArgsConstructor
public enum AmazonGPSRLocalEnums {
    UK("UK", "英国", "en", "en_US"),
    DE("DE", "德国", "de", "de_DE"),
    ES("ES", "西班牙", "es", "es_ES"),
    FR("FR", "法国", "fr", "fr_FR"),
    IT("IT", "意大利", "it", "it_IT"),
    PL("PL", "波兰", "pl", "pl_PL"),
    BE("BE", "比利时", "fr", "fr_BE"),
    NL("NL", "荷兰", "nl", "nl_NL"),
    SE("SE", "瑞典", "sv", "sv_SE");

    private final String site;
    private final String site_cn;
    private final String lang;
    private final String contentLang;


    public static AmazonGPSRLocalEnums getEnumBySiteCn(String siteCn) {
        for (AmazonGPSRLocalEnums e : AmazonGPSRLocalEnums.values()) {
            if (e.getSite_cn().equals(siteCn)) {
                return e;
            }
        }
        return null;
    }

    public static List<String> getNeedGPSRSiteList() {
        return List.of(DE.site, ES.site, FR.site, IT.site, PL.site, BE.site, NL.site, SE.site);
    }
}
