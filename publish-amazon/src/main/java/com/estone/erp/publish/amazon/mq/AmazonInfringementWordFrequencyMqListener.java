package com.estone.erp.publish.amazon.mq;

import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyLogService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: publish
 * @description: 亚马逊词频统计MQ监听器
 * @author: dinghong
 * @create: 2024/6/21
 **/
@Slf4j
@Component
public class AmazonInfringementWordFrequencyMqListener implements ChannelAwareMessageListener {

    @Resource
    private AmazonInfringementWordFrequencyStatisticsService amazonInfringementWordFrequencyStatisticsService;

    @Autowired
    private AmazonInfringementWordFrequencyLogService amazonInfringementWordFrequencyLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(messageBody)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }

        try {

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}