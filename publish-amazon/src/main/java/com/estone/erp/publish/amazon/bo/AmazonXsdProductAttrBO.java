package com.estone.erp.publish.amazon.bo;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.model.AmazonXsdProductAttr;
import org.apache.commons.lang3.StringUtils;

public class AmazonXsdProductAttrBO extends AmazonXsdProductAttr {

    private AmazonExtralData amazonExtralData;

    public AmazonExtralData getAmazonExtralData() {
        if (amazonExtralData != null) {
            return amazonExtralData;
        }

        amazonExtralData = StringUtils.isEmpty(getExtraData()) ? new AmazonExtralData()
                : JSON.parseObject(getExtraData(), AmazonExtralData.class);

        return amazonExtralData;
    }
}
