package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLogCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLogExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-26 17:09:15
 */
public interface AmazonPublishOperationLogService {
    int countByExample(AmazonPublishOperationLogExample example);

    CQueryResult<AmazonPublishOperationLog> search(CQuery<AmazonPublishOperationLogCriteria> cquery);

    List<AmazonPublishOperationLog> selectByExample(AmazonPublishOperationLogExample example);

    AmazonPublishOperationLog selectByPrimaryKey(Long id);

    int insert(AmazonPublishOperationLog record);

    int updateByPrimaryKeySelective(AmazonPublishOperationLog record);

    int updateByExampleSelective(AmazonPublishOperationLog record, AmazonPublishOperationLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void insertBatch(List<AmazonPublishOperationLog> operationLogs);

    int resetGpsrOperationLog();

    int updateSelectiveIdVersion(AmazonPublishOperationLog operationLog, int lastVersion);
}