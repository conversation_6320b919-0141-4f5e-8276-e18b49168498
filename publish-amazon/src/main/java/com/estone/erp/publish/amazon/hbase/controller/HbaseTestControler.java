package com.estone.erp.publish.amazon.hbase.controller;

import com.estone.erp.publish.amazon.hbase.model.AmazonListingCheckword;
import com.estone.erp.publish.amazon.hbase.model.StudentModel;
import com.estone.erp.publish.amazon.hbase.service.AmazonListingCheckwordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/4 17:49
 * @description
 */
@Slf4j
@RestController
@RequestMapping("hbase/test")
public class HbaseTestControler {

    /*@Resource
    private StudentDao studentDao;
    @Resource
    private AmazonListingCheckwordDao amazonListingCheckwordDao;
    @Resource
    private AmazonListingCheckwordService amazonListingCheckwordService;

    @RequestMapping("/getInfo")
    public Object getInfo(){
        List<StudentModel> studentModels = studentDao.selectAll();

        for (StudentModel bean : studentModels) {
            List<StudentModel> list = studentDao.selectStudent(bean.getName());
            log.info("name:{} -> {}", bean.getName(), list);
        }

        return studentModels;
    }

    @RequestMapping("/listingPage")
    public Object listingPage(){
        String account = "US-obvctdcwx";
        List<AmazonListingCheckword> result = amazonListingCheckwordService.selectPageByAccount(account);


//        String gtId = "0";
//        int limit = 100;
//        List<AmazonListingCheckword> result =new ArrayList<>();
//        do {
//            List<AmazonListingCheckword> list = amazonListingCheckwordDao.selectPageByAccount(account, gtId, limit);
//            log.info("size :{}", list.size());
//            if(CollectionUtils.isEmpty(list)){
//                break;
//            }
//            gtId = list.get(list.size()-1).getId();
//
//            result.addAll(list);
//            if(list.size() < limit){
//                break;
//            }
//
//        }while (true);

        Map<String, List<AmazonListingCheckword>> collect = result.stream().collect(Collectors.groupingBy(o -> o.getId()));
        log.info("size {} : {}", result.size(), collect.size());

        return result;
    }*/
}
