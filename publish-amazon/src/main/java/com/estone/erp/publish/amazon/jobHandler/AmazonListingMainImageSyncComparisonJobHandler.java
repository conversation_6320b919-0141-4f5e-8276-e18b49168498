package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.mq.model.AmazonMainImageSyncComparisonData;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Amazon在线主图对比
 * 后续增量核查数据，至检查每天新增的链接
 */
@Component
public class AmazonListingMainImageSyncComparisonJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    public AmazonListingMainImageSyncComparisonJobHandler() {
        super("amazonListingMainImageSyncComparisonJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<String> accountNumberList;
        private List<String> skuList;
        /**
         * 新创建的链接 开始时间
         */
        private String openTime;
        /**
         * 结束时间
         */
        private String endTime;
    }

    @XxlJob("amazonListingMainImageSyncComparisonJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        String openTime = innerParam.getOpenTime();
        String endTime = innerParam.getEndTime();
        if (StringUtils.isBlank(openTime) || StringUtils.isBlank(endTime)) {
            openTime = DateUtils.getStringDateBegin(-2);
            endTime = DateUtils.getStringDateEnd(-1);
        }
        Date openDate = com.estone.erp.common.util.DateUtils.parseDate(openTime, "yyyy-MM-dd HH:mm:ss");
        Date endDate = com.estone.erp.common.util.DateUtils.parseDate(endTime, "yyyy-MM-dd HH:mm:ss");

        XxlJobLogger.log("开始时间：{}，结束时间：{}", DateUtils.dateToString(openDate, "yyyy-MM-dd HH:mm:ss"), DateUtils.dateToString(endDate, "yyyy-MM-dd HH:mm:ss"));
        // 传参 同步正常账号
        AmazonAccountRelationExample relationExample = new AmazonAccountRelationExample();
        String columns = "id, account_number, account_country, account_level";
        relationExample.setFiledColumns(columns);
        AmazonAccountRelationExample.Criteria criteria = relationExample.createCriteria()
                .andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode())
                .andAccountNumberIsNotNull();
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            criteria.andAccountNumberIn(innerParam.getAccountNumberList());
        }
        List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(relationExample);
        if (CollectionUtils.isEmpty(relations)) {
            XxlJobLogger.log("无店铺数据");
            return ReturnT.SUCCESS;
        }
        List<AmazonAccountRelation> collect = relations.stream().peek(a -> {
            String accountLevel = a.getAccountLevel();
            if (StringUtils.isBlank(accountLevel)) {
                a.setAccountLevel("Z");
            }
        }).sorted(Comparator.comparing(AmazonAccountRelation::getAccountLevel)).collect(Collectors.toList());

        List<String> skuList = innerParam.getSkuList();
        // 查询
        for (AmazonAccountRelation relation : collect) {
            String accountNumber = relation.getAccountNumber();
            send(accountNumber, skuList, openDate, endDate);
        }
        return ReturnT.SUCCESS;
    }

    // 2024-03-15 00:00:00判断店铺是否跑过 这个时间开始的，就是没跑过的
    private void send(String accountNumber, List<String> skuList, Date openDate, Date endDate) {
        AmazonMainImageSyncComparisonData data = new AmazonMainImageSyncComparisonData();
        data.setFull(false);
        data.setAccountNumber(accountNumber);
        data.setSkuList(skuList);
        data.setOpenDate(DateUtils.dateToString(openDate, "yyyy-MM-dd HH:mm:ss"));
        data.setEndDate(DateUtils.dateToString(endDate, "yyyy-MM-dd HH:mm:ss"));
        XxlJobLogger.log("发送数据到队列：AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE，data: {}", JSON.toJSONString(data));
        String msgId = PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE + ":" + UUID.randomUUID();
        rabbitTemplate.convertAndSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_MAIN_IMAGE_SYNC_COMPARISON_KEY, data, (message) -> {
            message.getMessageProperties().setHeader("msg-id", msgId);
            return message;
        });
    }

}
