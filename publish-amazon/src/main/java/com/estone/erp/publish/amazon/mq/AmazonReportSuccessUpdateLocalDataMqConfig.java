package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 处理报告成功更新本地数据
 * 改价
 * 改库存
 * ---
 * <AUTHOR>
 * @date 2024-01-28 17:01
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class AmazonReportSuccessUpdateLocalDataMqConfig {

    private int reportSuccessUpdateLocalDataMqConsumers;
    private int reportSuccessUpdateLocalDataMqPrefetchCount;
    private boolean reportSuccessUpdateLocalDataMqListener;

    @Bean
    public Queue reportSuccessUpdateLocalData() {
        return new Queue(PublishQueues.AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA);
    }

    @Bean
    public Binding reportSuccessUpdateLocalDataBinding() {
        return new Binding(PublishQueues.AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA_KEY, null);
    }
    @Bean
    public AmazonReportSuccessUpdateLocalDataMqListener reportSuccessUpdateLocalDataMqListener() {
        return new AmazonReportSuccessUpdateLocalDataMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer reportSuccessUpdateLocalDataMqListenerContainer(
            AmazonReportSuccessUpdateLocalDataMqListener reportSuccessUpdateLocalDataMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA, reportSuccessUpdateLocalDataMqListener);
        return container;
    }

    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (reportSuccessUpdateLocalDataMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(reportSuccessUpdateLocalDataMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(reportSuccessUpdateLocalDataMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
