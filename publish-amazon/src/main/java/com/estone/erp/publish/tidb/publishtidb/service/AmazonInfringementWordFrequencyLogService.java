package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-05 10:31:52
 */
public interface AmazonInfringementWordFrequencyLogService {
    int countByExample(AmazonInfringementWordFrequencyLogExample example);

    CQueryResult<AmazonInfringementWordFrequencyLog> search(CQuery<AmazonInfringementWordFrequencyLogCriteria> cquery);

    List<AmazonInfringementWordFrequencyLog> selectByExample(AmazonInfringementWordFrequencyLogExample example);

    AmazonInfringementWordFrequencyLog selectByPrimaryKey(Long id);

    int insert(AmazonInfringementWordFrequencyLog record);

    int updateByPrimaryKeySelective(AmazonInfringementWordFrequencyLog record);

    int updateByExampleSelective(AmazonInfringementWordFrequencyLog record, AmazonInfringementWordFrequencyLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void batchInsert(List<AmazonInfringementWordFrequencyLog> list);

    List<TidbPageMeta<Long>> getPageMetaListByExample(AmazonInfringementWordFrequencyLogExample example);
}