package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonSpApiTotal;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonSpApiTotalMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonSpApiTotalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 请求code统计商家id 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class AmazonSpApiTotalServiceImpl extends ServiceImpl<AmazonSpApiTotalMapper, AmazonSpApiTotal> implements AmazonSpApiTotalService {

}
