package com.estone.erp.publish.amazon.call.model;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class NameValue {
    private String name;

    private String value;

    private String desc;

    private List<Attribute<String, String>> attrs;

    /**
     * 扩展变体属性，SizeMap, ColorMap
     */
    private List<NameValue> extralNameValues;

    private String extralDesc;

    public NameValue() {

    }

    public NameValue(String name, String value, String desc) {
        this.name = name;
        this.value = value;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<Attribute<String, String>> getAttrs() {
        return attrs;
    }

    public void setAttrs(List<Attribute<String, String>> attrs) {
        this.attrs = attrs;
    }

    public List<NameValue> getExtralNameValues() {
        return extralNameValues;
    }

    public void setExtralNameValues(List<NameValue> extralNameValues) {
        this.extralNameValues = extralNameValues;
    }

    @Override
    public String toString() {
        return String.valueOf(name) + ": " + String.valueOf(value);
    }

    public String getExtralDesc() {
        if (StringUtils.isEmpty(extralDesc) && CollectionUtils.isNotEmpty(extralNameValues)) {
            extralDesc = "";
            for (NameValue nameValue : extralNameValues) {
                if (StringUtils.isNotEmpty(nameValue.getValue())) {
                    extralDesc += "-" + nameValue.getValue();
                }
            }
        }

        return extralDesc;
    }

    public void setExtralDesc(String extralDesc) {
        this.extralDesc = extralDesc;
    }
}
