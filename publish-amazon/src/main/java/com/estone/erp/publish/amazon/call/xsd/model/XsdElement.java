package com.estone.erp.publish.amazon.call.xsd.model;

import java.io.Serializable;
import java.util.List;

/**
 * xsd节点
 * 
 * <AUTHOR>
 *
 */
public class XsdElement implements Serializable {
    private static final long serialVersionUID = -8393248128010225007L;

    /**
     * 已使用
     */
    private Boolean used;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点类型
     */
    private XsdType type;

    /**
     * 最小可以出现次数
     */
    private Integer minOccurs;

    /**
     * 最大可以出现次数， -1表示无限制，默认为1, 0和1时表示为单值
     */
    private Integer maxOccurs = 1;

    /**
     * 是否为单值
     */
    private Boolean isSingle;

    /**
     * 排序索引
     */
    private Integer seqIndex;

    /**
     * 属性
     */
    private List<XsdAttr> attrs;

    private List<String> documentations;

    private Boolean required;

    public Boolean getUsed() {
        return used;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public XsdType getType() {
        return type;
    }

    public void setType(XsdType type) {
        this.type = type;
    }

    public Integer getMinOccurs() {
        return minOccurs;
    }

    public void setMinOccurs(Integer minOccurs) {
        this.minOccurs = minOccurs;
    }

    public Integer getMaxOccurs() {
        return maxOccurs;
    }

    public void setMaxOccurs(Integer maxOccurs) {
        this.maxOccurs = maxOccurs;
    }

    public boolean isSingle() {
        isSingle = maxOccurs == 0 || maxOccurs == 1;
        return isSingle;
    }

    public Integer getSeqIndex() {
        return seqIndex;
    }

    public void setSeqIndex(Integer seqIndex) {
        this.seqIndex = seqIndex;
    }

    public List<XsdAttr> getAttrs() {
        return attrs;
    }

    public void setAttrs(List<XsdAttr> attrs) {
        this.attrs = attrs;
    }

    public List<String> getDocumentations() {
        return documentations;
    }

    public void setDocumentations(List<String> documentations) {
        this.documentations = documentations;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }
}
