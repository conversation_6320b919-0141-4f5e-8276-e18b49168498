package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonTemplateAuto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_template_auto.id
     */
    private Integer id;

    /**
     * 卖家帐号 database column amazon_template_auto.seller_id
     */
    private String sellerId;

    /**
     * 国家 database column amazon_template_auto.country
     */
    private String country;

    /**
     * 分类关联 database column amazon_template_auto.relative
     */
    private Boolean relative;

    /**
     * amazon平台分类browsePathById database column amazon_template_auto.category_id
     */
    private String categoryId;

    /**
     * 分类类型id database column amazon_template_auto.browse_path_by_id
     */
    private String browsePathById;

    /**
     * 分类类型 database column amazon_template_auto.product_type
     */
    private String productType;

    /**
     * 售卖形式-是否为变体 database column amazon_template_auto.sale_variant
     */
    private Boolean saleVariant;

    /**
     * ParentSKU database column amazon_template_auto.parent_SKU
     */
    private String parentSku;

    /**
     * 产品标题 database column amazon_template_auto.title
     */
    private String title;

    /**
     * 标准产品id类型 database column amazon_template_auto.standard_prodcut_id_type
     */
    private String standardProdcutIdType;

    /**
     * 标准产品id值 database column amazon_template_auto.standard_prodcut_id_value
     */
    private String standardProdcutIdValue;

    /**
     * 品牌 database column amazon_template_auto.brand
     */
    private String brand;

    /**
     * 制造商 database column amazon_template_auto.manufacturer
     */
    private String manufacturer;

    /**
     * 制造商零件编号 database column amazon_template_auto.mfr_part_number
     */
    private String mfrPartNumber;

    /**
     * 物品状况 database column amazon_template_auto.condition
     */
    private String condition;

    /**
     *  database column amazon_template_auto.condition_note
     */
    private String conditionNote;

    /**
     * 主图 database column amazon_template_auto.main_image
     */
    private String mainImage;

    /**
     * 附图 database column amazon_template_auto.extra_images
     */
    private String extraImages;

    /**
     * 产品描述 database column amazon_template_auto.description
     */
    private String description;

    /**
     * 价格 database column amazon_template_auto.standard_price
     */
    private Double standardPrice;

    /**
     * 促销价 database column amazon_template_auto.sale_price
     */
    private Double salePrice;

    /**
     * 币种 database column amazon_template_auto.currency
     */
    private String currency;

    /**
     * 促销起始日期 database column amazon_template_auto.sale_start_date
     */
    private Timestamp saleStartDate;

    /**
     * 促销结束日期 database column amazon_template_auto.sale_end_date
     */
    private Timestamp saleEndDate;

    /**
     * 总价 = 价格+运费
     */
    private Double totalPrice;

    /**
     * 促销总价 = 促销价+运费
     */
    private Double totalSalePrice;

    /**
     * 运费
     */
    private Double shippingCost;

    /**
     * 运费模板
     */
    private String shippingGroup;

    /**
     * 数量 database column amazon_template_auto.quantity
     */
    private Integer quantity;

    /**
     * 税务编码 database column amazon_template_auto.product_tax_code
     */
    private String productTaxCode;

    /**
     * 商品属性 database column amazon_template_auto.variation_themes
     */
    private String variationThemes;

    /**
     * 多属性 database column amazon_template_auto.variations
     */
    private String variations;

    /**
     * Bullet Point database column amazon_template_auto.bullet_point
     */
    private String bulletPoint;

    /**
     * Search Terms database column amazon_template_auto.search_terms
     */
    private String searchTerms;

    /**
     * 状态 database column amazon_template_auto.status
     */
    private Integer status;

    /**
     * 锁定模版 database column amazon_template_auto.is_lock
     */
    private Boolean isLock;

    /**
     * 创建时间 database column amazon_template_auto.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column amazon_template_auto.created_by
     */
    private String createdBy;

    /**
     * 修改时间 database column amazon_template_auto.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 修改人 database column amazon_template_auto.last_updated_by
     */
    private String lastUpdatedBy;

    /**
     * 样品图 database column amazon_template_auto.sample_image
     */
    private String sampleImage;

    /**
     * 扩展数据 database column amazon_template_auto.extra_data
     */
    private String extraData;

    /**
     * 步骤[模板]刊登成功 database column amazon_template_auto.step_template_status
     */
    private Boolean stepTemplateStatus;

    /**
     * sku后缀 database column amazon_template_auto.sku_suffix
     */
    private String skuSuffix;

    /**
     * 单品的平台货号 database column amazon_template_auto.seller_sku
     */
    private String sellerSku;

    /**
     * 关键词字符集 database column amazon_template_auto.search_data
     */
    private String searchData;

    /**
     * 多站点刊登关联id database column amazon_template_auto.amazon_variant_id
     */
    private Integer amazonVariantId;

    /**
     * 是否多站点刊登: 1是 0 否 database column amazon_template_auto.is_site_publish
     */
    private Boolean isSitePublish;

    /**
     * 刊登详细状态: 1 待刊登 2 刊登中 8刊登成功 9刊登失败 database column amazon_template_auto.publish_status
     */
    private Integer publishStatus;

    /**
     * 数据来源：productSystem(产品开发系统)、erpDataSystem(数据分析系统) 为空也是产品开发系统 database column amazon_template_auto.data_source
     */
    private String dataSource;

    /**
     * 关联listing操作次数 database column amazon_template_auto.listing_relation_times
     */
    private Integer listingRelationTimes;

    /**
     * 单品来源 ：1.销售开发,2.pms,3.产品开发,4.谭雅科技,5.刊登试卖 database column amazon_template_auto.single_source
     */
    private Integer singleSource;

    /**
     * SKU数据来源：1 产品系统、2  数据分析系统、3 冠通系统、4 探雅科技 database column amazon_template_auto.sku_data_source
     */
    private Integer skuDataSource;

    /**
     * 刊登类型：1 多站点刊登、2 自动刊登、3 普通刊登 database column amazon_template_auto.publish_type
     */
    private Integer publishType;

    /**
     * 生成范本的模板id database column amazon_template_auto.source_template_id
     */
    private Integer sourceTemplateId;

    /**
     * 范本状态: 0:禁用,1:启用 database column amazon_template_auto.template_status
     */
    private Integer templateStatus;

    /**
     * 不过滤侵权信息标识
     * false 过滤 默认 大多数情况是需要过滤才能刊登的
     * true  不过滤 销售手动选择不过滤刊登才会变为True
     */
    private Boolean noInfringementFilter;

    /**
     * 关联分类模板
     */
    private String categoryTemplateName;

    /**
     * 产品系统分类code路径
     */
    private String systemCategoryCodePath;

    /**
     * 产品属性[1,2] 1:关联模板 2:xsd
     */
    private String produceAttributeMode;

    /**
     * 是否有IsHeatSensitive属性
     */
    private String heatSensitive;

    /**
     * IsHeatSensitive值
     */
    private Boolean heatSensitiveValue;

    /**
     * interface_type 接口类型：1:XSD 2:JSON
     *
     * @see com.estone.erp.publish.amazon.enums.TemplateInterfaceEnums
     */
    private Integer interfaceType;
}