package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonEditListingRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Amazon listing 平台明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@RestController
@RequestMapping("listing/info")
public class AmazonListingInfoController {

    @Autowired
    private IAmazonListingInfoService amazonListingInfoService;
    @Resource
    private AmazonValidationHelper amazonValidationHelper;
    /**
     * 获取编辑 Amazon listing 信息
     */
    @PostMapping("edit")
    public ApiResult<AmazonTemplateBO> getEditAmazonListingInfo(@RequestBody AmazonEditListingRequest request) {
        return amazonListingInfoService.getEditAmazonListingInfo(request);
    }

    /**
     * 修改在线listing
     */
    @PostMapping("update")
    public ApiResult<String> updateListing(@RequestBody AmazonTemplateBO param) {
        return amazonListingInfoService.updateListing(param);
    }

    /**
     * 校验Listing数据<p>
     * 校验Listing信息 - 侵权词校验<p>
     * 侵权词提示
     *
     * @param amazonTemplateParam json字符串兼容以前的格式
     */
    @PostMapping("validation/data")
    public ApiResult<List<AmazonTemplateValidationDO>> validationListingData(@RequestBody AmazonTemplateBO amazonTemplate) {
        try {
            // 验证模板必填属性
            AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(amazonTemplate);
            validationContext.setIsCheckAttr(true);
            amazonValidationHelper.validationTemplateData(validationContext);
            return ApiResult.newSuccess(validationContext.getValidations());
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

}
