package com.estone.erp.publish.amazon.call.xsd.model;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class ProcessingReport {
    private Integer messageID;

    private String docTransId;

    private String statusCode;

    private ProcessingSummary processingSummary;

    private List<Result> results;

    public Integer getMessageID() {
        return messageID;
    }

    public void setMessageID(Integer messageID) {
        this.messageID = messageID;
    }

    public String getDocTransId() {
        return docTransId;
    }

    public void setDocTransId(String docTransId) {
        this.docTransId = docTransId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public ProcessingSummary getProcessingSummary() {
        return processingSummary;
    }

    public void setProcessingSummary(ProcessingSummary processingSummary) {
        this.processingSummary = processingSummary;
    }

    public List<Result> getResults() {
        return results;
    }

    public void setResults(List<Result> results) {
        this.results = results;
    }

    public static class ProcessingSummary {
        private Integer msgsProcessed;

        private Integer msgsSuccessful;

        private Integer msgsError;

        private Integer msgsWarn;

        public boolean isSuccess() {
            return msgsSuccessful > 0 && msgsError == 0;
        }

        public Integer getMsgsProcessed() {
            return msgsProcessed;
        }

        public void setMsgsProcessed(Integer msgsProcessed) {
            this.msgsProcessed = msgsProcessed;
        }

        public Integer getMsgsSuccessful() {
            return msgsSuccessful;
        }

        public void setMsgsSuccessful(Integer msgsSuccessful) {
            this.msgsSuccessful = msgsSuccessful;
        }

        public Integer getMsgsError() {
            return msgsError;
        }

        public void setMsgsError(Integer msgsError) {
            this.msgsError = msgsError;
        }

        public Integer getMsgsWarn() {
            return msgsWarn;
        }

        public void setMsgsWarn(Integer msgsWarn) {
            this.msgsWarn = msgsWarn;
        }
    }

    public static class Result {
        private Integer msgID;

        // Error, Warning
        private String ResultCode;

        private String ResultMsgCode;

        private String ResultDesc;

        private AdditionalInfo additionalInfo;

        public AdditionalInfo createAdditionalInfo() {
            this.additionalInfo = new AdditionalInfo();
            return this.additionalInfo;
        }

        public Integer getMsgID() {
            return msgID;
        }

        public void setMsgID(Integer msgID) {
            this.msgID = msgID;
        }

        public String getResultCode() {
            return ResultCode;
        }

        public void setResultCode(String resultCode) {
            ResultCode = resultCode;
        }

        public String getResultMsgCode() {
            return ResultMsgCode;
        }

        public void setResultMsgCode(String resultMsgCode) {
            ResultMsgCode = resultMsgCode;
        }

        public String getResultDesc() {
            return ResultDesc;
        }

        public void setResultDesc(String resultDesc) {
            ResultDesc = resultDesc;
        }

        public AdditionalInfo getAdditionalInfo() {
            return additionalInfo;
        }
    }

    public static class AdditionalInfo {
        private String sku;

        private String fulfillmentCenterID;

        private String amazonOrderID;

        private String amazonOrderItemCode;

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getFulfillmentCenterID() {
            return fulfillmentCenterID;
        }

        public void setFulfillmentCenterID(String fulfillmentCenterID) {
            this.fulfillmentCenterID = fulfillmentCenterID;
        }

        public String getAmazonOrderID() {
            return amazonOrderID;
        }

        public void setAmazonOrderID(String amazonOrderID) {
            this.amazonOrderID = amazonOrderID;
        }

        public String getAmazonOrderItemCode() {
            return amazonOrderItemCode;
        }

        public void setAmazonOrderItemCode(String amazonOrderItemCode) {
            this.amazonOrderItemCode = amazonOrderItemCode;
        }
    }

    /**
     * 合并处理报告结果
     * 
     * @param report
     * @param results
     */
    public static void mergeProcessingReportResult(AmazonProcessReport report, List<Result> results) {
        if (report == null) {
            return;
        }

        if (report.getStatus() == null) {
            report.setStatus(true);
        }

        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        if (BooleanUtils.toBoolean(report.getStatus())) {
            boolean success = true;
            for (Result result : results) {
                if ("Error".equalsIgnoreCase(result.getResultCode())) {
                    success = false;
                    break;
                }
            }

            report.setStatus(success);
        }

        String resultMsg = report.getResultMsg();
        if (StringUtils.isEmpty(resultMsg)) {
            report.setResultMsg(JSON.toJSONString(results));
        }
        else {
            results.addAll(JSON.parseArray(resultMsg, Result.class));
            report.setResultMsg(JSON.toJSONString(results));
        }
    }
}
