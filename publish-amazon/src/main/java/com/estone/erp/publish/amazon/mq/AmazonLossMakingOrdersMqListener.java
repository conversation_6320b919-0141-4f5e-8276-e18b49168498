package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.mq.model.AmazonLossMakingOrdersMsg;
import com.estone.erp.publish.amazon.service.AmazonLossMakingOrdersService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024-07-25 上午9:39
 */
@Slf4j
public class AmazonLossMakingOrdersMqListener implements ChannelAwareMessageListener {
    @Autowired
    private AmazonLossMakingOrdersService amazonLossMakingOrdersService;

    @Override
    public void onMessage(Message message, Channel channel) throws IOException {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            AmazonLossMakingOrdersMsg lossMakingOrdersMsg = JSON.parseObject(body, AmazonLossMakingOrdersMsg.class);
            amazonLossMakingOrdersService.addLossMakingOrders(lossMakingOrdersMsg);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("Amazon 亏损订单消息消费异常：", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
