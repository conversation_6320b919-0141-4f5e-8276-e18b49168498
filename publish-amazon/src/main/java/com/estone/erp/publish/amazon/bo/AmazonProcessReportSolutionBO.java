package com.estone.erp.publish.amazon.bo;

import com.estone.erp.publish.amazon.model.AmazonProcessReportSolution;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/27 15:06
 * @description
 */
@Data
public class AmazonProcessReportSolutionBO extends AmazonProcessReportSolution {

    private String sellerSku;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AmazonProcessReportSolutionBO that = (AmazonProcessReportSolutionBO) o;
        return Objects.equals(super.getReportCode(), that.getReportCode());
    }

    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), super.getReportCode());
    }
}
