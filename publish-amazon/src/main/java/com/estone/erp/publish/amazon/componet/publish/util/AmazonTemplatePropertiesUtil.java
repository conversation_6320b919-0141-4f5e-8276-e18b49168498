package com.estone.erp.publish.amazon.componet.publish.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.componet.publish.AmazonJsonSchemaFactory;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingFiledConstants;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr;
import com.estone.erp.publish.amazon.model.dto.TemplateBasicProductInfoVO;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.networknt.schema.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.util.Pair;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Amazon 属性
 *
 * <AUTHOR>
 * @date 2025-03-17 14:44
 */
@Slf4j
public class AmazonTemplatePropertiesUtil {


    /**
     * 合并模板属性
     * amazonTemplateDTO中运营配置的模版必填属性
     * admin范本中的属性
     *
     * @param amazonTemplateDTO 模版
     * @param adminTemplate     admin范本
     * @return
     */
    public static Map<String, Object> mergeTemplateAttributes(AmazonTemplateBO amazonTemplateDTO, AmazonTemplateBO adminTemplate) {
        String extraData = amazonTemplateDTO.getExtraData();
        Map<String, Object> extraDataMap;
        if (StringUtils.isBlank(extraData) || StringUtils.isBlank(adminTemplate.getExtraData())) {
            extraDataMap = new HashMap<>();
        } else {
            extraDataMap = JSON.parseObject(extraData, new TypeReference<>() {
            });
        }
        Map<String, Object> adminExtraDataMap = JSON.parseObject(adminTemplate.getExtraData(), new TypeReference<>() {
        });
        if (MapUtils.isEmpty(adminExtraDataMap)) {
            return extraDataMap;
        }

        // admin范本中的属性覆盖amazonTemplateDTO中的属性
        extraDataMap.putAll(adminExtraDataMap);
        return extraDataMap;
    }


    /**
     * 匹配技术部必填属性
     *
     * @param templateBO         模版
     * @param requiredAttributes 配置必填属性
     * @param schemaJson         schemaJson
     * @param properties         schema中属性配置信息
     */
    public static void matchTechnicalRequiredAttribute(AmazonTemplateBO templateBO, List<ProductTypeTemplateJsonAttr> requiredAttributes, String schemaJson, JSONObject properties) {
        if (CollectionUtils.isEmpty(requiredAttributes)) {
            return;
        }

        Set<String> jsonSchemaRequiredKeys = getJsonSchemaRequiredKeys(templateBO, schemaJson);
        if (CollectionUtils.isEmpty(jsonSchemaRequiredKeys)) {
            return;
        }

        Boolean saleVariant = AmazonTemplateUtils.isSaleVariant(templateBO);
        List<ProductTypeTemplateJsonAttr> filterRequiredAttributes = requiredAttributes.stream()
                .filter(attr -> attr.getType().equals(2))
                .filter(attr -> {
                    if (attr.getApplicableAttributeType() == 3) {
                        return true;
                    }
                    if (attr.getApplicableAttributeType() == 2 && saleVariant) {
                        return true;
                    }
                    return attr.getApplicableAttributeType() == 1 && !saleVariant;
                })
                .filter(attr -> jsonSchemaRequiredKeys.contains(attr.getAttributeName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterRequiredAttributes)) {
            return;
        }

        String extraData = templateBO.getExtraData();
        Map<String, Object> extraDataMap = getExtraDataMap(extraData);
        Map<String, TemplateBasicProductInfoVO.SubSkuInfo> skuInfoMap = new HashMap<>();
        AmazonTemplateBuilderHelper builderHelper = SpringUtils.getBean(AmazonTemplateBuilderHelper.class);
        // 获取产品系统信息
        ApiResult<TemplateBasicProductInfoVO> productBasisInfoResult = builderHelper.getProductBasisInfo(templateBO.getParentSku(), templateBO.getSkuDataSource());
        if (productBasisInfoResult.isSuccess()) {
            TemplateBasicProductInfoVO basicProductInfoVO = productBasisInfoResult.getResult();
            skuInfoMap = basicProductInfoVO.getSkusInfo().stream().collect(Collectors.toMap(TemplateBasicProductInfoVO.SubSkuInfo::getSku, Function.identity(), (k1, k2) -> k1));
        }

        // 代码适配属性
        List<ProductTypeTemplateJsonAttr> codeAdapterAttributes = filterRequiredAttributes.stream().filter(attr -> Boolean.TRUE.equals(attr.getCodeAdapter())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(codeAdapterAttributes)) {
//            String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "attribute", "code_adapter", 10);
            AttributeScriptExecutor.executeAttributeScript(templateBO, extraDataMap, codeAdapterAttributes, "", skuInfoMap, properties);
        }

        for (ProductTypeTemplateJsonAttr attr : filterRequiredAttributes) {
            String attrName = attr.getAttributeName();
            try {
                // 需要手动适配的属性
                if (Boolean.TRUE.equals(attr.getCodeAdapter())) {
                    continue;
                }
                // 简单判断是否JSON
                if (isJsonAttribute(attr.getAttributeValue())) {
                    extraDataMap.put(attrName, JSON.parseArray(attr.getAttributeValue()));
                }
            } catch (Exception e) {
                log.error("添加默认属性异常：key:{}, value:{},error:{}", attr.getAttributeName(), attr.getAttributeValue(), e.getMessage(), e);
            }
        }
        templateBO.setExtraData(JSON.toJSONString(extraDataMap));
        templateBO.updateAmazonSkus(templateBO.getVariations());
    }

    private static boolean isJsonAttribute(String attributeValue) {
        return attributeValue.startsWith("[") && attributeValue.endsWith("]");
    }

    public static Set<String> getJsonSchemaRequiredKeys(AmazonTemplateBO templateBO, String schemaJson) {
        Set<String> jsonSchemaRequiredKeys = new HashSet<>();
        try {
            JsonSchemaFactory jsonSchemaFactory = AmazonJsonSchemaFactory.getInstance();
            SchemaValidatorsConfig config = SchemaValidatorsConfig.builder()
                    .formatAssertionsEnabled(true)
                    .cacheRefs(true)
                    .build();
            JsonSchema schema = jsonSchemaFactory.getSchema(schemaJson, config);
            Set<ValidationMessage> validationMessages = schema.validate("{}", InputFormat.JSON, executionContext -> {
                ExecutionConfig executionConfig = executionContext.getExecutionConfig();
                executionConfig.setFormatAssertionsEnabled(true);
            });


            // 必填属性
            if (CollectionUtils.isNotEmpty(validationMessages)) {
                validationMessages.stream()
                        .filter(message -> "required".equals(message.getType()))
                        .forEach(message -> {
                            jsonSchemaRequiredKeys.add(message.getProperty());
                        });
            }
        } catch (Exception e) {
            if (templateBO != null) {
                log.error("jsonSchemaRequiredKeys error,template productType:{}, account:{}, sku:{}, user:{}", templateBO.getProductType(), templateBO.getSellerId(), templateBO.getParentSku(), templateBO.getCreatedBy(), e);
            } else {
                log.error("jsonSchemaRequiredKeys error", e);
            }
        }
        return jsonSchemaRequiredKeys;
    }


    private static Map<String, Object> getExtraDataMap(String extraData) {
        Map<String, Object> extraDataMap;
        if (StringUtils.isBlank(extraData)) {
            extraDataMap = new HashMap<>();
        } else {
            extraDataMap = JSON.parseObject(extraData, new TypeReference<>() {
            });
        }
        return extraDataMap;
    }

    /**
     * 往listing product data中添加数组形式的value值
     *
     * @param key   属性名
     * @param value 属性值
     * @return 当前对象
     */
    public static Map<String, Object> addValueProperty(String key, Object value) {
        return AmazonListingApiUtil.buildNestedArrayStructure(key, Pair.of(ListingFiledConstants.VALUE, value));
    }

    /**
     * 匹配运营配置属性，如果存在该属性则添加到模版数据中
     *
     * @param templateInfo       模板
     * @param requiredAttributes 必填属性
     */
    public static void matchOperationalRequiredAttribute(AmazonTemplateBO templateInfo, List<ProductTypeTemplateJsonAttr> requiredAttributes, JSONObject properties) {
        Set<String> propertiesKeys = properties.keySet();
        Boolean saleVariant = AmazonTemplateUtils.isSaleVariant(templateInfo);
        List<ProductTypeTemplateJsonAttr> filterRequiredAttributes = requiredAttributes.stream()
                .filter(attr -> attr.getType().equals(1))
                .filter(attr -> {
                    // 单体变体
                    if (attr.getApplicableAttributeType() == 3) {
                        return true;
                    }
                    // 变体
                    if (attr.getApplicableAttributeType() == 2 && saleVariant) {
                        return true;
                    }
                    // 单体
                    return attr.getApplicableAttributeType() == 1 && !saleVariant;
                })
                .filter(attr -> propertiesKeys.contains(attr.getAttributeName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterRequiredAttributes)) {
            return;
        }

        Map<String, Object> extraDataMap = getExtraDataMap(templateInfo.getExtraData());
        filterRequiredAttributes.forEach(attr -> {
            try {
                JSONObject attributeValue = JSON.parseObject(attr.getAttributeValue());
                JSONArray jsonArray = attributeValue.getJSONArray(attr.getAttributeName());
                extraDataMap.put(attr.getAttributeName(), jsonArray);
            } catch (Exception e) {
                log.error("添加默认属性异常：key:{}, value:{},error:{}", attr.getAttributeName(), attr.getAttributeValue(), e.getMessage(), e);
            }
        });
        templateInfo.setExtraData(JSON.toJSONString(extraDataMap));
    }

    /**
     * 匹配变体属性
     */
    public static void matchVariantTheme(AmazonTemplateBO templateInfo) {
        Boolean saleVariant = AmazonTemplateUtils.isSaleVariant(templateInfo);
        if (!saleVariant) {
            return;
        }
        // 变体主题适配
        String variationThemes = templateInfo.getVariationThemes();
        String theme = toThemesEnums(variationThemes);
        templateInfo.setVariationThemes(theme);
        // 变体规格匹配
        List<AmazonSku> amazonSkus = templateInfo.getAmazonSkus();
        for (AmazonSku sku : amazonSkus) {
            Map<String, Object> extraDataMap = getVariantThemeExtraData(sku);
            sku.setVariantAttribute(extraDataMap);
            sku.setExtraData(JSON.toJSONString(extraDataMap));
        }
        templateInfo.updateVariations(amazonSkus);
    }

    private static Map<String, Object> getVariantThemeExtraData(AmazonSku sku) {
        Map<String, Object> extraDataMap = getExtraDataMap(sku.getExtraData());
        List<NameValue> nameValues = sku.getNameValues();
        nameValues.forEach(nameValue -> {
            String name = nameValue.getName();
            String value = nameValue.getValue();
            String key;
            if (name.equalsIgnoreCase(ListingFiledConstants.COLOR_THEME)) {
                key = ListingFiledConstants.COLOR_THEME.toLowerCase();
            } else {
                key = ListingFiledConstants.SIZE_THEME.toLowerCase();
            }
            Map<String, Object> dataValue = AmazonListingApiUtil.buildNestedArrayStructure(key,
                    Pair.of(ListingFiledConstants.VALUE, value)
            );
            extraDataMap.putAll(dataValue);
        });
        return extraDataMap;
    }

    private static String toThemesEnums(String variationThemes) {
        if (StringUtils.isBlank(variationThemes)) {
            throw new BusinessException("变体属性主题为空");
        }
        if (variationThemes.equalsIgnoreCase(ListingFiledConstants.COLOR_THEME)) {
            return ListingFiledConstants.COLOR_THEME;
        }
        if (variationThemes.equalsIgnoreCase(ListingFiledConstants.SIZE_THEME)) {
            return ListingFiledConstants.SIZE_THEME;
        }
        return ListingFiledConstants.COLOR_SIZE;
    }
}
