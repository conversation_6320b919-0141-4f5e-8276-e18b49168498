package com.estone.erp.publish.amazon.enums;

/**
 * amazon listing order unit enum
 * <AUTHOR>
 */
public enum AmazonListingOrderUnitEnum {

    HOUR24("24h", "order_24H_count", "24小时销量"),
    DAY7("7d", "order_last_7d_count", "7天销量"),
    DAY14("14d", "order_last_14d_count", "14天销量"),
    DAY30("30d", "order_last_30d_count", "30天销量"),
    TOTAL("total", "order_num_total", "总销量");

    private final String unit;
    private final String filedName;
    private final String description;

    AmazonListingOrderUnitEnum(String unit, String filedName, String description) {
        this.unit = unit;
        this.filedName = filedName;
        this.description = description;
    }

    public String getUnit() {
        return unit;
    }

    public String getFiledName() {
        return filedName;
    }

    public String getDescription() {
        return description;
    }

    public boolean unitIsTrue(String unit) {
        return this.unit.equals(unit);
    }

}
