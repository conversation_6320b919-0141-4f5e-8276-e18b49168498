package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonHolidayUpdateRecord;

import java.util.List;

/**
 * <p>
 * amazon春节任务记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface AmazonHolidayUpdateRecordService extends IService<AmazonHolidayUpdateRecord> {

    void updateHolidayReportStatus(AmazonProcessReport amazonProcessReport);

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AmazonHolidayUpdateRecord> wrapper);

}
