package com.estone.erp.publish.amazon.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-09 16:05
 */
@Data
public class GatewayProxyTotal implements Serializable {
    private static final long serialVersionUID = -140967462475511472L;

    private String status;

    private DataObject data;

    @Data
    public static class DataObject{
        private String resultType;

        private List<DataResult> result;
    }

    @Data
    public static class DataResult{
        private Metric metric;

        private List<String> value;

        @Data
        public static class Metric {

            /**
             * id
             */
            private String __name__;

            /**
             * 解决方案
             */
            private String code;


            private String instance;

            private String job;

            private String merchant_id;

            private String method;

            private String url;

        }
    }



}
