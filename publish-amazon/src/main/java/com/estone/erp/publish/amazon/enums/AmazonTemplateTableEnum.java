package com.estone.erp.publish.amazon.enums;

import lombok.Getter;

/**
 * Amazon模板表枚举
 */
@Getter
public enum AmazonTemplateTableEnum {
    AMAZON_TEMPLATE("amazon_template", "最近模板"),
    // 功能废弃 ES-11430 【Amazon】去掉亚马逊角色范本页面权限及对应全部的功能权限
    AMAZON_TEMPLATE_MODEL("amazon_template_model", "范本"),

    AMAZON_TEMPLATE_2021_11("amazon_template_2021_11", "历史成功的模板"),

    AMAZON_TEMPLATE_ADMIN("amazon_template_admin", "新admin范本"),
    ;

    private String code;

    private String name;

    private AmazonTemplateTableEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }
}
