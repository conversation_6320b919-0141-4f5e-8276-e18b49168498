package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonSaleRecord;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonSaleRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonSaleRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * amazon最近出单的listing 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Service
public class AmazonSaleRecordServiceImpl extends ServiceImpl<AmazonSaleRecordMapper, AmazonSaleRecord> implements AmazonSaleRecordService {

}
