package com.estone.erp.publish.amazon.bo;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.model.AmazonFollowSell;
import org.apache.commons.lang3.StringUtils;

public class AmazonFollowSellBO extends AmazonFollowSellSuperiorBO {
    /**
     * 弃用了，sku生成规则用模块：sellerSku生成规则生成
     * @Date 2019-11-06
     * @return
     */
    public String getSellerSku() {
        if (StringUtils.isEmpty(getParentSku()) || StringUtils.isEmpty(getSkuSuffix())) {
            return getParentSku();
        }

        return AmazonConstant.FOLLOW_SELL_SKU_PREFIX + getParentSku().trim() + AmazonConstant.SKU_SUFFIX_JOIN
                + getSkuSuffix().trim();
    }
}
