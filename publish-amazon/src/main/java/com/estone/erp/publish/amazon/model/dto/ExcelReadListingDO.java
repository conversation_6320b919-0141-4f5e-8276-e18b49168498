package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-07-02 上午11:17
 */
@Data
public class ExcelReadListingDO {
    @ExcelProperty("店铺")
    private String accountNumber;
    @ExcelProperty("模板ID")
    private Integer templateId;
    @ExcelProperty("站点")
    private String site;
    @ExcelProperty("标题")
    private String title;
    @ExcelProperty("sellerSku")
    private String sellerSku;
    @ExcelProperty("ASIN")
    private String asin;
    @ExcelProperty("子ASIN")
    private String sonAsin;
    @ExcelProperty(value = "是否在线")
    private String isOnline;
    @ExcelProperty("商品状态")
    private String itemStatus;
    @ExcelProperty("价格")
    private String price;
    @ExcelProperty("毛利率")
    private String rate;
    @ExcelProperty(value = "上架时间")
    private String openDate;
    @ExcelProperty("销售")
    private String user;
}
