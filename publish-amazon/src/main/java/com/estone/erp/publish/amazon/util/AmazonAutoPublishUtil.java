package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonExtralData;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.enums.CountryEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.request.ProductNewSpuRequest;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/4 16:22
 * @description
 */
@Slf4j
public class AmazonAutoPublishUtil {

    private static SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

    private static AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);

    private static PmsSkuService pmsSkuService = SpringUtils.getBean(PmsSkuService.class);

    private static AmazonCategoryService amazonCategoryService = SpringUtils.getBean(AmazonCategoryService.class);

    /**
     * 验证模板必填参数是否完整
     *
     * @param template
     * @param accountRelation
     * @return
     */
    public static ApiResult<Object> verifyParamComplete(AmazonTemplateBO template, AmazonAccountRelation accountRelation) {
        Set<String> failMsg = new HashSet<>();
        if (template == null) {
            failMsg.add("模板数据为空！");
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }

        if (StringUtils.isBlank(template.getSellerId())) {
            failMsg.add("店铺为空！");
        }
        if (StringUtils.isBlank(template.getCountry())) {
            failMsg.add("国家为空！");
        }
        if (template.getCategoryId() == null) {
            failMsg.add("分类为空！");
        }
        if (StringUtils.isBlank(template.getProductType())) {
            failMsg.add("分类类型为空！");
        }
        if (StringUtils.isBlank(template.getTitle())) {
            failMsg.add("产品标题为空！");
        }
//        if (BooleanUtils.isFalse(template.getUpcExempt())){
//            if(StringUtils.isBlank(template.getStandardProdcutIdValue()) && !template.getSaleVariant()){
//                failMsg.add("产品ID为空！");
//            }
//            List<AmazonSku> amazonSkus = template.getAmazonSkus();
//            if (CollectionUtils.isNotEmpty(amazonSkus) && StringUtils.isEmpty(amazonSkus.get(amazonSkus.size()-1).getStandardProdcutIdValue())){
//                failMsg.add("产品ID为空！");
//            }
//        }

        if (StringUtils.isBlank(template.getManufacturer())) {
            failMsg.add("制造商为空！");
        }
        //关键词
        if (StringUtils.isBlank(template.getSearchTerms())) {
            failMsg.add("Search Terms为空！");
        }
        //分类属性
        AmazonExtralData amazonExtralData = template.getAmazonExtralData();
        if (amazonExtralData == null) {
            failMsg.add("分类属性为空！");
        } else {
            if (CollectionUtils.isEmpty(amazonExtralData.getDescriptionData()) && TemplateInterfaceTypeEnums.XSD.isTrue(template.getInterfaceType())) {
                failMsg.add("分类属性-DescriptionData为空！");
            }
//            if(CollectionUtils.isEmpty(amazonExtralData.getProductData())){
//                failMsg.add("分类属性-ProductData为空！");
//            }
        }

        if (StringUtils.isBlank(template.getBulletPoint())) {
            failMsg.add("五点描述为空！");
        }

        if (StringUtils.isBlank(template.getDescription())) {
            failMsg.add("描述为空！");
        }

        if (template.getSaleVariant()) {
            //变体
            if (StringUtils.isBlank(template.getMfrPartNumber())) {
                failMsg.add("制造商零件编号为空！");
            }
            if (CollectionUtils.isEmpty(template.getAmazonSkus())) {
                failMsg.add("变体多属性信息为空！");
            } else {
                for (AmazonSku sku : template.getAmazonSkus()) {
                    if (StringUtils.isBlank(sku.getMainImage())) {
                        failMsg.add("主图为空！");
                    }
                    if (StringUtils.isBlank(sku.getExtraImages()) || "[]".equals(sku.getExtraImages())) {
                        failMsg.add("附图为空！");
                    }
                    if (StringUtils.isBlank(sku.getSku())) {
                        failMsg.add("SKU编号为空！");
                    }
//                    if (!Boolean.TRUE.equals(template.getUpcExempt()) && StringUtils.isBlank(sku.getStandardProdcutIdValue())) {
//                        failMsg.add("产品ID为空！");
//                    }
//                    if (StringUtils.isBlank(sku.getCondition())) {
//                        failMsg.add("Condition 为空！");
//                    }
//                    if(sku.getQuantity() == null){
//                        failMsg.add("数量为空！");
//                    }
//                    if(sku.getStandardPrice() == null){
//                        failMsg.add("价格为空！");
//                    }
                }
            }
        } else {
            //单体
//            if (StringUtils.isBlank(template.getCondition())) {
//                failMsg.add("Condition 为空！");
//            }
            if (StringUtils.isBlank(template.getBrand())) {
                failMsg.add("品牌为空！");
            }
            if (StringUtils.isBlank(template.getMainImage())) {
                failMsg.add("主图为空！");
            }
            if (StringUtils.isBlank(template.getExtraImages()) || "[]".equals(template.getExtraImages())) {
                failMsg.add("附图为空！");
            }
//            if(template.getQuantity() == null){
//                failMsg.add("数量为空！");
//            }
//            if(template.getStandardPrice() == null){
//                failMsg.add("价格为空！");
//            }
        }

        if (accountRelation != null) {
            if (StringUtils.isBlank(accountRelation.getLogisticsCode())) {
                failMsg.add("物流方式为空，无法算价！");
            }
            if (accountRelation.getProfitMargin() == null) {
                failMsg.add("利润率为空，无法算价");
            } else if (accountRelation.getProfitMargin() <= 0 || accountRelation.getProfitMargin() >= 1) {
                failMsg.add("利润率不在区间(0,1)中，不能算价");
            }
        }

        if (!failMsg.isEmpty()) {
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }

        return ApiResult.newSuccess();
    }

    /**
     * 自动刊登模板信息补充
     * 图片、sku主图
     * 标题格式
     * 品牌格式
     *
     * @param account  账号
     * @param template 模板
     */
    public static void setTemplateInfo(AmazonAccount account, AmazonTemplateBO template) {

        // 设置EAN
//        setTemplateEAN(account, template);

        // 设置图片
        setTemplateImage(template);

        // 标题 描述长度截取
        templateLengthSplit(template);

        // ES-6016 模板标题描述的品牌、制造商与店铺配置保持一致
        alignTemplateBrandAndManufacturer(template);
    }


    /**
     * 设置模板图片， 取文件系统图片
     *
     * @param template 模板
     */
    public static void setTemplateImage(AmazonTemplateBO template) {
        if (SkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(template.getSkuDataSource())) {
            return;
        }
        // 图片
        List<String> images = amazonTemplateService.getProductImages(template.getParentSku());
        if (CollectionUtils.isEmpty(images)) {
            template.setExtraImages("[]");
        }

        // 获取产品最早的录入时间
        String productFirstCreateDate = AmazonTemplateUtils.getProductFirstCreateDate(template.getParentSku(), template.getSkuDataSource());
        if (StringUtils.isBlank(productFirstCreateDate)) {
            productFirstCreateDate = LocalDateTime.now().toString();
        }

        // EAN
        // 根据SKU图片进行取值，-cmd和-effect，优先取SKU-cmd，SKU-effect，若无，则取SPU-cmd，SPU-effect，排除其他子SKU的图片
        if (!template.getSaleVariant()) {
            // 单体 设置图片
            String mainImage = getSkuMainImage(template.getParentSku(), images, productFirstCreateDate);
            if (StringUtils.isNotBlank(mainImage)) {
                template.setMainImage(mainImage);
                template.setSampleImage(template.getMainImage());
            }
            // 按照规则找附图
            List<String> referImages = getSkuReferImages(template.getParentSku(), null, images);
            template.setExtraImages(JSON.toJSONString(referImages));
            return;
        }

        List<AmazonSku> amazonSkus = template.getAmazonSkus();
        if (!template.getSaleVariant() || CollectionUtils.isEmpty(amazonSkus)) {
            return;
        }
        for (AmazonSku sku : amazonSkus) {
            String articleNumber = sku.getSku();
            sku.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
            //主图匹配规则：sku名称相同
            String mainImage = getSkuMainImage(articleNumber, images, productFirstCreateDate);
            if (StringUtils.isNotEmpty(mainImage)) {
                sku.setMainImage(mainImage);
                sku.setSampleImage(mainImage);
            }

            // 按照规则找附图
            List<String> skuReferImages = getSkuReferImages(template.getParentSku(), articleNumber, images);
            sku.setExtraImages(JSON.toJSONString(skuReferImages));
            sku.setExtraImagesList(skuReferImages);
        }
        template.updateVariations(amazonSkus);
    }

    /**
     * 获取sku同名规则图片,需要随机
     *
     * @param articleNumber articleNumber
     * @param images        产品图片池
     * @param productFirstCreateDate 产品最早创建时间
     * @return 目标图片
     */
    public static String getSkuMainImage(String articleNumber, List<String> images, String productFirstCreateDate) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }
        if (StringUtils.isBlank(productFirstCreateDate)) {
            productFirstCreateDate = LocalDateTime.now().toString();
        }

        LocalDateTime productCreateDate = LocalDateTime.parse(productFirstCreateDate);
        // 2024年7月1日之前的主图只取SKU命名相同的图片
        if (productCreateDate.isBefore(LocalDateTime.of(2024, 7, 1, 0, 0, 0))) {
            return images.stream()
                    .filter(image -> image.contains(String.format("/%s.", articleNumber)))
                    .findFirst().orElse(null);
        } else {
            // 随机取SKU命名相同的图，子SKU命名 -00 ，-000命名的图片
            List<String> mainImageList = images.stream()
                    .filter(image -> image.contains(String.format("/%s.", articleNumber))
                            || image.contains(String.format("/%s.", articleNumber + "-00"))
                            || image.contains(String.format("/%s.", articleNumber + "-000"))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mainImageList)) {
                return null;
            }
            Collections.shuffle(mainImageList);
            return mainImageList.get(0);
        }
    }

    /**
     * 根据SKU图片进行取值，-cmd和-effect，优先取SKU-cmd，SKU-effect，若无，则取SPU-cmd，SPU-effect，排除其他子SKU的图片
     *
     * @param spu       SPU编号，用于匹配图片
     * @param sku       SKU编号，用于匹配图片
     * @param images    产品图片池，包含所有可选图片的列表
     * @return 附图集合
     */
    private static List<String> getSkuReferImages(String spu, String sku, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }

        // 获取当前spu下的sku列表信息
        List<String> skuList = singleItemEsService.getSonSkuListByMainSku(Collections.singletonList(spu));

        // 包含sku集合则为变体，否则为单体
        if (CollectionUtils.isNotEmpty(skuList) && ObjectUtils.isNotEmpty(sku)) {
            // 需要通过其它sku过滤掉不属于他们的图片，然后随机取这里的图片
            List<String> newImages = images.stream()
                    .filter(image -> image.contains(sku) || skuList.stream().noneMatch(skuStr -> image.contains(skuStr)))
                    .collect(Collectors.toList());

            // 图片集合
            List<String> referImageList = new ArrayList<>(8);
            // 获取主图列表图片和spu附图列表图片
            if (StringUtils.isNotBlank(spu)) {
                // 优先取SKU-cmd，SKU-effect
                referImageList.addAll(newImages.stream()
                        .filter(image -> image.contains(sku + "-cmd") || image.contains(sku + "-effect"))
                        .collect(Collectors.toList()));
                // 取SPU-cmd，SPU-effect
                if (referImageList.isEmpty()) {
                    referImageList.addAll(images.stream()
                            .filter(image -> image.contains(String.format("/%s.", spu + "-cmd")) || image.contains(String.format("/%s.", spu + "-effect")))
                            .collect(Collectors.toList()));
                }
            }

            // 过滤已经选择的图片然后随机取newImages中的图片
            Collections.shuffle(newImages, new Random());

            List<String> randomImages = newImages.stream()
                    .filter(image -> !referImageList.contains(image) && !image.contains("-cmd") && !image.contains("-effect"))
                    .collect(Collectors.toList());
            referImageList.addAll(randomImages);

            // 若超过8张图片，则截取前8张
            if (referImageList.size() > 8) {
                return referImageList.subList(0, 8);
            }
            return referImageList;
        }
        // 单体照片
        List<String> singleImageList = images.stream()
                .filter(image -> image.contains(spu + "-cmd") || image.contains(spu + "-effect"))
                .collect(Collectors.toList());

        // 打乱图片顺序
        Collections.shuffle(images, new Random());
        List<String> filterSingleImages = images.stream()
                .filter(image -> !singleImageList.contains(image) && !image.contains("-cmd") && !image.contains("-effect"))
                .collect(Collectors.toList());

        // 选择前八个图片
        List<String> singleImages = filterSingleImages.subList(0, Math.min(8 - singleImageList.size(), filterSingleImages.size()));
        singleImageList.addAll(singleImages);
        return singleImageList;

    }

    /**
     * 根据SPU查询SKU图片列表
     * @param spu
     * @param sku
     * @param skuList
     * @param images
     * @return
     */
    public static List<String> getSpuReferImages(String spu, String sku, List<String> skuList, Collection<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return null;
        }

        // 图片集合
        List<String> referImageList = new ArrayList<>(8);
        // 需要通过其它sku过滤掉不属于他们的图片，然后随机取这里的图片
        List<String> newImages = images.stream()
                .filter(image -> image.contains(sku) || skuList.stream().noneMatch(skuStr -> image.contains(skuStr)))
                .collect(Collectors.toList());


        // 获取主图列表图片和spu附图列表图片
        if (StringUtils.isNotBlank(spu)) {
            // 优先取SKU-cmd，SKU-effect
            referImageList.addAll(newImages.stream()
                    .filter(image -> image.contains(sku + "-cmd") || image.contains(sku + "-effect"))
                    .collect(Collectors.toList()));
            // 取SPU-cmd，SPU-effect
            if (referImageList.isEmpty()) {
                referImageList.addAll(images.stream()
                        .filter(image -> image.contains(String.format("/%s.", spu + "-cmd")) || image.contains(String.format("/%s.", spu + "-effect")))
                        .collect(Collectors.toList()));
            }
        }

        // 过滤已经选择的图片然后随机取newImages中的图片
        Collections.shuffle(newImages, new Random());

        List<String> randomImages = newImages.stream()
                .filter(image -> !referImageList.contains(image) && !image.contains("-cmd") && !image.contains("-effect"))
                .collect(Collectors.toList());
        referImageList.addAll(randomImages);

        // 若超过8张图片，则截取前8张
        if (referImageList.size() > 8) {
            return referImageList.subList(0, 8);
        }
        return referImageList;
    }

    public static ApiResult<AmazonTemplateBO> googleTranslateBo(AmazonTemplateBO bean) {
        String srcLang = GoogleTranslateUtils.changeDestLang("us");
        String destLang = GoogleTranslateUtils.changeDestLang(bean.getCountry());
        if (StringUtils.equals(srcLang, destLang)) {
            return ApiResult.newSuccess();
        }

        try {

            ArrayList<String> list = new ArrayList<>();

            //标题
            if (StringUtils.isNotBlank(bean.getTitle())) {
                list.add(bean.getTitle());
            } else {
                list.add("");
            }

            //描述
            String description = bean.getDescription();
            if (StringUtils.isNotBlank(description)) {
                list.add(description);
            } else {
                list.add("");
            }

            //5点描述
            String bulletPoint = bean.getBulletPoint();
            List<String> bpList = null;
            if (StringUtils.isNotBlank(bulletPoint)) {
                bpList = JSON.parseArray(bulletPoint, String.class);
            }
            if (CollectionUtils.isEmpty(bpList)) {
                list.add("");
                list.add("");
                list.add("");
                list.add("");
                list.add("");
            } else {
                bpList = bpList.size() > 5 ? bpList.subList(0, 5) : bpList;
                for (String str : bpList) {
                    list.add(str);
                }
                if (bpList.size() < 5) {
                    for (int i = 0; i < (5 - bpList.size()); i++) {
                        list.add("");
                    }
                }
            }

            //关键词
            String searchTerms = bean.getSearchTerms();
            List<String> stList = null;
            if (StringUtils.isNotBlank(searchTerms)) {
                if (BooleanUtils.isTrue(bean.getSaleVariant())) {
                    //变体
                    Map<String, List<String>> map = JSON.parseObject(searchTerms, new TypeReference<HashMap<String, List<String>>>() {
                    });
                    stList = new ArrayList<>(map.size());
                    for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                        if (CollectionUtils.isNotEmpty(entry.getValue())) {
                            stList.add(entry.getValue().get(0));
                        } else {
                            stList.add("");
                        }
                    }
                } else {
                    //单体
                    stList = JSON.parseArray(searchTerms, String.class);
                }
            }
            if (CollectionUtils.isNotEmpty(stList)) {
                for (String str : stList) {
                    list.add(str);
                }
            }

            //翻译
            int max = 1300, min = 500;
            int ran2 = (int) (Math.random() * (max - min) + min);
            Thread.sleep(ran2);
            String var = AmazonTranslateUtils.translate(srcLang, destLang, list, 3);

            if (StringUtils.isBlank(var)) {
                return ApiResult.newError("翻译失败，返回为null!");
            }

            List<String> transList = JSON.parseArray(var, String.class);
            if (transList.size() != list.size()) {
                return ApiResult.newError("翻译结果数量不一致!");
            }

            //标题
            if (StringUtils.isNotBlank(bean.getTitle())) {
                String title = transList.get(0);
                title = title.length() == 1 ? title.toUpperCase() : title.substring(0, 1).toUpperCase() + title.substring(1);
                bean.setTitle(title);
            }

            //描述
            if (StringUtils.isNotBlank(bean.getDescription())) {
                bean.setDescription(transList.get(1));
            }

            //5点描述
            if (StringUtils.isNotBlank(bean.getBulletPoint())) {
                bean.setBulletPoint(JSON.toJSONString(transList.subList(2, 7)));
            }

            //关键词
            if (StringUtils.isNotBlank(bean.getSearchTerms())) {
                if (BooleanUtils.isTrue(bean.getSaleVariant())) {
                    //变体
                    Map<String, List<String>> map = JSON.parseObject(searchTerms, new TypeReference<HashMap<String, List<String>>>() {
                    });
                    Map<String, List<String>> resultMap = new HashMap<>(map.size());
                    int index = 7;
                    for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                        resultMap.put(entry.getKey(), Arrays.asList(transList.get(index)));

                        index++;
                    }
                    bean.setSearchTerms(JSON.toJSONString(resultMap));
                } else {
                    //单体
                    bean.setSearchTerms(JSON.toJSONString(Arrays.asList(transList.get(7))));
                }
            }

            //标题 描述长度截取
            templateLengthSplit(bean);

            // 日本站点需要翻译变体属性值
            if (CountryEnum.JP.getSite().equalsIgnoreCase(bean.getCountry()) && bean.getSaleVariant()) {
                List<AmazonSku> amazonSkuList = JSON.parseArray(bean.getVariations(), AmazonSku.class);
                for (AmazonSku amazonSku : amazonSkuList) {
                    List<NameValue> nameValues = amazonSku.getNameValues();
                    if (CollectionUtils.isEmpty(nameValues)) {
                        continue;
                    }

                    for (NameValue nameValue : nameValues) {
                        String value = nameValue.getValue();
                        if (StringUtils.isBlank(value)) {
                            continue;
                        }
                        String translateValue = AmazonTranslateUtils.translate(srcLang, destLang, value, 3);
                        nameValue.setValue(translateValue);
                    }
                    amazonSku.setNameValues(nameValues);
                }
                bean.setAmazonSkus(amazonSkuList);
                bean.setVariations(JSON.toJSONString(amazonSkuList));
            }
        } catch (Exception e) {
            log.error("翻译异常：", e);
            return ApiResult.newError("翻译异常：" + e.getMessage());
        } finally {
//            AmazonLimiterUtil.GOOGLE_TRANSLATE_LIMITER.release();
        }

        return ApiResult.newSuccess();
    }

    /**
     * 模板字段长度截取
     *
     * @param template
     */
    public static void templateLengthSplit(AmazonTemplateBO template) {

        try {
            KeyWordUtils.checkAndUpdateKeyWord(template);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        //标题
        if (StringUtils.isNotEmpty(template.getTitle())) {
            String title = template.getTitle().replaceAll(",", " ").replaceAll("，", " ").replace("\n", " ").trim();
            template.setTitle(StrUtil.changTitleLimit(title, 200));
        }

        //描述
        if (StringUtils.isNotEmpty(template.getDescription())) {
            // 按字节截取
            String descStr = template.getDescription().replaceAll("\n", "<br/>");
            String newDesc = KeyWordUtils.truncateText(descStr, 2000);
            template.setDescription(newDesc.replaceAll("<br/>", "\n"));
        }

        //五点描述
        if (StringUtils.isNotEmpty(template.getBulletPoint())) {
            List<String> bulletPoints = JSONArray.parseArray(template.getBulletPoint(), String.class);
            List<String> newBullpoints = new ArrayList<>(bulletPoints.size());

            for (String bulletPoint : bulletPoints) {
                bulletPoint = bulletPoint.replaceAll("\n", "<br/>");
                String newBulletPoint = KeyWordUtils.truncateText(bulletPoint, 500);
                newBullpoints.add(newBulletPoint.replaceAll("<br/>", "\n"));
            }
            template.setBulletPoint(JSON.toJSONString(newBullpoints));
        }
    }

    /**
     * 获取对应时间产品系统编辑完成的产品
     *
     * @param stringDateBegin
     * @param stringDateEnd
     * @return
     */
    public static Map<String, SkuListAndCode> getSpuToCodeMapForNewProduct(String stringDateBegin, String stringDateEnd) {
        Map<String, SkuListAndCode> spuToCodeMap = new HashMap<>(0);

        ProductNewSpuRequest request = new ProductNewSpuRequest();
        request.setBeginTime(stringDateBegin);
        request.setEndTime(stringDateEnd);
        ResponseJson rsp = ProductUtils.getNewProduct(request);
        if (!rsp.isSuccess()) {
            XxlJobLogger.log(String.format("获取开始时间：%s，结束时间：%s 新品spu失败:", stringDateBegin, stringDateEnd) + rsp.getMessage());
            return spuToCodeMap;
        }

        spuToCodeMap = (Map<String, SkuListAndCode>) rsp.getBody().get(ProductUtils.resultKey);
        if (MapUtils.isEmpty(spuToCodeMap)) {
            XxlJobLogger.log(stringDateBegin + "----" + stringDateEnd + "没有已编辑的spu");
            XxlJobLogger.log(JSON.toJSONString(rsp));
        }

        // 查询输入的产品信息
        Set<String> spus = spuToCodeMap.keySet();

        // 查询ES
        SingleItemEsRequest criteria = new SingleItemEsRequest();
        criteria.setSkuList(new ArrayList<>(spus));
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        List<SingleItemEs> singleItemEsList = new ArrayList<>();
        try {
            singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
        } catch (Exception e) {
            XxlJobLogger.log("调用产品ES报错：" + e.getMessage());
        }

        spuToCodeMap = SingleItemEsUtils.getProductInfo(spuToCodeMap, singleItemEsList);

        return spuToCodeMap;
    }


    /**
     * 获取对应时间产品系统编辑完成的产品
     *
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> getSpuToCodeMapForNewProduct(Map<String, SkuListAndCode> spuToCodeMap) {
        Map<String, SkuListAndCode> spuToCodeNewMap = new HashMap<>(0);
        // 查询输入的产品信息
        Set<String> spus = spuToCodeMap.keySet();

        // 查询ES
        SingleItemEsRequest criteria = new SingleItemEsRequest();
        criteria.setSkuList(new ArrayList<>(spus));
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        List<SingleItemEs> singleItemEsList = new ArrayList<>();
        try {
            singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
        } catch (Exception e) {
            XxlJobLogger.log("调用产品ES报错：" + e.getMessage());
        }

        spuToCodeMap = SingleItemEsUtils.getProductInfo(spuToCodeMap, singleItemEsList);
        spuToCodeNewMap.putAll(spuToCodeMap);
        return spuToCodeNewMap;
    }

    /**
     * 获取数据库待刊登产品
     *
     * @return
     */
    public static Map<String, SkuListAndCode> getSpuToCodeMapForDbTable() {
        Map<String, SkuListAndCode> allSpuToCodeMap = new HashMap<>();

        int offset = 0;
        int limit = 500;
        while (true) {
            List<String> skus = pmsSkuService.selectTemporarySku(limit, offset);
            if (CollectionUtils.isEmpty(skus)) {
                break;
            }

            ResponseJson responseJson = ProductUtils.findSkuInfos(skus);
            if (!responseJson.isSuccess()) {
                XxlJobLogger.log("查询产品异常：" + responseJson.getMessage());
                continue;
            }

            List<ProductInfo> productInfos = (List<ProductInfo>) responseJson.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isEmpty(productInfos)) {
                continue;
            }

            Map<String, SkuListAndCode> spuToCodeMap = SingleItemEsUtils.getSpuToCodeMap(productInfos);
            if (MapUtils.isNotEmpty(spuToCodeMap)) {
                allSpuToCodeMap.putAll(spuToCodeMap);
            }

            offset += limit;
        }

        return allSpuToCodeMap;
    }

    /**
     * 模板标题描述五点描述关键词中的品牌、制造商与店铺配置保持一致
     */
    public static void alignTemplateBrandAndManufacturer(AmazonTemplateWithBLOBs template) {
        String brand = template.getBrand();
        String manufacturer = template.getManufacturer();

        if (StringUtils.isNotBlank(brand)) {
            String replacedDesc = replaceWordsIgnoreCase(template.getDescription(), brand);
            String replacedSearchData = replaceWordsIgnoreCase(template.getSearchData(), brand);
            String replacedSearchTerms = replaceWordsIgnoreCase(template.getSearchTerms(), brand);
            String replacedBulletPoint = replaceWordsIgnoreCase(template.getBulletPoint(), brand);


            template.setSearchData(replacedSearchData);
            template.setSearchTerms(replacedSearchTerms);
            template.setBulletPoint(replacedBulletPoint);
            template.setDescription(replacedDesc);
        }

        if (StringUtils.isNotBlank(manufacturer)) {
            String replacedDesc = replaceWordsIgnoreCase(template.getDescription(), manufacturer);
            String replacedSearchData = replaceWordsIgnoreCase(template.getSearchData(), manufacturer);
            String replacedSearchTerms = replaceWordsIgnoreCase(template.getSearchTerms(), manufacturer);
            String replacedBulletPoint = replaceWordsIgnoreCase(template.getBulletPoint(), manufacturer);


            template.setSearchData(replacedSearchData);
            template.setSearchTerms(replacedSearchTerms);
            template.setBulletPoint(replacedBulletPoint);
            template.setDescription(replacedDesc);
        }
        // 标题会在setTitle#AmazonTemplate.processTitle 中处理
        template.setTitle(template.getTitle());
    }

    public static String replaceWordsIgnoreCase(String text, String wordToReplace) {
        try {
            if (StringUtils.isBlank(text)) {
                return text;
            }
            String escapedWord = Pattern.quote(wordToReplace);
            Pattern regex = Pattern.compile(escapedWord, Pattern.CASE_INSENSITIVE);
            Matcher matcher = regex.matcher(text);
            return matcher.replaceAll(wordToReplace);
        } catch (Exception e) {
            log.error("替换品牌或制造商失败：text:{}, wordToReplace:{}, error:{}", text, wordToReplace, e.getMessage(), e);
        }
        return text;
    }

}
