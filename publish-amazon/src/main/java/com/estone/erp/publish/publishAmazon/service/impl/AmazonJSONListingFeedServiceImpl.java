package com.estone.erp.publish.publishAmazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.enums.SystemFeedTypeEnum;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.AmazonDelInfringementWordDO;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.publishAmazon.service.AmazonJSONListingFeedService;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Amazon JSON Listing Feed Service Implementation
 *
 * <AUTHOR>
 * @date 2025-04-02 10:05
 */
@Slf4j
@Service
public class AmazonJSONListingFeedServiceImpl implements AmazonJSONListingFeedService {
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonPublishImagePathService amazonPublishImagePathService;
    @Resource
    private AmazonInfringementWordHelper amazonInfringementWordHelper;

    @Override
    public ApiResult<String> batchUpdatePriceAndInventory(AmazonProductListingDto requestParam) {
        List<String> feedTypeList = requestParam.getFeedTypeList();
        List<AmazonProductListing> amazonProductListingList = requestParam.getAmazonProductListingList();

        for (String feedType : feedTypeList) {
            if (SystemFeedTypeEnum.LISTING_INVENTORY_FEED_DOCUMENT.getSystemFeedType().equals(feedType)) {
                batchUpdateInventory(amazonProductListingList);
            }
            if (SystemFeedTypeEnum.LISTING_PRICE_FEED_DOCUMENT.getSystemFeedType().equals(feedType)) {
                batchUpdatePrice(amazonProductListingList);
            }
        }
        return ApiResult.newSuccess("批量更新成功");
    }

    /**
     * 更新图片和发布图片
     */
    @Override
    public ApiResult<String> updateImageAndPublishImage(AmazonProductListing updateListing) {
        updateListing.setUpdatedBy(WebUtils.getUserName());
        AmazonProductListingExample productListingExample = new AmazonProductListingExample();
        AmazonProductListingExample.Criteria productListingExampleCriteria = productListingExample.createCriteria();
        productListingExampleCriteria.andSellerSkuEqualTo(updateListing.getSellerSku());
        productListingExampleCriteria.andAccountNumberEqualTo(updateListing.getAccountNumber());
        amazonProductListingService.updateByExampleSelective(updateListing, productListingExample);
        AmazonExecutors.downloadAmazonImage(() -> {
            updateListingImages(updateListing);
        });
        return ApiResult.newSuccess("更新图片成功");
    }

    @Override
    public ApiResult<String> batchProductListingImage(List<AmazonProductListing> amazonProductListingList) {
        // 过滤主图、附图、样品图全为空的变体
        Map<String, List<AmazonProductListing>> accountNumberListings = amazonProductListingList.stream().collect(Collectors.groupingBy(AmazonProductListing::getAccountNumber));
        String columns = "accountNumber,sellerSku,articleNumber,mainImage,sampleImage,extraImages";
        accountNumberListings.forEach((accountNumber, listings) -> {
            AmazonProductListingExample example = new AmazonProductListingExample();
            List<String> sellerSKuList = listings.stream().map(AmazonProductListing::getSellerSku).collect(Collectors.toList());
            AmazonProductListingExample.Criteria criteria = example.createCriteria();
            criteria.andAccountNumberEqualTo(accountNumber);
            criteria.andSellerSkuIn(sellerSKuList);
            criteria.andIsOnlineEqualTo(true);
            String site = listings.get(0).getSite();
            example.setColumns(columns);
            List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example, site);
            if (CollectionUtils.isEmpty(amazonProductListings)) {
                return;
            }

            List<AmazonProductListing> updateImagesListing = amazonProductListings.stream()
                    .filter(listing -> StringUtils.isNotBlank(listing.getMainImage()))
                    .filter(listing -> StringUtils.isNotBlank(listing.getExtraImages()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(updateImagesListing)) {
                return;
            }
            updateImagesListing.forEach(item -> {
                AmazonExecutors.downloadAmazonImage(() -> {
                    updateListingImages(item);
                });
            });
        });
        return ApiResult.newSuccess("批量更新图片成功");
    }

    @Override
    public ApiResult<String> batchProductListingTitle(List<AmazonProductListing> amazonProductListingList) {
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            // 处理报告
            AmazonProcessReport updateTitleReport = createBaseProcessReport(amazonProductListing, SystemFeedTypeEnum.LISTING_PRODUCT_TITLE_FEED_DOCUMENT.getSystemFeedType(), (report, listing) -> {
            });
            AmazonDelInfringementWordDO infringementWordDO = new AmazonDelInfringementWordDO();
            infringementWordDO.setAccountNumber(amazonProductListing.getAccountNumber());
            infringementWordDO.setSite(amazonProductListing.getSite());
            infringementWordDO.setSonSku(amazonProductListing.getArticleNumber());
            infringementWordDO.setSourceTexts(List.of(amazonProductListing.getItemName()));
            ApiResult<String> apiResult = amazonInfringementWordHelper.delTextInfringementWords(infringementWordDO);
            if (!apiResult.isSuccess()) {
                updateTitleReport.setStatus(false);
                updateTitleReport.setFinishDate(new Date());
                updateTitleReport.setResultMsg(apiResult.getErrorMsg());
                updateTitleReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
                amazonProcessReportService.insert(updateTitleReport);
                continue;
            }

            String itemName = infringementWordDO.getSourceTexts().get(0);
            amazonProductListing.setItemName(itemName);
            // 更新标题
            ApiResult<String> listingFeedTitleResult = AmazonSpLocalServiceUtils.updateJsonListingFeedTitle(amazonProductListing);
            if (!listingFeedTitleResult.isSuccess()) {
                updateTitleReport.setStatus(false);
                updateTitleReport.setFinishDate(new Date());
                updateTitleReport.setResultMsg(listingFeedTitleResult.getErrorMsg());
                updateTitleReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
                amazonProcessReportService.insert(updateTitleReport);
                continue;
            }
            updateTitleReport.setTaskId(listingFeedTitleResult.getResult());
            amazonProcessReportService.insert(updateTitleReport);
        }
        return ApiResult.newSuccess("批量更新标题成功,请稍后查询处理报告");
    }

    @Override
    public ApiResult<String> batchListingFulfillmentLatency(AmazonProductListingDto requestParam) {
        Integer fulfillmentLatency = requestParam.getFulfillmentLatency();
        List<AmazonProductListing> amazonProductListingList = requestParam.getAmazonProductListingList();
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("listing不能为空");
        }
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            AmazonProcessReport processReport = createBaseProcessReport(amazonProductListing, SystemFeedTypeEnum.LISTING_HANDING_TIME_FEED_DOCUMENT.getSystemFeedType(), (report, listing) -> {
                report.setPreviousFulfillmentLatencyValue(String.valueOf(listing.getOldFulfillmentLatencyValue()));
                report.setAfterFulfillmentLatencyValue(String.valueOf(fulfillmentLatency));
                report.setPreviousQuantityValue(Optional.ofNullable(listing.getOldQuantityValue()).orElse(""));
                report.setAfterQuantityValue(String.valueOf(listing.getQuantity()));
            });
            amazonProductListing.setFulfillmentLatency(fulfillmentLatency);
            if (processReport.getAfterFulfillmentLatencyValue().equals(processReport.getPreviousFulfillmentLatencyValue())) {
                processReport.setStatus(true);
                processReport.setResultMsg("无需更新");
                processReport.setFinishDate(new Date());
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
                amazonProcessReportService.insert(processReport);
                continue;
            }

            ApiResult<String> apiResult = AmazonSpLocalServiceUtils.updateJsonListingFeedQuantity(amazonProductListing);
            if (!apiResult.isSuccess()) {
                processReport.setStatus(false);
                processReport.setFinishDate(new Date());
                processReport.setResultMsg(JSON.toJSONString(apiResult));
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
                amazonProcessReportService.insert(processReport);
                continue;
            }
            processReport.setTaskId(apiResult.getResult());
            amazonProcessReportService.insert(processReport);
        }

        return ApiResult.newSuccess("批量更新备货期成功,请稍后查询处理报告");
    }

    /**
     * 批量更新价格
     *
     * @param amazonProductListingList listing
     * @return ApiResult
     */
    @Override
    public ApiResult<String> batchUpdatePrice(List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("listing不能为空");
        }
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            if (StringUtils.isBlank(amazonProductListing.getOldPriceValue())) {
                AmazonProductListing localData = amazonProductListingService.selectPriceInventoryListingData(amazonProductListing.getAccountNumber(), amazonProductListing.getSellerSku());
                amazonProductListing.setOldPriceValue(String.valueOf(localData.getPrice()));
            }
            AmazonProcessReport processReport = createBaseProcessReport(amazonProductListing, SystemFeedTypeEnum.LISTING_PRICE_FEED_DOCUMENT.getSystemFeedType(), (report, listing) -> {
                report.setPreviousPriceValue(listing.getOldPriceValue());
                report.setAfterPriceValue(String.valueOf(listing.getPrice()));
            });
            if (processReport.getAfterPriceValue().equals(processReport.getPreviousPriceValue())) {
                processReport.setStatus(false);
                processReport.setResultMsg("改前后价格相同,无需更新");
                processReport.setFinishDate(new Date());
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
                amazonProcessReportService.insert(processReport);
                continue;
            }
            ApiResult<String> apiResult = AmazonSpLocalServiceUtils.updateJsonListingFeedPrice(amazonProductListing);
            if (apiResult.isSuccess()) {
                processReport.setTaskId(apiResult.getResult());
            } else {
                processReport.setStatus(false);
                processReport.setFinishDate(new Date());
                processReport.setResultMsg(JSON.toJSONString(apiResult));
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
            }
            amazonProcessReportService.insert(processReport);
        }
        return ApiResult.newSuccess("批量更新价格成功");
    }

    /**
     * 批量更新库存
     *
     * @param amazonProductListingList listing
     * @return ApiResult
     */
    @Override
    public ApiResult<String> batchUpdateInventory(List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("listing不能为空");
        }
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            if (StringUtils.isBlank(amazonProductListing.getOldQuantityValue())) {
                AmazonProductListing localData = amazonProductListingService.selectPriceInventoryListingData(amazonProductListing.getAccountNumber(), amazonProductListing.getSellerSku());
                amazonProductListing.setOldQuantityValue(String.valueOf(localData.getQuantity()));
            }

            AmazonProcessReport processReport = createBaseProcessReport(amazonProductListing, SystemFeedTypeEnum.LISTING_INVENTORY_FEED_DOCUMENT.getSystemFeedType(), (report, listing) -> {
                report.setPreviousQuantityValue(listing.getOldQuantityValue());
                report.setAfterQuantityValue(String.valueOf(listing.getQuantity()));
            });
            if (processReport.getAfterQuantityValue().equals(processReport.getPreviousQuantityValue())) {
                processReport.setStatus(false);
                processReport.setResultMsg("改前后库存相同,无需更新");
                amazonProcessReportService.insert(processReport);
                continue;
            }
            amazonProductListing.setFulfillmentLatency(null);
            ApiResult<String> apiResult = AmazonSpLocalServiceUtils.updateJsonListingFeedQuantity(amazonProductListing);
            if (apiResult.isSuccess()) {
                processReport.setTaskId(apiResult.getResult());
            } else {
                processReport.setStatus(false);
                processReport.setFinishDate(new Date());
                processReport.setResultMsg(JSON.toJSONString(apiResult));
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
            }
            amazonProcessReportService.insert(processReport);
        }
        return ApiResult.newSuccess("批量更新库存成功,请稍后查询处理报告");
    }

    private AmazonProcessReport createBaseProcessReport(AmazonProductListing listing, String relationType, BiConsumer<AmazonProcessReport, AmazonProductListing> func) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.JSON_LISTINGS_FEED.getValue());
        report.setAccountNumber(listing.getAccountNumber());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(listing.getSellerSku());
        report.setRelationType(relationType);
        report.setStatusCode(ProcessingReportStatusCode.Processing.name());
        report.setCreationDate(new Date());
        report.setCreatedBy(Optional.ofNullable(WebUtils.getUserName())
                .orElseGet(() -> Optional.ofNullable(DataContextHolder.getUsername())
                        .orElse("admin")));
        if (func != null) {
            func.accept(report, listing);
        }
        return report;
    }

    private List<String> getListingImages(AmazonProductListing listing) {
        List<String> images = new ArrayList<>();
        Optional.ofNullable(listing.getMainImage())
                .ifPresent(images::add);
        Optional.ofNullable(listing.getSampleImage())
                .ifPresent(images::add);
        Optional.ofNullable(listing.getExtraImages())
                .map(extra -> JSON.parseArray(extra, String.class))
                .ifPresent(images::addAll);
        return images;
    }

    @Override
    public void updateListingImages(AmazonProductListing amazonProductListing) {
        AmazonProcessReport processReport = createBaseProcessReport(amazonProductListing, SystemFeedTypeEnum.LISTING_IMAGE_FEED_DOCUMENT.getSystemFeedType(), null);
        amazonProcessReportService.insert(processReport);
        try {
            // 图片映射
            String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(amazonProductListing.getAccountNumber());
            List<String> listingImages = getListingImages(amazonProductListing);
            Map<String, String> imageMapping = AmazonUtils.copyImagesToAliOSS(listingImages, imagePath);
            // 更新图片
            ApiResult<String> apiResult = AmazonSpLocalServiceUtils.updateJsonListingFeedImages(amazonProductListing, imageMapping);
            if (apiResult.isSuccess()) {
                processReport.setTaskId(apiResult.getResult());
            } else {
                processReport.setStatus(false);
                processReport.setFinishDate(new Date());
                processReport.setResultMsg(JSON.toJSONString(apiResult));
                processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            processReport.setStatus(false);
            processReport.setResultMsg(e.getMessage());
        }
        amazonProcessReportService.update(processReport);
    }

}
