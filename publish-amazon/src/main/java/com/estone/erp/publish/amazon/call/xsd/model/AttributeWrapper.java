package com.estone.erp.publish.amazon.call.xsd.model;

import java.util.List;

public class AttributeWrapper {
    private String name;

    private List<String> values;

    private Boolean required = false;

    private TypeWrapper type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public TypeWrapper getType() {
        return type;
    }

    public void setType(TypeWrapper type) {
        this.type = type;
    }
}
