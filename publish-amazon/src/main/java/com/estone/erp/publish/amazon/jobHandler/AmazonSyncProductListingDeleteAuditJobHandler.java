package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit;
import com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditExample;
import com.estone.erp.publish.amazon.service.AmazonDeleteProductListingAuditService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步删除审核列表的信息
 */
@Component
public class AmazonSyncProductListingDeleteAuditJobHandler extends AbstractJobHandler {

    @Resource
    private AmazonDeleteProductListingAuditService amazonDeleteProductListingAuditService;

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    public AmazonSyncProductListingDeleteAuditJobHandler() {
        super("amazonSyncProductListingDeleteAuditJobHandler");
    }

    @XxlJob("amazonSyncProductListingDeleteAuditJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("amazonSyncProductListingDeleteAuditJobHandler 开始同步listing数据信息到删除审核列表");
        int maxId = 0;
        int pageSize = 200;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        while (true) {
            try {
                AmazonDeleteProductListingAuditExample amazonDeleteProductListingAuditExample = new AmazonDeleteProductListingAuditExample();
                amazonDeleteProductListingAuditExample.setOffset(0);
                amazonDeleteProductListingAuditExample.setLimit(pageSize);
                amazonDeleteProductListingAuditExample.setOrderByClause(" id ASC");
                AmazonDeleteProductListingAuditExample.Criteria criteria = amazonDeleteProductListingAuditExample.createCriteria();
                criteria.andIdGreaterThan(maxId);
                List<AmazonDeleteProductListingAudit> list = amazonDeleteProductListingAuditService.selectByExample(amazonDeleteProductListingAuditExample);

                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                maxId = list.get(list.size() - 1).getId();
                List<String> idList = list.stream().map(a -> a.getAccountNumber() + "_" + a.getSellerSku()).collect(Collectors.toList());

                EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
                request.setIdList(idList);
                request.setFields(new String[]{ "id", "accountNumber", "mainSku", "articleNumber", "parentAsin", "price", "sonAsin", "sellerSku", "skuStatus",
                        "isOnline", "mainImage", "order_24H_count", "order_last_7d_count", "order_last_14d_count", "order_last_30d_count", "order_num_total",
                        "forbidChannel", "infringementObj", "normalSale", "infringementTypename"});
                List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(request);
                Map<String, EsAmazonProductListing> idAndListingMap = esAmazonProductListingList.stream().collect(Collectors.toMap(EsAmazonProductListing::getId, Function.identity(), (oldV, newV) -> newV));
                List<String> accountNumberList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getAccountNumber).distinct().collect(Collectors.toList());
                Map<String, SalesmanAccountDetail> salesmanAccountDetailMapByEs = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumberList, SaleChannel.CHANNEL_AMAZON);

                List<AmazonDeleteProductListingAudit> updateList = new ArrayList<>(idAndListingMap.size());
                for (AmazonDeleteProductListingAudit amazonDeleteProductListingAudit : list) {
                    EsAmazonProductListing esAmazonProductListing = idAndListingMap.get(amazonDeleteProductListingAudit.getAccountNumber() + "_" + amazonDeleteProductListingAudit.getSellerSku());
                    if (esAmazonProductListing != null) {
                        setValue(esAmazonProductListing, amazonDeleteProductListingAudit, salesmanAccountDetailMapByEs);
                        updateList.add(amazonDeleteProductListingAudit);
                    }
                }
                if (CollectionUtils.isNotEmpty(updateList)) {
                    amazonDeleteProductListingAuditService.batchUpdateByPrimaryKeySelective(updateList);
                }

                if (list.size() < pageSize) {
                    break;
                }
            } catch (Exception e) {
                XxlJobLogger.log("异常：{}", e);
            }
        }
        stopWatch.stop();
        XxlJobLogger.log("amazonSyncProductListingDeleteAuditJobHandler 结束同步listing数据信息到删除审核列表 耗时：{} ms", stopWatch.getLastTaskTimeMillis());
        return ReturnT.SUCCESS;
    }

    public void setValue(EsAmazonProductListing esAmazonProductListing, AmazonDeleteProductListingAudit amazonDeleteProductListingAudit, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap) {
        Date date = new Date();
        SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(esAmazonProductListing.getAccountNumber());
        amazonDeleteProductListingAudit.setSalesId(EsAccountUtils.getSaleId(salesmanAccountDetail));
        // 同步时间
        amazonDeleteProductListingAudit.setSyncDate(new Timestamp(date.getTime()));
        amazonDeleteProductListingAudit.setUpdateDate(new Timestamp(date.getTime()));
        amazonDeleteProductListingAudit.setUpdatedBy("admin");

        // 基础信息
        amazonDeleteProductListingAudit.setAccountNumber(esAmazonProductListing.getAccountNumber());
        amazonDeleteProductListingAudit.setMainSku(esAmazonProductListing.getMainSku());
        amazonDeleteProductListingAudit.setArticleNumber(esAmazonProductListing.getArticleNumber());
        amazonDeleteProductListingAudit.setParentAsin(esAmazonProductListing.getParentAsin());
        amazonDeleteProductListingAudit.setPrice(esAmazonProductListing.getPrice());
        amazonDeleteProductListingAudit.setSonAsin(esAmazonProductListing.getSonAsin());
        amazonDeleteProductListingAudit.setSellerSku(esAmazonProductListing.getSellerSku());
        amazonDeleteProductListingAudit.setSkuStatus(esAmazonProductListing.getSkuStatus());
        amazonDeleteProductListingAudit.setIsOnline(esAmazonProductListing.getIsOnline());
        amazonDeleteProductListingAudit.setMainImage(esAmazonProductListing.getMainImage());

        // 销量
        amazonDeleteProductListingAudit.setOrder24hCount(esAmazonProductListing.getOrder_24H_count());
        amazonDeleteProductListingAudit.setOrderLast7dCount(esAmazonProductListing.getOrder_last_7d_count());
        amazonDeleteProductListingAudit.setOrderLast14dCount(esAmazonProductListing.getOrder_last_14d_count());
        amazonDeleteProductListingAudit.setOrderLast30dCount(esAmazonProductListing.getOrder_last_30d_count());
        amazonDeleteProductListingAudit.setOrderNumTotal(esAmazonProductListing.getOrder_num_total());

        // 禁售
        amazonDeleteProductListingAudit.setForbidChannel(esAmazonProductListing.getForbidChannel());
        amazonDeleteProductListingAudit.setInfringementObj(esAmazonProductListing.getInfringementObj());
        amazonDeleteProductListingAudit.setProhibitionSite(esAmazonProductListing.getNormalSale());
        amazonDeleteProductListingAudit.setInfringementTypename(esAmazonProductListing.getInfringementTypename());
    }

}
