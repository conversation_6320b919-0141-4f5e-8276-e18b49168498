package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderPushPriceEnum {
    US_1(1, "美国站点1"),
    US_2(2, "美国站点2"),
    US_3(3, "美国站点3"),
    UK(4, "UK"),
    FR(5, "FR");

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }
}
