package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonHolidayUpdateRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: ${同步上传备货期结果到在线listing}
 */
@Slf4j
@Component
public class SyncPublishFulfillmentLatencyResultJobHandler extends AbstractJobHandler {
    private static final Integer PAGE_SIZE = 3000;
    private static final Integer BEFORE_MINUTES = 10;

    @Autowired
    private AmazonProcessReportService amazonProcessReportService ;
    @Autowired
    private AmazonProductListingService amazonProductListingService ;
    @Autowired
    private AmazonHolidayUpdateRecordService holidayUpdateRecordService;

    public SyncPublishFulfillmentLatencyResultJobHandler() {
        super("SyncPublishFulfillmentLatencyResultJobHandler");
    }

    @Override
    @XxlJob("SyncPublishFulfillmentLatencyResultJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        logger.warn("*****************亚马逊定时处理成功的备货期数据到Listing 开始*****************");
        long startTime = System.currentTimeMillis();
        Date time = new Date();
        Date greaterThanDate = null;
        Date dateBegin = com.estone.erp.publish.common.util.DateUtils.getDateBegin(0);
        Date dateEnd = DateUtils.addHours(dateBegin,2);
        Boolean dateFlag =DateUtils.betweenStartTimeAndEndTime(time,dateBegin,dateEnd);
       if (dateFlag) {
           // 在零点和两点之间就执行昨日一天的数据
           greaterThanDate = DateUtils.addHours(time, -24);
        }else {
           greaterThanDate = DateUtils.addHours(time, -6);
        }
        Date LessThanTime= DateUtils.addMinutes(time, -BEFORE_MINUTES);
        String feedType = SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue();
        String relationType = ProcessingReportTriggleType.Listing_Fulfillment_Latency.name();
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.createCriteria()
                .andCreationDateGreaterThanOrEqualTo(greaterThanDate)
                .andCreationDateLessThanOrEqualTo(LessThanTime)
                .andFeedTypeEqualTo(feedType)
                .andRelationTypeEqualTo(relationType)
                .andStatusEqualTo(true);
        long totalCount = amazonProcessReportService.countByExample(example);
        long pageCount = (totalCount + PAGE_SIZE - 1) / PAGE_SIZE;
        for (int currentPage = 1; currentPage <= pageCount; currentPage++) {
            try {
                AmazonProcessReportExample amazonProcessReportExample = new AmazonProcessReportExample();
                amazonProcessReportExample.setOrderByClause("creation_date asc");
                amazonProcessReportExample.createCriteria()
                        .andCreationDateGreaterThanOrEqualTo(greaterThanDate)
                        .andCreationDateLessThanOrEqualTo(LessThanTime)
                        .andFeedTypeEqualTo(feedType)
                        .andRelationTypeEqualTo(relationType)
                        .andStatusEqualTo(true);
                int offset = (currentPage - 1) * PAGE_SIZE;
                amazonProcessReportExample.setOffset(offset);
                amazonProcessReportExample.setLimit(PAGE_SIZE);
                List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.findByExample(amazonProcessReportExample);
                this.handleUpdateFulfillmentToListing(amazonProcessReportList);
            }catch (Exception e){
                XxlJobLogger.log(e.getMessage());
                log.error(e.getMessage());
            }
        }
        long endTime = System.currentTimeMillis();
        logger.warn("*****************亚马逊定时处理成功的备货期数据到Listing结束*****************耗时" + ((endTime - startTime) / 1000L));
        return ReturnT.SUCCESS;
    }

    /**
     * 处理修改成功的备货期数据到Listing
     * @param amazonProcessReportList
     */
    private void handleUpdateFulfillmentToListing(List<AmazonProcessReport> amazonProcessReportList){
        if (CollectionUtils.isEmpty(amazonProcessReportList)) {
            return;
        }
        Map<String,List<AmazonProcessReport>> accountaAazonProcessReportMap = amazonProcessReportList.stream().collect(Collectors.groupingBy(o -> o.getAccountNumber()));
        for (String accountNumber : accountaAazonProcessReportMap.keySet()){
            try {
                String count = amazonProductListingService.getSiteByAccount(accountNumber);
                if (StringUtils.isBlank(count)) {
                    XxlJobLogger.log(accountNumber + "读取订单系统获取账号对应站点失败，待处理备货期条数 " + accountaAazonProcessReportMap.get(accountNumber).size());
                    continue;
                }
                List<AmazonProcessReport> amazonProcessReports = accountaAazonProcessReportMap.get(accountNumber);
                for (AmazonProcessReport amazonProcessReport : amazonProcessReports) {
                    AmazonProductListing amazonProductListing = new AmazonProductListing();
                    amazonProductListing.setSellerSku(amazonProcessReport.getDataValue());
                    amazonProductListing.setAccountNumber(accountNumber);
                    amazonProductListing.setFulfillmentLatency(Integer.valueOf(amazonProcessReport.getAfterFulfillmentLatencyValue()));
                    amazonProductListing.setSite(count);
                    amazonProductListingService.updateDbAndEsBySellerSkuAndAccountNumber(amazonProductListing);
                }
            }catch (Exception e){
                XxlJobLogger.log(e.getMessage());
                log.error(e.getMessage());
            }
        }
    }

}
