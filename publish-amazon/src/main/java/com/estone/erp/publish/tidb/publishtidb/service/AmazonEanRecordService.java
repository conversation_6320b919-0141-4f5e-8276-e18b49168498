package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecordCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecordExample;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-09 16:21:02
 */
public interface AmazonEanRecordService {
    int countByExample(AmazonEanRecordExample example);

    CQueryResult<AmazonEanRecord> search(CQuery<AmazonEanRecordCriteria> cquery);

    List<AmazonEanRecord> selectByExample(AmazonEanRecordExample example);

    AmazonEanRecord selectByPrimaryKey(Long id);

    int insert(AmazonEanRecord record);

    int updateByPrimaryKeySelective(AmazonEanRecord record);

    int updateByExampleSelective(AmazonEanRecord record, AmazonEanRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);

    /**
     * 查询已存在的ean
     * @param eanList
     * @return
     */
    List<String>selectExistEan(List<String> eanList);

    int batchInsert(List<AmazonEanRecord> amazonEanRecordList);

    /**
     * 检测 该EAN前缀是否存在记录
     * @param eanPrefix
     * @return
     */
    int checkExistEanPrefixRecord(String eanPrefix);
}