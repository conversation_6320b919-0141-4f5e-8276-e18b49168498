package com.estone.erp.publish.amazon.enums;

import lombok.Getter;

/**
 * 店铺配置sku录入时间类型枚举
 * sku录入时间类型：1 月、2 年
 */
@Getter
public enum SkuCreateTimeTypeEnum {
    MONTH(1,"月"),
    YEAR(2,"年"),
    ;


    private Integer code;
    private String name;

    private SkuCreateTimeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isTrue(Integer val) {
        return this.code.equals(val);
    }

}
