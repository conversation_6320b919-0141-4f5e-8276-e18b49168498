package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.enums.AmazonReqAttrAdapterTypeEnums;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:44
 */
@Data
public class AmazonRequiredAttributeDO {

    /**
     * 记录Id
     */
    private Integer id;

    private String site;
    private String productType;

    /**
     * 站点
     */
    private List<String> sites;

    /**
     * 分类类型
     */
    private List<String> productTypes;

    /**
     * 必填属性
     */
    private String attributeName;

    /**
     * 必填属性值
     */
    private String attributeValue;

    /**
     * 属性适配类型 1  站点匹配 2 分类类型匹配 3 站点+分类类型匹配 4 通用属性
     */
    private Integer adapterType;

    /**
     * 1：运营配置 2: 技术部配置
     */
    private Integer type;

    /**
     * 适用属性类型 1:单体, 2:变体, 3:单体变体都适用
     */
    private Integer applicableAttributeType;

    private Boolean codeAdapter;

    private String scriptData;

    /**
     * 获取适配类型
     *
     * @return
     */
    public AmazonReqAttrAdapterTypeEnums matchAdapterType() {
        if (CollectionUtils.isNotEmpty(this.sites) && CollectionUtils.isEmpty(this.productTypes)) {
            return AmazonReqAttrAdapterTypeEnums.SITE_MATCH;
        }
        if (CollectionUtils.isEmpty(this.sites) && CollectionUtils.isNotEmpty(this.productTypes)) {
            return AmazonReqAttrAdapterTypeEnums.CATEGORY_TYPE_MATCH;
        }
        if (CollectionUtils.isNotEmpty(this.sites) && CollectionUtils.isNotEmpty(this.productTypes)) {
            return AmazonReqAttrAdapterTypeEnums.SITE_CATEGORY_TYPE_MATCH;
        }
        if (this.adapterType == null) {
            return AmazonReqAttrAdapterTypeEnums.GENERAL_ATTR;
        }
        return AmazonReqAttrAdapterTypeEnums.GENERAL_ATTR;
    }
}
