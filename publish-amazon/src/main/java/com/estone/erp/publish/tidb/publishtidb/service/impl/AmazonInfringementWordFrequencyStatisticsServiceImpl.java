package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.C<PERSON>uery<PERSON>ult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.enums.WenAnTypeEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyStatisticsLogMapper;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyStatisticsMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatistics;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsService;
import com.estone.erp.publish.tidb.publishtidb.domain.AmazonInfringementWordFrequencyStatisticVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 2024-06-13 16:25:34
 */
@Service("amazonInfringementWordFrequencyStatisticsService")
@Slf4j
public class AmazonInfringementWordFrequencyStatisticsServiceImpl implements AmazonInfringementWordFrequencyStatisticsService {

    @Resource
    private AmazonInfringementWordFrequencyStatisticsMapper amazonInfringementWordFrequencyStatisticsMapper;

    @Resource
    private AmazonInfringementWordFrequencyStatisticsLogMapper amazonInfringementWordFrequencyStatisticsLogMapper;


    @Override
    public int countByExample(AmazonInfringementWordFrequencyStatisticsExample example) {
        Assert.notNull(example, "example is null!");
        return amazonInfringementWordFrequencyStatisticsMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonInfringementWordFrequencyStatisticVO> search(CQuery<AmazonInfringementWordFrequencyStatisticsCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonInfringementWordFrequencyStatisticsCriteria query = cquery.getSearch();
        AmazonInfringementWordFrequencyStatisticsExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonInfringementWordFrequencyStatisticsMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonInfringementWordFrequencyStatistics> amazonInfringementWordFrequencyStatisticss = amazonInfringementWordFrequencyStatisticsMapper.selectByExample(example);

        // 组装结果
        CQueryResult<AmazonInfringementWordFrequencyStatisticVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        List<AmazonInfringementWordFrequencyStatisticVO> statisticVOList = amazonInfringementWordFrequencyStatisticss.stream().map(amazonInfringementWordFrequencyStatistics -> {
            AmazonInfringementWordFrequencyStatisticVO statisticVO = BeanUtil.copyProperties(amazonInfringementWordFrequencyStatistics, AmazonInfringementWordFrequencyStatisticVO.class);
            statisticVO.setId(amazonInfringementWordFrequencyStatistics.getId().toString());
            return statisticVO;
        }).collect(Collectors.toList());
        result.setRows(statisticVOList);
        return result;
    }

    @Override
    public AmazonInfringementWordFrequencyStatistics selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return amazonInfringementWordFrequencyStatisticsMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonInfringementWordFrequencyStatistics> selectByExample(AmazonInfringementWordFrequencyStatisticsExample example) {
        Assert.notNull(example, "example is null!");
        return amazonInfringementWordFrequencyStatisticsMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonInfringementWordFrequencyStatistics record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonInfringementWordFrequencyStatisticsMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonInfringementWordFrequencyStatistics record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonInfringementWordFrequencyStatisticsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonInfringementWordFrequencyStatistics record, AmazonInfringementWordFrequencyStatisticsExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonInfringementWordFrequencyStatisticsMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonInfringementWordFrequencyStatisticsMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<AmazonInfringementWordFrequencyStatisticVO> download(CQuery<AmazonInfringementWordFrequencyStatisticsCriteria> cquery) {
        AmazonInfringementWordFrequencyStatisticsCriteria query = cquery.getSearch();
        AmazonInfringementWordFrequencyStatisticsExample example = query.getExample();
        List<AmazonInfringementWordFrequencyStatistics> amazonInfringementWordFrequencyStatisticss = amazonInfringementWordFrequencyStatisticsMapper.selectByExample(example);

        // 组装结果
        return amazonInfringementWordFrequencyStatisticss.stream().map(amazonInfringementWordFrequencyStatistics -> {
            AmazonInfringementWordFrequencyStatisticVO statisticVO = BeanUtil.copyProperties(amazonInfringementWordFrequencyStatistics, AmazonInfringementWordFrequencyStatisticVO.class);
            statisticVO.setId(amazonInfringementWordFrequencyStatistics.getId().toString());
            return statisticVO;
        }).collect(Collectors.toList());
    }


    @Override
    public void saveOrUpdateQuantity(List<AmazonInfringementWordFrequencyLog> amazonInfringementWordFrequencyLogList) {
        // 校验
        if (CollectionUtils.isEmpty(amazonInfringementWordFrequencyLogList)) {
            return;
        }

        // 查询是否存在
        CompletableFuture<List<AmazonInfringementWordFrequencyStatistics>> future = CompletableFuture.supplyAsync(() -> {
            List<String> originWords = amazonInfringementWordFrequencyLogList.stream().map(AmazonInfringementWordFrequencyLog::getOriginWord).distinct().collect(Collectors.toList());
            AmazonInfringementWordFrequencyStatisticsExample statisticsExample = new AmazonInfringementWordFrequencyStatisticsExample();
            statisticsExample.createCriteria().andOriginWordIn(originWords);
            return amazonInfringementWordFrequencyStatisticsMapper.selectByExample(statisticsExample);
        }, AmazonExecutors.INFRINGEMENT_WORD_FREQUENCY_STATISTICS_POOL);

        // 批量更新
        CompletableFuture<Void> updateFuture = future.thenAcceptAsync(frequencyStatistics -> {
            if (CollectionUtils.isNotEmpty(frequencyStatistics)) {
                Map<String, List<AmazonInfringementWordFrequencyLog>> wordValidateResultMap = amazonInfringementWordFrequencyLogList.stream().collect(Collectors.groupingBy(AmazonInfringementWordFrequencyLog::getOriginWord));

                try {
                    List<AmazonInfringementWordFrequencyStatistics> infringementWordFrequencyStatisticsList = frequencyStatistics.stream().map(statistics -> {
                        List<AmazonInfringementWordFrequencyLog> wordValidateResults = wordValidateResultMap.get(statistics.getOriginWord());
                        if (CollectionUtils.isEmpty(wordValidateResults)) {
                            return null;
                        }

                        // 按照type分组
                        Map<Integer, List<AmazonInfringementWordFrequencyLog>> typeMap = wordValidateResults.stream().collect(Collectors.groupingBy(AmazonInfringementWordFrequencyLog::getType));

                        // 统计数量
                        statistics.setUniversalCopywritingNumber(
                                statistics.getUniversalCopywritingNumber() + Optional.ofNullable(typeMap.get(WenAnTypeEnum.defaultWenAn.getCode())).map(List::size).orElse(0)
                        );
                        statistics.setAmazonCopywritingNumber(
                                statistics.getAmazonCopywritingNumber() + Optional.ofNullable(typeMap.get(WenAnTypeEnum.amazonWenAn.getCode())).map(List::size).orElse(0)
                        );
                        statistics.setTotalNumber(statistics.getTotalNumber() + wordValidateResults.size());

                        // 判断是否是商标词，并聚合商标词
                        if (StringUtils.isNotBlank(statistics.getTrademarkWord())) {
                            String newTrademarkWord = Stream.of(wordValidateResults.stream().flatMap(wordValidateResult -> Arrays.stream(wordValidateResult.getTrademarkWord().split(","))).collect(Collectors.toList()), Arrays.stream(statistics.getTrademarkWord().split(",")).collect(Collectors.toList())).flatMap(List::stream).distinct().collect(Collectors.joining(","));
                            statistics.setTrademarkWord(newTrademarkWord);
                        }
                        return statistics;
                    }).filter(Objects::nonNull).collect(Collectors.toList());

                    // 批量更新
                    if (CollectionUtils.isNotEmpty(infringementWordFrequencyStatisticsList)) {
                        List<List<AmazonInfringementWordFrequencyStatistics>> partition = Lists.partition(infringementWordFrequencyStatisticsList, 300);
                        partition.forEach(list -> AmazonExecutors.executeInfringementWordFrequencyBatchUpdateTask(() -> {
                            amazonInfringementWordFrequencyStatisticsMapper.batchUpdate(list);
                        }));
                        partition.clear();
                        infringementWordFrequencyStatisticsList.clear();
                    }
                } catch (Exception e) {
                }
            }
        }, AmazonExecutors.INFRINGEMENT_WORD_FREQUENCY_STATISTICS_POOL);

        // 批量插入
        CompletableFuture<Void> insertFuture = future.thenAcceptAsync(frequencyStatistics -> {
            // 获取已存在的词
            Map<String, List<AmazonInfringementWordFrequencyStatistics>> wordValidateResultMap = frequencyStatistics.stream().collect(Collectors.groupingBy(AmazonInfringementWordFrequencyStatistics::getOriginWord));

            try {
                Map<String, List<AmazonInfringementWordFrequencyLog>> amazonInfringementWordFrequencyLogMap = amazonInfringementWordFrequencyLogList.stream().collect(Collectors.groupingBy(AmazonInfringementWordFrequencyLog::getOriginWord));

                // 不包含的则插入
                List<AmazonInfringementWordFrequencyStatistics> infringementWordFrequencyStatisticsList = amazonInfringementWordFrequencyLogMap.entrySet().stream()
                        .filter(entry -> Objects.isNull(wordValidateResultMap.get(entry.getKey())))
                        .map(wordValidateResult -> {
                            List<AmazonInfringementWordFrequencyLog> wordValidateResultValue = wordValidateResult.getValue();

                            AmazonInfringementWordFrequencyStatistics statistics = new AmazonInfringementWordFrequencyStatistics();
                            statistics.setOriginWord(wordValidateResult.getKey());
                            Optional.ofNullable(wordValidateResultValue.get(0).getTrademarkWord()).ifPresent(statistics::setTrademarkWord);
                            Optional.ofNullable(wordValidateResultValue.get(0).getSaleId()).ifPresent(statistics::setCreateBy);
                            Optional.ofNullable(wordValidateResultValue.get(0).getCreateTime()).ifPresent(statistics::setCreateTime);

                            // 按照type分组
                            Map<Integer, List<AmazonInfringementWordFrequencyLog>> integerListMap = wordValidateResultValue.stream().collect(Collectors.groupingBy(AmazonInfringementWordFrequencyLog::getType));

                            // 统计数量
                            statistics.setUniversalCopywritingNumber(
                                    Long.valueOf(Optional.ofNullable(integerListMap.get(WenAnTypeEnum.defaultWenAn.getCode())).map(List::size).orElse(0))
                            );
                            statistics.setAmazonCopywritingNumber(
                                    Long.valueOf(Optional.ofNullable(integerListMap.get(WenAnTypeEnum.amazonWenAn.getCode())).map(List::size).orElse(0))
                            );
                            statistics.setTotalNumber(Long.valueOf(wordValidateResultValue.size()));
                            return statistics;
                        }).collect(Collectors.toList());

                // 批量插入
                if (CollectionUtils.isNotEmpty(infringementWordFrequencyStatisticsList)) {
                    List<List<AmazonInfringementWordFrequencyStatistics>> partition = Lists.partition(infringementWordFrequencyStatisticsList, 300);
                    partition.forEach(list -> AmazonExecutors.executeInfringementWordFrequencyBatchUpdateTask(() -> {
                        amazonInfringementWordFrequencyStatisticsMapper.batchInsert(list);
                    }));
                    partition.clear();
                    infringementWordFrequencyStatisticsList.clear();
                }
            } catch (Exception e) {
            }
        }, AmazonExecutors.INFRINGEMENT_WORD_FREQUENCY_STATISTICS_POOL);

        CompletableFuture.allOf(updateFuture, insertFuture).join();
//        CompletableFuture.allOf(updateFuture, insertFuture).thenRunAsync(() -> {
//            try {
//                // 查询词频统计
//                List<String> originWordList = amazonInfringementWordFrequencyLogList.stream().map(AmazonInfringementWordFrequencyLog::getOriginWord).distinct().collect(Collectors.toList());
//                AmazonInfringementWordFrequencyStatisticsExample frequencyStatisticsExample = new AmazonInfringementWordFrequencyStatisticsExample();
//                frequencyStatisticsExample.createCriteria().andOriginWordIn(originWordList);
//                List<AmazonInfringementWordFrequencyStatistics> amazonInfringementWordFrequencyStatistics = amazonInfringementWordFrequencyStatisticsMapper.selectByExample(frequencyStatisticsExample);
//
//                // 保存或更新词频统计后保存词频统计日志
//                Map<String, List<AmazonInfringementWordFrequencyLog>> frequencyLogMap = amazonInfringementWordFrequencyLogList.stream()
//                        .collect(Collectors.groupingBy(AmazonInfringementWordFrequencyLog::getOriginWord));
//                List<AmazonInfringementWordFrequencyStatisticsLog> wordFrequencyStatisticsLogs = amazonInfringementWordFrequencyStatistics.parallelStream()
//                        .flatMap(statistics -> {
//                            List<AmazonInfringementWordFrequencyLog> logs = frequencyLogMap.get(statistics.getOriginWord());
//                            if (CollectionUtils.isNotEmpty(logs)) {
//                                return logs.stream().map(log -> {
//                                    // 保存日志
//                                    AmazonInfringementWordFrequencyStatisticsLog statisticsLog = new AmazonInfringementWordFrequencyStatisticsLog();
//                                    statisticsLog.setRelationId(statistics.getId());
//                                    statisticsLog.setSpu(Optional.ofNullable(log.getSpu()).orElse(""));
//                                    Optional.ofNullable(log.getSaleId()).ifPresent(statisticsLog::setSaleId);
//                                    statisticsLog.setType(Optional.ofNullable(log.getType()).orElse(WenAnTypeEnum.defaultWenAn.getCode()));
//                                    Optional.ofNullable(log.getSaleId()).ifPresent(statisticsLog::setCreateBy);
//                                    statisticsLog.setCreateTime(log.getCreateTime());
//                                    return statisticsLog;
//                                });
//                            }
//                            return Stream.empty();
//                        })
//                        .collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(wordFrequencyStatisticsLogs)) {
//                    List<List<AmazonInfringementWordFrequencyStatisticsLog>> partition = Lists.partition(wordFrequencyStatisticsLogs, 1000);
//                    partition.forEach(list -> AmazonExecutors.executeInfringementWordFrequencyBatchUpdateTask(() -> {
//                        amazonInfringementWordFrequencyStatisticsLogMapper.batchInsert(list);
//                    }));
//                }
//            } catch (Exception e) {
//            }
//        }, AmazonExecutors.INFRINGEMENT_WORD_FREQUENCY_STATISTICS_POOL).join();
    }

}