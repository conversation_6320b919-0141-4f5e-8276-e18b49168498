package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.AmazonAccountEan;
import com.estone.erp.publish.amazon.model.AmazonAccountEanExample;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> amazon_account_ean
 * 2019-08-27 16:50:57
 */
public class AmazonAccountEanCriteria extends AmazonAccountEan {
    private static final long serialVersionUID = 1L;

    public AmazonAccountEanExample getExample() {
        AmazonAccountEanExample example = new AmazonAccountEanExample();
        AmazonAccountEanExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSellerId())) {
            criteria.andSellerIdEqualTo(this.getSellerId());
        }
        if (StringUtils.isNotBlank(this.getEanPrefix())) {
            criteria.andEanPrefixEqualTo(this.getEanPrefix());
        }
        if (this.getLastPrefixStr() != null) {
            criteria.andLastPrefixstrEqualTo(this.getLastPrefixStr());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (this.getVersion() != null) {
            criteria.andVersionEqualTo(this.getVersion());
        }
        return example;
    }
}