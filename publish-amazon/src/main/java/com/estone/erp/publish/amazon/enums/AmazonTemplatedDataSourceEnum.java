package com.estone.erp.publish.amazon.enums;

/**
 * @Description: ${模板数据来源}
 * @Author: yjy
 * @Date: 2020/6/29 16:26
 * @Version: 1.0.0
 */
public enum AmazonTemplatedDataSourceEnum {
    //数据来源：productSystem(产品开发系统)、erpDataSystem(数据分析系统) 为空也是产品开发系统
    PRODUCT_SYSTEM("productSystem","产品开发系统"),
    ERP_DATA_SYSTEM("erpDataSystem","数据分析系统"),
    PRODUCT_AUTO_PUBLISH("productAutoPublish","产品系统自动刊登");

    //状态英文
    private String statusMsgEn;
    //状态中文
    private String statusMsgCn;

    private AmazonTemplatedDataSourceEnum(String statusMsgEn, String statusMsgCn) {
        this.statusMsgEn = statusMsgEn;
        this.statusMsgCn = statusMsgCn;
    }

    public String getStatusMsgEn() {
        return statusMsgEn;
    }

    public String getStatusMsgCn() {
        return statusMsgCn;
    }
}
