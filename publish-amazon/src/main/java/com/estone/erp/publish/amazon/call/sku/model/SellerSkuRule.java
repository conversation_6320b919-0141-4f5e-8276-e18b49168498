package com.estone.erp.publish.amazon.call.sku.model;

import lombok.Data;

import java.util.List;

/**
 * 
 * @Description: amazon sellerSKU生成规则
 * 
 * @ClassName: SellerSKURuler
 * @Author: Kevin
 * @Date: 2018/10/18
 * @Version: 0.0.1
 */
@Data
public class SellerSkuRule {

    /**
     * 前缀
     */
    private List<UnitRuleValue> prefixRuleValues;

    /**
     * 前分隔符
     */
    private UnitRuleValue prefixSplitRuleValue;

    /**
     * 后分隔符
     */
    private UnitRuleValue suffixSplitRuleValue;

    /**
     * 后缀
     */
    private List<UnitRuleValue> suffixRuleValues;
}
