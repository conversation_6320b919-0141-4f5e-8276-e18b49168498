package com.estone.erp.publish.amazon.call.process.submit;

import com.alibaba.druid.util.StringUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @Description: 基础刊登数据类
 * 
 * @ClassName: BasePublishData
 * @Author: Kevin
 * @Date: 2018/08/20
 * @Version: 0.0.1
 */
public class PublishData<T> {

    /**
     * 刊登需要的单元数据列表
     */
    private List<T> unitDatas;

    /**
     * amazon账号
     */
    private AmazonAccount account;

    /**
     * 上传数据类型
     */
    private String feedType;

    /**
     * 上传数据任务id
     */
    private String feedSubmissionId;

    /**
     * 自定义系统任务id
     */
    private String taskId;

    /**
     * amazon xsd操作类型
     */
    private OperationType operationType = OperationType.Update;

    /**
     * 成功状态：默认失败
     */
    private boolean success = false;

    /**
     * 处理报告list
     */
    private List<AmazonProcessReport> reports;

    /**
     * sku与处理报告的map
     */
    private Map<String, AmazonProcessReport> sku2ReportMap;

    /**
     * message id与sku的map
     */
    private Map<Integer, String> msgId2SkuMap;

    /**
     * 错误的sku与错误信息的map
     */
    private Map<String, String> errorSku2MsgMap;

    /**
     * 货币
     */
    private String currency;

    /**
     * 账号的图片路径
     */
    private String imagePath;

    /**
     * sku与数据来源的map
     */
    private Map<String, Integer> skuSpFlagMap;

    /**
     * sku与sellerSku的map
     */
    private Map<String, String> sku2SellerSkuMap;

    /**
     * 图片地址list，刊登时随机复制图片，刊登之后删除图片
     */
    private List<String> imageUrls;

    public List<T> getUnitDatas() {
        return unitDatas;
    }

    public void setUnitDatas(List<T> unitDatas) {
        this.unitDatas = unitDatas;
    }

    public AmazonAccount getAccount() {
        return account;
    }

    public void setAccount(AmazonAccount account) {
        this.account = account;
    }

    public String getFeedType() {
        return feedType;
    }

    public void setFeedType(String feedType) {
        this.feedType = feedType;
    }

    public String getFeedSubmissionId() {
        return feedSubmissionId;
    }

    public void setFeedSubmissionId(String feedSubmissionId) {
        this.feedSubmissionId = feedSubmissionId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public List<AmazonProcessReport> getReports() {
        return reports;
    }

    public void setReports(List<AmazonProcessReport> reports) {
        this.reports = reports;
    }

    public Map<String, AmazonProcessReport> getSku2ReportMap() {
        // 初始化map
        if (sku2ReportMap == null) {
            if (CollectionUtils.isEmpty(reports)) {
                return CommonUtils.emptyMap();
            }

            sku2ReportMap = new HashMap<String, AmazonProcessReport>(reports.size());
            for (AmazonProcessReport report : reports) {
                sku2ReportMap.put(report.getDataValue(), report);
            }
        }
        return sku2ReportMap;
    }

    public void setSku2ReportMap(Map<String, AmazonProcessReport> sku2ReportMap) {
        this.sku2ReportMap = sku2ReportMap;
    }

    public Map<Integer, String> getMsgId2SkuMap() {
        return msgId2SkuMap;
    }

    public void setMsgId2SkuMap(Map<Integer, String> msgId2SkuMap) {
        this.msgId2SkuMap = msgId2SkuMap;
    }

    public Map<String, String> getErrorSku2MsgMap() {
        if (errorSku2MsgMap == null) {
            errorSku2MsgMap = CommonUtils.emptyMap();
        }

        return errorSku2MsgMap;
    }

    public void setErrorSku2MsgMap(Map<String, String> errorSku2MsgMap) {
        this.errorSku2MsgMap = errorSku2MsgMap;
    }

    /**
     * 
     * @Description: 添加错误的sku和其错误信息
     *
     * @param sku sku
     * @param msg 错误信息
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void addErrorSku(String sku, String msg) {
        if (errorSku2MsgMap == null) {
            errorSku2MsgMap = new HashMap<String, String>();
        }

        errorSku2MsgMap.put(sku, msg);
    }

    public String getCurrency() {
        if (StringUtils.isEmpty(currency)) {
            initCurrency();
        }
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 
     * @Description: 初始化账号对应的货币
     *
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void initCurrency() {
        //AmazonMarketplace marketplace = AmazonConstant.marketplaceIdMap.get(account.getMarketplaceId());
        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        AmazonMarketplace marketplace = amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId());
        currency = marketplace != null ? marketplace.getCurrency() : AmazonConstant.DEFAULT_COUNTRY_CURRENCY;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public void addImageUrl(String imageUrl) {
        if (imageUrls == null) {
            imageUrls = new ArrayList<String>();
        }

        imageUrls.add(imageUrl);
    }

    public Map<String, String> getSku2SellerSkuMap() {
        return sku2SellerSkuMap;
    }

    public void setSku2SellerSkuMap(Map<String, String> sku2SellerSkuMap) {
        this.sku2SellerSkuMap = sku2SellerSkuMap;
    }

    public void addSku2SellerSku(String sku, String sellerSku) {
        if (sku2SellerSkuMap == null) {
            sku2SellerSkuMap = new HashMap<String, String>();
        }

        sku2SellerSkuMap.put(sku, sellerSku);
    }

    public Map<String, Integer> getSkuSpFlagMap() {
        return skuSpFlagMap;
    }

    public void setSkuSpFlagMap(Map<String, Integer> skuSpFlagMap) {
        this.skuSpFlagMap = skuSpFlagMap;
    }

    public void addSkuSpFlagMap(String sku, Integer skuDadaSource) {
        if (skuSpFlagMap == null) {
            skuSpFlagMap = new HashMap<String, Integer>();
        }
        skuSpFlagMap.put(sku, skuDadaSource);
    }
}
