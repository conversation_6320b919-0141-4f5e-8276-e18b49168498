package com.estone.erp.publish.amazon.model.request;

import lombok.Data;

import java.util.List;

/**
 * 下架参数
 * <AUTHOR>
 * @date 2023-05-12 10:43
 */
@Data
public class RepeatSkuOffLineRequest {

    private List<Integer> templateIds;
    /**
     * 不过滤侵权词保存模板信息
     */
    private boolean needFilter = false;

    /**
     * true 不过滤侵权词保存模板信息
     * false 过滤侵权词保存
     */
    //private boolean filterSave = false;

    @Override
    public String toString() {
        return "RepeatSkuOffLineRequest{" +
                "templateIds=" + templateIds +
                ", needFilter=" + needFilter  +
                '}';
    }
}
