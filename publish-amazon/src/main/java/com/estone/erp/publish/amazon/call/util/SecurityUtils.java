package com.estone.erp.publish.amazon.call.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;

/**
 * 加密工具类
 * 
 * <AUTHOR>
 *
 */
public class SecurityUtils {
    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);

    private static final String UTF8 = "UTF-8";

    /**
     * 生成32位md5码
     * 
     * @param dest
     * @return
     */
    public static String md5(String dest) {
        try {
            // 得到一个信息摘要器
            MessageDigest digest = MessageDigest.getInstance("md5");
            byte[] result = digest.digest(dest.getBytes(UTF8));
            StringBuffer buffer = new StringBuffer();
            // 把每一个byte 做一个与运算 0xff;
            for (byte b : result) {
                // 与运算
                int number = b & 0xff;// 加盐
                String str = Integer.toHexString(number);
                if (str.length() == 1) {
                    buffer.append("0");
                }
                buffer.append(str);
            }

            // 标准的md5加密后的结果
            return buffer.toString();
        }
        catch (Exception e) {
            log.error("mde encrypt occured error: " + e.getMessage());
        }

        return null;
    }

    /**
     * RSA私钥加密
     * 
     * @param data 待加密的数据
     * @param privateKey 私钥
     * @return
     */
    public static String rsaEncryptByPublic(String data, String privateKey) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(privateKey)) {
            return null;
        }

        try {
            return parseByte2HexStr(RSACoder.encryptByPrivateKey(data.getBytes(UTF8), Base64.decodeBase64(privateKey)));
        }
        catch (Exception e) {
            log.error("RSA encrypt by public key occured error: " + e.getMessage());
        }

        return null;
    }

    /**
     * RSA私钥解密
     * 
     * @param data 已加密的数据
     * @param privateKey 私钥
     * @return
     */
    public static String rsaDecryptByPublic(String encryptedData, String privateKey) {
        if (StringUtils.isBlank(encryptedData) || StringUtils.isBlank(privateKey)) {
            return null;
        }

        try {
            return new String(
                    RSACoder.decryptByPrivateKey(parseHexStr2Byte(encryptedData), Base64.decodeBase64(privateKey)));
        }
        catch (Exception e) {
            log.error("RSA decrypt by public key occured error: " + e.getMessage());
        }

        return null;
    }

    /**
     * AES加密
     * 
     * @param data 待加密的数据
     * @param password 秘钥
     * @return
     */
    public static String aesEncrypt(String data, String password) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(password)) {
            return null;
        }

        String result = null;
        try {
            result = parseByte2HexStr(AESCoder.encrypt(data.getBytes(UTF8), Base64.decodeBase64(password)));
        }
        catch (Exception e) {
            log.error("AES encrypt by password occured error: " + e.getMessage());
        }

        return result;
    }

    /**
     * AES解密
     * 
     * @param encryptedData 已加密的数据
     * @param password 秘钥
     * @return
     */
    public static String aesDecrypt(String encryptedData, String password) {
        if (StringUtils.isBlank(encryptedData) || StringUtils.isBlank(password)) {
            return null;
        }

        String result = null;
        try {
            result = new String(AESCoder.decrypt(parseHexStr2Byte(encryptedData), Base64.decodeBase64(password)));
        }
        catch (Exception e) {
            log.error("AES decrypt by password occured error: " + e.getMessage());
        }

        return result;
    }

    /**
     * 计算hmac签名
     * 
     * @param algorithm hmac算法
     * @param data 待签名的数据
     * @param signingKey 签名秘钥
     * @return
     */
    public static String hmac(String algorithm, String data, String signingKey) {
        try {
            return Base64.encodeBase64String(
                    HmacCoder.encrypt(algorithm, data.getBytes(UTF8), Base64.decodeBase64(signingKey)));
        }
        catch (Exception e) {
            log.error("hmacSha1 encrypt by signingKey occured error: " + e.getMessage());
        }

        return null;
    }

    /**
     * 将二进制转换成16进制
     * 
     * @param bytes
     * 
     * @return
     */
    public static String parseByte2HexStr(byte[] bytes) {
        if (ArrayUtils.isEmpty(bytes)) {
            return null;
        }

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }

        return sb.toString();
    }

    /**
     * 将16进制转换为二进制
     * 
     * @param hexStr
     * 
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (StringUtils.isEmpty(hexStr)) {
            return null;
        }

        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }

        return result;
    }
}
