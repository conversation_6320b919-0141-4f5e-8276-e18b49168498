package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementTaskRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * Amazon链接管理任务执行记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface AmazonLinkManagementTaskRecordService extends IService<AmazonLinkManagementTaskRecord> {
    /**
     * 分页查询
     * @param query
     * @return
     */
    CQueryResult<AmazonLinkManagementTaskRecord> queryPage(CQuery<AmazonLinkManagementTaskRecord> query);
}
