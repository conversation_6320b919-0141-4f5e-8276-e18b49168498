package com.estone.erp.publish.amazon.call;

import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.componet.AmazonTemplateInfringementProductHelper;
import com.estone.erp.publish.amazon.enums.FollowSellDataStatusEnum;
import com.estone.erp.publish.amazon.enums.FollowSellOperationType;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonFollowSellService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonProductFollowSellLogService;
import com.estone.erp.publish.amazon.util.AmazonProductStatusUtil;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * @Description 产品列表 批量跟卖辅助类
 * <AUTHOR>
 * @Date 2019/10/10 16:20
 **/
@Slf4j
@Component("amazonFollowSellPublishCallHelper")
public class AmazonFollowSellPublishCallHelper {

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;
    @Autowired
    protected AmazonProductFollowSellLogService amazonProductFollowSellLogService;
    @Autowired
    private AmazonFollowSellService amazonFollowSellService;
    @Autowired
    private AmazonTemplateInfringementProductHelper amazonTemplateInfringementProductHelper;
    /**
     * 初始化处理报告
     * @param publishData
     * @return
     */
    public List<AmazonProcessReport> initProcessReports(PublishData<AmazonFollowSellSuperiorBO> publishData, FollowSellOperationType followSellOperationType) {
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = publishData.getUnitDatas();
        String feedType = publishData.getFeedType();
        List<AmazonProcessReport> reports = new ArrayList<>(1);
        for (AmazonFollowSellSuperiorBO followSell : amazonFollowSells) {
            AmazonProcessReport report = new AmazonProcessReport();
            report.setFeedType(feedType);
            report.setAccountNumber(followSell.getSellerId());
            report.setStatusCode(ProcessingReportStatusCode.Init.name());
            report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
            report.setDataValue(followSell.getSellerSku());
            if(FollowSellOperationType.AMAZON_FOLLOW_SELL.equals(followSellOperationType)){
                report.setRelationId(followSell.getId());
                report.setRelationType(ProcessingReportTriggleType.Follow_Sell.name());
            }else if(FollowSellOperationType.AMAZON_PRODUCT_BATCH_FOLLOW_SELL.equals(followSellOperationType)){
                //取产品列表变体id，不取产品列表id
                report.setRelationId(followSell.getVariantId());
                //产品列表批量跟卖
                report.setRelationType(ProcessingReportTriggleType.PRODUCT_FOLLOW_SELL.name());
            }
            if (StringUtils.isNotBlank(WebUtils.getUserName())) {
                report.setCreatedBy(WebUtils.getUserName());
            }else if(StringUtils.isNotEmpty(followSell.getCreatedBy())){
                report.setCreatedBy(followSell.getCreatedBy());
            }
            amazonProcessReportService.insert(report);
            reports.add(report);
        }
        return reports;
    }

    /**
     * @Description: 结束禁售或下线的sku
     * @param unitPublishData
     * @Author: Kevin
     * @Date: 2019/01/16
     * @Version: 0.0.1
     */
    public void finishForbidOrOffLineSkus(PublishData<AmazonFollowSellSuperiorBO> unitPublishData) {
        Iterator<AmazonFollowSellSuperiorBO> iterator = unitPublishData.getUnitDatas().iterator();
        Map<String, AmazonProcessReport> sku2ReportMap = unitPublishData.getSku2ReportMap();

        List<AmazonProcessReport> updateReports = new ArrayList<>();

        // 获取侵权信息
        ApiResult<Map<String, Boolean>> apiResult = amazonTemplateInfringementProductHelper.checkArticleNumberIsInfringement(unitPublishData.getUnitDatas());

        // 获取禁售停产存档废弃信息
        ApiResult<Map<String, Boolean>> mapApiResult = AmazonProductStatusUtil.checkFollowSellForbiddenAndItemStatus(unitPublishData.getUnitDatas());

        while (iterator.hasNext()) {
            AmazonFollowSellSuperiorBO unitData = iterator.next();
            Map<String, String> sellerSku2skuMap = getUnitDataSellerSku2SkuMap(unitData);
            if (MapUtils.isNotEmpty(sellerSku2skuMap)) {
                List<String> skus = new ArrayList<>(sellerSku2skuMap.values());
                String msg = null;

                // 过滤侵权产品
                if (!apiResult.isSuccess()) {
                    msg = apiResult.getErrorMsg();
                } else {
                    Map<String, Boolean> checkInfringementMap = apiResult.getResult();
                    for (String sku : skus) {
                        if (checkInfringementMap.get(sku) != null) {
                            if (checkInfringementMap.get(sku)) {
                                msg = sku + "侵权，不可以刊登";
                            }
                        }
                    }
                }

                // 过滤禁售停产存档废弃产品
                if (null == msg) {
                    if (!mapApiResult.isSuccess()) {
                        msg = mapApiResult.getErrorMsg();
                    } else {
                        Map<String, Boolean> checkMap = mapApiResult.getResult();
                        for (String sku : skus) {
                            if (checkMap.get(sku) != null) {
                                if (checkMap.get(sku)) {
                                    msg = sku + "禁售或停产存档废弃，不可以刊登";
                                }
                            }
                        }
                    }
                }

                // 存在禁售或下线的sku，则该单元数据不再刊登，并标记处理报告为失败
                if (StringUtils.isNotEmpty(msg)) {
                    for (Map.Entry<String, String> entry : sellerSku2skuMap.entrySet()) {
                        AmazonProcessReport report = sku2ReportMap.get(entry.getKey());
                        updateReports.add(report);
                        finshProcessReport(report, false, msg);
                    }
                    iterator.remove();
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateReports)) {
            amazonProcessReportService.update(updateReports);
        }
    }

    public Map<String, String> getUnitDataSellerSku2SkuMap(AmazonFollowSellSuperiorBO unitData) {
//        if (org.apache.commons.lang.BooleanUtils.toBoolean(unitData.getStepPublishStatus())) {
//            return null;
//        }
        Map<String, String> result = new HashMap<>(1);
        result.put(unitData.getSellerSku(), unitData.getParentSku());
        return result;
    }

    /**
     *
     * @Description: 设置错误的处理报告为结束状态
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void finishErrorProcessReports(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        List<AmazonProcessReport> reports = publishData.getReports();
        Map<String, String> errorSku2MsgMap = publishData.getErrorSku2MsgMap();
        if (MapUtils.isNotEmpty(errorSku2MsgMap)) {
            List<AmazonProcessReport> errorReports = new ArrayList<>(errorSku2MsgMap.size());
            for (AmazonProcessReport report : reports) {
                String sku = report.getDataValue();
                if (errorSku2MsgMap.containsKey(sku)) {
                    errorReports.add(report);
                    finshProcessReport(report, false, errorSku2MsgMap.get(sku));
                }
            }
            if(errorReports.size() > 0){
                amazonProcessReportService.update(errorReports);
            }
        }
    }

    /**
     *
     * @Description: 设置处理报告为结束状态
     * @param report 处理报告
     * @param status 成功状态
     * @param msg 报告信息
     * @return
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public boolean finshProcessReport(AmazonProcessReport report, boolean status, String msg) {
        if (report == null || ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
            return false;
        }
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setStatus(status);
        report.setResultMsg(msg);
        report.setFinishDate(new Timestamp(System.currentTimeMillis()));
        return true;
    }

    /**
     *
     * @Description: 设置处理报告为进行状态
     * @param reports 报告list
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void runProcessReports(List<AmazonProcessReport> reports) {
        reports.forEach(report -> {
            // 已经结束的处理报告不处理
            if (ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
                return;
            }
            report.setStatusCode(ProcessingReportStatusCode.Processing.name());
        });
        amazonProcessReportService.update(reports);
    }

    /**
     *
     * @Description: 更新处理报告的taskId
     * @param publishData 刊登数据
     * @param taskId taskId
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    public void updatePublishDataTaskId(PublishData<AmazonFollowSellSuperiorBO> publishData, String taskId) {
        publishData.setTaskId(taskId);
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            report.setTaskId(taskId);
        });
        amazonProcessReportService.update(reports);
    }

    //对上传成功的数据进行记录跟卖日志
    public void insertFollowSellLog(List<PublishData<AmazonFollowSellSuperiorBO>> unitPublishDatas) {
        //过滤出成功的
        List<PublishData<AmazonFollowSellSuperiorBO>> collect = unitPublishDatas.stream().
                filter(o -> o.isSuccess())
                .collect(Collectors.toList());
        //添加数据到list
        List<AmazonFollowSellSuperiorBO> okList = new ArrayList<>(8);
        for (PublishData<AmazonFollowSellSuperiorBO> data : collect) {
            okList.addAll(data.getUnitDatas());
        }
        //按sellerSku分组
        Map<String, List<AmazonFollowSellSuperiorBO>> okMap = okList.stream().collect(Collectors.groupingBy(o -> o.getSellerSku()));

        //只有分组后 v的size大于1 才成功
        List<AmazonFollowSellSuperiorBO> insertList = new ArrayList<>(8);
        okMap.forEach( (k,v) -> {
            if(CollectionUtils.isNotEmpty(v) && v.size() > 1 ){
                insertList.add(v.get(0));
            }
        });
        amazonProductFollowSellLogService.batchInsert(insertList);
    }

    /**
     * amazon跟卖模块操作
     * @param publishData
     */
    public void afterFinishProcessReportsFollowSell(PublishData<AmazonFollowSellSuperiorBO> publishData) {
        List<AmazonProcessReport> reports = publishData.getReports();
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = publishData.getUnitDatas();
        Map<String, AmazonFollowSellSuperiorBO> followSellMap = new HashMap<>(amazonFollowSells.size());
        for (AmazonFollowSellSuperiorBO followSell : amazonFollowSells) {
            followSellMap.put(followSell.getSellerSku(), followSell);
        }

        String feedType = publishData.getFeedType();
        OperationType operationType = publishData.getOperationType();
        List<AmazonFollowSellSuperiorBO> updateList = new ArrayList<>(amazonFollowSells.size());
        List<AmazonFollowSellSuperiorBO> deleteList = new ArrayList<>(amazonFollowSells.size());
        for (AmazonProcessReport report : reports) {
            AmazonFollowSellSuperiorBO followSell = followSellMap.get(report.getDataValue());
            boolean status = org.apache.commons.lang.BooleanUtils.toBoolean(report.getStatus());
            followSell.setPublishStatus(status);
            // 当为刊登产品时，设置状态
            if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType) && !OperationType.Delete.equals(operationType)) {
                updateList.add(followSell);
                // 保存跟卖状态
                followSell.setStepPublishStatus(status);
                if (followSell.getFirstFollowSellDate() == null) {
                    // 记录第一次跟卖时间 2018年9月5日 10:15:17 黎圣
                    followSell.setFirstFollowSellDate(new Timestamp(System.currentTimeMillis()));
                }
            }
            // 刊登成功，或者接口访问被拒绝
            else if (status || "Access denied".equals(report.getResultMsg())) {
                updateList.add(followSell);
            }

            //是删除操作
            if(SpFeedType.POST_PRODUCT_DATA.getValue().equals(feedType) && OperationType.Delete.equals(operationType)){
                deleteList.add(followSell);
            }
        }

        if (updateList.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        switch (SpFeedType.of(feedType)) {
            case POST_PRODUCT_DATA:
                if (OperationType.Delete.equals(operationType)) {
                    //新需求，删除amazon产品后，逻辑删除本地数据
                    deleteList.stream().forEach(obj -> {
                        setFollowSellDataStatus(operationType, feedType, obj, obj.isPublishStatus(), true);
                    });
                }
                else {
                    updateList.stream().forEach(obj -> {
                        setFollowSellDataStatus(operationType, feedType, obj, obj.isPublishStatus(), true);
                    });
//                    List<AmazonFollowSellSuperiorBO> updateData = getUpdateData(updateList, (src, dest) -> {
//                        dest.setDataStatus(src.getDataStatus());
//                        dest.setStepPublishStatus(src.getStepPublishStatus());
//                        dest.setIsOffLine(true);
//                        dest.setFirstFollowSellDate(src.getFirstFollowSellDate());
//                        //如果是刊登跟卖设置下sellerSku,因为现在sellerSku是在刊登跟卖时候生成的
//                        if (StringUtils.isNotBlank(src.getSellerSku())) {
//                            dest.setSellerSku(src.getSellerSku());
//                        }
//                    });
//                    for (AmazonFollowSellSuperiorBO bean : updateData) {
//                        amazonFollowSellService.update(bean);
//                    }
                }
                break;

            case POST_PRODUCT_PRICING_DATA:
                updateList.stream().forEach(obj -> {
                    setFollowSellDataStatus(operationType, feedType, obj, obj.isPublishStatus(), true);
                });

//                List<AmazonFollowSellSuperiorBO> updateDataPrice = getUpdateData(updateList, (src, dest) -> {
//                    dest.setStandardPrice(src.getStandardPrice());
//                });
//                for (AmazonFollowSellSuperiorBO bean : updateDataPrice) {
//                    amazonFollowSellService.update(bean);
//                }
                break;

            case POST_INVENTORY_AVAILABILITY_DATA:
                updateList.stream().forEach(obj -> {
                    setFollowSellDataStatus(operationType, feedType, obj, obj.isPublishStatus(), true);
                });

                /*List<AmazonFollowSellSuperiorBO> updateData = getUpdateData(updateList, (src, dest) -> {
                    dest.setQuantity(src.getQuantity());
                    if (dest.getQuantity() == 0) {
                        dest.setStepPublishStatus(true);
                        dest.setIsOffLine(null);
                        // 下架需要设置下架时间为当前时间，防止重复跟卖
                        dest.setOffLineDate(src.getOffLineDate());
                        if (currentTime < src.getOffLineDate().getTime()) {
                            dest.setOffLineDate(new Timestamp(currentTime));
                        }
                    }
                });
                List<AmazonFollowSellBO> collect = updateData.stream().map(obj -> {
                    AmazonFollowSellBO bean = new AmazonFollowSellBO();
                    BeanUtils.copyProperties(obj, bean);
                    return bean;
                }).collect(Collectors.toList());
//                amazonFollowSellService.batchUpdateAmazonFollowSell2OffLineDelete(collect);

                for (AmazonFollowSellBO data : collect) {
                    if(data.getQuantity() == 0){
                        //下架库存为0更新
                        amazonFollowSellService.batchUpdateAmazonFollowSell2OffLineDelete(Arrays.asList(data));
                    }else{
                        //库存上传，只更新不为空字段
                        amazonFollowSellService.update(data);
                    }
                }*/
                break;

            default:
                break;
        }
    }

    private List<AmazonFollowSellSuperiorBO> getUpdateData(List<AmazonFollowSellSuperiorBO> src,
                                                           BiConsumer<AmazonFollowSellSuperiorBO, AmazonFollowSellSuperiorBO> cunsumer) {
        List<AmazonFollowSellSuperiorBO> dest = new ArrayList<>(src.size());
        src.forEach(srcItem -> {
            AmazonFollowSellSuperiorBO destItem = new AmazonFollowSellSuperiorBO();
            destItem.setId(srcItem.getId());
            cunsumer.accept(srcItem, destItem);

            dest.add(destItem);
        });
        return dest;
    }

    /**
     * 根据success刊登途中设置该条数据状态
     * @param operationType
     * @param feedType
     * @param unitData
     * @param success 成功状态
     * @param isPublishEndNode 是刊登结束节点吗 false：不是， true：是
     */
    public void setFollowSellDataStatus(OperationType operationType, String feedType, AmazonFollowSellSuperiorBO unitData, boolean success, boolean isPublishEndNode) {
        switch (SpFeedType.of(feedType)){
            case POST_PRODUCT_DATA:
                AmazonFollowSellSuperiorBO updateProduct = null;
                if(OperationType.Update.equals(operationType)){
                    //刊登产品
                    updateProduct = generateUpdateData(unitData, (source, target) -> {
                        target.setLastUpdateDate(new Date());
                        //默认刊登失败
                        target.setDataStatus(FollowSellDataStatusEnum._3.getDataStatus());
                        //是刊登结束节点
                        if(isPublishEndNode){
                            //批量刊登并且产品刊登成功还是置为刊登中
                            if(success && source.isRightBatchFollow()){
                                target.setDataStatus(FollowSellDataStatusEnum._1.getDataStatus());
                            }else if(success){
                                //刊登产品成功
                                target.setDataStatus(FollowSellDataStatusEnum._2.getDataStatus());
                            }
                            if(source.isRightBatchFollow()){
                                unitData.getBatchFollowSuccessFeedType().put(feedType, success);
                            }
                            //设置第一次跟卖时间
                            target.setFirstFollowSellDate(source.getFirstFollowSellDate());
                            //如果是刊登跟卖设置下sellerSku,因为现在sellerSku是在刊登跟卖时候生成的
                            if (StringUtils.isNotBlank(source.getSellerSku())) {
                                target.setSellerSku(source.getSellerSku());
                            }
                        }else{
                            if(success){
                                target.setDataStatus(FollowSellDataStatusEnum._1.getDataStatus());
                                //设置第一次跟卖时间
                                target.setFirstFollowSellDate(source.getFirstFollowSellDate());
                                //如果是刊登跟卖设置下sellerSku,因为现在sellerSku是在刊登跟卖时候生成的
                                if (StringUtils.isNotBlank(source.getSellerSku())) {
                                    target.setSellerSku(source.getSellerSku());
                                }
                            }
                        }
                    });
                }
                else if(OperationType.Delete.equals(operationType)){
                    updateProduct = generateUpdateData(unitData, (source, target) -> {
                        target.setLastUpdateDate(new Date());
                        target.setDataStatus(FollowSellDataStatusEnum._8.getDataStatus());
                        if(isPublishEndNode){
                            if(success){
                                //删除产品成功
                                target.setDataStatus(FollowSellDataStatusEnum._7.getDataStatus());
                            }
                        }else{
                            if(success){
                                //下架删除过程中，设置下架中
                                target.setDataStatus(FollowSellDataStatusEnum._4.getDataStatus());
                            }
                        }
                    });
                }
                if(updateProduct != null){
                    amazonFollowSellService.update(updateProduct);
                }
                break;
            case POST_INVENTORY_AVAILABILITY_DATA:
                //上传等于0的库存（下架）
                AmazonFollowSellSuperiorBO updateInventory = null;
                if(unitData.getQuantity() == 0){
                    updateInventory = generateUpdateData(unitData, (source, target) -> {
                        target.setLastUpdateDate(new Date());
                        //默认下架失败
                        target.setDataStatus(FollowSellDataStatusEnum._6.getDataStatus());
                        if(isPublishEndNode){
                            if(success){
                                //下架成功
                                target.setQuantity(source.getQuantity());
                                target.setDataStatus(FollowSellDataStatusEnum._5.getDataStatus());
                                // 下架需要设置下架时间为当前时间，防止重复跟卖
                                long currentTime = System.currentTimeMillis();
                                target.setOffLineDate(source.getOffLineDate());
                                if (currentTime < source.getOffLineDate().getTime()) {
                                    target.setOffLineDate(new Timestamp(currentTime));
                                }
                            }
                        }else{
                            if(success){
                                target.setDataStatus(FollowSellDataStatusEnum._4.getDataStatus());
                            }
                        }
                    });
                }else{
                    if(isPublishEndNode){
                        updateInventory = generateUpdateData(unitData, (source, target) -> {
                            target.setLastUpdateDate(new Date());
                            target.setQuantity(source.getQuantity());
                        });
                        //是批量刊登的后续操作
                        if(unitData.isRightBatchFollow()){
                            unitData.getBatchFollowSuccessFeedType().put(feedType, success);
                            List<Boolean> result = unitData.getBatchFollowSuccessFeedType().values().stream().filter(boo -> boo).collect(Collectors.toList());
                            if(unitData.getBatchFollowSuccessFeedType().size() == 3){
                                if(result.size() == 3){
                                    updateInventory.setDataStatus(FollowSellDataStatusEnum._2.getDataStatus());
                                }else{
                                    updateInventory.setDataStatus(FollowSellDataStatusEnum._3.getDataStatus());
                                }
                            }
                        }
                    }
                }

                if(updateInventory!=null){
                    amazonFollowSellService.update(updateInventory);
                }
                break;
            case POST_PRODUCT_PRICING_DATA:
                AmazonFollowSellSuperiorBO updatePrice = null;
                if(isPublishEndNode){
                    updatePrice = generateUpdateData(unitData, (source, target) -> {
                        target.setLastUpdateDate(new Date());
                        target.setStandardPrice(source.getStandardPrice());
                    });
                    //是批量刊登的后续操作
                    if(unitData.isRightBatchFollow()){
                        unitData.getBatchFollowSuccessFeedType().put(feedType, success);
                        List<Boolean> result = unitData.getBatchFollowSuccessFeedType().values().stream().filter(boo -> boo).collect(Collectors.toList());
                        if(unitData.getBatchFollowSuccessFeedType().size() == 3){
                            if(result.size() == 3){
                                updatePrice.setDataStatus(FollowSellDataStatusEnum._2.getDataStatus());
                            }else{
                                updatePrice.setDataStatus(FollowSellDataStatusEnum._3.getDataStatus());
                            }
                        }
                    }
                }

                if(updatePrice!=null){
                    amazonFollowSellService.update(updatePrice);
                }
                break;
        }
    }
    private AmazonFollowSellSuperiorBO generateUpdateData(AmazonFollowSellSuperiorBO src,
                                                          BiConsumer<AmazonFollowSellSuperiorBO, AmazonFollowSellSuperiorBO> cunsumer) {
            AmazonFollowSellSuperiorBO destItem = new AmazonFollowSellSuperiorBO();
            destItem.setId(src.getId());
            cunsumer.accept(src, destItem);
        return destItem;
    }
}
