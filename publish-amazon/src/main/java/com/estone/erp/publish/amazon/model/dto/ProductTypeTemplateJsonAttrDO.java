package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-20 9:39
 */
@Data
public class ProductTypeTemplateJsonAttrDO {
    /**
     *
     */
    private Integer id;

    /**
     * 站点: all 全站点
     */
    private String site;

    /**
     * 分类类型
     */
    private String productType;

    /**
     * 必填属性
     */
    private String attributeName;

    /**
     * 必填属性值
     */
    private String attributeValue;

    /**
     * 1：运营配置 2: 自动配置（技术部配置）
     */
    private Integer type;

    /**
     * 适用属性类型 1:单体, 2:变体, 3:单体变体都适用
     */
    private Integer applicableAttributeType;

    /**
     * 属性适配类型 1  站点匹配 2 分类类型匹配 3 站点+分类类型匹配 4 通用属性 5 技术部处理
     */
    private Integer adapterType;

    /**
     * 扩展属性
     */
    private List<String> productTypes;

    private List<String> sites;

    private Boolean codeAdapter;

    /**
     * 创建人
     */
    private String createBy;


    private String scriptData;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 修改时间
     */
    private Timestamp updatedTime;


    public ProductTypeTemplateJsonAttrDO(ProductTypeTemplateJsonAttr bean) {
        BeanUtils.copyProperties(bean, this);
        String extraData = bean.getExtraData();
        if (StringUtils.isNotBlank(extraData)) {
            Map<String, List<String>> extraDataMap = JSON.parseObject(extraData, new TypeReference<>() {
            });
            this.productTypes = extraDataMap.get("productTypes");
            this.sites = extraDataMap.get("sites");
            if (CollectionUtils.isNotEmpty(this.sites)) {
                this.site = StringUtils.join(this.sites, ",");
            }
        }
    }
}
