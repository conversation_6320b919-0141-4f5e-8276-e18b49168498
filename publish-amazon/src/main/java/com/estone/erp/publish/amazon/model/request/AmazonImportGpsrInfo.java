package com.estone.erp.publish.amazon.model.request;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-09-18 上午9:52
 */
@Data
public class AmazonImportGpsrInfo {
    @ExcelProperty("店铺")
    private String accountNumber;

    @ExcelProperty("站点")
    private String site;

    @ExcelProperty("制造商中文名称")
    private String manufacturerCn;

    @ExcelProperty("制造商英文名称")
    private String manufacturerEn;

    @ExcelProperty("制造商地址")
    private String manufacturerAddress;

    @ExcelProperty("制造商邮箱")
    private String manufacturerEmail;

    @ExcelProperty("制造商电话")
    private String manufacturerTel;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("备注")
    private String remark;

}
