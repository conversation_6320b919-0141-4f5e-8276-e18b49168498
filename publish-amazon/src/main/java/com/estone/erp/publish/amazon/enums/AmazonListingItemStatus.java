package com.estone.erp.publish.amazon.enums;

import org.apache.commons.lang.StringUtils;

public enum AmazonListingItemStatus {
    // (Inactive:不可售 Incomplete:内容不完整 Active:在售) 三个状态数据都标记为在线
    // 不活跃 ： 库存为0的数据也会是不活跃
    Inactive("Inactive","不活跃"),
    // 内容不完整
    Incomplete("Incomplete","内容不完整"),
    // 在售
    Active("Active","在售"),
    ;

    AmazonListingItemStatus(String statusCode, String statusMsg){
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }
    private String statusCode;

    private String statusMsg;

    public String getStatusCode() {
        return statusCode;
    }

    public String getStatusMsg() {
        return statusMsg;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public void setStatusMsg(String statusMsg) {
        this.statusMsg = statusMsg;
    }

    public static String getStatusMsgByStatusCode(String statusCode) {
        if (StringUtils.isBlank(statusCode)) {
            return null;
        }
        for (AmazonListingItemStatus value : values()) {
            if (value.getStatusCode().equals(statusCode)) {
                return value.getStatusMsg();
            }
        }
        return null;
    }
}
