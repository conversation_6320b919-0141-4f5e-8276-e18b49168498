package com.estone.erp.publish.amazon.enums;

/**
 * Amazon算价规则类型
 * <AUTHOR>
 * @date 2021/12/14 9:31
 */
public enum AmazonCalcPriceRuleTypeEnum {
    COMMON(1, "公共配置"),
    SELF(2, "账号私有配置");

    private int code;
    private String name;

    private AmazonCalcPriceRuleTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
