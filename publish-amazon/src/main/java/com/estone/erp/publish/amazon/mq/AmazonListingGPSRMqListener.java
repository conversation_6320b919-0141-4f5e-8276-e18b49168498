package com.estone.erp.publish.amazon.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.mq.model.ListingGPSRMessage;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingGpsrInfoService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * amazon 在线列表修改GPSR AMAZON_UPDATE_GPSR_QUEUE
 *
 * <AUTHOR>
 * @date 2024-09-25 下午1:40
 */
@Slf4j
@Component
public class AmazonListingGPSRMqListener implements ChannelAwareMessageListener {

    @Autowired
    private IAmazonListingGpsrInfoService amazonListingGpsrInfoService;
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private AmazonPublishOperationLogService amazonOperateLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 获取消息体
        ListingGPSRMessage messageData = JSON.parseObject(new String(message.getBody()), ListingGPSRMessage.class);
        if (messageData == null || StringUtils.isBlank(messageData.getAsin()) || StringUtils.isBlank(messageData.getAccountNumber())) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        try {
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setFields(new String[]{"id", "accountNumber", "sonAsin", "parentAsin", "sellerSku"});
            request.setSonAsin(messageData.getAsin());
            request.setAccountNumber(messageData.getAccountNumber());
            request.setIsOnline(true);
            List<EsAmazonProductListing> amazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);

            if (CollectionUtils.isEmpty(amazonProductListing)) {
                log.error("AmazonListingGPSRMqListener: AmazonListingId: {} not found", messageData.getListingId());
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            EsAmazonProductListing listing = amazonProductListing.get(0);
            ApiResult<String> result = amazonListingGpsrInfoService.generateGpsrInfo(listing);
            if (result.isSuccess()) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                log.error("messageData: {},generateGpsrInfo error: {}", JSON.toJSONString(messageData), result.getErrorMsg());
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                recodeFailLog(messageData, listing.getAccountNumber(), listing.getSonAsin(), result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("messageData: {},Exception error: {}", JSON.toJSONString(messageData), e.getMessage(), e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            recodeFailLog(messageData, null, null, "Exception: " + e.getMessage());
        }
    }

    private void recodeFailLog(ListingGPSRMessage messageData, String accountNumber, String sonAsin, String errorMsg) {
        // 生成一条操作日志
        String listingId = messageData.getListingId();
        if (accountNumber == null) {
            accountNumber = listingId.substring(0, listingId.indexOf("_") - 1);
        }
        AmazonPublishOperationLog amazonPublishOperationLog = new AmazonPublishOperationLog();
        amazonPublishOperationLog.setOpType(AmazonQueues.AMAZON_UPDATE_GPSR_QUEUE);
        amazonPublishOperationLog.setModId(accountNumber);
        amazonPublishOperationLog.setObject(listingId);
        if (sonAsin != null) {
            amazonPublishOperationLog.setObject1(sonAsin);
        }
        amazonPublishOperationLog.setState(1);
        amazonPublishOperationLog.setPlatform(SaleChannel.CHANNEL_AMAZON);
        amazonPublishOperationLog.setMetaObj(JSON.toJSONString(messageData));
        amazonPublishOperationLog.setAfterObj(JSON.toJSONString(Map.of("errorMsg", errorMsg)));
        amazonPublishOperationLog.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        amazonOperateLogService.insert(amazonPublishOperationLog);
    }
}
