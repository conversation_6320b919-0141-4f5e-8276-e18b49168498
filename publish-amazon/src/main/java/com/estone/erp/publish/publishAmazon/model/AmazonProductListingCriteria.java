package com.estone.erp.publish.publishAmazon.model;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> amazon_product_listing
 * 2020-12-19 16:28:50
 */
public class AmazonProductListingCriteria extends AmazonProductListing {

    private List<String> sellerSkuList;

    private List<String> extraImagesList;

    public List<String> getExtraImagesList() {
        if (extraImagesList != null) {
            return extraImagesList;
        }

        if (StringUtils.isNotBlank(getExtraImages())) {
            extraImagesList = JSON.parseArray(getExtraImages(), String.class);
        }
        else {
            extraImagesList = new ArrayList<String>(0);
        }

        return extraImagesList;
    }

    public List<String> getSellerSkuList() {
        return sellerSkuList;
    }

    public void setSellerSkuList(List<String> sellerSkuList) {
        this.sellerSkuList = sellerSkuList;
    }

    private static final long serialVersionUID = 1L;

    public AmazonProductListingExample getExample() {
        AmazonProductListingExample example = new AmazonProductListingExample();
        AmazonProductListingExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (StringUtils.isNotBlank(this.getParentAsin())) {
            criteria.andParentAsinEqualTo(this.getParentAsin());
        }
        if (StringUtils.isNotBlank(this.getSonAsin())) {
            criteria.andSonAsinEqualTo(this.getSonAsin());
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (CollectionUtils.isNotEmpty(this.sellerSkuList)){
            criteria.andSellerSkuIn(this.sellerSkuList);
        }
        if (StringUtils.isNotBlank(this.getMainSku())) {
            criteria.andMainSkuEqualTo(this.getMainSku());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (this.getSkuDataSource() != null) {
            criteria.andSkuDataSourceEqualTo(this.getSkuDataSource());
        }
        if (StringUtils.isNotBlank(this.getItemStatus())) {
            criteria.andItemStatusEqualTo(this.getItemStatus());
        }
        if (this.getIsOnline() != null) {
            criteria.andIsOnlineEqualTo(this.getIsOnline());
        }
        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameEqualTo(this.getName());
        }
        if (StringUtils.isNotBlank(this.getItemName())) {
            criteria.andItemNameEqualTo(this.getItemName());
        }
        if (StringUtils.isNotBlank(this.getItemDescription())) {
            criteria.andItemDescriptionEqualTo(this.getItemDescription());
        }
        if (StringUtils.isNotBlank(this.getInfringementWord())) {
            criteria.andInfringementWordEqualTo(this.getInfringementWord());
        }
        if (StringUtils.isNotBlank(this.getForbidChannel())) {
            criteria.andForbidChannelEqualTo(this.getForbidChannel());
        }
        if (StringUtils.isNotBlank(this.getSkuStatus())) {
            criteria.andSkuStatusEqualTo(this.getSkuStatus());
        }
        if (StringUtils.isNotBlank(this.getTagCodes())) {
            criteria.andTagCodesEqualTo(this.getTagCodes());
        }
        if (StringUtils.isNotBlank(this.getTagNames())) {
            criteria.andTagNamesEqualTo(this.getTagNames());
        }
        if (StringUtils.isNotBlank(this.getSpecialGoodsCode())) {
            criteria.andSpecialGoodsCodeEqualTo(this.getSpecialGoodsCode());
        }
        if (StringUtils.isNotBlank(this.getSpecialGoodsName())) {
            criteria.andSpecialGoodsNameEqualTo(this.getSpecialGoodsName());
        }
        if (StringUtils.isNotBlank(this.getItemIsMarketplace())) {
            criteria.andItemIsMarketplaceEqualTo(this.getItemIsMarketplace());
        }
        if (StringUtils.isNotBlank(this.getItemCondition())) {
            criteria.andItemConditionEqualTo(this.getItemCondition());
        }
        if (StringUtils.isNotBlank(this.getZshopCategory())) {
            criteria.andZshopCategoryEqualTo(this.getZshopCategory());
        }
        if (StringUtils.isNotBlank(this.getProductId())) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getMainImage())) {
            criteria.andMainImageEqualTo(this.getMainImage());
        }
        if (StringUtils.isNotBlank(this.getSampleImage())) {
            criteria.andSampleImageEqualTo(this.getSampleImage());
        }
        if (StringUtils.isNotBlank(this.getExtraImages())) {
            criteria.andExtraImagesEqualTo(this.getExtraImages());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getQuantity() != null) {
            criteria.andQuantityEqualTo(this.getQuantity());
        }
        if (this.getSaleQuantity() != null) {
            criteria.andSaleQuantityEqualTo(this.getSaleQuantity());
        }
        if (this.getSalePrice() != null) {
            criteria.andSalePriceEqualTo(this.getSalePrice());
        }
        if (this.getSaleStartDate() != null) {
            criteria.andSaleStartDateEqualTo(this.getSaleStartDate());
        }
        if (this.getSaleEndDate() != null) {
            criteria.andSaleEndDateEqualTo(this.getSaleEndDate());
        }
        if (this.getLowestPrice() != null) {
            criteria.andLowestPriceEqualTo(this.getLowestPrice());
        }
        if (StringUtils.isNotBlank(this.getIsPopular())) {
            criteria.andIsPopularEqualTo(this.getIsPopular());
        }
        if (this.getIsFollowSellDelete() != null) {
            criteria.andIsFollowSellDeleteEqualTo(this.getIsFollowSellDelete());
        }
        if (StringUtils.isNotBlank(this.getFollowSaleFlag())) {
            criteria.andFollowSaleFlagEqualTo(this.getFollowSaleFlag());
        }
        if (StringUtils.isNotBlank(this.getListingId())) {
            criteria.andListingIdEqualTo(this.getListingId());
        }
        if (StringUtils.isNotBlank(this.getSkuLifeCyclePhase())) {
            criteria.andSkuLifeCyclePhaseEqualTo(this.getSkuLifeCyclePhase());
        }
        if (StringUtils.isNotBlank(this.getMerchantShippingGroup())) {
            criteria.andMerchantShippingGroupEqualTo(this.getMerchantShippingGroup());
        }
        if (this.getShippingCost() != null) {
            criteria.andShippingCostEqualTo(this.getShippingCost());
        }
        if (this.getTotalPrice() != null) {
            criteria.andTotalPriceEqualTo(this.getTotalPrice());
        }
        if (StringUtils.isNotBlank(this.getCategoryId())) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (StringUtils.isNotBlank(this.getCategoryCnName())) {
            criteria.andCategoryCnNameEqualTo(this.getCategoryCnName());
        }
        if (this.getRelationTemplateId() != null) {
            criteria.andRelationTemplateIdEqualTo(this.getRelationTemplateId());
        }
        if (this.getAutoUpdateMsgDate() != null) {
            criteria.andAutoUpdateMsgDateEqualTo(this.getAutoUpdateMsgDate());
        }
        if (this.getLastAdjustPriceDate() != null) {
            criteria.andLastAdjustPriceDateEqualTo(this.getLastAdjustPriceDate());
        }
        if (StringUtils.isNotBlank(this.getReportOpenDate())) {
            criteria.andReportOpenDateEqualTo(this.getReportOpenDate());
        }
        if (this.getOpenDate() != null) {
            criteria.andOpenDateEqualTo(this.getOpenDate());
        }
        if (this.getFirstOpenDate() != null) {
            criteria.andFirstOpenDateEqualTo(this.getFirstOpenDate());
        }
        if (this.getOfflineDate() != null) {
            criteria.andOfflineDateEqualTo(this.getOfflineDate());
        }
        if (this.getFirstOfflineDate() != null) {
            criteria.andFirstOfflineDateEqualTo(this.getFirstOfflineDate());
        }
        if (this.getSyncDate() != null) {
            criteria.andSyncDateEqualTo(this.getSyncDate());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(this.getUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1EqualTo(this.getAttribute1());
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }
        if (StringUtils.isNotBlank(this.getAttribute6())) {
            criteria.andAttribute6EqualTo(this.getAttribute6());
        }
        if (StringUtils.isNotBlank(this.getAttribute7())) {
            criteria.andAttribute7EqualTo(this.getAttribute7());
        }
        if (StringUtils.isNotBlank(this.getIssuesSeverity())) {
            criteria.andIssuesSeverityEqualTo(this.getIssuesSeverity());
        }
        if (StringUtils.isNotBlank(this.getItemSummariesStastus())) {
            criteria.andItemSummariesStastusEqualTo(this.getItemSummariesStastus());
        }
        if (StringUtils.isNotBlank(this.getConditionType())) {
            criteria.andConditionTypeEqualTo(this.getConditionType());
        }
        if (this.getIteamLastUpdatedDate() != null) {
            criteria.andIteamLastUpdatedDateEqualTo(this.getIteamLastUpdatedDate());
        }
        if (this.getItemType() != null) {
            criteria.andItemTypeEqualTo(this.getItemType());
        }
        if (StringUtils.isNotBlank(this.getChildAsins())) {
            criteria.andChildAsinsEqualTo(this.getChildAsins());
        }
        if (this.getPackageQuantity() != null) {
            criteria.andPackageQuantityEqualTo(this.getPackageQuantity());
        }
        return example;
    }
}