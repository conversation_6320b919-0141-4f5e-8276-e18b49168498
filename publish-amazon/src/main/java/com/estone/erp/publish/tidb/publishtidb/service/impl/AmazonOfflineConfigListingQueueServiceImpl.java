package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.componet.download.AmazonDownloadTypeEnums;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinSalesVolume;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigContext;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.queue.AmazonOfflineConfigListingQueueVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.queue.AmazonOfflineQueueSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.SaleCountRuleConfigDO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonOfflineConfigListingQueueMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigAccountReport;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigAccountReportService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigDataStatisticsService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigListingQueueService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigService;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * amazon下架配置链接下架队列 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
public class AmazonOfflineConfigListingQueueServiceImpl extends ServiceImpl<AmazonOfflineConfigListingQueueMapper, AmazonOfflineConfigListingQueue> implements AmazonOfflineConfigListingQueueService {
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private ExcelDownloadLogService excelDownloadLogService;
    @Resource
    private AmazonOfflineConfigService amazonOfflineConfigService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonProductListingService productListingService;
    @Resource
    private AmazonOfflineConfigAccountReportService amazonOfflineConfigAccountReportService;
    @Autowired
    private AmazonOfflineConfigDataStatisticsService amazonOfflineConfigDataStatisticsService;


    @Override
    public IPage<AmazonOfflineConfigListingQueueVO> search(AmazonOfflineQueueSearchDTO searchParam) {
        // 销售仅可查看自己的数据，组长、主管仅可查看自己和下级数据，平台销售主管和超管可查看全部数据
        isAuth(searchParam);
        LambdaQueryWrapper<AmazonOfflineConfigListingQueue> wrapper = buildSearchWrapper(searchParam);
        Page<AmazonOfflineConfigListingQueue> page = new Page<>(searchParam.getPageIndex(), searchParam.getPageSize());
        IPage<AmazonOfflineConfigListingQueue> iPage = page(page, wrapper);

        // 扩展信息
        List<AmazonOfflineConfigListingQueueVO> rows = transferAsQueueVOs(iPage.getRecords());
        IPage<AmazonOfflineConfigListingQueueVO> iPageResult = new Page<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        iPageResult.setPages(iPage.getPages());
        iPageResult.setRecords(rows);
        return iPageResult;
    }

    @Override
    public ApiResult<String> download(AmazonOfflineQueueSearchDTO searchDTO) {
        try {

            searchDTO.setPageIndex(1);
            searchDTO.setPageSize(1);

            IPage<AmazonOfflineConfigListingQueueVO> iPage = search(searchDTO);

            int total = (int) iPage.getTotal();

            if (total == 0) {
                return ApiResult.newError("没有数据，无法下载");
            }
            if (total > 500000) {
                return ApiResult.newError("超出最大下载数量限制，无法下载");
            }
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            downloadLog.setType(AmazonDownloadTypeEnums.OFFLINE_CONFIG_LISTING_QUEUE.getType());
            downloadLog.setQueryCondition(JSON.toJSONString(searchDTO));
            downloadLog.setDownloadCount(total);
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            // 发送队列
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_AMAZON, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            log.error("导出异常:", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    public List<String> aggregateTotalTable(Integer configId, LocalDateTime latestTime) {
        return baseMapper.aggregateTotalTable(configId, latestTime);
    }

    @Override
    public void executeOfflineConfigLink(AmazonOfflineConfigMessage message) {
        OfflineConfigEnums.OfflineType offlineType = OfflineConfigEnums.OfflineType.getByName(message.getOfflineType());
        if (offlineType == null) {
            return;
        }

        AmazonOfflineEnums.Type taskTypeEnum = AmazonOfflineEnums.Type.getCodeType(offlineType.getOfflineTypeCode());
        if (taskTypeEnum == null) {
            return;
        }
        Integer configId = message.getConfigId();
        ApiResult<AmazonOfflineConfigVO> amazonOfflineConfigVOApiResult = amazonOfflineConfigService.editConfig(configId);
        if (!amazonOfflineConfigVOApiResult.isSuccess()) {
            return;
        }
        AmazonOfflineConfigVO configVO = amazonOfflineConfigVOApiResult.getResult();
        if (OfflineConfigEnums.ConfigStatus.DISABLE.isTrue(configVO.getConfig().getStatus())) {
            return;
        }
        // 下架排除店铺
        String excludeAccounts = configVO.getConfig().getExcludeAccounts();
        if (StringUtils.isNotEmpty(excludeAccounts) && excludeAccounts.contains(message.getAccountNumber())) {
            excludeAccountsQueue(message, configVO);
            return;
        }


        SaleCountRuleConfigDO saleCountRule = configVO.getSaleCountRule();
        List<Predicate<AmazonAsinSaleCountDO>> predicateList = amazonOfflineConfigService.transferToSaleCountCompareRules(saleCountRule);

        AmazonOfflineConfigContext context = new AmazonOfflineConfigContext(message, configVO, predicateList);


        // 查询店铺所有可下架的链接
        LambdaQueryWrapper<AmazonOfflineConfigListingQueue> pageWrapper = new LambdaQueryWrapper<>();
        pageWrapper.eq(AmazonOfflineConfigListingQueue::getRuleId, configId)
                .eq(AmazonOfflineConfigListingQueue::getAccountNumber, message.getAccountNumber())
                .eq(AmazonOfflineConfigListingQueue::getTaskType, offlineType.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, message.getConfirmedTime())
                .lt(AmazonOfflineConfigListingQueue::getSchedulingTime, message.getScheduleTime());
        List<Map<Object, Object>> tidbPageMetaMap = baseMapper.getTidbPageMetaMap(pageWrapper);
        List<TidbPageMeta<Long>> pageMetaList = TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);

        int total = 0;
        for (TidbPageMeta<Long> longTidbPageMeta : pageMetaList) {
            try {
                Long offlineConfigLink = executeOfflineLink(longTidbPageMeta, context, taskTypeEnum);
                total += offlineConfigLink;
            } catch (Exception e) {
                log.error("执行下架任务异常: message:{}, error:", message, e);
            }
        }

        // 更新记录表状态信息
        LambdaUpdateWrapper<AmazonOfflineConfigAccountReport> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmazonOfflineConfigAccountReport::getConfigId, configId);
        updateWrapper.eq(AmazonOfflineConfigAccountReport::getAccountNumber, message.getAccountNumber());
        updateWrapper.eq(AmazonOfflineConfigAccountReport::getStatisticsDate, message.getConfirmedTime());
        updateWrapper.set(AmazonOfflineConfigAccountReport::getStatus, 3);
        amazonOfflineConfigAccountReportService.update(updateWrapper);

        // 检查下架数据记录表状态流转，更新执行状态
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());
        log.info("执行下架任务完成, 总数:{}, message:{}", total, message);
    }

    private Long executeOfflineLink(TidbPageMeta<Long> longTidbPageMeta, AmazonOfflineConfigContext context, AmazonOfflineEnums.Type taskTypeEnum) {
        AmazonOfflineConfigMessage message = context.getMessage();
        AmazonOfflineConfigVO config = context.getConfig();
        List<Predicate<AmazonAsinSaleCountDO>> asinSaleCountComparePublishRules = context.getPredicateList();

        Integer offlineTypeCode = config.getConfig().getType();

        LambdaQueryWrapper<AmazonOfflineConfigListingQueue> pageWrapper = new LambdaQueryWrapper<>();
        pageWrapper.eq(AmazonOfflineConfigListingQueue::getRuleId, message.getConfigId())
                .eq(AmazonOfflineConfigListingQueue::getAccountNumber, message.getAccountNumber())
                .eq(AmazonOfflineConfigListingQueue::getTaskType, offlineTypeCode)
                .eq(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, message.getConfirmedTime())
                .lt(AmazonOfflineConfigListingQueue::getSchedulingTime, message.getScheduleTime())
                .between(AmazonOfflineConfigListingQueue::getId, longTidbPageMeta.getStartKey(), longTidbPageMeta.getEndKey());

        List<AmazonOfflineConfigListingQueue> queueList = baseMapper.selectList(pageWrapper);
        if (CollectionUtils.isEmpty(queueList)) {
            return 0L;
        }

        // asin 销量判断
        removeHasTodaySalesAsinsOffLinks(queueList, asinSaleCountComparePublishRules);
        if (CollectionUtils.isEmpty(queueList)) {
            return 0L;
        }

        Map<String, Long> offLinkIdMap = new HashMap<>();
        List<AmazonProductListing> offlineListings = new ArrayList<>();
        for (AmazonOfflineConfigListingQueue offLinkLog : queueList) {
            AmazonProductListing listing = productListingService.getOfflineProductListing(offLinkLog.getSellerSku(), offLinkLog.getAccountNumber(), offLinkLog.getSite());
            if (listing == null || BooleanUtils.isFalse(listing.getIsOnline())) {
                isOffLinked(offLinkLog, listing);
                continue;
            }
            offLinkIdMap.put(listing.getAccountNumber() + "_" + listing.getSellerSku(), offLinkLog.getId());
            offlineListings.add(listing);
        }
        // 执行下架请求
        if (CollectionUtils.isNotEmpty(offlineListings)) {
            // 下架成功回调, 更新offLink的状态、信息、处理报告id
            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
            deleteAmazonListingDto.setAmazonOfflineEnumType(taskTypeEnum);
            BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer = deleteAmazonListingCallBack(offLinkIdMap);
            DeleteAmazonListingUtils.systemBatchRetireProduct(offlineListings, deleteAmazonListingDto, biConsumer);
            return (long) offlineListings.size();

        }
        return 0L;
    }

    private void excludeAccountsQueue(AmazonOfflineConfigMessage message, AmazonOfflineConfigVO configVO) {
        baseMapper.update(null, new LambdaUpdateWrapper<AmazonOfflineConfigListingQueue>()
                .eq(AmazonOfflineConfigListingQueue::getAccountNumber, message.getAccountNumber())
                .eq(AmazonOfflineConfigListingQueue::getRuleId, configVO.getConfig().getId())
                .eq(AmazonOfflineConfigListingQueue::getTaskType, configVO.getConfig().getType())
                .eq(AmazonOfflineConfigListingQueue::getSchedulingTime, message.getConfirmedTime())
                .lt(AmazonOfflineConfigListingQueue::getSchedulingTime, message.getScheduleTime())
                .set(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.OFFLINE_INTERCEPT.getCode())
                .set(AmazonOfflineConfigListingQueue::getRemark, "配置排除下架店铺，不下架!")
                .set(AmazonOfflineConfigListingQueue::getOfflineTime, LocalDateTime.now())
                .set(AmazonOfflineConfigListingQueue::getUpdatedTime, LocalDateTime.now()));
    }

    private void isOffLinked(AmazonOfflineConfigListingQueue offLinkLog, AmazonProductListing listing) {
        if (listing == null) {
            return;
        }
        offLinkLog.setStatus(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.getCode());
        // 记录错误信息
        Map<String, Object> addData = new HashMap<>();
        addData.put("message", "链接已下架");
        if (StringUtils.isNotEmpty(listing.getAttribute3())) {
            addData.put("delRemark", listing.getAttribute3());
            offLinkLog.setRemark("链接已下架," + listing.getAttribute3());
        }
        addExtraData(offLinkLog, addData);
        baseMapper.update(null, new LambdaUpdateWrapper<AmazonOfflineConfigListingQueue>()
                .eq(AmazonOfflineConfigListingQueue::getId, offLinkLog.getId())
                .set(AmazonOfflineConfigListingQueue::getRemark, offLinkLog.getRemark())
                .set(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.isTrue(offLinkLog.getStatus()), AmazonOfflineConfigListingQueue::getOfflineTime, LocalDateTime.now())
                .set(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.isTrue(offLinkLog.getStatus()), AmazonOfflineConfigListingQueue::getIsOff, true)
                .set(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.getCode())
                .set(AmazonOfflineConfigListingQueue::getUpdatedTime, LocalDateTime.now())
                .set(AmazonOfflineConfigListingQueue::getExtraData, offLinkLog.getExtraData()));
    }


    /**
     * 下架成功回调, 更新offLink的状态、信息、处理报告id
     */
    private BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> deleteAmazonListingCallBack(Map<String, Long> offLinkIdMap) {
        BiConsumer<AmazonProductListing, ApiResult<ListingsItemSubmissionResponse>> biConsumer = (listing, apiResult) -> {
            String key = listing.getAccountNumber() + "_" + listing.getSellerSku();
            Long offLinkId = offLinkIdMap.get(key);
            Long businessId = apiResult.getBusinessId();
            log.debug("callback:{},offLinkId:{},apiResult:{}", key, offLinkId, JSON.toJSONString(apiResult));
            if (offLinkId == null) {
                return;
            }
            AmazonOfflineConfigListingQueue offLinkLog = baseMapper.selectById(offLinkId);
            if (offLinkLog == null) {
                return;
            }
            // 更新状态
            if (apiResult.isSuccess()) {
                offLinkLog.setStatus(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.getCode());
            } else {
                if (businessId != null && businessId == 1001L) {
                    offLinkLog.setStatus(OfflineQueueEnums.QueueStatusType.OFFLINE_INTERCEPT.getCode());
                } else {
                    offLinkLog.setStatus(OfflineQueueEnums.QueueStatusType.OFFLINE_FAILURE.getCode());
                }
            }
            // 记录错误信息
            Map<String, Object> addData = new HashMap<>();
            if (apiResult.getBusinessId() != null) {
                addData.put("reportId", apiResult.getBusinessId());
            }
            if (StringUtils.isNotEmpty(apiResult.getErrorMsg())) {
                addData.put("message", apiResult.getErrorMsg());
                offLinkLog.setRemark(apiResult.getErrorMsg());
            }
            addExtraData(offLinkLog, addData);
            baseMapper.update(null, new LambdaUpdateWrapper<AmazonOfflineConfigListingQueue>()
                    .eq(AmazonOfflineConfigListingQueue::getId, offLinkLog.getId())
                    .set(AmazonOfflineConfigListingQueue::getStatus, offLinkLog.getStatus())
                    .set(AmazonOfflineConfigListingQueue::getRemark, offLinkLog.getRemark())
                    .set(AmazonOfflineConfigListingQueue::getUpdatedTime, LocalDateTime.now())
                    .set(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.isTrue(offLinkLog.getStatus()), AmazonOfflineConfigListingQueue::getOfflineTime, LocalDateTime.now())
                    .set(OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.isTrue(offLinkLog.getStatus()), AmazonOfflineConfigListingQueue::getIsOff, true)
                    .set(AmazonOfflineConfigListingQueue::getExtraData, offLinkLog.getExtraData()));

        };
        return biConsumer;
    }


    @Override
    public List<AmazonOfflineConfigListingQueue> addListingToOffLinkQueue(List<EsAmazonProductListing> listings, AmazonOfflineConfigVO config, BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func) {
        if (CollectionUtils.isEmpty(listings)) {
            return null;
        }
        return listings.stream().map(listing -> {
            AmazonOfflineConfigListingQueue queue = new AmazonOfflineConfigListingQueue();
            queue.setRuleId(config.getConfig().getId());
            queue.setRuleName(config.getConfig().getRuleName());
            queue.setTaskType(config.getConfig().getType());
            queue.setAccountNumber(listing.getAccountNumber());
            queue.setSite(listing.getSite());
            queue.setStatus(0);
            queue.setIsOff(false);
            queue.setParentAsin(listing.getParentAsin());
            queue.setAsin(listing.getSonAsin());
            queue.setSpu(listing.getMainSku());
            queue.setArticleNumber(listing.getArticleNumber());
            queue.setSellerSku(listing.getSellerSku());
            Date openDate = Optional.ofNullable(listing.getOpenDate()).orElseGet(() -> Optional.ofNullable(listing.getFirstOpenDate()).orElseGet(Date::new));
            queue.setOpenTime(LocalDateTimeUtil.of(openDate));
            queue.setCreatedTime(LocalDateTime.now());
            queue.setUpdatedTime(LocalDateTime.now());
            // 执行扩展
            if (func != null) {
                func.accept(listing, queue);
            }
            return queue;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getAccountOffCountByStatus(String accountNumber, LocalDateTime confirmedTime, LocalDateTime schedulingTime, int status, int code) {
        LocalDateTime truncatedDateTime = schedulingTime.truncatedTo(ChronoUnit.SECONDS);
        LambdaQueryWrapper<AmazonOfflineConfigListingQueue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonOfflineConfigListingQueue::getAccountNumber, accountNumber)
                .eq(AmazonOfflineConfigListingQueue::getStatus, status)
                .eq(AmazonOfflineConfigListingQueue::getTaskType, code)
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, confirmedTime)
                .eq(AmazonOfflineConfigListingQueue::getSchedulingTime, truncatedDateTime);
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public void updateOffLinkStatusOrderOpenTimeLimit(String accountNumber, LocalDateTime confirmedTime, LocalDateTime schedulingTime, int taskTaskCode, int status, Integer limit) {
        LambdaUpdateWrapper<AmazonOfflineConfigListingQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmazonOfflineConfigListingQueue::getAccountNumber, accountNumber)
                .eq(AmazonOfflineConfigListingQueue::getTaskType, taskTaskCode)
                .eq(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, confirmedTime)
                .eq(AmazonOfflineConfigListingQueue::getSchedulingTime, schedulingTime)
                .set(AmazonOfflineConfigListingQueue::getStatus, status)
                .last("order by open_time asc LIMIT " + limit);
        baseMapper.update(null, updateWrapper);
    }

    private void isAuth(AmazonOfflineQueueSearchDTO searchParam) {
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(searchParam.getAccounts(),
                searchParam.getSalesSupervisorList(), searchParam.getSaleTeamLeaderList(), searchParam.getSaleManList(), SaleChannel.CHANNEL_AMAZON);
        searchParam.setAccounts(currentUserPermission);
    }

    private List<AmazonOfflineConfigListingQueueVO> transferAsQueueVOs(List<AmazonOfflineConfigListingQueue> queues) {
        List<String> accounts = queues.stream().map(AmazonOfflineConfigListingQueue::getAccountNumber).collect(Collectors.toList());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accounts, SaleChannel.CHANNEL_AMAZON);
        return queues.stream().map(queue -> {
            AmazonOfflineConfigListingQueueVO queueVO = new AmazonOfflineConfigListingQueueVO();
            BeanUtils.copyProperties(queue, queueVO);
            queueVO.setOffStatusName(Optional.ofNullable(queue.getIsOff())
                    .map(isOff -> isOff ? ResultStatusEnum.RESULT_SUCCESS.getStatusMsgCn() : ResultStatusEnum.RESULT_FAIL.getStatusMsgCn()).orElse(null));
            queueVO.setTaskTypeName(Optional.ofNullable(OfflineConfigEnums.OfflineType.getByCode(queue.getTaskType())).map(OfflineConfigEnums.OfflineType::getDesc).orElse(null));
            Triple<String, String, String> triple = saleSuperiorMap.get(queue.getAccountNumber());
            if (triple != null) {
                queueVO.setSaleMan(triple.getLeft());
                queueVO.setSaleTeamLeader(triple.getMiddle());
                queueVO.setSalesSupervisor(triple.getRight());
            }
            String extraData = queue.getExtraData();
            if (StringUtils.isNotBlank(extraData)) {
                JSONObject jsonObject = JSON.parseObject(extraData);
                JSONObject saleCountObject = jsonObject.getJSONObject("saleCount");
                if (saleCountObject != null) {
                    AmazonAsinSaleCountDO amazonAsinSaleCountDO = JSON.parseObject(saleCountObject.toJSONString(), AmazonAsinSaleCountDO.class);
                    queueVO.setSalesTotal24hCount(amazonAsinSaleCountDO.getSale_24_count());
                    queueVO.setSalesTotal7dCount(amazonAsinSaleCountDO.getSale_7d_count());
                    queueVO.setSalesTotal14dCount(amazonAsinSaleCountDO.getSale_14d_count());
                    queueVO.setSalesTotal30dCount(amazonAsinSaleCountDO.getSale_30d_count());
                }

            }
            return queueVO;
        }).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<AmazonOfflineConfigListingQueue> buildSearchWrapper(AmazonOfflineQueueSearchDTO searchParam) {
        LambdaQueryWrapper<AmazonOfflineConfigListingQueue> wrapper = new LambdaQueryWrapper<>();

        if (CollectionUtils.isNotEmpty(searchParam.getIds())) {
            wrapper.in(AmazonOfflineConfigListingQueue::getId, searchParam.getIds());
        } else {
            wrapper.like(StringUtils.isNotBlank(searchParam.getRuleName()), AmazonOfflineConfigListingQueue::getRuleName, searchParam.getRuleName())
                    .in(CollectionUtils.isNotEmpty(searchParam.getAccounts()), AmazonOfflineConfigListingQueue::getAccountNumber, searchParam.getAccounts())
                    .in(CollectionUtils.isNotEmpty(searchParam.getTypeList()), AmazonOfflineConfigListingQueue::getTaskType, searchParam.getTypeList())
                    .in(StringUtils.isNotBlank(searchParam.getParentAsinStr()), AmazonOfflineConfigListingQueue::getParentAsin, CommonUtils.splitList(searchParam.getParentAsinStr(), ","))
                    .in(StringUtils.isNotBlank(searchParam.getAsinStr()), AmazonOfflineConfigListingQueue::getAsin, CommonUtils.splitList(searchParam.getAsinStr(), ","))
                    .in(StringUtils.isNotBlank(searchParam.getSellerSkuStr()), AmazonOfflineConfigListingQueue::getSellerSku, CommonUtils.splitList(searchParam.getSellerSkuStr(), ","))
                    .in(StringUtils.isNotBlank(searchParam.getSpuStr()), AmazonOfflineConfigListingQueue::getSpu, CommonUtils.splitList(searchParam.getSpuStr(), ","))
                    .in(CollectionUtils.isNotEmpty(searchParam.getRuleIdList()), AmazonOfflineConfigListingQueue::getRuleId, searchParam.getRuleIdList())
                    .in(StringUtils.isNotBlank(searchParam.getSkuStr()), AmazonOfflineConfigListingQueue::getArticleNumber, CommonUtils.splitList(searchParam.getSkuStr(), ","))
                    .between(StringUtils.isNotBlank(searchParam.getOpenTimeFrom()) && StringUtils.isNotBlank(searchParam.getOpenTimeTo()),
                            AmazonOfflineConfigListingQueue::getOpenTime, searchParam.getOpenTimeFrom(), searchParam.getOpenTimeTo())
                    .eq(CollectionUtils.isNotEmpty(searchParam.getOffStatusList()) && searchParam.getOffStatusList().contains(1), AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.getCode())
                    .gt(CollectionUtils.isNotEmpty(searchParam.getOffStatusList()) && searchParam.getOffStatusList().contains(0), AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.OFFLINE_STATUS.getCode())
                    .between(StringUtils.isNotBlank(searchParam.getCreatedTimeFrom()) && StringUtils.isNotBlank(searchParam.getCreatedTimeTo()),
                            AmazonOfflineConfigListingQueue::getCreatedTime, searchParam.getCreatedTimeFrom(), searchParam.getCreatedTimeTo())
                    .eq(StringUtils.isNotBlank(searchParam.getConfirmedTime()), AmazonOfflineConfigListingQueue::getStatisticsDate, searchParam.getConfirmedTime())
                    .between(StringUtils.isNotBlank(searchParam.getOfflineTimeFrom()) && StringUtils.isNotBlank(searchParam.getOfflineTimeTo()),
                            AmazonOfflineConfigListingQueue::getOfflineTime, searchParam.getOfflineTimeFrom(), searchParam.getOfflineTimeTo());

            if (CollectionUtils.isNotEmpty(searchParam.getStatusList())) {
                wrapper.in(AmazonOfflineConfigListingQueue::getStatus, searchParam.getStatusList());
            } else {
                wrapper.ge(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode());
            }
            wrapper.orderByDesc(StringUtils.isBlank(searchParam.getSortField()), AmazonOfflineConfigListingQueue::getCreatedTime);
        }
        return wrapper;
    }


    /**
     * 移除今日有销量的亚马逊Asin
     *
     * @param offLinkList               待下架列表
     * @param asinSaleCountCompareRules 销量规则
     */
    public void removeHasTodaySalesAsinsOffLinks(List<AmazonOfflineConfigListingQueue> offLinkList, List<Predicate<AmazonAsinSaleCountDO>> asinSaleCountCompareRules) {
        List<String> sonAsinList = offLinkList.stream().map(AmazonOfflineConfigListingQueue::getAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        ApiResult<List<AsinSalesVolume>> apiResult = OrderUtils.getAmazonAsinTodaySaleVolume(sonAsinList);
        if (!apiResult.isSuccess() || org.apache.commons.collections4.CollectionUtils.isEmpty(apiResult.getResult())) {
            offLinkList.removeIf(listing -> StringUtils.isBlank(listing.getAsin()) || sonAsinList.contains(listing.getAsin()));
            log.error("获取亚马逊asin销量异常：size:{},{},{}", offLinkList.size(), StringUtils.join(sonAsinList, ","), apiResult.getErrorMsg());
            return;
        }

        // 在线列表asin销量
        Map<String, AmazonAsinSaleCountDO> asinSaleCountDOMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        // 合并订单今日销量
        List<AsinSalesVolume> asinSalesVolumes = apiResult.getResult();
        asinSalesVolumes.forEach(asinSalesVolume -> {
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = asinSaleCountDOMap.get(asinSalesVolume.getAsin());
            if (amazonAsinSaleCountDO == null) {
                return;
            }
            amazonAsinSaleCountDO.setSale_today_count(Optional.ofNullable(asinSalesVolume.getSalesVolume()).orElse(0));
        });


        offLinkList.removeIf(listing -> {
            AmazonAsinSaleCountDO asinSaleCountDO = asinSaleCountDOMap.get(listing.getAsin());
            if (asinSaleCountDO == null) {
                return false;
            }
            boolean match = asinSaleCountCompareRules.stream().allMatch(rule -> rule.test(asinSaleCountDO));
            // 销量不符合规则 或 今日销量大于0 则不下架
            if (!match || asinSaleCountDO.getSale_today_count() > 0) {
                addExtraData(listing, Map.of("message", asinSaleCountDO + "有销量，不下架!"));
                baseMapper.update(null, new LambdaUpdateWrapper<AmazonOfflineConfigListingQueue>()
                        .eq(AmazonOfflineConfigListingQueue::getId, listing.getId())
                        .set(AmazonOfflineConfigListingQueue::getStatus, 4)
                        .set(AmazonOfflineConfigListingQueue::getRemark, "当天有出单，不下架!")
                        .set(AmazonOfflineConfigListingQueue::getOfflineTime, LocalDateTime.now())
                        .set(AmazonOfflineConfigListingQueue::getUpdatedTime, LocalDateTime.now())
                        .set(AmazonOfflineConfigListingQueue::getExtraData, listing.getExtraData()));
            }
            return !match || asinSaleCountDO.getSale_today_count() > 0;
        });
    }

    public void addExtraData(AmazonOfflineConfigListingQueue offLinkLog, Map<String, Object> addData) {
        if (MapUtils.isEmpty(addData)) {
            return;
        }
        String extraDataStr = offLinkLog.getExtraData();
        if (StringUtils.isEmpty(extraDataStr)) {
            offLinkLog.setExtraData(JSON.toJSONString(addData));
            return;
        }
        JSONObject extraDataJson = JSON.parseObject(extraDataStr);
        extraDataJson.putAll(addData);
        offLinkLog.setExtraData(JSON.toJSONString(extraDataJson));
    }
}
