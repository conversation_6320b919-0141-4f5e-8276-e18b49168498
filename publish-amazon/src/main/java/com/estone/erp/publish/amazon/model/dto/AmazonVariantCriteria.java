package com.estone.erp.publish.amazon.model.dto;

import com.estone.erp.publish.amazon.model.AmazonVariant;
import com.estone.erp.publish.amazon.model.AmazonVariantExample;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> amazon_variant
 * 2019-08-13 09:36:45
 */
public class AmazonVariantCriteria extends AmazonVariant {
    private static final long serialVersionUID = 1L;

    public AmazonVariantExample getExample() {
        AmazonVariantExample example = new AmazonVariantExample();
        AmazonVariantExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getItemName())) {
            criteria.andItemNameEqualTo(this.getItemName());
        }
        if (StringUtils.isNotBlank(this.getItemDescription())) {
            criteria.andItemDescriptionEqualTo(this.getItemDescription());
        }
        if (StringUtils.isNotBlank(this.getListingId())) {
            criteria.andListingIdEqualTo(this.getListingId());
        }
        if (StringUtils.isNotBlank(this.getAsin())) {
            criteria.andAsinEqualTo(this.getAsin());
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getQuantity() != null) {
            criteria.andQuantityEqualTo(this.getQuantity());
        }
        if (this.getItemIsMarketplace() != null) {
            criteria.andItemIsMarketplaceEqualTo(this.getItemIsMarketplace());
        }
        if (StringUtils.isNotBlank(this.getItemCondition())) {
            criteria.andItemConditionEqualTo(this.getItemCondition());
        }
        if (StringUtils.isNotBlank(this.getZshopCategory())) {
            criteria.andZshopCategoryEqualTo(this.getZshopCategory());
        }
        if (StringUtils.isNotBlank(this.getMerchantShippingGroup())) {
            criteria.andMerchantShippingGroupEqualTo(this.getMerchantShippingGroup());
        }
        if (this.getIsOnline() != null) {
            criteria.andIsOnlineEqualTo(this.getIsOnline());
        }
        if (StringUtils.isNotBlank(this.getProductId())) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getMainImage())) {
            criteria.andMainImageEqualTo(this.getMainImage());
        }
        if (StringUtils.isNotBlank(this.getExtraImages())) {
            criteria.andExtraImagesEqualTo(this.getExtraImages());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getOfflineDate() != null) {
            criteria.andOfflineDateEqualTo(this.getOfflineDate());
        }
        if (this.getAmazonProductId() != null) {
            criteria.andAmazonProductIdEqualTo(this.getAmazonProductId());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (this.getSalePrice() != null) {
            criteria.andSalePriceEqualTo(this.getSalePrice());
        }
        if (this.getSaleStartDate() != null) {
            criteria.andSaleStartDateEqualTo(this.getSaleStartDate());
        }
        if (this.getSaleEndDate() != null) {
            criteria.andSaleEndDateEqualTo(this.getSaleEndDate());
        }
        if (StringUtils.isNotBlank(this.getSampleImage())) {
            criteria.andSampleImageEqualTo(this.getSampleImage());
        }
        if (this.getIsFollowSellDelete() != null) {
            criteria.andIsFollowSellDeleteEqualTo(this.getIsFollowSellDelete());
        }
        if (this.getOpenDate() != null) {
            criteria.andOpenDateEqualTo(this.getOpenDate());
        }
        if (this.getIsPopular() != null) {
            criteria.andIsPopularEqualTo(this.getIsPopular());
        }
        if (StringUtils.isNotBlank(this.getInfringementWord())) {
            criteria.andInfringementWordEqualTo(this.getInfringementWord());
        }
        if (this.getIsSitePublish() != null) {
            criteria.andIsSitePublishEqualTo(this.getIsSitePublish());
        }
        if (this.getAutoUpdateMsgDate() != null){
            criteria.andAutoUpdateMsgDateEqualTo(this.getAutoUpdateMsgDate());
        }
        return example;
    }
}