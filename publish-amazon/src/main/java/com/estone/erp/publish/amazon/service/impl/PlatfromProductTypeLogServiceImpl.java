package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.mapper.PlatfromProductTypeLogMapper;
import com.estone.erp.publish.amazon.model.PlatfromProductTypeLog;
import com.estone.erp.publish.amazon.model.PlatfromProductTypeLogCriteria;
import com.estone.erp.publish.amazon.model.PlatfromProductTypeLogExample;
import com.estone.erp.publish.amazon.service.PlatfromProductTypeLogService;
import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> platfrom_product_type_log
 * 2023-12-29 11:23:39
 */
@Service("platfromProductTypeLogService")
@Slf4j
public class PlatfromProductTypeLogServiceImpl implements PlatfromProductTypeLogService {
    @Resource
    private PlatfromProductTypeLogMapper platfromProductTypeLogMapper;

    @Override
    public int countByExample(PlatfromProductTypeLogExample example) {
        Assert.notNull(example, "example is null!");
        return platfromProductTypeLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<PlatfromProductTypeLog> search(CQuery<PlatfromProductTypeLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        PlatfromProductTypeLogCriteria query = cquery.getSearch();
        PlatfromProductTypeLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = platfromProductTypeLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<PlatfromProductTypeLog> platfromProductTypeLogs = platfromProductTypeLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<PlatfromProductTypeLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(platfromProductTypeLogs);
        return result;
    }

    @Override
    public PlatfromProductTypeLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return platfromProductTypeLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PlatfromProductTypeLog> selectByExample(PlatfromProductTypeLogExample example) {
        Assert.notNull(example, "example is null!");
        return platfromProductTypeLogMapper.selectByExample(example);
    }

    @Override
    public int insert(PlatfromProductTypeLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return platfromProductTypeLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(PlatfromProductTypeLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return platfromProductTypeLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(PlatfromProductTypeLog record, PlatfromProductTypeLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return platfromProductTypeLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return platfromProductTypeLogMapper.deleteByPrimaryKey(ids);
    }
}