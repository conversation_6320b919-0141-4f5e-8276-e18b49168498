package com.estone.erp.publish.amazon.mq.model;

import lombok.Getter;

/**
 * 图片比较相似度图，返回值
 */
@Getter
public enum MainImageSyncComparisonErrorType {

    /**
     * 保存
     */
    SUCCESS(-1, "成功"),

    /**
     * 不符合阈值要求
     */
    NOT_IN_COMPLIANCE_WITH_RULES(0, "不符合阈值要求"),

    /**
     * 1 数据不符合
     */
    DATA_DOES_NOT_MATCH(1, "数据不符合"),
    /**
     * 2 产品系统查询不到图片
     */
    PRODUCT_SYSTEM_CANNOT_FIND_THE_IMAGE(2, "产品系统查询不到图片"),
    /**
     * 3 不符合规则图片
     */
    NO_SKU_IMAGE_RULES_EXIST(3, "不符合规则图片"),
    /**
     * 4 算法返回值不存在有效数据
     */
    RETURN_VALUE_DOES_NOT_CONTAIN_VALID_DATA(4, "算法返回值不存在有效数据"),
    /**
     * 5 算法异常
     */
    ANOMALY(5, "算法异常"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR(6, "未知错误"),
    ;

    MainImageSyncComparisonErrorType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * code
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;


}
