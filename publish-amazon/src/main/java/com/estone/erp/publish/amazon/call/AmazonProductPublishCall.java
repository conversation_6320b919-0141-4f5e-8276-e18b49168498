package com.estone.erp.publish.amazon.call;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.ERPInvoker;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.process.submit.AmazonDelayTaskDispatcher;
import com.estone.erp.publish.amazon.call.process.submit.ProductSubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.call.process.submit.SubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.amazonImageGenerateRecord.service.AmazonImageGenerateRecordService;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.request.RequestFeedsApiParam;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;

/**
 * @Description 可用于：amazon产品列表相关刊登 和 定时把停产，存档的产品库存改为0，处理类
 * <AUTHOR>
 * @Date 2019/10/25 15:32
 **/
@Slf4j
@Setter
public class AmazonProductPublishCall {

    private AmazonProductPublishCallHelper amazonProductPublishCallHelper = SpringUtils.getBean(AmazonProductPublishCallHelper.class);
    protected SubmitFeedXmlStrategy<AmazonVariantBO> xmlStrategy = SpringUtils.getBean(ProductSubmitFeedXmlStrategy.class);
    protected AmazonProcessReportService amazonProcessReportService = SpringUtils.getBean(AmazonProcessReportService.class);
    protected EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);
    protected AmazonImageGenerateRecordService amazonImageGenerateRecordService = SpringUtils.getBean(AmazonImageGenerateRecordService.class);
    /**
     * 延时任务转发线程
     */
    public static final AmazonDelayTaskDispatcher delayTaskDispatcher = new AmazonDelayTaskDispatcher(200, 2000);
    /**
     * 图片路径
     */
    private String imagePath;
    /**
     * 账号
     */
    protected AmazonAccount account;
    /**
     * 为线程之间切换保留用户数据
     */
    private String username;
    /**
     * 添加任务时间
     */
    private long addTimeValue = System.nanoTime();

    /**
     * 是否执行sku验证
     */
    protected boolean executeFinishForbidOrOffLineSkus = true;

    /**
     * 是否执行 上传成功后的一些操作
     */
    protected boolean executeAfterFinishProcessReports = true;

    public AmazonProductPublishCall(String accountNumber){
        this(accountNumber, SpringUtils.getBean(AmazonAccountService.class).queryAmazonAccountByAccountNumber(accountNumber));
    }

    public AmazonProductPublishCall(String accountNumber, AmazonAccount account){
        Assert.notNull(account, String.format("帐号【%s】为空！", accountNumber));
        Assert.notNull(xmlStrategy, "XML组装策略为空！");
        this.account = account;
        saveThreadLocalData();
        if(StringUtils.isBlank(username)){
            username = DataContextHolder.getUsername();
        }
        imagePath = amazonProductPublishCallHelper.getImagePath(account.getAccountNumber());
    }

    public void batchPublish(List<AmazonVariantBO> amazonVariants, List<String> feedTypes) {
        batchPublish(feedTypes, item -> {
            item.setUnitDatas(amazonVariants);
            item.setImagePath(imagePath);
            item.setOperationType(OperationType.PartialUpdate);
            return true;
        }, null);
    }

    /**
     *
     * @Description: 批量刊登上传数据
     *
     * @param feedTypes 上传数据类型列表
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @param finishCallBack 结束回调函数
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public void batchPublish(List<String> feedTypes, Function<PublishData<AmazonVariantBO>, Boolean> fillPublishData,
                             ERPInvoker finishCallBack) {
        if (CollectionUtils.isEmpty(feedTypes)) {
            log.warn("invoke batchPublish() error, params not satisfy.");
            return;
        }

        List<PublishData<AmazonVariantBO>> totalUnitPublishDatas = new ArrayList<>();
        for (String feedType : feedTypes) {
            List<PublishData<AmazonVariantBO>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, fillPublishData);
            totalUnitPublishDatas.addAll(unitPublishDatas);
        }
    }

    /**
     *
     * @Description: 构建刊登数据
     *
     * @param feedType 上传数据类型
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public List<PublishData<AmazonVariantBO>> getSuccessUnitPublishDatas(String feedType,
                                                           Function<PublishData<AmazonVariantBO>, Boolean> fillPublishData) {
        PublishData<AmazonVariantBO> publishData = new PublishData<>();
        publishData.setAccount(account);
        publishData.setFeedType(feedType);
        if (fillPublishData != null && !BooleanUtils.toBoolean(fillPublishData.apply(publishData))) {
            return CommonUtils.emptyList();
        }

        List<PublishData<AmazonVariantBO>> unitPublishDatas = new ArrayList<>(publishData.getUnitDatas().size());
        for (AmazonVariantBO unitData : publishData.getUnitDatas()) {
            Optional<PublishData<AmazonVariantBO>> optional = getSuccessUnitPublishData(publishData, unitData);
            if (optional.isPresent()) {
                unitPublishDatas.add(optional.get());
            }
        }

        return unitPublishDatas;
    }

    public Optional<PublishData<AmazonVariantBO>> getSuccessUnitPublishData(PublishData<AmazonVariantBO> publishData, AmazonVariantBO unitData) {
        PublishData<AmazonVariantBO> unitPublishData = new PublishData<>();
        unitPublishData.setAccount(account);
        unitPublishData.setCurrency(publishData.getCurrency());
        unitPublishData.setFeedType(publishData.getFeedType());
        unitPublishData.setImagePath(publishData.getImagePath());
        unitPublishData.setOperationType(publishData.getOperationType());
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(unitData));

        List<AmazonProcessReport> reports = amazonProductPublishCallHelper.initProcessReports(unitPublishData);
        unitPublishData.setReports(reports);

        Optional<PublishData<AmazonVariantBO>> empty = Optional.empty();
        if(executeFinishForbidOrOffLineSkus){
            // 当sku为侵权产品或状态为清仓，甩卖，休假，停产，存档时，不允许刊登
            if (publishData.getFeedType().equals(SpFeedType.POST_PRODUCT_DATA.getValue())) {
                amazonProductPublishCallHelper.finishForbidOrOffLineSkus(unitPublishData);
            }
        }
        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return empty;
        }

        if (account == null || "0".equals(account.getAccountStatus())) {
            String msg = account == null ? "账号没有获取到授权信息" : "账号状态为禁用，不执行。";
            finishProcessReports(unitPublishData, false, msg);
            return empty;
        }

        String xml = getXsdXml(unitPublishData);
        if (StringUtils.isEmpty(xml)) {
            finishProcessReports(unitPublishData, false, "xml文件生成失败");
            return empty;
        }

        // 处理错误的sku报告
        amazonProductPublishCallHelper.finishErrorProcessReports(unitPublishData);
        // 没有Message和sku的关系，则退出
        if (MapUtils.isEmpty(unitPublishData.getMsgId2SkuMap())) {
            return empty;
        }

        // 设置处理报告为running状态
        amazonProductPublishCallHelper.runProcessReports(reports);

        // 调用SP接口
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        String feedTypeStr = unitPublishData.getFeedType();

        RequestFeedsApiParam request = new RequestFeedsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSpFeedType(SpFeedType.of(feedTypeStr));
        request.setDocId2MessageXmlMap(getDocId2MessageXml(xml, unitPublishData));
        request.setAddTimeValue(addTimeValue);

        ApiResult<String> apiResult = AmazonSpLocalServiceUtils.addFeedsTask(request);

        String taskId = apiResult.getResult();
        if (!apiResult.isSuccess()) {
            // 对于重复添加的任务，记录taskId
            if (StringUtils.isNotEmpty(taskId)) {
                reports.forEach(report -> {
                    report.setTaskId(taskId);
                });
            }
            finishProcessReports(unitPublishData, false, apiResult.getErrorMsg());
            return empty;
        }

        if (StringUtils.isEmpty(taskId)) {
            finishProcessReports(unitPublishData, false, "添加任务失败，taskId为空");
            return empty;
        }

        // 更新处理报告的taskId
        amazonProductPublishCallHelper.updatePublishDataTaskId(unitPublishData, taskId);

        return Optional.of(unitPublishData);
    }

    private String getXsdXml(PublishData<AmazonVariantBO> publishData) {
        String xml = null;
        String feedType = publishData.getFeedType();
        switch (SpFeedType.of(feedType)) {
            case POST_PRODUCT_DATA:
                xml = xmlStrategy.transferProduct2Xml(publishData);
                if (xml.contains("<Brand>")){
                    log.error(xml);
                    amazonProductPublishCallHelper.recordListingBrand(publishData,xml);
                }
                break;
            case POST_PRODUCT_RELATIONSHIP_DATA:
                xml = xmlStrategy.transferProductRelationship2Xml(publishData);
                break;
            case POST_PRODUCT_PRICING_DATA:
                xml = xmlStrategy.transferProductPrice2Xml(publishData);
                break;
            case POST_INVENTORY_AVAILABILITY_DATA:
                xml = xmlStrategy.transferProductInventory2Xml(publishData);
                break;
            case POST_PRODUCT_IMAGE_DATA:
                xml = xmlStrategy.transferProductImage2Xml(publishData);
                break;
            default:
                break;
        }

        return xml;
    }

    /**
     *
     * @Description: 批量设置处理报告为结束状态
     *
     * @param publishData 刊登数据
     * @param status 成功状态
     * @param msg 报告信息
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void finishProcessReports(PublishData<AmazonVariantBO> publishData, boolean status, String msg) {
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            amazonProductPublishCallHelper.finshProcessReport(report, status, msg);
        });
        amazonProcessReportService.update(reports);
        if (status) {
            afterFinishProcessReports(publishData);
        }
    }

    public void afterFinishProcessReports(PublishData<AmazonVariantBO> publishData) {
        /*List<AmazonProcessReport> reports = publishData.getReports();
        List<AmazonVariantBO> variants = publishData.getUnitDatas();
        Map<String, AmazonVariantBO> variantMap = new HashMap<String, AmazonVariantBO>(variants.size());
        for (AmazonVariantBO variant : variants) {
            variantMap.put(variant.getSellerSku(), variant);
        }
        //<AmazonVariant> upldateVariants = new ArrayList<AmazonVariant>();
        //String feedType = publishData.getFeedType();
        for (AmazonProcessReport report : reports) {
            String sellerSku = report.getDataValue();
            //AmazonVariant variant = variantMap.get(sellerSku);
            // 处理报告失败，不更新listing
            if (!org.apache.commons.lang.BooleanUtils.toBoolean(report.getStatus())) {
                continue;
            }

            /*AmazonVariant updateVariant = new AmazonVariant();
            updateVariant.setId(variant.getId());
            if (SpFeedType.POST_PRODUCT_PRICING_DATA.getValue().equals(feedType)) {
                updateVariant.setPrice(variant.getPrice());
                updateVariant.setSalePrice(variant.getSalePrice());
                updateVariant.setSaleStartDate(variant.getSaleStartDate());
                updateVariant.setSaleEndDate(variant.getSaleEndDate());
            }
            else if (SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue().equals(feedType)) {
                updateVariant.setQuantity(variant.getQuantity());
            }
            else if (SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue().equals(feedType)) {
                updateVariant.setMainImage(variant.getMainImage());
                updateVariant.setExtraImages(variant.getExtraImages());
                updateVariant.setSampleImage(variant.getSampleImage());
            }
            upldateVariants.add(updateVariant);
        }

        /*log.warn("update amazon variants size: {}.", upldateVariants.size());
        if (!upldateVariants.isEmpty()) {
            amazonVariantService.update(upldateVariants);
        }*/
    }

    private void updatePlatformSkuMappping(PublishData<AmazonVariantBO> publishData) {
        if (!SpFeedType.POST_PRODUCT_DATA.getValue().equals(publishData.getFeedType())) {
            return;
        }
        EsSkuBind skuBind = new EsSkuBind();
        skuBind.setPlatform(Platform.Amazon.name());
        skuBind.setSellerId(account.getAccountNumber());
        //设置不为试卖  (改成数据来源)
        skuBind.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        esSkuBindService.batchBindSkus(skuBind, publishData.getSku2SellerSkuMap(),null);
    }

    public Map<String, String> getDocId2MessageXml(String xml, PublishData<AmazonVariantBO> unitPublishData) {
        Map<Integer, String> msgId2SkuMap = unitPublishData.getMsgId2SkuMap();
        Map<Integer, String> msgId2MessageXmlMap = XsdUtils.splitXmlByMessage(xml);
        Map<String, List<String>> docId2MessageXmlsMap = new HashMap<>(unitPublishData.getSku2ReportMap().size());
        int size = SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue().equals(unitPublishData.getFeedType()) ? 9 : 1;
        msgId2MessageXmlMap.forEach((msgId, messageXml) -> {
            String sku = msgId2SkuMap.get(msgId);
            if (!docId2MessageXmlsMap.containsKey(sku)) {
                docId2MessageXmlsMap.put(sku, new ArrayList<>(size));
            }
            docId2MessageXmlsMap.get(sku).add(messageXml);
        });

        Map<String, String> docId2MessageXmlMap = new HashMap<>(docId2MessageXmlsMap.size());
        docId2MessageXmlsMap.forEach((k, v) -> {
            docId2MessageXmlMap.put(k, JSON.toJSONString(v));
        });

        return docId2MessageXmlMap;
    }

    /**
     * @Description: 保存ThreadLocal数据
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    private void saveThreadLocalData() {
        if (StringUtils.isEmpty(username)) {
            this.username = WebUtils.getUserName();
        }
    }

    /**
     * @Description: 初始化ThreadLocal数据
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public void initThreadLocalData() {
        if (StringUtils.isNotEmpty(username)) {
            DataContextHolder.setUsername(this.username);
        }
    }
}
