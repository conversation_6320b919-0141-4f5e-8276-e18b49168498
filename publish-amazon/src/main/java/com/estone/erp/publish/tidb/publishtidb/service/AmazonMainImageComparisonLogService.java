package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogCriteria;
import com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogExample;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> amazon_main_image_comparison_log
 * 2024-02-28 16:40:33
 */
public interface AmazonMainImageComparisonLogService {
    int countByExample(AmazonMainImageComparisonLogExample example);

    CQueryResult<AmazonMainImageComparisonLog> search(CQuery<AmazonMainImageComparisonLogCriteria> cquery);

    List<AmazonMainImageComparisonLog> selectByExample(AmazonMainImageComparisonLogExample example);

    AmazonMainImageComparisonLog selectByPrimaryKey(Integer id);

    int insert(AmazonMainImageComparisonLog record);

    int updateByPrimaryKeySelective(AmazonMainImageComparisonLog record);

    int updateByExampleSelective(AmazonMainImageComparisonLog record, AmazonMainImageComparisonLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void batchInsert(List<AmazonMainImageComparisonLog> list, String site);

    Boolean existFullAccountLog(String accountNumber, String site, String openDate);

    Set<String> existListingId(String site, List<String> allIds);
}