package com.estone.erp.publish.hbase;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.HBaseAdmin;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.phoenix.queryserver.client.ThinClientUtil;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;

/**
 * <AUTHOR>
 * @date 2022/11/3 17:11
 * @description
 */
@Slf4j
public class HbaseTest {

    Connection connection;

    @Before
    public void before() throws IOException {
        // {"config":{"hbase.master":"************:600000","hbase.zookeeper.property.clientPort":"2181","hbase.zookeeper.quorum":"************,************,************"}}

        Map<String, String> config = new HashMap<>();
//        config.put("hbase.master", "************:600000");
//        config.put("hbase.zookeeper.property.clientPort", "2181");
        config.put("hbase.zookeeper.quorum", "************,************,************");


        Configuration configuration = HBaseConfiguration.create();
        config.forEach(configuration::set);
        connection = ConnectionFactory.createConnection(configuration);
    }

    @Test
    public void t1() throws IOException {
        try (HBaseAdmin admin = (HBaseAdmin) connection.getAdmin()) {
            boolean test = admin.tableExists(TableName.valueOf("STUDENT"));
            System.out.println(test);
        }
    }

    @Test
    public void  get1() throws IOException {
        String tableName = "STUDENT";
        List<String> cols = null;
        try(Table table = connection.getTable(TableName.valueOf(tableName))) {
            String rowKey = "1001";
            Get get = new Get(Bytes.toBytes(rowKey));
            if (null != cols) {
                cols.forEach(col -> {
                    String colFamily = "f0";
                    get.addColumn(Bytes.toBytes(colFamily), Bytes.toBytes(col));
                });
            }
            Result result = table.get(get);
            List<Cell> cells = result.listCells();
            for (Cell cell: cells) {
                //这里获取的信息都是字节类型的，可以通过new String(bytes)转为字符串
                //列族
                byte[] famaily_bytes = CellUtil.cloneFamily(cell);
                //列
                byte[] column_bytes = CellUtil.cloneQualifier(cell);
                //值
                byte[] value_bytes = CellUtil.cloneValue(cell);
                System.out.println("列族："+new String(famaily_bytes)+",列："+new String(column_bytes)+",值："+new String(value_bytes));
            }
        }
    }

     @Test
     public void find1() throws IOException {
         String tableName = "STUDENT";
         try (Table table = connection.getTable(TableName.valueOf(tableName))){
             Scan scan = new Scan();
             scan.setReversed(false);
             scan.withStartRow(Bytes.toBytes("1001"));
//             scan.withStopRow(Bytes.toBytes(param.getStopRow()));

             scan.setFilter(new SingleColumnValueFilter(
                     Bytes.toBytes("f0"),
                     Bytes.toBytes("NAME"),
                     CompareOperator.EQUAL,
                     Bytes.toBytes("zhangsan")));

             scan.setCaching(1000);
             ResultScanner scanner = table.getScanner(scan);
             for (Result result : scanner) {
                 //可以使用listCells()获取所有cell，cell对应的是某一个列的数据
                 List<Cell> cells = result.listCells();
                 for (Cell cell: cells) {
                     //这里获取的信息都是字节类型的，可以通过new String(bytes)转为字符串
                     //列族
                     byte[] famaily_bytes = CellUtil.cloneFamily(cell);
                     //列
                     byte[] column_bytes = CellUtil.cloneQualifier(cell);
                     //值
                     byte[] value_bytes = CellUtil.cloneValue(cell);
                     System.out.println("列族："+new String(famaily_bytes)+",列："+new String(column_bytes)+",值："+new String(value_bytes));
                 }
                 //这里还可以直接使用getValue(family, qualifier)直接获取指定列族中指定列的数据
                 //byte[] age_bytes = result.getValue(Bytes.toBytes("info"),Bytes.toBytes("age"));
             }
         }
     }

    @Test
    public void t2() throws Exception {
        //1、加载驱动
//        Class.forName("org.apache.phoenix.queryserver.client.Driver");
        String connectionUrl = ThinClientUtil.getConnectionUrl("hadoop101", 8765);
        System.out.println(connectionUrl);
        java.sql.Connection connection = DriverManager.getConnection(connectionUrl);

        ForkJoinPool pool = new ForkJoinPool(6);
        for (int i = 0; i < 12; i++) {
            int n = i;
            pool.execute(() -> {
                try {

                    PreparedStatement preparedStatement = connection.prepareStatement("select * from \"student\" where \"name\" ='zhangsan' limit 1");
                    ResultSet resultSet = preparedStatement.executeQuery();
                    while (resultSet.next()) {
//                        System.out.println(String.format("thread:%s -> n:%s  ", Thread.currentThread().getName(), n)+ resultSet.getString(1) + "\t" + resultSet.getString(2));
                        log.info("result:"+resultSet.getString(1) + "\t" + resultSet.getString(2));
                    }
                }catch (Exception e){
                    log.error(String.format("n:%s ", n), e);
                }
            });
        }

        Thread.currentThread().join();;

        //关闭
        connection.close();
    }

    @Test
    public void t3() throws SQLException {
        String connectionUrl = ThinClientUtil.getConnectionUrl("hadoop101", 8765);
        System.out.println(connectionUrl);
        java.sql.Connection connection = DriverManager.getConnection(connectionUrl);
        PreparedStatement preparedStatement = connection.prepareStatement("select * from \"student\"");

        ResultSet resultSet = preparedStatement.executeQuery();

        while (resultSet.next()) {
            System.out.println(resultSet.getString(1) + "\t" + resultSet.getString(2));
        }

        //关闭
        connection.close();
    }

    @Test
    public void tt(){
        String account = "US-obvctdcwx";
        String prefix = account.substring(0, account.length() - 1);
        CharSequence suffix = account.subSequence(account.length() - 1, account.length());
        String gtAccount = prefix + (char)(suffix.charAt(0)-1);
        String ltAccount = prefix + (char)(suffix.charAt(0)+1);
        System.out.println();
    }
}
