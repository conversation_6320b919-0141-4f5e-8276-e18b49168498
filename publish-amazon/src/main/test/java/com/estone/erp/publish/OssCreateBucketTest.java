package com.estone.erp.publish;


import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.StorageClass;
import com.estone.erp.publish.common.oss.AliOss;
import com.estone.erp.publish.common.oss.OssUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Random;

@Slf4j
public class OssCreateBucketTest {

    /**
     * Bucket 命名规范
     * 命名长度为 3~63 个字符
     * 只允许小写字母、数字、短横线（-），且不能以短横线开头或结尾
     * Bucket 名称在 OSS 范围内必须全局唯一
     */
    @Test
    @SneakyThrows
    public void createGpsrImageBucket() {

        String bucketName = "-gpsrimage-";
        System.out.println("========================================");
        //AliOss aliOss = AliOss.Amazon_Image;
        AliOss aliOss = null;
        // 创建OSSClient实例。
        OSS ossClient = OssUtils.getOssClientSingle(aliOss);
        try {
            Random random = new Random();
            for (int i =0; i<=2;i++){
                String gpsrImageName = generateRandomString(random.nextInt(6) +2) + bucketName + generateRandomString(random.nextInt(6) +2);
                createBucket(ossClient,gpsrImageName);
                System.out.println(gpsrImageName);

            }
        }catch(Exception e) {
            log.error("异常", e.getMessage());
        }finally {
            ossClient.shutdown();
        }

    }

    public static String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            // 生成随机字符（a-z, 0-9）
            int randomChar = random.nextInt(35);
            if (randomChar < 10) {
                // 数字
                sb.append(randomChar);
            } else if (randomChar < 36) {
                // 小写字母
                sb.append((char) ('a' + randomChar - 10));
            }
        }

        return sb.toString();
    }




    public static void createBucket(OSS ossClient,String bucketName) throws Exception {
        try {
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            createBucketRequest.setStorageClass(StorageClass.Standard);
            createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
            ossClient.createBucket(createBucketRequest);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        }
    }
}
