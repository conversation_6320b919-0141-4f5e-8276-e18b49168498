package com.estone.erp.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingProductData;
import com.estone.erp.publish.amazon.componet.publish.domain.OSSImageData;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class AsinPublishTest {

    @Autowired
    private AmazonTemplateService amazonTemplateService;
    @Autowired
    private AmazonPublishImagePathService amazonPublishImagePathService;

    /**
     * 单体刊登
     */
    @Test
    public void testAsinPublish() {
        AmazonTemplateBO amazonTemplateBO = amazonTemplateService.selectBoById(114542463, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        amazonTemplateBO.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        updateTemplateImage(amazonTemplateBO);
        amazonTemplateService.updateByPrimaryKeySelective(amazonTemplateBO);
        ListingProductData listingProductData = ListingProductData.ofSingleProduct(amazonTemplateBO, Lists.newArrayList());
        log.info("listingProductData:{}", JSON.toJSONString(listingProductData));
    }


    public void updateTemplateImage(AmazonTemplateBO template) {
        List<String> templateImages = AmazonTemplateUtils.getAllImages(List.of(template));
        if (CollectionUtils.isEmpty(templateImages)) {
            return;
        }

        Map<String, OSSImageData> imageMapping = template.getImageMapping();
        if (MapUtils.isEmpty(imageMapping)) {
            // 空 上传OSS图片
            String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(template.getSellerId());
            uploadImageToAliOSS(templateImages, imagePath, imageMapping, template);
            return;
        }

        // 处理临期图片 临期时间近6小时删除
        List<String> deleteImages = imageMapping.entrySet().stream()
                .filter(entry -> entry.getValue().getExpireTime().isBefore(LocalDateTime.now().minusHours(6)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deleteImages)) {
            // 删除过期图片
            imageMapping.entrySet().removeIf(entry -> deleteImages.contains(entry.getKey()));
        }

        // templateImages 中获取需要上传的图片
        List<String> needUploadImages = templateImages.stream()
                .filter(image -> !imageMapping.containsKey(image))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUploadImages)) {
            // 无需上传
            return;
        }
        // 重新上传图片
        String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(template.getSellerId());
        uploadImageToAliOSS(needUploadImages, imagePath, imageMapping, template);
    }

    private static void uploadImageToAliOSS(List<String> templateImages, String imagePath, Map<String, OSSImageData> imageMapping, AmazonTemplateBO template) {
        Map<String, String> map = AmazonUtils.copyImagesToAliOSS(templateImages, imagePath);
        // 更新图片映射，图片过期时间为5天
        LocalDateTime expireTime = LocalDateTime.now().plusDays(5);
        for (String key : map.keySet()) {
            imageMapping.put(key, new OSSImageData(map.get(key), expireTime));
        }
        template.setImageMapping(imageMapping);
    }

}
