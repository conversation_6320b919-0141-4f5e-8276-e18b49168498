package com.estone.erp.publish;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@WebAppConfiguration
public class BaseJunit extends AbstractJUnit4SpringContextTests {

    @Autowired
    protected ApplicationContext ctx;

    protected ApplicationContext getContext() {
        return applicationContext;
    }
}
