package com.estone.erp.publish;

import com.estone.erp.publish.amazon.mapper.AmazonFormLogMapper;
import com.estone.erp.publish.amazon.model.AmazonFormLogExample;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class LogBackTest {

    @Autowired
    AmazonFormLogMapper amazonFormLogMapper;

    @Test
    public void testBase() {

        log.debug("这个是debug日志");
        log.info("这个是info日志");
        log.warn("这个是warn日志");
        log.error("这个是error日志");

        // 测试 sql 是否可以打印出来
        int i = amazonFormLogMapper.countByExample(new AmazonFormLogExample());
        log.info("amazonFormLogMapper.countByExample(new AmazonFormLogExample())->{}", i);

    }
}
