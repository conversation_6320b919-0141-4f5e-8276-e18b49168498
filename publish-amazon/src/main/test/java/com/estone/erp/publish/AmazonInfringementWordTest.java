package com.estone.erp.publish;

import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-21 9:46
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class AmazonInfringementWordTest {
    @Autowired
    private InfringementWordService infringementWordService;

    @Test
    public void delInfringementWordTest() {

        String desc = " <br> Pair Dog Shoes Protective Feet Covers Socks For Pet Indoor Use Outdoor Dog Feet Covers Protective Socks</br>\n" +
                "        <br>Features:</br>\n" +
                "        Our dog socks are soft and comfortable for paws.\n" +
                "        Easy to wear: Our dog protective shoes have 4 size to fit for small to large dogs. The socks are easy to put on and hard to fall with the elastic brim.\n" +
                "        Our socks can protect your dog's paws from the hard surfaces.\n" +
                "        The socks are washable, easy to clean by hand or washing machine.\n" +
                "        These socks are suitable for indoor and outdoor activity; Wearing dog protective shoes can also protect your dog's injured paws.\n" +
                "        <br>Specifications:</br>\n" +
                "        Component: Polyester\n" +
                "        Quantity: 4Pcs\n" +
                "        <br>Package Includes:</br>\n" +
                "        4PCS Dog Socks\n" +
                "        <br>Note:</br>\n" +
                "        1.Please allow 1-2cm errors due to manual measurement, note that you donot mind before order.\n" +
                "        2.Due to the differences between different monitors, the picture may not reflect the actual color.";

        WordValidateResult wordValidateResult = new WordValidateResult();
        wordValidateResult.setOriginWord("safety");
        wordValidateResult.setType("违禁词");
        wordValidateResult.setIsIgnore(0);

        WordValidateResult wordValidateResult2 = new WordValidateResult();
        wordValidateResult2.setOriginWord("Perfect");
        wordValidateResult2.setType("违禁词");
        wordValidateResult2.setIsIgnore(0);

        WordValidateResult wordValidateResult3 = new WordValidateResult();
        wordValidateResult3.setOriginWord("clean");
        wordValidateResult3.setType("违禁词");
        wordValidateResult3.setIsIgnore(0);


        List<WordValidateResult> wordValidateResultList = new ArrayList<>();
        wordValidateResultList.add(wordValidateResult);

        Map<String, Integer> infringementWordMap = new HashMap<>();
        infringementWordMap.put("Perfect", 1);
        infringementWordMap.put("clean", 1);
        infringementWordMap.put("safety", 1);

        String string = infringementWordService.htmlDelInfringementWord(desc, infringementWordMap);
        log.info("delInfringementWordTest result: {}", string);

    }

}
