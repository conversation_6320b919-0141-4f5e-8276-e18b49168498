package com.estone.erp.publish.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.PublishApplication;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.excel.model.ListingEUInfoExcelDO;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ProductCategoryInfo;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemOfficial;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-29 下午3:17
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class ListingEUInfoExportTest {
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonCategoryService amazonCategoryService;
    @Autowired
    private SingleItemEsService singleItemEsService;

    private static final Map<String, String> browseNodeCacheMap = new ConcurrentHashMap<>();


    private final static String exportFilePath = "D:\\temp\\file\\amazon_eu\\";

    @Test
    @SneakyThrows
    public void testExport() {
        // 需要更改线上ip+端口  getVmUserNameByAccountList    getProductSystemAllCategory getPictureUrlBySkuAndType
        // 单品和 在线数据切成线上es ip
        List<String> accountNumberList = List.of("DE-bp378jvix","ES-bp378jvix","FR-bp378jvix","IT-bp378jvix","DE-dgyzsiv7lmwm","ES-dgyzsiv7lmwm","FR-dgyzsiv7lmwm","IT-dgyzsiv7lmwm","DE-nahaob5","FR-nahaob5","IT-nahaob5","ES-nahaob5","DE-aeing400isga","FR-aeing400isga","IT-aeing400isga","ES-aeing400isga","DE-wumingshan2023_10","FR-wumingshan2023_10","IT-wumingshan2023_10","ES-wumingshan2023_10","DE-li_linzhong1","FR-li_linzhong1","ES-li_linzhong1","IT-li_linzhong1");
        //  需要调整header @RequestMapping(value = "/account/getVmusernameByAcc", method = RequestMethod.POST, headers = {"content-type=application/json", "Authorization=5df26666b185fbf0b3437482125d340e"})
        Map<String,String> vmUserMap = AccountUtils.getVmUserNameByAccountList(accountNumberList);

        List<ProductCategoryInfo> productCategoryInfoList = ProductUtils.getProductSystemAllCategory();
        Map<Integer,ProductCategoryInfo> productCategoryInfoMap = productCategoryInfoList.stream()
                .collect(Collectors.toMap(o -> o.getId(),o ->o));
        accountNumberList.forEach(accountNumber -> {

                try {
                    export(accountNumber,productCategoryInfoMap,vmUserMap.get(accountNumber));
                } catch (Exception e) {
                    log.error("导出 {} 站点 {} 数据失败",  accountNumber, e);
                }

        });
        log.info("导出白底图");
    }


    private void export(String accountNumber,Map<Integer,ProductCategoryInfo> productCategoryInfoMap,String vmUsername) throws IOException {
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

         new File(exportFilePath + vmUsername).mkdirs();
        File writeFile = new File(exportFilePath + vmUsername + "\\" + accountNumber + ".xlsx");

        ExcelWriter excelWriter = EasyExcel.write(writeFile, ListingEUInfoExcelDO.class).useDefaultStyle(false).build();

        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setOrderBy("createDate");
        request.setSequence("ASC");
        request.setIsOnline(true);
        request.setAccountNumber(accountNumber);
        request.setFields(new String[]{"accountNumber", "isOnline", "site", "articleNumber", "parentAsin", "sonAsin", "browseNode", "brandName","mainSku"});

        int total = esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                writeFile.deleteOnExit();
                return;
            }
            List<ListingEUInfoExcelDO> excelDOList = listings.stream().map(listing -> {
                ListingEUInfoExcelDO excelDO = new ListingEUInfoExcelDO();
                excelDO.setPurveyorName(vmUsername);
                excelDO.setAccountName(accountNumber);
                excelDO.setChildAsin(listing.getSonAsin());
                excelDO.setParentAsin(listing.getParentAsin());
                excelDO.setBrandName(listing.getBrandName());
                excelDO.setSku(listing.getArticleNumber());
               /* String browseNodeName = getBrowseNodeName(listing.getBrowseNode(), site);
                excelDO.setPlatformCategory(browseNodeName);*/

                return excelDO;
            }).collect(Collectors.toList());
            List<String> allSonSkus = excelDOList.stream().map(ListingEUInfoExcelDO::getSku).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            // 匹配单品信息
            matchProductInfo(excelDOList, allSonSkus,productCategoryInfoMap);
            // 导入下载数据
            excelWriter.write(excelDOList, writeSheet);
        });

        excelWriter.finish();
        try {
            TimeUnit.SECONDS.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("{},导入结束 total: {}", accountNumber, total);
    }

    private void matchProductInfo(List<ListingEUInfoExcelDO> excelDOList, List<String> allSonSkus,Map<Integer,ProductCategoryInfo> productCategoryInfoMap) {
        SingleItemEsRequest request = new SingleItemEsRequest();
        request.setFields(new String[]{"sonSku","mainSku","name", "singleItemOfficials","category.categoryPath"});
        request.setSonSkuList(allSonSkus);
        List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemsByCondition(request);
        if (CollectionUtils.isEmpty(singleItemEsList)) {
            return;
        }
        Map<String, SingleItemEs> skuProductMap = singleItemEsList.stream().collect(Collectors.toMap(SingleItemEs::getSonSku, Function.identity(), (k1, k2) -> k1));
        excelDOList.forEach(excelDO -> {
            String sku = excelDO.getSku();
            SingleItemEs singleItemEs = skuProductMap.get(sku);
            if (singleItemEs == null) {
                return;
            }
            excelDO.setProductNameCh(singleItemEs.getName());
            excelDO.setPlatformCategory(getProdCategory(productCategoryInfoMap,singleItemEs.getCategory().getCategoryPath()));
            String mainSku = singleItemEs.getMainSku() ;
            String sonSku = singleItemEs.getSonSku();
            String type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
            List<String> images = FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
            String url =  getSkuMainImage(sonSku,images);
            if (StringUtils.isBlank(url)) {
                type = PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName();
                images = FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
                url =  getSkuMainImage(sonSku,images);
            }
            if (StringUtils.isBlank(url)){
                log.warn("spu获取文件夹空" + sonSku);
            }
            excelDO.setImagerUrl(replaceFmsImage(url));
            List<SingleItemOfficial> singleItemOfficials = singleItemEs.getSingleItemOfficials();
            if (CollectionUtils.isEmpty(singleItemOfficials)) {
                return;
            }
            excelDO.setProductNameEn(getEnTitle(singleItemOfficials));
        });
    }

    private String getEnTitle(List<SingleItemOfficial> singleItemOfficials) {
        String entitle = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(singleItemOfficials)) {
            for (SingleItemOfficial singleItemOfficial : singleItemOfficials) {
                String titleAtt = null;
                // 标题、描述
                if (StringUtils.isNotEmpty(singleItemOfficial.getLanguage()) && "en".equals(singleItemOfficial.getLanguage())) {
                    if (StringUtils.isBlank(entitle)) {
                        entitle = getLongTitle(singleItemOfficial);
                    } else if (StringUtils.isBlank(entitle)) {
                        entitle = getShortTitle(singleItemOfficial);
                    } else {
                        titleAtt = StringUtils.isNotEmpty(singleItemOfficial.getTitle()) ? singleItemOfficial.getTitle() : titleAtt;
                        if (StringUtils.isNotEmpty(titleAtt)) {
                            try {
                                List<String> titleList = JSON.parseArray(titleAtt, String.class);
                                entitle = titleList.get(0);
                            } catch (Exception e) {
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(entitle)) {
                        return entitle;
                    }
                }
            }
        }
        return entitle;
    }

    private String getProdCategory(Map<Integer,ProductCategoryInfo> productCategoryInfoMap,String categoryPath){
        if (StringUtils.isBlank(categoryPath)){
            return null;
        }
        String [] categoryID= categoryPath.split("-");
        String name = productCategoryInfoMap.get(Integer.valueOf(categoryID[1])).getEnName();
        return name;
    }

    private String getBrowseNodeName(String browseNodeId, String site) {
        if (StringUtils.isEmpty(browseNodeId)) {
            return null;
        }
        if (browseNodeCacheMap.containsKey(browseNodeId)) {
            return browseNodeCacheMap.get(browseNodeId);
        }
        AmazonCategoryWithBLOBs amazonCategoryWithBLOBs = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId(browseNodeId, site);
        if (amazonCategoryWithBLOBs == null) {
            //log.error("站点：{}，节点：{}，为空", browseNodeId, site);
            return null;
        }
        if (StringUtils.isNotBlank(amazonCategoryWithBLOBs.getBrowsePathByName())) {
            // 截取第一个&前的字符串
            String pathByName = amazonCategoryWithBLOBs.getBrowsePathByName();
            int index = amazonCategoryWithBLOBs.getBrowsePathByName().indexOf("&");
            String result = (index != -1) ? pathByName.substring(0, index) : pathByName;
            browseNodeCacheMap.put(browseNodeId, result);
            return result;

        }
        return null;
    }
    private static String FILL_SYSTEM_URl ="http://10.100.1.200:8888";

    /**
     * 获取sku同名规则图片,需要随机
     *
     * @param articleNumber articleNumber
     * @param images        产品图片池
     * @return 目标图片
     */
    public static String getSkuMainImage(String articleNumber, List<String> images) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(images)) {
            //log.warn("spu获取文件夹空" + articleNumber);
            return null;
        }
        return images.stream()
                .filter(image -> image.contains(String.format("/%s.", articleNumber)))
                .findFirst().orElse(null);
    }

    public static String replaceFmsImage(String url){
        if (StringUtils.isEmpty(url)){
            return null;
        }
        return url.replace(FILL_SYSTEM_URl, "http://original-images.oss-cn-hongkong.aliyuncs.com");
    }

    /**
     *  获取图片名称
     * @param url
     * @return
     */
    public static String getImageName(String url) {
        String name = "";
        if (StringUtils.isEmpty(url)) {
            return name;
        }
        if (url.lastIndexOf("/") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1);
        }
        if (name.lastIndexOf(".") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."));
        }
        return name;
    }


    public String getLongTitle(SingleItemOfficial singleItemOfficial) {
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle1())) {
            return singleItemOfficial.getLongTitle1();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle2())) {
            return singleItemOfficial.getLongTitle2();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle3())) {
            return singleItemOfficial.getLongTitle3();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle4())) {
            return singleItemOfficial.getLongTitle4();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle5())) {
            return singleItemOfficial.getLongTitle5();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle6())) {
            return singleItemOfficial.getLongTitle6();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle7())) {
            return singleItemOfficial.getLongTitle7();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle8())) {
            return singleItemOfficial.getLongTitle8();
        }
        return null;
    }


    public String getShortTitle(SingleItemOfficial singleItemOfficial) {
        if (StringUtils.isNotBlank(singleItemOfficial.getLongTitle1())) {
            return singleItemOfficial.getLongTitle1();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle2())) {
            return singleItemOfficial.getLongTitle2();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle3())) {
            return singleItemOfficial.getLongTitle3();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle4())) {
            return singleItemOfficial.getShortTitle4();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle5())) {
            return singleItemOfficial.getShortTitle5();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle6())) {
            return singleItemOfficial.getShortTitle6();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle7())) {
            return singleItemOfficial.getShortTitle7();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle8())) {
            return singleItemOfficial.getShortTitle8();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle9())) {
            return singleItemOfficial.getShortTitle9();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle10())) {
            return singleItemOfficial.getShortTitle10();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle11())) {
            return singleItemOfficial.getShortTitle11();
        }
        if (StringUtils.isNotBlank(singleItemOfficial.getShortTitle12())) {
            return singleItemOfficial.getShortTitle12();
        }
        return  null;
    }

    public String getEnTitle(SingleItemOfficial singleItemOfficial) {
        String enTitle = null;
        if (StringUtils.isNotEmpty(singleItemOfficial.getLanguage())) {
            if ("en".equals(singleItemOfficial.getLanguage()) && org.apache.commons.lang.StringUtils.isNotEmpty(singleItemOfficial.getTitle())) {
                try {
                    List<String> titleList = JSON.parseArray(singleItemOfficial.getTitle(), String.class);
                    enTitle = titleList.get(0);
                } catch (Exception e) {

                }
            }
        }
        return enTitle;
    }

}
