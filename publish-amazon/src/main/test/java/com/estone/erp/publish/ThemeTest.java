package com.estone.erp.publish;

import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSampler;
import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSamplerFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.spell.LevenshteinDistance;
import org.junit.Test;

import java.io.File;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
public class ThemeTest {

    // 最多允许10个merchantId同时执行
    private static final int MAX_CONCURRENT_MERCHANTS = 10;
    // 存储每个merchantId的令牌桶
    private static final Map<String, Semaphore> MERCHANT_TOKENS = new ConcurrentHashMap<>();
    // 线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(20);
    // 用于提交任务的线程池
    private static final ExecutorService submitExecutor = Executors.newFixedThreadPool(5);

    @Test
    public void levenshteinExample() {
        LevenshteinDistance levenshteinDistance = new LevenshteinDistance();
        String text1 = "musical instruments";
        String text2 = "musical instrument toy\n" +
                "brass and woodwind instruments\n" +
                "misc other\n" +
                "percussion instruments\n" +
                "toys and games\n" +
                "instrument parts and accessories";


        String[] split = text2.split("\n");
        Arrays.stream(split).forEach(text -> {
            float distance1 = levenshteinDistance.getDistance(text1, text);
            double similarity1 = 1.0 - (double) distance1 / Math.max(text1.length(), text.length());
            System.out.println(String.format("'%s' 和 '%s' 的编辑距离是: %f", text1, text, similarity1));

        });

    }

    @Test
    public void samplerTest() {
        JsonFeedSamplerFactory jsonFeedSamplerFactory = new JsonFeedSamplerFactory();
        JsonFeedSampler jsonFeedSampler = jsonFeedSamplerFactory.getFeedJsonSampler(20);
        for (int i = 0; i < 100; i++) {
            boolean sample = jsonFeedSampler.sample(i);
            if (sample) {
                log.info("sample:{}", i);
            }
        }

    }

    @Test
    public void test() {
//        BigDecimal usd = ListingPriceData.priceNumberFormat("USD", 9.9d);
//        log.info("usd:{}", usd);
//
//        // 示例测试
//        System.out.println("USD, 9.9   -> " + ListingPriceData.priceNumberFormat("USD", 9.99));    // 预期: 9.90
//        System.out.println("USD, 9.82   -> " + ListingPriceData.priceNumberFormat("USD", 9.88));    // 预期: 9.90
//        System.out.println("USD, 9.79  -> " + ListingPriceData.priceNumberFormat("USD", 9.79));   // 预期: 9.99
//        System.out.println("USD, 9.995 -> " + ListingPriceData.priceNumberFormat("USD", 9.995));  // 预期: 10.00
//        System.out.println("USD, 9.994 -> " + ListingPriceData.priceNumberFormat("USD", 9.994));  // 预期: 9.99
//        System.out.println("JPY, 123.456 -> " + ListingPriceData.priceNumberFormat("JPY", 123.456)); // 预期: 123
//        System.out.println("JPY, 123.789 -> " + ListingPriceData.priceNumberFormat("JPY", 123.789)); // 预期: 124

        String str1 = "For EM5 DSLR Pouc";
        String str2 = "For DSLR Pouch";

        System.out.println("字符串 '" + str1 + "' 是否包含数字? " + containsDigit(str1));
        System.out.println("字符串 '" + str2 + "' 是否包含数字? " + containsDigit(str2));


        Map<String, Set<String>> merchantIdAsinMap = new HashMap<>();
        for (String merchantId : List.of("A", "B", "c")) {
            List.of("1", "2", "2", "3", "3", "4", "4", "5", "9", "10").forEach(asin -> {
                merchantIdAsinMap.computeIfAbsent(merchantId, k -> Sets.newHashSet()).add(asin);
            });
        }

        log.info("merchantIdAsinMap:{}", merchantIdAsinMap);


    }

    public static boolean containsDigit(String s) {
        // 首先检查字符串是否为null或空
        if (s == null || s.isEmpty()) {
            return false;
        }
        // 使用Stream API检查是否包含任何数字字符
        return s.chars().anyMatch(Character::isDigit);
    }


    @Test
    public void testDownloadFile() {
        File excelFile = getExcelFile("http://10.100.1.200:8888/productInfo/2025-06/20-09-44-47-153/Ozon-1741410-2025-06-209730886741202013180.xlsx");
        log.info("excelFile:{}", excelFile);
        if (excelFile == null) {
            return;
        }
    }

    private File getExcelFile(String url) {
        try {
            HttpClient client = HttpClient
                    .newBuilder()
                    .version(HttpClient.Version.HTTP_1_1)
                    .followRedirects(HttpClient.Redirect.NORMAL)
                    .connectTimeout(Duration.ofSeconds(20))
                    .build();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofMinutes(2))
                    .header("Content-Type", "application/json")
                    .GET().build();

            // 时间戳
            File tempFile = File.createTempFile("" + LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")), ".xlsx");
            HttpResponse<Path> response = client.send(request, HttpResponse.BodyHandlers.ofFile(tempFile.toPath()));
            if (response.statusCode() != 200) {
                throw new RuntimeException("请求失败，状态码：" + response.statusCode());
            }
            if (response.body() == null) {
                throw new RuntimeException("请求失败，返回结果为空");
            }
            if (!tempFile.exists()) {
                throw new RuntimeException("请求失败，文件不存在");
            }
            return tempFile;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void sampleTest() {
        try {
            // 初始化全局令牌池
            Semaphore globalSemaphore = new Semaphore(MAX_CONCURRENT_MERCHANTS);
            CountDownLatch submitLatch = new CountDownLatch(5); // 假设5个线程提交任务

            // 多个线程同时提交任务
            for (int t = 0; t < 5; t++) {
                final int threadId = t;
                submitExecutor.submit(() -> {
                    try {
                        // 每个线程提交20个任务
                        for (int i = 0; i < 20; i++) {
                            final int taskId = threadId * 20 + i;
                            final String merchantId = "merchant_" + (taskId % 5);

                            // 获取或创建merchantId对应的令牌桶
                            Semaphore merchantSemaphore = MERCHANT_TOKENS.computeIfAbsent(
                                    merchantId, k -> new Semaphore(1));

                            // 尝试获取全局令牌和商户令牌
                            if (globalSemaphore.tryAcquire(5, TimeUnit.SECONDS)) {
                                if (merchantSemaphore.tryAcquire(5, TimeUnit.SECONDS)) {
                                    // 成功获取到令牌后提交任务
                                    submitTask(merchantId, taskId, globalSemaphore, merchantSemaphore);
                                } else {
                                    // 获取商户令牌失败，释放全局令牌
                                    globalSemaphore.release();
                                    System.out.println("商户[" + merchantId + "]繁忙，任务[" + taskId + "]放弃执行");
                                }
                            } else {
                                System.out.println("系统繁忙，任务[" + taskId + "]放弃执行");
                            }

                            // 模拟任务提交间隔
                            Thread.sleep(100);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        submitLatch.countDown();
                    }
                });
            }

            // 等待所有提交线程完成
            submitLatch.await();
            
            // 关闭线程池
            submitExecutor.shutdown();
            executorService.shutdown();
            executorService.awaitTermination(1, TimeUnit.HOURS);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 清理资源
            MERCHANT_TOKENS.clear();
        }
    }

    private void submitTask(String merchantId, int taskId, Semaphore globalSemaphore,
                            Semaphore merchantSemaphore) {
        executorService.submit(() -> {
            try {
                System.out.println(String.format("商户[%s]的任务[%d]开始执行 - 当前时间: %d",
                        merchantId, taskId, System.currentTimeMillis()));

                // 模拟任务执行
                Thread.sleep(2000);

                System.out.println(String.format("商户[%s]的任务[%d]执行完成 - 当前时间: %d",
                        merchantId, taskId, System.currentTimeMillis()));

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 释放令牌
                merchantSemaphore.release();
                globalSemaphore.release();
            }
        });
    }
}
