{"unit_count": {"title": "Unit Count", "description": "Specify the number of units and the unit type of the product", "examples": ["72.0 <PERSON><PERSON>ces"], "type": "array", "minItems": 1, "minUniqueItems": 1, "maxUniqueItems": 1, "selectors": ["marketplace_id"], "items": {"type": "object", "required": ["value"], "properties": {"value": {"title": "Unit Count", "description": "For products that are consumed by volume, weight, linear dimension, etc., provide the net quantity that would be shipped to a customer who orders one ASIN (e.g. 12 pack of 6 floz. bottles of water = 72, vs. a single 2 liter bottle = 2). For products consumed as individual units, provide the total number of units (pack of 12 pens = 12). For packed assortments of non-identical items, enter 1", "editable": true, "hidden": false, "examples": ["72.0"], "type": "number", "minimum": 0}, "type": {"title": "Unit Count Type", "description": "For items consumed by volume, weight, linear dimension etc., provide the unit of measure listed on the products. For products consumed as individual units, enter: count", "examples": ["<PERSON><PERSON><PERSON>"], "type": "object", "required": ["language_tag", "value"], "properties": {"value": {"title": "Unit Count Type", "description": "For items consumed by volume, weight, linear dimension etc., provide the unit of measure listed on the products. For products consumed as individual units, enter: count", "editable": true, "hidden": false, "examples": ["<PERSON><PERSON><PERSON>"], "type": "string", "maxLength": 20, "maxUtf8ByteLength": 20}, "language_tag": {"$ref": "#/$defs/language_tag"}}, "additionalProperties": false}, "marketplace_id": {"$ref": "#/$defs/marketplace_id"}}, "additionalProperties": false}}}