package com.estone.erp.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.amazon.componet.AmazonAutoPublishHelper;
import com.estone.erp.publish.amazon.jobHandler.AmazonCheckSkuSyncLogJobHandler;
import com.estone.erp.publish.amazon.jobHandler.AmazonListingGrossPrafitJobHandle;
import com.estone.erp.publish.amazon.jobHandler.ProductListingRefreshHandler;
import com.estone.erp.publish.amazon.util.AmazonMatchProdInfoUtil;
import com.estone.erp.publish.base.pms.enums.PicturePlatEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.esProduct.service.PublishSingleItemEsService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.estone.erp.common.constant.RedisConstant.PRODUCT_MAIN_SKU_RELATION;
import static com.estone.erp.common.constant.RedisConstant.PRODUCT_SKU_RELATION;

/**
 * <AUTHOR>
 * @date 2022-06-07 9:01
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class ProductUtilTest {

    @Autowired
    private AmazonListingGrossPrafitJobHandle amazonListingGrossPrafitJobHandle;
    @Autowired
    private AmazonAutoPublishHelper amazonAutoPublishHelper;
    @Autowired
    private PublishSingleItemEsService singleItemEsService;
    @Autowired
    private AmazonCheckSkuSyncLogJobHandler amazonCheckSkuSyncLogJobHandler;
    @Autowired
    private ProductListingRefreshHandler productListingRefreshHandler;
    @Autowired
    private AmazonProductListingService amazonProductListingService;

    @Test
    public void productUtilTest() {

        ProductInfoVO productInfoVO = ProductUtils.getSkuInfo("7YY800052");

        List<ProductInfo> productInfos = ProductUtils.findProductInfos(List.of("7YY800052"));

        log.info("productInfoVO:{}", JSON.toJSONString(productInfoVO));
//        // 获取平台所有侵权词
//        ApiResult<List<String>> infringementWordBySaleChannel = ProductUtils.getInfringementWordBySaleChannel(SaleChannelEnum.AMAZON.getChannelName());
//        log.info("获取平台所有侵权词:{}",JSON.toJSONString(infringementWordBySaleChannel));
//
//        // 获取所有侵权词
//        ApiResult<List<InfringementWord>> allInfringementService = ProductUtils.getAllInfringementService();
//        log.info("获取所有侵权词:{}", JSON.toJSONString(allInfringementService));
//
//        // 根据侵权类型查询侵权词
//        ApiResult<List<InfringementWord>> infringementByType = ProductUtils.getInfringementByType(StrConstant.INFRINGING_FORBIDDEN_WORD);
//        log.info("根据侵权类型查询侵权词:{}",JSON.toJSONString(infringementByType));
//
//        ApiResult<Boolean> a1 = ProductUtils.isExistArticleNumber("7HH400726");
//        ApiResult<Boolean> a2 = ProductUtils.isExistArticleNumber("7HH400726_NSF");
//        log.info("检测sku是否存在 a1:{},a2:{}",JSON.toJSONString(a1), JSON.toJSONString(a2));
//
//
//        ArrayList<String> article = Lists.newArrayList("7HH400726", "7HH400726_NSF","7HH400716-8","5AC303812-PLR");
//        List<ProductInfo> productInfos = ProductUtils.findProductInfos(article);
//        log.info("根据sku查询对应信息:{}", JSON.toJSONString(productInfos));
//
//        String[] fields = {"mainSku", "sonSku", "tagCode", "tag"};
//        List<ProductInfo> skuInfosByEs = ProductUtils.findSkuInfosByEs(Lists.newArrayList("7HH400726", "7HH400726_NSF"), fields);
//        log.info("指定查询返回字段:{}",JSON.toJSONString(skuInfosByEs));
//
//        List<ProductInfo> skuImage = ProductUtils.findSkuImage(article);
//        log.info("根据sku查询对应主图信息:{}",JSON.toJSONString(skuImage));
//
//        List<ProductInfo> esSkuImageByMainSku = ProductUtils.findESSkuImageByMainSku(article);
//        log.info("根据主sku查询ES产品信息:{}",JSON.toJSONString(esSkuImageByMainSku));
//
//        List<String> stopAndArchivedSku = ProductUtils.findStopAndArchivedSku(article);
//        log.info("根据sku集合查询单品状态为’Stop’, ‘Archived’的sku:{}",JSON.toJSONString(stopAndArchivedSku));
//
//        List<String> New = ProductUtils.listByStatus("New");
//        List<String> Waiting = ProductUtils.listByStatus("Waiting");
//        List<String> Normal = ProductUtils.listByStatus("Normal");
//        List<String> Stop = ProductUtils.listByStatus("Stop");
//        List<String> Clearance = ProductUtils.listByStatus("Clearance");
//        List<String> Pending = ProductUtils.listByStatus("Pending");
//        List<String> Holiday = ProductUtils.listByStatus("Holiday");
//        List<String> Reduction = ProductUtils.listByStatus("Reduction");
//        List<String> Archived = ProductUtils.listByStatus("Archived");
//        log.info("从redis中获取特定状态的sku,new:{}", JSON.toJSONString(New));
//        log.info("从redis中获取特定状态的sku,Waiting:{}",JSON.toJSONString(Waiting));
//        log.info("从redis中获取特定状态的sku,Normal:{}",JSON.toJSONString(Normal));
//        log.info("从redis中获取特定状态的sku,Stop:{}",JSON.toJSONString(Stop));
//        log.info("从redis中获取特定状态的sku,Clearance:{}",JSON.toJSONString(Clearance));
//        log.info("从redis中获取特定状态的sku,Pending:{}",JSON.toJSONString(Pending));
//        log.info("从redis中获取特定状态的sku,Holiday:{}",JSON.toJSONString(Holiday));
//        log.info("从redis中获取特定状态的sku,Reduction:{}",JSON.toJSONString(Reduction));
//        log.info("从redis中获取特定状态的sku,Archived:{}",JSON.toJSONString(Archived));
//
//        ApiResult<List<String>> pendingAndReductionRecoverSkus = ProductUtils.getPendingAndReductionRecoverSkus();
//        log.info("获取暂停，休假变正常状态的sku:{}",JSON.toJSONString(pendingAndReductionRecoverSkus));
//
//        Calendar c = Calendar.getInstance();
//        String today = DateUtils.format(c.getTime(), "yyyy-MM-dd");
//        c.add(Calendar.DATE, -1);
//        String yesterday = DateUtils.format(c.getTime(), "yyyy-MM-dd");
//        String infInfoByTime = ProductUtils.getInfInfoByTime(yesterday + " 15:00:00", today + " 15:00:00");
//        log.info("根据时间获取侵权信息版本2：{}",JSON.toJSONString(infInfoByTime));
//
//        String now = DateUtils.format(c.getTime(), DateUtils.STANDARD_DATE_PATTERN);
//        c.add(Calendar.DATE, -3);
//        String _3day = DateUtils.format(c.getTime(), DateUtils.STANDARD_DATE_PATTERN);
//        ApiResult<Map<String, String>> forbiddenByDate = ProductUtils.getForbiddenByDate(_3day, now);
//        log.info("根据时间查询禁售信息:{}",JSON.toJSONString(forbiddenByDate));
//
//        ApiResult<Integer> skuCount = ProductUtils.getSkuCount();
//        log.info("获取所有sku的数量,{}",JSON.toJSONString(skuCount));
//
//        ProductInfoVO skuInfo1 = ProductUtils.getSkuInfo("5AC303812-PLR_YY");
//        ProductInfoVO skuInfo2 = ProductUtils.getSkuInfo("5AC303812-PLR");
//        log.info("ES查询单个子SKU对应产品状态等，s1:{},s2:{}",JSON.toJSONString(skuInfo1), JSON.toJSONString(skuInfo2));
//
//        ApiResult<Map<String, String>> smSkuMapByOffSite = ProductUtils.getSMSkuMapByOffSite(0,1000);
//        log.info("分页获取子sku和主sku对应的map,{}",JSON.toJSONString(smSkuMapByOffSite));


//        long begin = new Date().getTime() - (1000L * 60 * 60 * 24);
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        List<String> changeStopArchivedSku = ProductUtils.getChangeStopArchivedSku(sdf.format(new Date(begin)), sdf.format(new Date()));
//        log.info("按照时间范围查询停产存档SKU列表,{}",JSON.toJSONString(changeStopArchivedSku));

//        List<ProductCategoryInfo> productSystemAllCategory = ProductUtils.getProductSystemAllCategory();
//        log.info("获取所有的分类,{}",JSON.toJSONString(productSystemAllCategory));
//        List<String> spuList = Arrays.asList("11AG200895", "11AG200897", "11AG200898", "11AG200900", "0NN100503", "1000665", "10A00114", "10B00343", "10SN200023");
//        for (String spu : spuList) {
//            ApiResult<SpuInfringementWordDO> spuInfringementWord = ProductUtils.getSpuInfringementWord(SaleChannel.CHANNEL_AMAZON, spu);
//            log.info("spu:{}->{}",spu,JSON.toJSONString(spuInfringementWord));
//
//        }
//        ProductInfoVO productInfoVO = ProductUtils.getSkuInfo("2SS701811");
//        System.out.println(productInfoVO.getCatId());
//        System.out.println(productInfoVO.getCategoryId());
//        Map<String, Category> categoryBySku = ProductUtils.getCategoryBySku(Collections.singletonList("2SS701811-A"));
//        System.out.println(JSON.toJSONString(categoryBySku));
        ResponseJson spuTitleResp = ProductUtils.getSpuTitles(Arrays.asList("19SQ100077"));
        log.info(JSON.toJSONString(spuTitleResp));
    }

    @Test
    public void getProductInfoByArticleNumber() {
        String articleNumber = "19SQ100281";
        SpuOfficial defalutSpuOfficial = AmazonMatchProdInfoUtil.getDefalutSpuOfficial(articleNumber);
        SpuOfficial amazonOfficial = ProductUtils.getAmazonOfficial(articleNumber);

        List<SpuOfficial> defalutSpuOfficial1 = List.of(defalutSpuOfficial, amazonOfficial);
        log.info(JSON.toJSONString(defalutSpuOfficial1));

    }

    @Test
    public void jobTest() throws Exception {

//        ReturnT<String> run = amazonListingGrossPrafitJobHandle.run("{\n" +
//                "    \"accountNumbers\":[],\n" +
//                "    \"week\":3,\n" +
//                "    \"updateAll\":true\n" +
//                "}");
        String param = "{\"forbidType\":\"侵权,平台规则,产品限售,物流规则\",\"causes\":\"\", \"accounts\": [] , \"limit\": 8}";
        //syncAmazonProhibitionListingJobHandler.run(param);

        TimeUnit.SECONDS.sleep(500);
    }

    @Test
    public void loadAllProductSonSku() {
        int limit = 500;
        int page = 0;
        int total = 0;
        StopWatch started = StopWatch.createStarted();
        String lastSonSku = "";
        while (true) {
            List<String> sonSkuList = singleItemEsService.pageGetSonSkuList(page, limit);
            if (CollectionUtils.isEmpty(sonSkuList)) {
                break;
            }
            total += sonSkuList.size();
            page++;
            lastSonSku = sonSkuList.get(sonSkuList.size() - 1);
            log.info("lastSonSku:{},{}ms",lastSonSku,started.getTime());
        }
        started.stop();
        System.out.println();
        log.info("page:{},total:{},lastSonSku:{}, time:{}ms", page, total, lastSonSku, started.getTime());
    }

    @Test
    public void getSkuByItemStatusChangTimeRang() {
        LocalDate localDate = LocalDate.now().minusDays(1);
        LocalDateTime yesTodayMinTime = LocalDateTime.of(localDate, LocalTime.MIN);
        LocalDateTime todayMinTime = LocalDateTime.of(localDate, LocalTime.MAX);
        long starTime = yesTodayMinTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long endTime = todayMinTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();

        StopWatch started = StopWatch.createStarted();
        List<String> skuByItemStatusChangTimeRang = singleItemEsService.getSkuByItemStatusChangTimeRang(starTime, endTime);
        started.stop();
        System.out.println();
        log.info("total:{}, time:{}ms", skuByItemStatusChangTimeRang.size(), started.getTime());
    }

    @Test
    @SneakyThrows
    public void testCheckSkuSyncLog() {
        String param = "{\"beforeDay\":7}";
        amazonCheckSkuSyncLogJobHandler.run(param);
        TimeUnit.SECONDS.sleep(999);
    }

    @Test
    @SneakyThrows
    public void testSyncProductInfo() {
        String param = "{\"type\":\"executeNormalData\"}";
        productListingRefreshHandler.run(param);
        TimeUnit.SECONDS.sleep(999);
    }

    @Test
    public void getImagesByProduct() {
        String plat = PicturePlatEnum.AMAZON_TEMPLATE_PLAT.getName();
        List<String> images = FmsUtils.getTemplateImages("1AG300082",plat);
        System.out.println(JSON.toJSONString(images));
    }

    @Test
    public void updateProductTest() {
        AmazonProductListing amazonProductListing = new AmazonProductListing();
        amazonProductListing.setArticleNumber("2SW0135-BK");
        amazonProductListing.setAccountNumber("US-shangwang1258");
        amazonProductListing.setSite("us");
        amazonProductListing.setForbidChannel(",Amazon,Ebay,");
        amazonProductListing.setInfringementObj("|平台禁售|cjz测试的禁售原因|侵权测试test01|");
        amazonProductListing.setInfringementTypename(null);
        amazonProductListing.setNormalSale(",JP,");
        amazonProductListing.setTagNames(",普货,");
        amazonProductListing.setTagCodes(",normal goods,");
        amazonProductListing.setSpecialGoodsCode(",2002,");
        amazonProductListing.setSpecialGoodsName(",侵权,");

        AmazonProductListingExample example = new AmazonProductListingExample();
        AmazonProductListingExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo("US-shangwang1258");
        criteria.andArticleNumberEqualTo("2SW0135-BK");
        amazonProductListingService.updateProductMsgByExampleSelective(amazonProductListing, example);
    }

    @Test
    public void loadSkuRelationMapping() {
        String mainSkuStr = "19SQ100396";
        String sonSkuStr = "19SQ100396";
        String[] mainSkuArray = mainSkuStr.split(",");
        Arrays.stream(mainSkuArray).distinct().forEach(mainSku->{
            String v = PublishRedisClusterUtils.get(PRODUCT_MAIN_SKU_RELATION + mainSku);
            if (v == null) {
                PublishRedisClusterUtils.set(PRODUCT_MAIN_SKU_RELATION + mainSku, mainSku);
            }
        });

        String[] sonSkuArray = sonSkuStr.split(",");
        Arrays.stream(sonSkuArray).distinct().forEach(sonSku->{
            String v = PublishRedisClusterUtils.get(PRODUCT_SKU_RELATION + sonSku);
            if (v == null) {
                PublishRedisClusterUtils.set(PRODUCT_SKU_RELATION + sonSku, sonSku);
            }
        });
    }

}
