package com.estone.erp.publish.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-29 下午3:08
 */
@Data
public class ListingEUInfoExcelDO {
    // 紫鸟名	账号名	子ASIN	父ASIN	产品名称(中文)	产品名称(英文)	"产品分类(英文)平台大分类"	品牌名称	SKU
    @ExcelProperty("紫鸟名")
    private String purveyorName;
    @ExcelProperty("账号名")
    private String accountName;
    @ExcelProperty("子ASIN")
    private String childAsin;
    @ExcelProperty("父ASIN")
    private String parentAsin;
    @ExcelProperty("产品名称(中文)")
    private String productNameCh;
    @ExcelProperty("产品名称(英文)(产品系统)")
    private String productNameEn;
    @ExcelProperty("产品分类(英文)(sku产品分类大类翻译)")
    private String platformCategory;
    @ExcelProperty("品牌名称")
    private String brandName;
    @ExcelProperty("SKU")
    private String sku;
    @ExcelProperty("白底图链接(外网永久)")
    private String imagerUrl;
}
