package com.estone.erp.publish.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.erp.publish.PublishApplication;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.excel.model.EmojiListingExcelDO;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-08-16 下午2:56
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
public class ListingHasEmojiDownloadTest {
    private static final String EMOJI_REGEX = "[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]";

    private static List<String> filterTags = new ArrayList<>();

    static {
        //♠ ❉ ❤ ☆ ✿ ★ ❥ ☛ ✔ ♛ ✮ ▲ ♥ ✪ ▶ ◆ ★ ☂ ✎ ♣ ✉ ♫ ☎ ☀ ❃ ღ ❀ ➤ ▼ ► ➥ ✎ ❄ ❁ * ✤ ➽ ✈ ☿ ●
        filterTags.add("♠");
        filterTags.add("❉");
        filterTags.add("❤");
        filterTags.add("☆");
        filterTags.add("✿");
        filterTags.add("★");
        filterTags.add("❥");
        filterTags.add("☛");
        filterTags.add("✔");
        filterTags.add("♛");
        filterTags.add("✮");
        filterTags.add("▲");
        filterTags.add("♥");
        filterTags.add("✪");
        filterTags.add("▶");
        filterTags.add("◆");
        filterTags.add("★");
        filterTags.add("☂");
        filterTags.add("✎");
        filterTags.add("♣");
        filterTags.add("✉");
        filterTags.add("♫");
        filterTags.add("☎");
        filterTags.add("☀");
        filterTags.add("❃");
        filterTags.add("ღ");
        filterTags.add("❀");
        filterTags.add("➤");
        filterTags.add("▼");
        filterTags.add("►");
        filterTags.add("➥");
        filterTags.add("❄");
        filterTags.add("❁");
        filterTags.add("*");
        filterTags.add("✤");
        filterTags.add("➽");
        filterTags.add("✈");
        filterTags.add("☿");
        filterTags.add("●");
    }


    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private AmazonProductListingService amazonProductListingService;


    @Test
    @SneakyThrows
    public void testDownloadListingHasEmoji() {
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        // 小表情
        File writeFile = new File("D:\\temp\\file\\little_emoji_listing.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, EmojiListingExcelDO.class).useDefaultStyle(false).build();
        Map<String, SalesmanAccountDetail> createByAndNameMap = new HashMap<>();
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setOrderBy("syncDate");
        request.setSequence("ASC");
        request.setIsOnline(true);
        request.setFromSaleQuantity(1L);
        request.setSaleQuantityBean("order_num_total");
        request.setItemTypeList(List.of(AmazonListingitemtypeEnum.Monomer_Item.getStatusCode(), AmazonListingitemtypeEnum.Vriant_Item.getStatusCode()));
        request.setFields(new String[]{"id", "accountNumber", "isOnline", "site", "sellerSku", "articleNumber", "parentAsin", "sonAsin", "itemName", "order_last_30d_count", "order_num_total"});
        AtomicInteger loadTotal = new AtomicInteger(0);
        int total = esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            int totalSize = loadTotal.addAndGet(listings.size());
            log.info("current total:{}", totalSize);
            List<EmojiListingExcelDO> excelDOList = new ArrayList<>();
            List<EsAmazonProductListing> descListingList = new ArrayList<>();
            // 先检查标题 标题有就跳过，没有再查描述有没有
            listings.forEach(listing -> {
                String title = listing.getItemName();
                if (matchFilterTag(title, List.of("*"))) {
                    log.info("accountNumber:{}, sellerSku:{}, title:{}", listing.getAccountNumber(), listing.getSellerSku(), title);
                    EmojiListingExcelDO convert = convert(listing);
                    excelDOList.add(convert);
                } else {
                    descListingList.add(listing);
                }
            });
            // 再检查描述有没有emoji
            if (CollectionUtils.isNotEmpty(descListingList)) {
                List<EmojiListingExcelDO> emojiListingExcelDOS = filterDescHasEmoji(descListingList);
                if (CollectionUtils.isNotEmpty(emojiListingExcelDOS)) {
                    excelDOList.addAll(emojiListingExcelDOS);
                }
            }
            if (CollectionUtils.isNotEmpty(excelDOList)) {
                List<String> accountNumbers = excelDOList.stream()
                        .map(EmojiListingExcelDO::getAccountNumber)
                        .filter(account -> !createByAndNameMap.containsKey(account))
                        .collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(accountNumbers)) {
                    Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumbers, SaleChannel.CHANNEL_AMAZON);
                    createByAndNameMap.putAll(salesmanAccountDetailMap);
                }

                excelDOList.forEach(excelDO -> {
                    SalesmanAccountDetail salesmanAccountDetail = createByAndNameMap.get(excelDO.getAccountNumber());
                    if (salesmanAccountDetail != null) {
                        excelDO.setSaleUser(CollectionUtils.isNotEmpty(salesmanAccountDetail.getSalesmanSet()) ? org.apache.commons.lang.StringUtils.join(salesmanAccountDetail.getSalesmanSet(), ",") : "");
//                        excelDO.setSaleLearner(salesmanAccountDetail.getSalesTeamLeader());
                        excelDO.setSaleManager(salesmanAccountDetail.getSalesSupervisorName());
                    }
                });
                log.info("excelDOList size: {}", excelDOList.size());
                excelWriter.write(excelDOList, writeSheet);
            }
        });
        log.info("total: {}", total);
        excelWriter.finish();
        log.info("write file success");
    }


    @Test
    @SneakyThrows
    public void testSpuOnlineListing() {
        List<String> spuList = new ArrayList<>();
        readFile("D:\\workspace\\resource\\amazon\\sku.txt", spuList);

        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        File writeFile = new File("D:\\temp\\file\\spu_all_online_listing.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, EmojiListingExcelDO.class).useDefaultStyle(false).build();
        Map<String, SalesmanAccountDetail> createByAndNameMap = new HashMap<>();
        AtomicInteger loadTotal = new AtomicInteger(0);
        List<List<String>> spuPartitions = Lists.partition(spuList, 1000);
        for (int i = 0; i < spuPartitions.size(); i++) {
            List<String> spuPartition = spuPartitions.get(i);
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setOrderBy("syncDate");
            request.setSequence("ASC");
            request.setIsOnline(true);
            request.setMainSkuList(spuPartition);
//            request.setFromSaleQuantity(1L);
//            request.setSaleQuantityBean("order_num_total");
            request.setItemTypeList(List.of(AmazonListingitemtypeEnum.Monomer_Item.getStatusCode(), AmazonListingitemtypeEnum.Vriant_Item.getStatusCode()));
            request.setFields(new String[]{"id", "accountNumber", "isOnline", "site", "sellerSku", "articleNumber", "parentAsin", "sonAsin", "itemName", "order_last_30d_count", "order_num_total"});
            final int partition = i;
            esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
                int totalSize = loadTotal.addAndGet(listings.size());
                log.info("partition:{}, current total:{}", partition, totalSize);
                List<EmojiListingExcelDO> excelDOList = listings.stream().map(this::convert).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(excelDOList)) {
                    List<String> accountNumbers = excelDOList.stream()
                            .map(EmojiListingExcelDO::getAccountNumber)
                            .filter(account -> !createByAndNameMap.containsKey(account))
                            .collect(Collectors.toList());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(accountNumbers)) {
                        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumbers, SaleChannel.CHANNEL_AMAZON);
                        createByAndNameMap.putAll(salesmanAccountDetailMap);
                    }

                    excelDOList.forEach(excelDO -> {
                        SalesmanAccountDetail salesmanAccountDetail = createByAndNameMap.get(excelDO.getAccountNumber());
                        if (salesmanAccountDetail != null) {
                            excelDO.setSaleUser(CollectionUtils.isNotEmpty(salesmanAccountDetail.getSalesmanSet()) ? org.apache.commons.lang.StringUtils.join(salesmanAccountDetail.getSalesmanSet(), ",") : "");
//                            excelDO.setSaleLearner(salesmanAccountDetail.getSalesTeamLeader());
                            excelDO.setSaleManager(salesmanAccountDetail.getSalesSupervisorName());
                        }
                    });
                    log.info("excelDOList size: {}", excelDOList.size());
                    excelWriter.write(excelDOList, writeSheet);
                }
            });
        }
        log.info("total: {}", loadTotal.get());
        excelWriter.finish();
        log.info("write file success");

    }


    private List<EmojiListingExcelDO> filterDescHasEmoji(List<EsAmazonProductListing> listings) {
        List<EmojiListingExcelDO> excelDOList = new ArrayList<>();

        Map<String, List<EsAmazonProductListing>> siteListings = listings.stream().collect(Collectors.groupingBy(EsAmazonProductListing::getSite));
        siteListings.forEach((site, siteList) -> {
            String tableIndex = amazonProductListingService.getTableIndex(site);
            List<String> sellerSku = siteList.stream().map(EsAmazonProductListing::getSellerSku).collect(Collectors.toList());
            AmazonProductListingExample example = new AmazonProductListingExample();
            example.createCriteria().andSellerSkuIn(sellerSku);
            example.setTableIndex(tableIndex);
            example.setColumns("accountNumber,sellerSku,itemDescription, searchTerms, bulletPoint");
            List<AmazonProductListing> productListings = amazonProductListingService.selectCustomColumnByExample(example);
            if (CollectionUtils.isEmpty(productListings)) {
                return;
            }


            Map<String, EsAmazonProductListing> esMap = siteList.stream().collect(Collectors.toMap(EsAmazonProductListing::getId, Function.identity()));
            for (AmazonProductListing productListing : productListings) {
                String itemDescription = productListing.getItemDescription();
                String searchTerms = productListing.getSearchTerms();
                String browseNode = productListing.getBrowseNode();
                boolean hasTag = false;
                if (matchFilterTag(itemDescription, List.of("*"))) {
                    log.info("accountNumber:{}, sellerSku:{}, itemDescription:{}", productListing.getAccountNumber(), productListing.getSellerSku(), itemDescription);
                    hasTag = true;
                }
                if (matchFilterTag(searchTerms, null)) {
                    log.info("accountNumber:{}, sellerSku:{}, searchTerms:{}", productListing.getAccountNumber(), productListing.getSellerSku(), searchTerms);
                    hasTag = true;
                }
                if (matchFilterTag(browseNode, null)) {
                    log.info("accountNumber:{}, sellerSku:{}, browseNode:{}", productListing.getAccountNumber(), productListing.getSellerSku(), browseNode);
                    hasTag = true;
                }
                if (hasTag) {
                    String key = productListing.getAccountNumber() + "_" + productListing.getSellerSku();
                    EsAmazonProductListing amazonProductListing = esMap.get(key);
                    if (amazonProductListing == null) {
                        continue;
                    }
                    EmojiListingExcelDO convert = convert(amazonProductListing);
                    excelDOList.add(convert);
                }
            }
        });
        return excelDOList;
    }

    private EmojiListingExcelDO convert(EsAmazonProductListing listing) {
        EmojiListingExcelDO excelDO = new EmojiListingExcelDO();
        excelDO.setAccountNumber(listing.getAccountNumber());
        excelDO.setSellerSku(listing.getSellerSku());
        excelDO.setSku(listing.getArticleNumber());
        excelDO.setSonAsin(listing.getSonAsin());
        excelDO.setTotalSaleCount(listing.getOrder_num_total());
        excelDO.setSale30dCount(listing.getOrder_last_30d_count());
        return excelDO;
    }

    public boolean matchFilterTag(String text, List<String> excludeTags) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        for (String filterTag : filterTags) {
            if (text.contains(filterTag)) {
                if (excludeTags != null && excludeTags.contains(filterTag)) {
                    continue;
                }
                log.info("filterTag:{}, text:{}", filterTag, text);
                return true;
            }
        }
        return false;
    }

    public boolean containsEmoji(String text, String EMOJI_REGEX) {
        Pattern pattern = Pattern.compile(EMOJI_REGEX);
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }


    @Test
    public void getSaleUser() {
//        ApiResult<SuperEmployeeInfo> allSuperByEmpNo = NewUsermgtUtils.getAllSuperByEmpNo("182328");
//        log.info("allSuperByEmpNo:{}", JSON.toJSONString(allSuperByEmpNo));


        List<String> accountNumbers = new ArrayList<>();
        accountNumbers.add("UK-trjti5ioio");
        accountNumbers.add("JP-***********");
        accountNumbers.add("UK-mujidao9877");
        accountNumbers.add("JP-qqqww10");

        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumbers, SaleChannel.CHANNEL_AMAZON);

        log.info("salesmanAccountDetailMap:{}", salesmanAccountDetailMap);

    }

    public void readFile(String fileName, List<String> dataList) throws IOException {
        try (BufferedReader bufferedReader = new BufferedReader(new FileReader(fileName))) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                dataList.add(line);
            }
        }
    }
}
