package com.estone.erp.publish.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.publish.PublishApplication;
import com.estone.erp.publish.amazon.model.AmazonCategoryExample;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.excel.model.KeyWordCategoryExcelDO;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ProductCategoryInfo;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.esProduct.service.impl.SingleItemEsServiceImpl;
import com.google.common.collect.Lists;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.model.catalogItems.Item;
import io.swagger.client.model.catalogItems.ItemBrowseClassification;
import io.swagger.client.model.catalogItems.ItemBrowseClassificationsByMarketplace;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-16 10:57
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("dev")
public class KeyWordCategoryExcelTest {

    @Autowired
    private SingleItemEsService singleItemEsService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate3;
    @Autowired
    private SingleItemEsServiceImpl singleItemEsServiceImpl;
    @Autowired
    private AmazonCategoryService amazonCategoryService;


    @Test
    public void accountSiteTest() {
        List<String> accountSites = List.of("AE-HuaiXiAE", "AU-zengQingl", "MX-AQLDD");
        List<AmazonSpAccount> accountList = accountSites.stream().map(accountNumber -> {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
            return AmazonSpLocalUtils.ToAmazonSpAccount(account);
        }).collect(Collectors.toList());
        log.info("accountList:{}", accountList);

        String translate = GoogleTranslateUtils.translate("en", "es", "one Price", 3);
        log.info("translate:{}", translate);
    }

    @Test
    public void keyWordCategoryExcelTest() {

        // 获取产品系统分类树
        ApiResult<List<ProductCategoryInfo>> categoryTree = ProductUtils.getCategoryTree();
        if (!categoryTree.isSuccess()) {
            log.error("获取产品系统分类树失败：{}", categoryTree.getErrorMsg());
            return;
        }
        List<ProductCategoryInfo> result = categoryTree.getResult();
        List<ProductCategoryInfo> lefCategory = result.stream()
                .filter(category -> Boolean.TRUE.equals(category.getIsEnable()))
                .filter(category -> Boolean.FALSE.equals(category.getIsParent()))
                .collect(Collectors.toList());

        log.info("获取到{}个叶子分类数据", lefCategory.size());
        // 遍历分类数据获取spu文案信息
        Lock lock = new ReentrantLock();

        List<List<ProductCategoryInfo>> partition = Lists.partition(lefCategory, 2048);

        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        File writeFile = new File("D:\\temp\\file\\amazon_keyword_category2.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, KeyWordCategoryExcelDO.class).useDefaultStyle(false).build();

        List<ProductCategoryInfo> productCategoryInfos = partition.get(0);
        Lists.partition(productCategoryInfos, 100).forEach(productCategoryInfo -> {
            List<KeyWordCategoryExcelDO> dataList = getSingleItemEsList(productCategoryInfo);
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            log.info("dataList:{}", dataList.size());
            // 获取平台类目
            getPlatformCategory(dataList);
            // 类目数据
            matchCategory(dataList);
            excelWriter.write(dataList, writeSheet);
//            lock.lock();
//            try {
//
//            } finally {
//                lock.unlock();
//            }
        });
        excelWriter.finish();
        try {
            TimeUnit.SECONDS.sleep(30);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("写入文件完成");

    }

    private void matchCategory(List<KeyWordCategoryExcelDO> dataList) {
        List<String> ids = dataList.stream().map(item -> {
            ArrayList<String> list = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getCategoryId()) && !"null".equals(item.getCategoryId())) {
                list.add(item.getCategoryId());
            }
            if (StringUtils.isNotBlank(item.getAu_categoryId()) && !"null".equals(item.getAu_categoryId())) {
                list.add(item.getAu_categoryId());
            }
            if (StringUtils.isNotBlank(item.getMx_categoryId()) && !"null".equals(item.getMx_categoryId())) {
                list.add(item.getMx_categoryId());
            }
            return list;
        }).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        AmazonCategoryExample example = new AmazonCategoryExample();
        example.createCriteria().andBrowseNodeIdIn(ids);
        example.setColumns("account_site,browse_node_id,browse_path_by_name,browse_path_by_name_cn");

        List<AmazonCategoryWithBLOBs> amazonCategoryWithBLOBs = amazonCategoryService.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(amazonCategoryWithBLOBs)) {
            return;
        }
        Map<String, AmazonCategoryWithBLOBs> categoryMap = amazonCategoryWithBLOBs.stream().collect(Collectors.toMap(AmazonCategoryWithBLOBs::getBrowseNodeId, Function.identity(), (k1, k2) -> k1));

        dataList.forEach(item -> {
            AmazonCategoryWithBLOBs category = categoryMap.get(item.getCategoryId());
            if (category != null && category.getAccountSite().equals("AE")) {
                item.setPlatformCategoryName(category.getBrowsePathByName());
                item.setPlatformCategoryNameChinese(category.getBrowsePathByNameCn());
            }

            AmazonCategoryWithBLOBs category1 = categoryMap.get(item.getAu_categoryId());
            if (category1 != null && category1.getAccountSite().equals("AU")) {
                item.setAu_platformCategoryName(category1.getBrowsePathByName());
                item.setAu_platformCategoryNameChinese(category1.getBrowsePathByNameCn());
            }

            AmazonCategoryWithBLOBs category2 = categoryMap.get(item.getMx_categoryId());
            if (category2 != null && category2.getAccountSite().equals("MX")) {
                item.setMx_platformCategoryName(category2.getBrowsePathByName());
                item.setMx_platformCategoryNameChinese(category2.getBrowsePathByNameCn());
            }
        });


    }

    private void getPlatformCategory(List<KeyWordCategoryExcelDO> resultList) {
        List<String> accountSites = List.of("AE-HuaiXiAE", "AU-zengQingl", "MX-AQLDD");
        List<AmazonSpAccount> accountList = accountSites.stream().map(accountNumber -> {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
            return AmazonSpLocalUtils.ToAmazonSpAccount(account);
        }).collect(Collectors.toList());


        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 3, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());

        resultList.parallelStream().forEach(item -> {
            String submitKeyword = item.getSubmitKeyword();
            if (StringUtils.isBlank(submitKeyword)) {
                return;
            }
            List<CompletableFuture<Map<String, String>>> completableFutures = accountList.stream().map(siteAccount ->
                    CompletableFuture.supplyAsync(() -> {
                        String keyword = item.getSubmitKeyword();
                        if (siteAccount.getAccountNumber().startsWith("MX")) {
                            String translate = GoogleTranslateUtils.translate("en", "es", submitKeyword, 3);
                            if (StringUtils.isNotBlank(translate)) {
                                keyword = translate;
                            }
                        }
                        ApiResult<ItemSearchResults> searchResultsApiResult = AmazonSpLocalServiceUtils.searchCatalogItemsByIdentifiersV2(List.of(keyword), siteAccount);
                        if (!searchResultsApiResult.isSuccess()) {
                            log.error("获取{}平台类目失败：{}", siteAccount.getAccountNumber(), searchResultsApiResult.getErrorMsg());
                            return Map.of(siteAccount.getAccountNumber(), "");
                        }
                        ItemSearchResults itemSearchResults = searchResultsApiResult.getResult();
                        if (CollectionUtils.isEmpty(itemSearchResults.getItems())) {
                            return Map.of(siteAccount.getAccountNumber(), "null");
                        }

                        List<Item> items = itemSearchResults.getItems();
                        String platformId = "";
                        for (Item itemData : items) {
                            if (CollectionUtils.isEmpty(itemData.getClassifications())) {
                                continue;
                            }
                            if (StringUtils.isNotBlank(platformId)) {
                                break;
                            }

                            for (ItemBrowseClassificationsByMarketplace classifications : itemData.getClassifications()) {
                                List<ItemBrowseClassification> classificationList = classifications.getClassifications();
                                if (CollectionUtils.isEmpty(classificationList)) {
                                    continue;
                                }

                                for (ItemBrowseClassification itemBrowseClassification : classificationList) {
                                    if (StringUtils.isNotBlank(platformId)) {
                                        break;
                                    }
                                    platformId = itemBrowseClassification.getClassificationId();
                                }
                            }
                        }
                        return Map.of(siteAccount.getAccountNumber(), platformId);
                    }, threadPoolExecutor)
            ).collect(Collectors.toList());

            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
            List<Map<String, String>> maps = completableFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());
            log.info("获取到{}平台类目：{}", submitKeyword, maps);
            maps.forEach(map -> {
                map.forEach((k, v) -> {
                    if (k.startsWith("MX")) {
                        item.setMx_categoryId(v);
                    }
                    if (k.startsWith("AU")) {
                        item.setAu_categoryId(v);
                    }
                    if (k.startsWith("AE")) {
                        item.setCategoryId(v);
                    }
                });
            });
        });


    }

    private List<KeyWordCategoryExcelDO> getSingleItemEsList(List<ProductCategoryInfo> lefCategory) {
        List<Integer> cidList = lefCategory.stream().map(ProductCategoryInfo::getId).collect(Collectors.toList());

        // 查询条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("categoryId", cidList));
        boolQueryBuilder.filter(QueryBuilders.termQuery("itemStatus", 7002));
        boolQueryBuilder.must(QueryBuilders.existsQuery("singleItemOfficials.packageIncludes"));

        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("by_category_id").field("categoryId").size(100);
        TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders.topHits("top_items")
                .size(20).fetchSource(new String[]{"mainSku", "categoryId", "singleItemOfficials.packageIncludes", "categoryPath", "singleItemOfficials.language"}, null);
        termsAggregationBuilder.subAggregation(topHitsAggregationBuilder);

        NativeSearchQuery query = nativeSearchQueryBuilder.withQuery(boolQueryBuilder)
                .addAggregation(termsAggregationBuilder)
                .withFields("")
                .build();

        query.setMaxResults(0);
        boolean limiting = query.isLimiting();
        log.info("limiting:{}, query:{}", limiting, query);
        List<KeyWordCategoryExcelDO> dataList = Lists.newArrayList();

        SearchHits<SingleItemEs> singleitemes = elasticsearchRestTemplate3.search(query, SingleItemEs.class, IndexCoordinates.of("singleitemes"));
        ParsedLongTerms aggregations = singleitemes.getAggregations().get("by_category_id");
        aggregations.getBuckets().forEach(bucket -> {
            ParsedTopHits topItems = bucket.getAggregations().get("top_items");
            org.elasticsearch.search.SearchHits hits = topItems.getHits();
            AtomicReference<Boolean> tag = new AtomicReference<>(false);
            for (SearchHit hit : hits) {
                if (tag.get()) {
                    break;
                }
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String mainSku = (String) sourceAsMap.get("mainSku");
                String categoryPath = (String) sourceAsMap.get("categoryPath");
                Integer categoryId = (Integer) sourceAsMap.get("categoryId");

                KeyWordCategoryExcelDO keyWordCategoryExcelDO = new KeyWordCategoryExcelDO();
                keyWordCategoryExcelDO.setSku(mainSku);
                keyWordCategoryExcelDO.setSystemCategoryId(categoryId);
                keyWordCategoryExcelDO.setSystemCategory(categoryPath);

                ArrayList<Map<String, Object>> singleItemOfficials = (ArrayList<Map<String, Object>>) sourceAsMap.get("singleItemOfficials");
                singleItemOfficials.forEach(item -> {
                    String language = (String) item.get("language");
                    if ("en".equals(language)) {
                        String packageIncludes = (String) item.get("packageIncludes");
                        if (StringUtils.isNotBlank(packageIncludes)) {
                            String[] split = packageIncludes.split("\n");
                            String value = split[0];
                            keyWordCategoryExcelDO.setSubmitKeyword(value);
                            tag.set(true);
                        }
                    }
                });
                if (StringUtils.isNotBlank(keyWordCategoryExcelDO.getSubmitKeyword())) {
                    dataList.add(keyWordCategoryExcelDO);
                }
            }


        });
        return dataList;
    }
}
