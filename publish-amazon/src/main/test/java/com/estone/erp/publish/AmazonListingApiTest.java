package com.estone.erp.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.componet.publish.AmazonJsonSchemaFactory;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingBulletPointData;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingPriceData;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingQuantityData;
import com.estone.erp.publish.amazon.componet.publish.util.AmazonListingApiUtil;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.networknt.schema.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.data.util.Pair;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-21 9:53
 */
@Slf4j
public class AmazonListingApiTest {

    @Test
    public void test() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime lastTime = LocalDateTimeUtil.of("2025-04-01 00:00:00", formatter);
        LocalDateTime lastTime1 = LocalDateTimeUtil.of("2025-04-01 00:00:00", formatter);
        LocalDateTime lastTime2 = LocalDateTimeUtil.of("2025-03-01 01:00:00", formatter);
        log.info("lastTime1:{},lastTime:{}, 【{}】", lastTime1, lastTime, lastTime1.compareTo(lastTime) >= 0);
        log.info("lastTime2:{},lastTime:{}， 【{}】", lastTime2, lastTime, lastTime2.compareTo(lastTime) >= 0);

        log.info("lastTime2:{},lastTime:{}， 【{}】", lastTime1, lastTime, lastTime1.isBefore(lastTime));
        log.info("lastTime2:{},lastTime:{}， 【{}】", lastTime2, lastTime, lastTime2.isBefore(lastTime));

    }

    @Test
    public void buildListing() {
        // 构建价格结构
        Map<String, Object> priceSchedule = AmazonListingApiUtil.warpInMap(
                Pair.of("value_with_tax", 23.41)
        );

        Map<String, Object> priceWrapper = AmazonListingApiUtil.warpInMap(
                Pair.of("schedule", AmazonListingApiUtil.wrapInArray(priceSchedule))
        );

        // 构建完整的purchasable_offer结构
        Map<String, Object> result = AmazonListingApiUtil.buildNestedArrayStructure("purchasable_offer",
                Pair.of("currency", "USD"),
                Pair.of("our_price", AmazonListingApiUtil.wrapInArray(priceWrapper))
        );

        // 添加fulfillment_availability
        Map<String, Object> fulfillment = Map.of(
                "fulfillment_channel_code", "DEFAULT",
                "quantity", 500
        );

        Map<String, Object> finalResult = Map.of(
                "fulfillment_availability", List.of(fulfillment),
                "purchasable_offer", result.get("purchasable_offer")
        );


        System.out.println(JSON.toJSONString(finalResult));

        // AmazonSKU
        String skuData = "[{\"condition\":\"New\",\"extraImages\":[\"http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-07-692/11JJ112095-cmb.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-53-620/11JJ112095-2-000.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-02-594/11JJ112095-7.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-11-054/11JJ112095.jpg\"],\"extraImagesList\":[\"http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-07-692/11JJ112095-cmb.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-53-620/11JJ112095-2-000.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-02-594/11JJ112095-7.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-11-054/11JJ112095.jpg\"],\"mainImage\":\"http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg\",\"name\":\"11JJ112095-2\",\"nameValues\":[{\"desc\":\"2\",\"name\":\"Size\",\"value\":\"あ\"}],\"nameValuesJson\":[{\"desc\":\"2\",\"name\":\"Size\",\"value\":\"あ\"}],\"quantity\":1000,\"sampleImage\":\"http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg\",\"sellerSKU\":\"11JJ112095-2_TLCXULUHSZS\",\"shippingCost\":0.0,\"sku\":\"11JJ112095-2\",\"skulifecyclephase\":\"Normal\",\"standardPrice\":2099.12,\"standardProdcutIdType\":\"EAN\",\"standardProdcutIdValue\":\"7674241219960\",\"totalPrice\":2099.12},{\"condition\":\"New\",\"extraImages\":[\"http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-59-307/11JJ112095-5.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-44-662/11JJ112095-1-000.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-11-054/11JJ112095.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-47-888/11JJ112095-1.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-57-777/11JJ112095-4.jpg\"],\"extraImagesList\":[\"http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-59-307/11JJ112095-5.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-44-662/11JJ112095-1-000.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-11-054/11JJ112095.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-47-888/11JJ112095-1.jpg\",\"http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg\",\"http://************:8888/amazon/2024-06/04-17-58-57-777/11JJ112095-4.jpg\"],\"mainImage\":\"http://************:8888/amazon/2024-06/04-17-58-47-888/11JJ112095-1.jpg\",\"name\":\"11JJ112095-1\",\"nameValues\":[{\"desc\":\"1\",\"name\":\"Size\",\"value\":\"B\"}],\"nameValuesJson\":[{\"desc\":\"1\",\"name\":\"Size\",\"value\":\"B\"}],\"quantity\":1000,\"repeatFlag\":true,\"sampleImage\":\"http://************:8888/amazon/2024-06/04-17-58-47-888/11JJ112095-1.jpg\",\"sellerSKU\":\"11JJ112095-1_TLCXULUHSZS\",\"shippingCost\":0.0,\"sku\":\"11JJ112095-1\",\"skulifecyclephase\":\"Normal\",\"standardPrice\":2099.12,\"standardProdcutIdType\":\"EAN\",\"standardProdcutIdValue\":\"7674241219977\",\"totalPrice\":2099.12}]";
        List<AmazonSku> amazonSkus = JSON.parseArray(skuData, AmazonSku.class);
        Map<String, String> imagMapping = new HashMap<String, String>();
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg", "http://************:8888/amazon/2024-06/04-17-59-09-425/11JJ112095-effect.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg", "http://************:8888/amazon/2024-06/04-17-58-55-782/11JJ112095-3.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg", "http://************:8888/amazon/2024-06/04-17-58-54-652/11JJ112095-2.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-59-07-692/11JJ112095-cmb.jpg", "http://************:8888/amazon/2024-06/04-17-59-07-692/11JJ112095-cmb.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg", "http://************:8888/amazon/2024-06/04-17-59-04-675/11JJ112095-8.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-58-53-620/11JJ112095-2-000.jpg", "http://************:8888/amazon/2024-06/04-17-58-53-620/11JJ112095-2-000.jpg");
        imagMapping.put("http://************:8888/amazon/2024-06/04-17-59-02-594/11JJ112095-7.jpg", "http://************:8888/amazon/2024-06/04-17-59-02-594/11JJ112095-7.jpg");


        // 转换对象 price
        ListingPriceData priceDO = ListingPriceData.of("USD", new BigDecimal("23.41"), 2.99, new Date(), new Date());
        System.out.println("priceDO: \n" + JSON.toJSONString(priceDO.getData()));

        // quantity
        ListingQuantityData quantityData = ListingQuantityData.of(500);
        System.out.println("quantityData: \n" + JSON.toJSONString(quantityData.getData()));

        // 五点描述
        ListingBulletPointData listingBulletPoint = ListingBulletPointData.of(List.of("五点描述1", "五点描述2", "五点描述3"), "en_US");
        System.out.println("listingBulletPoint: \n" + JSON.toJSONString(listingBulletPoint.getData()));

        // 图片
//        ListingImageData listingImageData = ListingImageData.of(amazonSkus.get(0), imagMapping);
//        System.out.println("listingImageData: \n" + JSON.toJSONString(listingImageData.getData()));

    }

    public JsonSchemaFactory mySchemaFactory() {

        JsonMetaSchema metaSchema = JsonMetaSchema
                .builder("https://schemas.amazon.com/selling-partners/definitions/product-types/meta-schema/v1", JsonMetaSchema.getV201909())
                .keywords(ValidatorTypeCode.getKeywords(SpecVersion.VersionFlag.V202012))
                .keyword(new AnnotationKeyword("id"))
                .keyword(new AnnotationKeyword("schema"))
                .keyword(new AnnotationKeyword("oneOf"))
                .keywords(Arrays.asList(
                        new NonValidationKeyword("$id"),
                        new NonValidationKeyword("$schema"),
                        new NonValidationKeyword("oneOf")
                ))
                .build();
        return JsonSchemaFactory.builder()
                .defaultMetaSchemaIri(metaSchema.getIri()).metaSchema(metaSchema)
                .build();
    }


    @Test
    @SneakyThrows
    public void validateTest() {
        String data2 = "{ \"fulfillment_availability\": [ { \"fulfillment_channel_code\": \"DEFAULT\", \"quantity\": 500 } ]}";
        String schemaUrl = "http://10.100.1.1/proxyimg/productType/US/US_FASHIONEARRING.json";
        HttpClient client = HttpClient
                .newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .connectTimeout(Duration.ofSeconds(20))
                .build();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(schemaUrl))
                .timeout(Duration.ofMinutes(2))
                .header("Content-Type", "application/json")
                .GET().build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            String schemaData = response.body();
            JsonSchemaFactory jsonSchemaFactory = AmazonJsonSchemaFactory.getInstance();
            SchemaValidatorsConfig config = SchemaValidatorsConfig.builder()
                    .formatAssertionsEnabled(true)
                    .cacheRefs(false)
                    .build();
            JsonSchema schema = jsonSchemaFactory.getSchema(schemaData, config);

            Set<ValidationMessage> validationMessages = schema.validate(data2, InputFormat.JSON, executionContext -> {
                ExecutionConfig executionConfig = executionContext.getExecutionConfig();
                executionConfig.setDebugEnabled(true);
                executionConfig.setFormatAssertionsEnabled(true);
            });

            if (CollectionUtils.isEmpty(validationMessages)) {
                return;
            }

            if (CollectionUtils.isNotEmpty(validationMessages)) {
                List<String> errorMsgList = new ArrayList<>();
                for (ValidationMessage validationMessage : validationMessages) {
                    errorMsgList.add(validationMessage.getMessage());
                    log.error(validationMessage.getMessage());
                }

            }
        } catch (IOException | InterruptedException e) {
            log.error("从文件系统下载schema失败", e);
        }

    }


    @Test
    public void schemaTest() {
        // $id of the Amazon Product Type Definition Meta-Schema.
        String schemaId = "https://schemas.amazon.com/selling-partners/definitions/product-types/meta-schema/v1";

        // Local copy of the Amazon Product Type Definition Meta-Schema.
        String metaSchemaPath = "./amazon-product-type-definition-meta-schema-v1.json";

        // Local copy of an instance of the Amazon Product Type Definition Meta-Schema.
        String luggageSchemaPath = "./luggage.json";

        // Keywords that are informational only and do not require validation.
        List<String> nonValidatingKeywords = List.of("editable", "enumNames");

        // Standard JSON Schema 2019-09 that Amazon Product Type Definition Meta-Schema extends from.
        JsonMetaSchema standardMetaSchema = JsonMetaSchema.getV201909();

        // Build Amazon Product Type Definition Meta Schema with the standard JSON Schema 2019-09 as the blueprint.
        // Register custom keyword validation classes (see below).
        JsonMetaSchema metaSchema = JsonMetaSchema.builder(schemaId, standardMetaSchema)
                .addKeywords(nonValidatingKeywords.stream().map(NonValidationKeyword::new)
                        .collect(Collectors.toSet()))
//                .addKeyword(new MaxUniqueItemsKeyword())
//                .addKeyword(new MaxUtf8ByteLengthKeyword())
//                .addKeyword(new MinUtf8ByteLengthKeyword())
                .build();


//        URIFetcher uriFetcher = uri -> {
//            // Use the local copy of the meta-schema instead of retrieving from the web.
//            if (schemaId.equalsIgnoreCase(uri.toString())) {
//                return Files.newInputStream(Paths.get(metaSchemaPath));
//            }
//
//            // Default to the existing fetcher for other schemas.
//            return new URLFetcher().fetch(uri);
//        };

        // Build the JsonSchemaFactory.
//        JsonSchemaFactory schemaFactory = new JsonSchemaFactory.Builder()
//                .defaultMetaSchemaURI(schemaId)
//                .addMetaSchema(standardMetaSchema)
//                .addMetaSchema(metaSchema)
//                .uriFetcher(uriFetcher, "https")
//                .build();

        // Create the JsonSchema instance.
//        JsonSchema luggageSchema = schemaFactory.getSchema(new String(Files.readAllBytes(Paths.get(luggageSchemaPath))));
    }

}
