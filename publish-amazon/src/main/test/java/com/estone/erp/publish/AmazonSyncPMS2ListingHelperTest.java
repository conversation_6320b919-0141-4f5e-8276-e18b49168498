package com.estone.erp.publish;

import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.process.submit.PublishAmazonProductProcesser;
import com.estone.erp.publish.amazon.call.process.submit.PublishAmazonTemplateProcesser;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.componet.AmazonSyncPMS2ListingHelper;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import io.swagger.client.enums.SpFeedType;
import junit.framework.TestCase;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * xxxx
 *
 * <AUTHOR>
 * @createTime 2022-04-11
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
public class AmazonSyncPMS2ListingHelperTest extends TestCase {

    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;

    @Autowired
    private AmazonSyncPMS2ListingHelper amazonSyncPMS2ListingHelper;

    @Test
    public void testGetSkuMainMapBySonSkuList() {
        List<String> articleNumberList = Arrays.asList("19SQ100018-1", "19SQ100011-L", "19SQ100011-M", "19SQ100011-S", "19SQ100018-A", "2SS310674", "5AC1105704-WC", "3TT108106-3", "8YY800430-4", "8YY800430-5");
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setArticleNumberList(articleNumberList);
        esAmazonProductListingService.getMainSkuMapBySonSkuList(request);
    }

    @Test
    public void testSyncPMSSku2ProductListing() {
        List<String> strings = Arrays.asList("19SQ100011",
                "19SQ100011",
                "19SQ100011",
                "3TK500124-BK",
                "4NB1100769-GY",
                "4NB501777-GY",
                "4NB501858-1",
                "4NB501858-2",
                "4NB501858-3",
                "4NB601768-CF",
                "5AC601315-A",
                "5AC702728-D",
                "7HH300831-W",
                "7HH501727",
                "7HH502462",
                "7HH701658-BL",
                "7HH702074-1",
                "7HH702074-3",
                "7HH702317-4-B",
                "9FF700535-2",
                "9FF900407-BK",
                "9FF900457-Y",
                "9FF900529-BK",
                "9FF900542-GY",
                "9FF900650-W",
                "9FF900708-1-R",
                "9FF900743-2",
                "9FF900796-BL",
                "9FF900807-BL",
                "9FF900827-Y");
        List<String> collect = strings.stream().distinct().collect(Collectors.toList());
        amazonSyncPMS2ListingHelper.syncPMSSku2ProductListing(collect);
    }

    @Test
    @SneakyThrows
    public void testTransfer2Xml() {
        AmazonAccount saleAccount = new AmazonAccount();
        saleAccount.setAccountNumber("US-shangwang1258");
        saleAccount.setMerchantId("A2KNOMGTLTAK28");
        PublishAmazonProductProcesser productProcesser = new PublishAmazonProductProcesser(saleAccount.getAccountNumber());
        PublishAmazonTemplateProcesser templateProcesser = new PublishAmazonTemplateProcesser(saleAccount.getAccountNumber());


        AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
        amazonVariantBO.setItemName("product title");
        amazonVariantBO.setItemDescription("product desc");
        PublishData<AmazonVariantBO> unitPublishData = new PublishData<>();
        unitPublishData.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        unitPublishData.setAccount(saleAccount);
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(amazonVariantBO));


        String xsdXml = productProcesser.getXsdXml(unitPublishData);
        log.info("product1: {}", xsdXml);

        AmazonTemplateBO amazonTemplateBO = new AmazonTemplateBO();
        amazonTemplateBO.setTitle("template title");
        amazonTemplateBO.setDescription("template desc");
        amazonTemplateBO.setBrand("template brand");
        amazonTemplateBO.setManufacturer("template manufacturer");
        amazonTemplateBO.setBulletPoint("[\"1\",\"2\",\"3\",\"4\",\"5\"]");


        PublishData<AmazonTemplateBO> templateBOPublishData = new PublishData<>();
        templateBOPublishData.setAccount(saleAccount);
        templateBOPublishData.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        templateBOPublishData.setUnitDatas(CommonUtils.arrayAsList(amazonTemplateBO));

        String xsdXml1 = templateProcesser.getXsdXml(templateBOPublishData);
        log.info("template: {}", xsdXml1);

        String p2 = productProcesser.getXsdXml(unitPublishData);
        log.info("product2: {}", p2);


    }

}