package com.estone.erp.publish.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-05-06 上午10:42
 */
@Data
public class DownloadExcelDO {
    @ExcelProperty("侵权词汇")
    private String keyword;
    @ExcelProperty("类型")
    private String type;
    @ExcelProperty("添加时间")
    private Date createTime;
    @ExcelProperty("禁售平台")
    private String platform;
    @ExcelProperty("禁售站点")
    private String sites;
    @ExcelProperty("链接数量")
    private Integer onlineLinkCount;
    @ExcelProperty("US链接数量")
    private Integer usLinkCount;
    @ExcelProperty("CA链接数量")
    private Integer caLinkCount;
    @ExcelProperty("DE链接数量")
    private Integer deLinkCount;
    @ExcelProperty("ES链接数量")
    private Integer esLinkCount;
    @ExcelProperty("IT链接数量")
    private Integer itLinkCount;
    @ExcelProperty("GB链接数量")
    private Integer gbLinkCount;
    @ExcelProperty("FR链接数量")
    private Integer frLinkCount;
    @ExcelProperty("JP链接数量")
    private Integer jpLinkCount;
    @ExcelProperty("MX链接数量")
    private Integer mxLinkCount;
    @ExcelProperty("NL链接数量")
    private Integer nlLinkCount;
    @ExcelProperty("AE链接数量")
    private Integer aeLinkCount;
    @ExcelProperty("SE链接数量")
    private Integer seLinkCount;
    @ExcelProperty("BE链接数量")
    private Integer beLinkCount;
    @ExcelProperty("AU链接数量")
    private Integer auLinkCount;
    @ExcelProperty("TR链接数量")
    private Integer trLinkCount;
    @ExcelProperty("PL链接数量")
    private Integer plLinkCount;
    @ExcelProperty("BR链接数量")
    private Integer brLinkCount;
}
