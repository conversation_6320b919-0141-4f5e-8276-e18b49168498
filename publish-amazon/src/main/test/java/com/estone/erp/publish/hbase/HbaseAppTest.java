package com.estone.erp.publish.hbase;

/**
 * <AUTHOR>
 * @date 2022/11/4 16:00
 * @description
 */
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = PublishApplication.class)
//@ActiveProfiles("local")
//public class HbaseAppTest {
//
//
//    @Resource(name = "phoenixDataSource")
//    private DataSource dataSource;
//    @Resource
//    private StudentDao studentDao;
//
//    @Test
//    public void t1() throws SQLException {
//        Connection connection = dataSource.getConnection();
//        PreparedStatement preparedStatement = connection.prepareStatement("select * from student where name ='zhangsan' limit 1");
//        ResultSet resultSet = preparedStatement.executeQuery();
//        while (resultSet.next()) {
//            log.info(resultSet.getString(1) + "\t" + resultSet.getString(2));
//        }
//
//
//        Object o = studentDao.selectAll();
//        System.out.println(JSON.toJSONString(o));
//
//        o = studentDao.selectStudent("zhangsan");
//        System.out.println(JSON.toJSONString(o));
//    }
//}
