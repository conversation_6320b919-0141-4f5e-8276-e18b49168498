package com.estone.erp.publish;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.StorageClass;
import com.estone.erp.publish.common.oss.AliOss;
import org.apache.commons.lang3.RandomUtils;

import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

// Bucket 命名规范
//命名长度为 3~63 个字符
//只允许小写字母、数字、短横线（-），且不能以短横线开头或结尾
//Bucket 名称在 OSS 范围内必须全局唯一

public class CreateBucket {

    private static final String CHARS = "abcdefghijklmnopqrstuvwxyz0123456789";

    /**
     * 获取随机字符串
     * @param sampleString 样本字符串
     * @param length 生成的随机字符串长度
     * @return 返回随机字符串
     */
    public static String randomString(String sampleString, int length) {
        if (sampleString == null || sampleString.length() == 0) {
            return "";
        }
        if (length < 1) {
            length = 1;
        }
        final StringBuilder sb = new StringBuilder(length);
        int baseLength = sampleString.length();
        while (sb.length() < length) {
            //此处用ThreadLocalRandom 不用Random的感兴趣的同学可以看看这俩的区别
            //主要区别在于多线程高并发环境下 ThreadLocalRandom比Random生成随机数的速度快
            int number = ThreadLocalRandom.current().nextInt(baseLength);
            sb.append(sampleString.charAt(number));
        }
        return sb.toString();
        //;
    }


    public static void main(String[] args) throws Exception {
        //String endpoint = "https://oss-cn-hongkong.aliyuncs.com";
        String endpoint = null;
        OSS ossClient = null;
        for (int i = 0;i <= 1;i++){
            // yourEndpoint填写Bucket所在地域对应的Endpoint。
            // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
            //EnvironmentVariableCredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();

            // 填写Bucket名称。
            String bucketName = randomString(CHARS, RandomUtils.nextInt(1,10))+ "-gpsr-" + randomString(CHARS, RandomUtils.nextInt(5,10));
            // 填写资源组ID。如果不填写资源组ID，则创建的Bucket属于默认资源组。
            //String rsId = "rg-aek27tc****";
            System.out.println(bucketName);

            // 创建OSSClient实例。


            try {
                // 创建CreateBucketRequest对象。
                CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);

                // 如果创建存储空间的同时需要指定存储类型、存储空间的读写权限、数据容灾类型, 请参考如下代码。
                // 此处以设置存储空间的存储类型为标准存储为例介绍。
                createBucketRequest.setStorageClass(StorageClass.Standard);
                // 数据容灾类型默认为本地冗余存储，即DataRedundancyType.LRS。如果需要设置数据容灾类型为同城冗余存储，请设置为DataRedundancyType.ZRS。
                //createBucketRequest.setDataRedundancyType(DataRedundancyType.ZRS);
                // 设置存储空间读写权限为公共读，默认为私有。
                createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);

                // 在支持资源组的地域创建Bucket时，您可以为Bucket配置资源组。
                //createBucketRequest.setResourceGroupId(rsId);

                // 创建存储空间。
                ossClient.createBucket(createBucketRequest);
            } catch (OSSException oe) {
                System.out.println("Caught an OSSException, which means your request made it to OSS, "
                        + "but was rejected with an error response for some reason.");
                System.out.println("Error Message:" + oe.getErrorMessage());
                System.out.println("Error Code:" + oe.getErrorCode());
                System.out.println("Request ID:" + oe.getRequestId());
                System.out.println("Host ID:" + oe.getHostId());
            } catch (ClientException ce) {
                System.out.println("Caught an ClientException, which means the client encountered "
                        + "a serious internal problem while trying to communicate with OSS, "
                        + "such as not being able to access the network.");
                System.out.println("Error Message:" + ce.getMessage());
            } finally {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        }
    }
}
