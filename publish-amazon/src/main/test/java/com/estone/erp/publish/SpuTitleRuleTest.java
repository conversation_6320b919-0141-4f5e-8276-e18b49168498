package com.estone.erp.publish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.amazon.call.jsonfeed.FeedsResult;
import com.estone.erp.publish.platform.bo.SpuTitleRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/28 11:53
 * @description
 */
@Slf4j
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = PublishApplication.class)
public class SpuTitleRuleTest {

    String str = "[{\"type\":\"amazonTitle\",\"key\":\"0\",\"okCount\":0,\"totalCount\":0,\"priority\":1},{\"type\":\"amazonTitle\",\"key\":\"1\",\"okCount\":0,\"totalCount\":0,\"priority\":1},{\"type\":\"amazonTitle\",\"key\":\"2\",\"okCount\":0,\"totalCount\":0,\"priority\":1},{\"type\":\"amazonTitle\",\"key\":\"3\",\"okCount\":0,\"totalCount\":0,\"priority\":1},{\"type\":\"amazonTitle\",\"key\":\"4\",\"okCount\":0,\"totalCount\":0,\"priority\":1},{\"type\":\"longTitle\",\"key\":\"longTitle1\",\"okCount\":0,\"totalCount\":0,\"priority\":2},{\"type\":\"longTitle\",\"key\":\"longTitle2\",\"okCount\":0,\"totalCount\":0,\"priority\":2},{\"type\":\"longTitle\",\"key\":\"longTitle3\",\"okCount\":0,\"totalCount\":0,\"priority\":2},{\"type\":\"longTitle\",\"key\":\"longTitle4\",\"okCount\":0,\"totalCount\":0,\"priority\":2},{\"type\":\"longTitle\",\"key\":\"longTitle5\",\"okCount\":0,\"totalCount\":0,\"priority\":2},{\"type\":\"longTitle\",\"key\":\"longTitle6\",\"okCount\":0,\"totalCount\":0,\"priority\":2}]";
    String str2 = "[\n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle1\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1}\n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle2\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1}\n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle3\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1}\n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle4\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1}\n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle5\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1} \n" +
            "\t{\"type\":\"longTitle\", \"key\":\"longTitle6\", \"okCount\": 0, \"totalCount\": 0, \"priority\":1} \n" +
            "\t{\"type\":\"amazonTitle\", \"key\":\"0\", \"okCount\": 0, \"totalCount\": 0, \"priority\":2}\n" +
            "\t{\"type\":\"amazonTitle\", \"key\":\"1\", \"okCount\": 0, \"totalCount\": 0, \"priority\":2}\n" +
            "\t{\"type\":\"amazonTitle\", \"key\":\"2\", \"okCount\": 0, \"totalCount\": 0, \"priority\":2}\n" +
            "\t{\"type\":\"amazonTitle\", \"key\":\"3\", \"okCount\": 0, \"totalCount\": 0, \"priority\":2}\n" +
            "\t{\"type\":\"amazonTitle\", \"key\":\"4\", \"okCount\": 0, \"totalCount\": 0, \"priority\":2}\n" +
            "]";
    @Test
    public void testRule(){
        String amazon = "[{\"key\":\"longTitle6\",\"okCount\":0,\"priority\":1,\"totalCount\":0,\"type\":\"LONG_TITLE\"},{\"key\":\"0\",\"okCount\":0,\"priority\":2,\"totalCount\":0,\"type\":\"AMAZON_TITLE\"},{\"key\":\"1\",\"okCount\":0,\"priority\":2,\"totalCount\":0,\"type\":\"AMAZON_TITLE\"},{\"key\":\"2\",\"okCount\":0,\"priority\":2,\"totalCount\":0,\"type\":\"AMAZON_TITLE\"},{\"key\":\"3\",\"okCount\":0,\"priority\":2,\"totalCount\":0,\"type\":\"AMAZON_TITLE\"},{\"key\":\"4\",\"okCount\":0,\"priority\":2,\"totalCount\":0,\"type\":\"AMAZON_TITLE\"},{\"key\":\"longTitle1\",\"okCount\":8,\"priority\":1,\"totalCount\":8,\"type\":\"LONG_TITLE\"},{\"key\":\"longTitle2\",\"okCount\":8,\"priority\":1,\"totalCount\":8,\"type\":\"LONG_TITLE\"},{\"key\":\"longTitle3\",\"okCount\":8,\"priority\":1,\"totalCount\":8,\"type\":\"LONG_TITLE\"},{\"key\":\"longTitle4\",\"okCount\":8,\"priority\":1,\"totalCount\":8,\"type\":\"LONG_TITLE\"},{\"key\":\"longTitle5\",\"okCount\":8,\"priority\":1,\"totalCount\":8,\"type\":\"LONG_TITLE\"}]";
        List<SpuTitleRule> titleRules = JSON.parseObject(amazon, new TypeReference<List<SpuTitleRule>>() {
        });
        //排序
        Comparator<SpuTitleRule> byOK = Comparator.comparing(SpuTitleRule::getOkCount);//按照OkCount升序
        Comparator<SpuTitleRule> bPriority = Comparator.comparing(SpuTitleRule::getTotalCount);//按照Priority升序
        Comparator<SpuTitleRule> bTotal = Comparator.comparing(SpuTitleRule::getPriority);//按照TotalCount升序
//        Collections.sort(titleRules, byOK.thenComparing(bPriority.thenComparing(bTotal)));

        titleRules = titleRules.stream()
                .sorted(Comparator.comparing(SpuTitleRule::getOkCount).thenComparing(SpuTitleRule::getPriority).thenComparing(SpuTitleRule::getTotalCount))
                .collect(Collectors.toList());

//        log.info("{}", JSON.toJSONString(titleRules));
        StringBuilder out = new StringBuilder();
        for (SpuTitleRule rule : titleRules) {
            out.append(JSON.toJSONString(rule)+"\n");
        }
        log.info("{}", out.toString());


//        SpuOfficial spuInfo = new SpuOfficial();
//        spuInfo.setLongTitle1("1");
//        spuInfo.setLongTitle2("1");
//        Class<? extends SpuOfficial> aClass = spuInfo.getClass();
//        try {
////            Method method = aClass.getMethod("getLongTitle1");
//            Method method = aClass.getDeclaredMethod("getLongTitle1");
//            Object val = method.invoke(spuInfo);
//            String title = val == null ? "" : val.toString();
//            System.out.println("title  "+ title);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }


    @Test
    public void test() {
        String body = "{\"id\":null,\"accountNumber\":\"LISTING_PRODUCT_TITLE_FEED_DOCUMENT\",\"sellerId\":null,\"marketplaceId\":null,\"docId\":null,\"docIdTuple2Map\":{\"test15AC1111299-BL_ZYFus\":[true,\"error\"]},\"taskId\":\"**************\",\"messageId\":null,\"feedType\":\"JSON_LISTINGS_FEED\",\"systemFeedType\":\"LISTING_PRODUCT_TITLE_FEED_DOCUMENT\",\"feedId\":null,\"statusCode\":null,\"errorMsg\":null,\"requestResultTime\":null,\"finish\":true,\"status\":null,\"successTime\":*************}";
        FeedsResult feedsResult = JSON.parseObject(body, FeedsResult.class);
        log.info(feedsResult.getSystemFeedType());
    }
}
