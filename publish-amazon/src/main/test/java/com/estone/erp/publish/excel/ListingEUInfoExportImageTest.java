package com.estone.erp.publish.excel;

import com.estone.erp.publish.PublishApplication;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-10-29 下午3:17
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class ListingEUInfoExportImageTest {
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;

    private static final Map<String, String> browseNodeCacheMap = new ConcurrentHashMap<>();


    private final static String exportFilePath = "E:\\temp\\file\\amazon_eu\\";

    @Test
    @SneakyThrows
    public void testExport() {

        List<String> sites = List.of("DE", "ES", "FR", "IT","BE","NL","PL","SE");
        List<String> accountNumberList = List.of("BE-5fuyouzhang","BE-asasDErtyuiop22","BE-chenwei3ming","BE-dong14dong","BE-hongyifushi2024","BE-huangmianshen","BE-li1anzong","BE-lin_jie2024","BE-liuliuyong3","BE-lkjlaikejun","BE-wumingshan2023_10","BE-xingnan888","BE-xixixixiqunxi","BE-you_w3ei_1","DE-5fuyouzhang","DE-asasDErtyuiop22","DE-chenwei3ming","DE-dong14dong","DE-hongyifushi2024","DE-huangmianshen","DE-li1anzong","DE-lin_jie2024","DE-liuliuyong3","DE-lkjlaikejun","DE-szbodugongsi","DE-wumingshan2023_10","DE-xingnan888","DE-xixixixiqunxi","DE-you_w3ei_1","ES-5fuyouzhang","ES-asasDErtyuiop22","ES-chenwei3ming","ES-dong14dong","ES-hongyifushi2024","ES-huangmianshen","ES-li1anzong","ES-lin_jie2024","ES-liuliuyong3","ES-lkjlaikejun","ES-szbodugongsi","ES-wumingshan2023_10","ES-xingnan888","ES-xixixixiqunxi","ES-you_w3ei_1","FR-5fuyouzhang","FR-asasDErtyuiop22","FR-chenwei3ming","FR-dong14dong","FR-hongyifushi2024","FR-huangmianshen","FR-li1anzong","FR-lin_jie2024","FR-liuliuyong3","FR-lkjlaikejun","FR-szbodugongsi","FR-wumingshan2023_10","FR-xingnan888","FR-xixixixiqunxi","FR-you_w3ei_1","IT-5fuyouzhang","IT-asasDErtyuiop22","IT-chenwei3ming","IT-dong14dong","IT-hongyifushi2024","IT-huangmianshen","IT-li1anzong","IT-lin_jie2024","IT-liuliuyong3","IT-lkjlaikejun","IT-szbodugongsi","IT-wumingshan2023_10","IT-xingnan888","IT-xixixixiqunxi","IT-you_w3ei_1","NL-5fuyouzhang","NL-asasdertyuiop22","NL-chenwei3ming","NL-dong14dong","NL-hongyifushi2024","NL-huangmianshen","NL-li1anzong","NL-lin_jie2024","NL-liuliuyong3","NL-lkjlaikejun","NL-szbodugongsi","NL-wumingshan2023_10","NL-xingnan888","NL-xixixixiqunxi","NL-you_w3ei_1","PL-5fuyouzhang","PL-asasDErtyuiop22","PL-chenwei3ming","PL-dong14dong","PL-hongyifushi2024","PL-huangmianshen","PL-li1anzong","PL-lin_jie2024","PL-liuliuyong3","PL-lkjlaikejun","PL-szbodugongsi","PL-wumingshan2023_10","PL-xingnan888","PL-xixixixiqunxi","PL-you_w3ei_1","SE-5fuyouzhang","SE-asasDErtyuiop22","SE-chenwei3ming","SE-dong14dong","SE-hongyifushi2024","SE-huangmianshen","SE-li1anzong","SE-lin_jie2024","SE-liuliuyong3","SE-lkjlaikejun","SE-wumingshan2023_10","SE-xingnan888","SE-xixixixiqunxi","SE-you_w3ei_1","BE-xiaobing0621","DE-xiaobing0621","ES-xiaobing0621","FR-xiaobing0621","IT-xiaobing0621","NL-xiaobing0621","PL-xiaobing0621","SE-xiaobing0621","BE-fengq_io9","DE-fengq_io9","ES-fengq_io9","FR-fengq_io9","IT-fengq_io9","NL-fengq_io9","PL-fengq_io9","SE-fengq_io9");
        List<String> siteList  = List.of("FR", "UK", "DE", "US", "IT", "ES");
        List<String> accountNumbers = List.of("lkjlaikejun","li1anzong","szbodugongsi","xingnan888","lin_jie2024","liuliuyong3","5fuyouzhang","xixixixiqunxi","huangmianshen"
                ,"wumingshan2023_10","hongyifushi2024","dong14dong","chenwei3ming","asasDErtyuiop22","you_w3ei_1","fengq_io9","xiaobing0621");
        accountNumberList.forEach(accountNumber -> {
            siteList.forEach(site -> {
                    log.info("开始导出 {} 站点 {} 数据", site, accountNumber);
                    try {
                        export(site, accountNumber);
                    } catch (Exception e) {
                        log.error("导出 {} 站点 {} 数据失败", site, accountNumber, e);
                    }

            });
        });
        log.info("导出白底图");

    }


    private void export(String site, String accountNumber) throws IOException {

        String account = site + "-" + accountNumber;
        new File(exportFilePath + account).mkdirs();
        String filePath = exportFilePath + account  + "\\" +"白底图";

        new File(filePath).mkdirs();

        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setOrderBy("createDate");
        request.setSequence("ASC");
        request.setSite(site);
        request.setIsOnline(true);
        request.setAccountNumber(account);
        request.setFields(new String[]{ "articleNumber","mainSku"});

        int total = esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            //log.info("listings size: {}", listings.size());

            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            handleFile(listings,filePath);
        });


        try {
            TimeUnit.SECONDS.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("{},导入结束 total: {}", account, total);
    }

    public static File createFile(String filePath,String imageUrl) throws IOException {
        String imageName = getImageName(imageUrl);
        String suffix = imageUrl.substring(imageUrl.lastIndexOf("."));
        File file = new File(filePath +"//" + imageName+"." +suffix);
        try {
            FileOutputStream  os = new FileOutputStream(file);
            URL url = new URL(imageUrl);
            URLConnection con = url.openConnection();
            con.setConnectTimeout(40*1000);
            InputStream is=con.getInputStream();
            byte[] data =new byte[1024];
            int len = 0;
            while ((len = is.read(data)) != -1) {
                os.write(data, 0, len);
            }
            os.close();
            is.close();
        }
        catch (IOException e) {
            log.error("从文件系统下载失败路径" + imageUrl + e.getMessage());
            e.printStackTrace();
        }

        return file;
    }


    public void handleFile(List<EsAmazonProductListing> esAmazonProductListingList,String filePath){
        try {
            CountDownLatch countDownLatch = new CountDownLatch(esAmazonProductListingList.size());
            for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
                if (StringUtils.isBlank(esAmazonProductListing.getMainSku())) continue;
                AmazonExecutors.executeDownloadImage(()->{
                    try {
                        String mainSku  = esAmazonProductListing.getMainSku();
                        String articelNumber = esAmazonProductListing.getArticleNumber();
                        String type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
                        List<String> images = FmsUtils.getPictureUrlBySkuAndType(esAmazonProductListing.getMainSku(), type);
                        String url =  getSkuMainImage(articelNumber,images);
                        if (StringUtils.isBlank(url)) {
                            type = PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName();
                            images = FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
                            url =  getSkuMainImage(articelNumber,images);
                        }

                        if (StringUtils.isNotBlank(url)) {
                            createFile(filePath,url);
                        }else {
                            log.warn("获取文件为空" + articelNumber);
                        }

                    }catch (Exception e){
                        log.error("执行错误", e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await(1, TimeUnit.SECONDS);
        }catch (Exception e){

        }

    }

    /**
     * 获取sku同名规则图片,需要随机
     *
     * @param articleNumber articleNumber
     * @param images        产品图片池
     * @return 目标图片
     */
    public static String getSkuMainImage(String articleNumber, List<String> images) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(images)) {
            log.warn("spu获取文件夹空" + articleNumber);
            return null;
        }
        return images.stream()
                .filter(image -> image.contains(String.format("/%s.", articleNumber)))
                .findFirst().orElse(null);
    }

    /**
     *  获取图片名称
     * @param url
     * @return
     */
    public static String getImageName(String url) {
        String name = "";
        if (StringUtils.isEmpty(url)) {
            return name;
        }
        if (url.lastIndexOf("/") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1);
        }
        if (name.lastIndexOf(".") > -1) {
            name = url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."));
        }
        return name;
    }

}
