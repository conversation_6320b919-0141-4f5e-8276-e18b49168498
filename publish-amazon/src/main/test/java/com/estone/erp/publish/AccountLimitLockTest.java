package com.estone.erp.publish;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

/**
 * <AUTHOR>
 * @date 2024-08-22 下午7:24
 */
public class AccountLimitLockTest {

    public static void main(String[] args) {
        int accounts = 20;
        int tasksPerAccount = 150;
        int maxWorkersPerAccount = 20;
        int totalWorkers = 200;

        // 创建一个线程池，包含200个工人线程
        ExecutorService workerPool = Executors.newFixedThreadPool(totalWorkers);

        // 为每个账号创建一个信号量，限制同时处理任务的工人数
        List<Semaphore> accountSemaphores = new ArrayList<>();
        for (int i = 0; i < accounts; i++) {
            accountSemaphores.add(new Semaphore(maxWorkersPerAccount));
        }

        // 为每个账号分配150个任务
        for (int accountId = 0; accountId < accounts; accountId++) {
            Semaphore semaphore = accountSemaphores.get(accountId);

            for (int taskId = 0; taskId < tasksPerAccount; taskId++) {
                int finalAccountId = accountId;
                int finalTaskId = taskId;

                workerPool.submit(() -> {
                    try {
                        // 获取许可，如果达到最大并发数则等待
                        semaphore.acquire();

                        // 处理任务
                        System.out.println("Worker " + Thread.currentThread().getName() + " is processing task " +
                                finalTaskId + " for account " + finalAccountId);

                        // 模拟任务处理时间
                        Thread.sleep(100);  // 假设任务需要100ms处理

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    } finally {
                        // 释放许可
                        semaphore.release();
                    }
                });
            }
        }

        // 关闭线程池
        workerPool.shutdown();
    }


}
