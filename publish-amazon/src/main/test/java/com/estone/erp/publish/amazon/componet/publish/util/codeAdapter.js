function handleListPrice(template, schemaProperties) {
    let currency = template.getCurrency();
    let properties = JSON.parse(schemaProperties);
    let required = properties.list_price.items.required;
    // 是否存在value_with_tax
    let valueKey = required.find(item => item === "value_with_tax") ? "value_with_tax" : "value";
    if (template.getSaleVariant()) {
        // 变体数据
        let variationsStr = template.getVariations();
        let variations = JSON.parse(variationsStr);
        let result = {};
        variations.forEach(sku => {
            let extraData = sku.extraData ? JSON.parse(sku.extraData) : {};
            let price = sku.standardPrice * 1.3;
            let priceWithTax = priceNumberFormat(currency, price);
            let listPrice = [
                {
                    "currency": currency
                }
            ]
            listPrice[0][valueKey] = priceWithTax;
            extraData.list_price = listPrice;
            sku.extraData = JSON.stringify(extraData);
            result.list_price = listPrice;
        });
        template.setVariations(JSON.stringify(variations));
        return result;
    }else {
        let price = template.getStandardPrice() * 1.3;
        let priceWithTax = priceNumberFormat(currency, price);
        let listPrice = [
            {
                "currency": currency
            }
        ]
        listPrice[0][valueKey] = priceWithTax;

        let result = {};
        result.list_price = listPrice;
        return result;
    }
}
// 工具函数
const getCurrentDate = () => {
    const now = new Date();
    return now.getFullYear() + '-' +
        String(now.getMonth() + 1).padStart(2, '0') + '-' +
        String(now.getDate()).padStart(2, '0');
};

const buildKeyValueStructure = (key, value) => {
    let result = {};
    result[key] = [{ "value": value }];
    return result;
};

const priceNumberFormat = (currency, listPrice) => {
    if (currency === 'JPY') {
        // 日本站点取整数
        return Math.floor(listPrice);
    } else {
        return listPrice.toFixed(2).slice(0, -1) + '9';
    }
};

// 制造商编号处理
function handlePartNumber(template) {
    const brand = template.getBrand();
    if (brand && brand.trim() !== '') {
        return buildKeyValueStructure("part_number", brand);
    }
    return null;
}

// 日期相关属性处理
function handleDateAttribute(attrName) {
    return buildKeyValueStructure(attrName, getCurrentDate());
}

// 关键字相关属性处理
function handleKeywordAttribute(attrName, productType) {
    if (productType && productType.trim() !== '') {
        const keyword = productType.toLowerCase().replace(/_/g, " ");
        return buildKeyValueStructure(attrName, keyword);
    }
    return null;
}

// 体积处理
function handleCapacity(template, skuInfoMap) {
    let result = {};
    const variations = template.getSaleVariant() ?
        JSON.parse(template.getVariations()) : [{ sku: template.getParentSku() }];

    variations.forEach(sku => {
        const skuInfo = skuInfoMap[sku.sku];
        if (skuInfo) {
            const capacity = (skuInfo.getPackLength() * skuInfo.getPackWidth() * skuInfo.getPackHeight()) / 1000;
            const capacityLiters = Math.round(capacity * 100) / 100;

            result["capacity"] = [{
                "value": capacityLiters,
                "unit": "liters"
            }];

            updateSkuExtraData(template, sku, "capacity", result.capacity);
        }
    });

    updateVariationsIfNeeded(template, variations);
    return result;
}

// 重量处理
function handleItemPackageWeight(template, skuInfoMap) {
    let result = {};
    const variations = template.getSaleVariant() ?
        JSON.parse(template.getVariations()) : [{ sku: template.getParentSku() }];

    variations.forEach(sku => {
        const skuInfo = skuInfoMap[sku.sku];
        if (skuInfo) {
            const weightLB = Math.round(skuInfo.getPackageWeight() * 0.00220462 * 100) / 100;
            result["item_package_weight"] = [{
                "value": weightLB,
                "unit": "pounds"
            }];

            updateSkuExtraData(template, sku, "item_package_weight", result.item_package_weight);
        }
    });

    updateVariationsIfNeeded(template, variations);
    return JSON.stringify(result);
}

// 尺寸处理（包装尺寸和产品尺寸通用）
function handleDimensions(template, skuInfoMap, dimensionType) {
    let result = {};
    const variations = template.getSaleVariant() ?
        JSON.parse(template.getVariations()) : [{ sku: template.getParentSku() }];

    variations.forEach(sku => {
        const skuInfo = skuInfoMap[sku.sku];
        if (skuInfo) {
            const cm2In = 0.393701;
            const dimensions = {
                "length": {
                    "value": Math.round(skuInfo.getPackLength() * cm2In * 100) / 100,
                    "unit": "inches"
                },
                "width": {
                    "value": Math.round(skuInfo.getPackWidth() * cm2In * 100) / 100,
                    "unit": "inches"
                },
                "height": {
                    "value": Math.round(skuInfo.getPackHeight() * cm2In * 100) / 100,
                    "unit": "inches"
                }
            };

            result[dimensionType] = [dimensions];
            updateSkuExtraData(template, sku, dimensionType, result[dimensionType]);
        }
    });

    updateVariationsIfNeeded(template, variations);
    return JSON.stringify(result);
}

// 辅助函数：更新SKU的extraData
function updateSkuExtraData(template, sku, key, value) {
    if (template.getSaleVariant()) {
        let extraData = sku.extraData ? JSON.parse(sku.extraData) : {};
        extraData[key] = value;
        sku.extraData = JSON.stringify(extraData);
    }
}

function updateVariationsIfNeeded(template, variations) {
    if (template.getSaleVariant()) {
        // template.setVariations(JSON.stringify(variations));
    }
}

function handleRecommendedBrowseNodes(template) {
    let categoryId = template.getCategoryId();
    if (categoryId && categoryId.trim() !== '') {
        return buildKeyValueStructure("recommended_browse_nodes", categoryId);
    }
    return null;
}

function handleUnitCount(template, schemaProperties) {
    // 将JSON字符串格式的schema属性解析为JavaScript对象
    const schema = JSON.parse(schemaProperties);
    // 获取unit_count相关的schema定义
    const unitCountSchema = schema.unit_count;

    // 检查是否存在enum
    const hasEnum = unitCountSchema?.items?.properties?.type?.properties?.value?.enum || unitCountSchema?.items?.properties?.type?.properties?.value?.anyOf

    // 设置默认值为1，如果提供了countValue则使用它
    const value = 1;

    let result = {
        "unit_count": [
            {
                "value": value
            }
        ]
    };
    let languageTag = template.getLanguageTag();
    // 当存在enum时
    if (hasEnum) {
        const enumValues = unitCountSchema.items.properties.type.properties.value.enum || unitCountSchema.items.properties.type.properties.value.anyOf[1].enum
        // 优先匹配count值，如果enum中存在"count"则使用它，否则使用第一个元素
        const countIndex = enumValues.findIndex(val => val.toLowerCase() === 'count')
        const defaultType = countIndex !== -1 ? enumValues[countIndex] : enumValues[0]

        result.unit_count[0].type = {
            "value": defaultType,
            "language_tag": languageTag
        };
    } else {
        // 当不存在enum时使用默认值"count"
        result.unit_count[0].type = {
            "value": "count",
            "language_tag": languageTag
        };
    }

    return JSON.stringify(result);
}


function requiredAttributeAdaptation(attrName, attrValue, template, skuInfoMap, schemaProperties) {
    switch(attrName) {
        case "list_price":
            return handleListPrice(template, schemaProperties);
        case "part_number":
            return handlePartNumber(template);
        case "unit_count":
            return handleUnitCount(template, schemaProperties);
        case "street_date":
        case "product_site_launch_date":
        case "deprecated_offering_start_date":
            return handleDateAttribute(attrName);

        case "subject_keyword":
        case "item_type_name":
        case "item_type_keyword":
            return handleKeywordAttribute(attrName, template.getProductType());
        case "capacity":
            return handleCapacity(template, skuInfoMap);

        case "item_package_weight":
            return handleItemPackageWeight(template, skuInfoMap);

        case "item_package_dimensions":
        case "item_dimensions":
            return handleDimensions(template, skuInfoMap, attrName);
        case "recommended_browse_nodes":
            return handleRecommendedBrowseNodes(template);
        default:
            return null;
    }
}
