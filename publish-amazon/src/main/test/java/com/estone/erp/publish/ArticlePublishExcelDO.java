package com.estone.erp.publish;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-04-12 15:33
 */
@Data
public class ArticlePublishExcelDO {
    // SKU，分类，进入管理单品时间，单品状态（正常），禁售平台，US站点数量汇总，DE站点数量汇总
    @ExcelProperty("SKU")
    private String sku;
    @ExcelProperty("分类")
    private String category;
    @ExcelProperty("进入管理单品时间")
    private String manageTime;
    @ExcelProperty("单品状态")
    private String status;
    @ExcelProperty("禁售平台")
    private String disablePlatform;
    @ExcelProperty("US站点数量汇总")
    private Integer usSiteCount;
    @ExcelProperty("DE站点数量汇总")
    private Integer deSiteCount;
}
