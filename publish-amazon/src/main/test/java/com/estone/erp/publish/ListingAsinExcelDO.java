package com.estone.erp.publish;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @date 2024-12-12 11:11
 */
@Data
public class ListingAsinExcelDO {
    @ExcelProperty("ASIN")
    private String asin;
    @ExcelProperty("店铺")
    private String accounts;
    @ExcelProperty(value = "站点")
    private String sites;


    public void addAccounts(String account) {
        if (accounts == null) {
            accounts = account;
        } else {
            String[] split = accounts.split(",");
            HashSet<String> accountsSet = new HashSet<>(Arrays.asList(split));
            String join = String.join(",", accountsSet);
            setAccounts(join);
        }
    }

    public void addSites(String site) {
        if (sites == null) {
            sites = site;
        } else {
            String[] split = sites.split(",");
            HashSet<String> sitesSet = new HashSet<>(Arrays.asList(split));
            String join = String.join(",", sitesSet);
            setSites(join);
        }
    }
}
