package com.estone.erp.publish.amazon.componet.publish.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr;
import com.estone.erp.publish.amazon.model.dto.TemplateBasicProductInfoVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AttributeScriptExecutor测试类
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Slf4j
public class AttributeScriptExecutorTest {

    private AmazonTemplateBO templateBO;
    private Map<String, Object> extraDataMap;
    private Map<String, TemplateBasicProductInfoVO.SubSkuInfo> skuInfoMap;
    private JSONObject properties;

    @Before
    public void setUp() {
        // 初始化测试数据
        templateBO = new AmazonTemplateBO();
        extraDataMap = new HashMap<>();
        skuInfoMap = new HashMap<>();
        properties = new JSONObject();
    }



    /**
     * 测试使用模板数据
     */
    @Test
    public void testExecuteAttributeScriptWithTemplateData() {
        String schemaProperties = getSchemaProperties();
        JSONObject jsonObject = JSON.parseObject(schemaProperties);
        properties = jsonObject.getJSONObject("properties");

        String tempalteJson = "{}";

        AmazonTemplateBO amazonTemplateBO = JSON.parseObject(tempalteJson, AmazonTemplateBO.class);
        templateBO = amazonTemplateBO;

//        templateBO.setTitle("Test Product");
//        templateBO.setSaleVariant(true);
//        templateBO.setCurrency("GPB");
//        templateBO.setCountry("UK");
//        templateBO.setVariations("[{\"condition\":\"New\",\"conditionNote\":\"\",\"extraImages\":\"[\\\"http://************:8888/amazon/2025-05/14-09-15-06-885/8NB902482-effect.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-46-738/8NB902482-4.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-51-092/8NB902482-6.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-15-12-826/8NB902482-GN.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-57-296/8NB902482-9.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-36-600/8NB902482-1.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-15-14-666/8NB902482.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-15-10-976/8NB902482-GN-000.jpg\\\"]\",\"extraImagesList\":[\"http://************:8888/amazon/2025-05/14-09-15-06-885/8NB902482-effect.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-46-738/8NB902482-4.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-51-092/8NB902482-6.jpg\",\"http://************:8888/amazon/2025-05/14-09-15-12-826/8NB902482-GN.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-57-296/8NB902482-9.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-36-600/8NB902482-1.jpg\",\"http://************:8888/amazon/2025-05/14-09-15-14-666/8NB902482.jpg\",\"http://************:8888/amazon/2025-05/14-09-15-10-976/8NB902482-GN-000.jpg\"],\"gpsrImage\":\"\",\"mainImage\":\"http://************:8888/amazon/2025-05/14-09-15-12-826/8NB902482-GN.jpg\",\"name\":\"8NB902482-GN\",\"quantity\":500,\"repeatFlag\":false,\"sampleImage\":\"http://************:8888/amazon/2025-05/14-09-15-12-826/8NB902482-GN.jpg\",\"sellerSKU\":\"9_8NB902482-GN_Zoe1\",\"shippingCost\":0.0,\"shippingGroup\":\"\",\"sku\":\"8NB902482-GN\",\"skulifecyclephase\":\"Normal\",\"standardPrice\":11.0,\"standardProdcutIdType\":\"EAN\",\"standardProdcutIdValue\":\"7656070000223\",\"variantAttribute\":{\"color\":[{\"marketplace_id\":\"A1F83G8C2ARO7P\",\"language_tag\":\"en_GB\",\"value\":\"greenA\"}]}},{\"condition\":\"New\",\"conditionNote\":\"\",\"extraImages\":\"[\\\"http://************:8888/amazon/2025-05/14-09-15-06-885/8NB902482-effect.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-42-832/8NB902482-2.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-15-03-215/8NB902482-BL.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-51-092/8NB902482-6.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-38-761/8NB902482-10.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-48-872/8NB902482-5.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-57-296/8NB902482-9.jpg\\\",\\\"http://************:8888/amazon/2025-05/14-09-14-36-600/8NB902482-1.jpg\\\"]\",\"extraImagesList\":[\"http://************:8888/amazon/2025-05/14-09-15-06-885/8NB902482-effect.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-42-832/8NB902482-2.jpg\",\"http://************:8888/amazon/2025-05/14-09-15-03-215/8NB902482-BL.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-51-092/8NB902482-6.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-38-761/8NB902482-10.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-48-872/8NB902482-5.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-57-296/8NB902482-9.jpg\",\"http://************:8888/amazon/2025-05/14-09-14-36-600/8NB902482-1.jpg\"],\"gpsrImage\":\"\",\"mainImage\":\"http://************:8888/amazon/2025-05/14-09-15-01-360/8NB902482-BL-000.jpg\",\"name\":\"8NB902482-BL\",\"quantity\":500,\"repeatFlag\":false,\"sampleImage\":\"http://************:8888/amazon/2025-05/14-09-15-01-360/8NB902482-BL-000.jpg\",\"sellerSKU\":\"8_8NB902482-BL_Zoe1\",\"shippingCost\":0.0,\"shippingGroup\":\"\",\"sku\":\"8NB902482-BL\",\"skulifecyclephase\":\"Normal\",\"standardPrice\":21.0,\"standardProdcutIdType\":\"EAN\",\"standardProdcutIdValue\":\"7656070000230\",\"variantAttribute\":{\"color\":[{\"marketplace_id\":\"A1F83G8C2ARO7P\",\"language_tag\":\"en_GB\",\"value\":\"B\"}]}}]");

//        PropertiesTranslationCache.loadSiteTranslateCache("JP", List.of("as shown in the image", "not water resistant"));
        // 创建测试属性，脚本使用模板数据
        ProductTypeTemplateJsonAttr attr = new ProductTypeTemplateJsonAttr();
        attr.setAttributeName("weave_type");
        attr.setAttributeValue("as shown in the image");

        ProductTypeTemplateJsonAttr attr2 = new ProductTypeTemplateJsonAttr();
        attr2.setAttributeName("water_resistance_level");
        attr2.setAttributeValue("not water resistant");


        ProductTypeTemplateJsonAttr attr3 = new ProductTypeTemplateJsonAttr();
        attr3.setAttributeName("list_price");
        attr3.setAttributeValue("as shown in the image");

        // 读取本地脚本文件
        String scriptData = readScriptFile("D:\\workspace\\estone\\publish\\publish-amazon\\src\\main\\test\\java\\com\\estone\\erp\\publish\\amazon\\componet\\publish\\util\\codeAdapter.js");
        // 执行脚本
        AttributeScriptExecutor.executeAttributeScript(templateBO, extraDataMap, List.of(attr, attr2, attr3), scriptData, skuInfoMap, properties);
        log.info("Execute result: {}", JSON.toJSONString(extraDataMap));
    }

    @SneakyThrows
    private String getSchemaProperties() {
        String schemaUrl = "http://**********/proxyimg/productType/US/US_AUTO_ACCESSORY.json";
        HttpClient client = HttpClient
                .newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .connectTimeout(Duration.ofSeconds(20))
                .build();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(schemaUrl))
                .timeout(Duration.ofMinutes(2))
                .header("Content-Type", "application/json")
                .GET().build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        return response.body();
    }

    @SneakyThrows
    public String readScriptFile(String fileName) {
        StringBuilder data = new StringBuilder();
        try (BufferedReader bufferedReader = new BufferedReader(new FileReader(fileName))) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                data.append(line).append("\n");
            }
        }
        return data.toString();
    }
}