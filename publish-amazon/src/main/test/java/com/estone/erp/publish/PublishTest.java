package com.estone.erp.publish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.biz.ExportProductSkuImageProcesser;
import com.estone.erp.publish.amazon.bo.AmazonAccountRelationBO;
import com.estone.erp.publish.amazon.bo.AmazonExtralData;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.call.process.submit.TemplateSubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.componet.AmazonTemplateInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishHandler;
import com.estone.erp.publish.amazon.componet.scheduler.model.AmazonUpdatePriceDataMessage;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.jobHandler.AmazonSalesDataStatisticsJobHandler;
import com.estone.erp.publish.amazon.jobHandler.RefreshProductMsgToAmazonListingJobHandler;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonListingUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.amazon.util.model.SkuTheme;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.SalesStatisticsRoleTypeEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.model.dto.SalesDailyDataDO;
import com.estone.erp.publish.common.model.dto.ShopMonthTotalDataDO;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.EsCompositeAggsResponse;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopStatisticsData;
import com.estone.erp.publish.elasticsearch2.service.EsShopStatisticsDataService;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.order.OrderClient;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingGpsrInfoService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.catalogItems.Item;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.groovy.util.Maps;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.client.Client;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.estone.erp.publish.amazon.util.SkuThemeUtils.parseProductAttrs;
import static org.elasticsearch.common.xcontent.XContentFactory.jsonBuilder;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class PublishTest {
    private final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(5, 5, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private final Semaphore SEMAPHORE = new Semaphore(5);
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;

    @Autowired
    private RefreshProductMsgToAmazonListingJobHandler refreshProductMsgToAmazonListingJobHandler;

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private NewUsermgtClient newUsermgtClient;

    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Autowired
    private AmazonTemplateInfringementWordHelper infringementWordHelper;
    @Autowired
    private AmazonSalesDataStatisticsJobHandler amazonSalesDataStatisticsJobHandler;
    @Autowired
    private EsShopStatisticsDataService esShopStatisticsDataService;
    @Autowired
    private IAmazonListingGpsrInfoService iAmazonListingGpsrInfoService;
    @Autowired
    private TemplateSubmitFeedXmlStrategy templateSubmitFeedXmlStrategy;
    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private AmazonPublishImagePathService amazonPublishImagePathService;
    @Autowired
    private AmazonPublishOperationLogService amazonPublishOperationLogService;
    @Autowired
    private IAmazonListingInfoService amazonListingInfoService;
    @Autowired
    private SaleAccountService saleAccountService;
    @Autowired
    private AmazonPublishHandler amazonPublishHandler;


    @Test
    @SneakyThrows
    public void testDownloadImage() {
        AmazonAccount account = amazonAccountService
                .queryAmazonAccountByAccountNumber("US-y-y-z-25-03");

        ExportProductSkuImageProcesser exportProductSkuImageProcesser = new ExportProductSkuImageProcesser(account, List.of("9SD104326"), "2", "");
        String fileName = "amazon-Image-" + account.getAccountNumber().trim() + "-" + System.currentTimeMillis() + ".xlsx";
        File file = new File(fileName.trim());
        if (file.exists()) {
            file.delete();
        }
        OutputStream os = new FileOutputStream(file, true);
        exportProductSkuImageProcesser.exportAmazonImages(os);


    }


//    private void syncStatisticsOnlineAsin(List<String> sonAsinList, Map<String, String> merchantMap) {
//        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
//        request.setSonAsinList(sonAsinList);
//        request.setFields(new String[]{"accountNumber", "site", "parentAsin", "sonAsin", "isOnline", "itemType", "sellerSku", "createDate"});
//        List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);
//        if (CollectionUtils.isEmpty(esAmazonProductListing)) {
//            return;
//        }
//        Map<String, List<EsAmazonProductListing>> asinMap = esAmazonProductListing.stream().collect(Collectors.groupingBy(EsAmazonProductListing::getSonAsin));
//        asinMap.forEach((sonAsin, esAmazonProductListings) -> {
//            SEMAPHORE.acquireUninterruptibly();
//            THREAD_POOL_EXECUTOR.execute(() -> {
//                AmazonAsin amazonAsin = new AmazonAsin();
//                amazonAsin.setAsin(sonAsin);
//                try {
//                    EsAmazonProductListing listing = esAmazonProductListings.get(0);
//                    String merchantId = merchantMap.get(listing.getAccountNumber());
//                    if (StringUtils.isEmpty(merchantId)) {
//                        return;
//                    }
//
//                    amazonAsin.setMerchantId(merchantId);
//                    List<Map<String, Object>> siteArray = new ArrayList<>();
//                    esAmazonProductListings.forEach(item -> {
//                        if (StringUtils.isEmpty(amazonAsin.getParentAsin()) && StringUtils.isNotEmpty(item.getParentAsin())) {
//                            amazonAsin.setParentAsin(item.getParentAsin());
//                        }
//                        if (amazonAsin.getType() == null && item.getItemType() != null) {
//                            amazonAsin.setType(item.getItemType());
//                        }
//                        LocalDateTime createdTime = LocalDateTimeUtil.of(item.getCreateDate());
//                        if (amazonAsin.getCreatedTime() == null || createdTime.isBefore(amazonAsin.getCreatedTime())) {
//                            amazonAsin.setCreatedTime(createdTime);
//                        }
//                        siteArray.add(Maps.of("site", item.getSite(), "isOnline", item.getIsOnline()));
//                    });
//                    amazonAsin.setSiteArray(JSON.toJSONString(siteArray));
//                    amazonAsin.setUpdatedTime(LocalDateTime.now());
//                    amazonAsinService.save(amazonAsin);
//                } catch (Exception e) {
//                    log.error("保存asin失败->{}, {}", sonAsin, JSON.toJSONString(amazonAsin), e);
//                } finally {
//                    SEMAPHORE.release();
//                }
//            });
//        });
//    }

    @Test
    public void testTemplatePublish() {
        // ********* ********* *********
        // Naliyua
        AmazonTemplateBO templateBO = amazonTemplateService.selectBoById(*********, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        String accountNumber = templateBO.getSellerId();
        AmazonAccount amazonAccount = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
        String publishImagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(accountNumber);

        PublishData<AmazonTemplateBO> publishData = new PublishData<>();
        publishData.setUnitDatas(Collections.singletonList(templateBO));
        publishData.setAccount(amazonAccount);
        publishData.setImagePath(publishImagePath);

//        publishData.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
//        String product2Xml = templateSubmitFeedXmlStrategy.transferProduct2Xml(publishData);
//        log.info("product2Xml -> {}", product2Xml);

        publishData.setFeedType(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue());
        String image2Xml = templateSubmitFeedXmlStrategy.transferProductImage2Xml(publishData);
        log.info("image2Xml -> {}", image2Xml);

//        publishData.setFeedType(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue());
//        String price2Xml = templateSubmitFeedXmlStrategy.transferProductPrice2Xml(publishData);
//        log.info("price2Xml -> {}", price2Xml);

//        publishData.setFeedType(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());
//        String inventory2Xml = templateSubmitFeedXmlStrategy.transferProductInventory2Xml(publishData);
//        log.info("inventory2Xml -> {}", inventory2Xml);

//        publishData.setFeedType(SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue());
//        String productRelationship2Xml = templateSubmitFeedXmlStrategy.transferProductRelationship2Xml(publishData);
//        log.info("productRelationship2Xml -> {}", productRelationship2Xml);

    }

    @Test
    public void testUpdateWrapper() {
        AmazonListingGpsrInfo entity = iAmazonListingGpsrInfoService.getById("6192449487634445");
        if (entity != null) {
            updateRecordExtra(entity, Maps.of("reportId", "10086", "reportMsg", "中国移动"));
            LambdaUpdateWrapper<AmazonListingGpsrInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(AmazonListingGpsrInfo::getUpload, true);
            updateWrapper.set(AmazonListingGpsrInfo::getUpdatedTime, LocalDateTime.now());
            updateWrapper.set(AmazonListingGpsrInfo::getExtra, entity.getExtra());
            updateWrapper.eq(AmazonListingGpsrInfo::getId, entity.getId());
            iAmazonListingGpsrInfoService.update(null, updateWrapper);
        }
    }

    private void updateRecordExtra(AmazonListingGpsrInfo amazonListingGpsrInfo, Map<String, Object> extra) {
        String extraJson = amazonListingGpsrInfo.getExtra();
        if (StringUtils.isBlank(extraJson)) {
            String extraData = JSON.toJSONString(extra);
            amazonListingGpsrInfo.setExtra(extraData);
            return;
        }
        Map<String, Object> extraMap = JSON.parseObject(extraJson, Map.class);
        Object sourceType = extra.get("sourceType");
        if (sourceType != null) {
            String type = sourceType.toString();
            if ("FBA".equals(type)) {
                extraMap.put("isFBA", true);
            }
        }
        extraMap.putAll(extra);

        String extraData = JSON.toJSONString(extraMap);
        amazonListingGpsrInfo.setExtra(extraData);
    }

    @Test
    public void testThreadPool() throws InterruptedException {
//        submitSubmitFeedTask

        log.info("开始xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
        AtomicInteger atomicIndex = new AtomicInteger(0);

        AmazonExecutors.submitSubmitFeedTask(new Consumer<ResponseJson>() {
            @Override
            public void accept(ResponseJson responseJson) {
                while (true) {
                    AmazonExecutors.submitSubmitFeedTask(new Consumer<ResponseJson>() {
                        @Override
                        public void accept(ResponseJson responseJson) {
                            log.info("打印一下线程,当前线程序号->{}, -> {}", atomicIndex.incrementAndGet(), Thread.currentThread());
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    });

                    if (atomicIndex.get() > 1000) {
                        break;
                    }

                    log.info("completed--->{}", AmazonExecutors.SUBMIT_TASK_POOL.getCompletedTaskCount());
                }
            }
        });


        Thread.sleep(100000L);
    }



    @Test
    public void execProductTest(){
//        List<String> stopAndArchived = ProductServiceUtils.findStopAndArchived(Arrays.asList("9SD800035-39", "9SD800035-33", "8YY700438", "5AC703896-PK", "11AG800026-Y-L", "3TK300681-A"));
//        log.info(ToStringBuilder.reflectionToString(stopAndArchived));
//        ResponseJson skuInfos = ProductUtils.findSkuInfos(Arrays.asList("9SD800035-39", "9SD800035-33", "8YY700438", "5AC703896-PK", "11AG800026-Y-L", "3TK300681-A"));
//        log.info(ToStringBuilder.reflectionToString(skuInfos));
    }

    @Test
    public void testAccountRe(){
        List<AmazonAccountRelationBO> relations = new ArrayList<>();

        // 根据销售排序
        relations =  relations.stream().sorted(Comparator.comparing(o -> o.getSaleIds().get(0))).collect(Collectors.toList());
    }

    @Test
    public void ListingAmount(){
        EsAmazonProductListingService esAmazonProductListingService = SpringUtils.getBean(EsAmazonProductListingService.class);
        // 销售和账号
        List<String> saleIds = new ArrayList<>();
        saleIds.add("15539");
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleIds(saleIds);
        request.setSaleChannel("Amazon");
        List<String> saleIdAccountList = EsAccountUtils.getSaleIdAndAccountByEs(request);
        System.out.println("==================回收账号下店铺总条数=========================");
        System.out.println(saleIdAccountList.size());
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        String [] fields = {"id"};
        esAmazonProductListingRequest.setFields(fields);
        esAmazonProductListingRequest.setAccountNumberList(saleIdAccountList);
        esAmazonProductListingRequest.setIsOnline(true);
        esAmazonProductListingRequest.setInfringementTypename("侵权");
        esAmazonProductListingRequest.setForbidChannel("Amazon");
        Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest, 2, 0);
        System.out.println("==================回收账号总条数=========================");
        System.out.println(page.getContent().size());
    }

    @Test
    public void testSaveAmazonProductListing(){
        ElasticsearchTemplate elasticsearchTemplate = SpringUtils.getBean(ElasticsearchTemplate.class);
            try {
                long begin = System.currentTimeMillis();
                Client client = elasticsearchTemplate.getClient();
                EsAmazonProductListing esAmazonProductListing = new EsAmazonProductListing();
                esAmazonProductListing.setId("US-pengyang5_xllLU_5AC502318-SR_94");
                esAmazonProductListing.setUpdateDate(new Date());
                esAmazonProductListing.setIsOnline(true);
                esAmazonProductListing.setQuantity(1000);
                List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
                esAmazonProductListingList.add(esAmazonProductListing);
                BulkRequestBuilder bulkRequest = client.prepareBulk();
                for (EsAmazonProductListing amazonProductListing : esAmazonProductListingList) {
                    bulkRequest.add(client.prepareUpdate("amazon_product_listing", "esAmazonProductListing", amazonProductListing.getId()).setDoc(jsonBuilder()
                            .startObject()
                            .field("updateDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(amazonProductListing.getUpdateDate()))
                            .field("isOnline", amazonProductListing.getIsOnline())
                            .field("quantity", amazonProductListing.getQuantity())
                            .endObject()));
                }
                BulkResponse bulkResponse = bulkRequest.get();
                if (bulkResponse.hasFailures()) {
                    throw new BusinessException(bulkResponse.buildFailureMessage());
                }
//                log.info("更新刊登数据耗时" + bulkResponse.getTookInMillis() + ";执行时间为："
//                        + ((System.currentTimeMillis() - begin) / 1000) + "s");
            } catch (Exception e) {
                log.error(e.getMessage() + "更新刊登数据失败", e);
                throw new BusinessException(e.getMessage() + "更新刊登数据失败");
        }
    }

    @Test
    public void onlineNumber() {
        // 通过ES查询对应店铺在线商品数
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setIsOnline(Boolean.TRUE);
        request.setAccountNumberList(Arrays.asList("UK-lodfpsj4as"));
        // kv: accountNumber:sellerSkuNum
        Map<String, Integer> accountSellerSkuSumMap = esAmazonProductListingService.getAccountSellerSkuSumMap(request);
        log.info("{}", JSON.toJSONString(accountSellerSkuSumMap));
    }

    @Test
    public void updateListing() {
        refreshProductMsgToAmazonListingJobHandler.updateListing("5AC1102438-DB");
    }

    @Test
    public void userTest() {
//        ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo("181613");
//        //{"connectionRefused":false,"result":{"employeeId":15414,"employeeNo":"181613","name":"肖化鑫","positionName":"JAVA开发工程师","rsRoleNames":["刊登-超级管理员","超级管理员"]},"success":true}
//        log.info(JSON.toJSONString(userByNo));
//        NewUser result = userByNo.getResult();
//        result.getRsRoleNames().add("编辑主管");
//        Boolean wenanSupervisor = NewUsermgtUtils.isWenanSupervisor(result);
//        log.info("文案：{}",wenanSupervisor);
        List<String> bian = Arrays.asList("文案组长（产品开发部）");
//        List<String> strings = Arrays.asList("文案组长（产品开发部）","文案组长（西安文案）","文案","文案编辑");
        ApiResult<List<NewUser>> roleNames = NewUsermgtUtils.listUserByRoleNames(bian);
        log.info("EmployeeNos {}",JSON.toJSONString(roleNames.getResult()));
        // 4268
        Map<String, Object> argsMap = new HashMap<>();
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("employeeNo","107");
        argsMap.put("args", JSON.toJSONString(reqMap));
        ApiResult<?> result = newUsermgtClient.listTeamLeaderByEmployeeNo(JSON.toJSONString(argsMap));
        log.info("上下级：{}",JSON.toJSONString(result));
//        amazonAccountRelationService.syncAccount();
    }

    @Test
    public void testProductTheme() {
        //        String attrs = "[{\"cnName\":\"颜色\",\"cnValue\":\"米色\",\"enName\":\"Color\",\"enValue\":\"Beige\"},{\"cnName\":\"尺码\",\"cnValue\":\"L\",\"enName\":\"Size\",\"enValue\":\"L\"}]";
//        System.out.println("attrs---"+resolveAttrValue(attrs, "Color") + ":" +resolveAttrValue(attrs, "Size"));
//
//        String attrs1 = "[{\"cnName\":\"颜色\",\"cnValue\":\"米色\",\"enName\":\"1111\",\"enValue\":\"Beige\"},{\"cnName\":\"尺码\",\"cnValue\":\"L\",\"enName\":\"2222\",\"enValue\":\"L\"}]";
//        System.out.println("attrs1---"+resolveAttrValue(attrs1, "Color") + ":" +resolveAttrValue(attrs1, "Size"));
//
//        String productJson = "[{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-22-638/8ZZ800155-W-XL.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Discard\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-米色-XL\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"颜色\\\",\\\"cnValue\\\":\\\"米色\\\",\\\"enName\\\":\\\"Color\\\",\\\"enValue\\\":\\\"Beige\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"cnValue\\\":\\\"XL\\\",\\\"enName\\\":\\\"Size\\\",\\\"enValue\\\":\\\"XL\\\"}]\",\"saleForbiddenList\":[\"Wish\"],\"salesProhibition\":\"[{\\\"plat\\\":\\\"Wish\\\",\\\"sites\\\":[{\\\"isAuthority\\\":0,\\\"site\\\":\\\"CN\\\"},{\\\"isAuthority\\\":0,\\\"site\\\":\\\"US\\\"}]}]\",\"salesProhibitionsVos\":[{\"plat\":\"Wish\",\"sites\":[{\"isAuthority\":0,\"site\":\"CN\"},{\"isAuthority\":0,\"site\":\"US\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"XL\\\",\\\"Color\\\":\\\"Beige\\\"}\",\"shopifyAttrOption\":\"Size,Color\",\"singleSource\":4,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-W-XL\",\"specialTypeList\":[1007,2002],\"tag\":\"普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0},{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"liquid,normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-22-723/8ZZ800155-W-M.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Normal\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-米色-M\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"材质\\\",\\\"enName\\\":\\\"Material\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"尺寸\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"颜色\\\",\\\"enName\\\":\\\"Color\\\",\\\"cnValue\\\":\\\"米色\\\",\\\"enValue\\\":\\\"Beige\\\"},{\\\"cnName\\\":\\\"型号\\\",\\\"enName\\\":\\\"1\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"M\\\",\\\"enValue\\\":\\\"M\\\"},{\\\"cnName\\\":\\\"服装尺码\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"光线颜色\\\",\\\"enName\\\":\\\"Emitting Color\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"}]\",\"saleForbiddenList\":[\"虾皮\",\"Amazon\"],\"salesProhibition\":\"[{\\\"sites\\\":[{\\\"site\\\":\\\"ID\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MY\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"BR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TW\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"VN\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"虾皮\\\"},{\\\"sites\\\":[{\\\"site\\\":\\\"IT\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"FR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"ES\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AU\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"UK\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"US\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"CA\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"NL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"DE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"IN\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"JP\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"Amazon\\\"}]\",\"salesProhibitionsVos\":[{\"plat\":\"虾皮\",\"sites\":[{\"isAuthority\":0,\"site\":\"ID\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"MY\"},{\"isAuthority\":0,\"site\":\"BR\"},{\"isAuthority\":0,\"site\":\"TW\"},{\"isAuthority\":0,\"site\":\"TH\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"PH\"},{\"isAuthority\":0,\"site\":\"VN\"}]},{\"plat\":\"Amazon\",\"sites\":[{\"isAuthority\":0,\"site\":\"IT\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"FR\"},{\"isAuthority\":0,\"site\":\"ES\"},{\"isAuthority\":0,\"site\":\"AU\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"UK\"},{\"isAuthority\":0,\"site\":\"PL\"},{\"isAuthority\":0,\"site\":\"US\"},{\"isAuthority\":0,\"site\":\"CA\"},{\"isAuthority\":0,\"site\":\"NL\"},{\"isAuthority\":0,\"site\":\"DE\"},{\"isAuthority\":0,\"site\":\"AE\"},{\"isAuthority\":0,\"site\":\"IN\"},{\"isAuthority\":0,\"site\":\"JP\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"\\\",\\\"Color\\\":\\\"Beige\\\",\\\"Material\\\":\\\"\\\"}\",\"shopifyAttrOption\":\"Size,Color,Material\",\"singleSource\":3,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-W-M\",\"specialTypeList\":[1007,2002],\"tag\":\"带液体,普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0},{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-21-864/8ZZ800155-W-L.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Normal\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-米色-L\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"颜色\\\",\\\"cnValue\\\":\\\"米色\\\",\\\"enName\\\":\\\"Color\\\",\\\"enValue\\\":\\\"Beige\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"cnValue\\\":\\\"L\\\",\\\"enName\\\":\\\"Size\\\",\\\"enValue\\\":\\\"L\\\"}]\",\"saleForbiddenList\":[\"虾皮\",\"Amazon\"],\"salesProhibition\":\"[{\\\"sites\\\":[{\\\"site\\\":\\\"ID\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MY\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"BR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TW\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"VN\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"虾皮\\\"},{\\\"sites\\\":[{\\\"site\\\":\\\"IT\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"FR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"ES\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AU\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"UK\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"US\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"CA\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"NL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"DE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"IN\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"JP\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"Amazon\\\"}]\",\"salesProhibitionsVos\":[{\"plat\":\"虾皮\",\"sites\":[{\"isAuthority\":0,\"site\":\"ID\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"MY\"},{\"isAuthority\":0,\"site\":\"BR\"},{\"isAuthority\":0,\"site\":\"TW\"},{\"isAuthority\":0,\"site\":\"TH\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"PH\"},{\"isAuthority\":0,\"site\":\"VN\"}]},{\"plat\":\"Amazon\",\"sites\":[{\"isAuthority\":0,\"site\":\"IT\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"FR\"},{\"isAuthority\":0,\"site\":\"ES\"},{\"isAuthority\":0,\"site\":\"AU\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"UK\"},{\"isAuthority\":0,\"site\":\"PL\"},{\"isAuthority\":0,\"site\":\"US\"},{\"isAuthority\":0,\"site\":\"CA\"},{\"isAuthority\":0,\"site\":\"NL\"},{\"isAuthority\":0,\"site\":\"DE\"},{\"isAuthority\":0,\"site\":\"AE\"},{\"isAuthority\":0,\"site\":\"IN\"},{\"isAuthority\":0,\"site\":\"JP\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"L\\\",\\\"Color\\\":\\\"Beige\\\"}\",\"shopifyAttrOption\":\"Size,Color\",\"singleSource\":3,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-W-L\",\"specialTypeList\":[1007,2002],\"tag\":\"普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0},{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-21-489/8ZZ800155-B-XL.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Normal\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-黑色-XL\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"材质\\\",\\\"enName\\\":\\\"Material\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"尺寸\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"颜色\\\",\\\"enName\\\":\\\"Color\\\",\\\"cnValue\\\":\\\"黑色\\\",\\\"enValue\\\":\\\"Black\\\"},{\\\"cnName\\\":\\\"型号\\\",\\\"enName\\\":\\\"1\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"XL\\\",\\\"enValue\\\":\\\"XL\\\"},{\\\"cnName\\\":\\\"服装尺码\\\",\\\"enName\\\":\\\"Size\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"},{\\\"cnName\\\":\\\"光线颜色\\\",\\\"enName\\\":\\\"Emitting Color\\\",\\\"cnValue\\\":\\\"\\\",\\\"enValue\\\":\\\"\\\"}]\",\"saleForbiddenList\":[\"虾皮\",\"Amazon\"],\"salesProhibition\":\"[{\\\"sites\\\":[{\\\"site\\\":\\\"ID\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MY\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"BR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TW\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"VN\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"虾皮\\\"},{\\\"sites\\\":[{\\\"site\\\":\\\"IT\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"FR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"ES\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AU\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"UK\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"US\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"CA\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"NL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"DE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"IN\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"JP\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"Amazon\\\"}]\",\"salesProhibitionsVos\":[{\"plat\":\"虾皮\",\"sites\":[{\"isAuthority\":0,\"site\":\"ID\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"MY\"},{\"isAuthority\":0,\"site\":\"BR\"},{\"isAuthority\":0,\"site\":\"TW\"},{\"isAuthority\":0,\"site\":\"TH\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"PH\"},{\"isAuthority\":0,\"site\":\"VN\"}]},{\"plat\":\"Amazon\",\"sites\":[{\"isAuthority\":0,\"site\":\"IT\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"FR\"},{\"isAuthority\":0,\"site\":\"ES\"},{\"isAuthority\":0,\"site\":\"AU\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"UK\"},{\"isAuthority\":0,\"site\":\"PL\"},{\"isAuthority\":0,\"site\":\"US\"},{\"isAuthority\":0,\"site\":\"CA\"},{\"isAuthority\":0,\"site\":\"NL\"},{\"isAuthority\":0,\"site\":\"DE\"},{\"isAuthority\":0,\"site\":\"AE\"},{\"isAuthority\":0,\"site\":\"IN\"},{\"isAuthority\":0,\"site\":\"JP\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"\\\",\\\"Color\\\":\\\"Black\\\",\\\"Material\\\":\\\"\\\"}\",\"shopifyAttrOption\":\"Size,Color,Material\",\"singleSource\":3,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-B-XL\",\"specialTypeList\":[1007,2002],\"tag\":\"普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0},{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-21-675/8ZZ800155-B-M.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Normal\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-黑色-M\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"颜色\\\",\\\"cnValue\\\":\\\"黑色\\\",\\\"enName\\\":\\\"Color\\\",\\\"enValue\\\":\\\"Black\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"cnValue\\\":\\\"M\\\",\\\"enName\\\":\\\"Size\\\",\\\"enValue\\\":\\\"M\\\"}]\",\"saleForbiddenList\":[\"虾皮\",\"Amazon\"],\"salesProhibition\":\"[{\\\"sites\\\":[{\\\"site\\\":\\\"ID\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MY\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"BR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TW\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"VN\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"虾皮\\\"},{\\\"sites\\\":[{\\\"site\\\":\\\"IT\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"FR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"ES\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AU\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"UK\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"US\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"CA\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"NL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"DE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"IN\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"JP\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"Amazon\\\"}]\",\"salesProhibitionsVos\":[{\"plat\":\"虾皮\",\"sites\":[{\"isAuthority\":0,\"site\":\"ID\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"MY\"},{\"isAuthority\":0,\"site\":\"BR\"},{\"isAuthority\":0,\"site\":\"TW\"},{\"isAuthority\":0,\"site\":\"TH\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"PH\"},{\"isAuthority\":0,\"site\":\"VN\"}]},{\"plat\":\"Amazon\",\"sites\":[{\"isAuthority\":0,\"site\":\"IT\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"FR\"},{\"isAuthority\":0,\"site\":\"ES\"},{\"isAuthority\":0,\"site\":\"AU\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"UK\"},{\"isAuthority\":0,\"site\":\"PL\"},{\"isAuthority\":0,\"site\":\"US\"},{\"isAuthority\":0,\"site\":\"CA\"},{\"isAuthority\":0,\"site\":\"NL\"},{\"isAuthority\":0,\"site\":\"DE\"},{\"isAuthority\":0,\"site\":\"AE\"},{\"isAuthority\":0,\"site\":\"IN\"},{\"isAuthority\":0,\"site\":\"JP\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"M\\\",\\\"Color\\\":\\\"Black\\\"}\",\"shopifyAttrOption\":\"Size,Color\",\"singleSource\":3,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-B-M\",\"specialTypeList\":[1007,2002],\"tag\":\"普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0},{\"categoryName\":\"连衣裙\",\"categoryPath\":\"6292-5-69\",\"cnCustoms\":\"吊带裙\",\"cost\":50.6,\"desEn\":\"Features:\\n100% brand new and high quality\\nSleeveless, self tie up spaghetti shoulder straps, sexy square neck, lace patchwork at neckline.\\nBlack dress, gothic punk style, red cross patch embroidery, red heart striped at both sides, bodycon fit.\\nSexy split hem, package hip, above knee mini length, empire high waist, sweet bowknot decor.\\nMade of high quality polyester, soft, stretchy, breathable, provides exceptional comfort and durability.\\nPerfect for daily wear, party, cocktail, holiday, travel, outdoor, casual, music festival, and other occasions.\\n1 dress only, other accessories demo in the picture are not included!\\n\\nSpecification:\\nMaterial: Polyester\\nColor: Black+Red\\nSize: S/M/L(Chinese Size,follow the size chart to select please)\\nSize:S\\tBust:72cm-86cm(28.34inch-33.85inch)\\tWaist:58cm-70cm(22.83inch-27.55inch)\\tHip:76cm-90cm(29.92inch-35.43inch)\\tLength:57cm(22.44inch)\\nSize:M\\tBust:76cm-90cm(29.92inch-35.43inch)\\tWaist:62cm-74cm(24.4inch-29.13inch)\\tHip:80cm-94cm(31.49inch-37.01inch)\\tLength:59cm(23.22inch)\\nSize:L\\tBust:80cm-94cm(31.49inch-37.01inch)\\tWaist:66cm-78cm(25.98inch-30.71inch)\\tHip:84cm-98cm(33.07inch-38.58inch)\\tLength:61cm(24.01inch)\\nQuantity: 1 pc \\n\\nWarm prompt:\\n1.Asian people size is 1 or 2 sizes smaller than European and American.\\n2.Please note that low temperature washed,do not bleach and place under the blazing sun for quite a long time.\\n3.As different measuring methods,it will occur 1-3 cm deviation about the items,please ensure that it is okay for you before ordering.\\n4.Because of different monitors and lamplight,the picture may not reflect the actual color of the item,really thank you for your kind understanding!\\n \\nPackage includes:\\u007F\\n1 x Dress(without retail package)\",\"enCustoms\":\"Women Dress\",\"enTag\":\"normal goods\",\"firstImage\":\"http://************:8888/public/2021-08/16-15-24-22-438/8ZZ800155-B-L.jpg\",\"fullpath\":\"女装>连衣裙\",\"fullpathcode\":\"9996_163_183\",\"height\":2.0,\"itemStatus\":\"Normal\",\"length\":2.0,\"mainSku\":\"8ZZ800155\",\"mustKeyword\":\"[\\\"womens sandals size 8\\\",\\\"red cross patch reflective\\\",\\\"sexy pajamas for women shorts set\\\"]\",\"name\":\"女装风格连衣裙-黑色-L\",\"packingPrice\":0.11,\"packingWeight\":3.04,\"productWeight\":600.0,\"saleAtts\":\"[{\\\"cnName\\\":\\\"颜色\\\",\\\"cnValue\\\":\\\"黑色\\\",\\\"enName\\\":\\\"Color\\\",\\\"enValue\\\":\\\"Black\\\"},{\\\"cnName\\\":\\\"尺码\\\",\\\"cnValue\\\":\\\"L\\\",\\\"enName\\\":\\\"Size\\\",\\\"enValue\\\":\\\"L\\\"}]\",\"saleForbiddenList\":[\"虾皮\",\"Amazon\"],\"salesProhibition\":\"[{\\\"sites\\\":[{\\\"site\\\":\\\"ID\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MY\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"BR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TW\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"TH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PH\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"VN\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"虾皮\\\"},{\\\"sites\\\":[{\\\"site\\\":\\\"IT\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"MX\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"FR\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"ES\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AU\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"SG\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"UK\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"PL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"US\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"CA\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"NL\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"DE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"AE\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"IN\\\",\\\"isAuthority\\\":0},{\\\"site\\\":\\\"JP\\\",\\\"isAuthority\\\":0}],\\\"plat\\\":\\\"Amazon\\\"}]\",\"salesProhibitionsVos\":[{\"plat\":\"虾皮\",\"sites\":[{\"isAuthority\":0,\"site\":\"ID\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"MY\"},{\"isAuthority\":0,\"site\":\"BR\"},{\"isAuthority\":0,\"site\":\"TW\"},{\"isAuthority\":0,\"site\":\"TH\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"PH\"},{\"isAuthority\":0,\"site\":\"VN\"}]},{\"plat\":\"Amazon\",\"sites\":[{\"isAuthority\":0,\"site\":\"IT\"},{\"isAuthority\":0,\"site\":\"MX\"},{\"isAuthority\":0,\"site\":\"FR\"},{\"isAuthority\":0,\"site\":\"ES\"},{\"isAuthority\":0,\"site\":\"AU\"},{\"isAuthority\":0,\"site\":\"SG\"},{\"isAuthority\":0,\"site\":\"UK\"},{\"isAuthority\":0,\"site\":\"PL\"},{\"isAuthority\":0,\"site\":\"US\"},{\"isAuthority\":0,\"site\":\"CA\"},{\"isAuthority\":0,\"site\":\"NL\"},{\"isAuthority\":0,\"site\":\"DE\"},{\"isAuthority\":0,\"site\":\"AE\"},{\"isAuthority\":0,\"site\":\"IN\"},{\"isAuthority\":0,\"site\":\"JP\"}]}],\"shopifyAttr\":\"{\\\"Size\\\":\\\"L\\\",\\\"Color\\\":\\\"Black\\\"}\",\"shopifyAttrOption\":\"Size,Color\",\"singleSource\":3,\"sizeRemark\":\"20*20\",\"sonSku\":\"8ZZ800155-B-L\",\"specialTypeList\":[1007,2002],\"tag\":\"普货\",\"titleAtt\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black much MEtoo Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleCn\":\"[\\\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black VERY BIG\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem Party\\\",\\\"Women Goth Punk Tie Up Strap Sexy Bodycon Black VERY BIG Dress Harajuku Red Cross Patch Embroidery Split Hem High Waist Party Clubwear\\\"]\",\"titleEn\":\"Women Goth Punk Tie Up Strap Cross Patch Split Hem Sexy Bodycon Black much MEtoo\",\"type\":1,\"wide\":2.0}]";
//        List<ProductInfo> productInfos = JSON.parseArray(productJson, ProductInfo.class);

        List<ProductInfo> productInfoList = new ArrayList<>();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setMainSku("111");
        productInfo.setSonSku("111-1");
        productInfo.setSaleAtts("[{\"enName\":\"Size\",\"enValue\":\"xxl\"},{\"enName\":\"Color\",\"enValue\":\"red\"}]");

        ProductInfo productInfo1 = new ProductInfo();
        productInfo1.setMainSku("111");
        productInfo1.setSonSku("111-2");
        productInfo1.setSaleAtts("[{\"enName\":\"Size\",\"enValue\":\"M\"}]");

        productInfoList.add(productInfo);
        productInfoList.add(productInfo1);

        SkuTheme skuTheme = parseProductAttrs(productInfoList);
        System.out.println("skuTheme," + JSON.toJSONString(skuTheme));
    }


    @Test
    public void delProductInfo() {
        String param = "{\n" +
                "  \"method\": \"addAmazonTemplate\",\n" +
                "  \"args\": \"{\\\"titleRule\\\":\\\"{\\\\\\\"isFirst\\\\\\\":true,\\\\\\\"key\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"okCount\\\\\\\":0,\\\\\\\"priority\\\\\\\":1,\\\\\\\"totalCount\\\\\\\":3,\\\\\\\"type\\\\\\\":\\\\\\\"LONG_TITLE\\\\\\\"}\\\",\\\"sellerId\\\":\\\"NL-rcbbedxe\\\",\\\"singleSource\\\":3,\\\"country\\\":\\\"NL\\\",\\\"browsePathById\\\":\\\"16241836031,16241837031,16391692031,16391718031,16391822031,16392124031,16392420031\\\",\\\"categoryId\\\":\\\"16392420031\\\",\\\"productType\\\":\\\"Home.Home\\\",\\\"saleVariant\\\":true,\\\"parentSku\\\":\\\"3TT204401\\\",\\\"title\\\":\\\"Shapewear Tops for Women Tummy Control Tank Shaping Camisole Seamless Body Shaper Slimming Cami Waist Trainer Vest shapewear tops for women tummy control tummy control shapewear shapewear tank tops\\\",\\\"standardProdcutIdType\\\":\\\"EAN\\\",\\\"standardProdcutIdValue\\\":\\\"\\\",\\\"brand\\\":\\\"Selma.\\\",\\\"manufacturer\\\":\\\"Selma.\\\",\\\"extraData\\\":\\\"{\\\\\\\"descriptionData\\\\\\\":[{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--ShippingWeight\\\\\\\",\\\\\\\"values\\\\\\\":[107.5],\\\\\\\"attrs\\\\\\\":{\\\\\\\"unitOfMeasure\\\\\\\":[\\\\\\\"GR\\\\\\\"]}},{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--UsedFor\\\\\\\",\\\\\\\"values\\\\\\\":[\\\\\\\"women\\\\\\\",null,null,null,null],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--TargetAudience\\\\\\\",\\\\\\\"values\\\\\\\":[\\\\\\\"women\\\\\\\",null,null,null],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--IsGiftWrapAvailable\\\\\\\",\\\\\\\"values\\\\\\\":[true],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--IsExpirationDatedProduct\\\\\\\",\\\\\\\"values\\\\\\\":[\\\\\\\"false\\\\\\\"],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"DescriptionData--RecommendedBrowseNode\\\\\\\",\\\\\\\"values\\\\\\\":[\\\\\\\"16392420031\\\\\\\",\\\\\\\"\\\\\\\"],\\\\\\\"attrs\\\\\\\":null}],\\\\\\\"productData\\\\\\\":[{\\\\\\\"route\\\\\\\":\\\\\\\"Home\\\\\\\",\\\\\\\"values\\\\\\\":[null],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"Home--ProductType\\\\\\\",\\\\\\\"values\\\\\\\":[null],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"Home--ProductType--Home\\\\\\\",\\\\\\\"values\\\\\\\":[null],\\\\\\\"attrs\\\\\\\":null},{\\\\\\\"route\\\\\\\":\\\\\\\"Home--CountryOfOrigin\\\\\\\",\\\\\\\"values\\\\\\\":[\\\\\\\"CN\\\\\\\"],\\\\\\\"attrs\\\\\\\":null}]}\\\",\\\"searchData\\\":\\\"\\\",\\\"mfrPartNumber\\\":\\\"Selma.\\\",\\\"condition\\\":\\\"New\\\",\\\"mainImage\\\":null,\\\"currency\\\":\\\"EUR\\\",\\\"variationThemes\\\":\\\"Color-Size\\\",\\\"isLock\\\":false,\\\"sampleImage\\\":\\\"\\\",\\\"extraImages\\\":null,\\\"description\\\":\\\"Shapewear Tops for Women Tummy Control Tank Shaping Camisole Seamless Body Shaper Slimming Cami Waist Trainer Vest shapewear tops for women tummy control tummy control shapewear shapewear tank tops for women body shaper\\\\nFeatures: This waist training camisoles is made of top grade Nylon material, very smooth & soft, elastic and stretchable, light-weight and skin-friendly, breathable and flexible.\\\\n This basic tank top for women with scoop neck typical style design,the shape of neck, breasts and face can be well groomed.No chest pad so that you can wear your favorite bra under this top.\\\\n This slimming vest with special high midsection compression to control tummy and reduce waistline firmly, giving you the smoother and slimmer natual curves.\\\\n Abdominal trainer vest also can be used as a liposuction compression garment in the recovery process. It offers all the support and compression to your abdomen which helps your maternity body to get back to its original shape.\\\\n This underwear tank top can flatten abdomen,reduce waistline,get rid of back flabby fat,postnatal maternity recovery,combined with a healthy lean diet and drink plenty of water to loss weight,which makes you look slim and gorgeous.\\\\n\\\\nSpecifications: Material:Nylon\\\\nSize:L/XL(Chinese size ,follow the size chart to select please)\\\\nSize:L\\\\tWaist:59-80cm/23.23-31.50inch\\\\tBody Weight:45-60Kg\\\\nSize:XL\\\\tWaist:80-100cm/31.50-39.37inch\\\\tBody Weight:60-80Kg\\\\n\\\\nPackage Includes: 1x Shapewear Tops\\\\n\\\\nnote: Please allow 1mm errors due to manual measurement. \\\\nDue to the difference between different monitors, the picture may not reflect the actual color of the item.\\\",\\\"variations\\\":\\\"[{\\\\\\\"sku\\\\\\\":\\\\\\\"3TT204401-FC-XL\\\\\\\",\\\\\\\"sellerSKU\\\\\\\":null,\\\\\\\"standardProdcutIdType\\\\\\\":\\\\\\\"EAN\\\\\\\",\\\\\\\"standardProdcutIdValue\\\\\\\":\\\\\\\"7543510230008\\\\\\\",\\\\\\\"mainImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-359/effect-main-image36969d27-c489-4722-a36f-d0769cc117408429845858100568186.jpg\\\\\\\",\\\\\\\"sampleImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-359/effect-main-image36969d27-c489-4722-a36f-d0769cc117408429845858100568186.jpg\\\\\\\",\\\\\\\"extraImages\\\\\\\":\\\\\\\"[\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\\\\\\\\\"]\\\\\\\",\\\\\\\"extraImagesList\\\\\\\":[\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\"],\\\\\\\"condition\\\\\\\":\\\\\\\"New\\\\\\\",\\\\\\\"conditionNote\\\\\\\":null,\\\\\\\"standardPrice\\\\\\\":16.35,\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"saleStartDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"saleEndDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"totalPrice\\\\\\\":16.35,\\\\\\\"totalSalePrice\\\\\\\":null,\\\\\\\"shippingCost\\\\\\\":0,\\\\\\\"shippingGroup\\\\\\\":null,\\\\\\\"quantity\\\\\\\":1500,\\\\\\\"extraData\\\\\\\":null,\\\\\\\"nameValues\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"Color\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"color\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"color\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null},{\\\\\\\"name\\\\\\\":\\\\\\\"Size\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"XL\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"XL\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null}],\\\\\\\"name\\\\\\\":\\\\\\\"3TT204401-FC-XL\\\\\\\",\\\\\\\"stepTemplateStatus\\\\\\\":null,\\\\\\\"skulifecyclephase\\\\\\\":\\\\\\\"Normal\\\\\\\",\\\\\\\"repeatFlag\\\\\\\":null,\\\\\\\"nameValuesJson\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"color\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"XL\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Size\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"XL\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"extInfo\\\\\\\":[\\\\\\\"正常\\\\\\\",\\\\\\\"\\\\\\\",\\\\\\\"false\\\\\\\",\\\\\\\"见尺寸图\\\\\\\",\\\\\\\"(1件)无缝托胸束腹吊带-肤色-XL\\\\\\\",\\\\\\\"男女内衣及家居服>女士贴身衣物>塑身美体内衣\\\\\\\"]},{\\\\\\\"sku\\\\\\\":\\\\\\\"3TT204401-FC-L\\\\\\\",\\\\\\\"sellerSKU\\\\\\\":null,\\\\\\\"standardProdcutIdType\\\\\\\":\\\\\\\"EAN\\\\\\\",\\\\\\\"standardProdcutIdValue\\\\\\\":\\\\\\\"7543510230015\\\\\\\",\\\\\\\"mainImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-605/effect-main-image3ba295c6-2afe-4729-8759-aaf62a0f8d4c2967447945085689373.jpg\\\\\\\",\\\\\\\"sampleImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-605/effect-main-image3ba295c6-2afe-4729-8759-aaf62a0f8d4c2967447945085689373.jpg\\\\\\\",\\\\\\\"extraImages\\\\\\\":\\\\\\\"[\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\\\\\\\\\"]\\\\\\\",\\\\\\\"extraImagesList\\\\\\\":[\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\"],\\\\\\\"condition\\\\\\\":\\\\\\\"New\\\\\\\",\\\\\\\"conditionNote\\\\\\\":null,\\\\\\\"standardPrice\\\\\\\":16.35,\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"saleStartDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"saleEndDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"totalPrice\\\\\\\":16.35,\\\\\\\"totalSalePrice\\\\\\\":null,\\\\\\\"shippingCost\\\\\\\":0,\\\\\\\"shippingGroup\\\\\\\":null,\\\\\\\"quantity\\\\\\\":1500,\\\\\\\"extraData\\\\\\\":null,\\\\\\\"nameValues\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"Color\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"color\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"color\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null},{\\\\\\\"name\\\\\\\":\\\\\\\"Size\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"L\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"L\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null}],\\\\\\\"name\\\\\\\":\\\\\\\"3TT204401-FC-L\\\\\\\",\\\\\\\"stepTemplateStatus\\\\\\\":null,\\\\\\\"skulifecyclephase\\\\\\\":\\\\\\\"Normal\\\\\\\",\\\\\\\"repeatFlag\\\\\\\":null,\\\\\\\"nameValuesJson\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"color\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"L\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Size\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"L\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"extInfo\\\\\\\":[\\\\\\\"正常\\\\\\\",\\\\\\\"\\\\\\\",\\\\\\\"false\\\\\\\",\\\\\\\"见尺寸图\\\\\\\",\\\\\\\"(1件)无缝托胸束腹吊带-肤色-XL\\\\\\\",\\\\\\\"男女内衣及家居服>女士贴身衣物>塑身美体内衣\\\\\\\"]},{\\\\\\\"sku\\\\\\\":\\\\\\\"3TT204401-BK-XL\\\\\\\",\\\\\\\"sellerSKU\\\\\\\":null,\\\\\\\"standardProdcutIdType\\\\\\\":\\\\\\\"EAN\\\\\\\",\\\\\\\"standardProdcutIdValue\\\\\\\":\\\\\\\"7543510230022\\\\\\\",\\\\\\\"mainImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-715/effect-main-imagec97cf8f6-eb33-4d38-8d4c-7cade24d76da6809734561043097262.jpg\\\\\\\",\\\\\\\"sampleImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-715/effect-main-imagec97cf8f6-eb33-4d38-8d4c-7cade24d76da6809734561043097262.jpg\\\\\\\",\\\\\\\"extraImages\\\\\\\":\\\\\\\"[\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\\\\\\\\\"]\\\\\\\",\\\\\\\"extraImagesList\\\\\\\":[\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\"],\\\\\\\"condition\\\\\\\":\\\\\\\"New\\\\\\\",\\\\\\\"conditionNote\\\\\\\":null,\\\\\\\"standardPrice\\\\\\\":16.31,\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"saleStartDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"saleEndDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"totalPrice\\\\\\\":16.31,\\\\\\\"totalSalePrice\\\\\\\":null,\\\\\\\"shippingCost\\\\\\\":0,\\\\\\\"shippingGroup\\\\\\\":null,\\\\\\\"quantity\\\\\\\":1500,\\\\\\\"extraData\\\\\\\":null,\\\\\\\"nameValues\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"Color\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"Black\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Black\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null},{\\\\\\\"name\\\\\\\":\\\\\\\"Size\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"XL\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"XL\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null}],\\\\\\\"name\\\\\\\":\\\\\\\"3TT204401-BK-XL\\\\\\\",\\\\\\\"stepTemplateStatus\\\\\\\":null,\\\\\\\"skulifecyclephase\\\\\\\":\\\\\\\"Normal\\\\\\\",\\\\\\\"repeatFlag\\\\\\\":null,\\\\\\\"nameValuesJson\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Black\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Black\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"XL\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Size\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"XL\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"extInfo\\\\\\\":[\\\\\\\"正常\\\\\\\",\\\\\\\"\\\\\\\",\\\\\\\"false\\\\\\\",\\\\\\\"见尺寸图\\\\\\\",\\\\\\\"(1件)无缝托胸束腹吊带-肤色-XL\\\\\\\",\\\\\\\"男女内衣及家居服>女士贴身衣物>塑身美体内衣\\\\\\\"]},{\\\\\\\"sku\\\\\\\":\\\\\\\"3TT204401-BK-L\\\\\\\",\\\\\\\"sellerSKU\\\\\\\":null,\\\\\\\"standardProdcutIdType\\\\\\\":\\\\\\\"EAN\\\\\\\",\\\\\\\"standardProdcutIdValue\\\\\\\":\\\\\\\"7543510230039\\\\\\\",\\\\\\\"mainImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-985/effect-main-imagee1c8b1d4-d82f-4b07-a793-d8e719cdef1c5369076077218695610.jpg\\\\\\\",\\\\\\\"sampleImage\\\\\\\":\\\\\\\"http://************:8888/templateMainImage/2022-08/26-12-30-46-985/effect-main-imagee1c8b1d4-d82f-4b07-a793-d8e719cdef1c5369076077218695610.jpg\\\\\\\",\\\\\\\"extraImages\\\\\\\":\\\\\\\"[\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\\\\\\\\\"]\\\\\\\",\\\\\\\"extraImagesList\\\\\\\":[\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-54-318/3TT204401.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-984/3TT204401-effect.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-795/3TT204401-cmb.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-612/3TT204401-FC-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-404/3TT204401-FC-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-53-180/3TT204401-BK-XL.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-976/3TT204401-BK-L.jpg\\\\\\\",\\\\\\\"http://************:8888/amazon/2022-08/24-15-04-52-778/3TT204401-9.jpg\\\\\\\"],\\\\\\\"condition\\\\\\\":\\\\\\\"New\\\\\\\",\\\\\\\"conditionNote\\\\\\\":null,\\\\\\\"standardPrice\\\\\\\":16.31,\\\\\\\"salePrice\\\\\\\":null,\\\\\\\"saleStartDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"saleEndDate\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"totalPrice\\\\\\\":16.31,\\\\\\\"totalSalePrice\\\\\\\":null,\\\\\\\"shippingCost\\\\\\\":0,\\\\\\\"shippingGroup\\\\\\\":null,\\\\\\\"quantity\\\\\\\":1500,\\\\\\\"extraData\\\\\\\":null,\\\\\\\"nameValues\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"Color\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"Black\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Black\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null},{\\\\\\\"name\\\\\\\":\\\\\\\"Size\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"L\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"L\\\\\\\",\\\\\\\"attrs\\\\\\\":null,\\\\\\\"extralNameValues\\\\\\\":null,\\\\\\\"extralDesc\\\\\\\":null}],\\\\\\\"name\\\\\\\":\\\\\\\"3TT204401-BK-L\\\\\\\",\\\\\\\"stepTemplateStatus\\\\\\\":null,\\\\\\\"skulifecyclephase\\\\\\\":\\\\\\\"Normal\\\\\\\",\\\\\\\"repeatFlag\\\\\\\":null,\\\\\\\"nameValuesJson\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Black\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Color\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Black\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"desc\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"L\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"name\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"Size\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"value\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"L\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"extInfo\\\\\\\":[\\\\\\\"正常\\\\\\\",\\\\\\\"\\\\\\\",\\\\\\\"false\\\\\\\",\\\\\\\"见尺寸图\\\\\\\",\\\\\\\"(1件)无缝托胸束腹吊带-肤色-XL\\\\\\\",\\\\\\\"男女内衣及家居服>女士贴身衣物>塑身美体内衣\\\\\\\"]}]\\\",\\\"bulletPoint\\\":[\\\" This basic tank top for women with scoop neck typical style design,the shape of neck, breasts and face can be well groomed.No chest pad so that you can wear your favorite bra under this top.\\\",\\\" This underwear tank top can flatten abdomen,reduce waistline,get rid of back flabby fat,postnatal maternity recovery,combined with a healthy lean diet and drink plenty of water to loss weight,which makes you look slim and gorgeous.\\\",\\\"This waist training camisoles is made of top grade Nylon material, very smooth & soft, elastic and stretchable, light-weight and skin-friendly, breathable and flexible.\\\",\\\" This slimming vest with special high midsection compression to control tummy and reduce waistline firmly, giving you the smoother and slimmer natual curves.\\\",\\\" Abdominal trainer vest also can be used as a liposuction compression garment in the recovery process. It offers all the support and compression to your abdomen which helps your maternity body to get back to its original shape.\\\"],\\\"searchTerms\\\":\\\"{\\\\\\\"3TT204401-FC-XL\\\\\\\":[\\\\\\\"shapewear tops for women tummy control tummy control shapewear shapewear tank tops for women body shaper\\\\\\\"],\\\\\\\"3TT204401-BK-XL\\\\\\\":[\\\\\\\"shapewear tops for women tummy control tummy control shapewear shapewear tank tops for women body shaper\\\\\\\"],\\\\\\\"3TT204401-FC-L\\\\\\\":[\\\\\\\"shapewear tops for women tummy control tummy control shapewear shapewear tank tops for women body shaper\\\\\\\"],\\\\\\\"3TT204401-BK-L\\\\\\\":[\\\\\\\"shapewear tops for women tummy control tummy control shapewear shapewear tank tops for women body shaper\\\\\\\"]}\\\",\\\"whetherInfringingWordsDel\\\":true,\\\"publishType\\\":2,\\\"skuDataSource\\\":1,\\\"shippingCost\\\":0,\\\"shippingGroup\\\":null,\\\"totalPrice\\\":null,\\\"totalSalePrice\\\":null}\"\n" +
                "}";
        ApiRequestParam<String> requestParam = JSON.parseObject(param, new TypeReference<ApiRequestParam<String>>() {
        });
        AmazonTemplateWithBLOBs amazonTemplate = requestParam
                .getArgsValue(new TypeReference<AmazonTemplateWithBLOBs>() {
                });
        ApiResult apiResult;
        //是否删除侵权词保存
//        boolean infringingWordsDel = amazonTemplate.isWhetherInfringingWordsDel();
//        if(infringingWordsDel){
//            infringementWordHelper.delInfringementWordsWithProduct(amazonTemplate);
//        }else{
//            apiResult = infringementWordHelper.checkTemplateInfringementWordsWhitProduct(amazonTemplate);
//            if(!apiResult.isSuccess()){
//                System.out.println(JSON.toJSONString(apiResult));
//            }
//        }
        //验证参数json对象
        apiResult = checkParamJson(amazonTemplate);
        if(!apiResult.isSuccess()){
            System.out.println(JSON.toJSONString(apiResult));
        }
    }

    //验证参数json对象
    private ApiResult checkParamJson(AmazonTemplateWithBLOBs amazonTemplate) {
        //1. 验证分类属性格式
        String extraData = amazonTemplate.getExtraData();
        if(StringUtils.isNotBlank(extraData)){
            try {
                JSON.parseObject(extraData, AmazonExtralData.class);
            }catch (Exception e){
                log.error("分类属性保存格式错误", e);
                return ApiResult.newError("分类属性保存格式错误，不是对象类型！");
            }
        }

        //2. 验证属性信息
        String variations = amazonTemplate.getVariations();
        if(StringUtils.isNotBlank(variations)){
            try {
                List<AmazonSku> amazonSkuList = JSON.parseArray(variations, AmazonSku.class);
                for (AmazonSku amazonSku : amazonSkuList){
                    amazonSku.setRepeatFlag(null);
                }
                amazonTemplate.setVariations(JSON.toJSONString(amazonSkuList));
            }catch (Exception e){
                log.error("属性信息保存格式错误", e);
                return ApiResult.newError("属性信息保存格式错误，不是数组类型！");
            }
        }

        //3. 验证图片保存格式
        String extraImages = amazonTemplate.getExtraImages();
        if(StringUtils.isNotBlank(extraImages)){
            try {
                JSON.parseArray(extraImages, String.class);
            }catch (Exception e){
                log.error("图片信息保存格式错误", e);
                return ApiResult.newError("图片信息保存格式错误，不是数组类型！");
            }
        }

        //4. 验证 Bullet Point
        String bulletPoint = amazonTemplate.getBulletPoint();
        if(StringUtils.isNotBlank(bulletPoint)){
            try {
                JSON.parseArray(bulletPoint, String.class);
            }catch (Exception e){
                log.error("Bullet Point信息保存格式错误", e);
                return ApiResult.newError("Bullet Point信息保存格式错误，不是数组类型！");
            }
        }

        //5. true:范本 false:模板
        Boolean isLock = amazonTemplate.getIsLock();
        if(BooleanUtils.isTrue(isLock)){
            //验证范本

        }else{
            //验证模板
            String searchTerms = amazonTemplate.getSearchTerms();
            try {
                if(BooleanUtils.isTrue(amazonTemplate.getSaleVariant())){
                    //变体
                    JSON.parseObject(searchTerms, String.class);
                }else{
                    //单体
                    JSON.parseArray(searchTerms, String.class);
                }
            }catch (Exception e){
                log.error("关键词信息保存格式错误", e);
                String type = BooleanUtils.isTrue(amazonTemplate.getSaleVariant()) ? "对象" : "数组";
                return ApiResult.newError(String.format("关键词信息保存格式错误，不是%s类型！", type));
            }
        }
        return ApiResult.newSuccess();
    }


    @Test
    public void testAmazonPublishPrInvPicPcJobHandler() {
//        amazonTemplateService.handlePublishPrInvPicPcData();
    }

    @Test
    public void testOrderAsin(){
        List<EsAmazonProductListing> listings = new ArrayList<>();
        EsAmazonProductListing esAmazonProductListing = new EsAmazonProductListing();
        esAmazonProductListing.setAccountNumber("DE-keyisizhai97");
        esAmazonProductListing.setSonAsin("B07W8XS3WV");
        listings.add(esAmazonProductListing);
        // 排除订单FBA库存管理存在的asin
        listings = AmazonListingUtils.filterFBAExistAsin(listings);
        System.out.println(listings.size());

       /* List<EsAmazonProductListing> listings1 = new ArrayList<>();
        EsAmazonProductListing esAmazonProductListing1 = new EsAmazonProductListing();
        esAmazonProductListing1.setAccountNumber("DE-keyisizhai97");
        esAmazonProductListing1.setSonAsin("");
        listings1.add(esAmazonProductListing1);
        // 排除订单FBA库存管理存在的asin
        listings1 = AmazonListingUtils.filterFBAExistAsin(listings1);
        System.out.println(listings1.size());


        List<EsAmazonProductListing> listings2 = new ArrayList<>();
        EsAmazonProductListing esAmazonProductListing2 = new EsAmazonProductListing();
        esAmazonProductListing2.setAccountNumber("DE-keyisizhai97");
        esAmazonProductListing2.setSonAsin("");
        listings2.add(esAmazonProductListing2);
        // 排除订单FBA库存管理存在的asin
        listings2 = AmazonListingUtils.filterFBAExistAsin(listings2);
        System.out.println(listings2.size());*/

        OrderClient orderClient = SpringUtils.getBean(OrderClient.class);
        List<String> asinList = new ArrayList<>(Arrays.asList("B07W8XS3WV"));
        Map<String, String> body = new HashMap<>(1);
        body.put("asins", StringUtils.join(asinList, ","));
        ApiResult<String> asinResult = orderClient.getFbaInventoryAsin(body);
        if (!asinResult.isSuccess()) {
            System.out.println("调用订单接口报错：" + asinResult.getErrorMsg());
        }
        if (StringUtils.isBlank(asinResult.getResult())) {
            System.out.println(asinResult.getResult());
        }
    }

    @Test
    public void testSyncListingMsg(){
        String accountNumber = "UK-ploeere35";
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        List<String> asinList = new ArrayList<>(Arrays.asList("B083LWKZXM","B08GK6RP8X","B08GKK6D69","B083LX5XK2","B0B41LRT2J",
                "B0B4127SM5","B0B417MJWG","B0B41BFK61","B0B3M8HK5C","B0B3LV9NTH","B0B41P186J","B0B41C4JXX","B0B41BGZGP","B0B415RVCM","B0B41PBMQN","B0B41CD4FW"));
        for (String asin : asinList) {
            ApiResult<Item> getCatalogItemAPiResult = AmazonSpLocalServiceUtils.getCatalogItem(asin, amazonSpAccount);

            if (null == getCatalogItemAPiResult || !getCatalogItemAPiResult.isSuccess()) {
                log.error("getCatalogItem, sonAsin {}, accountNumber {} ApiResult {}", asin, accountNumber, JSON.toJSONString(getCatalogItemAPiResult));
                continue;
            }

            // 返回数据转为listing对象
            /*AmazonProductListing updateAmazonProductListing = AmazonSpLocalUtils.toAmazonProductListingDetail(getCatalogItemAPiResult.getResult());
            if (null == updateAmazonProductListing) {
                log.error("toAmazonProductListingDetail, sonAsin {}, accountNumber {} ApiResult {}", asin, accountNumber, JSON.toJSONString(getCatalogItemAPiResult));
                continue;
            }*/
        }
    }

    @Test
    public void testEsListingAgg() {
        long rangeTimeAddListingTotal = esAmazonProductListingService.getRangeTimeAddListingTotal("UK-ploeere35",
                "2022-12-30 00:00:00", "2023-01-02 23:59:59");
        System.out.println(rangeTimeAddListingTotal);

        long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(Arrays.asList("UK-ploeere35"));
        System.out.println(onlineListingNum);
    }

    @Test
    @SneakyThrows
    public void testStatistics() {
//        List<NewUser> userList = NewUsermgtUtils.getEmployeeByServicePlatform(SaleChannelEnum.AMAZON.getChannelName());
//        System.out.println(JSON.toJSON(userList));
//        List<String> accounts = Arrays.asList("CA-amzbrand", "UK-nahaob5");
//        ApiResult<List<BIShopStatisticsDataBO>> apiResult = BIServiceUtil.listBIShopStatisticsData(accounts, SaleChannel.CHANNEL_AMAZON, "2023-01-05");
//        System.out.println(JSON.toJSON(apiResult));
        String param = "{\"accountNumbers\":[],\"statistDate\":\"2023-09-12\"}";
        ReturnT<String> run = amazonSalesDataStatisticsJobHandler.run(param);
        log.info("end:{}",JSON.toJSON(run));
    }

    @Test
    public void mockShopStatisticsData() {
        AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
        amazonAccountRelationExample.createCriteria().andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());
        String oneColumns = "account_number";
        amazonAccountRelationExample.setFiledColumns(oneColumns);
        List<AmazonAccountRelation> accountRelations = amazonAccountRelationService.selectFiledColumnsByExample(amazonAccountRelationExample);

        List<Integer> orderList = Arrays.asList(10, 20, 30, 20, 30, 5, 8);
        List<Double> sale = Arrays.asList(59.99, 80.0, 100.29, 300.90, 70.89, 120.34, 215.89);
        List<Integer> listing = Arrays.asList(10,20,40,50,20,20,10);
        Date date = new Date();
        for (int i = 1; i <= 7; i++) {
            LocalDate yesterday = LocalDate.now().minusDays(i);
            String yearOfMonth = yesterday.getYear() + "" + yesterday.getMonthValue();
            for (AmazonAccountRelation accountRelation : accountRelations) {
                EsShopStatisticsData esShopStatisticsData = new EsShopStatisticsData();
                String shopStatisticsId = org.apache.commons.lang3.StringUtils.joinWith("_",
                        SaleChannelEnum.AMAZON.getChannelName(), yesterday, accountRelation.getAccountNumber());
                esShopStatisticsData.setId(shopStatisticsId);
                esShopStatisticsData.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
                esShopStatisticsData.setAccountNumber(accountRelation.getAccountNumber());
                esShopStatisticsData.setMonth(yearOfMonth);
                int index = RandomUtils.nextInt(0, 6);
                esShopStatisticsData.setOrderNum(orderList.get(index));
                esShopStatisticsData.setSaleNum(sale.get(index));
                esShopStatisticsData.setAddListingNum(listing.get(index));
                esShopStatisticsData.setStatisticalDate(yesterday.toString());
                esShopStatisticsData.setCreateTime(date);
                esShopStatisticsDataService.save(esShopStatisticsData);
            }
        }
    }

    @Test
    public void testAggMonthData() {
        List<String> accounts = Arrays.asList("CA-amzbrand", "UK-nahaob5");
        ShopMonthTotalDataDO shopMonthTotalDataDO = esShopStatisticsDataService.aggShopMonthTotalData(accounts, SalesStatisticsRoleTypeEnum.SALE,
                SaleChannelEnum.AMAZON.getChannelName(),
                "20231");
        log.info(JSON.toJSONString(shopMonthTotalDataDO));
    }


    @Test
    public void testAggDailyData() {
        List<String> accounts = Arrays.asList("CA-amzbrand", "UK-nahaob5");
        LocalDate endDate = LocalDate.parse("2023-01-30");
        LocalDate starDate = endDate.minusDays(6);
        List<SalesDailyDataDO> dailyDataDOS = esShopStatisticsDataService.salesDailyDataStatistics(accounts, SalesStatisticsRoleTypeEnum.SALE, starDate.toString(), SaleChannel.CHANNEL_AMAZON, endDate.toString());
        Map<String, SalesDailyDataDO> dailyDataDOMap = dailyDataDOS.stream().collect(Collectors.toMap(SalesDailyDataDO::getStatistDate, Function.identity(), (o1, o2) -> o2));
        dailyDataDOS.clear();
        for (int i = 0; i < 7; i++) {
            LocalDate currentDate;
            if (i > 0) {
                currentDate = starDate.plusDays(i);
            }else {
                currentDate = starDate;
            }
            SalesDailyDataDO salesDailyDataDO = dailyDataDOMap.get(currentDate.toString());
            if (salesDailyDataDO != null) {
                dailyDataDOS.add(salesDailyDataDO);
            }else {
                SalesDailyDataDO dailyData = new SalesDailyDataDO();
                dailyData.setStatistDate(currentDate.toString());
                dailyData.setSalesNum(0D);
                dailyData.setOrderNum(0);
                dailyData.setAddListingNum(0);
                dailyDataDOS.add(dailyData);
            }
        }
        log.info(JSON.toJSONString(dailyDataDOS));
    }

    @Test
    public void testUserPlatform() {
        List<String> strings = Arrays.asList("4727","6019","4689","031","262","642");
        Map<String, List<String>> servicePlatformByEmployeeNos = NewUsermgtUtils.getServicePlatformByEmployeeNos(strings);
        System.out.println(JSON.toJSON(servicePlatformByEmployeeNos));
    }


    @Test
    public void testLog() {
        AmazonPublishOperationLog operationLog = amazonPublishOperationLogService.selectByPrimaryKey(576460752303528034L);
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(10000));
        AmazonUpdatePriceDataMessage dataMessage = new AmazonUpdatePriceDataMessage();
        dataMessage.setTaskLogId(operationLog.getId());
        dataMessage.setRetryFailData(Boolean.FALSE);
        int updateSize = 10000;
        for (int i = 0; i < updateSize; i++) {
            threadPoolExecutor.execute(() -> {
                int size = RandomUtils.nextInt(0, 100);
                updateOperationLog(dataMessage, size);
            });
        }
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("end");


    }

    private void updateOperationLog(AmazonUpdatePriceDataMessage dataMessage, int updateSize) {
        Long taskLogId = dataMessage.getTaskLogId();
        try {
            AmazonPublishOperationLog operationLog = amazonPublishOperationLogService.selectByPrimaryKey(taskLogId);
            if (operationLog == null) {
                return;
            }
            String key = RedisConstant.AMAZON_TASK_TOTAL + taskLogId;
            long currentTotal = PublishRedisClusterUtils.addAndGet(key, updateSize);

            int lastVersion = Integer.parseInt(operationLog.getModId());
            int taskTotal = Integer.parseInt(operationLog.getObject());

            // 每10w条输出一次日志
            int logPart = 10000;
            long currentLogPart = currentTotal / logPart;
            if ((currentLogPart == lastVersion + 1) || currentTotal == taskTotal) {
                operationLog.setModId(String.valueOf(lastVersion + 1));
                operationLog.setObject1(String.valueOf(currentTotal));
                int updated = amazonPublishOperationLogService.updateSelectiveIdVersion(operationLog, lastVersion);
                if (updated > 0 || currentTotal == taskTotal) {
                    log.info("执行批量{}价格任务，批次:{}，已完成{}/{}条数据，当前进度：{}%",
                            Boolean.TRUE.equals(dataMessage.getRetryFailData()) ? "重传" : "更新",
                            lastVersion,
                            currentTotal,
                            taskTotal,
                            (int) (currentTotal * 100.0 / taskTotal));
                }
            }
        } catch (Exception ignore) {
        }
    }

    @Test
    public void asinPublishTest() {
        int templateId = *********;
        AmazonTemplateBO template = amazonTemplateService.selectBoById(templateId, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());

        List<String> allSellerSkuSku = AmazonTemplateUtils.getAllSellerSkuSku(template);
        List<AmazonListingInfo> listingInfoList = amazonListingInfoService.query()
                .eq("account_number", template.getSellerId())
                .in("seller_sku", allSellerSkuSku)
                .list();

        if (CollectionUtils.isEmpty(listingInfoList)) {
            return;
        }

        Map<String, String> sellerSkuAsinMap = listingInfoList.stream().collect(Collectors.toMap(AmazonListingInfo::getSellerSku, AmazonListingInfo::getAsin, (k1, k2) -> k1));
        if (MapUtils.isEmpty(sellerSkuAsinMap)) {
            return;
        }

        String sellerId = template.getSellerId();
        AmazonAccountRelation accountManufacturerInfo = amazonAccountRelationService.getAccountManufacturerInfo(sellerId);
        if (accountManufacturerInfo == null) {
            return;
        }
        String merchantId = accountManufacturerInfo.getMerchantId();
        if (org.apache.commons.lang3.StringUtils.isBlank(merchantId)) {
            return;
        }
        // 查询同套账 asin 刊登
        List<String> sameMerchantAccountNumber = saleAccountService.getSameMerchantAccountNumber(merchantId, SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isEmpty(sameMerchantAccountNumber)) {
            return;
        }
        sameMerchantAccountNumber.forEach(accountNumber -> {
            if (accountNumber.equals(sellerId) || accountNumber.startsWith("CA")) {
                return;
            }
            amazonPublishHandler.sameAsinTemplatePublish(template, accountNumber, sellerSkuAsinMap);
        });


    }
}
