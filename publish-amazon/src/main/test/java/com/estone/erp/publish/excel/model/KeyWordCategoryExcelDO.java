package com.estone.erp.publish.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-16 10:54
 */
@Data
public class KeyWordCategoryExcelDO {

    @ExcelProperty("SKU")
    private String sku;

    @ExcelProperty("系统分类")
    private String systemCategory;


    @ExcelProperty("系统分类Id")
    private Integer systemCategoryId;

    @ExcelProperty("提交的关键词")
    private String submitKeyword;

    @ExcelProperty(value = {"AE", "返回的类目ID"})
    private String categoryId;

    @ExcelProperty(value = {"AE", "平台类目名称"})
    private String platformCategoryName;

    @ExcelProperty(value = {"AE", "平台类目名称（中文）"})
    private String platformCategoryNameChinese;


    @ExcelProperty(value = {"AU", "返回的类目ID"})
    private String au_categoryId;

    @ExcelProperty(value = {"AU", "平台类目名称"})
    private String au_platformCategoryName;

    @ExcelProperty(value = {"AU", "平台类目名称（中文）"})
    private String au_platformCategoryNameChinese;


    @ExcelProperty(value = {"MX", "返回的类目ID"})
    private String mx_categoryId;

    @ExcelProperty(value = {"MX", "平台类目名称"})
    private String mx_platformCategoryName;

    @ExcelProperty(value = {"MX", "平台类目名称（中文）"})
    private String mx_platformCategoryNameChinese;
}
