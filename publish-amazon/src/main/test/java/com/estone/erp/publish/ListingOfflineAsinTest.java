package com.estone.erp.publish;

import com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkLog;
import com.estone.erp.publish.amazon.service.OverLimitAccountOffLinkLogService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.order.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024-12-26 11:40
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class ListingOfflineAsinTest {

    @Resource
    private OverLimitAccountOffLinkLogService offLinkLogService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    @Test
    public void testOfflineAsinSaleCount() {
        OverLimitAccountOffLinkLog offLinkLog = offLinkLogService.selectByPrimaryKey(1, "_usjpae");
        log.info("offLinkLog->{}", offLinkLog);
        AmazonProductListing amazonProductListing = new AmazonProductListing();
        amazonProductListing.setAccountNumber("US-t76tanqia3u");
        amazonProductListing.setSonAsin("B0D1MRXLHL");
        amazonProductListing.setSite("US");
        String extraData = offLinkLog.getExtraData();
        Boolean rematchAsinSaleCountRule = offLinkLogService.rematchAsinSaleCountRule(extraData, amazonProductListing, offLinkLog);
        log.info("rematchAsinSaleCountRule:{}", rematchAsinSaleCountRule);
    }

    @Test
    public void testFabAsin() {
        String asin = "B0CMZG1B6T";
        Set<String> fbaAsinSet = OrderUtils.getAllAsinCodeCache();
        if (fbaAsinSet.contains(asin)) {
            log.info("fbaAsin->{}", asin);
        } else {
            log.info("非 fbaAsin");
        }
    }

    @Test
    public void testOfflinePublishRule() {
        String id = StringUtils.join(List.of(SaleChannel.CHANNEL_AMAZON, "US-sprINgamz", "2SS317942_kbKangniceUS"), "_");
        CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(id);
    }


}
