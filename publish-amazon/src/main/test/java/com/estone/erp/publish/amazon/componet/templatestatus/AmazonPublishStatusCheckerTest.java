//package com.estone.erp.publish.amazon.componet.templatestatus;
//
//import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//
//import java.util.Collections;
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * AmazonPublishStatusChecker 的单元测试
// */
//class AmazonPublishStatusCheckerTest {
//
//    private AmazonPublishStatusChecker amazonPublishStatusChecker;
//
//    @BeforeEach
//    void setUp() {
//        amazonPublishStatusChecker = new AmazonPublishStatusChecker();
//    }
//
//    /**
//     * 创建一个 EsAmazonProductListing 测试对象
//     * @param sku SKU
//     * @param asin ASIN
//     * @param site 站点
//     * @return EsAmazonProductListing 实例
//     */
//    private EsAmazonProductListing createListing(String sku, String asin, String site) {
//        EsAmazonProductListing listing = new EsAmazonProductListing();
//        listing.setArticleNumber(sku);
//        listing.setSonAsin(asin);
//        listing.setSite(site);
//        // 可以根据需要设置其他字段
//        return listing;
//    }
//
//    @Test
//    @DisplayName("测试核心功能 - 正常的多SKU、多区域场景")
//    void testMatchLevelSkuAsin_HappyPath() {
//        // 1. 准备测试数据
//        List<EsAmazonProductListing> testData = List.of(
//                // SKU_A 在北美和欧洲都有，北美区US优先级更高，欧洲区UK优先级最高
//                createListing("SKU_A", "ASIN_A_US", "US"), // NA, Prio 1
//                createListing("SKU_A", "ASIN_A_MX", "MX"), // NA, Prio 3
//                createListing("SKU_A", "ASIN_A_UK", "UK"), // EU, Prio 1
//                // SKU_B 只在欧洲
//                createListing("SKU_B", "ASIN_B_DE", "DE"), // EU, Prio 2
//                // SKU_C 站点不在枚举中，应被忽略
//                createListing("SKU_C", "ASIN_C_JP", "JP"),
//                // SKU_D 只有一个有效listing
//                createListing("SKU_D", "ASIN_D_ES", "ES")  // EU, Prio 5
//        );
//
//        // 2. 调用被测方法
//        Map<String, Map<String, String>> result = amazonPublishStatusChecker.matchLevelSkuAsin(testData);
//
//        // 3. 验证结果
//        assertNotNull(result);
//        assertEquals(2, result.size(), "结果应包含北美和欧洲两个区域");
//        assertTrue(result.containsKey("North America"));
//        assertTrue(result.containsKey("Europe"));
//
//        // 验证北美区的结果
//        Map<String, String> northAmericaResult = result.get("North America");
//        assertNotNull(northAmericaResult);
//        assertEquals(1, northAmericaResult.size());
//        assertEquals("ASIN_A_US", northAmericaResult.get("SKU_A"), "SKU_A在北美应选择US的ASIN");
//
//        // 验证欧洲区的结果
//        Map<String, String> europeResult = result.get("Europe");
//        assertNotNull(europeResult);
//        assertEquals(3, europeResult.size(), "欧洲区应包含3个SKU");
//        assertEquals("ASIN_A_UK", europeResult.get("SKU_A"), "SKU_A在欧洲应选择UK的ASIN");
//        assertEquals("ASIN_B_DE", europeResult.get("SKU_B"));
//        assertEquals("ASIN_D_ES", europeResult.get("SKU_D"));
//    }
//
//    @Test
//    @DisplayName("测试边界条件 - 空列表和包含无效数据的列表")
//    void testMatchLevelSkuAsin_WithInvalidData() {
//        // 1. 测试空列表
//        Map<String, Map<String, String>> emptyResult = amazonPublishStatusChecker.matchLevelSkuAsin(Collections.emptyList());
//        assertNotNull(emptyResult);
//        assertTrue(emptyResult.isEmpty(), "输入为空列表时，结果应为空Map");
//
//        // 2. 准备包含无效数据的列表
//        List<EsAmazonProductListing> invalidData = List.of(
//                createListing(null, "ASIN_1", "US"),      // SKU为null
//                createListing("SKU_2", null, "DE"),         // ASIN为null
//                createListing("SKU_3", "ASIN_3", null),      // 站点为null
//                createListing("SKU_4", "ASIN_4", "FR")       // 唯一有效数据
//        );
//
//        // 3. 调用并验证
//        Map<String, Map<String, String>> resultWithInvalidData = amazonPublishStatusChecker.matchLevelSkuAsin(invalidData);
//        assertNotNull(resultWithInvalidData);
//        assertEquals(1, resultWithInvalidData.size(), "应只包含一个有效区域");
//        assertTrue(resultWithInvalidData.containsKey("Europe"));
//
//        Map<String, String> europeResult = resultWithInvalidData.get("Europe");
//        assertNotNull(europeResult);
//        assertEquals(1, europeResult.size(), "欧洲区应只包含一个有效SKU");
//        assertEquals("ASIN_4", europeResult.get("SKU_4"), "应只返回有效的数据");
//    }
//}