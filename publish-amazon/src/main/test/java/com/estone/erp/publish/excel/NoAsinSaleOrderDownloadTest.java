package com.estone.erp.publish.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.PublishApplication;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.excel.model.NoAsinSaleOrderExcelDO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-08-16 上午11:01
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
public class NoAsinSaleOrderDownloadTest {
    
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;

    @Test
    @SneakyThrows
    public void testDownloadSaleOrder() {
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        File writeFile = new File("D:\\temp\\file\\amazon_no_asin_sale_order.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, NoAsinSaleOrderExcelDO.class).useDefaultStyle(false).build();

        List<String> accountNumbers = List.of("BE-liangliang74", "DE-liangliang74", "ES-liangliang74",
                "FR-liangliang74", "IT-liangliang74", "NL-liangliang74",
                "PL-liangliang74", "SE-liangliang74", "TR-liangliang74",
                "UK-liangliang74");
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setOrderBy("syncDate");
        request.setSequence("ASC");
        request.setAccountNumberList(accountNumbers);
        request.setIsSaleQuantityNull(true);
        request.setSaleQuantityBean("order_num_total");
        request.setToSaleQuantity(0L);
        request.setFields(new String[]{"accountNumber", "isOnline", "site", "sellerSku", "articleNumber", "parentAsin", "sonAsin", "skuStatus", "forbidChannel", "openDate", "firstOpenDate"});

        int total = esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            log.info("listings size: {}", listings.size());
            checkAsinSaleOrder(listings);
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            List<NoAsinSaleOrderExcelDO> excelDOList = listings.stream().map(listing -> {
                NoAsinSaleOrderExcelDO noAsinSaleOrderExcelDO = BeanUtil.copyProperties(listing, NoAsinSaleOrderExcelDO.class);
                Date openDate = listing.getOpenDate();
                if (openDate == null) {
                    noAsinSaleOrderExcelDO.setOpenDate(listing.getFirstOpenDate());
                }
                return noAsinSaleOrderExcelDO;
            }).collect(Collectors.toList());
            // 导入下载数据
            excelWriter.write(excelDOList, writeSheet);
        });

        excelWriter.finish();
        try {
            TimeUnit.SECONDS.sleep(30);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("导入结束 total: {}", total);
    }


    private void checkAsinSaleOrder(List<EsAmazonProductListing> listings) {
        List<String> sonAsinList = listings.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonAsinList)) {
            return;
        }

//        ApiResult<List<AsinSalesVolume>> apiResult = OrderUtils.getAmazonAsinTodaySaleVolume(sonAsinList);
//        if (!apiResult.isSuccess()) {
//            log.error("获取亚马逊asin销量异常：size:{},{},{}", sonAsinList.size(), StringUtils.join(sonAsinList, ","), apiResult.getErrorMsg());
//            return;
//        }
//
//        List<AsinSalesVolume> asinSalesVolumes = apiResult.getResult();
//        Map<String, AsinSalesVolume> asinSalesVolumeMap = asinSalesVolumes.stream().collect(Collectors.toMap(AsinSalesVolume::getAsin, Function.identity()));


        // 查询全站点的ASIN销量
        Map<String, AmazonAsinSaleCountDO> asinSaleCountDOMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        listings.removeIf(listing -> {
            AmazonAsinSaleCountDO asinSaleCountDO = asinSaleCountDOMap.get(listing.getSonAsin());
            if (asinSaleCountDO == null) {
                return false;
            }
            if (asinSaleCountDO.getSale_total_count() == null || asinSaleCountDO.getSale_total_count() == 0) {
                // 订单数量为0，不再下载
//                AsinSalesVolume asinSalesVolume = asinSalesVolumeMap.get(listing.getSonAsin());
//                if (asinSalesVolume != null && asinSalesVolume.getSalesVolume() > 0) {
//                    return true;
//                }
                return false;
            } else {
                log.info("sonAsin:{}, sale_total_count:{}, order_num_total:{}", listing.getSonAsin(), asinSaleCountDO.getSale_total_count(), listing.getOrder_num_total());
                return true;
            }
        });

    }
}
