// Test script for handleUnitCount function
const fs = require('fs');
const path = require('path');

// Import the handleUnitCount function
// Note: In a real test environment, you would import the function from codeAdapter.js
// For this test, we'll define a simplified version of the function

function handleUnitCount(schemaProperties, countValue) {
    // 将JSON字符串格式的schema属性解析为JavaScript对象
    const schema = JSON.parse(schemaProperties);
    // 获取unit_count相关的schema定义
    const unitCountSchema = schema.unit_count;
    
    // 检查是否存在enum
    const hasEnum = unitCountSchema.items.properties.type && 
                   unitCountSchema.items.properties.type.properties.value && 
                   unitCountSchema.items.properties.type.properties.value.enum;
    
    // 设置默认值为1，如果提供了countValue则使用它
    const value = countValue !== undefined ? countValue : 1;
    
    let result = {
        "unit_count": [
            {
                "value": value
            }
        ]
    };
    
    // 当存在enum时
    if (hasEnum) {
        const enumValues = unitCountSchema.items.properties.type.properties.value.enum;
        // 优先匹配count值，如果enum中存在"count"则使用它，否则使用第一个元素
        const countIndex = enumValues.indexOf("count");
        const defaultType = countIndex !== -1 ? "count" : enumValues[0];
        
        result.unit_count[0].type = {
            "value": defaultType,
            "language_tag": "en_US"
        };
    } else {
        // 当不存在enum时使用默认值"count"
        result.unit_count[0].type = {
            "value": "count",
            "language_tag": "en_US"
        };
    }
    
    return result;
}

// Read the schema files
const toolSchemaPath = path.join(__dirname, 'tool.json');
const homeSchemaPath = path.join(__dirname, 'home.json');

const toolSchema = fs.readFileSync(toolSchemaPath, 'utf8');
const homeSchema = fs.readFileSync(homeSchemaPath, 'utf8');

// Test cases
console.log("Test 1: Tool schema without count value");
const result1 = handleUnitCount(toolSchema);
console.log(JSON.stringify(result1, null, 2));
console.log("Expected: unit_count[0].value = 1, unit_count[0].type.value = 'count'");

console.log("\nTest 2: Tool schema with count value = 5");
const result2 = handleUnitCount(toolSchema, 5);
console.log(JSON.stringify(result2, null, 2));
console.log("Expected: unit_count[0].value = 5, unit_count[0].type.value = 'count'");

console.log("\nTest 3: Home schema without count value");
const result3 = handleUnitCount(homeSchema);
console.log(JSON.stringify(result3, null, 2));
console.log("Expected: unit_count[0].value = 1, unit_count[0].type.value = 'count'");

console.log("\nTest 4: Home schema with count value = 10");
const result4 = handleUnitCount(homeSchema, 10);
console.log(JSON.stringify(result4, null, 2));
console.log("Expected: unit_count[0].value = 10, unit_count[0].type.value = 'count'");

// Test with a modified tool schema where "count" is not the first enum value
const modifiedToolSchema = JSON.parse(toolSchema);
if (modifiedToolSchema.unit_count.items.properties.type.properties.value.enum) {
    // Move "count" to the end of the enum array
    const enumValues = modifiedToolSchema.unit_count.items.properties.type.properties.value.enum;
    const countIndex = enumValues.indexOf("count");
    if (countIndex !== -1) {
        enumValues.splice(countIndex, 1);
        enumValues.push("count");
    }
}

console.log("\nTest 5: Modified tool schema (count is not the first enum value)");
const result5 = handleUnitCount(JSON.stringify(modifiedToolSchema));
console.log(JSON.stringify(result5, null, 2));
console.log("Expected: unit_count[0].value = 1, unit_count[0].type.value = 'count'");