package com.estone.erp.publish.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.BooleanCodeConverter;
import com.estone.erp.publish.component.converter.SkuStatusCodeConverter;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-08-16 下午12:05
 */
@Data
public class NoAsinSaleOrderExcelDO {
    @ExcelProperty(value = "店铺")
    private String accountNumber;
    @ExcelProperty(value = "站点")
    private String site;
    @ExcelProperty(value = "sellerSku")
    private String sellerSku;
    @ExcelProperty(value = "是否在线", converter = BooleanCodeConverter.class)
    private Boolean isOnline;
    @ExcelProperty(value = "sku")
    private String articleNumber;
    @ExcelProperty(value = "父asin")
    private String parentAsin;
    @ExcelProperty(value = "子asin")
    private String sonAsin;
    @ExcelProperty(value = "单品状态", converter = SkuStatusCodeConverter.class)
    private String skuStatus;
    @ExcelProperty(value = "禁售平台")
    private String forbidChannel;
    @ExcelProperty(value = "上架时间")
    private Date openDate;
}
