package com.estone.erp.publish.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-08-19 上午9:47
 */
@Data
public class EmojiListingExcelDO {
    @ExcelProperty("店铺")
    private String accountNumber;
    @ExcelProperty("sellerSku")
    private String sellerSku;
    @ExcelProperty("sku")
    private String sku;
    @ExcelProperty("子asin")
    private String sonAsin;
    @ExcelProperty("总销量")
    private Integer totalSaleCount;
    @ExcelProperty("30天销量")
    private Integer sale30dCount;
    @ExcelProperty("销售")
    private String saleUser;
    @ExcelProperty("销售组长")
    private String saleLearner;
    @ExcelProperty("销售主管")
    private String saleManager;
}
