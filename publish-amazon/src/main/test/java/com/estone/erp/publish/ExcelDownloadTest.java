package com.estone.erp.publish;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonSkuPublishNumberDO;
import com.estone.erp.publish.elasticsearch.model.beanresponse.EsCompositeAggsResponse;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024-04-12 15:35
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishApplication.class)
@ActiveProfiles("local")
public class ExcelDownloadTest {

    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;

    @Test
    public void testDownloadExcel() {
        String searchAfter = "";
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        File writeFile = new File("D:\\temp\\file\\家居用品_家庭装饰品.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, ArticlePublishExcelDO.class).build();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            EsCompositeAggsResponse<AmazonSkuPublishNumberDO> response = esAmazonProductListingService.compositeSkuPublishNumberSearch("161", List.of("US", "DE"), searchAfter);
            if (response.getDataList().isEmpty()) {
                break;
            }

            List<ArticlePublishExcelDO> dataList = Collections.synchronizedList(new ArrayList<>());
            List<CompletableFuture<ArticlePublishExcelDO>> futureList = com.google.common.collect.Lists.newArrayList();
            response.getDataList().forEach(data -> {
                CompletableFuture<ArticlePublishExcelDO> completableFuture = CompletableFuture.supplyAsync(() -> {
                    log.info("sku:{}, us:{}, de:{}", data.getSku(), data.getUsSiteCount(), data.getDeSiteCount());
                    ArticlePublishExcelDO articlePublishExcelDO = new ArticlePublishExcelDO();
                    articlePublishExcelDO.setSku(data.getSku());
                    articlePublishExcelDO.setUsSiteCount(data.getUsSiteCount() == null ? 0 : data.getUsSiteCount());
                    articlePublishExcelDO.setDeSiteCount(data.getDeSiteCount() == null ? 0 : data.getDeSiteCount());

                    ProductInfoVO skuInfo = ProductUtils.getSkuInfo(data.getSku());
                    if (StringUtils.isNotEmpty(skuInfo.getMainSku())) {
                        articlePublishExcelDO.setCategory(skuInfo.getCategoryCnName());
                        Long createAt = skuInfo.getCreateAt();

                        if (createAt != null) {
                            Timestamp timestamp = new Timestamp(createAt);
                            LocalDateTime createDateTime = timestamp.toLocalDateTime();
                            articlePublishExcelDO.setManageTime(dateTimeFormatter.format(createDateTime));
                        }
                        SkuStatusEnum build = SkuStatusEnum.build(skuInfo.getSkuStatus());
                        if (build != null) {
                            articlePublishExcelDO.setStatus(build.getName());
                        } else {
                            articlePublishExcelDO.setStatus(skuInfo.getSkuStatus());
                        }
                        articlePublishExcelDO.setDisablePlatform(skuInfo.getForbidChannel());
                    }
                    return articlePublishExcelDO;
                }, AmazonExecutors.OFFLINE_LIMIT_POOL).whenComplete((result, throwable) -> {
                    if (result != null) {
                        dataList.add(result);
                    }
                    if (throwable != null) {
                        log.error("异常", throwable);
                    }
                });
                futureList.add(completableFuture);
            });

            try {
                //将多个任务，汇总成一个任务，总共耗时不超时x秒
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get(10L * futureList.size(), TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("CompletableFuture.allOf Exception error.", e);
            }
            excelWriter.write(dataList, writeSheet);
            searchAfter = response.getSearchAfter();
        }
        excelWriter.finish();
        try {
            TimeUnit.SECONDS.sleep(60);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("download excel success");
    }


    @Test
    public void downloadNoUploadAsin() {
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        File writeFile = new File("D:\\temp\\file\\no_upload_asin.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(writeFile, ListingAsinExcelDO.class).build();

        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setFields(new String[]{"id", "accountNumber", "sellerSku", "sonAsin", "parentAsin"});
        request.setIsOnline(true);
        request.setExistGpsr(false);
        request.setExcludeItemTypeList(List.of(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode()));
        request.setOrderBy("createDate");
        request.setSiteList(List.of("DE", "ES", "FR", "IT", "PL", "BE", "NL", "SE"));

        AtomicLong total = new AtomicLong(0);
        Map<String, ListingAsinExcelDO> asinExcelDOMap = new ConcurrentHashMap<>(1000_0);
        int executorTask = esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            // 过滤存在gpsr的数据
            for (EsAmazonProductListing listing : listings) {
                String asin = Optional.ofNullable(listing.getSonAsin()).orElseGet(listing::getParentAsin);
                ListingAsinExcelDO asinExcelDO = asinExcelDOMap.getOrDefault(asin, new ListingAsinExcelDO());
                asinExcelDO.addAccounts(listing.getAccountNumber());
                asinExcelDO.addSites(listing.getSite());
                asinExcelDOMap.put(asin, asinExcelDO);
                total.incrementAndGet();
                if (total.get() % 1000 == 0) {
                    log.info("total:{}, asin:{}", total.get(), asinExcelDOMap.size());
                }
            }
        });
        log.info("完成 total:{}, asin:{} executorTask:{}", total.get(), asinExcelDOMap.size(), executorTask);
        Collection<ListingAsinExcelDO> values = asinExcelDOMap.values();
        List<List<ListingAsinExcelDO>> partition = Lists.partition(new ArrayList<>(values), 1000);
        for (List<ListingAsinExcelDO> subList : partition) {
            excelWriter.write(subList, writeSheet);
        }
        excelWriter.finish();
        log.info("download no upload asin success");
    }
}
