<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context"
                    name="springAppName"
                    source="spring.application.name"/>

    <property name="LOG_FILE"
              value="/var/log/zipkin/${springAppName}"/>

    <property name="APP_NAME"
              value="publish_amazon"/>

    <property name="log_path" value="/var/log/" />

    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}]){yellow} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd' 'HH:mm:ss.sss}] [%thread] %-5level [%C{1} %L] : %msg%n</pattern>
        </encoder>
    </appender>

    <!--配置info日志级别单独文件-->
    <appender name="FILE-info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log_path}/info/${APP_NAME}_info_%d{yyyy-MM-dd}.%i.log
            </FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <!-- 日志总保存量为10GB -->
            <totalSizeCap>10GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大1G时会被压缩和切割 -->
                <maxFileSize>1GB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
        </encoder>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--配置debug日志级别单独文件-->
    <appender name="FILE-debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log_path}/debug/${APP_NAME}_debug_%d{yyyy-MM-dd}.%i.log.zip
            </FileNamePattern>
            <MaxHistory>15</MaxHistory>
            <!-- 日志总保存量为10GB -->
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大1G时会被压缩和切割 -->
                <maxFileSize>1GB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--配置error日志级别单独文件-->
    <appender name="FILE-error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log_path}/error/${APP_NAME}_error_%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>30</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
        </encoder>
        <!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>128MB</MaxFileSize>
        </triggeringPolicy>-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--配置warn日志级别单独文件-->
    <appender name="FILE-warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log_path}/warn/${APP_NAME}_warn_%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
        </encoder>
        <!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>128MB</MaxFileSize>
        </triggeringPolicy>-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="com.estone.erp.common.util.Logstash" level="INFO" additivity="false">
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="com.estone.erp" level="INFO" additivity="false">
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="STDOUT" />

    </logger>
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="STDOUT" />
    </logger>
    <logger name="com.netflix.discovery.DiscoveryClient" level="WARN" additivity="false">
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.mybatis" level="WARN" additivity="false">
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="STDOUT" />
    </logger>
    <!--查看源码，发现sql打印的日志的级别由com.estone.erp.publish.amazon.mapper对应mapper包下的类日志级别进行控制-->
    <logger name="com.estone.erp.publish.amazon.mapper" level="WARN" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="FILE-warn" />
    </logger>
    <logger name="com.estone.erp.publish.tidb.*.mapper" level="WARN" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="FILE-warn" />
    </logger>
    <logger name="com.alibaba.nacos.client.naming.updater" level="FILE-warn" additivity="true">
        <appender-ref ref="FILE-warn" />
        <appender-ref ref="FILE-info" />
        <appender-ref ref="FILE-debug" />
        <appender-ref ref="FILE-error" />
        <appender-ref ref="STDOUT" />
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>