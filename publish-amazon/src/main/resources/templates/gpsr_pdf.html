<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width,initial-scale=1.0" name="viewport"/>
    <meta content="no-referrer" name="referrer"/>
    <title>AMAZON-GPSR</title>
    <style>
        @page {
            size: 800px 800px;
            margin: 0;
            padding: 0;
            border: 0;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            width: 99%;
            height: 100%;
            background-color: #FFFFFF;
        }

        .container {
            width: 99%;
            margin: 0 auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 10px;
            border: 1px solid #000000;
            text-align: left;
        }

        th {
            text-align: right;
            background-color: #ffffff;
        }

        .eu-rep {
            padding: 0;
        }

        .eu-rep-box {
            padding: 0 10px;
            display: flex;
            margin: 0 auto;
            justify-content: center;
            align-items: center;
            width: 136px;
            height: 60px;
            line-height: 60px;
            text-align: center;
        }

        .eu, .rep {
            font-weight: bold;
            width: 60px;
            height: 100%;
            line-height: 60px;
            border: 4px solid #000;
            float: left;
        }

        .eu {
            border-right: 1px solid #000000;
        }

        .logo {
            text-align: center;
        }

        .logo img {
            width: 80px;
            margin-bottom: 5px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .logo .img-2 {
            width: 250px;
        }

        .logo .img-rohs {
            width: 120px;
        }

        .age-box {
            position: relative;
            width: 80px;
            height: 80px;
            border: 6px #000 solid;
            font-weight: bold;
            margin-bottom: 5px;
            margin-right: 5px;
        }

        .age-box .age {
            font-size: 45px;
            text-align: center;
            line-height: 80px;
            font-weight: bold;
            color: #000;
        }

        .age-box .mark {
            position: absolute;
            right: 0px;
            top: -9px;
            font-size: 40px;
            color: #000;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <table>
        <tr>
            <th th:text="#{serial_number}">Serial Number:</th>
            <td th:text="${gpsr.sellerSku}">7HH506927-S_uKnxLIy888</td>
        </tr>
        <tr>
            <th th:text="#{manufacturer}">Manufacturer:</th>
            <td th:text="${gpsr.manufacturerEn}">Jingdezhen Minghuahao E-commerce Co., Ltd.</td>
        </tr>
        <tr>
            <th th:text="#{address}">Address:</th>
            <td th:text="${gpsr.manufacturerAddress}">B302, 3rd Floor, Agricultural Market, Liyang Town, Changjiang
                District, Jingdezhen, Jiangxi Province,
                China
            </td>
        </tr>
        <tr>
            <th th:text="#{email}">Email:</th>
            <td th:text="${gpsr.manufacturerEmail}"><EMAIL></td>
        </tr>
        <tr>
            <th>NEW</th>
            <td style="text-align: right"><strong>MADE IN CHINA</strong></td>
        </tr>
        <tr>
            <th th:text="#{warning}">Warming:</th>
            <td>
                <div style="margin-top: 10px">
                    <strong>UK</strong>:
                    <span th:text="${gpsr.langWarnings.get('UK')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>FR</strong>:
                    <span th:text="${gpsr.langWarnings.get('FR')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>DE</strong>:
                    <span th:text="${gpsr.langWarnings.get('DE')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>ES</strong>:
                    <span th:text="${gpsr.langWarnings.get('ES')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>IT</strong>:
                    <span th:text="${gpsr.langWarnings.get('IT')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>PL</strong>:
                    <span th:text="${gpsr.langWarnings.get('PL')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>NL</strong>:
                    <span th:text="${gpsr.langWarnings.get('NL')}">Ensure Proper Installation.</span>
                </div>
                <div style="margin-top: 10px">
                    <strong>SE</strong>:
                    <span th:text="${gpsr.langWarnings.get('SE')}">Ensure Proper Installation.</span>
                </div>

                <!--                <div th:each="map : ${gpsr.langWarnings}" style="margin-top: 10px">-->
                <!--                    <strong th:text="${map.key}">UK</strong>:-->
                <!--                    <span th:text="${map.value}">Ensure Proper Installation.</span>-->
                <!--                </div>-->
            </td>
        </tr>
        <tr>
            <th class="eu-rep">
                <div class="eu-rep-box">
                    <div class="eu">EU</div>
                    <div class="rep">REP</div>
                </div>
            </th>
            <td>
                <div>
                    E-CrossStu GmbH<br/>
                    Germany 69 Mainzer Landstrasse Frankfurt am Main Hessen 60329 <br/>
                    Contact:Yating He + 69332967674<br/>
                    <EMAIL><br/>
                </div>
            </td>
        </tr>
        <!--        <tr>-->
        <!--            <th colspan="2" style="text-align: left;">-->
        <!--                <div class="logo" id="logo" th:utext="${gpsr.templateImageTag}">-->
        <!--                </div>-->
        <!--            </th>-->
        <!--        </tr>-->
    </table>
</div>
</body>
</html>
