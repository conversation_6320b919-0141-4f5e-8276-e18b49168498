<?xml version="1.0"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

    <!--
    $Id: //depot/branches/project/st-release-4/src/partner-platform/applications/merchants/merchant-application-config/stylesheets/Components/1.0/CatPIL.xsd#4 $
 
    AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
    confidential and proprietary information of Amazon.com and may not be reproduced, 
    distributed or used, in whole or in part, for any purpose other than as necessary 
    to list products for sale on the www.amazon.com web site pursuant to an agreement 
    with Amazon.com.
    -->


     <xsd:element name="CatPIL">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="pi" type="piType" minOccurs="0" maxOccurs="unbounded"/>
            <xsd:element name="ItemSKU" minOccurs="0">
            <xsd:simpleType>
	        <xsd:restriction base="xsd:normalizedString">
        	    <xsd:minLength value="1"/>
	            <xsd:maxLength value="40"/>
        	</xsd:restriction>
        	</xsd:simpleType>
	  </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
     </xsd:element>


     <xsd:complexType name="piType">
       <xsd:simpleContent>
         <xsd:extension base="xsd:string">
           <xsd:attribute name="name" type="xsd:string"/>
           <xsd:attribute name="type" type="xsd:string"/>
           <xsd:attribute name="unit" type="xsd:string"/>
           <xsd:attribute name="unitType" type="xsd:string"/>
           <xsd:attribute name="unitValue" type="xsd:string"/>
           <xsd:attribute name="destination" type="xsd:string"/>
         </xsd:extension>
       </xsd:simpleContent>
     </xsd:complexType>

</xsd:schema>
