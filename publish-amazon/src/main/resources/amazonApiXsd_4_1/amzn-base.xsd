<?xml version="1.0"?>
<!-- Revision="$Revision: #C_1 $" -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<!--
    $Date: 2012/03/21 $

    AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
    confidential and proprietary information of Amazon.com and may not be reproduced, 
    distributed or used, in whole or in part, for any purpose other than as necessary 
    to list products for sale on the www.amazon.com web site pursuant to an agreement 
    with Amazon.com.
    -->
	<!--
    ##################################################
    # Address element
    ##################################################
    -->
	<xsd:element name="Address" type="AddressType"/>
	<xsd:complexType name="AddressType">
		<xsd:sequence>
			<xsd:element name="Name" type="String"/>
			<xsd:element name="FormalTitle" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>e.g.  Mr., Ms., etc.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:normalizedString">
						<xsd:maxLength value="10"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="GivenName" type="String" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Usually the customer's first name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FamilyName" type="String" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Usually the customer's last name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AddressFieldOne" type="AddressLine"/>
			<xsd:element name="AddressFieldTwo" type="AddressLine" minOccurs="0"/>
			<xsd:element name="AddressFieldThree" type="AddressLine" minOccurs="0"/>
			<xsd:element name="City" type="String" minOccurs="0"/>
			<xsd:element name="County" type="String" minOccurs="0"/>
			<xsd:element name="StateOrRegion" type="String" minOccurs="0"/>
			<xsd:element name="PostalCode" type="String" minOccurs="0"/>
			<xsd:element name="CountryCode">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:minLength value="2"/>
						<xsd:maxLength value="2"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="PhoneNumber" type="PhoneNumberType" minOccurs="0" maxOccurs="3"/>
			<xsd:element name="isDefaultShipping" type="xsd:boolean" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Only one default shipping address can exist at any given
						time. If more than one address has this set to "true," then the last one
						will become the default.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="isDefaultBilling" type="xsd:boolean" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Only one default billing address can exist at any given time.
						If more than one address has this set to "true," then the last one will
						become the default.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="isDefaultOneClick" type="xsd:boolean" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Only one default OneClick address can exist at any given
						time. If more than one address has this set to "true," then the last one
						will become the default.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--
    ##################################################
    # Address element, support non-city. Used by FBA.
    ##################################################
    -->
	<xsd:complexType name="AddressTypeSupportNonCity">
		<xsd:sequence>
			<xsd:element name="Name" type="String"/>
			<xsd:element name="AddressFieldOne" type="AddressLine"/>
			<xsd:element name="AddressFieldTwo" type="AddressLine" minOccurs="0"/>
			<xsd:element name="AddressFieldThree" type="AddressLine" minOccurs="0"/>
			<xsd:element name="City" type="String" minOccurs="0"/>
			<xsd:element name="DistrictOrCounty" type="String" minOccurs="0"/>
			<xsd:element name="County" type="String" minOccurs="0"/>
			<xsd:element name="StateOrRegion" type="String" minOccurs="0"/>
			<xsd:element name="PostalCode" type="String" minOccurs="0"/>
			<xsd:element name="CountryCode">
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:minLength value="2"/>
						<xsd:maxLength value="2"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="PhoneNumber" type="String" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="AddressLine">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="60"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="PhoneNumberType">
		<xsd:simpleContent>
			<xsd:extension base="String">
				<xsd:attribute name="Type" use="optional">
					<xsd:annotation>
						<xsd:documentation>Defaults to "Voice." Currently, only two voice numbers
							and one fax number are stored.</xsd:documentation>
					</xsd:annotation>
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="Voice"/>
							<xsd:enumeration value="Fax"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:attribute>
				<xsd:attribute name="Description" use="optional">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:maxLength value="30"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!--

    ##################################################
    # Email Address Type
    ##################################################

    -->
	<xsd:complexType name="EmailAddressType">
		<xsd:simpleContent>
			<xsd:extension base="EmailBase">
				<xsd:attribute name="PreferredFormat" use="optional">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="TextOnly"/>
							<xsd:enumeration value="HTML"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="EmailBase">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:pattern value="[^@]+@[^@\.]+(\.[^@\.]+)+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
    ##################################################
    # Amazon fees type
    ##################################################
    -->
	<xsd:complexType name="AmazonFees">
		<xsd:sequence>
			<xsd:element name="Fee" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Type">
							<xsd:simpleType>
								<xsd:restriction base="xsd:string"/>
							</xsd:simpleType>
						</xsd:element>
						<xsd:element name="Amount" type="CurrencyAmount"/>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--
    ##################################################
    # Battery type
    ##################################################
    -->
	<xsd:element name="Battery">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="AreBatteriesIncluded" type="xsd:boolean" minOccurs="0"/>
				<xsd:element name="AreBatteriesRequired" type="xsd:boolean" minOccurs="0"/>
				<xsd:element name="BatterySubgroup" minOccurs="0" maxOccurs="3">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="BatteryType">
								<xsd:simpleType>
									<xsd:restriction base="StringNotNull">
										<xsd:enumeration value="battery_type_2/3A"/>
										<xsd:enumeration value="battery_type_4/3A"/>
										<xsd:enumeration value="battery_type_4/5A"/>
										<xsd:enumeration value="battery_type_9v"/>
										<xsd:enumeration value="battery_type_12v"/>
										<xsd:enumeration value="battery_type_a"/>
										<xsd:enumeration value="battery_type_a76"/>
										<xsd:enumeration value="battery_type_aa"/>
										<xsd:enumeration value="battery_type_aaa"/>
										<xsd:enumeration value="battery_type_aaaa"/>
										<xsd:enumeration value="battery_type_c"/>
										<xsd:enumeration value="battery_type_cr123a"/>
										<xsd:enumeration value="battery_type_cr2"/>
										<xsd:enumeration value="battery_type_cr5"/>
										<xsd:enumeration value="battery_type_d"/>
										<xsd:enumeration value="battery_type_lithium_ion"/>
										<xsd:enumeration value="battery_type_lithium_metal"/>
										<xsd:enumeration value="battery_type_L-SC"/>
										<xsd:enumeration value="battery_type_p76"/>
										<xsd:enumeration value="battery_type_product_specific"/>
										<xsd:enumeration value="battery_type_SC"/>
										<xsd:enumeration value="nonstandard_battery"/>
										<xsd:enumeration value="lr44"/>
										<xsd:enumeration value="unknown"/>
										<xsd:enumeration value="cr2032"/>
										<xsd:enumeration value="lr41"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
							<xsd:element name="NumberOfBatteries" type="xsd:positiveInteger"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
		
    ##################################################
    # Buyer price type
    ##################################################
    
	-->
	<xsd:complexType name="BuyerPrice">
		<xsd:sequence>
			<xsd:element name="Component" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Type">
							<xsd:simpleType>
								<xsd:restriction base="xsd:string">
									<xsd:enumeration value="Principal"/>
									<xsd:enumeration value="Shipping"/>
									<xsd:enumeration value="CODFee"/>
									<xsd:enumeration value="Tax"/>
									<xsd:enumeration value="ShippingTax"/>
									<xsd:enumeration value="RestockingFee"/>
									<xsd:enumeration value="RestockingFeeTax"/>
									<xsd:enumeration value="GiftWrap"/>
									<xsd:enumeration value="GiftWrapTax"/>
									<xsd:enumeration value="Surcharge"/>
									<xsd:enumeration value="ReturnShipping"/>
									<xsd:enumeration value="Goodwill"/>
									<xsd:enumeration value="ExportCharge"/>
									<xsd:enumeration value="COD"/>
									<xsd:enumeration value="CODTax"/>
									<xsd:enumeration value="Other"/>
                                                                        <xsd:enumeration value="FreeReplacementReturnShipping"/>
                                                                        <xsd:enumeration value="VatExclusiveItemPrice"/>
                                                                        <xsd:enumeration value="VatExclusiveShippingPrice"/>
                                                                    </xsd:restriction>
							</xsd:simpleType>
						</xsd:element>
						<xsd:element name="Amount" type="CurrencyAmount"/>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--
    ##################################################
    # Direct Payment type
    ##################################################
  	-->
	<xsd:complexType name="DirectPaymentType">
		<xsd:sequence>
			<xsd:element name="Component" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Type" type="xsd:string"/>
						<xsd:element name="Amount" type="CurrencyAmount"/>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--		
    ##################################################
    # Currency amount type
    ##################################################
    
	-->
	<xsd:complexType name="CurrencyAmount">
		<xsd:simpleContent>
			<xsd:extension base="BaseCurrencyAmount">
				<xsd:attribute name="currency" type="BaseCurrencyCode"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="PositiveCurrencyAmount">
		<xsd:simpleContent>
			<xsd:extension base="BasePositiveCurrencyAmount">
				<xsd:attribute name="currency" type="BaseCurrencyCode" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="BaseCurrencyCode">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="USD"/>
			<xsd:enumeration value="GBP"/>
			<xsd:enumeration value="EUR"/>
			<xsd:enumeration value="JPY"/>
			<xsd:enumeration value="CAD"/>
			<xsd:enumeration value="CNY"/>
			<xsd:enumeration value="INR"/>
			<xsd:enumeration value="MXN"/>
			<xsd:enumeration value="BRL"/>
			<xsd:enumeration value="AUD"/>
			<xsd:enumeration value="TRY"/>
			<xsd:enumeration value="SGD"/>
			<xsd:enumeration value="SEK"/>
			<xsd:enumeration value="PLN"/>
			<xsd:enumeration value="AED"/>
			<xsd:enumeration value="SAR"/>
			<xsd:enumeration value="EGP"/>
			<xsd:enumeration value="ZAR"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GlobalCurrencyCode">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AED"/>
			<xsd:enumeration value="ALL"/>
			<xsd:enumeration value="ARS"/>
			<xsd:enumeration value="ATS"/>
			<xsd:enumeration value="AUD"/>
			<xsd:enumeration value="BAM"/>
			<xsd:enumeration value="BEF"/>
			<xsd:enumeration value="BGN"/>
			<xsd:enumeration value="BHD"/>
			<xsd:enumeration value="BOB"/>
			<xsd:enumeration value="BRL"/>
			<xsd:enumeration value="BYR"/>
			<xsd:enumeration value="CAD"/>
			<xsd:enumeration value="CHF"/>
			<xsd:enumeration value="CLP"/>
			<xsd:enumeration value="CNY"/>
			<xsd:enumeration value="COP"/>
			<xsd:enumeration value="CRC"/>
			<xsd:enumeration value="CSD"/>
			<xsd:enumeration value="CZK"/>
			<xsd:enumeration value="DEM"/>
			<xsd:enumeration value="DKK"/>
			<xsd:enumeration value="DOP"/>
			<xsd:enumeration value="DZD"/>
			<xsd:enumeration value="EEK"/>
			<xsd:enumeration value="EGP"/>
			<xsd:enumeration value="ESP"/>
			<xsd:enumeration value="EUR"/>
			<xsd:enumeration value="FIM"/>
			<xsd:enumeration value="FRF"/>
			<xsd:enumeration value="GBP"/>
			<xsd:enumeration value="GRD"/>
			<xsd:enumeration value="GTQ"/>
			<xsd:enumeration value="HKD"/>
			<xsd:enumeration value="HNL"/>
			<xsd:enumeration value="HRK"/>
			<xsd:enumeration value="HUF"/>
			<xsd:enumeration value="IDR"/>
			<xsd:enumeration value="ILS"/>
			<xsd:enumeration value="INR"/>
			<xsd:enumeration value="IQD"/>
			<xsd:enumeration value="ISK"/>
			<xsd:enumeration value="ITL"/>
			<xsd:enumeration value="JOD"/>
			<xsd:enumeration value="JPY"/>
			<xsd:enumeration value="KRW"/>
			<xsd:enumeration value="KWD"/>
			<xsd:enumeration value="LBP"/>
			<xsd:enumeration value="LTL"/>
			<xsd:enumeration value="LUF"/>
			<xsd:enumeration value="LVL"/>
			<xsd:enumeration value="LYD"/>
			<xsd:enumeration value="MAD"/>
			<xsd:enumeration value="MKD"/>
			<xsd:enumeration value="MXN"/>
			<xsd:enumeration value="MYR"/>
			<xsd:enumeration value="NIO"/>
			<xsd:enumeration value="NOK"/>
			<xsd:enumeration value="NZD"/>
			<xsd:enumeration value="OMR"/>
			<xsd:enumeration value="PAB"/>
			<xsd:enumeration value="PEN"/>
			<xsd:enumeration value="PHP"/>
			<xsd:enumeration value="PLN"/>
			<xsd:enumeration value="PTE"/>
			<xsd:enumeration value="PYG"/>
			<xsd:enumeration value="QAR"/>
			<xsd:enumeration value="RON"/>
			<xsd:enumeration value="RSD"/>
			<xsd:enumeration value="RUB"/>
			<xsd:enumeration value="SAR"/>
			<xsd:enumeration value="SDG"/>
			<xsd:enumeration value="SEK"/>
			<xsd:enumeration value="SGD"/>
			<xsd:enumeration value="SKK"/>
			<xsd:enumeration value="SVC"/>
			<xsd:enumeration value="SYP"/>
			<xsd:enumeration value="THB"/>
			<xsd:enumeration value="TND"/>
			<xsd:enumeration value="TRY"/>
			<xsd:enumeration value="TWD"/>
			<xsd:enumeration value="UAH"/>
			<xsd:enumeration value="USD"/>
			<xsd:enumeration value="UYU"/>
			<xsd:enumeration value="VEF"/>
			<xsd:enumeration value="VND"/>
			<xsd:enumeration value="YER"/>
			<xsd:enumeration value="ZAR"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EnergyUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="BTU"/>
			<xsd:enumeration value="watts"/>
			<xsd:enumeration value="joules"/>
			<xsd:enumeration value="kilojoules"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="WaterResistantType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="not_water_resistant"/>
			<xsd:enumeration value="water_resistant"/>
			<xsd:enumeration value="waterproof"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AntennaTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="fixed"/>
			<xsd:enumeration value="internal"/>
			<xsd:enumeration value="retractable"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ModemTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="analog_modem"/>
			<xsd:enumeration value="data_fax_voice"/>
			<xsd:enumeration value="isdn_modem"/>
			<xsd:enumeration value="cable"/>
			<xsd:enumeration value="data_modem"/>
			<xsd:enumeration value="network_modem"/>
			<xsd:enumeration value="cellular"/>
			<xsd:enumeration value="digital"/>
			<xsd:enumeration value="unknown_modem_type"/>
			<xsd:enumeration value="csu"/>
			<xsd:enumeration value="dsl"/>
			<xsd:enumeration value="voice"/>
			<xsd:enumeration value="data_fax"/>
			<xsd:enumeration value="dsu"/>
			<xsd:enumeration value="winmodem"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DockingStationExternalInterfaceTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="firewire_1600"/>
			<xsd:enumeration value="firewire_3200"/>
			<xsd:enumeration value="firewire_400"/>
			<xsd:enumeration value="firewire_800"/>
			<xsd:enumeration value="firewire_esata"/>
			<xsd:enumeration value="usb1.0"/>
			<xsd:enumeration value="usb1.1"/>
			<xsd:enumeration value="usb2.0"/>
			<xsd:enumeration value="usb3.0"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RemovableMemoryValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="1_4_inch_audio"/>
			<xsd:enumeration value="2_5_mm_audio"/>
			<xsd:enumeration value="3.0_v_ttl"/>
			<xsd:enumeration value="3_5_floppy"/>
			<xsd:enumeration value="3_5_mm_audio"/>
			<xsd:enumeration value="ata"/>
			<xsd:enumeration value="ata_flash_card"/>
			<xsd:enumeration value="audio_video_port"/>
			<xsd:enumeration value="bluetooth"/>
			<xsd:enumeration value="built_in_flash_memory"/>
			<xsd:enumeration value="cd-r"/>
			<xsd:enumeration value="cd_rw"/>
			<xsd:enumeration value="cdr_drive"/>
			<xsd:enumeration value="compact_disc"/>
			<xsd:enumeration value="compact_flash_card"/>
			<xsd:enumeration value="compact_flash_type_i_or_ii"/>
			<xsd:enumeration value="compactflash_type_i"/>
			<xsd:enumeration value="compactflash_type_ii"/>
			<xsd:enumeration value="component_video"/>
			<xsd:enumeration value="composite_video"/>
			<xsd:enumeration value="d_sub"/>
			<xsd:enumeration value="dmi"/>
			<xsd:enumeration value="dssi"/>
			<xsd:enumeration value="dvd_r"/>
			<xsd:enumeration value="dvd_rw"/>
			<xsd:enumeration value="dvi_x_1"/>
			<xsd:enumeration value="dvi_x_2"/>
			<xsd:enumeration value="dvi_x_4"/>
			<xsd:enumeration value="eide"/>
			<xsd:enumeration value="eisa"/>
			<xsd:enumeration value="ethernet"/>
			<xsd:enumeration value="express_card"/>
			<xsd:enumeration value="fibre_channel"/>
			<xsd:enumeration value="firewire_1600"/>
			<xsd:enumeration value="firewire_3200"/>
			<xsd:enumeration value="firewire_400"/>
			<xsd:enumeration value="firewire_800"/>
			<xsd:enumeration value="firewire_esata"/>
			<xsd:enumeration value="game_port"/>
			<xsd:enumeration value="gbic"/>
			<xsd:enumeration value="hdmi"/>
			<xsd:enumeration value="headphone"/>
			<xsd:enumeration value="hp_hsc"/>
			<xsd:enumeration value="hp_pb"/>
			<xsd:enumeration value="hs_mmc"/>
			<xsd:enumeration value="ibm_microdrive"/>
			<xsd:enumeration value="ide"/>
			<xsd:enumeration value="ieee_1284"/>
			<xsd:enumeration value="ieee_1394"/>
			<xsd:enumeration value="infrared"/>
			<xsd:enumeration value="internal_w_removable_media"/>
			<xsd:enumeration value="iomega_clik_disk"/>
			<xsd:enumeration value="isa"/>
			<xsd:enumeration value="isp"/>
			<xsd:enumeration value="lanc"/>
			<xsd:enumeration value="mca"/>
			<xsd:enumeration value="media_card"/>
			<xsd:enumeration value="memory_stick"/>
			<xsd:enumeration value="memory_stick_duo"/>
			<xsd:enumeration value="memory_stick_micro"/>
			<xsd:enumeration value="memory_stick_pro"/>
			<xsd:enumeration value="memory_stick_pro_duo"/>
			<xsd:enumeration value="memory_stick_pro_hg_duo"/>
			<xsd:enumeration value="memory_stick_select"/>
			<xsd:enumeration value="memory_stick_xc"/>
			<xsd:enumeration value="memory_stick_xc_hg_micro"/>
			<xsd:enumeration value="memory_stick_xc_micro"/>
			<xsd:enumeration value="micard"/>
			<xsd:enumeration value="micro_sdhc"/>
			<xsd:enumeration value="micro_sdxc"/>
			<xsd:enumeration value="microsd"/>
			<xsd:enumeration value="mini_dvd"/>
			<xsd:enumeration value="mini_hdmi"/>
			<xsd:enumeration value="mini_pci"/>
			<xsd:enumeration value="mini_sdhc"/>
			<xsd:enumeration value="mini_sdxc"/>
			<xsd:enumeration value="minisd"/>
			<xsd:enumeration value="mmc_micro"/>
			<xsd:enumeration value="multimedia_card"/>
			<xsd:enumeration value="multimedia_card_mobile"/>
			<xsd:enumeration value="multimedia_card_plus"/>
			<xsd:enumeration value="multipronged_audio"/>
			<xsd:enumeration value="nubus"/>
			<xsd:enumeration value="parallel_interface"/>
			<xsd:enumeration value="pc_card"/>
			<xsd:enumeration value="pci"/>
			<xsd:enumeration value="pci_64"/>
			<xsd:enumeration value="pci_64_hot_plug"/>
			<xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
			<xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
			<xsd:enumeration value="pci_express_x4"/>
			<xsd:enumeration value="pci_express_x8"/>
			<xsd:enumeration value="pci_hot_plug"/>
			<xsd:enumeration value="pci_raid"/>
			<xsd:enumeration value="pci_x"/>
			<xsd:enumeration value="pci_x_1"/>
			<xsd:enumeration value="pci_x_100_mhz"/>
			<xsd:enumeration value="pci_x_133_mhz"/>
			<xsd:enumeration value="pci_x_16"/>
			<xsd:enumeration value="pci_x_16_gb"/>
			<xsd:enumeration value="pci_x_4"/>
			<xsd:enumeration value="pci_x_66_mhz"/>
			<xsd:enumeration value="pci_x_8"/>
			<xsd:enumeration value="pci_x_hot_plug"/>
			<xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
			<xsd:enumeration value="pcmcia"/>
			<xsd:enumeration value="pcmcia_ii"/>
			<xsd:enumeration value="pcmcia_iii"/>
			<xsd:enumeration value="pictbridge"/>
			<xsd:enumeration value="ps/2"/>
			<xsd:enumeration value="radio_frequency"/>
			<xsd:enumeration value="rs_mmc"/>
			<xsd:enumeration value="s_video"/>
			<xsd:enumeration value="sas"/>
			<xsd:enumeration value="sata_1_5_gb"/>
			<xsd:enumeration value="sata_3_0_gb"/>
			<xsd:enumeration value="sata_6_0_gb"/>
			<xsd:enumeration value="sbus"/>
			<xsd:enumeration value="scsi"/>
			<xsd:enumeration value="sdhc"/>
			<xsd:enumeration value="sdio"/>
			<xsd:enumeration value="sdxc"/>
			<xsd:enumeration value="secure_digital"/>
			<xsd:enumeration value="secure_mmc"/>
			<xsd:enumeration value="serial_interface"/>
			<xsd:enumeration value="sim_card"/>
			<xsd:enumeration value="smartmedia_card"/>
			<xsd:enumeration value="solid_state_drive"/>
			<xsd:enumeration value="spd"/>
			<xsd:enumeration value="springboard_module"/>
			<xsd:enumeration value="ssfdc"/>
			<xsd:enumeration value="superdisk"/>
			<xsd:enumeration value="transflash"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="usb"/>
			<xsd:enumeration value="usb1.0"/>
			<xsd:enumeration value="usb1.1"/>
			<xsd:enumeration value="usb3.0"/>
			<xsd:enumeration value="usb_docking_station"/>
			<xsd:enumeration value="usb_streaming"/>
			<xsd:enumeration value="vga"/>
			<xsd:enumeration value="xd_picture_card"/>
			<xsd:enumeration value="xd_picture_card_h"/>
			<xsd:enumeration value="xd_picture_card_m"/>
			<xsd:enumeration value="xd_picture_card_m_plus"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GraphicsRAMTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="72_pin_edo_simm"/>
			<xsd:enumeration value="ddr2_sdram"/>
			<xsd:enumeration value="ddr3_sdram"/>
			<xsd:enumeration value="ddr4_sdram"/>
			<xsd:enumeration value="ddr5_sdram"/>
			<xsd:enumeration value="ddr_dram"/>
			<xsd:enumeration value="ddr_sdram"/>
			<xsd:enumeration value="dimm"/>
			<xsd:enumeration value="dram"/>
			<xsd:enumeration value="edo_dram"/>
			<xsd:enumeration value="eeprom"/>
			<xsd:enumeration value="eprom"/>
			<xsd:enumeration value="fpm_dram"/>
			<xsd:enumeration value="fpm_ram"/>
			<xsd:enumeration value="gddr3"/>
			<xsd:enumeration value="gddr4"/>
			<xsd:enumeration value="gddr5"/>
			<xsd:enumeration value="l2_cache"/>
			<xsd:enumeration value="micro_dimm"/>
			<xsd:enumeration value="pc2_4200"/>
			<xsd:enumeration value="pc2_4300"/>
			<xsd:enumeration value="pc2_5300"/>
			<xsd:enumeration value="pc2_5400"/>
			<xsd:enumeration value="pc2_6000"/>
			<xsd:enumeration value="pc_100_sdram"/>
			<xsd:enumeration value="pc_1066"/>
			<xsd:enumeration value="pc_133_sdram"/>
			<xsd:enumeration value="pc_1600"/>
			<xsd:enumeration value="pc_2100_ddr"/>
			<xsd:enumeration value="pc_2700_ddr"/>
			<xsd:enumeration value="pc_3000"/>
			<xsd:enumeration value="pc_3200_ddr"/>
			<xsd:enumeration value="pc_3500_ddr"/>
			<xsd:enumeration value="pc_3700"/>
			<xsd:enumeration value="pc_4000_ddr"/>
			<xsd:enumeration value="pc_4200"/>
			<xsd:enumeration value="pc_4300"/>
			<xsd:enumeration value="pc_4400"/>
			<xsd:enumeration value="pc_66_sdram"/>
			<xsd:enumeration value="pc_800"/>
			<xsd:enumeration value="rambus"/>
			<xsd:enumeration value="rdram"/>
			<xsd:enumeration value="rimm"/>
			<xsd:enumeration value="sdram"/>
			<xsd:enumeration value="sgram"/>
			<xsd:enumeration value="shared"/>
			<xsd:enumeration value="simm"/>
			<xsd:enumeration value="sipp"/>
			<xsd:enumeration value="sldram"/>
			<xsd:enumeration value="sodimm"/>
			<xsd:enumeration value="sorimm"/>
			<xsd:enumeration value="sram"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="vram"/>
			<xsd:enumeration value="wram"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HardDriveInterfaceTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ata"/>
			<xsd:enumeration value="ata100"/>
			<xsd:enumeration value="ata133"/>
			<xsd:enumeration value="ata_2"/>
			<xsd:enumeration value="ata_3"/>
			<xsd:enumeration value="ata_4"/>
			<xsd:enumeration value="ata_5"/>
			<xsd:enumeration value="atapi"/>
			<xsd:enumeration value="dma"/>
			<xsd:enumeration value="eide"/>
			<xsd:enumeration value="eio"/>
			<xsd:enumeration value="esata"/>
			<xsd:enumeration value="esdi"/>
			<xsd:enumeration value="ethernet"/>
			<xsd:enumeration value="ethernet_100base_t"/>
			<xsd:enumeration value="ethernet_100base_tx"/>
			<xsd:enumeration value="ethernet_10_100_1000"/>
			<xsd:enumeration value="ethernet_10base_t"/>
			<xsd:enumeration value="fast_scsi"/>
			<xsd:enumeration value="fast_wide_scsi"/>
			<xsd:enumeration value="fata"/>
			<xsd:enumeration value="fc_al"/>
			<xsd:enumeration value="fc_al_2"/>
			<xsd:enumeration value="fdd"/>
			<xsd:enumeration value="fibre_channel"/>
			<xsd:enumeration value="firewire"/>
			<xsd:enumeration value="ide"/>
			<xsd:enumeration value="ieee_1284"/>
			<xsd:enumeration value="ieee_1394b"/>
			<xsd:enumeration value="iscsi"/>
			<xsd:enumeration value="lvds"/>
			<xsd:enumeration value="pc_card"/>
			<xsd:enumeration value="pci_express_x16"/>
			<xsd:enumeration value="pci_express_x4"/>
			<xsd:enumeration value="pci_express_x8"/>
			<xsd:enumeration value="raid"/>
			<xsd:enumeration value="scsi"/>
			<xsd:enumeration value="serial_ata"/>
			<xsd:enumeration value="serial_ata150"/>
			<xsd:enumeration value="serial_ata300"/>
			<xsd:enumeration value="serial_ata600"/>
			<xsd:enumeration value="serial_scsi"/>
			<xsd:enumeration value="solid_state"/>
			<xsd:enumeration value="ssa"/>
			<xsd:enumeration value="st412"/>
			<xsd:enumeration value="ultra2_scsi"/>
			<xsd:enumeration value="ultra2_wide_scsi"/>
			<xsd:enumeration value="ultra3_scsi"/>
			<xsd:enumeration value="ultra_160_scsi"/>
			<xsd:enumeration value="ultra_320_scsi"/>
			<xsd:enumeration value="ultra_ata"/>
			<xsd:enumeration value="ultra_scsi"/>
			<xsd:enumeration value="ultra_wide_scsi"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="usb"/>
			<xsd:enumeration value="usb_1.1"/>
			<xsd:enumeration value="usb_2.0"/>
			<xsd:enumeration value="usb_2.0_3.0"/>
			<xsd:enumeration value="usb_3.0"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SupportedImageTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="3f_tiff"/>
			<xsd:enumeration value="bmp"/>
			<xsd:enumeration value="canon_raw"/>
			<xsd:enumeration value="eps"/>
			<xsd:enumeration value="exif"/>
			<xsd:enumeration value="flashpix_fpx"/>
			<xsd:enumeration value="gif"/>
			<xsd:enumeration value="jpeg"/>
			<xsd:enumeration value="pcx"/>
			<xsd:enumeration value="pgpf"/>
			<xsd:enumeration value="pict"/>
			<xsd:enumeration value="png"/>
			<xsd:enumeration value="psd"/>
			<xsd:enumeration value="raw"/>
			<xsd:enumeration value="tga"/>
			<xsd:enumeration value="tiff"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="wif"/>
			<xsd:enumeration value="wmf"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HardwareInterfaceValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="1_4_inch_audio"/>
			<xsd:enumeration value="2_5_mm_audio"/>
			<xsd:enumeration value="3.0_v_ttl"/>
			<xsd:enumeration value="3_5_floppy"/>
			<xsd:enumeration value="3_5_mm_audio"/>
			<xsd:enumeration value="ata"/>
			<xsd:enumeration value="ata_flash_card"/>
			<xsd:enumeration value="audio_video_port"/>
			<xsd:enumeration value="bluetooth"/>
			<xsd:enumeration value="built_in_flash_memory"/>
			<xsd:enumeration value="cd-r"/>
			<xsd:enumeration value="cd_rw"/>
			<xsd:enumeration value="cdr_drive"/>
			<xsd:enumeration value="compact_disc"/>
			<xsd:enumeration value="compact_flash_card"/>
			<xsd:enumeration value="compact_flash_type_i_or_ii"/>
			<xsd:enumeration value="compactflash_type_i"/>
			<xsd:enumeration value="compactflash_type_ii"/>
			<xsd:enumeration value="component_video"/>
			<xsd:enumeration value="composite_video"/>
			<xsd:enumeration value="d_sub"/>
			<xsd:enumeration value="dmi"/>
			<xsd:enumeration value="dssi"/>
			<xsd:enumeration value="dvd_r"/>
			<xsd:enumeration value="dvd_rw"/>
			<xsd:enumeration value="dvi_x_1"/>
			<xsd:enumeration value="dvi_x_2"/>
			<xsd:enumeration value="dvi_x_4"/>
			<xsd:enumeration value="eide"/>
			<xsd:enumeration value="eisa"/>
			<xsd:enumeration value="ethernet"/>
			<xsd:enumeration value="express_card"/>
			<xsd:enumeration value="fibre_channel"/>
			<xsd:enumeration value="firewire_1600"/>
			<xsd:enumeration value="firewire_3200"/>
			<xsd:enumeration value="firewire_400"/>
			<xsd:enumeration value="firewire_800"/>
			<xsd:enumeration value="firewire_esata"/>
			<xsd:enumeration value="game_port"/>
			<xsd:enumeration value="gbic"/>
			<xsd:enumeration value="hdmi"/>
			<xsd:enumeration value="headphone"/>
			<xsd:enumeration value="hp_hsc"/>
			<xsd:enumeration value="hp_pb"/>
			<xsd:enumeration value="hs_mmc"/>
			<xsd:enumeration value="ibm_microdrive"/>
			<xsd:enumeration value="ide"/>
			<xsd:enumeration value="ieee_1284"/>
			<xsd:enumeration value="infrared"/>
			<xsd:enumeration value="internal_w_removable_media"/>
			<xsd:enumeration value="iomega_clik_disk"/>
			<xsd:enumeration value="isa"/>
			<xsd:enumeration value="isp"/>
			<xsd:enumeration value="lanc"/>
			<xsd:enumeration value="mca"/>
			<xsd:enumeration value="media_card"/>
			<xsd:enumeration value="memory_stick"/>
			<xsd:enumeration value="memory_stick_duo"/>
			<xsd:enumeration value="memory_stick_micro"/>
			<xsd:enumeration value="memory_stick_pro"/>
			<xsd:enumeration value="memory_stick_pro_duo"/>
			<xsd:enumeration value="memory_stick_pro_hg_duo"/>
			<xsd:enumeration value="memory_stick_select"/>
			<xsd:enumeration value="memory_stick_xc"/>
			<xsd:enumeration value="memory_stick_xc_hg_micro"/>
			<xsd:enumeration value="memory_stick_xc_micro"/>
			<xsd:enumeration value="micard"/>
			<xsd:enumeration value="micro_sdhc"/>
			<xsd:enumeration value="micro_sdxc"/>
			<xsd:enumeration value="microsd"/>
			<xsd:enumeration value="mini_dvd"/>
			<xsd:enumeration value="mini_hdmi"/>
			<xsd:enumeration value="mini_pci"/>
			<xsd:enumeration value="mini_sdhc"/>
			<xsd:enumeration value="mini_sdxc"/>
			<xsd:enumeration value="minisd"/>
			<xsd:enumeration value="mmc_micro"/>
			<xsd:enumeration value="multimedia_card"/>
			<xsd:enumeration value="multimedia_card_mobile"/>
			<xsd:enumeration value="multimedia_card_plus"/>
			<xsd:enumeration value="multipronged_audio"/>
			<xsd:enumeration value="nubus"/>
			<xsd:enumeration value="parallel_interface"/>
			<xsd:enumeration value="pc_card"/>
			<xsd:enumeration value="pci"/>
			<xsd:enumeration value="pci_64"/>
			<xsd:enumeration value="pci_64_hot_plug"/>
			<xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
			<xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
			<xsd:enumeration value="pci_express_x4"/>
			<xsd:enumeration value="pci_express_x8"/>
			<xsd:enumeration value="pci_hot_plug"/>
			<xsd:enumeration value="pci_raid"/>
			<xsd:enumeration value="pci_x"/>
			<xsd:enumeration value="pci_x_1"/>
			<xsd:enumeration value="pci_x_100_mhz"/>
			<xsd:enumeration value="pci_x_16"/>
			<xsd:enumeration value="pci_x_16_gb"/>
			<xsd:enumeration value="pci_x_4"/>
			<xsd:enumeration value="pci_x_66_mhz"/>
			<xsd:enumeration value="pci_x_8"/>
			<xsd:enumeration value="pci_x_hot_plug"/>
			<xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
			<xsd:enumeration value="pcmcia"/>
			<xsd:enumeration value="pcmcia_ii"/>
			<xsd:enumeration value="pcmcia_iii"/>
			<xsd:enumeration value="pictbridge"/>
			<xsd:enumeration value="ps/2"/>
			<xsd:enumeration value="radio_frequency"/>
			<xsd:enumeration value="rs_mmc"/>
			<xsd:enumeration value="s_video"/>
			<xsd:enumeration value="sas"/>
			<xsd:enumeration value="sata_1_5_gb"/>
			<xsd:enumeration value="sata_3_0_gb"/>
			<xsd:enumeration value="sata_6_0_gb"/>
			<xsd:enumeration value="sbus"/>
			<xsd:enumeration value="scsi"/>
			<xsd:enumeration value="sdhc"/>
			<xsd:enumeration value="sdio"/>
			<xsd:enumeration value="sdxc"/>
			<xsd:enumeration value="secure_digital"/>
			<xsd:enumeration value="secure_mmc"/>
			<xsd:enumeration value="serial_interface"/>
			<xsd:enumeration value="sim_card"/>
			<xsd:enumeration value="smartmedia_card"/>
			<xsd:enumeration value="solid_state_drive"/>
			<xsd:enumeration value="spd"/>
			<xsd:enumeration value="springboard_module"/>
			<xsd:enumeration value="ssfdc"/>
			<xsd:enumeration value="superdisk"/>
			<xsd:enumeration value="transflash"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="usb"/>
			<xsd:enumeration value="usb1.0"/>
			<xsd:enumeration value="usb1.1"/>
			<xsd:enumeration value="usb2.0"/>
			<xsd:enumeration value="usb3.0"/>
			<xsd:enumeration value="usb_docking_station"/>
			<xsd:enumeration value="usb_streaming"/>
			<xsd:enumeration value="vga"/>
			<xsd:enumeration value="xd_picture_card"/>
			<xsd:enumeration value="xd_picture_card_h"/>
			<xsd:enumeration value="xd_picture_card_m"/>
			<xsd:enumeration value="xd_picture_card_m_plus"/>
			<xsd:enumeration value="ieee_1394"/>
			<xsd:enumeration value="pci_x_133_mhz"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VideotapeRecordingSpeedType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="sp"/>
			<xsd:enumeration value="ep"/>
			<xsd:enumeration value="slp"/>
			<xsd:enumeration value="lp"/>
			<xsd:enumeration value="Unknown"/>
			<xsd:enumeration value="SP"/>
			<xsd:enumeration value="EP"/>
			<xsd:enumeration value="SLP"/>
			<xsd:enumeration value="LP"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ThreeDTechnologyValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="active"/>
			<xsd:enumeration value="anaglyphic"/>
			<xsd:enumeration value="auto_stereoscopic"/>
			<xsd:enumeration value="passive"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="WirelessTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="2.4_ghz_radio_frequency"/>
			<xsd:enumeration value="5.8_ghz_radio_frequency"/>
			<xsd:enumeration value="802_11_A"/>
			<xsd:enumeration value="802_11_AB"/>
			<xsd:enumeration value="802_11_ABG"/>
			<xsd:enumeration value="802_11_AG"/>
			<xsd:enumeration value="802_11_B"/>
			<xsd:enumeration value="802_11_BGN"/>
			<xsd:enumeration value="802_11_G"/>
			<xsd:enumeration value="802_11_G_108Mbps"/>
			<xsd:enumeration value="802_11_N"/>
			<xsd:enumeration value="900_mhz_radio_frequency"/>
			<xsd:enumeration value="bluetooth"/>
			<xsd:enumeration value="dect"/>
			<xsd:enumeration value="dect_6.0"/>
			<xsd:enumeration value="infrared"/>
			<xsd:enumeration value="irda"/>
			<xsd:enumeration value="radio_frequency"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ProcessorTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="5x86"/>
			<xsd:enumeration value="68000"/>
			<xsd:enumeration value="68030"/>
			<xsd:enumeration value="68040"/>
			<xsd:enumeration value="68328"/>
			<xsd:enumeration value="68882"/>
			<xsd:enumeration value="68ez328"/>
			<xsd:enumeration value="68lc040"/>
			<xsd:enumeration value="6x86"/>
			<xsd:enumeration value="6x86mx"/>
			<xsd:enumeration value="8031"/>
			<xsd:enumeration value="8032"/>
			<xsd:enumeration value="80386"/>
			<xsd:enumeration value="80486"/>
			<xsd:enumeration value="80486dx2"/>
			<xsd:enumeration value="80486slc"/>
			<xsd:enumeration value="80486sx"/>
			<xsd:enumeration value="80c186"/>
			<xsd:enumeration value="80c31"/>
			<xsd:enumeration value="80c32"/>
			<xsd:enumeration value="80c88"/>
			<xsd:enumeration value="alpha_21064a"/>
			<xsd:enumeration value="alpha_21164"/>
			<xsd:enumeration value="alpha_21164a"/>
			<xsd:enumeration value="alpha_21264"/>
			<xsd:enumeration value="alpha_21264a"/>
			<xsd:enumeration value="alpha_21264b"/>
			<xsd:enumeration value="alpha_21264c"/>
			<xsd:enumeration value="alpha_21264d"/>
			<xsd:enumeration value="alpha_21364"/>
			<xsd:enumeration value="alpha_ev7"/>
			<xsd:enumeration value="amd_a_series"/>
			<xsd:enumeration value="amd_c_series"/>
			<xsd:enumeration value="amd_e_series"/>
			<xsd:enumeration value="amd_v_series"/>
			<xsd:enumeration value="arm610"/>
			<xsd:enumeration value="arm710"/>
			<xsd:enumeration value="arm_7100"/>
			<xsd:enumeration value="arm710a"/>
			<xsd:enumeration value="arm710t"/>
			<xsd:enumeration value="arm7500fe"/>
			<xsd:enumeration value="athlon"/>
			<xsd:enumeration value="Athlon_2650e"/>
			<xsd:enumeration value="Athlon_2850e"/>
			<xsd:enumeration value="athlon_4"/>
			<xsd:enumeration value="athlon_64"/>
			<xsd:enumeration value="Athlon_64_2650"/>
			<xsd:enumeration value="Athlon_64_3500"/>
			<xsd:enumeration value="Athlon_64_4200_plus"/>
			<xsd:enumeration value="Athlon_64_4800_plus"/>
			<xsd:enumeration value="athlon_64_for_dtr"/>
			<xsd:enumeration value="athlon_64_fx"/>
			<xsd:enumeration value="Athlon_64_Single_Core_TF_20"/>
			<xsd:enumeration value="Athlon_64_TF_36"/>
			<xsd:enumeration value="athlon_64_x2"/>
			<xsd:enumeration value="Athlon_64_X2_3600_plus"/>
			<xsd:enumeration value="Athlon_64_X2_4000_plus"/>
			<xsd:enumeration value="Athlon_64_X2_4200_plus"/>
			<xsd:enumeration value="Athlon_64_X2_4450B"/>
			<xsd:enumeration value="Athlon_64_X2_4800"/>
			<xsd:enumeration value="Athlon_64_X2_5000_plus"/>
			<xsd:enumeration value="Athlon_64_X2_5050"/>
			<xsd:enumeration value="Athlon_64_X2_5200_plus"/>
			<xsd:enumeration value="Athlon_64_X2_5400_plus"/>
			<xsd:enumeration value="Athlon_64_X2_5600_plus"/>
			<xsd:enumeration value="Athlon_64_X2_6000_plus"/>
			<xsd:enumeration value="Athlon_64_X2_6400_plus"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_3800_plus"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_4400"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_4450"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_L310"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_TK_42"/>
			<xsd:enumeration value="Athlon_64_X2_QL_64_Dual_Core"/>
			<xsd:enumeration value="Athlon_Dual_Core_QL62A"/>
			<xsd:enumeration value="Athlon_II"/>
			<xsd:enumeration value="Athlon_II_170u"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_240e"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_245"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_260"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_260u"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_M320"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_P320"/>
			<xsd:enumeration value="Athlon_II_Dual_Core_P360"/>
			<xsd:enumeration value="Athlon_II_Neo_Single_Core_K125"/>
			<xsd:enumeration value="Athlon_II_Neo_X2_Dual_Core_K325"/>
			<xsd:enumeration value="Athlon_II_Neo_X2_Dual_Core_K625"/>
			<xsd:enumeration value="Athlon_II_Single_Core_160u"/>
			<xsd:enumeration value="Athlon_II_X2_B24"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_170u"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_215"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_235e"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_240"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_240e"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_245"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_250"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_250U"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_255"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_3250e"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_M300"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_M320"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_M340"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_M520"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_P320"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_P340"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_P360"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_60"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_62"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_TK_53"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_TK_57"/>
			<xsd:enumeration value="athlon_ii_x3"/>
			<xsd:enumeration value="Athlon_II_X3_Triple_Core_400E"/>
			<xsd:enumeration value="Athlon_II_X3_Triple_Core_425"/>
			<xsd:enumeration value="Athlon_II_X3_Triple_Core_435"/>
			<xsd:enumeration value="athlon_ii_x4"/>
			<xsd:enumeration value="Athlon_II_X4_Dual_Core_240e"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_600E"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_605e"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_615e"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_620"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_630"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_635"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_640"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_645"/>
			<xsd:enumeration value="Athlon_LE_1640"/>
			<xsd:enumeration value="athlon_mp"/>
			<xsd:enumeration value="Athlon_Neo_Single_Core_MV_40"/>
			<xsd:enumeration value="Athlon_Neo_X2_Dual_Core_L325"/>
			<xsd:enumeration value="Athlon_Neo_X2_Dual_Core_L335"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_4200_plus"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_5000"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_5000_plus"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_5400_plus"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_7550"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_7750"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_QL_66"/>
			<xsd:enumeration value="athlon_xp"/>
			<xsd:enumeration value="athlon_xp_m"/>
			<xsd:enumeration value="Atom_D410"/>
			<xsd:enumeration value="Atom_D425"/>
			<xsd:enumeration value="Atom_D510"/>
			<xsd:enumeration value="Atom_D525"/>
			<xsd:enumeration value="Atom_N230"/>
			<xsd:enumeration value="Atom_N270"/>
			<xsd:enumeration value="Atom_N280"/>
			<xsd:enumeration value="Atom_N330"/>
			<xsd:enumeration value="Atom_N470"/>
			<xsd:enumeration value="Atom_N475"/>
			<xsd:enumeration value="Atom_N550"/>
			<xsd:enumeration value="Atom_Silverthorne"/>
			<xsd:enumeration value="Atom_Z330"/>
			<xsd:enumeration value="Atom_Z515"/>
			<xsd:enumeration value="bulverde"/>
			<xsd:enumeration value="c167cr"/>
			<xsd:enumeration value="celeron"/>
			<xsd:enumeration value="Celeron_450"/>
			<xsd:enumeration value="Celeron_585"/>
			<xsd:enumeration value="Celeron_743"/>
			<xsd:enumeration value="Celeron_900"/>
			<xsd:enumeration value="Celeron_925"/>
			<xsd:enumeration value="Celeron_D_Processor_360"/>
			<xsd:enumeration value="Celeron_D_Processor_420"/>
			<xsd:enumeration value="Celeron_D_Processor_440"/>
			<xsd:enumeration value="Celeron_E1200"/>
			<xsd:enumeration value="Celeron_E1500"/>
			<xsd:enumeration value="Celeron_E3200"/>
			<xsd:enumeration value="Celeron_E3300"/>
			<xsd:enumeration value="Celeron_M_353"/>
			<xsd:enumeration value="Celeron_M_440"/>
			<xsd:enumeration value="Celeron_M_520"/>
			<xsd:enumeration value="Celeron_M_530"/>
			<xsd:enumeration value="Celeron_M_540"/>
			<xsd:enumeration value="Celeron_M_550"/>
			<xsd:enumeration value="Celeron_M_560"/>
			<xsd:enumeration value="Celeron_M_575"/>
			<xsd:enumeration value="Celeron_M_585"/>
			<xsd:enumeration value="Celeron_M_T1400"/>
			<xsd:enumeration value="Celeron_SU2300"/>
			<xsd:enumeration value="Celeron_T1500"/>
			<xsd:enumeration value="Celeron_T3000"/>
			<xsd:enumeration value="Celeron_T4500"/>
			<xsd:enumeration value="Core_2_Duo"/>
			<xsd:enumeration value="Core_2_Duo_E2200"/>
			<xsd:enumeration value="Core_2_Duo_E4000"/>
			<xsd:enumeration value="Core_2_Duo_E4300"/>
			<xsd:enumeration value="Core_2_Duo_E4400"/>
			<xsd:enumeration value="Core_2_Duo_E4500"/>
			<xsd:enumeration value="Core_2_Duo_E4600"/>
			<xsd:enumeration value="Core_2_Duo_E5500"/>
			<xsd:enumeration value="Core_2_Duo_E6300"/>
			<xsd:enumeration value="Core_2_Duo_E6400"/>
			<xsd:enumeration value="Core_2_Duo_E6420"/>
			<xsd:enumeration value="Core_2_Duo_E6600"/>
			<xsd:enumeration value="Core_2_Duo_E7200"/>
			<xsd:enumeration value="Core_2_Duo_E7300"/>
			<xsd:enumeration value="Core_2_Duo_E7400"/>
			<xsd:enumeration value="Core_2_Duo_E7500"/>
			<xsd:enumeration value="Core_2_Duo_E7600"/>
			<xsd:enumeration value="Core_2_Duo_E8400"/>
			<xsd:enumeration value="Core_2_Duo_E8500"/>
			<xsd:enumeration value="Core_2_Duo_L7500"/>
			<xsd:enumeration value="Core_2_Duo_P3750"/>
			<xsd:enumeration value="Core_2_Duo_P7350"/>
			<xsd:enumeration value="Core_2_Duo_P7370"/>
			<xsd:enumeration value="Core_2_Duo_P7450"/>
			<xsd:enumeration value="Core_2_Duo_P7550"/>
			<xsd:enumeration value="Core_2_Duo_P8400"/>
			<xsd:enumeration value="Core_2_Duo_P8600"/>
			<xsd:enumeration value="Core_2_Duo_P8700"/>
			<xsd:enumeration value="Core_2_Duo_P8800"/>
			<xsd:enumeration value="Core_2_Duo_P9500"/>
			<xsd:enumeration value="Core_2_Duo_P9600"/>
			<xsd:enumeration value="Core_2_Duo_SL7100"/>
			<xsd:enumeration value="Core_2_Duo_SL9300"/>
			<xsd:enumeration value="Core_2_Duo_SL9400"/>
			<xsd:enumeration value="Core_2_Duo_SL9600"/>
			<xsd:enumeration value="Core_2_Duo_SP9400"/>
			<xsd:enumeration value="Core_2_Duo_SU4100"/>
			<xsd:enumeration value="Core_2_Duo_SU7300"/>
			<xsd:enumeration value="Core_2_Duo_SU9300"/>
			<xsd:enumeration value="Core_2_Duo_SU9400"/>
			<xsd:enumeration value="Core_2_Duo_SU_9600"/>
			<xsd:enumeration value="Core_2_Duo_T2310"/>
			<xsd:enumeration value="Core_2_Duo_T2330"/>
			<xsd:enumeration value="Core_2_Duo_T2390"/>
			<xsd:enumeration value="Core_2_Duo_T2450"/>
			<xsd:enumeration value="Core_2_Duo_T4200"/>
			<xsd:enumeration value="Core_2_Duo_T5200"/>
			<xsd:enumeration value="Core_2_Duo_T5250"/>
			<xsd:enumeration value="Core_2_Duo_T5270"/>
			<xsd:enumeration value="Core_2_Duo_T5300"/>
			<xsd:enumeration value="Core_2_Duo_T5450"/>
			<xsd:enumeration value="Core_2_Duo_T5470"/>
			<xsd:enumeration value="Core_2_Duo_T5500"/>
			<xsd:enumeration value="Core_2_Duo_T5550"/>
			<xsd:enumeration value="Core_2_Duo_T5600"/>
			<xsd:enumeration value="Core_2_Duo_T5670"/>
			<xsd:enumeration value="Core_2_Duo_T5750"/>
			<xsd:enumeration value="Core_2_Duo_T5800"/>
			<xsd:enumeration value="Core_2_Duo_T5850"/>
			<xsd:enumeration value="Core_2_Duo_T5870"/>
			<xsd:enumeration value="Core_2_Duo_T6400"/>
			<xsd:enumeration value="Core_2_Duo__T6500"/>
			<xsd:enumeration value="Core_2_Duo_T6570"/>
			<xsd:enumeration value="Core_2_Duo_T6600"/>
			<xsd:enumeration value="Core_2_Duo_T6670"/>
			<xsd:enumeration value="Core_2_Duo_T7100"/>
			<xsd:enumeration value="Core_2_Duo_T7200"/>
			<xsd:enumeration value="Core_2_Duo_T7250"/>
			<xsd:enumeration value="Core_2_Duo_T7270"/>
			<xsd:enumeration value="Core_2_Duo_T7300"/>
			<xsd:enumeration value="Core_2_Duo_T7350"/>
			<xsd:enumeration value="Core_2_Duo_T7400"/>
			<xsd:enumeration value="Core_2_Duo_T7500"/>
			<xsd:enumeration value="Core_2_Duo_T7700"/>
			<xsd:enumeration value="Core_2_Duo_T8100"/>
			<xsd:enumeration value="Core_2_Duo_T8300"/>
			<xsd:enumeration value="Core_2_Duo_T8400"/>
			<xsd:enumeration value="Core_2_Duo_T8700"/>
			<xsd:enumeration value="Core_2_Duo_T9300"/>
			<xsd:enumeration value="Core_2_Duo_T9400"/>
			<xsd:enumeration value="Core_2_Duo_T9500"/>
			<xsd:enumeration value="Core_2_Duo_T9550"/>
			<xsd:enumeration value="Core_2_Duo_T9600"/>
			<xsd:enumeration value="Core_2_Duo_T9900"/>
			<xsd:enumeration value="Core_2_Duo_U1400"/>
			<xsd:enumeration value="Core_2_Duo_U2200"/>
			<xsd:enumeration value="Core_2_Duo_U7500"/>
			<xsd:enumeration value="Core_2_Duo_U7700"/>
			<xsd:enumeration value="Core_2_Quad_9600"/>
			<xsd:enumeration value="Core_2_Quad_Q6600"/>
			<xsd:enumeration value="Core_2_Quad_Q6700"/>
			<xsd:enumeration value="Core_2_Quad_Q8200"/>
			<xsd:enumeration value="Core_2_Quad_Q8200S"/>
			<xsd:enumeration value="Core_2_Quad_Q8300"/>
			<xsd:enumeration value="Core_2_Quad_Q8400"/>
			<xsd:enumeration value="Core_2_Quad_Q8400S"/>
			<xsd:enumeration value="Core_2_Quad_Q9000"/>
			<xsd:enumeration value="Core_2_Quad_Q9300"/>
			<xsd:enumeration value="Core_2_Quad_Q9400"/>
			<xsd:enumeration value="Core_2_Quad_Q9400S"/>
			<xsd:enumeration value="Core_2_Quad_Q9450"/>
			<xsd:enumeration value="Core_2_Quad_Q9500"/>
			<xsd:enumeration value="Core_2_Quad_Q9550"/>
			<xsd:enumeration value="Core_2_Solo_SU3500"/>
			<xsd:enumeration value="Core_Duo_1V_L2400"/>
			<xsd:enumeration value="Core_Duo_LV_L2400"/>
			<xsd:enumeration value="Core_Duo_T2250"/>
			<xsd:enumeration value="Core_Duo_T2400"/>
			<xsd:enumeration value="Core_Duo_U2400"/>
			<xsd:enumeration value="core_i3"/>
			<xsd:enumeration value="Core_i3_2330M"/>
			<xsd:enumeration value="Core_i3_330UM"/>
			<xsd:enumeration value="Core_i3_350M"/>
			<xsd:enumeration value="Core_i3_370M"/>
			<xsd:enumeration value="Core_i3_380M"/>
			<xsd:enumeration value="Core_i3_380UM"/>
			<xsd:enumeration value="Core_i3_520M"/>
			<xsd:enumeration value="Core_i3_530"/>
			<xsd:enumeration value="Core_i3_530M"/>
			<xsd:enumeration value="Core_i3_540"/>
			<xsd:enumeration value="Core_i3_540M"/>
			<xsd:enumeration value="Core_i3_550"/>
			<xsd:enumeration value="core_i5"/>
			<xsd:enumeration value="Core_i5_2300"/>
			<xsd:enumeration value="Core_i5_2520M"/>
			<xsd:enumeration value="Core_i5_2540M"/>
			<xsd:enumeration value="Core_i5_430M"/>
			<xsd:enumeration value="Core_i5_430UM"/>
			<xsd:enumeration value="Core_i5_450M"/>
			<xsd:enumeration value="Core_i5_460M"/>
			<xsd:enumeration value="Core_i5_470UM"/>
			<xsd:enumeration value="Core_i5_480M"/>
			<xsd:enumeration value="Core_i5_560M"/>
			<xsd:enumeration value="Core_i5_650"/>
			<xsd:enumeration value="Core_i5_655K"/>
			<xsd:enumeration value="Core_i5_660"/>
			<xsd:enumeration value="Core_i5_750"/>
			<xsd:enumeration value="Core_i5_760"/>
			<xsd:enumeration value="Core_i5__760"/>
			<xsd:enumeration value="core_i7"/>
			<xsd:enumeration value="Core_i7_2600"/>
			<xsd:enumeration value="Core_i7_2620QM"/>
			<xsd:enumeration value="Core_i7_2630QM"/>
			<xsd:enumeration value="Core_i7_2720QM"/>
			<xsd:enumeration value="Core_i7_2820QM"/>
			<xsd:enumeration value="Core_i7_4800MQ"/>
			<xsd:enumeration value="Core_i7_620LM"/>
			<xsd:enumeration value="Core_i7_620M"/>
			<xsd:enumeration value="Core_i7_640LM"/>
			<xsd:enumeration value="Core_i7_640M"/>
			<xsd:enumeration value="Core_i7_640UM"/>
			<xsd:enumeration value="Core_i7_680UM"/>
			<xsd:enumeration value="Core_i7_740QM"/>
			<xsd:enumeration value="Core_i7_860"/>
			<xsd:enumeration value="Core_i7_870"/>
			<xsd:enumeration value="Core_i7_875K"/>
			<xsd:enumeration value="Core_i7_920"/>
			<xsd:enumeration value="Core_i7_930"/>
			<xsd:enumeration value="Core_i7_940"/>
			<xsd:enumeration value="Core_i7_950"/>
			<xsd:enumeration value="Core_i7_960"/>
			<xsd:enumeration value="Core_i7_980"/>
			<xsd:enumeration value="Core_Solo_U1500"/>
			<xsd:enumeration value="crusoe_5800"/>
			<xsd:enumeration value="crusoe_tm3200"/>
			<xsd:enumeration value="crusoe_tm5500"/>
			<xsd:enumeration value="crusoe_tm5600"/>
			<xsd:enumeration value="C_Series_C_50"/>
			<xsd:enumeration value="cyrix_iii"/>
			<xsd:enumeration value="cyrix_mii"/>
			<xsd:enumeration value="duron"/>
			<xsd:enumeration value="eden"/>
			<xsd:enumeration value="eden_esp"/>
			<xsd:enumeration value="eden_esp_4000"/>
			<xsd:enumeration value="eden_esp_5000"/>
			<xsd:enumeration value="eden_esp_6000"/>
			<xsd:enumeration value="eden_esp_7000"/>
			<xsd:enumeration value="efficeon"/>
			<xsd:enumeration value="efficeon_tm8000"/>
			<xsd:enumeration value="efficeon_tm8600"/>
			<xsd:enumeration value="efficion_8800"/>
			<xsd:enumeration value="elansc300"/>
			<xsd:enumeration value="elansc310"/>
			<xsd:enumeration value="elansc400"/>
			<xsd:enumeration value="E_Series_Dual_Core_E_350"/>
			<xsd:enumeration value="E_Series_Processor_E_240"/>
			<xsd:enumeration value="extremecpu"/>
			<xsd:enumeration value="fx_series_eight_core_fx_8100"/>
			<xsd:enumeration value="fx_series_eight_core_fx_8120"/>
			<xsd:enumeration value="fx_series_eight_core_fx_8150"/>
			<xsd:enumeration value="fx_series_quad_core_fx_4100"/>
			<xsd:enumeration value="fx_series_quad_core_fx_4170"/>
			<xsd:enumeration value="fx_series_quad_core_fx_b4150"/>
			<xsd:enumeration value="fx_series_six_core_fx_6100"/>
			<xsd:enumeration value="fx_series_six_core_fx_6120"/>
			<xsd:enumeration value="g3"/>
			<xsd:enumeration value="g4"/>
			<xsd:enumeration value="g5"/>
			<xsd:enumeration value="geode_gx"/>
			<xsd:enumeration value="Geode_GX"/>
			<xsd:enumeration value="geode_gx1"/>
			<xsd:enumeration value="geode_gxlv"/>
			<xsd:enumeration value="geode_gxm"/>
			<xsd:enumeration value="geoden_x"/>
			<xsd:enumeration value="h8s"/>
			<xsd:enumeration value="handheld_engine_cxd2230ga"/>
			<xsd:enumeration value="handheld_engine_cxd2230ga_temp"/>
			<xsd:enumeration value="hitachi_sh3"/>
			<xsd:enumeration value="hypersparc"/>
			<xsd:enumeration value="intel_atom"/>
			<xsd:enumeration value="intel_atom_230"/>
			<xsd:enumeration value="intel_atom_330"/>
			<xsd:enumeration value="intel_atom_n270"/>
			<xsd:enumeration value="intel_atom_n280"/>
			<xsd:enumeration value="intel_atom_n450"/>
			<xsd:enumeration value="intel_atom_n455"/>
			<xsd:enumeration value="intel_atom_z520"/>
			<xsd:enumeration value="intel_atom_z530"/>
			<xsd:enumeration value="intel_celeron_d"/>
			<xsd:enumeration value="intel_centrino"/>
			<xsd:enumeration value="intel_centrino_2"/>
			<xsd:enumeration value="intel_core_2_duo"/>
			<xsd:enumeration value="intel_core_2_duo_mobile"/>
			<xsd:enumeration value="intel_core_2_extreme"/>
			<xsd:enumeration value="intel_core_2_quad"/>
			<xsd:enumeration value="intel_core_2_solo"/>
			<xsd:enumeration value="intel_core_duo"/>
			<xsd:enumeration value="intel_core_solo"/>
			<xsd:enumeration value="Intel_Mobile_CPU"/>
			<xsd:enumeration value="intel_pentium_4_ht"/>
			<xsd:enumeration value="intel_pentium_d"/>
			<xsd:enumeration value="intel_strongarm"/>
			<xsd:enumeration value="intel_xeon"/>
			<xsd:enumeration value="intel_xeon_mp"/>
			<xsd:enumeration value="intel_xscale_pxa250"/>
			<xsd:enumeration value="intel_xscale_pxa255"/>
			<xsd:enumeration value="intel_xscale_pxa263"/>
			<xsd:enumeration value="itanium"/>
			<xsd:enumeration value="itanium_2"/>
			<xsd:enumeration value="k5"/>
			<xsd:enumeration value="k6_2"/>
			<xsd:enumeration value="k6_2e"/>
			<xsd:enumeration value="k6_2_plus"/>
			<xsd:enumeration value="k6_3"/>
			<xsd:enumeration value="k6_iii_plus"/>
			<xsd:enumeration value="k6_mmx"/>
			<xsd:enumeration value="mc68328"/>
			<xsd:enumeration value="mc68ez328"/>
			<xsd:enumeration value="mc68sz328"/>
			<xsd:enumeration value="mc68vz328"/>
			<xsd:enumeration value="mc88110"/>
			<xsd:enumeration value="mediagx"/>
			<xsd:enumeration value="mediagxi"/>
			<xsd:enumeration value="mediagxlv"/>
			<xsd:enumeration value="mediagxm"/>
			<xsd:enumeration value="microsparc_ii"/>
			<xsd:enumeration value="microsparc_iiep"/>
			<xsd:enumeration value="mobile_athlon_4"/>
			<xsd:enumeration value="mobile_athlon_xp_m"/>
			<xsd:enumeration value="mobile_athon_64"/>
			<xsd:enumeration value="mobile_celeron"/>
			<xsd:enumeration value="mobile_duron"/>
			<xsd:enumeration value="mobile_k6_2_plus"/>
			<xsd:enumeration value="mobile_pentium_2"/>
			<xsd:enumeration value="mobile_pentium_3"/>
			<xsd:enumeration value="mobile_pentium_4"/>
			<xsd:enumeration value="mobile_pentium_4_ht"/>
			<xsd:enumeration value="mobile_sempron"/>
			<xsd:enumeration value="motorola_dragonball"/>
			<xsd:enumeration value="nec_mips"/>
			<xsd:enumeration value="none"/>
			<xsd:enumeration value="omap1710"/>
			<xsd:enumeration value="omap310"/>
			<xsd:enumeration value="omap311"/>
			<xsd:enumeration value="omap850"/>
			<xsd:enumeration value="opteron"/>
			<xsd:enumeration value="pa_7100lc"/>
			<xsd:enumeration value="pa_7200"/>
			<xsd:enumeration value="pa_7300lc"/>
			<xsd:enumeration value="pa_8000"/>
			<xsd:enumeration value="pa_8200"/>
			<xsd:enumeration value="pa_8500"/>
			<xsd:enumeration value="pa_8600"/>
			<xsd:enumeration value="pa_8700"/>
			<xsd:enumeration value="pa_8700_plus"/>
			<xsd:enumeration value="pa_8800"/>
			<xsd:enumeration value="pa_8900"/>
			<xsd:enumeration value="pentium"/>
			<xsd:enumeration value="pentium_2"/>
			<xsd:enumeration value="pentium_3"/>
			<xsd:enumeration value="pentium_3_xeon"/>
			<xsd:enumeration value="pentium_4"/>
			<xsd:enumeration value="pentium_4_extreme_edition"/>
			<xsd:enumeration value="Pentium_D_925"/>
			<xsd:enumeration value="Pentium_D_T2060"/>
			<xsd:enumeration value="pentium_dual_core"/>
			<xsd:enumeration value="Pentium_E2140"/>
			<xsd:enumeration value="Pentium_E2160"/>
			<xsd:enumeration value="Pentium_E2180"/>
			<xsd:enumeration value="Pentium_E2200"/>
			<xsd:enumeration value="Pentium_E2220"/>
			<xsd:enumeration value="Pentium_E3200"/>
			<xsd:enumeration value="Pentium_E4400"/>
			<xsd:enumeration value="Pentium_E5200"/>
			<xsd:enumeration value="Pentium_E5300"/>
			<xsd:enumeration value="Pentium_E5301"/>
			<xsd:enumeration value="Pentium_E5400"/>
			<xsd:enumeration value="Pentium_E5500"/>
			<xsd:enumeration value="Pentium_E5700"/>
			<xsd:enumeration value="Pentium_E5800"/>
			<xsd:enumeration value="Pentium_E6300"/>
			<xsd:enumeration value="Pentium_E6600"/>
			<xsd:enumeration value="Pentium_E6700"/>
			<xsd:enumeration value="Pentium_E7200"/>
			<xsd:enumeration value="Pentium_E7400"/>
			<xsd:enumeration value="Pentium_E8400"/>
			<xsd:enumeration value="Pentium_G6950"/>
			<xsd:enumeration value="pentium_iii_e"/>
			<xsd:enumeration value="pentium_iii_s"/>
			<xsd:enumeration value="pentium_ii_xeon"/>
			<xsd:enumeration value="pentium_m"/>
			<xsd:enumeration value="Pentium_M_738"/>
			<xsd:enumeration value="Pentium_M_778"/>
			<xsd:enumeration value="pentium_mmx"/>
			<xsd:enumeration value="Pentium_P6000"/>
			<xsd:enumeration value="Pentium_P6100"/>
			<xsd:enumeration value="Pentium_P6200"/>
			<xsd:enumeration value="pentium_pro"/>
			<xsd:enumeration value="Pentium_SU2700"/>
			<xsd:enumeration value="Pentium_SU4100"/>
			<xsd:enumeration value="Pentium_T2080"/>
			<xsd:enumeration value="Pentium_T2130"/>
			<xsd:enumeration value="Pentium_T2310"/>
			<xsd:enumeration value="Pentium_T2330"/>
			<xsd:enumeration value="Pentium_T2350"/>
			<xsd:enumeration value="Pentium_T2370"/>
			<xsd:enumeration value="Pentium_T2390"/>
			<xsd:enumeration value="Pentium_T3200"/>
			<xsd:enumeration value="Pentium_T3400"/>
			<xsd:enumeration value="Pentium_T4200"/>
			<xsd:enumeration value="Pentium_T4300"/>
			<xsd:enumeration value="Pentium_T4400"/>
			<xsd:enumeration value="Pentium_T4500"/>
			<xsd:enumeration value="Pentium_T6570"/>
			<xsd:enumeration value="Pentium_U5400"/>
			<xsd:enumeration value="Pentium_U5600"/>
			<xsd:enumeration value="pentium_xeon"/>
			<xsd:enumeration value="phenom_dual_core"/>
			<xsd:enumeration value="phenom_ii_x2"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_511"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_550"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_N620"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_N640"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_N650"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_N660"/>
			<xsd:enumeration value="phenom_ii_x3"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_8400"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_8450"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_8550"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_8650"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_B75"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_N830"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_N850"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_P820"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_P840"/>
			<xsd:enumeration value="Phenom_II_X3_Triple_Core_P860"/>
			<xsd:enumeration value="phenom_ii_x4"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_810"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_820"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_830"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_840T"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_910"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9100E"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9150"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_920"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_925"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9350"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_945"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9450"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9500"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_955"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9550"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9600"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_965"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9650"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9750"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9850"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9950"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_N820"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_N930"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_N950"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_P920"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_P940"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_P960"/>
			<xsd:enumeration value="phenom_ii_x6"/>
			<xsd:enumeration value="Phenom_II_X6_Six_Core_1035T"/>
			<xsd:enumeration value="Phenom_II_X6_Six_Core_1045T"/>
			<xsd:enumeration value="Phenom_II_X6_Six_Core_1055T"/>
			<xsd:enumeration value="Phenom_II_X6_Six_Core_1090T"/>
			<xsd:enumeration value="phenom_quad_core"/>
			<xsd:enumeration value="phenom_triple_core"/>
			<xsd:enumeration value="power3"/>
			<xsd:enumeration value="power3_ii"/>
			<xsd:enumeration value="power4"/>
			<xsd:enumeration value="power4_plus"/>
			<xsd:enumeration value="power5"/>
			<xsd:enumeration value="powerpc"/>
			<xsd:enumeration value="powerpc_403"/>
			<xsd:enumeration value="powerpc_403ga"/>
			<xsd:enumeration value="powerpc_403_gcx"/>
			<xsd:enumeration value="powerpc_440gx"/>
			<xsd:enumeration value="powerpc_601"/>
			<xsd:enumeration value="powerpc_603"/>
			<xsd:enumeration value="powerpc_603e"/>
			<xsd:enumeration value="powerpc_603ev"/>
			<xsd:enumeration value="powerpc_604"/>
			<xsd:enumeration value="powerpc_604e"/>
			<xsd:enumeration value="powerpc_740_g3"/>
			<xsd:enumeration value="powerpc_750cx"/>
			<xsd:enumeration value="powerpc_750_g3"/>
			<xsd:enumeration value="powerpc_970"/>
			<xsd:enumeration value="powerpc_rs64"/>
			<xsd:enumeration value="powerpc_rs64_ii"/>
			<xsd:enumeration value="powerpc_rs64_iii"/>
			<xsd:enumeration value="powerpc_rs64_iv"/>
			<xsd:enumeration value="pr31700"/>
			<xsd:enumeration value="r10000"/>
			<xsd:enumeration value="r12000"/>
			<xsd:enumeration value="r12000a"/>
			<xsd:enumeration value="r14000"/>
			<xsd:enumeration value="r14000a"/>
			<xsd:enumeration value="r16000"/>
			<xsd:enumeration value="r16000a"/>
			<xsd:enumeration value="r16000b"/>
			<xsd:enumeration value="r3900"/>
			<xsd:enumeration value="r3910"/>
			<xsd:enumeration value="r3912"/>
			<xsd:enumeration value="r4000"/>
			<xsd:enumeration value="r4300"/>
			<xsd:enumeration value="r4310"/>
			<xsd:enumeration value="r4640"/>
			<xsd:enumeration value="r4700"/>
			<xsd:enumeration value="r5000"/>
			<xsd:enumeration value="r5230"/>
			<xsd:enumeration value="rm5231"/>
			<xsd:enumeration value="s3c2410"/>
			<xsd:enumeration value="s3c2440"/>
			<xsd:enumeration value="s3c2442"/>
			<xsd:enumeration value="sa_110"/>
			<xsd:enumeration value="sa_1100"/>
			<xsd:enumeration value="sa_1110"/>
			<xsd:enumeration value="sempron"/>
			<xsd:enumeration value="Sempron_140"/>
			<xsd:enumeration value="Sempron_3500_plus"/>
			<xsd:enumeration value="Sempron_3600_plus"/>
			<xsd:enumeration value="Sempron_LE_1250"/>
			<xsd:enumeration value="Sempron_LE_1300"/>
			<xsd:enumeration value="Sempron_M100"/>
			<xsd:enumeration value="Sempron_M120"/>
			<xsd:enumeration value="sh_4"/>
			<xsd:enumeration value="sh7709a"/>
			<xsd:enumeration value="sis550"/>
			<xsd:enumeration value="snapdragon"/>
			<xsd:enumeration value="sparc"/>
			<xsd:enumeration value="sparc64v"/>
			<xsd:enumeration value="sparc_ii"/>
			<xsd:enumeration value="supersparc"/>
			<xsd:enumeration value="supersparc_ii"/>
			<xsd:enumeration value="tegra"/>
			<xsd:enumeration value="tegra_2_0"/>
			<xsd:enumeration value="tegra_250"/>
			<xsd:enumeration value="tegra_3_0"/>
			<xsd:enumeration value="texas_instruments_omap1510"/>
			<xsd:enumeration value="tm_44"/>
			<xsd:enumeration value="tmpr3922au"/>
			<xsd:enumeration value="turbosparc"/>
			<xsd:enumeration value="turion_64"/>
			<xsd:enumeration value="turion_64_x2"/>
			<xsd:enumeration value="Turion_64_X2_Dual_Core_RM_70"/>
			<xsd:enumeration value="Turion_64_X2_Dual_Core_RM_72"/>
			<xsd:enumeration value="Turion_64_X2_Dual_Core_TL_52"/>
			<xsd:enumeration value="Turion_64_X2_Dual_Core_TL_56"/>
			<xsd:enumeration value="Turion_64_X2_Mobile"/>
			<xsd:enumeration value="Turion_64_X2_RM_74"/>
			<xsd:enumeration value="Turion_64_X2_TK_53"/>
			<xsd:enumeration value="Turion_64_X2_TK_55"/>
			<xsd:enumeration value="Turion_64_X2_TK_57"/>
			<xsd:enumeration value="Turion_64_X2_TK_58"/>
			<xsd:enumeration value="Turion_64_X2_TL_50"/>
			<xsd:enumeration value="Turion_64_X2_TL_57"/>
			<xsd:enumeration value="Turion_64_X2_TL_58"/>
			<xsd:enumeration value="Turion_64_X2_TL_60"/>
			<xsd:enumeration value="Turion_64_X2_TL_62"/>
			<xsd:enumeration value="Turion_64_X2_TL_64_Gold"/>
			<xsd:enumeration value="Turion_64_X2_TL_66"/>
			<xsd:enumeration value="Turion_64_X2_Ultra_ZM_82"/>
			<xsd:enumeration value="Turion_64_X2_Ultra_ZM_85"/>
			<xsd:enumeration value="Turion_64_X2_Ultra_ZM_87"/>
			<xsd:enumeration value="Turion_64_X2_ZM_72"/>
			<xsd:enumeration value="Turion_64_X2_ZM_74"/>
			<xsd:enumeration value="Turion_64_X2_ZM_80"/>
			<xsd:enumeration value="Turion_II_Neo_X2_Dual_Core_K625"/>
			<xsd:enumeration value="Turion_II_Neo_X2_Dual_Core_L625"/>
			<xsd:enumeration value="Turion_II_Ultra_X2_Dual_Core_M600"/>
			<xsd:enumeration value="Turion_II_Ultra_X2_Dual_Core_M620"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_M300"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_M500"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_M520"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_P520"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_P540"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_P560"/>
			<xsd:enumeration value="Turion_X2_Dual_Core_RM_75"/>
			<xsd:enumeration value="Turion_X2_Ultra_Dual_Core_ZM_85"/>
			<xsd:enumeration value="tx3922"/>
			<xsd:enumeration value="ultrasparc_i"/>
			<xsd:enumeration value="ultrasparc_ii"/>
			<xsd:enumeration value="ultrasparc_iie"/>
			<xsd:enumeration value="ultrasparc_iii"/>
			<xsd:enumeration value="ultrasparc_iii_cu"/>
			<xsd:enumeration value="ultrasparc_iiii"/>
			<xsd:enumeration value="ultrasparc_iii_plus"/>
			<xsd:enumeration value="ultrasparc_iis"/>
			<xsd:enumeration value="ultrasparc_iv"/>
			<xsd:enumeration value="ultrasparc_iv_plus"/>
			<xsd:enumeration value="ultrasparc_s400"/>
			<xsd:enumeration value="ultrasparc_t1"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="v25"/>
			<xsd:enumeration value="v30"/>
			<xsd:enumeration value="v30mx"/>
			<xsd:enumeration value="via_cyrix_c3"/>
			<xsd:enumeration value="vr4111"/>
			<xsd:enumeration value="vr4121"/>
			<xsd:enumeration value="vr4122"/>
			<xsd:enumeration value="vr4131"/>
			<xsd:enumeration value="vr4181"/>
			<xsd:enumeration value="vr4300"/>
			<xsd:enumeration value="V_Series_Single_Core_V105"/>
			<xsd:enumeration value="V_Series_Single_Core_V120"/>
			<xsd:enumeration value="V_Series_Single_Core_V140"/>
			<xsd:enumeration value="winchip_2"/>
			<xsd:enumeration value="winchip_c6"/>
			<xsd:enumeration value="Xeon"/>
			<xsd:enumeration value="Xeon_3000"/>
			<xsd:enumeration value="Xeon_3530"/>
			<xsd:enumeration value="Xeon_5000"/>
			<xsd:enumeration value="Xeon_5400"/>
			<xsd:enumeration value="Xeon_E5504"/>
			<xsd:enumeration value="Xeon_E5506"/>
			<xsd:enumeration value="Xeon_E5520"/>
			<xsd:enumeration value="Xeon_E5530"/>
			<xsd:enumeration value="Xeon_W3503"/>
			<xsd:enumeration value="Xeon_W5580"/>
			<xsd:enumeration value="Xeon_X5560"/>
			<xsd:enumeration value="xscale"/>
			<xsd:enumeration value="xscale_pxa260"/>
			<xsd:enumeration value="xscale_pxa261"/>
			<xsd:enumeration value="xscale_pxa262"/>
			<xsd:enumeration value="xscale_pxa270"/>
			<xsd:enumeration value="xscale_pxa272"/>
			<xsd:enumeration value="xscale_pxa901"/>
			<xsd:enumeration value="celeron_3865u"/>
			<xsd:enumeration value="ryzen_5_2600h"/>
			<xsd:enumeration value="core_i7_4810HQ"/>
			<xsd:enumeration value="xeon_silver_4116t"/>
			<xsd:enumeration value="xeon_silver_4116"/>
			<xsd:enumeration value="xeon_silver_4114t"/>
			<xsd:enumeration value="xeon_silver_4114"/>
			<xsd:enumeration value="xeon_silver_4112"/>
			<xsd:enumeration value="xeon_silver_4110"/>
			<xsd:enumeration value="xeon_silver_4109t"/>
			<xsd:enumeration value="xeon_silver_4108"/>
			<xsd:enumeration value="xeon_platinum_8180m"/>
			<xsd:enumeration value="xeon_platinum_8180"/>
			<xsd:enumeration value="xeon_platinum_8176m"/>
			<xsd:enumeration value="xeon_platinum_8176f"/>
			<xsd:enumeration value="xeon_platinum_8176"/>
			<xsd:enumeration value="xeon_platinum_8170m"/>
			<xsd:enumeration value="xeon_platinum_8170"/>
			<xsd:enumeration value="xeon_platinum_8168"/>
			<xsd:enumeration value="xeon_platinum_8164"/>
			<xsd:enumeration value="xeon_platinum_8160t"/>
			<xsd:enumeration value="xeon_platinum_8160m"/>
			<xsd:enumeration value="xeon_platinum_8160f"/>
			<xsd:enumeration value="xeon_platinum_8160"/>
			<xsd:enumeration value="xeon_platinum_8158"/>
			<xsd:enumeration value="xeon_platinum_8156"/>
			<xsd:enumeration value="xeon_platinum_8153"/>
			<xsd:enumeration value="xeon_gold_6154"/>
			<xsd:enumeration value="xeon_gold_6152"/>
			<xsd:enumeration value="xeon_gold_6150"/>
			<xsd:enumeration value="xeon_gold_6148f"/>
			<xsd:enumeration value="xeon_gold_6148"/>
			<xsd:enumeration value="xeon_gold_6146"/>
			<xsd:enumeration value="xeon_gold_6144"/>
			<xsd:enumeration value="xeon_gold_6142m"/>
			<xsd:enumeration value="xeon_gold_6142f"/>
			<xsd:enumeration value="xeon_gold_6142"/>
			<xsd:enumeration value="xeon_gold_6140m"/>
			<xsd:enumeration value="xeon_gold_6140"/>
			<xsd:enumeration value="xeon_gold_6138t"/>
			<xsd:enumeration value="xeon_gold_6138f"/>
			<xsd:enumeration value="xeon_gold_6138"/>
			<xsd:enumeration value="xeon_gold_6136"/>
			<xsd:enumeration value="xeon_gold_6134m"/>
			<xsd:enumeration value="xeon_gold_6134"/>
			<xsd:enumeration value="xeon_gold_6132"/>
			<xsd:enumeration value="xeon_gold_6130t"/>
			<xsd:enumeration value="xeon_gold_6130f"/>
			<xsd:enumeration value="xeon_gold_6130"/>
			<xsd:enumeration value="xeon_gold_6128"/>
			<xsd:enumeration value="xeon_gold_6126t"/>
			<xsd:enumeration value="xeon_gold_6126f"/>
			<xsd:enumeration value="xeon_gold_6126"/>
			<xsd:enumeration value="xeon_gold_5122"/>
			<xsd:enumeration value="xeon_gold_5120t"/>
			<xsd:enumeration value="xeon_gold_5120"/>
			<xsd:enumeration value="xeon_gold_5119t"/>
			<xsd:enumeration value="xeon_gold_5118"/>
			<xsd:enumeration value="xeon_gold_5115"/>
			<xsd:enumeration value="xeon_bronze_3106"/>
			<xsd:enumeration value="xeon_bronze_3104"/>
			<xsd:enumeration value="ryzen_threadripper_1950x"/>
			<xsd:enumeration value="ryzen_threadripper_1920x"/>
			<xsd:enumeration value="ryzen_threadripper_1900x"/>
			<xsd:enumeration value="ryzen_7_pro_2700u"/>
			<xsd:enumeration value="amd_ryzen_7_pro_1700x"/>
			<xsd:enumeration value="amd_ryzen_7_pro_1700"/>
			<xsd:enumeration value="ryzen_7_2700u"/>
			<xsd:enumeration value="amd_ryzen_7_1800x"/>
			<xsd:enumeration value="amd_ryzen_7_1700x"/>
			<xsd:enumeration value="amd_ryzen_7_1700"/>
			<xsd:enumeration value="amd_ryzen_5_pro_1600"/>
			<xsd:enumeration value="amd_ryzen_5_pro_1500"/>
			<xsd:enumeration value="ryzen_5_2500u"/>
			<xsd:enumeration value="ryzen_5_2400ge"/>
			<xsd:enumeration value="amd_ryzen_5_1600x"/>
			<xsd:enumeration value="amd_ryzen_5_1600"/>
			<xsd:enumeration value="amd_ryzen_5_1500x"/>
			<xsd:enumeration value="amd_ryzen_5_1400"/>
			<xsd:enumeration value="ryzen_3_pro_2300u"/>
			<xsd:enumeration value="amd_ryzen_3_pro_1300"/>
			<xsd:enumeration value="amd_ryzen_3_pro_1200"/>
			<xsd:enumeration value="ryzen_3_2300u"/>
			<xsd:enumeration value="ryzen_3_2200u"/>
			<xsd:enumeration value="ryzen_3_2200ge"/>
			<xsd:enumeration value="amd_ryzen_3_1300x"/>
			<xsd:enumeration value="amd_ryzen_3_1200"/>
			<xsd:enumeration value="pentium_gold_g5500t"/>
			<xsd:enumeration value="pentium_gold_g5400t"/>
			<xsd:enumeration value="core_i9_8950hk"/>
			<xsd:enumeration value="core_i9_7980xe"/>
			<xsd:enumeration value="core_i9_7960x"/>
			<xsd:enumeration value="core_i9_7940x"/>
			<xsd:enumeration value="core_i9_7920x"/>
			<xsd:enumeration value="core_i9_7900x"/>
			<xsd:enumeration value="core_i9_7820x"/>
			<xsd:enumeration value="core_i9_7800x"/>
			<xsd:enumeration value="core_i9_7740x"/>
			<xsd:enumeration value="core_i9_7640x"/>
			<xsd:enumeration value="core_i7_8750h"/>
			<xsd:enumeration value="core_i7_8700t"/>
			<xsd:enumeration value="core_i7_8700k"/>
			<xsd:enumeration value="core_i7_8700"/>
			<xsd:enumeration value="core_i7_8550u"/>
			<xsd:enumeration value="core_i5_8400t"/>
			<xsd:enumeration value="core_i5_8400"/>
			<xsd:enumeration value="core_i5_8300h"/>
			<xsd:enumeration value="core_i5_8250u"/>
			<xsd:enumeration value="core_i3_8300"/>
			<xsd:enumeration value="core_i3_8130u"/>
			<xsd:enumeration value="core_i3_8100"/>
			<xsd:enumeration value="pentium_gold_g5400"/>
			<xsd:enumeration value="pentium_gold_g5500"/>
			<xsd:enumeration value="pentium_gold_g5600"/>
			<xsd:enumeration value="ryzen_3_2200g"/>
			<xsd:enumeration value="ryzen_5_2400g"/>
			<xsd:enumeration value="core_i9"/>
			<xsd:enumeration value="amd_ryzen_1800x"/>
			<xsd:enumeration value="amd_ryzen_1700x"/>
			<xsd:enumeration value="amd_ryzen_1700"/>
			<xsd:enumeration value="amd_ryzen_1600x"/>
			<xsd:enumeration value="amd_ryzen_1600"/>
			<xsd:enumeration value="amd_ryzen_1500x"/>
			<xsd:enumeration value="amd_ryzen_1400"/>
			<xsd:enumeration value="amd_ryzen_7"/>
			<xsd:enumeration value="xeon_e3_1226v3"/>
			<xsd:enumeration value="core_i5_6402p"/>
			<xsd:enumeration value="core_i7_6600u"/>
			<xsd:enumeration value="celeron_n"/>
			<xsd:enumeration value="celeron_n3060"/>
			<xsd:enumeration value="atom_z8350"/>
			<xsd:enumeration value="apple_a8"/>
			<xsd:enumeration value="celeron_j3355"/>
			<xsd:enumeration value="celeron_j3455"/>
			<xsd:enumeration value="celeron_n3350"/>
			<xsd:enumeration value="celeron_n3450"/>
			<xsd:enumeration value="pentium_j4205"/>
			<xsd:enumeration value="pentium_n4200"/>
			<xsd:enumeration value="core_m_7y30"/>
			<xsd:enumeration value="core_i3_6157u"/>
			<xsd:enumeration value="core_i3_7100u"/>
			<xsd:enumeration value="core_i5_6350hq"/>
			<xsd:enumeration value="core_i5_7200u"/>
			<xsd:enumeration value="core_i5_7y54"/>
			<xsd:enumeration value="core_i7_7y75"/>
			<xsd:enumeration value="core_i7_6770hq"/>
			<xsd:enumeration value="core_i7_7500u"/>
			<xsd:enumeration value="core_i7_6800k"/>
			<xsd:enumeration value="core_i7_6850k"/>
			<xsd:enumeration value="core_i7_6900k"/>
			<xsd:enumeration value="core_i7_6950x"/>
			<xsd:enumeration value="xeon_e5_2650"/>
			<xsd:enumeration value="xeon_e5_2620"/>
			<xsd:enumeration value="xeon_e5_2600"/>
			<xsd:enumeration value="xeon_e5_2450"/>
			<xsd:enumeration value="xeon_e5_2407"/>
			<xsd:enumeration value="xeon_e5_2400"/>
			<xsd:enumeration value="xeon_e5_1650"/>
			<xsd:enumeration value="xeon_e5_1630v3"/>
			<xsd:enumeration value="xeon_e5_1620"/>
			<xsd:enumeration value="xeon_e5_1607"/>
			<xsd:enumeration value="xeon_e3_1271"/>
			<xsd:enumeration value="xeon_e3_1241"/>
			<xsd:enumeration value="xeon_e3_1231"/>
			<xsd:enumeration value="xeon_e3_1225"/>
			<xsd:enumeration value="xeon_e3_1220"/>
			<xsd:enumeration value="pentium_t7500"/>
			<xsd:enumeration value="pentium_t6670"/>
			<xsd:enumeration value="pentium_t6600"/>
			<xsd:enumeration value="pentium_t5750"/>
			<xsd:enumeration value="pentium_sl9300"/>
			<xsd:enumeration value="pentium_p8700"/>
			<xsd:enumeration value="pentium_p8600"/>
			<xsd:enumeration value="pentium_p7570"/>
			<xsd:enumeration value="pentium_other"/>
			<xsd:enumeration value="pentium_n3520"/>
			<xsd:enumeration value="pentium_n2930"/>
			<xsd:enumeration value="pentium_j2850"/>
			<xsd:enumeration value="pentium_g645t"/>
			<xsd:enumeration value="pentium_g3258"/>
			<xsd:enumeration value="pentium_g3240t"/>
			<xsd:enumeration value="pentium_g3220t"/>
			<xsd:enumeration value="pentium_g2130"/>
			<xsd:enumeration value="pentium_g2120"/>
			<xsd:enumeration value="pentium_e8500"/>
			<xsd:enumeration value="pentium_e7600"/>
			<xsd:enumeration value="pentium_e7500"/>
			<xsd:enumeration value="pentium_e6950"/>
			<xsd:enumeration value="pentium_d840"/>
			<xsd:enumeration value="pentium_d3400"/>
			<xsd:enumeration value="pentium_b987"/>
			<xsd:enumeration value="pentium_987"/>
			<xsd:enumeration value="pentium_967"/>
			<xsd:enumeration value="core_m_family"/>
			<xsd:enumeration value="core_i7_family"/>
			<xsd:enumeration value="core_i7_920xm"/>
			<xsd:enumeration value="core_i7_6700k"/>
			<xsd:enumeration value="core_i7_5960x"/>
			<xsd:enumeration value="core_i7_5930k"/>
			<xsd:enumeration value="core_i7_5820k"/>
			<xsd:enumeration value="core_i7_5600u"/>
			<xsd:enumeration value="core_i7_4970k"/>
			<xsd:enumeration value="core_i7_4930mx"/>
			<xsd:enumeration value="core_i7_4900mq"/>
			<xsd:enumeration value="core_i7_4770"/>
			<xsd:enumeration value="core_i7_4765t"/>
			<xsd:enumeration value="core_i7_4600u"/>
			<xsd:enumeration value="core_i7_4470s"/>
			<xsd:enumeration value="core_i7_3740qm"/>
			<xsd:enumeration value="core_i7_3687u"/>
			<xsd:enumeration value="core_i7_36517u"/>
			<xsd:enumeration value="core_i7_3635qm"/>
			<xsd:enumeration value="core_i7_3632qn"/>
			<xsd:enumeration value="core_i7_3630m"/>
			<xsd:enumeration value="core_i7_2760qm"/>
			<xsd:enumeration value="core_i7_2670m"/>
			<xsd:enumeration value="core_i7_2630q"/>
			<xsd:enumeration value="core_i7_2630m"/>
			<xsd:enumeration value="core_i5_family"/>
			<xsd:enumeration value="core_i5_6600k"/>
			<xsd:enumeration value="core_i5_5300u"/>
			<xsd:enumeration value="core_i5_4670s"/>
			<xsd:enumeration value="core_i5_4310u"/>
			<xsd:enumeration value="core_i5_4310m"/>
			<xsd:enumeration value="core_i5_4300u"/>
			<xsd:enumeration value="core_i5_3570s"/>
			<xsd:enumeration value="core_i5_3570"/>
			<xsd:enumeration value="core_i5_3470s"/>
			<xsd:enumeration value="core_i5_3470"/>
			<xsd:enumeration value="core_i5_3340"/>
			<xsd:enumeration value="core_i5_32320m"/>
			<xsd:enumeration value="core_i5_3200u"/>
			<xsd:enumeration value="core_i5_2550k"/>
			<xsd:enumeration value="core_i5_2410qm"/>
			<xsd:enumeration value="core_i5_2410"/>
			<xsd:enumeration value="core_i3_family"/>
			<xsd:enumeration value="core_i3_539"/>
			<xsd:enumeration value="core_i3_4350"/>
			<xsd:enumeration value="core_i3_4330t"/>
			<xsd:enumeration value="core_i3_4330"/>
			<xsd:enumeration value="core_i3_350um"/>
			<xsd:enumeration value="core_i3_3220t"/>
			<xsd:enumeration value="core_i3_2375m"/>
			<xsd:enumeration value="core_i3_2357u"/>
			<xsd:enumeration value="core_i3_2340m"/>
			<xsd:enumeration value="core_i3_2330"/>
			<xsd:enumeration value="core_i3_2227u"/>
			<xsd:enumeration value="core_i3_2125"/>
			<xsd:enumeration value="celeron_t1400"/>
			<xsd:enumeration value="celeron_n2930"/>
			<xsd:enumeration value="celeron_n2820"/>
			<xsd:enumeration value="celeron_n2810"/>
			<xsd:enumeration value="celeron_n2807"/>
			<xsd:enumeration value="celeron_n2806"/>
			<xsd:enumeration value="celeron_n2805"/>
			<xsd:enumeration value="celeron_j1850"/>
			<xsd:enumeration value="celeron_g465"/>
			<xsd:enumeration value="celeron_g1620t"/>
			<xsd:enumeration value="celeron_807"/>
			<xsd:enumeration value="celeron_1037u"/>
			<xsd:enumeration value="celeron_1005m"/>
			<xsd:enumeration value="celeron_1000m"/>
			<xsd:enumeration value="atom_z7345"/>
			<xsd:enumeration value="atom_z650"/>
			<xsd:enumeration value="atom_z3736f"/>
			<xsd:enumeration value="atom_x5_z8300"/>
			<xsd:enumeration value="atom_n2930"/>
			<xsd:enumeration value="atom_n2830"/>
			<xsd:enumeration value="atom_e3815"/>
			<xsd:enumeration value="atom_d2701"/>
			<xsd:enumeration value="atom_455"/>
			<xsd:enumeration value="apple_ci7"/>
			<xsd:enumeration value="apple_ci5"/>
			<xsd:enumeration value="apple_ci3"/>
			<xsd:enumeration value="apple_c2d"/>
			<xsd:enumeration value="pentium_g4520"/>
			<xsd:enumeration value="pentium_g4500t"/>
			<xsd:enumeration value="pentium_g4500"/>
			<xsd:enumeration value="pentium_g4400t"/>
			<xsd:enumeration value="pentium_g3900t"/>
			<xsd:enumeration value="pentium_g3900"/>
			<xsd:enumeration value="pentium_4405y"/>
			<xsd:enumeration value="core_m_6y57"/>
			<xsd:enumeration value="core_i7_6820hq"/>
			<xsd:enumeration value="core_i7_6567u"/>
			<xsd:enumeration value="core_i7_6560u"/>
			<xsd:enumeration value="core_i7_5675r"/>
			<xsd:enumeration value="core_i5_6440hq"/>
			<xsd:enumeration value="core_i5_6287u"/>
			<xsd:enumeration value="core_i5_6267u"/>
			<xsd:enumeration value="core_i5_6260u"/>
			<xsd:enumeration value="core_i3_6320"/>
			<xsd:enumeration value="core_i3_6300t"/>
			<xsd:enumeration value="core_i3_6300"/>
			<xsd:enumeration value="core_i3_6167u"/>
			<xsd:enumeration value="core_i3_6100t"/>
			<xsd:enumeration value="celeron_3855u"/>
			<xsd:enumeration value="intel_core_i3_6100"/>
			<xsd:enumeration value="intel_pentium_g4400"/>
			<xsd:enumeration value="intel_core_i5_6600"/>
			<xsd:enumeration value="intel_core_i5_6500"/>
			<xsd:enumeration value="atom_c3200_rk"/>
			<xsd:enumeration value="pentium_4405u"/>
			<xsd:enumeration value="core_m_6y30"/>
			<xsd:enumeration value="core_m_6y54"/>
			<xsd:enumeration value="core_m_6y75"/>
			<xsd:enumeration value="core_i3_6100h"/>
			<xsd:enumeration value="core_i3_6100u"/>
			<xsd:enumeration value="core_i5_6400t"/>
			<xsd:enumeration value="core_i5_6500t"/>
			<xsd:enumeration value="core_i5_6600t"/>
			<xsd:enumeration value="core_i5_6200u"/>
			<xsd:enumeration value="core_i5_6300hq"/>
			<xsd:enumeration value="core_i5_6400"/>
			<xsd:enumeration value="core_i5_6500"/>
			<xsd:enumeration value="core_i5_5350h"/>
			<xsd:enumeration value="core_i5_5675c"/>
			<xsd:enumeration value="core_i5_5575r"/>
			<xsd:enumeration value="core_i5_5675r"/>
			<xsd:enumeration value="core_i7_5775r"/>
			<xsd:enumeration value="core_i7_5775c"/>
			<xsd:enumeration value="core_i5_5750hq"/>
			<xsd:enumeration value="core_i5_6600"/>
			<xsd:enumeration value="core_i7_6700t"/>
			<xsd:enumeration value="core_i7_6500u"/>
			<xsd:enumeration value="core_i7_6700"/>
			<xsd:enumeration value="core_i7_6700hq"/>
			<xsd:enumeration value="core_i7_6820hk"/>
			<xsd:enumeration value="rockchip_rk3288"/>
			<xsd:enumeration value="amd_fx"/>
			<xsd:enumeration value="amd_a8"/>
			<xsd:enumeration value="amd_a6"/>
			<xsd:enumeration value="amd_a4"/>
			<xsd:enumeration value="intel_core_i7_5850hq"/>
			<xsd:enumeration value="intel_core_i7_5700hq"/>
			<xsd:enumeration value="intel_core_i7_5950hq"/>
			<xsd:enumeration value="intel_turbo_n2840"/>
			<xsd:enumeration value="amd_athlon_quad_core_5150"/>
			<xsd:enumeration value="a4_7210"/>
			<xsd:enumeration value="a6_7000"/>
			<xsd:enumeration value="a6_7310"/>
			<xsd:enumeration value="a6_7400k"/>
			<xsd:enumeration value="a6_8500p"/>
			<xsd:enumeration value="a6_8550"/>
			<xsd:enumeration value="a8_6410"/>
			<xsd:enumeration value="a8_7100"/>
			<xsd:enumeration value="a8_7200p"/>
			<xsd:enumeration value="a8_7410"/>
			<xsd:enumeration value="a8_7650k"/>
			<xsd:enumeration value="a8_8600p"/>
			<xsd:enumeration value="a8_8650"/>
			<xsd:enumeration value="a10_7300"/>
			<xsd:enumeration value="a10_7400p"/>
			<xsd:enumeration value="a10_7700k"/>
			<xsd:enumeration value="a10_8700p"/>
			<xsd:enumeration value="a10_8750"/>
			<xsd:enumeration value="athlon_x2_450"/>
			<xsd:enumeration value="athlon_x4_540"/>
			<xsd:enumeration value="athlon_x4_560"/>
			<xsd:enumeration value="athlon_x4_830"/>
			<xsd:enumeration value="athlon_x4_840"/>
			<xsd:enumeration value="athlon_x4_860k"/>
			<xsd:enumeration value="atom_c3130"/>
			<xsd:enumeration value="atom_c3230_rk"/>
			<xsd:enumeration value="atom_z2520"/>
			<xsd:enumeration value="atom_zZ8300"/>
			<xsd:enumeration value="atom_z8500"/>
			<xsd:enumeration value="atom_z8700"/>
			<xsd:enumeration value="celeron_3215u"/>
			<xsd:enumeration value="celeron_n3000"/>
			<xsd:enumeration value="celeron_n3050"/>
			<xsd:enumeration value="celeron_n3150"/>
			<xsd:enumeration value="core_i3_4170"/>
			<xsd:enumeration value="core_i3_4170t"/>
			<xsd:enumeration value="core_i3_4370t"/>
			<xsd:enumeration value="core_i3_5015u"/>
			<xsd:enumeration value="core_i3_5020u"/>
			<xsd:enumeration value="core_i5_4690k"/>
			<xsd:enumeration value="core_i7_4870hq"/>
			<xsd:enumeration value="e1_7010"/>
			<xsd:enumeration value="e2_6110"/>
			<xsd:enumeration value="e2_7110"/>
			<xsd:enumeration value="fx_8800p"/>
			<xsd:enumeration value="pentium_3825u"/>
			<xsd:enumeration value="pentium_g3260"/>
			<xsd:enumeration value="pentium_g3260t"/>
			<xsd:enumeration value="pentium_g3460t"/>
			<xsd:enumeration value="pentium_g3470"/>
			<xsd:enumeration value="pentium_n3700"/>
			<xsd:enumeration value="fx_4300"/>
			<xsd:enumeration value="core_i7_4790K"/>
			<xsd:enumeration value="bay_trail_t_z3735g"/>
			<xsd:enumeration value="pentium_n3540"/>
			<xsd:enumeration value="pentium_j2900"/>
			<xsd:enumeration value="pentium_g3460"/>
			<xsd:enumeration value="pentium_g3450t"/>
			<xsd:enumeration value="pentium_g3450"/>
			<xsd:enumeration value="pentium_g3250"/>
			<xsd:enumeration value="pentium_3805u"/>
			<xsd:enumeration value="pentium_3561y"/>
			<xsd:enumeration value="pentium_3560m"/>
			<xsd:enumeration value="pentium_2030m"/>
			<xsd:enumeration value="mediatek_mt8127"/>
			<xsd:enumeration value="core_m"/>
			<xsd:enumeration value="core_m_5y71"/>
			<xsd:enumeration value="core_m_5y70"/>
			<xsd:enumeration value="core_m_5y51"/>
			<xsd:enumeration value="core_m_5y31"/>
			<xsd:enumeration value="core_m_5y10c"/>
			<xsd:enumeration value="core_m_5y10a"/>
			<xsd:enumeration value="core_m_5y10"/>
			<xsd:enumeration value="core_i7_5557u"/>
			<xsd:enumeration value="core_i7_5550u"/>
			<xsd:enumeration value="core_i7_5500u"/>
			<xsd:enumeration value="core_i7_4790t"/>
			<xsd:enumeration value="core_i7_4790s"/>
			<xsd:enumeration value="core_i7_4785t"/>
			<xsd:enumeration value="core_i7_4771"/>
			<xsd:enumeration value="core_i7_4770r"/>
			<xsd:enumeration value="core_i7_4770hq"/>
			<xsd:enumeration value="core_i7_4722hq"/>
			<xsd:enumeration value="core_i7_4720hq"/>
			<xsd:enumeration value="core_i7_4712mq"/>
			<xsd:enumeration value="core_i7_4712hq"/>
			<xsd:enumeration value="core_i5_5287u"/>
			<xsd:enumeration value="core_i5_5257u"/>
			<xsd:enumeration value="core_i5_5250u"/>
			<xsd:enumeration value="core_i5_5200u"/>
			<xsd:enumeration value="core_i5_4690t"/>
			<xsd:enumeration value="core_i5_4690s"/>
			<xsd:enumeration value="core_i5_4690"/>
			<xsd:enumeration value="core_i5_4670r"/>
			<xsd:enumeration value="core_i5_4590t"/>
			<xsd:enumeration value="core_i5_4590s"/>
			<xsd:enumeration value="core_i5_4590"/>
			<xsd:enumeration value="core_i5_4570r"/>
			<xsd:enumeration value="core_i5_4460t"/>
			<xsd:enumeration value="core_i5_4460s"/>
			<xsd:enumeration value="core_i5_4308u"/>
			<xsd:enumeration value="core_i5_4278u"/>
			<xsd:enumeration value="core_i5_4260u"/>
			<xsd:enumeration value="core_i5_4220y"/>
			<xsd:enumeration value="core_i5_4210m"/>
			<xsd:enumeration value="core_i5_4210h"/>
			<xsd:enumeration value="core_i3_5157u"/>
			<xsd:enumeration value="core_i3_5010u"/>
			<xsd:enumeration value="core_i3_5005u"/>
			<xsd:enumeration value="core_i3_4370"/>
			<xsd:enumeration value="core_i3_4360t"/>
			<xsd:enumeration value="core_i3_4360"/>
			<xsd:enumeration value="core_i3_4160"/>
			<xsd:enumeration value="core_i3_4120u"/>
			<xsd:enumeration value="core_i3_4110m"/>
			<xsd:enumeration value="core_i3_4030y"/>
			<xsd:enumeration value="core_i3_4025u"/>
			<xsd:enumeration value="celeron_n2940"/>
			<xsd:enumeration value="celeron_n2840"/>
			<xsd:enumeration value="celeron_n2815"/>
			<xsd:enumeration value="celeron_n2808"/>
			<xsd:enumeration value="celeron_j1900"/>
			<xsd:enumeration value="celeron_j1800"/>
			<xsd:enumeration value="celeron_g1850"/>
			<xsd:enumeration value="celeron_g1840t"/>
			<xsd:enumeration value="celeron_g1840"/>
			<xsd:enumeration value="celeron_3205u"/>
			<xsd:enumeration value="celeron_2961y"/>
			<xsd:enumeration value="atom_z3795"/>
			<xsd:enumeration value="atom_z3785"/>
			<xsd:enumeration value="atom_z3775d"/>
			<xsd:enumeration value="atom_z3775"/>
			<xsd:enumeration value="atom_z3770d"/>
			<xsd:enumeration value="atom_z3745d"/>
			<xsd:enumeration value="atom_z3745"/>
			<xsd:enumeration value="atom_z3740d"/>
			<xsd:enumeration value="atom_z3735g"/>
			<xsd:enumeration value="atom_z3735f"/>
			<xsd:enumeration value="atom_z3735e"/>
			<xsd:enumeration value="atom_z3735d"/>
			<xsd:enumeration value="atom_z3580"/>
			<xsd:enumeration value="atom_z3560"/>
			<xsd:enumeration value="atom_z3480"/>
			<xsd:enumeration value="atom_z3460"/>
			<xsd:enumeration value="core_i7_4710MQ"/>
			<xsd:enumeration value="amd_a6_6400k"/>
			<xsd:enumeration value="core_i7_4810MQ"/>
			<xsd:enumeration value="core_i7_4860HQ"/>
			<xsd:enumeration value="atom_z3735"/>
			<xsd:enumeration value="quad_core_a8_6410"/>
			<xsd:enumeration value="Atom_z3635G"/>
			<xsd:enumeration value="arm_v7"/>
			<xsd:enumeration value="core_i7_4980HQ"/>
			<xsd:enumeration value="intel_core_i7_4810mq"/>
			<xsd:enumeration value="tegra_k1"/>
			<xsd:enumeration value="intel_bay_trail_m_n2830_dual_core"/>
			<xsd:enumeration value="intel_core_m"/>
			<xsd:enumeration value="a10_7850k"/>
			<xsd:enumeration value="a8_7600"/>
			<xsd:enumeration value="Core_i7_4790"/>
			<xsd:enumeration value="Core_i5_4460"/>
			<xsd:enumeration value="Core_i7_4710HQ"/>
			<xsd:enumeration value="Celeron_N2830_Dual_Core"/>
			<xsd:enumeration value="Core_i3_4160T"/>
			<xsd:enumeration value="Pentium_G3250T"/>
			<xsd:enumeration value="Core_i5_4570T"/>
			<xsd:enumeration value="FX_8300"/>
			<xsd:enumeration value="a10_7800"/>
			<xsd:enumeration value="cortex_a8"/>
			<xsd:enumeration value="Core_i3_4150T"/>
			<xsd:enumeration value="Pentium_G3240"/>
			<xsd:enumeration value="Core_i3_4150"/>
			<xsd:enumeration value="a33_arm_cortex_a7_quad_core"/>
			<xsd:enumeration value="quad_core_a8_6410_accelerated"/>
			<xsd:enumeration value="Celeron_N2830"/>
			<xsd:enumeration value="Pentium_N3530"/>
			<xsd:enumeration value="mxs"/>
			<xsd:enumeration value="mtk_8121"/>
			<xsd:enumeration value="E1_6010"/>
			<xsd:enumeration value="A4_6210"/>
			<xsd:enumeration value="Celeron_2957U"/>
			<xsd:enumeration value="Pentium_3558U"/>
			<xsd:enumeration value="Core_i3_4030U"/>
			<xsd:enumeration value="Core_i5_4210U"/>
			<xsd:enumeration value="Core_i7_4510U"/>
			<xsd:enumeration value="A6_6310"/>
			<xsd:enumeration value="MediaTek_MT8125"/>
			<xsd:enumeration value="phenom_x2"/>
			<xsd:enumeration value="phenom_x3"/>
			<xsd:enumeration value="phenom_x4"/>
			<xsd:enumeration value="celeron_d"/>
			<xsd:enumeration value="core_2_extreme"/>
			<xsd:enumeration value="core_2_quad"/>
			<xsd:enumeration value="core_2_solo"/>
			<xsd:enumeration value="core_duo"/>
			<xsd:enumeration value="core_i7_extreme"/>
			<xsd:enumeration value="core_solo"/>
			<xsd:enumeration value="pentium_d"/>
			<xsd:enumeration value="pentium_ii"/>
			<xsd:enumeration value="pentium_iii"/>
			<xsd:enumeration value="a_series"/>
			<xsd:enumeration value="athlon_ii_x2"/>
			<xsd:enumeration value="athlon_x2"/>
			<xsd:enumeration value="athlon_x4"/>
			<xsd:enumeration value="e_series"/>
			<xsd:enumeration value="fx_4_core"/>
			<xsd:enumeration value="fx_6_core"/>
			<xsd:enumeration value="fx_8_core"/>
			<xsd:enumeration value="cortex"/>
			<xsd:enumeration value="securcore"/>
			<xsd:enumeration value="celeron_m"/>
			<xsd:enumeration value="xeon_phi"/>
			<xsd:enumeration value="alpha"/>
			<xsd:enumeration value="exynos_5_octa_5800"/>
			<xsd:enumeration value="exynos_5_octa_5420"/>
			<xsd:enumeration value="Pentium_G3220"/>
			<xsd:enumeration value="Celeron_G1820"/>
			<xsd:enumeration value="A20"/>
			<xsd:enumeration value="A31"/>
			<xsd:enumeration value="A31s"/>
			<xsd:enumeration value="Core_i3_4130T"/>
			<xsd:enumeration value="Core_i3_4570T"/>
			<xsd:enumeration value="apple_a7"/>
			<xsd:enumeration value="A6_5350M"/>
			<xsd:enumeration value="T40_S_TEGRA_4_A15_Quad_Core"/>
			<xsd:enumeration value="SOC_PXA986_Dual_Core"/>
			<xsd:enumeration value="Pentium_N3510"/>
			<xsd:enumeration value="1_2GHz_Cortex_A8"/>
			<xsd:enumeration value="1_2GHz_Cortex_A13"/>
			<xsd:enumeration value="1_2GHz_Cortex_A9"/>
			<xsd:enumeration value="Atom_Z3740"/>
			<xsd:enumeration value="Atom_Z2560"/>
			<xsd:enumeration value="Atom_Z2580"/>
			<xsd:enumeration value="Atom_Z3770"/>
			<xsd:enumeration value="Core_i7_4650U"/>
			<xsd:enumeration value="A4_1250"/>
			<xsd:enumeration value="Clover_Trail_Plus_Z2560"/>
			<xsd:enumeration value="Exynos_4412"/>
			<xsd:enumeration value="Intel_Core_i7_Extreme"/>
			<xsd:enumeration value="MT8317T"/>
			<xsd:enumeration value="A10_5757M"/>
			<xsd:enumeration value="A8_5557M"/>
			<xsd:enumeration value="Intel_Core_i7_4702MQ"/>
			<xsd:enumeration value="A13"/>
			<xsd:enumeration value="Core_i5_3340s"/>
			<xsd:enumeration value="Core_i5_4440s"/>
			<xsd:enumeration value="Pentium_G2030T"/>
			<xsd:enumeration value="Core_i5_3340M"/>
			<xsd:enumeration value="E1_2500"/>
			<xsd:enumeration value="A4_1200_Accelerated"/>
			<xsd:enumeration value="Exynos_5250"/>
			<xsd:enumeration value="Core_i7_4200U"/>
			<xsd:enumeration value="E1_2500_Accelerated_Processor"/>
			<xsd:enumeration value="amd_r_series"/>
			<xsd:enumeration value="Core_i7_4700MQ"/>
			<xsd:enumeration value="Celeron_2950M"/>
			<xsd:enumeration value="Celeron_2955U"/>
			<xsd:enumeration value="Core_i3_3229Y"/>
			<xsd:enumeration value="Core_i3_4000M"/>
			<xsd:enumeration value="Core_i3_4005U"/>
			<xsd:enumeration value="Core_i3_4010U"/>
			<xsd:enumeration value="Core_i3_4010Y"/>
			<xsd:enumeration value="Core_i3_4012Y"/>
			<xsd:enumeration value="Core_i3_4020Y"/>
			<xsd:enumeration value="Core_i3_4100M"/>
			<xsd:enumeration value="Core_i3_4100U"/>
			<xsd:enumeration value="Core_i3_4158U"/>
			<xsd:enumeration value="Core_i5_3339Y"/>
			<xsd:enumeration value="Core_i5_3439Y"/>
			<xsd:enumeration value="Core_i5_4200H"/>
			<xsd:enumeration value="Core_i5_4200M"/>
			<xsd:enumeration value="Core_i5_4200Y"/>
			<xsd:enumeration value="Core_i5_4202Y"/>
			<xsd:enumeration value="Core_i5_4210Y"/>
			<xsd:enumeration value="Core_i5_4250U"/>
			<xsd:enumeration value="Core_i5_4258U"/>
			<xsd:enumeration value="Core_i5_4288U"/>
			<xsd:enumeration value="Core_i7_3689Y"/>
			<xsd:enumeration value="Core_i7_4550U"/>
			<xsd:enumeration value="Core_i7_4558U"/>
			<xsd:enumeration value="Core_i7_4700HQ"/>
			<xsd:enumeration value="Core_i7_4702HQ"/>
			<xsd:enumeration value="Core_i7_4750HQ"/>
			<xsd:enumeration value="Core_i7_4820K"/>
			<xsd:enumeration value="Core_i7_4930K"/>
			<xsd:enumeration value="Core_i7_4960X"/>
			<xsd:enumeration value="Pentium_3550M"/>
			<xsd:enumeration value="Pentium_3556U"/>
			<xsd:enumeration value="Pentium_3560Y"/>
			<xsd:enumeration value="Rockchip_RK2928"/>
			<xsd:enumeration value="Rockchip_3188"/>
			<xsd:enumeration value="A4_5000"/>
			<xsd:enumeration value="A6_5200"/>
			<xsd:enumeration value="Core_i3_4130"/>
			<xsd:enumeration value="Core_i5_4440"/>
			<xsd:enumeration value="Pentium_Dual_Core_2030M"/>
			<xsd:enumeration value="Celeron_1017U"/>
			<xsd:enumeration value="Pentium_Dual_Core_2127U"/>
			<xsd:enumeration value="Pentium_2127U"/>
			<xsd:enumeration value="Intel_Celeron_G1610T"/>
			<xsd:enumeration value="Intel_Pentium_G2020T"/>
			<xsd:enumeration value="Intel_Core_i3_3240T"/>
			<xsd:enumeration value="ARMv7"/>
			<xsd:enumeration value="ARMv7_Dual_Core"/>
			<xsd:enumeration value="Celeron_887"/>
			<xsd:enumeration value="Core_i3_2348M"/>
			<xsd:enumeration value="Core_i5_4430S"/>
			<xsd:enumeration value="Core_i5_4570"/>
			<xsd:enumeration value="Core_i5_4570S"/>
			<xsd:enumeration value="Core_i5_4670"/>
			<xsd:enumeration value="Core_i5_4670K"/>
			<xsd:enumeration value="Core_i7_4702MQ"/>
			<xsd:enumeration value="Core_i7_4770K"/>
			<xsd:enumeration value="Core_i7_4770S"/>
			<xsd:enumeration value="E2_Series_Dual_Core_E2_2000"/>
			<xsd:enumeration value="E_Series_Dual_Core_E1_1500"/>
			<xsd:enumeration value="i5_4670k"/>
			<xsd:enumeration value="i7_4770K"/>
			<xsd:enumeration value="Intel_Celeron_G470"/>
			<xsd:enumeration value="Intel_Core_i5_3330S"/>
			<xsd:enumeration value="Intel_Core_i5_4200U"/>
			<xsd:enumeration value="Intel_Core_i7_4500U"/>
			<xsd:enumeration value="Intel_Pentium_G2020"/>
			<xsd:enumeration value="Pentium_2117U"/>
			<xsd:enumeration value="Pentium_2129Y"/>
			<xsd:enumeration value="AMD_Kabini_A6_5200M_Quad_Core"/>
			<xsd:enumeration value="AMD_Kabini_E1_2100"/>
			<xsd:enumeration value="AMD_Richland_A10_5750M"/>
			<xsd:enumeration value="AMD_Richland_A8_5550M"/>
			<xsd:enumeration value="Intel_Core_i3_3120M"/>
			<xsd:enumeration value="Intel_Pentium_2127U_ULV"/>
			<xsd:enumeration value="Intel_Pentium_Dual_Core_2020M"/>
			<xsd:enumeration value="Intel_Celeron_1007U"/>
			<xsd:enumeration value="Intel_PDC_G2030"/>
			<xsd:enumeration value="Intel_Core_i3_3130M"/>
			<xsd:enumeration value="Intel_Core_i3_3240"/>
			<xsd:enumeration value="Intel_Core_i5_4430"/>
			<xsd:enumeration value="Intel_Core_i7_4700MQ"/>
			<xsd:enumeration value="Intel_Core_i7_4770"/>
			<xsd:enumeration value="Mediatek_8317_Dual_Core"/>
			<xsd:enumeration value="Mediatek_8389_Quadcore"/>
			<xsd:enumeration value="Intel_Clover_Trail"/>
			<xsd:enumeration value="a_series_quad_core_a8_6500"/>
			<xsd:enumeration value="a_series_quad_core_a10_6700"/>
			<xsd:enumeration value="A10"/>
			<xsd:enumeration value="210"/>
			<xsd:enumeration value="GP33003"/>
			<xsd:enumeration value="Core_i5_3330M"/>
			<xsd:enumeration value="Snapdragon_QSD8250"/>
			<xsd:enumeration value="GEODE_LX_LX_800"/>
			<xsd:enumeration value="Celeron_220"/>
			<xsd:enumeration value="Pentium_D_840"/>
			<xsd:enumeration value="Core_2_Duo_E6320"/>
			<xsd:enumeration value="Sempron_SI_42"/>
			<xsd:enumeration value="Phenom_II_X2_B55"/>
			<xsd:enumeration value="Athlon_64_3000"/>
			<xsd:enumeration value="Pentium_M_755"/>
			<xsd:enumeration value="C7_M_764"/>
			<xsd:enumeration value="Pentium_G524"/>
			<xsd:enumeration value="Sempron_2200"/>
			<xsd:enumeration value="Pentium_M_773"/>
			<xsd:enumeration value="Athlon_64_Mobile_3000"/>
			<xsd:enumeration value="Turion_64_MK_36"/>
			<xsd:enumeration value="C7_M_770_VIA"/>
			<xsd:enumeration value="Sempron_2100"/>
			<xsd:enumeration value="Sempron_3000"/>
			<xsd:enumeration value="Athlon_64_3800"/>
			<xsd:enumeration value="Athlon_XP_3000"/>
			<xsd:enumeration value="Celeron_T1600"/>
			<xsd:enumeration value="Pentium_M_710"/>
			<xsd:enumeration value="Pentium_M_750"/>
			<xsd:enumeration value="Pentium_G550"/>
			<xsd:enumeration value="Athlon_II_X3_445AIIX3"/>
			<xsd:enumeration value="Celeron_M_410"/>
			<xsd:enumeration value="Core_2_Duo_T5900"/>
			<xsd:enumeration value="Turion_X2_Ultra_ZM_84"/>
			<xsd:enumeration value="Pentium_G570"/>
			<xsd:enumeration value="Celeron_M_340"/>
			<xsd:enumeration value="Pentium_M_735"/>
			<xsd:enumeration value="Pentium_D_820"/>
			<xsd:enumeration value="Turion_II_ULTRA_M660"/>
			<xsd:enumeration value="Pentium_M_740"/>
			<xsd:enumeration value="Turion_64_ML_34"/>
			<xsd:enumeration value="Athlon_64_LE_1660"/>
			<xsd:enumeration value="Athlon_64_LE_1600"/>
			<xsd:enumeration value="Athlon_II_X3_415E"/>
			<xsd:enumeration value="Celeron_M_330"/>
			<xsd:enumeration value="Sempron_M_3100"/>
			<xsd:enumeration value="Celeron_D_345"/>
			<xsd:enumeration value="Celeron_540"/>
			<xsd:enumeration value="Pentium_E6500"/>
			<xsd:enumeration value="Turion_64_MT_28"/>
			<xsd:enumeration value="Pentium_D_940"/>
			<xsd:enumeration value="Celeron_M_370"/>
			<xsd:enumeration value="Core_Duo_T2300"/>
			<xsd:enumeration value="Core_2_Duo_T9800"/>
			<xsd:enumeration value="Turion_II_ULTRA_M600"/>
			<xsd:enumeration value="Pentium_M_A110"/>
			<xsd:enumeration value="Pentium_T2410"/>
			<xsd:enumeration value="Celeron_M_320"/>
			<xsd:enumeration value="Turion_64_ML_32"/>
			<xsd:enumeration value="Core_2_Duo_E4700"/>
			<xsd:enumeration value="Core_Duo_T2600"/>
			<xsd:enumeration value="Pentium_M_725A"/>
			<xsd:enumeration value="Celeron_M_380"/>
			<xsd:enumeration value="Pentium_G662"/>
			<xsd:enumeration value="Mobile_Pentium_4_538"/>
			<xsd:enumeration value="Core_2_Duo_E6700"/>
			<xsd:enumeration value="Pentium_G540"/>
			<xsd:enumeration value="Core_2_Duo_U7600"/>
			<xsd:enumeration value="Pentium_D_915"/>
			<xsd:enumeration value="Turion_64_MT_37"/>
			<xsd:enumeration value="Celeron_M_430"/>
			<xsd:enumeration value="Celeron_D_326"/>
			<xsd:enumeration value="Opteron_Quad_1354"/>
			<xsd:enumeration value="Sempron_3200"/>
			<xsd:enumeration value="Pentium_D_830"/>
			<xsd:enumeration value="Athlon_64_X2_QL_65"/>
			<xsd:enumeration value="Core_Solo_T1300"/>
			<xsd:enumeration value="Pentium_G517"/>
			<xsd:enumeration value="Sempron_M_3000"/>
			<xsd:enumeration value="Celeron_D_346"/>
			<xsd:enumeration value="Sempron_M_2600"/>
			<xsd:enumeration value="Athlon_64_X2_TK_55"/>
			<xsd:enumeration value="Sempron_3400"/>
			<xsd:enumeration value="Athlon_64_X2_4600"/>
			<xsd:enumeration value="Turion_64_MT_30"/>
			<xsd:enumeration value="Core_I3_2330E"/>
			<xsd:enumeration value="Athlon_64_3400"/>
			<xsd:enumeration value="Turion_64_ML_28"/>
			<xsd:enumeration value="Celeron_330"/>
			<xsd:enumeration value="Athlon_64_3200"/>
			<xsd:enumeration value="C7_M_772_VIA"/>
			<xsd:enumeration value="Pentium_D_945"/>
			<xsd:enumeration value="Pentium_M_760"/>
			<xsd:enumeration value="Pentium_M_745"/>
			<xsd:enumeration value="Athlon_64_L110"/>
			<xsd:enumeration value="Pentium_M_753"/>
			<xsd:enumeration value="Pentium_M_770"/>
			<xsd:enumeration value="Pentium_M_733"/>
			<xsd:enumeration value="Core_Duo_T2350"/>
			<xsd:enumeration value="Core_2_Duo_E8300"/>
			<xsd:enumeration value="Core_Solo_T1200"/>
			<xsd:enumeration value="Core_2_Duo_L7200"/>
			<xsd:enumeration value="Pentium_G519"/>
			<xsd:enumeration value="Core_Duo_T2050"/>
			<xsd:enumeration value="Pentium_G515"/>
			<xsd:enumeration value="Pentium_D_920"/>
			<xsd:enumeration value="Turion_64_MT_32"/>
			<xsd:enumeration value="Pentium_G631"/>
			<xsd:enumeration value="Turion_64_ML_37"/>
			<xsd:enumeration value="Pentium_D_930"/>
			<xsd:enumeration value="Pentium_G641"/>
			<xsd:enumeration value="Pentium_G650"/>
			<xsd:enumeration value="Celeron_M_420"/>
			<xsd:enumeration value="Celeron_D_335"/>
			<xsd:enumeration value="Pentium_G531"/>
			<xsd:enumeration value="Mobile_Pentium_4_532"/>
			<xsd:enumeration value="Turion_64_MK_38"/>
			<xsd:enumeration value="Pentium_G560"/>
			<xsd:enumeration value="Pentium_D_805"/>
			<xsd:enumeration value="Sempron_64_3000"/>
			<xsd:enumeration value="Pentium_D_950"/>
			<xsd:enumeration value="Pentium_G660"/>
			<xsd:enumeration value="V_SERIES_V160"/>
			<xsd:enumeration value="C_SERIES_C_60"/>
			<xsd:enumeration value="Turion_64_ML_30"/>
			<xsd:enumeration value="C_SERIES_C_30"/>
			<xsd:enumeration value="Athlon_X2_4050E"/>
			<xsd:enumeration value="Sempron_3300"/>
			<xsd:enumeration value="Celeron_M_350"/>
			<xsd:enumeration value="Celeron_M_390"/>
			<xsd:enumeration value="Pentium_G520"/>
			<xsd:enumeration value="Athlon_64_LE_1640"/>
			<xsd:enumeration value="Xeon_W3520"/>
			<xsd:enumeration value="Celeron_B800"/>
			<xsd:enumeration value="Sempron_3100"/>
			<xsd:enumeration value="Pentium_M_725"/>
			<xsd:enumeration value="Core_Solo_T1350"/>
			<xsd:enumeration value="Athlon_64_3700"/>
			<xsd:enumeration value="Celeron_M_360"/>
			<xsd:enumeration value="Pentium_G516"/>
			<xsd:enumeration value="Celeron_T3100"/>
			<xsd:enumeration value="Core_Duo_T2300E"/>
			<xsd:enumeration value="Phenom_II_X4_N960"/>
			<xsd:enumeration value="Pentium_M_730"/>
			<xsd:enumeration value="Core_Solo_U1400"/>
			<xsd:enumeration value="Pentium_M_715"/>
			<xsd:enumeration value="Celeron_D_340"/>
			<xsd:enumeration value="Sempron_210U"/>
			<xsd:enumeration value="Core_Solo_U1300"/>
			<xsd:enumeration value="Atom_N435"/>
			<xsd:enumeration value="Core_2_Duo_T7600"/>
			<xsd:enumeration value="Celeron_D_325"/>
			<xsd:enumeration value="Turion_64_X2_TL_68"/>
			<xsd:enumeration value="Celeron_G530"/>
			<xsd:enumeration value="Core_Duo_T2500"/>
			<xsd:enumeration value="Celeron_D_336"/>
			<xsd:enumeration value="Core_i7_3840QM"/>
			<xsd:enumeration value="Core_i3_3227U"/>
			<xsd:enumeration value="Core_i5_3337U"/>
			<xsd:enumeration value="Core_i7_3537U"/>
			<xsd:enumeration value="Core_i5_3230M"/>
			<xsd:enumeration value="Vision_A10_Quad_Core_APU"/>
			<xsd:enumeration value="Pentium_G870"/>
			<xsd:enumeration value="Core_i5_3350P"/>
			<xsd:enumeration value="Atom_Z2760"/>
			<xsd:enumeration value="Celeron_847"/>
			<xsd:enumeration value="Core_i3_2365M"/>
			<xsd:enumeration value="Exynos_5000_Series"/>
			<xsd:enumeration value="Core_i7_3632QM"/>
			<xsd:enumeration value="Core_i3_2328M"/>
			<xsd:enumeration value="Core_i7_3630QM"/>
			<xsd:enumeration value="Pentium_B980"/>
			<xsd:enumeration value="Core_i5_3330"/>
			<xsd:enumeration value="Core_i3_3220"/>
			<xsd:enumeration value="Celeron_B830"/>
			<xsd:enumeration value="Pentium_G645"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_3850"/>
			<xsd:enumeration value="ARM_Cortex_A_9"/>
			<xsd:enumeration value="Celeron_877"/>
			<xsd:enumeration value="A_Series_Eight_Core_A10_4600M"/>
			<xsd:enumeration value="Core_i3_2130"/>
			<xsd:enumeration value="ARM_Cortex_A5"/>
			<xsd:enumeration value="A_Series_Tri_Core_A6_3500"/>
			<xsd:enumeration value="Core_i5_2380P"/>
			<xsd:enumeration value="Celeron_B820"/>
			<xsd:enumeration value="Pentium_G640"/>
			<xsd:enumeration value="A_Series_Quad_Core_A10_5700"/>
			<xsd:enumeration value="Celeron_G540"/>
			<xsd:enumeration value="Atom_D2550"/>
			<xsd:enumeration value="cortex_a7"/>
			<xsd:enumeration value="Snapdragon_S5"/>
			<xsd:enumeration value="core_i7_3517um"/>
			<xsd:enumeration value="Core_i3_3217U"/>
			<xsd:enumeration value="OMAP_5400"/>
			<xsd:enumeration value="tegra_4"/>
			<xsd:enumeration value="Celeron_867"/>
			<xsd:enumeration value="OMAP_3500"/>
			<xsd:enumeration value="OMAP_3600"/>
			<xsd:enumeration value="Snapdragon_s2"/>
			<xsd:enumeration value="novathor_l9000"/>
			<xsd:enumeration value="Exynos_4200"/>
			<xsd:enumeration value="Snapdragon_S3"/>
			<xsd:enumeration value="nova_a9000"/>
			<xsd:enumeration value="OMAP_4400"/>
			<xsd:enumeration value="Snapdragon_S4"/>
			<xsd:enumeration value="core_i3_2370M"/>
			<xsd:enumeration value="Snapdragon_s1"/>
			<xsd:enumeration value="apple_a5x"/>
			<xsd:enumeration value="OMAP_3400"/>
			<xsd:enumeration value="Pentium_B820"/>
			<xsd:enumeration value="Core_i3_3110M"/>
			<xsd:enumeration value="core_i3_2377m"/>
			<xsd:enumeration value="Pentium_B967"/>
			<xsd:enumeration value="Exynos_5400"/>
			<xsd:enumeration value="novathor_l8000"/>
			<xsd:enumeration value="Exynos_5200"/>
			<xsd:enumeration value="Exynos_4400"/>
			<xsd:enumeration value="Celeron_B840"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_5500"/>
			<xsd:enumeration value="FX_Series_Eigth_Core_FX_8320"/>
			<xsd:enumeration value="E_Series_Dual_Core_E1_1200"/>
			<xsd:enumeration value="A_Series_Dual_Core_A6_3620"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4_4300M"/>
			<xsd:enumeration value="FX_Series_Eight_Core_FX_8350"/>
			<xsd:enumeration value="FX_Series_Six_Core_FX_6300"/>
			<xsd:enumeration value="A_Series_Dual_Core_A6_5400K"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4_4355M"/>
			<xsd:enumeration value="FX_Series_Quad_Core_FX_4320"/>
			<xsd:enumeration value="A_Series_Dual_Core_A6_4400M"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4_3305"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_4500M"/>
			<xsd:enumeration value="Core_i7_3615QM"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4_3420"/>
			<xsd:enumeration value="A_Series_Dual_Core_A6_4455M"/>
			<xsd:enumeration value="E_Series_Dual_Core_E3_3200"/>
			<xsd:enumeration value="FX_Series_Six_Core_FX_6200"/>
			<xsd:enumeration value="A_Series_Quad_Core_A6_3430MX"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_3520M"/>
			<xsd:enumeration value="FX_Series_Quad_Core_FX_4120"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4_5300"/>
			<xsd:enumeration value="E_Series_Dual_Core_E2_3000"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_5600K"/>
			<xsd:enumeration value="A_Series_Quad_Core_A10_4655M"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8_4555M"/>
			<xsd:enumeration value="A_Series_Eight_Core_A10_5800K"/>
			<xsd:enumeration value="A_Series_Quad_Core_A10_4600M"/>
			<xsd:enumeration value="A_Series_Eight_Core_A10_5700"/>
			<xsd:enumeration value="E_Series_Dual_Core_E2_1800"/>
			<xsd:enumeration value="A_Series_Dual_Core_A6_3650"/>
			<xsd:enumeration value="Pentium_G860"/>
			<xsd:enumeration value="Core_i7_3517U"/>
			<xsd:enumeration value="Core_i5_3550"/>
			<xsd:enumeration value="Core_i7_3520M"/>
			<xsd:enumeration value="Pentium_G630"/>
			<xsd:enumeration value="Pentium_B970"/>
			<xsd:enumeration value="Core_i7_3667U"/>
			<xsd:enumeration value="Core_i5_3470T"/>
			<xsd:enumeration value="Pentium_B960"/>
			<xsd:enumeration value="Core_i7_3770T"/>
			<xsd:enumeration value="Core_i5_2467M"/>
			<xsd:enumeration value="Core_i7_3610QM"/>
			<xsd:enumeration value="Core_i5_3570T"/>
			<xsd:enumeration value="Core_i7_3770S"/>
			<xsd:enumeration value="Pentium_G630T"/>
			<xsd:enumeration value="Core_i3_2390T"/>
			<xsd:enumeration value="Core_i7_3770K"/>
			<xsd:enumeration value="Core_i7_3820QM"/>
			<xsd:enumeration value="Core_i5_3450S"/>
			<xsd:enumeration value="Core_i5_3360M"/>
			<xsd:enumeration value="Core_i5_3210M"/>
			<xsd:enumeration value="Pentium_997"/>
			<xsd:enumeration value="Core_i7_3920XM"/>
			<xsd:enumeration value="Core_i5_3317U"/>
			<xsd:enumeration value="Core_i3_2120T"/>
			<xsd:enumeration value="Core_i5_3427U"/>
			<xsd:enumeration value="Core_i5_3320M"/>
			<xsd:enumeration value="Core_i5_3570K"/>
			<xsd:enumeration value="Core_i5_3450"/>
			<xsd:enumeration value="Core_i5_3550S"/>
			<xsd:enumeration value="Core_i7_3720QM"/>
			<xsd:enumeration value="Core_i7_3770"/>
			<xsd:enumeration value="Core_i7_3612QM"/>
			<xsd:enumeration value="tegra_ap_2600"/>
			<xsd:enumeration value="omap4470"/>
			<xsd:enumeration value="Snapdragon_MSM8260A"/>
			<xsd:enumeration value="Snapdragon_S3_MSM8260"/>
			<xsd:enumeration value="Snapdragon_S3_MSM8660"/>
			<xsd:enumeration value="apple_a4"/>
			<xsd:enumeration value="Snapdragon_S1_MSM7225"/>
			<xsd:enumeration value="Core_i5_2435M"/>
			<xsd:enumeration value="Snapdragon_S3_APQ8060"/>
			<xsd:enumeration value="Snapdragon_S4_APQ8064"/>
			<xsd:enumeration value="tegra_2_t20"/>
			<xsd:enumeration value="Exynos_4210"/>
			<xsd:enumeration value="tegra_2_t25"/>
			<xsd:enumeration value="Snapdragon_S2_MSM7230"/>
			<xsd:enumeration value="Atom_N2800"/>
			<xsd:enumeration value="brazos_e450"/>
			<xsd:enumeration value="Core_i5_2557M"/>
			<xsd:enumeration value="omap3630"/>
			<xsd:enumeration value="omap4430"/>
			<xsd:enumeration value="omap4460"/>
			<xsd:enumeration value="Exynos_3110"/>
			<xsd:enumeration value="apple_a5"/>
			<xsd:enumeration value="apple_a6"/>
			<xsd:enumeration value="tegra_650"/>
			<xsd:enumeration value="tegra_ap_2500"/>
			<xsd:enumeration value="a_series_dual_core_a4_3305m"/>
			<xsd:enumeration value="tegra_600"/>
			<xsd:enumeration value="omap3620"/>
			<xsd:enumeration value="omap5432"/>
			<xsd:enumeration value="Snapdragon_S4_MSM8230"/>
			<xsd:enumeration value="Snapdragon_S2_MSM8225"/>
			<xsd:enumeration value="Snapdragon_S4_MSM8270"/>
			<xsd:enumeration value="Snapdragon_S1_QSD8650"/>
			<xsd:enumeration value="Core_i7_2620M"/>
			<xsd:enumeration value="omap5430"/>
			<xsd:enumeration value="a_series_quad_core_a6_3420m"/>
			<xsd:enumeration value="Core_i3_2350M"/>
			<xsd:enumeration value="Core_i5_2450M"/>
			<xsd:enumeration value="Core_i7_2670QM"/>
			<xsd:enumeration value="Core_i5_2430M"/>
			<xsd:enumeration value="pentium_g530"/>
			<xsd:enumeration value="i3_2370m"/>
			<xsd:enumeration value="atom_n2600"/>
			<xsd:enumeration value="i3_2367m"/>
			<xsd:enumeration value="i7_2640m"/>
			<xsd:enumeration value="i5_2450m"/>
			<xsd:enumeration value="i3_2367"/>
			<xsd:enumeration value="atom_d2700"/>
			<xsd:enumeration value="i5_2320"/>
			<xsd:enumeration value="i7_2670qm"/>
			<xsd:enumeration value="celeron_b815"/>
			<xsd:enumeration value="i3_2350m"/>
			<xsd:enumeration value="i5_2430m"/>
			<xsd:enumeration value="i7_2675qm"/>
			<xsd:enumeration value="i7_2677m"/>
			<xsd:enumeration value="a_series_quad_core_a8_3500m"/>
			<xsd:enumeration value="a_series_quad_core_a6_3600m"/>
			<xsd:enumeration value="i7_3960x"/>
			<xsd:enumeration value="c_series_dual_core_c_60"/>
			<xsd:enumeration value="e_series_dual_core_e_300"/>
			<xsd:enumeration value="a_series_quad_core_a6_3400m"/>
			<xsd:enumeration value="a_series_quad_core_a6_3410m"/>
			<xsd:enumeration value="i7_2700k"/>
			<xsd:enumeration value="c_series_dual_core_c_50"/>
			<xsd:enumeration value="z_series_dual_core_z_01"/>
			<xsd:enumeration value="i7_3930k"/>
			<xsd:enumeration value="i7_2637m"/>
			<xsd:enumeration value="a_series_dual_core_a4_3400m"/>
			<xsd:enumeration value="e_series_single_core_e_240"/>
			<xsd:enumeration value="i7_3820"/>
			<xsd:enumeration value="phenom_ii_x4_quad_core_n970"/>
			<xsd:enumeration value="a_series_dual_core_a4_3310m"/>
			<xsd:enumeration value="i5_2467m"/>
			<xsd:enumeration value="arm_dual_core_cortex_a9_omap_4"/>
			<xsd:enumeration value="e_series_dual_core_e_450"/>
			<xsd:enumeration value="a_series_dual_core_a4_3300m"/>
			<xsd:enumeration value="ARM_9_2818"/>
			<xsd:enumeration value="ARM_11_Telechips_8902"/>
			<xsd:enumeration value="ARM_11_2818"/>
			<xsd:enumeration value="ARM_11_iMAPX210"/>
			<xsd:enumeration value="ARM_11_iMAP210"/>
			<xsd:enumeration value="ARM_11_ROCK_2818"/>
			<xsd:enumeration value="8803_CORTEX_A8"/>
			<xsd:enumeration value="ARM_11_8902"/>
			<xsd:enumeration value="Core_2_Duo_2.66GHz"/>
			<xsd:enumeration value="Turion_64_X2_RM_72"/>
			<xsd:enumeration value="Core_i5_430M_"/>
			<xsd:enumeration value="Celeron_485"/>
			<xsd:enumeration value="Core_2_Duo_SL9300_"/>
			<xsd:enumeration value="Quad_Core_2.26GHz"/>
			<xsd:enumeration value="Core_i5_2400"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_B75"/>
			<xsd:enumeration value="Core_i5_2405S"/>
			<xsd:enumeration value="Core_2_Duo_T6500"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_5000_plus_"/>
			<xsd:enumeration value="Celeron_D_360"/>
			<xsd:enumeration value="Core_2_Duo_2.13GHz"/>
			<xsd:enumeration value="Core_2_Duo_1.83GHz"/>
			<xsd:enumeration value="Core_Duo_T2450"/>
			<xsd:enumeration value="Atom_230"/>
			<xsd:enumeration value="Turion_64_X2_ZM_82"/>
			<xsd:enumeration value="Turion_X2_ZM_82"/>
			<xsd:enumeration value="Pentium_P6300"/>
			<xsd:enumeration value="Pentium_957"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_B95"/>
			<xsd:enumeration value="Pentium_B940"/>
			<xsd:enumeration value="Core_i5_540UM"/>
			<xsd:enumeration value="Turion_X2_RM_75"/>
			<xsd:enumeration value="Core_2_Duo_2.5GHz"/>
			<xsd:enumeration value="Core_i3_2105"/>
			<xsd:enumeration value="A_Series_Dual_Core_A4"/>
			<xsd:enumeration value="Atom_Z520"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_260"/>
			<xsd:enumeration value="Athlon_II_X3_Triple_Core_450"/>
			<xsd:enumeration value="Core_i3_3.2_GHz_"/>
			<xsd:enumeration value="Core_i5_2500K"/>
			<xsd:enumeration value="Core_2_Duo_1.66GHz"/>
			<xsd:enumeration value="Core_i3_2357M"/>
			<xsd:enumeration value="Turion_64_X2_TL_56"/>
			<xsd:enumeration value="Turion_X2_Ultra_ZM_85"/>
			<xsd:enumeration value="Core_i3_2120"/>
			<xsd:enumeration value="Core_i5_2.8GHz"/>
			<xsd:enumeration value="Core_2_Duo_2.1GHz"/>
			<xsd:enumeration value="Celeron_P4500"/>
			<xsd:enumeration value="Pentium_4_360"/>
			<xsd:enumeration value="Core_i5_2390T"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_N530"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_QL_64"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_235e_"/>
			<xsd:enumeration value="Athlon_Dual_Core"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_620_"/>
			<xsd:enumeration value="Core_i5_520M"/>
			<xsd:enumeration value="Pentium_G620T"/>
			<xsd:enumeration value="Core_i7_2.7_GHz"/>
			<xsd:enumeration value="Celeron_M_ULV"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_M520_"/>
			<xsd:enumeration value="Core_i7_2617M"/>
			<xsd:enumeration value="Core_2_Duo_2.33GHz"/>
			<xsd:enumeration value="Pentium_U3600"/>
			<xsd:enumeration value="Turion_X2_Ultra_ZM_82"/>
			<xsd:enumeration value="Pentium_G840"/>
			<xsd:enumeration value="Quad_Core_Xeon_2.8_GHz_"/>
			<xsd:enumeration value="Core_2_Duo_1.6GHz"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_220"/>
			<xsd:enumeration value="A_Series_Quad_Core_A8"/>
			<xsd:enumeration value="Core_i7_2600K"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_240e_"/>
			<xsd:enumeration value="Core_2_Duo_2.2GHz"/>
			<xsd:enumeration value="Core_i3_2100_"/>
			<xsd:enumeration value="Core_i5_470UM_"/>
			<xsd:enumeration value="Core_i7_2410M"/>
			<xsd:enumeration value="Core_2_Duo_1.4GHz_"/>
			<xsd:enumeration value="ARM_7500fe"/>
			<xsd:enumeration value="Core_i7_980X"/>
			<xsd:enumeration value="Core_i5_2537M"/>
			<xsd:enumeration value="Turion_64_X2_RM_70"/>
			<xsd:enumeration value="Pentium_G6960"/>
			<xsd:enumeration value="Pentium_M_ULV"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_B24"/>
			<xsd:enumeration value="Xeon_E5507"/>
			<xsd:enumeration value="Pentium_G850"/>
			<xsd:enumeration value="Core_2_Duo_1.8GHz"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_910_"/>
			<xsd:enumeration value="Celeron_E3400"/>
			<xsd:enumeration value="Core_2_Duo_2.16GHz"/>
			<xsd:enumeration value="Core_i5_2457M"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_QL_62"/>
			<xsd:enumeration value="Core_i7_820QM"/>
			<xsd:enumeration value="Core_i5_560UM"/>
			<xsd:enumeration value="Turion_X2_Ultra_ZM_87"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_B26"/>
			<xsd:enumeration value="Atom_N455"/>
			<xsd:enumeration value="Athlon_II_X4Quad_Core_600e"/>
			<xsd:enumeration value="Core_i5_540M"/>
			<xsd:enumeration value="Celeron_D_420"/>
			<xsd:enumeration value="Core_i3_3.06_GHz_"/>
			<xsd:enumeration value="Quad_Core_Xeon"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core"/>
			<xsd:enumeration value="Atom_N570"/>
			<xsd:enumeration value="Core_i5_2310"/>
			<xsd:enumeration value="Core_2_Due_P8400"/>
			<xsd:enumeration value="Atom_330"/>
			<xsd:enumeration value="Pentium_T2060"/>
			<xsd:enumeration value="ARM_710a"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_TK_57"/>
			<xsd:enumeration value="Core_Duo_T2400__"/>
			<xsd:enumeration value="Atom_Z670"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_645"/>
			<xsd:enumeration value="Core_2_Duo_2.26GHz"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_840T_"/>
			<xsd:enumeration value="Core_2_Duo_T6670_"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_64"/>
			<xsd:enumeration value="Turion_Neo_X2_Dual_Core_L625"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_245e_"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_TK_53"/>
			<xsd:enumeration value="Athlon_64_X2_Dual_Core_TK_42_"/>
			<xsd:enumeration value="A_Series_Quad_Core_A6"/>
			<xsd:enumeration value="Turion_II_X2_Dual_Core_M620"/>
			<xsd:enumeration value="A110"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_511_"/>
			<xsd:enumeration value="Core_i5_2.3_GHz"/>
			<xsd:enumeration value="Core_i5_2400s"/>
			<xsd:enumeration value="Core_2_Duo_T6600_"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_P650"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_260u"/>
			<xsd:enumeration value="Core_2_Duo_2.0GHz"/>
			<xsd:enumeration value="Core_i7_720QM"/>
			<xsd:enumeration value="Turion_X2_ZM_80"/>
			<xsd:enumeration value="Phenom_II_X6_Six_Core_1100T"/>
			<xsd:enumeration value="Core_i5_2410M"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_610e_"/>
			<xsd:enumeration value="Athon_II_X2_Dual_Core_P360"/>
			<xsd:enumeration value="Core_i7_2600S"/>
			<xsd:enumeration value="Core_i7_660UM"/>
			<xsd:enumeration value="Core_2_Duo_2.93GHz"/>
			<xsd:enumeration value="Turion_64_X2_TL_52"/>
			<xsd:enumeration value="Core_i3_390M"/>
			<xsd:enumeration value="Celeron_T3500"/>
			<xsd:enumeration value="Turion_64_X2_TL_64"/>
			<xsd:enumeration value="Core_i7_2657M"/>
			<xsd:enumeration value="Athlon_64_X2_Pro_L310"/>
			<xsd:enumeration value="Atom_Z540"/>
			<xsd:enumeration value="Atom_Z550"/>
			<xsd:enumeration value="Core_2_Duo_2.8GHz"/>
			<xsd:enumeration value="Quad_Core_Xeon_2.4GHz_"/>
			<xsd:enumeration value="Quad_Core_Q9000"/>
			<xsd:enumeration value="Athlon_II_X3_Triple_Core_440"/>
			<xsd:enumeration value="Core_2_Duo_SU9600"/>
			<xsd:enumeration value="ARM_610"/>
			<xsd:enumeration value="Core_2_Duo_1.86GHz_"/>
			<xsd:enumeration value="Core_i5_2500"/>
			<xsd:enumeration value="Core_i7_2.2_GHz"/>
			<xsd:enumeration value="Celeron_D_440"/>
			<xsd:enumeration value="Core_2_Duo_1.86GHz"/>
			<xsd:enumeration value="Athlon_II_X4_Quad_Core_610e"/>
			<xsd:enumeration value="Core_i5_2500S"/>
			<xsd:enumeration value="Celeron_E3500"/>
			<xsd:enumeration value="Core_i5_2500T"/>
			<xsd:enumeration value="Core_Duo_1.83GHz"/>
			<xsd:enumeration value="Atom_N450"/>
			<xsd:enumeration value="ARM_710t"/>
			<xsd:enumeration value="Core_i7_2.0_GHz"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_9100E_"/>
			<xsd:enumeration value="Xeon_Dual_Core"/>
			<xsd:enumeration value="Pentium_847"/>
			<xsd:enumeration value="Atom_Z530"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_M300"/>
			<xsd:enumeration value="ARM_710"/>
			<xsd:enumeration value="Athlon_II_X2_Dual_Core_B22"/>
			<xsd:enumeration value="Core_2_Duo_3.06GHz"/>
			<xsd:enumeration value="Core_i3_330M"/>
			<xsd:enumeration value="Phenom_II_X4_Quad_Core_840"/>
			<xsd:enumeration value="Phenom_II_X2_Dual_Core_250"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_L335"/>
			<xsd:enumeration value="Pentium_G620"/>
			<xsd:enumeration value="Tablet_Processor"/>
			<xsd:enumeration value="Atom_N450_"/>
			<xsd:enumeration value="Core_2_Duo_2.4GHz"/>
			<xsd:enumeration value="C_Series_Single_Core_C_30"/>
			<xsd:enumeration value="Pentium_B950"/>
			<xsd:enumeration value="Core_2_Duo_2.53GHz"/>
			<xsd:enumeration value="Core_i3_2310M"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_QL_60"/>
			<xsd:enumeration value="Athlon_X2_Dual_Core_3250e"/>
			<xsd:enumeration value="Celeron_763"/>
			<xsd:enumeration value="intel_atom_z550"/>
			<xsd:enumeration value="intel_atom_z510"/>
			<xsd:enumeration value=""/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BaseCurrencyAmount">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="20"/>
			<xsd:fractionDigits value="2" fixed="true"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BasePositiveCurrencyAmount">
		<xsd:restriction base="xsd:decimal">
			<xsd:maxInclusive value="99999999.99"/>
			<xsd:minInclusive value="0.00"/>
			<xsd:totalDigits value="10"/>
			<xsd:fractionDigits value="2" fixed="true"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BasePriceCurrencyAmount">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="20"/>
			<xsd:fractionDigits value="4" fixed="true"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<xsd:simpleType name="StringBasePriceCurrencyAmount">
		<xsd:restriction base="xsd:string">
		     <xsd:maxLength value="24"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="DatedPrice">
		<xsd:sequence>
			<xsd:element name="StartDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:element name="EndDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:choice>
				<xsd:element name="Price" type="CurrencyAmount"/>
				<xsd:element name="PreviousPrice" type="CurrencyAmount"/>
			</xsd:choice>
		</xsd:sequence>
		<xsd:attribute name="delete" type="xsd:boolean" use="optional"/>
	</xsd:complexType>
	<xsd:complexType name="DatedCompareAtPrice">
		<xsd:sequence>
			<xsd:element name="StartDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:element name="EndDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:element name="CompareAtPrice" type="CurrencyAmount"/>
		</xsd:sequence>
		<xsd:attribute name="delete" type="xsd:boolean" use="optional"/>
	</xsd:complexType>
	<!--
                
    ##################################################
    # Fulfillment center ID element
    ##################################################
    
	-->
	<xsd:element name="FulfillmentCenterID" type="String"/>
	<!--
		
    ##################################################
    # Fulfillment method element
    ##################################################
    
	-->
	<xsd:element name="FulfillmentMethod">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="Ship"/>
				<xsd:enumeration value="InStorePickup"/>
				<xsd:enumeration value="MerchantDelivery"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Fulfillment service level element
    ##################################################
    
	-->
	<xsd:element name="FulfillmentServiceLevel">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="Standard"/>
				<xsd:enumeration value="Expedited"/>
				<xsd:enumeration value="Scheduled"/>
				<xsd:enumeration value="NextDay"/>
				<xsd:enumeration value="SecondDay"/>
				<xsd:enumeration value="Next"/>
				<xsd:enumeration value="Second"/>
				<xsd:enumeration value="Priority"/>
				<!-- Priority is specific for CN -->
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
    ##################################################
    # Fulfillment readiness condition element
    ##################################################
    -->
	<xsd:simpleType name="FulfillReadiness">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="drop_ship_ready"/>
			<xsd:enumeration value="not_ready"/>
			<xsd:enumeration value="receive_ready"/>
			<xsd:enumeration value="exception_receive_ready"/>
			<xsd:enumeration value="po_ready"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FulfillmentLatencyValues">
		<xsd:annotation>
			<xsd:documentation>
			The below fulfillmentLatencyValues are subjective to long lead time. Any value above 30 has certain restrictions with Producttype and Marketplace used.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:integer">
			<xsd:minInclusive value="0"/>
			<xsd:maxInclusive value="120"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
                
    ##################################################
    # Carrier Code element
    ##################################################
    
	-->
    <xsd:element name="CarrierCode">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
			    <xsd:enumeration value="17FEIA"/>
				<xsd:enumeration value="360lion"/>
				<xsd:enumeration value="4PX"/>
				<xsd:enumeration value="A-1"/>
				<xsd:enumeration value="AAA Cooper"/>
				<xsd:enumeration value="ABF"/>
				<xsd:enumeration value="AFL/Fedex"/>
				<xsd:enumeration value="ALLJOY"/>
				<xsd:enumeration value="AMAUK"/>
				<xsd:enumeration value="AMAZON HORIZON"/>
				<xsd:enumeration value="Amazon Shipping"/>
				<xsd:enumeration value="AMZL"/>
				<xsd:enumeration value="AMZL_UK"/>
				<xsd:enumeration value="Andere"/>
				<xsd:enumeration value="Anjun"/>
				<xsd:enumeration value="AO"/>
				<xsd:enumeration value="AO Deutschland"/>
				<xsd:enumeration value="APC"/>
				<xsd:enumeration value="APC Overnight"/>
				<xsd:enumeration value="APC POSTAL LOGISTICS"/>
				<xsd:enumeration value="APG eCommerce"/>
				<xsd:enumeration value="Aramex"/>
				<xsd:enumeration value="ARAS"/>
				<xsd:enumeration value="Aras Kargo"/>
				<xsd:enumeration value="Arco Spedizioni"/>
				<xsd:enumeration value="Arkas"/>
				<xsd:enumeration value="Arrow XL"/>
				<xsd:enumeration value="Asendia"/>
				<xsd:enumeration value="Asgard"/>
				<xsd:enumeration value="Assett"/>
				<xsd:enumeration value="AT POST"/>
				<xsd:enumeration value="ATS"/>
				<xsd:enumeration value="AUSSIE_POST"/>
				<xsd:enumeration value="Australia Post"/>
				<xsd:enumeration value="Australia Post-ArticleID"/>
				<xsd:enumeration value="Australia Post-Consignment"/>
				<xsd:enumeration value="B2C"/>
				<xsd:enumeration value="B2C Europe"/>
				<xsd:enumeration value="B2CShip"/>
				<xsd:enumeration value="Balnak"/>
				<xsd:enumeration value="Bartolini"/>
				<xsd:enumeration value="Beijing Quanfeng Express"/>
				<xsd:enumeration value="Best Buy"/>
				<xsd:enumeration value="Best Express"/>
				<xsd:enumeration value="Better Trucks"/>
				<xsd:enumeration value="BJS"/>
				<xsd:enumeration value="Blowhorn"/>
				<xsd:enumeration value="Blue Package"/>
				<xsd:enumeration value="BlueDart"/>
				<xsd:enumeration value="Boğaziçi"/>
				<xsd:enumeration value="Bombax"/>
				<xsd:enumeration value="Bombino Express"/>
				<xsd:enumeration value="BPOST"/>
				<xsd:enumeration value="BR1 Express"/>
				<xsd:enumeration value="BRT"/>
				<xsd:enumeration value="Buylogic"/>
				<xsd:enumeration value="Canada Post"/>
				<xsd:enumeration value="Canpar"/>
				<xsd:enumeration value="CargoLine"/>
				<xsd:enumeration value="Caribou"/>
				<xsd:enumeration value="Cart2India"/>
				<xsd:enumeration value="CBL"/>
				<xsd:enumeration value="CDC"/>
				<xsd:enumeration value="CELERITAS"/>
				<xsd:enumeration value="Centex"/>
				<xsd:enumeration value="CEVA"/>
				<xsd:enumeration value="Ceva Lojistik"/>
				<xsd:enumeration value="China Post"/>
				<xsd:enumeration value="Chrono Express"/>
				<xsd:enumeration value="Chronopost"/>
				<xsd:enumeration value="Chukou1"/>
				<xsd:enumeration value="Cititrans"/>
				<xsd:enumeration value="City Link"/>
				<xsd:enumeration value="CityPost"/>
				<xsd:enumeration value="CNE"/>
				<xsd:enumeration value="Coliposte"/>
				<xsd:enumeration value="Colis Privé"/>
				<xsd:enumeration value="Colissimo"/>
				<xsd:enumeration value="Consegna Mezzi Propri"/>
				<xsd:enumeration value="Conway"/>
				<xsd:enumeration value="Correios"/>
				<xsd:enumeration value="Correos"/>
				<xsd:enumeration value="Correos Express"/>
				<xsd:enumeration value="COSCO"/>
				<xsd:enumeration value="CouriersPlease"/>
				<xsd:enumeration value="CTT EXPRESS"/>
				<xsd:enumeration value="Cubyn"/>
				<xsd:enumeration value="DACHSER"/>
				<xsd:enumeration value="DAIPOST"/>
				<xsd:enumeration value="DB Schenker"/>
				<xsd:enumeration value="DEL Deliveries"/>
				<xsd:enumeration value="Delhivery"/>
				<xsd:enumeration value="Delivengo"/>
				<xsd:enumeration value="Delivery Group"/>
				<xsd:enumeration value="Der Kurier"/>
				<xsd:enumeration value="Deutsche Post"/>
				<xsd:enumeration value="DFL"/>
				<xsd:enumeration value="DHL"/>
				<xsd:enumeration value="DHL eCommerce"/>
				<xsd:enumeration value="DHL Express"/>
				<xsd:enumeration value="DHL Freight"/>
				<xsd:enumeration value="DHL Global Mail"/>
				<xsd:enumeration value="DHL Home Delivery"/>
				<xsd:enumeration value="DHL Kargo"/>
				<xsd:enumeration value="DHL-Paket"/>
				<xsd:enumeration value="DHL Parcel UK"/>
				<xsd:enumeration value="DHLPL"/>
				<xsd:enumeration value="Digital Delivery"/>
				<xsd:enumeration value="DirectLog"/>
				<xsd:enumeration value="Dotzot"/>
				<xsd:enumeration value="DPB"/>
				<xsd:enumeration value="DPD"/>
				<xsd:enumeration value="DPD Local"/>
				<xsd:enumeration value="DSV"/>
				<xsd:enumeration value="DTDC"/>
				<xsd:enumeration value="DX"/>
				<xsd:enumeration value="DX Express"/>
				<xsd:enumeration value="DX Freight"/>
				<xsd:enumeration value="DX Secure"/>
				<xsd:enumeration value="DYNAMIC EXPRESS"/>
				<xsd:enumeration value="ECMS"/>
				<xsd:enumeration value="ECMS express"/>
				<xsd:enumeration value="Ecom Express"/>
				<xsd:enumeration value="Ecom Shipping solutions PVT ltd"/>
				<xsd:enumeration value="EINSA SOURCING"/>
				<xsd:enumeration value="EKI Trans"/>
				<xsd:enumeration value="Emirates Post"/>
				<xsd:enumeration value="Emons"/>
				<xsd:enumeration value="Endopack"/>
				<xsd:enumeration value="Energo"/>
				<xsd:enumeration value="Envialia"/>
				<xsd:enumeration value="ePost Global"/>
				<xsd:enumeration value="Equick"/>
				<xsd:enumeration value="Estafeta"/>
				<xsd:enumeration value="Estes"/>
				<xsd:enumeration value="EUB"/>
				<xsd:enumeration value="Europaczka"/>
				<xsd:enumeration value="Exapaq"/>
				<xsd:enumeration value="FAST EST"/>
				<xsd:enumeration value="Fastway"/>
				<xsd:enumeration value="FedEx"/>
				<xsd:enumeration value="Fedex Freight"/>
				<xsd:enumeration value="FEDEX JP"/>
				<xsd:enumeration value="FedEx SmartPost"/>
				<xsd:enumeration value="FERCAM"/>
				<xsd:enumeration value="Fillo Kargo"/>
				<xsd:enumeration value="First Flight"/>
				<xsd:enumeration value="First Flight China"/>
				<xsd:enumeration value="First Mile"/>
				<xsd:enumeration value="Fleetan"/>
				<xsd:enumeration value="Flyt"/>
				<xsd:enumeration value="FRACHTPOST"/>
				<xsd:enumeration value="FRANCE EXPRESS"/>
				<xsd:enumeration value="Gati"/>
				<xsd:enumeration value="GEL"/>
				<xsd:enumeration value="GEL Express"/>
				<xsd:enumeration value="geodis"/>
				<xsd:enumeration value="Geodis Calberson"/>
				<xsd:enumeration value="Geopost Kargo"/>
				<xsd:enumeration value="GFS"/>
				<xsd:enumeration value="GLS"/>
				<xsd:enumeration value="GO!"/>
				<xsd:enumeration value="Gojavas"/>
				<xsd:enumeration value="GRUPO LOGISTIC"/>
				<xsd:enumeration value="Hellmann"/>
				<xsd:enumeration value="Heppner"/>
				<xsd:enumeration value="Hermes"/>
				<xsd:enumeration value="Hermes (Corporate)"/>
				<xsd:enumeration value="Hermes Einrichtungsservice"/>
				<xsd:enumeration value="Hermes Logistik Gruppe"/>
				<xsd:enumeration value="Hermes UK"/>
				<xsd:enumeration value="Hlog"/>
				<xsd:enumeration value="HNC"/>
				<xsd:enumeration value="Home Logistics"/>
				<xsd:enumeration value="honesteye"/>
				<xsd:enumeration value="Hongkong Post"/>
				<xsd:enumeration value="Horoz Lojistik"/>
				<xsd:enumeration value="Hotpoint Logistics"/>
				<xsd:enumeration value="HRP"/>
				<xsd:enumeration value="HS code"/>
				<xsd:enumeration value="HUAHAN Logistics"/>
				<xsd:enumeration value="HubEurope"/>
				<xsd:enumeration value="Hunter Logistics"/>
				<xsd:enumeration value="Huxloe"/>
				<xsd:enumeration value="Huxloe Logistics"/>
				<xsd:enumeration value="ICC Worldwide"/>
				<xsd:enumeration value="IDS"/>
				<xsd:enumeration value="IDS Netzwerk"/>
				<xsd:enumeration value="iMile"/>
				<xsd:enumeration value="India Post"/>
				<xsd:enumeration value="InPost"/>
				<xsd:enumeration value="Interlink"/>
				<xsd:enumeration value="Interno"/>
				<xsd:enumeration value="Intersoft"/>
				<xsd:enumeration value="iParcel"/>
				<xsd:enumeration value="ITD Global"/>
				<xsd:enumeration value="J&amp;T Express"/>
				<xsd:enumeration value="Japan Post"/>
				<xsd:enumeration value="JCEX"/>
				<xsd:enumeration value="Jersey Post"/>
				<xsd:enumeration value="Jiufang"/>
				<xsd:enumeration value="JP EXPRESS"/>
				<xsd:enumeration value="JPL UPU"/>
				<xsd:enumeration value="JS Express"/>
				<xsd:enumeration value="JT"/>
				<xsd:enumeration value="Kargokar"/>
				<xsd:enumeration value="KEAVO"/>
				<xsd:enumeration value="Kerry"/>
				<xsd:enumeration value="Kuehne+Nagel"/>
				<xsd:enumeration value="Kybotech"/>
				<xsd:enumeration value="La Poste"/>
				<xsd:enumeration value="Landmark"/>
				<xsd:enumeration value="Landmark Global"/>
				<xsd:enumeration value="Lasership"/>
				<xsd:enumeration value="Lemonmode"/>
				<xsd:enumeration value="LF Logistic"/>
				<xsd:enumeration value="Liccardi"/>
				<xsd:enumeration value="Liccardi Trasporti"/>
				<xsd:enumeration value="Logistik Gruppe"/>
				<xsd:enumeration value="Loomis"/>
				<xsd:enumeration value="Mail Alliance"/>
				<xsd:enumeration value="Mail Americas"/>
				<xsd:enumeration value="Mainpost"/>
				<xsd:enumeration value="MBE"/>
				<xsd:enumeration value="Metro Kargo"/>
				<xsd:enumeration value="Mezzi propri"/>
				<xsd:enumeration value="MHI"/>
				<xsd:enumeration value="Milkman"/>
				<xsd:enumeration value="MNG Kargo"/>
				<xsd:enumeration value="Mondial Relay"/>
				<xsd:enumeration value="MRW"/>
				<xsd:enumeration value="MZZ-Briefdienst"/>
				<xsd:enumeration value="Nacex"/>
				<xsd:enumeration value="NAQEL EXPRESS"/>
				<xsd:enumeration value="Narpost Kargo"/>
				<xsd:enumeration value="New Zealand Post"/>
				<xsd:enumeration value="Newgistics"/>
				<xsd:enumeration value="Nexive"/>
				<xsd:enumeration value="Ninjavan"/>
				<xsd:enumeration value="NipponExpress"/>
				<xsd:enumeration value="NITTSU"/>
				<xsd:enumeration value="NOVEO"/>
				<xsd:enumeration value="NowBikes"/>
				<xsd:enumeration value="NTL"/>
				<xsd:enumeration value="OCS Worldwide"/>
				<xsd:enumeration value="Old Dominion"/>
				<xsd:enumeration value="OneWorldExpress"/>
				<xsd:enumeration value="ONTIME"/>
				<xsd:enumeration value="OnTrac"/>
				<xsd:enumeration value="OSM"/>
				<xsd:enumeration value="OSM Worldwide"/>
				<xsd:enumeration value="Other"/>
				<xsd:enumeration value="Otro"/>
				<xsd:enumeration value="Overnite Express"/>
				<xsd:enumeration value="Palletline"/>
				<xsd:enumeration value="Palletways"/>
				<xsd:enumeration value="Panther"/>
				<xsd:enumeration value="Parcel Hub"/>
				<xsd:enumeration value="Parcel Monkey"/>
				<xsd:enumeration value="Parcel Station"/>
				<xsd:enumeration value="Parcel2go"/>
				<xsd:enumeration value="ParcelDenOnline"/>
				<xsd:enumeration value="Parcelforce"/>
				<xsd:enumeration value="Parcelhub"/>
				<xsd:enumeration value="Parcelink Logistics"/>
				<xsd:enumeration value="Parcelnet"/>
				<xsd:enumeration value="ParcelOne"/>
				<xsd:enumeration value="PDC Logistics"/>
				<xsd:enumeration value="Pilot"/>
				<xsd:enumeration value="Pilot Freight"/>
				<xsd:enumeration value="PIN"/>
				<xsd:enumeration value="Polish Post"/>
				<xsd:enumeration value="Pos Laju"/>
				<xsd:enumeration value="Post Modern"/>
				<xsd:enumeration value="Poste Italiane"/>
				<xsd:enumeration value="PostNL"/>
				<xsd:enumeration value="PostNord"/>
				<xsd:enumeration value="Professional"/>
				<xsd:enumeration value="PTT Kargo"/>
				<xsd:enumeration value="PUROLATOR"/>
				<xsd:enumeration value="Qxpress"/>
				<xsd:enumeration value="R+L"/>
				<xsd:enumeration value="Raben Group"/>
				<xsd:enumeration value="RBNA"/>
				<xsd:enumeration value="REDUR"/>
				<xsd:enumeration value="Rhenus"/>
				<xsd:enumeration value="Rieck"/>
				<xsd:enumeration value="Rivigo"/>
				<xsd:enumeration value="RMLGB"/>
				<xsd:enumeration value="Roadrunner"/>
				<xsd:enumeration value="Royal Mail"/>
				<xsd:enumeration value="RR Donnelley"/>
				<xsd:enumeration value="Safexpress"/>
				<xsd:enumeration value="SAGAWA"/>
				<xsd:enumeration value="SAGAWA EXPRESS"/>
				<xsd:enumeration value="Saia"/>
				<xsd:enumeration value="Sailpost"/>
				<xsd:enumeration value="Schweizer Post"/>
				<xsd:enumeration value="SDA"/>
				<xsd:enumeration value="Seino"/>
				<xsd:enumeration value="SEINO TRANSPORTATION"/>
				<xsd:enumeration value="Seko Logistics"/>
				<xsd:enumeration value="Selem Kargo"/>
				<xsd:enumeration value="Self Delivery"/>
				<xsd:enumeration value="Sendcloud"/>
				<xsd:enumeration value="Sending"/>
				<xsd:enumeration value="SENDLE"/>
				<xsd:enumeration value="Seur"/>
				<xsd:enumeration value="Seven Senders"/>
				<xsd:enumeration value="SF Express"/>
				<xsd:enumeration value="SFC"/>
				<xsd:enumeration value="Ship Delight"/>
				<xsd:enumeration value="Ship Global US"/>
				<xsd:enumeration value="SHIPA"/>
				<xsd:enumeration value="ShipEconomy"/>
				<xsd:enumeration value="ShipGlobal"/>
				<xsd:enumeration value="Shipmate"/>
				<xsd:enumeration value="Shippit"/>
				<xsd:enumeration value="Shree Maruti Courier"/>
				<xsd:enumeration value="Shree Tirupati Courier"/>
				<xsd:enumeration value="Shunfeng Express"/>
				<xsd:enumeration value="Singapore Post"/>
				<xsd:enumeration value="Sinotrans"/>
				<xsd:enumeration value="Skypostal"/>
				<xsd:enumeration value="Smartmail"/>
				<xsd:enumeration value="Smartrlogistics"/>
				<xsd:enumeration value="SMSA"/>
				<xsd:enumeration value="SMSA Express"/>
				<xsd:enumeration value="Soarmall"/>
				<xsd:enumeration value="Sonstige"/>
				<xsd:enumeration value="South Eastern Freight Lines"/>
				<xsd:enumeration value="Speedaf"/>
				<xsd:enumeration value="Speedex"/>
				<xsd:enumeration value="Spoton"/>
				<xsd:enumeration value="SPRING"/>
				<xsd:enumeration value="spring GDS"/>
				<xsd:enumeration value="Sprint"/>
				<xsd:enumeration value="Stahlmann and Sachs"/>
				<xsd:enumeration value="Stampit"/>
				<xsd:enumeration value="Staples"/>
				<xsd:enumeration value="StarTrack-ArticleID"/>
				<xsd:enumeration value="StarTrack-Consignment"/>
				<xsd:enumeration value="STG"/>
				<xsd:enumeration value="STO Express"/>
				<xsd:enumeration value="Streamlite"/>
				<xsd:enumeration value="Sunyou"/>
				<xsd:enumeration value="Sürat Kargo"/>
				<xsd:enumeration value="Susa"/>
				<xsd:enumeration value="Swiss post"/>
				<xsd:enumeration value="Szendex"/>
				<xsd:enumeration value="Target"/>
				<xsd:enumeration value="TDN"/>
				<xsd:enumeration value="Tezel Lojistik"/>
				<xsd:enumeration value="The Delivery Group"/>
				<xsd:enumeration value="The Professional Couriers"/>
				<xsd:enumeration value="TIPSA"/>
				<xsd:enumeration value="TNT"/>
				<xsd:enumeration value="TNT Kargo"/>
				<xsd:enumeration value="TNTIT"/>
				<xsd:enumeration value="Toll"/>
				<xsd:enumeration value="Toll Global Express"/>
				<xsd:enumeration value="Tongda Global"/>
				<xsd:enumeration value="Topyou"/>
				<xsd:enumeration value="Total Express"/>
				<xsd:enumeration value="Tourline"/>
				<xsd:enumeration value="Trackon"/>
				<xsd:enumeration value="Trakpak"/>
				<xsd:enumeration value="Transaher"/>
				<xsd:enumeration value="Transaragonés"/>
				<xsd:enumeration value="TransFolha"/>
				<xsd:enumeration value="Translink"/>
				<xsd:enumeration value="Trans-o-Flex"/>
				<xsd:enumeration value="Truline"/>
				<xsd:enumeration value="TSB"/>
				<xsd:enumeration value="Tuffnells"/>
				<xsd:enumeration value="TWS"/>
				<xsd:enumeration value="TXT"/>
				<xsd:enumeration value="TyD"/>
				<xsd:enumeration value="UBI"/>
				<xsd:enumeration value="UKMail"/>
				<xsd:enumeration value="UPakWeShip"/>
				<xsd:enumeration value="UPS"/>
				<xsd:enumeration value="UPS Freight"/>
				<xsd:enumeration value="UPS Mail Innovations"/>
				<xsd:enumeration value="Upsilon"/>
				<xsd:enumeration value="UPSMI"/>
				<xsd:enumeration value="Urban Express"/>
				<xsd:enumeration value="Uship"/>
				<xsd:enumeration value="USPS"/>
				<xsd:enumeration value="Verage Shipping"/>
				<xsd:enumeration value="Via Xpress"/>
				<xsd:enumeration value="VIR"/>
				<xsd:enumeration value="VNLIN"/>
				<xsd:enumeration value="WanbExpress"/>
				<xsd:enumeration value="Watkins and Shepard"/>
				<xsd:enumeration value="Wefast"/>
				<xsd:enumeration value="WeWorldExpress"/>
				<xsd:enumeration value="Whistl"/>
				<xsd:enumeration value="Whizzard"/>
				<xsd:enumeration value="WINIT"/>
				<xsd:enumeration value="WPX"/>
				<xsd:enumeration value="XDP"/>
				<xsd:enumeration value="XPO"/>
				<xsd:enumeration value="XPO Freight"/>
				<xsd:enumeration value="Xpressbees"/>
				<xsd:enumeration value="YAMATO"/>
				<xsd:enumeration value="YAMATO TRANSPORT"/>
				<xsd:enumeration value="YANWEN"/>
				<xsd:enumeration value="YDH"/>
				<xsd:enumeration value="Yellow Freight"/>
				<xsd:enumeration value="YFHEX"/>
				<xsd:enumeration value="Yodel"/>
				<xsd:enumeration value="YTO Express"/>
				<xsd:enumeration value="Yun Express"/>
				<xsd:enumeration value="Yunda Express"/>
				<xsd:enumeration value="Yurtiçi Kargo"/>
				<xsd:enumeration value="Zeleris"/>
				<xsd:enumeration value="ZTO Express"/>
				<xsd:enumeration value="Zust Ambrosetti"/>
				<!--
		
                ##################################################
                # The below list of carrier codes are duplicates and are scheduled for deprecation, kindly do not use them.
                ##################################################
    
	            -->
				<xsd:enumeration value="APC-Overnight"/>
				<xsd:enumeration value="AT Post"/>
				<xsd:enumeration value="COLIS PRIVE"/>
				<xsd:enumeration value="Colis Prive"/>
				<xsd:enumeration value="Couriers Please"/>
				<xsd:enumeration value="CTTExpress"/>
				<xsd:enumeration value="DACSHER"/>
				<xsd:enumeration value="DASCHER"/>
				<xsd:enumeration value="FEDEX_JP"/>
				<xsd:enumeration value="Fercam"/>
				<xsd:enumeration value="France Express"/>
				<xsd:enumeration value="GEODIS"/>
				<xsd:enumeration value="JP_EXPRESS"/>
				<xsd:enumeration value="Kuehne Nagel"/>
				<xsd:enumeration value="MZZ Briefdienst"/>
				<xsd:enumeration value="NACEX"/>
				<xsd:enumeration value="PARCEL2GO.COM"/>
				<xsd:enumeration value="Post NL"/>
				<xsd:enumeration value="QExpress"/>
				<xsd:enumeration value="Raben"/>
				<xsd:enumeration value="ROYAL_MAIL"/>
				<xsd:enumeration value="SagawaExpress"/>
				<xsd:enumeration value="Sendle"/>
				<xsd:enumeration value="Spring GDS"/>
				<xsd:enumeration value="Transoflex"/>
				<xsd:enumeration value="UK MAIL"/>
				<xsd:enumeration value="YamatoTransport"/>
				<xsd:enumeration value="Yanwen"/>
			</xsd:restriction>
		</xsd:simpleType>     
	</xsd:element>
	<!--
		
    ##################################################
    # Ship Option element
    ##################################################
    
	-->
	<xsd:element name="ShipOption">
		<xsd:simpleType>
			<xsd:restriction base="xsd:normalizedString">
				<xsd:minLength value="1"/>
				<xsd:maxLength value="250"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Marketplace Name element
    ##################################################
    
	-->
	<xsd:element name="MarketplaceName" type="StringNotNull"/>
	<!--
		
    ##################################################
    # ID Number type
    ##################################################
    
	-->
	<xsd:simpleType name="IDNumber">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:pattern value="\d{1,20}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MessageIDNumber">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:pattern value="\d{1,18}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		
    ##################################################
    # String types
    ##################################################
    
        -->
	<xsd:simpleType name="SevenString">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TenString">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwentyString">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ThirtyString">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FortyString">
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FiveStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="5"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NineStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="9"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TenStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwentyStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ThirtyStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="30"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FortyStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PARTSLINK">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="9"/>
			<xsd:maxLength value="9"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="String">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="StringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="50"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HundredString">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HundredFiftyStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="150"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MediumString">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="200"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MediumStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="200"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwoFiftyStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="250"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LongString">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LongStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="500"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MediumLongStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="700"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SuperLongString">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="1000"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SuperLongStringNotNull">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="1000"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwoThousandString">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:maxLength value="2000"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
    
    ##################################################
    # External customer ID element
    ##################################################
    
	-->
	<xsd:element name="ExternalCustomerID" type="HundredString"/>
	<!--
		
    ##################################################
    # Merchant order ID element
    ##################################################
    
	-->
	<xsd:element name="MerchantOrderID" type="String"/>
	<!--
		
    ##################################################
    # Merchant order item ID element
    ##################################################
    
	-->
	<xsd:element name="MerchantOrderItemID" type="String"/>
	<!--

    ##################################################
    # Merchant fulfillment ID element
    ##################################################

    -->
	<xsd:element name="MerchantFulfillmentID" type="TwentyStringNotNull"/>
	<!--

    ##################################################
    # Amazon shipment ID element
    ##################################################


	-->
	<xsd:element name="ShipmentID" type="String"/>
	<!--
		
    ##################################################
    # Merchant promotion ID element
    ##################################################
    
	-->
	<xsd:element name="MerchantPromotionID">
		<xsd:simpleType>
			<xsd:restriction base="xsd:normalizedString">
				<xsd:maxLength value="80"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Order ID element
    ##################################################
    
	-->
	<xsd:element name="AmazonOrderID">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:pattern value="\w{3}-\w{7}-\w{7}"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Order item code element
    ##################################################
    
	-->
	<xsd:element name="AmazonOrderItemCode">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:pattern value="\d{1,64}"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Amazon Customer ID element - Encrypted
    ##################################################

        -->
	<xsd:element name="AmazonCustomerID" type="HundredString"/>
	<!--

    ##################################################
    # Standard Product ID element
    ##################################################
    
	-->
	<xsd:element name="StandardProductID">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Type">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="ISBN"/>
							<xsd:enumeration value="UPC"/>
							<xsd:enumeration value="EAN"/>
							<xsd:enumeration value="ASIN"/>
							<xsd:enumeration value="GTIN"/>
							<xsd:enumeration value="GCID"/>
							<xsd:enumeration value="PZN"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element name="Value">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:minLength value="8"/>
							<xsd:maxLength value="16"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
	
	##################################################
    # Related Product ID element
    ##################################################
    
	-->
	<xsd:element name="RelatedProductID">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Type">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="UPC"/>
							<xsd:enumeration value="EAN"/>
							<xsd:enumeration value="GTIN"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element name="Value">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:minLength value="8"/>
							<xsd:maxLength value="16"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	
	<!--
		
    ##################################################
    # Product tax code element
    ##################################################
    
	-->
	<xsd:element name="ProductTaxCode" type="StringNotNull"/>
	<!--
		
    ##################################################
    # Promotion application type
    ##################################################
    
	-->
	<xsd:simpleType name="PromotionApplicationType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Principal"/>
			<xsd:enumeration value="Shipping"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		
    ##################################################
    # Promotion claim code
    ##################################################
    
	-->
	<xsd:element name="PromotionClaimCode">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:minLength value="6"/>
				<xsd:maxLength value="12"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Promotion data type
    ##################################################
    
	-->
	<xsd:complexType name="PromotionDataType">
		<xsd:sequence>
			<xsd:element ref="PromotionClaimCode"/>
			<xsd:element ref="MerchantPromotionID"/>
			<xsd:element name="Component" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="Type" type="PromotionApplicationType"/>
						<xsd:element name="Amount" type="CurrencyAmount"/>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--
		
    ##################################################
    # SKU element
    ##################################################
        
	-->
	<xsd:element name="SKU" type="SKUType"/>
	<xsd:simpleType name="SKUType">
		<xsd:restriction base="xsd:normalizedString">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="40"/>
			<!-- This is removed because it was deleting spaces in the middle of the SKU, 
			    whitespace in SKU will now be ignored -->
			<!-- <xsd:whiteSpace value="collapse"/>  -->
			<!-- <xsd:pattern value="[\p{IsBasicLatin}\p{IsLatin-1Supplement}]*"/> -->
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		
    ##################################################
    # Condition Info
    ##################################################
    
	-->
	<xsd:complexType name="ConditionInfo">
		<xsd:sequence>
			<xsd:element ref="ConditionType"/>
			<xsd:element name="ConditionNote" type="TwoThousandString" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="ConditionType">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="New"/>
				<xsd:enumeration value="UsedLikeNew"/>
				<xsd:enumeration value="UsedVeryGood"/>
				<xsd:enumeration value="UsedGood"/>
				<xsd:enumeration value="UsedAcceptable"/>
				<xsd:enumeration value="CollectibleLikeNew"/>
				<xsd:enumeration value="CollectibleVeryGood"/>
				<xsd:enumeration value="CollectibleGood"/>
				<xsd:enumeration value="CollectibleAcceptable"/>
				<xsd:enumeration value="Club"/>
				<xsd:enumeration value="NewOpenBox"/>
				<xsd:enumeration value="NewOem"/>
				<xsd:enumeration value="Refurbished"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Customization Info
    ##################################################
    
	-->
	<xsd:complexType name="CustomizationInfoType">
		<xsd:sequence>
			<xsd:element name="Type" type="StringNotNull"/>
			<xsd:element name="Data" type="LongString"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--
		
    ##################################################
    # Computer Platform
    ##################################################
    
	-->
	<xsd:element name="ComputerPlatform">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Type">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="windows"/>
							<xsd:enumeration value="mac"/>
							<xsd:enumeration value="linux"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element name="SystemRequirements" type="LongStringNotNull" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
        ##################################################
        # Rebate Information for Offering Rebates
        ##################################################
        -->
	<xsd:complexType name="RebateType">
		<xsd:sequence>
			<xsd:element name="RebateStartDate" type="xsd:dateTime"/>
			<xsd:element name="RebateEndDate" type="xsd:dateTime"/>
			<xsd:element name="RebateMessage" type="TwoFiftyStringNotNull"/>
			<xsd:element name="RebateName" type="FortyStringNotNull"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--
		
    ##################################################
    # Color and ColorMap
    ##################################################
    
	-->
	<xsd:element name="ColorSpecification">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Color" type="SuperLongStringNotNull"/>
				<xsd:element ref="ColorMap"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ColorMap">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="beige"/>
				<xsd:enumeration value="black"/>
				<xsd:enumeration value="blue"/>
				<xsd:enumeration value="brass"/>
				<xsd:enumeration value="bronze"/>
				<xsd:enumeration value="brown"/>
				<xsd:enumeration value="burst"/>
				<xsd:enumeration value="chrome"/>
				<xsd:enumeration value="clear"/>
				<xsd:enumeration value="gold"/>
				<xsd:enumeration value="gray"/>
				<xsd:enumeration value="green"/>
				<xsd:enumeration value="metallic"/>
				<xsd:enumeration value="multi-colored"/>
				<xsd:enumeration value="natural"/>
				<xsd:enumeration value="off-white"/>
				<xsd:enumeration value="orange"/>
				<xsd:enumeration value="pink"/>
				<xsd:enumeration value="purple"/>
				<xsd:enumeration value="red"/>
				<xsd:enumeration value="silver"/>
				<xsd:enumeration value="white"/>
				<xsd:enumeration value="yellow"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Denomination Type
    ##################################################
    
	-->
	<xsd:element name="Denomination">
		<xsd:simpleType>
			<xsd:restriction base="xsd:decimal">
				<xsd:totalDigits value="6"/>
				<xsd:fractionDigits value="2"/>
				<xsd:minInclusive value=".01"/>
				<xsd:maxInclusive value="2500.00"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<!--
		
    ##################################################
    # Integer Types
    ##################################################
    
	-->
	<xsd:simpleType name="PositiveInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="12"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwoDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ThreeDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FourDigitMinimumTwoDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="4"/>
			<xsd:minInclusive value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FiveDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="5"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SixDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="6"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SevenDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="7"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TenDigitInteger">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	
<!--
		
    ##################################################
    # Energy Dimension Types
    ##################################################
    
	-->	
	<xsd:complexType name="EnergyDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
					<xsd:attribute name="unitOfMeasure" type="EnergyUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>	
	
	<!--
                
    ##################################################
    # Percentage Types
    ##################################################
    
        -->
	<xsd:simpleType name="PercentageType">
		<xsd:restriction base="xsd:decimal">
			<xsd:fractionDigits value="2"/>
			<xsd:minExclusive value="0"/>
			<xsd:maxInclusive value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IntegerPercentageType">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:minInclusive value="1"/>
			<xsd:maxInclusive value="100"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		
    ##################################################
    # Decimal Types
    ##################################################
    
	-->
	<xsd:simpleType name="Dimension">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="2" fixed="true"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PositiveDimension">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="2" fixed="true"/>
			<xsd:minInclusive value="0.00"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PositiveNonZeroDimension">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="2" fixed="true"/>
			<xsd:minExclusive value="0.00"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FourDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="4" fixed="true"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FourPositiveNonZeroDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="12"/>
			<xsd:fractionDigits value="4" fixed="true"/>
			<xsd:minExclusive value="0.0000"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SixDigitDecimalFractionOne">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="6"/>
			<xsd:fractionDigits value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TwoDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="2"/>
			<xsd:fractionDigits value=" 1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ThreeDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="3"/>
			<xsd:fractionDigits value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FourDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="4"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FiveDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="5"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SixDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="6"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SevenDigitDecimal">
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="7"/>
			<xsd:fractionDigits value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		
    ##################################################
    # Date Types
    ##################################################
    
	-->
	<xsd:simpleType name="FourDigitYear">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:totalDigits value="4"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FourDigitYearPlusNV">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9]{4}|NV|nv"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
    ##################################################
    # Delivery Channel element
    ##################################################
    -->
	<xsd:element name="DeliveryChannel">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="in_store"/>
				<xsd:enumeration value="direct_ship"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<xsd:complexType name="EnergyRatingType">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="EnergyUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!--
		
    ##################################################
    # Dimension Types
    ##################################################
    
	-->
	<xsd:complexType name="AreaDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AreaUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="AreaDimensionOptionalUnit">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AreaUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="AirFlowDisplacementDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AirFlowDisplacementUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="BurnTimeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="BurnTimeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="CurencyDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="GlobalCurrencyCode" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LengthDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
		<xsd:complexType name="LensFixedFocalLengthDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="FocalLengthDimension" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LengthDimensionOptionalUnit">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LengthIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
       <xsd:complexType name="OptionalLengthIntegerDimension">
                <xsd:simpleContent>
                        <xsd:extension base="xsd:positiveInteger">
                                <xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="optional"/>
                        </xsd:extension>
                </xsd:simpleContent>
        </xsd:complexType>
	<xsd:complexType name="LuminancePositiveIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="LuminanceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LuminanceIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:nonNegativeInteger">
				<xsd:attribute name="unitOfMeasure" type="LuminanceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LuminanceDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="LuminanceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VolumeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="VolumeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VolumeIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="VolumeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VolumeRateDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="VolumeRateUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WeightDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="WeightUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WeightIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="WeightUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="JewelryLengthDimension">
		<xsd:simpleContent>
			<xsd:extension base="FourDecimal">
				<xsd:attribute name="unitOfMeasure" type="JewelryLengthUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="JewelryWeightDimension">
		<xsd:simpleContent>
			<xsd:extension base="FourDecimal">
				<xsd:attribute name="unitOfMeasure" type="JewelryWeightUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="PositiveWeightDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveDimension">
				<xsd:attribute name="unitOfMeasure" type="WeightUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="PositiveNonZeroWeightDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveNonZeroDimension">
				<xsd:attribute name="unitOfMeasure" type="WeightUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="DegreeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="DegreeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="MemorySizeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="MemorySizeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="MemorySizeIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveInteger">
				<xsd:attribute name="unitOfMeasure" type="MemorySizeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="FrequencyDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="FrequencyUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="FrequencyIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="FrequencyUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="AmperageDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AmperageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="ResistanceDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="ResistanceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="TimeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TimeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="StringTimeDimension">
		<xsd:simpleContent>
			<xsd:extension base="StringNotNull">
				<xsd:attribute name="unitOfMeasure" type="TimeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>	
	<xsd:complexType name="BatteryLifeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="BatteryAverageLifeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="TimeIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="TimeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="DateIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="DateUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="SubscriptionTermDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="DateUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="SunProtectionDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="SunProtectionUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>	
	<xsd:complexType name="AssemblyTimeDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="AssemblyTimeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="AgeRecommendedDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="AgeRecommendedUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="MinimumAgeRecommendedDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:nonNegativeInteger">
				<xsd:attribute name="unitOfMeasure" type="AgeRecommendedUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="BatteryPowerIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="BatteryPowerUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="BatteryPowerDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="BatteryPowerUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="LuminiousIntensityDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="LuminousIntensityUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VoltageDecimalDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveDimension">
				<xsd:attribute name="unitOfMeasure" type="VoltageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VoltageIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="VoltageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VoltageIntegerDimensionOptionalUnit">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="VoltageUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WattageIntegerDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="WattageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WattageDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="WattageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WattageDimensionOptionalUnit">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="WattageUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="MillimeterDecimalDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveDimension">
				<xsd:attribute name="unitOfMeasure" type="MillimeterUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="NoiseLevelDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="NoiseLevelUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="TemperatureDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TemperatureUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="StringTemperatureDimension">
		<xsd:simpleContent>
			<xsd:extension base="StringNotNull">
				<xsd:attribute name="unitOfMeasure" type="TemperatureUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>	
	<xsd:complexType name="TemperatureRatingDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TemperatureRatingUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="ClothingSizeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="ClothingSizeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="StringLengthOptionalDimension">
		<xsd:simpleContent>
			<xsd:extension base="StringNotNull">
				<xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="optional"/>
			</xsd:extension>
			<!-- If using a numeric measurement, please include the units of measurement. -->
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="StringLengthDimension">
		<xsd:simpleContent>
			<xsd:extension base="StringNotNull">
				<xsd:attribute name="unitOfMeasure" type="LengthUnitOfMeasure" use="required"/>
			</xsd:extension>	
		</xsd:simpleContent>
	</xsd:complexType>	
	<xsd:complexType name="CurrentDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="CurrentUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="GraduationInterval">
		<xsd:simpleContent>
			<xsd:extension base="PositiveInteger">
				<xsd:attribute name="unitOfMeasure" type="GraduationIntervalUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VolumeAndVolumeRateDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="VolumeAndVolumeRateUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="ForceDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="ForceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="HardnessDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="HardnessUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="SweetnessAtHarvestDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="SweetnessAtHarvestUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="VineyardYieldDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="VineyardYieldUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="AlcoholContentDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AlcoholContentUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="NicotineConcentrationDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="NicotineConcentrationUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="InductanceUnitDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="InductanceUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="TransferDataSpeedDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TransferDataSpeedUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="MaxPrintSpeedDataDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="MaxPrintSpeedDataUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!--
		
    ##################################################
    # Unit of Measure Types
    ##################################################
    
	-->
	<xsd:simpleType name="AreaUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="square-in"/>
			<xsd:enumeration value="square-ft"/>
			<xsd:enumeration value="square-cm"/>
			<xsd:enumeration value="square-m"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AirFlowDisplacementUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="cubic_feet_per_minute"/>
			<xsd:enumeration value="cubic_feet_per_hour"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BurnTimeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="minutes"/>
			<xsd:enumeration value="hours"/>
			<xsd:enumeration value="cycles"/>			
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LengthUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="MM"/>
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="IN"/>
			<xsd:enumeration value="FT"/>
			<xsd:enumeration value="inches"/>
			<xsd:enumeration value="feet"/>
			<xsd:enumeration value="meters"/>
			<xsd:enumeration value="decimeters"/>
			<xsd:enumeration value="centimeters"/>
			<xsd:enumeration value="millimeters"/>
			<xsd:enumeration value="micrometers"/>
			<xsd:enumeration value="nanometers"/>
			<xsd:enumeration value="picometers"/>
			<xsd:enumeration value="hundredths_inches"/>
			<xsd:enumeration value="yards"/>
			<xsd:enumeration value="angstrom"/>
			<xsd:enumeration value="mils"/>
			<xsd:enumeration value="miles"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FocalLengthDimension">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="MM"/>
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="meters"/>
			<xsd:enumeration value="centimeters"/>
			<xsd:enumeration value="millimeters"/>
			<xsd:enumeration value="angstrom"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LuminanceUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="lumens"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LuminousIntensityUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="candela"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VolumeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="cubic-cm"/>
			<xsd:enumeration value="cubic-ft"/>
			<xsd:enumeration value="cubic-in"/>
			<xsd:enumeration value="cubic-m"/>
			<xsd:enumeration value="cubic-yd"/>
			<xsd:enumeration value="cup"/>
			<xsd:enumeration value="fluid-oz"/>
			<xsd:enumeration value="gallon"/>
			<xsd:enumeration value="liter"/>
			<xsd:enumeration value="milliliter"/>
			<xsd:enumeration value="ounce"/>
			<xsd:enumeration value="pint"/>
			<xsd:enumeration value="quart"/>
			<xsd:enumeration value="liters"/>
			<xsd:enumeration value="deciliters"/>
			<xsd:enumeration value="centiliters"/>
			<xsd:enumeration value="milliliters"/>
			<xsd:enumeration value="microliters"/>
			<xsd:enumeration value="nanoliters"/>
			<xsd:enumeration value="picoliters"/>
			<xsd:enumeration value="grams"/>
			<xsd:enumeration value="imperial_gallons"/>
			<xsd:enumeration value="unknown_modifier"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="WeightUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="GR"/>
			<xsd:enumeration value="KG"/>
			<xsd:enumeration value="OZ"/>
			<xsd:enumeration value="LB"/>
			<xsd:enumeration value="MG"/>			
			<xsd:enumeration value="hundredths_pounds"/>
			<xsd:enumeration value="kilograms"/>
			<xsd:enumeration value="pounds"/>
			<xsd:enumeration value="pounds_per_square_inch"/>
			<xsd:enumeration value="newtons"/>
			<xsd:enumeration value="pascal"/>			
			<xsd:enumeration value="ounces"/>
			<xsd:enumeration value="grams_per_square_meter"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="JewelryLengthUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="MM"/>
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="IN"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="JewelryWeightUnitOfMeasure">
		<xsd:annotation>
			<xsd:documentation>
				Accepted values for TotalDiamondWeight : "GR","KG","OZ","LB","CARATS","DWT","CTTW","MG"   
    				Accepted values for StoneWeight        : "CARATS","DWT","CTTW"  
    				Accepted values for TotalGemWeight     : "GR","KG","OZ","LB","CARATS","DWT","CTTW","MG" 
    				Accepted values for TotalMetalWeight   : "GR","KG","OZ","LB","CARATS","DWT","CTTW","MG","TONS"  	
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="GR"/>
			<xsd:enumeration value="KG"/>
			<xsd:enumeration value="OZ"/>
			<xsd:enumeration value="LB"/>
			<xsd:enumeration value="CARATS"/>
			<xsd:enumeration value="DWT"/>
			<xsd:enumeration value="CTTW"/>
			<xsd:enumeration value="MG"/>
			<xsd:enumeration value="TONS"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DegreeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="degrees"/>
			<xsd:enumeration value="microradian"/>
			<xsd:enumeration value="arc_minute"/>			
			<xsd:enumeration value="arc_sec"/>
			<xsd:enumeration value="milliradian"/>
			<xsd:enumeration value="radians"/>			
			<xsd:enumeration value="turns"/>				
			<xsd:enumeration value="revolutions"/>				
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MemorySizeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="TB"/>
			<xsd:enumeration value="GB"/>
			<xsd:enumeration value="MB"/>
			<xsd:enumeration value="KB"/>
			<xsd:enumeration value="KO"/>
			<xsd:enumeration value="MO"/>
			<xsd:enumeration value="GO"/>
			<xsd:enumeration value="TO"/>
			<xsd:enumeration value="bytes"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FrequencyUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="pages_per_minute"/>
			<xsd:enumeration value="hertz"/>
			<xsd:enumeration value="millihertz"/>
			<xsd:enumeration value="microhertz"/>
			<xsd:enumeration value="terahertz"/>
			<xsd:enumeration value="Hz"/>
			<xsd:enumeration value="KHz"/>
			<xsd:enumeration value="MHz"/>
			<xsd:enumeration value="GHz"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AmperageUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="amps"/>
			<xsd:enumeration value="kiloamps"/>
			<xsd:enumeration value="microamps"/>
			<xsd:enumeration value="milliamps"/>
			<xsd:enumeration value="nanoamps"/>
			<xsd:enumeration value="picoamps"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GraduationIntervalUnitOfMeasure">
		<xsd:union memberTypes="LengthUnitOfMeasure WeightUnitOfMeasure VolumeUnitOfMeasure"/>
	</xsd:simpleType>
	<xsd:simpleType name="VolumeAndVolumeRateUnitOfMeasure">
		<xsd:union memberTypes="VolumeUnitOfMeasure VolumeRateUnitOfMeasure"/>
	</xsd:simpleType>
	<xsd:simpleType name="TimeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="sec"/>
			<xsd:enumeration value="min"/>
			<xsd:enumeration value="hr"/>
			<xsd:enumeration value="days"/>
			<xsd:enumeration value="hours"/>
			<xsd:enumeration value="minutes"/>
			<xsd:enumeration value="seconds"/>
			<xsd:enumeration value="milliseconds"/>
			<xsd:enumeration value="microseconds"/>
			<xsd:enumeration value="nanoseconds"/>
			<xsd:enumeration value="picoseconds"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BatteryAverageLifeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="minutes"/>
			<xsd:enumeration value="hours"/>
			<xsd:enumeration value="days"/>
			<xsd:enumeration value="weeks"/>
			<xsd:enumeration value="months"/>
			<xsd:enumeration value="years"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DataTransferUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="KHz"/>
			<xsd:enumeration value="MHz"/>
			<xsd:enumeration value="GHz"/>
			<xsd:enumeration value="Mbps"/>
			<xsd:enumeration value="Gbps"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ResistanceUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ohms"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DateUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="days"/>
			<xsd:enumeration value="weeks"/>
			<xsd:enumeration value="months"/>
			<xsd:enumeration value="years"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AssemblyTimeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="minutes"/>
			<xsd:enumeration value="hours"/>
			<xsd:enumeration value="days"/>
			<xsd:enumeration value="weeks"/>
			<xsd:enumeration value="months"/>
			<xsd:enumeration value="years"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AgeRecommendedUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="months"/>
			<xsd:enumeration value="years"/>
			<xsd:enumeration value="days"/>
			<xsd:enumeration value="weeks"/>
			<xsd:enumeration value="seconds"/>
			<xsd:enumeration value="hours"/>
			<xsd:enumeration value="minutes"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BatteryPowerUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="milliamp_hours"/>
			<xsd:enumeration value="amp_hours"/>
			<xsd:enumeration value="volt_amperes"/>
			<xsd:enumeration value="watt_hours"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VoltageUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="volts"/>
			<xsd:enumeration value="millivolts"/>
			<xsd:enumeration value="microvolts"/>
			<xsd:enumeration value="nanovolts"/>
			<xsd:enumeration value="kilovolts"/>			
			<xsd:enumeration value="volts_of_alternating_current"/>			
			<xsd:enumeration value="volts_of_direct_current"/>			
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="WattageUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="watts"/>
			<xsd:enumeration value="kilowatts"/>			
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MillimeterUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="millimeters"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TemperatureRatingUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="degrees-celsius"/>
			<xsd:enumeration value="degrees-fahrenheit"/>
			<xsd:enumeration value="kelvin"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ClothingSizeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="IN"/>
			<xsd:enumeration value="CM"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PowerUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="watts"/>
			<xsd:enumeration value="kilowatts"/>
			<xsd:enumeration value="horsepower"/>
			<xsd:enumeration value="watts-per-sec"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ResolutionUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="megapixels"/>
			<xsd:enumeration value="pixels"/>
			<xsd:enumeration value="lines_per_inch"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ApertureUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="f"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ContinuousShootingUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="frames"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EnergyConsumptionUnitOfMeasure">
		<xsd:restriction base="String">
			<xsd:enumeration value="kilowatt_hours"/>
			<xsd:enumeration value="btu"/>
			<xsd:enumeration value="kilowatts"/>
			<xsd:enumeration value="watt_hours"/>
			<xsd:enumeration value="btus"/>
			<xsd:enumeration value="cubic_meters"/>
			<xsd:enumeration value="cubic_feet"/>
			<xsd:enumeration value="joules"/>
			<xsd:enumeration value="milliamp_hours"/>
			<xsd:enumeration value="milliampere_hour"/>
			<xsd:enumeration value="milliampere_second"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CurrentUnitOfMeasure">
		<xsd:restriction base="xsd:string">
		    <xsd:enumeration value="mA"/>
			<xsd:enumeration value="A"/>
			<xsd:enumeration value="amps"/>
			<xsd:enumeration value="picoamps"/>
			<xsd:enumeration value="microamps"/>
			<xsd:enumeration value="milliamps"/>
			<xsd:enumeration value="kiloamps"/>
			<xsd:enumeration value="nanoamps"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ForceUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="newtons"/>
			<xsd:enumeration value="Newton"/>
			<xsd:enumeration value="pounds"/>
			<xsd:enumeration value="kilograms"/>
			<xsd:enumeration value="PSI"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HardnessUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="brinnell"/>
			<xsd:enumeration value="vickers"/>
			<xsd:enumeration value="rockwell_a"/>
			<xsd:enumeration value="rockwell_b"/>
			<xsd:enumeration value="rockwell_c"/>
			<xsd:enumeration value="rockwell_d"/>
			<xsd:enumeration value="shore_a"/>
			<xsd:enumeration value="shore_b"/>
			<xsd:enumeration value="shore_c"/>
			<xsd:enumeration value="shore_d"/>
			<xsd:enumeration value="shore_do"/>
			<xsd:enumeration value="shore_e"/>
			<xsd:enumeration value="shore_m"/>
			<xsd:enumeration value="shore_o"/>
			<xsd:enumeration value="shore_oo"/>
			<xsd:enumeration value="shore_ooo"/>
			<xsd:enumeration value="shore_ooo_s"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NoiseLevelUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="dBA"/>
			<xsd:enumeration value="decibels"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SunProtectionUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="sun_protection_factor"/>
		</xsd:restriction>
	</xsd:simpleType>	
	<xsd:simpleType name="TemperatureUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="F"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="TorqueType">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TorqueUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="TorqueUnitOfMeasure">
		<xsd:restriction base="StringNotNull">
			<xsd:enumeration value="foot-lbs"/>
			<xsd:enumeration value="inch-lbs"/>
			<xsd:enumeration value="centimeter_kilograms"/>
			<xsd:enumeration value="foot_pounds"/>
			<xsd:enumeration value="inch_ounces"/>
			<xsd:enumeration value="inch_pounds"/>
			<xsd:enumeration value="kilonewtons"/>
			<xsd:enumeration value="kilograms_per_millimeter"/>
			<xsd:enumeration value="newton_meters"/>
			<xsd:enumeration value="newton_millimeters"/>
			<xsd:enumeration value="newtons"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VolumeRateUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="milliliters per second"/>
			<xsd:enumeration value="centiliters per second"/>
			<xsd:enumeration value="liters per second"/>
			<xsd:enumeration value="milliliters per minute"/>
			<xsd:enumeration value="liters per minute"/>
			<xsd:enumeration value="microliters per second"/>
			<xsd:enumeration value="nanoliters per second"/>
			<xsd:enumeration value="picoliters per second"/>
			<xsd:enumeration value="microliters per minute"/>
			<xsd:enumeration value="nanoliters per minute"/>
			<xsd:enumeration value="picoliters per minute"/>
			<xsd:enumeration value="gallons_per_day"/>
			<xsd:enumeration value="liters_per_day"/>
			<xsd:enumeration value="liters"/>			
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="InductanceUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="microhenry"/>
			<xsd:enumeration value="farad"/>
			<xsd:enumeration value="kilofarad"/>
			<xsd:enumeration value="millihenry"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TransferDataSpeedUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="gigabits_per_second"/>
			<xsd:enumeration value="megabits_per_second"/>
			<xsd:enumeration value="megabytes_per_second"/>
			<xsd:enumeration value="pages_per_second"/>
			<xsd:enumeration value="kilobytes_per_second"/>
			<xsd:enumeration value="GHz"/>
			<xsd:enumeration value="gigabytes_per_second"/>
			<xsd:enumeration value="KHz"/>
			<xsd:enumeration value="MHz"/>
			<xsd:enumeration value="kilobits_per_second"/>
			<xsd:enumeration value="bits_per_second"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MaxPrintSpeedDataUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="pages_per_minute"/>
			<xsd:enumeration value="pages_per_second"/>
			<xsd:enumeration value="pages_per_month"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--

    ##################################################
    # Generic Customer Type
    ##################################################

    -->
	<xsd:complexType name="Customer">
		<xsd:sequence>
			<xsd:element name="Name" type="String" minOccurs="0"/>
			<xsd:element name="FormalTitle" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>e.g. Mr., Ms., etc.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="10"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="GivenName" type="String" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Usually the customer's first name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FamilyName" type="String" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Usually the customer's last name.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Email" type="EmailAddressType" minOccurs="0"/>
			<xsd:element name="BirthDate" type="xsd:date" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The customer's birth date</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CustomerAddress" type="AddressType" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--
	####################
	# Name - value pair
	####################
	-->
	<xsd:complexType name="NameValuePair">
		<xsd:sequence>
			<xsd:element name="Name" type="StringNotNull"/>
			<xsd:element name="Value" type="LongString"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="LanguageStringType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Abkhazian"/>
			<xsd:enumeration value="Adygei"/>
			<xsd:enumeration value="Afar"/>
			<xsd:enumeration value="Afrikaans"/>
			<xsd:enumeration value="Albanian"/>
			<xsd:enumeration value="Alsatian"/>
			<xsd:enumeration value="Amharic"/>
			<xsd:enumeration value="Arabic"/>
			<xsd:enumeration value="Aramaic"/>
			<xsd:enumeration value="Armenian"/>
			<xsd:enumeration value="Assamese"/>
			<xsd:enumeration value="Aymara"/>
			<xsd:enumeration value="Azerbaijani"/>
			<xsd:enumeration value="Bambara"/>
			<xsd:enumeration value="Bashkir"/>
			<xsd:enumeration value="Basque"/>
			<xsd:enumeration value="Bengali"/>
			<xsd:enumeration value="Berber"/>
			<xsd:enumeration value="Bhutani"/>
			<xsd:enumeration value="Bihari"/>
			<xsd:enumeration value="Bislama"/>
			<xsd:enumeration value="Breton"/>
			<xsd:enumeration value="Bulgarian"/>
			<xsd:enumeration value="Burmese"/>
			<xsd:enumeration value="Buryat"/>
			<xsd:enumeration value="Byelorussian"/>
			<xsd:enumeration value="CantoneseChinese"/>
			<xsd:enumeration value="Castillian"/>
			<xsd:enumeration value="Catalan"/>
			<xsd:enumeration value="Cayuga"/>
			<xsd:enumeration value="Cheyenne"/>
			<xsd:enumeration value="Chinese"/>
			<xsd:enumeration value="ClassicalNewari"/>
			<xsd:enumeration value="Cornish"/>
			<xsd:enumeration value="Corsican"/>
			<xsd:enumeration value="Creole"/>
			<xsd:enumeration value="CrimeanTatar"/>
			<xsd:enumeration value="Croatian"/>
			<xsd:enumeration value="Czech"/>
			<xsd:enumeration value="Danish"/>
			<xsd:enumeration value="Dargwa"/>
			<xsd:enumeration value="Dutch"/>
			<xsd:enumeration value="English"/>
			<xsd:enumeration value="Esperanto"/>
			<xsd:enumeration value="Estonian"/>
			<xsd:enumeration value="Faroese"/>
			<xsd:enumeration value="Farsi"/>
			<xsd:enumeration value="Fiji"/>
			<xsd:enumeration value="Filipino"/>
			<xsd:enumeration value="Finnish"/>
			<xsd:enumeration value="Flemish"/>
			<xsd:enumeration value="French"/>
			<xsd:enumeration value="FrenchCanadian"/>
			<xsd:enumeration value="Frisian"/>
			<xsd:enumeration value="Galician"/>
			<xsd:enumeration value="Georgian"/>
			<xsd:enumeration value="German"/>
			<xsd:enumeration value="Gibberish"/>
			<xsd:enumeration value="Greek"/>
			<xsd:enumeration value="Greenlandic"/>
			<xsd:enumeration value="Guarani"/>
			<xsd:enumeration value="Gujarati"/>
			<xsd:enumeration value="Gullah"/>
			<xsd:enumeration value="Hausa"/>
			<xsd:enumeration value="Hawaiian"/>
			<xsd:enumeration value="Hebrew"/>
			<xsd:enumeration value="Hindi"/>
			<xsd:enumeration value="Hmong"/>
			<xsd:enumeration value="Hungarian"/>
			<xsd:enumeration value="Icelandic"/>
			<xsd:enumeration value="IndoEuropean"/>
			<xsd:enumeration value="Indonesian"/>
			<xsd:enumeration value="Ingush"/>
			<xsd:enumeration value="Interlingua"/>
			<xsd:enumeration value="Interlingue"/>
			<xsd:enumeration value="Inuktitun"/>
			<xsd:enumeration value="Inuktitut"/>
			<xsd:enumeration value="Inupiak"/>
			<xsd:enumeration value="Inupiaq"/>
			<xsd:enumeration value="Irish"/>
			<xsd:enumeration value="Italian"/>
			<xsd:enumeration value="Japanese"/>
			<xsd:enumeration value="Javanese"/>
			<xsd:enumeration value="Kalaallisut"/>
			<xsd:enumeration value="Kalmyk"/>
			<xsd:enumeration value="Kannada"/>
			<xsd:enumeration value="KarachayBalkar"/>
			<xsd:enumeration value="Kashmiri"/>
			<xsd:enumeration value="Kashubian"/>
			<xsd:enumeration value="Kazakh"/>
			<xsd:enumeration value="Khmer"/>
			<xsd:enumeration value="Kinyarwanda"/>
			<xsd:enumeration value="Kirghiz"/>
			<xsd:enumeration value="Kirundi"/>
			<xsd:enumeration value="Klingon"/>
			<xsd:enumeration value="Korean"/>
			<xsd:enumeration value="Kurdish"/>
			<xsd:enumeration value="Ladino"/>
			<xsd:enumeration value="Lao"/>
			<xsd:enumeration value="Lapp"/>
			<xsd:enumeration value="Latin"/>
			<xsd:enumeration value="Latvian"/>
			<xsd:enumeration value="Lingala"/>
			<xsd:enumeration value="Lithuanian"/>
			<xsd:enumeration value="Lojban"/>
			<xsd:enumeration value="LowerSorbian"/>
			<xsd:enumeration value="Macedonian"/>
			<xsd:enumeration value="Malagasy"/>
			<xsd:enumeration value="Malay"/>
			<xsd:enumeration value="Malayalam"/>
			<xsd:enumeration value="Maltese"/>
			<xsd:enumeration value="MandarinChinese"/>
			<xsd:enumeration value="Maori"/>
			<xsd:enumeration value="Marathi"/>
			<xsd:enumeration value="Mende"/>
			<xsd:enumeration value="MiddleEnglish"/>
			<xsd:enumeration value="Mirandese"/>
			<xsd:enumeration value="Moksha"/>
			<xsd:enumeration value="Moldavian"/>
			<xsd:enumeration value="Mongo"/>
			<xsd:enumeration value="Mongolian"/>
			<xsd:enumeration value="Multilingual"/>
			<xsd:enumeration value="Nauru"/>
			<xsd:enumeration value="Navaho"/>
			<xsd:enumeration value="Nepali"/>
			<xsd:enumeration value="Nogai"/>
			<xsd:enumeration value="Norwegian"/>
			<xsd:enumeration value="Occitan"/>
			<xsd:enumeration value="OldEnglish"/>
			<xsd:enumeration value="Oriya"/>
			<xsd:enumeration value="Oromo"/>
			<xsd:enumeration value="Pashto"/>
			<xsd:enumeration value="Persian"/>
			<xsd:enumeration value="PigLatin"/>
			<xsd:enumeration value="Polish"/>
			<xsd:enumeration value="Portuguese"/>
			<xsd:enumeration value="Punjabi"/>
			<xsd:enumeration value="Quechua"/>
			<xsd:enumeration value="Romance"/>
			<xsd:enumeration value="Romanian"/>
			<xsd:enumeration value="Romany"/>
			<xsd:enumeration value="Russian"/>
			<xsd:enumeration value="Samaritan"/>
			<xsd:enumeration value="Samoan"/>
			<xsd:enumeration value="Sangho"/>
			<xsd:enumeration value="Sanskrit"/>
			<xsd:enumeration value="Serbian"/>
			<xsd:enumeration value="Serbo-Croatian"/>
			<xsd:enumeration value="Sesotho"/>
			<xsd:enumeration value="Setswana"/>
			<xsd:enumeration value="Shona"/>
			<xsd:enumeration value="SichuanYi"/>
			<xsd:enumeration value="Sicilian"/>
			<xsd:enumeration value="SignLanguage"/>
			<xsd:enumeration value="Sindhi"/>
			<xsd:enumeration value="Sinhalese"/>
			<xsd:enumeration value="Siswati"/>
			<xsd:enumeration value="Slavic"/>
			<xsd:enumeration value="Slovak"/>
			<xsd:enumeration value="Slovakian"/>
			<xsd:enumeration value="Slovene"/>
			<xsd:enumeration value="Somali"/>
			<xsd:enumeration value="Spanish"/>
			<xsd:enumeration value="Sumerian"/>
			<xsd:enumeration value="Sundanese"/>
			<xsd:enumeration value="Swahili"/>
			<xsd:enumeration value="Swedish"/>
			<xsd:enumeration value="SwissGerman"/>
			<xsd:enumeration value="Syriac"/>
			<xsd:enumeration value="Tagalog"/>
			<xsd:enumeration value="TaiwaneseChinese"/>
			<xsd:enumeration value="Tajik"/>
			<xsd:enumeration value="Tamil"/>
			<xsd:enumeration value="Tatar"/>
			<xsd:enumeration value="Telugu"/>
			<xsd:enumeration value="Thai"/>
			<xsd:enumeration value="Tibetan"/>
			<xsd:enumeration value="Tigrinya"/>
			<xsd:enumeration value="Tonga"/>
			<xsd:enumeration value="Tsonga"/>
			<xsd:enumeration value="Turkish"/>
			<xsd:enumeration value="Turkmen"/>
			<xsd:enumeration value="Twi"/>
			<xsd:enumeration value="Udmurt"/>
			<xsd:enumeration value="Uighur"/>
			<xsd:enumeration value="Ukrainian"/>
			<xsd:enumeration value="Ukranian"/>
			<xsd:enumeration value="Unknown"/>
			<xsd:enumeration value="Urdu"/>
			<xsd:enumeration value="Uzbek"/>
			<xsd:enumeration value="Vietnamese"/>
			<xsd:enumeration value="Volapuk"/>
			<xsd:enumeration value="Welsh"/>
			<xsd:enumeration value="Wolof"/>
			<xsd:enumeration value="Xhosa"/>
			<xsd:enumeration value="Yiddish"/>
			<xsd:enumeration value="Yoruba"/>
			<xsd:enumeration value="Zhuang"/>
			<xsd:enumeration value="Zulu"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LanguageSWVG">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="adygei"/>
			<xsd:enumeration value="afrikaans"/>
			<xsd:enumeration value="albanian"/>
			<xsd:enumeration value="alsatian"/>
			<xsd:enumeration value="amharic"/>
			<xsd:enumeration value="arabic"/>
			<xsd:enumeration value="armenian"/>
			<xsd:enumeration value="assamese"/>
			<xsd:enumeration value="bambara"/>
			<xsd:enumeration value="basque"/>
			<xsd:enumeration value="bengali"/>
			<xsd:enumeration value="berber"/>
			<xsd:enumeration value="breton"/>
			<xsd:enumeration value="bulgarian"/>
			<xsd:enumeration value="buryat"/>
			<xsd:enumeration value="cantonese_chinese"/>
			<xsd:enumeration value="castillian"/>
			<xsd:enumeration value="catalan"/>
			<xsd:enumeration value="cayuga"/>
			<xsd:enumeration value="cheyenne"/>
			<xsd:enumeration value="chinese"/>
			<xsd:enumeration value="classical_newari"/>
			<xsd:enumeration value="cornish"/>
			<xsd:enumeration value="corsican"/>
			<xsd:enumeration value="creole"/>
			<xsd:enumeration value="crimean_tatar"/>
			<xsd:enumeration value="croatian"/>
			<xsd:enumeration value="czech"/>
			<xsd:enumeration value="danish"/>
			<xsd:enumeration value="dargwa"/>
			<xsd:enumeration value="dutch"/>
			<xsd:enumeration value="english"/>
			<xsd:enumeration value="esperanto"/>
			<xsd:enumeration value="estonian"/>
			<xsd:enumeration value="farsi"/>
			<xsd:enumeration value="filipino"/>
			<xsd:enumeration value="finnish"/>
			<xsd:enumeration value="flemish"/>
			<xsd:enumeration value="french"/>
			<xsd:enumeration value="french_canadian"/>
			<xsd:enumeration value="georgian"/>
			<xsd:enumeration value="german"/>
			<xsd:enumeration value="gibberish"/>
			<xsd:enumeration value="greek"/>
			<xsd:enumeration value="gujarati"/>
			<xsd:enumeration value="gullah"/>
			<xsd:enumeration value="hausa"/>
			<xsd:enumeration value="hawaiian"/>
			<xsd:enumeration value="hebrew"/>
			<xsd:enumeration value="hindi"/>
			<xsd:enumeration value="hmong"/>
			<xsd:enumeration value="hungarian"/>
			<xsd:enumeration value="icelandic"/>
			<xsd:enumeration value="indo_european"/>
			<xsd:enumeration value="indonesian"/>
			<xsd:enumeration value="ingush"/>
			<xsd:enumeration value="inuktitun"/>
			<xsd:enumeration value="inuktitut"/>
			<xsd:enumeration value="inupiaq"/>
			<xsd:enumeration value="irish"/>
			<xsd:enumeration value="italian"/>
			<xsd:enumeration value="japanese"/>
			<xsd:enumeration value="kalaallisut"/>
			<xsd:enumeration value="kalmyk"/>
			<xsd:enumeration value="karachay_balkar"/>
			<xsd:enumeration value="kashubian"/>
			<xsd:enumeration value="kazakh"/>
			<xsd:enumeration value="khmer"/>
			<xsd:enumeration value="klingon"/>
			<xsd:enumeration value="korean"/>
			<xsd:enumeration value="kurdish"/>
			<xsd:enumeration value="ladino"/>
			<xsd:enumeration value="lao"/>
			<xsd:enumeration value="lapp"/>
			<xsd:enumeration value="latin"/>
			<xsd:enumeration value="lithuanian"/>
			<xsd:enumeration value="lojban"/>
			<xsd:enumeration value="lower_sorbian"/>
			<xsd:enumeration value="macedonian"/>
			<xsd:enumeration value="malagasy"/>
			<xsd:enumeration value="malay"/>
			<xsd:enumeration value="malayalam"/>
			<xsd:enumeration value="maltese"/>
			<xsd:enumeration value="mandarin_chinese"/>
			<xsd:enumeration value="maori"/>
			<xsd:enumeration value="mende"/>
			<xsd:enumeration value="middle_english"/>
			<xsd:enumeration value="mirandese"/>
			<xsd:enumeration value="moksha"/>
			<xsd:enumeration value="mongo"/>
			<xsd:enumeration value="mongolian"/>
			<xsd:enumeration value="multilingual"/>
			<xsd:enumeration value="navaho"/>
			<xsd:enumeration value="nogai"/>
			<xsd:enumeration value="norwegian"/>
			<xsd:enumeration value="old_english"/>
			<xsd:enumeration value="persian"/>
			<xsd:enumeration value="pig_latin"/>
			<xsd:enumeration value="polish"/>
			<xsd:enumeration value="portuguese"/>
			<xsd:enumeration value="romance"/>
			<xsd:enumeration value="romanian"/>
			<xsd:enumeration value="romany"/>
			<xsd:enumeration value="russian"/>
			<xsd:enumeration value="samaritan"/>
			<xsd:enumeration value="sanskrit"/>
			<xsd:enumeration value="serbian"/>
			<xsd:enumeration value="serbo-croatian"/>
			<xsd:enumeration value="sichuan_yi"/>
			<xsd:enumeration value="sicilian"/>
			<xsd:enumeration value="sign_language"/>
			<xsd:enumeration value="slavic"/>
			<xsd:enumeration value="slovak"/>
			<xsd:enumeration value="slovene"/>
			<xsd:enumeration value="somali"/>
			<xsd:enumeration value="spanish"/>
			<xsd:enumeration value="sumerian"/>
			<xsd:enumeration value="swahili"/>
			<xsd:enumeration value="swedish"/>
			<xsd:enumeration value="swiss_german"/>
			<xsd:enumeration value="tagalog"/>
			<xsd:enumeration value="taiwanese_chinese"/>
			<xsd:enumeration value="tamil"/>
			<xsd:enumeration value="thai"/>
			<xsd:enumeration value="tibetan"/>
			<xsd:enumeration value="turkish"/>
			<xsd:enumeration value="udmurt"/>
			<xsd:enumeration value="ukrainian"/>
			<xsd:enumeration value="unknown"/>
			<xsd:enumeration value="urdu"/>
			<xsd:enumeration value="vietnamese"/>
			<xsd:enumeration value="welsh"/>
			<xsd:enumeration value="wolof"/>
			<xsd:enumeration value="xhosa"/>
			<xsd:enumeration value="yiddish"/>
			<xsd:enumeration value="zulu"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MusicFormatType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="authorized_bootleg"/>
			<xsd:enumeration value="bsides"/>
			<xsd:enumeration value="best_of"/>
			<xsd:enumeration value="box_set"/>
			<xsd:enumeration value="original_recording"/>
			<xsd:enumeration value="reissued"/>
			<xsd:enumeration value="remastered"/>
			<xsd:enumeration value="soundtrack"/>
			<xsd:enumeration value="special_edition"/>
			<xsd:enumeration value="special_limited_edition"/>
			<xsd:enumeration value="cast_recording"/>
			<xsd:enumeration value="compilation"/>
			<xsd:enumeration value="deluxe_edition"/>
			<xsd:enumeration value="digital_sound"/>
			<xsd:enumeration value="double_lp"/>
			<xsd:enumeration value="explicit_lyrics"/>
			<xsd:enumeration value="hi-fidelity"/>
			<xsd:enumeration value="import"/>
			<xsd:enumeration value="limited_collectors_edition"/>
			<xsd:enumeration value="limited_edition"/>
			<xsd:enumeration value="remixes"/>
			<xsd:enumeration value="live"/>
			<xsd:enumeration value="extra_tracks"/>
			<xsd:enumeration value="cutout"/>
			<xsd:enumeration value="cd_and_dvd"/>
			<xsd:enumeration value="dual_disc"/>
			<xsd:enumeration value="hybrid_sacd"/>
			<xsd:enumeration value="cd-single"/>
			<xsd:enumeration value="maxi_single"/>
			<xsd:enumeration value="sacd"/>
			<xsd:enumeration value="minidisc"/>
			<xsd:enumeration value="uk_import"/>
			<xsd:enumeration value="us_import"/>
			<xsd:enumeration value="jp_import"/>
			<xsd:enumeration value="enhanced"/>
			<xsd:enumeration value="clean"/>
			<xsd:enumeration value="copy_protected_cd"/>
			<xsd:enumeration value="double_lp"/>
			<xsd:enumeration value="soundtrack"/>
			<xsd:enumeration value="cd-single"/>
			<xsd:enumeration value="remastered"/>
			<xsd:enumeration value="box_set"/>
			<xsd:enumeration value="double_cd"/>
			<xsd:enumeration value="karaoke"/>
			<xsd:enumeration value="limited_edition"/>
			<xsd:enumeration value="maxi_single"/>
			<xsd:enumeration value="mp3_audio"/>
			<xsd:enumeration value="ringle"/>
			<xsd:enumeration value="cd_and_dvd"/>
			<xsd:enumeration value="shm_cd"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GiftCardsFormatType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="email_gift_cards"/>
			<xsd:enumeration value="plastic_gift_cards"/>
			<xsd:enumeration value="print_at_home"/>
			<xsd:enumeration value="multi_pack"/>
			<xsd:enumeration value="facebook"/>
			<xsd:enumeration value="gift_box"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CountryOfOriginType">
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AudioEncodingType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="5_1_disney_enhanced_home_theater_mix"/>
			<xsd:enumeration value="7_1_disney_enhanced_home_theater_mix"/>
			<xsd:enumeration value="analog"/>
			<xsd:enumeration value="digital_atrac"/>
			<xsd:enumeration value="dolby_digital_1.0"/>
			<xsd:enumeration value="dolby_digital_2.0"/>
			<xsd:enumeration value="dolby_digital_2.0_mono"/>
			<xsd:enumeration value="dolby_digital_2.0_stereo"/>
			<xsd:enumeration value="dolby_digital_2.0_surround"/>
			<xsd:enumeration value="dolby_digital_2.1"/>
			<xsd:enumeration value="dolby_digital_3.0"/>
			<xsd:enumeration value="dolby_digital_4.0"/>
			<xsd:enumeration value="dolby_digital_4.1"/>
			<xsd:enumeration value="dolby_digital_5.0"/>
			<xsd:enumeration value="dolby_digital_5.1"/>
			<xsd:enumeration value="dolby_digital_5.1_es"/>
			<xsd:enumeration value="dolby_digital_5.1_ex"/>
			<xsd:enumeration value="dolby_digital_6.1_es"/>
			<xsd:enumeration value="dolby_digital_6.1_ex"/>
			<xsd:enumeration value="dolby_digital_ex"/>
			<xsd:enumeration value="dolby_digital_live"/>
			<xsd:enumeration value="dolby_digital_plus"/>
			<xsd:enumeration value="dolby_digital_plus_2_0"/>
			<xsd:enumeration value="dolby_digital_plus_5_1"/>
			<xsd:enumeration value="dolby_stereo_analog"/>
			<xsd:enumeration value="dolby_surround"/>
			<xsd:enumeration value="dolby_truehd"/>
			<xsd:enumeration value="dolby_truehd_5_1"/>
			<xsd:enumeration value="dts_5.0"/>
			<xsd:enumeration value="dts_5.1"/>
			<xsd:enumeration value="dts_6.1"/>
			<xsd:enumeration value="dts_6_1_es"/>
			<xsd:enumeration value="dts_6.1_es"/>
			<xsd:enumeration value="dts_es"/>
			<xsd:enumeration value="dts_hd_high_res_audio"/>
			<xsd:enumeration value="dts_interactive"/>
			<xsd:enumeration value="hi_res_96_24_digital_surround"/>
			<xsd:enumeration value="mlp_lossless"/>
			<xsd:enumeration value="mono"/>
			<xsd:enumeration value="mpeg_1_2.0"/>
			<xsd:enumeration value="mpeg_2_5.1"/>
			<xsd:enumeration value="pcm"/>
			<xsd:enumeration value="pcm_24bit_96khz"/>
			<xsd:enumeration value="pcm_mono"/>
			<xsd:enumeration value="pcm_stereo"/>
			<xsd:enumeration value="pcm_surround"/>
			<xsd:enumeration value="quadraphonic"/>
			<xsd:enumeration value="stereo"/>
			<xsd:enumeration value="surround"/>
			<xsd:enumeration value="thx_surround_ex"/>
			<xsd:enumeration value="unknown_audio_encoding"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
     ###############################################################
     # CE and CameraPhoto type definitions
     ###############################################################
    -->
	<xsd:simpleType name="ZoomUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="x"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="ZoomDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="ZoomUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="PixelUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="centimeters"/>
			<xsd:enumeration value="pixels"/>
			<xsd:enumeration value="MP"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="PixelDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="PixelUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="PressureUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="bars"/>
			<xsd:enumeration value="psi"/>
			<xsd:enumeration value="pascal"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="PressureDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="PressureUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="OpticalPowerUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="diopters"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="OpticalPowerDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="OpticalPowerUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="PowerDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="PowerUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="ResolutionDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="ResolutionUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
        <xsd:complexType name="OptionalResolutionDimension">
                <xsd:simpleContent>
                        <xsd:extension base="StringNotNull">
                                <xsd:attribute name="unitOfMeasure" type="ResolutionUnitOfMeasure" use="optional"/>
                        </xsd:extension>
                </xsd:simpleContent>
        </xsd:complexType>
	<xsd:complexType name="ApertureDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="ApertureUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="ContinuousShootingDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="ContinuousShootingUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="EnergyConsumptionDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="EnergyConsumptionUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="StoneCreationMethod">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="natural"/>
			<xsd:enumeration value="simulated"/>
			<xsd:enumeration value="synthetic"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AspectRatio">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="2:01"/>
			<xsd:enumeration value="4:03"/>
			<xsd:enumeration value="11:09"/>
			<xsd:enumeration value="14:09"/>
			<xsd:enumeration value="16:09"/>
			<xsd:enumeration value="1.27:1"/>
			<xsd:enumeration value="1.29:1"/>
			<xsd:enumeration value="1.30:1"/>
			<xsd:enumeration value="1.33:1"/>
			<xsd:enumeration value="1.34:1"/>
			<xsd:enumeration value="1.35:1"/>
			<xsd:enumeration value="1.37:1"/>
			<xsd:enumeration value="1.38:1"/>
			<xsd:enumeration value="1.44:1"/>
			<xsd:enumeration value="1.45:1"/>
			<xsd:enumeration value="1.50:1"/>
			<xsd:enumeration value="1.55:1"/>
			<xsd:enumeration value="1.58:1"/>
			<xsd:enumeration value="1.59:1"/>
			<xsd:enumeration value="1.60:1"/>
			<xsd:enumeration value="1.63:1"/>
			<xsd:enumeration value="1.65:1"/>
			<xsd:enumeration value="1.66:1"/>
			<xsd:enumeration value="1.67:1"/>
			<xsd:enumeration value="1.70:1"/>
			<xsd:enumeration value="1.71:1"/>
			<xsd:enumeration value="1.74:1"/>
			<xsd:enumeration value="1.75:1"/>
			<xsd:enumeration value="1.76:1"/>
			<xsd:enumeration value="1.77:1"/>
			<xsd:enumeration value="1.78:1"/>
			<xsd:enumeration value="1.83:1"/>
			<xsd:enumeration value="1.85:1"/>
			<xsd:enumeration value="1.87:1"/>
			<xsd:enumeration value="1.88:1"/>
			<xsd:enumeration value="1.98:1"/>
			<xsd:enumeration value="2.10:1"/>
			<xsd:enumeration value="2.20:1"/>
			<xsd:enumeration value="2.21:1"/>
			<xsd:enumeration value="2.22:1"/>
			<xsd:enumeration value="2.30:1"/>
			<xsd:enumeration value="2.31:1"/>
			<xsd:enumeration value="2.33:1"/>
			<xsd:enumeration value="2.35:1"/>
			<xsd:enumeration value="2.39:1"/>
			<xsd:enumeration value="2.40:1"/>
			<xsd:enumeration value="2.55:1"/>
			<xsd:enumeration value="unknown_aspect_ratio"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BatteryCellTypeValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="NiCAD"/>
			<xsd:enumeration value="NiMh"/>
			<xsd:enumeration value="alkaline"/>
			<xsd:enumeration value="aluminum_oxygen"/>
			<xsd:enumeration value="lead_acid"/>
			<xsd:enumeration value="lead_calcium"/>
			<xsd:enumeration value="lithium"/>
			<xsd:enumeration value="lithium_ion"/>
			<xsd:enumeration value="lithium_manganese_dioxide"/>
			<xsd:enumeration value="lithium_metal"/>
			<xsd:enumeration value="lithium_polymer"/>
			<xsd:enumeration value="manganese"/>
			<xsd:enumeration value="polymer"/>
			<xsd:enumeration value="silver_oxide"/>
			<xsd:enumeration value="zinc"/>			
			<xsd:enumeration value="lead_acid_agm"/>
			<xsd:enumeration value="lithium_air"/>
			<xsd:enumeration value="lithium_cobalt"/>
			<xsd:enumeration value="lithium_nickel_cobalt_aluminum"/>
			<xsd:enumeration value="lithium_nickel_manganese_cobalt"/>
			<xsd:enumeration value="lithium_phosphate"/>
			<xsd:enumeration value="lithium_thionyl_chloride"/>
			<xsd:enumeration value="lithium_titanate"/>
			<xsd:enumeration value="nickel_iron"/>
			<xsd:enumeration value="nickel_zinc"/>
			<xsd:enumeration value="silver_calcium"/>
			<xsd:enumeration value="silver_zinc"/>
			<xsd:enumeration value="zinc_air"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
    ###############################################################
    # FBA Data Types
	###############################################################
	-->
	<xsd:simpleType name="HazmatItemType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="butane"/>
			<xsd:enumeration value="fuel_cell"/>
			<xsd:enumeration value="gasoline"/>
			<xsd:enumeration value="orm_d_class_1"/>
			<xsd:enumeration value="orm_d_class_2"/>
			<xsd:enumeration value="orm_d_class_3"/>
			<xsd:enumeration value="orm_d_class_4"/>
			<xsd:enumeration value="orm_d_class_5"/>
			<xsd:enumeration value="orm_d_class_6"/>
			<xsd:enumeration value="orm_d_class_7"/>
			<xsd:enumeration value="orm_d_class_8"/>
			<xsd:enumeration value="orm_d_class_9"/>
			<xsd:enumeration value="sealed_lead_acid_battery"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AmazonMaturityRatingType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="adult_content"/>
			<xsd:enumeration value="ages_13_and_older"/>
			<xsd:enumeration value="ages_17_and_older"/>
			<xsd:enumeration value="ages_9_and_older"/>
			<xsd:enumeration value="all_ages"/>
			<xsd:enumeration value="children"/>
			<xsd:enumeration value="rating_pending"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IdentityPackageType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="bulk"/>
			<xsd:enumeration value="frustration_free"/>
			<xsd:enumeration value="traditional"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SerialNumberFormatType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="a_or_z_alphanumeric_13"/>
			<xsd:enumeration value="alphanumeric_8"/>
			<xsd:enumeration value="numeric_14"/>
			<xsd:enumeration value="alphanumeric_14"/>
			<xsd:enumeration value="numeric_12"/>
			<xsd:enumeration value="w_alphanumeric_12"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
	########################################
	######  Loyalty Custom Attribute   ##### 
	########################################
	-->
	<xsd:complexType name="LoyaltyCustomAttribute">
		<xsd:simpleContent>
			<xsd:extension base="String">
				<xsd:attribute name="attributeName" type="String"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:complexType name="WeightRecommendationType">
		<xsd:sequence>
			<xsd:element name="MaximumWeightRecommendation" type="PositiveWeightDimension" minOccurs="0"/>
			<xsd:element name="MinimumWeightRecommendation" type="PositiveWeightDimension" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- 
	########################################
	######     Character Data Type     ##### 
	########################################
	-->
	<xsd:element name="CharacterData" type="CharacterDataType"/>
	<xsd:complexType name="CharacterDataType">
		<xsd:sequence>
			<xsd:element ref="SKU"/>
			<xsd:element name="EffectiveTimestamp" type="xsd:dateTime" minOccurs="0"/>
			<xsd:element name="Plugin" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="AdditionalMessageDiscriminator" type="xsd:string" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="Payload" type="xsd:string"/>
		</xsd:sequence>
		<xsd:attribute name="schemaVersion" type="xsd:string"/>
		<xsd:attribute name="isOfferOnlyUpdate" type="xsd:boolean"/>
	</xsd:complexType>
	<!-- 
	###########################################################
	# Toys and ToysBaby TypeDefs 
	###########################################################
	-->
	<!--
    ##################################################
    # Recall Group
    ##################################################
    -->
	<xsd:element name="Recall">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="IsRecalled" type="xsd:boolean"/>
				<xsd:element name="RecallDescription">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:maxLength value="1500"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # Age Recommendation Group
    ##################################################
    -->
	<xsd:element name="AgeRecommendation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="MinimumManufacturerAgeRecommended" type="MinimumAgeRecommendedDimension" minOccurs="0"/>
				<xsd:element name="MaximumManufacturerAgeRecommended" type="AgeRecommendedDimension" minOccurs="0"/>
				<xsd:element name="MinimumMerchantAgeRecommended" type="MinimumAgeRecommendedDimension" minOccurs="0"/>
				<xsd:element name="MaximumMerchantAgeRecommended" type="AgeRecommendedDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # Weight Recommendation Group
    ##################################################
    -->
	<xsd:element name="WeightRecommendation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="MinimumManufacturerWeightRecommended" type="WeightIntegerDimension" minOccurs="0"/>
				<xsd:element name="MaximumManufacturerWeightRecommended" type="WeightIntegerDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # Height Recommendation Group
    ##################################################
    -->
	<xsd:element name="HeightRecommendation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="MinimumHeightRecommended" type="LengthDimension" minOccurs="0"/>
				<xsd:element name="MaximumHeightRecommended" type="LengthDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # Forward Facing Weight Group
    ##################################################
    -->
	<xsd:element name="ForwardFacingWeight">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="ForwardFacingMaximumWeight" type="WeightDimension" minOccurs="0"/>
				<xsd:element name="ForwardFacingMinimumWeight" type="WeightDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # RearFacingWeight Group
    ##################################################
    -->
	<xsd:element name="RearFacingWeight">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="RearFacingMaximumWeight" type="WeightDimension" minOccurs="0"/>
				<xsd:element name="RearFacingMinimumWeight" type="WeightDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!--
    ##################################################
    # RearFacingWeight Group
    ##################################################
    -->
	<xsd:element name="ShoulderHarnessHeight">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="ShoulderHarnessMaximumHeight" type="LengthDimension" minOccurs="0"/>
				<xsd:element name="ShoulderHarnessMinimumHeight" type="LengthDimension" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!-- 
    ##################################################
	# SpeedDimension Type 
    ##################################################
	-->
	<xsd:complexType name="SpeedDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="SpeedUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="SpeedUnitOfMeasure">
		<xsd:restriction base="String">
			<xsd:enumeration value="feet_per_minute"/>
			<xsd:enumeration value="miles_per_hour"/>
			<xsd:enumeration value="kilometers_per_hour"/>
			<xsd:enumeration value="RPM"/>
			<xsd:enumeration value="RPS"/>
			<xsd:enumeration value="meters per second"/>
			<xsd:enumeration value="centimeters per second"/>
			<xsd:enumeration value="millimeters per second"/>
			<xsd:enumeration value="revolutions_per_second"/>
			<xsd:enumeration value="feet_per_second"/>
			<xsd:enumeration value="revolutions_per_week"/>
			<xsd:enumeration value="radians_per_second"/>
			<xsd:enumeration value="degrees_per_second"/>
			<xsd:enumeration value="revolutions_per_month"/>
			<xsd:enumeration value="revolutions_per_hour"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
    ##################################################
	# ToyAward Type 
    ##################################################
	-->
	<xsd:simpleType name="ToyAwardType">
		<xsd:restriction base="MediumStringNotNull">
			<xsd:enumeration value="australia_toy_fair_boys_toy_of_the_year"/>
			<xsd:enumeration value="australia_toy_fair_toy_of_the_year"/>
			<xsd:enumeration value="baby_and_you"/>
			<xsd:enumeration value="babyworld"/>
			<xsd:enumeration value="child_magazine"/>
			<xsd:enumeration value="creative_child_magazine"/>
			<xsd:enumeration value="dr_toys_100_best_child_products"/>
			<xsd:enumeration value="energizer_battery_operated_toy_of_the_yr"/>
			<xsd:enumeration value="family_fun_toy_of_the_year_seal"/>
			<xsd:enumeration value="games_magazine"/>
			<xsd:enumeration value="gomama_today"/>
			<xsd:enumeration value="german_toy_association_toy_of_the_year"/>
			<xsd:enumeration value="hamleys_toy_of_the_year"/>
			<xsd:enumeration value="junior"/>
			<xsd:enumeration value="lion_mark"/>
			<xsd:enumeration value="mother_and_baby"/>
			<xsd:enumeration value="mum_knows_best"/>
			<xsd:enumeration value="national_parenting_approval_award"/>
			<xsd:enumeration value="norwegian_toy_association_toy_of_the_yr"/>
			<xsd:enumeration value="oppenheim_toys"/>
			<xsd:enumeration value="parents_choice_portfolio"/>
			<xsd:enumeration value="parents_magazine"/>
			<xsd:enumeration value="practical_parenting"/>
			<xsd:enumeration value="prima_baby"/>
			<xsd:enumeration value="reddot"/>
			<xsd:enumeration value="rdj_france_best_electronic_toy_of_the_yr"/>
			<xsd:enumeration value="rdj_france_best_toy_of_the_year"/>
			<xsd:enumeration value="the_times"/>
			<xsd:enumeration value="toy_wishes"/>
			<xsd:enumeration value="uk_npd_report_number_one_selling_toy"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PowerPlugType">
		<xsd:restriction base="MediumStringNotNull">
			<xsd:enumeration value="type_a_2pin_jp"/>
			<xsd:enumeration value="type_e_2pin_fr"/>
			<xsd:enumeration value="type_j_3pin_ch"/>
			<xsd:enumeration value="type_a_2pin_na"/>
			<xsd:enumeration value="type_ef_2pin_eu"/>
			<xsd:enumeration value="type_k_3pin_dk"/>
			<xsd:enumeration value="type_b_3pin_jp"/>
			<xsd:enumeration value="type_f_2pin_de"/>
			<xsd:enumeration value="type_l_3pin_it"/>
			<xsd:enumeration value="type_b_3pin_na"/>
			<xsd:enumeration value="type_g_3pin_uk"/>
			<xsd:enumeration value="type_m_3pin_za"/>
			<xsd:enumeration value="type_c_2pin_eu"/>
			<xsd:enumeration value="type_h_3pin_il"/>
			<xsd:enumeration value="type_n_3pin_br"/>
			<xsd:enumeration value="type_d_3pin_in"/>
			<xsd:enumeration value="type_i_3pin_au"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HumanInterfaceInputType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="buttons"/>
			<xsd:enumeration value="dial"/>
			<xsd:enumeration value="handwriting_recognition"/>
			<xsd:enumeration value="keyboard"/>
			<xsd:enumeration value="keypad"/>
			<xsd:enumeration value="keypad_stroke"/>
			<xsd:enumeration value="keypad_stroke"/>
			<xsd:enumeration value="microphone"/>
			<xsd:enumeration value="touch_screen"/>
			<xsd:enumeration value="touch_screen_stylus_pen"/>
			<xsd:enumeration value="trackpoint_pointing_device"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="HumanInterfaceOutputType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="screen"/>
			<xsd:enumeration value="speaker"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BluRayRegionType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="region_a"/>
			<xsd:enumeration value="region_b"/>
			<xsd:enumeration value="region_c"/>
			<xsd:enumeration value="region_free"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VinylRecordDetailsType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="lp"/>
			<xsd:enumeration value="12_single"/>
			<xsd:enumeration value="45"/>
			<xsd:enumeration value="ep"/>
			<xsd:enumeration value="78"/>
			<xsd:enumeration value="other"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TargetGenderType">
		<xsd:restriction base="StringNotNull">
			<xsd:enumeration value="male"/>
			<xsd:enumeration value="female"/>
			<xsd:enumeration value="unisex"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SweetnessAtHarvestUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="brix"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VineyardYieldUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="tons"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AllergenInformationType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="abalone"/>
			<xsd:enumeration value="abalone_free"/>
			<xsd:enumeration value="amberjack"/>
			<xsd:enumeration value="amberjack_free"/>
			<xsd:enumeration value="apple"/>
			<xsd:enumeration value="apple_free"/>
			<xsd:enumeration value="banana"/>
			<xsd:enumeration value="banana_free"/>
			<xsd:enumeration value="barley"/>
			<xsd:enumeration value="barley_free"/>
			<xsd:enumeration value="beef"/>
			<xsd:enumeration value="beef_free"/>
			<xsd:enumeration value="buckwheat"/>
			<xsd:enumeration value="buckwheat_free"/>
			<xsd:enumeration value="celery"/>
			<xsd:enumeration value="celery_free"/>
			<xsd:enumeration value="chicken_meat"/>
			<xsd:enumeration value="chicken_meat_free"/>
			<xsd:enumeration value="codfish"/>
			<xsd:enumeration value="codfish_free"/>
			<xsd:enumeration value="crab"/>
			<xsd:enumeration value="crab_free"/>
			<xsd:enumeration value="dairy"/>
			<xsd:enumeration value="dairy_free"/>
			<xsd:enumeration value="eggs"/>
			<xsd:enumeration value="egg_free"/>
			<xsd:enumeration value="fish"/>
			<xsd:enumeration value="fish_free"/>
			<xsd:enumeration value="gelatin"/>
			<xsd:enumeration value="gelatin_free"/>
			<xsd:enumeration value="gluten"/>
			<xsd:enumeration value="gluten_free"/>
			<xsd:enumeration value="kiwi"/>
			<xsd:enumeration value="kiwi_free"/>
			<xsd:enumeration value="mackerel"/>
			<xsd:enumeration value="mackerel_free"/>
			<xsd:enumeration value="melon"/>
			<xsd:enumeration value="melon_free"/>
			<xsd:enumeration value="mushroom"/>
			<xsd:enumeration value="mushroom_free"/>
			<xsd:enumeration value="octopus"/>
			<xsd:enumeration value="octopus_free"/>
			<xsd:enumeration value="orange"/>
			<xsd:enumeration value="orange_free"/>
			<xsd:enumeration value="peach"/>
			<xsd:enumeration value="peach_free"/>
			<xsd:enumeration value="peanuts"/>
			<xsd:enumeration value="peanut_free"/>
			<xsd:enumeration value="pork"/>
			<xsd:enumeration value="pork_free"/>
			<xsd:enumeration value="salmon"/>
			<xsd:enumeration value="salmon_free"/>
			<xsd:enumeration value="salmon_roe"/>
			<xsd:enumeration value="salmon_roe_free"/>
			<xsd:enumeration value="scad"/>
			<xsd:enumeration value="scad_free"/>
			<xsd:enumeration value="scallop"/>
			<xsd:enumeration value="scallop_free"/>
			<xsd:enumeration value="sesame_seeds"/>
			<xsd:enumeration value="sesame_seeds_free"/>
			<xsd:enumeration value="shellfish"/>
			<xsd:enumeration value="shellfish_free"/>
			<xsd:enumeration value="shrimp"/>
			<xsd:enumeration value="shrimp_free"/>
			<xsd:enumeration value="soy"/>
			<xsd:enumeration value="soy_free"/>
			<xsd:enumeration value="squid"/>
			<xsd:enumeration value="squid_free"/>
			<xsd:enumeration value="tree_nuts"/>
			<xsd:enumeration value="tree_nut_free"/>
			<xsd:enumeration value="tuna"/>
			<xsd:enumeration value="tuna_free"/>
			<xsd:enumeration value="walnut"/>
			<xsd:enumeration value="walnut_free"/>
			<xsd:enumeration value="yam"/>
			<xsd:enumeration value="yam_free"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerReturnPolicyType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="collectible"/>
			<xsd:enumeration value="restocking_fee"/>
			<xsd:enumeration value="standard"/>
			<xsd:enumeration value="non_returnable"/>
			<xsd:enumeration value="seasonal"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="AlcoholContentUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="percent_by_volume"/>
			<xsd:enumeration value="percent_by_weight"/>
			<xsd:enumeration value="unit_of_alcohol"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ComputerPlatformValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="game_boy_advance"/>
			<xsd:enumeration value="gameboy"/>
			<xsd:enumeration value="gameboy_color"/>
			<xsd:enumeration value="gamecube"/>
			<xsd:enumeration value="gizmondo"/>
			<xsd:enumeration value="linux"/>
			<xsd:enumeration value="macintosh"/>
			<xsd:enumeration value="n_gage"/>
			<xsd:enumeration value="nintendo_ds"/>
			<xsd:enumeration value="nintendo_NES"/>
			<xsd:enumeration value="nintendo_super_NES"/>
			<xsd:enumeration value="nintendo_wii"/>
			<xsd:enumeration value="nintendo64"/>
			<xsd:enumeration value="palm"/>
			<xsd:enumeration value="playstation"/>
			<xsd:enumeration value="playstation_2"/>
			<xsd:enumeration value="playstation_vita"/>
			<xsd:enumeration value="pocket_pc"/>
			<xsd:enumeration value="powermac"/>
			<xsd:enumeration value="sega_saturn"/>
			<xsd:enumeration value="sony_psp"/>
			<xsd:enumeration value="super_nintendo"/>
			<xsd:enumeration value="unix"/>
			<xsd:enumeration value="windows"/>
			<xsd:enumeration value="xbox"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="MagnificationDimension">
		<xsd:simpleContent>
			<xsd:extension base="xsd:positiveInteger">
				<xsd:attribute name="unitOfMeasure" type="MagnificationUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="MagnificationUnitOfMeasure">
		<xsd:restriction base="String">
			<xsd:enumeration value="multiplier_x"/>
			<xsd:enumeration value="diopters"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="OptionalMagnificationDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="MagnificationUnitOfMeasure" use="optional"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
		<xsd:simpleType name="CarSeatWeightGroupEUType">
		<xsd:restriction base="MediumStringNotNull">
			<xsd:enumeration value="group_zero"/>
			<xsd:enumeration value="group_zero_plus"/>
			<xsd:enumeration value="group_one"/>
			<xsd:enumeration value="group_two"/>
			<xsd:enumeration value="group_three"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="NeckSizeDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveDimension">
				<xsd:attribute name="unitOfMeasure" type="NeckSizeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="NeckSizeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="IN"/>
			<xsd:enumeration value="MM"/>
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="FT"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CycleLengthDimension">
		<xsd:simpleContent>
			<xsd:extension base="PositiveDimension">
				<xsd:attribute name="unitOfMeasure" type="CycleLengthUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="CycleLengthUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="CM"/>
			<xsd:enumeration value="IN"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="BootSizeDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="BootSizeUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="BootSizeUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="adult_us"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LithiumBatteryPackagingType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="batteries_contained_in_equipment"/>
			<xsd:enumeration value="batteries_only"/>
			<xsd:enumeration value="batteries_packed_with_equipment"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EnergyLabelEfficiencyClass">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="a"/>
			<xsd:enumeration value="b"/>
			<xsd:enumeration value="c"/>
			<xsd:enumeration value="d"/>
			<xsd:enumeration value="e"/>
			<xsd:enumeration value="f"/>
			<xsd:enumeration value="g"/>
			<xsd:enumeration value="a_plus"/>
			<xsd:enumeration value="a_plus_plus"/>
			<xsd:enumeration value="a_plus_plus_plus"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DistributionDesignationValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="jp_parallel_import"/>
		</xsd:restriction>
	</xsd:simpleType>	
	<xsd:complexType name="DensityDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="DensityUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="DensityUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="grams_per_square_meter"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CapacityUnit">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="CapacityUnitMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="CapacityUnitMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="cubic_centimeters"/>
			<xsd:enumeration value="cubic_feet"/>
			<xsd:enumeration value="cubic_inches"/>
			<xsd:enumeration value="cubic_meters"/>
			<xsd:enumeration value="cubic_yards"/>
			<xsd:enumeration value="cups"/>
			<xsd:enumeration value="fluid_ounces"/>
			<xsd:enumeration value="gallons"/>
			<xsd:enumeration value="imperial_gallons"/>
			<xsd:enumeration value="liters"/>
			<xsd:enumeration value="milliliters"/>
			<xsd:enumeration value="ounces"/>
			<xsd:enumeration value="pints"/>
			<xsd:enumeration value="quarts"/>
			<xsd:enumeration value="deciliters"/>
			<xsd:enumeration value="centiliters"/>
			<xsd:enumeration value="microliters"/>
			<xsd:enumeration value="nanoliters"/>
			<xsd:enumeration value="picoliters"/>
			<xsd:enumeration value="grams"/>
			<xsd:enumeration value="kilograms"/>
			<xsd:enumeration value="ounces"/>
			<xsd:enumeration value="pounds"/>
			<xsd:enumeration value="milligrams"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PEGIRatingType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ages_3_and_over"/>
			<xsd:enumeration value="ages_7_and_over"/>
			<xsd:enumeration value="ages_12_and_over"/>
			<xsd:enumeration value="ages_16_and_over"/>
			<xsd:enumeration value="ages_18_and_over"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="USKRatingType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="ages_6_and_over"/>
			<xsd:enumeration value="ages_12_and_over"/>
			<xsd:enumeration value="ages_16_and_over"/>
			<xsd:enumeration value="ages_18_and_over"/>
			<xsd:enumeration value="cannot_publicize"/>
			<xsd:enumeration value="checked_by_legal_department"/>
			<xsd:enumeration value="not_checked"/>
			<xsd:enumeration value="without_age_limitation"/>
			<xsd:enumeration value="unknown"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Originality">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Original"/>
			<xsd:enumeration value="Original Limited Edition"/>
			<xsd:enumeration value="Reproduced"/>
			<xsd:enumeration value="Reproduced Limited Edition"/>
			<xsd:enumeration value="Replica"/>
			<xsd:enumeration value="Replica Limited Edition"/>
			<xsd:enumeration value="Limited Edition"/>
			<xsd:enumeration value="Manufactured"/>
			<xsd:enumeration value="Licensed"/>
			<xsd:enumeration value="Vintage"/>
		</xsd:restriction>
	</xsd:simpleType>
        <xsd:complexType name="ServingDimension">
                <xsd:simpleContent>
                        <xsd:extension base="PositiveDimension">
                               <xsd:attribute name="unitOfMeasure" type="ServingUnit" use="required"/>
                        </xsd:extension>
                </xsd:simpleContent>
        </xsd:complexType>
        <xsd:simpleType name="ServingUnit">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="percent-fda"/>
                        <xsd:enumeration value="mg"/>
                        <xsd:enumeration value="gr"/>
                        <xsd:enumeration value="ml"/>
                        <xsd:enumeration value="grams"/>
                        <xsd:enumeration value="milligrams"/>
                        <xsd:enumeration value="milliliters"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="BinaryInteger">
                <xsd:restriction base="xsd:integer">
                        <xsd:minInclusive value="0"/>
                        <xsd:maxInclusive value="1"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="OrganizationTaxRoles">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="doctor"/>
                        <xsd:enumeration value="dentist"/>
                        <xsd:enumeration value="hospital"/>
                        <xsd:enumeration value="clinic"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="B2bQuantityPriceTypeValues">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="fixed"/>
                        <xsd:enumeration value="percent"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="IsSourcingOnDemandValues">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="yes"/>
                        <xsd:enumeration value="no"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="UKMedicinesClassUnit">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="professional_use_only"/>
                        <xsd:enumeration value="general_sales_list"/>
                        <xsd:enumeration value="pharmacy_p_line"/>
                        <xsd:enumeration value="prescription_only"/>
                </xsd:restriction>
        </xsd:simpleType>
			<xsd:complexType name="MaximumPowerType">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="MaximumPowerUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
		<xsd:simpleType name="MaximumPowerUnitOfMeasure">
		<xsd:restriction base="StringNotNull">
			<xsd:enumeration value="W"/>
			<xsd:enumeration value="KW"/>
		</xsd:restriction>
	</xsd:simpleType>
		<xsd:simpleType name="PricingStrategyValues">
                <xsd:restriction base="xsd:string">
                        <xsd:enumeration value="bulk_by_uom"/>
                        <xsd:enumeration value="catch_by_uom"/>
						<xsd:enumeration value="produce_by_uom"/>
						<xsd:enumeration value="produce_by_each"/>
                </xsd:restriction>
        </xsd:simpleType>
		<xsd:simpleType name="GenericUnit">
                <xsd:restriction base="xsd:string">
						<xsd:enumeration value="kilograms"/>
                        <xsd:enumeration value="grams"/>
                        <xsd:enumeration value="milligrams"/>
                        <xsd:enumeration value="ounces"/>
                        <xsd:enumeration value="pounds"/>
                        <xsd:enumeration value="inches"/>
                        <xsd:enumeration value="feet"/>
                        <xsd:enumeration value="meters"/>
                        <xsd:enumeration value="centimeters"/>
                        <xsd:enumeration value="millimeters"/>
                        <xsd:enumeration value="square_meters"/>
                        <xsd:enumeration value="square_centimeters"/>
                        <xsd:enumeration value="square_feet"/>
                        <xsd:enumeration value="square_inches"/>
                        <xsd:enumeration value="gallons"/>
                        <xsd:enumeration value="pints"/>
                        <xsd:enumeration value="quarts"/>
                        <xsd:enumeration value="fluid_ounces"/>
                        <xsd:enumeration value="liters"/>
                        <xsd:enumeration value="cubic_meters"/>
                        <xsd:enumeration value="cubic_feet"/>
                        <xsd:enumeration value="cubic_inches"/>
                        <xsd:enumeration value="cubic_centimeters"/>
                </xsd:restriction>
        </xsd:simpleType>
			
<!--
		
    ##################################################
    # Nicotine Concentration Unit
    ##################################################
    
	-->	
	<xsd:simpleType name="NicotineConcentrationUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="milligrams_per_milliliter"/>
		</xsd:restriction>
	</xsd:simpleType>

<!--
		
    ##################################################
    # Gdpr Risk Type
    ##################################################
    
	-->	
	<xsd:simpleType name="GdprRiskType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="manufacturer_website_registration"/>
			<xsd:enumeration value="no_electronic_information_stored"/>
			<xsd:enumeration value="user_setting_information_storage"/>
			<xsd:enumeration value="pin_or_biometric_recognition_lock"/>
			<xsd:enumeration value="cloud_account_connectivity"/>
			<xsd:enumeration value="physical_or_cloud_data_storage"/>
		</xsd:restriction>
	</xsd:simpleType>
		
<!--
		
    ##################################################
    # Sim Card Slot Count Type
    ##################################################
    
	-->	
	<xsd:simpleType name="SimCardSlotCountType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="single_sim"/>
			<xsd:enumeration value="dual_sim"/>
			<xsd:enumeration value="triple_sim"/>
			<xsd:enumeration value="quad_sim"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!--
		
    ##################################################
    # Radiation Unit Dimension
    ##################################################
    
	-->	
	<xsd:complexType name="RadiationUnitDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute type="RadiationUnitOfMeasure" name="unitOfMeasure" use="optional" />
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
	
	<xsd:simpleType name="RadiationUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="watts_per_kilogram"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!--
		
    ##################################################
    # Refresh Rate Unit Dimension
    ##################################################
    
	-->	
	
	<xsd:complexType name="RefreshRateDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute type="RefreshRateUnitOfMeasure" name="unitOfMeasure" use="optional" />
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
	
	<xsd:simpleType name="RefreshRateUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="GHz"/>
			<xsd:enumeration value="MHz"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	    <xsd:simpleType name="EuAcousticNoiseValue">
        <xsd:restriction base="xsd:positiveInteger">
            <xsd:enumeration value="1"/>
            <xsd:enumeration value="2"/>
            <xsd:enumeration value="3"/>
        </xsd:restriction>
    </xsd:simpleType>
	<!--
		
    ##################################################
    # Air Efficiency Unit Dimension
    ##################################################
    
	-->	
	
	<xsd:complexType name="AirEfficiencyDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="AirEfficiencyUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>

    <xsd:simpleType name="AirEfficiencyUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="cubic_feet_per_minute_per_watt"/>
		</xsd:restriction>
	</xsd:simpleType>
	
	<!--
		
    ##################################################
    # Melting Temperature Unit Dimension
    ##################################################
    
	-->	
	
	<xsd:complexType name="MeltingTemperatureDimension">
		<xsd:simpleContent>
			<xsd:extension base="Dimension">
				<xsd:attribute name="unitOfMeasure" type="TemperatureRatingUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	
	<!-- 
    ##################################################
	# MileageUnit Type 
    ##################################################
	-->
	
	<xsd:complexType name="MileageUnit">
		<xsd:simpleContent>
			<xsd:extension base="xsd:string">
				<xsd:attribute name="unitOfMeasure" type="MileageUnitOfMeasure" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<xsd:simpleType name="MileageUnitOfMeasure">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="miles"/>
			<xsd:enumeration value="Kilometer"/>
		</xsd:restriction>
	</xsd:simpleType>

		<xsd:annotation>
			<xsd:documentation>CountryAsLabeled Values</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleType name="CountryAsLabeledValues">
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="PR"/>
				<xsd:enumeration value="PS"/>
				<xsd:enumeration value="PT"/>
				<xsd:enumeration value="PW"/>
				<xsd:enumeration value="PY"/>
				<xsd:enumeration value="QA"/>
				<xsd:enumeration value="AC"/>
				<xsd:enumeration value="AD"/>
				<xsd:enumeration value="AE"/>
				<xsd:enumeration value="AF"/>
				<xsd:enumeration value="AG"/>
				<xsd:enumeration value="AI"/>
				<xsd:enumeration value="AL"/>
				<xsd:enumeration value="AM"/>
				<xsd:enumeration value="AN"/>
				<xsd:enumeration value="AO"/>
				<xsd:enumeration value="AQ"/>
				<xsd:enumeration value="AR"/>
				<xsd:enumeration value="AS"/>
				<xsd:enumeration value="AT"/>
				<xsd:enumeration value="RE"/>
				<xsd:enumeration value="AU"/>
				<xsd:enumeration value="AW"/>
				<xsd:enumeration value="AX"/>
				<xsd:enumeration value="AZ"/>
				<xsd:enumeration value="RO"/>
				<xsd:enumeration value="BA"/>
				<xsd:enumeration value="BB"/>
				<xsd:enumeration value="RS"/>
				<xsd:enumeration value="BD"/>
				<xsd:enumeration value="BE"/>
				<xsd:enumeration value="RU"/>
				<xsd:enumeration value="BF"/>
				<xsd:enumeration value="BG"/>
				<xsd:enumeration value="RW"/>
				<xsd:enumeration value="BH"/>
				<xsd:enumeration value="BI"/>
				<xsd:enumeration value="BJ"/>
				<xsd:enumeration value="BL"/>
				<xsd:enumeration value="BM"/>
				<xsd:enumeration value="BN"/>
				<xsd:enumeration value="BO"/>
				<xsd:enumeration value="SA"/>
				<xsd:enumeration value="SB"/>
				<xsd:enumeration value="BQ"/>
				<xsd:enumeration value="BR"/>
				<xsd:enumeration value="SC"/>
				<xsd:enumeration value="BS"/>
				<xsd:enumeration value="SD"/>
				<xsd:enumeration value="BT"/>
				<xsd:enumeration value="SE"/>
				<xsd:enumeration value="BV"/>
				<xsd:enumeration value="SG"/>
				<xsd:enumeration value="BW"/>
				<xsd:enumeration value="SH"/>
				<xsd:enumeration value="SI"/>
				<xsd:enumeration value="BY"/>
				<xsd:enumeration value="SJ"/>
				<xsd:enumeration value="BZ"/>
				<xsd:enumeration value="SK"/>
				<xsd:enumeration value="SL"/>
				<xsd:enumeration value="SM"/>
				<xsd:enumeration value="SN"/>
				<xsd:enumeration value="SO"/>
				<xsd:enumeration value="CA"/>
				<xsd:enumeration value="SR"/>
				<xsd:enumeration value="CC"/>
				<xsd:enumeration value="SS"/>
				<xsd:enumeration value="CD"/>
				<xsd:enumeration value="ST"/>
				<xsd:enumeration value="CF"/>
				<xsd:enumeration value="SV"/>
				<xsd:enumeration value="CG"/>
				<xsd:enumeration value="CH"/>
				<xsd:enumeration value="SX"/>
				<xsd:enumeration value="CI"/>
				<xsd:enumeration value="SY"/>
				<xsd:enumeration value="SZ"/>
				<xsd:enumeration value="CK"/>
				<xsd:enumeration value="CL"/>
				<xsd:enumeration value="CM"/>
				<xsd:enumeration value="CN"/>
				<xsd:enumeration value="CO"/>
				<xsd:enumeration value="TA"/>
				<xsd:enumeration value="CR"/>
				<xsd:enumeration value="TC"/>
				<xsd:enumeration value="TD"/>
				<xsd:enumeration value="CS"/>
				<xsd:enumeration value="CU"/>
				<xsd:enumeration value="TF"/>
				<xsd:enumeration value="CV"/>
				<xsd:enumeration value="TG"/>
				<xsd:enumeration value="TH"/>
				<xsd:enumeration value="CW"/>
				<xsd:enumeration value="CX"/>
				<xsd:enumeration value="CY"/>
				<xsd:enumeration value="TJ"/>
				<xsd:enumeration value="CZ"/>
				<xsd:enumeration value="TK"/>
				<xsd:enumeration value="TL"/>
				<xsd:enumeration value="TM"/>
				<xsd:enumeration value="TN"/>
				<xsd:enumeration value="TO"/>
				<xsd:enumeration value="TP"/>
				<xsd:enumeration value="TR"/>
				<xsd:enumeration value="TT"/>
				<xsd:enumeration value="DE"/>
				<xsd:enumeration value="TV"/>
				<xsd:enumeration value="TW"/>
				<xsd:enumeration value="DJ"/>
				<xsd:enumeration value="TZ"/>
				<xsd:enumeration value="DK"/>
				<xsd:enumeration value="DM"/>
				<xsd:enumeration value="DO"/>
				<xsd:enumeration value="UA"/>
				<xsd:enumeration value="UG"/>
				<xsd:enumeration value="DZ"/>
				<xsd:enumeration value="UK"/>
				<xsd:enumeration value="UM"/>
				<xsd:enumeration value="EC"/>
				<xsd:enumeration value="US"/>
				<xsd:enumeration value="EE"/>
				<xsd:enumeration value="EG"/>
				<xsd:enumeration value="EH"/>
				<xsd:enumeration value="UY"/>
				<xsd:enumeration value="UZ"/>
				<xsd:enumeration value="VA"/>
				<xsd:enumeration value="ER"/>
				<xsd:enumeration value="VC"/>
				<xsd:enumeration value="ES"/>
				<xsd:enumeration value="ET"/>
				<xsd:enumeration value="VE"/>
				<xsd:enumeration value="VG"/>
				<xsd:enumeration value="VI"/>
				<xsd:enumeration value="VN"/>
				<xsd:enumeration value="VU"/>
				<xsd:enumeration value="FI"/>
				<xsd:enumeration value="FJ"/>
				<xsd:enumeration value="FK"/>
				<xsd:enumeration value="FM"/>
				<xsd:enumeration value="FO"/>
				<xsd:enumeration value="FR"/>
				<xsd:enumeration value="WD"/>
				<xsd:enumeration value="WF"/>
				<xsd:enumeration value="GA"/>
				<xsd:enumeration value="GB"/>
				<xsd:enumeration value="WS"/>
				<xsd:enumeration value="GD"/>
				<xsd:enumeration value="GE"/>
				<xsd:enumeration value="GF"/>
				<xsd:enumeration value="GG"/>
				<xsd:enumeration value="GH"/>
				<xsd:enumeration value="GI"/>
				<xsd:enumeration value="WZ"/>
				<xsd:enumeration value="GL"/>
				<xsd:enumeration value="GM"/>
				<xsd:enumeration value="GN"/>
				<xsd:enumeration value="GP"/>
				<xsd:enumeration value="GQ"/>
				<xsd:enumeration value="XB"/>
				<xsd:enumeration value="GR"/>
				<xsd:enumeration value="XC"/>
				<xsd:enumeration value="GS"/>
				<xsd:enumeration value="GT"/>
				<xsd:enumeration value="XE"/>
				<xsd:enumeration value="GU"/>
				<xsd:enumeration value="GW"/>
				<xsd:enumeration value="GY"/>
				<xsd:enumeration value="XK"/>
				<xsd:enumeration value="XM"/>
				<xsd:enumeration value="XN"/>
				<xsd:enumeration value="XY"/>
				<xsd:enumeration value="HK"/>
				<xsd:enumeration value="HM"/>
				<xsd:enumeration value="HN"/>
				<xsd:enumeration value="HR"/>
				<xsd:enumeration value="HT"/>
				<xsd:enumeration value="YE"/>
				<xsd:enumeration value="HU"/>
				<xsd:enumeration value="IC"/>
				<xsd:enumeration value="ID"/>
				<xsd:enumeration value="YT"/>
				<xsd:enumeration value="IE"/>
				<xsd:enumeration value="YU"/>
				<xsd:enumeration value="IL"/>
				<xsd:enumeration value="IM"/>
				<xsd:enumeration value="IN"/>
				<xsd:enumeration value="IO"/>
				<xsd:enumeration value="ZA"/>
				<xsd:enumeration value="IQ"/>
				<xsd:enumeration value="IR"/>
				<xsd:enumeration value="IS"/>
				<xsd:enumeration value="IT"/>
				<xsd:enumeration value="ZM"/>
				<xsd:enumeration value="ZR"/>
				<xsd:enumeration value="JE"/>
				<xsd:enumeration value="ZW"/>
				<xsd:enumeration value="JM"/>
				<xsd:enumeration value="JO"/>
				<xsd:enumeration value="JP"/>
				<xsd:enumeration value="unknown"/>
				<xsd:enumeration value="KE"/>
				<xsd:enumeration value="KG"/>
				<xsd:enumeration value="KH"/>
				<xsd:enumeration value="KI"/>
				<xsd:enumeration value="KM"/>
				<xsd:enumeration value="KN"/>
				<xsd:enumeration value="KP"/>
				<xsd:enumeration value="KR"/>
				<xsd:enumeration value="KW"/>
				<xsd:enumeration value="KY"/>
				<xsd:enumeration value="KZ"/>
				<xsd:enumeration value="LA"/>
				<xsd:enumeration value="LB"/>
				<xsd:enumeration value="LC"/>
				<xsd:enumeration value="LI"/>
				<xsd:enumeration value="LK"/>
				<xsd:enumeration value="LR"/>
				<xsd:enumeration value="LS"/>
				<xsd:enumeration value="LT"/>
				<xsd:enumeration value="LU"/>
				<xsd:enumeration value="LV"/>
				<xsd:enumeration value="LY"/>
				<xsd:enumeration value="MA"/>
				<xsd:enumeration value="MC"/>
				<xsd:enumeration value="MD"/>
				<xsd:enumeration value="ME"/>
				<xsd:enumeration value="MF"/>
				<xsd:enumeration value="MG"/>
				<xsd:enumeration value="MH"/>
				<xsd:enumeration value="MK"/>
				<xsd:enumeration value="ML"/>
				<xsd:enumeration value="MM"/>
				<xsd:enumeration value="MN"/>
				<xsd:enumeration value="MO"/>
				<xsd:enumeration value="MP"/>
				<xsd:enumeration value="MQ"/>
				<xsd:enumeration value="MR"/>
				<xsd:enumeration value="MS"/>
				<xsd:enumeration value="MT"/>
				<xsd:enumeration value="MU"/>
				<xsd:enumeration value="MV"/>
				<xsd:enumeration value="MW"/>
				<xsd:enumeration value="MX"/>
				<xsd:enumeration value="MY"/>
				<xsd:enumeration value="MZ"/>
				<xsd:enumeration value="NA"/>
				<xsd:enumeration value="NC"/>
				<xsd:enumeration value="NE"/>
				<xsd:enumeration value="NF"/>
				<xsd:enumeration value="NG"/>
				<xsd:enumeration value="NI"/>
				<xsd:enumeration value="NL"/>
				<xsd:enumeration value="NO"/>
				<xsd:enumeration value="NP"/>
				<xsd:enumeration value="NR"/>
				<xsd:enumeration value="NU"/>
				<xsd:enumeration value="NZ"/>
				<xsd:enumeration value="OM"/>
				<xsd:enumeration value="PA"/>
				<xsd:enumeration value="PE"/>
				<xsd:enumeration value="PF"/>
				<xsd:enumeration value="PG"/>
				<xsd:enumeration value="PH"/>
				<xsd:enumeration value="PK"/>
				<xsd:enumeration value="PL"/>
				<xsd:enumeration value="PM"/>
				<xsd:enumeration value="PN"/>
			</xsd:restriction>
		</xsd:simpleType>
		<xsd:annotation>
			<xsd:documentation>LanguageValue Values</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleType name="LanguageValues">
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="german"/>
				<xsd:enumeration value="aragonese"/>
				<xsd:enumeration value="sidamo"/>
				<xsd:enumeration value="altaic_languages"/>
				<xsd:enumeration value="luo"/>
				<xsd:enumeration value="papuan_languages"/>
				<xsd:enumeration value="khotanese"/>
				<xsd:enumeration value="kinyarwanda"/>
				<xsd:enumeration value="elamite"/>
				<xsd:enumeration value="hausa"/>
				<xsd:enumeration value="dutch"/>
				<xsd:enumeration value="old_french"/>
				<xsd:enumeration value="classical_syriac"/>
				<xsd:enumeration value="flemish"/>
				<xsd:enumeration value="kokborok"/>
				<xsd:enumeration value="songhai_languages"/>
				<xsd:enumeration value="nepali"/>
				<xsd:enumeration value="makasar"/>
				<xsd:enumeration value="ancient_greek"/>
				<xsd:enumeration value="sardinian"/>
				<xsd:enumeration value="niger_kordofanian_languages"/>
				<xsd:enumeration value="chinook_jargon"/>
				<xsd:enumeration value="cayuga"/>
				<xsd:enumeration value="castillian"/>
				<xsd:enumeration value="old_irish"/>
				<xsd:enumeration value="persian"/>
				<xsd:enumeration value="aleut"/>
				<xsd:enumeration value="jula"/>
				<xsd:enumeration value="siksika"/>
				<xsd:enumeration value="pohnpeian"/>
				<xsd:enumeration value="nzima"/>
				<xsd:enumeration value="chiricahua"/>
				<xsd:enumeration value="siswati"/>
				<xsd:enumeration value="sumerian"/>
				<xsd:enumeration value="north_american_indian_languages"/>
				<xsd:enumeration value="pidgin_english"/>
				<xsd:enumeration value="minangkabau"/>
				<xsd:enumeration value="dravidian_languages"/>
				<xsd:enumeration value="gorontalo"/>
				<xsd:enumeration value="slovak"/>
				<xsd:enumeration value="hebrew"/>
				<xsd:enumeration value="sasak"/>
				<xsd:enumeration value="northern_sami"/>
				<xsd:enumeration value="ekajuk"/>
				<xsd:enumeration value="chechen"/>
				<xsd:enumeration value="selkup"/>
				<xsd:enumeration value="kirundi"/>
				<xsd:enumeration value="braj"/>
				<xsd:enumeration value="celtic_languages"/>
				<xsd:enumeration value="bengali"/>
				<xsd:enumeration value="azerbaijani"/>
				<xsd:enumeration value="upper_sorbian"/>
				<xsd:enumeration value="sorbian_languages"/>
				<xsd:enumeration value="scots"/>
				<xsd:enumeration value="afrikaans"/>
				<xsd:enumeration value="sami"/>
				<xsd:enumeration value="umbundu"/>
				<xsd:enumeration value="australian_languages"/>
				<xsd:enumeration value="assyrian"/>
				<xsd:enumeration value="navaho"/>
				<xsd:enumeration value="khoisan_languages"/>
				<xsd:enumeration value="chamic_languages"/>
				<xsd:enumeration value="lithuanian"/>
				<xsd:enumeration value="bambara"/>
				<xsd:enumeration value="vietnamese"/>
				<xsd:enumeration value="bini"/>
				<xsd:enumeration value="maltese"/>
				<xsd:enumeration value="slave_athapascan"/>
				<xsd:enumeration value="mandar"/>
				<xsd:enumeration value="susu"/>
				<xsd:enumeration value="lule_sami"/>
				<xsd:enumeration value="apache_languages"/>
				<xsd:enumeration value="artificial_languages"/>
				<xsd:enumeration value="algonquian_languages"/>
				<xsd:enumeration value="bikol"/>
				<xsd:enumeration value="sanskrit"/>
				<xsd:enumeration value="tuvinian"/>
				<xsd:enumeration value="bihari"/>
				<xsd:enumeration value="wakashan_languages"/>
				<xsd:enumeration value="gaelic_scots"/>
				<xsd:enumeration value="tatar"/>
				<xsd:enumeration value="luba_katanga"/>
				<xsd:enumeration value="kumyk"/>
				<xsd:enumeration value="welsh"/>
				<xsd:enumeration value="chinese"/>
				<xsd:enumeration value="japanese"/>
				<xsd:enumeration value="beja"/>
				<xsd:enumeration value="norwegian_bokmal"/>
				<xsd:enumeration value="tzeltal"/>
				<xsd:enumeration value="tiv"/>
				<xsd:enumeration value="angika"/>
				<xsd:enumeration value="scots_gaelic"/>
				<xsd:enumeration value="garo"/>
				<xsd:enumeration value="otomian_languages"/>
				<xsd:enumeration value="north_ndebele"/>
				<xsd:enumeration value="dhivehi"/>
				<xsd:enumeration value="aramaic"/>
				<xsd:enumeration value="rarotongan"/>
				<xsd:enumeration value="setswana"/>
				<xsd:enumeration value="kanuri"/>
				<xsd:enumeration value="mon_khmer_languages"/>
				<xsd:enumeration value="haryanvi"/>
				<xsd:enumeration value="zaza"/>
				<xsd:enumeration value="lushai"/>
				<xsd:enumeration value="ijo_languages"/>
				<xsd:enumeration value="zande_languages"/>
				<xsd:enumeration value="indic"/>
				<xsd:enumeration value="sandawe"/>
				<xsd:enumeration value="fon"/>
				<xsd:enumeration value="ndonga"/>
				<xsd:enumeration value="xhosa"/>
				<xsd:enumeration value="judeo_persian"/>
				<xsd:enumeration value="taiwanese_chinese"/>
				<xsd:enumeration value="karen_languages"/>
				<xsd:enumeration value="bribri"/>
				<xsd:enumeration value="marathi"/>
				<xsd:enumeration value="sinhalese"/>
				<xsd:enumeration value="inuktitut"/>
				<xsd:enumeration value="tigre"/>
				<xsd:enumeration value="slovene"/>
				<xsd:enumeration value="choctaw"/>
				<xsd:enumeration value="ga"/>
				<xsd:enumeration value="northern_frisian"/>
				<xsd:enumeration value="yugoslavian"/>
				<xsd:enumeration value="mirandese"/>
				<xsd:enumeration value="nauru"/>
				<xsd:enumeration value="spanish"/>
				<xsd:enumeration value="somali"/>
				<xsd:enumeration value="dakota"/>
				<xsd:enumeration value="syriac"/>
				<xsd:enumeration value="french_canadian"/>
				<xsd:enumeration value="lower_sorbian"/>
				<xsd:enumeration value="punjabi"/>
				<xsd:enumeration value="inari_sami"/>
				<xsd:enumeration value="gwichin"/>
				<xsd:enumeration value="inuktitun"/>
				<xsd:enumeration value="erzya"/>
				<xsd:enumeration value="cushitic_languages"/>
				<xsd:enumeration value="kikuyu"/>
				<xsd:enumeration value="quechua"/>
				<xsd:enumeration value="nilo_saharan_languages"/>
				<xsd:enumeration value="sino_tibetan"/>
				<xsd:enumeration value="kalaallisut"/>
				<xsd:enumeration value="asturian"/>
				<xsd:enumeration value="romance"/>
				<xsd:enumeration value="pampanga"/>
				<xsd:enumeration value="fanti"/>
				<xsd:enumeration value="bislama"/>
				<xsd:enumeration value="bahasa"/>
				<xsd:enumeration value="aromanian"/>
				<xsd:enumeration value="madurese"/>
				<xsd:enumeration value="pedi"/>
				<xsd:enumeration value="norwegian"/>
				<xsd:enumeration value="herero"/>
				<xsd:enumeration value="yoruba"/>
				<xsd:enumeration value="ottoman_turkish"/>
				<xsd:enumeration value="latin"/>
				<xsd:enumeration value="middle_english"/>
				<xsd:enumeration value="gilbertese"/>
				<xsd:enumeration value="french"/>
				<xsd:enumeration value="georgian"/>
				<xsd:enumeration value="portuguese_brazilian"/>
				<xsd:enumeration value="old_provencal"/>
				<xsd:enumeration value="tamashek"/>
				<xsd:enumeration value="serbian"/>
				<xsd:enumeration value="marshallese"/>
				<xsd:enumeration value="kru_languages"/>
				<xsd:enumeration value="kashubian"/>
				<xsd:enumeration value="chhattisgarhi"/>
				<xsd:enumeration value="kosraean"/>
				<xsd:enumeration value="hindi"/>
				<xsd:enumeration value="esperanto"/>
				<xsd:enumeration value="kazakh"/>
				<xsd:enumeration value="gayo"/>
				<xsd:enumeration value="afghan_pashtu"/>
				<xsd:enumeration value="rapanui"/>
				<xsd:enumeration value="ewondo"/>
				<xsd:enumeration value="egyptian"/>
				<xsd:enumeration value="gibberish"/>
				<xsd:enumeration value="khmer"/>
				<xsd:enumeration value="banda_languages"/>
				<xsd:enumeration value="hungarian"/>
				<xsd:enumeration value="moksha"/>
				<xsd:enumeration value="creek"/>
				<xsd:enumeration value="luiseno"/>
				<xsd:enumeration value="karelian"/>
				<xsd:enumeration value="greenlandic"/>
				<xsd:enumeration value="samoan"/>
				<xsd:enumeration value="romansch"/>
				<xsd:enumeration value="berber"/>
				<xsd:enumeration value="cree"/>
				<xsd:enumeration value="gothic"/>
				<xsd:enumeration value="nyamwezi"/>
				<xsd:enumeration value="magahi"/>
				<xsd:enumeration value="shona"/>
				<xsd:enumeration value="lunda"/>
				<xsd:enumeration value="uzbek"/>
				<xsd:enumeration value="arawak"/>
				<xsd:enumeration value="friulian"/>
				<xsd:enumeration value="fiji"/>
				<xsd:enumeration value="turkmen"/>
				<xsd:enumeration value="old_persian"/>
				<xsd:enumeration value="shan"/>
				<xsd:enumeration value="latvian"/>
				<xsd:enumeration value="old_english"/>
				<xsd:enumeration value="tsonga"/>
				<xsd:enumeration value="faroese"/>
				<xsd:enumeration value="votic"/>
				<xsd:enumeration value="ossetian"/>
				<xsd:enumeration value="iroquoian_languages"/>
				<xsd:enumeration value="yupik_languages"/>
				<xsd:enumeration value="dargwa"/>
				<xsd:enumeration value="papiamento"/>
				<xsd:enumeration value="phoenician"/>
				<xsd:enumeration value="mandingo"/>
				<xsd:enumeration value="delaware"/>
				<xsd:enumeration value="low_german"/>
				<xsd:enumeration value="lao"/>
				<xsd:enumeration value="mongolian"/>
				<xsd:enumeration value="telugu"/>
				<xsd:enumeration value="abkhazian"/>
				<xsd:enumeration value="chagatai"/>
				<xsd:enumeration value="achinese"/>
				<xsd:enumeration value="udmurt"/>
				<xsd:enumeration value="siouan_languages"/>
				<xsd:enumeration value="malagasy"/>
				<xsd:enumeration value="pashto"/>
				<xsd:enumeration value="thai"/>
				<xsd:enumeration value="efik"/>
				<xsd:enumeration value="luxembourgish"/>
				<xsd:enumeration value="bodo"/>
				<xsd:enumeration value="gbaya"/>
				<xsd:enumeration value="kara_kalpak"/>
				<xsd:enumeration value="eastern_frisian"/>
				<xsd:enumeration value="nepal_bhasa"/>
				<xsd:enumeration value="malay"/>
				<xsd:enumeration value="germanic_languages"/>
				<xsd:enumeration value="tsimshian"/>
				<xsd:enumeration value="hokkien"/>
				<xsd:enumeration value="adangme"/>
				<xsd:enumeration value="dogri"/>
				<xsd:enumeration value="lamba"/>
				<xsd:enumeration value="sogdian"/>
				<xsd:enumeration value="scandanavian_languages"/>
				<xsd:enumeration value="middle_french"/>
				<xsd:enumeration value="afrihili"/>
				<xsd:enumeration value="estonian"/>
				<xsd:enumeration value="sichuan_yi"/>
				<xsd:enumeration value="portuguese_creole"/>
				<xsd:enumeration value="igbo"/>
				<xsd:enumeration value="awadhi"/>
				<xsd:enumeration value="ukranian"/>
				<xsd:enumeration value="interlingua"/>
				<xsd:enumeration value="gahrwali"/>
				<xsd:enumeration value="mizo"/>
				<xsd:enumeration value="interlingue"/>
				<xsd:enumeration value="cantonese_chinese"/>
				<xsd:enumeration value="albanian"/>
				<xsd:enumeration value="italian"/>
				<xsd:enumeration value="adygei"/>
				<xsd:enumeration value="korean"/>
				<xsd:enumeration value="khasi"/>
				<xsd:enumeration value="tupi_languages"/>
				<xsd:enumeration value="lojban"/>
				<xsd:enumeration value="ewe"/>
				<xsd:enumeration value="gullah"/>
				<xsd:enumeration value="simplified_chinese"/>
				<xsd:enumeration value="prakrit_languages"/>
				<xsd:enumeration value="akan"/>
				<xsd:enumeration value="kashmiri"/>
				<xsd:enumeration value="bosnian"/>
				<xsd:enumeration value="klingon"/>
				<xsd:enumeration value="tai_languages"/>
				<xsd:enumeration value="dzongkha"/>
				<xsd:enumeration value="belgian"/>
				<xsd:enumeration value="manipuri"/>
				<xsd:enumeration value="lapp"/>
				<xsd:enumeration value="guarani"/>
				<xsd:enumeration value="valencian"/>
				<xsd:enumeration value="sangho"/>
				<xsd:enumeration value="yapese"/>
				<xsd:enumeration value="zuni"/>
				<xsd:enumeration value="kuanyama"/>
				<xsd:enumeration value="bhutani"/>
				<xsd:enumeration value="english"/>
				<xsd:enumeration value="sign_language"/>
				<xsd:enumeration value="czech"/>
				<xsd:enumeration value="hawaiian"/>
				<xsd:enumeration value="south_ndebele"/>
				<xsd:enumeration value="palauan"/>
				<xsd:enumeration value="geez"/>
				<xsd:enumeration value="austronesian"/>
				<xsd:enumeration value="tahitian"/>
				<xsd:enumeration value="ladino"/>
				<xsd:enumeration value="dinka"/>
				<xsd:enumeration value="komi"/>
				<xsd:enumeration value="bhojpuri"/>
				<xsd:enumeration value="old_norse"/>
				<xsd:enumeration value="walloon"/>
				<xsd:enumeration value="central_american_indian_languages"/>
				<xsd:enumeration value="javanese"/>
				<xsd:enumeration value="belarusian"/>
				<xsd:enumeration value="tibetan"/>
				<xsd:enumeration value="zulu"/>
				<xsd:enumeration value="cherokee"/>
				<xsd:enumeration value="swahili"/>
				<xsd:enumeration value="iranian_languages"/>
				<xsd:enumeration value="himachali_languages"/>
				<xsd:enumeration value="oriya"/>
				<xsd:enumeration value="galibi_carib"/>
				<xsd:enumeration value="middle_irish"/>
				<xsd:enumeration value="icelandic"/>
				<xsd:enumeration value="classical_newari"/>
				<xsd:enumeration value="baltic_languages"/>
				<xsd:enumeration value="kamba"/>
				<xsd:enumeration value="twi"/>
				<xsd:enumeration value="afro_asiatic_languages"/>
				<xsd:enumeration value="gujarati"/>
				<xsd:enumeration value="nyankole"/>
				<xsd:enumeration value="baluchi"/>
				<xsd:enumeration value="uighur"/>
				<xsd:enumeration value="occitan"/>
				<xsd:enumeration value="pangasinan"/>
				<xsd:enumeration value="semitic_languages"/>
				<xsd:enumeration value="sundanese"/>
				<xsd:enumeration value="nko"/>
				<xsd:enumeration value="tamil"/>
				<xsd:enumeration value="gondi"/>
				<xsd:enumeration value="judeo_arabic"/>
				<xsd:enumeration value="arapaho"/>
				<xsd:enumeration value="micmac"/>
				<xsd:enumeration value="mohawk"/>
				<xsd:enumeration value="yao"/>
				<xsd:enumeration value="sranan_tongo"/>
				<xsd:enumeration value="farsi"/>
				<xsd:enumeration value="bliss"/>
				<xsd:enumeration value="gallegan"/>
				<xsd:enumeration value="buryat"/>
				<xsd:enumeration value="manx"/>
				<xsd:enumeration value="tagalog"/>
				<xsd:enumeration value="assamese"/>
				<xsd:enumeration value="kurukh"/>
				<xsd:enumeration value="swiss_german"/>
				<xsd:enumeration value="scandinavian_languages"/>
				<xsd:enumeration value="old_high_german"/>
				<xsd:enumeration value="mandarin_chinese"/>
				<xsd:enumeration value="polish"/>
				<xsd:enumeration value="kabyle"/>
				<xsd:enumeration value="galician"/>
				<xsd:enumeration value="mayan"/>
				<xsd:enumeration value="ukrainian"/>
				<xsd:enumeration value="bamileke_languages"/>
				<xsd:enumeration value="zenaga"/>
				<xsd:enumeration value="kalmyk"/>
				<xsd:enumeration value="ojibwa"/>
				<xsd:enumeration value="tereno"/>
				<xsd:enumeration value="karachay_balkar"/>
				<xsd:enumeration value="yakut"/>
				<xsd:enumeration value="filipino"/>
				<xsd:enumeration value="rajasthani"/>
				<xsd:enumeration value="aymara"/>
				<xsd:enumeration value="kawi"/>
				<xsd:enumeration value="manchu"/>
				<xsd:enumeration value="traditional_chinese"/>
				<xsd:enumeration value="romanian"/>
				<xsd:enumeration value="limburgan"/>
				<xsd:enumeration value="southern_sami"/>
				<xsd:enumeration value="burmese"/>
				<xsd:enumeration value="armenian"/>
				<xsd:enumeration value="breton"/>
				<xsd:enumeration value="hmong"/>
				<xsd:enumeration value="indo_european"/>
				<xsd:enumeration value="middle_high_german"/>
				<xsd:enumeration value="ido"/>
				<xsd:enumeration value="sindhi"/>
				<xsd:enumeration value="bulgarian"/>
				<xsd:enumeration value="neapolitan"/>
				<xsd:enumeration value="kachin"/>
				<xsd:enumeration value="dogrib"/>
				<xsd:enumeration value="moldavian"/>
				<xsd:enumeration value="mongo"/>
				<xsd:enumeration value="blin"/>
				<xsd:enumeration value="ugaritic"/>
				<xsd:enumeration value="hiri_motu"/>
				<xsd:enumeration value="soninke"/>
				<xsd:enumeration value="tok_pisin"/>
				<xsd:enumeration value="osage"/>
				<xsd:enumeration value="romany"/>
				<xsd:enumeration value="byelorussian"/>
				<xsd:enumeration value="maharati"/>
				<xsd:enumeration value="duala"/>
				<xsd:enumeration value="american_sign_language"/>
				<xsd:enumeration value="marwari"/>
				<xsd:enumeration value="sicilian"/>
				<xsd:enumeration value="akkadian"/>
				<xsd:enumeration value="timne"/>
				<xsd:enumeration value="tumbuka"/>
				<xsd:enumeration value="greek"/>
				<xsd:enumeration value="basa"/>
				<xsd:enumeration value="kabardian"/>
				<xsd:enumeration value="southern_sotho"/>
				<xsd:enumeration value="haida"/>
				<xsd:enumeration value="basque"/>
				<xsd:enumeration value="chipewyan"/>
				<xsd:enumeration value="serbo-croatian"/>
				<xsd:enumeration value="finnish"/>
				<xsd:enumeration value="venda"/>
				<xsd:enumeration value="avaric"/>
				<xsd:enumeration value="croatian"/>
				<xsd:enumeration value="hittite"/>
				<xsd:enumeration value="southern_altai"/>
				<xsd:enumeration value="salishan_languages"/>
				<xsd:enumeration value="mari"/>
				<xsd:enumeration value="mende"/>
				<xsd:enumeration value="nahuatl"/>
				<xsd:enumeration value="haitian"/>
				<xsd:enumeration value="maori"/>
				<xsd:enumeration value="sukuma"/>
				<xsd:enumeration value="corsican"/>
				<xsd:enumeration value="ingush"/>
				<xsd:enumeration value="nyoro"/>
				<xsd:enumeration value="washo"/>
				<xsd:enumeration value="none"/>
				<xsd:enumeration value="romansh"/>
				<xsd:enumeration value="inupiaq"/>
				<xsd:enumeration value="mossi"/>
				<xsd:enumeration value="buginese"/>
				<xsd:enumeration value="pali"/>
				<xsd:enumeration value="inupiak"/>
				<xsd:enumeration value="nias"/>
				<xsd:enumeration value="vai"/>
				<xsd:enumeration value="kumaoni"/>
				<xsd:enumeration value="russian"/>
				<xsd:enumeration value="chichewa"/>
				<xsd:enumeration value="lahnda"/>
				<xsd:enumeration value="nogai"/>
				<xsd:enumeration value="french_creole"/>
				<xsd:enumeration value="iban"/>
				<xsd:enumeration value="manobo_languages"/>
				<xsd:enumeration value="nubian_languages"/>
				<xsd:enumeration value="pig_latin"/>
				<xsd:enumeration value="cornish"/>
				<xsd:enumeration value="walamo"/>
				<xsd:enumeration value="afar"/>
				<xsd:enumeration value="yiddish"/>
				<xsd:enumeration value="bantu"/>
				<xsd:enumeration value="avestan"/>
				<xsd:enumeration value="grebo"/>
				<xsd:enumeration value="irish"/>
				<xsd:enumeration value="kannada"/>
				<xsd:enumeration value="niuean"/>
				<xsd:enumeration value="acoli"/>
				<xsd:enumeration value="unknown"/>
				<xsd:enumeration value="norwegian_nynorsk"/>
				<xsd:enumeration value="arabic"/>
				<xsd:enumeration value="dari"/>
				<xsd:enumeration value="multilingual"/>
				<xsd:enumeration value="indonesian"/>
				<xsd:enumeration value="danish"/>
				<xsd:enumeration value="philippine_languages"/>
				<xsd:enumeration value="chamorro"/>
				<xsd:enumeration value="tetum"/>
				<xsd:enumeration value="tonga_nyasa"/>
				<xsd:enumeration value="lingala"/>
				<xsd:enumeration value="zhuang"/>
				<xsd:enumeration value="batak"/>
				<xsd:enumeration value="zapotec"/>
				<xsd:enumeration value="caddo"/>
				<xsd:enumeration value="catalan"/>
				<xsd:enumeration value="cebuano"/>
				<xsd:enumeration value="skolt_sami"/>
				<xsd:enumeration value="kirghiz"/>
				<xsd:enumeration value="munda_languages"/>
				<xsd:enumeration value="old_slavonic"/>
				<xsd:enumeration value="ganda"/>
				<xsd:enumeration value="serer"/>
				<xsd:enumeration value="lezghian"/>
				<xsd:enumeration value="tlingit"/>
				<xsd:enumeration value="hupa"/>
				<xsd:enumeration value="unqualified"/>
				<xsd:enumeration value="provencal"/>
				<xsd:enumeration value="chuukese"/>
				<xsd:enumeration value="cambodian"/>
				<xsd:enumeration value="caucasian_languages"/>
				<xsd:enumeration value="slovakian"/>
				<xsd:enumeration value="waray"/>
				<xsd:enumeration value="fang"/>
				<xsd:enumeration value="swedish"/>
				<xsd:enumeration value="maithili"/>
				<xsd:enumeration value="alsatian"/>
				<xsd:enumeration value="kutenai"/>
				<xsd:enumeration value="wolof"/>
				<xsd:enumeration value="bashkir"/>
				<xsd:enumeration value="luba_lulua"/>
				<xsd:enumeration value="fulah"/>
				<xsd:enumeration value="kpelle"/>
				<xsd:enumeration value="slavic"/>
				<xsd:enumeration value="kurdish"/>
				<xsd:enumeration value="turkish"/>
				<xsd:enumeration value="cheyenne"/>
				<xsd:enumeration value="macedonian"/>
				<xsd:enumeration value="tokelau"/>
				<xsd:enumeration value="tigrinya"/>
				<xsd:enumeration value="santali"/>
				<xsd:enumeration value="crimean_tatar"/>
				<xsd:enumeration value="south_american_indian"/>
				<xsd:enumeration value="lozi"/>
				<xsd:enumeration value="ainu"/>
				<xsd:enumeration value="sesotho"/>
				<xsd:enumeration value="mapudungun"/>
				<xsd:enumeration value="athapascan_languages"/>
				<xsd:enumeration value="coptic"/>
				<xsd:enumeration value="pahlavi"/>
				<xsd:enumeration value="malayalam"/>
				<xsd:enumeration value="chuvash"/>
				<xsd:enumeration value="urdu"/>
				<xsd:enumeration value="land_dayak_languages"/>
				<xsd:enumeration value="portuguese"/>
				<xsd:enumeration value="latin_spanish"/>
				<xsd:enumeration value="bemba"/>
				<xsd:enumeration value="oromo"/>
				<xsd:enumeration value="frisian"/>
				<xsd:enumeration value="amharic"/>
				<xsd:enumeration value="kongo"/>
				<xsd:enumeration value="chibcha"/>
				<xsd:enumeration value="masai"/>
				<xsd:enumeration value="iloko"/>
				<xsd:enumeration value="hiligaynon"/>
				<xsd:enumeration value="finno_ugrian"/>
				<xsd:enumeration value="tuvalu"/>
				<xsd:enumeration value="tajik"/>
				<xsd:enumeration value="volapuk"/>
				<xsd:enumeration value="balinese"/>
				<xsd:enumeration value="kimbundu"/>
				<xsd:enumeration value="creole"/>
				<xsd:enumeration value="middle_dutch"/>
				<xsd:enumeration value="tonga"/>
				<xsd:enumeration value="tulu"/>
				<xsd:enumeration value="samaritan"/>
				<xsd:enumeration value="konkani"/>
			</xsd:restriction>
		</xsd:simpleType>

	<xsd:complexType name="NetContentCountUnit">
		<xsd:simpleContent>
			<xsd:extension base="StringNotNull">
				<xsd:attribute name="unitOfMeasure" type="CountUnit" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
		<xsd:simpleType name="CountUnit">
		<xsd:restriction base="StringNotNull">
			<xsd:enumeration value="count"/>
			<xsd:enumeration value="roll"/>
			<xsd:enumeration value="can"/>
			<xsd:enumeration value="piece"/>
			<xsd:enumeration value="pair"/>
			<xsd:enumeration value="bag"/>
			<xsd:enumeration value="box"/>
			<xsd:enumeration value="sheet"/>
			<xsd:enumeration value="bottle"/>
		</xsd:restriction>
	</xsd:simpleType>
		<xsd:annotation>
			<xsd:documentation>ComputerCpuType Values</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleType name="ComputerCpuTypeValues">
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="Pentium_N3510"/>
				<xsd:enumeration value="Celeron_867"/>
				<xsd:enumeration value="Core_i3_350M"/>
				<xsd:enumeration value="Athlon_II_X3_Triple_Core_450"/>
				<xsd:enumeration value="pentium_gold_g5500t"/>
				<xsd:enumeration value="Core_i5_3340s"/>
				<xsd:enumeration value="Pentium_G662"/>
				<xsd:enumeration value="Pentium_G660"/>
				<xsd:enumeration value="celeron_n3000"/>
				<xsd:enumeration value="Turion_II_ULTRA_M600"/>
				<xsd:enumeration value="C7_M_772_VIA"/>
				<xsd:enumeration value="xeon_platinum_8176f"/>
				<xsd:enumeration value="68882"/>
				<xsd:enumeration value="atom_z8700"/>
				<xsd:enumeration value="A4_1200_Accelerated"/>
				<xsd:enumeration value="core_i3_2357u"/>
				<xsd:enumeration value="Core_i5_3210M"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_235e"/>
				<xsd:enumeration value="i3_2367"/>
				<xsd:enumeration value="Core_i7_2.2_GHz"/>
				<xsd:enumeration value="xeon_platinum_8176m"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_550"/>
				<xsd:enumeration value="pentium_g3450t"/>
				<xsd:enumeration value="Pentium_G650"/>
				<xsd:enumeration value="Pentium_N3530"/>
				<xsd:enumeration value="Athlon_II_X3_Triple_Core_435"/>
				<xsd:enumeration value="Core_i3_2357M"/>
				<xsd:enumeration value="Core_2_Duo_P8800"/>
				<xsd:enumeration value="a8_7600"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_5600K"/>
				<xsd:enumeration value="Sempron_3000"/>
				<xsd:enumeration value="atom_z3480"/>
				<xsd:enumeration value="xeon_bronze_3106"/>
				<xsd:enumeration value="xeon_bronze_3104"/>
				<xsd:enumeration value="pentium_e7600"/>
				<xsd:enumeration value="Intel_Core_i7_Extreme"/>
				<xsd:enumeration value="Celeron_887"/>
				<xsd:enumeration value="Core_2_Duo_T9300"/>
				<xsd:enumeration value="Core_2_Duo_2.93GHz"/>
				<xsd:enumeration value="Pentium_3556U"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_5000_plus_"/>
				<xsd:enumeration value="amd_a6"/>
				<xsd:enumeration value="Xeon_W3520"/>
				<xsd:enumeration value="Athlon_II_X3_Triple_Core_440"/>
				<xsd:enumeration value="fx_8_core"/>
				<xsd:enumeration value="amd_a8"/>
				<xsd:enumeration value="core_i7_2760qm"/>
				<xsd:enumeration value="Pentium_M_715"/>
				<xsd:enumeration value="amd_a4"/>
				<xsd:enumeration value="pentium_4405y"/>
				<xsd:enumeration value="Pentium_M_710"/>
				<xsd:enumeration value="Core_2_Duo_1.8GHz"/>
				<xsd:enumeration value="Celeron_877"/>
				<xsd:enumeration value="ryzen_5_4500u"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_8450"/>
				<xsd:enumeration value="pentium_4405u"/>
				<xsd:enumeration value="omap3630"/>
				<xsd:enumeration value="Core_i5_2450M"/>
				<xsd:enumeration value="core_i5_6500"/>
				<xsd:enumeration value="Core_i7_4550U"/>
				<xsd:enumeration value="Pentium_U5600"/>
				<xsd:enumeration value="core_m"/>
				<xsd:enumeration value="core_i3_2227u"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_920"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_P940"/>
				<xsd:enumeration value="Pentium_M_725"/>
				<xsd:enumeration value="Celeron_2957U"/>
				<xsd:enumeration value="Core_i5_3470T"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_925"/>
				<xsd:enumeration value="pa_8600"/>
				<xsd:enumeration value="FX_Series_Eight_Core_FX_8350"/>
				<xsd:enumeration value="cyrix_mii"/>
				<xsd:enumeration value="xscale"/>
				<xsd:enumeration value="core_i5_1035g1"/>
				<xsd:enumeration value="Core_i5_4670K"/>
				<xsd:enumeration value="core_i5_3470"/>
				<xsd:enumeration value="geode_gx1"/>
				<xsd:enumeration value="Xeon_W3503"/>
				<xsd:enumeration value="v30mx"/>
				<xsd:enumeration value="A6_5200"/>
				<xsd:enumeration value="Core_i3_2350M"/>
				<xsd:enumeration value="Pentium_3550M"/>
				<xsd:enumeration value="Pentium_M_738"/>
				<xsd:enumeration value="mobile_athlon_xp_m"/>
				<xsd:enumeration value="apple_a7"/>
				<xsd:enumeration value="Pentium_M_733"/>
				<xsd:enumeration value="core_i7_4712mq"/>
				<xsd:enumeration value="apple_a6"/>
				<xsd:enumeration value="Pentium_M_735"/>
				<xsd:enumeration value="apple_a5"/>
				<xsd:enumeration value="apple_a4"/>
				<xsd:enumeration value="Pentium_M_730"/>
				<xsd:enumeration value="apple_a8"/>
				<xsd:enumeration value="core_i5_3470s"/>
				<xsd:enumeration value="xeon_gold_6126t"/>
				<xsd:enumeration value="Athlon_64_3800"/>
				<xsd:enumeration value="e_series_dual_core_e_450"/>
				<xsd:enumeration value="arm_v7"/>
				<xsd:enumeration value="pentium_d3400"/>
				<xsd:enumeration value="celeron_n3450"/>
				<xsd:enumeration value="alpha_21164a"/>
				<xsd:enumeration value="Celeron_847"/>
				<xsd:enumeration value="pentium_g3258"/>
				<xsd:enumeration value="Snapdragon_s2"/>
				<xsd:enumeration value="Snapdragon_s1"/>
				<xsd:enumeration value="1_2GHz_Cortex_A13"/>
				<xsd:enumeration value="Core_i5_3340M"/>
				<xsd:enumeration value="ARM_Cortex_A5"/>
				<xsd:enumeration value="core_i7_4980HQ"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_P960"/>
				<xsd:enumeration value="Pentium_M_745"/>
				<xsd:enumeration value="Celeron_2955U"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_N530"/>
				<xsd:enumeration value="Pentium_M_740"/>
				<xsd:enumeration value="Core_2_Duo_1.66GHz"/>
				<xsd:enumeration value="Core_i7_2630QM"/>
				<xsd:enumeration value="Core_2_Duo"/>
				<xsd:enumeration value="winchip_c6"/>
				<xsd:enumeration value="pentium_g3250"/>
				<xsd:enumeration value="Pentium_P6200"/>
				<xsd:enumeration value="core_i5_1035g7"/>
				<xsd:enumeration value="core_i5_1035g4"/>
				<xsd:enumeration value="Phenom_II_X6_Six_Core_1055T"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_8400"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_910"/>
				<xsd:enumeration value="pentium_g3260"/>
				<xsd:enumeration value="Pentium_M_755"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9650"/>
				<xsd:enumeration value="intel_atom_230"/>
				<xsd:enumeration value="Pentium_M_753"/>
				<xsd:enumeration value="Pentium_M_750"/>
				<xsd:enumeration value="core_i5_4278u"/>
				<xsd:enumeration value="geode_gxm"/>
				<xsd:enumeration value="Turion_II_Neo_X2_Dual_Core_K625"/>
				<xsd:enumeration value="Pentium_957"/>
				<xsd:enumeration value="pentium_g630"/>
				<xsd:enumeration value="Quad_Core_Xeon_2.4GHz_"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_965"/>
				<xsd:enumeration value="intel_turbo_n2840"/>
				<xsd:enumeration value="novathor_l9000"/>
				<xsd:enumeration value="Core_i3_390M"/>
				<xsd:enumeration value="Pentium_E8400"/>
				<xsd:enumeration value="Pentium_T2330"/>
				<xsd:enumeration value="Turion_64_X2_Dual_Core_TL_52"/>
				<xsd:enumeration value="Core_i5_2540M"/>
				<xsd:enumeration value="Pentium_M_760"/>
				<xsd:enumeration value="Turion_64_X2_Dual_Core_TL_56"/>
				<xsd:enumeration value="Snapdragon"/>
				<xsd:enumeration value="Core_i7_4800MQ"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_B75"/>
				<xsd:enumeration value="Athlon_64_X2_QL_64_Dual_Core"/>
				<xsd:enumeration value="core_i7_6600u"/>
				<xsd:enumeration value="core_i9_7940x"/>
				<xsd:enumeration value="Core_i5_4460"/>
				<xsd:enumeration value="Athlon_64_X2_3600_plus"/>
				<xsd:enumeration value="Core_i7_980X"/>
				<xsd:enumeration value="Pentium_M_778"/>
				<xsd:enumeration value="ryzen_3_3250u"/>
				<xsd:enumeration value="core_i7_6820hk"/>
				<xsd:enumeration value="Core_i7_4702MQ"/>
				<xsd:enumeration value="Pentium_M_773"/>
				<xsd:enumeration value="sa_1100"/>
				<xsd:enumeration value="Intel_Core_i5_4430"/>
				<xsd:enumeration value="Pentium_T4500"/>
				<xsd:enumeration value="v25"/>
				<xsd:enumeration value="5x86"/>
				<xsd:enumeration value="Pentium_M_770"/>
				<xsd:enumeration value="omap5432"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_N850"/>
				<xsd:enumeration value="core_i7_6820hq"/>
				<xsd:enumeration value="omap5430"/>
				<xsd:enumeration value="z_series_dual_core_z_01"/>
				<xsd:enumeration value="Core_2_Duo_T8400"/>
				<xsd:enumeration value="E_Series_Processor_E_240"/>
				<xsd:enumeration value="Athlon_64_X2_4600"/>
				<xsd:enumeration value="core_i7_6950x"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_945"/>
				<xsd:enumeration value="Core_2_Quad_Q9500"/>
				<xsd:enumeration value="core_i7_6700hq"/>
				<xsd:enumeration value="Core_i7_4770S"/>
				<xsd:enumeration value="E1_2500_Accelerated_Processor"/>
				<xsd:enumeration value="Sempron_3400"/>
				<xsd:enumeration value="Athlon_LE_1640"/>
				<xsd:enumeration value="k6_2_plus"/>
				<xsd:enumeration value="Pentium_T2310"/>
				<xsd:enumeration value="v30"/>
				<xsd:enumeration value="amd_ryzen_5_1600x"/>
				<xsd:enumeration value="Core_i5_2457M"/>
				<xsd:enumeration value="Quad_Core_Xeon_2.8_GHz_"/>
				<xsd:enumeration value="Turion_X2_RM_75"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_620"/>
				<xsd:enumeration value="powerpc"/>
				<xsd:enumeration value="ryzen_threadripper_3960x"/>
				<xsd:enumeration value="extremecpu"/>
				<xsd:enumeration value="Core_i5_560UM"/>
				<xsd:enumeration value="core_i7_4860HQ"/>
				<xsd:enumeration value="amd_ryzen_1800x"/>
				<xsd:enumeration value="core_i3"/>
				<xsd:enumeration value="core_i5"/>
				<xsd:enumeration value="core_i7"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_955"/>
				<xsd:enumeration value="Core_i7_4200U"/>
				<xsd:enumeration value="Core_i3_3.2_GHz_"/>
				<xsd:enumeration value="Pentium_B820"/>
				<xsd:enumeration value="Core_i7_4770K"/>
				<xsd:enumeration value="C7_M_764"/>
				<xsd:enumeration value="xeon_platinum_8170m"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_630"/>
				<xsd:enumeration value="core_i9"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_N830"/>
				<xsd:enumeration value="Core_i5_2410M"/>
				<xsd:enumeration value="Turion_II_ULTRA_M660"/>
				<xsd:enumeration value="core_i5_4670r"/>
				<xsd:enumeration value="Xeon_X5560"/>
				<xsd:enumeration value="core_i5_4670s"/>
				<xsd:enumeration value="amd_ryzen_3_1200"/>
				<xsd:enumeration value="xscale_pxa901"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_635"/>
				<xsd:enumeration value="Core_i7_4558U"/>
				<xsd:enumeration value="intel_core_2_duo"/>
				<xsd:enumeration value="atom_z8350"/>
				<xsd:enumeration value="Core_i7_4510U"/>
				<xsd:enumeration value="i3_2350m"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_640"/>
				<xsd:enumeration value="cyrix_iii"/>
				<xsd:enumeration value="pentium_g4520"/>
				<xsd:enumeration value="Quad_Core_2.26GHz"/>
				<xsd:enumeration value="core_i7_4930mx"/>
				<xsd:enumeration value="geoden_x"/>
				<xsd:enumeration value="Core_2_Duo_T7100"/>
				<xsd:enumeration value="Pentium_T2370"/>
				<xsd:enumeration value="Athlon_II_X3_Triple_Core_425"/>
				<xsd:enumeration value="core_i7_4770r"/>
				<xsd:enumeration value="core_i5_5300u"/>
				<xsd:enumeration value="A4_1250"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_645"/>
				<xsd:enumeration value="Core_i5_660"/>
				<xsd:enumeration value="Pentium_3558U"/>
				<xsd:enumeration value="Core_i3_2310M"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_4450"/>
				<xsd:enumeration value="Celeron_2950M"/>
				<xsd:enumeration value="Intel_Core_i3_3120M"/>
				<xsd:enumeration value="Pentium_997"/>
				<xsd:enumeration value="intel_centrino"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_M320"/>
				<xsd:enumeration value="ARM_9_2818"/>
				<xsd:enumeration value="i5_4670k"/>
				<xsd:enumeration value="Sempron_210U"/>
				<xsd:enumeration value="Pentium_T3200"/>
				<xsd:enumeration value="210"/>
				<xsd:enumeration value="sa_1110"/>
				<xsd:enumeration value="pentium_g4500"/>
				<xsd:enumeration value="Celeron_T3000"/>
				<xsd:enumeration value="Pentium_T2350"/>
				<xsd:enumeration value="Phenom_II_X6_Six_Core_1100T"/>
				<xsd:enumeration value="A31s"/>
				<xsd:enumeration value="Sempron_2100"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_60"/>
				<xsd:enumeration value="Sempron_M_2600"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_62"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_QL_64"/>
				<xsd:enumeration value="6x86mx"/>
				<xsd:enumeration value="Core_i3_3.06_GHz_"/>
				<xsd:enumeration value="athlon_mp"/>
				<xsd:enumeration value="eden_esp_7000"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_3800_plus"/>
				<xsd:enumeration value="Core_2_Due_P8400"/>
				<xsd:enumeration value="core_i5_6500t"/>
				<xsd:enumeration value="efficion_8800"/>
				<xsd:enumeration value="mc68sz328"/>
				<xsd:enumeration value="core_i5_5250u"/>
				<xsd:enumeration value="Core_i5_750"/>
				<xsd:enumeration value="celeron_n2940"/>
				<xsd:enumeration value="Phenom_II_X6_Six_Core_1090T"/>
				<xsd:enumeration value="core_i7_4600u"/>
				<xsd:enumeration value="Core_i5_2310"/>
				<xsd:enumeration value="core_i7_4770"/>
				<xsd:enumeration value="core_i7_4771"/>
				<xsd:enumeration value="core_i3_4360t"/>
				<xsd:enumeration value="Core_2_Quad_Q9450"/>
				<xsd:enumeration value="duron"/>
				<xsd:enumeration value="Core_2_Duo_E6600"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_4400"/>
				<xsd:enumeration value="ryzen_7_2700"/>
				<xsd:enumeration value="celeron_3855u"/>
				<xsd:enumeration value="intel_core_i9"/>
				<xsd:enumeration value="Core_i7_3610QM"/>
				<xsd:enumeration value="core_i5_6440hq"/>
				<xsd:enumeration value="Core_i5_2537M"/>
				<xsd:enumeration value="pentium_gold_g5500"/>
				<xsd:enumeration value="Pentium_E5300"/>
				<xsd:enumeration value="Atom_Z3770"/>
				<xsd:enumeration value="Pentium_E5301"/>
				<xsd:enumeration value="intel_atom_n280"/>
				<xsd:enumeration value="Core_i7_3667U"/>
				<xsd:enumeration value="Exynos_5200"/>
				<xsd:enumeration value="core_i7_5930k"/>
				<xsd:enumeration value="Core_Duo_T2600"/>
				<xsd:enumeration value="Core_Duo_U2400"/>
				<xsd:enumeration value="Atom_N330"/>
				<xsd:enumeration value="core_i9_7800x"/>
				<xsd:enumeration value="pentium_n3520"/>
				<xsd:enumeration value="Core_i5_450M"/>
				<xsd:enumeration value="alpha_21364"/>
				<xsd:enumeration value="a_series_quad_core_a8_6500"/>
				<xsd:enumeration value="ARM_Cortex_A_9"/>
				<xsd:enumeration value="Atom_230"/>
				<xsd:enumeration value="Atom_D2550"/>
				<xsd:enumeration value="Pentium_T2390"/>
				<xsd:enumeration value="Core_i5_2405S"/>
				<xsd:enumeration value="Intel_Core_i7_4702MQ"/>
				<xsd:enumeration value="motorola_dragonball"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4_3305"/>
				<xsd:enumeration value="tmpr3922au"/>
				<xsd:enumeration value="Athlon_64_X2_4000_plus"/>
				<xsd:enumeration value="Core_i5_760"/>
				<xsd:enumeration value="Athlon_64_4200_plus"/>
				<xsd:enumeration value="Pentium_4"/>
				<xsd:enumeration value="intel_bay_trail_m_n2830_dual_core"/>
				<xsd:enumeration value="SOC_PXA986_Dual_Core"/>
				<xsd:enumeration value="intel_core_2_extreme"/>
				<xsd:enumeration value="Core_i5_470UM"/>
				<xsd:enumeration value="Core_2_Quad_Q9000"/>
				<xsd:enumeration value="core_i7_4900mq"/>
				<xsd:enumeration value="Core_2_Duo_T6600"/>
				<xsd:enumeration value="Mediatek_8389_Quadcore"/>
				<xsd:enumeration value="phenom_dual_core"/>
				<xsd:enumeration value="Celeron_E3500"/>
				<xsd:enumeration value="Core_2_Duo_T5750"/>
				<xsd:enumeration value="Core_2_Duo_SP9400"/>
				<xsd:enumeration value="Pentium_M"/>
				<xsd:enumeration value="pentium_n3540"/>
				<xsd:enumeration value="Core_2_Quad_Q9400"/>
				<xsd:enumeration value="Core_i5_3427U"/>
				<xsd:enumeration value="core_i7_8650u"/>
				<xsd:enumeration value="Athlon_II_170u"/>
				<xsd:enumeration value="Core_2_Duo_SU_9600"/>
				<xsd:enumeration value="supersparc"/>
				<xsd:enumeration value="xeon_e3_1226v3"/>
				<xsd:enumeration value="core_i3_family"/>
				<xsd:enumeration value="Core_2_Duo_T7500"/>
				<xsd:enumeration value="Pentium_E4400"/>
				<xsd:enumeration value="core_i7_4770hq"/>
				<xsd:enumeration value="quad_core_a8_6410_accelerated"/>
				<xsd:enumeration value="Pentium_E2220"/>
				<xsd:enumeration value="Celeron_T3500"/>
				<xsd:enumeration value="Core_2_Duo_E2200"/>
				<xsd:enumeration value="Snapdragon_S1_MSM7225"/>
				<xsd:enumeration value="Core_2_Duo_T6670"/>
				<xsd:enumeration value="power5"/>
				<xsd:enumeration value="Turion_64_X2_Mobile"/>
				<xsd:enumeration value="ryzen_threadripper_1950x"/>
				<xsd:enumeration value="r4000"/>
				<xsd:enumeration value="Athlon_64_3000"/>
				<xsd:enumeration value="Intel_PDC_G2030"/>
				<xsd:enumeration value="Sempron_64_3000"/>
				<xsd:enumeration value="OMAP_3400"/>
				<xsd:enumeration value="celeron_2961y"/>
				<xsd:enumeration value="AMD_Kabini_E1_2100"/>
				<xsd:enumeration value="power3"/>
				<xsd:enumeration value="power4"/>
				<xsd:enumeration value="Xeon_3530"/>
				<xsd:enumeration value="celeron_j3355"/>
				<xsd:enumeration value="AMD_Kabini_A6_5200M_Quad_Core"/>
				<xsd:enumeration value="Sempron_3600_plus"/>
				<xsd:enumeration value="atom_z3735d"/>
				<xsd:enumeration value="atom_z3735e"/>
				<xsd:enumeration value="A_Series_Quad_Core_A6_3430MX"/>
				<xsd:enumeration value="Pentium_E6600"/>
				<xsd:enumeration value="Core_i7_4820K"/>
				<xsd:enumeration value="ryzen_3_3300x"/>
				<xsd:enumeration value="atom_z3735f"/>
				<xsd:enumeration value="intel_atom_n270"/>
				<xsd:enumeration value="atom_z3735g"/>
				<xsd:enumeration value="Core_2_Duo_T5300"/>
				<xsd:enumeration value="Pentium_G620T"/>
				<xsd:enumeration value="Core_i5_2400s"/>
				<xsd:enumeration value="Core_2_Duo_E4400"/>
				<xsd:enumeration value="Core_i7_3537U"/>
				<xsd:enumeration value="Core_i7_3632QM"/>
				<xsd:enumeration value="celeron_n2930"/>
				<xsd:enumeration value="core_i5_5257u"/>
				<xsd:enumeration value="ryzen_7_3700x"/>
				<xsd:enumeration value="atom_z3736f"/>
				<xsd:enumeration value="Core_i5_540M"/>
				<xsd:enumeration value="Pentium_M_725A"/>
				<xsd:enumeration value="amd_r_series"/>
				<xsd:enumeration value="phenom_x4"/>
				<xsd:enumeration value="core_i7_3740qm"/>
				<xsd:enumeration value="atom_x5_z8300"/>
				<xsd:enumeration value="i7_2637m"/>
				<xsd:enumeration value="Athlon_64_Single_Core_TF_20"/>
				<xsd:enumeration value="Intel_Core_i5_4200U"/>
				<xsd:enumeration value="xeon_e5_2450"/>
				<xsd:enumeration value="phenom_x2"/>
				<xsd:enumeration value="phenom_x3"/>
				<xsd:enumeration value="core_i7_6560u"/>
				<xsd:enumeration value="Athlon_64_X2_4450B"/>
				<xsd:enumeration value="intel_core_2_duo_mobile"/>
				<xsd:enumeration value="arm610"/>
				<xsd:enumeration value="Snapdragon_S3_APQ8060"/>
				<xsd:enumeration value="eden_esp_4000"/>
				<xsd:enumeration value="MT8317T"/>
				<xsd:enumeration value="80486"/>
				<xsd:enumeration value="Core_2_Duo_P9600"/>
				<xsd:enumeration value="athlon_x2"/>
				<xsd:enumeration value="powerpc_970"/>
				<xsd:enumeration value="pentium_ii_xeon"/>
				<xsd:enumeration value="athlon_x4"/>
				<xsd:enumeration value="FX_Series_Quad_Core_FX_4320"/>
				<xsd:enumeration value="80c31"/>
				<xsd:enumeration value="Core_i7_2600K"/>
				<xsd:enumeration value="80c32"/>
				<xsd:enumeration value="intel_atom_z530"/>
				<xsd:enumeration value="winchip_2"/>
				<xsd:enumeration value="amd_ryzen_7_pro_1700x"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_M520"/>
				<xsd:enumeration value="Core_i7_2600S"/>
				<xsd:enumeration value="Celeron_330"/>
				<xsd:enumeration value="pentium_e8500"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_260"/>
				<xsd:enumeration value="Pentium_E5700"/>
				<xsd:enumeration value="core_i7_10510u"/>
				<xsd:enumeration value="atom_z3770d"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_TK_53"/>
				<xsd:enumeration value="core_i7_10510y"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_TK_57"/>
				<xsd:enumeration value="core_i5_6400"/>
				<xsd:enumeration value="Core_i3_530M"/>
				<xsd:enumeration value="Core_i3_4100M"/>
				<xsd:enumeration value="xeon_gold_6126f"/>
				<xsd:enumeration value="A_Series_Dual_Core_A6_5400K"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_P920"/>
				<xsd:enumeration value="unknown"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_255"/>
				<xsd:enumeration value="Core_i7_2670QM"/>
				<xsd:enumeration value="ryzen_5_2500x"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_250"/>
				<xsd:enumeration value="Core_2_Duo_E8400"/>
				<xsd:enumeration value="athlon_xp"/>
				<xsd:enumeration value="Atom_z3635G"/>
				<xsd:enumeration value="Pentium_E2200"/>
				<xsd:enumeration value="ryzen_5_2500u"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9600"/>
				<xsd:enumeration value="intel_atom_z550"/>
				<xsd:enumeration value="xeon_e5_2407"/>
				<xsd:enumeration value="core_i7_3517um"/>
				<xsd:enumeration value="Athlon_2650e"/>
				<xsd:enumeration value="a_series_quad_core_a6_3600m"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_245"/>
				<xsd:enumeration value="mobile_athlon_4"/>
				<xsd:enumeration value="Core_i3_4100U"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_240"/>
				<xsd:enumeration value="core_i9_8950hk"/>
				<xsd:enumeration value="a10_7850k"/>
				<xsd:enumeration value="80c88"/>
				<xsd:enumeration value="atom_z3580"/>
				<xsd:enumeration value="Athlon_64_X2_5400_plus"/>
				<xsd:enumeration value="i7_3960x"/>
				<xsd:enumeration value="itanium_2"/>
				<xsd:enumeration value="core_i3_6100h"/>
				<xsd:enumeration value="Atom_Z3740"/>
				<xsd:enumeration value="Core_i3_4012Y"/>
				<xsd:enumeration value="xeon_e5_2400"/>
				<xsd:enumeration value="Intel_Celeron_G1610T"/>
				<xsd:enumeration value="Core_i7_820QM"/>
				<xsd:enumeration value="Atom_Z670"/>
				<xsd:enumeration value="core_i3_8100"/>
				<xsd:enumeration value="a6_8500p"/>
				<xsd:enumeration value="core_i3_6100t"/>
				<xsd:enumeration value="core_i3_6100u"/>
				<xsd:enumeration value="Core_2_Duo_U7700"/>
				<xsd:enumeration value="Celeron_SU2300"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_220"/>
				<xsd:enumeration value="rockchip_rk3288"/>
				<xsd:enumeration value="Athlon_64_3400"/>
				<xsd:enumeration value="Athlon_II_Neo_X2_Dual_Core_K325"/>
				<xsd:enumeration value="Core_2_Duo_P7450"/>
				<xsd:enumeration value="Core_2_Duo_E7500"/>
				<xsd:enumeration value="atom_z3560"/>
				<xsd:enumeration value="Exynos_5250"/>
				<xsd:enumeration value="intel_atom_z520"/>
				<xsd:enumeration value="Sempron_M_3000"/>
				<xsd:enumeration value="mobile_pentium_4"/>
				<xsd:enumeration value="Snapdragon_MSM8260A"/>
				<xsd:enumeration value="mobile_pentium_2"/>
				<xsd:enumeration value="mobile_pentium_3"/>
				<xsd:enumeration value="Athlon_64_X2_6000_plus"/>
				<xsd:enumeration value="atom_z3775d"/>
				<xsd:enumeration value="Core_i3_4010U"/>
				<xsd:enumeration value="E_Series_Dual_Core_E3_3200"/>
				<xsd:enumeration value="FX_Series_Six_Core_FX_6200"/>
				<xsd:enumeration value="pentium_p7570"/>
				<xsd:enumeration value="Core_i3_4010Y"/>
				<xsd:enumeration value="Core_2_Duo_E4000"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_215"/>
				<xsd:enumeration value="Quad_Core_Q9000"/>
				<xsd:enumeration value="Core_i7_620LM"/>
				<xsd:enumeration value="intel_atom_z510"/>
				<xsd:enumeration value="Core_2_Duo_1.86GHz"/>
				<xsd:enumeration value="Core_i5_2300"/>
				<xsd:enumeration value="core_i7_8700"/>
				<xsd:enumeration value="pentium_pro"/>
				<xsd:enumeration value="i7_2677m"/>
				<xsd:enumeration value="Turion_64_X2_TK_55"/>
				<xsd:enumeration value="Turion_64_X2_TK_57"/>
				<xsd:enumeration value="Turion_64_X2_TK_58"/>
				<xsd:enumeration value="80486dx2"/>
				<xsd:enumeration value="Turion_64_X2_TK_53"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_260"/>
				<xsd:enumeration value="r3900"/>
				<xsd:enumeration value="ARM_710a"/>
				<xsd:enumeration value="Sempron_3100"/>
				<xsd:enumeration value="a6_7000"/>
				<xsd:enumeration value="Core_2_Duo_2.0GHz"/>
				<xsd:enumeration value="Core_i5_4200Y"/>
				<xsd:enumeration value="ryzen_5_4600u"/>
				<xsd:enumeration value="core_i7_extreme"/>
				<xsd:enumeration value="Pentium_G540"/>
				<xsd:enumeration value="Core_i3_2100_"/>
				<xsd:enumeration value="Phenom_II_X6_Six_Core_1045T"/>
				<xsd:enumeration value="ryzen_5_4600h"/>
				<xsd:enumeration value="core_i5_6600k"/>
				<xsd:enumeration value="pentium_g3220t"/>
				<xsd:enumeration value="r3912"/>
				<xsd:enumeration value="pentium_t7500"/>
				<xsd:enumeration value="r3910"/>
				<xsd:enumeration value="geode_gxlv"/>
				<xsd:enumeration value="a10_8700p"/>
				<xsd:enumeration value="i7_2670qm"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9550"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_245"/>
				<xsd:enumeration value="xeon_phi"/>
				<xsd:enumeration value="core_i3_8300"/>
				<xsd:enumeration value="Pentium_G531"/>
				<xsd:enumeration value="pa_7200"/>
				<xsd:enumeration value="mobile_athon_64"/>
				<xsd:enumeration value="Celeron_D_360"/>
				<xsd:enumeration value="Intel_Core_i7_4500U"/>
				<xsd:enumeration value="core_i5_3570s"/>
				<xsd:enumeration value="Core_i5_4202Y"/>
				<xsd:enumeration value="xeon_gold_6150"/>
				<xsd:enumeration value="Athlon_II_X3_Triple_Core_400E"/>
				<xsd:enumeration value="xeon_gold_6152"/>
				<xsd:enumeration value="core_i5_6600t"/>
				<xsd:enumeration value="sparc"/>
				<xsd:enumeration value="Pentium_G560"/>
				<xsd:enumeration value="xeon_gold_6154"/>
				<xsd:enumeration value="amd_ryzen_1600"/>
				<xsd:enumeration value="ARM_710t"/>
				<xsd:enumeration value="intel_pentium_g4400"/>
				<xsd:enumeration value="Tegra"/>
				<xsd:enumeration value="xeon_gold_6146"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_M300"/>
				<xsd:enumeration value="ryzen_5_3400g"/>
				<xsd:enumeration value="xeon_gold_6148"/>
				<xsd:enumeration value="core_i7_4720hq"/>
				<xsd:enumeration value="amd_ryzen_3_pro_1200"/>
				<xsd:enumeration value="xeon_gold_6140"/>
				<xsd:enumeration value="pentium_t6670"/>
				<xsd:enumeration value="Turion_64_X2_TL_64_Gold"/>
				<xsd:enumeration value="xeon_gold_6142"/>
				<xsd:enumeration value="celeron_n3150"/>
				<xsd:enumeration value="Pentium_G550"/>
				<xsd:enumeration value="intel_xeon_mp"/>
				<xsd:enumeration value="xeon_gold_6144"/>
				<xsd:enumeration value="athlon_x4_540"/>
				<xsd:enumeration value="pentium_J2900"/>
				<xsd:enumeration value="Core_i3_540"/>
				<xsd:enumeration value="nec_mips"/>
				<xsd:enumeration value="Core_2_Duo_P8700"/>
				<xsd:enumeration value="Snapdragon_S2_MSM8225"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_M300"/>
				<xsd:enumeration value="Celeron_D_340"/>
				<xsd:enumeration value="pentium_t6600"/>
				<xsd:enumeration value="Celeron_D_346"/>
				<xsd:enumeration value="Celeron_D_345"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_260u"/>
				<xsd:enumeration value="pentium_t5750"/>
				<xsd:enumeration value="pentium_3561y"/>
				<xsd:enumeration value="Celeron_G1820"/>
				<xsd:enumeration value="omap4430"/>
				<xsd:enumeration value="Core_2_Duo_E8300"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9500"/>
				<xsd:enumeration value="Pentium_3560Y"/>
				<xsd:enumeration value="Core_i3_550"/>
				<xsd:enumeration value="Core_i7_640LM"/>
				<xsd:enumeration value="core_i7_4810MQ"/>
				<xsd:enumeration value="Core_i5_3350P"/>
				<xsd:enumeration value="Core_2_Duo_1.86GHz_"/>
				<xsd:enumeration value="Celeron_D_335"/>
				<xsd:enumeration value="a_series_quad_core_a6_3400m"/>
				<xsd:enumeration value="Celeron_D_336"/>
				<xsd:enumeration value="xeon_gold_6138t"/>
				<xsd:enumeration value="Core_i7_2620QM"/>
				<xsd:enumeration value="core_i5_8250u"/>
				<xsd:enumeration value="a8_6410"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_8550"/>
				<xsd:enumeration value="Core_Duo_T2050"/>
				<xsd:enumeration value="core_i5_4690"/>
				<xsd:enumeration value="Pentium_G570"/>
				<xsd:enumeration value="athlon_x4_560"/>
				<xsd:enumeration value="pentium_3560m"/>
				<xsd:enumeration value="core_i3_4370"/>
				<xsd:enumeration value="intel_core_2_solo"/>
				<xsd:enumeration value="ultrasparc_iii"/>
				<xsd:enumeration value="ultrasparc_iis"/>
				<xsd:enumeration value="Core_i5_3570K"/>
				<xsd:enumeration value="Core_i3_2365M"/>
				<xsd:enumeration value="E1_6010"/>
				<xsd:enumeration value="Celeron_D_326"/>
				<xsd:enumeration value="alpha"/>
				<xsd:enumeration value="Celeron_D_325"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_3850"/>
				<xsd:enumeration value="ARM_7100"/>
				<xsd:enumeration value="Xeon_Dual_Core"/>
				<xsd:enumeration value="ultrasparc_iie"/>
				<xsd:enumeration value="pa_8500"/>
				<xsd:enumeration value="a10_7800"/>
				<xsd:enumeration value="intel_pentium_4_ht"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_5000_plus"/>
				<xsd:enumeration value="Core_i5_4200M"/>
				<xsd:enumeration value="powerpc_440gx"/>
				<xsd:enumeration value="core_i3_4360"/>
				<xsd:enumeration value="Core_i5_4200H"/>
				<xsd:enumeration value="Opteron_Quad_1354"/>
				<xsd:enumeration value="Core_i5_3570T"/>
				<xsd:enumeration value="core_i5_4460t"/>
				<xsd:enumeration value="core_i5_4460s"/>
				<xsd:enumeration value="core_i5_3340"/>
				<xsd:enumeration value="intel_atom"/>
				<xsd:enumeration value="Core_2_Duo_T5250"/>
				<xsd:enumeration value="Celeron_900"/>
				<xsd:enumeration value="core_i3_4350"/>
				<xsd:enumeration value="V_Series_Single_Core_V140"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9150"/>
				<xsd:enumeration value="amd_ryzen_1700x"/>
				<xsd:enumeration value="mobile_sempron"/>
				<xsd:enumeration value="Turion_X2_Ultra_Dual_Core_ZM_85"/>
				<xsd:enumeration value="Core_i5_4570"/>
				<xsd:enumeration value="omap4470"/>
				<xsd:enumeration value="Core_i5_2467M"/>
				<xsd:enumeration value="ryzen_threadripper_3970x"/>
				<xsd:enumeration value="apple_ci5"/>
				<xsd:enumeration value="apple_ci3"/>
				<xsd:enumeration value="Core_2_Duo_T9600"/>
				<xsd:enumeration value="mc88110"/>
				<xsd:enumeration value="apple_ci7"/>
				<xsd:enumeration value="Pentium_P6100"/>
				<xsd:enumeration value="pentium_g3260t"/>
				<xsd:enumeration value="elansc400"/>
				<xsd:enumeration value="pentium_987"/>
				<xsd:enumeration value="celeron_3205u"/>
				<xsd:enumeration value="xeon_platinum_8180m"/>
				<xsd:enumeration value="amd_c_series"/>
				<xsd:enumeration value="core_i7_5960x"/>
				<xsd:enumeration value="omap4460"/>
				<xsd:enumeration value="V_Series_Single_Core_V120"/>
				<xsd:enumeration value="celeron_t1400"/>
				<xsd:enumeration value="Core_2_Duo_2.33GHz"/>
				<xsd:enumeration value="Pentium_E7200"/>
				<xsd:enumeration value="a8_8650"/>
				<xsd:enumeration value="powerpc_740_g3"/>
				<xsd:enumeration value="via_cyrix_c3"/>
				<xsd:enumeration value="Core_i3_380M"/>
				<xsd:enumeration value="efficeon_tm8600"/>
				<xsd:enumeration value="Celeron_925"/>
				<xsd:enumeration value="core_i7_2670m"/>
				<xsd:enumeration value="core_i5_5575r"/>
				<xsd:enumeration value="Athlon_64_L110"/>
				<xsd:enumeration value="Athon_II_X2_Dual_Core_P360"/>
				<xsd:enumeration value="core_i3_4330"/>
				<xsd:enumeration value="athlon_xp_m"/>
				<xsd:enumeration value="Core_2_Duo_T7400"/>
				<xsd:enumeration value="Core_2_Duo_T6570"/>
				<xsd:enumeration value="Core_i3_2328M"/>
				<xsd:enumeration value="i5_2450m"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_260u"/>
				<xsd:enumeration value="Core_i3_530"/>
				<xsd:enumeration value="Core_2_Quad_Q8300"/>
				<xsd:enumeration value="Core_i5_3317U"/>
				<xsd:enumeration value="Athlon_64"/>
				<xsd:enumeration value="core_i7_8500y"/>
				<xsd:enumeration value="Turion_64_MT_37"/>
				<xsd:enumeration value="ryzen_3_2200ge"/>
				<xsd:enumeration value="core_i5_2550k"/>
				<xsd:enumeration value="V_Series_Single_Core_V105"/>
				<xsd:enumeration value="Xeon_E5520"/>
				<xsd:enumeration value="powerpc_rs64"/>
				<xsd:enumeration value="xeon_gold_5119t"/>
				<xsd:enumeration value="core_i7_9700k"/>
				<xsd:enumeration value="core_m3_8100y"/>
				<xsd:enumeration value="core_i7_6500u"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_645"/>
				<xsd:enumeration value="Quad_Core_Xeon"/>
				<xsd:enumeration value="Pentium_E6300"/>
				<xsd:enumeration value="core_i5_family"/>
				<xsd:enumeration value="Celeron_T3100"/>
				<xsd:enumeration value="6x86"/>
				<xsd:enumeration value="Intel_Core_i3_3240"/>
				<xsd:enumeration value="Core_Duo_T2450"/>
				<xsd:enumeration value="Core_Solo_U1300"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_910_"/>
				<xsd:enumeration value="Sempron_2200"/>
				<xsd:enumeration value="E1_2500"/>
				<xsd:enumeration value="mtk_8121"/>
				<xsd:enumeration value="Tablet_Processor"/>
				<xsd:enumeration value="core_i3_2125"/>
				<xsd:enumeration value="Athlon_64_X2_5200_plus"/>
				<xsd:enumeration value="tegra_4"/>
				<xsd:enumeration value="Geode_GX"/>
				<xsd:enumeration value="Core_i7_640UM"/>
				<xsd:enumeration value="Xeon_E5530"/>
				<xsd:enumeration value="Core_2_Duo_T5270"/>
				<xsd:enumeration value="Core_i7_2820QM"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_170u"/>
				<xsd:enumeration value="Xeon_E5504"/>
				<xsd:enumeration value="Xeon_E5506"/>
				<xsd:enumeration value="Sempron_3500_plus"/>
				<xsd:enumeration value="Xeon_E5507"/>
				<xsd:enumeration value="Core_Duo_LV_L2400"/>
				<xsd:enumeration value="Core_2_Duo_2.13GHz"/>
				<xsd:enumeration value="core_i7_6850k"/>
				<xsd:enumeration value="pentium_967"/>
				<xsd:enumeration value="Celeron_N2830_Dual_Core"/>
				<xsd:enumeration value="Core_i3_3217U"/>
				<xsd:enumeration value="pa_8900"/>
				<xsd:enumeration value="pentium_iii_e"/>
				<xsd:enumeration value="celeron_b815"/>
				<xsd:enumeration value="core_i5_2410"/>
				<xsd:enumeration value="pentium_iii_s"/>
				<xsd:enumeration value="amd_ryzen_5_1400"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_7750"/>
				<xsd:enumeration value="Pentium_T4400"/>
				<xsd:enumeration value="Intel_Clover_Trail"/>
				<xsd:enumeration value="E_Series_Dual_Core_E2_3000"/>
				<xsd:enumeration value="opteron"/>
				<xsd:enumeration value="amd_ryzen_7_1700"/>
				<xsd:enumeration value="Intel_Core_i3_3130M"/>
				<xsd:enumeration value="Core_2_Duo_T8300"/>
				<xsd:enumeration value="Athlon_64_LE_1660"/>
				<xsd:enumeration value="Celeron_B800"/>
				<xsd:enumeration value="Core_2_Duo_2.53GHz"/>
				<xsd:enumeration value="Atom_Z520"/>
				<xsd:enumeration value="celeron_j3455"/>
				<xsd:enumeration value="pentium_3825u"/>
				<xsd:enumeration value="ARM_11_Telechips_8902"/>
				<xsd:enumeration value="Core_2_Quad_9600"/>
				<xsd:enumeration value="r4310"/>
				<xsd:enumeration value="Core_i5_460M"/>
				<xsd:enumeration value="ryzen_7_3800x"/>
				<xsd:enumeration value="pa_7300lc"/>
				<xsd:enumeration value="Atom_D525"/>
				<xsd:enumeration value="Core_i5_2500K"/>
				<xsd:enumeration value="amd_ryzen_7_1700x"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_3250e"/>
				<xsd:enumeration value="Core_i3_4020Y"/>
				<xsd:enumeration value="Core_i5_2500T"/>
				<xsd:enumeration value="Core_i5_2500S"/>
				<xsd:enumeration value="celeron_g1850"/>
				<xsd:enumeration value="Core_2_Duo_E4300"/>
				<xsd:enumeration value="Core_2_Quad_Q8400S"/>
				<xsd:enumeration value="fx_series_eight_core_fx_8100"/>
				<xsd:enumeration value="core_i7_8550u"/>
				<xsd:enumeration value="core_m_5y31"/>
				<xsd:enumeration value="Atom_Z515"/>
				<xsd:enumeration value="Core_i5_2.3_GHz"/>
				<xsd:enumeration value="Core_2_Duo_P7370"/>
				<xsd:enumeration value="Sempron_M120"/>
				<xsd:enumeration value="Core_i7_2410M"/>
				<xsd:enumeration value="Pentium_G3250T"/>
				<xsd:enumeration value="Intel_Celeron_G470"/>
				<xsd:enumeration value="Core_2_Duo_T6500"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9100E"/>
				<xsd:enumeration value="Core_i7_680UM"/>
				<xsd:enumeration value="core_i7_6900k"/>
				<xsd:enumeration value="Celeron_T4500"/>
				<xsd:enumeration value="Athlon_64_LE_1640"/>
				<xsd:enumeration value="Core_i7_3520M"/>
				<xsd:enumeration value="Atom_Z540"/>
				<xsd:enumeration value="Core_i5_650"/>
				<xsd:enumeration value="core_i7_2630m"/>
				<xsd:enumeration value="a4_7210"/>
				<xsd:enumeration value="core_i7_2630q"/>
				<xsd:enumeration value="core_i5_5350h"/>
				<xsd:enumeration value="Core_i5__760"/>
				<xsd:enumeration value="Core_i7_3720QM"/>
				<xsd:enumeration value="powerpc_604e"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_610e_"/>
				<xsd:enumeration value="ultrasparc_t1"/>
				<xsd:enumeration value="Atom_Z530"/>
				<xsd:enumeration value="core_i7_920xm"/>
				<xsd:enumeration value="Core_2_Duo_U7600"/>
				<xsd:enumeration value="core_m_5y10"/>
				<xsd:enumeration value="68lc040"/>
				<xsd:enumeration value="celeron_3865u"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4_3420"/>
				<xsd:enumeration value="r4300"/>
				<xsd:enumeration value="fx_series_eight_core_fx_8120"/>
				<xsd:enumeration value="core_i5_10310y"/>
				<xsd:enumeration value="crusoe_tm5600"/>
				<xsd:enumeration value="Core_i7_620M"/>
				<xsd:enumeration value="Core_Duo_T2500"/>
				<xsd:enumeration value="Core_2_Duo_2.4GHz"/>
				<xsd:enumeration value="A8_5557M"/>
				<xsd:enumeration value="ultrasparc_iv_plus"/>
				<xsd:enumeration value="core_i7_4765t"/>
				<xsd:enumeration value="Turion_64_MT_32"/>
				<xsd:enumeration value="Core_i5_3439Y"/>
				<xsd:enumeration value="Turion_64_MT_30"/>
				<xsd:enumeration value="core_i3_4025u"/>
				<xsd:enumeration value="Celeron_M_340"/>
				<xsd:enumeration value="T40_S_TEGRA_4_A15_Quad_Core"/>
				<xsd:enumeration value="Pentium_E6700"/>
				<xsd:enumeration value="Turion_64_MT_28"/>
				<xsd:enumeration value="Pentium_G630T"/>
				<xsd:enumeration value="Core_i3_520M"/>
				<xsd:enumeration value="Core_i7_3920XM"/>
				<xsd:enumeration value="Pentium_D_840"/>
				<xsd:enumeration value="Core_2_Duo_T5200"/>
				<xsd:enumeration value="core_i3_5005u"/>
				<xsd:enumeration value="core_i5_4590s"/>
				<xsd:enumeration value="core_i3_2370M"/>
				<xsd:enumeration value="r14000"/>
				<xsd:enumeration value="core_i5_4590t"/>
				<xsd:enumeration value="Core_2_Quad_Q8200"/>
				<xsd:enumeration value="core_i9_7920x"/>
				<xsd:enumeration value="Core_i5_3330"/>
				<xsd:enumeration value="core_m_5y71"/>
				<xsd:enumeration value="core_i3_4110m"/>
				<xsd:enumeration value="core_m_5y70"/>
				<xsd:enumeration value="Celeron_M_330"/>
				<xsd:enumeration value="Core_2_Duo_2.16GHz"/>
				<xsd:enumeration value="eden_esp_5000"/>
				<xsd:enumeration value="Core_i3_4150T"/>
				<xsd:enumeration value="quad_core_a8_6410"/>
				<xsd:enumeration value="atom_c3200_rk"/>
				<xsd:enumeration value="Pentium_E3200"/>
				<xsd:enumeration value="core_i9_7980xe"/>
				<xsd:enumeration value="pr31700"/>
				<xsd:enumeration value="vr4300"/>
				<xsd:enumeration value="ryzen_3_2200g"/>
				<xsd:enumeration value="Atom_D510"/>
				<xsd:enumeration value="athlon_220ge"/>
				<xsd:enumeration value="pentium_p8700"/>
				<xsd:enumeration value="core_i3_4330t"/>
				<xsd:enumeration value="Celeron_M_320"/>
				<xsd:enumeration value="Core_i7_4700HQ"/>
				<xsd:enumeration value="Core_i5_4670"/>
				<xsd:enumeration value="Core_2_Quad_Q9550"/>
				<xsd:enumeration value="pentium_j2900"/>
				<xsd:enumeration value="a_series_quad_core_a8_3520m"/>
				<xsd:enumeration value="core_i5_6200u"/>
				<xsd:enumeration value="Exynos_4200"/>
				<xsd:enumeration value="Core_i7_3840QM"/>
				<xsd:enumeration value="core_m_5y51"/>
				<xsd:enumeration value="A10_5757M"/>
				<xsd:enumeration value="Phenom_II_X2_B55"/>
				<xsd:enumeration value="pentium_gold_g5400"/>
				<xsd:enumeration value="Pentium_E5400"/>
				<xsd:enumeration value="ryzen_3_2200u"/>
				<xsd:enumeration value="Core_2_Duo_T8700"/>
				<xsd:enumeration value="Core_2_Duo_T5670"/>
				<xsd:enumeration value="Exynos_4210"/>
				<xsd:enumeration value="ryzen_5_2600h"/>
				<xsd:enumeration value="pa_7100lc"/>
				<xsd:enumeration value="ryzen_5_2600e"/>
				<xsd:enumeration value="Celeron_D_420"/>
				<xsd:enumeration value="amd_fx"/>
				<xsd:enumeration value="Core_2_Duo_SU9600"/>
				<xsd:enumeration value="Atom_N270"/>
				<xsd:enumeration value="pa_8000"/>
				<xsd:enumeration value="Core_i7_2.0_GHz"/>
				<xsd:enumeration value="core_i3_350um"/>
				<xsd:enumeration value="Pentium_D_805"/>
				<xsd:enumeration value="Pentium_G3240"/>
				<xsd:enumeration value="Core_i5_4288U"/>
				<xsd:enumeration value="amd_a6_6400k"/>
				<xsd:enumeration value="atom_zZ8300"/>
				<xsd:enumeration value="Core_i3_2130"/>
				<xsd:enumeration value="r10000"/>
				<xsd:enumeration value="core_i7_7y75"/>
				<xsd:enumeration value="Atom_N280"/>
				<xsd:enumeration value="pentium"/>
				<xsd:enumeration value="Celeron_450"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9950"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_B95"/>
				<xsd:enumeration value="xeon_gold_6134m"/>
				<xsd:enumeration value="ryzen_5_3600"/>
				<xsd:enumeration value="ryzen_5_2600x"/>
				<xsd:enumeration value="68000"/>
				<xsd:enumeration value="Core_2_Duo_SL9300"/>
				<xsd:enumeration value="powerpc_403_gcx"/>
				<xsd:enumeration value="arm710"/>
				<xsd:enumeration value="turion_64"/>
				<xsd:enumeration value="xeon_gold_6138f"/>
				<xsd:enumeration value="C7_M_770_VIA"/>
				<xsd:enumeration value="pentium_xeon"/>
				<xsd:enumeration value="core_i7_4712hq"/>
				<xsd:enumeration value="Core_2_Duo_P9500"/>
				<xsd:enumeration value="Celeron_485"/>
				<xsd:enumeration value="A_Series_Dual_Core_A6_4455M"/>
				<xsd:enumeration value="Pentium_D_820"/>
				<xsd:enumeration value="powerpc_601"/>
				<xsd:enumeration value="powerpc_603"/>
				<xsd:enumeration value="powerpc_604"/>
				<xsd:enumeration value="ryzen_threadripper_2970wx"/>
				<xsd:enumeration value="tegra_2_0"/>
				<xsd:enumeration value="ultrasparc_iv"/>
				<xsd:enumeration value="ryzen_threadripper_1920x"/>
				<xsd:enumeration value="r5230"/>
				<xsd:enumeration value="celeron_g1840t"/>
				<xsd:enumeration value="Celeron_M_T1400"/>
				<xsd:enumeration value="core_i5_4590"/>
				<xsd:enumeration value="Pentium_D_830"/>
				<xsd:enumeration value="A_Series_Quad_Core_A10_5700"/>
				<xsd:enumeration value="handheld_engine_cxd2230ga_temp"/>
				<xsd:enumeration value="Core_2_Duo_SL7100"/>
				<xsd:enumeration value="core_i3_1005g1"/>
				<xsd:enumeration value="Celeron_E3200"/>
				<xsd:enumeration value="atom_n2600"/>
				<xsd:enumeration value="ultrasparc_ii"/>
				<xsd:enumeration value="Celeron_B840"/>
				<xsd:enumeration value="Core_i3_2105"/>
				<xsd:enumeration value="ARM_11_iMAPX210"/>
				<xsd:enumeration value="a8_8600p"/>
				<xsd:enumeration value="Atom_N230"/>
				<xsd:enumeration value="pentium_e7500"/>
				<xsd:enumeration value="Core_2_Duo_SL9300_"/>
				<xsd:enumeration value="68030"/>
				<xsd:enumeration value="Pentium_G620"/>
				<xsd:enumeration value="Core_2_Duo_T5600"/>
				<xsd:enumeration value="omap3620"/>
				<xsd:enumeration value="xeon_gold_6130t"/>
				<xsd:enumeration value="core_2_solo"/>
				<xsd:enumeration value="cortex"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_B22"/>
				<xsd:enumeration value="Atom_Z550"/>
				<xsd:enumeration value="c167cr"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_511"/>
				<xsd:enumeration value="Core_i5_2400"/>
				<xsd:enumeration value="ultrasparc_iii_cu"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_B26"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_B24"/>
				<xsd:enumeration value="celeron_g1620t"/>
				<xsd:enumeration value="Core_i3_4158U"/>
				<xsd:enumeration value="xeon_gold_6130f"/>
				<xsd:enumeration value="Atom_Z2760"/>
				<xsd:enumeration value="Snapdragon_S4_APQ8064"/>
				<xsd:enumeration value="68040"/>
				<xsd:enumeration value="a6_7400k"/>
				<xsd:enumeration value="Core_2_Duo_1.6GHz"/>
				<xsd:enumeration value="k6_iii_plus"/>
				<xsd:enumeration value="powerpc_603e"/>
				<xsd:enumeration value="core_i5_6287u"/>
				<xsd:enumeration value="core_i5_9400f"/>
				<xsd:enumeration value="celeron_n3050"/>
				<xsd:enumeration value="Celeron_B820"/>
				<xsd:enumeration value="Core_i3_2120"/>
				<xsd:enumeration value="Athlon_64_4800_plus"/>
				<xsd:enumeration value="core_i3_6157u"/>
				<xsd:enumeration value="core_i3_4370t"/>
				<xsd:enumeration value="Pentium_G645"/>
				<xsd:enumeration value="Pentium_G3220"/>
				<xsd:enumeration value="Pentium_G641"/>
				<xsd:enumeration value="a10_7300"/>
				<xsd:enumeration value="Pentium_G640"/>
				<xsd:enumeration value="fx_series_eight_core_fx_8150"/>
				<xsd:enumeration value="Athlon_64_LE_1600"/>
				<xsd:enumeration value="Athlon_II_Single_Core_160u"/>
				<xsd:enumeration value="celeron_n3060"/>
				<xsd:enumeration value="Celeron_B830"/>
				<xsd:enumeration value="V_SERIES_V160"/>
				<xsd:enumeration value="alpha_ev7"/>
				<xsd:enumeration value="Xeon_3000"/>
				<xsd:enumeration value="Sempron_M100"/>
				<xsd:enumeration value="Pentium_D_T2060"/>
				<xsd:enumeration value="pentium_gold_g5400t"/>
				<xsd:enumeration value="Athlon_64_2650"/>
				<xsd:enumeration value="Pentium_E5800"/>
				<xsd:enumeration value="Atom_z3735G"/>
				<xsd:enumeration value="Pentium_G631"/>
				<xsd:enumeration value="Pentium_G630"/>
				<xsd:enumeration value="core_i5_8200y"/>
				<xsd:enumeration value="Core_2_Duo_P7350"/>
				<xsd:enumeration value="celeron_g1840"/>
				<xsd:enumeration value="Core_2_Duo_E7400"/>
				<xsd:enumeration value="Athlon_64_3500"/>
				<xsd:enumeration value="Athlon_Neo_Single_Core_MV_40"/>
				<xsd:enumeration value="core_i7_4790K"/>
				<xsd:enumeration value="Core_i5_3450S"/>
				<xsd:enumeration value="core_solo"/>
				<xsd:enumeration value="sparc_ii"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_5000"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_P860"/>
				<xsd:enumeration value="Core_i5_4430S"/>
				<xsd:enumeration value="Core_i3_3227U"/>
				<xsd:enumeration value="Core_I3_2330E"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_TK_53"/>
				<xsd:enumeration value="Core_2_Duo_U1400"/>
				<xsd:enumeration value="core_i7_4790t"/>
				<xsd:enumeration value="core_i7_4790s"/>
				<xsd:enumeration value="exynos_5_octa_5800"/>
				<xsd:enumeration value="mxs"/>
				<xsd:enumeration value="supersparc_ii"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4_4355M"/>
				<xsd:enumeration value="Core_i3_2330M"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_610e"/>
				<xsd:enumeration value="Core_i7_920"/>
				<xsd:enumeration value="C_Series_Dual_Core_C_50"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_TK_57"/>
				<xsd:enumeration value="r12000a"/>
				<xsd:enumeration value="Core_i7_2617M"/>
				<xsd:enumeration value="Core_2_Duo_T9550"/>
				<xsd:enumeration value="xeon_e3_1271"/>
				<xsd:enumeration value="Pentium_U3600"/>
				<xsd:enumeration value="core_i7_36517u"/>
				<xsd:enumeration value="bay_trail_t_z3735g"/>
				<xsd:enumeration value="Snapdragon_S2_MSM7230"/>
				<xsd:enumeration value="Core_i5_3320M"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_L335"/>
				<xsd:enumeration value="A_Series_Dual_Core_A6_3620"/>
				<xsd:enumeration value="sparc64v"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_P840"/>
				<xsd:enumeration value="Phenom_II_X6_Six_Core_1035T"/>
				<xsd:enumeration value="Turion_64_ML_37"/>
				<xsd:enumeration value="Turion_64_ML_34"/>
				<xsd:enumeration value="A6_6310"/>
				<xsd:enumeration value="Turion_64_ML_32"/>
				<xsd:enumeration value="Turion_64_ML_30"/>
				<xsd:enumeration value="Snapdragon_S3_MSM8260"/>
				<xsd:enumeration value="FX_Series_Eigth_Core_FX_8320"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_P520"/>
				<xsd:enumeration value="core_i7_5550u"/>
				<xsd:enumeration value="core_i7_4720HQ"/>
				<xsd:enumeration value="Core_Solo_T1200"/>
				<xsd:enumeration value="a_series_quad_core_a6_3410m"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9450"/>
				<xsd:enumeration value="omap850"/>
				<xsd:enumeration value="tegra_ap_2600"/>
				<xsd:enumeration value="core_i5_4210m"/>
				<xsd:enumeration value="a8_7200p"/>
				<xsd:enumeration value="Core_i3_370M"/>
				<xsd:enumeration value="core_i5_4210h"/>
				<xsd:enumeration value="Intel_Pentium_2127U_ULV"/>
				<xsd:enumeration value="athlon_64_for_dtr"/>
				<xsd:enumeration value="athlon"/>
				<xsd:enumeration value="tm_44"/>
				<xsd:enumeration value="Core_2_Duo_L7200"/>
				<xsd:enumeration value="atom_c3130"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_P820"/>
				<xsd:enumeration value="Core_i5_470UM_"/>
				<xsd:enumeration value="atom_z8500"/>
				<xsd:enumeration value="intel_xscale_pxa263"/>
				<xsd:enumeration value="nova_a9000"/>
				<xsd:enumeration value="Athlon_64_X2_5000_plus"/>
				<xsd:enumeration value="crusoe_5800"/>
				<xsd:enumeration value="Athlon_64_X2_Pro_L310"/>
				<xsd:enumeration value="Core_i5_3230M"/>
				<xsd:enumeration value="intel_core_2_quad"/>
				<xsd:enumeration value="intel_core_solo"/>
				<xsd:enumeration value="a6_7310"/>
				<xsd:enumeration value="Xeon_5000"/>
				<xsd:enumeration value="A_Series_Quad_Core_A10_4655M"/>
				<xsd:enumeration value="core_i3_10110u"/>
				<xsd:enumeration value="amd_ryzen_3_pro_1300"/>
				<xsd:enumeration value="core_i3_10110y"/>
				<xsd:enumeration value="Core_2_Duo_2.26GHz"/>
				<xsd:enumeration value="intel_xscale_pxa255"/>
				<xsd:enumeration value="xeon_silver_4109t"/>
				<xsd:enumeration value="80c186"/>
				<xsd:enumeration value="Core_2_Duo_P8600"/>
				<xsd:enumeration value="intel_xscale_pxa250"/>
				<xsd:enumeration value="Celeron_D_440"/>
				<xsd:enumeration value="celeron_j1850"/>
				<xsd:enumeration value="Athlon_64_X2_6400_plus"/>
				<xsd:enumeration value="i5_2467m"/>
				<xsd:enumeration value="Pentium_4_360"/>
				<xsd:enumeration value="pentium_e6950"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_615e"/>
				<xsd:enumeration value="Core_i5_4210Y"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_4555M"/>
				<xsd:enumeration value="e_series"/>
				<xsd:enumeration value="powerpc_rs64_iv"/>
				<xsd:enumeration value="Core_i5_4210U"/>
				<xsd:enumeration value="Core_i5_4258U"/>
				<xsd:enumeration value="powerpc_rs64_ii"/>
				<xsd:enumeration value="Core_2_Quad_Q6600"/>
				<xsd:enumeration value="Clover_Trail_Plus_Z2560"/>
				<xsd:enumeration value="arm7500fe"/>
				<xsd:enumeration value="Celeron_P4500"/>
				<xsd:enumeration value="Athlon_64_X2_4200_plus"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_8650"/>
				<xsd:enumeration value="Athlon_II_Neo_X2_Dual_Core_K625"/>
				<xsd:enumeration value="sh_4"/>
				<xsd:enumeration value="pentium_n2930"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_3250e"/>
				<xsd:enumeration value="Core_2_Duo_2.8GHz"/>
				<xsd:enumeration value="Pentium_U5400"/>
				<xsd:enumeration value="Core_Duo_T2300"/>
				<xsd:enumeration value="core_i5_4690s"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_250U"/>
				<xsd:enumeration value="core_i5_4690t"/>
				<xsd:enumeration value="AMD_Richland_A8_5550M"/>
				<xsd:enumeration value="xeon_e5_1630v3"/>
				<xsd:enumeration value="celeron_1037u"/>
				<xsd:enumeration value="core_m_6y75"/>
				<xsd:enumeration value="core_i5_6300hq"/>
				<xsd:enumeration value="core_i7_4870hq"/>
				<xsd:enumeration value="i3_2370m"/>
				<xsd:enumeration value="Core_2_Duo_E4700"/>
				<xsd:enumeration value="Celeron_M_380"/>
				<xsd:enumeration value="ARM_11_2818"/>
				<xsd:enumeration value="celeron"/>
				<xsd:enumeration value="pa_8800"/>
				<xsd:enumeration value="Core_2_Duo_T7300"/>
				<xsd:enumeration value="pentium_g3470"/>
				<xsd:enumeration value="mobile_k6_2_plus"/>
				<xsd:enumeration value="a8_7410"/>
				<xsd:enumeration value="E_Series_Dual_Core_E1_1500"/>
				<xsd:enumeration value="arm710t"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4_5300"/>
				<xsd:enumeration value="Core_2_Quad_Q8400"/>
				<xsd:enumeration value="fx_8800p"/>
				<xsd:enumeration value="intel_core_duo"/>
				<xsd:enumeration value="Celeron_M_370"/>
				<xsd:enumeration value="geode_gx"/>
				<xsd:enumeration value="arm710a"/>
				<xsd:enumeration value="Turion_Neo_X2_Dual_Core_L625"/>
				<xsd:enumeration value="Celeron_E1500"/>
				<xsd:enumeration value="Core_2_Duo_P3750"/>
				<xsd:enumeration value="core_m_6y57"/>
				<xsd:enumeration value="core_m_6y54"/>
				<xsd:enumeration value="Intel_Core_i7_4770"/>
				<xsd:enumeration value="Snapdragon_S3_MSM8660"/>
				<xsd:enumeration value="pentium_j4205"/>
				<xsd:enumeration value="Celeron_M_360"/>
				<xsd:enumeration value="Pentium_T3400"/>
				<xsd:enumeration value="core_i7_5557u"/>
				<xsd:enumeration value="core_i7_5775r"/>
				<xsd:enumeration value="pentium_g2120"/>
				<xsd:enumeration value="pentium_g3450"/>
				<xsd:enumeration value="Core_i7_3630QM"/>
				<xsd:enumeration value="Pentium_P6000"/>
				<xsd:enumeration value="core_i3_3220t"/>
				<xsd:enumeration value="i7_2675qm"/>
				<xsd:enumeration value="mobile_pentium_4_ht"/>
				<xsd:enumeration value="celeron_3215u"/>
				<xsd:enumeration value="r4700"/>
				<xsd:enumeration value="Core_Solo_U1400"/>
				<xsd:enumeration value="ultrasparc_iii_plus"/>
				<xsd:enumeration value="core_i3_2375m"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core"/>
				<xsd:enumeration value="core_duo"/>
				<xsd:enumeration value="Celeron_M_353"/>
				<xsd:enumeration value="core_i7_1065g7"/>
				<xsd:enumeration value="Celeron_M_350"/>
				<xsd:enumeration value="intel_xeon"/>
				<xsd:enumeration value="pentium_g2130"/>
				<xsd:enumeration value="pentium_g3460"/>
				<xsd:enumeration value="Turion_64_ML_28"/>
				<xsd:enumeration value="Core_2_Duo_T7350"/>
				<xsd:enumeration value="intel_core_i7_5950hq"/>
				<xsd:enumeration value="Xeon"/>
				<xsd:enumeration value="amd_ryzen_5_pro_1500"/>
				<xsd:enumeration value="texas_instruments_omap1510"/>
				<xsd:enumeration value="Core_i7_4790"/>
				<xsd:enumeration value="xeon_silver_4108"/>
				<xsd:enumeration value="xeon_silver_4110"/>
				<xsd:enumeration value="xeon_silver_4114"/>
				<xsd:enumeration value="ultrasparc_s400"/>
				<xsd:enumeration value="xeon_silver_4112"/>
				<xsd:enumeration value="Core_i3_330UM"/>
				<xsd:enumeration value="Core_i5_540UM"/>
				<xsd:enumeration value="Pentium_2117U"/>
				<xsd:enumeration value="Pentium_T2130"/>
				<xsd:enumeration value="core_i5_5675c"/>
				<xsd:enumeration value="celeron_j1800"/>
				<xsd:enumeration value="core_i3_2377m"/>
				<xsd:enumeration value="Celeron_D_Processor_420"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_5500"/>
				<xsd:enumeration value="Athlon_Neo_X2_Dual_Core_L335"/>
				<xsd:enumeration value="core_i9_7960x"/>
				<xsd:enumeration value="core_m_5y10c"/>
				<xsd:enumeration value="core_i7_5775c"/>
				<xsd:enumeration value="core_m_5y10a"/>
				<xsd:enumeration value="xeon_silver_4116"/>
				<xsd:enumeration value="amd_ryzen_1600x"/>
				<xsd:enumeration value="core_i5_5675r"/>
				<xsd:enumeration value="Intel_Pentium_G2020T"/>
				<xsd:enumeration value="Athlon_II_X3_415E"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_P540"/>
				<xsd:enumeration value="pa_8700_plus"/>
				<xsd:enumeration value="Pentium_T4300"/>
				<xsd:enumeration value="Core_i7_4700MQ"/>
				<xsd:enumeration value="phenom_triple_core"/>
				<xsd:enumeration value="amd_ryzen_1700"/>
				<xsd:enumeration value="Athlon_Neo_X2_Dual_Core_L325"/>
				<xsd:enumeration value="Core_i7_3770K"/>
				<xsd:enumeration value="Sempron_3200"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_TK_42_"/>
				<xsd:enumeration value="athlon_x4_840"/>
				<xsd:enumeration value="Turion_64_X2_RM_70"/>
				<xsd:enumeration value="Turion_64_X2_RM_72"/>
				<xsd:enumeration value="core_i5_4300u"/>
				<xsd:enumeration value="atom_455"/>
				<xsd:enumeration value="Athlon_64_Mobile_3000"/>
				<xsd:enumeration value="elansc310"/>
				<xsd:enumeration value="Core_i3_3229Y"/>
				<xsd:enumeration value="Turion_64_X2_RM_74"/>
				<xsd:enumeration value="Core_2_Duo_T9500"/>
				<xsd:enumeration value="Celeron_D_Processor_440"/>
				<xsd:enumeration value="Athlon_64_X2_4800"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9100E_"/>
				<xsd:enumeration value="E_Series_Single_Core_E_240"/>
				<xsd:enumeration value="Exynos_5000_Series"/>
				<xsd:enumeration value="core_i9_7740x"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_5400_plus"/>
				<xsd:enumeration value="E2_Series_Dual_Core_E2_2000"/>
				<xsd:enumeration value="Celeron_M_390"/>
				<xsd:enumeration value="r16000"/>
				<xsd:enumeration value="athlon_x4_830"/>
				<xsd:enumeration value="A_Series_Dual_Core_A6_3650"/>
				<xsd:enumeration value="tegra_k1"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_P560"/>
				<xsd:enumeration value="Core_i7_3770T"/>
				<xsd:enumeration value="phenom_ii_x2"/>
				<xsd:enumeration value="Core_i5_2430M"/>
				<xsd:enumeration value="phenom_ii_x4"/>
				<xsd:enumeration value="Core_i7_3770S"/>
				<xsd:enumeration value="phenom_ii_x3"/>
				<xsd:enumeration value="phenom_ii_x6"/>
				<xsd:enumeration value="elansc300"/>
				<xsd:enumeration value="Core_Duo_T2350"/>
				<xsd:enumeration value="A4_5000"/>
				<xsd:enumeration value="phenom_ii_x4_quad_core_n970"/>
				<xsd:enumeration value="Atom_N550"/>
				<xsd:enumeration value="Atom_D410"/>
				<xsd:enumeration value="ryzen_threadripper_2950x"/>
				<xsd:enumeration value="eden_esp"/>
				<xsd:enumeration value="Celeron_M_420"/>
				<xsd:enumeration value="mc68ez328"/>
				<xsd:enumeration value="Core_2_Duo_T7700"/>
				<xsd:enumeration value="Athlon_64_X2"/>
				<xsd:enumeration value="ryzen_7_2700e"/>
				<xsd:enumeration value="A10"/>
				<xsd:enumeration value="A13"/>
				<xsd:enumeration value="Core_2_Duo_2.5GHz"/>
				<xsd:enumeration value="Core_2_Duo_U7500"/>
				<xsd:enumeration value="core_i5_8300h"/>
				<xsd:enumeration value="k6_3"/>
				<xsd:enumeration value="core_i7_6700"/>
				<xsd:enumeration value="k6_2"/>
				<xsd:enumeration value="OMAP_3600"/>
				<xsd:enumeration value="A110"/>
				<xsd:enumeration value="Intel_Pentium_Dual_Core_2020M"/>
				<xsd:enumeration value="Core_2_Duo_T4200"/>
				<xsd:enumeration value="Celeron_M_410"/>
				<xsd:enumeration value="pentium_2"/>
				<xsd:enumeration value="crusoe_tm5500"/>
				<xsd:enumeration value="pentium_4"/>
				<xsd:enumeration value="mediatek_mt8127"/>
				<xsd:enumeration value="A20"/>
				<xsd:enumeration value="pentium_3"/>
				<xsd:enumeration value="core_i5_2410qm"/>
				<xsd:enumeration value="intel_core_i7_4810mq"/>
				<xsd:enumeration value="core_i5_5750hq"/>
				<xsd:enumeration value="phenom_quad_core"/>
				<xsd:enumeration value="amd_ryzen_7_1800x"/>
				<xsd:enumeration value="Core_2_Duo__T6500"/>
				<xsd:enumeration value="eden_esp_6000"/>
				<xsd:enumeration value="Atom_N570"/>
				<xsd:enumeration value="core_i3_4120u"/>
				<xsd:enumeration value="Core_i3_4030U"/>
				<xsd:enumeration value="Atom_D425"/>
				<xsd:enumeration value="ARM_11_iMAP210"/>
				<xsd:enumeration value="Core_2_Duo_T5500"/>
				<xsd:enumeration value="intel_core_i5_6600"/>
				<xsd:enumeration value="Atom_Silverthorne"/>
				<xsd:enumeration value="core_i3_5015u"/>
				<xsd:enumeration value="Celeron_D_Processor_360"/>
				<xsd:enumeration value="A31"/>
				<xsd:enumeration value="ryzen_7_2700x"/>
				<xsd:enumeration value="Athlon_Dual_Core"/>
				<xsd:enumeration value="core_i7_10710u"/>
				<xsd:enumeration value="mobile_duron"/>
				<xsd:enumeration value="athlon_x4_860k"/>
				<xsd:enumeration value="ryzen_3_4300u"/>
				<xsd:enumeration value="intel_celeron_d"/>
				<xsd:enumeration value="ryzen_7_2700u"/>
				<xsd:enumeration value="pentium_d"/>
				<xsd:enumeration value="Core_i5_560M"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_M340"/>
				<xsd:enumeration value="pentium_m"/>
				<xsd:enumeration value="Pentium_Dual_Core_2127U"/>
				<xsd:enumeration value="i3_2367m"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_P650"/>
				<xsd:enumeration value="xeon_gold_6140m"/>
				<xsd:enumeration value="Core_i7_3689Y"/>
				<xsd:enumeration value="Core_Duo_T2300E"/>
				<xsd:enumeration value="Athlon_64_X2_5600_plus"/>
				<xsd:enumeration value="core_m_6y30"/>
				<xsd:enumeration value="pentium_p8600"/>
				<xsd:enumeration value="Core_2_Duo_2.66GHz"/>
				<xsd:enumeration value="core_i7_6800k"/>
				<xsd:enumeration value="core_i7_5600u"/>
				<xsd:enumeration value="atom_z3745"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_235e_"/>
				<xsd:enumeration value="core_i7_3687u"/>
				<xsd:enumeration value="FX_Series_Quad_Core_FX_4120"/>
				<xsd:enumeration value="Pentium_T6570"/>
				<xsd:enumeration value="amd_athlon_quad_core_5150"/>
				<xsd:enumeration value="omap1710"/>
				<xsd:enumeration value="novathor_l8000"/>
				<xsd:enumeration value="core_i7_5820k"/>
				<xsd:enumeration value="Core_i7_2620M"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_M320"/>
				<xsd:enumeration value="Core_i7_875K"/>
				<xsd:enumeration value="Core_i5_3450"/>
				<xsd:enumeration value="core_2_extreme"/>
				<xsd:enumeration value="Pentium_E5500"/>
				<xsd:enumeration value="Core_i7_4930K"/>
				<xsd:enumeration value="Turion_II_Ultra_X2_Dual_Core_M600"/>
				<xsd:enumeration value="Core_i5_2557M"/>
				<xsd:enumeration value="atom_z3735"/>
				<xsd:enumeration value="celeron_d"/>
				<xsd:enumeration value="Exynos_5400"/>
				<xsd:enumeration value="i7_2700k"/>
				<xsd:enumeration value="Athlon_II"/>
				<xsd:enumeration value="pentium_sl9300"/>
				<xsd:enumeration value="itanium"/>
				<xsd:enumeration value="Core_Duo_T2400"/>
				<xsd:enumeration value="Turion_II_Ultra_X2_Dual_Core_M620"/>
				<xsd:enumeration value="core_i9_7820x"/>
				<xsd:enumeration value="intel_core_i3_6100"/>
				<xsd:enumeration value="Xeon_W5580"/>
				<xsd:enumeration value="Celeron_M_440"/>
				<xsd:enumeration value="alpha_21164"/>
				<xsd:enumeration value="core_i7_4970k"/>
				<xsd:enumeration value="Core_i5_430M"/>
				<xsd:enumeration value="core_i5_10210u"/>
				<xsd:enumeration value="A_Series_Tri_Core_A6_3500"/>
				<xsd:enumeration value="Core_i7_2.7_GHz"/>
				<xsd:enumeration value="rm5231"/>
				<xsd:enumeration value="ARM_710"/>
				<xsd:enumeration value="Core_2_Duo_T9900"/>
				<xsd:enumeration value="microsparc_ii"/>
				<xsd:enumeration value="core_i5_10210y"/>
				<xsd:enumeration value="core_i5_4690K"/>
				<xsd:enumeration value="Intel_Core_i5_3330S"/>
				<xsd:enumeration value="80486slc"/>
				<xsd:enumeration value="core_i3_6300t"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_M300"/>
				<xsd:enumeration value="pentium_3_xeon"/>
				<xsd:enumeration value="Celeron_M_430"/>
				<xsd:enumeration value="securcore"/>
				<xsd:enumeration value="Core_2_Solo_SU3500"/>
				<xsd:enumeration value="Intel_Mobile_CPU"/>
				<xsd:enumeration value="Core_i3_4160T"/>
				<xsd:enumeration value="Core_2_Duo_T6400"/>
				<xsd:enumeration value="Core_2_Duo_T5550"/>
				<xsd:enumeration value="Core_2_Duo_E5500"/>
				<xsd:enumeration value="A_Series_Dual_Core_A6_4400M"/>
				<xsd:enumeration value="core_i5_4690k"/>
				<xsd:enumeration value="ryzen_7_4700u"/>
				<xsd:enumeration value="a_series_dual_core_a4_3400m"/>
				<xsd:enumeration value="E_Series_Dual_Core_E_350"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_240e_"/>
				<xsd:enumeration value="atom_z3785"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_M500"/>
				<xsd:enumeration value="xscale_pxa261"/>
				<xsd:enumeration value="xscale_pxa260"/>
				<xsd:enumeration value="core_i3_6167u"/>
				<xsd:enumeration value="xeon_gold_6148f"/>
				<xsd:enumeration value="athlon_x2_450"/>
				<xsd:enumeration value="xscale_pxa262"/>
				<xsd:enumeration value="pentium_b987"/>
				<xsd:enumeration value="tx3922"/>
				<xsd:enumeration value="intel_atom_n450"/>
				<xsd:enumeration value="Core_i7_720QM"/>
				<xsd:enumeration value="Pentium_D_925"/>
				<xsd:enumeration value="core_i3_539"/>
				<xsd:enumeration value="xscale_pxa270"/>
				<xsd:enumeration value="athlon_ii_x4"/>
				<xsd:enumeration value="athlon_ii_x2"/>
				<xsd:enumeration value="athlon_ii_x3"/>
				<xsd:enumeration value="Celeron_585"/>
				<xsd:enumeration value="Pentium_D_920"/>
				<xsd:enumeration value="athlon_64"/>
				<xsd:enumeration value="core_2_quad"/>
				<xsd:enumeration value="exynos_5_octa_5420"/>
				<xsd:enumeration value="intel_atom_n455"/>
				<xsd:enumeration value="atom_z3775"/>
				<xsd:enumeration value="xscale_pxa272"/>
				<xsd:enumeration value="tegra_3_0"/>
				<xsd:enumeration value="Sempron"/>
				<xsd:enumeration value="Athlon_64_3200"/>
				<xsd:enumeration value="Core_2_Duo_SU7300"/>
				<xsd:enumeration value="amd_a_series"/>
				<xsd:enumeration value="C_Series_Single_Core_C_30"/>
				<xsd:enumeration value="Core_2_Duo_E7300"/>
				<xsd:enumeration value="core_i3_7100u"/>
				<xsd:enumeration value="Pentium_D_930"/>
				<xsd:enumeration value="core_i5_7y54"/>
				<xsd:enumeration value="tegra_ap_2500"/>
				<xsd:enumeration value="celeron_m"/>
				<xsd:enumeration value="celeron_n"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_M520"/>
				<xsd:enumeration value="core_i7_4810HQ"/>
				<xsd:enumeration value="cortex_a8"/>
				<xsd:enumeration value="cortex_a7"/>
				<xsd:enumeration value="fx_series_six_core_fx_6120"/>
				<xsd:enumeration value="Core_2_Duo_U2200"/>
				<xsd:enumeration value="ryzen_3_2300u"/>
				<xsd:enumeration value="Core_2_Duo_T5900"/>
				<xsd:enumeration value="pentium_g4500t"/>
				<xsd:enumeration value="Pentium_D_945"/>
				<xsd:enumeration value="celeron_807"/>
				<xsd:enumeration value="Pentium_D_940"/>
				<xsd:enumeration value="Core_i7_3517U"/>
				<xsd:enumeration value="pentium_g3900"/>
				<xsd:enumeration value="Athlon_XP_3000"/>
				<xsd:enumeration value="alpha_21064a"/>
				<xsd:enumeration value="arm_dual_core_cortex_a9_omap_4"/>
				<xsd:enumeration value="Core_i5_2380P"/>
				<xsd:enumeration value="Athlon_64_TF_36"/>
				<xsd:enumeration value="pentium_n4200"/>
				<xsd:enumeration value="Core_i5_2500"/>
				<xsd:enumeration value="xeon_gold_5120t"/>
				<xsd:enumeration value="Core_i5_3360M"/>
				<xsd:enumeration value="hypersparc"/>
				<xsd:enumeration value="Core_i7_980"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9850"/>
				<xsd:enumeration value="fx_series_six_core_fx_6100"/>
				<xsd:enumeration value="OMAP_5400"/>
				<xsd:enumeration value="xeon_e5_2650"/>
				<xsd:enumeration value="Pentium_D_950"/>
				<xsd:enumeration value="Snapdragon_S1_QSD8650"/>
				<xsd:enumeration value="intel_pentium_d"/>
				<xsd:enumeration value="Core_i3_330M"/>
				<xsd:enumeration value="celeron_n4000"/>
				<xsd:enumeration value="Core_2_Duo_SL9400"/>
				<xsd:enumeration value="a_series_quad_core_a10_6700"/>
				<xsd:enumeration value="Celeron_J1800"/>
				<xsd:enumeration value="xeon_gold_6134"/>
				<xsd:enumeration value="pentium_other"/>
				<xsd:enumeration value="Pentium_M_A110"/>
				<xsd:enumeration value="Core_i7_950"/>
				<xsd:enumeration value="xeon_gold_6136"/>
				<xsd:enumeration value="core_i7_3635qm"/>
				<xsd:enumeration value="Pentium_SU2700"/>
				<xsd:enumeration value="xeon_gold_6138"/>
				<xsd:enumeration value="Athlon_II_Neo_Single_Core_K125"/>
				<xsd:enumeration value="ARM_7500fe"/>
				<xsd:enumeration value="core_i5_8400"/>
				<xsd:enumeration value="pentium_ii"/>
				<xsd:enumeration value="Celeron_540"/>
				<xsd:enumeration value="xeon_gold_6130"/>
				<xsd:enumeration value="xeon_gold_6132"/>
				<xsd:enumeration value="Core_2_Duo_T2450"/>
				<xsd:enumeration value="Core_2_Duo_E6400"/>
				<xsd:enumeration value="xeon_e3_1241"/>
				<xsd:enumeration value="Core_2_Duo_T6600_"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_N930"/>
				<xsd:enumeration value="apple_a5x"/>
				<xsd:enumeration value="a_series_dual_core_a4_3310m"/>
				<xsd:enumeration value="Core_i7_960"/>
				<xsd:enumeration value="xeon_gold_6142f"/>
				<xsd:enumeration value="xeon_gold_6126"/>
				<xsd:enumeration value="pentium_j2850"/>
				<xsd:enumeration value="xeon_gold_6128"/>
				<xsd:enumeration value="Core_2_Duo_2.1GHz"/>
				<xsd:enumeration value="xeon_gold_6142m"/>
				<xsd:enumeration value="xeon_silver_4116t"/>
				<xsd:enumeration value="Core_i7_3612QM"/>
				<xsd:enumeration value="ARM_11_8902"/>
				<xsd:enumeration value="xeon_e3_1231"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_QL_60"/>
				<xsd:enumeration value="Celeron_M_ULV"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_QL_62"/>
				<xsd:enumeration value="mediagx"/>
				<xsd:enumeration value="pentium_b960"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_QL_64"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_QL_66"/>
				<xsd:enumeration value="a_series"/>
				<xsd:enumeration value="Pentium_G524"/>
				<xsd:enumeration value="e2_6110"/>
				<xsd:enumeration value="Core_i5_4250U"/>
				<xsd:enumeration value="Core_i7_930"/>
				<xsd:enumeration value="xeon_e5_2620"/>
				<xsd:enumeration value="a10_8750"/>
				<xsd:enumeration value="Core_2_Duo_E6420"/>
				<xsd:enumeration value="Pentium_G520"/>
				<xsd:enumeration value="A_Series_Quad_Core_A10_4600M"/>
				<xsd:enumeration value="Turion_64_X2_Dual_Core_RM_70"/>
				<xsd:enumeration value="Celeron_T1500"/>
				<xsd:enumeration value="Intel_Core_i7_4700MQ"/>
				<xsd:enumeration value="Turion_64_X2_Dual_Core_RM_72"/>
				<xsd:enumeration value="xeon_e3_1225"/>
				<xsd:enumeration value="xeon_e3_1220"/>
				<xsd:enumeration value="Mobile_Pentium_4_532"/>
				<xsd:enumeration value="xeon_e5_2600"/>
				<xsd:enumeration value="atom_z3795"/>
				<xsd:enumeration value="a33_arm_cortex_a7_quad_core"/>
				<xsd:enumeration value="ryzen_5_3500u"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_N950"/>
				<xsd:enumeration value="ARM_11_ROCK_2818"/>
				<xsd:enumeration value="Mobile_Pentium_4_538"/>
				<xsd:enumeration value="Pentium_G519"/>
				<xsd:enumeration value="Pentium_G517"/>
				<xsd:enumeration value="A_Series_Eight_Core_A10_5700"/>
				<xsd:enumeration value="Pentium_G516"/>
				<xsd:enumeration value="Core_i7_2600"/>
				<xsd:enumeration value="Pentium_D_915"/>
				<xsd:enumeration value="Pentium_G515"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_620_"/>
				<xsd:enumeration value="Core_i7_940"/>
				<xsd:enumeration value="core_i3_5010u"/>
				<xsd:enumeration value="core_i3_4030y"/>
				<xsd:enumeration value="Celeron_E3300"/>
				<xsd:enumeration value="amd_ryzen_3_1300x"/>
				<xsd:enumeration value="xeon_silver_4114t"/>
				<xsd:enumeration value="celeron_n3350"/>
				<xsd:enumeration value="Core_2_Duo_T7270"/>
				<xsd:enumeration value="Core_2_Duo_SL9600"/>
				<xsd:enumeration value="core_i9_7640x"/>
				<xsd:enumeration value="ultrasparc_iiii"/>
				<xsd:enumeration value="Core_i5_3330M"/>
				<xsd:enumeration value="core_i7_7500u"/>
				<xsd:enumeration value="Athlon_II_X4_Dual_Core_240e"/>
				<xsd:enumeration value="Celeron_743"/>
				<xsd:enumeration value="core_i5_4310u"/>
				<xsd:enumeration value="Turion_II_Neo_X2_Dual_Core_L625"/>
				<xsd:enumeration value="core_i7_8700k"/>
				<xsd:enumeration value="Athlon_64_X2_TK_55"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_4200_plus"/>
				<xsd:enumeration value="core_i7_8700t"/>
				<xsd:enumeration value="core_i5_4310m"/>
				<xsd:enumeration value="atom_n2930"/>
				<xsd:enumeration value="alpha_21264a"/>
				<xsd:enumeration value="ryzen_threadripper_2990wx"/>
				<xsd:enumeration value="alpha_21264b"/>
				<xsd:enumeration value="alpha_21264c"/>
				<xsd:enumeration value="alpha_21264d"/>
				<xsd:enumeration value="core_i7_6567u"/>
				<xsd:enumeration value="Athlon_II_X4Quad_Core_600e"/>
				<xsd:enumeration value="Core_Solo_T1300"/>
				<xsd:enumeration value="pentium_g645t"/>
				<xsd:enumeration value="power4_plus"/>
				<xsd:enumeration value="Celeron_N2830"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_3520M"/>
				<xsd:enumeration value="a_series_quad_core_a6_3420m"/>
				<xsd:enumeration value="powerpc_603ev"/>
				<xsd:enumeration value="xeon_e5_1620"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8_4500M"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_840T_"/>
				<xsd:enumeration value="core_i7_4710MQ"/>
				<xsd:enumeration value="Athlon_X2_4050E"/>
				<xsd:enumeration value="Core_Duo_T2250"/>
				<xsd:enumeration value="core_i3_8130u"/>
				<xsd:enumeration value="Core_2_Duo_T8100"/>
				<xsd:enumeration value="a8_7650k"/>
				<xsd:enumeration value="Core_2_Duo_T7250"/>
				<xsd:enumeration value="core_i3_4170"/>
				<xsd:enumeration value="Turion_64_X2_Ultra_ZM_82"/>
				<xsd:enumeration value="amd_ryzen_5_pro_1600"/>
				<xsd:enumeration value="Turion_64_X2_Ultra_ZM_87"/>
				<xsd:enumeration value="amd_ryzen_1500x"/>
				<xsd:enumeration value="Core_2_Duo_SU9300"/>
				<xsd:enumeration value="Turion_64_X2_Ultra_ZM_85"/>
				<xsd:enumeration value="xeon_e5_1607"/>
				<xsd:enumeration value="Pentium_2127U"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_600E"/>
				<xsd:enumeration value="C_SERIES_C_60"/>
				<xsd:enumeration value="Celeron_763"/>
				<xsd:enumeration value="E_Series_Dual_Core_E1_1200"/>
				<xsd:enumeration value="core_i3_4160"/>
				<xsd:enumeration value="Pentium_M_ULV"/>
				<xsd:enumeration value="arm_7100"/>
				<xsd:enumeration value="Mediatek_8317_Dual_Core"/>
				<xsd:enumeration value="Core_i5_3550S"/>
				<xsd:enumeration value="Athlon_X2_Dual_Core_7550"/>
				<xsd:enumeration value="Athlon_64_3700"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_M520_"/>
				<xsd:enumeration value="k6_mmx"/>
				<xsd:enumeration value="e_series_dual_core_e_350"/>
				<xsd:enumeration value="apple_c2d"/>
				<xsd:enumeration value="core_i7_4470s"/>
				<xsd:enumeration value="ultrasparc_i"/>
				<xsd:enumeration value="intel_centrino_2"/>
				<xsd:enumeration value="pentium_d840"/>
				<xsd:enumeration value="amd_ryzen_5_1500x"/>
				<xsd:enumeration value="ryzen_5_2400ge"/>
				<xsd:enumeration value="mc68vz328"/>
				<xsd:enumeration value="Pentium_T2410"/>
				<xsd:enumeration value="Core_i3_2120T"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_840T"/>
				<xsd:enumeration value="ryzen_5_2600"/>
				<xsd:enumeration value="Phenom_II_X3_Triple_Core_B75"/>
				<xsd:enumeration value="ryzen_threadripper_3990x"/>
				<xsd:enumeration value="a10_7700k"/>
				<xsd:enumeration value="68328"/>
				<xsd:enumeration value="Pentium_P6300"/>
				<xsd:enumeration value="Snapdragon_S4"/>
				<xsd:enumeration value="Snapdragon_S3"/>
				<xsd:enumeration value="Core_i7_740QM"/>
				<xsd:enumeration value="Athlon_2850e"/>
				<xsd:enumeration value="g3"/>
				<xsd:enumeration value="Snapdragon_S5"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_810"/>
				<xsd:enumeration value="Core_2_Quad_Q6700"/>
				<xsd:enumeration value="g4"/>
				<xsd:enumeration value="g5"/>
				<xsd:enumeration value="Intel_Celeron_1007U"/>
				<xsd:enumeration value="fx_4_core"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9750"/>
				<xsd:enumeration value="intel_atom_330"/>
				<xsd:enumeration value="pentium_4_extreme_edition"/>
				<xsd:enumeration value="core_i5_4220y"/>
				<xsd:enumeration value="omap311"/>
				<xsd:enumeration value="core_i3_6320"/>
				<xsd:enumeration value="omap310"/>
				<xsd:enumeration value="core_i5_6600"/>
				<xsd:enumeration value="Sempron_SI_42"/>
				<xsd:enumeration value="fx_series_quad_core_fx_4100"/>
				<xsd:enumeration value="a6_8550"/>
				<xsd:enumeration value="A6_5350M"/>
				<xsd:enumeration value="Core_Solo_T1350"/>
				<xsd:enumeration value="Celeron_N2840"/>
				<xsd:enumeration value="core_i5_3570"/>
				<xsd:enumeration value="efficeon_tm8000"/>
				<xsd:enumeration value="i5_2430m"/>
				<xsd:enumeration value="powerpc_403"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_P320"/>
				<xsd:enumeration value="amd_ryzen_1400"/>
				<xsd:enumeration value="brazos_e450"/>
				<xsd:enumeration value="Core_i7_4750HQ"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_P360"/>
				<xsd:enumeration value="Core_i7_3770"/>
				<xsd:enumeration value="Athlon_II_X4_Quad_Core_605e"/>
				<xsd:enumeration value="bulverde"/>
				<xsd:enumeration value="Core_i5_430M_"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_240e"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_L310"/>
				<xsd:enumeration value="core_i7_4722hq"/>
				<xsd:enumeration value="Turion_X2_Ultra_ZM_82"/>
				<xsd:enumeration value="pentium_g4400t"/>
				<xsd:enumeration value="Turion_X2_Ultra_ZM_87"/>
				<xsd:enumeration value="Turion_X2_Ultra_ZM_85"/>
				<xsd:enumeration value="core_i3_6300"/>
				<xsd:enumeration value="Core_i5_4440s"/>
				<xsd:enumeration value="Turion_X2_Ultra_ZM_84"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_250"/>
				<xsd:enumeration value="Pentium_Dual_Core_2030M"/>
				<xsd:enumeration value="atom_z7345"/>
				<xsd:enumeration value="Core_i7_640M"/>
				<xsd:enumeration value="AMD_Richland_A10_5750M"/>
				<xsd:enumeration value="Celeron_G530"/>
				<xsd:enumeration value="ryzen_9_3900x"/>
				<xsd:enumeration value="core_i7_4785t"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_840"/>
				<xsd:enumeration value="celeron_g465"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_N640"/>
				<xsd:enumeration value="e_series_dual_core_e_300"/>
				<xsd:enumeration value="celeron_1000m"/>
				<xsd:enumeration value="core_i5_4570r"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_P340"/>
				<xsd:enumeration value="k5"/>
				<xsd:enumeration value="Core_i7_4650U"/>
				<xsd:enumeration value="Celeron_G540"/>
				<xsd:enumeration value="Core_Solo_U1500"/>
				<xsd:enumeration value="amd_ryzen_7"/>
				<xsd:enumeration value="fx_series_quad_core_fx_b4150"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_N650"/>
				<xsd:enumeration value="Sempron_140"/>
				<xsd:enumeration value="core_i7_5500u"/>
				<xsd:enumeration value="Core_2_Duo_T5470"/>
				<xsd:enumeration value="pentium_g530"/>
				<xsd:enumeration value="core_i5_5200u"/>
				<xsd:enumeration value="vr4181"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_820"/>
				<xsd:enumeration value="intel_core_i7_5700hq"/>
				<xsd:enumeration value="intel_core_m"/>
				<xsd:enumeration value="Core_i5_3339Y"/>
				<xsd:enumeration value="Core_2_Duo_E4600"/>
				<xsd:enumeration value="tegra_650"/>
				<xsd:enumeration value="pentium_g3900t"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_N620"/>
				<xsd:enumeration value="Core_i7_2720QM"/>
				<xsd:enumeration value="Pentium_B950"/>
				<xsd:enumeration value="pa_8700"/>
				<xsd:enumeration value="Exynos_4400"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_511_"/>
				<xsd:enumeration value="sa_110"/>
				<xsd:enumeration value="Pentium_847"/>
				<xsd:enumeration value="power3_ii"/>
				<xsd:enumeration value="c_series_dual_core_c_60"/>
				<xsd:enumeration value="amd_ryzen_5_1600"/>
				<xsd:enumeration value="Sempron_LE_1250"/>
				<xsd:enumeration value="e1_7010"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_P320"/>
				<xsd:enumeration value="core_m_7y30"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_830"/>
				<xsd:enumeration value="Pentium_B940"/>
				<xsd:enumeration value="Core_i7_4710HQ"/>
				<xsd:enumeration value="Core_i5_520M"/>
				<xsd:enumeration value="mediatek_mt8183"/>
				<xsd:enumeration value="Athlon_II_X2_B24"/>
				<xsd:enumeration value="mediagxm"/>
				<xsd:enumeration value="Exynos_4412"/>
				<xsd:enumeration value="i7_4770K"/>
				<xsd:enumeration value="mediagxi"/>
				<xsd:enumeration value="turion_64_x2"/>
				<xsd:enumeration value="c_series_dual_core_c_50"/>
				<xsd:enumeration value="Core_i7_660UM"/>
				<xsd:enumeration value="pentium_n3700"/>
				<xsd:enumeration value="Turion_64_X2_ZM_80"/>
				<xsd:enumeration value="a8_7100"/>
				<xsd:enumeration value="vr4121"/>
				<xsd:enumeration value="i5_2320"/>
				<xsd:enumeration value="Turion_64_X2_ZM_82"/>
				<xsd:enumeration value="vr4122"/>
				<xsd:enumeration value="celeron_1005m"/>
				<xsd:enumeration value="athlon_240ge"/>
				<xsd:enumeration value="GEODE_LX_LX_800"/>
				<xsd:enumeration value="Sempron_3300"/>
				<xsd:enumeration value="Snapdragon_S4_MSM8270"/>
				<xsd:enumeration value="xeon_platinum_8160t"/>
				<xsd:enumeration value="core_i3_2340m"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4"/>
				<xsd:enumeration value="i7_3820"/>
				<xsd:enumeration value="Pentium_2129Y"/>
				<xsd:enumeration value="athlon_64_x2"/>
				<xsd:enumeration value="Pentium_B970"/>
				<xsd:enumeration value="Pentium_E2180"/>
				<xsd:enumeration value="Intel_Pentium_G2020"/>
				<xsd:enumeration value="Core_2_Duo_T9400"/>
				<xsd:enumeration value="turbosparc"/>
				<xsd:enumeration value="core_i5_6400t"/>
				<xsd:enumeration value="core_i5_6402p"/>
				<xsd:enumeration value="Atom_N450_"/>
				<xsd:enumeration value="Turion_64_X2_ZM_72"/>
				<xsd:enumeration value="h8s"/>
				<xsd:enumeration value="vr4111"/>
				<xsd:enumeration value="pentium_g3240t"/>
				<xsd:enumeration value="Turion_64_X2_ZM_74"/>
				<xsd:enumeration value="hitachi_sh3"/>
				<xsd:enumeration value="eden"/>
				<xsd:enumeration value="sis550"/>
				<xsd:enumeration value="Pentium_B967"/>
				<xsd:enumeration value="Pentium_SU4100"/>
				<xsd:enumeration value="r4640"/>
				<xsd:enumeration value="Pentium_E7400"/>
				<xsd:enumeration value="core_i5_3200u"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_9350"/>
				<xsd:enumeration value="Core_i5_655K"/>
				<xsd:enumeration value="xeon_platinum_8160m"/>
				<xsd:enumeration value="Pentium_B960"/>
				<xsd:enumeration value="tegra_600"/>
				<xsd:enumeration value="xeon_platinum_8160f"/>
				<xsd:enumeration value="Turion_64_MK_38"/>
				<xsd:enumeration value="Turion_64_MK_36"/>
				<xsd:enumeration value="fx_6_core"/>
				<xsd:enumeration value="Core_2_Duo_1.4GHz_"/>
				<xsd:enumeration value="Core_i5_430UM"/>
				<xsd:enumeration value="Athlon_64_X2_QL_65"/>
				<xsd:enumeration value="powerpc_750_g3"/>
				<xsd:enumeration value="microsparc_iiep"/>
				<xsd:enumeration value="microsoft_sq1"/>
				<xsd:enumeration value="Intel_Core_i3_3240T"/>
				<xsd:enumeration value="fx_4300"/>
				<xsd:enumeration value="Phenom_II_X2_Dual_Core_N660"/>
				<xsd:enumeration value="Athlon_64_X2_Dual_Core_TK_42"/>
				<xsd:enumeration value="Core_2_Duo_T7200"/>
				<xsd:enumeration value="celeron_j1900"/>
				<xsd:enumeration value="Core_i3_2348M"/>
				<xsd:enumeration value="handheld_engine_cxd2230ga"/>
				<xsd:enumeration value="ARMv7_Dual_Core"/>
				<xsd:enumeration value="mc68328"/>
				<xsd:enumeration value="fx_series_quad_core_fx_4170"/>
				<xsd:enumeration value="vr4131"/>
				<xsd:enumeration value="Core_i5_3337U"/>
				<xsd:enumeration value="C_SERIES_C_30"/>
				<xsd:enumeration value="Pentium_B980"/>
				<xsd:enumeration value="core_i7_5675r"/>
				<xsd:enumeration value="pentium_g3460t"/>
				<xsd:enumeration value="A4_6210"/>
				<xsd:enumeration value="Core_2_Duo_1.83GHz"/>
				<xsd:enumeration value="amd_v_series"/>
				<xsd:enumeration value="ARMv7"/>
				<xsd:enumeration value="celeron_n2820"/>
				<xsd:enumeration value="Atom_N435"/>
				<xsd:enumeration value="athlon_200ge"/>
				<xsd:enumeration value="Vision_A10_Quad_Core_APU"/>
				<xsd:enumeration value="Pentium_E2140"/>
				<xsd:enumeration value="Celeron_M_540"/>
				<xsd:enumeration value="Pentium_E6500"/>
				<xsd:enumeration value="core_i3_2330"/>
				<xsd:enumeration value="Atom_330"/>
				<xsd:enumeration value="Core_i3_4160"/>
				<xsd:enumeration value="FX_Series_Six_Core_FX_6300"/>
				<xsd:enumeration value="TEGRA_250"/>
				<xsd:enumeration value="Core_2_Duo_T6670_"/>
				<xsd:enumeration value="core_i9_7900x"/>
				<xsd:enumeration value="Core_2_Duo_T2390"/>
				<xsd:enumeration value="powerpc_750cx"/>
				<xsd:enumeration value="core_i5_5287u"/>
				<xsd:enumeration value="athlon_silver_3050U"/>
				<xsd:enumeration value="Atom_Z2560"/>
				<xsd:enumeration value="Celeron_M_530"/>
				<xsd:enumeration value="crusoe_tm3200"/>
				<xsd:enumeration value="Core_i3_4130T"/>
				<xsd:enumeration value="intel_core_i7_5850hq"/>
				<xsd:enumeration value="Core_i3_4150"/>
				<xsd:enumeration value="Celeron_E3400"/>
				<xsd:enumeration value="core_i5_6267u"/>
				<xsd:enumeration value="core_i3_5157u"/>
				<xsd:enumeration value="Celeron_E1200"/>
				<xsd:enumeration value="celeron_n2840"/>
				<xsd:enumeration value="Core_i7_4960X"/>
				<xsd:enumeration value="ryzen_3_3200g"/>
				<xsd:enumeration value="Atom_N455"/>
				<xsd:enumeration value="Core_i7_4702HQ"/>
				<xsd:enumeration value="Pentium_E2160"/>
				<xsd:enumeration value="Atom_N450"/>
				<xsd:enumeration value="core_i7_3632qn"/>
				<xsd:enumeration value="a_series_quad_core_a8_3500m"/>
				<xsd:enumeration value="C_Series_C_50"/>
				<xsd:enumeration value="Celeron_M_520"/>
				<xsd:enumeration value="pentium_N5000"/>
				<xsd:enumeration value="Pentium_G2030T"/>
				<xsd:enumeration value="athlon_64_fx"/>
				<xsd:enumeration value="Core_i5_3550"/>
				<xsd:enumeration value="core_i5_7200u"/>
				<xsd:enumeration value="r12000"/>
				<xsd:enumeration value="OMAP_3500"/>
				<xsd:enumeration value="pentium_gold_g5600"/>
				<xsd:enumeration value="Pentium_E5200"/>
				<xsd:enumeration value="Pentium_T4200"/>
				<xsd:enumeration value="ryzen_3_3200x"/>
				<xsd:enumeration value="ryzen_7_pro_2700u"/>
				<xsd:enumeration value="e2_7110"/>
				<xsd:enumeration value="Snapdragon_S4_MSM8230"/>
				<xsd:enumeration value="ryzen_3_3200u"/>
				<xsd:enumeration value="e_series_single_core_e_240"/>
				<xsd:enumeration value="Core_2_Duo_E7600"/>
				<xsd:enumeration value="Core_2_Duo_T5870"/>
				<xsd:enumeration value="tegra_2_t20"/>
				<xsd:enumeration value="68ez328"/>
				<xsd:enumeration value="athlon_4"/>
				<xsd:enumeration value="alpha_21264"/>
				<xsd:enumeration value="Core_2_Duo_SU4100"/>
				<xsd:enumeration value="atom_z3745d"/>
				<xsd:enumeration value="A_Series_Quad_Core_A6"/>
				<xsd:enumeration value="Core_2_Duo_E4500"/>
				<xsd:enumeration value="Core_2_Quad_Q9400S"/>
				<xsd:enumeration value="Celeron_M_585"/>
				<xsd:enumeration value="Core_i5_2435M"/>
				<xsd:enumeration value="xeon_platinum_8176"/>
				<xsd:enumeration value="ARM_610"/>
				<xsd:enumeration value="Core_2_Duo_T9800"/>
				<xsd:enumeration value="Core_i5_2520M"/>
				<xsd:enumeration value="intel_core_i5_6500"/>
				<xsd:enumeration value="xeon_platinum_8170"/>
				<xsd:enumeration value="tegra_2_t25"/>
				<xsd:enumeration value="A_Series_Quad_Core_A8"/>
				<xsd:enumeration value="Celeron_1017U"/>
				<xsd:enumeration value="atom_z2520"/>
				<xsd:enumeration value="powerpc_403ga"/>
				<xsd:enumeration value="Sempron_LE_1300"/>
				<xsd:enumeration value="Pentium_T2080"/>
				<xsd:enumeration value="Celeron_M_575"/>
				<xsd:enumeration value="core_i5_8400t"/>
				<xsd:enumeration value="Core_2_Duo_T5450"/>
				<xsd:enumeration value="xeon_platinum_8180"/>
				<xsd:enumeration value="ryzen_threadripper_2920x"/>
				<xsd:enumeration value="core_i7_3630m"/>
				<xsd:enumeration value="core_m_family"/>
				<xsd:enumeration value="ryzen_9_3950x"/>
				<xsd:enumeration value="core_i7_6700t"/>
				<xsd:enumeration value="Core_2_Quad_Q9300"/>
				<xsd:enumeration value="Athlon_64_X2_5050"/>
				<xsd:enumeration value="celeron_n2807"/>
				<xsd:enumeration value="Celeron_M_560"/>
				<xsd:enumeration value="ryzen_7_4800h"/>
				<xsd:enumeration value="celeron_n2808"/>
				<xsd:enumeration value="r14000a"/>
				<xsd:enumeration value="celeron_n2805"/>
				<xsd:enumeration value="Core_2_Duo_E6700"/>
				<xsd:enumeration value="xeon_platinum_8158"/>
				<xsd:enumeration value="celeron_n2806"/>
				<xsd:enumeration value="xeon_platinum_8156"/>
				<xsd:enumeration value="OMAP_4400"/>
				<xsd:enumeration value="xeon_platinum_8153"/>
				<xsd:enumeration value="core_i7_8750h"/>
				<xsd:enumeration value="Core_2_Duo_T7600"/>
				<xsd:enumeration value="Exynos_3110"/>
				<xsd:enumeration value="Core_2_Quad_Q8200S"/>
				<xsd:enumeration value="core_i5_4308u"/>
				<xsd:enumeration value="Rockchip_RK2928"/>
				<xsd:enumeration value="Turion_X2_ZM_80"/>
				<xsd:enumeration value="core_i5_32320m"/>
				<xsd:enumeration value="celeron_n2810"/>
				<xsd:enumeration value="Turion_X2_ZM_82"/>
				<xsd:enumeration value="celeron_n2815"/>
				<xsd:enumeration value="ryzen_7_4800u"/>
				<xsd:enumeration value="core_i7_6700k"/>
				<xsd:enumeration value="Celeron_M_550"/>
				<xsd:enumeration value="Core_i5_4440"/>
				<xsd:enumeration value="xeon_platinum_8168"/>
				<xsd:enumeration value="xeon_platinum_8164"/>
				<xsd:enumeration value="Athlon_II_X2_Dual_Core_245e_"/>
				<xsd:enumeration value="Pentium_T2060"/>
				<xsd:enumeration value="Core_i3_4130"/>
				<xsd:enumeration value="xeon_platinum_8160"/>
				<xsd:enumeration value="s3c2442"/>
				<xsd:enumeration value="Atom_Z2580"/>
				<xsd:enumeration value="s3c2440"/>
				<xsd:enumeration value="ryzen_3_pro_2300u"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_240e"/>
				<xsd:enumeration value="Core_i3_540M"/>
				<xsd:enumeration value="Turion_II_X2_Dual_Core_M620"/>
				<xsd:enumeration value="none"/>
				<xsd:enumeration value="Core_i3_4005U"/>
				<xsd:enumeration value="xeon_gold_5122"/>
				<xsd:enumeration value="Turion_64_X2_TL_50"/>
				<xsd:enumeration value="Core_i7_870"/>
				<xsd:enumeration value="r5000"/>
				<xsd:enumeration value="Celeron_220"/>
				<xsd:enumeration value="Core_2_Duo_T5800"/>
				<xsd:enumeration value="Atom_N2800"/>
				<xsd:enumeration value="Core_2_Duo_E6320"/>
				<xsd:enumeration value="Core_i3_3110M"/>
				<xsd:enumeration value="Turion_64_X2_TL_56"/>
				<xsd:enumeration value="Turion_64_X2_TL_57"/>
				<xsd:enumeration value="Turion_64_X2_TL_58"/>
				<xsd:enumeration value="core_i3_4170t"/>
				<xsd:enumeration value="Turion_64_X2_TL_52"/>
				<xsd:enumeration value="xeon_gold_5120"/>
				<xsd:enumeration value="Core_2_Duo_E8500"/>
				<xsd:enumeration value="i7_3930k"/>
				<xsd:enumeration value="Core_i3_3220"/>
				<xsd:enumeration value="Turion_X2_Dual_Core_RM_75"/>
				<xsd:enumeration value="Phenom_II_X4_N960"/>
				<xsd:enumeration value="pentium_dual_core"/>
				<xsd:enumeration value="80486sx"/>
				<xsd:enumeration value="Core_i5_2390T"/>
				<xsd:enumeration value="E_Series_Dual_Core_E2_1800"/>
				<xsd:enumeration value="xeon_gold_5115"/>
				<xsd:enumeration value="MediaTek_MT8125"/>
				<xsd:enumeration value="amd_e_series"/>
				<xsd:enumeration value="xeon_gold_5118"/>
				<xsd:enumeration value="Core_i7_3820QM"/>
				<xsd:enumeration value="a_series_dual_core_a4_3305m"/>
				<xsd:enumeration value="Core_i7_2657M"/>
				<xsd:enumeration value="8803_CORTEX_A8"/>
				<xsd:enumeration value="pentium_iii"/>
				<xsd:enumeration value="Snapdragon_QSD8250"/>
				<xsd:enumeration value="Celeron_J1900"/>
				<xsd:enumeration value="8031"/>
				<xsd:enumeration value="intel_strongarm"/>
				<xsd:enumeration value="8032"/>
				<xsd:enumeration value="A_Series_Dual_Core_A4_4300M"/>
				<xsd:enumeration value="Core_2_Duo_L7500"/>
				<xsd:enumeration value="80386"/>
				<xsd:enumeration value="Core_2_Duo_2.2GHz"/>
				<xsd:enumeration value="core_i5_6260u"/>
				<xsd:enumeration value="Pentium_G840"/>
				<xsd:enumeration value="s3c2410"/>
				<xsd:enumeration value="Athlon_II_Dual_Core_P360"/>
				<xsd:enumeration value="sh7709a"/>
				<xsd:enumeration value="core_i5_6350hq"/>
				<xsd:enumeration value="Turion_64_X2_TL_60"/>
				<xsd:enumeration value="pentium_2030m"/>
				<xsd:enumeration value="Core_i7_860"/>
				<xsd:enumeration value="Turion_64_X2_TL_62"/>
				<xsd:enumeration value="efficeon"/>
				<xsd:enumeration value="Core_i5_2.8GHz"/>
				<xsd:enumeration value="Core_i5_4570T"/>
				<xsd:enumeration value="Core_i7_3615QM"/>
				<xsd:enumeration value="Core_2_Duo_P8400"/>
				<xsd:enumeration value="Core_2_Duo_E7200"/>
				<xsd:enumeration value="Core_i5_4570S"/>
				<xsd:enumeration value="Turion_64_X2_TL_68"/>
				<xsd:enumeration value="Turion_64_X2_TL_64"/>
				<xsd:enumeration value="Core_2_Duo_P7550"/>
				<xsd:enumeration value="Turion_64_X2_TL_66"/>
				<xsd:enumeration value="Celeron_T1600"/>
				<xsd:enumeration value="A_Series_Eight_Core_A10_5800K"/>
				<xsd:enumeration value="mediagxlv"/>
				<xsd:enumeration value="atom_e3815"/>
				<xsd:enumeration value="ryzen_5_3600x"/>
				<xsd:enumeration value="Sempron_M_3100"/>
				<xsd:enumeration value="Core_i5_480M"/>
				<xsd:enumeration value="Atom_N475"/>
				<xsd:enumeration value="Core_2_Duo_SU9400"/>
				<xsd:enumeration value="atom_z3460"/>
				<xsd:enumeration value="pentium_3805u"/>
				<xsd:enumeration value="Core_i3_2390T"/>
				<xsd:enumeration value="core_i7_family"/>
				<xsd:enumeration value="Athlon_II_X3_445AIIX3"/>
				<xsd:enumeration value="Atom_N470"/>
				<xsd:enumeration value="Athlon_Dual_Core_QL62A"/>
				<xsd:enumeration value="Pentium_G6950"/>
				<xsd:enumeration value="Core_2_Duo_T2330"/>
				<xsd:enumeration value="Pentium_G860"/>
				<xsd:enumeration value="Core_Duo_T2400__"/>
				<xsd:enumeration value="pa_8200"/>
				<xsd:enumeration value="Xeon_5400"/>
				<xsd:enumeration value="ryzen_3_3100"/>
				<xsd:enumeration value="core_i5_4260u"/>
				<xsd:enumeration value="Core_i3_4570T"/>
				<xsd:enumeration value="core_i3_5020u"/>
				<xsd:enumeration value="Core_2_Duo_T5850"/>
				<xsd:enumeration value="Pentium_G850"/>
				<xsd:enumeration value="ryzen_5_2400g"/>
				<xsd:enumeration value="pentium_mmx"/>
				<xsd:enumeration value="sempron"/>
				<xsd:enumeration value="atom_d2701"/>
				<xsd:enumeration value="Phenom_II_X4_Quad_Core_N820"/>
				<xsd:enumeration value="GP33003"/>
				<xsd:enumeration value="atom_d2700"/>
				<xsd:enumeration value="i7_2640m"/>
				<xsd:enumeration value="Core_2_Duo_3.06GHz"/>
				<xsd:enumeration value="a10_7400p"/>
				<xsd:enumeration value="Core_2_Duo_T2310"/>
				<xsd:enumeration value="xeon_e5_1650"/>
				<xsd:enumeration value="amd_ryzen_7_pro_1700"/>
				<xsd:enumeration value="Core_2_Duo_E6300"/>
				<xsd:enumeration value="core_i7_6770hq"/>
				<xsd:enumeration value="atom_n2830"/>
				<xsd:enumeration value="Atom_Z330"/>
				<xsd:enumeration value="atom_c3230_rk"/>
				<xsd:enumeration value="mobile_celeron"/>
				<xsd:enumeration value="powerpc_rs64_iii"/>
				<xsd:enumeration value="Core_i3_380UM"/>
				<xsd:enumeration value="1_2GHz_Cortex_A8"/>
				<xsd:enumeration value="1_2GHz_Cortex_A9"/>
				<xsd:enumeration value="Core_i3_4000M"/>
				<xsd:enumeration value="a_series_dual_core_a4_3300m"/>
				<xsd:enumeration value="A_Series_Eight_Core_A10_4600M"/>
				<xsd:enumeration value="ryzen_threadripper_1900x"/>
				<xsd:enumeration value="atom_z650"/>
				<xsd:enumeration value="atom_z3740d"/>
				<xsd:enumeration value="k6_2e"/>
				<xsd:enumeration value="r16000b"/>
				<xsd:enumeration value="FX_8300"/>
				<xsd:enumeration value="Core_Duo_1.83GHz"/>
				<xsd:enumeration value="r16000a"/>
				<xsd:enumeration value="Pentium_G870"/>
				<xsd:enumeration value="Rockchip_3188"/>
				<xsd:enumeration value="Pentium_G6960"/>
			</xsd:restriction>
                    </xsd:simpleType>
	<xsd:complexType name="CapacityDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="CapacityUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="CapacityUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="kg"/>
            <xsd:enumeration value="place_settings"/>
        </xsd:restriction>
    </xsd:simpleType>
	<xsd:complexType name="OptionalMotorSizeDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="MotorSizeUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="MotorSizeUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="horsepower"/>
            <xsd:enumeration value="kilowatts"/>
        </xsd:restriction>
    </xsd:simpleType>
	<xsd:simpleType name="RepairabilityIndexScheme">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="french_circular_economy_law"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EcMedicalDeviceSalesChannelValues">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="exempt"/>
			<xsd:enumeration value="otc_other"/>
			<xsd:enumeration value="otc_pharmacy"/>
			<xsd:enumeration value="otc_retail"/>
			<xsd:enumeration value="prescription_other"/>
			<xsd:enumeration value="prescription_pharmacy"/>
			<xsd:enumeration value="prescription_retail"/>
			<xsd:enumeration value="professional_use"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="EuEnergyEfficiencyRatingValues">
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="a"/>
		<xsd:enumeration value="b"/>
		<xsd:enumeration value="c"/>
		<xsd:enumeration value="d"/>
		<xsd:enumeration value="e"/>
		<xsd:enumeration value="not_rated"/>
		<xsd:enumeration value="f"/>
		<xsd:enumeration value="g"/>
	</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VersionForCountryType">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[A-Z][A-Z]"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>