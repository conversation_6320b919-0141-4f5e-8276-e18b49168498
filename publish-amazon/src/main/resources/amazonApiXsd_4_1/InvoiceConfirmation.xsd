<?xml version="1.0"?>
<!-- Revision="$Revision: #2 $" -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<!--
    $Date: 2005/04/01 $

    AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
    confidential and proprietary information of Amazon.com and may not be reproduced, 
    distributed or used, in whole or in part, for any purpose other than as necessary 
    to list products for sale on the www.amazon.com web site pursuant to an agreement 
    with Amazon.com.
    -->
	<xsd:include schemaLocation="amzn-base.xsd"/>
	<xsd:element name="InvoiceConfirmation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element ref="AmazonOrderID"/>
				<xsd:element name="InvoiceSentDate" type="xsd:dateTime"/>
				<xsd:element name="Item" minOccurs="0" maxOccurs="unbounded">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element ref="AmazonOrderItemCode"/>
							<xsd:element name="QuantityConfirmed" type="xsd:positiveInteger"
								minOccurs="0"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
