<?xml version="1.0" encoding="UTF-8"?>
<!-- Revision="$Revision: #1 $" -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <!--
  			 $Date: 20011/06/22/ $      
         AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
         confidential and proprietary information of Amazon.com and may not be reproduced,
         distributed or used, in whole or in part, for any purpose other than as necessary 
         to list products for sale on the www.amazon.com web site pursuant to an agreement
         with Amazon.com.
	-->
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="FBA">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="IsExpirationDatedProduct" type="xsd:boolean" minOccurs="0"/>
                <xsd:element name="IsHighValue" type="xsd:boolean" minOccurs="0"/>
                <xsd:element name="CanShipInOriginalContainer" type="xsd:boolean" minOccurs="0"/>
                <xsd:element name="SerialNumScan" type="xsd:boolean" minOccurs="0"/>
                <xsd:element name="HazmatItem" type="HazmatItemType" minOccurs="0"/>
                <xsd:element name="AmazonMaturityRating" type="AmazonMaturityRatingType"
                    minOccurs="0"/>
                <xsd:element name="IdentityPackage" type="IdentityPackageType" minOccurs="0"/>
                <xsd:element name="SerialNumberFormat" type="SerialNumberFormatType" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
