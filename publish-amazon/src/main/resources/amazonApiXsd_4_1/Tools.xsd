<?xml version="1.0"?>
<!-- Revision="$Revision: #3 $" -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

    <!--
    $Date: 2005/04/01 $

    AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
    confidential and proprietary information of Amazon.com and may not be reproduced, 
    distributed or used, in whole or in part, for any purpose other than as necessary 
    to list products for sale on the www.amazon.com web site pursuant to an agreement 
    with Amazon.com.
    -->

    <xsd:include schemaLocation="amzn-base.xsd"/>

    <xsd:element name="Tools">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="GritRating" type="xsd:positiveInteger" minOccurs="0"/>
                <xsd:element name="Horsepower" type="ToolsHorsepower" minOccurs="0"/>
                <xsd:element name="StyleName" type="StringNotNull" minOccurs="0"/>
                <xsd:element name="FinishTypes" type="StringNotNull" minOccurs="0"/>
                <xsd:element name="Diameter" type="LengthDimension" minOccurs="0"/>
                <xsd:element name="Length" type="LengthDimension" minOccurs="0"/>
                <xsd:element name="Width" type="LengthDimension" minOccurs="0"/>
                <xsd:element name="Height" type="LengthDimension" minOccurs="0"/>
                <xsd:element name="Weight" type="WeightDimension" minOccurs="0"/>
                
                <xsd:element name="PowerSource" minOccurs="0" maxOccurs="2">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="battery-powered"/>
                            <xsd:enumeration value="gas-powered"/>
                            <xsd:enumeration value="hydraulic-powered"/>
                            <xsd:enumeration value="air-powered"/>
                            <xsd:enumeration value="corded-electric"/>                            
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                
                <xsd:element name="Wattage" type="xsd:positiveInteger" minOccurs="0"/>
                <xsd:element name="Voltage" type="xsd:positiveInteger" minOccurs="0"/>
                <xsd:element name="NumberOfItemsInPackage" type="xsd:positiveInteger" minOccurs="0"/>
		<xsd:element name="BatteryTypeLithiumIon" type="xsd:positiveInteger" minOccurs="0"/>
		<xsd:element name="BatteryTypeLithiumMetal" type="xsd:positiveInteger" minOccurs="0"/>
		<xsd:element name="LithiumBatteryEnergyContent" type="xsd:decimal" minOccurs="0"/>
		<xsd:element name="LithiumBatteryPackaging" minOccurs="0">
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:enumeration value="batteries_contained_in_equipment"/>
					<xsd:enumeration value="batteries_only"/>
					<xsd:enumeration value="batteries_packed_with_equipment"/>
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="LithiumBatteryVoltage" type="xsd:decimal" minOccurs="0"/>
		<xsd:element name="LithiumBatteryWeight" type="xsd:decimal" minOccurs="0"/>
		<xsd:element name="NumberOfLithiumIonCells" type="xsd:positiveInteger" minOccurs="0"/>
		<xsd:element name="NumberOfLithiumMetalCells" type="xsd:positiveInteger" minOccurs="0"/>
		<xsd:element name="CustomerRestrictionType" type="StringNotNull" minOccurs="0"/>
		<xsd:element name="PowerPlugType" type="PowerPlugType" minOccurs="0"/>
       </xsd:sequence>
      </xsd:complexType>
    </xsd:element>




    <!--
     ###############################################################
     # Tools type definitions
     ###############################################################
    -->

    <xsd:simpleType name="ToolsHorsepower">
        <xsd:restriction base="xsd:decimal">
            <xsd:totalDigits value="12"/>
            <xsd:fractionDigits value="2" fixed="true"/>
        </xsd:restriction>
    </xsd:simpleType>


</xsd:schema>
