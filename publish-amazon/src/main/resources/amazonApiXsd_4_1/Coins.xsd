<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="Coins">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element ref="Coin"/>
                            <xsd:element ref="CollectibleCoins"/>
                            <xsd:element ref="Bullion"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Coin">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="10" minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MintMark" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DenominationUnit" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeriesTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Variety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubVariety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradedBy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CertificateNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitGrouping" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EachUnitCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="StyleName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EdgeStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designer" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistID" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EstatePeriod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCoint" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TotalMetalWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MetalStamp" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="LabelDescription" type="StringNotNull"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CollectibleCoins">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="10" minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MintMark" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DenominationUnit" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeriesTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Variety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubVariety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradedBy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CertificateNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitGrouping" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EachUnitCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="StyleName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EdgeStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designer" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistID" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EstatePeriod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCoint" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TotalMetalWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MetalStamp" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="LabelDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackLabelImageUrl" type="xsd:anyURI"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Bullion">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="10" minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MintMark" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DenominationUnit" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeriesTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Variety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubVariety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradedBy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CertificateNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitGrouping" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EachUnitCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="StyleName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EdgeStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Designer" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistID" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EstatePeriod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCoint" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TotalMetalWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MetalStamp" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="LabelDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackLabelImageUrl" type="xsd:anyURI"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MetalType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContactPlatingMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CertificateType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Importer" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
