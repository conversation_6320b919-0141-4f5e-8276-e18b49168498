<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonSellerskuTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplate" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="seller_id" property="sellerId" jdbcType="VARCHAR" />
    <result column="country" property="country" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="son_sku" property="sonSku" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="template_ids" property="templateIds" jdbcType="VARCHAR" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="exist_listing" property="existListing" jdbcType="BIT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, seller_id, country, main_sku, son_sku, seller_sku, template_ids, publish_type, 
    exist_listing, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_sellersku_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_sellersku_template
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_sellersku_template
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplate" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_sellersku_template (seller_id, country, main_sku, 
      son_sku, seller_sku, template_ids, 
      publish_type, exist_listing, create_time
      )
    values (#{sellerId,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, 
      #{sonSku,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{templateIds,jdbcType=VARCHAR}, 
      #{publishType,jdbcType=INTEGER}, #{existListing,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplateExample" resultType="java.lang.Integer" >
    select count(*) from amazon_sellersku_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_sellersku_template
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null" >
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.sonSku != null" >
        son_sku = #{record.sonSku,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.templateIds != null" >
        template_ids = #{record.templateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=INTEGER},
      </if>
      <if test="record.existListing != null" >
        exist_listing = #{record.existListing,jdbcType=BIT},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplate" >
    update amazon_sellersku_template
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="sonSku != null" >
        son_sku = #{sonSku,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="templateIds != null" >
        template_ids = #{templateIds,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="existListing != null" >
        exist_listing = #{existListing,jdbcType=BIT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerskuTemplate" >
    insert ignore into amazon_sellersku_template (
    seller_id, country, main_sku,
    son_sku, seller_sku, template_ids,
    publish_type, exist_listing, create_time)
    values
    <foreach collection="list"  item="item" separator=",">
      (
      #{item.sellerId,jdbcType=VARCHAR}, #{item.country,jdbcType=VARCHAR}, #{item.mainSku,jdbcType=VARCHAR},
      #{item.sonSku,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.templateIds,jdbcType=VARCHAR},
      #{item.publishType,jdbcType=INTEGER}, #{item.existListing,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>