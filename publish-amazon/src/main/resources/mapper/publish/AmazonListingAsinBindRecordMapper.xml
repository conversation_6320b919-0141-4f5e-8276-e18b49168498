<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonListingAsinBindRecordMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="origin_asin" property="originAsin" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="browse_node_id" property="browseNodeId" jdbcType="VARCHAR" />
    <result column="variation_theme" property="variationTheme" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="model_number" property="modelNumber" jdbcType="VARCHAR" />
    <result column="child_asins" property="childAsins" jdbcType="VARCHAR" />
    <result column="child_seller_skus" property="childSellerSkus" jdbcType="VARCHAR" />
    <result column="extra_data" property="extraData" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="upload_success" property="uploadSuccess" jdbcType="BIT" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, origin_asin, seller_sku, article_number, item_name, product_type, 
    browse_node_id, variation_theme, brand_name, manufacturer, model_number, child_asins, 
    child_seller_skus, extra_data, `status`, upload_success, last_update_by, create_date, 
    update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_listing_asin_bind_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_listing_asin_bind_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_asin_bind_record
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecord" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_asin_bind_record (account_number, site, origin_asin, 
      seller_sku, article_number, item_name, 
      product_type, browse_node_id, variation_theme, 
      brand_name, manufacturer, model_number, 
      child_asins, child_seller_skus, extra_data, 
      `status`, upload_success, last_update_by, 
      create_date, update_date)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{originAsin,jdbcType=VARCHAR}, 
      #{sellerSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, 
      #{productType,jdbcType=VARCHAR}, #{browseNodeId,jdbcType=VARCHAR}, #{variationTheme,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{modelNumber,jdbcType=VARCHAR}, 
      #{childAsins,jdbcType=VARCHAR}, #{childSellerSkus,jdbcType=VARCHAR}, #{extraData,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{uploadSuccess,jdbcType=BIT}, #{lastUpdateBy,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecordExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_asin_bind_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_asin_bind_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.originAsin != null" >
        origin_asin = #{record.originAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null" >
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeId != null" >
        browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.variationTheme != null" >
        variation_theme = #{record.variationTheme,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null" >
        model_number = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.childAsins != null" >
        child_asins = #{record.childAsins,jdbcType=VARCHAR},
      </if>
      <if test="record.childSellerSkus != null" >
        child_seller_skus = #{record.childSellerSkus,jdbcType=VARCHAR},
      </if>
      <if test="record.extraData != null" >
        extra_data = #{record.extraData,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.uploadSuccess != null" >
        upload_success = #{record.uploadSuccess,jdbcType=BIT},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingAsinBindRecord" >
    update amazon_listing_asin_bind_record
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="originAsin != null" >
        origin_asin = #{originAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null" >
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="variationTheme != null" >
        variation_theme = #{variationTheme,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null" >
        model_number = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="childAsins != null" >
        child_asins = #{childAsins,jdbcType=VARCHAR},
      </if>
      <if test="childSellerSkus != null" >
        child_seller_skus = #{childSellerSkus,jdbcType=VARCHAR},
      </if>
      <if test="extraData != null" >
        extra_data = #{extraData,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="uploadSuccess != null" >
        upload_success = #{uploadSuccess,jdbcType=BIT},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>