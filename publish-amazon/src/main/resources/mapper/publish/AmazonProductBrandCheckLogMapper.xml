<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonProductBrandCheckLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonProductBrandCheckLog" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="account_config_brand" property="accountConfigBrand" jdbcType="VARCHAR" />
    <result column="order_num_total" property="orderNumTotal" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="sale_leader_man" property="saleLeaderMan" jdbcType="VARCHAR" />
    <result column="sale_supervisor" property="saleSupervisor" jdbcType="VARCHAR" />
    <result column="account_history_brand" property="accountHistoryBrand" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, site, account_number, seller_sku, brand, account_config_brand, order_num_total, 
    create_by, create_date, update_by, update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonProductBrandCheckLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_product_brand_check_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_product_brand_check_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_product_brand_check_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonProductBrandCheckLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_product_brand_check_log (site, account_number, seller_sku, 
      brand, account_config_brand, order_num_total, 
      create_by, create_date, update_by, 
      update_date)
    values (#{site,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{accountConfigBrand,jdbcType=VARCHAR}, #{orderNumTotal,jdbcType=INTEGER}, 
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProductBrandCheckLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_product_brand_check_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_product_brand_check_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.accountConfigBrand != null" >
        account_config_brand = #{record.accountConfigBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNumTotal != null" >
        order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProductBrandCheckLog" >
    update amazon_product_brand_check_log
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="accountConfigBrand != null" >
        account_config_brand = #{accountConfigBrand,jdbcType=VARCHAR},
      </if>
      <if test="orderNumTotal != null" >
        order_num_total = #{orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


  <insert id="batchInsert">
    insert <if test="ignore != null and ignore != ''">${ignore}</if>
    into amazon_product_brand_check_log (site, account_number, seller_sku,
    brand, account_config_brand, order_num_total,
    create_by, create_date, update_by,
    update_date,sale_man, sale_leader_man, sale_supervisor,account_history_brand,parent_asin,son_asin,sku
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.site,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR},
      #{item.brand,jdbcType=VARCHAR}, #{item.accountConfigBrand,jdbcType=VARCHAR}, #{item.orderNumTotal,jdbcType=INTEGER},
      #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
      #{item.updateDate,jdbcType=TIMESTAMP}, #{item.saleMan,jdbcType=VARCHAR}, #{item.saleLeaderMan,jdbcType=VARCHAR},
      #{item.saleSupervisor,jdbcType=VARCHAR}, #{item.accountHistoryBrand,jdbcType=VARCHAR},
      #{item.parentAsin,jdbcType=VARCHAR}, #{item.sonAsin,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>