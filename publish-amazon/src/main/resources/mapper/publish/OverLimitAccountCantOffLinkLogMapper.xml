<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.OverLimitAccountCantOffLinkLogMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.OverLimitAccountCantOffLinkLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="account_number" property="accountNumber" jdbcType="VARCHAR"/>
        <result column="site" property="site" jdbcType="VARCHAR"/>
        <result column="link_type" property="linkType" jdbcType="INTEGER"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
        <result column="asin" property="asin" jdbcType="VARCHAR"/>
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR"/>
        <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR"/>
        <result column="sales_total_count" property="salesTotalCount" jdbcType="INTEGER"/>
        <result column="open_time" property="openTime" jdbcType="TIMESTAMP"/>
        <result column="statistics_date" property="statisticsDate" jdbcType="DATE"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , account_number, site, link_type, task_type, asin, article_number, seller_sku,
    sales_total_count, open_time, statistics_date, created_time, updated_time, parent_asin
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountCantOffLinkLogExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from over_limit_account_cant_off_link_log${tableIndex}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from over_limit_account_cant_off_link_log${tableIndex}
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey">
        delete from over_limit_account_cant_off_link_log${tableIndex}
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountCantOffLinkLog">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into over_limit_account_cant_off_link_log${tableIndex} (account_number, site, link_type,
        task_type, asin, article_number,
        seller_sku, sales_total_count, open_time,
        statistics_date, created_time, updated_time,
        parent_asin)
        values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{linkType,jdbcType=INTEGER},
        #{taskType,jdbcType=INTEGER}, #{asin,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR},
        #{sellerSku,jdbcType=VARCHAR}, #{salesTotalCount,jdbcType=INTEGER}, #{openTime,jdbcType=TIMESTAMP},
        #{statisticsDate,jdbcType=DATE}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP},
        #{parentAsin,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into over_limit_account_cant_off_link_log${tableIndex}
        (account_number, site, link_type,
        task_type, asin, article_number,
        seller_sku, sales_total_count, open_time,
        statistics_date, created_time, updated_time,
        parent_asin)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR},
            #{item.linkType,jdbcType=INTEGER},#{item.taskType,jdbcType=INTEGER},
            #{item.asin,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR},
            #{item.salesTotalCount,jdbcType=INTEGER}, #{item.openTime,jdbcType=TIMESTAMP},
            #{item.statisticsDate,jdbcType=DATE},
            #{item.createdTime,jdbcType=TIMESTAMP}, #{item.updatedTime,jdbcType=TIMESTAMP},
            #{item.parentAsin,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <select id="countByExample"
            parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountCantOffLinkLogExample"
            resultType="java.lang.Integer">
        select count(*) from over_limit_account_cant_off_link_log${tableIndex}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="getAccountCantOffLinkTotalByDate" resultType="java.lang.Integer">
        select count(*)
        from over_limit_account_cant_off_link_log${tableIndex}
        where account_number = #{accountNumber}
          and statistics_date = #{statisticalDate}
          and task_type = #{taskType}
    </select>
    <select id="getExistParentAsinList" resultType="java.lang.String">
        select distinct parent_asin
        from over_limit_account_cant_off_link_log${tableIndex}
        where statistics_date = #{statisticalDate}
        and parent_asin in
        <foreach collection="parentAsinList" item="asin" open="(" separator="," close=")">
            #{asin}
        </foreach>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update over_limit_account_cant_off_link_log${tableIndex}
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.accountNumber != null">
                account_number = #{record.accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.site != null">
                site = #{record.site,jdbcType=VARCHAR},
            </if>
            <if test="record.linkType != null">
                link_type = #{record.linkType,jdbcType=INTEGER},
            </if>
            <if test="record.asin != null">
                asin = #{record.asin,jdbcType=VARCHAR},
            </if>
            <if test="record.articleNumber != null">
                article_number = #{record.articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.sellerSku != null">
                seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
            </if>
            <if test="record.salesTotalCount != null">
                sales_total_count = #{record.salesTotalCount,jdbcType=INTEGER},
            </if>
            <if test="record.openTime != null">
                open_time = #{record.openTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.statisticsDate != null">
                statistics_date = #{record.statisticsDate,jdbcType=DATE},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedTime != null">
                updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountCantOffLinkLog">
        update over_limit_account_cant_off_link_log${tableIndex}
        <set>
            <if test="accountNumber != null">
                account_number = #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="site != null">
                site = #{site,jdbcType=VARCHAR},
            </if>
            <if test="linkType != null">
                link_type = #{linkType,jdbcType=INTEGER},
            </if>
            <if test="asin != null">
                asin = #{asin,jdbcType=VARCHAR},
            </if>
            <if test="articleNumber != null">
                article_number = #{articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="sellerSku != null">
                seller_sku = #{sellerSku,jdbcType=VARCHAR},
            </if>
            <if test="salesTotalCount != null">
                sales_total_count = #{salesTotalCount,jdbcType=INTEGER},
            </if>
            <if test="openTime != null">
                open_time = #{openTime,jdbcType=TIMESTAMP},
            </if>
            <if test="statisticsDate != null">
                statistics_date = #{statisticsDate,jdbcType=DATE},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>