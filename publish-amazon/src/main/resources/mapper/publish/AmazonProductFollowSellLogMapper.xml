<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonProductFollowSellLogMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="relation_key" jdbcType="VARCHAR" property="relationKey" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="seller_sku" jdbcType="VARCHAR" property="sellerSku" />
    <result column="standard_prodcut_id_type" jdbcType="VARCHAR" property="standardProdcutIdType" />
    <result column="standard_prodcut_id_value" jdbcType="VARCHAR" property="standardProdcutIdValue" />
    <result column="standard_price" jdbcType="DOUBLE" property="standardPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, relation_key, account_number, country, currency, seller_sku, standard_prodcut_id_type, standard_prodcut_id_value,
    standard_price, quantity, create_by, create_date
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_product_follow_sell_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from amazon_product_follow_sell_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey">
    delete from amazon_product_follow_sell_log
    where id IN
    <foreach close=")" collection="list" item="listItem" open="(" separator=",">
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_product_follow_sell_log (relation_key, account_number, country, currency, seller_sku,
      standard_prodcut_id_type, standard_prodcut_id_value,
      standard_price, quantity, create_by,
      create_date)
    values (#{relationKey,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR},
      #{standardProdcutIdType,jdbcType=VARCHAR}, #{standardProdcutIdValue,jdbcType=VARCHAR},
      #{standardPrice,jdbcType=DOUBLE}, #{quantity,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR},
      #{createDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLogExample" resultType="java.lang.Integer">
    select count(*) from amazon_product_follow_sell_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <!--根据变体id查询是否存在日志-->
  <select id="selectExistLogByVariantId" resultType="java.lang.String">
    select distinct relation_key from amazon_product_follow_sell_log
    <where>
      <choose>
        <when test="relationKeyList != null and relationKeyList.size > 0">
          relation_key in
          <foreach collection="relationKeyList" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          1 != 1
        </otherwise>
      </choose>
    </where>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update amazon_product_follow_sell_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.relationKey != null">
        relation_key = #{record.relationKey,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null">
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProdcutIdType != null">
        standard_prodcut_id_type = #{record.standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProdcutIdValue != null">
        standard_prodcut_id_value = #{record.standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="record.standardPrice != null">
        standard_price = #{record.standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLog">
    update amazon_product_follow_sell_log
    <set>
      <if test="relationKey != null">
        relation_key = #{relationKey,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null">
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdType != null">
        standard_prodcut_id_type = #{standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdValue != null">
        standard_prodcut_id_value = #{standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="standardPrice != null">
        standard_price = #{standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 一次性任务更改关联key-->
  <update id="updateRelationKey" parameterType="com.estone.erp.publish.amazon.model.AmazonProductFollowSellLog">
    update amazon_product_follow_sell_log
    SET relation_key = #{accountNumber,jdbcType=VARCHAR}
    where relation_key = #{relationKey,jdbcType=VARCHAR}
  </update>
</mapper>