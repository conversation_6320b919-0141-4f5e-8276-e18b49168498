<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonVariantUpdateMsgLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="relation_id" property="relationId" jdbcType="INTEGER" />
    <result column="relation_type" property="relationType" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="item_description" property="itemDescription" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="attribute1" property="attribute1" jdbcType="VARCHAR" />
    <result column="attribute2" property="attribute2" jdbcType="VARCHAR" />
    <result column="attribute3" property="attribute3" jdbcType="VARCHAR" />
    <result column="attribute4" property="attribute4" jdbcType="VARCHAR" />
    <result column="attribute5" property="attribute5" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, relation_id, relation_type, account_number, seller_sku, item_name, item_description, 
    `status`, create_by, create_date, update_date, attribute1, attribute2, attribute3, 
    attribute4, attribute5
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_variant_update_msg_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_variant_update_msg_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_variant_update_msg_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_variant_update_msg_log (relation_id, relation_type, account_number, 
      seller_sku, item_name, item_description, 
      `status`, create_by, create_date, 
      update_date, attribute1, attribute2, 
      attribute3, attribute4, attribute5
      )
    values (#{relationId,jdbcType=INTEGER}, #{relationType,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, 
      #{sellerSku,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, #{itemDescription,jdbcType=VARCHAR}, 
      #{status,jdbcType=BIT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_variant_update_msg_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_variant_update_msg_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.relationId != null" >
        relation_id = #{record.relationId,jdbcType=INTEGER},
      </if>
      <if test="record.relationType != null" >
        relation_type = #{record.relationType,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null" >
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemDescription != null" >
        item_description = #{record.itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.attribute1 != null" >
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null" >
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null" >
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null" >
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLog" >
    update amazon_variant_update_msg_log
    <set >
      <if test="relationId != null" >
        relation_id = #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationType != null" >
        relation_type = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null" >
        item_description = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute1 != null" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <insert id="batchInsertAmazonVariantUpdateMsgLogs" parameterType="java.util.List">
    insert ignore into amazon_variant_update_msg_log (relation_id, relation_type, account_number,
    seller_sku, item_name, item_description,
    `status`, create_by, create_date,
    update_date, attribute1, attribute2,
    attribute3, attribute4, attribute5)
    values
    <foreach collection="amazonVariantUpdateMsgLogs"  item="item" separator=",">
      (
      #{item.relationId,jdbcType=INTEGER}, #{item.relationType,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR},
      #{item.sellerSku,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR}, #{item.itemDescription,jdbcType=VARCHAR},
      #{item.status,jdbcType=BIT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
      #{item.updateDate,jdbcType=TIMESTAMP}, #{item.attribute1,jdbcType=VARCHAR}, #{item.attribute2,jdbcType=VARCHAR},
      #{item.attribute3,jdbcType=VARCHAR}, #{item.attribute4,jdbcType=VARCHAR}, #{item.attribute5,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdateAmazonVariantUpdateMsgLogs" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  amazon_variant_update_msg_log SET
      `status` = #{item.status,jdbcType=BIT},
       attribute5 = #{item.attribute5,jdbcType=VARCHAR},
       update_date = #{item.updateDate,jdbcType=TIMESTAMP}
      where account_number = #{item.accountNumber,jdbcType=VARCHAR}
      and seller_sku = #{item.sellerSku,jdbcType=INTEGER}
      and relation_type = #{item.relationType,jdbcType=VARCHAR}
      and `status` is null
    </foreach>
  </update>

  <select id="selectSellerSkuListByExample" resultType="java.lang.String" parameterType="com.estone.erp.publish.amazon.model.AmazonVariantUpdateMsgLogExample" >
    select seller_sku
    from amazon_variant_update_msg_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>