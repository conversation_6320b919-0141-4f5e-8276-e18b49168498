<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonOperateLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonOperateLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="business_id" property="businessId" jdbcType="INTEGER" />
    <result column="field_name" property="fieldName" jdbcType="VARCHAR" />
    <result column="before" property="before" jdbcType="VARCHAR" />
    <result column="after" property="after" jdbcType="VARCHAR" />
    <result column="message" property="message" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, `type`, account_number, business_id, field_name, `before`, `after`, message, 
    create_by, create_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonOperateLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_operate_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_operate_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonOperateLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_operate_log (`type`, account_number, business_id, 
      field_name, `before`, `after`, 
      message, create_by, create_date
      )
    values (#{type,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{businessId,jdbcType=INTEGER}, 
      #{fieldName,jdbcType=VARCHAR}, #{before,jdbcType=VARCHAR}, #{after,jdbcType=VARCHAR}, 
      #{message,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into amazon_operate_log (`type`, account_number,business_id, field_name,
    `before`, `after`, message,
    create_by, create_date)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.type,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR},#{item.businessId,jdbcType=INTEGER}, #{item.fieldName,jdbcType=VARCHAR},
      #{item.before,jdbcType=VARCHAR}, #{item.after,jdbcType=VARCHAR}, #{item.message,jdbcType=VARCHAR},
      #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonOperateLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_operate_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_operate_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null" >
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.fieldName != null" >
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.before != null" >
        `before` = #{record.before,jdbcType=VARCHAR},
      </if>
      <if test="record.after != null" >
        `after` = #{record.after,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null" >
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonOperateLog" >
    update amazon_operate_log
    <set >
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null" >
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="fieldName != null" >
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="before != null" >
        `before` = #{before,jdbcType=VARCHAR},
      </if>
      <if test="after != null" >
        `after` = #{after,jdbcType=VARCHAR},
      </if>
      <if test="message != null" >
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>