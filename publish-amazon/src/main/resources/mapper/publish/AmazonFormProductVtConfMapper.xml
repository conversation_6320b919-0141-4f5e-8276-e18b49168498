<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonFormProductVtConfMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonFormProductVtConf" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="template_site" property="templateSite" jdbcType="VARCHAR" />
    <result column="template_type" property="templateType" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="variatio_theme" property="variatioTheme" jdbcType="VARCHAR" />
    <result column="enable" property="enable" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_site, template_type, product_type, variatio_theme, `enable`, create_by, 
    create_time, update_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonFormProductVtConfExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_form_product_vt_conf
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_form_product_vt_conf
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_form_product_vt_conf
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonFormProductVtConf" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_form_product_vt_conf (template_site, template_type, product_type, 
      variatio_theme, `enable`, create_by, 
      create_time, update_by, update_time
      )
    values (#{templateSite,jdbcType=VARCHAR}, #{templateType,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, 
      #{variatioTheme,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonFormProductVtConfExample" resultType="java.lang.Integer" >
    select count(*) from amazon_form_product_vt_conf
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_form_product_vt_conf
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateSite != null" >
        template_site = #{record.templateSite,jdbcType=VARCHAR},
      </if>
      <if test="record.templateType != null" >
        template_type = #{record.templateType,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.variatioTheme != null" >
        variatio_theme = #{record.variatioTheme,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null" >
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonFormProductVtConf" >
    update amazon_form_product_vt_conf
    <set >
      <if test="templateSite != null" >
        template_site = #{templateSite,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null" >
        template_type = #{templateType,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="variatioTheme != null" >
        variatio_theme = #{variatioTheme,jdbcType=VARCHAR},
      </if>
      <if test="enable != null" >
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert
    <if test="ignore != null and ignore != ''">${ignore}</if>
    into amazon_form_product_vt_conf (template_site, template_type, product_type,
    variatio_theme, `enable`, create_by,
    create_time, update_by, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.templateSite,jdbcType=VARCHAR}, #{item.templateType,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR},
      #{item.variatioTheme,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}, #{item.createBy,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_form_product_vt_conf
      <set >
        <if test="record.variatioTheme != null" >
          variatio_theme = #{record.variatioTheme,jdbcType=VARCHAR},
        </if>
        <if test="record.updateBy != null" >
          update_by = #{record.updateBy,jdbcType=VARCHAR},
        </if>
        <if test="record.updateTime != null" >
          update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where template_site = #{record.templateSite,jdbcType=VARCHAR}
      and  template_type = #{record.templateType,jdbcType=VARCHAR}
      and product_type = #{record.productType,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>