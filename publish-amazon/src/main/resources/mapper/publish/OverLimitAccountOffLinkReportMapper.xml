<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.OverLimitAccountOffLinkReportMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkReport" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="online_count" property="onlineCount" jdbcType="INTEGER" />
    <result column="offline_count" property="offlineCount" jdbcType="INTEGER" />
    <result column="actual_offline_count" property="actualOfflineCount" jdbcType="INTEGER" />
    <result column="statistics_date" property="statisticsDate" jdbcType="TIMESTAMP"/>
    <result column="task_type" property="taskType" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="p_asin_offline_count" property="pAsinOfflineCount" jdbcType="INTEGER" />
    <result column="p_asin_online_count" property="pAsinOnlineCount" jdbcType="INTEGER" />
    <result column="p_asin_actual_offline_count" property="pAsinActualOfflineCount" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, online_count, offline_count, actual_offline_count, statistics_date, 
    task_type, `status`, created_time, updated_time, p_asin_offline_count, p_asin_online_count, 
    p_asin_actual_offline_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkReportExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from over_limit_account_off_link_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from over_limit_account_off_link_report
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from over_limit_account_off_link_report
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkReport" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into over_limit_account_off_link_report (account_number, online_count, offline_count, 
      actual_offline_count, statistics_date, task_type, 
      `status`, created_time, updated_time, 
      p_asin_offline_count, p_asin_online_count, p_asin_actual_offline_count
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{onlineCount,jdbcType=INTEGER}, #{offlineCount,jdbcType=INTEGER}, 
      #{actualOfflineCount,jdbcType=INTEGER}, #{statisticsDate,jdbcType=TIMESTAMP}, #{taskType,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, 
      #{pAsinOfflineCount,jdbcType=INTEGER}, #{pAsinOnlineCount,jdbcType=INTEGER}, #{pAsinActualOfflineCount,jdbcType=INTEGER}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkReportExample" resultType="java.lang.Integer" >
    select count(*) from over_limit_account_off_link_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update over_limit_account_off_link_report
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineCount != null" >
        online_count = #{record.onlineCount,jdbcType=INTEGER},
      </if>
      <if test="record.offlineCount != null" >
        offline_count = #{record.offlineCount,jdbcType=INTEGER},
      </if>
      <if test="record.actualOfflineCount != null" >
        actual_offline_count = #{record.actualOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="record.statisticsDate != null" >
        statistics_date = #{record.statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskType != null" >
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pAsinOfflineCount != null" >
        p_asin_offline_count = #{record.pAsinOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="record.pAsinOnlineCount != null" >
        p_asin_online_count = #{record.pAsinOnlineCount,jdbcType=INTEGER},
      </if>
      <if test="record.pAsinActualOfflineCount != null" >
        p_asin_actual_offline_count = #{record.pAsinActualOfflineCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.OverLimitAccountOffLinkReport" >
    update over_limit_account_off_link_report
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="onlineCount != null" >
        online_count = #{onlineCount,jdbcType=INTEGER},
      </if>
      <if test="offlineCount != null" >
        offline_count = #{offlineCount,jdbcType=INTEGER},
      </if>
      <if test="actualOfflineCount != null" >
        actual_offline_count = #{actualOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="statisticsDate != null" >
        statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null" >
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pAsinOfflineCount != null" >
        p_asin_offline_count = #{pAsinOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="pAsinOnlineCount != null" >
        p_asin_online_count = #{pAsinOnlineCount,jdbcType=INTEGER},
      </if>
      <if test="pAsinActualOfflineCount != null" >
        p_asin_actual_offline_count = #{pAsinActualOfflineCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>