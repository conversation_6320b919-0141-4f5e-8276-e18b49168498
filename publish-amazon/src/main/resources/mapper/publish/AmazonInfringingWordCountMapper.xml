<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonInfringingWordCountMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonInfringingWordCount" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="word" property="word" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="trademark_identification" property="trademarkIdentification" jdbcType="VARCHAR" />
    <result column="prohibition_platform" property="prohibitionPlatform" jdbcType="VARCHAR" />
    <result column="prohibition_site" property="prohibitionSite" jdbcType="VARCHAR" />
    <result column="listing_count" property="listingCount" jdbcType="INTEGER" />
    <result column="ae_count" property="aeCount" jdbcType="INTEGER" />
    <result column="au_count" property="auCount" jdbcType="INTEGER" />
    <result column="be_count" property="beCount" jdbcType="INTEGER" />
    <result column="br_count" property="brCount" jdbcType="INTEGER" />
    <result column="ca_count" property="caCount" jdbcType="INTEGER" />
    <result column="de_count" property="deCount" jdbcType="INTEGER" />
    <result column="es_count" property="esCount" jdbcType="INTEGER" />
    <result column="fr_count" property="frCount" jdbcType="INTEGER" />
    <result column="in_count" property="inCount" jdbcType="INTEGER" />
    <result column="it_count" property="itCount" jdbcType="INTEGER" />
    <result column="jp_count" property="jpCount" jdbcType="INTEGER" />
    <result column="mx_count" property="mxCount" jdbcType="INTEGER" />
    <result column="nl_count" property="nlCount" jdbcType="INTEGER" />
    <result column="pl_count" property="plCount" jdbcType="INTEGER" />
    <result column="sa_count" property="saCount" jdbcType="INTEGER" />
    <result column="se_count" property="seCount" jdbcType="INTEGER" />
    <result column="sg_count" property="sgCount" jdbcType="INTEGER" />
    <result column="tr_count" property="trCount" jdbcType="INTEGER" />
    <result column="uk_count" property="ukCount" jdbcType="INTEGER" />
    <result column="us_count" property="usCount" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, word, `type`, trademark_identification, prohibition_platform, prohibition_site, 
    listing_count, ae_count, au_count, be_count, br_count, ca_count, de_count, es_count, 
    fr_count, in_count, it_count, jp_count, mx_count, nl_count, pl_count, sa_count, se_count, 
    sg_count, tr_count, uk_count, us_count, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonInfringingWordCountExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_infringing_word_count
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_infringing_word_count
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_infringing_word_count
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonInfringingWordCount" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_infringing_word_count (word, `type`, trademark_identification, 
      prohibition_platform, prohibition_site, listing_count, 
      ae_count, au_count, be_count, 
      br_count, ca_count, de_count, 
      es_count, fr_count, in_count, 
      it_count, jp_count, mx_count, 
      nl_count, pl_count, sa_count, 
      se_count, sg_count, tr_count, 
      uk_count, us_count, create_time)
    values (#{word,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{trademarkIdentification,jdbcType=VARCHAR}, 
      #{prohibitionPlatform,jdbcType=VARCHAR}, #{prohibitionSite,jdbcType=VARCHAR}, #{listingCount,jdbcType=INTEGER}, 
      #{aeCount,jdbcType=INTEGER}, #{auCount,jdbcType=INTEGER}, #{beCount,jdbcType=INTEGER}, 
      #{brCount,jdbcType=INTEGER}, #{caCount,jdbcType=INTEGER}, #{deCount,jdbcType=INTEGER}, 
      #{esCount,jdbcType=INTEGER}, #{frCount,jdbcType=INTEGER}, #{inCount,jdbcType=INTEGER}, 
      #{itCount,jdbcType=INTEGER}, #{jpCount,jdbcType=INTEGER}, #{mxCount,jdbcType=INTEGER}, 
      #{nlCount,jdbcType=INTEGER}, #{plCount,jdbcType=INTEGER}, #{saCount,jdbcType=INTEGER}, 
      #{seCount,jdbcType=INTEGER}, #{sgCount,jdbcType=INTEGER}, #{trCount,jdbcType=INTEGER}, 
      #{ukCount,jdbcType=INTEGER}, #{usCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonInfringingWordCountExample" resultType="java.lang.Integer" >
    select count(*) from amazon_infringing_word_count
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_infringing_word_count
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.word != null" >
        word = #{record.word,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.trademarkIdentification != null" >
        trademark_identification = #{record.trademarkIdentification,jdbcType=VARCHAR},
      </if>
      <if test="record.prohibitionPlatform != null" >
        prohibition_platform = #{record.prohibitionPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.prohibitionSite != null" >
        prohibition_site = #{record.prohibitionSite,jdbcType=VARCHAR},
      </if>
      <if test="record.listingCount != null" >
        listing_count = #{record.listingCount,jdbcType=INTEGER},
      </if>
      <if test="record.aeCount != null" >
        ae_count = #{record.aeCount,jdbcType=INTEGER},
      </if>
      <if test="record.auCount != null" >
        au_count = #{record.auCount,jdbcType=INTEGER},
      </if>
      <if test="record.beCount != null" >
        be_count = #{record.beCount,jdbcType=INTEGER},
      </if>
      <if test="record.brCount != null" >
        br_count = #{record.brCount,jdbcType=INTEGER},
      </if>
      <if test="record.caCount != null" >
        ca_count = #{record.caCount,jdbcType=INTEGER},
      </if>
      <if test="record.deCount != null" >
        de_count = #{record.deCount,jdbcType=INTEGER},
      </if>
      <if test="record.esCount != null" >
        es_count = #{record.esCount,jdbcType=INTEGER},
      </if>
      <if test="record.frCount != null" >
        fr_count = #{record.frCount,jdbcType=INTEGER},
      </if>
      <if test="record.inCount != null" >
        in_count = #{record.inCount,jdbcType=INTEGER},
      </if>
      <if test="record.itCount != null" >
        it_count = #{record.itCount,jdbcType=INTEGER},
      </if>
      <if test="record.jpCount != null" >
        jp_count = #{record.jpCount,jdbcType=INTEGER},
      </if>
      <if test="record.mxCount != null" >
        mx_count = #{record.mxCount,jdbcType=INTEGER},
      </if>
      <if test="record.nlCount != null" >
        nl_count = #{record.nlCount,jdbcType=INTEGER},
      </if>
      <if test="record.plCount != null" >
        pl_count = #{record.plCount,jdbcType=INTEGER},
      </if>
      <if test="record.saCount != null" >
        sa_count = #{record.saCount,jdbcType=INTEGER},
      </if>
      <if test="record.seCount != null" >
        se_count = #{record.seCount,jdbcType=INTEGER},
      </if>
      <if test="record.sgCount != null" >
        sg_count = #{record.sgCount,jdbcType=INTEGER},
      </if>
      <if test="record.trCount != null" >
        tr_count = #{record.trCount,jdbcType=INTEGER},
      </if>
      <if test="record.ukCount != null" >
        uk_count = #{record.ukCount,jdbcType=INTEGER},
      </if>
      <if test="record.usCount != null" >
        us_count = #{record.usCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonInfringingWordCount" >
    update amazon_infringing_word_count
    <set >
      <if test="word != null" >
        word = #{word,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="trademarkIdentification != null" >
        trademark_identification = #{trademarkIdentification,jdbcType=VARCHAR},
      </if>
      <if test="prohibitionPlatform != null" >
        prohibition_platform = #{prohibitionPlatform,jdbcType=VARCHAR},
      </if>
      <if test="prohibitionSite != null" >
        prohibition_site = #{prohibitionSite,jdbcType=VARCHAR},
      </if>
      <if test="listingCount != null" >
        listing_count = #{listingCount,jdbcType=INTEGER},
      </if>
      <if test="aeCount != null" >
        ae_count = #{aeCount,jdbcType=INTEGER},
      </if>
      <if test="auCount != null" >
        au_count = #{auCount,jdbcType=INTEGER},
      </if>
      <if test="beCount != null" >
        be_count = #{beCount,jdbcType=INTEGER},
      </if>
      <if test="brCount != null" >
        br_count = #{brCount,jdbcType=INTEGER},
      </if>
      <if test="caCount != null" >
        ca_count = #{caCount,jdbcType=INTEGER},
      </if>
      <if test="deCount != null" >
        de_count = #{deCount,jdbcType=INTEGER},
      </if>
      <if test="esCount != null" >
        es_count = #{esCount,jdbcType=INTEGER},
      </if>
      <if test="frCount != null" >
        fr_count = #{frCount,jdbcType=INTEGER},
      </if>
      <if test="inCount != null" >
        in_count = #{inCount,jdbcType=INTEGER},
      </if>
      <if test="itCount != null" >
        it_count = #{itCount,jdbcType=INTEGER},
      </if>
      <if test="jpCount != null" >
        jp_count = #{jpCount,jdbcType=INTEGER},
      </if>
      <if test="mxCount != null" >
        mx_count = #{mxCount,jdbcType=INTEGER},
      </if>
      <if test="nlCount != null" >
        nl_count = #{nlCount,jdbcType=INTEGER},
      </if>
      <if test="plCount != null" >
        pl_count = #{plCount,jdbcType=INTEGER},
      </if>
      <if test="saCount != null" >
        sa_count = #{saCount,jdbcType=INTEGER},
      </if>
      <if test="seCount != null" >
        se_count = #{seCount,jdbcType=INTEGER},
      </if>
      <if test="sgCount != null" >
        sg_count = #{sgCount,jdbcType=INTEGER},
      </if>
      <if test="trCount != null" >
        tr_count = #{trCount,jdbcType=INTEGER},
      </if>
      <if test="ukCount != null" >
        uk_count = #{ukCount,jdbcType=INTEGER},
      </if>
      <if test="usCount != null" >
        us_count = #{usCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>