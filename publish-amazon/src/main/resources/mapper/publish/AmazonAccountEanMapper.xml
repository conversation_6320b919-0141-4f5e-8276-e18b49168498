<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonAccountEanMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonAccountEan" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="seller_id" property="sellerId" jdbcType="VARCHAR" />
    <result column="ean_prefix" property="eanPrefix" jdbcType="VARCHAR" />
    <result column="last_prefixStr" property="lastPrefixStr" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, seller_id, ean_prefix, last_prefixStr, create_date, update_date, version
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEanExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_account_ean
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_account_ean
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from amazon_account_ean
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEanExample" >
    delete from amazon_account_ean
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEan" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_account_ean (seller_id, ean_prefix, last_prefixStr,
      create_date, update_date, version)
    values (#{sellerId,jdbcType=VARCHAR}, #{eanPrefix,jdbcType=VARCHAR}, #{lastPrefixStr,jdbcType=VARCHAR},
      #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEan" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_account_ean
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sellerId != null" >
        seller_id,
      </if>
      <if test="eanPrefix != null" >
        ean_prefix,
      </if>
      <if test="lastPrefixStr != null" >
        last_prefixStr,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="version != null" >
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sellerId != null" >
        #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="eanPrefix != null" >
        #{eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="lastPrefixStr != null" >
        #{lastPrefixStr,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEanExample" resultType="java.lang.Integer" >
    select count(*) from amazon_account_ean
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_account_ean
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.eanPrefix != null" >
        ean_prefix = #{record.eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="record.lastPrefixStr != null" >
        last_prefixStr = #{record.lastPrefixStr,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update amazon_account_ean
    set id = #{record.id,jdbcType=INTEGER},
      seller_id = #{record.sellerId,jdbcType=VARCHAR},
      ean_prefix = #{record.eanPrefix,jdbcType=VARCHAR},
      last_prefixStr = #{record.lastPrefixStr,jdbcType=VARCHAR},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEan" >
    update amazon_account_ean
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="eanPrefix != null" >
        ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="lastPrefixStr != null" >
        last_prefixStr = #{lastPrefixStr,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountEan" >
    update amazon_account_ean
    set seller_id = #{sellerId,jdbcType=VARCHAR},
      ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      last_prefixStr = #{lastPrefixStr,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>