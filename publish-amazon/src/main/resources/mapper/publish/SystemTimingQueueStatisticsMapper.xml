<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.SystemTimingQueueStatisticsMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.SystemTimingQueueStatistics" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="day" property="day" jdbcType="DATE" />
    <result column="publish_quantity" property="publishQuantity" jdbcType="INTEGER" />
    <result column="publish_interval_time" property="publishIntervalTime" jdbcType="INTEGER" />
    <result column="min_publish_mount" property="minPublishMount" jdbcType="INTEGER" />
    <result column="timing_queue_count" property="timingQueueCount" jdbcType="INTEGER" />
    <result column="publish_count" property="publishCount" jdbcType="INTEGER" />
    <result column="publish_success_count" property="publishSuccessCount" jdbcType="INTEGER" />
    <result column="publish_fail_count" property="publishFailCount" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, `day`, publish_quantity, publish_interval_time, min_publish_mount, 
    timing_queue_count, publish_count, publish_success_count, publish_fail_count, create_by, 
    create_time, update_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.SystemTimingQueueStatisticsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_timing_queue_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from system_timing_queue_statistics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from system_timing_queue_statistics
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.SystemTimingQueueStatistics" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into system_timing_queue_statistics (account_number, site, `day`, 
      publish_quantity, publish_interval_time, min_publish_mount, 
      timing_queue_count, publish_count, publish_success_count, 
      publish_fail_count, create_by, create_time, 
      update_by, update_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{day,jdbcType=DATE}, 
      #{publishQuantity,jdbcType=INTEGER}, #{publishIntervalTime,jdbcType=INTEGER}, #{minPublishMount,jdbcType=INTEGER}, 
      #{timingQueueCount,jdbcType=INTEGER}, #{publishCount,jdbcType=INTEGER}, #{publishSuccessCount,jdbcType=INTEGER}, 
      #{publishFailCount,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.SystemTimingQueueStatisticsExample" resultType="java.lang.Integer" >
    select count(*) from system_timing_queue_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update system_timing_queue_statistics
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.day != null" >
        `day` = #{record.day,jdbcType=DATE},
      </if>
      <if test="record.publishQuantity != null" >
        publish_quantity = #{record.publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.publishIntervalTime != null" >
        publish_interval_time = #{record.publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="record.minPublishMount != null" >
        min_publish_mount = #{record.minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="record.timingQueueCount != null" >
        timing_queue_count = #{record.timingQueueCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishCount != null" >
        publish_count = #{record.publishCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishSuccessCount != null" >
        publish_success_count = #{record.publishSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishFailCount != null" >
        publish_fail_count = #{record.publishFailCount,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.SystemTimingQueueStatistics" >
    update system_timing_queue_statistics
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="day != null" >
        `day` = #{day,jdbcType=DATE},
      </if>
      <if test="publishQuantity != null" >
        publish_quantity = #{publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="publishIntervalTime != null" >
        publish_interval_time = #{publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="minPublishMount != null" >
        min_publish_mount = #{minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="timingQueueCount != null" >
        timing_queue_count = #{timingQueueCount,jdbcType=INTEGER},
      </if>
      <if test="publishCount != null" >
        publish_count = #{publishCount,jdbcType=INTEGER},
      </if>
      <if test="publishSuccessCount != null" >
        publish_success_count = #{publishSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="publishFailCount != null" >
        publish_fail_count = #{publishFailCount,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteByExample">
    delete from system_timing_queue_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="_parameter == null" >
      where 1 != 1
    </if>
  </delete>
</mapper>