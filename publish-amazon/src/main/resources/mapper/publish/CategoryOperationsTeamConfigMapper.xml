<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.CategoryOperationsTeamConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sale_id" property="saleId" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="INTEGER" />
    <result column="is_leaf" property="isLeaf" jdbcType="BIT" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="show_table" property="showTable" jdbcType="BIT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="category_full_path_code" property="categoryFullPathCode" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sale_id, `level`, is_leaf, `status`, show_table, category_id, category_code,
    category_name, category_full_path_code, created_time, updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from category_operations_team_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from category_operations_team_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from category_operations_team_config
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into category_operations_team_config (sale_id, `level`, is_leaf,
    `status`, show_table, category_id,
    category_code, category_name, category_full_path_code,
    created_time, updated_time)
    values (#{saleId,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER}, #{isLeaf,jdbcType=BIT},
    #{status,jdbcType=BIT}, #{showTable,jdbcType=BIT}, #{categoryId,jdbcType=INTEGER},
    #{categoryCode,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, #{categoryFullPathCode,jdbcType=VARCHAR},
    #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfigExample" resultType="java.lang.Integer" >
    select count(*) from category_operations_team_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update category_operations_team_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.saleId != null" >
        sale_id = #{record.saleId,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null" >
        `level` = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.isLeaf != null" >
        is_leaf = #{record.isLeaf,jdbcType=BIT},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.showTable != null" >
        show_table = #{record.showTable,jdbcType=BIT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryCode != null" >
        category_code = #{record.categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null" >
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryFullPathCode != null" >
        category_full_path_code = #{record.categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfig" >
    update category_operations_team_config
    <set >
      <if test="saleId != null" >
        sale_id = #{saleId,jdbcType=VARCHAR},
      </if>
      <if test="level != null" >
        `level` = #{level,jdbcType=INTEGER},
      </if>
      <if test="isLeaf != null" >
        is_leaf = #{isLeaf,jdbcType=BIT},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="showTable != null" >
        show_table = #{showTable,jdbcType=BIT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryCode != null" >
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null" >
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryFullPathCode != null" >
        category_full_path_code = #{categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="batchUpdateByPrimaryKeySelective" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      update category_operations_team_config
      <set>
        <if test="record.saleId != null">
          sale_id = #{record.saleId,jdbcType=VARCHAR},
        </if>
        <if test="record.level != null">
          `level` = #{record.level,jdbcType=INTEGER},
        </if>
        <if test="record.isLeaf != null">
          is_leaf = #{record.isLeaf,jdbcType=BIT},
        </if>
        <if test="record.status != null">
          `status` = #{record.status,jdbcType=BIT},
        </if>
        <if test="record.showTable != null">
          show_table = #{record.showTable,jdbcType=BIT},
        </if>
        <if test="record.categoryId != null">
          category_id = #{record.categoryId,jdbcType=INTEGER},
        </if>
        <if test="record.categoryCode != null">
          category_code = #{record.categoryCode,jdbcType=VARCHAR},
        </if>
        <if test="record.categoryName != null">
          category_name = #{record.categoryName,jdbcType=VARCHAR},
        </if>
        <if test="record.categoryFullPathCode != null">
          category_full_path_code = #{record.categoryFullPathCode,jdbcType=VARCHAR},
        </if>
        <if test="record.createdTime != null">
          created_time = #{record.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updatedTime != null">
          updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into category_operations_team_config (sale_id, `level`, is_leaf,
    `status`, show_table, category_id,category_code,
    category_name, category_full_path_code, created_time,
    updated_time
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.saleId,jdbcType=VARCHAR}, #{item.level,jdbcType=INTEGER}, #{item.isLeaf,jdbcType=BIT},
      #{item.status,jdbcType=BIT}, #{item.showTable,jdbcType=BIT},#{item.categoryId,jdbcType=INTEGER}, #{item.categoryCode,jdbcType=VARCHAR},
      #{item.categoryName,jdbcType=VARCHAR}, #{item.categoryFullPathCode,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP},
      #{item.updatedTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="getAllLeve2Category" resultMap="BaseResultMap">
    select `level`, category_code, category_name from category_operations_team_config where level = 2 group by category_code
  </select>
  <select id="selectDistinctSaleId" resultType="java.lang.String">
    select distinct sale_id from category_operations_team_config where status = 1;
  </select>
</mapper>