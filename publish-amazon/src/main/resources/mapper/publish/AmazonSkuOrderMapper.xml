<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonSkuOrderMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonSkuOrder" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="system_sku" property="systemSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="payment_time" property="paymentTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, seller_sku, system_sku, article_number, order_no, price, payment_time, 
    create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_sku_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_sku_order
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" >
    delete from amazon_sku_order
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByExample" >
    delete from amazon_sku_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrder" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_sku_order (account_number, seller_sku, system_sku, 
      article_number, order_no, price, 
      payment_time, create_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{systemSku,jdbcType=VARCHAR}, 
      #{articleNumber,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{price,jdbcType=DOUBLE}, 
      #{paymentTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert">
     insert into amazon_sku_order (account_number, seller_sku, system_sku,
      article_number, order_no, price,
      payment_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.systemSku,jdbcType=VARCHAR},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.price,jdbcType=DOUBLE},
      #{item.paymentTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderExample" resultType="java.lang.Integer" >
    select count(*) from amazon_sku_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_sku_order
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.systemSku != null" >
        system_sku = #{record.systemSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.paymentTime != null" >
        payment_time = #{record.paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrder" >
    update amazon_sku_order
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="systemSku != null" >
        system_sku = #{systemSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="paymentTime != null" >
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>