<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonPublishImagePathMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonPublishImagePath">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    <id column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    account_number, image_path
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_publish_image_path
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from amazon_publish_image_path
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    delete from amazon_publish_image_path
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    delete from amazon_publish_image_path
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePath">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    insert ignore  into amazon_publish_image_path (account_number, image_path)
    values (#{accountNumber,jdbcType=VARCHAR}, #{imagePath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePath">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    insert into amazon_publish_image_path
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="imagePath != null">
        image_path,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null">
        #{imagePath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePathExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    select count(*) from amazon_publish_image_path
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    update amazon_publish_image_path
    <set>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.imagePath != null">
        image_path = #{record.imagePath,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    update amazon_publish_image_path
    set account_number = #{record.accountNumber,jdbcType=VARCHAR},
      image_path = #{record.imagePath,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePath">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    update amazon_publish_image_path
    <set>
      <if test="imagePath != null">
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
    </set>
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishImagePath">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:21 CST 2019.
    -->
    update amazon_publish_image_path
    set image_path = #{imagePath,jdbcType=VARCHAR}
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </update>
</mapper>