<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonCalcPriceRuleMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonCalcPriceRule" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="label" property="label" jdbcType="VARCHAR" />
    <result column="from_price" property="fromPrice" jdbcType="DOUBLE" />
    <result column="to_price" property="toPrice" jdbcType="DOUBLE" />
    <result column="calc_logistics" property="calcLogistics" jdbcType="VARCHAR" />
    <result column="prefer_logistics" property="preferLogistics" jdbcType="VARCHAR" />
    <result column="alternate_logistics" property="alternateLogistics" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, `type`, account_number, site, `label`, from_price, to_price, calc_logistics, 
    prefer_logistics, alternate_logistics, create_by, create_date, last_update_by, last_update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRuleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_calc_price_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_calc_price_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_calc_price_rule
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

    <!-- 根据账号删除 -->
    <delete id="deleteByAccountNumber">
      delete from amazon_calc_price_rule
    where account_number = #{item}
    </delete>

    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRule" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_calc_price_rule (`type`, account_number, site,
      `label`, from_price, to_price, 
      calc_logistics, prefer_logistics, alternate_logistics, 
      create_by, create_date, last_update_by, 
      last_update_date)
    values (#{type,jdbcType=INTEGER}, #{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, 
      #{label,jdbcType=VARCHAR}, #{fromPrice,jdbcType=DOUBLE}, #{toPrice,jdbcType=DOUBLE}, 
      #{calcLogistics,jdbcType=VARCHAR}, #{preferLogistics,jdbcType=VARCHAR}, #{alternateLogistics,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateBy,jdbcType=VARCHAR}, 
      #{lastUpdateDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRuleExample" resultType="java.lang.Integer" >
    select count(*) from amazon_calc_price_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <!-- 查询去重后的试算物流方式 -->
  <select id="selectDistinctCalcLogisticsByExample" resultType="java.lang.String">
    select distinct calc_logistics
    from amazon_calc_price_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_calc_price_rule
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null" >
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.fromPrice != null" >
        from_price = #{record.fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.toPrice != null" >
        to_price = #{record.toPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.calcLogistics != null" >
        calc_logistics = #{record.calcLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.preferLogistics != null" >
        prefer_logistics = #{record.preferLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.alternateLogistics != null" >
        alternate_logistics = #{record.alternateLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRule" >
    update amazon_calc_price_rule
    <set >
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="label != null" >
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="fromPrice != null" >
        from_price = #{fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="toPrice != null" >
        to_price = #{toPrice,jdbcType=DOUBLE},
      </if>
      <if test="calcLogistics != null" >
        calc_logistics = #{calcLogistics,jdbcType=VARCHAR},
      </if>
      <if test="preferLogistics != null" >
        prefer_logistics = #{preferLogistics,jdbcType=VARCHAR},
      </if>
      <if test="alternateLogistics != null" >
        alternate_logistics = #{alternateLogistics,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 更新 -->
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRule">
    update amazon_calc_price_rule set
    `type` = #{type,jdbcType=INTEGER},
    account_number = #{accountNumber,jdbcType=VARCHAR},
    site = #{site,jdbcType=VARCHAR},
    label = #{label,jdbcType=VARCHAR},
    from_price = #{fromPrice,jdbcType=DOUBLE},
    to_price = #{toPrice,jdbcType=DOUBLE},
    calc_logistics = #{calcLogistics,jdbcType=VARCHAR},
    prefer_logistics = #{preferLogistics,jdbcType=VARCHAR},
    alternate_logistics = #{alternateLogistics,jdbcType=VARCHAR},
    create_by = #{createBy,jdbcType=VARCHAR},
    create_date = #{createDate,jdbcType=TIMESTAMP},
    last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
    last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 根据账号查询 -->
  <select id="selectByAccountNumber" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from amazon_calc_price_rule
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </select>

  <!-- 查询存在禁用物流方式的数据 -->
  <select id="selectExistDisableShippingMethod" resultMap="BaseResultMap">
    SELECT * FROM amazon_calc_price_rule
    WHERE calc_logistics IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
    OR prefer_logistics IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
    OR alternate_logistics IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </select>

  <!-- 查询必要字段 -->
  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap">
    select ${filedColumns}
    from amazon_calc_price_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <!-- 清空存在禁用物流的物流方式 -->
  <update id="clearExistDisableShippingMethod" parameterType="com.estone.erp.publish.amazon.model.AmazonCalcPriceRule">
    update amazon_calc_price_rule
    <set >
      <if test="calcLogistics == null" >
        calc_logistics = #{calcLogistics,jdbcType=VARCHAR},
      </if>
      <if test="preferLogistics == null" >
        prefer_logistics = #{preferLogistics,jdbcType=VARCHAR},
      </if>
      <if test="alternateLogistics == null" >
        alternate_logistics = #{alternateLogistics,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>