<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonNewRemindMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonNewRemind" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="sale_leader_man" property="saleLeaderMan" jdbcType="VARCHAR" />
    <result column="first_image" property="firstImage" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="category" property="category" jdbcType="VARCHAR" />
    <result column="edit_finish_time" property="editFinishTime" jdbcType="TIMESTAMP" />
    <result column="create_at" property="createAt" jdbcType="TIMESTAMP" />
    <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
    <result column="is_success_temp" property="isSuccessTemp" jdbcType="BIT" />
    <result column="temp_finish_time" property="tempFinishTime" jdbcType="TIMESTAMP" />
    <result column="remarks" property="remarks" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="is_site_publish" property="isSitePublish" jdbcType="BIT" />
    <result column="step_template_status" property="stepTemplateStatus" jdbcType="BIT" />
    <result column="account_country" property="accountCountry" jdbcType="VARCHAR" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER"/>
    <result column="editor" property="editor" jdbcType="VARCHAR"/>
    <result column="publish_role" property="publishRole" jdbcType="INTEGER"/>
    <result column="data_source_type" property="dataSourceType" jdbcType="INTEGER"/>
    <result column="reassign_time" property="reassignTime" jdbcType="TIMESTAMP" />
    <result column="reassign_by" property="reassignBy" jdbcType="VARCHAR" />
    <result column="reassign_status" property="reassignStatus" jdbcType="INTEGER"/>
    <result column="reassign_fail_msg" property="reassignFailMsg" jdbcType="VARCHAR"/>
    <result column="reassign_retry_count" property="reassignRetryCount" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, spu, account, sale_man, sale_leader_man, first_image, title, category, edit_finish_time, 
    create_at, push_time, is_success_temp, temp_finish_time, remarks, create_by, create_date, 
    last_update_by, last_update_date, template_id, is_site_publish, step_template_status,
    account_country, publish_status, editor, publish_role, data_source_type, reassign_time,
    reassign_by, reassign_status, reassign_fail_msg, reassign_retry_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_new_remind
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_new_remind
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemind" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_new_remind (spu, account, sale_man, 
      sale_leader_man, first_image, title, 
      category, edit_finish_time, create_at, 
      push_time, is_success_temp, temp_finish_time, 
      remarks, create_by, create_date, 
      last_update_by, last_update_date,
      template_id, is_site_publish,
      step_template_status, account_country,
      publish_status, editor, publish_role,
      data_source_type, reassign_time, reassign_by,
      reassign_status, reassign_fail_msg, reassign_retry_count)
    values (#{spu,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{saleMan,jdbcType=VARCHAR}, 
      #{saleLeaderMan,jdbcType=VARCHAR}, #{firstImage,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{editFinishTime,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP}, 
      #{pushTime,jdbcType=TIMESTAMP}, #{isSuccessTemp,jdbcType=BIT}, #{tempFinishTime,jdbcType=TIMESTAMP}, 
      #{remarks,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{lastUpdateBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{templateId,jdbcType=INTEGER},
      #{isSitePublish,jdbcType=BIT}, #{stepTemplateStatus,jdbcType=BIT}, #{accountCountry,jdbcType=VARCHAR},
      #{publishStatus,jdbcType=INTEGER}, #{editor,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER},
      #{dataSourceType,jdbcType=INTEGER}, #{reassignTime,jdbcType=TIMESTAMP}, #{reassignBy,jdbcType=VARCHAR},
      #{reassignStatus,jdbcType=INTEGER}, #{reassignFailMsg,jdbcType=VARCHAR}, #{reassignRetryCount,jdbcType=INTEGER})
  </insert>

  <!-- 批量插入 -->
  <insert id="batchInsert">
    insert into amazon_new_remind (spu, account, sale_man,
    sale_leader_man, first_image, title,
    category, edit_finish_time, create_at,
    push_time, is_success_temp, temp_finish_time,
    remarks, create_by, create_date,
    last_update_by, last_update_date,
    template_id, is_site_publish,
    step_template_status, account_country,
    publish_status, editor, publish_role,
    data_source_type, reassign_time, reassign_by,
    reassign_status, reassign_fail_msg, reassign_retry_count)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.spu,jdbcType=VARCHAR}, #{item.account,jdbcType=VARCHAR}, #{item.saleMan,jdbcType=VARCHAR},
      #{item.saleLeaderMan,jdbcType=VARCHAR}, #{item.firstImage,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR},
      #{item.category,jdbcType=VARCHAR}, #{item.editFinishTime,jdbcType=TIMESTAMP}, #{item.createAt,jdbcType=TIMESTAMP},
      #{item.pushTime,jdbcType=TIMESTAMP}, #{item.isSuccessTemp,jdbcType=BIT}, #{item.tempFinishTime,jdbcType=TIMESTAMP},
      #{item.remarks,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
      #{item.lastUpdateBy,jdbcType=VARCHAR}, #{item.lastUpdateDate,jdbcType=TIMESTAMP}, #{item.templateId,jdbcType=INTEGER},
      #{item.isSitePublish,jdbcType=BIT}, #{item.stepTemplateStatus,jdbcType=BIT}, #{item.accountCountry,jdbcType=VARCHAR},
      #{item.publishStatus,jdbcType=INTEGER}, #{item.editor,jdbcType=VARCHAR}, #{item.publishRole,jdbcType=INTEGER},
      #{item.dataSourceType,jdbcType=INTEGER}, #{item.reassignTime,jdbcType=TIMESTAMP}, #{item.reassignBy,jdbcType=VARCHAR},
      #{item.reassignStatus,jdbcType=INTEGER}, #{item.reassignFailMsg,jdbcType=VARCHAR}, #{item.reassignRetryCount,jdbcType=INTEGER})
    </foreach>
  </insert>

    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindExample" resultType="java.lang.Integer" >
    select count(*) from amazon_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <!-- 查询去重后的spu -->
  <select id="selectSpuByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindExample" resultType="java.lang.String">
    select distinct spu from amazon_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <!-- 查询必要字段 -->
  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindExample" >
    select ${filedColumns}
    from amazon_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_new_remind
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.saleMan != null" >
        sale_man = #{record.saleMan,jdbcType=VARCHAR},
      </if>
      <if test="record.saleLeaderMan != null" >
        sale_leader_man = #{record.saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="record.firstImage != null" >
        first_image = #{record.firstImage,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.editFinishTime != null" >
        edit_finish_time = #{record.editFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null" >
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pushTime != null" >
        push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isSuccessTemp != null" >
        is_success_temp = #{record.isSuccessTemp,jdbcType=BIT},
      </if>
      <if test="record.tempFinishTime != null" >
        temp_finish_time = #{record.tempFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remarks != null" >
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.isSitePublish != null" >
        is_site_publish = #{record.isSitePublish,jdbcType=BIT},
      </if>
      <if test="record.stepTemplateStatus != null" >
        step_template_status = #{record.stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="record.accountCountry != null" >
        account_country = #{record.accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.publishStatus != null" >
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.editor != null" >
        editor = #{record.editor,jdbcType=VARCHAR},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.dataSourceType != null" >
        data_source_type = #{record.dataSourceType,jdbcType=INTEGER},
      </if>
      <if test="record.reassignTime != null" >
        reassign_time = #{record.reassignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reassignBy != null" >
        reassign_by = #{record.reassignBy,jdbcType=VARCHAR},
      </if>
      <if test="record.reassignStatus != null" >
        reassign_status = #{record.reassignStatus,jdbcType=INTEGER},
      </if>
      <if test="record.reassignFailMsg != null" >
        reassign_fail_msg = #{record.reassignFailMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.reassignRetryCount != null" >
        reassign_retry_count = #{record.reassignRetryCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemind" >
    update amazon_new_remind
    <set >
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="saleMan != null" >
        sale_man = #{saleMan,jdbcType=VARCHAR},
      </if>
      <if test="saleLeaderMan != null" >
        sale_leader_man = #{saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="firstImage != null" >
        first_image = #{firstImage,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="editFinishTime != null" >
        edit_finish_time = #{editFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null" >
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="pushTime != null" >
        push_time = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSuccessTemp != null" >
        is_success_temp = #{isSuccessTemp,jdbcType=BIT},
      </if>
      <if test="tempFinishTime != null" >
        temp_finish_time = #{tempFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null" >
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="isSitePublish != null" >
        is_site_publish = #{isSitePublish,jdbcType=BIT},
      </if>
      <if test="stepTemplateStatus != null" >
        step_template_status = #{stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="accountCountry != null" >
        account_country = #{accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null" >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="editor != null" >
        editor = #{editor,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="dataSourceType != null" >
        data_source_type = #{dataSourceType,jdbcType=INTEGER},
      </if>
      <if test="reassignTime != null" >
        reassign_time = #{reassignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassignBy != null" >
        reassign_by = #{reassignBy,jdbcType=VARCHAR},
      </if>
      <if test="reassignStatus != null" >
        reassign_status = #{reassignStatus,jdbcType=INTEGER},
      </if>
      <if test="reassignFailMsg != null" >
        reassign_fail_msg = #{reassignFailMsg,jdbcType=VARCHAR},
      </if>
      <if test="reassignRetryCount != null" >
        reassign_retry_count = #{reassignRetryCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 清空错误历史数据 -->
  <update id="updateErrorDateById" parameterType="java.lang.Integer" >
    update amazon_new_remind
    <set >
--         template_id = null,
--
--         is_site_publish = null,
--
--         step_template_status = null,

      is_success_temp = 0,

      remarks = NULL,

      temp_finish_time = NULL,
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 批量标记备注已完成 -->
  <update id="batchUpdateRemark" parameterType="java.util.List">
      UPDATE  amazon_new_remind SET
      is_success_temp = TRUE,
      publish_role = 0,
      temp_finish_time = now(),
      remarks = '主管标记已完成'
      where id IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>