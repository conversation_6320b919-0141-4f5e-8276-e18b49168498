<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.ProductTypeTemplateJsonAttrMapper" >
  <!-- 在resultMap中添加 -->
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="CHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="attribute_name" property="attributeName" jdbcType="VARCHAR" />
    <result column="attribute_value" property="attributeValue" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="adapter_type" property="adapterType" jdbcType="INTEGER"/>
    <result column="extra_data" property="extraData" jdbcType="VARCHAR"/>
    <result column="applicable_attribute_type" property="applicableAttributeType" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="code_adapter" property="codeAdapter" jdbcType="BIT"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id,
    site,
    product_type,
    attribute_name,
    attribute_value,
    `type`,
    applicable_attribute_type,
    adapter_type,
    code_adapter,
    extra_data,
    create_by, created_time, updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttrExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_type_template_json_attr
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="groupByClause != null" >
      group by ${groupByClause}
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByExampleCustomList" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttrExample">
    select
    ${customList}
    from product_type_template_json_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="groupByClause != null">
      group by ${groupByClause}
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from product_type_template_json_attr
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from product_type_template_json_attr
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr">
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_type_template_json_attr (site, product_type, attribute_name,
    attribute_value, `type`, applicable_attribute_type, adapter_type, code_adapter, extra_data,
      create_by, created_time, updated_time
      )
    values (#{site,jdbcType=CHAR}, #{productType,jdbcType=VARCHAR}, #{attributeName,jdbcType=VARCHAR},
    #{attributeValue,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{applicableAttributeType,jdbcType=INTEGER},
    #{adapterType,jdbcType=INTEGER}, #{codeAdapter,jdbcType=VARCHAR},
    #{extraData,jdbcType=VARCHAR},#{createBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP},
    #{updatedTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttrExample" resultType="java.lang.Integer" >
    select count(*) from product_type_template_json_attr
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update product_type_template_json_attr
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=CHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeName != null" >
        attribute_name = #{record.attributeName,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeValue != null" >
        attribute_value = #{record.attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.applicableAttributeType != null" >
        applicable_attribute_type = #{record.applicableAttributeType,jdbcType=INTEGER},
      </if>
      <if test="record.adapterType != null">
        adapter_type = #{record.adapterType,jdbcType=INTEGER},
      </if>
      <if test="record.extraData != null">
        extra_data = #{record.extraData,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.codeAdapter != null">
        code_adapter = #{record.codeAdapter,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <!-- 在updateByPrimaryKeySelective中添加 -->
  <update id="updateByPrimaryKeySelective"
          parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr">
    update product_type_template_json_attr
    <set >
      <if test="site != null and site != ''">
        site = #{site,jdbcType=CHAR},
      </if>
      <if test="site == ''">
        site = null,
      </if>
      <if test="productType != null and productType != ''">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="productType == ''">
        product_type = null,
      </if>
      <if test="attributeName != null" >
        attribute_name = #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null" >
        attribute_value = #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="applicableAttributeType != null" >
        applicable_attribute_type = #{applicableAttributeType,jdbcType=INTEGER},
      </if>
      <if test="adapterType != null">
        adapter_type = #{adapterType,jdbcType=INTEGER},
      </if>
      <if test="extraData != null">
        extra_data = #{extraData,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="codeAdapter != null">
        code_adapter = #{codeAdapter,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
