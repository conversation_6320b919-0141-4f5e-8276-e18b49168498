<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.ListingBrandCheckLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.ListingBrandCheckLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="account_config_brand" property="accountConfigBrand" jdbcType="VARCHAR" />
    <result column="account_history_brand" property="accountHistoryBrand" jdbcType="VARCHAR" />
    <result column="order_num_total" property="orderNumTotal" jdbcType="INTEGER" />
    <result column="order_24_hour_sale" property="order24HourSale" jdbcType="INTEGER" />
    <result column="order_7_sale" property="order7Sale" jdbcType="INTEGER" />
    <result column="order_30_sale" property="order30Sale" jdbcType="INTEGER" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="store_group_leader" property="storeGroupLeader" jdbcType="VARCHAR" />
    <result column="sale_group_leader" property="saleGroupLeader" jdbcType="VARCHAR" />
    <result column="manager_leader" property="managerLeader" jdbcType="VARCHAR" />
    <result column="sale_supervisor" property="saleSupervisor" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, site, account_number, seller_sku, parent_asin, son_asin, sku, brand, account_config_brand, 
    account_history_brand, order_num_total, order_24_hour_sale, order_7_sale, order_30_sale, 
    sale_man, store_group_leader, sale_group_leader, manager_leader, sale_supervisor, 
    create_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.ListingBrandCheckLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from listing_brand_check_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from listing_brand_check_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from listing_brand_check_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.ListingBrandCheckLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="BEFORE" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into listing_brand_check_log (id, site, account_number, 
      seller_sku, parent_asin, son_asin, 
      sku, brand, account_config_brand, 
      account_history_brand, order_num_total, order_24_hour_sale, 
      order_7_sale, order_30_sale, sale_man, 
      store_group_leader, sale_group_leader, manager_leader, 
      sale_supervisor, create_date)
    values (#{id,jdbcType=BIGINT}, #{site,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, 
      #{sellerSku,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, #{sonAsin,jdbcType=VARCHAR}, 
      #{sku,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{accountConfigBrand,jdbcType=VARCHAR}, 
      #{accountHistoryBrand,jdbcType=VARCHAR}, #{orderNumTotal,jdbcType=INTEGER}, #{order24HourSale,jdbcType=INTEGER}, 
      #{order7Sale,jdbcType=INTEGER}, #{order30Sale,jdbcType=INTEGER}, #{saleMan,jdbcType=VARCHAR}, 
      #{storeGroupLeader,jdbcType=VARCHAR}, #{saleGroupLeader,jdbcType=VARCHAR}, #{managerLeader,jdbcType=VARCHAR}, 
      #{saleSupervisor,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.ListingBrandCheckLogExample" resultType="java.lang.Integer" >
    select count(*) from listing_brand_check_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update listing_brand_check_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.accountConfigBrand != null" >
        account_config_brand = #{record.accountConfigBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.accountHistoryBrand != null" >
        account_history_brand = #{record.accountHistoryBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNumTotal != null" >
        order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="record.order24HourSale != null" >
        order_24_hour_sale = #{record.order24HourSale,jdbcType=INTEGER},
      </if>
      <if test="record.order7Sale != null" >
        order_7_sale = #{record.order7Sale,jdbcType=INTEGER},
      </if>
      <if test="record.order30Sale != null" >
        order_30_sale = #{record.order30Sale,jdbcType=INTEGER},
      </if>
      <if test="record.saleMan != null" >
        sale_man = #{record.saleMan,jdbcType=VARCHAR},
      </if>
      <if test="record.storeGroupLeader != null" >
        store_group_leader = #{record.storeGroupLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.saleGroupLeader != null" >
        sale_group_leader = #{record.saleGroupLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.managerLeader != null" >
        manager_leader = #{record.managerLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.saleSupervisor != null" >
        sale_supervisor = #{record.saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.ListingBrandCheckLog" >
    update listing_brand_check_log
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="accountConfigBrand != null" >
        account_config_brand = #{accountConfigBrand,jdbcType=VARCHAR},
      </if>
      <if test="accountHistoryBrand != null" >
        account_history_brand = #{accountHistoryBrand,jdbcType=VARCHAR},
      </if>
      <if test="orderNumTotal != null" >
        order_num_total = #{orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="order24HourSale != null" >
        order_24_hour_sale = #{order24HourSale,jdbcType=INTEGER},
      </if>
      <if test="order7Sale != null" >
        order_7_sale = #{order7Sale,jdbcType=INTEGER},
      </if>
      <if test="order30Sale != null" >
        order_30_sale = #{order30Sale,jdbcType=INTEGER},
      </if>
      <if test="saleMan != null" >
        sale_man = #{saleMan,jdbcType=VARCHAR},
      </if>
      <if test="storeGroupLeader != null" >
        store_group_leader = #{storeGroupLeader,jdbcType=VARCHAR},
      </if>
      <if test="saleGroupLeader != null" >
        sale_group_leader = #{saleGroupLeader,jdbcType=VARCHAR},
      </if>
      <if test="managerLeader != null" >
        manager_leader = #{managerLeader,jdbcType=VARCHAR},
      </if>
      <if test="saleSupervisor != null" >
        sale_supervisor = #{saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert <if test="ignore != null and ignore != ''">${ignore}</if>
    into listing_brand_check_log (site, account_number,
    seller_sku, parent_asin, son_asin,
    sku, brand, account_config_brand,
    account_history_brand, order_num_total, order_24_hour_sale,
    order_7_sale, order_30_sale, sale_man,
    store_group_leader, sale_group_leader, manager_leader,
    sale_supervisor, create_date
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.site,jdbcType=VARCHAR},  #{item.accountNumber,jdbcType=VARCHAR},
      #{item.sellerSku,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR}, #{item.sonAsin,jdbcType=VARCHAR},
      #{item.sku,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.accountConfigBrand,jdbcType=VARCHAR},
      #{item.accountHistoryBrand,jdbcType=VARCHAR}, #{item.orderNumTotal,jdbcType=INTEGER}, #{item.order24HourSale,jdbcType=INTEGER},
      #{item.order7Sale,jdbcType=INTEGER}, #{item.order30Sale,jdbcType=INTEGER}, #{item.saleMan,jdbcType=VARCHAR},
      #{item.storeGroupLeader,jdbcType=VARCHAR}, #{item.saleGroupLeader,jdbcType=VARCHAR}, #{item.managerLeader,jdbcType=VARCHAR},
      #{item.saleSupervisor,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

</mapper>