<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonAccountOfflineConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonAccountOfflineConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="business_id" property="businessId" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="listing_time" property="listingTime" jdbcType="INTEGER" />
    <result column="listing_time_unit" property="listingTimeUnit" jdbcType="VARCHAR" />
    <result column="sales_unit" property="salesUnit" jdbcType="VARCHAR" />
    <result column="sales_from" property="salesFrom" jdbcType="INTEGER" />
    <result column="sales_to" property="salesTo" jdbcType="INTEGER" />
    <result column="exe_frequency" property="exeFrequency" jdbcType="VARCHAR" />
    <result column="exe_day" property="exeDay" jdbcType="VARCHAR" />
    <result column="exe_start_time" property="exeStartTime" jdbcType="VARCHAR" />
    <result column="max_offline_count" property="maxOfflineCount" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, business_id, account_number, listing_time, listing_time_unit, sales_unit, sales_from, 
    sales_to, exe_frequency, exe_day, exe_start_time, max_offline_count, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountOfflineConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_account_offline_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_account_offline_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_account_offline_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountOfflineConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_account_offline_config (business_id, account_number, listing_time, 
      listing_time_unit, sales_unit, sales_from, 
      sales_to, exe_frequency, exe_day, 
      exe_start_time, max_offline_count, create_by, 
      create_time, update_by, update_time
      )
    values (#{businessId,jdbcType=INTEGER}, #{accountNumber,jdbcType=VARCHAR}, #{listingTime,jdbcType=INTEGER}, 
      #{listingTimeUnit,jdbcType=VARCHAR}, #{salesUnit,jdbcType=VARCHAR}, #{salesFrom,jdbcType=INTEGER}, 
      #{salesTo,jdbcType=INTEGER}, #{exeFrequency,jdbcType=VARCHAR}, #{exeDay,jdbcType=VARCHAR}, 
      #{exeStartTime,jdbcType=VARCHAR}, #{maxOfflineCount,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountOfflineConfigExample" resultType="java.lang.Integer" >
    select count(*) from amazon_account_offline_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_account_offline_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null" >
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.listingTime != null" >
        listing_time = #{record.listingTime,jdbcType=INTEGER},
      </if>
      <if test="record.listingTimeUnit != null" >
        listing_time_unit = #{record.listingTimeUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.salesUnit != null" >
        sales_unit = #{record.salesUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.salesFrom != null" >
        sales_from = #{record.salesFrom,jdbcType=INTEGER},
      </if>
      <if test="record.salesTo != null" >
        sales_to = #{record.salesTo,jdbcType=INTEGER},
      </if>
      <if test="record.exeFrequency != null" >
        exe_frequency = #{record.exeFrequency,jdbcType=VARCHAR},
      </if>
      <if test="record.exeDay != null" >
        exe_day = #{record.exeDay,jdbcType=VARCHAR},
      </if>
      <if test="record.exeStartTime != null" >
        exe_start_time = #{record.exeStartTime,jdbcType=VARCHAR},
      </if>
      <if test="record.maxOfflineCount != null" >
        max_offline_count = #{record.maxOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountOfflineConfig" >
    update amazon_account_offline_config
    <set >
      <if test="businessId != null" >
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="listingTime != null" >
        listing_time = #{listingTime,jdbcType=INTEGER},
      </if>
      <if test="listingTimeUnit != null" >
        listing_time_unit = #{listingTimeUnit,jdbcType=VARCHAR},
      </if>
      <if test="salesUnit != null" >
        sales_unit = #{salesUnit,jdbcType=VARCHAR},
      </if>
      <if test="salesFrom != null" >
        sales_from = #{salesFrom,jdbcType=INTEGER},
      </if>
      <if test="salesTo != null" >
        sales_to = #{salesTo,jdbcType=INTEGER},
      </if>
      <if test="exeFrequency != null" >
        exe_frequency = #{exeFrequency,jdbcType=VARCHAR},
      </if>
      <if test="exeDay != null" >
        exe_day = #{exeDay,jdbcType=VARCHAR},
      </if>
      <if test="exeStartTime != null" >
        exe_start_time = #{exeStartTime,jdbcType=VARCHAR},
      </if>
      <if test="maxOfflineCount != null" >
        max_offline_count = #{maxOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>