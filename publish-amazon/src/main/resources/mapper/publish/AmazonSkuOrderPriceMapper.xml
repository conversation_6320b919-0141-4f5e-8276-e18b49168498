<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonSkuOrderPriceMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonSkuOrderPrice" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="is_online" property="isOnline" jdbcType="BIT" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="tag_names" property="tagNames" jdbcType="VARCHAR" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="order_logistics" property="orderLogistics" jdbcType="VARCHAR" />
    <result column="order_price" property="orderPrice" jdbcType="DOUBLE" />
    <result column="order_profit" property="orderProfit" jdbcType="DOUBLE" />
    <result column="package_weight" property="packageWeight" jdbcType="DOUBLE" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="shipping_cost" property="shippingCost" jdbcType="DOUBLE" />
    <result column="total_price" property="totalPrice" jdbcType="DOUBLE" />
    <result column="rule_code" property="ruleCode" jdbcType="INTEGER" />
    <result column="rule" property="rule" jdbcType="VARCHAR" />
    <result column="update_price" property="updatePrice" jdbcType="DOUBLE" />
    <result column="update_total_price" property="updateTotalPrice" jdbcType="DOUBLE" />
    <result column="update_profit" property="updateProfit" jdbcType="DOUBLE" />
    <result column="calc_logistics" property="calcLogistics" jdbcType="VARCHAR" />
    <result column="profit_logistics" property="profitLogistics" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="result_msg" property="resultMsg" jdbcType="VARCHAR" />
    <result column="extra_data" property="extraData" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, seller_sku, parent_asin, son_asin, is_online, article_number,
    tag_names, currency_code, order_logistics, order_price, order_profit, package_weight,
    price, shipping_cost, total_price, rule_code, `rule`, update_price, update_total_price,
    update_profit, calc_logistics, profit_logistics, `status`, result_msg, extra_data,
    create_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderPriceExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_sku_order_price
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from amazon_sku_order_price
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_sku_order_price
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderPrice" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_sku_order_price (account_number, site, seller_sku,
      parent_asin, son_asin, is_online,
      article_number, tag_names, currency_code,
      order_logistics, order_price, order_profit,
      package_weight, price, shipping_cost,
      total_price, rule_code, `rule`,
      update_price, update_total_price, update_profit,
      calc_logistics, profit_logistics, `status`,
      result_msg, extra_data, create_time,
      update_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR},
      #{parentAsin,jdbcType=VARCHAR}, #{sonAsin,jdbcType=VARCHAR}, #{isOnline,jdbcType=BIT},
      #{articleNumber,jdbcType=VARCHAR}, #{tagNames,jdbcType=VARCHAR}, #{currencyCode,jdbcType=VARCHAR},
      #{orderLogistics,jdbcType=VARCHAR}, #{orderPrice,jdbcType=DOUBLE}, #{orderProfit,jdbcType=DOUBLE},
      #{packageWeight,jdbcType=DOUBLE}, #{price,jdbcType=DOUBLE}, #{shippingCost,jdbcType=DOUBLE},
      #{totalPrice,jdbcType=DOUBLE}, #{ruleCode,jdbcType=INTEGER}, #{rule,jdbcType=VARCHAR},
      #{updatePrice,jdbcType=DOUBLE}, #{updateTotalPrice,jdbcType=DOUBLE}, #{updateProfit,jdbcType=DOUBLE},
      #{calcLogistics,jdbcType=VARCHAR}, #{profitLogistics,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{resultMsg,jdbcType=VARCHAR}, #{extraData,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="batchInsert" parameterType="java.util.List">
    insert ignore into  amazon_sku_order_price (account_number, site, seller_sku,
    parent_asin, son_asin, is_online,
    article_number, tag_names, currency_code,
    order_logistics, order_price, order_profit,
    package_weight, price, shipping_cost,
    total_price, rule_code, `rule`,
    update_price, update_total_price, update_profit,
    calc_logistics, profit_logistics, `status`,
    result_msg, extra_data, create_time,
    update_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR},
      #{item.parentAsin,jdbcType=VARCHAR}, #{item.sonAsin,jdbcType=VARCHAR}, #{item.isOnline,jdbcType=BIT},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.tagNames,jdbcType=VARCHAR}, #{item.currencyCode,jdbcType=VARCHAR},
      #{item.orderLogistics,jdbcType=VARCHAR}, #{item.orderPrice,jdbcType=DOUBLE}, #{item.orderProfit,jdbcType=DOUBLE},
      #{item.packageWeight,jdbcType=DOUBLE}, #{item.price,jdbcType=DOUBLE}, #{item.shippingCost,jdbcType=DOUBLE},
      #{item.totalPrice,jdbcType=DOUBLE}, #{item.ruleCode,jdbcType=INTEGER}, #{item.rule,jdbcType=VARCHAR},
      #{item.updatePrice,jdbcType=DOUBLE}, #{item.updateTotalPrice,jdbcType=DOUBLE}, #{item.updateProfit,jdbcType=DOUBLE},
      #{item.calcLogistics,jdbcType=VARCHAR}, #{item.profitLogistics,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
      #{item.resultMsg,jdbcType=VARCHAR}, #{item.extraData,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderPriceExample" resultType="java.lang.Integer" >
    select count(*) from amazon_sku_order_price
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_sku_order_price
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnline != null" >
        is_online = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.tagNames != null" >
        tag_names = #{record.tagNames,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLogistics != null" >
        order_logistics = #{record.orderLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPrice != null" >
        order_price = #{record.orderPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.orderProfit != null" >
        order_profit = #{record.orderProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.packageWeight != null" >
        package_weight = #{record.packageWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingCost != null" >
        shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.totalPrice != null" >
        total_price = #{record.totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.ruleCode != null" >
        rule_code = #{record.ruleCode,jdbcType=INTEGER},
      </if>
      <if test="record.rule != null" >
        `rule` = #{record.rule,jdbcType=VARCHAR},
      </if>
      <if test="record.updatePrice != null" >
        update_price = #{record.updatePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.updateTotalPrice != null" >
        update_total_price = #{record.updateTotalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.updateProfit != null" >
        update_profit = #{record.updateProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.calcLogistics != null" >
        calc_logistics = #{record.calcLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.profitLogistics != null" >
        profit_logistics = #{record.profitLogistics,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.resultMsg != null" >
        result_msg = #{record.resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.extraData != null" >
        extra_data = #{record.extraData,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonSkuOrderPrice" >
    update amazon_sku_order_price
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null" >
        is_online = #{isOnline,jdbcType=BIT},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="tagNames != null" >
        tag_names = #{tagNames,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="orderLogistics != null" >
        order_logistics = #{orderLogistics,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null" >
        order_price = #{orderPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderProfit != null" >
        order_profit = #{orderProfit,jdbcType=DOUBLE},
      </if>
      <if test="packageWeight != null" >
        package_weight = #{packageWeight,jdbcType=DOUBLE},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="shippingCost != null" >
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="totalPrice != null" >
        total_price = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="ruleCode != null" >
        rule_code = #{ruleCode,jdbcType=INTEGER},
      </if>
      <if test="rule != null" >
        `rule` = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="updatePrice != null" >
        update_price = #{updatePrice,jdbcType=DOUBLE},
      </if>
      <if test="updateTotalPrice != null" >
        update_total_price = #{updateTotalPrice,jdbcType=DOUBLE},
      </if>
      <if test="updateProfit != null" >
        update_profit = #{updateProfit,jdbcType=DOUBLE},
      </if>
      <if test="calcLogistics != null" >
        calc_logistics = #{calcLogistics,jdbcType=VARCHAR},
      </if>
      <if test="profitLogistics != null" >
        profit_logistics = #{profitLogistics,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="resultMsg != null" >
        result_msg = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="extraData != null" >
        extra_data = #{extraData,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchByPrimaryKeySelective" parameterType="java.util.List">
      <foreach collection="list" item="item" index="index" separator=";">
          update amazon_sku_order_price
          <set>
              <if test="item.accountNumber != null">
                  account_number = #{item.accountNumber,jdbcType=VARCHAR},
              </if>
              <if test="item.site != null">
                  site = #{item.site,jdbcType=VARCHAR},
              </if>
              <if test="item.sellerSku != null">
                  seller_sku = #{item.sellerSku,jdbcType=VARCHAR},
              </if>
              <if test="item.parentAsin != null">
                  parent_asin = #{item.parentAsin,jdbcType=VARCHAR},
              </if>
              <if test="item.sonAsin != null">
                  son_asin = #{item.sonAsin,jdbcType=VARCHAR},
              </if>
              <if test="item.isOnline != null">
                  is_online = #{item.isOnline,jdbcType=BIT},
              </if>
              <if test="item.articleNumber != null">
                  article_number = #{item.articleNumber,jdbcType=VARCHAR},
              </if>
              <if test="item.tagNames != null">
                  tag_names = #{item.tagNames,jdbcType=VARCHAR},
              </if>
              <if test="item.currencyCode != null">
                  currency_code = #{item.currencyCode,jdbcType=VARCHAR},
              </if>
              <if test="item.orderLogistics != null">
                  order_logistics = #{item.orderLogistics,jdbcType=VARCHAR},
              </if>
              <if test="item.orderPrice != null">
                  order_price = #{item.orderPrice,jdbcType=DOUBLE},
              </if>
              <if test="item.orderProfit != null">
                  order_profit = #{item.orderProfit,jdbcType=DOUBLE},
              </if>
              <if test="item.packageWeight != null">
                  package_weight = #{item.packageWeight,jdbcType=DOUBLE},
              </if>
              <if test="item.price != null">
                  price = #{item.price,jdbcType=DOUBLE},
              </if>
              <if test="item.shippingCost != null">
                  shipping_cost = #{item.shippingCost,jdbcType=DOUBLE},
              </if>
              <if test="item.totalPrice != null">
                  total_price = #{item.totalPrice,jdbcType=DOUBLE},
              </if>
              <if test="item.ruleCode != null">
                  rule_code = #{item.ruleCode,jdbcType=INTEGER},
              </if>
              <if test="item.rule != null">
                  `rule` = #{item.rule,jdbcType=VARCHAR},
              </if>
              <if test="item.updatePrice != null">
                  update_price = #{item.updatePrice,jdbcType=DOUBLE},
              </if>
              <if test="item.updateTotalPrice != null">
                  update_total_price = #{item.updateTotalPrice,jdbcType=DOUBLE},
              </if>
              <if test="item.updateProfit != null">
                  update_profit = #{item.updateProfit,jdbcType=DOUBLE},
              </if>
              <if test="item.calcLogistics != null">
                  calc_logistics = #{item.calcLogistics,jdbcType=VARCHAR},
              </if>
              <if test="item.profitLogistics != null">
                  profit_logistics = #{item.profitLogistics,jdbcType=VARCHAR},
              </if>
              <if test="item.status != null">
                  `status` = #{item.status,jdbcType=INTEGER},
              </if>
              <if test="item.resultMsg != null">
                  result_msg = #{item.resultMsg,jdbcType=VARCHAR},
              </if>
              <if test="item.extraData != null">
                  extra_data = #{item.extraData,jdbcType=VARCHAR},
              </if>
              <if test="item.createTime != null">
                  create_time = #{item.createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="item.updateTime != null">
                  update_time = #{item.updateTime,jdbcType=TIMESTAMP},
              </if>
          </set>
          where id = #{item.id,jdbcType=INTEGER}
      </foreach>
  </update>

    <select id="loadDistinctArticleNumber" resultType="java.lang.String">
    select distinct article_number from amazon_sku_order_price where rule = '30_DAY_NO_SALES' limit #{offset},#{limit}
  </select>

</mapper>