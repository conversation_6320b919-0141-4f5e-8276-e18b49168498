<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonListingUpdateDescMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingUpdateDesc" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="asin" property="asin" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="before_value" property="beforeValue" jdbcType="VARCHAR" />
    <result column="after_value" property="afterValue" jdbcType="VARCHAR" />
    <result column="confirm_status" property="confirmStatus" jdbcType="INTEGER" />
    <result column="confirm_by" property="confirmBy" jdbcType="VARCHAR" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="confirm_remark" property="confirmRemark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="wen_an_type" property="wenAnType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, asin, sku, seller_sku, item_name, before_value, after_value, confirm_status,
    confirm_by, confirm_time, confirm_remark, `status`, remark, create_time, create_by, wen_an_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingUpdateDescExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from amazon_listing_update_desc
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from amazon_listing_update_desc
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_update_desc
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingUpdateDesc" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_update_desc (account_number, site, asin,
      sku, seller_sku, before_value,
      after_value, confirm_status, confirm_by,
      confirm_time, confirm_remark, `status`,
      remark, create_time, create_by, item_name,
      wen_an_type
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{asin,jdbcType=VARCHAR},
      #{sku,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{beforeValue,jdbcType=VARCHAR},
      #{afterValue,jdbcType=VARCHAR}, #{confirmStatus,jdbcType=INTEGER}, #{confirmBy,jdbcType=VARCHAR},
      #{confirmTime,jdbcType=TIMESTAMP}, #{confirmRemark,jdbcType=VARCHAR}, #{status,jdbcType=BIT},
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
      #{wenAnType,jdbcType=INTEGER}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingUpdateDescExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_update_desc
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_update_desc
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.asin != null" >
        asin = #{record.asin,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null" >
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeValue != null" >
        before_value = #{record.beforeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.afterValue != null" >
        after_value = #{record.afterValue,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null" >
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.confirmBy != null" >
        confirm_by = #{record.confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmTime != null" >
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmRemark != null" >
        confirm_remark = #{record.confirmRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.wenAnType != null" >
        wen_an_type = #{record.wenAnType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingUpdateDesc" >
    update amazon_listing_update_desc
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="asin != null" >
        asin = #{asin,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        item_name = #{temName,jdbcType=VARCHAR},
      </if>
      <if test="beforeValue != null" >
        before_value = #{beforeValue,jdbcType=VARCHAR},
      </if>
      <if test="afterValue != null" >
        after_value = #{afterValue,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null" >
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmBy != null" >
        confirm_by = #{confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null" >
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmRemark != null" >
        confirm_remark = #{confirmRemark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="wenAnType != null" >
        wen_an_type = #{wenAnType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert into amazon_listing_update_desc (
      account_number, site, asin,
      sku, seller_sku, before_value,
      after_value, confirm_status, confirm_by,
      confirm_time, confirm_remark, `status`,
      remark, create_time, create_by, item_name,
      wen_an_type
    )
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.asin,jdbcType=VARCHAR},
      #{item.sku,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.beforeValue,jdbcType=VARCHAR},
      #{item.afterValue,jdbcType=VARCHAR}, #{item.confirmStatus,jdbcType=INTEGER}, #{item.confirmBy,jdbcType=VARCHAR},
      #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.confirmRemark,jdbcType=VARCHAR}, #{item.status,jdbcType=BIT},
      #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
      #{item.wenAnType,jdbcType=INTEGER}
      )
    </foreach>
    </insert>

  <update id="batchUpdateConfirmStatus">
        <foreach collection="list" item="record" separator=";">
          update amazon_listing_update_desc
          set
            confirm_status = #{record.confirmStatus},
            confirm_by = #{record.confirmBy},
            confirm_time = #{record.confirmTime},
            confirm_remark = #{record.confirmRemark}
          where id = #{record.id}
        </foreach>
  </update>

  <update id="batchUpdateStatus" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      update amazon_listing_update_desc
      <set>
        status = #{record.status},
        <if test="record.remark != null">
          remark = #{record.remark,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{record.id}
    </foreach>
  </update>

  <delete id="deleteByExample">
      delete from amazon_listing_update_desc
      <if test="_parameter != null" >
          <include refid="Example_Where_Clause" />
      </if>
  </delete>

  <update id="batchUpdate">
    <foreach collection="list" item="record" separator=";">
      update amazon_listing_update_desc
      set
      account_number = #{record.accountNumber},
      site = #{record.site},
      asin = #{record.asin},
      sku = #{record.sku},
      seller_sku = #{record.sellerSku},
      before_value = #{record.beforeValue},
      after_value = #{record.afterValue},
      item_name = #{record.itemName},
      confirm_status = #{record.confirmStatus},
      confirm_by = #{record.confirmBy},
      confirm_time = #{record.confirmTime},
      confirm_remark = #{record.confirmRemark},
      `status` = #{record.status},
      remark = #{record.remark},
      create_time = #{record.createTime},
      create_by = #{record.createBy},
      wen_an_type = #{record.wenAnType}
      where id = #{record.id}
    </foreach>
  </update>
</mapper>