<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonCategoryMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="account_site" jdbcType="VARCHAR" property="accountSite" />
    <result column="browse_node_id" jdbcType="VARCHAR" property="browseNodeId" />
    <result column="browse_node_name" jdbcType="VARCHAR" property="browseNodeName" />
    <result column="browse_node_store_context_name" jdbcType="VARCHAR" property="browseNodeStoreContextName" />
    <result column="has_children" jdbcType="BIT" property="hasChildren" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="enable"  jdbcType="BIT" property="enable"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <result column="browse_node_attributes" jdbcType="LONGVARCHAR" property="browseNodeAttributes" />
    <result column="browse_path_by_id" jdbcType="LONGVARCHAR" property="browsePathById" />
    <result column="browse_path_by_name" jdbcType="LONGVARCHAR" property="browsePathByName" />
    <result column="browse_path_by_name_cn" jdbcType="LONGVARCHAR" property="browsePathByNameCn" />
    <result column="child_nodes" jdbcType="LONGVARCHAR" property="childNodes" />
    <result column="product_type_definitions" jdbcType="LONGVARCHAR" property="productTypeDefinitions" />
    <result column="refinements_information" jdbcType="LONGVARCHAR" property="refinementsInformation" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    id, account_number, account_site, browse_node_id, browse_node_name, browse_node_store_context_name, has_children,
    last_update_date,create_date,enable
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    browse_node_attributes, browse_path_by_id, browse_path_by_name,browse_path_by_name_cn, child_nodes, product_type_definitions,
    refinements_information
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_category_sp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


    <select id="selectFiledColumnsByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryExample" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Jul 18 16:05:48 CST 2019.
        -->
        select ${columns}
        from amazon_category_sp
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryExample" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generatedxc
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Jul 18 16:05:48 CST 2019.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List" />
        from amazon_category_sp
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_category_sp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    delete from amazon_category_sp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    delete from amazon_category_sp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert ignore into amazon_category_sp (account_number, account_site,
      browse_node_id, browse_node_name, browse_node_store_context_name,
      has_children, last_update_date, browse_node_attributes, 
      browse_path_by_id, browse_path_by_name, browse_path_by_name_cn,
      child_nodes, product_type_definitions, 
      refinements_information,enable,create_date)
    values (#{accountNumber,jdbcType=VARCHAR}, #{accountSite,jdbcType=VARCHAR},
      #{browseNodeId,jdbcType=VARCHAR}, #{browseNodeName,jdbcType=VARCHAR}, #{browseNodeStoreContextName,jdbcType=VARCHAR},
      #{hasChildren,jdbcType=BIT}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{browseNodeAttributes,jdbcType=LONGVARCHAR}, 
      #{browsePathById,jdbcType=LONGVARCHAR}, #{browsePathByName,jdbcType=LONGVARCHAR}, #{browsePathByNameCn,jdbcType=LONGVARCHAR},
    #{childNodes,jdbcType=LONGVARCHAR}, #{productTypeDefinitions,jdbcType=LONGVARCHAR},
      #{refinementsInformation,jdbcType=LONGVARCHAR}, #{enable,jdbcType=BIT}, #{createDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_category_sp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="accountSite != null">
        account_site,
      </if>
      <if test="browseNodeId != null">
        browse_node_id,
      </if>
      <if test="browseNodeName != null">
        browse_node_name,
      </if>
      <if test="browseNodeStoreContextName != null">
        browse_node_store_context_name,
      </if>
      <if test="hasChildren != null">
        has_children,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="browseNodeAttributes != null">
        browse_node_attributes,
      </if>
      <if test="browsePathById != null">
        browse_path_by_id,
      </if>
      <if test="browsePathByName != null">
        browse_path_by_name,
      </if>
      <if test="browsePathByNameCn != null">
        browse_path_by_name_cn,
      </if>
      <if test="childNodes != null">
        child_nodes,
      </if>
      <if test="productTypeDefinitions != null">
        product_type_definitions,
      </if>
      <if test="refinementsInformation != null">
        refinements_information,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountSite != null">
        #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null">
        #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeName != null">
        #{browseNodeName,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeStoreContextName != null">
        #{browseNodeStoreContextName,jdbcType=VARCHAR},
      </if>
      <if test="hasChildren != null">
        #{hasChildren,jdbcType=BIT},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="browseNodeAttributes != null">
        #{browseNodeAttributes,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathById != null">
        #{browsePathById,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByName != null">
        #{browsePathByName,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByNameCn != null">
        #{browsePathByNameCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="childNodes != null">
        #{childNodes,jdbcType=LONGVARCHAR},
      </if>
      <if test="productTypeDefinitions != null">
        #{productTypeDefinitions,jdbcType=LONGVARCHAR},
      </if>
      <if test="refinementsInformation != null">
        #{refinementsInformation,jdbcType=LONGVARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>

    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    select count(*) from amazon_category_sp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSite != null">
        account_site = #{record.accountSite,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeId != null">
        browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeName != null">
        browse_node_name = #{record.browseNodeName,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeStoreContextName != null">
        browse_node_store_context_name = #{record.browseNodeStoreContextName,jdbcType=VARCHAR},
      </if>
      <if test="record.hasChildren != null">
        has_children = #{record.hasChildren,jdbcType=BIT},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.browseNodeAttributes != null">
        browse_node_attributes = #{record.browseNodeAttributes,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.browsePathById != null">
        browse_path_by_id = #{record.browsePathById,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.browsePathByName != null">
        browse_path_by_name = #{record.browsePathByName,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.browsePathByNameCn != null">
        browse_path_by_name_cn = #{record.browsePathByNameCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.childNodes != null">
        child_nodes = #{record.childNodes,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.productTypeDefinitions != null">
        product_type_definitions = #{record.productTypeDefinitions,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refinementsInformation != null">
        refinements_information = #{record.refinementsInformation,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    set id = #{record.id,jdbcType=INTEGER},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      account_site = #{record.accountSite,jdbcType=VARCHAR},
      browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      browse_node_name = #{record.browseNodeName,jdbcType=VARCHAR},
      browse_node_store_context_name = #{record.browseNodeStoreContextName,jdbcType=VARCHAR},
      has_children = #{record.hasChildren,jdbcType=BIT},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      browse_node_attributes = #{record.browseNodeAttributes,jdbcType=LONGVARCHAR},
      browse_path_by_id = #{record.browsePathById,jdbcType=LONGVARCHAR},
      browse_path_by_name = #{record.browsePathByName,jdbcType=LONGVARCHAR},
      browse_path_by_name_cn = #{record.browsePathByNameCn,jdbcType=LONGVARCHAR},
      child_nodes = #{record.childNodes,jdbcType=LONGVARCHAR},
      product_type_definitions = #{record.productTypeDefinitions,jdbcType=LONGVARCHAR},
      refinements_information = #{record.refinementsInformation,jdbcType=LONGVARCHAR},
      enable = #{record.enable,jdbcType=BIT},
      create_date = #{record.createDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    set id = #{record.id,jdbcType=INTEGER},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      account_site = #{record.accountSite,jdbcType=VARCHAR},
      browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      browse_node_name = #{record.browseNodeName,jdbcType=VARCHAR},
      browse_node_store_context_name = #{record.browseNodeStoreContextName,jdbcType=VARCHAR},
      has_children = #{record.hasChildren,jdbcType=BIT},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      enable = #{record.enable,jdbcType=BIT},
      create_date = #{record.createDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    <set>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountSite != null">
        account_site = #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null">
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeName != null">
        browse_node_name = #{browseNodeName,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeStoreContextName != null">
        browse_node_store_context_name = #{browseNodeStoreContextName,jdbcType=VARCHAR},
      </if>
      <if test="hasChildren != null">
        has_children = #{hasChildren,jdbcType=BIT},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="browseNodeAttributes != null">
        browse_node_attributes = #{browseNodeAttributes,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathById != null">
        browse_path_by_id = #{browsePathById,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByName != null">
        browse_path_by_name = #{browsePathByName,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByNameCn != null">
        browse_path_by_name_cn = #{browsePathByNameCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="childNodes != null">
        child_nodes = #{childNodes,jdbcType=LONGVARCHAR},
      </if>
      <if test="productTypeDefinitions != null">
        product_type_definitions = #{productTypeDefinitions,jdbcType=LONGVARCHAR},
      </if>
      <if test="refinementsInformation != null">
        refinements_information = #{refinementsInformation,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBySiteAndPath" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    <set>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountSite != null">
        account_site = #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null">
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeName != null">
        browse_node_name = #{browseNodeName,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeStoreContextName != null">
        browse_node_store_context_name = #{browseNodeStoreContextName,jdbcType=VARCHAR},
      </if>
      <if test="hasChildren != null">
        has_children = #{hasChildren,jdbcType=BIT},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="browseNodeAttributes != null">
        browse_node_attributes = #{browseNodeAttributes,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathById != null">
        browse_path_by_id = #{browsePathById,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByName != null">
        browse_path_by_name = #{browsePathByName,jdbcType=LONGVARCHAR},
      </if>
      <if test="browsePathByNameCn != null">
        browse_path_by_name_cn = #{browsePathByNameCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="childNodes != null">
        child_nodes = #{childNodes,jdbcType=LONGVARCHAR},
      </if>
      <if test="productTypeDefinitions != null">
        product_type_definitions = #{productTypeDefinitions,jdbcType=LONGVARCHAR},
      </if>
      <if test="refinementsInformation != null">
        refinements_information = #{refinementsInformation,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where account_site = #{accountSite,jdbcType=VARCHAR} and browse_path_by_id = #{browsePathById,jdbcType=LONGVARCHAR}
  </update>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="categoryList" item="item" open="" separator=";" close=";">
    update amazon_category_sp
    <set>
      <if test="item.accountNumber != null">
        account_number = #{item.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="item.accountSite != null">
        account_site = #{item.accountSite,jdbcType=VARCHAR},
      </if>
      <if test="item.browseNodeId != null">
        browse_node_id = #{item.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="item.browseNodeName != null">
        browse_node_name = #{item.browseNodeName,jdbcType=VARCHAR},
      </if>
      <if test="item.browseNodeStoreContextName != null">
        browse_node_store_context_name = #{item.browseNodeStoreContextName,jdbcType=VARCHAR},
      </if>
      <if test="item.hasChildren != null">
        has_children = #{item.hasChildren,jdbcType=BIT},
      </if>
      <if test="item.lastUpdateDate != null">
        last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.browseNodeAttributes != null">
        browse_node_attributes = #{item.browseNodeAttributes,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.browsePathById != null">
        browse_path_by_id = #{item.browsePathById,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.browsePathByName != null">
        browse_path_by_name = #{item.browsePathByName,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.browsePathByNameCn != null">
        browse_path_by_name_cn = #{item.browsePathByNameCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.childNodes != null">
        child_nodes = #{item.childNodes,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.productTypeDefinitions != null">
        product_type_definitions = #{item.productTypeDefinitions,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.refinementsInformation != null">
        refinements_information = #{item.refinementsInformation,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.enable != null">
        enable = #{item.enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    set account_number = #{accountNumber,jdbcType=VARCHAR},
      account_site = #{accountSite,jdbcType=VARCHAR},
      browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      browse_node_name = #{browseNodeName,jdbcType=VARCHAR},
      browse_node_store_context_name = #{browseNodeStoreContextName,jdbcType=VARCHAR},
      has_children = #{hasChildren,jdbcType=BIT},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      browse_node_attributes = #{browseNodeAttributes,jdbcType=LONGVARCHAR},
      browse_path_by_id = #{browsePathById,jdbcType=LONGVARCHAR},
      browse_path_by_name = #{browsePathByName,jdbcType=LONGVARCHAR},
      browse_path_by_name_cn = #{browsePathByNameCn,jdbcType=LONGVARCHAR},
      child_nodes = #{childNodes,jdbcType=LONGVARCHAR},
      product_type_definitions = #{productTypeDefinitions,jdbcType=LONGVARCHAR},
      refinements_information = #{refinementsInformation,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:05:48 CST 2019.
    -->
    update amazon_category_sp
    set account_number = #{accountNumber,jdbcType=VARCHAR},
      account_site = #{accountSite,jdbcType=VARCHAR},
      browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      browse_node_name = #{browseNodeName,jdbcType=VARCHAR},
      browse_node_store_context_name = #{browseNodeStoreContextName,jdbcType=VARCHAR},
      has_children = #{hasChildren,jdbcType=BIT},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectCategoryAllAccount" resultMap="BaseResultMap">
    select account_number,account_site
    from amazon_category_sp
    where account_number is not null
    group by account_number,account_site
  </select>

</mapper>