<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonCategorySpProductTypeMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonCategorySpProductType" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_site" property="accountSite" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="display_name" property="displayName" jdbcType="VARCHAR" />
    <result column="information" property="information" jdbcType="VARCHAR" />
    <result column="schema_url" property="schemaUrl" jdbcType="VARCHAR" />
    <result column="product_type_version" property="productTypeVersion" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="requirements" property="requirements" jdbcType="VARCHAR" />
    <result column="sync_status" property="syncStatus" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="properties_keys" property="propertiesKeys" jdbcType="VARCHAR"/>
    <result column="properties_sync_time" property="propertiesSyncTime" jdbcType="TIMESTAMP"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id,
    account_site,
    product_type,
    display_name,
    information,
    schema_url,
    product_type_version,
    create_date,
    last_update_date,
    requirements,
    sync_status,
    remark,
    properties_keys,
    properties_sync_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonCategorySpProductTypeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_category_sp_product_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_category_sp_product_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_category_sp_product_type
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonCategorySpProductType" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_category_sp_product_type (account_site, product_type, display_name, 
      information, schema_url, product_type_version, 
      create_date, last_update_date, requirements, 
      sync_status, remark)
    values (#{accountSite,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{displayName,jdbcType=VARCHAR}, 
      #{information,jdbcType=VARCHAR}, #{schemaUrl,jdbcType=VARCHAR}, #{productTypeVersion,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{requirements,jdbcType=VARCHAR}, 
      #{syncStatus,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategorySpProductTypeExample" resultType="java.lang.Integer" >
    select count(*) from amazon_category_sp_product_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_category_sp_product_type
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountSite != null" >
        account_site = #{record.accountSite,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.displayName != null" >
        display_name = #{record.displayName,jdbcType=VARCHAR},
      </if>
      <if test="record.information != null" >
        information = #{record.information,jdbcType=VARCHAR},
      </if>
      <if test="record.schemaUrl != null" >
        schema_url = #{record.schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.productTypeVersion != null" >
        product_type_version = #{record.productTypeVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requirements != null" >
        requirements = #{record.requirements,jdbcType=VARCHAR},
      </if>
      <if test="record.syncStatus != null" >
        sync_status = #{record.syncStatus,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCategorySpProductType" >
    update amazon_category_sp_product_type
    <set >
      <if test="accountSite != null" >
        account_site = #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null" >
        display_name = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="information != null" >
        information = #{information,jdbcType=VARCHAR},
      </if>
      <if test="schemaUrl != null" >
        schema_url = #{schemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="productTypeVersion != null" >
        product_type_version = #{productTypeVersion,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="requirements != null" >
        requirements = #{requirements,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null" >
        sync_status = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonCategorySpProductTypeExample" >
    select ${filedColumns}
    from amazon_category_sp_product_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="updateProductTypeKeysById">
    update amazon_category_sp_product_type set properties_keys = #{keys}, properties_sync_time = now() where id = #{id}
  </update>
</mapper>