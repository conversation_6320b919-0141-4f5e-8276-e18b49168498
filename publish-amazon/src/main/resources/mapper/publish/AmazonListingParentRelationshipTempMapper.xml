<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonListingParentRelationshipTempMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="order_sale" property="orderSale" jdbcType="BIT" />
    <result column="fba" property="fba" jdbcType="BIT" />
    <result column="item_status" property="itemStatus" jdbcType="VARCHAR" />
    <result column="online" property="online" jdbcType="BIT" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="browse_node_id" property="browseNodeId" jdbcType="VARCHAR" />
    <result column="variation_theme" property="variationTheme" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="model_number" property="modelNumber" jdbcType="VARCHAR" />
    <result column="child_asins" property="childAsins" jdbcType="VARCHAR" />
    <result column="child_sellerskus" property="childSellerskus" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="product_message_xml" property="productMessageXml" jdbcType="VARCHAR" />
    <result column="product_success_status" property="productSuccessStatus" jdbcType="BIT" />
    <result column="product_errorMsg" property="productErrormsg" jdbcType="VARCHAR" />
    <result column="product_success_time" property="productSuccessTime" jdbcType="TIMESTAMP" />
    <result column="relationship_message_xml" property="relationshipMessageXml" jdbcType="VARCHAR" />
    <result column="relationship_success_status" property="relationshipSuccessStatus" jdbcType="BIT" />
    <result column="relationship_errorMsg" property="relationshipErrormsg" jdbcType="VARCHAR" />
    <result column="relationship_success_time" property="relationshipSuccessTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, son_asin, seller_sku, article_number, order_sale, fba, 
    item_status, `online`, item_name, product_type, browse_node_id, variation_theme, 
    brand_name, manufacturer, model_number, child_asins, child_sellerskus, create_date, 
    update_date, product_message_xml, product_success_status, product_errorMsg, product_success_time, 
    relationship_message_xml, relationship_success_status, relationship_errorMsg, relationship_success_time,remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTempExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <if test="columns != null">
      ${columns}
    </if>
    <if test="columns == null">
      <include refid="Base_Column_List" />
    </if>
    from amazon_listing_parent_relationship_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_listing_parent_relationship_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_parent_relationship_temp
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_parent_relationship_temp (account_number, site, son_asin, 
      seller_sku, article_number, order_sale, 
      fba, item_status, `online`, item_name, 
      product_type, browse_node_id, variation_theme, 
      brand_name, manufacturer, model_number, 
      child_asins, child_sellerskus, create_date, 
      update_date, product_message_xml, product_success_status, 
      product_errorMsg, product_success_time, 
      relationship_message_xml, relationship_success_status, 
      relationship_errorMsg, relationship_success_time,remark
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{sonAsin,jdbcType=VARCHAR}, 
      #{sellerSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{orderSale,jdbcType=BIT}, 
      #{fba,jdbcType=BIT}, #{itemStatus,jdbcType=VARCHAR}, #{online,jdbcType=BIT}, #{itemName,jdbcType=VARCHAR}, 
      #{productType,jdbcType=VARCHAR}, #{browseNodeId,jdbcType=VARCHAR}, #{variationTheme,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{modelNumber,jdbcType=VARCHAR}, 
      #{childAsins,jdbcType=VARCHAR}, #{childSellerskus,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{productMessageXml,jdbcType=VARCHAR}, #{productSuccessStatus,jdbcType=BIT}, 
      #{productErrormsg,jdbcType=VARCHAR}, #{productSuccessTime,jdbcType=TIMESTAMP}, 
      #{relationshipMessageXml,jdbcType=VARCHAR}, #{relationshipSuccessStatus,jdbcType=BIT}, 
      #{relationshipErrormsg,jdbcType=VARCHAR}, #{relationshipSuccessTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTempExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_parent_relationship_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_parent_relationship_temp
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSale != null" >
        order_sale = #{record.orderSale,jdbcType=BIT},
      </if>
      <if test="record.fba != null" >
        fba = #{record.fba,jdbcType=BIT},
      </if>
      <if test="record.itemStatus != null" >
        item_status = #{record.itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.online != null" >
        `online` = #{record.online,jdbcType=BIT},
      </if>
      <if test="record.itemName != null" >
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeId != null" >
        browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.variationTheme != null" >
        variation_theme = #{record.variationTheme,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null" >
        model_number = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.childAsins != null" >
        child_asins = #{record.childAsins,jdbcType=VARCHAR},
      </if>
      <if test="record.childSellerskus != null" >
        child_sellerskus = #{record.childSellerskus,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productMessageXml != null" >
        product_message_xml = #{record.productMessageXml,jdbcType=VARCHAR},
      </if>
      <if test="record.productSuccessStatus != null" >
        product_success_status = #{record.productSuccessStatus,jdbcType=BIT},
      </if>
      <if test="record.productErrormsg != null" >
        product_errorMsg = #{record.productErrormsg,jdbcType=VARCHAR},
      </if>
      <if test="record.productSuccessTime != null" >
        product_success_time = #{record.productSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.relationshipMessageXml != null" >
        relationship_message_xml = #{record.relationshipMessageXml,jdbcType=VARCHAR},
      </if>
      <if test="record.relationshipSuccessStatus != null" >
        relationship_success_status = #{record.relationshipSuccessStatus,jdbcType=BIT},
      </if>
      <if test="record.relationshipErrormsg != null" >
        relationship_errorMsg = #{record.relationshipErrormsg,jdbcType=VARCHAR},
      </if>
      <if test="record.relationshipSuccessTime != null" >
        relationship_success_time = #{record.relationshipSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingParentRelationshipTemp" >
    update amazon_listing_parent_relationship_temp
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderSale != null" >
        order_sale = #{orderSale,jdbcType=BIT},
      </if>
      <if test="fba != null" >
        fba = #{fba,jdbcType=BIT},
      </if>
      <if test="itemStatus != null" >
        item_status = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="online != null" >
        `online` = #{online,jdbcType=BIT},
      </if>
      <if test="itemName != null" >
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null" >
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="variationTheme != null" >
        variation_theme = #{variationTheme,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null" >
        model_number = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="childAsins != null" >
        child_asins = #{childAsins,jdbcType=VARCHAR},
      </if>
      <if test="childSellerskus != null" >
        child_sellerskus = #{childSellerskus,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="productMessageXml != null" >
        product_message_xml = #{productMessageXml,jdbcType=VARCHAR},
      </if>
      <if test="productSuccessStatus != null" >
        product_success_status = #{productSuccessStatus,jdbcType=BIT},
      </if>
      <if test="productErrormsg != null" >
        product_errorMsg = #{productErrormsg,jdbcType=VARCHAR},
      </if>
      <if test="productSuccessTime != null" >
        product_success_time = #{productSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relationshipMessageXml != null" >
        relationship_message_xml = #{relationshipMessageXml,jdbcType=VARCHAR},
      </if>
      <if test="relationshipSuccessStatus != null" >
        relationship_success_status = #{relationshipSuccessStatus,jdbcType=BIT},
      </if>
      <if test="relationshipErrormsg != null" >
        relationship_errorMsg = #{relationshipErrormsg,jdbcType=VARCHAR},
      </if>
      <if test="relationshipSuccessTime != null" >
        relationship_success_time = #{relationshipSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAccountNumberList" resultType="java.lang.String" >
    select DISTINCT(account_number) from  amazon_listing_parent_relationship_temp
  </select>


  <select id="selectProductStatusIsNullByAccountNumber" resultMap="BaseResultMap"  >
    select
    id, account_number, seller_sku
    from amazon_listing_parent_relationship_temp
    where account_number = #{accountNumber,jdbcType=VARCHAR}
      and fba = false
      and order_sale = false
      and remark is null
      and product_success_status is null
  </select>

  <select id="selectRelationStatusIsNullByAccountNumber" resultMap="BaseResultMap"  >
    select
      id, account_number, seller_sku
    from amazon_listing_parent_relationship_temp
    where account_number = #{accountNumber,jdbcType=VARCHAR}
      and fba = false
      and order_sale = false
      and remark is null
      and product_success_status = true and (relationship_success_status is null or relationship_success_status=false)
  </select>

  <update id="batchProductStatus" parameterType="java.util.List">
      <foreach collection="list" item="record" separator=";">
          update amazon_listing_parent_relationship_temp
          <set>
              product_success_status = #{record.productSuccessStatus,jdbcType=BIT},
              product_errorMsg = #{record.productErrormsg,jdbcType=VARCHAR},
              <if test="record.productSuccessTime != null">
                  product_success_time = #{record.productSuccessTime,jdbcType=TIMESTAMP},
              </if>
          </set>
      where account_number = #{record.accountNumber,jdbcType=VARCHAR} and seller_sku =  #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>


  <update id="batchRelationStatus" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      update amazon_listing_parent_relationship_temp
      <set>
        relationship_success_status = #{record.relationshipSuccessStatus,jdbcType=BIT},
        relationship_errorMsg = #{record.relationshipErrormsg,jdbcType=VARCHAR},
        <if test="record.relationshipSuccessTime != null">
          relationship_success_time = #{record.relationshipSuccessTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where account_number = #{record.accountNumber,jdbcType=VARCHAR} and seller_sku =  #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>


</mapper>