<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonFormTypeTemplateRelMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRel" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="template_site" property="templateSite" jdbcType="VARCHAR" />
    <result column="template_type" property="templateType" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="type_path_en" property="typePathEn" jdbcType="VARCHAR" />
    <result column="enable" property="enable" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="BaseResultMapBo" type="com.estone.erp.publish.amazon.bo.AmazonFormTypeTemplateRelBo" extends="BaseResultMap">
    <result column="type_path_cn" property="typePathCn" jdbcType="VARCHAR" />
    <result column="type_path_other" property="typePathOther" jdbcType="VARCHAR" />
    <result column="node_id" property="nodeId" jdbcType="VARCHAR" />
    <result column="item_type" property="itemType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_site, template_type, site, type_path_en, `enable`, create_by, create_time, 
    update_by, update_time
  </sql>
  <sql id="Base_Column_List_BO" >
    rel.*, ty.type_path_cn, ty.type_path_other, ty.node_id, ty.item_type
  </sql>
  <sql id="Base_TABLE_BO" >
    from amazon_form_type_template_rel rel
    left join amazon_form_type ty on rel.site = ty.site and rel.type_path_en = ty.type_path_en
  </sql>

  <select id="selectBoByExample" resultMap="BaseResultMapBo" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List_BO" />

    <include refid="Base_TABLE_BO" />

    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_form_type_template_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_form_type_template_rel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_form_type_template_rel
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRel" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_form_type_template_rel (template_site, template_type, site, 
      type_path_en, `enable`, create_by, 
      create_time, update_by, update_time
      )
    values (#{templateSite,jdbcType=VARCHAR}, #{templateType,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, 
      #{typePathEn,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="batchInsert">
    insert ignore into amazon_form_type_template_rel (template_site, template_type, site,
      type_path_en, `enable`, create_by,
      create_time, update_by, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.templateSite,jdbcType=VARCHAR}, #{item.templateType,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR},
      #{item.typePathEn,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}, #{item.createBy,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>

  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRelExample" resultType="java.lang.Integer" >
    select count(*) from amazon_form_type_template_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="countBoByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRelExample" resultType="java.lang.Integer" >
    select count(*)
    <include refid="Base_TABLE_BO" />

    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_form_type_template_rel
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateSite != null" >
        template_site = #{record.templateSite,jdbcType=VARCHAR},
      </if>
      <if test="record.templateType != null" >
        template_type = #{record.templateType,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.typePathEn != null" >
        type_path_en = #{record.typePathEn,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null" >
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeTemplateRel" >
    update amazon_form_type_template_rel
    <set >
      <if test="templateSite != null" >
        template_site = #{templateSite,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null" >
        template_type = #{templateType,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="typePathEn != null" >
        type_path_en = #{typePathEn,jdbcType=VARCHAR},
      </if>
      <if test="enable != null" >
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>