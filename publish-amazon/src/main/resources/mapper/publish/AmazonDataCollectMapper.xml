<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonDataCollectMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonDataCollect" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="sku_quantity" property="skuQuantity" jdbcType="INTEGER" />
    <result column="sale_user" property="saleUser" jdbcType="VARCHAR" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="attribute1" property="attribute1" jdbcType="VARCHAR" />
    <result column="attribute2" property="attribute2" jdbcType="VARCHAR" />
    <result column="attribute3" property="attribute3" jdbcType="VARCHAR" />
    <result column="attribute4" property="attribute4" jdbcType="VARCHAR" />
    <result column="attribute5" property="attribute5" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, `type`, `status`, sale_user, remark, creation_date, created_by, 
    attribute1, attribute2, attribute3, attribute4, attribute5, quantity, sku_quantity, username
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollectExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_data_collect
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectStatisticsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollectExample" >
    select
      account_number, sale_user, username,
      `type`, creation_date,
      sum(quantity) quantity,
      sum(sku_quantity) sku_quantity
    from amazon_data_collect
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    GROUP BY account_number, sale_user

    <if test="havingWhere != null" >
      ${havingWhere}
    </if>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>

    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectCountByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollectExample" >
    select
      id, account_number, `type`, 2 as status, creation_date,
      sum(attribute1) as attribute1, sum(attribute2) as attribute2
    from amazon_data_collect
    <if test="_parameter != null" >
        <include refid="Example_Where_Clause" />
    </if>
      group by account_number
    <if test="orderByClause != null" >
        order by ${orderByClause}
    </if>
    <if test="limit != null" >
        <if test="offset != null" >
            limit ${offset}, ${limit}
        </if>
        <if test="offset == null" >
            limit ${limit}
        </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_data_collect
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_data_collect
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByExample">
    delete from amazon_data_collect
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="_parameter == null" >
      where 1 != 1
    </if>
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollect" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_data_collect (account_number, `type`, `status`, 
      sale_user, remark, creation_date, 
      created_by, attribute1, attribute2, 
      attribute3, attribute4, attribute5,
      quantity, sku_quantity, username
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{saleUser,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR},
      #{quantity,jdbcType=INTEGER}, #{skuQuantity,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}
      )
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollectExample" resultType="java.lang.Integer" >
    select count(*) from amazon_data_collect
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="countStatisticsByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollectExample" resultType="java.lang.Integer" >
    select count(num) from (
      select
        count(DISTINCT account_number, sale_user) num,
        sum(quantity) quantity,
        sum(sku_quantity) sku_quantity
      from amazon_data_collect

      <if test="_parameter != null" >
        <include refid="Example_Where_Clause" />
      </if>

      GROUP BY account_number, sale_user

      <if test="havingWhere != null" >
        ${havingWhere}
      </if>
    )aa
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_data_collect
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.skuQuantity != null" >
        sku_quantity = #{record.skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.saleUser != null" >
        sale_user = #{record.saleUser,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null" >
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute1 != null" >
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null" >
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null" >
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null" >
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonDataCollect" >
    update amazon_data_collect
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="skuQuantity != null" >
        sku_quantity = #{skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="saleUser != null" >
        sale_user = #{saleUser,jdbcType=VARCHAR},
      </if>
      <if test="username != null" >
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="customSelect" resultType="map" parameterType="java.lang.String" >
	${_parameter}
  </select>
</mapper>