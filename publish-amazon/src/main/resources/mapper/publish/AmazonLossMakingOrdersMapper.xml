<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonLossMakingOrdersMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonLossMakingOrders" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="loss_making_24h_order" property="lossMaking24hOrder" jdbcType="INTEGER" />
    <result column="is_online" property="isOnline" jdbcType="BIT" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="asin_status" property="asinStatus" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="close_status" property="closeStatus" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="closed_time" property="closedTime" jdbcType="TIMESTAMP" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, seller_sku, article_number, loss_making_24h_order, is_online, 
    son_asin, asin_status, quantity, close_status, remark, closed_time, created_time, 
    updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonLossMakingOrdersExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_loss_making_orders
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_loss_making_orders
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_loss_making_orders
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonLossMakingOrders" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_loss_making_orders (account_number, site, seller_sku, 
      article_number, loss_making_24h_order, is_online, 
      son_asin, asin_status, quantity, 
      close_status, remark, closed_time, 
      created_time, updated_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, 
      #{articleNumber,jdbcType=VARCHAR}, #{lossMaking24hOrder,jdbcType=INTEGER}, #{isOnline,jdbcType=BIT}, 
      #{sonAsin,jdbcType=VARCHAR}, #{asinStatus,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, 
      #{closeStatus,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{closedTime,jdbcType=TIMESTAMP}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonLossMakingOrdersExample" resultType="java.lang.Integer" >
    select count(*) from amazon_loss_making_orders
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_loss_making_orders
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.lossMaking24hOrder != null" >
        loss_making_24h_order = #{record.lossMaking24hOrder,jdbcType=INTEGER},
      </if>
      <if test="record.isOnline != null" >
        is_online = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.asinStatus != null" >
        asin_status = #{record.asinStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.closeStatus != null" >
        close_status = #{record.closeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.closedTime != null" >
        closed_time = #{record.closedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonLossMakingOrders" >
    update amazon_loss_making_orders
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="lossMaking24hOrder != null" >
        loss_making_24h_order = #{lossMaking24hOrder,jdbcType=INTEGER},
      </if>
      <if test="isOnline != null" >
        is_online = #{isOnline,jdbcType=BIT},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="asinStatus != null" >
        asin_status = #{asinStatus,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="closeStatus != null" >
        close_status = #{closeStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="closedTime != null" >
        closed_time = #{closedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteByCreatedTimeBefore">
    delete from amazon_loss_making_orders where created_time &lt; #{lessThanCreatedTime,jdbcType=TIMESTAMP}
  </delete>
</mapper>