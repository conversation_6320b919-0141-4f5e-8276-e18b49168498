<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonRepublishReportMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonRepublishReport" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="report_solution_type" property="reportSolutionType" jdbcType="VARCHAR" />
    <result column="report_solution_id" property="reportSolutionId" jdbcType="INTEGER" />
    <result column="retry_time" property="retryTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_id, report_solution_type, report_solution_id, retry_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonRepublishReportExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_republish_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_republish_report
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_republish_report
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonRepublishReport" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_republish_report (template_id, report_solution_type, report_solution_id, 
      retry_time)
    values (#{templateId,jdbcType=INTEGER}, #{reportSolutionType,jdbcType=VARCHAR}, #{reportSolutionId,jdbcType=INTEGER}, 
      #{retryTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into amazon_republish_report (template_id, report_solution_type, report_solution_id, retry_time)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.templateId,jdbcType=INTEGER}, #{item.reportSolutionType,jdbcType=VARCHAR}, #{item.reportSolutionId,jdbcType=INTEGER},
      #{item.retryTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonRepublishReportExample" resultType="java.lang.Integer" >
    select count(*) from amazon_republish_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_republish_report
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.reportSolutionType != null" >
        report_solution_type = #{record.reportSolutionType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportSolutionId != null" >
        report_solution_id = #{record.reportSolutionId,jdbcType=INTEGER},
      </if>
      <if test="record.retryTime != null" >
        retry_time = #{record.retryTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonRepublishReport" >
    update amazon_republish_report
    <set >
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="reportSolutionType != null" >
        report_solution_type = #{reportSolutionType,jdbcType=VARCHAR},
      </if>
      <if test="reportSolutionId != null" >
        report_solution_id = #{reportSolutionId,jdbcType=INTEGER},
      </if>
      <if test="retryTime != null" >
        retry_time = #{retryTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteByRetryTime">
    delete from amazon_republish_report
    where  retry_time <![CDATA[ < ]]> #{retryTime,jdbcType=TIMESTAMP}
  </delete>

</mapper>