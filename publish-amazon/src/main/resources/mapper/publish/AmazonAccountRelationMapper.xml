<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonAccountRelationMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonAccountRelation" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="account_country" property="accountCountry" jdbcType="VARCHAR" />
    <result column="relation_account" property="relationAccount" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
    <result column="attribute1" property="attribute1" jdbcType="VARCHAR" />
    <result column="attribute2" property="attribute2" jdbcType="VARCHAR" />
    <result column="attribute3" property="attribute3" jdbcType="VARCHAR" />
    <result column="attribute4" property="attribute4" jdbcType="VARCHAR" />
    <result column="attribute5" property="attribute5" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="logistics_code" property="logisticsCode" jdbcType="VARCHAR" />
    <result column="profit_margin" property="profitMargin" jdbcType="DOUBLE" />
    <result column="promotion_margin" property="promotionMargin" jdbcType="DOUBLE" />
    <result column="title_rule" property="titleRule" jdbcType="VARCHAR" />
    <result column="publish_quantity" property="publishQuantity" jdbcType="INTEGER" />
    <result column="publish_time" property="publishTime" jdbcType="TIME" />
    <result column="publish_interval_time" property="publishIntervalTime" jdbcType="INTEGER" />
    <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP"/>
    <result column="account_status" property="accountStatus" jdbcType="VARCHAR"/>
    <result column="upc_exempt_enable" property="upcExemptEnable" jdbcType="BIT" />
    <result column="category_ids" property="categoryIds" jdbcType="VARCHAR"/>
    <result column="prod_category_codes" property="prodCategoryCodes" jdbcType="VARCHAR"/>
    <result column="prod_category_names" property="prodCategoryNames" jdbcType="VARCHAR"/>
    <result column="auto_publish_new" property="autoPublishNew" jdbcType="BIT" />
    <result column="account_level" property="accountLevel" jdbcType="VARCHAR" />
    <result column="is_complete_config" property="isCompleteConfig" jdbcType="BIT" />
    <result column="first_auto_publish" property="firstAutoPublish" jdbcType="BIT"/>
    <result column="is_distribute_new" property="isDistributeNew" jdbcType="BIT" />
    <result column="auto_off_incomplete_asin" property="autoOffIncompleteAsin" jdbcType="BIT" />
    <result column="online_item_num" property="onlineItemNum" jdbcType="INTEGER"/>
    <result column="exclude_label" property="excludeLabel" jdbcType="VARCHAR" />
    <result column="from_weight" property="fromWeight" jdbcType="DOUBLE" />
    <result column="to_weight" property="toWeight" jdbcType="DOUBLE" />
    <result column="from_price" property="fromPrice" jdbcType="DOUBLE" />
    <result column="to_price" property="toPrice" jdbcType="DOUBLE" />
    <result column="sales_type" property="salesType" jdbcType="INTEGER" />
    <result column="from_sales" property="fromSales" jdbcType="INTEGER" />
    <result column="to_sales" property="toSales" jdbcType="INTEGER" />
    <result column="from_inventory" property="fromInventory" jdbcType="INTEGER" />
    <result column="to_inventory" property="toInventory" jdbcType="INTEGER" />
    <result column="from_input_time" property="fromInputTime" jdbcType="INTEGER" />
    <result column="to_input_time" property="toInputTime" jdbcType="INTEGER" />
    <result column="sale_account_status" property="saleAccountStatus" jdbcType="INTEGER" />
    <result column="exception_status" property="exceptionStatus" jdbcType="VARCHAR" />
    <result column="overseas_business" property="overseasBusiness" jdbcType="BIT" />
    <result column="record_brand" property="recordBrand" jdbcType="VARCHAR" />
    <result column="auth_brand" property="authBrand" jdbcType="VARCHAR" />
    <result column="month_sale_target" property="monthSaleTarget" jdbcType="DOUBLE" />
    <result column="year_sale_target" property="yearSaleTarget" jdbcType="DOUBLE" />
    <result column="month_add_listing_target" property="monthAddListingTarget" jdbcType="INTEGER" />
    <result column="min_publish_mount" property="minPublishMount" jdbcType="INTEGER" />
    <result column="sku_create_time_type" property="skuCreateTimeType" jdbcType="INTEGER" />
    <result column="sku_create_time_year" property="skuCreateTimeYear" jdbcType="INTEGER"/>
    <result column="auto_offline_listing" property="autoOfflineListing" jdbcType="BIT"/>
    <result column="merchant_id" property="merchantId" jdbcType="VARCHAR"/>
    <result column="manufacturer_cn" property="manufacturerCn" jdbcType="VARCHAR"/>
    <result column="manufacturer_en" property="manufacturerEn" jdbcType="VARCHAR"/>
    <result column="manufacturer_address" property="manufacturerAddress" jdbcType="VARCHAR"/>
    <result column="manufacturer_email" property="manufacturerEmail" jdbcType="VARCHAR"/>
    <result column="manufacturer_tel" property="manufacturerTel" jdbcType="VARCHAR"/>
  </resultMap>

  <resultMap id="ConfigResultMap" type="com.estone.erp.publish.amazon.bo.AmazonAccountRelationBO"
             extends="BaseResultMap">
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, account_country, relation_account, remark, creation_date, created_by,
    last_update_date, last_updated_by, attribute1, attribute2, attribute3, attribute4,
    attribute5, brand, manufacturer, quantity, sync_time, account_status,
    logistics_code, profit_margin, promotion_margin, title_rule, publish_quantity, publish_time, publish_interval_time,
    upc_exempt_enable,category_ids, prod_category_codes,prod_category_names, auto_publish_new, first_auto_publish,
    account_level, is_complete_config, online_item_num, is_distribute_new, auto_off_incomplete_asin, exclude_label, from_weight, to_weight,
    from_price, to_price, sales_type, from_sales, to_sales, from_inventory, to_inventory, from_input_time, to_input_time,
    sale_account_status,exception_status,overseas_business,record_brand,auth_brand, month_sale_target, year_sale_target,
    month_add_listing_target,
    min_publish_mount,
    sku_create_time_type,
    sku_create_time_year,
    auto_offline_listing,
    merchant_id,
    manufacturer_cn,
    manufacturer_en,
    manufacturer_address,
    manufacturer_email,
    manufacturer_tel
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_account_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelationExample" >
    select ${filedColumns}
    from amazon_account_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectBoFiledColumnsByExample" resultMap="ConfigResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelationExample" >
    select ${filedColumns}
    from amazon_account_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectRelationBoByExample" resultMap="ConfigResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelationExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_account_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from amazon_account_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_account_relation
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelation" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_account_relation (account_number, account_country, relation_account,
    remark, creation_date, created_by,
    last_update_date, last_updated_by, attribute1,
    attribute2, attribute3, attribute4,
    attribute5, brand, manufacturer,
    quantity, logistics_code, profit_margin,
    promotion_margin, title_rule, publish_quantity,
    publish_time, publish_interval_time, sync_time,
    account_status, upc_exempt_enable, category_ids,
    prod_category_codes, prod_category_names, auto_publish_new,
    account_level, is_complete_config, first_auto_publish,
    is_distribute_new, auto_off_incomplete_asin, online_item_num,
    exclude_label, from_weight, to_weight,
    from_price, to_price, sales_type,
    from_sales, to_sales, from_inventory,
    to_inventory, from_input_time, to_input_time,
    sale_account_status, exception_status, overseas_business,
    record_brand, auth_brand, month_sale_target,
    year_sale_target, month_add_listing_target, min_publish_mount,
    sku_create_time_type, sku_create_time_year, auto_offline_listing,
    merchant_id, manufacturer_cn, manufacturer_en,
    manufacturer_address, manufacturer_email,
    manufacturer_tel)
    values (#{accountNumber,jdbcType=VARCHAR}, #{accountCountry,jdbcType=VARCHAR}, #{relationAccount,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR},
    #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR},
    #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR},
    #{attribute5,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
    #{quantity,jdbcType=INTEGER}, #{logisticsCode,jdbcType=VARCHAR}, #{profitMargin,jdbcType=DOUBLE},
    #{promotionMargin,jdbcType=DOUBLE}, #{titleRule,jdbcType=VARCHAR}, #{publishQuantity,jdbcType=INTEGER},
    #{publishTime,jdbcType=TIME}, #{publishIntervalTime,jdbcType=INTEGER}, #{syncTime,jdbcType=TIMESTAMP},
    #{accountStatus,jdbcType=VARCHAR}, #{upcExemptEnable,jdbcType=BIT}, #{categoryIds,jdbcType=VARCHAR},
    #{prodCategoryCodes,jdbcType=VARCHAR}, #{prodCategoryNames,jdbcType=VARCHAR}, #{autoPublishNew,jdbcType=BIT},
    #{accountLevel,jdbcType=VARCHAR}, #{isCompleteConfig,jdbcType=BIT}, #{firstAutoPublish,jdbcType=BIT},
    #{isDistributeNew,jdbcType=BIT}, #{autoOffIncompleteAsin,jdbcType=BIT}, #{onlineItemNum,jdbcType=INTEGER},
    #{excludeLabel,jdbcType=VARCHAR}, #{fromWeight,jdbcType=DOUBLE}, #{toWeight,jdbcType=DOUBLE},
    #{fromPrice,jdbcType=DOUBLE}, #{toPrice,jdbcType=DOUBLE}, #{salesType,jdbcType=INTEGER},
    #{fromSales,jdbcType=INTEGER}, #{toSales,jdbcType=INTEGER}, #{fromInventory,jdbcType=INTEGER},
    #{toInventory,jdbcType=INTEGER}, #{fromInputTime,jdbcType=INTEGER}, #{toInputTime,jdbcType=INTEGER},
    #{saleAccountStatus,jdbcType=INTEGER}, #{exceptionStatus,jdbcType=VARCHAR}, #{overseasBusiness,jdbcType=BIT},
    #{recordBrand,jdbcType=VARCHAR}, #{authBrand,jdbcType=VARCHAR}, #{monthSaleTarget,jdbcType=DOUBLE},
    #{yearSaleTarget,jdbcType=DOUBLE}, #{monthAddListingTarget,jdbcType=INTEGER}, #{minPublishMount,jdbcType=INTEGER},
    #{skuCreateTimeType,jdbcType=INTEGER}, #{skuCreateTimeYear,jdbcType=INTEGER}, #{autoOfflineListing,jdbcType=BIT},
    #{merchantId,jdbcType=VARCHAR}, #{manufacturerCn,jdbcType=VARCHAR}, #{manufacturerEn,jdbcType=VARCHAR},
    #{manufacturerAddress,jdbcType=VARCHAR}, #{manufacturerEmail,jdbcType=VARCHAR},
    #{manufacturerTel,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelationExample" resultType="java.lang.Integer" >
    select count(*) from amazon_account_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_account_relation
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.accountCountry != null" >
        account_country = #{record.accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.relationAccount != null" >
        relation_account = #{record.relationAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute1 != null" >
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null" >
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null" >
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null" >
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsCode != null">
        logistics_code = #{record.logisticsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.profitMargin != null">
        profit_margin = #{record.profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="record.promotionMargin != null" >
        promotion_margin = #{record.promotionMargin,jdbcType=DOUBLE},
      </if>
      <if test="record.titleRule != null">
        title_rule = #{record.titleRule,jdbcType=VARCHAR},
      </if>
      <if test="record.publishQuantity != null">
        publish_quantity = #{record.publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.publishTime != null">
        publish_time = #{record.publishTime,jdbcType=TIME},
      </if>
      <if test="record.publishIntervalTime != null">
        publish_interval_time = #{record.publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="record.syncTime != null">
        sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountStatus != null">
        account_status = #{record.accountStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.upcExemptEnable != null">
        upc_exempt_enable = #{record.upcExemptEnable,jdbcType=BIT},
      </if>
      <if test="record.categoryIds != null">
        category_ids = #{record.categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="record.prodCategoryCodes != null">
        prod_category_codes = #{record.prodCategoryCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.prodCategoryNames != null">
        prod_category_names = #{record.prodCategoryNames,jdbcType=VARCHAR},
      </if>
      <if test="record.autoPublishNew != null" >
        auto_publish_new = #{record.autoPublishNew,jdbcType=BIT},
      </if>
      <if test="record.accountLevel != null">
        account_level = #{record.accountLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.isCompleteConfig != null" >
        is_complete_config = #{record.isCompleteConfig,jdbcType=BIT},
      </if>
      <if test="record.firstAutoPublish != null">
        first_auto_publish = #{record.firstAutoPublish,jdbcType=BIT},
      </if>
      <if test="record.isDistributeNew != null" >
        is_distribute_new = #{record.isDistributeNew,jdbcType=BIT},
      </if>
      <if test="record.autoOffIncompleteAsin != null" >
        auto_off_incomplete_asin = #{record.autoOffIncompleteAsin,jdbcType=BIT},
      </if>
      <if test="record.onlineItemNum != null">
        online_item_num = #{record.onlineItemNum,jdbcType=INTEGER},
      </if>
      <if test="record.excludeLabel != null" >
        exclude_label = #{record.excludeLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.fromWeight != null" >
        from_weight = #{record.fromWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.toWeight != null" >
        to_weight = #{record.toWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.fromPrice != null" >
        from_price = #{record.fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.toPrice != null" >
        to_price = #{record.toPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.salesType != null" >
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
      <if test="record.fromSales != null" >
        from_sales = #{record.fromSales,jdbcType=INTEGER},
      </if>
      <if test="record.toSales != null" >
        to_sales = #{record.toSales,jdbcType=INTEGER},
      </if>
      <if test="record.fromInventory != null" >
        from_inventory = #{record.fromInventory,jdbcType=INTEGER},
      </if>
      <if test="record.toInventory != null" >
        to_inventory = #{record.toInventory,jdbcType=INTEGER},
      </if>
      <if test="record.fromInputTime != null" >
        from_input_time = #{record.fromInputTime,jdbcType=INTEGER},
      </if>
      <if test="record.toInputTime != null" >
        to_input_time = #{record.toInputTime,jdbcType=INTEGER},
      </if>
      <if test="record.saleAccountStatus != null" >
        sale_account_status = #{record.saleAccountStatus,jdbcType=INTEGER},
      </if>
      <if test="record.exceptionStatus != null" >
        exception_status = #{record.exceptionStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.overseasBusiness != null" >
        overseas_business = #{record.overseasBusiness,jdbcType=BIT},
      </if>
      <if test="record.recordBrand != null" >
        record_brand = #{record.recordBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.authBrand != null" >
        auth_brand = #{record.authBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.monthSaleTarget != null" >
        month_sale_target = #{record.monthSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="record.yearSaleTarget != null" >
        year_sale_target = #{record.yearSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="record.monthAddListingTarget != null" >
        month_add_listing_target = #{record.monthAddListingTarget,jdbcType=INTEGER},
      </if>
      <if test="record.minPublishMount != null" >
        min_publish_mount = #{record.minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="record.skuCreateTimeType != null">
        sku_create_time_type = #{record.skuCreateTimeType,jdbcType=INTEGER},
      </if>
      <if test="record.skuCreateTimeYear != null">
        sku_create_time_year = #{record.skuCreateTimeYear,jdbcType=INTEGER},
      </if>
      <if test="record.autoOfflineListing != null">
        auto_offline_listing = #{record.autoOfflineListing,jdbcType=BIT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerCn != null">
        manufacturer_cn = #{record.manufacturerCn,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerEn != null">
        manufacturer_en = #{record.manufacturerEn,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerAddress != null">
        manufacturer_address = #{record.manufacturerAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerEmail != null">
        manufacturer_email = #{record.manufacturerEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerTel != null">
        manufacturer_tel = #{record.manufacturerTel,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelation" >
    update amazon_account_relation
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountCountry != null" >
        account_country = #{accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="relationAccount != null" >
        relation_account = #{relationAccount,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="logisticsCode != null">
        logistics_code = #{logisticsCode,jdbcType=VARCHAR},
      </if>
      <if test="profitMargin != null">
        profit_margin = #{profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="promotionMargin != null">
        promotion_margin = #{promotionMargin,jdbcType=DOUBLE},
      </if>
      <if test="titleRule != null">
        title_rule = #{titleRule,jdbcType=VARCHAR},
      </if>
      <if test="publishQuantity != null" >
        publish_quantity = #{publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIME},
      </if>
      <if test="publishIntervalTime != null">
        publish_interval_time = #{publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="syncTime != null">
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountStatus != null">
        account_status = #{accountStatus,jdbcType=VARCHAR},
      </if>
      <if test="upcExemptEnable != null" >
        upc_exempt_enable = #{upcExemptEnable,jdbcType=BIT},
      </if>
      <if test="categoryIds != null" >
        category_ids = #{categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="prodCategoryCodes != null" >
        prod_category_codes = #{prodCategoryCodes,jdbcType=VARCHAR},
      </if>
      <if test="prodCategoryNames != null" >
        prod_category_names = #{prodCategoryNames,jdbcType=VARCHAR},
      </if>
      <if test="autoPublishNew != null" >
        auto_publish_new = #{autoPublishNew,jdbcType=BIT},
      </if>
      <if test="accountLevel != null">
        account_level = #{accountLevel,jdbcType=VARCHAR},
      </if>
      <if test="isCompleteConfig != null" >
        is_complete_config = #{isCompleteConfig,jdbcType=BIT},
      </if>
      <if test="firstAutoPublish != null">
        first_auto_publish = #{firstAutoPublish,jdbcType=BIT},
      </if>
      <if test="isDistributeNew != null" >
        is_distribute_new = #{isDistributeNew,jdbcType=BIT},
      </if>
      <if test="autoOffIncompleteAsin != null" >
        auto_off_incomplete_asin = #{autoOffIncompleteAsin,jdbcType=BIT},
      </if>
      <if test="onlineItemNum != null">
        online_item_num = #{onlineItemNum,jdbcType=INTEGER},
      </if>
      <if test="excludeLabel != null" >
        exclude_label = #{excludeLabel,jdbcType=VARCHAR},
      </if>
      <if test="fromWeight != null" >
        from_weight = #{fromWeight,jdbcType=DOUBLE},
      </if>
      <if test="toWeight != null" >
        to_weight = #{toWeight,jdbcType=DOUBLE},
      </if>
      <if test="fromPrice != null" >
        from_price = #{fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="toPrice != null" >
        to_price = #{toPrice,jdbcType=DOUBLE},
      </if>
      <if test="salesType != null" >
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
      <if test="fromSales != null" >
        from_sales = #{fromSales,jdbcType=INTEGER},
      </if>
      <if test="toSales != null" >
        to_sales = #{toSales,jdbcType=INTEGER},
      </if>
      <if test="fromInventory != null" >
        from_inventory = #{fromInventory,jdbcType=INTEGER},
      </if>
      <if test="toInventory != null" >
        to_inventory = #{toInventory,jdbcType=INTEGER},
      </if>
      <if test="fromInputTime != null" >
        from_input_time = #{fromInputTime,jdbcType=INTEGER},
      </if>
      <if test="toInputTime != null" >
        to_input_time = #{toInputTime,jdbcType=INTEGER},
      </if>
      <if test="saleAccountStatus != null" >
        sale_account_status = #{saleAccountStatus,jdbcType=INTEGER},
      </if>
      <if test="exceptionStatus != null" >
        exception_status = #{exceptionStatus,jdbcType=VARCHAR},
      </if>
      <if test="overseasBusiness != null" >
        overseas_business = #{overseasBusiness,jdbcType=BIT},
      </if>
      <if test="recordBrand != null" >
        record_brand = #{recordBrand,jdbcType=VARCHAR},
      </if>
      <if test="authBrand != null" >
        auth_brand = #{authBrand,jdbcType=VARCHAR},
      </if>
      <if test="monthSaleTarget != null" >
        month_sale_target = #{monthSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="yearSaleTarget != null" >
        year_sale_target = #{yearSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="monthAddListingTarget != null" >
        month_add_listing_target = #{monthAddListingTarget,jdbcType=INTEGER},
      </if>
      <if test="minPublishMount != null" >
        min_publish_mount = #{minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="skuCreateTimeType != null">
        sku_create_time_type = #{skuCreateTimeType,jdbcType=INTEGER},
      </if>
      <if test="skuCreateTimeYear != null">
        sku_create_time_year = #{skuCreateTimeYear,jdbcType=INTEGER},
      </if>
      <if test="autoOfflineListing != null">
        auto_offline_listing = #{autoOfflineListing,jdbcType=BIT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerCn != null">
        manufacturer_cn = #{manufacturerCn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerEn != null">
        manufacturer_en = #{manufacturerEn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerAddress != null">
        manufacturer_address = #{manufacturerAddress,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerEmail != null">
        manufacturer_email = #{manufacturerEmail,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerTel != null">
        manufacturer_tel = #{manufacturerTel,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateInfoByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonAccountRelation">
    update amazon_account_relation
    <set>
      relation_account = #{relationAccount,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      attribute1 = #{attribute1,jdbcType=VARCHAR},
      attribute2 = #{attribute2,jdbcType=VARCHAR},
      attribute3 = #{attribute3,jdbcType=VARCHAR},
      attribute4 = #{attribute4,jdbcType=VARCHAR},
      attribute5 = #{attribute5,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      logistics_code = #{logisticsCode,jdbcType=VARCHAR},
      profit_margin = #{profitMargin,jdbcType=DOUBLE},
      promotion_margin = #{promotionMargin,jdbcType=DOUBLE},
      title_rule = #{titleRule,jdbcType=VARCHAR},
      publish_quantity = #{publishQuantity,jdbcType=INTEGER},
      publish_time = #{publishTime,jdbcType=TIME},
      publish_interval_time = #{publishIntervalTime,jdbcType=INTEGER},
      upc_exempt_enable = #{upcExemptEnable,jdbcType=BIT},
      category_ids = #{categoryIds,jdbcType=LONGVARCHAR},
      prod_category_codes = #{prodCategoryCodes,jdbcType=LONGVARCHAR},
      prod_category_names = #{prodCategoryNames,jdbcType=LONGVARCHAR},
      auto_publish_new = #{autoPublishNew,jdbcType=BIT},
      first_auto_publish = #{firstAutoPublish,jdbcType=BIT},
      is_complete_config = #{isCompleteConfig,jdbcType=BIT},
      is_distribute_new = #{isDistributeNew,jdbcType=BIT},
      auto_off_incomplete_asin = #{autoOffIncompleteAsin,jdbcType=BIT},
      exclude_label = #{excludeLabel,jdbcType=VARCHAR},
      from_weight = #{fromWeight,jdbcType=DOUBLE},
      to_weight = #{toWeight,jdbcType=DOUBLE},
      from_price = #{fromPrice,jdbcType=DOUBLE},
      to_price = #{toPrice,jdbcType=DOUBLE},
      sales_type = #{salesType,jdbcType=INTEGER},
      from_sales = #{fromSales,jdbcType=INTEGER},
      to_sales = #{toSales,jdbcType=INTEGER},
      from_inventory = #{fromInventory,jdbcType=INTEGER},
      to_inventory = #{toInventory,jdbcType=INTEGER},
      from_input_time = #{fromInputTime,jdbcType=INTEGER},
      to_input_time = #{toInputTime,jdbcType=INTEGER},
      sale_account_status = #{saleAccountStatus,jdbcType=INTEGER},
      exception_status = #{exceptionStatus,jdbcType=VARCHAR},
      overseas_business = #{overseasBusiness,jdbcType=BIT},
      record_brand = #{recordBrand,jdbcType=VARCHAR},
      auth_brand = #{authBrand,jdbcType=VARCHAR},
      month_sale_target = #{monthSaleTarget,jdbcType=DOUBLE},
      year_sale_target = #{yearSaleTarget,jdbcType=DOUBLE},
      month_add_listing_target = #{monthAddListingTarget,jdbcType=INTEGER},
      min_publish_mount = #{minPublishMount,jdbcType=INTEGER},
      sku_create_time_type = #{skuCreateTimeType,jdbcType=INTEGER},
      sku_create_time_year = #{skuCreateTimeYear,jdbcType=INTEGER},
      auto_offline_listing = #{autoOfflineListing,jdbcType=BIT},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--更新账号信息-->
  <update id="updateAccountInfoByExample">
    <foreach collection="list" item="item" separator=";" close=";">
      update amazon_account_relation
      <set>
        <if test="item.accountCountry != null and item.accountCountry != ''">
          account_country = #{item.accountCountry,jdbcType=VARCHAR},
        </if>
        <if test="item.accountLevel != null and item.accountLevel != ''">
          account_level = #{item.accountLevel,jdbcType=VARCHAR},
        </if>
        <if test="item.saleAccountStatus != null">
          sale_account_status = #{item.saleAccountStatus,jdbcType=INTEGER},
        </if>
        <if test="item.overseasBusiness != null">
          overseas_business = #{item.overseasBusiness,jdbcType=BIT},
        </if>
        merchant_id = #{item.merchantId,jdbcType=VARCHAR},
        record_brand = #{item.recordBrand,jdbcType=VARCHAR},
        auth_brand = #{item.authBrand,jdbcType=VARCHAR},
        exception_status = #{item.exceptionStatus,jdbcType=VARCHAR},
        account_status = #{item.accountStatus,jdbcType=VARCHAR},
        sync_time = now(),
      </set>
      where account_number = #{item.accountNumber,jdbcType=VARCHAR}
    </foreach>
  </update>
  <select id="getAllAccountNumber" resultType="java.lang.String">
    select account_number
    from amazon_account_relation
  </select>

  <!-- 查询配置不全店铺 -->
  <select id="getIncompleteConfAccount" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT account_number,is_complete_config FROM amazon_account_relation
    WHERE account_status = 1 AND
    <foreach collection="list" index="index" item="item" separator="or" open="(" close=")">
      ${item} is NULL
    </foreach>
  </select>
  <select id="getAllEnableAccountNumberOrderByLevel" resultType="java.lang.String">
    select account_number
    from amazon_account_relation
    where account_status = 1
      and sale_account_status = 1
  </select>

  <select id="getAllSite" resultType="java.lang.String">
    select account_country
    from amazon_account_relation
    where account_country is not null
    group by account_country;
  </select>


  <update id="updateRelationAccountByAccount">
    update amazon_account_relation
    <set>
      relation_account = #{relationAccount,jdbcType=VARCHAR}
    </set>
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </update>

  <update id="batchUpdateAccountOnlineItemNum" parameterType="java.util.List">
    <foreach close="" collection="records" index="index" item="item" open="" separator=";">
      update amazon_account_relation set online_item_num = #{item.onlineItemNum,jdbcType=INTEGER} where id =
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="getEnableCatCodeAccountNumbers" resultType="java.lang.String">
    select account_number
    from amazon_account_relation
    where
    account_status = 1 and sale_account_status = 1 and
    account_number in
    <foreach collection="accountNumbers" item="accountNumber" open="(" close=")" separator="," >
      #{accountNumber}
    </foreach>
    and
    <foreach collection="catCodes" index="index" item="item" separator="or" open="(" close=")">
      find_in_set(#{item}, prod_category_codes)
    </foreach>
  </select>

    <update id="batchUpdateAccountGPSRInfo">
        <foreach close="" collection="dataList" index="index" item="item" open="" separator=";">
            update amazon_account_relation set manufacturer_cn = #{item.manufacturerCn}, manufacturer_en =
            #{item.manufacturerEn},
            manufacturer_address =#{item.manufacturerAddress}, manufacturer_email = #{item.manufacturerEmail},
            manufacturer_tel = #{item.manufacturerTel} where account_number = #{item.accountNumber}
        </foreach>
    </update>

    <select id="getAccountManufacturerInfo" resultMap="BaseResultMap">
      select account_number, account_country, merchant_id, manufacturer_cn, manufacturer_en,
        manufacturer_address, manufacturer_email,
        manufacturer_tel from amazon_account_relation where account_number = #{accountNumber};
    </select>

    <select id="getAccountManufacturerInfoGroup" resultMap="BaseResultMap">
        select manufacturer_en, manufacturer_address, manufacturer_email, merchant_id, account_number
        from amazon_account_relation
        where manufacturer_en is not null
        group by manufacturer_en, manufacturer_address, manufacturer_email, merchant_id;
    </select>
</mapper>