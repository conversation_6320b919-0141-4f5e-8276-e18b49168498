<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonListingSonRelationshipTempMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTemp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="order_sale" property="orderSale" jdbcType="INTEGER" />
    <result column="order_30_sale" property="order30Sale" jdbcType="INTEGER" />
    <result column="fba" property="fba" jdbcType="BIT" />
    <result column="item_status" property="itemStatus" jdbcType="VARCHAR" />
    <result column="online" property="online" jdbcType="BIT" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="browse_node_id" property="browseNodeId" jdbcType="VARCHAR" />
    <result column="color_name" property="colorName" jdbcType="VARCHAR" />
    <result column="size_name" property="sizeName" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="model_number" property="modelNumber" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, parent_asin, son_asin, seller_sku, main_sku, article_number, 
    order_sale, order_30_sale, fba, item_status, `online`, item_name, product_type, browse_node_id, 
    color_name, size_name, brand_name, manufacturer, model_number, create_date, update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTempExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_listing_son_relationship_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_listing_son_relationship_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_son_relationship_temp
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTemp" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_son_relationship_temp (account_number, site, parent_asin, 
      son_asin, seller_sku, main_sku, 
      article_number, order_sale, order_30_sale, 
      fba, item_status, `online`, item_name, 
      product_type, browse_node_id, color_name, 
      size_name, brand_name, manufacturer, 
      model_number, create_date, update_date
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, 
      #{sonAsin,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, 
      #{articleNumber,jdbcType=VARCHAR}, #{orderSale,jdbcType=INTEGER}, #{order30Sale,jdbcType=INTEGER}, 
      #{fba,jdbcType=BIT}, #{itemStatus,jdbcType=VARCHAR}, #{online,jdbcType=BIT}, #{itemName,jdbcType=VARCHAR}, 
      #{productType,jdbcType=VARCHAR}, #{browseNodeId,jdbcType=VARCHAR}, #{colorName,jdbcType=VARCHAR}, 
      #{sizeName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{modelNumber,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTempExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_son_relationship_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_son_relationship_temp
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSale != null" >
        order_sale = #{record.orderSale,jdbcType=INTEGER},
      </if>
      <if test="record.order30Sale != null" >
        order_30_sale = #{record.order30Sale,jdbcType=INTEGER},
      </if>
      <if test="record.fba != null" >
        fba = #{record.fba,jdbcType=BIT},
      </if>
      <if test="record.itemStatus != null" >
        item_status = #{record.itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.online != null" >
        `online` = #{record.online,jdbcType=BIT},
      </if>
      <if test="record.itemName != null" >
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNodeId != null" >
        browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.colorName != null" >
        color_name = #{record.colorName,jdbcType=VARCHAR},
      </if>
      <if test="record.sizeName != null" >
        size_name = #{record.sizeName,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null" >
        model_number = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingSonRelationshipTemp" >
    update amazon_listing_son_relationship_temp
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderSale != null" >
        order_sale = #{orderSale,jdbcType=INTEGER},
      </if>
      <if test="order30Sale != null" >
        order_30_sale = #{order30Sale,jdbcType=INTEGER},
      </if>
      <if test="fba != null" >
        fba = #{fba,jdbcType=BIT},
      </if>
      <if test="itemStatus != null" >
        item_status = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="online != null" >
        `online` = #{online,jdbcType=BIT},
      </if>
      <if test="itemName != null" >
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="browseNodeId != null" >
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null" >
        color_name = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="sizeName != null" >
        size_name = #{sizeName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null" >
        model_number = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert <if test="ignore != null">${ignore}</if> into amazon_listing_son_relationship_temp
    (account_number, site, parent_asin,
    son_asin, seller_sku, main_sku,
    article_number, order_sale, order_30_sale,
    fba, item_status, `online`, item_name,
    product_type, browse_node_id, color_name,
    size_name, brand_name, manufacturer,
    model_number, create_date, update_date)
    values
    <foreach collection="list" item="item" separator=",">
    (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR},
    #{item.sonAsin,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.mainSku,jdbcType=VARCHAR},
    #{item.articleNumber,jdbcType=VARCHAR}, #{item.orderSale,jdbcType=INTEGER}, #{item.order30Sale,jdbcType=INTEGER},
    #{item.fba,jdbcType=BIT}, #{item.itemStatus,jdbcType=VARCHAR}, #{item.online,jdbcType=BIT}, #{item.itemName,jdbcType=VARCHAR},
    #{item.productType,jdbcType=VARCHAR}, #{item.browseNodeId,jdbcType=VARCHAR}, #{item.colorName,jdbcType=VARCHAR},
    #{item.sizeName,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
    #{item.modelNumber,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updateDate,jdbcType=TIMESTAMP})
  </foreach>
  </insert>
</mapper>