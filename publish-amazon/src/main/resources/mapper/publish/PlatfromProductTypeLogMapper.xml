<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.PlatfromProductTypeLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.PlatfromProductTypeLog" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="platform_publish_type" property="platformPublishType" jdbcType="VARCHAR" />
    <result column="publish_type" property="publishType" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="sale_variant" property="saleVariant" jdbcType="BIT" />
    <result column="old_publish_type" property="oldPublishType" jdbcType="VARCHAR" />
    <result column="attr_cloumn" property="attrCloumn" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, site, template_id, platform_publish_type, publish_type, main_sku, create_time,
    sale_variant, old_publish_type, attr_cloumn
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.PlatfromProductTypeLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from platfrom_product_type_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from platfrom_product_type_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from platfrom_product_type_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.PlatfromProductTypeLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into platfrom_product_type_log (site, template_id, platform_publish_type, 
      publish_type, main_sku, create_time,
      sale_variant, old_publish_type, attr_cloumn
      )
    values (#{site,jdbcType=VARCHAR}, #{templateId,jdbcType=INTEGER}, #{platformPublishType,jdbcType=VARCHAR}, 
      #{publishType,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{saleVariant,jdbcType=BIT}, #{oldPublishType,jdbcType=VARCHAR}, #{attrCloumn,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.PlatfromProductTypeLogExample" resultType="java.lang.Integer" >
    select count(*) from platfrom_product_type_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update platfrom_product_type_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.platformPublishType != null" >
        platform_publish_type = #{record.platformPublishType,jdbcType=VARCHAR},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleVariant != null" >
        sale_variant = #{record.saleVariant,jdbcType=BIT},
      </if>
      <if test="record.oldPublishType != null" >
        old_publish_type = #{record.oldPublishType,jdbcType=VARCHAR},
      </if>
      <if test="record.attrCloumn != null" >
        attr_cloumn = #{record.attrCloumn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.PlatfromProductTypeLog" >
    update platfrom_product_type_log
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="platformPublishType != null" >
        platform_publish_type = #{platformPublishType,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleVariant != null" >
        sale_variant = #{saleVariant,jdbcType=BIT},
      </if>
      <if test="oldPublishType != null" >
        old_publish_type = #{oldPublishType,jdbcType=VARCHAR},
      </if>
      <if test="attrCloumn != null" >
        attr_cloumn = #{attrCloumn,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>