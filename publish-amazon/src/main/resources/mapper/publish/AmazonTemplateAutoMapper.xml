<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonTemplateAutoMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonTemplateAuto" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="seller_id" property="sellerId" jdbcType="VARCHAR" />
    <result column="country" property="country" jdbcType="VARCHAR" />
    <result column="relative" property="relative" jdbcType="BIT" />
    <result column="category_id" property="categoryId" jdbcType="VARCHAR" />
    <result column="browse_path_by_id" property="browsePathById" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="sale_variant" property="saleVariant" jdbcType="BIT" />
    <result column="parent_SKU" property="parentSku" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="standard_prodcut_id_type" property="standardProdcutIdType" jdbcType="VARCHAR" />
    <result column="standard_prodcut_id_value" property="standardProdcutIdValue" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="mfr_part_number" property="mfrPartNumber" jdbcType="VARCHAR" />
    <result column="condition" property="condition" jdbcType="VARCHAR" />
    <result column="condition_note" property="conditionNote" jdbcType="VARCHAR" />
    <result column="main_image" property="mainImage" jdbcType="VARCHAR" />
    <result column="extra_images" property="extraImages" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="standard_price" property="standardPrice" jdbcType="DOUBLE" />
    <result column="sale_price" property="salePrice" jdbcType="DOUBLE" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="sale_start_date" property="saleStartDate" jdbcType="TIMESTAMP" />
    <result column="sale_end_date" property="saleEndDate" jdbcType="TIMESTAMP" />
    <result column="total_price" jdbcType="DOUBLE" property="totalPrice" />
    <result column="total_sale_price" jdbcType="DOUBLE" property="totalSalePrice" />
    <result column="shipping_cost" jdbcType="DOUBLE" property="shippingCost" />
    <result column="shipping_group" jdbcType="VARCHAR" property="shippingGroup" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="product_tax_code" property="productTaxCode" jdbcType="VARCHAR" />
    <result column="variation_themes" property="variationThemes" jdbcType="VARCHAR" />
    <result column="variations" property="variations" jdbcType="VARCHAR" />
    <result column="bullet_point" property="bulletPoint" jdbcType="VARCHAR" />
    <result column="search_terms" property="searchTerms" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_lock" property="isLock" jdbcType="BIT" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
    <result column="sample_image" property="sampleImage" jdbcType="VARCHAR" />
    <result column="extra_data" property="extraData" jdbcType="VARCHAR" />
    <result column="step_template_status" property="stepTemplateStatus" jdbcType="BIT" />
    <result column="sku_suffix" property="skuSuffix" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="search_data" property="searchData" jdbcType="VARCHAR" />
    <result column="amazon_variant_id" property="amazonVariantId" jdbcType="INTEGER" />
    <result column="is_site_publish" property="isSitePublish" jdbcType="BIT" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
    <result column="data_source" property="dataSource" jdbcType="VARCHAR" />
    <result column="listing_relation_times" property="listingRelationTimes" jdbcType="INTEGER" />
    <result column="single_source" property="singleSource" jdbcType="INTEGER" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="source_template_id" property="sourceTemplateId" jdbcType="INTEGER" />
    <result column="template_status" property="templateStatus" jdbcType="INTEGER" />
    <result column="no_infringement_filter" property="noInfringementFilter" jdbcType="BIT" />
    <result column="category_template_name" property="categoryTemplateName" jdbcType="VARCHAR" />
    <result column="system_category_code_path" property="systemCategoryCodePath" jdbcType="VARCHAR" />
    <result column="produce_attribute_mode" property="produceAttributeMode" jdbcType="VARCHAR" />
    <result column="heat_sensitive" property="heatSensitive" jdbcType="VARCHAR" />
    <result column="heat_sensitive_value" property="heatSensitiveValue" jdbcType="BIT" />
    <result column="interface_type" property="interfaceType" jdbcType="BIT"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, seller_id, country, `relative`, category_id, browse_path_by_id, product_type, 
    sale_variant, parent_SKU, title, standard_prodcut_id_type, standard_prodcut_id_value, 
    brand, manufacturer, mfr_part_number, `condition`, condition_note, main_image, extra_images, 
    description, standard_price, sale_price, currency, sale_start_date, sale_end_date,
    total_price, total_sale_price, shipping_cost, shipping_group,
    quantity, product_tax_code, variation_themes, variations, bullet_point, search_terms, 
    `status`, is_lock, creation_date, created_by, last_update_date, last_updated_by, 
    sample_image, extra_data, step_template_status, sku_suffix, seller_sku, search_data, 
    amazon_variant_id, is_site_publish, publish_status, data_source, listing_relation_times,
    single_source, sku_data_source, publish_type, source_template_id, template_status,no_infringement_filter,
    category_template_name,
    system_category_code_path,
    produce_attribute_mode,
    heat_sensitive,
    heat_sensitive_value,
    interface_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAutoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_template_auto
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectColumnsColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAutoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    ${columns}
    from amazon_template_auto
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_template_auto
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_template_auto
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAuto" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_template_auto (seller_id, country, `relative`, 
      category_id, browse_path_by_id, product_type, 
      sale_variant, parent_SKU, title, 
      standard_prodcut_id_type, standard_prodcut_id_value, 
      brand, manufacturer, mfr_part_number, 
      `condition`, condition_note, main_image, 
      extra_images, description, standard_price, 
      sale_price, currency, sale_start_date, 
      sale_end_date, quantity, product_tax_code,
      total_price, total_sale_price, shipping_cost, shipping_group,
      variation_themes, variations, bullet_point, 
      search_terms, `status`, is_lock, 
      creation_date, created_by, last_update_date, 
      last_updated_by, sample_image, extra_data, 
      step_template_status, sku_suffix, seller_sku, 
      search_data, amazon_variant_id, is_site_publish, 
      publish_status, data_source,
      listing_relation_times, single_source, sku_data_source, 
      publish_type, source_template_id, template_status,no_infringement_filter,
      category_template_name, system_category_code_path,produce_attribute_mode,
    heat_sensitive,heat_sensitive_value,interface_type
      )
    values (#{sellerId,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{relative,jdbcType=BIT}, 
      #{categoryId,jdbcType=VARCHAR}, #{browsePathById,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR},
      #{saleVariant,jdbcType=BIT}, #{parentSku,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{standardProdcutIdType,jdbcType=VARCHAR}, #{standardProdcutIdValue,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{mfrPartNumber,jdbcType=VARCHAR}, 
      #{condition,jdbcType=VARCHAR}, #{conditionNote,jdbcType=VARCHAR}, #{mainImage,jdbcType=VARCHAR}, 
      #{extraImages,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{standardPrice,jdbcType=DOUBLE}, 
      #{salePrice,jdbcType=DOUBLE}, #{currency,jdbcType=VARCHAR}, #{saleStartDate,jdbcType=TIMESTAMP}, 
      #{saleEndDate,jdbcType=TIMESTAMP},#{totalPrice,jdbcType=DOUBLE}, #{totalSalePrice,jdbcType=DOUBLE}, #{shippingCost,jdbcType=DOUBLE},
      #{shippingGroup,jdbcType=VARCHAR},#{quantity,jdbcType=INTEGER}, #{productTaxCode,jdbcType=VARCHAR},
      #{variationThemes,jdbcType=VARCHAR}, #{variations,jdbcType=VARCHAR}, #{bulletPoint,jdbcType=VARCHAR}, 
      #{searchTerms,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{isLock,jdbcType=BIT}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=VARCHAR}, #{sampleImage,jdbcType=VARCHAR}, #{extraData,jdbcType=VARCHAR}, 
      #{stepTemplateStatus,jdbcType=BIT}, #{skuSuffix,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, 
      #{searchData,jdbcType=VARCHAR}, #{amazonVariantId,jdbcType=INTEGER}, #{isSitePublish,jdbcType=BIT}, 
      #{publishStatus,jdbcType=INTEGER}, #{dataSource,jdbcType=VARCHAR},
      #{listingRelationTimes,jdbcType=INTEGER}, #{singleSource,jdbcType=INTEGER}, #{skuDataSource,jdbcType=INTEGER}, 
      #{publishType,jdbcType=INTEGER}, #{sourceTemplateId,jdbcType=INTEGER}, #{templateStatus,jdbcType=INTEGER},
      #{noInfringementFilter,jdbcType=BIT},#{categoryTemplateName,jdbcType=VARCHAR},
      #{systemCategoryCodePath,jdbcType=VARCHAR},#{produceAttributeMode,jdbcType=VARCHAR},
    #{heatSensitive,jdbcType=VARCHAR}, #{heatSensitiveValue,jdbcType=BIT},#{interfaceType,jdbcType=INTEGER})
  </insert>

  <insert id="batchCreateAmazonTemplate" useGeneratedKeys="true" keyProperty="id">
    insert ignore into amazon_template_auto (seller_id, country, `relative`,
    category_id,browse_path_by_id, product_type, sale_variant,
    parent_SKU, title, standard_prodcut_id_type,
    standard_prodcut_id_value, brand, manufacturer,
    mfr_part_number, `condition`, condition_note,
    main_image, standard_price, sale_price,
    currency, sale_start_date, sale_end_date,
    total_price, total_sale_price, shipping_cost, shipping_group,
    quantity, product_tax_code, variation_themes,
    `status`, is_lock, creation_date,
    created_by, last_update_date, last_updated_by,
    sample_image, step_template_status, sku_suffix,
    seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,
    single_source,sku_data_source,publish_type,
    extra_images, description,
    variations, bullet_point, search_terms,
    extra_data, search_data, source_template_id,
    template_status,no_infringement_filter,category_template_name,
    system_category_code_path,produce_attribute_mode,
    heat_sensitive,heat_sensitive_value,interface_type)
    values
    <foreach collection="list" item="template" separator=",">
      (#{template.sellerId,jdbcType=VARCHAR}, #{template.country,jdbcType=VARCHAR}, #{template.relative,jdbcType=BIT},
      #{template.categoryId,jdbcType=VARCHAR},#{template.browsePathById,jdbcType=VARCHAR}, #{template.productType,jdbcType=VARCHAR}, #{template.saleVariant,jdbcType=BIT},
      #{template.parentSku,jdbcType=VARCHAR}, #{template.title,jdbcType=VARCHAR}, #{template.standardProdcutIdType,jdbcType=VARCHAR},
      #{template.standardProdcutIdValue,jdbcType=VARCHAR}, #{template.brand,jdbcType=VARCHAR}, #{template.manufacturer,jdbcType=VARCHAR},
      #{template.mfrPartNumber,jdbcType=VARCHAR}, #{template.condition,jdbcType=VARCHAR}, #{template.conditionNote,jdbcType=VARCHAR},
      #{template.mainImage,jdbcType=VARCHAR}, #{template.standardPrice,jdbcType=DOUBLE}, #{template.salePrice,jdbcType=DOUBLE},
      #{template.currency,jdbcType=VARCHAR}, #{template.saleStartDate,jdbcType=TIMESTAMP}, #{template.saleEndDate,jdbcType=TIMESTAMP},
      #{template.totalPrice,jdbcType=DOUBLE},  #{template.totalSalePrice,jdbcType=DOUBLE},#{template.shippingCost,jdbcType=DOUBLE}, #{template.shippingGroup,jdbcType=VARCHAR},
      #{template.quantity,jdbcType=INTEGER}, #{template.productTaxCode,jdbcType=VARCHAR}, #{template.variationThemes,jdbcType=VARCHAR},
      #{template.status,jdbcType=INTEGER}, #{template.isLock,jdbcType=BIT}, #{template.creationDate,jdbcType=TIMESTAMP},
      #{template.createdBy,jdbcType=VARCHAR}, #{template.lastUpdateDate,jdbcType=TIMESTAMP}, #{template.lastUpdatedBy,jdbcType=VARCHAR},
      #{template.sampleImage,jdbcType=VARCHAR}, #{template.stepTemplateStatus,jdbcType=BIT}, #{template.skuSuffix,jdbcType=VARCHAR},
      #{template.sellerSku,jdbcType=VARCHAR},#{template.amazonVariantId,jdbcType=INTEGER},#{template.isSitePublish,jdbcType=BIT},
      #{template.publishStatus,jdbcType=INTEGER},
      #{template.listingRelationTimes,jdbcType=INTEGER},#{template.singleSource,jdbcType=INTEGER},#{template.skuDataSource,jdbcType=INTEGER},
      #{template.publishType,jdbcType=INTEGER},#{template.extraImages,jdbcType=LONGVARCHAR},#{template.description,jdbcType=LONGVARCHAR},
      #{template.variations,jdbcType=LONGVARCHAR}, #{template.bulletPoint,jdbcType=LONGVARCHAR}, #{template.searchTerms,jdbcType=LONGVARCHAR},
      #{template.extraData,jdbcType=LONGVARCHAR}, #{template.searchData,jdbcType=LONGVARCHAR}, #{template.sourceTemplateId,jdbcType=INTEGER},
      #{template.templateStatus,jdbcType=INTEGER},#{template.noInfringementFilter,jdbcType=BIT},
      #{template.categoryTemplateName,jdbcType=VARCHAR}, #{template.systemCategoryCodePath,jdbcType=VARCHAR},
      #{template.produceAttributeMode,jdbcType=VARCHAR},#{template.heatSensitive,jdbcType=VARCHAR},
      #{template.heatSensitiveValue,jdbcType=BIT},
      #{template.interfaceType,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAutoExample" resultType="java.lang.Integer" >
    select count(*) from amazon_template_auto
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectCustomByWhere" resultType="com.estone.erp.publish.amazon.model.AmazonTemplateAuto">
    select ${columns} from amazon_template_auto
    where id &gt; ${maxId}
    order by id asc
    limit 10000
  </select>
  <select id="getPublishSpuModelPageTotal" resultType="java.lang.Integer"
          parameterType="com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest">
    select count(distinct parent_SKU)
    from amazon_template_auto
    <where>
      <if test="request.site != null">
        and country = #{request.site}
      </if>
      <if test="request.isEnable != null">
        and template_status = #{request.isEnable}
      </if>
      <if test="request.starUpdateTime != null and request.endUpdateTime != null">
        and creation_date between #{request.starUpdateTime} and #{request.endUpdateTime}
      </if>
    </where>
  </select>
  <select id="getPublishSpuModelPage" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest">
    select parent_SKU, group_concat(template_status) extra_data, sku_data_source
    from amazon_template_auto
    <where>
      <if test="request.site != null">
       and country = #{request.site}
      </if>
      <if test="request.isEnable != null">
        and template_status = #{request.isEnable}
      </if>
      <if test="request.starUpdateTime != null and request.endUpdateTime != null">
       and creation_date between #{request.starUpdateTime} and #{request.endUpdateTime}
      </if>
    </where>
    group by parent_SKU limit #{offset}, #{limit};
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_template_auto
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null" >
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.relative != null" >
        `relative` = #{record.relative,jdbcType=BIT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.browsePathById != null" >
        browse_path_by_id = #{record.browsePathById,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.saleVariant != null" >
        sale_variant = #{record.saleVariant,jdbcType=BIT},
      </if>
      <if test="record.parentSku != null" >
        parent_SKU = #{record.parentSku,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProdcutIdType != null" >
        standard_prodcut_id_type = #{record.standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProdcutIdValue != null" >
        standard_prodcut_id_value = #{record.standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.mfrPartNumber != null" >
        mfr_part_number = #{record.mfrPartNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.condition != null" >
        `condition` = #{record.condition,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionNote != null" >
        condition_note = #{record.conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImage != null" >
        main_image = #{record.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.extraImages != null" >
        extra_images = #{record.extraImages,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.standardPrice != null" >
        standard_price = #{record.standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.salePrice != null" >
        sale_price = #{record.salePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.currency != null" >
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.saleStartDate != null" >
        sale_start_date = #{record.saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleEndDate != null" >
        sale_end_date = #{record.saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.totalSalePrice != null">
        total_sale_price = #{record.totalSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingCost != null">
        shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingGroup != null">
        shipping_group = #{record.shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.productTaxCode != null" >
        product_tax_code = #{record.productTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.variationThemes != null" >
        variation_themes = #{record.variationThemes,jdbcType=VARCHAR},
      </if>
      <if test="record.variations != null" >
        variations = #{record.variations,jdbcType=VARCHAR},
      </if>
      <if test="record.bulletPoint != null" >
        bullet_point = #{record.bulletPoint,jdbcType=VARCHAR},
      </if>
      <if test="record.searchTerms != null" >
        search_terms = #{record.searchTerms,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.isLock != null" >
        is_lock = #{record.isLock,jdbcType=BIT},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleImage != null" >
        sample_image = #{record.sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="record.extraData != null" >
        extra_data = #{record.extraData,jdbcType=VARCHAR},
      </if>
      <if test="record.stepTemplateStatus != null" >
        step_template_status = #{record.stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="record.skuSuffix != null" >
        sku_suffix = #{record.skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.searchData != null" >
        search_data = #{record.searchData,jdbcType=VARCHAR},
      </if>
      <if test="record.amazonVariantId != null" >
        amazon_variant_id = #{record.amazonVariantId,jdbcType=INTEGER},
      </if>
      <if test="record.isSitePublish != null" >
        is_site_publish = #{record.isSitePublish,jdbcType=BIT},
      </if>
      <if test="record.publishStatus != null" >
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.dataSource != null" >
        data_source = #{record.dataSource,jdbcType=VARCHAR},
      </if>
      <if test="record.listingRelationTimes != null" >
        listing_relation_times = #{record.listingRelationTimes,jdbcType=INTEGER},
      </if>
      <if test="record.singleSource != null" >
        single_source = #{record.singleSource,jdbcType=INTEGER},
      </if>
      <if test="record.skuDataSource != null" >
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceTemplateId != null" >
        source_template_id = #{record.sourceTemplateId,jdbcType=INTEGER},
      </if>
      <if test="record.templateStatus != null" >
        template_status = #{record.templateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.noInfringementFilter != null" >
        no_infringement_filter = #{record.noInfringementFilter,jdbcType=BIT},
      </if>
      <if test="record.categoryTemplateName != null" >
        category_template_name = #{record.categoryTemplateName,jdbcType=VARCHAR},
      </if>
      <if test="record.systemCategoryCodePath != null" >
        system_category_code_path = #{record.systemCategoryCodePath,jdbcType=VARCHAR},
      </if>
      <if test="record.produceAttributeMode != null" >
        produce_attribute_mode = #{record.produceAttributeMode,jdbcType=VARCHAR},
      </if>
      <if test="record.heatSensitive != null" >
        heat_sensitive = #{record.heatSensitive,jdbcType=VARCHAR},
      </if>
      <if test="record.heatSensitiveValue != null" >
        heat_sensitive_value = #{record.heatSensitiveValue,jdbcType=BIT},
      </if>
      <if test="record.interfaceType != null">
        interface_type = #{record.interfaceType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAuto" >
    update amazon_template_auto
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="relative != null" >
        `relative` = #{relative,jdbcType=BIT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="browsePathById != null" >
        browse_path_by_id = #{browsePathById,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="saleVariant != null" >
        sale_variant = #{saleVariant,jdbcType=BIT},
      </if>
      <if test="parentSku != null" >
        parent_SKU = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdType != null" >
        standard_prodcut_id_type = #{standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdValue != null" >
        standard_prodcut_id_value = #{standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="mfrPartNumber != null" >
        mfr_part_number = #{mfrPartNumber,jdbcType=VARCHAR},
      </if>
      <if test="condition != null" >
        `condition` = #{condition,jdbcType=VARCHAR},
      </if>
      <if test="conditionNote != null" >
        condition_note = #{conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        extra_images = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="standardPrice != null" >
        standard_price = #{standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="salePrice != null" >
        sale_price = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null" >
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="saleStartDate != null" >
        sale_start_date = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null" >
        sale_end_date = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="totalSalePrice != null">
        total_sale_price = #{totalSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="shippingCost != null">
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="shippingGroup != null">
        shipping_group = #{shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="productTaxCode != null" >
        product_tax_code = #{productTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="variationThemes != null" >
        variation_themes = #{variationThemes,jdbcType=VARCHAR},
      </if>
      <if test="variations != null" >
        variations = #{variations,jdbcType=VARCHAR},
      </if>
      <if test="bulletPoint != null" >
        bullet_point = #{bulletPoint,jdbcType=VARCHAR},
      </if>
      <if test="searchTerms != null" >
        search_terms = #{searchTerms,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isLock != null" >
        is_lock = #{isLock,jdbcType=BIT},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null" >
        sample_image = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="extraData != null" >
        extra_data = #{extraData,jdbcType=VARCHAR},
      </if>
      <if test="stepTemplateStatus != null" >
        step_template_status = #{stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="skuSuffix != null" >
        sku_suffix = #{skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="searchData != null" >
        search_data = #{searchData,jdbcType=VARCHAR},
      </if>
      <if test="amazonVariantId != null" >
        amazon_variant_id = #{amazonVariantId,jdbcType=INTEGER},
      </if>
      <if test="isSitePublish != null" >
        is_site_publish = #{isSitePublish,jdbcType=BIT},
      </if>
      <if test="publishStatus != null" >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="dataSource != null" >
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="listingRelationTimes != null" >
        listing_relation_times = #{listingRelationTimes,jdbcType=INTEGER},
      </if>
      <if test="singleSource != null" >
        single_source = #{singleSource,jdbcType=INTEGER},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="sourceTemplateId != null" >
        source_template_id = #{sourceTemplateId,jdbcType=INTEGER},
      </if>
      <if test="templateStatus != null" >
        template_status = #{templateStatus,jdbcType=INTEGER},
      </if>
      <if test="noInfringementFilter != null" >
        no_infringement_filter = #{noInfringementFilter,jdbcType=BIT},
      </if>
      <if test="categoryTemplateName != null" >
        category_template_name = #{categoryTemplateName,jdbcType=VARCHAR},
      </if>
      <if test="systemCategoryCodePath != null" >
        system_category_code_path = #{systemCategoryCodePath,jdbcType=VARCHAR},
      </if>
      <if test="produceAttributeMode != null" >
        produce_attribute_mode = #{produceAttributeMode,jdbcType=VARCHAR},
      </if>
      <if test="heatSensitive != null" >
        heat_sensitive = #{heatSensitive,jdbcType=VARCHAR},
      </if>
      <if test="heatSensitiveValue != null" >
        heat_sensitive_value = #{heatSensitiveValue,jdbcType=BIT},
      </if>
      <if test="interfaceType != null">
        interface_type = #{interfaceType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="batchUpdate">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_template_auto
      <set >
        <if test="record.id != null" >
          id = #{record.id,jdbcType=INTEGER},
        </if>
        <if test="record.sellerId != null" >
          seller_id = #{record.sellerId,jdbcType=VARCHAR},
        </if>
        <if test="record.country != null" >
          country = #{record.country,jdbcType=VARCHAR},
        </if>
        <if test="record.relative != null" >
          `relative` = #{record.relative,jdbcType=BIT},
        </if>
        <if test="record.categoryId != null" >
          category_id = #{record.categoryId,jdbcType=VARCHAR},
        </if>
        <if test="record.browsePathById != null" >
          browse_path_by_id = #{record.browsePathById,jdbcType=VARCHAR},
        </if>
        <if test="record.productType != null" >
          product_type = #{record.productType,jdbcType=VARCHAR},
        </if>
        <if test="record.saleVariant != null" >
          sale_variant = #{record.saleVariant,jdbcType=BIT},
        </if>
        <if test="record.parentSku != null" >
          parent_SKU = #{record.parentSku,jdbcType=VARCHAR},
        </if>
        <if test="record.title != null" >
          title = #{record.title,jdbcType=VARCHAR},
        </if>
        <if test="record.standardProdcutIdType != null" >
          standard_prodcut_id_type = #{record.standardProdcutIdType,jdbcType=VARCHAR},
        </if>
        <if test="record.standardProdcutIdValue != null" >
          standard_prodcut_id_value = #{record.standardProdcutIdValue,jdbcType=VARCHAR},
        </if>
        <if test="record.brand != null" >
          brand = #{record.brand,jdbcType=VARCHAR},
        </if>
        <if test="record.manufacturer != null" >
          manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
        </if>
        <if test="record.mfrPartNumber != null" >
          mfr_part_number = #{record.mfrPartNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.condition != null" >
          `condition` = #{record.condition,jdbcType=VARCHAR},
        </if>
        <if test="record.conditionNote != null" >
          condition_note = #{record.conditionNote,jdbcType=VARCHAR},
        </if>
        <if test="record.mainImage != null" >
          main_image = #{record.mainImage,jdbcType=VARCHAR},
        </if>
        <if test="record.extraImages != null" >
          extra_images = #{record.extraImages,jdbcType=VARCHAR},
        </if>
        <if test="record.description != null" >
          description = #{record.description,jdbcType=VARCHAR},
        </if>
        <if test="record.standardPrice != null" >
          standard_price = #{record.standardPrice,jdbcType=DOUBLE},
        </if>
        <if test="record.salePrice != null" >
          sale_price = #{record.salePrice,jdbcType=DOUBLE},
        </if>
        <if test="record.currency != null" >
          currency = #{record.currency,jdbcType=VARCHAR},
        </if>
        <if test="record.saleStartDate != null" >
          sale_start_date = #{record.saleStartDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.saleEndDate != null" >
          sale_end_date = #{record.saleEndDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.quantity != null" >
          quantity = #{record.quantity,jdbcType=INTEGER},
        </if>
        <if test="record.productTaxCode != null" >
          product_tax_code = #{record.productTaxCode,jdbcType=VARCHAR},
        </if>
        <if test="record.variationThemes != null" >
          variation_themes = #{record.variationThemes,jdbcType=VARCHAR},
        </if>
        <if test="record.variations != null" >
          variations = #{record.variations,jdbcType=VARCHAR},
        </if>
        <if test="record.bulletPoint != null" >
          bullet_point = #{record.bulletPoint,jdbcType=VARCHAR},
        </if>
        <if test="record.searchTerms != null" >
          search_terms = #{record.searchTerms,jdbcType=VARCHAR},
        </if>
        <if test="record.status != null" >
          `status` = #{record.status,jdbcType=INTEGER},
        </if>
        <if test="record.isLock != null" >
          is_lock = #{record.isLock,jdbcType=BIT},
        </if>
        <if test="record.creationDate != null" >
          creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.createdBy != null" >
          created_by = #{record.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="record.lastUpdateDate != null" >
          last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.lastUpdatedBy != null" >
          last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="record.sampleImage != null" >
          sample_image = #{record.sampleImage,jdbcType=VARCHAR},
        </if>
        <if test="record.extraData != null" >
          extra_data = #{record.extraData,jdbcType=VARCHAR},
        </if>
        <if test="record.stepTemplateStatus != null" >
          step_template_status = #{record.stepTemplateStatus,jdbcType=BIT},
        </if>
        <if test="record.skuSuffix != null" >
          sku_suffix = #{record.skuSuffix,jdbcType=VARCHAR},
        </if>
        <if test="record.sellerSku != null" >
          seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
        </if>
        <if test="record.searchData != null" >
          search_data = #{record.searchData,jdbcType=VARCHAR},
        </if>
        <if test="record.amazonVariantId != null" >
          amazon_variant_id = #{record.amazonVariantId,jdbcType=INTEGER},
        </if>
        <if test="record.isSitePublish != null" >
          is_site_publish = #{record.isSitePublish,jdbcType=BIT},
        </if>
        <if test="record.publishStatus != null" >
          publish_status = #{record.publishStatus,jdbcType=INTEGER},
        </if>
        <if test="record.dataSource != null" >
          data_source = #{record.dataSource,jdbcType=VARCHAR},
        </if>
        <if test="record.listingRelationTimes != null" >
          listing_relation_times = #{record.listingRelationTimes,jdbcType=INTEGER},
        </if>
        <if test="record.singleSource != null" >
          single_source = #{record.singleSource,jdbcType=INTEGER},
        </if>
        <if test="record.skuDataSource != null" >
          sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
        </if>
        <if test="record.publishType != null" >
          publish_type = #{record.publishType,jdbcType=INTEGER},
        </if>
        <if test="record.sourceTemplateId != null" >
          source_template_id = #{record.sourceTemplateId,jdbcType=INTEGER},
        </if>
        <if test="record.templateStatus != null" >
          template_status = #{record.templateStatus,jdbcType=INTEGER},
        </if>
        <if test="record.noInfringementFilter != null" >
          no_infringement_filter = #{record.noInfringementFilter,jdbcType=BIT},
        </if>
        <if test="record.categoryTemplateName != null" >
          category_template_name = #{record.categoryTemplateName,jdbcType=VARCHAR},
        </if>
        <if test="record.systemCategoryCodePath != null" >
          system_category_code_path = #{record.systemCategoryCodePath,jdbcType=VARCHAR},
        </if>
        <if test="record.produceAttributeMode != null" >
          produce_attribute_mode = #{record.produceAttributeMode,jdbcType=VARCHAR},
        </if>
        <if test="record.heatSensitive != null" >
          heat_sensitive = #{record.heatSensitive,jdbcType=VARCHAR},
        </if>
        <if test="record.heatSensitiveValue != null" >
          heat_sensitive_value = #{record.heatSensitiveValue,jdbcType=BIT},
        </if>
        <if test="record.interfaceType != null">
          interface_type = #{record.interfaceType,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateCategoryIdByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateAuto" >
    update amazon_template_auto
    <set >
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="batchDisableTemplateByIds">
    update amazon_template_auto
    set template_status = 0,
        last_updated_by  = #{updateUser},
        last_update_date = now()
    where id IN
    <foreach collection="ids" item="id" open="(" close=")" separator="," >
      #{id}
    </foreach>

  </update>
</mapper>