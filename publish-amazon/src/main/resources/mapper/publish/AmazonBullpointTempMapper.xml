<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonBullpointTempMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonBullpointTemp" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="accountNumber" property="accountnumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="sonAsin" property="sonasin" jdbcType="VARCHAR" />
    <result column="sellerSku" property="sellersku" jdbcType="VARCHAR" />
    <result column="isOnline" property="isonline" jdbcType="BIT" />
    <result column="oldItemName" property="olditemname" jdbcType="VARCHAR" />
    <result column="oldSearchTerms" property="oldsearchterms" jdbcType="VARCHAR" />
    <result column="oldBulletPoint" property="oldbulletpoint" jdbcType="VARCHAR" />
    <result column="oldItemDescription" property="olditemdescription" jdbcType="VARCHAR" />
    <result column="newItemName" property="newitemname" jdbcType="VARCHAR" />
    <result column="newSearchTerms" property="newsearchterms" jdbcType="VARCHAR" />
    <result column="newBulletPoint" property="newbulletpoint" jdbcType="VARCHAR" />
    <result column="newItemDescription" property="newitemdescription" jdbcType="VARCHAR" />
    <result column="infringementWord" property="infringementword" jdbcType="VARCHAR" />
    <result column="infringementWordBrand" property="infringementwordbrand" jdbcType="VARCHAR" />
    <result column="errorMsg" property="errormsg" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, accountNumber, site, sonAsin, sellerSku, isOnline, oldItemName, oldSearchTerms, 
    oldBulletPoint, oldItemDescription, newItemName, newSearchTerms, newBulletPoint, 
    newItemDescription, infringementWord, infringementWordBrand, errorMsg
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonBullpointTempExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_bullpoint_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_bullpoint_temp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_bullpoint_temp
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonBullpointTemp" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_bullpoint_temp (accountNumber, site, sonAsin, 
      sellerSku, isOnline, oldItemName, 
      oldSearchTerms, oldBulletPoint, oldItemDescription, 
      newItemName, newSearchTerms, newBulletPoint, 
      newItemDescription, infringementWord, infringementWordBrand, 
      errorMsg)
    values (#{accountnumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{sonasin,jdbcType=VARCHAR}, 
      #{sellersku,jdbcType=VARCHAR}, #{isonline,jdbcType=BIT}, #{olditemname,jdbcType=VARCHAR}, 
      #{oldsearchterms,jdbcType=VARCHAR}, #{oldbulletpoint,jdbcType=VARCHAR}, #{olditemdescription,jdbcType=VARCHAR}, 
      #{newitemname,jdbcType=VARCHAR}, #{newsearchterms,jdbcType=VARCHAR}, #{newbulletpoint,jdbcType=VARCHAR}, 
      #{newitemdescription,jdbcType=VARCHAR}, #{infringementword,jdbcType=VARCHAR}, #{infringementwordbrand,jdbcType=VARCHAR}, 
      #{errormsg,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonBullpointTempExample" resultType="java.lang.Integer" >
    select count(*) from amazon_bullpoint_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_bullpoint_temp
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountnumber != null" >
        accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.sonasin != null" >
        sonAsin = #{record.sonasin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellersku != null" >
        sellerSku = #{record.sellersku,jdbcType=VARCHAR},
      </if>
      <if test="record.isonline != null" >
        isOnline = #{record.isonline,jdbcType=BIT},
      </if>
      <if test="record.olditemname != null" >
        oldItemName = #{record.olditemname,jdbcType=VARCHAR},
      </if>
      <if test="record.oldsearchterms != null" >
        oldSearchTerms = #{record.oldsearchterms,jdbcType=VARCHAR},
      </if>
      <if test="record.oldbulletpoint != null" >
        oldBulletPoint = #{record.oldbulletpoint,jdbcType=VARCHAR},
      </if>
      <if test="record.olditemdescription != null" >
        oldItemDescription = #{record.olditemdescription,jdbcType=VARCHAR},
      </if>
      <if test="record.newitemname != null" >
        newItemName = #{record.newitemname,jdbcType=VARCHAR},
      </if>
      <if test="record.newsearchterms != null" >
        newSearchTerms = #{record.newsearchterms,jdbcType=VARCHAR},
      </if>
      <if test="record.newbulletpoint != null" >
        newBulletPoint = #{record.newbulletpoint,jdbcType=VARCHAR},
      </if>
      <if test="record.newitemdescription != null" >
        newItemDescription = #{record.newitemdescription,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementword != null" >
        infringementWord = #{record.infringementword,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementwordbrand != null" >
        infringementWordBrand = #{record.infringementwordbrand,jdbcType=VARCHAR},
      </if>
      <if test="record.errormsg != null" >
        errorMsg = #{record.errormsg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonBullpointTemp" >
    update amazon_bullpoint_temp
    <set >
      <if test="accountnumber != null" >
        accountNumber = #{accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="sonasin != null" >
        sonAsin = #{sonasin,jdbcType=VARCHAR},
      </if>
      <if test="sellersku != null" >
        sellerSku = #{sellersku,jdbcType=VARCHAR},
      </if>
      <if test="isonline != null" >
        isOnline = #{isonline,jdbcType=BIT},
      </if>
      <if test="olditemname != null" >
        oldItemName = #{olditemname,jdbcType=VARCHAR},
      </if>
      <if test="oldsearchterms != null" >
        oldSearchTerms = #{oldsearchterms,jdbcType=VARCHAR},
      </if>
      <if test="oldbulletpoint != null" >
        oldBulletPoint = #{oldbulletpoint,jdbcType=VARCHAR},
      </if>
      <if test="olditemdescription != null" >
        oldItemDescription = #{olditemdescription,jdbcType=VARCHAR},
      </if>
      <if test="newitemname != null" >
        newItemName = #{newitemname,jdbcType=VARCHAR},
      </if>
      <if test="newsearchterms != null" >
        newSearchTerms = #{newsearchterms,jdbcType=VARCHAR},
      </if>
      <if test="newbulletpoint != null" >
        newBulletPoint = #{newbulletpoint,jdbcType=VARCHAR},
      </if>
      <if test="newitemdescription != null" >
        newItemDescription = #{newitemdescription,jdbcType=VARCHAR},
      </if>
      <if test="infringementword != null" >
        infringementWord = #{infringementword,jdbcType=VARCHAR},
      </if>
      <if test="infringementwordbrand != null" >
        infringementWordBrand = #{infringementwordbrand,jdbcType=VARCHAR},
      </if>
      <if test="errormsg != null" >
        errorMsg = #{errormsg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>