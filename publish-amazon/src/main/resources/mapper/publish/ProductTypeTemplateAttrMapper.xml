<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.ProductTypeTemplateAttrMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttr" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="template_name" property="templateName" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="attribute_name" property="attributeName" jdbcType="VARCHAR" />
    <result column="table_attribute" property="tableAttribute" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="attribute_name_route" property="attributeNameRoute" jdbcType="VARCHAR" />
    <result column="attribute_name_type" property="attributeNameType" jdbcType="VARCHAR" />
    <result column="attribute_data" property="attributeData" jdbcType="VARCHAR" />
    <result column="applicable_attribute_type" property="applicableAttributeType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, site, template_name, product_type, attribute_name, attribute_value, table_attribute,
    create_by, create_time, update_by, update_time, attribute_name_route, attribute_name_type,
    attribute_data, applicable_attribute_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttrExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from product_type_template_attr
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttrExample" >
    select ${columns}
    from product_type_template_attr
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from product_type_template_attr
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from product_type_template_attr
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttr" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_type_template_attr (site, template_name, product_type,
    attribute_name, table_attribute,
    create_by, create_time, update_by,
    update_time, attribute_name_route, attribute_name_type,
    attribute_data, applicable_attribute_type)
    values (#{site,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR},
    #{attributeName,jdbcType=VARCHAR}, #{tableAttribute,jdbcType=VARCHAR},
    #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
    #{updateTime,jdbcType=TIMESTAMP}, #{attributeNameRoute,jdbcType=VARCHAR}, #{attributeNameType,jdbcType=VARCHAR},
    #{attributeData,jdbcType=VARCHAR}, #{applicableAttributeType,jdbcType=INTEGER})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttrExample" resultType="java.lang.Integer" >
    select count(*) from product_type_template_attr
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update product_type_template_attr
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null" >
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeName != null" >
        attribute_name = #{record.attributeName,jdbcType=VARCHAR},
      </if>
      <if test="record.tableAttribute != null" >
        table_attribute = #{record.tableAttribute,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.attributeNameRoute != null" >
        attribute_name_route = #{record.attributeNameRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeNameType != null" >
        attribute_name_type = #{record.attributeNameType,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeData != null">
        attribute_data = #{record.attributeData,jdbcType=VARCHAR},
      </if>
      <if test="record.applicableAttributeType != null" >
        applicable_attribute_type = #{record.applicableAttributeType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.ProductTypeTemplateAttr" >
    update product_type_template_attr
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null" >
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="attributeName != null" >
        attribute_name = #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="tableAttribute != null" >
        table_attribute = #{tableAttribute,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attributeNameRoute != null" >
        attribute_name_route = #{attributeNameRoute,jdbcType=VARCHAR},
      </if>
      <if test="attributeNameType != null" >
        attribute_name_type = #{attributeNameType,jdbcType=VARCHAR},
      </if>
      <if test="attributeData != null">
        attribute_data = #{attributeData,jdbcType=VARCHAR},
      </if>
      <if test="applicableAttributeType != null" >
        applicable_attribute_type = #{applicableAttributeType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>