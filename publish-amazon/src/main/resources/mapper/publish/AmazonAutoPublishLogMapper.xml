<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonAutoPublishLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonAutoPublishLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="account_country" property="accountCountry" jdbcType="VARCHAR" />
    <result column="check_publish_rule" property="checkPublishRule" jdbcType="INTEGER" />
    <result column="publish_spu_queue_mount" property="publishSpuQueueMount" jdbcType="INTEGER" />
    <result column="no_record_spu_mount" property="noRecordSpuMount" jdbcType="INTEGER" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="error_msg" property="errorMsg" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, spu, account_number, account_country, check_publish_rule, publish_spu_queue_mount,
    no_record_spu_mount, publish_type, publish_role, error_msg, create_by, create_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonAutoPublishLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_auto_publish_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from amazon_auto_publish_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_auto_publish_log
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonAutoPublishLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_auto_publish_log (spu, account_number, account_country,
    check_publish_rule, publish_spu_queue_mount,
    no_record_spu_mount, publish_type, publish_role,
    error_msg, create_by, create_date
    )
    values (#{spu,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{accountCountry,jdbcType=VARCHAR},
    #{checkPublishRule,jdbcType=INTEGER}, #{publishSpuQueueMount,jdbcType=INTEGER},
    #{noRecordSpuMount,jdbcType=INTEGER}, #{publishType,jdbcType=INTEGER}, #{publishRole,jdbcType=INTEGER},
    #{errorMsg,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}
    )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonAutoPublishLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_auto_publish_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_auto_publish_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.accountCountry != null" >
        account_country = #{record.accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.checkPublishRule != null" >
        check_publish_rule = #{record.checkPublishRule,jdbcType=INTEGER},
      </if>
      <if test="record.publishSpuQueueMount != null" >
        publish_spu_queue_mount = #{record.publishSpuQueueMount,jdbcType=INTEGER},
      </if>
      <if test="record.noRecordSpuMount != null" >
        no_record_spu_mount = #{record.noRecordSpuMount,jdbcType=INTEGER},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=INTEGER},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.errorMsg != null" >
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonAutoPublishLog" >
    update amazon_auto_publish_log
    <set >
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountCountry != null" >
        account_country = #{accountCountry,jdbcType=VARCHAR},
      </if>
      <if test="checkPublishRule != null" >
        check_publish_rule = #{checkPublishRule,jdbcType=INTEGER},
      </if>
      <if test="publishSpuQueueMount != null" >
        publish_spu_queue_mount = #{publishSpuQueueMount,jdbcType=INTEGER},
      </if>
      <if test="noRecordSpuMount != null" >
        no_record_spu_mount = #{noRecordSpuMount,jdbcType=INTEGER},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null" >
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsertAmazonAutoPublishLogs" parameterType="com.estone.erp.publish.amazon.model.AmazonAutoPublishLog" >
    insert into amazon_auto_publish_log (spu, account_number, account_country, check_publish_rule, publish_spu_queue_mount,
    no_record_spu_mount, publish_type, publish_role, error_msg, create_by, create_date
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.spu,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.accountCountry,jdbcType=VARCHAR},
      #{item.checkPublishRule,jdbcType=INTEGER},#{item.publishSpuQueueMount,jdbcType=INTEGER},#{item.noRecordSpuMount,jdbcType=INTEGER},
      #{item.publishType,jdbcType=INTEGER},#{item.publishRole,jdbcType=INTEGER},
      #{item.errorMsg,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>