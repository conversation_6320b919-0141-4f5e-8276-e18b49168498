<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonReportDownloadProgressMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="accountNumber" jdbcType="VARCHAR" property="accountnumber" />
    <result column="reportType" jdbcType="VARCHAR" property="reporttype" />
    <result column="reportRequestId" jdbcType="VARCHAR" property="reportrequestid" />
    <result column="reportId" jdbcType="VARCHAR" property="reportid" />
    <result column="fileLength" jdbcType="BIGINT" property="filelength" />
    <result column="fileRelativePath" jdbcType="VARCHAR" property="filerelativepath" />
    <result column="fileName" jdbcType="VARCHAR" property="filename" />
    <result column="sha1" jdbcType="VARCHAR" property="sha1" />
    <result column="generationTime" jdbcType="TIMESTAMP" property="generationtime" />
    <result column="success" jdbcType="BIT" property="success" />
    <result column="successParts" jdbcType="VARCHAR" property="successparts" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <result column="fileParts" jdbcType="LONGVARCHAR" property="fileparts" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    id, accountNumber, reportType, reportRequestId, reportId, fileLength, fileRelativePath, 
    fileName, sha1, generationTime, success, successParts
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    fileParts
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgressExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_report_download_progress
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgressExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_report_download_progress
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_report_download_progress
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    delete from amazon_report_download_progress
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgressExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    delete from amazon_report_download_progress
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_report_download_progress (accountNumber, reportType, reportRequestId, 
      reportId, fileLength, fileRelativePath, 
      fileName, sha1, generationTime, 
      success, successParts, fileParts
      )
    values (#{accountnumber,jdbcType=VARCHAR}, #{reporttype,jdbcType=VARCHAR}, #{reportrequestid,jdbcType=VARCHAR}, 
      #{reportid,jdbcType=VARCHAR}, #{filelength,jdbcType=BIGINT}, #{filerelativepath,jdbcType=VARCHAR}, 
      #{filename,jdbcType=VARCHAR}, #{sha1,jdbcType=VARCHAR}, #{generationtime,jdbcType=TIMESTAMP}, 
      #{success,jdbcType=BIT}, #{successparts,jdbcType=VARCHAR}, #{fileparts,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_report_download_progress
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountnumber != null">
        accountNumber,
      </if>
      <if test="reporttype != null">
        reportType,
      </if>
      <if test="reportrequestid != null">
        reportRequestId,
      </if>
      <if test="reportid != null">
        reportId,
      </if>
      <if test="filelength != null">
        fileLength,
      </if>
      <if test="filerelativepath != null">
        fileRelativePath,
      </if>
      <if test="filename != null">
        fileName,
      </if>
      <if test="sha1 != null">
        sha1,
      </if>
      <if test="generationtime != null">
        generationTime,
      </if>
      <if test="success != null">
        success,
      </if>
      <if test="successparts != null">
        successParts,
      </if>
      <if test="fileparts != null">
        fileParts,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountnumber != null">
        #{accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="reporttype != null">
        #{reporttype,jdbcType=VARCHAR},
      </if>
      <if test="reportrequestid != null">
        #{reportrequestid,jdbcType=VARCHAR},
      </if>
      <if test="reportid != null">
        #{reportid,jdbcType=VARCHAR},
      </if>
      <if test="filelength != null">
        #{filelength,jdbcType=BIGINT},
      </if>
      <if test="filerelativepath != null">
        #{filerelativepath,jdbcType=VARCHAR},
      </if>
      <if test="filename != null">
        #{filename,jdbcType=VARCHAR},
      </if>
      <if test="sha1 != null">
        #{sha1,jdbcType=VARCHAR},
      </if>
      <if test="generationtime != null">
        #{generationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="success != null">
        #{success,jdbcType=BIT},
      </if>
      <if test="successparts != null">
        #{successparts,jdbcType=VARCHAR},
      </if>
      <if test="fileparts != null">
        #{fileparts,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgressExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    select count(*) from amazon_report_download_progress
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountnumber != null">
        accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="record.reporttype != null">
        reportType = #{record.reporttype,jdbcType=VARCHAR},
      </if>
      <if test="record.reportrequestid != null">
        reportRequestId = #{record.reportrequestid,jdbcType=VARCHAR},
      </if>
      <if test="record.reportid != null">
        reportId = #{record.reportid,jdbcType=VARCHAR},
      </if>
      <if test="record.filelength != null">
        fileLength = #{record.filelength,jdbcType=BIGINT},
      </if>
      <if test="record.filerelativepath != null">
        fileRelativePath = #{record.filerelativepath,jdbcType=VARCHAR},
      </if>
      <if test="record.filename != null">
        fileName = #{record.filename,jdbcType=VARCHAR},
      </if>
      <if test="record.sha1 != null">
        sha1 = #{record.sha1,jdbcType=VARCHAR},
      </if>
      <if test="record.generationtime != null">
        generationTime = #{record.generationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.success != null">
        success = #{record.success,jdbcType=BIT},
      </if>
      <if test="record.successparts != null">
        successParts = #{record.successparts,jdbcType=VARCHAR},
      </if>
      <if test="record.fileparts != null">
        fileParts = #{record.fileparts,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    set id = #{record.id,jdbcType=BIGINT},
      accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      reportType = #{record.reporttype,jdbcType=VARCHAR},
      reportRequestId = #{record.reportrequestid,jdbcType=VARCHAR},
      reportId = #{record.reportid,jdbcType=VARCHAR},
      fileLength = #{record.filelength,jdbcType=BIGINT},
      fileRelativePath = #{record.filerelativepath,jdbcType=VARCHAR},
      fileName = #{record.filename,jdbcType=VARCHAR},
      sha1 = #{record.sha1,jdbcType=VARCHAR},
      generationTime = #{record.generationtime,jdbcType=TIMESTAMP},
      success = #{record.success,jdbcType=BIT},
      successParts = #{record.successparts,jdbcType=VARCHAR},
      fileParts = #{record.fileparts,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    set id = #{record.id,jdbcType=BIGINT},
      accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      reportType = #{record.reporttype,jdbcType=VARCHAR},
      reportRequestId = #{record.reportrequestid,jdbcType=VARCHAR},
      reportId = #{record.reportid,jdbcType=VARCHAR},
      fileLength = #{record.filelength,jdbcType=BIGINT},
      fileRelativePath = #{record.filerelativepath,jdbcType=VARCHAR},
      fileName = #{record.filename,jdbcType=VARCHAR},
      sha1 = #{record.sha1,jdbcType=VARCHAR},
      generationTime = #{record.generationtime,jdbcType=TIMESTAMP},
      success = #{record.success,jdbcType=BIT},
      successParts = #{record.successparts,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    <set>
      <if test="accountnumber != null">
        accountNumber = #{accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="reporttype != null">
        reportType = #{reporttype,jdbcType=VARCHAR},
      </if>
      <if test="reportrequestid != null">
        reportRequestId = #{reportrequestid,jdbcType=VARCHAR},
      </if>
      <if test="reportid != null">
        reportId = #{reportid,jdbcType=VARCHAR},
      </if>
      <if test="filelength != null">
        fileLength = #{filelength,jdbcType=BIGINT},
      </if>
      <if test="filerelativepath != null">
        fileRelativePath = #{filerelativepath,jdbcType=VARCHAR},
      </if>
      <if test="filename != null">
        fileName = #{filename,jdbcType=VARCHAR},
      </if>
      <if test="sha1 != null">
        sha1 = #{sha1,jdbcType=VARCHAR},
      </if>
      <if test="generationtime != null">
        generationTime = #{generationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="success != null">
        success = #{success,jdbcType=BIT},
      </if>
      <if test="successparts != null">
        successParts = #{successparts,jdbcType=VARCHAR},
      </if>
      <if test="fileparts != null">
        fileParts = #{fileparts,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    set accountNumber = #{accountnumber,jdbcType=VARCHAR},
      reportType = #{reporttype,jdbcType=VARCHAR},
      reportRequestId = #{reportrequestid,jdbcType=VARCHAR},
      reportId = #{reportid,jdbcType=VARCHAR},
      fileLength = #{filelength,jdbcType=BIGINT},
      fileRelativePath = #{filerelativepath,jdbcType=VARCHAR},
      fileName = #{filename,jdbcType=VARCHAR},
      sha1 = #{sha1,jdbcType=VARCHAR},
      generationTime = #{generationtime,jdbcType=TIMESTAMP},
      success = #{success,jdbcType=BIT},
      successParts = #{successparts,jdbcType=VARCHAR},
      fileParts = #{fileparts,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonReportDownloadProgress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:02:01 CST 2019.
    -->
    update amazon_report_download_progress
    set accountNumber = #{accountnumber,jdbcType=VARCHAR},
      reportType = #{reporttype,jdbcType=VARCHAR},
      reportRequestId = #{reportrequestid,jdbcType=VARCHAR},
      reportId = #{reportid,jdbcType=VARCHAR},
      fileLength = #{filelength,jdbcType=BIGINT},
      fileRelativePath = #{filerelativepath,jdbcType=VARCHAR},
      fileName = #{filename,jdbcType=VARCHAR},
      sha1 = #{sha1,jdbcType=VARCHAR},
      generationTime = #{generationtime,jdbcType=TIMESTAMP},
      success = #{success,jdbcType=BIT},
      successParts = #{successparts,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>