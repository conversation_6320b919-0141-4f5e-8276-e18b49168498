<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.SkuBindMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.SkuBind">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    <id column="bind_id" jdbcType="INTEGER" property="bindId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="bind_sku" jdbcType="VARCHAR" property="bindSku" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="system_sku" property="systemSku" jdbcType="VARCHAR" />
    <result column="main_sku" jdbcType="VARCHAR" property="mainSku" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    bind_id, sku, bind_sku, platform, seller_id, create_date, extend,system_sku,main_sku,sku_data_source
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.SkuBindExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_sku_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_sku_bind
    where bind_id = #{bindId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    delete from t_sku_bind
    where bind_id = #{bindId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.SkuBindExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    delete from t_sku_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.SkuBind">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    <selectKey keyProperty="bindId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_sku_bind (sku, bind_sku, platform, 
      seller_id, create_date, extend,system_sku,main_sku,sku_data_source
      )
    values (#{sku,jdbcType=VARCHAR}, #{bindSku,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, 
      #{sellerId,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
      #{systemSku,jdbcType=VARCHAR},#{mainSku,jdbcType=VARCHAR},#{skuDataSource,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.SkuBind">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    <selectKey keyProperty="bindId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_sku_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        sku,
      </if>
      <if test="bindSku != null">
        bind_sku,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="systemSku != null" >
        system_sku,
      </if>
      <if test="mainSku != null" >
        main_sku,
      </if>
      <if test="skuDataSource != null" >
        sku_data_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="bindSku != null">
        #{bindSku,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="systemSku != null" >
        #{systemSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null">
        #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null" >
        #{skuDataSource,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.SkuBindExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    select count(*) from t_sku_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    update t_sku_bind
    <set>
      <if test="record.bindId != null">
        bind_id = #{record.bindId,jdbcType=INTEGER},
      </if>
      <if test="record.sku != null">
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.bindSku != null">
        bind_sku = #{record.bindSku,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerId != null">
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.systemSku != null" >
        system_sku = #{record.systemSku,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null" >
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    update t_sku_bind
    set bind_id = #{record.bindId,jdbcType=INTEGER},
      sku = #{record.sku,jdbcType=VARCHAR},
      bind_sku = #{record.bindSku,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=VARCHAR},
      seller_id = #{record.sellerId,jdbcType=VARCHAR},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      system_sku = #{record.systemSku,jdbcType=VARCHAR},
      main_sku = #{record.mainSku,jdbcType=VARCHAR},
      sku_data_source = #{record.skuDataSource,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.SkuBind">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    update t_sku_bind
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="bindSku != null">
        bind_sku = #{bindSku,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="systemSku != null" >
        system_sku = #{systemSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
    </set>
    where bind_id = #{bindId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.SkuBind">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jul 22 16:30:26 CST 2019.
    -->
    update t_sku_bind
    set sku = #{sku,jdbcType=VARCHAR},
      bind_sku = #{bindSku,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      seller_id = #{sellerId,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      system_sku = #{systemSku,jdbcType=VARCHAR},
      main_sku = #{mainSku,jdbcType=VARCHAR},
      sku_data_source = #{skuDataSource,jdbcType=INTEGER}
    where bind_id = #{bindId,jdbcType=INTEGER}
  </update>

  <insert id="insertSkuBindList" parameterType="java.util.List">
    insert ignore into t_sku_bind (<include refid="Base_Column_List" />) values
    <foreach collection="list" index="index" item="item" separator =",">
      (
      #{item.bindId,jdbcType=BIGINT},
      #{item.sku,jdbcType=VARCHAR},
      #{item.bindSku,jdbcType=VARCHAR},
      #{item.platform,jdbcType=VARCHAR},
      #{item.sellerId,jdbcType=VARCHAR},
      #{item.createDate,jdbcType=TIMESTAMP},
      #{item.extend,jdbcType=VARCHAR},
      #{item.systemSku,jdbcType=VARCHAR},
      #{item.mainSku,jdbcType=VARCHAR},
      #{item.skuDataSource,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateSkuBindList" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  t_sku_bind SET
      <if test="item.skuDataSource != null">
        sku_data_source = #{item.skuDataSource,jdbcType=BIT},
      </if>
      <if test="item.sku != null and item.sku !='' ">
        sku = #{item.sku,jdbcType=VARCHAR}
      </if>
      <!--<if test="item.sku != null and item.sku !='' ">
          sku = #{item.sku,jdbcType=VARCHAR},
      </if>
      <if test="item.bindSku != null and item.bindSku !='' ">
        bind_sku = #{item.bindSku,jdbcType=VARCHAR},
      </if>
      <if test="item.platform != null and item.platform !='' ">
        platform = #{item.platform,jdbcType=VARCHAR},
      </if>
      <if test="item.sellerId != null and item.sellerId !='' ">
        seller_id = #{item.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="item.extend != null and item.extend !='' ">
        extend = #{item.extend,jdbcType=VARCHAR}
      </if>-->
      where bind_id = #{item.bindId,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="getSkuBindByBindSku" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT bind_id, sku, bind_sku, platform, seller_id, create_date, extend,system_sku,sku_data_source
    FROM t_sku_bind
    WHERE 1 = 1
    and bind_sku = #{bindSku,jdbcType=VARCHAR}
  </select>

  <update id="batchUpdateSpSkuBindList" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  t_sku_bind SET
      system_sku = #{item.systemSku,jdbcType=VARCHAR}
      where sku = #{item.sku,jdbcType=INTEGER}
      and platform = #{item.platform,jdbcType=VARCHAR}
      and sku_data_source = #{record.skuDataSource,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateMainSkuBysku" parameterType="com.estone.erp.publish.amazon.model.SkuBind">
   update t_sku_bind
    set main_sku = #{mainSku,jdbcType=VARCHAR}
    where sku = #{sku,jdbcType=VARCHAR}
    and main_sku is null
  </update>

  <select id="selectSkuListByExample" parameterType="com.estone.erp.publish.amazon.model.SkuBindExample" resultType="java.lang.String">
    select sku from t_sku_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>