<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.CategoryOperationsPublishConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="category_full_path_code" property="categoryFullPathCode" jdbcType="VARCHAR" />
    <result column="category_path_name" property="categoryPathName" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="sale_id" property="saleId" jdbcType="VARCHAR" />
    <result column="publish_number" property="publishNumber" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, parent_id, category_full_path_code, category_path_name, `type`, sale_id, publish_number, 
    `status`, created_time, updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from category_operations_publish_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from category_operations_publish_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from category_operations_publish_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into category_operations_publish_config (parent_id, category_full_path_code, category_path_name, 
      `type`, sale_id, publish_number, 
      `status`, created_time, updated_time
      )
    values (#{parentId,jdbcType=INTEGER}, #{categoryFullPathCode,jdbcType=VARCHAR}, #{categoryPathName,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{saleId,jdbcType=VARCHAR}, #{publishNumber,jdbcType=INTEGER}, 
      #{status,jdbcType=BIT}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfigExample" resultType="java.lang.Integer" >
    select count(*) from category_operations_publish_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    group by parent_id
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update category_operations_publish_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryFullPathCode != null" >
        category_full_path_code = #{record.categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryPathName != null" >
        category_path_name = #{record.categoryPathName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.saleId != null" >
        sale_id = #{record.saleId,jdbcType=VARCHAR},
      </if>
      <if test="record.publishNumber != null" >
        publish_number = #{record.publishNumber,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfig" >
    update category_operations_publish_config
    <set >
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryFullPathCode != null" >
        category_full_path_code = #{categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryPathName != null" >
        category_path_name = #{categoryPathName,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="saleId != null" >
        sale_id = #{saleId,jdbcType=VARCHAR},
      </if>
      <if test="publishNumber != null" >
        publish_number = #{publishNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>