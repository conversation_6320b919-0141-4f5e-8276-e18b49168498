<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonProcessReportMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="feed_type" jdbcType="VARCHAR" property="feedType" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="doc_trans_id" jdbcType="VARCHAR" property="docTransId" />
    <result column="status_code" jdbcType="VARCHAR" property="statusCode" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="data_value" jdbcType="VARCHAR" property="dataValue" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="relation_id" jdbcType="INTEGER" property="relationId" />
    <result column="relation_type" jdbcType="VARCHAR" property="relationType" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="previous_fulfillmentLatency_value" property="previousFulfillmentLatencyValue" jdbcType="VARCHAR" />
    <result column="after_fulfillmentLatency_value" property="afterFulfillmentLatencyValue" jdbcType="VARCHAR" />
    <result column="previous_quantity_value" property="previousQuantityValue" jdbcType="VARCHAR" />
    <result column="after_quantity_value" property="afterQuantityValue" jdbcType="VARCHAR" />
    <result column="previous_price_value" property="previousPriceValue" jdbcType="VARCHAR" />
    <result column="after_price_value" property="afterPriceValue" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <result column="result_msg" jdbcType="LONGVARCHAR" property="resultMsg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    id, feed_type, account_number, doc_trans_id, status_code, data_type, data_value, 
    status, creation_date, finish_date, created_by, relation_id, relation_type, task_id,previous_fulfillmentLatency_value,
    after_fulfillmentLatency_value,previous_quantity_value,after_quantity_value,previous_price_value,after_price_value
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    result_msg
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <!-- 查询自定义字段 -->
  <select id="selectFiledColumnsByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultMap="ResultMapWithBLOBs">
    select ${filedColumns}
    from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_process_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    delete from amazon_process_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    delete from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <!--删除超过7天的数据（类型为下架产品的保留近两个月的数据）-->
  <delete id="deleteCreateDateExpired">
    delete report.* from amazon_process_report report
    where (creation_date &lt;= date_sub(NOW(), interval 7 DAY ) AND feed_type != 'DELETET_LISTINGS_DATA')
    OR (creation_date &lt;= date_sub(NOW(), interval 2 MONTH ) AND feed_type = 'DELETET_LISTINGS_DATA')
  </delete>

    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_process_report (feed_type, account_number, doc_trans_id, 
      status_code, data_type, data_value, 
      status, creation_date, finish_date, 
      created_by, relation_id, relation_type, 
      task_id, result_msg,previous_fulfillmentLatency_value,
      after_fulfillmentLatency_value,previous_quantity_value,after_quantity_value,previous_price_value,after_price_value)
    values (#{feedType,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{docTransId,jdbcType=VARCHAR}, 
      #{statusCode,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR}, #{dataValue,jdbcType=VARCHAR}, 
      #{status,jdbcType=BIT}, #{creationDate,jdbcType=TIMESTAMP}, #{finishDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{relationId,jdbcType=INTEGER}, #{relationType,jdbcType=VARCHAR}, 
      #{taskId,jdbcType=VARCHAR}, #{resultMsg,jdbcType=LONGVARCHAR}, #{previousFulfillmentLatencyValue,jdbcType=VARCHAR},
      #{afterFulfillmentLatencyValue,jdbcType=VARCHAR}, #{previousQuantityValue,jdbcType=VARCHAR},
      #{afterQuantityValue,jdbcType=VARCHAR}, #{previousPriceValue,jdbcType=VARCHAR}, #{afterPriceValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_process_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="feedType != null">
        feed_type,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="docTransId != null">
        doc_trans_id,
      </if>
      <if test="statusCode != null">
        status_code,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="dataValue != null">
        data_value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="finishDate != null">
        finish_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="resultMsg != null">
        result_msg,
      </if>
      <if test="previousFulfillmentLatencyValue != null">
        previous_fulfillmentLatency_value,
      </if>
      <if test="afterFulfillmentLatencyValue != null">
        after_fulfillmentLatency_value,
      </if>
      <if test="previousQuantityValue != null">
        previous_quantity_value,
      </if>
      <if test="afterQuantityValue != null">
        after_quantity_value,
      </if>
      <if test="previousPriceValue != null">
        previous_price_value,
      </if>
      <if test="afterPriceValue != null">
        after_price_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="feedType != null">
        #{feedType,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="docTransId != null">
        #{docTransId,jdbcType=VARCHAR},
      </if>
      <if test="statusCode != null">
        #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="dataValue != null">
        #{dataValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null">
        #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="resultMsg != null">
        #{resultMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="previousFulfillmentLatencyValue != null">
        #{previousFulfillmentLatencyValue,jdbcType=VARCHAR},
      </if>
      <if test="afterFulfillmentLatencyValue != null">
        #{afterFulfillmentLatencyValue,jdbcType=VARCHAR},
      </if>
      <if test="previousQuantityValue != null">
        #{previousQuantityValue,jdbcType=VARCHAR},
      </if>
      <if test="afterQuantityValue != null">
        #{afterQuantityValue,jdbcType=VARCHAR},
      </if>
      <if test="previousPriceValue != null">
        #{previousPriceValue,jdbcType=VARCHAR},
      </if>
      <if test="afterPriceValue != null">
        #{afterPriceValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    select count(*) from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.feedType != null">
        feed_type = #{record.feedType,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.docTransId != null">
        doc_trans_id = #{record.docTransId,jdbcType=VARCHAR},
      </if>
      <if test="record.statusCode != null">
        status_code = #{record.statusCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataValue != null">
        data_value = #{record.dataValue,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finishDate != null">
        finish_date = #{record.finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.relationId != null">
        relation_id = #{record.relationId,jdbcType=INTEGER},
      </if>
      <if test="record.relationType != null">
        relation_type = #{record.relationType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=VARCHAR},
      </if>
      <if test="record.resultMsg != null">
        result_msg = #{record.resultMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    set id = #{record.id,jdbcType=BIGINT},
      feed_type = #{record.feedType,jdbcType=VARCHAR},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      doc_trans_id = #{record.docTransId,jdbcType=VARCHAR},
      status_code = #{record.statusCode,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      data_value = #{record.dataValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      finish_date = #{record.finishDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      relation_id = #{record.relationId,jdbcType=INTEGER},
      relation_type = #{record.relationType,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=VARCHAR},
      result_msg = #{record.resultMsg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    set id = #{record.id,jdbcType=BIGINT},
      feed_type = #{record.feedType,jdbcType=VARCHAR},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      doc_trans_id = #{record.docTransId,jdbcType=VARCHAR},
      status_code = #{record.statusCode,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      data_value = #{record.dataValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      finish_date = #{record.finishDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      relation_id = #{record.relationId,jdbcType=INTEGER},
      relation_type = #{record.relationType,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    <set>
      <if test="feedType != null">
        feed_type = #{feedType,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="docTransId != null">
        doc_trans_id = #{docTransId,jdbcType=VARCHAR},
      </if>
      <if test="statusCode != null">
        status_code = #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="dataValue != null">
        data_value = #{dataValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null">
        finish_date = #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="resultMsg != null">
        result_msg = #{resultMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="afterQuantityValue != null">
        after_quantity_value = #{afterQuantityValue,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    set feed_type = #{feedType,jdbcType=VARCHAR},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      doc_trans_id = #{docTransId,jdbcType=VARCHAR},
      status_code = #{statusCode,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      data_value = #{dataValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      finish_date = #{finishDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      relation_id = #{relationId,jdbcType=INTEGER},
      relation_type = #{relationType,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      result_msg = #{resultMsg,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:03:49 CST 2019.
    -->
    update amazon_process_report
    set feed_type = #{feedType,jdbcType=VARCHAR},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      doc_trans_id = #{docTransId,jdbcType=VARCHAR},
      status_code = #{statusCode,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      data_value = #{dataValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      finish_date = #{finishDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      relation_id = #{relationId,jdbcType=INTEGER},
      relation_type = #{relationType,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectProcessReportByTemplateId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select * from amazon_process_report
    where  relation_id = #{relationId,jdbcType=INTEGER}
    and creation_date <![CDATA[ >= ]]>( SELECT max( creation_date ) from amazon_process_report
    WHERE relation_id = #{relationId,jdbcType=INTEGER} AND feed_type = 'POST_PRODUCT_DATA'
         GROUP BY relation_id)
    order by creation_date desc
  </select>

  <select id="selectProcessReportByFollowSellId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select * from amazon_process_report
    where  relation_id = #{relationId,jdbcType=INTEGER}
    and relation_type = 'Follow_Sell'
    and creation_date <![CDATA[ >= ]]>( SELECT max( creation_date ) from amazon_process_report
         WHERE relation_id = #{relationId,jdbcType=INTEGER} AND feed_type = 'POST_PRODUCT_DATA' and relation_type = 'Follow_Sell'
         GROUP BY relation_id)
    order by creation_date desc
  </select>

  <update id="updateProcessReportTimeoutFail" parameterType="map">
    UPDATE  amazon_process_report SET
      status_code = #{record.statusCode,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      finish_date = #{record.finishDate,jdbcType=TIMESTAMP},
      result_msg = #{record.resultMsg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>

  <update id="batchUpdateProcessReportList" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      update amazon_process_report set
      <if test="item.docTransId != null">
        doc_trans_id = #{item.docTransId,jdbcType=VARCHAR},
      </if>
      <if test="item.statusCode != null">
        status_code = #{item.statusCode,jdbcType=VARCHAR},
      </if>
      <if test="item.status != null">
        status = #{item.status,jdbcType=BIT},
      </if>
      <if test="item.finishDate != null">
        finish_date = #{item.finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.resultMsg != null">
        result_msg = #{item.resultMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.id != null " >
        id = #{item.id,jdbcType=BIGINT}
      </if>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateProcessReportResultMsgByIdList" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      update amazon_process_report set
        result_msg = #{resultMsg,jdbcType=LONGVARCHAR}
      where id = #{item,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="updateAmazonProcessReportResult" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    update amazon_process_report set
    <if test="status != null">
      status = #{status,jdbcType=BIT},
    </if>
    <if test="finishDate != null">
      finish_date = #{finishDate,jdbcType=TIMESTAMP},
    </if>
    <if test="resultMsg != null">
      result_msg = #{resultMsg,jdbcType=LONGVARCHAR},
    </if>
    <if test="statusCode != null">
      status_code = #{statusCode,jdbcType=VARCHAR}
    </if>
    where account_number = #{accountNumber,jdbcType=VARCHAR}
    <if test="dataValue != null and dataValue !=''">
        and data_value = #{dataValue,jdbcType=VARCHAR}
    </if>
    <if test="taskId != null and taskId !=''">
      and task_id = #{taskId,jdbcType=VARCHAR}
    </if>
    <if test="feedType != null and feedType !=''">
      and feed_type = #{feedType,jdbcType=VARCHAR}
    </if>
    and status_code != 'Complete'
  </update>

  <select id="selectRelationIdByCondition" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultType="java.lang.String">
    select DISTINCT(relation_id) from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectDataValueByCondition" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportExample" resultType="java.lang.String">
    select DISTINCT(data_value) from amazon_process_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="listGroupConcatStatusTemplate" resultMap="BaseResultMap">
      select relation_id, group_concat(status||status) as "data_value"
      from amazon_process_report
      where relation_id in
      <foreach collection="templateIds" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
        and creation_date > #{startTime}
        and feed_type = 'POST_PRODUCT_DATA'
        and status_code = 'Complete' group by relation_id;
    </select>

    <!-- 记录备份数据 -->
  <insert id="insertBak" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReport">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into process_report_bak (feed_type, account_number, doc_trans_id,
    status_code, data_type, data_value,
    status, creation_date, finish_date,
    created_by, relation_id, relation_type,
    task_id, result_msg)
    values (#{feedType,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{docTransId,jdbcType=VARCHAR},
    #{statusCode,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR}, #{dataValue,jdbcType=VARCHAR},
    #{status,jdbcType=BIT}, #{creationDate,jdbcType=TIMESTAMP}, #{finishDate,jdbcType=TIMESTAMP},
    #{createdBy,jdbcType=VARCHAR}, #{relationId,jdbcType=INTEGER}, #{relationType,jdbcType=VARCHAR},
    #{taskId,jdbcType=VARCHAR}, #{resultMsg,jdbcType=LONGVARCHAR})
  </insert>

  <insert id="batchInsert">
    <foreach collection="list" item="record" separator=";">
      insert into amazon_process_report (feed_type, account_number, doc_trans_id,
      status_code, data_type, data_value,
      status, creation_date, finish_date,
      created_by, relation_id, relation_type,
      task_id, result_msg,previous_fulfillmentLatency_value,
      after_fulfillmentLatency_value,previous_quantity_value,after_quantity_value,previous_price_value,after_price_value)
      values (#{record.feedType,jdbcType=VARCHAR}, #{record.accountNumber,jdbcType=VARCHAR}, #{record.docTransId,jdbcType=VARCHAR},
      #{record.statusCode,jdbcType=VARCHAR}, #{record.dataType,jdbcType=VARCHAR}, #{record.dataValue,jdbcType=VARCHAR},
      #{record.status,jdbcType=BIT}, #{record.creationDate,jdbcType=TIMESTAMP}, #{record.finishDate,jdbcType=TIMESTAMP},
      #{record.createdBy,jdbcType=VARCHAR}, #{record.relationId,jdbcType=INTEGER}, #{record.relationType,jdbcType=VARCHAR},
      #{record.taskId,jdbcType=VARCHAR}, #{record.resultMsg,jdbcType=LONGVARCHAR}, #{record.previousFulfillmentLatencyValue,jdbcType=VARCHAR},
      #{record.afterFulfillmentLatencyValue,jdbcType=VARCHAR}, #{record.previousQuantityValue,jdbcType=VARCHAR},
      #{record.afterQuantityValue,jdbcType=VARCHAR}, #{record.previousPriceValue,jdbcType=VARCHAR}, #{record.afterPriceValue,jdbcType=VARCHAR})
    </foreach>
    </insert>
</mapper>