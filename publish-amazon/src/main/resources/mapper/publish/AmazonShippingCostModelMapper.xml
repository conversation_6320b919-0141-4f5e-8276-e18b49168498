<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonShippingCostModelMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonShippingCostModel" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="shipping_group" property="shippingGroup" jdbcType="VARCHAR" />
    <result column="shipping_cost" property="shippingCost" jdbcType="DOUBLE" />
    <result column="is_default" property="isDefault" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, shipping_group, shipping_cost, is_default, create_by, create_date,
    last_update_by, last_update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonShippingCostModelExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_shipping_cost_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_shipping_cost_model
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_shipping_cost_model
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonShippingCostModel" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_shipping_cost_model (account_number, shipping_group, shipping_cost, 
      is_default, create_by, create_date, 
      last_update_by, last_update_date)
    values (#{accountNumber,jdbcType=VARCHAR}, #{shippingGroup,jdbcType=VARCHAR}, #{shippingCost,jdbcType=DOUBLE}, 
      #{isDefault,jdbcType=BIT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{lastUpdateBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP})
  </insert>

  <!-- 批量插入 -->
  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into amazon_shipping_cost_model (account_number, shipping_group, shipping_cost,
    is_default, create_by, create_date,
    last_update_by, last_update_date)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.shippingGroup,jdbcType=VARCHAR}, #{item.shippingCost,jdbcType=DOUBLE},
      #{item.isDefault,jdbcType=BIT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
      #{item.lastUpdateBy,jdbcType=VARCHAR}, #{item.lastUpdateDate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonShippingCostModelExample" resultType="java.lang.Integer" >
    select count(*) from amazon_shipping_cost_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_shipping_cost_model
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.shippingGroup != null" >
        shipping_group = #{record.shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.shippingCost != null" >
        shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.isDefault != null" >
        is_default = #{record.isDefault,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonShippingCostModel" >
    update amazon_shipping_cost_model
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="shippingGroup != null" >
        shipping_group = #{shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null" >
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="isDefault != null" >
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 更新 -->
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonShippingCostModel">
    update amazon_shipping_cost_model set
    account_number = #{accountNumber,jdbcType=VARCHAR},
    shipping_group = #{shippingGroup,jdbcType=VARCHAR},
    shipping_cost = #{shippingCost,jdbcType=DOUBLE},
    is_default = #{isDefault,jdbcType=BIT},
    create_by = #{createBy,jdbcType=VARCHAR},
    create_date = #{createDate,jdbcType=TIMESTAMP},
    last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
    last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>