<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonListingInfringTempNewMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNew" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="accountNumber" property="accountnumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="parentAsin" property="parentasin" jdbcType="VARCHAR" />
    <result column="sonAsin" property="sonasin" jdbcType="VARCHAR" />
    <result column="sellerSku" property="sellersku" jdbcType="VARCHAR" />
    <result column="articleNumber" property="articlenumber" jdbcType="VARCHAR" />
    <result column="categoryId" property="categoryid" jdbcType="INTEGER" />
    <result column="itemName" property="itemname" jdbcType="VARCHAR" />
    <result column="itemDescription" property="itemdescription" jdbcType="VARCHAR" />
    <result column="brandName" property="brandname" jdbcType="VARCHAR" />
    <result column="orderNumTotal" property="ordernumtotal" jdbcType="INTEGER" />
    <result column="order30dNumTotal" property="order30dnumtotal" jdbcType="INTEGER" />
    <result column="infringementWord" property="infringementword" jdbcType="VARCHAR" />
    <result column="updateDate" property="updatedate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, accountNumber, site, parentAsin, sonAsin, sellerSku, articleNumber, categoryId, 
    itemName, itemDescription, brandName, orderNumTotal, order30dNumTotal, infringementWord, 
    updateDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNewExample" >
    select
    <!--<if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />-->
    id, accountNumber, site, parentAsin, sonAsin, sellerSku, articleNumber
    from amazon_listing_infring_temp_new_msg
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_listing_infring_temp_new
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_infring_temp_new
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNew" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_infring_temp_new (accountNumber, site, parentAsin, 
      sonAsin, sellerSku, articleNumber, 
      categoryId, itemName, itemDescription, 
      brandName, orderNumTotal, order30dNumTotal, 
      infringementWord, updateDate)
    values (#{accountnumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentasin,jdbcType=VARCHAR}, 
      #{sonasin,jdbcType=VARCHAR}, #{sellersku,jdbcType=VARCHAR}, #{articlenumber,jdbcType=VARCHAR}, 
      #{categoryid,jdbcType=INTEGER}, #{itemname,jdbcType=VARCHAR}, #{itemdescription,jdbcType=VARCHAR}, 
      #{brandname,jdbcType=VARCHAR}, #{ordernumtotal,jdbcType=INTEGER}, #{order30dnumtotal,jdbcType=INTEGER}, 
      #{infringementword,jdbcType=VARCHAR}, #{updatedate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNewExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_infring_temp_new_msg
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_infring_temp_new
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountnumber != null" >
        accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.parentasin != null" >
        parentAsin = #{record.parentasin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonasin != null" >
        sonAsin = #{record.sonasin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellersku != null" >
        sellerSku = #{record.sellersku,jdbcType=VARCHAR},
      </if>
      <if test="record.articlenumber != null" >
        articleNumber = #{record.articlenumber,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryid != null" >
        categoryId = #{record.categoryid,jdbcType=INTEGER},
      </if>
      <if test="record.itemname != null" >
        itemName = #{record.itemname,jdbcType=VARCHAR},
      </if>
      <if test="record.itemdescription != null" >
        itemDescription = #{record.itemdescription,jdbcType=VARCHAR},
      </if>
      <if test="record.brandname != null" >
        brandName = #{record.brandname,jdbcType=VARCHAR},
      </if>
      <if test="record.ordernumtotal != null" >
        orderNumTotal = #{record.ordernumtotal,jdbcType=INTEGER},
      </if>
      <if test="record.order30dnumtotal != null" >
        order30dNumTotal = #{record.order30dnumtotal,jdbcType=INTEGER},
      </if>
      <if test="record.infringementword != null" >
        infringementWord = #{record.infringementword,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedate != null" >
        updateDate = #{record.updatedate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNew" >
    update amazon_listing_infring_temp_new
    <set >
      <if test="accountnumber != null" >
        accountNumber = #{accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentasin != null" >
        parentAsin = #{parentasin,jdbcType=VARCHAR},
      </if>
      <if test="sonasin != null" >
        sonAsin = #{sonasin,jdbcType=VARCHAR},
      </if>
      <if test="sellersku != null" >
        sellerSku = #{sellersku,jdbcType=VARCHAR},
      </if>
      <if test="articlenumber != null" >
        articleNumber = #{articlenumber,jdbcType=VARCHAR},
      </if>
      <if test="categoryid != null" >
        categoryId = #{categoryid,jdbcType=INTEGER},
      </if>
      <if test="itemname != null" >
        itemName = #{itemname,jdbcType=VARCHAR},
      </if>
      <if test="itemdescription != null" >
        itemDescription = #{itemdescription,jdbcType=VARCHAR},
      </if>
      <if test="brandname != null" >
        brandName = #{brandname,jdbcType=VARCHAR},
      </if>
      <if test="ordernumtotal != null" >
        orderNumTotal = #{ordernumtotal,jdbcType=INTEGER},
      </if>
      <if test="order30dnumtotal != null" >
        order30dNumTotal = #{order30dnumtotal,jdbcType=INTEGER},
      </if>
      <if test="infringementword != null" >
        infringementWord = #{infringementword,jdbcType=VARCHAR},
      </if>
      <if test="updatedate != null" >
        updateDate = #{updatedate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAccountNumberList" resultType="java.lang.String" >
    select DISTINCT(accountNumber) from  amazon_listing_infring_temp_ES3913
  </select>

  <insert id="insertMsg" parameterType="com.estone.erp.publish.amazon.model.AmazonListingInfringTempNew" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_infring_temp_new_msg (accountNumber, site, parentAsin,
    sonAsin, sellerSku, articleNumber,
    categoryId, itemName, itemDescription,
    brandName, orderNumTotal, order30dNumTotal,
    infringementWord, updateDate)
    values (#{accountnumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentasin,jdbcType=VARCHAR},
    #{sonasin,jdbcType=VARCHAR}, #{sellersku,jdbcType=VARCHAR}, #{articlenumber,jdbcType=VARCHAR},
    #{categoryid,jdbcType=INTEGER}, #{itemname,jdbcType=VARCHAR}, #{itemdescription,jdbcType=VARCHAR},
    #{brandname,jdbcType=VARCHAR}, #{ordernumtotal,jdbcType=INTEGER}, #{order30dnumtotal,jdbcType=INTEGER},
    #{infringementword,jdbcType=VARCHAR}, #{updatedate,jdbcType=TIMESTAMP})
  </insert>

  <select id="selectLiLangDaAccountNumberList" resultType="java.lang.String" >
    select DISTINCT(accountNumber) from  amazon_listing_infring_temp_new_msg
  </select>
</mapper>