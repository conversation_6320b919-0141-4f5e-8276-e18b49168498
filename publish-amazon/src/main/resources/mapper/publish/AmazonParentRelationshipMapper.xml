<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonParentRelationshipMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonParentRelationship" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="parent_seller_sku" property="parentSellerSku" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="son_seller_sku" property="sonSellerSku" jdbcType="VARCHAR" />
    <result column="online_status" property="onlineStatus" jdbcType="BIT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, parent_asin, parent_seller_sku, son_asin, son_seller_sku, online_status,
    `status`, created_by, create_date, update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonParentRelationshipExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_parent_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from amazon_parent_relationship
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_parent_relationship
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonParentRelationship" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_parent_relationship (account_number, parent_asin, parent_seller_sku,
      son_asin, son_seller_sku, online_status,
      `status`, created_by, create_date,
      update_date)
    values (#{accountNumber,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, #{parentSellerSku,jdbcType=VARCHAR},
      #{sonAsin,jdbcType=VARCHAR}, #{sonSellerSku,jdbcType=VARCHAR}, #{onlineStatus,jdbcType=BIT},
      #{status,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
      #{updateDate,jdbcType=TIMESTAMP})
  </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonParentRelationshipExample" resultType="java.lang.Integer" >
    select count(*) from amazon_parent_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <update id="updateByExampleSelective" parameterType="map" >
    update amazon_parent_relationship
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.parentSellerSku != null" >
        parent_seller_sku = #{record.parentSellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonSellerSku != null" >
        son_seller_sku = #{record.sonSellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineStatus != null" >
        online_status = #{record.onlineStatus,jdbcType=BIT},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonParentRelationship" >
    update amazon_parent_relationship
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="parentSellerSku != null" >
        parent_seller_sku = #{parentSellerSku,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonSellerSku != null" >
        son_seller_sku = #{sonSellerSku,jdbcType=VARCHAR},
      </if>
      <if test="onlineStatus != null" >
        online_status = #{onlineStatus,jdbcType=BIT},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="batchUpdate">
     <foreach collection="list" item="item" separator=";" >
       update amazon_parent_relationship
       <set>
         <if test="item.accountNumber != null" >
           account_number = #{item.accountNumber,jdbcType=VARCHAR},
         </if>
         <if test="item.parentAsin != null" >
           parent_asin = #{item.parentAsin,jdbcType=VARCHAR},
         </if>
         <if test="item.parentAsin == null" >
           parent_asin = null,
         </if>
         <if test="item.parentSellerSku != null" >
           parent_seller_sku = #{item.parentSellerSku,jdbcType=VARCHAR},
         </if>
         <if test="item.sonAsin != null" >
           son_asin = #{item.sonAsin,jdbcType=VARCHAR},
         </if>
         <if test="item.sonSellerSku != null" >
           son_seller_sku = #{item.sonSellerSku,jdbcType=VARCHAR},
         </if>
         <if test="item.onlineStatus != null" >
           online_status = #{item.onlineStatus,jdbcType=BIT},
         </if>
         <if test="item.status != null" >
           status = #{item.status,jdbcType=INTEGER},
         </if>
         <if test="item.createdBy != null" >
           created_by = #{item.createdBy,jdbcType=VARCHAR},
         </if>
         <if test="item.createDate != null" >
           create_date = #{item.createDate,jdbcType=TIMESTAMP},
         </if>
         <if test="item.updateDate != null" >
           update_date = #{item.updateDate,jdbcType=TIMESTAMP},
         </if>
       </set>
       where id = #{item.id,jdbcType=BIGINT}
     </foreach>
    </update>

  <insert id="batchInsert">
    <foreach collection="list" item="item" separator=";">
      insert into amazon_parent_relationship (account_number, parent_asin, parent_seller_sku,
      son_asin, son_seller_sku, online_status,
      status, created_by, create_date,
      update_date)
     values
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR}, #{item.parentSellerSku,jdbcType=VARCHAR},
       #{item.sonAsin,jdbcType=VARCHAR}, #{item.sonSellerSku,jdbcType=VARCHAR}, #{item.onlineStatus,jdbcType=BIT},
       #{item.status,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
       #{item.updateDate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="selectCustomColumnByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonParentRelationshipExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <if test="columns != null" >
      ${columns}
    </if>
    <if test="columns == null" >
      <include refid="Base_Column_List" />
    </if>
    from amazon_parent_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>