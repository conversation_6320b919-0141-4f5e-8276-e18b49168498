<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonNewRemindCountInfoStatisticsMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatistics" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="sale_leader_man" property="saleLeaderMan" jdbcType="VARCHAR" />
    <result column="sale_supervisor" property="saleSupervisor" jdbcType="VARCHAR" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
    <result column="today_up_rate" property="todayUpRate" jdbcType="DOUBLE" />
    <result column="four_up_rate" property="fourUpRate" jdbcType="DOUBLE" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="editor" property="editor" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, `type`, sale_man, sale_leader_man, sale_supervisor, platform, push_time, today_up_rate, 
    four_up_rate, create_by, create_date, last_update_by, last_update_date, editor
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatisticsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_new_remind_count_info_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_new_remind_count_info_statistics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_new_remind_count_info_statistics
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatistics" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_new_remind_count_info_statistics (`type`, sale_man, sale_leader_man, 
      sale_supervisor, platform, push_time, 
      today_up_rate, four_up_rate, create_by, 
      create_date, last_update_by, last_update_date, editor
      )
    values (#{type,jdbcType=INTEGER}, #{saleMan,jdbcType=VARCHAR}, #{saleLeaderMan,jdbcType=VARCHAR}, 
      #{saleSupervisor,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{pushTime,jdbcType=TIMESTAMP}, 
      #{todayUpRate,jdbcType=DOUBLE}, #{fourUpRate,jdbcType=DOUBLE}, #{createBy,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP},
      #{editor,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatisticsExample" resultType="java.lang.Integer" >
    select count(*) from amazon_new_remind_count_info_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_new_remind_count_info_statistics
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.saleMan != null" >
        sale_man = #{record.saleMan,jdbcType=VARCHAR},
      </if>
      <if test="record.saleLeaderMan != null" >
        sale_leader_man = #{record.saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="record.saleSupervisor != null" >
        sale_supervisor = #{record.saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null" >
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.pushTime != null" >
        push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.todayUpRate != null" >
        today_up_rate = #{record.todayUpRate,jdbcType=DOUBLE},
      </if>
      <if test="record.fourUpRate != null" >
        four_up_rate = #{record.fourUpRate,jdbcType=DOUBLE},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.editor != null" >
        editor = #{record.editor,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatistics" >
    update amazon_new_remind_count_info_statistics
    <set >
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="saleMan != null" >
        sale_man = #{saleMan,jdbcType=VARCHAR},
      </if>
      <if test="saleLeaderMan != null" >
        sale_leader_man = #{saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="saleSupervisor != null" >
        sale_supervisor = #{saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="pushTime != null" >
        push_time = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="todayUpRate != null" >
        today_up_rate = #{todayUpRate,jdbcType=DOUBLE},
      </if>
      <if test="fourUpRate != null" >
        four_up_rate = #{fourUpRate,jdbcType=DOUBLE},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="editor != null" >
        editor = #{editor,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>