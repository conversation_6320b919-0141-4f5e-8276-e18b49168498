<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonXsdProductAttrMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <result column="extra_data" jdbcType="LONGVARCHAR" property="extraData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    id, product_type, last_update_date
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    extra_data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttrExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_xsd_product_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttrExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_xsd_product_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_xsd_product_attr
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    delete from amazon_xsd_product_attr
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttrExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    delete from amazon_xsd_product_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_xsd_product_attr (product_type, last_update_date, extra_data
      )
    values (#{productType,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{extraData,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_xsd_product_attr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productType != null">
        product_type,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="extraData != null">
        extra_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productType != null">
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraData != null">
        #{extraData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttrExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    select count(*) from amazon_xsd_product_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraData != null">
        extra_data = #{record.extraData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    set id = #{record.id,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      extra_data = #{record.extraData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    set id = #{record.id,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    <set>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraData != null">
        extra_data = #{extraData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    set product_type = #{productType,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      extra_data = #{extraData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonXsdProductAttr">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 15:58:59 CST 2019.
    -->
    update amazon_xsd_product_attr
    set product_type = #{productType,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据类型查询业务对象 -->
  <resultMap extends="ResultMapWithBLOBs" id="ResultMapBo" type="com.estone.erp.publish.amazon.bo.AmazonXsdProductAttrBO">
  </resultMap>
  <select id="selectBoByProductType" parameterType="java.lang.String" resultMap="ResultMapBo">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_xsd_product_attr
    where product_type = #{productType,jdbcType=VARCHAR}
  </select>

  <select id="getAllAmazonXsdProductAttrs" resultMap="ResultMapBo">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from amazon_xsd_product_attr
    where 1=1
  </select>
</mapper>