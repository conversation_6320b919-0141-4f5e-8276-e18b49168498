<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonRecommendMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonRecommend" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="VARCHAR" />
    <result column="order_last_7d_count" property="orderLast7dCount" jdbcType="VARCHAR" />
    <result column="order_last_7d_count_day" property="orderLast7dCountDay" jdbcType="VARCHAR" />
    <result column="order_last_14d_count" property="orderLast14dCount" jdbcType="VARCHAR" />
    <result column="order_last_14d_count_day" property="orderLast14dCountDay" jdbcType="VARCHAR" />
    <result column="order_last_30d_count" property="orderLast30dCount" jdbcType="VARCHAR" />
    <result column="order_last_30d_count_day" property="orderLast30dCountDay" jdbcType="VARCHAR" />
    <result column="order_days_within_30d" property="orderDaysWithin30d" jdbcType="VARCHAR" />
    <result column="order_last_7d_30d_count_day" property="orderLast7d30dCountDay" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, site, price, order_last_7d_count, order_last_7d_count_day, order_last_14d_count, 
    order_last_14d_count_day, order_last_30d_count, order_last_30d_count_day, order_days_within_30d, 
    order_last_7d_30d_count_day, `status`, created_by, create_date, updated_by, update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonRecommendExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_recommend
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_recommend
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_recommend
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonRecommend" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_recommend (site, price, order_last_7d_count, 
      order_last_7d_count_day, order_last_14d_count, 
      order_last_14d_count_day, order_last_30d_count, 
      order_last_30d_count_day, order_days_within_30d, 
      order_last_7d_30d_count_day, `status`, created_by, 
      create_date, updated_by, update_date
      )
    values (#{site,jdbcType=VARCHAR}, #{price,jdbcType=VARCHAR}, #{orderLast7dCount,jdbcType=VARCHAR}, 
      #{orderLast7dCountDay,jdbcType=VARCHAR}, #{orderLast14dCount,jdbcType=VARCHAR}, 
      #{orderLast14dCountDay,jdbcType=VARCHAR}, #{orderLast30dCount,jdbcType=VARCHAR}, 
      #{orderLast30dCountDay,jdbcType=VARCHAR}, #{orderDaysWithin30d,jdbcType=VARCHAR}, 
      #{orderLast7d30dCountDay,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonRecommendExample" resultType="java.lang.Integer" >
    select count(*) from amazon_recommend
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_recommend
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast7dCount != null" >
        order_last_7d_count = #{record.orderLast7dCount,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast7dCountDay != null" >
        order_last_7d_count_day = #{record.orderLast7dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast14dCount != null" >
        order_last_14d_count = #{record.orderLast14dCount,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast14dCountDay != null" >
        order_last_14d_count_day = #{record.orderLast14dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast30dCount != null" >
        order_last_30d_count = #{record.orderLast30dCount,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast30dCountDay != null" >
        order_last_30d_count_day = #{record.orderLast30dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDaysWithin30d != null" >
        order_days_within_30d = #{record.orderDaysWithin30d,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast7d30dCountDay != null" >
        order_last_7d_30d_count_day = #{record.orderLast7d30dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonRecommend" >
    update amazon_recommend
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=VARCHAR},
      </if>
      <if test="orderLast7dCount != null" >
        order_last_7d_count = #{orderLast7dCount,jdbcType=VARCHAR},
      </if>
      <if test="orderLast7dCountDay != null" >
        order_last_7d_count_day = #{orderLast7dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="orderLast14dCount != null" >
        order_last_14d_count = #{orderLast14dCount,jdbcType=VARCHAR},
      </if>
      <if test="orderLast14dCountDay != null" >
        order_last_14d_count_day = #{orderLast14dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="orderLast30dCount != null" >
        order_last_30d_count = #{orderLast30dCount,jdbcType=VARCHAR},
      </if>
      <if test="orderLast30dCountDay != null" >
        order_last_30d_count_day = #{orderLast30dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="orderDaysWithin30d != null" >
        order_days_within_30d = #{orderDaysWithin30d,jdbcType=VARCHAR},
      </if>
      <if test="orderLast7d30dCountDay != null" >
        order_last_7d_30d_count_day = #{orderLast7d30dCountDay,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>