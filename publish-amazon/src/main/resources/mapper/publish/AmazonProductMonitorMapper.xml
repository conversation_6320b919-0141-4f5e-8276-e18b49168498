<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonProductMonitorMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonProductMonitor">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purple_bird_name" property="purpleBirdName" jdbcType="VARCHAR"/>
        <result column="account_number" property="accountNumber" jdbcType="VARCHAR"/>
        <result column="sale_id" property="saleId" jdbcType="VARCHAR"/>
        <result column="site" property="site" jdbcType="VARCHAR"/>
        <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR"/>
        <result column="son_asin" property="sonAsin" jdbcType="VARCHAR"/>
        <result column="main_sku" property="mainSku" jdbcType="VARCHAR"/>
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR"/>
        <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR"/>
        <result column="es_listing_id" property="esListingId" jdbcType="VARCHAR"/>
        <result column="monitor_id" property="monitorId" jdbcType="INTEGER"/>
        <result column="monitor_type" property="monitorType" jdbcType="INTEGER"/>
        <result column="monitor_content" property="monitorContent" jdbcType="VARCHAR"/>
        <result column="offline_remark" property="offlineRemark" jdbcType="VARCHAR"/>
        <result column="forbid_channel" property="forbidChannel" jdbcType="VARCHAR"/>
        <result column="infringement_typename" property="infringementTypename" jdbcType="VARCHAR"/>
        <result column="infringement_obj" property="infringementObj" jdbcType="VARCHAR"/>
        <result column="prohibition_site" property="prohibitionSite" jdbcType="VARCHAR"/>
        <result column="order_last_24h_count" property="orderLast24hCount" jdbcType="INTEGER"/>
        <result column="order_last_7d_count" property="orderLast7dCount" jdbcType="INTEGER"/>
        <result column="order_last_14d_count" property="orderLast14dCount" jdbcType="INTEGER"/>
        <result column="order_last_30d_count" property="orderLast30dCount" jdbcType="INTEGER"/>
        <result column="order_num_total" property="orderNumTotal" jdbcType="INTEGER"/>
        <result column="open_date" property="openDate" jdbcType="TIMESTAMP"/>
        <result column="offline_date" property="offlineDate" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , purple_bird_name, account_number, sale_id, site, parent_asin, son_asin, main_sku,
    article_number, seller_sku, es_listing_id, monitor_id, monitor_type, monitor_content,
    offline_remark, forbid_channel, infringement_typename, infringement_obj, prohibition_site,
    order_last_24h_count, order_last_7d_count, order_last_14d_count, order_last_30d_count,
    order_num_total, open_date, offline_date, create_time
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.amazon.model.AmazonProductMonitorExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <choose>
            <when test="columns != null and columns != ''">
                ${columns}
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from amazon_product_monitor
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from amazon_product_monitor
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey">
        delete from amazon_product_monitor
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonProductMonitor">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_product_monitor (purple_bird_name, account_number, sale_id,
        site, parent_asin, son_asin,
        main_sku, article_number, seller_sku,
        es_listing_id, monitor_id, monitor_type,
        monitor_content, offline_remark, forbid_channel,
        infringement_typename, infringement_obj,
        prohibition_site, order_last_24h_count, order_last_7d_count,
        order_last_14d_count, order_last_30d_count, order_num_total,
        open_date, offline_date, create_time
        )
        values (#{purpleBirdName,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{saleId,jdbcType=VARCHAR},
        #{site,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, #{sonAsin,jdbcType=VARCHAR},
        #{mainSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR},
        #{esListingId,jdbcType=VARCHAR}, #{monitorId,jdbcType=INTEGER}, #{monitorType,jdbcType=INTEGER},
        #{monitorContent,jdbcType=VARCHAR}, #{offlineRemark,jdbcType=VARCHAR}, #{forbidChannel,jdbcType=VARCHAR},
        #{infringementTypename,jdbcType=VARCHAR}, #{infringementObj,jdbcType=VARCHAR},
        #{prohibitionSite,jdbcType=VARCHAR}, #{orderLast24hCount,jdbcType=INTEGER},
        #{orderLast7dCount,jdbcType=INTEGER},
        #{orderLast14dCount,jdbcType=INTEGER}, #{orderLast30dCount,jdbcType=INTEGER}, #{orderNumTotal,jdbcType=INTEGER},
        #{openDate,jdbcType=TIMESTAMP}, #{offlineDate,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProductMonitorExample"
            resultType="java.lang.Integer">
        select count(*) from amazon_product_monitor
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update amazon_product_monitor
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.purpleBirdName != null">
                purple_bird_name = #{record.purpleBirdName,jdbcType=VARCHAR},
            </if>
            <if test="record.accountNumber != null">
                account_number = #{record.accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.saleId != null">
                sale_id = #{record.saleId,jdbcType=VARCHAR},
            </if>
            <if test="record.site != null">
                site = #{record.site,jdbcType=VARCHAR},
            </if>
            <if test="record.parentAsin != null">
                parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
            </if>
            <if test="record.sonAsin != null">
                son_asin = #{record.sonAsin,jdbcType=VARCHAR},
            </if>
            <if test="record.mainSku != null">
                main_sku = #{record.mainSku,jdbcType=VARCHAR},
            </if>
            <if test="record.articleNumber != null">
                article_number = #{record.articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.sellerSku != null">
                seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
            </if>
            <if test="record.esListingId != null">
                es_listing_id = #{record.esListingId,jdbcType=VARCHAR},
            </if>
            <if test="record.monitorId != null">
                monitor_id = #{record.monitorId,jdbcType=INTEGER},
            </if>
            <if test="record.monitorType != null">
                monitor_type = #{record.monitorType,jdbcType=INTEGER},
            </if>
            <if test="record.monitorContent != null">
                monitor_content = #{record.monitorContent,jdbcType=VARCHAR},
            </if>
            <if test="record.offlineRemark != null">
                offline_remark = #{record.offlineRemark,jdbcType=VARCHAR},
            </if>
            <if test="record.forbidChannel != null">
                forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
            </if>
            <if test="record.infringementTypename != null">
                infringement_typename = #{record.infringementTypename,jdbcType=VARCHAR},
            </if>
            <if test="record.infringementObj != null">
                infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
            </if>
            <if test="record.prohibitionSite != null">
                prohibition_site = #{record.prohibitionSite,jdbcType=VARCHAR},
            </if>
            <if test="record.orderLast24hCount != null">
                order_last_24h_count = #{record.orderLast24hCount,jdbcType=INTEGER},
            </if>
            <if test="record.orderLast7dCount != null">
                order_last_7d_count = #{record.orderLast7dCount,jdbcType=INTEGER},
            </if>
            <if test="record.orderLast14dCount != null">
                order_last_14d_count = #{record.orderLast14dCount,jdbcType=INTEGER},
            </if>
            <if test="record.orderLast30dCount != null">
                order_last_30d_count = #{record.orderLast30dCount,jdbcType=INTEGER},
            </if>
            <if test="record.orderNumTotal != null">
                order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
            </if>
            <if test="record.openDate != null">
                open_date = #{record.openDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.offlineDate != null">
                offline_date = #{record.offlineDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProductMonitor">
        update amazon_product_monitor
        <set>
            <if test="purpleBirdName != null">
                purple_bird_name = #{purpleBirdName,jdbcType=VARCHAR},
            </if>
            <if test="accountNumber != null">
                account_number = #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="saleId != null">
                sale_id = #{saleId,jdbcType=VARCHAR},
            </if>
            <if test="site != null">
                site = #{site,jdbcType=VARCHAR},
            </if>
            <if test="parentAsin != null">
                parent_asin = #{parentAsin,jdbcType=VARCHAR},
            </if>
            <if test="sonAsin != null">
                son_asin = #{sonAsin,jdbcType=VARCHAR},
            </if>
            <if test="mainSku != null">
                main_sku = #{mainSku,jdbcType=VARCHAR},
            </if>
            <if test="articleNumber != null">
                article_number = #{articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="sellerSku != null">
                seller_sku = #{sellerSku,jdbcType=VARCHAR},
            </if>
            <if test="esListingId != null">
                es_listing_id = #{esListingId,jdbcType=VARCHAR},
            </if>
            <if test="monitorId != null">
                monitor_id = #{monitorId,jdbcType=INTEGER},
            </if>
            <if test="monitorType != null">
                monitor_type = #{monitorType,jdbcType=INTEGER},
            </if>
            <if test="monitorContent != null">
                monitor_content = #{monitorContent,jdbcType=VARCHAR},
            </if>
            <if test="offlineRemark != null">
                offline_remark = #{offlineRemark,jdbcType=VARCHAR},
            </if>
            <if test="forbidChannel != null">
                forbid_channel = #{forbidChannel,jdbcType=VARCHAR},
            </if>
            <if test="infringementTypename != null">
                infringement_typename = #{infringementTypename,jdbcType=VARCHAR},
            </if>
            <if test="infringementObj != null">
                infringement_obj = #{infringementObj,jdbcType=VARCHAR},
            </if>
            <if test="prohibitionSite != null">
                prohibition_site = #{prohibitionSite,jdbcType=VARCHAR},
            </if>
            <if test="orderLast24hCount != null">
                order_last_24h_count = #{orderLast24hCount,jdbcType=INTEGER},
            </if>
            <if test="orderLast7dCount != null">
                order_last_7d_count = #{orderLast7dCount,jdbcType=INTEGER},
            </if>
            <if test="orderLast14dCount != null">
                order_last_14d_count = #{orderLast14dCount,jdbcType=INTEGER},
            </if>
            <if test="orderLast30dCount != null">
                order_last_30d_count = #{orderLast30dCount,jdbcType=INTEGER},
            </if>
            <if test="orderNumTotal != null">
                order_num_total = #{orderNumTotal,jdbcType=INTEGER},
            </if>
            <if test="openDate != null">
                open_date = #{openDate,jdbcType=TIMESTAMP},
            </if>
            <if test="offlineDate != null">
                offline_date = #{offlineDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="saveAll">
        insert into amazon_product_monitor (purple_bird_name, account_number, sale_id,
        site, parent_asin, son_asin,
        main_sku, article_number, seller_sku,
        es_listing_id, monitor_id, monitor_type,
        monitor_content, offline_remark, forbid_channel,
        infringement_typename, infringement_obj,
        prohibition_site, order_last_24h_count, order_last_7d_count,
        order_last_14d_count, order_last_30d_count, order_num_total,
        open_date, offline_date, create_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.purpleBirdName,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.saleId,jdbcType=VARCHAR},
            #{item.site,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR}, #{item.sonAsin,jdbcType=VARCHAR},
            #{item.mainSku,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR},
            #{item.esListingId,jdbcType=VARCHAR}, #{item.monitorId,jdbcType=INTEGER}, #{item.monitorType,jdbcType=INTEGER},
            #{item.monitorContent,jdbcType=VARCHAR}, #{item.offlineRemark,jdbcType=VARCHAR}, #{item.forbidChannel,jdbcType=VARCHAR},
            #{item.infringementTypename,jdbcType=VARCHAR}, #{item.infringementObj,jdbcType=VARCHAR},
            #{item.prohibitionSite,jdbcType=VARCHAR}, #{item.orderLast24hCount,jdbcType=INTEGER},#{item.orderLast7dCount,jdbcType=INTEGER},
            #{item.orderLast14dCount,jdbcType=INTEGER}, #{item.orderLast30dCount,jdbcType=INTEGER},#{item.orderNumTotal,jdbcType=INTEGER},
            #{item.openDate,jdbcType=TIMESTAMP}, #{item.offlineDate,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>