<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonDeleteProductListingAuditMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="is_online" property="isOnline" jdbcType="BIT" />
    <result column="forbid_channel" property="forbidChannel" jdbcType="VARCHAR" />
    <result column="infringement_typename" property="infringementTypename" jdbcType="VARCHAR" />
    <result column="infringement_obj" property="infringementObj" jdbcType="VARCHAR" />
    <result column="prohibition_site" property="prohibitionSite" jdbcType="VARCHAR" />
    <result column="sku_status" property="skuStatus" jdbcType="VARCHAR" />
    <result column="main_image" property="mainImage" jdbcType="VARCHAR" />
    <result column="order_24h_count" property="order24hCount" jdbcType="INTEGER" />
    <result column="order_last_7d_count" property="orderLast7dCount" jdbcType="INTEGER" />
    <result column="order_last_14d_count" property="orderLast14dCount" jdbcType="INTEGER" />
    <result column="order_last_30d_count" property="orderLast30dCount" jdbcType="INTEGER" />
    <result column="order_num_total" property="orderNumTotal" jdbcType="INTEGER" />
    <result column="offline_date" property="offlineDate" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="offline_reason" property="offlineReason" jdbcType="VARCHAR" />
    <result column="sales_id" property="salesId" jdbcType="VARCHAR" />
    <result column="submit_time" property="submitTime" jdbcType="TIMESTAMP" />
    <result column="submit_by" property="submitBy" jdbcType="VARCHAR" />
    <result column="sales_team_leader_audit_by" property="salesTeamLeaderAuditBy" jdbcType="VARCHAR" />
    <result column="sales_team_leader_audit_time" property="salesTeamLeaderAuditTime" jdbcType="TIMESTAMP" />
    <result column="sales_supervisor_audit_by" property="salesSupervisorAuditBy" jdbcType="VARCHAR" />
    <result column="sales_supervisor_audit_time" property="salesSupervisorAuditTime" jdbcType="TIMESTAMP" />
    <result column="audit_reason" property="auditReason" jdbcType="VARCHAR" />
    <result column="sync_date" property="syncDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, parent_asin, son_asin, seller_sku, main_sku, article_number,
    price, is_online, forbid_channel, infringement_typename, infringement_obj, prohibition_site,
    sku_status, main_image, order_24h_count, order_last_7d_count, order_last_14d_count,
    order_last_30d_count, order_num_total, offline_date, `status`, offline_reason, sales_id,
    submit_time, submit_by, sales_team_leader_audit_by, sales_team_leader_audit_time,
    sales_supervisor_audit_by, sales_supervisor_audit_time, audit_reason, sync_date,
    created_by, create_date, update_date, updated_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_delete_product_listing_audit
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from amazon_delete_product_listing_audit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_delete_product_listing_audit
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_delete_product_listing_audit (account_number, parent_asin, son_asin,
    seller_sku, main_sku, article_number,
    price, is_online, forbid_channel,
    infringement_typename, infringement_obj,
    prohibition_site, sku_status, main_image,
    order_24h_count, order_last_7d_count, order_last_14d_count,
    order_last_30d_count, order_num_total, offline_date,
    `status`, offline_reason, sales_id,
    submit_time, submit_by, sales_team_leader_audit_by,
    sales_team_leader_audit_time, sales_supervisor_audit_by,
    sales_supervisor_audit_time, audit_reason,
    sync_date, created_by, create_date,
    update_date, updated_by)
    values (#{accountNumber,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, #{sonAsin,jdbcType=VARCHAR},
    #{sellerSku,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR},
    #{price,jdbcType=DOUBLE}, #{isOnline,jdbcType=BIT}, #{forbidChannel,jdbcType=VARCHAR},
    #{infringementTypename,jdbcType=VARCHAR}, #{infringementObj,jdbcType=VARCHAR},
    #{prohibitionSite,jdbcType=VARCHAR}, #{skuStatus,jdbcType=VARCHAR}, #{mainImage,jdbcType=VARCHAR},
    #{order24hCount,jdbcType=INTEGER}, #{orderLast7dCount,jdbcType=INTEGER}, #{orderLast14dCount,jdbcType=INTEGER},
    #{orderLast30dCount,jdbcType=INTEGER}, #{orderNumTotal,jdbcType=INTEGER}, #{offlineDate,jdbcType=TIMESTAMP},
    #{status,jdbcType=INTEGER}, #{offlineReason,jdbcType=VARCHAR}, #{salesId,jdbcType=VARCHAR},
    #{submitTime,jdbcType=TIMESTAMP}, #{submitBy,jdbcType=VARCHAR}, #{salesTeamLeaderAuditBy,jdbcType=VARCHAR},
    #{salesTeamLeaderAuditTime,jdbcType=TIMESTAMP}, #{salesSupervisorAuditBy,jdbcType=VARCHAR},
    #{salesSupervisorAuditTime,jdbcType=TIMESTAMP}, #{auditReason,jdbcType=VARCHAR},
    #{syncDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
    #{updateDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAuditExample" resultType="java.lang.Integer" >
    select count(*) from amazon_delete_product_listing_audit
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="getUnAuditedSubmitBy" resultType="java.lang.String">
      select distinct submit_by from amazon_delete_product_listing_audit where status = #{status}
    </select>
    <update id="updateByExampleSelective" parameterType="map" >
    update amazon_delete_product_listing_audit
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.isOnline != null" >
        is_online = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.forbidChannel != null" >
        forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementTypename != null" >
        infringement_typename = #{record.infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementObj != null" >
        infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="record.prohibitionSite != null" >
        prohibition_site = #{record.prohibitionSite,jdbcType=VARCHAR},
      </if>
      <if test="record.skuStatus != null" >
        sku_status = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImage != null" >
        main_image = #{record.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.order24hCount != null" >
        order_24h_count = #{record.order24hCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast7dCount != null" >
        order_last_7d_count = #{record.orderLast7dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast14dCount != null" >
        order_last_14d_count = #{record.orderLast14dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast30dCount != null" >
        order_last_30d_count = #{record.orderLast30dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderNumTotal != null" >
        order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="record.offlineDate != null" >
        offline_date = #{record.offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.offlineReason != null" >
        offline_reason = #{record.offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="record.salesId != null" >
        sales_id = #{record.salesId,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null" >
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.submitBy != null" >
        submit_by = #{record.submitBy,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTeamLeaderAuditBy != null" >
        sales_team_leader_audit_by = #{record.salesTeamLeaderAuditBy,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTeamLeaderAuditTime != null" >
        sales_team_leader_audit_time = #{record.salesTeamLeaderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.salesSupervisorAuditBy != null" >
        sales_supervisor_audit_by = #{record.salesSupervisorAuditBy,jdbcType=VARCHAR},
      </if>
      <if test="record.salesSupervisorAuditTime != null" >
        sales_supervisor_audit_time = #{record.salesSupervisorAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditReason != null" >
        audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      </if>
      <if test="record.syncDate != null" >
        sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonDeleteProductListingAudit" >
    update amazon_delete_product_listing_audit
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="isOnline != null" >
        is_online = #{isOnline,jdbcType=BIT},
      </if>
      <if test="forbidChannel != null" >
        forbid_channel = #{forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypename != null" >
        infringement_typename = #{infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null" >
        infringement_obj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="prohibitionSite != null" >
        prohibition_site = #{prohibitionSite,jdbcType=VARCHAR},
      </if>
      <if test="skuStatus != null" >
        sku_status = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="order24hCount != null" >
        order_24h_count = #{order24hCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast7dCount != null" >
        order_last_7d_count = #{orderLast7dCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast14dCount != null" >
        order_last_14d_count = #{orderLast14dCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast30dCount != null" >
        order_last_30d_count = #{orderLast30dCount,jdbcType=INTEGER},
      </if>
      <if test="orderNumTotal != null" >
        order_num_total = #{orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="offlineDate != null" >
        offline_date = #{offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="offlineReason != null" >
        offline_reason = #{offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="salesId != null" >
        sales_id = #{salesId,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null" >
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitBy != null" >
        submit_by = #{submitBy,jdbcType=VARCHAR},
      </if>
      <if test="salesTeamLeaderAuditBy != null" >
        sales_team_leader_audit_by = #{salesTeamLeaderAuditBy,jdbcType=VARCHAR},
      </if>
      <if test="salesTeamLeaderAuditTime != null" >
        sales_team_leader_audit_time = #{salesTeamLeaderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesSupervisorAuditBy != null" >
        sales_supervisor_audit_by = #{salesSupervisorAuditBy,jdbcType=VARCHAR},
      </if>
      <if test="salesSupervisorAuditTime != null" >
        sales_supervisor_audit_time = #{salesSupervisorAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditReason != null" >
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null" >
        sync_date = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateByPrimaryKeySelective">
    <foreach collection="list" item="record" open="" separator=";" close="">
      update amazon_delete_product_listing_audit
      <set >
        <if test="record.id != null" >
          id = #{record.id,jdbcType=INTEGER},
        </if>
        <if test="record.accountNumber != null" >
          account_number = #{record.accountNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.parentAsin != null" >
          parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.sonAsin != null" >
          son_asin = #{record.sonAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.sellerSku != null" >
          seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
        </if>
        <if test="record.mainSku != null" >
          main_sku = #{record.mainSku,jdbcType=VARCHAR},
        </if>
        <if test="record.articleNumber != null" >
          article_number = #{record.articleNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.price != null" >
          price = #{record.price,jdbcType=DOUBLE},
        </if>
        <if test="record.isOnline != null" >
          is_online = #{record.isOnline,jdbcType=BIT},
        </if>
        <if test="record.forbidChannel != null" >
          forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
        </if>
        <if test="record.infringementTypename != null" >
          infringement_typename = #{record.infringementTypename,jdbcType=VARCHAR},
        </if>
        <if test="record.infringementObj != null" >
          infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
        </if>
        <if test="record.prohibitionSite != null" >
          prohibition_site = #{record.prohibitionSite,jdbcType=VARCHAR},
        </if>
        <if test="record.skuStatus != null" >
          sku_status = #{record.skuStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.mainImage != null" >
          main_image = #{record.mainImage,jdbcType=VARCHAR},
        </if>
        <if test="record.order24hCount != null" >
          order_24h_count = #{record.order24hCount,jdbcType=INTEGER},
        </if>
        <if test="record.orderLast7dCount != null" >
          order_last_7d_count = #{record.orderLast7dCount,jdbcType=INTEGER},
        </if>
        <if test="record.orderLast14dCount != null" >
          order_last_14d_count = #{record.orderLast14dCount,jdbcType=INTEGER},
        </if>
        <if test="record.orderLast30dCount != null" >
          order_last_30d_count = #{record.orderLast30dCount,jdbcType=INTEGER},
        </if>
        <if test="record.orderNumTotal != null" >
          order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
        </if>
        <if test="record.offlineDate != null" >
          offline_date = #{record.offlineDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.status != null" >
          `status` = #{record.status,jdbcType=INTEGER},
        </if>
        <if test="record.offlineReason != null" >
          offline_reason = #{record.offlineReason,jdbcType=VARCHAR},
        </if>
        <if test="record.salesId != null" >
          sales_id = #{record.salesId,jdbcType=VARCHAR},
        </if>
        <if test="record.submitTime != null" >
          submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.submitBy != null" >
          submit_by = #{record.submitBy,jdbcType=VARCHAR},
        </if>
        <if test="record.salesTeamLeaderAuditBy != null" >
          sales_team_leader_audit_by = #{record.salesTeamLeaderAuditBy,jdbcType=VARCHAR},
        </if>
        <if test="record.salesTeamLeaderAuditTime != null" >
          sales_team_leader_audit_time = #{record.salesTeamLeaderAuditTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.salesSupervisorAuditBy != null" >
          sales_supervisor_audit_by = #{record.salesSupervisorAuditBy,jdbcType=VARCHAR},
        </if>
        <if test="record.salesSupervisorAuditTime != null" >
          sales_supervisor_audit_time = #{record.salesSupervisorAuditTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.auditReason != null" >
          audit_reason = #{record.auditReason,jdbcType=VARCHAR},
        </if>
        <if test="record.syncDate != null" >
          sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.createdBy != null" >
          created_by = #{record.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="record.createDate != null" >
          create_date = #{record.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updateDate != null" >
          update_date = #{record.updateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updatedBy != null" >
          updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
    <update id="defaultPass">
      update amazon_delete_product_listing_audit set `status` = 6 where `status` &lt; 3 and id in
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id,jdbcType=INTEGER}
      </foreach>
    </update>

    <delete id="deleteByExample">
    delete from amazon_delete_product_listing_audit
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="_parameter == null" >
      where 1 != 1
    </if>
  </delete>
</mapper>