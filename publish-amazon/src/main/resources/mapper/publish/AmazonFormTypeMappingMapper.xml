<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonFormTypeMappingMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonFormTypeMapping" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="type_path_en" property="typePathEn" jdbcType="VARCHAR" />
    <result column="rel_site" property="relSite" jdbcType="VARCHAR" />
    <result column="rel_type_path_en" property="relTypePathEn" jdbcType="VARCHAR" />
    <result column="enable" property="enable" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <resultMap id="BaseResultMapExpand" type="com.estone.erp.publish.amazon.bo.AmazonFormTypeMappingBo" extends="BaseResultMap">
    <result column="rel_type_path_cn" property="relTypePathCn" jdbcType="VARCHAR" />
    <result column="rel_type_path_other" property="relTypePathOther" jdbcType="VARCHAR" />
    <result column="rel_node_id" property="relNodeId" jdbcType="VARCHAR" />
    <result column="rel_item_type" property="relItemType" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List" >
    id, site, type_path_en, rel_site, rel_type_path_en,
    `enable`, create_by, create_time, update_by, update_time
  </sql>

  <sql id="ExpandTableColumn" >
      mp.*,
      ty.type_path_cn rel_type_path_cn,
      ty.type_path_other rel_type_path_other,
      ty.node_id rel_node_id,
      ty.item_type rel_item_type
  </sql>
  <sql id="ExpandTable" >
    from amazon_form_type_mapping mp
    left join amazon_form_type ty on mp.rel_site = ty.site and mp.rel_type_path_en = ty.type_path_en
  </sql>

  <select id="selectExpandByExample" resultMap="BaseResultMapExpand" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMappingExample" >
    select
    <include refid="ExpandTableColumn" />

    <include refid="ExpandTable" />

    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMappingExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_form_type_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="countExpandByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMappingExample" resultType="java.lang.Integer" >
    select count(*)
    <include refid="ExpandTable" />

    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMappingExample" resultType="java.lang.Integer" >
    select count(*) from amazon_form_type_mapping
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_form_type_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_form_type_mapping
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMapping" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_form_type_mapping (site, type_path_en, rel_site, 
      rel_type_path_en,
      `enable`, create_by, create_time, 
      update_by, update_time)
    values (#{site,jdbcType=VARCHAR}, #{typePathEn,jdbcType=VARCHAR}, #{relSite,jdbcType=VARCHAR}, 
      #{relTypePathEn,jdbcType=VARCHAR},
      #{enable,jdbcType=BIT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert">
    insert ignore into amazon_form_type_mapping (site, type_path_en, rel_site,
      rel_type_path_en,
      `enable`, create_by, create_time,
      update_by, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.site,jdbcType=VARCHAR}, #{item.typePathEn,jdbcType=VARCHAR}, #{item.relSite,jdbcType=VARCHAR},
      #{item.relTypePathEn,jdbcType=VARCHAR},
      #{item.enable,jdbcType=BIT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_form_type_mapping
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.typePathEn != null" >
        type_path_en = #{record.typePathEn,jdbcType=VARCHAR},
      </if>
      <if test="record.relSite != null" >
        rel_site = #{record.relSite,jdbcType=VARCHAR},
      </if>
      <if test="record.relTypePathEn != null" >
        rel_type_path_en = #{record.relTypePathEn,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null" >
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonFormTypeMapping" >
    update amazon_form_type_mapping
    <set >
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="typePathEn != null" >
        type_path_en = #{typePathEn,jdbcType=VARCHAR},
      </if>
      <if test="relSite != null" >
        rel_site = #{relSite,jdbcType=VARCHAR},
      </if>
      <if test="relTypePathEn != null" >
        rel_type_path_en = #{relTypePathEn,jdbcType=VARCHAR},
      </if>
      <if test="enable != null" >
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="batchUpdate">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_form_type_mapping
      <set >
        <if test="record.site != null" >
          site = #{record.site,jdbcType=VARCHAR},
        </if>
        <if test="record.relTypePathEn != null" >
          rel_type_path_en = #{record.relTypePathEn,jdbcType=VARCHAR},
        </if>
        <if test="record.enable != null" >
          `enable` = #{record.enable,jdbcType=BIT},
        </if>
        <if test="record.updateBy != null" >
          update_by = #{record.updateBy,jdbcType=VARCHAR},
        </if>
        <if test="record.updateTime != null" >
          update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where rel_site = #{record.relSite,jdbcType=VARCHAR} and type_path_en = #{record.typePathEn,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>