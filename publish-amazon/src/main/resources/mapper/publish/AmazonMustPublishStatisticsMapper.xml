<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonMustPublishStatisticsMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonMustPublishStatistics" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
    <result column="year_week" property="yearWeek" jdbcType="VARCHAR" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="sales_id" property="salesId" jdbcType="VARCHAR" />
    <result column="sales_granularity" property="salesGranularity" jdbcType="INTEGER" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="allocate_count" property="allocateCount" jdbcType="INTEGER" />
    <result column="publish_count" property="publishCount" jdbcType="INTEGER" />
    <result column="publish_proportion" property="publishProportion" jdbcType="VARCHAR" />
    <result column="publish_success_count" property="publishSuccessCount" jdbcType="INTEGER" />
    <result column="publish_success_proportion" property="publishSuccessProportion" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, push_time, year_week, `year_month`, sales_id, sales_granularity, site, allocate_count, publish_count,
    publish_proportion, publish_success_count, publish_success_proportion, created_time, 
    updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatisticsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_must_publish_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectDateGranularityByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatisticsExample" >
    select
    push_time,
    year_week,
    `year_month`,
    sales_id,
    sales_granularity,
    site,
    SUM(allocate_count) allocate_count,
    SUM(publish_count) publish_count,
    SUM(publish_success_count) publish_success_count
    from amazon_must_publish_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="groupBy != null" >
      group by ${groupBy}
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="countDateGranularityByExample" resultType="java.lang.Integer" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatisticsExample" >
    select
    count(*)
    from(
    select id
    from amazon_must_publish_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    group by ${groupBy}
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    ) statistics
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_must_publish_statistics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_must_publish_statistics
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteMustPublishStatistics" >
    delete from amazon_must_publish_statistics
    where
    <if test="begin != null" >
      push_time <![CDATA[ >= ]]> #{begin} and
    </if>
    push_time <![CDATA[ <= ]]> #{end}
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatistics" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_must_publish_statistics (push_time, year_week, `year_month`, sales_id, sales_granularity,
      site, allocate_count, publish_count, 
      publish_proportion, publish_success_count, 
      publish_success_proportion, created_time, 
      updated_time)
    values (#{pushTime,jdbcType=TIMESTAMP}, #{yearWeek,jdbcType=VARCHAR}, #{yearMonth,jdbcType=VARCHAR},
      #{salesId,jdbcType=VARCHAR}, #{salesGranularity,jdbcType=INTEGER},
      #{site,jdbcType=VARCHAR}, #{allocateCount,jdbcType=INTEGER}, #{publishCount,jdbcType=INTEGER}, 
      #{publishProportion,jdbcType=VARCHAR}, #{publishSuccessCount,jdbcType=INTEGER}, 
      #{publishSuccessProportion,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatisticsExample" resultType="java.lang.Integer" >
    select count(*) from amazon_must_publish_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_must_publish_statistics
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.pushTime != null" >
        push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.yearWeek != null" >
        year_week = #{record.yearWeek,jdbcType=VARCHAR},
      </if>
      <if test="record.yearMonth != null" >
        `year_month` = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.salesId != null" >
        sales_id = #{record.salesId,jdbcType=VARCHAR},
      </if>
      <if test="record.salesGranularity != null" >
        sales_granularity = #{record.salesGranularity,jdbcType=INTEGER},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.allocateCount != null" >
        allocate_count = #{record.allocateCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishCount != null" >
        publish_count = #{record.publishCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishProportion != null" >
        publish_proportion = #{record.publishProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.publishSuccessCount != null" >
        publish_success_count = #{record.publishSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="record.publishSuccessProportion != null" >
        publish_success_proportion = #{record.publishSuccessProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishStatistics" >
    update amazon_must_publish_statistics
    <set >
      <if test="pushTime != null" >
        push_time = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yearWeek != null" >
        year_week = #{yearWeek,jdbcType=VARCHAR},
      </if>
      <if test="yearMonth != null" >
        `year_month` = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="salesId != null" >
        sales_id = #{salesId,jdbcType=VARCHAR},
      </if>
      <if test="salesGranularity != null" >
        sales_granularity = #{salesGranularity,jdbcType=INTEGER},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="allocateCount != null" >
        allocate_count = #{allocateCount,jdbcType=INTEGER},
      </if>
      <if test="publishCount != null" >
        publish_count = #{publishCount,jdbcType=INTEGER},
      </if>
      <if test="publishProportion != null" >
        publish_proportion = #{publishProportion,jdbcType=VARCHAR},
      </if>
      <if test="publishSuccessCount != null" >
        publish_success_count = #{publishSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="publishSuccessProportion != null" >
        publish_success_proportion = #{publishSuccessProportion,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into amazon_must_publish_statistics (push_time, year_week, `year_month`,
    sales_id, sales_granularity,
    site, allocate_count, publish_count,
    publish_proportion, publish_success_count,
    publish_success_proportion, created_time,
    updated_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.pushTime,jdbcType=TIMESTAMP}, #{item.yearWeek,jdbcType=VARCHAR},
      #{item.yearMonth,jdbcType=VARCHAR}, #{item.salesId,jdbcType=VARCHAR}, #{item.salesGranularity,jdbcType=INTEGER},
      #{item.site,jdbcType=VARCHAR}, #{item.allocateCount,jdbcType=INTEGER}, #{item.publishCount,jdbcType=INTEGER},
      #{item.publishProportion,jdbcType=VARCHAR}, #{item.publishSuccessCount,jdbcType=INTEGER},
      #{item.publishSuccessProportion,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP},
      #{item.updatedTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>