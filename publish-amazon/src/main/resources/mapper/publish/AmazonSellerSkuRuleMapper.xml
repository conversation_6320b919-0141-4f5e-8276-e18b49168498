<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonSellerSkuRuleMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonSellerSkuRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="prefix_rule_values" jdbcType="VARCHAR" property="prefixRuleValues" />
    <result column="prefix_split_rule_value" jdbcType="VARCHAR" property="prefixSplitRuleValue" />
    <result column="suffix_split_rule_value" jdbcType="VARCHAR" property="suffixSplitRuleValue" />
    <result column="suffix_rule_values" jdbcType="VARCHAR" property="suffixRuleValues" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="is_wenan" jdbcType="BIT" property="isWenan" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    id, account_number, rule_name, prefix_rule_values, prefix_split_rule_value, suffix_split_rule_value, 
    suffix_rule_values, priority, enable, is_wenan,rule_type, creation_date, created_by, last_update_date,
    last_updated_by
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_seller_sku_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from amazon_seller_sku_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    delete from amazon_seller_sku_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    delete from amazon_seller_sku_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_seller_sku_rule (account_number, rule_name, prefix_rule_values, 
      prefix_split_rule_value, suffix_split_rule_value, 
      suffix_rule_values, priority, enable, is_wenan,rule_type,
      creation_date, created_by, last_update_date, 
      last_updated_by)
    values (#{accountNumber,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{prefixRuleValues,jdbcType=VARCHAR}, 
      #{prefixSplitRuleValue,jdbcType=VARCHAR}, #{suffixSplitRuleValue,jdbcType=VARCHAR}, 
      #{suffixRuleValues,jdbcType=VARCHAR}, #{priority,jdbcType=INTEGER}, #{enable,jdbcType=BIT}, #{isWenan,jdbcType=BIT},
      #{ruleType,jdbcType=VARCHAR},#{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP},
      #{lastUpdatedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_seller_sku_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="prefixRuleValues != null">
        prefix_rule_values,
      </if>
      <if test="prefixSplitRuleValue != null">
        prefix_split_rule_value,
      </if>
      <if test="suffixSplitRuleValue != null">
        suffix_split_rule_value,
      </if>
      <if test="suffixRuleValues != null">
        suffix_rule_values,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="isWenan != null">
        is_wenan,
      </if>
      <if test="rule_type != null">
        rule_type,
      </if>
      <if test="creationDate != null">
        creation_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="prefixRuleValues != null">
        #{prefixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="prefixSplitRuleValue != null">
        #{prefixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="suffixSplitRuleValue != null">
        #{suffixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="suffixRuleValues != null">
        #{suffixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="isWenan != null">
        #{is_wenan,jdbcType=BIT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    select count(*) from amazon_seller_sku_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <!-- 查询去重后的店铺账号 -->
  <select id="selectDistinctAccountByExample" resultType="java.lang.String">
    select distinct account_number
    from amazon_seller_sku_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    update amazon_seller_sku_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null">
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.prefixRuleValues != null">
        prefix_rule_values = #{record.prefixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="record.prefixSplitRuleValue != null">
        prefix_split_rule_value = #{record.prefixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="record.suffixSplitRuleValue != null">
        suffix_split_rule_value = #{record.suffixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="record.suffixRuleValues != null">
        suffix_rule_values = #{record.suffixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="record.priority != null">
        priority = #{record.priority,jdbcType=INTEGER},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.isWenan != null">
        is_wenan = #{record.isWenan,jdbcType=BIT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null">
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    update amazon_seller_sku_rule
    set id = #{record.id,jdbcType=INTEGER},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      prefix_rule_values = #{record.prefixRuleValues,jdbcType=VARCHAR},
      prefix_split_rule_value = #{record.prefixSplitRuleValue,jdbcType=VARCHAR},
      suffix_split_rule_value = #{record.suffixSplitRuleValue,jdbcType=VARCHAR},
      suffix_rule_values = #{record.suffixRuleValues,jdbcType=VARCHAR},
      priority = #{record.priority,jdbcType=INTEGER},
      enable = #{record.enable,jdbcType=BIT},
      is_wenan = #{record.isWenan,jdbcType=BIT},
      rule_type = #{record.ruleType,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    update amazon_seller_sku_rule
    <set>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="prefixRuleValues != null">
        prefix_rule_values = #{prefixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="prefixSplitRuleValue != null">
        prefix_split_rule_value = #{prefixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="suffixSplitRuleValue != null">
        suffix_split_rule_value = #{suffixSplitRuleValue,jdbcType=VARCHAR},
      </if>
      <if test="suffixRuleValues != null">
        suffix_rule_values = #{suffixRuleValues,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
      <if test="isWenan != null">
        is_wenan = #{isWenan,jdbcType=BIT},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonSellerSkuRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 18 16:01:28 CST 2019.
    -->
    update amazon_seller_sku_rule
    set account_number = #{accountNumber,jdbcType=VARCHAR},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      prefix_rule_values = #{prefixRuleValues,jdbcType=VARCHAR},
      prefix_split_rule_value = #{prefixSplitRuleValue,jdbcType=VARCHAR},
      suffix_split_rule_value = #{suffixSplitRuleValue,jdbcType=VARCHAR},
      suffix_rule_values = #{suffixRuleValues,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=INTEGER},
      enable = #{enable,jdbcType=BIT},
      is_wenan = #{isWenan,jdbcType=BIT},
      rule_type = #{ruleType,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>