<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonProcessReportSolutionTypeMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionType" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="purple_bird_name" property="purpleBirdName" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="parent_sku" property="parentSku" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="salesman" property="salesman" jdbcType="VARCHAR" />
    <result column="standard_prodcut_id_type" property="standardProdcutIdType" jdbcType="VARCHAR" />
    <result column="solution_type" property="solutionType" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, purple_bird_name, sku, parent_sku, seller_sku, salesman, standard_prodcut_id_type, 
    solution_type, created_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionTypeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_process_report_solution_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_process_report_solution_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_process_report_solution_type
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionType" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_process_report_solution_type (account_number, purple_bird_name, sku, 
      parent_sku, seller_sku, salesman, 
      standard_prodcut_id_type, solution_type, created_date
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{purpleBirdName,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{parentSku,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{salesman,jdbcType=VARCHAR}, 
      #{standardProdcutIdType,jdbcType=VARCHAR}, #{solutionType,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionTypeExample" resultType="java.lang.Integer" >
    select count(*) from amazon_process_report_solution_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_process_report_solution_type
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.purpleBirdName != null" >
        purple_bird_name = #{record.purpleBirdName,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.parentSku != null" >
        parent_sku = #{record.parentSku,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.salesman != null" >
        salesman = #{record.salesman,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProdcutIdType != null" >
        standard_prodcut_id_type = #{record.standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.solutionType != null" >
        solution_type = #{record.solutionType,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionType" >
    update amazon_process_report_solution_type
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="purpleBirdName != null" >
        purple_bird_name = #{purpleBirdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="parentSku != null" >
        parent_sku = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="salesman != null" >
        salesman = #{salesman,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdType != null" >
        standard_prodcut_id_type = #{standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="solutionType != null" >
        solution_type = #{solutionType,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>