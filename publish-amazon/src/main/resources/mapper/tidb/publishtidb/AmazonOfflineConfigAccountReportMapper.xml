<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonOfflineConfigAccountReportMapper">

    <select id="aggregateTotalTable" resultType="java.lang.String">
        SELECT DISTINCT account_number
        FROM `amazon_offline_config_account_report`
        WHERE config_id = #{configId}
          AND status = #{status}
          AND statistics_date = #{latestTime}
        GROUP BY account_number
    </select>
</mapper>
