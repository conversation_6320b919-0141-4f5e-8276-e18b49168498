<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonListingMainImageComparisonMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonListingMainImageComparison" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="seller_sku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="is_online" property="isOnline" jdbcType="BIT" />
    <result column="sales_id" property="salesId" jdbcType="VARCHAR" />
    <result column="listing_main_image" property="listingMainImage" jdbcType="VARCHAR" />
    <result column="system_main_image" property="systemMainImage" jdbcType="VARCHAR" />
    <result column="similarity" property="similarity" jdbcType="DECIMAL" />
    <result column="open_date" property="openDate" jdbcType="TIMESTAMP" />
    <result column="comparison_time" property="comparisonTime" jdbcType="TIMESTAMP" />
    <result column="offline_reason" property="offlineReason" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, parent_asin, son_asin, seller_sku, article_number, is_online, 
    sales_id, listing_main_image, system_main_image, main_sku, similarity, open_date,
    comparison_time, offline_reason, create_date, create_by, update_date, update_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonListingMainImageComparisonExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from amazon_listing_main_image_comparison
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from amazon_listing_main_image_comparison
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_listing_main_image_comparison
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonListingMainImageComparison" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_listing_main_image_comparison (account_number, site, parent_asin, 
      son_asin, seller_sku, article_number, 
      is_online, sales_id, listing_main_image, 
      system_main_image, main_sku, similarity,
      open_date, comparison_time, offline_reason, 
      create_date, create_by, update_date, 
      update_by)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, 
      #{sonAsin,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, 
      #{isOnline,jdbcType=BIT}, #{salesId,jdbcType=VARCHAR}, #{listingMainImage,jdbcType=VARCHAR}, 
      #{systemMainImage,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR} , #{similarity,jdbcType=DECIMAL},
      #{openDate,jdbcType=TIMESTAMP}, #{comparisonTime,jdbcType=TIMESTAMP}, #{offlineReason,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR})
  </insert>


  <insert id="batchInsert">
    insert into amazon_listing_main_image_comparison (account_number, site, parent_asin,
    son_asin, seller_sku, article_number,
    is_online, sales_id, listing_main_image,
    system_main_image, main_sku, similarity,
    open_date, comparison_time, offline_reason,
    create_date, create_by, update_date,
    update_by)
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR},
      #{item.sonAsin,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR},
      #{item.isOnline,jdbcType=BIT}, #{item.salesId,jdbcType=VARCHAR}, #{item.listingMainImage,jdbcType=VARCHAR},
      #{item.systemMainImage,jdbcType=VARCHAR}, #{item.mainSku,jdbcType=VARCHAR} , #{item.similarity,jdbcType=DECIMAL},
      #{item.openDate,jdbcType=TIMESTAMP}, #{item.comparisonTime,jdbcType=TIMESTAMP}, #{item.offlineReason,jdbcType=VARCHAR},
      #{item.createDate,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
      #{item.updateBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonListingMainImageComparisonExample" resultType="java.lang.Integer" >
    select count(*) from amazon_listing_main_image_comparison
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_listing_main_image_comparison
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        son_asin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        seller_sku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnline != null" >
        is_online = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.salesId != null" >
        sales_id = #{record.salesId,jdbcType=VARCHAR},
      </if>
      <if test="record.listingMainImage != null" >
        listing_main_image = #{record.listingMainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.systemMainImage != null" >
        system_main_image = #{record.systemMainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.similarity != null" >
        similarity = #{record.similarity,jdbcType=DECIMAL},
      </if>
      <if test="record.openDate != null" >
        open_date = #{record.openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.comparisonTime != null" >
        comparison_time = #{record.comparisonTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.offlineReason != null" >
        offline_reason = #{record.offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonListingMainImageComparison" >
    update amazon_listing_main_image_comparison
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        son_asin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null" >
        is_online = #{isOnline,jdbcType=BIT},
      </if>
      <if test="salesId != null" >
        sales_id = #{salesId,jdbcType=VARCHAR},
      </if>
      <if test="listingMainImage != null" >
        listing_main_image = #{listingMainImage,jdbcType=VARCHAR},
      </if>
      <if test="systemMainImage != null" >
        system_main_image = #{systemMainImage,jdbcType=VARCHAR},
      </if>
      <if test="similarity != null" >
        similarity = #{similarity,jdbcType=DECIMAL},
      </if>
      <if test="openDate != null" >
        open_date = #{openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="comparisonTime != null" >
        comparison_time = #{comparisonTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineReason != null" >
        offline_reason = #{offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateSimilarity">
    <foreach collection="list" item="record" separator=";" close=";">
      update amazon_listing_main_image_comparison
      set
      system_main_image = #{record.systemMainImage,jdbcType=VARCHAR},
      similarity = #{record.similarity,jdbcType=DECIMAL},
      listing_main_image = #{record.listingMainImage,jdbcType=VARCHAR}
      where id = #{record.id}
    </foreach>
  </update>
</mapper>