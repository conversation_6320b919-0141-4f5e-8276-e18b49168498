<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyStatisticsMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatistics" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="origin_word" property="originWord" jdbcType="VARCHAR" />
    <result column="trademark_word" property="trademarkWord" jdbcType="VARCHAR" />
    <result column="total_number" property="totalNumber" jdbcType="BIGINT" />
    <result column="amazon_copywriting_number" property="amazonCopywritingNumber" jdbcType="BIGINT" />
    <result column="universal_copywriting_number" property="universalCopywritingNumber" jdbcType="BIGINT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, origin_word, trademark_word, total_number, amazon_copywriting_number, universal_copywriting_number, 
    create_by, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_infringement_word_frequency_statistics
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatistics" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_infringement_word_frequency_statistics (origin_word, trademark_word, total_number, 
      amazon_copywriting_number, universal_copywriting_number, 
      create_by, create_time)
    values (#{originWord,jdbcType=VARCHAR}, #{trademarkWord,jdbcType=VARCHAR}, #{totalNumber,jdbcType=BIGINT}, 
      #{amazonCopywritingNumber,jdbcType=BIGINT}, #{universalCopywritingNumber,jdbcType=BIGINT}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into amazon_infringement_word_frequency_statistics (origin_word, trademark_word, total_number,
    amazon_copywriting_number, universal_copywriting_number,
    create_by, create_time)
    values
    <foreach collection="list" item="record" separator=",">
      (#{record.originWord,jdbcType=VARCHAR}, #{record.trademarkWord,jdbcType=VARCHAR},
      #{record.totalNumber,jdbcType=BIGINT},
      #{record.amazonCopywritingNumber,jdbcType=BIGINT}, #{record.universalCopywritingNumber,jdbcType=BIGINT},
      #{record.createBy,jdbcType=VARCHAR}, #{record.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsExample" resultType="java.lang.Integer" >
    select count(*) from amazon_infringement_word_frequency_statistics
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_infringement_word_frequency_statistics
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.originWord != null" >
        origin_word = #{record.originWord,jdbcType=VARCHAR},
      </if>
      <if test="record.trademarkWord != null" >
        trademark_word = #{record.trademarkWord,jdbcType=VARCHAR},
      </if>
      <if test="record.totalNumber != null" >
        total_number = #{record.totalNumber,jdbcType=BIGINT},
      </if>
      <if test="record.amazonCopywritingNumber != null" >
        amazon_copywriting_number = #{record.amazonCopywritingNumber,jdbcType=BIGINT},
      </if>
      <if test="record.universalCopywritingNumber != null" >
        universal_copywriting_number = #{record.universalCopywritingNumber,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatistics" >
    update amazon_infringement_word_frequency_statistics
    <set >
      <if test="originWord != null" >
        origin_word = #{originWord,jdbcType=VARCHAR},
      </if>
      <if test="trademarkWord != null" >
        trademark_word = #{trademarkWord,jdbcType=VARCHAR},
      </if>
      <if test="totalNumber != null" >
        total_number = #{totalNumber,jdbcType=BIGINT},
      </if>
      <if test="amazonCopywritingNumber != null" >
        amazon_copywriting_number = #{amazonCopywritingNumber,jdbcType=BIGINT},
      </if>
      <if test="universalCopywritingNumber != null" >
        universal_copywriting_number = #{universalCopywritingNumber,jdbcType=BIGINT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdate">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_infringement_word_frequency_statistics
      <set>
        <if test="record.id != null">
          id = #{record.id,jdbcType=BIGINT},
        </if>
        <if test="record.originWord != null">
          origin_word = #{record.originWord,jdbcType=VARCHAR},
        </if>
        <if test="record.trademarkWord != null">
          trademark_word = #{record.trademarkWord,jdbcType=VARCHAR},
        </if>
        <if test="record.totalNumber != null">
          total_number = #{record.totalNumber,jdbcType=BIGINT},
        </if>
        <if test="record.amazonCopywritingNumber != null">
          amazon_copywriting_number = #{record.amazonCopywritingNumber,jdbcType=BIGINT},
        </if>
        <if test="record.universalCopywritingNumber != null">
          universal_copywriting_number = #{record.universalCopywritingNumber,jdbcType=BIGINT},
        </if>
        <if test="record.createBy != null">
          create_by = #{record.createBy,jdbcType=VARCHAR},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>