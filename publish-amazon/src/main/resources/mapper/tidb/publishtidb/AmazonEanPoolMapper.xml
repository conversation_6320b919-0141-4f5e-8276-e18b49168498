<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonEanPoolMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPool">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="prefix" property="prefix" jdbcType="VARCHAR"/>
        <result column="ean_code" property="eanCode" jdbcType="INTEGER"/>
        <result column="is_available" property="isAvailable" jdbcType="BIT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id,
        `prefix`,
        ean_code,
        is_available,
        created_time,
        updated_time
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPoolExample">
        select
        <if test="distinct">
            distinct
        </if>
        /*+ USE_INDEX(amazon_ean_pool, idx_prefix_ean_code) */ 'true' AS QUERYID,
        <include refid="Base_Column_List"/>
        from amazon_ean_pool
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from amazon_ean_pool
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey">
        delete
        from amazon_ean_pool
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPool">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_ean_pool (`prefix`, ean_code, is_available,
        created_time, updated_time)
        values (#{prefix,jdbcType=VARCHAR}, #{eanCode,jdbcType=INTEGER}, #{isAvailable,jdbcType=BIT},
        #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPoolExample"
            resultType="java.lang.Integer">
        select count(*)
        from amazon_ean_pool
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update amazon_ean_pool
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.prefix != null">
                `prefix` = #{record.prefix,jdbcType=VARCHAR},
            </if>
            <if test="record.eanCode != null">
                ean_code = #{record.eanCode,jdbcType=INTEGER},
            </if>
            <if test="record.isAvailable != null">
                is_available = #{record.isAvailable,jdbcType=BIT},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedTime != null">
                updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPool">
        update amazon_ean_pool
        <set>
            <if test="prefix != null">
                `prefix` = #{prefix,jdbcType=VARCHAR},
            </if>
            <if test="eanCode != null">
                ean_code = #{eanCode,jdbcType=INTEGER},
            </if>
            <if test="isAvailable != null">
                is_available = #{isAvailable,jdbcType=BIT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="batchInsert">
        insert into amazon_ean_pool (`prefix`, ean_code, is_available, created_time, updated_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.prefix,jdbcType=VARCHAR}, #{item.eanCode,jdbcType=INTEGER}, #{item.isAvailable,jdbcType=BIT},
            #{item.createdTime,jdbcType=TIMESTAMP}, #{item.updatedTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>


    <update id="batchUpdateEanPoolStatus">
        update amazon_ean_pool set is_available = #{isAvailable,jdbcType=BIT}, updated_time = NOW() where id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <delete id="batchDeleteByIds">
        delete from amazon_ean_pool where id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>