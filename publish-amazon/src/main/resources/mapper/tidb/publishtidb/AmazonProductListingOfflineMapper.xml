<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonProductListingOfflineMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="accountNumber" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="parentAsin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="sonAsin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="sellerSku" property="sellerSku" jdbcType="VARCHAR" />
    <result column="mainSku" property="mainSku" jdbcType="VARCHAR" />
    <result column="articleNumber" property="articleNumber" jdbcType="VARCHAR" />
    <result column="skuDataSource" property="skuDataSource" jdbcType="INTEGER" />
    <result column="itemStatus" property="itemStatus" jdbcType="VARCHAR" />
    <result column="isOnline" property="isOnline" jdbcType="BIT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="itemName" property="itemName" jdbcType="VARCHAR" />
    <result column="itemDescription" property="itemDescription" jdbcType="VARCHAR" />
    <result column="forbidChannel" property="forbidChannel" jdbcType="VARCHAR" />
    <result column="skuStatus" property="skuStatus" jdbcType="VARCHAR" />
    <result column="tagCodes" property="tagCodes" jdbcType="VARCHAR" />
    <result column="tagNames" property="tagNames" jdbcType="VARCHAR" />
    <result column="specialGoodsCode" property="specialGoodsCode" jdbcType="VARCHAR" />
    <result column="specialGoodsName" property="specialGoodsName" jdbcType="VARCHAR" />
    <result column="itemIsMarketplace" property="itemIsMarketplace" jdbcType="VARCHAR" />
    <result column="itemCondition" property="itemCondition" jdbcType="VARCHAR" />
    <result column="zshopCategory" property="zshopCategory" jdbcType="VARCHAR" />
    <result column="productIdType" property="productIdType" jdbcType="INTEGER" />
    <result column="productId" property="productId" jdbcType="VARCHAR" />
    <result column="mainImage" property="mainImage" jdbcType="VARCHAR" />
    <result column="sampleImage" property="sampleImage" jdbcType="VARCHAR" />
    <result column="extraImages" property="extraImages" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="grossProfitRate" property="grossProfitRate" jdbcType="DOUBLE" />
    <result column="grossProfit" property="grossProfit" jdbcType="DOUBLE" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="saleQuantity" property="saleQuantity" jdbcType="INTEGER" />
    <result column="salePrice" property="salePrice" jdbcType="DOUBLE" />
    <result column="saleStartDate" property="saleStartDate" jdbcType="TIMESTAMP" />
    <result column="saleEndDate" property="saleEndDate" jdbcType="TIMESTAMP" />
    <result column="lowestPrice" property="lowestPrice" jdbcType="DOUBLE" />
    <result column="isPopular" property="isPopular" jdbcType="VARCHAR" />
    <result column="isFollowSellDelete" property="isFollowSellDelete" jdbcType="BIT" />
    <result column="followSaleFlag" property="followSaleFlag" jdbcType="VARCHAR" />
    <result column="listingId" property="listingId" jdbcType="VARCHAR" />
    <result column="skuLifeCyclePhase" property="skuLifeCyclePhase" jdbcType="VARCHAR" />
    <result column="merchantShippingGroup" property="merchantShippingGroup" jdbcType="VARCHAR" />
    <result column="shippingCost" property="shippingCost" jdbcType="DOUBLE" />
    <result column="totalPrice" property="totalPrice" jdbcType="DOUBLE" />
    <result column="identifierType" property="identifierType" jdbcType="VARCHAR" />
    <result column="identifier" property="identifier" jdbcType="VARCHAR" />
    <result column="productType" property="productType" jdbcType="VARCHAR" />
    <result column="brandName" property="brandName" jdbcType="VARCHAR" />
    <result column="browseNode" property="browseNode" jdbcType="VARCHAR" />
    <result column="colorName" property="colorName" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="modelNumber" property="modelNumber" jdbcType="VARCHAR" />
    <result column="sizeName" property="sizeName" jdbcType="VARCHAR" />
    <result column="styleName" property="styleName" jdbcType="VARCHAR" />
    <result column="categoryId" property="categoryId" jdbcType="VARCHAR" />
    <result column="categoryCnName" property="categoryCnName" jdbcType="VARCHAR" />
    <result column="relationTemplateId" property="relationTemplateId" jdbcType="INTEGER" />
    <result column="autoUpdateMsgDate" property="autoUpdateMsgDate" jdbcType="TIMESTAMP" />
    <result column="lastAdjustPriceDate" property="lastAdjustPriceDate" jdbcType="TIMESTAMP" />
    <result column="reportOpenDate" property="reportOpenDate" jdbcType="VARCHAR" />
    <result column="openDate" property="openDate" jdbcType="TIMESTAMP" />
    <result column="firstOpenDate" property="firstOpenDate" jdbcType="TIMESTAMP" />
    <result column="offlineDate" property="offlineDate" jdbcType="TIMESTAMP" />
    <result column="firstOfflineDate" property="firstOfflineDate" jdbcType="TIMESTAMP" />
    <result column="syncDate" property="syncDate" jdbcType="TIMESTAMP" />
    <result column="createdBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="createDate" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updateDate" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="updatedBy" property="updatedBy" jdbcType="VARCHAR" />
    <result column="attribute1" property="attribute1" jdbcType="VARCHAR" />
    <result column="attribute2" property="attribute2" jdbcType="VARCHAR" />
    <result column="attribute3" property="attribute3" jdbcType="VARCHAR" />
    <result column="attribute4" property="attribute4" jdbcType="VARCHAR" />
    <result column="attribute5" property="attribute5" jdbcType="VARCHAR" />
    <result column="attribute6" property="attribute6" jdbcType="VARCHAR" />
    <result column="attribute7" property="attribute7" jdbcType="VARCHAR" />
    <result column="infringementTypename" property="infringementTypename" jdbcType="VARCHAR" />
    <result column="infringementObj" property="infringementObj" jdbcType="VARCHAR" />
    <result column="normalSale" property="normalSale" jdbcType="VARCHAR" />
    <result column="publishRole" property="publishRole" jdbcType="INTEGER" />
    <result column="fulfillmentLatency" property="fulfillmentLatency" jdbcType="INTEGER" />
    <result column="composeStatus" property="composeStatus" jdbcType="INTEGER" />
    <result column="newState" property="newState" jdbcType="BIT" />
    <result column="promotion" property="promotion" jdbcType="INTEGER" />
    <result column="issuesSeverity" property="issuesSeverity" jdbcType="VARCHAR" />
    <result column="itemSummariesStastus" property="itemSummariesStastus" jdbcType="VARCHAR" />
    <result column="conditionType" property="conditionType" jdbcType="VARCHAR" />
    <result column="iteamLastUpdatedDate" property="iteamLastUpdatedDate" jdbcType="TIMESTAMP" />
    <result column="itemType" property="itemType" jdbcType="INTEGER" />
    <result column="childAsins" property="childAsins" jdbcType="VARCHAR" />
    <result column="packageQuantity" property="packageQuantity" jdbcType="INTEGER" />
    <result column="searchTerms" property="searchTerms" jdbcType="VARCHAR" />
    <result column="bulletPoint" property="bulletPoint" jdbcType="VARCHAR" />
    <result column="order_24H_count" property="order_24H_count" jdbcType="INTEGER" />
    <result column="order_last_7d_count" property="order_last_7d_count" jdbcType="INTEGER" />
    <result column="order_last_14d_count" property="order_last_14d_count" jdbcType="INTEGER" />
    <result column="order_last_30d_count" property="order_last_30d_count" jdbcType="INTEGER" />
    <result column="order_num_total" property="order_num_total" jdbcType="INTEGER" />
    <result column="order_days_within_30d" property="order_days_within_30d" jdbcType="INTEGER" />
    <result column="infringementWord" property="infringementWord" jdbcType="VARCHAR" />
    <result column="infringementWordInfos" property="infringementWordInfos" jdbcType="VARCHAR" />
    <result column="updateInfringementTime" property="updateInfringementTime" jdbcType="TIMESTAMP" />
    <result column="saleNo" property="saleNo" jdbcType="VARCHAR" />
    <result column="saleName" property="saleName" jdbcType="VARCHAR" />
    <result column="accountLevel" property="accountLevel" jdbcType="VARCHAR" />
    <result column="fba" property="fba" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, accountNumber, site, parentAsin, sonAsin, sellerSku, mainSku, articleNumber, 
    skuDataSource, itemStatus, isOnline, `name`, itemName, itemDescription, forbidChannel, 
    skuStatus, tagCodes, tagNames, specialGoodsCode, specialGoodsName, itemIsMarketplace, 
    itemCondition, zshopCategory, productIdType, productId, mainImage, sampleImage, extraImages, 
    price, grossProfitRate, grossProfit, quantity, saleQuantity, salePrice, saleStartDate, 
    saleEndDate, lowestPrice, isPopular, isFollowSellDelete, followSaleFlag, listingId, 
    skuLifeCyclePhase, merchantShippingGroup, shippingCost, totalPrice, identifierType, 
    identifier, productType, brandName, browseNode, colorName, manufacturer, modelNumber, 
    sizeName, styleName, categoryId, categoryCnName, relationTemplateId, autoUpdateMsgDate, 
    lastAdjustPriceDate, reportOpenDate, openDate, firstOpenDate, offlineDate, firstOfflineDate, 
    syncDate, createdBy, createDate, updateDate, updatedBy, attribute1, attribute2, attribute3, 
    attribute4, attribute5, attribute6, attribute7, infringementTypename, infringementObj, 
    normalSale, publishRole, fulfillmentLatency, composeStatus, newState, promotion, 
    issuesSeverity, itemSummariesStastus, conditionType, iteamLastUpdatedDate, itemType, 
    childAsins, packageQuantity, searchTerms, bulletPoint, order_24H_count, order_last_7d_count, 
    order_last_14d_count, order_last_30d_count, order_num_total, order_days_within_30d, 
    infringementWord, infringementWordInfos, updateInfringementTime, saleNo, saleName, 
    accountLevel,fba
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample">
    SELECT
        <include refid="Base_Column_List" />
    FROM
        amazon_product_listing_offline
    WHERE id IN (
        SELECT
            id
        FROM
            amazon_product_listing_offline
        <if test="_parameter != null">
          <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
          ORDER BY ${orderByClause}
        </if>
        <if test="limit != null">
          <if test="offset != null">
            LIMIT ${offset}, ${limit}
          </if>
          <if test="offset == null">
            LIMIT ${limit}
          </if>
        </if>
        )
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_product_listing_offline
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_product_listing_offline
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_product_listing_offline (accountNumber, site, parentAsin, 
      sonAsin, sellerSku, mainSku, 
      articleNumber, skuDataSource, itemStatus, 
      isOnline, `name`, itemName, 
      itemDescription, forbidChannel, skuStatus, 
      tagCodes, tagNames, specialGoodsCode, 
      specialGoodsName, itemIsMarketplace, itemCondition, 
      zshopCategory, productIdType, productId, 
      mainImage, sampleImage, extraImages, 
      price, grossProfitRate, grossProfit, 
      quantity, saleQuantity, salePrice, 
      saleStartDate, saleEndDate, lowestPrice, 
      isPopular, isFollowSellDelete, followSaleFlag, 
      listingId, skuLifeCyclePhase, merchantShippingGroup, 
      shippingCost, totalPrice, identifierType, 
      identifier, productType, brandName, 
      browseNode, colorName, manufacturer, 
      modelNumber, sizeName, styleName, 
      categoryId, categoryCnName, relationTemplateId, 
      autoUpdateMsgDate, lastAdjustPriceDate, 
      reportOpenDate, openDate, firstOpenDate, 
      offlineDate, firstOfflineDate, syncDate, 
      createdBy, createDate, updateDate, 
      updatedBy, attribute1, attribute2, 
      attribute3, attribute4, attribute5, 
      attribute6, attribute7, infringementTypename, 
      infringementObj, normalSale, publishRole, 
      fulfillmentLatency, composeStatus, newState, 
      promotion, issuesSeverity, itemSummariesStastus, 
      conditionType, iteamLastUpdatedDate, 
      itemType, childAsins, packageQuantity, 
      searchTerms, bulletPoint, order_24H_count, 
      order_last_7d_count, order_last_14d_count, 
      order_last_30d_count, order_num_total, 
      order_days_within_30d, infringementWord, 
      infringementWordInfos, updateInfringementTime, 
      saleNo, saleName, accountLevel,fba
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, 
      #{sonAsin,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, 
      #{articleNumber,jdbcType=VARCHAR}, #{skuDataSource,jdbcType=INTEGER}, #{itemStatus,jdbcType=VARCHAR}, 
      #{isOnline,jdbcType=BIT}, #{name,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, 
      #{itemDescription,jdbcType=VARCHAR}, #{forbidChannel,jdbcType=VARCHAR}, #{skuStatus,jdbcType=VARCHAR}, 
      #{tagCodes,jdbcType=VARCHAR}, #{tagNames,jdbcType=VARCHAR}, #{specialGoodsCode,jdbcType=VARCHAR}, 
      #{specialGoodsName,jdbcType=VARCHAR}, #{itemIsMarketplace,jdbcType=VARCHAR}, #{itemCondition,jdbcType=VARCHAR}, 
      #{zshopCategory,jdbcType=VARCHAR}, #{productIdType,jdbcType=INTEGER}, #{productId,jdbcType=VARCHAR}, 
      #{mainImage,jdbcType=VARCHAR}, #{sampleImage,jdbcType=VARCHAR}, #{extraImages,jdbcType=VARCHAR}, 
      #{price,jdbcType=DOUBLE}, #{grossProfitRate,jdbcType=DOUBLE}, #{grossProfit,jdbcType=DOUBLE}, 
      #{quantity,jdbcType=INTEGER}, #{saleQuantity,jdbcType=INTEGER}, #{salePrice,jdbcType=DOUBLE}, 
      #{saleStartDate,jdbcType=TIMESTAMP}, #{saleEndDate,jdbcType=TIMESTAMP}, #{lowestPrice,jdbcType=DOUBLE}, 
      #{isPopular,jdbcType=VARCHAR}, #{isFollowSellDelete,jdbcType=BIT}, #{followSaleFlag,jdbcType=VARCHAR}, 
      #{listingId,jdbcType=VARCHAR}, #{skuLifeCyclePhase,jdbcType=VARCHAR}, #{merchantShippingGroup,jdbcType=VARCHAR}, 
      #{shippingCost,jdbcType=DOUBLE}, #{totalPrice,jdbcType=DOUBLE}, #{identifierType,jdbcType=VARCHAR}, 
      #{identifier,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, 
      #{browseNode,jdbcType=VARCHAR}, #{colorName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{modelNumber,jdbcType=VARCHAR}, #{sizeName,jdbcType=VARCHAR}, #{styleName,jdbcType=VARCHAR}, 
      #{categoryId,jdbcType=VARCHAR}, #{categoryCnName,jdbcType=VARCHAR}, #{relationTemplateId,jdbcType=INTEGER}, 
      #{autoUpdateMsgDate,jdbcType=TIMESTAMP}, #{lastAdjustPriceDate,jdbcType=TIMESTAMP}, 
      #{reportOpenDate,jdbcType=VARCHAR}, #{openDate,jdbcType=TIMESTAMP}, #{firstOpenDate,jdbcType=TIMESTAMP}, 
      #{offlineDate,jdbcType=TIMESTAMP}, #{firstOfflineDate,jdbcType=TIMESTAMP}, #{syncDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR}, 
      #{attribute6,jdbcType=VARCHAR}, #{attribute7,jdbcType=VARCHAR}, #{infringementTypename,jdbcType=VARCHAR}, 
      #{infringementObj,jdbcType=VARCHAR}, #{normalSale,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER}, 
      #{fulfillmentLatency,jdbcType=INTEGER}, #{composeStatus,jdbcType=INTEGER}, #{newState,jdbcType=BIT}, 
      #{promotion,jdbcType=INTEGER}, #{issuesSeverity,jdbcType=VARCHAR}, #{itemSummariesStastus,jdbcType=VARCHAR}, 
      #{conditionType,jdbcType=VARCHAR}, #{iteamLastUpdatedDate,jdbcType=TIMESTAMP}, 
      #{itemType,jdbcType=INTEGER}, #{childAsins,jdbcType=VARCHAR}, #{packageQuantity,jdbcType=INTEGER}, 
      #{searchTerms,jdbcType=VARCHAR}, #{bulletPoint,jdbcType=VARCHAR}, #{order_24H_count,jdbcType=INTEGER}, 
      #{order_last_7d_count,jdbcType=INTEGER}, #{order_last_14d_count,jdbcType=INTEGER}, 
      #{order_last_30d_count,jdbcType=INTEGER}, #{order_num_total,jdbcType=INTEGER}, 
      #{order_days_within_30d,jdbcType=INTEGER}, #{infringementWord,jdbcType=VARCHAR}, 
      #{infringementWordInfos,jdbcType=VARCHAR}, #{updateInfringementTime,jdbcType=TIMESTAMP}, 
      #{saleNo,jdbcType=VARCHAR}, #{saleName,jdbcType=VARCHAR}, #{accountLevel,jdbcType=VARCHAR},
     #{fba,jdbcType=BIT}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample" resultType="java.lang.Integer" >
    select count(*) from amazon_product_listing_offline
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_product_listing_offline
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        accountNumber = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parentAsin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null" >
        sonAsin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null" >
        sellerSku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        mainSku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        articleNumber = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null" >
        skuDataSource = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.itemStatus != null" >
        itemStatus = #{record.itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnline != null" >
        isOnline = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null" >
        itemName = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemDescription != null" >
        itemDescription = #{record.itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidChannel != null" >
        forbidChannel = #{record.forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.skuStatus != null" >
        skuStatus = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tagCodes != null" >
        tagCodes = #{record.tagCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.tagNames != null" >
        tagNames = #{record.tagNames,jdbcType=VARCHAR},
      </if>
      <if test="record.specialGoodsCode != null" >
        specialGoodsCode = #{record.specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.specialGoodsName != null" >
        specialGoodsName = #{record.specialGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemIsMarketplace != null" >
        itemIsMarketplace = #{record.itemIsMarketplace,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCondition != null" >
        itemCondition = #{record.itemCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.zshopCategory != null" >
        zshopCategory = #{record.zshopCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.productIdType != null" >
        productIdType = #{record.productIdType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null" >
        productId = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImage != null" >
        mainImage = #{record.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleImage != null" >
        sampleImage = #{record.sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="record.extraImages != null" >
        extraImages = #{record.extraImages,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.grossProfitRate != null" >
        grossProfitRate = #{record.grossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="record.grossProfit != null" >
        grossProfit = #{record.grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.saleQuantity != null" >
        saleQuantity = #{record.saleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.salePrice != null" >
        salePrice = #{record.salePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.saleStartDate != null" >
        saleStartDate = #{record.saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleEndDate != null" >
        saleEndDate = #{record.saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lowestPrice != null" >
        lowestPrice = #{record.lowestPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.isPopular != null" >
        isPopular = #{record.isPopular,jdbcType=VARCHAR},
      </if>
      <if test="record.isFollowSellDelete != null" >
        isFollowSellDelete = #{record.isFollowSellDelete,jdbcType=BIT},
      </if>
      <if test="record.followSaleFlag != null" >
        followSaleFlag = #{record.followSaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.listingId != null" >
        listingId = #{record.listingId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuLifeCyclePhase != null" >
        skuLifeCyclePhase = #{record.skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantShippingGroup != null" >
        merchantShippingGroup = #{record.merchantShippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.shippingCost != null" >
        shippingCost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.totalPrice != null" >
        totalPrice = #{record.totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.identifierType != null" >
        identifierType = #{record.identifierType,jdbcType=VARCHAR},
      </if>
      <if test="record.identifier != null" >
        identifier = #{record.identifier,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        productType = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        brandName = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNode != null" >
        browseNode = #{record.browseNode,jdbcType=VARCHAR},
      </if>
      <if test="record.colorName != null" >
        colorName = #{record.colorName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null" >
        modelNumber = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sizeName != null" >
        sizeName = #{record.sizeName,jdbcType=VARCHAR},
      </if>
      <if test="record.styleName != null" >
        styleName = #{record.styleName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null" >
        categoryId = #{record.categoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCnName != null" >
        categoryCnName = #{record.categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.relationTemplateId != null" >
        relationTemplateId = #{record.relationTemplateId,jdbcType=INTEGER},
      </if>
      <if test="record.autoUpdateMsgDate != null" >
        autoUpdateMsgDate = #{record.autoUpdateMsgDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAdjustPriceDate != null" >
        lastAdjustPriceDate = #{record.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportOpenDate != null" >
        reportOpenDate = #{record.reportOpenDate,jdbcType=VARCHAR},
      </if>
      <if test="record.openDate != null" >
        openDate = #{record.openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstOpenDate != null" >
        firstOpenDate = #{record.firstOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.offlineDate != null" >
        offlineDate = #{record.offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstOfflineDate != null" >
        firstOfflineDate = #{record.firstOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncDate != null" >
        syncDate = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        createdBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        createDate = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        updateDate = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updatedBy = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute1 != null" >
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null" >
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null" >
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null" >
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute6 != null" >
        attribute6 = #{record.attribute6,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute7 != null" >
        attribute7 = #{record.attribute7,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementTypename != null" >
        infringementTypename = #{record.infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementObj != null" >
        infringementObj = #{record.infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="record.normalSale != null" >
        normalSale = #{record.normalSale,jdbcType=VARCHAR},
      </if>
      <if test="record.publishRole != null" >
        publishRole = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.fulfillmentLatency != null" >
        fulfillmentLatency = #{record.fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="record.composeStatus != null" >
        composeStatus = #{record.composeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.newState != null" >
        newState = #{record.newState,jdbcType=BIT},
      </if>
      <if test="record.promotion != null" >
        promotion = #{record.promotion,jdbcType=INTEGER},
      </if>
      <if test="record.issuesSeverity != null" >
        issuesSeverity = #{record.issuesSeverity,jdbcType=VARCHAR},
      </if>
      <if test="record.itemSummariesStastus != null" >
        itemSummariesStastus = #{record.itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionType != null" >
        conditionType = #{record.conditionType,jdbcType=VARCHAR},
      </if>
      <if test="record.iteamLastUpdatedDate != null" >
        iteamLastUpdatedDate = #{record.iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.itemType != null" >
        itemType = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.childAsins != null" >
        childAsins = #{record.childAsins,jdbcType=VARCHAR},
      </if>
      <if test="record.packageQuantity != null" >
        packageQuantity = #{record.packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.searchTerms != null" >
        searchTerms = #{record.searchTerms,jdbcType=VARCHAR},
      </if>
      <if test="record.bulletPoint != null" >
        bulletPoint = #{record.bulletPoint,jdbcType=VARCHAR},
      </if>
      <if test="record.order_24H_count != null" >
        order_24H_count = #{record.order_24H_count,jdbcType=INTEGER},
      </if>
      <if test="record.order_last_7d_count != null" >
        order_last_7d_count = #{record.order_last_7d_count,jdbcType=INTEGER},
      </if>
      <if test="record.order_last_14d_count != null" >
        order_last_14d_count = #{record.order_last_14d_count,jdbcType=INTEGER},
      </if>
      <if test="record.order_last_30d_count != null" >
        order_last_30d_count = #{record.order_last_30d_count,jdbcType=INTEGER},
      </if>
      <if test="record.order_num_total != null" >
        order_num_total = #{record.order_num_total,jdbcType=INTEGER},
      </if>
      <if test="record.order_days_within_30d != null" >
        order_days_within_30d = #{record.order_days_within_30d,jdbcType=INTEGER},
      </if>
      <if test="record.infringementWord != null" >
        infringementWord = #{record.infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementWordInfos != null" >
        infringementWordInfos = #{record.infringementWordInfos,jdbcType=VARCHAR},
      </if>
      <if test="record.updateInfringementTime != null" >
        updateInfringementTime = #{record.updateInfringementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleNo != null" >
        saleNo = #{record.saleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.saleName != null" >
        saleName = #{record.saleName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountLevel != null" >
        accountLevel = #{record.accountLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.fba != null" >
        fba = #{record.fba,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOffline" >
    update amazon_product_listing_offline
    <set >
      <if test="accountNumber != null" >
        accountNumber = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parentAsin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null" >
        sonAsin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null" >
        sellerSku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        mainSku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        articleNumber = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null" >
        skuDataSource = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="itemStatus != null" >
        itemStatus = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null" >
        isOnline = #{isOnline,jdbcType=BIT},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        itemName = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null" >
        itemDescription = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="forbidChannel != null" >
        forbidChannel = #{forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="skuStatus != null" >
        skuStatus = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="tagCodes != null" >
        tagCodes = #{tagCodes,jdbcType=VARCHAR},
      </if>
      <if test="tagNames != null" >
        tagNames = #{tagNames,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsCode != null" >
        specialGoodsCode = #{specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsName != null" >
        specialGoodsName = #{specialGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="itemIsMarketplace != null" >
        itemIsMarketplace = #{itemIsMarketplace,jdbcType=VARCHAR},
      </if>
      <if test="itemCondition != null" >
        itemCondition = #{itemCondition,jdbcType=VARCHAR},
      </if>
      <if test="zshopCategory != null" >
        zshopCategory = #{zshopCategory,jdbcType=VARCHAR},
      </if>
      <if test="productIdType != null" >
        productIdType = #{productIdType,jdbcType=INTEGER},
      </if>
      <if test="productId != null" >
        productId = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null" >
        sampleImage = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        extraImages = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="grossProfitRate != null" >
        grossProfitRate = #{grossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="grossProfit != null" >
        grossProfit = #{grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="saleQuantity != null" >
        saleQuantity = #{saleQuantity,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null" >
        salePrice = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="saleStartDate != null" >
        saleStartDate = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null" >
        saleEndDate = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lowestPrice != null" >
        lowestPrice = #{lowestPrice,jdbcType=DOUBLE},
      </if>
      <if test="isPopular != null" >
        isPopular = #{isPopular,jdbcType=VARCHAR},
      </if>
      <if test="isFollowSellDelete != null" >
        isFollowSellDelete = #{isFollowSellDelete,jdbcType=BIT},
      </if>
      <if test="followSaleFlag != null" >
        followSaleFlag = #{followSaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="listingId != null" >
        listingId = #{listingId,jdbcType=VARCHAR},
      </if>
      <if test="skuLifeCyclePhase != null" >
        skuLifeCyclePhase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="merchantShippingGroup != null" >
        merchantShippingGroup = #{merchantShippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null" >
        shippingCost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="totalPrice != null" >
        totalPrice = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="identifierType != null" >
        identifierType = #{identifierType,jdbcType=VARCHAR},
      </if>
      <if test="identifier != null" >
        identifier = #{identifier,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        productType = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brandName = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="browseNode != null" >
        browseNode = #{browseNode,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null" >
        colorName = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null" >
        modelNumber = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="sizeName != null" >
        sizeName = #{sizeName,jdbcType=VARCHAR},
      </if>
      <if test="styleName != null" >
        styleName = #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        categoryId = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCnName != null" >
        categoryCnName = #{categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="relationTemplateId != null" >
        relationTemplateId = #{relationTemplateId,jdbcType=INTEGER},
      </if>
      <if test="autoUpdateMsgDate != null" >
        autoUpdateMsgDate = #{autoUpdateMsgDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustPriceDate != null" >
        lastAdjustPriceDate = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportOpenDate != null" >
        reportOpenDate = #{reportOpenDate,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null" >
        openDate = #{openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOpenDate != null" >
        firstOpenDate = #{firstOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineDate != null" >
        offlineDate = #{offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOfflineDate != null" >
        firstOfflineDate = #{firstOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="syncDate != null" >
        syncDate = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        createdBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        createDate = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        updateDate = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        updatedBy = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null" >
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null" >
        attribute7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypename != null" >
        infringementTypename = #{infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null" >
        infringementObj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="normalSale != null" >
        normalSale = #{normalSale,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null" >
        publishRole = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="fulfillmentLatency != null" >
        fulfillmentLatency = #{fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="composeStatus != null" >
        composeStatus = #{composeStatus,jdbcType=INTEGER},
      </if>
      <if test="newState != null" >
        newState = #{newState,jdbcType=BIT},
      </if>
      <if test="promotion != null" >
        promotion = #{promotion,jdbcType=INTEGER},
      </if>
      <if test="issuesSeverity != null" >
        issuesSeverity = #{issuesSeverity,jdbcType=VARCHAR},
      </if>
      <if test="itemSummariesStastus != null" >
        itemSummariesStastus = #{itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null" >
        conditionType = #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="iteamLastUpdatedDate != null" >
        iteamLastUpdatedDate = #{iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null" >
        itemType = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="childAsins != null" >
        childAsins = #{childAsins,jdbcType=VARCHAR},
      </if>
      <if test="packageQuantity != null" >
        packageQuantity = #{packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="searchTerms != null" >
        searchTerms = #{searchTerms,jdbcType=VARCHAR},
      </if>
      <if test="bulletPoint != null" >
        bulletPoint = #{bulletPoint,jdbcType=VARCHAR},
      </if>
      <if test="order_24H_count != null" >
        order_24H_count = #{order_24H_count,jdbcType=INTEGER},
      </if>
      <if test="order_last_7d_count != null" >
        order_last_7d_count = #{order_last_7d_count,jdbcType=INTEGER},
      </if>
      <if test="order_last_14d_count != null" >
        order_last_14d_count = #{order_last_14d_count,jdbcType=INTEGER},
      </if>
      <if test="order_last_30d_count != null" >
        order_last_30d_count = #{order_last_30d_count,jdbcType=INTEGER},
      </if>
      <if test="order_num_total != null" >
        order_num_total = #{order_num_total,jdbcType=INTEGER},
      </if>
      <if test="order_days_within_30d != null" >
        order_days_within_30d = #{order_days_within_30d,jdbcType=INTEGER},
      </if>
      <if test="infringementWord != null" >
        infringementWord = #{infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="infringementWordInfos != null" >
        infringementWordInfos = #{infringementWordInfos,jdbcType=VARCHAR},
      </if>
      <if test="updateInfringementTime != null" >
        updateInfringementTime = #{updateInfringementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleNo != null" >
        saleNo = #{saleNo,jdbcType=VARCHAR},
      </if>
      <if test="saleName != null" >
        saleName = #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="accountLevel != null" >
        accountLevel = #{accountLevel,jdbcType=VARCHAR},
      </if>
      <if test="fba != null" >
        fba = #{fba,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectIdListByExample" resultType="java.lang.Long"  parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample" >
    select id from amazon_product_listing_offline
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap"  parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineExample" >
    select ${filedColumns} from amazon_product_listing_offline
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <insert id="batchInsert">
    insert into amazon_product_listing_offline
    (accountNumber, site, parentAsin,
    sonAsin, sellerSku, mainSku,
    articleNumber, skuDataSource, itemStatus,
    isOnline, `name`, itemName,
    itemDescription, forbidChannel, skuStatus,
    tagCodes, tagNames, specialGoodsCode,
    specialGoodsName, itemIsMarketplace, itemCondition,
    zshopCategory, productIdType, productId,
    mainImage, sampleImage, extraImages,
    price, grossProfitRate, grossProfit,
    quantity, saleQuantity, salePrice,
    saleStartDate, saleEndDate, lowestPrice,
    isPopular, isFollowSellDelete, followSaleFlag,
    listingId, skuLifeCyclePhase, merchantShippingGroup,
    shippingCost, totalPrice, identifierType,
    identifier, productType, brandName,
    browseNode, colorName, manufacturer,
    modelNumber, sizeName, styleName,
    categoryId, categoryCnName, relationTemplateId,
    autoUpdateMsgDate, lastAdjustPriceDate,
    reportOpenDate, openDate, firstOpenDate,
    offlineDate, firstOfflineDate, syncDate,
    createdBy, createDate, updateDate,
    updatedBy, attribute1, attribute2,
    attribute3, attribute4, attribute5,
    attribute6, attribute7, infringementTypename,
    infringementObj, normalSale, publishRole,
    fulfillmentLatency, composeStatus, newState,
    promotion, issuesSeverity, itemSummariesStastus,
    conditionType, iteamLastUpdatedDate,
    itemType, childAsins, packageQuantity,
    searchTerms, bulletPoint, order_24H_count,
    order_last_7d_count, order_last_14d_count,
    order_last_30d_count, order_num_total,
    order_days_within_30d, infringementWord,
    infringementWordInfos, updateInfringementTime,
    saleNo, saleName, accountLevel,fba)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR},
      #{item.sonAsin,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.mainSku,jdbcType=VARCHAR},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.skuDataSource,jdbcType=INTEGER}, #{item.itemStatus,jdbcType=VARCHAR},
      #{item.isOnline,jdbcType=BIT}, #{item.name,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
      #{item.itemDescription,jdbcType=VARCHAR}, #{item.forbidChannel,jdbcType=VARCHAR}, #{item.skuStatus,jdbcType=VARCHAR},
      #{item.tagCodes,jdbcType=VARCHAR}, #{item.tagNames,jdbcType=VARCHAR}, #{item.specialGoodsCode,jdbcType=VARCHAR},
      #{item.specialGoodsName,jdbcType=VARCHAR}, #{item.itemIsMarketplace,jdbcType=VARCHAR}, #{item.itemCondition,jdbcType=VARCHAR},
      #{item.zshopCategory,jdbcType=VARCHAR}, #{item.productIdType,jdbcType=INTEGER}, #{item.productId,jdbcType=VARCHAR},
      #{item.mainImage,jdbcType=VARCHAR}, #{item.sampleImage,jdbcType=VARCHAR}, #{item.extraImages,jdbcType=VARCHAR},
      #{item.price,jdbcType=DOUBLE}, #{item.grossProfitRate,jdbcType=DOUBLE}, #{item.grossProfit,jdbcType=DOUBLE},
      #{item.quantity,jdbcType=INTEGER}, #{item.saleQuantity,jdbcType=INTEGER}, #{item.salePrice,jdbcType=DOUBLE},
      #{item.saleStartDate,jdbcType=TIMESTAMP}, #{item.saleEndDate,jdbcType=TIMESTAMP}, #{item.lowestPrice,jdbcType=DOUBLE},
      #{item.isPopular,jdbcType=VARCHAR}, #{item.isFollowSellDelete,jdbcType=BIT}, #{item.followSaleFlag,jdbcType=VARCHAR},
      #{item.listingId,jdbcType=VARCHAR}, #{item.skuLifeCyclePhase,jdbcType=VARCHAR}, #{item.merchantShippingGroup,jdbcType=VARCHAR},
      #{item.shippingCost,jdbcType=DOUBLE}, #{item.totalPrice,jdbcType=DOUBLE}, #{item.identifierType,jdbcType=VARCHAR},
      #{item.identifier,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR},
      #{item.browseNode,jdbcType=VARCHAR}, #{item.colorName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.modelNumber,jdbcType=VARCHAR}, #{item.sizeName,jdbcType=VARCHAR}, #{item.styleName,jdbcType=VARCHAR},
      #{item.categoryId,jdbcType=VARCHAR}, #{item.categoryCnName,jdbcType=VARCHAR}, #{item.relationTemplateId,jdbcType=INTEGER},
      #{item.autoUpdateMsgDate,jdbcType=TIMESTAMP}, #{item.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      #{item.reportOpenDate,jdbcType=VARCHAR}, #{item.openDate,jdbcType=TIMESTAMP}, #{item.firstOpenDate,jdbcType=TIMESTAMP},
      #{item.offlineDate,jdbcType=TIMESTAMP}, #{item.firstOfflineDate,jdbcType=TIMESTAMP}, #{item.syncDate,jdbcType=TIMESTAMP},
      #{item.createdBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updateDate,jdbcType=TIMESTAMP},
      #{item.updatedBy,jdbcType=VARCHAR}, #{item.attribute1,jdbcType=VARCHAR}, #{item.attribute2,jdbcType=VARCHAR},
      #{item.attribute3,jdbcType=VARCHAR}, #{item.attribute4,jdbcType=VARCHAR}, #{item.attribute5,jdbcType=VARCHAR},
      #{item.attribute6,jdbcType=VARCHAR}, #{item.attribute7,jdbcType=VARCHAR}, #{item.infringementTypename,jdbcType=VARCHAR},
      #{item.infringementObj,jdbcType=VARCHAR}, #{item.normalSale,jdbcType=VARCHAR}, #{item.publishRole,jdbcType=INTEGER},
      #{item.fulfillmentLatency,jdbcType=INTEGER}, #{item.composeStatus,jdbcType=INTEGER}, #{item.newState,jdbcType=BIT},
      #{item.promotion,jdbcType=INTEGER}, #{item.issuesSeverity,jdbcType=VARCHAR}, #{item.itemSummariesStastus,jdbcType=VARCHAR},
      #{item.conditionType,jdbcType=VARCHAR}, #{item.iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      #{item.itemType,jdbcType=INTEGER}, #{item.childAsins,jdbcType=VARCHAR}, #{item.packageQuantity,jdbcType=INTEGER},
      #{item.searchTerms,jdbcType=VARCHAR}, #{item.bulletPoint,jdbcType=VARCHAR}, #{item.order_24H_count,jdbcType=INTEGER},
      #{item.order_last_7d_count,jdbcType=INTEGER}, #{item.order_last_14d_count,jdbcType=INTEGER},
      #{item.order_last_30d_count,jdbcType=INTEGER}, #{item.order_num_total,jdbcType=INTEGER},
      #{item.order_days_within_30d,jdbcType=INTEGER}, #{item.infringementWord,jdbcType=VARCHAR},
      #{item.infringementWordInfos,jdbcType=VARCHAR}, #{item.updateInfringementTime,jdbcType=TIMESTAMP},
      #{item.saleNo,jdbcType=VARCHAR}, #{item.saleName,jdbcType=VARCHAR}, #{item.accountLevel,jdbcType=VARCHAR},
      #{item.fba,jdbcType=BIT}
      )
    </foreach>
  </insert>
</mapper>