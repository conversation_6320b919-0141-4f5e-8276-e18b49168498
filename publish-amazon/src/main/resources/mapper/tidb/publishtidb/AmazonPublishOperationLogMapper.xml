<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonPublishOperationLogMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="mod_id" property="modId" jdbcType="VARCHAR"/>
        <result column="op_type" property="opType" jdbcType="VARCHAR"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="user" property="user" jdbcType="VARCHAR"/>
        <result column="object" property="object" jdbcType="VARCHAR"/>
        <result column="object1" property="object1" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="meta_obj" property="metaObj" jdbcType="VARCHAR"/>
        <result column="before_obj" property="beforeObj" jdbcType="VARCHAR"/>
        <result column="after_obj" property="afterObj" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , mod_id, op_type, platform, `user`, `object`, object1, `state`, meta_obj, before_obj,
    after_obj, created_time
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLogExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from amazon_publish_operation_log
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from amazon_publish_operation_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey">
        delete from amazon_publish_operation_log
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_publish_operation_log (mod_id, op_type, platform,
        `user`, `object`, object1,
        `state`, meta_obj, before_obj,
        after_obj, created_time)
        values (#{modId,jdbcType=VARCHAR}, #{opType,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR},
        #{user,jdbcType=VARCHAR}, #{object,jdbcType=VARCHAR}, #{object1,jdbcType=VARCHAR},
        #{state,jdbcType=INTEGER}, #{metaObj,jdbcType=VARCHAR}, #{beforeObj,jdbcType=VARCHAR},
        #{afterObj,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP})
    </insert>
    <!--批量新增-->
    <insert id="insertBatch">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_publish_operation_log (mod_id, op_type, platform,
        `user`, `object`, object1,
        `state`, meta_obj, before_obj,
        after_obj, created_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.modId,jdbcType=VARCHAR}, #{item.opType,jdbcType=VARCHAR}, #{item.platform,jdbcType=VARCHAR},
            #{item.user,jdbcType=VARCHAR}, #{item.object,jdbcType=VARCHAR}, #{item.object1,jdbcType=VARCHAR},
            #{item.state,jdbcType=INTEGER}, #{item.metaObj,jdbcType=VARCHAR}, #{item.beforeObj,jdbcType=VARCHAR},
            #{item.afterObj,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLogExample"
            resultType="java.lang.Integer">
        select count(*) from amazon_publish_operation_log
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update amazon_publish_operation_log
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.modId != null">
                mod_id = #{record.modId,jdbcType=VARCHAR},
            </if>
            <if test="record.opType != null">
                op_type = #{record.opType,jdbcType=VARCHAR},
            </if>
            <if test="record.platform != null">
                platform = #{record.platform,jdbcType=VARCHAR},
            </if>
            <if test="record.user != null">
                `user` = #{record.user,jdbcType=VARCHAR},
            </if>
            <if test="record.object != null">
                `object` = #{record.object,jdbcType=VARCHAR},
            </if>
            <if test="record.object1 != null">
                object1 = #{record.object1,jdbcType=VARCHAR},
            </if>
            <if test="record.state != null">
                `state` = #{record.state,jdbcType=INTEGER},
            </if>
            <if test="record.metaObj != null">
                meta_obj = #{record.metaObj,jdbcType=VARCHAR},
            </if>
            <if test="record.beforeObj != null">
                before_obj = #{record.beforeObj,jdbcType=VARCHAR},
            </if>
            <if test="record.afterObj != null">
                after_obj = #{record.afterObj,jdbcType=VARCHAR},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog">
        update amazon_publish_operation_log
        <set>
            <if test="modId != null">
                mod_id = #{modId,jdbcType=VARCHAR},
            </if>
            <if test="opType != null">
                op_type = #{opType,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="user != null">
                `user` = #{user,jdbcType=VARCHAR},
            </if>
            <if test="object != null">
                `object` = #{object,jdbcType=VARCHAR},
            </if>
            <if test="object1 != null">
                object1 = #{object1,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="metaObj != null">
                meta_obj = #{metaObj,jdbcType=VARCHAR},
            </if>
            <if test="beforeObj != null">
                before_obj = #{beforeObj,jdbcType=VARCHAR},
            </if>
            <if test="afterObj != null">
                after_obj = #{afterObj,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="resetGpsrOperationLog">
        update amazon_publish_operation_log
        set state = 2
        where op_type = 'LISTING_UPDATE_GPSR'
          and state = 1
    </update>

    <update id="updateSelectiveIdVersion">
        update amazon_publish_operation_log
        <set>
            <if test="record.modId != null">
                mod_id = #{record.modId,jdbcType=VARCHAR},
            </if>
            <if test="record.opType != null">
                op_type = #{record.opType,jdbcType=VARCHAR},
            </if>
            <if test="record.platform != null">
                platform = #{record.platform,jdbcType=VARCHAR},
            </if>
            <if test="record.user != null">
                `user` = #{record.user,jdbcType=VARCHAR},
            </if>
            <if test="record.object != null">
                `object` = #{record.object,jdbcType=VARCHAR},
            </if>
            <if test="record.object1 != null">
                object1 = #{record.object1,jdbcType=VARCHAR},
            </if>
            <if test="record.state != null">
                `state` = #{record.state,jdbcType=INTEGER},
            </if>
            <if test="record.metaObj != null">
                meta_obj = #{record.metaObj,jdbcType=VARCHAR},
            </if>
            <if test="record.beforeObj != null">
                before_obj = #{record.beforeObj,jdbcType=VARCHAR},
            </if>
            <if test="record.afterObj != null">
                after_obj = #{record.afterObj,jdbcType=VARCHAR},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{record.id,jdbcType=BIGINT} and mod_id = #{version,jdbcType=VARCHAR}
    </update>
</mapper>