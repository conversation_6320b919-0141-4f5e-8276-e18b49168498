<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyStatisticsLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="relation_id" property="relationId" jdbcType="BIGINT" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="sale_id" property="saleId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, relation_id, spu, sale_id, `type`, create_by, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_statistics_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_statistics_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_infringement_word_frequency_statistics_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_infringement_word_frequency_statistics_log (relation_id, spu, sale_id, 
      `type`, create_by, create_time
      )
    values (#{relationId,jdbcType=BIGINT}, #{spu,jdbcType=VARCHAR}, #{saleId,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="batchInsert">
    <foreach collection="list" item="record" separator=";">
      insert into amazon_infringement_word_frequency_statistics_log (relation_id, spu, sale_id,
      `type`, create_by, create_time
      )
      values (#{record.relationId,jdbcType=BIGINT}, #{record.spu,jdbcType=VARCHAR}, #{record.saleId,jdbcType=VARCHAR},
      #{record.type,jdbcType=INTEGER}, #{record.createBy,jdbcType=VARCHAR}, #{record.createTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_infringement_word_frequency_statistics_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_infringement_word_frequency_statistics_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.relationId != null" >
        relation_id = #{record.relationId,jdbcType=BIGINT},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.saleId != null" >
        sale_id = #{record.saleId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog" >
    update amazon_infringement_word_frequency_statistics_log
    <set >
      <if test="relationId != null" >
        relation_id = #{relationId,jdbcType=BIGINT},
      </if>
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="saleId != null" >
        sale_id = #{saleId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>