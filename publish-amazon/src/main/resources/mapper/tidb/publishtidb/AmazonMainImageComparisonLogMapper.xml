<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonMainImageComparisonLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="request" property="request" jdbcType="VARCHAR" />
    <result column="response" property="response" jdbcType="VARCHAR" />
    <result column="full" property="full" jdbcType="BIT" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="error_type" property="errorType" jdbcType="INTEGER" />
    <result column="es_listing_id" property="esListingId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="main_sku_url" property="mainSkuUrl" jdbcType="VARCHAR"/>
    <result column="max_sim" property="maxSim" jdbcType="DECIMAL"/>
    <result column="max_sim_url" property="maxSimUrl" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, site, main_sku, article_number, request, response, `full`, `status`,
    error_type, es_listing_id, create_time, main_sku_url, max_sim, max_sim_url
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_main_image_comparison_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from amazon_main_image_comparison_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_main_image_comparison_log
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_main_image_comparison_log (account_number, site, main_sku,
    article_number, request, response,
    `full`, `status`, error_type, es_listing_id,
    create_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR},
    #{articleNumber,jdbcType=VARCHAR}, #{request,jdbcType=VARCHAR}, #{response,jdbcType=VARCHAR},
    #{full,jdbcType=BIT}, #{status,jdbcType=BIT}, #{errorType,jdbcType=INTEGER}, #{esListingId,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLogExample" resultType="java.lang.Integer" >
    select count(*) from amazon_main_image_comparison_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_main_image_comparison_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.request != null" >
        request = #{record.request,jdbcType=VARCHAR},
      </if>
      <if test="record.response != null" >
        response = #{record.response,jdbcType=VARCHAR},
      </if>
      <if test="record.full != null" >
        `full` = #{record.full,jdbcType=BIT},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.errorType != null" >
        error_type = #{record.errorType,jdbcType=INTEGER},
      </if>
      <if test="record.esListingId != null" >
        es_listing_id = #{record.esListingId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonMainImageComparisonLog" >
    update amazon_main_image_comparison_log
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="request != null" >
        request = #{request,jdbcType=VARCHAR},
      </if>
      <if test="response != null" >
        response = #{response,jdbcType=VARCHAR},
      </if>
      <if test="full != null" >
        `full` = #{full,jdbcType=BIT},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="errorType != null" >
        error_type = #{errorType,jdbcType=INTEGER},
      </if>
      <if test="esListingId != null" >
        es_listing_id = #{esListingId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert into amazon_main_image_comparison_log${table} (account_number, site, main_sku,
    article_number, request, response,
    `full`, `status`, error_type, es_listing_id,
    create_time, max_sim, max_sim_url, main_sku_url)
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.mainSku,jdbcType=VARCHAR},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.request,jdbcType=VARCHAR}, #{item.response,jdbcType=VARCHAR},
      #{item.full,jdbcType=BIT}, #{item.status,jdbcType=BIT}, #{item.errorType,jdbcType=INTEGER}, #{item.esListingId,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.maxSim,jdbcType=DECIMAL}, #{item.maxSimUrl,jdbcType=VARCHAR}, #{item.mainSkuUrl, jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="existFullAccountLog" resultType="int">
    select count(*) from amazon_main_image_comparison_log${table}
    where account_number = #{accountNumber} and create_time > #{openDate} and full = true
    limit 1
  </select>

  <select id="existListingId" resultType="java.lang.String">
    select distinct es_listing_id from amazon_main_image_comparison_log${table}
    where es_listing_id in
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </select>
</mapper>