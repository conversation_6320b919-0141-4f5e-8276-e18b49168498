<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="sale_id" property="saleId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="origin_word" property="originWord" jdbcType="VARCHAR" />
    <result column="trademark_word" property="trademarkWord" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, spu, sale_id, `type`, origin_word, trademark_word, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from amazon_infringement_word_frequency_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from amazon_infringement_word_frequency_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_infringement_word_frequency_log (spu, sale_id, `type`, 
      origin_word, trademark_word, create_time
      )
    values (#{spu,jdbcType=VARCHAR}, #{saleId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{originWord,jdbcType=VARCHAR}, #{trademarkWord,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="batchInsert">
    insert into amazon_infringement_word_frequency_log (spu, sale_id, `type`,
    origin_word, trademark_word, create_time
    )
    values
    <foreach collection="list" item="record" separator=",">
      (#{record.spu,jdbcType=VARCHAR}, #{record.saleId,jdbcType=VARCHAR}, #{record.type,jdbcType=INTEGER},
      #{record.originWord,jdbcType=VARCHAR}, #{record.trademarkWord,jdbcType=VARCHAR}, #{record.createTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogExample" resultType="java.lang.Integer" >
  select count(*) from amazon_infringement_word_frequency_log
  <if test="_parameter != null" >
    <include refid="Example_Where_Clause" />
  </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update amazon_infringement_word_frequency_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.saleId != null" >
        sale_id = #{record.saleId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.originWord != null" >
        origin_word = #{record.originWord,jdbcType=VARCHAR},
      </if>
      <if test="record.trademarkWord != null" >
        trademark_word = #{record.trademarkWord,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog" >
    update amazon_infringement_word_frequency_log
    <set >
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="saleId != null" >
        sale_id = #{saleId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="originWord != null" >
        origin_word = #{originWord,jdbcType=VARCHAR},
      </if>
      <if test="trademarkWord != null" >
        trademark_word = #{trademarkWord,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getPageMetaListByExample" resultType="map" parameterType="com.estone.erp.publish.platform.model.TemplateRecordExample" >
    SELECT
    floor((t.row_num - 1) / 300) + 1 AS page_num,
    min(t.id) AS start_key,
    max(t.id) AS end_key,
    count(*) AS page_size
    FROM (
    SELECT id, row_number() OVER (ORDER BY id) AS row_num
    FROM amazon_infringement_word_frequency_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    ) t
    GROUP BY page_num
    ORDER BY page_num;
  </select>
</mapper>