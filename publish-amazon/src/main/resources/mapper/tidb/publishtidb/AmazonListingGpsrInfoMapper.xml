<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AmazonListingGpsrInfoMapper">
    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
        floor((t.row_num - 1) / 300) + 1 AS page_num,
        min(t.id) AS start_key,
        max(t.id) AS end_key,
        count(*) AS page_size
        FROM
        (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
        FROM amazon_listing_gpsr_info
        ${ew.customSqlSegment}
        ) t
        GROUP BY
        page_num
        ORDER BY
        page_num;
    </select>
</mapper>
