<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishAmazon.mapper.TidbAmazonProductListingMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="accountNumber" jdbcType="VARCHAR" property="accountNumber" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="parentAsin" jdbcType="VARCHAR" property="parentAsin" />
    <result column="sonAsin" jdbcType="VARCHAR" property="sonAsin" />
    <result column="sellerSku" jdbcType="VARCHAR" property="sellerSku" />
    <result column="mainSku" jdbcType="VARCHAR" property="mainSku" />
    <result column="articleNumber" jdbcType="VARCHAR" property="articleNumber" />
    <result column="skuDataSource" jdbcType="INTEGER" property="skuDataSource" />
    <result column="itemStatus" jdbcType="INTEGER" property="itemStatus" />
    <result column="isOnline" jdbcType="BIT" property="isOnline" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="itemName" jdbcType="VARCHAR" property="itemName" />
    <result column="itemDescription" jdbcType="VARCHAR" property="itemDescription" />
    <result column="infringementWord" jdbcType="VARCHAR" property="infringementWord" />
    <result column="forbidChannel" jdbcType="VARCHAR" property="forbidChannel" />
    <result column="skuStatus" jdbcType="VARCHAR" property="skuStatus" />
    <result column="tagCodes" jdbcType="VARCHAR" property="tagCodes" />
    <result column="tagNames" jdbcType="VARCHAR" property="tagNames" />
    <result column="specialGoodsCode" jdbcType="VARCHAR" property="specialGoodsCode" />
    <result column="specialGoodsName" jdbcType="VARCHAR" property="specialGoodsName" />
    <result column="itemIsMarketplace" jdbcType="VARCHAR" property="itemIsMarketplace" />
    <result column="itemCondition" jdbcType="VARCHAR" property="itemCondition" />
    <result column="zshopCategory" jdbcType="VARCHAR" property="zshopCategory" />
    <result column="productIdType" jdbcType="INTEGER" property="productIdType" />
    <result column="productId" jdbcType="VARCHAR" property="productId" />
    <result column="mainImage" jdbcType="VARCHAR" property="mainImage" />
    <result column="sampleImage" jdbcType="VARCHAR" property="sampleImage" />
    <result column="extraImages" jdbcType="VARCHAR" property="extraImages" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="grossProfitRate" jdbcType="DOUBLE" property="grossProfitRate" />
    <result column="grossProfit" jdbcType="DOUBLE" property="grossProfit" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="saleQuantity" jdbcType="INTEGER" property="saleQuantity" />
    <result column="salePrice" jdbcType="DOUBLE" property="salePrice" />
    <result column="saleStartDate" jdbcType="TIMESTAMP" property="saleStartDate" />
    <result column="saleEndDate" jdbcType="TIMESTAMP" property="saleEndDate" />
    <result column="lowestPrice" jdbcType="DOUBLE" property="lowestPrice" />
    <result column="isPopular" jdbcType="VARCHAR" property="isPopular" />
    <result column="isFollowSellDelete" jdbcType="BIT" property="isFollowSellDelete" />
    <result column="followSaleFlag" jdbcType="VARCHAR" property="followSaleFlag" />
    <result column="listingId" jdbcType="VARCHAR" property="listingId" />
    <result column="skuLifeCyclePhase" jdbcType="VARCHAR" property="skuLifeCyclePhase" />
    <result column="merchantShippingGroup" jdbcType="VARCHAR" property="merchantShippingGroup" />
    <result column="shippingCost" jdbcType="DOUBLE" property="shippingCost" />
    <result column="totalPrice" jdbcType="DOUBLE" property="totalPrice" />
    <result column="identifierType" jdbcType="VARCHAR" property="identifierType" />
    <result column="identifier" jdbcType="VARCHAR" property="identifier" />
    <result column="productType" jdbcType="VARCHAR" property="productType" />
    <result column="brandName" jdbcType="VARCHAR" property="brandName" />
    <result column="browseNode" jdbcType="VARCHAR" property="browseNode" />
    <result column="colorName" jdbcType="VARCHAR" property="colorName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="modelNumber" jdbcType="VARCHAR" property="modelNumber" />
    <result column="sizeName" jdbcType="VARCHAR" property="sizeName" />
    <result column="styleName" jdbcType="VARCHAR" property="styleName" />
    <result column="categoryId" jdbcType="VARCHAR" property="categoryId" />
    <result column="categoryCnName" jdbcType="VARCHAR" property="categoryCnName" />
    <result column="relationTemplateId" jdbcType="INTEGER" property="relationTemplateId" />
    <result column="autoUpdateMsgDate" jdbcType="TIMESTAMP" property="autoUpdateMsgDate" />
    <result column="lastAdjustPriceDate" jdbcType="TIMESTAMP" property="lastAdjustPriceDate" />
    <result column="reportOpenDate" jdbcType="VARCHAR" property="reportOpenDate" />
    <result column="openDate" jdbcType="TIMESTAMP" property="openDate" />
    <result column="firstOpenDate" jdbcType="TIMESTAMP" property="firstOpenDate" />
    <result column="offlineDate" jdbcType="TIMESTAMP" property="offlineDate" />
    <result column="firstOfflineDate" jdbcType="TIMESTAMP" property="firstOfflineDate" />
    <result column="syncDate" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
    <result column="createDate" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updateDate" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="updatedBy" jdbcType="VARCHAR" property="updatedBy" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
    <result column="attribute4" jdbcType="VARCHAR" property="attribute4" />
    <result column="attribute5" jdbcType="VARCHAR" property="attribute5" />
    <result column="attribute6" jdbcType="VARCHAR" property="attribute6" />
    <result column="attribute7" jdbcType="VARCHAR" property="attribute7" />
    <result column="infringementTypename" jdbcType="VARCHAR" property="infringementTypename" />
    <result column="infringementObj" jdbcType="VARCHAR" property="infringementObj" />
    <result column="normalSale" jdbcType="VARCHAR" property="normalSale" />
    <result column="publishRole" jdbcType="INTEGER" property="publishRole" />
    <result column="fulfillmentLatency" jdbcType="INTEGER" property="fulfillmentLatency" />
    <result column="compose_status" jdbcType="INTEGER" property="composeStatus" />
    <result column="promotion" jdbcType="INTEGER" property="promotion" />
    <result column="newState" jdbcType="BIT" property="newState" />
    <result column="issuesSeverity" property="issuesSeverity" jdbcType="VARCHAR" />
    <result column="itemSummariesStastus" property="itemSummariesStastus" jdbcType="VARCHAR" />
    <result column="conditionType" property="conditionType" jdbcType="VARCHAR" />
    <result column="iteamLastUpdatedDate" property="iteamLastUpdatedDate" jdbcType="TIMESTAMP" />
    <result column="itemType" property="itemType" jdbcType="INTEGER" />
    <result column="childAsins" property="childAsins" jdbcType="VARCHAR" />
    <result column="packageQuantity" property="packageQuantity" jdbcType="INTEGER" />
    <result column="searchTerms" property="searchTerms" jdbcType="VARCHAR" />
    <result column="bulletPoint" property="bulletPoint" jdbcType="VARCHAR" />
    <result column="riskLevelId" jdbcType="INTEGER" property="riskLevelId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  id, accountNumber, site, parentAsin, sonAsin, sellerSku, mainSku, articleNumber, skuDataSource,
  itemStatus, isOnline, `name`, itemName, itemDescription, infringementWord, forbidChannel, skuStatus,
  tagCodes, tagNames, specialGoodsCode, specialGoodsName, itemIsMarketplace, itemCondition,
  zshopCategory, productIdType, productId, mainImage, sampleImage, extraImages, price,grossProfitRate,grossProfit, quantity, saleQuantity,
  salePrice, saleStartDate, saleEndDate, lowestPrice, isPopular, isFollowSellDelete,
  followSaleFlag, listingId, skuLifeCyclePhase, merchantShippingGroup, shippingCost, totalPrice, identifierType, identifier,
  productType, brandName, browseNode, colorName, manufacturer, modelNumber, sizeName, styleName, categoryId,
  categoryCnName, relationTemplateId, autoUpdateMsgDate, lastAdjustPriceDate, reportOpenDate,
  openDate, firstOpenDate, offlineDate, firstOfflineDate, syncDate, createdBy, createDate,
  updateDate, updatedBy, attribute1, attribute2, attribute3, attribute4, attribute5,
  attribute6, attribute7,infringementTypename,infringementObj,normalSale,publishRole,fulfillmentLatency,compose_status, promotion,
  newState, issuesSeverity, itemSummariesStastus, conditionType,IteamLastUpdatedDate, itemType, childAsins, packageQuantity,
  searchTerms,bulletPoint,riskLevelId
</sql>

  <select id="selectByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <!--查询自定义字段listing-->
  <select id="selectCustomColumnByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultMap="BaseResultMap">
    select ${columns}
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>


  <select id="selectMerchantShippingGroupByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultType="java.lang.String">
    select distinct(merchantShippingGroup)
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="countByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultType="java.lang.Integer">
    select count(*) from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

</mapper>