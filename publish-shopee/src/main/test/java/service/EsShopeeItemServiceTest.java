package service;

import com.estone.erp.publish.PublishShopeeApplication;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.UUID;

/**
 * EsShopeeItemService 的集成测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishShopeeApplication.class)
@ActiveProfiles("local")
public class EsShopeeItemServiceTest {

    @Autowired
    private EsShopeeItemService esShopeeItemService;

    private static final String TEST_ITEM_ID = "7390420399";

    /**
     * 测试 save 方法 (完全覆盖保存)
     */
    @Test
    void testSave() {
        // 1. 从ES获取一个现有对象作为模板
        EsShopeeItem originalItem = esShopeeItemService.findAllById(TEST_ITEM_ID);
        Assertions.assertNotNull(originalItem, "测试前置条件失败: 无法在ES中找到ID为 '" + TEST_ITEM_ID + "' 的文档。");

        // 2. 修改一个非订单统计字段
        String newName = "Mignona-Molde De Silicona Para Pendientes , Tachuelas , Joyas , Moldes De Resina Epoxi " + UUID.randomUUID();
        originalItem.setName(newName);
        // 确保修改一个字段，以便验证
        originalItem.setLastUpdateDate(new Date());

        // 3. 调用 save 方法
        esShopeeItemService.save(originalItem);

        // 4. 重新从ES获取数据
        EsShopeeItem updatedItem = esShopeeItemService.findAllById(TEST_ITEM_ID);

        // 5. 断言字段已被更新
        Assertions.assertEquals(newName, updatedItem.getName(), "save() 方法未能成功更新 name 字段。");
    }

    /**
     * 测试 savePreserveOrderStats 方法 (保留订单统计字段的保存)
     */
    @Test
    void testSavePreserveOrderStats() {
        // 1. 从ES获取一个现有对象作为模板
        EsShopeeItem originalItem = esShopeeItemService.findAllById(TEST_ITEM_ID);
        Assertions.assertNotNull(originalItem, "测试前置条件失败: 无法在ES中找到ID为 '" + TEST_ITEM_ID + "' 的文档。");

        // 为了保证测试的健壮性，我们先手动设置一个订单统计值并保存
        Integer originalOrderNumTotal = 100;
        originalItem.setOrderNumTotal(originalOrderNumTotal);
        esShopeeItemService.save(originalItem); // 使用常规save确保初始状态

        // 2. 记录原始订单统计字段的值
        EsShopeeItem itemBeforeUpdate = esShopeeItemService.findAllById(TEST_ITEM_ID);
        Integer recordedOrderNumTotal = itemBeforeUpdate.getOrderNumTotal();
        log.info("recordedOrderNumTotal: {}", recordedOrderNumTotal);
//        Assertions.assertEquals(originalOrderNumTotal, recordedOrderNumTotal, "前置条件设置失败，订单总数不一致。");


        // 3. 修改一个非订单统计字段，并"试图"修改一个订单统计字段
        String newName = "Mignona-Molde De Silicona Para Pendientes , Tachuelas , Joyas , Moldes De Resina Epoxi " + UUID.randomUUID();
        itemBeforeUpdate.setName(newName);
        itemBeforeUpdate.setOrderNumTotal(0); // 试图将订单数清零

        // 4. 调用 savePreserveOrderStats 方法
        esShopeeItemService.savePreserveOrderStats(itemBeforeUpdate);

        // 5. 再次从ES获取数据
        EsShopeeItem updatedItem = esShopeeItemService.findAllById(TEST_ITEM_ID);

        // 6. 断言
        // a. 非订单统计字段应该被更新
        log.info("updatedItem: {}", updatedItem.getName());
//        Assertions.assertEquals(newName, updatedItem.getName(), "savePreserveOrderStats() 方法未能成功更新 name 字段。");
        // b. 订单统计字段应该保持不变 (核心测试点)
        log.info("updatedItem: {}", updatedItem.getOrderNumTotal());
//        Assertions.assertEquals(recordedOrderNumTotal, updatedItem.getOrderNumTotal(), "savePreserveOrderStats() 方法未能保留 'orderNumTotal' 字段的原始值。");
    }
} 