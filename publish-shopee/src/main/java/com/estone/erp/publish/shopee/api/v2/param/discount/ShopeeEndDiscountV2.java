package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/12 12:06
 * @description 添加折扣
 */
@Getter
@Setter
public class ShopeeEndDiscountV2 implements RequestCommon {

    /** 折扣ID */
    @JSONField(name = "discount_id")
    private Long discountId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.END_DISCOUNT;
    }
}
