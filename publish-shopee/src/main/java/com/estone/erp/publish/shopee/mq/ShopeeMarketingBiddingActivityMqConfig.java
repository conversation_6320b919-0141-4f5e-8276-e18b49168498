package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeMarketingBiddingActivityMqConfig {
    private int shopeeMarketingBiddingActivityMqConsumers;
    private int shopeeMarketingBiddingActivityMqPrefetchCount;
    private boolean shopeeMarketingBiddingActivityMqListener;

    @Bean
    public VhQueue shopeeMarketingBiddingActivityQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_BIDDING_ACTIVITY_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding shopeeMarketingBiddingActivityQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_BIDDING_ACTIVITY_QUEUE, VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_MARKETING_BIDDING_ACTIVITY_KEY, null);
    }

    @Bean
    public ShopeeMarketingBiddingActivityMqListener shopeeMarketingBiddingActivityMqListener() {
        return new ShopeeMarketingBiddingActivityMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeMarketingBiddingActivityListenerContainer(
            ShopeeMarketingBiddingActivityMqListener shopeeMarketingBiddingActivityMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_MARKETING_BIDDING_ACTIVITY_QUEUE, shopeeMarketingBiddingActivityMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeMarketingBiddingActivityMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeMarketingBiddingActivityMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeMarketingBiddingActivityMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
