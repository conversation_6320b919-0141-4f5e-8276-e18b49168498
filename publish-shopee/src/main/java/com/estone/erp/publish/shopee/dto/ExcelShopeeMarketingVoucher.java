package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingActivitiesStatusConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingRewardTypeConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingVoucherPurposeConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingVoucherTypeConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/6/22 16:15
 */
@Data
public class ExcelShopeeMarketingVoucher {


    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家")
    private String merchantName;

    /**
     * 店铺账号
     */
    @ExcelProperty(value = "店铺")
    private String accountNumber;

    /**
     * 站点
     */
    @ExcelProperty(value = "站点")
    private String site;

    /**
     * 优惠券名称
     */
    @ExcelProperty(value = "优惠券名称")
    private String name;

    /**
     * 优惠券码
     */
    @ExcelProperty(value = "优惠券码")
    private String code;

    /**
     * 优惠券类型
     */
    @ExcelProperty(value = "优惠券类型", converter = ShopeeMarketingVoucherTypeConverter.class)
    private Integer voucherType;

    /**
     * 使用商品范围
     */
    @ExcelProperty(value = "使用商品范围")
    private String useScope;

    /**
     * 目标买家
     */
    @ExcelProperty(value = "目标买家", converter = ShopeeMarketingVoucherPurposeConverter.class)
    private Integer voucherPurpose;

    /**
     * 折扣类型
     */
    @ExcelProperty(value = "折扣类型", converter = ShopeeMarketingRewardTypeConverter.class)
    private Integer rewardType;

    /**
     * 折扣金额
     */
    @ExcelProperty(value = "折扣金额")
    private Double discountAmount;

    /**
     * 折扣百分比
     */
    @ExcelProperty(value = "折扣百分比")
    private Integer percentage;

    /**
     * 可使用总数
     */
    @ExcelProperty(value = "可使用总数")
    private Integer usageQuantity;

    /**
     * 已使用
     */
    @ExcelProperty(value = "已使用")
    private Integer currentUsage;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ShopeeMarketingActivitiesStatusConverter.class)
    private String status;

    /**
     * 优惠券领取开始时间
     */
    @ExcelProperty(value = "优惠券领取开始时间", converter = TimestampFormatConverter.class)
    private Timestamp startTime;

    /**
     * 优惠券领取结束时间
     */
    @ExcelProperty(value = "优惠券领取结束时间", converter = TimestampFormatConverter.class)
    private Timestamp endTime;

    /**
     * 销售
     */
    @ExcelProperty(value = "销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty(value = "销售组长")
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    /**
     * 同步时间
     */
    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp syncTime;

}
