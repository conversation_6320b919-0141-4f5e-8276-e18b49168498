package com.estone.erp.publish.shopee.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 营销活动 mq 队列
 */
@Data
public class MarketingTaskDto {

    /**
     * 任务id
     */
    private Integer marketingTaskId;

    /**
     * 处理报告id
     */
    private Integer feedTaskId;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 时间
     */
    private Date beginCountSuccessDate;

    /**
     * 最多添加商品
     */
    private Integer onePromotionMaximunItems;

    /**
     * 产品id列表
     */
    private List<String> productIdList;

    /**
     * 物流
     */
    private String logisticCode;

}
