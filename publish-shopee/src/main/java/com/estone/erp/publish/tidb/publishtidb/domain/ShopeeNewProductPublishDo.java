package com.estone.erp.publish.tidb.publishtidb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ShopeeNewProductPublishDo {

    /**
     * id集合
     */
    private List<String> idList;

    /**
     * spu集合
     */
    private List<String> spuList;

    /**
     * 单品状态
     */
    private List<String> itemStatusList;

    /**
     * 是否禁售
     */
    private Boolean isBanned;

    /**
     * 开始推送时间（产品系统推送给刊登系统的时间）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushTimeStart;

    /**
     * 结束推送时间（产品系统推送给刊登系统的时间）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushTimeEnd;

    /**
     * 开始进入单品时间（SPU进入产品系统管理单品时间）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enterProductTimeStart;

    /**
     * 结束进入单品时间（SPU进入产品系统管理单品时间）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enterProductTimeEnd;

    /**
     * X个站点开始刊登完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime sitePublishCompleteTimeStart;

    /**
     * X个站点结束刊登完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime sitePublishCompleteTimeEnd;

    /**
     * 开始 达成率
     */
    private Double fromOneListingNumberPercent;
    /**
     * 结束 达成率
     */
    private Double toOneListingNumberPercent;

    /**
     * 开始 分站点达成率
     */
    private Double fromSiteFromOneListingNumberPercent;

    /**
     * 结束 分站点达成率
     */
    private Double toSiteFromOneListingNumberPercent;

    /**
     * 开始 近7天在线连接数
     */
    private Integer fromOneListingNumber;
    /**
     * 结束 近7天在线连接数
     */
    private Integer toOneListingNumber;

    /**
     * 开始 近30天在线连接数
     */
    private Integer fromTwoListingNumber;

    /**
     * 结束 近30天在线连接数
     */
    private Integer toTwoListingNumber;

    /**
     * 是否超管
     */
    private Boolean isAdmin;

    /**
     * 是否为组长
     */
    private Boolean isLeader;

    /**
     * 工号
     */
    private String employeeNo;

}
