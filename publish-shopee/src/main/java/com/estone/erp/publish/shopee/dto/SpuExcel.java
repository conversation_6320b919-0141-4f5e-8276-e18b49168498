package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.component.converter.SaleNameConverter;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpuExcel implements Serializable {

    private static final String SYSTEM_PARAM_VALUE = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_PUBLISH_CONFIG", "site_publish_completion_days", 10);

    @ExcelProperty("SPU")
    private String spu;

    /**
     *完成为是的SPU
     */
    @ExcelProperty("完成为是的SPU")
    private String publishedSpu;

    /**
     * 完成为否的SPU
     */
    @ExcelProperty("完成为否的SPU")
    private String unPublishedSpu;


    /**
     * 完成为否的SPU的分配组长
     */
    @ExcelProperty(value = "完成为否的SPU的分配组长",converter = SaleNameConverter.class)
    private String unPublishedSpuLeader;

    /**
     * 完成为否的SPU的分配销售
     */
    @ExcelProperty(value = "完成为否的SPU的分配销售",converter = SaleNameConverter.class)
    private String unPublishedSpuSale;

    public static String getSpuColumnName() {
        return SYSTEM_PARAM_VALUE + "个站点刊登完成时间为昨天的SPU";
    }

    public static String getPublishedSpuColumnName() {
        return SYSTEM_PARAM_VALUE + "个站点刊登完成为是的SPU";
    }
    public static String getUnPublishedSpuColumnName() {
        return SYSTEM_PARAM_VALUE + "个站点刊登完成为否的SPU";
    }

    public static String getUnPublishedSpuLeaderColumnName() {
        return SYSTEM_PARAM_VALUE + "个站点刊登完成为否的SPU的分配组长";
    }
    public static String getUnPublishedSpuSaleColumnName() {
        return SYSTEM_PARAM_VALUE + "个站点刊登完成为否的SPU的分配销售";
    }
}
