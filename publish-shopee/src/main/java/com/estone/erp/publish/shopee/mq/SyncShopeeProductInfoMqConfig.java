package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Auther yucm
 * @Date 2022/8/15
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class SyncShopeeProductInfoMqConfig {

    private int syncShopeeProductInfoMqConsumers;
    private int syncShopeeProductInfoMqPrefetchCount;
    private boolean syncShopeeProductInfoMqListener;

    @Bean
    public Queue syncShopeeProductInfo() {
        return new Queue(PublishQueues.SHOPEE_SYNC_PRODUCT_INFO_QUEUE);
    }

    @Bean
    public Binding syncShopeeProductInfoBinding() {
        return new Binding(PublishQueues.SHOPEE_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_SYNC_PRODUCT_INFO_KEY, null);
    }
    @Bean
    public SyncShopeeProductInfoMqListener syncShopeeProductInfoMqListener() {
        return new SyncShopeeProductInfoMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer syncShopeeProductInfoListenerContainer(
            SyncShopeeProductInfoMqListener syncShopeeProductInfoMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_SYNC_PRODUCT_INFO_QUEUE, syncShopeeProductInfoMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (syncShopeeProductInfoMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(syncShopeeProductInfoMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(syncShopeeProductInfoMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
