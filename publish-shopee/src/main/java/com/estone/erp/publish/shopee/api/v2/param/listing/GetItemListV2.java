package com.estone.erp.publish.shopee.api.v2.param.listing;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 17:43
 * @description
 */
@Getter
@Setter
public class GetItemListV2 implements RequestCommon{

    /**
     * 指定要在当前调用中返回的数据的起始条目。
     默认值为0。如果数据超过一页，则偏移量可以是开始下一个调用的某个条目。
     * Required
     */
    @JSONField(name = "offset")
    private Integer offset;

    /**
     * Required
     */
    @JSONField(name = "page_size")
    private Integer pageSize;

    /**
     * 指定用于检索订单的日期范围（基于商品更新时间） 开始日期
     */
    @JSONField(name = "update_time_from")
    private Long updateTimeFrom;

    @JSONField(name = "update_time_to")
    private Long updateTimeTo;

    /**
     *  NORMAL/BANNED/DELETED/UNLIST
     * Required
     */
    @JSONField(name = "item_status")
    private List<String> itemStatus;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_ITEM_LIST;
    }
}
