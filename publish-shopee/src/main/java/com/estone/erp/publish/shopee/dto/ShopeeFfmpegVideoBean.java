package com.estone.erp.publish.shopee.dto;

import lombok.Data;

/**
 * @Auther yucm
 * @Date 2023/6/8
 */
@Data
public class ShopeeFfmpegVideoBean {

    /**
     * 输入地址
     */
    private String inputFile;

    /**
     * 输出目录
     */
    private String outputPath;

    /**
     * 输出地址
     */
    private String outputFile;

    /**
     * 默认视频格式
     */
    private String defaultVideoFormat = "mp4";

    /**
     * 最大像素限制px
     */
    private Integer maxWidth = 1280;
    private Integer maxHeight = 1280;

    /**
     * 是否需要处理（符合要求10-60s 小于30m则不需要处理）
     */
    private Boolean isConvert;

    /**
     * 货号
     */
    private String articleNumber;
}
