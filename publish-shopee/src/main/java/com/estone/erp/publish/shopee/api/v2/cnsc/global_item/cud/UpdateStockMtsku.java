package com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.SellerStock;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/15 11:16
 * @description
 */
@Getter
@Setter
public class UpdateStockMtsku implements RequestCommon {

    @JSONField(name = "global_item_id")
    private Long globalItemId;

    /** list长度应在1到50之间 */
    @JSONField(name = "stock_list")
    private List<UpdateStockPriceDetailMtsku> stockList;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.UPDATE_STOCK;
    }

    /**
     * mtsku修改 批量修改同一个 global_item_id 下的产品库存,调用者自己做 global_item_id分组
     * @param list
     * @return
     */
    public UpdateStockMtsku(List<EsShopeeItem> list){
        String GlobalItemId = list.get(0).getGlobalItemId();
        this.setGlobalItemId(Long.valueOf(GlobalItemId));

        List<UpdateStockPriceDetailMtsku> stockList = list.stream().map(o -> {
            UpdateStockPriceDetailMtsku stock = new UpdateStockPriceDetailMtsku();
            stock.setSellerStocks(Arrays.asList(new SellerStock(o.getStock(), ShopeeConstants.SELLER_STOCK_LOCATION_ID_CNZ)));
            if (o.getGlobalModelId() != null && !o.getGlobalModelId().equalsIgnoreCase("0")) {
                stock.setGlobalModelId(Long.valueOf(o.getGlobalModelId()));
            }
            return stock;
        }).collect(Collectors.toList());
        this.setStockList(stockList);
    }
}
