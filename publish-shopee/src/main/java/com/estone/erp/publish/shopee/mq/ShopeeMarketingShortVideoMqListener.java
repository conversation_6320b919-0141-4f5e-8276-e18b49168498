package com.estone.erp.publish.shopee.mq;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.component.marking.ShortVideoConfigParam;
import com.estone.erp.publish.shopee.dto.ShopeeCrawMustProperty;
import com.estone.erp.publish.shopee.enums.ShopeeConfigOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeConfigStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeVideoGenStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeVideoSubmitStatusEnum;
import com.estone.erp.publish.shopee.handler.ShopeeShortVideoHandler;
import com.estone.erp.publish.shopee.model.ShopeeConfigTask;
import com.estone.erp.publish.shopee.mq.model.ShopeeFlashSaleGenGoodsDto;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeMarketingShortVideo;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeMarketingShortVideoService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * shopee 短视频生成商品的mq监听器
 */
@Slf4j
public class ShopeeMarketingShortVideoMqListener implements ChannelAwareMessageListener {

    @Resource
    private IShopeeMarketingShortVideoService iShopeeMarketingShortVideoService;

    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Resource
    private ShopeeShortVideoHandler shopeeShortVideoHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("ShopeeMarketingShortVideoMqListener body to String is null");
            }

            boolean sign = false;
            try {
                ShopeeFlashSaleGenGoodsDto dto = JSON.parseObject(body, new TypeReference<>() {
                });
                doService(dto);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeMarketingShortVideoMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeMarketingShortVideoMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeMarketingShortVideoMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeMarketingShortVideoMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    /**
     * 业务处理
     *
     * @param dto 业务对象
     */
    private void doService(ShopeeFlashSaleGenGoodsDto dto) {
        if (dto == null) {
            log.error("ShopeeMarketingShortVideoMqListener dto is null");
            return;
        }

        Date date = new Date();
        Timestamp now = new Timestamp(date.getTime());

        Integer marketingTaskId = dto.getTaskId().intValue();

        ShopeeConfigTask shortVideoTask = shopeeConfigTaskService.selectByPrimaryKey(marketingTaskId);
        if (shortVideoTask == null) {
            log.error("ShopeeMarketingShortVideoMqListener getById is null, {}", dto);
            return;
        }
        Integer publishOperatorStatus = shortVideoTask.getOperatorStatus();
        if (!Objects.equals(publishOperatorStatus, ShopeeConfigStatusEnum.WAITING.getCode())) {
            log.error("ShopeeMarketingShortVideoMqListener 该任务刊登操作状态未处于待执行状态, {}", dto);
            shopeeConfigTaskService.failTask(shortVideoTask.getId(), "该任务刊登操作状态未处于待执行状态, 存在重复消费可能");
            return;
        }

        // 处理任务 将状态扭转为执行中
        shortVideoTask.setOperatorStatus(ShopeeConfigOperatorStatusEnum.RUNNING.getCode());
        shortVideoTask.setOperatorTime(now);
        shopeeConfigTaskService.updateByPrimaryKeySelective(shortVideoTask);

        String marketingRuleJson = shortVideoTask.getConfigRuleJson();
        if (StringUtils.isBlank(marketingRuleJson)) {
            shopeeConfigTaskService.failTask(marketingTaskId, "任务参数有问题");
            return;
        }
        ShortVideoConfigParam shortVideoConfigParam = JSON.parseObject(marketingRuleJson, ShortVideoConfigParam.class);
        // 处理数据
        try {
            genGoods(shortVideoTask, shortVideoConfigParam, dto.getProductIdList());
        } catch (Exception e) {
            log.error("ShopeeMarketingShortVideoMqListener 执行异常" + e.getMessage(), e);
            shopeeConfigTaskService.failTask(marketingTaskId, "异常：" + e.getMessage());
        }
    }

    private void genGoods(ShopeeConfigTask shortVideoTask, ShortVideoConfigParam shortVideoConfigParam, List<String> producctIdList) {
        Date now = new Date();
        Integer itemSalesTimePeriod = shortVideoConfigParam.getItemSalesTimePeriod();
        // 每天的发货个数
        Integer publishNumber = shortVideoConfigParam.getPublishNumber();
        Integer itemStockFrom = shortVideoConfigParam.getItemStockFrom();
        Integer itemStockTo = shortVideoConfigParam.getItemStockTo();
        ShopeeCrawMustProperty shopeeCrawMustProperty = buildShopeeCrawMustProperty(shortVideoTask);

        // 处理数据
        boolean repeat = BooleanUtils.isTrue(shortVideoConfigParam.getRepeatFlag());
        Integer repeatDay = shortVideoConfigParam.getRepeatDay();
        String orderByDescFiled = ShopeeShortVideoHandler.getOrderByDescField(itemSalesTimePeriod);

        String accountNumber = shortVideoTask.getAccountNumber();

        LocalDateTime localDateTimeNow = LocalDateTime.now();

        EsShopeeItemRequest request = ShopeeShortVideoHandler.buildEsShopeeItemRequest(producctIdList, accountNumber, shortVideoConfigParam, now);

        // 先按照最近X天发布的链接优先上传，如果新品不够的话，就按照销量从高到低生成
        String fromUploadDate = request.getFromUploadDate();

        String[] field = new String[]{"id", "itemSeller", "itemId", "spu"};
        int offset = 0;
        int size = 100;
        int addNumber = 0;
        int successNumber = 0;
        int failNumber = 0;
        // 已经跑过的itemId
        Set<String> ranItemSet = new HashSet<>();
        while (true) {
            if (publishNumber <= 0) {
                break;
            }

            List<EsShopeeItem> esShopeeItems = shopeeShortVideoHandler.getAggregationByItemId(orderByDescFiled, field, offset, size, request);
            if (CollectionUtils.isEmpty(esShopeeItems)) {
                if (StringUtils.isNotBlank(fromUploadDate)) {
                    request.setFromUploadDate(null);
                    offset = 0;
                    fromUploadDate = null;
                    continue;
                }
                break;
            }
            offset += size;

            Map<String, String> itemIdAndSpuMap = shopeeShortVideoHandler.filterRanItemId(esShopeeItems, ranItemSet);
            if (MapUtils.isEmpty(itemIdAndSpuMap)) {
                continue;
            }
            Set<String> itemIdList = itemIdAndSpuMap.keySet();
            // 需要
            LocalDateTime fromCreatedTime = null;
            if (repeat) {
                Date gteUploadDate = DateUtils.addDays(now, -repeatDay);
                fromCreatedTime = LocalDateTimeUtil.of(gteUploadDate);
            }
            List<String> itemList = shopeeShortVideoHandler.filterNotExistBdItemId(fromCreatedTime, itemIdList, accountNumber);
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            // 判断平台库存是否符合
            List<String> nowRunItemIdList = shopeeShortVideoHandler.checkStock(itemStockFrom, itemStockTo, itemList);
            if (CollectionUtils.isEmpty(nowRunItemIdList)) {
                continue;
            }

            // 判断可以生成多少条视频
            if (publishNumber > nowRunItemIdList.size()) {
                publishNumber -= nowRunItemIdList.size();
            } else {
                nowRunItemIdList = nowRunItemIdList.subList(0, publishNumber);
                publishNumber = 0;
            }
            addNumber += nowRunItemIdList.size();
            List<String> finalNowRunItemIdList = nowRunItemIdList;
            List<String> spuList = itemIdAndSpuMap.entrySet().stream().filter(a -> finalNowRunItemIdList.contains(a.getKey())).map(Map.Entry::getValue).distinct().collect(Collectors.toList());
            Map<String, String> spuAndEnCustomsMap = ShopeeShortVideoHandler.getSpuAndEnCustomsMap(spuList);
            // 添加的数量
            for (String itemId : finalNowRunItemIdList) {
                String spu = itemIdAndSpuMap.get(itemId);
                ShopeeMarketingShortVideo marketingShortVideo = ShopeeShortVideoHandler.buildShortVideo(shortVideoTask.getId().longValue(), shortVideoTask.getConfigId(),
                        shortVideoConfigParam, itemId, shopeeCrawMustProperty, localDateTimeNow, spuAndEnCustomsMap.get(spu), spu);

                // 获取spu图片
                // 获取文件系统的产品库图片
                List<String> pictureUrlBySkuAndType = ShopeeShortVideoHandler.getImageList(spu);
                if (CollectionUtils.isEmpty(pictureUrlBySkuAndType)) {
                    log.error("ShopeeMarketingShortVideoMqListener 图片为空, spu: {}", spu);
                    setVideoFailInfo(marketingShortVideo, spu + "：图片为空");
                    iShopeeMarketingShortVideoService.save(marketingShortVideo);
                    failNumber++;
                    continue;
                }
                if (pictureUrlBySkuAndType.size() == 1) {
                    log.error("ShopeeMarketingShortVideoMqListener 图片数量为1张，不生成视频, spu: {}", spu);
                    setVideoFailInfo(marketingShortVideo, spu + "：图片数量为1张，不生成视频");
                    iShopeeMarketingShortVideoService.save(marketingShortVideo);
                    failNumber++;
                    continue;
                }
                successNumber++;
                iShopeeMarketingShortVideoService.save(marketingShortVideo);
                Long shortVideoId = marketingShortVideo.getId();
                // 开始生成的图片
                List<String> images = ShopeeShortVideoHandler.genImagsList(pictureUrlBySkuAndType, spu);
                shopeeShortVideoHandler.sendGenVideo(itemId, images, shortVideoId, spu);
            }
        }
        shopeeConfigTaskService.successTask(shortVideoTask.getId(), "任务执行成功，添加短视频数量：" + addNumber + "，成功：" + successNumber + "，失败：" + failNumber);
    }

    private ShopeeCrawMustProperty buildShopeeCrawMustProperty(ShopeeConfigTask shortVideoTask) {
        ShopeeCrawMustProperty shopeeCrawMustProperty = new ShopeeCrawMustProperty();
        shopeeCrawMustProperty.setAccountNumber(shortVideoTask.getAccountNumber());
        shopeeCrawMustProperty.setSellerAccount(shortVideoTask.getSellerAccount());
        shopeeCrawMustProperty.setSite(shortVideoTask.getSite());
        shopeeCrawMustProperty.setMerchantId(shortVideoTask.getMerchantId());
        shopeeCrawMustProperty.setMerchantName(shortVideoTask.getMerchantName());
        shopeeCrawMustProperty.setShopId(shortVideoTask.getShopId());
        return shopeeCrawMustProperty;
    }

    public void setVideoFailInfo(ShopeeMarketingShortVideo shopeeMarketingShortVideo, String message) {
        shopeeMarketingShortVideo.setVideoStatus(ShopeeVideoGenStatusEnum.FAIL.getCode());
        shopeeMarketingShortVideo.setSubmitStatus(ShopeeVideoSubmitStatusEnum.FAILD.getCode());
        shopeeMarketingShortVideo.setSubmitRemark(message);
    }

}

