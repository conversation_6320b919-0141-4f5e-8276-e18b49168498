package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 18:05
 * @description 预购设定
 */
@Getter
@Setter
public class PreOrderDtoV2 {

    /** 是否为预购*/
    @JSONField(name = "is_pre_order")
    private Boolean isPreOrder;

    /** 保证发货天数。
     预订时，请输入7到30之间的值；
     对于非预购商品，请排除此字段，该字段将默认为您所在商店的相应标准值。（例如，CrossBorder为3）*/
    @JSONField(name = "days_to_ship")
    private Integer daysToShip;

}
