package com.estone.erp.publish.shopee.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 尺寸表
 * <AUTHOR>
 * @Date 2025/6/12 15:08
 */
@Data
public class ShopeeSizeChartDTO {

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * esIdList(更新必传)
     */
    private List<String> esIdList;

    /**
     * globalItemIdList
     */
    private List<String> globalItemIdList;

    /**
     * 尺码表id（更新必传）
     */
    private Integer sizeChartId;

    /**
     * 类目id
     */
    private Integer categoryId;

}
