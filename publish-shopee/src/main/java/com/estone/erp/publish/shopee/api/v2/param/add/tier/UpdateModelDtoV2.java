package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.v2.param.add.DimensionDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import lombok.Data;

/**
 * 更新变体
 */
@Data
public class UpdateModelDtoV2 {

    /**
     * 变体id
     */
    @JSONField(name = "model_id")
    private Long modelId;

    /**
     * 变体SKU
     */
    @JSONField(name = "model_sku")
    private String modelSku;

    /**
     * 预售信息
     */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    /**
     * 尺寸
     */
    @JSONField(name = "dimension")
    private DimensionDtoV2 dimension;

    /**
     * 重量
     */
    @JSONField(name = "weight")
    private Double weight;

    /**
     * 变体状态
     */
    @JSONField(name = "model_status")
    private String modelStatus;

    @JSONField(name = "gtin_code")
    private String gtinCode;

}
