package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/12 12:06
 * @description 添加折扣
 */
@Getter
@Setter
public class ShopeeUpdateDiscountItemV2 extends ShopeeAddDiscountItemV2 {


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_DISCOUNT_ITEM;
    }
}
