package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.shopee.converter.ShopeeFeedTaskResultStatusConverter;
import com.estone.erp.publish.shopee.converter.ShopeeFeedTaskTypeConverter;
import lombok.Data;

/**
 * shopee 导出
 */
@Data
public class ExcelShopeeFeedTask {

    @ExcelProperty(value = "账号")
    private String accountNumber;

    @ExcelProperty(value = "SKU(货号)")
    private String articleNumber;

    @ExcelProperty(value = "报告类型", converter = ShopeeFeedTaskTypeConverter.class)
    private String taskType;

    @ExcelProperty(value = "处理结果", converter = ShopeeFeedTaskResultStatusConverter.class)
    private Integer resultStatus;

    @ExcelProperty(value = "备注信息")
    private String resultMsg;
}
