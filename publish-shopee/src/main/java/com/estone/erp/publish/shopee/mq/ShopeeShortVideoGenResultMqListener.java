package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.shopee.enums.ShopeeVideoGenStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeVideoSubmitStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeVideoDetails;
import com.estone.erp.publish.shopee.util.ShopeeUploadVideoUtil;
import com.estone.erp.publish.system.fms.request.UploadVideoRequest;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeMarketingShortVideo;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeMarketingShortVideoService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * shopee 短视频生成队列
 */
@Slf4j
public class ShopeeShortVideoGenResultMqListener implements ChannelAwareMessageListener {

    @Resource
    private IShopeeMarketingShortVideoService iShopeeMarketingShortVideoService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("ShopeeShortVideoGenResultMqListener body to String is null");
            }

            boolean sign = false;
            try {
                UploadVideoRequest dto = JSON.parseObject(body, new TypeReference<>() {
                });
                doService(dto);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeShortVideoGenResultMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeShortVideoGenResultMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeShortVideoGenResultMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeShortVideoGenResultMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    /**
     * 业务处理
     *
     * @param dto 业务对象
     */
    private void doService(UploadVideoRequest dto) {
        if (dto == null) {
            log.error("ShopeeShortVideoGenResultMqListener dto is null");
            return;
        }
        String message = dto.getMessage();
        Long shortVideoId = dto.getShortVideoId();
        boolean success = StringUtils.isBlank(message);
        ShopeeMarketingShortVideo shopeeMarketingShortVideo = iShopeeMarketingShortVideoService.getById(shortVideoId);

        String videoUrl = dto.getVideoUrl();
        if (success) {
            ShopeeVideoDetails videoDetails = null;
            try {
                videoDetails = ShopeeUploadVideoUtil.getVideoDetails(videoUrl);
            } catch (Exception e) {
                log.error("ShopeeShortVideoGenResultMqListener 获取视频信息失败" + e.getMessage(), e);
            }
            if (videoDetails != null) {
                Long videoSize = videoDetails.getVideoSize();
                if (videoSize <= (30 * 1024 * 1024)) {
                    shopeeMarketingShortVideo.setVideoStatus(ShopeeVideoGenStatusEnum.SUCCESS.getCode());
                    shopeeMarketingShortVideo.setVideoLink(dto.getVideoUrl());
                    shopeeMarketingShortVideo.setSubmitStatus(ShopeeVideoSubmitStatusEnum.WAITING.getCode());
                } else {
                    shopeeMarketingShortVideo.setVideoStatus(ShopeeVideoGenStatusEnum.FAIL.getCode());
                    shopeeMarketingShortVideo.setVideoLink(dto.getVideoUrl());
                    shopeeMarketingShortVideo.setSubmitStatus(ShopeeVideoSubmitStatusEnum.FAILD.getCode());
                    shopeeMarketingShortVideo.setSubmitRemark("视频大于30M");
                }
            } else {
                shopeeMarketingShortVideo.setVideoLink(videoUrl);
                shopeeMarketingShortVideo.setVideoStatus(ShopeeVideoGenStatusEnum.FAIL.getCode());
                shopeeMarketingShortVideo.setSubmitStatus(0);
                shopeeMarketingShortVideo.setSubmitRemark("获取视频信息失败");
            }
        } else {
            shopeeMarketingShortVideo.setVideoStatus(ShopeeVideoGenStatusEnum.FAIL.getCode());
            shopeeMarketingShortVideo.setSubmitStatus(ShopeeVideoSubmitStatusEnum.FAILD.getCode());
            shopeeMarketingShortVideo.setSubmitRemark(StringUtils.isNotBlank(message) ? message : "生成视频失败");
        }
        iShopeeMarketingShortVideoService.updateById(shopeeMarketingShortVideo);
    }

}

