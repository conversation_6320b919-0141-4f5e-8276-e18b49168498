package com.estone.erp.publish.shopee.api.v2.param.video;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-20 15:05
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InitUploadVideo implements RequestCommon, Serializable {

    /**
     * 完整文件大小 字节
     */
    @JSONField(name = "file_size")
    private int fileSize;

    /**
     * 文件MD5
     */
    @JSONField(name = "file_md5")
    private String fileMd5;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.INIT_UPLOAD_VIDEO;
    }
}
