package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/25 16:34
 * @description 删除产品项目
 */
@Getter
@Setter
public class DeleteItemV2 implements RequestCommon {

    @JSONField(name = "item_id")
    private Long itemId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.DELETE_ITEM;
    }
}
