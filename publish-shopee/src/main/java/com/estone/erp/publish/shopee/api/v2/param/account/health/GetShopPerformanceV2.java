package com.estone.erp.publish.shopee.api.v2.param.account.health;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

@Data
public class GetShopPerformanceV2 implements RequestCommon {

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_SHOP_PERFORMANCE;
    }
}
