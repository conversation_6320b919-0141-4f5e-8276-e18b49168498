package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class BundleDealPageV2 implements RequestCommon {

    @JSONField(name = "page_no")
    private Integer pageNo;
    @JSONField(name = "page_size")
    private Integer pageSize;

    /**
     * 捆绑交易的状态，all=1;upcoming=2;ongoing=3，expired=4，默认值为 1
     */
    @JSONField(name = "time_status")
    private Integer timeStatus;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_BUNDLE_DEAL_LIST;
    }

    @Getter
    public enum TimeStatusEnum {
        All(1), Upcoming(2), Ongoing(3), Expired(4);

        private final Integer code;

        TimeStatusEnum(int value) {
            this.code = value;
        }

        public static List<TimeStatusEnum> syncStatusList() {
            return List.of(Upcoming, Ongoing);
        }

    }

}
