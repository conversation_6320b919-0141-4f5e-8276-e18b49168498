package com.estone.erp.publish.shopee.api.v2.cnsc.add;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplateShop;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.GlobalItemDto;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/29 15:28
 * @description
 */
@Getter
@Setter
public class GlobalCreatePublishTask implements RequestCommon {

    @JSONField(name = "global_item_id")
    private Long globalItemId;

    /** 店铺id */
    @JSONField(name = "shop_id")
    private Integer shopId;

    /** 店铺所在地区: TW SG PH ...*/
    @JSONField(name = "shop_region")
    private String shopRegion;

    /** 商品信息 */
    @JSONField(name = "item")
    private GlobalItemDto item;

    public GlobalCreatePublishTask(EsShopeeGlobalTemplateShop shop, Long globalItemId){
        this.globalItemId = globalItemId;
        this.shopId = Integer.valueOf(shop.getShopId());
        this.shopRegion = shop.getSite();


    }

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.CREATE_PUBLISH_TASK;
    }
}
