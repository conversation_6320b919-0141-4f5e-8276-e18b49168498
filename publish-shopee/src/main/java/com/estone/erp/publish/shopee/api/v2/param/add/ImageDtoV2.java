package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12 18:00
 * @description
 */
@Getter
@Setter
public class ImageDtoV2 {

    /** 图片编号 */
    @JSONField(name = "image_id_list")
    private List<String> imageIdList = new ArrayList<>(9);

}
