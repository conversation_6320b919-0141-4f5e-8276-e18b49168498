package com.estone.erp.publish.shopee.service.impl;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.shopee.component.publishStatus.ListingStatusParam;
import com.estone.erp.publish.shopee.dto.BatchUpdateStatusDto;
import com.estone.erp.publish.shopee.dto.ShopeeListingOfflineConfigDTO;
import com.estone.erp.publish.shopee.dto.ShopeeListingOnlineConfigDTO;
import com.estone.erp.publish.shopee.dto.SyncGroupConfigAccountDto;
import com.estone.erp.publish.shopee.enums.ShopeeConfigGroupLinkTypeEnums;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingLogTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeListingTypeEnum;
import com.estone.erp.publish.shopee.mapper.ShopeeListingStatusConfigMapper;
import com.estone.erp.publish.shopee.mapper.ShopeeMarketingConfigLogMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountGroupService;
import com.estone.erp.publish.shopee.service.ShopeeConfigGroupLinkService;
import com.estone.erp.publish.shopee.service.ShopeeListingStatusConfigService;
import com.estone.erp.publish.shopee.util.ShopeeConfigLogUtil;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-29 15:10:27
 */
@Service("ShopeeListingStatusConfigService")
@Slf4j
class ShopeeListingStatusConfigServiceImpl implements ShopeeListingStatusConfigService {

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private ShopeeListingStatusConfigMapper shopeeListingStatusConfigMapper;

    @Resource
    private ShopeeMarketingConfigLogMapper shopeeMarketingConfigLogMapper;

    @Resource
    private ShopeeConfigGroupLinkService shopeeConfigGroupLinkService;

    @Resource
    private ShopeeAccountGroupService shopeeAccountGroupService;

    @Override
    public int countByExample(ShopeeListingStatusConfigExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeListingStatusConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeListingStatusConfig> search(CQuery<ShopeeListingStatusConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeListingStatusConfigCriteria query = cquery.getSearch();
        ShopeeListingStatusConfigExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeListingStatusConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeListingStatusConfig> ShopeeListingStatusConfigs = shopeeListingStatusConfigMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ShopeeListingStatusConfig> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ShopeeListingStatusConfigs);
        return result;
    }

    @Override
    public ShopeeListingStatusConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return shopeeListingStatusConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeListingStatusConfig> selectByExample(ShopeeListingStatusConfigExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeListingStatusConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeListingStatusConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        return shopeeListingStatusConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeListingStatusConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeListingStatusConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeListingStatusConfig record, ShopeeListingStatusConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeListingStatusConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeListingStatusConfigMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ApiResult<?> saveOrUpdateConfig(ShopeeListingStatusConfig config) {
        // 1、参数校验
        ListingStatusParam marketingConfigParam = config.getMarketingConfigParam();
        if (marketingConfigParam == null) {
            return ApiResult.newError("配置信息错误！");
        }
        marketingConfigParam.paramCheck();

        // 2、校验参数名称是否重复
        this.verifyRuleName(config);

        // 3、校验店铺是否可用
        List<String> accountGroupNames = Arrays.asList(StringUtils.split(config.getAccounts(), ","));
        Map<String, List<String>> accountMap = saleAccountService.filterFrozenStoreAndSip(SaleChannel.CHANNEL_SHOPEE, accountGroupNames);
        if (MapUtils.isEmpty(accountMap) || CollectionUtils.isEmpty(accountMap.get("execAccounts"))) {
            return ApiResult.newError("无可用店铺！");
        }

        try {
            // 保存或更新配置
            config.setAccounts(StringUtils.join(accountMap.get("execAccounts"), ","));
            insertOrUpdateConfig(config);

            // 同步到分组店铺配置映射表
            shopeeConfigGroupLinkService.updateLink(config.getId(), config.getAccountGroupId(), config.getName(), config.getStatus(), config.getAccountType(),
                    ShopeeListingTypeEnum.OFFLINE.getCode().equals(config.getType()) ? ShopeeConfigGroupLinkTypeEnums.OFFLINE_CONFIG : ShopeeConfigGroupLinkTypeEnums.ONLINE_CONFIG
            );

            // 判断是否存在过滤店铺
            if (CollectionUtils.isNotEmpty(accountMap.get("filterAccounts"))) {
                return ApiResult.newSuccess(String.format("过滤店铺：%s", accountMap.get("filterAccounts").stream().collect(Collectors.joining(","))));
            }
        } catch (Exception e) {
            throw new RuntimeException("保存或更新配置失败");
        }

        return ApiResult.newSuccess(config);
    }

    @Override
    public void syncGroupAccount(ShopeeListingStatusConfig ShopeeListingStatusConfig, SyncGroupConfigAccountDto dto) {
        String accounts = ShopeeListingStatusConfig.getAccounts();
        String groupNames = ShopeeListingStatusConfig.getAccountGroupName();
        String userName = WebUtils.getUserName();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        ShopeeListingStatusConfig.setAccountGroupName(StringUtils.join(dto.getNewGroupName(), ","));
        ShopeeListingStatusConfig.setAccounts(StringUtils.join(dto.getNewAccount(), ","));
        ShopeeListingStatusConfig.setAccountGroupId(StringUtils.join(dto.getNewGroupId(), ","));

        List<ShopeeMarketingConfigLog> list = new ArrayList<>();
        ShopeeMarketingConfigLog shopeeMarketingConfigLog1 = ShopeeConfigLogUtil.checkEqualLog(
                ShopeeListingStatusConfig.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG,
                "accounts", "适用店铺", accounts, ShopeeListingStatusConfig.getAccounts(), userName, now);
        if (shopeeMarketingConfigLog1 != null) {
            list.add(shopeeMarketingConfigLog1);
        }

        ShopeeMarketingConfigLog shopeeMarketingConfigLog2 = ShopeeConfigLogUtil.checkEqualLog(
                ShopeeListingStatusConfig.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG,
                "groupNames", "分组", groupNames, ShopeeListingStatusConfig.getAccountGroupName(),
                userName, now);
        if (shopeeMarketingConfigLog2 != null) {
            list.add(shopeeMarketingConfigLog2);
        }

        ShopeeListingStatusConfig.setUpdatedBy(userName);
        ShopeeListingStatusConfig.setUpdatedTime(now);
        // 更新 group
        shopeeListingStatusConfigMapper.updateGroup(ShopeeListingStatusConfig);

        if (CollectionUtils.isNotEmpty(list)) {
            shopeeMarketingConfigLogMapper.batchInsert(list);
        }
    }

    @Override
    public ApiResult<?> updateStatus(BatchUpdateStatusDto requestParam) {

        // 通过id获取配置，并过滤不等于状态的配置
        ShopeeListingStatusConfigExample example = new ShopeeListingStatusConfigExample();
        example.createCriteria().andIdIn(requestParam.getIds()).andStatusNotEqualTo(requestParam.getStatus());
        List<ShopeeListingStatusConfig> shopeeListingStatusConfigs = shopeeListingStatusConfigMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(shopeeListingStatusConfigs)) {
            // 修改配置并记录日志
            for (ShopeeListingStatusConfig shopeeListingStatusConfig : shopeeListingStatusConfigs) {
                shopeeListingStatusConfig.setStatus(requestParam.getStatus());

                // 记录日志
                saveListingStatusConfigLogs(shopeeListingStatusConfig);

                // 修改状态
                shopeeListingStatusConfigMapper.updateByPrimaryKeySelective(shopeeListingStatusConfig);
            }

            List<Integer> updateLinkList = shopeeListingStatusConfigs.stream().map(ShopeeListingStatusConfig::getId).collect(Collectors.toList());
            shopeeConfigGroupLinkService.updateStatusLink(updateLinkList, requestParam.getStatus(), ShopeeConfigGroupLinkTypeEnums.OFFLINE_CONFIG);
        }

        return ApiResult.newSuccess();
    }

    /**
     * 保存日志
     *
     * @param config
     */
    private void saveListingStatusConfigLogs(ShopeeListingStatusConfig config) {
        // 日志列表
        List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs = new ArrayList<>();

        // 获取旧的配置
        ShopeeListingStatusConfig oldShopeeMarketingConfig = shopeeListingStatusConfigMapper.selectByPrimaryKey(config.getId());

        // 如果为下架配置，则需要记录下架原因（类型较少未使用策略）
        if (ShopeeListingTypeEnum.OFFLINE.getCode().equals(oldShopeeMarketingConfig.getType())) {
            ShopeeListingOfflineConfigDTO oldShopeeListingOfflineConfigDTO = ShopeeListingOfflineConfigDTO.reconvert(oldShopeeMarketingConfig);
            ShopeeListingOfflineConfigDTO newShopeeListingOfflineConfigDTO = ShopeeListingOfflineConfigDTO.reconvert(config);
            // 通用属性日志处理
            shopeeMarketingConfigLogs.addAll(
                    ShopeeConfigLogUtil.generateLog(newShopeeListingOfflineConfigDTO, oldShopeeListingOfflineConfigDTO, config.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG)
            );
            // 配置规则属性日志处理
            shopeeMarketingConfigLogs.addAll(
                    ShopeeConfigLogUtil.generateLog(newShopeeListingOfflineConfigDTO.getRuleConfigJson(), oldShopeeListingOfflineConfigDTO.getRuleConfigJson(), config.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG)
            );
        } else {
            ShopeeListingOnlineConfigDTO oldShopeeListingOnlineConfigDTO = ShopeeListingOnlineConfigDTO.reconvert(oldShopeeMarketingConfig);
            ShopeeListingOnlineConfigDTO newShopeeListingOnlineConfigDTO = ShopeeListingOnlineConfigDTO.reconvert(config);
            // 通用属性日志处理
            shopeeMarketingConfigLogs.addAll(
                    ShopeeConfigLogUtil.generateLog(newShopeeListingOnlineConfigDTO, oldShopeeListingOnlineConfigDTO, config.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG)
            );
            // 配置规则属性日志处理
            shopeeMarketingConfigLogs.addAll(
                    ShopeeConfigLogUtil.generateLog(newShopeeListingOnlineConfigDTO.getRuleConfigJson(), oldShopeeListingOnlineConfigDTO.getRuleConfigJson(), config.getId(), ShopeeMarketingLogTypeEnum.OFFLINE_CONFIG)
            );
        }

        if (CollectionUtils.isNotEmpty(shopeeMarketingConfigLogs)) {
            shopeeMarketingConfigLogMapper.batchInsert(shopeeMarketingConfigLogs);
        }

    }

    /**
     * 校验规则名称是否重复
     *
     * @param config
     */
    private void verifyRuleName(ShopeeListingStatusConfig config) {
        ShopeeListingStatusConfigExample configExample = new ShopeeListingStatusConfigExample();
        ShopeeListingStatusConfigExample.Criteria configExampleCriteria = configExample.createCriteria();
        configExampleCriteria.andTypeEqualTo(config.getType()).andNameEqualTo(config.getName());
        if (ObjectUtils.isNotEmpty(config.getId())) {
            configExampleCriteria.andIdNotEqualTo(config.getId());
        }
        int sameNameCount = shopeeListingStatusConfigMapper.countByExample(configExample);
        if (sameNameCount > 0) {
            throw new BusinessException("当前配置类型下，规则名称已存在");
        }
    }

    /**
     * 保存或更新配置
     *
     * @param config
     */
    private void insertOrUpdateConfig(ShopeeListingStatusConfig config) {
        // 获取分组名称
        if (StringUtils.isNotBlank(config.getAccountGroupId())) {
            String[] split = config.getAccountGroupId().split(",");
            ShopeeAccountGroupExample example = new ShopeeAccountGroupExample();
            example.createCriteria().andIdIn(Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList()));
            List<ShopeeAccountGroup> shopeeAccountGroups = shopeeAccountGroupService.selectByExample(example);
            List<String> collect = shopeeAccountGroups.stream().map(ShopeeAccountGroup::getGroupName).collect(Collectors.toList());
            config.setAccountGroupName(StringUtils.join(collect, ","));
        } else {
            config.setAccountGroupName("");
            config.setAccountGroupId("");
        }

        // 保存或更新配置
        if (ObjectUtils.isEmpty(config.getId())) {
            config.setCreatedBy(WebUtils.getUserName());
            config.setCreatedTime(new Timestamp(System.currentTimeMillis()));
            shopeeListingStatusConfigMapper.insert(config);
        } else {
            // 记录日志
            saveListingStatusConfigLogs(config);

            // 更新配置并记录
            config.setUpdatedBy(WebUtils.getUserName());
            config.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
            shopeeListingStatusConfigMapper.updateByPrimaryKeySelective(config);
        }
    }

}