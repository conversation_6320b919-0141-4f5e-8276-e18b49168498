package com.estone.erp.publish.shopee.api.v2.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 11:53
 * @description
 */
@Getter
@Setter
public class GetBrandV2 implements RequestCommon {

    /**
     * Specifies the starting entry of data to return in the current call. Default is 0. if data is more than one page, the offset can be some entry to start next call.
     */
    @JSONField(name = "offset")
    private Integer offset;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @JSONField(name = "category_id")
    private Integer categoryId;

    /** Brand status , 1: normal brand, 2: pending brand */
    @JSONField(name = "status")
    private Integer status;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_BRAND_LIST;
    }
}
