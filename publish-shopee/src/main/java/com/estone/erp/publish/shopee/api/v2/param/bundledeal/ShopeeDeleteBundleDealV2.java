package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * @Description: 删除优惠套装
 * <AUTHOR>
 * @Date 2024/10/23 下午5:32
 */
@Data
public class ShopeeDeleteBundleDealV2 implements RequestCommon {

    private Long bundle_deal_id;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.DELETE_BUNDLE_DEAL;
    }
}
