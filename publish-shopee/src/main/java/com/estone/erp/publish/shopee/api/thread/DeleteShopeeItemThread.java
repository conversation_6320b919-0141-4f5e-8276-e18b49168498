package com.estone.erp.publish.shopee.api.thread;

import java.util.List;
import java.util.concurrent.CountDownLatch;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.shopee.model.ShopeeItem;
import com.estone.erp.publish.shopee.model.ShopeeLog;

/**
 * @Description: 多线程删除Shopee产品
 * @Author: yang<PERSON>yue
 * @Date: 2019/3/29 10:27
 * @Version: 1.0.0
 */
//20200818 这个类启用
public class DeleteShopeeItemThread implements Runnable {

    private static final Log logger = LogFactory.getLog(DeleteShopeeItemThread.class);

    private CountDownLatch countDownLatch;

    private ResponseJson responseJson;

    private List<ShopeeItem> shopeeItemList;

    private List<ShopeeLog> shopeeLogList;

    private List<String> itemIdList;

    private String operationId;

    public DeleteShopeeItemThread(CountDownLatch countDownLatch, ResponseJson responseJson,
            List<ShopeeItem> shopeeItemList, List<String> itemIdList, List<ShopeeLog> shopeeLogList,
            String operationId) {
        super();
        this.countDownLatch = countDownLatch;
        this.responseJson = responseJson;
        this.shopeeItemList = shopeeItemList;
        this.itemIdList = itemIdList;
        this.shopeeLogList = shopeeLogList;
        this.operationId = operationId;
    }

    @Override
    public void run() {
        /*if (CollectionUtils.isEmpty(shopeeItemList)) {
            return;
        }
        try {
            for (ShopeeItem shopeeItem : shopeeItemList) {
                if (StringUtils.isEmpty(shopeeItem.getItemSeller())) {
                    break;
                }

                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, shopeeItem.getItemSeller(), true);
                if(account == null) {
                    return;
                }
                ShopeeDeleteItemCall shopeeDeleteItemCall = new ShopeeDeleteItemCall();
                ResponseJson res = shopeeDeleteItemCall.deleteItem(shopeeItem, account);
                List<ResponseError> errors = res.getErrors();
                if (CollectionUtils.isEmpty(errors)) {
                    responseJson.addError(new ResponseError(StatusCode.FAIL, shopeeItem.getItemId(), "请求失败"));
                }
                else {
                    if (errors.get(0).getStatus() == StatusCode.SUCCESS) {
                        ShopeeLog record = new ShopeeLog();
                        record.setRelevanceId(shopeeItem.getArticleNumber());
                        record.setContent(shopeeItem.getItemSeller() + "删除线上和本地的产品");
                        record.setModule("ShopeeItem");
                        record.setCreatedBy(operationId);
                        record.setCreationDate(new Timestamp(new Date().getTime()));
                        shopeeLogList.add(record);

                        itemIdList.add(shopeeItem.getItemId());
                    }
                    responseJson.addError(errors.get(0));
                }
            }
        }
        catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        finally {
            countDownLatch.countDown();
        }*/
    }
}
