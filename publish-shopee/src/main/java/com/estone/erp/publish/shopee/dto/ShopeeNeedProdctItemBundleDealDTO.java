package com.estone.erp.publish.shopee.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class ShopeeNeedProdctItemBundleDealDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 本地套装id
     */
    private Long bundleDealId;


    /**
     * 店铺账号 database column shopee_marketing_bundle_deal.account_number
     */
    private String accountNumber;


    /**
     * 套装开始时间 database column shopee_marketing_bundle_deal.bundle_deal_start_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bundleDealStartTime;

    /**
     * 套装结束时间 database column shopee_marketing_bundle_deal.bundle_deal_end_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bundleDealEndTime;

    private String merchantName;

    private String sellerAccount;

    /**
     * 店铺id
     */
    private String shopId;
}