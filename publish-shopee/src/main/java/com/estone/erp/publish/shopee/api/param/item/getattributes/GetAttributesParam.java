package com.estone.erp.publish.shopee.api.param.item.getattributes;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * 获取某个第三级类目的商品属性:品牌 材料等
 */
public class GetAttributesParam implements IRequestUrlApiKey {

    /** 必须是第三级类目, 就是没有子类目 */
    @JSONField(name = "category_id")
    private Integer categoryId;

    /** 语言, 可选 */
    @JSONField(name = "language")
    private Integer language;

    /** 伙伴id */
    @JSONField(name = "partner_id")
    private Integer partnerId;

    /** 商店id */
    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(serialize = false)
    private String apiKey;

    public GetAttributesParam() {
    }

    public GetAttributesParam(SaleAccountAndBusinessResponse account, Integer categoryId) {
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
        this.categoryId = categoryId;
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.GET_ATTRIBUTES;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getLanguage() {
        return language;
    }

    public void setLanguage(Integer language) {
        this.language = language;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
}
