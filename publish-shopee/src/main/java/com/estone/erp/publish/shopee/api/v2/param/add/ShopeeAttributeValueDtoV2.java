package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:53
 * @description
 */
@Getter
@Setter
public class ShopeeAttributeValueDtoV2 {


    @JSONField(name = "value_id")
    private Integer valueId;

    /**
     * 如果值id = 0，则此字段为必填
     */
    @JSONField(name = "original_value_name")
    private String originalValueName;

    /**
     * 属性值的单位。（仅用于定量属性需要上传)
     */
    @JSONField(name = "value_unit")
    private String valueUnit;

}
