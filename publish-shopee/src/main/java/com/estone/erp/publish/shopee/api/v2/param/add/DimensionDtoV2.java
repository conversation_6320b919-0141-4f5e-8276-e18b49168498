package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:43
 * @description
 */
@Getter
@Setter
public class DimensionDtoV2 {

    /** 可选 */
    @JSONField(name = "package_length")
    private Integer packageLength;

    /** 可选 */
    @JSONField(name = "package_width")
    private Integer packageWidth;

    /** 可选 */
    @JSONField(name = "package_height")
    private Integer packageHeight;
}
