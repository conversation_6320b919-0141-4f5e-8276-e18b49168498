package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * 删除产品项目变体（mpsku该方法暂时掉不通）
 */
@Getter
@Setter
public class DeleteModelV2 implements RequestCommon {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "model_id")
    private Long modelId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.DELETE_MODEL;
    }
}
