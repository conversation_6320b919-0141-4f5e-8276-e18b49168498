package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.dto.ShopeeNewYearUpdateRecordDTO;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskMsgEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishOperationTypeEnum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeHolidayUpdateRecord;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeePublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeHolidayUpdateRecordService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeePublishOperationLogService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 新年更新mq配置
 * <AUTHOR>
 * @Date 2025/1/3 下午5:28
 */
@Slf4j
public class ShopeeNewYearUpdateRecordMqListener implements ChannelAwareMessageListener {

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Resource
    private ShopeePublishOperationLogService shopeePublishOperationLogService;

    @Autowired
    private ShopeeHolidayUpdateRecordService shopeeHolidayUpdateRecordService;

    @Resource
    protected FeedTaskService feedTaskService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            List<ShopeeNewYearUpdateRecordDTO> shopeeNewYearUpdateRecordDTOList = JSON.parseObject(body, new TypeReference<List<ShopeeNewYearUpdateRecordDTO>>() {
            });


            ArrayList<ShopeeHolidayUpdateRecord> shopeeHolidayUpdateRecordList = new ArrayList<>();
            shopeeNewYearUpdateRecordDTOList.forEach(dto -> {
                try {
                    String attribute3 = ShopeeFeedTaskMsgEnum.UPDATE_STOCK_FOR_NEW_YEAR.getCode();
                    if (dto.getType().equals(2)) {
                        attribute3 = ShopeeFeedTaskMsgEnum.RECOVER_STOCK_FOR_NEW_YEAR.getCode();
                    }
                    FeedTaskExample feedTaskExample = new FeedTaskExample();
                    feedTaskExample.createCriteria()
                            .andPlatformEqualTo(SaleChannelEnum.SHOPEE.getChannelName())
                            .andAccountNumberEqualTo(dto.getAccount())
                            .andArticleNumberEqualTo(dto.getSku())
                            .andAttribute3EqualTo(attribute3)
                            .andResultStatusEqualTo(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    feedTaskExample.setOrderByClause("create_time DESC");
                    feedTaskExample.setLimit(1);
                    List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, SaleChannelEnum.SHOPEE.getChannelName());
                    if (CollectionUtils.isNotEmpty(feedTasks)) {
                        FeedTask feedTask = feedTasks.get(0);
                        String resultMsg = feedTask.getResultMsg();

                        ShopeeHolidayUpdateRecord shopeeHolidayUpdateRecord = new ShopeeHolidayUpdateRecord();
                        shopeeHolidayUpdateRecord.setId(dto.getId());
                        if (dto.getType().equals(1)) {
                            shopeeHolidayUpdateRecord.setExecStatus(0);
                            shopeeHolidayUpdateRecord.setExecResultMsg(resultMsg);
                        } else {
                            shopeeHolidayUpdateRecord.setRecoverStatus(2);
                            shopeeHolidayUpdateRecord.setRecoverResultMsg(resultMsg);
                        }
                        shopeeHolidayUpdateRecordList.add(shopeeHolidayUpdateRecord);
                    }
                } catch (Exception e) {
                    addOperationLog(0, dto.getAccount(), "更新状态失败，类型：" + dto.getType());
                }
            });

            if (CollectionUtils.isNotEmpty(shopeeHolidayUpdateRecordList)) {
                shopeeHolidayUpdateRecordService.updateBatchById(shopeeHolidayUpdateRecordList);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("更新库存异常：{}", e.getMessage(), e);
            addOperationLog(0, null, "更新库存异常：" + e.getMessage());
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 保存日志
     *
     * @param status
     * @param account
     * @param remark
     */
    private void addOperationLog(Integer status, String account, String remark) {
        try {
            ShopeePublishOperationLog operationLog = new ShopeePublishOperationLog();
            operationLog.setOpType(ShopeePublishOperationTypeEnum.NEW_YEAR_UPDATE_RECORD_STOCK.getDesc());
            Optional.ofNullable(account).ifPresent(operationLog::setModId);
            operationLog.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
            operationLog.setUser("admin");
            operationLog.setState(status);
            Map<String, String> metaData = new HashMap<>();
            metaData.put("remark", remark);
            operationLog.setMetaObj(JSON.toJSONString(metaData));
            operationLog.setCreatedTime(LocalDateTime.now());
            shopeePublishOperationLogService.save(operationLog);
        } catch (Exception e) {
            log.error("ShopeeNewYearUpdateMqListener保存日志异常：{}", e.getMessage(), e);
        }
    }
}
