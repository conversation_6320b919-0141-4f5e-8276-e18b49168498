package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 18:09
 * @description
 */
@Getter
@Setter
public class WholesaleDtoV2 {


    /** 批发最低数量 */
    @JSONField(name = "min_count")
    private Integer minCount;

    /** 批发最高数量 */
    @JSONField(name = "max_count")
    private Integer maxCount;

    /** 批发单价 */
    @JSONField(name = "unit_price")
    private Double unitPrice;
}
