package com.estone.erp.publish.shopee.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.PromotionConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.shopee.converter.ShopeeCrawStatusConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingFlashSaleStatusConverter;
import com.estone.erp.publish.shopee.converter.ShopeeVideoSubmitStatusConverter;
import com.estone.erp.publish.shopee.converter.StopStatusConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * shopee 市场营销 excel 实体类
 */
@Data
public class ExcelShopeeMarketingFlashSale {


    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家")
    private String merchantName;

    /**
     * 店铺账号
     */
    @ExcelProperty(value = "店铺")
    private String accountNumber;

    /**
     * 站点
     */
    @ExcelProperty(value = "站点")
    private String site;

    @ExcelProperty(value = "配置规则名称")
    private String ruleName;

    @ExcelProperty(value = "店铺分组")
    private String groupName;

    @ExcelProperty(value = "停止状态", converter = StopStatusConverter.class)
    private Integer stopStatus;

    @ExcelProperty(value = "爬虫状态", converter = ShopeeCrawStatusConverter.class)
    private Integer crawStatus;
    /**
     * 0 待申报 1 申报成功 2 申报失败
     */
    @ExcelProperty(value = "提交状态", converter = ShopeeVideoSubmitStatusConverter.class)
    private Integer submitStatus;
    /**
     * 提交失败原因
     */
    @ExcelProperty(value = "失败信息")
    private String submitRemark;
    /**
     * 秒杀开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "秒杀开始时间", converter = TimestampFormatConverter.class)
    private Timestamp startTime;

    /**
     * 秒杀结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "秒杀结束时间", converter = TimestampFormatConverter.class)
    private Timestamp endTime;

    /**
     * 参与的商品数量（itemId维度的）
     */
    @ExcelProperty(value = "秒杀商品数量")
    private Integer applyNumber;

    /**
     * 剩余限额
     */
    @ExcelProperty(value = "秒杀剩余限额")
    private Integer remainingNumber;
    /**
     * 已设置提醒的数量
     */
    @ExcelProperty(value = "已设置提醒的数量")
    private Integer tipNumber;

    /**
     * 点击量 database column shopee_marketing_flash_sale.click_number
     */
    @ExcelProperty(value = "商品点击数")
    private Integer clickNumber;

    /**
     * 游览量 database column shopee_marketing_flash_sale.views_number
     */
    @ExcelProperty(value = "商品浏览量")
    private Integer viewsNumber;

    /**
     * 状态(进行中，接下来，已过期) database column shopee_marketing_flash_sale.status
     * @see com.estone.erp.publish.shopee.enums.ShopeeMarketingFlashSaleStatusEnum
     */
    @ExcelProperty(value = "状态", converter = ShopeeMarketingFlashSaleStatusConverter.class)
    private Integer status;

    /**
     * 是否开启（1，开启，0 未开启） database column shopee_marketing_flash_sale.is_open
     */
    @ExcelProperty(value = "是否开启", converter = PromotionConverter.class)
    private Integer isOpen;

    /**
     * 销售
     */
    @ExcelProperty(value = "销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty(value = "销售组长")
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间", converter = TimestampFormatConverter.class)
    private Timestamp createdTime;
    @ExcelProperty(value = "爬取时间", converter = TimestampFormatConverter.class)
    private Timestamp crawCanGoodsTime;
    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp syncTime;
    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间", converter = TimestampFormatConverter.class)
    private Timestamp submitTime;
//    /**
//     * 爬虫时间
//     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @ExcelProperty(value = "爬虫时间", converter = TimestampFormatConverter.class)
//    private Timestamp crawlTime;
}
