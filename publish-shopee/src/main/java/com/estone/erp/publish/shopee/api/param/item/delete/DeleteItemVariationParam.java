package com.estone.erp.publish.shopee.api.param.item.delete;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * 
 * @Description: 删除Shopee产品变体
 * 
 * @ClassName: DeleteItemVariationParam
 * @Author: qinyangkai
 * @Date: 2019/07/15
 * @Version: 0.0.1
 */
public class DeleteItemVariationParam implements IRequestUrlApiKey {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "variation_id")
    private Long variationId;

    @JSONField(name = "partner_id")
    private Long partnerId;

    @JSONField(name = "shopid")
    private Long shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(serialize = false)
    private String apiKey;

    public DeleteItemVariationParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Long.valueOf(account.getColStr1());
        this.shopId = Long.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.ITEM_DELETE_VARIATION;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getVariationId() {
        return variationId;
    }

    public void setVariationId(Long variationId) {
        this.variationId = variationId;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
}
