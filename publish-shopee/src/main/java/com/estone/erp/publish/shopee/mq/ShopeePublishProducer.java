package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.enums.TemplateQueueTypeEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.bo.ShopeePublishObject;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.component.publishV1.ShopeePublishParam;
import com.estone.erp.publish.shopee.dto.ShopeeSpuGlobalPublishParam;
import com.estone.erp.publish.shopee.dto.ShopeeSpuPublishParam;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/30 18:08
 * @description shopee 刊登生产者
 */
@Slf4j
@Component
public class ShopeePublishProducer {

    @Resource
    private ShopeeTemplateNewService shopeeTemplateNewService;
    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private ShopeePublishQueueService shopeePublishQueueService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private ShopeeGlobalTemplateEsService shopeeGlobalTemplateEsService;
    @Resource
    private ShopeeGlobalPublishQueueService shopeeGlobalPublishQueueService;

    @Transactional
    public ResponseJson sendMq(ShopeeTemplateNew shopeeTemplate, String userName) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        //验证改模板状态
//        ShopeeTemplateNew dbTp = shopeeTemplateNewService.selectByPrimaryKey(shopeeTemplate.getId());
//        if(dbTp.getPublishStatus() != null && (dbTp.getPublishStatus() == ShopeeNewPublishStatusEnum.PUBLISHING.getCode() || dbTp.getPublishStatus() == ShopeeNewPublishStatusEnum.SUCCESS.getCode())){
//            rsp.setMessage("改模模板已经是刊登中或成功状态，无需重复提交。");
//            return rsp;
//        }
        // 处理报告状态，如果是待刊登表示 已经存在排队任务
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAssociationIdEqualTo(shopeeTemplate.getId().toString())
                .andPlatformEqualTo(Platform.Shopee.name())
                .andTaskStatusEqualTo(FeedTaskStatusEnum.WAITING.getTaskStatus())
                .andTaskTypeEqualTo(ShopeeFeedTaskEnum.PRODUCT_PUBLISH.getValue());
        List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Shopee.name());
        if(CollectionUtils.isNotEmpty(feedTasks)) {
            rsp.setMessage("该模板已存在刊登任务,无需重复提交。");
            return rsp;
        }

        //初始化处理报告
        FeedTask feedTask = initFeedTask(task ->{
            task.setAssociationId(shopeeTemplate.getId()+"");
            task.setAccountNumber(shopeeTemplate.getAccountNumber());
            task.setArticleNumber(shopeeTemplate.getSku());
            task.setCreatedBy(userName);
        });

        //置为刊登中
        shopeeTemplateNewService.updatePublishStatus(shopeeTemplate.getId(), ShopeeNewPublishStatusEnum.PUBLISHING.getCode(),new Date());

        //发送mq
        try{
            ShopeePublishParam publishParam = new ShopeePublishParam();
            publishParam.setTemplateId(shopeeTemplate.getId().longValue());
            publishParam.setFeedTaskId(feedTask.getId());
            publishParam.setPublishType(ShopeePublishTypeEnum.MANUAL_TEMPLATE.getCode());
            publishParam.setPublishUserCode(userName);
            publishParam.setAccountNumber(shopeeTemplate.getAccountNumber());

            rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));
        } catch (Exception e) {
            log.error(String.format("模板[%s]发送mq失败", shopeeTemplate.getId()), e);
            rsp.setMessage("发送mq失败:"+ e.getMessage());
            //置为失败
            shopeeTemplateNewService.updatePublishStatus(shopeeTemplate.getId(), ShopeeNewPublishStatusEnum.FAIL.getCode(),new Date());
            // 日志...
            updateFeedTask(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
        }

        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 批量刊登发送队列
     * @param shopeePublishObjectList
     * @param currentUser
     * @return
     */
    public ResponseJson batchPublishTemplateToMq(List<ShopeePublishObject> shopeePublishObjectList, String currentUser) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        if(CollectionUtils.isEmpty(shopeePublishObjectList)){
            rsp.setMessage("参数为空!");
            return rsp;
        }

        //毛利率必须大于0
        List<Integer> failIds = shopeePublishObjectList.stream()
                .filter(o -> o.getProfitRate() == null || o.getProfitRate() <= 0)
                .map(o -> o.getTemplateId())
                .collect(Collectors.toList());
        if(failIds.size() > 0){
            rsp.setMessage(String.format("模板编号%s，毛利润必须大于0", failIds));
            return rsp;
        }
//        //发货天数必须大于0
//        failIds = shopeePublishObjectList.stream()
//                .filter(o -> o.getDaysToShip() == null || o.getDaysToShip() <= 0)
//                .map(o -> o.getTemplateId())
//                .collect(Collectors.toList());
//        if(failIds.size() > 0){
//            rsp.setMessage(String.format("模板编号%s，发货天数必须大于0", failIds));
//            return rsp;
//        }
        //标题关键词账号 不能为空
        failIds = shopeePublishObjectList.stream()
                .filter(o -> StringUtils.isBlank(o.getName()) || StringUtils.isBlank(o.getAccounts()))
                .map(o -> o.getTemplateId())
                .collect(Collectors.toList());
        if(failIds.size() > 0){
            rsp.setMessage(String.format("模板编号%s，标题或账号为空", failIds));
            return rsp;
        }

        List<Integer> idList = shopeePublishObjectList.stream().map(o -> o.getTemplateId()).collect(Collectors.toList());
        ShopeeTemplateNewExample example = new ShopeeTemplateNewExample();
        example.createCriteria().andIdIn(idList);
        example.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
        List<ShopeeTemplateNew> dbList = shopeeTemplateNewService.selectByExample(example);

        //刊登中 刊登成功不允许刊登
        failIds = dbList.stream()
                .filter(o -> o.getPublishStatus() == ShopeeNewPublishStatusEnum.PUBLISHING.getCode()
                        || o.getPublishStatus() == ShopeeNewPublishStatusEnum.SUCCESS.getCode())
                .map(o -> o.getId())
                .collect(Collectors.toList());
        if(failIds.size() > 0){
            rsp.setMessage(String.format("模板编号%s状态为：刊登中或刊登成功，不允许再次刊登！", failIds));
            return rsp;
        }

        //验证店铺 与站点 是否匹配
        List<String> accList = shopeePublishObjectList.stream().map(o -> o.getAccounts()).distinct().collect(Collectors.toList());
        ShopeeAccountConfigExample accountEx = new ShopeeAccountConfigExample();
        accountEx.createCriteria().andAccountIn(accList);
        List<ShopeeAccountConfig> accountList = shopeeAccountConfigService.selectByExample(accountEx);
        Map<String, String> accountSiteMap = accountList.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSite()))
                .collect(Collectors.toMap(o -> o.getAccount(), o -> o.getSite(), (o1, o2) -> o1));
        Map<Integer, String> templateSiteMap = shopeePublishObjectList.stream()
                .collect(Collectors.toMap(o -> o.getTemplateId(), o -> {
                    if (accountSiteMap.containsKey(o.getAccounts())) {
                        return accountSiteMap.get(o.getAccounts());
                    }
                    return "";
                }));
        failIds = dbList.stream()
                .filter(o -> !StringUtils.equals(o.getSite(), templateSiteMap.get(o.getId())))
                .map(o -> o.getId())
                .collect(Collectors.toList());
        if(failIds.size() > 0){
            rsp.setMessage(String.format("模板编号%s：选择的店铺站点与模板站点不匹配！", failIds));
            return rsp;
        }


        Map<Integer, ShopeeTemplateNew> dbMap = dbList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
        for (ShopeePublishObject publishObject : shopeePublishObjectList) {
            ShopeeTemplateNew dbTemplate = dbMap.get(publishObject.getTemplateId());
            if(dbTemplate == null){
                continue;
            }

            // 处理报告状态，如果是待刊登表示 已经存在排队任务
            FeedTaskExample feedTaskExample = new FeedTaskExample();
            feedTaskExample.createCriteria()
                    .andAssociationIdEqualTo(publishObject.getTemplateId().toString())
                    .andPlatformEqualTo(Platform.Shopee.name())
                    .andTaskStatusEqualTo(FeedTaskStatusEnum.WAITING.getTaskStatus())
                    .andTaskTypeEqualTo(ShopeeFeedTaskEnum.PRODUCT_PUBLISH.getValue());
            List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Shopee.name());
            if(CollectionUtils.isNotEmpty(feedTasks)) {
                shopeeTemplateNewService.updatePublishStatus(publishObject.getTemplateId(), ShopeeNewPublishStatusEnum.FAIL.getCode(),new Date());
                // 日志...
                insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), "该模板已存在刊登任务,无需重复提交。");
                continue;
            }

            //初始化处理报告
            FeedTask feedTask = null;
            try {
                //初始化处理报告
                feedTask = initFeedTask(task ->{
                    task.setAssociationId(publishObject.getTemplateId()+"");
                    task.setAccountNumber(publishObject.getAccounts());
                    task.setArticleNumber(dbTemplate.getSku());
                    task.setCreatedBy(currentUser);
                });

                //置为刊登中
                ShopeeTemplateNew update = new ShopeeTemplateNew();
                update.setId(publishObject.getTemplateId());
                update.setName(publishObject.getName());
                update.setAccountNumber(publishObject.getAccounts());
                update.setKeyword(publishObject.getKeyword());
                update.setDaysToShip(publishObject.getDaysToShip());
                update.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
                update.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                update.setLastUpdatedBy(currentUser);
                List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(dbTemplate.getShopeeSkusStr(), ShopeeSku.class);
                for (ShopeeSku o : shopeeSkuList) {
                    o.setProfitMargin(publishObject.getProfitRate());
                    o.setPrice(null);
                }
                update.setShopeeSkusStr(JSON.toJSONString(shopeeSkuList));
                shopeeTemplateNewService.updateByPrimaryKeySelective(update);

                //发送mq
                ShopeePublishParam publishParam = new ShopeePublishParam();
                publishParam.setTemplateId(update.getId().longValue());
                publishParam.setFeedTaskId(feedTask.getId());
                publishParam.setPublishType(ShopeePublishTypeEnum.BATCH_TEMPLATE.getCode());
                publishParam.setPublishUserCode(currentUser);
                publishParam.setAccountNumber(update.getAccountNumber());
                publishParam.setProfitRate(publishObject.getProfitRate());

                rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));
            }catch (Exception e){
                log.error(String.format("模板[%s]刊登出错:", publishObject.getTemplateId()), e);
                shopeeTemplateNewService.updatePublishStatus(publishObject.getTemplateId(), ShopeeNewPublishStatusEnum.FAIL.getCode(),new Date());
                // 日志...
                if(feedTask == null){
                    insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
                }else {
                    updateFeedTask(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
                }
            }
        }


        rsp.setMessage("后台已开始批量刊登，请到处理报告处进行查看结果");
        return rsp;
    }

    /**
     * @param param
     * @param isTimingPublish  true：定时刊登，false：直接刊登
     * @return
     */
    public ApiResult<?> autoPublishByTypeToMq(ShopeeSpuPublishParam param, boolean isTimingPublish) {
        List<String> accountList = Arrays.asList(param.getAccountNumber().split(","));
        ShopeeAccountConfigExample ex = new ShopeeAccountConfigExample();
        ex.createCriteria().andAccountIn(accountList);
        List<ShopeeAccountConfig> accList = shopeeAccountConfigService.selectByExample(ex);
        if(CollectionUtils.isEmpty(accList)){
            return ApiResult.newError("数据库查询不到店铺信息!");
        }

        Map<String, ShopeeAccountConfig> accountMap = accList.stream().collect(Collectors.toMap(o -> o.getAccount().toLowerCase(), o -> o, (o1, o2) -> o1));

        String user = WebUtils.getUserName();
        if(StringUtils.isBlank(user)){
            user = DataContextHolder.getUsername();
        }
        String userName = user;

        List<String> spuList = Arrays.asList(param.getSpu().split(","));
        Integer number = param.getNumber();
        if(null == number || number <= 0) {
            number = spuList.size();
        }

        for (String account : accountList) {
            ShopeeAccountConfig accountConfig = accountMap.get(account.toLowerCase());
            if(accountConfig == null){
                continue;
            }
            if(accountConfig.getProfit() == null || accountConfig.getProfit() <= 0) {
                // 日志...
                ShopeeTemplateNew dbTemplate = new ShopeeTemplateNew();
                dbTemplate.setId(-1);
                dbTemplate.setAccountNumber(account);
                dbTemplate.setSku("-");
                insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), "该店铺的毛利不能为空或<=0，请先完成店铺配置!");
                continue;
            }

            List<List<String>> lists  = PagingUtils.pagingList(spuList, number);
            for (int i = 0; i < lists.size(); i++) {
                List<String> spuPageList = lists.get(i);
                for (int j = 0; j < spuPageList.size() ; j++) {
                    String spu = spuPageList.get(j);
                    try {
                        ShopeePublishParam publishParam = new ShopeePublishParam();
                        publishParam.setSpu(spu);
                        publishParam.setAccountNumber(account);
//                    publishParam.setProfitRate(accountConfig.getProfit());
                        publishParam.setByTemplateId(param.getByTemplateId());
                        publishParam.setByCategoryMappingId(param.getByCategoryMappingId());
                        publishParam.setPublishUserCode(userName);
                        if(isTimingPublish){
                            ShopeePublishQueueExample queueExample = new ShopeePublishQueueExample();
                            queueExample.createCriteria()
                                    .andSellerIdEqualTo(account)
                                    .andSkuEqualTo(spu)
                                    .andStatusIn(Arrays.asList(ShopeeQueueStatusEnum.WAITING.getCode(), ShopeeQueueStatusEnum.QUEUING.getCode()));
                            int count = shopeePublishQueueService.countByExample(queueExample);
                            if(count > 0){
                                // 日志...
                                ShopeeTemplateNew dbTemplate = new ShopeeTemplateNew();
                                dbTemplate.setId(-1);
                                dbTemplate.setAccountNumber(account);
                                dbTemplate.setSku(spu);
                                insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), String.format("该%s %s,已存在待定时刊登的队列!", account, spu));
                            }else{
                                //定时刊登
                                publishParam.setPublishType(ShopeePublishTypeEnum.SPU_TIMING.getCode());

                                ShopeePublishQueue queue = new ShopeePublishQueue();
                                //保存在这里，刊登时候取出来
                                queue.setRemark(JSON.toJSONString(publishParam));
                                queue.setSellerId(account);
                                queue.setSku(spu);
                                queue.setType(TemplateQueueTypeEnum.SPU.intCode());
                                queue.setStatus(ShopeeQueueStatusEnum.WAITING.getCode());
                                long startMilis = param.getStartTime().getTime() + 1000L * 60 * 60 * 24 * i +  j * param.getIntervalTime().longValue() * 60 * 1000;
                                queue.setStartTime(new Timestamp(startMilis));
                                queue.setCreationDate(new Timestamp(System.currentTimeMillis()));
                                queue.setCreatedBy(userName);
                                queue.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                                queue.setLastUpdatedBy(userName);
                                shopeePublishQueueService.insert(queue);
                            }
                        }else{
                            // 处理报告状态，如果是待刊登表示 已经存在排队任务
                            FeedTaskExample feedTaskExample = new FeedTaskExample();
                            feedTaskExample.createCriteria()
                                    .andPlatformEqualTo(Platform.Shopee.name())
                                    .andAccountNumberEqualTo(account)
                                    .andArticleNumberEqualTo(spu)
                                    .andTaskStatusEqualTo(FeedTaskStatusEnum.WAITING.getTaskStatus())
                                    .andTaskTypeEqualTo(ShopeeFeedTaskEnum.PRODUCT_PUBLISH.getValue());
                            int count = feedTaskService.countByExample(feedTaskExample, Platform.Shopee.name());
                            if(count > 0) {
                                // 日志...
                                ShopeeTemplateNew dbTemplate = new ShopeeTemplateNew();
                                dbTemplate.setId(-1);
                                dbTemplate.setAccountNumber(account);
                                dbTemplate.setSku(spu);
                                insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), String.format("该%s %s,已存在待直接刊登的队列!", account, spu));
                            }else{
                                //初始化处理报告
                                FeedTask feedTask = initFeedTask(task ->{
                                    task.setAccountNumber(account);
                                    task.setArticleNumber(spu);
                                    task.setCreatedBy(userName);
                                });

                                //直接刊登
                                publishParam.setPublishType(ShopeePublishTypeEnum.SPU_DIRECT.getCode());
                                publishParam.setFeedTaskId(feedTask.getId());

                                //发送队列
                                rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));
                            }
                        }
                    }catch (Exception e){
                        log.error("刊登操作失败", e);
                        // 日志...
                        ShopeeTemplateNew dbTemplate = new ShopeeTemplateNew();
                        dbTemplate.setId(-1);
                        dbTemplate.setAccountNumber(account);
                        dbTemplate.setSku(spu);
                        insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
                    }
                }
            }
        }

        return ApiResult.newSuccess();
    }

    /**
     * 定时队列发送mq
     * @param spuQueueList
     */
    public void queueSendMq(List<ShopeePublishQueue> spuQueueList) {
        if(CollectionUtils.isEmpty(spuQueueList)){
            return;
        }
        for (ShopeePublishQueue queue : spuQueueList) {
            String json = queue.getRemark();
            if(StringUtils.isEmpty(json)){
                continue;
            }
            try {
                ShopeePublishParam publishParam = JSON.parseObject(json, new TypeReference<ShopeePublishParam>() {
                });

                //初始化处理报告
                FeedTask feedTask = initFeedTask(task ->{
                    task.setAccountNumber(queue.getSellerId());
                    task.setArticleNumber(queue.getSku());
                    task.setCreatedBy(queue.getCreatedBy());
                });

                //把状态改成正在排队
                queue.setStatus(ShopeeQueueStatusEnum.QUEUING.getCode());
                shopeePublishQueueService.updateByPrimaryKeySelective(queue);

                publishParam.setQueueId(queue.getId());
                publishParam.setFeedTaskId(feedTask.getId());
                //发送队列
                rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));
            }catch (Exception e){
                log.error(String.format("[%s]queue error", queue.getId()), e);
                // 日志...
                ShopeeTemplateNew dbTemplate = new ShopeeTemplateNew();
                dbTemplate.setId(-1);
                dbTemplate.setAccountNumber(queue.getSellerId());
                dbTemplate.setSku(queue.getSku());
                insertFeedTask(dbTemplate, FeedTaskResultStatusEnum.FAIL.getResultStatus(), String.format("{'queueId':%s, 'publishParam': %s, 'errorMsg':%s}", queue.getId(), json, e.getMessage()));
            }
        }
    }

    public FeedTask initFeedTask(Consumer<FeedTask> consumer){
        FeedTask feedTask = new FeedTask();
        feedTask.setTaskType(ShopeeFeedTaskEnum.PRODUCT_PUBLISH.getValue());
        feedTask.setPlatform(Platform.Shopee.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.WAITING.getTaskStatus());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();

        //回调
        consumer.accept(feedTask);

        //新增日志
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    private void insertFeedTask(ShopeeTemplateNew dbTemplate, int resultStatus, String resultMsg) {
        //初始化处理报告
        FeedTask feedTask = new FeedTask();
        if(dbTemplate != null){
            feedTask.setAssociationId(dbTemplate.getId()+"");
            feedTask.setAccountNumber(dbTemplate.getAccountNumber());
            feedTask.setArticleNumber(dbTemplate.getSku());
        }
        feedTask.setTaskType(ShopeeFeedTaskEnum.PRODUCT_PUBLISH.getValue());
        feedTask.setPlatform(Platform.Shopee.name());
        feedTask.setCreatedBy(WebUtils.getUserName());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(resultStatus);
        feedTask.setResultMsg(resultMsg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        //新增日志
        feedTaskService.insertSelective(feedTask);

        if(dbTemplate != null && dbTemplate.getId() != null && dbTemplate.getId() != -1){
            if(resultStatus == FeedTaskResultStatusEnum.SUCCESS.getResultStatus()){
                shopeeTemplateNewService.updatePublishStatus(dbTemplate.getId(), ShopeeNewPublishStatusEnum.SUCCESS.getCode(),new Date());
            }else{
                shopeeTemplateNewService.updatePublishStatus(dbTemplate.getId(), ShopeeNewPublishStatusEnum.FAIL.getCode(),new Date());
            }
        }
    }


    private void updateFeedTask(FeedTask feedTask, int resultStatus, String resultMsg) {
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(resultStatus);
        feedTask.setResultMsg(resultMsg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        if(feedTask != null && feedTask.getId() != null){
            feedTaskService.updateByPrimaryKeySelective(feedTask);
        }
    }


    /**
     * 全球产品spu自动刊登, 定时刊登和自动刊登，组合刊登也在这里 skuDataSource 来区分就行了。后续在定时任务那边改
     * @param param
     * @param isTimingPublish
     * @return
     */
    public ApiResult<?> globalAutoPublishByTypeToMq(ShopeeSpuGlobalPublishParam param, boolean isTimingPublish, String user) {
        String testMessage = "globalAutoPublishByTypeToMq MTSKU刊登";
        Long begin = System.currentTimeMillis();
        if(StringUtils.isBlank(user)){
            user = DataContextHolder.getUsername();
        }
        String userName = user;

        List<String> accountList = CommonUtils.splitList(param.getAccounts(), ",");
        //店铺排个序
        Collections.sort(accountList);
        String accountJoin = accountList.stream().collect(Collectors.joining(","));

        List<String> spuList = Arrays.asList(param.getSpu().split(","));
        Integer number = param.getNumber();
        if(null == number || number <= 0) {
            number = spuList.size();
        }
        log.info(testMessage + " number {}" , number);
        List<List<String>> lists  = PagingUtils.pagingList(spuList, number);
        for (int i = 0; i < lists.size(); i++) {
            List<String> spuPageList = lists.get(i);
            for (int j = 0; j < spuPageList.size() ; j++) {
                log.info(testMessage + " i={}  j={}" , i, j);
                String spu = spuPageList.get(j);
                try {
                    ShopeePublishParam publishParam = new ShopeePublishParam();
                    publishParam.setSpu(spu);
                    publishParam.setSubAccount(param.getSubAccount());
                    publishParam.setMerchant(param.getMerchant());
                    publishParam.setMerchantId(param.getMerchantId());
                    publishParam.setAccounts(param.getAccounts());
                    publishParam.setSiteList(param.getSiteList());
                    publishParam.setSkuDataSource(Optional.ofNullable(param.getSkuDataSource()).orElse(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
                    publishParam.setByTemplateId(param.getByTemplateId());
                    publishParam.setByCategoryMappingId(param.getByCategoryMappingId());
                    publishParam.setPublishUserCode(userName);
                    publishParam.setPublishType(ShopeePublishTypeEnum.SPU_GP_DIRECT.getCode());
                    publishParam.setModuleType(ShopeeModuleTypeEnum.GP_TP.name());
                    publishParam.setCopyWritingType(param.getCopyWritingType());

                    if (isTimingPublish) {
                        //定时刊登...
                        publishParam.setPublishType(ShopeePublishTypeEnum.SPU_GP_TIMING.getCode());

                        ShopeeGlobalPublishQueueExample queueExample = new ShopeeGlobalPublishQueueExample();
                        queueExample.createCriteria().andSkuEqualTo(spu)
                                .andSubAccountEqualTo(param.getSubAccount())
                                //要刊登的店铺完全相同
                                .andAccountsEqualTo(accountJoin)
                                .andStatusIn(Arrays.asList(ShopeeQueueStatusEnum.WAITING.getCode(), ShopeeQueueStatusEnum.QUEUING.getCode()));
                        int count = shopeeGlobalPublishQueueService.countByExample(queueExample);
                        if(count > 0){
                            // 日志...
                            ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
                                task.setAssociationId("-1");
                                task.setAccountNumber(param.getSubAccount());
                                task.setArticleNumber(spu);
                                task.setCreatedBy(userName);
                                task.setResultMsg(String.format("该批店铺：%s, 全部已存在待定时刊登的队列!", accountJoin, spu));
                                task.setAttribute4(JSON.toJSONString(param));
                                task.setAttribute5(param.getSubAccount());
                            });
                        }else{
                            ShopeeGlobalPublishQueue publishQueue = new ShopeeGlobalPublishQueue();
                            publishQueue.setAccounts(accountJoin);
                            publishQueue.setMerchant(param.getMerchant());
                            publishQueue.setMerchantId(param.getMerchantId());
                            publishQueue.setSubAccount(param.getSubAccount());
                            publishQueue.setSku(spu);
                            publishQueue.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
                            publishQueue.setType(TemplateQueueTypeEnum.SPU.intCode());
                            long startMilis = param.getStartTime().getTime() + 1000L * 60 * 60 * 24 * i + j * param.getIntervalTime().longValue() * 60 * 1000;
                            publishQueue.setStartTime(new Timestamp(startMilis));
                            publishQueue.setCreatedBy(userName);
                            publishQueue.setLastUpdatedBy(userName);
                            publishQueue.setStatus(ShopeeQueueStatusEnum.WAITING.getCode());
                            publishQueue.setCopyWritingType(param.getCopyWritingType());
                            publishQueue.setSkuDataSource(param.getSkuDataSource());
                            publishParam.setSkuDataSource(Optional.ofNullable(param.getSkuDataSource()).orElse(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
                            shopeeGlobalPublishQueueService.insert(publishQueue);
                        }
                    } else {
                        Long ij1 = System.currentTimeMillis();
                        //直接刊登
                        // 处理报告状态，如果是待刊登表示 已经存在排队任务（1个小时之内待刊登）
                        Date _1hours = DateUtils.addHours(new Date(), -1);
                        FeedTaskExample feedTaskExample = new FeedTaskExample();
                        feedTaskExample.createCriteria()
                                .andPlatformEqualTo(Platform.Shopee.name())
                                .andAccountNumberEqualTo(param.getSubAccount())
                                .andArticleNumberEqualTo(spu)
                                .andTaskStatusEqualTo(FeedTaskStatusEnum.WAITING.getTaskStatus())
                                .andTaskTypeEqualTo(ShopeeFeedTaskEnum.GLOBAL_PRODUCT_PUBLISH.getValue())
                                .andCreateTimeGreaterThanOrEqualTo(_1hours)
                                //要刊登的店铺完全相同
                                .andAttribute3EqualTo(accountJoin);
                        int count = feedTaskService.countByExample(feedTaskExample, Platform.Shopee.name());
                        Long ij2 = System.currentTimeMillis();
                        log.info(testMessage + " feedTaskExample search time{}" , ij2 - ij1);
                        if (count > 0) {
                            // 日志...
                            ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
                                task.setAssociationId("-1");
                                task.setAccountNumber(param.getSubAccount());
                                task.setArticleNumber(spu);
                                task.setCreatedBy(userName);
                                task.setResultMsg(String.format("该批店铺：%s, 全部已存在待直接刊登的队列!", accountJoin, spu));
                                task.setAttribute4(JSON.toJSONString(param));
                                task.setAttribute5(param.getSubAccount());
                            });
                            Long ij3 = System.currentTimeMillis();
                            log.info(testMessage + "insertFinishFeedTask  search time{}" , ij3 - ij2);
                        } else {
                            //init template
                            EsShopeeGlobalTemplate bean = new EsShopeeGlobalTemplate();
                            bean.setSku(spu);
                            bean.setSubAccount(param.getSubAccount());
                            bean.setMerchant(param.getMerchant());
                            bean.setMerchantId(param.getMerchantId());
                            bean.setAccounts(CommonUtils.splitList(param.getAccounts(), ","));
                            bean.setType(ShopeeTemplateTypeEnum.AUTO_PUBLISH.getCode());
                            bean.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
                            bean.setDataSource(Optional.ofNullable(param.getSkuDataSource()).orElse(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
                            bean.setIsParent(false);
                            bean.setCreateDate(new Timestamp(System.currentTimeMillis()));
                            bean.setCopyWritingType(param.getCopyWritingType());
                            shopeeGlobalTemplateEsService.save(bean);
                            Long ij4 = System.currentTimeMillis();
                            log.info(testMessage + " shopeeGlobalTemplateEsService.save time{}" , ij4 - ij2);

                            //初始化处理报告
                            FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                                task.setAssociationId(StrUtil.objectToStr(bean.getId()));
                                task.setAccountNumber(param.getSubAccount());
                                task.setArticleNumber(spu);
                                task.setCreatedBy(userName);
                                task.setAttribute3(accountJoin);
                                task.setAttribute4(JSON.toJSONString(param));
                                task.setAttribute5(param.getSubAccount());
                            });
                            publishParam.setTemplateId(bean.getId());
                            publishParam.setFeedTaskId(feedTask.getId());

                            Long ij5 = System.currentTimeMillis();
                            log.info(testMessage + "ShopeeFeedTaskHandleUtil.initFeedTask sucess time{}" , ij5 - ij4);

                            //发送队列
                            rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));

                            Long ij6 = System.currentTimeMillis();
                            log.info(testMessage + "rabbitMqSender.send time{}" , ij6 - ij5);
                            log.info(testMessage + "ij time {}" , ij6 - ij1);
                        }
                    }
                } catch (Exception e) {
                    log.error("刊登操作失败", e);
                    // 日志...
                    ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
                        task.setAssociationId("-1");
                        task.setAccountNumber(param.getSubAccount());
                        task.setArticleNumber(spu);
                        task.setCreatedBy(userName);
                        task.setResultMsg(e.getMessage());
                        task.setAttribute4(JSON.toJSONString(param));
                        task.setAttribute5(param.getSubAccount());
                    });
                }
            }
        }

        Long end = System.currentTimeMillis();
        log.info(testMessage + " begin - end {} number {}" , end - begin , number);

        return ApiResult.newSuccess();
    }

    public void globalAutoPublishByQueueeToMq(ShopeeGlobalPublishQueue publishQueue) {
        try {
            List<String> accountList = CommonUtils.splitList(publishQueue.getAccountsRemovePrefix(), ",");
            //店铺排个序
            Collections.sort(accountList);
            String accountJoin = accountList.stream().collect(Collectors.joining(","));

            ShopeePublishParam publishParam = new ShopeePublishParam();
            publishParam.setSpu(publishQueue.getSku());
            publishParam.setSubAccount(publishQueue.getSubAccount());
            publishParam.setMerchant(publishQueue.getMerchant());
            publishParam.setMerchantId(publishQueue.getMerchantId());
            publishParam.setAccounts(publishQueue.getAccountsRemovePrefix());
            publishParam.setSiteList(publishQueue.getSiteList());
            publishParam.setPublishUserCode(publishQueue.getCreatedBy());
            publishParam.setPublishType(ShopeePublishTypeEnum.SPU_GP_TIMING.getCode());
            publishParam.setModuleType(ShopeeModuleTypeEnum.GP_TP.name());
            publishParam.setQueueId(publishQueue.getId());
            publishParam.setPublishRole(publishQueue.getPublishRole());
            publishParam.setCopyWritingType(publishQueue.getCopyWritingType());
            publishParam.setSkuDataSource(Optional.ofNullable(publishQueue.getSkuDataSource()).orElse(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
            publishParam.setRuleJson(publishQueue.getRuleJson());
            publishParam.setRuleName(publishQueue.getRuleName());
            publishParam.setConfigSource(publishQueue.getConfigSource());
            EsShopeeGlobalTemplate bean = new EsShopeeGlobalTemplate();
            bean.setSku(publishQueue.getSku());
            bean.setSubAccount(publishQueue.getSubAccount());
            bean.setMerchant(publishQueue.getMerchant());
            bean.setMerchantId(publishQueue.getMerchantId());
            bean.setAccounts(publishQueue.getAccountList());
            bean.setType(ShopeeTemplateTypeEnum.TIMER_PUBLISH.getCode());
            bean.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
            bean.setIsParent(false);
            bean.setCreatedBy(publishQueue.getCreatedBy());
            bean.setCreateDate(new Timestamp(System.currentTimeMillis()));
            bean.setLastUpdatedBy(publishQueue.getCreatedBy());
            bean.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            bean.setPublishRole(publishQueue.getPublishRole());
            bean.setCopyWritingType(publishQueue.getCopyWritingType());
            bean.setDataSource(Optional.ofNullable(publishQueue.getSkuDataSource()).orElse(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
            shopeeGlobalTemplateEsService.save(bean);

            //初始化处理报告
            FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                task.setAssociationId(StrUtil.objectToStr(bean.getId()));
                task.setAccountNumber(publishQueue.getSubAccount());
                task.setArticleNumber(publishQueue.getSku());
                Optional.ofNullable(publishQueue.getRuleName()).ifPresent(task::setAttribute1);
                task.setCreatedBy(publishQueue.getCreatedBy());
                task.setAttribute3(accountJoin);
                task.setAttribute4(JSON.toJSONString(publishQueue, SerializerFeature.NotWriteDefaultValue));
                task.setAttribute5(publishQueue.getSubAccount());
            });
            publishParam.setTemplateId(bean.getId());
            publishParam.setFeedTaskId(feedTask.getId());

            //发送队列
            rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_PUBLISH_ROUTE_KEY, JSON.toJSON(publishParam));

            shopeeGlobalPublishQueueService.updateStatus(publishQueue.getId(), ShopeeQueueStatusEnum.QUEUING.getCode(), feedTask.getId(), null);
        }catch (Exception e){
            log.error("刊登操作失败", e);
            // 日志...
            FeedTask feedTask = ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
                task.setAssociationId("-1");
                task.setAccountNumber(publishQueue.getSubAccount());
                task.setArticleNumber(publishQueue.getSku());
                task.setCreatedBy(publishQueue.getCreatedBy());
                task.setResultMsg(e.getMessage());
                task.setAttribute4(JSON.toJSONString(publishQueue, SerializerFeature.NotWriteDefaultValue));
                task.setAttribute5(publishQueue.getSubAccount());
            });

            ShopeeGlobalPublishQueue shopeeGlobalPublishQueue = new ShopeeGlobalPublishQueue();
            shopeeGlobalPublishQueue.setId(publishQueue.getId());
            shopeeGlobalPublishQueue.setStatus(ShopeeQueueStatusEnum.FINISH.getCode());
            shopeeGlobalPublishQueue.setEndTime(new Timestamp(System.currentTimeMillis()));
            shopeeGlobalPublishQueue.setLogId(feedTask.getId());
            shopeeGlobalPublishQueue.setPublishResult(ShopeeNewPublishStatusEnum.FAIL.getCode());
            shopeeGlobalPublishQueueService.updateByPrimaryKeySelective(shopeeGlobalPublishQueue);
        }
    }
}
