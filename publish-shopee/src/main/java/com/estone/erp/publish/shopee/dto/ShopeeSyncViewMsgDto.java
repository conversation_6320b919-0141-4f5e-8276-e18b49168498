package com.estone.erp.publish.shopee.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-03-05 10:27
 */
@Data
public class ShopeeSyncViewMsgDto {

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 商品Id
     */
    private String productId;

    /**
     * 7天浏览量
     */
    private Integer view_7d_count;

    /**
     * 14天浏览量
     */
    private Integer view_14d_count;

    /**
     * 30天浏览量
     */
    private Integer view_30d_count;
    /**
     * 60天访问量
     */
    private Integer view_60d_count;
    /**
     * 90天访问量
     */
    private Integer view_90d_count;
    /**
     * 7天搜索点击人数
     */
    private Integer search_7d_count;

    /**
     * 14天搜索点击人数
     */
    private Integer search_14d_count;

    /**
     * 30天搜索点击人数
     */
    private Integer search_30d_count;
    /**
     * 60天搜索点击人数
     */
    private Integer search_60d_count;
    /**
     * 90天搜索点击人数
     */
    private Integer search_90d_count;
    /**
     * 流量最后统计时间
     */
    private LocalDateTime lastViewUpdateDate;

    /**
     * 是否重置PV为空值
     */
    private Boolean resetPVEmpty;
}
