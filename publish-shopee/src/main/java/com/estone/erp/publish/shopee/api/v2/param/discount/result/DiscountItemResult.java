package com.estone.erp.publish.shopee.api.v2.param.discount.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/14 16:27
 * @description
 */
@Getter
@Setter
public class DiscountItemResult {

    /** 折扣ID */
    @JSONField(name = "discount_id")
    private Long discountId;

    /** 添加成功的项目数 */
    private Integer count;

    /** 错误详情 */
    @JSONField(name = "error_list")
    private List<DiscountItemDetailResult> errorList;

}
