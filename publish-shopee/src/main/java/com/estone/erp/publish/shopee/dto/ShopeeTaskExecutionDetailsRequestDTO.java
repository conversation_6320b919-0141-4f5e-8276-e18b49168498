package com.estone.erp.publish.shopee.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/11 下午4:32
 */
@Data
public class ShopeeTaskExecutionDetailsRequestDTO {

    /**
     * 统计总表规则名称类型枚举
     */
    private Integer ruleNameType;

    /**
     * 销售类型
     */
    private Integer saleType;

    /**
     * 操作类型列表
     */
    private List<Integer> operationTypeList;

    /**
     * 账户列表
     */
    private List<String> accountList;

    /**
     * 销售列表
     */
    private List<String> saleList;

    /**
     * 销售组长列表
     */
    private List<String> saleLeaderList;

    /**
     * 销售主管列表
     */
    private List<String> salesSupervisorList;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

}
