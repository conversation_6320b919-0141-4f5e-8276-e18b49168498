package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:34
 * @description
 */
@Getter
@Setter
public class OptionDtoV2 {

    @JSONField(name = "option")
    private String option;

    /**
     * { "image_id": "xxxxxxx" }
     * */
    @JSONField(name = "image")
    private Map<String, String> image ;

}
