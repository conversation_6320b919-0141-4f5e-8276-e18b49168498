package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:34
 * @description
 */
@Getter
@Setter
public class OptionDtoV2 {

    /**
     * 必传
     */
    @JSONField(name = "variation_option_id")
    private Integer variationOptionId;

    /**
     * 非必传
     */
    @JSONField(name = "variation_option_name")
    private String variationOptionName;

    /**
     * 非必传
     */
    @JSONField(name = "image_id")
    private String imageId;

    /**
     * 平台接口文档该字段，接口调用存在
     */
    @JSONField(name = "image_url")
    private String imageUrl;
}
