package com.estone.erp.publish.shopee.jobHandler.stock;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.FeedTaskTypeConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskMsgEnum;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 南宁地区Shopee店铺禁止刊登SKU库存管理定时任务
 * 
 * 功能描述：
 * 1. 获取所有南宁地区的Shopee店铺
 * 2. 扫描店铺中在线状态的SKU
 * 3. 筛选特殊标签字段包含"NN禁止刊登"关键字的商品
 * 4. 将符合条件的SKU库存数量设置为0
 * 
 * 执行频率：每天2次（上午9:00和下午15:00）
 * 
 * <AUTHOR> Generated
 * @date 2025-06-27
 */
@Component
@Slf4j
public class ShopeeNanningProhibitedSkuStockHandler extends AbstractJobHandler {

    @Autowired
    private ShopeeItemEsService shopeeItemEsService;
    
    @Autowired
    private ShopeeGlobalItemService shopeeGlobalItemService;

    /**
     * 内部参数类，用于接收JSON格式的任务参数
     */
    @Getter
    @Setter
    public static class InnerParam {
        /**
         * 指定账号列表（可选，用于测试）
         */
        private List<String> accountList;
        
        /**
         * 指定SKU列表（可选，用于测试）
         */
        private List<String> skuList;
        
        /**
         * 是否为测试模式（可选，测试模式下不实际修改库存）
         */
        private Boolean testMode = false;
    }

    /**
     * 用于缓存账号信息，避免重复查询
     */

    /**
     * 异步处理线程池大小（建议根据系统资源调整）
     */
    private static final int ASYNC_THREAD_POOL_SIZE = 5;

    public ShopeeNanningProhibitedSkuStockHandler() {
        super("ShopeeNanningProhibitedSkuStockHandler");
    }

    @Override
    @XxlJob("ShopeeNanningProhibitedSkuStockHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务开始执行");
        XxlJobLogger.log("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务开始执行");
        try {
            // 解析任务参数
            InnerParam innerParam = parseJobParam(param);
            
            // 获取南宁地区店铺列表
            List<SaleAccountAndBusinessResponse> nanningAccounts = getNanningShopeeAccounts(innerParam.getAccountList());
            
            if (CollectionUtils.isEmpty(nanningAccounts)) {
                XxlJobLogger.log("未找到符合条件的南宁地区Shopee店铺");
                log.info("未找到符合条件的南宁地区Shopee店铺");
                return ReturnT.SUCCESS;
            }
            
            XxlJobLogger.log("找到{}个南宁地区Shopee店铺", nanningAccounts.size());
            log.info("找到{}个南宁地区Shopee店铺", nanningAccounts.size());
            
            // 按店铺维度异步处理禁止刊登SKU
            AtomicInteger totalProcessedCount = new AtomicInteger(0);
            processAccountsAsyncWithThreadPool(nanningAccounts, innerParam, totalProcessedCount);
            
            XxlJobLogger.log("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务执行完成，共处理{}个SKU", totalProcessedCount.get());
            log.info("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务执行完成，共处理{}个SKU", totalProcessedCount.get());
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务执行失败", e);
            XxlJobLogger.log("南宁地区Shopee店铺禁止刊登SKU库存管理定时任务执行失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析任务参数
     * 
     * @param param JSON格式的参数字符串
     * @return 解析后的参数对象
     */
    private InnerParam parseJobParam(String param) {
        log.info("开始解析任务参数");
        
        InnerParam innerParam = new InnerParam();
        
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
                log.info("任务参数解析成功：{}", JSON.toJSONString(innerParam));
            } catch (Exception e) {
                log.error("任务参数解析失败，使用默认参数", e);
                XxlJobLogger.log("任务参数解析失败，使用默认参数：{}", e.getMessage());
            }
        }
        
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        
        return innerParam;
    }

    /**
     * 使用线程池按店铺维度异步处理禁止刊登SKU
     *
     * @param nanningAccounts 南宁地区店铺列表
     * @param innerParam 任务参数
     * @param totalProcessedCount 总处理数量计数器
     */
    private void processAccountsAsyncWithThreadPool(
            List<SaleAccountAndBusinessResponse> nanningAccounts,
            InnerParam innerParam,
            AtomicInteger totalProcessedCount) {

        log.info("开始创建{}个店铺的异步处理任务，线程池大小：{}", nanningAccounts.size(), ASYNC_THREAD_POOL_SIZE);
        XxlJobLogger.log("开始创建{}个店铺的异步处理任务，线程池大小：{}", nanningAccounts.size(), ASYNC_THREAD_POOL_SIZE);

        try {
            List<CompletableFuture<Integer>> futures = new ArrayList<>();

            // 为每个店铺创建异步任务
            for (SaleAccountAndBusinessResponse account : nanningAccounts) {
                CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("线程[{}]开始异步处理店铺[{}]", Thread.currentThread().getName(), account.getAccountNumber());
                        XxlJobLogger.log("线程[{}]开始异步处理店铺[{}]", Thread.currentThread().getName(), account.getAccountNumber());

                        int processedCount = processAccountProhibitedSkus(account, innerParam);
                        totalProcessedCount.addAndGet(processedCount);

                        log.info("线程[{}]店铺[{}]异步处理完成，共处理{}个SKU",
                            Thread.currentThread().getName(), account.getAccountNumber(), processedCount);
                        XxlJobLogger.log("线程[{}]店铺[{}]异步处理完成，共处理{}个SKU",
                            Thread.currentThread().getName(), account.getAccountNumber(), processedCount);

                        return processedCount;

                    } catch (Exception e) {
                        log.error("线程[{}]异步处理店铺[{}]时发生异常", Thread.currentThread().getName(), account.getAccountNumber(), e);
                        XxlJobLogger.log("线程[{}]异步处理店铺[{}]时发生异常：{}",
                            Thread.currentThread().getName(), account.getAccountNumber(), e.getMessage());
                        return 0;
                    }
                }, ShopeeExecutors.UPDATE_NN_PROHIBITED_SKU_STOCK_POOL);
                futures.add(future);
            }

            log.info("已创建{}个异步处理任务，等待所有任务完成", futures.size());
            XxlJobLogger.log("已创建{}个异步处理任务，等待所有任务完成", futures.size());

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join(); // 阻塞等待所有任务完成

            log.info("所有店铺异步处理完成");
            XxlJobLogger.log("所有店铺异步处理完成");

        } catch (Exception e) {
            log.error("异步处理店铺时发生异常", e);
            XxlJobLogger.log("异步处理店铺时发生异常：{}", e.getMessage());
        }
    }

    /**
     * 优雅关闭线程池
     *
     * @param executorService 线程池
     */
    private void shutdownExecutorService(ExecutorService executorService) {
        try {
            log.info("开始关闭线程池");

            // 停止接收新任务
            executorService.shutdown();

            // 等待已提交的任务完成，最多等待30秒
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("线程池在30秒内未能正常关闭，强制关闭");
                XxlJobLogger.log("线程池在30秒内未能正常关闭，强制关闭");

                // 强制关闭
                executorService.shutdownNow();

                // 再等待5秒
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("线程池强制关闭失败");
                    XxlJobLogger.log("线程池强制关闭失败");
                }
            }

            log.info("线程池已成功关闭");

        } catch (InterruptedException e) {
            log.error("关闭线程池时被中断", e);
            XxlJobLogger.log("关闭线程池时被中断：{}", e.getMessage());

            // 恢复中断状态
            Thread.currentThread().interrupt();

            // 强制关闭
            executorService.shutdownNow();
        }
    }

    /**
     * 获取南宁地区的Shopee店铺列表
     * 
     * @param specifiedAccounts 指定的账号列表（可选）
     * @return 南宁地区店铺列表
     */
    private List<SaleAccountAndBusinessResponse> getNanningShopeeAccounts(List<String> specifiedAccounts) {
        log.info("开始获取南宁地区Shopee店铺列表");
        
        try {
            // 获取所有Shopee店铺
            List<SaleAccountAndBusinessResponse> allShopeeAccounts = 
                AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SHOPEE);
            
            if (CollectionUtils.isEmpty(allShopeeAccounts)) {
                log.warn("未获取到任何Shopee店铺");
                return new ArrayList<>();
            }
            
            // 筛选南宁地区店铺
            List<SaleAccountAndBusinessResponse> nanningAccounts = allShopeeAccounts.stream()
                .filter(account -> {
                    // 检查是否为南宁仓店铺
                    boolean isNanningWarehouse = account.getShopeeColBool3() != null && account.getShopeeColBool3();
                    
                    // 检查店铺状态（排除冻结状态，除非是SIP店铺）
                    boolean isValidStatus = !SaleAccountStastusEnum.FROZEN.getCode().equals(account.getAccountStatus()) 
                        || !Boolean.TRUE.equals(account.getColBool2());
                    
                    // 如果指定了账号列表，则只处理指定账号
                    boolean isSpecifiedAccount = CollectionUtils.isEmpty(specifiedAccounts) 
                        || specifiedAccounts.contains(account.getAccountNumber());
                    
                    return isNanningWarehouse && isValidStatus && isSpecifiedAccount;
                })
                .collect(Collectors.toList());
            
            log.info("从{}个Shopee店铺中筛选出{}个南宁地区店铺", allShopeeAccounts.size(), nanningAccounts.size());
            
            return nanningAccounts;
            
        } catch (Exception e) {
            log.error("获取南宁地区Shopee店铺列表时发生异常", e);
            throw new RuntimeException("获取南宁地区Shopee店铺列表失败", e);
        }
    }

    /**
     * 处理指定店铺的禁止刊登SKU
     *
     * @param account 店铺账号信息
     * @param innerParam 任务参数
     * @return 处理的SKU数量
     */
    private int processAccountProhibitedSkus(SaleAccountAndBusinessResponse account, InnerParam innerParam) {
        log.info("开始处理店铺[{}]的禁止刊登SKU", account.getAccountNumber());

        try {
            // 使用原子计数器记录处理数量
            AtomicInteger processedCount = new AtomicInteger(0);

            // 构建查询请求
            EsShopeeItemRequest request = buildProhibitedSkuQuery(account, innerParam.getSkuList());

            // 使用滚动查询处理数据
            int totalQueryCount = shopeeItemEsService.scrollQueryExecutorTask(request, prohibitedSkus -> {
                if (CollectionUtils.isEmpty(prohibitedSkus)) {
                    return;
                }

                log.info("店铺[{}]本批次查询到{}个包含禁止刊登标签的SKU", account.getAccountNumber(), prohibitedSkus.size());

                // 处理每个禁止刊登的SKU
                for (EsShopeeItem shopeeItem : prohibitedSkus) {
                    try {
                        if (updateSkuStockToZero(account, shopeeItem, innerParam.getTestMode())) {
                            processedCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        log.error("处理店铺[{}]的SKU[{}]时发生异常", account.getAccountNumber(), shopeeItem.getArticleNumber(), e);
                        XxlJobLogger.log("处理店铺[{}]的SKU[{}]时发生异常：{}",
                            account.getAccountNumber(), shopeeItem.getArticleNumber(), e.getMessage());
                    }
                }
            });

            log.info("店铺[{}]查询到{}个包含禁止刊登标签的SKU，成功处理{}个",
                account.getAccountNumber(), totalQueryCount, processedCount.get());

            return processedCount.get();

        } catch (Exception e) {
            log.error("处理店铺[{}]的禁止刊登SKU时发生异常", account.getAccountNumber(), e);
            throw new RuntimeException("处理店铺禁止刊登SKU失败", e);
        }
    }

    /**
     * 构建查询包含禁止刊登标签的SKU的请求
     *
     * @param account 店铺账号信息
     * @param specifiedSkus 指定的SKU列表（可选）
     * @return ES查询请求对象
     */
    private EsShopeeItemRequest buildProhibitedSkuQuery(SaleAccountAndBusinessResponse account, List<String> specifiedSkus) {
        log.info("构建店铺[{}]的禁止刊登SKU查询请求", account.getAccountNumber());

        // 构建ES查询请求
        EsShopeeItemRequest request = new EsShopeeItemRequest();

        // 设置店铺账号
        request.setItemSeller(account.getAccountNumber());

        // 设置查询字段（不需要查询库存字段）
        request.setQueryFields(new String[] {
            "id", "itemId", "itemSeller", "articleNumber",
            "itemStatus", "specialGoodsCode"
        });

        // 只查询在线状态的商品
        request.setItemStatus("NORMAL");

        // 只查询商品（非父体）
        request.setIsGoods(true);

        // 设置特殊标签筛选条件：NN禁止刊登
        request.setSpecialGoodsCodeList(Arrays.asList(String.valueOf(SpecialTagEnum.s_2041.getCode())));
        request.setFromStock(1);
        // 如果指定了SKU列表，则只查询指定SKU
        if (CollectionUtils.isNotEmpty(specifiedSkus)) {
            request.setArticleNumberList(specifiedSkus);
        }

        // 设置排序和分页
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setPageSize(1000);

        log.info("构建查询请求完成，店铺：{}，特殊标签：{}", account.getAccountNumber(), SpecialTagEnum.s_2041.getCode());

        return request;
    }



    /**
     * 将SKU库存设置为0
     *
     * @param account 店铺账号信息
     * @param shopeeItem SKU商品信息
     * @param testMode 是否为测试模式
     * @return 是否处理成功
     */
    private boolean updateSkuStockToZero(SaleAccountAndBusinessResponse account, EsShopeeItem shopeeItem, Boolean testMode) {
        String sku = shopeeItem.getArticleNumber();

        log.info("开始处理包含NN禁止刊登标签的SKU[{}]，目标库存：0", sku);

        try {
            // 测试模式下不实际修改库存
            if (Boolean.TRUE.equals(testMode)) {
                log.info("测试模式：SKU[{}]库存将调整为0（未实际执行）", sku);
                XxlJobLogger.log("测试模式：SKU[{}]库存将调整为0（未实际执行）", sku);
                return true;
            }

            // 设置目标库存为0
            shopeeItem.setStock(0);

            // 调用库存更新服务
            shopeeGlobalItemService.updateShopeeStock(
                account,
                StrConstant.ADMIN,
                shopeeItem,
                FeedTaskTypeConstant.JOB,
                ShopeeFeedTaskMsgEnum.NANNING_PROHIBITED_SKU_STOCK_TO_ZERO.getCode()
            );

            log.info("SKU[{}]库存调整成功，已设置为0", sku);
            XxlJobLogger.log("SKU[{}]库存调整成功，已设置为0", sku);

            return true;

        } catch (Exception e) {
            log.error("SKU[{}]库存调整失败", sku, e);
            XxlJobLogger.log("SKU[{}]库存调整失败：{}", sku, e.getMessage());
            return false;
        }
    }
}
