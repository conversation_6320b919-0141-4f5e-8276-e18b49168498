package com.estone.erp.publish.shopee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 属性树格式类型枚举 原： INT_TYPE STRING_TYPE ENUM_TYPE FLOAT_TYPE DATE_TYPE TIMESTAMP_TYPE
 * <AUTHOR>
 * @Date 2025/6/23 17:08
 */
@Getter
@AllArgsConstructor
public enum ShopeeAttributeTreeInputValidationTypeEnum {

    VALIDATOR_NO_VALIDATE_TYPE(0, "NO_VALIDATE_TYPE"),
    VALIDATOR_INT_TYPE(1, "INT_TYPE"),
    VALIDATOR_STRING_TYPE(2, "STRING_TYPE"),
    VALIDATOR_FLOAT_TYPE(3, "FLOAT_TYPE"),
    VALIDATOR_DATE_TYPE(4, "DATE_TYPE");

    private final int code;
    private final String value;

    public static String convert(int code) {
        for (ShopeeAttributeTreeInputValidationTypeEnum type : values()) {
            if (type.code == code) {
                return type.value;
            }
        }
        return null;
    }

}
