package com.estone.erp.publish.shopee.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/6 10:27
 * @description
 */
@Getter
@Setter
public class ShopeeSpuGlobalPublishParam extends ShopeeSpuPublishParam {

    /** 子账号 */
    private String subAccount;

    /** 商家 */
    private String merchant;

    /** 商家id */
    private String merchantId;

    /** 店铺 */
    private String accounts;

    /** 站点集合 */
    private List<String> siteList;

    /** 刊登类型 */
    private String publishType;

    /**
     * 文案类型
     */
    private Integer copyWritingType;

    /**
     * 数据源
     */
    private Integer skuDataSource;

}
