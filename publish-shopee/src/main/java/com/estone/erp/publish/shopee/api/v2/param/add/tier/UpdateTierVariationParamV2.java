package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.ShopeeVariationOption;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.dto.ShopeeImageDto;
import com.estone.erp.publish.shopee.dto.ShopeeItemUpdateSkuImageDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:12
 * @description
 */
@Getter
@Setter
public class UpdateTierVariationParamV2 implements RequestCommon{

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "tier_variation")
    private List<TierVariationDtoV2> tierVariation = new ArrayList<>(2);

    /** Model info list, length should be between 1 to 50 */
    @JSONField(name = "model_list")
    private List<ModelDtoV2> model = new ArrayList<>();


    public void buildInfo(Map<String, ShopeeImageDto> shopeeImageDtoMap, List<EsShopeeItem> locaShopeeItems, List<ShopeeItemUpdateSkuImageDto> updateSkuImageDtos){

        Map<String, String> skuImageMap = updateSkuImageDtos.stream().collect(Collectors.toMap(o->o.getArticleNumber(), o->o.getSkuImage(), (k1,k2)->k1));

        Map<Integer, OptionDtoV2> colorOptionMap = new HashMap<>();
        Map<Integer, OptionDtoV2> sizeOptionMap = new HashMap<>();

        for (EsShopeeItem locaShopeeItem : locaShopeeItems) {
            String sonSku = locaShopeeItem.getArticleNumber();
            String modelId = locaShopeeItem.getVariationId();
            if(StringUtils.isBlank(modelId)) {
                continue;
            }
            List<ShopeeVariationOption> variationOptions = locaShopeeItem.getVariationOptions();
            if(CollectionUtils.isEmpty(variationOptions)) {
                throw new RuntimeException("sku" + locaShopeeItem.getArticleNumber() + "无变体属性相关信息，请同步后重试");
            }

            ShopeeImageDto shopeeImageDto = null;
            String originImage = skuImageMap.get(sonSku);
            if(StringUtils.isNotBlank(originImage)) {
                shopeeImageDto = shopeeImageDtoMap.get(originImage);
            }

            // 默认修改 未找到图片时候 不修改传原图片id
            Boolean update = true;
            if(StringUtils.isBlank(originImage) || null == shopeeImageDto) {
                update = false;
            }

            // color size 同时存在 color控制图片
            Integer variationOptionSize = variationOptions.size();

            ModelDtoV2 modelDtoV2 = new ModelDtoV2();
            this.model.add(modelDtoV2);
            modelDtoV2.setModelId(Long.valueOf(modelId));
            for (int i = 0; i < variationOptions.size(); i++) {
                ShopeeVariationOption shopeeVariationOption = variationOptions.get(i);
                Integer index = shopeeVariationOption.getIndex();
                modelDtoV2.getTierIndex().add(index);

                String name = shopeeVariationOption.getName();
                String originalImageUrl = shopeeVariationOption.getImageUrl();
                String originalImageId = StringUtils.isBlank(originalImageUrl) ? null :  StringUtils.substringAfterLast(originalImageUrl, "/");
                String imageId = update ? shopeeImageDto.getImageId() : originalImageId;
                String imageUrl = update ? shopeeImageDto.getImageUrl() : originalImageUrl;

                OptionDtoV2 optionDtoV2 = new OptionDtoV2();
                optionDtoV2.setOption(shopeeVariationOption.getOption());
                if(StringUtils.equalsIgnoreCase(name, "color")) {
                    colorOptionMap.put(index, optionDtoV2);
                    Map<String, String> image = new HashMap<>();
                    image.put("image_id", imageId);
                    optionDtoV2.setImage(image);
                    shopeeVariationOption.setImageUrl(imageUrl);
                }
                if(StringUtils.equalsIgnoreCase(name, "size")) {
                    sizeOptionMap.put(index, optionDtoV2);
                    if(2 != variationOptionSize) { // 等于2 代表color size 都有 图片设置再color 下面
                        Map<String, String> image = new HashMap<>();
                        image.put("image_id", imageId);
                        optionDtoV2.setImage(image);
                        shopeeVariationOption.setImageUrl(imageUrl);
                    }
                }
            }
        }

        if(MapUtils.isNotEmpty(colorOptionMap)) {
            TierVariationDtoV2 tierVariationDtoV2 = new TierVariationDtoV2();
            tierVariationDtoV2.setName("color");
            for (int i = 0; i < colorOptionMap.size(); i++) {
                OptionDtoV2 optionDtoV2 = colorOptionMap.get(i);
                tierVariationDtoV2.getOptionList().add(optionDtoV2);
            }
            this.tierVariation.add(tierVariationDtoV2);
        }
        if(MapUtils.isNotEmpty(sizeOptionMap)) {
            TierVariationDtoV2 tierVariationDtoV2 = new TierVariationDtoV2();
            tierVariationDtoV2.setName("size");
            for (int i = 0; i < sizeOptionMap.size(); i++) {
                OptionDtoV2 optionDtoV2 = sizeOptionMap.get(i);
                tierVariationDtoV2.getOptionList().add(optionDtoV2);
            }
            this.tierVariation.add(tierVariationDtoV2);
        }
    }

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_TIER_VARIATION;
    }
}
