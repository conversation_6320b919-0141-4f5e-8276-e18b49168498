package com.estone.erp.publish.shopee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/6/10 16:36
 */
@Data
public class ShopeeSizeChartTemplatesDO {


    /**
     * idList
     */
    private List<Long> idList;

    /**
     * 模板ID
     */
    private String sizeChartId;

    /**
     * 尺码表名称
     */
    private String sizeChartName;

    /**
     * 店铺
     */
    private List<String> accountNumberList;

    /**
     * 商家name
     */
    private String merchantName;

    /**
     * 商家name
     */
    private Set<String> merchantNameList;

    /**
     * 类目id集合
     */
    private List<Integer> categoryIdList;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTimeForm;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTimeTo;

}
