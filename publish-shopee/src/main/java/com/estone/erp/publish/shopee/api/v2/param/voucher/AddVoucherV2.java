package com.estone.erp.publish.shopee.api.v2.param.voucher;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

import java.util.List;

/**
 * 添加折扣
 */
@Data
public class AddVoucherV2 implements RequestCommon {

    /**
     * 优惠券名称
     */
    @JSONField(name = "voucher_name")
    private String voucherName;

    /**
     * 优惠券码
     */
    @JSONField(name = "voucher_code")
    private String voucherCode;

    /**
     * 开始时间
     */
    @JSONField(name = "start_time")
    private Long startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "end_time")
    private Long endTime;

    /**
     * 凭证的类型。可用值为：1：商店凭证，2：产品凭证。
     */
    @JSONField(name = "voucher_type")
    private Integer voucherType = 1;

    /**
     * 凭证的奖励类型。可用值为：1：fix_amount 凭证，2：discount_percentage 凭证，3：coin_cashback 凭证。
     */
    @JSONField(name = "reward_type")
    private Integer rewardType;

    /**
     * 可使用总数
     */
    @JSONField(name = "usage_quantity")
    private Integer usageQuantity;

    /**
     * 最低消费限额
     */
    @JSONField(name ="min_basket_price")
    private Double minBasketPrice;

    /**
     * 优惠金额，为此特定优惠券设置的折扣金额。仅在创建固定金额凭证时填写。
     */
    @JSONField(name = "discount_amount")
    private Double discountAmount;

    /**
     * 优惠百分比
     */
    @JSONField(name = "percentage")
    private Integer percentage;

    /**
     * 百分比最大优惠值
     */
    @JSONField(name = "max_price")
    private Double maxPrice;

    /**
     * 显示页面
     */
    @JSONField(name = "display_channel_list")
    private List<Integer> displayChannelList;

    /**
     * 显示时间
     */
    @JSONField(name = "display_start_time")
    private Long displayStartTime;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_VOUCHER;
    }
}
