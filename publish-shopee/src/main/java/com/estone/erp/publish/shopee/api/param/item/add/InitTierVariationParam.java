package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.shopee.bo.ShippingFeePrice;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.model.ShopeeTemplate;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.shopee.util.AliOSSUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 上传产品的子属性
 */
public class InitTierVariationParam implements IRequestUrlApiKey {

    @JSONField(name = "item_id")
    private Long itemId;

    //shopee目前只允许最多2个维度
    @JSONField(name = "tier_variation")
    private List<ShopeeTierVariationParam> tierVariationList = new ArrayList<>(2);

    @JSONField(name = "variation")
    private List<ShopeeMultiVariationParam> variationList = new ArrayList<>();

    @JSONField(name = "partner_id")
    private Integer partnerId;

    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(serialize = false)
    private String apiKey;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    public InitTierVariationParam(){}

    public InitTierVariationParam(SaleAccountAndBusinessResponse account, ShopeeTemplate shopeeTemplate, Long itemId){
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.parseInt(account.getMarketplaceId());
        this.apiKey = account.getClientId();
        this.itemId = itemId;
        //String accountSiteCode = ShopeeCommonUtils.getAccountSiteCode(account);
        //现在shopee账号会直接返回账号的站点了，不会自己去切割
        String accountSiteCode = account.getAccountSite();
        //List<ShopeeSku> shopeeSkuList = shopeeTemplate.getShopeeSkus();
        List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(shopeeTemplate.getShopeeSkusStr(), ShopeeSku.class);
        List<ShippingFeePrice> shippingFeePriceList = JSON.parseArray(shopeeTemplate.getShippingFeePricesStr(),ShippingFeePrice.class);
        Set<String> colorSet = new LinkedHashSet<>();
        Set<String> sizeSet = new LinkedHashSet<>();
        List<String> imageList = new ArrayList<>();
        //维度和对应的图片url
        Map<String,String> tierImageMap = new HashMap<>();
       //设置颜色和尺寸的维度,台湾要使用中文颜色和中文尺寸
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            if(StringUtils.isNotEmpty(shopeeSku.getColor())){
                //很可能没有中文颜色，加异常处理不要影响到别的流程
                String chineseColor = "";
                try {
                    chineseColor = shopeeSku.getChineseColor().trim();
                } catch (Exception e) {
                    chineseColor = shopeeSku.getColor().trim();
                    shopeeSku.setChineseColor(chineseColor);
                }
                String color = shopeeSku.getColor().trim();
                colorSet.add("TW".equals(accountSiteCode)?chineseColor:color);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
                    tierImageMap.put(chineseColor,shopeeSku.getImage());
                    tierImageMap.put(color,shopeeSku.getImage());
                }
            }
            if(StringUtils.isNotEmpty(shopeeSku.getSize())){
                //很可能没有中文尺寸，加异常处理不要影响到别的流程
                String chineseSize = "";
                try {
                    chineseSize = shopeeSku.getChineseSize().trim();
                } catch (Exception e) {
                    chineseSize = shopeeSku.getSize().trim();
                    shopeeSku.setChineseSize(chineseSize);
                }
                String size = shopeeSku.getSize().trim();
                sizeSet.add("TW".equals(accountSiteCode)?chineseSize:size);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
                    tierImageMap.put(chineseSize,shopeeSku.getImage());
                    tierImageMap.put(size,shopeeSku.getImage());
                }
            }
            if(StringUtils.isNotEmpty(shopeeSku.getImage())) {
                imageList.add(shopeeSku.getImage().trim());
            }
        }
        //把图片上传到阿里云
        if(CollectionUtils.isNotEmpty(imageList)) {
            AliOSSUtils.uploadImageToAliOss(imageList);
        }
        //AliOSSUtils.transSystemImageToAliOssImage(imageList);
        //变体图片只能对一个维度的属性生效，比如只能设置color的维度或者size的维度
        boolean isImageParamHasValue = false;
        //color维度设置
        List<String> colorOptionList = null;
        if (CollectionUtils.isNotEmpty(colorSet)) {
            ShopeeTierVariationParam colorParam = new ShopeeTierVariationParam();
            colorParam.setName("TW".equals(accountSiteCode)?"顏色":"color");
            colorOptionList = new ArrayList<>(colorSet);
            colorParam.setOptions(colorOptionList);
            if(!isImageParamHasValue && CollectionUtils.isNotEmpty(imageList)) {
                //colorParam.setImagesUrlList(imageList.subList(0,colorOptionList.size()));
                List<String> tierImageList = new ArrayList<>();
                for (String colorOption : colorOptionList) {
                    tierImageList.add(tierImageMap.get(colorOption));
                    AliOSSUtils.transSystemImageToAliOssImage(tierImageList);
                    colorParam.setImagesUrlList(tierImageList);
                }
                isImageParamHasValue = true;
            } else {
                //这句看上去是无用代码，实际上是必要的，不要删掉
                colorParam.setImagesUrlList(null);
            }
            //colorParam.setImagesUrlList(null);
            tierVariationList.add(colorParam);
        }
        //size维度设置
        List<String> sizeOptionList = null;
        if (CollectionUtils.isNotEmpty(sizeSet)) {
            ShopeeTierVariationParam sizeParam = new ShopeeTierVariationParam();
            sizeParam.setName("TW".equals(accountSiteCode)?"尺寸":"size");
            sizeOptionList = new ArrayList<>(sizeSet);
            sizeParam.setOptions(sizeOptionList);
            if(!isImageParamHasValue && CollectionUtils.isNotEmpty(imageList)) {
                //sizeParam.setImagesUrlList(imageList.subList(0,sizeOptionList.size()));
                List<String> tierImageList = new ArrayList<>();
                for (String sizeOption : sizeOptionList) {
                    tierImageList.add(tierImageMap.get(sizeOption));
                    AliOSSUtils.transSystemImageToAliOssImage(tierImageList);
                    sizeParam.setImagesUrlList(tierImageList);
                }
                isImageParamHasValue = true;
            } else {
                //这句看上去是无用代码，实际上是必要的，不要删掉
                sizeParam.setImagesUrlList(null);
            }
            //sizeParam.setImagesUrlList(null);
            tierVariationList.add(sizeParam);
        }
        //this.setTierVariationList(tierVariationParamList);
        //设置产品的规格
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            ShopeeMultiVariationParam param = new ShopeeMultiVariationParam();
            //设置库存
            param.setStock(shopeeSku.getQuantity());
            //设置价格
            for (ShippingFeePrice shippingFeePrice : shippingFeePriceList) {
                if(StringUtils.equalsIgnoreCase(shippingFeePrice.getSite(), accountSiteCode) && StringUtils.equalsIgnoreCase(shippingFeePrice.getSku(), shopeeSku.getSku())){
                    param.setPrice(shippingFeePrice.getPrice());
                    break;
                }
            }
            //设置sku
            param.setVariationSku(shopeeSku.getSku());
            //设置属性维度
            int colorIndex = -1;
            if (CollectionUtils.isNotEmpty(colorOptionList)) {
                String color = "TW".equals(accountSiteCode)?shopeeSku.getChineseColor():shopeeSku.getColor();
                color = color.trim();
                colorIndex = 0;
                for (int i = 0; i < colorOptionList.size(); i++) {
                    if(color.equals(colorOptionList.get(i))){
                        colorIndex = i;
                        break;
                    }
                }
            }
            int sizeIndex = -1;
            if (CollectionUtils.isNotEmpty(sizeOptionList)) {
                String size = "TW".equals(accountSiteCode)?shopeeSku.getChineseSize():shopeeSku.getSize();
                size = size.trim();
                sizeIndex = 0;
                for (int i = 0; i < sizeOptionList.size(); i++) {
                    if(size.equals(sizeOptionList.get(i))){
                        sizeIndex = i;
                        break;
                    }
                }
            }
            //设置属性维度索引，color在前，size在后
            List<Integer> indexList = new ArrayList<>();
            if (colorIndex != -1) {
                indexList.add(colorIndex);
            }
            if (sizeIndex != -1) {
                indexList.add(sizeIndex);
            }
            param.setTierIndex(indexList);
            variationList.add(param);
        }
    }

    public InitTierVariationParam(SaleAccountAndBusinessResponse account, ShopeeTemplateNew shopeeTemplate, Long itemId, Map<String, String> imgMappingMap){
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.parseInt(account.getMarketplaceId());
        this.apiKey = account.getClientId();
        this.itemId = itemId;
        String accountSiteCode = account.getAccountSite();
        List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(shopeeTemplate.getShopeeSkusStr(), ShopeeSku.class);
        Set<String> colorSet = new LinkedHashSet<>();
        Set<String> sizeSet = new LinkedHashSet<>();
        List<String> imageList = new ArrayList<>();
        //维度和对应的图片url
        Map<String,String> tierImageMap = new HashMap<>();
        //设置颜色和尺寸的维度,台湾要使用中文颜色和中文尺寸
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            if(StringUtils.isNotEmpty(shopeeSku.getColor())){
                //很可能没有中文颜色，加异常处理不要影响到别的流程
//                String chineseColor = "";
//                try {
//                    chineseColor = shopeeSku.getChineseColor().trim();
//                } catch (Exception e) {
//                    chineseColor = shopeeSku.getColor().trim();
//                    shopeeSku.setChineseColor(chineseColor);
//                }
                String color = shopeeSku.getColor().trim();
//                colorSet.add("TW".equals(accountSiteCode)?chineseColor:color);
                colorSet.add(color);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
//                    tierImageMap.put(chineseColor,shopeeSku.getImage());
                    tierImageMap.put(color,shopeeSku.getImage());
                }
            }
            if(StringUtils.isNotEmpty(shopeeSku.getSize())){
                //很可能没有中文尺寸，加异常处理不要影响到别的流程
//                String chineseSize = "";
//                try {
//                    chineseSize = shopeeSku.getChineseSize().trim();
//                } catch (Exception e) {
//                    chineseSize = shopeeSku.getSize().trim();
//                    shopeeSku.setChineseSize(chineseSize);
//                }
                String size = shopeeSku.getSize().trim();
//                sizeSet.add("TW".equals(accountSiteCode)?chineseSize:size);
                sizeSet.add(size);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
//                    tierImageMap.put(chineseSize,shopeeSku.getImage());
                    tierImageMap.put(size,shopeeSku.getImage());
                }
            }
            if(StringUtils.isNotEmpty(shopeeSku.getImage())) {
                imageList.add(shopeeSku.getImage().trim());
            }
        }
        //把图片上传到阿里云
        if(CollectionUtils.isNotEmpty(imageList)) {
//            AliOSSUtils.uploadImageToAliOss(imageList);
        }
        //AliOSSUtils.transSystemImageToAliOssImage(imageList);
        //变体图片只能对一个维度的属性生效，比如只能设置color的维度或者size的维度
        boolean isImageParamHasValue = false;
        //color维度设置
        List<String> colorOptionList = null;
        if (CollectionUtils.isNotEmpty(colorSet)) {
            ShopeeTierVariationParam colorParam = new ShopeeTierVariationParam();
            colorParam.setName("TW".equals(accountSiteCode)?"顏色":"color");
            colorOptionList = new ArrayList<>(colorSet);
            colorParam.setOptions(colorOptionList);
            // todo YCF 因为shopee图片池的问题，子sku上传图片的功能暂时关闭
            if(!isImageParamHasValue && CollectionUtils.isNotEmpty(imageList)) {
                //colorParam.setImagesUrlList(imageList.subList(0,colorOptionList.size()));
                List<String> tierImageList = new ArrayList<>();
                for (String colorOption : colorOptionList) {
                    String url = tierImageMap.get(colorOption);
                    String img = imgMappingMap.get(url);
                    tierImageList.add(img);
//                    AliOSSUtils.transSystemImageToAliOssImage(tierImageList);
                    colorParam.setImagesUrlList(tierImageList);
                }
                isImageParamHasValue = true;
            } else {
                //这句看上去是无用代码，实际上是必要的，不要删掉
                colorParam.setImagesUrlList(null);
            }
//            colorParam.setImagesUrlList(null);
            tierVariationList.add(colorParam);
        }
        //size维度设置
        List<String> sizeOptionList = null;
        if (CollectionUtils.isNotEmpty(sizeSet)) {
            ShopeeTierVariationParam sizeParam = new ShopeeTierVariationParam();
            sizeParam.setName("TW".equals(accountSiteCode)?"尺寸":"size");
            sizeOptionList = new ArrayList<>(sizeSet);
            sizeParam.setOptions(sizeOptionList);
            // todo YCF 因为shopee图片池的问题，子sku上传图片的功能暂时关闭
            if(!isImageParamHasValue && CollectionUtils.isNotEmpty(imageList)) {
                //sizeParam.setImagesUrlList(imageList.subList(0,sizeOptionList.size()));
                List<String> tierImageList = new ArrayList<>();
                for (String sizeOption : sizeOptionList) {
                    String url = tierImageMap.get(sizeOption);
                    String img = imgMappingMap.get(url);
                    tierImageList.add(img);
//                    AliOSSUtils.transSystemImageToAliOssImage(tierImageList);
                    sizeParam.setImagesUrlList(tierImageList);
                }
                isImageParamHasValue = true;
            } else {
                //这句看上去是无用代码，实际上是必要的，不要删掉
                sizeParam.setImagesUrlList(null);
            }
//            sizeParam.setImagesUrlList(null);
            tierVariationList.add(sizeParam);
        }
        //this.setTierVariationList(tierVariationParamList);
        //设置产品的规格
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            ShopeeMultiVariationParam param = new ShopeeMultiVariationParam();
            //设置库存
            param.setStock(shopeeSku.getQuantity());
            //设置价格
            param.setPrice(shopeeSku.getPrice());
            //设置sku
            param.setVariationSku(shopeeSku.getSku());
            //设置属性维度
            int colorIndex = -1;
            if (CollectionUtils.isNotEmpty(colorOptionList)) {
//                String color = "TW".equals(accountSiteCode)?shopeeSku.getChineseColor():shopeeSku.getColor();
                String color = shopeeSku.getColor();
                color = color.trim();
                colorIndex = 0;
                for (int i = 0; i < colorOptionList.size(); i++) {
                    if(color.equals(colorOptionList.get(i))){
                        colorIndex = i;
                        break;
                    }
                }
            }
            int sizeIndex = -1;
            if (CollectionUtils.isNotEmpty(sizeOptionList)) {
//                String size = "TW".equals(accountSiteCode)?shopeeSku.getChineseSize():shopeeSku.getSize();
                String size = shopeeSku.getSize();
                size = size.trim();
                sizeIndex = 0;
                for (int i = 0; i < sizeOptionList.size(); i++) {
                    if(size.equals(sizeOptionList.get(i))){
                        sizeIndex = i;
                        break;
                    }
                }
            }
            //设置属性维度索引，color在前，size在后
            List<Integer> indexList = new ArrayList<>();
            if (colorIndex != -1) {
                indexList.add(colorIndex);
            }
            if (sizeIndex != -1) {
                indexList.add(sizeIndex);
            }
            param.setTierIndex(indexList);
            variationList.add(param);
        }
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.TIER_VAR_INIT;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public List<ShopeeTierVariationParam> getTierVariationList() {
        return tierVariationList;
    }

    public void setTierVariationList(List<ShopeeTierVariationParam> tierVariationList) {
        this.tierVariationList = tierVariationList;
    }

    public List<ShopeeMultiVariationParam> getVariationList() {
        return variationList;
    }

    public void setVariationList(List<ShopeeMultiVariationParam> variationList) {
        this.variationList = variationList;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}
