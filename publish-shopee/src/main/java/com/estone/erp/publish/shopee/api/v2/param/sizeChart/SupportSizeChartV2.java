package com.estone.erp.publish.shopee.api.v2.param.sizeChart;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/15 下午3:16
 */
@Data
public class SupportSizeChartV2 implements RequestCommon {

    @JSONField(name = "category_id")
    private Integer categoryId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.SUPPORT_SIZE_CHART;
    }
}
