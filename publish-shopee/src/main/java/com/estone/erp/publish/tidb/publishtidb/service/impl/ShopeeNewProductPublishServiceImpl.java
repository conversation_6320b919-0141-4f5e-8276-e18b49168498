package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductPublishDo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductPublishVo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewPublishLeaderInfo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewPublishSiteInfo;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductPublishMapper;
import com.estone.erp.publish.tidb.publishtidb.model.*;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductPublishLeaderService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductPublishService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductPublishSiteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Shopee 刊登次数达成率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class ShopeeNewProductPublishServiceImpl extends ServiceImpl<ShopeeNewProductPublishMapper, ShopeeNewProductPublish> implements ShopeeNewProductPublishService {

    @Resource
    private ShopeeNewProductPublishLeaderService shopeeNewProductPublishLeaderService;
    @Resource
    private ShopeeNewProductPublishSiteService shopeeNewProductPublishSiteService;
    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @Override
    public void saveEmptyPublish(List<ShopeeNewProductRecommendation> spuList, List<ShopeeNewProductSites> sites, Set<String> siteCodeSet, Set<String> saleLeaderSet) {
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isEmpty(spuList)) {
            return;
        }
        Map<String, Long> recommendationSpuAndIdMap = spuList.stream().collect(Collectors.toMap(ShopeeNewProductRecommendation::getSpu, ShopeeNewProductRecommendation::getId, (a, b) -> a));
        Map<Long, List<ShopeeNewProductSites>> bannedMap = sites.stream().collect(Collectors.groupingBy(ShopeeNewProductSites::getProductId, Collectors.toList()));

        List<ShopeeNewProductPublish> collect = spuList.stream().map(a -> {
            String spu = a.getSpu();
            LocalDateTime enterProductTime = a.getEnterProductTime();
            LocalDateTime pushTime = a.getPushTime();
            ShopeeNewProductPublish shopeeNewProductPublish = new ShopeeNewProductPublish();
            shopeeNewProductPublish.setSpu(spu);
            shopeeNewProductPublish.setPushTime(pushTime);
            shopeeNewProductPublish.setItemStatus(a.getItemStatus());
            shopeeNewProductPublish.setEnterProductTime(enterProductTime);
            shopeeNewProductPublish.setOneListingNumber(0);
            shopeeNewProductPublish.setOneListingNumberPercent(0D);
            shopeeNewProductPublish.setTwoListingNumber(0);
            shopeeNewProductPublish.setCreatedTime(now);
            shopeeNewProductPublish.setUpdatedTime(now);
            return shopeeNewProductPublish;
        }).collect(Collectors.toList());
        saveBatch(collect, 300);

        List<ShopeeNewProductPublishSite> shopeeNewProductPublishSiteList = new ArrayList<>();
        List<ShopeeNewProductPublishLeader> shopeeNewProductPublishLeaderList = new ArrayList<>();
        for (ShopeeNewProductPublish shopeeNewProductPublish : collect) {
            Long id = shopeeNewProductPublish.getId();
            String spu = shopeeNewProductPublish.getSpu();
            Long recommendationId = recommendationSpuAndIdMap.get(spu);
            List<ShopeeNewProductSites> shopeeNewProductSites = bannedMap.get(recommendationId);
            Map<String, Boolean> siteAndBannedMap = shopeeNewProductSites.stream().collect(Collectors.toMap(ShopeeNewProductSites::getSiteCode, ShopeeNewProductSites::getIsBanned));
            for (String siteCode : siteCodeSet) {
                ShopeeNewProductPublishSite shopeeNewProductPublishSite = new ShopeeNewProductPublishSite();
                shopeeNewProductPublishSite.setPublishId(id);
                shopeeNewProductPublishSite.setSiteCode(siteCode);
                shopeeNewProductPublishSite.setOneListingNumber(0);
                shopeeNewProductPublishSite.setOneListingNumberPercent(0D);
                shopeeNewProductPublishSite.setTwoListingNumber(0);
                shopeeNewProductPublishSite.setIsBanned(siteAndBannedMap.getOrDefault(siteCode, false));
                shopeeNewProductPublishSite.setCreatedTime(now);
                shopeeNewProductPublishSite.setUpdatedTime(now);
                shopeeNewProductPublishSiteList.add(shopeeNewProductPublishSite);
            }
            for (String saleLeader : saleLeaderSet) {
                ShopeeNewProductPublishLeader shopeeNewProductPublishLeader = new ShopeeNewProductPublishLeader();
                shopeeNewProductPublishLeader.setPublishId(id);
                shopeeNewProductPublishLeader.setSaleLeader(saleLeader);
                shopeeNewProductPublishLeader.setCreatedTime(now);
                shopeeNewProductPublishLeader.setUpdatedTime(now);
                shopeeNewProductPublishLeader.setPercent(0D);
                shopeeNewProductPublishLeader.setBrPercent(0D);
                shopeeNewProductPublishLeader.setClPercent(0D);
                shopeeNewProductPublishLeader.setCoPercent(0D);
                shopeeNewProductPublishLeader.setMxPercent(0D);
                shopeeNewProductPublishLeader.setPhPercent(0D);
                shopeeNewProductPublishLeader.setSgPercent(0D);
                shopeeNewProductPublishLeader.setThPercent(0D);
                shopeeNewProductPublishLeader.setTwPercent(0D);
                shopeeNewProductPublishLeader.setVnPercent(0D);
                shopeeNewProductPublishLeader.setMyPercent(0D);
                shopeeNewProductPublishLeader.setJson("");
                shopeeNewProductPublishLeaderList.add(shopeeNewProductPublishLeader);
            }
        }
        shopeeNewProductPublishSiteService.saveBatch(shopeeNewProductPublishSiteList, 300);
        shopeeNewProductPublishLeaderService.saveBatch(shopeeNewProductPublishLeaderList, 300);
    }

    @Override
    public void updateSitesPublishCompleteTime(String spu, LocalDateTime sitesPublishCompleteTime) {
        this.baseMapper.updateSitesPublishCompleteTime(spu, sitesPublishCompleteTime);
    }

    @Override
    public CQueryResult<ShopeeNewProductPublishVo> queryPage(CQuery<ShopeeNewProductPublishDo> cQuery) {
        ShopeeNewProductPublishDo search = cQuery.getSearch();
        // 如果是管理员就查询全部，如果是销售组长就查询销售组长自己
        isAuth(search);
        Page<ShopeeNewProductPublishVo> page = new Page<>(cQuery.getPage(), cQuery.getLimit());
        // 首先，查询主表
        IPage<ShopeeNewProductPublishVo> pageResult = this.page(page, search);

        CQueryResult<ShopeeNewProductPublishVo> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }

    private void isAuth(ShopeeNewProductPublishDo search) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        boolean isAdmin = BooleanUtils.isTrue(superAdminOrEquivalent.getResult());
        search.setIsAdmin(isAdmin);
        if (isAdmin) {
            return;
        }
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.tokenUser();
        if (!newUserApiResult.isSuccess()) {
            throw new RuntimeException(newUserApiResult.getErrorMsg());
        }
        // 是否为组长
        NewUser result = newUserApiResult.getResult();
        if (result.getRsRoleNames().contains(RoleConstant.GROUP_LEADER)) {
            search.setIsLeader(true);
        } else {
            search.setIsLeader(false);
        }
        search.setEmployeeNo(result.getEmployeeNo());
    }

    @Override
    public IPage<ShopeeNewProductPublishVo> page(Page<ShopeeNewProductPublishVo> page, ShopeeNewProductPublishDo search) {
        IPage<ShopeeNewProductPublishVo> pageResult = this.baseMapper.queryPage(page, search);
        List<ShopeeNewProductPublishVo> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> mainIdList = records.stream().map(ShopeeNewProductPublishVo::getId).collect(Collectors.toList());
            // 然后，查询leader表
            LambdaQueryWrapper<ShopeeNewProductPublishLeader> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ShopeeNewProductPublishLeader::getPublishId, mainIdList);
            if (!search.getIsAdmin()) {
                queryWrapper.eq(ShopeeNewProductPublishLeader::getSaleLeader, search.getEmployeeNo());
            }
            List<ShopeeNewProductPublishLeader> shopeeNewProductPublishLeaderList = shopeeNewProductPublishLeaderService.list(queryWrapper);
            Map<Long, List<ShopeeNewProductPublishLeader>> collect =
                    shopeeNewProductPublishLeaderList.stream().collect(Collectors.groupingBy(ShopeeNewProductPublishLeader::getPublishId));
            for (ShopeeNewProductPublishVo record : records) {
                String siteList = record.getSiteList();
                List<ShopeeNewPublishSiteInfo> shopeeNewPublishSiteInfos = JSON.parseArray(siteList, ShopeeNewPublishSiteInfo.class);
                record.setSiteInfos(shopeeNewPublishSiteInfos);
                List<ShopeeNewProductPublishLeader> shopeeNewProductPublishLeader = collect.get(record.getId());
                if (CollectionUtils.isNotEmpty(shopeeNewProductPublishLeader)) {
                    List<ShopeeNewPublishLeaderInfo> shopeeNewPublishLeaderInfos = BeanUtil.copyBeanList(shopeeNewProductPublishLeader, ShopeeNewPublishLeaderInfo.class);
                    Map<String, String> userMap = new HashMap<>();
                    for (ShopeeNewPublishLeaderInfo shopeeNewPublishLeaderInfo : shopeeNewPublishLeaderInfos) {
                        String name = userMap.get(shopeeNewPublishLeaderInfo.getSaleLeader());
                        if (name != null) {
                            shopeeNewPublishLeaderInfo.setSaleName(name);
                            continue;
                        }
                        ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(shopeeNewPublishLeaderInfo.getSaleLeader());
                        if (userByNo.isSuccess()) {
                            NewUser result = userByNo.getResult();
                            userMap.put(shopeeNewPublishLeaderInfo.getSaleLeader(), result.getName());
                            shopeeNewPublishLeaderInfo.setSaleName(result.getName());
                        }
                    }
                    record.setLeaderInfos(shopeeNewPublishLeaderInfos);
                }
            }
        }
        return page;
    }

    @Override
    public ApiResult<String> download(CQuery<ShopeeNewProductPublishDo> query) {
        try {
            ShopeeNewProductPublishDo search = query.getSearch();
            isAuth(search);

            int count = this.baseMapper.count(search);
            if (count == 0) {
                return ApiResult.newError("没有数据，无法下载");
            }
            if (count > 500000) {
                return ApiResult.newError("超出最大下载数量限制，无法下载");
            }

            // 构造导出日志
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            downloadLog.setType(ShopeeDownloadTypeEnums.NEW_PRODUCT_PUBLISH.getType());
            downloadLog.setQueryCondition(JSON.toJSONString(search));
            downloadLog.setDownloadCount(count);
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            // 发送队列
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<ShopeeNewProductPublish> wrapper) {
        List<Map<Object, Object>> tidbPageMetaMap = baseMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }
}
