package com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.SellerStock;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/26 9:58
 * @description
 */
@Getter
@Setter
public class UpdateStockPriceDetailMtsku {

    /** 如果是变体必填*/
    @JSONField(name = "global_model_id")
    private Long globalModelId;

    /** 库存 */
//    @JSONField(name = "normal_stock")
//    private Integer normalStock;

    /** 库存 */
    @JSONField(name = "seller_stock")
    private List<SellerStock> sellerStocks;

//    /** 价格 */
//    @JSONField(name = "original_price")
//    private Double originalPrice;

}
