package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 秒杀报名商品队列
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeRetryMarketingShortVideoMqConfig {

    private int shopeeRetryMarketingShortVideoMqConsumers;
    private int shopeeRetryMarketingShortVideoMqPrefetchCount;
    private boolean shopeeRetryMarketingShortVideoMqListener;

    @Bean
    public VhQueue shopeeRetryMarketingShortVideoQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_RETRY_MARKETING_SHORT_VIDEO_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding shopeeRetryMarketingShortVideoQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_RETRY_MARKETING_SHORT_VIDEO_QUEUE, VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_RETRY_MARKETING_SHORT_VIDEO_KEY, null);
    }

    @Bean
    public ShopeeRetryMarketingShortVideoMqListener shopeeRetryMarketingShortVideoMqListener() {
        return new ShopeeRetryMarketingShortVideoMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeRetryMarketingShortVideoListenerContainer(
            ShopeeRetryMarketingShortVideoMqListener shopeeRetryMarketingShortVideoMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_RETRY_MARKETING_SHORT_VIDEO_QUEUE, shopeeRetryMarketingShortVideoMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeRetryMarketingShortVideoMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeRetryMarketingShortVideoMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeRetryMarketingShortVideoMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
