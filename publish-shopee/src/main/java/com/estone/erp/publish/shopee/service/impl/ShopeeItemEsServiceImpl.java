package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.model.dto.ResultListDTO;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeGlobalTemplateRequest;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud.UpdateGlobalItem;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.add.DescriptionInfoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.UpdateTierVariationParamV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.cud.UpdateItemV2;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.call.v2.*;
import com.estone.erp.publish.shopee.call.v2.cnsc.ShopeeUpdateGlobalItemCall;
import com.estone.erp.publish.shopee.component.ShopeeItemEsBulkProcessor;
import com.estone.erp.publish.shopee.component.ShopeeListingV2Component;
import com.estone.erp.publish.shopee.constant.ShopeeRedisConstant;
import com.estone.erp.publish.shopee.dto.*;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeOperateLog;
import com.estone.erp.publish.shopee.model.ShopeePublishSku;
import com.estone.erp.publish.shopee.model.ShopeePublishSkuExample;
import com.estone.erp.publish.shopee.result.ShopeeCustomResult;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.*;
import com.estone.erp.publish.system.Logistics.LogisticsClientHelper;
import com.estone.erp.publish.system.Logistics.response.CalcShippingCostParam;
import com.estone.erp.publish.system.Logistics.response.CalcShippingCostResult;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.AaleTransactionCommissionFormula;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Shopee在线列表（es）业务层
 * @Auther yucm
 * @Date 2022/3/25
 */
@Slf4j
@Service("shopeeItemEsService")
public class ShopeeItemEsServiceImpl implements ShopeeItemEsService {

    @Resource
    private EsShopeeItemService esShopeeItemService;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private ShopeeDiscountService shopeeDiscountService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;
    @Resource
    private ShopeeListingV2Component shopeeListingV2Component;
    @Resource
    private ShopeePublishSkuService shopeePublishSkuService;
    @Resource
    private ShopeeItemOfflineEsService shopeeItemOfflineEsService;
    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private InfringementWordService infringementWordService;
    @Resource
    private ShopeeItemEsBulkProcessor shopeeItemEsBulkProcessor;
    @Resource
    private ShopeeCategoryV2Service shopeeCategoryV2Service;

    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    @Resource
    private ShopeeGlobalTemplateEsService shopeeGlobalTemplateEsService;

    @Resource
    private EsSkuBindService esSkuBindService;

    @Resource
    private ShopeeOperateLogService shopeeOperateLogService;

    @Override
    public EsShopeeItemResponse search(CQuery<EsShopeeItemRequest> cquery) throws Exception {
        Assert.notNull(cquery, "cquery is null!");
        EsShopeeItemRequest query = cquery.getSearch();

        EsShopeeItemResponse response = new EsShopeeItemResponse();
        // 折扣页面需去除折扣中的
        if (BooleanUtils.isTrue(query.getAddDiscountPage()) && BooleanUtils.isFalse(query.getHasDiscount()) && CollectionUtils.isNotEmpty(query.getItemSellerList())) {
            //查询无折扣产品，还要过滤掉折扣列表的itemId
            List<String> discountIngItemList = new ArrayList<>();
            for(String itemSeller : query.getItemSellerList()){
                List<String> list = shopeeDiscountService.selectUseItemIdsByAccount(itemSeller);
                discountIngItemList.addAll(list);
            }
            query.setNotItemIdList(discountIngItemList);
            // 避免干扰了
            query.setHasDiscount(null);
        }

        Page<EsShopeeItem> results = esShopeeItemService.page(query, cquery.getLimit(), cquery.getOffset());

        // 计算毛利率
        Map<String, ShopeeItemEsExtend> esExtendMap = new HashMap<>();
        if(BooleanUtils.isTrue(query.getIsCalcProfitRate())) {
            //获取各个站点的物流方式
            List<EsShopeeItem> esShopeeItems = results.getContent();
            esExtendMap = ShopeeCalculatedPriceUtil.calcItemProfit(esShopeeItems);
        }
        if (CollectionUtils.isNotEmpty(results.getContent())) {
            for (EsShopeeItem esShopeeItem : results.getContent()) {
                Integer nnAvableStock = SkuStockUtils.getNnAvableStock(esShopeeItem.getArticleNumber());
                esShopeeItem.setNnStock(nnAvableStock);

                Integer avableStock = SkuStockUtils.getAvableStock(esShopeeItem.getArticleNumber());
                esShopeeItem.setSzStock(avableStock);
            }
        }

        esExtendMap = handelPageExtend(results.getContent(), esExtendMap, query);
        response.setExtendMap(esExtendMap);
        response.setEsShopeeItemPage(results);
        return response;
    }

    @Override
    public Page<EsShopeeItem> page(EsShopeeItemRequest request, int pageSize, Integer offset) {
        return esShopeeItemService.page(request, pageSize, offset);
    }

    @Override
    public List<EsShopeeItem> getEsShopeeItems(EsShopeeItemRequest esShopeeItemRequest){
        return esShopeeItemService.getEsShopeeItems(esShopeeItemRequest);
    }

    @Override
    public List<EsShopeeItem> getEsShopeeItemsByItemId(String itemId, String[] queryFields){
        if(StringUtils.isBlank(itemId)) {
            return new ArrayList<>();
        }
        EsShopeeItemRequest esShopeeItemRequest = new EsShopeeItemRequest();
        esShopeeItemRequest.setItemId(itemId);
        esShopeeItemRequest.setQueryFields(queryFields);
        return this.getEsShopeeItems(esShopeeItemRequest);
    }

    @Override
    public EsShopeeItem findAllById(String id) {
        if(StringUtils.isBlank(id)) {
            return null;
        }
        return esShopeeItemService.findAllById(id);
    }

    @Override
    public List<String> getIdsByItemId(String itemId) {
        if(StringUtils.isBlank(itemId)) {
            return Collections.emptyList();
        }
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemId(itemId);
        request.setQueryFields(new String[]{"id"});
        List<EsShopeeItem> esShopeeItems = this.getEsShopeeItems(request);
        if(CollectionUtils.isNotEmpty(esShopeeItems)) {
            return esShopeeItems.stream().map(o->o.getId()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getIdsByGlobalItemId(String globalItemId) {
        if(StringUtils.isBlank(globalItemId)) {
            return Collections.emptyList();
        }
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setGlobalItemId(globalItemId);
        request.setQueryFields(new String[]{"id"});
        List<EsShopeeItem> esShopeeItems = this.getEsShopeeItems(request);
        if(CollectionUtils.isNotEmpty(esShopeeItems)) {
            return esShopeeItems.stream().map(o->o.getId()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, Integer> getAccountItemSumMap(EsShopeeItemRequest request) {
        if(null == request) {
            return null;
        }
        return esShopeeItemService.getAccountItemSumMap(request);
    }

    @Override
    public  Map<String, Map<Integer,Integer>> getAccountItemSumGroupByRoleMap(EsShopeeItemRequest request) {
        if(null == request) {
            return null;
        }
        return esShopeeItemService.getAccountItemSumGroupByRoleMap(request);
    }

    @Override
    public void deleteById(String id) {
        if(StringUtils.isBlank(id)) {
            return;
        }
        esShopeeItemService.deleteById(id);
    }

    @Override
    public void save(EsShopeeItem esShopeeItem) {
        esShopeeItemService.save(esShopeeItem);
    }

    @Override
    public void saveAll(List<EsShopeeItem> esShopeeItems) {
        esShopeeItemService.saveAll(esShopeeItems);
    }

    @Override
    public void savePreserveOrderStats(EsShopeeItem esShopeeItem) {
        esShopeeItemService.savePreserveOrderStats(esShopeeItem);
    }

    @Override
    public void saveAllPreserveOrderStats(List<EsShopeeItem> esShopeeItems) {
        esShopeeItemService.saveAllPreserveOrderStats(esShopeeItems);
    }

    @Override
    public void update(EsShopeeItem esShopeeItem) {
        if(esShopeeItem != null) {
            esShopeeItemService.updateRequest(esShopeeItem);
        }
    }

    @Override
    public void update(String json, String id) {
        if(StringUtils.isBlank(json) || StringUtils.isBlank(id)){
            return;
        }
        esShopeeItemService.updateRequest(json, id);
    }

    @Override
    public void batchSave(List<EsShopeeItem> esShopeeItems) {
        if(CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }
        // 迁移数据可先屏蔽 完毕后打开
        List<String> itemIds = esShopeeItems.stream().map(EsShopeeItem::getItemId).distinct().collect(Collectors.toList());
        EsShopeeItemRequest esShopeeItemRequest = new EsShopeeItemRequest();
        esShopeeItemRequest.setQueryFields(null);
        esShopeeItemRequest.setItemIdList(itemIds);
        List<EsShopeeItem> localEsShopeeItems = esShopeeItemService.getEsShopeeItems(esShopeeItemRequest);

        Map<String, EsShopeeItem> localItemMap = localEsShopeeItems.stream().collect(Collectors.toMap(EsShopeeItem::getId, item -> item));
        localEsShopeeItems.clear();

        // 试卖会记录到skubind 根据skubind 更新货号和数据来源
        ShopeeItemUtils.setDataSourceBySkuBind(esShopeeItems);

        List<EsShopeeItem> addEsShopeeItems = new ArrayList<>();
        List<EsShopeeItem> updateEsShopeeItems = new ArrayList<>();

        esShopeeItems.forEach(item->{
            EsShopeeItem localEsShopeeItem = localItemMap.get(item.getId());
            if (localEsShopeeItem != null && (localEsShopeeItem.getTrialSaleWeight() == null || localEsShopeeItem.getSaleCost() == null)) {
                // 获取模板信息
                EsShopeeGlobalTemplateRequest templateRequest = new EsShopeeGlobalTemplateRequest();
                templateRequest.setAccountsStr(item.getItemSeller());
                templateRequest.setSku(item.getSpu());
                templateRequest.setQueryFields(new String[]{"id", "sku", "shopeeSkusStr", "trialSaleWeight", "saleCost"});
                List<EsShopeeGlobalTemplate> shopeeGlobalTemplates = shopeeGlobalTemplateEsService.getEsShopeeGlobalTemplates(templateRequest);
                if (CollectionUtils.isNotEmpty(shopeeGlobalTemplates)) {
                    EsShopeeGlobalTemplate shopeeGlobalTemplate = shopeeGlobalTemplates.get(0);
                    List<ShopeeSku> shopeeSkus = JSONArray.parseArray(shopeeGlobalTemplate.getShopeeSkusStr(), ShopeeSku.class);
                    // 有一个满足则直接返回这个ShopeeSku
                    Optional<ShopeeSku> skuOptional = shopeeSkus.stream().filter(shopeeSku -> shopeeSku.getSku().equals(item.getItemSku())).findFirst();
                    if (skuOptional.isPresent()) {
                        ShopeeSku shopeeSku = skuOptional.get();
                        item.setTrialSaleWeight(shopeeSku.getShippingWeight());
                        item.setSaleCost(shopeeSku.getSaleCost());
                    }
                }
            }

            if(null != localEsShopeeItem) {
                // 构建非同步信息 避免被同步覆盖
                ShopeeItemUtils.bulidNotSyncInfo(item, localEsShopeeItem);

                // 下架状态
                if (ShopeeItemStatusEnum.UNLIST.getCode().equals(item.getItemStatus())) {
                    if (StringUtils.isEmpty(localEsShopeeItem.getDownType())) {
                        setFeedTaskService(item);
                    }
                    if (StringUtils.isNotEmpty(localEsShopeeItem.getDownType())) {
                        item.setDownType(localEsShopeeItem.getDownType());
                    }
                }
                updateEsShopeeItems.add(item);

                // 根据itemid查找数据 找到对应的则remove 未找到的为状态同步不回来的变体需要删除
                localItemMap.remove(item.getId());
            } else {
                if (ShopeeItemStatusEnum.UNLIST.getCode().equals(item.getItemStatus())) {
                    setFeedTaskService(item);
                }
                addEsShopeeItems.add(item);
            }
        });

        try {
            // 已存在 保存之前已处理非同步数据
            esShopeeItemService.saveAllPreserveOrderStats(updateEsShopeeItems);
        }catch (Exception e) {
            log.error("shopeeItem batchSave update error" + e.getMessage());
        }

        try {
            // 新增保存前查询产品相关信息 和刊登角色
            ShopeeItemUtils.handleProductInfo(addEsShopeeItems);
            esShopeeItemService.saveAll(addEsShopeeItems);
        }catch (Exception e) {
            log.error("shopeeItem batchSave add error" + e.getMessage());
        }

        localItemMap.forEach((k,v)->{
            try{
                // 备份到下架listing 删除产品可以同步到 变体未同步到的数据
                shopeeItemOfflineEsService.saveAllByEsShopeeItem(Arrays.asList(v), ShopeeItemStatusEnum.DELETED.getCode());
                this.deleteById(k);
            }catch (Exception e) {
                log.error(k + " shopeeItem batchSave del error" + e.getMessage());
            }
        });
    }

    /**
     * 下架状态处理
     *
     * @param item
     */
    private void setFeedTaskService(EsShopeeItem item) {
        // 查处理报告是否有下架数据
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andPlatformEqualTo(Platform.Shopee.name())
                .andAssociationIdEqualTo(item.getItemId())
                .andArticleNumberEqualTo(item.getSpu())
                .andAccountNumberEqualTo(item.getItemSeller())
                .andTaskStatusEqualTo(FeedTaskStatusEnum.FINISH.getTaskStatus())
                .andTaskTypeEqualTo(ShopeeFeedTaskEnum.END_ITEM.getValue());
        int count = feedTaskService.countByExample(feedTaskExample, Platform.Shopee.name());
        if (count > 0) {
            item.setDownType("SALES_OFFLINE");
        } else {
            item.setDownType("PLATFORM_OFFLINE");
        }
    }

    @Override
    public void updateShopeePrice(SaleAccountAndBusinessResponse account, String user, ShopeeItemCalcRequest request) {
        EsShopeeItem esShopeeItem = esShopeeItemService.findAllById(request.getId());
        if(null == esShopeeItem) {
            log.error("updateShopeePrice异常 对应id未查询到对应产品" + request.getId());
        }

        request.setPostFeeIsZero(null);
        request.setArticleNumber(esShopeeItem.getArticleNumber());
        ApiResult<String> apiResult = ShopeeCalcPriceUtil.calcPrice(request, account);

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(esShopeeItem, ShopeeFeedTaskEnum.UPDATE_PRICE, user);

        String resultMsg = null;
        if (apiResult.isSuccess()) {
            //在改价之前，如果当前商品参加了活动，先把活动删掉
            if(esShopeeItem.getDiscountId() != null && esShopeeItem.getDiscountId() != 0) {
                ShopeeDiscountCallV2.deleteDiscountItem(account, esShopeeItem);
            }

            EsShopeeItem changeItem = new EsShopeeItem();
            changeItem.setId(esShopeeItem.getId());
            changeItem.setItemId(esShopeeItem.getItemId());
            changeItem.setOriginalPrice(request.getPrice());
            changeItem.setVariationId(esShopeeItem.getVariationId());
            //请求接口
            ShopeeResponse response = ShopeeUpdateStockPriceCallV2.updatePriceCall(account, changeItem);

//            feedTask.setAttribute1(JSON.toJSONString(changeItem));

            if (StringUtils.isNotBlank(response.getError())) {
                resultMsg = JSON.toJSONString(response);
            } else {
                JSONObject json = JSON.parseObject(response.getResponse());
                JSONArray success_list = json.getJSONArray("success_list");
                if (success_list != null && success_list.size() > 0) {
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    Double original_price = success_list.getJSONObject(0).getDouble("original_price");
                    resultMsg = "修改前价格：" + esShopeeItem.getOriginalPrice() + "，修改后价格：" + original_price;

                    // 修改本地库价格
                    EsShopeeItem localShopeeItem = this.findAllById(request.getId());
                    if(null != localShopeeItem) {
                        localShopeeItem.setOriginalPrice(original_price);
                        localShopeeItem.setPrice(original_price);
                        localShopeeItem.setLastUpdatedBy(user);
                        localShopeeItem.setLastUpdateDate(new Date());
                        this.save(localShopeeItem);
                    }
                }
                JSONArray failure_list = json.getJSONArray("failure_list");
                if (failure_list != null && failure_list.size() > 0) {
                    resultMsg = JSON.toJSONString(response);
                }
            }
        } else {
            resultMsg = apiResult.getErrorMsg();
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultMsg(resultMsg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTaskService.insert(feedTask);
    }

    @Override
    public void updateShopeeSipItemPrice(SaleAccountAndBusinessResponse account, String user, ShopeeItemCalcRequest request) {
        EsShopeeItem esShopeeItem = esShopeeItemService.findAllById(request.getId());
        if(null == esShopeeItem) {
            log.error("updateShopeeSipItemPrice 对应id未查询到对应产品" + request.getId());
        }

        request.setPostFeeIsZero(true);
        request.setArticleNumber(esShopeeItem.getArticleNumber());
        ApiResult<String> apiResult = ShopeeCalcPriceUtil.calcPrice(request, account);

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(esShopeeItem, ShopeeFeedTaskEnum.UPDATE_SIP_ITEM_PRICE, user);

        String resultMsg = null;
        if (apiResult.isSuccess()) {
            Double sipItemPrice = request.getPrice();
            EsShopeeItem changeItem = new EsShopeeItem();
            changeItem.setId(esShopeeItem.getId());
            changeItem.setItemId(esShopeeItem.getItemId());
            changeItem.setSipItemPrice(sipItemPrice);
            changeItem.setVariationId(esShopeeItem.getVariationId());
            //请求接口
            ShopeeResponse response = ShopeeUpdateStockPriceCallV2.updateSipItemPriceCall(account, changeItem);

//            feedTask.setAttribute1(JSON.toJSONString(changeItem));
            feedTask.setAttribute2(JSON.toJSONString(response));
            if (StringUtils.isNotBlank(response.getError())) {
                resultMsg = JSON.toJSONString(response);
            } else {
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                resultMsg = "修改前价格：" + esShopeeItem.getOriginalPrice() + "，修改后价格：" + sipItemPrice;

                // 修改本地库价格
                EsShopeeItem localShopeeItem = this.findAllById(request.getId());
                if(null != localShopeeItem) {
                    localShopeeItem.setSipItemPrice(sipItemPrice);
                    localShopeeItem.setLastUpdatedBy(user);
                    localShopeeItem.setLastUpdateDate(new Date());
                    this.save(localShopeeItem);
                }
            }
        } else {
            resultMsg = apiResult.getErrorMsg();
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultMsg(resultMsg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTaskService.insert(feedTask);
    }

    @Override
    public void syncAccountShopeeItem(SaleAccountAndBusinessResponse account, String user, Boolean isFullSync, Integer days) {
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAccountNumber(account.getAccountNumber());
            task.setTaskType(ShopeeFeedTaskEnum.SYNC_LISTING.getValue());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(user);
            if (BooleanUtils.isFalse(isFullSync)) {
//                task.setAttribute1(String.format("isFullSync: %s, days: %s", isFullSync, days));
            }
        });

        String limitSyncKey = ShopeeRedisConstant.SYNC_ALL_ACCOUNT_LIMIT + ":" + account.getAccountNumber();
        List<String> errorList = new ArrayList<>();
        Boolean limitSync = false;
        if(isFullSync) {
            limitSync = ShopeeCommonUtils.isLimit(limitSyncKey, 1);
            if(limitSync) {
                errorList.add("同一个账号一小时只能全量同步一次，请稍后同步！");
            }
        }

        List<String> allItemIdList = new ArrayList<>();
        Integer totalCount = null;
        int pageSize = 100;
        int offset = 0;
        int tryNum = 5;

        Map<Object, Object> msg = new ConcurrentHashMap<>(3);
        while (true && BooleanUtils.isFalse(limitSync) && tryNum > 0) {
            // 同步itemId
            ApiResult<ResultListDTO<String>> result = ShopeeGetItemsCallV2.getPageItems(account, isFullSync, days, null, offset, pageSize);
            if(!result.isSuccess()) {
                errorList.add(result.getErrorMsg());
            }
            // 没有报错 也没有获取到数据 且 总数比同步的结果数量 超过50 认为漏数据了 需要重试
            ResultListDTO<String> dto = result.getResult();
            if((null == dto || CollectionUtils.isEmpty(dto.getList())) && (totalCount == null || totalCount.intValue() - allItemIdList.size() > 50)) {
                tryNum--;
                continue;
            }
            // 重试多次 或者 相差数量较少跳出
            if(null == dto || CollectionUtils.isEmpty(dto.getList())) {
                log.info(account.getAccountNumber() + "同步总数" + offset);
                break;
            }
            if(null != dto.getTotalCount()) {
                totalCount = dto.getTotalCount();
            }

            List<String> itemIdList = dto.getList();
            allItemIdList.addAll(itemIdList);
            List<List<String>> pagingList = PagingUtils.newPagingList(itemIdList, 50);
            for (int i = 0; i < pagingList.size(); i++) {
                List<String> itemIds = pagingList.get(i);
                try {
                    changeItemInfo(account, isFullSync, msg, itemIds, i);
                }catch (Exception e){
                    log.error(String.format("account:%s, 第%s页更新出错", account.getAccountNumber(), i), e);
                }
            }
            offset += pageSize;
        }

        // 全量同步存在报错删除限制频率的key
        if(isFullSync && (MapUtils.isNotEmpty(msg) || CollectionUtils.isNotEmpty(errorList))) {
            if(CollectionUtils.isNotEmpty(errorList) && "同一个账号一小时只能全量同步一次，请稍后同步！".equals(errorList.get(0))) {
            // 限制频率的报错不可以删除redis频率拦截
            } else {
                try {
                    PublishRedisClusterUtils.del(limitSyncKey);
                }catch (Exception e) {
                    log.error(limitSyncKey + "删除限制同步redis失败" + e.getMessage());
                }
            }
        }

        // 全量同步且无错误信息时候 删除未同步到（delete状态）的数据
        Integer size = null;
        if(isFullSync && MapUtils.isEmpty(msg) && CollectionUtils.isEmpty(errorList)){
            size = CollectionUtils.isEmpty(allItemIdList) ? 0 : allItemIdList.size();
            log.info(account.getAccountNumber() + "账号全量同步 allItemIdList size " + size);
            EsShopeeItemRequest localRequest = new EsShopeeItemRequest();
            localRequest.setQueryFields(new String[]{"id", "itemId", "syncDate"});
            localRequest.setItemSeller(account.getAccountNumber());
            List<EsShopeeItem> localList = esShopeeItemService.getEsShopeeItems(localRequest);
            List<EsShopeeItem> delItems = localList.stream().filter(o -> !allItemIdList.contains(o.getItemId())).distinct().collect(Collectors.toList());

            // 读取系统参数获取指定天数未更新的链接，备份后进行删除
            String notUpdatedDays = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_LISTING_NOT_UPDATED", "NOT_UPDATED_DAYS", 5);

            // 部分店铺同步时间（7天），店铺取配置
            boolean notUpdatedAccountsStatus;
            String notUpdatedAccounts = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_LISTING_NOT_UPDATED", "NOT_UPDATED_ACCOUNTS", 5);
            if (StringUtils.isNotEmpty(notUpdatedAccounts)) {
                notUpdatedAccountsStatus = notUpdatedAccounts.contains(account.getAccountNumber());
            } else {
                notUpdatedAccountsStatus = false;
            }
            delItems.forEach(esShopeeItem -> {
                try{
                    Date thresholdDate = DateUtils.getDateBegin(-35);
                    if (!notUpdatedAccountsStatus) {
                        // 同步时间最近30天（配置）未更新的链接，备份后进行删除
                        if (StringUtils.isNotEmpty(notUpdatedDays)) {
                            try {
                                thresholdDate = DateUtils.getDateBegin(-Integer.parseInt(notUpdatedDays));
                            } catch (NumberFormatException e) {
                                log.error("参数SHOPEE_LISTING_NOT_UPDATED配置错误，请检查！");
                            }
                        }
                    } else {
                        thresholdDate = DateUtils.getDateBegin(-7);
                    }


                    Date syncDate = esShopeeItem.getSyncDate();
                    if (null != syncDate && syncDate.after(thresholdDate)) {
                        return;
                    }
                    shopeeItemOfflineEsService.saveById(esShopeeItem.getId(), ShopeeItemStatusEnum.DELETED.getCode());
                    this.deleteById(esShopeeItem.getId());
                }catch (Exception e) {
                    log.error(esShopeeItem.getId() + "全量同步后删除 delete状态失败" + e.getMessage());
                }
            });
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        if(CollectionUtils.isEmpty(errorList)) {
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
        } else {
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        }

        if(msg.size() > 0 || errorList.size() > 0 ){
            feedTask.setResultMsg((isFullSync?"全量":"增量") + "同步Listing Msg:" + JSON.toJSONString(msg) + JSON.toJSONString(errorList));
        }else{
            feedTask.setResultMsg((isFullSync?"全量":"增量") + "同步Listing完成." + size + "，totalCount " + totalCount);
        }
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public void syncListingByItemId(String accountNumber, String itemId, String user) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), accountNumber, true);
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAccountNumber(accountNumber);
            task.setAssociationId(itemId);
            task.setTaskType(ShopeeFeedTaskEnum.SYNC_LISTING.getValue());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(user);
        });
        ApiResult<ShopeeCustomResult<String, EsShopeeItem>> apiResult = shopeeListingV2Component.getItemList(account, itemId, true);
        if(!apiResult.isSuccess()){
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setResultMsg(apiResult.getErrorMsg());
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTaskService.updateByPrimaryKeySelective(feedTask);
        }else{
            ShopeeCustomResult<String, EsShopeeItem> result = apiResult.getResult();
            List<EsShopeeItem> shopeeItemList = result.getSuccessList();
            result.setSuccessList(null);
            //把父sku捡出来，做防重复刊登
            String parentSku = "";
            for (EsShopeeItem shopeeItem : shopeeItemList) {
                if(shopeeItem.getIsFather()) {
                    parentSku = shopeeItem.getArticleNumber();
                    break;
                }
            }

            String msg="同步Listing成功";
            if(CollectionUtils.isEmpty(shopeeItemList)){
                // todo 根据itemId 删除数据
            }else{
                // 保存数据
                this.batchSave(shopeeItemList);
            }

            //检查重复刊登表里是否有这个sku，没有就插入一条新数据
            if(StringUtils.isNotBlank(parentSku)){
                ShopeePublishSkuExample shopeePublishSkuExample = new ShopeePublishSkuExample();
                ShopeePublishSkuExample.Criteria shopeePublishSkuCriteria = shopeePublishSkuExample.createCriteria();
                shopeePublishSkuCriteria.andAccountEqualTo(accountNumber);
                shopeePublishSkuCriteria.andSkuEqualTo(parentSku);
                int count = shopeePublishSkuService.countByExample(shopeePublishSkuExample);
                if(count == 0){
                    ShopeePublishSku shopeePublishSku = new ShopeePublishSku();
                    shopeePublishSku.setSku(parentSku);
                    shopeePublishSku.setAccount(accountNumber);
                    shopeePublishSkuService.insert(shopeePublishSku);
                }
            }

            feedTask.setArticleNumber(parentSku);
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            List<Object> failList = result.getFailList();
            failList.add(msg);
            feedTask.setResultMsg(JSON.toJSONString(failList));
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTaskService.updateByPrimaryKeySelective(feedTask);
        }
    }

    @Override
    public void updateProduct(SaleAccountAndBusinessResponse shopeeAccount, String user, EsShopeeItem updateShopeeItem) {
        // 过滤侵权词
        delInfringingWords(updateShopeeItem);

        UpdateItemV2 param = new UpdateItemV2();
        param.setItemId(Long.valueOf(updateShopeeItem.getItemId()));

        String descImgMapping = updateShopeeItem.getDescImgMapping();
        String description = updateShopeeItem.getDescription();

        FeedTask feedTask = new FeedTask();
        feedTask.setAccountNumber(shopeeAccount.getAccountNumber());
        feedTask.setAssociationId(updateShopeeItem.getItemId());
        feedTask.setArticleNumber(updateShopeeItem.getSpu());
        if(StringUtils.isNotEmpty(updateShopeeItem.getName())) {
            param.setItemName(updateShopeeItem.getName());
            feedTask.setTaskType(ShopeeFeedTaskEnum.UPDATE_TITLE.getValue());
        }
        boolean isExtend = false;
        if (StringUtils.isNotEmpty(updateShopeeItem.getDescription())) {

            String descriptionType = updateShopeeItem.getDescriptionType();
            isExtend = StringUtils.isNotBlank(descriptionType) && ShopeeDescriptionTypeEnum.EXTENDED.getCode().equals(descriptionType);

            if (isExtend) {
                DescriptionInfoV2 descriptionInfoV2 = new DescriptionInfoV2();
                DescriptionInfoV2.ExtendInfo extendInfo = new DescriptionInfoV2.ExtendInfo();
                descriptionInfoV2.setExtendedDescription(extendInfo);
                List<DescriptionInfoV2.FiledInfo> filedInfos = new ArrayList<>();
                DescriptionInfoV2.FiledInfo filedInfo = ShopeeDescriptionUtil.buildTextFiledInfo(description);
                filedInfos.add(filedInfo);
                if (StringUtils.isNotBlank(descImgMapping)) {
                    List<DescriptionInfoV2.DescriptionInfoImage> descriptionInfoImages = JSON.parseArray(descImgMapping, DescriptionInfoV2.DescriptionInfoImage.class);
                    List<DescriptionInfoV2.FiledInfo> collect = descriptionInfoImages.stream().map(a -> {
                        DescriptionInfoV2.FiledInfo imgFiledInfo = new DescriptionInfoV2.FiledInfo();
                        imgFiledInfo.setFieldType(ShopeeDescriptionFiledTypeEnum.IMAGE.getCode());
                        imgFiledInfo.setImageInfo(a);
                        return imgFiledInfo;
                    }).collect(Collectors.toList());
                    filedInfos.addAll(collect);
                }
                extendInfo.setFieldList(filedInfos);
                param.setDescriptionInfo(descriptionInfoV2);
                param.setDescriptionType(ShopeeDescriptionTypeEnum.EXTENDED.getCode());
            } else {
                param.setDescription(description);
                param.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
            }
            feedTask.setTaskType(ShopeeFeedTaskEnum.UPDATE_DESC.getValue());
        }
        feedTask.setPlatform(Platform.Shopee.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        feedTask.setCreatedBy(user);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        //这个字段毫无意义，随便填
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        feedTaskService.insertSelective(feedTask);

        ShopeeResponse doResponse = ShopeeHttpUtils.doPostV2(shopeeAccount, param);
        if(StringUtils.isNotBlank(doResponse.getError())){
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setResultMsg(JSON.toJSONString(doResponse));
        }else{
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());

            JSONObject json = JSON.parseObject(doResponse.getResponse());
            if(ShopeeFeedTaskEnum.UPDATE_TITLE.getValue().equalsIgnoreCase(feedTask.getTaskType())){
                String item_name = json.getString("item_name");

                // 标题修改成功 修改单个item 需修改所有
                List<EsShopeeItem> localEsShopeeItem = this.getEsShopeeItemsByItemId(updateShopeeItem.getItemId(), null);
                for (EsShopeeItem esShopeeItem : localEsShopeeItem) {
                    esShopeeItem.setName(item_name);
                    esShopeeItem.setLastUpdatedBy(user);
                    esShopeeItem.setLastUpdateDate(new Date());
                }
                esShopeeItemService.saveAll(localEsShopeeItem);
            } else {
                List<EsShopeeItem> localEsShopeeItem = this.getEsShopeeItemsByItemId(updateShopeeItem.getItemId(), null);
                for (EsShopeeItem esShopeeItem : localEsShopeeItem) {
                    if (isExtend) {
                        DescriptionInfoV2 descriptionInfo = param.getDescriptionInfo();
                        DescriptionInfoV2.ExtendInfo extendedDescription1 = descriptionInfo.getExtendedDescription();
                        List<DescriptionInfoV2.FiledInfo> fieldList1 = extendedDescription1.getFieldList();
                        esShopeeItem.setDescription(ShopeeDescriptionUtil.filedInfoToString(fieldList1));
                        List<DescriptionInfoV2.DescriptionInfoImage> collect = fieldList1.stream()
                                .filter(a -> a.getFieldType().equals(ShopeeDescriptionFiledTypeEnum.IMAGE.getCode()))
                                .map(DescriptionInfoV2.FiledInfo::getImageInfo)
                                .filter(Objects::nonNull).collect(Collectors.toList());
                        if (!collect.isEmpty()) {
                            esShopeeItem.setDescImgMapping(JSON.toJSONString(collect));
                            esShopeeItem.setDescriptionType(ShopeeDescriptionTypeEnum.EXTENDED.getCode());
                        } else {
                            esShopeeItem.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
                        }
                    } else {
                        esShopeeItem.setDescription(description);
                        esShopeeItem.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
                    }

                    esShopeeItem.setLastUpdatedBy(user);
                    esShopeeItem.setLastUpdateDate(new Date());
                    shopeeItemEsBulkProcessor.updateDesc(esShopeeItem);
                }
            }
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public void updateProductTitleDesc(EsShopeeItem updateShopeeItem, String userName) {
        SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), updateShopeeItem.getItemSeller(), true);
        //台湾站的不允许更新标题和描述（因为需要翻译）
        if (shopeeAccount.getAccountSite().equalsIgnoreCase("tw")) {
            return;
        }
        String descriptionType = updateShopeeItem.getDescriptionType();
        Boolean isExtend = StringUtils.isNotBlank(descriptionType) && ShopeeDescriptionTypeEnum.EXTENDED.getCode().equals(descriptionType);

        FeedTask titleFeedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(updateShopeeItem, ShopeeFeedTaskEnum.UPDATE_TITLE, userName);
        titleFeedTask.setAssociationId(updateShopeeItem.getItemId());
        titleFeedTask.setArticleNumber(updateShopeeItem.getSpu());
        feedTaskService.insert(titleFeedTask);
        InfringmentResponse infringmentResponse = null;

        Map<String, String> replaceMap = new HashMap<>();
        if (isExtend) {
            // 替换标签, 提前处理下img标签，保存在 replaceMap 中
            updateShopeeItem.setDescription(ShopeeDescriptionUtil.doReplace(updateShopeeItem.getDescription(), replaceMap));
        }

        try {
            infringmentResponse = ShopeeCommonUtils.checkInfringmentWord(Arrays.asList(updateShopeeItem.getName(), updateShopeeItem.getDescription()), updateShopeeItem.getSite());
        } catch (Exception e) {
            String message = "侵权词校验出错" + e.getMessage();
            log.error(updateShopeeItem.getItemId() + message, e);
            feedTaskService.updateFeedTaskToFinish(titleFeedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), message);
            return;
        }

        FeedTask descFeedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(updateShopeeItem, ShopeeFeedTaskEnum.UPDATE_DESC, userName);
        descFeedTask.setAssociationId(updateShopeeItem.getItemId());
        descFeedTask.setArticleNumber(updateShopeeItem.getSpu());
        feedTaskService.insert(descFeedTask);

        // shopee 侵权词服务 暂未校验商标词 只过滤侵权词
        String name = infringementWordService.delInfringementWord(updateShopeeItem.getName(), infringmentResponse.getInfringementMap());
        name = infringementWordService.delInfringementWord(name, infringmentResponse.getBrandMap());
        String desc = infringementWordService.delInfringementWord(updateShopeeItem.getDescription(), infringmentResponse.getInfringementMap());
        desc = infringementWordService.delInfringementWord(desc, infringmentResponse.getBrandMap());

        UpdateItemV2 param = new UpdateItemV2();
        param.setItemId(Long.valueOf(updateShopeeItem.getItemId()));
        if (StringUtils.isNotEmpty(updateShopeeItem.getName())) {
            param.setItemName(name);
        }
        if (StringUtils.isNotEmpty(updateShopeeItem.getDescription())) {
            if (isExtend) {
                // 如果是扩展描述，需要先恢复成模版描述，再替换img标签
                desc = ShopeeDescriptionUtil.recovery(desc, replaceMap);
                String descImgMapping = updateShopeeItem.getDescImgMapping();
                Map<String, String> imgMappingMap = new HashMap<>();
                if (StringUtils.isNotBlank(descImgMapping)) {
                    List<DescriptionInfoV2.DescriptionInfoImage> descriptionInfoImages = JSON.parseArray(descImgMapping, DescriptionInfoV2.DescriptionInfoImage.class);
                    if (CollectionUtils.isNotEmpty(descriptionInfoImages)) {
                        Map<String, String> collect = descriptionInfoImages.stream()
                                .filter(a -> StringUtils.isNotBlank(a.getImageUrl()))
                                .filter(a -> StringUtils.isNotBlank(a.getImageId()))
                                .collect(Collectors.toMap(
                                        DescriptionInfoV2.DescriptionInfoImage::getImageUrl,
                                        DescriptionInfoV2.DescriptionInfoImage::getImageId,
                                        (a, b) -> b));
                        imgMappingMap.putAll(collect);
                    }
                }
                DescriptionInfoV2 descriptionInfoV2 = new DescriptionInfoV2();
                DescriptionInfoV2.ExtendInfo extendInfo = new DescriptionInfoV2.ExtendInfo();
                descriptionInfoV2.setExtendedDescription(extendInfo);

                List<DescriptionInfoV2.FiledInfo> filedInfos = ShopeeDescriptionUtil.buildFiled(desc, imgMappingMap);
                extendInfo.setFieldList(filedInfos);
                param.setDescriptionType(ShopeeDescriptionTypeEnum.EXTENDED.getCode());
                param.setDescriptionInfo(descriptionInfoV2);
                // 重新恢复成为模版描述
                desc = ShopeeDescriptionUtil.filedInfoToString(filedInfos);
            } else {
                param.setDescription(desc);
                param.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
            }
        }
        ShopeeResponse doResponse = ShopeeHttpUtils.doPostV2(shopeeAccount, param);
        if (StringUtils.isNotBlank(doResponse.getError())) {
            feedTaskService.updateFeedTaskToFinish(titleFeedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(doResponse));
            feedTaskService.updateFeedTaskToFinish(descFeedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(doResponse));
        } else {
            feedTaskService.updateFeedTaskToFinish(titleFeedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(doResponse));
            feedTaskService.updateFeedTaskToFinish(descFeedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(doResponse));

            List<String> updateIds = this.getIdsByItemId(updateShopeeItem.getItemId());
            shopeeItemEsBulkProcessor.syncUpdateTitleDesc(updateIds, name, desc, updateShopeeItem.getDescImgMapping(), updateShopeeItem.getDescriptionType());
        }
    }

    /**
     * 过滤标题描述侵权词
     * <p>
     * 这个方法只能在在线列表修改描述使用
     *
     * @param shopeeItem
     */
    private void delInfringingWords(EsShopeeItem shopeeItem) {
        List<String> replaceList = new ArrayList<>(2);
        String name = shopeeItem.getName();
        replaceList.add(StringUtils.isBlank(name) ? "" : name);

        String description = shopeeItem.getDescription();
        replaceList.add(StringUtils.isBlank(description) ? "" : description);

        String site = shopeeItem.getSite();
        if ("BR".equalsIgnoreCase(site)) {
            InfringmentResponse infringmentResponse = null;
            try {
                infringmentResponse = ShopeeCommonUtils.checkInfringmentWord(Arrays.asList(shopeeItem.getName(), shopeeItem.getDescription()), shopeeItem.getSite());
            }catch (Exception e) {
                String message = "侵权词校验出错" + e.getMessage();
                log.error(shopeeItem.getItemId() + message, e);
                return;
            }
            // shopee 侵权词服务 暂未校验商标词 只过滤侵权词
            name = infringementWordService.delInfringementWord(name, infringmentResponse.getInfringementMap());
            name = infringementWordService.delInfringementWord(name, infringmentResponse.getBrandMap());
            description = infringementWordService.delInfringementWord(description, infringmentResponse.getInfringementMap());
            description = infringementWordService.delInfringementWord(description, infringmentResponse.getBrandMap());
            replaceList = new ArrayList<>();
            replaceList.add(StringUtils.isBlank(name) ? "" : name);
            replaceList.add(StringUtils.isBlank(description) ? "" : description);
        } else {
            // 过滤侵权词 不包括违禁词 类型 目前有 侵权，违规
            infringementWordService.delInfringingWords(replaceList, SaleChannel.CHANNEL_SHOPEE, null, null);
            // 过滤侵权词 违禁词 类型
            infringementWordService.delInfringingWords(replaceList, SaleChannel.CHANNEL_SHOPEE, null, StrConstant.INFRINGING_FORBIDDEN_WORD);
        }

        if (StringUtils.isNotBlank(replaceList.get(0))) {
            shopeeItem.setName(replaceList.get(0));
        } else {
            shopeeItem.setName(null);
        }
        if (StringUtils.isNotBlank(replaceList.get(1))) {
            shopeeItem.setDescription(replaceList.get(1));
        } else {
            shopeeItem.setDescription(null);
        }
    }

    @Override
    public void batchStartItems(List<String> itemIds, String user) {
        // 去重
        itemIds = itemIds.stream().distinct().collect(Collectors.toList());

        // 索引数据
        EsShopeeItemRequest itemRequest = new EsShopeeItemRequest();
        itemRequest.setItemStatus("UNLIST");
        itemRequest.setItemIdList(itemIds);
        List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(itemRequest);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            for (String itemId : itemIds) {
                ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                    task.setAssociationId(itemId);
                    task.setTaskType(ShopeeFeedTaskEnum.START_ITEM.getValue());
                    task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    task.setCreatedBy(user);
                    task.setResultMsg("未获取到状态为UNLIST的商品，请核对状态");
                });
            }
        }
        Map<String, List<EsShopeeItem>> accountMap = esShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemSeller));
        accountMap.forEach((account, esShopeeItemList) -> {
            try {
                // 判断是否是SIP店铺或冻结店铺
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, true);
                if (saleAccountAndBusinessResponse.getAccountStatus().equals(SaleAccountStastusEnum.FROZEN.getCode())) {
                    ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                        task.setAccountNumber(account);
                        task.setTaskType(ShopeeFeedTaskEnum.START_ITEM.getValue());
                        task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                        task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                        task.setCreatedBy(user);
                        task.setResultMsg("冻结店铺，不做处理");
                    });
                    return;
                }
                if (BooleanUtils.isTrue(saleAccountAndBusinessResponse.getColBool2())) {
                    ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                        task.setAccountNumber(account);
                        task.setTaskType(ShopeeFeedTaskEnum.START_ITEM.getValue());
                        task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                        task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                        task.setCreatedBy(user);
                        task.setResultMsg("SIP店铺，不做处理");
                    });
                    return;
                }

                Map<String, List<EsShopeeItem>> esShopeeItemMap = esShopeeItemList.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));

                List<EsShopeeItem> shopeeItemList = esShopeeItemList.stream().filter(EsShopeeItem::getIsFather).collect(Collectors.toList());
                // 创建处理报告
                Map<String, FeedTask> feedTaskMap = shopeeItemList.stream()
                        .map(item -> {
                            // 创建处理报告信息
                            return ShopeeFeedTaskHandleUtil.initFeedTask((feedTasktemp) -> {
                                feedTasktemp.setAssociationId(item.getItemId());
                                feedTasktemp.setArticleNumber(item.getSpu());
                                feedTasktemp.setTaskType(ShopeeFeedTaskEnum.START_ITEM.getValue());
                                feedTasktemp.setAccountNumber(account);
                                feedTasktemp.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                                feedTasktemp.setCreatedBy(user);
                            });
                        })
                        .collect(Collectors.toMap(FeedTask::getAssociationId, Function.identity()));


                List<List<EsShopeeItem>> partition = Lists.partition(shopeeItemList, 50);
                for (List<EsShopeeItem> itemList : partition) {
                    // 调用平台接口
                    ResponseJson responseJson = ShopeeUnlistItemCallV2.unlistItem(saleAccountAndBusinessResponse, false, itemList);

                    // 处理结果信息
                    Map<String, List<ResponseError>> responseErrorMap = responseJson.getErrors().stream().collect(Collectors.groupingBy(ResponseError::getStatus));
                    if (responseErrorMap.containsKey(StatusCode.FAIL)) {
                        List<ResponseError> failList = responseErrorMap.get(StatusCode.FAIL);
                        for (ResponseError responseError : failList) {
                            // 更新处理报告
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(responseError.getField()), ResultStatusEnum.RESULT_FAIL.getStatusCode(), responseError.getMessage());
                        }
                    }
                    if (responseErrorMap.containsKey(StatusCode.SUCCESS)) {
                        List<ResponseError> successList = responseErrorMap.get(StatusCode.SUCCESS);
                        for (ResponseError responseError : successList) {
                            // 更新item平台状态
                            List<EsShopeeItem> updateEsShopeeItems = esShopeeItemMap.get(responseError.getField()).stream()
                                    .peek(esShopeeItem -> {
                                        esShopeeItem.setItemStatus("NORMAL");
                                        esShopeeItem.setDownType("");
                                    })
                                    .collect(Collectors.toList());
                            BulkResponse bulkItemResponses = esShopeeItemService.batchUpdateDocuments(updateEsShopeeItems);
                            if (bulkItemResponses.hasFailures()) {
                                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(responseError.getField()), ResultStatusEnum.RESULT_FAIL.getStatusCode(), "修改平台成功，更新本地数据状态失败");
                                continue;
                            }

                            // 更新处理报告
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(responseError.getField()), ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "上架成功");
                        }
                    }
                }

            } catch (Exception e) {
                log.error("shopee上架异常 店铺账号：{}", account, e);
            }
        });
    }


    @Override
    public ResponseJson batchEndItems(List<String> itemIds, boolean unlist, String userName) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(itemIds)) {
            return response;
        }
        List<ResponseError> errorsList = new CopyOnWriteArrayList<>();
        response.setErrors(errorsList);
        try {
            // 父子结构一起查 需要用到子结构删除促销活动
            EsShopeeItemRequest request = new EsShopeeItemRequest();
            request.setItemIdList(itemIds);
            request.setQueryFields(new String[]{"id", "itemId", "hasVariation", "articleNumber", "isFather", "itemSeller", "articleNumber", "spu", "discountId", "variationId"});
            List<EsShopeeItem> esShopeeItems = this.getEsShopeeItems(request);

            Map<String, List<EsShopeeItem>> accountNumberAndEsShopeeItemMap = esShopeeItems.stream().filter(a -> StringUtils.isNotBlank(a.getItemSeller())).distinct().collect(Collectors.groupingBy(a -> a.getItemSeller(), Collectors.toList()));

            Map<String, Boolean> accountIsNnWareHourseMap = new HashMap<>();

            for (String account : accountNumberAndEsShopeeItemMap.keySet()) {
                SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), account);
                if (shopeeAccount != null) {
                    Boolean lazadaBigbagAuthStatus = shopeeAccount.getShopeeColBool3();
                    accountIsNnWareHourseMap.put(account, lazadaBigbagAuthStatus);
                }
            }

            // 过滤清仓甩卖不禁售库存大于0
            for (Map.Entry<String, List<EsShopeeItem>> stringListEntry : accountNumberAndEsShopeeItemMap.entrySet()) {
                List<EsShopeeItem> value = stringListEntry.getValue();
                filterReductionAndCleanForbidSkuItem(accountIsNnWareHourseMap, value, ShopeeFeedTaskEnum.END_ITEM, userName, esShopeeItem -> {
                    response.addError(new ResponseError(StatusCode.FAIL, esShopeeItem.getItemId(), "存在SKU["+esShopeeItem.getArticleNumber()+"]单品状态为清仓，甩卖，且SKU在shopee不禁售，不允许下架"));
                });
            }
            // 重新获取剩余的
            esShopeeItems = accountNumberAndEsShopeeItemMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());;

            // 收集同账号集合
            Map<String, List<EsShopeeItem>> endItemsMap = esShopeeItems.stream().collect(Collectors.groupingBy(o -> o.getItemSeller()));
            List<List<EsShopeeItem>> endList = new ArrayList<>();
            // 数据请求最大 50个集合
            for (Map.Entry<String, List<EsShopeeItem>> entry : endItemsMap.entrySet()) {
                List<EsShopeeItem> items = entry.getValue();
                List<List<EsShopeeItem>> pagingList = PagingUtils.pagingList(items, 50);
                for (List<EsShopeeItem> paging : pagingList) {
                    endList.add(paging);
                }
            }

            // 线程大小
            CountDownLatch countDownLatch = new CountDownLatch(endList.size());
            for (List<EsShopeeItem> list : endList) {
                //异步
                ShopeeExecutors.submitUpdateProduct(rsp -> {
                    try {
                        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, list.get(0).getItemSeller(), true);
                        ResponseJson responseJson = ShopeeUnlistItemCallV2.unlistItem(account, unlist, list);
                        List<ResponseError> errors = responseJson.getErrors();
                        if(CollectionUtils.isNotEmpty(errors)){
                            response.getErrors().addAll(errors);
                        }
                    }catch (Exception e){
                        list.forEach(item -> {
                            response.addError(new ResponseError(StatusCode.FAIL, item.getItemId(), e.getMessage()));
                        });

                        log.error("下架出错：", e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await(10, TimeUnit.MINUTES);

            //记录处理报告
            List<FeedTask> feedTasks = new ArrayList<>();
            // 修改成功后同步修改本地数据
            List<ResponseError> errorList = response.getErrors();
            if (CollectionUtils.isNotEmpty(errorList)) {
                List<String> updateItemIds = new ArrayList<>(errorList.size());
                for (EsShopeeItem item : esShopeeItems) {
                    if(BooleanUtils.isNotTrue(item.getIsFather())) {
                        continue;// 只根据父产品记录处理报告
                    }
                    FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(item, ShopeeFeedTaskEnum.END_ITEM, userName);
                    feedTask.setArticleNumber(item.getSpu());
                    feedTask.setAssociationId(item.getItemId());
                    feedTasks.add(feedTask);
                    for (ResponseError error : errorList) {
                        if (error == null || StringUtils.isBlank(error.getField())) {
                            continue;
                        }
                        String field = error.getField();
                        if (null != item.getId() && StringUtils.equals(item.getItemId(), field)) {
                            if(StatusCode.SUCCESS.equals(error.getStatus())) {
                                updateItemIds.add(item.getItemId());
                                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                            }else {
                                feedTask.setResultMsg(error.getMessage());
                            }
                            break;
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(updateItemIds)) {
                    // 修改本地数据
                    EsShopeeItemRequest updateRequest = new EsShopeeItemRequest();
                    updateRequest.setItemIdList(itemIds);
                    updateRequest.setQueryFields(null);
                    List<EsShopeeItem> localEsShopeeItem = this.getEsShopeeItems(updateRequest);
                    for (EsShopeeItem esShopeeItem : localEsShopeeItem) {
                        esShopeeItem.setItemStatus(unlist ? "UNLIST" : "NORMAL");
                        if (unlist) {
                            esShopeeItem.setDownType("SALES_OFFLINE");
                        }
                        esShopeeItem.setLastUpdatedBy(userName);
                        esShopeeItem.setLastUpdateDate(new Date());
                    }
                    esShopeeItemService.saveAll(localEsShopeeItem);
                    // 下架需要更新至下架listing
                    if(unlist) {
                        shopeeItemOfflineEsService.saveAllByEsShopeeItem(localEsShopeeItem, ShopeeItemStatusEnum.UNLIST.getCode());
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(feedTasks)) {
                try{
                    feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
                }catch (Exception e) {
                    log.error("上下架保存日志出错:", e);
                }
            }
        } catch (Exception e) {
            log.error("上下架出错:", e);
            response.setMessage(e.getMessage());
            response.setStatus(StatusCode.FAIL);
        }

        return response;
    }

    @Override
    public void deleteItem(String itemId, String user, String... remarks) {
        String remark = "";
        if (remarks.length != 0) {
            remark  = remarks[0];
        }
        String[] queryFields = new String[]{"id", "itemId", "hasVariation", "isFather", "itemSeller", "articleNumber", "spu", "discountId", "variationId", "itemSeller"};
        List<EsShopeeItem> esShopeeItems = this.getEsShopeeItemsByItemId(itemId, queryFields);
        if(CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }
        Map<String, Boolean> accountIsNnWareHourseMap = new HashMap<>();
        Map<String, List<EsShopeeItem>> accountNumberAndEsShopeeItemMap = esShopeeItems.stream().filter(a -> StringUtils.isNotBlank(a.getItemSeller())).distinct().collect(Collectors.groupingBy(a -> a.getItemSeller(), Collectors.toList()));
        for (String account : accountNumberAndEsShopeeItemMap.keySet()) {
            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), account);
            if (shopeeAccount != null) {
                Boolean lazadaBigbagAuthStatus = shopeeAccount.getShopeeColBool3();
                accountIsNnWareHourseMap.put(account, lazadaBigbagAuthStatus);
            }
        }

        // 过滤清仓甩卖不禁售库存不为0的
        // 过滤清仓甩卖不禁售库存大于0
        for (Map.Entry<String, List<EsShopeeItem>> stringListEntry : accountNumberAndEsShopeeItemMap.entrySet()) {
            List<EsShopeeItem> value = stringListEntry.getValue();
            filterReductionAndCleanForbidSkuItem(accountIsNnWareHourseMap, value, ShopeeFeedTaskEnum.END_ITEM, user, esShopeeItem -> {});
        }
        // 重新获取剩余的
        esShopeeItems = accountNumberAndEsShopeeItemMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());;

        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(esShopeeItems.get(0), ShopeeFeedTaskEnum.DELETE_ITEM, user);
        feedTask.setArticleNumber(esShopeeItems.get(0).getSpu());
        feedTask.setAssociationId(esShopeeItems.get(0).getItemId());
//        feedTask.setAttribute1(JSON.toJSONString(esShopeeItems.get(0), SerializerFeature.NotWriteDefaultValue));

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), esShopeeItems.get(0).getItemSeller(), true);
        try{
            //在删除之前，如果当前商品参加了活动，先把活动删掉
            for (EsShopeeItem shopeeItem : esShopeeItems) {
                if(shopeeItem.getDiscountId() != null && shopeeItem.getDiscountId() != 0) {
                    ShopeeDiscountCallV2.deleteDiscountItem(account, shopeeItem);
                }
            }
            // 删除产品
            ShopeeResponse response = ShopeeDeleteItemCallV2.deleteItem(esShopeeItems.get(0), account);
            if(StringUtils.isNotBlank(response.getError())){
                feedTask.setResultMsg(remark + JSON.toJSONString(response));
            }else{
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setResultMsg(remark + JSON.toJSONString(response));
                //先备份到下架listing 把本地库的Listing删掉
                for (EsShopeeItem shopeeItem : esShopeeItems) {
                    shopeeItemOfflineEsService.saveById(shopeeItem.getId(), ShopeeItemStatusEnum.DELETED.getCode());
                    this.deleteById(shopeeItem.getId());
                }

                //把重复刊登的数据删掉
                ShopeePublishSkuExample shopeePublishSkuExample = new ShopeePublishSkuExample();
                shopeePublishSkuExample.createCriteria()
                        .andSkuEqualTo(esShopeeItems.get(0).getSpu())
                        .andAccountEqualTo(account.getAccountNumber());
                shopeePublishSkuService.deleteByExample(shopeePublishSkuExample);
            }
        }catch (Exception e){
            feedTask.setResultMsg(remark + e.getMessage());
        }
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        try{
            feedTaskService.insert(feedTask);
        }catch (Exception e) {
            log.error("删除产品保存日志出错:", e);
        }
    }

    // MPSKU删除子sku平台限制不可以删除 该方法未完全测试通过 暂留此方法启用时需测试
    @Override
    public void deleteItemVariation(String id, String user) {
        EsShopeeItem esShopeeItem = this.findAllById(id);
        if(null == esShopeeItem) {
            return;
        }

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(esShopeeItem, ShopeeFeedTaskEnum.DELETE_MODEL, user);
        feedTask.setArticleNumber(esShopeeItem.getArticleNumber());
        feedTask.setAssociationId(esShopeeItem.getItemId());
        if(BooleanUtils.isTrue(esShopeeItem.getIsFather())) {
            feedTask.setTaskType(ShopeeFeedTaskEnum.DELETE_ITEM.getValue());
            feedTask.setArticleNumber(esShopeeItem.getSpu());
        }

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), esShopeeItem.getItemSeller(), true);
        try{
            //在删除之前，如果当前商品参加了活动，先把活动删掉
            if(esShopeeItem.getDiscountId() != null && esShopeeItem.getDiscountId() != 0) {
                ShopeeDiscountCallV2.deleteDiscountItem(account, esShopeeItem);
            }

            // 父产品页面调用此接口为单体调用删除产品接口 变体则调用删除变体接口
            ShopeeResponse response = null;
            if(BooleanUtils.isTrue(esShopeeItem.getIsFather())) {
                response = ShopeeDeleteItemCallV2.deleteItem(esShopeeItem, account);
            } else {
                response = ShopeeDeleteModelCallV2.deleteModel(esShopeeItem, account);
            }
            if(null == response) {
                feedTask.setResultMsg("response is null");
            } else if(StringUtils.isNotBlank(response.getError())){
                feedTask.setResultMsg(JSON.toJSONString(response));
            }else{
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setResultMsg(JSON.toJSONString(response));
                // 先备份到下架listing 把本地库的Listing删掉
                shopeeItemOfflineEsService.saveAllByEsShopeeItem(Arrays.asList(esShopeeItem), ShopeeItemStatusEnum.DELETED.getCode());
                this.deleteById(esShopeeItem.getId());

                // 删除单体后判断 对应item剩下产品 若不是商品（单体 或者变体是商品）则删除对应产品
                List<EsShopeeItem> localEsShopeeItems = this.getEsShopeeItemsByItemId(esShopeeItem.getItemId(), new String[]{"id", "isFather", "isGoods"});
                if(CollectionUtils.isNotEmpty(localEsShopeeItems) && localEsShopeeItems.size() == 1 && BooleanUtils.isFalse(localEsShopeeItems.get(0).getIsGoods())) {
                    this.deleteItem(esShopeeItem.getItemId(), user);
                }

                //把重复刊登的数据删掉
                ShopeePublishSkuExample shopeePublishSkuExample = new ShopeePublishSkuExample();
                shopeePublishSkuExample.createCriteria()
                        .andSkuEqualTo(esShopeeItem.getSpu())
                        .andAccountEqualTo(account.getAccountNumber());
                shopeePublishSkuService.deleteByExample(shopeePublishSkuExample);
            }
        }catch (Exception e){
            feedTask.setResultMsg(e.getMessage());
        }
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        try{
            feedTaskService.insert(feedTask);
        }catch (Exception e) {
            log.error("删除产品保存日志出错:", e);
        }
    }

    @Override
    public void syncProductSkuInfo(List<String> list, List<String> accountNumberList) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        List<List<String>> skus = PagingUtils.newPagingList(list, 200);
        for (List<String> pageSkus : skus) {
            EsShopeeItemRequest request = new EsShopeeItemRequest();
            request.setArticleNumberList(pageSkus);
            request.setQueryFields(new String[]{"id", "articleNumber"});
            request.setItemSellerList(accountNumberList);
            List<EsShopeeItem> esShopeeItems = this.getEsShopeeItems(request);
            if(CollectionUtils.isEmpty(esShopeeItems)) {
                return;
            }

            List<String> existSkus = esShopeeItems.stream()
                    .filter(o->StringUtils.isNotBlank(o.getArticleNumber()))
                    .map(EsShopeeItem::getArticleNumber)
                    .distinct()
                    .collect(Collectors.toList());
            List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(existSkus);
            Map<String, ProductInfoVO> skuMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(singleItemEsList)) {
                for (SingleItemEs singleItemEs : singleItemEsList) {
                    String sonSku = singleItemEs.getSonSku();
                    if(StringUtils.isNotBlank(sonSku)) {
                        skuMap.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                    }
                }
            }

            for (EsShopeeItem esShopeeItem : esShopeeItems) {
                EsShopeeItem locaEsShopeeItem = this.findAllById(esShopeeItem.getId());
                if(null == locaEsShopeeItem) {
                    continue;
                }
                String articleNumber = locaEsShopeeItem.getArticleNumber();
                ProductInfoVO productInfoVO = skuMap.get(articleNumber);
                if(null == productInfoVO) {
                    continue;
                }
                ShopeeItemUtils.setProductInfo(locaEsShopeeItem, productInfoVO);
                locaEsShopeeItem.setLastUpdateDate(new Date());
                this.save(locaEsShopeeItem);
            }
        }
    }

    @Override
    public List<ExeclShopeeItemMonth> monthListingStatisticsFile(String fromUploadDate, String toUploadDate) {
        if(StringUtils.isBlank(fromUploadDate) || StringUtils.isBlank(toUploadDate)) {
            return Collections.emptyList();
        }
        // 产品数量
        EsShopeeItemRequest fatherRequest = new EsShopeeItemRequest();
        fatherRequest.setIsFather(true);
        fatherRequest.setFromUploadDate(fromUploadDate);
        fatherRequest.setToUploadDate(toUploadDate);
        Map<String, Integer> fatherAccountCountMap = this.getAccountItemSumMap(fatherRequest);

        // listing总数
        EsShopeeItemRequest goodsRequest = new EsShopeeItemRequest();
        goodsRequest.setIsGoods(true);
        goodsRequest.setFromUploadDate(fromUploadDate);
        goodsRequest.setToUploadDate(toUploadDate);
        Map<String, Integer> goodsAccountCountMap = this.getAccountItemSumMap(goodsRequest);

        // 发货天数小于等于2 daysToShip
        EsShopeeItemRequest daysToShipLe3Request = new EsShopeeItemRequest();
        daysToShipLe3Request.setDaysToShipType(1);
        daysToShipLe3Request.setIsGoods(true);
        daysToShipLe3Request.setFromUploadDate(fromUploadDate);
        daysToShipLe3Request.setToUploadDate(toUploadDate);
        Map<String, Integer> daysToShipLe3AccountCountMap = this.getAccountItemSumMap(daysToShipLe3Request);

        // 发货天数大于2
        EsShopeeItemRequest daysToShipGt3Request = new EsShopeeItemRequest();
        daysToShipGt3Request.setDaysToShipType(2);
        daysToShipGt3Request.setIsGoods(true);
        daysToShipGt3Request.setFromUploadDate(fromUploadDate);
        daysToShipGt3Request.setToUploadDate(toUploadDate);
        Map<String, Integer> daysToShipGt3AccountCountMap = this.getAccountItemSumMap(daysToShipGt3Request);

        // NORMAL
        EsShopeeItemRequest NormalRequest = new EsShopeeItemRequest();
        NormalRequest.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        NormalRequest.setIsGoods(true);
        NormalRequest.setFromUploadDate(fromUploadDate);
        NormalRequest.setToUploadDate(toUploadDate);
        Map<String, Integer> normalAccountCountMap = this.getAccountItemSumMap(NormalRequest);

        List<String> accountNumbers = new ArrayList<>(goodsAccountCountMap.keySet());
        Map<String, Set<String>> accountSaleMap = EsAccountUtils.getSalesmanAccountMapByEs(accountNumbers, SaleChannel.CHANNEL_SHOPEE);
        List<ExeclShopeeItemMonth> execlShopeeItemMonths = new ArrayList<>();
        goodsAccountCountMap.forEach((account,goodsCount)->{
            ExeclShopeeItemMonth shopeeItemMonth = new ExeclShopeeItemMonth();
            shopeeItemMonth.setItemSeller(account);
            shopeeItemMonth.setGoodsCount(goodsCount.toString());
            Integer fatherAccount = fatherAccountCountMap.get(account);
            shopeeItemMonth.setFatherCount(fatherAccount == null ? "0" : fatherAccount.toString());
            Integer daysToShipLe3Count = daysToShipLe3AccountCountMap.get(account);
            shopeeItemMonth.setDaysToShipLe3Count(daysToShipLe3Count == null ? "0" : daysToShipLe3Count.toString());
            Integer daysToShipGt3Count = daysToShipGt3AccountCountMap.get(account);
            shopeeItemMonth.setDaysToShipGt3Count(daysToShipGt3Count == null ? "0" : daysToShipGt3Count.toString());
            Integer normalCount = normalAccountCountMap.get(account);
            shopeeItemMonth.setNormalCount(normalCount == null ? "0" : normalCount.toString());
            shopeeItemMonth.setStatus("listing总数");
            shopeeItemMonth.setUploadTime(fromUploadDate + "~" + toUploadDate);
            Set<String> saleNames = accountSaleMap.get(account);
            String saleName = CollectionUtils.isEmpty(saleNames) ? "" :  new ArrayList<>(saleNames).get(0);
            shopeeItemMonth.setSaleName(saleName);
            execlShopeeItemMonths.add(shopeeItemMonth);
        });
        return execlShopeeItemMonths;
    }

    @Override
    public void syncItemLimit(String accountNumber) throws Exception {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), accountNumber, true);
        ShopeeResponse response = ShopeeGetItemLimitCallV2.getItemLimit(account, null);

        ShopeeAccountConfig accountConfig = ShopeeGetItemLimitCallV2.toItemLimit(response);
        if(accountConfig == null ||
                (accountConfig.getLimitItemCount() == null && accountConfig.getLimitItemPriceMax() == null && accountConfig.getLimitItemPriceMin() == null)) {
            throw new Exception("返回正确未解析到相关数据" + JSON.toJSONString(response));
        } else {
            accountConfig.setAccount(accountNumber);
            accountConfig.setSyncLimitDate(new Timestamp(System.currentTimeMillis()));
            shopeeAccountConfigService.updateItemLimitByAccount(accountConfig);
        }
    }

    @Override
    public void syncItemCount(String accountNumber) throws Exception {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), accountNumber, true);
        Integer itemCount = ShopeeGetItemsCallV2.getItemCount(account, Arrays.asList(ShopeeItemStatusEnum.NORMAL.getCode()));
        if(itemCount == null) {
            throw new Exception("返回正确未解析到相关数据" );
        } else {
            ShopeeAccountConfig accountConfig = new ShopeeAccountConfig();
            accountConfig.setItemCount(itemCount);
            accountConfig.setAccount(accountNumber);
            accountConfig.setSyncItemCountDate(new Timestamp(System.currentTimeMillis()));
            shopeeAccountConfigService.updateItemCountByAccount(accountConfig);
        }
    }

    /**
     * 获取商品中清仓甩卖且不禁售的sku及不为0库存
     * itemId可以是不同店铺
     * @param accountIsNnWarehouseMap
     * @param esIds
     * @return
     */
    @Override
    public Map<String, Integer> getReductionAndCleanForbidSkuStockByEsId(Map<String, Boolean> accountIsNnWarehouseMap, List<String> esIds) {
        if (CollectionUtils.isEmpty(esIds)) {
            return Collections.emptyMap();
        }

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setIdList(esIds);
        request.setQueryFields(new String[]{"id", "articleNumber", "forbidChannel", "itemSeller"});
        request.setSkuStatusList(Arrays.asList(SkuStatusEnum.REDUCTION.getCode(), SkuStatusEnum.CLEARANCE.getCode()));
        List<EsShopeeItem> items = esShopeeItemService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyMap();
        }

        if (MapUtils.isEmpty(accountIsNnWarehouseMap)) {
            List<String> accountNumberList = items.stream().map(EsShopeeItem::getItemSeller).collect(Collectors.toList());
            List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByAccounts(accountNumberList);
            Map<String, Boolean> collect = shopeeAccountConfigs.stream().filter(a -> Objects.nonNull(a.getIsNanningWarehouse()))
                    .collect(Collectors.toMap(ShopeeAccountConfig::getAccount, ShopeeAccountConfig::getIsNanningWarehouse, (oldValue, newValue) -> newValue));
            accountIsNnWarehouseMap.putAll(collect);
        }

        // 存在清仓甩卖的sku,检查是否虾皮禁售
        Map<String, List<EsShopeeItem>> collect = items.stream()
                .filter(item -> StringUtils.isBlank(item.getForbidChannel()) || !item.getForbidChannel().contains(SaleChannel.CHANNEL_SHOPEE))
                .collect(Collectors.groupingBy(a -> a.getItemSeller(), Collectors.toList()));

        Map<String, Integer> esIdAndskuStockMap = Maps.newHashMap();
        for (Map.Entry<String, List<EsShopeeItem>> stringListEntry : collect.entrySet()) {
            String key = stringListEntry.getKey();
            List<EsShopeeItem> value = stringListEntry.getValue();
            for (EsShopeeItem esShopeeItem : value) {
                Boolean b = accountIsNnWarehouseMap.get(key);
                String sku = esShopeeItem.getArticleNumber();
                Integer skuAllStock = null;
                if (BooleanUtils.isTrue(b)) {
                    skuAllStock = SkuStockUtils.getSZAndNNSkuSystemStock(sku);
                } else {
                    skuAllStock = SkuStockUtils.getSkuSystemStock(sku);
                }
                if (skuAllStock != null && skuAllStock > 0) {
                    esIdAndskuStockMap.put(esShopeeItem.getId(), skuAllStock);
                }
            }
        }
        return esIdAndskuStockMap;
    }

    @Override
    public ApiResult<List<BatchPriceCalculatorResponse>> calcPriceByItem(List<ShopeeItemCalcPrice> shopeeItemCalcPrices) {
        if(CollectionUtils.isEmpty(shopeeItemCalcPrices)) {
            return ApiResult.newError("请求数据为空！");
        }
        List<String> itemIds = shopeeItemCalcPrices.stream().map(ShopeeItemCalcPrice::getItemId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newError("请求数据为空！");
        }

        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_ITEM" ,"Crawl_Ad_grossProfitRate", null);
        Double grossProfitRate = null;
        try {
            grossProfitRate = Double.parseDouble(StrUtil.strDeldSpecialChar(paramValue));
        }catch (Exception e) {
            log.error("爬虫广告毛利率配置异常" + e.getMessage(), e);
        }
        if(null == grossProfitRate) {
            return ApiResult.newError("请检查刊登配置的爬虫广告毛利率是否正确！");
        }

        List<BatchPriceCalculatorResponse> esNotFounds = new ArrayList<>();

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemIdList(itemIds);
        request.setIsGoods(true);
        request.setQueryFields(new String[]{"id", "itemId", "variationId", "itemSeller", "site", "isGoods", "articleNumber", "skuStatus"});
        List<EsShopeeItem> items = esShopeeItemService.getEsShopeeItems(request);
        Map<String, List<EsShopeeItem>> itemIdMap = items.stream().filter(o->o.getIsGoods()).collect(Collectors.groupingBy(o->o.getItemId()));

        List<BatchPriceCalculatorRequest> reqList = new ArrayList<>();
        for (ShopeeItemCalcPrice shopeeItemCalcPrice : shopeeItemCalcPrices) {
            try {
                EsShopeeItem esShopeeItem = ShopeeItemUtils.getShopeeItemByCalcPriceRequest(itemIdMap ,shopeeItemCalcPrice);
                BatchPriceCalculatorRequest req = ShopeeCalcPriceUtil.buildRequest(shopeeItemCalcPrice, esShopeeItem, grossProfitRate);
                reqList.add(req);
            }catch (Exception e) {
                BatchPriceCalculatorResponse response = new BatchPriceCalculatorResponse();
                response.setId(shopeeItemCalcPrice.getId());
                response.setIsSuccess(false);
                response.setErrorMsg(e.getMessage());
                esNotFounds.add(response);
                continue;
            }
        }

        List<BatchPriceCalculatorResponse> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(esNotFounds)) {
            resultList.addAll(esNotFounds);
        }
        if(CollectionUtils.isNotEmpty(reqList)) {
            ApiResult<List<BatchPriceCalculatorResponse>> calcApiResult = PriceCalculatedUtil.batchPriceCalculator(reqList, 3);
            if(!calcApiResult.isSuccess()) {
                return calcApiResult;
            }

            // 成功加上 刊登过滤的数据返回给爬虫
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = calcApiResult.getResult();
            if(CollectionUtils.isNotEmpty(batchPriceCalculatorResponses)) {
                resultList.addAll(batchPriceCalculatorResponses);
            }
        }

        return ApiResult.newSuccess(resultList);
    }

    @Override
    public ApiResult<List<BatchPriceCalculatorResponse>> calcPriceByItem2(List<ShopeeItemCalcPrice> shopeeItemCalcPrices) {
        if(CollectionUtils.isEmpty(shopeeItemCalcPrices)) {
            return ApiResult.newError("请求数据为空！");
        }
        List<String> itemIds = shopeeItemCalcPrices.stream().map(ShopeeItemCalcPrice::getItemId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newError("请求数据为空！");
        }

        List<BatchPriceCalculatorResponse> esNotFounds = new ArrayList<>();

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemIdList(itemIds);
        request.setIsGoods(true);
        request.setQueryFields(new String[]{"id", "itemId", "variationId", "itemSeller", "site", "isGoods", "articleNumber", "skuStatus"});
        List<EsShopeeItem> items = esShopeeItemService.getEsShopeeItems(request);
        Map<String, List<EsShopeeItem>> itemIdMap = items.stream().filter(o->o.getIsGoods()).collect(Collectors.groupingBy(o->o.getItemId()));

        List<BatchPriceCalculatorRequest> reqList = new ArrayList<>();
        for (ShopeeItemCalcPrice shopeeItemCalcPrice : shopeeItemCalcPrices) {
            try {
                EsShopeeItem esShopeeItem = ShopeeItemUtils.getShopeeItemByCalcPriceRequest(itemIdMap ,shopeeItemCalcPrice);
                BatchPriceCalculatorRequest req = ShopeeCalcPriceUtil.buildRequest(shopeeItemCalcPrice, esShopeeItem, shopeeItemCalcPrice.getProfit());
                reqList.add(req);
            }catch (Exception e) {
                BatchPriceCalculatorResponse response = new BatchPriceCalculatorResponse();
                response.setId(shopeeItemCalcPrice.getId());
                response.setIsSuccess(false);
                response.setErrorMsg(e.getMessage());
                esNotFounds.add(response);
                continue;
            }
        }

        List<BatchPriceCalculatorResponse> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(esNotFounds)) {
            resultList.addAll(esNotFounds);
        }
        if(CollectionUtils.isNotEmpty(reqList)) {
            ApiResult<List<BatchPriceCalculatorResponse>> calcApiResult = PriceCalculatedUtil.batchPriceCalculator(reqList, 3);
            if(!calcApiResult.isSuccess()) {
                return calcApiResult;
            }

            // 成功加上 刊登过滤的数据返回给爬虫
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = calcApiResult.getResult();
            if(CollectionUtils.isNotEmpty(batchPriceCalculatorResponses)) {
                List<BatchPriceCalculatorResponse> collect = batchPriceCalculatorResponses.stream().filter(a -> a.getShippingCost() != null && a.getShippingCost() != 0).collect(Collectors.toList());
                resultList.addAll(collect);
            }
        }
        return ApiResult.newSuccess(resultList);
    }

    @Override
    public void updateGlobalItemVideo(UpdateGlobalItemVideoRequest request, String userName) {
        if(request == null) {
            return;
        }
        String globalItemId = request.getGlobalItemId();
        String accountNumber = request.getAccountNumber();
        String spu = request.getSpu();
        if(StringUtils.isBlank(globalItemId) || StringUtils.isBlank(accountNumber) || StringUtils.isBlank(spu)) {
            return;
        }

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.createFeedTask(accountNumber, ShopeeFeedTaskEnum.UPDATE_GLOBAL_ITEM_VIDEO.getValue(), globalItemId, spu, userName);
        try {
            String videoUrl = request.getVideoUrl();
            String videoId = request.getVideoId();
            if(StringUtils.isBlank(videoId)) { // 空字符串代表删除视频
                if(StringUtils.isBlank(videoUrl)) {
                    throw new RuntimeException("视频地址不可以为空");
                }
                // 查询连接 是否已经记录对应的videoId
                ApiResult<String> videoResult = ShopeeUploadVideoUtil.getVideoUploadId(videoUrl, accountNumber, spu);
                if(!videoResult.isSuccess()) {
                    throw new RuntimeException(StringUtils.isBlank(videoResult.getErrorMsg()) ? "错误信息未记录到" : videoResult.getErrorMsg());
                } else if (StringUtils.isBlank(videoResult.getResult())) {
                    throw new RuntimeException("上传视频未获取到videoId");
                }
                videoId = videoResult.getResult();
            }
            if(StringUtils.isBlank(videoId)) {
                throw new RuntimeException("videoId不可以为空");
            }

            UpdateGlobalItem updateGlobalItem = new UpdateGlobalItem();
            updateGlobalItem.setGlobalItemId(Long.valueOf(globalItemId));
            updateGlobalItem.setVideoUploadId(Collections.singletonList(videoId));
            SaleAccountAndBusinessResponse cnscAccount = null;
            try {
                cnscAccount = CNSCPublishUtil.getCnscAccount(accountNumber);
            }catch (Exception e){
                throw new RuntimeException(String.format("account:%s, get info error:%s", accountNumber, e.getMessage()));
            }
            if(cnscAccount == null){
                throw new RuntimeException( accountNumber + "cnsc账号获取失败为空！");
            }
            ShopeeResponse shopeeResponse = ShopeeUpdateGlobalItemCall.updateGlobalItem(updateGlobalItem, cnscAccount);
            if(StringUtils.isNotBlank(shopeeResponse.getError())){
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(shopeeResponse));
            }  else {
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(shopeeResponse));
            }
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
        }
    }

    @Override
    public void updateImage(ShopeeItemUpdateImageDto dto, String userName) {
        if(dto == null || StringUtils.isBlank(dto.getItemSeller()) || StringUtils.isBlank(dto.getItemId())) {
            return;
        }

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.createFeedTask(dto.getItemSeller(), ShopeeFeedTaskEnum.UPDATE_IMAGE.getValue(), dto.getItemId(), dto.getSpu(), userName);
        try {
            List<String> imageList = dto.getImages();
            if(CollectionUtils.isEmpty(imageList)) {
                throw new RuntimeException("图片为空！");
            }

            Map<String, ShopeeImageDto> shopeeImageDtoMap = ShopeeUploadImageUtil.getShopeeImageDto(dto.getSpu(), imageList, dto.getItemSeller(), true);
            UpdateItemV2 param = new UpdateItemV2();
            param.setItemId(Long.valueOf(dto.getItemId()));
            List<String> platformImageList = param.getPlatformImageAndBulidImage(imageList, shopeeImageDtoMap);

            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getItemSeller(), true);
            ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(shopeeAccount, param);
            if(StringUtils.isNotBlank(shopeeResponse.getError())){
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(shopeeResponse));
            } else {
                // 修改本地记录 根据itemId修改 item下的子sku都需要修改
                List<String> updateIds = this.getIdsByItemId(dto.getItemId());
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("images", JSON.toJSONString(platformImageList));
                updateMap.put("lastUpdateDate", DateUtils.getCurrentTimeStr(null));
                updateMap.put("lastUpdatedBy", userName);
                String json = JSON.toJSONString(updateMap);
                for (String updateId : updateIds) {
                    this.update(json, updateId);
                }
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(shopeeResponse));
            }
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
        }
    }

    @Override
    public void updateSkuImage(List<ShopeeItemUpdateSkuImageDto> dtos, String userName) {
        if(CollectionUtils.isEmpty(dtos)) {
            return;
        }

        ShopeeItemUpdateSkuImageDto dto = dtos.get(0);
        String itemId = dto.getItemId();
        String account = dto.getItemSeller();
        String spu = dto.getSpu();
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.createFeedTask(dto.getItemSeller(), ShopeeFeedTaskEnum.UPDATE_SKU_IMAGE.getValue(), itemId, spu, userName);
        try {
            if(StringUtils.isBlank(itemId) || StringUtils.isBlank(account) || StringUtils.isBlank(spu)) {
                throw new RuntimeException("账号 spu itemId 都不可以为空");
            }

            EsShopeeItemRequest esShopeeItemRequest = new EsShopeeItemRequest();
            esShopeeItemRequest.setItemId(itemId);
            esShopeeItemRequest.setItemSeller(account);
            esShopeeItemRequest.setQueryFields(new String[]{"id", "itemId", "itemSeller", "articleNumber", "spu", "variationId", "variationOptions"});
            List<EsShopeeItem> locaShopeeItems = this.getEsShopeeItems(esShopeeItemRequest);
            locaShopeeItems = locaShopeeItems.stream().filter(o->StringUtils.isNotBlank(o.getVariationId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(locaShopeeItems)) {
                throw new RuntimeException(String.format("es未找到 itemId:%s, itemSeller:%s", itemId, account));
            }
            dtos = dtos.stream()
                    .filter(o->(StringUtils.equalsIgnoreCase(itemId, o.getItemId()) && StringUtils.equalsIgnoreCase(account, o.getItemSeller())))
                    .collect(Collectors.toList());

            List<String> imageList = dtos.stream().map(o->o.getSkuImage()).collect(Collectors.toList());
            Map<String, ShopeeImageDto> shopeeImageDtoMap = ShopeeUploadImageUtil.getShopeeImageDto(spu, imageList, account, false);
            if(MapUtils.isEmpty(shopeeImageDtoMap)) {
                throw new RuntimeException("上传图片获取数据为空集合");
            }

            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getItemSeller(), true);

            UpdateTierVariationParamV2 param = new UpdateTierVariationParamV2();
            param.setItemId(Long.valueOf(dto.getItemId()));
            param.buildInfo(shopeeImageDtoMap, locaShopeeItems, dtos);
            ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(shopeeAccount, param);
            if(StringUtils.isNotBlank(shopeeResponse.getError())){
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(shopeeResponse));
            } else {
                for (EsShopeeItem esShopeeItem : locaShopeeItems) {
                    EsShopeeItem locaEsShopeeItem = this.findAllById(esShopeeItem.getId());
                    if(null == locaEsShopeeItem) {
                        continue;
                    }
                    locaEsShopeeItem.setLastUpdatedBy(userName);
                    locaEsShopeeItem.setLastUpdateDate(new Date());
                    locaEsShopeeItem.setVariationOptions(esShopeeItem.getVariationOptions());
                    this.save(locaEsShopeeItem);
                }
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(shopeeResponse));
            }
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
        }
    }

    @Override
    public void batchUpdateAttributes(UpdateAttributesDto updateAttributesDto, String userName) {
        try {
            List<String> ids = updateAttributesDto.getIds();
            if(CollectionUtils.isEmpty(ids)) {
                log.error("修改属性ids为空"  + JSON.toJSONString(updateAttributesDto));
                return;
            }

            // 过滤时候 属性value为空  替换时不可以为空
            Boolean needBlankValue = StringUtils.equalsIgnoreCase(updateAttributesDto.getType(), "delete");
            Map<Integer, JSONObject> attributesMap = ShopeeAttributesUtils.toAttributesMap(updateAttributesDto.getAttributesStr(), needBlankValue);

            // 获取全球id
            EsShopeeItemRequest globalItemIdRequest = new EsShopeeItemRequest();
            globalItemIdRequest.setIdList(ids);
            globalItemIdRequest.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
            globalItemIdRequest.setQueryFields(new String[]{"id", "itemId", "globalItemId"});
            List<EsShopeeItem> globalShopeeItems = this.getEsShopeeItems(globalItemIdRequest);
            if(CollectionUtils.isEmpty(globalShopeeItems)) {
                return;
            }
            List<String> globalItemIds = globalShopeeItems.stream().map(o->o.getGlobalItemId()).collect(Collectors.toList());

            EsShopeeItemRequest esShopeeItemRequest = new EsShopeeItemRequest();
            esShopeeItemRequest.setGlobalItemIdList(globalItemIds);
            esShopeeItemRequest.setQueryFields(new String[]{"id", "itemId", "globalItemId", "itemSeller", "spu", "articleNumber", "attributes"});
            List<EsShopeeItem> esShopeeItems = this.getEsShopeeItems(esShopeeItemRequest);
            if(CollectionUtils.isEmpty(esShopeeItems)) {
                return;
            }

            // globalItemId 分组
            Map<String, List<EsShopeeItem>> globalItemMap = esShopeeItems.stream()
                    .filter(o->StringUtils.isNotBlank(o.getGlobalItemId()))
                    .collect(Collectors.groupingBy(o->o.getGlobalItemId()));

            for (List<EsShopeeItem> shopeeItems : globalItemMap.values()) {
                ShopeeExecutors.executeItemAttributes(() -> {
                    try {
                        EsShopeeItem localEsShopeeItem = shopeeItems.get(0);
                        String accountNumber = localEsShopeeItem.getItemSeller();
                        Map<Integer, JSONObject> dbAttributesMap = ShopeeAttributesUtils.toAttributesMap(localEsShopeeItem.getAttributes(), false);
                        dbAttributesMap = ShopeeAttributesUtils.replaceAttributes(dbAttributesMap, attributesMap, updateAttributesDto.getType());

                        List<FeedTask> feedTasks = new ArrayList<>();
                        // globalItemId 分组
                        Map<String, List<EsShopeeItem>> itemIdMap = shopeeItems.stream()
                                .filter(o->StringUtils.isNotBlank(o.getItemId()))
                                .collect(Collectors.groupingBy(o->o.getItemId()));
                        itemIdMap.forEach((itemId, items)->{
                            FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(items.get(0), ShopeeFeedTaskEnum.UPDATE_ITEM_ATTRIBUTES, userName);
                            feedTask.setAssociationId(itemId);
                            feedTask.setArticleNumber(items.get(0).getSpu());
                            feedTasks.add(feedTask);
                        });

                        UpdateGlobalItem param = new UpdateGlobalItem();
                        param.setGlobalItemId(Long.valueOf(localEsShopeeItem.getGlobalItemId()));
                        param.buildAttributeList(JSON.toJSONString(dbAttributesMap.values()));
                        SaleAccountAndBusinessResponse cnscAccount = null;
                        try {
                            cnscAccount = CNSCPublishUtil.getCnscAccount(accountNumber);
                        }catch (Exception e){
                            throw new RuntimeException(String.format("account:%s, get info error:%s", accountNumber, e.getMessage()));
                        }
                        if(cnscAccount == null){
                            throw new RuntimeException( accountNumber + "cnsc账号获取失败为空！");
                        }
                        ShopeeResponse shopeeResponse = ShopeeUpdateGlobalItemCall.updateGlobalItem(param, cnscAccount);
                        if(StringUtils.isBlank(shopeeResponse.getError())){
                            for (FeedTask feedTask : feedTasks) {
                                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                            }
                            for (EsShopeeItem updateEsShopeeItem : shopeeItems) {
                                updateEsShopeeItem.setLastUpdatedBy(userName);
                                updateEsShopeeItem.setAttributes(JSON.toJSONString(dbAttributesMap.values()));
                                shopeeItemEsBulkProcessor.updateAttributesStr(updateEsShopeeItem);
                            }
                        }
                        for (FeedTask feedTask : feedTasks) {
                            feedTask.setResultMsg(JSON.toJSONString(shopeeResponse));
                            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                            feedTaskService.insert(feedTask);
                        }
                    }catch (Exception e) {
                        log.error(e.getMessage(), e);
                        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(shopeeItems.get(0), ShopeeFeedTaskEnum.UPDATE_ITEM_ATTRIBUTES, userName);
                        feedTask.setResultMsg(e.getMessage());
                        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                        feedTaskService.insert(feedTask);
                    }
                });
            }
        }catch (Exception e) {
            log.error("修改分类属性异常" + updateAttributesDto.getCategoryId() + e.getMessage(), e);
        }
    }

    @Override
    public ApiResult<String> syncListingByItemIds(String accountNumber, List<String> itemIds, Boolean isPullAllDetail) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), accountNumber, true);

        AtomicInteger atomicInteger = new AtomicInteger();
        List<List<String>> itemPartition = Lists.partition(itemIds, 50);
        itemPartition.forEach(syncItems -> {
            try {
                ApiResult<ShopeeCustomResult<String, EsShopeeItem>> apiResult = shopeeListingV2Component.getItemList(account, syncItems, isPullAllDetail);
                if (!apiResult.isSuccess()) {
                    log.error("同步Listing失败，原因：" + apiResult.getErrorMsg());
                    return;
                }
                ShopeeCustomResult<String, EsShopeeItem> result = apiResult.getResult();
                List<EsShopeeItem> shopeeItemList = result.getSuccessList();
                if (CollectionUtils.isNotEmpty(shopeeItemList)) {
                    atomicInteger.addAndGet(shopeeItemList.size());
                    // 保存数据
                    this.batchSave(shopeeItemList);
                }
            } catch (Exception e) {
                log.error("同步Listing失败，原因：" + e.getMessage(), e);
            }
        });
        return ApiResult.newSuccess(atomicInteger.get() + "条数据同步成功");
    }

    /**
     * 过滤清仓甩卖不禁售库存不为0的
     * 要求 item 是同个店铺
     * @param items          目标数据源
     * @param feedTaskEnum   操作类型
     * @param userName       当前用户
     * @param consumer       单个额外的操作
     */
    public void filterReductionAndCleanForbidSkuItem(Map<String, Boolean> accountIsNnWareHourseMap, List<EsShopeeItem> items, ShopeeFeedTaskEnum feedTaskEnum, String userName, Consumer<EsShopeeItem> consumer) {
        List<String> idList = items.stream().map(EsShopeeItem::getId).collect(Collectors.toList());
        Map<String, Integer> esIdAndSkuStockMap = getReductionAndCleanForbidSkuStockByEsId(accountIsNnWareHourseMap, idList);
        Map<String, List<EsShopeeItem>> itemGroup = items.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
        itemGroup.forEach((itemId, itemList) -> {
            List<EsShopeeItem> resultList = itemList.stream().filter(item -> {
                if (esIdAndSkuStockMap.containsKey(item.getId())) {
                    consumer.accept(item);
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(resultList)) {
                EsShopeeItem esShopeeItem = resultList.get(0);
                FeedTask feedTask = ShopeeFeedTaskHandleUtil.initItemFeedTask(esShopeeItem, feedTaskEnum, userName);
                feedTask.setArticleNumber(esShopeeItem.getSpu());
                feedTask.setAssociationId(esShopeeItem.getItemId());
                String msg = String.format("存在SKU单品状态为清仓，甩卖，且SKU在shopee不禁售，不允许%s", feedTaskEnum.equals(ShopeeFeedTaskEnum.END_ITEM) ? "下架" : "删除");
                feedTask.setResultMsg(msg);
                feedTaskService.insert(feedTask);
                items.removeIf(item -> item.getItemId().equals(itemId));
            }
        });
    }

    private void changeItemInfo(SaleAccountAndBusinessResponse account, Boolean isFullSync, Map<Object, Object> msg, List<String> itemIds, int batchNo) {
        ApiResult<ShopeeCustomResult<String, EsShopeeItem>> apiResult = shopeeListingV2Component.getItemListAsync(account, itemIds);

        if(!apiResult.isSuccess()){
            if(apiResult.getErrorMsg() != null){
                msg.put("getDetail"+ batchNo, apiResult.getErrorMsg());
            }
        }else{
            ShopeeCustomResult<String, EsShopeeItem> customResult = apiResult.getResult();
            List<Object> failList = customResult.getFailList();
            if(CollectionUtils.isNotEmpty(failList)){
                msg.put("getDetail"+ batchNo, failList);
            }

            List<EsShopeeItem> successList = customResult.getSuccessList();
            //有时候一些产品的父sku是没有的（销售上传的时候没填），这种就不要生成重复刊登的记录
            List<String> parentSkus = successList.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getArticleNumber()))
                    .map(o -> o.getArticleNumber())
                    .distinct().collect(Collectors.toList());
            for (String parentSku : parentSkus) {
                ShopeePublishSkuExample shopeePublishSkuExample = new ShopeePublishSkuExample();
                shopeePublishSkuExample.createCriteria()
                        .andAccountEqualTo(account.getAccountNumber())
                        .andSkuEqualTo(parentSku);
                int count = shopeePublishSkuService.countByExample(shopeePublishSkuExample);
                if(count == 0) {
                    ShopeePublishSku shopeePublishSku = new ShopeePublishSku();
                    shopeePublishSku.setAccount(account.getAccountNumber());
                    shopeePublishSku.setSku(parentSku);
                    shopeePublishSkuService.insert(shopeePublishSku);
                }
            }

            // 保存数据
            this.batchSave(successList);

            //如果是增量更新，要把sku推给产品系统那边做统计用
            /*if(!isFullSync) {
                for (EsShopeeItem shopeeItem : successList) {
                    //单属性商品推主sku，多属性商品推子sku
                    if(!shopeeItem.getHasVariation().equals(shopeeItem.getIsFather()) && StringUtils.isNotBlank(shopeeItem.getArticleNumber())) {
                        PublishRedisClusterUtils.sAdd(RedisKeyConstant.PUBLISH_PRODUCT_STATISTICS, shopeeItem.getArticleNumber().toUpperCase());
                    }
                }
            }*/
        }
    }

    /**
     * 权限控制
     * @param query
     */
    @Override
    public void itemAuth(EsShopeeItemRequest query) {
        // 避免分页查询时候重复调用授权
        if(BooleanUtils.isFalse(query.getNeedSearchAuth())) {
            return;
        }

        /*权限控制----start*/
        //如果入参店铺为空且不是超管或最高权限者，则只查询当前登录人的店铺
        CQueryResult<EsShopeeItem> cQueryResult = new CQueryResult<>();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        List<String> authorAccountList = new ArrayList<>();
        //销售
        String salemanager = query.getSalemanager();
        String salemanagerLeader = query.getSalemanagerLeader();
        String salesSupervisorName = query.getSalesSupervisorName();

        if (StringUtils.isBlank(query.getItemSellerStr()) && CollectionUtils.isEmpty(query.getItemSellerList()) && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_SHOPEE, false);
            if (!authorAccountListResult.isSuccess()) {
                throw new RuntimeException(authorAccountListResult.getErrorMsg());
            }
            //查询销售对应店铺列表
            if (CollectionUtils.isEmpty(authorAccountListResult.getResult())) {
                throw new RuntimeException("未查询到可用店铺列表！");
            }
            authorAccountList.addAll(authorAccountListResult.getResult());
        }

        EsSaleAccountRequest request = new EsSaleAccountRequest();

        //有选择销售
        if(StringUtils.isNotBlank(salemanager)){
            List<String> saleIds = new ArrayList<>();
            for (String sale : CommonUtils.splitList(salemanager, ",")) {
                //根据销售查询销售信息，获取employeeId
                ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(sale);
                if(!userByNo.isSuccess()){
                    //不抛错误，只打印日志
                    log.error("获取员工信息异常：" + userByNo.getErrorMsg());
                }else{
                    saleIds.add(userByNo.getResult().getEmployeeId().toString());
                }
            }

            if(CollectionUtils.isEmpty(saleIds)){
                throw new RuntimeException("选择的销售查不到用户信息！");
            }
            request.setSaleIds(saleIds);
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
            if(CollectionUtils.isEmpty(accountInfoList)){
                throw new RuntimeException("选择的销售没有权限！");
            }
            //销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if(CollectionUtils.isEmpty(authorAccountList)){
                throw new RuntimeException("选择的销售没有权限！");
            }
        }

        //有选择销售组长
        if(StringUtils.isNotBlank(salemanagerLeader)){
            List<String> strings = CommonUtils.splitList(salemanagerLeader, ",");
            //管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : strings) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(SaleChannel.CHANNEL_SHOPEE, string);
                if(!listApiResult.isSuccess()){
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString())
                        .collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }

            if(CollectionUtils.isEmpty(saleIdsSet)){
                throw new RuntimeException("选择的销售组长查不到下级！");
            }
            request.setSaleIds(new ArrayList<>(saleIdsSet));
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
            if(CollectionUtils.isEmpty(accountInfoList)){
                throw new RuntimeException("选择的销售组长没有权限！");
            }
            //销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if(CollectionUtils.isEmpty(authorAccountList)){
                throw new RuntimeException("选择的销售组长没有权限！");
            }
        }

        //有选择销售主管
        if(StringUtils.isNotBlank(salesSupervisorName)){
            List<String> strings = CommonUtils.splitList(salesSupervisorName, ",");
            //管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : strings) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(SaleChannel.CHANNEL_SHOPEE, string);
                if(!listApiResult.isSuccess()){
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString())
                        .collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }

            if(CollectionUtils.isEmpty(saleIdsSet)){
                throw new RuntimeException("选择的销售主管查不到下级！");
            }

            request.setSaleIds(new ArrayList<>(saleIdsSet));
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
            if(CollectionUtils.isEmpty(accountInfoList)){
                throw new RuntimeException("选择的销售主管没有权限！");
            }
            //销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if(CollectionUtils.isEmpty(authorAccountList)){
                throw new RuntimeException("选择的销售主管没有权限！");
            }
        }

        //设置权限账号
        query.setAuthSellerList(authorAccountList);
        query.setNeedSearchAuth(false);
    }

    @Override
    public void batchUpdateTrialSalePrice(SaleAccountAndBusinessResponse account, String user, ShopeeItemCalcRequest request) {
        EsShopeeItem esShopeeItem = esShopeeItemService.findAllById(request.getId());
        if (ObjectUtils.isEmpty(esShopeeItem)) {
            throw new RuntimeException("updateShopeePrice异常 对应id未查询到对应产品" + request.getId());
        }

        // 创建处理报告信息
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((feedTasktemp) -> {
            feedTasktemp.setAssociationId(esShopeeItem.getId());
            feedTasktemp.setAccountNumber(esShopeeItem.getItemSeller());
            feedTasktemp.setArticleNumber(esShopeeItem.getArticleNumber());
            feedTasktemp.setTaskType(ShopeeFeedTaskEnum.UPDATE_PRICE.getValue());
            feedTasktemp.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTasktemp.setCreatedBy(user);
        });

        try {
            // 获取模板信息
            EsShopeeGlobalTemplateRequest templateRequest = new EsShopeeGlobalTemplateRequest();
            templateRequest.setAccountsStr(esShopeeItem.getItemSeller());
            templateRequest.setGlobalItemId(Long.parseLong(esShopeeItem.getGlobalItemId()));
            templateRequest.setOrderBy("id");
            templateRequest.setSequence("DESC");
            templateRequest.setQueryFields(new String[]{"id", "weight", "shopeeSkusStr", "globalItemId", "accounts"});
            List<EsShopeeGlobalTemplate> shopeeGlobalTemplates = shopeeGlobalTemplateEsService.getEsShopeeGlobalTemplates(templateRequest);
            if (CollectionUtils.isEmpty(shopeeGlobalTemplates)) {
                throw new RuntimeException(String.format("获取模板信息失败,店铺：%s,商品：%s", esShopeeItem.getItemSeller(), esShopeeItem.getGlobalItemId()));
            }

            // 计算原价
            JSONObject originalPriceJson = this.calcOriginalPrice(account, request, shopeeGlobalTemplates.get(0));
            // 记录日志
            Double originalPrice = originalPriceJson.getDouble("originalPrice");
            createOperateLog("BATCH_UPDATE_TRIAL_SALE_PRICE", esShopeeItem.getItemSeller(), esShopeeItem.getArticleNumber(), esShopeeItem.getOriginalPrice().toString(), String.valueOf(originalPrice), originalPriceJson.getString("priceJson"));

            // 在改价之前，如果当前商品参加了活动，先把活动删掉
            if (esShopeeItem.getDiscountId() != null && esShopeeItem.getDiscountId() != 0) {
                ShopeeDiscountCallV2.deleteDiscountItem(account, esShopeeItem);
            }

            // 请求平台接口
            EsShopeeItem changeItem = new EsShopeeItem();
            changeItem.setId(esShopeeItem.getId());
            changeItem.setItemId(esShopeeItem.getItemId());
            changeItem.setOriginalPrice(originalPriceJson.getDouble("originalPrice"));
            changeItem.setVariationId(esShopeeItem.getVariationId());
            ShopeeResponse response = ShopeeUpdateStockPriceCallV2.updatePriceCall(account, changeItem);
            if (StringUtils.isNotBlank(response.getError())) {
                throw new RuntimeException(JSON.toJSONString(response));
            }

            // 修改本地库价格
            EsShopeeItem localShopeeItem = shopeeItemEsService.findAllById(request.getId());
            if (ObjectUtils.isNotEmpty(localShopeeItem)) {
                localShopeeItem.setOriginalPrice(originalPrice);
                localShopeeItem.setPrice(originalPrice);
                localShopeeItem.setLastUpdatedBy(user);
                localShopeeItem.setDiscountRate(request.getDiscountRate());
                localShopeeItem.setLastUpdateDate(new Date());
                shopeeItemEsService.save(localShopeeItem);
            }

            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), String.format("修改前价格：%s，修改后价格：%s", esShopeeItem.getOriginalPrice(), originalPrice));
        } catch (RuntimeException e) {
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
        }

    }

    @Override
    public int scrollQueryExecutorTask(EsShopeeItemRequest request, Consumer<List<EsShopeeItem>> executorTask) {
        return esShopeeItemService.scrollQueryExecutorTask(request, executorTask);
    }

    /**
     * 计算原价
     *
     * @param account
     * @param request
     * @param shopeeGlobalTemplate
     */
    private JSONObject calcOriginalPrice(SaleAccountAndBusinessResponse account, ShopeeItemCalcRequest request, EsShopeeGlobalTemplate shopeeGlobalTemplate) {
        String site = account.getAccountSite();

        try {
            JSONObject jsonObject = new JSONObject();

            // 销售成本价
            ShopeeSku shopeeSku = getShopeeSkuByGlobalTemplate(request, shopeeGlobalTemplate);
            if (ObjectUtils.isEmpty(shopeeSku.getSaleCost())) {
                throw new RuntimeException("销售成本价为空");
            }
            BigDecimal saleCost = BigDecimal.valueOf(shopeeSku.getSaleCost());

            // 包装费
            BigDecimal packingFee = saleCost.multiply(new BigDecimal("0.02"));
            // 发货费(运费)
            Map<String, Object> calcShippingCosMap = calcShippingCost(account, shopeeSku);
            if (ObjectUtils.isEmpty(calcShippingCosMap.get("price"))) {
                throw new RuntimeException("获取运费失败");
            }
            BigDecimal calcShippingCos = new BigDecimal(calcShippingCosMap.get("price").toString());

            // 平台佣金率
            List<AaleTransactionCommissionFormula> paymentCommissionFormula = AccountUtils.paymentCommissionFormula(SaleChannel.CHANNEL_SHOPEE, site);
            if (CollectionUtils.isEmpty(paymentCommissionFormula)) {
                throw new RuntimeException("获取平台佣金率失败，站点：" + site);
            }
            Double paymentFee = paymentCommissionFormula.get(0).getCoefficient();

            // 平台交易费
            List<AaleTransactionCommissionFormula> transactionCommissionFormulas = AccountUtils.saleTransactionCommissionFormula(SaleChannel.CHANNEL_SHOPEE, site);
            if (CollectionUtils.isEmpty(transactionCommissionFormulas)) {
                throw new RuntimeException("获取平台交易费率失败，站点：" + site);
            }
            Double tradeFee = transactionCommissionFormulas.get(0).getCoefficient();

            // 获取费率
            String siteCurrency = ShopeeCountryEnum.getCurrencyByCode(site);
            ApiResult<Double> cnyRateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.CNY, siteCurrency);
            if (!cnyRateResult.isSuccess() || null == cnyRateResult.getResult()) {
                throw new RuntimeException(String.format("%s获取汇率失败：%s", siteCurrency, cnyRateResult.getErrorMsg()));
            }
            Double exchangeRate = cnyRateResult.getResult();

            // 价格(销售成本价+包装费+发货费)
            BigDecimal price = saleCost.add(packingFee).add(calcShippingCos);
            // 利率(1-平台佣金率-平台交易费-毛利率-仓库操作费(2%)-采购运费(2%))
            BigDecimal interestRate = new BigDecimal("1")
                    .subtract(BigDecimal.valueOf(paymentFee))
                    .subtract(BigDecimal.valueOf((tradeFee)))
                    .subtract(BigDecimal.valueOf((request.getProfitMargin())))
                    .subtract(new BigDecimal("0.02"))
                    .subtract(new BigDecimal("0.02"));

            // 判断站点小数位(SG / MY / BR / MX / PL / ES / AR)
            List<String> siteList = List.of("SG", "MY", "BR", "MX", "PL", "ES", "AR");
            int scale = siteList.contains(site) ? 2 : 0;

            // 原价=【（(销售成本价+包装费+发货费)/（1-平台佣金率-平台交易费-毛利率-4%））*汇率】/（1-折扣率）
            Double originalPrice = (price.divide(interestRate, 6, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(exchangeRate))
                    .divide(new BigDecimal("1").subtract(BigDecimal.valueOf((request.getDiscountRate()))), 6, RoundingMode.HALF_UP)).setScale(scale, RoundingMode.HALF_UP).doubleValue();

            String priceJson = String.format("销售成本价：%s, 包装费：%s, 发货费(%s)：%s, 平台佣金率：%s, 平台交易费：%s, 毛利率：%s, 汇率：%s, 折扣率：%s, 小数位：%s", saleCost, packingFee, calcShippingCosMap.get("priceValue"), calcShippingCos, paymentFee, tradeFee, request.getProfitMargin(), exchangeRate, request.getDiscountRate(), scale);
            jsonObject.put("originalPrice", originalPrice);
            jsonObject.put("priceJson", priceJson);
            return jsonObject;
        } catch (RuntimeException e) {
            throw new RuntimeException(String.format("计算原价失败:%s", e.getMessage()));
        }
    }

    /**
     * 根据模板获取对应sku
     *
     * @param request
     * @param shopeeGlobalTemplate
     * @return
     */
    private ShopeeSku getShopeeSkuByGlobalTemplate(ShopeeItemCalcRequest request, EsShopeeGlobalTemplate shopeeGlobalTemplate) {
        List<ShopeeSku> shopeeSkus = JSONArray.parseArray(shopeeGlobalTemplate.getShopeeSkusStr(), ShopeeSku.class);
        List<ShopeeSku> skuList = shopeeSkus.stream().filter(shopeeSku -> shopeeSku.getSku().equals(request.getArticleNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            EsSkuBindRequest skuBindRequest = new EsSkuBindRequest();
            skuBindRequest.setPlatform(SaleChannel.CHANNEL_SHOPEE);
            skuBindRequest.setSystemSku(request.getArticleNumber());
            List<EsSkuBind> skuBinds = esSkuBindService.getEsSkuBinds(skuBindRequest);
            if (CollectionUtils.isEmpty(skuBinds)) {
                throw new RuntimeException("未查询到对应sku信息，sku：" + request.getArticleNumber());
            }
            Map<String, EsSkuBind> skuBindMap = skuBinds.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getBindSku()))
                    .collect(Collectors.toMap(o -> o.getSystemSku(), o -> o, (o1, o2) -> o1));
            if (MapUtils.isEmpty(skuBindMap)) {
                throw new RuntimeException("未查询到对应sku绑定信息，sku：" + request.getArticleNumber());
            }

            EsSkuBind esSkuBind = skuBindMap.get(request.getArticleNumber());
            List<ShopeeSku> bindShopeeSkus = shopeeSkus.stream().filter(shopeeSku -> shopeeSku.getSku().equals(esSkuBind.getBindSku())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bindShopeeSkus)) {
                throw new RuntimeException("未查询到对应sku信息，sku：" + esSkuBind.getBindSku());
            }
            skuList = bindShopeeSkus;
        }
        ShopeeSku shopeeSku = skuList.get(0);
        return shopeeSku;
    }

    /**
     * 计算运费(根据站点+重量取对应运费（重量需要+10g）---->调用物流接口，传参：平台，站点，范本的子SKU重量（注意单位换算），物流返回的运费则为发货费)
     *
     * @param account
     * @param shopeeSku
     * @return
     */
    private Map<String, Object> calcShippingCost(SaleAccountAndBusinessResponse account, ShopeeSku shopeeSku) {
        String site = account.getAccountSite();
        // 物流方式
        Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic(account.getAccountNumber());
        String logisticCode = siteLogisticMap.get(site);
        if (StringUtils.isBlank(logisticCode)) {
            throw new RuntimeException("站点" + site + "没有配置物流信息");
        }

        // 货币
        String currency = ShopeeCountryEnum.getCurrencyByCode(site);
        if (StringUtils.isBlank(currency)) {
            throw new RuntimeException("通过代码获取货币失败");
        }

        // 调用物流计算运费
        CalcShippingCostParam shippingCostParam = new CalcShippingCostParam();
        shippingCostParam.setBuyerCountry(site);
        shippingCostParam.setCurrency(currency);
        shippingCostParam.setShippingMethod(logisticCode);
        shippingCostParam.setSaleChannel(SaleChannelEnum.SHOPEE.getChannelName());
        // 转为g
        Double shippingWeight = shopeeSku.getShippingWeight();
        if (ObjectUtils.isEmpty(shippingWeight)) {
            throw new RuntimeException("产品重量为空");
        }
        shippingCostParam.setTotalWeight(shippingWeight + 10);

        ApiResult<CalcShippingCostResult> calcShippingCostResultApiResult = LogisticsClientHelper.calcShippingCostApiResult(shippingCostParam);
        if (!calcShippingCostResultApiResult.isSuccess()) {
            throw new RuntimeException("物流计算失败：" + calcShippingCostResultApiResult.getErrorMsg());
        }

        // 运费价格信息
        BigDecimal price = BigDecimal.valueOf(calcShippingCostResultApiResult.getResult().getPrice());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("price", price);
        dataMap.put("priceValue", String.format("站点：%s，重量：%s，运费：%s", site, shippingCostParam.getTotalWeight(), price));
        return dataMap;
    }

    /**
     * 创建操作日志
     *
     * @param type
     * @param text
     * @param account
     * @return
     */
    private void createOperateLog(String type, String account, String articleNumber, String before, String after, String text) {
        ShopeeOperateLog operateLog = new ShopeeOperateLog();
        operateLog.setType(type);
        operateLog.setFieldName(articleNumber);
        operateLog.setBusinessId(account);
        operateLog.setAfter(after);
        operateLog.setBefore(before);
        operateLog.setMessage(text);
        operateLog.setCreateBy("admin");
        operateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        shopeeOperateLogService.insert(operateLog);
    }

    /**
     * 处理扩展字段信息
     * @param esShopeeItems
     * @param query
     * @return
     */
    private Map<String, ShopeeItemEsExtend> handelPageExtend(List<EsShopeeItem> esShopeeItems, Map<String, ShopeeItemEsExtend> extendMap, EsShopeeItemRequest query) {
        if(null == extendMap) {
            extendMap = new HashMap<>();
        }
        if(CollectionUtils.isEmpty(esShopeeItems) || BooleanUtils.isFalse(query.getQueryExtend())) {
            return extendMap;
        }

        // 销售
        List<String> accountNumberList =  esShopeeItems.stream().map(EsShopeeItem::getItemSeller).distinct().collect(Collectors.toList());
        Map<String, Triple<String, String, String>> salesmanAccountDetailMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SHOPEE);
        // 平台类目名称
        List<Integer> categoryIdList = esShopeeItems.stream().filter(o->null != o.getCategoryId()).map(EsShopeeItem::getCategoryId).distinct().collect(Collectors.toList());
        Map<String, String> categoryMap = shopeeCategoryV2Service.findCategoryNamePath(categoryIdList);

        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            ShopeeItemEsExtend shopeeItemEsExtend = extendMap.get(esShopeeItem.getId());
            if(null == shopeeItemEsExtend) {
                shopeeItemEsExtend = new ShopeeItemEsExtend();
                extendMap.put(esShopeeItem.getId(), shopeeItemEsExtend);
            }
            if (MapUtils.isNotEmpty(salesmanAccountDetailMap)) {
                // 获取分组销售信息
                if (MapUtils.isNotEmpty(salesmanAccountDetailMap)) {
                    Triple<String, String, String> saleSuperiorTriple = salesmanAccountDetailMap.get(esShopeeItem.getItemSeller());
                    if (saleSuperiorTriple != null) {
                        shopeeItemEsExtend.setSalemanager(saleSuperiorTriple.getLeft());
                        shopeeItemEsExtend.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                        shopeeItemEsExtend.setSalesSupervisorName(saleSuperiorTriple.getRight());
                    }
                }
            }

            if (MapUtils.isNotEmpty(categoryMap)) {
                String categoryName = categoryMap.get(esShopeeItem.getCategoryId() + "_" + esShopeeItem.getSite());
                shopeeItemEsExtend.setCategoryName(categoryName);
            }
        }
        return extendMap;
    }
}
