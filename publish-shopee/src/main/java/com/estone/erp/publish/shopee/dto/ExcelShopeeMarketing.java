package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.StatusConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingConfigTypeConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * shopee 市场营销 excel 实体类
 */
@Data
public class ExcelShopeeMarketing {

    @ExcelProperty(value = "规则名称")
    private String name;

    /**
     * @see com.estone.erp.publish.shopee.enums.ShopeeMarketingConfigTypeEnum
     */
    @ExcelProperty(value = "配置类型", converter = ShopeeMarketingConfigTypeConverter.class)
    private Integer type;

    /**
     * 规格内容 database column shopee_marketing_config.rule_json
     * 实现类继承MarketingConfigParam
     * 手动适配
     * @see com.estone.erp.publish.shopee.component.marking.MarketingConfigParam
     */
    @ExcelProperty(value = "配置规则")
    private String ruleConfig;

    @ExcelProperty(value = "适用店铺")
    private String accounts;

    /**
     * 手动适配
     */
    @ExcelProperty(value = "时间配置")
    private String dateConfig;

    @ExcelProperty(value = "策略开始时间", converter = TimestampFormatConverter.class)
    private Timestamp strategyStartTime;

    @ExcelProperty(value = "策略结束时间", converter = TimestampFormatConverter.class)
    private Timestamp strategyEndTime;

    @ExcelProperty(value = "启用状态", converter = StatusConverter.class)
    private Integer status;

    @ExcelProperty(value = "创建人")
    private String createdBy;

    @ExcelProperty(value = "创建时间", converter = TimestampFormatConverter.class)
    private Timestamp createdTime;

    @ExcelProperty(value = "更新人")
    private String updateBy;

    @ExcelProperty(value = "创建时间", converter = TimestampFormatConverter.class)
    private Timestamp updatedTime;
}
