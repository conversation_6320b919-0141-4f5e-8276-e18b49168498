package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:29
 * @description
 */
@Getter
@Setter
public class TierVariationDtoV2 {

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "option_list")
    private List<OptionDtoV2> optionList = new ArrayList<>();

}
