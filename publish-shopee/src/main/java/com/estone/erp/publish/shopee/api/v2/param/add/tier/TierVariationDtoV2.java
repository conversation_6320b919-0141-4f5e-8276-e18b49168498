package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:29
 * @description
 */
@Getter
@Setter
public class TierVariationDtoV2 {

    /**
     * 必传
     */
    @JSONField(name = "variation_id")
    private Integer variantId;

    /**
     * 非必传
     */
    @JSONField(name = "variation_name")
    private String variantName;

    /**
     * 非必传
     */
    @JSONField(name = "variation_group_id")
    private Integer variantGroupId;

    /**
     * 必传
     */
    @JSONField(name = "variation_option_list")
    private List<OptionDtoV2> variantOptionList = new ArrayList<>();

}
