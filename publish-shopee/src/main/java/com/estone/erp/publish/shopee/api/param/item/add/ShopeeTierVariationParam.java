package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;

public class ShopeeTierVariationParam {

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "options")
    private List<String> options = new ArrayList<>();

    @JSONField(name = "images_url")
    private List<String> imagesUrlList = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getOptions() {
        return options;
    }

    public void setOptions(List<String> options) {
        this.options = options;
    }

    public List<String> getImagesUrlList() {
        return imagesUrlList;
    }

    public void setImagesUrlList(List<String> imagesUrlList) {
        this.imagesUrlList = imagesUrlList;
    }
}
