package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:53
 * @description
 */
@Getter
@Setter
public class ShopeeAttributeDtoV2 {

    @JSONField(name = "attribute_id")
    private Integer attributeId;

    @JSONField(name = "attribute_value_list")
    private List<ShopeeAttributeValueDtoV2> attributeValueList = new ArrayList<>();

}
