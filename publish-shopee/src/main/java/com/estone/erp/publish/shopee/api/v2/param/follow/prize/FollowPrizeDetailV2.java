package com.estone.erp.publish.shopee.api.v2.param.follow.prize;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

@Data
public class FollowPrizeDetailV2 implements RequestCommon {

    @JSONField(name = "campaign_id")
    private Integer campaignId;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_FOLLOW_PRIZE_DETAIL;
    }
}
