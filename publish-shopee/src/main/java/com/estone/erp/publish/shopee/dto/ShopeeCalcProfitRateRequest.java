package com.estone.erp.publish.shopee.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @Auther yucm
 * @Date 2022/3/30
 */
@Getter
@Setter
public class ShopeeCalcProfitRateRequest {

    /**
     * 毛利率 % 使用整数  25%的利润里就传25 不考虑更精准的利率 因返回接口只保留两位小数
     */
    private Integer profitRate;

    /**
     * id集合
     */
    private String idStr;

    /**
     * “=”：eq
     * “>”:gt
     * “>=”:ge
     * “<”:lt
     * “<=”:le
     */
    private String condition;
}
