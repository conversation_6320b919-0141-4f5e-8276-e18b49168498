package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 秒杀报名商品队列
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeMarketingFlashSaleApplyGoodMqConfig {

    private int shopeeMarketingFlashSaleApplyGoodMqConsumers;
    private int shopeeMarketingFlashSaleApplyGoodMqPrefetchCount;
    private boolean shopeeMarketingFlashSaleApplyGoodMqListener;

    @Bean
    public VhQueue shopeeMarketingFlashSaleApplyGoodQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding shopeeMarketingFlashSaleApplyGoodQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_QUEUE, VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_KEY, null);
    }

    @Bean
    public ShopeeMarketingFlashSaleApplyGoodMqListener shopeeMarketingFlashSaleApplyGoodMqListener() {
        return new ShopeeMarketingFlashSaleApplyGoodMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeMarketingFlashSaleApplyGoodListenerContainer(
            ShopeeMarketingFlashSaleApplyGoodMqListener shopeeMarketingFlashSaleApplyGoodMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_QUEUE, shopeeMarketingFlashSaleApplyGoodMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeMarketingFlashSaleApplyGoodMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeMarketingFlashSaleApplyGoodMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeMarketingFlashSaleApplyGoodMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
