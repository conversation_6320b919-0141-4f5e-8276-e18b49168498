package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * shopee上传产品分类属性
 *
 * <AUTHOR>
 */
public class ShopeeAttributeParam {

    /**
     * related to shopee.item.GetAttributes result.attributes.attribute_id
     */
    @JSONField(name = "attributes_id")
    private Integer attributeId;

    /**
     * related to shopee.item.GetAttributes one of result.attributes.options
     */
    @JSONField(name = "value")
    private String value;
    
    private String site;
    
    private String attributeName;
    
    private String inputType;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        ShopeeAttributeParam other = (ShopeeAttributeParam) obj;
        if (attributeId == null) {
            if (other.attributeId != null) {
                return false;
            }
        }
        else if (!attributeId.equals(other.attributeId)) {
            return false;
        }
        if (site == null) {
            if (other.site != null) {
                return false;
            }
        }
        else if (!site.equals(other.site)) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((attributeId == null) ? 0 : attributeId.hashCode());
        result = prime * result + ((site == null) ? 0 : site.hashCode());
        return result;
    }

    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }
}