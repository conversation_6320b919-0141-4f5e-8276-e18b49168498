package com.estone.erp.publish.shopee.mq;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.marking.FlashSaleConfigParam;
import com.estone.erp.publish.shopee.enums.MarketingStatusEnum;
import com.estone.erp.publish.shopee.enums.PublishOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeRecentSalesTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeDiscountItem;
import com.estone.erp.publish.shopee.model.ShopeeDiscountItemExample;
import com.estone.erp.publish.shopee.mq.model.ShopeeFlashSaleGenGoodsDto;
import com.estone.erp.publish.shopee.service.ShopeeDiscountItemService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.shopee.util.ShopeeCalculatedPriceUtil;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeMarketingFlashSale;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeMarketingFlashSaleCanApplyGoodsNew;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeMarketingFlashSaleCanApplyGoodsService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeMarketingFlashSaleService;
import com.estone.erp.publish.tidb.publishtidb.service.UnsalableSkuService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * shopee 秒杀生成可报名数据
 */
@Slf4j
public class ShopeeMarketingFlashSaleApplyGoodMqListener implements ChannelAwareMessageListener {

    private static final Map<Integer, Function<EsShopeeItem, Integer>> VIEW_COUNT_FIELDS;

    static {
        VIEW_COUNT_FIELDS = new HashMap<>();
        VIEW_COUNT_FIELDS.put(7, item -> Optional.ofNullable(item.getView_7d_count()).orElse(0));
        VIEW_COUNT_FIELDS.put(14, item -> Optional.ofNullable(item.getView_14d_count()).orElse(0));
        VIEW_COUNT_FIELDS.put(30, item -> Optional.ofNullable(item.getView_30d_count()).orElse(0));
        VIEW_COUNT_FIELDS.put(60, item -> Optional.ofNullable(item.getView_60d_count()).orElse(0));
        VIEW_COUNT_FIELDS.put(90, item -> Optional.ofNullable(item.getView_90d_count()).orElse(0));
    }

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Resource
    private ShopeeMarketingFlashSaleCanApplyGoodsService shopeeMarketingFlashSaleCanApplyGoodsService;

    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;

    @Resource
    private SingleItemEsService singleItemEsService;

    @Resource
    private ShopeeDiscountItemService shopeeDiscountItemService;

    @Resource
    private ShopeeMarketingFlashSaleService shopeeMarketingFlashSaleService;

    @Resource
    private UnsalableSkuService unsalableSkuService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("ShopeeMarketingFlashSaleApplyGoodMqListener body to String is null");
            }

            boolean sign = false;
            try {
                TimeUnit.MINUTES.sleep(3);
                ShopeeFlashSaleGenGoodsDto dto = JSON.parseObject(body, new TypeReference<>() {
                });
                doService(dto);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeMarketingFlashSaleApplyGoodMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeMarketingFlashSaleApplyGoodMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeMarketingFlashSaleApplyGoodMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeMarketingFlashSaleApplyGoodMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    /**
     * 业务处理
     *
     * @param dto
     */
    private void doService(ShopeeFlashSaleGenGoodsDto dto) {
        if (dto == null) {
            log.error("ShopeeMarketingFlashSaleApplyGoodMqListener dto is null");
            return;
        }

        Long taskId = dto.getFlashRecordId();
        ShopeeMarketingFlashSale shopeeMarketingFlashSale = shopeeMarketingFlashSaleService.getById(taskId);
        if (shopeeMarketingFlashSale == null) {
            log.error("ShopeeMarketingFlashSaleApplyGoodMqListener getById is null, {}", dto);
            return;
        }

        exce(shopeeMarketingFlashSale, dto.getProductIdList());
    }

    /**
     * 执行创建，并且还要过滤数据
     */
    public void exce(ShopeeMarketingFlashSale shopeeMarketingFlashSale, List<String> productIdList) {
        Set<Long> searchItemList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            for (String s : productIdList) {
                if (StringUtils.isNotBlank(s)) {
                    searchItemList.add(Long.valueOf(s.trim()));
                }
            }
        }

        String accountNumber = shopeeMarketingFlashSale.getAccountNumber();
        String marketingRuleJson = shopeeMarketingFlashSale.getMarketingRuleJson();
        Long flashRecordId = shopeeMarketingFlashSale.getId();
        FlashSaleConfigParam flashSaleConfigParam = JSON.parseObject(marketingRuleJson, FlashSaleConfigParam.class);

        // 获取店铺仓库
        Boolean saleAccountWarehouse = getSaleAccountWarehouse(accountNumber);

        Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic(accountNumber);

        int limit = 50;
        long gtItemId = 0L;
        List<ShopeeMarketingFlashSaleCanApplyGoodsNew> successList = new ArrayList<>();
        while (true) {
            Set<Long> errorItemSet = new HashSet<>();

            List<Long> list = shopeeMarketingFlashSaleCanApplyGoodsService.getItemIdList(flashRecordId, gtItemId, limit, searchItemList);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            gtItemId = list.get(list.size() - 1);
            // 只需要查询这些

            LambdaQueryWrapper<ShopeeMarketingFlashSaleCanApplyGoodsNew> canApplyGoodsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            canApplyGoodsLambdaQueryWrapper
                    .eq(ShopeeMarketingFlashSaleCanApplyGoodsNew::getFlashRecordId, flashRecordId)
                    .in(ShopeeMarketingFlashSaleCanApplyGoodsNew::getItemId, list)
                    .eq(ShopeeMarketingFlashSaleCanApplyGoodsNew::getStatus, 0);

            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> shopeeMarketingFlashSaleCanApplyGoodNews = shopeeMarketingFlashSaleCanApplyGoodsService.list(canApplyGoodsLambdaQueryWrapper);

            if (CollectionUtils.isNotEmpty(shopeeMarketingFlashSaleCanApplyGoodNews)) {
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodNew : shopeeMarketingFlashSaleCanApplyGoodNews) {
                    shopeeMarketingFlashSaleCanApplyGoodNew.setCrawDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodNew.getDiscountPrice());
                    shopeeMarketingFlashSaleCanApplyGoodNew.setUpdatedTime(timestamp);
                }
            }

            EsShopeeItemRequest request = new EsShopeeItemRequest();
            request.setItemSeller(accountNumber);
            request.setIsGoods(true);
            request.setPageSize(10000);
            request.setItemIdList(list.stream().map(String::valueOf).collect(Collectors.toList()));
            request.setQueryFields(new String[]{"id", "variationId", "itemId", "itemSeller", "site", "hasVariation", "itemStatus", "skuStatus",
                    "view_7d_count", "view_14d_count", "view_30d_count", "view_60d_count", "view_90d_count",
                    "order24HCount", "orderLast7dCount", "orderLast14dCount", "orderLast30dCount", "orderNumTotal", "itemSku", "articleNumber", "originalPrice", "price"});
            List<EsShopeeItem> esShopeeItems = new ArrayList<>();
            try {
                List<EsShopeeItem> now = RetryUtil.doRetry(() -> esShopeeItemService.getEsShopeeItems(request), 3);
                if (CollectionUtils.isNotEmpty(now)) {
                    esShopeeItems.addAll(now);
                }
            } catch (Exception e) {
                log.error("ShopeeMarketingFlashSaleApplyGoodMqListener 查询商品在线信息异常：{}", e.getMessage(), e);
            }
            if (CollectionUtils.isEmpty(esShopeeItems)) {
                for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodNew : shopeeMarketingFlashSaleCanApplyGoodNews) {
                    shopeeMarketingFlashSaleCanApplyGoodNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodNew.getCrawDiscountPrice());
                    shopeeMarketingFlashSaleCanApplyGoodNew.setMessage("商品在线列表不存在或非NORMAL状态");
                }
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(shopeeMarketingFlashSaleCanApplyGoodNews);
            }

            Map<Long, List<ShopeeMarketingFlashSaleCanApplyGoodsNew>> collect = shopeeMarketingFlashSaleCanApplyGoodNews.stream().collect(Collectors.groupingBy(ShopeeMarketingFlashSaleCanApplyGoodsNew::getItemId));

            // 过滤最近七天涨价的item数据（ES-10753）
            Map<String, List<EsShopeeItem>> existItemList = esShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
            Iterator<Map.Entry<String, List<EsShopeeItem>>> iterator = existItemList.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, List<EsShopeeItem>> entry = iterator.next();
                String itemId = entry.getKey();
                List<EsShopeeItem> shopeeItems = entry.getValue();
                try {
                    LocalDate endDate = LocalDate.now();
                    LocalDate startDate = endDate.minusDays(7);
                    List<String> itemList = shopeeItems.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
                    List<String> feedTaskUpdatePricePrice = ShopeeFeedTaskHandleUtil.getFeedTaskUpdatePricePrice(ShopeeFeedTaskEnum.UPDATE_PRICE.getValue(), accountNumber, itemId, itemList, startDate, endDate);
                    if (CollectionUtils.isNotEmpty(feedTaskUpdatePricePrice)) {
                        // 保存日志
                        List<ShopeeMarketingFlashSaleCanApplyGoodsNew> shopeeMarketingFlashSaleCanApplyGoodsNews = collect.get(Long.valueOf(itemId));
                        if (CollectionUtils.isNotEmpty(shopeeMarketingFlashSaleCanApplyGoodsNews)) {
                            for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodNew : shopeeMarketingFlashSaleCanApplyGoodsNews) {
                                shopeeMarketingFlashSaleCanApplyGoodNew.setStatus(3);
                                shopeeMarketingFlashSaleCanApplyGoodNew.setMessage("过滤最近七天涨价的item数据");
                                shopeeMarketingFlashSaleCanApplyGoodNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodNew.getCrawDiscountPrice());
                            }
                            shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(shopeeMarketingFlashSaleCanApplyGoodsNews);
                        }

                        // 移除该item
                        iterator.remove();
                    }
                } catch (Exception e) {
                    log.error("ShopeeMarketingFlashSaleApplyGoodMqListener 过滤最近七天涨价的item数据失败：{}", e.getMessage(), e);
                }
            }
            if (MapUtils.isEmpty(existItemList)) {
                continue;
            }

            List<String> skuList = existItemList.values().stream().flatMap(Collection::stream).map(EsShopeeItem::getArticleNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            Map<String, Integer> allSkuAndStatusMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuList)) {
                SingleItemEsRequest singleItemEsRequest = new SingleItemEsRequest();
                singleItemEsRequest.setSkuList(skuList);
                try {
                    List<SingleItemEs> singleItemEsList = RetryUtil.doRetry(() -> singleItemEsService.getSingleItemEsList(singleItemEsRequest), 3);
                    if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                        Map<String, Integer> skuAndStatusMap = singleItemEsList.stream().filter(a -> a.getItemStatus() != null).collect(Collectors.toMap(SingleItemEs::getSonSku, SingleItemEs::getItemStatus, (oldV, newV) -> newV));
                        allSkuAndStatusMap.putAll(skuAndStatusMap);
                    }
                } catch (Exception e) {
                    log.error("ShopeeMarketingFlashSaleApplyGoodMqListener 查询单品状态异常：{}", shopeeMarketingFlashSale, e);
                }
            }

            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> callPriceList = new ArrayList<>();

            // 1. 过滤基础数据 销量区间 单品状态
            filterData(accountNumber, flashSaleConfigParam, collect, existItemList, allSkuAndStatusMap, callPriceList, errorItemSet, saleAccountWarehouse);

            // 2. 过滤计算数据，过滤毛利率, 毛利率报错的要记录下来
            filterProfit(accountNumber, callPriceList, flashSaleConfigParam, siteLogisticMap, errorItemSet);
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> errorCall = callPriceList.stream().filter(a -> a.getStatus() != 0).collect(Collectors.toList());
            callPriceList = callPriceList.stream().filter(a -> a.getStatus() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorCall)) {
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(errorCall, 300);
            }

            if (CollectionUtils.isEmpty(callPriceList)) {
                continue;
            }
            // 3. 过滤折扣价格计算，计算商品的价格  还要判断活动商品的价格是否符合规则，折扣限制 大于等于5%
            callProfit(accountNumber, callPriceList, flashSaleConfigParam, siteLogisticMap, errorItemSet);
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> errorCall2 = callPriceList.stream().filter(a -> a.getStatus() != 0).collect(Collectors.toList());
            callPriceList = callPriceList.stream().filter(a -> a.getStatus() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorCall2)) {
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(errorCall2, 300);
            }
            if (CollectionUtils.isEmpty(callPriceList)) {
                continue;
            }
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> errorList = callPriceList.stream().filter(a -> errorItemSet.contains(a.getItemId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorList)) {
                for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew : errorList) {
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("整个item存在不符合规则的SKU");
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());

                }
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(errorList, 300);
            }
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> nowSuccessList = callPriceList.stream().filter(a -> !errorItemSet.contains(a.getItemId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nowSuccessList)) {
                continue;
            }
            // 不为空，开始保存
            for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew : nowSuccessList) {
                shopeeMarketingFlashSaleCanApplyGoodsNew.setFlashSaleStock(flashSaleConfigParam.getStock());
                shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(1);
            }
            successList.addAll(nowSuccessList);
        }
        if (CollectionUtils.isEmpty(successList)) {
            shopeeMarketingFlashSale.setSubmitStatus(MarketingStatusEnum.FAILED.getCode());
            shopeeMarketingFlashSale.setSubmitTime(new Timestamp(System.currentTimeMillis()));
            shopeeMarketingFlashSale.setSubmitRemark("没有符合条件的商品");
            shopeeMarketingFlashSale.setPublishOperatorStatus(PublishOperatorStatusEnum.SUCCESS.getCode());
            shopeeMarketingFlashSale.setPublishOperatorStatusUpdateTime(new Timestamp(System.currentTimeMillis()));
            shopeeMarketingFlashSaleService.updateById(shopeeMarketingFlashSale);
        } else {
            shopeeMarketingFlashSale.setSubmitStatus(MarketingStatusEnum.WAITING_EXE.getCode());
            shopeeMarketingFlashSale.setPublishOperatorStatus(PublishOperatorStatusEnum.SUCCESS.getCode());
            shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(successList, 300);
            shopeeMarketingFlashSaleService.updateById(shopeeMarketingFlashSale);
        }
    }

    /**
     * 库存要适当的减少
     *
     * @param goods
     * @return
     */
    private static Integer getStock(ShopeeMarketingFlashSaleCanApplyGoodsNew goods, FlashSaleConfigParam flashSaleConfigParam) {
        Integer stock1 = flashSaleConfigParam.getStock();
        if (stock1 != null) {
            return stock1;
        }

        Integer stock = goods.getStock();
        if (stock == null) {
            return null;
        }
        if (stock == 1) {
            return stock;
        }
        if (stock > 999) {
            stock = 999;
        }
        if (stock < 10 && stock > 2) {
            return stock - 1;
        }
        if (stock > 200) {
            stock = stock - RandomUtils.nextInt(20, 100);
        } else {
            stock = stock - RandomUtils.nextInt(0, 10);
        }
        if (stock < 0) {
            return 0;
        }
        return stock;
    }

    private void filterData(String accountNumber, FlashSaleConfigParam flashSaleConfigParam, Map<Long, List<ShopeeMarketingFlashSaleCanApplyGoodsNew>> collect,
                            Map<String, List<EsShopeeItem>> existItemList, Map<String, Integer> allSkuAndStatusMap, List<ShopeeMarketingFlashSaleCanApplyGoodsNew> callPriceList,
                            Set<Long> errorItemIdSet, Boolean saleAccountWarehouse) {
        List<String> skuStatusList = Optional.ofNullable(flashSaleConfigParam.getSkuStatusList()).orElse(new ArrayList<>());

        for (Map.Entry<Long, List<ShopeeMarketingFlashSaleCanApplyGoodsNew>> entry : collect.entrySet()) {
            Long itemId = entry.getKey();
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> value = entry.getValue();
            Set<String> allModelIdSet = value.stream().map(ShopeeMarketingFlashSaleCanApplyGoodsNew::getModelId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
            // 在线列表没有符合条件的跳过
            if (!existItemList.containsKey(itemId.toString())) {
                for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodNew : value) {
                    if (shopeeMarketingFlashSaleCanApplyGoodNew.getStatus() != null && shopeeMarketingFlashSaleCanApplyGoodNew.getStatus() == 3) {
                        continue;
                    }
                    shopeeMarketingFlashSaleCanApplyGoodNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodNew.setMessage("商品在线列表不存在或非NORMAL状态");
                    shopeeMarketingFlashSaleCanApplyGoodNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodNew.getCrawDiscountPrice());
                    errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodNew.getItemId());
                }
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(value);
                continue;
            }
            boolean isHasVariation = false;
            List<EsShopeeItem> itemIdList = existItemList.get(itemId.toString());
            EsShopeeItem esShopeeItem1 = itemIdList.get(0);
            if (BooleanUtils.isTrue(esShopeeItem1.getHasVariation())) {
                isHasVariation = true;
            }

            Map<String, EsShopeeItem> variationIdAndEsShopeeItemMap = itemIdList.stream().filter(a -> StringUtils.isNotBlank(a.getVariationId())).collect(Collectors.toMap(EsShopeeItem::getVariationId, a -> a, (oldV, newV) -> newV));
            // 过滤商品和产品相关信息, modelId 为空时为 itemId，避免重复数据，modelId 判断跑过没有
            Set<Long> runModelIdSet = new HashSet<>();
            for (ShopeeMarketingFlashSaleCanApplyGoodsNew marketingFlashSaleCanApplyGoods : value) {
                if (marketingFlashSaleCanApplyGoods.getModelId() != null && runModelIdSet.contains(marketingFlashSaleCanApplyGoods.getModelId())) {
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setMessage("重复数据：modelId:" + marketingFlashSaleCanApplyGoods.getModelId());
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());

                    continue;
                }
                if (marketingFlashSaleCanApplyGoods.getModelId() != null) {
                    runModelIdSet.add(marketingFlashSaleCanApplyGoods.getModelId());
                }
                EsShopeeItem esShopeeItem = getItem(itemIdList, variationIdAndEsShopeeItemMap, marketingFlashSaleCanApplyGoods);
                if (esShopeeItem == null) {
                    marketingFlashSaleCanApplyGoods.setMessage("商品在线列表不存在或非NORMAL状态");
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());

                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }
                String articleNumber = esShopeeItem.getArticleNumber();
                marketingFlashSaleCanApplyGoods.setSku(articleNumber);
                boolean checkStock = checkStock(marketingFlashSaleCanApplyGoods, flashSaleConfigParam);
                if (!checkStock) {
                    marketingFlashSaleCanApplyGoods.setMessage("库存不符合");
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }
                boolean checkRecent = checkRecentSales(itemIdList, isHasVariation, allModelIdSet, flashSaleConfigParam);
                if (!checkRecent) {
                    Integer i = checkRecentSalesNumber(itemIdList, flashSaleConfigParam);
                    marketingFlashSaleCanApplyGoods.setMessage("销量不符合，销量：" + i);
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }

                boolean checkViewCount = checkViewCount(itemIdList, isHasVariation, allModelIdSet, flashSaleConfigParam);
                if (!checkViewCount) {
                    Function<EsShopeeItem, Integer> viewCountExtractor = VIEW_COUNT_FIELDS.get(flashSaleConfigParam.getViewCount());
                    marketingFlashSaleCanApplyGoods.setMessage("访问量不符合，访问量：" + viewCountExtractor.apply(itemIdList.get(0)));
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }

                List<Integer> unsalableLevels = flashSaleConfigParam.getUnsalableLevels();
                if (CollectionUtils.isNotEmpty(unsalableLevels)) {
                    if (Objects.isNull(saleAccountWarehouse)) {
                        marketingFlashSaleCanApplyGoods.setMessage("未找到卖家账号是否南宁仓");
                        marketingFlashSaleCanApplyGoods.setStatus(3);
                        marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                        errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                        continue;
                    }

                    Map<String, Integer> skuUnsalableLevelMap = unsalableSkuService.getSkuUnsalableLevelMap(Lists.newArrayList(articleNumber));
                    int type = saleAccountWarehouse ? 3 : 1;
                    Integer orDefault = skuUnsalableLevelMap.getOrDefault(articleNumber + "_" + type, 0);
                    if (!unsalableLevels.contains(orDefault)) {
                        marketingFlashSaleCanApplyGoods.setMessage("是否滞销不符合");
                        marketingFlashSaleCanApplyGoods.setStatus(3);
                        marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                        errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                        continue;
                    }
                }


                Integer skuStatus = allSkuAndStatusMap.get(articleNumber);
                if (CollectionUtils.isEmpty(skuStatusList)) {
                    continue;
                }
                if (skuStatus == null) {
                    marketingFlashSaleCanApplyGoods.setMessage("sku产品系统不存在");
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }
                String enNameByCode = SingleItemEnum.getEnNameByCode(skuStatus);
                if (StringUtils.isBlank(enNameByCode)) {
                    marketingFlashSaleCanApplyGoods.setMessage("sku状态不符合:" + skuStatus);
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                    continue;
                }
                if (!skuStatusList.contains(enNameByCode)) {
                    marketingFlashSaleCanApplyGoods.setMessage("sku状态不符合:" + skuStatus);
                    marketingFlashSaleCanApplyGoods.setStatus(3);
                    marketingFlashSaleCanApplyGoods.setDiscountPrice(marketingFlashSaleCanApplyGoods.getCrawDiscountPrice());
                    errorItemIdSet.add(marketingFlashSaleCanApplyGoods.getItemId());
                }
            }

            // 计算毛利率的数据
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> collect1 = value.stream().filter(a -> a.getStatus() == 0).collect(Collectors.toList());
            List<ShopeeMarketingFlashSaleCanApplyGoodsNew> errorList = value.stream().filter(a -> a.getStatus() != 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorList)) {
                shopeeMarketingFlashSaleCanApplyGoodsService.updateBatchById(errorList);
            }

            if (CollectionUtils.isEmpty(collect1)) {
                continue;
            }
            callPriceList.addAll(collect1);
        }
    }

    private EsShopeeItem getItem(List<EsShopeeItem> itemIdList, Map<String, EsShopeeItem> variationIdAndEsShopeeItemMap, ShopeeMarketingFlashSaleCanApplyGoodsNew marketingFlashSaleCanApplyGoods) {
        // 判断是变体还是其他
        Long modelId = marketingFlashSaleCanApplyGoods.getModelId();
        if (modelId == null || modelId.equals(marketingFlashSaleCanApplyGoods.getItemId())) {
            return itemIdList.get(0);
        } else {
            EsShopeeItem esShopeeItem = variationIdAndEsShopeeItemMap.get(modelId.toString());
            if (esShopeeItem == null) {
                if (itemIdList.size() == 1) {
                    Boolean hasVariation = itemIdList.get(0).getHasVariation();
                    if (hasVariation != null && !hasVariation) {
                        esShopeeItem = itemIdList.get(0);
                    }
                }
            }
            return esShopeeItem;
        }
    }

    private boolean checkStock(ShopeeMarketingFlashSaleCanApplyGoodsNew marketingFlashSaleCanApplyGoods, FlashSaleConfigParam flashSaleConfigParam) {
        Integer stockFrom = flashSaleConfigParam.getStockFrom();
        Integer stockTo = flashSaleConfigParam.getStockTo();
        if (stockFrom != null || stockTo != null) {
            if (marketingFlashSaleCanApplyGoods.getStock() == null) {
                return false;
            }
            if (stockFrom != null && marketingFlashSaleCanApplyGoods.getStock() < stockFrom) {
                return false;
            }
            if (stockTo != null && marketingFlashSaleCanApplyGoods.getStock() >= stockTo) {
                return false;
            }
            if (marketingFlashSaleCanApplyGoods.getStock() <= 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查销量
     */
    private boolean checkRecentSales(List<EsShopeeItem> esShopeeItems, boolean isHasVariation, Set<String> allModelIdSet, FlashSaleConfigParam flashSaleConfigParam) {
        String recentType = flashSaleConfigParam.getRecentType();
        Integer recentSalesFrom = flashSaleConfigParam.getRecentSalesFrom();
        Integer recentSalesTo = flashSaleConfigParam.getRecentSalesTo();
        ShopeeRecentSalesTypeEnum recentSalesTypeEnum = ShopeeRecentSalesTypeEnum.getByCode(recentType);
        if (recentSalesTypeEnum == null) {
            return true;
        }
        List<EsShopeeItem> list = new ArrayList<>();
        if (isHasVariation) {
            List<EsShopeeItem> collect = esShopeeItems.stream().filter(a -> StringUtils.isNotBlank(a.getVariationId())).filter(a -> allModelIdSet.contains(a.getVariationId())).collect(Collectors.toList());
            list.addAll(collect);
        } else {
            list.add(esShopeeItems.get(0));
        }

        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.H24) {
            Integer collect = list.stream().map(EsShopeeItem::getOrder24HCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
            return checkSales(collect, recentSalesFrom, recentSalesTo);
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D7) {
            Integer collect = list.stream().map(EsShopeeItem::getOrderLast7dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
            return checkSales(collect, recentSalesFrom, recentSalesTo);
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D14) {
            Integer collect = list.stream().map(EsShopeeItem::getOrderLast14dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
            return checkSales(collect, recentSalesFrom, recentSalesTo);
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D30) {
            Integer collect = list.stream().map(EsShopeeItem::getOrderLast30dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
            return checkSales(collect, recentSalesFrom, recentSalesTo);
        }
        return false;
    }

    private Integer checkRecentSalesNumber(List<EsShopeeItem> esShopeeItems, FlashSaleConfigParam flashSaleConfigParam) {
        String recentType = flashSaleConfigParam.getRecentType();
        ShopeeRecentSalesTypeEnum recentSalesTypeEnum = ShopeeRecentSalesTypeEnum.getByCode(recentType);
        if (recentSalesTypeEnum == null) {
            return null;
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.H24) {
            return esShopeeItems.stream().map(EsShopeeItem::getOrder24HCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D7) {
            return esShopeeItems.stream().map(EsShopeeItem::getOrderLast7dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D14) {
            return esShopeeItems.stream().map(EsShopeeItem::getOrderLast14dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        }
        if (recentSalesTypeEnum == ShopeeRecentSalesTypeEnum.D30) {
            return esShopeeItems.stream().map(EsShopeeItem::getOrderLast30dCount).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        }
        return null;
    }

    /**
     * 检查访问量
     */
    private boolean checkViewCount(List<EsShopeeItem> esShopeeItems, boolean isHasVariation, Set<String> allModelIdSet, FlashSaleConfigParam flashSaleConfigParam) {
        // 过滤最近X天访问量(如果为null当0处理),左闭右开
        Integer viewCount = flashSaleConfigParam.getViewCount();
        Integer viewCountFrom = flashSaleConfigParam.getViewCountFrom();
        Integer viewCountTo = flashSaleConfigParam.getViewCountTo();
        if (viewCount == null || viewCountFrom == null || viewCountTo == null) {
            return true;
        }

        List<EsShopeeItem> list = new ArrayList<>();
        if (isHasVariation) {
            List<EsShopeeItem> collect = esShopeeItems.stream().filter(a -> StringUtils.isNotBlank(a.getVariationId())).filter(a -> allModelIdSet.contains(a.getVariationId())).collect(Collectors.toList());
            list.addAll(collect);
        } else {
            list.add(esShopeeItems.get(0));
        }

        // 获取对应周期的访问量
        Function<EsShopeeItem, Integer> viewCountExtractor = VIEW_COUNT_FIELDS.get(viewCount);
        if (viewCountExtractor == null) {
            return true;
        }
        // 获取一个计算访问量
        EsShopeeItem esShopeeItem = list.get(0);
        Integer viewNumber = viewCountExtractor.apply(esShopeeItem);
        return viewNumber >= viewCountFrom && viewNumber < viewCountTo;
    }

    private boolean checkSales(Integer count, Integer recentSalesFrom, Integer recentSalesTo) {
        if (recentSalesFrom != null || recentSalesTo != null) {
            if (count == null) {
                return false;
            }
            if (recentSalesFrom != null && count < recentSalesFrom) {
                return false;
            }
            if (recentSalesTo != null && count >= recentSalesTo) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断折扣率，需要判断存在折扣产品
     *
     * @param list
     * @param flashSaleConfigParam
     * @param siteLogisticMap
     */
    public void filterProfit(String accountNumber, List<ShopeeMarketingFlashSaleCanApplyGoodsNew> list, FlashSaleConfigParam flashSaleConfigParam, Map<String, String> siteLogisticMap,
                             Set<Long> errorItemIdSet) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BigDecimal grossMarginFrom = flashSaleConfigParam.getGrossMarginFrom();
        BigDecimal grossMarginTo = flashSaleConfigParam.getGrossMarginTo();

        List<Long> itemIds = list.stream().map(ShopeeMarketingFlashSaleCanApplyGoodsNew::getItemId).distinct().collect(Collectors.toList());
        ShopeeDiscountItemExample discountItemExample = new ShopeeDiscountItemExample();
        // 修改时间为24小时内的
        discountItemExample.createCriteria()
                .andItemIdIn(itemIds)
                .andUploadSuccessEqualTo(true);
        List<ShopeeDiscountItem> discountItems = shopeeDiscountItemService.selectByExample(discountItemExample);
        Map<Long, List<ShopeeDiscountItem>> itemIdAndDiscountItemListMap = discountItems.stream().collect(Collectors.groupingBy(ShopeeDiscountItem::getItemId));

        // 首先，过滤了不符合的商品
        for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew : list) {
            Long itemId = shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId();
            Long modelId = shopeeMarketingFlashSaleCanApplyGoodsNew.getModelId();
            // 优先取折扣活动的价格
            List<ShopeeDiscountItem> shopeeDiscountItems = itemIdAndDiscountItemListMap.get(itemId);
            if (CollectionUtils.isEmpty(shopeeDiscountItems)) {
                shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("不存在折扣活动，无法计算折扣毛利率");
                shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                errorItemIdSet.add(itemId);
                continue;
            }

            // 覆盖折扣价格
            if (modelId == null) {
                // 取最新的一条记录并且取modelId 是空的，取 促销价格不是空的
                filterSingleDiscountItem(shopeeMarketingFlashSaleCanApplyGoodsNew, shopeeDiscountItems, errorItemIdSet);
            } else {
                // shopeeDiscountItems必须不为空，才能执行
                // 这里的modelId可能是单体来着、也可能是变体来着
                // 首先判断所有的折扣活动里面是否有modelId, 如果没有，就认为是单体商品，此时就不需要判断modelId 了，
                // 如果折扣活动里面有modelId，就判断modelId是否相等，相等才能进行秒杀
                List<ShopeeDiscountItem> collect = shopeeDiscountItems.stream().filter(a -> a.getModelId() != null).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    filterSingleDiscountItem(shopeeMarketingFlashSaleCanApplyGoodsNew, shopeeDiscountItems, errorItemIdSet);
                } else {
                    filterModelDiscountItem(shopeeMarketingFlashSaleCanApplyGoodsNew, collect, errorItemIdSet);
                }
            }
        }
        // 过滤掉不符合的商品
        List<ShopeeMarketingFlashSaleCanApplyGoodsNew> collect = list.stream().filter(a -> a.getStatus() == 0).collect(Collectors.toList());
        if (grossMarginFrom != null || grossMarginTo != null) {
            if (CollectionUtils.isEmpty(collect)) {
                return;
            }
            Map<String, String> errorMap = new HashMap<>();
            Map<String, BigDecimal> esIdShopeeItemEsExtendMap = ShopeeCalculatedPriceUtil.calcItemProfitApplyGoods(accountNumber, collect, siteLogisticMap, errorMap);
            for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew : collect) {
                String itemIdAndModelId = shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId() + "-" + shopeeMarketingFlashSaleCanApplyGoodsNew.getModelId();
                BigDecimal profit = esIdShopeeItemEsExtendMap.get(itemIdAndModelId);
                if (profit == null) {
                    String errorMsg = errorMap.get(itemIdAndModelId);
                    errorMsg = StringUtils.isBlank(errorMsg) ? "毛利率计算失败" : errorMsg;
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("算法错误：" + errorMsg);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                    errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
                    continue;
                }
                if (grossMarginFrom != null && profit.compareTo(grossMarginFrom) < 0) {
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("毛利率不符合，毛利率：" + profit);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                    errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
                    continue;
                }
                if (grossMarginTo != null && profit.compareTo(grossMarginTo) >= 0) {
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("毛利率不符合，毛利率：" + profit);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setProfitMargin(profit);
                    shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                    errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
                    continue;
                }
                shopeeMarketingFlashSaleCanApplyGoodsNew.setProfitMargin(profit);
            }
        }
    }

    private void filterModelDiscountItem(ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew, List<ShopeeDiscountItem> shopeeDiscountItems, Set<Long> errorItemIdSet) {
        ShopeeDiscountItem shopeeDiscountItem = shopeeDiscountItems.stream()
                .filter(a -> a.getModelId() != null && a.getModelId().equals(shopeeMarketingFlashSaleCanApplyGoodsNew.getModelId()))
                .filter(a -> a.getItemPromotionPrice() != null)
                .max(Comparator.comparing(ShopeeDiscountItem::getUpdateTime)).orElse(null);
        if (shopeeDiscountItem == null) {
            shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("不存在折扣活动，无法计算折扣毛利率");
            shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
            shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
            errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
            return;
        }
        Double itemPromotionPrice = shopeeDiscountItem.getItemPromotionPrice();
        shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(BigDecimal.valueOf(itemPromotionPrice));
        shopeeMarketingFlashSaleCanApplyGoodsNew.setDbDiscountPrice(BigDecimal.valueOf(itemPromotionPrice));
    }

    /**
     * 过滤单体商品的折扣
     *
     * @param shopeeMarketingFlashSaleCanApplyGoodsNew 秒杀商品
     * @param shopeeDiscountItems                      折扣商品
     */
    private static void filterSingleDiscountItem(ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew,
                                                 List<ShopeeDiscountItem> shopeeDiscountItems, Set<Long> errorItemIdSet) {
        // 就说明这个是规格单品
        ShopeeDiscountItem shopeeDiscountItem = shopeeDiscountItems.stream()
                .filter(a -> a.getItemPromotionPrice() != null && a.getModelId() == null)
                .max(Comparator.comparing(ShopeeDiscountItem::getUpdateTime)).orElse(null);
        if (shopeeDiscountItem == null) {
            shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("不存在折扣活动，无法计算折扣毛利率");
            shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
            errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
            shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
            return;
        }
        Double itemPromotionPrice = shopeeDiscountItem.getItemPromotionPrice();
        shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(BigDecimal.valueOf(itemPromotionPrice));
        shopeeMarketingFlashSaleCanApplyGoodsNew.setDbDiscountPrice(BigDecimal.valueOf(itemPromotionPrice));
    }

    /**
     * 计算毛利率是否符合
     */
    private void callProfit(String accountNumber, List<ShopeeMarketingFlashSaleCanApplyGoodsNew> collect, FlashSaleConfigParam flashSaleConfigParam, Map<String, String> siteLogisticMap, Set<Long> errorItemIdSet) {
        Map<String, String> errorMap = new HashMap<>();
        Map<String, BigDecimal> esIdShopeeItemEsExtendMap = ShopeeCalculatedPriceUtil.calcItemProfitApplyGoodsPrice(accountNumber, collect, siteLogisticMap,
                flashSaleConfigParam.getDiscountProfit().doubleValue(), errorMap);
        for (ShopeeMarketingFlashSaleCanApplyGoodsNew shopeeMarketingFlashSaleCanApplyGoodsNew : collect) {
            String itemIdAndModelId = shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId() + "-" + shopeeMarketingFlashSaleCanApplyGoodsNew.getModelId();
            BigDecimal price = esIdShopeeItemEsExtendMap.get(itemIdAndModelId);
            if (price == null) {
                String errorMsg = errorMap.get(itemIdAndModelId);
                errorMsg = StringUtils.isBlank(errorMsg) ? "毛利率计算失败" : errorMsg;
                shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("算法错误：" + errorMsg);
                shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
                continue;
            }
            BigDecimal discountPrice = shopeeMarketingFlashSaleCanApplyGoodsNew.getDiscountPrice();
            shopeeMarketingFlashSaleCanApplyGoodsNew.setCallPrice(price);
            shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(price);
            // 判断是否价格符合
            if (discountPrice.compareTo(price) < 0) {
                shopeeMarketingFlashSaleCanApplyGoodsNew.setStatus(3);
                shopeeMarketingFlashSaleCanApplyGoodsNew.setMessage("价格不符合,[折扣价：" + discountPrice + "秒杀价：" + price + "]");
                shopeeMarketingFlashSaleCanApplyGoodsNew.setDiscountPrice(shopeeMarketingFlashSaleCanApplyGoodsNew.getCrawDiscountPrice());
                errorItemIdSet.add(shopeeMarketingFlashSaleCanApplyGoodsNew.getItemId());
            }
        }
    }

    /**
     * 获取店铺仓库（true是南宁仓库，false是深圳仓库）
     *
     * @param account
     * @return
     */
    private Boolean getSaleAccountWarehouse(String account) {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, false);
        if (Objects.isNull(saleAccount) || Objects.isNull(saleAccount.getColBool3())) {
            return null;
        }
        return saleAccount.getColBool3();
    }
}
