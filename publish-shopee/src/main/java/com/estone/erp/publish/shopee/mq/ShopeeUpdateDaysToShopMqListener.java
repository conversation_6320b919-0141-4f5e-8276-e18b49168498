package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.dto.UpdateDaysToShipDto;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.handler.ShopeeUpdateDaysToShipHandler;
import com.estone.erp.publish.shopee.model.ShopeePlatformLogistic;
import com.estone.erp.publish.shopee.mq.model.ShopeeNewUpdateDaysToShipDto;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.service.ShopeePlatformLogisticService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * shopee 修改发货天数
 */
@Slf4j
public class ShopeeUpdateDaysToShopMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    @Resource
    private ShopeeUpdateDaysToShipHandler shopeeUpdateDaysToShipHandler;

    @Resource
    private ShopeePlatformLogisticService shopeePlatformLogisticService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            ShopeeNewUpdateDaysToShipDto dto = JSON.parseObject(body, new TypeReference<>() {
            });

            boolean sign = false;
            try {
                Integer type = dto.getType();
                if (type == null) {
                    return;
                }
                if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_3_TO_2.getType())) {
                    updateDaysToShopBy3To2(dto);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_PRE_SALE_TO_SALE.getType())) {
                    updatePreSaleToSale(dto);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_SALE_TO_PRE_SALE.getType())) {
                    updateSaleToPreSale(dto);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_10_TO_7.getType())) {
                    updateDaysToShopBy10To7(dto);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_2_DAY.getType())) {
                    updateDay(dto, 2);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_5_DAY.getType())) {
                    updateDay5(dto, 5);
                } else if (type.equals(ShopeeNewUpdateDaysToShipDto.Type.UPDATE_7_DAY.getType())) {
                    updateDay(dto, 7);
                }
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeUpdateDaysToShopMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeUpdateDaysToShopMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeUpdateDaysToShopMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeUpdateDaysToShopMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    /**
     * 更新为2天
     *
     * @param dto
     */
    private void updateDay(ShopeeNewUpdateDaysToShipDto dto, Integer newDayToShip) {
        List<String> esIdList = dto.getEsIdList();
        if (CollectionUtils.isEmpty(esIdList)) {
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(dto.getAccountNumber());
        request.setIdList(dto.getEsIdList());
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku"});

        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }
        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(newDayToShip);
        }
        Map<String, Integer> stockMap = dto.getStockMap();
        Map<String, Integer> orderTotalMap = dto.getOrderTotalMap();
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip(item, stockMap, orderTotalMap, account), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    /**
     * 更新为5天
     *
     * @param dto
     */
    private void updateDay5(ShopeeNewUpdateDaysToShipDto dto, Integer newDayToShip) {
        List<String> esIdList = dto.getEsIdList();
        if (CollectionUtils.isEmpty(esIdList)) {
            return;
        }
        SaleAccountAndBusinessResponse accountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(dto.getAccountNumber());
        request.setIdList(dto.getEsIdList());
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku", "site"});

        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }
        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(newDayToShip);
        }
        Map<String, Integer> stockMap = dto.getStockMap();
        Map<String, Integer> orderTotalMap = dto.getOrderTotalMap();

        List<ShopeePlatformLogistic> logisticInfo = shopeePlatformLogisticService.getLogisticInfo(dto.getAccountNumber());
        Map<String, Integer> collect = logisticInfo.stream().collect(Collectors.toMap(ShopeePlatformLogistic::getLogisticName, ShopeePlatformLogistic::getLogisticId));
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip5(item, stockMap, orderTotalMap, accountAndBusinessResponse, collect), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    /**
     * 非预售改为预售
     *
     * @param dto
     */
    @Deprecated
    private void updateSaleToPreSale(ShopeeNewUpdateDaysToShipDto dto) {
        String account = dto.getAccountNumber();
        Set<String> itemIdList = dto.getItemIdList();
        SaleAccountAndBusinessResponse accountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(account);
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku"});
        request.setIsFather(true);
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setItemIdList(new ArrayList<>(itemIdList));
        // 发货天数类型 1：小于等于2 2：大于2
        request.setDaysToShipType(1);
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);

        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(7);
        }
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip(item, null, null, accountAndBusinessResponse), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    /**
     * 预售改为非预售
     *
     * @param dto
     */
    @Deprecated
    private void updatePreSaleToSale(ShopeeNewUpdateDaysToShipDto dto) {
        String account = dto.getAccountNumber();
        Set<String> itemIdList = dto.getItemIdList();
        SaleAccountAndBusinessResponse accountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(account);
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku"});
        request.setIsFather(true);
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setItemIdList(new ArrayList<>(itemIdList));
        // 发货天数类型 1：小于等于2 2：大于2
        request.setDaysToShipType(2);
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);

        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(2);
        }
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip(item, null, null, accountAndBusinessResponse), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    @Deprecated
    private void updateDaysToShopBy3To2(ShopeeNewUpdateDaysToShipDto dto) {
        if (StringUtils.isBlank(dto.getAccountNumber()) || CollectionUtils.isEmpty(dto.getItemIdList())) {
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(dto.getAccountNumber());
        // 如果设置了sku
        request.setItemIdList(new ArrayList<>(dto.getItemIdList()));
        request.setIsFather(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setDaysToShip(3);
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku"});
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if (esShopeeItems.isEmpty()) {
            return;
        }
        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(2);
        }
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip(item, null, null, account), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    @Deprecated
    private void updateDaysToShopBy10To7(ShopeeNewUpdateDaysToShipDto dto) {
        if (StringUtils.isBlank(dto.getAccountNumber()) || CollectionUtils.isEmpty(dto.getItemIdList())) {
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, dto.getAccountNumber(), true);

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(dto.getAccountNumber());
        // 如果设置了sku
        request.setItemIdList(new ArrayList<>(dto.getItemIdList()));
        request.setIsFather(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setDaysToShip(10);
        request.setQueryFields(new String[]{"id", "itemId", "daysToShip", "itemSeller", "spu", "itemSeller",
                "variationId", "isFather", "isGoods", "articleNumber", "itemSku"});
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if (esShopeeItems.isEmpty()) {
            return;
        }
        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            esShopeeItem.setOldDaysToShip(esShopeeItem.getDaysToShip());
            esShopeeItem.setDaysToShip(7);
        }
        CompletableFuture.allOf(esShopeeItems.stream().map(item ->
                CompletableFuture.runAsync(() -> updateDaysToShip(item, null, null, account), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
        ).join();
    }

    /**
     * 更新发货天数
     *
     * @param item
     */
    private void updateDaysToShip(EsShopeeItem item, Map<String, Integer> redisStockMap, Map<String, Integer> orderToalMap, SaleAccountAndBusinessResponse account) {
        if (redisStockMap == null) {
            redisStockMap = new HashMap<>();
        }
        if (orderToalMap == null) {
            orderToalMap = new HashMap<>();
        }

        UpdateDaysToShipDto updateDaysToShipDto = new UpdateDaysToShipDto();
        updateDaysToShipDto.setId(item.getId());
        updateDaysToShipDto.setNewDaysToShip(item.getDaysToShip());
        updateDaysToShipDto.setOldDaysToShip(item.getOldDaysToShip());
        updateDaysToShipDto.setItemSeller(item.getItemSeller());
        updateDaysToShipDto.setItemId(item.getItemId());
        updateDaysToShipDto.setModelId(item.getVariationId());
        updateDaysToShipDto.setIsFather(item.getIsFather());
        updateDaysToShipDto.setIsGood(item.getIsGoods());
        updateDaysToShipDto.setSku(item.getArticleNumber());
        updateDaysToShipDto.setItemSku(item.getItemSku());
        updateDaysToShipDto.setRedisStock(redisStockMap.get(item.getId()));
        updateDaysToShipDto.setTotalOrderNumber(orderToalMap.get(item.getId()));
        shopeeUpdateDaysToShipHandler.updateDaysToShip(updateDaysToShipDto, null, account);
    }

    private void updateDaysToShip5(EsShopeeItem item, Map<String, Integer> redisStockMap, Map<String, Integer> orderToalMap, SaleAccountAndBusinessResponse account, Map<String, Integer> logisticNameAndIdMap) {
        if (redisStockMap == null) {
            redisStockMap = new HashMap<>();
        }
        if (orderToalMap == null) {
            orderToalMap = new HashMap<>();
        }
        if (logisticNameAndIdMap == null) {
            logisticNameAndIdMap = new HashMap<>();
        }

        UpdateDaysToShipDto updateDaysToShipDto = new UpdateDaysToShipDto();
        updateDaysToShipDto.setId(item.getId());
        updateDaysToShipDto.setNewDaysToShip(item.getDaysToShip());
        updateDaysToShipDto.setOldDaysToShip(item.getOldDaysToShip());
        updateDaysToShipDto.setItemSeller(item.getItemSeller());
        updateDaysToShipDto.setItemId(item.getItemId());
        updateDaysToShipDto.setModelId(item.getVariationId());
        updateDaysToShipDto.setIsFather(item.getIsFather());
        updateDaysToShipDto.setIsGood(item.getIsGoods());
        updateDaysToShipDto.setSku(item.getArticleNumber());
        updateDaysToShipDto.setItemSku(item.getItemSku());
        updateDaysToShipDto.setRedisStock(redisStockMap.get(item.getId()));
        updateDaysToShipDto.setTotalOrderNumber(orderToalMap.get(item.getId()));
        updateDaysToShipDto.setSite(item.getSite());
        shopeeUpdateDaysToShipHandler.updateDaysToShip(updateDaysToShipDto, account, logisticNameAndIdMap);
    }

}