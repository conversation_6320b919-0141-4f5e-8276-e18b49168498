package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeLogisticParam;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeSizeChartInfoParam;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.param.add.*;
import com.estone.erp.publish.shopee.dto.ShopeeImageDto;
import com.estone.erp.publish.shopee.model.ShopeeAttributeV2;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/25 17:10
 * @description
 */
@Setter
@Getter
public class UpdateItemV2 implements RequestCommon{

    @JSONField(name = "item_id")
    private Long itemId;

    /** 预购设定 */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    @JSONField(name = "item_name")
    private String itemName;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "description_type")
    private String descriptionType;

    @JSONField(name = "description_info")
    private DescriptionInfoV2 descriptionInfo;

    /** 图片 */
    @JSONField(name = "image")
    private ImageDtoV2 image;

    /** 根据不同类别下的特定属性，此字段是可选的（印度尼西亚）*/
    @JSONField(name = "attribute_list")
    private List<ShopeeAttributeDtoV2> attributeList;

    @JSONField(name = "logistic_info")
    private List<ShopeeLogisticParam> logisticInfo;

    @JSONField(name = "size_chart_info")
    private ShopeeSizeChartInfoParam sizeChartInfo;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_ITEM;
    }

    public List<String> getPlatformImageAndBulidImage(List<String> systemImageList,  Map<String, ShopeeImageDto> shopeeImageDtoMap) {
        ImageDtoV2 imageDtoV2 = new ImageDtoV2();
        this.setImage(imageDtoV2);

        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        for (int i = 1; i < systemImageList.size(); i++) {
            if(systemImageList.get(i).equals(systemImageList.get(0))) {
                systemImageList.set(i, "");
            }
        }
        systemImageList = systemImageList.stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
        if (systemImageList.size() > 9) {
            systemImageList = new ArrayList<>(systemImageList.subList(0, 9));
        }
        List<String> platformImageList = new ArrayList<>();
        for (String image : systemImageList) {
            //替换图片为 image_id
            ShopeeImageDto shopeeImageDto = shopeeImageDtoMap.get(image);
            if(shopeeImageDto == null) {
                throw new RuntimeException("图片" + image + "没有获取到对应的平台图片");
            }
            if(StringUtils.isBlank(shopeeImageDto.getImageId())) {
                throw new RuntimeException("图片" + image + "没有获取到对应的平台图片ID");
            }

            platformImageList.add(shopeeImageDto.getImageUrl());
            imageDtoV2.getImageIdList().add(shopeeImageDto.getImageId());
        }
        return platformImageList;
    }

    public void buildAttributeList(String attributesStr) {
        if (StringUtils.isNotEmpty(attributesStr)) {
            List<ShopeeAttributeV2> attributes = JSONArray.parseArray(attributesStr, ShopeeAttributeV2.class);
            if(CollectionUtils.isNotEmpty(attributes)) {
                this.attributeList = new ArrayList<>(attributes.size());
                for (ShopeeAttributeV2 attr : attributes) {
                    ShopeeAttributeDtoV2 attParam = new ShopeeAttributeDtoV2();
                    attParam.setAttributeId(attr.getAttributeId());
                    String attrValueListStr = attr.getAttributeValueList();
                    JSONArray jsonArray = JSON.parseArray(attrValueListStr);
                    for (int i = 0; jsonArray != null && i < jsonArray.size(); i++) {
                        JSONObject valueJson = jsonArray.getJSONObject(i);
                        ShopeeAttributeValueDtoV2 value = JSON.parseObject(valueJson.toJSONString(), new TypeReference<ShopeeAttributeValueDtoV2>() {
                        });
                        attParam.getAttributeValueList().add(value);

                        //如果存在父子结构 也要设置属性,如果有多个只取一个就可以
                        JSONArray parentArray = valueJson.getJSONArray("parent_attribute_list");
                        if (parentArray != null && parentArray.size() > 0) {
                            JSONObject parentJson = parentArray.getJSONObject(0);
                            Integer parent_attribute_id = parentJson.getInteger("parent_attribute_id");
                            Integer parent_value_id = parentJson.getInteger("parent_value_id");
                            ShopeeAttributeDtoV2 parentAttParam = new ShopeeAttributeDtoV2();
                            parentAttParam.setAttributeId(parent_attribute_id);
                            ShopeeAttributeValueDtoV2 parentValue = new ShopeeAttributeValueDtoV2();
                            parentValue.setValueId(parent_value_id);
                            parentAttParam.getAttributeValueList().add(parentValue);
                            if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                                this.attributeList.add(parentAttParam);
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                        this.attributeList.add(attParam);
                    }
                }
            }
        }
    }
}
