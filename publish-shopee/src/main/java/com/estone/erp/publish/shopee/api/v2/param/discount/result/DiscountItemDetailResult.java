package com.estone.erp.publish.shopee.api.v2.param.discount.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/14 16:32
 * @description
 */
@Getter
@Setter
public class DiscountItemDetailResult {

    /** 产品ID */
    @JSONField(name = "item_id")
    private Long itemId;

    /** 变体id */
    @JSONField(name = "model_id")
    private Long modelId;

    /** 如果一个元素命中错误，则指示错误详细信息 */
    @JSONField(name = "fail_message")
    private String failMessage;

    /** 如果一个元素命中错误，则指示错误类型 */
    @JSONField(name = "fail_error")
    private String failError;
}
