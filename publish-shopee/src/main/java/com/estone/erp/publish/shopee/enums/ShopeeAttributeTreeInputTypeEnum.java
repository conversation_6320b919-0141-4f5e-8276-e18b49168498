package com.estone.erp.publish.shopee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 属性树格式类型枚举 原：DROP_DOWN MULTIPLE_SELECT TEXT_FILED COMBO_BOX MULTIPLE_SELECT_COMBO_BOX.
 * <AUTHOR>
 * @Date 2025/6/23 17:07
 */
@Getter
@AllArgsConstructor
public enum ShopeeAttributeTreeInputTypeEnum {

    SINGLE_DROP_DOWN(1, "SINGLE_COMBO_BOX"),
    SINGLE_COMBO_BOX(2, "SINGLE_COMBO_BOX"),
    FREE_TEXT_FIELD(3, "TEXT_FILED"),
    MULTI_DROP_DOWN(4, "MULTI_DROP_DOWN"),
    MULTI_COMBO_BOX(5, "MULTI_COMBO_BOX");

    private final int code;
    private final String value;

    public static String convert(int code) {
        for (ShopeeAttributeTreeInputTypeEnum type : values()) {
            if (type.code == code) {
                return type.value;
            }
        }
        return null;
    }

}
