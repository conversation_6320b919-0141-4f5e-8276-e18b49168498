package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.enums.MakeVideoStatusEnum;
import com.estone.erp.publish.platform.model.MakeVideo;
import com.estone.erp.publish.platform.model.MakeVideoExample;
import com.estone.erp.publish.platform.service.MakeVideoService;
import com.estone.erp.publish.shopee.dto.UpdateGlobalItemVideoRequest;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.system.fms.request.UploadVideoRequest;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: ${监听消费Shopee同步产品}
 * @Auther yucm
 * @Date 2021/11/22
 */
@Slf4j
@Component
public class ShopeeGlobalItemUploadVideoMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeItemEsService shopeeItemEsService;
    @Resource
    private MakeVideoService makeVideoService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            UploadVideoRequest uploadVideoRequest = JSON.parseObject(body, new TypeReference<UploadVideoRequest>() {
            });

            boolean sign = false;
            try {
                doService(uploadVideoRequest);
                sign = true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }finally {
                try {
                    if(sign){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    }else{
                        channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                    }
                }
                catch (IOException ioe) {
                    log.warn(ioe.getMessage(), ioe);
                }
            }
        }
        catch (Exception e) {
            log.error("shopee生成视频url异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.warn(ioe.getMessage(), ioe);
            }
        }
    }

    private void doService(UploadVideoRequest uploadVideoRequest) {
        String spu = uploadVideoRequest.getSpu();
        String videoUrl = uploadVideoRequest.getVideoUrl();
        String message = uploadVideoRequest.getMessage();
        if (StringUtils.isBlank(spu)) {
            return;
        }

        // 查询改SPU 制作中等待中的记录
        MakeVideoExample example = new MakeVideoExample();
        example.createCriteria()
                .andArticleNumberEqualTo(spu)
                .andPlatformEqualTo(SaleChannel.CHANNEL_SHOPEE)
                .andStatusIn(Arrays.asList(MakeVideoStatusEnum.RUNNING.getValue(), MakeVideoStatusEnum.WAITING.getValue()));
        List<MakeVideo> makeVideos = makeVideoService.selectByExample(example);
        for (MakeVideo makeVideo : makeVideos) {
            MakeVideo updateMakeVideo = new MakeVideo();
            updateMakeVideo.setId(makeVideo.getId());
            try {
                if(StringUtils.isNotBlank(message)) {
                    updateMakeVideo.setStatus(MakeVideoStatusEnum.FAIL.getValue());
                    updateMakeVideo.setMessage(message);
                } else if(StringUtils.isBlank(videoUrl)) {
                    updateMakeVideo.setStatus(MakeVideoStatusEnum.FAIL.getValue());
                    updateMakeVideo.setMessage("未获取到错误信息 也未获取到视频地址");
                } else {
                    updateMakeVideo.setStatus(MakeVideoStatusEnum.SUCCESS.getValue());
                }
                updateMakeVideo.setVideoUrl(videoUrl);
                makeVideoService.updateByPrimaryKeySelective(updateMakeVideo);
            }catch (Exception e) {
                log.error("修改制作视频本地记录失败" + makeVideo.getId() + e.getMessage(), e);
            }
            try {
                if (updateMakeVideo.getStatus().equals(MakeVideoStatusEnum.SUCCESS.getValue()) && StringUtils.isNotBlank(makeVideo.getProductId()) && StringUtils.isNotBlank(makeVideo.getAccountNumber())){
                    UpdateGlobalItemVideoRequest request = new UpdateGlobalItemVideoRequest();
                    request.setGlobalItemId(makeVideo.getProductId());
                    request.setSpu(spu);
                    request.setAccountNumber(makeVideo.getAccountNumber());
                    request.setVideoUrl(videoUrl);
                    String userName = "系统自动";
                    shopeeItemEsService.updateGlobalItemVideo(request, userName);
                }
            }catch (Exception e) {
                log.error("视频制后上传失败" + e.getMessage(), e);
            }
        }
    }
}