package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 关注礼物自动创建消息队列配置
 * <AUTHOR>
 * @Date 2024/9/12 下午5:52
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeMarketingFollowPrizeAutoCreateMqConfig {
    private int shopeeMarketingFollowPrizeAutoCreateMqConsumers;
    private int shopeeMarketingFollowPrizeAutoCreateMqPrefetchCount;
    private boolean shopeeMarketingFollowPrizeAutoCreateMqListener;

    @Bean
    public VhQueue shopeeMarketingFollowPrizeAutoCreateQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding shopeeMarketingFollowPrizeAutoCreateQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_QUEUE, VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_KEY, null);
    }

    @Bean
    public ShopeeMarketingFollowPrizeAutoCreateMqListener shopeeMarketingFollowPrizeAutoCreateMqListener() {
        return new ShopeeMarketingFollowPrizeAutoCreateMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeMarketingFollowPrizeAutoListenerContainer(
            ShopeeMarketingFollowPrizeAutoCreateMqListener shopeeMarketingFollowPrizeAutoCreateMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_QUEUE, shopeeMarketingFollowPrizeAutoCreateMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeMarketingFollowPrizeAutoCreateMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeMarketingFollowPrizeAutoCreateMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeMarketingFollowPrizeAutoCreateMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
