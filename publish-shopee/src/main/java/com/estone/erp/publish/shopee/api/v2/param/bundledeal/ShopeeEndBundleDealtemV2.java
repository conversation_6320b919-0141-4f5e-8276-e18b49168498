package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

import java.util.List;
@Data
public class ShopeeEndBundleDealtemV2 implements RequestCommon {
    private Long bundle_deal_id;
    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.END_BUNDLE_DEAL_ITEM;
    }
}
