package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeUpdateItemTitleDescByInfringWordsMqConfig {

    private int shopeeUpdateItemTitleDescByInfringWordsMqConsumers;
    private int shopeeUpdateItemTitleDescByInfringWordsMqPrefetchCount;
    private boolean shopeeUpdateItemTitleDescByInfringWordsMqListener;


    @Bean
    public Queue shopeeUpdateItemTitleDescByInfringWords() {
        return new Queue(PublishQueues.SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_QUEUE);
    }

    @Bean
    public Binding shopeeUpdateItemTitleDescByInfringWordsBinding() {
        return new Binding(PublishQueues.SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_KEY, null);
    }

    @Bean
    public ShopeeUpdateItemTitleDescByInfringWordsMqListener shopeeUpdateItemTitleDescByInfringWordsMqListener() {
        return new ShopeeUpdateItemTitleDescByInfringWordsMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeUpdateItemTitleDescByInfringWordsListenerContainer(
            ShopeeUpdateItemTitleDescByInfringWordsMqListener shopeeUpdateItemTitleDescByInfringWordsMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_QUEUE, shopeeUpdateItemTitleDescByInfringWordsMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
            ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeUpdateItemTitleDescByInfringWordsMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeUpdateItemTitleDescByInfringWordsMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeUpdateItemTitleDescByInfringWordsMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
