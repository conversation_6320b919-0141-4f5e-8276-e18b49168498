package com.estone.erp.publish.shopee.api.v2.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/11 15:09
 * @description
 */
@Getter
@Setter
public class GetCategoryV2 implements RequestCommon {

//    @JSONField(name = "partner_id")
////    private Integer partnerId;
////
////    @JSONField(name = "timestamp")
////    private Long timestamp = System.currentTimeMillis() / 1000;
////
////    @JSONField(name = "access_token")
////    private String accessToken;
////
////    @JSONField(name = "shop_id")
////    private Integer shopId;
////
////    @JSONField(serialize = false)
////    private String apiKey;

    @JSONField(name = "language")
    private String language = "en";


//    public GetCategoryV2(SaleAccountAndBusinessResponse account) {
//        this.partnerId = Integer.valueOf(account.getMerchantId());
//        this.accessToken = account.getAccessToken();
//        this.shopId = Integer.valueOf(account.getShopId());
//        this.apiKey = account.getApiKey();
//        this.language = "en";
//    }

    /**
     * 获取请求路径
     * @return
     */
    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_CATEGORY;
    }

}
