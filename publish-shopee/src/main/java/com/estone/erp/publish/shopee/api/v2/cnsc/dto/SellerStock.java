package com.estone.erp.publish.shopee.api.v2.cnsc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther yucm
 * @Date 2023/7/19
 */
@Getter
@Setter
public class SellerStock {
    @JSONField(name = "location_id")
    private String locationId;

    @JSONField(name = "stock")
    private Integer stock;

    public SellerStock() {}

    public SellerStock(Integer stock, String locationId) {
        this.stock = stock;
        this.locationId = locationId;
    }

    public SellerStock(Integer stock) {
        this.stock = stock;
    }
}
