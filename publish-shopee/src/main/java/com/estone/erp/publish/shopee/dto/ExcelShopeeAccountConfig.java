package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.BooleanCodeConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * shopee 店铺配置导出
 */
@Data
public class ExcelShopeeAccountConfig {

    @ExcelProperty(value = "店铺")
    private String account;

    @ExcelProperty(value = "店铺关联销售")
    private String salemanager;

    @ExcelProperty(value = "店铺关联销售组长")
    private String salemanagerLeader;

    @ExcelProperty(value = "店铺关联销售主管")
    private String salesSupervisorName;

    @ExcelProperty(value = "是否已选择类目", converter = BooleanCodeConverter.class)
    private Boolean isCategorySelected;

    @ExcelProperty(value = "定时上架时间")
    private String publishBeginTime;

    @ExcelProperty(value = "上架间隔时间")
    private Integer timerInterval;

    @ExcelProperty(value = "每天最大刊登数量")
    private Integer maxPublishAmount;

    @ExcelProperty(value = "是否自动刊登新品", converter = BooleanCodeConverter.class)
    private Boolean automaticPublishProduct;

    @ExcelProperty(value ="店铺最大item数量")
    private Integer limitItemCount;

    @ExcelProperty(value = "店铺NORMAL链接总数")
    private Integer itemCount;

    @ExcelProperty(value = "迟发货率")
    private Double deliveryRate;

    @ExcelProperty(value = "迟发货率更新时间", converter = TimestampFormatConverter.class)
    private Timestamp deliveryRateUpdateTime;
}
