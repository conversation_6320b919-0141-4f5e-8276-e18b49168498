package com.estone.erp.publish.shopee.mq;


import com.estone.erp.common.mq.PublishQueues;
import lombok.Data;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 产品系统推送管理单品新状态消息队列配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class ShopeeProductNewStateMqConfig {

    private boolean shopeeProductSingleNewStatusQueueEnable;
    private int shopeeProductSingleNewStatusQueueConsumer;
    private int shopeeProductSingleNewStatusQueuePrefetchCount;

    @Bean
    public Queue productSingleNewStatusShopeeQueue() {
        return new Queue(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_SHOPEE_QUEUE);
    }
    @Bean
    public Binding productSingleNewStatusShopeeQueueBinging(Queue productSingleNewStatusShopeeQueue, FanoutExchange productSingleNewStatusToPublishFanout) {
        return BindingBuilder
                .bind(productSingleNewStatusShopeeQueue)
                .to(productSingleNewStatusToPublishFanout);
    }
    @Bean
    public ShopeeProductNewStateMqListener shopeeProductNewStateMqListener() {
        return new ShopeeProductNewStateMqListener();
    }
    @Bean
    public SimpleMessageListenerContainer shopeeProductNewStateMQListenerContainer(
            ShopeeProductNewStateMqListener shopeeProductNewStateMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (shopeeProductSingleNewStatusQueueEnable) {
            container.setQueueNames(PublishQueues.PRODUCT_SINGLE_NEW_STATUS_SHOPEE_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeProductSingleNewStatusQueuePrefetchCount);
            container.setConcurrentConsumers(shopeeProductSingleNewStatusQueueConsumer);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(shopeeProductNewStateMqListener);
        }
        return container;
    }

}
