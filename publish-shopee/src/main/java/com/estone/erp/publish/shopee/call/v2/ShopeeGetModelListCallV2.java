package com.estone.erp.publish.shopee.call.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.ShopeeVariationOption;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.OptionDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.TierVariationDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.GetModelListV2;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/5/21 10:29
 * @description 获取变体信息
 */
@Slf4j
public class ShopeeGetModelListCallV2 {


    /**
     * @param account
     * @param itemId
     * @return
     */
    public static ApiResult<List<EsShopeeItem>> getItemModelList(SaleAccountAndBusinessResponse account, Long itemId) {
        if (itemId == null) {
            return ApiResult.newError("itemId 为空!");
        }

        List<EsShopeeItem> itemList = new ArrayList<>(0);
        ApiResult<List<EsShopeeItem>> result = ApiResult.newError(null);

        GetModelListV2 param = new GetModelListV2();
        param.setItemId(itemId);

        ShopeeResponse response = ShopeeHttpUtils.doGetV2(account, param);
        if (StringUtils.isNotBlank(response.getError())) {
            result.setErrorMsg(JSON.toJSONString(response));
//            log.info(account.getAccountNumber() + ",获取GetModelList error :" + JSON.toJSONString(response));
        } else {
            JSONObject json = JSON.parseObject(response.getResponse());
            if (json == null) {
                result.setErrorMsg("ModelList结果解析为空: " + JSON.toJSONString(response));
            } else {
                //解析数据
                try {
                    itemList = transModel(account, json, itemId);
                } catch (Exception e) {
                    log.error(String.format("解析[%s] ModelList  出错：", itemId), e);
                }
            }
        }

        result.setResult(itemList);
        result.setSuccess(true);
        return result;
    }

    private static List<EsShopeeItem> transModel(SaleAccountAndBusinessResponse account, JSONObject json, Long itemId) {
        // 获取变体属性和图片
        List<TierVariationDtoV2> tierVariationDtoV2s = transTierVariation(json, itemId);

        List<EsShopeeItem> list = new ArrayList<>();
        JSONArray models = json.getJSONArray("model");
        for (int i = 0; models != null && i < models.size(); i++) {
            JSONObject jsonItem = models.getJSONObject(i);

            EsShopeeItem item = new EsShopeeItem();
            item.setItemSeller(account.getAccountNumber());
            item.setItemId(itemId.toString());
            //促销ID
            item.setDiscountId(jsonItem.getLong("promotion_id"));
            item.setItemSku(jsonItem.getString("model_sku"));
            item.setVariationId(jsonItem.getString("model_id"));

            List<ShopeeVariationOption> variationOptions = new ArrayList<>();
            JSONArray tierIndex = jsonItem.getJSONArray("tier_index");
            for (int j = 0; CollectionUtils.isNotEmpty(tierVariationDtoV2s) && tierIndex != null && j < tierIndex.size(); j++) {
                Integer index = tierIndex.getInteger(j);
                ShopeeVariationOption variationOption = new ShopeeVariationOption();
                variationOptions.add(variationOption);
                variationOption.setIndex(index);

                if(tierVariationDtoV2s.size() <= j) {
                    continue;
                }
                TierVariationDtoV2 tierVariationDtoV2 = tierVariationDtoV2s.get(j);
                if(tierVariationDtoV2 == null) {
                    continue;
                }

                variationOption.setId(tierVariationDtoV2.getVariantId());
                variationOption.setName(tierVariationDtoV2.getVariantName());
                List<OptionDtoV2> optionList = tierVariationDtoV2.getVariantOptionList();
                if(optionList.size() > index) {
                    OptionDtoV2 optionDtoV2 = optionList.get(index);
                    variationOption.setOption(optionDtoV2.getVariationOptionName());

                    String imageUrl = optionDtoV2.getImageUrl();
                    if (StringUtils.isNotBlank(imageUrl)) {
                        variationOption.setImageUrl(imageUrl);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(variationOptions)) {
                item.setVariationOptions(variationOptions);
            }

            JSONArray price_info = jsonItem.getJSONArray("price_info");
            if(price_info != null){
                JSONObject priceJson = price_info.getJSONObject(0);
                item.setOriginalPrice(priceJson.getDouble("original_price"));
                item.setPrice(priceJson.getDouble("current_price"));
                item.setSipItemPrice(priceJson.getDouble("sip_item_price"));
            }

            JSONObject stock_info_v2 = jsonItem.getJSONObject("stock_info_v2");
            if(null != stock_info_v2) {
                JSONObject summary_info = stock_info_v2.getJSONObject("summary_info");
                if(null != summary_info) {
                    item.setStock(summary_info.getInteger("total_available_stock"));
                }
            }

            JSONObject pre_order = jsonItem.getJSONObject("pre_order");
            if(pre_order != null){
                item.setDaysToShip(pre_order.getInteger("days_to_ship"));
            }

            list.add(item);
        }

        return list;
    }

    private static List<TierVariationDtoV2> transTierVariation(JSONObject json, Long itemId) {
        List<TierVariationDtoV2> tierVariationDtoV2s = new ArrayList<>();
        try{
            JSONArray tierVariations = json.getJSONArray("standardise_tier_variation");
            for (int i = 0; tierVariations != null && i < tierVariations.size(); i++) {
                JSONObject tierVariation = tierVariations.getJSONObject(i);

                TierVariationDtoV2 tierVariationDtoV2 = new TierVariationDtoV2();
                tierVariationDtoV2s.add(tierVariationDtoV2);
                tierVariationDtoV2.setVariantName(tierVariation.getString("variation_name"));

                JSONArray optionList = tierVariation.getJSONArray("variation_option_list");
                for (int j = 0; optionList != null && j < optionList.size(); j++) {
                    JSONObject option = optionList.getJSONObject(j);

                    OptionDtoV2 optionDtoV2 = new OptionDtoV2();
                    tierVariationDtoV2.getVariantOptionList().add(optionDtoV2);
                    optionDtoV2.setVariationOptionName(option.getString("variation_option_name"));
                    Optional.ofNullable(option.getString("image_id")).ifPresent(optionDtoV2::setImageId);
                    Optional.ofNullable(option.getString("image_url")).ifPresent(optionDtoV2::setImageUrl);
                }
            }
        }catch (Exception e) {
            log.error(String.format("itemId: %s, tier_variation 转换出错 %s", itemId, e.getMessage()), e);
        }
        return tierVariationDtoV2s;
    }

}