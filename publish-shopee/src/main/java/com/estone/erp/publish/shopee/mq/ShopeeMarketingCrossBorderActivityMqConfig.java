package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhExchange;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 跨境活动配置类
 * <AUTHOR>
 * @Date 2025/4/18 17:55
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeMarketingCrossBorderActivityMqConfig {

    private int shopeeMarketingCrossBorderActivityMqConsumers;
    private int shopeeMarketingCrossBorderActivityMqPrefetchCount;
    private boolean shopeeMarketingCrossBorderActivityMqListener;

    @Bean
    public VhQueue shopeeMarketingCrossBorderActivityQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", 3600000); // 配置是一小时一批数据
        args.put("x-dead-letter-exchange", PublishRabbitMqExchange.SHOPEE_API_LETTER_EXCHANGE);
        args.put("x-dead-letter-routing-key", PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_KEY);
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_QUEUE,
                true, false, false, args);
    }

    @Bean
    public VhBinding shopeeMarketingCrossBorderActivityQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_QUEUE,
                VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_KEY,
                null);
    }

    @Bean
    public VhExchange shopeeMarketingCrossBorderActivityLetterExchange() {
        return new VhExchange(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishRabbitMqExchange.SHOPEE_API_LETTER_EXCHANGE,
                true, false, ExchangeTypes.DIRECT, null);
    }

    @Bean
    public VhQueue shopeeMarketingCrossBorderActivityLetterQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_QUEUE,
                true, false, false, null);
    }

    @Bean
    public VhBinding shopeeMarketingCrossBorderActivityLetterQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_QUEUE,
                VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_LETTER_EXCHANGE,
                PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_KEY,
                null);
    }

    @Bean
    public ShopeeMarketingCrossBorderActivityMqListener shopeeMarketingCrossBorderActivityMqListener() {
        return new ShopeeMarketingCrossBorderActivityMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeMarketingCrossBorderActivityListenerContainer(
            ShopeeMarketingCrossBorderActivityMqListener shopeeMarketingCrossBorderActivityMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_QUEUE, shopeeMarketingCrossBorderActivityMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeMarketingCrossBorderActivityMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeMarketingCrossBorderActivityMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeMarketingCrossBorderActivityMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}