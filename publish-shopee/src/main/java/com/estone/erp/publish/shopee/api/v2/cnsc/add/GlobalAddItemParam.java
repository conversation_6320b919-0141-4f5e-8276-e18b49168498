package com.estone.erp.publish.shopee.api.v2.cnsc.add;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.SellerStock;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.param.add.*;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.enums.ShopeeDescriptionFiledTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeDescriptionTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAttributeV2;
import com.estone.erp.publish.shopee.util.ShopeeDescriptionUtil;
import com.estone.erp.publish.shopee.util.ShopeeGlobalTemplateEsUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:35
 * @description
 */
@Getter
@Setter
public class GlobalAddItemParam implements RequestCommon{

    /** 类别编号*/
    @JSONField(name = "category_id")
    private Integer categoryId ;

    /** 标题*/
    @JSONField(name = "global_item_name")
    private String itemName;

    /** Description of item*/
    @JSONField(name = "description")
    private String description;

    @JSONField(name = "description_info")
    private DescriptionInfoV2 descriptionInfo;

    /**
     * description_type=extended otherwise api will return error.
     * If you don't use this field, you don't need to upload the description_type or upload description_type=normal
     */
    @JSONField(name = "description_type")
    private String descriptionType;

    /** sku */
    @JSONField(name = "global_item_sku")
    private String itemSku;

    /** 图片 */
    @JSONField(name = "image")
    private ImageDtoV2 image = new ImageDtoV2();

    /** Item price*/
    @JSONField(name = "original_price")
    private Double originalPrice;

    /** Item  stock*/
//    @JSONField(name = "normal_stock")
//    private Integer normalStock;

    /** seller_stock of global item*/
    @JSONField(name = "seller_stock")
    private List<SellerStock> sellerStocks;

    /** Weight of item*/
    @JSONField(name = "weight")
    private Double weight;

    /** Item dimension 如果这个字段不为空，那就是必填*/
    @JSONField(name = "dimension")
    private DimensionDtoV2 dimension;

    /** 预购设定 */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    /** Condition of item, could be USED or NEW */
    @JSONField(name = "condition")
    private String condition;

    /** 从视频上传API返回的视频上传ID。 最多只能接受一个 */
    @JSONField(name = "video_upload_id")
    private List<String> videoUploadId;

    /** 品牌 */
    @JSONField(name = "brand")
    private BrandDtoV2 brand;

    /** 根据不同类别下的特定属性，此字段是可选的（印度尼西亚）*/
    @JSONField(name = "attribute_list")
    private List<ShopeeAttributeDtoV2> attributeList;

    @JSONField(name = "size_chart_info")
    private ShopeeSizeChartInfoParamV2 sizeChartInfo;



//    /** 物流渠道设置 */
//    @JSONField(name = "logistic_info")
//    private List<ShopeeLogisticParam> logisticInfo;
//
//    /** Item status, could be UNLIST or NORMAL*/
//    @JSONField(name = "item_status")
//    private String itemStatus;
//
//    /** 批发设置 */
//    @JSONField(name = "wholesale")
//    private List<WholesaleDtoV2> wholesale;



    public GlobalAddItemParam(EsShopeeGlobalTemplate template, Map<String, String> imgMappingMap, List<ShopeeAccountConfig> accountConfigList) {
        //类目id
        this.categoryId = template.getCategoryId();

        //标题和描述
        this.itemName = template.getName();
        //加入标题前缀（最新拉取店铺前缀优先级如下：MY-PH-VN-TH-SG-BR-PL-CO-CL-MX-ID-TW-ES-FR）
        String titlePrefix = ShopeeCountryEnum.getTitlePrefixByAccountSitePriority(accountConfigList);
        if(StringUtils.isNotBlank(titlePrefix)){
            this.itemName = titlePrefix.trim() + " " + this.itemName;
        }
        // --标题不能超过500个字符
        if(this.itemName.length() > 120) {
            this.itemName = this.itemName.substring(0, 120);
        }

        //描述
//        String keyword = StringUtils.isEmpty(template.getKeyword())?"":template.getKeyword() + "\n";
//        this.description = keyword + template.getDescription();
//        this.description = template.getDescription();
        boolean newDesc = false;
        List<DescriptionInfoV2.FiledInfo> filedInfos = ShopeeDescriptionUtil.buildFiled(template.getDescription(), imgMappingMap);
        if (CollectionUtils.isNotEmpty(filedInfos)) {
            long count = filedInfos.stream().filter(a -> Objects.equals(a.getFieldType(), ShopeeDescriptionFiledTypeEnum.IMAGE.getCode())).count();
            if (count > 0) {
                newDesc = true;
            }
        }

        if (newDesc) {
            DescriptionInfoV2.ExtendInfo extendInfo = new DescriptionInfoV2.ExtendInfo();
            extendInfo.setFieldList(filedInfos);
            DescriptionInfoV2 descriptionInfoV2 = new DescriptionInfoV2();
            descriptionInfoV2.setExtendedDescription(extendInfo);
            this.descriptionInfo = descriptionInfoV2;
            this.setDescriptionType(ShopeeDescriptionTypeEnum.EXTENDED.getCode());
        } else {
            this.description = template.getDescription();
            this.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
        }

        //mtsku
        this.itemSku = template.getMtsku();

        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        List<String> imageList = template.getImagesList();
        for (int i = 1; i < imageList.size(); i++) {
            if(imageList.get(i).equals(imageList.get(0))) {
                imageList.set(i, "");
            }
        }
        imageList = imageList.stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
        if (imageList.size() > 9) {
            imageList = new ArrayList<>(imageList.subList(0, 9));
        }
        // --转换图片，把图片转换成阿里云的地址
        for (String image : imageList) {
            //替换图片为 image_id
            String image_id = imgMappingMap.get(image);
            this.image.getImageIdList().add(image_id);
        }

        //价格 库存
        // --如果不是多属性产品的话，直接在这里设置价格
        List<ShopeeSku> shopeeSkus = ShopeeGlobalTemplateEsUtils.parseShopeeSkus(template.getShopeeSkusStr());
        if (shopeeSkus.size() == 1 && StringUtils.equalsIgnoreCase(shopeeSkus.get(0).getSku(), template.getSku())) {
            ShopeeSku sku = shopeeSkus.get(0);
            this.originalPrice = sku.getPrice();
//            this.normalStock = sku.getQuantity();
            this.sellerStocks = Arrays.asList(new SellerStock(sku.getQuantity(), ShopeeConstants.SELLER_STOCK_LOCATION_ID_CNZ));
        }
        else {
            //多维度属性需要另外的接口设置，但是stock和price在这个接口是必须的，所以这里必须设置一个值
            this.originalPrice = 999.0;
//            this.normalStock = 0;
            this.sellerStocks = Arrays.asList(new SellerStock(0, ShopeeConstants.SELLER_STOCK_LOCATION_ID_CNZ));
        }

        //重量
        this.weight = template.getWeight();

        //尺寸 长宽高  要么长宽高都填  要么dimension为null
        this.dimension = JSON.parseObject(template.getDimension(), new TypeReference<DimensionDtoV2>(){});
        if(null != this.dimension && (null == dimension.getPackageLength() || null == dimension.getPackageWidth() || null == dimension.getPackageHeight())) {
            this.dimension = null;
        }
        if(null != this.dimension) { // 平台限制不可以为0 小于1 修改为1
            if(dimension.getPackageLength() != null && dimension.getPackageLength() < 1) {
                dimension.setPackageLength(1);
            }
            if(dimension.getPackageWidth() != null && dimension.getPackageWidth() < 1) {
                dimension.setPackageWidth(1);
            }
            if(dimension.getPackageHeight() != null && dimension.getPackageHeight() < 1) {
                dimension.setPackageHeight(1);
            }
        }

        //发货天数
        // --保证发货天数。预订时，请输入7到30之间的值；对于非预购商品，请排除此字段，该字段将默认为您所在商店的相应标准值。（例如，CrossBorder为2）
        // 大于2天就算是预售，默认发货天数是2
        if (CollectionUtils.isNotEmpty(shopeeSkus) && shopeeSkus.size() == 1) {
            ShopeeSku shopeeSku = shopeeSkus.get(0);
            // 如果是单体，没有变体信息，那么这里就要设置上发货天数  如果是变体，就到 GlobalInitTierVariationParam 设置发货天数
            int defaultDayToShip = shopeeSku.getDaysToShip() == null ? ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP : shopeeSku.getDaysToShip();
            boolean isPreOrder = ShopeeDaysToShipConstant.isPreOrder(defaultDayToShip);
            this.preOrder = new PreOrderDtoV2();
            this.preOrder.setDaysToShip(defaultDayToShip);
            this.preOrder.setIsPreOrder(isPreOrder);
        } else {
            this.preOrder = new PreOrderDtoV2();
            Optional<Integer> max = shopeeSkus.stream().map(ShopeeSku::getDaysToShip).filter(Objects::nonNull).max(Integer::compareTo);
            Integer dayToShip = max.orElse(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
            this.preOrder.setDaysToShip(dayToShip);
            this.preOrder.setIsPreOrder(ShopeeDaysToShipConstant.isPreOrder(dayToShip));
        }

        //condition
        this.condition = template.getCondition();

        //品牌
        this.brand = JSON.parseObject(template.getBrand(), new TypeReference<BrandDtoV2>(){});

        //属性
        if (StringUtils.isNotEmpty(template.getAttributesStr())) {
            List<ShopeeAttributeV2> attributes = JSONArray.parseArray(template.getAttributesStr(), ShopeeAttributeV2.class);
            if(CollectionUtils.isNotEmpty(attributes)){
                this.attributeList = new ArrayList<>(attributes.size());
                for (ShopeeAttributeV2 attr : attributes) {
                    ShopeeAttributeDtoV2 attParam = new ShopeeAttributeDtoV2();
                    attParam.setAttributeId(attr.getAttributeId());
                    String attrValueListStr = attr.getAttributeValueList();
                    JSONArray jsonArray = JSON.parseArray(attrValueListStr);
                    for (int i = 0; jsonArray != null && i < jsonArray.size(); i++) {
                        JSONObject valueJson = jsonArray.getJSONObject(i);
                        ShopeeAttributeValueDtoV2 value = JSON.parseObject(valueJson.toJSONString(), new TypeReference<ShopeeAttributeValueDtoV2>() {
                        });
                        attParam.getAttributeValueList().add(value);

                        //如果存在父子结构 也要设置属性,如果有多个只取一个就可以
                        JSONArray parentArray = valueJson.getJSONArray("parent_attribute_list");
                        if(parentArray != null && parentArray.size() > 0){
                            JSONObject parentJson = parentArray.getJSONObject(0);
                            Integer parent_attribute_id = parentJson.getInteger("parent_attribute_id");
                            Integer parent_value_id = parentJson.getInteger("parent_value_id");
                            ShopeeAttributeDtoV2 parentAttParam = new ShopeeAttributeDtoV2();
                            parentAttParam.setAttributeId(parent_attribute_id);
                            ShopeeAttributeValueDtoV2 parentValue = new ShopeeAttributeValueDtoV2();
                            parentValue.setValueId(parent_value_id);
                            parentAttParam.getAttributeValueList().add(parentValue);
                            if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                                this.attributeList.add(parentAttParam);
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                        this.attributeList.add(attParam);
                    }
                }
            }
        }

        // 尺码表
        if (StringUtils.isNotEmpty(template.getSizeChartInfo())) {
            ShopeeSizeChartInfoParamV2 shopeeSizeChartInfoParamV2 = JSON.parseObject(template.getSizeChartInfo(), new TypeReference<ShopeeSizeChartInfoParamV2>() {
            });
            if (StringUtils.isNotBlank(shopeeSizeChartInfoParamV2.getSizeChart())) {
                String imageId = imgMappingMap.get(shopeeSizeChartInfoParamV2.getSizeChart());
                shopeeSizeChartInfoParamV2.setSizeChart(imageId);
            }
            this.sizeChartInfo = shopeeSizeChartInfoParamV2;
        }


//        // 物流
//        this.logisticInfo = JSONArray.parseArray(template.getLogisticsStr(),ShopeeLogisticParam.class);
//        if(CollectionUtils.isNotEmpty(logisticInfo)){
//            logisticInfo.stream().forEach(o -> o.setSite(null));
//        }

        // 不做批发价
//         this.wholesale = JSON.parseObject(template.getWholesale(), new TypeReference<List<WholesaleDtoV2>>(){});

    }


    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.ADD_GLOBAL_ITEM;
    }
}
