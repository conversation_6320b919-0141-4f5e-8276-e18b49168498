package com.estone.erp.publish.shopee.api.v2.cnsc;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/11 15:09
 * @description
 */
@Getter
@Setter
public class GetCategoryCNSC implements RequestCommon {

    @JSONField(name = "language")
    private String language = "en";

    /**
     * 获取请求路径
     * @return
     */
    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_CATEGORY;
    }

}
