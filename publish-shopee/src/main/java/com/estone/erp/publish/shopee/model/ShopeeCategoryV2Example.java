package com.estone.erp.publish.shopee.model;

import java.util.ArrayList;
import java.util.List;

public class ShopeeCategoryV2Example {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ShopeeCategoryV2Example() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andHasChildrenIsNull() {
            addCriterion("has_children is null");
            return (Criteria) this;
        }

        public Criteria andHasChildrenIsNotNull() {
            addCriterion("has_children is not null");
            return (Criteria) this;
        }

        public Criteria andHasChildrenEqualTo(Boolean value) {
            addCriterion("has_children =", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenNotEqualTo(Boolean value) {
            addCriterion("has_children <>", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenGreaterThan(Boolean value) {
            addCriterion("has_children >", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("has_children >=", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenLessThan(Boolean value) {
            addCriterion("has_children <", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenLessThanOrEqualTo(Boolean value) {
            addCriterion("has_children <=", value, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenIn(List<Boolean> values) {
            addCriterion("has_children in", values, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenNotIn(List<Boolean> values) {
            addCriterion("has_children not in", values, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenBetween(Boolean value1, Boolean value2) {
            addCriterion("has_children between", value1, value2, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andHasChildrenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("has_children not between", value1, value2, "hasChildren");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameIsNull() {
            addCriterion("display_category_name is null");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameIsNotNull() {
            addCriterion("display_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameEqualTo(String value) {
            addCriterion("display_category_name =", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameNotEqualTo(String value) {
            addCriterion("display_category_name <>", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameGreaterThan(String value) {
            addCriterion("display_category_name >", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("display_category_name >=", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameLessThan(String value) {
            addCriterion("display_category_name <", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("display_category_name <=", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameLike(String value) {
            addCriterion("display_category_name like", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameNotLike(String value) {
            addCriterion("display_category_name not like", value, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameIn(List<String> values) {
            addCriterion("display_category_name in", values, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameNotIn(List<String> values) {
            addCriterion("display_category_name not in", values, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameBetween(String value1, String value2) {
            addCriterion("display_category_name between", value1, value2, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andDisplayCategoryNameNotBetween(String value1, String value2) {
            addCriterion("display_category_name not between", value1, value2, "displayCategoryName");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("`level` is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("`level` is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(Integer value) {
            addCriterion("`level` =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(Integer value) {
            addCriterion("`level` <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(Integer value) {
            addCriterion("`level` >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("`level` >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(Integer value) {
            addCriterion("`level` <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(Integer value) {
            addCriterion("`level` <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<Integer> values) {
            addCriterion("`level` in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<Integer> values) {
            addCriterion("`level` not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(Integer value1, Integer value2) {
            addCriterion("`level` between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("`level` not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andForbiddenIsNull() {
            addCriterion("forbidden is null");
            return (Criteria) this;
        }

        public Criteria andForbiddenIsNotNull() {
            addCriterion("forbidden is not null");
            return (Criteria) this;
        }

        public Criteria andForbiddenEqualTo(Boolean value) {
            addCriterion("forbidden =", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenNotEqualTo(Boolean value) {
            addCriterion("forbidden <>", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenGreaterThan(Boolean value) {
            addCriterion("forbidden >", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("forbidden >=", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenLessThan(Boolean value) {
            addCriterion("forbidden <", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenLessThanOrEqualTo(Boolean value) {
            addCriterion("forbidden <=", value, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenIn(List<Boolean> values) {
            addCriterion("forbidden in", values, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenNotIn(List<Boolean> values) {
            addCriterion("forbidden not in", values, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenBetween(Boolean value1, Boolean value2) {
            addCriterion("forbidden between", value1, value2, "forbidden");
            return (Criteria) this;
        }

        public Criteria andForbiddenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("forbidden not between", value1, value2, "forbidden");
            return (Criteria) this;
        }

        public Criteria andIdPathIsNull() {
            addCriterion("id_path is null");
            return (Criteria) this;
        }

        public Criteria andIdPathIsNotNull() {
            addCriterion("id_path is not null");
            return (Criteria) this;
        }

        public Criteria andIdPathEqualTo(String value) {
            addCriterion("id_path =", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathNotEqualTo(String value) {
            addCriterion("id_path <>", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathGreaterThan(String value) {
            addCriterion("id_path >", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathGreaterThanOrEqualTo(String value) {
            addCriterion("id_path >=", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathLessThan(String value) {
            addCriterion("id_path <", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathLessThanOrEqualTo(String value) {
            addCriterion("id_path <=", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathLike(String value) {
            addCriterion("id_path like", "%" + value + "%", "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathNotLike(String value) {
            addCriterion("id_path not like", value, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathIn(List<String> values) {
            addCriterion("id_path in", values, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathNotIn(List<String> values) {
            addCriterion("id_path not in", values, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathBetween(String value1, String value2) {
            addCriterion("id_path between", value1, value2, "idPath");
            return (Criteria) this;
        }

        public Criteria andIdPathNotBetween(String value1, String value2) {
            addCriterion("id_path not between", value1, value2, "idPath");
            return (Criteria) this;
        }

        public Criteria andNamePathIsNull() {
            addCriterion("name_path is null");
            return (Criteria) this;
        }

        public Criteria andNamePathIsNotNull() {
            addCriterion("name_path is not null");
            return (Criteria) this;
        }

        public Criteria andNamePathEqualTo(String value) {
            addCriterion("name_path =", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathNotEqualTo(String value) {
            addCriterion("name_path <>", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathGreaterThan(String value) {
            addCriterion("name_path >", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathGreaterThanOrEqualTo(String value) {
            addCriterion("name_path >=", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathLessThan(String value) {
            addCriterion("name_path <", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathLessThanOrEqualTo(String value) {
            addCriterion("name_path <=", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathLike(String value) {
            addCriterion("name_path like", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathNotLike(String value) {
            addCriterion("name_path not like", value, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathIn(List<String> values) {
            addCriterion("name_path in", values, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathNotIn(List<String> values) {
            addCriterion("name_path not in", values, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathBetween(String value1, String value2) {
            addCriterion("name_path between", value1, value2, "namePath");
            return (Criteria) this;
        }

        public Criteria andNamePathNotBetween(String value1, String value2) {
            addCriterion("name_path not between", value1, value2, "namePath");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}