package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeEndBundleDealtemV2;
import com.estone.erp.publish.shopee.api.v2.param.follow.prize.EndFollowPrizeV2;
import com.estone.erp.publish.shopee.api.v2.param.voucher.EndVoucherV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeFollowPrizeCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeVoucherCallV2;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingConfigTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeStopstatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingFollowPrize;
import com.estone.erp.publish.shopee.model.ShopeeMarketingVoucher;
import com.estone.erp.publish.shopee.mq.model.ShopeeStopMarketingActivityDO;
import com.estone.erp.publish.shopee.service.ShopeeDiscountService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingFollowPrizeService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingVoucherService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Description: ${监听消费Shopee同步产品}
 * @Auther yucm
 * @Date 2021/11/22
 */
@Slf4j
public class ShopeeStopMarketingActivityMqListener  implements ChannelAwareMessageListener {
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private ShopeeMarketingVoucherService shopeeMarketingVoucherService;
    @Resource
    private ShopeeMarketingFollowPrizeService shopeeMarketingFollowPrizeService;
    @Resource
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;
    @Resource
    private ShopeeDiscountService shopeeDiscountService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            ShopeeStopMarketingActivityDO msgDO = JSON.parseObject(body, new TypeReference<>() {
            });
            boolean sign = false;
            try {
                stopActivity(msgDO);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeStopMarketingActivityMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeStopMarketingActivityMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeStopMarketingActivityMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeStopMarketingActivityMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    private void stopActivity(ShopeeStopMarketingActivityDO shopeeStopMarketingActivityDO) {
        String accountNumber = shopeeStopMarketingActivityDO.getAccountNumber();
        Integer type = shopeeStopMarketingActivityDO.getType();
        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber);
        //优惠券
        if (type == ShopeeMarketingConfigTypeEnum.VOUCHER.getCode()) {
            // 提交请求
            EndVoucherV2 endVoucherV2 = new EndVoucherV2();
            endVoucherV2.setVoucher_id(shopeeStopMarketingActivityDO.getMarketingActivityId());
            ShopeeResponse shopeeResponse = ShopeeVoucherCallV2.endVoucher(saleAccount, endVoucherV2);
            FeedTask feedTask = feedTaskService.selectByPrimaryKey(shopeeStopMarketingActivityDO.getFeedTaskId(), Platform.Shopee.name());
            //更新停止状态
            ShopeeMarketingVoucher marketingVoucher = new ShopeeMarketingVoucher();
            marketingVoucher.setId(shopeeStopMarketingActivityDO.getId());
            if (StringUtils.isNotBlank(shopeeResponse.getError())) {
                marketingVoucher.setStopStatus(ShopeeStopstatusEnum.FAIL.getCode());
                shopeeMarketingVoucherService.updateByPrimaryKeySelective(marketingVoucher);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "优惠券名称：" + shopeeStopMarketingActivityDO.getName() + ",停止失败," +"原因:"+ shopeeResponse.getError());
            }else {
                marketingVoucher.setStopStatus(ShopeeStopstatusEnum.SUCCESS.getCode());
                shopeeMarketingVoucherService.updateByPrimaryKeySelective(marketingVoucher);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "优惠券名称：" + shopeeStopMarketingActivityDO.getName() + "停止成功");
            }
        }

        //套餐
        if (type == ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL.getCode()) {
            // 提交请求
            ShopeeEndBundleDealtemV2 shopeeEndBundleDealtemV2 = new ShopeeEndBundleDealtemV2();
            shopeeEndBundleDealtemV2.setBundle_deal_id(shopeeStopMarketingActivityDO.getMarketingActivityId());
            ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.endBundelDealItem(saleAccount, shopeeEndBundleDealtemV2);
            //修改日志
            FeedTask feedTask = feedTaskService.selectByPrimaryKey(shopeeStopMarketingActivityDO.getFeedTaskId(), Platform.Shopee.name());
            //更新停止状态
            ShopeeMarketingBundleDeal  shopeeMarketingBundleDeal = new ShopeeMarketingBundleDeal();
            shopeeMarketingBundleDeal.setId(Long.valueOf(shopeeStopMarketingActivityDO.getId()));
            if (StringUtils.isNotBlank(shopeeResponse.getError())) {
                shopeeMarketingBundleDeal.setStopStatus(ShopeeStopstatusEnum.FAIL.getCode());
                shopeeMarketingBundleDealService.updateByPrimaryKeySelective(shopeeMarketingBundleDeal);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "套装名称：" + shopeeStopMarketingActivityDO.getName() + ",停止失败," +"原因:"+ shopeeResponse.getError());
            }else {
                shopeeMarketingBundleDeal.setStopStatus(ShopeeStopstatusEnum.SUCCESS.getCode());
                shopeeMarketingBundleDealService.updateByPrimaryKeySelective(shopeeMarketingBundleDeal);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "套装名称：" + shopeeStopMarketingActivityDO.getName() + "停止成功");
            }
        }

        //关注礼
        if (type == ShopeeMarketingConfigTypeEnum.FOLLOW_PRIZE.getCode()) {
            // 提交请求
            EndFollowPrizeV2 endFollowPrizeV2 = new EndFollowPrizeV2();
            endFollowPrizeV2.setCampaign_id(shopeeStopMarketingActivityDO.getMarketingActivityId());
            ShopeeResponse shopeeResponse = ShopeeFollowPrizeCallV2.endFollowPrize(saleAccount,endFollowPrizeV2);
            //修改日志
            FeedTask feedTask = feedTaskService.selectByPrimaryKey(shopeeStopMarketingActivityDO.getFeedTaskId(), Platform.Shopee.name());
            //更新停止状态
            ShopeeMarketingFollowPrize shopeeMarketingFollowPrize = new ShopeeMarketingFollowPrize();
            shopeeMarketingFollowPrize.setId(shopeeStopMarketingActivityDO.getId());
            if (StringUtils.isNotBlank(shopeeResponse.getError())) {
                shopeeMarketingFollowPrize.setStopStatus(ShopeeStopstatusEnum.FAIL.getCode());
                shopeeMarketingFollowPrizeService.updateByPrimaryKeySelective(shopeeMarketingFollowPrize);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "关注礼名称：" + shopeeStopMarketingActivityDO.getName() + ",停止失败," +"原因:"+ shopeeResponse.getError());
            }else {
                shopeeMarketingFollowPrize.setStopStatus(ShopeeStopstatusEnum.SUCCESS.getCode());
                shopeeMarketingFollowPrizeService.updateByPrimaryKeySelective(shopeeMarketingFollowPrize);
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "关注礼名称：" + shopeeStopMarketingActivityDO.getName() + "停止成功");
            }
        }

        if (type == ShopeeMarketingConfigTypeEnum.DISCOUNT.getCode()) {
            DataContextHolder.setUsername(shopeeStopMarketingActivityDO.getOperator());
            Integer id = shopeeStopMarketingActivityDO.getId();
            Long marketingActivityId = shopeeStopMarketingActivityDO.getMarketingActivityId();
            FeedTask feedTask = feedTaskService.selectByPrimaryKey(shopeeStopMarketingActivityDO.getFeedTaskId(), Platform.Shopee.name());
            shopeeDiscountService.endDiscount(id, marketingActivityId, feedTask);
        }
    }
}
