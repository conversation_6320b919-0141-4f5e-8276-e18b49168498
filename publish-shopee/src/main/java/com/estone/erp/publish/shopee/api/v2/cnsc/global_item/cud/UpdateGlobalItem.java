package com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeSizeChartInfoParam;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.param.add.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2023/8/2
 */
@Getter
@Setter
public class UpdateGlobalItem implements RequestCommon {

    /** 全球ID */
    @JSONField(name = "global_item_id")
    private Long globalItemId;

    /** 类别编号*/
    @JSONField(name = "category_id")
    private Integer categoryId ;

    /** 标题*/
    @JSONField(name = "global_item_name")
    private String itemName;

    /** Description of item*/
    @JSONField(name = "description")
    private String description;

    /** sku */
    @JSONField(name = "global_item_sku")
    private String itemSku;

    /** Weight of item*/
    @JSONField(name = "weight")
    private Double weight;

    /** Item dimension 如果这个字段不为空，那就是必填*/
    @JSONField(name = "dimension")
    private DimensionDtoV2 dimension;

    /** 预购设定 */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    /** Condition of item, could be USED or NEW */
    @JSONField(name = "condition")
    private String condition;

    /** 图片 */
    @JSONField(name = "image")
    private ImageDtoV2 image = new ImageDtoV2();

    /** 从视频上传API返回的视频上传ID。 最多只能接受一个 */
    @JSONField(name = "video_upload_id")
    private List<String> videoUploadId;

    /** 品牌 */
    @JSONField(name = "brand")
    private BrandDtoV2 brand;

    /** 根据不同类别下的特定属性，此字段是可选的（印度尼西亚）*/
    @JSONField(name = "attribute_list")
    private List<ShopeeAttributeDtoV2> attributeList;

    @JSONField(name = "size_chart_info")
    private ShopeeSizeChartInfoParam sizeChartInfo;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.UPDATE_GLOBAL_ITEM;
    }

    public void buildAttributeList(String attributesStr) {
        //属性
        if (StringUtils.isNotEmpty(attributesStr)) {
            JSONArray attributes = JSONArray.parseArray(attributesStr);
            if(CollectionUtils.isNotEmpty(attributes)){
                this.attributeList = new ArrayList<>(attributes.size());
                for (int i = 0; i < attributes.size(); i++) {
                    JSONObject attr = attributes.getJSONObject(i);
                    Integer attributeId = attr.getInteger("attributeId");
                    if(null == attributeId) {
                        attributeId = attr.getInteger("attribute_id");
                    }
                    if(null == attributeId) {
                        continue;
                    }
                    ShopeeAttributeDtoV2 attParam = new ShopeeAttributeDtoV2();
                    attParam.setAttributeId(attributeId);
                    JSONArray attributeValueList = attr.getJSONArray("attributeValueList");
                    if(attributeValueList == null) {
                        attributeValueList = attr.getJSONArray("attribute_value_list");
                    }

                    for (int j = 0; attributeValueList != null && j < attributeValueList.size(); j++) {
                        JSONObject valueJson = attributeValueList.getJSONObject(j);
                        ShopeeAttributeValueDtoV2 value = JSON.parseObject(valueJson.toJSONString(), new TypeReference<ShopeeAttributeValueDtoV2>() {
                        });
                        attParam.getAttributeValueList().add(value);

                        //如果存在父子结构 也要设置属性,如果有多个只取一个就可以
                        JSONArray parentArray = valueJson.getJSONArray("parent_attribute_list");
                        if(parentArray != null && parentArray.size() > 0){
                            JSONObject parentJson = parentArray.getJSONObject(0);
                            Integer parent_attribute_id = parentJson.getInteger("parent_attribute_id");
                            Integer parent_value_id = parentJson.getInteger("parent_value_id");
                            ShopeeAttributeDtoV2 parentAttParam = new ShopeeAttributeDtoV2();
                            parentAttParam.setAttributeId(parent_attribute_id);
                            ShopeeAttributeValueDtoV2 parentValue = new ShopeeAttributeValueDtoV2();
                            parentValue.setValueId(parent_value_id);
                            parentAttParam.getAttributeValueList().add(parentValue);
                            if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                                this.attributeList.add(parentAttParam);
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                        this.attributeList.add(attParam);
                    }
                }
            }
        }
    }
}
