package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.config.ShopeeApplicationRunner;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.model.PmsSku;
import com.estone.erp.publish.platform.model.PmsSkuExample;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.bo.ShopeePublishObject;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.bo.ShopeeTemplateNewBo;
import com.estone.erp.publish.shopee.cache.CommonSharedCache;
import com.estone.erp.publish.shopee.domain.ShopeeTemplateNewHelp;
import com.estone.erp.publish.shopee.enums.ShopeeNewPublishStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishRoleEnum;
import com.estone.erp.publish.shopee.enums.ShopeeTemplateTableEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNewCriteria;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNewExample;
import com.estone.erp.publish.shopee.mq.ShopeePublishProducer;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeePublishSkuService;
import com.estone.erp.publish.shopee.service.ShopeeTemplateNewService;
import com.estone.erp.publish.shopee.util.ShopeeProductInfoUtil;
import com.estone.erp.publish.shopee.util.ShopeeTemplateNewUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils.tokenUser;

/**
 * 2021-03-18 17:47:39
 */
@Slf4j
@RestController
@RequestMapping("shopeeTemplateNew")
public class ShopeeTemplateNewController {
    @Resource
    private ShopeeTemplateNewService shopeeTemplateNewService;
    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private ShopeePublishSkuService shopeePublishSkuService;
    @Resource
    private PmsSkuService pmsSkuService;
    @Resource
    private ShopeePublishProducer shopeePublishProducer;

    @PostMapping
    public ApiResult<?> postShopeeTemplateNew(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        String userName = WebUtils.getUserName();

        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeTemplates": // 查询列表
                    CQuery<ShopeeTemplateNewCriteria> cQueryShopeeTemplates = requestParam
                            .getArgsValue(new TypeReference<CQuery<ShopeeTemplateNewCriteria>>() {
                            });
                    Asserts.isTrue(cQueryShopeeTemplates != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                    //只能看见创建人是自己的模板，超级管理员可以看到全部模板
//                    if (!AccountUtils.isSuperAdmin()) {
//                        if (!cQueryShopeeTemplates.getSearch().getIsParent() && StringUtils.isEmpty(cQueryShopeeTemplates.getSearch().getCreatedBy())) {
//                            cQueryShopeeTemplates.getSearch().setCreatedBy(userName);
//                        }
//                    }

                    /*权限控制----start*/
                    //如果是模板，入参创建人为空且不是超管或者最高权限者，则只查自己
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
                    if (!superAdminOrEquivalent.isSuccess()) {
                        return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                    }
                    if (!cQueryShopeeTemplates.getSearch().getIsParent() && StringUtils.isBlank(cQueryShopeeTemplates.getSearch().getCreatedBy()) && !superAdminOrEquivalent.getResult()) {
                        if (StringUtils.isBlank(WebUtils.getUserName())){
                            return ApiResult.newError("获取登录账号失败！");
                        }
                        cQueryShopeeTemplates.getSearch().setCreatedBy(WebUtils.getUserName());
                    }
                    /*权限控制----end*/

                    CQueryResult<ShopeeTemplateNew> results = shopeeTemplateNewService.search(cQueryShopeeTemplates);
                    return results;
                case "searchNotExistTemplateSku": // 查询没有创建范本的sku

                    return searchNotExistTemplateSku(requestParam);
                case "searchNotCopyTemplateSku": // 查询销售还未复制的范本的sku

                    return searchNotCopyTemplateSku(requestParam);
                case "batchCopyTemplate": // 批量复制模板

                    return batchCopyTemplate(requestParam);
                case "searchShopeeTemplateSku": //检验货号SKU

                    return searchShopeeTemplateSku(requestParam);
                case "addShopeeTemplate": // 添加

                    return addShopeeTemplate(requestParam);
                case "updatePublishStatus":
                    //修改模板刊登状态,刊登状态，1.待刊登 2.刊登中 3.成功 4.失败

                    return updatePublishStatus(requestParam);
                case "batchPublishTemplate": //批量刊登

                    List<ShopeePublishObject> ShopeePublishObjectList = requestParam.getArgsValue(new TypeReference<List<ShopeePublishObject>>() {
                    });
//                    return shopeeTemplateNewService.batchPublishTemplate(ShopeePublishObjectList, userName);

                    ResponseJson resp = shopeePublishProducer.batchPublishTemplateToMq(ShopeePublishObjectList, userName);
                    if (resp.isSuccess()) {
                        return ApiResult.newSuccess(resp.getMessage());
                    } else {
                        return ApiResult.newError(resp.getMessage());
                    }
            }
        }
        return ApiResult.newSuccess();
    }

    private ApiResult<?> updatePublishStatus(ApiRequestParam<String> requestParam) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }
        if (!superAdminOrEquivalent.getResult()) {
            return ApiResult.newSuccess("模板状态仅允许超管，平台主管修改");
        }
        String userName = WebUtils.getUserName();
        JSONObject templateParamObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
        });
        String ids = templateParamObject.getString("ids");
        List<Integer> idList = Arrays.asList(ids.split(","))
                .stream().map(o -> Integer.valueOf(o))
                .collect(Collectors.toList());
        Integer publishStatus = templateParamObject.getInteger("publishStatus");

//        ShopeeTemplateNewExample ex = new ShopeeTemplateNewExample();
//        ex.createCriteria()
//                .andIdIn(idList)
//                .andPublishStatusEqualTo(ShopeeNewPublishStatusEnum.SUCCESS.getCode());
//        List<ShopeeTemplateNew> list = shopeeTemplateNewService.selectByExample(ex);
//        if(CollectionUtils.isNotEmpty(list)){
//            List<Integer> failIds = list.stream().map(o -> o.getId()).collect(Collectors.toList());
//            return ApiResult.newError(String.format("模板%s为刊登成功，不能修改状态！", failIds));
//        }

        ShopeeTemplateNew updateData = new ShopeeTemplateNew();
        updateData.setPublishStatus(publishStatus);
        updateData.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        updateData.setLastUpdatedBy(userName);

        ShopeeTemplateNewExample where = new ShopeeTemplateNewExample();
        where.createCriteria().andIdIn(idList);
        where.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
        int count = shopeeTemplateNewService.updateByExampleSelective(updateData, where);

        return ApiResult.newSuccess(count);
    }

    /**
     * 添加模板 范本
     *
     * @param requestParam
     * @return
     */
    private ApiResult<?> addShopeeTemplate(ApiRequestParam<String> requestParam) {
        ShopeeTemplateNewHelp templateHelp = requestParam.getArgsValue(new TypeReference<ShopeeTemplateNewHelp>() {
        });

        ShopeeTemplateNewBo shopeeTemplate = templateHelp.getShopeeTemplate();
        String sku = ProductUtils.getMainSku(shopeeTemplate.getSku().trim());
        // 子SKU过滤禁售侵权
        ApiResult<?> apiResult = ShopeeTemplateNewUtil.checkPublishCondition(false, shopeeTemplate, null);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }
        //普通刊登的模板才需要校验重复模板，自动刊登不需要
        if (templateHelp.getTemplateType().equals(1)) {
            //不允许创建相同的sku的范本
            ShopeeTemplateNewExample example = new ShopeeTemplateNewExample();
            example.createCriteria()
                    .andSkuEqualTo(sku)
                    .andSiteEqualTo(shopeeTemplate.getSite())
                    .andIsParentEqualTo(true);
            example.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_MODEL.getCode());
            int templateNum = shopeeTemplateNewService.countByExample(example);
            if (templateNum > 0) {
                return ApiResult.newError(String.format("站点%s,已存在当前sku的范本", shopeeTemplate.getSite()));
            }
        }
        // 校验不可刊登sku
        ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByAccountNumber(shopeeTemplate.getAccountNumber());
        ApiResult accountConfigResult = ShopeeTemplateNewUtil.checkAccountConfig(shopeeTemplate, shopeeAccountConfig);
        if (!accountConfigResult.isSuccess()) {
            return accountConfigResult;
        }
        //验证店铺所属站点 和模板站点是否一致
        if (null == shopeeAccountConfig || !StringUtils.equalsIgnoreCase(shopeeAccountConfig.getSite(), shopeeTemplate.getSite())) {
            return ApiResult.newError("店铺配置为空或店铺所属站点和模板站点不一致，请确认！");
        }

        // 保存母板
        ApiResult<String> persistApiResult = shopeeTemplateNewService.persistTemplate(templateHelp, "add");
        if (!persistApiResult.isSuccess()) {
            return persistApiResult;
        }

        //普通刊登的范本才需要上传图片
//        if (templateHelp.getTemplateType().equals(1)) {
//            //把范本图片上传到阿里云
//            List<ShopeeImageParam> imageList = shopeeTemplate.getImages();
//            if(CollectionUtils.isNotEmpty(imageList)) {
//                List<String> uploadImageList = new ArrayList<>();
//                for (ShopeeImageParam image : imageList) {
//                    uploadImageList.add(image.getUrl().trim());
//                }
//                AliOSSUtils.uploadImageToAliOss(uploadImageList);
//            }
//        }

        if (persistApiResult.isSuccess()) {
            if (templateHelp.getUpload()) {
                SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, shopeeTemplate.getAccountNumber(), true);
                return uploadProduct(shopeeTemplate, shopeeAccount);
            } else {
                return ApiResult.newSuccess("添加成功！");
            }
        } else {
            return ApiResult.newError(persistApiResult.getErrorMsg());
        }
    }

    private ApiResult<?> uploadProduct(ShopeeTemplateNewBo shopeeTemplateBo, SaleAccountAndBusinessResponse shopeeAccount) {
        String userName = WebUtils.getUserName();
        //范本的刊登，要先复制一个模板，再进行刊登
        int publishTemplateId;
        String table = ShopeeTemplateNewUtil.getShopeeTemplateTable(shopeeTemplateBo.getIsParent());
        ShopeeTemplateNew shopeeTemplate = shopeeTemplateNewService.selectByPrimaryKey(shopeeTemplateBo.getId(), table);
        if (BooleanUtils.isTrue(shopeeTemplate.getIsParent())) {
            ShopeeTemplateNew newShopeeTemplate = new ShopeeTemplateNew();
            BeanUtils.copyProperties(shopeeTemplate, newShopeeTemplate);
            newShopeeTemplate.setIsParent(false);
            newShopeeTemplate.setParentId(shopeeTemplate.getId());
            String imagesStr = StringUtils.isEmpty(newShopeeTemplate.getImagesStr()) ? "[]" : newShopeeTemplate.getImagesStr();
            //如果没有主图数量（这是历史数据引起的），则默认主图数量是1
            int mainImageNum = newShopeeTemplate.getMainImageNum() == null ? 1 : newShopeeTemplate.getMainImageNum();
            List<Map> originImageMapList = JSONArray.parseArray(imagesStr, Map.class);
            Random random = new Random();
            List<Map> newImageMapList = new ArrayList<>();
            //随机挑选范本的一张主图当成模板的副图
            newImageMapList.add(originImageMapList.get(random.nextInt(mainImageNum)));
            for (int j = mainImageNum; j < originImageMapList.size(); j++) {
                newImageMapList.add(originImageMapList.get(j));
            }
            newShopeeTemplate.setImagesStr(JSONArray.toJSONString(newImageMapList));
            newShopeeTemplate.setMainImageNum(1);
            newShopeeTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
            newShopeeTemplate.setCreatedBy(WebUtils.getUserName());
            newShopeeTemplate.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            newShopeeTemplate.setLastUpdatedBy(WebUtils.getUserName());
            newShopeeTemplate.setId(null);
            newShopeeTemplate.setTitleRule(null);
            shopeeTemplateNewService.insert(newShopeeTemplate);
            publishTemplateId = newShopeeTemplate.getId();

            shopeeTemplate = newShopeeTemplate;
        } else {
            publishTemplateId = shopeeTemplate.getId();
        }

//        ShopeeExecutors.executePublishProduct(() -> {
//            shopeeTemplateNewService.singleAccountPublish(publishTemplateId,null,shopeeAccount.getAccountNumber(),userName, false);
//        });

        //********改成mq方式
        ResponseJson resp = shopeePublishProducer.sendMq(shopeeTemplate, userName);
        if (!resp.isSuccess()) {
            return ApiResult.newError(resp.getMessage());
        }

        return ApiResult.newSuccess("后台已经开始进行刊登处理，请到处理报告中查看详细结果");

    }


    /**
     * 查询没有创建范本的sku
     *
     * @param requestParam
     * @return
     */
    public ApiResult<?> searchNotExistTemplateSku(ApiRequestParam<String> requestParam) {
        Map<String, String> map = requestParam.getArgsValue(new TypeReference<Map<String, String>>() {
        });
        String site = map.get("site");
        String skusStr = map.get("sku");
        String[] skus = skusStr.replace(" ", "").replace("，", ",").replace("\t", "").split(",");
        ShopeeTemplateNewExample shopeeTemplateExample = new ShopeeTemplateNewExample();
        ShopeeTemplateNewExample.Criteria criteria = shopeeTemplateExample.createCriteria();
        criteria.andIsParentEqualTo(true)
                .andSkuIn(Arrays.asList(skus));
        if (StringUtils.isNotBlank(site)) {
            criteria.andSiteEqualTo(site);
        }
        shopeeTemplateExample.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_MODEL.getCode());
        List<ShopeeTemplateNew> shopeeTemplateList = shopeeTemplateNewService.selectByExample(shopeeTemplateExample);
        Set<String> existSkuSet = new HashSet<>();
        for (ShopeeTemplateNew shopeeTemplate : shopeeTemplateList) {
            existSkuSet.add(shopeeTemplate.getSku().trim());
        }
        Set<String> noExistSkuSet = new HashSet<>();
        for (String sku : skus) {
            if (!existSkuSet.contains(sku.trim())) {
                noExistSkuSet.add(sku.trim());
            }
        }
        return ApiResult.newSuccess(noExistSkuSet);
    }

    public ApiResult<?> searchNotCopyTemplateSku(ApiRequestParam<String> requestParam) {
        String paramSkusStr = requestParam.getArgs();
        String[] paramSkus = paramSkusStr.replace(" ", "").replace("\t", "").split(",");
        List<String> paramSkuList = new ArrayList<>(Arrays.asList(paramSkus));
        ShopeeTemplateNewExample existTemplateExample = new ShopeeTemplateNewExample();
        existTemplateExample.createCriteria()
                .andSkuIn(paramSkuList)
                .andIsParentEqualTo(false)
                .andCreatedByEqualTo(String.valueOf(WebUtils.getUserName()));
        existTemplateExample.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
        List<ShopeeTemplateNew> existTemplateList = shopeeTemplateNewService.selectByExample(existTemplateExample);
        List<String> existSkuList = existTemplateList.stream().map(ShopeeTemplateNew::getSku).collect(Collectors.toList());
        paramSkuList.removeAll(existSkuList);
        return ApiResult.newSuccess(paramSkuList);
    }

    /**
     * 批量复制模板
     *
     * @param requestParam
     * @return
     */
    public ApiResult<?> batchCopyTemplate(ApiRequestParam<String> requestParam) {
        String userName = WebUtils.getUserName();
        List<Map<String, Integer>> paramMapList = requestParam.getArgsValue(new TypeReference<List<Map<String, Integer>>>() {
        });
        for (Map<String, Integer> paramMap : paramMapList) {
            int id = paramMap.get("id");
            int quantity = paramMap.get("quantity");
            int isParent = paramMap.get("isParent");
            String table = ShopeeTemplateNewUtil.getShopeeTemplateTable(isParent);
            ShopeeTemplateNew originShopeeTemplate = shopeeTemplateNewService.selectByPrimaryKey(id, table);

            List<ShopeeTemplateNew> newShopeeTemplateList = new ArrayList<>();
            for (int i = 0; i < quantity; i++) {
                ShopeeTemplateNew newShopeeTemplate = new ShopeeTemplateNew();
                BeanUtils.copyProperties(originShopeeTemplate, newShopeeTemplate);

                //如果复制的是范本，并且把范本主图随机赋值一张给模板
                if (originShopeeTemplate.getIsParent()) {
                    newShopeeTemplate.setIsParent(false);
                    String imagesStr = StringUtils.isEmpty(newShopeeTemplate.getImagesStr()) ? "[]" : newShopeeTemplate.getImagesStr();
                    //如果没有主图数量（这是历史数据引起的），则默认主图数量是1
                    int mainImageNum = newShopeeTemplate.getMainImageNum() == null ? 1 : newShopeeTemplate.getMainImageNum();
                    List<Map> originImageMapList = JSONArray.parseArray(imagesStr, Map.class);
                    Random random = new Random();
                    List<Map> newImageMapList = new ArrayList<>();
                    //随机挑选范本的一张主图当成模板的副图
                    mainImageNum = (mainImageNum > originImageMapList.size()) ? originImageMapList.size() - 1 : mainImageNum;
                    newImageMapList.add(originImageMapList.get(random.nextInt(mainImageNum)));
                    for (int j = mainImageNum; j < originImageMapList.size(); j++) {
                        newImageMapList.add(originImageMapList.get(j));
                    }
                    newShopeeTemplate.setImagesStr(JSONArray.toJSONString(newImageMapList));
                    newShopeeTemplate.setMainImageNum(1);
                }
                newShopeeTemplate.setParentId(originShopeeTemplate.getId());
                newShopeeTemplate.setId(null);
                newShopeeTemplate.setAccountNumber(null);
                newShopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
                newShopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
                newShopeeTemplate.setTitleRule(null);
                newShopeeTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
                newShopeeTemplate.setCreatedBy(userName);
                newShopeeTemplate.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                newShopeeTemplate.setLastUpdatedBy(userName);
                //重新查一遍sku的单品状态并设置
                if (StringUtils.isNotEmpty(newShopeeTemplate.getShopeeSkusStr())) {
                    List<JSONObject> skuObjectList = JSONArray.parseArray(newShopeeTemplate.getShopeeSkusStr(), JSONObject.class);
                    List<JSONObject> newSkuObjectList = new ArrayList<>();
                    for (JSONObject skuObject : skuObjectList) {
                        String subSku = skuObject.getString("sku");
                        PmsSkuExample pmsSkuExample = new PmsSkuExample();
                        PmsSkuExample.Criteria pmsSkuCriteria = pmsSkuExample.createCriteria();
                        pmsSkuCriteria.andArticleNumberEqualTo(subSku);
                        List<PmsSku> pmsSkuList = pmsSkuService.selectByExample(pmsSkuExample);
                        if (CollectionUtils.isNotEmpty(pmsSkuList)) {
                            PmsSku pmsSku = pmsSkuList.get(0);
                            String skuCode = SkuStatusEnum.getCodeById(pmsSku.getStatus());
                            skuObject.put("productStatus", skuCode);
                        }
                        newSkuObjectList.add(skuObject);
                    }
                    newShopeeTemplate.setShopeeSkusStr(JSON.toJSONString(newSkuObjectList));
                }
                newShopeeTemplateList.add(newShopeeTemplate);
            }
            shopeeTemplateNewService.batchInsertShopeeTemplate(newShopeeTemplateList, ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
        }
        return ApiResult.newSuccess("复制完成");
    }

    /**
     * 根据货号生成模板数据
     *
     * @param requestParam
     * @return
     */
    public ApiResult<?> searchShopeeTemplateSku(ApiRequestParam<String> requestParam) {
        CQuery<ShopeeTemplateNewCriteria> cSearchShopeeTemplateSku = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeTemplateNewCriteria>>() {
        });
        Asserts.isTrue(cSearchShopeeTemplateSku != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
        Asserts.isTrue(cSearchShopeeTemplateSku.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数search错误，请检查！");

        // sku空检查
        if (StringUtils.isBlank(cSearchShopeeTemplateSku.getSearch().getSku())) {
            return ApiResult.of(false, null, "sku is null！");
        }
        if (StringUtils.isBlank(cSearchShopeeTemplateSku.getSearch().getSite())) {
            return ApiResult.of(false, null, "site is null！");
        }

        String sku = ProductUtils.getMainSku(cSearchShopeeTemplateSku.getSearch().getSku().trim());
        String site = cSearchShopeeTemplateSku.getSearch().getSite();

        // sku去空格, 如果存在就提示重复
        ShopeeTemplateNewExample example = new ShopeeTemplateNewExample();
        example.createCriteria()
                .andSkuEqualTo(sku)
                .andSiteEqualTo(site)
                .andIsParentEqualTo(true);
        example.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_MODEL.getCode());
        long i = shopeeTemplateNewService.countByExample(example);
        if (i > 0) {
            return ApiResult.of(false, null, "已存在当前sku的范本！");
        }

        //从新产品系统接口获取信息
        ApiResult<?> apiResultx = ShopeeProductInfoUtil.buildProductInfo(sku, site);

        return apiResultx;
    }

    /**
     * 根据id获取模板
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/template/{id}/{isParent}")
    public ApiResult<?> getShopeeTemplateNew(@PathVariable(value = "id", required = true) Integer id, @PathVariable(value = "isParent", required = true) Boolean isParent) {
        String table = ShopeeTemplateNewUtil.getShopeeTemplateTable(isParent);
        ShopeeTemplateNew shopeeTemplateNew = shopeeTemplateNewService.selectByPrimaryKey(id, table);
        return ApiResult.newSuccess(shopeeTemplateNew);
    }

    /**
     * 修改模板并刊登
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/updateShopeeTemplate")
    private ApiResult<?> updateShopeeTemplate(@RequestBody ApiRequestParam<String> requestParam) {
        ShopeeTemplateNewHelp shopeeTemplateNewHelp = requestParam.getArgsValue(new TypeReference<ShopeeTemplateNewHelp>() {
        });
        ShopeeTemplateNewBo shopeeTemplate = shopeeTemplateNewHelp.getShopeeTemplate();

        ApiResult<?> apiResult = ShopeeTemplateNewUtil.checkPublishCondition(false, shopeeTemplate, null);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }

        // 校验不可刊登sku
        ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByAccountNumber(shopeeTemplate.getAccountNumber());
        ApiResult accountConfigResult = ShopeeTemplateNewUtil.checkAccountConfig(shopeeTemplate, shopeeAccountConfig);
        if (!accountConfigResult.isSuccess()) {
            return accountConfigResult;
        }

        // 保存母板
        ApiResult<String> persistApiResult = shopeeTemplateNewService.persistTemplate(shopeeTemplateNewHelp, "update");
        if (persistApiResult.isSuccess()) {
            if (shopeeTemplateNewHelp.getUpload()) {
                SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, shopeeTemplate.getAccountNumber(), true);
                return uploadProduct(shopeeTemplate, shopeeAccount);
            } else {
                return ApiResult.newSuccess("修改成功！");
            }
        } else {
            return ApiResult.newError(persistApiResult.getErrorMsg());
        }
    }

    /**
     * 删除模板 范本
     * deleteShopeeItems
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/deleteShopeeTemplate")
    public ApiResult<?> deleteShopeeTemplate(@RequestBody ApiRequestParam<String> requestParam) {
        ShopeeTemplateNewCriteria bean = requestParam.getArgsValue(new TypeReference<ShopeeTemplateNewCriteria>() {
        });
        if (bean == null || StringUtils.isBlank(bean.getTemplateIds())) {
            return ApiResult.newError("参数为空");
        }
        String templateIds = bean.getTemplateIds();
        Boolean isParent = bean.getIsParent();
        String table = ShopeeTemplateNewUtil.getShopeeTemplateTable(isParent);
        List<Integer> idList = CommonUtils.splitIntList(templateIds, ",");

        List<Integer> statusList = Arrays.asList(ShopeeNewPublishStatusEnum.PUBLISHING.getCode(), ShopeeNewPublishStatusEnum.SUCCESS.getCode());
        if (BooleanUtils.isTrue(isParent)) {
            /*权限判断-----start*/
            //查询登录用户
            ApiResult<?> tokenUser = tokenUser();
            if (tokenUser != null && tokenUser.isSuccess()) {
                NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
                //查询数据库数据
                ShopeeTemplateNewExample example = new ShopeeTemplateNewExample();
                example.createCriteria().andIdIn(idList);
                example.setTable(table);
                List<ShopeeTemplateNew> parentTempList = shopeeTemplateNewService.selectByExample(example);
                for (ShopeeTemplateNew shopeeTemplateNew : parentTempList) {
                    //如果范本创建人不是当前登录人，就判断是否是超管或者主管
                    if (!shopeeTemplateNew.getCreatedBy().equals(newUser.getEmployeeNo()) && !NewUsermgtUtils.isSuperAdminOrEquivalent(newUser, SaleChannel.CHANNEL_SHOPEE)) {
                        return ApiResult.newError("范本 " + shopeeTemplateNew.getId() + " 仅允许创建人，超管，主管删除！！");
                    }
                }
            } else {
                return ApiResult.newError(tokenUser.getErrorMsg());
            }
            /*权限判断-----end*/

            //范本关联的模板为刊登中 刊登成功不能删除
            ShopeeTemplateNewExample newExample = new ShopeeTemplateNewExample();
            newExample.createCriteria()
                    .andParentIdIn(idList)
                    .andIsParentEqualTo(false)
                    .andPublishStatusIn(statusList);
            newExample.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
            List<ShopeeTemplateNew> tempList = shopeeTemplateNewService.selectByExample(newExample);
            if (CollectionUtils.isNotEmpty(tempList)) {
                List<Integer> failIds = tempList.stream().map(o -> o.getId()).collect(Collectors.toList());
                return ApiResult.newError("id为" + failIds + "的范本，关联模板存在刊登中或刊登成功，不能删除");
            }
        } else {
            //刊登成功、刊登中的模板不允许删除
            ShopeeTemplateNewExample ex = new ShopeeTemplateNewExample();
            ex.createCriteria().andIdIn(idList);
            ex.setTable(table);
            List<ShopeeTemplateNew> list = shopeeTemplateNewService.selectByExample(ex);
            List<Integer> hasPublishTemplateIdList = new ArrayList<>(list.size());
            for (ShopeeTemplateNew templateNew : list) {
                if (templateNew.getPublishStatus() != null
                        && (ShopeeNewPublishStatusEnum.SUCCESS.getCode() == templateNew.getPublishStatus() || ShopeeNewPublishStatusEnum.PUBLISHING.getCode() == templateNew.getPublishStatus())) {
                    hasPublishTemplateIdList.add(templateNew.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(hasPublishTemplateIdList)) {
                return ApiResult.newError("id为" + hasPublishTemplateIdList + "的模板为刊登中或刊登成功，不能删除");
            }
        }

        ShopeeTemplateNewExample ex = new ShopeeTemplateNewExample();
        ex.createCriteria().andIdIn(idList);
        ex.setTable(table);
        Integer deleteShopeeTemplateInt = shopeeTemplateNewService.deleteByExample(ex);
        if (deleteShopeeTemplateInt > 0) {
            return ApiResult.newSuccess("删除成功！");
        } else {
            return ApiResult.newError("删除条数为0，删除失败！");
        }
    }


    /**
     * 批量算价接口
     *
     * @param reqList [{
     *                articleNumber:货号,
     *                shippingMethod:物流方式,
     *                grossProfitRate:毛利率,
     *                site:站点,
     *                }]
     * @return
     */
    @PostMapping(value = "/batchCalcPrice")
    public ApiResult<?> calcPrice(@RequestBody List<BatchPriceCalculatorRequest> reqList) {
        if (reqList == null || reqList.size() == 0) {
            return ApiResult.newError("参数为空！");
        }

        String platform = Platform.Shopee.name();
        reqList.stream().forEach(bean -> {
            bean.setId(String.format("%s_%s", bean.getSite(), bean.getArticleNumber()));
            bean.setSaleChannel(platform);
//            bean.setSite(ShopeeCountryEnum.getCountryCodeByName(bean.getSite()));
            bean.setSite(bean.getSite());
            bean.setCountryCode(bean.getSite());
            bean.setQuantity(1);
        });

        //请求
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(reqList, 3);
        return listApiResult;
    }


    /**
     * 根据sku，国家 查询刊登成功模板的color size
     */
    @GetMapping("/matchSkuColorSizeBySkuSite")
    public ApiResult<?> matchSkuColorSizeBySkuSite(@RequestParam("sku") String sku, @RequestParam("site") String site) {
        if (StringUtils.isBlank(sku)) {
            return ApiResult.newError("sku is null");
        }
        if (StringUtils.isBlank(site)) {
            return ApiResult.newError("site is null");
        }
        ShopeeTemplateNewBo bo = ShopeeProductInfoUtil.matchSkuColorSizeBySkuSite(sku, site);
        if (CollectionUtils.isEmpty(bo.getShopeeSkus())) {
            //查询产品系统
            List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(sku));
            if (CollectionUtils.isNotEmpty(skuInfoList)) {
                List<StockKeepingUnitWithBLOBs> skuList = ShopeeProductInfoUtil.convertSkuInfo(skuInfoList, null);
                //获取子属性sku
                List<ShopeeSku> shopeeSkus = ShopeeProductInfoUtil.getShopeeChildSkus(skuList, skuInfoList);
//                    if(ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(site)){
//                        for (ShopeeSku o : shopeeSkus) {
//                            o.setSize(o.getChineseSize());
//                            o.setColor(o.getChineseColor());
//                        }
//                    }
                bo.setShopeeSkus(shopeeSkus);
            }
        }

        return ApiResult.newSuccess(bo);
    }


    /***临时接口 start***/
    @GetMapping("/sendMq/{tempId}/{isParent}")
    public ApiResult sendMq(@PathVariable Integer tempId, @PathVariable Boolean isParent) {
        String table = ShopeeTemplateNewUtil.getShopeeTemplateTable(isParent);
        ShopeeTemplateNew shopeeTemplate = shopeeTemplateNewService.selectByPrimaryKey(tempId, table);
        ResponseJson resp = shopeePublishProducer.sendMq(shopeeTemplate, "10005");
        if (!resp.isSuccess()) {
            return ApiResult.newError(resp.getMessage());
        }
        return ApiResult.newSuccess();
    }

    @GetMapping("/getV2SiteList")
    public ApiResult getV2SiteList() {
        return ApiResult.newSuccess(CommonSharedCache.V2_SITE_LIST);
    }

    @GetMapping("/setV2SiteList")
    public ApiResult setV2SiteList() {
        ShopeeApplicationRunner bean = SpringUtils.getBean(ShopeeApplicationRunner.class);
        try {
            CommonSharedCache.V2_SITE_LIST.clear();
            bean.run(null);
            return ApiResult.newSuccess(CommonSharedCache.V2_SITE_LIST);
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.newError(e.getMessage());
        }
    }
    /***临时接口 end***/

}
