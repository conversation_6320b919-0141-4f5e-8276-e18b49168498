package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.component.ShopeeItemEsBulkProcessor;
import com.estone.erp.publish.shopee.dto.ShopeeMqAccountSitesDto;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.data.domain.Page;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * shopee 修改item 标题描述 监听
 */
@Slf4j
public class ShopeeUpdateItemTitleDescByInfringWordsMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeItemEsService shopeeItemEsService;
    @Resource
    private ShopeeItemEsBulkProcessor shopeeItemEsBulkProcessor;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            if(StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            ShopeeMqAccountSitesDto dto = JSON.parseObject(body, new TypeReference<>() {
            });

            boolean sign = false;
            try {
                checkInfringingWordsByAccount(dto);
                sign = true;
            }catch (Exception e) {
                log.error("ShopeeUpdateItemTitleDescByInfringWordsMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if(sign){
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }else{
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            }
            catch (IOException ioe) {
                log.warn("ShopeeUpdateItemTitleDescByInfringWordsMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        }catch (Exception e) {
            log.error("ShopeeUpdateItemTitleDescByInfringWordsMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.warn("ShopeeUpdateItemTitleDescByInfringWordsMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    private void checkInfringingWordsByAccount(ShopeeMqAccountSitesDto dto) {
        if(dto == null || StringUtils.isBlank(dto.getAccountNumber())) {
            log.error("ShopeeUpdateItemTitleDescByInfringWordsMqListener 无账号 不执行");
        }
        String accountNumber= dto.getAccountNumber();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("修改标题描述 根据侵权词");
        String maxId = null;

        int limit = 1000;
        int offset = 0;

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setPageFields(new String[]{"id", "itemId", "site", "spu", "itemSeller", "name", "description", "infringementWord", "infringementWordInfos", "descImgMapping", "descriptionType"});
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setItemSeller(accountNumber);
        request.setExistInfringementWordInfos(true);
        request.setSiteList(dto.getSites());
        List<String> itemIds = dto.getItemIds();
        if (CollectionUtils.isNotEmpty(itemIds)) {
            request.setItemIdList(itemIds);
        }
        request.setIsFather(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        while (true) {
            Page<EsShopeeItem> page = shopeeItemEsService.page(request, limit, offset);
            if (page == null || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            if(maxId == null) {
                log.info(accountNumber + "账号 修改标题描述 根据侵权词 数量" + page.getTotalElements());
            }

            List<EsShopeeItem> esShopeeItems = page.getContent();
            maxId = esShopeeItems.get(esShopeeItems.size() - 1).getId();
            for (EsShopeeItem esShopeeItem : esShopeeItems) {
                ShopeeExecutors.executeTitleDesc(() -> {
                    try {
                        shopeeItemEsService.updateProductTitleDesc(esShopeeItem, "admin");
                    }catch (Exception e) {
                        log.error(esShopeeItem.getItemId() + "修改标题描述 根据侵权词出错" + e.getMessage(), e);
                    }
                });
            }
            offset += limit;
        }
        stopWatch.stop();
        log.info(accountNumber + stopWatch.prettyPrint());
        log.info("账号{} 修改标题描述 根据侵权词{}", accountNumber ,  stopWatch.prettyPrint());
    }
}