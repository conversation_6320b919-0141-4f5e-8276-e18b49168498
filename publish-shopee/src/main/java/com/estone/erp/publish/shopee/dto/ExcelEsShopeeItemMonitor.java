package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItemMonitor;
import com.estone.erp.publish.platform.enums.ItemMonitorTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishRoleEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Date;

/**
 * @Auther yucm
 * @Date 2022/7/9
 */
@Slf4j
@Getter
@Setter
public class ExcelEsShopeeItemMonitor {

    /**
     * id唯一值 itemId + "_" + variationId
     */
    @ExcelIgnore
    private String id;

    /**
     * 平台ID
     */
    @ExcelProperty(value = "产品ID")
    private String itemId;

    /**
     * 帐号
     */
    @ExcelProperty(value = "店铺名称")
    private String itemSeller;

    /**
     * 货号
     */
    @ExcelProperty(value = "SKU")
    private String articleNumber;

    /**
     * 主货号 spu
     */
    @ExcelProperty(value = "SPU")
    private String spu;

    /**
     * item是否参加折扣
     */
    @ExcelProperty(value = "折扣")
    private String itemHasDiscount;

    /**
     * 价格
     */
    @ExcelProperty(value = "折扣价")
    private Double price;

    /**
     * 原始售价
     */
    @ExcelProperty(value = "原价")
    private Double originalPrice;

    /**
     * sip成本价
     */
    @ExcelProperty(value = "sip成本价")
    private Double sipItemPrice;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String name;

    /**
     * 上架时间
     */
    @ExcelProperty(value = "上架时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadDate;

    /**
     * 下架时间
     */
    @ExcelProperty(value = "下架时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downDate;

    /**
     * 刊登角色
     */
    @ExcelProperty(value = "刊登角色")
    private String publishRole;

    /**
     * 单品状态
     */
    @ExcelProperty(value = "单品状态")
    private String skuStatus;

    @ExcelProperty(value = "产品类目")
    private String proCategoryCnName;

    /**
     * 24小时销量
     */
    @ExcelProperty(value = "24H销量")
    private Integer order24HCount ;

    /**
     * 7天销量
     */
    @ExcelProperty(value = "7天销量")
    private Integer orderLast7dCount;

    /**
     * 14天销量
     */
    @ExcelProperty(value = "14天销量")
    private Integer orderLast14dCount;

    /**
     * 30天销量
     */
    @ExcelProperty(value = "30天销量")
    private Integer orderLast30dCount;

    /**
     * 总销量
     */
    @ExcelProperty(value = "总销量")
    private Integer orderNumTotal;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "跟踪时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationDate;

    /**
     * 监控类型 1.在线时间， 2.销量
     */
    @ExcelProperty(value = "监控类型")
    private String monitorType;

    /**
     * 监控详情
     */
    @ExcelProperty(value = "监控详情")
    private String monitorContent;

    // 销售
    @ExcelProperty(value = "销售")
    private String salemanager;

    // 销售组长
    @ExcelProperty(value = "销售组长")
    private String salemanagerLeader;

    // 销售主管
    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    public ExcelEsShopeeItemMonitor() {}

    public ExcelEsShopeeItemMonitor(EsShopeeItemMonitor monitor) {
        this.setId(monitor.getId());
        try {
            this.setItemId(monitor.getItemId());
            this.setItemSeller(monitor.getItemSeller());
            this.setArticleNumber(monitor.getArticleNumber());
            this.setSpu(monitor.getSpu());
            this.setItemHasDiscount(BooleanUtils.isTrue(monitor.getItemHasDiscount())? "是" : "否");
            this.setPrice(monitor.getPrice());
            this.setOriginalPrice(monitor.getOriginalPrice());
            this.setSipItemPrice(monitor.getSipItemPrice());
            this.setName(monitor.getName());
            this.setUploadDate(monitor.getUploadDate());
            this.setDownDate(monitor.getDownDate());
            String publishRoleName = null != monitor.getPublishRole() ? ShopeePublishRoleEnum.getNameByCode(monitor.getPublishRole()) : "";
            this.setPublishRole(publishRoleName);
            this.setSkuStatus(SkuStatusEnum.buildName(monitor.getSkuStatus()));
            this.setOrder24HCount(monitor.getOrder24HCount());
            this.setOrderLast7dCount(monitor.getOrderLast7dCount());
            this.setOrderLast14dCount(monitor.getOrderLast14dCount());
            this.setOrderLast30dCount(monitor.getOrderLast30dCount());
            this.setOrderNumTotal(monitor.getOrderNumTotal());
            this.setCreationDate(monitor.getCreationDate());
            this.setProCategoryCnName(monitor.getProCategoryCnName());
            this.setMonitorType(ItemMonitorTypeEnum.getNameByCode(monitor.getMonitorType()));
            this.setMonitorContent(monitor.getMonitorContent());
        }catch (Exception e) {
            log.error("链接监控导出存在对象转换异常" + e.getMessage(), e);
        }
    }
}
