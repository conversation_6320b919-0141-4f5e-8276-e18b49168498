package com.estone.erp.publish.shopee.api.v2.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12 9:18
 * @description
 */
@Getter
@Setter
public class GetAttributesV2 implements RequestCommon{

    @JSONField(name = "category_id")
    private Integer categoryId;

    @JSONField(name = "language")
    private String language = "en";


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_ATTRIBUTES;
    }
}
