package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import lombok.Data;

import java.util.List;

/**
 * @Description: ShopeeConfigUpdateStockDTO
 * <AUTHOR>
 * @Date 2025/2/12 16:53
 */
@Data
public class ShopeeConfigUpdateStockDTO {

    /**
     * 店铺
     */
    private String account;

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 调库存规则
     */
    private ShopeeAdjustInventoryRuleConfig adjustInventoryRuleConfig;

    /**
     * 链接数据
     */
    private List<EsShopeeItem> shopeeItems;

}
