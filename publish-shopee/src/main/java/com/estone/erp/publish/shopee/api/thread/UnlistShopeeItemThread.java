package com.estone.erp.publish.shopee.api.thread;

import java.util.List;
import java.util.concurrent.CountDownLatch;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.shopee.call.ShopeeUnlistItemCall;
import com.estone.erp.publish.shopee.model.ShopeeItem;

public class UnlistShopeeItemThread implements Runnable {

    private static final Log logger = LogFactory.getLog(UnlistShopeeItemThread.class);

    private CountDownLatch countDownLatch;

    // 同一个账号的集合
    private List<ShopeeItem> items;

    private ResponseJson response;

    private boolean unlist;

    public UnlistShopeeItemThread(List<ShopeeItem> items, boolean unlist, CountDownLatch countDownLatch,
            ResponseJson response) {
        super();
        this.items = items;
        this.countDownLatch = countDownLatch;
        this.response = response;
        this.unlist = unlist;
    }

    @Override
    public void run() {

        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        String itemSeller = items.get(0).getItemSeller();
        if (StringUtils.isBlank(itemSeller)) {
            return;
        }

        try {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, itemSeller, true);
            if (account == null) {
                return;
            }

            ShopeeUnlistItemCall call = new ShopeeUnlistItemCall();
            ResponseJson rsp = call.unlistItems(account, items, unlist);

            List<ResponseError> errors = rsp.getErrors();

            if (CollectionUtils.isEmpty(errors)) {
                for (ShopeeItem item : items) {
                    response.addError(new ResponseError(StatusCode.FAIL, item.getItemId(), "请求失败"));
                }
            }
            else {
                for (ResponseError responseError : errors) {
                    response.addError(responseError);
                }
            }

        }
        catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        finally {
            countDownLatch.countDown();
        }

    }

}
