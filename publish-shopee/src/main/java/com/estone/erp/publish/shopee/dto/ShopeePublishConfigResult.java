package com.estone.erp.publish.shopee.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * @Description: ShopeePublishConfigResult
 * <AUTHOR>
 * @Date 2025/2/24 11:54
 */
@Data
@AllArgsConstructor
public class ShopeePublishConfigResult {

    /**
     * 状态
     * 1-若SPU近30天出单量大于等于X，且SPU在线链接大于等于N个时，则不生成队列；
     * 2-若SPU近30天出单量大于等于X，且SPU在线链接小于N个时，SPU生成队列数补齐到N
     * 3-若SPU近30天出单量小于X时，SPU正常生成队列；
     */
    private int status;

    /**
     * SPU
     */
    private String spu;

    /**
     * 在线链接数
     */
    private int onlineLinkCount;

    /**
     * 允许刊登数
     */
    private int publishCount;

    /**
     * 30天出单数
     */
    private int orderCount30Days;

}
