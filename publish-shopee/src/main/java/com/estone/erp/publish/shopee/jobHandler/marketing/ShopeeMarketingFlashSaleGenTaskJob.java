package com.estone.erp.publish.shopee.jobHandler.marketing;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.shopee.enums.PublishOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.MarketingStatusEnum;
import com.estone.erp.publish.shopee.mq.model.ShopeeFlashSaleGenGoodsDto;
import com.estone.erp.publish.tidb.publishtidb.model.*;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeMarketingFlashSaleService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * shopee 关注礼物任务生成
 */
@Slf4j
@Component
public class ShopeeMarketingFlashSaleGenTaskJob extends AbstractJobHandler {

    @Resource
    private ShopeeMarketingFlashSaleService shopeeMarketingFlashSaleService;

    @Resource
    private RabbitMqSender rabbitMqSender;
    @Getter
    @Setter
    public static class InnerParam {
        /**
         * 需要跑的店铺
         */
        private List<String> accountList;
    }

    public ShopeeMarketingFlashSaleGenTaskJob() {
        super("ShopeeMarketingFlashSaleGenTaskJob");
    }

    @XxlJob("ShopeeMarketingFlashSaleGenTaskJob")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("ShopeeMarketingFlashSaleGenTaskJob start, param: " + param);

        InnerParam innerParam = Optional.ofNullable(passParam(param, InnerParam.class)).orElseGet(InnerParam::new);
        List<String> accountList = innerParam.getAccountList();
        List<ShopeeMarketingFlashSale> shopeeMarketingFlashSaleTasks = shopeeMarketingFlashSaleService.getCompleteCanApplyGoodTaskAndNotApplyTask(accountList);
        if (CollectionUtils.isEmpty(shopeeMarketingFlashSaleTasks)) {
            XxlJobLogger.log("没有需要生成添加商品进秒杀活动的任务");
        }

        List<ShopeeMarketingFlashSale> successTask = shopeeMarketingFlashSaleTasks.stream().filter(a -> a.getCrawStatus().equals(MarketingStatusEnum.SUCCESS.getCode())).collect(Collectors.toList());
        List<ShopeeMarketingFlashSale> errorTask = shopeeMarketingFlashSaleTasks.stream().filter(a -> a.getCrawStatus().equals(MarketingStatusEnum.FAILED.getCode())).collect(Collectors.toList());
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        if (CollectionUtils.isNotEmpty(errorTask)) {
            List<ShopeeMarketingFlashSale> updateList = new ArrayList<>(errorTask.size());
            // 将信息都保存一下
            for (ShopeeMarketingFlashSale old : errorTask) {
                // 创建一条在线列表的数据
                old.setSubmitStatus(2);
                old.setSubmitTime(new Timestamp(System.currentTimeMillis()));
                old.setPublishOperatorStatus(PublishOperatorStatusEnum.SUCCESS.getCode());
                old.setPublishOperatorStatusUpdateTime(timestamp);
                updateList.add(old);
            }
            Set<String> collect = updateList.stream().map(ShopeeMarketingFlashSale::getAccountNumber).collect(Collectors.toSet());
            shopeeMarketingFlashSaleService.updateBatchById(updateList, 300);
            XxlJobLogger.log("爬虫爬取提添加商品失败的店铺[{}]", collect);
        }
        if (CollectionUtils.isNotEmpty(successTask)) {
            List<ShopeeMarketingFlashSale> updateList = new ArrayList<>(successTask.size());
            for (ShopeeMarketingFlashSale old : successTask) {
                ShopeeMarketingFlashSale newV = new ShopeeMarketingFlashSale();
                newV.setId(old.getId());
                newV.setAccountNumber(old.getAccountNumber());
                newV.setPublishOperatorStatus(PublishOperatorStatusEnum.RUNNING.getCode());
                newV.setPublishOperatorStatusUpdateTime(timestamp);
                updateList.add(newV);
            }
            shopeeMarketingFlashSaleService.updateBatchById(updateList, 300);

            // 发送到队列去执行生成数据
            for (ShopeeMarketingFlashSale marketingFlashSale : updateList) {
                Long flashRecordId = marketingFlashSale.getId();
                Integer marketingId = marketingFlashSale.getMarketingId();
                String accountNumber = marketingFlashSale.getAccountNumber();
                send(accountNumber, flashRecordId, marketingId);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void send(String accountNumber, Long flashRecordId, Integer marketingId) {
        // TODO 发送消息到队列
        ShopeeFlashSaleGenGoodsDto shopeeFlashSaleGenGoodsDto = new ShopeeFlashSaleGenGoodsDto();
        shopeeFlashSaleGenGoodsDto.setFlashRecordId(flashRecordId);
        shopeeFlashSaleGenGoodsDto.setAccountNumber(accountNumber);
        shopeeFlashSaleGenGoodsDto.setMarketingId(marketingId);
        rabbitMqSender.publishShopeeVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_KEY, shopeeFlashSaleGenGoodsDto);
    }

}
