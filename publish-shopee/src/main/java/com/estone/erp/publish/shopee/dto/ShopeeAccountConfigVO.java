package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.shopee.annotation.NeedToLog;
import lombok.Data;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/10 下午2:26
 */
@Data
public class ShopeeAccountConfigVO {

    /**
     * 子账号
     */
    private String subAccount;

    /**
     * 商家名称
     */
    private String merchant;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 站点
     */
    private String site;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 是否自动删除违规产品
     */
    @NeedToLog
    private Boolean automaticDeleteViolationProduct;

    /**
     * 是否自动修改违规产品
     */
    @NeedToLog
    private Boolean automaticUpdateViolationProduct;
}
