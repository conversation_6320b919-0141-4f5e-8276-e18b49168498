package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/24 14:39
 */
@Data
public class ExcelShopeeListingStatistics {

    /**
     * 类型
     */
    @ExcelProperty("类型")
    private String type;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String saleName;

    /**
     * 店铺
     */
    @ExcelProperty("店铺")
    private String itemSeller;

    /**
     * 站点
     */
    @ExcelProperty("站点")
    private String site;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String status;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    private Integer amount;

    /**
     * 统计时间
     */
    @ExcelProperty("统计时间")
    private Date statisticsDate;

}
