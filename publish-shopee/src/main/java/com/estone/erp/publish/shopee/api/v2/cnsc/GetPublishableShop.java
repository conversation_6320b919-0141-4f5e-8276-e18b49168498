package com.estone.erp.publish.shopee.api.v2.cnsc;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/29 15:14
 * @description
 */
@Getter
@Setter
public class GetPublishableShop implements RequestCommon {

    @JSONField(name = "global_item_id")
    private Long globalItemId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_PUBLISHABLE_SHOP;
    }
}
