package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.SellerStock;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:38
 * @description
 */
@Getter
@Setter
public class ModelDtoV2 {

    @JSONField(name = "model_id")
    private Long modelId;
    @JSONField(name = "tier_index")
    private List<Integer> tierIndex = new ArrayList<>();
    @JSONField(name = "original_price")
    private Double originalPrice;
    @JSONField(name = "model_sku")
    private String modelSku;
    @JSONField(name = "seller_stock")
    private List<SellerStock> sellerStocks;
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;
}
