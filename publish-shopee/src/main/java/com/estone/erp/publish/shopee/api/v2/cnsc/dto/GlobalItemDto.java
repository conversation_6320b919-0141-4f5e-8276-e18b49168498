package com.estone.erp.publish.shopee.api.v2.cnsc.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeImageParam;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeLogisticParam;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.GlobalModelDto;
import com.estone.erp.publish.shopee.api.v2.param.add.ImageDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.TierVariationDtoV2;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:35
 * @description
 */
@Getter
@Setter
public class GlobalItemDto {

    /** 标题*/
    @JSONField(name = "item_name")
    private String itemName;

    /** Description of item*/
    @JSONField(name = "description")
    private String description;

    /** Item status, could be UNLIST or NORMAL*/
    @JSONField(name = "item_status")
    private String itemStatus;

    /** Item price*/
    @JSONField(name = "original_price")
    private Double originalPrice;

    /** 图片 */
    @JSONField(name = "image")
    private ImageDtoV2 image;

    /** 类别编号*/
    @JSONField(name = "category_id")
    private Integer categoryId ;

    /** 全局项目的层变化信息。限制为 [1,2] */
    @JSONField(name = "tier_variation")
    private List<TierVariationDtoV2> tierVariation;

    /** Model info list, length should be between 1 to 50 */
    @JSONField(name = "model")
    private List<GlobalModelDto> model;

    /** 物流渠道设置 */
    @JSONField(name = "logistic")
    private List<ShopeeLogisticParam> logistic;

    /** 预购设定 */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    public GlobalItemDto(){}

    /**
     * 如果重新使用，得看下描述
     * @param account
     * @param template
     * @param imgMappingMap
     */
    @Deprecated
    public GlobalItemDto(SaleAccountAndBusinessResponse account, ShopeeTemplateNew template, Map<String, String> imgMappingMap) {

        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        List<ShopeeImageParam> imgList = JSONArray.parseArray(template.getImagesStr(), ShopeeImageParam.class);
        List<ShopeeImageParam> deleteImageList = new ArrayList<>();
        for (int i = 1; i < imgList.size(); i++) {
            if(imgList.get(i).getUrl().equals(imgList.get(0).getUrl())) {
                deleteImageList.add(imgList.get(i));
            }
        }
        imgList.removeAll(deleteImageList);
        if (imgList.size() > 9) {
            imgList = new ArrayList<>(imgList.subList(0, 9));
        }
        //转换图片，把图片转换成阿里云的地址
        for (ShopeeImageParam image : imgList) {
            String url = image.getUrl();
            //替换图片为 image_id
            url = imgMappingMap.get(url);
            this.image.getImageIdList().add(url);
        }

        // 物流
        this.logistic = JSONArray.parseArray(template.getLogisticsStr(),ShopeeLogisticParam.class);
        if(CollectionUtils.isNotEmpty(logistic)){
            logistic.stream().forEach(o -> o.setSite(null));
        }

        // 台湾站点用中文标题和描述
//        String keyword = StringUtils.isEmpty(template.getKeyword())?"":template.getKeyword() + "\n";
        this.itemName = template.getName();
        this.description = template.getDescription();
        //标题不能超过255个字符
        if(this.itemName.length() > 255) {
            this.itemName = this.itemName.substring(0,255);
        }
    }
}
