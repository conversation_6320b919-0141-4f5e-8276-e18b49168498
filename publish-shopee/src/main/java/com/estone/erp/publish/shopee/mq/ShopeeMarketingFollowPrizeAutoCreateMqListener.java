package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.shopee.component.marking.FollowPrizeConfigParam;
import com.estone.erp.publish.shopee.enums.ShopeeConfigOperatorStatusEnum;
import com.estone.erp.publish.shopee.handler.ShopeeFollowPrizeHandler;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeConfigTask;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/12 下午6:08
 */
@Slf4j
public class ShopeeMarketingFollowPrizeAutoCreateMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Resource
    private ShopeeFollowPrizeHandler shopeeFollowPrizeHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        FeedTask feedTask = JSON.parseObject(body, new TypeReference<FeedTask>() {
        });

        Integer id = Integer.valueOf(feedTask.getAttribute5());
        ShopeeConfigTask configTask = shopeeConfigTaskService.selectByPrimaryKey(id);
        if (Objects.isNull(configTask) && !ShopeeConfigOperatorStatusEnum.WAITING.getCode().equals(configTask.getOperatorStatus())) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            return;
        }

        try {
            // 解析配置规则
            FollowPrizeConfigParam followPrizeConfigParam = JSON.parseObject(configTask.getConfigRuleJson(), FollowPrizeConfigParam.class);

            // 已维护状态信息和处理报告
            ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByAccountNumber(configTask.getAccountNumber());
            shopeeFollowPrizeHandler.addFollowPrize(shopeeAccountConfig, followPrizeConfigParam, feedTask, id, configTask.getConfigId());

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
