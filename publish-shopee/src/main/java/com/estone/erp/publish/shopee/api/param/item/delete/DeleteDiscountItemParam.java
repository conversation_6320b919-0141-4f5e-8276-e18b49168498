package com.estone.erp.publish.shopee.api.param.item.delete;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

public class DeleteDiscountItemParam implements IRequestUrlApiKey {

    @JSONField(name = "discount_id")
    private Long discountId;

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "variation_id")
    private Long variationId;

    @JSONField(name = "partner_id")
    private Long partnerId;

    @JSONField(name = "shopid")
    private Long shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(serialize = false)
    private String apiKey;

    public DeleteDiscountItemParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Long.valueOf(account.getColStr1());
        this.shopId = Long.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.DISCOUNT_DELETE;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public Long getVariationId() {
        return variationId;
    }

    public void setVariationId(Long variationId) {
        this.variationId = variationId;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
}
