package com.estone.erp.publish.shopee.component.download;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.shopee.component.download.template.ShopeeNoModelDownloadService;
import com.estone.erp.publish.shopee.component.download.template.dto.NoModelWriteData;
import com.estone.erp.publish.shopee.enums.SiteListingEnum;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductPublishDo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductPublishVo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewPublishLeaderInfo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewPublishLeaderSiteInfo;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewPublishSiteInfo;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductPublishService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 新品推荐下载服务
 * <AUTHOR>
 * @Date 2025/3/6 10:27
 */
@Component
public class ShopeeNewProductPublishDownload extends ShopeeNoModelDownloadService {

    @Resource
    private ShopeeNewProductPublishService shopeeNewProductPublishService;

    @Override
    public NoModelWriteData getDataList(ExcelDownloadLog excelDownloadLog) {
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_PUBLISH_CONFIG", "site_publish_completion_days", 10);

        String queryCondition = excelDownloadLog.getQueryCondition();
        ShopeeNewProductPublishDo search = JSON.parseObject(queryCondition, ShopeeNewProductPublishDo.class);

        int current = 1;
        int size = 100;

        List<ShopeeNewProductPublishVo> dataList = new ArrayList<>();
        while (true) {
            Page<ShopeeNewProductPublishVo> queryPage = new Page<>(current, size);
            IPage<ShopeeNewProductPublishVo> page = shopeeNewProductPublishService.page(queryPage, search);
            List<ShopeeNewProductPublishVo> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            current++;
            dataList.addAll(records);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            throw new IllegalArgumentException("没有查询到数据");
        }

        NoModelWriteData noModelWriteData = new NoModelWriteData();
        List<Map<String, Object>> dataMapList = new ArrayList<>();

        Boolean isAdmin = search.getIsAdmin();
        List<NewUser> leaderList = new ArrayList<>();
        // 这里是按照现有组长的来做
        // 后续如果不行，就需要用 下面来获取所有的组长
        // Set<String> collect1 = dataList.stream().map(ShopeeNewProductPublishVo::getLeaderInfos).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).map(ShopeeNewPublishLeaderInfo::getSaleLeader).collect(Collectors.toSet());
        if (isAdmin) {
            // 管理员下载所有数据
            List<NewUser> inUseEmplByPlatformAndPosition = NewUsermgtUtils.getInUseEmplByPlatformAndPosition(SaleChannel.CHANNEL_SHOPEE, RoleConstant.GROUP_LEADER);
            leaderList.addAll(inUseEmplByPlatformAndPosition);
        } else {
            Boolean isLeader = search.getIsLeader();
            if (BooleanUtils.isTrue(isLeader)) {
                ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(search.getEmployeeNo());
                leaderList.add(userByNo.getResult());
            }
        }
        List<String> siteList = SiteListingEnum.getSiteList();
        setHeaderAndKey(noModelWriteData, leaderList, siteList, systemParamValue);
        for (ShopeeNewProductPublishVo shopeeNewProductPublishVo : dataList) {
            // 处理数据
            Map<String, Object> data = new HashMap<>();
            data.put("SPU", shopeeNewProductPublishVo.getSpu());
            data.put("itemStatus", SkuStatusEnum.buildName(shopeeNewProductPublishVo.getItemStatus()));
            data.put("enterProductTime", shopeeNewProductPublishVo.getEnterProductTime());
            data.put("pushTime", shopeeNewProductPublishVo.getPushTime());
            data.put("sitesPublishCompleteTime", shopeeNewProductPublishVo.getSitesPublishCompleteTime());
            data.put("oneListingNumber", shopeeNewProductPublishVo.getOneListingNumber());
            data.put("oneListingNumberPercent", shopeeNewProductPublishVo.getOneListingNumberPercent());
            data.put("twoListingNumber", shopeeNewProductPublishVo.getTwoListingNumber());
            for (String site : siteList) {
                ShopeeNewPublishSiteInfo siteInfo = shopeeNewProductPublishVo.getSiteInfos().stream().filter(s -> s.getSiteCode().equals(site)).findFirst().orElse(null);
                if (siteInfo != null) {
                    data.put(getSiteOneListingNumberKey(site), siteInfo.getOneListingNumber());
                    data.put(getSiteOneListingNumberPercentKey(site), siteInfo.getOneListingNumberPercent());
                    data.put(getSiteTwoListingNumberKey(site), siteInfo.getTwoListingNumber());
                    data.put(getSiteIsBannedKey(site), BooleanUtils.isTrue(siteInfo.getIsBanned()) ? "是":"否");
                } else {
                    // 存在数据异常的时候，得补充下字段
                    data.put(getSiteOneListingNumberKey(site), "");
                    data.put(getSiteOneListingNumberPercentKey(site), "");
                    data.put(getSiteTwoListingNumberKey(site), "");
                    data.put(getSiteIsBannedKey(site), "");
                }
            }
            List<ShopeeNewPublishLeaderInfo> leaderInfos = shopeeNewProductPublishVo.getLeaderInfos();
            if (CollectionUtils.isEmpty(leaderInfos)) {
                leaderInfos = new ArrayList<>();
            }
            Map<String, ShopeeNewPublishLeaderInfo> employeeInfoMap = leaderInfos.stream().collect(Collectors.toMap(ShopeeNewPublishLeaderInfo::getSaleLeader, a -> a, (a, b) -> a));
            for (NewUser leader : leaderList) {
                ShopeeNewPublishLeaderInfo defaultInfo = new ShopeeNewPublishLeaderInfo();
                defaultInfo.setSaleLeader(leader.getEmployeeNo());
                defaultInfo.setSaleName(leader.getName());
                ShopeeNewPublishLeaderInfo leaderInfo = employeeInfoMap.getOrDefault(leader.getEmployeeNo(), defaultInfo);
                List<ShopeeNewPublishLeaderSiteInfo> leaderSiteList = leaderInfo.getSiteList();
                Map<String, Double> collect = leaderSiteList.stream().collect(Collectors.toMap(ShopeeNewPublishLeaderSiteInfo::getSiteCode, ShopeeNewPublishLeaderSiteInfo::getPercent, (a, b) -> a));
                data.put(getLeaderOneListingNumberPercentKey(leader.getEmployeeNo()), leaderInfo.getPercent());
                for (String site : siteList) {
                    data.put(getLeaderSiteOneListingNumberPercentKey(leader.getEmployeeNo(), site), collect.get(site));
                }
            }
            dataMapList.add(data);
        }
        noModelWriteData.setDataList(dataMapList);
        // 更新下载记录
        excelDownloadLog.setDownloadCount(dataList.size());
        return noModelWriteData;
    }

    @Override
    public ShopeeDownloadTypeEnums downloadType() {
        return ShopeeDownloadTypeEnums.NEW_PRODUCT_PUBLISH;
    }

    public void setHeaderAndKey(NoModelWriteData noModelWriteData, List<NewUser> leaderList, List<String> siteList, String systemParamValue) {
        List<String> headerKey = new ArrayList<>();
        List<String> header = new ArrayList<>();

        header.add("SPU");
        headerKey.add("SPU");

        header.add("单品状态");
        headerKey.add("itemStatus");

        header.add("进入单品时间");
        headerKey.add("enterProductTime");

        header.add("推送时间");
        headerKey.add("pushTime");

        header.add(systemParamValue + "个站点刊登完成时间");
        headerKey.add("sitesPublishCompleteTime");

        for (String site : siteList) {
            header.add(site + "站点是否禁售");
            headerKey.add(getSiteIsBannedKey(site));
        }

        header.add("近7天在线链接数");
        headerKey.add("oneListingNumber");

        for (String site : siteList) {
            header.add(site + "站点近7天在线链接数");
            headerKey.add(getSiteOneListingNumberKey(site));
        }

        header.add("达成率");
        headerKey.add("oneListingNumberPercent");

        for (String site : siteList) {
            header.add(site + "站点达成率");
            headerKey.add(getSiteOneListingNumberPercentKey(site));
        }

        header.add("近30天在线链接数");
        headerKey.add("twoListingNumber");

        for (String site : siteList) {
            header.add(site + "站点近30天在线链接数");
            headerKey.add(getSiteTwoListingNumberKey(site));
        }

        for (NewUser user : leaderList) {
            header.add(user.getName() + "达成率");
            headerKey.add(getLeaderOneListingNumberPercentKey(user.getEmployeeNo()));
            for (String site : siteList) {
                header.add(user.getName() + site + "站点达成率");
                headerKey.add(getLeaderSiteOneListingNumberPercentKey(user.getEmployeeNo(), site));
            }
        }

        noModelWriteData.setHeadMap(header.toArray(new String[0]));
        noModelWriteData.setDataStrMap(headerKey.toArray(new String[0]));
    }

    private String getSiteIsBannedKey(String site) {
        return site + "_" + "isBanned";
    }

    public String getSiteOneListingNumberKey(String site) {
        return site + "_" + "oneListingNumber";
    }

    public String getSiteOneListingNumberPercentKey(String site) {
        return site + "_" + "oneListingNumberPercent";
    }

    public String getSiteTwoListingNumberKey(String site) {
        return site + "_" + "twoListingNumber";
    }

    public String getLeaderOneListingNumberPercentKey(String employeeNo) {
        return employeeNo + "_" + "oneListingNumberPercent";
    }

    public String getLeaderSiteOneListingNumberPercentKey(String employeeNo, String site) {
        return employeeNo + "_" + site + "_" + "oneListingNumberPercent";
    }
}
