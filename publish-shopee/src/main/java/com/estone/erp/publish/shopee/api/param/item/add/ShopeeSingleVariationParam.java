package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * shopee产品多属性
 *
 * <AUTHOR>
 */
public class ShopeeSingleVariationParam {

    /** 名称 */
    @JSONField(name = "name")
    private String name;

    /** 库存 */
    @JSONField(name = "stock")
    private Integer stock;

    /** 价格 */
    @JSONField(name = "price")
    private Double price;

    /** 子sku货号 */
    @JSONField(name = "variation_sku")
    private String variationSku;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getVariationSku() {
        return variationSku;
    }

    public void setVariationSku(String variationSku) {
        this.variationSku = variationSku;
    }
}
