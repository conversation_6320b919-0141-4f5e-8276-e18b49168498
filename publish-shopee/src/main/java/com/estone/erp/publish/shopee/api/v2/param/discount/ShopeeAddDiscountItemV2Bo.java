package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 14:23
 * @description
 */
@Getter
@Setter
public class ShopeeAddDiscountItemV2Bo {

    /** 产品id (必填)*/
    @JSONField(name = "item_id")
    private Long itemId;

    /** 变体集合 */
    @JSONField(name = "model_list")
    private List<ShopeeAddDiscountItemV2ModelBo> modelList;

    /** 商品的折扣价。如果项目没有变化，则此参数是必填的 */
    @JSONField(name = "item_promotion_price")
    private Double itemPromotionPrice;

    /** 促销价中该产品的最大数量。 如果是无限制，请为该请求数据输入 0 (必填)*/
    @JSONField(name = "purchase_limit")
    private Integer purchaseLimit;

    /** 物料的预留库存 */
    @JSONField(name = "item_promotion_stock")
    private Integer itemPromotionStock;

}
