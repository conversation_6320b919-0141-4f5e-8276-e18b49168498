package com.estone.erp.publish.shopee.call.v2.cnsc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.shopee.api.v2.cnsc.global_item.GetGlobalModelList;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.model.ShopeeGlobalItem;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/14 18:14
 * @description
 */
@Slf4j
public class GetGlobalModelListCall {

    /**
     * @param cnscAccount
     * @param globalItemId 全球产品id
     * @return
     */
    public static ApiResult<List<ShopeeGlobalItem>> GetGlobalModelList(SaleAccountAndBusinessResponse cnscAccount, Long globalItemId) {
        if (globalItemId == null) {
            return ApiResult.newError("globalItemId 为空!");
        }

        List<ShopeeGlobalItem> itemList = new ArrayList<>(0);
        ApiResult<List<ShopeeGlobalItem>> result = ApiResult.newError(null);

        GetGlobalModelList param = new GetGlobalModelList();
        param.setGlobalItemId(globalItemId);

        ShopeeResponse response = ShopeeHttpUtils.doGetV2(cnscAccount, param);
        if (StringUtils.isNotBlank(response.getError())) {
            result.setErrorMsg(JSON.toJSONString(response));
//            log.info(cnscAccount.getAccountNumber() + ",获取GetGlobalModelList error :" + JSON.toJSONString(response));
        } else {
            JSONObject json = JSON.parseObject(response.getResponse());
            if (json == null) {
                result.setErrorMsg("GlobalModelList结果解析为空: " + JSON.toJSONString(response));
            } else {
                //解析数据
                try {
                    itemList = transModel(cnscAccount, json, globalItemId);
                } catch (Exception e) {
                    log.error(String.format("解析[%s]GlobalModelList  出错：", globalItemId), e);
                }
            }
        }

        result.setResult(itemList);
        result.setSuccess(true);
        return result;
    }

    private static List<ShopeeGlobalItem> transModel(SaleAccountAndBusinessResponse cnscAccount, JSONObject json, Long globalItemId) {
        List<ShopeeGlobalItem> list = new ArrayList<>();
        JSONArray global_models = json.getJSONArray("global_model");
        for (int i = 0; global_models != null && i < global_models.size(); i++) {
            JSONObject jsonItem = global_models.getJSONObject(i);

            ShopeeGlobalItem item = new ShopeeGlobalItem();
            item.setItemSeller(cnscAccount.getAccountNumber());
            item.setGlobalItemId(globalItemId);
            item.setVariationId(jsonItem.getString("global_model_id"));
            item.setVariationSku(jsonItem.getString("global_model_sku"));

            JSONObject price_info = jsonItem.getJSONObject("price_info");
            if (price_info != null) {
                item.setOriginalPrice(price_info.getDouble("original_price"));
                item.setPrice(price_info.getDouble("current_price"));
                if (item.getPrice() == null) {
                    item.setPrice(item.getOriginalPrice());
                }
            }

            JSONObject stock_info_v2 = jsonItem.getJSONObject("stock_info_v2");
            if (null != stock_info_v2) {
                JSONObject summary_info = stock_info_v2.getJSONObject("summary_info");
                if (null != summary_info) {
                    item.setStock(summary_info.getInteger("total_available_stock"));
                }
            }

            list.add(item);
        }

        return list;
    }
}
