package com.estone.erp.publish.tidb.publishtidb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description:Shopee新品范本完成率统计表DO
 * <AUTHOR>
 * @Date 2025/3/13 16:44
 */
@Data
public class ShopeeNewProductTemplateCompletionDO {

    /**
     * 主键id集合
     */
    private List<Long> idList;

    /**
     * 时间粒度
     * 日：day
     * 周：week
     * 月：month
     */
    private String timeType = "day";

    /**
     * 推送时间开始
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushTimeStart;

    /**
     * 推送时间结束
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pushTimeEnd;

    /**
     * 人员维度
     */
    private Integer saleType = 0;

    /**
     * 导出类型
     * 1：全部
     * 2：未完成
     */
    private Integer exportType;

    /**
     * 销售
     */
    private String sale;

    /**
     * 销售组长
     */
    private String saleLeader;

}
