package com.estone.erp.publish.shopee.api.v2.param.image;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/17 15:27
 * @description
 */
@Getter
@Setter
public class UploadImage implements RequestCommon {



    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPLOAD_IMAGE;
    }
}
