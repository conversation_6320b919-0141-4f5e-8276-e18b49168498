package com.estone.erp.publish.shopee.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ShopeeAdminGlobalTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    // 自定义查询字段
    private String columns;

    public ShopeeAdminGlobalTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSubAccountIsNull() {
            addCriterion("sub_account is null");
            return (Criteria) this;
        }

        public Criteria andSubAccountIsNotNull() {
            addCriterion("sub_account is not null");
            return (Criteria) this;
        }

        public Criteria andSubAccountEqualTo(String value) {
            addCriterion("sub_account =", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountNotEqualTo(String value) {
            addCriterion("sub_account <>", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountGreaterThan(String value) {
            addCriterion("sub_account >", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountGreaterThanOrEqualTo(String value) {
            addCriterion("sub_account >=", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountLessThan(String value) {
            addCriterion("sub_account <", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountLessThanOrEqualTo(String value) {
            addCriterion("sub_account <=", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountLike(String value) {
            addCriterion("sub_account like", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountNotLike(String value) {
            addCriterion("sub_account not like", value, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountIn(List<String> values) {
            addCriterion("sub_account in", values, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountNotIn(List<String> values) {
            addCriterion("sub_account not in", values, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountBetween(String value1, String value2) {
            addCriterion("sub_account between", value1, value2, "subAccount");
            return (Criteria) this;
        }

        public Criteria andSubAccountNotBetween(String value1, String value2) {
            addCriterion("sub_account not between", value1, value2, "subAccount");
            return (Criteria) this;
        }

        public Criteria andMerchantIsNull() {
            addCriterion("merchant is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIsNotNull() {
            addCriterion("merchant is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantEqualTo(String value) {
            addCriterion("merchant =", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantNotEqualTo(String value) {
            addCriterion("merchant <>", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantGreaterThan(String value) {
            addCriterion("merchant >", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantGreaterThanOrEqualTo(String value) {
            addCriterion("merchant >=", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantLessThan(String value) {
            addCriterion("merchant <", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantLessThanOrEqualTo(String value) {
            addCriterion("merchant <=", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantLike(String value) {
            addCriterion("merchant like", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantNotLike(String value) {
            addCriterion("merchant not like", value, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantIn(List<String> values) {
            addCriterion("merchant in", values, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantNotIn(List<String> values) {
            addCriterion("merchant not in", values, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantBetween(String value1, String value2) {
            addCriterion("merchant between", value1, value2, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantNotBetween(String value1, String value2) {
            addCriterion("merchant not between", value1, value2, "merchant");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdEqualTo(String value) {
            addCriterion("merchant_id =", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotEqualTo(String value) {
            addCriterion("merchant_id <>", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThan(String value) {
            addCriterion("merchant_id >", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_id >=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThan(String value) {
            addCriterion("merchant_id <", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThanOrEqualTo(String value) {
            addCriterion("merchant_id <=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLike(String value) {
            addCriterion("merchant_id like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotLike(String value) {
            addCriterion("merchant_id not like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIn(List<String> values) {
            addCriterion("merchant_id in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotIn(List<String> values) {
            addCriterion("merchant_id not in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdBetween(String value1, String value2) {
            addCriterion("merchant_id between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotBetween(String value1, String value2) {
            addCriterion("merchant_id not between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNull() {
            addCriterion("accounts is null");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNotNull() {
            addCriterion("accounts is not null");
            return (Criteria) this;
        }

        public Criteria andAccountsEqualTo(String value) {
            addCriterion("accounts =", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotEqualTo(String value) {
            addCriterion("accounts <>", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThan(String value) {
            addCriterion("accounts >", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThanOrEqualTo(String value) {
            addCriterion("accounts >=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThan(String value) {
            addCriterion("accounts <", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThanOrEqualTo(String value) {
            addCriterion("accounts <=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLike(String value) {
            addCriterion("accounts like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotLike(String value) {
            addCriterion("accounts not like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsIn(List<String> values) {
            addCriterion("accounts in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotIn(List<String> values) {
            addCriterion("accounts not in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsBetween(String value1, String value2) {
            addCriterion("accounts between", value1, value2, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotBetween(String value1, String value2) {
            addCriterion("accounts not between", value1, value2, "accounts");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andMtskuIsNull() {
            addCriterion("mtsku is null");
            return (Criteria) this;
        }

        public Criteria andMtskuIsNotNull() {
            addCriterion("mtsku is not null");
            return (Criteria) this;
        }

        public Criteria andMtskuEqualTo(String value) {
            addCriterion("mtsku =", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuNotEqualTo(String value) {
            addCriterion("mtsku <>", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuGreaterThan(String value) {
            addCriterion("mtsku >", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuGreaterThanOrEqualTo(String value) {
            addCriterion("mtsku >=", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuLessThan(String value) {
            addCriterion("mtsku <", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuLessThanOrEqualTo(String value) {
            addCriterion("mtsku <=", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuLike(String value) {
            addCriterion("mtsku like", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuNotLike(String value) {
            addCriterion("mtsku not like", value, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuIn(List<String> values) {
            addCriterion("mtsku in", values, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuNotIn(List<String> values) {
            addCriterion("mtsku not in", values, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuBetween(String value1, String value2) {
            addCriterion("mtsku between", value1, value2, "mtsku");
            return (Criteria) this;
        }

        public Criteria andMtskuNotBetween(String value1, String value2) {
            addCriterion("mtsku not between", value1, value2, "mtsku");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andKeywordIsNull() {
            addCriterion("keyword is null");
            return (Criteria) this;
        }

        public Criteria andKeywordIsNotNull() {
            addCriterion("keyword is not null");
            return (Criteria) this;
        }

        public Criteria andKeywordEqualTo(String value) {
            addCriterion("keyword =", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordNotEqualTo(String value) {
            addCriterion("keyword <>", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordGreaterThan(String value) {
            addCriterion("keyword >", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordGreaterThanOrEqualTo(String value) {
            addCriterion("keyword >=", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordLessThan(String value) {
            addCriterion("keyword <", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordLessThanOrEqualTo(String value) {
            addCriterion("keyword <=", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordLike(String value) {
            addCriterion("keyword like", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordNotLike(String value) {
            addCriterion("keyword not like", value, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordIn(List<String> values) {
            addCriterion("keyword in", values, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordNotIn(List<String> values) {
            addCriterion("keyword not in", values, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordBetween(String value1, String value2) {
            addCriterion("keyword between", value1, value2, "keyword");
            return (Criteria) this;
        }

        public Criteria andKeywordNotBetween(String value1, String value2) {
            addCriterion("keyword not between", value1, value2, "keyword");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andAttributesStrIsNull() {
            addCriterion("attributes_str is null");
            return (Criteria) this;
        }

        public Criteria andAttributesStrIsNotNull() {
            addCriterion("attributes_str is not null");
            return (Criteria) this;
        }

        public Criteria andAttributesStrEqualTo(String value) {
            addCriterion("attributes_str =", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrNotEqualTo(String value) {
            addCriterion("attributes_str <>", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrGreaterThan(String value) {
            addCriterion("attributes_str >", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrGreaterThanOrEqualTo(String value) {
            addCriterion("attributes_str >=", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrLessThan(String value) {
            addCriterion("attributes_str <", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrLessThanOrEqualTo(String value) {
            addCriterion("attributes_str <=", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrLike(String value) {
            addCriterion("attributes_str like", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrNotLike(String value) {
            addCriterion("attributes_str not like", value, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrIn(List<String> values) {
            addCriterion("attributes_str in", values, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrNotIn(List<String> values) {
            addCriterion("attributes_str not in", values, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrBetween(String value1, String value2) {
            addCriterion("attributes_str between", value1, value2, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andAttributesStrNotBetween(String value1, String value2) {
            addCriterion("attributes_str not between", value1, value2, "attributesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrIsNull() {
            addCriterion("images_str is null");
            return (Criteria) this;
        }

        public Criteria andImagesStrIsNotNull() {
            addCriterion("images_str is not null");
            return (Criteria) this;
        }

        public Criteria andImagesStrEqualTo(String value) {
            addCriterion("images_str =", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrNotEqualTo(String value) {
            addCriterion("images_str <>", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrGreaterThan(String value) {
            addCriterion("images_str >", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrGreaterThanOrEqualTo(String value) {
            addCriterion("images_str >=", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrLessThan(String value) {
            addCriterion("images_str <", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrLessThanOrEqualTo(String value) {
            addCriterion("images_str <=", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrLike(String value) {
            addCriterion("images_str like", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrNotLike(String value) {
            addCriterion("images_str not like", value, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrIn(List<String> values) {
            addCriterion("images_str in", values, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrNotIn(List<String> values) {
            addCriterion("images_str not in", values, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrBetween(String value1, String value2) {
            addCriterion("images_str between", value1, value2, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andImagesStrNotBetween(String value1, String value2) {
            addCriterion("images_str not between", value1, value2, "imagesStr");
            return (Criteria) this;
        }

        public Criteria andMainImageNumIsNull() {
            addCriterion("main_image_num is null");
            return (Criteria) this;
        }

        public Criteria andMainImageNumIsNotNull() {
            addCriterion("main_image_num is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageNumEqualTo(Integer value) {
            addCriterion("main_image_num =", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumNotEqualTo(Integer value) {
            addCriterion("main_image_num <>", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumGreaterThan(Integer value) {
            addCriterion("main_image_num >", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("main_image_num >=", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumLessThan(Integer value) {
            addCriterion("main_image_num <", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumLessThanOrEqualTo(Integer value) {
            addCriterion("main_image_num <=", value, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumIn(List<Integer> values) {
            addCriterion("main_image_num in", values, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumNotIn(List<Integer> values) {
            addCriterion("main_image_num not in", values, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumBetween(Integer value1, Integer value2) {
            addCriterion("main_image_num between", value1, value2, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andMainImageNumNotBetween(Integer value1, Integer value2) {
            addCriterion("main_image_num not between", value1, value2, "mainImageNum");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrIsNull() {
            addCriterion("shopee_skus_str is null");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrIsNotNull() {
            addCriterion("shopee_skus_str is not null");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrEqualTo(String value) {
            addCriterion("shopee_skus_str =", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrNotEqualTo(String value) {
            addCriterion("shopee_skus_str <>", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrGreaterThan(String value) {
            addCriterion("shopee_skus_str >", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrGreaterThanOrEqualTo(String value) {
            addCriterion("shopee_skus_str >=", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrLessThan(String value) {
            addCriterion("shopee_skus_str <", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrLessThanOrEqualTo(String value) {
            addCriterion("shopee_skus_str <=", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrLike(String value) {
            addCriterion("shopee_skus_str like", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrNotLike(String value) {
            addCriterion("shopee_skus_str not like", value, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrIn(List<String> values) {
            addCriterion("shopee_skus_str in", values, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrNotIn(List<String> values) {
            addCriterion("shopee_skus_str not in", values, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrBetween(String value1, String value2) {
            addCriterion("shopee_skus_str between", value1, value2, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andShopeeSkusStrNotBetween(String value1, String value2) {
            addCriterion("shopee_skus_str not between", value1, value2, "shopeeSkusStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrIsNull() {
            addCriterion("logistics_str is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrIsNotNull() {
            addCriterion("logistics_str is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrEqualTo(String value) {
            addCriterion("logistics_str =", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrNotEqualTo(String value) {
            addCriterion("logistics_str <>", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrGreaterThan(String value) {
            addCriterion("logistics_str >", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrGreaterThanOrEqualTo(String value) {
            addCriterion("logistics_str >=", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrLessThan(String value) {
            addCriterion("logistics_str <", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrLessThanOrEqualTo(String value) {
            addCriterion("logistics_str <=", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrLike(String value) {
            addCriterion("logistics_str like", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrNotLike(String value) {
            addCriterion("logistics_str not like", value, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrIn(List<String> values) {
            addCriterion("logistics_str in", values, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrNotIn(List<String> values) {
            addCriterion("logistics_str not in", values, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrBetween(String value1, String value2) {
            addCriterion("logistics_str between", value1, value2, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andLogisticsStrNotBetween(String value1, String value2) {
            addCriterion("logistics_str not between", value1, value2, "logisticsStr");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("weight is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("weight is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Double value) {
            addCriterion("weight =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Double value) {
            addCriterion("weight <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Double value) {
            addCriterion("weight >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("weight >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Double value) {
            addCriterion("weight <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Double value) {
            addCriterion("weight <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Double> values) {
            addCriterion("weight in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Double> values) {
            addCriterion("weight not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Double value1, Double value2) {
            addCriterion("weight between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Double value1, Double value2) {
            addCriterion("weight not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNull() {
            addCriterion("dimension is null");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNotNull() {
            addCriterion("dimension is not null");
            return (Criteria) this;
        }

        public Criteria andDimensionEqualTo(String value) {
            addCriterion("dimension =", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotEqualTo(String value) {
            addCriterion("dimension <>", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThan(String value) {
            addCriterion("dimension >", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThanOrEqualTo(String value) {
            addCriterion("dimension >=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThan(String value) {
            addCriterion("dimension <", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThanOrEqualTo(String value) {
            addCriterion("dimension <=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLike(String value) {
            addCriterion("dimension like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotLike(String value) {
            addCriterion("dimension not like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionIn(List<String> values) {
            addCriterion("dimension in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotIn(List<String> values) {
            addCriterion("dimension not in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionBetween(String value1, String value2) {
            addCriterion("dimension between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotBetween(String value1, String value2) {
            addCriterion("dimension not between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andDaysToShipIsNull() {
            addCriterion("days_to_ship is null");
            return (Criteria) this;
        }

        public Criteria andDaysToShipIsNotNull() {
            addCriterion("days_to_ship is not null");
            return (Criteria) this;
        }

        public Criteria andDaysToShipEqualTo(Integer value) {
            addCriterion("days_to_ship =", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipNotEqualTo(Integer value) {
            addCriterion("days_to_ship <>", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipGreaterThan(Integer value) {
            addCriterion("days_to_ship >", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipGreaterThanOrEqualTo(Integer value) {
            addCriterion("days_to_ship >=", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipLessThan(Integer value) {
            addCriterion("days_to_ship <", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipLessThanOrEqualTo(Integer value) {
            addCriterion("days_to_ship <=", value, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipIn(List<Integer> values) {
            addCriterion("days_to_ship in", values, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipNotIn(List<Integer> values) {
            addCriterion("days_to_ship not in", values, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipBetween(Integer value1, Integer value2) {
            addCriterion("days_to_ship between", value1, value2, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andDaysToShipNotBetween(Integer value1, Integer value2) {
            addCriterion("days_to_ship not between", value1, value2, "daysToShip");
            return (Criteria) this;
        }

        public Criteria andConditionIsNull() {
            addCriterion("`condition` is null");
            return (Criteria) this;
        }

        public Criteria andConditionIsNotNull() {
            addCriterion("`condition` is not null");
            return (Criteria) this;
        }

        public Criteria andConditionEqualTo(String value) {
            addCriterion("`condition` =", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotEqualTo(String value) {
            addCriterion("`condition` <>", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionGreaterThan(String value) {
            addCriterion("`condition` >", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionGreaterThanOrEqualTo(String value) {
            addCriterion("`condition` >=", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLessThan(String value) {
            addCriterion("`condition` <", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLessThanOrEqualTo(String value) {
            addCriterion("`condition` <=", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLike(String value) {
            addCriterion("`condition` like", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotLike(String value) {
            addCriterion("`condition` not like", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionIn(List<String> values) {
            addCriterion("`condition` in", values, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotIn(List<String> values) {
            addCriterion("`condition` not in", values, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionBetween(String value1, String value2) {
            addCriterion("`condition` between", value1, value2, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotBetween(String value1, String value2) {
            addCriterion("`condition` not between", value1, value2, "condition");
            return (Criteria) this;
        }

        public Criteria andVideoIsNull() {
            addCriterion("video is null");
            return (Criteria) this;
        }

        public Criteria andVideoIsNotNull() {
            addCriterion("video is not null");
            return (Criteria) this;
        }

        public Criteria andVideoEqualTo(String value) {
            addCriterion("video =", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotEqualTo(String value) {
            addCriterion("video <>", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThan(String value) {
            addCriterion("video >", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThanOrEqualTo(String value) {
            addCriterion("video >=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThan(String value) {
            addCriterion("video <", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThanOrEqualTo(String value) {
            addCriterion("video <=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLike(String value) {
            addCriterion("video like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotLike(String value) {
            addCriterion("video not like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoIn(List<String> values) {
            addCriterion("video in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotIn(List<String> values) {
            addCriterion("video not in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoBetween(String value1, String value2) {
            addCriterion("video between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotBetween(String value1, String value2) {
            addCriterion("video not between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andWholesaleIsNull() {
            addCriterion("wholesale is null");
            return (Criteria) this;
        }

        public Criteria andWholesaleIsNotNull() {
            addCriterion("wholesale is not null");
            return (Criteria) this;
        }

        public Criteria andWholesaleEqualTo(String value) {
            addCriterion("wholesale =", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleNotEqualTo(String value) {
            addCriterion("wholesale <>", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleGreaterThan(String value) {
            addCriterion("wholesale >", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleGreaterThanOrEqualTo(String value) {
            addCriterion("wholesale >=", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleLessThan(String value) {
            addCriterion("wholesale <", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleLessThanOrEqualTo(String value) {
            addCriterion("wholesale <=", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleLike(String value) {
            addCriterion("wholesale like", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleNotLike(String value) {
            addCriterion("wholesale not like", value, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleIn(List<String> values) {
            addCriterion("wholesale in", values, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleNotIn(List<String> values) {
            addCriterion("wholesale not in", values, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleBetween(String value1, String value2) {
            addCriterion("wholesale between", value1, value2, "wholesale");
            return (Criteria) this;
        }

        public Criteria andWholesaleNotBetween(String value1, String value2) {
            addCriterion("wholesale not between", value1, value2, "wholesale");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNull() {
            addCriterion("is_parent is null");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNotNull() {
            addCriterion("is_parent is not null");
            return (Criteria) this;
        }

        public Criteria andIsParentEqualTo(Boolean value) {
            addCriterion("is_parent =", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotEqualTo(Boolean value) {
            addCriterion("is_parent <>", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThan(Boolean value) {
            addCriterion("is_parent >", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_parent >=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThan(Boolean value) {
            addCriterion("is_parent <", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThanOrEqualTo(Boolean value) {
            addCriterion("is_parent <=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentIn(List<Boolean> values) {
            addCriterion("is_parent in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotIn(List<Boolean> values) {
            addCriterion("is_parent not in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent not between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Long value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Long value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Long value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Long value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Long> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Long> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Long value1, Long value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andPublishStepsIsNull() {
            addCriterion("publish_steps is null");
            return (Criteria) this;
        }

        public Criteria andPublishStepsIsNotNull() {
            addCriterion("publish_steps is not null");
            return (Criteria) this;
        }

        public Criteria andPublishStepsEqualTo(String value) {
            addCriterion("publish_steps =", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsNotEqualTo(String value) {
            addCriterion("publish_steps <>", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsGreaterThan(String value) {
            addCriterion("publish_steps >", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsGreaterThanOrEqualTo(String value) {
            addCriterion("publish_steps >=", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsLessThan(String value) {
            addCriterion("publish_steps <", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsLessThanOrEqualTo(String value) {
            addCriterion("publish_steps <=", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsLike(String value) {
            addCriterion("publish_steps like", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsNotLike(String value) {
            addCriterion("publish_steps not like", value, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsIn(List<String> values) {
            addCriterion("publish_steps in", values, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsNotIn(List<String> values) {
            addCriterion("publish_steps not in", values, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsBetween(String value1, String value2) {
            addCriterion("publish_steps between", value1, value2, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStepsNotBetween(String value1, String value2) {
            addCriterion("publish_steps not between", value1, value2, "publishSteps");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNull() {
            addCriterion("publish_status is null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNotNull() {
            addCriterion("publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusEqualTo(Integer value) {
            addCriterion("publish_status =", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotEqualTo(Integer value) {
            addCriterion("publish_status <>", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThan(Integer value) {
            addCriterion("publish_status >", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_status >=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThan(Integer value) {
            addCriterion("publish_status <", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("publish_status <=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIn(List<Integer> values) {
            addCriterion("publish_status in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotIn(List<Integer> values) {
            addCriterion("publish_status not in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("publish_status between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_status not between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdIsNull() {
            addCriterion("global_item_id is null");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdIsNotNull() {
            addCriterion("global_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdEqualTo(Long value) {
            addCriterion("global_item_id =", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdNotEqualTo(Long value) {
            addCriterion("global_item_id <>", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdGreaterThan(Long value) {
            addCriterion("global_item_id >", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("global_item_id >=", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdLessThan(Long value) {
            addCriterion("global_item_id <", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdLessThanOrEqualTo(Long value) {
            addCriterion("global_item_id <=", value, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdIn(List<Long> values) {
            addCriterion("global_item_id in", values, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdNotIn(List<Long> values) {
            addCriterion("global_item_id not in", values, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdBetween(Long value1, Long value2) {
            addCriterion("global_item_id between", value1, value2, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andGlobalItemIdNotBetween(Long value1, Long value2) {
            addCriterion("global_item_id not between", value1, value2, "globalItemId");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIsNull() {
            addCriterion("is_enabled is null");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIsNotNull() {
            addCriterion("is_enabled is not null");
            return (Criteria) this;
        }

        public Criteria andIsEnabledEqualTo(Boolean value) {
            addCriterion("is_enabled =", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotEqualTo(Boolean value) {
            addCriterion("is_enabled <>", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledGreaterThan(Boolean value) {
            addCriterion("is_enabled >", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_enabled >=", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledLessThan(Boolean value) {
            addCriterion("is_enabled <", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("is_enabled <=", value, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledIn(List<Boolean> values) {
            addCriterion("is_enabled in", values, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotIn(List<Boolean> values) {
            addCriterion("is_enabled not in", values, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enabled between", value1, value2, "isEnabled");
            return (Criteria) this;
        }

        public Criteria andIsEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enabled not between", value1, value2, "isEnabled");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}