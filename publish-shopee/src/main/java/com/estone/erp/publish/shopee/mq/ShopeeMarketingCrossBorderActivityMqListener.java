package com.estone.erp.publish.shopee.mq;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.ExcelFileUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.marking.CrossBorderActivityParam;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingCrossBorderActivityMessageDTO;
import com.estone.erp.publish.shopee.enums.ShopeeConfigOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishOperationTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeRegistrationMethodAndActivityTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeRegistrationStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.product.bean.SkuSystemStock;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeActivityRegistration;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeActivityTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeePublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeActivityRegistrationService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeActivityTemplateService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeePublishOperationLogService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 跨境活动消费者
 * <AUTHOR>
 * @Date 2025/4/18 17:56
 */
@Slf4j
public class ShopeeMarketingCrossBorderActivityMqListener implements ChannelAwareMessageListener {

    private static final String EDITABLE_KEYWORD = "（可编辑）";
    private static final String ITEM_ID_COLUMN = "商品ID";
    private static final String VARIATION_ID_COLUMN = "规格ID";
    private static final String SELLER_OFFER_COLUMN = "卖家报价";
    private static final String CURRENT_PRICE_COLUMN = "当前售价（税前）";
    private static final String DAILY_SETTLEMENT_PRICE_COLUMN = "日常结算价";
    private static final String ACTIVITY_SETTLEMENT_PRICE_COLUMN = "活动结算价";
    private static final String OPERATION_EDITABLE_KEYWORD = "操作（可编辑，从允许的操作里选择一个数字填入）";

    private static final String[] includes = {"itemSeller", "articleNumber", "itemId", "variationId", "skuStatus", "site", "isGoods", "originalPrice"};

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;

    @Autowired
    private ShopeeActivityTemplateService shopeeActivityTemplateService;

    @Autowired
    private ShopeeActivityRegistrationService shopeeActivityRegistrationService;

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Autowired
    private ShopeePublishOperationLogService shopeeOperateLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        ShopeeMarketingCrossBorderActivityMessageDTO innerParamDTO = JSON.parseObject(body, new TypeReference<ShopeeMarketingCrossBorderActivityMessageDTO>() {
        });

        Integer configId = innerParamDTO.getConfigId();
        CrossBorderActivityParam crossBorderActivityParam = innerParamDTO.getCrossBorderActivityParam();
        Integer registrationMethod = crossBorderActivityParam.getRegistrationMethod();
        List<Integer> activityTemplateIdList = crossBorderActivityParam.getActivityTemplateIdList();

        try {
            ShopeeAccountConfig accountConfig = shopeeAccountConfigService.selectByAccountNumber(innerParamDTO.getAccount());
            if (accountConfig == null) {
                throw new RuntimeException("根据卖家跨境活动配置生成报名文件任务失败，原因：未获取到账号信息，account为：" + innerParamDTO.getAccount());
            }
            // 2、获取启用模版数据
            List<ShopeeActivityTemplate> activityTemplateList = shopeeActivityTemplateService.list(new LambdaQueryWrapper<ShopeeActivityTemplate>().eq(ShopeeActivityTemplate::getStatus, 1).in(ShopeeActivityTemplate::getId, activityTemplateIdList));
            if (CollectionUtils.isEmpty(activityTemplateList)) {
                throw new RuntimeException("根据卖家跨境活动配置生成报名文件任务失败，原因：未获取到商品信息模版，id为：" + activityTemplateList);
            }

            // 3、获取活动数据，条件为配置店铺、报名方式、活动类型、状态（不等于已结束）、上传状态（为空）
            LambdaQueryWrapper<ShopeeActivityRegistration> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ShopeeActivityRegistration::getShopId, accountConfig.getShopId());
            queryWrapper.eq(ShopeeActivityRegistration::getRegistrationMethod, registrationMethod.equals(2) || registrationMethod.equals(4) ? 0 : 1);
            queryWrapper.eq(ShopeeActivityRegistration::getActivityType, registrationMethod.equals(1) || registrationMethod.equals(2) ? 0 : 1);
            queryWrapper.ne(ShopeeActivityRegistration::getStatus, ShopeeRegistrationStatusEnum.ENDED.getCode());
            queryWrapper.isNull(ShopeeActivityRegistration::getUploadStatus);
            List<ShopeeActivityRegistration> shopeeActivityRegistrationList = shopeeActivityRegistrationService.list(queryWrapper);
            if (CollectionUtils.isEmpty(shopeeActivityRegistrationList)) {
                throw new RuntimeException(String.format("根据卖家跨境活动配置生成报名文件任务失败，原因：未获取到活动信息，配置id为：%s，店铺为：%s", configId, innerParamDTO.getAccount()));
            }

            // 4、处理表格数据
            for (ShopeeActivityRegistration activityRegistration : shopeeActivityRegistrationList) {
                try {
                    String fileUrl = StringUtils.isNotBlank(activityRegistration.getGeneratedFile()) ? activityRegistration.getGeneratedFile() : activityRegistration.getEligibleProductInfo();
                    if (StringUtils.isBlank(fileUrl)) {
                        throw new RuntimeException("执行失败，未获取到可报名商品文件信息");
                    }
                    byte[] bytes = getFileBytes(fileUrl);
                    // 匹配对照模版配置，如果配置多个需要循环匹配，判断表头和对照列是否一致
                    for (int i = 0; i < activityTemplateList.size(); i++) {
                        ShopeeActivityTemplate activityTemplate = activityTemplateList.get(i);

                        // 处理对照列数据并判断对照列是否没有对应的模版数据
                        // 全部对照列（对照列-模版列）
                        Map<String, List<String>> allReferenceColumnMap = parseReferenceColumnData(activityTemplate);

                        // 可编辑对照列（模版列-对照列）
                        Map<String, String> ditableKeywordReferenceColumnData = parseeDitableKeywordReferenceColumnData(activityTemplate);

                        try {
                            InputStream inputStream = new ByteArrayInputStream(bytes);
                            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {

                                /**
                                 * 修改数据行
                                 */
                                private final AtomicInteger updateCount = new AtomicInteger(0);

                                /**
                                 * 列名-列表对照
                                 */
                                private Map<String, Integer> columnNameMap = new ConcurrentHashMap<>();

                                /**
                                 * 表数据
                                 */
                                private List<Map<Integer, String>> dataMapList = new CopyOnWriteArrayList<>();

                                @Override
                                public void invoke(Map<Integer, String> data, AnalysisContext context) {

                                    // 表头列数据匹配
                                    if (context.readRowHolder().getRowIndex() == 0) {
                                        List<String> headers = new ArrayList<>(data.values());

                                        // 如果有模版对照列满足，则后续的模版无需执行
                                        columnNameMap = new ConcurrentHashMap<>();
                                        dataMapList = new CopyOnWriteArrayList<>();

                                        // 校验表头和对照列
                                        for (int j = 0; j < headers.size(); j++) {
                                            String header = headers.get(j);
                                            // 校验表头和对照列
                                            if (header.contains(EDITABLE_KEYWORD) && !ditableKeywordReferenceColumnData.containsKey(header)) {
                                                throw new RuntimeException(String.format("对照列数据与模版数据不匹配，请检查对照列数据是否正确, 模版名称：%s，模版字段：%s", activityTemplate.getTemplateName(), header));
                                            }
                                            columnNameMap.put(header, j);
                                        }
                                    }

                                    // 非表头列数据匹配
                                    if (context.readRowHolder().getRowIndex() > 0) {
                                        dataMapList.add(data);
                                    }
                                }

                                @Override
                                public void doAfterAllAnalysed(AnalysisContext context) {
                                    String shopId = dataMapList.get(0).get(columnNameMap.get("店铺ID"));
                                    ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByShopId(shopId);
                                    if (shopeeAccountConfig == null) {
                                        throw new RuntimeException("店铺配置中未获取到当前店铺信息");
                                    }

                                    // 获取所有key包含（可编辑）的列数据
                                    Map<String, Integer> editableColumnMap = columnNameMap.entrySet().stream()
                                            .filter(entry -> entry.getKey().contains(EDITABLE_KEYWORD))
                                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                                    // 获取商品信息
                                    Map<String, List<EsShopeeItem>> esShopeeItemGroups = getEsShopeeItemMap(shopeeAccountConfig.getAccount());
                                    if (MapUtils.isEmpty(esShopeeItemGroups)) {
                                        throw new RuntimeException(String.format("%s配置的%s店铺，无符合配置规则的数据", innerParamDTO.getConfigName(), shopeeAccountConfig.getAccount()));
                                    }

                                    // 判断类型并校验数据，满足条件的数据直接修改数据
                                    Integer registrationMethod = crossBorderActivityParam.getRegistrationMethod();
                                    if (ShopeeRegistrationMethodAndActivityTypeEnum.NON_SIP_BARGAINING.isTrue(registrationMethod)) {
                                        // 商品议价-非SIP
                                        nonSipBargaining(shopeeAccountConfig, esShopeeItemGroups, editableColumnMap);
                                    }
                                    if (ShopeeRegistrationMethodAndActivityTypeEnum.NON_SIP_CONFIRMATION.isTrue(registrationMethod)) {
                                        // 商品确认-非SIP
                                        nonSipConfirmation(shopeeAccountConfig, esShopeeItemGroups, editableColumnMap);
                                    }
                                    if (ShopeeRegistrationMethodAndActivityTypeEnum.SIP_BARGAINING.isTrue(registrationMethod)) {
                                        // 商品议价-SIP
                                        sipBargaining(esShopeeItemGroups, editableColumnMap);
                                    }
                                    if (ShopeeRegistrationMethodAndActivityTypeEnum.SIP_CONFIRMATION.isTrue(registrationMethod)) {
                                        // 商品确认-SIP
                                        sipConfirmation(esShopeeItemGroups, editableColumnMap);
                                    }

                                    // 修改数据
                                    shopeeActivityRegistrationUpdate();
                                }

                                /**
                                 * 修改数据
                                 */
                                private void shopeeActivityRegistrationUpdate() {
                                    try {
                                        int count = updateCount.get();
                                        if (count == 0) {
                                            throw new RuntimeException("配置：" + innerParamDTO.getConfigName() + ",暂无数据满足条件");
                                        }

                                        // 上传文件
                                        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
                                        dataMapList.add(0, new HashMap<Integer, String>());
                                        SeaweedFile seaweedFile = ExcelFileUtils.uploadExcelFile(fileUrl, fileName, "shopeeExcel", dataMapList);

                                        // 规则名称调整
                                        ShopeeActivityRegistration shopeeActivityRegistration = shopeeActivityRegistrationService.getById(activityRegistration.getId());
                                        String ruleName = innerParamDTO.getConfigName();
                                        if (StringUtils.isNotBlank(shopeeActivityRegistration.getRuleName())) {
                                            ruleName = shopeeActivityRegistration.getRuleName().contains(innerParamDTO.getConfigName()) ? shopeeActivityRegistration.getRuleName() : shopeeActivityRegistration.getRuleName() + "," + innerParamDTO.getConfigName();
                                        }

                                        // 更新数据
                                        LocalDateTime now = LocalDateTime.now();
                                        LambdaUpdateWrapper<ShopeeActivityRegistration> wrapper = new LambdaUpdateWrapper<>();
                                        wrapper.eq(ShopeeActivityRegistration::getId, activityRegistration.getId());
                                        wrapper.set(ShopeeActivityRegistration::getGeneratedFile, seaweedFile.getUrl());
                                        wrapper.set(ShopeeActivityRegistration::getGenerationTime, now);
                                        wrapper.set(ShopeeActivityRegistration::getUpdatedTime, now);
                                        wrapper.set(ShopeeActivityRegistration::getRuleName, ruleName);
                                        wrapper.set(ShopeeActivityRegistration::getGenerationFailureReason, null);
                                        shopeeActivityRegistrationService.update(wrapper);
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                }

                                /**
                                 * 获取key
                                 * @param columns
                                 * @return
                                 */
                                private String getColumnKey(Map<Integer, String> columns) {
                                    Integer itemIdIndex = columnNameMap.get(ITEM_ID_COLUMN);

                                    String key = columns.get(itemIdIndex);
                                    if (columnNameMap.get(VARIATION_ID_COLUMN) != null) {
                                        key = columns.get(itemIdIndex) + "_" + columns.get(columnNameMap.get(VARIATION_ID_COLUMN));
                                    }
                                    return key;
                                }

                                /**
                                 * 商品议价-非SIP
                                 *
                                 * @param shopeeAccountConfig
                                 * @param esShopeeItemGroups
                                 * @param editableColumnMap
                                 */
                                private void nonSipBargaining(ShopeeAccountConfig shopeeAccountConfig, Map<String, List<EsShopeeItem>> esShopeeItemGroups, Map<String, Integer> editableColumnMap) {
                                    // 可用库存区间
                                    List<Integer> warehouseTypeList = crossBorderActivityParam.getWarehouseType();
                                    List<Integer> availableStockRange = crossBorderActivityParam.getAvailableStockRange();

                                    // 目标毛利率区间
                                    List<Double> targetGrossMarginRange = crossBorderActivityParam.getTargetGrossMarginRange();

                                    // 判断目标毛利率区间是否存在（计算毛利率）
                                    Map<String, BatchPriceCalculatorResponse> priceCalculatorResponseMap = new HashMap<>();
                                    if (CollectionUtils.isNotEmpty(targetGrossMarginRange)) {
                                        priceCalculatorResponseMap = getPriceCalculatorResponseMap(shopeeAccountConfig, esShopeeItemGroups, CURRENT_PRICE_COLUMN);
                                    }

                                    // 卖家报价（站点减值）
                                    Integer sellerOffer = crossBorderActivityParam.getSellerOffer();
                                    List<CrossBorderActivityParam.SiteReduction> siteReductionList = crossBorderActivityParam.getSiteReduction();

                                    // 日志
                                    List<String> operateLogMessage = new ArrayList<>();
                                    for (Map<Integer, String> columns : dataMapList) {
                                        // 如果可编辑行存在数据，则跳过
                                        boolean anyMatch = columnNameMap.entrySet().stream()
                                                .filter(entry -> entry.getKey().contains(EDITABLE_KEYWORD))
                                                .anyMatch(entry -> Objects.nonNull(columns.get(entry.getValue())));
                                        if (anyMatch) {
                                            continue;
                                        }

                                        // 获取esShopeeItem
                                        String key = getColumnKey(columns);
                                        List<EsShopeeItem> esShopeeItemList = esShopeeItemGroups.get(key);
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            esShopeeItemList = esShopeeItemGroups.get(columns.get(columnNameMap.get(ITEM_ID_COLUMN)));
                                        }
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            String message = String.format("商品议价-非SIP,未获取到对应item数据，活动名称：%s，key：%s", activityRegistration.getActivityName(), key);
                                            operateLogMessage.add(message);
                                            continue;
                                        }

                                        // 判断可用库存区间
                                        if (CollectionUtils.isNotEmpty(warehouseTypeList) && CollectionUtils.isNotEmpty(availableStockRange)) {
                                            boolean warehouseTypeMatch = esShopeeItemList.stream()
                                                    .allMatch(item -> {
                                                        SkuSystemStock systemStock = SkuStockUtils.getSystemStocksBySku(item.getArticleNumber());
                                                        if (Objects.isNull(systemStock)) {
                                                            String message = String.format("商品议价-非SIP,未获取到对应库存信息，活动名称：%s，货号：%s", activityRegistration.getActivityName(), item.getArticleNumber());
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }

                                                        int stock = 0;
                                                        Integer warehouseType = warehouseTypeList.get(0);
                                                        if (warehouseType.equals(1)) {
                                                            stock = systemStock.getUsableStock() - systemStock.getPendingStock();
                                                        }
                                                        if (warehouseType.equals(2)) {
                                                            stock = systemStock.getNnUsableStock() - systemStock.getNnPendingStock();
                                                        }
                                                        if (warehouseType.equals(3)) {
                                                            stock = (systemStock.getUsableStock() - systemStock.getPendingStock()) + (systemStock.getNnUsableStock() - systemStock.getNnPendingStock());

                                                        }
                                                        String stockMessage = "可用库存：" + systemStock.getUsableStock() + "，待发库存：" + systemStock.getPendingStock() + "，南宁可用库存：" + systemStock.getNnUsableStock() + "，南宁待发库存：" + systemStock.getNnPendingStock();
                                                        if (stock < availableStockRange.get(0) || stock >= availableStockRange.get(1)) {
                                                            String message = String.format("商品议价-非SIP,库存区间不满足，活动名称：%s，库存：%s，区间：%s", activityRegistration.getActivityName(), stockMessage, availableStockRange);
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        return true;
                                                    });
                                            if (!warehouseTypeMatch) {
                                                continue;
                                            }
                                        }

                                        // 判断目标毛利率区间
                                        if (CollectionUtils.isNotEmpty(targetGrossMarginRange)) {
                                            Map<String, BatchPriceCalculatorResponse> finalPriceCalculatorResponseMap = priceCalculatorResponseMap;
                                            boolean targetGrossMarginRangeMatch = esShopeeItemList.stream()
                                                    .allMatch(item -> {
                                                        BatchPriceCalculatorResponse batchPriceCalculatorResponse = finalPriceCalculatorResponseMap.get(item.getId());
                                                        if (Objects.isNull(batchPriceCalculatorResponse)) {
                                                            String message = String.format("商品议价-非SIP,未获取到对应毛利率信息，活动名称：%s，key：%s", activityRegistration.getActivityName(), item.getId());
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        Double grossProfitRate = batchPriceCalculatorResponse.getGrossProfitRate();
                                                        if (grossProfitRate == null) {
                                                            String message = String.format("商品议价-非SIP,未获取到对应毛利率，活动名称：%s，key：%s", activityRegistration.getActivityName(), item.getId());
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        if (grossProfitRate < targetGrossMarginRange.get(0) || grossProfitRate >= targetGrossMarginRange.get(1)) {
                                                            String message = String.format("商品议价-非SIP,目标毛利率区间不满足，活动名称：%s，key：%s，毛利率：%s，区间：%s", activityRegistration.getActivityName(), item.getId(), grossProfitRate, targetGrossMarginRange);
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        return true;
                                                    });
                                            if (!targetGrossMarginRangeMatch) {
                                                continue;
                                            }
                                        }

                                        // 卖家报价（站点减值）
                                        BigDecimal sellerOfferPrice = BigDecimal.ZERO;
                                        if (Objects.nonNull(sellerOffer) && sellerOffer.equals(1)) {
                                            Integer priceIndex = columnNameMap.get(CURRENT_PRICE_COLUMN);
                                            if (Objects.isNull(priceIndex)) {
                                                List<String> nameList = allReferenceColumnMap.get(CURRENT_PRICE_COLUMN);
                                                if (CollectionUtils.isEmpty(nameList) || nameList.size() > 1) {
                                                    continue;
                                                }
                                                priceIndex = columnNameMap.get(nameList.get(0));
                                            }
                                            if (Objects.isNull(priceIndex)) {
                                                continue;
                                            }

                                            // 站点减值（根据区间配置来）
                                            boolean isUpdateCurrentPriceRange = false;
                                            double price = Double.parseDouble(columns.get(priceIndex));
                                            for (CrossBorderActivityParam.SiteReduction siteReduction : siteReductionList) {
                                                // 判断是否在区间内
                                                List<Double> currentReductionList = siteReduction.getCurrentPriceRangeList();
                                                if (shopeeAccountConfig.getSite().equals(siteReduction.getSite()) && price >= currentReductionList.get(0) && price < currentReductionList.get(1)) {
                                                    sellerOfferPrice = BigDecimal.valueOf(price).subtract(BigDecimal.valueOf(siteReduction.getReductionOperation())).setScale(2, RoundingMode.HALF_UP);
                                                    isUpdateCurrentPriceRange = true;
                                                    break;
                                                }
                                            }
                                            if (!isUpdateCurrentPriceRange) {
                                                String message = String.format("商品议价-非SIP,站点减值不满足，活动名称：%s，当前售价（税前）：%s", activityRegistration.getActivityName(), Double.parseDouble(columns.get(priceIndex)));
                                                operateLogMessage.add(message);
                                                continue;
                                            }
                                        }

                                        // 表头对照
                                        for (Map.Entry<String, Integer> entry : editableColumnMap.entrySet()) {
                                            Integer value = entry.getValue();
                                            String configKey = ditableKeywordReferenceColumnData.get(entry.getKey());
                                            switch (configKey) {
                                                case "是否同意目标价格":
                                                    Boolean agreeTargetPrice = crossBorderActivityParam.getAgreeTargetPrice();
                                                    columns.put(value, agreeTargetPrice ? "Y" : "N");
                                                    break;
                                                case "卖家报价":
                                                    columns.put(value, sellerOfferPrice.toString());
                                                    break;
                                                case "卖家反馈":
                                                    String sellerFeedback = crossBorderActivityParam.getSellerFeedback();
                                                    columns.put(value, sellerFeedback);
                                                    break;
                                                case "活动库存":
                                                    Integer activityStockValue = crossBorderActivityParam.getActivityStock();
                                                    columns.put(value, String.valueOf(activityStockValue));
                                                    break;
                                                case "购买上限":
                                                    Integer purchaseLimitValue = crossBorderActivityParam.getPurchaseLimit();
                                                    columns.put(value, String.valueOf(purchaseLimitValue));
                                                    break;
                                                case "卖家评论":
                                                    String sellerCommentValue = crossBorderActivityParam.getSellerComment();
                                                    if (StringUtils.isNotBlank(sellerCommentValue)) {
                                                        columns.put(value, sellerCommentValue);
                                                    }
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }

                                        // 判断是否有操作（可编辑，从允许的操作里选择一个数字填入）行，如果存在则修改为1，因为走到这里说明校验通过
                                        Integer operation = columnNameMap.get(OPERATION_EDITABLE_KEYWORD);
                                        if (Objects.nonNull(operation)) {
                                            columns.put(operation, "1");
                                        }
                                        updateCount.incrementAndGet();
                                    }
                                    if (CollectionUtils.isNotEmpty(operateLogMessage)) {
                                        List<ShopeePublishOperationLog> shopeePublishOperationLogs = operateLogMessage.stream().map(message -> createOperateLog(configId, message)).collect(Collectors.toList());
                                        shopeeOperateLogService.saveBatch(shopeePublishOperationLogs);
                                    }

                                }

                                /**
                                 * 商品确认-非SIP
                                 *
                                 * @param shopeeAccountConfig
                                 * @param esShopeeItemGroups
                                 * @param editableColumnMap
                                 */
                                private void nonSipConfirmation(ShopeeAccountConfig shopeeAccountConfig, Map<String, List<EsShopeeItem>> esShopeeItemGroups, Map<String, Integer> editableColumnMap) {
                                    List<Double> sellerOfferGrossMarginRange = crossBorderActivityParam.getSellerOfferGrossMarginRange();
                                    Map<String, BatchPriceCalculatorResponse> priceCalculatorResponseMap = new HashMap<>();
                                    if (CollectionUtils.isNotEmpty(sellerOfferGrossMarginRange)) {
                                        priceCalculatorResponseMap = getPriceCalculatorResponseMap(shopeeAccountConfig, esShopeeItemGroups, SELLER_OFFER_COLUMN);
                                    }

                                    List<String> operateLogMessage = new ArrayList<>();
                                    for (Map<Integer, String> columns : dataMapList) {
                                        // 如果可编辑行存在数据，则跳过
                                        boolean anyMatch = columnNameMap.entrySet().stream()
                                                .filter(entry -> entry.getKey().contains(EDITABLE_KEYWORD))
                                                .anyMatch(entry -> Objects.nonNull(columns.get(entry.getValue())));
                                        if (anyMatch) {
                                            continue;
                                        }

                                        // 获取esShopeeItem
                                        String key = getColumnKey(columns);
                                        List<EsShopeeItem> esShopeeItemList = esShopeeItemGroups.get(key);
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            esShopeeItemList = esShopeeItemGroups.get(columns.get(columnNameMap.get(ITEM_ID_COLUMN)));
                                        }
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            String message = String.format("商品确认-非SIP,未获取到对应item数据，活动名称：%s，key：%s", activityRegistration.getActivityName(), key);
                                            operateLogMessage.add(message);
                                            continue;
                                        }

                                        if (CollectionUtils.isNotEmpty(sellerOfferGrossMarginRange)) {
                                            Map<String, BatchPriceCalculatorResponse> finalPriceCalculatorResponseMap = priceCalculatorResponseMap;
                                            boolean buyerOfferGrossMarginRangeMatch = esShopeeItemList.stream()
                                                    .allMatch(item -> {
                                                        BatchPriceCalculatorResponse batchPriceCalculatorResponse = finalPriceCalculatorResponseMap.get(item.getId());
                                                        if (batchPriceCalculatorResponse == null) {
                                                            String message = String.format("商品确认-非SIP,未获取到对应毛利率信息，活动名称：%s，key：%s", activityRegistration.getActivityName(), item.getId());
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        Double grossProfitRate = batchPriceCalculatorResponse.getGrossProfitRate();
                                                        if (grossProfitRate == null) {
                                                            String message = String.format("商品确认-非SIP,未获取到对应毛利率，活动名称：%s，key：%s", activityRegistration.getActivityName(), item.getId());
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        if (grossProfitRate < sellerOfferGrossMarginRange.get(0) || grossProfitRate >= sellerOfferGrossMarginRange.get(1)) {
                                                            String message = String.format("商品确认-非SIP,目标毛利率区间不满足，活动名称：%s，key：%s，毛利率：%s，区间：%s", activityRegistration.getActivityName(), item.getId(), grossProfitRate, sellerOfferGrossMarginRange);
                                                            operateLogMessage.add(message);
                                                            return false;
                                                        }
                                                        return true;
                                                    });
                                            if (!buyerOfferGrossMarginRangeMatch) {
                                                continue;
                                            }
                                        }

                                        // 表头对照
                                        for (Map.Entry<String, Integer> entry : editableColumnMap.entrySet()) {
                                            Integer value = entry.getValue();
                                            String configKey = ditableKeywordReferenceColumnData.get(entry.getKey());
                                            switch (configKey) {
                                                case "卖家反馈":
                                                    String sellerFeedback = crossBorderActivityParam.getSellerFeedback();
                                                    columns.put(value, sellerFeedback);
                                                    break;
                                                case "活动库存":
                                                    Integer activityStockValue = crossBorderActivityParam.getActivityStock();
                                                    columns.put(value, String.valueOf(activityStockValue));
                                                    break;
                                                case "购买上限":
                                                    Integer purchaseLimitValue = crossBorderActivityParam.getPurchaseLimit();
                                                    columns.put(value, String.valueOf(purchaseLimitValue));
                                                    break;
                                                case "卖家评论":
                                                    String sellerCommentValue = crossBorderActivityParam.getSellerComment();
                                                    if (StringUtils.isNotBlank(sellerCommentValue)) {
                                                        columns.put(value, sellerCommentValue);
                                                    }
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                        updateCount.incrementAndGet();
                                    }
                                    if (CollectionUtils.isNotEmpty(operateLogMessage)) {
                                        List<ShopeePublishOperationLog> shopeePublishOperationLogs = operateLogMessage.stream().map(message -> createOperateLog(configId, message)).collect(Collectors.toList());
                                        shopeeOperateLogService.saveBatch(shopeePublishOperationLogs);
                                    }
                                }

                                /**
                                 * 商品议价-SIP
                                 *
                                 * @param esShopeeItemGroups
                                 * @param editableColumnMap
                                 */
                                private void sipBargaining(Map<String, List<EsShopeeItem>> esShopeeItemGroups, Map<String, Integer> editableColumnMap) {
                                    Integer sellerOffer = crossBorderActivityParam.getSellerOffer();

                                    // 校验日常结算价减值的日常结算价区间并批量计算毛利率
                                    List<CrossBorderActivityParam.DailySettlementReduction> dailySettlementReduction = crossBorderActivityParam.getDailySettlementReduction();
                                    for (Map<Integer, String> columns : dataMapList) {
                                        // 如果可编辑行存在数据，则跳过
                                        boolean anyMatch = columnNameMap.entrySet().stream()
                                                .filter(entry -> entry.getKey().contains(EDITABLE_KEYWORD))
                                                .anyMatch(entry -> Objects.nonNull(columns.get(entry.getValue())));
                                        if (anyMatch) {
                                            continue;
                                        }

                                        // 获取esShopeeItem
                                        String key = getColumnKey(columns);
                                        List<EsShopeeItem> esShopeeItemList = esShopeeItemGroups.get(key);
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            esShopeeItemList = esShopeeItemGroups.get(columns.get(columnNameMap.get(ITEM_ID_COLUMN)));
                                        }
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            log.info("商品议价-SIP,未获取到对应item数据，配置id：{}，活动名称：{}，key：{}", configId, activityRegistration.getActivityName(), key);
                                            continue;
                                        }

                                        // 卖家报价处理
                                        BigDecimal sellerOfferPrice = BigDecimal.ZERO;
                                        if (Objects.nonNull(sellerOffer) && sellerOffer.equals(1)) {
                                            Integer columnDailyPrice = columnNameMap.get(DAILY_SETTLEMENT_PRICE_COLUMN);
                                            if (Objects.isNull(columnDailyPrice)) {
                                                List<String> nameList = allReferenceColumnMap.get(DAILY_SETTLEMENT_PRICE_COLUMN);
                                                if (CollectionUtils.isEmpty(nameList) || nameList.size() > 1) {
                                                    continue;
                                                }
                                                columnDailyPrice = columnNameMap.get(nameList.get(0));
                                            }
                                            if (Objects.isNull(columnDailyPrice)) {
                                                continue;
                                            }

                                            // 计算卖家报价（根据区间配置来）
                                            boolean isUpdateCurrentPriceRange = false;
                                            double dailyPrice = Double.parseDouble(columns.get(columnDailyPrice));
                                            for (CrossBorderActivityParam.DailySettlementReduction settlementReduction : dailySettlementReduction) {
                                                // 判断是否在区间内
                                                List<Double> dailySettlementPriceRange = settlementReduction.getDailySettlementPriceRange();
                                                if (dailyPrice >= dailySettlementPriceRange.get(0) && dailyPrice < dailySettlementPriceRange.get(1)) {
                                                    sellerOfferPrice = BigDecimal.valueOf(dailyPrice).subtract(BigDecimal.valueOf(settlementReduction.getReductionOperation())).setScale(2, RoundingMode.HALF_UP);
                                                    isUpdateCurrentPriceRange = true;
                                                    break;
                                                }

                                            }
                                            if (!isUpdateCurrentPriceRange) {
                                                continue;
                                            }
                                        }

                                        // 表头对照
                                        for (Map.Entry<String, Integer> entry : editableColumnMap.entrySet()) {
                                            Integer value = entry.getValue();
                                            String configKey = ditableKeywordReferenceColumnData.get(entry.getKey());
                                            switch (configKey) {
                                                case "卖家报价":
                                                    columns.put(value, sellerOfferPrice.toString());
                                                    break;
                                                case "活动库存":
                                                    Integer activityStockValue = crossBorderActivityParam.getActivityStock();
                                                    columns.put(value, String.valueOf(activityStockValue));
                                                    break;
                                                case "购买上限":
                                                    Integer purchaseLimitValue = crossBorderActivityParam.getPurchaseLimit();
                                                    columns.put(value, String.valueOf(purchaseLimitValue));
                                                    break;
                                                case "卖家评论":
                                                    String sellerCommentValue = crossBorderActivityParam.getSellerComment();
                                                    if (StringUtils.isNotBlank(sellerCommentValue)) {
                                                        columns.put(value, sellerCommentValue);
                                                    }
                                                    break;
                                                case "重复上档活动次数":
                                                    Integer repeatActivityCountValue = crossBorderActivityParam.getRepeatActivityCount();
                                                    columns.put(value, String.valueOf(repeatActivityCountValue));
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }

                                        // 判断是否有操作（可编辑，从允许的操作里选择一个数字填入）行，如果存在则修改为1，因为走到这里说明校验通过
                                        Integer operation = columnNameMap.get(OPERATION_EDITABLE_KEYWORD);
                                        if (Objects.nonNull(operation)) {
                                            columns.put(operation, "1");
                                        }
                                        updateCount.incrementAndGet();
                                    }
                                }

                                /**
                                 * 商品确认-SIP
                                 *
                                 * @param esShopeeItemGroups
                                 * @param editableColumnMap
                                 */
                                private void sipConfirmation(Map<String, List<EsShopeeItem>> esShopeeItemGroups, Map<String, Integer> editableColumnMap) {
                                    Boolean hasDailySettlementPrice = crossBorderActivityParam.getHasDailySettlementPrice();
                                    for (Map<Integer, String> columns : dataMapList) {
                                        // 如果可编辑行存在数据，则跳过
                                        boolean anyMatch = columnNameMap.entrySet().stream()
                                                .filter(entry -> entry.getKey().contains(EDITABLE_KEYWORD))
                                                .anyMatch(entry -> Objects.nonNull(columns.get(entry.getValue())));
                                        if (anyMatch) {
                                            continue;
                                        }

                                        // 获取key
                                        String key = getColumnKey(columns);
                                        List<EsShopeeItem> esShopeeItemList = esShopeeItemGroups.get(key);
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            esShopeeItemList = esShopeeItemGroups.get(columns.get(columnNameMap.get(ITEM_ID_COLUMN)));
                                        }
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            log.info("商品确认-SIP,未获取到对应item数据，配置id：{}，活动名称：{}，key：{}", configId, activityRegistration.getActivityName(), key);
                                            continue;
                                        }

                                        // 判断是否存在日常结算价
                                        if (hasDailySettlementPrice) {
                                            //  日常结算价区间
                                            Integer columnDailyPriceIndex = columnNameMap.get(DAILY_SETTLEMENT_PRICE_COLUMN);
                                            if (Objects.isNull(columnDailyPriceIndex)) {
                                                List<String> nameList = allReferenceColumnMap.get(DAILY_SETTLEMENT_PRICE_COLUMN);
                                                if (CollectionUtils.isEmpty(nameList)) {
                                                    continue;
                                                }
                                                if (nameList.size() > 1) {
                                                    throw new RuntimeException(activityTemplate.getTemplateName() + "模板" + DAILY_SETTLEMENT_PRICE_COLUMN + "字段配置存在多个非编辑字段");
                                                }
                                                columnDailyPriceIndex = columnNameMap.get(nameList.get(0));
                                            }
                                            if (Objects.isNull(columnDailyPriceIndex)) {
                                                continue;
                                            }
                                            double dailyPrice = Double.parseDouble(columns.get(columnDailyPriceIndex));
                                            List<Double> dailySettlementPriceRange1 = crossBorderActivityParam.getDailySettlementPriceRange();
                                            if (dailyPrice < dailySettlementPriceRange1.get(0) || dailyPrice >= dailySettlementPriceRange1.get(1)) {
                                                continue;
                                            }

                                            // 日常结算价-活动结算价
                                            List<Double> settlementPriceDifference = crossBorderActivityParam.getSettlementPriceDifference();
                                            if (CollectionUtils.isNotEmpty(settlementPriceDifference)) {
                                                Integer columnActivityPrice = columnNameMap.get(ACTIVITY_SETTLEMENT_PRICE_COLUMN);
                                                if (Objects.isNull(columnActivityPrice)) {
                                                    List<String> nameList = allReferenceColumnMap.get(ACTIVITY_SETTLEMENT_PRICE_COLUMN);
                                                    if (CollectionUtils.isEmpty(nameList)) {
                                                        continue;
                                                    }
                                                    if (nameList.size() > 1) {
                                                        throw new RuntimeException(activityTemplate.getTemplateName() + "模板" + ACTIVITY_SETTLEMENT_PRICE_COLUMN + "字段配置存在多个非编辑字段");
                                                    }
                                                    columnActivityPrice = columnNameMap.get(nameList.get(0));
                                                }
                                                if (Objects.isNull(columnActivityPrice)) {
                                                    continue;
                                                }
                                                double activityPrice = dailyPrice - Double.parseDouble(columns.get(columnActivityPrice));
                                                if (activityPrice < settlementPriceDifference.get(0) || activityPrice >= settlementPriceDifference.get(1)) {
                                                    continue;
                                                }
                                            }
                                        }

                                        // 表头对照
                                        for (Map.Entry<String, Integer> entry : editableColumnMap.entrySet()) {
                                            Integer value = entry.getValue();
                                            String configKey = ditableKeywordReferenceColumnData.get(entry.getKey());
                                            switch (configKey) {
                                                case "卖家反馈":
                                                    String sellerFeedback = crossBorderActivityParam.getSellerFeedback();
                                                    columns.put(value, sellerFeedback);
                                                    break;
                                                case "活动库存":
                                                    Integer activityStockValue = crossBorderActivityParam.getActivityStock();
                                                    columns.put(value, String.valueOf(activityStockValue));
                                                    break;
                                                case "购买上限":
                                                    Integer purchaseLimitValue = crossBorderActivityParam.getPurchaseLimit();
                                                    columns.put(value, String.valueOf(purchaseLimitValue));
                                                    break;
                                                case "卖家评论":
                                                    String sellerCommentValue = crossBorderActivityParam.getSellerComment();
                                                    if (StringUtils.isNotBlank(sellerCommentValue)) {
                                                        columns.put(value, sellerCommentValue);
                                                    }
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                        updateCount.incrementAndGet();
                                    }
                                }

                                /**
                                 * 获取商品信息
                                 *
                                 * @param account
                                 * @return
                                 */
                                private Map<String, List<EsShopeeItem>> getEsShopeeItemMap(String account) {
                                    Map<String, List<EsShopeeItem>> esShopeeItemMap = new HashMap<>();

                                    // 获取商品信息
                                    Integer itemIdIndex = columnNameMap.get(ITEM_ID_COLUMN);
                                    Integer variableIdIndex = columnNameMap.get(VARIATION_ID_COLUMN);
                                    List<String> skuStatusList = crossBorderActivityParam.getSkuStatusList();
                                    for (List<Map<Integer, String>> dataList : Lists.partition(dataMapList, 200)) {
                                        List<String> itemIdList = dataList.stream().map(columnMap -> columnMap.get(itemIdIndex)).collect(Collectors.toList());

                                        if (variableIdIndex == null) {
                                            // 按价格排序取最高
                                            BoolQueryBuilder fallbackQueryBuilder1 = QueryBuilders.boolQuery()
                                                    .must(QueryBuilders.termsQuery("itemSeller", account))
                                                    .must(QueryBuilders.termsQuery("itemId", itemIdList))
                                                    .must(QueryBuilders.termQuery("isGoods", true));
                                            NativeSearchQuery fallbackSearchQuery = new NativeSearchQueryBuilder()
                                                    .withQuery(fallbackQueryBuilder1)
                                                    .withSourceFilter(new FetchSourceFilter(includes, null))
                                                    .build();
                                            List<EsShopeeItem> esShopeeItems = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, fallbackSearchQuery, EsShopeeItem.class);
                                            if (CollectionUtils.isEmpty(esShopeeItems)) {
                                                continue;
                                            }
                                            Map<String, List<EsShopeeItem>> esShopeeItemGroups = esShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
                                            if (CollectionUtils.isEmpty(skuStatusList)) {
                                                esShopeeItemMap.putAll(esShopeeItemGroups);
                                                continue;
                                            }

                                            // item维度单品状态条件则过滤掉
                                            esShopeeItemGroups = esShopeeItemGroups.entrySet().stream()
                                                    .filter(entry -> entry.getValue().stream().allMatch(item -> item.getSkuStatus() != null && skuStatusList.contains(item.getSkuStatus())))
                                                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                                            esShopeeItemMap.putAll(esShopeeItemGroups);
                                            continue;
                                        }

                                        // 读取表格查询条件数据
                                        List<String> idList = dataList.stream().map(columnMap -> {
                                            return String.format("%s_%s", columnMap.get(itemIdIndex), columnMap.get(variableIdIndex));
                                        }).collect(Collectors.toList());

                                        // 通过id查询es商品信息数据
                                        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                                                .must(QueryBuilders.termsQuery("id", idList))
                                                .must(QueryBuilders.termQuery("itemSeller", account))
                                                .must(QueryBuilders.termQuery("isGoods", true));
                                        if (!CollectionUtils.isEmpty(skuStatusList)) {
                                            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
                                        }
                                        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                                                .withQuery(boolQueryBuilder)
                                                .withSourceFilter(new FetchSourceFilter(includes, null))
                                                .build();
                                        Map<String, List<EsShopeeItem>> esShopeeItemGroups = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, searchQuery, EsShopeeItem.class).stream().collect(Collectors.groupingBy(EsShopeeItem::getId));
                                        esShopeeItemMap.putAll(esShopeeItemGroups);
                                        if (MapUtils.isNotEmpty(esShopeeItemGroups) && esShopeeItemGroups.size() >= idList.size()) {
                                            continue;
                                        }

                                        // 如果为空或者确实则通过item继续查询数据
                                        List<String> missItemIdList = itemIdList.stream()
                                                .filter(itemId -> esShopeeItemGroups.keySet().stream().noneMatch(id -> id.equals(itemId)))
                                                .collect(Collectors.toList());

                                        // 备用查询：无 variationId 限制，按价格排序取最高
                                        BoolQueryBuilder fallbackQueryBuilder = QueryBuilders.boolQuery()
                                                .must(QueryBuilders.termsQuery("itemSeller", account))
                                                .must(QueryBuilders.termsQuery("itemId", missItemIdList))
                                                .must(QueryBuilders.termQuery("isGoods", true));
                                        NativeSearchQuery fallbackSearchQuery = new NativeSearchQueryBuilder()
                                                .withQuery(fallbackQueryBuilder)
                                                .withSourceFilter(new FetchSourceFilter(includes, null))
                                                .build();
                                        List<EsShopeeItem> esShopeeItems = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, fallbackSearchQuery, EsShopeeItem.class);
                                        if (CollectionUtils.isEmpty(esShopeeItems)) {
                                            continue;
                                        }
                                        // 聚合数据，方便行数据提取
                                        Map<String, EsShopeeItem> shopeeItemIdMap = esShopeeItems.stream()
                                                .filter(item -> item.getItemId() != null)
                                                .collect(Collectors.toMap(
                                                        EsShopeeItem::getId,
                                                        esShopeeItem -> esShopeeItem,
                                                        (oldV, newV) -> {
                                                            double oldPrice = oldV.getOriginalPrice() != null ? oldV.getOriginalPrice() : Double.MAX_VALUE;
                                                            double newPrice = newV.getOriginalPrice() != null ? newV.getOriginalPrice() : Double.MAX_VALUE;
                                                            return oldPrice <= newPrice ? oldV : newV;
                                                        }
                                                ));
                                        Map<String, List<EsShopeeItem>> esShopeeItemGroupMap = shopeeItemIdMap.values().stream()
                                                .filter(item -> CollectionUtils.isEmpty(skuStatusList) || (item.getSkuStatus() != null && skuStatusList.contains(item.getSkuStatus())))
                                                .collect(Collectors.groupingBy(EsShopeeItem::getId));
                                        esShopeeItemMap.putAll(esShopeeItemGroupMap);
                                    }
                                    return esShopeeItemMap;
                                }

                                /**
                                 * 获取毛利率信息
                                 * @param shopeeAccountConfig
                                 * @param esShopeeItemGroups
                                 * @param columnName
                                 * @return
                                 */
                                private Map<String, BatchPriceCalculatorResponse> getPriceCalculatorResponseMap(ShopeeAccountConfig shopeeAccountConfig, Map<String, List<EsShopeeItem>> esShopeeItemGroups, String columnName) {
                                    Integer columnNameIndex = columnNameMap.get(columnName);
                                    if (Objects.isNull(columnNameIndex)) {
                                        List<String> nameList = allReferenceColumnMap.get(columnName);
                                        if (CollectionUtils.isEmpty(nameList) || nameList.size() > 1) {
                                            return new HashMap<>();
                                        }
                                        columnNameIndex = columnNameMap.get(nameList.get(0));
                                    }
                                    if (Objects.isNull(columnNameIndex)) {
                                        return new HashMap<>();
                                    }

                                    // 获取物流信息
                                    Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic(shopeeAccountConfig.getAccount());
                                    String logisticCode = siteLogisticMap.get(shopeeAccountConfig.getSite());
                                    if (StringUtils.isBlank(logisticCode)) {
                                        throw new RuntimeException("获取物流信息失败");
                                    }

                                    // 批量计算毛利率
                                    List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = new ArrayList<>();
                                    for (Map<Integer, String> columns : dataMapList) {
                                        // 获取key
                                        String key = getColumnKey(columns);
                                        List<EsShopeeItem> esShopeeItemList = esShopeeItemGroups.get(key);
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            esShopeeItemList = esShopeeItemGroups.get(columns.get(columnNameMap.get(ITEM_ID_COLUMN)));
                                        }
                                        if (CollectionUtils.isEmpty(esShopeeItemList)) {
                                            continue;
                                        }

                                        for (EsShopeeItem esShopeeItem : esShopeeItemList) {
                                            BatchPriceCalculatorRequest req = new BatchPriceCalculatorRequest();
                                            req.setId(esShopeeItem.getId());
                                            req.setArticleNumber(esShopeeItem.getArticleNumber());
                                            req.setQuantity(1);
                                            req.setSaleChannel(Platform.Shopee.name());
                                            req.setSite(shopeeAccountConfig.getSite());
                                            req.setCountryCode(shopeeAccountConfig.getSite());
                                            req.setShippingMethod(logisticCode);
                                            if (columnName.equals(CURRENT_PRICE_COLUMN)) {
                                                // 目标毛利率区间(站点减值)
                                                List<Double> targetGrossMarginRange = crossBorderActivityParam.getTargetGrossMarginRange();
                                                List<CrossBorderActivityParam.SiteReduction> siteReductionList = crossBorderActivityParam.getSiteReduction();
                                                if (CollectionUtils.isEmpty(targetGrossMarginRange) || CollectionUtils.isEmpty(siteReductionList)) {
                                                    continue;
                                                }

                                                // 站点减值（根据区间配置来）
                                                Double price = Double.parseDouble(columns.get(columnNameIndex));
                                                for (CrossBorderActivityParam.SiteReduction siteReduction : siteReductionList) {
                                                    // 判断是否在区间内
                                                    List<Double> currentReductionList = siteReduction.getCurrentPriceRangeList();
                                                    if (shopeeAccountConfig.getSite().equals(siteReduction.getSite()) && price >= currentReductionList.get(0) && price < currentReductionList.get(1)) {
                                                        price = price - siteReduction.getReductionOperation();
                                                        break;
                                                    }
                                                }
                                                req.setSalePrice(price);
                                            } else {
                                                req.setSalePrice(Double.parseDouble(columns.get(columnNameIndex)));
                                            }
                                            batchPriceCalculatorRequestList.add(req);
                                        }

                                    }
                                    ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequestList, 3);
                                    if (!listApiResult.isSuccess()) {
                                        throw new RuntimeException("获取毛利率信息失败");
                                    }

                                    Map<String, BatchPriceCalculatorResponse> responseMap = listApiResult.getResult().stream()
                                            .collect(Collectors.toMap(
                                                    BatchPriceCalculatorResponse::getId,
                                                    Function.identity(),
                                                    (o1, o2) -> o1
                                            ));
                                    return responseMap;
                                }

                            }).headRowNumber(0).sheet().doRead();
                        } catch (Exception e) {
                            updateActivityRegistration(activityRegistration, e, innerParamDTO);
                        }
                    }
                } catch (Exception e) {
                    updateActivityRegistration(activityRegistration, e, innerParamDTO);
                }
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.info(e.getMessage());
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 更新跨境活动数据
     *
     * @param activityRegistration
     * @param e
     * @param innerParamDTO
     */
    private void updateActivityRegistration(ShopeeActivityRegistration activityRegistration, Exception e, ShopeeMarketingCrossBorderActivityMessageDTO innerParamDTO) {
        LambdaUpdateWrapper<ShopeeActivityRegistration> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ShopeeActivityRegistration::getId, activityRegistration.getId());
        wrapper.set(ShopeeActivityRegistration::getGenerationFailureReason, e.getMessage());
        wrapper.set(ShopeeActivityRegistration::getUpdatedTime, LocalDateTime.now());
        shopeeActivityRegistrationService.update(wrapper);
    }

    /**
     * 解析匹配对照列数据
     *
     * @param shopeeActivityTemplate
     * @return HashMap<String, String>
     */
    private static Map<String, String> parseeDitableKeywordReferenceColumnData(ShopeeActivityTemplate shopeeActivityTemplate) {
        // 解析 JSON 字符串为 Map<String, List<String>>
        Map<String, List<String>> referenceColumnMap = JSON.parseObject(shopeeActivityTemplate.getReferenceColumn(), new TypeReference<Map<String, List<String>>>() {
        });

        // 反向映射，仅保留包含“可编辑”的字段
        return referenceColumnMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream()
                        .filter(Objects::nonNull)
                        .filter(value -> value.contains(EDITABLE_KEYWORD))
                        .map(value -> Map.entry(value, entry.getKey()))
                )
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 解析匹配对照列数据
     *
     * @param shopeeActivityTemplate
     * @return HashMap<String, String>
     */
    private static Map<String, List<String>> parseReferenceColumnData(ShopeeActivityTemplate shopeeActivityTemplate) {
        // 解析 JSON 字符串为 Map<String, List<String>>
        Map<String, List<String>> referenceColumnMap = JSON.parseObject(shopeeActivityTemplate.getReferenceColumn(), new TypeReference<>() {
        });

        // 剔除里面包含可编辑的字段
        return referenceColumnMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(Objects::nonNull)
                                .filter(value -> !value.contains(EDITABLE_KEYWORD))
                                .collect(Collectors.toList()),
                        (v1, v2) -> v1,
                        HashMap::new
                ));

    }

    /**
     * 获取文件
     *
     * @param fileUrl
     * @return
     * @throws IOException
     */
    public static byte[] getFileBytes(String fileUrl) throws IOException {
        // 使用 RestTemplate 从 URL 下载文件
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> response = restTemplate.getForEntity(fileUrl, byte[].class);
        return response.getBody();
    }


    /**
     * 创建操作日志
     *
     * @return
     */
    private ShopeePublishOperationLog createOperateLog(Integer configId, String text) {
        ShopeePublishOperationLog operationLog = new ShopeePublishOperationLog();
        operationLog.setOpType(ShopeePublishOperationTypeEnum.CROSS_BORDER_ACTIVITY.getDesc());
        operationLog.setPlatform(SaleChannelEnum.SHOPEE.getChannelName());
        operationLog.setModId(configId.toString());
        operationLog.setState(1);
        operationLog.setObject(text);
        operationLog.setCreatedTime(LocalDateTime.now());
        operationLog.setUser("admin");
        return operationLog;
    }
}