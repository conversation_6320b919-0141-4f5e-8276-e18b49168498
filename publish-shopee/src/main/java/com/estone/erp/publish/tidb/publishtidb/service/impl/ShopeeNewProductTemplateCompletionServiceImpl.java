package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.dto.ShopeeNewProductSaleLederTreeNodeDTO;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductTemplateCompletionDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductTemplateCompletionVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductTemplateCompletionMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductTemplateCompletion;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductTemplateCompletionService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Shopee新品范本完成率统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class ShopeeNewProductTemplateCompletionServiceImpl extends ServiceImpl<ShopeeNewProductTemplateCompletionMapper, ShopeeNewProductTemplateCompletion> implements ShopeeNewProductTemplateCompletionService {


    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @Resource
    private ShopeeNewProductTemplateCompletionMapper shopeeNewProductTemplateCompletionMapper;

    @Override
    public CQueryResult<ShopeeNewProductTemplateCompletionVO> queryPage(CQuery<ShopeeNewProductTemplateCompletionDO> cQuery) {
        ShopeeNewProductTemplateCompletionDO search = cQuery.getSearch();
        if (search.getTimeType().isEmpty()) {
            search.setTimeType("day");
        }

        // 权限处理
        isPermissionProcessing(search);

        // 查询数据
        LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> lambdaQueryWrapper = getShopeeNewProductTemplateCompletionLambdaQueryWrapper(search);

        Page<ShopeeNewProductTemplateCompletionVO> doPage = new Page<>(cQuery.getPage(), cQuery.getLimit());
        IPage<ShopeeNewProductTemplateCompletionVO> pageResult = null;
        switch (search.getTimeType()) {
            case "day":
                lambdaQueryWrapper.orderByDesc(ShopeeNewProductTemplateCompletion::getPushTime);
                pageResult = shopeeNewProductTemplateCompletionMapper.selectNewProductTemplateCompletionPage(doPage, lambdaQueryWrapper);
                break;
            case "week":
                pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByWeek(doPage, lambdaQueryWrapper);
                break;
            case "month":
                pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByMonth(doPage, lambdaQueryWrapper);
                break;
            default:
                throw new IllegalArgumentException("timeType is not valid!");
        }

        // 查询推荐数据
        CQueryResult<ShopeeNewProductTemplateCompletionVO> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        List<ShopeeNewProductTemplateCompletionVO> records = pageResult.getRecords();
        // 转换为VO
        if (CollectionUtils.isNotEmpty(records)) {
            List<ShopeeNewProductTemplateCompletionVO> shopeeNewProductTemplateCompletionVOList = records.stream()
                    .peek(completion -> {
                        // 处理所有完成率的百分比计算
                        processCompletionRates(completion);

                        // 处理未完成SPU
                        String unfinishedSpuInfo = completion.getUnfinishedSpuInfo();
                        List<ShopeeNewProductSaleLederTreeNodeDTO> shopeeNewProductSaleLederTreeNodeDTOList = new ArrayList<>();
                        Gson gson = new Gson();
                        if (search.getTimeType().equals("day")) {
                            if (StringUtils.isNotBlank(unfinishedSpuInfo)) {
                                shopeeNewProductSaleLederTreeNodeDTOList = gson.fromJson(
                                        unfinishedSpuInfo,
                                        new TypeToken<List<ShopeeNewProductSaleLederTreeNodeDTO>>() {
                                        }.getType()
                                );
                            }
                        } else {
                            List<List<ShopeeNewProductSaleLederTreeNodeDTO>> shopeeNewProductSaleList = gson.fromJson(
                                    unfinishedSpuInfo,
                                    new TypeToken<List<List<ShopeeNewProductSaleLederTreeNodeDTO>>>() {
                                    }.getType()
                            );
                            // 按照销售组长分组
                            Map<String, List<ShopeeNewProductSaleLederTreeNodeDTO>> leaderMap = shopeeNewProductSaleList.stream().flatMap(List::stream).collect(Collectors.groupingBy(ShopeeNewProductSaleLederTreeNodeDTO::getSaleLeader));
                            shopeeNewProductSaleLederTreeNodeDTOList = leaderMap.keySet().stream()
                                    .map(leader -> {
                                        ShopeeNewProductSaleLederTreeNodeDTO shopeeNewProductSaleLederTreeNodeDTO = new ShopeeNewProductSaleLederTreeNodeDTO(leader);
                                        // 获取同一个 saleLeader 下的所有 DTO
                                        List<ShopeeNewProductSaleLederTreeNodeDTO> shopeeNewProductSaleLederTreeNodeDTOS = leaderMap.get(leader);

                                        // 合并 saleSpuMap
                                        Map<String, Set<String>> mergedSaleSpuMap = new HashMap<>();
                                        shopeeNewProductSaleLederTreeNodeDTOS.forEach(dto ->
                                                dto.getSaleSpuMap().forEach((sale, spuSet) ->
                                                        mergedSaleSpuMap.computeIfAbsent(sale, k -> new HashSet<>()).addAll(spuSet)
                                                )
                                        );
                                        // 设置合并后的 saleSpuMap
                                        shopeeNewProductSaleLederTreeNodeDTO.setSaleSpuMap(mergedSaleSpuMap);
                                        return shopeeNewProductSaleLederTreeNodeDTO;
                                    })
                                    .collect(Collectors.toList());
                        }
                        completion.setUnfinishedSpu(shopeeNewProductSaleLederTreeNodeDTOList);
                    })
                    .collect(Collectors.toList());
            result.setRows(shopeeNewProductTemplateCompletionVOList);
        }
        result.setSuccess(true);
        return result;
    }


    @Override
    public ApiResult<String> download(CQuery<ShopeeNewProductTemplateCompletionDO> cQuery) {
        ShopeeNewProductTemplateCompletionDO search = cQuery.getSearch();
        if (search.getTimeType().isEmpty()) {
            search.setTimeType("day");
        }
        // 权限处理
        isPermissionProcessing(search);

        // 判断导出类型
        Integer exportType = search.getExportType();
        try {
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            if (exportType.equals(1)) {
                LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> lambdaQueryWrapper = getShopeeNewProductTemplateCompletionLambdaQueryWrapper(search);

                long count = 0;
                switch (search.getTimeType()) {
                    case "day":
                        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getIdList()), ShopeeNewProductTemplateCompletion::getId, search.getIdList());
                        count = shopeeNewProductTemplateCompletionMapper.selectList(lambdaQueryWrapper).size();
                        break;
                    case "week":
                        count = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByWeek(new Page<>(0, 500000), lambdaQueryWrapper).getTotal();
                        break;
                    case "month":
                        count = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByMonth(new Page<>(0, 500000), lambdaQueryWrapper).getTotal();
                        break;
                    default:
                        throw new IllegalArgumentException("timeType is not valid!");
                }
                if (count == 0) {
                    return ApiResult.newError("没有数据，无法下载");
                }
                if (count > 500000) {
                    return ApiResult.newError("超出最大下载数量限制，无法下载");
                }

                // 构造导出日志
                downloadLog.setType(ShopeeDownloadTypeEnums.NEW_PRODUCT_TEMPLATE_COMPLETION.getType());
                downloadLog.setQueryCondition(JSON.toJSONString(search));
                downloadLog.setDownloadCount((int) count);
            } else {
                // 构造导出日志
                downloadLog.setType(ShopeeDownloadTypeEnums.NEW_PRODUCT_TEMPLATE_COMPLETION_SPU.getType());
                downloadLog.setQueryCondition(JSON.toJSONString(search));
            }
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 权限处理
     *
     * @param search
     */
    private static void isPermissionProcessing(ShopeeNewProductTemplateCompletionDO search) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        // 如果不为超级管理员，则只能查询自己的数据
        if (!superAdminOrEquivalent.getResult()) {
            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.tokenUser();
            NewUser newUser = newUserApiResult.getResult();
            boolean isLeader = StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.GROUP_LEADER);
            if (isLeader) {
                search.setSaleLeader(newUser.getEmployeeNo());
            } else {
                search.setSale(newUser.getEmployeeNo());
            }
        }
    }

    /**
     * 获取查询条件
     *
     * @param search
     * @return
     */
    private static LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> getShopeeNewProductTemplateCompletionLambdaQueryWrapper(ShopeeNewProductTemplateCompletionDO search) {
        LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSale()), ShopeeNewProductTemplateCompletion::getSale, search.getSale());
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSaleLeader()), ShopeeNewProductTemplateCompletion::getSaleLeader, search.getSaleLeader());
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSaleType()), ShopeeNewProductTemplateCompletion::getSaleType, search.getSaleType());
        lambdaQueryWrapper.ge(ObjectUtils.isNotNull(search.getPushTimeStart()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeStart());
        lambdaQueryWrapper.le(ObjectUtils.isNotNull(search.getPushTimeEnd()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeEnd());
        return lambdaQueryWrapper;
    }


    /**
     * 处理所有完成率的百分比计算
     *
     * @param completionVO ShopeeNewProductTemplateCompletionVO对象
     */
    private void processCompletionRates(ShopeeNewProductTemplateCompletionVO completionVO) {
        // 定义所有需要处理的字段
        BigDecimal[] rates = {
                completionVO.getTotalCompletionRate(),
                completionVO.getTwCompletionRate(),
                completionVO.getThCompletionRate(),
                completionVO.getMyCompletionRate(),
                completionVO.getSgCompletionRate(),
                completionVO.getPhCompletionRate(),
                completionVO.getBrCompletionRate(),
                completionVO.getVnCompletionRate(),
                completionVO.getClCompletionRate(),
                completionVO.getCoCompletionRate(),
                completionVO.getMxCompletionRate()
        };

        // 统一乘以100处理
        BigDecimal hundred = BigDecimal.valueOf(100);
        for (int i = 0; i < rates.length; i++) {
            if (rates[i] != null && rates[i].compareTo(BigDecimal.ZERO) != 0) {
                rates[i] = rates[i].multiply(hundred).setScale(2, RoundingMode.HALF_UP);
            } else {
                rates[i] = BigDecimal.ZERO; // null或0都设置为0
            }
        }

        // 设置回对象
        completionVO.setTotalCompletionRate(rates[0]);
        completionVO.setTwCompletionRate(rates[1]);
        completionVO.setThCompletionRate(rates[2]);
        completionVO.setMyCompletionRate(rates[3]);
        completionVO.setSgCompletionRate(rates[4]);
        completionVO.setPhCompletionRate(rates[5]);
        completionVO.setBrCompletionRate(rates[6]);
        completionVO.setVnCompletionRate(rates[7]);
        completionVO.setClCompletionRate(rates[8]);
        completionVO.setCoCompletionRate(rates[9]);
        completionVO.setMxCompletionRate(rates[10]);
    }

}
