package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 尺码表属性参数
 * <AUTHOR>
 * @Date 2025/6/13 16:00
 */
@Data
public class ShopeeSizeChartInfoParam {

    /**
     * size_chart 非必传
     * <p>
     * <p>
     * 大小图表图像的 ID。如果您想删除商品的图片尺寸表，请将“size_chart”留空。
     * global：注意：CB 商店和本地商店都支持设置 “size_chart”。
     */
    @JSONField(name = "size_chart")
    private String sizeChart;

    /**
     * size_chart_id 非必传
     * 模板大小图表的 ID。如果要删除项目的模板尺寸表，请将 “size_chart_id” 作为 0 传递。
     * global：注：本地店铺和 CB 店铺均支持设置“size_chart_id”，卖家需要提前在跨境引导中设置 size_chart 模板
     * local：注意：仅支持本地商店设置 “size_chart_id”，CB 商店请使用 “size_chart”。
     */
    @JSONField(name = "size_chart_id")
    private Integer sizeChartId;
}
