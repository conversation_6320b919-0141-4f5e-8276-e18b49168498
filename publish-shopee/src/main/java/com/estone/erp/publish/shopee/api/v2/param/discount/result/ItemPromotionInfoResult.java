package com.estone.erp.publish.shopee.api.v2.param.discount.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-01 上午11:59
 */
@Getter
@Setter
public class ItemPromotionInfoResult {

    @JSONField(name = "item_id")
    private Long itemId;
    @JSONField(name = "promotion")
    private List<PromotionInfo> promotion;

    @NoArgsConstructor
    @Data
    public static class PromotionInfo {
        @JSONField(name = "promotion_type")
        private String promotionType;
        @JSONField(name = "promotion_id")
        private Long promotionId;
        @JSONField(name = "model_id")
        private Long modelId;
        @JSONField(name = "start_time")
        private Long startTime;
        @JSONField(name = "end_time")
        private Long endTime;
        @JSONField(name = "promotion_staging")
        private String promotionStaging;
        @JSONField(name = "promotion_price_info")
        private List<PromotionPriceInfoDTO> promotionPriceInfo;
        @JSONField(name = "promotion_stock_info_v2")
        private PromotionStockInfoV2DTO promotionStockInfoV2;

        @NoArgsConstructor
        @Data
        public static class PromotionStockInfoV2DTO {
            @JSONField(name = "summary_info")
            private SummaryInfoDTO summaryInfo;

            @NoArgsConstructor
            @Data
            public static class SummaryInfoDTO {
                @JSONField(name = "total_reserved_stock")
                private Integer totalReservedStock;
            }
        }

        @NoArgsConstructor
        @Data
        public static class PromotionPriceInfoDTO {
            @JSONField(name = "promotion_price")
            private Double promotionPrice;
        }
    }
}
