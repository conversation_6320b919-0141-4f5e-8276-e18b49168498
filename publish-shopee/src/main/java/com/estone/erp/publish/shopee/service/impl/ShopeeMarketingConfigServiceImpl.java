package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.shopee.component.marking.BiddingActivityParam;
import com.estone.erp.publish.shopee.component.marking.CrossBorderActivityParam;
import com.estone.erp.publish.shopee.component.marking.FlashSaleConfigParam;
import com.estone.erp.publish.shopee.component.marking.MarketingConfigParam;
import com.estone.erp.publish.shopee.dto.BatchUpdateStatusDto;
import com.estone.erp.publish.shopee.dto.SyncGroupConfigAccountDto;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.mapper.ShopeeMarketingConfigLogMapper;
import com.estone.erp.publish.shopee.mapper.ShopeeMarketingConfigMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountGroupService;
import com.estone.erp.publish.shopee.service.ShopeeConfigGroupLinkService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.shopee.util.ShopeeConfigLogUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-06-06 17:22:40
 */
@Service("shopeeMarketingConfigService")
@Slf4j
public class ShopeeMarketingConfigServiceImpl implements ShopeeMarketingConfigService {

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private ShopeeMarketingConfigMapper shopeeMarketingConfigMapper;

    @Resource
    private ShopeeMarketingConfigLogMapper shopeeMarketingConfigLogMapper;

    @Resource
    private ShopeeConfigGroupLinkService shopeeConfigGroupLinkService;

    @Resource
    private ShopeeAccountGroupService shopeeAccountGroupService;

    @Override
    public int countByExample(ShopeeMarketingConfigExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeMarketingConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeMarketingConfig> search(CQuery<ShopeeMarketingConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeMarketingConfigCriteria query = cquery.getSearch();
        ShopeeMarketingConfigExample example = query.getExample();
        example.setOrderByClause(" created_time desc ");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeMarketingConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = shopeeMarketingConfigMapper.selectByExample(example);
        for (ShopeeMarketingConfig marketingConfig : shopeeMarketingConfigs) {
            // 时间配置
            String execTime = marketingConfig.getExecTime();
            Integer beforeDay = marketingConfig.getBeforeDay();
            Integer execFrequency = marketingConfig.getExecFrequency();
            StringJoiner dateConfigStringJoiner = new StringJoiner("\n");
            if (execFrequency != null) {
                // 这里是固定的
                dateConfigStringJoiner.add("执行频率：" + "日");
            }
            if (beforeDay != null) {
                dateConfigStringJoiner.add("活动开始前" + beforeDay + "天");
            }
            if (StringUtils.isNotBlank(execTime)) {
                dateConfigStringJoiner.add("执行时间：" + execTime);
            }
            marketingConfig.setDateConfig(dateConfigStringJoiner.toString());

            // 配置规则
            String ruleJson = marketingConfig.getRuleJson();
            if (StringUtils.isNotBlank(ruleJson)) {
                Integer type = marketingConfig.getType();
                Class<? extends MarketingConfigParam> jsonClass = ShopeeMarketingConfigTypeEnum.getJsonClass(type);
                MarketingConfigParam param = JSON.parseObject(ruleJson, jsonClass);
                marketingConfig.setRuleConfig(param.toExcel());
            }
        }
        // 组装结果
        CQueryResult<ShopeeMarketingConfig> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeMarketingConfigs);
        return result;
    }

    @Override
    public ShopeeMarketingConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return shopeeMarketingConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeMarketingConfig> selectByExample(ShopeeMarketingConfigExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeMarketingConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeMarketingConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        return shopeeMarketingConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeMarketingConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeMarketingConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeMarketingConfig record, ShopeeMarketingConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeMarketingConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeMarketingConfigMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ApiResult<?> saveOrUpdateShopeeMarketingConfig(ShopeeMarketingConfig shopeeMarketingConfig) {
        // 检查规则名称是否存在
        boolean existName = existName(shopeeMarketingConfig);
        if (existName) {
            return ApiResult.newError("规则名称已存在！");
        }

        // 校验店铺是否可用
        List<String> accountGroupNames = Arrays.asList(StringUtils.split(shopeeMarketingConfig.getAccounts(), ","));
        Map<String, List<String>> accountMap = saleAccountService.filterFrozenStoreAndSip(SaleChannel.CHANNEL_SHOPEE, accountGroupNames);
        if (MapUtils.isEmpty(accountMap) || CollectionUtils.isEmpty(accountMap.get("execAccounts"))) {
            return ApiResult.newError("无可用店铺！");
        }
        shopeeMarketingConfig.setAccounts(StringUtils.join(accountMap.get("execAccounts"), ","));

        // 检查分组是否存在
        if (Objects.equals(shopeeMarketingConfig.getAccountType(), ShopeeMarketingAccountTypeEnum.GROUP.getCode())) {
            shopeeAccountGroupService.existGroup(shopeeMarketingConfig.getGroupIds());
            String groupIds = shopeeMarketingConfig.getGroupIds();
            String[] split = groupIds.split(",");
            ShopeeAccountGroupExample example = new ShopeeAccountGroupExample();
            example.createCriteria().andIdIn(Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList()));
            List<ShopeeAccountGroup> shopeeAccountGroups = shopeeAccountGroupService.selectByExample(example);
            List<String> collect = shopeeAccountGroups.stream().map(ShopeeAccountGroup::getGroupName).collect(Collectors.toList());
            shopeeMarketingConfig.setGroupNames(StringUtils.join(collect, ","));
        } else {
            shopeeMarketingConfig.setGroupNames("");
            shopeeMarketingConfig.setGroupIds("");
        }
        Integer status = shopeeMarketingConfig.getStatus();
        Integer id = shopeeMarketingConfig.getId();
        Integer accountType = shopeeMarketingConfig.getAccountType();
        if (accountType == null) {
            return ApiResult.newError("请选择账号类型！");
        }
        this.initAttributeAndExtend(shopeeMarketingConfig);

        // 检查配置规则，这个不需要sql，提前做
        MarketingConfigParam param = shopeeMarketingConfig.getMarketingConfigParam();
        if (param == null) {
            return ApiResult.newError("配置信息错误！");
        }
        try {
            boolean check = param.check();
            if (!check) {
                return ApiResult.newError("配置信息错误！");
            }
            String s = param.initJson();
            shopeeMarketingConfig.setRuleJson(s);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

        // 启用状态下，检查是否存在其他启用状态的配置
        if (status != null && status == 1) {
            String message = checkEnableStatus(shopeeMarketingConfig);
            if (StringUtils.isNotBlank(message)) {
                return ApiResult.newError(message);
            }
        }

        // 优先级校验
        String checkEnablePriorityMessage = checkEnablePriority(shopeeMarketingConfig);
        if (StringUtils.isNotBlank(checkEnablePriorityMessage)) {
            return ApiResult.newError(checkEnablePriorityMessage);
        }

        ShopeeMarketingConfig oldShopeeMarketingConfig = null;
        MarketingConfigParam oldParam = null;
        if (id != null) {
            oldShopeeMarketingConfig = selectByPrimaryKey(id);
            if (oldShopeeMarketingConfig != null) {
                oldParam = oldShopeeMarketingConfig.getMarketingConfigParam();
            }
            if (oldParam != null) {
                // 切换了奖励类型，要将奖励的值置空
                if (oldParam.rewardType() != null && param.rewardType() != null) {
                    if (!Objects.equals(oldParam.rewardType(), param.rewardType())) {
                        oldParam.rewardValueInitNull();
                    }
                }
            }
        }

        Timestamp now = new Timestamp(System.currentTimeMillis());

        if (id == null) {
            shopeeMarketingConfig.setCreatedBy(WebUtils.getUserName());
            shopeeMarketingConfig.setCreatedTime(now);
            shopeeMarketingConfig.setUpdatedBy(null);
            shopeeMarketingConfig.setUpdatedTime(null);
            shopeeMarketingConfigMapper.insert(shopeeMarketingConfig);
        } else {
            shopeeMarketingConfig.setId(id);
            shopeeMarketingConfig.setUpdatedBy(WebUtils.getUserName());
            shopeeMarketingConfig.setUpdatedTime(now);
            shopeeMarketingConfigMapper.updateByPrimaryKey(shopeeMarketingConfig);
        }

        List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs = addLog(oldShopeeMarketingConfig, oldParam, shopeeMarketingConfig, param);

        if (CollectionUtils.isNotEmpty(shopeeMarketingConfigLogs)) {
            shopeeMarketingConfigLogMapper.batchInsert(shopeeMarketingConfigLogs);
        }

        // 同步到分组店铺配置映射表
        shopeeConfigGroupLinkService.updateLink(shopeeMarketingConfig.getId(), shopeeMarketingConfig.getGroupIds(), shopeeMarketingConfig.getName(), shopeeMarketingConfig.getStatus(),
                shopeeMarketingConfig.getAccountType(), ShopeeConfigGroupLinkTypeEnums.marketingTypeConvertToEnum(ShopeeMarketingConfigTypeEnum.convertEnum(shopeeMarketingConfig.getType())));

        // 判断是否存在过滤店铺
        if (CollectionUtils.isNotEmpty(accountMap.get("filterAccounts"))) {
            return ApiResult.newSuccess(String.format("过滤店铺：%s", accountMap.get("filterAccounts").stream().collect(Collectors.joining(","))));
        }

        return ApiResult.newSuccess(shopeeMarketingConfig);
    }

    /**
     * 初始化属性和扩展属性
     *
     * @param shopeeMarketingConfig
     */
    private void initAttributeAndExtend(ShopeeMarketingConfig shopeeMarketingConfig) {
        // 频率
        if (shopeeMarketingConfig.getExecFrequency() == null) {
            shopeeMarketingConfig.setExecFrequency(1);
        }
        // 账号类型
        Integer accountType = shopeeMarketingConfig.getAccountType();
        if (accountType.equals(ShopeeMarketingAccountTypeEnum.GROUP.getCode())) {
            shopeeMarketingConfig.setSites(null);
        } else if (accountType.equals(ShopeeMarketingAccountTypeEnum.SITE.getCode())) {
            shopeeMarketingConfig.setGroupNames(null);
        } else if (accountType.equals(ShopeeMarketingAccountTypeEnum.ACCOUNT.getCode())) {
            shopeeMarketingConfig.setGroupNames(null);
            shopeeMarketingConfig.setSites(null);
        }

        // 扩展属性
        MarketingConfigParam param = shopeeMarketingConfig.getMarketingConfigParam();
        shopeeMarketingConfig.setAttribute1(param.attribute1());
        shopeeMarketingConfig.setAttribute2(param.attribute2());
    }

    /**
     * 校验优先级
     *
     * @param shopeeMarketingConfig
     * @return
     */
    private String checkEnablePriority(ShopeeMarketingConfig shopeeMarketingConfig) {
        Integer type = shopeeMarketingConfig.getType();
        String accounts = shopeeMarketingConfig.getAccounts();

        // 跨境卖家活动配置(店铺+绑定模版+报名方式及活动类型)
        if (Objects.equals(type, ShopeeMarketingConfigTypeEnum.CROSS_BORDER_ACTIVITY.getCode())) {
            CrossBorderActivityParam param = (CrossBorderActivityParam) shopeeMarketingConfig.getMarketingConfigParam();
            List<String> accountList = Arrays.asList(accounts.split(",")); // 直接使用 Arrays.asList 替代 stream

            // 查询店铺数据
            ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
            example.createCriteria()
                    .andTypeEqualTo(type)
                    .andAccountsLikeIn(accountList);
            List<ShopeeMarketingConfig> configs = shopeeMarketingConfigMapper.selectByExample(example);

            // 遍历检查是否存在相同优先级规则
            for (ShopeeMarketingConfig config : configs) {
                CrossBorderActivityParam crossBorderParam = (CrossBorderActivityParam) config.getMarketingConfigParam();
                // 判断店铺+绑定模版+报名方式及活动类型
                if (param.getPriority() == crossBorderParam.getPriority() &&
                        param.getRegistrationMethod().equals(crossBorderParam.getRegistrationMethod()) &&
                        !Collections.disjoint(param.getActivityTemplateIdList(), crossBorderParam.getActivityTemplateIdList())
                ) {
                    if (Objects.nonNull(shopeeMarketingConfig.getId()) && Objects.equals(shopeeMarketingConfig.getId(), config.getId())) {
                        continue;
                    }

                    return "存在相同优先级等级规则，规则名称为：" + config.getName();
                }
            }
        }

        // 竞价活动配置（店铺）
        if (Objects.equals(type, ShopeeMarketingConfigTypeEnum.BIDDING_ACTIVITY.getCode())) {
            BiddingActivityParam param = (BiddingActivityParam) shopeeMarketingConfig.getMarketingConfigParam();
            List<String> accountList = Arrays.asList(accounts.split(",")); // 直接使用 Arrays.asList 替代 stream

            // 查询店铺数据
            ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
            example.createCriteria()
                    .andTypeEqualTo(type)
                    .andAccountsLikeIn(accountList);
            List<ShopeeMarketingConfig> configs = shopeeMarketingConfigMapper.selectByExample(example);

            // 遍历检查是否存在相同优先级规则
            for (ShopeeMarketingConfig config : configs) {
                BiddingActivityParam activityParam = (BiddingActivityParam) config.getMarketingConfigParam();
                // 判断店铺
                if (param.getPriority() == activityParam.getPriority()) {
                    if (Objects.nonNull(shopeeMarketingConfig.getId()) && Objects.equals(shopeeMarketingConfig.getId(), config.getId())) {
                        continue;
                    }
                    return "存在相同优先级等级规则，规则名称为：" + config.getName();
                }
            }
        }
        return null;
    }

    /**
     * 检查开启状态的数据是否存在
     *
     * @param shopeeMarketingConfig
     * @return
     */
    private String checkEnableStatus(ShopeeMarketingConfig shopeeMarketingConfig) {
        String accounts = shopeeMarketingConfig.getAccounts();
        Integer type = shopeeMarketingConfig.getType();
        if (Objects.equals(type, ShopeeMarketingConfigTypeEnum.CROSS_BORDER_ACTIVITY.getCode()) || Objects.equals(type, ShopeeMarketingConfigTypeEnum.BIDDING_ACTIVITY.getCode())) {
            return null;
        }

        // 首先判断配置的账号是否添加过该配置
        String[] split = accounts.split(",");
        List<String> list = Arrays.asList(split);

        List<ShopeeMarketingConfig> existShopeeMarketingConfigs = new ArrayList<>();
        // 秒杀配置，要求是时间不能重复
        if (Objects.equals(type, ShopeeMarketingConfigTypeEnum.FLASH_SALE.getCode())) {
            FlashSaleConfigParam flashSaleConfigParam = (FlashSaleConfigParam) shopeeMarketingConfig.getMarketingConfigParam();

            // 单个秒杀活动需校验重复
            if (Objects.isNull(flashSaleConfigParam.getActivityType()) || ShopeeFlashSaleActivityTypeEnum.SINGLE_FLASH_SALE.getCode().equals(flashSaleConfigParam.getActivityType())) {
                String day = flashSaleConfigParam.getDay();
                String timePeriod = flashSaleConfigParam.getTimePeriod();

                ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
                example.setCustomColumn("accounts");
                ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria()
                        .andTypeEqualTo(type)
                        .andStatusEqualTo(1)
                        .andAccountsLikeIn(list)
                        .andAttribute1EqualTo(day)
                        .andAttribute2EqualTo(timePeriod);
                if (shopeeMarketingConfig.getId() != null) {
                    criteria.andIdNotEqualTo(shopeeMarketingConfig.getId());
                }
                existShopeeMarketingConfigs = shopeeMarketingConfigMapper.selectByExample(example);
            }
        } else if (Objects.equals(type, ShopeeMarketingConfigTypeEnum.VOUCHER.getCode())){
            // 不需要判断
        } else {
            ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
            example.setCustomColumn("accounts");
            ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria()
                    .andTypeEqualTo(type)
                    .andStatusEqualTo(1)
                    .andAccountsLikeIn(list);
            if (shopeeMarketingConfig.getId() != null) {
                criteria.andIdNotEqualTo(shopeeMarketingConfig.getId());
            }
            existShopeeMarketingConfigs = selectByExample(example);
        }

        Set<String> existAccounts = existShopeeMarketingConfigs.stream().map(a -> a.getAccounts().split(",")).flatMap(Arrays::stream).collect(Collectors.toSet());

        List<String> collect = list.stream().filter(existAccounts::contains).collect(Collectors.toList());

        if (!collect.isEmpty()) {
            int size = collect.size();
            String flagStr = "";
            if (collect.size() > 10) {
                flagStr = "...";
                collect = collect.stream().limit(10).collect(Collectors.toList());
            }
            return "账号：" + String.join(",", collect) + flagStr + " 等" + size + "个配置已存在";
        }
        return null;
    }

    /**
     * 日志
     *
     * @param oldShopeeMarketingConfig
     * @param oldParam
     * @param shopeeMarketingConfig
     * @param param
     * @param <T>
     * @return
     */
    private <T extends MarketingConfigParam> List<ShopeeMarketingConfigLog> addLog(ShopeeMarketingConfig oldShopeeMarketingConfig, T oldParam, ShopeeMarketingConfig shopeeMarketingConfig, T param) {
        List<ShopeeMarketingConfigLog> list = new ArrayList<>();
        if (oldShopeeMarketingConfig == null) {
            return list;
        }
        if (oldParam == null) {
            return list;
        }
        Integer id = oldShopeeMarketingConfig.getId();
        List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs = ShopeeConfigLogUtil.generateLog(shopeeMarketingConfig, oldShopeeMarketingConfig, id, ShopeeMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG);
        list.addAll(shopeeMarketingConfigLogs);
        List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs1 = ShopeeConfigLogUtil.generateLog(param, oldParam, id, ShopeeMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG);
        list.addAll(shopeeMarketingConfigLogs1);
        return list;
    }

    private boolean existName(ShopeeMarketingConfig shopeeMarketingConfig) {
        String name = shopeeMarketingConfig.getName();
        Integer id = shopeeMarketingConfig.getId();
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria().andNameEqualTo(name)
                .andTypeEqualTo(shopeeMarketingConfig.getType());
        if (id != null) {
            criteria.andIdNotEqualTo(id);
        }
        example.setCustomColumn("id");
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = selectByExample(example);
        return CollectionUtils.isNotEmpty(shopeeMarketingConfigs);
    }

    @Override
    public List<ShopeeMarketingConfig> getRunningConfig(Date date, ShopeeMarketingConfigTypeEnum shopeeMarketingConfigTypeEnum) {
        String day = DateUtils.format(date, "yyyy-MM-dd");
        // yyyy-MM-dd 00:00:00
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(shopeeMarketingConfigTypeEnum.getCode());
        criteria.andStatusEqualTo(1);
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = selectByExample(example);

        // 筛选出当前时间段的配置
        List<ShopeeMarketingConfig> list = new ArrayList<>();
        for (ShopeeMarketingConfig shopeeMarketingConfig : shopeeMarketingConfigs) {
            Timestamp strategyStartTime = shopeeMarketingConfig.getStrategyStartTime();
            Timestamp strategyEndTime = shopeeMarketingConfig.getStrategyEndTime();
            String execTime = shopeeMarketingConfig.getExecTime();
            Date execTimeDate = DateUtils.parseDate(day + " " + execTime, "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm:ss");
            if (execTimeDate == null) {
                continue;
            }
            if (strategyStartTime != null && strategyEndTime != null) {
                if (DateUtils.betweenStartTimeAndEndTime(execTimeDate, strategyStartTime, strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime != null && strategyEndTime == null) {
                if (execTimeDate.getTime() == strategyStartTime.getTime() || execTimeDate.after(strategyStartTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime != null) {
                if (execTimeDate.getTime() == strategyEndTime.getTime() || execTimeDate.before(strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime == null) {
                list.add(shopeeMarketingConfig);
            }
        }
        return list;
    }

    @Override
    public List<ShopeeMarketingConfig> getRunningConfigByDateHours(Date date, ShopeeMarketingConfigTypeEnum shopeeMarketingConfigTypeEnum) {
        String day = DateUtils.format(date, "yyyy-MM-dd");
        String currentDayHour = DateUtils.format(date, "yyyy-MM-dd HH");
        Date currentDayHourDate = DateUtils.parseDate(currentDayHour, "yyyy-MM-dd HH");
        // yyyy-MM-dd HH
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(shopeeMarketingConfigTypeEnum.getCode());
        criteria.andStatusEqualTo(1);
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = selectByExample(example);

        // 筛选出当前时间段的配置
        List<ShopeeMarketingConfig> list = new ArrayList<>();
        for (ShopeeMarketingConfig shopeeMarketingConfig : shopeeMarketingConfigs) {
            Timestamp strategyStartTime = shopeeMarketingConfig.getStrategyStartTime();
            Timestamp strategyEndTime = shopeeMarketingConfig.getStrategyEndTime();
            String execTime = shopeeMarketingConfig.getExecTime();
            if (StringUtils.isBlank(execTime)) {
                XxlJobLogger.log("配置id:[{}],执行时间为空", shopeeMarketingConfig.getId());
                log.error("配置id:[{}],执行时间为空", shopeeMarketingConfig.getId());
                continue;
            }
            String[] split = execTime.split(":");
            String hour = split[0];
            Date execTimeDate = DateUtils.parseDate(day + " " + hour, "yyyy-MM-dd HH", "yyyy-MM-dd HH");
            if (execTimeDate == null) {
                continue;
            }
            // 不是同一小时，不处理  竞价活动配置执行特殊需忽略执行频次
            if (!DateUtils.isTheTime(execTimeDate, currentDayHourDate, "yyyy-MM-dd HH")
                    && !ShopeeMarketingConfigTypeEnum.BIDDING_ACTIVITY.getCode().equals(shopeeMarketingConfigTypeEnum.getCode())) {
                continue;
            }

            // 接下来判断执行时间是否在策略时间内
            if (strategyStartTime != null && strategyEndTime != null) {
                if (DateUtils.betweenStartTimeAndEndTime(execTimeDate, strategyStartTime, strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime != null && strategyEndTime == null) {
                if (execTimeDate.getTime() == strategyStartTime.getTime() || execTimeDate.after(strategyStartTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime != null) {
                if (execTimeDate.getTime() == strategyEndTime.getTime() || execTimeDate.before(strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime == null) {
                list.add(shopeeMarketingConfig);
            }
        }
        return list;
    }

    @Override
    public void fixGroupIds(List<ShopeeMarketingConfig> shopeeMarketingConfigs) {
        if (CollectionUtils.isEmpty(shopeeMarketingConfigs)) {
            return;
        }

        List<List<ShopeeMarketingConfig>> lists = PagingUtils.newPagingList(shopeeMarketingConfigs, 1000);
        for (List<ShopeeMarketingConfig> list : lists) {
            shopeeMarketingConfigMapper.fixGroupIds(list);
        }
    }

    @Override
    public void syncGroupAccount(ShopeeMarketingConfig shopeeMarketingConfig, SyncGroupConfigAccountDto dto) {
        String accounts = shopeeMarketingConfig.getAccounts();
        String groupNames = shopeeMarketingConfig.getGroupNames();
        String userName = WebUtils.getUserName();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        shopeeMarketingConfig.setGroupNames(StringUtils.join(dto.getNewGroupName(), ","));
        shopeeMarketingConfig.setAccounts(StringUtils.join(dto.getNewAccount(), ","));
        shopeeMarketingConfig.setGroupIds(StringUtils.join(dto.getNewGroupId(), ","));
        // 检查
        Integer status = shopeeMarketingConfig.getStatus();
        if (status == 1) {
            String msg = checkEnableStatus(shopeeMarketingConfig);
            if (StringUtils.isNotBlank(msg)) {
                throw new RuntimeException(msg);
            }
        }
        List<ShopeeMarketingConfigLog> list = new ArrayList<>();

        ShopeeMarketingConfigLog shopeeMarketingConfigLog1 = ShopeeConfigLogUtil.checkEqualLog(
                shopeeMarketingConfig.getId(), ShopeeMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG,
                "accounts", "适用店铺", accounts, shopeeMarketingConfig.getAccounts(), userName, now);
        if (shopeeMarketingConfigLog1 != null) {
            list.add(shopeeMarketingConfigLog1);
        }

        ShopeeMarketingConfigLog shopeeMarketingConfigLog2 = ShopeeConfigLogUtil.checkEqualLog(
                shopeeMarketingConfig.getId(), ShopeeMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG,
                "groupNames", "分组", groupNames, shopeeMarketingConfig.getGroupNames(),
                userName, now);
        if (shopeeMarketingConfigLog2 != null) {
            list.add(shopeeMarketingConfigLog2);
        }

        shopeeMarketingConfig.setUpdatedBy(userName);
        shopeeMarketingConfig.setUpdatedTime(now);
        // 更新 group
        shopeeMarketingConfigMapper.updateGroup(shopeeMarketingConfig);

        if (CollectionUtils.isNotEmpty(list)) {
            shopeeMarketingConfigLogMapper.batchInsert(list);
        }
    }

    @Override
    public Map<Integer, String> getRuleNameById(List<Integer> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return Map.of();
        }
        List<Integer> ids = new ArrayList<>(collect);
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        example.createCriteria().andIdIn(ids);
        List<ShopeeMarketingConfig> shopeeMarketingRules = shopeeMarketingConfigMapper.selectByExample(example);
        return shopeeMarketingRules.stream().collect(Collectors.toMap(ShopeeMarketingConfig::getId, ShopeeMarketingConfig::getName));
    }

    @Override
    public List<Integer> getConfigIdByLikeRuleName(String likeRuleName, ShopeeMarketingConfigTypeEnum type) {
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        example.setCustomColumn("id");
        example.createCriteria().andNameLike("%" + likeRuleName + "%")
                        .andTypeEqualTo(type.getCode());
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = shopeeMarketingConfigMapper.selectByExample(example);
        return shopeeMarketingConfigs.stream().map(ShopeeMarketingConfig::getId).collect(Collectors.toList());
    }

    @Override
    public ApiResult<?> updateStatus(BatchUpdateStatusDto requestParam) {
        String tip = "操作%s条，成功%s条，失败%s条";
        int sum = 0;
        int success = 0;
        int fail = 0;
        String userName = WebUtils.getUserName();
        Timestamp now = new Timestamp(System.currentTimeMillis());
        try {
            if (requestParam == null || requestParam.getStatus() == null || CollectionUtils.isEmpty(requestParam.getIds())) {
                return ApiResult.newError("缺少必填参数");
            }
            //需要修改的状态
            Integer status = requestParam.getStatus();
            List<Integer> idList = requestParam.getIds();
            sum = idList.size();
            ShopeeMarketingConfigExample shopeeMarketingConfigExample = new ShopeeMarketingConfigExample();
            shopeeMarketingConfigExample.createCriteria().andIdIn(idList);
            List<ShopeeMarketingConfig> shopeeMarketingConfigs = this.selectByExample(shopeeMarketingConfigExample);

            List<ShopeeMarketingConfig> updateList = new ArrayList<>();
            List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs = new ArrayList<>();
            for (ShopeeMarketingConfig shopeeMarketingConfig : shopeeMarketingConfigs) {
                if (status == 1) {
//                    //启用需要 额外校验时间 1-全部策略校验，若策略开始时间早于当前日期则不允许启用
//                    Timestamp strategyStartTime = shopeeMarketingConfig.getStrategyStartTime();
//                    if (strategyStartTime.getTime() < System.currentTimeMillis()) {
//                        log.info("规则{}, 早于当前时间", shopeeMarketingConfig.getName());
//                        continue;
//                    }
                    // 启用状态下，检查是否存在其他启用状态的配置
                    if (status != null && status == 1) {
                        String message = checkEnableStatus(shopeeMarketingConfig);
                        if (StringUtils.isNotBlank(message)) {
                            continue;
                        }
                    }
                }
                ShopeeMarketingConfigLog shopeeMarketingConfigLog = ShopeeConfigLogUtil.checkEqualLog(shopeeMarketingConfig.getId(), ShopeeMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG,
                        "status", "启用状态",
                        shopeeMarketingConfig.getStatus(),status , userName, now);

                if (shopeeMarketingConfigLog != null) {
                    shopeeMarketingConfigLogs.add(shopeeMarketingConfigLog);
                }
                shopeeMarketingConfig.setUpdatedBy(userName);
                shopeeMarketingConfig.setUpdatedTime(now);
                shopeeMarketingConfig.setStatus(status);
                updateList.add(shopeeMarketingConfig);
                shopeeMarketingConfigMapper.updateByPrimaryKeySelective(shopeeMarketingConfig);
            }
            //记录日志
            if (CollectionUtils.isNotEmpty(shopeeMarketingConfigLogs)) {
                shopeeMarketingConfigLogMapper.batchInsert(shopeeMarketingConfigLogs);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
//                shopeeMarketingConfigMapper.batchUpdate(updateList);
                // 同步到分组店铺配置映射表
                Map<Integer, List<Integer>> productInfoMap = updateList.stream()
                        .collect(Collectors.groupingBy(
                                ShopeeMarketingConfig::getType,
                                Collectors.mapping(ShopeeMarketingConfig::getId, Collectors.toList())
                        ));
                productInfoMap.forEach((type, ids) -> {
                    shopeeConfigGroupLinkService.updateStatusLink(ids, requestParam.getStatus(), ShopeeConfigGroupLinkTypeEnums.marketingTypeConvertToEnum(ShopeeMarketingConfigTypeEnum.convertEnum(type)));
                });
            }
            success = updateList.size();
            fail = sum - success;
            tip = String.format(tip, sum, success, fail);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(tip);
    }

    @Override
    public List<ShopeeMarketingConfig> getEnableConfig(boolean b, Integer configType) {
        int status = b ? 1:0;
        ShopeeMarketingConfigExample shopeeMarketingConfigExample = new ShopeeMarketingConfigExample();
        shopeeMarketingConfigExample.createCriteria().andStatusEqualTo(status).andTypeEqualTo(configType);
        return selectByExample(shopeeMarketingConfigExample);
    }

    @Override
    public List<ShopeeMarketingConfig> getConfig(Integer configType) {
        ShopeeMarketingConfigExample shopeeMarketingConfigExample = new ShopeeMarketingConfigExample();
        shopeeMarketingConfigExample.createCriteria().andTypeEqualTo(configType);
        return selectByExample(shopeeMarketingConfigExample);
    }

    @Override
    public List<String> selectAccountsByTypeAndStatus(ShopeeMarketingConfigTypeEnum shopeeMarketingConfigTypeEnum, int status) {
        ShopeeMarketingConfigExample shopeeMarketingConfigExample = new ShopeeMarketingConfigExample();
        shopeeMarketingConfigExample.createCriteria().andTypeEqualTo(shopeeMarketingConfigTypeEnum.getCode()).andStatusEqualTo(status);
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = selectByExample(shopeeMarketingConfigExample);

        // 获取店铺账号
        List<String> accounts = shopeeMarketingConfigs.stream().map(ShopeeMarketingConfig::getAccounts).collect(Collectors.toList());
        return accounts.stream().flatMap(account -> Arrays.stream(account.split(","))).distinct().collect(Collectors.toList());
    }
}