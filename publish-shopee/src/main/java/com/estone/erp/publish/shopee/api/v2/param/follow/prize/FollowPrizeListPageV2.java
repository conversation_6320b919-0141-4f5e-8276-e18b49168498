package com.estone.erp.publish.shopee.api.v2.param.follow.prize;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingActivitiesStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 关注礼
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FollowPrizeListPageV2 implements RequestCommon {

    @JSONField(name = "page_no")
    private Integer pageNo;

    @JSONField(name = "page_size")
    private Integer pageSize;

    /**
     * @see ShopeeMarketingActivitiesStatusEnum
     */
    @JSONField(name = "status")
    private String status;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_FOLLOW_PRIZE_LIST;
    }
}
