package com.estone.erp.publish.shopee.api.v2.param.listing;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 17:43
 * @description
 */
@Getter
@Setter
public class GetModelListV2 implements RequestCommon{

    @JSONField(name = "item_id")
    private Long itemId ;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_MODEL_LIST;
    }
}
