package com.estone.erp.publish.shopee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 属性树格式类型枚举 原：NORMAL QUANTITATIVE
 * <AUTHOR>
 * @Date 2025/6/23 17:08
 */
@Getter
@AllArgsConstructor
public enum ShopeeAttributeTreeFormatTypeEnum {

    FORMAT_NORMAL(1, "NORMAL"),
    FORMAT_QUANTITATIVE_WITH_UNIT(2, "QUANTITATIVE");

    private final int code;
    private final String value;

    public static String convert(int code) {
        for (ShopeeAttributeTreeFormatTypeEnum type : values()) {
            if (type.code == code) {
                return type.value;
            }
        }
        return null;
    }
}
