package com.estone.erp.publish.shopee.api.v2.cnsc.global_item;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/14 17:29
 * @description
 */
@Getter
@Setter
public class GetGlobalItemInfo implements RequestCommon {

    @JSONField(name = "global_item_id_list")
    private List<Long> globalItemIdList;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_GLOBAL_ITEM_INFO;
    }
}
