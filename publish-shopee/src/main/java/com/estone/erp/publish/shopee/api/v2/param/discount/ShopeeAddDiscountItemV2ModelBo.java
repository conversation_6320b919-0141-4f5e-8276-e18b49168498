package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 14:23
 * @description
 */
@Getter
@Setter
public class ShopeeAddDiscountItemV2ModelBo {


    /** 变体id (必填)*/
    @JSONField(name = "model_id")
    private Long modelId;

    /** 商品的折扣价 (必填)*/
    @JSONField(name = "model_promotion_price")
    private Double modelPromotionPrice;

    /** 模型预留库存，默认无限制，不可更新。 如果要更新，请删除此项目，然后重新添加 */
    @JSONField(name = "model_promotion_stock")
    private Integer modelPromotionStock;

}
