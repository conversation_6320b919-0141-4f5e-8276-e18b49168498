package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/19 9:59
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class ShopeeAddBundleDealV2 implements RequestCommon {
    private Integer rule_type;
    private Float discount_value;
    private Float fix_price;
    private Integer discount_percentage;
    private Integer min_amount;
    private long start_time;
    private long end_time;
    private String name;
    private Integer purchase_limit;
    private List<AdditionalTiersDTO> additional_tiers;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_BUNDLE_DEAL;
    }


    @NoArgsConstructor
    @Data
    public static class AdditionalTiersDTO {
        private Integer min_amount;
        private Float fix_price;
        private Float discount_value;
        private Integer discount_percentage;
    }
}
