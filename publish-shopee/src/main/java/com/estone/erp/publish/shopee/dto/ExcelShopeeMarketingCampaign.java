package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.shopee.converter.ShopeeMarketingCampaignSonStatusConverter;
import com.estone.erp.publish.shopee.converter.StatusToYesOrNotConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * shopee 市场营销 excel 实体类
 */
@Data
public class ExcelShopeeMarketingCampaign {


    /**
     * 父活动名称
     */
    @ExcelProperty("所属父活动名称")
    private String parentTitle;

    /**
     * 子活动标题
     */
    @ExcelProperty("子活动名称")
    private String sonTitle;

    @ExcelProperty(value = "父活动ID", converter = LongStringConverter.class)
    private Long parentCampaignId;

    @ExcelProperty(value = "子活动ID", converter = LongStringConverter.class)
    private Long sonCampaignId;

    /**
     * 商家名称
     */
    @ExcelProperty("商家")
    private String merchantName;

    /**
     * 店铺账号
     */
    @ExcelProperty("店铺")
    private String accountNumber;

    /**
     * 站点
     */
    @ExcelProperty("站点")
    private String site;

    @ExcelProperty(value = "店铺分组")
    private String groupName;
    /**
     * 子活动是否报名状态
     */
    @ExcelProperty(value = "子活动状态", converter = ShopeeMarketingCampaignSonStatusConverter.class)
    private Integer sonStatus;
    /**
     * 子活动商品已报名数量
     */
    @ExcelProperty(value = "已报名商品数量")
    private Integer sonSubmitNumber;
    /**
     * 子活动可报名商品数量
     */
    @ExcelProperty(value = "可报名商品数量")
    private Integer sonApplyNumber;

    /**
     * 子活动报名开始时间
     */
    @ExcelProperty(value = "子活动报名开始时间", converter = TimestampFormatConverter.class)
    private Timestamp sonNominationStartDate;

    /**
     * 子活动报名结束时间
     */
    @ExcelProperty(value = "子活动报名结束时间", converter = TimestampFormatConverter.class)
    private Timestamp sonNominationEndDate;

    /**
     * 子活动进行开始时间 database column shopee_marketing_campaign.son_start_date
     */
    @ExcelProperty(value = "子活动进行开始时间", converter = TimestampFormatConverter.class)
    private Timestamp sonStartDate;

    /**
     * 子活动进行结束时间 database column shopee_marketing_campaign.son_end_date
     */
    @ExcelProperty(value = "子活动进行结束时间", converter = TimestampFormatConverter.class)
    private Timestamp sonEndDate;

    /**
     * 父活动开始时间
     */
    @ExcelProperty(value = "所属父活动开始时间", converter = TimestampFormatConverter.class)
    private Timestamp parentStartDate;

    /**
     * 父活动结束时间
     */
    @ExcelProperty(value = "所属父活结束始时间", converter = TimestampFormatConverter.class)
    private Timestamp parentEndDate;

    /**
     * 子活动卖家提名
     */
    @ExcelProperty(value = "卖家提名数量")
    private Integer sonSellerApply;

    /**
     * 子活动待确认商品数量
     */
    @ExcelProperty(value = "待确认商品数量")
    private Integer sonToBeConfirmedNumber;

    /**
     * 子活动已拒绝商品数量
     */
    @ExcelProperty(value = "已拒绝商品数量")
    private Integer sonRejectNumber;

    /**
     * 子活动shopee反向提名
     */
    @ExcelProperty(value = "Shopee反向报名数量")
    private Integer sonSonReverseApply;

    /**
     * 子活动shopee推荐数量
     */
    @ExcelProperty(value = "Shopee推荐数量")
    private Integer sonShopeeApply;

    /**
     * 子活动商品未提交数量
     */
    @ExcelProperty(value = "尚未提交商品数量")
    private Integer sonUnsubmittedNumber;

    /**
     * 可报名子活动数量
     */
    @ExcelProperty(value = "可报名子活动数量")
    private Integer availableNumber;
    /**
     * 待确认子活动数量
     */
    @ExcelProperty(value = "待确认子活动数量")
    private Integer toBeConfirmedNumber;
    /**
     * 已经报名子活动数量
     */
    @ExcelProperty(value = "已报名子活动数量")
    private Integer alreadyNumber;

    @ExcelProperty(value = "已报名无符合商品", converter = StatusToYesOrNotConverter.class)
    private Integer registeredNotData;

    /**
     * 销售
     */
    @ExcelProperty(value = "销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty(value = "销售组长")
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp syncTime;

    /**
     * 爬虫时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "爬虫时间", converter = TimestampFormatConverter.class)
    private Timestamp crawlTime;

}
