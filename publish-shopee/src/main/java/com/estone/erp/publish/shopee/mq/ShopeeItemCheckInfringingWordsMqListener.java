package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.component.ShopeeItemEsBulkProcessor;
import com.estone.erp.publish.shopee.dto.ShopeeMqAccountSitesDto;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.util.ShopeeCommonUtils;
import com.estone.erp.publish.shopee.util.ShopeeDescriptionUtil;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringementWordInfo;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.data.domain.Page;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * shopee 在线列表侵权词校验
 */
@Slf4j
public class ShopeeItemCheckInfringingWordsMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeItemEsService shopeeItemEsService;
    @Resource
    private ShopeeItemEsBulkProcessor shopeeItemEsBulkProcessor;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            ShopeeMqAccountSitesDto dto = JSON.parseObject(body, new TypeReference<ShopeeMqAccountSitesDto>() {
            });

            boolean sign = false;
            try {
                checkInfringingWordsByAccount(dto);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeItemCheckInfringingWordsMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeItemCheckInfringingWordsMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeItemCheckInfringingWordsMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeItemCheckInfringingWordsMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    private void checkInfringingWordsByAccount(ShopeeMqAccountSitesDto dto) {
        if (dto == null || StringUtils.isBlank(dto.getAccountNumber())) {
            log.error("ShopeeItemCheckInfringingWordsMqListener 无账号 不执行");
        }
        String accountNumber = dto.getAccountNumber();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("侵权词校验");
        String maxId = null;

        int limit = 1000;
        int offset = 0;

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setPageFields(new String[]{"id", "itemId", "site", "itemSeller", "name", "description", "infringementWord", "infringementWordInfos",
                "descImgMapping", "descriptionType"});
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setItemSeller(accountNumber);
        request.setSiteList(dto.getSites());
        request.setIsFather(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        while (true) {
            Page<EsShopeeItem> page = shopeeItemEsService.page(request, limit, offset);
            if (page == null || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            if (maxId == null) {
                log.info(accountNumber + "账号 校验侵权词数量" + page.getTotalElements());
            }

            List<EsShopeeItem> esShopeeItems = page.getContent();
            maxId = esShopeeItems.get(esShopeeItems.size() - 1).getId();
            for (EsShopeeItem esShopeeItem : esShopeeItems) {
                Map<String, String> replaceImgMap = new HashMap<>();
                InfringmentResponse infringmentResponse = null;
                try {
                    String description = esShopeeItem.getDescription();

                    // 图片标签要去掉
                    description = ShopeeDescriptionUtil.doReplace(description, replaceImgMap);

                    infringmentResponse = ShopeeCommonUtils.checkInfringmentWord(Arrays.asList(esShopeeItem.getName(), description), esShopeeItem.getSite());
                } catch (Exception e) {
                    log.error(esShopeeItem.getItemId() + "侵权词校验出错" + e.getMessage(), e);
                    continue;
                }

                List<InfringementWordInfo> infringementWordInfos = new ArrayList<>();
                List<String> tortAllList = new ArrayList<>();
                if (MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                    infringmentResponse.getInfringementWordSourceMap().forEach((word, wordSource) -> {
                        InfringementWordInfo infringementWordInfo = new InfringementWordInfo(wordSource, word);
                        infringementWordInfo.setWord(StringUtils.toRootLowerCase(word));// 转小写方便查询
                        infringementWordInfos.add(infringementWordInfo);
                        tortAllList.add(word);
                    });
                }

                if (MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                    infringmentResponse.getBrandWordSourceMap().forEach((word, wordSource) -> {
                        InfringementWordInfo infringementWordInfo = new InfringementWordInfo(wordSource, word);
                        infringementWordInfo.setWord(StringUtils.toRootLowerCase(word));// 转小写方便查询
                        infringementWordInfos.add(infringementWordInfo);
                        tortAllList.add(word);
                    });
                }

                Boolean equals = InfringementUtils.compareInfringementWordInfos(esShopeeItem.getInfringementWordInfos(), infringementWordInfos);
                if (BooleanUtils.isTrue(equals)) {
                    continue; // 一样则不修改
                }

                List<String> updateIds = shopeeItemEsService.getIdsByItemId(esShopeeItem.getItemId());
                shopeeItemEsBulkProcessor.syncUpdateInfringementWord(updateIds, tortAllList, infringementWordInfos);
            }
            offset += limit;
        }
        stopWatch.stop();
        log.info(accountNumber + stopWatch.prettyPrint());
        log.info("账号{} 校验侵权词{}", accountNumber, stopWatch.prettyPrint());
    }
}