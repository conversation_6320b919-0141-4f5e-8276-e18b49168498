package com.estone.erp.publish.shopee.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.shopee.annotation.NeedToLog;
import com.estone.erp.publish.shopee.enums.ShopeeListingTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeListingStatusConfig;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/15 上午11:51
 */
@Data
public class ShopeeListingOnlineConfigDTO {
    /**
     * id
     */
    private Integer id;

    /**
     * 规则名称
     */
    @NeedToLog(description = "规则名称")
    @NotNull(message = "规则名称不能为空")
    private String name;

    /**
     * 配置类型 0 下架 1 上架
     *
     * @see ShopeeListingTypeEnum
     */
    @NeedToLog(description = "配置类型")
    @NotNull(message = "配置类型不能为空")
    private Integer type;

    /**
     * 店铺类型 1 店铺分组 2 店铺
     */
    @NeedToLog(description = "店铺类型")
    @NotNull(message = "店铺类型不能为空")
    private Integer accountType;

    /**
     * 店铺分组名称（逗号拼接）
     */
    @NeedToLog(description = "店铺分组名称")
    @NotNull(message = "店铺分组名称不能为空")
    private String accountGroupName;

    /**
     * 店铺分组id（逗号拼接）
     */
    @NotNull(message = "店铺分组id不能为空")
    private String accountGroupId;

    /**
     * 店铺集（逗号拼接）
     */
    @NeedToLog(description = "店铺")
    @NotNull(message = "店铺不能为空")
    private String accounts;

    /**
     * 最大上架数量
     */
    @NeedToLog(description = "最大上架数量")
    private int maxOfflineNumber;

    /**
     * 执行频率 (day 每天, week 每周, month 每月)
     */
    @NeedToLog(description = "执行频率")
    private String execFrequency;

    /**
     * 执行日期 (对于月和年)
     */
    @NeedToLog(description = "执行日期")
    private String execDate;

    /**
     * 执行时间 (时分)
     */
    @NeedToLog(description = "执行时间")
    private String execTime;

    /**
     * 策略开始时间 (年月日)
     */
    @NeedToLog(description = "策略开始时间")
    private Timestamp strategyStartTime;

    /**
     * 策略结束时间 (年月日)
     */
    @NeedToLog(description = "策略结束时间")
    private Timestamp strategyEndTime;

    /**
     * 状态 0 禁用 1 启用
     */
    @NeedToLog(description = "启用状态")
    private Integer status;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Timestamp updatedTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 配置规则
     */
    private RuleConfigJson ruleConfigJson;

    @Data
    public static class RuleConfigJson {

        /**
         * 类名code
         */
        @NeedToLog(description = "类目")
        private List<String> catCodeList;

        /**
         * 可用库存仓库类型  1：深圳仓，3：南宁仓
         */
        @NeedToLog(description = "仓库类型")
        private List<Integer> availableStockWarehouses;

        /**
         * 可用库存起始值
         */
        @NeedToLog(description = "可用库存起始值")
        private Integer availableStockStart;

        /**
         * 可用库存结束值
         */
        @NeedToLog(description = "可用库存结束值")
        private Integer availableStockEnd;

        /**
         * 指定周期（1/7/14/30/60/90）查询总销量，配合 salesNumStart、salesNumEnd 使用
         */
        @NeedToLog(description = "平台总销量区间周期")
        private Integer salesNumCycle;

        /**
         * 总销量区间 起始值
         */
        @NeedToLog(description = "平台总销量区间起始值")
        private Integer salesNumStart;

        /**
         * 总销量区间 结束值
         */
        @NeedToLog(description = "平台总销量区间结束值")
        private Integer salesNumEnd;

        /**
         * 指定周期（1/7/14/30/60/90）查询shopee总销量
         */
        @NeedToLog(description = "shopee平台总销量区间周期")
        private Integer shopeeSalesNumCycle;

        /**
         * shopee总销量区间 起始值
         */
        @NeedToLog(description = "总销量区间起始值")
        private Integer shopeeSalesNumStart;

        /**
         * shopee总销量区间 结束值
         */
        @NeedToLog(description = "总销量区间结束值")
        private Integer shopeeSalesNumEnd;

        /**
         * 录入时间
         */
        @NeedToLog(description = "录入时间")
        private List<InputTimeInfo> inputTimeList;

        /**
         * 销售成本价 起始值
         */
        @NeedToLog(description = "销售成本价起始值")
        private Double saleCostStart;

        /**
         * 销售成本价 结束值
         */
        @NeedToLog(description = "销售成本价结束值")
        private Double saleCostEnd;

        /**
         * 产品标签列表
         * - normal goods
         */
        private List<String> productCodes;

        /**
         * 产品标签列表
         * - BQ005
         */
        @NeedToLog(description = "产品标签")
        private List<String> productTags;

        /**
         * 单品状态列表
         * -Discard
         */
        @NeedToLog(description = "单品状态")
        private List<String> itemStatus;

        /**
         * 禁售信息，包含多个键值对，每个键映射到一个值列表
         */
        @NeedToLog(description = "禁售信息")
        private List<ProhibitedInfo> prohibitedInfo;

        /**
         * 季节性商品
         */
        @NeedToLog(description = "季节性商品")
        private List<String> seasonProduct;

        /**
         * 是否滞销
         */
        @NeedToLog(description = "是否滞销")
        private List<Integer> unsalableLevels;

        @Data
        public static class ProhibitedInfo {

            /**
             * 禁售平台
             */
            @NeedToLog(description = "禁售平台")
            private String platform;

            /**
             * 禁售站点
             */
            @NeedToLog(description = "禁售站点")
            private List<String> siteList = new ArrayList<>();

            @Override
            public String toString() {
                return String.format("禁售平台=%s, 禁售站点=%s", platform, siteList);
            }
        }

        @Data
        public static class InputTimeInfo {

            /**
             * 录入时间类型 month->月 year->年
             */
            @NeedToLog(description = "录入时间类型")
            private String type;

            /**
             * 录入时间 year->2021 month->0-12
             */
            @NeedToLog(description = "录入时间")
            private List<Integer> inputTime;

            /**
             * 占比率
             */
            @NeedToLog(description = "占比率")
            private Double ratio;

            @Override
            public String toString() {
                return String.format("录入时间类型=%s, 录入时间=%s, 占比率=%s", type, inputTime, ratio);
            }
        }
    }

    public static ShopeeListingOnlineConfigDTO reconvert(ShopeeListingStatusConfig entity) {
        ShopeeListingOnlineConfigDTO configDTO = new ShopeeListingOnlineConfigDTO();
        if (Objects.nonNull(entity)) {
            BeanUtils.copyProperties(entity, configDTO);
            configDTO.setRuleConfigJson(
                    JSON.parseObject(entity.getRuleConfigJson(), new TypeReference<ShopeeListingOnlineConfigDTO.RuleConfigJson>() {
                    })
            );
        }
        return configDTO;
    }

    public static ShopeeListingOnlineConfigDTO.RuleConfigJson reconvertRuleConfig(String ruleConfigJson) {
        return JSON.parseObject(ruleConfigJson, new TypeReference<RuleConfigJson>() {
        });
    }
}
