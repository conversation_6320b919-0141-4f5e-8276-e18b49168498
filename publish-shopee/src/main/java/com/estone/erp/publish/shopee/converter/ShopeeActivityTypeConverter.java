package com.estone.erp.publish.shopee.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.estone.erp.publish.shopee.enums.ShopeeActivityTypeEnum;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/4/17 18:04
 */
public class ShopeeActivityTypeConverter implements Converter<Integer> {
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value != null) {
            ShopeeActivityTypeEnum methodEnum = ShopeeActivityTypeEnum.convert(value);
            if (methodEnum != null) {
                return new WriteCellData<>(methodEnum.getDesc());
            }
        }
        return new WriteCellData<>("");
    }
}