package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/26 9:56
 * @description 修改产品库存
 */
@Getter
@Setter
public class UpdateStockV2 implements RequestCommon{

    @JSONField(name = "item_id")
    private Long itemId;

    /** list长度应在1到50之间 */
    @JSONField(name = "stock_list")
    private List<UpdateStockPriceDetailV2> stockList;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_STOCK;
    }
}
