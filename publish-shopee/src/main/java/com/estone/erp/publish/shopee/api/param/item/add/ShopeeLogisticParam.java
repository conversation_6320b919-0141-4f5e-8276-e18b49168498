package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * shopee物流方式实体类
 *
 * <AUTHOR>
 */
public class ShopeeLogisticParam {

    @JSONField(name = "logistic_id")
    private Integer logisticId;

    @JSONField(name = "enabled")
    private Boolean enabled = true;

    /**
     * 可选 Only needed when logistics fee_type = CUSTOM_PRICE
     */
    @JSONField(name = "shipping_fee")
    private Double shippingFee;

    /**
     * 可选 If specify logistic fee_type is SIZE_SELECTION size_id is required
     */
    @JSONField(name = "size_id")
    private Integer sizeId;

    /**
     * 可选 when seller chooses this option, the shipping fee of this channel on
     * item will be set to 0. Default value is False
     */
    @JSONField(name = "is_free")
    private Boolean isFree;

    @JSONField(name = "site")
    private String site;

    public Integer getLogisticId() {
        return logisticId;
    }

    public void setLogisticId(Integer logisticId) {
        this.logisticId = logisticId;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Double getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(Double shippingFee) {
        this.shippingFee = shippingFee;
    }

    public Integer getSizeId() {
        return sizeId;
    }

    public void setSizeId(Integer sizeId) {
        this.sizeId = sizeId;
    }

    public Boolean getIsFree() {
        return isFree;
    }

    public void setIsFree(Boolean isFree) {
        this.isFree = isFree;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }
}