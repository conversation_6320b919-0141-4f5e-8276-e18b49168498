package com.estone.erp.publish.shopee.api.param.logistics.getlogistics;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * 获取平台物流方式
 */
public class GetLogisticsParam implements IRequestUrlApiKey {

    /** 伙伴id */
    @JSONField(name = "partner_id")
    private Integer partnerId;

    /** 商店id */
    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    private String apiKey;

    public GetLogisticsParam() {
    }

    public GetLogisticsParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.GET_LOGISTICS;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
}
