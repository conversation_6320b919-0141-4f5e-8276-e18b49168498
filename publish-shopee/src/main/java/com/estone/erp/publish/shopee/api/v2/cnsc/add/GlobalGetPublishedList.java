package com.estone.erp.publish.shopee.api.v2.cnsc.add;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/29 15:47
 * @description
 */
@Setter
@Getter
public class GlobalGetPublishedList implements RequestCommon {

    @JSONField(name = "global_item_id")
    private Long globalItemId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_PUBLISHED_LIST;
    }
}
