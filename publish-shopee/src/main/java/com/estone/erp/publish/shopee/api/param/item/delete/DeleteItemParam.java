package com.estone.erp.publish.shopee.api.param.item.delete;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * @Description: 删除Shopee产品
 * @Author: yang<PERSON><PERSON><PERSON>
 * @Date: 2019/3/28 15:30
 * @Version: 1.0.0
 */
public class DeleteItemParam implements IRequestUrlApiKey {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "partner_id")
    private Integer partnerId;

    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(serialize = false)
    private String apiKey;

    public DeleteItemParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.ITEM_DELETE;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
}
