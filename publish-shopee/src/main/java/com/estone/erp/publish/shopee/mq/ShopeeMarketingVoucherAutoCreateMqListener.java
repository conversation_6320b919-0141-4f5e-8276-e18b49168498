package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.shopee.component.marking.FollowPrizeConfigParam;
import com.estone.erp.publish.shopee.component.marking.VoucherConfigParam;
import com.estone.erp.publish.shopee.enums.ShopeeConfigOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.handler.ShopeeFollowPrizeHandler;
import com.estone.erp.publish.shopee.handler.ShopeeVoucherHandler;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeConfigTask;
import com.estone.erp.publish.shopee.model.ShopeeMarketingFollowPrizeTask;
import com.estone.erp.publish.shopee.model.ShopeeMarketingVoucherTask;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingFollowPrizeTaskService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingVoucherTaskService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/12 下午6:08
 */
@Slf4j
public class ShopeeMarketingVoucherAutoCreateMqListener implements ChannelAwareMessageListener {


    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Resource
    private ShopeeVoucherHandler shopeeVoucherHandler;

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        FeedTask feedTask = JSON.parseObject(body, new TypeReference<FeedTask>() {
        });

        Integer id = Integer.valueOf(feedTask.getAttribute5());
        ShopeeConfigTask configTask = shopeeConfigTaskService.selectByPrimaryKey(id);
        if (Objects.isNull(configTask) && !ShopeeConfigOperatorStatusEnum.WAITING.getCode().equals(configTask.getOperatorStatus())) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            return;
        }

        try {
            // 解析配置规则
            VoucherConfigParam voucherConfigParam = JSON.parseObject(configTask.getConfigRuleJson(), VoucherConfigParam.class);

            ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByAccountNumber(configTask.getAccountNumber());
            shopeeVoucherHandler.addVoucher(shopeeAccountConfig, voucherConfigParam, feedTask, id, configTask.getConfigId());

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
