package com.estone.erp.publish.shopee.api.param.item.update;

import com.alibaba.fastjson.annotation.JSONField;

public class Item {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "stock")
    private Integer stock;

    @JSONField(name = "price")
    private Double price;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

}
