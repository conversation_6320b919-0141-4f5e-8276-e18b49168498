package com.estone.erp.publish.shopee.api.constant;

/**
 * <AUTHOR>
 * @date 2021/6/25 10:55
 * @description cnsc 接口
 */
public class ShopeeApiV2CnscConstant extends ShopeeApiV2Constant{

    /** 获取类目 */
    public static final String GET_CATEGORY = "/api/v2/global_product/get_category";
    /** 获取属性 */
    public static final String GET_ATTRIBUTES_TREE = "/api/v2/global_product/get_attribute_tree";
    /** 获取品牌 */
    public static final String GET_BRAND_LIST = "/api/v2/global_product/get_brand_list";

    /** 获取可用刊登的店铺 */
    public static final String GET_PUBLISHABLE_SHOP = "/api/v2/global_product/get_publishable_shop";

    /** 添加全球产品 */
    public static final String ADD_GLOBAL_ITEM = "/api/v2/global_product/add_global_item";
    /** 初始化全球产品变体 */
    public static final String INIT_TIER_VARIATION = "/api/v2/global_product/init_tier_variation";

    /** 创建刊登任务 */
    public static final String CREATE_PUBLISH_TASK = "/api/v2/global_product/create_publish_task";
    /** 获取刊登结果 */
    public static final String GET_PUBLISH_TASK_RESULT = "/api/v2/global_product/get_publish_task_result";
    /** 获取刊登结果集合 */
    public static final String GET_PUBLISHED_LIST = "/api/v2/global_product/get_published_list";

    /** 获取全球产品item */
    public static final String GET_GLOBAL_ITEM_LIST = "/api/v2/global_product/get_global_item_list";
    /** 获取全球产品基础信息 */
    public static final String GET_GLOBAL_ITEM_INFO = "/api/v2/global_product/get_global_item_info";
    /** 获取全球产品变体 */
    public static final String GET_GLOBAL_MODEL_LIST = "/api/v2/global_product/get_global_model_list";
    /** 根据itemId 获取 globalItemId */
    public static final String GET_GLOBAL_ITEM_ID = "/api/v2/global_product/get_global_item_id";

    /** 更新全球产品价格 */
    public static final String UPDATE_PRICE = "/api/v2/global_product/update_price";
    /** 更新全球产品库存 */
    public static final String UPDATE_STOCK = "/api/v2/global_product/update_stock";

    /** 删除全球产品 */
    public static final String DELETE_GLOBAL_ITEM = "/api/v2/global_product/delete_global_item";
    /** 删除全球产品变体 */
    public static final String DELETE_GLOBAL_MODEL = "/api/v2/global_product/delete_global_model";

    /** 更新全球产品 */
    public static final String UPDATE_GLOBAL_ITEM = "/api/v2/global_product/update_global_item";

    /** 获取产品刊登字段长度限制 */
    public static final String GET_GLOBAL_ITEM_LIMIT = "/api/v2/global_product/get_global_item_limit";

    /**
     * 废弃
     * 获取发货天数：使用get_item_limit
     * 当前对接发货天数未使用，所以不作调整<shopee_category_days_to_ship_limit>
     */
    public static final String GET_DAYS_TO_SHOP_LIMIT = "/api/v2/global_product/get_dts_limit";

    /**
     * 废弃
     * 是否支持此码表信息（提示已过期，但是可以调用，推荐使用get_item_limit）
     */
    public static final String SUPPORT_SIZE_CHART = "/api/v2/global_product/support_size_chart";

    /**
     * 获取尺码表列表
     */
    public static final String GET_SIZE_CHART_LIST = "/api/v2/global_product/get_size_chart_list";

    /**
     * 获取尺码表详情
     */
    public static final String GET_SIZE_CHART_DETAIL = "/api/v2/global_product/get_size_chart_detail";

}
