package com.estone.erp.publish.shopee.api.v2.cnsc.add;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/29 15:44
 * @description
 */
@Getter
@Setter
public class GlobalGetPublishTaskResult implements RequestCommon {

    @JSONField(name = "publish_task_id")
    private String publishTaskId ;

    public GlobalGetPublishTaskResult(String publish_task_id){
        this.publishTaskId = publish_task_id;

    }

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_PUBLISH_TASK_RESULT;
    }
}
