package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Data;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.shopee.dto
 * @Author: sj
 * @CreateTime: 2025-05-13  16:05
 * @Description: TODO
 */
@Data
public class ShopeeSaleAccountVO {
    /**
     * 子账号名称
     */
    private String sonAccountName;

    /**
     * 卖家ID
     */
    private String sellerId;

    /**
     * 账号所在站点
     */
    private String accountSite;

    public ShopeeSaleAccountVO toShopeeSaleAccountVO(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse) {
    	this.sonAccountName = saleAccountAndBusinessResponse.getColStr5();
    	this.sellerId = saleAccountAndBusinessResponse.getMerchantId();
    	this.accountSite = saleAccountAndBusinessResponse.getAccountSite();
    	return this;
    }
}
