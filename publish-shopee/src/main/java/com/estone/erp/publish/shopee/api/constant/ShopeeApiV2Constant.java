package com.estone.erp.publish.shopee.api.constant;

/**
 * <AUTHOR>
 * @date 2021/5/11 14:57
 * @description shopee平台v2接口
 */
public class ShopeeApiV2Constant {

    /** 请求域名 */
//    public static final String HOST = "https://partner.shopeemobile.com";

    /** 请求ip 国内的地址 */
    public static final String HOST = "https://openplatform.shopee.cn";

    /** 测试请求域名*/
    public static final String TEST_HOST = "https://partner.uat.shopeemobile.com";

    /** 测试请求域名 */
    public static final String TEST_STABLE_HOST = "https://partner.test-stable.shopeemobile.com";


    /** 获取类目 */
    public static final String GET_CATEGORY = "/api/v2/product/get_category";
    /** 获取属性 */
    public static final String GET_ATTRIBUTES = "/api/v2/product/get_attributes";
    /** 获取品牌*/
    public static final String GET_BRAND_LIST = "/api/v2/product/get_brand_list";

    /** Add a new item */
    public static final String ADD_ITEM = "/api/v2/product/add_item";
    /** init VARIATION */
    public static final String INIT_TIER_VARIATION = "/api/v2/product/init_tier_variation";
    /** update_tier_variation VARIATION */
    public static final String UPDATE_TIER_VARIATION = "/api/v2/product/update_tier_variation";

    /** 上传图片 */
    public static final String UPLOAD_IMAGE = "/api/v2/media_space/upload_image";

    /** 初始化上传视频 */
    public static final String INIT_UPLOAD_VIDEO = "/api/v2/media_space/init_video_upload";

    /** 上传视频 */
    public static final String UPLOAD_VIDEO_PART = "/api/v2/media_space/upload_video_part";

    /** 视频转码 */
    public static final String COMPLETE_VIDEO_UPLOAD = "/api/v2/media_space/complete_video_upload";


    /** 获取视频上传结果 */
    public static final String GET_VIDEO_UPLOAD_RESULT = "/api/v2/media_space/get_video_upload_result";

    /** get listing */
    public static final String GET_ITEM_LIST = "/api/v2/product/get_item_list";
    /** get listing base info */
    public static final String GET_ITEM_BASE_INFO = "/api/v2/product/get_item_base_info";
    /** get listing extra info */
    public static final String GET_ITEM_EXTRA_INFO = "/api/v2/product/get_item_extra_info";
    /** get model list */
    public static final String GET_MODEL_LIST = "/api/v2/product/get_model_list";

    /** 上下架产品 */
    public static final String UNLIST_ITEM = "/api/v2/product/unlist_item";

    /** 使用此api删除产品项目 */
    public static final String DELETE_ITEM = "/api/v2/product/delete_item";

    /** 使用此api删除产品项目变体 */
    public static final String DELETE_MODEL = "/api/v2/product/delete_model";

    /** 更新产品信息 */
    public static final String UPDATE_ITEM = "/api/v2/product/update_item";
    /** 更新产品库存 */
    public static final String UPDATE_STOCK = "/api/v2/product/update_stock";
    /** 更新产品价格 */
    public static final String UPDATE_PRICE = "/api/v2/product/update_price";
    /** 更新产品sip价格 */
    public static final String UPDATE_SIP_ITEM_PRICE = "/api/v2/product/update_sip_item_price";

    /** 按项目名称获取推荐类别 */
    public static final String CATEGORY_RECOMMEND = "/api/v2/product/category_recommend";
    /**
     * 产品相关限制
     */
    public static final String GET_ITEM_LIMIT = "/api/v2/product/get_item_limit";

    /** 获取所有支持的物流渠道 */
    public static final String GET_CHANNEL_LIST = "/api/v2/logistics/get_channel_list";


    /** 添加折扣 **/
    public static final String ADD_DISCOUNT = "/api/v2/discount/add_discount";
    /** 添加折扣产品 **/
    public static final String ADD_DISCOUNT_ITEM = "/api/v2/discount/add_discount_item";
    /** 删除折扣 **/
    public static final String DELETE_DISCOUNT = "/api/v2/discount/delete_discount";
    /** 删除折扣活动中的项目 **/
    public static final String DELETE_DISCOUNT_ITEM = "/api/v2/discount/delete_discount_item";
    /** 获取折扣详情 **/
    public static final String GET_DISCOUNT = "/api/v2/discount/get_discount";
    /** 获取折扣列表 **/
    public static final String GET_DISCOUNT_LIST = "/api/v2/discount/get_discount_list";
    /** 更新折扣 **/
    public static final String UPDATE_DISCOUNT = "/api/v2/discount/update_discount";
    /** 更新折扣产品 **/
    public static final String UPDATE_DISCOUNT_ITEM = "/api/v2/discount/update_discount_item";
    /**  结束折扣 **/
    public static final String END_DISCOUNT = "/api/v2/discount/end_discount";
    /**
     * 获取商品折扣
     **/
    public static final String GET_ITEM_PROMOTION = "/api/v2/product/get_item_promotion";

    /**
     * 优惠套装
     */
    public static final String ADD_BUNDLE_DEAL = "/api/v2/bundle_deal/add_bundle_deal";
    public static final String ADD_BUNDLE_DEAL_ITEM = "/api/v2/bundle_deal/add_bundle_deal_item";
    public static final String DELETE_BUNDLE_DEAL_ITEM = "/api/v2/bundle_deal/delete_bundle_deal_item";
    public static final String END_BUNDLE_DEAL_ITEM = "/api/v2/bundle_deal/end_bundle_deal";
    public static final String DELETE_BUNDLE_DEAL = "/api/v2/bundle_deal/delete_bundle_deal";


    /** 获取商户仓库位置列表 **/
    public static final String GET_MERCHANT_WAREHOUSE_LOCATION_LIST = "/api/v2/merchant/get_merchant_warehouse_location_list";


    /** 获取商户仓库位置列表 **/
    public static final String GET_SHOP_INFO = "/api/v2/merchant/get_shop_info";
    /**
     * 商铺业绩数据指标
     */
    public static final String GET_SHOP_PERFORMANCE = "/api/v2/account_health/get_shop_performance";

    /**
     * 获取关注
     */
    public static final String GET_FOLLOW_PRIZE_LIST = "/api/v2/follow_prize/get_follow_prize_list";
    /**
     * 获取关注
     */
    public static final String END_FOLLOW_PRIZE = "/api/v2/follow_prize/end_follow_prize";
    /**
     * 获取关注详情
     */
    public static final String GET_FOLLOW_PRIZE_DETAIL = "/api/v2/follow_prize/get_follow_prize_detail";

    /**
     * 添加关注礼
     */
    public static final String ADD_FOLLOW_PRIZE = "/api/v2/follow_prize/add_follow_prize";

    /**
     * 获取套装优惠列表
     */
    public static final String GET_BUNDLE_DEAL_LIST = "/api/v2/bundle_deal/get_bundle_deal_list";

    /**
     * 获取套装优惠商品列表
     */
    public static final String GET_BUNDLE_DEAL_ITEM_LIST = "/api/v2/bundle_deal/get_bundle_deal_item";
    public static final String GET_BUNDLE_DEAL_ITEM_DETAIL = "/api/v2/bundle_deal/get_bundle_deal";

    /**
     * 获取优惠券列表
     */
    public static final String GET_VOUCHER_LIST = "/api/v2/voucher/get_voucher_list";
    public static final String GET_VOUCHER = "/api/v2/voucher/get_voucher";
    public static final String ADD_VOUCHER = "/api/v2/voucher/add_voucher";
    public static final String END_VOUCHER = "/api/v2/voucher/end_voucher";

    /**
     * 更新变体
     */
    public static final String UPDATE_MODEL = "/api/v2/product/update_model";

    /**
     * 是否支持此码表信息(废弃)
     */
    public static final String SUPPORT_SIZE_CHART = "/api/v2/product/support_size_chart";

    /**
     * 更新此码表信息
     */
    public static final String UPDATE_SIZE_CHART = "/api/v2/product/update_size_chart";

    /**
     * 获取尺码表列表
     */
    public static final String GET_SIZE_CHART_LIST = "/api/v2/product/get_size_chart_list";

    /**
     * 获取此码表详情
     */
    public static final String GET_SIZE_CHART_DETAIL = "/api/v2/product/get_size_chart_detail";
}
