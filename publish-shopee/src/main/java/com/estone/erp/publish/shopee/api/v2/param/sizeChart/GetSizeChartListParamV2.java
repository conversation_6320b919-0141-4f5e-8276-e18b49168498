package com.estone.erp.publish.shopee.api.v2.param.sizeChart;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * 根据分类获取是否支持尺码图的参数
 */
@Data
public class GetSizeChartListParamV2 implements RequestCommon {

    @JSONField(name = "category_id")
    private Integer categoryId;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @JSONField(name = "cursor")
    private String cursor;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_SIZE_CHART_LIST;
    }
}
