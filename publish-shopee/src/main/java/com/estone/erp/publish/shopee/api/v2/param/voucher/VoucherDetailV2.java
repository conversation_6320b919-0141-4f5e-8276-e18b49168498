package com.estone.erp.publish.shopee.api.v2.param.voucher;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

@Data
public class VoucherDetailV2 implements RequestCommon {

    @JSONField(name = "voucher_id")
    private Long voucherId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_VOUCHER;
    }
}
