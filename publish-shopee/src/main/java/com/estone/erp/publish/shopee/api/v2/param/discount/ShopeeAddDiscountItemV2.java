package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12 12:06
 * @description 添加折扣
 */
@Getter
@Setter
public class ShopeeAddDiscountItemV2 implements RequestCommon {

    /** 折扣ID */
    @JSONField(name = "discount_id")
    private Long discountId;

    /** 折扣活动结束的时间。结束时间必须晚于当前时间 */
    @JSONField(name = "item_list")
    private List<ShopeeAddDiscountItemV2Bo> itemLst;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_DISCOUNT_ITEM;
    }
}
