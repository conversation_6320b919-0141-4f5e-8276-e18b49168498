package com.estone.erp.publish.shopee.component.download;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.shopee.dto.ShopeeNewProductSaleLederTreeNodeDTO;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.DownloadService;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductTemplateCompletionDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductTemplateCompletionDownloadVO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductTemplateCompletionVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductTemplateCompletionMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductTemplateCompletion;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/3/17 17:24
 */
@Slf4j
@Component
public class ShopeeNewProductTemplateCompletionSpuDownload implements DownloadService {

    @Resource
    private ShopeeNewProductTemplateCompletionMapper shopeeNewProductTemplateCompletionMapper;

    @Override
    public String platform() {
        return SaleChannel.CHANNEL_SHOPEE;
    }

    @Override
    public String type() {
        return ShopeeDownloadTypeEnums.NEW_PRODUCT_TEMPLATE_COMPLETION_SPU.getType();
    }

    @Override
    public void download(ExcelDownloadLog downloadLog, File temFile) {
        List<ShopeeNewProductTemplateCompletionDownloadVO> newShopeeNewProductTemplateCompletionDownloadVOList = new ArrayList<>();

        DataContextHolder.setUsername(downloadLog.getCreateBy());
        ShopeeNewProductTemplateCompletionDO search = JSON.parseObject(downloadLog.getQueryCondition(), ShopeeNewProductTemplateCompletionDO.class);

        // 导出数据
        ExcelWriter excelWriter = EasyExcel.write(temFile, ShopeeNewProductTemplateCompletionDownloadVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        // 构建参数
        LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSale()), ShopeeNewProductTemplateCompletion::getSale, search.getSale());
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSaleLeader()), ShopeeNewProductTemplateCompletion::getSaleLeader, search.getSaleLeader());
        lambdaQueryWrapper.eq(ObjectUtils.isNotNull(search.getSaleType()), ShopeeNewProductTemplateCompletion::getSaleType, search.getSaleType());
        lambdaQueryWrapper.ge(ObjectUtils.isNotNull(search.getPushTimeStart()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeStart());
        lambdaQueryWrapper.le(ObjectUtils.isNotNull(search.getPushTimeEnd()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeEnd());

        // 导出数量
        int totalCount = 0;

        // 分页查询
        int pageIndex = 1;
        int pageSize = 500;
        Gson gson = new Gson();
        while (true) {
            IPage<ShopeeNewProductTemplateCompletionVO> pageResult = null;
            Page<ShopeeNewProductTemplateCompletionVO> doPage = new Page<>(pageIndex, pageSize);
            switch (search.getTimeType()) {
                case "day":
                    lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getIdList()), ShopeeNewProductTemplateCompletion::getId, search.getIdList());
                    pageResult = shopeeNewProductTemplateCompletionMapper.selectNewProductTemplateCompletionPage(doPage, lambdaQueryWrapper);
                    break;
                case "week":
                    pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByWeek(doPage, lambdaQueryWrapper);
                    break;
                case "month":
                    pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByMonth(doPage, lambdaQueryWrapper);
                    break;
                default:
                    throw new IllegalArgumentException("timeType is not valid!");
            }
            pageIndex++;
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                break;
            }

            // 构建导出数据
            for (ShopeeNewProductTemplateCompletionVO recordVO : pageResult.getRecords()) {
                String unfinishedSpuInfo = recordVO.getUnfinishedSpuInfo();
                if (search.getTimeType().equals("day")) {
                    List<ShopeeNewProductSaleLederTreeNodeDTO> shopeeNewProductSaleLederTreeNodeByDay = gson.fromJson(
                            unfinishedSpuInfo,
                            new TypeToken<List<ShopeeNewProductSaleLederTreeNodeDTO>>() {
                            }.getType()
                    );

                    // 数据处理
                    List<ShopeeNewProductTemplateCompletionDownloadVO> downloadVOList = shopeeNewProductSaleLederTreeNodeByDay.stream()
                            .flatMap(dto -> {
                                String saleLeader = dto.getSaleLeader();
                                return dto.getSaleSpuMap().entrySet().stream()
                                        .flatMap(entry -> {
                                            String sale = entry.getKey();
                                            Set<String> spuSet = entry.getValue();
                                            return spuSet.stream()
                                                    .map(spu -> new ShopeeNewProductTemplateCompletionDownloadVO(spu, saleLeader, sale));
                                        });
                            })
                            .collect(Collectors.toList());
                    // 如果到500000条，则不在继续导出
                    if (totalCount + downloadVOList.size() > 500000) {
                        break;
                    }
                    totalCount += downloadVOList.size();
                    newShopeeNewProductTemplateCompletionDownloadVOList.addAll(downloadVOList);
                } else {
                    List<List<ShopeeNewProductSaleLederTreeNodeDTO>> shopeeNewProductSaleLederTreeNode = gson.fromJson(
                            unfinishedSpuInfo,
                            new TypeToken<List<List<ShopeeNewProductSaleLederTreeNodeDTO>>>() {
                            }.getType()
                    );
                    for (List<ShopeeNewProductSaleLederTreeNodeDTO> shopeeNewProductSaleLederTreeNodeDTOS : shopeeNewProductSaleLederTreeNode) {
                        List<ShopeeNewProductTemplateCompletionDownloadVO> downloadVOList = shopeeNewProductSaleLederTreeNodeDTOS.stream()
                                .flatMap(dto -> {
                                    String saleLeader = dto.getSaleLeader();
                                    return dto.getSaleSpuMap().entrySet().stream()
                                            .flatMap(entry -> {
                                                String sale = entry.getKey();
                                                Set<String> spuSet = entry.getValue();
                                                return spuSet.stream().map(spu -> new ShopeeNewProductTemplateCompletionDownloadVO(spu, saleLeader, sale.equals("0") ? "" : sale));
                                            });
                                })
                                .collect(Collectors.toList());
                        // 如果到500000条，则不在继续导出
                        if (totalCount + downloadVOList.size() > 500000) {
                            break;
                        }
                        totalCount += downloadVOList.size();
                        newShopeeNewProductTemplateCompletionDownloadVOList.addAll(downloadVOList);
                    }
                }

            }

            excelWriter.write(newShopeeNewProductTemplateCompletionDownloadVOList, writeSheet);
            excelWriter.finish();
            downloadLog.setDownloadCount(newShopeeNewProductTemplateCompletionDownloadVOList.size());
        }
    }
}