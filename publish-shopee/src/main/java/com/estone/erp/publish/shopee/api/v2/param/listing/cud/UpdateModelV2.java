package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.UpdateModelDtoV2;
import lombok.Data;

import java.util.List;

/**
 * 更新变体
 */
@Data
public class UpdateModelV2 implements RequestCommon {

    /**
     * item_id
     */
    @JSONField(name = "item_id")
    private Long itemId;

    /**
     * 变体
     */
    private List<UpdateModelDtoV2> model;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_MODEL;
    }
}
