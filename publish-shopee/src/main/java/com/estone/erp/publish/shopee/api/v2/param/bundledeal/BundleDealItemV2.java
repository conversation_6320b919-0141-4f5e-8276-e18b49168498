package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

@Data
public class BundleDealItemV2 implements RequestCommon {


    @JSONField(name = "bundle_deal_id")
    private Long bundle_deal_id;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_BUNDLE_DEAL_ITEM_LIST;
    }
}
