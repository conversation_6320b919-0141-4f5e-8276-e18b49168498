package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealAddStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingConfigTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfig;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBundleDealProductListingMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 添加优惠套装商品到优惠套装队列监听器
 * <AUTHOR>
 * @Date 2024/9/20 下午3:23
 */
@Slf4j
public class ShopeeAddBundleDealItemMqListener implements ChannelAwareMessageListener {

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Autowired
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;

    @Autowired
    private ShopeeBundleDealProductListingMapper shopeeBundleDealProductListingMapper;

    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;

    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            Map<String, Object> parsedObject = JSON.parseObject(body, new TypeReference<Map<String, Object>>() {
            });
            String account = parsedObject.get("account").toString();
            List<ShopeeMarketingBundleDeal> bundleDealList = JSON.parseObject(parsedObject.get("bundleDealList").toString(), new TypeReference<List<ShopeeMarketingBundleDeal>>() {
            });

            // 校验当前店铺是否有启用的配置
            Timestamp now = new Timestamp(System.currentTimeMillis());
            // 获取当前店铺启用的配置，当前规则是同一个店铺只能有一个启用的配置
            ShopeeMarketingConfigExample configExample = new ShopeeMarketingConfigExample();
            configExample.createCriteria()
                    .andTypeEqualTo(ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL.getCode())
                    .andStatusEqualTo(1)
                    .andAccountsFindInSet(List.of(account))
                    .andStrategyStartTimeLessThanOrEqualTo(now)
                    .andStrategyEndTimeGreaterThanOrIsNull(now);
            List<ShopeeMarketingConfig> shopeeMarketingConfigs = shopeeMarketingConfigService.selectByExample(configExample);
            if (CollectionUtils.isEmpty(shopeeMarketingConfigs)) {
                log.error("当前店铺:{}，没有可执行的配置", account);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            ShopeeMarketingConfig shopeeMarketingConfig = shopeeMarketingConfigs.get(0);

            // 获取当前店铺的套装id，并过滤获取商品数据
            List<Long> bundleDealIds = bundleDealList.stream().map(ShopeeMarketingBundleDeal::getBundleDealId).collect(Collectors.toList());

            // 获取店铺信息
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, true);

            // 获取当前店铺可以添加的商品数据(并过滤物流渠道未开启的商品)
            List<EsShopeeItem> esShopeeItemList = getEsShopeeItemsByAccount(account, bundleDealIds);
            if (CollectionUtils.isEmpty(esShopeeItemList)) {
                log.error("当前店铺:{}，没有可添加的商品数据", account);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            Collections.shuffle(esShopeeItemList);

            // 过滤掉已添加满且非系统创建的套装数据 遍历优惠套装并添加商品(按照活动时间降序)
            List<ShopeeMarketingBundleDeal> newShopeeMarketingBundleDeals = bundleDealList.stream()
                    .filter(bundleDeal -> Objects.nonNull(bundleDeal.getConfigId()) && !bundleDeal.getAddStatus().equals(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_FULL.getCode()))
                    .sorted(Comparator.comparing(ShopeeMarketingBundleDeal::getBundleDealEndTime))
                    .collect(Collectors.toList());
            for (ShopeeMarketingBundleDeal shopeeMarketingBundleDeal : newShopeeMarketingBundleDeals) {
                // 判断是否达到2000个商品上限
                if (shopeeMarketingBundleDeal.getProductNumber() < 2000) {
                    Integer logisticsId = shopeeMarketingBundleDeal.getLogisticsId();
                    if (ObjectUtils.isEmpty(logisticsId)) {
                        continue;
                    }

                    // 用于存储物流ID与批次商品映射
                    Map<Integer, List<EsShopeeItem>> logisticGroups = shopeeMarketingBundleDealService.getLogisticsGroupsByItemsMap(esShopeeItemList, logisticsId);
                    if (CollectionUtils.isEmpty(logisticGroups.get(logisticsId))) {
                        log.error("当前店铺:{}，暂无开启的物流渠道:{}", account, logisticsId);
                        continue;
                    }

                    // 当前物流ID的批次商品
                    List<EsShopeeItem> currentLogisticsGroupList = getExecutingShopeeItems(logisticGroups.get(logisticsId), account);
                    if (CollectionUtils.isEmpty(currentLogisticsGroupList)) {
                        continue;
                    }

                    // 组装商品数据添加当前套装活动中
                    List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = currentLogisticsGroupList.stream()
                            .limit(2000 - shopeeMarketingBundleDeal.getProductNumber())
                            .map(item -> {
                                ShopeeBundleDealProductListing shopeeBundleDealProductListing = new ShopeeBundleDealProductListing();
                                shopeeBundleDealProductListing.setAccountNumber(account);
                                shopeeBundleDealProductListing.setItemId(Long.parseLong(item.getItemId()));
                                shopeeBundleDealProductListing.setBundleDealId(shopeeMarketingBundleDeal.getBundleDealId());
                                shopeeBundleDealProductListing.setCreateTime(new Timestamp(System.currentTimeMillis()));
                                shopeeBundleDealProductListing.setStatus(1);
                                return shopeeBundleDealProductListing;
                            })
                            .collect(Collectors.toList());
                    ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.addBundelDealItem(saleAccount, shopeeBundleDealProductListings, shopeeMarketingBundleDeal);
                    // 调用接口失败
                    if (ObjectUtils.isEmpty(shopeeResponse) || StringUtils.isNotBlank(shopeeResponse.getError())) {
                        ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
                            feedTask.setAttribute1(Objects.isNull(shopeeMarketingConfig) ? null : shopeeMarketingConfig.getName());
                            feedTask.setAccountNumber(account);
                            feedTask.setAssociationId(String.valueOf(shopeeMarketingBundleDeal.getBundleDealId()));
                            feedTask.setTaskType(ShopeeFeedTaskEnum.ADD_BUNDLE_DEAL_ITEM.getValue());
                            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                            feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                            feedTask.setRunTime(new Timestamp(new Date().getTime()));
                            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
                            feedTask.setResultMsg(String.format("店铺:%s，套装名称：%s，添加商品任务执行失败，原因：%s", account, shopeeMarketingBundleDeal.getName(), shopeeResponse.getMessage()));
                            feedTask.setCreatedBy("admin");
                        });
                        continue;
                    }

                    // 部分数据添加失败
                    JSONObject responseJSON = JSON.parseObject(shopeeResponse.getResponse());
                    if (responseJSON.containsKey("failed_list") && CollectionUtils.isNotEmpty(responseJSON.getJSONArray("failed_list"))) {
                        List<JSONObject> failedList = JSON.parseObject(responseJSON.getString("failed_list"), new TypeReference<List<JSONObject>>() {
                        });
                        failedList.forEach(obj -> {
                            // 删除已经存在得item数据
                            esShopeeItemList.removeIf(item -> item.getItemId().equals(obj.getString("item_id")));

                            // 记录失败的商品数据
                            saveFeedTask(account, shopeeMarketingBundleDeal, obj.getString("item_id"), shopeeMarketingConfig, ResultStatusEnum.RESULT_FAIL.getStatusCode(), obj.getString("fail_message"));
                        });
                    }

                    // 添加成功
                    if (responseJSON.containsKey("success_list") && CollectionUtils.isNotEmpty(responseJSON.getJSONArray("success_list"))) {
                        // 解析成功的商品id
                        List<String> successList = JSON.parseObject(responseJSON.getString("success_list"), new TypeReference<List<String>>() {
                        });

                        // 删除已经存在得item数据
                        esShopeeItemList.removeIf(item -> successList.contains(item.getItemId()));

                        // 只添加添加成功的商品数据
                        List<ShopeeBundleDealProductListing> successBundleDealProductListing = shopeeBundleDealProductListings.stream().filter(t -> successList.contains(Long.toString(t.getItemId()))).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(successBundleDealProductListing)) {
                            //批量添加套装商品表数据
                            shopeeBundleDealProductListingMapper.batchInsert(successBundleDealProductListing);

                            //更新shopee_marketing_bundle_deal
                            int totalProductNumber = shopeeMarketingBundleDeal.getProductNumber() + successBundleDealProductListing.size();
                            if (totalProductNumber >= 2000) {
                                shopeeMarketingBundleDeal.setAddStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_FULL.getCode());
                            } else {
                                shopeeMarketingBundleDeal.setAddStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_NOT_FULL.getCode());
                            }
                            shopeeMarketingBundleDeal.setProductNumber(totalProductNumber);
                            shopeeMarketingBundleDeal.setLastSyncProductExecTime(new Timestamp(new Date().getTime()));
                            shopeeMarketingBundleDeal.setUpdatedTime(new Timestamp(new Date().getTime()));
                            shopeeMarketingBundleDealService.updateByPrimaryKeySelective(shopeeMarketingBundleDeal);

                            // 处理报告
                            successBundleDealProductListing.forEach(t -> {
                                saveFeedTask(account, shopeeMarketingBundleDeal, String.valueOf(t.getItemId()), shopeeMarketingConfig, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "添加成功");
                            });

                        }
                    }
                }
            }

            // 判断是否还存在可添加商品数据且所有套装都已提报满
            if (CollectionUtils.isNotEmpty(esShopeeItemList)) {
                // 获取剩余的商品数据物流ID和名称映射
                Map<Integer, String> logisticIdNameMap = shopeeMarketingBundleDealService.getLogisticsIdAndNameMapByItemList(esShopeeItemList);

                // 筛选需要处理的套装（未满状态）
                Set<Integer> existingLogisticIds = newShopeeMarketingBundleDeals.stream()
                        .filter(deal -> !ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_FULL.getCode().equals(deal.getAddStatus()))
                        .map(ShopeeMarketingBundleDeal::getLogisticsId)
                        .collect(Collectors.toSet());

                // 删除已存在的物流ID
                existingLogisticIds.forEach(logisticIdNameMap::remove);

                // 创建新的套装
                logisticIdNameMap.forEach((logisticsId, logisticsName) ->
                        createBundleDeal(logisticsName, saleAccount, shopeeMarketingConfig, logisticsId)
                );

            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("添加商品任务执行失败，原因：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 获取满足条件的item商品数据
     *
     * @param currentLogisticsGroup
     * @param account
     * @return
     */
    private static List<EsShopeeItem> getExecutingShopeeItems(List<EsShopeeItem> currentLogisticsGroup, String account) {
        // ES-10759 设置同一item每天重试上限3次
        List<EsShopeeItem> currentLogisticsGroupList = currentLogisticsGroup.stream()
                .filter(item -> !ShopeeFeedTaskHandleUtil.existFeedTaskCountByItemId(ShopeeFeedTaskEnum.ADD_BUNDLE_DEAL_ITEM.getValue(), account, item.getItemId(), 3))
                .collect(Collectors.toList());
        return currentLogisticsGroupList;
    }

    /**
     * 处理报告
     *
     * @param account
     * @param shopeeMarketingBundleDeal
     * @param itemId
     * @param statusCode
     */
    private static void saveFeedTask(String account, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal, String itemId, ShopeeMarketingConfig shopeeMarketingConfig, int statusCode, String message) {
        ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAttribute1(Objects.isNull(shopeeMarketingConfig) ? null : shopeeMarketingConfig.getName());
            feedTask.setAccountNumber(account);
            feedTask.setArticleNumber(itemId);
            feedTask.setAssociationId(String.valueOf(shopeeMarketingBundleDeal.getBundleDealId()));
            feedTask.setTaskType(ShopeeFeedTaskEnum.ADD_BUNDLE_DEAL_ITEM.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setResultStatus(statusCode);
            feedTask.setRunTime(new Timestamp(new Date().getTime()));
            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
            feedTask.setResultMsg(message);
            feedTask.setCreatedBy("admin");
        });
    }

    /**
     * 创建优惠套装
     *
     * @param logisticName
     * @param saleAccount
     * @param logisticId   物流渠道id
     */
    private void createBundleDeal(String logisticName, SaleAccountAndBusinessResponse saleAccount, ShopeeMarketingConfig shopeeMarketingConfig, Integer logisticId) {
        // 睡眠1秒
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 创建任务
        FeedTask insertFeedTask = getInsertFeedTask(shopeeMarketingConfig, saleAccount.getAccountNumber(), new Timestamp(System.currentTimeMillis()), "admin");
        try {
            // 根据店铺获取策略时间内启用的配置
            Timestamp startTimeStamp = Timestamp.valueOf(LocalDateTime.now().plusHours(2));
            ShopeeMarketingBundleDeal.ShopeeMarketingBundleDealBuilder bundleDealBuilder = ShopeeMarketingBundleDeal.builder();
            BundleDealConfigParam bundleDealConfigParam = JSON.parseObject(shopeeMarketingConfig.getRuleJson(), BundleDealConfigParam.class);
            ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.addBundelDeal(logisticName, saleAccount, bundleDealConfigParam, startTimeStamp, bundleDealBuilder, new Timestamp(System.currentTimeMillis()));
            if (ObjectUtils.isEmpty(shopeeResponse) || StringUtils.isNotBlank(shopeeResponse.getError())) {
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + JSON.toJSONString(shopeeResponse));
                return;
            }

            // 保存优惠套装数据
            String bundleDealId = JSON.parseObject(shopeeResponse.getResponse()).get("bundle_deal_id").toString();
            ShopeeAccountConfig accountConfigByAccount = shopeeAccountConfigService.getAccountConfigByAccount(saleAccount.getAccountNumber());
            if (ObjectUtils.isNotEmpty(accountConfigByAccount)) {
                bundleDealBuilder.site(accountConfigByAccount.getSite());
            }
            ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = bundleDealBuilder
                    .accountNumber(saleAccount.getAccountNumber())
                    .configId(Long.valueOf(shopeeMarketingConfig.getId()))
                    .bundleDealId(Long.valueOf(bundleDealId))
                    .logisticsId(logisticId)
                    .ruleJson(shopeeMarketingConfig.getRuleJson())
                    .status(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode()).productNumber(0)
                    .addStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_PENDING.getCode())
                    .build();
            shopeeMarketingBundleDealService.insert(shopeeMarketingBundleDeal);
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "创建成功，套装名称：" + shopeeMarketingBundleDeal.getName());
        } catch (NumberFormatException e) {
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + e.getMessage());
        }
    }

    /**
     * 从ES获取可添加商品数据（并过滤物流渠道未开启的商品）
     *
     * @param account
     * @param bundleDealIds 未开始、进行中的优惠套装id列表
     * @return
     */
    private List<EsShopeeItem> getEsShopeeItemsByAccount(String account, List<Long> bundleDealIds) {
        // 1、通过店铺查询已经添加的商品
        ShopeeBundleDealProductListingExample dealProductListingExample = new ShopeeBundleDealProductListingExample();
        dealProductListingExample.createCriteria().andAccountNumberEqualTo(account).andBundleDealIdIn(bundleDealIds);
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingService.selectByExample(dealProductListingExample);

        // 2、获取当前店铺下在线列表商品数据，并排除已存在的商品\并过滤物流渠道未开启的商品
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(account);
        Optional.ofNullable(shopeeBundleDealProductListings).ifPresent(list -> {
            // 获取item并将其转换为String
            request.setNotItemIdList(list.stream().map(ShopeeBundleDealProductListing::getItemId).map(String::valueOf).collect(Collectors.toList()));
        });
        request.setQueryFields(new String[]{"itemId", "logistics", "articleNumber"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> itemServiceEsShopeeItems = esShopeeItemService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(itemServiceEsShopeeItems)) {
            return new ArrayList<>();
        }

        // 3、过滤最近七天涨价的item数据（ES-10753）
        Map<String, List<EsShopeeItem>> itemIdMap = itemServiceEsShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
        Iterator<Map.Entry<String, List<EsShopeeItem>>> iterator = itemIdMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<EsShopeeItem>> entry = iterator.next();
            String itemId = entry.getKey();
            List<EsShopeeItem> shopeeItems = entry.getValue();
            try {
                LocalDate endDate = LocalDate.now();
                LocalDate startDate = endDate.minusDays(7);
                List<String> list = shopeeItems.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
                List<String> feedTaskUpdatePricePrice = ShopeeFeedTaskHandleUtil.getFeedTaskUpdatePricePrice(ShopeeFeedTaskEnum.UPDATE_PRICE.getValue(), account, itemId, list, startDate, endDate);
                if (CollectionUtils.isNotEmpty(feedTaskUpdatePricePrice)) {
                    iterator.remove(); // 使用 iterator 安全地移除元素
                }
            } catch (Exception e) {
                log.error("ShopeeAddBundleDealItemMqListener 查询商品涨价信息异常：{}", e.getMessage(), e);
            }
        }

        // 去重保留唯一的item数据
        return itemIdMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(EsShopeeItem::getItemId, item -> item, (existing, replacement) -> replacement))
                .values()
                .stream()
                .filter(item -> {
                    // 获取商品支持的物流 ID 列表
                    if (ObjectUtils.isEmpty(item.getLogistics())) {
                        return false;
                    }

                    JSONArray logisticsArray = JSON.parseArray(item.getLogistics());
                    Set<Integer> logisticIds = logisticsArray.stream()
                            .filter(o -> ((JSONObject) o).getBoolean("enabled"))
                            .map(o -> ((JSONObject) o).getInteger("logistic_id"))
                            .collect(Collectors.toSet());
                    return CollectionUtils.isNotEmpty(logisticIds);
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建FeedTask
     *
     * @param config
     * @param account
     * @param nowTime
     * @param currentUser
     * @return
     */
    private static FeedTask getInsertFeedTask(ShopeeMarketingConfig config, String account, Timestamp nowTime, String currentUser) {
        FeedTask insertFeedTask = ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAttribute1(config.getName());
            feedTask.setAccountNumber(account);
            feedTask.setTaskType(ShopeeFeedTaskEnum.CREATE_BUNDLE_DEAL.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setRunTime(nowTime);
            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
            feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? "admin" : currentUser);
        });
        return insertFeedTask;
    }

}
