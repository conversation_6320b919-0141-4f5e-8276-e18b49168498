package com.estone.erp.publish.shopee.api.v2.cnsc.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:38
 * @description
 */
@Getter
@Setter
public class GlobalModelDto {

    @JSONField(name = "original_price")
    private Double originalPrice;

//    @JSONField(name = "normal_stock")
//    private Integer normalStock;

    @JSONField(name = "seller_stock")
    private List<SellerStock> sellerStocks;

    @JSONField(name = "global_model_sku")
    private String globalModelSku;

    @JSONField(name = "tier_index")
    private List<Integer> tierIndex = new ArrayList<>();

    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;
}
