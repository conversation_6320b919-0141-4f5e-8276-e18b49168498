package com.estone.erp.publish.shopee.api.v2.param.listing;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19 17:43
 * @description
 */
@Getter
@Setter
public class GetItemBaseInfoV2 implements RequestCommon{

    /**
     *  多个逗号拼接
     *  item_id list; limit [0,50]
     * Required
     */
    @JSONField(name = "item_id_list")
    private List<Long> itemIdList;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_ITEM_BASE_INFO;
    }
}
