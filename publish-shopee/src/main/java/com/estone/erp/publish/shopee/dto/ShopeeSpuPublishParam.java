package com.estone.erp.publish.shopee.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2021/4/5 14:29
 * @description
 */
@Getter
@Setter
public class ShopeeSpuPublishParam {

    /** spu多个逗号拼接*/
    private String spu;
    /** 店铺*/
    private String accountNumber;
    /** 上架开始时间*/
    private Date startTime;
    /** 上架时间间隔*/
    private Integer intervalTime;

    /** 根据范本id刊登*/
    private Long byTemplateId;
    /** 根据类目映射id刊登*/
    private Integer byCategoryMappingId;

    /**
     * 单批次刊登数量
     */
    private Integer number;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则json
     */
    private String ruleJson;

    /**
     * 配置来源
     *
     * @see com.estone.erp.publish.shopee.enums.ConfigSourceEnum
     */
    private String configSource;

}
