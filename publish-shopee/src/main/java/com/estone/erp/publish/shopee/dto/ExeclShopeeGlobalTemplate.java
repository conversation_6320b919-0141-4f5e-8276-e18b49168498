package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.platform.enums.ShopeeSkuDataSourceEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.util.Date;

/**
 * @Auther yucm
 * @Date 2022/6/13
 */
@Getter
@Setter
public class ExeclShopeeGlobalTemplate  {

    public ExeclShopeeGlobalTemplate() {}

    // 转换对象 增加字段该方法需要对应增加字段
    public ExeclShopeeGlobalTemplate(EsShopeeGlobalTemplate globalTemplate) {
        this.setId(globalTemplate.getId());
        this.setSku(globalTemplate.getSku());
        this.setName(globalTemplate.getName());
        this.setCategoryId(globalTemplate.getCategoryId());
        this.setDataSource(globalTemplate.getDataSource());
        this.setCreateDate(globalTemplate.getCreateDate());
        this.setCreatedBy(globalTemplate.getCreatedBy());
        this.setLastUpdateDate(globalTemplate.getLastUpdateDate());
        this.setLastUpdatedBy(globalTemplate.getLastUpdatedBy());
    }

    @Id
    @ExcelProperty(value = "范本编号")
    private Long id;

    /**
     * 货号
     */
    @ExcelProperty(value = "SKU")
    private String sku;

    /**
     * 标题
     */
    @ExcelProperty(value = "商品名称")
    private String name;

    @ExcelProperty(value = "分类")
    private String categoryNamePath;

    @ExcelProperty(value = "数据来源")
    private String dataSourceExcel;

    @ExcelProperty(value = "创建人")
    private String createdByExcel;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;


    @ExcelProperty(value = "修改人")
    private String lastUpdatedByExcel;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;


    /**
     * 类目id
     */
    @ExcelIgnore
    private Integer categoryId;

    @ExcelIgnore
    private Integer dataSource;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createdBy;

    /**
     * 修改人
     */
    @ExcelIgnore
    private String lastUpdatedBy;

    public String getDataSourceExcel() {
        return ShopeeSkuDataSourceEnum.buildName(this.getDataSource());
    }
}
