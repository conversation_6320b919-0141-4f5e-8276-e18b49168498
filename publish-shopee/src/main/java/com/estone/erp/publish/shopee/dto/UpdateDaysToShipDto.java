package com.estone.erp.publish.shopee.dto;

import lombok.Data;

@Data
public class UpdateDaysToShipDto {

    /**
     * esId
     */
    private String id;

    private String itemSeller;

    private String sku;

    /**
     * MPSKU
     */
    private String itemSku;

    private Boolean isFather;

    private Boolean isGood;

    private String itemId;

    private String modelId;

    private Integer newDaysToShip;

    private Integer oldDaysToShip;

    /**
     * redis 当时的库存
     */
    private Integer redisStock;

    /**
     * 订单总销量
     */
    private Integer totalOrderNumber;

    /**
     * 站点
     */
    private String site;
}
