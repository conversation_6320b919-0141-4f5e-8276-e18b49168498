package com.estone.erp.publish.shopee.api.v2.param.video;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.File;
import java.io.Serializable;

/**
 * 上传视频
 * <AUTHOR>
 * @date 2023-03-20 15:14
 */
@Getter
@Setter
public class UploadVideoPart implements RequestCommon, Serializable {

    /**
     * The video_upload_id in the response of initiate_video_upload
     */
    @JSONField(name = "video_upload_id")
    private String uploadVideoId;

    /**
     * 分片序号
     */
    @JSONField(name = "part_seq")
    private int partSeq;

    /**
     * 当前文件MD5
     */
    @JSONField(name = "content_md5")
    private String contentMd5;

    /**
     * 分片文件
     */
    @JsonInclude
    private File partContent;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPLOAD_VIDEO_PART;
    }
}
