package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.param.item.item.UnItem;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/25 11:13
 * @description 下架或上架listing
 */
@Getter
@Setter
public class UnlistItemV2 implements RequestCommon {

    @JSONField(name = "item_list")
    private List<UnItem> itemList;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UNLIST_ITEM;
    }
}
