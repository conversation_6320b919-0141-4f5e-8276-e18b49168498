package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MQ声明和绑定
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeItemCheckInfringingWordsMqConfig {

    private int shopeeItemCheckInfringingWordsMqConsumers;
    private int shopeeItemCheckInfringingWordsMqPrefetchCount;
    private boolean shopeeItemCheckInfringingWordsMqListener;


    @Bean
    public Queue shopeeItemCheckInfringingWords() {
        return new Queue(PublishQueues.SHOPEE_ITEM_CHECK_INFRINGING_WORDS_QUEUE);
    }

    @Bean
    public Binding shopeeItemCheckInfringingWordsBinding() {
        return new Binding(PublishQueues.SHOPEE_ITEM_CHECK_INFRINGING_WORDS_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_ITEM_CHECK_INFRINGING_WORDS_KEY, null);
    }

    @Bean
    public ShopeeItemCheckInfringingWordsMqListener shopeeItemCheckInfringingWordsMqListener() {
        return new ShopeeItemCheckInfringingWordsMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeItemCheckInfringingWordsListenerContainer(
            ShopeeItemCheckInfringingWordsMqListener shopeeItemCheckInfringingWordsMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_ITEM_CHECK_INFRINGING_WORDS_QUEUE, shopeeItemCheckInfringingWordsMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
            ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeItemCheckInfringingWordsMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeItemCheckInfringingWordsMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeItemCheckInfringingWordsMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
