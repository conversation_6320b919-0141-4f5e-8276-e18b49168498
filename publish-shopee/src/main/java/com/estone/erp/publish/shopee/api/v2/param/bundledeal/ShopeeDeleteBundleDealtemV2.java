package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/19 9:59
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class ShopeeDeleteBundleDealtemV2 implements RequestCommon {

    private Long bundle_deal_id;
    private List<ItemListDTO> item_list;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.DELETE_BUNDLE_DEAL_ITEM;
    }

    @NoArgsConstructor
    @Data
    public static class ItemListDTO {
        private Long item_id;
    }
}
