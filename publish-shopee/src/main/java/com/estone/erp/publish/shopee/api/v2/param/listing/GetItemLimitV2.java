package com.estone.erp.publish.shopee.api.v2.param.listing;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * @Auther yucm
 * @Date 2022/7/20
 */
@Data
public class GetItemLimitV2 implements RequestCommon {

    @JSONField(name = "category_id")
    private Integer categoryId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_ITEM_LIMIT;
    }
}
