package com.estone.erp.publish.shopee.api.param.item.get;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * 获取列表
 * 
 * <AUTHOR>
 *
 */
public class GetItemsParam implements IRequestUrlApiKey {

    @JSONField(name = "pagination_offset")
    private Integer offset;

    @JSONField(name = "pagination_entries_per_page")
    private Integer page;

    @JSONField(name = "update_time_from")
    private Long updateTimeFrom;

    @JSONField(name = "update_time_to")
    private Long updateTimeTo;

    @JSONField(name = "partner_id")
    private Integer partnerId;

    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(serialize = false)
    private String apiKey;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    public GetItemsParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.GET_ITEMS;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Long getUpdateTimeFrom() {
        return updateTimeFrom;
    }

    public void setUpdateTimeFrom(Long updateTimeFrom) {
        this.updateTimeFrom = updateTimeFrom;
    }

    public Long getUpdateTimeTo() {
        return updateTimeTo;
    }

    public void setUpdateTimeTo(Long updateTimeTo) {
        this.updateTimeTo = updateTimeTo;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

}
