package com.estone.erp.publish.shopee.api.param.item.item;

import com.alibaba.fastjson.annotation.JSONField;

public class UnItem {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "unlist")
    private Boolean unlist;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Boolean getUnlist() {
        return unlist;
    }

    public void setUnlist(Boolean unlist) {
        this.unlist = unlist;
    }

}
