package com.estone.erp.publish.shopee.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @Auther yucm
 * @Date 2022/3/30
 */
@Getter
@Setter
public class ShopeeItemCalcRequest {

    private String id;

    /**
     * 账号
     */
    private String itemSeller;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 毛利率
     */
    private Double profitMargin;

    /**
     * 折扣率(修改试卖链接调价成功后写入)
     */
    private Double discountRate;

    /**
     * 价格
     */
    private Double price;

    /**
     * 是否是sip成本价
     */
    private Boolean postFeeIsZero;
}
