package com.estone.erp.publish.shopee.jobHandler.stock;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.FeedTaskTypeConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskMsgEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.util.CheckSkuUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import jodd.util.ThreadUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

//把异常的sku的库存改成0 全量停产存档库存改0
//@JobHandler("ShopeeIllegalSkuModifyStockHandler")
@Slf4j
@Component
public class ShopeeIllegalSkuModifyStockHandler extends AbstractJobHandler {

    @Getter
    @Setter
    public static class InnerParam {
        //店鋪账号
        private List<String> accountNumberList;
        //sku集合
        private List<String> skuList;
    }

    @Autowired
    private ShopeeGlobalItemService shopeeGlobalItemService;
    @Autowired
    private ShopeeItemEsService shopeeItemEsService;
    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    public ShopeeIllegalSkuModifyStockHandler() {
        super("ShopeeIllegalSkuModifyStockHandler");
    }

    @Override
    @XxlJob("ShopeeIllegalSkuModifyStockHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                log.error("参数解析错误！");
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        List<String> skuList = innerParam.getSkuList();
        //停产 存档
        List<String> statusList = new ArrayList<>(2);
        statusList.add(SingleItemEnum.STOP.getEnName());
        statusList.add(SingleItemEnum.ARCHIVED.getEnName());

        ShopeeAccountConfigExample ex = new ShopeeAccountConfigExample();
        ShopeeAccountConfigExample.Criteria criteria = ex.createCriteria().andSiteIsNotNull();
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            criteria.andAccountIn(innerParam.getAccountNumberList());
        }
        List<ShopeeAccountConfig> allAccountList = shopeeAccountConfigService.selectByExample(ex);
        for (ShopeeAccountConfig accountConfig : allAccountList) {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountConfig.getAccount(), true);
            if ("0".equals(account.getAccountStatus())) {
                XxlJobLogger.log("店铺状态为禁用:{}, 不执行", account.getAccountStatus());
                continue;
            }
            boolean isNnShop = BooleanUtils.isTrue(account.getShopeeColBool3());
            try {
                EsShopeeItemRequest request = new EsShopeeItemRequest();
                request.setItemSeller(accountConfig.getAccount());
                request.setIsGoods(true);
                request.setFromStock(1);
                request.setQueryFields(new String[]{"id", "stock", "itemSeller", "skuStatus", "articleNumber", "skuDataSource"});
                request.setSkuStatusList(statusList);
                if (CollectionUtils.isEmpty(skuList)) {
                    request.setArticleNumberList(skuList);
                }
                List<EsShopeeItem> shopeeItemList = shopeeItemEsService.getEsShopeeItems(request);
                if (CollectionUtils.isEmpty(shopeeItemList)) {
                    continue;
                }

                // 校验es和redis单品状态是否一致
                shopeeItemList.removeIf(item -> !CheckSkuUtils.checkSkuStatus(item.getArticleNumber(), item.getSkuDataSource(), SaleChannel.CHANNEL_SHOPEE));
                if (CollectionUtils.isEmpty(shopeeItemList)) {
                    continue;
                }

                //库存改0
                shopeeItemList.forEach(o -> {
                    String articleNumber = o.getArticleNumber();
                    if (isNnShop) {
                        Integer nnAvableStock = SkuStockUtils.getNnAvableStock(articleNumber);
                        if (nnAvableStock == null) {
                            XxlJobLogger.log("南宁店铺：[{}], sku:[{}], 南宁仓库为空，按照0处理", accountConfig.getAccount(), articleNumber);
                        }
                        o.setStock(Optional.ofNullable(nnAvableStock).orElse(0));
                    } else {
                        o.setStock(0);
                    }
                });

                for (EsShopeeItem shopeeItem : shopeeItemList) {
                    shopeeGlobalItemService.updateShopeeStock(account, StrConstant.ADMIN, shopeeItem, FeedTaskTypeConstant.JOB, true,
                            ShopeeFeedTaskMsgEnum.STOP_ARCHIVED_TO_ZERO_ALL.getCode());
                }
                XxlJobLogger.log("账号{}, 修改库存数量{}条", accountConfig.getAccount(), shopeeItemList.size());

                if (shopeeItemList.size() > 100) {
                    ThreadUtil.sleep(6 * 1000L);
                }
            } catch (Exception e) {
                XxlJobLogger.log("账号{}, 出错 {}", accountConfig.getAccount(), e.getMessage());
                log.error(String.format("账号%s 出错", accountConfig.getAccount()), e);
            }
        }

        return ReturnT.SUCCESS;
    }
}
