package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther yucm
 * @Date 2022/5/18
 */
@Setter
@Getter
public class ExcelEsShopeeItem extends EsShopeeItem {

    public ExcelEsShopeeItem(){};

    /**
     * 导出使用 增加导出字段需要改此方法
     * @param esShopeeItem
     */
    public ExcelEsShopeeItem(EsShopeeItem esShopeeItem){
        this.setItemSeller(esShopeeItem.getItemSeller());
        this.setItemId(esShopeeItem.getItemId());
        this.setVariationId(esShopeeItem.getVariationId());
        this.setItemSku(esShopeeItem.getItemSku());
        this.setSpu(esShopeeItem.getSpu());
        this.setArticleNumber(esShopeeItem.getArticleNumber());
        this.setSkuStatus(esShopeeItem.getSkuStatus());
        this.setName(esShopeeItem.getName());
        this.setProCategoryCnName(StrUtil.strDeldComma(esShopeeItem.getProCategoryCnName()));
        this.setForbidChannel(StrUtil.strDeldComma(esShopeeItem.getForbidChannel()));
        this.setInfringementTypeNames(esShopeeItem.getInfringementTypeNames());
        this.setInfringementObjs(esShopeeItem.getInfringementObjs());
        this.setProhibitionSites(esShopeeItem.getProhibitionSites());
        this.setTagNames(StrUtil.strDeldComma(esShopeeItem.getTagNames()));
        this.setSpecialGoodsName(StrUtil.strDeldComma(esShopeeItem.getSpecialGoodsName()));
        this.setStock(esShopeeItem.getStock());
        this.setPrice(esShopeeItem.getPrice());
        this.setOriginalPrice(esShopeeItem.getOriginalPrice());
        this.setDiscountId(esShopeeItem.getDiscountId());
        this.setSipItemPrice(esShopeeItem.getSipItemPrice());
        this.setWeight(esShopeeItem.getWeight());
        this.setItemStatus(esShopeeItem.getItemStatus());
        this.setOrder24HCount(esShopeeItem.getOrder24HCount());
        this.setOrderLast7dCount(esShopeeItem.getOrderLast7dCount());
        this.setOrderLast14dCount(esShopeeItem.getOrderLast14dCount());
        this.setOrderLast30dCount(esShopeeItem.getOrderLast30dCount());
        this.setOrderLast60dCount(esShopeeItem.getOrderLast60dCount());
        this.setOrderLast90dCount(esShopeeItem.getOrderLast90dCount());
        this.setOrderNumTotal(esShopeeItem.getOrderNumTotal());
        this.setSales(esShopeeItem.getSales());
        this.setViews(esShopeeItem.getViews());
        this.setDaysToShip(esShopeeItem.getDaysToShip());
        this.setDataSource(esShopeeItem.getDataSource());
        this.setUploadDate(esShopeeItem.getUploadDate());
        this.setDownDate(esShopeeItem.getDownDate());
        this.setSyncDate(esShopeeItem.getSyncDate());
        this.setView_7d_count(esShopeeItem.getView_7d_count());
        this.setView_14d_count(esShopeeItem.getView_14d_count());
        this.setView_30d_count(esShopeeItem.getView_30d_count());
        this.setView_60d_count(esShopeeItem.getView_60d_count());
        this.setView_90d_count(esShopeeItem.getView_90d_count());
        this.setViewSyncUpdateDate(esShopeeItem.getViewSyncUpdateDate());

        this.setSearch_7d_count(esShopeeItem.getSearch_7d_count());
        this.setSearch_14d_count(esShopeeItem.getSearch_14d_count());
        this.setSearch_30d_count(esShopeeItem.getSearch_30d_count());
        this.setSearch_60d_count(esShopeeItem.getSearch_60d_count());
        this.setSearch_90d_count(esShopeeItem.getSearch_90d_count());

        this.setNnStock(esShopeeItem.getNnStock());
        this.setSzStock(esShopeeItem.getSzStock());
        this.setSkuDataSource(esShopeeItem.getSkuDataSource());

        this.setDiscountRate(esShopeeItem.getDiscountRate());

        this.setDownType(esShopeeItem.getDownType());
    }

    /**
     * 单品状态
     */
    @ExcelProperty(value = "单品状态")
    private String skuStatusExcel;

    @ExcelProperty(value = "折扣")
    private String isDiscountExcel;

    @ExcelProperty(value = "shopee类目")
    private String categoryName;

    public String getSkuStatusExcel() {
        return SkuStatusEnum.buildName(this.getSkuStatus());
    }

    public String getIsDiscountExcel() {
        Boolean modelDiscount = false;
        if (null != this.getDiscountId() && this.getDiscountId() > 0) {
            modelDiscount = true;
        }
        return modelDiscount ? "是" : "否";
    }

    // 销售
    @ExcelProperty(value = "销售")
    private String salemanager;

    // 销售组长
    @ExcelProperty(value = "销售组长")
    private String salemanagerLeader;

    // 销售主管
    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;
}
