package com.estone.erp.publish.shopee.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description: 新年活动库存处理DTO
 * <AUTHOR>
 * @Date 2025/1/3 下午4:13
 */
@Data
public class ShopeeNewYearHandlingStockDTO {

    /**
     * 账号类型 0-非南宁仓 1-南宁仓
     */
    private Integer accountType;

    /**
     * 店铺集合
     */
    private List<String> accountList;

    /**
     * sku集合
     */
    private List<HolidayUpdateRecord> skuList;

    /**
     * 调整库存
     */
    private Integer adjustStock;

    @Data
    public static class HolidayUpdateRecord {

        /**
         * sku
         */
        private String sku;

        /**
         * 单品状态
         */
        private Integer itemStatus;

        /**
         * 可卖天数
         */
        private Integer availableStockDays;

        /**
         * 可用库存
         */
        private Integer availableStock;

        /**
         * 待发库存
         */
        private Integer pendingCount;

    }

}
