package com.estone.erp.publish.shopee.api.v2.param.follow.prize;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * 关注礼
 */
@Data
public class AddFollowPrizeV2 implements RequestCommon {

    /**
     * 关注礼名称 必传
     */
    @JSONField(name = "follow_prize_name")
    private String followPrizeName;

    /**
     * 开始时间 时间戳(时间段不可以重叠) 必传
     * @return
     */
    @JSONField(name = "start_time")
    private Long startTime;

    /**
     * 结束时间 时间戳(时间段不可以重叠) 必传
     */
    @JSONField(name = "end_time")
    private Long endTime;

    /**
     * 可用库存(1-200000) 必传
     */
    @JSONField(name = "usage_quantity")
    private Integer usageQuantity;

    /**
     * 最低消费 必传
     */
    @JSONField(name = "min_spend")
    private Double minSpend;

    /**
     * @see com.estone.erp.publish.shopee.enums.ShopeeMarketingRewardTypeEnum 必传
     */
    @JSONField(name = "reward_type")
    private Integer rewardType;

    /**
     * 奖金(rewardType为1必传)
     */
    @JSONField(name = "discount_amount")
    private Double discountAmount;

    /**
     * 百分比(reward_type ==2或reward_type==3时必传)
     */
    @JSONField(name = "percentage")
    private Integer percentage;

    /**
     * 最大优惠金额百分比(reward_type ==2必传)
     */
    @JSONField(name = "max_price")
    private Double maxPrice;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_FOLLOW_PRIZE;
    }
}
