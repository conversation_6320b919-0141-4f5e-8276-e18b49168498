package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.cud.UpdateItemV2;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 一次性恢复更新的天数
 */
@Component
@Slf4j
public class ShopeeOnceRecoverUpdateDaysToShipJobHandler extends AbstractJobHandler {

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    @Resource
    private EsShopeeItemService esShopeeItemService;

    public ShopeeOnceRecoverUpdateDaysToShipJobHandler() {
        super("shopeeOnceRecoverUpdateDaysToShipJobHandler");
    }


    @XxlJob("shopeeOnceRecoverUpdateDaysToShipJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        Long gtId = 0L;
        int pageSize = 100;
        while (true) {
            List<FeedTask> list = feedTaskService.selectShopeeUpdateDaysToShipData(gtId, pageSize);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            gtId = list.get(list.size() - 1).getId();

            try {
                List<EsShopeeItem> collect = list.stream().map(a -> {
                    EsShopeeItem item = new EsShopeeItem();
                    item.setId(a.getAssociationId());
                    item.setSpu(a.getArticleNumber());
                    item.setItemSeller(a.getAccountNumber());
                    item.setItemId(a.getAttribute2());

                    String attribute5 = a.getAttribute5();
                    if (StringUtils.isNotBlank(attribute5)) {
                        item.setOldDaysToShip(Integer.valueOf(attribute5));
                    }
                    String attribute4 = a.getAttribute4();
                    if (StringUtils.isNotBlank(attribute4)) {
                        item.setDaysToShip(Integer.valueOf(attribute4));
                    }
                    return item;
                }).collect(Collectors.toList());

                EsShopeeItemRequest request = new EsShopeeItemRequest();
                List<String> itemIdList = collect.stream().map(EsShopeeItem::getItemId).collect(Collectors.toList());
                request.setIsFather(true);
                request.setItemIdList(itemIdList);
                request.setQueryFields(new String[]{"id", "itemId", "itemSeller", "articleNumber", "daysToShip", "spu"});
                List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(request);
                Map<String, Integer> itemIdAndDaysToShipMap =
                        esShopeeItems.stream().collect(Collectors.toMap(EsShopeeItem::getItemId, EsShopeeItem::getDaysToShip, (old, newV) -> old));
                Iterator<EsShopeeItem> iterator = collect.iterator();
                while (iterator.hasNext()) {
                    EsShopeeItem next = iterator.next();
                    String itemId = next.getItemId();
                    Integer daysToShip = next.getDaysToShip();
                    Integer currenDays = itemIdAndDaysToShipMap.get(itemId);
                    if (currenDays != null && daysToShip.equals(currenDays)) {
                        iterator.remove();
                    }
                }
                if (CollectionUtils.isEmpty(collect)) {
                    continue;
                }
                StopWatch stopWatch1 = new StopWatch();
                stopWatch1.start();
                CompletableFuture.allOf(collect.stream().map(item ->
                        CompletableFuture.runAsync(() -> updateDaysToShip(item), ShopeeExecutors.UPDATE_STORE_DAYS_TO_SHIP)).toArray(CompletableFuture[]::new)
                ).join();
                stopWatch1.stop();
                log.info("shopeeOnceRecoverUpdateDaysToShipJobHandler: 更新{}个数据，用时{} ms", collect.size(), stopWatch1.getLastTaskTimeMillis());
                XxlJobLogger.log("shopeeOnceRecoverUpdateDaysToShipJobHandler: 更新{}个数据，用时{} ms", collect.size(), stopWatch1.getLastTaskTimeMillis());
            } catch (Exception e) {
                XxlJobLogger.log("更新异常：gt:{}, pageSize: - {} : error:{}", gtId, pageSize, e);
                log.error("更新异常：gt:{}, pageSize: - {} : error:{}", gtId, pageSize, e);
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 更新发货天数
     *
     * @param item
     */
    private void updateDaysToShip(EsShopeeItem item) {
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((task) -> {
            task.setAssociationId(item.getId() + "");
            task.setAccountNumber(item.getItemSeller());
            task.setArticleNumber(item.getSpu());
            task.setCreatedBy("系统恢复发货天数");
            task.setAttribute2(item.getItemId());
            task.setAttribute3("修改发货天数");
            task.setAttribute4(item.getOldDaysToShip() + "");
            task.setAttribute5(item.getDaysToShip() + "");
            task.setTaskType(ShopeeFeedTaskEnum.UPDATE_DAYS_TO_SHIP.getValue());
        });
        try {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, item.getItemSeller(), true);
            if (account != null) {
                UpdateItemV2 v2 = new UpdateItemV2();
                v2.setItemId(Long.valueOf(item.getItemId()));
                PreOrderDtoV2 preOrder = new PreOrderDtoV2();
                preOrder.setDaysToShip(item.getDaysToShip());
                preOrder.setIsPreOrder(ShopeeDaysToShipConstant.isPreOrder(item.getDaysToShip()));
                v2.setPreOrder(preOrder);

                ShopeeResponse doResponse = ShopeeHttpUtils.doPostV2(account, v2);
                if (StringUtils.isNotBlank(doResponse.getError())) {
                    feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    //默认失败
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setResultMsg(JSON.toJSONString(doResponse));
                    ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                    log.error("accountNumber:{}, itemId:{}, 更新发货天数失败的结果: {}", item.getItemSeller(), item.getItemId(), doResponse.getError());
                    XxlJobLogger.log("accountNumber:{}, itemId:{}, 更新发货天数失败的结果: {}", item.getItemSeller(), item.getItemId(), doResponse.getError());
                } else {
                    feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setResultMsg("修改前发货天数：" + item.getOldDaysToShip() + "，修改后发货天数：" + item.getDaysToShip());
                    ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                    // 修改本地数据
                    List<EsShopeeItem> localEsShopeeItems = shopeeItemEsService.getEsShopeeItemsByItemId(item.getItemId(), null);
                    for (EsShopeeItem esShopeeItem : localEsShopeeItems) {
                        esShopeeItem.setDaysToShip(item.getDaysToShip());
                        esShopeeItem.setLastUpdatedBy("admin");
                        esShopeeItem.setLastUpdateDate(new Date());
                    }
                    shopeeItemEsService.saveAll(localEsShopeeItems);
                }
            }
        } catch (Exception e) {
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            //默认失败
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setResultMsg(e.getMessage());
            ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
            log.error("accountNumber:{}, itemId:{}, 更新发货天数发送异常: {}", item.getItemSeller(), item.getItemId(), e.getMessage());
            XxlJobLogger.log("accountNumber:{}, itemId:{}, 更新发货天数发送异常: {}", item.getItemSeller(), item.getItemId(), e.getMessage());
        }
    }
}
