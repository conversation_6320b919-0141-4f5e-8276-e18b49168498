package com.estone.erp.publish.shopee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/23 下午3:06
 */
@Data
public class ShopeeTaskExecutionTotalDTO {

    /**
     * 操作类型
     *
     * @see com.estone.erp.publish.shopee.enums.ShopeeTaskExecutionDetailsOperationTypeEnum
     */
    private Integer operationType;

    /**
     * 规则名称(0:全部，1：有规则，2：无规则)
     *
     * @see com.estone.erp.publish.shopee.enums.ShopeeTaskExecutionDetailTotalityRuleNameTypeEnum
     */
    private Integer ruleNameType;

    /**
     * 销售类型（0：全部，1：销售，2：组长，3：主管）
     *
     * @see com.estone.erp.publish.shopee.enums.ShopeeTaskExecutionDetailTotalitySaleTypeEnum
     */
    private Integer saleType;

    /**
     * 昨天的店铺数
     */
    private Long yesterdayStores;

    /**
     * 最近7天的店铺数
     */
    private Long lastSevenStores;

    /**
     * 昨天的处理总数量
     */
    private Long yesterdayTotalNum;

    /**
     * 最近7天的处理总数量
     */
    private Long lastSevenTotalNum;

    /**
     * 昨天的成功数量
     */
    private Long yesterdaySuccessNum;

    /**
     * 最近7天的成功数量
     */
    private Long lastSevenSuccessNum;

    /**
     * 昨天的失败数量
     */
    private Long yesterdayFailNum;

    /**
     * 最近7天的失败数量
     */
    private Long lastSevenFailNum;

    /**
     * 昨日成功率
     */
    private Long yesterdaySuccessRate;

    /**
     * 最近7天成功率
     */
    private Long lastSevenSuccessRate;

    /**
     * 关联销售
     */
    private String sale;

    /**
     * 销售组长
     */
    private String saleLeader;

    /**
     * 销售主管
     */
    private String saleSupervisor;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdAt;

}
