package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/25 15:04
 * @description 删除折扣活动中的项目
 */
@Getter
@Setter
public class ShopeeDeleteDiscountItemV2 implements RequestCommon {

    @JSONField(name = "discount_id")
    private Long discountId;

    @JSONField(name = "item_id")
    private Long itemId;

    /** 非必填 */
    @JSONField(name = "model_id")
    private Long modelId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.DELETE_DISCOUNT_ITEM;
    }
}
