package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "yml-config")
public class ShopeeGlobalItemUploadVideoMqConfig {

    private int shopeeGlobalItemUploadVideoMqConsumers;
    private int shopeeGlobalItemUploadVideoMqPrefetchCount;
    private boolean shopeeGlobalItemUploadVideoMqListener;

    @Bean
    public Queue shopeeGlobalItemUploadVideo() {
        return new Queue(PublishQueues.SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_QUEUE);
    }

    @Bean
    public Binding shopeeGlobalItemUploadVideoBinding() {
        return new Binding(PublishQueues.SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_ROUTE_KEY, null);
    }
    @Bean
    public ShopeeGlobalItemUploadVideoMqListener shopeeGlobalItemUploadVideoMqListener() {
        return new ShopeeGlobalItemUploadVideoMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeGlobalItemUploadVideoListenerContainer(
            ShopeeGlobalItemUploadVideoMqListener shopeeGlobalItemUploadVideoMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        simpleMessageListenerContainer(container, PublishQueues.SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_QUEUE, shopeeGlobalItemUploadVideoMqListener);
        return container;
    }

    private void simpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (shopeeGlobalItemUploadVideoMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeGlobalItemUploadVideoMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeGlobalItemUploadVideoMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
