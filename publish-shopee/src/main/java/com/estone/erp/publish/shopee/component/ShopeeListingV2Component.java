package com.estone.erp.publish.shopee.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.bo.ShopeeItemWithBLOBs;
import com.estone.erp.publish.shopee.call.v2.ShopeeGetItemBaseInfoCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeGetItemExtraInfoCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeGetModelListCallV2;
import com.estone.erp.publish.shopee.call.v2.cnsc.GetGlobalModelListCall;
import com.estone.erp.publish.shopee.call.v2.cnsc.ShopeeGetGlobalItemIdCall;
import com.estone.erp.publish.shopee.call.v2.cnsc.ShopeeGetGlobalItemInfoCall;
import com.estone.erp.publish.shopee.enums.ShopeeListingDataSourceEnum;
import com.estone.erp.publish.shopee.model.ShopeeGlobalItem;
import com.estone.erp.publish.shopee.result.ShopeeCustomResult;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.util.CNSCPublishUtil;
import com.estone.erp.publish.shopee.util.ShopeeSkuUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/21 17:39
 * @description
 */
@Slf4j
@Component
public class ShopeeListingV2Component {
    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    public ApiResult<ShopeeCustomResult<String, EsShopeeItem>> getItemList(SaleAccountAndBusinessResponse account, String itemId, boolean isPullAllDetail) {
        return getItemList(account, CommonUtils.arrayAsList(itemId), isPullAllDetail);
    }

    /**
     * 根据itemId 获取listing
     * @param account
     * @param itemIdList 最多50个itemId
     * @param isPullAllDetail 是否拉取所有详情
     * @return
     */
    public ApiResult<ShopeeCustomResult<String, EsShopeeItem>> getItemList(SaleAccountAndBusinessResponse account, List<String> itemIdList, boolean isPullAllDetail) {
        //先获取基础信息
        ApiResult<List<EsShopeeItem>> apiResult = ShopeeGetItemBaseInfoCallV2.getItemBaseInfo(account, itemIdList);
        if(!apiResult.isSuccess()){
            return ApiResult.newError(apiResult.getErrorMsg());
        }

        List<EsShopeeItem> allEsShopeeItems = new ArrayList<>();
        ShopeeCustomResult<String, EsShopeeItem> cusResult = new ShopeeCustomResult();
        List<EsShopeeItem> itemBaseList = apiResult.getResult();
        if(CollectionUtils.isEmpty(itemBaseList)){
            return ApiResult.newSuccess(cusResult);
        }

        if(isPullAllDetail){
            //再获取拓展信息
            ApiResult<ShopeeCustomResult<String, EsShopeeItem>> extraResult = ShopeeGetItemExtraInfoCallV2.getItemExtraInfo(account, itemIdList);
            if(!extraResult.isSuccess()){
                log.error(String.format("账号 %s，获取拓展信息出错：%s", account.getAccountNumber(), extraResult.getErrorMsg()));
                cusResult.getFailList().add(String.format("账号 %s，获取拓展信息出错：%s", account.getAccountNumber(), extraResult.getErrorMsg()));
            }else{
                ShopeeCustomResult<String, EsShopeeItem> result = extraResult.getResult();
                Map<String, EsShopeeItem> itemMap = result.getSuccessMap();
                if(itemMap != null && itemMap.size() > 0){
                    itemBaseList.parallelStream().forEach(item ->{
                        EsShopeeItem extraItem = itemMap.get(item.getItemId());
                        if(extraItem != null){
                            item.setSales(extraItem.getSales());
                            item.setViews(extraItem.getViews());
                            item.setLikes(extraItem.getLikes());
                            item.setRatingStar(extraItem.getRatingStar());
                            item.setCmtCount(extraItem.getCmtCount());
                        }
                    });
                }
            }
        }

        //获取变体
        if(itemBaseList.size() > 0){
            List<String> failIds = new ArrayList<>();
            for (EsShopeeItem itemBase : itemBaseList) {
                String itemId = itemBase.getItemId();
                ApiResult<List<EsShopeeItem>> modelResult = ShopeeGetModelListCallV2.getItemModelList(account, Long.valueOf(itemId));
                if(modelResult.isSuccess()){
                    List<EsShopeeItem> list = modelResult.getResult();
                    list = transModelBean(itemBase, list);

                    allEsShopeeItems.add(itemBase);
                    if(CollectionUtils.isNotEmpty(list)) {
                        allEsShopeeItems.addAll(list);
                    }
                }else{
                    failIds.add(itemId);
                }
            }

            if(failIds.size() > 0){
                cusResult.getFailList().add(String.format("%s 获取变体失败", failIds));
            }
        }

        cusResult.setSuccessList(allEsShopeeItems);
        getGlobalItemId(account, allEsShopeeItems);

        return ApiResult.newSuccess(cusResult);
    }



    /**
     * 根据itemId 获取listing， 异步查询变体信息
     * @param account
     * @param itemIdList
     * @return
     */
    public ApiResult<ShopeeCustomResult<String, EsShopeeItem>> getItemListAsync(SaleAccountAndBusinessResponse account, List<String> itemIdList) {
        //先获取基础信息
        ApiResult<List<EsShopeeItem>> apiResult = ShopeeGetItemBaseInfoCallV2.getItemBaseInfo(account, itemIdList);
        if(!apiResult.isSuccess()){
            return ApiResult.newError(apiResult.getErrorMsg());
        }

        List<EsShopeeItem> allEsShopeeItems = new ArrayList<>();
        ShopeeCustomResult<String, EsShopeeItem> cusResult = new ShopeeCustomResult();
        cusResult.setFailList(Collections.synchronizedList(new ArrayList<>()));
        List<EsShopeeItem> itemBaseList = apiResult.getResult();

        //再获取拓展信息
        ApiResult<ShopeeCustomResult<String, EsShopeeItem>> extraResult = ShopeeGetItemExtraInfoCallV2.getItemExtraInfo(account, itemIdList);
        if(!extraResult.isSuccess()){
            log.error(String.format("账号 %s，获取拓展信息出错：%s", account.getAccountNumber(), extraResult.getErrorMsg()));
            cusResult.getFailList().add(String.format("账号 %s，获取拓展信息出错：%s", account.getAccountNumber(), extraResult.getErrorMsg()));
        }else{
            ShopeeCustomResult<String, EsShopeeItem> result = extraResult.getResult();
            Map<String, EsShopeeItem> itemMap = result.getSuccessMap();
            if(itemMap != null && itemMap.size() > 0){
                itemBaseList.parallelStream().forEach(item ->{
                    EsShopeeItem extraItem = itemMap.get(item.getItemId());
                    if(extraItem != null){
                        item.setSales(extraItem.getSales());
                        item.setViews(extraItem.getViews());
                        item.setLikes(extraItem.getLikes());
                        item.setRatingStar(extraItem.getRatingStar());
                        item.setCmtCount(extraItem.getCmtCount());
                    }
                });
            }
        }



        //过滤需要查询变体的item
//        List<String> itemIds = itemBaseList.stream()
//                .filter(o -> BooleanUtils.isTrue(o.getHasVariation()))
//                .map(o -> o.getItemId())
//                .collect(Collectors.toList());

        //获取变体
        if(itemBaseList.size() > 0){
            List<String> failIds = Collections.synchronizedList(new ArrayList<>());

            List<List<EsShopeeItem>> pagingList = PagingUtils.pagingList(itemBaseList, 3);
            CountDownLatch countDownLatch = new CountDownLatch(pagingList.size());
            for (List<EsShopeeItem> itemBases : pagingList) {
                //异步执行
                ShopeeExecutors.executeSyncProductDetail(() -> {
                    try {

                        for (EsShopeeItem itemBase : itemBases) {
                            String itemId = itemBase.getItemId();
                            ApiResult<List<EsShopeeItem>> modelResult = ShopeeGetModelListCallV2.getItemModelList(account, Long.valueOf(itemId));
                            if(modelResult.isSuccess()){
                                List<EsShopeeItem> list = modelResult.getResult();
                                list = transModelBean(itemBase, list);
                                allEsShopeeItems.add(itemBase);
                                if(CollectionUtils.isNotEmpty(list)) {
                                    allEsShopeeItems.addAll(list);
                                }
                            }else{
                                failIds.add(itemId);
                            }
                        }
                    }catch (Exception e){
                        log.error("同步变体信息出错", e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }

            try {
                boolean awaitResult = countDownLatch.await(20, TimeUnit.MINUTES);
                if (!awaitResult) {
                    log.error("同步变体信息超时");
                }
            } catch (InterruptedException e) {
                log.error("同步变体信息等待被中断", e);
                Thread.currentThread().interrupt();
            }


            if(failIds.size() > 0){
                cusResult.getFailList().add(String.format("%s 获取变体失败", failIds));
            }
        }

        cusResult.setSuccessList(allEsShopeeItems);

        //根据itemId 获取 globalItemId
//        getGlobalItemId(account, itemIdList, allEsShopeeItems);
        // 查询本地全球id 全球变体id减少接口查询
        getLocalEsShoppItemInfo(allEsShopeeItems);
        getGlobalItemId(account, allEsShopeeItems);

        return ApiResult.newSuccess(cusResult);
    }

    private List<EsShopeeItem> transModelBean(EsShopeeItem parentItem, List<EsShopeeItem> sonEsShopeeItems) {
        if(CollectionUtils.isEmpty(sonEsShopeeItems)) {
            return sonEsShopeeItems;
        }

        Boolean hasDiscount = false;
//        parentItem.setId(parentItem.getItemId());
        List<EsShopeeItem> models = new ArrayList<>(sonEsShopeeItems.size());
        for (EsShopeeItem son : sonEsShopeeItems) {
            EsShopeeItem model = new EsShopeeItem();
            BeanUtils.copyProperties(parentItem, model);
            // 变体如果有发货天数，就直接用变体的发货天数
            Integer daysToShip = son.getDaysToShip();
            if (daysToShip != null) {
                model.setDaysToShip(daysToShip);
            }
            models.add(model);

            model.setDiscountId(son.getDiscountId());
            // 变体有折扣时，给父item的discountId赋值
            if ((son.getDiscountId() == null ? 0 : son.getDiscountId()) > 0) {
                parentItem.setDiscountId(son.getDiscountId());
                hasDiscount = true;
            }

            model.setItemSku(son.getItemSku());
            model.setArticleNumber(son.getItemSku().toUpperCase());
            //默认普通listing
            model.setDataSource(ShopeeListingDataSourceEnum.ORDINARY.name());
            if(model.getArticleNumber().contains("_")){
                model.setArticleNumber(ShopeeSkuUtil.spliMpsku(model.getArticleNumber()));
            }
            // 去除前后特殊字符
            model.setArticleNumber(StrUtil.strDeldSpecialChar(model.getArticleNumber()));
            //解析spu
            model.setSpu(ShopeeSkuUtil.spliSpu(model.getArticleNumber()));
            model.setVariationId(son.getVariationId());
            model.setCurrency(son.getCurrency());
            model.setOriginalPrice(son.getOriginalPrice());
            model.setPrice(son.getPrice());
            model.setStock(son.getStock());
            model.setSipItemPrice(son.getSipItemPrice());
            //子sku标识
            model.setIsFather(false);
            // 子sku一定是商品
            model.setIsGoods(true);
            // 子sku赋值上 自己的属性和图片
            model.setVariationOptions(son.getVariationOptions());

            String variationId = model.getVariationId();
            String id = model.getItemId() + (StringUtils.isNotBlank(variationId) ? ("_" + variationId) : "");
            model.setId(id);
        }

        // 根据变体 是否有折扣判断 整个商品是否在打折（主体中有折扣id 不一定是在打折 可能是 套装捆绑的编号 ）
        if(BooleanUtils.isTrue(hasDiscount)) {
            parentItem.setItemHasDiscount(true);
        } else {
            parentItem.setItemHasDiscount(false);
            parentItem.setDiscountId(0L); // 变体不存在折扣id 时候主体也设置为0
        }
        for (EsShopeeItem model : models) {
            model.setItemHasDiscount(parentItem.getItemHasDiscount());
        }

        return models;
    }

    private ShopeeItemWithBLOBs transModelBean(ShopeeItemWithBLOBs parentItem, ShopeeItemWithBLOBs son) {
        ShopeeItemWithBLOBs model = new ShopeeItemWithBLOBs();
        BeanUtils.copyProperties(parentItem, model);
        model.setDiscountId(son.getDiscountId());

        // 变体有折扣时，给父item的discountId赋值
        if ((son.getDiscountId() == null ? 0 : son.getDiscountId()) > 0) {
            parentItem.setDiscountId(son.getDiscountId());
        }

        model.setVariationSku(son.getVariationSku());
        model.setItemSku(son.getVariationSku());
        model.setArticleNumber(son.getVariationSku());
        //默认普通listing
        model.setDataSource(ShopeeListingDataSourceEnum.ORDINARY.name());
        if(model.getArticleNumber().contains("_")){
            model.setArticleNumber(ShopeeSkuUtil.spliMpsku(model.getArticleNumber()));
            model.setItemSku(model.getArticleNumber());
            //包含下滑线就为mpsku
//            model.setDataSource(ShopeeListingDataSourceEnum.MPSKU.name());
        }
        //解析spu
        model.setSpu(ShopeeSkuUtil.spliSpu(model.getArticleNumber()));
        model.setVariationId(son.getVariationId());
        model.setCurrency(son.getCurrency());
        model.setOriginalPrice(son.getOriginalPrice());
        model.setPrice(son.getPrice());
        model.setStock(son.getStock());
        model.setSipItemPrice(son.getSipItemPrice());
        //子sku标识
        model.setIsFather(false);
        //model.setHasVariation(false);

        //变体置空长字段
        model.setName("");
        model.setWholesales("[]");
        model.setLogistics("[]");
        model.setAttributes("[]");
        model.setImages("[]");
        model.setDescription("");
        model.setBrand("{}");
        model.setViews(null);
        model.setLikes(null);
        model.setRatingStar(null);
        model.setCmtCount(null);
        return model;
    }

    private void getLocalEsShoppItemInfo(List<EsShopeeItem> itemList){
        if(CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<String> ids = itemList.stream().map(EsShopeeItem::getId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)) {
            return;
        }
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setQueryFields(new String[]{"id", "globalItemId", "globalModelId", "dataSource"});
        request.setIdList(ids);
        request.setItemSeller(itemList.get(0).getItemSeller());
        List<EsShopeeItem> localEsShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        Map<String, EsShopeeItem> localMap = localEsShopeeItems.stream().collect(Collectors.toMap(EsShopeeItem::getId, o->o));
        if(MapUtils.isEmpty(localMap)) {
            return;
        }

        for (EsShopeeItem esShopeeItem : itemList) {
            String id = esShopeeItem.getId();
            EsShopeeItem localEsShopeeItem = localMap.get(id);
            if (null == localEsShopeeItem || Objects.equals(localEsShopeeItem.getGlobalItemId(), "0")) {
                continue;
            }
            esShopeeItem.setDataSource(localEsShopeeItem.getDataSource());
            esShopeeItem.setGlobalItemId(localEsShopeeItem.getGlobalItemId());
            esShopeeItem.setGlobalModelId(localEsShopeeItem.getGlobalModelId());
        }
    }

    private void getGlobalItemId(SaleAccountAndBusinessResponse account, List<EsShopeeItem> itemList) {
        SaleAccountAndBusinessResponse cnscAccount = null;
        try {
            cnscAccount = CNSCPublishUtil.transCNSCAccount(account);
        }catch (Exception e){
            log.error(String.format("account %s get error", account.getAccountNumber()),e);
        }
        // 非全球卖家找不到全球账号
        if(cnscAccount == null){
            return;
        }

        // 查询本地全球产品id减少接口调用
        getLocalEsShoppItemInfo(itemList);

        // 获取全球id为空的产品 itemId不为空的itemId集合
        List<String> needGlobalItemIds = itemList.stream()
                .filter(o->(StringUtils.isBlank(o.getGlobalItemId()) && StringUtils.isNotBlank(o.getItemId())))
                .map(EsShopeeItem::getItemId)
                .distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(needGlobalItemIds)) {
            Map<String, String> allItemIdMap = new HashMap<>(needGlobalItemIds.size());

            List<List<String>> itemIdListPage = PagingUtils.pagingList(needGlobalItemIds, 20);
            SaleAccountAndBusinessResponse finalCnscAccount = cnscAccount;
            itemIdListPage.forEach(itemIdPage -> {
                ShopeeResponse globalItemIdResult = ShopeeGetGlobalItemIdCall.getGlobalItemId(itemIdPage, account.getMarketplaceId(), finalCnscAccount);
                if(StringUtils.isBlank(globalItemIdResult.getError())){
                    try {
                        String response = globalItemIdResult.getResponse();
                        JSONObject itemJson = JSON.parseObject(response);

                        if(itemJson != null && itemJson.containsKey("item_id_map")){
                            List<Map<String, String>> list = itemJson.getObject("item_id_map", new TypeReference<List<Map<String, String>>>() {
                            });

                            //key: iemId, value: globalItemId
                            Map<String, String> item_id_map = list.stream().collect(Collectors.toMap(o -> o.get("item_id"), o -> o.get("global_item_id"), (o1, o2) -> o1));
                            allItemIdMap.putAll(item_id_map);
                        }
                    }catch (Exception e){
                        log.error(String.format("账号%s 获取globalItemId 出错", account.getAccountNumber()), e);
                    }
                }else {
                    log.error("获取全球产品id error" + globalItemIdResult.getMessage());
                }
            });

            itemList.forEach(item->{
                String itemId = item.getItemId();
                if(StringUtils.isBlank(itemId)) {
                    return;
                }
                String globalItemId = allItemIdMap.get(itemId);
                if(StringUtils.isNotBlank(globalItemId)) {
                    item.setDataSource(ShopeeListingDataSourceEnum.MPSKU.name());
                    item.setGlobalItemId(globalItemId);
                }
            });
        }

        // 获取子产品 存在全球产品id 无全球产品变体id的 全球产品id集合
        List<String> needGlobalModelIds = itemList.stream()
                .filter(o->(BooleanUtils.isFalse(o.getIsFather()) && StringUtils.isNotBlank(o.getGlobalItemId()) && StringUtils.isBlank(o.getGlobalModelId())))
                .map(EsShopeeItem::getGlobalItemId)
                .distinct().collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(needGlobalModelIds)){
            //请求变体信息
            List<ShopeeGlobalItem> globalModelList = new ArrayList<>();
            for (String globalItemIdStr : needGlobalModelIds) {
                try{
                    Long globalItemId = Long.valueOf(globalItemIdStr);
                    ApiResult<List<ShopeeGlobalItem>> result = GetGlobalModelListCall.GetGlobalModelList(cnscAccount, globalItemId);
                    if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getResult())){
                        globalModelList.addAll(result.getResult());
                    }else {
                        log.error(globalItemIdStr + "获取全球产品变体id error" + result.getErrorMsg());
                    }
                }catch (Exception e) {
                    log.error(globalItemIdStr + "获取全球产品变体id error" + e.getMessage());
                }
            }

            if(globalModelList.size() > 0){
                Map<String, Map<String, String>> globalMap =
                        globalModelList.stream().collect(Collectors.groupingBy(o -> o.getGlobalItemId().toString(), Collectors.toMap(o -> o.getVariationSku(), o -> o.getVariationId(), (o1, o2) -> o1)));
                itemList.parallelStream().forEach(item -> {
                    if(globalMap.containsKey(item.getGlobalItemId())){
                        Map<String, String> modelMap = globalMap.get(item.getGlobalItemId());
                        if(modelMap.containsKey(item.getItemSku())){
                            item.setGlobalModelId(modelMap.get(item.getItemSku()));
                            //mpsku
                            item.setDataSource(ShopeeListingDataSourceEnum.MPSKU.name());
                        }
                    }
                });
            }
        }

        // 同步尺码信息
        List<String> globalItemIdList = itemList.stream().map(EsShopeeItem::getGlobalItemId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ShopeeGlobalItem> globalItemInfo = ShopeeGetGlobalItemInfoCall.getGlobalItemInfo(globalItemIdList, cnscAccount);
        if (CollectionUtils.isNotEmpty(globalItemInfo)) {
            // globalItemInfo按照全球商品id分组
            Map<Long, List<ShopeeGlobalItem>> globalItemMap = globalItemInfo.stream().collect(Collectors.groupingBy(ShopeeGlobalItem::getGlobalItemId));
            for (EsShopeeItem esShopeeItem : itemList) {
                String globalItemId = esShopeeItem.getGlobalItemId();
                if (StringUtils.isBlank(globalItemId)) {
                    continue;
                }
                List<ShopeeGlobalItem> shopeeGlobalItems = globalItemMap.get(Long.valueOf(globalItemId));
                if (CollectionUtils.isEmpty(shopeeGlobalItems)) {
                    continue;
                }
                ShopeeGlobalItem shopeeGlobalItem = shopeeGlobalItems.get(0);
                Optional.ofNullable(shopeeGlobalItem.getSizeChart()).ifPresent(esShopeeItem::setSizeChart);
                Optional.ofNullable(shopeeGlobalItem.getSizeChartId()).ifPresent(esShopeeItem::setSizeChartId);
            }
        }
    }
}
