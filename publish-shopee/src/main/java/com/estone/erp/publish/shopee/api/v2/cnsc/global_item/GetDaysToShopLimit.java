package com.estone.erp.publish.shopee.api.v2.cnsc.global_item;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * 获取发货天数限制
 * 跟分类有关系
 */
@Data
public class GetDaysToShopLimit implements RequestCommon {

    public GetDaysToShopLimit(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * 分类id
     */
    @JSONField(name = "category_id")
    private Integer categoryId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_DAYS_TO_SHOP_LIMIT;
    }
}
