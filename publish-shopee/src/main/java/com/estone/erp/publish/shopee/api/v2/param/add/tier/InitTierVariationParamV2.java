package com.estone.erp.publish.shopee.api.v2.param.add.tier;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeMultiVariationParam;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeTierVariationParam;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.GlobalModelDto;
import com.estone.erp.publish.shopee.api.v2.cnsc.dto.SellerStock;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.shopee.util.AliOSSUtils;
import com.estone.erp.publish.shopee.util.ShopeeGlobalTemplateEsUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/5/14 10:12
 * @description
 */
@Getter
@Setter
public class InitTierVariationParamV2 implements RequestCommon{

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "tier_variation")
    private List<TierVariationDtoV2> tierVariation = new ArrayList<>(2);

    /** Model info list, length should be between 1 to 50 */
    @JSONField(name = "model")
    private List<ModelDtoV2> model = new ArrayList<>();


    public InitTierVariationParamV2(SaleAccountAndBusinessResponse account, ShopeeTemplateNew template, Long itemId, Map<String, String> imgMappingMap) {
        this.itemId = itemId;
        List<ShopeeSku> shopeeSkuList = ShopeeGlobalTemplateEsUtils.parseShopeeSkus(template.getShopeeSkusStr());
        Set<String> colorSet = new LinkedHashSet<>();
        Set<String> sizeSet = new LinkedHashSet<>();
        //维度和对应的图片url
        Map<String,String> tierImageMap = new HashMap<>();
        //设置颜色和尺寸的维度,台湾要使用中文颜色和中文尺寸
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            if(StringUtils.isNotEmpty(shopeeSku.getColor())){
                String color = shopeeSku.getColor().trim();
                colorSet.add(color);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
                    tierImageMap.put(color,shopeeSku.getImage());
                }
            }
            if(StringUtils.isNotEmpty(shopeeSku.getSize())){
                String size = shopeeSku.getSize().trim();
                sizeSet.add(size);
                if (StringUtils.isNotEmpty(shopeeSku.getImage())) {
                    tierImageMap.put(size,shopeeSku.getImage());
                }
            }
        }


        //变体图片只能对一个维度的属性生效，比如只能设置color的维度或者size的维度
        boolean isImageParamHasValue = false;

        //color维度设置
        List<String> colorOptionList = null;
        if (CollectionUtils.isNotEmpty(colorSet)) {
            isImageParamHasValue = true;
            colorOptionList = new ArrayList<>(colorSet);

            TierVariationDtoV2 colorParam = new TierVariationDtoV2();
            colorParam.setName("color");

            for (String color : colorSet) {
                OptionDtoV2 option = new OptionDtoV2();
                option.setOption(color);

                if(tierImageMap.get(color) != null){
                    String imageId = imgMappingMap.get(tierImageMap.get(color));
                    Map<String, String> image = new HashMap<>();
                    image.put("image_id", imageId);
                    option.setImage(image);
                }

                colorParam.getOptionList().add(option);
            }

            this.tierVariation.add(colorParam);
        }

        //size维度设置
        List<String> sizeOptionList = null;
        if (CollectionUtils.isNotEmpty(sizeSet)) {
            sizeOptionList = new ArrayList<>(sizeSet);

            TierVariationDtoV2 sizeParam = new TierVariationDtoV2();
            sizeParam.setName("size");

            for (String site : sizeSet) {
                OptionDtoV2 option = new OptionDtoV2();
                option.setOption(site);

                if (tierImageMap.get(site) != null) {
                    String imageId = imgMappingMap.get(tierImageMap.get(site));
                    Map<String, String> image = new HashMap<>();
                    image.put("image_id", imageId);
                    option.setImage(image);
                }

                sizeParam.getOptionList().add(option);
            }

            this.tierVariation.add(sizeParam);
        }

        //设置产品的规格
        for (ShopeeSku shopeeSku : shopeeSkuList) {
            ModelDtoV2 modelTier = new ModelDtoV2();
            PreOrderDtoV2 preOrder = new PreOrderDtoV2();
            preOrder.setDaysToShip(template.getDaysToShip() == null ? ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP : template.getDaysToShip());
            preOrder.setIsPreOrder(ShopeeDaysToShipConstant.isPreOrder(preOrder.getDaysToShip()));
            modelTier.setPreOrder(preOrder);
            //子sku不加后缀
//            modelTier.setGlobalModelSku(shopeeSku.getSku() + CNSCPublishUtil.getRandomChar());
            modelTier.setModelSku(shopeeSku.getSku());
//            modelTier.setNormalStock(shopeeSku.getQuantity());
            modelTier.setSellerStocks(Arrays.asList(new SellerStock(shopeeSku.getQuantity(), null)));
            modelTier.setOriginalPrice(shopeeSku.getPrice());

            //设置属性维度
            int colorIndex = -1;
            if (CollectionUtils.isNotEmpty(colorOptionList)) {
                String color = shopeeSku.getColor();
                color = color.trim();
                colorIndex = 0;
                for (int i = 0; i < colorOptionList.size(); i++) {
                    if(colorOptionList.get(i).equalsIgnoreCase(color)){
                        colorIndex = i;
                        break;
                    }
                }
            }
            int sizeIndex = -1;
            if (CollectionUtils.isNotEmpty(sizeOptionList)) {
                String size = shopeeSku.getSize();
                size = size.trim();
                sizeIndex = 0;
                for (int i = 0; i < sizeOptionList.size(); i++) {
                    if(sizeOptionList.get(i).equalsIgnoreCase(size)){
                        sizeIndex = i;
                        break;
                    }
                }
            }

            //设置属性维度索引，color在前，size在后
            List<Integer> indexList = new ArrayList<>();
            if (colorIndex != -1) {
                indexList.add(colorIndex);
            }
            if (sizeIndex != -1) {
                indexList.add(sizeIndex);
            }

            modelTier.setTierIndex(indexList);
            this.model.add(modelTier);
        }


    }

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.INIT_TIER_VARIATION;
    }
}
