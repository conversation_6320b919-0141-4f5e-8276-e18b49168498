package com.estone.erp.publish.tidb.publishtidb.controller;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingLogTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfigLog;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfigLogCriteria;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigLogService;
import com.estone.erp.publish.shopee.util.ShopeeConfigLogUtil;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductPublishLeaderConfig;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductPublishLeaderConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 组长刊登次数配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/shopeeNewProductPublishLeaderConfig")
public class ShopeeNewProductPublishLeaderConfigController {

    @Resource
    private ShopeeMarketingConfigLogService shopeeMarketingConfigLogService;

    @Autowired
    private ShopeeNewProductPublishLeaderConfigService shopeeNewProductPublishLeaderConfigService;

    /**
     * 获取组长刊登次数配置列表信息
     */
    @RequestMapping("list")
    public ApiResult<List<ShopeeNewProductPublishLeaderConfig>> list() {
        // 同步平台组长信息
        shopeeNewProductPublishLeaderConfigService.syncLeaderChanges();

        // 获取所有配置信息
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminResult.isSuccess()) {
            throw new RuntimeException(superAdminResult.getErrorMsg());
        }
        LambdaQueryWrapper<ShopeeNewProductPublishLeaderConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (!superAdminResult.getResult()) {
            configLambdaQueryWrapper.eq(ShopeeNewProductPublishLeaderConfig::getSaleLeader, WebUtils.getUserName());
        }
        return ApiResult.newSuccess(shopeeNewProductPublishLeaderConfigService.list(configLambdaQueryWrapper));
    }

    /**
     * 更新配置信息
     */
    @RequestMapping("update")
    public ApiResult<String> update(@RequestBody List<ShopeeNewProductPublishLeaderConfig> leaderConfigList) {
        // 保存日志
        List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs = new ArrayList<>();
        Map<Long, List<ShopeeNewProductPublishLeaderConfig>> configMap = shopeeNewProductPublishLeaderConfigService.list().stream().collect(Collectors.groupingBy(ShopeeNewProductPublishLeaderConfig::getId));
        leaderConfigList.forEach(leaderConfig -> {
            Long id = leaderConfig.getId();
            // 记录日志
            List<ShopeeMarketingConfigLog> shopeeMarketingConfigLogs1 = ShopeeConfigLogUtil.generateLog(leaderConfig, configMap.get(id).get(0), id.intValue(), leaderConfig.getSaleLeader(), ShopeeMarketingLogTypeEnum.SALE_LEADER_PUBLISH_CONFIG);
            shopeeMarketingConfigLogs.addAll(shopeeMarketingConfigLogs1);
        });
        if (CollectionUtils.isNotEmpty(shopeeMarketingConfigLogs)) {
            shopeeMarketingConfigLogService.batchInsert(shopeeMarketingConfigLogs);
        }

        // 更新配置
        shopeeNewProductPublishLeaderConfigService.updateBatchById(leaderConfigList);
        return ApiResult.newSuccess("更新成功");
    }

    /**
     * 查看日志
     */
    @RequestMapping("getConfig/logs/{offset}/{limit}")
    public ApiResult<?> getConfigLogs(@PathVariable(value = "offset", required = false) Integer offset, @PathVariable(value = "limit", required = false) Integer limit) {
        ShopeeMarketingConfigLogCriteria shopeeMarketingConfigLogCriteria = new ShopeeMarketingConfigLogCriteria();
        shopeeMarketingConfigLogCriteria.setType(ShopeeMarketingLogTypeEnum.SALE_LEADER_PUBLISH_CONFIG.getCode());
        CQuery<ShopeeMarketingConfigLogCriteria> cquery = new CQuery<>();
        cquery.setLimit(limit);
        cquery.setPageReqired(true);
        cquery.setOffset(offset);
        cquery.setSearch(shopeeMarketingConfigLogCriteria);
        cquery.setOrder("desc");
        cquery.setSort("id");
        return shopeeMarketingConfigLogService.search(cquery);
    }

}