package com.estone.erp.publish.shopee.api.v2.param.video;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-20 18:01
 */
@Getter
@Setter
public class CompleteVideoUpload implements RequestCommon, Serializable {

    @JSONField(name = "video_upload_id")
    private String videoUploadId;
    @JSONField(name = "part_seq_list")
    private List<Integer> partSeqList;
    @JSONField(name = "report_data")
    private ReportData reportData;


    @Getter
    @Setter
    static class ReportData  {
        @JSONField(name = "upload_cost")
        private Integer uploadCost;
    }


    public void initReportData(int uploadCost) {
        ReportData data = new ReportData();
        data.setUploadCost(uploadCost);
        this.setReportData(data);
    }

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.COMPLETE_VIDEO_UPLOAD;
    }
}
