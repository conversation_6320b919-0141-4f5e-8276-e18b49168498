package com.estone.erp.publish.shopee.component.download;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.shopee.enums.ShopeeViolationUpdateStatusEnum;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.DownloadService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeViolationQueryDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeViolationVO;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeViolationsRecord;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeViolationsRecordService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 违规记录导出
 * <AUTHOR>
 * @Date 2024-09-09
 */
@Component
public class ShopeeViolationsRecordDownload implements DownloadService {

    @Resource
    private IShopeeViolationsRecordService shopeeViolationsRecordService;

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Override
    public String platform() {
        return SaleChannel.CHANNEL_SHOPEE;
    }

    @Override
    public String type() {
        return ShopeeDownloadTypeEnums.VIOLATIONS_RECORD.getType();
    }

    @Override
    public void download(ExcelDownloadLog downloadLog, File temFile) {
        List<ShopeeViolationVO> newShopeeViolationsRecordList = new ArrayList<>();

        DataContextHolder.setUsername(downloadLog.getCreateBy());
        ShopeeViolationQueryDO search = JSON.parseObject(downloadLog.getQueryCondition(), ShopeeViolationQueryDO.class);

        // 导出数据
        ExcelWriter excelWriter = EasyExcel.write(temFile, ShopeeViolationVO.class)
                .includeColumnFiledNames(search.getDownloadFieldList())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        // 构建参数
        LambdaQueryWrapper<ShopeeViolationsRecord> lambdaQueryWrapper = new LambdaQueryWrapper<ShopeeViolationsRecord>();
        if (CollectionUtils.isNotEmpty(search.getIdList())) {
            lambdaQueryWrapper.in(ShopeeViolationsRecord::getId, search.getIdList());
        } else {
            lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getAccountNumberList()), ShopeeViolationsRecord::getAccountNumber, search.getAccountNumberList())
                    .eq(ObjectUtils.isNotEmpty(search.getItemType()), ShopeeViolationsRecord::getItemType, search.getItemType())
                    .like(StringUtils.isNotEmpty(search.getItemName()), ShopeeViolationsRecord::getItemName, search.getItemName())
                    .in(CollectionUtils.isNotEmpty(search.getItemIds()), ShopeeViolationsRecord::getItemId, search.getItemIds())
                    .eq(ObjectUtils.isNotEmpty(search.getViolationType()), ShopeeViolationsRecord::getViolationType, search.getViolationType())
                    .like(StringUtils.isNotEmpty(search.getViolationReason()), ShopeeViolationsRecord::getViolationReason, search.getViolationReason())
                    .eq(ObjectUtils.isNotEmpty(search.getDeleteStatus()), ShopeeViolationsRecord::getDeleteStatus, search.getDeleteStatus())
//                    .and(wrapper -> wrapper
//                            .eq(ShopeeViolationsRecord::getUpdateStatus, ShopeeViolationUpdateStatusEnum.DELETE_STATUS_0.getCode())
//                            .or()
//                            .isNull(ShopeeViolationsRecord::getUpdateStatus)
//                    )
                    .between(search.getCrawlTimeStart() != null && search.getCrawlTimeEnd() != null, ShopeeViolationsRecord::getCrawlTime, search.getCrawlTimeStart(), search.getCrawlTimeEnd())
                    .between(search.getDeleteTimeStart() != null && search.getDeleteTimeEnd() != null, ShopeeViolationsRecord::getDeleteTime, search.getDeleteTimeStart(), search.getDeleteTimeEnd())
                    .orderByDesc(ShopeeViolationsRecord::getDeleteTime);
        }

        // 分页查询
        int pageIndex = 1;
        int pageSize = 500;
        while (true) {
            // 分页查询
            IPage<ShopeeViolationsRecord> page = new Page<>(pageIndex, pageSize);
            IPage<ShopeeViolationsRecord> pageResult = shopeeViolationsRecordService.page(page, lambdaQueryWrapper);
            pageIndex++;
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                break;
            }

            // 获取销售信息
            List<ShopeeViolationVO> shopeeViolationVOList = pageResult.getRecords().stream().map(registration -> BeanUtil.copyProperties(registration, ShopeeViolationVO.class)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shopeeViolationVOList)) {
                // 调用ES获取销售信息
                List<String> accountNumberList = shopeeViolationVOList.stream().map(ShopeeViolationVO::getAccountNumber).distinct().collect(Collectors.toList());
                Map<String, Triple<String, String, String>> salesmanAccountDetailMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SHOPEE);

                for (ShopeeViolationVO shopeeViolationVO : shopeeViolationVOList) {
                    // 获取分组销售信息
                    if (MapUtils.isNotEmpty(salesmanAccountDetailMap)) {
                        Triple<String, String, String> saleSuperiorTriple = salesmanAccountDetailMap.get(shopeeViolationVO.getAccountNumber());
                        if (saleSuperiorTriple != null) {
                            shopeeViolationVO.setSaleMan(saleSuperiorTriple.getLeft());
                            shopeeViolationVO.setSaleTeamLeader(saleSuperiorTriple.getMiddle());
                            shopeeViolationVO.setSalesSupervisor(saleSuperiorTriple.getRight());
                        }
                    }
                }
            }
            newShopeeViolationsRecordList.addAll(shopeeViolationVOList);
        }

        excelWriter.write(newShopeeViolationsRecordList, writeSheet);
        excelWriter.finish();
        downloadLog.setDownloadCount(newShopeeViolationsRecordList.size());
        // 最多只需要50个店铺
        downloadLog.setAccountNumber(newShopeeViolationsRecordList.stream().map(ShopeeViolationVO::getAccountNumber).distinct().limit(50).collect(Collectors.joining(",")));
    }
}
