package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.platform.bo.SpuTitleRule;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/6 15:03
 * @description 产品字段信息
 */
@Getter
@Setter
public class ShopeeProductColumnInfo {

    //标题
    private String title;

    //该标题生成规则
    private SpuTitleRule titleRule;

    //描述
    private String description;

    //尺寸 长宽高
    private Map<String, Double> dimension;

    // 文案类型
    private Integer copyWritingType;


    public String getTitle() {
        if(StringUtils.isBlank(title)){
            return "";
        }
        return title;
    }

    public String getDescription() {
        if(StringUtils.isBlank(description)){
            return "";
        }
        return description;
    }
}