package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.dto.ShopeeAdjustInventoryRuleConfig;
import com.estone.erp.publish.shopee.dto.ShopeeConfigUpdateStockDTO;
import com.estone.erp.publish.shopee.enums.AdjustInventoryMethodEnum;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.handler.adjustStock.ShopeeAdjustStock;
import com.estone.erp.publish.shopee.service.ShopeeLinkManagementConfigService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.bean.SkuSystemStock;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsPublishShopeeAdjustInventoryResultMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishShopeeAdjustInventoryResult;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeAdjustInventoryItemPoolService;
import com.estone.erp.publish.tidb.publishtidb.service.UnsalableSkuService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/2/12 16:14
 */
@Slf4j
public class ShopeeConfigUpdateStockMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeLinkManagementConfigService shopeeLinkManagementConfigService;

    @Resource
    private AdsPublishShopeeAdjustInventoryResultMapper absPublishShopeeAdjustInventoryResultMapper;

    @Resource
    private IShopeeAdjustInventoryItemPoolService shopeeAdjustInventoryItemPoolService;

    @Resource
    private ShopeeAdjustStock fixedInventory;

    @Resource
    private ShopeeAdjustStock linkInventory;

    @Resource
    private ShopeeAdjustStock linkInventoryByPercent;

    @Resource
    private UnsalableSkuService unsalableSkuService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            ShopeeConfigUpdateStockDTO dto = JSON.parseObject(body, new TypeReference<ShopeeConfigUpdateStockDTO>() {
            });

            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), dto.getAccount(), true);
            if (Objects.isNull(saleAccount)) {
                shopeeAdjustInventoryItemPoolService.insertOperatorFailStatusByAccountMsg(dto.getConfigId(), dto.getAccount(), "账号不存在");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            ShopeeAdjustInventoryRuleConfig adjustInventoryRuleConfig = dto.getAdjustInventoryRuleConfig();

            // 1、根据规则过滤SKU维度数据
            List<EsShopeeItem> shopeeItems = filterSkuDimensionDataByRules(dto.getAccount(), dto.getConfigId(), dto.getAdjustInventoryRuleConfig(), dto.getShopeeItems());
            if (CollectionUtils.isEmpty(shopeeItems)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 2、将匹配商品根据规则修改库存
            ShopeeAdjustInventoryRuleConfig.AdjustInventoryMethod adjustInventoryMethod = adjustInventoryRuleConfig.getAdjustInventoryMethod();
            if (AdjustInventoryMethodEnum.FIXED_INVENTORY.isTrue(adjustInventoryMethod.getType())) {
                fixedInventory.calculateAdjustStock(saleAccount, shopeeItems, dto.getConfigName(), adjustInventoryRuleConfig);
            }
            if (AdjustInventoryMethodEnum.LINK_INVENTORY.isTrue(adjustInventoryMethod.getType())) {
                linkInventory.calculateAdjustStock(saleAccount, shopeeItems, dto.getConfigName(), adjustInventoryRuleConfig);
            }
            if (AdjustInventoryMethodEnum.LINK_INVENTORY_BY_PERCENT.isTrue(adjustInventoryMethod.getType())) {
                linkInventoryByPercent.calculateAdjustStock(saleAccount, shopeeItems, dto.getConfigName(), adjustInventoryRuleConfig);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("ShopeeConfigUpdateStockMqListener 更新库存异常：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }

    }

    /**
     * 根据规则过滤SKU维度数据
     *
     * @param account                   店铺账号
     * @param configId                  配置ID
     * @param adjustInventoryRuleConfig 规则配置
     * @param esShopeeItemList          商品列表
     * @return
     */
    private List<EsShopeeItem> filterSkuDimensionDataByRules(String account, Integer configId, ShopeeAdjustInventoryRuleConfig adjustInventoryRuleConfig, List<EsShopeeItem> esShopeeItemList) {
        Map<String, String> messageMap = new HashMap<>();

        // 1、X小时内调库存记录过滤(店铺、类型、sku)
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(adjustInventoryRuleConfig.getIgnoreThisRule());
        Set<String> ignoreSkuSet = esShopeeItemList.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toSet());
        List<String> ignoreSkuList = shopeeLinkManagementConfigService.selectStockFeedTaskSkuByAccount(account, ShopeeFeedTaskEnum.UPDATE_STOCK.getValue(), ignoreSkuSet, startTime, endTime);
        if (CollectionUtils.isNotEmpty(ignoreSkuList)) {
            for (String sku : ignoreSkuList) {
                messageMap.put(sku, String.format("%s小时内存在调库存记录，忽略执行", adjustInventoryRuleConfig.getIgnoreThisRule()));
            }

            esShopeeItemList = esShopeeItemList.stream().filter(item -> !ignoreSkuList.contains(item.getArticleNumber())).collect(Collectors.toList());
        }

        // 2、过滤在线列表sku维度数据
        Map<String, String> resultMsgMap = new HashMap<>();
        // 判断链接库存-指定仓库库存
        Integer accountType = adjustInventoryRuleConfig.getAccountType();
        List<Integer> unsalableLevels = adjustInventoryRuleConfig.getUnsalableLevels();
        Map<String, Integer> skuWareAndTriggeredMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unsalableLevels)) {
            // 单个店铺
            Map<String, Integer> unsalableLevelMap = getSkuWareAndTriggeredMap(esShopeeItemList);
            skuWareAndTriggeredMap.putAll(unsalableLevelMap);
        }

        List<EsShopeeItem> excludeEsShopeeItemList = esShopeeItemList.stream()
                .filter(item -> {
                    String sku = item.getArticleNumber();
                    JSONObject messageJson = new JSONObject();

                    // 判断知否滞销
                    if (CollectionUtils.isNotEmpty(unsalableLevels)) {
                        // 归属仓库id
                        int warehouseId = accountType == 0 ? 1 : 3;
                        Integer unsalable = skuWareAndTriggeredMap.getOrDefault(sku + "_" + warehouseId, 0);
                        messageJson.put("unsalable", unsalable);
                        if (!unsalableLevels.contains(unsalable)) {
                            messageMap.put(sku, messageJson.toJSONString());
                            resultMsgMap.put(sku, "所属仓库滞销状态不满足条件，忽略执行");
                            return false;
                        }
                    }

                    // 判断listing平台状态
                    String platformStatus = adjustInventoryRuleConfig.getPlatformStatus();
                    messageJson.put("platformStatus", item.getItemStatus());
                    if (!item.getItemStatus().equals(platformStatus)) {
                        messageMap.put(sku, messageJson.toJSONString());
                        resultMsgMap.put(sku, "listing平台状态不满足条件，忽略执行");
                        return false;
                    }

                    // 判断单品状态
                    List<String> skuStatuName = SingleItemEnum.getEnNameListByCodes(adjustInventoryRuleConfig.getItemStatusCodes());
                    messageJson.put("skuStatus", skuStatuName);
                    if (!skuStatuName.contains(item.getSkuStatus())) {
                        messageMap.put(sku, messageJson.toJSONString());
                        resultMsgMap.put(sku, "单品状态不满足条件，忽略执行");
                        return false;
                    }

                    // 判断listing链接库存
                    ShopeeAdjustInventoryRuleConfig.RangeConfig linkStock = adjustInventoryRuleConfig.getLinkStock();
                    messageJson.put("linkStock", item.getStock());
                    if (!(item.getStock() >= linkStock.getFrom() && item.getStock() < linkStock.getTo())) {
                        messageMap.put(sku, messageJson.toJSONString());
                        resultMsgMap.put(sku, "链接库存不满足条件，忽略执行");
                        return false;
                    }

                    // 获取库存信息
                    SkuSystemStock systemStock = SkuStockUtils.getSystemStocksBySku(sku.toUpperCase());
                    if (Objects.isNull(systemStock)) {
                        resultMsgMap.put(sku, "库存信息不存在，忽略执行");
                        return false;
                    }

                    // 深圳库存
                    if (accountType.equals(0)) {
                        ShopeeAdjustInventoryRuleConfig.RangeConfig linkStockMinusLocalStock = adjustInventoryRuleConfig.getLinkStockMinusLocalStock();
                        if (Objects.nonNull(linkStockMinusLocalStock)) {
                            // 链接库存-本地库存
                            int max = Math.max(systemStock.getUsableStock() - systemStock.getPendingStock(), 0);
                            int stock = Math.max(item.getStock() - max, 0);
                            if (!(stock >= linkStockMinusLocalStock.getFrom() && stock < linkStockMinusLocalStock.getTo())) {
                                messageMap.put(sku, messageJson.toJSONString());
                                resultMsgMap.put(sku, "链接库存-本地库存不满足条件，忽略执行");
                                return false;
                            }
                        }

                    }
                    // 南宁库存
                    if (accountType.equals(1)) {
                        ShopeeAdjustInventoryRuleConfig.RangeConfig linkStockMinusNnStock = adjustInventoryRuleConfig.getLinkStockMinusNnStock();
                        if (Objects.nonNull(linkStockMinusNnStock)) {
                            // 链接库存-南宁仓库存
                            int max = Math.max(systemStock.getNnUsableStock() - systemStock.getNnPendingStock(), 0);
                            int stock = Math.max(item.getStock() - max, 0);
                            if (!(stock >= linkStockMinusNnStock.getFrom() && stock < linkStockMinusNnStock.getTo())) {
                                messageMap.put(sku, messageJson.toJSONString());
                                resultMsgMap.put(sku, "链接库存-南宁仓库存不满足条件，忽略执行");
                                return false;
                            }
                        }
                    }

                    messageMap.put(sku, messageJson.toJSONString());
                    return true;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(excludeEsShopeeItemList)) {
            shopeeAdjustInventoryItemPoolService.insertOperatorByMessageMap(null, configId, messageMap, resultMsgMap);
            return Collections.emptyList();
        }
        Map<String, List<EsShopeeItem>> skuMapGroup = excludeEsShopeeItemList.stream().collect(Collectors.groupingBy(EsShopeeItem::getArticleNumber));

        // 3、过滤数仓sku维度数据
        List<AdsPublishShopeeAdjustInventoryResult> resultList = absPublishShopeeAdjustInventoryResultMapper.selectList(
                new LambdaQueryWrapper<AdsPublishShopeeAdjustInventoryResult>()
                        .eq(AdsPublishShopeeAdjustInventoryResult::getRuleTypeId, configId)
                        .in(AdsPublishShopeeAdjustInventoryResult::getSku, skuMapGroup.keySet())
        );
        List<AdsPublishShopeeAdjustInventoryResult> publishShopeeAdjustInventoryResults = resultList.stream()
                .filter(skuResult -> {
                    String sku = skuResult.getSku();
                    JSONObject messageJson = JSONObject.parseObject(messageMap.computeIfAbsent(sku, key -> new JSONObject().toJSONString()));

                    // 深圳仓、南宁仓、可售天数、链接库存调整为指定仓库
                    ShopeeAdjustInventoryRuleConfig.RangeConfig configLocalStock = adjustInventoryRuleConfig.getLocalStock();
                    ShopeeAdjustInventoryRuleConfig.RangeConfig configNnStock = adjustInventoryRuleConfig.getNnStock();
                    ShopeeAdjustInventoryRuleConfig.RangeConfig daysToSell = adjustInventoryRuleConfig.getDaysToSell();
                    ShopeeAdjustInventoryRuleConfig.AdjustInventoryMethod adjustInventoryMethod = adjustInventoryRuleConfig.getAdjustInventoryMethod();
                    if (Objects.nonNull(daysToSell) || Objects.nonNull(configLocalStock) || Objects.nonNull(configNnStock) || AdjustInventoryMethodEnum.LINK_INVENTORY_BY_PERCENT.isTrue(adjustInventoryMethod.getType())) {
                        // 获取库存信息
                        SkuSystemStock systemStock = SkuStockUtils.getSystemStocksBySku(sku.toUpperCase());
                        if (Objects.isNull(systemStock)) {
                            resultMsgMap.put(sku, "库存信息不存在，忽略执行");
                            return false;
                        }
                        // 库存信息
                        Integer usableStock = systemStock.getUsableStock();
                        Integer pendingStock = systemStock.getPendingStock();
                        Integer nnUsableStock = systemStock.getNnUsableStock();
                        Integer nnPendingStock = systemStock.getNnPendingStock();

                        // 记录日志
                        messageJson.put("localUsableStock", usableStock);
                        messageJson.put("localPendingStock", pendingStock);
                        messageJson.put("nnUsableStock", nnUsableStock);
                        messageJson.put("nnPendingStock", nnPendingStock);

                        // 深圳仓
                        if (Objects.nonNull(configLocalStock) && adjustInventoryRuleConfig.getAccountType().equals(0)) {
                            // 判断库存是否满足条件
                            int redisLocalStock = Math.max(usableStock - pendingStock, 0);
                            if (!(redisLocalStock >= configLocalStock.getFrom() && redisLocalStock < configLocalStock.getTo())) {
                                resultMsgMap.put(sku, "深圳仓库存不满足条件，忽略执行");
                                messageMap.put(sku, messageJson.toJSONString());
                                return false;
                            }
                        }

                        // 南宁仓
                        if (Objects.nonNull(configNnStock) && adjustInventoryRuleConfig.getAccountType().equals(1)) {
                            // 判断库存是否满足条件
                            int redisNnStock = Math.max(nnUsableStock - nnPendingStock, 0);
                            if (!(redisNnStock >= configNnStock.getFrom() && redisNnStock < configNnStock.getTo())) {
                                resultMsgMap.put(sku, "南宁仓库存不满足条件，忽略执行");
                                messageMap.put(sku, messageJson.toJSONString());
                                return false;
                            }
                        }

                        // 可售天数=可用库存/(全平台7天总销量/7)
                        if (Objects.nonNull(daysToSell)) {
                            // 全平台七天总销量(计算可售天数)
                            Integer orderNumPlats7d = skuResult.getOrderNumPlats7d();
                            // 可用库存
                            Integer usableStockToSellDays = usableStock;
                            if (accountType.equals(1)) {
                                usableStockToSellDays = usableStock + nnUsableStock;
                            }

                            int toSellDays = 0;
                            // 如果库存大于0，七天销量等于0，则可用库存天数=9999
                            if (usableStockToSellDays > 0 && orderNumPlats7d.equals(0)) {
                                toSellDays = 9999;
                            } else if (orderNumPlats7d.equals(0) || usableStockToSellDays.equals(0)) {
                                toSellDays = 0;
                            } else {
                                toSellDays = BigDecimal.valueOf(usableStockToSellDays).divide(BigDecimal.valueOf(orderNumPlats7d).divide(BigDecimal.valueOf(7), 6, RoundingMode.FLOOR), 0, RoundingMode.FLOOR).intValue();
                            }

                            // 记录日志
                            messageJson.put("orderNumPlats7d", orderNumPlats7d);
                            messageJson.put("toSellDays", toSellDays);

                            // 判断可售天数是否满足条件
                            if (!(toSellDays >= daysToSell.getFrom() && toSellDays < daysToSell.getTo())) {
                                resultMsgMap.put(sku, "可售天数不满足条件，忽略执行");
                                messageMap.put(sku, messageJson.toJSONString());
                                return false;
                            }
                        }
                    }
                    messageMap.put(sku, messageJson.toJSONString());
                    return true;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(publishShopeeAdjustInventoryResults)) {
            shopeeAdjustInventoryItemPoolService.insertOperatorByMessageMap(null, configId, messageMap, resultMsgMap);
            return Collections.emptyList();
        }

        // 4、获取满足条件的item集合数据
        Set<String> skuSet = publishShopeeAdjustInventoryResults.stream().map(AdsPublishShopeeAdjustInventoryResult::getSku).collect(Collectors.toSet());
        List<EsShopeeItem> shopeeItemList = skuSet.stream().map(skuMapGroup::get).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopeeItemList)) {
            return Collections.emptyList();
        }
        return shopeeItemList;
    }

    /**
     * 获取在线商品列表
     * @param accountType 店铺类型
     * @param esShopeeItems 在线列表
     * @return
     */
    private Map<String, Integer> getSkuWareAndTriggeredMap(List<EsShopeeItem> esShopeeItems) {
        List<String> skuList = esShopeeItems.stream()
                .map(EsShopeeItem::getArticleNumber)
                .collect(Collectors.toList());
        return unsalableSkuService.getSkuUnsalableLevelMap(skuList);
    }


}
