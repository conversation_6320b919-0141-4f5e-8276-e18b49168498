package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/27 9:56
 * @description 修改sip成本价
 */
@Getter
@Setter
public class UpdateSipItemPriceV2 implements RequestCommon {

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "sip_item_price")
    private List<UpdateSipItemPriceDetailV2> sipItemPriceList;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.UPDATE_SIP_ITEM_PRICE;
    }
}
