package com.estone.erp.publish.shopee.api.v2.param.voucher;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoucherPageV2 implements RequestCommon {

    @JSONField(name = "page_no")
    private Integer pageNo;
    @JSONField(name = "page_size")
    private Integer pageSize;

    /**
     * The status filter for retrieving voucher list. Available value: upcoming/ongoing/expired/all.
     */
    @JSONField(name = "status")
    private String status;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_VOUCHER_LIST;
    }
}
