package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;

@Data
@HeadFontStyle(fontHeightInPoints = 11)
public class BiddingActivitySubmitExcelDTO {
    /**
     * 竞价规格名称
     */
    @ExcelProperty("竞价规格名称")
    private String biddingModelName;

    /**
     * 竞价规格ID
     */
    @ExcelProperty("竞价规格ID")
    private String biddingModelId;

    /**
     * 竞价产品ID
     */
    @ExcelProperty("竞价产品ID")
    private String biddingItemId;

    /**
     * 我的规格名称
     */
    @ExcelProperty("我的规格名称")
    private String myModelName;

    /**
     * 我的规格ID
     */
    @ExcelProperty("我的规格ID")
    private String myModelId;

    /**
     * 竞标价格*
     */
    @ExcelProperty("竞标价格*")
    private Double biddingPrice;

    /**
     * 系统建议价
     */
    @ExcelProperty("系统建议价")
    private Double suggestedPrice;

    /**
     * 商品原价
     */
    @ExcelProperty("商品原价")
    private Double originalPrice;


    /**
     * 竞价库存*
     */
    @ExcelProperty("竞价库存*")
    private String biddingStock;

    /**
     * 建议库存
     */
    @ExcelProperty("建议库存")
    private Integer suggestedStock;


    /**
     * 当前库存
     */
    @ExcelProperty("当前库存")
    private Integer currentStock;
}
