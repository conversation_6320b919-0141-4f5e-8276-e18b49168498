package com.estone.erp.publish.shopee.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.estone.erp.publish.shopee.enums.ShopeeBiddingActivityEnums;

public class BiddingActivityUploadStatusConverter implements Converter<Integer> {

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value != null) {
            ShopeeBiddingActivityEnums.UploadStatusEnum methodEnum = ShopeeBiddingActivityEnums.UploadStatusEnum.convert(value);
            if (methodEnum != null) {
                return new WriteCellData<>(methodEnum.getDescription());
            }
        }
        return new WriteCellData<>("");
    }
}
