package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/9/27 9:58
 * @description
 */
@Getter
@Setter
public class UpdateSipItemPriceDetailV2 {

    /** 如果是变体必填*/
    @JSONField(name = "model_id")
    private Long modelId;

    /** 价格 */
    @JSONField(name = "sip_item_price")
    private Double sipItemPrice;
}
