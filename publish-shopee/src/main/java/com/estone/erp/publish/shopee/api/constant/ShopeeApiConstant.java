package com.estone.erp.publish.shopee.api.constant;

/**
 * shopee的API常量
 *
 * <AUTHOR>
 */
public class ShopeeApiConstant {

    /** PARTNER_ID */
    public static Integer PARTNER_ID = 10681;

    /** 秘钥 */
    public static String SECRET_KEY = "49a1bdba13e5577e0267a22fa7847bdf197215a5a5b15cb9d164441649078fd1";

    /** 错误 */
    public static final String ERROR = "error";

    /** 请求ip */
//    public static final String HOST = "https://partner.shopeemobile.com";

    /** 请求ip 国内的地址 */
    public static final String HOST = "https://openplatform.shopee.cn";

    /** 测试请求ip */
    public static final String TEST_HOST = "http://partner.test.shopeemobile.com";

    /** 平台物流获取 */
    public static final String GET_LOGISTICS = "/api/v1/logistics/channel/get";

    /** 类目获取 */
    public static final String GET_CATEGORIES = "/api/v1/item/categories/get";

    /** 属性获取 */
    public static final String GET_ATTRIBUTES = "/api/v1/item/attributes/get";

    /** 产品上传父属性 */
    public static final String ADD = "/api/v1/item/add";

    /** 产品上传子属性 */
    public static final String ADD_VARIATIONS = "/api/v1/item/add_variations";

    /** 初始化多个子属性 */
    public static final String TIER_VAR_INIT = "/api/v1/item/tier_var/init";

    /** 同步产品 */
    public static final String GET_ITEMS = "/api/v1/items/get";

    /** 产品详情 */
    public static final String ITEM_DETAIL = "/api/v1/item/get";

    /** 产品上架 或 下架 */
    public static final String ITEM_UPORDOWN = "/api/v1/items/unlist";

    /** 单属性修改库存 */
    public static final String UPDATE_STOCK = "/api/v1/items/update_stock";

    /** 单属性修改价格 */
    public static final String UPDATE_PRICE = "/api/v1/items/update_price";

    /** 单属性修改库存 */
    public static final String UPDATE_VARIATION_STOCK = "/api/v1/items/update_variation_stock";

    /** 多属性修改价格 */
    public static final String UPDATE_VARIATION_PRICE = "/api/v1/items/update_variation_price";


    /** 产品批量修改库存 */
    public static final String UPDATE_STOCK_BATCH = "/api/v1/items/update/items_stock";

    /** 产品批量修改价格 */
    public static final String UPDATE_PRICE_BATCH = "/api/v1/items/update/items_price";

    /** 产品多属性批量修改库存 */
    public static final String UPDATE_VARS_STOCK_BATCH = "/api/v1/items/update/vars_stock";

    /** 产品多属性批量修改价格 */
    public static final String UPDATE_VARS_PRICE_BATCH = "/api/v1/items/update/vars_price";

    /** 产品修改 */
    public static final String UPDATE_ITEM = "/api/v1/item/update";

    /** 产品删除 */
    public static final String ITEM_DELETE = "/api/v1/item/delete";

    /** 产品变体删除 */
    public static final String ITEM_DELETE_VARIATION = "/api/v1/item/delete_variation";

    /** 产品折扣删除 */
    public static final String DISCOUNT_DELETE = "/api/v1/discount/item/delete";

    /* 修改产品图片 */
    public static final String UPDATE_ITEM_IMAGE = "/api/v1/item/img/update";
}
