package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.mq.model.ShopeeSyncListingMqDto;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.Semaphore;

@Component
@Slf4j
//shopee同步在线Listing定时器
public class ShopeeSyncListingHandler extends AbstractJobHandler {

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private  FeedTaskService feedTaskService ;

    public ShopeeSyncListingHandler() {
        super("ShopeeSyncListingHandler");
    }

    @Getter
    @Setter
    public static class InnerParam {
        //店鋪账号
        private List<String> accountNumberList;
        //增量同步天数
        private int days;
        //线程池数量
        private int threadNum;
        //站点
        private String site;
        // 日期对应全量同步站点 若 1，MY 代表1号同步马来站点
        private Map<Integer, String> daySiteMap;
    }

    @Override
    @XxlJob("ShopeeSyncListingHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("获取参数--start");
        //获取参数供测试使用
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                log.error("参数解析错误！" + param);
                return ReturnT.FAIL;
            }
        }

        //同步账号
        List<String> accountNumberList = innerParam.getAccountNumberList();
        //增量同步天数
        int days = innerParam.getDays();
        int threadNum = innerParam.getThreadNum();
        log.info("获取参数--end");

        //控制线程池数量
        final Semaphore sp = new Semaphore(threadNum);//创建Semaphore信号量，初始化许可大小为5

        ShopeeAccountConfigExample example = new ShopeeAccountConfigExample();
        ShopeeAccountConfigExample.Criteria criteria = example.createCriteria();
        criteria.andSiteIsNotNull();
        if(CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountIn(accountNumberList);
        }
        String site = innerParam.getSite();
        if(StringUtils.isNotBlank(site)){
            criteria.andSiteEqualTo(site);
        }
        List<ShopeeAccountConfig> shopeeAccountList = shopeeAccountConfigService.selectByExample(example);
        if(CollectionUtils.isEmpty(shopeeAccountList)) {
            XxlJobLogger.log("根据条件查询账号为空 不执行同步！");
            return ReturnT.FAIL;
        }

        // 获取执行当天的日需要全量同步的站点
        int nowDay = DateUtils.getYmd(new Date())[2];
        String fullSyncSite = null;
        Map<Integer, String> daySiteMap = innerParam.getDaySiteMap();
        if(MapUtils.isNotEmpty(daySiteMap)) {
            fullSyncSite = daySiteMap.get(nowDay);
        }

        List<String> addSyncAccount = new ArrayList<>();
        // 增量同步账号集合
        Date time = com.estone.erp.common.util.DateUtils.addHours(new Date(),-2);
        for (ShopeeAccountConfig shopeeAccountConfig : shopeeAccountList) {
            String account = shopeeAccountConfig.getAccount();
            /*FeedTaskExample feedTaskExample = new FeedTaskExample();
            FeedTaskExample.Criteria taskExampleCriteria = feedTaskExample.createCriteria();
            taskExampleCriteria.andCreatedByEqualTo(StrConstant.ADMIN)
                    .andTaskTypeEqualTo(ShopeeFeedTaskEnum.SYNC_LISTING.getValue())
                    .andAccountNumberEqualTo(account)
                    .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus())
                    .andArticleNumberIsNull()
                    .andFinishTimeGreaterThanOrEqualTo(time);
            feedTaskExample.setLimit(1);
            List<FeedTask> feedTaskList = feedTaskService.selectByExample(feedTaskExample,Platform.Shopee.name());
            if (CollectionUtils.isEmpty(feedTaskList)) {
                addSyncAccount.add(account);
            }else {
                // 记录处理报告
                FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                    task.setAccountNumber(account);
                    task.setTaskType(ShopeeFeedTaskEnum.SYNC_LISTING.getValue());
                    task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    task.setCreatedBy(StrConstant.ADMIN);
                    task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    task.setResultMsg( String.format("增量同步Listing Msg:  %s 2h内存在全量同步成功账号，本次不执行增量同步",account));
                    task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                });
            }*/
            addSyncAccount.add(account);
        }

        XxlJobLogger.log("开始增量同步shopee在线Listing: " + addSyncAccount.size());
        Collections.shuffle(addSyncAccount);
        // 晚上普通服务销售操作不多 增量同步发送mq 多台服务器运行 加快运行同步速度
        for (String syncAccount : addSyncAccount) {

            ShopeeSyncListingMqDto dto = new ShopeeSyncListingMqDto();
            dto.setAccountNumber(syncAccount);
            dto.setCreatedBy(StrConstant.ADMIN);
            dto.setIsFullSync(false);
            dto.setDays(days);

            try{
                rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_SYNC_LISTING_ROUTE_KEY, JSON.toJSON(dto));
            } catch (Exception e) {
                XxlJobLogger.log( syncAccount + " 增量同步发送mq " +  e.getMessage());
            }
        }

        return ReturnT.SUCCESS;
    }


}
