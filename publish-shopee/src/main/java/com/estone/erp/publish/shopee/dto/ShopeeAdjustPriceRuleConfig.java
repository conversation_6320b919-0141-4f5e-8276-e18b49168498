package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.shopee.annotation.NeedToLog;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * shopee 链接管理 - 调价规则配置
 *
 * <AUTHOR>
 * @date 2024-07-04 下午3:32
 */
@Data
public class ShopeeAdjustPriceRuleConfig {

    /**
     * 优先级
     */
    private Integer level;

    /**
     * 单品状态
     * - 7001
     * - 7002
     */
    @NeedToLog(description = "单品状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Integer> itemStatusCodes;

//    /**
//     * 仓库销售属性 - 产品系统标签
//     */
//    @NeedToLog(description = "仓库销售属性")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private List<String> salesAttributes;

    /**
     * 平台状态
     */
    @NeedToLog(description = "平台状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String platformStatus;

    /**
     * 上架时长超过X天
     */
    @NeedToLog(description = "上架时长超过X天")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer addedDay;

    /**
     * 订单状态
     * - 0 : 待处理
     * - 1 ：待审核
     */
    @NeedToLog(description = "订单状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Integer> orderStatusCodes;

    /**
     * x天毛利率
     */
    @NeedToLog(description = "毛利率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderRangeConfig grossProfitRate;

    /**
     * x天毛利
     */
    @NeedToLog(description = "毛利")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderRangeConfig grossProfit;

    /**
     * x 天内单量
     */
    @NeedToLog(description = "订单销量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderRangeConfig orderCount;

    /**
     * x 天内单量趋势
     */
    @NeedToLog(description = "单量趋势")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderRangeConfig orderTrend;

    /**
     * X天每单毛利
     */
    @NeedToLog(description = "每单毛利")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderRangeConfig profitPerOrder;

    /**
     * 调价方式
     */
    @NotNull(message = "调价方式不能为空")
    @NeedToLog(description = "调价方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AdjustPriceMethod adjustPriceMethod;

    /**
     * 是否滞销
     */
    @NeedToLog(description = "是否滞销")
    private List<Integer> unsalableLevels;

    /**
     * 订单区间规则
     */
    @Data
    public static class OrderRangeConfig {
        /**
         * 订单范围
         */
        private Integer orderRange;
        /**
         * 订单趋势
         * 0 : 下降
         * 1 : 增长
         */
        private Integer orderTrend;

        /**
         * 开始范围
         * 毛利率两位小数，毛利两位小数，单量正整数
         */
        private String from;

        /**
         * 结束范围
         * 毛利率两位小数，毛利两位小数，单量正整数
         */
        private String to;

        /**
         * 值
         */
        private String value;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            OrderRangeConfig that = (OrderRangeConfig) o;
            return Objects.equals(orderRange, that.orderRange) && Objects.equals(orderTrend, that.orderTrend) && Objects.equals(from, that.from) && Objects.equals(to, that.to);
        }

        @Override
        public int hashCode() {
            int result = Objects.hashCode(orderRange);
            result = 31 * result + Objects.hashCode(orderTrend);
            result = 31 * result + Objects.hashCode(from);
            result = 31 * result + Objects.hashCode(to);
            return result;
        }

        @JsonIgnore
        public boolean isNullObject() {
            return orderRange == null && orderTrend == null && from == null && to == null;
        }

        @Override
        public String toString() {
            if (orderTrend != null) {
                return "最近" + orderRange + "天" + (orderTrend.equals(0) ? "下降" : "增长") + ":" + from + "-" + to;
            } else {
                return "最近" + orderRange + "天," + from + "-" + to;
            }
        }
    }

    /**
     * 调价方式配置
     */
    @Data
    public static class AdjustPriceMethod {
        /**
         * 类型
         * 1 : 按照店铺配置的折扣毛利率重新算价
         * 2 : 按百分比调价
         * 3 : 固定毛利率调价
         */
        private Integer type;

        /**
         * 0 : 降价
         * 1 : 涨价
         */
        private Integer priceTrend;

        /**
         * 百分比
         */
        private String percentage;

        /**
         * 毛利率
         */
        private String grossProfitRate;

        /**
         * 最低毛利率
         */
        private String minGrossProfitRate;

        /**
         * 最高毛利率
         */
        private String maxGrossProfitRate;


        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            AdjustPriceMethod that = (AdjustPriceMethod) o;
            return Objects.equals(type, that.type) && Objects.equals(priceTrend, that.priceTrend) && Objects.equals(grossProfitRate, that.grossProfitRate) && Objects.equals(minGrossProfitRate, that.minGrossProfitRate) && Objects.equals(maxGrossProfitRate, that.maxGrossProfitRate);
        }

        @Override
        public int hashCode() {
            int result = Objects.hashCode(type);
            result = 31 * result + Objects.hashCode(priceTrend);
            result = 31 * result + Objects.hashCode(grossProfitRate);
            result = 31 * result + Objects.hashCode(minGrossProfitRate);
            result = 31 * result + Objects.hashCode(maxGrossProfitRate);
            return result;
        }

        @Override
        public String toString() {
            if (type.equals(1)) {
                return "按照店铺配置的折扣毛利率重新算价";
            }
            if (type.equals(2)) {
                return "按百分比调价," + (priceTrend.equals(0) ? "降价" : "涨价") + percentage + "%,毛利率区间:[" + minGrossProfitRate + "-" + maxGrossProfitRate + "]";
            }
            if (type.equals(3)) {
                return "指定毛利率调价,算价毛利率：" + grossProfitRate;
            }
            return "";
        }
    }

    public void checkEmptyValue() {
        if (CollectionUtils.isEmpty(this.getItemStatusCodes())) {
            this.setItemStatusCodes(null);
        }
//        if (CollectionUtils.isEmpty(this.getSalesAttributes())) {
//            this.setSalesAttributes(null);
//        }
        if (CollectionUtils.isEmpty(this.getOrderStatusCodes())) {
            this.setOrderStatusCodes(null);
        }
        if (this.getGrossProfitRate() != null && this.getGrossProfitRate().isNullObject()) {
            this.setGrossProfitRate(null);
        }
        if (this.getGrossProfit() != null && this.getGrossProfit().isNullObject()) {
            this.setGrossProfit(null);
        }
        if (this.getOrderCount() != null && this.getOrderCount().isNullObject()) {
            this.setOrderCount(null);
        }
        if (this.getOrderTrend() != null && this.getOrderTrend().isNullObject()) {
            this.setOrderTrend(null);
        }
        if (this.getProfitPerOrder() != null && this.getProfitPerOrder().isNullObject()) {
            this.setProfitPerOrder(null);
        }

    }

}
