package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;

/**
 * shopee产品多属性
 *
 * <AUTHOR>
 */
public class ShopeeMultiVariationParam {

    @JSONField(name = "tier_index")
    private List<Integer> tierIndex = new ArrayList<>();

    @JSONField(name = "stock")
    private Integer stock;

    @JSONField(name = "price")
    private Double price;

    @JSONField(name = "variation_sku")
    private String variationSku;

    public List<Integer> getTierIndex() {
        return tierIndex;
    }

    public void setTierIndex(List<Integer> tierIndex) {
        this.tierIndex = tierIndex;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getVariationSku() {
        return variationSku;
    }

    public void setVariationSku(String variationSku) {
        this.variationSku = variationSku;
    }
}
