package com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/07/06 16:34
 * @description 删除产品项目
 */
@Getter
@Setter
public class DeleteGlobalItem implements RequestCommon {

    @JSONField(name = "global_item_id")
    private Long globalItemId;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.DELETE_GLOBAL_ITEM;
    }
}
