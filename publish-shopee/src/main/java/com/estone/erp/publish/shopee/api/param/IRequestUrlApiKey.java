package com.estone.erp.publish.shopee.api.param;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 获取请求路径接口
 *
 * <AUTHOR>
 */
public interface IRequestUrlApiKey {

    /**
     * 获取请求路径
     * 
     * @return
     */
    @JSONField(serialize = false)
    String getRequestUrl();

    /**
     * 获取api秘钥
     * 
     * @return
     */
    @JSONField(serialize = false)
    String getApiKey();
}
