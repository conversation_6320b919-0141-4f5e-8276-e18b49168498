package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.shopee.bo.ShippingFeePrice;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.bo.SiteCategoryId;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeTemplate;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.shopee.util.AliOSSUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 上传产品
 */
public class AddParam implements IRequestUrlApiKey {

    @JSONField(name = "category_id")
    private Integer categoryId;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "price")
    private Double price;

    @JSONField(name = "stock")
    private Integer stock;

    /** 可选 */
    @JSONField(name = "item_sku")
    private String itemSku;

    /** 可选 */
    @JSONField(name = "variations")
    private List<ShopeeSingleVariationParam> variations = new ArrayList<>();

    @JSONField(name = "images")
    private List<ShopeeImageParam> images = new ArrayList<>();

    @JSONField(name = "attributes")
    private List<ShopeeAttributeParam> attributes = new ArrayList<>();

    @JSONField(name = "logistics")
    private List<ShopeeLogisticParam> logistics = new ArrayList<>();

    @JSONField(name = "weight")
    private Double weight = 0.0;

    /** 可选 */
    @JSONField(name = "package_length")
    private Integer packageLength;

    /** 可选 */
    @JSONField(name = "package_width")
    private Integer packageWidth;

    /** 可选 */
    @JSONField(name = "package_height")
    private Integer packageHeight;

    /** 可选 */
    @JSONField(name = "days_to_ship")
    private Integer daysToShip;

    /** 可选 */
    @JSONField(name = "wholesales")
    private List<ShopeeWholeSalesParam> wholesales = new ArrayList<>();

    @JSONField(name = "partner_id")
    private Integer partnerId;

    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(serialize = false)
    private String apiKey;

    @JSONField(name = "is_pre_order")
    private Boolean isPreOrder;

    public AddParam() {
    }

    public AddParam(SaleAccountAndBusinessResponse account, ShopeeTemplate template, ShopeeAccountConfig shopeeAccountConfig) {
        //String accountSiteCode = ShopeeCommonUtils.getAccountSiteCode(account);
        //现在shopee账号会直接返回账号的站点了，不会自己去切割
        String accountSiteCode = account.getAccountSite();
        //List<SiteCategoryId> siteCategoryIds = template.getSiteCategoryIds();
        List<SiteCategoryId> siteCategoryIds = JSONArray.parseArray(template.getSiteCategoryIdStr(), SiteCategoryId.class);
        for (SiteCategoryId siteCategoryId : siteCategoryIds) {
            if (StringUtils.equalsIgnoreCase(siteCategoryId.getSite(), accountSiteCode)) {
                this.categoryId = siteCategoryId.getCategoryId();
            }
        }

        //JSON.parseObject(JSON.toJSONString(template.getAttributes()), new TypeReference<List<ShopeeAttributeParam>>() {});
        //this.attributes = JSON.parseObject(JSON.toJSONString(template.getAttributes()), new TypeReference<List<ShopeeAttributeParam>>() {});
        if (StringUtils.isNotEmpty(template.getAttributesStr())) {
            this.attributes = JSONArray.parseArray(template.getAttributesStr(),ShopeeAttributeParam.class);

            for (Iterator<ShopeeAttributeParam> iterator = this.attributes.iterator(); iterator.hasNext();) {
                ShopeeAttributeParam attribute = iterator.next();
                if (!StringUtils.equalsIgnoreCase(attribute.getSite(), accountSiteCode)) {
                    iterator.remove();
                }
                attribute.setSite(null);
                attribute.setAttributeName(null);
                attribute.setInputType(null);
            }
        }

        //发货天数
        //Integer daysToShip = template.getDaysToShip() == null ? 2 : template.getDaysToShip();
        //发货天数，除了巴西3天，其他都是2天
        //********，shopee新的政策，所有的平台默认都是3天
        // 2024/1/11 shopee 新政策，所有平台默认2天
        /*if(account.getAccountSite().equalsIgnoreCase("br")) {
            defaultDayToShip = 3;
        } else {
            defaultDayToShip = 2;
        }*/
        this.daysToShip = template.getDaysToShip() == null ? ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP : template.getDaysToShip();
        this.isPreOrder = ShopeeDaysToShipConstant.isPreOrder(this.daysToShip);

        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        //this.images = template.getImages();
        this.images = JSONArray.parseArray(template.getImagesStr(),ShopeeImageParam.class);
        List<ShopeeImageParam> deleteImageList = new ArrayList<>();
        for (int i = 1; i < this.images.size(); i++) {
            if(this.images.get(i).getUrl().equals(this.images.get(0).getUrl())) {
                deleteImageList.add(this.images.get(i));
            }
        }
        this.images.removeAll(deleteImageList);
        if (this.images.size() > 9) {
            this.images = new ArrayList<>(this.images.subList(0, 9));
        }

        //转换图片，把图片转换成阿里云的地址
        for (ShopeeImageParam image : images) {
            String url = image.getUrl();
            //替换图片的URL
            //url = url.replace(AliOSSUtils.INTRANET_IMAGE_BASE_URL,AliOSSUtils.ALI_IMAGE_BASE_URL);
            url = AliOSSUtils.transSystemImageToAliOssImage(url);
            image.setUrl(url);
        }

        // 主货号
        this.itemSku = template.getSku();

        // 物流
        //ObjectMapper objectMapper = new ObjectMapper();
        try {
            /*this.logistics = objectMapper.readValue(objectMapper.writeValueAsBytes(template.getLogistics()),
                    new com.fasterxml.jackson.core.type.TypeReference<List<ShopeeLogisticParam>>() {
                    });*/
            this.logistics = JSONArray.parseArray(template.getLogisticsStr(),ShopeeLogisticParam.class);
        }
        catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        for (Iterator<ShopeeLogisticParam> iterator = this.logistics.iterator(); iterator.hasNext();) {
            ShopeeLogisticParam logistic = iterator.next();
            if (!StringUtils.equalsIgnoreCase(logistic.getSite(), accountSiteCode)) {
                iterator.remove();
            }
        }

        // 台湾站点用中文标题和描述
        // 描述最后要加上关键字，平台会提高搜索权限
        if ("TW".equals(accountSiteCode)) {
            String chKeyWord = StringUtils.isEmpty(template.getChKeyword())?"":template.getChKeyword() + "\n";
            this.name = template.getChineseName();
            this.description = chKeyWord + template.getChDescription();
        }
        else {
            String keyword = StringUtils.isEmpty(template.getKeyword())?"":template.getKeyword() + "\n";
            this.name = template.getName();
            this.description = keyword + template.getDescription();
        }
        //标题不能超过255个字符
        if(name.length() > 255) {
            name = name.substring(0,255);
        }

        //重量：1.舍弃小数点    2.个位数向上取整（如果是0就不用向上取整）   3.再加10g
        double maxWeight = 0;
        List<ShopeeSku> shopeeSkus = JSONArray.parseArray(template.getShopeeSkusStr(), ShopeeSku.class);
        for (ShopeeSku shopeeSku : shopeeSkus) {
            if(shopeeSku.getShippingWeight() != null) {
                if(shopeeSku.getShippingWeight() > maxWeight) {
                    maxWeight = shopeeSku.getShippingWeight();
                }
            }
        }
        Double shippingWeightKg = maxWeight;
        Double shippingWeightG = maxWeight * 1000;
        //个位数的值
        int units = shippingWeightG.intValue() % 10;
        if(units == 0) {
            this.weight = NumberUtils.format(shippingWeightKg + 0.01);
        } else{
            this.weight = NumberUtils.format(shippingWeightKg + 0.02 - Double.parseDouble("0.00" + units));
        }

        List<ShippingFeePrice> shippingFeePrices = JSON.parseArray(template.getShippingFeePricesStr(),ShippingFeePrice.class);
        /*Double thisSitePrice = 0.0;
        for (ShippingFeePrice shippingFeePrice : shippingFeePrices) {
            if (StringUtils.equalsIgnoreCase(shippingFeePrice.getSite(), account.getHistoryaccountnumber())) {
                thisSitePrice = shippingFeePrice.getPrice();
                break;
            }
        }*/
        //Double thisSitePrice1 = thisSitePrice;
        //如果不是多属性产品的话，直接在这里设置价格
        if (shopeeSkus.size() == 1 && StringUtils.equals(shopeeSkus.get(0).getSku(), template.getSku())) {
            ShopeeSku sku = shopeeSkus.get(0);
            //this.price = thisSitePrice;
            for (ShippingFeePrice shippingFeePrice : shippingFeePrices) {
                if(StringUtils.equalsIgnoreCase(shippingFeePrice.getSite(), accountSiteCode)
                        && StringUtils.equalsIgnoreCase(shippingFeePrice.getSku(), template.getSku())){
                    this.price = shippingFeePrice.getPrice();
                    break;
                }
            }
            this.stock = sku.getQuantity();
        }
        //如果是多属性商品的话，看看有多少个维度的多属性（比如size和color就是两个维度），单维度在这里直接设置属性，多维度要调用另一个接口
        //********逻辑改变，因为要上传变体图片，所以只要变体数量超过一个，都要调用生成变体数据的那个接口
        else {
            //属性里color或size有一个是null就说明多属性只有一个维度
            if(StringUtils.isEmpty(shopeeSkus.get(0).getColor()) && StringUtils.isEmpty(shopeeSkus.get(0).getSize())){
                // 多属性, 价格取试算器的价格
                shopeeSkus.forEach((sku) -> {
                    ShopeeSingleVariationParam var = new ShopeeSingleVariationParam();
                    /*if ("TW".equals(accountSiteCode)) {
                        var.setName(StringUtils.isNotBlank(sku.getChineseColor())?sku.getChineseColor():sku.getChineseSize());
                    }
                    else {
                        var.setName(StringUtils.isNotBlank(sku.getColor())?sku.getColor():sku.getSize());
                    }*/
                    for (ShippingFeePrice shippingFeePrice : shippingFeePrices) {
                        if(StringUtils.equalsIgnoreCase(shippingFeePrice.getSite(), accountSiteCode)
                                && StringUtils.equalsIgnoreCase(shippingFeePrice.getSku(), sku.getSku())){
                            var.setPrice(shippingFeePrice.getPrice());
                            break;
                        }
                    }
                    var.setStock(sku.getQuantity());
                    var.setVariationSku(sku.getSku());
                    var.setName(sku.getSku());
                    this.variations.add(var);
                });
            } else {
                //多维度属性需要另外的接口设置，但是stock和price在这个接口是必须的，所以这里必须设置一个值
                //因为越南站点和印尼站点的货币面值较大，所以这两个站点的价格要设置大一点
                if("VN".equalsIgnoreCase(accountSiteCode) || "ID".equalsIgnoreCase(accountSiteCode)
                        || "CO".equalsIgnoreCase(accountSiteCode) || "CL".equalsIgnoreCase(accountSiteCode)) {
                    this.price = 999000.0;
                } else {
                    this.price = 999.0;
                }
                this.stock = 999;
            }
        }

        // 不做批发价
        // this.wholesales = new ArrayList<>(template.getWholesales());

        // this.packageHeight = 10;
        // this.packageWidth = 10;
        // this.packageLength = 10;

        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.parseInt(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    public AddParam(SaleAccountAndBusinessResponse account, ShopeeTemplateNew template, Map<String, String> imgMappingMap) {
        String accountSiteCode = account.getAccountSite();

        //类目id
        this.categoryId = template.getCategoryId();

        //属性
        if (StringUtils.isNotEmpty(template.getAttributesStr())) {
            this.attributes = JSONArray.parseArray(template.getAttributesStr(),ShopeeAttributeParam.class);

            for (Iterator<ShopeeAttributeParam> iterator = this.attributes.iterator(); iterator.hasNext();) {
                ShopeeAttributeParam attribute = iterator.next();
                if (!StringUtils.equalsIgnoreCase(attribute.getSite(), accountSiteCode)) {
                    iterator.remove();
                }
                attribute.setSite(null);
                attribute.setAttributeName(null);
                attribute.setInputType(null);
            }
        }

        //发货天数
        // 待修改
        this.daysToShip = template.getDaysToShip() == null ? ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP : template.getDaysToShip();
        this.isPreOrder = ShopeeDaysToShipConstant.isPreOrder(this.daysToShip);

        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        this.images = JSONArray.parseArray(template.getImagesStr(),ShopeeImageParam.class);
        List<ShopeeImageParam> deleteImageList = new ArrayList<>();
        for (int i = 1; i < this.images.size(); i++) {
            if(this.images.get(i).getUrl().equals(this.images.get(0).getUrl())) {
                deleteImageList.add(this.images.get(i));
            }
        }
        this.images.removeAll(deleteImageList);
        if (this.images.size() > 9) {
            this.images = new ArrayList<>(this.images.subList(0, 9));
        }

        //转换图片，把图片转换成阿里云的地址
        for (ShopeeImageParam image : images) {
            String url = image.getUrl();
            //替换图片的URL
            //url = url.replace(AliOSSUtils.INTRANET_IMAGE_BASE_URL,AliOSSUtils.ALI_IMAGE_BASE_URL);
//            url = AliOSSUtils.transSystemImageToAliOssImage(url);
            url = imgMappingMap.get(url);
            image.setUrl(url);
        }

        // 主货号
        this.itemSku = template.getSku();

        // 物流
        try {
            this.logistics = JSONArray.parseArray(template.getLogisticsStr(),ShopeeLogisticParam.class);
        }
        catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        for (Iterator<ShopeeLogisticParam> iterator = this.logistics.iterator(); iterator.hasNext();) {
            ShopeeLogisticParam logistic = iterator.next();
            if (!StringUtils.equalsIgnoreCase(logistic.getSite(), accountSiteCode)) {
                iterator.remove();
            }
        }

        // 台湾站点用中文标题和描述
        // 描述最后要加上关键字，平台会提高搜索权限
//        if ("TW".equals(accountSiteCode)) {
//            String chKeyWord = StringUtils.isEmpty(template.getChKeyword())?"":template.getChKeyword() + "\n";
//            this.name = template.getChineseName();
//            this.description = chKeyWord + template.getChDescription();
//        }
//        else {
//            String keyword = StringUtils.isEmpty(template.getKeyword())?"":template.getKeyword() + "\n";
            this.name = template.getName();
            this.description = template.getDescription();
//        }
        //标题不能超过255个字符
        if(name.length() > 255) {
            name = name.substring(0,255);
        }

        //重量：1.舍弃小数点    2.个位数向上取整（如果是0就不用向上取整）   3.再加10g
        double maxWeight = 0;
        List<ShopeeSku> shopeeSkus = JSONArray.parseArray(template.getShopeeSkusStr(), ShopeeSku.class);
        for (ShopeeSku shopeeSku : shopeeSkus) {
            if(shopeeSku.getShippingWeight() != null) {
                if(shopeeSku.getShippingWeight() > maxWeight) {
                    maxWeight = shopeeSku.getShippingWeight();
                }
            }
        }
        Double shippingWeightKg = maxWeight;
        Double shippingWeightG = maxWeight * 1000;
        //个位数的值
        int units = shippingWeightG.intValue() % 10;
        if(units == 0) {
            this.weight = NumberUtils.format(shippingWeightKg + 0.01);
        } else{
            this.weight = NumberUtils.format(shippingWeightKg + 0.02 - Double.parseDouble("0.00" + units));
        }

        //如果不是多属性产品的话，直接在这里设置价格
        if (shopeeSkus.size() == 1 && StringUtils.equals(shopeeSkus.get(0).getSku(), template.getSku())) {
            ShopeeSku sku = shopeeSkus.get(0);
            this.price = sku.getPrice();
            this.stock = sku.getQuantity();
        }
        //如果是多属性商品的话，看看有多少个维度的多属性（比如size和color就是两个维度），单维度在这里直接设置属性，多维度要调用另一个接口
        //********逻辑改变，因为要上传变体图片，所以只要变体数量超过一个，都要调用生成变体数据的那个接口
        else {
            //属性里color或size有一个是null就说明多属性只有一个维度
            if(StringUtils.isEmpty(shopeeSkus.get(0).getColor()) && StringUtils.isEmpty(shopeeSkus.get(0).getSize())){
                // 多属性, 价格取试算器的价格
                shopeeSkus.forEach((sku) -> {
                    ShopeeSingleVariationParam var = new ShopeeSingleVariationParam();
                    var.setPrice(sku.getPrice());
                    var.setStock(sku.getQuantity());
                    var.setVariationSku(sku.getSku());
                    var.setName(sku.getSku());
                    this.variations.add(var);
                });
            } else {
                //多维度属性需要另外的接口设置，但是stock和price在这个接口是必须的，所以这里必须设置一个值
                //因为越南站点和印尼站点的货币面值较大，所以这两个站点的价格要设置大一点
                if("VN".equals(accountSiteCode) || "ID".equals(accountSiteCode)
                        || "CO".equalsIgnoreCase(accountSiteCode) || "CL".equalsIgnoreCase(accountSiteCode)) {
                    this.price = 999000.0;
                } else {
                    this.price = 999.0;
                }
                this.stock = 999;
            }
        }

        // 不做批发价
        // this.wholesales = new ArrayList<>(template.getWholesales());

        // this.packageHeight = 10;
        // this.packageWidth = 10;
        // this.packageLength = 10;

        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.parseInt(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.ADD;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getItemSku() {
        return itemSku;
    }

    public void setItemSku(String itemSku) {
        this.itemSku = itemSku;
    }

    public List<ShopeeSingleVariationParam> getVariations() {
        return variations;
    }

    public void setVariations(List<ShopeeSingleVariationParam> variations) {
        this.variations = variations;
    }

    public List<ShopeeImageParam> getImages() {
        return images;
    }

    public void setImages(List<ShopeeImageParam> images) {
        this.images = images;
    }

    public List<ShopeeAttributeParam> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<ShopeeAttributeParam> attributes) {
        this.attributes = attributes;
    }

    public List<ShopeeLogisticParam> getLogistics() {
        return logistics;
    }

    public void setLogistics(List<ShopeeLogisticParam> logistics) {
        this.logistics = logistics;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Integer getPackageLength() {
        return packageLength;
    }

    public void setPackageLength(Integer packageLength) {
        this.packageLength = packageLength;
    }

    public Integer getPackageWidth() {
        return packageWidth;
    }

    public void setPackageWidth(Integer packageWidth) {
        this.packageWidth = packageWidth;
    }

    public Integer getPackageHeight() {
        return packageHeight;
    }

    public void setPackageHeight(Integer packageHeight) {
        this.packageHeight = packageHeight;
    }

    public Integer getDaysToShip() {
        return daysToShip;
    }

    public void setDaysToShip(Integer daysToShip) {
        this.daysToShip = daysToShip;
    }

    public List<ShopeeWholeSalesParam> getWholesales() {
        return wholesales;
    }

    public void setWholesales(List<ShopeeWholeSalesParam> wholesales) {
        this.wholesales = wholesales;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Boolean getPreOrder() {
        return isPreOrder;
    }

    public void setPreOrder(Boolean preOrder) {
        isPreOrder = preOrder;
    }
}
