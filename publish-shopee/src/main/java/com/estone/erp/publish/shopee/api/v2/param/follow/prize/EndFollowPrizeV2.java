package com.estone.erp.publish.shopee.api.v2.param.follow.prize;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

@Data
public class EndFollowPrizeV2 implements RequestCommon {
    @JSONField(name = "campaign_id")
    private Long campaign_id;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.END_FOLLOW_PRIZE;
    }
}
