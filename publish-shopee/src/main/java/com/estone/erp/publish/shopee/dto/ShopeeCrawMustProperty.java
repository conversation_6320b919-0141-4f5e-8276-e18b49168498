package com.estone.erp.publish.shopee.dto;

import lombok.Data;

/**
 * shopee 爬虫必须的属性
 */
@Data
public class ShopeeCrawMustProperty {

    /**
     * 子账号
     */
    private String sellerAccount;

    /**
     * 商家
     */
    private String merchantName;

    /**
     * 商家id
     */
    private Integer merchantId;

    /**
     * shopId
     */
    private Long shopId;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 站点
     */
    private String site;

}
