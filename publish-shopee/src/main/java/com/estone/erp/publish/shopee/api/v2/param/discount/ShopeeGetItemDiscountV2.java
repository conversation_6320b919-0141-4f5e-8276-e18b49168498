package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-01 上午11:17
 */
@Getter
@Setter
public class ShopeeGetItemDiscountV2 implements RequestCommon {
    /**
     * 商品ids Item ID list, can send 1 to 50 items.
     */
    @JSONField(name = "item_id_list")
    private List<Long> itemIdList;

    /**
     * @return
     */
    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_ITEM_PROMOTION;
    }
}
