package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.shopee.call.v2.ShopeeUnlistItemCallV2;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.jobHandler.offline.ExecuteOfflineListingParam;
import com.estone.erp.publish.shopee.jobHandler.offline.OfflineSkuInfo;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeTaskOffLinkListingReport;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeTaskOffLinkListingSpuLog;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeTaskOffLinkListingReportService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeTaskOffLinkListingSpuLogService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * shopee 过滤下架任务
 */
@Slf4j
public class ShopeeExecuteOfflineLinkItemMqListener implements ChannelAwareMessageListener {

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Resource
    private ShopeeTaskOffLinkListingReportService shopeeTaskOffLinkListingReportService;

    @Resource
    private ShopeeTaskOffLinkListingSpuLogService shopeeTaskOffLinkListingSpuLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("ShopeeOfflineLinkItemMqListener body to String is null");
            }

            boolean sign = false;
            try {
                ExecuteOfflineListingParam dto = JSON.parseObject(body, new TypeReference<>() {
                });
                doService(dto);
                sign = true;
            } catch (Exception e) {
                log.error("ShopeeOfflineLinkItemMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("ShopeeOfflineLinkItemMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("ShopeeOfflineLinkItemMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("ShopeeOfflineLinkItemMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    private void doService(ExecuteOfflineListingParam dto) {
        if (dto == null || dto.getAccountNumber() == null || dto.getId() == null) {
            return;
        }
        ShopeeTaskOffLinkListingReport shopeeTaskOffLinkListingReport = shopeeTaskOffLinkListingReportService.getById(dto.getId());
        if (shopeeTaskOffLinkListingReport == null) {
            log.error("ShopeeExecuteOfflineLinkItemMqListener.getById" + dto.getId() + " is null");
            return;
        }
        if (shopeeTaskOffLinkListingReport.getStatus() != 2) {
            log.error("ShopeeExecuteOfflineLinkItemMqListener.getById" + dto.getId() + " status is not 2 , status is " + shopeeTaskOffLinkListingReport.getStatus());
        }

        LocalDate statisticsDate = shopeeTaskOffLinkListingReport.getStatisticsDate();
        String accountNumber = shopeeTaskOffLinkListingReport.getAccountNumber();

        List<ShopeeTaskOffLinkListingSpuLog> list = getShopeeTaskOffLinkListingSpuLogs(accountNumber, statisticsDate, shopeeTaskOffLinkListingReport.getSpuOnlineCount());
        for (ShopeeTaskOffLinkListingSpuLog spuLog : list) {
            spuLog.setStatus(1);
            spuLog.setRemark("");
        }
        // 检查下销量写入时间对不对
        filterSaleTime(list);
        List<ShopeeTaskOffLinkListingSpuLog> errorSaleTimeList = list.stream().filter(a -> a.getStatus() == 3).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorSaleTimeList)) {
            for (ShopeeTaskOffLinkListingSpuLog spuLog : errorSaleTimeList) {
                ShopeeFeedTaskHandleUtil.initFeedTask((feedTaskTemp) -> {
                    feedTaskTemp.setAssociationId(spuLog.getItemId());
                    feedTaskTemp.setArticleNumber(spuLog.getSpu());
                    feedTaskTemp.setTaskType(ShopeeFeedTaskEnum.END_ITEM.getValue());
                    feedTaskTemp.setAccountNumber(accountNumber);
                    feedTaskTemp.setCreatedBy("admin");
                    feedTaskTemp.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    feedTaskTemp.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                    if(StringUtils.isNotBlank(spuLog.getRemark())){
                        feedTaskTemp.setResultMsg(StrUtil.removeNonBmpUnicodes(spuLog.getRemark()));
                    }
                    feedTaskTemp.setFinishTime(new Timestamp(System.currentTimeMillis()));
                });
            }
            shopeeTaskOffLinkListingSpuLogService.updateBatchById(errorSaleTimeList, 100);
        }

        List<ShopeeTaskOffLinkListingSpuLog> offLineList = list.stream().filter(a -> a.getStatus() != 3).collect(Collectors.toList());

        // 执行下架
        processOffline(dto, offLineList, shopeeTaskOffLinkListingReport);
    }

    private List<ShopeeTaskOffLinkListingSpuLog> getShopeeTaskOffLinkListingSpuLogs(String accountNumber, LocalDate statisticsDate, Integer lonSiZe) {
        LambdaQueryWrapper<ShopeeTaskOffLinkListingSpuLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopeeTaskOffLinkListingSpuLog::getAccountNumber, accountNumber);
        queryWrapper.eq(ShopeeTaskOffLinkListingSpuLog::getStatisticsDate, statisticsDate);
        queryWrapper.in(ShopeeTaskOffLinkListingSpuLog::getStatus, 1,3);
        queryWrapper.orderByAsc(ShopeeTaskOffLinkListingSpuLog::getId);
        List<TidbPageMeta<Long>> tidbPageMetaMap = shopeeTaskOffLinkListingSpuLogService.getTidbPageMetaMap(queryWrapper);

        List<ShopeeTaskOffLinkListingSpuLog> list = new ArrayList<>(lonSiZe);
        for (TidbPageMeta<Long> longTidbPageMeta : tidbPageMetaMap) {
            Long startKey = longTidbPageMeta.getStartKey();
            Long endKey = longTidbPageMeta.getEndKey();
            LambdaQueryWrapper<ShopeeTaskOffLinkListingSpuLog> tempQueryWrapper = new LambdaQueryWrapper<>();
            tempQueryWrapper.eq(ShopeeTaskOffLinkListingSpuLog::getAccountNumber, accountNumber);
            tempQueryWrapper.eq(ShopeeTaskOffLinkListingSpuLog::getStatisticsDate, statisticsDate);
            tempQueryWrapper.in(ShopeeTaskOffLinkListingSpuLog::getStatus, 1,3);
            tempQueryWrapper.between(ShopeeTaskOffLinkListingSpuLog::getId, startKey, endKey);
            List<ShopeeTaskOffLinkListingSpuLog> list1 = shopeeTaskOffLinkListingSpuLogService.list(tempQueryWrapper);
            list.addAll(list1);
        }
        return list;
    }

    private void filterSaleTime(List<ShopeeTaskOffLinkListingSpuLog> list) {
        for (ShopeeTaskOffLinkListingSpuLog spuLog : list) {
            String extraData = spuLog.getExtraData();
            if (StringUtils.isBlank(extraData)) {
                continue;
            }
            List<OfflineSkuInfo> offlineSizeVos = JSON.parseArray(extraData, OfflineSkuInfo.class);
            StringJoiner stringJoiner = new StringJoiner(",");
            for (OfflineSkuInfo skuLog : offlineSizeVos) {
                // Shopee_accountNumber_sellerSku_productId (id唯一值 itemSeller + '_' + itemSku + "_" + itemId)
                boolean b = CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime("Shopee" + "_" + spuLog.getAccountNumber() + "_" + skuLog.getSellerSku() + "_" + spuLog.getItemId());
                if (!b) {
                    stringJoiner.add(skuLog.getSku() + ":" + "销量写入时间有问题");
                }
            }
            String message = stringJoiner.toString();
            if (StringUtils.isNotBlank(message)) {
                spuLog.setStatus(3);
                spuLog.setRemark(message);
                spuLog.setUpdatedTime(LocalDateTime.now());
            }
        }
    }

    /**
     * 执行下架
     *
     * @param dto
     * @param list
     * @param shopeeTaskOffLinkListingReport
     */
    private void processOffline(ExecuteOfflineListingParam dto, List<ShopeeTaskOffLinkListingSpuLog> list, ShopeeTaskOffLinkListingReport shopeeTaskOffLinkListingReport) {
        if (list == null) {
            return;
        }
        if (list.isEmpty()) {
            // 更新报告状态
            shopeeTaskOffLinkListingReport.setStatus(3); // 已完成
            shopeeTaskOffLinkListingReport.setUpdatedTime(LocalDateTime.now());
            shopeeTaskOffLinkListingReportService.updateById(shopeeTaskOffLinkListingReport);
            return;
        }

        String accountNumber = dto.getAccountNumber();

        // 获取账号信息
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, accountNumber, true);

        // 根据itemId分组
        Map<String, List<ShopeeTaskOffLinkListingSpuLog>> itemIdGroupMap = list.stream()
                .collect(Collectors.groupingBy(ShopeeTaskOffLinkListingSpuLog::getItemId));

        // 获取所有itemId
        List<String> itemIdList = new ArrayList<>(itemIdGroupMap.keySet());

        // 分批处理，每批50个
        List<List<String>> partitionList = PagingUtils.pagingList(itemIdList, 50);

        try {
            // 执行下架
            partitionList.forEach(batchItemIds -> {
                // 创建处理报告
                Map<String, FeedTask> feedTaskMap = batchItemIds.stream()
                        .map(itemId -> {
                            List<ShopeeTaskOffLinkListingSpuLog> logs = itemIdGroupMap.get(itemId);
                            if (logs == null || logs.isEmpty()) {
                                return null;
                            }
                            // 创建处理报告信息
                            return ShopeeFeedTaskHandleUtil.initFeedTask((feedTasktemp) -> {
                                feedTasktemp.setAssociationId(itemId);
                                feedTasktemp.setArticleNumber(logs.get(0).getSpu());
                                feedTasktemp.setTaskType(ShopeeFeedTaskEnum.END_ITEM.getValue());
                                feedTasktemp.setAccountNumber(accountNumber);
                                feedTasktemp.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                                feedTasktemp.setCreatedBy("admin");
                            });
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(FeedTask::getAssociationId, Function.identity()));
                try {
                    EsShopeeItemRequest request = new EsShopeeItemRequest();
                    request.setItemIdList(batchItemIds);
                    request.setItemSeller(accountNumber);
                    request.setIsGoods(true);
                    List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(request);
                    Map<String, List<EsShopeeItem>> itemIdAndEsShopeeItemListMap = esShopeeItems.stream().collect(Collectors.groupingBy(a -> a.getItemId(), Collectors.toList()));

                    // 记录找不到的itemId
                    List<String> notFoundItemIds = batchItemIds.stream().filter(a -> !itemIdAndEsShopeeItemListMap.containsKey(a)).collect(Collectors.toList());

                    // 处理找不到的itemId
                    for (String itemId : notFoundItemIds) {
                        // 更新日志状态为下架失败
                        List<ShopeeTaskOffLinkListingSpuLog> logs = itemIdGroupMap.get(itemId);
                        if (logs != null) {
                            for (ShopeeTaskOffLinkListingSpuLog log : logs) {
                                log.setStatus(3); // 下架失败
                                log.setRemark("在线列表不存在");
                                log.setUpdatedTime(LocalDateTime.now());
                            }
                        }
                        // 更新处理报告
                        FeedTask feedTask = feedTaskMap.get(itemId);
                        if (feedTask != null) {
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask,
                                    ResultStatusEnum.RESULT_FAIL.getStatusCode(), "在线列表不存在");
                        }
                    }

                    if (itemIdAndEsShopeeItemListMap.isEmpty()) {
                        // 批量更新
                        List<ShopeeTaskOffLinkListingSpuLog> resultList = new ArrayList<>();
                        for (String batchItemId : batchItemIds) {
                            List<ShopeeTaskOffLinkListingSpuLog> shopeeTaskOffLinkListingSpuLogs = itemIdGroupMap.get(batchItemId);
                            resultList.addAll(shopeeTaskOffLinkListingSpuLogs);
                        }
                        shopeeTaskOffLinkListingSpuLogService.updateBatchById(resultList);
                        return;
                    }
                    // 调用API执行下架
                    ResponseJson responseJson = ShopeeUnlistItemCallV2.unlistItem(saleAccountByAccountNumber, false, esShopeeItems);

                    // 处理下架结果
                    Map<String, List<ResponseError>> responseErrorMap = responseJson.getErrors().stream()
                            .collect(Collectors.groupingBy(ResponseError::getStatus));

                    // 处理失败的情况
                    if (responseErrorMap.containsKey(StatusCode.FAIL)) {
                        List<ResponseError> failList = responseErrorMap.get(StatusCode.FAIL);
                        for (ResponseError responseError : failList) {
                            // 更新日志状态为下架失败
                            String itemId = responseError.getField();
                            List<ShopeeTaskOffLinkListingSpuLog> logs = itemIdGroupMap.get(itemId);
                            if (logs != null) {
                                for (ShopeeTaskOffLinkListingSpuLog log : logs) {
                                    log.setStatus(3); // 下架失败
                                    log.setRemark(responseError.getMessage());
                                    log.setUpdatedTime(LocalDateTime.now());
                                }
                            }
                            // 更新处理报告
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(itemId),
                                    ResultStatusEnum.RESULT_FAIL.getStatusCode(), responseError.getMessage());
                        }
                    }

                    // 处理成功的情况
                    if (responseErrorMap.containsKey(StatusCode.SUCCESS)) {
                        List<ResponseError> successList = responseErrorMap.get(StatusCode.SUCCESS);
                        for (ResponseError responseError : successList) {
                            String itemId = responseError.getField();

                            // 更新ES中的商品状态
                            List<EsShopeeItem> saveList = esShopeeItems.stream()
                                    .filter(item -> item.getItemId().equals(itemId))
                                    .peek(item -> {
                                        item.setDownType("SALES_OFFLINE");
                                        item.setItemStatus(ShopeeItemStatusEnum.UNLIST.getCode());
                                    })
                                    .collect(Collectors.toList());

                            // 局部更新下架方法
                            if (!saveList.isEmpty()) {
                                esShopeeItemService.batchUpdateDocuments(saveList);
                            }

                            // 更新日志状态为已下架
                            List<ShopeeTaskOffLinkListingSpuLog> logs = itemIdGroupMap.get(itemId);
                            if (logs != null) {
                                for (ShopeeTaskOffLinkListingSpuLog log : logs) {
                                    log.setStatus(2); // 已下架
                                    log.setUpdatedTime(LocalDateTime.now());
                                }
                            }
                            // 更新处理报告
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(itemId),
                                    ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "下架成功");
                        }
                    }
                    // 批量更新
                    List<ShopeeTaskOffLinkListingSpuLog> resultList = new ArrayList<>();
                    for (String batchItemId : batchItemIds) {
                        List<ShopeeTaskOffLinkListingSpuLog> shopeeTaskOffLinkListingSpuLogs = itemIdGroupMap.get(batchItemId);
                        resultList.addAll(shopeeTaskOffLinkListingSpuLogs);
                    }
                    shopeeTaskOffLinkListingSpuLogService.updateBatchById(resultList);
                } catch (Exception e) {
                    log.error("执行下架异常：", e);
                    // 更新所有处理报告为失败
                    for (String itemId : batchItemIds) {
                        // 更新日志状态为下架失败
                        List<ShopeeTaskOffLinkListingSpuLog> logs = itemIdGroupMap.get(itemId);
                        if (logs != null) {
                            for (ShopeeTaskOffLinkListingSpuLog log : logs) {
                                log.setStatus(3); // 下架失败
                                log.setRemark("执行异常：" + e.getMessage());
                                log.setUpdatedTime(LocalDateTime.now());
                                shopeeTaskOffLinkListingSpuLogService.updateById(log);
                            }
                        }

                        // 更新处理报告
                        FeedTask feedTask = feedTaskMap.get(itemId);
                        if (feedTask != null) {
                            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask,
                                    ResultStatusEnum.RESULT_FAIL.getStatusCode(), "执行异常：" + e.getMessage());
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("ShopeeExecuteOfflineLinkItemMqListener 下架异常");
        }

        // 更新报告状态
        shopeeTaskOffLinkListingReport.setStatus(3); // 已完成
        shopeeTaskOffLinkListingReport.setUpdatedTime(LocalDateTime.now());
        shopeeTaskOffLinkListingReportService.updateById(shopeeTaskOffLinkListingReport);
    }


}
