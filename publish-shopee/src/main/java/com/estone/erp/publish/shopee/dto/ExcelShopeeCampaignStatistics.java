package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
public class ExcelShopeeCampaignStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 父活动名称
     */
    @ExcelProperty(value = "所属父活动名称")
    private String parentTitle;
    @ExcelProperty(value = "父活动ID", converter = LongStringConverter.class)
    private Long parentCampaignId;
    /**
     * 子活动名称
     */
    @ExcelProperty(value = "子活动名称")
    private String sonTitle;
    @ExcelProperty(value = "子活动ID", converter = LongStringConverter.class)
    private Long sonCampaignId;
    /**
     * 子活动报名的开始时间
     */
    @ExcelProperty(value = "子活动报名开始时间", converter = TimestampFormatConverter.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(name = "son_nomination_start_date", format = "yyyy-MM-dd HH:mm:ss")
    private Timestamp sonNominationStartDate;

    /**
     * 子活动报名的结束时间
     */
    @ExcelProperty(value = "子活动报名结束时间", converter = TimestampFormatConverter.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(name = "son_nomination_end_date", format = "yyyy-MM-dd HH:mm:ss")
    private Timestamp sonNominationEndDate;

    /**
     * 子活动开始时间
     */
    @ExcelProperty(value = "子活动进行开始时间", converter = TimestampFormatConverter.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(name = "son_start_date", format = "yyyy-MM-dd HH:mm:ss")
    private Timestamp sonStartDate;

    /**
     * 子活动结束时间
     */
    @ExcelProperty(value = "子活动进行结束时间", converter = TimestampFormatConverter.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(name = "son_end_date", format = "yyyy-MM-dd HH:mm:ss")
    private Timestamp sonEndDate;


    /**
     * 采集的店铺数
     */
    @ExcelProperty(value = "采集店铺数")
    private Integer collectStoresTotal;

    /**
     * 子活动已报名店铺数
     */
    @ExcelProperty(value = "子活动已报名店铺数")
    private Integer registerSuccessfullTotal;

    /**
     * 已报名无符合商品为是的店铺数量
     */
    @ExcelProperty(value = "无适配商品未报名成功店铺")
    private Integer registeredNotDataTotal;

    /**
     * 可报名商品数量
     */
    @ExcelProperty(value = "店铺可报名商品数量")
    private Integer sonApplyNumberTotal;

    /**
     * 已报名商品数量
     */
    @ExcelProperty(value = "已报名商品数量")
    private Integer sonSubmitNumberTotal;

    /**
     * 已拒绝商品数量
     */
    @ExcelProperty(value = "已拒绝商品数量")
    private Integer sonRejectNumberTotal;


    @ExcelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 统计日期
     */
    @ExcelProperty(value = "统计日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JSONField(name = "statistical_date", format = "yyyy-MM-dd")
    private Date statisticalDate;
}