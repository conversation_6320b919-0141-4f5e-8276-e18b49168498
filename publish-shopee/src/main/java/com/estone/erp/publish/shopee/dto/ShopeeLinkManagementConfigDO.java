package com.estone.erp.publish.shopee.dto;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.shopee.enums.ShopeeLinkManagementConfigTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeLinkManagementConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @date 2024-07-04 下午3:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ShopeeLinkManagementConfigDO extends ShopeeLinkManagementConfig {

    private static final long serialVersionUID = -916840247746453343L;

    /**
     * 调价规则
     */
    private ShopeeAdjustPriceRuleConfig adjustPriceRuleConfig;

    /**
     * 库存规则
     */
    private ShopeeAdjustInventoryRuleConfig adjustInventoryRuleConfig;

    public void checkAdjustPriceRuleConfig() {
        Integer type = this.getType();
        if (type == null) {
            throw new IllegalArgumentException("类型不能为空");
        }

        switch (type) {
            case 1: // 调价规则
                if (adjustPriceRuleConfig == null || adjustPriceRuleConfig.getAdjustPriceMethod() == null) {
                    throw new IllegalArgumentException("调价规则或方式不能为空");
                }
                break;
            case 2: // 库存规则
                if (adjustInventoryRuleConfig == null || adjustInventoryRuleConfig.getAdjustInventoryMethod() == null) {
                    throw new IllegalArgumentException("调库存规则或方式不能为空");
                }
                break;
            default:
                throw new IllegalArgumentException("不支持的类型：" + type);
        }
    }

    public static ShopeeLinkManagementConfigDO convertDO(ShopeeLinkManagementConfig config) {
        ShopeeLinkManagementConfigDO shopeeLinkManagementConfig = BeanUtil.copyProperties(config, ShopeeLinkManagementConfigDO.class);
        String rule = shopeeLinkManagementConfig.getRule();
        Integer type = shopeeLinkManagementConfig.getType();

        if (rule != null && type != null) {
            switch (type) {
                case 1: // 调价规则
                    shopeeLinkManagementConfig.setAdjustPriceRuleConfig(JSON.parseObject(rule, ShopeeAdjustPriceRuleConfig.class));
                    break;
                case 2: // 库存规则
                    shopeeLinkManagementConfig.setAdjustInventoryRuleConfig(JSON.parseObject(rule, ShopeeAdjustInventoryRuleConfig.class));
                    break;
                default:
                    // 无需处理其他类型
                    break;
            }
        }
        return shopeeLinkManagementConfig;
    }

}
