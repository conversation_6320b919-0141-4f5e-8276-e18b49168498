package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.shopee.model.ShopeeMarketingFollowPrizeCriteria;
import com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails;
import com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetailsCriteria;
import com.estone.erp.publish.shopee.service.ShopeeTaskExecutionDetailsService;

import javax.annotation.Resource;

import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeViolationQueryDO;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-10-15 18:32:54
 */
@RestController
@RequestMapping("shopeeTaskExecutionDetails")
public class ShopeeTaskExecutionDetailsController {

    @Resource
    private ShopeeTaskExecutionDetailsService shopeeTaskExecutionDetailsService;

    @PostMapping
    public ApiResult<?> postShopeeTaskExecutionDetails(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            CQuery<ShopeeTaskExecutionDetailsCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeTaskExecutionDetailsCriteria>>() {
            });
            switch (method) {
                case "searchShopeeTaskExecutionDetails": {
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    return shopeeTaskExecutionDetailsService.search(cquery);
                }
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("download")
    public ApiResult<String> download(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<ShopeeTaskExecutionDetailsCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeTaskExecutionDetailsCriteria>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        return shopeeTaskExecutionDetailsService.download(cquery);
    }

    @GetMapping("getRuleNameList")
    public ApiResult<?> getRuleNameList() {
        return shopeeTaskExecutionDetailsService.getRuleNameList();
    }

}