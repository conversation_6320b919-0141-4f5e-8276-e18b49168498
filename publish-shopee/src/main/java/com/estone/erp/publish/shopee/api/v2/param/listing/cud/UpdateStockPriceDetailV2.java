package com.estone.erp.publish.shopee.api.v2.param.listing.cud;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/26 9:58
 * @description
 */
@Getter
@Setter
public class UpdateStockPriceDetailV2 {

    /** 如果是变体必填*/
    @JSONField(name = "model_id")
    private Long modelId;

    /** 库存 */
//    @JSONField(name = "normal_stock")
//    private Integer normalStock;

    /** 库存 */
    @JSONField(name = "seller_stock")
    private List<UpdateSellerStockV2> sellerStockV2;

    /** 价格 */
    @JSONField(name = "original_price")
    private Double originalPrice;

}
