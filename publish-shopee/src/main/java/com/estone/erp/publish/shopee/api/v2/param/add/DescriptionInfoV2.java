package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 扩展描述
 */
@Data
public class DescriptionInfoV2 {

    @JSONField(name = "extended_description")
    private ExtendInfo extendedDescription;

    @Data
    public static class ExtendInfo {

        /**
         * 字段描述
         */
        @JSONField(name = "field_list")
        private List<FiledInfo> fieldList;

    }

    @Data
    public static class FiledInfo {
        /**
         * 字段类型，可选值有：text、image
         */
        @JSONField(name = "field_type")
        private String fieldType;
        /**
         * 文本
         */
        @JSONField(name = "text")
        private String text;
        /**
         *
         */
        @J<PERSON>NField(name = "image_info")
        private DescriptionInfoImage imageInfo;
    }

    @Data
    public static class DescriptionInfoImage {

        @JSONField(name = "image_url")
        private String imageUrl;

        @JSONField(name = "image_id")
        private String imageId;
    }
}
