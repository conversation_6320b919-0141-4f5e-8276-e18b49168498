package com.estone.erp.publish.shopee.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.platform.enums.ShopeeSkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.discount.ShopeeGetDiscountV2;
import com.estone.erp.publish.shopee.call.model.ShopeeDiscountItemInfo;
import com.estone.erp.publish.shopee.call.v2.ShopeeDiscountCallV2;
import com.estone.erp.publish.shopee.enums.ShopeeDiscountCreatedTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeDiscountStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeDiscountItemService;
import com.estone.erp.publish.shopee.service.ShopeeDiscountService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.shopee.util.ShopeeCalcPriceUtil;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ShopeeDiscountHandler {

    @Resource
    private ShopeeDiscountItemService shopeeDiscountItemService;
    @Resource
    private ShopeeDiscountService shopeeDiscountService;
    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;
    @Resource
    private FeedTaskService feedTaskService;
    /**
     * 构建折扣
     * <p>
     * taskId 可以为空
     *
     * @param esShopeeItems 商品列表
     * @param shopeeAccountConfig 账号配置
     * @param logisticCode 物流code
     * @param onePromotionMaximunItems 单个促销最大商品数量
     * @param shopeeConfigTask 任务配置
     * @return
     */
    public void exec(List<EsShopeeItem> esShopeeItems, ShopeeAccountConfig shopeeAccountConfig, String logisticCode, int onePromotionMaximunItems, ShopeeConfigTask shopeeConfigTask, Boolean itemDiscountRate) {
        // 补全物流代码
        if (StringUtils.isBlank(logisticCode)) {
            try {
                Map<String, String> map = shopeeLogisticHandleService.selectLogistic(shopeeAccountConfig.getAccount());
                logisticCode = map.get(shopeeAccountConfig.getSite());
            } catch (Exception e) {
                log.error("获取物流信息失败: {}", e.getMessage());
                return;
            }
        }

        String configName = null;
        Integer configId = null;
        Integer taskId = null;
        if (shopeeConfigTask != null) {
            configName = shopeeConfigTask.getConfigName();
            configId = shopeeConfigTask.getConfigId();
            taskId = shopeeConfigTask.getId();
        }
        String marketingName = configName;
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return;
        }
        String accountNumber = shopeeAccountConfig.getAccount();

        Map<String, String> errorSkuAndItemIdMap = new HashMap<>();
        Map<String, String> errorSkuAndErrorMsgMap = new HashMap<>();
        List<List<ShopeeDiscountItem>> shopeeDiscountItemsList = buildShopeeDiscountItemMap(esShopeeItems, shopeeAccountConfig, logisticCode,
                errorSkuAndItemIdMap, errorSkuAndErrorMsgMap, itemDiscountRate);
        if (MapUtils.isNotEmpty(errorSkuAndItemIdMap)) {
            Map<String, Double> itemDiscountRateMap = esShopeeItems.stream().collect(Collectors.toMap(a -> a.getItemId() + "_" + a.getArticleNumber(), a -> a.getDiscountRate(), (a, b) -> a));

            for (Map.Entry<String, String> stringStringEntry : errorSkuAndItemIdMap.entrySet()) {
                String sku = stringStringEntry.getKey();
                String itemId = stringStringEntry.getValue();
                Double discountRate = itemDiscountRateMap.get(itemId + "_" + sku);
                ShopeeFeedTaskHandleUtil.insertFinishFeedTask((feedTask) -> {
                    feedTask.setTaskType(ShopeeFeedTaskEnum.ADD_DISCOUNT_ITEM.getValue());
                    feedTask.setAccountNumber(accountNumber);
                    feedTask.setAttribute1(marketingName);
                    feedTask.setAttribute2(itemId);
                    feedTask.setArticleNumber(sku);
                    feedTask.setAttribute4(Objects.isNull(discountRate) ? "" : discountRate.toString());
                    feedTask.setAttribute3("add");
                    feedTask.setCreatedBy("admin");
                    feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                    feedTask.setResultMsg("折扣价格计算失败：" + errorSkuAndErrorMsgMap.get(sku));
                });
            }
        }

        if (CollectionUtils.isEmpty(shopeeDiscountItemsList)) {
            if (taskId != null) {
                shopeeConfigTaskService.failTask(taskId, "折扣价格计算失败");
            }
            return;
        }

        int createDiscountItemSize = shopeeDiscountItemsList.size();

        Date endDdate = DateUtils.getDateEnd(7);
        // 查找账号未过期的折扣 且过期时间大于未来7天时间
        ShopeeDiscountExample discountExample = new ShopeeDiscountExample();
        discountExample.setOrderByClause("end_time desc");
        discountExample.createCriteria()
                .andAccountEqualTo(accountNumber)
                // 确保活动是存在的
                .andDiscountIdGreaterThan(0L)
                .andStatusNotEqualTo(ShopeeDiscountStatusEnum.expired.name())
                .andEndTimeGreaterThan(new Timestamp(endDdate.getTime()))
                .andCreatedTypeEqualTo(ShopeeDiscountCreatedTypeEnum.SYSTEM.getCode());
        List<ShopeeDiscount> dbShopeeDiscounts = shopeeDiscountService.selectByExample(discountExample);

        List<Integer> idList = dbShopeeDiscounts.stream().map(ShopeeDiscount::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            List<ShopeeDiscount> itemList = shopeeDiscountItemService.selectItemTotalByAssociateIds(idList);
            if (CollectionUtils.isNotEmpty(itemList)) {
                Map<Integer, Long> associateMap = itemList.stream().collect(Collectors.toMap(ShopeeDiscount::getId, ShopeeDiscount::getItemTotal));
                Map<Integer, Long> associateSuccessItemMap = itemList.stream().collect(Collectors.toMap(ShopeeDiscount::getId, ShopeeDiscount::getItemSuccessTotal));
                dbShopeeDiscounts.parallelStream().forEach(bean -> bean.setItemTotal(associateMap.get(bean.getId())));
                dbShopeeDiscounts.parallelStream().forEach(bean -> bean.setItemSuccessTotal(associateSuccessItemMap.get(bean.getId())));
            }
        }

        int fromIndex = 0;
        // 往就折扣添加数据 至最大值
        for (ShopeeDiscount dbShopeeDiscount : dbShopeeDiscounts) {
            Long dbDiscountItemSize = dbShopeeDiscount.getItemSuccessTotal();
            dbDiscountItemSize = dbDiscountItemSize == null ? 0 : dbDiscountItemSize;

            // 促销最多可以上传1000个item
            if (dbDiscountItemSize >= onePromotionMaximunItems) {
                continue;
            }
            // 折扣满了970 添加折扣不能一次添加完 不用旧折扣
            if (dbDiscountItemSize >= 970 && createDiscountItemSize + dbDiscountItemSize > onePromotionMaximunItems) {
                continue;
            }

            // size:截取的item数量 fromIndex:原集合开始截取的位置 toIndex:原结合截取的最后位置
            int size = (int) (onePromotionMaximunItems - dbDiscountItemSize);
            int toIndex = fromIndex + size;
            toIndex = Math.min(toIndex, createDiscountItemSize);
            List<List<ShopeeDiscountItem>> currentDiscountItemsList = shopeeDiscountItemsList.subList(fromIndex, toIndex);

            List<ShopeeDiscountItem> currentDiscountItems = new ArrayList<>();
            currentDiscountItemsList.forEach(currentDiscountItems::addAll);

            dbShopeeDiscount.setDiscountItemList(currentDiscountItems);
            shopeeDiscountService.incrementDiscountItem(dbShopeeDiscount, shopeeConfigTask, StrConstant.ADMIN);

            fromIndex = toIndex;
            if (fromIndex >= createDiscountItemSize) {
                break;
            }
        }

        // 添加到旧折扣之后 剩余需要新建折扣
        List<List<ShopeeDiscountItem>> newDiscountItemsList = shopeeDiscountItemsList.subList(fromIndex, createDiscountItemSize);
        if (CollectionUtils.isEmpty(newDiscountItemsList)) {
            if (taskId != null) {
                shopeeConfigTaskService.successTask(taskId, "已全部添加进旧活动");
            }
            return;
        }

        int i = 0;
        // 创建折扣使用最大数量 分页
        List<List<List<ShopeeDiscountItem>>> newDiscountItemsListPages = PagingUtils.newPagingList(newDiscountItemsList, onePromotionMaximunItems);
        Map<String, ApiResult<?>> map = new HashMap<>();

        for (List<List<ShopeeDiscountItem>> newDiscountItemsListPage : newDiscountItemsListPages) {
            List<ShopeeDiscountItem> currentDiscountItems = new ArrayList<>();
            newDiscountItemsListPage.forEach(currentDiscountItems::addAll);

            // 创建新的折扣
            ShopeeDiscount newShopeeDiscount = this.newShopeeDiscount(accountNumber, i++, configId);
            newShopeeDiscount.setDiscountItemList(currentDiscountItems);
            ApiResult<Object> objectApiResult = shopeeDiscountService.addDiscount(newShopeeDiscount, StrConstant.ADMIN, marketingName, ShopeeDiscountCreatedTypeEnum.SYSTEM);
            map.put(newShopeeDiscount.getDiscountName(), objectApiResult);
        }

        boolean success = true;
        StringJoiner stringJoiner = new StringJoiner(",");

        for (Map.Entry<String, ApiResult<?>> entry : map.entrySet()) {
            String discountName = entry.getKey();
            ApiResult<?> apiResult = entry.getValue();
            if (apiResult == null) {
                success = false;
                stringJoiner.add(discountName + " 创建失败, 结果为空");
                continue;
            }
            if (apiResult.isSuccess()) {
                stringJoiner.add(discountName + " 创建成功");
            } else {
                success = false;
                stringJoiner.add(discountName + " 创建失败, " + apiResult.getErrorMsg());
            }
        }

        if (taskId != null) {
            if (success) {
                shopeeConfigTaskService.successTask(taskId, stringJoiner.toString());
            } else {
                shopeeConfigTaskService.failTask(taskId, stringJoiner.toString());
            }
        }
    }

    /**
     * 按规则设置新的折扣
     *
     * @param account 账号
     * @param i 编号
     * @param marketingId 任务配置id
     * @return 折扣活动对下
     */
    private ShopeeDiscount newShopeeDiscount(String account, int i, Integer marketingId) {
        // 开始时间为创建折扣时间+(1-2)小时随机 向后面的时间取整数 api限制请求时间一小时以后
        // 结束时间 开始时间+(165-175)随机天数（结束时间设置在9：00-18：00范围）
        Timestamp startTime = getStartTime();
        Timestamp endTime = getEndTime(startTime);
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyyMMdd");
        String date = simpleDateFormat1.format(new Date());

        String discountName = account + date + (0 == i ? "" : "_" + i);

        ShopeeDiscount shopeeDiscount = new ShopeeDiscount();
        shopeeDiscount.setAccount(account);
        shopeeDiscount.setDiscountName(discountName);
        shopeeDiscount.setStartTime(startTime);
        shopeeDiscount.setEndTime(endTime);
        shopeeDiscount.setMarketingId(marketingId);
        return shopeeDiscount;
    }

    /**
     * 构建折扣item对象
     *
     * @param esShopeeItems 商品列表
     * @param shopeeAccountConfig 账号配置
     * @param logisticCode 物流code
     * @return 折扣item列表
     */
    private List<List<ShopeeDiscountItem>> buildShopeeDiscountItemMap(List<EsShopeeItem> esShopeeItems, ShopeeAccountConfig shopeeAccountConfig, String logisticCode,
                                                                      Map<String, String> errorSkuMap, Map<String, String> skuAndErrorMsgMap, Boolean itemDiscountRate) {
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return null;
        }

        // 分组试卖
        Map<Boolean, List<EsShopeeItem>> groupedItems = esShopeeItems.stream()
                .collect(Collectors.partitioningBy(
                        item -> ShopeeSkuDataSourceEnum.ERP_DATA_SYSTEM.getCode().equals(item.getSkuDataSource()) ||
                                ShopeeSkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode().equals(item.getSkuDataSource())
                ));
        Double discountProfitRate = shopeeAccountConfig.getDiscountProfit();
        Map<String, String> errorIdAndErrorMsgMap = new HashMap<>();
        Map<String, Double> priceMap = new HashMap<>();
        // 非试卖商品（调用试算器）
        List<EsShopeeItem> shopeeItemList = groupedItems.getOrDefault(false, new ArrayList<>());
        if (CollectionUtils.isNotEmpty(shopeeItemList)) {
            ApiResult<Map<String, Double>> calcResult = ShopeeCalcPriceUtil.calcPrice(shopeeItemList, logisticCode, discountProfitRate, errorIdAndErrorMsgMap, itemDiscountRate);
            priceMap = calcResult.getResult();
        }

        // 试卖（原价 * （1-折扣率））
        for (EsShopeeItem item : groupedItems.getOrDefault(true, new ArrayList<>())) {
            Double originalPrice = item.getOriginalPrice();
            BigDecimal multiply = BigDecimal.valueOf((originalPrice)).multiply(BigDecimal.valueOf(1).subtract(BigDecimal.valueOf(item.getDiscountRate())));
            priceMap.put(item.getId(), multiply.setScale(2, RoundingMode.HALF_UP).doubleValue());
        }

        // 组装折扣详情数据
        List<ShopeeDiscountItem> discountItemList = new ArrayList<>();
        for (EsShopeeItem esShopeeItem : esShopeeItems) {
            try {
                ShopeeDiscountItem shopeeDiscountItem = new ShopeeDiscountItem();
                shopeeDiscountItem.setItemId(Long.valueOf(esShopeeItem.getItemId()));
                String variationId = esShopeeItem.getVariationId();
                if (StringUtils.isNotBlank(variationId)) {
                    shopeeDiscountItem.setModelId(Long.valueOf(variationId));
                }
                shopeeDiscountItem.setArticleNumber(esShopeeItem.getArticleNumber());
                shopeeDiscountItem.setPurchaseLimit(0);
                shopeeDiscountItem.setItemOriginalPrice(esShopeeItem.getOriginalPrice());

                String id = esShopeeItem.getId();
                if (MapUtils.isEmpty(priceMap) || !priceMap.containsKey(id)) {
                    String errorMsg = errorIdAndErrorMsgMap.get(esShopeeItem.getId());
                    skuAndErrorMsgMap.put(esShopeeItem.getArticleNumber(), errorMsg);
                    errorSkuMap.put(esShopeeItem.getArticleNumber(), esShopeeItem.getItemId());
                    continue;
                }
                Double promotionPrice = priceMap.get(id);
                shopeeDiscountItem.setItemPromotionPrice(NumberUtils.round(promotionPrice, 2));
                discountItemList.add(shopeeDiscountItem);
            } catch (Exception e) {
                errorSkuMap.put(esShopeeItem.getArticleNumber(), esShopeeItem.getItemId());
                log.error("buildShopeeDiscountItemMap error, sku:{}, itemId:{}, error:{}", esShopeeItem.getArticleNumber(), esShopeeItem.getItemId(), e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(discountItemList)) {
            return null;
        }

        Map<Long, List<ShopeeDiscountItem>> collect = discountItemList.stream().collect(Collectors.groupingBy(ShopeeDiscountItem::getItemId));
        return new ArrayList<>(collect.values());
    }

    /**
     * 开始时间为创建折扣时间+(1-2)小时随机 向后面的时间取整数 api限制请求时间一小时以后
     *
     * @return 开始时间
     */
    private Timestamp getStartTime() {
        int a = RandomUtils.nextInt(1, 3);
        Date startDate = DateUtils.getAfterHourDate(new Date(), a);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
//        calendar.add(Calendar.HOUR_OF_DAY, 1);
//        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 结束时间 开始时间+(165-175)随机天数（结束时间设置在9：00-18：00范围）
     *
     * @param startTime 开始时间
     * @return 结束时间
     */
    private Timestamp getEndTime(Timestamp startTime) {
        Date endTime = DateUtils.getAfterDate(startTime, (int) (Math.random() * 10) + 165);

        // [0,10) + 9 取整数为 [9-18]
        int hour = (int) (Math.random() * 10) + 9;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return new Timestamp(calendar.getTimeInMillis());
    }

    public void syncDiscountItem(ShopeeDiscount shopeeDiscount, FeedTask feedTask, String operator) {
        try {
            String accountNumber = shopeeDiscount.getAccount();
            Long discountId = shopeeDiscount.getDiscountId();
            Integer id = shopeeDiscount.getId();
            String account = shopeeDiscount.getAccount();
            if (id != null) {
                feedTask.setAssociationId(id.toString());
            }
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account);
            ShopeeGetDiscountV2 discountParam = new ShopeeGetDiscountV2();
            discountParam.setDiscountId(discountId);
            discountParam.setPageSize(100);
            int pageNo = 1;
            StringBuilder stringBuilder = new StringBuilder();
            Boolean more = true;
            List<ShopeeDiscountItem> allDiscountItems = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            boolean deleteFlag = true;
            while (BooleanUtils.isTrue(more)) {
                discountParam.setPageNo(pageNo++);
                ShopeeResponse response = ShopeeDiscountCallV2.getDiscount(saleAccountByAccountNumber, discountParam);
                String error = response.getError();
                if (StringUtils.isNotBlank(error)) {
                    log.error(account + "获取折扣商品失败, 错误信息: " + error);
                    stringBuilder.append(error);
                    deleteFlag = false;
                    break;
                }
                String result = response.getResponse();
                JSONObject json = JSON.parseObject(result);
                more = json.getBoolean("more");

                JSONArray itemList = json.getJSONArray("item_list");
                if (CollectionUtils.isEmpty(itemList)) {
                    continue;
                }
                List<ShopeeDiscountItemInfo> itemInfoList = itemList.toJavaList(ShopeeDiscountItemInfo.class);

                for (ShopeeDiscountItemInfo shopeeDiscountItemInfo : itemInfoList) {
                    ShopeeDiscountItem shopeeDiscountItem = new ShopeeDiscountItem();
                    shopeeDiscountItem.setAssociateId(id);
                    shopeeDiscountItem.setDiscountId(discountId);

                    shopeeDiscountItem.setItemId(shopeeDiscountItemInfo.getItemId());
                    shopeeDiscountItem.setNormalStock(shopeeDiscountItemInfo.getNormalStock());
                    shopeeDiscountItem.setItemPromotionStock(shopeeDiscountItemInfo.getItemPromotionStock());
                    shopeeDiscountItem.setUploadSuccess(true);
                    shopeeDiscountItem.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                    shopeeDiscountItem.setItemInflatedPriceOfOriginalPrice(shopeeDiscountItemInfo.getItemInflatedPriceOfOriginalPrice());
                    shopeeDiscountItem.setItemInflatedPriceOfPromotionPrice(shopeeDiscountItemInfo.getItemInflatedPriceOfPromotionPrice());
                    shopeeDiscountItem.setPurchaseLimit(shopeeDiscountItemInfo.getPurchaseLimit());
                    shopeeDiscountItem.setItemPromotionPrice(shopeeDiscountItemInfo.getItemPromotionPrice());
                    shopeeDiscountItem.setItemOriginalPrice(shopeeDiscountItemInfo.getItemOriginalPrice());

                    List<ShopeeDiscountItemInfo.ModelInfo> modelList = shopeeDiscountItemInfo.getModelList();
                    if (CollectionUtils.isEmpty(modelList)) {
                        allDiscountItems.add(shopeeDiscountItem);
                    } else {
                        for (ShopeeDiscountItemInfo.ModelInfo modelInfo : modelList) {
                            ShopeeDiscountItem modelDiscountItem = BeanUtil.copyProperties(shopeeDiscountItem, ShopeeDiscountItem.class);
                            modelDiscountItem.setNormalStock(modelInfo.getModelNormalStock());
                            modelDiscountItem.setItemPromotionStock(modelInfo.getModelPromotionStock());
                            modelDiscountItem.setItemOriginalPrice(modelInfo.getModelOriginalPrice());
                            modelDiscountItem.setItemPromotionPrice(modelInfo.getModelPromotionPrice());
                            modelDiscountItem.setItemInflatedPriceOfPromotionPrice(modelInfo.getModelInflatedPriceOfPromotionPrice());
                            modelDiscountItem.setItemInflatedPriceOfOriginalPrice(modelInfo.getModelInflatedPriceOfOriginalPrice());
                            modelDiscountItem.setModelId(modelInfo.getModelId());

                            allDiscountItems.add(modelDiscountItem);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(allDiscountItems)) {
                shopeeDiscountItemService.batchSaveOrUpdate(account, allDiscountItems);
                List<FeedTask> feedTasksList = new ArrayList<>();
                for (ShopeeDiscountItem shopeeDiscountItem : allDiscountItems) {
                    String articleNumber = shopeeDiscountItem.getArticleNumber();
                    FeedTask itemFeedTask = new FeedTask();
                    itemFeedTask.setPlatform(Platform.Shopee.name());
                    itemFeedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    itemFeedTask.setTableIndex();
                    itemFeedTask.setAccountNumber(accountNumber);
                    itemFeedTask.setAssociationId(shopeeDiscount.getId().toString());
                    itemFeedTask.setTaskType(ShopeeFeedTaskEnum.SYNC_DISCOUNT_ITEM.getValue());
                    itemFeedTask.setCreatedBy(operator);
                    itemFeedTask.setAttribute3("同步折扣活动");
                    itemFeedTask.setArticleNumber(articleNumber);
                    itemFeedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    itemFeedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    itemFeedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    feedTasksList.add(itemFeedTask);
                }
                if (CollectionUtils.isNotEmpty(feedTasksList)) {
                    List<List<FeedTask>> lists = PagingUtils.newPagingList(feedTasksList, 100);
                    for (List<FeedTask> list : lists) {
                        feedTaskService.batchInsertSelective(list, list.get(0).getTableIndex());
                    }
                }
            }
            if (deleteFlag) {
                // 删除同步不到的数据
                shopeeDiscountItemService.deleteExpiredDate(shopeeDiscount.getId(), now.plusMinutes(-1));
            }
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "成功");
        } catch (Exception e) {
            log.error("同步折扣商品失败:" + e.getMessage());
            try {
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "同步报错：" + e.getMessage());
            } catch (Exception ex) {
                log.error("更新任务失败", ex);
            }
        }
    }

}
