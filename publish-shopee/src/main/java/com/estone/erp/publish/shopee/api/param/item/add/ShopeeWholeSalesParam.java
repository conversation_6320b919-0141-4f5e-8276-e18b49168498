package com.estone.erp.publish.shopee.api.param.item.add;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 批发数量和价格
 *
 * <AUTHOR>
 */
public class ShopeeWholeSalesParam {

    /** 批发最低数量 */
    @JSONField(name = "min")
    private Integer min;

    /** 批发最高数量 */
    @JSONField(name = "max")
    private Integer max;

    /** 批发单价 */
    @JSONField(name = "unit_price")
    private Double unitPrice;

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }
}
