package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/12 12:06
 * @description 添加折扣
 */
@Getter
@Setter
public class ShopeeAddDiscountV2 implements RequestCommon {

    /** 折扣的标题 */
    @JSONField(name = "discount_name")
    private String discountName;

    /** 折扣活动开始的时间。开始时间必须比当前时间晚1小时 */
    @JSONField(name = "start_time")
    private Long startTime;

    /** 折扣活动结束的时间。结束时间必须晚于当前时间 */
    @JSONField(name = "end_time")
    private Long endTime;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_DISCOUNT;
    }
}
