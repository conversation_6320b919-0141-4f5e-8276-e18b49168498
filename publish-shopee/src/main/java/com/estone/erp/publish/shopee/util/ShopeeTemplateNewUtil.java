package com.estone.erp.publish.shopee.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.enums.ShopeeTemplateTableEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.shopee.service.ShopeePublishSkuService;
import com.estone.erp.publish.shopee.service.ShopeeTemplateNewService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/19 17:07
 * @description
 */
@Slf4j
@Deprecated
public class ShopeeTemplateNewUtil {

    static PmsSkuService pmsSkuService = SpringUtils.getBean(PmsSkuService.class);
    static ShopeePublishSkuService shopeePublishSkuService = SpringUtils.getBean(ShopeePublishSkuService.class);
    static ShopeeTemplateNewService shopeeTemplateNewService = SpringUtils.getBean(ShopeeTemplateNewService.class);
    static SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
    static InfringementWordService infringementWordService = SpringUtils.getBean(InfringementWordService.class);

    /**
     * 验证参数
     * @param template
     * @return
     */
    public static ApiResult<?> checkTemplateParam(ShopeeTemplateNew template) {
        if(template == null){
            return ApiResult.newError("参数为空！");
        }
        List<String> msg = new ArrayList<>();
        if(StringUtils.isBlank(template.getAccountNumber())){
            msg.add("店铺为空！");
        }
        if(StringUtils.isBlank(template.getSite())){
            msg.add("站点为空！");
        }
        if(StringUtils.isBlank(template.getSku())){
            msg.add("货号为空！");
        }
        if(StringUtils.isBlank(template.getName())){
            msg.add("标题为空！");
        }
        //台湾不判断关键词
//        if(!ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(template.getSite())){
//            if(StringUtils.isBlank(template.getKeyword())){
//                msg.add("关键词为空！");
//            }
//        }
        if(template.getDaysToShip() == null || template.getDaysToShip() <= 0){
            msg.add("发货天数必须 > 0");
        }
        if(template.getCategoryId() == null){
            msg.add("类目id为空");
        }
        if(StringUtils.isBlank(template.getImagesStr())){
            msg.add("图片为空！");
        }
        if(StringUtils.isBlank(template.getDescription())){
            msg.add("描述为空！");
        }
        if(StringUtils.isBlank(template.getLogisticsStr())){
            msg.add("物流方式为空！");
        }
        if(StringUtils.isBlank(template.getShopeeSkusStr())){
            msg.add("多属性为空！");
        }
        // 自动刊登校验错误信息 例如台湾站翻译失败
        if(StringUtils.isNotBlank(template.getMessage())) {
            msg.add(template.getMessage());
        }

        if(msg.size() >0){
            return ApiResult.newError(msg.toString());
        }

        return ApiResult.newSuccess();
    }

    /**
     * 验证是否满足刊登条件
     *
     * @return
     */
    public static ApiResult<?> checkPublishCondition(boolean checkOther,
                                                     ShopeeTemplateNew templateNew,
                                                     SaleAccountAndBusinessResponse shopeeAccount) {
        // 过滤侵权词
        delInfringingWords(templateNew);

        List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(templateNew.getShopeeSkusStr(), ShopeeSku.class);
        List<String> skuList = shopeeSkuList.stream().map(ShopeeSku::getSku).collect(Collectors.toList());
        String templateSite = templateNew.getSite();

        if(CollectionUtils.isNotEmpty(shopeeSkuList) && CollectionUtils.isNotEmpty(skuList) && StringUtils.isNotBlank(templateSite)) {
            String [] fields = {"mainSku", "sonSku", "itemStatus", "salesProhibition"};
            SingleItemEsRequest criteria = new SingleItemEsRequest();
            criteria.setSkuList(skuList);
            criteria.setFields(fields );
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
            List<ProductInfo> productInfoList =  SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
            if(CollectionUtils.isNotEmpty(productInfoList)) {
                // 过滤 停产 存档 废弃状态
                shopeeSkuList = ShopeeProductInfoUtil.filterIllegalStatus(shopeeAccount, shopeeSkuList, productInfoList);

                // 过滤当前站点禁售
                shopeeSkuList = ShopeeProductInfoUtil.filterForbidden(shopeeSkuList, productInfoList, Arrays.asList(templateSite));
            }

            // 过滤侵权
            try{
                shopeeSkuList = ShopeeProductInfoUtil.filterInfringement(shopeeSkuList, Arrays.asList(templateSite));
            } catch (Exception e) {
                return ApiResult.newError(e.getMessage());
            }

            if(CollectionUtils.isEmpty(shopeeSkuList)) {
                return ApiResult.newError("所有子SKU侵权,禁售或状态非法！");
            }
        }

        templateNew.setShopeeSkusStr(JSON.toJSONString(shopeeSkuList));
        if(!checkOther){
            return ApiResult.newSuccess();
        }

        //校验店铺站点与模板站点是否一致
        if(!StringUtils.equals(shopeeAccount.getAccountSite(), templateSite)){
            String errorMsg = String.format("店铺站点(%s)与模板站点(%s)不一致", shopeeAccount.getAccountSite(), templateSite);
            return ApiResult.newError(errorMsg);
        }

        // 泰国站点禁止刊登分类 101809 Home & Living Tools & Home Improvement Tools >> Power Welding Tools
        if(ShopeeCountryEnum.Thailand.getCode().equalsIgnoreCase(templateNew.getSite()) && "101809".equals(templateNew.getCategoryId().toString())) {
            String errorMsg = String.format("该分类平台禁止刊登，请重新选择分类!");
            return ApiResult.newError(errorMsg);
        }

        // 新系统校验sku是否当前店铺刊登过
        boolean isSkuHavePublished = shopeePublishSkuService.isSkuHavePublished(shopeeAccount.getAccountNumber(), templateNew.getSku());
        if (isSkuHavePublished) {
            String errorMsg = "此账号已经刊登过商品" + templateNew.getSku() + ",请勿重复刊登！";
            return ApiResult.newError(errorMsg);
        }

        //校验是否没有图片（这种情况大概率是产品系统的图片池接口挂了）
        if(StringUtils.isEmpty(templateNew.getImagesStr())) {
            String errorMsg = "此模板没有设置图片，请设置完再进行刊登";
            return ApiResult.newError(errorMsg);
        }

        //看看图片池里面是否有不能访问的图片（路径包括www.winddeal.net就是不能访问）
        if(templateNew.getImagesStr().contains("www.winddeal.net")){
            String errorMsg = "产品图片有失效图片（以www.winddeal.net开头的图片），请重新选择图片再进行刊登";
            return ApiResult.newError(errorMsg);
        }

        //如果刊登的平台不是TW站点，但是标题是中文，那么禁止刊登，并生成处理报告
        if(!ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(shopeeAccount.getAccountSite())) {
            if(checkContainChinese(templateNew.getName())) {
                String errorMsg = "非台湾站点的标题不能包含中文，请修改标题后再刊登";
                return ApiResult.newError(errorMsg);
            }
        }

        //多属性sku 验证color size不能同时为空
        if(templateNew.getShopeeSkusStr() != null){
            List<ShopeeSku> shopeeSkus = JSON.parseObject(templateNew.getShopeeSkusStr(), new TypeReference<List<ShopeeSku>>() {
            });
            if(CollectionUtils.isNotEmpty(shopeeSkus)){
                if(shopeeSkus.size() > 1){
                    List<String> failSku = shopeeSkus.stream().filter(o -> StringUtils.isBlank(o.getColor()) && StringUtils.isBlank(o.getSize()))
                            .map(o -> o.getSku())
                            .collect(Collectors.toList());
                    if(failSku.size() > 0){
                        return ApiResult.newError(String.format("多属性sku%s color size不能同时为空！", failSku));
                    }
                    failSku = shopeeSkus.stream().filter(o -> StringUtils.isBlank(o.getColor()))
                            .map(o -> o.getSku())
                            .collect(Collectors.toList());
                    if(failSku.size() > 0 && failSku.size() != shopeeSkus.size()){
                        return ApiResult.newError(String.format("多属性sku%s color 不能为空！", failSku));
                    }
                    failSku = shopeeSkus.stream().filter(o -> StringUtils.isBlank(o.getSize()))
                            .map(o -> o.getSku())
                            .collect(Collectors.toList());
                    if(failSku.size() > 0 && failSku.size() != shopeeSkus.size()){
                        return ApiResult.newError(String.format("多属性sku%s size 不能为空！", failSku));
                    }
                }else if(shopeeSkus.size() == 1 && !templateNew.getSku().equalsIgnoreCase(shopeeSkus.get(0).getSku())){
                    //主sku 和 子sku 不一致 -> 变体刊登单个sku，也要验证color size
                    List<String> failSku = shopeeSkus.stream().filter(o -> StringUtils.isBlank(o.getColor()) && StringUtils.isBlank(o.getSize()))
                            .map(o -> o.getSku())
                            .collect(Collectors.toList());
                    if(failSku.size() > 0){
                        return ApiResult.newError(String.format("多属性单个sku%s color size不能同时为空！", failSku));
                    }
                }
            }
        }

        return ApiResult.newSuccess();
    }

    /**
     * 店铺配置校验
     * @param templateNew
     * @param accountConfig
     * @return
     */
    public static ApiResult<?> checkAccountConfig(ShopeeTemplateNew templateNew, ShopeeAccountConfig accountConfig) {
        if(null == accountConfig) {
            return ApiResult.newError("店铺配置校验 店铺配置为空！");
        }

        // 不可刊登sku 过滤校验
        List<String> nonPublishableSkuList = CommonUtils.splitList(accountConfig.getNonPublishableSku(), ",");
        if(CollectionUtils.isNotEmpty(nonPublishableSkuList)) {
            List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(templateNew.getShopeeSkusStr(), ShopeeSku.class);

            try{
                shopeeSkuList = ShopeeProductInfoUtil.filterNonPublishableSku(shopeeSkuList, templateNew.getSku(), nonPublishableSkuList);
                templateNew.setShopeeSkusStr(JSON.toJSONString(shopeeSkuList));
            }catch (Exception e) {
                return ApiResult.newError(e.getMessage());
            }
        }

        return ApiResult.newSuccess();
    }

    /**
     * 过滤标题 关键词 描述 侵权词汇
     * @param templateNew
     */
    private static void delInfringingWords(ShopeeTemplateNew templateNew) {
        String site = templateNew.getSite();
        // 侵权词台湾站跳过
        if(ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(site)) {
            return;
        }

        List<String> replaceList = new ArrayList<>(3);
        String name = templateNew.getName();
        replaceList.add(StringUtils.isEmpty(name) ? "" : name);

        String keyword = templateNew.getKeyword();
        replaceList.add(StringUtils.isEmpty(keyword) ? "" : keyword);

        String description = templateNew.getDescription();
        replaceList.add(StringUtils.isEmpty(description) ? "" : description);

        // 过滤侵权词 不包括违禁词 类型 目前有 侵权，违规
        infringementWordService.delInfringingWords(replaceList, SaleChannel.CHANNEL_SHOPEE, templateNew.getSite(), null);

        // 过滤侵权词 违禁词 类型
        infringementWordService.delInfringingWords(replaceList, SaleChannel.CHANNEL_SHOPEE, templateNew.getSite(), StrConstant.INFRINGING_FORBIDDEN_WORD);

        templateNew.setName(replaceList.get(0));
        templateNew.setKeyword(replaceList.get(1));
        templateNew.setDescription(replaceList.get(2));
    }

    //检查是否包含中文
    public static boolean checkContainChinese(String str){
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }

    public static String getShopeeTemplateTable(Boolean isParent) {
        String table = null;
        if(BooleanUtils.isTrue(isParent)) {
            table = ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_MODEL.getCode();
        } else {
            table = ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode();
        }
        return table;
    }

    public static String getShopeeTemplateTable(int isParent) {
        String table = null;
        if(isParent == 1) {
            table = ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_MODEL.getCode();
        } else {
            table = ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode();
        }
        return table;
    }
}
