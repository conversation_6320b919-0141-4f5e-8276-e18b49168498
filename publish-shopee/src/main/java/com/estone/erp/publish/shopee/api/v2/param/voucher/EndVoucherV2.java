package com.estone.erp.publish.shopee.api.v2.param.voucher;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * 结束折扣
 */
@Data
public class EndVoucherV2 implements RequestCommon {
    /**
     * 优惠券id
     */
    @JSONField(name = "voucher_id")
    private Long voucher_id;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.END_VOUCHER;
    }
}
