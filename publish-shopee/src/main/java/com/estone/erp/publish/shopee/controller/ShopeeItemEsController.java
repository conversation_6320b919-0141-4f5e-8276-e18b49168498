package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.FeedTaskTypeConstant;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CUpdate;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.ShopeeVariationOption;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.ShopeeItemServiceCustom;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.constant.ShopeeRedisConstant;
import com.estone.erp.publish.shopee.dto.*;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskMsgEnum;
import com.estone.erp.publish.shopee.service.ShopeeCategoryV2Service;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.util.ShopeeCommonUtils;
import com.estone.erp.publish.shopee.util.ShopeeItemUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Shopee在线列表（es）控制层
 * @Auther yucm
 * @Date 2022/3/25
 */
@Slf4j
@RestController()
@RequestMapping("/shopeeItemEs")
public class ShopeeItemEsController {

    @Resource
    private EsShopeeItemService esShopeeItemService;
    @Resource
    private ShopeeItemEsService shopeeItemEsService;
    @Resource
    private DrainageSkuService drainageSkuService;
    @Resource
    private ShopeeItemServiceCustom shopeeItemServiceCustom;
    @Resource
    private ShopeeGlobalItemService shopeeGlobalItemService;
    @Resource
    private ShopeeCategoryV2Service shopeeCategoryV2Service;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @PostMapping
    public ApiResult<?> postShopeeItem(@RequestBody(required = true) ApiRequestParam<String> requestParam,
                                       HttpServletRequest request, HttpServletResponse response) {
        String user = WebUtils.getUserName();
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeItem": // 查询shopee在线列表
                    try{
                        CQuery<EsShopeeItemRequest> cquery = requestParam
                                .getArgsValue(new TypeReference<CQuery<EsShopeeItemRequest>>() {
                                });
                        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                        Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                        cquery.getSearch().setIsGoods(true);
                        EsShopeeItemRequest query = cquery.getSearch();
                        shopeeItemEsService.itemAuth(query);// 权限控制
                        return ApiResult.newSuccess(shopeeItemEsService.search(cquery));
                    } catch (Exception e){
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }
                case "searchShopeeFatherItem": // 查询shopee在线列表 父产品
                    try{
                        CQuery<EsShopeeItemRequest> cquery = requestParam
                                .getArgsValue(new TypeReference<CQuery<EsShopeeItemRequest>>() {
                                });
                        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                        Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                        cquery.getSearch().setIsFather(true);
                        EsShopeeItemRequest query = cquery.getSearch();
                        shopeeItemEsService.itemAuth(query);
                        return ApiResult.newSuccess(shopeeItemEsService.search(cquery));
                    } catch (Exception e){
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }
                case "syncShopeeAccountNumber": // 同步shopee账号(分为全量同步和增量同步两种)
                    Map<String, String> paramMap = requestParam.getArgsValue(new TypeReference<Map<String, String>>() {
                    });
                    JSONObject paramObject = JSONObject.parseObject(paramMap.get("search"), JSONObject.class);
                    String itemSellerStr = paramObject.getString("itemSellerStr");
                    //是否全量同步，由于兼容性的原因用isFather来接收
                    boolean isFullSync = paramObject.getString("isFather").equals("1");
                    if (StringUtils.isBlank(itemSellerStr)) {
                        return ApiResult.newError("参数shopee账号为空！");
                    }
                    //同步所有账号（增量同步）
                    if (itemSellerStr.equals("all")) {
                        Boolean aBoolean = ShopeeCommonUtils.isLimit(ShopeeRedisConstant.SYNC_ALL_ACCOUNT_LIMIT, 1);
                        if (aBoolean == true) {
                            return ApiResult.newError("1个小时内只允许全量同步一次账号信息！");
                        }
                        List<SaleAccountAndBusinessResponse> accountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannelEnum.SHOPEE.getChannelName());
                        for (SaleAccountAndBusinessResponse account : accountList) {
                            //不同步SIP（虚拟）店铺
                            if (BooleanUtils.isTrue(account.getColBool2())) {
                                continue;
                            }
                            ShopeeExecutors.executeSyncProduct(() -> {
                                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account.getAccountNumber(), true);
                                shopeeItemEsService.syncAccountShopeeItem(saleAccount,user,false,2);
                            });
                        }
                    } else { // 同步指定的账号
                        String[] accountArray = itemSellerStr.split(",");
                        for (String accountNumber : accountArray) {
                            ShopeeExecutors.executeSyncProduct(() -> {
                                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);
                                shopeeItemEsService.syncAccountShopeeItem(saleAccount,user,isFullSync,2);
                            });
                        }
                    }
                    return ApiResult.newSuccess("后台已开始账号同步处理，请到处理报告查看处理结果");
                //根据itemId同步Shopee的Listing
                case "syncListingByItemId":
                    SyncItemDto syncItemDto = requestParam.getArgsValue(new TypeReference<SyncItemDto>() {});
                    Set<String> syncItemIdSet = new HashSet<>();
                    for (String itemId : syncItemDto.getItemIdStr().split(",")) {
                        if(!syncItemIdSet.add(itemId)) {
                            continue;
                        }
                        ShopeeExecutors.executeSyncProductDetail(() ->{
                            shopeeItemEsService.syncListingByItemId(syncItemDto.getItemSellerStr(),itemId.trim(),user);
                        });
                    }
                    return ApiResult.newSuccess("后台已开始Listing同步，请到处理报告查看处理结果");
                case "batchUpdateTitle": //更改标题
                    String updateTitleItemIdStr = requestParam.getArgs();
                    if(StringUtils.isBlank(updateTitleItemIdStr)) {
                        return ApiResult.newError("请选择需要修改的itemId");
                    }
                    EsShopeeItemRequest updateTitleRequest = new EsShopeeItemRequest();
                    updateTitleRequest.setItemIdList(Arrays.asList(updateTitleItemIdStr.split(",")));
                    updateTitleRequest.setIsFather(true);
                    updateTitleRequest.setQueryFields(new String[]{"id", "itemId", "itemSeller", "articleNumber", "site", "spu"});
                    List<EsShopeeItem> updateTitleItemList = shopeeItemEsService.getEsShopeeItems(updateTitleRequest);

                    Map<String, String> skuTitleMap = new HashMap<>();
                    for (EsShopeeItem esShopeeItem : updateTitleItemList) {
                        String articleNumber = esShopeeItem.getArticleNumber();
                        String title = skuTitleMap.get(articleNumber + esShopeeItem.getSite());
                        if(StringUtils.isEmpty(title)) {
                            List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(articleNumber));
                            if(CollectionUtils.isEmpty(skuInfoList) || StringUtils.isBlank(skuInfoList.get(0).getTitleEn())) {
                                continue;
                            }
                            title = skuInfoList.get(0).getTitleEn();
                            skuTitleMap.put(articleNumber + esShopeeItem.getSite(), title);
                        }

                        String finalTitle = title;
                        ShopeeExecutors.updateProduct(() -> {
                            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), esShopeeItem.getItemSeller(), true);
                            //台湾站的不允许更新标题和描述（因为需要翻译）
                            if (shopeeAccount.getAccountSite().equalsIgnoreCase("tw")) {
                                return;
                            }
                            EsShopeeItem updateTitleItem = new EsShopeeItem();
                            updateTitleItem.setId(esShopeeItem.getId());
                            updateTitleItem.setItemId(esShopeeItem.getItemId());
                            updateTitleItem.setName(finalTitle);
                            updateTitleItem.setSite(esShopeeItem.getSite());

                            shopeeItemEsService.updateProduct(shopeeAccount, user, updateTitleItem);
                        });
                    }

                    return ApiResult.newSuccess();
                case "batchUpdateDesc": //更改描述
                    String updateDescItemIdStr = requestParam.getArgs();
                    if(StringUtils.isBlank(updateDescItemIdStr)) {
                        return ApiResult.newError("请选择需要修改的itemId");
                    }
                    EsShopeeItemRequest updateDescRequest = new EsShopeeItemRequest();
                    updateDescRequest.setItemIdList(Arrays.asList(updateDescItemIdStr.split(",")));
                    updateDescRequest.setIsFather(true);
                    updateDescRequest.setQueryFields(new String[]{"id", "itemId", "itemSeller", "articleNumber", "site", "descImgMapping", "descriptionType", "spu"});
                    List<EsShopeeItem> updateDescItemList = shopeeItemEsService.getEsShopeeItems(updateDescRequest);

                    Map<String, String> skuDescMap = new HashMap<>();
                    for (EsShopeeItem esShopeeItem : updateDescItemList) {
                        String articleNumber = esShopeeItem.getArticleNumber();
                        String desc = skuDescMap.get(articleNumber);
                        if(StringUtils.isEmpty(desc)) {
                            List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(articleNumber));
                            if(CollectionUtils.isEmpty(skuInfoList) || StringUtils.isBlank(skuInfoList.get(0).getDesEn())) {
                                continue;
                            }
                            desc = skuInfoList.get(0).getDesEn();
                            skuDescMap.put(articleNumber, desc);
                        }

                        String finalDesc = desc;
                        ShopeeExecutors.updateProduct(() -> {
                            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), esShopeeItem.getItemSeller(), true);
                            //台湾站的不允许更新标题和描述（因为需要翻译）
                            if (shopeeAccount.getAccountSite().equalsIgnoreCase("tw")) {
                                return;
                            }
                            EsShopeeItem updateTitleItem = new EsShopeeItem();
                            updateTitleItem.setId(esShopeeItem.getId());
                            updateTitleItem.setItemId(esShopeeItem.getItemId());
                            updateTitleItem.setDescription(finalDesc);
                            updateTitleItem.setSite(esShopeeItem.getSite());
                            updateTitleItem.setDescImgMapping(esShopeeItem.getDescImgMapping());
                            updateTitleItem.setDescriptionType(esShopeeItem.getDescriptionType());
                            shopeeItemEsService.updateProduct(shopeeAccount, user, updateTitleItem);
                        });
                    }
                    return ApiResult.newSuccess();
                case "searchNotCopyItemSku": // 查询销售还未上架产品的sku
                    JSONObject accountSkuObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    String accountNumber = accountSkuObject.getString("accountNumber");
                    String[] paramSkus = accountSkuObject.getString("sku").replace(" ", "").replace("\t", "").split(",");
                    List<String> paramSkuList = new ArrayList<>(Arrays.asList(paramSkus));

                    EsShopeeItemRequest existItemRequest = new EsShopeeItemRequest();
                    existItemRequest.setArticleNumberList(paramSkuList);
                    existItemRequest.setItemSeller(accountNumber);
                    existItemRequest.setQueryFields(new String[]{"id", "itemSeller", "articleNumber"});
                    List<EsShopeeItem> existItemList = shopeeItemEsService.getEsShopeeItems(existItemRequest);
                    List<String> existSkuList = existItemList.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
                    paramSkuList.removeAll(existSkuList);
                    return ApiResult.newSuccess(paramSkuList);
                case "downloadShopeeItems": // 批量导出选中记录
                    CQuery<EsShopeeItemRequest> cQuery = requestParam
                            .getArgsValue(new TypeReference<CQuery<EsShopeeItemRequest>>() {
                            });
                    Asserts.isTrue(cQuery != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                    Asserts.isTrue(cQuery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                    // 获取导出字段
                    List<String> fields = cQuery.getSearch().getDownFields();
                    EsShopeeItemResponse shopeeItemResponse;
                    int offset = 0;
                    final int maxDownload = 500000;
                    cQuery.setLimit(1);
                    cQuery.getSearch().setQueryExtend(false);
                    // 只查询商品 父产品且有变体 则父产品不展示
                    cQuery.getSearch().setIsGoods(true);
                    try {
                        cQuery.setOffset(offset);
                        shopeeItemResponse = shopeeItemEsService.search(cQuery);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }
                    Page<EsShopeeItem> esShopeeItemPage = shopeeItemResponse.getEsShopeeItemPage();
                    long totalElements = esShopeeItemPage.getTotalElements();
                    if (totalElements > maxDownload) {
                        return ApiResult.newError("导出数据超过最大" + maxDownload + "行");
                    }
                    // 构造导出日志
                    ExcelDownloadLog downloadLog = new ExcelDownloadLog();
                    downloadLog.setType(ShopeeDownloadTypeEnums.LILSTING_RECORD.getType());
                    downloadLog.setQueryCondition(JSON.toJSONString(cQuery));
                    downloadLog.setDownloadCount((int) totalElements);
                    downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
                    downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
                    downloadLog.setCreateBy(org.apache.commons.lang.StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
                    // 发送队列
                    excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
                    return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
                }
        }
        return ApiResult.newSuccess();
    }

    @PutMapping
    public ApiResult<?> putShopeeItem(@RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        String user = WebUtils.getUserName();
        if (StringUtils.isNotBlank(method)) {

            switch (method) {
                case "startShopeeItems": // 批量上架
                    String startItemIdStr = requestParam.getArgs();
                    if (StringUtils.isBlank(startItemIdStr)) {
                        return ApiResult.newError("请选择需要修改的itemId");
                    }

                    try {
                        shopeeItemEsService.batchStartItems(Arrays.asList(startItemIdStr.split(",")), user);
                    } catch (Exception e) {
                        return ApiResult.newError("批量上架失败,稍后重试！");
                    }
                    return ApiResult.newSuccess("已执行上架，请到处理报告确认成功失败的结果");
                case "endShopeeItems": // 批量下架
                    String endItemIdStr = requestParam.getArgs();
                    if(StringUtils.isBlank(endItemIdStr)) {
                        return ApiResult.newError("请选择需要修改的itemId");
                    }

                    ResponseJson responseJson = shopeeItemEsService.batchEndItems(Arrays.asList(endItemIdStr.split(",")), true, user);
                    return ResponseJsonToApiResult(responseJson);
                case "deleteShopeeItems": // 批量删除
                    String delItemIdStr = requestParam.getArgs();
                    if(StringUtils.isBlank(delItemIdStr)) {
                        return ApiResult.newError("请选择需要删除的itemId");
                    }

                    Set<String> delItemIdSet = new HashSet<>();
                    String[] split = delItemIdStr.split(",");
                    delItemIdSet.addAll(Arrays.asList(split));

                    // 该操作会将满足以下条件之一的勾选数据进行批量删除，其他数据将会自动过滤，请确认。
                    //1-对应shopee平台站点禁售
                    //2-刊登时间大于60天且销量=0
                    //3-系统状态为停产或存档的单属性spu，所有子sku都为停产或存档的多属性spu
                    EsShopeeItemRequest request = new EsShopeeItemRequest();
                    request.setItemIdList(new ArrayList<>(delItemIdSet));
                    String[] string = new String[]{"itemId", "itemSeller", "site", "itemStatus","skuStatus", "itemSku",
                            "articleNumber", "spu", "skuDataSource", "uploadDate", "sales", "forbidChannel", "prohibitionSites"};
                    request.setQueryFields(string);
                    request.setIsGoods(true);
                    List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
                    Set<String> deleteItemSet = ShopeeItemUtils.filterDeleteItem(esShopeeItems);
                    if (CollectionUtils.isEmpty(deleteItemSet)) {
                        return ApiResult.newError("过滤完无可操作的数据");
                    }

                    for (String itemId : deleteItemSet) {
                        ShopeeExecutors.executeDeleteItem(() -> {
                            shopeeItemEsService.deleteItem(itemId, user);
                        });
                    }
                    return ApiResult.newSuccess("后台已开始删除产品，请到处理报告查看处理结果");
                case "deleteShopeeItemVariations": // 批量删除
                    String delIdStr = requestParam.getArgs();
                    if(StringUtils.isBlank(delIdStr)) {
                        return ApiResult.newError("请选择需要删除的id");
                    }

                    for (String id : delIdStr.split(",")) {
                        ShopeeExecutors.executeDeleteItem(() -> {
                            shopeeItemEsService.deleteItemVariation(id, user);
                        });
                    }
                    return ApiResult.newSuccess("后台已开始删除产品，请到处理报告查看处理结果");
                case "batchUpdateStock":
                    List<EsShopeeItem> updateStockItemList = requestParam.getArgsValue(new TypeReference<List<EsShopeeItem>>() {
                    });
                    for (EsShopeeItem shopeeItem : updateStockItemList) {
                        ShopeeExecutors.executeUpdateStock(() -> {
                            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
                            shopeeGlobalItemService.updateShopeeStock(account, user, shopeeItem, FeedTaskTypeConstant.ARTIFICIAL,
                                    ShopeeFeedTaskMsgEnum.SALE_UPDATE_STOCK.getCode());
                        });
                    }
                    return ApiResult.newSuccess("后台已开始修改库存，请到处理报告查看处理结果");
                case "updateDaysToShipShopeeItems": // 批量修改发货天数
                    CUpdate<EsShopeeBatchRequest> cupdateDaysToShipShopeeItems = requestParam
                            .getArgsValue(new TypeReference<CUpdate<EsShopeeBatchRequest>>() {
                            });

                    Asserts.isTrue(cupdateDaysToShipShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
                    Asserts.isTrue(cupdateDaysToShipShopeeItems.getUpdateData() != null,
                            ErrorCode.PARAM_EMPTY_ERROR, "参数updateData错误，请检查！");
                    Asserts.isTrue(cupdateDaysToShipShopeeItems.getUpdateData().getEsShopeeItems() != null,
                        ErrorCode.PARAM_EMPTY_ERROR, "参数shopeeItemWithBLOBss错误，请检查！");

                    ResponseJson updateDaysToShipResponseJson = shopeeItemServiceCustom.batchUpdateDays(cupdateDaysToShipShopeeItems.getUpdateData().getEsShopeeItems());
                    return ResponseJsonToApiResult(updateDaysToShipResponseJson);
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("/getShopeeItem")
    public ApiResult<?> getShopeeItem(@RequestBody EsShopeeItemRequest request) {
        if(null == request) {
            return ApiResult.newError("请求参数为空！");
        }
        // 设置一些必传条件避免查询数据量过大 根据需要可以添加其他值
        if(CollectionUtils.isEmpty(request.getItemIdList()) && CollectionUtils.isEmpty(request.getIdList())
                && CollectionUtils.isEmpty(request.getItemSellerList()) && StringUtils.isBlank(request.getItemSeller())) {
            return ApiResult.newError("请确认必填参数未传 可能查询数据量过大");
        }
        // 查询主要字段和需要的字段 后续其他需求看着添加
        String [] queryFields = {
                "id", "itemId", "variationId", "globalItemId", "globalModelId", "itemSeller", "site", "hasVariation", "isFather", "isGoods",
                "images", "name", "itemStatus", "itemSku", "articleNumber", "spu", "dataSource", "discountId", "itemHasDiscount",
                "price", "originalPrice", "sipItemPrice", "stock", "skuStatus", "uploadDate", "syncDate",
        };
        request.setQueryFields(queryFields);
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        return ApiResult.newSuccess(esShopeeItems);
    }

    /**
     * 批量修改mtsku库存
     *
     * @param body
     * @return
     */
    @PostMapping("/batchUpdateStockMtsku")
    public ApiResult<?> batchUpdateStockMtsku(@RequestBody String body) {
        if (StringUtils.isBlank(body)) {
            return ApiResult.newError("参数为空！");
        }
        List<EsShopeeItem> itemList;
        try {
            itemList = JSON.parseObject(body, new TypeReference<List<EsShopeeItem>>() {
            });
        } catch (Exception e) {
            log.error("body解析失败：", e);
            return ApiResult.newError(String.format("%s. 参数解析失败", body));
        }
        if (CollectionUtils.isEmpty(itemList)) {
            return ApiResult.newError("参数为空！");
        }
        String user = WebUtils.getUserName();


        for (EsShopeeItem shopeeItem : itemList) {
            ShopeeExecutors.executeUpdateStock(() -> {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
                shopeeGlobalItemService.updateShopeeStock(account, user, shopeeItem, FeedTaskTypeConstant.ARTIFICIAL,
                        ShopeeFeedTaskMsgEnum.SALE_UPDATE_MTSK_STOCK.getCode());
            });
        }

        return ApiResult.newSuccess("后台已开始修改库存，请到处理报告查看处理结果");
    }

    /**
     * 批量修改价格
     * @param requests
     * @return
     */
    @PostMapping("/batchUpdatePrice")
    public ApiResult<?> batchUpdatePrice(@RequestBody List<ShopeeItemCalcRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数不能为空！");
        }

        String user = WebUtils.getUserName();
        Map<String,List<ShopeeItemCalcRequest>> shopeeItemMap = requests.stream()
                .filter(item -> item.getItemSeller() != null)
                .collect(Collectors.groupingBy(ShopeeItemCalcRequest::getItemSeller));

        for (Map.Entry<String, List<ShopeeItemCalcRequest>> entry : shopeeItemMap.entrySet()) {
            String itemSeller = entry.getKey();
            List<ShopeeItemCalcRequest> list = entry.getValue();
            ShopeeExecutors.executeUpdatePrice(() -> {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), itemSeller, true);
                for (ShopeeItemCalcRequest request : list) {
                    shopeeItemEsService.updateShopeePrice(account, user, request);
                }
            });
        }
        return ApiResult.newSuccess("后台已开始修改价格，请到处理报告查看处理结果");
    }

    /**
     * 批量修改试卖价格
     *
     * @param requests
     * @return
     */
    @PostMapping("/batchUpdatePrice/trialSale")
    public ApiResult<?> batchUpdateTrialSalePrice(@RequestBody List<ShopeeItemCalcRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数不能为空！");
        }

        String user = WebUtils.getUserName();
        Map<String, List<ShopeeItemCalcRequest>> shopeeItemMap = requests.stream()
                .filter(item -> item.getItemSeller() != null)
                .collect(Collectors.groupingBy(ShopeeItemCalcRequest::getItemSeller));

        for (Map.Entry<String, List<ShopeeItemCalcRequest>> entry : shopeeItemMap.entrySet()) {
            String itemSeller = entry.getKey();
            List<ShopeeItemCalcRequest> list = entry.getValue();
            ShopeeExecutors.executeUpdatePrice(() -> {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), itemSeller, true);
                for (ShopeeItemCalcRequest request : list) {
                    shopeeItemEsService.batchUpdateTrialSalePrice(account, user, request);
                }
            });
        }
        return ApiResult.newSuccess("后台已开始修改价格，请到处理报告查看处理结果");
    }


    /**
     * 批量修改sip价格
     * @param requests
     * @return
     */
    @PostMapping("/batchUpdateSipItemPrice")
    public ApiResult<?> batchUpdateSipItemPrice(@RequestBody List<ShopeeItemCalcRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数不能为空！");
        }

        String user = WebUtils.getUserName();
        Map<String,List<ShopeeItemCalcRequest>> shopeeItemMap = requests.stream()
                .filter(item -> item.getItemSeller() != null)
                .collect(Collectors.groupingBy(ShopeeItemCalcRequest::getItemSeller));

        for (Map.Entry<String, List<ShopeeItemCalcRequest>> entry : shopeeItemMap.entrySet()) {
            String itemSeller = entry.getKey();
            List<ShopeeItemCalcRequest> list = entry.getValue();
            ShopeeExecutors.executeUpdatePrice(() -> {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), itemSeller, true);
                for (ShopeeItemCalcRequest request : list) {
                    shopeeItemEsService.updateShopeeSipItemPrice(account, user, request);
                }
            });
        }
        return ApiResult.newSuccess("后台已开始修改sip价格，请到处理报告查看处理结果");
    }

    /**
     * 去修改产品图片
     * @param itemIds
     * @return
     */
    @PostMapping("/toBatchUpdateImage")
    public ApiResult<?> toBatchUpdateImage(@RequestBody List<String> itemIds) {
        if(CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newError("请选择需要修改的数据");
        }

        String [] queryFields = { "itemId", "itemSeller", "articleNumber", "spu", "images"};
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemIdList(itemIds);
        request.setQueryFields(queryFields);
        List<EsShopeeItem> shopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if(CollectionUtils.isEmpty(shopeeItems)) {
            return ApiResult.newError("选择的产品未找到");
        }

        List<ShopeeItemUpdateImageDto> shopeeItemUpdateImageDtos = new ArrayList<>();
        Set<String> accountItemSet = new HashSet();
        Map<String, List<String>> spuImageMap = new HashMap();
        for (EsShopeeItem shopeeItem : shopeeItems) {
            if(!accountItemSet.add(shopeeItem.getItemSeller() + "_" + shopeeItem.getItemId())) {
                continue; // 根据店铺加itemId去重
            }

            String spu = StringUtils.isBlank(shopeeItem.getSpu()) ? shopeeItem.getArticleNumber() : shopeeItem.getSpu();
            List<String> spuImage = spuImageMap.get(spu);
            if(CollectionUtils.isEmpty(spuImage)) {
                // 获取文件系统的产品库图片
                String type = PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName();
                spuImage = FmsUtils.getPictureUrlBySkuAndType(spu, type);
                spuImageMap.put(spu, spuImage);
            }
            String image = shopeeItem.getImages();

            ShopeeItemUpdateImageDto dto = new ShopeeItemUpdateImageDto();
            dto.setItemId(shopeeItem.getItemId());
            dto.setItemSeller(shopeeItem.getItemSeller());
            dto.setSpu(spu);
            try {
                dto.setImages(JSON.parseArray(image, String.class));
            }catch (Exception e) {
                return ApiResult.newError(shopeeItem.getItemId() + "图片原图获取出错" + e.getMessage());
            }
            dto.setSpuImages(spuImage);
            shopeeItemUpdateImageDtos.add(dto);
        }

        return ApiResult.newSuccess(shopeeItemUpdateImageDtos);
    }

    /**
     * 修改产品图片
     * @param dtos
     * @return
     */
    @PostMapping("/batchUpdateImage")
    public ApiResult<?> batchUpdateImage(@RequestBody List<ShopeeItemUpdateImageDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return ApiResult.newError("参数不能为空！");
        }

        String user = WebUtils.getUserName();
        Set<String> itemSellerAndItemIdSet = new HashSet<>();
        for (ShopeeItemUpdateImageDto dto : dtos) {
            if(!itemSellerAndItemIdSet.add(dto.getItemSeller() + "_" + dto.getItemId())) {
                continue;
            }

            ShopeeExecutors.executeUpdateImage(() -> {
                shopeeItemEsService.updateImage(dto, user);
            });
        }
        return ApiResult.newSuccess("后台已开始修改图片，请到处理报告查看处理结果");
    }

    /**
     * 去修改变体图片
     * @param itemIds
     * @return
     */
    @PostMapping("/toBatchUpdateSkuImage")
    public ApiResult<?> toBatchUpdateSkuImage(@RequestBody List<String> itemIds) {
        if(CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newError("请选择需要修改的数据");
        }

        String [] queryFields = { "itemId", "variationId", "itemSeller", "articleNumber", "spu", "isFather", "variationOptions"};
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemIdList(itemIds);
        request.setQueryFields(queryFields);
        List<EsShopeeItem> shopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        if(CollectionUtils.isEmpty(shopeeItems)) {
            return ApiResult.newError("选择的产品未找到");
        }

        List<ShopeeItemUpdateSkuImageDto> shopeeItemUpdateSkuImageDtos = new ArrayList<>();
        Map<String, List<String>> spuImageMap = new HashMap();
        for (EsShopeeItem shopeeItem : shopeeItems) {
            if(StringUtils.isBlank(shopeeItem.getVariationId())) {
                continue;// 单体 多变体的父体都不需要修改
            }
            String spu = StringUtils.isBlank(shopeeItem.getSpu()) ? shopeeItem.getArticleNumber() : shopeeItem.getSpu();
            List<String> spuImage = spuImageMap.get(spu);
            if(CollectionUtils.isEmpty(spuImage)) {
                // 获取文件系统的产品库图片
                String type = PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName();
                spuImage = FmsUtils.getPictureUrlBySkuAndType(spu, type);
                spuImageMap.put(spu, spuImage);
            }

            ShopeeItemUpdateSkuImageDto dto = new ShopeeItemUpdateSkuImageDto();
            shopeeItemUpdateSkuImageDtos.add(dto);
            dto.setItemId(shopeeItem.getItemId());
            dto.setItemSeller(shopeeItem.getItemSeller());
            dto.setSpu(spu);
            dto.setArticleNumber(shopeeItem.getArticleNumber());

            List<ShopeeVariationOption> variationOptions = shopeeItem.getVariationOptions();
            if(CollectionUtils.isNotEmpty(variationOptions)) {
                for (ShopeeVariationOption variationOption : variationOptions) {
                    String skuImage = variationOption.getImageUrl();
                    if(StringUtils.isNotBlank(skuImage)) {
                        dto.setSkuImage(skuImage);
                        break;
                    }
                }
            } else {
                dto.setMessage("该sku无子sku图片和属性无法修改子sku图片，请同步后再来修改");
            }
        }
        if(CollectionUtils.isEmpty(shopeeItemUpdateSkuImageDtos)) {
            return ApiResult.newError("请确认选择的数据是多属性，无model id数据无法修改子sku图片");
        }

        ShopeeItemUpdateSkuImageVo vo = new ShopeeItemUpdateSkuImageVo();
        vo.setShopeeItemUpdateSkuImageDtos(shopeeItemUpdateSkuImageDtos);
        vo.setSpuImageMap(spuImageMap);

        return ApiResult.newSuccess(vo);
    }

    /**
     * 修改产品Sku图片
     * @param dtos
     * @return
     */
    @PostMapping("/batchUpdateSkuImage")
    public ApiResult<?> batchUpdateSkuImage(@RequestBody List<ShopeeItemUpdateSkuImageDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return ApiResult.newError("参数不能为空！");
        }

        String user = WebUtils.getUserName();
        Map<String, List<ShopeeItemUpdateSkuImageDto>> accountItemMap = dtos.stream()
                .filter(item -> item.getItemSeller() != null)
                .collect(Collectors.groupingBy(o->o.getItemSeller() + "_" + o.getItemId()));

        for (Map.Entry<String, List<ShopeeItemUpdateSkuImageDto>> entry : accountItemMap.entrySet()) {
            ShopeeExecutors.executeUpdateSkuImage(() -> {
                shopeeItemEsService.updateSkuImage(entry.getValue(), user);
            });
        }

        return ApiResult.newSuccess("后台已开始修改图片，请到处理报告查看处理结果");
    }


    /**
     * @param updateAttributesDto
     * @return {@link ApiResult<?>}
     * @docName batchUpdateAttributes
     */
    @PostMapping("batchUpdateAttributes")
    public ApiResult<?> batchUpdateAttributes(@RequestBody UpdateAttributesDto updateAttributesDto){
        if (null == updateAttributesDto) {
            return ApiResult.newError("参数不能为空！");
        }
        String user = WebUtils.getUserName();
        if(StringUtils.isBlank(user)) {
            return ApiResult.newError("未获取到user,请重新登录" );
        }

        try {
            new Thread(() -> {
                shopeeItemEsService.batchUpdateAttributes(updateAttributesDto, user);
            }).start();
            return ApiResult.newSuccess("后台处理中，请稍后查看处理报告");
        }catch (Exception e) {
            log.error("批量修改属性失败" + e.getMessage(), e);
            return ApiResult.newError("批量修改属性失败" + e.getMessage());
        }
    }

    /**
     * 引流-根据店铺获取引流sku
     * http://172.16.10.40/web/#/31?page_id=5575
     * @param account
     * @return
     */
    @GetMapping("/getDrainageSkuByAccount")
    public ApiResult<?> getDrainageSkuByAccount(@RequestParam(defaultValue = "") String account) {
        if(StringUtils.isBlank(account)){
            return ApiResult.newError("account is required");
        }
        DrainageSkuExample ex = new DrainageSkuExample();
        ex.createCriteria()
                .andPlatformEqualTo(Platform.Shopee.name())
                .andAccountNumberEqualTo(account)
                .andIsDrainageEqualTo(true);
        List<DrainageSku> list = drainageSkuService.selectByExample(ex);
        return ApiResult.newSuccess(list);
    }

    /**
     *  引流-保存店铺设置的引流sku
     *  http://172.16.10.40/web/#/31?page_id=5576
     * @param list
     * @return
     */
    @PostMapping("/saveDrainageSkuByAccount")
    public ApiResult<?> saveDrainageSkuByAccount(@RequestBody List<DrainageSku> list) {
        if(list == null){
            return ApiResult.newError("sku info is required");
        }
        if(CollectionUtils.isNotEmpty(list)){
            list = list.stream()
                    .filter(o -> {
                        if(StringUtils.isNotBlank(o.getSku())){
                            o.setSku(o.getSku().toUpperCase());
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("list sku info is required");
        }
        Map<String, Long> skuCount = list.stream().collect(Collectors.groupingBy(o -> o.getSku(), Collectors.counting()));
        List<String> failMsg = new ArrayList<>();
        for (Map.Entry<String, Long> entry : skuCount.entrySet()) {
            if (entry.getValue() > 1) {
                failMsg.add(entry.getKey());
            }
        }
        if(failMsg.size() > 0){
            return ApiResult.newError(String.format("sku %s 存在重复，请删除重复sku！", failMsg));
        }

        String accountNumber = list.get(0).getAccountNumber();
        List<String> skuList = list.stream()
                .filter(o -> BooleanUtils.isTrue(o.getIsDrainage()))
                .map(o -> o.getSku())
                .collect(Collectors.toList());
        if(skuList.size() > 0){
            EsShopeeItemRequest request = new EsShopeeItemRequest();
            request.setItemSeller(accountNumber);
            request.setArticleNumberList(skuList);
            List<EsShopeeItem> itemList = shopeeItemEsService.getEsShopeeItems(request);
            Map<String, Set<String>> skuStatusMap =
                    itemList.stream()
                            //过滤出 单体和变体的子sku
                            .filter(o -> (o.getIsFather() && !o.getHasVariation())  || (!o.getIsFather() && o.getHasVariation()))
                            .collect(Collectors.groupingBy(o -> o.getArticleNumber().toUpperCase(), Collectors.mapping(o -> o.getItemStatus(), Collectors.toSet())));
            for (Map.Entry<String, Set<String>> entry : skuStatusMap.entrySet()) {
                if(!entry.getValue().contains("NORMAL")){
                    failMsg.add(entry.getKey());
                }
            }
            if(failMsg.size() > 0){
                return ApiResult.newError(String.format("sku %s 不是 NORMAL 状态，请删除或取消选中！", failMsg));
            }
            String failSku = skuList.stream().filter(sku -> !skuStatusMap.containsKey(sku)).collect(Collectors.joining(","));
            if(StringUtils.isNotBlank(failSku)){
                return ApiResult.newError(String.format("sku[%s] 不存在，请删除或取消选中！提示：如果是变体请确认输入的是子sku！", failSku));
            }
        }
        //sku 转大写
        list.stream().forEach(o -> {
            o.setPlatform(Platform.Shopee.name());
            o.setSku(o.getSku());
        });

        ApiResult<?> apiResult = drainageSkuService.updateOrInsert(list);
        return apiResult;
    }

    /**
     * 修改库存校验
     * @param itemIds 商品Ids
     * @return
     */
    @PostMapping("checkUpdateStock")
    public ApiResult<Map<String,Integer>> checkUpdateStock(@RequestBody List<String> itemIds) {
        // 获取商品中清仓甩卖且不禁售的sku及库存
        Map<String,Boolean> accountAndIsNnWarehouse = new HashMap<>();
        Map<String,Integer> skuStockMap = shopeeItemEsService.getReductionAndCleanForbidSkuStockByEsId(accountAndIsNnWarehouse, itemIds);
        if (MapUtils.isNotEmpty(skuStockMap)) {
            return ApiResult.of(true, skuStockMap, "存在SKU单品状态为清仓、甩卖，且SKU在Shopee不禁售，不允许修改库存为0，只允许修改库存为可用库存,是否修改为可用库存?");
        }
        return ApiResult.newSuccess();
    }

    /**
     * 计算价格（外部系统调用）
     * @return
     */
    @PostMapping("calcPriceByItem")
    private ApiResult<?> calcPriceByItem(@RequestBody List<ShopeeItemCalcPrice> shopeeItemCalcPrices) {
        if(CollectionUtils.isEmpty(shopeeItemCalcPrices)) {
            return ApiResult.newError("请求参数为空");
        }
        for (ShopeeItemCalcPrice shopeeItemCalcPrice : shopeeItemCalcPrices) {
            if(StringUtils.isBlank(shopeeItemCalcPrice.getAccountNumber()) || StringUtils.isBlank(shopeeItemCalcPrice.getId()) || StringUtils.isBlank(shopeeItemCalcPrice.getItemId())) {
                return ApiResult.newError("id,accountNumber,itemId必传！");
            }
        }
        if(shopeeItemCalcPrices.size() > 200) {
            return ApiResult.newError("一次请求限制200");
        }

        return shopeeItemEsService.calcPriceByItem(shopeeItemCalcPrices);
    }

    @PostMapping("calcPriceByItem2")
    private ApiResult<?> calcPriceByItem2(@RequestBody List<ShopeeItemCalcPrice> shopeeItemCalcPrices) {
        if(CollectionUtils.isEmpty(shopeeItemCalcPrices)) {
            return ApiResult.newError("请求参数为空");
        }
        for (ShopeeItemCalcPrice shopeeItemCalcPrice : shopeeItemCalcPrices) {
            if(StringUtils.isBlank(shopeeItemCalcPrice.getAccountNumber()) || StringUtils.isBlank(shopeeItemCalcPrice.getId()) || StringUtils.isBlank(shopeeItemCalcPrice.getItemId())) {
                return ApiResult.newError("id,accountNumber,itemId必传！");
            }
        }
        if(shopeeItemCalcPrices.size() > 200) {
            return ApiResult.newError("一次请求限制200");
        }

        return shopeeItemEsService.calcPriceByItem2(shopeeItemCalcPrices);
    }


    private ApiResult<?> ResponseJsonToApiResult(ResponseJson responseJson) {
        ApiResult<?> updateDaysToShipApiResult = new ApiResult<Object>();
        if (null == responseJson) {
            return null;
        }

        if (responseJson.isSuccess()) {
            updateDaysToShipApiResult.setSuccess(true);
        } else {
            updateDaysToShipApiResult.setSuccess(false);
        }
        updateDaysToShipApiResult.setErrorMsg(JSON.toJSONString(responseJson.getErrors()));
        return updateDaysToShipApiResult;
    }

    @PostMapping("toCustomer/getShopeeItemItemList")
    public List<EsShopeeItem> getShopeeItemItemList(@RequestBody EsShopeeItemRequest esShopeeItemRequest) {
        Asserts.isTrue(null != esShopeeItemRequest, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空，请检查！");
        List<EsShopeeItem> list = esShopeeItemService.getListToCustomer(esShopeeItemRequest);
        return  list;
    }


    @PostMapping("toCustomer/getShopePageList")
    public List<EsShopeeItem> getShopePageList(@RequestBody EsShopeeItemRequest esShopeeItemRequest) {
        Asserts.isTrue(null != esShopeeItemRequest, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空，请检查！");
        List<EsShopeeItem> shopeeItems = esShopeeItemService.getPageListToCustomer(esShopeeItemRequest);
        return  shopeeItems;
    }

    @GetMapping("toCustomer/searchEsShopeeItemByItemId/{itemId}")
    public EsShopeeItem searchEsShopeeItemByItemId(@PathVariable(name = "itemId")  String itemId) {
        Asserts.isTrue(StringUtils.isNotBlank(itemId), ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空，请检查！");
        EsShopeeItem esShopeeItem = esShopeeItemService.getEsShopeeItemByItemIdToCustomer(itemId);
        return  esShopeeItem;
    }

    @GetMapping("toCustomer/searchArticleNumbersByItemId/{itemId}")
    public List<EsShopeeItem> searchArticleNumbersByItemId(@PathVariable(name = "itemId")  String itemId) {
        Asserts.isTrue(StringUtils.isNotBlank(itemId), ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空，请检查！");
        List<EsShopeeItem> sonItems = esShopeeItemService.searchArticleNumbersByItemIdToCustomer(itemId);
        return  sonItems;
    }
}
