package com.estone.erp.publish.shopee.api.v2.param.discount;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/12 12:06
 * @description 添加折扣
 */
@Getter
@Setter
public class ShopeeGetDiscountListV2 implements RequestCommon {

    /** 检索折扣清单的状态过滤器。可用值：upcoming/ongoing/expired/all */
    @JSONField(name = "discount_status")
    private String discountStatus;

    @JSONField(name = "page_no")
    private Integer pageNo;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_DISCOUNT_LIST;
    }
}
