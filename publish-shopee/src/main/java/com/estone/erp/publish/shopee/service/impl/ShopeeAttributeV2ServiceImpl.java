package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.shopee.api.v2.cnsc.GetAttributesCNSC;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.GetAttributesV2;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.shopee.enums.ShopeeAttributeTreeFormatTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeAttributeTreeInputTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeAttributeTreeInputValidationTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.mapper.ShopeeAttributeV2Mapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAttributeV2Service;
import com.estone.erp.publish.shopee.service.ShopeeCategoryV2Service;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> shopee_attribute_v2
 * 2021-05-12 09:28:57
 */
@Service("shopeeAttributeV2Service")
@Slf4j
public class ShopeeAttributeV2ServiceImpl implements ShopeeAttributeV2Service {
    @Resource
    private ShopeeAttributeV2Mapper shopeeAttributeV2Mapper;
    @Resource
    private ShopeeCategoryV2Service shopeeCategoryV2Service;

    @Override
    public int countByExample(ShopeeAttributeV2Example example) {
        Assert.notNull(example, "example is null!");
        return shopeeAttributeV2Mapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeAttributeV2> search(CQuery<ShopeeAttributeV2Criteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeAttributeV2Criteria query = cquery.getSearch();
        ShopeeAttributeV2Example example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeAttributeV2Mapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeAttributeV2> shopeeAttributeV2s = shopeeAttributeV2Mapper.selectByExample(example);
        // 组装结果
        CQueryResult<ShopeeAttributeV2> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeAttributeV2s);
        return result;
    }

    @Override
    public ShopeeAttributeV2 selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return shopeeAttributeV2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeAttributeV2> selectByExample(ShopeeAttributeV2Example example) {
        Assert.notNull(example, "example is null!");
        return shopeeAttributeV2Mapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeAttributeV2 record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return shopeeAttributeV2Mapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeAttributeV2 record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeAttributeV2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeAttributeV2 record, ShopeeAttributeV2Example example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeAttributeV2Mapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeAttributeV2Mapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByExample(ShopeeAttributeV2Example example) {
        return shopeeAttributeV2Mapper.deleteByExample(example);
    }

    @Override
    public int insertBatch(List<ShopeeAttributeV2> attributeList) {
        if (CollectionUtils.isEmpty(attributeList)) {
            return 0;
        }
        return shopeeAttributeV2Mapper.insertBatch(attributeList);
    }

    @Override
    public int updateBatch(List<ShopeeAttributeV2> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return shopeeAttributeV2Mapper.updateBatch(list);
    }

    @Override
    public void pullAttrV2(SaleAccountAndBusinessResponse shopeeAccount) {
        // 参数校验
        if (shopeeAccount == null || StringUtils.isBlank(shopeeAccount.getAccountSite())) {
            return;
        }

        // 查询类目列表
        ShopeeCategoryV2Example example = new ShopeeCategoryV2Example();
        example.createCriteria()
                .andSiteEqualTo(shopeeAccount.getAccountSite())
                .andHasChildrenEqualTo(false);
        List<ShopeeCategoryV2> shopeeCategoryList = shopeeCategoryV2Service.selectByExample(example);
        if (CollectionUtils.isEmpty(shopeeCategoryList)) {
            return;
        }

        // 分批处理，每批最多 20 个类目
        List<List<ShopeeCategoryV2>> partition = Lists.partition(shopeeCategoryList, 20);
        ExecutorService executor = Executors.newFixedThreadPool(20);
        CountDownLatch latch = new CountDownLatch(partition.size());
        try {
            for (List<ShopeeCategoryV2> shopeeCategoryV2List : partition) {
                executor.submit(() -> {
                    try {
                        List<Integer> categoryIdList = shopeeCategoryV2List.stream().map(ShopeeCategoryV2::getCategoryId).collect(Collectors.toList());

                        // 构建请求参数
                        RequestCommon rc;
                        boolean isTaiwan = ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(shopeeAccount.getAccountSite());
                        if (ShopeeConstants.CNSC.equalsIgnoreCase(shopeeAccount.getAccountSite())) {
                            GetAttributesCNSC param = new GetAttributesCNSC();
                            param.setCategoryIdList(categoryIdList);
                            if (isTaiwan) {
                                param.setLanguage("zh-hans");
                            }
                            rc = param;
                        } else {
                            GetAttributesV2 param = new GetAttributesV2();
                            param.setCategoryIdList(categoryIdList);
                            if (isTaiwan) {
                                param.setLanguage("zh-hans");
                            }
                            rc = param;
                        }

                        // 执行 API 调用并重试
                        ShopeeResponse response = null;
                        int retryTime = 3;
                        while (retryTime > 0) {
                            try {
                                response = ShopeeHttpUtils.doGetV2(shopeeAccount, rc);
                                break;
                            } catch (Exception e) {
                                retryTime--;
                                if (retryTime == 0) {
                                    break;
                                }
                            }
                        }

                        if (StringUtils.isNotBlank(response.getError())) {
                            return;
                        }
                        JSONObject resultObject = JSONObject.parseObject(response.getResponse());
                        if (ObjectUtils.isEmpty(resultObject)) {
                            return;
                        }
                        List<JSONObject> categoryList = JSON.parseObject(resultObject.getString("list"), new TypeReference<List<JSONObject>>() {
                        });
                        if (CollectionUtils.isEmpty(categoryList)) {
                            return;
                        }

                        for (JSONObject category : categoryList) {
                            Integer categoryId = category.getInteger("category_id");
                            if (categoryId == null) {
                                continue;
                            }

                            List<JSONObject> attributeTreeList = category.getObject("attribute_tree", List.class);
                            if (CollectionUtils.isEmpty(attributeTreeList)) {
                                continue;
                            }
                            List<ShopeeAttributeV2> attributeList = new ArrayList<>(attributeTreeList.size());
                            attributeTreeList.forEach(attributeTree -> {
                                try {
                                    ShopeeAttributeV2 shopeeAttributeV2 = new ShopeeAttributeV2();
                                    shopeeAttributeV2.setSite(shopeeAccount.getAccountSite());
                                    shopeeAttributeV2.setCategoryId(categoryId);
                                    shopeeAttributeV2.setAttributeId(attributeTree.getInteger("attribute_id"));
                                    shopeeAttributeV2.setOriginalAttributeName(attributeTree.getString("name"));
                                    shopeeAttributeV2.setDisplayAttributeName(attributeTree.getString("name"));
                                    shopeeAttributeV2.setIsMandatory(attributeTree.getBoolean("mandatory"));

                                    // 兼容刊登和原数据结构
                                    List<JSONObject> attributeValueList = JSON.parseObject(attributeTree.getString("attribute_value_list"), new TypeReference<List<JSONObject>>() {
                                    });
                                    if (!CollectionUtils.isEmpty(attributeValueList)) {
                                        List<JSONObject> shopeeAttributeValueDtoV2s = attributeValueList.stream()
                                                .map(attributeValue -> {
                                                    JSONObject attributeValueJSON = new JSONObject();
                                                    attributeValueJSON.put("value_id", attributeValue.getIntValue("value_id"));
                                                    attributeValueJSON.put("value_unit", attributeValue.getString("value_unit"));
                                                    attributeValueJSON.put("display_value_name", attributeValue.getString("name"));
                                                    attributeValueJSON.put("original_value_name", attributeValue.getString("name"));
                                                    return attributeValueJSON;
                                                })
                                                .collect(Collectors.toList());
                                        shopeeAttributeV2.setAttributeValueList(JSON.toJSONString(shopeeAttributeValueDtoV2s));
                                    }

                                    // 属性信息
                                    JSONObject attributeInfo = attributeTree.getJSONObject("attribute_info");
                                    if (ObjectUtils.isNotEmpty(attributeInfo)) {
                                        Optional.ofNullable(attributeInfo.getInteger("input_validation_type")).ifPresent(code -> shopeeAttributeV2.setInputValidationType(ShopeeAttributeTreeInputValidationTypeEnum.convert(code)));
                                        Optional.ofNullable(attributeInfo.getInteger("format_type")).ifPresent(code -> shopeeAttributeV2.setFormatType(ShopeeAttributeTreeFormatTypeEnum.convert(code)));
                                        Optional.ofNullable(attributeInfo.getInteger("input_type")).ifPresent(code -> shopeeAttributeV2.setInputType(ShopeeAttributeTreeInputTypeEnum.convert(code)));
                                        shopeeAttributeV2.setAttributeUnit(attributeInfo.getString("attribute_unit_list"));
                                    }

                                    attributeList.add(shopeeAttributeV2);
                                } catch (Exception e) {
                                    log.error("处理属性失败，categoryId: {}, attributeTree: {}", categoryId, attributeTree, e);
                                }
                            });
                            updateDetail(shopeeAccount, attributeList, categoryId);
                        }
                    } catch (Exception e) {
                        log.error("处理类目批次失败", e);
                    } finally {
                        latch.countDown();
                    }
                });
            }
            // 等待所有任务完成
            if (!latch.await(30, TimeUnit.MINUTES)) {
                log.warn("属性同步超时，部分任务可能未完成");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("属性同步被中断", e);
        } finally {
            executor.shutdown();
        }
        log.info("account:{}属性同步完成", shopeeAccount.getAccountNumber());
    }

    private void updateDetail(SaleAccountAndBusinessResponse shopeeAccount, List<ShopeeAttributeV2> attributeList, int categoryId) {
        ShopeeAttributeV2Example attributeExample = new ShopeeAttributeV2Example();
        attributeExample.createCriteria()
                .andSiteEqualTo(shopeeAccount.getAccountSite())
                .andCategoryIdEqualTo(categoryId);

        int count = this.deleteByExample(attributeExample);
        log.info("site:{}_类目{}下_先删除{}条属性", shopeeAccount.getAccountSite(), categoryId, count);
        if (CollectionUtils.isEmpty(attributeList)) {
            return;
        }
        int insertCount = this.insertBatch(attributeList);
        log.info("site:{}_类目{}下_先删除{}条属性, 后新增{}条属性", shopeeAccount.getAccountSite(), categoryId, count, insertCount);
    }
}