package com.estone.erp.publish.shopee.jobHandler.stock;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.LogPrintUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.service.ShopeeOperateLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuSystemStock;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Es-12456
 * sku单品状态为停产或存档或废弃，且多属性为否，且SKU可用+在途-待发=0的链接，系统自动删除，记录对删除处理报告，备注为：停产存档且为单属性，系统自动删除
 * <p>
 * 所有sku单品状态为停产或存档或废弃，且多属性为是，且所有SKU可用+在途-待发之和=0的链接，系统自动删除，记录对删除处理报告，备注为：停产存档且为多属性，系统自动删除
 * <p>
 * 新增逻辑：
 * <p>
 * 针对南宁店铺：
 * <p>
 * 1. sku单品状态为停产或存档或废弃，且多属性为否，且SKU可用+在途-待发=0的链接，且特殊标签不包含NN滞销的链接
 * <p>
 * 系统自动删除，记录对删除处理报告，备注为：停产存档且为单属性，系统自动删除
 * <p>
 * 2. sku单品状态为停产或存档或废弃，且多属性为是，且所有SKU可用+在途-待发之和=0的链接，且所有的sku的特殊标签都不包含NN滞销的链接
 * <p>
 * 系统自动删除，记录对删除处理报告，备注为：停产存档且为多属性，系统自动删除
 * <p>
 * 备注：下架前需查询产品系统SKU单品状态是否为停产或存档或者废弃，不是，则跳过不删除。
 */
@Slf4j
@Component
public class ShopeeListingSingleItemDelJobHandler extends AbstractJobHandler {

    public ShopeeListingSingleItemDelJobHandler() {
        super("shopeeListingSingleItemDelJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<String> skuList;
        private List<String> accountNumberList;
    }


    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Autowired
    private ShopeeItemEsService shopeeItemEsService;
    @Autowired
    private ShopeeOperateLogService shopeeOperateLogService;


    @XxlJob("shopeeListingSingleItemDelJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = new InnerParam();
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析错误！" + param);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        List<String> accountNumberList = innerParam.getAccountNumberList();
        List<String> skuList = innerParam.getSkuList();

        ShopeeAccountConfigExample shopeeAccountConfigExample = new ShopeeAccountConfigExample();
        shopeeAccountConfigExample.setColumns("id, account, percent_pre_order_target, percent_pre_order_actual, account_type");
        ShopeeAccountConfigExample.Criteria criteria = shopeeAccountConfigExample.createCriteria();
        criteria.andAccountIsNotNull();
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountIn(accountNumberList);
        }
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectCustomColumnByExample(shopeeAccountConfigExample);
        for (ShopeeAccountConfig shopeeAccountConfig : shopeeAccountConfigs) {
            ShopeeExecutors.executeAccountDeleteItem(() -> {
                String account = shopeeAccountConfig.getAccount();
                if (StringUtils.isBlank(account)) {
                    return;
                }
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, true);
                boolean isNnShop = saleAccountAndBusinessResponse != null && BooleanUtils.isTrue(saleAccountAndBusinessResponse.getShopeeColBool3());
                XxlJobLogger.log("account:{} 开始删除停单存档且为单属性的", account);

                Map<String, String> skuAndStatusMap = new HashMap<>();
                Map<String, String> skuAndSpeciMap = new HashMap<>();
                Set<String> existDeleteItemIds = new HashSet<>();

                try {
                    EsShopeeItemRequest request = new EsShopeeItemRequest();
                    request.setSkuStatusList(List.of(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.DISCARD.getCode()));
                    if (CollectionUtils.isNotEmpty(skuList)) {
                        request.setArticleNumberList(skuList);
                    }
                    request.setQueryFields(new String[]{"id", "itemId", "articleNumber", "isGoods", "isFather", "specialGoodsCode"});
                    request.setItemSeller(account);
                    request.setOrderBy("id");
                    request.setIsGoods(true);
                    shopeeItemEsService.scrollQueryExecutorTask(request, listings -> {
                        if (CollectionUtils.isEmpty(listings)) {
                            return;
                        }

                        for (EsShopeeItem esShopeeItem : listings) {
                            JSONObject itemInfo = new JSONObject();
                            itemInfo.put("itemId", esShopeeItem.getItemId());
                            itemInfo.put("itemSeller", esShopeeItem.getItemSeller());

                            try {
                                if (esShopeeItem.getIsFather()) {
                                    // 检查 sku的 可用+在途-待发=0 就删除
                                    String articleNumber = esShopeeItem.getArticleNumber();
                                    if (StringUtils.isBlank(articleNumber)) {
                                        continue;
                                    }
                                    itemInfo.put("articleNumber", articleNumber);

                                    SkuSystemStock systemStock = SkuStockUtils.getMultipleSkuSystemStocks(articleNumber);
                                    if (systemStock == null) {
                                        throw new RuntimeException("redis 库存对象返回为空: 店铺：" + account + ",sku" + articleNumber);
                                    }
                                    itemInfo.put("usableStock", systemStock.getUsableStock());
                                    itemInfo.put("pendingStock", systemStock.getPendingStock());
                                    itemInfo.put("waitingOnWayStock", systemStock.getWaitingOnWayStock());
                                    Integer skuSystemStock = systemStock.getUsableWaitingPendingStock();
                                    if (skuSystemStock > 0) {
                                        continue;
                                    }
                                    String skuStatus = "";
                                    if (skuAndStatusMap.containsKey(articleNumber)) {
                                        skuStatus = skuAndStatusMap.get(articleNumber);
                                    } else {
                                        ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                                        skuStatus = productInfoVO.getSkuStatus();
                                        String specialGoodsCode = productInfoVO.getSpecialGoodsCode();
                                        if (StringUtils.isBlank(skuStatus)) {
                                            continue;
                                        }
                                        skuAndStatusMap.put(articleNumber, skuStatus);
                                        skuAndSpeciMap.put(articleNumber, specialGoodsCode);
                                    }
                                    itemInfo.put("skuStatus", skuStatus);
                                    String specialGoodsCode = skuAndSpeciMap.get(articleNumber);
                                    itemInfo.put("specialGoodsCode", Optional.ofNullable(specialGoodsCode).orElse(""));
                                    if (isNnShop) {
                                        if (StringUtils.isNotBlank(specialGoodsCode)) {
                                            List<Integer> specialGoodList = CommonUtils.splitIntList(specialGoodsCode, ",");
                                            if (CollectionUtils.isNotEmpty(specialGoodList) && specialGoodList.contains(SpecialTagEnum.s_2036.code)) {
                                                continue;
                                            }
                                        }
                                    }
                                    shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, itemInfo.toJSONString());
                                    if (skuStatus.equals(SkuStatusEnum.STOP.getCode()) || skuStatus.equals(SkuStatusEnum.ARCHIVED.getCode()) || skuStatus.equals(SkuStatusEnum.DISCARD.getCode())) {
                                        shopeeItemEsService.deleteItem(esShopeeItem.getItemId(), "admin", "停产存档废弃且为单属性，系统自动删除");
                                    }
                                } else {
                                    if (existDeleteItemIds.contains(esShopeeItem.getItemId())) {
                                        continue;
                                    }
                                    EsShopeeItemRequest itemRequest = new EsShopeeItemRequest();
                                    itemRequest.setQueryFields(new String[]{"id", "itemId", "articleNumber", "isGoods", "isFather", "skuStatus"});
                                    itemRequest.setItemSeller(shopeeAccountConfig.getAccount());
                                    itemRequest.setItemId(esShopeeItem.getItemId());
                                    itemRequest.setIsGoods(true);
                                    List<EsShopeeItem> esServiceEsShopeeItems = shopeeItemEsService.getEsShopeeItems(itemRequest);
                                    for (EsShopeeItem item : esServiceEsShopeeItems) {
                                        String articleNumber = item.getArticleNumber();
                                        itemInfo.put("articleNumber", articleNumber);
                                        SkuSystemStock systemStock = SkuStockUtils.getMultipleSkuSystemStocks(articleNumber);
                                        if (systemStock == null) {
                                            throw new RuntimeException("redis 库存对象返回为空: 店铺：" + account + ",sku" + articleNumber);
                                        }
                                        itemInfo.put("usableStock", systemStock.getUsableStock());
                                        itemInfo.put("pendingStock", systemStock.getPendingStock());
                                        itemInfo.put("waitingOnWayStock", systemStock.getWaitingOnWayStock());
                                        Integer skuSystemStock = systemStock.getUsableWaitingPendingStock();
                                        if (skuSystemStock > 0) {
                                            throw new BusinessException("库存大于0或者空,货号：" + articleNumber);
                                        }
                                        if (!SkuStatusEnum.isContainIllegalStatus(item.getSkuStatus())) {
                                            throw new BusinessException("刊登系统商品状态不满足停产、存档、废弃，不允许删除，货号：" + articleNumber);
                                        }

                                        String skuStatus = "";
                                        if (skuAndStatusMap.containsKey(articleNumber)) {
                                            skuStatus = skuAndStatusMap.get(articleNumber);
                                        } else {
                                            ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                                            skuStatus = productInfoVO.getSkuStatus();
                                            String specialGoodsCode = productInfoVO.getSpecialGoodsCode();
                                            if (StringUtils.isBlank(skuStatus)) {
                                                continue;
                                            }
                                            skuAndStatusMap.put(articleNumber, skuStatus);
                                            skuAndSpeciMap.put(articleNumber, specialGoodsCode);
                                        }
                                        String specialGoodsCode = skuAndSpeciMap.get(articleNumber);

                                        if (isNnShop) {
                                            if (StringUtils.isNotBlank(specialGoodsCode)) {
                                                List<Integer> specialGoodList = CommonUtils.splitIntList(specialGoodsCode, ",");
                                                if (CollectionUtils.isNotEmpty(specialGoodList) && specialGoodList.contains(SpecialTagEnum.s_2036.code)) {
                                                    throw new BusinessException("刊登系统商品标签不满足停产、存档、废弃，不允许删除，货号：" + articleNumber);
                                                }
                                            }
                                        }

                                        itemInfo.put("skuStatus", skuStatus);
                                        itemInfo.put("specialGoodsCode", Optional.ofNullable(specialGoodsCode).orElse(""));
                                        shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, itemInfo.toJSONString());

                                        if (!SkuStatusEnum.isContainIllegalStatus(skuStatus)) {
                                            throw new BusinessException("产品系统商品状态不满足停产、存档、废弃，不允许删除，货号：" + articleNumber);
                                        }
                                    }
                                    existDeleteItemIds.add(esShopeeItem.getItemId());
                                    shopeeItemEsService.deleteItem(esShopeeItem.getItemId(), "admin", "停产存档废弃且为多属性，系统自动删除");
                                }
                            } catch (Exception e) {
                                shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, e.getMessage());
                            }
                        }
                    });
                } catch (Exception e) {
                    XxlJobLogger.log("account:{}, 处理异常：{}", account, LogPrintUtil.getMinimumReverseStackCause(e));
                    shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, e.getMessage());
                }
            });
        }
        return ReturnT.SUCCESS;
    }
}
