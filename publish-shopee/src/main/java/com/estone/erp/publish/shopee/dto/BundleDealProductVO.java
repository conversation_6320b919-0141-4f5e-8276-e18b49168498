package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.ListStringFormatConverter;
import com.estone.erp.publish.elasticsearch2.model.ShopeeVariationOption;
import com.estone.erp.publish.system.infringement.response.InfringementWordInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2022/3/25
 */
@Data
@Document(indexName = "shopee_item", type = "esShopeeItem")
public class BundleDealProductVO {


    /**
     * 产品ID
     */
    private String itemId;

    /**
     * 标题
     */
    private String name;

    /**
     * 价格
     */
    private String price;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 物流
     */
    private String logistics;

    /**
     * 状态 1-启用 0-停用
     */
    private Integer status;

    /**
     * 是否报名
     */
    private Boolean isSignUp;

    /**
     * 站点
     */
    private String site;


}
