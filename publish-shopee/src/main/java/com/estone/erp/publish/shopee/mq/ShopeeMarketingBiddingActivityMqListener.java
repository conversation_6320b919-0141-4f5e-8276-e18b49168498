package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.shopee.dto.BiddingActivitySubmitExcelDTO;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBiddingActivityMessageDTO;
import com.estone.erp.publish.shopee.enums.ShopeeBiddingActivityEnums;
import com.estone.erp.publish.shopee.util.ExcelUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingActivity;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingActivitySubmitRecord;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBiddingActivityService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBiddingActivitySubmitRecordService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class ShopeeMarketingBiddingActivityMqListener implements ChannelAwareMessageListener {
    @Resource
    private ShopeeBiddingActivitySubmitRecordService shopeeBiddingActivitySubmitRecordService;

    @Resource
    private ShopeeBiddingActivityService shopeeBiddingActivityService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        ShopeeMarketingBiddingActivityMessageDTO messageDTO = JSON.parseObject(body, new TypeReference<ShopeeMarketingBiddingActivityMessageDTO>() {
        });
        String shopId = messageDTO.getShopId();
        LocalDate crawlDate = messageDTO.getCrawlDate();
        File file = null;
        try {
            LambdaQueryWrapper<ShopeeBiddingActivity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ShopeeBiddingActivity::getShopId, shopId);
            wrapper.between(ShopeeBiddingActivity::getCrawlTime, crawlDate.atStartOfDay(), crawlDate.atTime(LocalTime.MAX));
            wrapper.eq(ShopeeBiddingActivity::getProcessingStatus, ShopeeBiddingActivityEnums.ProcessingStatusEnum.PARTICIPATE.getCode());
            List<ShopeeBiddingActivity> activityList = shopeeBiddingActivityService.list(wrapper);
            if (CollectionUtils.isEmpty(activityList)) {
                log.warn("shopId:{}, 采集日期:{}, 数据为空不生成excel文件", shopId, crawlDate);
                return;
            }

            List<BiddingActivitySubmitExcelDTO> dataList = activityList.stream().map(activity -> {
                BiddingActivitySubmitExcelDTO dto = new BiddingActivitySubmitExcelDTO();
                BeanUtils.copyProperties(activity, dto);
                return dto;
            }).collect(Collectors.toList());

            byte[] excelBytes = ExcelUtils.buildBiddingActivityExcelBytes(dataList);
            String fileName = String.format("shopee_bidding_activity_submit_%s_%s", shopId, crawlDate);
            String suffix = ".xlsx";
            file = File.createTempFile(fileName, suffix);
            try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
                fileOutputStream.write(excelBytes);
            } catch (IOException e) {
                log.error("shopId:{}, 采集日期:{},竞价活动提报excel生成模板异常:{}", shopId, crawlDate, e.getMessage(), e);
            }

            // 调用文件上传方法并推送钉钉 shopeeExcel文件下能保留一个月
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName, "shopeeExcel", "admin");
            SeaweedFile result = uploadResult.getResult();
            String url = Optional.ofNullable(result).map(SeaweedFile::getUrl).orElse(null);
            if (!uploadResult.isSuccess() || StringUtils.isBlank(url)) {
                log.error("shopId:{}, 采集日期:{},竞价活动提报excel上传失败:{}", shopId, crawlDate, JSON.toJSONString(uploadResult));
                return;
            }
            String uniqueKeys = activityList.stream().map(ShopeeBiddingActivity::getUniqueKey).collect(Collectors.joining(","));
            ShopeeBiddingActivity activity = activityList.get(0);
            ShopeeBiddingActivitySubmitRecord record = new ShopeeBiddingActivitySubmitRecord();
            LocalDateTime currentDateTime = LocalDateTime.now();
            record.setSubAccount(activity.getSubAccount());
            record.setMerchantId(activity.getMerchantId());
            record.setMerchantName(activity.getMerchantName());
            record.setAccountNumber(activity.getAccountNumber());
            record.setShopId(activity.getShopId());
            record.setCrawlDate(crawlDate);
            record.setGeneratedFile(url);
            record.setUniqueKeys(uniqueKeys);
            record.setUploadFileStatus(ShopeeBiddingActivityEnums.UploadFileStatusEnum.TO_BE_UPLOADED.getCode());
            record.setCreatedTime(currentDateTime);
            record.setUpdateTime(currentDateTime);
            record.setSite(activity.getSite());
            shopeeBiddingActivitySubmitRecordService.save(record);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("shopId:{}, 采集日期:{}, 生成excel文件失败", shopId, crawlDate, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            if (null != file) {
                file.deleteOnExit();
            }
        }
    }

}
