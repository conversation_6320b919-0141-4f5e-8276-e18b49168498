package com.estone.erp.publish.shopee.api.v2.param.bundledeal;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-06-22 13:00
 **/
@NoArgsConstructor
@Data
public class BundleDealListVov2 {


    private Long bundle_deal_id;
    private String name;
    private Long start_time;
    private Long end_time;
    private BundleDealRuleDTO bundle_deal_rule;
    private Integer purchase_limit;

    @NoArgsConstructor
    @Data
    public static class BundleDealRuleDTO {
        private Integer rule_type;
        private Integer discount_value;
        private Integer fix_price;
        private Integer discount_percentage;
        private Integer min_amount;
        private List<AdditionalTiersDTO> additional_tiers;

        @NoArgsConstructor
        @Data
        public static class AdditionalTiersDTO {
            private Integer min_amount;
            private Integer fix_price;
            private Integer discount_value;
            private Integer discount_percentage;
        }
    }
}
