package com.estone.erp.publish.shopee.api.param.item.item;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiConstant;
import com.estone.erp.publish.shopee.api.param.IRequestUrlApiKey;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import java.util.ArrayList;
import java.util.List;

public class UnlistItemParam implements IRequestUrlApiKey {

    @JSONField(name = "partner_id")
    private Integer partnerId;

    @JSONField(name = "shopid")
    private Integer shopId;

    @JSONField(serialize = false)
    private String apiKey;

    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000;

    @JSONField(name = "items")
    private List<UnItem> items = new ArrayList<UnItem>();

    public UnlistItemParam(SaleAccountAndBusinessResponse account) {
        this.partnerId = Integer.valueOf(account.getColStr1());
        this.shopId = Integer.valueOf(account.getMarketplaceId());
        this.apiKey = account.getClientId();
    }

    @Override
    public String getRequestUrl() {
        return ShopeeApiConstant.ITEM_UPORDOWN;
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public List<UnItem> getItems() {
        return items;
    }

    public void setItems(List<UnItem> items) {
        this.items = items;
    }
}
