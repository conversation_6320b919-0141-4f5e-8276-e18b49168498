package com.estone.erp.publish.shopee.dto;

import com.estone.erp.publish.elasticsearch2.model.EsShopeeItemOffline;
import lombok.Data;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.HashMap;
import java.util.Map;

/**
 * @Auther yucm
 * @Date 2022/3/28
 */
@Setter
@Data
public class EsShopeeItemOfflineResponse {
    /**
     * es List
     */
    private Page<EsShopeeItemOffline> esShopeeItemOfflinePage ;

    private Map<String, ShopeeItemEsExtend> extendMap = new HashMap<>();
}
