package com.estone.erp.publish.shopee.api.v2.param.video;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-03-20 18:25
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GetVideoUploadResult implements RequestCommon {

    @JSONField(name = "video_upload_id")
    private String videoUploadId;


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.GET_VIDEO_UPLOAD_RESULT;
    }
}
