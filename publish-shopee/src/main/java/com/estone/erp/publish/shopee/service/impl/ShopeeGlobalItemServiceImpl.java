package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.api.v2.cnsc.global_item.cud.UpdateStockMtsku;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.call.v2.ShopeeUpdateStockPriceCallV2;
import com.estone.erp.publish.shopee.call.v2.cnsc.ShopeeDeleteGlobalItemCall;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeSystemParamEnums;
import com.estone.erp.publish.shopee.mapper.ShopeeGlobalItemMapper;
import com.estone.erp.publish.shopee.model.ShopeeGlobalItem;
import com.estone.erp.publish.shopee.model.ShopeeGlobalItemCriteria;
import com.estone.erp.publish.shopee.model.ShopeeGlobalItemExample;
import com.estone.erp.publish.shopee.model.ShopeeItem;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.service.ShopeeItemService;
import com.estone.erp.publish.shopee.util.CNSCPublishUtil;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> shopee_global_item
 * 2021-07-14 17:40:21
 */
@Service("shopeeGlobalItemService")
@Slf4j
public class ShopeeGlobalItemServiceImpl implements ShopeeGlobalItemService {
    @Resource
    private ShopeeGlobalItemMapper shopeeGlobalItemMapper;
    @Resource
    private ShopeeItemService shopeeItemService;
    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    @Override
    public int countByExample(ShopeeGlobalItemExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeGlobalItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeGlobalItem> search(CQuery<ShopeeGlobalItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeGlobalItemCriteria query = cquery.getSearch();
        ShopeeGlobalItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeGlobalItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeGlobalItem> shopeeGlobalItems = shopeeGlobalItemMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ShopeeGlobalItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeGlobalItems);
        return result;
    }

    @Override
    public ShopeeGlobalItem selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return shopeeGlobalItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeGlobalItem> selectByExample(ShopeeGlobalItemExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeGlobalItemMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeGlobalItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return shopeeGlobalItemMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeGlobalItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return shopeeGlobalItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeGlobalItem record, ShopeeGlobalItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return shopeeGlobalItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeGlobalItemMapper.deleteByPrimaryKey(ids);
    }

    /**
     *
     * @param cnscAccount 商家的账号信息
     * @param user
     * @param deleteShopeeItem
     */
    @Override
    public void deleteGlobalItem(SaleAccountAndBusinessResponse cnscAccount, String user, ShopeeItem deleteShopeeItem) {
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAccountNumber(cnscAccount.getAccountNumber());
            task.setAssociationId(deleteShopeeItem.getItemId());
            task.setArticleNumber(deleteShopeeItem.getArticleNumber());
            task.setTaskType(ShopeeFeedTaskEnum.DELETE_ITEM.getValue());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(user);
            task.setAttribute3("删除全球产品");
        });

        try {
            // 删除产品
            ShopeeResponse response = ShopeeDeleteGlobalItemCall.deleteGlobalItem(deleteShopeeItem.getItemId(), cnscAccount);
            if(StringUtils.isNotBlank(response.getError())){
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(response));
            }else{
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(response));
            }
        }catch (Exception e){
            log.error("删除全球产品出错：", e);
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
        }
    }

    @Override
    public ApiResult updateShopeeStock(SaleAccountAndBusinessResponse account, String user, EsShopeeItem updateShopeeItem, String taskType, String taskMsg) {
        return updateShopeeStock(account, user, updateShopeeItem, taskType, false, taskMsg);
    }

    @Override
    public ApiResult updateShopeeStock(SaleAccountAndBusinessResponse account, String user, EsShopeeItem updateShopeeItem, String taskType, String taskMsg, String ruleName) {
        return updateShopeeStockCode(account, user, updateShopeeItem, taskType, false, taskMsg, ruleName);
    }

    /**
     *
     * @param account 账号
     * @param user  用户
     * @param updateShopeeItem  item
     * @param taskType  类型
     * @param isAsync  是否异步执行
     * @param taskMsg 任务描述 用来处理报告区分开那个任务那种操作改的
     * @return
     */
    @Override
    public ApiResult updateShopeeStock(SaleAccountAndBusinessResponse account, String user, EsShopeeItem updateShopeeItem,
                                       String taskType, boolean isAsync, String taskMsg) {
        return updateShopeeStockCode(account, user, updateShopeeItem, taskType, isAsync, taskMsg, null);
    }

    /**
     * 库存修改
     *
     * @param account
     * @param user
     * @param updateShopeeItem
     * @param taskType
     * @param isAsync
     * @param taskMsg
     * @return
     */
    private ApiResult<?> updateShopeeStockCode(SaleAccountAndBusinessResponse account, String user, EsShopeeItem updateShopeeItem, String taskType, boolean isAsync, String taskMsg, String ruleName) {
        if(updateShopeeItem == null) {
            return ApiResult.newError("item is null");
        }

        EsShopeeItem originShopeeItem = shopeeItemEsService.findAllById(updateShopeeItem.getId());
        if(originShopeeItem == null) {
            return ApiResult.newError("es item is null");
        }
        //过滤变体父类
        if(originShopeeItem.getIsFather() && originShopeeItem.getHasVariation()){
            return ApiResult.newError(String.format("id%s 是变体父产品不执行", updateShopeeItem.getId()));
        }

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAssociationId(String.valueOf(originShopeeItem.getId()));
            task.setArticleNumber(originShopeeItem.getArticleNumber());
            task.setAccountNumber(originShopeeItem.getItemSeller());
            task.setTaskType(ShopeeFeedTaskEnum.UPDATE_STOCK.getValue());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(user);
            Optional.ofNullable(ruleName).ifPresent(task::setAttribute1);
            task.setAttribute2(taskType);
            task.setAttribute3(taskMsg);
            task.setAttribute4(originShopeeItem.getStock() + "");
            task.setAttribute5(updateShopeeItem.getStock() + "");
        });

        EsShopeeItem changeItem = new EsShopeeItem();
        changeItem.setId(originShopeeItem.getId());
        changeItem.setItemSeller(originShopeeItem.getItemSeller());
        changeItem.setGlobalItemId(originShopeeItem.getGlobalItemId());
        changeItem.setGlobalModelId(originShopeeItem.getGlobalModelId());
        changeItem.setItemId(originShopeeItem.getItemId());
        changeItem.setVariationId(originShopeeItem.getVariationId());
        changeItem.setArticleNumber(originShopeeItem.getArticleNumber());
        changeItem.setStock(updateShopeeItem.getStock());
        changeItem.setLastUpdatedBy(user);

        if(isAsync){
            ShopeeExecutors.executeUpdateStock(() -> {
                //异步执行
                try {
                    updateStock(account, user, originShopeeItem, feedTask, changeItem);
                }catch (Exception e){
                    log.error(String.format("item %s,更新库存出错", originShopeeItem.getId()), e);
                }
            });
        }else{
            try {
                updateStock(account, user, originShopeeItem, feedTask, changeItem);
            }catch (Exception e){
                log.error(String.format("item %s,更新库存出错", originShopeeItem.getId()), e);
                return ApiResult.newError(String.format("item %s,更新库存出错:%s", originShopeeItem.getId(), e.getMessage()));
            }
        }


        return ApiResult.newSuccess("后台已开始修改库存，请到处理报告查看处理结果");
    }

    private void updateStock(SaleAccountAndBusinessResponse account, String user, EsShopeeItem originShopeeItem, FeedTask feedTask, EsShopeeItem changeItem) {
        ShopeeResponse response = null;

        // 非海外仓店铺使用Global
        if (BooleanUtils.isFalse(account.getOverseaWarehouse())) {
            SaleAccountAndBusinessResponse cnscAccount = null;
            try {
                cnscAccount = CNSCPublishUtil.transCNSCAccount(account);
            }catch (Exception e){
                log.error(String.format("account %s get error", changeItem.getItemSeller()),e);
                response = new ShopeeResponse();
                response.setError(String.format("account %s get error:%s", changeItem.getItemSeller(), e.getMessage()));
            }
            UpdateStockMtsku param = new UpdateStockMtsku(CommonUtils.arrayAsList(changeItem));
            if(cnscAccount != null){
                String updateStockSwitch = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, ShopeeSystemParamEnums.SHOPEE_UPDATE_STOCK.getCode(), ShopeeSystemParamEnums.SHOPEE_UPDATE_STOCK.getParamKey(), null);
                if (StringUtils.isNotBlank(updateStockSwitch) && !Boolean.parseBoolean(updateStockSwitch)) {
                    response = new ShopeeResponse();
                    response.setError("更新库存功能未开启");

                } else {
                    //请求接口
                    response = ShopeeHttpUtils.doPostV2(cnscAccount, param);
                }
            }
            if(response == null){
                response = new ShopeeResponse();
                response.setError("更新库存失败,接口返回体为空");
            }

            boolean isSuccess = StringUtils.isBlank(response.getError());
            if(isSuccess){
                feedTask.setResultMsg("修改前库存：" + originShopeeItem.getStock() + "，修改后库存：" + changeItem.getStock());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());

                EsShopeeItem localShopeeItem = shopeeItemEsService.findAllById(originShopeeItem.getId());
                if(originShopeeItem != null) {
                    localShopeeItem.setStock(changeItem.getStock());
                    localShopeeItem.setLastUpdatedBy(user);
                    localShopeeItem.setLastUpdateDate(new Date());
                    shopeeItemEsService.save(localShopeeItem);
                }
            }else{
                feedTask.setResultMsg(JSON.toJSONString(response));
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            }
            feedTask.setAttribute2(JSON.toJSONString(param));
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
        }else{
            //普通listing
//            CNSCPublishUtil.checkAccountExpire(account);
            //请求接口
            response = ShopeeUpdateStockPriceCallV2.updateStockCall(account, changeItem);

            feedTask.setAttribute2(JSON.toJSONString(changeItem));
            if(StringUtils.isNotBlank(response.getError())){

                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(response));
            }else{
                JSONObject json = JSON.parseObject(response.getResponse());
                JSONArray success_list = json.getJSONArray("success_list");
                if(success_list != null && success_list.size() > 0){
                    Integer normal_stock = success_list.getJSONObject(0).getInteger("normal_stock");

                    String msg = "修改前库存：" + originShopeeItem.getStock() + "，修改后库存：" + normal_stock;
                    ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), msg);

                    EsShopeeItem localShopeeItem = shopeeItemEsService.findAllById(originShopeeItem.getId());
                    if(originShopeeItem != null) {
                        localShopeeItem.setStock(changeItem.getStock());
                        localShopeeItem.setLastUpdatedBy(user);
                        localShopeeItem.setLastUpdateDate(new Date());
                        shopeeItemEsService.save(localShopeeItem);
                    }
                }
                JSONArray failure_list = json.getJSONArray("failure_list");
                if(failure_list != null && failure_list.size() > 0){
                    ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(response));
                }
            }
        }
    }

}