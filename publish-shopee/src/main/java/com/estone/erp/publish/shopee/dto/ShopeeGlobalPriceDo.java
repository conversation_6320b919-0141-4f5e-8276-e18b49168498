package com.estone.erp.publish.shopee.dto;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class ShopeeGlobalPriceDo {

    /**
     * sku
     */
    private String sku;
    /**
     * 销售成本
     */
    private BigDecimal salePrice;

    /**
     * 包材价格
     */
    private BigDecimal packingPrice;

    /**
     * 搭配包材成本
     */
    private BigDecimal matchPrice;

    /**
     * 运费
     */
    private BigDecimal sellerPaysFreight;

    /**
     * 全球
     */
    private Double priceConfig;

    /**
     * 店铺毛利率
     */
    private Double ratio;

    private BigDecimal productWeight;
    private BigDecimal packingWeight;
    private BigDecimal matchWeight;
    private BigDecimal addWeight;
    private boolean filter;


    /**
     * 最大重量
     */
    private BigDecimal maxWeight;
    private String maxWeightSku;
    /**
     * 总价
     */
    private Double price;
}
