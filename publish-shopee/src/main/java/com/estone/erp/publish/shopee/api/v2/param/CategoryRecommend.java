package com.estone.erp.publish.shopee.api.v2.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/1 11:33
 * @description 按项目名称获取推荐类别
 */
@Getter
@Setter
public class CategoryRecommend implements RequestCommon {

    @JSONField(name = "item_name")
    private String itemName;

    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.CATEGORY_RECOMMEND;
    }
}
