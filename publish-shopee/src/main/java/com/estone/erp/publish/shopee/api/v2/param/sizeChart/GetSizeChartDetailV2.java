package com.estone.erp.publish.shopee.api.v2.param.sizeChart;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2CnscConstant;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import lombok.Data;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/15 下午3:16
 */
@Data
public class GetSizeChartDetailV2 implements RequestCommon {

    @JSONField(name = "size_chart_id")
    private Integer sizeChartId;

    @JSONField(name = "language")
    private Integer language;

    @Override
    public String requestUrl() {
        return ShopeeApiV2CnscConstant.GET_SIZE_CHART_DETAIL;
    }
}
