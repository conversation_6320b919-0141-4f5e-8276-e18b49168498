<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.shopee.mapper.ShopeeGlobalPublishQueueMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueue" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="template_id" property="templateId" jdbcType="BIGINT" />
    <result column="sub_account" property="subAccount" jdbcType="VARCHAR" />
    <result column="merchant" property="merchant" jdbcType="VARCHAR" />
    <result column="merchant_id" property="merchantId" jdbcType="VARCHAR" />
    <result column="accounts" property="accounts" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="log_id" property="logId" jdbcType="BIGINT" />
    <result column="publish_result" property="publishResult" jdbcType="INTEGER" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="copy_writing_type" property="copyWritingType" jdbcType="INTEGER" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
    <result column="rule_json" property="ruleJson" jdbcType="VARCHAR"/>
    <result column="config_source" property="configSource" jdbcType="VARCHAR"/>
    <result column="article_numbers" property="articleNumbers" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_id, sub_account, merchant, merchant_id, accounts, title, sku, `status`, 
    start_time, end_time, `type`, publish_role, log_id, publish_result, creation_date,
    created_by,
    last_update_date,
    last_updated_by,
    remark,
    copy_writing_type,
    sku_data_source,
    rule_name,
    rule_json,
    config_source,
    article_numbers
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueueExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="customColumn != null and customColumn != ''">
        ${customColumn}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from shopee_global_publish_queue
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from shopee_global_publish_queue
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from shopee_global_publish_queue
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueueExample" >
    delete from shopee_global_publish_queue
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueue" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopee_global_publish_queue (template_id, sub_account, merchant, 
      merchant_id, accounts, title, 
      sku, `status`, start_time, 
      end_time, `type`, publish_role, 
      log_id, publish_result, creation_date, 
      created_by, last_update_date, last_updated_by,
    remark, copy_writing_type, sku_data_source, rule_name, rule_json, config_source, article_numbers)
    values (#{templateId,jdbcType=BIGINT}, #{subAccount,jdbcType=VARCHAR}, #{merchant,jdbcType=VARCHAR}, 
      #{merchantId,jdbcType=VARCHAR}, #{accounts,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{sku,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}, #{publishRole,jdbcType=INTEGER}, 
      #{logId,jdbcType=BIGINT}, #{publishResult,jdbcType=INTEGER}, #{creationDate,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{copyWritingType,javaType=INTEGER}, #{skuDataSource,jdbcType=INTEGER},
    #{ruleJson,jdbcType=VARCHAR}, #{configSource,jdbcType=VARCHAR},
    #{ruleName,jdbcType=VARCHAR},#{articleNumbers,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueueExample" resultType="java.lang.Integer" >
    select count(*) from shopee_global_publish_queue
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="getT1ExecutionDetailsByAccountNumber"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealDetailsDTO">
    SELECT accounts                                                  AS account,
           rule_name                                                 AS ruleName,
           COUNT(CASE WHEN publish_result IN (1, 5, 4) THEN 1 END)   AS totalNum,
           SUM(CASE WHEN publish_result IN (1, 5) THEN 1 ELSE 0 END) AS successNum,
           SUM(CASE WHEN publish_result = 4 THEN 1 ELSE 0 END)       AS failNum
    FROM shopee_global_publish_queue
    WHERE end_time IS NOT NULL
      AND REGEXP_REPLACE(accounts, '&amp;', ',') REGEXP CONCAT('(^|,)', #{account}
        , '(,|$)')
      AND end_time <![CDATA[>=]]> #{startTime}
      AND end_time <![CDATA[<=]]> #{endTime}
    GROUP BY ruleName;
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update shopee_global_publish_queue
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.subAccount != null" >
        sub_account = #{record.subAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.merchant != null" >
        merchant = #{record.merchant,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null" >
        merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.accounts != null" >
        accounts = #{record.accounts,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null" >
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null" >
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.logId != null" >
        log_id = #{record.logId,jdbcType=BIGINT},
      </if>
      <if test="record.publishResult != null" >
        publish_result = #{record.publishResult,jdbcType=INTEGER},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.copyWritingType != null" >
        cope_writing_type = #{record.copyWritingType,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null">
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleJson != null">
        rule_json = #{record.ruleJson,jdbcType=VARCHAR},
      </if>
      <if test="record.configSource != null">
        config_source = #{record.configSource,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumbers != null">
        article_numbers = #{record.articleNumbers,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueue" >
    update shopee_global_publish_queue
    <set >
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="subAccount != null" >
        sub_account = #{subAccount,jdbcType=VARCHAR},
      </if>
      <if test="merchant != null" >
        merchant = #{merchant,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null" >
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="accounts != null" >
        accounts = #{accounts,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="logId != null" >
        log_id = #{logId,jdbcType=BIGINT},
      </if>
      <if test="publishResult != null" >
        publish_result = #{publishResult,jdbcType=INTEGER},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="copyWritingType != null" >
        copy_writing_type = #{copyWritingType,jdbcType=INTEGER},
      </if>
      <if test="skuDataSource != null">
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="ruleJson != null">
        rule_json = #{ruleJson,jdbcType=VARCHAR},
      </if>
      <if test="configSource != null">
        config_source = #{configSource,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="articleNumbers != null">
        article_numbers = #{articleNumbers,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueue" >
    insert into shopee_global_publish_queue (template_id, sub_account, merchant,
    merchant_id, accounts, title,
    sku, `status`, start_time,
    end_time, `type`, publish_role,
    log_id, publish_result, creation_date,
    created_by, last_update_date, last_updated_by,
    remark,copy_writing_type, sku_data_source, rule_name, rule_json, config_source, article_numbers)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.templateId,jdbcType=BIGINT}, #{item.subAccount,jdbcType=VARCHAR}, #{item.merchant,jdbcType=VARCHAR},
      #{item.merchantId,jdbcType=VARCHAR}, #{item.accounts,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR},
      #{item.sku,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.startTime,jdbcType=TIMESTAMP},
      #{item.endTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=INTEGER}, #{item.publishRole,jdbcType=INTEGER},
      #{item.logId,jdbcType=BIGINT}, #{item.publishResult,jdbcType=INTEGER}, #{item.creationDate,jdbcType=TIMESTAMP},
      #{item.createdBy,jdbcType=VARCHAR}, #{item.lastUpdateDate,jdbcType=TIMESTAMP}, #{item.lastUpdatedBy,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR},#{item.copyWritingType,jdbcType=INTEGER},
      #{item.skuDataSource, jdbcType=INTEGER}, #{item.ruleName,jdbcType=VARCHAR}, #{item.ruleJson,jdbcType=VARCHAR},
      #{item.configSource,jdbcType=VARCHAR}, #{item.articleNumbers,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="batchUpdate">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      update shopee_global_publish_queue
      <set >
        <if test="item.templateId != null" >
          template_id = #{item.templateId,jdbcType=BIGINT},
        </if>
        <if test="item.subAccount != null" >
          sub_account = #{item.subAccount,jdbcType=VARCHAR},
        </if>
        <if test="item.merchant != null" >
          merchant = #{item.merchant,jdbcType=VARCHAR},
        </if>
        <if test="item.merchantId != null" >
          merchant_id = #{item.merchantId,jdbcType=VARCHAR},
        </if>
        <if test="item.accounts != null" >
          accounts = #{item.accounts,jdbcType=VARCHAR},
        </if>
        <if test="item.title != null" >
          title = #{item.title,jdbcType=VARCHAR},
        </if>
        <if test="item.sku != null" >
          sku = #{item.sku,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null" >
          `status` = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.startTime != null" >
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null" >
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.type != null" >
          `type` = #{item.type,jdbcType=INTEGER},
        </if>
        <if test="item.publishRole != null" >
          publish_role = #{item.publishRole,jdbcType=INTEGER},
        </if>
        <if test="item.logId != null" >
          log_id = #{item.logId,jdbcType=BIGINT},
        </if>
        <if test="item.publishResult != null" >
          publish_result = #{item.publishResult,jdbcType=INTEGER},
        </if>
        <if test="item.creationDate != null" >
          creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastUpdateDate != null" >
          last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null" >
          last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.copyWritingType != null" >
          copy_writing_type = #{item.copyWritingType,jdbcType=INTEGER},
        </if>
        <if test="item.skuDataSource != null">
          sku_data_source = #{item.skuDataSource,jdbcType=INTEGER},
        </if>
        <if test="item.ruleJson != null">
          rule_json = #{item.ruleJson,jdbcType=VARCHAR},
        </if>
        <if test="item.configSource != null">
          config_source = #{item.configSource,jdbcType=VARCHAR},
        </if>
        <if test="item.ruleName != null">
          rule_name = #{item.ruleName,jdbcType=VARCHAR},
        </if>
        <if test="item.articleNumbers != null">
          article_numbers = #{item.articleNumbers,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <delete id="batchDelete">
    delete from shopee_global_publish_queue
    where id in
    <foreach close=")" collection="list" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </delete>

  <update id="updateStatusByTemplate" parameterType="com.estone.erp.publish.shopee.model.ShopeeGlobalPublishQueue" >
    update shopee_global_publish_queue
    <set >
      <if test="subAccount != null" >
        sub_account = #{subAccount,jdbcType=VARCHAR},
      </if>
      <if test="merchant != null" >
        merchant = #{merchant,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null" >
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="accounts != null" >
        accounts = #{accounts,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="logId != null" >
        log_id = #{logId,jdbcType=BIGINT},
      </if>
      <if test="publishResult != null" >
        publish_result = #{publishResult,jdbcType=INTEGER},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="copyWritingType != null" >
        copy_writing_type = #{copyWritingType,jdbcType=INTEGER},
      </if>
      <if test="skuDataSource != null">
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="ruleJson != null">
        rule_json = #{ruleJson,jdbcType=VARCHAR},
      </if>
      <if test="configSource != null">
        config_source = #{configSource,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="articleNumbers != null">
        article_numbers = #{articleNumbers,jdbcType=VARCHAR},
      </if>
    </set>
    where template_id = #{templateId,jdbcType=BIGINT}
  </update>
</mapper>