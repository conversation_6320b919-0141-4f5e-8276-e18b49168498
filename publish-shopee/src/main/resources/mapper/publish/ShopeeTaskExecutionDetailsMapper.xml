<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.shopee.mapper.ShopeeTaskExecutionDetailsMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="plate" property="plate" jdbcType="INTEGER" />
    <result column="config_type" property="configType" jdbcType="INTEGER" />
    <result column="operation_type" property="operationType" jdbcType="INTEGER" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="total_num" property="totalNum" jdbcType="INTEGER" />
    <result column="success_num" property="successNum" jdbcType="INTEGER" />
    <result column="fail_num" property="failNum" jdbcType="INTEGER" />
    <result column="count_time" property="countTime" jdbcType="TIMESTAMP" />
    <result column="exec_time" property="execTime" jdbcType="TIMESTAMP" />
    <result column="account_group_id" property="accountGroupId" jdbcType="INTEGER" />
    <result column="account_group_name" property="accountGroupName" jdbcType="VARCHAR"/>
    <result column="sale" property="sale" jdbcType="VARCHAR" />
    <result column="sale_leader" property="saleLeader" jdbcType="VARCHAR" />
    <result column="sale_supervisor" property="saleSupervisor" jdbcType="VARCHAR" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, plate, config_type, operation_type, rule_name, account, total_num, success_num, 
    fail_num, count_time, exec_time, account_group_id, account_group_name, sale, sale_leader, sale_supervisor,
    created_at
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetailsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from shopee_task_execution_details
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from shopee_task_execution_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from shopee_task_execution_details
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopee_task_execution_details (plate, config_type, operation_type, 
      rule_name, account, total_num, 
      success_num, fail_num, count_time,
    exec_time, account_group_id, account_group_name, sale,
      sale_leader, sale_supervisor, created_at
      )
    values (#{plate,jdbcType=INTEGER}, #{configType,jdbcType=INTEGER}, #{operationType,jdbcType=INTEGER}, 
      #{ruleName,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{totalNum,jdbcType=INTEGER}, 
      #{successNum,jdbcType=INTEGER}, #{failNum,jdbcType=INTEGER}, #{countTime,jdbcType=TIMESTAMP},
    #{execTime,jdbcType=TIMESTAMP}, #{accountGroupId,jdbcType=INTEGER}, #{accountGroupName,jdbcType=VARCHAR},
    #{sale,jdbcType=VARCHAR},
      #{saleLeader,jdbcType=VARCHAR}, #{saleSupervisor,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertBatch">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopee_task_execution_details (plate, config_type, operation_type,
    rule_name, account, total_num,
    success_num, fail_num, count_time,
    exec_time, account_group_id, account_group_name, sale,
    sale_leader, sale_supervisor, created_at
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.plate,jdbcType=INTEGER}, #{item.configType,jdbcType=INTEGER}, #{item.operationType,jdbcType=INTEGER},
      #{item.ruleName,jdbcType=VARCHAR}, #{item.account,jdbcType=VARCHAR}, #{item.totalNum,jdbcType=INTEGER},
      #{item.successNum,jdbcType=INTEGER}, #{item.failNum,jdbcType=INTEGER}, #{item.countTime,jdbcType=TIMESTAMP},
      #{item.execTime,jdbcType=TIMESTAMP}, #{item.accountGroupId,jdbcType=INTEGER},
      #{item.accountGroupName,jdbcType=VARCHAR}, #{item.sale,jdbcType=VARCHAR},
      #{item.saleLeader,jdbcType=VARCHAR}, #{item.saleSupervisor,jdbcType=VARCHAR},
      #{item.createdAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetailsExample" resultType="java.lang.Integer" >
    select count(*) from shopee_task_execution_details
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="getRuleNameList" resultType="java.lang.String">
    SELECT `name` FROM shopee_marketing_config
    UNION ALL
        SELECT `name` FROM shopee_listing_status_config
    UNION ALL
        SELECT rule_name AS `name` FROM shopee_link_management_config
  </select>
  <select id="getT1ExecutionDetailsByAccountNumber"
          resultType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails">
    SELECT account_number                                     AS account,-- 店铺
           attribute1                                         AS ruleName,-- 规则名称
           COUNT(1)                                           AS totalNum,-- 处理总数据量
           SUM(CASE WHEN result_status = 1 THEN 1 ELSE 0 END) AS successNum,-- 执行成功数量
           SUM(CASE WHEN result_status = 2 THEN 1 ELSE 0 END) AS failNum -- 执行失败数量
    FROM feed_task_3
    WHERE task_type = #{code}
    <if test="accountNumber != null">
      AND account_number = #{accountNumber}
    </if>
    <if test="sonAccountNumber == true">
      AND account_number != attribute5
    </if>
    AND finish_time <![CDATA[>=]]> #{startTime}
    AND finish_time <![CDATA[<=]]> #{endTime}
    GROUP BY account_number, attribute1
    ORDER BY account_number;
  </select>

  <select id="getTaskExecutionDetails"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionTotalDTO">
    SELECT
    `operation_type` AS 'operationType',
    COUNT( DISTINCT CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN account END ) AS 'yesterdayStores',
    COUNT( DISTINCT CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN account END ) AS 'lastSevenStores',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN total_num ELSE 0 END ) AS 'yesterdayTotalNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN total_num ELSE 0 END ) AS 'lastSevenTotalNum',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN success_num ELSE 0 END ) AS
    'yesterdaySuccessNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN success_num ELSE 0 END ) AS
    'lastSevenSuccessNum',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN fail_num ELSE 0 END ) AS 'yesterdayFailNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN fail_num ELSE 0 END ) AS 'lastSevenFailNum',
    sale AS 'sale',
    sale_leader AS 'saleLeader',
    sale_supervisor AS 'saleSupervisor'
    FROM
    shopee_task_execution_details
    WHERE
    count_time >= CURDATE() - INTERVAL 7 DAY AND operation_type NOT IN ( 45, 54 ) AND sale IS NOT NULL AND sale_leader
    != '' AND sale_supervisor != ''
    <if test="dto.operationTypeList != null and dto.operationTypeList.size > 0">
      AND operation_type IN
      <foreach item="operationType" collection="dto.operationTypeList" open="(" separator="," close=")">
        #{operationType}
      </foreach>
    </if>
    <if test="dto.accountList != null and dto.accountList.size > 0">
      AND account IN
      <foreach item="account" collection="dto.accountList" open="(" separator="," close=")">
        #{account}
      </foreach>
    </if>
    <if test="dto.ruleNameType != null">
      <if test="dto.ruleNameType == 1">
        AND rule_name IS NOT NULL
      </if>
      <if test="dto.ruleNameType == 2">
        AND rule_name IS NULL
      </if>
    </if>
    <if test="dto.saleType != null">
      <if test="dto.saleType == 1">
        <if test="dto.saleList != null and dto.saleList.size > 0">
          AND sale IN
          <foreach item="sale" collection="dto.saleList" open="(" separator="," close=")">
            #{sale}
          </foreach>
        </if>
      </if>
      <if test="dto.saleType == 2">
        <if test="dto.saleList != null and dto.saleList.size > 0">
          AND sale IN
          <foreach item="sale" collection="dto.saleList" open="(" separator="," close=")">
            #{sale}
          </foreach>
        </if>
        <if test="dto.saleLeaderList != null and dto.saleLeaderList.size > 0">
          AND sale_leader IN
          <foreach item="dto.saleLeader" collection="dto.saleLeaderList" open="(" separator="," close=")">
            #{saleLeader}
          </foreach>
        </if>
      </if>
      <if test="dto.saleType == 3">
        <if test="dto.saleList != null and dto.saleList.size > 0">
          AND sale IN
          <foreach item="sale" collection="dto.saleList" open="(" separator="," close=")">
            #{sale}
          </foreach>
        </if>
        <if test="dto.saleLeaderList != null and dto.saleLeaderList.size > 0">
          AND sale_leader IN
          <foreach item="saleLeader" collection="dto.saleLeaderList" open="(" separator="," close=")">
            #{saleLeader}
          </foreach>
        </if>
        <if test="dto.salesSupervisorList != null and dto.salesSupervisorList.size > 0">
          AND sale_supervisor IN
          <foreach item="saleSupervisor" collection="dto.salesSupervisorList" open="(" separator="," close=")">
            #{saleSupervisor}
          </foreach>
        </if>
      </if>
    </if>
    GROUP BY
    operation_type
    <if test="dto.saleType != 0">
      <choose>
        <when test="dto.saleType == 1">
          ,`sale`
        </when>
        <when test="dto.saleType == 2">
          ,`sale_leader`
        </when>
        <otherwise>
          ,`sale_supervisor`
        </otherwise>
      </choose>
    </if>
    ORDER BY id DESC
  </select>

  <sql id="Base_Column_List_For">
    count_time >= CURDATE() - INTERVAL 7 DAY AND operation_type NOT IN ( 45, 54 ) AND sale IS NOT NULL AND sale_leader
    != '' AND sale_supervisor != ''
    <if test="dto.operationTypeList != null and dto.operationTypeList.size > 0">
      AND operation_type IN
      <foreach item="operationType" collection="dto.operationTypeList" open="(" separator="," close=")">
        #{operationType}
      </foreach>
    </if>
    <if test="dto.accountList != null and dto.accountList.size > 0">
      AND account IN
      <foreach item="account" collection="dto.accountList" open="(" separator="," close=")">
        #{account}
      </foreach>
    </if>
    <if test="dto.ruleNameType != null">
      <if test="dto.ruleNameType == 1">
        AND rule_name IS NOT NULL
      </if>
      <if test="dto.ruleNameType == 2">
        AND rule_name IS NULL
      </if>
    </if>
    <if test="dto.saleType != null">
      <if test="dto.saleType == 1">
        <if test="dto.saleList != null and dto.saleList.size > 0">
          AND sale IN
          <foreach item="sale" collection="dto.saleList" open="(" separator="," close=")">
            #{sale}
          </foreach>
        </if>
      </if>
      <if test="dto.saleType == 2">
        <if test="dto.saleLeaderList != null and dto.saleLeaderList.size > 0">
          AND sale_leader IN
          <foreach item="saleLeader" collection="dto.saleLeaderList" open="(" separator="," close=")">
            #{saleLeader}
          </foreach>
        </if>
      </if>
      <if test="dto.saleType == 3">
        <if test="dto.salesSupervisorList != null and dto.salesSupervisorList.size > 0">
          AND sale_supervisor IN
          <foreach item="saleSupervisor" collection="dto.salesSupervisorList" open="(" separator="," close=")">
            #{saleSupervisor}
          </foreach>
        </if>
      </if>
    </if>
    GROUP BY
    operation_type
    <if test="dto.saleType != 0">
      <choose>
        <when test="dto.saleType == 1">
          ,`sale`
        </when>
        <when test="dto.saleType == 2">
          ,`sale_leader`
        </when>
        <otherwise>
          ,`sale_supervisor`
        </otherwise>
      </choose>
    </if>
    ORDER BY id DESC
  </sql>

  <select id="getTaskExecutionDetailPageCount" resultType="java.lang.Integer">
    SELECT COUNT(*) AS total_count
    FROM (SELECT * FROM shopee_task_execution_details WHERE
    <include refid="Base_Column_List_For"/>
    ) AS grouped_data;
  </select>

  <select id="getTaskExecutionDetailPages"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionTotalDTO">
    SELECT
    `operation_type` AS 'operationType',
    COUNT( DISTINCT CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN account END ) AS 'yesterdayStores',
    COUNT( DISTINCT CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN account END ) AS 'lastSevenStores',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN total_num ELSE 0 END ) AS 'yesterdayTotalNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN total_num ELSE 0 END ) AS 'lastSevenTotalNum',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN success_num ELSE 0 END ) AS
    'yesterdaySuccessNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN success_num ELSE 0 END ) AS
    'lastSevenSuccessNum',
    SUM( CASE WHEN DATE ( count_time ) = CURDATE() - INTERVAL 1 DAY THEN fail_num ELSE 0 END ) AS 'yesterdayFailNum',
    SUM( CASE WHEN DATE ( count_time ) >= CURDATE() - INTERVAL 7 DAY THEN fail_num ELSE 0 END ) AS 'lastSevenFailNum',
    sale AS 'sale',
    sale_leader AS 'saleLeader',
    sale_supervisor AS 'saleSupervisor'
    FROM
    shopee_task_execution_details
    WHERE
    <include refid="Base_Column_List_For"/>
    <if test="dto.limit != null">
      <if test="dto.offset != null">
        limit ${dto.offset}, ${dto.limit}
      </if>
      <if test="dto.offset == null">
        limit ${dto.limit}
      </if>
    </if>
  </select>

  <select id="getTaskExecutionDetailsByDay"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionHistoryVO">
    SELECT
    DATE(count_time) AS `date`,
    SUM(total_num) AS totalNum,
    SUM(success_num) AS successNum
    FROM
    shopee_task_execution_details
    WHERE
    operation_type = #{operationType} AND sale IS NOT NULL
    AND count_time <![CDATA[>=]]> #{startDate}
    AND count_time <![CDATA[<=]]> #{endDate}
    <if test="accountList != null and accountList.size > 0">
      AND account IN
      <foreach item="account" collection="accountList" open="(" separator="," close=")">
        #{account}
      </foreach>
    </if>
    <if test="ruleNameType == 1">
      AND rule_name IS NOT NULL
    </if>
    <if test="ruleNameType == 2">
      AND rule_name IS NULL
    </if>
    <if test="salesNumber != null">
      <if test="saleType == 1">
        AND sale = #{salesNumber}
      </if>
      <if test="saleType == 2">
        AND sale_leader = #{salesNumber}
      </if>
      <if test="saleType == 3">
        AND sale_supervisor = #{salesNumber}
      </if>
    </if>
    GROUP BY count_time
    ORDER BY count_time
  </select>

  <select id="getTaskExecutionDetailsByWeek"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionHistoryVO">
    SELECT
    CONCAT(SUBSTR(YEARWEEK(count_time, 1), 1, 4), '-', SUBSTR(YEARWEEK(count_time, 1), 5, 2)) AS `date`,
    SUM(total_num) AS totalNum,
    SUM(success_num) AS successNum
    FROM
    shopee_task_execution_details
    WHERE
    operation_type = #{operationType} AND sale IS NOT NULL
    AND count_time <![CDATA[>=]]> #{startDate} AND count_time <![CDATA[<]]> #{endDate}
    <if test="accountList != null and accountList.size > 0">
      AND account IN
      <foreach item="account" collection="accountList" open="(" separator="," close=")">
        #{account}
      </foreach>
    </if>
    <if test="ruleNameType == 1">
      AND rule_name IS NOT NULL
    </if>
    <if test="ruleNameType == 2">
      AND rule_name IS NULL
    </if>
    <if test="salesNumber != null">
      <if test="saleType == 1">
        AND sale = #{salesNumber}
      </if>
      <if test="saleType == 2">
        AND sale_leader = #{salesNumber}
      </if>
      <if test="saleType == 3">
        AND sale_supervisor = #{salesNumber}
      </if>
    </if>
    GROUP BY YEARWEEK(count_time, 1)
    ORDER BY YEARWEEK(count_time, 1);
  </select>

  <select id="getTaskExecutionDetailsByMonth"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionHistoryVO">
    SELECT
    DATE_FORMAT(count_time, '%Y-%m-01') AS `date`,
    SUM(total_num) AS totalNum,
    SUM(success_num) AS successNum
    FROM
    shopee_task_execution_details
    WHERE
    operation_type = #{operationType} AND sale IS NOT NULL
    AND count_time <![CDATA[>=]]> #{startDate} AND count_time <![CDATA[<]]> #{endDate}
    <if test="accountList != null and accountList.size > 0">
      AND account IN
      <foreach item="account" collection="accountList" open="(" separator="," close=")">
        #{account}
      </foreach>
    </if>
    <if test="ruleNameType == 1">
      AND rule_name IS NOT NULL
    </if>
    <if test="ruleNameType == 2">
      AND rule_name IS NULL
    </if>
    <if test="salesNumber != null">
      <if test="saleType == 1">
        AND sale = #{salesNumber}
      </if>
      <if test="saleType == 2">
        AND sale_leader = #{salesNumber}
      </if>
      <if test="saleType == 3">
        AND sale_supervisor = #{salesNumber}
      </if>
    </if>
    GROUP BY DATE(DATE_FORMAT(count_time, '%Y-%m-01'))
    ORDER BY DATE(DATE_FORMAT(count_time, '%Y-%m-01'));
  </select>

  <select id="getT1ExecutionDetailsDistinctByAccountNumber"
          resultType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails">
    SELECT account_number AS account,-- 店铺
    attribute1 AS ruleName,-- 规则名称
    COUNT(DISTINCT article_number) AS totalNum,-- 处理总数据量
    COUNT(DISTINCT CASE WHEN result_status = 1 THEN article_number END) AS successNum,-- 执行成功数量
    COUNT(DISTINCT CASE WHEN result_status = 2 THEN article_number END) AS failNum -- 执行失败数量
    FROM feed_task_3
    WHERE task_type = #{code}
    <if test="accountNumber != null">
      AND account_number = #{accountNumber}
    </if>
    <if test="sonAccountNumber == true">
      AND account_number != attribute5
    </if>
    AND finish_time <![CDATA[>=]]> #{startTime}
    AND finish_time <![CDATA[<=]]> #{endTime}
    GROUP BY account_number, attribute1
    ORDER BY account_number;
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update shopee_task_execution_details
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.plate != null" >
        plate = #{record.plate,jdbcType=INTEGER},
      </if>
      <if test="record.configType != null" >
        config_type = #{record.configType,jdbcType=INTEGER},
      </if>
      <if test="record.operationType != null" >
        operation_type = #{record.operationType,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.totalNum != null" >
        total_num = #{record.totalNum,jdbcType=INTEGER},
      </if>
      <if test="record.successNum != null" >
        success_num = #{record.successNum,jdbcType=INTEGER},
      </if>
      <if test="record.failNum != null" >
        fail_num = #{record.failNum,jdbcType=INTEGER},
      </if>
      <if test="record.countTime != null" >
        count_time = #{record.countTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.execTime != null" >
        exec_time = #{record.execTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountGroupId != null" >
        account_group_id = #{record.accountGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.accountGroupName != null">
        account_group_name = #{record.accountGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.sale != null" >
        sale = #{record.sale,jdbcType=VARCHAR},
      </if>
      <if test="record.saleLeader != null" >
        sale_leader = #{record.saleLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.saleSupervisor != null" >
        sale_supervisor = #{record.saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null" >
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.shopee.model.ShopeeTaskExecutionDetails" >
    update shopee_task_execution_details
    <set >
      <if test="plate != null" >
        plate = #{plate,jdbcType=INTEGER},
      </if>
      <if test="configType != null" >
        config_type = #{configType,jdbcType=INTEGER},
      </if>
      <if test="operationType != null" >
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null" >
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="successNum != null" >
        success_num = #{successNum,jdbcType=INTEGER},
      </if>
      <if test="failNum != null" >
        fail_num = #{failNum,jdbcType=INTEGER},
      </if>
      <if test="countTime != null" >
        count_time = #{countTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execTime != null" >
        exec_time = #{execTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountGroupId != null" >
        account_group_id = #{accountGroupId,jdbcType=INTEGER},
      </if>
      <if test="accountGroupName != null">
        account_group_name = #{accountGroupName,jdbcType=VARCHAR},
      </if>
      <if test="sale != null" >
        sale = #{sale,jdbcType=VARCHAR},
      </if>
      <if test="saleLeader != null" >
        sale_leader = #{saleLeader,jdbcType=VARCHAR},
      </if>
      <if test="saleSupervisor != null" >
        sale_supervisor = #{saleSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null" >
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSalesman">
    <foreach collection="list" item="item" separator=";">
      UPDATE shopee_task_execution_details
      <set>
        <if test="item.sale != null">
          sale = #{item.sale},
        </if>
        <if test="item.saleLeader != null">
          sale_leader = #{item.saleLeader},
        </if>
        <if test="item.saleSupervisor != null">
          sale_supervisor = #{item.saleSupervisor},
        </if>
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>
  <update id="updateSalesmanByAccountNumber">
    UPDATE shopee_task_execution_details
    <set>
      <if test="details.sale != null">
        sale = #{details.sale},
      </if>
      <if test="details.saleLeader != null">
        sale_leader = #{details.saleLeader},
      </if>
      <if test="details.saleSupervisor != null">
        sale_supervisor = #{details.saleSupervisor},
      </if>
    </set>
    WHERE account = #{accountNumber} AND operation_type = #{operationType}
  </update>

</mapper>