package com.estone.erp.publish.shopee.jobHandler.stock;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ShopeeNanningProhibitedSkuStockHandler 测试类
 * 
 * <AUTHOR> Generated
 * @date 2025-06-27
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class ShopeeNanningProhibitedSkuStockHandlerTest {

    @Mock
    private ShopeeItemEsService shopeeItemEsService;

    @Mock
    private ShopeeGlobalItemService shopeeGlobalItemService;

    @InjectMocks
    private ShopeeNanningProhibitedSkuStockHandler handler;

    private SaleAccountAndBusinessResponse mockAccount;
    private List<EsShopeeItem> mockItems;

    @BeforeEach
    void setUp() {
        // 初始化模拟账号
        mockAccount = new SaleAccountAndBusinessResponse();
        mockAccount.setAccountNumber("test_nanning_account");
        mockAccount.setColBool3(true); // 南宁仓标识
        mockAccount.setAccountStatus("1"); // 正常状态
        mockAccount.setColBool2(false); // 非SIP店铺

        // 初始化模拟商品
        mockItems = new ArrayList<>();
        
        // 包含禁止刊登标签的商品
        EsShopeeItem prohibitedItem = new EsShopeeItem();
        prohibitedItem.setId("item_001");
        prohibitedItem.setArticleNumber("TEST-SKU-001");
        prohibitedItem.setItemSeller("test_nanning_account");
        prohibitedItem.setItemStatus("NORMAL");
        prohibitedItem.setSpecialGoodsCode(String.valueOf(SpecialTagEnum.s_2041.getCode()));
        mockItems.add(prohibitedItem);

        // 另一个包含禁止刊登标签的商品
        EsShopeeItem prohibitedItem2 = new EsShopeeItem();
        prohibitedItem2.setId("item_002");
        prohibitedItem2.setArticleNumber("TEST-SKU-002");
        prohibitedItem2.setItemSeller("test_nanning_account");
        prohibitedItem2.setItemStatus("NORMAL");
        prohibitedItem2.setSpecialGoodsCode(String.valueOf(SpecialTagEnum.s_2041.getCode()));
        mockItems.add(prohibitedItem2);
    }

    @Test
    void testRunWithEmptyParam() throws Exception {
        // 测试空参数执行
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenReturn(0);
        
        ReturnT<String> result = handler.run("");
        
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testRunWithValidParam() throws Exception {
        // 测试有效参数执行
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param = 
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setAccountList(Arrays.asList("test_nanning_account"));
        param.setTestMode(true);
        
        String paramJson = JSON.toJSONString(param);
        
        // 模拟scrollQueryExecutorTask的行为
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(mockItems);
                return mockItems.size();
            });
        
        ReturnT<String> result = handler.run(paramJson);
        
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testRunWithInvalidParam() throws Exception {
        // 测试无效参数执行
        String invalidParam = "invalid json";
        
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenReturn(0);
        
        ReturnT<String> result = handler.run(invalidParam);
        
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testRunWithTestMode() throws Exception {
        // 测试测试模式
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param = 
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setTestMode(true);
        
        String paramJson = JSON.toJSONString(param);
        
        // 模拟scrollQueryExecutorTask的行为
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(mockItems);
                return mockItems.size();
            });
        
        ReturnT<String> result = handler.run(paramJson);
        
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        // 测试模式下不应该调用库存更新服务
        verify(shopeeGlobalItemService, never()).updateShopeeStock(any(), any(), any(), any(), any());
    }

    @Test
    void testRunWithProductionMode() throws Exception {
        // 测试生产模式
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param =
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setTestMode(false);
        param.setAccountList(Arrays.asList("test_nanning_account"));

        String paramJson = JSON.toJSONString(param);

        // 模拟scrollQueryExecutorTask的行为
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(mockItems);
                return mockItems.size();
            });

        ReturnT<String> result = handler.run(paramJson);

        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        // 生产模式下应该调用库存更新服务（2个商品）
        // 注意：由于异步执行，可能需要等待一段时间
        Thread.sleep(1000); // 等待异步任务完成
        verify(shopeeGlobalItemService, times(2)).updateShopeeStock(any(), any(), any(), any(), any());
    }

    @Test
    void testRunWithSpecificSkus() throws Exception {
        // 测试指定SKU执行
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param = 
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setSkuList(Arrays.asList("TEST-SKU-001"));
        param.setTestMode(true);
        
        String paramJson = JSON.toJSONString(param);
        
        // 只返回指定的SKU
        List<EsShopeeItem> specificItems = Arrays.asList(mockItems.get(0));
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(specificItems);
                return specificItems.size();
            });
        
        ReturnT<String> result = handler.run(paramJson);
        
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    void testRunWithException() throws Exception {
        // 测试异常情况
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenThrow(new RuntimeException("ES查询异常"));
        
        ReturnT<String> result = handler.run("{}");
        
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
    }

    @Test
    void testQueryRequestConfiguration() {
        // 测试查询请求配置
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param = 
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setSkuList(Arrays.asList("TEST-SKU-001"));
        
        // 验证查询请求的配置
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                EsShopeeItemRequest request = invocation.getArgument(0);
                
                // 验证查询条件
                assertEquals("NORMAL", request.getItemStatus());
                assertTrue(request.getIsGoods());
                assertNotNull(request.getSpecialGoodsCodeList());
                assertTrue(request.getSpecialGoodsCodeList().contains(String.valueOf(SpecialTagEnum.s_2041.getCode())));
                assertEquals(Arrays.asList("TEST-SKU-001"), request.getArticleNumberList());
                assertEquals("id", request.getOrderBy());
                assertEquals("ASC", request.getSequence());
                assertEquals(1000, request.getPageSize().intValue());
                
                return 0;
            });
        
        try {
            handler.run(JSON.toJSONString(param));
        } catch (Exception e) {
            // 忽略异常，我们只关心查询请求的配置
        }
        
        verify(shopeeItemEsService, times(1)).scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class));
    }

    @Test
    void testParameterValidation() {
        // 测试参数验证
        
        // 测试null参数
        assertDoesNotThrow(() -> {
            handler.run(null);
        });
        
        // 测试空字符串参数
        assertDoesNotThrow(() -> {
            handler.run("");
        });
        
        // 测试空JSON参数
        assertDoesNotThrow(() -> {
            handler.run("{}");
        });
        
        // 测试参数对象
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param = 
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setAccountList(new ArrayList<>());
        param.setSkuList(new ArrayList<>());
        param.setTestMode(false);
        
        assertNotNull(param.getAccountList());
        assertNotNull(param.getSkuList());
        assertFalse(param.getTestMode());
        assertTrue(param.getAccountList().isEmpty());
        assertTrue(param.getSkuList().isEmpty());
    }

    @Test
    void testAsyncProcessingWithMultipleAccounts() throws Exception {
        // 测试多店铺异步处理
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param =
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setAccountList(Arrays.asList("account_1", "account_2", "account_3"));
        param.setTestMode(true);

        String paramJson = JSON.toJSONString(param);

        // 模拟每个店铺都有数据
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(mockItems);
                return mockItems.size();
            });

        long startTime = System.currentTimeMillis();
        ReturnT<String> result = handler.run(paramJson);
        long endTime = System.currentTimeMillis();

        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证异步处理确实被调用了多次（每个店铺一次）
        verify(shopeeItemEsService, times(3)).scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class));

        log.info("多店铺异步处理耗时：{}ms", endTime - startTime);
    }

    @Test
    void testAsyncProcessingPerformance() throws Exception {
        // 测试异步处理性能
        ShopeeNanningProhibitedSkuStockHandler.InnerParam param =
            new ShopeeNanningProhibitedSkuStockHandler.InnerParam();
        param.setTestMode(true);

        String paramJson = JSON.toJSONString(param);

        // 模拟处理时间
        when(shopeeItemEsService.scrollQueryExecutorTask(any(EsShopeeItemRequest.class), any(Consumer.class)))
            .thenAnswer(invocation -> {
                try {
                    // 模拟处理时间
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                Consumer<List<EsShopeeItem>> consumer = invocation.getArgument(1);
                consumer.accept(mockItems);
                return mockItems.size();
            });

        long startTime = System.currentTimeMillis();
        ReturnT<String> result = handler.run(paramJson);
        long endTime = System.currentTimeMillis();

        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        log.info("异步处理性能测试耗时：{}ms", endTime - startTime);

        // 异步处理应该比串行处理更快（这里只是示例，实际测试需要更多店铺）
        assertTrue(endTime - startTime < 5000, "异步处理耗时应该在合理范围内");
    }
}
