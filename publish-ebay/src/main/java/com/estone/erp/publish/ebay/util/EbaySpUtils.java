package com.estone.erp.publish.ebay.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.publish.base.pms.enums.CountryEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.ebay.bean.ItemVariation;
import com.estone.erp.publish.ebay.bean.ItemVariations;
import com.estone.erp.publish.ebay.bean.PublishBean;
import com.estone.erp.publish.ebay.enums.EbaySkuDataSourceEnum;
import com.estone.erp.publish.ebay.enums.MarketplaceEnum;
import com.estone.erp.publish.ebay.enums.PublishTypeEnum;
import com.estone.erp.publish.ebay.enums.TemplateSaleMethodEnum;
import com.estone.erp.publish.ebay.model.EbayTemplate;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.erpDas.ErpDasUtils;
import com.estone.erp.publish.system.erpDas.esModel.EsAttrInfo;
import com.estone.erp.publish.system.erpDas.esModel.EsVariationsInfo;
import com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg;
import com.estone.erp.publish.system.erpDas.esModel.ali1688.EsAli1688VariationsInfo;
import com.estone.erp.publish.system.erpDas.esModel.ebay.EsAttrInfos;
import com.estone.erp.publish.system.erpDas.esModel.ebay.EsEbayVariationsInfo;
import com.estone.erp.publish.system.erpDas.esModel.ebay.EsVariationsInfos;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/4/8
 */
@Slf4j
public class EbaySpUtils {

    /**
     * 子sku分隔符
     */
    private static final String SKU_SPLIT_STR = "-";

    /**
     * 子sku属性分隔符
     */
    private static final String PROP_VALUES_SPLIT_STR = ":";

    private static final String color = "color";

    private static final String size = "size";

    /**
     * 获取试卖数据变体
     * @param ebayTemplate
     * @param ebayVariationsInfo
     * @return
     */
    public static List<ItemVariation> getSpVariations(EbayTemplate ebayTemplate, EsEbayVariationsInfo ebayVariationsInfo, String spPriceConfig) {
        List<EsVariationsInfos> variationsInfos = ebayVariationsInfo.getVariationsInfos();
        if(CollectionUtils.isEmpty(variationsInfos) || null == ebayTemplate) {
            return null;
        }

        String country = ebayVariationsInfo.getCountry();
        if(StringUtils.isBlank(country)) {
            throw new RuntimeException("原数据country为空，无法价格转换");
        }
        String originalCurrency = MarketplaceEnum.getCurrencyByShortSite(country);
        if(StringUtils.isBlank(originalCurrency)) {
            throw new RuntimeException("未找到站点对应的币种，请联系技术人员" + country);
        }

        List<ItemVariation> itemVariations = new ArrayList<ItemVariation>();
        ItemVariation itemVariation;
        Double cnyRate = null;

        // 试卖运算的价格 和运算符号
        Double spPriceOperation = null;
        String symbol = EbaySpUtils.getSpPriceConfigSymbol(spPriceConfig);
        if(StringUtils.isNotBlank(symbol)) {
            String spPriceOperationStr = StringUtils.replace(spPriceConfig, symbol, "");
            try {
                spPriceOperation = Double.parseDouble(StringUtils.trim(spPriceOperationStr));
            }catch (Exception e) {
                log.error("试算价格配置解析出错请检查" + spPriceConfig + e.getMessage(), e);
                throw new RuntimeException("试算价格配置解析出错请检查" + spPriceConfig);
            }
            if(null == spPriceOperation) {
                throw new RuntimeException("试算价格配置不正确，请检查" + spPriceConfig);
            }
        }

        for (EsVariationsInfos variationsInfo : variationsInfos) {
            itemVariation = new ItemVariation();
            // 子SKU
            String sonSku = ebayTemplate.getArticleNumber();

            // 子SKU价格
            Double originalPrice = variationsInfo.getPrice();
            String currency = ebayTemplate.getCurrency();
            if(StringUtils.isBlank(currency)) {
                throw new RuntimeException("未找到账号对应的币种，请联系技术人员" + ebayTemplate.getAccountNumber());
            }
            BigDecimal allPrice = BigDecimal.valueOf(originalPrice);
            Double postag = ebayVariationsInfo.getPostage();
            if(null != postag) {
                allPrice = allPrice.add(BigDecimal.valueOf(postag));
            }
            // 优先使用店铺配置的试卖价格配置 未配置则减去0.01
            BigDecimal bigDecimalPrice = null;
            if(!StringUtils.equalsIgnoreCase(currency, originalCurrency)) {// 不同币种需要根据利率换算
                if(null == cnyRate) {
                    ApiResult<Double> rateResult = PriceCalculatedUtil.getExchangeRate(originalCurrency, currency);
                    if(!rateResult.isSuccess() || null == rateResult.getResult()) {
                        throw new RuntimeException(String.format("%s获取汇率失败：%s to %s", originalCurrency, currency, rateResult.getErrorMsg()));
                    }
                    cnyRate = rateResult.getResult();
                }
                allPrice = allPrice.multiply(BigDecimal.valueOf(cnyRate));
            }
            bigDecimalPrice = getSpPrice(allPrice, symbol, spPriceOperation);
            itemVariation.setPrice(bigDecimalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

            String img = "";
            // 子SKU属性
            List<EsAttrInfos> attrInfos = variationsInfo.getAttrInfos();
            List<String> propValues = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfos attrInfo : attrInfos ) {
                    propValues.add(attrInfo.getName() + PROP_VALUES_SPLIT_STR + attrInfo.getValue());
                    sonSku = sonSku + SKU_SPLIT_STR + attrInfo.getValue();
                    if(StringUtils.isNotBlank(attrInfo.getImg()) && (StringUtils.isBlank(img) ||  StringUtils.equalsIgnoreCase("color", attrInfo.getName())) ) {
                        img = attrInfo.getImg();// 默认取第一个不为空的图片 存在颜色 则优先取颜色的
                    }
                }
                itemVariation.setPropValues(propValues);
            }

            itemVariation.setPicture(img);
            itemVariation.setArticleNumber(StringUtils.upperCase(sonSku));

            itemVariations.add(itemVariation);
        }

        return itemVariations;
    }

    public static List<ItemVariation> getSp1688Variations(EbayTemplate ebayTemplate, EsAli1688VariationsInfo ali1688VariationsInfo, PublishBean publishBean) {
        List<EsVariationsInfo> variationsInfos = ali1688VariationsInfo.getVariationsInfos();
        if(CollectionUtils.isEmpty(variationsInfos) || null == ebayTemplate) {
            return null;
        }
        if (variationsInfos.size() > 30) {
            variationsInfos = variationsInfos.subList(0, 30);
        }


        List<ItemVariation> itemVariations = new ArrayList<ItemVariation>();
        ItemVariation itemVariation;

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("1688试卖属性翻译");
        Map<String, String> cnAndEnMap = translateSpAttrInfos(variationsInfos);
        stopWatch.stop();
        log.info( stopWatch.prettyPrint());

        for (EsVariationsInfo variationsInfo : variationsInfos) {
            itemVariation = new ItemVariation();
            // 子SKU
            String sonSku = ebayTemplate.getArticleNumber();

            // 子SKU属性
            List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
            List<String> propValues = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfo attrInfo : attrInfos) {
                    // 1.颜色尺码翻译为英文，只保留字母和数字和-  Color size 需要加s
                    String name = attrInfo.getName() == null ? "": attrInfo.getName();
                    String value =  attrInfo.getValue() == null ? "": attrInfo.getValue();

                    String enName = cnAndEnMap.get(name);
                    String enValue = cnAndEnMap.get(value);
                    if (enValue!=null && enValue.length() > 65 ){
                        enValue = enValue.substring(0, 65);
                    }
                    // 防止与平台属性重复默认加上s
                    enName = enName + "s";
                    propValues.add(enName + PROP_VALUES_SPLIT_STR + enValue);

                    // 过滤字符 只保留字母和数字和-
                    enValue = enValue.replaceAll("[^a-zA-Z0-9-]", "");
                    sonSku = sonSku + SKU_SPLIT_STR + enValue;
                }
                itemVariation.setPropValues(propValues);
            }

            List<String> skuImagePaths = variationsInfo.getSkuImagePath();
            if(CollectionUtils.isNotEmpty(skuImagePaths)) {
                skuImagePaths = skuImagePaths.stream()
                        .filter(StringUtils::isNotBlank)
                        .filter(EbaySpUtils::filterLowPixelImg)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(skuImagePaths)) {
                    itemVariation.setPicture(StringUtils.isBlank(skuImagePaths.get(0)) ? "" : skuImagePaths.get(0));
                }
            }
            Double price = variationsInfo.getPrice();
            if (TemplateSaleMethodEnum.PACKAGE.getCode() == publishBean.getSalesMethod() && price != null
                    && !ObjectUtils.nullSafeEquals(PublishTypeEnum.batch_1688.getCode(), publishBean.getPublishType())) {
                price = BigDecimal.valueOf(price * publishBean.getSalesPackPieceNum()).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }
            itemVariation.setPrice(price);
            itemVariation.setArticleNumber(StringUtils.upperCase(sonSku));
            itemVariations.add(itemVariation);
        }

        return itemVariations;
    }

    /**
     * 过滤低像素图片
     * @param imgUrl
     * @return
     */
    public static boolean filterLowPixelImg(String imgUrl) {
        if(StringUtils.isBlank(imgUrl)) {
            return false;
        }
        if(imgUrl.contains("32x32")) {
            return false;
        }
        try {
            URL url = new URL(imgUrl);
            BufferedImage image = ImageIO.read(url);
            int width = image.getWidth();
            int height = image.getHeight();
            if (width < 800 || height < 800) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 翻译为英文 name 和 value
     * 默认使用color size 属性
     * @param variationsInfos
     * @return
     */
    private static Map<String, String> translateSpAttrInfos(List<EsVariationsInfo> variationsInfos) {
        if(CollectionUtils.isEmpty(variationsInfos)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> cnAndEnMap = new HashMap<>();
        Set<String> cnSet = new HashSet<>();
        for (EsVariationsInfo variationsInfo : variationsInfos) {
            List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
            if (CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfo attrInfo : attrInfos) {
                    String name = attrInfo.getName() == null ? "": attrInfo.getName();
                    String value =  attrInfo.getValue() == null ? "": attrInfo.getValue();
                    cnSet.add(name);
                    cnSet.add(value);
                }
            }
        }

        List<String> cnList = new ArrayList<>(cnSet);
        String result = GoogleTranslateUtils.googleTranslateText(cnList, CountryEnum.CN.getLanguage(), CountryEnum.US.getLanguage());
        if(StringUtils.isNotBlank(result)){
            try {
                List<String> transList = JSON.parseArray(result, String.class);
                if(transList.size() == cnList.size()){
                    for (int i = 0; i < cnList.size(); i++) {
                        String cn = cnList.get(i);
                        String en = transList.get(i);
                        cnAndEnMap.put(cn, en);
                    }
                }
            } catch (Exception e){
                log.error("试卖翻译子sku属性出错" + e.getMessage(), e);
                throw new RuntimeException("试卖翻译子sku属性出错" + e.getMessage());
            }
        } else {
            log.error("试卖翻译子sku属性出错!");
            throw new RuntimeException("试卖翻译子sku属性出错!");
        }

        Set<String> enNameSet = new HashSet<>();
        Set<String> cnNameSet = new HashSet<>();
        for (EsVariationsInfo variationsInfo : variationsInfos) {
            List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
            if (CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfo attrInfo : attrInfos) {
                    // 1.颜色尺码翻译为英文，只保留字母和数字和-  Color size 需要加s
                    String name = attrInfo.getName() == null ? "" : attrInfo.getName();
                    cnNameSet.add(name);
                    enNameSet.add(cnAndEnMap.get(name));
                }
            }
        }

        Boolean existColor = false;
        Boolean existSize = false;
        for (String enName : enNameSet) { // 产品经理特意要求 默认使用 color size 超过两个再使用翻译的语言
            if(StringUtils.equalsIgnoreCase(enName, color)) {
                existColor = true;
            }
            if(StringUtils.equalsIgnoreCase(enName, size)) {
                existSize = true;
            }
        }

        // 默认使用 color size 超过两个再使用翻译的语言 (找出两个color size  不存在则使用其他转为 color size)
        List<String> cnNameList = new ArrayList<>(cnNameSet);
        for (int i = 0; i < cnNameList.size(); i++) {
            if(BooleanUtils.isTrue(existColor) && BooleanUtils.isTrue(existSize)) {
                break;
            }
            if(BooleanUtils.isNotTrue(existColor)) {
                cnAndEnMap.put(cnNameList.get(i), color);
                existColor = true;
            }else {
                cnAndEnMap.put(cnNameList.get(i), size);
                existSize = true;
            }
        }

        return cnAndEnMap;
    }

    /**
     * 更新试卖产品
     * @param template
     */
    public static ApiResult<?> updateSpProduct(EbayTemplate template) {
        List<String> skuList = new ArrayList<>();
        ItemVariations itemVariations = template.getItemVariations();

        if (itemVariations != null) {
            List<ItemVariation> itemVariationsList = itemVariations.getItemVariations();
            if (CollectionUtils.isNotEmpty(itemVariationsList)) {
                for (ItemVariation itemVariation : itemVariationsList) {
                    String sonSku = itemVariation.getArticleNumber();
                    if (StringUtils.isNotBlank(sonSku) && sonSku != "null") {
                        skuList.add(sonSku);
                    }
                }
            }
        }

        if(CollectionUtils.isEmpty(skuList)) {
            skuList.add(template.getArticleNumber());
        }

        // 试卖sku数据对象
        SpProductSaleMsg spProductSaleMsg = new SpProductSaleMsg();
        if(EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.isTrue(template.getSkuDataSource())) {
            spProductSaleMsg.setOriginPlatform(Platform.Alibaba1688.name());
        } else {
            spProductSaleMsg.setOriginPlatform(SaleChannel.CHANNEL_EBAY);
        }
        spProductSaleMsg.setMainSku(template.getArticleNumber());
        spProductSaleMsg.setSonSkus(JSON.toJSONString(skuList));

        return ErpDasUtils.pushSpSkuToDas(spProductSaleMsg);
    }

    public static final String SUB_MULTIPLY_SYMBOL = "-*";  // -*0.2: (p- 0.2P)
    public static final String ADD_MULTIPLY_SYMBOL = "+*";  // +*0.2: (p+ 0.2P)
    public static final String ADD_SYMBOL = "+"; // +0.1: p + 0.1
    public static final String SUB_SYMBOL = "-"; // -0.1: p - 0.1

    /**
     * 获取试卖价格配置运算符号
     * @param spPriceConfig
     * @return
     */
    public static String getSpPriceConfigSymbol(String spPriceConfig){
        spPriceConfig = StringUtils.trim(spPriceConfig);
        if(StringUtils.isBlank(spPriceConfig)) {
            return null;
        }

        if(StringUtils.startsWith(spPriceConfig, SUB_MULTIPLY_SYMBOL)) {
            return SUB_MULTIPLY_SYMBOL;
        }else if(StringUtils.startsWith(spPriceConfig, ADD_MULTIPLY_SYMBOL)) {
            return ADD_MULTIPLY_SYMBOL;
        } else if(StringUtils.startsWith(spPriceConfig, ADD_SYMBOL)){
            return ADD_SYMBOL;
        } else if(StringUtils.startsWith(spPriceConfig, SUB_SYMBOL)){
            return SUB_SYMBOL;
        } else {
            throw new RuntimeException("店铺试卖价格配置存在问题请检查：" + spPriceConfig);
        }
    }

    /**
     * 存在试卖价格配置 优先使用试卖价格配置 不存在则默认减去0.01
     * @param allPrice
     * @param symbol
     * @param spPriceOperation
     * @return
     */
    public static BigDecimal getSpPrice(BigDecimal allPrice, String symbol, Double spPriceOperation){
        if(null == allPrice) {
            return allPrice;
        }

        BigDecimal bigDecimalPrice = null;
        if(StringUtils.isBlank(symbol)) {
            bigDecimalPrice = allPrice.subtract(BigDecimal.valueOf(0.01));
        } else if(StringUtils.equalsIgnoreCase(symbol, SUB_MULTIPLY_SYMBOL)) {
            bigDecimalPrice = allPrice.subtract(allPrice.multiply(BigDecimal.valueOf(spPriceOperation)));
        } else if(StringUtils.equalsIgnoreCase(symbol, ADD_MULTIPLY_SYMBOL)) {
            bigDecimalPrice = allPrice.add(allPrice.multiply(BigDecimal.valueOf(spPriceOperation)));
        } else if(StringUtils.equalsIgnoreCase(symbol, ADD_SYMBOL)){
            bigDecimalPrice = allPrice.add(BigDecimal.valueOf(spPriceOperation));
        } else if(StringUtils.equalsIgnoreCase(symbol, SUB_SYMBOL)){
            bigDecimalPrice = allPrice.subtract(BigDecimal.valueOf(spPriceOperation));
        } else {
            throw new RuntimeException("店铺试卖价格配置存在问题请检查");
        }

        return bigDecimalPrice;
    }
}
