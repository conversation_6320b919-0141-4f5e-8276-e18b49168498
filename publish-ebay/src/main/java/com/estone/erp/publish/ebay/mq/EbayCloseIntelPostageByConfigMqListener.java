package com.estone.erp.publish.ebay.mq;

import com.alibaba.fastjson.JSON;
import com.ebay.soap.eBLBaseComponents.InternationalShippingServiceOptionsType;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.ebay.soap.eBLBaseComponents.ShippingDetailsType;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.bean.EbayCloseIntelPostageDo;
import com.estone.erp.publish.ebay.call.EbayReviseItemCall;
import com.estone.erp.publish.ebay.componet.EbayItemEsBulkProcessor;
import com.estone.erp.publish.ebay.enums.FeedTaskEnum;
import com.estone.erp.publish.ebay.enums.MarketplaceEnum;
import com.estone.erp.publish.ebay.model.EbayItem;
import com.estone.erp.publish.ebay.model.EbayItemExample;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.service.EbayItemService;
import com.estone.erp.publish.ebay.util.EbayFeedTaskUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-08 11:50
 */
@Slf4j
public class EbayCloseIntelPostageByConfigMqListener implements ChannelAwareMessageListener {

    @Resource
    private EbayItemService ebayItemService;
    @Resource
    private EbayItemEsService ebayItemEsService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private EbayItemEsBulkProcessor ebayItemEsBulkProcessor;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            boolean sign = false;
            try {
                EbayCloseIntelPostageDo ebayCloseIntelPostageDo = JSON.parseObject(body, EbayCloseIntelPostageDo.class);

                doService(ebayCloseIntelPostageDo);
                sign = true;
            } catch (Exception e) {
                log.error("EbayCloseIntelPostageMqListener执行异常" + e.getMessage(), e);
            }

            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioe) {
                log.warn("EbayCloseIntelPostageMqListener确认异常" + ioe.getMessage(), ioe);
            }
        } catch (Exception e) {
            log.error("EbayCloseIntelPostageMqListenerE异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioe) {
                log.warn("EbayCloseIntelPostageMqListener确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

    private void doService(EbayCloseIntelPostageDo ebayCloseIntelPostageDo) {
        String accountNumber = ebayCloseIntelPostageDo.getAccountNumber();
        List<String> itemIdList = ebayCloseIntelPostageDo.getItemIdList();
        if (CollectionUtils.isEmpty(itemIdList) || StringUtils.isBlank(accountNumber)) {
            return;
        }

        EbayItemExample example = new EbayItemExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber)
                        .andItemIdIn(itemIdList);
        List<EbayItem> ebayItems = ebayItemService.selectByExample(example);
        if (CollectionUtils.isEmpty(ebayItems)) {
            return;
        }
        List<String> nowItemIdList = ebayItems.stream().map(EbayItem::getItemId).distinct().collect(Collectors.toList());
        List<EsEbayItem> esEbayItem = getEsEbayItem(accountNumber, nowItemIdList);
        if (CollectionUtils.isEmpty(esEbayItem)) {
            log.error("EbayCloseIntelPostageByConfigMqListener esEbayItem is null : " + nowItemIdList);
            return;
        }
        Map<String, List<EsEbayItem>> esEbayItemMap = esEbayItem.stream().collect(Collectors.groupingBy(EsEbayItem::getItemId));

        for (EbayItem ebayItem : ebayItems) {
            String itemId = ebayItem.getItemId();
            List<EsEbayItem> esEbayItems = esEbayItemMap.get(itemId);
            if (CollectionUtils.isEmpty(esEbayItems)) {
                continue;
            }
            closeIntlShippingServiceOption(esEbayItems, ebayItem);
        }
    }

    private List<EsEbayItem> getEsEbayItem(String accountNumber, List<String> itemIdList) {
        if (StringUtils.isBlank(accountNumber)) {
            return null;
        }
        EsEbayItemRequest request = new EsEbayItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "itemId", "ebaySite", "primaryCategoryId", "country", "shippingDetails"});
        request.setAccountNumber(accountNumber);
        request.setItemIds(itemIdList);
        request.setIsOnline(true);
        return ebayItemEsService.getEsEbayItems(request);
    }

    /**
     * 更新的时候要更新两个
     * @param esEbayItems
     * @param ebayItem
     */
    public void closeIntlShippingServiceOption(List<EsEbayItem> esEbayItems, EbayItem ebayItem) {
        EsEbayItem esItem = esEbayItems.get(0);
        FeedTask feedTask = EbayFeedTaskUtils.getItemFailFeedTask(esItem, FeedTaskEnum.CLOSE_INTEL_POSTAGE, null, "admin");
        feedTask.setAttribute10("EbayCloseIntelPostageByConfigMqListener");
        ResponseJson rsp = new ResponseJson();
        String esUpdatedShippingDetails = null;
        String dbUpdateEbayItem = null;
        try {
            ItemType dbItemType = JSON.parseObject(ebayItem.getEbayItem(), ItemType.class);

            // db 没有精简的
            ShippingDetailsType dbShippingDetails = dbItemType.getShippingDetails();
            dbShippingDetails.setInternationalShippingServiceOption(new InternationalShippingServiceOptionsType[0]);
            dbItemType.setShippingDetails(dbShippingDetails);

            // es 精简的
            ShippingDetailsType esShippingDetailsTypes = JSON.parseObject(esItem.getShippingDetails(), ShippingDetailsType.class);
            esShippingDetailsTypes.setInternationalShippingServiceOption(new InternationalShippingServiceOptionsType[0]);

            String siteSite = MarketplaceEnum.getShortSiteBySite(esItem.getEbaySite());
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, esItem.getAccountNumber());
            EbayReviseItemCall call = new EbayReviseItemCall(saleAccount);
            ItemType item = new ItemType();
            item.setShipToLocations(new String[]{siteSite});
            item.setItemID(esItem.getItemId());
            item.setShippingDetails(esShippingDetailsTypes);
            rsp = call.reviseItem(item);

            esUpdatedShippingDetails = JSON.toJSONString(esShippingDetailsTypes);
            dbUpdateEbayItem = JSON.toJSONString(dbItemType);
        } catch (Exception e) {
            log.error("关闭item国际运费-----itemId：{}", esItem.getItemId(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("关闭item国际运费异常：" + e.getMessage());
        }

        boolean flag = true;
        feedTask.setResultStatus(1);
        if (StatusCode.FAIL.equals(rsp.getStatus())) {
            feedTask.setResultStatus(2);
            feedTask.setResultMsg(rsp.getMessage());
            flag = false;
        }
        feedTaskService.insertSelective(feedTask);
        try {
            if (flag) {
                List<String> collect = esEbayItems.stream().map(EsEbayItem::getId).collect(Collectors.toList());
                ebayItemEsBulkProcessor.updateCloseIntel(collect, esUpdatedShippingDetails);

                ebayItem.setEbayItem(dbUpdateEbayItem);
                ebayItem.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                ebayItemService.updateByPrimaryKeySelective(ebayItem);
            }
        } catch (Exception e) {
            log.error("更新关闭国际物流失败：" + e.getMessage());
        }
    }
}
