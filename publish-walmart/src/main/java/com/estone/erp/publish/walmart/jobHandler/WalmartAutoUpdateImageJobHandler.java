package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.platform.service.impl.ProductImageService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.model.WalmartAccountConfigExample;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.model.vo.WalmartReplaceItemVO;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动修改在线图片
 *
 * <AUTHOR>
 * @date 2023/5/24 16:42
 */
@Slf4j
@Component
public class WalmartAutoUpdateImageJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private ProductImageService productImageService;

    public WalmartAutoUpdateImageJobHandler() {
        super("WalmartAutoUpdateImageJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {
        /**
         * sku
         */
        private List<String> spuList;

        /**
         * 店铺
         */
        private List<String> accountNumberList;
    }

    @Override
    @XxlJob("WalmartAutoUpdateImageJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("开始执行");
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        // 获取参数
        List<String> accountNumberList = innerParam.getAccountNumberList();
        List<String> spuList = innerParam.getSpuList();

        // 获取自动修改图片的正常账号
        WalmartAccountConfigExample example = new WalmartAccountConfigExample();
        example.setColumns("id, account_number");
        WalmartAccountConfigExample.Criteria criteria = example.createCriteria().andAutoUpdateImageEqualTo(true);
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountNumberIn(accountNumberList);
        }
        List<SaleAccountAndBusinessResponse> accountList = WalmartAccountUtils.getNormalAccountByExample(example);
        if (CollectionUtils.isEmpty(accountList)) {
            XxlJobLogger.log("没有配置自动修改图片的正常账号");
            return ReturnT.SUCCESS;
        }

        // 获取图片变更的spu
        if (CollectionUtils.isEmpty(spuList)) {
            String beginTime = DateUtils.getStringDateBegin(-1);
            String endTime = DateUtils.getStringDateEnd(-1);
            spuList = ProductUtils.getImageChangeMainSku(beginTime, endTime);
        }
        if (CollectionUtils.isEmpty(spuList)) {
            XxlJobLogger.log("没有图片变更的SPU");
            return ReturnT.SUCCESS;
        }

        for (SaleAccountAndBusinessResponse account : accountList) {
            try {
                updateItemImage(account.getAccountNumber(), spuList);
            } catch (Exception e) {
                XxlJobLogger.log("账号：" + account.getAccountNumber() + "报错：" + e.getMessage());
            }
        }

        XxlJobLogger.log("结束执行");
        return ReturnT.SUCCESS;
    }

    private void updateItemImage(String accountNumber, List<String> spuList) {
        WalmartItemExample itemExample = new WalmartItemExample();
        String filed = "id, main_sku, sku";
        itemExample.setFiledColumns(filed);
        itemExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andMainSkuIn(spuList)
                .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode());
        List<WalmartItem> walmartItemList = walmartItemService.selectFiledColumnsByExample(itemExample);
        if (CollectionUtils.isEmpty(walmartItemList)) {
            return;
        }

        // 获取spu图片
        List<String> mainSkuList = walmartItemList.stream().map(WalmartItem::getMainSku).distinct().collect(Collectors.toList());
        Map<String, List<String>> spuToImageMap = productImageService.getImages(mainSkuList);

        List<WalmartReplaceItemVO> walmartReplaceItemVOList = new ArrayList<>();
        for (WalmartItem walmartItem : walmartItemList) {
            try {
                List<String> imageList = spuToImageMap.get(walmartItem.getMainSku());
                if (CollectionUtils.isEmpty(imageList)) {
                    continue;
                }
                List<String> allImageList = new ArrayList<>(imageList);

                WalmartReplaceItemVO itemVO = new WalmartReplaceItemVO();
                itemVO.setRelationId(walmartItem.getId().intValue());
                String articleNumber = walmartItem.getSku();
                String mainImage = allImageList.stream()
                        .filter(o -> o.contains(String.format("/%s.", articleNumber))
                                || o.contains(String.format("/%s.", articleNumber + "-00"))
                                || o.contains(String.format("/%s.", articleNumber + "-000"))
                                ||o.contains(String.format("/%s.", articleNumber + "-kd-"))
                                ||o.contains(String.format("/%s.", articleNumber + "-KD-")))
                        .reduce((first, second) -> first).orElse(null);
                if (StringUtils.isNotBlank(mainImage)) {
                    itemVO.setMainImageUrl(mainImage);
                }
                allImageList.remove(mainImage);

                // 附图默认取12张
                if (allImageList.size() > 12) {
                    allImageList = allImageList.subList(0, 12);
                }
                itemVO.setExtraImageList(allImageList);
                walmartReplaceItemVOList.add(itemVO);
            } catch (Exception e) {
                XxlJobLogger.log("id：" + walmartItem.getItemId() + "报错：" + e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(walmartReplaceItemVOList)) {
            return;
        }
        walmartItemService.replaceItemImage(walmartReplaceItemVOList);
    }
}
