package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.redis.config.RedisSpringUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.platform.enums.BulletPointFilterEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.walmart.model.dto.TitleDescription;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/15 17:12
 */
@Slf4j
public class WalmartCommonUtils {
    private static RedisClusterTemplate redisClusterTemplate = RedisSpringUtils.getBean(RedisClusterTemplate.class);

    public static TitleDescription matchingSpuTitleDescription(SpuOfficial spu, boolean complete) {
        TitleDescription titleDescription = new TitleDescription();
        if (spu != null) {
            String title = setTitle(spu);
            String description = setDescription(spu);
            List<String> features = setFeatures(spu);
            titleDescription.setTitle(title);
            titleDescription.setDescription(description);
            titleDescription.setKeyFeatures(JSON.toJSONString(features));
        }
        if (complete) {
            if (StringUtils.isBlank(titleDescription.getTitle()) || StringUtils.isBlank(titleDescription.getDescription())) {
                return null;
            } else {
                return titleDescription;
            }
        } else {
            return titleDescription;
        }
    }

    public static TitleDescription matchingAmazonSpuTitleDescription(SpuOfficial amazonSpuOffical) {
        if (amazonSpuOffical == null) {
            return null;
        }
        String title = setTitle(amazonSpuOffical);
        String description = setDescription(amazonSpuOffical);
        if (StringUtils.isBlank(amazonSpuOffical.getFeaturesJson())) {
            amazonSpuOffical.setFeaturesJson(amazonSpuOffical.getFeatures());
        }
        List<String> features = setFeatures(amazonSpuOffical);
        if (StringUtils.isBlank(title) || StringUtils.isBlank(description)) {
            return null;
        } else {
            TitleDescription titleDescription = new TitleDescription();
            titleDescription.setTitle(title);
            titleDescription.setDescription(description);
            titleDescription.setKeyFeatures(JSON.toJSONString(features));
            return titleDescription;
        }
    }

    /**
     * 获取标题描述五点描述 默认会有返回值
     * @param spuOfficial
     * @return
     */
    public static TitleDescription matchingTitleDescription(SpuOfficial spuOfficial, SpuOfficial amazonSpuOffical) {
        TitleDescription titleDescription = new TitleDescription();
        if (null == spuOfficial && amazonSpuOffical == null) {
            return titleDescription;
        }
        if (amazonSpuOffical != null && StringUtils.isBlank(amazonSpuOffical.getFeaturesJson())) {
            amazonSpuOffical.setFeaturesJson(amazonSpuOffical.getFeatures());
        }
        // 这里还得修改 亚马逊文案要求整个完成，也就是要有描述好标题
        // 标题 优先级：长标题>短标题>sku标题
        String title = setTitle(amazonSpuOffical);
        String description = setDescription(amazonSpuOffical);
        List<String> features = setFeatures(amazonSpuOffical);
        if (StringUtils.isBlank(title) || StringUtils.isBlank(description)) {
            String title2 = setTitle(spuOfficial);
            String description2 = setDescription(spuOfficial);
            List<String> features2 = setFeatures(spuOfficial);
            if (StringUtils.isNotBlank(description2) && StringUtils.isNotBlank(title2)) {
                title = title2;
                description = description2;
                features = features2;
            } else if (StringUtils.isBlank(description) && StringUtils.isNotBlank(description2)) {
                title = title2;
                description = description2;
                features = features2;
            } else if (StringUtils.isBlank(description) && StringUtils.isBlank(description2)
                    && StringUtils.isBlank(title) && StringUtils.isNotBlank(title2)) {
                title = title2;
                description = description2;
                features = features2;
            }
        }
        titleDescription.setTitle(title);
        titleDescription.setDescription(description);
        if (CollectionUtils.isNotEmpty(features)) {
            Collections.shuffle(features);
            if (features.size() > 5) {
                titleDescription.setKeyFeatures(JSON.toJSONString(features.subList(0, 5)));
            } else {
                titleDescription.setKeyFeatures(JSON.toJSONString(features));
            }
        }
        // 描述 优先级：SKU描述新>SKU描述
        // 描述跟title组合
        if (StringUtils.isNotBlank(description)) {
            if (StringUtils.isNotBlank(title)) {
                titleDescription.setDescription(title + "\n\n" + description);
            } else {
                titleDescription.setDescription(description);
            }
        }
        return titleDescription;
    }

    private static List<String> setFeatures(SpuOfficial spuOfficial) {
        if (spuOfficial == null) {
            return Collections.emptyList();
        }
        List<String> features = new ArrayList<>();
        if (StringUtils.isNotBlank(spuOfficial.getFeaturesJson())) {
            features = JSON.parseObject(spuOfficial.getFeaturesJson(), new TypeReference<>() {});
            features = features.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(features)) {
                // 如果描述为100% brand new and high quality则过滤
                for (int i = 0; i < features.size(); i++) {
                    String bulletPoint = features.get(i)
                            .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP.getName(), "")
                            .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP.getName(), "")
                            .replace(BulletPointFilterEnum.ORIGINAL_FIELD.getName(), "")
                            .replace(BulletPointFilterEnum.FIELD.getName(), "")
                            .replace(BulletPointFilterEnum.UPPERCASE_FIELD.getName(), "")
                            .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                            .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                            .replace(BulletPointFilterEnum.ORIGINAL_FIELD_NO_100.getName(), "")
                            .replace(BulletPointFilterEnum.FIELD_NO_100.getName(), "")
                            .replace(BulletPointFilterEnum.UPPERCASE_FIELD_NO_100.getName(), "");
                    if (StringUtils.isBlank(bulletPoint)) {
                        // 这里只会删除一次，所以可以用for循环删除
                        features.remove(i);
                        break;
                    }
                    features.set(i, bulletPoint);
                }
            }
        }
        return features;
    }

    private static String setDescription(SpuOfficial spuOfficial) {
        if (spuOfficial == null) {
            return null;
        }
        String description = spuOfficial.getNewDescription();
        if (StringUtils.isBlank(description)) {
            try {
                String desc = StrUtil.objectToStr(spuOfficial.getDescription());
                List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {});
                if (CollectionUtils.isNotEmpty(list)) {
                    for (String s : list) {
                        if (StringUtils.isNotBlank(s)) {
                            description = s;
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析描述报错：" + e.getMessage());
            }
        }
        return description;
    }

    /**
     * 标题 优先级：长标题>短标题>sku标题
     * @param spuOfficial
     * @return
     */
    private static String setTitle(SpuOfficial spuOfficial) {
        if (spuOfficial == null) {
            return null;
        }
        String title = null;
        if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<List<String>>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }
        if (StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getShortTitleJson(), new TypeReference<List<String>>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }
        if (StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getTitle())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }

        return title;
    }

    /**
     * walmart 刊登产品生成gtin 5-13位的数据 redis存储 自增
     * @return
     */
    public static Long getWalmartGtinSkuId(Integer skuDataSource) {
        if (redisClusterTemplate == null) {
            redisClusterTemplate = RedisSpringUtils.getBean(RedisClusterTemplate.class);
        }

        if (SkuDataSourceEnum.COMPOSE_SYSTEM.getCode().equals(skuDataSource)) {
            if (!redisClusterTemplate.existsKey(RedisConstant.WALMART_COMPOSE_GTIN_SKU_ID)) {
                // 查询模板
                Long maxId = getLastTemplateProductId(skuDataSource);

                // 多变体可能会有多个 +100
                redisClusterTemplate.set(RedisConstant.WALMART_COMPOSE_GTIN_SKU_ID, maxId + 100);
            }

            return redisClusterTemplate.incrementAndGet(RedisConstant.WALMART_COMPOSE_GTIN_SKU_ID);
        } else {
            // 第一次迁移数据需要手动设置值 后续若redis被清空 需要查_TE账号 productId 6-13 位
            if (!redisClusterTemplate.existsKey(RedisConstant.WALMART_TEMU_GTIN_SKU_ID)) {
                // 查询模板
                Long maxId = getLastTemplateProductId(skuDataSource);

                // 多变体可能会有多个 +100
                redisClusterTemplate.set(RedisConstant.WALMART_TEMU_GTIN_SKU_ID, maxId + 100);
            }

            return redisClusterTemplate.incrementAndGet(RedisConstant.WALMART_TEMU_GTIN_SKU_ID);
        }
    }

    private static Long getLastTemplateProductId(Integer skuDataSource) {
        WalmartTemplateService walmartTemplateService = SpringUtils.getBean(WalmartTemplateService.class);
        String productId = walmartTemplateService.getLastProductId(skuDataSource);
        if(StringUtils.isBlank(productId) || StringUtils.length(productId) != 14) {
            throw new RuntimeException("查询Gtin需要的产品id失败！" + productId);
        }

        long maxId;
        try{
            String maxIdStr = StringUtils.substring(productId, 5, 13);
            maxId = Long.parseLong(maxIdStr);
        }catch (Exception e) {
            throw new RuntimeException("查询Gtin需要的产品id失败！" + e.getMessage());
        }
        return maxId;
    }


}
