package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.call.model.OzonAttributeInfoDO;
import com.estone.erp.publish.ozon.call.model.OzonAttributeValue;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.model.OzonCategory;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonAttributeService;
import com.estone.erp.publish.ozon.service.OzonCategoryService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.service.SmallPlatformExcelDownloadLogService;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonCategoryAttributeDefaultValueMapper;
import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryAttributeDefaultValue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueExportDTO;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.service.OzonCategoryAttributeDefaultValueService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Slf4j
@Service
public class OzonCategoryAttributeDefaultValueServiceImpl extends ServiceImpl<OzonCategoryAttributeDefaultValueMapper, OzonCategoryAttributeDefaultValue> implements OzonCategoryAttributeDefaultValueService {

    private static final String PARAM_CODE = "OZON_SYNC_ATTRIBUTE_TO_NEW_ATTRIBUTE";
    private static final String PARAM_KEY = "LAST_EXECUTION_TIME";

    @Resource
    private OzonCategoryService ozonCategoryService;
    @Resource
    private OzonAttributeService ozonAttributeService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private SmallPlatformExcelDownloadLogService downloadLogService;
    @Resource
    private RabbitMqSender rabbitMqSender;

    /**
     * 将分类的属性，同步到tidb中
     */
    @Override
    public void syncAttribute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 获取所有叶子类目
        List<OzonCategory> leafCategories = ozonCategoryService.getLeafCategories();
        if (CollectionUtils.isEmpty(leafCategories)) {
            return;
        }

        // 使用CompletableFuture进行并发处理
        List<CompletableFuture<Void>> futures = leafCategories.stream().map(category -> CompletableFuture.runAsync(() -> {
            try {
                DataContextHolder.setUsername("admin");
                syncAttribute(category);
            } catch (Exception e) {
                log.error("Failed to sync attributes for category: " + category.getCategoryId(), e);
            }
        }, OzonExecutors.OZON_SYNC_CATEGORY_TO_TIDB_POOL)).collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        stopWatch.stop();
        log.info("同步分类属性到tidb耗时：{}", stopWatch.getTotalTimeMillis());
        LocalDateTime now = LocalDateTime.now();
        // 保存同步时间，时间设置为 yyyy-MM-dd HH:mm:ss
        String format = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        saveSyncTime(format);
    }

    /**
     * 同步指定类目的属性
     */
    @Override
    public void syncAttribute(OzonCategory category) {
        String employeeNo = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            employeeNo = "admin";
        }
        if (category == null) {
            log.warn("Category is null");
            return;
        }
        Integer categoryId = category.getCategoryId();

        // 获取类目属性和值列表
        List<OzonAttributeInfoDO> attributeList = ozonAttributeService.getAttributeList(categoryId).getResult();
        if (CollectionUtils.isEmpty(attributeList)) {
            log.warn("No attributes found for category: " + categoryId);
            return;
        }

        // 转换为OzonCategoryAttributeDefaultValue对象
        List<OzonCategoryAttributeDefaultValue> defaultValueList = convertToDefaultValueList(category, attributeList);
        // 批量保存到数据库
        if (CollectionUtils.isNotEmpty(defaultValueList)) {
            syncBatchSaveOrUpdate(category.getCategoryId(), defaultValueList, employeeNo);
        }
    }

    private void syncBatchSaveOrUpdate(Integer categoryId, List<OzonCategoryAttributeDefaultValue> defaultValueList, String username) {
        LambdaQueryWrapper<OzonCategoryAttributeDefaultValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OzonCategoryAttributeDefaultValue::getCategoryId, categoryId);
        List<OzonCategoryAttributeDefaultValue> list = list(queryWrapper);
        // 根据 categoryId 和 attributeId 判断唯一，批量更新保存
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Integer, OzonCategoryAttributeDefaultValue> collect =
                    list.stream().collect(Collectors.toMap(OzonCategoryAttributeDefaultValue::getAttributeId, Function.identity(), (v1, v2) -> v1));
            for (OzonCategoryAttributeDefaultValue defaultValue : defaultValueList) {
                OzonCategoryAttributeDefaultValue ozonCategoryAttributeDefaultValue = collect.get(defaultValue.getAttributeId());
                if (ozonCategoryAttributeDefaultValue != null) {
                    String defaultValueCustom = ozonCategoryAttributeDefaultValue.getDefaultValue();
                    if (StringUtils.isNotBlank(defaultValueCustom)) {
                        String options = defaultValue.getOptions();
                        if (StringUtils.isNotBlank(options) && "[]".equalsIgnoreCase(options)) {
                            List<OzonAttributeValue> ozonAttributeValues = JSON.parseArray(options, OzonAttributeValue.class);
                            boolean b = ozonAttributeValues.stream().anyMatch(ozonAttributeValue -> defaultValueCustom.equalsIgnoreCase(ozonAttributeValue.getValue()));
                            defaultValue.setCheckFlag(b);
                        } else {
                            defaultValue.setCheckFlag(true);
                        }
                    }
                    defaultValue.setId(ozonCategoryAttributeDefaultValue.getId());
                    defaultValue.setUpdatedTime(now);
                    defaultValue.setUpdatedBy(username);
                } else {
                    defaultValue.setCreatedBy(username);
                    defaultValue.setCreatedTime(now);
//                    defaultValue.setImportBy(username);
//                    defaultValue.setImportTime(now);
                }
            }
            saveOrUpdateBatch(defaultValueList, 100);
        } else {
            for (OzonCategoryAttributeDefaultValue ozonCategoryAttributeDefaultValue : defaultValueList) {
                ozonCategoryAttributeDefaultValue.setCreatedBy(username);
                ozonCategoryAttributeDefaultValue.setCreatedTime(now);
            }
            saveBatch(defaultValueList, 100);
        }
    }

    /**
     * 转换为OzonCategoryAttributeDefaultValue对象列表
     */
    private List<OzonCategoryAttributeDefaultValue> convertToDefaultValueList(OzonCategory category, List<OzonAttributeInfoDO> attributeList) {
        List<OzonCategoryAttributeDefaultValue> result = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 创建属性值映射
        Map<Integer, List<OzonAttributeValue>> attributeValueMap = attributeList.stream()
                .filter(a -> CollectionUtils.isNotEmpty(a.getAttributeValues()))
                .collect(Collectors.toMap(
                        OzonAttributeInfoDO::getId,
                        OzonAttributeInfoDO::getAttributeValues,
                        (v1, v2) -> v1)
                );

        // 处理每个属性
        for (OzonAttributeInfoDO attribute : attributeList) {
            OzonCategoryAttributeDefaultValue defaultValue = new OzonCategoryAttributeDefaultValue();
            defaultValue.setCategoryId(category.getCategoryId());

            // 处理多语言
            String categoryMultName = getCategoryMultName(category);
            defaultValue.setCategoryMultName(categoryMultName);
            defaultValue.setCategoryFullMultName(getCategoryFullMultName(category));

            defaultValue.setAttributeName(attribute.getName());
            defaultValue.setAttributeId(attribute.getId());
            defaultValue.setIsRequired(attribute.getIsRequired());
            defaultValue.setIsAspect(attribute.getIsAspect());
            // 处理属性值
            List<OzonAttributeValue> ozonAttributeValues = attributeValueMap.get(attribute.getId());
            if (ozonAttributeValues != null && !ozonAttributeValues.isEmpty()) {
                // 设置选项列表
                defaultValue.setOptions(JSON.toJSONString(ozonAttributeValues));
            }

            // 设置时间
            defaultValue.setSyncTime(now);

            result.add(defaultValue);
        }

        return result;
    }

    /**
     * 获取类目多语言名称
     */
    @Override
    public String getCategoryMultName(OzonCategory category) {
        if (category == null) {
            return null;
        }
        // 根据实际情况处理多语言
        // 这里假设title字段已经包含了多语言信息，格式如："中文名称|Russian Name"
        String lang = category.getLang();
        if (StringUtils.isBlank(lang)) {
            return null;
        }
        Map<String, String> langMap = JSON.parseObject(lang, new TypeReference<>() {
        });

        return langMap.get("ZH_HANS") + "(" + langMap.get("RU") + ")";
    }

    /**
     * 获取类目完整多语言名称
     */
    @Override
    public String getCategoryFullMultName(OzonCategory category) {
        if (category == null) {
            return null;
        }
        // 获取类目路径
        String cidPath = category.getCidPath();
        if (StringUtils.isBlank(cidPath)) {
            return getCategoryMultName(category);
        }
        // 获取所有父类目
        String[] split = cidPath.split("/");
        List<OzonCategory> parentCategories = new ArrayList<>();
        for (String s : split) {
            OzonCategory ozonCategory = ozonCategoryService.selectCategoryByCid(Long.valueOf(s));
            parentCategories.add(ozonCategory);
        }
        // 拼接完整路径
        return parentCategories.stream().map(this::getCategoryMultName).filter(StringUtils::isNotBlank).collect(Collectors.joining(" > "));
    }

    public void saveSyncTime(String newTime) {
        SystemParam param = systemParamService.querySystemParamByCodeKey(PARAM_CODE + "." + PARAM_KEY);
        if (param == null) {
            // 创建新参数
            param = new SystemParam();
            param.setParamCode(PARAM_CODE);
            param.setParamKey(PARAM_KEY);
            param.setParamName("ozon分类配置属性同步时间");
            param.setParamType(1); // 字符串类型
            param.setParamDisplay(true);
            param.setParamEnabled(true);
            param.setPlatform("OZON");
        }
        // 设置参数值为: startTime|endTime
        param.setParamValue(newTime);
        // 保存参数
        if (param.getParamId() == null) {
            systemParamService.insertSelective(param);
        } else {
            systemParamService.updateByPrimaryKeySelective(param);
        }
    }

    /**
     * 查询属性配置
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @Override
    public Page<OzonCategoryAttributeDefaultValue> queryAttributeConfig(OzonCategoryAttributeDefaultValueQueryDTO queryDTO) {
        LambdaQueryWrapper<OzonCategoryAttributeDefaultValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OzonCategoryAttributeDefaultValue::getId,
                OzonCategoryAttributeDefaultValue::getDefaultValue,
                OzonCategoryAttributeDefaultValue::getAttributeName,
                OzonCategoryAttributeDefaultValue::getCategoryMultName,
                OzonCategoryAttributeDefaultValue::getAttributeId,
                OzonCategoryAttributeDefaultValue::getCategoryFullMultName,
                OzonCategoryAttributeDefaultValue::getCategoryId,
                OzonCategoryAttributeDefaultValue::getImportBy,
                OzonCategoryAttributeDefaultValue::getImportTime,
                OzonCategoryAttributeDefaultValue::getSyncTime);
        // 添加查询条件
        if (StringUtils.isNotBlank(queryDTO.getCategoryMultName())) {
            queryWrapper.like(OzonCategoryAttributeDefaultValue::getCategoryMultName, queryDTO.getCategoryMultName());
        }
        if (StringUtils.isNotBlank(queryDTO.getAttributeName())) {
            queryWrapper.like(OzonCategoryAttributeDefaultValue::getAttributeName, queryDTO.getAttributeName());
        }
        queryWrapper.isNotNull(OzonCategoryAttributeDefaultValue::getDefaultValue);
        // 按导入时间倒序排序
        queryWrapper.orderByDesc(OzonCategoryAttributeDefaultValue::getImportTime);
        // 创建分页对象
        Page<OzonCategoryAttributeDefaultValue> page = new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize());
        // 执行分页查询
        page(page, queryWrapper);
        return page;
    }

    /**
     * 导出属性配置
     *
     * @param exportDTO 导出参数
     * @return 导出结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> exportAttributeConfig(OzonCategoryAttributeDefaultValueExportDTO exportDTO) {
        try {
            // 创建查询条件
            LambdaQueryWrapper<OzonCategoryAttributeDefaultValue> queryWrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (CollectionUtils.isNotEmpty(exportDTO.getIds())) {
                queryWrapper.in(OzonCategoryAttributeDefaultValue::getId, exportDTO.getIds());
            } else {
                if (StringUtils.isNotBlank(exportDTO.getCategoryMultName())) {
                    queryWrapper.like(OzonCategoryAttributeDefaultValue::getCategoryMultName, exportDTO.getCategoryMultName());
                }
                if (StringUtils.isNotBlank(exportDTO.getAttributeName())) {
                    queryWrapper.like(OzonCategoryAttributeDefaultValue::getAttributeName, exportDTO.getAttributeName());
                }
            }

            // 检查数据量
            long count = count(queryWrapper);
            if (count > 500000) {
                return ApiResult.newError("导出数据量过大,请缩小查询条件");
            }

            // 创建下载日志
            SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
            downloadLog.setType(SmallPlatformDownloadEnums.Type.ATTRIBUTE_EXPORT.name());

            // 构建查询条件JSON
            downloadLog.setQueryCondition(JSON.toJSONString(exportDTO));

            downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);
            downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
            downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());

            int insert = downloadLogService.insert(downloadLog);
            if (insert > 0) {
                // 发送到下载队列
                rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY, downloadLog.getId());
            }
        } catch (Exception e) {
            log.error("导出属性配置失败", e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("前往导出结果页查询");
    }

    @Override
    public void batchUpdateImport(List<OzonCategoryAttributeDefaultValue> updateList) {
        baseMapper.batchUpdateImport(updateList);
    }

    @Override
    public ApiResult<String> getSyncTime() {
        SystemParam param = systemParamService.querySystemParamByCodeKey(PARAM_CODE + "." + PARAM_KEY);
        if (param != null) {
            return ApiResult.newSuccess(param.getParamValue());
        }
        return ApiResult.newSuccess("");
    }

    @Override
    public ApiResult<String> exportCategoryAttribute() {
        try {

            // 创建下载日志
            SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
            downloadLog.setType(SmallPlatformDownloadEnums.Type.CATEGORY_ATTRIBUTE_EXPORT.name());
            // 构建查询条件JSON
            downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);
            downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
            downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());

            int insert = downloadLogService.insert(downloadLog);
            if (insert > 0) {
                // 发送到下载队列
                rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY, downloadLog.getId());
            }
        } catch (Exception e) {
            log.error("导出属性配置失败", e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("前往导出结果页查询");
    }
}
