package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateCriteria;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【walmart_admin_template(walmart admin 范本)】的数据库操作Service
* @createDate 2025-06-27 14:44:27
*/
public interface WalmartAdminTemplateService extends IService<WalmartAdminTemplate> {

    /**
     * 分页查询Walmart Admin模板
     * @param cquery 查询条件
     * @return 分页结果
     */
    CQueryResult<WalmartAdminTemplate> search(WalmartAdminTemplateCriteria cquery);

    /**
     * 批量更新模板状态
     * @param ids 模板ID列表
     * @param status 状态 0-禁用 1-启用
     * @param userName 操作人
     */
    void updateStatus(List<Long> ids, Integer status, String userName);
}
