package com.estone.erp.publish.tidb.publishtidb.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateLogService;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description Walmart Admin 范本控制器
 * @createDate 2025-06-27 15:30:00
 */
@Slf4j
@RestController
@RequestMapping("walmartAdminTemplate")
public class WalmartAdminTemplateController {
    @Resource
    private WalmartAdminTemplateService walmartAdminTemplateService;

    @Resource
    private WalmartAdminTemplateLogService walmartAdminTemplateLogService;

    /**
     * 查询Walmart Admin范本列表
     * 支持多条件筛选：范本编号、货号、标题、店铺、数据来源、刊登类型、状态、售卖形式
     *
     * @param cquery 请求参数
     * @return 分页查询结果
     */
    @PostMapping("/search")
    public ApiResult<?> postWalmartAdminTemplate(@RequestBody(required = true) WalmartAdminTemplateCriteria cquery) {
        try {
            CQueryResult<WalmartAdminTemplate> results = walmartAdminTemplateService.search(cquery);
            return results;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据ID查询Walmart Admin范本
     *
     * @param id 范本ID
     * @return 范本详情
     */
    @GetMapping(value = "/{id}")
    public ApiResult<?> getWalmartAdminTemplate(@PathVariable(value = "id", required = true) Long id) {
        WalmartAdminTemplate template = walmartAdminTemplateService.getById(id);
        return ApiResult.newSuccess(template);
    }

    /**
     * 批量更新范本状态（启用/禁用）
     *
     * @param request 请求参数，包含ids和status
     * @return 操作结果
     */
    @PostMapping(value = "/updateStatus")
    public ApiResult<?> updateStatus(@RequestBody WalmartAdminTemplateCriteria request) {
        if (null == request || null == request.getStatus() || CollectionUtils.isEmpty(request.getIds())) {
            return ApiResult.newError("存在必填参数为空！");
        }

        try {
            String userName = WebUtils.getUserName();
            if (StringUtils.isBlank(userName)) {
                throw new IllegalArgumentException("当前登录人为获取到请重新登录");
            }

            walmartAdminTemplateService.updateStatus(request.getIds(), request.getStatus(), userName);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            log.error("更新范本状态失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 查看范本操作日志
     *
     * @param templateId 范本ID
     * @return 日志列表
     */
    @GetMapping(value = "/logs/{templateId}")
    public ApiResult<?> getTemplateLogs(@PathVariable(value = "templateId", required = true) Integer templateId) {
        try {
            List<WalmartAdminTemplateLog> logs = walmartAdminTemplateLogService.getLogsByTemplateId(templateId);
            return ApiResult.newSuccess(logs);
        } catch (Exception e) {
            log.error("查询范本日志失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }
}