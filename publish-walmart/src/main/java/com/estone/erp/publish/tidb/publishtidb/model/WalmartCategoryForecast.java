package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 类目预测
 * @TableName walmart_category_forecast
 */
@TableName(value ="walmart_category_forecast")
@Data
public class WalmartCategoryForecast {
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货号
     */
    @TableField(value = "article_number")
    private String articleNumber;

    /**
     * SPU标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 处理后的SPU标题
     */
    @TableField(value = "handled_title")
    private String handledTitle;

    /**
     * SPU分类
     */
    @TableField(value = "category_path")
    private String categoryPath;

    /**
     * 处理后的SPU最次级分类
     */
    @TableField(value = "handled_category_path")
    private String handledCategoryPath;

    /**
     * 类目ID
     */
    @TableField(value = "category_id")
    private String categoryId;


    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;
}