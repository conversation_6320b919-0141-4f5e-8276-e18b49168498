package com.estone.erp.publish.walmart.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.walmart.call.feeds.WalmartGetFeedStatusCall;
import com.estone.erp.publish.walmart.componet.WalmartTemplateBuilderHelper;
import com.estone.erp.publish.walmart.enums.WalmartTemplateTableEnum;
import com.estone.erp.publish.walmart.handler.publish.WalmartTemplatePublishExecutor;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.WalmartTemplateCriteria;
import com.estone.erp.publish.walmart.model.WalmartTemplateExample;
import com.estone.erp.publish.walmart.model.WalmartVariant;
import com.estone.erp.publish.walmart.model.dto.*;
import com.estone.erp.publish.walmart.model.vo.WalmartSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateVO;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.CardCodeUtils;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.estone.erp.publish.walmart.util.WalmartCalcPriceUtil;
import com.estone.erp.publish.walmart.util.WalmartTemplateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> walmart_template
 * 2022-08-12 15:32:22
 */
@RestController
@RequestMapping("walmartTemplate")
@Slf4j
public class WalmartTemplateController {
    @Resource
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private FeedTaskService feedTaskService;

    @Autowired
    private WalmartTemplateBuilderHelper walmartTemplateBuilderHelper;

    @Resource
    private WalmartTemplatePublishExecutor walmartTemplatePublishExecutor;

    @PostMapping
    public ApiResult<?> postWalmartTemplate(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchWalmartTemplate": // 查询列表
                    CQuery<WalmartTemplateCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<WalmartTemplateCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    return walmartTemplateService.search(cquery);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据id查询范本模板
     *
     * @param id
     * @param isParent
     * @return
     */
    @GetMapping(value = "getTemplate/{id}/{isParent}")
    public ApiResult<?> getWalmartTemplate(@PathVariable("id") Integer id, @PathVariable("isParent") Boolean isParent) {
        if (null == id || null == isParent) {
            return ApiResult.newError("参数不完整");
        }
        String table = WalmartTemplateUtils.getTemplateTable(isParent);
        WalmartTemplateVO walmartTemplate = walmartTemplateService.findById(id, table);

        // 获取图片池
        if (!WalmartAccountUtils.isPublishTemuAccount(walmartTemplate.getAccountNumber())) {
            String mainSku = ProductUtils.getMainSku(walmartTemplate.getArticleNumber());
            List<String> images = walmartTemplateService.getImagesByMainSku(mainSku);
            walmartTemplate.setImageList(images);
        }

        String articleNumber = walmartTemplate.getArticleNumber();

        // 根据货号获取产品信息
        List<ProductInfo> productInfos = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber));

        List<String> skuList;
        if (walmartTemplate.getSaleVariant()) {
            List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
            });
            skuList = variants.stream().map(WalmartVariant::getSku).collect(Collectors.toList());
        } else {
            skuList = Lists.newArrayList(articleNumber);
        }
        productInfos = productInfos.stream().filter(productInfo -> skuList.contains(productInfo.getSonSku())).collect(Collectors.toList());

        // 单品状态
        String itemStatusStr = productInfos.stream().map(ProductInfo::getItemStatus).filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        walmartTemplate.setItemStatus(itemStatusStr);

        // 禁售平台
        String saleForbiddenPlatformStr = productInfos.stream().map(ProductInfo::getSaleForbiddenList).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        walmartTemplate.setSaleForbiddenPlatform(saleForbiddenPlatformStr);


        // 上贴备注/刊登备注
        String postRemarkStr = productInfos.stream().map(ProductInfo::getPostRemark).filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        walmartTemplate.setPostRemark(postRemarkStr);

        // 标签
        String tagStr = productInfos.stream().map(ProductInfo::getTag).filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        walmartTemplate.setTag(tagStr);


        return ApiResult.newSuccess(walmartTemplate);
    }

    /**
     * 添加或修改范本模板
     *
     * @param walmartTemplateVO
     * @return
     */
    @PostMapping("/addEditTemplate")
    public ApiResult<?> addWalmartTemplate(@RequestBody WalmartTemplateVO walmartTemplateVO) {
        try {
            walmartTemplateService.insertEditTemplate(walmartTemplateVO);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 构建范本模板信息
     *
     * @param articleNumber
     * @param categoryId
     * @return
     */
    @GetMapping("/getTemplateData/{articleNumber}/{categoryId}/{skuDataSource}")
    public ApiResult<WalmartTemplateSkuInfoVO> getTemplateData(@PathVariable("articleNumber") String articleNumber,
                                                               @PathVariable("categoryId") String categoryId,
                                                               @PathVariable("skuDataSource") Integer skuDataSource) {
        if (StringUtils.isBlank(articleNumber) || StringUtils.isBlank(categoryId) || null == skuDataSource) {
            return ApiResult.newError("参数为空");
        }

        WalmartTemplateSkuInfoVO walmartTemplateSkuInfoVO;
        try {
            walmartTemplateSkuInfoVO = walmartTemplateBuilderHelper.getTemplateData(articleNumber, categoryId, skuDataSource);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(walmartTemplateSkuInfoVO);
    }


    /**
     * 根据货号查询产品信息
     *
     * @param articleNumber
     * @return
     */
    @GetMapping("/getProductInfo/{articleNumber}/{skuDataSource}")
    public ApiResult<?> getProductInfo(@PathVariable("articleNumber") String articleNumber, @PathVariable("skuDataSource") Integer skuDataSource) {
        List<WalmartSkuInfoVO> walmartSkuInfoVOList;
        try {
            walmartSkuInfoVOList = walmartTemplateBuilderHelper.getProductInfo(articleNumber, skuDataSource);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess(walmartSkuInfoVOList);
    }

    /**
     * 校验货号是否存在范本
     *
     * @param articleNumber
     * @param id
     * @return
     */
    @GetMapping(value = {"/checkIsExistTemplate/{articleNumber}/{id}", "/checkIsExistTemplate/{articleNumber}"})
    public ApiResult<?> checkIsExistTemplate(@PathVariable("articleNumber") String articleNumber,
                                             @PathVariable(name = "id", required = false) Integer id) {
        if (StringUtils.isBlank(articleNumber)) {
            return ApiResult.newError("参数为空");
        }
        List<WalmartTemplate> walmartTemplateList = walmartTemplateService
                .selectByArticleNumber(articleNumber, WalmartTemplateTableEnum.WALMART_TEMPLATE_MODEL.getCode());
        if (null == id) {
            if (CollectionUtils.isNotEmpty(walmartTemplateList)) {
                return ApiResult.newError("该货号已存在范本");
            }
        } else {
            if (CollectionUtils.isNotEmpty(walmartTemplateList)
                    && !ObjectUtils.nullSafeEquals(id, walmartTemplateList.get(0).getId())) {
                return ApiResult.newError("该货号已存在范本");
            }
        }

        return ApiResult.newSuccess();
    }

    /**
     * 算价
     *
     * @param request
     * @return
     */
    @PostMapping("/calcPrice")
    public ApiResult<?> calcPrice(@RequestBody WalmartTemplateCalcPriceRequest request) {
        return WalmartCalcPriceUtil.calcTemplatePrice(request);
    }

    /**
     * 生成产品id
     */
    @PostMapping("/generateProductId")
    public ApiResult<List<String>> generateProductId(@RequestBody AccountEanRequest accountEanRequest) {
        Asserts.isTrue(accountEanRequest != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountEanRequest.getSellerId());
            String eanPrefix = WalmartAccountUtils.getEanPrefix(account, accountEanRequest.getCardCodeType());
            accountEanRequest.setEanPrefix(eanPrefix);
            List<String> idList = CardCodeUtils.generateCardCodes(accountEanRequest);
            return ApiResult.newSuccess(idList);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 复制范本到模板
     *
     * @param copyTemplateRequestList
     * @return
     */
    @PostMapping("/copyTemplate")
    public ApiResult<?> copyTemplate(@RequestBody List<CopyTemplateRequest> copyTemplateRequestList) {
        Asserts.isTrue(copyTemplateRequestList != null, ErrorCode.PARAM_EMPTY_ERROR);

        String error = walmartTemplateService.copyTemplate(copyTemplateRequestList);
        if (StringUtils.isNotBlank(error)) {
            return ApiResult.newError(error);
        } else {
            return ApiResult.newSuccess();
        }
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    @PostMapping("/batchDelete")
    public ApiResult<?> batchDelete(@RequestBody List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数为空");
        }
        String table = WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode();
        walmartTemplateService.batchDelete(idList, table);
        return ApiResult.newSuccess();
    }

    /**
     * 批量另存模板
     *
     * @param idList
     * @return
     */
    @PostMapping("/batchSaveAs")
    public ApiResult<?> batchSaveAs(@RequestBody List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数为空");
        }

        walmartTemplateService.batchSaveAs(idList);
        return ApiResult.newSuccess();
    }

    /**
     * 另存模板
     *
     * @param walmartTemplateVO
     * @return
     */
    @PostMapping("/saveAsTemplate")
    public ApiResult<?> saveAsTemplate(@RequestBody WalmartTemplateVO walmartTemplateVO) {
        Asserts.isTrue(walmartTemplateVO != null, ErrorCode.PARAM_EMPTY_ERROR);

        String error = walmartTemplateService.saveAsTemplate(walmartTemplateVO);
        if (StringUtils.isNotBlank(error)) {
            return ApiResult.newError(error);
        } else {
            return ApiResult.newSuccess("保存成功");
        }
    }

    /**
     * 获取店铺后缀
     *
     * @param accountNumber
     * @return
     */
    @GetMapping("/getAccountSuffix/{accountNumber}")
    public ApiResult<String> getAccountSuffix(@PathVariable("accountNumber") String accountNumber) {
        String accountSuffix;
        try {
            accountSuffix = walmartTemplateService.getAccountSuffix(accountNumber);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(accountSuffix);
    }

    /**
     * 获取刊登状态（后端测试用，无需处理异常）
     *
     * @param accountNumber
     * @param feedId
     * @return
     */
    @GetMapping("/publishStatus/{accountNumber}/{feedId}")
    public FeedStatusResultDTO publishStatus(@PathVariable String accountNumber, @PathVariable String feedId) {
        SaleAccountAndBusinessResponse account = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        WalmartAccountUtils.refreshAccountToken(account);
        return new WalmartGetFeedStatusCall(account).execute(feedId);
    }

    /**
     * 刊登模板
     *
     * @param id
     * @return
     */
    @GetMapping("/publishTemplate/{id}")
    public ApiResult<?> publishTemplate(@PathVariable Integer id) {
        try {
            walmartTemplateService.publishTemplate(id,false);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 保存并刊登 (废弃)
     *
     * @param walmartTemplateVO
     * @return
     */
    @PostMapping("/saveAndPublish")
    public ApiResult<?> saveAndPublish(@RequestBody WalmartTemplateVO walmartTemplateVO) {
        try {
            walmartTemplateService.saveAndPublish(walmartTemplateVO);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量刊登
     *
     * @param request
     * @return
     */
    @PostMapping("/batchPublish")
    public ApiResult<?> batchPublish(@RequestBody BatchPublishRequest request) {
        try {
            walmartTemplateService.batchPublish(request);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess();
    }

    /**
     * 批量刊登校验侵权词
     *
     * @param requests
     * @return
     */
    @PostMapping("checkInfringementWords")
    public ApiResult<?> checkInfringementWords(@RequestBody List<BatchPublishBean> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数为空");
        }

        Map<Integer, String> requestMap =
                requests.stream().collect(Collectors.toMap(BatchPublishBean::getId, BatchPublishBean::getTitle));

        WalmartTemplateExample example = new WalmartTemplateExample();
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        example.createCriteria().andIdIn(new ArrayList<>(requestMap.keySet()));
        String filed = "id, title, description, key_features";
        example.setFiledColumns(filed);
        List<WalmartTemplate> walmartTemplateList = walmartTemplateService.selectFiledColumnsByExample(example);

        List<Integer> resultList = new ArrayList<>();

        for (WalmartTemplate walmartTemplate : walmartTemplateList) {
            walmartTemplate.setTitle(requestMap.get(walmartTemplate.getId()));

            String attributeStr = null;
            if (walmartTemplate.getSaleVariant() && StringUtils.isNotBlank(walmartTemplate.getVariations())) {
                List<WalmartVariant> walmartVariantList = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
                });
                if (CollectionUtils.isNotEmpty(walmartVariantList)) {
                    attributeStr = WalmartTemplateUtils.getAttributeStr(walmartVariantList);
                }
            }
            Map<String, String> resultMap = WalmartTemplateUtils.checkInfringementWords(walmartTemplate.getTitle(), walmartTemplate.getDescription(),
                    walmartTemplate.getBrand(), walmartTemplate.getKeyFeatures(), attributeStr);
            if (MapUtils.isNotEmpty(resultMap)) {
                resultList.add(walmartTemplate.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(resultList)) {
            return ApiResult.newSuccess(resultList);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 模板校验侵权词
     *
     * @param walmartTemplate
     * @return
     */
    @PostMapping("checkTemplateInfringementWords")
    public ApiResult<Map<String, String>> checkTemplateInfringementWords(@RequestBody WalmartTemplate walmartTemplate) {
        if (null == walmartTemplate) {
            return ApiResult.newError("参数为空");
        }
        String attributeStr = null;
        if (walmartTemplate.getSaleVariant() && StringUtils.isNotBlank(walmartTemplate.getVariations())) {
            List<WalmartVariant> walmartVariantList = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
            });
            if (CollectionUtils.isNotEmpty(walmartVariantList)) {
                attributeStr = WalmartTemplateUtils.getAttributeStr(walmartVariantList);
            }
        }
        Map<String, String> resultMap = WalmartTemplateUtils.checkInfringementWords(walmartTemplate.getTitle(),
                walmartTemplate.getDescription(), walmartTemplate.getBrand(), walmartTemplate.getKeyFeatures(), attributeStr);
        return ApiResult.newSuccess(resultMap);
    }

    /**
     * 根据模板id获取刊登失败备注
     *
     * @param templateId
     * @return
     */
    @GetMapping("/getFailureRemarks/{templateId}")
    public ApiResult<?> getFailureRemarks(@PathVariable String templateId) {
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andTaskTypeEqualTo(WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn())
                .andAttribute4EqualTo(templateId);
        example.setOrderByClause("finish_time DESC");
        List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Walmart.name());
        return ApiResult.newSuccess(feedTasks);
    }

    /**
     * 上传库存
     *
     * @param templateIdList
     * @return
     */
    @PostMapping("uploadInventory")
    public ApiResult<?> uploadInventory(@RequestBody List<Integer> templateIdList) {
        try {
            walmartTemplateService.uploadInventory(templateIdList);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess("已提交库存，请稍后查看处理报告上传结果");
    }

    /**
     * 构建temu模板数据
     *
     * @param accountNumber
     * @param itemId
     * @return
     */
    @GetMapping("/buildTemuTemplate/{accountNumber}/{itemId}")
    public ApiResult<WalmartTemplate> buildTemuTemplate(@PathVariable String accountNumber, @PathVariable String itemId) {
        WalmartTemplate walmartTemplate = walmartTemplateService.buildTemuTemplate(accountNumber, itemId);
        return ApiResult.newSuccess(walmartTemplate);
    }

    /** ------------------------------------------------------新版刊登接口------------------------------------------------- **/

    /**
     * 构建模板信息
     */
    @PostMapping("getTemplateInfo")
    public ApiResult<Map<String, Object>> getTemplateInfo(@RequestBody BuilderTemplateDTO builderTemplateDTO) {
        try {
            return ApiResult.newSuccess(walmartTemplateService.getTemplateInfo(builderTemplateDTO));
        } catch (Exception e) {
            log.error("前端请求构建模板接口异常", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 校验模板数据
     */
    @PostMapping("checkTemplateData")
    public ApiResult<Map<String, Object>> checkTemplateData(@RequestBody WalmartTemplateDTO templateDTO) {
        try {
            return ApiResult.newSuccess(walmartTemplateService.checkTemplateData(templateDTO));
        } catch (Exception e) {
            log.error("前端请求校验模板数据接口异常", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 模板-保存模板及范本-保存范本
     */
    @PostMapping("saveTemplate")
    public ApiResult<String> saveTemplate(@RequestBody WalmartTemplateDTO templateDTO) {
        try {
            walmartTemplateService.saveTemplate(templateDTO);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            log.error("前端请求模板-保存模板及范本-保存范本接口异常", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 模板-刊登模板及范本-保存并刊登
     */
    @PostMapping("saveAndPublishNew")
    public ApiResult<String> saveAndPublishNew(@RequestBody WalmartTemplateDTO templateDTO) {
        try {
            walmartTemplateService.saveAndPublishNew(templateDTO);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            log.error("前端请求模板-刊登模板及范本-保存并刊登接口异常", e);
            return ApiResult.newError(e.getMessage());
        }
    }
}
