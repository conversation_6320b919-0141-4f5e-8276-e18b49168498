package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartCategoryForecast;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartCategoryForecastService;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartCategoryForecastMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【walmart_category_forecast(类目预测)】的数据库操作Service实现
* @createDate 2025-06-11 16:55:40
*/
@Service
public class WalmartCategoryForecastServiceImpl extends ServiceImpl<WalmartCategoryForecastMapper, WalmartCategoryForecast>
    implements WalmartCategoryForecastService{

}




