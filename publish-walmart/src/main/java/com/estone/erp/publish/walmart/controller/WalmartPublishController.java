package com.estone.erp.publish.walmart.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import com.estone.erp.publish.walmart.enums.WalmartPublishModeEnum;
import com.estone.erp.publish.walmart.handler.publish.param.AutoPublishMessage;
import com.estone.erp.publish.walmart.handler.publish.param.SpuPublishParam;
import com.estone.erp.publish.walmart.model.dto.WalmartPublishRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.walmart.controller
 * @Author: sj
 * @CreateTime: 2025-06-26  10:09
 * @Description: TODO
 */
@Slf4j
@RestController
@RequestMapping("/walmartPublish")
public class WalmartPublishController {

    @Resource
    private  WalmartTimePublishQueueService walmartTimePublishQueueService;

    @Resource
    private RabbitMqSender rabbitMqSender;
    /**
     * 直接刊登
     */
    @PostMapping("directPublish")
    public ApiResult<String> directPublish(@RequestBody WalmartPublishRequest request) {
        try {
            String user = WebUtils.getUserName();
            if (StringUtils.isEmpty(user)) {
                return ApiResult.newError("该用户不能操作");
            }
            request.setUser(user);
            request.validationData();


            List<String> existSpu = request.getSpuList();
            if (CollectionUtils.isEmpty(existSpu)){
                return ApiResult.newError("请选择产品");
            }
            for (String spu : existSpu) {
                SpuPublishParam spuPublishParam = new SpuPublishParam();
                spuPublishParam.setPublishType(WalmartPublishModeEnum.SPU_PUBLISH.getCode());
                spuPublishParam.setSpu(spu);
                spuPublishParam.setUser(request.getUser());
                spuPublishParam.setAccountNumber(request.getAccountNumber());
                spuPublishParam.setSkuDataSource(request.getSkuDataSource());
                AutoPublishMessage publishMessage = new AutoPublishMessage(WalmartPublishModeEnum.SPU_PUBLISH.getCode(), JSON.toJSONString(spuPublishParam));
                rabbitMqSender.send(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE, PublishQueues.WALMART_AUTO_PUBLISH_QUEUE_KEY, publishMessage);
            }
            List<String> spuList = request.getSpuList();
            spuList.removeIf(existSpu::contains);
            if (CollectionUtils.isNotEmpty(spuList)) {
                return ApiResult.newSuccess("部分处理成功");
            }
            return ApiResult.newSuccess("处理成功");
        }catch (Exception e) {
            log.error("直接刊登失败",e);
            return ApiResult.newError(e.getMessage());
        }
    }
    /**
     * 定时刊登
     * @param request
     * @return
     */
    @PostMapping("/timePublish")
    public ApiResult<String> timePublish(@RequestBody WalmartPublishRequest request){
        try {
            String user = WebUtils.getUserName();
            if (StringUtils.isEmpty(user)) {
                return ApiResult.newError("该用户不能操作");
            }
            request.setUser(user);
            request.validationData();
            walmartTimePublishQueueService.createTimePublishQueue(request);
            return ApiResult.newSuccess("请求成功");
        }catch (Exception e){
            log.error("定时刊登失败",e);
            return ApiResult.newError(e.getMessage());
        }
    }
}
