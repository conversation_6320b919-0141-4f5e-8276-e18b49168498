package com.estone.erp.publish.tidb.publishtidb.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * ozon 属性配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ozon_category_attribute_default_value")
public class OzonCategoryAttributeDefaultValue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类id
     */
    @ExcelProperty("分类id")
    private Integer categoryId;
    /**
     * 类目名称
     */
    @ExcelProperty("类目名称")
    private String categoryMultName;
    /**
     * ozon类目
     */
    @ExcelProperty("OZON类目")
    private String categoryFullMultName;
    /**
     * 属性id
     */
    @ExcelProperty("属性id")
    private Integer attributeId;
    /**
     * 属性名称
     */
    @ExcelProperty("属性名称")
    private String attributeName;
    /**
     * 默认值
     */
    @ExcelProperty("属性默认值")
    private String defaultValue;
    /**
     * 选项结点列表
     */
    @ExcelProperty("选项节点列表")
    private String options;

    /**
     * 导入人
     */
    @ExcelProperty("导入人")
    private String importBy;
    /**
     * 导入时间
     */
    @ExcelProperty("导入时间")
    private LocalDateTime importTime;
    /**
     * 同步时间
     */
    @ExcelProperty("同步时间")
    private LocalDateTime syncTime;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createdBy;

    /**
     * 更新人
     */
    @ExcelIgnore
    private String updatedBy;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ExcelIgnore
    private LocalDateTime updatedTime;


    /**
     * 判断默认值是否在选项里面
     */
    @ExcelIgnore
    private Boolean checkFlag;

    /**
     * 是否必填
     */
    @ExcelIgnore
    private Boolean isRequired;

    /**
     * 是否规格属性
     */
    @ExcelIgnore
    private Boolean isAspect;


}
