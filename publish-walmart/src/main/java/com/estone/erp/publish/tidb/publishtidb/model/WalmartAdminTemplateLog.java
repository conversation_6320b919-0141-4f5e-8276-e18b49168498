package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * walmart范本日志表
 * @TableName walmart_admin_template_log
 */
@TableName(value ="walmart_admin_template_log")
@Data
public class WalmartAdminTemplateLog {
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 范本ID
     */
    @TableField(value = "template_id")
    private Integer templateId;

    /**
     * 改前值
     */
    @TableField(value = "before_value")
    private String beforeValue;

    /**
     * 改后值
     */
    @TableField(value = "after_value")
    private String afterValue;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;
}