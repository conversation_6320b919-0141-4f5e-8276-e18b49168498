package com.estone.erp.publish.ozon.component.group;

import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.ozon.model.*;
import com.estone.erp.publish.ozon.model.dto.marketing.OzonMarketingConfigDO;
import com.estone.erp.publish.ozon.model.dto.marketing.OzonMarketingLogTypeEnum;
import com.estone.erp.publish.ozon.service.OzonAccountGroupService;
import com.estone.erp.publish.ozon.service.OzonMarketingConfigLogService;
import com.estone.erp.publish.ozon.service.OzonMarketingConfigService;
import com.estone.erp.publish.ozon.utils.OzonMarketingConfigLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OzonMarketingConfigGroup implements ConfigGroup {

    @Resource
    private OzonMarketingConfigService ozonMarketingConfigService;

    @Resource
    private OzonAccountGroupService ozonAccountGroupService;

    @Resource
    private OzonMarketingConfigLogService ozonMarketingConfigLogService;


    @Override
    public void sync(List<Integer> groupIds) {
        OzonMarketingConfigExample example = new OzonMarketingConfigExample();
        example.createCriteria().andGroupIdOrLike(groupIds).andStatusEqualTo(1);;
        List<OzonMarketingConfig> ozonMarketingConfigList = ozonMarketingConfigService.selectByExample(example);
        if (CollectionUtils.isEmpty(ozonMarketingConfigList)) {
            return;
        }

        List<Integer> allGroupIds = ozonMarketingConfigList.stream().filter(a -> StringUtils.isNotBlank(a.getAccountGroupId()))
                .map(a -> a.getAccountGroupId().split(","))
                .flatMap(Arrays::stream)
                .filter(StringUtils::isNotBlank)
                .map(Integer::parseInt)
                .distinct()
                .collect(Collectors.toList());
        allGroupIds = allGroupIds.stream().distinct().collect(Collectors.toList());

        OzonAccountGroupExample OzonAccountGroupExample = new OzonAccountGroupExample();
        OzonAccountGroupExample.createCriteria().andIdIn(allGroupIds);
        List<OzonAccountGroup> ozonAccountGroupList = ozonAccountGroupService.selectByExample(OzonAccountGroupExample);
        Map<Integer, OzonAccountGroup> groupMap = ozonAccountGroupList.stream().collect(Collectors.toMap(OzonAccountGroup::getId, a -> a, (a, b) -> a));

        for (OzonMarketingConfig ozonMarketingConfig : ozonMarketingConfigList) {
            try {
                String accountGroupId = ozonMarketingConfig.getAccountGroupId();
                if (StringUtils.isBlank(accountGroupId)) {
                    continue;
                }
                List<Integer> oldGroupIdsList = Arrays.stream(accountGroupId.split(",")).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList());
                Set<String> groupNameSet = new HashSet<>();
                Set<String> accountSet = new HashSet<>();
                Set<Integer> groupIdSet = new HashSet<>();
                for (Integer groupId : oldGroupIdsList) {
                    OzonAccountGroup OzonAccountGroup = groupMap.get(groupId);
                    if (OzonAccountGroup == null) {
                        continue;
                    }
                    groupNameSet.add(OzonAccountGroup.getGroupName());
                    groupIdSet.add(OzonAccountGroup.getId());
                    String accounts = OzonAccountGroup.getAccounts();
                    List<String> accountList = Arrays.stream(Optional.ofNullable(accounts).orElse("").split(",")).distinct().collect(Collectors.toList());
                    accountSet.addAll(accountList);
                }

                OzonMarketingConfigDO ozonMarketingConfigDO = BeanUtil.copyProperties(ozonMarketingConfig, OzonMarketingConfigDO.class);
                ozonMarketingConfigDO.setAccountGroupId(StringUtils.join(groupIdSet, ","));
                ozonMarketingConfigDO.setAccounts(StringUtils.join(accountSet, ","));
                ozonMarketingConfigDO.setAccountGroupName(StringUtils.join(groupNameSet, ","));
                ozonMarketingConfigService.saveOrUpdate(ozonMarketingConfigDO);
            } catch (Exception e) {
                OzonMarketingConfigLog ozonMarketingConfigLog = OzonMarketingConfigLogUtil.initOzonMarketingConfigLog(
                        ozonMarketingConfig.getId(), OzonMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG,
                        "errorSync", "同步分组失败",
                        e.getMessage(), null,
                        WebUtils.getUserName(), new Timestamp(System.currentTimeMillis()));
                ozonMarketingConfigLogService.insert(ozonMarketingConfigLog);
            }
        }
    }
}
