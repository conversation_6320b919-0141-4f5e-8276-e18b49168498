package com.estone.erp.publish.ozon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonAccountConfigCriteria;
import com.estone.erp.publish.ozon.model.OzonSkuSuffixHistory;
import com.estone.erp.publish.ozon.model.dto.account.AccountGrossProfitRateDO;
import com.estone.erp.publish.ozon.model.dto.account.OzonUpdateAccountConfigDO;
import com.estone.erp.publish.ozon.model.vo.OzonWareHouseInfoVO;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonSkuSuffixHistoryService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Ozon店铺配置控制器
 * 处理Ozon平台店铺账户配置的相关操作
 * 
 * <AUTHOR> ozon_account_config
 * 2023-04-13 11:56:39
 */
@RestController
@RequestMapping("ozonAccountConfig")
public class OzonAccountConfigController {

    @Resource
    private OzonAccountConfigService ozonAccountConfigService;

    @Resource
    private OzonSkuSuffixHistoryService ozonSkuSuffixHistoryService;
    @Autowired
    private RedisClusterTemplate redisClusterTemplate;

    private static final String SKU_SUFFIX_DUPLICATE_ERROR = "店铺后缀重复，请重新输入";

    /**
     * 处理Ozon账户配置的POST请求
     * 根据method参数执行不同的操作：
     * - searchOzonAccountConfig: 查询账户配置列表
     * - addOzonAccountConfig: 添加新的账户配置
     *
     * @param requestParam 请求参数
     * @return API结果
     */
    @PostMapping
    public ApiResult<?> postOzonAccountConfig(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String methodName = requestParam.getMethod();
        if (StringUtils.isNotBlank(methodName)) {
            switch (methodName) {
                case "searchOzonAccountConfig":
                    return searchOzonAccountConfig(requestParam);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 查询Ozon账户配置列表
     *
     * @param requestParam 包含查询条件的请求参数
     * @return 查询结果
     */
    private ApiResult<?> searchOzonAccountConfig(ApiRequestParam<String> requestParam) {
        try {
            CQuery<OzonAccountConfigCriteria> queryCondition = requestParam.getArgsValue(
                    new TypeReference<>() {}
            );
            Asserts.isTrue(queryCondition != null, ErrorCode.PARAM_EMPTY_ERROR);

            return ozonAccountConfigService.search(queryCondition);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 验证SKU后缀是否可用
     * 
     * @param skuSuffix 要验证的SKU后缀
     * @param currentConfigId 当前配置ID（更新时使用，新增时为null）
     * @return 验证结果
     */
    private ApiResult<?> validateSkuSuffix(String skuSuffix, Integer currentConfigId) {
        if (StringUtils.isBlank(skuSuffix)) {
            return ApiResult.newSuccess();
        }

        // 如果是更新操作，先检查当前店铺是否已经使用了这个后缀
        if (currentConfigId != null) {
            OzonAccountConfig currentConfig = ozonAccountConfigService.selectByPrimaryKey(currentConfigId);
            // 如果后缀没有变化，则不需要验证
            if (currentConfig != null && skuSuffix.equals(currentConfig.getSkuSuffix())) {
                return ApiResult.newSuccess();
            }
        }

        // 检查后缀是否已被其他店铺使用
        boolean exists = ozonSkuSuffixHistoryService.isSkuSuffixExists(skuSuffix);
        if (exists) {
            return ApiResult.newError(SKU_SUFFIX_DUPLICATE_ERROR);
        }

        return ApiResult.newSuccess();
    }

    /**
     * 保存SKU后缀到历史记录
     * 
     * @param skuSuffix 要保存的SKU后缀
     */
    private void saveSkuSuffixToHistory(String skuSuffix) {
        if (StringUtils.isBlank(skuSuffix)) {
            return;
        }

        OzonSkuSuffixHistory history = new OzonSkuSuffixHistory();
        history.setSkuSuffix(skuSuffix);
        history.setCreateTime(new Timestamp(System.currentTimeMillis()));
        ozonSkuSuffixHistoryService.insert(history);
    }

    /**
     * 根据ID获取Ozon账户配置信息
     *
     * @param id 账户配置ID
     * @return 账户配置信息
     */
    @GetMapping(value = "/{id}")
    public ApiResult<?> getOzonAccountConfig(@PathVariable(value = "id", required = true) Integer id) {
        try {
            OzonAccountConfig accountConfig = ozonAccountConfigService.selectConfigWithCalePriceRule(id);
            return ApiResult.newSuccess(accountConfig);
        } catch (Exception e) {
            return ApiResult.newError("获取账户配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定账户的当前货币配置
     *
     * @param account 账户名称
     * @return 账户的货币配置
     */
    @GetMapping(value = "/getOzonAccountCurrentConfig/{account}")
    public ApiResult<?> getOzonAccountCurrentConfig(@PathVariable(value = "account", required = true) String account) {
        try {
            OzonAccountConfig accountConfig = ozonAccountConfigService.selectByAccountNumber(account);
            return ApiResult.newSuccess(accountConfig.getCurrency());
        } catch (Exception e) {
            return ApiResult.newError("获取账户货币配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新Ozon账户配置信息
     *
     * @param id 账户配置ID
     * @param accountConfig 更新的账户配置信息
     * @return 更新结果
     */
    @PutMapping(value = "/update/{id}")
    public ApiResult<?> putOzonAccountConfig(@PathVariable("id") Integer id, @RequestBody OzonAccountConfig accountConfig) {
        try {
            // 验证SKU后缀
            ApiResult<?> validationResult = validateSkuSuffix(accountConfig.getSkuSuffix(), id);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            Double bottomPriceGrossProfit = accountConfig.getBottomPriceGrossProfit();
            if (bottomPriceGrossProfit != null && bottomPriceGrossProfit < 0.05) {
                return ApiResult.newError("最低价毛利率不能小于0.05");
            }
            // 获取旧配置（用于比较后缀是否变化）
            OzonAccountConfig oldConfig = ozonAccountConfigService.selectByPrimaryKey(id);

            // 更新配置
            ozonAccountConfigService.updateConfigWithCalePriceRule(accountConfig);

            // 如果后缀发生了变化，保存新后缀到历史记录
            if (StringUtils.isNotBlank(accountConfig.getSkuSuffix()) && 
                (oldConfig == null || !accountConfig.getSkuSuffix().equals(oldConfig.getSkuSuffix()))) {
                saveSkuSuffixToHistory(accountConfig.getSkuSuffix());
            }

            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError("更新账户配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新账户配置
     *
     * @param updateConfigRequest 批量更新请求参数
     * @return 更新结果
     */
    @PostMapping("/batchUpdateConfigById")
    public ApiResult<String> batchUpdateConfigById(@RequestBody OzonUpdateAccountConfigDO updateConfigRequest) {
        try {
            // 校验最低价毛利率：如果有值且小于0.05，则返回错误
            Double bottomPriceGrossProfit = updateConfigRequest.getBottomPriceGrossProfit();
            if (bottomPriceGrossProfit != null && bottomPriceGrossProfit < 0.05) {
                return ApiResult.newError("最低价毛利率不能小于0.05");
            }
            
            ozonAccountConfigService.batchUpdateConfigById(updateConfigRequest);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError("批量更新账户配置失败: " + e.getMessage());
        }
    }

    /**
     * 同步账户信息
     *
     * @return 同步结果
     */
    @PostMapping(value = "/syncAccount")
    public ApiResult<?> syncAccountInfo() {
        try {
            ozonAccountConfigService.syncAccount();
            return ApiResult.newSuccess("账户信息同步成功");
        } catch (Exception e) {
            return ApiResult.newError("账户信息同步失败: " + e.getMessage());
        }
    }

    /**
     * 初始化店铺SKU后缀历史记录
     * 将所有店铺的SKU后缀添加到历史记录中
     *
     * @return 初始化结果
     */
    @PostMapping(value = "/initSkuSuffixHistory")
    public ApiResult<?> initSkuSuffixHistory() {
        try {
            ozonSkuSuffixHistoryService.initSkuSuffixHistory();
            return ApiResult.newSuccess("初始化店铺后缀历史记录成功");
        } catch (Exception e) {
            return ApiResult.newError("初始化店铺后缀历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 模板页面获取店铺配置信息
     * 根据账户编号获取对应的店铺配置信息
     *
     * @param accountNumber 店铺账户编号
     * @return 店铺配置信息
     */
    @PostMapping("template/info")
    public ApiResult<OzonAccountConfig> getAccountConfigInfo(@RequestBody String accountNumber) {
        try {
            OzonAccountConfig accountConfig = ozonAccountConfigService.selectByAccountNumber(accountNumber);
            if (accountConfig != null) {
                return ApiResult.newSuccess(accountConfig);
            }
            return ApiResult.newError("未查询到店铺信息");
        } catch (Exception e) {
            return ApiResult.newError("查询店铺信息异常: " + e.getMessage());
        }
    }

    /**
     * 同步店铺仓库信息
     * 从Ozon平台获取最新的仓库信息并更新到系统中
     *
     * @param accountNumbers 需要同步的店铺账户编号列表
     * @return 同步结果
     */
    @PostMapping(value = "/syncAccountWareHouse")
    public ApiResult<String> syncAccountWareHouse(@RequestBody List<String> accountNumbers) {
        try {
            return ozonAccountConfigService.syncAccountWareHouse(accountNumbers, false);
        } catch (Exception e) {
            return ApiResult.newError("同步店铺仓库信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取店铺仓库信息
     * 返回指定店铺的所有仓库信息
     *
     * @param accountNumbers 需要获取仓库信息的店铺账户编号列表
     * @return 店铺仓库信息映射表，key为店铺账户编号，value为该店铺的仓库信息列表
     */
    @PostMapping("accountWareHouseInfo")
    public ApiResult<Map<String, List<OzonWareHouseInfoVO>>> getAccountWareHouseInfo(@RequestBody List<String> accountNumbers) {
        try {
            return ozonAccountConfigService.getAccountWareHouseInfo(accountNumbers);
        } catch (Exception e) {
            return ApiResult.newError("获取店铺仓库信息失败: " + e.getMessage());
        }
    }

    /**
     * 校验算价毛利率
     * 检查指定账户的毛利率设置是否合理
     *
     * @param profitRateList 需要校验的账户毛利率列表
     * @return 校验结果
     */
    @PostMapping("/checkCalcGrossProfitRate")
    public ApiResult<String> checkCalcGrossProfitRate(@RequestBody List<AccountGrossProfitRateDO> profitRateList) {
        try {
            return ApiResult.newSuccess(ozonAccountConfigService.checkCalcGrossProfitRate(profitRateList));
        } catch (Exception e) {
            return ApiResult.newError("校验算价毛利率失败: " + e.getMessage());
        }
    }

    /**
     * 删除当天额度用完标识
     * @param accountNumber 店铺
     * @param day 天
     * @return boolean
     */
    @GetMapping("/delete/redisKey/dailyCreateLimitExceeded")
    public ApiResult<Boolean> deleteRedisKey(String accountNumber, String day) {
        redisClusterTemplate.del(RedisConstant.DAILY_CREATE_LIMIT_EXCEEDED + accountNumber + ":" + day);
        return ApiResult.newSuccess();
    }
}
