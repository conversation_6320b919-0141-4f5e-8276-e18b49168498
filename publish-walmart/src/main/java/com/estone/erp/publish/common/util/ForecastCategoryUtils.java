package com.estone.erp.publish.common.util;

import com.estone.erp.common.model.CategoryForecastDTO;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonHttpUtils;
import com.estone.erp.publish.ozon.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryForecast;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartCategoryForecast;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.common.util
 * @Author: sj
 * @CreateTime: 2025-06-10  15:17
 * @Description: 预测分类工具类
 */
@Slf4j
public class ForecastCategoryUtils {

    /**
     * 尝试匹配类目
     *
     * @param forecastResult   预测结果
     * @param builderTemplate  构建模板
     * @param forecast         预测记录
     * @return 匹配结果对，左边是预测结果，右边是是否匹配成功
     */
    public static Pair<ApiResult<CategoryForecastDTO>, Boolean> tryMatchCategory(
            ApiResult<CategoryForecastDTO> forecastResult, 
            BuilderTemplateDO builderTemplate, 
            OzonCategoryForecast forecast) {

        CategoryForecastDTO result = forecastResult.getResult();
        if (result == null || StringUtils.isBlank(result.getCat())) {
            return Pair.of(forecastResult, false);
        }
        Matcher matcher = Pattern.compile("[A-Za-z]*(\\d+)-").matcher(result.getCat());
        if (matcher.find()) {
            Integer categoryId = Integer.valueOf(matcher.group(1));
            builderTemplate.setCategoryId(categoryId);
            forecast.setCategoryId(categoryId);
            return Pair.of(forecastResult, true);
        }
        return Pair.of(forecastResult, false);
    }

    /**
     * 尝试匹配沃尔玛类目
     *
     * @param forecastResult   预测结果
     * @param forecast         预测记录
     * @return 匹配结果对，左边是预测结果，右边是提取的子分类名称
     */
    public static Pair<ApiResult<CategoryForecastDTO>, String> tryMatchWalmartCategory(
            ApiResult<CategoryForecastDTO> forecastResult, 
            WalmartCategoryForecast forecast) {

        CategoryForecastDTO result = forecastResult.getResult();
        if (result == null || StringUtils.isBlank(result.getCat())) {
            return Pair.of(forecastResult, null);
        }
        
        // 提取最后一个斜杠后的子分类
        String subCategory = extractWalmartSubCategory(result.getCat());
        if (StringUtils.isNotBlank(subCategory)) {
            // 将原始分类路径保存到forecast中
            forecast.setCategoryPath(result.getCat());
            forecast.setHandledCategoryPath(subCategory);
            return Pair.of(forecastResult, subCategory);
        }
        return Pair.of(forecastResult, null);
    }

    /**
     * 提取沃尔玛子分类（最后一个斜杠后的内容）
     *
     * @param categoryPath 分类路径，如 "Beauty/Skin Care/Eye Creams & Serums"
     * @return 子分类名称，如 "Eye Creams & Serums"
     */
    public static String extractWalmartSubCategory(String categoryPath) {
        if (StringUtils.isBlank(categoryPath)) {
            return null;
        }
        
        int lastSlashIndex = categoryPath.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < categoryPath.length() - 1) {
            return categoryPath.substring(lastSlashIndex + 1).trim();
        }
        
        // 如果没有斜杠，返回原字符串
        return categoryPath.trim();
    }

    /**
     * 通过类目路径尝试匹配沃尔玛类目
     *
     * @param spuInfo          SPU信息
     * @param forecast         预测记录
     * @return 匹配结果对，左边是预测结果，右边是提取的子分类名称
     */
    public static Pair<ApiResult<CategoryForecastDTO>, String> tryMatchWalmartCategoryByCategoryPath(
            SpuInfo spuInfo, 
            WalmartCategoryForecast forecast) {

        String categoryPath = spuInfo.getCategoryPath();
        String handledCategoryPath = handledCategoryPath(categoryPath);
        forecast.setCategoryPath(categoryPath);
        forecast.setHandledCategoryPath(handledCategoryPath);

        ApiResult<CategoryForecastDTO> forecastResult = CommonHttpUtils.getCategoryForecast(
                handledCategoryPath, SaleChannelEnum.WALMART.getChannelName().toLowerCase(), 3);

        return tryMatchWalmartCategory(forecastResult, forecast);
    }

    /**
     * 通过类目路径尝试匹配类目
     *
     * @param spuInfo          SPU信息
     * @param builderTemplate  构建模板
     * @param forecast         预测记录
     * @return 匹配结果对，左边是预测结果，右边是是否匹配成功
     */
    public static Pair<ApiResult<CategoryForecastDTO>, Boolean> tryMatchCategoryByCategoryPath(
            SpuInfo spuInfo, 
            BuilderTemplateDO builderTemplate, 
            OzonCategoryForecast forecast) {

        String categoryPath = spuInfo.getCategoryPath();
        String handledCategoryPath = handledCategoryPath(categoryPath);
        forecast.setCategoryPath(categoryPath);
        forecast.setHandledCategoryPath(handledCategoryPath);

        ApiResult<CategoryForecastDTO> forecastResult = CommonHttpUtils.getCategoryForecast(
                handledCategoryPath, SaleChannelEnum.WALMART.getChannelName().toLowerCase(), 3);

        return tryMatchCategory(forecastResult, builderTemplate, forecast);
    }

    /**
     * 处理类目路径，比如男装>毛衣 则取毛衣
     *
     * @param categoryPath 类目路径
     * @return 处理后的类目路径
     */
    public static String handledCategoryPath(String categoryPath) {
        if (StringUtils.isBlank(categoryPath)) {
            return categoryPath; // 原样返回，避免 NPE
        }

        // 比如男装>毛衣 则取毛衣
        String[] parts = categoryPath.split(">");
        return parts[parts.length - 1].trim();
    }

    /**
     * 处理标题，去除特殊字符
     */
    public static String handleTitle(String title) {
        if (StringUtils.isBlank(title)) {
            return title; // 原样返回，避免 NPE
        }

        // 去除所有全角（……）和半角(……)括号及内容
        return title.replaceAll("（[^）]*）", "")
                .replaceAll("\\([^)]*\\)", "")
                .trim();
    }
}
