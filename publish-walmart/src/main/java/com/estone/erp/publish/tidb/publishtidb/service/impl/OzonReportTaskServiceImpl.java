package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportTask;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonReportTaskMapper;
import com.estone.erp.publish.tidb.publishtidb.service.IOzonReportTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
public class OzonReportTaskServiceImpl extends ServiceImpl<OzonReportTaskMapper, OzonReportTask> implements IOzonReportTaskService {


    @Override
    public Set<String> selectAccountNumberByDay(List<String> listAccountNumber, LocalDate nowDay) {
        LambdaQueryWrapper<OzonReportTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OzonReportTask::getDay, nowDay);
        queryWrapper.in(OzonReportTask::getAccountNumber, listAccountNumber);
        List<OzonReportTask> list = this.list(queryWrapper);
        if(list!= null && !list.isEmpty()){
            return list.stream().map(OzonReportTask::getAccountNumber).collect(Collectors.toSet());
        }
        return Set.of();
    }
}
