package com.estone.erp.publish.yandex.job;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.yandex.enums.YandexFeedTaskEnums;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 保留近x天的处理报告
 */
@Slf4j
@Component
public class YandexDeleteReportJobHandler extends AbstractJobHandler {
    @Resource
    private FeedTaskService feedTaskService;

    public YandexDeleteReportJobHandler() {
        super(YandexDeleteReportJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private Integer xDayBefore;
    }


    @Override
    @XxlJob("YandexDeleteReportJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("*****************定时删除处理报告 start*****************");
        YandexDeleteReportJobHandler.InnerParam innerParam = passParam(param, YandexDeleteReportJobHandler.InnerParam.class);

        if (null == innerParam.getXDayBefore()) {
            return ReturnT.FAIL;
        }

        Date xDayBeforeDate = DateUtils.addDays(new Date(), -innerParam.getXDayBefore());
        String format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(xDayBeforeDate);
        int count = 0;
        for (YandexFeedTaskEnums.TaskType value : YandexFeedTaskEnums.TaskType.values()) {
            int i = feedTaskService.deleteByPage(SaleChannel.CHANNEL_YANDEX, value.name(), xDayBeforeDate);
            count += i;
            XxlJobLogger.log("删除了 {}之前 {}条{}数据", format, i, value.getDesc());
        }

        XxlJobLogger.log("本次删除了 {}条数据", count);
        XxlJobLogger.log("*****************定时删除处理报告 end*****************");
        return ReturnT.SUCCESS;
    }

    public <T> T passParam(String param, Class<T> tClass) {
        if (StringUtils.isBlank(param)) return null;

        try {
            return JSON.parseObject(param, tClass);
        } catch (Exception e) {
            XxlJobLogger.log("passParamError:{}", e.getMessage());
            logger.error("passParamError:{}", e.getMessage());
        }
        return null;
    }
}
