package com.estone.erp.publish.yandex.service;

import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.yandex.model.dto.YandexSyncItemDTO;
import com.estone.erp.publish.yandex.model.dto.YandexUpdateDO;
import com.estone.erp.publish.yandex.model.vo.YandexListingVO;

import java.util.List;


public interface YandexEsItemService {
    EsYandexItem getOneSelectFieldsWithId(EsYandexItemRequest request);

    PageInfo<YandexListingVO> search(EsYandexItemRequest request);

    void syncProductInfo(YandexSyncItemDTO syncItemDTO);

    void syncCategoryPath(String esId, Long categoryId);

    ApiResult<String> updateStock(List<YandexUpdateDO> updateParam);

    List<EsYandexItem> getEsYandexItems(EsYandexItemRequest request);

    void syncAllAccountList(List<String> accountNumbers, String beginDate, String syncType);

    void syncAccountList(List<String> accountNumbers);
}
