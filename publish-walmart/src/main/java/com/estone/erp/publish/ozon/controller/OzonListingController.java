package com.estone.erp.publish.ozon.controller;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.ozon.enums.OzonCalcPriceTypeEnum;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonUpdatePriceTypeEnum;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRule;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceRequest;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.OzonSyncItemDO;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.vo.OzonEditItemDescVO;
import com.estone.erp.publish.ozon.model.vo.OzonListingVO;
import com.estone.erp.publish.ozon.model.vo.OzonWarehouseStockInfoVO;
import com.estone.erp.publish.ozon.service.OzonEsItemService;
import com.estone.erp.publish.ozon.service.OzonEstdaLogisticsRuleService;
import com.estone.erp.publish.ozon.utils.OzonCalcUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ozon在线列表
 * <AUTHOR>
 * @date 2023-04-04 17:16
 */
@Slf4j
@RestController
@RequestMapping("ozon/listing")
public class OzonListingController {

    @Autowired
    private OzonEsItemService esItemService;

    @Autowired
    private OzonEstdaLogisticsRuleService ozonEstdaLogisticsRuleService;

    /**
     * 查询
     */
    @PostMapping("search")
    public ApiResult<PageInfo<OzonListingVO>> searchListing(@RequestBody EsOzonItemRequest request) {
        try {
            PageInfo<OzonListingVO> pageInfo = esItemService.searchListing(request);
            return ApiResult.newSuccess(pageInfo);
        } catch (Exception e) {
            log.error("search exception:", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 同步产品
     * @param syncItemDO 商品编码
     */
    @PostMapping("sync/productInfo")
    public ApiResult<String> syncProductInfo(@RequestBody OzonSyncItemDO syncItemDO) {
        try {
            esItemService.syncProductInfo(syncItemDO);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("同步请求成功,请前往处理报告查询结果");
    }


    /**
     * 同步店铺产品
     * @param accountNumbers 店铺
     */
    @PostMapping("sync/account/list")
    public ApiResult<String> syncAccountList(@RequestBody List<String> accountNumbers) {
        esItemService.syncAccountList(accountNumbers);
        return ApiResult.newSuccess("同步请求成功,请前往处理报告查询结果");
    }

    /**
     * 同步所有店铺产品
     * */
    @GetMapping("sync/all/account/list")
    public ApiResult<String> syncAllAccountList() {
        esItemService.syncAllAccountList(null, null, OzonFeedTaskEnums.SyncItemType.DELTA.name());
        return ApiResult.newSuccess("同步请求成功,请前往处理报告查询结果");
    }

    /**
     * 修改库存
     */
    @PostMapping("update/stock")
    public ApiResult<String> updateStock(@RequestBody List<OzonUpdateDO> updateParam) {
        return esItemService.updateStock(updateParam);
    }

    /**
     * 修改价格
     */
    @PostMapping("update/price")
    public ApiResult<String> updatePrice(@RequestBody List<OzonUpdateDO> updateParam) {
        return esItemService.updatePrice(updateParam, OzonUpdatePriceTypeEnum.UPDATE_PRICE, false);
    }

    /**
     * 修改最低价
     */
    @PostMapping("update/min/price")
    public ApiResult<String> updateMinPrice(@RequestBody List<OzonUpdateDO> updateParam) {
        return esItemService.updatePrice(updateParam, OzonUpdatePriceTypeEnum.UPDATE_MIN_PRICE, false);
    }

    /**
     * 在线列表手动算价
     *
     * @param requestList
     * @return
     */
    @PostMapping("batchCalcPrice")
    public ApiResult<List<OzonCalcPriceResponse>> batchCalcPrice(@RequestBody List<OzonCalcPriceRequest> requestList, @RequestParam Integer type) {
        try {
            if (OzonCalcPriceTypeEnum.AUTO_PRICE.getCode().equals(type)) {
                getSpecialGoodsCodeAndLinkTagAndWeight(requestList);
                List<OzonEstdaLogisticsRule> allRulesByCache = ozonEstdaLogisticsRuleService.getAllRulesByCache();
                return OzonCalcUtils.listingBatchCalcByAutoPrice(requestList, allRulesByCache);
            }
            if (OzonCalcPriceTypeEnum.CALC_CONFIGURE_RULES.getCode().equals(type)) {
                // 获取产品重量和标签
                OzonCalcUtils.getWeightAndTag(requestList);
                getSpecialGoodsCodeAndLinkTagAndWeight(requestList);
            }

            List<OzonCalcPriceResponse> responses = OzonCalcUtils.listingBatchCalc(requestList, type);
            return ApiResult.newSuccess(responses);
        } catch (Exception e) {
            log.error("算价失败 " + e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    private void getSpecialGoodsCodeAndLinkTagAndWeight(List<OzonCalcPriceRequest> requestList) {
        List<String> sellerSku = requestList.stream().map(OzonCalcPriceRequest::getBusinessId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerSku)) {
            throw new RuntimeException("参数为空");
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setSellerSkus(sellerSku);
        request.setFields(new String[] {"actualWeight", "linkTag",  "specialGoodsCode", "sellerSku", "accountNumber", "actualWeight"});
        List<EsOzonItem> dbList = esItemService.listItemByRequest(request);
        Map<String, Map<String, List<EsOzonItem>>> collect =
                dbList.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber, Collectors.groupingBy(EsOzonItem::getSellerSku)));
        Map<String, List<EsOzonItem>> emptyMap = Maps.newHashMap();
        for (OzonCalcPriceRequest ozonCalcPriceRequest : requestList) {
            String businessId = ozonCalcPriceRequest.getBusinessId();
            String accountNumber = ozonCalcPriceRequest.getAccountNumber();
            Map<String, List<EsOzonItem>> valueList = collect.getOrDefault(accountNumber, emptyMap);
            List<EsOzonItem> esOzonItems = valueList.get(businessId);
            if (CollectionUtils.isEmpty(esOzonItems)) {
                throw new RuntimeException(businessId + ":未找到对应商品");
            }
            EsOzonItem esOzonItem = esOzonItems.get(0);
            if (esOzonItem.getActualWeight() != null) {
                ozonCalcPriceRequest.setWeight(esOzonItem.getActualWeight() * 1000);
            }
            // sku个数这里设置1，因为重量已经乘以sku个数了
            ozonCalcPriceRequest.setSkuCount(1);
            ozonCalcPriceRequest.setLinkTag(esOzonItem.getLinkTag());
            ozonCalcPriceRequest.setSpecialGoodsCode(esOzonItem.getSpecialGoodsCode());
        }
    }

    /**
     * 删除Item
     */
    @PostMapping("delete/item")
    public ApiResult<String> deleteItem(@RequestBody List<Long> productId) {
        return esItemService.deleteItem(productId);
    }

    /**
     * 恢复归档数据
     * @param productId 产品id
     * @return 恢复结果
     */
    @PostMapping("unarchive/item")
    public ApiResult<String> unarchiveItems(@RequestBody List<Long> productId) {
        return esItemService.unarchiveItems(productId);
    }

    @GetMapping("downloadField")
    public ApiResult<Map<String, String>> downloadField() {
        Map<String, String> resultMap = Maps.newLinkedHashMap();
        Class<OzonListingVO> esFruugoItemClass = OzonListingVO.class;
        Field[] declaredFields = esFruugoItemClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (null != excelProperty) {
                String[] value = excelProperty.value();
                resultMap.put(field.getName(), value[0]);
            }
        }
        return ApiResult.newSuccess(resultMap);
    }

    /**
     * 导出
     */
    @PostMapping("export")
    public ApiResult<String> export(@RequestBody EsOzonItemRequest request) {
        return esItemService.export(request);
    }

    /**
     * 编辑产品描述
     * @param productIds 商品编码
     */
    @PostMapping("editItemDesc")
    public ApiResult<List<OzonEditItemDescVO>> editItemDesc(@RequestBody List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return ApiResult.newError("请勾选数据后再操作");
        }
        return esItemService.editItemDesc(productIds);
    }

    /**
     * 修改产品描述
     */
    @PostMapping("updateItemDesc")
    public ApiResult<String> updateItemDesc(@RequestBody List<OzonEditItemDescVO> updateData) {
        if (CollectionUtils.isEmpty(updateData)) {
            return ApiResult.newError("请勾选数据后再操作");
        }
        return esItemService.updateItemDesc(updateData);
    }

    /**
     * 获取修改库存详细信息
     */
    @PostMapping("warehouseStockInfo")
    public ApiResult<List<OzonWarehouseStockInfoVO>> getWarehouseStockInfo(@RequestBody List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return ApiResult.newError("请勾选数据后再操作");
        }
        return esItemService.getWarehouseStockInfo(productIds);
    }

    /**
     * 标识OZON线上物流禁运（原标识空运）
     */
    @PostMapping("markAirFreight")
    public ApiResult<String> markAirFreight(@RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数不能为空");
        }

        try {
            esItemService.markAirFreight(idList);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }


    /**
     * 取消OZON线上物流禁运（原取消空运）
     */
    @PostMapping("deleteAirFreight")
    public ApiResult<String> deleteAirFreight(@RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数不能为空");
        }
        try {
            esItemService.deleteAirFreight(idList);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }
}
