package com.estone.erp.publish.walmart.model.dto;

import com.estone.erp.common.exception.BusinessException;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.walmart.model.dto
 * @Author: sj
 * @CreateTime: 2025-06-25  14:34
 * @Description: Walmart刊登请求参数
 */
@Data
public class WalmartPublishRequest {
    /**
     * 刊登店铺
     */
    private String accountNumber;

    /**
     * 当前用户
     */
    private String user;

    /**
     * 勾选的spu数据
     */
    private List<String> spuList;

    /**
     * 刊登类型
     * 1:直接刊登
     * 2:定时刊登
     */
    private Integer publishType;

    /**
     * 数据来源
     * {@link com.estone.erp.publish.platform.enums.SkuDataSourceEnum}
     */
    private Integer skuDataSource;

    /**
     * 批次数量
     */
    private Integer batchNumber;

    /**
     * 开始刊登时间 yyyy-mm-dd HH:MM:SS
     */
    private String publishTime;

    /**
     * 刊登间隔时间 mm 分钟
     */
    private Integer intervalTime;


    public void validationData() {
        if (StringUtils.isEmpty(this.accountNumber)) {
            throw new BusinessException("刊登店铺不能为空");
        }

        if (CollectionUtils.isEmpty(this.spuList)) {
            throw new BusinessException("spu不能为空");
        }
        if (this.publishType == null) {
            throw new BusinessException("刊登类型不能为空");
        }
        if (this.skuDataSource == null) {
            throw new BusinessException("数据来源不能为空");
        }
        if (this.publishType == 2) {
            if (StringUtils.isEmpty(this.publishTime)) {
                throw new BusinessException("开始刊登时间不能为空");
            }

            if (this.batchNumber == null) {
                throw new BusinessException("批次数量不能为空");
            }

            if (this.intervalTime == null) {
                throw new BusinessException("间隔时间不能为空");
            }

        }


    }
}
