package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.OzonReportTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
public interface IOzonReportTaskService extends IService<OzonReportTask> {

    /**
     * 判断是否跑过
     * @param listAccountNumber
     * @param nowDay
     * @return
     */
    Set<String> selectAccountNumberByDay(List<String> listAccountNumber, LocalDate nowDay);
}
