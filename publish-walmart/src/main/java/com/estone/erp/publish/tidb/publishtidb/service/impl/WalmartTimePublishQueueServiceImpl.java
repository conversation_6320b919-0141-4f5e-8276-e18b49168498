package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartTimePublishQueueMapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartTimePublishQueuePageDto;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import com.estone.erp.publish.walmart.enums.WalmartTimePublishEnums;
import com.estone.erp.publish.walmart.model.dto.WalmartPublishRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【walmart_time_publish_queue(walmart定时刊登队列)】的数据库操作Service实现
* @createDate 2025-06-25 15:43:19
*/
@Service
public class WalmartTimePublishQueueServiceImpl extends ServiceImpl<WalmartTimePublishQueueMapper, WalmartTimePublishQueue>
    implements WalmartTimePublishQueueService{

    @Resource
    private PermissionsHelper permissionsHelper;
    @Override
    public CQueryResult<WalmartTimePublishQueue> search(WalmartTimePublishQueuePageDto dto) {
        Page<WalmartTimePublishQueue> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        /**
         * 权限控制
         */
        applyPermissions(dto);

        IPage<WalmartTimePublishQueue> result = this.page(page, dto.buildQueryWrapper());
        CQueryResult<WalmartTimePublishQueue> cQueryResult = new CQueryResult<>();
        cQueryResult.setRows(result.getRecords());
        cQueryResult.setTotal(result.getTotal());
        cQueryResult.setTotalPages((int)result.getPages());
        cQueryResult.setSuccess(true);
        return cQueryResult;
    }

    private void applyPermissions(WalmartTimePublishQueuePageDto dto) {
        List<String> authorizedAccounts = permissionsHelper.getCurrentUserPermission(dto.getAccountNumbers(), null, null, null, SaleChannel.CHANNEL_SMT, true);
        dto.setAccountNumbers(authorizedAccounts);
    }

    @Override
    public ApiResult<?> recovery(List<Integer> ids) {
        LambdaUpdateWrapper<WalmartTimePublishQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WalmartTimePublishQueue::getId, ids);
        updateWrapper.eq(WalmartTimePublishQueue::getStatus, WalmartTimePublishEnums.PAUSE.getCode());
        List<WalmartTimePublishQueue> list = this.list(updateWrapper);
        if (CollectionUtils.isEmpty(list)){
            return  ApiResult.newError("没有可以恢复的数据");
        }
        updateWrapper.set(WalmartTimePublishQueue::getStatus, WalmartTimePublishEnums.WAITING.getCode());
        this.update(updateWrapper);
        return ApiResult.newSuccess();
    }

    @Override
    public ApiResult<?> stop(List<Integer> ids) {
        LambdaUpdateWrapper<WalmartTimePublishQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WalmartTimePublishQueue::getId, ids);
        updateWrapper.eq(WalmartTimePublishQueue::getStatus, WalmartTimePublishEnums.WAITING.getCode());
        List<WalmartTimePublishQueue> list = this.list(updateWrapper);
        if (CollectionUtils.isEmpty(list)){
            return  ApiResult.newError("没有可以停止的数据");
        }
        updateWrapper.set(WalmartTimePublishQueue::getStatus, WalmartTimePublishEnums.PAUSE.getCode());
        this.update(updateWrapper);
        return ApiResult.newSuccess();
    }

    @Override
    public void createTimePublishQueue(WalmartPublishRequest request) {
        String accountNumber = request.getAccountNumber();
        String user = request.getUser();

        List<String> existSpu = request.getSpuList();
        if (CollectionUtils.isEmpty(existSpu)){
            throw new BusinessException("请选择产品");
        }

        // 刊登时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime publishTime = LocalDateTimeUtil.of(request.getPublishTime(), formatter);
        LocalDateTime now = LocalDateTime.now();
        Timestamp timestamp = Timestamp.valueOf(now);
        List<List<String>> publishBatchParts;
        if (request.getBatchNumber() > 0) {
            publishBatchParts = Lists.partition(existSpu, request.getBatchNumber());
        }else {
            publishBatchParts = new ArrayList<>();
            publishBatchParts.add(existSpu);
        }

        List<WalmartTimePublishQueue> timePublishQueues = new ArrayList<>();
        for (List<String> publishBatchPart : publishBatchParts) {
            LocalDateTime currPublishTime = publishTime;
            List<WalmartTimePublishQueue> queueList = publishBatchPart.stream().map(spu -> {
                WalmartTimePublishQueue queue = new WalmartTimePublishQueue();
                queue.setAccountNumber(accountNumber);
                queue.setSkuDataSource(request.getSkuDataSource());
                queue.setStatus(WalmartTimePublishEnums.WAITING.getCode());
                queue.setArticleNumber(spu);
                queue.setPublishStatus(null);
                queue.setCreatedBy(user);
                queue.setCreatedTime(timestamp);
                queue.setPublishTime(Timestamp.valueOf(currPublishTime));
                queue.setUpdateTime(timestamp);
                queue.setPublisherSource(0);
                queue.setPublishRole(1);
                queue.setTitle(null);
                queue.setExtra(null);
                return queue ;
            }).collect(Collectors.toList());

            publishTime = publishTime.plusMinutes(request.getIntervalTime());
            timePublishQueues.addAll(queueList);
        }

        // 批量插入
        List<List<WalmartTimePublishQueue>> partition = Lists.partition(timePublishQueues, 200);
        for (List<WalmartTimePublishQueue> queueList : partition) {
            this.saveBatch(queueList);
        }
    }
}




