package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.OzonAdjustPriceSkuLog;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonAdjustPriceSkuLogMapper;
import com.estone.erp.publish.tidb.publishtidb.service.OzonAdjustPriceSkuLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ozon调价的sku的日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class OzonAdjustPriceSkuLogServiceImpl extends ServiceImpl<OzonAdjustPriceSkuLogMapper, OzonAdjustPriceSkuLog> implements OzonAdjustPriceSkuLogService {

}
