package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * walmart发布操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("walmart_publish_operation_log")
public class WalmartPublishOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务ID
     */
    private String modId;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 用户
     */
    private String user;

    /**
     * 辅助查询字段
     */
    private String object;

    /**
     * 辅助查询字段1
     */
    private String object1;

    /**
     * 标识
     */
    private Integer state;

    /**
     * 元数据
     */
    private String metaObj;

    /**
     * 改前数据
     */
    private String beforeObj;

    /**
     * 改后数据
     */
    private String afterObj;

    /**
     * 标记时间
     */
    private LocalDateTime createdTime;


}
