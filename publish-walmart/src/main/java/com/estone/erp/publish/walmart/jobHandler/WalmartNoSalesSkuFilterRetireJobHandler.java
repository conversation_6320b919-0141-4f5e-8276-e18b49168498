package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.mapper.WalmartTaskOffLinkListingLogMapper;
import com.estone.erp.publish.walmart.mapper.custom.CustomWalmartItemMapper;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.model.WalmartTaskOffLinkListingLog;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时器功能类：筛选特定账号的SKU数据并记录到下架日志表
 *
 * 功能：
 * 1. 在WalmartItem表中筛选特定账号数据
 * 2. 统计满足条件的去重SKU列表
 * 3. 按SKU分组处理数据
 * 4. 筛选数据并记录到WalmartTaskOffLinkListingLog表中
 *
 */
@Slf4j
@Component
public class WalmartNoSalesSkuFilterRetireJobHandler extends AbstractJobHandler {
    // 查询字段列表
    private static final String FIELD_COLUMNS = "id,account_number,site,item_id,gtin,seller_sku,main_sku,sku,create_date,order_num_total,lifecycle_status";

    // 指定账号列表
    private static final List<String> SPECIFIC_ACCOUNTS = Arrays.asList(
            "Atizamart", "BeaLooy", "Bluxpant", "Buydiant", "Cakkcool", "COMEET US", "Exmurtz",
            "hejieWRM", "Jeasouy", "KAWAHINA", "kesheng", "Mao YI", "PixelNest", "PJYANG",
            "QiQiMei", "SangLoom", "shile", "Shogelci", "SHUNHUIG", "Tacjmart", "TangZi",
            "Tuokend", "Turansi", "Vepteix", "WenYin", "Xibeidzi", "Xingzet", "Xinsery",
            "YALINDER", "YitaoLIY", "YTonxni", "YunSiYing", "YuZhu", "ZENSWXD", "ZHIFEITE",
            "Zixin", "Zlkjpath"
    );

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private WalmartTaskOffLinkListingLogMapper walmartTaskOffLinkListingLogMapper;
    @Resource
    private CustomWalmartItemMapper customWalmartItemMapper;

    public WalmartNoSalesSkuFilterRetireJobHandler() {
        super("WalmartNoSalesSkuFilterRetireJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {
        /**
         * 每个SKU保留的数据条数阈值
         */
        private Integer n = 5;

        /**
         * 任务类型
         */
        private Integer taskType = 2;

        /**
         * 状态 0 待下架
         */
        private Integer status = 0;

        /**
         * 创建时间n天内
         */
        private Integer daysWithin = 180;

        /**
         * 账号列表，为空则使用默认账号列表
         */
        private List<String> accountNumberList;
    }

    @Override
    @XxlJob("WalmartNoSalesSkuFilterRetireJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("开始执行");

        // 解析参数
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        // 获取参数
        Integer n = innerParam.getN();
        Integer taskType = innerParam.getTaskType();
        Integer status = innerParam.getStatus();
        if (null == status) {
            status = 0;
        }
        Integer daysWithin = innerParam.getDaysWithin();

        // 计算日期
        Date dateWithin = DateUtils.addDays(new Date(), -daysWithin);
        XxlJobLogger.log("开始执行，日期为：" +  DateUtils.format(dateWithin, "yyyy-MM-dd HH:mm:ss"));
        Timestamp statisticsDate = new Timestamp(System.currentTimeMillis());

        // 获取账号列表
        List<String> accountNumberList = innerParam.getAccountNumberList();
        if (CollectionUtils.isEmpty(accountNumberList)) {
            accountNumberList = SPECIFIC_ACCOUNTS;
        }
        XxlJobLogger.log("处理账号列表: {}", JSON.toJSONString(accountNumberList));

        // 1. 获取满足条件的去重SKU列表
        List<String> skuList = getDistinctSkuList(accountNumberList, dateWithin);

        String extra = String.format("SKU超量链接筛选大于%s条，系统自动记录" , n);

        List<List<String>> batchSkuLists = Lists.partition(skuList, 100);
        XxlJobLogger.log("获取到满足条件的去重SKU列表，共 {} 个，分 {} 批次", skuList.size(),batchSkuLists.size());
        for (int i = 0; i < batchSkuLists.size(); i++) {
            List<String> batchSkuList = batchSkuLists.get(i);


            // 3. 查询SKU对应的数据
            List<WalmartItem> items = getItemsBySku(accountNumberList, batchSkuList, dateWithin);

            XxlJobLogger.log("处理第 {} 批SKU，listing共 {} 个", i + 1, items.size());
            // 4. 按SKU分组处理数据
            Map<String, List<WalmartItem>> skuItemsMap = items.stream()
                    .collect(Collectors.groupingBy(WalmartItem::getSku));
            // 5. 筛选数据并记录到WalmartTaskOffLinkListingLog表
            for (Map.Entry<String, List<WalmartItem>> entry : skuItemsMap.entrySet()) {
                // 2. 分批处理SKU列表

                List<WalmartTaskOffLinkListingLog> logList = new ArrayList<>();
                String sku = entry.getKey();
                List<WalmartItem> skuItems = entry.getValue();
                // 筛选数据
                List<WalmartItem> filteredItems = filterItems(skuItems, n,dateWithin);
                // 构建日志记录
                for (WalmartItem item : filteredItems) {
                    WalmartTaskOffLinkListingLog log = new WalmartTaskOffLinkListingLog();
                    log.setAccountNumber(item.getAccountNumber());
                    log.setSite(item.getSite());
                    log.setItemId(item.getItemId());
                    log.setGtin(item.getGtin());
                    log.setSellerSku(item.getSellerSku());
                    log.setMainSku(item.getMainSku());
                    log.setSku(item.getSku());
                    log.setStatus(status);
                    log.setSalesTotalCount(item.getOrderNumTotal() != null ? item.getOrderNumTotal() : 0);
                    log.setOpenTime(item.getCreateDate());
                    log.setStatisticsDate(statisticsDate);
                    log.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                    log.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
                    log.setTaskType(taskType);
                    log.setExtraData(extra);
                    logList.add(log);
                }
                //totalProcessed = logList.size();
                if (CollectionUtils.isNotEmpty(logList)) {
                    walmartTaskOffLinkListingLogMapper.batchInsert(logList);
                }
               // XxlJobLogger.log(sku + "总共处理 {} 条数据，记录 {} 条数据", totalRecorded, totalProcessed);
            }
        }

        XxlJobLogger.log("结束执行");
        return ReturnT.SUCCESS;
    }

    /**
     * 获取满足条件的去重SKU列表
     *
     * @param accountNumberList 账号列表
     * @param dateWithin 日期范围
     * @return 去重SKU列表
     */
    private List<String> getDistinctSkuList(List<String> accountNumberList, Date dateWithin) {
        // 直接使用自定义Mapper方法执行GROUP BY查询
        return customWalmartItemMapper.selectDistinctSkuList(
                accountNumberList,
                dateWithin,
                ItemLifecycleStatusEnum.ACTIVE.getCode()
        );
    }

    /**
     * 根据SKU列表查询数据
     *
     * @param accountNumberList 账号列表
     * @param skuList SKU列表
     * @param dateWithin 日期范围
     * @return 数据列表
     */
    private List<WalmartItem> getItemsBySku(List<String> accountNumberList, List<String> skuList, Date dateWithin) {
        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria()
                .andAccountNumberIn(accountNumberList)
                .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode())
                .andSkuIn(skuList);

        // 设置查询字段
        example.setFiledColumns(FIELD_COLUMNS);
        // 查询结果
        return walmartItemService.selectFiledColumnsByExample(example);
    }

    /**
     * 筛选数据
     *
     * @param items 数据列表
     * @param n 保留条数阈值
     * @param dateWithin 日期范围
     * @return 筛选后的数据列表
     */
    private List<WalmartItem> filterItems(List<WalmartItem> items, int n, Date dateWithin) {
        // 如果没有数据，直接返回空列表
        if (CollectionUtils.isEmpty(items) || items.size() <= n) {
            return new ArrayList<>();
        }

        // a. 统计 orderNumTotal 大于 0 的记录数量 positiveCount
        long positiveCount = items.stream()
                .filter(item -> item.getOrderNumTotal() != null && item.getOrderNumTotal() > 0)
                .count();

        // b. 过滤掉 orderNumTotal 大于 0 或者 createDate 上架时间超过 180 天的数据
        List<WalmartItem> filteredItems = items.stream()
                .filter(item -> (item.getOrderNumTotal() == null || item.getOrderNumTotal() <= 0)
                        && item.getCreateDate().getTime() >= dateWithin.getTime())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredItems)) {
            return new ArrayList<>();
        }

        // 按createDate降序排序
        filteredItems.sort((a, b) -> b.getCreateDate().compareTo(a.getCreateDate()));

        // c. 计算需要保留的条数
        int keepCount = (int) (n - positiveCount);

        // d. 如果 keepCount 小于等于 0，则返回步骤 b 过滤完剩下的所有数据
        if (keepCount <= 0) {
            return filteredItems;
        }

        // e. 如果 keepCount 大于等于 filteredItems.size()，则返回空列表
        if (keepCount >= filteredItems.size()) {
            return new ArrayList<>();
        }

        // f. 否则（即 positiveCount < n），则排除前 keepCount 条记录后剩余的数据列表
        return filteredItems.subList(keepCount, filteredItems.size());
    }
}