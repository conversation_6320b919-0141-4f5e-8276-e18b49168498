package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartNewCategoryAttributeMapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartNewCategoryAttribute;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartNewCategoryAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【walmart_new_category_attribute(沃尔玛新分类属性)】的数据库操作Service实现
* @createDate 2025-06-17 16:21:09
*/
@Slf4j
@Service
public class WalmartNewCategoryAttributeServiceImpl extends ServiceImpl<WalmartNewCategoryAttributeMapper, WalmartNewCategoryAttribute>
    implements WalmartNewCategoryAttributeService{
    @Override
    public void refreshNewCategoryAttribute(JSONObject jsonObject) {
        try {
            // 验证输入 JSON
            if (jsonObject == null || !jsonObject.containsKey("schema")) {
                throw new IllegalArgumentException("JSON 输入无效或缺少 schema");
            }

            // 提取嵌套 JSON 对象并进行空值检查
            JSONObject schema = jsonObject.getJSONObject("schema");
            if (schema == null) {
                throw new IllegalArgumentException("Schema 对象为空");
            }

            JSONObject mpItemFeedProperties = schema.getJSONObject("properties");
            if (mpItemFeedProperties == null) {
                throw new IllegalArgumentException("Properties 对象为空");
            }

            JSONObject mpItem = mpItemFeedProperties.getJSONObject("MPItem");
            if (mpItem == null) {
                throw new IllegalArgumentException("MPItem 对象为空");
            }

            JSONObject items = mpItem.getJSONObject("items");
            if (items == null) {
                throw new IllegalArgumentException("Items 对象为空");
            }

            JSONObject itemsProperties = items.getJSONObject("properties");
            if (itemsProperties == null) {
                throw new IllegalArgumentException("Items 属性对象为空");
            }

            JSONObject visible = itemsProperties.getJSONObject("Visible");
            if (visible == null) {
                throw new IllegalArgumentException("Visible 对象为空");
            }

            String visiblePropertiesStr = visible.getString("properties");
            if (visiblePropertiesStr == null) {
                throw new IllegalArgumentException("Visible 属性字符串为空");
            }

            // 解析 visible 属性为 map
            HashMap<String, JSONObject> visiblePropertiesMap = JSON.parseObject(visiblePropertiesStr, HashMap.class);
            if (visiblePropertiesMap == null) {
                throw new IllegalArgumentException("无法解析 visible 属性");
            }
            List<WalmartNewCategoryAttribute> toUpdate = new ArrayList<>();
            List<WalmartNewCategoryAttribute> toInsert = new ArrayList<>();
            //遍历循环visiblePropertiesMap
            for (Map.Entry<String, JSONObject> entry : visiblePropertiesMap.entrySet()) {
                String key = entry.getKey();
                JSONObject value = entry.getValue();
                if (key == null || value == null) {
                    continue;
                }

                JSONArray requiredAttributeName = value.getJSONArray("required");
                String propertiesStr = value.getString("properties");
                if (propertiesStr == null) {
                    continue;
                }

                // 解析属性为 map
                HashMap<String, JSONObject> propertiesMap = JSON.parseObject(propertiesStr, HashMap.class);
                if (propertiesMap == null) {
                    continue;
                }

                // 过滤非必填属性
                List<String> nonRequiredAttributes = new ArrayList<>();
                for (String propKey : propertiesMap.keySet()) {
                    if (requiredAttributeName == null || !requiredAttributeName.contains(propKey)) {
                        nonRequiredAttributes.add(propKey);
                    }
                }

                // 获取现有记录
                WalmartNewCategoryAttribute existingAttribute = getCategoryAttributeByName(key);
                WalmartNewCategoryAttribute attribute = new WalmartNewCategoryAttribute();
                attribute.setSubCategoryName(key);
                attribute.setAttribute(value.toJSONString());
                attribute.setRequiredAttributeName(requiredAttributeName != null ? requiredAttributeName.toJSONString() : "[]");
                attribute.setAdditionalAttributeName(JSON.toJSONString(nonRequiredAttributes));
                attribute.setUpdateDate(new Date());

                if (ObjectUtils.isNotEmpty(existingAttribute)) {
                    attribute.setId(existingAttribute.getId());
                    toUpdate.add(attribute);
                } else {
                    attribute.setCreateDate(new Date());
                    toInsert.add(attribute);
                }
            }
            if (!toUpdate.isEmpty()) {
                this.updateBatchById(toUpdate);
            }
            if (!toInsert.isEmpty()) {
                this.saveBatch(toInsert);
            }
        } catch (Exception e) {
            log.error("处理类别属性时出错: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private WalmartNewCategoryAttribute getCategoryAttributeByName(String key) {
        LambdaQueryWrapper<WalmartNewCategoryAttribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartNewCategoryAttribute::getSubCategoryName, key);
        return this.getOne(queryWrapper);
    }
}




