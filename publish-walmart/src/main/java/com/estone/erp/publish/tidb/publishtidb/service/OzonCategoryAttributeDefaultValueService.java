package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.ozon.model.OzonCategory;
import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryAttributeDefaultValue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueExportDTO;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueQueryDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
public interface OzonCategoryAttributeDefaultValueService extends IService<OzonCategoryAttributeDefaultValue> {

    /**
     * 同步属性
     */
    void syncAttribute();

    /**
     * 同步指定分类的属性
     * @param category 分类
     */
    void syncAttribute(OzonCategory category);

    String getCategoryMultName(OzonCategory category);

    String getCategoryFullMultName(OzonCategory category);

    /**
     * 查询属性配置
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    Page<OzonCategoryAttributeDefaultValue> queryAttributeConfig(OzonCategoryAttributeDefaultValueQueryDTO queryDTO);

    /**
     * 导出属性配置
     * @param exportDTO 导出参数
     * @return 导出结果
     */
    ApiResult<String> exportAttributeConfig(OzonCategoryAttributeDefaultValueExportDTO exportDTO);

    void batchUpdateImport(List<OzonCategoryAttributeDefaultValue> updateList);

    ApiResult<String> getSyncTime();

    /**
     * 导出分类表的属性
     * @return 队列导出
     */
    ApiResult<String> exportCategoryAttribute();

}
