package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartGenerateGtinMainMapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartGenerateGtinDetail;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartGenerateGtinMain;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartGenerateGtinImportDto;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartGenerateGtinPageDto;
import com.estone.erp.publish.tidb.publishtidb.neums.WalmartGenerateGtinStatusEnum;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartGenerateGtinDetailService;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartGenerateGtinMainService;
import com.estone.erp.publish.walmart.enums.CardCodeTypeEnum;
import com.estone.erp.publish.walmart.model.dto.AccountEanRequest;
import com.estone.erp.publish.walmart.util.CardCodeUtils;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【walmart_generate_gtin_main】的数据库操作Service实现
 * @createDate 2024-12-16 15:52:28
 */
@Slf4j
@Service
public class WalmartGenerateGtinMainServiceImpl extends ServiceImpl<WalmartGenerateGtinMainMapper, WalmartGenerateGtinMain>
        implements WalmartGenerateGtinMainService {

    @Resource
    private PermissionsHelper permissionsHelper;


    @Resource
    private WalmartGenerateGtinDetailService walmartGenerateGtinDetailService;

    @Resource
    private WalmartGenerateGtinMainService walmartGenerateGtinMainService;

    @Override
    public void export(HttpServletResponse response, Integer id) {
        String fileName = "SKU—GTIN" + System.currentTimeMillis() + ".xlsx";
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Transfer-Encoding", "binary");
        response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
        response.setHeader("Pragma", "public");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(outputStream, WalmartGenerateGtinDetail.class)
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet("gtin").build();
            List<WalmartGenerateGtinDetail> gtinList = walmartGenerateGtinDetailService.list(new LambdaQueryWrapper<WalmartGenerateGtinDetail>()
                    .eq(WalmartGenerateGtinDetail::getMainId, id));
            excelWriter.write(gtinList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("生成GTIN导出失败信息：", e);
            throw new BusinessException("导出失败");
        }
        WalmartGenerateGtinMain walmartGenerateGtinMain = new WalmartGenerateGtinMain();
        walmartGenerateGtinMain.setExportTime(new Date());
        walmartGenerateGtinMain.setId(id);
        walmartGenerateGtinMainService.updateById(walmartGenerateGtinMain);

    }

    @Override
    public void importExcel(MultipartFile file, String fileName) {
        List<WalmartGenerateGtinImportDto> list = this.readExcel(file);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("导入失败，没有数据无法生成GTIN");
        }

        String userName = WebUtils.getUserName();
        CompletableFuture.runAsync(() -> {
            this.generateGtin(list, fileName,userName);
        });

    }

    private void generateGtin(List<WalmartGenerateGtinImportDto> list, String fileName, String userName) {
        // 按店铺分组
        Map<String, List<WalmartGenerateGtinImportDto>> accountMap = list.stream()
                .collect(Collectors.groupingBy(WalmartGenerateGtinImportDto::getAccount));

        // 初始化主表数据并插入数据库
        Map<String, WalmartGenerateGtinMain> accountMainMap = accountMap.keySet().stream()
                .collect(Collectors.toMap(account -> account, account -> {
                    WalmartGenerateGtinMain main = createWalmartGtinMain(account, fileName,userName);
                    baseMapper.insert(main);
                    return main;
                }));

        // 批量处理 GTIN 生成
        accountMap.forEach((account, accountList) -> {
            if (CollectionUtils.isEmpty(accountList)) {
                return;
            }
            WalmartGenerateGtinMain main = accountMainMap.get(account);
            processAccountGtin(account, accountList, main);
        });
    }

    /**
     * 创建 Walmart GTIN 主表记录
     */
    private WalmartGenerateGtinMain createWalmartGtinMain(String account, String fileName, String userName) {
        WalmartGenerateGtinMain main = new WalmartGenerateGtinMain();
        main.setAccount(account);
        main.setInputTime(new Date());
        main.setInputBy(userName);
        main.setInputExcelName(fileName);
        main.setStatus(WalmartGenerateGtinStatusEnum.generating.getCode());
        return main;
    }

    /**
     * 处理单个账号的 GTIN 生成逻辑
     */
    private void processAccountGtin(String account, List<WalmartGenerateGtinImportDto> accountList, WalmartGenerateGtinMain main) {
        List<WalmartGenerateGtinDetail> detailList = new ArrayList<>();
        Set<Integer> statusSet = new HashSet<>();

        accountList.forEach(dto -> {
            WalmartGenerateGtinDetail detail = generateGtinDetail(account, dto, main.getId());
            detailList.add(detail);
            statusSet.add(detail.getStatus());
        });

        // 批量保存明细
        walmartGenerateGtinDetailService.saveBatch(detailList);

        // 更新主表状态
        updateMainStatus(main, statusSet);
    }

    /**
     * 生成单个 GTIN 明细记录
     */
    private WalmartGenerateGtinDetail generateGtinDetail(String account, WalmartGenerateGtinImportDto dto, Integer mainId) {
        WalmartGenerateGtinDetail detail = new WalmartGenerateGtinDetail();
        detail.setAccount(account);
        detail.setMainId(mainId);
        detail.setSku(dto.getSku());

        try {
            if(StringUtils.isBlank(dto.getSku())){
                throw new BusinessException("SKU为空");
            }
            if(StringUtils.isBlank(account)){
                throw new BusinessException("店铺为空");
            }
            // 构建请求并生成 GTIN
            AccountEanRequest request = buildAccountEanRequest(account, dto.getSku());
            List<String> idList = CardCodeUtils.generateCardCodes(request);

            if (CollectionUtils.isEmpty(idList)) {
                detail.setRemak("生成GTIN失败");
                detail.setStatus(WalmartGenerateGtinStatusEnum.FAIL.getCode());
            } else {
                detail.setStatus(WalmartGenerateGtinStatusEnum.SUCCESS.getCode());
                String gtin = idList.get(0);
                detail.setGtinValue(idList.get(0));
                if (StringUtils.isBlank(gtin) ){
                    throw new BusinessException("生成GTIN失败");
                }
                if( gtin.length() < 14){
                    throw new BusinessException("生成GTIN失败，获取不到SKUID");
                }
            }
        } catch (Exception e) {
            log.error("生成GTIN失败", e);
            detail.setStatus(WalmartGenerateGtinStatusEnum.FAIL.getCode());
            detail.setRemak(e.getMessage());
            return detail;
        }
        return detail;
    }

    /**
     * 构建 AccountEanRequest 请求对象
     */
    private AccountEanRequest buildAccountEanRequest(String account, String sku) {
        AccountEanRequest request = new AccountEanRequest();
        request.setSellerId(account);
        request.setSkuList(List.of(sku));
        request.setCardCodeType(CardCodeTypeEnum.GTIN.getCode());
        request.setSize(1);
        request.setSkuDataSource(1);

        SaleAccountAndBusinessResponse response = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, account);
        String eanPrefix = WalmartAccountUtils.getEanPrefix(response, request.getCardCodeType());
        request.setEanPrefix(eanPrefix);

        return request;
    }

    /**
     * 更新主表状态
     */
    private void updateMainStatus(WalmartGenerateGtinMain main, Set<Integer> statusSet) {
        if (statusSet.size() > 1) {
            main.setStatus(WalmartGenerateGtinStatusEnum.PART_SUCCESS.getCode());
        } else {
            main.setStatus(statusSet.iterator().next());
        }
        main.setCompleteTime(new Date());
        baseMapper.updateById(main);
    }

    private List<WalmartGenerateGtinImportDto> readExcel(MultipartFile file) {
        List<WalmartGenerateGtinImportDto> list = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;

                Cell accountCell = row.getCell(0);
                Cell skullCell = row.getCell(1);

                String account = "";
                if (accountCell != null) {
                    if (accountCell.getCellType() == CellType.STRING) {
                        account = accountCell.getStringCellValue().trim();
                    } else if (accountCell.getCellType() == CellType.NUMERIC) {
                        account = String.valueOf(accountCell.getNumericCellValue()).trim();
                    }
                }

                String sku = "";
                if (skullCell != null) {
                    if (skullCell.getCellType() == CellType.STRING) {
                        sku = skullCell.getStringCellValue().trim();
                    } else if (skullCell.getCellType() == CellType.NUMERIC) {
                        sku = String.valueOf(skullCell.getNumericCellValue()).trim();
                    }
                }
                WalmartGenerateGtinImportDto walmartGenerateGtinImportDto = new WalmartGenerateGtinImportDto(account, sku);
                list.add(walmartGenerateGtinImportDto);
            }
        } catch (IOException e) {
            throw new BusinessException("导入失败，读取excel数据出错： " + e.getMessage());
        }

        return list;
    }

    @Override
    public IPage<WalmartGenerateGtinMain> pageQuery(WalmartGenerateGtinPageDto dto) {
        LambdaQueryWrapper<WalmartGenerateGtinMain> queryWrapper = generateQueryWrapper(dto);
        Page<WalmartGenerateGtinMain> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<WalmartGenerateGtinMain> result = this.page(page, queryWrapper);
        return result;
    }

    private LambdaQueryWrapper<WalmartGenerateGtinMain> generateQueryWrapper(WalmartGenerateGtinPageDto dto) {
        LambdaQueryWrapper<WalmartGenerateGtinMain> queryWrapper = new LambdaQueryWrapper<>();

        //权限控制
        List<String> accountList = permissionsHelper.getCurrentUserPermission(dto.getAccount(), null, null, null, SaleChannel.CHANNEL_WALMART);
        if (CollectionUtils.isNotEmpty(accountList)) {
            queryWrapper.in(WalmartGenerateGtinMain::getAccount, accountList);
        }

        if (StringUtils.isNotEmpty(dto.getInputTimeFrom()) && StringUtils.isNotEmpty(dto.getInputTimeTo())) {
            queryWrapper.between(WalmartGenerateGtinMain::getInputTime, dto.getInputTimeFrom(), dto.getInputTimeTo());
        }

        if (StringUtils.isNotEmpty(dto.getCompleteTimeFrom()) && StringUtils.isNotEmpty(dto.getCompleteTimeTo())) {
            queryWrapper.between(WalmartGenerateGtinMain::getCompleteTime, dto.getCompleteTimeFrom(), dto.getCompleteTimeTo());
        }

        if (StringUtils.isNotEmpty(dto.getExportTimeFrom()) && StringUtils.isNotEmpty(dto.getExportTimeTo())) {
            queryWrapper.between(WalmartGenerateGtinMain::getExportTime, dto.getExportTimeFrom(), dto.getExportTimeTo());
        }
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            queryWrapper.in(WalmartGenerateGtinMain::getStatus, dto.getStatus());
        }
        if (CollectionUtils.isNotEmpty(dto.getInputBy())){
            queryWrapper.in(WalmartGenerateGtinMain::getInputBy, dto.getInputBy());
        }
        queryWrapper.orderByDesc(WalmartGenerateGtinMain::getInputTime);
        return queryWrapper;
    }
}




