package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * walmart定时刊登队列
 * @TableName walmart_time_publish_queue
 */
@TableName(value ="walmart_time_publish_queue")
@Data
public class WalmartTimePublishQueue {
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板ID
     */
    @TableField(value = "template_id")
    private Integer templateId;

    /**
     * 店铺
     */
    @TableField(value = "account_number")
    private String accountNumber;

    /**
     * 数据来源
     */
    @TableField(value = "sku_data_source")
    private Integer skuDataSource;

    /**
     * 货号
     */
    @TableField(value = "article_number")
    private String articleNumber;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 状态 1 等待中、2 暂停中、3 处理中、4 结束
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 刊登状态 对应模板刊登状态
     */
    @TableField(value = "publish_status")
    private Integer publishStatus;

    /**
     * 扩展数据
     */
    @TableField(value = "extra")
    private String extra;

    /**
     * 来源，0-自动刊登、1-普通刊登
     */
    @TableField(value = "publish_source")
    private Integer publisherSource;

    /**
     * 刊登角色，0-系统刊登、1-销售刊登
     */
    @TableField(value = "publish_role")
    private Integer publishRole;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 刊登时间
     */
    @TableField(value = "publish_time")
    private Date publishTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}