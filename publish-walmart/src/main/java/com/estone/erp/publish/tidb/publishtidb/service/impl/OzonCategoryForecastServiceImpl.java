package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryForecast;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonCategoryForecastMapper;
import com.estone.erp.publish.tidb.publishtidb.service.OzonCategoryForecastService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 类目预测 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class OzonCategoryForecastServiceImpl extends ServiceImpl<OzonCategoryForecastMapper, OzonCategoryForecast> implements OzonCategoryForecastService {

}
