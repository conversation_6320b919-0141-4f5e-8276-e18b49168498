package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsYandexItemService;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.yandex.common.YandexEsItemBulkProcessor;
import com.estone.erp.publish.yandex.handler.YandexSyncProductInfoHandler;
import com.estone.erp.publish.yandex.model.dto.sync.YandexProductInfoDO;
import com.estone.erp.publish.yandex.mq.YandexMqConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ConditionalOnProperty(
        name = {"mq-config.yandexSyncProductQueueEnable"},
        havingValue = "true"
)
@Slf4j
@Component
public class YandexSyncProductInfoChangeMQListener {
    @Resource
    private ChangeSkuLogService changeSkuLogService;
    @Autowired
    private EsYandexItemService esYandexItemService;
    @Autowired
    private YandexSyncProductInfoHandler syncProductInfoHandler;
    @Autowired
    private YandexEsItemBulkProcessor bulkProcessor;

    private static final int MAX_RETRY_COUNT = 3;

    @RabbitListener(queues = YandexMqConfig.YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE, containerFactory = "batchAntifollowConsumeFactory")
    public void syncYandexProductInfo(Message message, Channel channel) throws IOException {
        StopWatch stopWatch = StopWatch.createStarted();
        boolean consumerFlag = false;
        Exception error = null;
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }

            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            consumerFlag = isSuccess;
            if (isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_YANDEX);
            }
        } catch (Exception e) {
            error = e;
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_YANDEX);
        } finally {
            log.info("同步产品信息变更完成，消费状态：{}，耗时：{}, error: {}", consumerFlag, stopWatch, error == null ? "" : error.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        List<String> accountNumberList = changeSku.getAccountNumberList();
        syncProductInfo(skuList, accountNumberList);

        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }

    private void syncProductInfo(List<String> skuList, List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        EsYandexItemRequest request = new EsYandexItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "sellerSku", "spu", "sku", "skuStatus", "stock", "weight"});
        request.setSkus(skuList);
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            request.setAccountNumbers(accountNumberList);
        }
        Map<String, ProductInfoVO> map = new HashMap<>();
        int pageNum = 0;
        int pageSize = 100;
        while (true) {
            request.setPageIndex(pageNum);
            request.setPageSize(pageSize);
            PageInfo<EsYandexItem> pageInfo = esYandexItemService.page(request);
            List<EsYandexItem> contents = pageInfo.getContents();
            if (CollectionUtils.isEmpty(contents)) {
                return;
            }
            for (EsYandexItem item : contents) {
                YandexProductInfoDO productInfoDO = new YandexProductInfoDO();
                productInfoDO.setSku(item.getSku());
                productInfoDO.setId(item.getId());
                ProductInfoVO productInfoVO = map.get(item.getSku());
                if (ObjectUtils.isEmpty(productInfoVO)) {
                    productInfoVO = ProductUtils.getSkuInfo(item.getSku());
                    map.put(item.getSku(), productInfoVO);
                }
                if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                    return;
                }
                syncProductInfoHandler.setProductInfo(productInfoDO, productInfoVO, item.getWeight());
                productInfoDO.setUpdateSyncProductInfoDate(new Date());
                bulkProcessor.syncHandler(productInfoDO);
            }
            pageNum++;
        }


    }
}
