package com.estone.erp.publish.walmart.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/3/23
 */
@Slf4j
@RestController
@RequestMapping("walmartImageDownload")
public class WalmartImageDownloadController {

    @Resource
    private FeedTaskService feedTaskService;

    private Set<String> splitParams(String param, String split) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isNotEmpty(param)) {
            if (param.contains(split)) {
                List<String> list = Arrays.asList(param.split(split));
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(i->{
                        set.add(i.trim());
                    });
                }
            }
            else {
                set.add(param.trim());
            }
        }
        return set;
    }

    @ResponseBody
    @PostMapping("/search")
    public ApiResult<?> search(@RequestParam("articleNumbers") String articleNumberStr) {

        Set<String> articleNumbers = splitParams(articleNumberStr, ",");
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(new ArrayList<>(articleNumbers));
        if (CollectionUtils.isEmpty(productInfoList)){
            return ApiResult.newSuccess();
        }

        //取所有主sku信息
        List<ProductInfo> allList = new ArrayList<>();
        List<ProductInfo> productInfoListNoFirstImage = productInfoList.stream().filter(productInfo -> StringUtils.isEmpty(productInfo.getFirstImage())).collect(
                Collectors.collectingAndThen(Collectors.toCollection(()
                        -> new TreeSet<>(Comparator.comparing(o -> o.getMainSku()))),ArrayList::new));
        if (CollectionUtils.isNotEmpty(productInfoListNoFirstImage)){
            allList.addAll(productInfoListNoFirstImage);
        }
        List<ProductInfo> productInfoListFirstImage =
                productInfoList.stream().filter(productInfo -> StringUtils.isNotEmpty(productInfo.getFirstImage()))
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(()
                                -> new TreeSet<>(Comparator.comparing(o -> o.getMainSku()))),ArrayList::new));

        if (CollectionUtils.isNotEmpty(productInfoListFirstImage)){
            allList.addAll(productInfoListFirstImage);
        }
        return ApiResult.newSuccess(allList);
    }

    @PostMapping(value = "download")
    @ResponseBody
    public ApiResult<?> download(@RequestParam("articleNumbers") String articleNumberStr, @RequestParam("accountNumber") String accountNumber, @RequestParam(value = "pictureType", required = false) String pictureType) {
        String userName = WebUtils.getUserName();
        List<String> articleNumbers = new ArrayList<>();
        if (articleNumberStr.contains(",")) {
            articleNumbers = Arrays.asList(articleNumberStr.split(","));
        } else {
            articleNumbers.add(articleNumberStr.trim());
        }

        if (CollectionUtils.isEmpty(articleNumbers)
                || articleNumbers.size() > 100) {
            return ApiResult.newError("请检查货号数量");
        }

        if (isFinishDownloadTask(userName,true)) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAccountNumber(accountNumber);
            feedTask.setPlatform(Platform.Walmart.name());
            feedTask.setArticleNumber(StringUtils.join(articleNumbers,","));
            feedTask.setTaskType(WalmartTaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
            feedTask.setTaskStatus(TaskStatusEnum.WAITING.getStatusCode());
            // 像素 1600 1024
            feedTask.setAttribute6(pictureType);
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setCreatedBy(userName);
            feedTask.setTableIndex();
            feedTaskService.insertSelective(feedTask);
            return ApiResult.newSuccess("提交下载任务成功，请查看下载图片日志");
        }
        return ApiResult.newError("已存在5条排队中或者下载中的数据，请等待下载完成后再提交");
    }


    @ResponseBody
    @RequestMapping(value = "repeatDownload/{id}", method = { RequestMethod.GET })
    public ApiResult<?> repeatDownloading(@PathVariable(value = "id", required = true) @ApiParam(name = "id", value = "id", required = true) Long id,
                                          HttpServletRequest request) {
        String userName = WebUtils.getUserName();
        if (StringUtils.isNotEmpty(userName)) {
            FeedTaskExample example = new FeedTaskExample();
            FeedTaskExample.Criteria criteria = example.createCriteria();
            criteria.andTaskTypeEqualTo(WalmartTaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
            criteria.andResultStatusEqualTo(ResultStatusEnum.RESULT_FAIL.getStatusCode());
            criteria.andIdEqualTo(id);
            List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Walmart.name());
            if (isFinishDownloadTask(userName, true)) {
                if (CollectionUtils.isNotEmpty(feedTasks)) {
                    FeedTask feedTask = new FeedTask();
                    feedTask.setAccountNumber(feedTasks.get(0).getAccountNumber());
                    feedTask.setPlatform(Platform.Walmart.name());
                    feedTask.setArticleNumber(feedTasks.get(0).getArticleNumber());
                    feedTask.setTaskType(WalmartTaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
                    feedTask.setTaskStatus(TaskStatusEnum.WAITING.getStatusCode());
                    if (null != feedTasks.get(0).getAttribute3() && StringUtils.isNotEmpty(feedTasks.get(0).getAttribute3())) {
                        feedTask.setAttribute3(feedTasks.get(0).getAttribute3());
                    }
                    // 像素 1600 1024
                    feedTask.setAttribute6(feedTasks.get(0).getAttribute6());
                    feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setCreatedBy(userName);
                    feedTask.setTableIndex();
                    feedTaskService.insertSelective(feedTask);
                    return ApiResult.newSuccess("提交下载任务成功，请查看下载图片日志");
                }
                return ApiResult.newError("该任务不能重新下载");
            }
            return ApiResult.newError("已存在5条排队中或者下载中的数据，请等待下载完成后再提交");
        }
        return ApiResult.newError("用户信息为空");
    }

    /**
     *上一次下载任务是否已完成
     * @param userName
     * @return
     */
    public Boolean isFinishDownloadTask(String userName,Boolean downloadNum){
        if (StringUtils.isEmpty(userName)){
            return true;
        }
        FeedTaskExample example = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = example.createCriteria();
        criteria.andTaskTypeEqualTo(WalmartTaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
        criteria.andCreatedByEqualTo(userName);
        //下载任务排队中,或者正在下载中
        List<Integer> statsuList = new ArrayList<>();
        statsuList.add(TaskStatusEnum.WAITING.getStatusCode());
        statsuList.add(TaskStatusEnum.EXECUTING.getStatusCode());
        if (BooleanUtils.isTrue(downloadNum)) {
            // 20个
            criteria.andAttribute3IsNull();
        }else {
            //表格刊登 500个
            criteria.andAttribute3IsNotNull();
        }
        criteria.andTaskStatusIn(statsuList);
        List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Walmart.name());
        if (CollectionUtils.isEmpty(feedTasks) || feedTasks.size()<5){
            return true;
        }else {
            return false;
        }
    }
}
