package com.estone.erp.publish.tidb.publishtidb.service;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartNewCategoryAttribute;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【walmart_new_category_attribute(沃尔玛新分类属性)】的数据库操作Service
* @createDate 2025-06-17 16:21:09
*/
public interface WalmartNewCategoryAttributeService extends IService<WalmartNewCategoryAttribute> {
    /**
     * 解析新分类数据
     */
    void refreshNewCategoryAttribute(JSONObject jsonObject);
}
