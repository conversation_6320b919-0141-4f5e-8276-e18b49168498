package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartAdminTemplateLogMapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【walmart_admin_template_log(walmart范本日志表)】的数据库操作Service实现
* @createDate 2025-06-27 14:44:32
*/
@Service
public class WalmartAdminTemplateLogServiceImpl extends ServiceImpl<WalmartAdminTemplateLogMapper, WalmartAdminTemplateLog>
    implements WalmartAdminTemplateLogService {

    @Override
    public List<WalmartAdminTemplateLog> getLogsByTemplateId(Integer templateId) {
        Assert.notNull(templateId, "模板ID不能为空");

        // 构建查询条件
        LambdaQueryWrapper<WalmartAdminTemplateLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WalmartAdminTemplateLog::getTemplateId, templateId);
        queryWrapper.orderByDesc(WalmartAdminTemplateLog::getCreatedTime);

        // 查询日志记录
        return this.list(queryWrapper);
    }
}
