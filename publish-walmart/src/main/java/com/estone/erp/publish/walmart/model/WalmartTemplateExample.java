package com.estone.erp.publish.walmart.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class WalmartTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * 自定义查询字段
     */
    private String filedColumns;

    /**
     * 查询哪张表
     */
    private String table;

    public WalmartTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public String getFiledColumns() {
        return filedColumns;
    }

    public void setFiledColumns(String filedColumns) {
        this.filedColumns = filedColumns;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNull() {
            addCriterion("sale_variant is null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNotNull() {
            addCriterion("sale_variant is not null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantEqualTo(Boolean value) {
            addCriterion("sale_variant =", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotEqualTo(Boolean value) {
            addCriterion("sale_variant <>", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThan(Boolean value) {
            addCriterion("sale_variant >", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant >=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThan(Boolean value) {
            addCriterion("sale_variant <", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant <=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIn(List<Boolean> values) {
            addCriterion("sale_variant in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotIn(List<Boolean> values) {
            addCriterion("sale_variant not in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant not between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(String value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(String value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(String value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(String value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLike(String value) {
            addCriterion("sub_category_id like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotLike(String value) {
            addCriterion("sub_category_id not like", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<String> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<String> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(String value1, String value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(String value1, String value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIsNull() {
            addCriterion("category_attribute is null");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIsNotNull() {
            addCriterion("category_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeEqualTo(String value) {
            addCriterion("category_attribute =", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotEqualTo(String value) {
            addCriterion("category_attribute <>", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeGreaterThan(String value) {
            addCriterion("category_attribute >", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("category_attribute >=", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLessThan(String value) {
            addCriterion("category_attribute <", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLessThanOrEqualTo(String value) {
            addCriterion("category_attribute <=", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLike(String value) {
            addCriterion("category_attribute like", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotLike(String value) {
            addCriterion("category_attribute not like", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIn(List<String> values) {
            addCriterion("category_attribute in", values, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotIn(List<String> values) {
            addCriterion("category_attribute not in", values, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeBetween(String value1, String value2) {
            addCriterion("category_attribute between", value1, value2, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotBetween(String value1, String value2) {
            addCriterion("category_attribute not between", value1, value2, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeIsNull() {
            addCriterion("product_id_type is null");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeIsNotNull() {
            addCriterion("product_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeEqualTo(String value) {
            addCriterion("product_id_type =", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeNotEqualTo(String value) {
            addCriterion("product_id_type <>", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeGreaterThan(String value) {
            addCriterion("product_id_type >", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_id_type >=", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeLessThan(String value) {
            addCriterion("product_id_type <", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeLessThanOrEqualTo(String value) {
            addCriterion("product_id_type <=", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeLike(String value) {
            addCriterion("product_id_type like", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeNotLike(String value) {
            addCriterion("product_id_type not like", value, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeIn(List<String> values) {
            addCriterion("product_id_type in", values, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeNotIn(List<String> values) {
            addCriterion("product_id_type not in", values, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeBetween(String value1, String value2) {
            addCriterion("product_id_type between", value1, value2, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdTypeNotBetween(String value1, String value2) {
            addCriterion("product_id_type not between", value1, value2, "productIdType");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesIsNull() {
            addCriterion("key_features is null");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesIsNotNull() {
            addCriterion("key_features is not null");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesEqualTo(String value) {
            addCriterion("key_features =", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesNotEqualTo(String value) {
            addCriterion("key_features <>", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesGreaterThan(String value) {
            addCriterion("key_features >", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesGreaterThanOrEqualTo(String value) {
            addCriterion("key_features >=", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesLessThan(String value) {
            addCriterion("key_features <", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesLessThanOrEqualTo(String value) {
            addCriterion("key_features <=", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesLike(String value) {
            addCriterion("key_features like", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesNotLike(String value) {
            addCriterion("key_features not like", value, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesIn(List<String> values) {
            addCriterion("key_features in", values, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesNotIn(List<String> values) {
            addCriterion("key_features not in", values, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesBetween(String value1, String value2) {
            addCriterion("key_features between", value1, value2, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andKeyFeaturesNotBetween(String value1, String value2) {
            addCriterion("key_features not between", value1, value2, "keyFeatures");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlIsNull() {
            addCriterion("main_image_url is null");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlIsNotNull() {
            addCriterion("main_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlEqualTo(String value) {
            addCriterion("main_image_url =", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlNotEqualTo(String value) {
            addCriterion("main_image_url <>", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlGreaterThan(String value) {
            addCriterion("main_image_url >", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("main_image_url >=", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlLessThan(String value) {
            addCriterion("main_image_url <", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlLessThanOrEqualTo(String value) {
            addCriterion("main_image_url <=", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlLike(String value) {
            addCriterion("main_image_url like", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlNotLike(String value) {
            addCriterion("main_image_url not like", value, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlIn(List<String> values) {
            addCriterion("main_image_url in", values, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlNotIn(List<String> values) {
            addCriterion("main_image_url not in", values, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlBetween(String value1, String value2) {
            addCriterion("main_image_url between", value1, value2, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andMainImageUrlNotBetween(String value1, String value2) {
            addCriterion("main_image_url not between", value1, value2, "mainImageUrl");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsIsNull() {
            addCriterion("extra_image_urls is null");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsIsNotNull() {
            addCriterion("extra_image_urls is not null");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsEqualTo(String value) {
            addCriterion("extra_image_urls =", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsNotEqualTo(String value) {
            addCriterion("extra_image_urls <>", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsGreaterThan(String value) {
            addCriterion("extra_image_urls >", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsGreaterThanOrEqualTo(String value) {
            addCriterion("extra_image_urls >=", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsLessThan(String value) {
            addCriterion("extra_image_urls <", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsLessThanOrEqualTo(String value) {
            addCriterion("extra_image_urls <=", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsLike(String value) {
            addCriterion("extra_image_urls like", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsNotLike(String value) {
            addCriterion("extra_image_urls not like", value, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsIn(List<String> values) {
            addCriterion("extra_image_urls in", values, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsNotIn(List<String> values) {
            addCriterion("extra_image_urls not in", values, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsBetween(String value1, String value2) {
            addCriterion("extra_image_urls between", value1, value2, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andExtraImageUrlsNotBetween(String value1, String value2) {
            addCriterion("extra_image_urls not between", value1, value2, "extraImageUrls");
            return (Criteria) this;
        }

        public Criteria andMsrpIsNull() {
            addCriterion("msrp is null");
            return (Criteria) this;
        }

        public Criteria andMsrpIsNotNull() {
            addCriterion("msrp is not null");
            return (Criteria) this;
        }

        public Criteria andMsrpEqualTo(Double value) {
            addCriterion("msrp =", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpNotEqualTo(Double value) {
            addCriterion("msrp <>", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpGreaterThan(Double value) {
            addCriterion("msrp >", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpGreaterThanOrEqualTo(Double value) {
            addCriterion("msrp >=", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpLessThan(Double value) {
            addCriterion("msrp <", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpLessThanOrEqualTo(Double value) {
            addCriterion("msrp <=", value, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpIn(List<Double> values) {
            addCriterion("msrp in", values, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpNotIn(List<Double> values) {
            addCriterion("msrp not in", values, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpBetween(Double value1, Double value2) {
            addCriterion("msrp between", value1, value2, "msrp");
            return (Criteria) this;
        }

        public Criteria andMsrpNotBetween(Double value1, Double value2) {
            addCriterion("msrp not between", value1, value2, "msrp");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNull() {
            addCriterion("price_unit is null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNotNull() {
            addCriterion("price_unit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitEqualTo(String value) {
            addCriterion("price_unit =", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotEqualTo(String value) {
            addCriterion("price_unit <>", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThan(String value) {
            addCriterion("price_unit >", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThanOrEqualTo(String value) {
            addCriterion("price_unit >=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThan(String value) {
            addCriterion("price_unit <", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThanOrEqualTo(String value) {
            addCriterion("price_unit <=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLike(String value) {
            addCriterion("price_unit like", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotLike(String value) {
            addCriterion("price_unit not like", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIn(List<String> values) {
            addCriterion("price_unit in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotIn(List<String> values) {
            addCriterion("price_unit not in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitBetween(String value1, String value2) {
            addCriterion("price_unit between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotBetween(String value1, String value2) {
            addCriterion("price_unit not between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityIsNull() {
            addCriterion("price_per_unit_quantity is null");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityIsNotNull() {
            addCriterion("price_per_unit_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityEqualTo(Double value) {
            addCriterion("price_per_unit_quantity =", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityNotEqualTo(Double value) {
            addCriterion("price_per_unit_quantity <>", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityGreaterThan(Double value) {
            addCriterion("price_per_unit_quantity >", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("price_per_unit_quantity >=", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityLessThan(Double value) {
            addCriterion("price_per_unit_quantity <", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityLessThanOrEqualTo(Double value) {
            addCriterion("price_per_unit_quantity <=", value, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityIn(List<Double> values) {
            addCriterion("price_per_unit_quantity in", values, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityNotIn(List<Double> values) {
            addCriterion("price_per_unit_quantity not in", values, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityBetween(Double value1, Double value2) {
            addCriterion("price_per_unit_quantity between", value1, value2, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andPricePerUnitQuantityNotBetween(Double value1, Double value2) {
            addCriterion("price_per_unit_quantity not between", value1, value2, "pricePerUnitQuantity");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNull() {
            addCriterion("inventory is null");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNotNull() {
            addCriterion("inventory is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualTo(Integer value) {
            addCriterion("inventory =", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualTo(Integer value) {
            addCriterion("inventory <>", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThan(Integer value) {
            addCriterion("inventory >", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory >=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThan(Integer value) {
            addCriterion("inventory <", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualTo(Integer value) {
            addCriterion("inventory <=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryIn(List<Integer> values) {
            addCriterion("inventory in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotIn(List<Integer> values) {
            addCriterion("inventory not in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryBetween(Integer value1, Integer value2) {
            addCriterion("inventory between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory not between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeIsNull() {
            addCriterion("fulfillment_lag_time is null");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeIsNotNull() {
            addCriterion("fulfillment_lag_time is not null");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeEqualTo(Integer value) {
            addCriterion("fulfillment_lag_time =", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeNotEqualTo(Integer value) {
            addCriterion("fulfillment_lag_time <>", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeGreaterThan(Integer value) {
            addCriterion("fulfillment_lag_time >", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("fulfillment_lag_time >=", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeLessThan(Integer value) {
            addCriterion("fulfillment_lag_time <", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeLessThanOrEqualTo(Integer value) {
            addCriterion("fulfillment_lag_time <=", value, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeIn(List<Integer> values) {
            addCriterion("fulfillment_lag_time in", values, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeNotIn(List<Integer> values) {
            addCriterion("fulfillment_lag_time not in", values, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeBetween(Integer value1, Integer value2) {
            addCriterion("fulfillment_lag_time between", value1, value2, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andFulfillmentLagTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("fulfillment_lag_time not between", value1, value2, "fulfillmentLagTime");
            return (Criteria) this;
        }

        public Criteria andShippingWeightIsNull() {
            addCriterion("shipping_weight is null");
            return (Criteria) this;
        }

        public Criteria andShippingWeightIsNotNull() {
            addCriterion("shipping_weight is not null");
            return (Criteria) this;
        }

        public Criteria andShippingWeightEqualTo(Double value) {
            addCriterion("shipping_weight =", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightNotEqualTo(Double value) {
            addCriterion("shipping_weight <>", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightGreaterThan(Double value) {
            addCriterion("shipping_weight >", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("shipping_weight >=", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightLessThan(Double value) {
            addCriterion("shipping_weight <", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightLessThanOrEqualTo(Double value) {
            addCriterion("shipping_weight <=", value, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightIn(List<Double> values) {
            addCriterion("shipping_weight in", values, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightNotIn(List<Double> values) {
            addCriterion("shipping_weight not in", values, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightBetween(Double value1, Double value2) {
            addCriterion("shipping_weight between", value1, value2, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andShippingWeightNotBetween(Double value1, Double value2) {
            addCriterion("shipping_weight not between", value1, value2, "shippingWeight");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Timestamp value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Timestamp value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Timestamp value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Timestamp value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Timestamp> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Timestamp> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Timestamp value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Timestamp value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Timestamp value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Timestamp value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Timestamp> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Timestamp> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIsNull() {
            addCriterion("product_attribute is null");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIsNotNull() {
            addCriterion("product_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andProductAttributeEqualTo(String value) {
            addCriterion("product_attribute =", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotEqualTo(String value) {
            addCriterion("product_attribute <>", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeGreaterThan(String value) {
            addCriterion("product_attribute >", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("product_attribute >=", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLessThan(String value) {
            addCriterion("product_attribute <", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLessThanOrEqualTo(String value) {
            addCriterion("product_attribute <=", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLike(String value) {
            addCriterion("product_attribute like", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotLike(String value) {
            addCriterion("product_attribute not like", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIn(List<String> values) {
            addCriterion("product_attribute in", values, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotIn(List<String> values) {
            addCriterion("product_attribute not in", values, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeBetween(String value1, String value2) {
            addCriterion("product_attribute between", value1, value2, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotBetween(String value1, String value2) {
            addCriterion("product_attribute not between", value1, value2, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNull() {
            addCriterion("variations is null");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNotNull() {
            addCriterion("variations is not null");
            return (Criteria) this;
        }

        public Criteria andVariationsEqualTo(String value) {
            addCriterion("variations =", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotEqualTo(String value) {
            addCriterion("variations <>", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThan(String value) {
            addCriterion("variations >", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThanOrEqualTo(String value) {
            addCriterion("variations >=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThan(String value) {
            addCriterion("variations <", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThanOrEqualTo(String value) {
            addCriterion("variations <=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLike(String value) {
            addCriterion("variations like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotLike(String value) {
            addCriterion("variations not like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsIn(List<String> values) {
            addCriterion("variations in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotIn(List<String> values) {
            addCriterion("variations not in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsBetween(String value1, String value2) {
            addCriterion("variations between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotBetween(String value1, String value2) {
            addCriterion("variations not between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNull() {
            addCriterion("publish_type is null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNotNull() {
            addCriterion("publish_type is not null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeEqualTo(Integer value) {
            addCriterion("publish_type =", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotEqualTo(Integer value) {
            addCriterion("publish_type <>", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThan(Integer value) {
            addCriterion("publish_type >", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_type >=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThan(Integer value) {
            addCriterion("publish_type <", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThanOrEqualTo(Integer value) {
            addCriterion("publish_type <=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIn(List<Integer> values) {
            addCriterion("publish_type in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotIn(List<Integer> values) {
            addCriterion("publish_type not in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeBetween(Integer value1, Integer value2) {
            addCriterion("publish_type between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_type not between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIsNull() {
            addCriterion("publish_role is null");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIsNotNull() {
            addCriterion("publish_role is not null");
            return (Criteria) this;
        }

        public Criteria andPublishRoleEqualTo(Integer value) {
            addCriterion("publish_role =", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotEqualTo(Integer value) {
            addCriterion("publish_role <>", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleGreaterThan(Integer value) {
            addCriterion("publish_role >", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_role >=", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleLessThan(Integer value) {
            addCriterion("publish_role <", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleLessThanOrEqualTo(Integer value) {
            addCriterion("publish_role <=", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIn(List<Integer> values) {
            addCriterion("publish_role in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotIn(List<Integer> values) {
            addCriterion("publish_role not in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleBetween(Integer value1, Integer value2) {
            addCriterion("publish_role between", value1, value2, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_role not between", value1, value2, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNull() {
            addCriterion("publish_status is null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNotNull() {
            addCriterion("publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusEqualTo(Integer value) {
            addCriterion("publish_status =", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotEqualTo(Integer value) {
            addCriterion("publish_status <>", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThan(Integer value) {
            addCriterion("publish_status >", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_status >=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThan(Integer value) {
            addCriterion("publish_status <", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("publish_status <=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIn(List<Integer> values) {
            addCriterion("publish_status in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotIn(List<Integer> values) {
            addCriterion("publish_status not in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("publish_status between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_status not between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIsNull() {
            addCriterion("inventory_upload is null");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIsNotNull() {
            addCriterion("inventory_upload is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadEqualTo(Integer value) {
            addCriterion("inventory_upload =", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotEqualTo(Integer value) {
            addCriterion("inventory_upload <>", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadGreaterThan(Integer value) {
            addCriterion("inventory_upload >", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_upload >=", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadLessThan(Integer value) {
            addCriterion("inventory_upload <", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_upload <=", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIn(List<Integer> values) {
            addCriterion("inventory_upload in", values, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotIn(List<Integer> values) {
            addCriterion("inventory_upload not in", values, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadBetween(Integer value1, Integer value2) {
            addCriterion("inventory_upload between", value1, value2, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_upload not between", value1, value2, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}