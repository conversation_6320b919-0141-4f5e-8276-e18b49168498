package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsYandexItemService;
import com.estone.erp.publish.yandex.common.YandexConstant;
import com.estone.erp.publish.yandex.enums.YandexStockRoleEnums;
import com.estone.erp.publish.yandex.handler.YandexUpdateHandler;
import com.estone.erp.publish.yandex.model.dto.YandexUpdateDO;
import com.estone.erp.publish.yandex.mq.bean.YandexUpdateStockByRealBean;
import com.estone.erp.publish.yandex.utils.YandexStockUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 可用+在途0调-，可用+在途大于10调9999
 */
@Component
@Slf4j
public class YandexUpdateStockByRealListener implements ChannelAwareMessageListener {

    @Resource
    private EsYandexItemService esYandexItemService;

    @Resource
    private YandexUpdateHandler yandexUpdateHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            YandexUpdateStockByRealBean bean = JSONObject.parseObject(body, YandexUpdateStockByRealBean.class);
            String accountNumber = bean.getAccountNumber();
            try {
                executeHandler(bean);
            } catch (Exception e) {
                log.error("YandexUpdateStockByGoodSourceListener [{}]店铺执行任务失败,e:{}", accountNumber, e.getMessage(), e);
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("YandexUpdateStockByGoodSourceListener error：{}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }


    private void executeHandler(YandexUpdateStockByRealBean bean) {
        Integer stockType = bean.getStockType();
        String accountNumber = bean.getAccountNumber();
        EsYandexItemRequest request = new EsYandexItemRequest();
        request.setFields(YandexConstant.UPDATE_STOCK_FILES);
        request.setAccountNumber(accountNumber);

        // 设置排除的单品状态
        if (CollectionUtils.isNotEmpty(bean.getExcludeSkuStatusList())) {
            request.setExcludeSkuStatusList(bean.getExcludeSkuStatusList());
        }

        // 设置特殊标签
        if (CollectionUtils.isNotEmpty(bean.getExcludeSpecialGoodsCodeList())) {
            request.setExcludeSpecialTag(bean.getExcludeSpecialGoodsCodeList());
        }

        // 设置SKU列表
        if (CollectionUtils.isNotEmpty(bean.getSkuList())) {
            request.setSkus(bean.getSkuList());
        }

        // 设置创建时间范围
        if (bean.getBeforeDay() != null) {
            LocalDateTime beforeDay = LocalDateTime.of(LocalDate.now().minusDays(bean.getBeforeDay()), LocalTime.MIN);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String format = beforeDay.format(dateTimeFormatter);
            request.setCreateDateLessThan(format);
        }
        int pageNum = 0;
        int pageSize = 1000;
        String gtId = null;
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setPageIndex(pageNum);
        request.setPageSize(pageSize);
        List<YandexUpdateDO> updateList = new ArrayList<>();
        while (true) {
            request.setGreaterThanId(gtId);
            PageInfo<EsYandexItem> pageInfo = esYandexItemService.page(request);
            List<EsYandexItem> items = pageInfo.getContents();
            if (CollectionUtils.isEmpty(items)) {
                break;
            }
            gtId = items.get(items.size() - 1).getId();
            for (EsYandexItem item : items) {
                YandexUpdateDO yandexUpdateDO = YandexStockUtils
                        .matchStockRule(item, null, YandexStockRoleEnums.REAL_AND_IN_TRANSIT, stockType);
                if (null == yandexUpdateDO) {
                    continue;
                }
                updateList.add(yandexUpdateDO);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (YandexUpdateDO yandexUpdateDO : updateList) {
                    yandexUpdateDO.setJob("YandexUpdateStockByRealJobHandler");
                }
                // 发送请求修改库存
                yandexUpdateHandler.syncUpdateStock(accountNumber, updateList);
                updateList = new ArrayList<>();
            }
        }
    }
}