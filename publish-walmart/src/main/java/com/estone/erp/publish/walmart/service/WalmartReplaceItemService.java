package com.estone.erp.publish.walmart.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.walmart.model.WalmartReplaceItem;
import com.estone.erp.publish.walmart.model.WalmartReplaceItemCriteria;
import com.estone.erp.publish.walmart.model.WalmartReplaceItemExample;
import java.util.List;

/**
 * <AUTHOR> walmart_replace_item
 * 2022-09-15 15:55:13
 */
public interface WalmartReplaceItemService {
    int countByExample(WalmartReplaceItemExample example);

    CQueryResult<WalmartReplaceItem> search(CQuery<WalmartReplaceItemCriteria> cquery);

    List<WalmartReplaceItem> selectByExample(WalmartReplaceItemExample example);

    WalmartReplaceItem selectByPrimaryKey(Integer id);

    int insert(WalmartReplaceItem record);

    int batchInsert(List<WalmartReplaceItem> records);

    int updateByPrimaryKeySelective(WalmartReplaceItem record);

    int updateByExampleSelective(WalmartReplaceItem record, WalmartReplaceItemExample example);

    void batchUpdateStatus(List<Integer> idList, int status);

    int deleteByPrimaryKey(List<Integer> ids);

    int deleteReplaceItem();

    int batchUpdateByPrimaryKeySelective(List<WalmartReplaceItem> replaceItems);
}