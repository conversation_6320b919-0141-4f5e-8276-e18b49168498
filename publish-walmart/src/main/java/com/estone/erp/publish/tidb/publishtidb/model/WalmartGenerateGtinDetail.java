package com.estone.erp.publish.tidb.publishtidb.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.estone.erp.publish.tidb.publishtidb.neums.WalmartGenerateGtinStatusEnum;
import com.estone.erp.publish.walmart.util.converter.EConverter;
import com.estone.erp.publish.walmart.util.converter.EasyExcel;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName walmart_generate_gtin_detail
 */
@TableName(value ="walmart_generate_gtin_detail")
@Data
public class WalmartGenerateGtinDetail implements Serializable {
    /**
     * 
     */
    @ExcelIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主表ID
     */
    @ExcelIgnore
    @TableField(value = "main_id")
    private Integer mainId;

    /**
     * 店铺
     */
    @ExcelProperty(value = "店铺")
    @TableField(value = "account")
    private String account;

    /**
     * sku
     */
    @ExcelProperty(value = "SKU")
    @TableField(value = "sku")
    private String sku;

    /**
     * gtin
     */
    @ExcelProperty(value = "GTIN")
    @TableField(value = "gtin_value")
    private String gtinValue;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @TableField(value = "remak")
    private String remak;

    /**
     * 状态，0-失败，1-成功
     */
    @ExcelProperty(value = "状态",converter = EConverter.class)
    @EasyExcel(type = WalmartGenerateGtinStatusEnum.class)
    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}