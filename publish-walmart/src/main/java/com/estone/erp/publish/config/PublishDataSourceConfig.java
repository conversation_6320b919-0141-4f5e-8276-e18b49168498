package com.estone.erp.publish.config;

import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import com.estone.erp.publish.mybatis.DataSources;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/***
 * publish数据库配置类
 */
@Configuration
@MapperScan(basePackages = { "com.estone.erp.publish.*.mapper",
        "com.estone.erp.publish.system.*.mapper", "com.estone.erp.publish.tidb.product.mapper"}, sqlSessionFactoryRef = DataSources.PUBLISH_FAC)
@ConfigurationProperties(prefix = "mybatis.publish")
public class PublishDataSourceConfig extends AbstractDataSourceConfig {

    @Primary
    @Bean(DataSources.PUBLISH_DS)
    @ConfigurationProperties(prefix = "spring.datasource.publish")
    public DataSource dataSource() {
        return getDataSource();
    }

    @Bean(DataSources.PUBLISH_FAC)
    public SqlSessionFactory sqlSessionFactory() {
        return getSqlSessionFactory(dataSource());
    }

    @Bean(DataSources.PUBLISH_TEMP)
    public SqlSessionTemplate sqlSessionTemplate() {
        return getSqlSessionTemplate(sqlSessionFactory());
    }
}