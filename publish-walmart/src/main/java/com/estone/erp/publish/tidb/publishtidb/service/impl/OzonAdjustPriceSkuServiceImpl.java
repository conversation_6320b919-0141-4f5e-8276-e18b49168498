package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.OzonAdjustPriceSku;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonAdjustPriceSkuMapper;
import com.estone.erp.publish.tidb.publishtidb.service.OzonAdjustPriceSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ozon需要调价的sku 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class OzonAdjustPriceSkuServiceImpl extends ServiceImpl<OzonAdjustPriceSkuMapper, OzonAdjustPriceSku> implements OzonAdjustPriceSkuService {

}
