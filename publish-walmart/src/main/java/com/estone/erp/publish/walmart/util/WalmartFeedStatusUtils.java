package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.walmart.model.dto.FeedStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3 10:53
 */
@Slf4j
public class WalmartFeedStatusUtils {

    /**
     * 转换feedStatus对象
     * @param body
     * @return
     */
    public static List<FeedStatus> toFeedStatus(String body) {
        if (StringUtils.isBlank(body)) {
            return Collections.emptyList();
        }

        JSONObject jsonObject = JSON.parseObject(body);

        String feedStatus = jsonObject.getString("feedStatus");
        if ("ERROR".equalsIgnoreCase(feedStatus)) {
            throw new RuntimeException("feed文件整体报错：" + body);
        }
        if (!"PROCESSED".equalsIgnoreCase(feedStatus)) {
            return Collections.emptyList();
        }

        // 如果item没有完全处理，返回空集合
        Integer itemsReceived = jsonObject.getInteger("itemsReceived");
        Integer itemsSucceeded = jsonObject.getInteger("itemsSucceeded");
        Integer itemsFailed = jsonObject.getInteger("itemsFailed");
        if (itemsFailed + itemsSucceeded != itemsReceived) {
            return Collections.emptyList();
        }

        List<FeedStatus> feedStatuses = new ArrayList<>();

        JSONObject itemDetailsJsonObject = jsonObject.getJSONObject("itemDetails");
        JSONArray itemStatusJsonArray = itemDetailsJsonObject.getJSONArray("itemIngestionStatus");

        for (int i = 0; i < itemStatusJsonArray.size(); i++) {
            JSONObject itemStatusJsonObject = itemStatusJsonArray.getJSONObject(i);
            FeedStatus itemStatus = new FeedStatus();
            String sellerSku = itemStatusJsonObject.getString("sku");
            itemStatus.setSellerSku(sellerSku);
            String status = itemStatusJsonObject.getString("ingestionStatus");
            itemStatus.setIngestionStatus(status);
            if (!"SUCCESS".equalsIgnoreCase(status)) {
                JSONObject errorsJsonObject = itemStatusJsonObject.getJSONObject("ingestionErrors");
                if (null == errorsJsonObject) {
                    String message = String.format("sellerSku[%s]执行失败：错误类型：%s；", sellerSku, status);
                    itemStatus.setIngestionErrors(message);
                } else {
                    JSONArray errorsJsonArray = errorsJsonObject.getJSONArray("ingestionError");
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(String.format("sellerSku[%s]执行失败：", sellerSku));
                    for (int j = 0; j < errorsJsonArray.size(); j++) {
                        JSONObject errorJsonObject = errorsJsonArray.getJSONObject(j);
                        String type = errorJsonObject.getString("type");
                        if (StringUtils.isNotBlank(type)) {
                            stringBuilder.append(String.format("%s.错误类型：%s，", j + 1, type));
                        }
                        String field = errorJsonObject.getString("field");
                        if (StringUtils.isNotBlank(field)) {
                            stringBuilder.append(String.format("字段：%s，", field));
                        }
                        String description = errorJsonObject.getString("description");
                        if (StringUtils.isNotBlank(description)) {
                            stringBuilder.append(String.format("描述：%s；", description));
                        }
                    }
                    itemStatus.setIngestionErrors(stringBuilder.toString());
                }
            }
            feedStatuses.add(itemStatus);
        }
        return feedStatuses;
    }

    /**
     * 解析feedId
     *
     * @param body 响应体
     * @return feedId
     */
    public static String analysisFeedId(String body) {
        if (StringUtils.isBlank(body)) {
            throw new RuntimeException("请求平台响应feedId为空");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        String feedId = jsonObject.getString("feedId");
        if (StringUtils.isBlank(feedId)) {
            log.error("异常，未获取到feedId：" + body);
            throw new RuntimeException(body);
        }
        return feedId;
    }

    /**
     * 获取feed总处理数
     * @param body
     * @return
     */
    public static int toFeedStatusCount(String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        Integer itemsReceived = jsonObject.getInteger("itemsReceived");
        if (null == itemsReceived) {
            return 0;
        } else {
            return itemsReceived;
        }
    }
}
