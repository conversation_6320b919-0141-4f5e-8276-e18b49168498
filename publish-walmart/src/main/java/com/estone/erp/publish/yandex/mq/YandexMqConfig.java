package com.estone.erp.publish.yandex.mq;

import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhExchange;
import com.estone.erp.common.mq.model.VhQueue;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.yandex.mq.listener.*;
import lombok.Data;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "mq-config")
public class YandexMqConfig {
    private boolean yandexSyncAccountListingEnable;
    private int yandexSyncAccountListingConsumer;
    private int yandexSyncAccountListingPrefetchCount;

    private boolean yandexSyncListingQueueEnable;
    private int yandexSyncListingQueueConsumer;
    private int yandexSyncListingQueuePrefetchCount;

    private boolean yandexUpdateStockZeroQueueEnable;
    private int yandexUpdateStockZeroQueueConsumers;
    private int yandexUpdateStockZeroQueuePrefetchCount;

    private boolean yandexUpdateStockByGoodSourceQueueEnable;
    private int yandexUpdateStockByGoodSourceQueueConsumers;
    private int yandexUpdateStockByGoodSourceQueuePrefetchCount;

    private boolean yandexUpdateStockByNormalQueueEnable;
    private int yandexUpdateStockByNormalQueueConsumers;
    private int yandexUpdateStockByNormalQueuePrefetchCount;


    private boolean yandexUpdateStockByRealQueueEnable;
    private int yandexUpdateStockByRealQueueConsumers;
    private int yandexUpdateStockByRealQueuePrefetchCount;


    private boolean yandexSyncProductQueueEnable;


    /**
     * 交换机
     */
    public static final String YANDEX_API_DIRECT_EXCHANGE = "YANDEX_API_DIRECT_EXCHANGE";
    public static final String YANDEX_DEAD_DIRECT_EXCHANGE = "YANDEX_DEAD_DIRECT_EXCHANGE";

    /**
     * Yandex同步产品信息队列 -修改在线列表
     */
    public static final String YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE = "YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE";
    public static final String YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE_KEY = "YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE_KEY";

    /**
     * Yandex同步产品信息队列 - 平台同步
     */
    public static final String YANDEX_SYNC_ITEM_INFO_QUEUE = "YANDEX_SYNC_ITEM_INFO_QUEUE";
    public static final String YANDEX_SYNC_ITEM_INFO_QUEUE_KEY = "YANDEX_SYNC_ITEM_INFO_QUEUE_KEY";
    public static final String YANDEX_SYNC_ITEM_INFO_DEAD_QUEUE = "YANDEX_SYNC_ITEM_INFO_DEAD_QUEUE";
    public static final String YANDEX_SYNC_ITEM_INFO_DEAD_LETTER_QUEUE_KEY = "YANDEX_SYNC_ITEM_INFO_DEAD_LETTER_QUEUE_KEY";
    /**
     * 组合套装产品信息变更
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_YANDEX_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_YANDEX_QUEUE";


    public static final String YANDEX_UPDATE_STOCK_ZERO_QUEUE = "YANDEX_UPDATE_STOCK_ZERO_QUEUE";
    public static final String YANDEX_UPDATE_STOCK_ZERO_QUEUE_KEY = "YANDEX_UPDATE_STOCK_ZERO_QUEUE_KEY";

    /**
     * Yandex根据货源地调库存
     */
    public static final String YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE = "YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE";
    public static final String YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE_KEY = "YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE_KEY";

    /**
     * Yandex正常调库存
     */
    public static final String YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE = "YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE";
    public static final String YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE_KEY = "YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE_KEY";


    /**
     * Yandex可用+在途为0调0，可用+在途大于10调9999
     */
    public static final String YANDEX_UPDATE_STOCK_BY_REAL_QUEUE = "YANDEX_UPDATE_STOCK_BY_REAL_QUEUE";
    public static final String YANDEX_UPDATE_STOCK_BY_REAL_QUEUE_KEY = "YANDEX_UPDATE_STOCK_BY_REAL_QUEUE_KEY";


    /**
     * Yandex同步listing
     */
    public static final String YANDEX_SYNC_ACCOUNT_LISTING_QUEUE = "YANDEX_SYNC_ACCOUNT_LISTING_QUEUE";
    public static final String YANDEX_SYNC_ACCOUNT_LISTING_QUEUE_KEY = "YANDEX_SYNC_ACCOUNT_LISTING_QUEUE_KEY";


    /**
     * yandex交换机
     */
    @Bean
    public DirectExchange yandexApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(YANDEX_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * yandex交换机-ALL_PUBLISH_VIRTUAL_HOST
     */
    @Bean
    public VhExchange yandexApiDirectExchangeAllPublish() {
        return new VhExchange(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_API_DIRECT_EXCHANGE, true, false, ExchangeTypes.DIRECT, null);
    }

    /**
     * 死信队列
     *
     * @return
     */
    @Bean
    public DirectExchange yandexDeadDirectExchange() {
        return new DirectExchange(YANDEX_DEAD_DIRECT_EXCHANGE, true, false);
    }

    /**
     * 死信队列-ALL_PUBLISH_VIRTUAL_HOST
     *
     * @return
     */
    @Bean
    public VhExchange yandexDeadDirectExchangeAllPublish() {
        return new VhExchange(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_DEAD_DIRECT_EXCHANGE, true, false, ExchangeTypes.DIRECT, null);
    }

    /**
     * ========================================== Yandex 同步产品信息变更 延时10s =======================================
     */

    @Bean
    public VhQueue yandexSyncItemInfoQueue() {
        // 队列持久化
        Map<String, Object> args = new HashMap<>(3);
        // x-dead-letter-exchange 这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", YANDEX_DEAD_DIRECT_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", YANDEX_SYNC_ITEM_INFO_DEAD_LETTER_QUEUE_KEY);
        // x-message-ttl  声明队列的TTL 10S
        args.put("x-message-ttl", 10000);
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ITEM_INFO_QUEUE, true, false, false, args);
    }

    @Bean
    public VhBinding yandexSyncItemInfoQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ITEM_INFO_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_SYNC_ITEM_INFO_QUEUE_KEY, null);
    }

    @Bean
    public VhQueue yandexSyncItemInfoDeadQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ITEM_INFO_DEAD_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexSyncItemInfoDeadQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ITEM_INFO_DEAD_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_DEAD_DIRECT_EXCHANGE, YANDEX_SYNC_ITEM_INFO_DEAD_LETTER_QUEUE_KEY, null);
    }

    /**
     * ========================================== YANDEX 同步产品信息变更- 产品系统信息同步 ==========================================
     */
    @Bean
    public Queue yandexSyncProductInfoChangeQueue() {
        // 队列持久化
        return new Queue(YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE, true);
    }

    @Bean
    public Binding yandexSyncProductInfoQueueChangeBinding() {
        return new Binding(YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE, Binding.DestinationType.QUEUE, YANDEX_API_DIRECT_EXCHANGE, YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE_KEY, null);
    }


    /**
     * ========================================== 广播队列 ==========================================
     */
    @Bean
    public Queue productComposeChangeDataYandexQueue() {
        // 队列持久化
        return new Queue(COMPOSE_SKU_IS_ENABLE_CHANGE_YANDEX_QUEUE, true);
    }

    @Bean
    public Binding productComposeChangeDataYandexQueueBinding() {
        FanoutExchange fanoutExchange = SpringUtils.getBean("product2PublishFanoutExchange", FanoutExchange.class);
        return BindingBuilder
                .bind(productComposeChangeDataYandexQueue())
                .to(fanoutExchange);
    }

    /**
     * ========================================== YANDEX 停产存档库存调0 ==========================================
     */
    @Bean
    public VhQueue yandexUpdateStockZeroQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_ZERO_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexUpdateStockZeroQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_ZERO_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_UPDATE_STOCK_ZERO_QUEUE_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer yandexUpdateStockZeroMessageListenerContainer(
            YandexUpdateStockZeroListener yandexUpdateStockZeroListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexUpdateStockZeroQueueEnable) {
            container.setQueueNames(YANDEX_UPDATE_STOCK_ZERO_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexUpdateStockZeroQueuePrefetchCount);
            container.setConcurrentConsumers(yandexUpdateStockZeroQueueConsumers);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(yandexUpdateStockZeroListener);
        }
        return container;
    }

    //
//
//    /**
//     * ========================================== 在线listing同步队列-详情 ==========================================s
//     */
    @Bean
    public YandexSyncEsItemInfoMQListener yandexSyncEsItemInfoMQListener() {
        return new YandexSyncEsItemInfoMQListener();
    }

    @Bean
    public SimpleMessageListenerContainer yandexSyncEsItemInfoMQListenerContainer(
            YandexSyncEsItemInfoMQListener listener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexSyncListingQueueEnable) {
            container.setQueueNames(YANDEX_SYNC_ITEM_INFO_DEAD_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexSyncListingQueuePrefetchCount);
            container.setConcurrentConsumers(yandexSyncListingQueueConsumer);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(listener);
        }
        return container;
    }

    /**
     * ========================================== 在线listing同步队列-店铺 ==========================================s
     */

    @Bean
    public VhQueue yandexSyncAccountListingQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ACCOUNT_LISTING_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexSyncAccountListingQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_SYNC_ACCOUNT_LISTING_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_SYNC_ACCOUNT_LISTING_QUEUE_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer yandexSyncAccountListingMQListenerContainer(
            YandexSyncAccountListingMQListener listener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexSyncAccountListingEnable) {
            container.setQueueNames(YANDEX_SYNC_ACCOUNT_LISTING_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexSyncAccountListingPrefetchCount);
            container.setConcurrentConsumers(yandexSyncAccountListingConsumer);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(listener);
        }
        return container;
    }


    /**
     * ========================================== YANDEX 根据货源地调库存 ==========================================
     */

    @Bean
    public VhQueue yandexUpdateStockByGoodSourceQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexUpdateStockByGoodSourceQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer yandexUpdateStockByGoodSourceListenerContainer(
            YandexUpdateStockByGoodSourceListener yandexUpdateStockByGoodSourceListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexUpdateStockByGoodSourceQueueEnable) {
            container.setQueueNames(YANDEX_UPDATE_STOCK_BY_GOOD_SOURCE_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexUpdateStockByGoodSourceQueuePrefetchCount);
            container.setConcurrentConsumers(yandexUpdateStockByGoodSourceQueueConsumers);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(yandexUpdateStockByGoodSourceListener);
        }
        return container;
    }


    /**
     * ========================================== YANDEX 正常调库存为9999 ==========================================
     */

    @Bean
    public VhQueue yandexUpdateStockByNormalQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexUpdateStockByNormalQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer yandexUpdateStockByNormalListenerContainer(
            YandexUpdateStockByNormalListener yandexUpdateStockByNormalListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexUpdateStockByNormalQueueEnable) {
            container.setQueueNames(YANDEX_UPDATE_STOCK_BY_NORMAL_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexUpdateStockByNormalQueuePrefetchCount);
            container.setConcurrentConsumers(yandexUpdateStockByNormalQueueConsumers);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(yandexUpdateStockByNormalListener);
        }
        return container;
    }


    /**
     * ========================================== YANDEX 可用+在途为0调0，可用+在途大于10调9999 ==========================================
     */

    @Bean
    public VhQueue yandexUpdateStockByRealQueue() {
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_REAL_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding yandexUpdateStockByRealQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, YANDEX_UPDATE_STOCK_BY_REAL_QUEUE, VhBinding.DestinationType.QUEUE,
                YANDEX_API_DIRECT_EXCHANGE, YANDEX_UPDATE_STOCK_BY_REAL_QUEUE_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer yandexUpdateStockByRealListenerContainer(
            YandexUpdateStockByRealListener yandexUpdateStockByRealListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (yandexUpdateStockByRealQueueEnable) {
            container.setQueueNames(YANDEX_UPDATE_STOCK_BY_REAL_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(yandexUpdateStockByRealQueuePrefetchCount);
            container.setConcurrentConsumers(yandexUpdateStockByRealQueueConsumers);
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
            container.setMessageListener(yandexUpdateStockByRealListener);
        }
        return container;
    }
}
