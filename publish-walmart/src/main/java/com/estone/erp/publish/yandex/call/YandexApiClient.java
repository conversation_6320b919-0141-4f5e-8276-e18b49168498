package com.estone.erp.publish.yandex.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.common.util.DynamicLimiter;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.yandex.call.constant.YandexApiConstant;
import com.estone.erp.publish.yandex.call.model.dto.category.CategoryDTO;
import com.estone.erp.publish.yandex.call.model.request.*;
import com.estone.erp.publish.yandex.call.model.response.CampaignResponse;
import com.estone.erp.publish.yandex.call.model.response.GetStockResponse;
import com.estone.erp.publish.yandex.call.model.response.ProductDetailInfoResponse;
import com.estone.erp.publish.yandex.call.model.response.ProductInfoResponse;
import com.estone.erp.publish.yandex.common.YandexAccountCacheManager;
import com.estone.erp.publish.yandex.common.YandexConstant;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class YandexApiClient extends AbstractHttpClient {
    @Resource
    private YandexAccountCacheManager yandexAccountCacheManager;

    private List<Header> createDefaultHeader(String apikey) {
        if (StringUtils.isBlank(apikey)) {
            throw new IllegalArgumentException("apikey cant be empty");
        }
        List<Header> headers = new ArrayList<>();
        headers.add(new BasicHeader("Api-Key", apikey));
        return headers;
    }

    /**
     * 获取库存
     */
    public YandexResponseResult<CategoryDTO> getCategory(String accountNumber, GetCategoryRequest request) {
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        String param = JSON.toJSONString(request);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.GET_CATEGORY;
        return doPost(account, param, url, new TypeReference<YandexResponseResult<CategoryDTO>>() {
        });
    }

    /**
     * 获取库存
     */
    public YandexResponseResult<GetStockResponse> getStock(String accountNumber, GetStockRequest request) {
        DynamicLimiter.getInstance(YandexConstant.YANDEX_API_GET_STOCK_LIMIT_KEY + accountNumber, 4d).acquire();
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        String param = JSON.toJSONString(request);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.getStock(account.getClientId(), request);
        return doPost(account, param, url, new TypeReference<YandexResponseResult<GetStockResponse>>() {
        });
    }

    /**
     * 更新库存
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public YandexResponseResult<Void> updateStock(String accountNumber, UpdateStockRequest request) {
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        String param = JSON.toJSONString(request);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.updateStock(account.getClientId());
        return doPut(account, param, url, new TypeReference<YandexResponseResult<Void>>() {
        });
    }

    /**
     * 获取商品
     */
    public YandexResponseResult<ProductInfoResponse> productList(String accountNumber, ProductListRequest request) {
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        String param = JSON.toJSONString(request);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.getProductList(account.getClientId(), request);
        return doPost(account, param, url, new TypeReference<YandexResponseResult<ProductInfoResponse>>() {
        });
    }


    /**
     * 获取商品详情
     */
    public YandexResponseResult<ProductDetailInfoResponse> productDetailList(String accountNumber, ProductDetailListRequest request) {
        // 通过offerIds并行查询会报"Hit rate limit of 4 parallel requests",因此在此进行限流
        DynamicLimiter.getInstance(YandexConstant.YANDEX_API_PRODUCT_DETAIL_LIST_LIMIT_KEY + accountNumber, 4d).acquire();
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        String param = JSON.toJSONString(request);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.getProductDetailList(account.getBusinessId(), request);
        return doPost(account, param, url, new TypeReference<YandexResponseResult<ProductDetailInfoResponse>>() {
        });
    }


    /**
     * 获取businessId
     */
    public CampaignResponse getCampaigns(String accountNumber) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_YANDEX, accountNumber, false);
        String url = YandexApiConstant.BASE_HOST + YandexApiConstant.GET_CAMPAIGNS;
        return doSpecialGet(account, new HashMap<>(), url, new TypeReference<>() {
        });
    }

    private <T> YandexResponseResult<T> doGet(SaleAccountAndBusinessResponse account, Map<String, Object> paramMap, String url, TypeReference<YandexResponseResult<T>> reference) {
        List<Header> defaultHeader = createDefaultHeader(account.getClientSecret());
        YandexResponseResult<T> responseResult = get(paramMap, url, reference, defaultHeader);
        if (responseResult.getResult() != null) {
            responseResult.setStatus("OK");
        }
        return responseResult;
    }


    private <T> T doSpecialGet(SaleAccountAndBusinessResponse account, Map<String, Object> paramMap, String url, TypeReference<T> reference) {
        List<Header> defaultHeader = createDefaultHeader(account.getClientSecret());
        return get(paramMap, url, reference, defaultHeader);
    }

    private <T> YandexResponseResult<T> doPost(SaleAccountAndBusinessResponse account, String json, String url, TypeReference<YandexResponseResult<T>> reference) {
        // yandex平台接口很容易超时,因此设置为60秒
        return doPost(account, json, url, reference, 600001);
    }

    private <T> YandexResponseResult<T> doPost(SaleAccountAndBusinessResponse account, String json, String url, TypeReference<YandexResponseResult<T>> reference, Integer timeOut) {
        List<Header> defaultHeader = createDefaultHeader(account.getClientSecret());
        Stopwatch watch = Stopwatch.createStarted();
        Exception ex = null;
        String result = null;
        HttpPost httpPost = new HttpPost();
        try {
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if (CollectionUtils.isNotEmpty(defaultHeader)) {
                httpPost.setHeaders(defaultHeader.toArray(new Header[]{}));
            }
            httpPost.setEntity(entity);
            httpPost.setURI(new URI(url));
            return getResultString(getHttpClient2(timeOut).execute(httpPost), reference);
        } catch (Exception e) {
            ex = new IllegalStateException("请求失败", e);
            return YandexResponseResult.failResult(e.getMessage());
        } finally {
            httpPost.releaseConnection();
            String template = "[http-client][POST][{}][requestData][{}][result][{}][error][{}][costTime][{} ms]";
            if (ex != null) {
                log.error(template, url, json, null, ex.getMessage(), watch.elapsed(TimeUnit.MILLISECONDS), ex);
            } else {
                log.debug(template, url, json, null, org.apache.commons.lang3.StringUtils.EMPTY, watch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }

    private <T> YandexResponseResult<T> doPut(SaleAccountAndBusinessResponse account, String json, String url, TypeReference<YandexResponseResult<T>> reference) {
        return doPut(account, json, url, reference, 61001);
    }

    private <T> YandexResponseResult<T> doPut(SaleAccountAndBusinessResponse account, String json, String url, TypeReference<YandexResponseResult<T>> reference, Integer timeOut) {
        List<Header> defaultHeader = createDefaultHeader(account.getClientSecret());
        Stopwatch watch = Stopwatch.createStarted();
        Exception ex = null;
        String result = null;
        HttpPut httpPut = new HttpPut();
        try {
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if (CollectionUtils.isNotEmpty(defaultHeader)) {
                httpPut.setHeaders(defaultHeader.toArray(new Header[]{}));
            }
            httpPut.setEntity(entity);
            httpPut.setURI(new URI(url));
            return getResultString(getHttpClient2(timeOut).execute(httpPut), reference);
        } catch (Exception e) {
            ex = new IllegalStateException("请求失败", e);
            return YandexResponseResult.failResult(e.getMessage());
        } finally {
            httpPut.releaseConnection();
            String template = "[http-client][POST][{}][requestData][{}][result][{}][error][{}][costTime][{} ms]";
            if (ex != null) {
                log.error(template, url, json, null, ex.getMessage(), watch.elapsed(TimeUnit.MILLISECONDS), ex);
            } else {
                log.debug(template, url, json, null, org.apache.commons.lang3.StringUtils.EMPTY, watch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }


    private <T> YandexResponseResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<YandexResponseResult<T>> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        String result = null;
        try {
            try {
                result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    YandexResponseResult<T> responseResult = JSON.parseObject(result, reference);
                    responseResult.setStatus("ERROR");
                    return responseResult;
                }
                YandexResponseResult<T> responseResult = JSON.parseObject(result, reference);
                responseResult.setStatus("OK");
                return responseResult;
            } catch (Exception e) {
                UUID uuid = UUID.randomUUID();
                log.error("[{}],statusCode:{},error:{}", uuid, statusCode, e.getMessage(), e);
                throw new RuntimeException(String.format("[%s],%s", uuid, e.getMessage()), e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }
}
