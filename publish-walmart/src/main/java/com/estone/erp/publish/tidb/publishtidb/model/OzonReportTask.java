package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OzonReportTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺
     */
    @TableField("account_number")
    private String accountNumber;

    /**
     * 报告类型SELLER_PRODUCTS — 商品报告，
     * SELLER_TRANSACTIONS — 交易报告，
     * SELLER_PRODUCT_PRICES — 商品价格报告，
     * SELLER_STOCK — 商品库存报告，
     * SELLER_RETURNS — 退货报告，
     * SELLER_POSTINGS — 发货报告，
     * SELLER_FINANCE — 财务报告，
     * SELLER_PRODUCT_DISCOUNTED — 减价商品报告。
     */
    @TableField("report_type")
    private String reportType;

    /**
     * 报告的唯一标识
     */
    @TableField("code")
    private String code;

    /**
     * waiting—在等待队列中待处理，
     * processing—正在处理，
     * success—报告成功生成，
     * failed — 报告生成错误。
     */
    @TableField("status")
    private String status;

    /**
     * 统计的日期
     */
    @TableField("day")
    private LocalDate day;

    /**
     * 文件的地址
     */
    @TableField("url")
    private String url;

    /**
     * 行数
     */
    @TableField("record_number")
    private Integer recordNumber;

    /**
     * 刊登操作状态 0 待执行 1 执行中 2 执行完成
     */
    @TableField("gen_result_status")
    private Integer genResultStatus;

    /**
     * 刊登操作状态的操作时间
     */
    @TableField("gen_result_status_time")
    private LocalDateTime genResultStatusTime;

    /**
     * 刊登操作状态 0 待执行 1 执行中 2 执行完成
     */
    @TableField("parse_file_status")
    private Integer parseFileStatus;

    /**
     * 刊登操作状态的操作时间
     */
    @TableField("parse_file_status_time")
    private LocalDateTime parseFileStatusTime;


    @TableField("message")
    private String message;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 解析文件耗时
     */
    @TableField("parse_file_cost_time")
    private Long parseFileCostTime;

}
