package com.estone.erp.publish.walmart.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> walmart_template
 * 2022-08-12 15:32:22
 */
@Getter
@Setter
public class WalmartTemplateCriteria extends WalmartTemplate {
    private static final long serialVersionUID = 1L;

    /**
     * id 逗号分隔
     */
    private String idStr;

    /**
     * 货号 逗号分隔
     */
    private String articleNumberStr;

    /**
     * 店铺集合
     */
    private List<String> accountNumberList;

    /**
     * sellerSku 逗号分隔
     */
    private String sellerSkuStr;

    /**
     * 刊登状态
     */
    private List<Integer> publishStatusList;

    /**
     * 库存上传状态
     */
    private List<Integer> inventoryUploadList;

    /**
     * 创建时间范围
     */
    private Timestamp fromCreateDate;
    private Timestamp toCreateDate;

    /**
     * 修改时间范围
     */
    private Timestamp fromUpdateDate;
    private Timestamp toUpdateDate;

    /**
     * 创建人集合
     */
    private List<String> createByList;

    public WalmartTemplateExample getExample() {
        WalmartTemplateExample example = new WalmartTemplateExample();
        WalmartTemplateExample.Criteria criteria = example.createCriteria();

        // id
        if (StringUtils.isNotBlank(this.getIdStr())) {
            if (this.getIdStr().contains(",")) {
                criteria.andIdIn(CommonUtils.splitList(this.getIdStr(), ",")
                        .stream().map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                criteria.andIdEqualTo(Integer.valueOf(this.getIdStr()));
            }
        }

        // 货号
        if (StringUtils.isNotBlank(this.getArticleNumberStr())) {
            if (this.getArticleNumberStr().contains(",")) {
                criteria.andArticleNumberIn(CommonUtils.splitList(this.getArticleNumberStr(), ","));
            } else {
                criteria.andArticleNumberEqualTo(this.getArticleNumberStr());
            }
        }

        // 店铺
        if (CollectionUtils.isNotEmpty(this.getAccountNumberList())) {
            criteria.andAccountNumberIn(this.getAccountNumberList());
        }

        // sellerSku
        if (StringUtils.isNotBlank(this.getSellerSkuStr())) {
            if (this.getSellerSkuStr().contains(",")) {
                criteria.andSellerSkuIn(CommonUtils.splitList(this.getSellerSkuStr(), ","));
            } else {
                criteria.andSellerSkuEqualTo(this.getSellerSkuStr());
            }
        }

        // 刊登状态
        if (CollectionUtils.isNotEmpty(this.getPublishStatusList())) {
            criteria.andPublishStatusIn(this.getPublishStatusList());
        }

        // 库存上传
        if (CollectionUtils.isNotEmpty(this.getInventoryUploadList())) {
            criteria.andInventoryUploadIn(this.getInventoryUploadList());
        }

        // 创建人
        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreateByIn(this.getCreateByList());
        }

        // 数据来源
        if (null != this.getSkuDataSource()) {
            criteria.andSkuDataSourceEqualTo(this.getSkuDataSource());
        }

        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (this.getSaleVariant() != null) {
            criteria.andSaleVariantEqualTo(this.getSaleVariant());
        }
        if (StringUtils.isNotBlank(this.getCategoryName())) {
            criteria.andCategoryNameEqualTo(this.getCategoryName());
        }
        if (StringUtils.isNotBlank(this.getSubCategoryId())) {
            criteria.andSubCategoryIdEqualTo(this.getSubCategoryId());
        }
        if (StringUtils.isNotBlank(this.getSubCategoryName())) {
            criteria.andSubCategoryNameEqualTo(this.getSubCategoryName());
        }
        if (StringUtils.isNotBlank(this.getCategoryAttribute())) {
            criteria.andCategoryAttributeEqualTo(this.getCategoryAttribute());
        }
        if (StringUtils.isNotBlank(this.getProductIdType())) {
            criteria.andProductIdTypeEqualTo(this.getProductIdType());
        }
        if (StringUtils.isNotBlank(this.getProductId())) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getTitle())) {
            criteria.andTitleLike("%" + this.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(this.getBrand())) {
            criteria.andBrandEqualTo(this.getBrand());
        }
        if (StringUtils.isNotBlank(this.getDescription())) {
            criteria.andDescriptionEqualTo(this.getDescription());
        }
        if (StringUtils.isNotBlank(this.getKeyFeatures())) {
            criteria.andKeyFeaturesEqualTo(this.getKeyFeatures());
        }
        if (StringUtils.isNotBlank(this.getMainImageUrl())) {
            criteria.andMainImageUrlEqualTo(this.getMainImageUrl());
        }
        if (StringUtils.isNotBlank(this.getExtraImageUrls())) {
            criteria.andExtraImageUrlsEqualTo(this.getExtraImageUrls());
        }
        if (this.getMsrp() != null) {
            criteria.andMsrpEqualTo(this.getMsrp());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (StringUtils.isNotBlank(this.getPriceUnit())) {
            criteria.andPriceUnitEqualTo(this.getPriceUnit());
        }
        if (this.getPricePerUnitQuantity() != null) {
            criteria.andPricePerUnitQuantityEqualTo(this.getPricePerUnitQuantity());
        }
        if (this.getInventory() != null) {
            criteria.andInventoryEqualTo(this.getInventory());
        }
        if (this.getFulfillmentLagTime() != null) {
            criteria.andFulfillmentLagTimeEqualTo(this.getFulfillmentLagTime());
        }
        if (this.getShippingWeight() != null) {
            criteria.andShippingWeightEqualTo(this.getShippingWeight());
        }
        if (this.getStartDate() != null) {
            criteria.andStartDateEqualTo(this.getStartDate());
        }
        if (this.getEndDate() != null) {
            criteria.andEndDateEqualTo(this.getEndDate());
        }
        if (StringUtils.isNotBlank(this.getProductAttribute())) {
            criteria.andProductAttributeEqualTo(this.getProductAttribute());
        }
        if (StringUtils.isNotBlank(this.getVariations())) {
            criteria.andVariationsEqualTo(this.getVariations());
        }
        if (this.getPublishType() != null) {
            criteria.andPublishTypeEqualTo(this.getPublishType());
        }
        if (this.getPublishRole() != null) {
            criteria.andPublishRoleEqualTo(this.getPublishRole());
        }
        if (this.getPublishStatus() != null) {
            criteria.andPublishStatusEqualTo(this.getPublishStatus());
        }
        if (this.getInventoryUpload() != null) {
            criteria.andInventoryUploadEqualTo(this.getInventoryUpload());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getFromCreateDate() != null) {
            criteria.andCreateDateGreaterThanOrEqualTo(this.getFromCreateDate());
        }
        if (this.getToCreateDate() != null) {
            criteria.andCreateDateLessThanOrEqualTo(this.getToCreateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (this.getFromUpdateDate() != null) {
            criteria.andUpdateDateGreaterThanOrEqualTo(this.getFromUpdateDate());
        }
        if (this.getToUpdateDate() != null) {
            criteria.andUpdateDateLessThanOrEqualTo(this.getToUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        return example;
    }
}