package com.estone.erp.publish.yandex.mq.bean;

import lombok.Data;

import java.util.List;

/**
 * 根据货源地调库存
 */
@Data
public class YandexUpdateStockByGoodSourceBean {

    private String accountNumber;

    private List<String> skuList;
    /**
     * 排除的单品状态
     */
    private List<String> excludeSkuStatusList;
    /**
     * 排除的特殊标签
     */
    private List<Integer> excludeSpecialGoodsCodeList;
    /**
     * 货源地
     */
    private List<String> supplyList;
    /**
     * 创建时间X时间内不进行调库存
     */
    private Integer beforeDay;

    /**
     * @see com.estone.erp.publish.common.StockTypeEnum
     */
    private Integer stockType;
}