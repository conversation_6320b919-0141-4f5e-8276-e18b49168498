package com.estone.erp.publish.tidb.publishtidb.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartTimePublishQueuePageDto;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.controller
 * @Author: sj
 * @CreateTime: 2025-06-25  15:46
 * @Description: 沃尔玛定时刊登
 */
@Slf4j
@RestController
@RequestMapping("/walmartTimePublishQueue")
public class WalmartTimePublishQueueController {

    @Resource
    private WalmartTimePublishQueueService walmartTimePublishQueueService;
    @PostMapping("/search")
    public ApiResult<CQueryResult<WalmartTimePublishQueue>> search(@RequestBody WalmartTimePublishQueuePageDto dto){
        CQueryResult<WalmartTimePublishQueue> result = walmartTimePublishQueueService.search(dto);
        return ApiResult.newSuccess(result);
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody List<Long> ids){
        walmartTimePublishQueueService.removeByIds(ids);
        return ApiResult.newSuccess();
    }

    /**
     * 批量恢复
     * @param ids
     * @return
     */
    @PostMapping("/recovery")
    public ApiResult<?> recovery(@RequestBody List<Integer> ids){
        ApiResult<?> result = walmartTimePublishQueueService.recovery(ids);
        return result;
    }

    /**
     * 批量暂停
     */
    @PostMapping("/stop")
    public ApiResult<?> stop(@RequestBody List<Integer> ids){
        return walmartTimePublishQueueService.stop(ids);
    }
}
