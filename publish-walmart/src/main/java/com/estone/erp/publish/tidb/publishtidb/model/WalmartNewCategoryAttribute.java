package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 沃尔玛新分类属性
 * @TableName walmart_new_category_attribute
 */
@TableName(value ="walmart_new_category_attribute")
@Data
public class WalmartNewCategoryAttribute {
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 叶子分类名称
     */
    @TableField(value = "sub_category_name")
    private String subCategoryName;

    /**
     * 叶子分类id
     */
    @TableField(value = "sub_category_id")
    private String subCategoryId;

    /**
     * 分类属性json
     */
    @TableField(value = "attribute")
    private String attribute;

    /**
     * 必填属性名
     */
    @TableField(value = "required_attribute_name")
    private String requiredAttributeName;

    /**
     * 非必填属性名
     */
    @TableField(value = "additional_attribute_name")
    private String additionalAttributeName;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField(value = "update_date")
    private Date updateDate;
}