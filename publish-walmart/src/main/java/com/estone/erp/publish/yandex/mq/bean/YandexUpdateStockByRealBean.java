package com.estone.erp.publish.yandex.mq.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Yandex平台根据实际库存和在途库存调整在线库存的消息Bean
 */
@Data
public class YandexUpdateStockByRealBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号编号
     */
    private String accountNumber;

    /**
     * 库存类型
     */
    private Integer stockType;

    /**
     * 创建时间X天内不进行调库存
     */
    private Integer beforeDay;

    /**
     * 指定SKU列表
     */
    private List<String> skuList;

    /**
     * 排除的单品状态列表
     */
    private List<String> excludeSkuStatusList;

    /**
     * 排除的特殊标签列表
     */
    private List<Integer> excludeSpecialGoodsCodeList;
}