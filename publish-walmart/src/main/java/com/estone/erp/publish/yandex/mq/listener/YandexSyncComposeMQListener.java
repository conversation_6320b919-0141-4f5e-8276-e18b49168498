package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.yandex.common.YandexEsItemBulkProcessor;
import com.estone.erp.publish.yandex.handler.YandexSyncProductInfoHandler;
import com.estone.erp.publish.yandex.model.dto.sync.YandexProductInfoDO;
import com.estone.erp.publish.yandex.mq.YandexMqConfig;
import com.estone.erp.publish.yandex.service.YandexEsItemService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 组合套装产品更新
 */
@ConditionalOnProperty(
        name = {"mq-config.yandexSyncComposeQueueEnable"},
        havingValue = "true"
)
@Slf4j
@Component
public class YandexSyncComposeMQListener {
    @Resource
    private YandexEsItemService yandexEsItemService;
    @Resource
    private YandexEsItemBulkProcessor yandexEsItemBulkProcessor;
    @Resource
    private YandexSyncProductInfoHandler syncProductInfoHandler;

    @RabbitListener(queues = YandexMqConfig.COMPOSE_SKU_IS_ENABLE_CHANGE_YANDEX_QUEUE, containerFactory = "publishCommonFactory")
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            String spu = JSON.parseObject(body, String.class);
            syncComposeProductInfo(spu);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("同步组合产品异常, body:{}", body, e);
        }
    }

    private void syncComposeProductInfo(String spu) {
        if (StringUtils.isBlank(spu)) {
            return;
        }
        EsYandexItemRequest request = new EsYandexItemRequest();
        request.setSku(spu);
        request.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());

        List<EsYandexItem> items = yandexEsItemService.getEsYandexItems(request);
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        for (EsYandexItem data : items) {
            try {
                YandexProductInfoDO productInfoDO = new YandexProductInfoDO();
                productInfoDO.setSku(data.getSku());
                productInfoDO.setId(data.getId());

                syncProductInfoHandler.matchComposeProduct(productInfoDO, data.getWeight());
                productInfoDO.setUpdateSyncProductInfoDate(new Date());
                yandexEsItemBulkProcessor.syncHandler(productInfoDO);
            } catch (Exception e) {
                log.error("同步异常, spu:{}", spu, e);
            }
        }

    }
}
