package com.estone.erp.publish.tidb.publishtidb.model.dto;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.model.dto
 * @Author: sj
 * @CreateTime: 2025-06-25  15:54
 * @Description: TODO
 */
@Data
public class WalmartTimePublishQueuePageDto {
    /**
     * 模板ID
     */
    private String templateIds;
    /**
     * 店铺
     */
    private List<String> accountNumbers;
    /**
     * spu
     */
    private String spuList;
    /**
     * 来源，0-自动刊登、1-普通刊登
     */
    private Integer publisherSource;

    /**
     * 刊登角色，0-系统刊登、1-销售刊登
     */
    private Integer publishRole;

    /**
     * 状态 1 等待中、2 暂停中、3 处理中、4 结束
     */
    private Integer status;

    /**
     * 标题
     */
    private String title;
    /**
     * 创建时间
     */
    private String starCreateTime;
    private String endCreateTime;
    /**
     * 刊登时间
     */
    private String starPublishTime;
    private String endPublishTime;

    /**
     * 分页页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;
    public LambdaQueryWrapper<WalmartTimePublishQueue> buildQueryWrapper() {
        LambdaQueryWrapper<WalmartTimePublishQueue> wrapper =  new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(templateIds)){
            wrapper.in(WalmartTimePublishQueue::getTemplateId, List.of(templateIds.split(",")));
        }

        if (CollectionUtils.isNotEmpty(accountNumbers)){
            wrapper.in(WalmartTimePublishQueue::getAccountNumber, accountNumbers);
        }

        if (StringUtils.isNotBlank(spuList)){
            wrapper.in(WalmartTimePublishQueue::getArticleNumber, List.of(spuList.split(",")));
        }

        if (StringUtils.isNotBlank(title)){
            wrapper.like(WalmartTimePublishQueue::getTitle, title);
        }
        if (ObjectUtils.isNotEmpty(publisherSource)){
            wrapper.eq(WalmartTimePublishQueue::getPublisherSource, publisherSource);
        }
        if (ObjectUtils.isNotEmpty(publishRole)){
            wrapper.eq(WalmartTimePublishQueue::getPublishRole, publishRole);
        }
        if (ObjectUtils.isNotEmpty(status)){
            wrapper.eq(WalmartTimePublishQueue::getStatus, status);
        }
        if (StringUtils.isNotBlank(starCreateTime)){
            wrapper.ge(WalmartTimePublishQueue::getCreatedTime, starCreateTime);
        }
        if (StringUtils.isNotBlank(endCreateTime)){
            wrapper.le(WalmartTimePublishQueue::getCreatedTime, endCreateTime);
        }
        if (StringUtils.isNotBlank(starPublishTime)){
            wrapper.ge(WalmartTimePublishQueue::getPublishTime, starPublishTime);
        }
        if (StringUtils.isNotBlank(endPublishTime)){
            wrapper.le(WalmartTimePublishQueue::getPublishTime, endPublishTime);
        }
        wrapper.orderByDesc(WalmartTimePublishQueue::getCreatedTime);
        return wrapper;
    }
}
