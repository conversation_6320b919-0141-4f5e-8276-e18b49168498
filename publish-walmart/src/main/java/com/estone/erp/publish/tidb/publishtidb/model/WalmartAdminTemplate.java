package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * walmart admin 范本
 * @TableName walmart_admin_template
 */
@TableName(value ="walmart_admin_template")
@Data
public class WalmartAdminTemplate {
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺
     */
    @TableField(value = "account_number")
    private String accountNumber;

    /**
     * 货号
     */
    @TableField(value = "article_number")
    private String articleNumber;

    /**
     * 平台sku
     */
    @TableField(value = "seller_sku")
    private String sellerSku;

    /**
     * 售卖形式-是否为变体
     */
    @TableField(value = "sale_variant")
    private Boolean saleVariant;

    /**
     * root分类名称
     */
    @TableField(value = "category_name")
    private String categoryName;

    /**
     * 叶子分类id
     */
    @TableField(value = "sub_category_id")
    private String subCategoryId;

    /**
     * 叶子分类名称
     */
    @TableField(value = "sub_category_name")
    private String subCategoryName;

    /**
     * 分类属性json
     */
    @TableField(value = "category_attribute")
    private String categoryAttribute;

    /**
     * 产品id类型
     */
    @TableField(value = "product_id_type")
    private String productIdType;

    /**
     * 产品id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    private String brand;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 五点描述
     */
    @TableField(value = "key_features")
    private String keyFeatures;

    /**
     * 主图url
     */
    @TableField(value = "main_image_url")
    private String mainImageUrl;

    /**
     * 附图url
     */
    @TableField(value = "extra_image_urls")
    private String extraImageUrls;

    /**
     * 建议零售价
     */
    @TableField(value = "msrp")
    private Double msrp;

    /**
     * 价格
     */
    @TableField(value = "price")
    private Double price;

    /**
     * 价格单位
     */
    @TableField(value = "price_unit")
    private String priceUnit;

    /**
     * 每单位价格
     */
    @TableField(value = "price_per_unit_quantity")
    private Double pricePerUnitQuantity;

    /**
     * 库存
     */
    @TableField(value = "inventory")
    private Integer inventory;

    /**
     * 履行滞后时间
     */
    @TableField(value = "fulfillment_lag_time")
    private Integer fulfillmentLagTime;

    /**
     * 装运重量
     */
    @TableField(value = "shipping_weight")
    private Double shippingWeight;

    /**
     * 网站开始时间
     */
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 网站结束时间
     */
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 产品属性json
     */
    @TableField(value = "product_attribute")
    private String productAttribute;

    /**
     * 变体
     */
    @TableField(value = "variations")
    private String variations;

    /**
     * 刊登类型：1自动刊登 2普通刊登
     */
    @TableField(value = "publish_type")
    private Integer publishType;

    /**
     * 刊登角色: 0系统刊登 1销售刊登 
     */
    @TableField(value = "publish_role")
    private Integer publishRole;

    /**
     * 刊登详细状态: 1待刊登 2待发布 3刊登中 4刊登成功 5刊登失败 6部分成功
     */
    @TableField(value = "publish_status")
    private Integer publishStatus;

    /**
     * 库存上传：1未上传库存 2请求中 3上传成功 4上传失败 5待上传库存
     */
    @TableField(value = "inventory_upload")
    private Integer inventoryUpload;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否是范本 1是 0否
     */
    @TableField(value = "is_parent")
    private Boolean isParent;

    /**
     * 数据来源 1产品系统 5组合产品
     */
    @TableField(value = "sku_data_source")
    private Integer skuDataSource;

    /**
     * 范本状态: 0-禁用，1-启用
     */
    @TableField(value = "status")
    private Integer status;
}