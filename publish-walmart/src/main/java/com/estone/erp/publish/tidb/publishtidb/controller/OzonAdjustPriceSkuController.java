package com.estone.erp.publish.tidb.publishtidb.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.tidb.publishtidb.model.OzonAdjustPriceSku;
import com.estone.erp.publish.tidb.publishtidb.service.OzonAdjustPriceSkuService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ozon需要调价的sku 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@RestController
@RequestMapping("/ozonAdjustPriceSku")
public class OzonAdjustPriceSkuController {

    @Autowired
    private OzonAdjustPriceSkuService ozonAdjustPriceSkuService;

    /**
     * 删除所有的sku
     *
     * @return apiResult
     */
    @PostMapping("/deleteAllSku")
    public ApiResult<String> deleteAllSku() {
        LambdaQueryWrapper<OzonAdjustPriceSku> wrapper = new LambdaQueryWrapper<>();
        int size = 0;
        while (true) {
            IPage<OzonAdjustPriceSku> page = new Page<>();
            page.setPages(1000);
            page.setCurrent(1);
            IPage<OzonAdjustPriceSku> resultPage = ozonAdjustPriceSkuService.page(page, wrapper);
            List<OzonAdjustPriceSku> records = resultPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            List<Long> collect = records.stream().map(OzonAdjustPriceSku::getId).collect(Collectors.toList());
            size += collect.size();
            ozonAdjustPriceSkuService.removeByIds(collect);
        }
        return ApiResult.newSuccess("一共删除了" + size + "条数据");
    }

    /**
     * 删除部分sku
     *
     * @param skus skus
     * @return ApiResult
     */
    @PostMapping("/deleteSku")
    public ApiResult<String> deleteSku(@RequestBody List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return ApiResult.newError("sku不能为空");
        }
        List<List<String>> lists = PagingUtils.newPagingList(skus, 300);
        int deleteSize = 0;
        for (List<String> list : lists) {
            // 先查询再删除
            LambdaQueryWrapper<OzonAdjustPriceSku> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OzonAdjustPriceSku::getSku, list);
            List<OzonAdjustPriceSku> ozonAdjustPriceSkus = ozonAdjustPriceSkuService.list(wrapper);
            if (CollectionUtils.isNotEmpty(ozonAdjustPriceSkus)) {
                List<Long> collect = ozonAdjustPriceSkus.stream().map(OzonAdjustPriceSku::getId).collect(Collectors.toList());
                deleteSize += collect.size();
                ozonAdjustPriceSkuService.removeByIds(collect);
            }
        }
        return ApiResult.newSuccess("一共删除了" + deleteSize + "条数据");
    }

    /**
     * 导入sku
     *
     * @param skus sku
     * @return apiResult
     */
    @PostMapping("importSku")
    public ApiResult<String> importSku(@RequestBody List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return ApiResult.newError("sku不能为空");
        }
        // 先查询再判断更新还是插入
        List<List<String>> lists = PagingUtils.newPagingList(skus, 300);
        int insertSize = 0;
        int existSize = 0;
        for (List<String> list : lists) {
            LambdaQueryWrapper<OzonAdjustPriceSku> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OzonAdjustPriceSku::getSku, list);
            List<OzonAdjustPriceSku> ozonAdjustPriceSkus = ozonAdjustPriceSkuService.list(wrapper);
            // 没有的进行插入
            List<String> existSku = ozonAdjustPriceSkus.stream().map(OzonAdjustPriceSku::getSku).collect(Collectors.toList());
            existSize += existSku.size();
            List<String> insertSkuList = list.stream().filter(item -> !existSku.contains(item)).collect(Collectors.toList());
            // 插入
            if (CollectionUtils.isNotEmpty(insertSkuList)) {
                insertSize+=insertSkuList.size();
                List<OzonAdjustPriceSku> ozonAdjustPriceSkuList = insertSkuList.stream().map(item -> {
                    OzonAdjustPriceSku ozonAdjustPriceSku = new OzonAdjustPriceSku();
                    ozonAdjustPriceSku.setSku(item);
                    ozonAdjustPriceSku.setCreatedTime(LocalDateTime.now());
                    return ozonAdjustPriceSku;
                }).collect(Collectors.toList());
                ozonAdjustPriceSkuService.saveBatch(ozonAdjustPriceSkuList);
            }
        }
        return ApiResult.newSuccess("一共导入了" + insertSize + "条数据,已经存在的" + existSize + "条数据");
    }

    @PostMapping("importExcel")
    public ApiResult<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file == null) {
            return ApiResult.newError("文件不能为空");
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Set<String> allSkuSet = new HashSet<>();
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                for (Map.Entry<Integer, String> keyValue : data.entrySet()) {
                    String value = keyValue.getValue();
                    if (StringUtils.isNotBlank(value) && !"SKU".equalsIgnoreCase(value)) {
                        allSkuSet.add(value.trim());
                    }
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).headRowNumber(1).sheet().doRead();
        // 拿到所有的sku了
        // 开始进行判断和插入
        List<List<String>> lists = PagingUtils.newPagingList(new ArrayList<>(allSkuSet), 300);
        int insertSize = 0;
        int existSize = 0;
        for (List<String> list : lists) {
            LambdaQueryWrapper<OzonAdjustPriceSku> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OzonAdjustPriceSku::getSku, list);
            List<OzonAdjustPriceSku> ozonAdjustPriceSkus = ozonAdjustPriceSkuService.list(wrapper);
            // 没有的进行插入
            List<String> existSku = ozonAdjustPriceSkus.stream().map(OzonAdjustPriceSku::getSku).collect(Collectors.toList());
            existSize += existSku.size();
            List<String> insertSkuList = list.stream().filter(item -> !existSku.contains(item)).collect(Collectors.toList());
            // 插入
            if (CollectionUtils.isNotEmpty(insertSkuList)) {
                insertSize += existSize;
                List<OzonAdjustPriceSku> ozonAdjustPriceSkuList = insertSkuList.stream().map(item -> {
                    OzonAdjustPriceSku ozonAdjustPriceSku = new OzonAdjustPriceSku();
                    ozonAdjustPriceSku.setSku(item);
                    ozonAdjustPriceSku.setCreatedTime(LocalDateTime.now());
                    return ozonAdjustPriceSku;
                }).collect(Collectors.toList());
                ozonAdjustPriceSkuService.saveBatch(ozonAdjustPriceSkuList);
            }
        }
        return ApiResult.newSuccess("一共导入了" + insertSize + "条数据,已经存在的" + existSize + "条数据");
    }

}
