package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartReplaceItemStatusEnum;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.model.WalmartReplaceItem;
import com.estone.erp.publish.walmart.model.dto.SpuToAccount;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.service.WalmartReplaceItemService;
import com.estone.erp.publish.walmart.util.WalmartFeedTaskUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 一次性任务 修改item结束时间
 * <AUTHOR>
 * @date 2023/5/15 14:38
 */
@Slf4j
@Component
public class WalmartUpdateItemEndDateJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private WalmartReplaceItemService walmartReplaceItemService;

    @Resource
    private FeedTaskService feedTaskService;

    public WalmartUpdateItemEndDateJobHandler() {
        super("WalmartUpdateItemEndDateJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {

        /**
         * 数据源类型 1查询处理报告 2查询在线列表
         */
        Integer type = 1;

        /**
         * 账号
         */
        private String accountNumber;
    }

    @Override
    @XxlJob("WalmartUpdateItemEndDateJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("执行开始");
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        Integer type = innerParam.getType();
        String accountNumber = innerParam.getAccountNumber();
        if (type == 1) {
            // 根据处理报告数据上架
            handleFeedTaskData(accountNumber);
        } else {
            // 根据在线列表数据上架
            handleListing();
        }

        XxlJobLogger.log("执行结束");
        return ReturnT.SUCCESS;
    }

    private void handleListing() {
        // 查询销量不为空被下架的spu
        List<SpuToAccount> spuToAccountList = walmartItemService.selectRetireAccountSpu();
        if (CollectionUtils.isEmpty(spuToAccountList)) {
            XxlJobLogger.log("没有可执行数据");
            return;
        }

        Map<String, List<SpuToAccount>> accountToSpuMap = spuToAccountList.stream().collect(Collectors.groupingBy(SpuToAccount::getAccountNumber));
        for (String accountNumber : accountToSpuMap.keySet()) {
            try {
                List<SpuToAccount> spuToAccounts = accountToSpuMap.get(accountNumber);
                List<String> spuList = spuToAccounts.stream().map(SpuToAccount::getSpu).collect(Collectors.toList());
                WalmartItemExample example = new WalmartItemExample();
                example.setFiledColumns("id, account_number, main_sku, sku, seller_sku, gtin, update_date");
                example.createCriteria().andAccountNumberEqualTo(accountNumber).andMainSkuIn(spuList).andRemarksLike("上架超过");
                List<WalmartItem> walmartItems = walmartItemService.selectFiledColumnsByExample(example);
                if (CollectionUtils.isEmpty(walmartItems)) {
                    continue;
                }

                // 执行
                save(walmartItems);
            } catch (Exception e) {
                XxlJobLogger.log("账号：" + accountNumber + "执行失败：" + e.getMessage());
            }
        }
    }

    private void handleFeedTaskData(String account) {
        Calendar cld = Calendar.getInstance(Locale.CHINA);
        cld.setFirstDayOfWeek(Calendar.MONDAY);// 以周一为首日
        cld.set(Calendar.HOUR_OF_DAY, 0);
        cld.set(Calendar.MINUTE, 0);
        cld.set(Calendar.SECOND, 0);// 当前时间
        cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);// 周一
        Date dateBegin = cld.getTime();

        // 查询处理报告数据
        int limit = 50000;
        Long id = 0L;
        while (true) {
            FeedTaskExample example = new FeedTaskExample();
            String filedColumns = "id, account_number, attribute3";
            example.setCustomColumn(filedColumns);
            example.setOrderByClause("id ASC");
            example.setOffset(0);
            example.setLimit(limit);
            FeedTaskExample.Criteria criteria = example.createCriteria()
                    .andIdGreaterThan(id)
                    .andTaskTypeEqualTo(WalmartTaskTypeEnum.RETIRE_ITEM.getStatusMsgEn())
                    .andCreateTimeGreaterThanOrEqualTo(dateBegin)
                    .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus())
                    .andResultMsgLike("上架超过%");
            if (StringUtils.isNotBlank(account)) {
                criteria.andAccountNumberEqualTo(account);
            }
            List<FeedTask> feedTasks = feedTaskService.selectByExample(example, SaleChannel.CHANNEL_WALMART);
            if(CollectionUtils.isEmpty(feedTasks)) {
                break;
            }
            id = feedTasks.get(feedTasks.size() - 1).getId();

            Map<String, List<FeedTask>> accountToMap = feedTasks.stream().collect(Collectors.groupingBy(FeedTask::getAccountNumber));
            for (String accountNumber : accountToMap.keySet()) {
                try {
                    List<FeedTask> feedTaskList = accountToMap.get(accountNumber);
                    List<String> sellerSkuList = feedTaskList.stream().map(FeedTask::getAttribute3).collect(Collectors.toList());
                    WalmartItemExample walmartItemExample = new WalmartItemExample();
                    walmartItemExample.setFiledColumns("id, account_number, main_sku, sku, seller_sku, gtin, update_date");
                    walmartItemExample.createCriteria()
                            .andAccountNumberEqualTo(accountNumber)
                            .andSellerSkuIn(sellerSkuList)
                            .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.RETIRED.getCode())
                            .andRemarksIsNotNull();
                    List<WalmartItem> walmartItems = walmartItemService.selectFiledColumnsByExample(walmartItemExample);
                    if (CollectionUtils.isEmpty(walmartItems)) {
                        continue;
                    }

                    // 执行
                    save(walmartItems);
                } catch (Exception e) {
                    XxlJobLogger.log("账号：" + accountNumber + "执行失败：" + e.getMessage());
                }
            }
        }
    }

    private void save(List<WalmartItem> walmartItems) {
        List<WalmartReplaceItem> replaceItems = new ArrayList<>();
        for (WalmartItem item : walmartItems) {
            try {
                WalmartReplaceItem replaceItem = new WalmartReplaceItem();
                replaceItem.setRelationId(item.getId().intValue());
                replaceItem.setAccountNumber(item.getAccountNumber());
                replaceItem.setMainSku(item.getMainSku());
                replaceItem.setSku(item.getSku());
                replaceItem.setSellerSku(item.getSellerSku());
                replaceItem.setProductIdType(WalmartPublishConstant.DEFAULT_PRODUCT_ID_TYPE);
                replaceItem.setProductId(item.getGtin());
                replaceItem.setSubCategoryName(WalmartPublishConstant.DEFAULT_CATEGORY);
                replaceItem.setEndDate(setEndDate(item.getUpdateDate()));
                replaceItem.setStatus(WalmartReplaceItemStatusEnum.WAIT_SUBMIT.getCode());
                replaceItem.setIsRetry(false);
                String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
                replaceItem.setCreateBy(currentUser);
                replaceItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
                replaceItems.add(replaceItem);
            } catch (Exception e) {
                XxlJobLogger.log(e.getMessage());
            }
        }
        walmartReplaceItemService.batchInsert(replaceItems);

        // 生成处理报告
        WalmartFeedTaskUtil.generateFeedTask(replaceItems,
                Lists.newArrayList(WalmartTaskTypeEnum.UPDATE_START_END_TIME.getStatusMsgEn()));
    }

    private Timestamp setEndDate(Timestamp updateDate) {
        updateDate = new Timestamp(updateDate.getTime() + 10L * 365 * 24 * 60 * 60 * 1000);
        return updateDate;
    }
}
