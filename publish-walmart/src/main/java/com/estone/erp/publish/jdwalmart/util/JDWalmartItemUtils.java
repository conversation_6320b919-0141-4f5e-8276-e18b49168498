package com.estone.erp.publish.jdwalmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.SkuUtils;
import com.estone.erp.publish.elasticsearch2.model.EsJDWalmartItem;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/22 11:18
 */
@Slf4j
public class JDWalmartItemUtils {

    /**
     * 转换列表对象
     * @param body
     * @param accountNumber
     * @return
     */
    public static List<EsJDWalmartItem> toEsJDWalmartItemList(String body, String accountNumber) {
        JSONObject jsonObject = JSON.parseObject(body);
        if (null == jsonObject) {
            return null;
        }
        JSONObject modelJsonObject = jsonObject.getJSONObject("model");
        if (null == modelJsonObject) {
            return null;
        }
        JSONArray itemJsonArray = modelJsonObject.getJSONArray("dataList");
        if(null == itemJsonArray) {
            return null;
        }

        List<EsJDWalmartItem> esJDWalmartItems = new ArrayList<>();
        for (int i = 0; i < itemJsonArray.size(); i++) {
            try {
                JSONObject itemJsonObject = itemJsonArray.getJSONObject(i);
                EsJDWalmartItem esJDWalmartItem = toEsJDWalmartItem(itemJsonObject, accountNumber);
                if (null != esJDWalmartItem) {
                    esJDWalmartItems.add(esJDWalmartItem);
                }
            } catch (Exception e) {
                log.error("JDWalmart解析结构体报错:" + e.getMessage());
            }
        }

        return esJDWalmartItems;
    }

    private static EsJDWalmartItem toEsJDWalmartItem(JSONObject itemJsonObject, String accountNumber) {
        if (null == itemJsonObject) {
            return null;
        }

        EsJDWalmartItem item = new EsJDWalmartItem();
        String sellerSku = itemJsonObject.getString("venderSkuCode");
        String sku = SkuUtils.getSkuBySellerSku(sellerSku, SaleChannel.CHANNEL_JDWALMART);
        item.setSellerSku(sellerSku);
        item.setSku(StringUtils.upperCase(sku));
        Long id = itemJsonObject.getLong("id");
        Long skuId = itemJsonObject.getLong("sku");
        String itemId = itemJsonObject.getString("itemId");
        item.setId(accountNumber + "_" + id + itemId + skuId);
        item.setAccountNumber(accountNumber);
        item.setItemId(itemId);
        item.setSkuId(skuId);
        item.setAppUserPin(itemJsonObject.getString("appUserPin"));
        item.setPrice(itemJsonObject.getDouble("price"));
        item.setShipMethods(itemJsonObject.getString("shipMethods"));
        item.setPublishStatus(itemJsonObject.getString("publishStatus"));
        item.setTitle(itemJsonObject.getString("productName"));
        item.setCategory(itemJsonObject.getString("productCategory"));
        item.setCreatedBy(StrConstant.ADMIN);
        item.setCreationDate(new Date());
        item.setSyncDate(new Date());

        return item;
    }

    public static void buildNotSyncInfo(EsJDWalmartItem esJDWalmartItem, EsJDWalmartItem localItem) {
        if (null == localItem || null == esJDWalmartItem) {
            return;
        }

        // 解析sku与原数据sku不一致 保留原sku
        if(!StringUtils.equalsIgnoreCase(esJDWalmartItem.getSku(), localItem.getSku())) {
            String mergeSku = ProductUtils.getMergeSku(esJDWalmartItem.getSku());
            if(StringUtils.equalsIgnoreCase(mergeSku, localItem.getSku())) {
                // 相等使用合并sku并使用原数据的产品信息
                esJDWalmartItem.setSku(mergeSku);
                esJDWalmartItem.setSpu(localItem.getSpu());
            } else {
                ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(esJDWalmartItem.getSku());
                JDWalmartItemUtils.setProductInfo(esJDWalmartItem, productInfoVO);

                esJDWalmartItem.setCreatedBy(localItem.getCreatedBy());
                esJDWalmartItem.setCreationDate(localItem.getCreationDate());
                return;
            }
        }

        // 产品相关数据
        esJDWalmartItem.setSpu(localItem.getSpu());
        esJDWalmartItem.setForbidChannel(localItem.getForbidChannel());
        esJDWalmartItem.setSkuStatus(localItem.getSkuStatus());
        esJDWalmartItem.setTagCodes(localItem.getTagCodes());
        esJDWalmartItem.setTagNames(localItem.getTagNames());
        esJDWalmartItem.setSpecialGoodsCode(localItem.getSpecialGoodsCode());
        esJDWalmartItem.setSpecialGoodsName(localItem.getSpecialGoodsName());
        esJDWalmartItem.setProCategoryId(localItem.getProCategoryId());
        esJDWalmartItem.setProCategoryCnName(localItem.getProCategoryCnName());
        esJDWalmartItem.setProhibitionSites(localItem.getProhibitionSites());
        esJDWalmartItem.setInfringementObj(localItem.getInfringementObj());
        esJDWalmartItem.setInfringementTypeName(localItem.getInfringementTypeName());
        esJDWalmartItem.setPromotion(localItem.getPromotion());
        esJDWalmartItem.setNewState(localItem.getNewState());
        esJDWalmartItem.setSkuDataSource(localItem.getSkuDataSource());
        esJDWalmartItem.setComposeStatus(localItem.getComposeStatus());

        esJDWalmartItem.setInventory(localItem.getInventory());
        esJDWalmartItem.setCreatedBy(localItem.getCreatedBy());
        esJDWalmartItem.setCreationDate(localItem.getCreationDate());
        esJDWalmartItem.setLastUpdateDate(localItem.getLastUpdateDate());
        esJDWalmartItem.setLastUpdatedBy(localItem.getLastUpdatedBy());
        esJDWalmartItem.setSyncProdDate(localItem.getSyncProdDate());
    }

    public static void handleProductInfo(List<EsJDWalmartItem> items) {
        if(CollectionUtils.isEmpty(items)) {
            return;
        }

        List<String> skuList = items.stream().map(EsJDWalmartItem::getSku).collect(Collectors.toList());
        List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(skuList);
        if (CollectionUtils.isEmpty(singleItemEsList)) {
            return;
        }

        Map<String, ProductInfoVO> skuMap = new HashMap<>();
        for (SingleItemEs singleItemEs : singleItemEsList) {
            try {
                String sonSku = singleItemEs.getSonSku();
                if(StringUtils.isNotBlank(sonSku)) {
                    skuMap.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                }
            } catch (Exception e) {
                log.error("handleProductInfo -->" + e.getMessage());
            }
        }

        for (EsJDWalmartItem item : items) {
            String sku = StringUtils.upperCase(item.getSku());
            ProductInfoVO productInfoVO = skuMap.get(sku);
            if (null == productInfoVO || null == productInfoVO.getSonSku()) {
                // 匹配组合套装
                matchComposeProduct(sku, item);
                continue;
            }

            setProductInfo(item, productInfoVO);
        }
    }

    public static void setProductInfo(EsJDWalmartItem item, ProductInfoVO productInfoVO) {
        // spu
        if (StringUtils.isBlank(item.getSpu())) {
            item.setSpu(productInfoVO.getMainSku());
        }

        // 废弃状态sku取合并sku信息
        if (StringUtils.isNotBlank(productInfoVO.getSonSku())
                && StringUtils.equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode(), productInfoVO.getSkuStatus())) {
            String mergeSku = ProductUtils.getMergeSku(productInfoVO.getSonSku());
            if(!StringUtils.equalsIgnoreCase(mergeSku, productInfoVO.getSonSku())) {
                productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                item.setSku(mergeSku);
                item.setSpu(productInfoVO.getMainSku());
            }
        }

        // 数据来源
        item.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        // 类别id
        item.setProCategoryId(productInfoVO.getCategoryId());
        // 类别中文名
        item.setProCategoryCnName(productInfoVO.getCategoryCnName());
        // 禁售平台(逗号拼接)
        item.setForbidChannel(productInfoVO.getForbidChannel());
        // 禁售站点
        item.setProhibitionSites(productInfoVO.getProhibitionSiteWithPlatformDefaultSite());
        // 禁售类型
        item.setInfringementTypeName(productInfoVO.getInfringementTypeName());
        // 禁售原因
        item.setInfringementObj(productInfoVO.getInfringementObj());
        // 单品状态
        item.setSkuStatus(productInfoVO.getSkuStatus());
        // 产品标签code
        item.setTagCodes(productInfoVO.getTagCodes());
        // 产品标签
        item.setTagNames(productInfoVO.getTagNames());
        // 特殊标签code
        item.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
        // 特殊标签
        item.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());
        // 促销状态
        item.setPromotion(productInfoVO.getPromotion());
        // 是否新品
        item.setNewState(productInfoVO.getNewState());
        // 同步产品信息时间
        item.setSyncProdDate(new Date());
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param articleNumber 货号
     * @param item  listing
     */
    public static void matchComposeProduct(String articleNumber, EsJDWalmartItem item) {
        log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",item.getAccountNumber(), articleNumber);
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(item, composeProduct);
            return;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {
            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return;
            }
            item.setSpu(suiteSku.getSuiteSku());
            item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            item.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            item.setComposeStatus(stepEnum.getCode());
            // 禁售平台
            item.setForbidChannel(StrUtil.strAddComma(StringUtils.join(suiteSku.getForbidChannels(), ",")));
            // 禁售类型
            item.setInfringementTypeName(StrUtil.strAddComma(StringUtils.join(suiteSku.getInfringementTypeNames(), "|")));
            // 禁售原因
            item.setInfringementObj(StrUtil.strAddComma(StringUtils.join(suiteSku.getInfringementObjs(), "|")));
            // 禁售站点
            item.setProhibitionSites(suiteSku.getProhibitionPlatSites());
            return;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(item, composeProductRef);
        }
    }

    private static void setProductInfoByCompose(EsJDWalmartItem item, ComposeSku composeProduct) {
        item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        String articleNumber = StringUtils.upperCase(composeProduct.getComposeSku());
        // 组合状态
        item.setComposeStatus(composeProduct.getCheckStep());
        // 单品状态
        item.setSkuStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 主SKU
        item.setSpu(articleNumber);
        // 子SKU
        item.setSku(articleNumber);
        // 类别id
        String categoryId = composeProduct.getCategoryId() != null ? StrUtil.strAddComma(composeProduct.getCategoryId().toString()) : null;
        item.setProCategoryId(categoryId);
        // 类别中文名
        String categoryName = StringUtils.isNotBlank(composeProduct.getCategoryName()) ? StrUtil.strAddComma(composeProduct.getCategoryName().replaceAll(">", ",")) : null;
        item.setProCategoryCnName(categoryName);
        // 禁售原因
        String infringementObj = StrUtil.strAddComma(StringUtils.join(composeProduct.getInfringementObjs(), "|"));
        if (StringUtils.isNotBlank(infringementObj)) {
            item.setInfringementObj(infringementObj);
        }
        // 禁售站点
        item.setProhibitionSites(composeProduct.getProhibitionPlatSites());
        //禁售平台(逗号拼接)
        String forbidChannel = StrUtil.strAddComma(StringUtils.join(composeProduct.getForbidChannels(), ","));
        item.setForbidChannel(forbidChannel);
        // 禁售类型
        String infringementTypeName = StrUtil.strAddComma(StringUtils.join(composeProduct.getInfringementTypeNames(), "|"));
        item.setInfringementTypeName(infringementTypeName);
        // 产品标签code
        item.setTagCodes(StrUtil.strAddComma(composeProduct.getTagCode()));
        // 产品标签
        item.setTagNames(StrUtil.strAddComma(composeProduct.getTag()));
        // 特殊标签
        item.setSpecialGoodsCode(null);
        // 特殊标签
        item.setSpecialGoodsName(null);
    }
}
