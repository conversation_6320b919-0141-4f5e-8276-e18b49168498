package com.estone.erp.publish.walmart.mq.consumer;

import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.walmart.mq.consumer
 * @Author: sj
 * @CreateTime: 2025-06-26  12:03
 * @Description: TODO
 */
public class WalmartAutoPublishMqListener implements ChannelAwareMessageListener {
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {

    }
}
