package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsYandexItemService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.yandex.common.YandexAccountCacheManager;
import com.estone.erp.publish.yandex.common.YandexEsItemBulkProcessor;
import com.estone.erp.publish.yandex.common.YandexExecutors;
import com.estone.erp.publish.yandex.enums.YandexFeedTaskEnums;
import com.estone.erp.publish.yandex.handler.YandexSyncListingHandler;
import com.estone.erp.publish.yandex.model.dto.sync.YandexItemDO;
import com.estone.erp.publish.yandex.model.dto.sync.YandexSyncListingMessageDO;
import com.estone.erp.publish.yandex.service.YandexFeedTaskService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Yandex同步listing
 */
@Slf4j
@Component
public class YandexSyncAccountListingMQListener implements ChannelAwareMessageListener {
    @Resource
    private YandexFeedTaskService yandexFeedTaskService;
    @Autowired
    private YandexSyncListingHandler syncListingHandler;
    @Resource
    private EsYandexItemService esYandexItemService;
    @Autowired
    private YandexEsItemBulkProcessor itemBulkProcessor;
    @Resource
    private YandexAccountCacheManager yandexAccountCacheManager;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            YandexSyncListingMessageDO messageDO = JSON.parseObject(body, YandexSyncListingMessageDO.class);
            syncAccountListing(messageDO);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("YANDEX_SYNC_ACCOUNT_LISTING_QUEUE 消费异常, message: {}", body, e);
        }
    }

    private void syncAccountListing(YandexSyncListingMessageDO messageDO) {
        StopWatch started = StopWatch.createStarted();
        String accountNumber = messageDO.getAccountNumber();
        Long feedTaskId = messageDO.getFeedTaskId();
        String syncType = messageDO.getSyncType();
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedTaskId);
        yandexFeedTaskService.updateTaskStatus(feedTask, FeedTaskStatusEnum.RUNNING.getTaskStatus());
        SaleAccountAndBusinessResponse account = yandexAccountCacheManager.getAccount(accountNumber);
        if (null == account) {
            yandexFeedTaskService.failTask(feedTask, "查询账户信息为空");
            return;
        }
        if (StringUtil.isBlank(account.getBusinessId())) {
            yandexFeedTaskService.failTask(feedTask, "账号异常,调用平台接口获取businessId为空");
            return;
        }

        ApiResult<List<String>> apiResult = syncListingHandler.loadAllSyncSellerSkus(accountNumber);
        if (!apiResult.isSuccess() || CollectionUtils.isEmpty(apiResult.getResult())) {
            if (CollectionUtils.isEmpty(apiResult.getResult())) {
                apiResult.setErrorMsg(apiResult.getErrorMsg());
            }
            yandexFeedTaskService.failTask(feedTask, "获取所有同步商品sellerSku失败, 错误原因: " + apiResult.getErrorMsg());
            // 将没有同步到的数据设置为不在线
            setOtherProductOffline(new HashSet<>(), accountNumber);
            return;
        }
        List<String> syncSellerSkus = apiResult.getResult();
        // 按是否新品分开同步
        Map<String, List<String>> groupMap = groupByNew(syncSellerSkus, accountNumber);
        List<String> newSellerSkus = groupMap.get(Boolean.TRUE.toString());
        List<String> existingSellerSkus = groupMap.get(Boolean.FALSE.toString());
        log.info("开始同步商品详情, 店铺: {}, 数量: {}, 新品：{}, 同步类型: {}, 耗时：{} ms", accountNumber, apiResult.getErrorMsg(), newSellerSkus.size(), syncType, started.getTime());
        // 同步新品
        syncNewProduct(newSellerSkus, accountNumber);

        if (YandexFeedTaskEnums.SyncItemType.ALL.name().equals(syncType)) {
            // 全量时更新已存在商品
            updateExistProduct(existingSellerSkus, accountNumber);
        }
        // 将没有同步到的数据设置为不在线
        setOtherProductOffline(new HashSet<>(syncSellerSkus), accountNumber);

        log.info("同步商品详情完成, 店铺: {}, 数量: {}, 耗时：{} ms", accountNumber, syncSellerSkus.size(), started.getTime(TimeUnit.MILLISECONDS));
        yandexFeedTaskService.succeedTask(feedTask, apiResult.getErrorMsg() + ",已同步新品:" + newSellerSkus.size());
    }

    /**
     * 按是否新品分开同步
     */
    private Map<String, List<String>> groupByNew(List<String> syncSellerSkus, String accountNumber) {
        Map<String, List<String>> groupMap = new HashMap<>();
        groupMap.put(Boolean.FALSE.toString(), new ArrayList<>());
        groupMap.put(Boolean.TRUE.toString(), new ArrayList<>());
        List<List<String>> partition = Lists.partition(syncSellerSkus, 1000);
        for (List<String> sellerSkus : partition) {
            EsYandexItemRequest request = new EsYandexItemRequest();
            request.setAccountNumber(accountNumber);
            request.setSellerSkus(sellerSkus);
            request.setFields(new String[]{"id", "sellerSku"});
            List<EsYandexItem> esYandexItems = esYandexItemService.getEsYandexItems(request);
            if (CollectionUtils.isEmpty(esYandexItems)) {
                List<String> newSellerSkus = groupMap.get(Boolean.TRUE.toString());
                newSellerSkus.addAll(sellerSkus);
                continue;
            }
            List<String> existSellerSkus = esYandexItems.stream().map(EsYandexItem::getSellerSku).collect(Collectors.toList());
            sellerSkus.forEach(sellerSku -> {
                List<String> sellerSkuGroup;
                if (existSellerSkus.contains(sellerSku)) {
                    sellerSkuGroup = groupMap.get(Boolean.FALSE.toString());
                } else {
                    sellerSkuGroup = groupMap.get(Boolean.TRUE.toString());
                }
                sellerSkuGroup.add(sellerSku);
            });
        }
        return groupMap;
    }


    /**
     * 设置非同步商品为不在线
     *
     * @param onlineSellerSkuSet 在线的sellerSku
     * @param accountNumber      店铺
     */
    private void setOtherProductOffline(Set<String> onlineSellerSkuSet, String accountNumber) {
        try {
            // do 在线数据改为不在线
            EsYandexItemRequest request = new EsYandexItemRequest();
            request.setAccountNumber(accountNumber);
            request.setOnlineOrIsNot(true);
            request.setFields(new String[]{"id", "sellerSku"});
            request.setOrderBy("id");
            request.setSequence("asc");
            esYandexItemService.scrollQueryExecutorTask(request, (itemList) -> {
                if (CollectionUtils.isEmpty(itemList)) {
                    return;
                }
                List<EsYandexItem> setOfflineItems = itemList.stream().filter(a -> !onlineSellerSkuSet.contains(a.getSellerSku())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(setOfflineItems)) {
                    return;
                }
                for (EsYandexItem setOfflineItem : setOfflineItems) {
                    itemBulkProcessor.setOnlineState(setOfflineItem.getId(), false);
                }
            });

        } catch (Exception e) {
            log.error("店铺：{}， 设置非同步商品为不在线失败：{}", accountNumber, e.getMessage(), e);
        }
    }


    /**
     * 同步商品详情
     */
    private int executeSyncProductInfo(String accountNumber, List<String> sellerSkus, Executor taskExecutor, Executor stockExecutor, boolean isSyncNewProduct) {
        if (CollectionUtils.isEmpty(sellerSkus)) {
            return 0;
        }
        // 分批同步详情
        AtomicInteger count = new AtomicInteger();
        // 平台接口限制一次最多200个商品
        List<List<String>> partition = Lists.partition(sellerSkus, 200);
        List<CompletableFuture<List<YandexItemDO>>> completableFutures = partition.stream()
                .map(partitionSellerSkus -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 提交同步商品详细任务
                        return syncListingHandler.batchSyncAndSaveItemInfo(partitionSellerSkus, accountNumber, isSyncNewProduct);
                    } catch (Exception e) {
                        log.error("提交同步商品详细任务 exception:", e);
                        return null;
                    }
                }, taskExecutor))
                .collect(Collectors.toList());
        List<List<YandexItemDO>> itemList = completableFutures.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return 0;
        }

        // 库存接口允许一次查询500个
        List<YandexItemDO> allItemDOs = itemList.stream().flatMap(List::stream).collect(Collectors.toList());
        List<List<YandexItemDO>> stockItemList = Lists.partition(allItemDOs, 500);

        stockItemList.forEach(items -> {
            List<String> sellerSkusGroup = items.stream().map(YandexItemDO::getSellerSku).collect(Collectors.toList());

            count.addAndGet(items.size());
            // 同步库存
            stockExecutor.execute(() -> {
                // 同步库存
                syncListingHandler.batchSyncStock(accountNumber, sellerSkusGroup);
            });
        });
        return count.get();
    }

    /**
     * 更新已存在商品
     */
    private void updateExistProduct(List<String> existingSellerSkus, String accountNumber) {
        if (CollectionUtils.isEmpty(existingSellerSkus)) {
            return;
        }
        YandexExecutors.SYNC_ACCOUNT_EXIST_LISTING_INFO.execute(() -> {
            syncOldProduct(existingSellerSkus, accountNumber);
        });
    }

    /**
     * 同步新品
     */
    private void syncNewProduct(List<String> newSellerSkus, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executeTotal = executeSyncProductInfo(accountNumber, newSellerSkus, YandexExecutors.SYNC_ACCOUNT_NEW_LISTING_INFO, YandexExecutors.SYNC_ACCOUNT_NEW_LISTING_INFO, true);
        log.info("同步新品详情, 店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, newSellerSkus.size(), executeTotal, started.getTime(TimeUnit.MILLISECONDS));
    }


    /**
     * 同步店铺商品详情
     */
    private void syncOldProduct(List<String> existingSellerSkus, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executedTotal = executeSyncProductInfo(accountNumber, existingSellerSkus, YandexExecutors.SYNC_ITEM_LISTING_INFO, YandexExecutors.SYNC_ITEM_STOCK_INFO, false);
        log.info("更新商品详情, 店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, existingSellerSkus.size(), executedTotal, started.getTime(TimeUnit.MILLISECONDS));
    }
}
