package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsYandexItemService;
import com.estone.erp.publish.yandex.handler.YandexUpdateHandler;
import com.estone.erp.publish.yandex.model.dto.YandexUpdateDO;
import com.estone.erp.publish.yandex.mq.bean.YandexUpdateStockZeroBean;
import com.estone.erp.publish.yandex.utils.YandexStockUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 停产存档库存调0
 */
@Slf4j
@Component
public class YandexUpdateStockZeroListener implements ChannelAwareMessageListener {

    @Resource
    private EsYandexItemService esYandexItemService;

    @Resource
    private YandexUpdateHandler yandexUpdateHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            YandexUpdateStockZeroBean yandexUpdateStockZeroBean = JSONObject.parseObject(body, YandexUpdateStockZeroBean.class);
            String accountNumber = yandexUpdateStockZeroBean.getAccountNumber();
            try {
                // 构建查询条件
                EsYandexItemRequest request = builderRequest(yandexUpdateStockZeroBean);
                executeHandler(yandexUpdateStockZeroBean.getAccountNumber(), request);
            } catch (Exception e) {
                log.error("YandexUpdateStockZeroListener [{}]店铺执行任务失败,e:{}", accountNumber, e.getMessage(), e);
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("YandexUpdateStockZeroListener error：{}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void executeHandler(String accountNumber, EsYandexItemRequest request) {
        request.setFields(new String[]{"id", "accountNumber", "sellerSku", "spu", "sku", "skuStatus", "stock", "state"});
        request.setAccountNumber(accountNumber);
        int pageNum = 0;
        int pageSize = 100;
        String gtId = null;
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setPageIndex(pageNum);
        request.setPageSize(pageSize);
        while (true) {
            request.setGreaterThanId(gtId);
            PageInfo<EsYandexItem> pageInfo = esYandexItemService.page(request);
            List<EsYandexItem> contents = pageInfo.getContents();
            if (CollectionUtils.isEmpty(contents)) {
                return;
            }
            gtId = contents.get(contents.size() - 1).getId();
            updateStock(accountNumber, contents);
        }
    }

    private void updateStock(String accountNumber, List<EsYandexItem> contents) {
        List<YandexUpdateDO> updateDOList = new ArrayList<>();
        for (EsYandexItem esYandexItem : contents) {
            try {
                Integer beforeStock = esYandexItem.getStock() == null ? 0 : Math.toIntExact(esYandexItem.getStock());
                Integer newStock = 0;
                if (beforeStock.equals(newStock)) {
                    continue;
                }
                YandexUpdateDO yandexUpdateDO = YandexStockUtils.buildUpdateStockDO(esYandexItem, beforeStock, newStock, null, null);
                updateDOList.add(yandexUpdateDO);
            } catch (Exception e) {
                log.error("YandexUpdateStockZeroListener [{} - {}]调库存失败,e:{}", accountNumber, esYandexItem.getSellerSku(), e.getMessage(), e);
            }
        }
        if (CollectionUtils.isEmpty(updateDOList)) {
            return;
        }

        for (YandexUpdateDO yandexUpdateDO : updateDOList) {
            yandexUpdateDO.setJob("YandexUpdateStockZeroJobHandler");
        }
        yandexUpdateHandler.syncUpdateStock(accountNumber, updateDOList);
    }

    private EsYandexItemRequest builderRequest(YandexUpdateStockZeroBean innerParam) {
        EsYandexItemRequest request = new EsYandexItemRequest();
        LocalDateTime now = LocalDateTime.now();
        int dayOfWeek = now.getDayOfWeek().getValue();
        // 非全量并且不是周六
        if (!"all".equals(innerParam.getAction()) && dayOfWeek != 6) {
            LocalDateTime starTime = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN).minusDays(innerParam.getInDays());
            LocalDateTime endTime = LocalDateTime.of(now.toLocalDate(), LocalTime.MAX);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String fromTime = starTime.format(dateTimeFormatter);
            String toTime = endTime.format(dateTimeFormatter);
            request.setFromSyncProductDate(fromTime);
            request.setToSyncProductDate(toTime);
        }

        if (CollectionUtils.isNotEmpty(innerParam.getSkus())) {
            request.setSkus(innerParam.getSkus());
        }
        // 停产存档
        request.setSkuStatusList(Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.PENDING.getCode()));
        // 库存大于 1
        request.setFromStock(1);
        return request;
    }
}
