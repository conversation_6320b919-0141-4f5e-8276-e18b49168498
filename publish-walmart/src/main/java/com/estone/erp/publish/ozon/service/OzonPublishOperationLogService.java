package com.estone.erp.publish.ozon.service;


import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.model.OzonPublishOperationLog;
import com.estone.erp.publish.ozon.model.OzonPublishOperationLogCriteria;
import com.estone.erp.publish.ozon.model.OzonPublishOperationLogExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2025-03-26 15:52:22
 */
public interface OzonPublishOperationLogService {
    int countByExample(OzonPublishOperationLogExample example);

    CQueryResult<OzonPublishOperationLog> search(CQuery<OzonPublishOperationLogCriteria> cquery);

    List<OzonPublishOperationLog> selectByExample(OzonPublishOperationLogExample example);

    OzonPublishOperationLog selectByPrimaryKey(Long id);

    int insert(OzonPublishOperationLog record);

    int updateByPrimaryKeySelective(OzonPublishOperationLog record);

    int updateByExampleSelective(OzonPublishOperationLog record, OzonPublishOperationLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void batchInsert(List<OzonPublishOperationLog> list);
}