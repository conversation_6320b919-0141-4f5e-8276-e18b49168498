package com.estone.erp.publish.ozon.controller;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.call.model.AccountWareHouseInfo;
import com.estone.erp.publish.ozon.model.*;
import com.estone.erp.publish.ozon.model.dto.account.AccountParamDO;
import com.estone.erp.publish.ozon.model.dto.marketing.BatchUpdateStatusDto;
import com.estone.erp.publish.ozon.model.dto.marketing.OzonMarketingConfigDO;
import com.estone.erp.publish.ozon.model.dto.marketing.OzonMarketingLogTypeEnum;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonMarketingConfigLogService;
import com.estone.erp.publish.ozon.service.OzonMarketingConfigService;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.estone.erp.publish.ozon.utils.OzonConfigAuthUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025-01-06 10:16:20
 */
@RestController
@RequestMapping("ozonMarketingConfig")
public class OzonMarketingConfigController {
    @Resource
    private OzonMarketingConfigService ozonMarketingConfigService;
    @Resource
    private OzonMarketingConfigLogService ozonMarketingConfigLogService;
    @Resource
    private OzonAccountConfigService ozonAccountConfigService;
    @Resource
    private OzonConfigAuthUtils ozonConfigAuthUtils;

    @PostMapping("/search")
    public CQueryResult<OzonMarketingConfigDO> search(@RequestBody CQuery<OzonMarketingConfigCriteria> cquery) {
        OzonMarketingConfigCriteria search = cquery.getSearch();
        isAuth(search);
        return ozonMarketingConfigService.search(cquery);
    }

    private void isAuth(OzonMarketingConfigCriteria search) {
        List<String> auth = ozonConfigAuthUtils.isAuth(search.getCreatedByList());
        search.setCreatedByList(auth);
    }

//    private void isAuth(OzonMarketingConfigCriteria search) {
//        // 查询用户权限
//        List<String> userList = permissionsHelper
//                .getCurrentUserEmployeeNoPermission2(null, null, null, SaleChannel.CHANNEL_OZON);
//
//        if (CollectionUtils.isEmpty(userList)) {
//            return;
//        }
//        List<String> createdByList = search.getCreatedByList();
//        if (CollectionUtils.isEmpty(createdByList)) {
//            search.setCreatedByList(userList);
//        } else {
//            List<String> collect = createdByList.stream().filter(userList::contains).collect(Collectors.toList());
//            search.setCreatedByList(collect);
//            if (collect.isEmpty()) {
//                throw new IllegalArgumentException("无权限访问");
//            }
//        }
//    }

    @PostMapping("/saveOrUpdate")
    public ApiResult<?> saveOrUpdate(@RequestBody @Valid OzonMarketingConfigDO ozonMarketingConfigDO) {
        return ozonMarketingConfigService.saveOrUpdate(ozonMarketingConfigDO);
    }

    @GetMapping("/getConfigLog/{id}/{offset}/{limit}")
    private CQueryResult<OzonMarketingConfigLog> getConfigLog(@PathVariable(value = "id", required = true) Integer id ,
                                                              @PathVariable(value = "offset", required = false) Integer offset,
                                                              @PathVariable(value = "limit", required = false) Integer limit) {
        OzonMarketingConfigLogCriteria ozonMarketingConfigLogCriteria = new OzonMarketingConfigLogCriteria();
        ozonMarketingConfigLogCriteria.setMarketingId(id);
        ozonMarketingConfigLogCriteria.setType(OzonMarketingLogTypeEnum.MARKETING_ACTIVITY_CONFIG.getCode());
        CQuery<OzonMarketingConfigLogCriteria> cquery = new CQuery<>();
        cquery.setLimit(limit);
        cquery.setPageReqired(true);
        cquery.setOffset(offset);
        cquery.setSearch(ozonMarketingConfigLogCriteria);
        cquery.setOrder("desc");
        cquery.setSort("operate_time");
        return ozonMarketingConfigLogService.search(cquery);
    }

    /**
     * 批量更新状态
     *
     * @param requestParam
     * @return
     */
    @PostMapping("updateStatus")
    public ApiResult<?> updateStatus(@RequestBody @Valid BatchUpdateStatusDto requestParam) {
        return ozonMarketingConfigService.updateStatus(requestParam);
    }

    @PostMapping("/getWarehouseList")
    public ApiResult<List<String>> getWarehouseList(@RequestBody AccountParamDO accountParamDO) {
        if (CollectionUtils.isEmpty(accountParamDO.getAccountNumberList())) {
            return ApiResult.newSuccess(new ArrayList<>());
        }
        OzonAccountConfigExample example = new OzonAccountConfigExample();
        example.setColumns("fbs_warehouse_info");
        example.createCriteria().andAccountNumberIn(accountParamDO.getAccountNumberList());
        List<OzonAccountConfig> ozonAccountConfigs = ozonAccountConfigService.selectByExample(example);
        Set<String> collect = ozonAccountConfigs.stream().map(OzonAccountConfig::getFbsWarehouseInfo).filter(StringUtils::isNotBlank)
                .map(a -> JSON.parseArray(a, AccountWareHouseInfo.class))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(AccountWareHouseInfo::getName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        return ApiResult.newSuccess(new ArrayList<>(collect));
    }

}