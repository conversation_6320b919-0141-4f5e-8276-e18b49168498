package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartTimePublishQueuePageDto;
import com.estone.erp.publish.walmart.model.dto.WalmartPublishRequest;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【walmart_time_publish_queue(walmart定时刊登队列)】的数据库操作Service
* @createDate 2025-06-25 15:43:19
*/
public interface WalmartTimePublishQueueService extends IService<WalmartTimePublishQueue> {

    CQueryResult<WalmartTimePublishQueue> search(WalmartTimePublishQueuePageDto dto);

    ApiResult<?> recovery(List<Integer> ids);

    ApiResult<?> stop(List<Integer> ids);

    void createTimePublishQueue(WalmartPublishRequest request);
}
