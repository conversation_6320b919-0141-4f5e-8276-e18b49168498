package com.estone.erp.publish.yandex.mq.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Yandex正常调库存Bean
 *
 */
@Data
public class YandexUpdateStockByNormalBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * sku列表
     */
    private List<String> skuList;

    /**
     * 库存类型
     * @see com.estone.erp.publish.common.StockTypeEnum
     */
    private Integer stockType;
}