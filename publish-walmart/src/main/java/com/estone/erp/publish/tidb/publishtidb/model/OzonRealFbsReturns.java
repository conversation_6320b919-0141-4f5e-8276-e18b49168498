package com.estone.erp.publish.tidb.publishtidb.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OzonRealFbsReturns implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 店铺账号
     */
    @TableField("account_number")
    private String accountNumber;

    /**
     * 退货类型
     */
    @TableField("retrun_type")
    private String retrunType;

    /**
     * 退货或取消日期
     */
    @TableField("return_or_cancellation_date")
    private LocalDateTime returnOrCancellationDate;

    /**
     * 货件编号
     */
    @TableField("posting_number")
    private String postingNumber;

    /**
     * 货号
     */
    @TableField("seller_sku")
    private String sellerSku;

    /**
     * ozon sku
     */
    @TableField("sku")
    private Integer sku;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商品数量
     */
    @TableField("product_number")
    private Integer productNumber;

    /**
     * 商品价格
     */
    @TableField("product_price")
    private BigDecimal productPrice;

    /**
     * 货币
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 状态code
     */
    @TableField("state_code")
    private String stateCode;

    /**
     * 状态日期
     */
    @TableField("state_date")
    private LocalDateTime stateDate;

    /**
     * 退货或取消原因
     */
    @TableField("return_or_cancellation_reason")
    private String returnOrCancellationReason;

    /**
     * 客户评论
     */
    @TableField("comments")
    private String comments;

    /**
     * 退货方式
     */
    @TableField("return_method_type")
    private String returnMethodType;

    /**
     * 承运商
     */
    @TableField("carrier")
    private String carrier;

    /**
     * 追踪号码

     */
    @TableField("ru_post_tracking_number")
    private String ruPostTrackingNumber;

    /**
     * 取获地址
     */
    @TableField("pickup_location")
    private String pickupLocation;

    /**
     * 退货标识id
     */
    @TableField("return_id")
    private Integer returnId;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
