package com.estone.erp.publish.tidb.publishtidb.neums;

public enum WalmartGenerateGtinStatusEnum {
    FAIL(0, "生成失败"),
    SUCCESS(1, "生成成功"),
    PART_SUCCESS(2, "部分成功"),
    wait(3, "待生成"),
    generating(4, "生成中");

    private final int code;
    private final String description;

    WalmartGenerateGtinStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取枚举
    public static WalmartGenerateGtinStatusEnum fromCode(int code) {
        for (WalmartGenerateGtinStatusEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching enum for code: " + code);
    }
}
