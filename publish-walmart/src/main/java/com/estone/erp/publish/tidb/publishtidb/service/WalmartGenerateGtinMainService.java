package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartGenerateGtinMain;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartGenerateGtinPageDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【walmart_generate_gtin_main】的数据库操作Service
* @createDate 2024-12-16 15:52:28
*/
public interface WalmartGenerateGtinMainService extends IService<WalmartGenerateGtinMain> {

    IPage<WalmartGenerateGtinMain> pageQuery(WalmartGenerateGtinPageDto dto);

    /**
     * 导入生成GTIN
     * @param file
     * @param fileName
     */
    void importExcel(MultipartFile file, String fileName);

    /**
     * 导出
     * @param response
     * @param id
     */
    void export(HttpServletResponse response, Integer id);
}
