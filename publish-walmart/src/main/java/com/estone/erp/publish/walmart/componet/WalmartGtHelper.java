package com.estone.erp.publish.walmart.componet;

import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.gt.GtProductDetail;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Walmart冠通产品帮助类
 *
 * @Auther yucm
 * @Date 2020/11/24
 */
@Slf4j
@Component
public class WalmartGtHelper {

    /**
     * 获取冠通sku站点库存
     * @param articleNumberList
     */
    public Map<String, Integer> findSkuProductStock(List<String> articleNumberList) {
        if(CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }

        Map<String, Integer> skuSite2Stock = new HashMap<>();

        List<ProductInfo> productInfoList = null;
        int tryNum = 3;
        do {
            try {
                productInfoList = ProductUtils.findProductInfos(articleNumberList);
                break;
            }catch (Exception e){
                ThreadUtil.sleep(2000);
                log.error("请求产品信息出错：", e);
            }
        }while (--tryNum > 0);

        if(CollectionUtils.isNotEmpty(productInfoList)){
            for (ProductInfo prod : productInfoList) {
                GtProductDetail other = prod.getOther();
                if(other != null){
                    Map<String, Integer> site2Stock = other.getSite2Stock();
                    if(site2Stock != null && site2Stock.size() > 0){
                        site2Stock.forEach((site, qty) ->{
                            //货号_站点 --> 库存
                            skuSite2Stock.put(String.format("%s_%s", other.getSku(), site), qty);
                        });
                    }
                }
            }
        }

        return skuSite2Stock;
    }
}
