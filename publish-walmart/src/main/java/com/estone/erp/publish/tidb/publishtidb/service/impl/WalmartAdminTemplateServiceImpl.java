package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartAdminTemplateMapper;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateLogService;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【walmart_admin_template(walmart admin 范本)】的数据库操作Service实现
 * @createDate 2025-06-27 14:44:27
 */
@Service
@Slf4j
public class WalmartAdminTemplateServiceImpl extends ServiceImpl<WalmartAdminTemplateMapper, WalmartAdminTemplate>
        implements WalmartAdminTemplateService {

    @Resource
    private WalmartAdminTemplateLogService walmartAdminTemplateLogService;

    @Override
    public CQueryResult<WalmartAdminTemplate> search(WalmartAdminTemplateCriteria cquery) {
        Assert.notNull(cquery, "查询参数不能为空");

        // 构建查询条件
        LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper = cquery.buildQueryWrapper();

        // 分页查询
        Page<WalmartAdminTemplate> page = new Page<>(cquery.getPageNum(), cquery.getPageSize());
        page = (Page<WalmartAdminTemplate>) this.page(page, queryWrapper);

        // 构建返回结果
        CQueryResult<WalmartAdminTemplate> result = new CQueryResult<>();
        result.setTotal(page.getTotal());
        result.setTotalPages((int) page.getPages());
        result.setRows(page.getRecords());
        return result;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(List<Long> ids, Integer status, String userName) {
        if (CollectionUtils.isEmpty(ids) || status == null || StringUtils.isBlank(userName)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 检查状态值是否合法
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("状态值只能为0(禁用)或1(启用)");
        }

        Date now = new Date();
        List<WalmartAdminTemplateLog> logs = new ArrayList<>();

        // 查询原状态，用于记录日志
        LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WalmartAdminTemplate::getId, ids);
        List<WalmartAdminTemplate> templates = this.list(queryWrapper);

        // 构建日志记录
        for (WalmartAdminTemplate template : templates) {
            // 如果状态没变，则跳过
            if (template.getStatus() != null && template.getStatus().equals(status)) {
                continue;
            }

            // 记录日志
            WalmartAdminTemplateLog log = new WalmartAdminTemplateLog();
            log.setTemplateId(template.getId().intValue());
            log.setBeforeValue(template.getStatus() == null ? "" : template.getStatus().toString());
            log.setAfterValue(status.toString());
            log.setCreatedBy(userName);
            log.setCreatedTime(now);
            logs.add(log);
        }

        // 批量更新状态
        if (!ids.isEmpty()) {
            LambdaUpdateWrapper<WalmartAdminTemplate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(WalmartAdminTemplate::getId, ids);
            updateWrapper.set(WalmartAdminTemplate::getStatus, status);
            updateWrapper.set(WalmartAdminTemplate::getUpdateBy, userName);
            updateWrapper.set(WalmartAdminTemplate::getUpdateDate, now);
            this.update(updateWrapper);
        }

        // 批量保存日志
        if (!logs.isEmpty()) {
            walmartAdminTemplateLogService.saveBatch(logs);
        }
    }
}
