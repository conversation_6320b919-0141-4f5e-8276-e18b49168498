package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【walmart_admin_template_log(walmart范本日志表)】的数据库操作Service
* @createDate 2025-06-27 14:44:32
*/
public interface WalmartAdminTemplateLogService extends IService<WalmartAdminTemplateLog> {

    /**
     * 根据模板ID查询日志记录
     * @param templateId 模板ID
     * @return 日志记录列表，按创建时间倒序排序
     */
    List<WalmartAdminTemplateLog> getLogsByTemplateId(Integer templateId);
}
