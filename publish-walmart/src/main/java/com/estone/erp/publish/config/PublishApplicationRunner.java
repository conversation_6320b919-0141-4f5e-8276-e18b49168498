package com.estone.erp.publish.config;

import com.estone.erp.common.alert.policy.AlertPolicyPool;
import com.estone.erp.common.util.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 项目启动后执行的方法
 *
 * @Description:
 * @ClassName: ApplicationStartup
 * @Author: wuhuiqiang
 * @Date: 2019/05/07
 * @Version: 0.0.1
 */
@Component
@ConfigurationProperties(prefix = "yml-config")
@Data
@Slf4j
public class PublishApplicationRunner implements ApplicationRunner {

    private String localPath;

    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        AlertPolicyPool.init();
        log.info("告警通知初始完成！");
    }

    public static String getResourcePath(String loaction) {
        try {
            return SpringUtils.getApplicationContext().getResource(loaction).getFile().getAbsolutePath();

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}