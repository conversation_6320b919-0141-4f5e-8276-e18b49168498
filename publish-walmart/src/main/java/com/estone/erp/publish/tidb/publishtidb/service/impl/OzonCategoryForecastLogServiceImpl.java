package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryForecastLog;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonCategoryForecastLogMapper;
import com.estone.erp.publish.tidb.publishtidb.service.OzonCategoryForecastLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ozon类目预测日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class OzonCategoryForecastLogServiceImpl extends ServiceImpl<OzonCategoryForecastLogMapper, OzonCategoryForecastLog> implements OzonCategoryForecastLogService {

}
