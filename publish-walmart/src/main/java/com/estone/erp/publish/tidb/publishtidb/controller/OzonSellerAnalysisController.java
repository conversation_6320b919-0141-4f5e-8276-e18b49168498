package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.model.OzonSellerAnalysis;
import com.estone.erp.publish.tidb.publishtidb.service.IOzonSellerAnalysisService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Controller
@RequestMapping("/publishtidb/ozon-seller-analysis")
public class OzonSellerAnalysisController {

    @Resource
    private IOzonSellerAnalysisService iOzonSellerAnalysisService;

    @GetMapping("/select")
    public ApiResult<?> select() {
        List<OzonSellerAnalysis> list = iOzonSellerAnalysisService.list();
        return ApiResult.newSuccess(list);
    }
}
