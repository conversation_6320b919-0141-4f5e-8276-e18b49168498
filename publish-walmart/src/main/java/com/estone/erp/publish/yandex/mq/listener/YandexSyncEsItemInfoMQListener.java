package com.estone.erp.publish.yandex.mq.listener;


import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.yandex.common.YandexEsItemBulkProcessor;
import com.estone.erp.publish.yandex.common.YandexExecutors;
import com.estone.erp.publish.yandex.handler.YandexSyncProductInfoHandler;
import com.estone.erp.publish.yandex.model.dto.sync.YandexProductInfoDO;
import com.estone.erp.publish.yandex.service.YandexEsItemService;
import com.rabbitmq.client.Channel;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class YandexSyncEsItemInfoMQListener implements ChannelAwareMessageListener {
    @Autowired
    private YandexEsItemService yandexEsItemService;
    @Autowired
    private YandexSyncProductInfoHandler syncProductInfoHandler;
    @Autowired
    private YandexEsItemBulkProcessor bulkProcessor;

    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            String esYandexItemId = JSON.parseObject(body, String.class);
            YandexExecutors.SYNC_ITEM_LISTING_INFO.execute(() -> {
                syncProductInfo(esYandexItemId);
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("解析sellerSku同步产品系统信息失败：{}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析sellerSku同步产品系统信息
     */
    private void syncProductInfo(String esYandexItemId) {
        if (StringUtil.isBlank(esYandexItemId)) {
            return;
        }
        StopWatch started = StopWatch.createStarted();
        try {
            EsYandexItem item = RetryUtil.doRetry(() -> getYandexItem(esYandexItemId), 3);
            // 类目
            if (StringUtil.isBlank(item.getCategoryPath())) {
                yandexEsItemService.syncCategoryPath(item.getId(), item.getCategoryId());
            }
            if (Objects.nonNull(item.getSpu())
                    && Objects.nonNull(item.getUpdateSyncProductInfoDate())
                    && item.getSellerSku().contains(item.getSpu())) {
                // 存在产品信息,spu与sellerSku相同,跳过
                return;
            }
            YandexProductInfoDO yandexProductInfoDO = syncProductInfoHandler.syncProductInfoWithSellerSku(item.getSellerSku(), item.getWeight());
            if (yandexProductInfoDO == null) {
                return;
            }
            yandexProductInfoDO.setId(esYandexItemId);
            yandexProductInfoDO.setUpdateSyncProductInfoDate(new Date());
            bulkProcessor.syncHandler(yandexProductInfoDO);
        } catch (Exception e) {
            log.error("同步产品信息失败：id:{},error:", esYandexItemId, e);
        } finally {
            log.debug("syncProductInfo:{},{}ms", esYandexItemId, started.getTime(TimeUnit.MILLISECONDS));
        }
    }

    private EsYandexItem getYandexItem(String esYandexItemId) {
        EsYandexItemRequest request = new EsYandexItemRequest();
        request.setId(esYandexItemId);
        request.setFields(new String[]{"id", "accountNumber", "sellerSku", "sku", "spu", "updateSyncProductInfoDate", "weight", "categoryId", "categoryPath", "stockSyncDate", "actualWeight"});
        EsYandexItem item = yandexEsItemService.getOneSelectFieldsWithId(request);
        if (item == null) {
            throw new RuntimeException("not find data in es:" + esYandexItemId);
        }
        return item;
    }
}
