package com.estone.erp.publish.tidb.publishtidb.model.dto;

import lombok.Data;

/**
 * Ozon属性配置查询DTO
 */
@Data
public class OzonCategoryAttributeDefaultValueQueryDTO {

    /**
     * 类目名称
     */
    private String categoryMultName;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
}