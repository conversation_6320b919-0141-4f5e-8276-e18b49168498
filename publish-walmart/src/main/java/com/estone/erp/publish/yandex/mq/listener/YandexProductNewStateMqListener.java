package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.yandex.mq.YandexMqConfig;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

/**
 * 同步产品新品状态
 */
@Slf4j
@Component
public class YandexProductNewStateMqListener implements ChannelAwareMessageListener {

    @Resource
    private RabbitTemplate rabbitTemplate;

    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            List<String> productSkus = JSON.parseArray(body, String.class);
            partitionSendMQ(productSkus);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("同步新品状态异常, body:{}", body, e);
        }
    }

    private void partitionSendMQ(List<String> productSkus) {
        List<List<String>> partition = Lists.partition(productSkus, 500);
        for (List<String> partSkus : partition) {
            ChangeSku changeSku = new ChangeSku();
            changeSku.setSkuList(partSkus);
            sendMQ(changeSku);
        }
    }

    protected void sendMQ(ChangeSku changeSku) {
        if (changeSku == null || CollectionUtils.isEmpty(changeSku.getSkuList())) {
            XxlJobLogger.log("同步新品状态存在sku为空的数据" + JSON.toJSONString(changeSku));
            return;
        }
        String msgId = YandexMqConfig.YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE + ":" + UUID.randomUUID();
        rabbitTemplate.convertAndSend(YandexMqConfig.YANDEX_API_DIRECT_EXCHANGE, YandexMqConfig.YANDEX_SYNC_PRODUCT_INFO_CHANGE_QUEUE_KEY, changeSku, (message) -> {
            message.getMessageProperties().setHeader("msg-id", msgId);
            return message;
        });
    }


}
