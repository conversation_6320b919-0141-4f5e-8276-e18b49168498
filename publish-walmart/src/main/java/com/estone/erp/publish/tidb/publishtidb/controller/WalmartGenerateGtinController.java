package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartGenerateGtinMain;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartGenerateGtinPageDto;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartGenerateGtinMainService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Controller
@RestController
@RequestMapping("/walmartGenerateGtin")
public class WalmartGenerateGtinController {

    @Resource
    private WalmartGenerateGtinMainService walmartGenerateGtinMainService;

    /**
     * 分页查询
     */
    @PostMapping("/pageQuery")
    public ApiResult<?> pageQuery(@RequestBody  WalmartGenerateGtinPageDto dto) {
        IPage<WalmartGenerateGtinMain> page = walmartGenerateGtinMainService.pageQuery(dto);
        return ApiResult.newSuccess(page);
    }

    /**
     * excel导入生成GTIN
     */
    @PostMapping("/import")
    public ApiResult<?> importExcel(@RequestParam("file") MultipartFile file,@RequestParam("fileName") String fileName) {
        walmartGenerateGtinMainService.importExcel(file, fileName);
        return ApiResult.newSuccess("导入成功，正在飞速生成。。。");
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response, @RequestParam("id") Integer id) {
        if (ObjectUtils.isEmpty(id)){
            throw new BusinessException("id不能为空");
        }
        walmartGenerateGtinMainService.export(response,id);
    }


}
