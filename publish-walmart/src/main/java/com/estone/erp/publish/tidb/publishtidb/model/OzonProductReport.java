package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OzonProductReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货号
     */
    @TableField("article_code")
    private String articleCode;

    /**
     * Ozon Product ID
     */
    @TableField("ozon_product_id")
    private String ozonProductId;

    /**
     * FBO OZON SKU ID
     */
    @TableField("fbo_ozon_sku_id")
    private String fboOzonSkuId;

    /**
     * FBS OZON SKU ID
     */
    @TableField("fbs_ozon_sku_id")
    private String fbsOzonSkuId;

    /**
     * CrossBorder Ozon SKU
     */
    @TableField("cross_border_ozon_sku")
    private String crossBorderOzonSku;

    /**
     * Barcode
     */
    @TableField("barcode")
    private String barcode;

    /**
     * Product name
     */
    @TableField("product_name")
    private String productName;

    /**
     * Content rating
     */
    @TableField("content_rating")
    private String contentRating;

    /**
     * Brand
     */
    @TableField("brand")
    private String brand;

    /**
     * Product status
     */
    @TableField("product_status")
    private String productStatus;

    /**
     * FBO visibility
     */
    @TableField("fbo_visibility")
    private String fboVisibility;

    /**
     * Reasons for hiding FBO (if any)
     */
    @TableField("reasons_for_hiding_fbo")
    private String reasonsForHidingFbo;

    /**
     * FBS visibility
     */
    @TableField("fbs_visibility")
    private String fbsVisibility;

    /**
     * Reasons for hiding FBS (if any)
     */
    @TableField("reasons_for_hiding_fbs")
    private String reasonsForHidingFbs;

    /**
     * Date of creation
     */
    @TableField("date_of_creation")
    private String dateOfCreation;

    /**
     * Commission Category
     */
    @TableField("commission_category")
    private String commissionCategory;

    /**
     * Product volume l
     */
    @TableField("product_volume_l")
    private String productVolumeL;

    /**
     * Volume weight kg
     */
    @TableField("volume_weight_kg")
    private String volumeWeightKg;

    /**
     * Available for sale under the FBO scheme pcs.
     */
    @TableField("available_for_sale_under_fbo")
    private String availableForSaleUnderFbo;

    /**
     * Take out and apply KIZ (except Tver) pcs
     */
    @TableField("take_out_and_apply_kiz")
    private String takeOutAndApplyKiz;

    /**
     * Reserved pcs
     */
    @TableField("reserved")
    private String reserved;

    /**
     * Available for sale under the FBS scheme pcs.
     */
    @TableField("available_for_sale_under_fbs")
    private String availableForSaleUnderFbs;

    /**
     * Available for sale under the realFBS scheme pcs.
     */
    @TableField("available_for_sale_under_real_fbs")
    private String availableForSaleUnderRealFbs;

    /**
     * Reserved in my warehouses pcs
     */
    @TableField("reserved_in_my_warehouses")
    private String reservedInMyWarehouses;

    /**
     * Current discounted price $
     */
    @TableField("current_discounted_price")
    private String currentDiscountedPrice;

    /**
     * Price before discount (crossed-out price) $
     */
    @TableField("price_before_discount")
    private String priceBeforeDiscount;

    /**
     * Premium Price $
     */
    @TableField("premium_price")
    private String premiumPrice;

    /**
     * Market price $
     */
    @TableField("market_price")
    private String marketPrice;

    /**
     * Up-to-date link to the market price
     */
    @TableField("up_to_date_link_to_market_price")
    private String upToDateLinkToMarketPrice;

    /**
     * Amount of VAT %
     */
    @TableField("amount_of_vat_percent")
    private String amountOfVatPercent;

    /**
     * 最后更新的任务id
     */
    @TableField("last_task_id")
    private Integer lastTaskId;

    /**
     * 店铺
     */
    @TableField("account_number")
    private String accountNumber;

    /**
     * 数据创建的时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
