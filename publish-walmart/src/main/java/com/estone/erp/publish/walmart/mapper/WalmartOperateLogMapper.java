package com.estone.erp.publish.walmart.mapper;

import com.estone.erp.publish.walmart.model.WalmartOperateLog;
import com.estone.erp.publish.walmart.model.WalmartOperateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WalmartOperateLogMapper {
    int countByExample(WalmartOperateLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(WalmartOperateLog record);

    WalmartOperateLog selectByPrimaryKey(Long id);

    List<WalmartOperateLog> selectByExample(WalmartOperateLogExample example);

    int updateByExampleSelective(@Param("record") WalmartOperateLog record, @Param("example") WalmartOperateLogExample example);

    int updateByPrimaryKeySelective(WalmartOperateLog record);
}