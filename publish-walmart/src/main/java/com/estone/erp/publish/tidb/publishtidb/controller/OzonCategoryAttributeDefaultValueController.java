package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.tidb.publishtidb.model.OzonCategoryAttributeDefaultValue;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueExportDTO;
import com.estone.erp.publish.tidb.publishtidb.model.dto.OzonCategoryAttributeDefaultValueQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.service.OzonCategoryAttributeDefaultValueService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@RestController
@RequestMapping("/ozonCategoryAttributeDefaultValue")
public class OzonCategoryAttributeDefaultValueController {

    @Resource
    private OzonCategoryAttributeDefaultValueService ozonCategoryAttributeDefaultValueService;

    /**
     * 同步分类属性表到tidb
     */
    @PostMapping("/syncAttribute")
    public ApiResult<String> syncAttribute() {
        OzonExecutors.executeOzonSyncCategory(() -> {
            ozonCategoryAttributeDefaultValueService.syncAttribute();
        });
        // 同步数据
        return ApiResult.newSuccess("正在异步同步分类属性");
    }

    /**
     * 查询属性配置
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @PostMapping("/query")
    public ApiResult<Page<OzonCategoryAttributeDefaultValue>> queryAttributeConfig(
            @RequestBody OzonCategoryAttributeDefaultValueQueryDTO queryDTO) {
        Page<OzonCategoryAttributeDefaultValue> result = ozonCategoryAttributeDefaultValueService.queryAttributeConfig(queryDTO);
        return ApiResult.newSuccess(result);
    }

    /**
     * 导出属性配置
     *
     * @param exportDTO 导出参数
     * @return 导出结果
     */
    @PostMapping("/export")
    public ApiResult<String> exportAttributeConfig(@RequestBody OzonCategoryAttributeDefaultValueExportDTO exportDTO) {
        return ozonCategoryAttributeDefaultValueService.exportAttributeConfig(exportDTO);
    }

    /**
     * 获取最近同步的时间
     *
     * @return 最近同步时间
     */
    @GetMapping("/getSyncTime")
    public ApiResult<String> getSyncTime() {
        return ozonCategoryAttributeDefaultValueService.getSyncTime();
    }

    @PostMapping("exportCategoryAttribute")
    public ApiResult<String> exportCategoryAttribute() {
        return ozonCategoryAttributeDefaultValueService.exportCategoryAttribute();
    }
}
