package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartCategoryForecastLog;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartCategoryForecastLogService;
import com.estone.erp.publish.tidb.publishtidb.mapper.WalmartCategoryForecastLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【walmart_category_forecast_log(walmart类目预测日志表)】的数据库操作Service实现
* @createDate 2025-06-11 16:55:34
*/
@Service
public class WalmartCategoryForecastLogServiceImpl extends ServiceImpl<WalmartCategoryForecastLogMapper, WalmartCategoryForecastLog>
    implements WalmartCategoryForecastLogService{

}




