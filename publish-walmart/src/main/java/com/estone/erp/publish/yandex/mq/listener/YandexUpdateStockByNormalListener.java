package com.estone.erp.publish.yandex.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItem;
import com.estone.erp.publish.elasticsearch4.model.EsYandexItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsYandexItemService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.yandex.common.YandexConstant;
import com.estone.erp.publish.yandex.enums.YandexStockRoleEnums;
import com.estone.erp.publish.yandex.handler.YandexUpdateHandler;
import com.estone.erp.publish.yandex.model.dto.YandexUpdateDO;
import com.estone.erp.publish.yandex.mq.bean.YandexUpdateStockByNormalBean;
import com.estone.erp.publish.yandex.utils.YandexStockUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * Yandex正常调库存监听器
 */
@Slf4j
@Component
public class YandexUpdateStockByNormalListener implements ChannelAwareMessageListener {

    @Resource
    private EsYandexItemService esYandexItemService;


    @Resource
    private YandexUpdateHandler yandexUpdateHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            YandexUpdateStockByNormalBean bean = JSONObject.parseObject(body, YandexUpdateStockByNormalBean.class);
            String accountNumber = bean.getAccountNumber();
            log.info("YandexUpdateStockByNormalListener [{}]店铺开始执行任务", accountNumber);
            // 查询定制产品
            ApiResult<List<String>> result = ProductUtils.getAllCustomMade();
            if (!result.isSuccess()) {
                log.error("YandexUpdateStockByNormalListener 调用产品获取定制产品sku报错：{}", result.getErrorMsg());
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            List<String> customMadeList = result.getResult();
            executeHandler(bean, customMadeList);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("YandexUpdateStockByNormalListener error：{}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void executeHandler(YandexUpdateStockByNormalBean bean,
                                List<String> customMadeList) {
        String accountNumber = bean.getAccountNumber();
        Integer stockType = bean.getStockType();
        EsYandexItemRequest request = new EsYandexItemRequest();
        request.setFields(YandexConstant.UPDATE_STOCK_FILES);
        request.setAccountNumber(bean.getAccountNumber());
        if (CollectionUtils.isNotEmpty(bean.getSkuList())) {
            request.setSkus(bean.getSkuList());
        }

        int pageNum = 0;
        int pageSize = 1000;
        String gtId = null;
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setPageIndex(pageNum);
        request.setPageSize(pageSize);
        List<YandexUpdateDO> updateList = new ArrayList<>();
        while (true) {
            request.setGreaterThanId(gtId);
            PageInfo<EsYandexItem> pageInfo = esYandexItemService.page(request);
            List<EsYandexItem> items = pageInfo.getContents();
            if (CollectionUtils.isEmpty(items)) {
                break;
            }
            gtId = items.get(items.size() - 1).getId();

            for (EsYandexItem item : items) {
                YandexUpdateDO yandexUpdateDO = YandexStockUtils
                        .matchStockRule(item, customMadeList, YandexStockRoleEnums.NORMAL, stockType);
                if (null == yandexUpdateDO) {
                    continue;
                }
                updateList.add(yandexUpdateDO);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                for (YandexUpdateDO yandexUpdateDO : updateList) {
                    yandexUpdateDO.setJob("YandexUpdateStockByNormalListener");
                }
                // 发送请求修改库存
                yandexUpdateHandler.syncUpdateStock(accountNumber, updateList);
                updateList = new ArrayList<>();
            }
        }
    }
}