<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.WalmartAdminTemplateMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="articleNumber" column="article_number" jdbcType="VARCHAR"/>
            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>
            <result property="saleVariant" column="sale_variant" jdbcType="BIT"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="subCategoryId" column="sub_category_id" jdbcType="VARCHAR"/>
            <result property="subCategoryName" column="sub_category_name" jdbcType="VARCHAR"/>
            <result property="categoryAttribute" column="category_attribute" jdbcType="VARCHAR"/>
            <result property="productIdType" column="product_id_type" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="brand" column="brand" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="keyFeatures" column="key_features" jdbcType="VARCHAR"/>
            <result property="mainImageUrl" column="main_image_url" jdbcType="VARCHAR"/>
            <result property="extraImageUrls" column="extra_image_urls" jdbcType="VARCHAR"/>
            <result property="msrp" column="msrp" jdbcType="DOUBLE"/>
            <result property="price" column="price" jdbcType="DOUBLE"/>
            <result property="priceUnit" column="price_unit" jdbcType="VARCHAR"/>
            <result property="pricePerUnitQuantity" column="price_per_unit_quantity" jdbcType="DOUBLE"/>
            <result property="inventory" column="inventory" jdbcType="INTEGER"/>
            <result property="fulfillmentLagTime" column="fulfillment_lag_time" jdbcType="INTEGER"/>
            <result property="shippingWeight" column="shipping_weight" jdbcType="DOUBLE"/>
            <result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
            <result property="productAttribute" column="product_attribute" jdbcType="VARCHAR"/>
            <result property="variations" column="variations" jdbcType="VARCHAR"/>
            <result property="publishType" column="publish_type" jdbcType="INTEGER"/>
            <result property="publishRole" column="publish_role" jdbcType="INTEGER"/>
            <result property="publishStatus" column="publish_status" jdbcType="INTEGER"/>
            <result property="inventoryUpload" column="inventory_upload" jdbcType="INTEGER"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="isParent" column="is_parent" jdbcType="BIT"/>
            <result property="skuDataSource" column="sku_data_source" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_number,article_number,
        seller_sku,sale_variant,category_name,
        sub_category_id,sub_category_name,category_attribute,
        product_id_type,product_id,title,
        brand,description,key_features,
        main_image_url,extra_image_urls,msrp,
        price,price_unit,price_per_unit_quantity,
        inventory,fulfillment_lag_time,shipping_weight,
        start_date,end_date,product_attribute,
        variations,publish_type,publish_role,
        publish_status,inventory_upload,create_date,
        create_by,update_date,update_by,
        is_parent,sku_data_source,status
    </sql>
</mapper>
