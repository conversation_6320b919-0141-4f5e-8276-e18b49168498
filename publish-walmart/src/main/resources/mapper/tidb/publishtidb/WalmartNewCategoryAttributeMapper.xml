<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.WalmartNewCategoryAttributeMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.WalmartNewCategoryAttribute">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="subCategoryName" column="sub_category_name" jdbcType="VARCHAR"/>
            <result property="subCategoryId" column="sub_category_id" jdbcType="VARCHAR"/>
            <result property="attribute" column="attribute" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sub_category_name,sub_category_id,
        attribute,create_date,update_date
    </sql>
</mapper>
