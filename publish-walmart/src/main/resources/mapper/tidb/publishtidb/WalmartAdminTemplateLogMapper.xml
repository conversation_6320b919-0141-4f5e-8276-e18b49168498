<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.WalmartAdminTemplateLogMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="templateId" column="template_id" jdbcType="INTEGER"/>
            <result property="beforeValue" column="before_value" jdbcType="VARCHAR"/>
            <result property="afterValue" column="after_value" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,template_id,before_value,
        after_value,created_by,created_time
    </sql>
</mapper>
