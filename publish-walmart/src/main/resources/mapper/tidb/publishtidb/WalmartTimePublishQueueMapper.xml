<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.WalmartTimePublishQueueMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="templateId" column="template_id" jdbcType="INTEGER"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="skuDataSource" column="sku_data_source" jdbcType="INTEGER"/>
            <result property="articleNumber" column="article_number" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="publishStatus" column="publish_status" jdbcType="INTEGER"/>
            <result property="extra" column="extra" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,template_id,account_number,
        sku_data_source,article_number,title,
        status,publish_status,extra,
        created_by,created_time,publish_time,
        update_time
    </sql>
</mapper>
