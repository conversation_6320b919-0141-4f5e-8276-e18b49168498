<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.OzonAccountConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonAccountConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="system_category_ids" property="systemCategoryIds" jdbcType="VARCHAR" />
    <result column="sku_suffix" property="skuSuffix" jdbcType="VARCHAR" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="default_length" property="defaultLength" jdbcType="DOUBLE" />
    <result column="default_Width" property="defaultWidth" jdbcType="DOUBLE" />
    <result column="default_height" property="defaultHeight" jdbcType="DOUBLE" />
    <result column="default_stock" property="defaultStock" jdbcType="INTEGER" />
    <result column="update_stock_warehouse_id" property="updateStockWarehouseId" jdbcType="BIGINT" />
    <result column="fbs_warehouse_info" property="fbsWarehouseInfo" jdbcType="VARCHAR" />
    <result column="ean_prefix" property="eanPrefix" jdbcType="VARCHAR" />
    <result column="update_stock_rule_code" property="updateStockRuleCode" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="min_gross_profit_rate" property="minGrossProfitRate" jdbcType="DOUBLE" />
    <result column="air_freight_warehouse_id" property="airFreightWarehouseId" jdbcType="BIGINT" />
    <result column="air_freight_update_stock_rule" property="airFreightUpdateStockRule" jdbcType="VARCHAR" />
    <result column="update_stock_warehouse_ids" property="updateStockWarehouseIds" jdbcType="VARCHAR" />
    <result column="auto_update_weight" property="autoUpdateWeight" jdbcType="BIT" />
    <result column="auto_set_bottom_price" property="autoSetBottomPrice" jdbcType="BIT" />
    <result column="bottom_price_gross_profit" property="bottomPriceGrossProfit" jdbcType="DOUBLE" />
    <result column="all_product_number" property="allProductNumber" jdbcType="INTEGER" />
    <result column="account_limit_number" property="accountLimitNumber" jdbcType="INTEGER" />
    <result column="all_product_number_update_time" property="allProductNumberUpdateTime" jdbcType="TIMESTAMP" />
    <result column="account_limit_number_update_time" property="accountLimitNumberUpdateTime" jdbcType="TIMESTAMP" />
    <result column="daily_create_limit" property="dailyCreateLimit" javaType="INTEGER" />
    <result column="daily_update_limit" property="dailyUpdateLimit" javaType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, system_category_ids, sku_suffix, currency, default_length, default_Width, 
    default_height, default_stock, update_stock_warehouse_id, fbs_warehouse_info, ean_prefix, 
    update_stock_rule_code, create_time, update_by, update_time, min_gross_profit_rate,
    air_freight_warehouse_id, air_freight_update_stock_rule, update_stock_warehouse_ids,
    auto_update_weight, auto_set_bottom_price, bottom_price_gross_profit, all_product_number, account_limit_number, all_product_number_update_time,
    account_limit_number_update_time, daily_create_limit, daily_update_limit
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonAccountConfigExample" >
    select
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from ozon_account_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ozon_account_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ozon_account_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ozon.model.OzonAccountConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_account_config (account_number, system_category_ids, 
      sku_suffix, currency, default_length, 
      default_Width, default_height, default_stock, 
      update_stock_warehouse_id, fbs_warehouse_info, 
      ean_prefix, update_stock_rule_code, create_time, 
      update_by, update_time, min_gross_profit_rate,
      air_freight_warehouse_id, air_freight_update_stock_rule,
      update_stock_warehouse_ids, auto_update_weight, auto_set_bottom_price, bottom_price_gross_profit)
    values (#{accountNumber,jdbcType=VARCHAR}, #{systemCategoryIds,jdbcType=VARCHAR}, 
      #{skuSuffix,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{defaultLength,jdbcType=DOUBLE}, 
      #{defaultWidth,jdbcType=DOUBLE}, #{defaultHeight,jdbcType=DOUBLE}, #{defaultStock,jdbcType=INTEGER}, 
      #{updateStockWarehouseId,jdbcType=BIGINT}, #{fbsWarehouseInfo,jdbcType=VARCHAR}, 
      #{eanPrefix,jdbcType=VARCHAR}, #{updateStockRuleCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{minGrossProfitRate,jdbcType=DOUBLE},
      #{airFreightWarehouseId,jdbcType=BIGINT}, #{airFreightUpdateStockRule,jdbcType=VARCHAR},
      #{updateStockWarehouseIds,jdbcType=VARCHAR}, #{autoUpdateWeight,jdbcType=BIT}, #{autoSetBottomPrice,jdbcType=BIT}, #{bottomPriceGrossProfit,jdbcType=DOUBLE})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ozon.model.OzonAccountConfigExample" resultType="java.lang.Integer" >
    select count(*) from ozon_account_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ozon_account_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.systemCategoryIds != null" >
        system_category_ids = #{record.systemCategoryIds,jdbcType=VARCHAR},
      </if>
      <if test="record.skuSuffix != null" >
        sku_suffix = #{record.skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null" >
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultLength != null" >
        default_length = #{record.defaultLength,jdbcType=DOUBLE},
      </if>
      <if test="record.defaultWidth != null" >
        default_Width = #{record.defaultWidth,jdbcType=DOUBLE},
      </if>
      <if test="record.defaultHeight != null" >
        default_height = #{record.defaultHeight,jdbcType=DOUBLE},
      </if>
      <if test="record.defaultStock != null" >
        default_stock = #{record.defaultStock,jdbcType=INTEGER},
      </if>
      <if test="record.updateStockWarehouseId != null" >
        update_stock_warehouse_id = #{record.updateStockWarehouseId,jdbcType=BIGINT},
      </if>
      <if test="record.fbsWarehouseInfo != null" >
        fbs_warehouse_info = #{record.fbsWarehouseInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.eanPrefix != null" >
        ean_prefix = #{record.eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="record.updateStockRuleCode != null" >
        update_stock_rule_code = #{record.updateStockRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.minGrossProfitRate != null" >
        min_gross_profit_rate = #{record.minGrossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="record.airFreightWarehouseId != null" >
        air_freight_warehouse_id = #{record.airFreightWarehouseId,jdbcType=BIGINT},
      </if>
      <if test="record.airFreightUpdateStockRule != null" >
        air_freight_update_stock_rule = #{record.airFreightUpdateStockRule,jdbcType=VARCHAR},
      </if>
      <if test="record.updateStockWarehouseIds != null" >
        update_stock_warehouse_ids = #{record.updateStockWarehouseIds,jdbcType=VARCHAR},
      </if>
      <if test="record.autoUpdateWeight != null" >
        auto_update_weight = #{record.autoUpdateWeight,jdbcType=BIT},
      </if>
      <if test="record.autoSetBottomPrice != null">
        auto_set_bottom_price = #{record.autoSetBottomPrice,jdbcType=BIT},
      </if>
      <if test="record.bottomPriceGrossProfit != null">
        bottom_price_gross_profit = #{record.bottomPriceGrossProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.allProductNumber != null">
        all_product_number = #{record.allProductNumber,jdbcType=INTEGER},
      </if>
      <if test="record.accountLimitNumber != null">
        account_limit_number = #{record.accountLimitNumber,jdbcType=INTEGER},
      </if>
      <if test="record.allProductNumberUpdateTime != null">
        all_product_number_update_time = #{record.allProductNumberUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountLimitNumberUpdateTime != null">
        account_limit_number_update_time = #{record.accountLimitNumberUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dailyCreateLimit != null">
        daily_create_limit = #{record.dailyCreateLimit,jdbcType=INTEGER},
      </if>
      <if test="record.dailyUpdateLimit != null">
        daily_update_limit = #{record.dailyUpdateLimit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ozon.model.OzonAccountConfig" >
    update ozon_account_config
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="systemCategoryIds != null" >
        system_category_ids = #{systemCategoryIds,jdbcType=VARCHAR},
      </if>
      <if test="skuSuffix != null" >
        sku_suffix = #{skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="currency != null" >
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="defaultLength != null" >
        default_length = #{defaultLength,jdbcType=DOUBLE},
      </if>
      <if test="defaultWidth != null" >
        default_Width = #{defaultWidth,jdbcType=DOUBLE},
      </if>
      <if test="defaultHeight != null" >
        default_height = #{defaultHeight,jdbcType=DOUBLE},
      </if>
      <if test="defaultStock != null" >
        default_stock = #{defaultStock,jdbcType=INTEGER},
      </if>
      <if test="updateStockWarehouseId != null" >
        update_stock_warehouse_id = #{updateStockWarehouseId,jdbcType=BIGINT},
      </if>
      <if test="fbsWarehouseInfo != null" >
        fbs_warehouse_info = #{fbsWarehouseInfo,jdbcType=VARCHAR},
      </if>
      <if test="eanPrefix != null" >
        ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="updateStockRuleCode != null" >
        update_stock_rule_code = #{updateStockRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="minGrossProfitRate != null" >
        min_gross_profit_rate = #{minGrossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="airFreightWarehouseId != null" >
        air_freight_warehouse_id = #{airFreightWarehouseId,jdbcType=BIGINT},
      </if>
      <if test="airFreightUpdateStockRule != null" >
        air_freight_update_stock_rule = #{airFreightUpdateStockRule,jdbcType=VARCHAR},
      </if>
      <if test="updateStockWarehouseIds != null" >
        update_stock_warehouse_ids = #{updateStockWarehouseIds,jdbcType=VARCHAR},
      </if>
      <if test="autoUpdateWeight != null" >
        auto_update_weight = #{autoUpdateWeight,jdbcType=BIT},
      </if>
      <if test="autoSetBottomPrice != null">
        auto_set_bottom_price = #{autoSetBottomPrice,jdbcType=BIT},
      </if>
      <if test="bottomPriceGrossProfit != null">
        bottom_price_gross_profit = #{bottomPriceGrossProfit,jdbcType=DOUBLE},
      </if>
      <if test="allProductNumber != null">
        all_product_number = #{allProductNumber,jdbcType=INTEGER},
      </if>
      <if test="accountLimitNumber != null">
        account_limit_number = #{accountLimitNumber,jdbcType=INTEGER},
      </if>
      <if test="allProductNumberUpdateTime != null">
        all_product_number_update_time = #{allProductNumberUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountLimitNumberUpdateTime != null">
        account_limit_number_update_time = #{accountLimitNumberUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dailyCreateLimit != null">
        daily_create_limit = #{dailyCreateLimit,jdbcType=INTEGER},
      </if>
      <if test="dailyUpdateLimit != null">
        daily_update_limit = #{dailyUpdateLimit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.ozon.model.OzonAccountConfig" >
    update ozon_account_config
    <set >
      account_number = #{accountNumber,jdbcType=VARCHAR},
      system_category_ids = #{systemCategoryIds,jdbcType=VARCHAR},
      sku_suffix = #{skuSuffix,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      default_length = #{defaultLength,jdbcType=DOUBLE},
      default_Width = #{defaultWidth,jdbcType=DOUBLE},
      default_height = #{defaultHeight,jdbcType=DOUBLE},
      default_stock = #{defaultStock,jdbcType=INTEGER},
      update_stock_warehouse_id = #{updateStockWarehouseId,jdbcType=BIGINT},
      ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      update_stock_rule_code = #{updateStockRuleCode,jdbcType=VARCHAR},
      min_gross_profit_rate = #{minGrossProfitRate,jdbcType=DOUBLE},
      air_freight_warehouse_id = #{airFreightWarehouseId,jdbcType=BIGINT},
      air_freight_update_stock_rule = #{airFreightUpdateStockRule,jdbcType=VARCHAR},
      update_stock_warehouse_ids = #{updateStockWarehouseIds,jdbcType=VARCHAR},
      auto_update_weight = #{autoUpdateWeight,jdbcType=BIT},
      bottom_price_gross_profit = #{bottomPriceGrossProfit,jdbcType=DOUBLE},
      <if test="autoSetBottomPrice != null">
        auto_set_bottom_price = #{autoSetBottomPrice,jdbcType=BIT},
      </if>
      <if test="fbsWarehouseInfo != null" >
        fbs_warehouse_info = #{fbsWarehouseInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByAccountNumber" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ozon_account_config
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </select>

  <update id="updateProductLimit">
    update ozon_account_config
      set account_limit_number = #{accountLimitNumber,jdbcType=INTEGER},
      all_product_number = #{allProductNumber,jdbcType=INTEGER},
      all_product_number_update_time = #{allProductNumberUpdateTime,jdbcType=TIMESTAMP},
      account_limit_number_update_time = #{accountLimitNumberUpdateTime,jdbcType=TIMESTAMP},
      daily_create_limit = #{dailyCreateLimit,jdbcType=INTEGER},
      daily_update_limit = #{dailyUpdateLimit,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
