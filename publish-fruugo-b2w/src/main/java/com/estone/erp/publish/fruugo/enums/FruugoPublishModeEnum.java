package com.estone.erp.publish.fruugo.enums;

public enum FruugoPublishModeEnum {

    TEMPLATE(1,"模板刊登"),

    BATCH_PUBLISH(2,"批量刊登"),

    SPU_PUBLISH(3,"spu直接刊登"),

    TIME_PUBLISH(4,"定时刊登");

    private int code;

    private String name;

    FruugoPublishModeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}