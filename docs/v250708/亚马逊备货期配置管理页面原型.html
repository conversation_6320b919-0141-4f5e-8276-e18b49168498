<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊修改备货期配置管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background-color: #f8f9fa;
        }
        .sidebar {
            width: 250px;
            background: #343a40;
            min-height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            padding-top: 60px;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-radius: 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            margin-left: 250px;
            padding-top: 60px;
        }
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 60px;
        }
        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }
        .status-enabled { color: #28a745; }
        .status-disabled { color: #6c757d; }
        .table th { 
            background-color: #f8f9fa; 
            font-weight: 600;
            padding: 12px 8px;
            border-bottom: 2px solid #dee2e6;
        }
        .table td { 
            padding: 12px 8px;
            vertical-align: middle;
        }
        .search-form { 
            background: #fff; 
            padding: 25px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .function-buttons {
            background: #fff;
            padding: 15px 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .config-section { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .config-section h5 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .form-row { margin-bottom: 15px; }
        .required::after { content: "*"; color: red; margin-left: 3px; }
        .radio-group { padding: 15px; background: #f8f9fa; border-radius: 5px; margin-bottom: 15px; }
        .condition-panel { display: none; padding: 15px; background: #e9ecef; border-radius: 5px; margin-top: 10px; }
        .condition-panel.active { display: block; }
        .page-header { 
            background: #fff; 
            padding: 25px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table-container { 
            background: #fff; 
            border-radius: 8px; 
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn-group .btn {
            margin-right: 4px;
            white-space: nowrap;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .operation-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        .operation-buttons .btn {
            font-size: 12px;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white top-navbar">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold text-primary" href="#">跨境ERP系统</a>
            <div class="d-flex align-items-center">
                <nav aria-label="breadcrumb" class="me-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="#" class="text-decoration-none">工具管理</a></li>
                        <li class="breadcrumb-item"><a href="#" class="text-decoration-none">亚马逊</a></li>
                        <li class="breadcrumb-item active">备货期配置管理</li>
                    </ol>
                </nav>
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle fa-lg"></i> 管理员
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 左侧菜单栏 -->
    <div class="sidebar">
        <div class="nav flex-column nav-pills">
            <a class="nav-link" href="#"><i class="fas fa-tachometer-alt me-2"></i> 仪表盘</a>
            <a class="nav-link" href="#"><i class="fas fa-boxes me-2"></i> 商品管理</a>
            <a class="nav-link" href="#"><i class="fas fa-shopping-cart me-2"></i> 订单管理</a>
            <a class="nav-link" href="#"><i class="fas fa-warehouse me-2"></i> 仓储管理</a>
            <a class="nav-link" href="#"><i class="fas fa-tools me-2"></i> 工具管理</a>
            <a class="nav-link active" href="#" style="padding-left: 40px;"><i class="fab fa-amazon me-2"></i> 亚马逊备货期配置</a>
            <a class="nav-link" href="#"><i class="fas fa-chart-bar me-2"></i> 数据分析</a>
            <a class="nav-link" href="#"><i class="fas fa-cog me-2"></i> 系统设置</a>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 列表页面 -->
            <div id="listPage">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0"><i class="fab fa-amazon text-warning me-2"></i>亚马逊修改备货期配置管理</h3>
                    </div>
                </div>

                <!-- 查询表单 -->
                <div class="search-form">
                    <h5 class="mb-4"><i class="fas fa-search me-2"></i>查询条件</h5>
                    <form>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">规则名称</label>
                                <select class="form-select">
                                    <option value="">请选择规则名称</option>
                                    <option>春季促销备货期调整</option>
                                    <option>高价值商品备货期优化</option>
                                    <option>综合条件备货期管理</option>
                                    <option>夏季备货期策略</option>
                                    <option>黑五促销备货期调整</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">店铺</label>
                                <select class="form-select">
                                    <option value="">请选择店铺</option>
                                    <option><EMAIL></option>
                                    <option><EMAIL></option>
                                    <option><EMAIL></option>
                                    <option><EMAIL></option>
                                    <option><EMAIL></option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">站点</label>
                                <select class="form-select">
                                    <option value="">请选择站点</option>
                                    <option>US</option>
                                    <option>UK</option>
                                    <option>DE</option>
                                    <option>FR</option>
                                    <option>IT</option>
                                    <option>ES</option>
                                    <option>JP</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">启用状态</label>
                                <select class="form-select">
                                    <option value="">全部</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">调整方式</label>
                                <select class="form-select">
                                    <option value="">全部</option>
                                    <option value="1">价格区间</option>
                                    <option value="2">指定SKU</option>
                                    <option value="3">价格+重量+标签</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">优先级</label>
                                <select class="form-select">
                                    <option value="">请选择优先级</option>
                                    <option value="1">1 (最高)</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6-10</option>
                                    <option value="7">11-20</option>
                                    <option value="8">21以上</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">创建时间</label>
                                <select class="form-select">
                                    <option value="">请选择时间范围</option>
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="last3days">最近3天</option>
                                    <option value="last7days">最近7天</option>
                                    <option value="last30days">最近30天</option>
                                    <option value="thismonth">本月</option>
                                    <option value="lastmonth">上月</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">创建人</label>
                                <select class="form-select">
                                    <option value="">请选择创建人</option>
                                    <option>张三</option>
                                    <option>李四</option>
                                    <option>王五</option>
                                    <option>赵六</option>
                                    <option>孙七</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 功能按钮区域 -->
                <div class="function-buttons">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-muted">
                            <i class="fas fa-list me-2"></i>配置规则列表
                        </h6>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="showConfigPage()">
                                <i class="fas fa-plus me-1"></i>新增配置
                            </button>
                            <button type="button" class="btn btn-success">
                                <i class="fas fa-play me-1"></i>批量启用
                            </button>
                            <button type="button" class="btn btn-warning">
                                <i class="fas fa-pause me-1"></i>批量禁用
                            </button>
                            <button type="button" class="btn btn-info">
                                <i class="fas fa-download me-1"></i>导出配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="50"><input type="checkbox" class="form-check-input"></th>
                                    <th width="200">规则名称</th>
                                    <th width="220">店铺</th>
                                    <th width="120">调整方式</th>
                                    <th width="80">优先级</th>
                                    <th width="100">启用状态</th>
                                    <th width="160">创建时间</th>
                                    <th width="80">创建人</th>
                                    <th width="260">操作</th>
                                    <th width="160">最近执行</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox" class="form-check-input"></td>
                                    <td><strong>春季促销备货期调整</strong></td>
                                    <td><small><EMAIL></small></td>
                                    <td><span class="badge bg-info">价格区间</span></td>
                                    <td><span class="badge bg-primary">1</span></td>
                                    <td><span class="status-enabled"><i class="fas fa-check-circle"></i> 启用</span></td>
                                    <td><small>2024/12/12 10:30:15</small></td>
                                    <td>张三</td>
                                    <td>
                                        <div class="operation-buttons">
                                            <button class="btn btn-outline-primary btn-sm" onclick="editRule(1)" title="编辑">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" title="复制">
                                                <i class="fas fa-copy"></i> 复制
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" title="日志">
                                                <i class="fas fa-history"></i> 日志
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" title="删除">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                    <td><small>2024/12/12 13:42:16</small></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="form-check-input"></td>
                                    <td><strong>高价值商品备货期优化</strong></td>
                                    <td><small><EMAIL></small></td>
                                    <td><span class="badge bg-warning text-dark">指定SKU</span></td>
                                    <td><span class="badge bg-primary">2</span></td>
                                    <td><span class="status-disabled"><i class="fas fa-pause-circle"></i> 禁用</span></td>
                                    <td><small>2024/12/11 14:20:30</small></td>
                                    <td>李四</td>
                                    <td>
                                        <div class="operation-buttons">
                                            <button class="btn btn-outline-primary btn-sm" title="编辑">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" title="复制">
                                                <i class="fas fa-copy"></i> 复制
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" title="日志">
                                                <i class="fas fa-history"></i> 日志
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" title="删除">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                    <td><small class="text-muted">-</small></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="form-check-input"></td>
                                    <td><strong>综合条件备货期管理</strong></td>
                                    <td><small><EMAIL><br><EMAIL></small></td>
                                    <td><span class="badge bg-success">价格+重量+标签</span></td>
                                    <td><span class="badge bg-primary">3</span></td>
                                    <td><span class="status-enabled"><i class="fas fa-check-circle"></i> 启用</span></td>
                                    <td><small>2024/12/10 09:15:42</small></td>
                                    <td>王五</td>
                                    <td>
                                        <div class="operation-buttons">
                                            <button class="btn btn-outline-primary btn-sm" title="编辑">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" title="复制">
                                                <i class="fas fa-copy"></i> 复制
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" title="日志">
                                                <i class="fas fa-history"></i> 日志
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" title="删除">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                    <td><small>2024/12/12 08:30:21</small></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="d-flex justify-content-between align-items-center p-3 border-top">
                        <div>
                            <span class="text-muted">共 3 条记录，每页显示 50 条</span>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item disabled">
                                    <span class="page-link">上一页</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link">下一页</span>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 配置页面 -->
            <div id="configPage" style="display: none;">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0"><i class="fas fa-cog text-primary me-2"></i>备货期配置规则</h3>
                        <button type="button" class="btn btn-outline-secondary" onclick="showListPage()">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </button>
                    </div>
                </div>

                <form>
                    <!-- 基础配置 -->
                    <div class="config-section">
                        <h5><i class="fas fa-info-circle me-2"></i>基础配置</h5>
                        <div class="form-row">
                            <label class="form-label required">规则名称</label>
                            <input type="text" class="form-control" placeholder="请输入规则名称，最多50字符" maxlength="50">
                        </div>
                        <div class="form-row">
                            <label class="form-label required">店铺</label>
                            <select class="form-select" multiple required>
                                <option><EMAIL></option>
                                <option><EMAIL></option>
                                <option><EMAIL></option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label class="form-label">站点</label>
                            <select class="form-select" multiple>
                                <option>US</option>
                                <option>UK</option>
                                <option>DE</option>
                                <option>FR</option>
                                <option>IT</option>
                                <option>ES</option>
                                <option>JP</option>
                            </select>
                            <small class="form-text text-muted">不选择则应用所有站点</small>
                        </div>
                        <div class="form-row">
                            <label class="form-label required">优先级</label>
                            <input type="number" class="form-control" min="1" max="999" placeholder="数字越小优先级越高，范围1-999" required>
                        </div>
                    </div>

                    <!-- 筛选条件配置 -->
                    <div class="config-section">
                        <h5><i class="fas fa-filter me-2"></i>筛选条件配置</h5>
                        
                        <!-- 调整方式选择 -->
                        <div class="radio-group">
                            <label class="form-label required mb-3">修改备货期方式</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="adjustType" id="priceRange" value="1" onchange="showConditionPanel('price')">
                                        <label class="form-check-label" for="priceRange">
                                            <strong>按照价格区间修改</strong><br>
                                            <small class="text-muted">根据商品价格范围调整备货期</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="adjustType" id="specifySku" value="2" onchange="showConditionPanel('sku')">
                                        <label class="form-check-label" for="specifySku">
                                            <strong>指定SellerSKU修改</strong><br>
                                            <small class="text-muted">指定特定SKU列表调整备货期</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="adjustType" id="comprehensive" value="3" onchange="showConditionPanel('comprehensive')">
                                        <label class="form-check-label" for="comprehensive">
                                            <strong>按照价格+重量+标签修改</strong><br>
                                            <small class="text-muted">综合多个条件调整备货期</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 价格区间条件面板 -->
                        <div id="pricePanel" class="condition-panel">
                            <h6 class="mb-3"><i class="fas fa-dollar-sign me-2"></i>价格区间设置</h6>
                            <div class="form-row">
                                <label class="form-label required">最小价格</label>
                                <input type="number" class="form-control" step="0.01" placeholder="0.00">
                            </div>
                            <div class="form-row">
                                <label class="form-label required">最大价格</label>
                                <input type="number" class="form-control" step="0.01" placeholder="0.00">
                            </div>
                        </div>

                        <!-- SKU条件面板 -->
                        <div id="skuPanel" class="condition-panel">
                            <h6 class="mb-3"><i class="fas fa-barcode me-2"></i>SellerSKU设置</h6>
                            <div class="form-row">
                                <label class="form-label required">SellerSKU列表</label>
                                <textarea class="form-control" rows="5" placeholder="请输入SellerSKU，每行一个SKU&#10;例如：&#10;SKU001&#10;SKU002&#10;SKU003"></textarea>
                                <small class="form-text text-muted">支持批量粘贴，每行一个SKU</small>
                            </div>
                        </div>

                        <!-- 综合条件面板 -->
                        <div id="comprehensivePanel" class="condition-panel">
                            <h6 class="mb-3"><i class="fas fa-layer-group me-2"></i>综合条件设置</h6>
                            <div class="form-row">
                                <label class="form-label required">最小价格</label>
                                <input type="number" class="form-control" step="0.01" placeholder="0.00">
                            </div>
                            <div class="form-row">
                                <label class="form-label required">最大价格</label>
                                <input type="number" class="form-control" step="0.01" placeholder="0.00">
                            </div>
                            <div class="form-row">
                                <label class="form-label required">最小重量(kg)</label>
                                <input type="number" class="form-control" step="0.1" placeholder="0.0">
                            </div>
                            <div class="form-row">
                                <label class="form-label required">最大重量(kg)</label>
                                <input type="number" class="form-control" step="0.1" placeholder="0.0">
                            </div>
                            <div class="form-row">
                                <label class="form-label required">产品标签</label>
                                <select class="form-select" multiple>
                                    <option>电子产品</option>
                                    <option>时尚服饰</option>
                                    <option>家居用品</option>
                                    <option>运动户外</option>
                                    <option>美妆个护</option>
                                </select>
                            </div>
                        </div>

                        <!-- 附加筛选条件 -->
                        <div class="mt-4">
                            <h6 class="mb-3"><i class="fas fa-plus-circle me-2"></i>附加筛选条件</h6>
                            <div class="form-row">
                                <label class="form-label">ASIN状态</label>
                                <select class="form-select" multiple>
                                    <option>在线</option>
                                    <option>内容不完整</option>
                                    <option>不可售</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label class="form-label">销量类型</label>
                                <select class="form-select" onchange="toggleSalesRange(this)">
                                    <option value="">请选择</option>
                                    <option value="total">总销量</option>
                                    <option value="7days">7天</option>
                                    <option value="14days">14天</option>
                                    <option value="30days">30天</option>
                                    <option value="60days">60天</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label class="form-label">单品状态</label>
                                <select class="form-select" multiple>
                                    <option>正常</option>
                                    <option>停产</option>
                                    <option>存档</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label class="form-label required">最近X天不调整</label>
                                <input type="number" class="form-control" min="0" max="365" value="7" placeholder="0-365天">
                                <small class="form-text text-muted">0表示不启用此机制</small>
                            </div>
                            <div id="salesRangeRow" style="display: none;">
                                <div class="form-row">
                                    <label class="form-label">销量最小值</label>
                                    <input type="number" class="form-control" min="0" placeholder="0">
                                </div>
                                <div class="form-row">
                                    <label class="form-label">销量最大值</label>
                                    <input type="number" class="form-control" min="0" placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 备货期设置 -->
                    <div class="config-section">
                        <h5><i class="fas fa-calendar-alt me-2"></i>备货期设置</h5>
                        <div class="form-row">
                            <label class="form-label required">新备货期天数</label>
                            <input type="number" class="form-control" min="1" max="365" placeholder="1-365天" required>
                            <small class="form-text text-muted">调整后的备货期天数</small>
                        </div>
                    </div>

                    <!-- 执行策略配置 -->
                    <div class="config-section">
                        <h5><i class="fas fa-clock me-2"></i>执行策略配置</h5>
                        <div class="form-row">
                            <label class="form-label required">执行频率</label>
                            <select class="form-select" onchange="toggleExecuteDate(this)" required>
                                <option value="">请选择</option>
                                <option value="daily">每日执行</option>
                                <option value="weekly">每周执行</option>
                                <option value="monthly">每月执行</option>
                            </select>
                        </div>
                        <div class="form-row" id="executeDateDiv" style="display: none;">
                            <label class="form-label">执行日期</label>
                            <select class="form-select" id="executeDate">
                                <!-- 动态加载选项 -->
                            </select>
                        </div>
                        <div class="form-row">
                            <label class="form-label required">执行时间</label>
                            <input type="time" class="form-control" value="00:00" required>
                        </div>
                        <div class="form-row">
                            <label class="form-label">启用状态</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="enableStatus" checked>
                                <label class="form-check-label" for="enableStatus">启用</label>
                            </div>
                        </div>
                        <div class="form-row">
                            <label class="form-label required">开始时间</label>
                            <input type="datetime-local" class="form-control" required>
                        </div>
                        <div class="form-row">
                            <label class="form-label">结束时间</label>
                            <input type="datetime-local" class="form-control">
                            <small class="form-text text-muted">不选择表示永久有效</small>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="config-section">
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="showListPage()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面切换功能
        function showListPage() {
            document.getElementById('listPage').style.display = 'block';
            document.getElementById('configPage').style.display = 'none';
        }

        function showConfigPage() {
            document.getElementById('listPage').style.display = 'none';
            document.getElementById('configPage').style.display = 'block';
        }

        function editRule(id) {
            showConfigPage();
            // 这里可以加载具体规则数据进行编辑
        }

        // 筛选条件面板切换
        function showConditionPanel(type) {
            // 隐藏所有面板
            document.querySelectorAll('.condition-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // 显示对应面板
            if (type === 'price') {
                document.getElementById('pricePanel').classList.add('active');
            } else if (type === 'sku') {
                document.getElementById('skuPanel').classList.add('active');
            } else if (type === 'comprehensive') {
                document.getElementById('comprehensivePanel').classList.add('active');
            }
        }

        // 销量范围切换
        function toggleSalesRange(select) {
            const salesRangeRow = document.getElementById('salesRangeRow');
            if (select.value) {
                salesRangeRow.style.display = 'flex';
            } else {
                salesRangeRow.style.display = 'none';
            }
        }

        // 执行日期切换
        function toggleExecuteDate(select) {
            const executeDateDiv = document.getElementById('executeDateDiv');
            const executeDateSelect = document.getElementById('executeDate');
            
            if (select.value === 'daily') {
                executeDateDiv.style.display = 'none';
            } else if (select.value === 'weekly') {
                executeDateDiv.style.display = 'block';
                executeDateSelect.innerHTML = `
                    <option value="1">周一</option>
                    <option value="2">周二</option>
                    <option value="3">周三</option>
                    <option value="4">周四</option>
                    <option value="5">周五</option>
                    <option value="6">周六</option>
                    <option value="7">周日</option>
                `;
            } else if (select.value === 'monthly') {
                executeDateDiv.style.display = 'block';
                let options = '';
                for (let i = 1; i <= 31; i++) {
                    options += `<option value="${i}">${i}日</option>`;
                }
                executeDateSelect.innerHTML = options;
            } else {
                executeDateDiv.style.display = 'none';
            }
        }

        // 表格全选功能
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
        });
    </script>
</body>
</html> 