# 亚马逊备货期配置管理查询接口使用示例

## 1. 接口信息

### 1.1 基本信息
- **接口地址**：`POST /amazonLinkManagementConfig/queryByConditions`
- **请求方式**：POST
- **内容类型**：application/json
- **响应格式**：JSON

### 1.2 参数说明

#### 1.2.1 URL参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNum | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 20 | 每页记录数，最大200 |

#### 1.2.2 请求体参数（AmazonLinkManagementConfigQueryRequest）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ruleName | String | 否 | 规则名称，支持模糊查询 |
| accounts | List\<String\> | 否 | 店铺列表，多选 |
| sites | List\<String\> | 否 | 站点列表，如：["US","UK","DE"] |
| status | Integer | 否 | 启用状态：1-启用，0-禁用 |
| ruleType | Integer | 否 | 调整方式：1-价格区间，2-指定SKU，3-价格+重量+标签 |
| level | Integer | 否 | 优先级，精确查询 |
| createTimeStart | LocalDateTime | 否 | 创建时间范围开始 |
| createTimeEnd | LocalDateTime | 否 | 创建时间范围结束 |
| createdByList | List\<String\> | 否 | 创建人列表，多选 |

## 2. 请求示例

### 2.1 基础查询示例
```http
POST /amazonLinkManagementConfig/queryByConditions?pageNum=1&pageSize=20
Content-Type: application/json

{
  "ruleName": "促销",
  "status": 1,
  "ruleType": 1
}
```

### 2.2 复杂条件查询示例
```http
POST /amazonLinkManagementConfig/queryByConditions?pageNum=1&pageSize=50
Content-Type: application/json

{
  "ruleName": "春季备货期调整",
  "accounts": ["<EMAIL>", "<EMAIL>"],
  "sites": ["US", "UK", "DE"],
  "status": 1,
  "ruleType": 1,
  "level": 1,
  "createTimeStart": "2024-01-01T00:00:00",
  "createTimeEnd": "2024-12-31T23:59:59",
  "createdByList": ["张三", "李四"]
}
```

### 2.3 时间范围查询示例
```http
POST /amazonLinkManagementConfig/queryByConditions?pageNum=1&pageSize=20
Content-Type: application/json

{
  "createTimeStart": "2024-07-01T00:00:00",
  "createTimeEnd": "2024-07-31T23:59:59",
  "status": 1
}
```

## 3. 响应示例

### 3.1 成功响应
```json
{
  "success": true,
  "message": null,
  "total": 25,
  "totalPages": 2,
  "rows": [
    {
      "id": ***************,
      "type": 1,
      "ruleName": "春季促销备货期调整",
      "accountType": 1,
      "accountOption": "US,UK,DE",
      "accounts": "{\"account\":[\"<EMAIL>\",\"<EMAIL>\"]}",
      "level": 1,
      "ruleType": 1,
      "rule": "{\"newHandlingTime\":15,\"updateLimitDay\":7,\"priceRange\":{\"minPrice\":10.00,\"maxPrice\":100.00,\"currency\":\"USD\"}}",
      "status": 1,
      "exeFrequency": "day",
      "exeTime": "09:00",
      "strategyStartTime": "2024-03-20T19:17:50",
      "strategyEndTime": "2024-12-31T23:59:59",
      "lastExecuteTime": "2024-07-08T09:00:00",
      "createdBy": "张三",
      "updatedBy": "张三",
      "createdTime": "2024-03-20T19:17:50",
      "updatedTime": "2024-07-08T10:30:00"
    }
  ]
}
```

### 3.2 失败响应
```json
{
  "success": false,
  "message": "查询失败：数据库连接异常",
  "total": 0,
  "totalPages": 0,
  "rows": []
}
```

## 4. 查询逻辑说明

### 4.1 查询条件组合逻辑
- 所有查询条件采用 **AND** 逻辑组合
- 多选条件（如accounts、sites、createdByList）内部采用 **OR** 逻辑
- 空值或null条件将被忽略

### 4.2 权限过滤
- 查询结果会自动按用户权限范围过滤
- 用户只能查看有权限管理的店铺相关配置
- 权限控制在Service层实现

### 4.3 排序规则
- 默认按 `created_time` 字段倒序排列
- 避免使用主键排序（TiDB优化）

### 4.4 性能要求
- 普通查询响应时间 ≤ 2秒
- 复杂查询响应时间 ≤ 5秒
- 支持50个用户同时查询
- 单次查询结果不超过5000条

## 5. 使用注意事项

### 5.1 分页参数
- `pageNum` 从1开始，传入0或负数会被重置为1
- `pageSize` 最大值为200，超出会被限制
- 默认每页20条记录

### 5.2 时间格式
- 时间参数使用ISO 8601格式：`yyyy-MM-ddTHH:mm:ss`
- 时区默认为系统时区

### 5.3 模糊查询
- `ruleName` 支持模糊查询，会匹配包含关键字的规则名称
- 查询关键字会自动去除前后空格

### 5.4 权限控制
- 需要在Controller中集成实际的权限系统
- 设置用户当前信息和权限范围内的店铺列表

## 6. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数格式错误 | 检查请求参数格式和类型 |
| 500 | 服务器内部错误 | 查看服务器日志，联系技术支持 |
| 数据库连接异常 | 数据库连接失败 | 检查数据库连接配置 |
| 查询超时 | 查询执行时间过长 | 优化查询条件，减少查询范围 |

## 7. 前端集成示例

### 7.1 JavaScript/Ajax 示例
```javascript
// 查询请求示例
function queryConfigs(queryParams, pageNum = 1, pageSize = 20) {
    $.ajax({
        url: '/amazonLinkManagementConfig/queryByConditions',
        type: 'POST',
        data: {
            pageNum: pageNum,
            pageSize: pageSize
        },
        contentType: 'application/json',
        data: JSON.stringify(queryParams),
        success: function(response) {
            if (response.success) {
                // 处理成功响应
                console.log('查询成功，共' + response.total + '条记录');
                renderTable(response.rows);
                renderPagination(response.total, response.totalPages);
            } else {
                // 处理失败响应
                alert('查询失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('请求失败：' + error);
        }
    });
}

// 使用示例
const queryParams = {
    ruleName: '促销',
    status: 1,
    ruleType: 1,
    accounts: ['<EMAIL>'],
    sites: ['US', 'UK']
};

queryConfigs(queryParams, 1, 20);
```

### 7.2 Vue.js 示例
```javascript
// Vue组件中的查询方法
export default {
    data() {
        return {
            queryForm: {
                ruleName: '',
                accounts: [],
                sites: [],
                status: null,
                ruleType: null,
                level: null,
                createTimeStart: null,
                createTimeEnd: null,
                createdByList: []
            },
            tableData: [],
            total: 0,
            pageNum: 1,
            pageSize: 20
        }
    },
    methods: {
        async queryConfigs() {
            try {
                const response = await this.$http.post(
                    `/amazonLinkManagementConfig/queryByConditions?pageNum=${this.pageNum}&pageSize=${this.pageSize}`,
                    this.queryForm
                );
                
                if (response.data.success) {
                    this.tableData = response.data.rows;
                    this.total = response.data.total;
                } else {
                    this.$message.error('查询失败：' + response.data.message);
                }
            } catch (error) {
                this.$message.error('请求失败：' + error.message);
            }
        },
        
        handleSearch() {
            this.pageNum = 1; // 重置到第一页
            this.queryConfigs();
        },
        
        handlePageChange(page) {
            this.pageNum = page;
            this.queryConfigs();
        }
    }
}
```

---

**文档创建时间**：2024-07-08  
**最后更新时间**：2024-07-08 