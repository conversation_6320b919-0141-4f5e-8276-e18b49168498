## 1. 基本信息

### 1.1 项目信息

*   **项目名称**：亚马逊修改备货期配置管理系统
*   **项目编码**：AMAZON-LEADTIME-CONFIG-MGMT
*   **文档版本**：v1.0
*   **创建日期**：2024年
*   **更新日期**：2024年
*   **文档类型**：功能需求文档
*   **维护人员**：开发团队

### 1.2 功能概述

*   **功能名称**：亚马逊修改备货期配置管理
*   **功能描述**：通过配置化规则管理亚马逊店铺商品备货期的自动调整，支持多种筛选条件和执行策略，实现备货期的精准化和自动化管理
*   **业务场景**：运营人员需要根据不同的业务策略和商品特性，对亚马逊店铺的商品备货期进行批量调整，通过系统化配置提升运营效率，降低人工操作成本
*   **目标用户**：亚马逊运营人员、店铺管理员、超级管理员
*   **开发优先级**：高

### 1.3 功能价值

*   **自动化管理**：通过配置化规则实现备货期的自动调整，减少人工干预，提升运营效率
*   **精准匹配**：支持多维度筛选条件，确保备货期调整的精准性和针对性
*   **策略灵活**：支持多种调整策略和执行频率，满足不同业务场景需求
*   **风险控制**：通过优先级和防重复调整机制，避免策略冲突和频繁调整
*   **执行追踪**：完整的配置记录和执行日志，便于策略效果分析和问题排查
*   **权限管控**：基于角色的数据权限管理，确保配置和执行的安全性

## 2. 列表数据操作流程图
```mermaid
graph TD
    START[开始] --> A[创建配置规则]
    A --> B[配置基础信息]
    B --> C[选择调整方式]
    C --> D{调整方式判断}
    
    D -->|价格区间| E[配置价格区间]
    D -->|指定SKU| F[配置SellerSKU列表]
    D -->|价格+重量+标签| G[配置价格+重量+标签]
    
    E --> H[配置附加筛选条件]
    F --> H
    G --> H
    
    H --> I[配置ASIN状态]
    I --> J[配置销量区间]
    J --> K[配置单品状态]
    K --> L[配置防重复调整]
    
    L --> M[设置新备货期]
    M --> N[配置执行策略]
    N --> O[设置优先级]
    O --> P[保存配置]
    
    P --> Q[定时任务扫描]
    Q --> R{是否到执行时间?}
    R -->|否| Q
    R -->|是| S[获取启用的配置]
    
    S --> T[按优先级排序]
    T --> U[匹配店铺链接]
    U --> V{匹配成功?}
    V -->|否| END[结束]
    V -->|是| W[检查防重复条件]
    
    W --> X{是否满足条件?}
    X -->|否| END
    X -->|是| Y[执行备货期调整]
    
    Y --> Z[记录执行结果]
    Z --> END
```

## 3. 列表字段说明

### 3.1 配置规则字段说明

#### 3.1.1 基础信息字段

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 规则名称 | 文本 | 是 | 配置规则的唯一标识名称，用于区分不同的调整策略 | 春季促销备货期调整 |
| 店铺 | 多选列表 | 是 | 应用此规则的亚马逊店铺邮箱，支持多店铺选择 | <EMAIL>,<EMAIL> |
| 站点 | 多选列表 | 否 | 应用此规则的亚马逊站点，不选择则应用所有站点 | US,UK,DE |
| 优先级 | 数字 | 是 | 规则执行优先级，数字越小优先级越高，范围1-999 | 1 |

#### 3.1.2 筛选条件字段

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 调整方式 | 单选 | 是 | 备货期调整的筛选方式：1-价格区间，2-指定SKU，3-价格+重量+标签 | 1 |
| 价格区间最小值 | 数字 | 条件必填 | 价格筛选的最小值，币种根据站点自动确定 | 10.00 |
| 价格区间最大值 | 数字 | 条件必填 | 价格筛选的最大值，币种根据站点自动确定 | 100.00 |
| SellerSKU列表 | 文本列表 | 条件必填 | 指定要调整的SellerSKU，每行一个SKU | SKU001,SKU002,SKU003 |
| 重量区间最小值 | 数字 | 条件必填 | 重量筛选的最小值，单位kg | 0.5 |
| 重量区间最大值 | 数字 | 条件必填 | 重量筛选的最大值，单位kg | 5.0 |
| 产品标签 | 多选列表 | 条件必填 | 产品标签筛选，来自产品系统的标签库 | 电子产品,时尚服饰 |

#### 3.1.3 附加筛选字段

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| ASIN状态 | 多选列表 | 否 | 筛选特定状态的ASIN：在线、内容不完整、不可售 | 在线,内容不完整 |
| 销量类型 | 单选 | 否 | 销量统计周期：total,7days,14days,30days,60days | 30days |
| 销量区间最小值 | 数字 | 否 | 销量筛选的最小值 | 10 |
| 销量区间最大值 | 数字 | 否 | 销量筛选的最大值 | 100 |
| 单品状态 | 多选列表 | 否 | 筛选特定状态的产品：正常、停产、存档 | 正常,停产 |
| 最近X天不调整 | 数字 | 是 | 防重复调整的天数，0表示不启用此机制，范围0-365 | 7 |

#### 3.1.4 执行策略字段

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 新备货期天数 | 数字 | 是 | 调整后的备货期天数，范围1-365 | 15 |
| 执行频率 | 单选 | 是 | 规则执行频率：daily,weekly,monthly | daily |
| 执行日期 | 文本 | 条件必填 | 根据执行频率确定：每周1-7，每月1-31 | 1 |
| 执行时间 | 时间 | 是 | 每日执行的具体时间点 | 00:00:00 |
| 策略开始时间 | 日期时间 | 是 | 策略生效的开始时间 | 2024/03/20 19:17:50 |
| 策略结束时间 | 日期时间 | 否 | 策略生效的结束时间，空值表示永久有效 | 2024/12/31 23:59:59 |
| 启用状态 | 布尔值 | 是 | 规则的启用状态：0-禁用，1-启用 | 1 |

#### 3.1.5 系统管理字段

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 创建人 | 文本 | 系统填充 | 创建此配置规则的用户 | 张三 |
| 创建时间 | 日期时间 | 系统填充 | 规则创建时间 | 2024/03/20 19:17:50 |
| 更新人 | 文本 | 系统填充 | 最后更新此规则的用户 | 李四 |
| 更新时间 | 日期时间 | 系统填充 | 规则最后更新时间 | 2024/03/25 14:20:30 |
| 最近执行时间 | 日期时间 | 系统填充 | 规则最近一次执行时间 | 2024/03/27 13:42:16 |

### 3.2 任务执行记录字段说明

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 配置规则ID | 数字 | 系统填充 | 关联的配置规则标识 | 1 |
| 店铺账号 | 文本 | 系统填充 | 执行任务的店铺账号 | shop001 |
| 配置优先级 | 数字 | 系统填充 | 记录执行时的配置优先级 | 1 |
| 执行日期 | 日期 | 系统填充 | 任务执行的日期 | 2024-03-27 |
| 执行时间 | 日期时间 | 系统填充 | 具体的执行开始时间 | 2024/03/27 13:42:16 |
| 执行状态 | 枚举 | 系统填充 | 执行状态：0-待处理，1-执行中，2-执行成功，3-执行失败 | 2 |
| 创建时间 | 日期时间 | 系统填充 | 记录创建时间 | 2024/03/27 13:42:16 |
| 更新时间 | 日期时间 | 系统填充 | 记录最后更新时间 | 2024/03/27 13:45:30 |

### 3.3 执行日志字段说明

| 字段名 | 字段类型 | 必填 | 字段说明 | 示例值 |
| :-------- | :---------- | :------ | :------ | :------ |
| 配置规则ID | 数字 | 系统填充 | 关联的配置规则标识 | 1 |
| 店铺邮箱 | 文本 | 系统填充 | 执行调整的店铺邮箱 | <EMAIL> |
| SellerSKU | 文本 | 系统填充 | 被调整的商品SKU | SKU12345 |
| ASIN | 文本 | 系统填充 | 被调整的商品ASIN | B08XXXXXX |
| 原备货期 | 数字 | 系统填充 | 调整前的备货期天数 | 10 |
| 新备货期 | 数字 | 系统填充 | 调整后的备货期天数 | 15 |
| 执行状态 | 枚举 | 系统填充 | 执行结果：0-待处理，1-成功，2-失败 | 1 |
| 执行结果 | 文本 | 系统填充 | 执行结果的详细信息，失败时记录错误原因 | 调整成功 |
| 执行时间 | 日期时间 | 系统填充 | 具体的执行时间 | 2024/03/27 13:42:16 |

### 3.4 字段依赖关系说明

#### 3.4.1 调整方式字段依赖

-   **价格区间调整（调整方式=1）**：必须填写价格区间最小值和最大值
-   **指定SKU调整（调整方式=2）**：必须填写SellerSKU列表
-   **价格+重量+标签调整（调整方式=3）**：必须同时填写价格区间、重量区间、产品标签

#### 3.4.2 执行频率字段依赖

-   **每日执行（execute_frequency=daily）**：不需要填写执行日期
-   **每周执行（execute_frequency=weekly）**：执行日期为1-7（周一到周日）
-   **每月执行（execute_frequency=monthly）**：执行日期为1-31（每月的日期）

#### 3.4.3 销量筛选字段依赖

-   **销量区间筛选**：需要先选择销量类型，再设置销量区间最小值和最大值
-   **销量类型为空**：销量区间筛选不生效

### 3.5 字段验证规则

#### 3.5.1 必填字段验证

-   规则名称、店铺、调整方式、优先级、新备货期天数、执行频率、执行时间、策略开始时间为必填字段
-   根据调整方式的不同，相应的筛选条件字段为条件必填

#### 3.5.2 数据格式验证

-   **数字字段**：价格、重量、销量、天数等必须为正数
-   **时间字段**：时间格式必须正确，结束时间不能早于开始时间
-   **优先级字段**：范围1-999，同店铺不允许重复
-   **邮箱字段**：必须符合邮箱格式规范

#### 3.5.3 业务逻辑验证

-   **店铺权限**：只能选择用户有权限管理的店铺
-   **优先级唯一性**：同店铺不允许创建相同优先级的规则
-   **时间逻辑**：策略结束时间必须晚于开始时间
-   **筛选条件完整性**：根据调整方式验证相应筛选条件的完整性

## 4. 查询功能与说明

### 4.1 查询条件设计

| 查询项 | 控件类型 | 数据类型 | 必填 | 说明 |
| :-------- | :---------- | :---------- | :------ | :------ |
| 规则名称 | 文本输入框 | String | 否 | 支持模糊查询规则名称 |
| 店铺 | 多选下拉框 | String[] | 否 | 支持多店铺同时查询，选项根据用户权限动态加载 |
| 站点 | 多选下拉框 | String[] | 否 | 支持多站点同时查询，选项：US,UK,DE,FR,IT,ES,JP等 |
| 启用状态 | 单选下拉框 | String | 否 | 枚举值：全部、启用、禁用 |
| 调整方式 | 单选下拉框 | String | 否 | 枚举值：全部、价格区间、指定SKU、价格+重量+标签 |
| 优先级 | 数字输入框 | Integer | 否 | 支持精确查询优先级 |
| 创建时间 | 日期范围选择器 | Date Range | 否 | 支持开始时间和结束时间选择 |
| 创建人 | 多选下拉框 | String[] | 否 | 支持多人同时查询，选项为系统中所有用户 |

### 4.2 查询逻辑

*   **多条件组合**：所有查询条件采用AND逻辑组合
*   **权限过滤**：查询结果自动按用户权限范围过滤
*   **分页查询**：默认每页20条记录，支持分页导航
*   **排序规则**：默认按优先级正序，相同优先级按创建时间倒序排列

### 4.3 查询性能要求

*   **响应时间**：普通查询≤2秒，复杂查询≤5秒
*   **并发支持**：支持50个用户同时查询
*   **数据量限制**：单次查询结果不超过5000条

## 5. 列表说明和列表权限说明

### 5.1 列表字段设计

| 字段名 | 数据类型 | 宽度 | 对齐方式 | 说明 |
| :-------- | :---------- | :------ | :---------- | :------ |
| 勾选框 | Checkbox | 40px | 居中 | 支持单选和全选，用于批量操作 |
| 规则名称 | String | 200px | 左对齐 | 配置规则的名称 |
| 店铺 | String | 150px | 左对齐 | 关联的店铺邮箱，多个用逗号分隔 |
| 调整方式 | Enum | 120px | 居中 | 备货期调整方式 |
| 优先级 | Integer | 80px | 居中 | 规则执行优先级 |
| 启用状态 | Enum | 80px | 居中 | 带颜色标识的状态显示 |
| 创建时间 | DateTime | 150px | 居中 | 格式：yyyy/MM/dd HH:mm:ss |
| 创建人 | String | 100px | 居中 | 创建配置的用户 |
| 操作 | Action | 120px | 居中 | 编辑、复制、删除等操作按钮 |
| 最近执行 | DateTime | 150px | 居中 | 最近一次执行时间 |

### 5.2 状态显示规则

| 状态 | 颜色 | 图标 | 说明 |
| :------ | :------ | :------ | :------ |
| 启用 | #28a745 | ✅ | 绿色显示，规则正常运行 |
| 禁用 | #6c757d | ⏸️ | 灰色显示，规则暂停运行 |

### 5.3 调整方式显示规则

| 调整方式 | 显示文本 | 说明 |
| :---------- | :---------- | :------ |
| 1 | 价格区间 | 按照价格区间调整 |
| 2 | 指定SKU | 指定SellerSKU调整 |
| 3 | 价格+重量+标签 | 综合条件调整 |

### 5.4 操作按钮说明

*   **编辑**：跳转到配置页面，编辑当前规则
*   **复制**：复制当前规则创建新规则
*   **删除**：删除当前规则（软删除）
*   **查看执行日志**：查看规则的执行历史记录

### 5.5 列表权限说明

| 用户角色 | 可查看的数据范围 | 说明 |
| :---------- | :------------------ | :------ |
| **亚马逊运营 (Amazon Operator)** | 仅可查看自己店铺的配置 | 只能查看和管理自己负责店铺的备货期配置 |
| **店铺管理员 (Shop Manager)** | 可查看管理的店铺配置 | 可查看和管理授权店铺的备货期配置 |
| **运营主管 (Operation Supervisor)** | 可查看部门内所有配置 | 可查看和管理部门内所有店铺的备货期配置 |
| **超管 (Super Admin)** | 可查看全部配置 | 系统管理员，拥有所有配置的查看和管理权限 |

## 6. 配置页面功能说明

### 6.1 基础配置区域

| 配置项 | 控件类型 | 数据类型 | 必填 | 说明 |
| :-------- | :---------- | :---------- | :------ | :------ |
| 规则名称 | 文本输入框 | String | 是 | 配置规则的唯一标识名称，长度限制50位 |
| 店铺 | 多选下拉框 | String[] | 是 | 选择要应用此规则的店铺，选项根据权限动态加载 |
| 站点 | 多选下拉框 | String[] | 否 | 选择要应用的站点，默认全部站点 |
| 优先级 | 数字输入框 | Integer | 是 | 规则执行优先级，数字越小优先级越高，范围1-999 |

### 6.2 筛选条件配置区域

#### 6.2.1 调整方式选择

*   **价格区间调整**
    -   最小价格：数字输入框，支持小数点后2位
    -   最大价格：数字输入框，支持小数点后2位
    -   币种：自动根据店铺站点确定

*   **指定SellerSKU调整**
    -   SellerSKU列表：文本域，支持多行输入，每行一个SKU
    -   支持批量粘贴和导入

*   **价格+重量+标签调整**
    -   价格区间：同价格区间调整
    -   重量区间：最小重量和最大重量，单位kg
    -   产品标签：多选下拉框，选项来自产品系统的标签库

#### 6.2.2 附加筛选条件

| 筛选项 | 控件类型 | 选项 | 说明 |
| :-------- | :---------- | :------ | :------ |
| ASIN状态 | 多选下拉框 | 在线、内容不完整、不可售 | 筛选特定状态的ASIN |
| 销量区间 | 复合控件 | 总销量、7天、14天、30天、60天 | 选择销量统计周期和数量区间 |
| 单品状态 | 多选下拉框 | 正常、停产、存档 | 筛选特定状态的产品 |
| 最近X天不调整 | 数字输入框 | 0-365天 | 防止频繁调整的保护机制 |

### 6.3 备货期设置区域

*   **新备货期天数**：数字输入框，范围1-365天，必填项

### 6.4 执行策略配置区域

| 配置项 | 控件类型 | 选项/格式 | 说明 |
| :-------- | :---------- | :------ | :------ |
| 执行频率 | 单选下拉框 | 每日、每周、每月 | 规则执行的频率 |
| 执行日期 | 复合控件 | 根据频率动态显示 | 每周：周一到周日；每月：1-31日 |
| 执行时间 | 时间选择器 | HH:mm格式 | 具体执行时间点 |
| 开始时间 | 日期时间选择器 | yyyy/MM/dd HH:mm | 策略生效开始时间 |
| 结束时间 | 日期时间选择器 | yyyy/MM/dd HH:mm | 策略生效结束时间，可为空 |
| 启用状态 | 开关按钮 | 启用/禁用 | 规则的启用状态 |

## 7. 查询条件说明

| 查询项 | 说明 |
| :-------- | :------ |
| 规则名称 | 支持模糊查询，匹配规则名称中包含的关键字 |
| 店铺 | 下拉框，可多选，选项：根据用户权限显示可管理的店铺列表 |
| 站点 | 下拉框，可多选，选项：US、UK、DE、FR、IT、ES、JP等亚马逊站点 |
| 启用状态 | 下拉框，单选，选项：全部、启用、禁用 |
| 调整方式 | 下拉框，单选，选项：全部、价格区间、指定SKU、价格+重量+标签 |
| 优先级 | 数字输入框，支持精确查询指定优先级的规则 |
| 创建时间 | 按时间范围筛选，支持选择开始时间和结束时间 |
| 创建人 | 下拉框，可多选，选项：系统中所有用户 |

## 8. 列表字段说明

| 字段 | 说明 |
| :------ | :------ |
| 勾选框 | 支持单选和全选，用于批量启用、禁用等操作 |
| 规则名称 | 配置规则的唯一名称标识 |
| 店铺 | 规则应用的店铺邮箱，多个店铺用逗号分隔显示 |
| 调整方式 | 显示备货期调整的方式类型 |
| 优先级 | 规则执行优先级，数字越小优先级越高 |
| 启用状态 | 规则当前的启用状态，带颜色标识 |
| 创建时间 | 规则创建或最后更新时间 |
| 创建人 | 创建该规则的用户名 |
| 操作 | 编辑、复制、删除、查看日志等操作按钮 |
| 最近执行 | 规则最近一次执行的时间 |

## 9. 业务规则详细说明

### 9.1 配置规则优先级处理

*   **同店铺同链接多规则匹配**：按优先级数字正序执行，优先级1最高
*   **优先级冲突处理**：不允许同店铺创建相同优先级的规则
*   **规则互斥性**：一个链接同一时间只能被一个规则处理

### 9.2 筛选条件匹配逻辑

#### 9.2.1 价格区间调整

```
匹配条件：产品价格 >= 最小价格 AND 产品价格 <= 最大价格
匹配范围：指定店铺的所有在售商品
筛选逻辑：先按价格筛选，再应用附加筛选条件
```

#### 9.2.2 指定SellerSKU调整

```
匹配条件：SellerSKU IN (配置的SKU列表)
匹配范围：指定店铺的指定SKU商品
筛选逻辑：精确匹配SKU，再应用附加筛选条件
```

#### 9.2.3 价格+重量+标签调整

```
匹配条件：
1. 产品价格在指定区间
2. 产品重量在指定区间  
3. 产品标签包含指定标签
匹配范围：指定店铺的商品
筛选逻辑：三个条件必须同时满足，再应用附加筛选条件
```

### 9.3 附加筛选条件说明

*   **ASIN状态筛选**：只处理指定状态的ASIN
*   **销量区间筛选**：根据选择的时间周期和销量范围筛选
*   **单品状态筛选**：只处理指定状态的产品
*   **防重复调整**：检查最近X天内是否已调整过备货期

### 9.4 执行策略说明

#### 9.4.1 执行频率配置

*   **每日执行**：每天在指定时间执行一次
*   **每周执行**：每周在指定星期的指定时间执行
*   **每月执行**：每月在指定日期的指定时间执行

#### 9.4.2 执行时间窗口

*   **策略生效期**：在开始时间和结束时间范围内有效
*   **永久执行**：结束时间为空时，按频率永久执行
*   **过期自动禁用**：超过结束时间自动禁用规则

### 9.5 数据权限控制

*   **店铺权限**：用户只能配置有权限的店铺
*   **查看权限**：只能查看权限范围内的配置规则
*   **操作权限**：只能编辑、删除自己创建或有权限的规则

### 9.6 防重复调整机制

*   **时间窗口检查**：检查最近X天内是否调整过备货期
*   **记录追踪**：记录每次调整的时间和结果
*   **跳过处理**：已在时间窗口内调整的商品跳过本次处理

### 9.7 任务执行记录机制

#### 9.7.1 防重复执行控制

*   **单日单店铺限制**：每个店铺每天只能执行一次任务，通过`uk_account_date`唯一索引保证
*   **执行状态追踪**：记录任务的执行状态（待处理、执行中、执行成功、执行失败）
*   **执行时间记录**：记录任务的具体执行时间，便于追踪和分析

#### 9.7.2 优先级覆盖机制

*   **高优先级覆盖**：当同一店铺同一天有多个配置需要执行时，优先级更高（数字更小）的配置可以覆盖已有记录
*   **覆盖条件判断**：只有当新配置的优先级高于已记录的配置优先级时，才允许覆盖执行
*   **失败重试机制**：如果之前的任务执行失败（状态为3），允许重新执行

#### 9.7.3 执行逻辑流程

```
1. 定时任务触发
2. 查询当日是否已有执行记录
3. 如果没有记录 → 创建新记录并执行
4. 如果有记录：
   - 比较优先级：新配置优先级更高 → 覆盖记录并执行
   - 检查执行状态：之前执行失败 → 允许重新执行
   - 其他情况 → 跳过执行
5. 更新执行状态和结果
```

## 10. 执行日志管理

### 10.1 日志记录内容

*   **配置规则信息**：记录触发的配置规则ID和名称
*   **商品信息**：记录处理的店铺、SKU、ASIN等信息
*   **调整详情**：记录原备货期和新备货期
*   **执行结果**：记录成功或失败状态及详细信息
*   **执行时间**：记录具体的执行时间点

### 10.2 日志查询功能

*   **按配置规则查询**：查看指定规则的执行历史
*   **按店铺查询**：查看指定店铺的调整记录
*   **按时间范围查询**：查看指定时间段的执行情况
*   **按执行状态查询**：查看成功或失败的执行记录

## 11. 功能特性总结

### 11.1 核心功能

*   **配置化管理**：支持灵活的规则配置和管理
*   **多维度筛选**：支持价格、SKU、重量、标签等多种筛选方式
*   **智能调度**：支持多种执行频率和时间策略
*   **优先级控制**：支持规则优先级和冲突处理
*   **权限管控**：基于角色的细粒度权限管理
*   **执行追踪**：完整的执行日志和状态跟踪

### 11.2 技术特性

*   **响应式设计**：适配不同屏幕尺寸
*   **异步处理**：配置验证和执行采用异步处理
*   **数据校验**：完善的前端和后端数据校验机制
*   **错误处理**：友好的错误提示和异常处理
*   **性能优化**：分页查询、索引优化等性能保障措施

### 11.3 用户体验

*   **操作便捷**：分组布局和步骤化配置，降低使用难度
*   **信息清晰**：详细的字段说明和配置指引
*   **反馈及时**：实时的配置验证和状态反馈
*   **容错性强**：完善的数据校验和错误恢复机制
*   **扩展性好**：支持新增调整方式和筛选条件

### 11.4 业务价值

*   **效率提升**：自动化备货期调整，减少人工操作
*   **策略灵活**：支持多种业务场景和调整策略
*   **风险控制**：防重复调整和优先级控制机制
*   **数据驱动**：基于销量、价格等数据的智能调整
*   **可追溯性**：完整的操作记录和效果分析

## 12. 数据库表结构设计

### 12.1 主表DDL

```sql
-- Amazon链接管理配置表
-- 参考需求文档：亚马逊修改备货期配置管理综合需求文档.md
-- 参考表结构：amazon_offline_config

CREATE TABLE `amazon_link_management_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` int DEFAULT '1' COMMENT '配置类型：1-修改备货期',
  `rule_name` varchar(128) DEFAULT NULL COMMENT '规则名称，配置规则的唯一标识名称',
  `account_type` int DEFAULT NULL COMMENT '账户类型：1-指定账户，2-站点账户',
  `account_option` varchar(1024) DEFAULT NULL COMMENT '账户选项，存储站点信息如US,UK,DE等',
  `accounts` json DEFAULT NULL COMMENT '适用店铺账号JSON数组',
  `level` int NOT NULL COMMENT '优先级，数字越小优先级越高，范围1-999',
  `rule_type` int NOT NULL COMMENT '规则类型：1-价格区间，2-指定SKU，3-价格+重量+标签',
  `rule` json DEFAULT NULL COMMENT '规则配置JSON，包含筛选条件、执行策略、新备货期天数、防重复调整天数等',
  `status` int DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `exe_frequency` varchar(32) DEFAULT NULL COMMENT '执行频率：day-每日，week-每周，month-每月',
  `exe_time` varchar(255) DEFAULT NULL COMMENT '执行时间，格式如：09:00',
  `strategy_start_time` datetime DEFAULT NULL COMMENT '策略开始生效时间',
  `strategy_end_time` datetime DEFAULT NULL COMMENT '策略结束时间，NULL表示永久有效',
  `last_execute_time` datetime DEFAULT NULL COMMENT '最近执行时间',
  `created_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_type_status` (`account_type`, `status`),
  KEY `idx_level_status` (`level`, `status`),
  KEY `idx_exe_frequency_time` (`exe_frequency`, `exe_time`),
  KEY `idx_strategy_time` (`strategy_start_time`, `strategy_end_time`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_last_execute_time` (`last_execute_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Amazon链接管理配置表';
```

```sql
-- Amazon链接管理任务执行记录表

CREATE TABLE `amazon_link_management_task_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` int NOT NULL COMMENT '配置规则ID，关联amazon_link_management_config.id',
  `account_number` varchar(128) NOT NULL COMMENT '店铺账号',
  `config_level` int NOT NULL COMMENT '配置优先级，记录执行时的优先级',
  `execute_date` date NOT NULL COMMENT '执行日期，格式YYYY-MM-DD',
  `execute_time` datetime NOT NULL COMMENT '具体执行时间',
  `execute_status` int DEFAULT '0' COMMENT '执行状态：0-待处理，1-执行中，2-执行成功，3-执行失败',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_execute_date` (`execute_date`),
  KEY `idx_execute_status` (`execute_status`),
  KEY `idx_config_level` (`config_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Amazon链接管理任务执行记录表';

```
### 12.2 日志表说明

备货期配置日志使用现有表：`amazon_marketing_config_log`

**字段映射说明：**
- `marketing_id`: 关联amazon_link_management_config.id
- `type`: 3 (AmazonMarketingLogTypeEnum.LINK_MANAGE_HANDING_TIME)
- `operate_attr`: 操作属性，如 'handling_time', 'config_rule', 'status' 等
- `operate_attr_desc`: 操作属性描述，如 '备货期调整', '配置规则变更', '状态变更' 等
- `previous_value`: 调整前的值
- `after_value`: 调整后的值
- `operator`: 操作人员
- `operate_time`: 操作时间

### 12.3 JSON字段结构说明

#### 12.3.1 accounts字段结构
```json
{
  "account": [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
  ]
}
```

#### 12.3.2 rule字段结构
```json
{
  "newHandlingTime": 15,
  "updateLimitDay": 7,
  "priceRange": {
    "minPrice": 10.00,
    "maxPrice": 100.00,
    "currency": "USD"
  },
  "sellerSkuList": ["SKU001", "SKU002", "SKU003"],
  "weightRange": {
    "minWeight": 0.5,
    "maxWeight": 5.0,
    "unit": "kg"
  },
  "productTags": ["电子产品", "时尚服饰"],
  "asinStatus": ["Active", "Incomplete"],
  "salesRange": {
    "salesType": "order_last_30d_count",
    "minSales": 10,
    "maxSales": 100
  },
  "itemStatus": ["正常", "停产", "存档"],
  "excludeWords": "侵权词汇",
  "lastOpenDateExceeding": 30
}
```
