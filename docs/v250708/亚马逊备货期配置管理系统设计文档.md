# 亚马逊备货期配置管理系统设计文档

## 1. 文档信息

### 1.1 基本信息
- **文档名称**：亚马逊备货期配置管理系统设计文档
- **项目编码**：AMAZON-LEADTIME-CONFIG-MGMT
- **文档版本**：v1.0
- **创建日期**：2024-07-08
- **更新日期**：2024-07-08
- **文档类型**：系统设计文档
- **维护人员**：开发团队

### 1.2 文档目的
本文档旨在为亚马逊备货期配置管理系统提供完整的技术设计方案，包括系统架构、数据库设计、接口设计、技术实现等方面的详细说明。

### 1.3 适用范围
- 系统架构师
- 后端开发工程师
- 前端开发工程师
- 数据库管理员
- 测试工程师

## 2. 系统概述

### 2.1 系统目标
通过配置化规则管理亚马逊店铺商品备货期的自动调整，支持多种筛选条件和执行策略，实现备货期的精准化和自动化管理。

### 2.2 核心功能
- 配置化规则管理
- 多维度商品筛选
- 自动化备货期调整
- 任务执行记录与监控
- 权限管理与数据隔离

### 2.3 技术架构
- **后端框架**：Spring Boot + MyBatis Plus
- **数据库**：TiDB
- **缓存**：Redis
- **消息队列**：RabbitMQ
- **搜索引擎**：Elasticsearch
- **前端框架**：Vue.js + Element UI

## 3. 数据库设计

### 3.1 设计原则

#### 3.1.1 TiDB优化原则
- 使用AUTO_RANDOM主键避免写入热点
- 避免深分页，使用基于时间字段的范围查询
- 控制事务大小，避免大事务导致的性能问题
- 合理设计复合索引，减少回表操作
- 移除外键约束，通过应用层保证数据一致性

#### 3.1.2 业务设计原则
- 支持多租户数据隔离
- 记录完整的操作日志
- 支持软删除和数据恢复
- 保证数据的完整性和一致性

### 3.2 核心表结构

#### 3.2.1 配置规则表 (amazon_link_management_config)

**表功能说明：**
存储亚马逊备货期配置规则，支持多种调整方式和执行策略。

**DDL语句：**
```sql
-- Amazon链接管理配置表
-- 参考需求文档：亚马逊修改备货期配置管理综合需求文档.md
-- 参考表结构：amazon_offline_config
-- 基于TiDB最佳实践设计，使用AUTO_RANDOM避免写入热点

CREATE TABLE `amazon_link_management_config` (
  `id` bigint(15) NOT NULL /*T![auto_rand] AUTO_RANDOM(5, 54) */ COMMENT '主键ID',
  `type` int DEFAULT '1' COMMENT '配置类型：1-修改备货期',
  `rule_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则名称，配置规则的唯一标识名称',
  `account_type` int DEFAULT NULL COMMENT '账户类型：1-指定账户，2-站点账户',
  `account_option` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账户选项，存储站点信息如US,UK,DE等',
  `accounts` json DEFAULT NULL COMMENT '适用店铺账号JSON数组',
  `level` int NOT NULL COMMENT '优先级，数字越小优先级越高，范围1-999',
  `rule_type` int NOT NULL COMMENT '规则类型：1-价格区间，2-指定SKU，3-价格+重量+标签',
  `rule` json DEFAULT NULL COMMENT '规则配置JSON，包含筛选条件、执行策略、新备货期天数、防重复调整天数等',
  `status` int DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `exe_frequency` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行频率：day-每日，week-每周，month-每月',
  `exe_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行时间，格式如：09:00',
  `strategy_start_time` datetime DEFAULT NULL COMMENT '策略开始生效时间',
  `strategy_end_time` datetime DEFAULT NULL COMMENT '策略结束时间，NULL表示永久有效',
  `last_execute_time` datetime DEFAULT NULL COMMENT '最近执行时间',
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
  KEY `idx_account_type_status` (`account_type`, `status`),
  KEY `idx_level_status` (`level`, `status`),
  KEY `idx_exe_frequency_time` (`exe_frequency`, `exe_time`),
  KEY `idx_strategy_time` (`strategy_start_time`, `strategy_end_time`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_updated_time` (`updated_time`),
  KEY `idx_last_execute_time` (`last_execute_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon链接管理配置表';
```

**字段详细说明：**

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| id | bigint | 15 | 是 | AUTO_RANDOM | 主键ID，使用TiDB的AUTO_RANDOM避免写入热点 |
| type | int | - | 否 | 1 | 配置类型：1-修改备货期 |
| rule_name | varchar | 128 | 否 | NULL | 规则名称，配置规则的唯一标识名称 |
| account_type | int | - | 否 | NULL | 账户类型：1-指定账户，2-站点账户 |
| account_option | varchar | 1024 | 否 | NULL | 账户选项，存储站点信息如US,UK,DE等 |
| accounts | json | - | 否 | NULL | 适用店铺账号JSON数组 |
| level | int | - | 是 | - | 优先级，数字越小优先级越高，范围1-999 |
| rule_type | int | - | 是 | - | 规则类型：1-价格区间，2-指定SKU，3-价格+重量+标签 |
| rule | json | - | 否 | NULL | 规则配置JSON，包含筛选条件、执行策略等 |
| status | int | - | 否 | 1 | 启用状态：0-禁用，1-启用 |
| exe_frequency | varchar | 32 | 否 | NULL | 执行频率：day-每日，week-每周，month-每月 |
| exe_time | varchar | 255 | 否 | NULL | 执行时间，格式如：09:00 |
| strategy_start_time | datetime | - | 否 | NULL | 策略开始生效时间 |
| strategy_end_time | datetime | - | 否 | NULL | 策略结束时间，NULL表示永久有效 |
| last_execute_time | datetime | - | 否 | NULL | 最近执行时间 |
| created_by | varchar | 255 | 否 | NULL | 创建人 |
| updated_by | varchar | 255 | 否 | NULL | 更新人 |
| created_time | datetime | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | datetime | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

#### 3.2.2 任务执行记录表 (amazon_link_management_task_record)

**表功能说明：**
记录配置规则的执行记录，防止同一店铺同一天重复执行，支持优先级覆盖机制。

**DDL语句：**
```sql
-- Amazon链接管理任务执行记录表
-- 用于记录单个店铺的执行记录，防止同日重复执行，支持优先级覆盖
-- 基于TiDB最佳实践设计，移除外键约束，使用AUTO_RANDOM避免写入热点
CREATE TABLE `amazon_link_management_task_record` (
  `id` bigint(15) NOT NULL /*T![auto_rand] AUTO_RANDOM(5, 54) */ COMMENT '主键ID',
  `config_id` bigint(15) NOT NULL COMMENT '配置规则ID，关联amazon_link_management_config.id',
  `account_number` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺账号',
  `config_level` int NOT NULL COMMENT '配置优先级，记录执行时的优先级',
  `execute_date` date NOT NULL COMMENT '执行日期，格式YYYY-MM-DD',
  `execute_time` datetime NOT NULL COMMENT '具体执行时间',
  `execute_status` int DEFAULT '0' COMMENT '执行状态：0-待处理，1-执行中，2-执行成功，3-执行失败',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
  UNIQUE KEY `uk_account_date` (`account_number`, `execute_date`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_execute_date` (`execute_date`),
  KEY `idx_execute_status` (`execute_status`),
  KEY `idx_config_level` (`config_level`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_updated_time` (`updated_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon链接管理任务执行记录表';
```

**字段详细说明：**

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| id | bigint | 15 | 是 | AUTO_RANDOM | 主键ID |
| config_id | bigint | 15 | 是 | - | 配置规则ID，关联amazon_link_management_config.id |
| account_number | varchar | 128 | 是 | - | 店铺账号 |
| config_level | int | - | 是 | - | 配置优先级，记录执行时的优先级 |
| execute_date | date | - | 是 | - | 执行日期，格式YYYY-MM-DD |
| execute_time | datetime | - | 是 | - | 具体执行时间 |
| execute_status | int | - | 否 | 0 | 执行状态：0-待处理，1-执行中，2-执行成功，3-执行失败 |
| created_time | datetime | - | 否 | CURRENT_TIMESTAMP | 记录创建时间 |
| updated_time | datetime | - | 否 | CURRENT_TIMESTAMP | 记录更新时间 |

### 3.3 索引设计说明

#### 3.3.1 配置规则表索引
- **PRIMARY KEY (id)**：聚簇索引，TiDB主键
- **idx_account_type_status**：账户类型和状态复合索引，用于筛选启用的配置
- **idx_level_status**：优先级和状态复合索引，用于按优先级排序
- **idx_exe_frequency_time**：执行频率和时间复合索引，用于定时任务查询
- **idx_strategy_time**：策略时间复合索引，用于时间范围筛选
- **idx_created_time**：创建时间索引，用于列表排序
- **idx_updated_time**：更新时间索引，用于变更追踪
- **idx_last_execute_time**：最近执行时间索引，用于执行状态查询

#### 3.3.2 任务执行记录表索引
- **PRIMARY KEY (id)**：聚簇索引，TiDB主键
- **uk_account_date**：账号和日期唯一索引，防止重复执行
- **idx_config_id**：配置ID索引，用于关联查询
- **idx_execute_date**：执行日期索引，用于日期范围查询
- **idx_execute_status**：执行状态索引，用于状态筛选
- **idx_config_level**：配置优先级索引，用于优先级比较
- **idx_created_time**：创建时间索引，用于时间排序
- **idx_updated_time**：更新时间索引，用于变更追踪

### 3.4 JSON字段结构设计

#### 3.4.1 accounts字段结构
```json
{
  "account": [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
  ]
}
```

#### 3.4.2 rule字段结构
```json
{
  "newHandlingTime": 15,
  "updateLimitDay": 7,
  "priceRange": {
    "minPrice": 10.00,
    "maxPrice": 100.00,
    "currency": "USD"
  },
  "sellerSkuList": ["SKU001", "SKU002", "SKU003"],
  "weightRange": {
    "minWeight": 0.5,
    "maxWeight": 5.0,
    "unit": "kg"
  },
  "productTags": ["电子产品", "时尚服饰"],
  "asinStatus": ["Active", "Incomplete"],
  "salesRange": {
    "salesType": "order_last_30d_count",
    "minSales": 10,
    "maxSales": 100
  },
  "itemStatus": ["正常", "停产", "存档"],
  "excludeWords": "侵权词汇",
  "lastOpenDateExceeding": 30
}
```

### 3.5 日志表设计

#### 3.5.1 操作日志记录
备货期配置日志使用现有表：`amazon_marketing_config_log`

**字段映射说明：**
- `marketing_id`: 关联amazon_link_management_config.id
- `type`: 3 (AmazonMarketingLogTypeEnum.LINK_MANAGE_HANDING_TIME)
- `operate_attr`: 操作属性，如 'handling_time', 'config_rule', 'status' 等
- `operate_attr_desc`: 操作属性描述，如 '备货期调整', '配置规则变更', '状态变更' 等
- `previous_value`: 调整前的值
- `after_value`: 调整后的值
- `operator`: 操作人员
- `operate_time`: 操作时间

### 3.6 数据库性能优化策略

#### 3.6.1 TiDB特性优化
1. **AUTO_RANDOM主键**：避免写入热点，提升并发写入性能
2. **聚簇索引**：主键使用聚簇索引，减少回表操作
3. **复合索引设计**：根据查询模式设计复合索引，减少索引扫描
4. **移除外键约束**：通过应用层保证数据一致性，提升性能

#### 3.6.2 查询性能优化
1. **避免深分页**：使用基于时间字段的范围查询代替OFFSET分页
2. **索引覆盖查询**：尽量使用索引覆盖，减少回表操作
3. **批量操作优化**：控制批量操作的大小，避免大事务
4. **分区表设计**：考虑按时间分区，提升查询和维护性能

#### 3.6.3 写入性能优化
1. **事务大小控制**：每个事务处理记录数不超过300条
2. **批量插入**：使用批量插入代替循环单条插入
3. **异步处理**：非关键路径操作异步化处理
4. **连接池优化**：合理配置数据库连接池参数

### 3.7 数据一致性保证

#### 3.7.1 事务控制
1. **原子性保证**：关键业务操作使用事务保证原子性
2. **隔离级别**：使用READ-COMMITTED隔离级别平衡性能和一致性
3. **乐观锁**：使用版本号或时间戳实现乐观锁控制
4. **重试机制**：对于可重试的失败操作实现指数退避重试

#### 3.7.2 数据校验
1. **应用层校验**：在应用层实现外键约束和数据完整性校验
2. **唯一性约束**：通过数据库唯一索引保证数据唯一性
3. **数据格式校验**：JSON字段使用Schema验证数据格式
4. **业务规则校验**：在业务层实现复杂的业务规则校验

## 5. 接口设计

### 5.1 查询接口设计

#### 5.1.1 查询请求对象设计

**类名：** `AmazonLinkManagementConfigQueryRequest`

**功能说明：** 根据需求文档4.1查询条件设计实现的查询请求对象

**字段设计：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ruleName | String | 否 | 规则名称，支持模糊查询 |
| accounts | List\<String\> | 否 | 店铺列表，多选 |
| sites | List\<String\> | 否 | 站点列表，如：["US","UK","DE"] |
| status | Integer | 否 | 启用状态：1-启用，0-禁用 |
| ruleType | Integer | 否 | 调整方式：1-价格区间，2-指定SKU，3-价格+重量+标签 |
| level | Integer | 否 | 优先级，精确查询 |
| createTimeStart | LocalDateTime | 否 | 创建时间范围开始 |
| createTimeEnd | LocalDateTime | 否 | 创建时间范围结束 |
| createdByList | List\<String\> | 否 | 创建人列表，多选 |
| currentUser | String | 否 | 当前用户（系统内部使用） |
| userAuthorizedAccounts | List\<String\> | 否 | 用户权限范围内的店铺列表（系统内部使用） |

#### 5.1.2 查询接口规范

**接口地址：** `POST /amazonLinkManagementConfig/queryByConditions`

**请求参数：**
- **URL参数：**
  - `pageNum` (Integer, 可选, 默认1): 页码
  - `pageSize` (Integer, 可选, 默认20): 每页大小
- **请求体：** AmazonLinkManagementConfigQueryRequest对象

**响应格式：** CQueryResult\<AmazonLinkManagementConfig\>

### 5.2 查询逻辑实现

#### 5.2.1 查询条件构建逻辑

```java
/**
 * 构建查询条件的核心逻辑
 * 1. 规则名称 - 支持模糊查询
 * 2. 店铺权限过滤 - 查询结果自动按用户权限范围过滤  
 * 3. 指定店铺查询 - 多选店铺查询
 * 4. 站点查询 - 多选站点查询
 * 5. 启用状态查询 - 单选
 * 6. 调整方式查询 - 单选
 * 7. 优先级查询 - 精确查询
 * 8. 创建时间范围查询
 * 9. 创建人查询 - 多选
 * 排序规则：默认按创建时间倒序排列，避免使用主键排序（TiDB优化）
 */
```

#### 5.2.2 权限控制机制

- **数据权限过滤：** 用户只能查看权限范围内的配置规则
- **店铺权限控制：** 通过`userAuthorizedAccounts`字段实现店铺级权限过滤
- **权限实现层级：** Service层实现权限控制逻辑

#### 5.2.3 性能优化策略

- **分页限制：** 单页最大200条记录，避免大数据量查询
- **排序优化：** 使用`created_time`字段排序，避免主键排序（TiDB优化）
- **查询优化：** 基于索引设计的查询条件组合
- **响应时间要求：** 普通查询≤2秒，复杂查询≤5秒

### 5.3 Service层设计

#### 5.3.1 接口方法定义

```java
/**
 * 根据查询条件分页查询配置规则
 * 实现需求文档4.查询功能与说明中的查询逻辑
 * 
 * @param queryRequest 查询请求对象
 * @param pageNum 页码
 * @param pageSize 每页大小
 * @return 分页查询结果
 */
CQueryResult<AmazonLinkManagementConfig> queryPageByConditions(
        AmazonLinkManagementConfigQueryRequest queryRequest, 
        Integer pageNum, 
        Integer pageSize);
```

#### 5.3.2 实现特性

- **异常处理：** 完善的异常捕获和日志记录
- **参数验证：** 分页参数的合理性检查和默认值设置
- **查询构建：** 基于LambdaQueryWrapper的动态查询条件构建
- **结果封装：** 标准的CQueryResult响应格式

### 5.4 Controller层设计

#### 5.4.1 接口实现

```java
/**
 * 根据查询条件分页查询配置规则
 * 实现需求文档4.查询功能与说明中的查询逻辑
 */
@PostMapping("/queryByConditions")
public CQueryResult<AmazonLinkManagementConfig> queryByConditions(
        @RequestBody AmazonLinkManagementConfigQueryRequest queryRequest,
        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
        @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize)
```

#### 5.4.2 特性说明

- **日志记录：** 详细的请求参数和执行结果日志
- **权限集成：** 预留权限系统集成的TODO标记
- **异常处理：** 统一的异常处理和错误响应

## 6. 业务流程设计

### 6.1 查询流程

```mermaid
graph TD
    A[前端发起查询请求] --> B[Controller接收请求]
    B --> C[参数验证和默认值设置]
    C --> D[获取用户权限信息]
    D --> E[Service层查询逻辑]
    E --> F[构建查询条件]
    F --> G[执行数据库查询]
    G --> H[结果封装和返回]
    H --> I[前端渲染查询结果]
    
    F --> F1[规则名称模糊查询]
    F --> F2[权限过滤]
    F --> F3[多选条件处理]
    F --> F4[时间范围查询]
    F --> F5[排序和分页]
```

### 6.2 权限控制流程

```mermaid
graph TD
    A[用户发起查询] --> B[获取用户身份]
    B --> C[查询用户权限范围]
    C --> D[设置权限过滤条件]
    D --> E[执行带权限的查询]
    E --> F[返回权限范围内的数据]
```

## 7. 系统架构设计

### 7.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   网关层        │    │   后端服务层     │
│   Vue.js        │────│   Gateway       │────│   Spring Boot   │
│   Element UI    │    │   认证/授权     │    │   业务逻辑      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   缓存层        │    │   数据持久层     │
                       │   Redis         │    │   TiDB          │
                       │   分布式缓存    │    │   主数据库      │
                       └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   消息队列      │    │   搜索引擎      │
                       │   RabbitMQ      │    │   Elasticsearch │
                       │   异步处理      │    │   日志搜索      │
                       └─────────────────┘    └─────────────────┘
```

### 7.2 服务分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                        Controller 层                        │
│                     RESTful API 接口                       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        Service 层                          │
│                      业务逻辑处理                          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        Manager 层                          │
│                    第三方服务调用                          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                         DAO 层                             │
│                     数据访问对象                           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据库层                            │
│                      TiDB 数据库                           │
└─────────────────────────────────────────────────────────────┘
```

## 8. 后续扩展说明

本系统设计文档将根据项目进展持续更新，后续将补充以下内容：
- ~~接口设计规范~~ ✅ 已完成
- ~~业务流程设计~~ ✅ 已完成
- 安全设计方案
- 监控告警设计
- 部署架构设计
- 测试策略设计

## 9. 附录

### 9.1 相关文档链接
- [亚马逊修改备货期配置管理综合需求文档](../亚马逊修改备货期配置管理综合需求文档.md)
- [亚马逊备货期配置管理查询接口使用示例](../亚马逊备货期配置管理查询接口使用示例.md)

### 9.2 技术参考
- [TiDB最佳实践文档](../../TiDB-Migration-Guide.md)
- [项目开发规范](../../development-guidelines.mdc)
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis Plus官方文档](https://baomidou.com/)

---

**文档状态：** 第二版本（已补充接口设计和业务流程）  
**最后更新时间：** 2024-07-08  
**下次更新：** 根据开发进度和需求变更进行更新
