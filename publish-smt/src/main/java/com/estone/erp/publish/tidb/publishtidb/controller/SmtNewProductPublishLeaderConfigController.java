package com.estone.erp.publish.tidb.publishtidb.controller;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.model.AliexpressOperateLog;
import com.estone.erp.publish.smt.model.AliexpressOperateLogCriteria;
import com.estone.erp.publish.smt.service.AliexpressOperateLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductPublishLeaderConfig;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductPublishLeaderConfigService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 组长刊登次数配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@RestController
@RequestMapping("/smtNewProductPublishLeaderConfig")
public class SmtNewProductPublishLeaderConfigController {

    @Resource
    private SmtNewProductPublishLeaderConfigService smtNewProductPublishLeaderConfigService;
    @Resource
    private AliexpressOperateLogService aliexpressOperateLogService;

    /**
     * 获取组长刊登次数配置列表信息
     */
    @RequestMapping("list")
    public ApiResult<List<SmtNewProductPublishLeaderConfig>> list() {


        List<SmtNewProductPublishLeaderConfig> list = smtNewProductPublishLeaderConfigService.getList();
        return ApiResult.newSuccess(list);
    }

    /**
     * 更新配置信息
     */
    @RequestMapping("update")
    public ApiResult<String> update(@RequestBody List<SmtNewProductPublishLeaderConfig> leaderConfigList) {
        // 保存日志
        List<AliexpressOperateLog> aliexpressOperateLogs = new ArrayList<>();
        Map<Long, List<SmtNewProductPublishLeaderConfig>> configMap = smtNewProductPublishLeaderConfigService.list().stream().collect(Collectors.groupingBy(SmtNewProductPublishLeaderConfig::getId));
        leaderConfigList.forEach(leaderConfig -> {
            Long id = leaderConfig.getId();

            List<SmtNewProductPublishLeaderConfig> smtNewProductPublishLeaderConfigs = configMap.get(id);
            if (CollectionUtils.isNotEmpty(smtNewProductPublishLeaderConfigs)) {
                SmtNewProductPublishLeaderConfig smtNewProductPublishLeaderConfig = smtNewProductPublishLeaderConfigs.get(0);
                Integer dbPublishCount = smtNewProductPublishLeaderConfig.getPublishCount() == null ? 0 : smtNewProductPublishLeaderConfig.getPublishCount();
                Integer publishCount = leaderConfig.getPublishCount() == null ? 0 : leaderConfig.getPublishCount();
                if (dbPublishCount != publishCount) {
                    AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
                    aliexpressOperateLog.setType(OperateLogEnum.SALE_LEADER_PUBLISH_CONFIG.getCode());
                    aliexpressOperateLog.setFieldName(smtNewProductPublishLeaderConfig.getSaleLeader() + "- 刊登次数");
                    aliexpressOperateLog.setBefore(dbPublishCount.toString());
                    aliexpressOperateLog.setAfter(publishCount.toString());
                    aliexpressOperateLog.setMessage(smtNewProductPublishLeaderConfig.getId() + ",修改刊登次数 befor " + dbPublishCount + " after " + publishCount);
                    aliexpressOperateLog.setCreateBy(WebUtils.getUserName());
                    aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    aliexpressOperateLogs.add(aliexpressOperateLog);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(aliexpressOperateLogs)) {
            aliexpressOperateLogService.batchInsert(aliexpressOperateLogs);
        }

        // 更新配置
        smtNewProductPublishLeaderConfigService.updateBatchById(leaderConfigList);
        return ApiResult.newSuccess("更新成功");
    }

    /**
     * 查看日志
     */
    @RequestMapping("getConfig/logs/{offset}/{limit}")
    public ApiResult<?> getConfigLogs(@PathVariable(value = "offset", required = false) Integer offset, @PathVariable(value = "limit", required = false) Integer limit) {
        AliexpressOperateLogCriteria aliexpressOperateLogCriteria = new AliexpressOperateLogCriteria();
        aliexpressOperateLogCriteria.setType(OperateLogEnum.SALE_LEADER_PUBLISH_CONFIG.getCode());
        CQuery<AliexpressOperateLogCriteria> cquery = new CQuery<>();
        cquery.setLimit(limit);
        cquery.setPageReqired(true);
        cquery.setOffset(offset);
        cquery.setSearch(aliexpressOperateLogCriteria);
        cquery.setOrder("desc");
        cquery.setSort("id");
        return aliexpressOperateLogService.search(cquery);
    }

}