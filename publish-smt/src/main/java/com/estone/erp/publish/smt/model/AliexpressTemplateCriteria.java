package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> aliexpress_template
 * 2019-10-23 11:13:07
 */
@Data
public class AliexpressTemplateCriteria extends AliexpressTemplate {
    private static final long serialVersionUID = 1L;

    //产品自动刊登标识，如果设置true 就需要过滤掉 权限查询（模板创建人） 范本自动刊登
//    private Boolean autoPublishSign = false;

    private List<Integer> ids;

    //idStr
    private String idStr;

    //货号
    private String articleNumberStr;

    //标题
    private String likeSubject;

    //复制模板使用
    private Integer copyNum;

    private Date fromCreateDate;

    private Date toCreateDate;

    private Date fromUpdateDate;

    private Date toUpdateDate;

    private List<String> authSellerList = new ArrayList<>();

    private Integer notInTempStatus;

    private List<Integer> tempStatusList = new ArrayList<>();

    private List<Integer> categoryIdList = new ArrayList<>();

    //编辑模板 批量传入商品编码使用
    private List<String> skuCodeList = new ArrayList<>();

    /**
     * 单品状态
     */
    private List<String> skuStatusList = new ArrayList<>();

    /**
     *  推荐类目与模板类目不一致
     */
    private Boolean isDiffCategory;

    /**
     * 是否托管
     */
    private Boolean isTg;


    //自定义导出内容
    private List<String> downValueList;

    private List<Integer> idList;

    //问题分类
    private String problemType;

    private List<String> createByList;

    public AliexpressTemplateExample getExample() {
        AliexpressTemplateExample example = new AliexpressTemplateExample();
        AliexpressTemplateExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isNotBlank(this.getProblemType())){
            criteria.andProblemTypeEqualTo(this.getProblemType());
        }

        if (StringUtils.isNotBlank(this.getDraftId())) {
            criteria.andDraftIdEqualTo(this.getDraftId());
        }

        if (this.getPublishType() != null) {
            criteria.andPublishTypeEqualTo(this.getPublishType());
        }
        if (this.getPopTempId() != null) {
            criteria.andPopTempIdEqualTo(this.getPopTempId());
        }
        if (this.getHalfTempId() != null) {
            criteria.andHalfTempIdEqualTo(this.getHalfTempId());
        }

        if(StringUtils.isNotBlank(this.getExamineState())){
            criteria.andExamineStateEqualTo(this.getExamineState());
        }
        if(this.getPublishRole() != null){
            criteria.andPublishRoleEqualTo(this.getPublishRole());
        }

        if(CollectionUtils.isNotEmpty(authSellerList)){
            example.setAuthSellerList(authSellerList);
        }

        if(CollectionUtils.isNotEmpty(this.getTempStatusList())){
            criteria.andTemplateStatusIn(this.getTempStatusList());
        }

        if(StringUtils.isNotBlank(this.getAliexpressAccountNumber())){
            if(StringUtils.indexOf(this.getAliexpressAccountNumber(), ",") != -1){
                criteria.andAliexpressAccountNumberIn(CommonUtils.splitList(this.getAliexpressAccountNumber(), ","));
            }else{
                criteria.andAliexpressAccountNumberEqualTo(this.getAliexpressAccountNumber());
            }
        }

        //排除状态
        if(this.getNotInTempStatus() != null){
            criteria.andTemplateStatusNotEqualTo(this.getNotInTempStatus());
        }

        if(CollectionUtils.isNotEmpty(this.getIds())){
            criteria.andIdIn(this.getIds());
        }
        if(StringUtils.isNotBlank(this.getIdStr())){
            if(StringUtils.contains(this.getIdStr(), ",")){
                criteria.andIdIn(CommonUtils.splitIntList(this.getIdStr(), ","));
            }else{
                criteria.andIdEqualTo(Integer.valueOf(this.getIdStr()));
            }
        }

        if(StringUtils.isNotBlank(this.getArticleNumberStr())){
            if(StringUtils.contains(this.getArticleNumberStr(), ",")){
                criteria.andArticleNumberIn(CommonUtils.splitList(this.getArticleNumberStr(),","));
            }else{
                criteria.andArticleNumberEqualTo(this.getArticleNumberStr());
            }
        }

        if(StringUtils.isNotBlank(this.getLikeSubject())){
            criteria.andSubjectLike("%" + this.getLikeSubject() + "%");
        }

        if (this.getProductType() != null) {
            criteria.andProductTypeEqualTo(this.getProductType());
        }
        if (StringUtils.isNotBlank(this.getDetail())) {
            criteria.andDetailEqualTo(this.getDetail());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductSkusJson())) {
            criteria.andAeopAeProductSkusJsonEqualTo(this.getAeopAeProductSkusJson());
        }
        if (this.getDeliveryTime() != null) {
            criteria.andDeliveryTimeEqualTo(this.getDeliveryTime());
        }
        if (this.getPromiseTemplateId() != null) {
            criteria.andPromiseTemplateIdEqualTo(this.getPromiseTemplateId());
        }
        if (StringUtils.isNotBlank(this.getCategoryName())) {
            criteria.andCategoryNameEqualTo(this.getCategoryName());
        }
        if (this.getCategoryTableId() != null) {
            criteria.andCategoryTableIdEqualTo(this.getCategoryTableId());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if(CollectionUtils.isNotEmpty(this.getCategoryIdList())){
            criteria.andCategoryIdIn(this.getCategoryIdList());
        }

        if (StringUtils.isNotBlank(this.getSubject())) {
            criteria.andSubjectEqualTo(this.getSubject());
        }
        if (this.getProductPrice() != null) {
            criteria.andProductPriceEqualTo(this.getProductPrice());
        }
        if (this.getFreightTemplateId() != null) {
            criteria.andFreightTemplateIdEqualTo(this.getFreightTemplateId());
        }
        if (StringUtils.isNotBlank(this.getImageUrls())) {
            criteria.andImageUrlsEqualTo(this.getImageUrls());
        }
        if (this.getProductUnit() != null) {
            criteria.andProductUnitEqualTo(this.getProductUnit());
        }
        if (this.getPackageType() != null) {
            criteria.andPackageTypeEqualTo(this.getPackageType());
        }
        if (this.getLotNum() != null) {
            criteria.andLotNumEqualTo(this.getLotNum());
        }
        if (this.getPackageLength() != null) {
            criteria.andPackageLengthEqualTo(this.getPackageLength());
        }
        if (this.getPackageWidth() != null) {
            criteria.andPackageWidthEqualTo(this.getPackageWidth());
        }
        if (this.getPackageHeight() != null) {
            criteria.andPackageHeightEqualTo(this.getPackageHeight());
        }
        if (StringUtils.isNotBlank(this.getGrossWeight())) {
            criteria.andGrossWeightEqualTo(this.getGrossWeight());
        }
        if (this.getIsPackSell() != null) {
            criteria.andIsPackSellEqualTo(this.getIsPackSell());
        }
        if (this.getIsWholesale() != null) {
            criteria.andIsWholesaleEqualTo(this.getIsWholesale());
        }
        if (this.getBaseUnit() != null) {
            criteria.andBaseUnitEqualTo(this.getBaseUnit());
        }
        if (this.getAddUnit() != null) {
            criteria.andAddUnitEqualTo(this.getAddUnit());
        }
        if (StringUtils.isNotBlank(this.getAddWeight())) {
            criteria.andAddWeightEqualTo(this.getAddWeight());
        }
        if (this.getWsValidNum() != null) {
            criteria.andWsValidNumEqualTo(this.getWsValidNum());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductPropertysJson())) {
            criteria.andAeopAeProductPropertysJsonEqualTo(this.getAeopAeProductPropertysJson());
        }
        if (this.getBulkOrder() != null) {
            criteria.andBulkOrderEqualTo(this.getBulkOrder());
        }
        if (this.getBulkDiscount() != null) {
            criteria.andBulkDiscountEqualTo(this.getBulkDiscount());
        }
        if (this.getSizeChartId() != null) {
            criteria.andSizeChartIdEqualTo(this.getSizeChartId());
        }
        if (StringUtils.isNotBlank(this.getReduceStrategy())) {
            criteria.andReduceStrategyEqualTo(this.getReduceStrategy());
        }
        if (this.getGroupId() != null) {
            criteria.andGroupIdEqualTo(this.getGroupId());
        }
        if (StringUtils.isNotBlank(this.getCurrencyCode())) {
            criteria.andCurrencyCodeEqualTo(this.getCurrencyCode());
        }
        if (StringUtils.isNotBlank(this.getMobileDetail())) {
            criteria.andMobileDetailEqualTo(this.getMobileDetail());
        }
        if (this.getCouponStartDate() != null) {
            criteria.andCouponStartDateEqualTo(this.getCouponStartDate());
        }
        if (this.getCouponEndDate() != null) {
            criteria.andCouponEndDateEqualTo(this.getCouponEndDate());
        }
        if (StringUtils.isNotBlank(this.getAeopNationalQuoteConfiguration())) {
            criteria.andAeopNationalQuoteConfigurationEqualTo(this.getAeopNationalQuoteConfiguration());
        }
        if (StringUtils.isNotBlank(this.getAeopAeMultimedia())) {
            criteria.andAeopAeMultimediaEqualTo(this.getAeopAeMultimedia());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        //权限前端传值
        if (StringUtils.isNotBlank(this.getCreator())) {
            if(this.getCreator().indexOf(",") != -1){
                criteria.andCreatorIn(CommonUtils.splitList(this.getCreator(), ","));
            }else{
                criteria.andCreatorEqualTo(this.getCreator());
            }
        }

        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreatorIn(this.getCreateByList());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }

        if(this.getFromCreateDate() != null){
            criteria.andCreateTimeGreaterThanOrEqualTo(new Timestamp(this.getFromCreateDate().getTime()));
        }
        if(this.getToCreateDate() != null){
            criteria.andCreateTimeLessThanOrEqualTo(new Timestamp(this.getToCreateDate().getTime()));
        }

        if (this.getLastEditTime() != null) {
            criteria.andLastEditTimeEqualTo(this.getLastEditTime());
        }

        if(this.getFromUpdateDate() != null){
            criteria.andLastEditTimeGreaterThanOrEqualTo(new Timestamp(this.getFromUpdateDate().getTime()));
        }

        if(this.getToUpdateDate() != null){
            criteria.andLastEditTimeLessThanOrEqualTo(new Timestamp(this.getToUpdateDate().getTime()));
        }

        if (StringUtils.isNotBlank(this.getDisplayImageUrl())) {
            criteria.andDisplayImageUrlEqualTo(this.getDisplayImageUrl());
        }
        if (StringUtils.isNotBlank(this.getProductCode())) {
            criteria.andProductCodeEqualTo(this.getProductCode());
        }
        if (this.getProductStock() != null) {
            criteria.andProductStockEqualTo(this.getProductStock());
        }
        if (this.getPostTime() != null) {
            criteria.andPostTimeEqualTo(this.getPostTime());
        }
        if (this.getMargin() != null) {
            criteria.andMarginEqualTo(this.getMargin());
        }
        if (StringUtils.isNotBlank(this.getShippingMethodCode())) {
            criteria.andShippingMethodCodeEqualTo(this.getShippingMethodCode());
        }
        if (StringUtils.isNotBlank(this.getShareUser())) {
            criteria.andShareUserEqualTo(this.getShareUser());
        }
        if (this.getIsPublic() != null) {
            criteria.andIsPublicEqualTo(this.getIsPublic());
        }
        if (this.getIsParent() != null) {
            if(!this.getIsParent()) {
                String platform = SaleChannel.CHANNEL_SMT;
                // 人员权限
                Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, this.getCreator(),
                        this.getCreateByList(), this.getAliexpressAccountNumber(), null);
                if (BooleanUtils.isTrue(employeePair.getLeft())) {
                    criteria.andCreatorIn(employeePair.getRight());
                }
            }
            criteria.andIsParentEqualTo(this.getIsParent());
        }
        if (this.getTemplateStatus() != null) {
            criteria.andTemplateStatusEqualTo(this.getTemplateStatus());
        }
        if (this.getTemplateType() != null) {
            criteria.andTemplateTypeEqualTo(this.getTemplateType());
        }
        if (StringUtils.isNotBlank(this.getTemplateLabel())) {
            criteria.andTemplateLabelEqualTo(this.getTemplateLabel());
        }
        if (this.getParentId() != null){
            criteria.andParentIdEqualTo(this.getParentId());
        }

        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("create_time DESC,id DESC");
        }

        if (Boolean.TRUE.equals(this.getIsDiffCategory())) {
            criteria.andIsDiffCategory();
        }
        if (CollectionUtils.isNotEmpty(this.skuStatusList)) {
            criteria.andSkuStatusIn(this.skuStatusList);
        }
        if(CollectionUtils.isNotEmpty(this.getIdList())){
            criteria.andIdIn(this.getIdList());
        }
        if (StringUtils.isNotBlank(this.getTaxType())) {
            criteria.andTaxTypeEqualTo(this.getTaxType());
        }
        if (StringUtils.isNotBlank(this.getHacodeJson())) {
            criteria.andHacodeJsonEqualTo(this.getHacodeJson());
        }
        if(StringUtils.isBlank(this.getTable())) {
            this.initTable();
        }
        example.setTable(this.getTable());
        return example;
    }
}