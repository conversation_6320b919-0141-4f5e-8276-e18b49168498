package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublishLeader;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【aliexpress_new_product_publish_leader(Smt 刊登次数组长刊登情况)】的数据库操作Service
* @createDate 2025-03-13 09:40:29
*/
public interface AliexpressNewProductPublishLeaderService extends IService<AliexpressNewProductPublishLeader> {

    Map<Long, List<AliexpressNewProductPublishLeader>> getPublishLeaderByMainId(List<Long> mainIdList);
}
