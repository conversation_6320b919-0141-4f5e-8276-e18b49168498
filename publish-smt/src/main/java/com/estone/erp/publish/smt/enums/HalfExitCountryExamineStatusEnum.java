package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HalfExitCountryExamineStatusEnum {

    S_0(0, "审核中"),
    S_1(1, "审核通过"),
    S_2(2, "审核失败"),
    S_3(3, "手动清除"),
    ;

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static String convert(Integer value) {
        for (HalfExitCountryExamineStatusEnum status : HalfExitCountryExamineStatusEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

}
