package com.estone.erp.publish.tidb.publishtidb.model;



import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressEarlyBirdActivityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressEarlyBirdActivityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStoreIsNull() {
            addCriterion("store is null");
            return (Criteria) this;
        }

        public Criteria andStoreIsNotNull() {
            addCriterion("store is not null");
            return (Criteria) this;
        }

        public Criteria andStoreEqualTo(String value) {
            addCriterion("store =", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreNotEqualTo(String value) {
            addCriterion("store <>", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreGreaterThan(String value) {
            addCriterion("store >", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreGreaterThanOrEqualTo(String value) {
            addCriterion("store >=", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreLessThan(String value) {
            addCriterion("store <", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreLessThanOrEqualTo(String value) {
            addCriterion("store <=", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreLike(String value) {
            addCriterion("store like", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreNotLike(String value) {
            addCriterion("store not like", value, "store");
            return (Criteria) this;
        }

        public Criteria andStoreIn(List<String> values) {
            addCriterion("store in", values, "store");
            return (Criteria) this;
        }

        public Criteria andStoreNotIn(List<String> values) {
            addCriterion("store not in", values, "store");
            return (Criteria) this;
        }

        public Criteria andStoreBetween(String value1, String value2) {
            addCriterion("store between", value1, value2, "store");
            return (Criteria) this;
        }

        public Criteria andStoreNotBetween(String value1, String value2) {
            addCriterion("store not between", value1, value2, "store");
            return (Criteria) this;
        }

        public Criteria andPictureIsNull() {
            addCriterion("picture is null");
            return (Criteria) this;
        }

        public Criteria andPictureIsNotNull() {
            addCriterion("picture is not null");
            return (Criteria) this;
        }

        public Criteria andPictureEqualTo(String value) {
            addCriterion("picture =", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureNotEqualTo(String value) {
            addCriterion("picture <>", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureGreaterThan(String value) {
            addCriterion("picture >", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureGreaterThanOrEqualTo(String value) {
            addCriterion("picture >=", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureLessThan(String value) {
            addCriterion("picture <", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureLessThanOrEqualTo(String value) {
            addCriterion("picture <=", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureLike(String value) {
            addCriterion("picture like", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureNotLike(String value) {
            addCriterion("picture not like", value, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureIn(List<String> values) {
            addCriterion("picture in", values, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureNotIn(List<String> values) {
            addCriterion("picture not in", values, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureBetween(String value1, String value2) {
            addCriterion("picture between", value1, value2, "picture");
            return (Criteria) this;
        }

        public Criteria andPictureNotBetween(String value1, String value2) {
            addCriterion("picture not between", value1, value2, "picture");
            return (Criteria) this;
        }

        public Criteria andItemidIsNull() {
            addCriterion("itemid is null");
            return (Criteria) this;
        }

        public Criteria andItemidIsNotNull() {
            addCriterion("itemid is not null");
            return (Criteria) this;
        }

        public Criteria andItemidEqualTo(String value) {
            addCriterion("itemid =", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidNotEqualTo(String value) {
            addCriterion("itemid <>", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidGreaterThan(String value) {
            addCriterion("itemid >", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidGreaterThanOrEqualTo(String value) {
            addCriterion("itemid >=", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidLessThan(String value) {
            addCriterion("itemid <", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidLessThanOrEqualTo(String value) {
            addCriterion("itemid <=", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidLike(String value) {
            addCriterion("itemid like", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidNotLike(String value) {
            addCriterion("itemid not like", value, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidIn(List<String> values) {
            addCriterion("itemid in", values, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidNotIn(List<String> values) {
            addCriterion("itemid not in", values, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidBetween(String value1, String value2) {
            addCriterion("itemid between", value1, value2, "itemid");
            return (Criteria) this;
        }

        public Criteria andItemidNotBetween(String value1, String value2) {
            addCriterion("itemid not between", value1, value2, "itemid");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andSellerskuIsNull() {
            addCriterion("sellersku is null");
            return (Criteria) this;
        }

        public Criteria andSellerskuIsNotNull() {
            addCriterion("sellersku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerskuEqualTo(String value) {
            addCriterion("sellersku =", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuNotEqualTo(String value) {
            addCriterion("sellersku <>", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuGreaterThan(String value) {
            addCriterion("sellersku >", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuGreaterThanOrEqualTo(String value) {
            addCriterion("sellersku >=", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuLessThan(String value) {
            addCriterion("sellersku <", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuLessThanOrEqualTo(String value) {
            addCriterion("sellersku <=", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuLike(String value) {
            addCriterion("sellersku like", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuNotLike(String value) {
            addCriterion("sellersku not like", value, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuIn(List<String> values) {
            addCriterion("sellersku in", values, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuNotIn(List<String> values) {
            addCriterion("sellersku not in", values, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuBetween(String value1, String value2) {
            addCriterion("sellersku between", value1, value2, "sellersku");
            return (Criteria) this;
        }

        public Criteria andSellerskuNotBetween(String value1, String value2) {
            addCriterion("sellersku not between", value1, value2, "sellersku");
            return (Criteria) this;
        }

        public Criteria andBasePriceIsNull() {
            addCriterion("base_price is null");
            return (Criteria) this;
        }

        public Criteria andBasePriceIsNotNull() {
            addCriterion("base_price is not null");
            return (Criteria) this;
        }

        public Criteria andBasePriceEqualTo(String value) {
            addCriterion("base_price =", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceNotEqualTo(BigDecimal value) {
            addCriterion("base_price <>", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceGreaterThan(BigDecimal value) {
            addCriterion("base_price >", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price >=", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceLowLessThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price_low <=", value, "basePriceLow");
            return (Criteria) this;
        }

        public Criteria andBasePriceLowGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price_low >=", value, "basePriceLow");
            return (Criteria) this;
        }

        public Criteria andBasePriceHighGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price_high >=", value, "basePriceHigh");
            return (Criteria) this;
        }

        public Criteria andBasePriceHighLessThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price_high <=", value, "basePriceHigh");
            return (Criteria) this;
        }

        public Criteria andBasePriceHighLessThan(BigDecimal value) {
            addCriterion("base_price_high <", value, "basePriceHigh");
            return (Criteria) this;
        }

        public Criteria andBasePriceLessThan(BigDecimal value) {
            addCriterion("base_price <", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("base_price <=", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceIn(List<BigDecimal> values) {
            addCriterion("base_price in", values, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceNotIn(List<BigDecimal> values) {
            addCriterion("base_price not in", values, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceBetween(BigDecimal value1, BigDecimal value2) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("base_price_low between " + value1 +"and"+ "value2");
            str.append("or base_price_high between " + value1 +"and"+ value2);
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andBasePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("base_price not between", value1, value2, "basePrice");
            return (Criteria) this;
        }

        public Criteria andStockIsNull() {
            addCriterion("stock is null");
            return (Criteria) this;
        }

        public Criteria andStockIsNotNull() {
            addCriterion("stock is not null");
            return (Criteria) this;
        }

        public Criteria andStockEqualTo(Integer value) {
            addCriterion("stock =", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotEqualTo(Integer value) {
            addCriterion("stock <>", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThan(Integer value) {
            addCriterion("stock >", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock >=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThan(Integer value) {
            addCriterion("stock <", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThanOrEqualTo(Integer value) {
            addCriterion("stock <=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockIn(List<Integer> values) {
            addCriterion("stock in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotIn(List<Integer> values) {
            addCriterion("stock not in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockBetween(Integer value1, Integer value2) {
            addCriterion("stock between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotBetween(Integer value1, Integer value2) {
            addCriterion("stock not between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodIsNull() {
            addCriterion("custody_period is null");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodIsNotNull() {
            addCriterion("custody_period is not null");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodEqualTo(String value) {
            addCriterion("custody_period =", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodNotEqualTo(String value) {
            addCriterion("custody_period <>", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodGreaterThan(String value) {
            addCriterion("custody_period >", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("custody_period >=", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodLessThan(String value) {
            addCriterion("custody_period <", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodLessThanOrEqualTo(String value) {
            addCriterion("custody_period <=", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodLike(String value) {
            addCriterion("custody_period like", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodNotLike(String value) {
            addCriterion("custody_period not like", value, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodIn(List<String> values) {
            addCriterion("custody_period in", values, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodNotIn(List<String> values) {
            addCriterion("custody_period not in", values, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodBetween(String value1, String value2) {
            addCriterion("custody_period between", value1, value2, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andCustodyPeriodNotBetween(String value1, String value2) {
            addCriterion("custody_period not between", value1, value2, "custodyPeriod");
            return (Criteria) this;
        }

        public Criteria andActivityStatusIsNull() {
            addCriterion("activity_status is null");
            return (Criteria) this;
        }

        public Criteria andActivityStatusIsNotNull() {
            addCriterion("activity_status is not null");
            return (Criteria) this;
        }

        public Criteria andActivityStatusEqualTo(String value) {
            addCriterion("activity_status =", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusNotEqualTo(String value) {
            addCriterion("activity_status <>", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusGreaterThan(String value) {
            addCriterion("activity_status >", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusGreaterThanOrEqualTo(String value) {
            addCriterion("activity_status >=", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusLessThan(String value) {
            addCriterion("activity_status <", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusLessThanOrEqualTo(String value) {
            addCriterion("activity_status <=", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusLike(String value) {
            addCriterion("activity_status like", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusNotLike(String value) {
            addCriterion("activity_status not like", value, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusIn(List<String> values) {
            addCriterion("activity_status in", values, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusNotIn(List<String> values) {
            addCriterion("activity_status not in", values, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusBetween(String value1, String value2) {
            addCriterion("activity_status between", value1, value2, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andActivityStatusNotBetween(String value1, String value2) {
            addCriterion("activity_status not between", value1, value2, "activityStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusIsNull() {
            addCriterion("join_status is null");
            return (Criteria) this;
        }

        public Criteria andJoinStatusIsNotNull() {
            addCriterion("join_status is not null");
            return (Criteria) this;
        }

        public Criteria andJoinStatusEqualTo(String value) {
            addCriterion("join_status =", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusNotEqualTo(String value) {
            addCriterion("join_status <>", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusGreaterThan(String value) {
            addCriterion("join_status >", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusGreaterThanOrEqualTo(String value) {
            addCriterion("join_status >=", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusLessThan(String value) {
            addCriterion("join_status <", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusLessThanOrEqualTo(String value) {
            addCriterion("join_status <=", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusLike(String value) {
            addCriterion("join_status like", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusNotLike(String value) {
            addCriterion("join_status not like", value, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusIn(List<String> values) {
            addCriterion("join_status in", values, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusNotIn(List<String> values) {
            addCriterion("join_status not in", values, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusBetween(String value1, String value2) {
            addCriterion("join_status between", value1, value2, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andJoinStatusNotBetween(String value1, String value2) {
            addCriterion("join_status not between", value1, value2, "joinStatus");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andSpuIsNull() {
            addCriterion("spu is null");
            return (Criteria) this;
        }

        public Criteria andSpuIsNotNull() {
            addCriterion("spu is not null");
            return (Criteria) this;
        }

        public Criteria andSpuEqualTo(String value) {
            addCriterion("spu =", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotEqualTo(String value) {
            addCriterion("spu <>", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThan(String value) {
            addCriterion("spu >", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThanOrEqualTo(String value) {
            addCriterion("spu >=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThan(String value) {
            addCriterion("spu <", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThanOrEqualTo(String value) {
            addCriterion("spu <=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLike(String value) {
            addCriterion("spu like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotLike(String value) {
            addCriterion("spu not like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuIn(List<String> values) {
            addCriterion("spu in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotIn(List<String> values) {
            addCriterion("spu not in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuBetween(String value1, String value2) {
            addCriterion("spu between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotBetween(String value1, String value2) {
            addCriterion("spu not between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andAvailableStockIsNull() {
            addCriterion("available_stock is null");
            return (Criteria) this;
        }

        public Criteria andAvailableStockIsNotNull() {
            addCriterion("available_stock is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableStockEqualTo(Integer value) {
            addCriterion("available_stock =", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockNotEqualTo(Integer value) {
            addCriterion("available_stock <>", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockGreaterThan(Integer value) {
            addCriterion("available_stock >", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("available_stock >=", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockLessThan(Integer value) {
            addCriterion("available_stock <", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockLessThanOrEqualTo(Integer value) {
            addCriterion("available_stock <=", value, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockIn(List<Integer> values) {
            addCriterion("available_stock in", values, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockNotIn(List<Integer> values) {
            addCriterion("available_stock not in", values, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockBetween(Integer value1, Integer value2) {
            addCriterion("available_stock between", value1, value2, "availableStock");
            return (Criteria) this;
        }

        public Criteria andAvailableStockNotBetween(Integer value1, Integer value2) {
            addCriterion("available_stock not between", value1, value2, "availableStock");
            return (Criteria) this;
        }

        public Criteria andListingTimeIsNull() {
            addCriterion("listing_time is null");
            return (Criteria) this;
        }

        public Criteria andListingTimeIsNotNull() {
            addCriterion("listing_time is not null");
            return (Criteria) this;
        }

        public Criteria andListingTimeEqualTo(Timestamp value) {
            addCriterion("listing_time =", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeNotEqualTo(Timestamp value) {
            addCriterion("listing_time <>", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeGreaterThan(Timestamp value) {
            addCriterion("listing_time >", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("listing_time >=", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeLessThan(Timestamp value) {
            addCriterion("listing_time <", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("listing_time <=", value, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeIn(List<Timestamp> values) {
            addCriterion("listing_time in", values, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeNotIn(List<Timestamp> values) {
            addCriterion("listing_time not in", values, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("listing_time between", value1, value2, "listingTime");
            return (Criteria) this;
        }

        public Criteria andListingTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("listing_time not between", value1, value2, "listingTime");
            return (Criteria) this;
        }

        public Criteria andSellerIsNull() {
            addCriterion("seller is null");
            return (Criteria) this;
        }

        public Criteria andSellerIsNotNull() {
            addCriterion("seller is not null");
            return (Criteria) this;
        }

        public Criteria andSellerEqualTo(String value) {
            addCriterion("seller =", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerNotEqualTo(String value) {
            addCriterion("seller <>", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerGreaterThan(String value) {
            addCriterion("seller >", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerGreaterThanOrEqualTo(String value) {
            addCriterion("seller >=", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerLessThan(String value) {
            addCriterion("seller <", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerLessThanOrEqualTo(String value) {
            addCriterion("seller <=", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerLike(String value) {
            addCriterion("seller like", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerNotLike(String value) {
            addCriterion("seller not like", value, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerIn(List<String> values) {
            addCriterion("seller in", values, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerNotIn(List<String> values) {
            addCriterion("seller not in", values, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerBetween(String value1, String value2) {
            addCriterion("seller between", value1, value2, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerNotBetween(String value1, String value2) {
            addCriterion("seller not between", value1, value2, "seller");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderIsNull() {
            addCriterion("seller_leader is null");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderIsNotNull() {
            addCriterion("seller_leader is not null");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderEqualTo(String value) {
            addCriterion("seller_leader =", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderNotEqualTo(String value) {
            addCriterion("seller_leader <>", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderGreaterThan(String value) {
            addCriterion("seller_leader >", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderGreaterThanOrEqualTo(String value) {
            addCriterion("seller_leader >=", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderLessThan(String value) {
            addCriterion("seller_leader <", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderLessThanOrEqualTo(String value) {
            addCriterion("seller_leader <=", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderLike(String value) {
            addCriterion("seller_leader like", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderNotLike(String value) {
            addCriterion("seller_leader not like", value, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderIn(List<String> values) {
            addCriterion("seller_leader in", values, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderNotIn(List<String> values) {
            addCriterion("seller_leader not in", values, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderBetween(String value1, String value2) {
            addCriterion("seller_leader between", value1, value2, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerLeaderNotBetween(String value1, String value2) {
            addCriterion("seller_leader not between", value1, value2, "sellerLeader");
            return (Criteria) this;
        }

        public Criteria andSellerManagerIsNull() {
            addCriterion("seller_manager is null");
            return (Criteria) this;
        }

        public Criteria andSellerManagerIsNotNull() {
            addCriterion("seller_manager is not null");
            return (Criteria) this;
        }

        public Criteria andSellerManagerEqualTo(String value) {
            addCriterion("seller_manager =", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerNotEqualTo(String value) {
            addCriterion("seller_manager <>", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerGreaterThan(String value) {
            addCriterion("seller_manager >", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerGreaterThanOrEqualTo(String value) {
            addCriterion("seller_manager >=", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerLessThan(String value) {
            addCriterion("seller_manager <", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerLessThanOrEqualTo(String value) {
            addCriterion("seller_manager <=", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerLike(String value) {
            addCriterion("seller_manager like", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerNotLike(String value) {
            addCriterion("seller_manager not like", value, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerIn(List<String> values) {
            addCriterion("seller_manager in", values, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerNotIn(List<String> values) {
            addCriterion("seller_manager not in", values, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerBetween(String value1, String value2) {
            addCriterion("seller_manager between", value1, value2, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andSellerManagerNotBetween(String value1, String value2) {
            addCriterion("seller_manager not between", value1, value2, "sellerManager");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNull() {
            addCriterion("order_last_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNotNull() {
            addCriterion("order_last_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountEqualTo(Integer value) {
            addCriterion("order_last_7d_count =", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotEqualTo(Integer value) {
            addCriterion("order_last_7d_count <>", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThan(Integer value) {
            addCriterion("order_last_7d_count >", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count >=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThan(Integer value) {
            addCriterion("order_last_7d_count <", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count <=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIn(List<Integer> values) {
            addCriterion("order_last_7d_count in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotIn(List<Integer> values) {
            addCriterion("order_last_7d_count not in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count not between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNull() {
            addCriterion("order_last_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNotNull() {
            addCriterion("order_last_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountEqualTo(Integer value) {
            addCriterion("order_last_14d_count =", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotEqualTo(Integer value) {
            addCriterion("order_last_14d_count <>", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThan(Integer value) {
            addCriterion("order_last_14d_count >", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count >=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThan(Integer value) {
            addCriterion("order_last_14d_count <", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count <=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIn(List<Integer> values) {
            addCriterion("order_last_14d_count in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotIn(List<Integer> values) {
            addCriterion("order_last_14d_count not in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count not between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNull() {
            addCriterion("order_last_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNotNull() {
            addCriterion("order_last_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountEqualTo(Integer value) {
            addCriterion("order_last_30d_count =", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotEqualTo(Integer value) {
            addCriterion("order_last_30d_count <>", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThan(Integer value) {
            addCriterion("order_last_30d_count >", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count >=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThan(Integer value) {
            addCriterion("order_last_30d_count <", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count <=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIn(List<Integer> values) {
            addCriterion("order_last_30d_count in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotIn(List<Integer> values) {
            addCriterion("order_last_30d_count not in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count not between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIsNull() {
            addCriterion("order_last_60d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIsNotNull() {
            addCriterion("order_last_60d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountEqualTo(Integer value) {
            addCriterion("order_last_60d_count =", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotEqualTo(Integer value) {
            addCriterion("order_last_60d_count <>", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountGreaterThan(Integer value) {
            addCriterion("order_last_60d_count >", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_60d_count >=", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountLessThan(Integer value) {
            addCriterion("order_last_60d_count <", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_60d_count <=", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIn(List<Integer> values) {
            addCriterion("order_last_60d_count in", values, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotIn(List<Integer> values) {
            addCriterion("order_last_60d_count not in", values, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_60d_count between", value1, value2, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_60d_count not between", value1, value2, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeIsNull() {
            addCriterion("crawling_time is null");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeIsNotNull() {
            addCriterion("crawling_time is not null");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeEqualTo(Timestamp value) {
            addCriterion("crawling_time =", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeNotEqualTo(Timestamp value) {
            addCriterion("crawling_time <>", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeGreaterThan(Timestamp value) {
            addCriterion("crawling_time >", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("crawling_time >=", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeLessThan(Timestamp value) {
            addCriterion("crawling_time <", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("crawling_time <=", value, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeIn(List<Timestamp> values) {
            addCriterion("crawling_time in", values, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeNotIn(List<Timestamp> values) {
            addCriterion("crawling_time not in", values, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawling_time between", value1, value2, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andCrawlingTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawling_time not between", value1, value2, "crawlingTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeIsNull() {
            addCriterion("syn_time is null");
            return (Criteria) this;
        }

        public Criteria andSynTimeIsNotNull() {
            addCriterion("syn_time is not null");
            return (Criteria) this;
        }

        public Criteria andSynTimeEqualTo(Timestamp value) {
            addCriterion("syn_time =", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeNotEqualTo(Timestamp value) {
            addCriterion("syn_time <>", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeGreaterThan(Timestamp value) {
            addCriterion("syn_time >", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("syn_time >=", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeLessThan(Timestamp value) {
            addCriterion("syn_time <", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("syn_time <=", value, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeIn(List<Timestamp> values) {
            addCriterion("syn_time in", values, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeNotIn(List<Timestamp> values) {
            addCriterion("syn_time not in", values, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("syn_time between", value1, value2, "synTime");
            return (Criteria) this;
        }

        public Criteria andSynTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("syn_time not between", value1, value2, "synTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeIsNull() {
            addCriterion("confirm_join_time is null");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeIsNotNull() {
            addCriterion("confirm_join_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeEqualTo(Timestamp value) {
            addCriterion("confirm_join_time =", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeNotEqualTo(Timestamp value) {
            addCriterion("confirm_join_time <>", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeGreaterThan(Timestamp value) {
            addCriterion("confirm_join_time >", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("confirm_join_time >=", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeLessThan(Timestamp value) {
            addCriterion("confirm_join_time <", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("confirm_join_time <=", value, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeIn(List<Timestamp> values) {
            addCriterion("confirm_join_time in", values, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeNotIn(List<Timestamp> values) {
            addCriterion("confirm_join_time not in", values, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("confirm_join_time between", value1, value2, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andConfirmJoinTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("confirm_join_time not between", value1, value2, "confirmJoinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeIsNull() {
            addCriterion("join_time is null");
            return (Criteria) this;
        }

        public Criteria andJoinTimeIsNotNull() {
            addCriterion("join_time is not null");
            return (Criteria) this;
        }

        public Criteria andJoinTimeEqualTo(Timestamp value) {
            addCriterion("join_time =", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeNotEqualTo(Timestamp value) {
            addCriterion("join_time <>", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeGreaterThan(Timestamp value) {
            addCriterion("join_time >", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("join_time >=", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeLessThan(Timestamp value) {
            addCriterion("join_time <", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("join_time <=", value, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeIn(List<Timestamp> values) {
            addCriterion("join_time in", values, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeNotIn(List<Timestamp> values) {
            addCriterion("join_time not in", values, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("join_time between", value1, value2, "joinTime");
            return (Criteria) this;
        }

        public Criteria andJoinTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("join_time not between", value1, value2, "joinTime");
            return (Criteria) this;
        }


        public Criteria andListingTimeDateEqualTo(Timestamp time) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("date(listing_time) = date("+time+")");
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andCrawlingTimeDateEqualTo(Timestamp time) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("date(crawling_time) = date("+time+")");
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andSynTimeDateEqualTo(Timestamp time) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("date(syn_time) = date("+time+")");
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andConfirmJoinTimeDateEqualTo(Timestamp time) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("date(confirm_join_time) = date("+time+")");
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andJoinTimeDateEqualTo(Timestamp time) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            str.append("date(join_time) = date("+time+")");
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andSellersLike(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            for (int i = 0; i < values.size(); i++) {
                if(i==0){
                    str.append("seller like '%" + values.get(i) +  "%'");
                }else{
                    str.append("or seller like '%" + values.get(i) +  "%'");
                }
            }
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andSellersLeadersLike(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            for (int i = 0; i < values.size(); i++) {
                if(i==0){
                    str.append("seller_leader like '%" + values.get(i) +  "%'");
                }else{
                    str.append("or seller_leader like '%" + values.get(i) +  "%'");
                }
            }
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andSellersManagersLike(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            for (int i = 0; i < values.size(); i++) {
                if(i==0){
                    str.append("seller_manager like '%" + values.get(i) +  "%'");
                }else{
                    str.append("or seller_manager like '%" + values.get(i) +  "%'");
                }
            }
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }
        public Criteria andCountIsNull(Integer isNumCountNull) {
            String condition;
            if(isNumCountNull.intValue() == 0){
                condition = "order_num_total is not null";
            }else {
                condition = "order_num_total is null";
            }
            addCriterion(condition);
            return (Criteria) this;
        }

        public Criteria andDayCountIsNull(Integer daySearch,Integer isDayCountNull) {
            String condition;
            if(isDayCountNull.intValue() == 0){
                condition = "order_last_" + daySearch + "d_count is not null";
            }else {
                condition = "order_last_" + daySearch + "d_count is null";
            }
            addCriterion(condition);
            return (Criteria) this;
        }

        public Criteria andHostEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("host_end_time between", value1, value2, "hostEndTime");
            return (Criteria) this;
        }
        public Criteria andHostEndTimeLessThan(Timestamp value) {
            addCriterion("host_end_time <", value, "hostEndTime");
            return (Criteria) this;
        }

        public Criteria andHostEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("host_end_time >=", value, "hostEndTime");
            return (Criteria) this;
        }

        public Criteria andHostEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("host_end_time <=", value, "hostEndTime");
            return (Criteria) this;
        }


        public Criteria andFiveDiscountBetween(Double value1, Double value2) {
            addCriterion("five_discount between", value1, value2, "fiveDiscount");
            return (Criteria) this;
        }
        public Criteria andFiveDiscountLessThan(Double value) {
            addCriterion("five_discount <", value, "fiveDiscount");
            return (Criteria) this;
        }

        public Criteria andFiveDiscountGreaterThanOrEqualTo(Double value) {
            addCriterion("five_discount >=", value, "fiveDiscount");
            return (Criteria) this;
        }

        public Criteria andFiveDiscountLessThanOrEqualTo(Double value) {
            addCriterion("five_discount <=", value, "fiveDiscount");
            return (Criteria) this;
        }

        public Criteria andSevenDiscountBetween(Double value1, Double value2) {
            addCriterion("seven_discount between", value1, value2, "sevenDiscount");
            return (Criteria) this;
        }
        public Criteria andSevenDiscountLessThan(Double value) {
            addCriterion("seven_discount <", value, "sevenDiscount");
            return (Criteria) this;
        }

        public Criteria andSevenDiscountGreaterThanOrEqualTo(Double value) {
            addCriterion("seven_discount >=", value, "sevenDiscount");
            return (Criteria) this;
        }

        public Criteria andSevenDiscountLessThanOrEqualTo(Double value) {
            addCriterion("seven_discount <=", value, "sevenDiscount");
            return (Criteria) this;
        }


        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", value, "ruleName");
            return (Criteria) this;
        }


    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}