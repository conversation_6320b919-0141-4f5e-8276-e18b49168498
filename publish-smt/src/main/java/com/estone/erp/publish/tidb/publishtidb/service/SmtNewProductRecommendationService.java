package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductRecommendationDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 *
 */
public interface SmtNewProductRecommendationService extends IService<SmtNewProductRecommendation> {

    IPage<SmtNewProductRecommendation> pageQuery(SmtNewProductRecommendationDto dto);

    IPage<SmtNewProductRecommendation> page(SmtNewProductRecommendationDto dto);

    ApiResult<String> download(SmtNewProductRecommendationDto dto);

    List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtNewProductRecommendation> wrapper);

    void updateTitleToProductName();

    String importUpdateSale(MultipartFile file) throws IOException;

    SmtNewProductRecommendation getBySpu(String spu);
}
