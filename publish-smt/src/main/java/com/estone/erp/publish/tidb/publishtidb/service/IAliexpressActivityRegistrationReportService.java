package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReport;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReportCriteria;

/**
 * <p>
 * 平台活动报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface IAliexpressActivityRegistrationReportService extends IService<AliexpressActivityRegistrationReport> {
    CQueryResult<AliexpressActivityRegistrationReport> search(CQuery<AliexpressActivityRegistrationReportCriteria> cquery);

    /**
     * 导出
     * @param cquery
     * @return
     */
    ResponseJson download(CQuery<AliexpressActivityRegistrationReportCriteria> cquery);
}
