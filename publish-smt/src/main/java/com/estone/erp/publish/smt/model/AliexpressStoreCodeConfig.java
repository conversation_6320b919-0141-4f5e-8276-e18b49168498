package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class AliexpressStoreCodeConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelIgnore
    private Integer id;

    /**
     * 规则名称
     */
    @ExcelProperty("规则名称")
    private String ruleName;

    /**
     * 店铺 
     */
    @ExcelProperty("店铺")
    private String store;

    /**
     * 类型 
     */
    @ExcelProperty("类型")
    private String type;

    /**
     * 优惠名称
     */
    @ExcelProperty("优惠名称")
    private String discountName;

    /**
     * 活动id
     */
    @ExcelProperty("活动id")
    private String activityId;

    /**
     * 优惠金额
     */
    @ExcelProperty("优惠金额")
    private String discountNum;

    /**
     * 优惠门槛 
     */
    @ExcelProperty("优惠门槛")
    private String discountThreshold;

    /**
     * 发放张数
     */
    @ExcelProperty("发放张数")
    private Integer num;

    /**
     * 每人限额
     */
    @ExcelProperty("每人限额")
    private Integer limitNum;

    /**
     * 使用开始时间
     */
    @ExcelProperty("使用开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp timeStart;

    /**
     * 使用结束时间
     */
    @ExcelProperty("使用结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp timeEnd;

    /**
     * 投放渠道
     */
    @ExcelProperty("投放渠道")
    private String channel;

    /**
     * 其他投放渠道
     */
    @ExcelProperty("其他投放渠道")
    private String anotherChannel;

    /**
     * 适用商品
     */
    @ExcelProperty("适用商品")
    private String activityScope;

    /**
     * 适用国家
     */
    @ExcelIgnore
    private String activityCountry;

    /**
     * 提交状态
     */
    @ExcelProperty("提交状态")
    private String commitStatus;

    /**
     * 错误信息
     */
    @ExcelProperty("失败信息")
    private String errorMessage;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String saleManager;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleManagerLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String salesSupervisor;

    /**
     * 活动状态
     */
    private String activityStatus;

    /**
     * 配置json
     */
    @ExcelIgnore
    private String configJson;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

    /**
     * 提交时间
     */
    @ExcelProperty("提交时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp commitTime;

    /**
     * 已提交的数据是否被更改，0否1是
     */
    @ExcelIgnore
    private Integer isUpdate;

    /**
     * 店铺分组
     */
    @ExcelProperty(value = "店铺分组")
    private String groupName;
}