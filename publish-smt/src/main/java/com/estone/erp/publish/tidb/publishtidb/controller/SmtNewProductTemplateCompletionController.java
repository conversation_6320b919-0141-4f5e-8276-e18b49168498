package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductTemplateCompletionDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductTemplateCompletion;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductTemplateCompletionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * smt 新品范本完成率
 * </p>
 *
 * <AUTHOR>
 * @since 2024年12月1日17:32:58
 */
@Slf4j
@RestController
@RequestMapping("/smtNewProductTemplateCompletion")
public class SmtNewProductTemplateCompletionController {
    @Resource
    private SmtNewProductTemplateCompletionService smtNewProductTemplateCompletionService;

    /**
     * 分页查询
     */
    @PostMapping("queryPage")
    public ApiResult<IPage<SmtNewProductTemplateCompletion>> queryPage(@RequestBody SmtNewProductTemplateCompletionDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            IPage<SmtNewProductTemplateCompletion> page = smtNewProductTemplateCompletionService.pageQuery(dto);
            return ApiResult.newSuccess(page);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 导出
     */
    @PostMapping("download")
    public ApiResult<String> download(@RequestBody SmtNewProductTemplateCompletionDto dto) {
        try {
             smtNewProductTemplateCompletionService.download(dto);
        }catch (Exception e){
            log.error("导出失败",e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }


    /**
     * 导出未完成Spu
     */
    @PostMapping("downloadUnfinishedSpu")
    public ApiResult<String> downloadUnfinishedSpu(@RequestBody SmtNewProductTemplateCompletionDto dto) {
        try {
            smtNewProductTemplateCompletionService.downloadUnfinishedSpu(dto);
        }catch (Exception e){
            log.error("导出失败",e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }
}
