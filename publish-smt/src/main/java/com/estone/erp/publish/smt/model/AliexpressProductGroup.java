package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

@Data
public class AliexpressProductGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_product_group.id
     */
    private Integer id;

    /**
     * 速卖通帐号 database column aliexpress_product_group.account_number
     */
    private String accountNumber;

    /**
     * 分组id database column aliexpress_product_group.group_id
     */
    private Long groupId;

    /**
     * 分组名 database column aliexpress_product_group.group_name
     */
    private String groupName;

    /**
     * 父分组id database column aliexpress_product_group.parent_id
     */
    private Integer parentId;

    /**
     * 根分组到此分组完整group_id组合 database column aliexpress_product_group.full_path_code
     */
    private String fullPathCode;

    /**
     * 是否叶子分组 database column aliexpress_product_group.leaf
     */
    private Boolean leaf;

    private List<AliexpressProductGroup> childGroups;
}