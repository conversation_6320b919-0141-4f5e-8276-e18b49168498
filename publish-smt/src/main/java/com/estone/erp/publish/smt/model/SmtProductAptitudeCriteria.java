package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> smt_product_aptitude
 * 2023-11-21 17:03:16
 */
@Data
public class SmtProductAptitudeCriteria extends SmtProductAptitude {
    private static final long serialVersionUID = 1L;

    /**
     * 正序还是倒序，正序ASC,倒序DESC
     */
    private String sequence;

    private String orderBy;

    private List<Long> idList;

    private List<Long> productIdList;

    private List<String> skuList;

    private List<String> issueTypeList;

    private List<String> riskCountryList;

    private List<String> handleStatusList;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;

    private Integer from_account_order_num_7d;
    private Integer to_account_order_num_7d;
    private Integer from_account_order_num_14d;
    private Integer to_account_order_num_14d;
    private Integer from_account_order_num_30d;
    private Integer to_account_order_num_30d;
    private Integer from_account_order_num_60d;
    private Integer to_account_order_num_60d;
    private Integer from_account_order_num_90d;
    private Integer to_account_order_num_90d;
    private Integer from_smt_plat_order_num_7d;
    private Integer to_smt_plat_order_num_7d;
    private Integer from_smt_plat_order_num_14d;
    private Integer to_smt_plat_order_num_14d;
    private Integer from_smt_plat_order_num_30d;
    private Integer to_smt_plat_order_num_30d;

    public SmtProductAptitudeExample getExample() {
        SmtProductAptitudeExample example = new SmtProductAptitudeExample();



        SmtProductAptitudeExample.Criteria criteria = example.createCriteria();

        if(this.getFrom_account_order_num_7d() != null){
            criteria.andAccountOrderNum7dGreaterThanOrEqualTo(this.getFrom_account_order_num_7d());
        }
        if(this.getTo_account_order_num_7d() != null){
            criteria.andAccountOrderNum7dLessThanOrEqualTo(this.getTo_account_order_num_7d());
        }
        if(this.getFrom_account_order_num_14d() != null){
            criteria.andAccountOrderNum14dGreaterThanOrEqualTo(this.getFrom_account_order_num_14d());
        }
        if(this.getTo_account_order_num_14d() != null){
            criteria.andAccountOrderNum14dLessThanOrEqualTo(this.getTo_account_order_num_14d());
        }
        if(this.getFrom_account_order_num_30d() != null){
            criteria.andAccountOrderNum30dGreaterThanOrEqualTo(this.getFrom_account_order_num_30d());
        }
        if(this.getTo_account_order_num_30d() != null){
            criteria.andAccountOrderNum30dLessThanOrEqualTo(this.getTo_account_order_num_30d());
        }
        if(this.getFrom_account_order_num_60d() != null){
            criteria.andAccountOrderNum60dGreaterThanOrEqualTo(this.getFrom_account_order_num_60d());
        }
        if(this.getTo_account_order_num_60d() != null){
            criteria.andAccountOrderNum60dLessThanOrEqualTo(this.getTo_account_order_num_60d());
        }
        if(this.getFrom_account_order_num_90d() != null){
            criteria.andAccountOrderNum90dGreaterThanOrEqualTo(this.getFrom_account_order_num_90d());
        }
        if(this.getTo_account_order_num_90d() != null){
            criteria.andAccountOrderNum90dLessThanOrEqualTo(this.getTo_account_order_num_90d());
        }
        if(this.getFrom_smt_plat_order_num_7d() != null){
            criteria.andAccountOrderNum7dGreaterThanOrEqualTo(this.getFrom_smt_plat_order_num_7d());
        }
        if(this.getTo_smt_plat_order_num_7d() != null){
            criteria.andSmtPlatOrderNum7dLessThanOrEqualTo(this.getTo_smt_plat_order_num_7d());
        }
        if(this.getFrom_smt_plat_order_num_14d() != null){
            criteria.andSmtPlatOrderNum14dGreaterThanOrEqualTo(this.getFrom_smt_plat_order_num_14d());
        }
        if(this.getTo_smt_plat_order_num_14d() != null){
            criteria.andSmtPlatOrderNum14dLessThanOrEqualTo(this.getTo_smt_plat_order_num_14d());
        }
        if(this.getFrom_smt_plat_order_num_30d() != null){
            criteria.andSmtPlatOrderNum30dGreaterThanOrEqualTo(this.getFrom_smt_plat_order_num_30d());
        }
        if(this.getTo_smt_plat_order_num_30d() != null){
            criteria.andSmtPlatOrderNum30dLessThanOrEqualTo(this.getTo_smt_plat_order_num_30d());
        }
        if(CollectionUtils.isNotEmpty(this.getIdList())){
            criteria.andIdIn(this.getIdList());
        }
        if(CollectionUtils.isNotEmpty(this.getProductIdList())){
            criteria.andProductIdIn(this.getProductIdList());
        }
        if(CollectionUtils.isNotEmpty(this.getSkuList())){
            criteria.andSkuIn(this.getSkuList());
        }
        if(CollectionUtils.isNotEmpty(this.getIssueTypeList())){
            criteria.andIssueTypeLikeIn(this.getIssueTypeList());
        }
        if(CollectionUtils.isNotEmpty(this.getRiskCountryList())){
            criteria.andRiskCountryIn(this.getRiskCountryList());
        }
        if(CollectionUtils.isNotEmpty(this.getHandleStatusList())){
            criteria.andHandleStatusIn(this.getHandleStatusList());
        }
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            List<String> strings = CommonUtils.splitList(this.getAccountNumber(), ",");
            criteria.andAccountNumberIn(strings);
        }
        if (StringUtils.isNotBlank(this.getImage())) {
            criteria.andImageEqualTo(this.getImage());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getTitle())) {
            criteria.andTitleEqualTo(this.getTitle());
        }
        if (StringUtils.isNotBlank(this.getIssueType())) {
            criteria.andIssueTypeEqualTo(this.getIssueType());
        }
        if (StringUtils.isNotBlank(this.getRiskCountry())) {
            criteria.andRiskCountryEqualTo(this.getRiskCountry());
        }
        if (StringUtils.isNotBlank(this.getImpactType())) {
            criteria.andImpactTypeEqualTo(this.getImpactType());
        }
        if (StringUtils.isNotBlank(this.getHandleStatus())) {
            criteria.andHandleStatusEqualTo(this.getHandleStatus());
        }
        if (StringUtils.isNotBlank(this.getAuditResultList())) {
            criteria.andAuditResultListEqualTo(this.getAuditResultList());
        }
        if (StringUtils.isNotBlank(this.getIssueDetails())) {
            criteria.andIssueDetailsEqualTo(this.getIssueDetails());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if (this.getCrawlTime() != null) {
            criteria.andCrawlTimeEqualTo(this.getCrawlTime());
        }
        if (this.getCrawlUpdatedTime() != null) {
            criteria.andCrawlUpdatedTimeEqualTo(this.getCrawlUpdatedTime());
        }

        //排序
        if(StringUtils.isNotBlank(this.getOrderBy())){
            example.setOrderByClause(this.getOrderBy() + " " + this.getSequence());
        }
        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("crawl_updated_time desc");
        }
        return example;
    }
}