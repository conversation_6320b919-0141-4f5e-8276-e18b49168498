package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliMarketingTempInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ali_marketing_temp_info.id
     */
    private Integer id;

    /**
     * 关联模板id database column ali_marketing_temp_info.temp_id
     */
    private Integer tempId;

    /**
     * 产品ID database column ali_marketing_temp_info.product_id
     */
    private Long productId;


    private String articleNumber;

    /**
     * 标题 database column ali_marketing_temp_info.title
     */
    private String title;

    /**
     * 图片 database column ali_marketing_temp_info.img_url
     */
    private String imgUrl;

    /**
     * sku价格 database column ali_marketing_temp_info.sku_price
     */
    private Double skuPrice;

    /**
     * 创建人 database column ali_marketing_temp_info.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column ali_marketing_temp_info.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column ali_marketing_temp_info.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column ali_marketing_temp_info.last_update_date
     */
    private Timestamp lastUpdateDate;
}