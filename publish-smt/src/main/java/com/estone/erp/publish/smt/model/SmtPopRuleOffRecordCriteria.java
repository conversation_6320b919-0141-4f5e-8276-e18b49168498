package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-09 16:19:21
 */
@Data
public class SmtPopRuleOffRecordCriteria extends SmtPopRuleOffRecord {
    private static final long serialVersionUID = 1L;
    private List<Long> idList;
    private String ruleNameLike;
    private String skusLike;
    private List<String> accountList;
    private String productIdStr;
    //下架状态
    private List<Integer> executeStateList;

    private String createDateFrom;
    private String createDateTo;

    private String offDateFrom;
    private String offDateTo;

    public SmtPopRuleOffRecordExample getExample() {
        SmtPopRuleOffRecordExample example = new SmtPopRuleOffRecordExample();
        SmtPopRuleOffRecordExample.Criteria criteria = example.createCriteria();
        if(CollectionUtils.isNotEmpty(this.getIdList())){
            criteria.andIdIn(this.getIdList());
        }
        if(StringUtils.isNotBlank(this.getSkusLike())){
            criteria.andSkusListLike(this.getSkusLike());
        }
        if(StringUtils.isNotBlank(this.getRuleNameLike())){
            criteria.andRuleNameLike("%" + this.getRuleNameLike() + "%");
        }
        if(CollectionUtils.isNotEmpty(this.getAccountList())){
            criteria.andAccountIn(this.getAccountList());
        }
        if(StringUtils.isNotBlank(this.getProductIdStr())){
            criteria.andProductIdIn(CommonUtils.splitLongList(this.getProductIdStr(), ","));
        }
        if(CollectionUtils.isNotEmpty(this.getExecuteStateList())){
            criteria.andExecuteStateIn(this.getExecuteStateList());
        }
        if(StringUtils.isNotBlank(this.getCreateDateFrom())){
            criteria.andCreateDateGreaterThanOrEqualTo(this.getCreateDateFrom());
        }
        if(StringUtils.isNotBlank(this.getCreateDateTo())){
            criteria.andCreateDateLessThanOrEqualTo(this.getCreateDateTo());
        }
        if(StringUtils.isNotBlank(this.getOffDateFrom())){
            criteria.andOffDateGreaterThanOrEqualTo(this.getOffDateFrom());
        }
        if(StringUtils.isNotBlank(this.getOffDateTo())){
            criteria.andOffDateLessThanOrEqualTo(this.getOffDateTo());
        }
        if (StringUtils.isNotBlank(this.getImg())) {
            criteria.andImgEqualTo(this.getImg());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getSpu())) {
            criteria.andSpuEqualTo(this.getSpu());
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            criteria.andSkusEqualTo(this.getSkus());
        }
        if (StringUtils.isNotBlank(this.getSkuIds())) {
            criteria.andSkuIdsEqualTo(this.getSkuIds());
        }
        if (StringUtils.isNotBlank(this.getRuleName())) {
            criteria.andRuleNameEqualTo(this.getRuleName());
        }
        if (StringUtils.isNotBlank(this.getOffWay())) {
            criteria.andOffWayEqualTo(this.getOffWay());
        }
        if (StringUtils.isNotBlank(this.getRuleContent())) {
            criteria.andRuleContentEqualTo(this.getRuleContent());
        }
        if (StringUtils.isNotBlank(this.getProductInfo())) {
            criteria.andProductInfoEqualTo(this.getProductInfo());
        }
        if (this.getOrder24hCount() != null) {
            criteria.andOrder24hCountEqualTo(this.getOrder24hCount());
        }
        if (this.getOrderLast7dCount() != null) {
            criteria.andOrderLast7dCountEqualTo(this.getOrderLast7dCount());
        }
        if (this.getOrderLast14dCount() != null) {
            criteria.andOrderLast14dCountEqualTo(this.getOrderLast14dCount());
        }
        if (this.getOrderLast30dCount() != null) {
            criteria.andOrderLast30dCountEqualTo(this.getOrderLast30dCount());
        }
        if (this.getOrderLast60dCount() != null) {
            criteria.andOrderLast60dCountEqualTo(this.getOrderLast60dCount());
        }
        if (this.getOrderLast180dCount() != null) {
            criteria.andOrderLast180dCountEqualTo(this.getOrderLast180dCount());
        }
        if (this.getOrderNumTotal() != null) {
            criteria.andOrderNumTotalEqualTo(this.getOrderNumTotal());
        }
        if (this.getView7dCount() != null) {
            criteria.andView7dCountEqualTo(this.getView7dCount());
        }
        if (this.getView14dCount() != null) {
            criteria.andView14dCountEqualTo(this.getView14dCount());
        }
        if (this.getView30dCount() != null) {
            criteria.andView30dCountEqualTo(this.getView30dCount());
        }
        if (this.getExposure7dCount() != null) {
            criteria.andExposure7dCountEqualTo(this.getExposure7dCount());
        }
        if (this.getExposure14dCount() != null) {
            criteria.andExposure14dCountEqualTo(this.getExposure14dCount());
        }
        if (this.getExposure30dCount() != null) {
            criteria.andExposure30dCountEqualTo(this.getExposure30dCount());
        }
        if (this.getExecuteState() != null) {
            criteria.andExecuteStateEqualTo(this.getExecuteState());
        }
        if (StringUtils.isNotBlank(this.getFailInfo())) {
            criteria.andFailInfoEqualTo(this.getFailInfo());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("id desc");
        }
        return example;
    }
}