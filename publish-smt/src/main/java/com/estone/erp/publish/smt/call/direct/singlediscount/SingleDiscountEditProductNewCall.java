package com.estone.erp.publish.smt.call.direct.singlediscount;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.AbstractSmtCall;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscount;
import com.estone.erp.publish.smt.model.dto.BatchUpdatedConfigParam;
import com.estone.erp.publish.smt.model.dto.SingleDiscountEditProDTO;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProDTO;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProErrorDTO;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class SingleDiscountEditProductNewCall extends AbstractSmtCall {
    public static SingleDiscountEditProDTO buildEditProDTO(Long singleDiscountId, List<SingleDiscountProDTO> singleDiscountProDTOS) {
        SingleDiscountEditProDTO singleDiscountEditProDTO = new SingleDiscountEditProDTO();
        //实际就一个元素
        List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjectsDtoList = singleDiscountProDTOS.stream().map(t -> {
            SingleDiscountEditProDTO.ProductObjectsDTO productObjectsDTO = new SingleDiscountEditProDTO.ProductObjectsDTO();
            productObjectsDTO.setProduct_id(t.getItemId().toString());

            SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO productDiscountListDTO = new SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO();
            productDiscountListDTO.setTerminal("ALL");
            productDiscountListDTO.setDiscount(t.getDiscount());

            ArrayList<SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO> productDiscountListDtoList = Lists.newArrayList(productDiscountListDTO);
            productObjectsDTO.setProduct_discount_list(productDiscountListDtoList);
            productObjectsDTO.setBuy_max_num(t.getBuy_max_num());
            return productObjectsDTO;
        }).collect(Collectors.toList());

        singleDiscountEditProDTO.setProduct_objects(productObjectsDtoList);
        singleDiscountEditProDTO.setPromotion_id(Optional.ofNullable(singleDiscountId).map(Object::toString).orElse(null));
        if (ObjectUtils.isNotEmpty(singleDiscountProDTOS.get(0).getStore_club_discount_rate())) {
            singleDiscountEditProDTO.setStore_club_discount_rate(singleDiscountProDTOS.get(0).getStore_club_discount_rate().toString());
        } else {
            singleDiscountEditProDTO.setStore_club_discount_rate("");
        }
        return singleDiscountEditProDTO;
    }

    public static ResponseJson editProductCommonDiscount(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount smtMarketingSingleDiscount,
                                                         List<Long> itemIds, Integer discount, Integer buyMaxNum, BatchUpdatedConfigParam batchUpdatedConfigParam) {
        SingleDiscountEditProDTO singleDiscountEditProDTO = new SingleDiscountEditProDTO();

        List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjectsDtoList = itemIds.stream().map(t -> {
            SingleDiscountEditProDTO.ProductObjectsDTO productObjectsDTO = new SingleDiscountEditProDTO.ProductObjectsDTO();
            productObjectsDTO.setProduct_id(t.toString());

            SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO productDiscountListDTO = new SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO();
            productDiscountListDTO.setTerminal("ALL");
            productDiscountListDTO.setDiscount(discount);

            ArrayList<SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO> productDiscountListDtoList = Lists.newArrayList(productDiscountListDTO);
            productObjectsDTO.setProduct_discount_list(productDiscountListDtoList);
            productObjectsDTO.setBuy_max_num(buyMaxNum);
            return productObjectsDTO;
        }).collect(Collectors.toList());

        singleDiscountEditProDTO.setProduct_objects(productObjectsDtoList);
        singleDiscountEditProDTO.setPromotion_id(smtMarketingSingleDiscount.getSingleDiscountId().toString());
        if (ObjectUtils.isNotEmpty(batchUpdatedConfigParam) && ObjectUtils.isNotEmpty(batchUpdatedConfigParam.getStore_club_discount_rate())) {
            singleDiscountEditProDTO.setStore_club_discount_rate(batchUpdatedConfigParam.getStore_club_discount_rate().toString());
        } else {
            singleDiscountEditProDTO.setStore_club_discount_rate("");
        }
        return editPromotionPros(saleAccount, singleDiscountEditProDTO);
    }

    public static IopResponse editPromotionProducts(SaleAccountAndBusinessResponse saleAccount,
                                                    SingleDiscountEditProDTO singleDiscountEditProDTO) {

        List<String> testAccounts = Optional.ofNullable(testAccountsConfig.getEditProductNewCallTestAccounts()).orElse(Collections.emptyList());
        // 执行请求
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.marketing.limitdiscountpromotionproduct.edit");
        request.addApiParameter("param_limited_disc_product_input_dto", JSON.toJSONString(singleDiscountEditProDTO));
        return EnvironmentSupplierWrapper.execute(() -> {
            IopResponse iopResponse = new IopResponse();
            try {
                iopResponse = AbstractSmtOpenCall.execute(saleAccount, request);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                iopResponse.setCode("500");
                iopResponse.setMessage(e.getMessage());
                iopResponse.setBody(e.getMessage());
            }
            return iopResponse;
        }, () -> {
            IopResponse response = new IopResponse();
            response.setCode("500");
            response.setMessage("非正式环境不允许更改折扣商品");
            response.setBody("非正式环境不允许更改折扣商品");
            return response;
        }, testAccounts, saleAccount);
    }


    public static ResponseJson editPromotionPros(SaleAccountAndBusinessResponse saleAccount,
                                                 SingleDiscountEditProDTO singleDiscountEditProDTO) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        IopResponse iopResponse = editPromotionProducts(saleAccount, singleDiscountEditProDTO);
        List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjects = singleDiscountEditProDTO.getProduct_objects();
        rsp.setMessage(iopResponse.getBody());
        if (isValidIopResponse(iopResponse)) {
            JSONObject errorResponse = JSON.parseObject(iopResponse.getBody()).getJSONObject("error_response");
            if (ObjectUtils.isNotEmpty(errorResponse)) {
                //失败的商品会返回,并处理在返回的error参数中
                processErrorResponse(errorResponse, productObjects, rsp);
            }
            rsp.setStatus(StatusCode.SUCCESS);
        } else {
            rsp.setErrors(buildErrorsByProDTOs(productObjects, iopResponse.getMessage()));
        }
        return rsp;
    }

    public static ResponseJson editPros(SaleAccountAndBusinessResponse saleAccount, Long singleDiscountId, List<SingleDiscountProDTO> singleDiscountProDTOS) {
        SingleDiscountEditProDTO singleDiscountEditProDTO = buildEditProDTO(singleDiscountId, singleDiscountProDTOS);
        return editPromotionPros(saleAccount, singleDiscountEditProDTO);
    }

    private static boolean isValidIopResponse(IopResponse iopResponse) {
        return ObjectUtils.isNotEmpty(iopResponse) && iopResponse.isSuccess() && StringUtils.isNotBlank(iopResponse.getBody());
    }

    private static void processErrorResponse(JSONObject errorResponse, List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjects, ResponseJson rsp) {
        try {
            JSONArray subMsg = errorResponse.getJSONArray("sub_msg");
            if (CollectionUtils.isNotEmpty(subMsg)) {
                List<SingleDiscountProErrorDTO> errorDTOs = subMsg.toJavaList(SingleDiscountProErrorDTO.class);
                rsp.setErrors(buildErrorsByErrorDTOs(errorDTOs));
            }
        } catch (Exception e) {
            List<String> itemIds = productObjects.stream().map(SingleDiscountEditProDTO.ProductObjectsDTO::getProduct_id).collect(Collectors.toList());
            //一般能到这里的都是Illegal product id等这种不带有商品id返回的错误,因此特别处理补充添加商品id
            rsp.setMessage(String.format("商品ID[%s]-%s", StringUtils.join(itemIds, ","), rsp.getMessage()));
            rsp.setErrors(buildErrorsByProDTOs(productObjects, JSONObject.toJSONString(errorResponse)));
        }
    }

    private static List<ResponseError> buildErrorsByErrorDTOs(List<SingleDiscountProErrorDTO> errorDTOs) {
        return errorDTOs.stream()
                .map(errorDTO -> new ResponseError(StatusCode.FAIL, Optional.ofNullable(errorDTO.getItemId()).map(Object::toString).orElse(null), errorDTO.getErrorMsg()))
                .collect(Collectors.toList());
    }

    private static List<ResponseError> buildErrorsByProDTOs(List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjects, String message) {
        return productObjects.stream()
                .map(proDTO -> new ResponseError(StatusCode.FAIL, Optional.ofNullable(proDTO.getProduct_id()).map(Object::toString).orElse(null), message))
                .collect(Collectors.toList());

    }
}
