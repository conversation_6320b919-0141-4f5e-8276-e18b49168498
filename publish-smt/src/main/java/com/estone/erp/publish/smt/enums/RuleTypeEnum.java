package com.estone.erp.publish.smt.enums;

public enum RuleTypeEnum {
    TOTAL(0, "总"),
    WITH_RULE(1, "有规则"),
    WITHOUT_RULE(2, "无规则");

    private final int code;
    private final String description;

    RuleTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取枚举
    public static RuleTypeEnum fromCode(int code) {
        for (RuleTypeEnum type : RuleTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    @Override
    public String toString() {
        return code + "-" + description;
    }
}
