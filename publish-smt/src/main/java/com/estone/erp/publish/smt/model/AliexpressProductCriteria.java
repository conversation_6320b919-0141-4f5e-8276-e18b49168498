package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import lombok.Data;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> aliexpress_product
 * 2019-10-24 11:42:03
 */
@Data
public class AliexpressProductCriteria extends AliexpressProduct {
    private static final long serialVersionUID = 1L;

    private String idStr;

    //productIdStr
    private String productIdStr;

    private String articleNumberStr;

    //库存有无
    private String stockStatus;

    private String orderBy;

    //禁售平台
    private String forbidChannel;

    //sku状态
    private String skuStatusStr;

    private Date fromCreateTime;

    private Date toCreateDate;

    private Date fromGmtCreateDate;

    private Date toGmtCreateDate;

    private Date fromGmtModifiedDate;

    private Date toGmtModifiedDate;

    private Date fromLastSynchDate;

    private Date toLastSynchDate;

    private List<Integer> fields;//导出文件自选字段

    private String shippingMethodCode;

    private String subjectLike;

    private String groupBy;

    private Double fromGrossWeight;

    private Double toGrossWeight;

    private String freightTemplateIds;

    //权限账号
    private List<String> authSellerList = new ArrayList<>();


    private String leftMark;

    private Double leftValue;

    private String rightMark;

    private Double rightValue;

    /**
     * 修改车型库提交的json
     */
    private String cayJson;

    /**
     * 产品上下架 的类型 上架—1 、下架—0
     */
    private Integer type;

    /**
     * 是否保留之前分组
     */
    private Boolean retain;

    private String groupIdStr;

    /**
     * 是否支持车型库
     */
    private Boolean isCayType;

    //---在线列表32国调价

    private String logisticsName;

    private String createBy;

    private Double gross;

    //调价方式
    private String priceType;

    private String fromLastphaseChangeTime;

    //是否查询产品，默认true 在线列表先不查
    private Boolean isSeleteProduct = true;

    private Boolean isClear;

    //库存范围
    private Integer fromIpmSkuStock;

    private Integer toIpmSkuStock;

    //引流sku
    private Map<String, Boolean> map;

    //需要算价的国家
    private List<String> updateCountryCodeList;

    //待优化
    private String optimized;
    
    private Boolean isSynchAll = false;

    private Boolean isMqMessage = false;

    public AliexpressProductExample getExample() {
        AliexpressProductExample example = new AliexpressProductExample();
        AliexpressProductExample.Criteria criteria = example.createCriteria();

        boolean isLeftJoin = false;

        if(this.getIsVariant() != null){
            criteria.andIsVariantEqualTo(this.getIsVariant());
        }

        if(this.getIsCayType() != null){
            example.setIsCayType(this.getIsCayType());
        }

        if(StringUtils.isNotBlank(this.getFreightTemplateIds())){
            criteria.andFreightTemplateIdIn(CommonUtils.splitLongList(this.getFreightTemplateIds(),","));
        }

        if(this.getFreightTemplateId() != null){
            criteria.andFreightTemplateIdEqualTo(this.getFreightTemplateId());
        }

        if(StringUtils.isNotBlank(this.getGroupBy())){
            example.setGroupBy(this.getGroupBy());
        }

        if(this.getId() != null){
            criteria.andIdEqualTo(this.getId());
        }

        if(StringUtils.isNotBlank(this.idStr)){
            List<Integer> integers = CommonUtils.splitIntList(this.idStr, ",");
            if(integers.size() == 1){
                criteria.andIdEqualTo(integers.get(0));
            }else{
                criteria.andIdIn(integers);
            }
        }

        //商品id
        if(StringUtils.isNotBlank(this.productIdStr)){
            List<Long> longs = CommonUtils.splitLongList(this.productIdStr, ",");
            if(longs.size() == 1){
                criteria.andProductIdEqualTo(longs.get(0));
            }else{
                criteria.andProductIdIn(longs);
            }
        }

        if(this.getProductId() != null){
            criteria.andProductIdEqualTo(this.getProductId());
        }

        //货号
        if(StringUtils.isNotBlank(this.articleNumberStr)){
            List<String> strings = CommonUtils.splitList(this.articleNumberStr, ",");
            if(strings.size() == 1){
                criteria.andArticleNumberEqualTo(strings.get(0));
            }else{
                criteria.andArticleNumberIn(strings);
            }
        }

        if(StringUtils.isNotBlank(this.getArticleNumber())){
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }

        //账号
        if(StringUtils.isNotBlank(this.getAliexpressAccountNumber())){
            List<String> accountList = CommonUtils.splitList(this.getAliexpressAccountNumber(), ",");
            example.setForceIndex("idx_account_number");
            if(accountList.size() == 1){
                criteria.andAliexpressAccountNumberEqualTo(accountList.get(0));
            }else{
                criteria.andAliexpressAccountNumberIn(accountList);
            }
        }
        //权限
        if(StringUtils.isBlank(this.getAliexpressAccountNumber()) && CollectionUtils.isNotEmpty(this.getAuthSellerList())){
            example.setForceIndex("idx_account_number");
            example.setAuthSellerList(this.getAuthSellerList());
        }

        //在售状态
        if(StringUtils.isNotBlank(this.getProductStatusType())){
            List<String> strings = CommonUtils.splitList(this.getProductStatusType(), ",");
            if(strings.size() == 1){
                criteria.andProductStatusTypeEqualTo(strings.get(0));
            }else{
                criteria.andProductStatusTypeIn(strings);
            }
        }

        if(StringUtils.isNotBlank(this.getSkuCode())){
            criteria.andSkuCodeEqualTo(this.getSkuCode());
        }

        if(StringUtils.isNotBlank(this.getSkuId())){
            criteria.andSkuIdEqualTo(this.getSkuId());
        }
        if(this.getGroupId() != null){
            criteria.andGroupIdEqualTo(this.getGroupId());
        }
        if(StringUtils.isNotBlank(this.getGroupIds())){
            List<Long> integers = CommonUtils.splitLongList(this.getGroupIds(), ",");
            if(integers.size() == 1){
                criteria.andGroupIdEqualTo(integers.get(0));
            }else{
                criteria.andGroupIdIn(integers);
            }
        }
        if(this.getFreightTemplateId() != null){
            criteria.andFreightTemplateIdEqualTo(this.getFreightTemplateId());
        }

        if(StringUtils.isNotBlank(this.getStockStatus())){
            if(StringUtils.equalsIgnoreCase(this.getStockStatus(), "true")){
                criteria.andIpmSkuStockGreaterThan(0);
            }else if(StringUtils.equalsIgnoreCase(this.getStockStatus(), "false")){
                criteria.andIpmSkuStockEqualTo(0);
            }
        }

        if(this.getFromIpmSkuStock() != null){
            criteria.andIpmSkuStockGreaterThan(this.getFromIpmSkuStock());
        }

        if(this.getToIpmSkuStock() != null){
            criteria.andIpmSkuStockLessThan(this.getToIpmSkuStock());
        }

        if(this.getFromCreateTime() != null){
            criteria.andCreateTimeGreaterThanOrEqualTo(new Timestamp(this.getFromCreateTime().getTime()));
        }

        if(this.getToCreateDate() != null){
            criteria.andCreateTimeLessThanOrEqualTo(new Timestamp(this.getToCreateDate().getTime()));
        }

        if(this.getFromGmtCreateDate() != null){
            criteria.andGmtCreateGreaterThanOrEqualTo(new Timestamp(this.getFromGmtCreateDate().getTime()));
        }
        if(this.getToGmtCreateDate() != null){
            criteria.andGmtCreateLessThanOrEqualTo(new Timestamp(this.getToGmtCreateDate().getTime()));
        }

        if(this.getFromGmtModifiedDate() != null){
            criteria.andGmtModifiedGreaterThanOrEqualTo(new Timestamp(this.getFromGmtModifiedDate().getTime()));
        }

        if(this.getToGmtModifiedDate() != null){
            criteria.andGmtModifiedLessThanOrEqualTo(new Timestamp(this.getToGmtModifiedDate().getTime()));
        }

        if(this.getFromLastSynchDate() != null){
            criteria.andLastSyncTimeGreaterThanOrEqualTo(new Timestamp(this.getFromLastSynchDate().getTime()));
        }

        if(this.getToLastSynchDate() != null){
            criteria.andLastSyncTimeLessThanOrEqualTo(new Timestamp(this.getToLastSynchDate().getTime()));
        }

        //状态
        if(StringUtils.isNotBlank(this.getSkuStatusStr())){
            if(StringUtils.indexOf(this.getSkuStatusStr(), ",") != -1){
                example.setPmsSkuStatusList(CommonUtils.splitIntList(this.getSkuStatusStr(), ","));
            }else{
                example.setPmsSkuStatus(Integer.valueOf(this.getSkuStatusStr()));
            }

            isLeftJoin = true;
        }

        if(StringUtils.isNotBlank(this.getForbidChannel())){
            example.setForbidChannel("%," + this.getForbidChannel() + ",%");
            isLeftJoin = true;
        }

        if(StringUtils.isNotBlank(this.getFromLastphaseChangeTime())){
            example.setFromLastphaseChangeTime(this.getFromLastphaseChangeTime());
            isLeftJoin = true;
        }

        if(StringUtils.isNotBlank(this.getOrderBy())){
            example.setOrderBy(this.getOrderBy());
        }

        if(StringUtils.isNotBlank(this.getGroupBy())){
            example.setGroupBy(this.groupBy);
        }

        if(StringUtils.isNotBlank(this.getSubjectLike())) {
            criteria.andSubjectLike("%" + this.getSubjectLike() + "%");
        }

        if(this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }

        if(this.getFromGrossWeight() != null) {
            criteria.andGrossWeightGreaterThanOrEqualTo(this.getFromGrossWeight());
        }

        if(this.getToGrossWeight() != null) {
            criteria.andGrossWeightLessThanOrEqualTo(this.getToGrossWeight());
        }

        if(isLeftJoin){
            example.setLeftJoinTable("true");
        }
        return example;
    }
}