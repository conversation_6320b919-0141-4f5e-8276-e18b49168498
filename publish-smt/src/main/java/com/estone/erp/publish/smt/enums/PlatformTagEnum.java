package com.estone.erp.publish.smt.enums;

public enum PlatformTagEnum {
    s_274273("274273", "纯电"),
    s_274526("274526", "内电"),
    s_274452("274452", "弱磁"),
    s_274511("274511", "粉末"),
    s_274259("274259", "液体"),
    s_274363("274363", "膏体"),
    s_("", "普货"),
    ;

    private String code;

    private String name;

    private PlatformTagEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PlatformTagEnum[] values = values();
        for (PlatformTagEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        PlatformTagEnum[] values = values();
        for (PlatformTagEnum type : values) {
            if (type.name.equalsIgnoreCase(name)) {
                return type.getCode();
            }
        }
        return null;
    }
}
