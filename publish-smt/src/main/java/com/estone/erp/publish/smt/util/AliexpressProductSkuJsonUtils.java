package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.smt.bean.AliexpressTemplateAutoPublish;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeBean;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeJson;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeValueJson;
import com.estone.erp.publish.smt.bean.SkuProperty.AeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.temp.TempSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgAeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgTempSkuProperty;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.SizeCharOpenCall;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.template.attribute.AttributeMatchContext;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述: 通过分类id, spu，账号 来组装 颜色尺寸json信息
 *
 * @className: AliexpressProductSkuJsonUtils
 * @Date: 2018/12/20
 * @Version: 0.0.1
 */
@Slf4j
public class AliexpressProductSkuJsonUtils {

    public static String splitStr = "-";

    private static AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);

    public static List<AliexpressConfig> getAliexpressConfig(String account){
        AliexpressConfigExample example = new AliexpressConfigExample();
        example.setLimit(1);
        example.createCriteria()
                        .andAccountEqualTo(account);

        return aliexpressConfigService.selectByExample(example);
    }

    public static String getAttrJsonNew(AttributeMatchContext context) throws RuntimeException{
        try {
            return getAttrJsonNew(context.getAccount(), context.getSkuToInfoMap(), context.getCalcResultMap(), context.getAutoTemplate(), null, context.isTg());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 重新构造 属性集合
     * @param saleAccountAndBusiness
     * @param skuToInfoMap
     * @param resultMap
     * @param autoTemplate
     * @param synchAttributes
     * @return
     * @throws Exception
     */
    public static String getAttrJsonNew(SaleAccountAndBusinessResponse saleAccountAndBusiness,
                                        Map<String, Map<String, Object>> skuToInfoMap, Map<String, BatchPriceCalculatorResponse> resultMap,
                                        AliexpressTemplateAutoPublish autoTemplate, String synchAttributes, boolean tg) throws Exception{

        //折扣率
        Double discountRate = autoTemplate.getAreaDiscountRate();

        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        SystemParam systemParam = systemParamService.querySystemParamByCodeKey("smt_param.adult_category");
        String paramValue = systemParam.getParamValue();
        List<Integer> categoryIdList = ProductUtils.getAllCodeByFullPath(CommonUtils.splitList(paramValue, ","));
        if(CollectionUtils.isEmpty(categoryIdList)){
            categoryIdList = new ArrayList<>();
        }

        boolean isContainAdult = false;
        String spu = "";
        //判断产品类目是否属于成人用品
        for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
            Map<String, Object> value = stringMapEntry.getValue();
            spu = stringMapEntry.getKey();
            Object categoryIdObject = value.get("categoryId");
            if(categoryIdObject == null || Objects.isNull(categoryIdObject)){
                break;
            }
            Integer categoryId = Integer.valueOf(value.get("categoryId").toString());
            if(categoryIdList.contains(categoryId)){
                isContainAdult = true;
                break;
            }
        }

        //smt 图片池(仅仅只包含aliexpress)
        List<String> smtImages = new ArrayList<>();
        String mainSku = ProductUtils.getMainSku(spu);
        List<String> images = FmsUtils
                .getSmtImgs(mainSku, null);

        //获取spu 的 smt图片池
        if(isContainAdult){
            smtImages = images.stream().filter(t -> StringUtils.indexOf(t, "/aliexpress/") != -1).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(smtImages)){
                throw new Exception("成人分类无smt图片！");
            }
        }

        //汇率
//        double exchangeRate = 0.0;
//        if(tg){
//            String format = DateUtils.format(new Date(), "yyyy-MM-dd");
//            ApiResult<Double> apiResult = saleClient.getExchangeRate("USD", "CNY", format);
//            if(!apiResult.isSuccess()){
//                log.error("汇率接口调用失败:" + apiResult.getErrorMsg());
//                throw new Exception("汇率接口调用失败:" + apiResult.getErrorMsg());
//            }
//            exchangeRate = apiResult.getResult();
//        }

        String skuPrefix = saleAccountAndBusiness.getSellerSkuPrefix();
        if(StringUtils.isEmpty(skuPrefix)){
            skuPrefix = "";
        }
        Integer categoryId = autoTemplate.getCategoryId();

        //调用是否支持新尺码，如过是就必须只能用 新尺码的属性
        SizeCharOpenCall sizeCharOpenCall = new SizeCharOpenCall();
        ResponseJson supportnewSizeChartRsp = sizeCharOpenCall.isSupportnewSizeChartList(saleAccountAndBusiness, categoryId.toString());
        if(!supportnewSizeChartRsp.isSuccess()){
            throw new Exception("调用是否支持新尺码表异常 " + supportnewSizeChartRsp.getMessage());
        }
        String supportnewSizeRsp = supportnewSizeChartRsp.getMessage();
        JSONObject jsonObject = JSONObject.parseObject(supportnewSizeRsp);
        boolean isNewSize = jsonObject.getBooleanValue("isSuccess"); // 是否支持新尺码

        if(StringUtils.isEmpty(synchAttributes)){
            //获取平台的属性值
            CategoryOpenCall call = new CategoryOpenCall();
            String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, categoryId.toString());
            synchAttributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
        }
        AttributeBean attributeBean = JSON.parseObject(synchAttributes, new TypeReference<>() {
        });

        //en -> 平台id值
        Map<String, String> platEnToIdMap = new HashMap<>();

        //en -> values
        Map<String, List<AttributeValueJson>> platEnToValuesMap = new HashMap<>();
        Map<String, Long> idMap = new HashMap<>();

        //是否支持图片
        Map<Long, Boolean> picMap = new HashMap<>();

        //平台属性集合
        List<String> platformAttrEnList = new ArrayList<>();

        //单独存储支持图片的属性
        List<String> picEnList = new ArrayList<>();

        //新尺码 id 不能设置自定义数值
        Set<Long> newSizeIdSet = new HashSet<>();

        //发货地属性
        AeopSkuProperty seedAeopSkuProperty = null;

        List<AttributeJson> attributesList = attributeBean.getAttributes();
        for (AttributeJson attributeJson : attributesList) {
            //父id
            Long id = attributeJson.getId();
            //忽略发货地属性
            if (id.longValue() == PreCheckUtils.sendAttrId) {
                List<AttributeValueJson> values = attributeJson.getValues();
                for (int i = 0; i < values.size(); i++) {
                    AttributeValueJson attributeValueJson = values.get(i);
                    JSONObject names = attributeValueJson.getNames();
                    String en = names.getString("en");
                    if(StringUtils.equalsIgnoreCase(en, "CHINA")){
                        seedAeopSkuProperty = new AeopSkuProperty();
                        seedAeopSkuProperty.setSku_property_id(PreCheckUtils.sendAttrId);
                        seedAeopSkuProperty.setProperty_value_id(attributeValueJson.getId());
                        seedAeopSkuProperty.setProperty_value_definition_name("");
                        seedAeopSkuProperty.setSku_image("");
                        break;
                    }
                    if(StringUtils.equalsIgnoreCase(en, "CN")){
                        seedAeopSkuProperty = new AeopSkuProperty();
                        seedAeopSkuProperty.setSku_property_id(PreCheckUtils.sendAttrId);
                        seedAeopSkuProperty.setProperty_value_id(attributeValueJson.getId());
                        seedAeopSkuProperty.setProperty_value_definition_name("");
                        seedAeopSkuProperty.setSku_image("");
                    }
                }
                continue;
            }
            JSONObject names = attributeJson.getNames();
            String en = names.getString("en");
            Boolean sku = attributeJson.getSku();
            if (sku == null || !sku) {
                continue;
            }
            //是否支持图片上传
            Boolean customizedPic = attributeJson.getCustomizedPic();
            picMap.put(id, customizedPic);
            idMap.put(en, id);
            List<AttributeValueJson> values = attributeJson.getValues();
            if (CollectionUtils.isNotEmpty(values)) {
                //为了属性匹配规则
                for (AttributeValueJson value : values) {
                    JSONObject valueNames = value.getNames();
                    if (null == valueNames) {
                        continue;
                    }
                    platformAttrEnList.add(en);
                    if(customizedPic != null && customizedPic){
                        picEnList.add(en);
                    }
                    platEnToValuesMap.put(en, values); //加入了所有的values 这个需要调整，如果是新尺码 就只能加入新尺码的values
                    break;
                }

                //新尺码，只存储常规
                List<AttributeValueJson> newValues = new ArrayList<>();
                //新尺码 均码
                List<AttributeValueJson> newOneSizeValues = new ArrayList<>();
                //所有属性存储
                for (AttributeValueJson value : values) {
                    JSONObject valueNames = value.getNames();
                    if (null == valueNames) {
                        continue;
                    }

                    //只能存储新尺码
                    if(isNewSize){
                        JSONObject valueTagsObject = JSONObject.parseObject(value.getValueTags().toString());
                        if(valueTagsObject.containsKey("size_model_value")){
                            String size_model_value = valueTagsObject.getString("size_model_value");
                            if(StringUtils.equalsIgnoreCase(size_model_value, "standard")){
                                newSizeIdSet.add(id);
                                newValues.add(value);
                            }else if(StringUtils.equalsIgnoreCase(size_model_value, "onesize")){
                                newOneSizeValues.add(value);
                            }
                        }
                    }

                    Long valueId = value.getId();
                    String valueEn = valueNames.getString("en");
                    platEnToIdMap.put(en + splitStr + valueEn, id + splitStr + valueId);
                }

                if(isNewSize && CollectionUtils.isNotEmpty(newValues)){
                    platEnToValuesMap.put(en, newValues);
                }
            }
        }

        //属性对应的value集合
        Map<String, List<String>> arrtValueMap = new HashMap<>();

        Set<String> productAttrEnList = new HashSet<>();
        //获取 产品属性集合 必须要有 enValue
        for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
            Map<String, Object> value = stringMapEntry.getValue();
            List<Map<String, String>> attMapList = (List<Map<String, String>>) value.get("saleAttContent");
            if (CollectionUtils.isNotEmpty(attMapList)) {
                for (Map<String, String> valuesMap : attMapList) {
                    String enName = valuesMap.get("enName");
                    String enValue = valuesMap.get("enValue");
                    if(StringUtils.isNotBlank(enValue)){
                        productAttrEnList.add(enName);
                        List<String> strings = arrtValueMap.get(enName);
                        if(CollectionUtils.isEmpty(strings)){
                            strings = new ArrayList<>();
                            arrtValueMap.put(enName, strings);
                        }
                        if(!strings.contains(enValue)){
                            strings.add(enValue);
                        }
                    }
                }
            }
        }

        //新的平台属性，优先支持图片的属性放在前面
        List<String> newPlatformAttrEnList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(picEnList)){
            newPlatformAttrEnList.addAll(picEnList);
        }
        for (String s : platformAttrEnList) {
            if(!picEnList.contains(s)){
                newPlatformAttrEnList.add(s);
            }
        }

        if(newPlatformAttrEnList.size() == 0 && skuToInfoMap.size() > 1){
            throw new Exception("平台类目无属性但产品sku属性有多个！请手动选择可用类目");
        }

        //平台只有一个属性，但是产品有多个属性，需要把产品的多个属性匹配到 平台属性上面
        if(newPlatformAttrEnList.size() == 1 && productAttrEnList.size() > 1){
            //有效数据
            List<TempSkuProperty> tempSkuPropertyListy = new ArrayList<>();
            List<TgTempSkuProperty> tgTempSkuPropertyListy = new ArrayList<>(); //tg用

            //平台只有唯一属性，这个就是唯一value值 每一个value值匹配一个货号，知道value分配完
            List<AttributeValueJson> value = null;
            for (Map.Entry<String, List<AttributeValueJson>> stringListEntry : platEnToValuesMap.entrySet()) {
                value = stringListEntry.getValue();
            }

            if(CollectionUtils.isEmpty(value)){
                return null;
            }

            List<Map<String, Map<String, Object>>> productAttrList = new ArrayList<>();

            //计数
            int count = 1;
            //产品map 转成list
            for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                Map<String, Map<String, Object>> map = new HashMap<>();
                map.put(stringMapEntry.getKey(), stringMapEntry.getValue());
                if(count <= value.size()){
                    productAttrList.add(map);
                }else{
                    break;
                }
                count++;
            }

            for (int i = 0; i < productAttrList.size(); i++) {
                AttributeValueJson attributeValueJson = value.get(i);
                Map<String, Map<String, Object>> stringMapMap = productAttrList.get(i);

                for (Map.Entry<String, Map<String, Object>> stringMapEntry : stringMapMap.entrySet()) {
                    //单品货号
                    String key = stringMapEntry.getKey();
                    String firstImage = AliexpressContentUtils.randomSkuMainImage(key, images);

                    Map<String, Object> sonSkuAttrMap = stringMapEntry.getValue();
                    List<Map<String, String>> attMapList = (List<Map<String, String>>) sonSkuAttrMap.get("saleAttContent");

                    if(CollectionUtils.isEmpty(attMapList)){
                        continue;
                    }

                    TgTempSkuProperty tgTempSkuProperty = new TgTempSkuProperty();
                    TempSkuProperty tempSkuProperty = new TempSkuProperty();
                    tempSkuProperty.setIpm_sku_stock(autoTemplate.getProductStock());
                    tempSkuProperty.setSku_code(skuPrefix + key);
                    if(resultMap != null){
                        BatchPriceCalculatorResponse batchPriceCalculatorResponse = resultMap.get(key);
                        if(!batchPriceCalculatorResponse.getIsSuccess()){
                            throw new Exception("算价失败" + batchPriceCalculatorResponse.getErrorMsg());
                        }

                        BigDecimal decimal = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                        if(discountRate != null){
                            //刊登到模板上的价格,需要用试算器算出来的价格/(1-折扣率)
                            BigDecimal bgDecimalPrice = decimal.divide(BigDecimal.valueOf(1).subtract(BigDecimal.valueOf(discountRate)), 6, RoundingMode.UP);
                            tempSkuProperty.setSku_price(bgDecimalPrice.setScale(2, RoundingMode.UP).doubleValue());
                        }else{
                            tempSkuProperty.setSku_price(decimal.doubleValue());
                        }
                    }

                    AeopSkuProperty aeopSkuProperty = new AeopSkuProperty();
                    //托管属性
                    TgAeopSkuProperty tgAeopSkuProperty = new TgAeopSkuProperty();

                    Long sku_property_id = idMap.get(new ArrayList<>(newPlatformAttrEnList).get(0));
                    aeopSkuProperty.setSku_property_id(sku_property_id);
                    Boolean aBoolean = picMap.get(sku_property_id.longValue());

                    if (aBoolean != null && aBoolean) {
                        aeopSkuProperty.setSku_image(firstImage);
                    }

                    if(!newSizeIdSet.contains(sku_property_id)){
                        aeopSkuProperty.setProperty_value_definition_name(skuPrefix + key);
                        tgAeopSkuProperty.setProperty_value_definition_name(skuPrefix + key);
                    }
                    aeopSkuProperty.setProperty_value_id(attributeValueJson.getId());
                    List<AeopSkuProperty> aeopSkuPropertyList = new ArrayList<>();
                    aeopSkuPropertyList.add(aeopSkuProperty);
                    if(seedAeopSkuProperty != null){
                        AeopSkuProperty copyProperty = new AeopSkuProperty();
                        BeanUtils.copyProperties(seedAeopSkuProperty, copyProperty);
                        aeopSkuPropertyList.add(copyProperty);
                    }
                    tempSkuProperty.setAeop_s_k_u_property(aeopSkuPropertyList);
                    tempSkuPropertyListy.add(tempSkuProperty);

                    tgAeopSkuProperty.setSku_property_id(sku_property_id);
                    if (aBoolean != null && aBoolean) {
                        tgAeopSkuProperty.setSku_image(firstImage);
                    }
                    tgAeopSkuProperty.setProperty_value_id(attributeValueJson.getId());
                    List<TgAeopSkuProperty> tgAeopSkuPropertyList = new ArrayList<>();
                    tgAeopSkuPropertyList.add(tgAeopSkuProperty);

                    tgTempSkuProperty.setSku_property_list(tgAeopSkuPropertyList);
                    tgTempSkuProperty.setSku_code(key); //托管不加前缀
//                    //美元转CNY
//                    double d = decimal.doubleValue();
//                    d  = d  * exchangeRate;
//                    d  = BigDecimal.valueOf(d ).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//                    tgTempSkuProperty.setSupply_price(String.valueOf(d));
                    tgTempSkuProperty.setStatus("active");
                    tgTempSkuPropertyListy.add(tgTempSkuProperty);
                }
            }

            if(tg){
                List<String> sonSkuList = new ArrayList<>();
                //获取所有的子sku集合
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();
                    sonSkuList.add(key);
                }

                ResponseJson skuInfos = ProductUtils.findSkuInfos(sonSkuList);
                if (!StatusCode.SUCCESS.equals(skuInfos.getStatus())) {
                    log.error("获取产品信息失败:" + skuInfos.getMessage());
                    throw new Exception("获取产品信息失败:" + skuInfos.getMessage());
                }

                List<ProductInfo> productInfos = (List<ProductInfo>)skuInfos.getBody().get(ProductUtils.resultKey);
                Map<String, ProductInfo> sonSkuMap = new HashMap<>();
                for (ProductInfo productInfo : productInfos) {
                    sonSkuMap.put(productInfo.getSonSku().toUpperCase(), productInfo);
                }
                List<AliexpressConfig> aliexpressConfigList = getAliexpressConfig(autoTemplate.getAliexpressAccountNumber());
                if (!aliexpressConfigList.isEmpty()) {
                    AliexpressConfig aliexpressConfig = aliexpressConfigList.get(0);
                    Double tgProfitRate = aliexpressConfig.getTgProfitRate();
                    if (tgProfitRate != null) {
                        double profitMargin = tgProfitRate;
                        // 算价
                        for (TgTempSkuProperty tgTempSkuProperty : tgTempSkuPropertyListy) {
                            ProductInfo productInfo = sonSkuMap.get(tgTempSkuProperty.getSku_code().toUpperCase());
                            if (productInfo != null) {
                                //单位g
                                Double maxWeight = AliexpressWeightUtils.getMaxWeight(productInfo, null);
                                //重量
                                double weight = NumberUtils.format(maxWeight);
                                //产品成本价
                                Double cost = productInfo.getCost() == null ? 0.00 : productInfo.getCost().doubleValue();;
                                //采购运费
                                Double purchaseFreight = productInfo.getShippingCost() == null ? 0.00 : productInfo.getShippingCost().doubleValue();
                                //供货价
                                double supplyPrice = NumberUtils.format(weight * 0.06 + (cost + purchaseFreight + 0.2) / (1 - profitMargin));
                                tgTempSkuProperty.setSupply_price(String.valueOf(supplyPrice));
                            }
                        }
                    }
                }
            }

            return tg ? JSON.toJSONString(tgTempSkuPropertyListy) : JSON.toJSONString(tempSkuPropertyListy);
        }else{
            //属性配对，产品属性 对应 平台属性
            Map<String, String> productAttrEnToPlatformAttrEnMap = AliexpressTemplateDataUtils
                    .attrMatching(new ArrayList<>(productAttrEnList), newPlatformAttrEnList);

            //通过产品属性name_value 匹配 AeopSkuProperty 值
            Map<String, AeopSkuProperty> aeopSkuPropertyMap = new HashMap<>();
            Map<String, TgAeopSkuProperty> tgAeopSkuPropertyMap = new HashMap<>(); //tg属性

            //属性值 已使用 模糊匹配的时候判断
            Set<Long> usedId = new HashSet<>();

            //通过属性 来确定平台的值
            for (Map.Entry<String, String> stringStringEntry : productAttrEnToPlatformAttrEnMap.entrySet()) {
                String productAttrName = stringStringEntry.getKey(); //产品属性
                String platformAttrEn = stringStringEntry.getValue(); //平台属性

                //先循环完所有产品 保证精确匹配的先匹配上，匹配不到的值在进行模糊匹配
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    //单品货号
                    String key = stringMapEntry.getKey();
                    Map<String, Object> sonSkuAttrMap = stringMapEntry.getValue();
                    // 随机生成子sku图片
                    String firstImage = AliexpressContentUtils.randomSkuMainImage(key, images);
                    List<Map<String, String>> attMapList = (List<Map<String, String>>) sonSkuAttrMap.get("saleAttContent");

                    if(CollectionUtils.isEmpty(attMapList)){
                        continue;
                    }

                    for (Map<String, String> valuesMap : attMapList) {
                        String enName = valuesMap.get("enName");
                        if(!StringUtils.equalsIgnoreCase(enName, productAttrName)){
                            continue;
                        }

                        String productAttrValue = valuesMap.get("enValue"); //产品属性 -> 产品属性value值
                        if(StringUtils.isBlank(productAttrValue)){
                            continue;
                        }
                        String aeopSkuPropertyKey = productAttrValue; //换成value当key 方便匹配属性
                        AeopSkuProperty aeopSkuProperty = aeopSkuPropertyMap.get(aeopSkuPropertyKey);
                        if(aeopSkuProperty == null){
                            aeopSkuProperty = new AeopSkuProperty();
                            TgAeopSkuProperty tgAeopSkuProperty = new TgAeopSkuProperty();
                            //平台属性对应的valuejson
                            List<AttributeValueJson> platformAttrValueJsons = platEnToValuesMap.get(platformAttrEn);
                            for (AttributeValueJson platformAttrValueJson : platformAttrValueJsons) {
                                JSONObject valueNames = platformAttrValueJson.getNames();
                                Long valueId = platformAttrValueJson.getId();
                                String valueEn = valueNames.getString("en");//平台属性值 en
                                if (StringUtils.equalsIgnoreCase(productAttrValue, valueEn)) { //精确匹配到值
                                    Long sku_property_id = idMap.get(platformAttrEn);
                                    aeopSkuProperty.setSku_property_id(sku_property_id);
                                    Boolean aBoolean = picMap.get(aeopSkuProperty.getSku_property_id().longValue());

                                    if (aBoolean != null && aBoolean) {
                                        aeopSkuProperty.setSku_image(firstImage);
                                        tgAeopSkuProperty.setSku_image(firstImage);
                                    }

                                    if(!newSizeIdSet.contains(sku_property_id)){
                                        aeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                        tgAeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                    }
                                    aeopSkuProperty.setProperty_value_id(valueId);

                                    tgAeopSkuProperty.setSku_property_id(sku_property_id);
                                    tgAeopSkuProperty.setProperty_value_id(valueId);
                                    tgAeopSkuProperty.setImage_url_list(null);//图片预留
                                    tgAeopSkuPropertyMap.put(aeopSkuPropertyKey, tgAeopSkuProperty);

                                    //加入到已使用
                                    usedId.add(valueId);
                                    aeopSkuPropertyMap.put(aeopSkuPropertyKey, aeopSkuProperty);
                                    break;
                                }
                            }
                        }
                    }
                }//end 精确匹配完成

                //模糊匹配
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();
                    Map<String, Object> sonSkuAttrMap = stringMapEntry.getValue();

                    String firstImage = AliexpressContentUtils.randomSkuMainImage(key, images);
                    List<Map<String, String>> attMapList = (List<Map<String, String>>) sonSkuAttrMap.get("saleAttContent");

                    if(CollectionUtils.isEmpty(attMapList)){
                        continue;
                    }

                    for (Map<String, String> valuesMap : attMapList) {
                        String enName = valuesMap.get("enName");
                        if(!StringUtils.equalsIgnoreCase(enName, productAttrName)){
                            continue;
                        }

                        String productAttrValue = valuesMap.get("enValue"); //产品属性 -> 产品属性value值
                        if(StringUtils.isBlank(productAttrValue)){
                            continue;
                        }
                        String aeopSkuPropertyKey = productAttrValue; //换成value当key 方便匹配属性
                        AeopSkuProperty aeopSkuProperty = aeopSkuPropertyMap.get(aeopSkuPropertyKey);
                        if(aeopSkuProperty == null){
                            aeopSkuProperty = new AeopSkuProperty();
                            TgAeopSkuProperty tgAeopSkuProperty = new TgAeopSkuProperty();
                            //平台属性对应的valuejson
                            List<AttributeValueJson> platformAttrValueJsons = platEnToValuesMap.get(platformAttrEn);
                            for (AttributeValueJson platformAttrValueJson : platformAttrValueJsons) {
                                JSONObject valueNames = platformAttrValueJson.getNames();
                                Long valueId = platformAttrValueJson.getId();

                                //已使用
                                if(usedId.contains(valueId)){
                                    continue;
                                }

                                String valueEn = valueNames.getString("en");//平台属性值 en

                                if (StringUtils.contains(productAttrValue, valueEn) || StringUtils.contains(valueEn, productAttrValue)) { //模糊匹配到值
                                    Long sku_property_id = idMap.get(platformAttrEn);
                                    aeopSkuProperty.setSku_property_id(sku_property_id);
                                    Boolean aBoolean = picMap.get(aeopSkuProperty.getSku_property_id().longValue());

                                    if (aBoolean != null && aBoolean) {
                                        aeopSkuProperty.setSku_image(firstImage);
                                        tgAeopSkuProperty.setSku_image(firstImage);
                                    }
                                    if(!newSizeIdSet.contains(sku_property_id)){
                                        aeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                        tgAeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                    }
                                    aeopSkuProperty.setProperty_value_id(valueId);

                                    tgAeopSkuProperty.setSku_property_id(sku_property_id);
                                    tgAeopSkuProperty.setProperty_value_id(valueId);
                                    tgAeopSkuProperty.setImage_url_list(null);//图片预留
                                    tgAeopSkuPropertyMap.put(aeopSkuPropertyKey, tgAeopSkuProperty);

                                    //加入到已使用
                                    usedId.add(valueId);
                                    aeopSkuPropertyMap.put(aeopSkuPropertyKey, aeopSkuProperty);
                                    break;
                                }
                            }
                        }
                    }
                }//end 模糊匹配完成

                //随机匹配
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();
                    Map<String, Object> sonSkuAttrMap = stringMapEntry.getValue();

                    String firstImage = AliexpressContentUtils.randomSkuMainImage(key, images);

                    List<Map<String, String>> attMapList = (List<Map<String, String>>) sonSkuAttrMap.get("saleAttContent");
                    if(CollectionUtils.isEmpty(attMapList)){
                        continue;
                    }
                    for (Map<String, String> valuesMap : attMapList) {
                        String enName = valuesMap.get("enName");
                        if(!StringUtils.equalsIgnoreCase(enName, productAttrName)){
                            continue;
                        }

                        String productAttrValue = valuesMap.get("enValue"); //产品属性 -> 产品属性value值
                        if(StringUtils.isBlank(productAttrValue)){
                            continue;
                        }
                        String aeopSkuPropertyKey = productAttrValue; //换成value当key 方便匹配属性
                        AeopSkuProperty aeopSkuProperty = aeopSkuPropertyMap.get(aeopSkuPropertyKey);
                        if(aeopSkuProperty == null){
                            aeopSkuProperty = new AeopSkuProperty();
                            TgAeopSkuProperty tgAeopSkuProperty = new TgAeopSkuProperty();
                            //平台属性对应的valuejson
                            List<AttributeValueJson> platformAttrValueJsons = platEnToValuesMap.get(platformAttrEn);
                            for (AttributeValueJson platformAttrValueJson : platformAttrValueJsons) {
                                JSONObject valueNames = platformAttrValueJson.getNames();
                                Long valueId = platformAttrValueJson.getId();
                                //已使用
                                if(usedId.contains(valueId)){
                                    continue;
                                }
                                String valueEn = valueNames.getString("en");//平台属性值 en

                                Long sku_property_id = idMap.get(platformAttrEn);
                                aeopSkuProperty.setSku_property_id(sku_property_id);
                                Boolean aBoolean = picMap.get(aeopSkuProperty.getSku_property_id().longValue());

                                if (aBoolean != null && aBoolean) {
                                    aeopSkuProperty.setSku_image(firstImage);
                                    tgAeopSkuProperty.setSku_image(firstImage);
                                }
                                if(!newSizeIdSet.contains(sku_property_id)){
                                    aeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                    tgAeopSkuProperty.setProperty_value_definition_name(productAttrValue);
                                }
                                aeopSkuProperty.setProperty_value_id(valueId);

                                tgAeopSkuProperty.setSku_property_id(sku_property_id);
                                tgAeopSkuProperty.setProperty_value_id(valueId);
                                tgAeopSkuProperty.setImage_url_list(null);//图片预留
                                tgAeopSkuPropertyMap.put(aeopSkuPropertyKey, tgAeopSkuProperty);

                                //加入到已使用
                                usedId.add(valueId);
                                aeopSkuPropertyMap.put(aeopSkuPropertyKey, aeopSkuProperty);
                                break;
                            }
                        }
                    }
                }//end 随机匹配完成

            }// end 循环

            List<List<String>> listAll = new ArrayList<>();
            //属性集合
            for (Map.Entry<String, List<String>> stringListEntry : arrtValueMap.entrySet()) {
                List<String> vList = stringListEntry.getValue();
                listAll.add(vList);
            }

            List<List<String>> finalList = new LinkedList<>();
            if(CollectionUtils.isNotEmpty(listAll)){
                getChildList(0, listAll, finalList, new LinkedList<>());
            }

            //有效数据
            List<TempSkuProperty> usableList = new ArrayList<>(); //正常刊登用
            List<TgTempSkuProperty> tgUsableList = new ArrayList<>(); // tg用

            Set<String> useSku = new HashSet<>();

            //匹配属性 sku不能重复
            for (List<String> enValueList : finalList) {
                //配对属性次数
                int matchingCount = 0;
                //配对货号
                String matchingSku = "";

                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    //货号
                    String key = stringMapEntry.getKey();

                    if(useSku.contains(key)){
                        continue;
                    }

                    Map<String, Object> sonSkuAttrMap = stringMapEntry.getValue();
                    List<Map<String, String>> attMapList = (List<Map<String, String>>) sonSkuAttrMap.get("saleAttContent");
                    if(CollectionUtils.isEmpty(attMapList)){
                        continue;
                    }

                    List<String> enValueNewList = enValueList.stream().collect(Collectors.toList());
                    List<String> skuEnValueList = new ArrayList<>();
                    //
                    for (Map<String, String> valuesMap : attMapList) {
                        String skuEnValue = valuesMap.get("enValue");
                        if(StringUtils.isNotBlank(skuEnValue)){
                            skuEnValueList.add(skuEnValue);
                        }
                    }
                    //交集
                    enValueNewList.retainAll(skuEnValueList);

                    if(enValueNewList.size() > matchingCount){
                        matchingCount = enValueNewList.size();
                        matchingSku = key;
                    }
                }

                TempSkuProperty tempSkuProperty = new TempSkuProperty();
                TgTempSkuProperty tgTempSkuProperty = new TgTempSkuProperty();

                tempSkuProperty.setIpm_sku_stock(autoTemplate.getProductStock());
                if(StringUtils.isNotBlank(matchingSku)){
                    useSku.add(matchingSku);
                    tempSkuProperty.setSku_code(skuPrefix + matchingSku);
                    if(resultMap != null){
                        BatchPriceCalculatorResponse batchPriceCalculatorResponse = resultMap.get(matchingSku);
                        if(!batchPriceCalculatorResponse.getIsSuccess()){
                            throw new Exception("算价失败" + batchPriceCalculatorResponse.getErrorMsg());
                        }

                        BigDecimal decimal = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                        if(discountRate != null){
                            //刊登到模板上的价格,需要用试算器算出来的价格/(1-折扣率)
                            BigDecimal bgDecimalPrice = decimal.divide(BigDecimal.valueOf(1).subtract(BigDecimal.valueOf(discountRate)), 6, RoundingMode.UP);
                            tempSkuProperty.setSku_price(bgDecimalPrice.setScale(2, RoundingMode.UP).doubleValue());
                        }else{
                            tempSkuProperty.setSku_price(decimal.doubleValue());
                        }

                    }
                    tgTempSkuProperty.setSku_code(matchingSku);

//                    //美元转CNY
//                    double d = decimal.doubleValue();
//                    d  = d  * exchangeRate;
//                    d  = BigDecimal.valueOf(d ).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//                    tgTempSkuProperty.setSupply_price(String.valueOf(d));
                    tgTempSkuProperty.setStatus("active");
                }

                List<AeopSkuProperty> aeopSkuPropertyList = new ArrayList<>();
                List<TgAeopSkuProperty> tgAeopSkuPropertyList = new ArrayList<>();
                for (String s : enValueList) {
                    AeopSkuProperty aeopSkuProperty = aeopSkuPropertyMap.get(s);
                    if(aeopSkuProperty != null){
                        AeopSkuProperty newAeopSkuProperty = new AeopSkuProperty();
                        BeanUtils.copyProperties(aeopSkuProperty, newAeopSkuProperty);
                        aeopSkuPropertyList.add(newAeopSkuProperty);
                    }

                    TgAeopSkuProperty tgAeopSkuProperty = tgAeopSkuPropertyMap.get(s);
                    if(tgAeopSkuProperty != null){
                        TgAeopSkuProperty newTgAeopSkuProperty = new TgAeopSkuProperty();
                        BeanUtils.copyProperties(tgAeopSkuProperty, newTgAeopSkuProperty);
                        tgAeopSkuPropertyList.add(newTgAeopSkuProperty);
                    }

                }

                //默认加上发货地属性
                if(seedAeopSkuProperty != null){
                    AeopSkuProperty copyProperty = new AeopSkuProperty();
                    BeanUtils.copyProperties(seedAeopSkuProperty, copyProperty);
                    aeopSkuPropertyList.add(copyProperty);
                }
                tempSkuProperty.setAeop_s_k_u_property(aeopSkuPropertyList);
                usableList.add(tempSkuProperty);

                tgTempSkuProperty.setSku_property_list(tgAeopSkuPropertyList);
                tgUsableList.add(tgTempSkuProperty);
            }

            //单属性
            if(!tg && CollectionUtils.isEmpty(usableList) && skuToInfoMap.size() == 1){
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();

                    TempSkuProperty tempSkuProperty = new TempSkuProperty();
                    tempSkuProperty.setIpm_sku_stock(autoTemplate.getProductStock());
                    tempSkuProperty.setSku_code(skuPrefix + key);

                    List<AeopSkuProperty> aeopSkuPropertyList = new ArrayList<>();
                    //默认加上发货地属性
                    if(seedAeopSkuProperty != null){
                        AeopSkuProperty copyProperty = new AeopSkuProperty();
                        BeanUtils.copyProperties(seedAeopSkuProperty, copyProperty);
                        aeopSkuPropertyList.add(copyProperty);
                    }
                    tempSkuProperty.setAeop_s_k_u_property(aeopSkuPropertyList);

                    if(resultMap != null){
                        BatchPriceCalculatorResponse batchPriceCalculatorResponse = resultMap.get(key);
                        if(!batchPriceCalculatorResponse.getIsSuccess()){
                            throw new Exception("算价失败" + batchPriceCalculatorResponse.getErrorMsg());
                        }

                        BigDecimal decimal = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                        if(discountRate != null){
                            //刊登到模板上的价格,需要用试算器算出来的价格/(1-折扣率)
                            BigDecimal bgDecimalPrice = decimal.divide(BigDecimal.valueOf(1).subtract(BigDecimal.valueOf(discountRate)), 6, RoundingMode.UP);
                            tempSkuProperty.setSku_price(bgDecimalPrice.setScale(2, RoundingMode.UP).doubleValue());
                        }else{
                            tempSkuProperty.setSku_price(decimal.doubleValue());
                        }
                        usableList.add(tempSkuProperty);
                    }
                }
            }

            //单属性（tg）
            if(tg && CollectionUtils.isEmpty(tgUsableList) && skuToInfoMap.size() == 1){
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();

                    TgTempSkuProperty tgTempSkuProperty = new TgTempSkuProperty();

//                    BatchPriceCalculatorResponse batchPriceCalculatorResponse = resultMap.get(key);
//                    if(!batchPriceCalculatorResponse.getIsSuccess()){
//                        throw new Exception("算价失败" + batchPriceCalculatorResponse.getErrorMsg());
//                    }
//
//                    BigDecimal decimal = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);

                    tgTempSkuProperty.setSku_code(key);

                    //美元转CNY
//                    double d = decimal.doubleValue();
//                    d  = d  * exchangeRate;
//                    d  = BigDecimal.valueOf(d ).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//                    tgTempSkuProperty.setSupply_price(String.valueOf(d));
                    tgTempSkuProperty.setStatus("active");
                    tgUsableList.add(tgTempSkuProperty);
                }
            }

            if(tg){
                List<String> sonSkuList = new ArrayList<>();
                //获取所有的子sku集合
                for (Map.Entry<String, Map<String, Object>> stringMapEntry : skuToInfoMap.entrySet()) {
                    String key = stringMapEntry.getKey();
                    sonSkuList.add(key);
                }

                ResponseJson skuInfos = ProductUtils.findSkuInfos(sonSkuList);
                if (!StatusCode.SUCCESS.equals(skuInfos.getStatus())) {
                    log.error("获取产品信息失败:" + skuInfos.getMessage());
                    throw new Exception("获取产品信息失败:" + skuInfos.getMessage());
                }

                List<ProductInfo> productInfos = (List<ProductInfo>)skuInfos.getBody().get(ProductUtils.resultKey);
                Map<String, ProductInfo> sonSkuMap = new HashMap<>();
                for (ProductInfo productInfo : productInfos) {
                    sonSkuMap.put(productInfo.getSonSku().toUpperCase(), productInfo);
                }
                List<AliexpressConfig> aliexpressConfigList = getAliexpressConfig(autoTemplate.getAliexpressAccountNumber());
                if (!aliexpressConfigList.isEmpty()) {
                    AliexpressConfig aliexpressConfig = aliexpressConfigList.get(0);
                    Double tgProfitRate = aliexpressConfig.getTgProfitRate();
                    if (tgProfitRate != null) {
                        double profitMargin = tgProfitRate;
                        // 算价
                        for (TgTempSkuProperty tgTempSkuProperty : tgUsableList) {
                            ProductInfo productInfo = sonSkuMap.get(tgTempSkuProperty.getSku_code().toUpperCase());
                            if (productInfo != null) {
                                //单位g
                                Double maxWeight = AliexpressWeightUtils.getMaxWeight(productInfo, null);
                                //重量
                                double weight = NumberUtils.format(maxWeight);
                                //产品成本价
                                Double cost = productInfo.getCost() == null ? 0.00 : productInfo.getCost().doubleValue();;
                                //采购运费
                                Double purchaseFreight = productInfo.getShippingCost() == null ? 0.00 : productInfo.getShippingCost().doubleValue();
                                //供货价
                                double supplyPrice = NumberUtils.format(weight * 0.06 + (cost + purchaseFreight + 0.2) / (1 - profitMargin));
                                tgTempSkuProperty.setSupply_price(String.valueOf(supplyPrice));
                            }
                        }
                    }
                }
            }
            return tg ? JSON.toJSONString(tgUsableList) : JSON.toJSONString(usableList);
        }
    }


    private static void getChildList(int index, List<List<String>> listAll, List<List<String>> finalList, List<String> currList){
        if(index == listAll.size()){
            finalList.add(currList);
            return;
        }
        for (String s : listAll.get(index)){
            List<String> cList = new LinkedList<>(currList);
            cList.add(s);
            getChildList(index + 1, listAll, finalList, cList);
        }
    }

    public static void main(String[] args) {
        List<String> list1 = Arrays.asList("red", "blue");
        List<String> list2 = Arrays.asList("x", "xl");
        List<List<String>> listAll = Arrays.asList(list1, list2);

        List<List<String>> finalList = new LinkedList<>();
        getChildList(0, listAll, finalList, new LinkedList<>());
        finalList.forEach(System.out::println);
    }

}
