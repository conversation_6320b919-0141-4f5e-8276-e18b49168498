package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.erp.publish.smt.bean.CarInfoTempEntity;
import lombok.Data;

@Data
public class AliCustomTemp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ali_custom_temp.id
     */
    private Integer id;

    /**
     * 模板类型 database column ali_custom_temp.temp_type
     */
    private Integer tempType;

    /**
     * 模板名称 database column ali_custom_temp.temp_name
     */
    private String tempName;

    /**
     * 模板内容 database column ali_custom_temp.temp_json
     */
    private String tempJson;

    /**
     * 创建时间 database column ali_custom_temp.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column ali_custom_temp.create_by
     */
    private String createBy;

    /**
     * 修改时间 database column ali_custom_temp.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 修改人 database column ali_custom_temp.last_updated_by
     */
    private String lastUpdatedBy;

    /**
     * 属性(预留) database column ali_custom_temp.attribute1
     */
    private String attribute1;

    /**
     * 属性 database column ali_custom_temp.attribute2
     */
    private String attribute2;

    /**
     * 属性 database column ali_custom_temp.attribute3
     */
    private String attribute3;

    /**
     * 属性 database column ali_custom_temp.attribute4
     */
    private String attribute4;

    /**
     * 属性 database column ali_custom_temp.attribute5
     */
    private String attribute5;

    /**
     * 车型库编辑数据(用于前端回写数据)
     */
    private String editData;

    /**
     * 编辑修改 转换
     */
    private List<CarInfoTempEntity> infoList = new ArrayList<>();

}