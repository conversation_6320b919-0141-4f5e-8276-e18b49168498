package com.estone.erp.publish.smt.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleDiscountGetReqVo {
    /**
     * 店铺账号
     */
    private String accountNumber;
    /**
     * 单品折扣活动主键
     */
    private Long id;

    /**
     * 偏移量
     */
    private Integer offset;
    /**
     * 分页大小
     */
    private Integer limit;


    private List<Long> itemIds;

}
