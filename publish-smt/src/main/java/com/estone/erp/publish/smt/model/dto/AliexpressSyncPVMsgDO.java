package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-03-05 10:27
 */
@Data
public class AliexpressSyncPVMsgDO {

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 7天浏览量
     */
    private Integer view_7d_count;

    /**
     * 7天曝光量
     */
    private Integer exposure_7d_count;

    /**
     * 14天浏览量
     */
    private Integer view_14d_count;

    /**
     * 14天曝光量
     */
    private Integer exposure_14d_count;

    /**
     * 30天浏览量
     */
    private Integer view_30d_count;

    /**
     * 30天曝光量
     */
    private Integer exposure_30d_count;

    /**
     * 流量最后统计时间
     */
    private LocalDateTime lastPVUpdateTime;

    /**
     * 是否重置PV为空值
     */
    private Boolean resetPVEmpty;
}
