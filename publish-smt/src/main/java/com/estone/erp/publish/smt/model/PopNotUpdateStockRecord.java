package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class PopNotUpdateStockRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column pop_not_update_stock_record.id
     */
    private Long id;

    /**
     * 账号 database column pop_not_update_stock_record.account
     */
    private String account;

    /**
     * 产品id database column pop_not_update_stock_record.product_id
     */
    private Long productId;

    /**
     * 货号 database column pop_not_update_stock_record.article_number
     */
    private String articleNumber;

    /**
     * skuId database column pop_not_update_stock_record.sku_id
     */
    private String skuId;

    /**
     * remarks database column pop_not_update_stock_record.remarks
     */
    private String remarks;

    /**
     * 推送时间 database column pop_not_update_stock_record.create_date
     */
    private Timestamp createDate;
}