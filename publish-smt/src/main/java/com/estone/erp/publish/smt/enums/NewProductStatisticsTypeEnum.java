package com.estone.erp.publish.smt.enums;

public enum NewProductStatisticsTypeEnum {

    sale(1, "销售"),
    wenan(11, "文案"),
    platform(2, "平台");

    private int code;

    private String name;

    private NewProductStatisticsTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static NewProductStatisticsTypeEnum build(int code) {
        NewProductStatisticsTypeEnum[] values = values();
        for (NewProductStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        NewProductStatisticsTypeEnum[] values = values();
        for (NewProductStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
