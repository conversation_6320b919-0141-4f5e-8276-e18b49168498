package com.estone.erp.publish.smt.enums;

/**
 * 平台活动确认状态
 */
public enum ActivityConfirmationStatusEnum {

    /**
     * 确认状态 0-未确认 1-已确认(上传) 2-已确认(不上传)
     */

    UNCONFIRMED(0, "未确认"),
    CONFIRMED_UPLOAD(1, "已确认(上传)"),
    CONFIRMED_NO_UPLOAD(2, "已确认(不上传)"),
    CANNOT_CONFIRMED(3, "无法确认(招商截止)"),
    ;


    private int code;

    private String name;

    ActivityConfirmationStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }


    public String getName() {
        return name;
    }


}
