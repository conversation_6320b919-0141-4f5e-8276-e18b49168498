package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.elasticsearch2.model.PublishTimeSalesRangeBean;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AliexpressMarketingConfigAddOrUpdateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column aliexpress_marketing_config.id
     */
    private Integer id;



    /**
     * 规则名称 database column aliexpress_marketing_config.name
     */
    @NotBlank(message="规则名称不能为空")
    private String name;


    /**
     * 店铺 database column aliexpress_marketing_config.accounts
     */
    @NotBlank(message="店铺不能为空")
    private String accounts;

    /**
     * 规则内容 database column aliexpress_marketing_config.rule_json
     */
    @NotEmpty(message="规则内容不能为空")
    @Valid
    private List<PublishTimeSalesRangeBean> publishTimeSalesRangeBeanList;

    /**
     * 状态 0 禁用 1 启用 database column aliexpress_marketing_config.status
     */
    @NotNull(message="禁启用不能为空")
    private Integer status;

    /**
     * 移除分组 database column aliexpress_marketing_config.remove_group
     */
    @Valid
    private List<AccountNumGroupDTO> accountNumRemoveGroupDtoList;



    private String removeGroup;

    /**
     * 设置频率;every_day;every_week;every_month database column aliexpress_marketing_config.trigger_type
     */
    @NotBlank(message="设置频率必填")
    private String triggerType;

    /**
     * 每周/每月执行哪些天,多个使用逗号隔开 database column aliexpress_marketing_config.exec_days_time
     */
    private String execDaysTime;

    /**
     * 开始设置时间 database column aliexpress_marketing_config.start_time
     */
    @NotBlank(message="开始设置时间必填")
    private String startTime;

    /**
     * 计划推广时长 database column aliexpress_marketing_config.plan_days
     */
    @NotNull(message="计划推广时长必填")
    @Min(value =1,message = "计划推广时长必须为正整数")
    private Integer planDays;

    /**
     * 计划出单数量 database column aliexpress_marketing_config.plan_order_counts
     */
    @Min(value =1,message = "计划出单数量必须为正整数")
    private Integer planOrderCounts;

    /**
     * 策略开始时间
     */
    @NotNull(message="策略开始时间必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date strategyStartTime;

    /**
     * 策略结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date strategyEndTime;

    /**
     * 店铺类型 1 店铺分组 2 店铺
     */
    private Integer accountType;

    /**
     * 店铺分组名称 1 店铺分组 2 店铺
     */
    private String accountGroupName;

    /**
     * 店铺分组id
     */
    private String accountGroupId;

    @Data
    public static class AccountNumGroupDTO {
        @NotBlank(message = "店铺账号不能为空")
        private String accountNum;
        /**
         * 多个用英文逗号隔开
         */
        @NotBlank(message = "产品分组不能为空")
        private String groupIds;

        private String groupNames;
    }



}