package com.estone.erp.publish.smt.enums;

/**
 * 平台活动上传状态
 * <AUTHOR>
 */
public enum ActivityUploadStatusEnum {

    /**
     * 0-失败 1-成功 2-待上传 3-上传中 4-无需上传
     */

    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    TO_BE_UPLOADED(2, "待上传"),
    UPLOADING(3, "上传中"),
    NO_NEED_UPLOAD(4, "无需上传"),

    ;


    private int code;

    private String name;

    ActivityUploadStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }


    public String getName() {
        return name;
    }



}
