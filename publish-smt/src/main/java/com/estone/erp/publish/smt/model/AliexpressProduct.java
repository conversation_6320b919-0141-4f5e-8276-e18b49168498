package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AliexpressProduct implements Serializable, Cloneable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_product.id
     */
    private Integer id;

    /**
     * Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\textarea\style\iframe\frame\input\pre\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签. database column aliexpress_product.detail
     */
    private String detail;

    /**
     *  database column aliexpress_product.aeop_ae_product_skus_json
     */
    private String aeopAeProductSkusJson;

    /**
     * 备货期。取值范围:1-60;单位:天。 database column aliexpress_product.delivery_time
     */
    private Integer deliveryTime;

    /**
     * 服务模板设置。（需和服务模板查询接口api.queryPromiseTemplateById进行关联使用） database column aliexpress_product.promise_template_id
     */
    private Long promiseTemplateId;

    /**
     * 类目名 database column aliexpress_product.category_name
     */
    private String categoryName;

    /**
     * 类目在数据表的id database column aliexpress_product.category_table_id
     */
    private Long categoryTableId;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。 database column aliexpress_product.category_id
     */
    private Integer categoryId;

    /**
     * 	商品标题 长度在1-128之间英文。 database column aliexpress_product.subject
     */
    private String subject;

    /**
     * 商品一口价。取值范围:0-100000,保留两位小数;单位:美元。如:200.07，表示:200美元7分。需要在正确的价格区间内。上传多属性产品的时候，有好几个SKU和价格，productprice无需填写。 database column aliexpress_product.product_price
     */
    private Double productPrice;

    /**
     * 运费模版ID。通过运费接口listFreightTemplate获取。 database column aliexpress_product.freight_template_id
     */
    private Long freightTemplateId;

    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。 database column aliexpress_product.image_urls
     */
    private String imageUrls;

    /**
     * 商品单位 (存储单位编号) 100000000:袋 (bag/bags) 100000001:桶 (barrel/barrels) 100000002:蒲式耳 (bushel/bushels) 100078580:箱 (carton) 100078581:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_product.product_unit
     */
    private Integer productUnit;

    /**
     * 打包销售: true 非打包销售:false database column aliexpress_product.package_type
     */
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_product.lot_num
     */
    private Integer lotNum;

    /**
     * 商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。 database column aliexpress_product.package_length
     */
    private Integer packageLength;

    /**
     * 商品包装宽度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_width
     */
    private Integer packageWidth;

    /**
     * 商品包装高度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_height
     */
    private Integer packageHeight;

    /**
     * 商品毛重,取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_product.gross_weight
     */
    private String grossWeight;

    /**
     * 是否自定义计重.true为自定义计重,false反之. database column aliexpress_product.is_pack_sell
     */
    private Boolean isPackSell;

    /**
     * 是否自定义计重 database column aliexpress_product.is_wholesale
     */
    private Boolean isWholesale;

    /**
     * isPackSell为true时,此项必填。购买几件以内不增加运费。取值范围1-1000 database column aliexpress_product.base_unit
     */
    private Integer baseUnit;

    /**
     * isPackSell为true时,此项必填。 每增加件数.取值范围1-1000。 database column aliexpress_product.add_unit
     */
    private Integer addUnit;

    /**
     * isPackSell为true时,此项必填。 对应增加的重量.取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_product.add_weight
     */
    private String addWeight;

    /**
     * 商品有效天数。取值范围:1-30,单位:天。 database column aliexpress_product.ws_valid_num
     */
    private Integer wsValidNum;

    /**
     * 产品属性，以json格式进行封装后提交。参看aeopAeProductPropertys数据结构。此字段是否必填，需从类目接口getChildAttributesResultByPostCateIdAndPath获取（即获取到的required来判断属性是否必填），该项只输入普通类目属性数据，不可输入sku类目属性。 对于类目属性包含子类目属性的情况，此处不确认父属性和子属性，即选择任何属性，都以该对象提交。 对于一个属性多个选中值的情况，以多个该对象存放。 其中"attrNameId","attrValueId"为整型(Integer), "attrName", "attrValue"为字符串类型(String)。 1. 当设置一些系统属性时，如果类目自定义了一些候选值，只需要提供"attrNameId"和"attrValueId"即可。例如：{"attrNameId":494, "attrValueId":284}。 2. 当设置一些需要手工输入属性内容时，只需要提供"attrNameId"和"attrValue"即可。例如：{"attrNameId": 1000, "attrValue": "test"} 3. 当设置自定义属性时，需要提供"attrName"和"attrValue"即可。例如: {"attrName": "Color", "attrValue": "red"} 4. 当设置一个Other属性时，需要提供"attrNameId", "attrValueId", "attrValue"三个参数。例如：{"attrNameId": 1000, "attrValueId": 4, "attrValue": "Other Value"}。 database column aliexpress_product.aeop_ae_product_propertys_json
     */
    private String aeopAeProductPropertysJson;

    /**
     * 批发最小数量 。取值范围2-100000。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_product.bulk_order
     */
    private Integer bulkOrder;

    /**
     * 批发折扣。扩大100倍，存整数。取值范围:1-99。注意：这是折扣，不是打折率。 如,打68折,则存32。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_product.bulk_discount
     */
    private Integer bulkDiscount;

    /**
     * 尺码表模版ID。必须选择当前类目下的尺码模版。 database column aliexpress_product.size_chart_id
     */
    private Long sizeChartId;

    /**
     * 库存扣减策略，总共有2种：下单减库存(place_order_withhold)和支付减库存(payment_success_deduct)。 database column aliexpress_product.reduce_strategy
     */
    private String reduceStrategy;

    /**
     * 产品所关联的产品分组ID database column aliexpress_product.group_id
     */
    private Long groupId;

    /**
     * 产品所在的产品分组列表，多个用;分开 database column aliexpress_product.group_ids
     */
    private String groupIds;

    /**
     * 货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。 database column aliexpress_product.currency_code
     */
    private String currencyCode;

    /**
     * mobile Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\textarea\style\iframe\frame\input\pre\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签. database column aliexpress_product.mobile_detail
     */
    private String mobileDetail;

    /**
     * 卡券商品开始有效期 database column aliexpress_product.coupon_start_date
     */
    private Timestamp couponStartDate;

    /**
     * 卡券商品结束有效期 database column aliexpress_product.coupon_end_date
     */
    private Timestamp couponEndDate;

    /**
     * 商品分国家报价的配置 database column aliexpress_product.aeop_national_quote_configuration
     */
    private String aeopNationalQuoteConfiguration;

    /**
     * 商品多媒体信息，该属性主要包含商品的视频列表 database column aliexpress_product.aeop_ae_multimedia
     */
    private String aeopAeMultimedia;

    /**
     * 编辑人 database column aliexpress_product.editor
     */
    private String editor;

    /**
     * 创建时间 database column aliexpress_product.create_time
     */
    private Timestamp createTime;

    /**
     * 上次编辑时间 database column aliexpress_product.last_edit_time
     */
    private Timestamp lastEditTime;

    /**
     * 速卖通帐号 database column aliexpress_product.aliexpress_account_number
     */
    private String aliexpressAccountNumber;

    /**
     * 模版展示图 database column aliexpress_product.display_image_url
     */
    private String displayImageUrl;

    /**
     * 商品拥有者的login_id database column aliexpress_product.owner_member_id
     */
    private String ownerMemberId;

    /**
     * 商品拥有者的ID database column aliexpress_product.owner_member_seq
     */
    private Integer ownerMemberSeq;

    /**
     * 产品ID database column aliexpress_product.product_id
     */
    private Long productId;

    /**
     * 产品的来源 database column aliexpress_product.src
     */
    private String src;

    /**
     * 产品的下架日期 database column aliexpress_product.ws_offline_date
     */
    private Timestamp wsOfflineDate;

    /**
     * 产品的下架原因 database column aliexpress_product.ws_display
     */
    private String wsDisplay;

    /**
     * 产品的状态 database column aliexpress_product.product_status_type
     */
    private String productStatusType;

    /**
     * 是否是动态图产品 database column aliexpress_product.is_image_dynamic
     */
    private Boolean isImageDynamic;

    /**
     * 创建时间 database column aliexpress_product.gmt_create
     */
    private Timestamp gmtCreate;

    /**
     * 更新时间 database column aliexpress_product.gmt_modified
     */
    private Timestamp gmtModified;

    /**
     * 最小价格 database column aliexpress_product.product_min_price
     */
    private Double productMinPrice;

    /**
     * 最大价格 database column aliexpress_product.product_max_price
     */
    private Double productMaxPrice;

    /**
     * 最后同步时间 database column aliexpress_product.last_sync_time
     */
    private Timestamp lastSyncTime;

    /**
     * 单品货号，多个用“;”分开 database column aliexpress_product.article_numbers
     */
    private String articleNumbers;

    /**
     * SKU ID database column aliexpress_product.sku_id
     */
    private String skuId;

    /**
     * sku实际可售库存 database column aliexpress_product.ipm_sku_stock
     */
    private Integer ipmSkuStock;

    /**
     * sku商品编码 database column aliexpress_product.sku_code
     */
    private String skuCode;

    /**
     * sku图片 database column aliexpress_product.sku_display_img
     */
    private String skuDisplayImg;

    /**
     * sku价格 database column aliexpress_product.sku_price
     */
    private Double skuPrice;

    /**
     * sku对应的单品货号 database column aliexpress_product.article_number
     */
    private String articleNumber;

    /**
     * sku日常促销价 database column aliexpress_product.sku_discount_price
     */
    private Double skuDiscountPrice;

    /**
     * barcode database column aliexpress_product.barcode
     */
    private String barcode;

    /**
     * Sku属性对象list，允许1-3个sku属性对象，按sku属性顺序排放 database column aliexpress_product.aeop_s_k_u_property_list
     */
    private String aeopSKUPropertyList;

    /**
     * sku分国家的日常促销价 database column aliexpress_product.aeop_s_k_u_national_discount_price_list
     */
    private String aeopSKUNationalDiscountPriceList;


    private AliexpressCategory aliexpressCategory;

    private String grossProfit;

    private String grossProfitMargin;

    private Double skuTotalWeight;

    private Double skuPurchasePrice;

    private Double pmWeight;

    private Double pmPrice;

    private String lifeCyclePhase;

    private String forbiddenSaleChannel;

    private String tags;

    private String freightTemplateName;

    private String groupNames;

    /**
     * 是否多属性
     */
    private Boolean isVariant;

    /**
     * 产品标签
     */
    private String productTag;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 中文申报名
    private String declarecnname;

    //sku状态
    private String skuStatus;

    private List<String> images;

    private String operator;

    private String errorTip;

    //在线列表改价用
    private Map<String, Double> countryPriceMap = new HashMap<>();

    private String priceType;

    private List<BatchPriceCalculatorResponse> calculatorResponseList = new ArrayList<>();

    //算价用 算价国家
    private String countryCode;

    //获取模板所有sku信息
    @JSONField(serialize = false)
    public List<String> getSkuList() {

        Set<String> set = new HashSet<>();
        set.add(this.getArticleNumber());

        try{

            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, this.getAliexpressAccountNumber());

            String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();

            JSONArray jsonArray = JSON.parseArray(aeopAeProductSkusJson);
            for(int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String sku_code = jsonObject.getString("sku_code");
                if(StringUtils.isNotBlank(sku_code)){
                    if(StringUtils.isNotBlank(account.getSellerSkuPrefix()) && sku_code.startsWith(account.getSellerSkuPrefix())){
                        sku_code = sku_code.replaceFirst(account.getSellerSkuPrefix(), "");
                    }
                    set.add(sku_code);
                }
            }
            return new ArrayList<>(set);

        }catch (Exception e){
            //忽略报错
            //log.error(e.getMessage(), e);
        }
        return new ArrayList<>(set);
    }

    @Override
    public Object clone() {
        AliexpressProduct product = null;
        try {
            product = (AliexpressProduct) super.clone();
        }
        catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return product;
    }

    public String getArticleNumber() {
        return StrUtil.strTrimToUpperCase(articleNumber);
    }
}