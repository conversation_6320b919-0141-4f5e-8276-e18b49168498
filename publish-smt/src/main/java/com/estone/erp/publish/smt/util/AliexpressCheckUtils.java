package com.estone.erp.publish.smt.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.model.TemplateQueueExample;
import com.estone.erp.publish.platform.service.TemplateQueueService;
import com.estone.erp.publish.smt.call.direct.dto.pre.PopChoiceProductSkuScItemInfo;
import com.estone.erp.publish.smt.call.direct.dto.pre.PopChoiceSkuWarehouseStock;
import com.estone.erp.publish.smt.call.direct.dto.pre.PreItemSubmit;
import com.estone.erp.publish.smt.call.direct.dto.pre.ProductSku;
import com.estone.erp.publish.smt.enums.TemplateProductTypeEnum;
import com.estone.erp.publish.smt.enums.TemplateStatusEnum;
import com.estone.erp.publish.smt.enums.TemplateTgStatusEnum;
import com.estone.erp.publish.smt.model.AliexpressTemplate;
import com.estone.erp.publish.smt.model.AliexpressTemplateExample;
import com.estone.erp.publish.smt.model.AliexpressTgTemplate;
import com.estone.erp.publish.smt.model.AliexpressTgTemplateExample;
import com.estone.erp.publish.smt.service.AliexpressEsTgExtendService;
import com.estone.erp.publish.smt.service.AliexpressTemplateService;
import com.estone.erp.publish.smt.service.AliexpressTgTemplateService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/1016:34
 */
@Slf4j
public class AliexpressCheckUtils {

    public static String passKey = "pass";

    public static String noPassKey = "noPass";

    static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);

    /**
     * 是否包含侵权
     * @param skuList  只要有一个侵权就算侵权
     * @param saleChannel
     * @return
     * @throws Exception
     */
    public static Boolean isHasInfringementSales(String saleChannel, List<String> skuList) throws Exception{
        Assert.notNull(saleChannel);
        if(CollectionUtils.isEmpty(skuList)) {
            return false;
        }

        Map<String, Boolean> checkSkuMap = ProductInfringementForbiddenSaleUtils
                .checkSkuInfringementBySaleChannel(saleChannel, skuList);

        if(MapUtils.isEmpty(checkSkuMap)) {
            return false;
        }

        for (Boolean value : checkSkuMap.values()) {
            if(BooleanUtils.isTrue(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否包含禁售产品
     * @param skuList
     * @param saleChannel
     * @return
     * @throws Exception
     */
    public static Boolean isHasForbiddenSales(String saleChannel, List<String> skuList) throws Exception{
        Assert.notNull(saleChannel);
        if(CollectionUtils.isEmpty(skuList)) {
            return false;
        }

        Map<String, Boolean> checkSkuMap = ProductInfringementForbiddenSaleUtils
                .checkSkuForbiddenSalesBySaleChannel(saleChannel, skuList);

        if(MapUtils.isEmpty(checkSkuMap)) {
            return false;
        }

        for (Boolean aBoolean : checkSkuMap.values()) {
            if(BooleanUtils.isTrue(aBoolean)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询包含指定状态的sku
     * @param skuList
     * @return
     * @throws Exception
     */
    public static List<String> findSkuListByStatus(List<String> skuList, List<Integer> statusList){
        Assert.notEmpty(skuList);
        Assert.notEmpty(statusList);

        //状态int集合
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        List<String> findSkuList = singleItemEsService.getFindSkuList(skuList, statusList);
        return findSkuList;
    }

    /**
     * 数据检查  模板保存并刊登的时候, 模板更换货号场景使用
     * @param saleChannel
     * @param skuList
     * @return
     * @throws Exception
     */
    public static Boolean checkData(String saleChannel, List<String> skuList) throws Exception{
        Assert.notEmpty(skuList);
        Assert.notNull(saleChannel);

        Boolean infringementSales = isHasInfringementSales(saleChannel, skuList);
        if(BooleanUtils.isTrue(infringementSales)){
            return infringementSales;
        }

        Boolean hasForbiddenSales = isHasForbiddenSales(saleChannel, skuList);
        if(BooleanUtils.isTrue(hasForbiddenSales)){
            return hasForbiddenSales;
        }

        List<String> skuListByStatus = findSkuListByStatus(skuList, PublishCommonConstant.INTERCEPT_CODE_STATE_LIST);

        if(CollectionUtils.isNotEmpty(skuListByStatus)){
            return true;
        }
        return false;
    }

    /**
     * 拆分可用数据和不可用数据
     * @param saleChannel
     * @param skuList 只能传子sku list
     */
    public static Map<String, List<String>> splitSkuList(String saleChannel, List<String> skuList, String account) throws Exception{
        Assert.notEmpty(skuList);
        Assert.notNull(saleChannel);
        Assert.notNull(account);
        Map<String, List<String>> map = new HashMap<>();
        List<String> passSkuList = new ArrayList<>();

        String systemParamValue = "";
        SystemParam fileParam = systemParamService.querySystemParamByCodeKey("smt_filter_infringement.accounts");
        if(fileParam != null){
            systemParamValue = fileParam.getParamValue();
        }

        List<String> filterAccountList = CommonUtils.splitList(systemParamValue, ",");
        Set<String> noPassSkuSet = new HashSet<>();

        if(!filterAccountList.contains(account)){
            //不可用sku集合
            noPassSkuSet = findData(saleChannel, skuList);
        }
        map.put(noPassKey, new ArrayList<>(noPassSkuSet));
        List<String> noPassList = new ArrayList<>(noPassSkuSet);

        if(CollectionUtils.isEmpty(noPassList)){
            map.put(passKey, skuList);
        }else{
            for (String s : skuList) {
                if(!noPassList.contains(s)){
                    passSkuList.add(s);
                }
            }
            map.put(passKey, passSkuList);
        }
        return map;
    }

    public static Set<String> findData(String saleChannel, List<String> skuList) throws Exception{
        //不可用sku集合
        Set<String> noPassSkuSet = new HashSet<>();
        //侵权
        Map<String, Boolean> checkInfringementSkuMap = ProductInfringementForbiddenSaleUtils
                .checkSkuInfringementBySaleChannel(saleChannel, skuList);

        if(MapUtils.isNotEmpty(checkInfringementSkuMap)){
            for (Map.Entry<String, Boolean> map : checkInfringementSkuMap.entrySet()) {
                if(BooleanUtils.isTrue(map.getValue())){
                    noPassSkuSet.add(map.getKey());
                }
            }
        }
        //禁售
        Map<String, Boolean> checkForbiddenSkuMap = ProductInfringementForbiddenSaleUtils
                .checkSkuForbiddenSalesBySaleChannel(saleChannel, skuList);
        if(MapUtils.isNotEmpty(checkForbiddenSkuMap)){
            for (Map.Entry<String, Boolean> map : checkForbiddenSkuMap.entrySet()) {
                if(BooleanUtils.isTrue(map.getValue())){
                    noPassSkuSet.add(map.getKey());
                }
            }
        }
        List<String> skuListByStatus = findSkuListByStatus(skuList,PublishCommonConstant.INTERCEPT_CODE_STATE_LIST);

        if(CollectionUtils.isNotEmpty(skuListByStatus)){
            noPassSkuSet.addAll(skuListByStatus);
        }
        return noPassSkuSet;
    }

    /**
     * 获取sku所有的禁售平台  模板编辑页面使用
     * @param skuList
     */
    public static List<String> getAllForbiddenList(List<String> skuList) throws Exception{
        Assert.notEmpty(skuList);

        Set<String> platSet = new HashSet<>();
        Map<String, List<SalesProhibitionsVo>> forbiddenMap = ProductInfringementForbiddenSaleUtils
                .getForbiddenSalesSiteBySku(skuList);
        if(MapUtils.isEmpty(forbiddenMap)){
            return new ArrayList<>(platSet);
        }

        for (Map.Entry<String, List<SalesProhibitionsVo>> map : forbiddenMap.entrySet()) {
            List<SalesProhibitionsVo> values = map.getValue();

            for (SalesProhibitionsVo value : values) {
                String plat = value.getPlat();
                platSet.add(plat);
            }
        }
        return new ArrayList<>(platSet);
    }

    /**
     * 检查sku特殊标签
     * @param publishTemp
     * @throws Exception
     */
    public static void checkSkuSpecialTag(AliexpressTemplate publishTemp) throws Exception{
        //若sku特殊标签为FBA精铺，FZ，则过滤该产品，不允许进行刊登
        List<String> skuList = publishTemp.getSkuList();
        checkSkuSpecialTag(skuList);
    }

    public static void checkSkuSpecialTag(List<String> skuList) throws Exception{
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }
        //若sku特殊标签为FBA精铺，FZ，则过滤该产品，不允许进行刊登
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        for (String s : skuList) {
            SingleItemEs skuInfo = singleItemEsService.getSkuInfo(s);
            if(skuInfo != null){
                List<SpecialGoods> specialGoodsList = skuInfo.getSpecialGoodsList();
                Set<Integer> specialTypeSet = new HashSet<>();
                if(CollectionUtils.isNotEmpty(specialGoodsList)){
                    for (SpecialGoods specialGoods : specialGoodsList) {
                        Integer specialType = specialGoods.getSpecialType();
                        if(specialType != null && (specialType.intValue() == SpecialTagEnum.s_2023.code || specialType.intValue() == SpecialTagEnum.s_2007.code)){
                            specialTypeSet.add(specialType);

                        }
                    }
                }
                if(specialTypeSet.size() == 2){
                    throw new Exception(s +  "货号存在特殊标签FZ和FBA精铺 刊登拦截！");
                }
            }
        }
    }

    //模板刊登统一过滤校验
    public static void checkTemp(AliexpressTemplate publishTemp, List<String> passList) throws Exception{

        //说明已经 过滤非正常数据
        if(CollectionUtils.isNotEmpty(passList)){
            return;
        }
        List<String> skuList = publishTemp.getSkuList();
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }
        AliexpressTemplateService aliexpressTemplateService = SpringUtils.getBean(AliexpressTemplateService.class);

        if(CollectionUtils.isEmpty(passList)){
            Map<String, List<String>> map = splitSkuList(SaleChannel.CHANNEL_SMT, skuList, publishTemp.getAliexpressAccountNumber());
            passList = map.get(passKey);
        }

        if(CollectionUtils.isEmpty(passList)){
            throw new Exception(StringUtils.join(skuList, ",") +  "货号侵权禁售停产存档废弃！");
        }

        //固定标识
        String sign = " EEEE-SIGN";

        String aliexpressAccountNumber = publishTemp.getAliexpressAccountNumber();

        boolean isUpdate = false;

        String aeopAeProductSKUsJson = publishTemp.getAeopAeProductSkusJson();

        JSONArray newArray = new JSONArray();
        JSONArray jsonArray = JSONArray.parseArray(aeopAeProductSKUsJson);
        if (jsonArray != null) {
            int size = jsonArray.size();
            for (int i = 0; i < size; i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                JSONObject skuPropListObj = jsonObject.getJSONObject("aeop_s_k_u_property_list");
                if (null != skuPropListObj && skuPropListObj.containsKey("aeop_sku_property")) {
                    JSONArray skuPropListArr = skuPropListObj.getJSONArray("aeop_sku_property");
                    jsonObject.put("aeop_s_k_u_property", skuPropListArr);
                }

                if (jsonObject.containsKey("sku_code")) {
                    String skuCode = jsonObject.getString("sku_code");

                    //查询是否有原账号的前缀,大健云仓不需要处理
                    if(StringUtils.isNotBlank(aliexpressAccountNumber)
                            && (ObjectUtils.isEmpty(publishTemp.getProductType())
                            || !publishTemp.getProductType().equals(TemplateProductTypeEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.getCode()))){
                        SaleAccountAndBusinessResponse beforAccount = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

                        String originPrefix = beforAccount.getSellerSkuPrefix();
                        if (StringUtils.isNotBlank(originPrefix) && skuCode.startsWith(originPrefix)) {
                            skuCode = skuCode.replace(originPrefix, "");
                        }

                        if(!passList.contains(skuCode)){
                            isUpdate = true;
                            continue;
                        }else{
                            newArray.add(jsonObject);
                        }

                        if (StringUtils.isNotBlank(originPrefix)) {
                            skuCode = originPrefix + skuCode;
                        }
                    }
                    jsonObject.put("sku_code", skuCode);
                    jsonObject.put("currency_code", "USD");
                }

                jsonObject.remove("id");
                jsonObject.remove("sku_stock");
                jsonObject.remove("currency_code");
                jsonObject.remove("aeop_s_k_u_property_list");

                String systemParamValue = "";
                SystemParam fileParam = systemParamService.querySystemParamByCodeKey("smt_filter_infringement.accounts");
                if(fileParam != null) {
                    systemParamValue = fileParam.getParamValue();
                }
                List<String> filterAccountList = CommonUtils.splitList(systemParamValue, ",");
                if(!filterAccountList.contains(aliexpressAccountNumber)){
                    //自定义属性需要校验是否侵权，如果是需要加for
                    if(jsonObject.containsKey("aeop_s_k_u_property")){
                        JSONArray aeop_s_k_u_property = jsonObject.getJSONArray("aeop_s_k_u_property");
                        for (int i1 = 0; i1 < aeop_s_k_u_property.size(); i1++) {
                            JSONObject jsonObject1 = aeop_s_k_u_property.getJSONObject(i1);
                            if(jsonObject1 != null && StringUtils.isNotBlank(jsonObject1.getString("property_value_definition_name"))){
                                String keyword = jsonObject1.getString("property_value_definition_name");
                                // 获取速卖通侵权词
                                SearchVo searchVo = new SearchVo();
                                searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                                searchVo.setText(keyword);
                                ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
                                if(!checkResult.isSuccess()){
                                    throw new Exception("调用校验侵权服务 " + checkResult.getErrorMsg());
                                }

                                //收集所有的侵权词，商标词
                                Set<String> infringementSet = new HashSet<>();
                                InfringmentResponse infringmentResponse = checkResult.getResult();
                                if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                                }

                                if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                                }
                                List<String> infringementList = new ArrayList<>(infringementSet);

                                keyword = AliexpressTemplateDataUtils.tortAddFor(keyword, infringementList);

                                jsonObject1.put("property_value_definition_name", keyword);
                            }
                        }
                    }
                }
            }
            if(isUpdate){
                publishTemp.setAeopAeProductSkusJson(newArray.toJSONString());
            }else{
                publishTemp.setAeopAeProductSkusJson(jsonArray.toJSONString());
            }
        }

        if(isUpdate){
            if(publishTemp.getId() != null){
                aliexpressTemplateService.updateByPrimaryKeySelective(publishTemp);
            }
        }
    }

    //模板刊登统一过滤校验
    public static void checkTemp(AliexpressTgTemplate tgTemplate, List<String> passList) throws Exception{

        //说明已经 过滤非正常数据
        if(CollectionUtils.isNotEmpty(passList)){
            return;
        }
        List<String> skuList = tgTemplate.getSkuList();
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }
        AliexpressTgTemplateService aliexpressTgTemplateService = SpringUtils.getBean(AliexpressTgTemplateService.class);

        if(CollectionUtils.isEmpty(passList)){
            Map<String, List<String>> map = splitSkuList(SaleChannel.CHANNEL_SMT, skuList, tgTemplate.getAliexpressAccountNumber());
            passList = map.get(passKey);
        }

        if(CollectionUtils.isEmpty(passList)){
            throw new Exception(StringUtils.join(skuList, ",") +  "货号侵权禁售停产存档废弃！");
        }

        //固定标识
        String sign = " EEEE-SIGN";

        String aliexpressAccountNumber = tgTemplate.getAliexpressAccountNumber();

        boolean isUpdate = false;

        String aeopAeProductSKUsJson = tgTemplate.getAeopAeProductSkusJson();

        JSONArray newArray = new JSONArray();
        JSONArray jsonArray = JSONArray.parseArray(aeopAeProductSKUsJson);
        if (jsonArray != null) {
            int size = jsonArray.size();
            for (int i = 0; i < size; i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
//                JSONObject skuPropListObj = jsonObject.getJSONObject("aeop_s_k_u_property_list");
//                if (null != skuPropListObj && skuPropListObj.containsKey("aeop_sku_property")) {
//                    JSONArray skuPropListArr = skuPropListObj.getJSONArray("aeop_sku_property");
//                    jsonObject.put("aeop_s_k_u_property", skuPropListArr);
//                }

                if (jsonObject.containsKey("sku_code")) {
                    String skuCode = jsonObject.getString("sku_code");

                    //查询是否有原账号的前缀
                    if(StringUtils.isNotBlank(aliexpressAccountNumber)){
                        SaleAccountAndBusinessResponse beforAccount = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

                        String originPrefix = beforAccount.getSellerSkuPrefix();
                        if (StringUtils.isNotBlank(originPrefix) && skuCode.startsWith(originPrefix)) {
                            skuCode = skuCode.replace(originPrefix, "");
                        }

                        if(!passList.contains(skuCode)){
                            isUpdate = true;
                            continue;
                        }else{
                            newArray.add(jsonObject);
                        }

//                        if (StringUtils.isNotBlank(originPrefix)) {
//                            skuCode = originPrefix + skuCode;
//                        }
                    }
                    jsonObject.put("sku_code", skuCode);
//                    jsonObject.put("currency_code", "USD");
                }

//                jsonObject.remove("id");
//                jsonObject.remove("sku_stock");
//                jsonObject.remove("currency_code");
//                jsonObject.remove("aeop_s_k_u_property_list");

                //自定义属性需要校验是否侵权，如果是需要加for
                if(jsonObject.containsKey("sku_property_list")){
                    JSONArray aeop_s_k_u_property = jsonObject.getJSONArray("sku_property_list");
                    for (int i1 = 0; i1 < aeop_s_k_u_property.size(); i1++) {
                        JSONObject jsonObject1 = aeop_s_k_u_property.getJSONObject(i1);
                        if(jsonObject1 != null && StringUtils.isNotBlank(jsonObject1.getString("property_value_definition_name"))){
                            String keyword = jsonObject1.getString("property_value_definition_name");
                            // 获取速卖通侵权词
                            SearchVo searchVo = new SearchVo();
                            searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                            searchVo.setText(keyword);
                            ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
                            if(!checkResult.isSuccess()){
                                throw new Exception("调用校验侵权服务 " + checkResult.getErrorMsg());
                            }

                            //收集所有的侵权词，商标词
                            Set<String> infringementSet = new HashSet<>();
                            InfringmentResponse infringmentResponse = checkResult.getResult();
                            if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                                infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                            }

                            if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                                infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                            }
                            List<String> infringementList = new ArrayList<>(infringementSet);

                            keyword = AliexpressTemplateDataUtils.tortAddFor(keyword, infringementList);

                            jsonObject1.put("property_value_definition_name", keyword);
                        }
                    }
                }
            }
            if(isUpdate){
                tgTemplate.setAeopAeProductSkusJson(newArray.toJSONString());
            }else{
                tgTemplate.setAeopAeProductSkusJson(jsonArray.toJSONString());
            }
        }

        if(isUpdate){
            if(tgTemplate.getId() != null){
                aliexpressTgTemplateService.updateByPrimaryKeySelective(tgTemplate);
            }
        }
    }


    /**
     * 拦截状态 拦截停产 存档 废弃 true拦截
     *
     * @param itemStatus
     * @return
     */
    public static Boolean interceptSkuStatus(String itemStatus){

        List<String> interceptStatus = new ArrayList<>();
        interceptStatus.add(SkuStatusEnum.STOP.getCode());
        interceptStatus.add(SkuStatusEnum.ARCHIVED.getCode());
        interceptStatus.add(SkuStatusEnum.DISCARD.getCode());

        if(interceptStatus.contains(itemStatus)) {
            return true;
        }
        return false;
    }

    /**
     * 拦截禁售 SMT  平台禁售 true拦截
     *
     * @param saleForbiddenList
     * @return
     */
    public static Boolean interceptSaleForbidden(List<String> saleForbiddenList){
        if(CollectionUtils.isEmpty(saleForbiddenList)) {
            return false;
        }

        List<String> saleChannels = new ArrayList<>();
        saleChannels.add(SaleChannel.CHANNEL_SMT);

        for (String saleForbidden : saleForbiddenList) {
            if(saleChannels.contains(saleForbidden)) {
                return true;
            }
        }
        return false;
    }

    /**
     * SPU 过滤禁售和SKU状态  自动刊登过滤
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> filterForbiddenAndItemStatus(Map<String, SkuListAndCode> spuToCodeMap) {
        Map<String, SkuListAndCode> checkStatusAndForbiddenMap = new HashMap<>();
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();

            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if(null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()){

                SonSkuFewInfo sonSkuFewInfo = it.next();
                String itemStatus = sonSkuFewInfo.getItemStatus();
                if(interceptSkuStatus(itemStatus)) {
                    it.remove();
                    continue;
                }

                List<String> saleForbiddenList = sonSkuFewInfo.getSaleForbiddenList();
                if(interceptSaleForbidden(saleForbiddenList)) {
                    it.remove();
                    continue;
                }
            }

            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                log.warn(String.format("自动刊登 spu %s 所有子SKU 停产，存档，废弃或者平台禁售，不允许刊登", spu));
            } else {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
            }
        }

        return checkStatusAndForbiddenMap;
    }

    /**
     * 过滤侵权 自动刊登过滤
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> filterInfringement(Map<String, SkuListAndCode> spuToCodeMap, Map<String, Boolean> stringBooleanMap) {
        if(MapUtils.isEmpty(spuToCodeMap) || MapUtils.isEmpty(stringBooleanMap)) {
            return spuToCodeMap;
        }

        Map<String, SkuListAndCode> checkStatusAndForbiddenMap = new HashMap<>();
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();

            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if(null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()){
                SonSkuFewInfo sonSkuFewInfo = it.next();
                if(stringBooleanMap.containsKey(sonSkuFewInfo.getSonSku())) {
                    it.remove();
                }
            }

            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                log.warn(String.format("自动刊登 spu %s 所有子SKU 侵权，不允许刊登", spu));
            } else {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
            }
        }

        return checkStatusAndForbiddenMap;
    }

    /**
     *  获取 新品推荐 如果产品全部停产存档废弃，或者全部禁售，获取全部侵权的map
     * @param
     */
    public static Map<String, String> getNewProductRemindErrorMap(List<String> spuList) throws Exception{

        Map<String, String> resultMap = new HashMap<>();

        if(CollectionUtils.isEmpty(spuList)){
            return resultMap;
        }

        ResponseJson responseJson = ProductUtils.findSkuInfos(new ArrayList<>(spuList));
        if(!responseJson.isSuccess()){
            log.warn(responseJson.getMessage());
            return resultMap;
        }
        List<ProductInfo> productInfos = (List<ProductInfo>)responseJson.getBody().get(ProductUtils.resultKey);

        Map<String, List<ProductInfo>> infoMap = productInfos.stream()
                .collect(Collectors.groupingBy(pi -> pi.getMainSku()));

        Map<String, Boolean> stringBooleanMap = ProductInfringementForbiddenSaleUtils
                .checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SMT, spuList);

        //先过滤
        for (Map.Entry<String, List<ProductInfo>> stringListEntry : infoMap.entrySet()) {
            List<ProductInfo> value = stringListEntry.getValue();

            Set<String> errorList = new HashSet<>();

            Iterator<ProductInfo> iterator = value.iterator();
            while(iterator.hasNext()){
                ProductInfo info = iterator.next();
                if(interceptSkuStatus(info.getItemStatus())){
                    iterator.remove();
                    errorList.add("状态停产存档废弃！");
                    continue;
                }

                List<String> saleForbiddenList = info.getSaleForbiddenList();
                if(interceptSaleForbidden(saleForbiddenList)) {
                    iterator.remove();
                    errorList.add("禁售！");
                    continue;
                }

                if(stringBooleanMap.containsKey(info.getSonSku())){
                    Boolean aBoolean = stringBooleanMap.get(info.getSonSku());
                    if(aBoolean != null && aBoolean){
                        iterator.remove();
                        errorList.add("侵权！");
                        continue;
                    }
                }
            }
            if(CollectionUtils.isEmpty(value)){
                resultMap.put(stringListEntry.getKey(), String.format("spu[%s]:[%s]", stringListEntry.getKey(), StringUtils.join(errorList, ",")));
                continue;
            }
        }
        return resultMap;
    }


    /**
     * 检查sku异常状态
     * @param skus
     * @return k: sku v: 单品状态, 返回存在异常的sku与异常状态
     */
    public static Map<String,String> hasExceptionSku(List<String> skus) {
        Map<String,String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(skus)) {
            return resultMap;
        }

        try {
            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            Map<String, Integer> skuStatusMap = singleItemEsService.findSkuAndStatusBySku(skus);
            // 停产存档
            skuStatusMap.forEach((k,v)->{
                if (SingleItemEnum.STOP.isTrue(v) || SingleItemEnum.ARCHIVED.isTrue(v)) {
                    resultMap.put(k, SingleItemEnum.getEnNameByCode(v));
                }
            });
            // 侵权
            Map<String, Boolean> checkSkuMap = ProductInfringementForbiddenSaleUtils
                    .checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SMT, skus);

            checkSkuMap.forEach((k,v)->{
                if (Boolean.TRUE.equals(v)) {
                    resultMap.put(k,"侵权");
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resultMap;
    }

    //自动刊登分配的时候 校验是否重复刊登
    public static ResponseJson autoCheckSpu(String account, String spu, List<String> skuList){
        skuList.add(spu);
        EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
        AliexpressTemplateService aliexpressTemplateService = SpringUtils.getBean(AliexpressTemplateService.class);
        TemplateQueueService templateQueueService = SpringUtils.getBean(TemplateQueueService.class);
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try{
            boolean b = esAliexpressProductListingService
                    .checkIsSkuHavePublished(account, skuList);
            if(b){
                rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在产品" , StringUtils.join(skuList, ","), account));
                return rsp;
            }

            TemplateQueueExample queueExample = new TemplateQueueExample();
            queueExample.createCriteria()
                    .andSaleChannelEqualTo(SaleChannel.CHANNEL_SMT)
                    .andSellerIdEqualTo(account)
                    .andStatusNotEqualTo(QueueStatus.END.getCode())
                    .andSkuEqualTo(spu);

            int i = templateQueueService.countByExample(queueExample);
            if(i > 0){
                rsp.setMessage(String.format("货号[%s] 在账号[%s] 存在定时队列" , StringUtils.join(skuList, ","), account));
                return rsp;
            }

            //还需要验证模板是否有刊登中或者在队列中的
            AliexpressTemplateExample templateExample = new AliexpressTemplateExample();
            AliexpressTemplateExample.Criteria criteria = templateExample.createCriteria();
            criteria.andIsParentEqualTo(false);
            criteria.andCreateTimeBetween(new Timestamp(DateUtils.getDateBegin(0).getTime()), new Timestamp(DateUtils.getDateEnd(0).getTime()));

            List<Integer> statusList = new ArrayList<>(
                    Arrays.asList(TemplateStatusEnum.PUBLISHING.intCode(), TemplateStatusEnum.WAIT_QUEUE_PUBLISH.intCode(), TemplateStatusEnum.WAIT_TIMING.intCode())
            );
            criteria.andTemplateStatusIn(statusList);
            criteria.andArticleNumberIn(skuList);
            criteria.andAliexpressAccountNumberEqualTo(account);
            List<AliexpressTemplate> aliexpressTemplates = aliexpressTemplateService.timingSelectByExample(templateExample);
            if(CollectionUtils.isEmpty(aliexpressTemplates)){
                rsp.setStatus(StatusCode.SUCCESS);
            }else{
                List<Integer> idList = aliexpressTemplates.stream().map(t -> t.getId())
                        .collect(Collectors.toList());
                rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在刊登中或者刊登成功的模板[%s]" , StringUtils.join(skuList, ","), account, StringUtils.join(idList, ",")));
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    public static ResponseJson isProductHavePublished(AliexpressTemplate aliexpressTemplate){
        EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
        AliexpressTemplateService aliexpressTemplateService = SpringUtils.getBean(AliexpressTemplateService.class);
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        try{
            List<String> skuList = aliexpressTemplate.getCheckSkuList();
            if(CollectionUtils.isEmpty(skuList)){
                rsp.setMessage("没有解析到货号！");
                return rsp;
            }
            boolean b = esAliexpressProductListingService
                    .checkIsSkuHavePublished(aliexpressTemplate.getAliexpressAccountNumber(), skuList);
            if(b){
                rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在产品" , StringUtils.join(skuList, ","), aliexpressTemplate.getAliexpressAccountNumber()));
            }else{
                //还需要验证当天模板是否有刊登中或者刊登成功的模板,或者在队列中的
                AliexpressTemplateExample templateExample = new AliexpressTemplateExample();
                AliexpressTemplateExample.Criteria criteria = templateExample.createCriteria();
                criteria.andIsParentEqualTo(false);
                criteria.andCreateTimeBetween(new Timestamp(DateUtils.getDateBegin(0).getTime()), new Timestamp(DateUtils.getDateEnd(0).getTime()));

                List<Integer> statusList = new ArrayList<>(
                        Arrays.asList(TemplateStatusEnum.PUBLISH_SUCCESS.intCode()
                                , TemplateStatusEnum.PUBLISHING.intCode(), TemplateStatusEnum.WAIT_QUEUE_PUBLISH.intCode(), TemplateStatusEnum.WAIT_TIMING.intCode())
                );
                criteria.andTemplateStatusIn(statusList);
                criteria.andArticleNumberIn(skuList);
                criteria.andAliexpressAccountNumberEqualTo(aliexpressTemplate.getAliexpressAccountNumber());

                Integer id = aliexpressTemplate.getId();

                //除去自身模板id
                if(id != null){
                    criteria.andIdNotEqualTo(id);
                }

                List<AliexpressTemplate> aliexpressTemplates = aliexpressTemplateService.timingSelectByExample(templateExample);
                if(CollectionUtils.isEmpty(aliexpressTemplates)){
                    rsp.setStatus(StatusCode.SUCCESS);
                }else{

                    List<Integer> idList = aliexpressTemplates.stream().map(t -> t.getId())
                            .collect(Collectors.toList());
                    rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在刊登中或者刊登成功的模板[%s]" , StringUtils.join(skuList, ","), aliexpressTemplate.getAliexpressAccountNumber(), StringUtils.join(idList, ",")));
                }
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    public static ResponseJson isProductHavePublishedForTg(AliexpressTgTemplate aliexpressTgTemplate){
        AliexpressTgTemplateService aliexpressTgTemplateService = SpringUtils.getBean(AliexpressTgTemplateService.class);
        AliexpressEsTgExtendService aliexpressEsTgExtendService = SpringUtils.getBean(AliexpressEsTgExtendService.class);
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        try{
            List<String> skuList = aliexpressTgTemplate.getCheckSkuList();
            if(CollectionUtils.isEmpty(skuList)){
                rsp.setMessage("没有解析到货号！");
                return rsp;
            }
            boolean b = aliexpressEsTgExtendService
                    .checkIsSkuHavePublished(aliexpressTgTemplate.getAliexpressAccountNumber(), skuList);
            if(b){
                rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在产品" , StringUtils.join(skuList, ","), aliexpressTgTemplate.getAliexpressAccountNumber()));
            }else{
                //还需要验证当天模板是否有刊登中或者刊登成功的模板,或者在队列中的
                AliexpressTgTemplateExample templateTgExample = new AliexpressTgTemplateExample();
                AliexpressTgTemplateExample.Criteria criteria = templateTgExample.createCriteria();
                criteria.andIsParentEqualTo(false);
                criteria.andCreateDateBetween(DateUtils.getStringDateBegin(0), DateUtils.getStringDateEnd(0));

                List<Integer> statusList = new ArrayList<>(
                        Arrays.asList(TemplateTgStatusEnum.PUBLISH_SUCCESS.intCode()
                                , TemplateTgStatusEnum.PUBLISHING.intCode(), TemplateTgStatusEnum.WAIT_QUEUE_PUBLISH.intCode(), TemplateTgStatusEnum.WAIT_TIMING.intCode())
                );
                criteria.andTemplateStatusIn(statusList);
                criteria.andArticleNumberIn(skuList);
                criteria.andAliexpressAccountNumberEqualTo(aliexpressTgTemplate.getAliexpressAccountNumber());

                Integer id = aliexpressTgTemplate.getId();

                //除去自身模板id
                if(id != null){
                    criteria.andIdNotEqualTo(id);
                }

                List<AliexpressTgTemplate> aliexpressTgTemplates = aliexpressTgTemplateService.selectByExample(templateTgExample);
                if(CollectionUtils.isEmpty(aliexpressTgTemplates)){
                    rsp.setStatus(StatusCode.SUCCESS);
                }else{
                    List<Integer> idList = aliexpressTgTemplates.stream().map(t -> t.getId())
                            .collect(Collectors.toList());
                    rsp.setMessage(String.format("货号[%s] 在账号[%s] 已经存在刊登中或者刊登成功的模板[%s]" , StringUtils.join(skuList, ","), aliexpressTgTemplate.getAliexpressAccountNumber(), StringUtils.join(idList, ",")));
                }
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }


    /**
     * 侵权商标词校验
     * @param aliexpressTemplate
     * @return
     */
    public static ApiResult<InfringmentResponse> checkInfringWordAndBrandForTemplate(AliexpressTemplate aliexpressTemplate) {
        // 获取速卖通侵权词
        SearchVo searchVo = new SearchVo();
        searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
        String subject = aliexpressTemplate.getSubject();
        //多个空格变成一个空格
        subject = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");
        String text = subject + StrConstant.CHECK_INFRING_WORD_SPLIT + aliexpressTemplate.getDetail() + StrConstant.CHECK_INFRING_WORD_SPLIT + aliexpressTemplate.getMobileDetail();
        if(StringUtils.isNotBlank(aliexpressTemplate.getInterSubjects())){
            Map<String, String> map = JSON.parseObject(aliexpressTemplate.getInterSubjects(), Map.class);
            if(MapUtils.isNotEmpty(map)){
                for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                    String value = stringStringEntry.getValue();
                    if(StringUtils.isNotBlank(value)){
                        text = text + " " + value;
                    }
                }
            }
        }
        searchVo.setText(text);
        return InfringementUtils.checkInfringWordAndBrand(searchVo);
    }


    /**
     * 侵权商标词校验
     * @param searchVo
     * @return
     */
    public static ApiResult<InfringmentResponse> checkInfringWordAndBrand(SearchVo searchVo) {
        String text = searchVo.getText();
        text = Jsoup.parse(text).text();
        searchVo.setText(text);
        return InfringementUtils.checkInfringWordAndBrand(searchVo);
    }


    public static String halfCheck(List<ProductSku> productSkuList, String skuPrefix){
        if(StringUtils.isBlank(skuPrefix)){
            skuPrefix = "";
        }
        String tips = "";
        for (ProductSku productSku : productSkuList) {
            String skuCode = productSku.getSkuCode();
            Double packageWeight = productSku.getPackageWeight(); //kg
            if (packageWeight != null && packageWeight > 2.0) {
                tips = skuCode + " 限重2kg 实际重量:" + packageWeight + " kg";
                break;
            }
            Double packageLenght = productSku.getPackageLenght() == null ? 0.0 : productSku.getPackageLenght();
            if (packageLenght >= 58.00) {
                tips = skuCode + " L<58cm 实际长度:" + packageLenght + " cm";
                break;
            }
            Double packageWidth = productSku.getPackageWidth() == null ? 0.0 : productSku.getPackageWidth();
            Double packageHeight = productSku.getPackageHeight() == null ? 0.0 : productSku.getPackageHeight();
            double format = NumberUtils.format((packageLenght + packageWidth + packageHeight));
            if (format >= 87.00) {
                tips = skuCode + " L+W+H<87cm 实际总和:" + format + " cm";
                break;
            }

            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            //若sku特殊标签为smt半托管禁售，不允许加入
            String articleNumber = skuCode.replaceFirst(skuPrefix, "");
            SingleItemEs skuInfo = singleItemEsService.getSkuInfo(articleNumber);
            if (skuInfo != null) {
                List<SpecialGoods> specialGoodsList = skuInfo.getSpecialGoodsList();
                if (CollectionUtils.isNotEmpty(specialGoodsList)) {
                    for (SpecialGoods specialGoods : specialGoodsList) {
                        Integer specialType = specialGoods.getSpecialType();
                        if (specialType != null && (specialType.intValue() == SpecialTagEnum.s_2030.code)) {
                            tips = skuCode + " 货号存在特殊标签SMT半托管禁售 拦截";
                            break;
                        }
                    }
                }
                if (StringUtils.isNotBlank(tips)) {
                    break;
                }
            }
        }
        return tips;
    }

    /**
     * 校验数据
     * @param preItem
     * @return
     */
    public static ApiResult<Object> verifyData(PreItemSubmit preItem){
        Set<String> failMsg = new HashSet<>();
        if(preItem == null){
            failMsg.add("preItem为空");
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }
        List<ProductSku> productSkuList = preItem.getProductSkuList();
        if(CollectionUtils.isEmpty(productSkuList)){
            failMsg.add("productSkuList为空");
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }

        for (ProductSku productSku : productSkuList) {
            Double basePrice = productSku.getBasePrice();
            if(basePrice == null || basePrice <= 0.0d){
                failMsg.add("basePrice商品价格不能为空或者小于等于0");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            Double packageHeight = productSku.getPackageHeight();
            Double packageLenght = productSku.getPackageLenght();
            Double packageWidth = productSku.getPackageWidth();
            if(packageHeight == null){
                failMsg.add("packageHeight为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            if(packageLenght == null){
                failMsg.add("packageLenght为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            if(packageWidth == null){
                failMsg.add("packageWidth为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            Double packageWeight = productSku.getPackageWeight();
            if(packageWeight == null){
                failMsg.add("packageWeight为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            PopChoiceSkuWarehouseStock popChoiceSkuWarehouseStock = productSku.getPopChoiceSkuWarehouseStock();
            if(popChoiceSkuWarehouseStock == null){
                failMsg.add("popChoiceSkuWarehouseStock 仓库为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            String warehouseCode = popChoiceSkuWarehouseStock.getWarehouseCode();
            if(StringUtils.isBlank(warehouseCode)){
                failMsg.add("warehouseCode 仓库为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            Integer sellableQuantity = popChoiceSkuWarehouseStock.getSellableQuantity();
            if(sellableQuantity == null){
                failMsg.add("sellableQuantity JIT库存为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
            PopChoiceProductSkuScItemInfo popChoiceProductSkuScItemInfo = productSku.getPopChoiceProductSkuScItemInfo();
            if(popChoiceProductSkuScItemInfo == null){
                failMsg.add("popChoiceProductSkuScItemInfo 货品信息为空");
                return ApiResult.newError(JSON.toJSONString(failMsg));
            }
        }
        if(!failMsg.isEmpty()){
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }
        return ApiResult.newSuccess();

    }
}
