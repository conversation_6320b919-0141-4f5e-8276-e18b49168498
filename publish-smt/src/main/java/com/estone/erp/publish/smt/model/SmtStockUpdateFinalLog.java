package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtStockUpdateFinalLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_stock_update_final_log.id
     */
    private Long id;

    /**
     * 店铺 database column smt_stock_update_final_log.account
     */
    private String account;

    /**
     * 产品id database column smt_stock_update_final_log.product_id
     */
    private Long productId;

    /**
     * 货号 database column smt_stock_update_final_log.article_number
     */
    private String articleNumber;

    /**
     * sku状态 database column smt_stock_update_final_log.sku_status
     */
    private String skuStatus;

    /**
     * sku_id database column smt_stock_update_final_log.sku_id
     */
    private String skuId;

    /**
     * 可用库存 database column smt_stock_update_final_log.usable_stock
     */
    private Integer usableStock;

    /**
     * 代发库存 database column smt_stock_update_final_log.pending_stock
     */
    private Integer pendingStock;

    /**
     * 预扣库存 database column smt_stock_update_final_log.pre_reduction_stock
     */
    private Integer preReductionStock;

    /**
     * redis库存 database column smt_stock_update_final_log.redis_stock
     */
    private Integer redisStock;

    /**
     * 改前库存 database column smt_stock_update_final_log.stock_before
     */
    private Integer stockBefore;

    /**
     * 改后库存 database column smt_stock_update_final_log.stock_after
     */
    private Integer stockAfter;

    /**
     * sku全平台30天销量 database column smt_stock_update_final_log.order_num_30d
     */
    private Integer orderNum30d;

    /**
     * 30天动销天数（全平台） database column smt_stock_update_final_log.order_days_within_30d
     */
    private Integer orderDaysWithin30d;

    /**
     * 创建时间 database column smt_stock_update_final_log.create_date
     */
    private Timestamp createDate;

    /**
     * 更新人 database column smt_stock_update_final_log.update_by
     */
    private String updateBy;

    /**
     * eg:pop database column smt_stock_update_final_log.update_type
     */
    private String updateType;

    /**
     * 操作结果 1 成功 0 失败 2.忽略不修改 database column smt_stock_update_final_log.result_type
     */
    private Integer resultType;

    /**
     * 错误信息 database column smt_stock_update_final_log.fail_info
     */
    private String failInfo;
}