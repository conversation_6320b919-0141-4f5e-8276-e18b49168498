package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class AliexpressProductCategoryLabelDto {

    private List<Integer> idList;

    /**
     * 标签类型 database column aliexpress_product_category_label.label_type
     */
    private String labelType;

    /**
     * 类目id database column aliexpress_product_category_label.category_id
     */
    private Long categoryId;

    /**
     * 一级类目名 database column aliexpress_product_category_label.one_category_name
     */
    private String oneCategoryName;

    /**
     * 二级类目名 database column aliexpress_product_category_label.two_category_name
     */
    private String twoCategoryName;

    /**
     * 三级类目名 database column aliexpress_product_category_label.three_category_name
     */
    private String threeCategoryName;

    /**
     * 叶子类目名 database column aliexpress_product_category_label.child_category_name
     */
    private String childCategoryName;

    /**
     * category 类目  item 商品id sku sku
     */
    private String type;

    /**
     * 商品id 多个英文逗号拼接
     */
    private String productIdStr;

    /**
     * sku多个英文逗号拼接
     */
    private String skuStr;

}