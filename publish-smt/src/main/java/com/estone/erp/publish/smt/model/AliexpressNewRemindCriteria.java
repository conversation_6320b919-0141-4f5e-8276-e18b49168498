package com.estone.erp.publish.smt.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> aliexpress_new_remind
 * 2021-07-02 11:45:47
 */
@Data
public class AliexpressNewRemindCriteria extends AliexpressNewRemind {
    private static final long serialVersionUID = 1L;

    private List<Integer> idList;

    private List<String> authSellerList;

    //编辑时间
    private Timestamp fromEditFinishTime;
    private Timestamp toEditFinishTime;

    //推送时间
    private Timestamp fromPushTime;
    private Timestamp toPushTime;

    //模板创建时间
    private Timestamp fromTempFinishTime;
    private Timestamp toTempFinishTime;

    //刊登状态
    private List<Integer> publishStatusList;

    //产品标签
    private List<String> tagCodeList;

    public AliexpressNewRemindExample getExample() {
        AliexpressNewRemindExample example = new AliexpressNewRemindExample();
        AliexpressNewRemindExample.Criteria criteria = example.createCriteria();

        if(CollectionUtils.isNotEmpty(this.getTagCodeList())){
            criteria.andTagCodeLikeIn(this.getTagCodeList());
        }

        if(this.getPublishStatus() != null){
            criteria.andPublishStatusEqualTo(this.getPublishStatus());
        }
        if(CollectionUtils.isNotEmpty(this.getPublishStatusList())){
            criteria.andPublishStatusIn(this.getPublishStatusList());
        }

        if(StringUtils.isNotBlank(this.getSaleMan())){
            List<String> strings = CommonUtils.splitList(this.getSaleMan(), ",");
            if(strings.size() == 1){
                criteria.andSaleManEqualTo(this.getSaleMan());
            }else{
                criteria.andSaleManIn(strings);
            }
        }

        if(StringUtils.isNotBlank(this.getSaleLeaderMan())){
            List<String> strings = CommonUtils.splitList(this.getSaleLeaderMan(), ",");
            if(strings.size() == 1){
                criteria.andSaleManEqualTo(this.getSaleLeaderMan());
            }else{
                criteria.andSaleManIn(strings);
            }
        }

        // 文案
        if (StringUtils.isNotBlank(this.getEditor())) {
            List<String> strings = CommonUtils.splitList(this.getEditor(), ",");
            if (strings.size() == 1) {
                criteria.andEditorEqualTo(this.getEditor());
            } else {
                criteria.andEditorIn(strings);
            }
        }

        //页面勾选账号
        if (StringUtils.isNotBlank(this.getAccount())) {
            List<String> strings = CommonUtils.splitList(this.getAccount(), ",");
            if(strings.size() == 1){
                criteria.andAccountEqualTo(strings.get(0));
            }else{
                criteria.andAccountIn(strings);
            }
        }else{
            if (CollectionUtils.isNotEmpty(this.getAuthSellerList())) {
                criteria.andAccountIn(this.getAuthSellerList());
            }
        }

        //编辑时间
        if(this.getFromEditFinishTime() != null){
            criteria.andEditFinishTimeGreaterThanOrEqualTo(this.getFromEditFinishTime());
        }
        if(this.getToEditFinishTime() != null){
            criteria.andEditFinishTimeLessThanOrEqualTo(this.getToEditFinishTime());
        }

        //推送时间
        if(this.getFromPushTime() != null){
            criteria.andPushTimeGreaterThanOrEqualTo(this.getFromPushTime());
        }
        if(this.getToPushTime() != null){
            criteria.andPushTimeLessThanOrEqualTo(this.getToPushTime());
        }

        //模板完成时间
        if(this.getFromTempFinishTime() != null){
            criteria.andTempFinishTimeGreaterThanOrEqualTo(this.getFromTempFinishTime());
        }
        if(this.getToTempFinishTime() != null){
            criteria.andTempFinishTimeLessThanOrEqualTo(this.getToTempFinishTime());
        }

        if(this.getId() != null){
            criteria.andIdEqualTo(this.getId());
        }

        if(CollectionUtils.isNotEmpty(this.getIdList())){
            criteria.andIdIn(this.getIdList());
        }

        //spu
        if (StringUtils.isNotBlank(this.getSpu())) {
            List<String> strings = CommonUtils.splitList(this.getSpu(), ",");
            if(strings.size() == 1){
                criteria.andSpuEqualTo(strings.get(0));
            }else{
                criteria.andSpuIn(strings);
            }
        }


        //经营大类
        if (this.getRootCategory() != null) {
            criteria.andRootCategoryEqualTo(this.getRootCategory());
        }

        if (StringUtils.isNotBlank(this.getRootCategoryZhname())) {
            criteria.andRootCategoryZhnameEqualTo(this.getRootCategoryZhname());
        }

        if (this.getEditFinishTime() != null) {
            criteria.andEditFinishTimeEqualTo(this.getEditFinishTime());
        }

        if (this.getPushTime() != null) {
            criteria.andPushTimeEqualTo(this.getPushTime());
        }
        if (this.getIsSuccessTemp() != null) {
            criteria.andIsSuccessTempEqualTo(this.getIsSuccessTemp());
        }
        if (this.getTempFinishTime() != null) {
            criteria.andTempFinishTimeEqualTo(this.getTempFinishTime());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdateBy())) {
            criteria.andLastUpdateByEqualTo(this.getLastUpdateBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }

        example.setOrderByClause("push_time desc ");
        return example;
    }
}