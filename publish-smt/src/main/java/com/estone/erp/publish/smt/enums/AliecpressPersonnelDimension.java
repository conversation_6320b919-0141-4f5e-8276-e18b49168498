package com.estone.erp.publish.smt.enums;

public enum AliecpressPersonnelDimension {
    SUPER_ADMIN(0, "超级管理员"),
    SALE(1, "销售"),
    SALE_SUPERVISOR(3, "销售主管"),
    SALE_LEADER(2, "销售组长");

    private final int code;
    private final String description;

    AliecpressPersonnelDimension(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static AliecpressPersonnelDimension fromCode(int code) {
        for (AliecpressPersonnelDimension dimension : AliecpressPersonnelDimension.values()) {
            if (dimension.getCode() == code) {
                return dimension;
            }
        }
        throw new IllegalArgumentException("未知的人员维度: " + code);
    }
}
