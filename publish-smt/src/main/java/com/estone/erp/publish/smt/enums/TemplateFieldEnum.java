package com.estone.erp.publish.smt.enums;

import lombok.Getter;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-05-30 09:03
 **/
@Getter
public enum TemplateFieldEnum {
    ID("id", "模板编号"),
    ARTICLE_NUMBER("articleNumber", "单品货号"),
    DISPLAY_IMAGEURL("displayImageUrl", "图片"),
    SUBJECT("subject", "标题"),
    CATEGORY_ZH_NAME("categoryZhName", "速卖通类目"),
    PRODUCT_TYPE("productType", "数据来源"),
    FORBID_CHANNEL_STR("forbidChannelStr", "禁售平台"),
    INFRINGEMENT_WORD_STR("infringementWordStr", "侵权关键词"),
    TEMPLATE_STATUS("templateStatus", "刊登状态"),
    PUBLISH_ROLE("publishRole", "刊登角色"),
    TEMPLATE_TYPE("templateType", "刊登类型"),
    EXAMINE_STATE("examineState", "审核状态"),
    EXAMINE_SALEMAN("examineSaleman", "审核人"),
    EXAMINE_DATE("examineDate", "审核时间"),
    CREATOR("creator", "模板创建人"),
    LAST_EDITTIME("lastEditTime", "修改时间");

    private String code;

    private String name;

    private TemplateFieldEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }
}
