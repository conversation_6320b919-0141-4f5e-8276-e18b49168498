package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> smt_tidb_data_update_log
 * 2024-01-17 10:51:46
 */
public class SmtTidbDataUpdateLogCriteria extends SmtTidbDataUpdateLog {
    private static final long serialVersionUID = 1L;

    public SmtTidbDataUpdateLogExample getExample() {
        SmtTidbDataUpdateLogExample example = new SmtTidbDataUpdateLogExample();
        SmtTidbDataUpdateLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getRequestData())) {
            criteria.andRequestDataEqualTo(this.getRequestData());
        }
        if (this.getUploadState() != null) {
            criteria.andUploadStateEqualTo(this.getUploadState());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        return example;
    }
}