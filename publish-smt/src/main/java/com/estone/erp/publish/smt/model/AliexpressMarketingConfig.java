package com.estone.erp.publish.smt.model;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.publish.elasticsearch2.model.PublishTimeSalesRangeBean;
import com.estone.erp.publish.smt.componet.marking.MarketingConfigParam;
import com.estone.erp.publish.smt.componet.marking.PlatformActivityConfigParam;
import com.estone.erp.publish.smt.enums.AlianceMarketingConfigTypeEnum;
import com.estone.erp.publish.smt.model.dto.AliexpressMarketingConfigAddOrUpdateDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class AliexpressMarketingConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column aliexpress_marketing_config.id
     */
    private Integer id;


    /**
     * 规则名称 database column aliexpress_marketing_config.name
     */
    @NeedToLog("规则名称")
    private String name;


    /**
     * 配置类型 配置类型 1-联盟 2-平台活动
     * @see com.estone.erp.publish.smt.enums.AlianceMarketingConfigTypeEnum
     */
    private Integer type;

    /**
     * 店铺类型 1 店铺分组 2 店铺
     */
    @NeedToLog("店铺类型")
    private Integer accountType;

    /**
     * 店铺分组名称 1 店铺分组 2 店铺
     */
    @NeedToLog("店铺分组名称")
    private String accountGroupName;

    /**
     * 店铺分组id
     */
    private String accountGroupId;


    /**
     * 店铺 database column aliexpress_marketing_config.accounts
     */
    @NeedToLog("店铺")
    private String accounts;

    /**
     * 规则内容 database column aliexpress_marketing_config.rule_json
     */
    @NeedToLog("规则内容")
    private String ruleJson;

    /**
     * 状态 0 禁用 1 启用 database column aliexpress_marketing_config.status
     */
    @NeedToLog("启用状态")
    private Integer status;

    /**
     * 移除分组 database column aliexpress_marketing_config.remove_group
     */
    @NeedToLog("排除产品分组")
    private String removeGroup;

    /**
     * 设置频率;every_day;every_week;every_month database column aliexpress_marketing_config.trigger_type
     */
    @NeedToLog("设置频率")
    private String triggerType;

    /**
     * 每周/每月执行哪些天,多个使用逗号隔开 database column aliexpress_marketing_config.exec_days_time
     */
    @NeedToLog("设置时间")
    private String execDaysTime;

    /**
     * 开始设置时间 database column aliexpress_marketing_config.start_time
     */
    @NeedToLog("执行时间")
    private String startTime;

    /**
     * 策略开始时间
     */
    @NeedToLog("策略开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date strategyStartTime;

    /**
     * 策略结束时间
     */
    @NeedToLog("策略结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date strategyEndTime;


    /**
     * 计划推广时长 database column aliexpress_marketing_config.plan_days
     */
    @NeedToLog("计划推广时长")
    private Integer planDays;

    /**
     * 计划出单数量 database column aliexpress_marketing_config.plan_order_counts
     */
    @NeedToLog("计划出单数量")
    private Integer planOrderCounts;

    /**
     * 创建时间 database column aliexpress_marketing_config.created_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 创建人 database column aliexpress_marketing_config.created_by
     */
    private String createdBy;

    /**
     * 更新时间 database column aliexpress_marketing_config.updated_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 更新人 database column aliexpress_marketing_config.updated_by
     */
    private String updatedBy;

    /**
     * 单个店铺,发送MQ时用
     */
    private String singleStore;
    /**
     * 当前日期,定时任务发送MQ用
     */
    private LocalDate localDateNow;
    /**
     * 给爬虫的执行时间,定时任务发送MQ用
     */
    private LocalDateTime execTime;

    private List<PublishTimeSalesRangeBean> publishTimeSalesRangeBeanList;

    private List<AliexpressMarketingConfigAddOrUpdateDTO.AccountNumGroupDTO> accountNumRemoveGroupDtoList;

    /**
     * 配置信息
     */
    @JsonIgnore
    private MarketingConfigParam marketingConfigParam;

    public MarketingConfigParam getMarketingConfigParam() {
        Assert.notNull(type, "配置类型不能为空");
        if (AlianceMarketingConfigTypeEnum.ALIANCE.getCode().equals(type)) {
            MarketingConfigParam aliance = new PlatformActivityConfigParam();
            return aliance;
        }
        Class<? extends MarketingConfigParam> jsonClass = AlianceMarketingConfigTypeEnum.getJsonClass(type);
        if (marketingConfigParam == null) {
            marketingConfigParam = JSON.parseObject(ruleJson, jsonClass);
        }
        return jsonClass.cast(marketingConfigParam);
    }
}