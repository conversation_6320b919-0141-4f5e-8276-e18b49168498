package com.estone.erp.publish.smt.enums;

public enum AliexpressFullReductionConstEnum {
    FAULSE(0, "未更改"),
    TRUE(1, "已更改"),
    ACTIVITY_COUNTRY(2, "全部国家"),
    ACTIVITY_SCOP(3, "全店所有商品"),
    REDUCE_CONDITION_TEXT(4, "单笔订单金额大于等于（USD）"),
    REDUCE_VALUE_TEXT(5, "立减（USD）"),
    DISCOUNT_CONDITION_TEXT(6, "单笔订单件数大于等于 "),
    DISCOUNT_VALUE_TEXT(7, "折扣（%）"),
    GRADIENT_TEXT(8,"条件梯度");

    private int code;

    private String name;

    AliexpressFullReductionConstEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressFullReductionConstEnum build(int code) {
        AliexpressFullReductionConstEnum[] values = values();
        for (AliexpressFullReductionConstEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressFullReductionConstEnum[] values = values();
        for (AliexpressFullReductionConstEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }

}
