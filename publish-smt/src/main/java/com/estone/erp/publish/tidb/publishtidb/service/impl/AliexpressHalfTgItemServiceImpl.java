package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.C<PERSON>uery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.smt.bean.*;
import com.estone.erp.publish.smt.bean.excel.UpdateStockExcel;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.call.direct.half.HalfTgEditCall;
import com.estone.erp.publish.smt.call.direct.half.HalfTgListCall;
import com.estone.erp.publish.smt.call.direct.half.HalfTgStocksCall;
import com.estone.erp.publish.smt.enums.*;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.SmtStockUpdateFinalLog;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateCountryDTO;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.smt.service.SmtStockUpdateFinalLogService;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.smt.util.HalfTgUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.erpCommon.constant.ErpCommonConstant;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressHalfTgItemMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_half_tg_item
 * 2023-11-07 17:05:33
 */
@Service("aliexpressHalfTgItemService")
@Slf4j
public class AliexpressHalfTgItemServiceImpl implements AliexpressHalfTgItemService {
    @Resource
    private AliexpressHalfTgItemMapper aliexpressHalfTgItemMapper;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private SmtStockUpdateFinalLogService smtStockUpdateFinalLogService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;
    private static final String indexName = "sku_bind";
    private IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
    @Resource
    private EsSkuBindService esSkuBindService;

    @Override
    public int countByExample(AliexpressHalfTgItemExample example) {
        Assert.notNull(example, "example is null!");
        //新增默认查询在线状态
        List<AliexpressHalfTgItemExample.Criteria> oredCriteria = example.getOredCriteria();
        if(!Objects.isNull(example.getOnlineStatus())){
            if(!OnlineStatusEnum.ALL.getCode().equals(example.getOnlineStatus())){
                if(!CollectionUtils.isEmpty(oredCriteria)){
                    example.getOredCriteria().get(0).addOnlineStatusEqualTo(example.getOnlineStatus());
                }else{
                    example.createCriteria().addOnlineStatusEqualTo(example.getOnlineStatus());
                }
            }
        }else{
            if(!CollectionUtils.isEmpty(oredCriteria)){
                example.getOredCriteria().get(0).addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }else{
                example.createCriteria().addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }
        }
        return aliexpressHalfTgItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressHalfTgItem> search(CQuery<AliexpressHalfTgItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressHalfTgItemCriteria query = cquery.getSearch();
        if(query.getCategoryId() != null){
            List<Integer> allSubCategoryId = aliexpressCategoryService
                    .findAllSubCategoryId(query.getCategoryId().toString());
            query.setCategoryIdList(allSubCategoryId);
            query.setCategoryId(null);
        }
        AliexpressHalfTgItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressHalfTgItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemMapper.search(example);
        saleInfo(aliexpressHalfTgItems);
        // 组装结果
        CQueryResult<AliexpressHalfTgItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressHalfTgItems);
        return result;
    }

    public static void saleInfo(List<AliexpressHalfTgItem> aliexpressHalfTgItems){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(aliexpressHalfTgItems)){
            List<String> accountList = aliexpressHalfTgItems.stream().map(t -> t.getAccount()).distinct()
                    .collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountList, SaleChannel.CHANNEL_SMT);
            for (AliexpressHalfTgItem aliexpressHalfTgItem : aliexpressHalfTgItems) {
                //转换在线状态页面显示code->name
                aliexpressHalfTgItem.setOnlineStatus(OnlineStatusEnum.getNameByCode(aliexpressHalfTgItem.getOnlineStatus()));
                // 销售、销售组长、销售主管
                if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(aliexpressHalfTgItem.getAccount());
                    aliexpressHalfTgItem.setSalemanager(saleSuperiorTriple.getLeft());
                    aliexpressHalfTgItem.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                    aliexpressHalfTgItem.setSalesSupervisorName(saleSuperiorTriple.getRight());
                }
            }
        }

        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    @Override
    public AliexpressHalfTgItem selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressHalfTgItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Long> selectScCode(AliexpressHalfTgItemExample example) {
        //新增默认查询在线状态
        List<AliexpressHalfTgItemExample.Criteria> oredCriteria = example.getOredCriteria();
        if(!Objects.isNull(example.getOnlineStatus())){
            if(!OnlineStatusEnum.ALL.getCode().equals(example.getOnlineStatus())){
                if(!CollectionUtils.isEmpty(oredCriteria)){
                    example.getOredCriteria().get(0).addOnlineStatusEqualTo(example.getOnlineStatus());
                }else{
                    example.createCriteria().addOnlineStatusEqualTo(example.getOnlineStatus());
                }
            }
        }else{
            if(!CollectionUtils.isEmpty(oredCriteria)){
                example.getOredCriteria().get(0).addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }else{
                example.createCriteria().addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }
        }
        return aliexpressHalfTgItemMapper.selectScCode(example);
    }

    @Override
    public void batchUpdateOnlineStatus(List<Long> list) {
        aliexpressHalfTgItemMapper.batchUpdateOnlineStatus(list);
    }

    @Override
    public void exitUpdateOnlineStatus(String account, Long productId) {
        aliexpressHalfTgItemMapper.exitUpdateOnlineStatus(account, productId);
    }

    @Override
    public List<AliexpressHalfTgItem> selectByExample(AliexpressHalfTgItemExample example) {
        Assert.notNull(example, "example is null!");
        //新增默认查询在线状态
        List<AliexpressHalfTgItemExample.Criteria> oredCriteria = example.getOredCriteria();
        if(!Objects.isNull(example.getOnlineStatus())){
            if(!OnlineStatusEnum.ALL.getCode().equals(example.getOnlineStatus())){
                if(!CollectionUtils.isEmpty(oredCriteria)){
                    example.getOredCriteria().get(0).addOnlineStatusEqualTo(example.getOnlineStatus());
                }else{
                    example.createCriteria().addOnlineStatusEqualTo(example.getOnlineStatus());
                }
            }
        }else{
            if(!CollectionUtils.isEmpty(oredCriteria)){
                example.getOredCriteria().get(0).addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }else{
                example.createCriteria().addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode());
            }
        }
        return aliexpressHalfTgItemMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressHalfTgItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressHalfTgItemMapper.insert(record);
    }

    @Override
    public void batchInsert(List<AliexpressHalfTgItem> recordList){
        Assert.notNull(recordList, "recordList is null!");
        aliexpressHalfTgItemMapper.batchInsert(recordList);
    }

    @Override
    public void batchuUdate(List<AliexpressHalfTgItem> recordList){
        Assert.notNull(recordList, "recordList is null!");
        aliexpressHalfTgItemMapper.batchUpdate(recordList);
    }

    @Override
    public int updateBySkuChange(AliexpressHalfTgItem record){
        Assert.notNull(record, "record is null!");
        return aliexpressHalfTgItemMapper.updateBySkuChange(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressHalfTgItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressHalfTgItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressHalfTgItem record, AliexpressHalfTgItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressHalfTgItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressHalfTgItemMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception{
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(200);

        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        halfTgItemExample.setFields("id,article_number");
        AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
        criteria.andArticleNumberIn(skuList);
        if(CollectionUtils.isNotEmpty(accountNumberList)){
            criteria.andAccountIn(accountNumberList);
        }
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = this.selectByExample(halfTgItemExample);

        if (CollectionUtils.isEmpty(aliexpressHalfTgItems)) {
            return;
        }
        List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(skuList);
        if(CollectionUtils.isNotEmpty(singleItemEsList)) {
            for (SingleItemEs singleItemEs : singleItemEsList) {
                String sonSku = singleItemEs.getSonSku();
                if(StringUtils.isNotBlank(sonSku)) {
                    map.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                }
            }
        }

//        log.info("当前更新数量：{}，第一个id{}", aliexpressHalfTgItems.size(), aliexpressHalfTgItems.get(0).getId());
        long start1 = System.currentTimeMillis();
        aliexpressHalfTgItems.forEach(t -> {

            String articleNumber = t.getArticleNumber();
            if (StringUtils.isNotBlank(articleNumber)) {
                try {
                    ProductInfoVO productInfoVO = map.get(t.getArticleNumber());
                    if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                        return;
                    }
                    HalfTgUtils.assembleProductInfo(t);
                    this.updateByPrimaryKeySelective(t);
                } catch (Exception e) {
                    log.error(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()), e);
                    throw new RuntimeException(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()));
                }
            } else {
                log.error(String.format("产品货号[%s]为空", t.getId()));
            }
        });
        long start2 = System.currentTimeMillis() - start1;
//        log.info("当前更新数量：{}，页耗时{}ms，第一个id{}", aliexpressHalfTgItems.size(), start2, aliexpressHalfTgItems.get(0).getId());
    }

    @Override
    public void synchItem(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, HalfTgSyncProductListRequest listRequest, Long logId){
        AliexpressProductLog log = new AliexpressProductLog();
        log.setId(logId);
        log.setOperateStatus(OperateLogStatusEnum.processing.intCode());
        aliexpressProductLogService.updateByPrimaryKeySelective(log);

        HalfTgListCall tgListCall = new HalfTgListCall();
        String rsp = tgListCall.list(saleAccountAndBusinessResponse, listRequest);
        log.setResult(StringUtils.isBlank(rsp) ? true : false);
        log.setFailInfo(rsp);
        log.setOperateStatus(OperateLogStatusEnum.end.intCode());
        log.setOperateTime(new Timestamp(System.currentTimeMillis()));
        aliexpressProductLogService.updateByPrimaryKeySelective(log);
    }

    @Override
    public ResponseJson updateStocks(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemRequest> halfTgItemRequests, String userName){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        List<String> skuIdList = halfTgItemRequests.stream().map(t -> t.getSkuId()).collect(Collectors.toList());
        if(productId == null ||  CollectionUtils.isEmpty(skuIdList)){
            rsp.setMessage("参数为空！");
            return rsp;
        }

        //查询本地数据，记录改前改后日志
        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
        criteria.andProductIdEqualTo(productId);
        criteria.andSkuIdIn(skuIdList);
        halfTgItemExample.setFields("id, account, product_id, article_number, sku_code, sku_id, pop_choice_sku_warehouse_stock_list, system_stock");
        List<AliexpressHalfTgItem> dbList = this.selectByExample(halfTgItemExample);
        if(CollectionUtils.isEmpty(dbList)){
            rsp.setMessage("查询不到本地数据！");
            return rsp;
        }

        //产品 sku_id 对应的数据
        Map<String, List<AliexpressHalfTgItem>> skuIdMap = dbList.stream().collect(Collectors.groupingBy(t -> t.getSkuId()));

        String account = saleAccountAndBusinessResponse.getAccountNumber();
        List<Long> logIdList = new ArrayList<>();
        List<SmtStockUpdateFinalLog> finalLogList = new ArrayList<>(); //测试日志

        for (HalfTgItemRequest halfTgItemRequest : halfTgItemRequests) {
            String skuId = halfTgItemRequest.getSkuId();
            String warehouseCode = halfTgItemRequest.getWarehouseCode();
            Integer sellableQuantity = halfTgItemRequest.getSellableQuantity();

            AliexpressHalfTgItem dbItem = skuIdMap.get(skuId).get(0);
            String popChoiceSkuWarehouseStockList = dbItem.getPopChoiceSkuWarehouseStockList();
            JSONArray dbJsonArray = JSONObject.parseArray(popChoiceSkuWarehouseStockList);

            //本地warehouse_code 对应的数据
            Map<String, JSONObject> dbWarehouseCodeMap = new HashMap<>();
            for (int i = 0; i < dbJsonArray.size(); i++) {
                JSONObject dbJsonArrayJSONObject = dbJsonArray.getJSONObject(i);
                String warehouse_code = dbJsonArrayJSONObject.getString("warehouse_code");
                dbWarehouseCodeMap.put(warehouse_code, dbJsonArrayJSONObject);
            }
            JSONObject dbJson = dbWarehouseCodeMap.get(warehouseCode);
            Integer dbQuantity = dbJson.getInteger("sellable_quantity");
            String warehouse_name = dbJson.getString("warehouse_name");

            //已经记录日志
            AliexpressProductLog aliexpressProductLog = halfTgItemRequest.getAliexpressProductLog();
            if(aliexpressProductLog != null && aliexpressProductLog.getId() != null){
                logIdList.add(aliexpressProductLog.getId());
            }else{
                //日志是每个仓库一条
                AliexpressProductLog log = new AliexpressProductLog();
                log.setProductId(productId);
                log.setAccountNumber(account);
                log.setSkuCode(dbItem.getArticleNumber());
                log.setOperator(userName);
                log.setOperateType(OperateLogTypeEnum.edit_half_tg_stock.getCode());
                log.setStockBeforeEdit(dbQuantity.doubleValue());
                log.setStockAfterEdit(sellableQuantity.doubleValue());
                log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
                log.setNewRemark(warehouse_name);
                aliexpressProductLogService.insert(log);
                logIdList.add(log.getId());
            }

            //半托管定时任务修改，需要记录测试日志
            List<String> needFinalLogUser = Arrays.asList(AliexpressLogUtils.preReductionStockByOrderPushMqListener,
                    AliexpressLogUtils.halfTgUpdateStockZeroBySystemStockJobHandler,
                    AliexpressLogUtils.smtRuleUpdateHalfTgStockMqListener);
            if(needFinalLogUser.contains(userName)){
                SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                smtStockUpdateFinalLog.setAccount(account);
                smtStockUpdateFinalLog.setProductId(productId);
                smtStockUpdateFinalLog.setArticleNumber(dbItem.getArticleNumber());
                smtStockUpdateFinalLog.setSkuId(dbItem.getSkuId());
                smtStockUpdateFinalLog.setSkuStatus(dbItem.getSkuStatus());
                smtStockUpdateFinalLog.setUsableStock(halfTgItemRequest.getUsableStock());
                smtStockUpdateFinalLog.setPendingStock(halfTgItemRequest.getPendingStock());
                smtStockUpdateFinalLog.setPreReductionStock(halfTgItemRequest.getPreReductionStock());
                smtStockUpdateFinalLog.setRedisStock(halfTgItemRequest.getSystemStock());
                smtStockUpdateFinalLog.setStockBefore(dbQuantity);
                smtStockUpdateFinalLog.setStockAfter(sellableQuantity);
                smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                smtStockUpdateFinalLog.setUpdateBy(userName);
                String updateType = StringUtils.equals(AliexpressLogUtils.preReductionStockByOrderPushMqListener, userName) ? UpdateStockTypeEnum.halfTgByOrder.getCode() : UpdateStockTypeEnum.halfTg.getCode();
                if(AliexpressLogUtils.smtRuleUpdateHalfTgStockMqListener.equalsIgnoreCase(userName)){
                    updateType = UpdateStockTypeEnum.halfTgByRule.getCode();
                }
                smtStockUpdateFinalLog.setUpdateType(updateType);
                finalLogList.add(smtStockUpdateFinalLog);
            }
        }

        //如果修改成功 修改本地数据
        List<AliexpressHalfTgItem> dbUpdateList = new ArrayList<>(); //已sku_id 为维度

        //整合json
        JSONArray jsonArray = new JSONArray();
        Map<String, List<HalfTgItemRequest>> collect = halfTgItemRequests.stream().collect(Collectors.groupingBy(t -> t.getSkuId()));
        for (Map.Entry<String, List<HalfTgItemRequest>> stringListEntry : collect.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            String key = stringListEntry.getKey();
            jsonObject.put("sku_id", key);

            AliexpressHalfTgItem dbItem = skuIdMap.get(key).get(0);
            JSONArray updateDbArray = new JSONArray();
            List<HalfTgItemRequest> value = stringListEntry.getValue();
            JSONArray pop_choice_sku_warehouse_stock_listArray = new JSONArray();
            Integer dbSkuStock = 0;
            for (HalfTgItemRequest halfTgItemRequest : value) {
                JSONObject warehouse_codeObject = new JSONObject();
                warehouse_codeObject.put("warehouse_code", halfTgItemRequest.getWarehouseCode());
                warehouse_codeObject.put("sellable_quantity", halfTgItemRequest.getSellableQuantity());
                pop_choice_sku_warehouse_stock_listArray.add(warehouse_codeObject);

                JSONObject updateDbObject = new JSONObject();
                updateDbObject.put("warehouse_name", halfTgItemRequest.getWarehouseName());
                updateDbObject.put("warehouse_code", halfTgItemRequest.getWarehouseCode());
                updateDbObject.put("sellable_quantity", halfTgItemRequest.getSellableQuantity());
                updateDbArray.add(updateDbObject);

                dbSkuStock += halfTgItemRequest.getSellableQuantity();
            }
            dbItem.setSkuStock(dbSkuStock); //更新本地sku库存
            dbItem.setPopChoiceSkuWarehouseStockList(updateDbArray.toJSONString());
            dbUpdateList.add(dbItem);

            jsonObject.put("pop_choice_sku_warehouse_stock_list", pop_choice_sku_warehouse_stock_listArray);
            jsonArray.add(jsonObject);
        }
        return updateStocks(saleAccountAndBusinessResponse, productId, jsonArray.toJSONString(), logIdList, dbUpdateList, finalLogList);
    }

    private ResponseJson updateStocks(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String json, List<Long> logIdList, List<AliexpressHalfTgItem> dbUpdateList, List<SmtStockUpdateFinalLog> finalLogList){
        for (Long aLong : logIdList) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setId(aLong);
            log.setOperateStatus(OperateLogStatusEnum.processing.intCode());
            aliexpressProductLogService.updateByPrimaryKeySelective(log);
        }

        HalfTgStocksCall stocksCall = new HalfTgStocksCall();
        ResponseJson rsp = stocksCall.updateStock(saleAccountAndBusinessResponse, productId, json);
//        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
//        rsp.setMessage("系统认为设置失败，没有请求到平台 " + json);
        for (Long aLong : logIdList) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setId(aLong);
            log.setResult(rsp.isSuccess());
            log.setFailInfo(rsp.getMessage());
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            aliexpressProductLogService.updateByPrimaryKeySelective(log);
        }
        if(rsp.isSuccess()){
           this.batchuUdate(dbUpdateList);
        }
        //测试日志
        if(CollectionUtils.isNotEmpty(finalLogList)){
            for (SmtStockUpdateFinalLog smtStockUpdateFinalLog : finalLogList) {
                smtStockUpdateFinalLog.setResultType(rsp.isSuccess() ? UpdateStockResultEnum.success.getCode() : UpdateStockResultEnum.fail.getCode());
                smtStockUpdateFinalLog.setFailInfo(rsp.getMessage());
            }
            smtStockUpdateFinalLogService.batchInsert(finalLogList);
        }
        return rsp;
    }

    @Override
    public void updatePrices(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemRequest> halfTgItemRequests, String userName){
        List<String> skuIdList = halfTgItemRequests.stream().map(t -> t.getSkuId()).collect(Collectors.toList());
        if(productId == null ||  CollectionUtils.isEmpty(skuIdList)){
            return;
        }
        String account = saleAccountAndBusinessResponse.getAccountNumber();

        //查询本地数据，提交的skuid 如果不全 需要补充
        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
        criteria.andProductIdEqualTo(productId);
        criteria.andAccountEqualTo(account);
        List<AliexpressHalfTgItem> dbList = this.selectByExample(halfTgItemExample);
        if(CollectionUtils.isEmpty(dbList)){
            return ;
        }

        Map<String, AliexpressHalfTgItem> dbSkuIdMap = dbList.stream().collect(Collectors.toMap(t -> t.getSkuId(), t -> t, (k1, k2) -> k1));
        Map<String, HalfTgItemRequest> updateSkuIdMap = halfTgItemRequests.stream().collect(Collectors.toMap(t -> t.getSkuId(), t -> t, (k1, k2) -> k1));

        List<AliexpressProductLog> logList = new ArrayList<>();
        //记录改前改后日志
        for (HalfTgItemRequest halfTgItemRequest : halfTgItemRequests) {
            String skuId = halfTgItemRequest.getSkuId();
            Double basePrice = halfTgItemRequest.getBasePrice();
            AliexpressHalfTgItem dbItem = dbSkuIdMap.get(skuId);
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setProductId(productId);
            productLog.setAccountNumber(account);
            productLog.setSkuCode(dbItem.getArticleNumber());
            productLog.setOperator(WebUtils.getUserName());
            productLog.setOperateType(OperateLogTypeEnum.edit_half_tg_price.getCode());
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            productLog.setPriceBeforeEdit(dbItem.getBasePrice());
            productLog.setPriceAfterEdit(basePrice);
            aliexpressProductLogService.insert(productLog);
            logList.add(productLog);
        }

        //编辑json
        JSONObject pop_choice_productJson = new JSONObject();
        AliexpressHalfTgItem aliexpressHalfTgItem = dbList.get(0);

        try{
            Integer categoryId = aliexpressHalfTgItem.getCategoryId();
            String joinedCountryList = aliexpressHalfTgItem.getJoinedCountryList();
            pop_choice_productJson.put("product_id", productId);
            pop_choice_productJson.put("category_id", categoryId);
            pop_choice_productJson.put("currency_code", aliexpressHalfTgItem.getCurrencyCode());
            if(StringUtils.isNotBlank(joinedCountryList)){
                pop_choice_productJson.put("joined_country_list", joinedCountryList);
            }else{
                pop_choice_productJson.put("joined_country_list", null);
            }

            JSONArray product_sku_list = new JSONArray();

            for (AliexpressHalfTgItem halfTgItem : dbList) {
                JSONObject product_skuJson = new JSONObject();
                product_sku_list.add(product_skuJson);

                JSONObject pop_choice_product_sku_sc_item_info = new JSONObject();
                pop_choice_product_sku_sc_item_info.put("sc_item_code", halfTgItem.getScItemCode());
                pop_choice_product_sku_sc_item_info.put("sc_item_bar_code", halfTgItem.getScItemBarCode());
                pop_choice_product_sku_sc_item_info.put("original_box", halfTgItem.getOriginalBox());
                List<String> specialTypeList = halfTgItem.getSpecialTypeList();
                if(CollectionUtils.isNotEmpty(specialTypeList)){
                    pop_choice_product_sku_sc_item_info.put("special_product_type_list", specialTypeList);
                }
                product_skuJson.put("pop_choice_product_sku_sc_item_info", pop_choice_product_sku_sc_item_info);

                product_skuJson.put("package_height", halfTgItem.getPackageHeight());
                product_skuJson.put("package_weight", halfTgItem.getPackageWeight());
                product_skuJson.put("package_width", halfTgItem.getPackageWidth());
                product_skuJson.put("package_length", halfTgItem.getPackageLength());
                product_skuJson.put("sku_id", halfTgItem.getSkuId());
                product_skuJson.put("sku_code", halfTgItem.getSkuCode());

                String skuId = halfTgItem.getSkuId();
                HalfTgItemRequest itemRequest = updateSkuIdMap.get(skuId);

                //替换价格
                if(itemRequest != null && itemRequest.getBasePrice() != null){
                    product_skuJson.put("base_price", itemRequest.getBasePrice());
                }else{
                    product_skuJson.put("base_price", halfTgItem.getBasePrice());
                }
                product_skuJson.put("pop_choice_sku_warehouse_stock_list", halfTgItem.getPopChoiceSkuWarehouseStockList());
                product_skuJson.put("sku_property_list", halfTgItem.getSkuPropertyList());
            }
            pop_choice_productJson.put("product_sku_list", product_sku_list);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(false);
                productLog.setFailInfo(e.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }

        AliexpressExecutors.updateHalfTgPricePool(() ->{
            for (AliexpressProductLog productLog : logList) {
                productLog.setOperateStatus(OperateLogStatusEnum.processing.intCode());
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
            HalfTgEditCall editCall = new HalfTgEditCall();
            ResponseJson responseJson = EnvironmentSupplierWrapper.execute(()->{
                return editCall.editItem(saleAccountAndBusinessResponse, pop_choice_productJson.toJSONString());
            },()->{
                log.info("非正式环境不执行改价,productId:{}, pop_choice_productJson:{}", productId, pop_choice_productJson.toJSONString());
                ResponseJson rsp = new ResponseJson();
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage("非正式环境不执行改价");
                return rsp;
            });

            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(responseJson.isSuccess());
                productLog.setFailInfo(responseJson.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
            //修改成功后同步产品
            if(responseJson.isSuccess()){
                HalfTgListCall listCall = new HalfTgListCall();
                HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
                listRequest.setProductId(productId);
                listCall.list(saleAccountAndBusinessResponse, listRequest);
            }
        });
    }

    @Override
    public void updatePackageWeight(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemUpdatePackageWeight> halfTgItemRequests, String userName){
        if(productId == null){
            return;
        }
        String account = saleAccountAndBusinessResponse.getAccountNumber();

        //查询本地数据，提交的skuid 如果不全 需要补充
        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
        criteria.andProductIdEqualTo(productId);
        criteria.andAccountEqualTo(account);
        List<AliexpressHalfTgItem> dbList = this.selectByExample(halfTgItemExample);
        if(CollectionUtils.isEmpty(dbList)){
            return ;
        }

        Map<String, AliexpressHalfTgItem> dbSkuIdMap = dbList.stream().collect(Collectors.toMap(AliexpressHalfTgItem::getSkuId, t -> t, (k1, k2) -> k1));
        Map<String, HalfTgItemUpdatePackageWeight> updateSkuIdMap = halfTgItemRequests.stream().collect(Collectors.toMap(HalfTgItemUpdatePackageWeight::getSkuId, t -> t, (k1, k2) -> k1));

        List<AliexpressProductLog> logList = new ArrayList<>();
        //记录改前改后日志
        for (HalfTgItemUpdatePackageWeight halfTgItemRequest : halfTgItemRequests) {
            String skuId = halfTgItemRequest.getSkuId();
            Double packageWeight = halfTgItemRequest.getPackageWeight();
            AliexpressHalfTgItem dbItem = dbSkuIdMap.get(skuId);
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setProductId(productId);
            productLog.setAccountNumber(account);
            productLog.setSkuCode(dbItem.getArticleNumber());
            productLog.setOperator(WebUtils.getUserName());
            productLog.setOperateType(OperateLogTypeEnum.edit_half_tg_package_weight.getCode());
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            productLog.setWeightBeforeEdit(dbItem.getPackageWeight());
            productLog.setWeightAfterEdit(packageWeight);
            aliexpressProductLogService.insert(productLog);
            logList.add(productLog);
        }

        //编辑json
        JSONObject pop_choice_productJson = new JSONObject();
        AliexpressHalfTgItem aliexpressHalfTgItem = dbList.get(0);

        try{
            Integer categoryId = aliexpressHalfTgItem.getCategoryId();
            String joinedCountryList = aliexpressHalfTgItem.getJoinedCountryList();
            pop_choice_productJson.put("product_id", productId);
            pop_choice_productJson.put("category_id", categoryId);
            pop_choice_productJson.put("currency_code", aliexpressHalfTgItem.getCurrencyCode());
            if(StringUtils.isNotBlank(joinedCountryList)){
                pop_choice_productJson.put("joined_country_list", joinedCountryList);
            }else{
                pop_choice_productJson.put("joined_country_list", null);
            }
            JSONArray product_sku_list = new JSONArray();
            for (AliexpressHalfTgItem halfTgItem : dbList) {
                JSONObject product_skuJson = new JSONObject();
                product_sku_list.add(product_skuJson);

                JSONObject pop_choice_product_sku_sc_item_info = new JSONObject();
                pop_choice_product_sku_sc_item_info.put("sc_item_code", halfTgItem.getScItemCode());
                pop_choice_product_sku_sc_item_info.put("sc_item_bar_code", halfTgItem.getScItemBarCode());
                pop_choice_product_sku_sc_item_info.put("original_box", halfTgItem.getOriginalBox());
                List<String> specialTypeList = halfTgItem.getSpecialTypeList();
                if(CollectionUtils.isNotEmpty(specialTypeList)){
                    pop_choice_product_sku_sc_item_info.put("special_product_type_list", specialTypeList);
                }
                product_skuJson.put("pop_choice_product_sku_sc_item_info", pop_choice_product_sku_sc_item_info);

                String skuId = halfTgItem.getSkuId();
                HalfTgItemUpdatePackageWeight halfTgItemUpdatePackageWeight = updateSkuIdMap.get(skuId);
                // 修改重量
                product_skuJson.put("package_weight", halfTgItemUpdatePackageWeight.getPackageWeight());

                product_skuJson.put("package_height", halfTgItem.getPackageHeight());
                product_skuJson.put("package_width", halfTgItem.getPackageWidth());
                product_skuJson.put("package_length", halfTgItem.getPackageLength());
                product_skuJson.put("sku_id", halfTgItem.getSkuId());
                product_skuJson.put("sku_code", halfTgItem.getSkuCode());
                product_skuJson.put("base_price", halfTgItem.getBasePrice());
                product_skuJson.put("pop_choice_sku_warehouse_stock_list", halfTgItem.getPopChoiceSkuWarehouseStockList());
                product_skuJson.put("sku_property_list", halfTgItem.getSkuPropertyList());
            }
            pop_choice_productJson.put("product_sku_list", product_sku_list);
        } catch (Exception e){
            log.error(e.getMessage(), e);
            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(false);
                productLog.setFailInfo(e.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }

        AliexpressExecutors.updateHalfTgPublicPool(() ->{
            for (AliexpressProductLog productLog : logList) {
                productLog.setOperateStatus(OperateLogStatusEnum.processing.intCode());
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
            HalfTgEditCall editCall = new HalfTgEditCall();
//            ResponseJson responseJson = editCall.editItem(saleAccountAndBusinessResponse, pop_choice_productJson.toJSONString());
            ResponseJson responseJson = EnvironmentSupplierWrapper.execute(()->{
                return editCall.editItem(saleAccountAndBusinessResponse, pop_choice_productJson.toJSONString());
            },()->{
                log.info("非正式环境不执行改价,productId:{}, pop_choice_productJson:{}", productId, pop_choice_productJson.toJSONString());
                ResponseJson rsp = new ResponseJson();
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage("非正式环境不执行改价");
                return rsp;
            });

            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(responseJson.isSuccess());
                productLog.setFailInfo(responseJson.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
            //修改成功后同步产品
            if(responseJson.isSuccess()){
                HalfTgListCall listCall = new HalfTgListCall();
                HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
                listRequest.setProductId(productId);
                listCall.list(saleAccountAndBusinessResponse, listRequest);
            }
        });
    }

    @Override
    public void updatePack(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemUpdatePack> halfTgItemRequests, String userName){
        if(productId == null){
            return;
        }
        String account = saleAccountAndBusinessResponse.getAccountNumber();

        //查询本地数据，提交的skuid 如果不全 需要补充
        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
        criteria.andProductIdEqualTo(productId);
        criteria.andAccountEqualTo(account);
        List<AliexpressHalfTgItem> dbList = this.selectByExample(halfTgItemExample);
        if(CollectionUtils.isEmpty(dbList)){
            return;
        }

        Map<String, AliexpressHalfTgItem> dbSkuIdMap = dbList.stream().collect(Collectors.toMap(AliexpressHalfTgItem::getSkuId, t -> t, (k1, k2) -> k1));
        Map<String, HalfTgItemUpdatePack> updateSkuIdMap = halfTgItemRequests.stream().collect(Collectors.toMap(HalfTgItemUpdatePack::getSkuId, t -> t, (k1, k2) -> k1));

        List<AliexpressProductLog> logList = new ArrayList<>();
        //记录改前改后日志
        for (HalfTgItemUpdatePack halfTgItemRequest : halfTgItemRequests) {
            String skuId = halfTgItemRequest.getSkuId();
            HalfTgCalePackInfo newV = new HalfTgCalePackInfo();
            newV.setPackageLength(halfTgItemRequest.getPackageLength());
            newV.setPackageHeight(halfTgItemRequest.getPackageHeight());
            newV.setPackageWidth(halfTgItemRequest.getPackageWidth());
            AliexpressHalfTgItem dbItem = dbSkuIdMap.get(skuId);
            HalfTgCalePackInfo oldV = new HalfTgCalePackInfo();
            oldV.setPackageLength(dbItem.getPackageLength());
            oldV.setPackageHeight(dbItem.getPackageHeight());
            oldV.setPackageWidth(dbItem.getPackageWidth());
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setProductId(productId);
            productLog.setAccountNumber(account);
            productLog.setSkuCode(dbItem.getArticleNumber());
            productLog.setOperator(WebUtils.getUserName());
            productLog.setOperateType(OperateLogTypeEnum.edit_half_tg_pack.getCode());
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            productLog.setNewRemark("改前值：" + oldV.log() + ",改后值：" + newV.log());
            aliexpressProductLogService.insert(productLog);
            logList.add(productLog);
        }

        //编辑json
        JSONObject pop_choice_productJson = new JSONObject();
        AliexpressHalfTgItem aliexpressHalfTgItem = dbList.get(0);

        try{
            Integer categoryId = aliexpressHalfTgItem.getCategoryId();
            String joinedCountryList = aliexpressHalfTgItem.getJoinedCountryList();
            pop_choice_productJson.put("product_id", productId);
            pop_choice_productJson.put("category_id", categoryId);
            pop_choice_productJson.put("currency_code", aliexpressHalfTgItem.getCurrencyCode());
            if(StringUtils.isNotBlank(joinedCountryList)){
                pop_choice_productJson.put("joined_country_list", joinedCountryList);
            }else{
                pop_choice_productJson.put("joined_country_list", null);
            }

            JSONArray product_sku_list = new JSONArray();

            for (AliexpressHalfTgItem halfTgItem : dbList) {
                JSONObject product_skuJson = new JSONObject();
                product_sku_list.add(product_skuJson);

                JSONObject pop_choice_product_sku_sc_item_info = new JSONObject();
                pop_choice_product_sku_sc_item_info.put("sc_item_code", halfTgItem.getScItemCode());
                pop_choice_product_sku_sc_item_info.put("sc_item_bar_code", halfTgItem.getScItemBarCode());
                pop_choice_product_sku_sc_item_info.put("original_box", halfTgItem.getOriginalBox());
                List<String> specialTypeList = halfTgItem.getSpecialTypeList();
                if(CollectionUtils.isNotEmpty(specialTypeList)){
                    pop_choice_product_sku_sc_item_info.put("special_product_type_list", specialTypeList);
                }
                product_skuJson.put("pop_choice_product_sku_sc_item_info", pop_choice_product_sku_sc_item_info);


                String skuId = halfTgItem.getSkuId();
                HalfTgItemUpdatePack halfTgItemUpdatePack = updateSkuIdMap.get(skuId);
                // 修改尺寸
                product_skuJson.put("package_height", halfTgItemUpdatePack.getPackageHeight());
                product_skuJson.put("package_width", halfTgItemUpdatePack.getPackageWidth());
                product_skuJson.put("package_length", halfTgItemUpdatePack.getPackageLength());

                product_skuJson.put("package_weight", halfTgItem.getPackageWeight());
                product_skuJson.put("sku_id", halfTgItem.getSkuId());
                product_skuJson.put("sku_code", halfTgItem.getSkuCode());
                product_skuJson.put("base_price", halfTgItem.getBasePrice());
                product_skuJson.put("pop_choice_sku_warehouse_stock_list", halfTgItem.getPopChoiceSkuWarehouseStockList());
                product_skuJson.put("sku_property_list", halfTgItem.getSkuPropertyList());
            }
            pop_choice_productJson.put("product_sku_list", product_sku_list);
        } catch (Exception e){
            log.error(e.getMessage(), e);
            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(false);
                productLog.setFailInfo(e.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }

        AliexpressExecutors.updateHalfTgPublicPool(() ->{
            for (AliexpressProductLog productLog : logList) {
                productLog.setOperateStatus(OperateLogStatusEnum.processing.intCode());
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }

            HalfTgEditCall editCall = new HalfTgEditCall();
//            ResponseJson responseJson = editCall.editItem(saleAccountAndBusinessResponse, pop_choice_productJson.toJSONString());

             ResponseJson responseJson = EnvironmentSupplierWrapper.execute(()->{
                            return editCall.editItem(saleAccountAndBusinessResponse, pop_choice_productJson.toJSONString());
                        },()->{
                            log.info("非正式环境不执行改价,productId:{}, pop_choice_productJson:{}", productId, pop_choice_productJson.toJSONString());
                            ResponseJson rsp = new ResponseJson();
                            rsp.setStatus(StatusCode.FAIL);
                            rsp.setMessage("非正式环境不执行改价");
                            return rsp;
                        });

            for (AliexpressProductLog productLog : logList) {
                productLog.setResult(responseJson.isSuccess());
                productLog.setFailInfo(responseJson.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
            //修改成功后同步产品
            if(responseJson.isSuccess()){
                HalfTgListCall listCall = new HalfTgListCall();
                HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
                listRequest.setProductId(productId);
                listCall.list(saleAccountAndBusinessResponse, listRequest);
            }
        });
    }

    @Override
    public void batchUpdateSystemStock(String articleNumber){
        if (StringUtils.isBlank(articleNumber) || articleNumber.startsWith("GT")) {
            return;
        }
        String msg;
        try {
            //可用
            Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            if (null == avableStock) {
                msg = String.format(" 更新listing系统库存失败,%s 查询redis库存为空", articleNumber);
                log.error(msg);
            } else {
                //smt中转
                Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                //可用-待发
                Integer systemStock = SkuStockUtils.getSkuStockToEbay(articleNumber);
                //可用+中转-待发
                Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                try {
                    AliexpressHalfTgItem aliexpressHalfTgItem = new AliexpressHalfTgItem();
                    aliexpressHalfTgItem.setSmtTransferStock(transferStockForRedis);
                    aliexpressHalfTgItem.setUsableStock(avableStock);
                    aliexpressHalfTgItem.setSystemStock(systemStock);
                    aliexpressHalfTgItem.setSystemUsableTransferStock(skuTransferStock);
                    aliexpressHalfTgItem.setUpdateSystemStockDate(timestamp);
                    aliexpressHalfTgItem.setArticleNumber(articleNumber);
                    aliexpressHalfTgItemMapper.updateSystemStockBySku(aliexpressHalfTgItem);
                } catch (Exception e) {
                    msg = String.format(" 更新listing系统库存货号失败,原因：%s", e.getMessage());
                    log.error(msg, e);
                }
            }
        } catch (Exception e) {
            msg = String.format(" %s 更新listing系统库存失败,原因：%s", articleNumber, e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public ResponseJson editItem(List<AliexpressHalfTgItem> halfTgItems, String user){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(CollectionUtils.isEmpty(halfTgItems)){
            rsp.setMessage("参数为空！");
            return rsp;
        }
        AliexpressHalfTgItem aliexpressHalfTgItem = halfTgItems.get(0);
        String account = aliexpressHalfTgItem.getAccount();
        Long productId = aliexpressHalfTgItem.getProductId();
        String currencyCode = aliexpressHalfTgItem.getCurrencyCode();
        if(StringUtils.isBlank(account) || productId == null){
            rsp.setMessage("参数为空！");
            return rsp;
        }

        AliexpressProductLog productLog = new AliexpressProductLog();
        productLog.setProductId(productId);
        productLog.setAccountNumber(account);
        productLog.setOperator(StringUtils.isEmpty(user) ? "admin" : user);
        productLog.setOperateType(OperateLogTypeEnum.edit_half_tg_item.getCode());
        productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        aliexpressProductLogService.insert(productLog);

        //编辑json
        JSONObject pop_choice_productJson = new JSONObject();

        try{
            Integer categoryId = aliexpressHalfTgItem.getCategoryId();
            String joinedCountryList = aliexpressHalfTgItem.getJoinedCountryList();
            pop_choice_productJson.put("product_id", productId);
            pop_choice_productJson.put("category_id", categoryId);
            pop_choice_productJson.put("currency_code", currencyCode);
            if(StringUtils.isNotBlank(joinedCountryList)){
                pop_choice_productJson.put("joined_country_list", joinedCountryList);
            }else{
                pop_choice_productJson.put("joined_country_list", null);
            }

            JSONArray product_sku_list = new JSONArray();

            for (AliexpressHalfTgItem halfTgItem : halfTgItems) {
                JSONObject product_skuJson = new JSONObject();
                product_sku_list.add(product_skuJson);

                JSONObject pop_choice_product_sku_sc_item_info = new JSONObject();
                pop_choice_product_sku_sc_item_info.put("sc_item_code", halfTgItem.getScItemCode());
                pop_choice_product_sku_sc_item_info.put("sc_item_bar_code", halfTgItem.getScItemBarCode());
                pop_choice_product_sku_sc_item_info.put("original_box", halfTgItem.getOriginalBox());
                List<String> specialTypeList = halfTgItem.getSpecialTypeList();
                if(CollectionUtils.isNotEmpty(specialTypeList)){
                    pop_choice_product_sku_sc_item_info.put("special_product_type_list", specialTypeList);
                }else{
                    pop_choice_product_sku_sc_item_info.put("special_product_type_list", "");
                }

                product_skuJson.put("pop_choice_product_sku_sc_item_info", pop_choice_product_sku_sc_item_info);

                product_skuJson.put("package_height", halfTgItem.getPackageHeight());
                product_skuJson.put("package_weight", halfTgItem.getPackageWeight());
                product_skuJson.put("package_width", halfTgItem.getPackageWidth());
                product_skuJson.put("package_length", halfTgItem.getPackageLength());
                product_skuJson.put("sku_id", halfTgItem.getSkuId());
                product_skuJson.put("sku_code", halfTgItem.getSkuCode());
                product_skuJson.put("base_price", halfTgItem.getBasePrice());
                product_skuJson.put("pop_choice_sku_warehouse_stock_list", halfTgItem.getPopChoiceSkuWarehouseStockList());
                product_skuJson.put("sku_property_list", halfTgItem.getSkuPropertyList());
            }
            pop_choice_productJson.put("product_sku_list", product_sku_list);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            productLog.setResult(false);
            productLog.setFailInfo(e.getMessage());
            productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            rsp.setMessage(e.getMessage());
            return rsp;
        }

        HalfTgEditCall editCall = new HalfTgEditCall();
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        String json = pop_choice_productJson.toJSONString();
        ResponseJson responseJson = editCall.editItem(saleAccountByAccountNumber, json);
        productLog.setResult(responseJson.isSuccess());
        productLog.setFailInfo(responseJson.getMessage());
        productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
        productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
        aliexpressProductLogService.updateByPrimaryKeySelective(productLog);

        //修改成功后同步产品
        if(responseJson.isSuccess()){
            HalfTgListCall listCall = new HalfTgListCall();
            HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
            listRequest.setProductId(productId);
            listCall.list(saleAccountByAccountNumber, listRequest);
        }
        return responseJson;
    }



    //excel 修改半托管库存
    @Override
    public List<UpdateStockExcel> updateHalfStockForExcle(String[] headers, MultipartFile file, String userName){
        //excel 返回结果
        List<UpdateStockExcel> returnResultList = new ArrayList<>();

        //解析excel
        Map<String, List<UpdateStockExcel>> productIdEntityListMap = new HashMap<>();

        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if(row == null) {
                    return null;
                }
                UpdateStockExcel rowData = new UpdateStockExcel();
                String productIdStr = "";
                try {
                    String sellerStr = ExcelUtils.getCellValue(row.getCell(0)).trim();
                    rowData.setAccount(sellerStr);
                    productIdStr = ExcelUtils.getCellValue(row.getCell(1)).trim();
                    rowData.setProductId(productIdStr);
                    String skuCodeStr = ExcelUtils.getCellValue(row.getCell(2)).trim();
                    rowData.setSkuCode(skuCodeStr);

                    String systemStockString = "";
                    Cell cell = row.getCell(3);
                    if(cell != null){
                        cell.setCellType(CellType.STRING);
                        systemStockString = row.getCell(3).getStringCellValue().toString();
                    }
                    String updateStockString = "";
                    Cell cell1 = row.getCell(4);
                    if(cell1 != null){
                        cell1.setCellType(CellType.STRING);
                        updateStockString = row.getCell(4).getStringCellValue().toString();
                    }
                    Integer systemStock, updateStock = null;
                    try{
                        if(StringUtils.isNotBlank(systemStockString)){
                            systemStock = Integer.parseInt(systemStockString);
                            rowData.setSystemStock(systemStock);
                        }
                        if(StringUtils.isNotBlank(updateStockString)){
                            updateStock = Integer.parseInt(updateStockString);
                            rowData.setUpdateStock(updateStock);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        rowData.setTip("库存格式有误！" + e.getMessage());
                        rowData.setResult("失败");
                    }
                    if(StringUtils.isBlank(sellerStr) || StringUtils.isBlank(productIdStr) ||
                            StringUtils.isBlank(skuCodeStr) || null == updateStock) {
                        rowData.setTip("数据为空不处理！");
                        rowData.setResult("失败");
                        return row;
                    }
                }catch(Exception e) {
                    log.error(e.getMessage(), e);
                    rowData.setTip("数据有误！" + e.getMessage());
                    rowData.setResult("失败");
                }finally {
                    if(StringUtils.isNotBlank(productIdStr)){
                        List<UpdateStockExcel> entityList = productIdEntityListMap.get(productIdStr);
                        if(CollectionUtils.isNotEmpty(entityList)) {
                            entityList.add(rowData);
                        }else {
                            entityList = new ArrayList<>();
                            entityList.add(rowData);
                        }
                        productIdEntityListMap.put(productIdStr, entityList);
                    }
                }
                return row;
            }, false);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("速卖通---修改半托管库存解析excel报错");
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productIdEntityListMap.size());
        productIdEntityListMap.forEach((k, v) -> {
            AliexpressExecutors.smtItemHalfStockUpdate(() -> {
                try{
                    String seller = v.get(0).getAccount();
                    ResponseJson rsp = ExcelOperationUtils.authIntercept(seller, userName);
                    if(!rsp.isSuccess()){
                        for (UpdateStockExcel updateStockExcel : v) {
                            updateStockExcel.setTip(rsp.getMessage());
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return;
                    }


                    Set<String> skuCodeSet = v.stream().map(t -> t.getSkuCode()).collect(Collectors.toSet());
                    if(skuCodeSet.size() < v.size()){
                        for (UpdateStockExcel updateStockExcel : v) {
                            updateStockExcel.setTip("excel 同个产品 商品编码重复 请检查！");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return;
                    }

                    List<UpdateStockExcel> requestUpdateStockExcelList = new ArrayList<>();

                    for (UpdateStockExcel updateStockExcel : v) {
                        String result = updateStockExcel.getResult();
                        if(StringUtils.equalsIgnoreCase("失败", result)){
                            returnResultList.add(updateStockExcel);
                        }else{
                            requestUpdateStockExcelList.add(updateStockExcel);
                        }
                    }

                    if(CollectionUtils.isEmpty(requestUpdateStockExcelList)){
                        return;
                    }

                    String account = requestUpdateStockExcelList.get(0).getAccount();

                    SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(account);
                    if(saleAccountAndBusinessResponse == null){
                        saleAccountAndBusinessResponse = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
                        accountMap.put(account, saleAccountAndBusinessResponse);
                    }

                    List<String> skuCodeList = requestUpdateStockExcelList.stream().map(t -> t.getSkuCode()).collect(Collectors.toList());

                    String fields = "id, account, product_id, article_number, sku_code, sku_id, pop_choice_sku_warehouse_stock_list, system_stock, sku_stock, sku_status";
                    AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
                    AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
                    criteria.andAccountEqualTo(account);
                    criteria.andProductIdEqualTo(Long.valueOf(k));
                    criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
                    criteria.andSkuCodeIn(skuCodeList);
                    halfTgItemExample.setFields(fields);
                    List<AliexpressHalfTgItem> dbList = this.selectByExample(halfTgItemExample);
                    if(CollectionUtils.isEmpty(dbList)){
                        for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                            updateStockExcel.setTip("请检查商品id和商品编码是否正确或者产品非上架、在线状态！");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return; //查询不到本地数据直接返回
                    }

                    if(dbList.size() < v.size()){
                        List<String> dbSkuCodeList = dbList.stream().map(t -> t.getSkuCode()).collect(Collectors.toList());
                        for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                            String excelSkuCode = updateStockExcel.getSkuCode();
                            if(!dbSkuCodeList.contains(excelSkuCode)){
                                updateStockExcel.setTip("请检查商品id和商品编码是否正确或者产品非上架、在线状态！");
                                updateStockExcel.setResult("失败");
                                returnResultList.add(updateStockExcel);
                            }
                        }
                    } //部分数据

                    Map<String, UpdateStockExcel> skuCodeMap = new HashMap<>();
                    for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                        String skuCode = updateStockExcel.getSkuCode();
                        skuCodeMap.put(skuCode, updateStockExcel);
                    }

                    List<HalfTgItemRequest> requestsList = new ArrayList<>(); //请求平台数据

                    for (AliexpressHalfTgItem dbItem : dbList) {
                        String skuCode = dbItem.getSkuCode();
                        UpdateStockExcel updateStockExcel = skuCodeMap.get(skuCode);
                        if(updateStockExcel == null){
                            updateStockExcel = new UpdateStockExcel();
                        }
                        String articleNumber = dbItem.getArticleNumber();

                        Integer excleUpdateStock = updateStockExcel.getUpdateStock(); //excel需要调整的库存
                        if(excleUpdateStock == null){
                            updateStockExcel.setTip(articleNumber + " 需要调整的库存为空");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                            continue;
                        }

                        //可用+中转-待发
                        Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber.trim().toUpperCase(), ErpCommonConstant.wmsTransferStockSMT);
                        if(skuTransferStock == null){
                            updateStockExcel.setTip(articleNumber + " redis 可用+中转-待发为空");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                            continue;
                        }

                        //提交到平台的库存值
                        Integer submitStock = excleUpdateStock;
                        if(excleUpdateStock > skuTransferStock.intValue()){
                            submitStock = skuTransferStock;
                            updateStockExcel.setRemark("库存大于可用+中转-待发库存，改后库存为实际可用+中转-待发库存，改后库存为：" + skuTransferStock);
                        }

                        String popChoiceSkuWarehouseStockList = dbItem.getPopChoiceSkuWarehouseStockList();
                        JSONArray dbJsonArray = JSONObject.parseArray(popChoiceSkuWarehouseStockList);

                        for (int i = 0; i < dbJsonArray.size(); i++) {
                            JSONObject dbJsonArrayJSONObject = dbJsonArray.getJSONObject(i);
                            String warehouse_code = dbJsonArrayJSONObject.getString("warehouse_code");
                            String warehouse_name = dbJsonArrayJSONObject.getString("warehouse_name");

                            //数据库的数量
                            Integer sellable_quantity = dbJsonArrayJSONObject.getInteger("sellable_quantity");
                            if(sellable_quantity == submitStock.intValue()){
                                //不需要修改但是需要记录日志
                                AliexpressProductLog log = new AliexpressProductLog();
                                log.setProductId(Long.valueOf(k));
                                log.setAccountNumber(account);
                                log.setSkuCode(dbItem.getArticleNumber());
                                log.setOperator(userName);
                                log.setOperateType(OperateLogTypeEnum.edit_half_tg_stock.getCode());
                                log.setStockBeforeEdit(sellable_quantity.doubleValue());
                                log.setStockAfterEdit(submitStock.doubleValue());
                                log.setOperateStatus(OperateLogStatusEnum.end.intCode());
                                log.setNewRemark(warehouse_name);
                                log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                                log.setCreateTime(new Timestamp(System.currentTimeMillis()));
                                log.setResult(true);
                                aliexpressProductLogService.insert(log);

                                updateStockExcel.setTip("当前库存和需要修改库存一直 不需要调整:" + submitStock);
                                updateStockExcel.setResult("成功");
                                returnResultList.add(updateStockExcel);
                                continue;
                            }

                            HalfTgItemRequest itemRequest = new HalfTgItemRequest();
                            itemRequest.setAccount(account);
                            itemRequest.setSkuId(dbItem.getSkuId());
                            itemRequest.setWarehouseName(warehouse_name);
                            itemRequest.setWarehouseCode(warehouse_code);
                            itemRequest.setSellableQuantity(submitStock);
                            itemRequest.setProductId(dbItem.getProductId());
                            itemRequest.setSkuCode(dbItem.getSkuCode()); //excel修改用
                            requestsList.add(itemRequest);
                        }

                        if(CollectionUtils.isNotEmpty(requestsList)){
                            ResponseJson responseJson = this.updateStocks(saleAccountAndBusinessResponse, Long.valueOf(k), requestsList, userName);
                            for (HalfTgItemRequest halfTgItemRequest : requestsList) {
                                String skuCode1 = halfTgItemRequest.getSkuCode();
                                UpdateStockExcel updateStockExcel1 = skuCodeMap.get(skuCode1);
                                if(updateStockExcel1 == null){
                                    updateStockExcel1 = new UpdateStockExcel();
                                }
                                updateStockExcel1.setResult(responseJson.isSuccess() ? "成功" : "失败");
                                updateStockExcel1.setTip(responseJson.getMessage());
                                returnResultList.add(updateStockExcel1);
                            }
                        }
                    }

                }catch (Exception e){
                    log.error(e.getMessage(), e);
                }finally {
                    countDownLatch.countDown();
                }
            });
        });

        try {
            countDownLatch.await();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }
        return returnResultList;
    }


    @Override
    public List<UpdatePriceEntity> updateHalfItem(String[] headers, MultipartFile file, String user){
        //操作人
        String userName = StringUtils.isNotBlank(user) ? user : WebUtils.getUserName();
        //excel 返回结果
        List<UpdatePriceEntity> returnResultList = new ArrayList<>();

        //解析excel
        Map<String, List<UpdatePriceEntity>> productIdEntityListMap = new HashMap<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if(row == null) {
                    return null;
                }
                UpdatePriceEntity rowData = new UpdatePriceEntity();
                String productIdStr = "";
                try {
                    String sellerStr = ExcelUtils.getCellValue(row.getCell(0)).trim();
                    if(StringUtils.equalsIgnoreCase(sellerStr, "红色为必填项目")){
                        return null;
                    }
                    rowData.setSeller(sellerStr);
                    productIdStr = ExcelUtils.getCellValue(row.getCell(1)).trim();
                    rowData.setProductId(productIdStr);
                    String skuCodeStr = ExcelUtils.getCellValue(row.getCell(2)).trim();
                    rowData.setSkuCode(skuCodeStr);
                    //这3个是必填项目
                    if(StringUtils.isBlank(sellerStr) || StringUtils.isBlank(productIdStr) || StringUtils.isBlank(skuCodeStr)){
                        return null;
                    }

                    //货品编码
                    String scItemCode = ExcelUtils.getCellValue(row.getCell(3));
                    rowData.setScItemCode(scItemCode);

                    //SKU货品条码
                    String scItemBarCode = ExcelUtils.getCellValue(row.getCell(4));
                    rowData.setScItemBarCode(scItemBarCode);

                    Double basePrice = null;
                    Double packageWeight = null;//kg
                    Double packageLength = null;
                    Double packageWidth = null;
                    Double packageHeight = null;
                    Integer skuStock = null;

                    try{
                        Cell cell5 = row.getCell(5);
                        if(cell5 != null){
                            cell5.setCellType(CellType.STRING);
                            String priceString = ExcelUtils.getCellValue(row.getCell(5));
                            if(StringUtils.isNotBlank(priceString)){
                                basePrice = Double.parseDouble(priceString.trim());
                            }
                        }

                        Cell cell6 = row.getCell(6);
                        if(cell6 != null){
                            cell6.setCellType(CellType.STRING);
                            String packageWeightString = ExcelUtils.getCellValue(row.getCell(6));
                            if(StringUtils.isNotBlank(packageWeightString)){
                                packageWeight = Double.parseDouble(packageWeightString.trim());
                            }
                        }


                        Cell cell7 = row.getCell(7);
                        if(cell7 != null){
                            cell7.setCellType(CellType.STRING);
                            String packageLengthString = ExcelUtils.getCellValue(row.getCell(7));
                            if(StringUtils.isNotBlank(packageLengthString)){
                                packageLength = Double.parseDouble(packageLengthString.trim());
                            }
                        }

                        Cell cell8 = row.getCell(8);
                        if(cell8 != null){
                            cell8.setCellType(CellType.STRING);
                            String packageWidthString = ExcelUtils.getCellValue(row.getCell(8));
                            if(StringUtils.isNotBlank(packageWidthString)){
                                packageWidth = Double.parseDouble(packageWidthString.trim());
                            }
                        }

                        Cell cell9 = row.getCell(9);
                        if(cell9 != null){
                            cell9.setCellType(CellType.STRING);
                            String packageHeightString = ExcelUtils.getCellValue(row.getCell(9));
                            if(StringUtils.isNotBlank(packageHeightString)){
                                packageHeight = Double.parseDouble(packageHeightString.trim());
                            }
                        }

                        Cell cell11 = row.getCell(11);
                        if(cell11 != null){
                            cell11.setCellType(CellType.STRING);
                            String skuStockString = ExcelUtils.getCellValue(row.getCell(11));
                            if(StringUtils.isNotBlank(skuStockString)){
                                skuStock = Integer.valueOf(skuStockString.trim());
                            }
                        }

                        rowData.setPrice(basePrice);
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        rowData.setErrorTip("价格格式错误有误！");
                    }

                    //SKU特殊商品类型
                    String specialProductTypeList = ExcelUtils.getCellValue(row.getCell(10));

                    rowData.setBasePrice(basePrice);
                    rowData.setPackageWeight(packageWeight);
                    rowData.setPackageLength(packageLength);
                    rowData.setPackageWidth(packageWidth);
                    rowData.setPackageHeight(packageHeight);
                    rowData.setSkuStock(skuStock);
                    if(StringUtils.isNotBlank(specialProductTypeList)){
                        rowData.setSpecialProductTypeList(specialProductTypeList);
                    }
                }catch(Exception e) {
                    log.error(e.getMessage(), e);
                    rowData.setErrorTip("数据有误！" + e.getMessage());
                }finally {
                    if(StringUtils.isNotBlank(productIdStr)){
                        List<UpdatePriceEntity> entityList = productIdEntityListMap.get(productIdStr);
                        if(CollectionUtils.isNotEmpty(entityList)) {
                            entityList.add(rowData);
                        }else {
                            entityList = new ArrayList<>();
                            entityList.add(rowData);
                        }
                        productIdEntityListMap.put(productIdStr, entityList);
                    }
                }
                return row;
            }, false);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("smt---半托管编辑解析excel报错" + e.getMessage());
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productIdEntityListMap.size());

        productIdEntityListMap.forEach((k,v) -> {
            //组装数据进行半托管编辑
            AliexpressExecutors.excelUpdateHalfTgPublicPool(() -> {
                try{
                    String seller = v.get(0).getSeller();
                    ResponseJson responseJson = ExcelOperationUtils.authIntercept(seller, userName);
                    if(!responseJson.isSuccess()){
                        String errMsg = responseJson.getMessage();
                        for (UpdatePriceEntity item : v) {
                            item.setErrorTip(errMsg);
                            //结果返回
                            returnResultList.add(item);
                        }
                        return;
                    }
                    Long productId = Long.valueOf(k);
                    SaleAccountAndBusinessResponse account = accountMap.get(seller);
                    if(account == null){
                        account = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, seller);
                        accountMap.put(seller, account);
                    }

                    //查询本地数据
                    AliexpressHalfTgItemExample tgItemExample = new AliexpressHalfTgItemExample();
                    tgItemExample.createCriteria().andAccountEqualTo(seller).andProductIdEqualTo(productId);
                    List<AliexpressHalfTgItem> dbItemList = this.selectByExample(tgItemExample);
                    if(CollectionUtils.isEmpty(dbItemList) ||dbItemList.size() < v.size()){
                        String errMsg = "本地数据不存在 或者excel数据大于本地产品数据，请检查！";
                        for (UpdatePriceEntity item : v) {
                            item.setErrorTip(errMsg);
                            //结果返回
                            returnResultList.add(item);
                        }
                        return;
                    }
                    List<String> dbSkuCodeList = dbItemList.stream().map(t -> t.getSkuCode().trim()).collect(Collectors.toList());

                    for (UpdatePriceEntity updatePriceEntity : v) {
                        String excelSkuCode = updatePriceEntity.getSkuCode();
                        if(!dbSkuCodeList.contains(excelSkuCode)){
                            String errMsg = "本地数据商品编码不包含 " + excelSkuCode ;
                            for (UpdatePriceEntity item : v) {
                                item.setErrorTip(errMsg);
                                //结果返回
                                returnResultList.add(item);
                            }
                            return;
                        }
                    }

                    Map<String, UpdatePriceEntity> excelSkuCodeMap = v.stream().collect(Collectors.toMap(k1 -> k1.getSkuCode(), k2 -> k2, (t, t1) -> t));

                    for (AliexpressHalfTgItem dbItem : dbItemList) {
                        String dbSkuCode = dbItem.getSkuCode().trim();
                        dbItem.setSkuCode(dbSkuCode);
                        UpdatePriceEntity updatePriceEntity = excelSkuCodeMap.get(dbSkuCode);
                        if(updatePriceEntity != null){
                            String scItemCode = updatePriceEntity.getScItemCode();
                            if(StringUtils.isNotBlank(scItemCode)){
                                dbItem.setScItemCode(scItemCode);
                            }
                            String scItemBarCode = updatePriceEntity.getScItemBarCode();
                            if(StringUtils.isNotBlank(scItemBarCode)){
                                dbItem.setScItemBarCode(scItemBarCode);
                            }
                            Double basePrice = updatePriceEntity.getBasePrice();
                            if(basePrice != null){
                                dbItem.setBasePrice(basePrice);
                            }
                            Double packageWeight = updatePriceEntity.getPackageWeight();
                            if(packageWeight != null){
                                dbItem.setPackageWeight(packageWeight);
                            }
                            Double packageLength = updatePriceEntity.getPackageLength();
                            if(packageLength != null){
                                dbItem.setPackageLength(packageLength);
                            }
                            Double packageWidth = updatePriceEntity.getPackageWidth();
                            if(packageWidth != null){
                                dbItem.setPackageWidth(packageWidth);
                            }
                            Double packageHeight = updatePriceEntity.getPackageHeight();
                            if(packageHeight != null){
                                dbItem.setPackageHeight(packageHeight);
                            }
                            Integer skuStock = updatePriceEntity.getSkuStock();
                            if(skuStock != null){
                                String popChoiceSkuWarehouseStockList = dbItem.getPopChoiceSkuWarehouseStockList();
                                JSONArray dbJsonArray = JSONObject.parseArray(popChoiceSkuWarehouseStockList);
                                JSONArray newJsonArray = new JSONArray();
                                for (int i = 0; i < dbJsonArray.size(); i++) {
                                    JSONObject dbJsonObject = dbJsonArray.getJSONObject(i);
                                    dbJsonObject.put("sellable_quantity", skuStock);
                                    newJsonArray.add(dbJsonObject);
                                }
                                dbItem.setPopChoiceSkuWarehouseStockList(newJsonArray.toJSONString());
                            }
                            String specialProductTypeList = updatePriceEntity.getSpecialProductTypeList();
                            if(StringUtils.isNotBlank(specialProductTypeList)){
                                if(StringUtils.equalsIgnoreCase(PlatformTagEnum.s_.getName(), specialProductTypeList)){
                                    dbItem.setSpecialProductTypeList(null);
                                }else{
                                    String codeByName = PlatformTagEnum.getCodeByName(specialProductTypeList);
                                    dbItem.setSpecialProductTypeList(JSON.toJSONString(Arrays.asList(codeByName)));
                                }
                            }
                        }
                    }

                    ResponseJson rsp = this.editItem(dbItemList, userName);
                    for (UpdatePriceEntity item : v) {
                        if(rsp.isSuccess()){
                            item.setErrorTip("修改成功！");
                        }else{
                            item.setErrorTip(rsp.getMessage());
                        }
                        //结果返回
                        returnResultList.add(item);
                    }
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                }finally {
                    countDownLatch.countDown();
                }
            });

        });

        try {
            countDownLatch.await();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }
        return returnResultList;
    }

    public void deleteSmtHalfBinds(List<EsSkuBind> esSkuBindList){
        Map<String, List<EsSkuBind>> accountMap = esSkuBindList.stream().collect(Collectors.groupingBy(t -> t.getSellerId()));

        List<EsSkuBind> createTSkuBindList = new ArrayList<>();
        for (Map.Entry<String, List<EsSkuBind>> stringListEntry : accountMap.entrySet()) {
            try {
                String key = stringListEntry.getKey();
                List<EsSkuBind> value = stringListEntry.getValue();
                List<String> skuIdList = value.stream().map(t -> t.getBindSku()).collect(Collectors.toList());
                EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
                listingRequest.setAliexpressAccountNumber(key);
                listingRequest.setPlatSkuIdList(skuIdList);
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
                for (EsSkuBind esSkuBind : value) {
                    //删除有延迟
                    elasticsearchRestTemplate1.delete(esSkuBind.getId(), indexCoordinates);
                }

                for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                    EsSkuBind createTSkuBind = new EsSkuBind();
                    String platSkuId = aliexpressProductListing.getPlatSkuId();
                    if(StringUtils.isBlank(platSkuId) || !skuIdList.contains(platSkuId)){
                        continue;
                    }
                    createTSkuBindList.add(createTSkuBind);
                    createTSkuBind.setId(Platform.Smt.name() + "_" + platSkuId);
                    createTSkuBind.setSku(aliexpressProductListing.getArticleNumber());
                    createTSkuBind.setBindSku(platSkuId);
                    createTSkuBind.setPlatform(Platform.Smt.name());
                    createTSkuBind.setSellerId(aliexpressProductListing.getAliexpressAccountNumber());
                    createTSkuBind.setSkuDataSource(SkuDataSourceEnum.SMT_HALF.getCode());
                    createTSkuBind.setCreateDate(new Date());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        if(CollectionUtils.isNotEmpty(createTSkuBindList)){
            esSkuBindService.saveAll(createTSkuBindList, Platform.Smt.name());
        }
    }

    @Override
    public void updateCountryList(AliexpressUpdateCountryDTO updateCountryDTO) {
        // 查询item维度数据
        List<Long> productIds = updateCountryDTO.getAccountInfo().stream().map(t -> t.getProductId()).distinct().collect(Collectors.toList());
        AliexpressHalfTgItemExample aliexpressHalfTgItemExample = new AliexpressHalfTgItemExample();
        aliexpressHalfTgItemExample.createCriteria().andProductIdIn(productIds);
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemMapper.selectByExample(aliexpressHalfTgItemExample);

        // 已item分组处理
        if (CollectionUtils.isNotEmpty(aliexpressHalfTgItems)) {
            String userName = WebUtils.getUserName();

            Map<Long, List<AliexpressHalfTgItem>> listMap = aliexpressHalfTgItems.stream().collect(Collectors.groupingBy(AliexpressHalfTgItem::getProductId));
            listMap.forEach((productId, itemList) -> {
                AliexpressExecutors.updateCountry(() -> {
                    HalfTgEditCall editCall = new HalfTgEditCall();

                    // 参数
                    AliexpressHalfTgItem accountInfo = itemList.get(0);
                    JSONObject popChoicePoductJson = new JSONObject();
                    popChoicePoductJson.put("product_id", accountInfo.getProductId());
                    popChoicePoductJson.put("category_id", accountInfo.getCategoryId());
                    popChoicePoductJson.put("currency_code", accountInfo.getCurrencyCode());
                    popChoicePoductJson.put("joined_country_list", updateCountryDTO.getJoinedCountryList());
                    try {
                        // 查询账户信息
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountInfo.getAccount());
                        List<JSONObject> productSkuList = itemList.stream().map(item -> {
                            // 该半托管商品下SKU列表
                            JSONObject productSkuJson = new JSONObject();
                            JSONObject pop_choice_product_sku_sc_item_info = new JSONObject();
                            Optional.ofNullable(item.getScItemCode()).ifPresent(t -> pop_choice_product_sku_sc_item_info.put("sc_item_code", item.getScItemCode()));
                            Optional.ofNullable(item.getScItemBarCode()).ifPresent(t -> pop_choice_product_sku_sc_item_info.put("sc_item_bar_code", item.getScItemBarCode()));
                            Optional.ofNullable(item.getOriginalBox()).ifPresent(t -> pop_choice_product_sku_sc_item_info.put("original_box", item.getOriginalBox()));
                            List<String> specialTypeList = item.getSpecialTypeList();
                            if (CollectionUtils.isNotEmpty(specialTypeList)) {
                                pop_choice_product_sku_sc_item_info.put("special_product_type_list", specialTypeList);
                            } else {
                                pop_choice_product_sku_sc_item_info.put("special_product_type_list", "");
                            }
                            productSkuJson.put("pop_choice_product_sku_sc_item_info", pop_choice_product_sku_sc_item_info);

                            Optional.ofNullable(item.getPackageHeight()).ifPresent(t -> productSkuJson.put("package_height", item.getPackageHeight()));
                            Optional.ofNullable(item.getPackageWeight()).ifPresent(t -> productSkuJson.put("package_weight", item.getPackageWeight()));
                            Optional.ofNullable(item.getPackageWidth()).ifPresent(t -> productSkuJson.put("package_width", item.getPackageWidth()));
                            Optional.ofNullable(item.getPackageLength()).ifPresent(t -> productSkuJson.put("package_length", item.getPackageLength()));
                            Optional.ofNullable(item.getSkuId()).ifPresent(t -> productSkuJson.put("sku_id", item.getSkuId()));
                            Optional.ofNullable(item.getSkuCode()).ifPresent(t -> productSkuJson.put("sku_code", item.getSkuCode()));
                            Optional.ofNullable(item.getBasePrice()).ifPresent(t -> productSkuJson.put("base_price", item.getBasePrice()));
                            Optional.ofNullable(item.getPopChoiceSkuWarehouseStockList()).ifPresent(t -> productSkuJson.put("pop_choice_sku_warehouse_stock_list", item.getPopChoiceSkuWarehouseStockList()));
                            Optional.ofNullable(item.getSkuPropertyList()).ifPresent(t -> productSkuJson.put("sku_property_list", item.getSkuPropertyList()));

                            return productSkuJson;
                        }).collect(Collectors.toList());
                        popChoicePoductJson.put("product_sku_list", productSkuList);
                        String json = popChoicePoductJson.toJSONString();

                        // 请求编辑接口
                        ResponseJson responseJson = editCall.editItem(saleAccountByAccountNumber, json);
                        if (responseJson.isSuccess()) {
                            AliexpressHalfTgItem aliexpressHalfTgItem = new AliexpressHalfTgItem();
                            aliexpressHalfTgItem.setJoinedCountryList(updateCountryDTO.getJoinedCountryList());
                            AliexpressHalfTgItemExample itemExample = new AliexpressHalfTgItemExample();
                            itemExample.createCriteria().andProductIdEqualTo(productId);
                            aliexpressHalfTgItemMapper.updateByExampleSelective(aliexpressHalfTgItem, itemExample);
                        }

                        // 记录处理报告
                        AliexpressProductLog productLog = new AliexpressProductLog();
                        productLog.setAccountNumber(accountInfo.getAccount());
                        productLog.setProductId(accountInfo.getProductId());
                        productLog.setOperateType(OperateLogTypeEnum.UPDATE_COUNTRY.getCode());
                        productLog.setOperator(userName);
                        productLog.setResult(responseJson.isSuccess());
                        productLog.setFailInfo(Optional.ofNullable(responseJson.getMessage()).orElse(""));
                        // 记录改前该后值
                        String oldCountryList = Optional.ofNullable(accountInfo.getJoinedCountryList()).orElse("");
                        productLog.setNewRemark(String.format("改前值：%s,改后值：%s", oldCountryList, updateCountryDTO.getJoinedCountryList()));
                        aliexpressProductLogService.insert(productLog);

                    } catch (Exception e) {
                        // 处理报告
                        AliexpressProductLog productLog = new AliexpressProductLog();
                        productLog.setAccountNumber(accountInfo.getAccount());
                        productLog.setProductId(accountInfo.getProductId());
                        productLog.setOperateType(OperateLogTypeEnum.UPDATE_COUNTRY.getCode());
                        productLog.setOperator(userName);
                        productLog.setResult(false);
                        productLog.setFailInfo("调用平台接口失败，请稍后重试");
                        aliexpressProductLogService.insert(productLog);
                    }
                });
            });
        }
    }

}