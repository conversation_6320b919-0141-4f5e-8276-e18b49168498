package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.controller.AliexpressHalfTgItemController;
import com.estone.erp.publish.smt.enums.HalfExitCountryExamineStatusEnum;
import com.estone.erp.publish.smt.enums.HalfExitCountrySubmitEnum;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.ExcelImportDTO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountry;
import com.estone.erp.publish.tidb.publishtidb.service.SmtHalfExitCountryService;
import com.estone.erp.publish.tidb.publishtidb.util.JsonUtil;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.vo.SmtHalfExitCountryQueryVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 * <p>
 * smt半托管商品退出部分国家 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@RequestMapping("/smtHalfExitCountry")
public class SmtHalfExitCountryController {
    @Autowired
    private SmtHalfExitCountryService smtHalfExitCountryService;
    @Autowired
    private EsAliexpressProductListingService esAliexpressProductListingService;

    /**
     * 分页查询接口
     *
     * @param queryVO 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public ApiResult<IPage<SmtHalfExitCountry>> list(@RequestBody SmtHalfExitCountryQueryVO queryVO) {
        return ApiResult.newSuccess(smtHalfExitCountryService.list(queryVO));
    }

    /**
     * 更新数据（爬虫操作数据后更新）
     * 
     * @param smtHalfExitCountry 数据对象
     * @return ApiResult<?> 操作结果
     */
    @PostMapping("/updateBySubmitStatus")
    public ApiResult<String> updateBySubmitStatus(@RequestBody SmtHalfExitCountry smtHalfExitCountry) {
        // 校验必填字段
        if (smtHalfExitCountry.getId() == null || smtHalfExitCountry.getSubmitStatus() == null) {
            return ApiResult.newError("ID和审核状态为必填项");
        }

        try {
            // 执行更新操作
            boolean result = smtHalfExitCountryService.updateBySubmitStatus(smtHalfExitCountry);
            if (result) {
                return ApiResult.newSuccess("更新成功");
            } else {
                return ApiResult.newError("更新失败");
            }
        } catch (Exception e) {
            return ApiResult.newError("更新过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * Excel导入功能实现
     * 表头字段: 店铺、商品ID、退出国家、零售价，所有字段均为必填
     * 数据合并规则：店铺和商品ID合并成一条数据
     * 退出国家和零售价存储到exit_country_info字段，采用Map结构存储
     * 列表数据校验：若存在审核中的数据，则过滤
     * 保存数据时记录创建人、创建时间，提交状态默认为待提交
     */
    @PostMapping("/import")
    public ApiResult<String> importExcel(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (file == null || file.isEmpty()) {
            return ApiResult.newError("文件为空");
        }

        try {
            // 读取Excel数据
            List<ExcelImportDTO> importList = EasyExcel.read(file.getInputStream())
                    .head(ExcelImportDTO.class)
                    .sheet()
                    .doReadSync();

            // 校验导入数据是否为空
            if (importList == null || importList.isEmpty()) {
                return ApiResult.newError("导入数据为空");
            }

            // 创建数据映射表和获取当前用户信息
            Map<String, SmtHalfExitCountry> dataMap = new HashMap<>();
            String currentUser = WebUtils.getUserName();
            Date now = new Date();

            // 处理每个导入数据项
            for (ExcelImportDTO dto : importList) {
                // 校验必填字段
                if (StringUtils.isBlank(dto.getAccount()) || dto.getProductId() == null ||
                    StringUtils.isBlank(dto.getExitCountry()) || dto.getRetailPrice() == null) {
                    continue; // 跳过不完整数据
                }

                List<String> halfCountryList = AliexpressHalfTgItemController.halfCountryList;
                String exitCountry = dto.getExitCountry();
                if (StringUtils.equalsIgnoreCase("GB", exitCountry)) {
                    exitCountry = "UK";
                }
                if (!halfCountryList.contains(dto.getExitCountry())) {
                    continue; // 跳过不支持的国家
                }

                //通过店铺和登录人 判断是否有店铺权限
                ResponseJson rsp = ExcelOperationUtils.authIntercept(dto.getAccount(), WebUtils.getUserName());
                if(!rsp.isSuccess()){
                    return ApiResult.newError(dto.getAccount() + " 无权限");
                }

                // 生成数据标识key
                String key = dto.getAccount() + "_" + dto.getProductId();
                // 列表数据校验：若存在审核中的数据，则过滤
                if (isExistingReviewData(dto.getAccount(), dto.getProductId())) {
                    continue;
                }

                // 获取或创建新的SmtHalfExitCountry实体
                SmtHalfExitCountry entity = dataMap.get(key);
                if (entity == null) {
                    entity = new SmtHalfExitCountry();
                    entity.setAccount(dto.getAccount());
                    entity.setProductId(dto.getProductId());
                    entity.setCreatedBy(currentUser);
                    entity.setCreatedTime(now);
                    entity.setSubmitStatus(HalfExitCountrySubmitEnum.S_0.getCode()); // 默认待提交
                    entity.setExitCountryInfo(JsonUtil.toJson(new HashMap<>()));
                }

                // 更新退出国家和零售价信息
                Map<String, Object> exitCountryInfo = JsonUtil.fromJson(entity.getExitCountryInfo(), Map.class);
                exitCountryInfo.put(dto.getExitCountry(), dto.getRetailPrice());
                entity.setExitCountryInfo(JsonUtil.toJson(exitCountryInfo));
                dataMap.put(key, entity);
            }

            // 保存处理后的数据
            List<SmtHalfExitCountry> saveList = new ArrayList<>(dataMap.values());
            if (!saveList.isEmpty()) {
                //saveList 需要通过店铺和产品id 查询数据库是否存在数据，如果存在并且提交状态为待提交 使用待提交数据的主键做更新处理，否则插入
                saveList.forEach(entity -> {
                    SmtHalfExitCountry existEntity = smtHalfExitCountryService.getOne(new LambdaQueryWrapper<SmtHalfExitCountry>()
                            .eq(SmtHalfExitCountry::getAccount, entity.getAccount())
                            .eq(SmtHalfExitCountry::getProductId, entity.getProductId()));
                    if (existEntity != null && existEntity.getSubmitStatus() == HalfExitCountrySubmitEnum.S_0.getCode()) {
                        //补全更新时间和更新人
                        entity.setUpdateBy(currentUser);
                        entity.setUpdatedTime(now);
                        entity.setId(existEntity.getId());
                    }
                });
                smtHalfExitCountryService.saveOrUpdateBatch(saveList);
            }

            return ApiResult.newSuccess("导入成功");
        } catch (IOException e) {
            return ApiResult.newError("文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            return ApiResult.newError("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出功能
     *
     * @param
     * @return ApiResult<?> 导出结果
     */
    @PostMapping("/export")
    public ApiResult<?> export(@RequestBody SmtHalfExitCountryQueryVO dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        return smtHalfExitCountryService.download(dto);
    }

    /**
     * 检查是否存在审核中的数据
     * @param account 店铺
     * @param productId 商品ID
     * @return boolean 是否存在审核中的数据
     */
    private boolean isExistingReviewData(String account, Long productId) {
        // 根据店铺和产品ID查询是否存在审核中的数据
        // 调用Service层方法进行查询，假设存在审核状态字段（如 examine_status）为 0 表示审核中
        Integer count = smtHalfExitCountryService.countByShopAndProductIdAndReviewStatus(account, productId, HalfExitCountryExamineStatusEnum.S_0.getCode());
        if(count != null && count > 0){
            return true;
        }
        //查询是否存在半托管标签
        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setAliexpressAccountNumber(account);
        listingRequest.setProductId(productId);
        listingRequest.setHalfCountryExitLabelIsNull(false);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
            return true;
        }
        return false;
    }
}