package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-08 18:18
 **/
@Data
public class SingleDiscountItemVO {

    private Long id;

    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。 database column aliexpress_product.image_urls
     */
    private String imageUrls;

    private Long itemId;

    /**
     * 商品标题 长度在1-128之间英文。 database column aliexpress_product.subject
     */
    private String subject;


    /**
     * 最大sku价格 database column aliexpress_product.sku_price
     */
    private Double maxSkuPrice;

    /**
     * 最小sku价格 database column aliexpress_product.sku_price
     */
    private Double minSkuPrice;


    /**
     * 折扣率
     */
    private Integer discount;

    /**
     * 定向人群额外折扣类型,0=无,1=店铺粉丝
     */
    private Integer club_discount_type;

    /**
     * 粉丝折扣率
     */
    private Integer store_club_discount_rate;


    private List<SkuVO> skuVOList;
    /**
     * 限购数
     */
    private Integer buy_max_num;

    /**
     * 状态,0=未参加，1=已参加
     */
    private Integer status;

    private String groupIds;

    private String productStatusType;

    @Data
    public static class SkuVO {

        /**
         * skuid
         */
        private String platSkuId;

        /**
         * sku库存
         */
        private Integer ipmSkuStock;

    }
}
