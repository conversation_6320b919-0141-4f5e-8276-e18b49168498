package com.estone.erp.publish.smt.enums;

import lombok.Getter;

/**
 * smt 店铺分组
 */
@Getter
public enum SmtMarketingLogTypeEnum {
    // 1 营销活动配置 2 链接管理
    MARKETING_ACTIVITY_CONFIG(1, "营销配置"),
    ACCONT_GROUP(2, "账号分组"),
    ONLINE_GLOBAL_CONFIG(3, "上架全局配置"),
    ;

    private final Integer code;

    private final String desc;

    SmtMarketingLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
