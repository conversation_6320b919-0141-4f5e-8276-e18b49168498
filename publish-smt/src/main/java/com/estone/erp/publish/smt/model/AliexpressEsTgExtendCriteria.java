package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> aliexpress_es_tg_extend
 * 2023-04-03 14:40:38
 */
@Data
public class AliexpressEsTgExtendCriteria extends AliexpressEsTgExtend {
    private static final long serialVersionUID = 1L;

    /**
     * 调整库存 参数
     */
    private String esIdStr;
    private Integer updateStock;

    /**
     * 指定修改资质图片类型 1.GPSR 2.库存图 3.外包装
     */
    private List<Integer> updateQualificationTypeList;

    private List<String> spuList;

    public AliexpressEsTgExtendExample getExample() {
        AliexpressEsTgExtendExample example = new AliexpressEsTgExtendExample();
        AliexpressEsTgExtendExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAliexpressAccountNumber())) {
            criteria.andAliexpressAccountNumberEqualTo(this.getAliexpressAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getOwnerMemberId())) {
            criteria.andOwnerMemberIdEqualTo(this.getOwnerMemberId());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductSkusJson())) {
            criteria.andAeopAeProductSkusJsonEqualTo(this.getAeopAeProductSkusJson());
        }
        if (StringUtils.isNotBlank(this.getAeopAeMultimedia())) {
            criteria.andAeopAeMultimediaEqualTo(this.getAeopAeMultimedia());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductPropertysJson())) {
            criteria.andAeopAeProductPropertysJsonEqualTo(this.getAeopAeProductPropertysJson());
        }
        if (StringUtils.isNotBlank(this.getDetail())) {
            criteria.andDetailEqualTo(this.getDetail());
        }
        if (StringUtils.isNotBlank(this.getMobileDetail())) {
            criteria.andMobileDetailEqualTo(this.getMobileDetail());
        }
        if (StringUtils.isNotBlank(this.getAeopQualificationStructList())) {
            criteria.andAeopQualificationStructListEqualTo(this.getAeopQualificationStructList());
        }
        if (StringUtils.isNotBlank(this.getSquareImg())) {
            criteria.andSquareImgEqualTo(this.getSquareImg());
        }
        if (StringUtils.isNotBlank(this.getLongImg())) {
            criteria.andLongImgEqualTo(this.getLongImg());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1EqualTo(this.getAttribute1());
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }
        return example;
    }
}