package com.estone.erp.publish.smt.enums;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public enum DownEnum {
    sale_leader(1, "销售组长", "saleLeader"),
    sale(2, "销售", "sale"),
    account(3, "账号", "account"),
    product_id(4, "Product_Id", "productId"),
    dataSourceType(46, "数据来源", "dataSourceType"),
    title(5, "标题", "title"),
    cn(6, "中文申报名", "cn"),
    brand(42, "品牌", "brand"),
    article_number(7, "货号", "articleNumber"),
    sku(8, "sku", "sku"),
    price(9, "价格", "price"),
    stock(10, "库存", "stock"),
    item_status(11, "商品状态", "itemStatus"),
    gross_profit(12, "毛利", "grossProfit"),
    gross_profit_rate(13, "毛利率", "grossProfitRate"),
    shop_group(14, "店铺分组", "shopGroup"),
    freight_grpup(15, "运费分组", "freightGrpup"),
    sku_weight(16, "sku重量+包材重量 +搭配包材重量+3g", "skuWeight"),
    sku_cost(17, "sku销售成本价", "skuCost"),
    sku_packing_weight(18, "SKU对应的包材重量", "skuPackingWeight"),
    packing_cost(19, "包材价格+填充物价格", "packingCost"),
    sku_status(20, "单品状态", "skuStatus"),
    forbid(21, "禁售平台", "forbid"),
    infringementTypeNames(43, "禁售类型", "infringementTypeNames"),
    infringementObjs(44, "禁售原因", "infringementObjs"),
    prohibitionSites(45, "禁售站点", "prohibitionSites"),
    item_label(22, "产品标签", "itemLabel"),
    category_id(23, "类目id", "categoryId"),
    category_cn(24, "类目中文名", "categoryCn"),
    gross_weight(26, "重量", "grossWeight"),
    order_24H_count(27, "24小时销量", "order_24H_count"),
    order_last_7d_count(28, "7天销量", "order_last_7d_count"),
    order_last_14d_count(29, "14天销量", "order_last_14d_count"),
    order_last_30d_count(30, "30天销量", "order_last_30d_count"),
    order_last_60d_count(31, "60天销量", "order_last_60d_count"),
    order_num_total(32, "总销量", "order_num_total"),
    order_days_within_30d(33, "30天动销天数", "order_days_within_30d"),
    order_days_within_60d(34, "60天动销天数", "order_days_within_60d"),
    order_days_within_30d_rate(35, "30天动销率", "order_days_within_30d_rate"),
    order_days_within_60d_rate(36, "60天动销率", "order_days_within_60d_rate"),
    exposure_7d_count(55, "7天曝光量", "exposure_7d_count"),
    view_7d_count(56, "7天浏览量", "view_7d_count"),
    exposure_14d_count(57, "14天曝光量", "exposure_14d_count"),
    view_14d_count(58, "14天浏览量", "view_14d_count"),
    exposure_30d_count(37, "30天曝光量", "exposure_30d_count"),
    view_30d_count(38, "30天浏览量", "view_30d_count"),
    gmtCreate(39, "上架时间", "gmtCreate"),
    wsOfflineDate(40, "下架时间", "wsOfflineDate"),
    lastSyncTime(41, "同步时间", "lastSyncTime"),
    promotion(47, "是否促销", "promotion"),
    newState(48, "是否新品", "newState"),
    bulkOrder(49, "起批量", "bulkOrder"),
    bulkDiscount(50, "减免折扣", "bulkDiscount"),
    skuId(51, "skuId", "skuId"),
    system_stock(52, "可用-待发", "systemStock"),
    onlineStatus(53, "是否在线", "onlineStatus"),
    shippingCost(54, "采购运费", "shippingCost"),
    //上面14天浏览量是58，和其他曝光量放一起，从59开始
    standardWeight(59, "标准重量", "standardWeight"),
    singleDisCountName(60, "单品折扣名称", "singleDisCountName"),
    statusName(61, "活动状态", "statusName"),
    wholeStationDiscount(62, "全站折扣", "wholeStationDiscount"),

    storeClubDiscountRate(63, "粉丝折扣", "storeClubDiscountRate"),
    jit_order_num_24h(70, "JIT 24小时销量","jit_order_num_24h"),
    jit_order_num_7d(71, "JIT 7天销量","jit_order_num_7d"),
    jit_order_num_14d(72, "JIT 14天销量","jit_order_num_14d"),
    jit_order_num_30d(73, "JIT 30天销量","jit_order_num_30d"),
    jit_order_num_60d(74, "JIT 60天销量","jit_order_num_60d"),
    jit_order_num_180d(75, "JIT 180天销量","jit_order_num_180d"),
    jit_order_num_total(76, "JIT 总销量","jit_order_num_total"),
    halfCountryExitLabel(77, "半托管退出标签","halfCountryExitLabel"),
    ;

    private int code;

    private String name;

    private String excelValue;

    private DownEnum(int code, String name, String excelValue) {
        this.name = name;
        this.code = code;
        this.excelValue = excelValue;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExcelValue() {
        return excelValue;
    }

    public void setExcelValue(String excelValue) {
        this.excelValue = excelValue;
    }

    public static DownEnum build(int code) {
        DownEnum[] values = values();
        for (DownEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        DownEnum[] values = values();
        for (DownEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }


    public static List<String> getNameList(List<Integer> codeList){

        if(CollectionUtils.isEmpty(codeList)){
            return null;
        }
        List<String> nameList = new ArrayList<>();
        DownEnum[] values = values();
        for (DownEnum type : values) {
            int code = type.getCode();

            if(codeList.contains(code)){
                nameList.add(type.name);
            }
        }
        return nameList;
    }

    public static List<String> getExcelValueList(List<Integer> codeList){

        if(CollectionUtils.isEmpty(codeList)){
            return null;
        }
        List<String> excelValueList = new ArrayList<>();
        DownEnum[] values = values();
        for (DownEnum type : values) {
            int code = type.getCode();

            if(codeList.contains(code)){
                excelValueList.add(type.excelValue);
            }
        }
        return excelValueList;
    }


}
