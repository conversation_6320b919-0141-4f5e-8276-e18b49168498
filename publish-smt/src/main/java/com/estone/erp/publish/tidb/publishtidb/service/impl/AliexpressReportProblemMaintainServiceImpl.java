package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.AliexpressProductLogExample;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressReportProblemMaintainQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressReportProblemMaintainMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressReportProblemMaintain;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressReportProblemMaintainService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【aliexpress_report_problem_maintain】的数据库操作Service实现
 * @createDate 2025-03-04 14:38:43
 */
@Service
public class AliexpressReportProblemMaintainServiceImpl extends ServiceImpl<AliexpressReportProblemMaintainMapper, AliexpressReportProblemMaintain>
        implements AliexpressReportProblemMaintainService {


    @Resource
    private ExcelSend excelSend;

    @Resource
    private AliexpressProductLogService aliexpressProductLogService;

    @Override
    public AliexpressTemplateErrorMsgVO getTemplateErrorMsgAndSolution(AliexpressTemplateErrorMsgDto dto) {
        AliexpressProductLogExample example =  new AliexpressProductLogExample();
        AliexpressProductLogExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo(dto.getAccount());
        criteria.andRelationIdEqualTo(dto.getTemplateId());
        criteria.andOperateTypeEqualTo(OperateLogTypeEnum.POST.getCode());
        example.setOrderByClause("create_time desc");
        List<AliexpressProductLog> aliexpressProductLogs = aliexpressProductLogService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(aliexpressProductLogs)) {
            AliexpressProductLog aliexpressProductLog = aliexpressProductLogs.get(0);
            AliexpressReportProblemMaintain solutionByErrorMsg = this.getSolutionByErrorMsg(aliexpressProductLog.getFailInfo(), aliexpressProductLog.getOperateType());
            AliexpressTemplateErrorMsgVO vo = new AliexpressTemplateErrorMsgVO();
            vo.setSkuCode(aliexpressProductLog.getSkuCode());
            vo.setErrorMsg(aliexpressProductLog.getFailInfo());

            if (ObjectUtils.isNotEmpty(solutionByErrorMsg)){
                vo.setSolutionType(solutionByErrorMsg.getSolutionType());
                vo.setProblemType(solutionByErrorMsg.getProblemType());
            }
            return vo;
        }
        return null;
    }

    @Override
    public AliexpressReportProblemMaintain getSolutionByErrorMsg(String errorMsg, String operationType) {
        AliexpressReportProblemMaintain aliexpressReportProblemMaintain = baseMapper.selectSolutionByErrorMsg(errorMsg,operationType);
        return aliexpressReportProblemMaintain;
    }

    @Override
    public List<String> getAllSolutionType() {
        List<AliexpressReportProblemMaintain> list = this.list();
        return list.stream().map(AliexpressReportProblemMaintain::getProblemType).distinct().collect(Collectors.toList());
    }

    @Override
    public ResponseJson export(AliexpressReportProblemMaintainQueryDto dto) {

        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        LambdaQueryWrapper<AliexpressReportProblemMaintain> queryWrapper = this.getQueryWrapper(dto);
        Integer count = baseMapper.selectCount(queryWrapper);
        if (ObjectUtils.isEmpty(count) || count == 0) {
            responseJson.setMessage("不存在数据");
            return responseJson;
        }
        if (count > 500000) {
            responseJson.setMessage("导出数据不可超过50W,请缩小查询范围");
            return responseJson;
        }

        return excelSend.downloadAliexpressReportProblemMaintainMapper(ExcelTypeEnum.downloadProblemMaintain.getCode(), dto);
    }

    @Override
    public String saveOrUpdateByEntity(AliexpressReportProblemMaintain aliexpress) {
        String result = "";
        if (ObjectUtils.isEmpty(aliexpress)) {
            throw new BusinessException("参数不能为空");
        }
        //判断是否有重复类型数据
        LambdaQueryWrapper<AliexpressReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AliexpressReportProblemMaintain::getOperationType, aliexpress.getOperationType())
                .eq(AliexpressReportProblemMaintain::getReport, aliexpress.getReport());

        if (ObjectUtils.isNotEmpty(aliexpress.getId())) {
            queryWrapper.ne(AliexpressReportProblemMaintain::getId, aliexpress.getId());
            List<AliexpressReportProblemMaintain> aliexpressReportProblemMaintainList = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(aliexpressReportProblemMaintainList)) {
                throw new BusinessException("该类型数据已存在");
            }
            aliexpress.setUpdatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
            this.updateById(aliexpress);
            result = "修改成功";
        } else {
            List<AliexpressReportProblemMaintain> aliexpressReportProblemMaintainList = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(aliexpressReportProblemMaintainList)) {
                throw new BusinessException("该类型数据已存在");
            }
            aliexpress.setCreatedTime(new Timestamp(System.currentTimeMillis()));
            aliexpress.setCreatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
            this.save(aliexpress);
            result = "新增成功";
        }
        return result;
    }

    @Override
    public IPage<AliexpressReportProblemMaintain> pageQuery(AliexpressReportProblemMaintainQueryDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new BusinessException("查询条件不能为空");
        }
        LambdaQueryWrapper<AliexpressReportProblemMaintain> queryWrapper = this.getQueryWrapper(dto);
        Page<AliexpressReportProblemMaintain> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return this.page(page, queryWrapper);
    }

    private LambdaQueryWrapper<AliexpressReportProblemMaintain> getQueryWrapper(AliexpressReportProblemMaintainQueryDto dto) {
        LambdaQueryWrapper<AliexpressReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getOperationType())) {
            queryWrapper.eq(AliexpressReportProblemMaintain::getOperationType, dto.getOperationType());
        }
        if (StringUtils.isNotBlank(dto.getProblemType())) {
            queryWrapper.eq(AliexpressReportProblemMaintain::getProblemType, dto.getProblemType());
        }
        if (StringUtils.isNotBlank(dto.getReport())) {
            queryWrapper.like(AliexpressReportProblemMaintain::getReport, dto.getReport());
        }
        if (CollectionUtils.isNotEmpty(dto.getIdList())) {
            queryWrapper.in(AliexpressReportProblemMaintain::getId, dto.getIdList());
        }
        queryWrapper.orderByDesc(AliexpressReportProblemMaintain::getCreatedTime);
        return queryWrapper;
    }
}




