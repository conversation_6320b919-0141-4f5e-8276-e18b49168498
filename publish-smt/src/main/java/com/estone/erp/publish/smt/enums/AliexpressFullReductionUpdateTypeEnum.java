package com.estone.erp.publish.smt.enums;

public enum AliexpressFullReductionUpdateTypeEnum {
    UPDATE(1, "更新"),
    STOP(2, "暂停"),
    EFFECT(3, "生效");

    private int code;

    private String name;

    AliexpressFullReductionUpdateTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressFullReductionUpdateTypeEnum build(int code) {
        AliexpressFullReductionUpdateTypeEnum[] values = values();
        for (AliexpressFullReductionUpdateTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressFullReductionUpdateTypeEnum[] values = values();
        for (AliexpressFullReductionUpdateTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
