package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtTidbDataUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_tidb_data_update_log.id
     */
    private Long id;

    /**
     * 账号 database column smt_tidb_data_update_log.account
     */
    private String account;

    /**
     * 产品id database column smt_tidb_data_update_log.product_id
     */
    private Long productId;

    /**
     * 请求数据 database column smt_tidb_data_update_log.request_data
     */
    private String requestData;

    /**
     * 状态 database column smt_tidb_data_update_log.upload_state
     */
    private Integer uploadState;

    /**
     * 创建时间 database column smt_tidb_data_update_log.created_time
     */
    private Timestamp createdTime;
}