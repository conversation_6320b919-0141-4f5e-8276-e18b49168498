package com.estone.erp.publish.smt.enums;

public enum AccountConfigBatchUpdateEnum {
    CategoryIds("1", "修改分类"),
    MaxPublishNum("2", "修改最大刊登数量"),
    TIME("3", "修改刊登时间"),
    AutoGroundingNew("4", "自动上架新品"),
    AutoUpdateWeight("5", "自动调整重量"),
    AutoSupplyStock("6", "自动补库存"),
    AutoUpdateDeficitOrder("7", "系统自动更新亏损订单价格"),
    AutoUpdateTitle("8", "自动调整标题"),
    AutoUpdateDetail("9", "自动调整描述"),
    AutoUpdateSonimg("10", "自动调整图片"),
    AutoRecommendNewProduct("11", "是否分配新品"),
    AutoDownForBadItem("12", "自动下架竞争力不佳商品"),
    AddEprPackFee("13", "是否加入商品及物流包装环保费"),
    AddErpPrice("14", "是否自动添加ERP价格"),
    ;

    private String code;

    private String name;

    private AccountConfigBatchUpdateEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }
}
