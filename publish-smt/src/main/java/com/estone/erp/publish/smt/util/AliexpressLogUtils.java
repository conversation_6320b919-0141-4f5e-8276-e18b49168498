package com.estone.erp.publish.smt.util;

import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskErrorTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2021/7/13
 */
@Slf4j
public class AliexpressLogUtils {

    //定时任务半托管修改库存 全量修改产品状态停产存档，库存修改为0
    public static String halfTgUpdateStockZeroByStopJobHandler = "HalfTgUpdateStockZeroByStopJobHandler";

    //定时任务半托管修改库存 增量修改产品状态停产存档，库存修改为0
    public static String halfTgUpdateStockZeroEveryDayByStopJobHandler = "HalfTgUpdateStockZeroEveryDayByStopJobHandler";

    //监听订单推送修改pop和半托管库存
    public static String preReductionStockByOrderPushMqListener = "PreReductionStockByOrderPushMqListener";

    //定时任务半托管修改库存根据规则
    public static String halfTgUpdateStockZeroBySystemStockJobHandler = "HalfTgUpdateStockZeroBySystemStockJobHandler";

    //定时任务pop修改库存
    public static String smtUpdateStockMqListener = "SmtUpdateStockMqListener";

    //规则调整pop修改库存
    public static String smtRuleUpdatePopStockMqListener = "SmtRuleUpdatePopStockMqListener";

    //规则调整半托管修改库存
    public static String smtRuleUpdateHalfTgStockMqListener = "SmtRuleUpdateHalfTgStockMqListener";


    /**
     * 品牌
     */
    static List<String> brands = Arrays.asList("品牌出错", "没有获取到品牌信息");

    /**
     * 店铺配置拦截
     */
    static List<String> accountConfigErrors = Arrays.asList("获取不到配置", "没有设置好配置规则", "没有合适的配置", "Please attain the authorization before publishing", "TPL_NOT_EXIT:TPL_NOT_EXIT", "Input value not in candidate list");


    /**
     * 属性拦截
     */
    static List<String> attrEmptys = Arrays.asList("商品属性为空");


    /**
     * 系统拦截
     */
    static List<String> systemLimits = Arrays.asList("系统拦截", "已存在任务", "处理超时", "已经存在","不能刊登", "包含侵权平台smt!", "重复", "侵权词", "禁售平台");

    /**
     * 销售原因
     */
    static List<String> saleReasons = Arrays.asList("销售原因");

    /**
     * 系统错误
     */
    static List<String> systemError = Arrays.asList("系统错误", "failed and no fallback available", "logistics-engine-service", "SaleClient#", "ProductClient#", "java.lang.NoClassDefFoundError", "be empty", "publishApiController", "java.lang", "出现异常：", "m the fallback");

    /**
     * 平台报错
     */
    static List<String> platformError = Arrays.asList("平台报错", "Read timed out", "connect timed out", "Connection reset", "api.taobao.com", "message:Remote service error,subMessage:null", "message:Remote service error,subMessage:请求后端HTTP服务响应超时，请重试", "Industrial service exception", "maximum number of products you can publish", "获取分类属性失败", "productUnit_requiredValidator");

    /**
     * 设置错误类型
     */
    public static void setTaskErrorType(AliexpressProductLog productLog) {
        // 错误类型已经设置值 不需要再次处理
        if(null == productLog || StringUtils.isNotBlank(productLog.getResultType())) {
//            if(StringUtils.equalsIgnoreCase(productLog.getResultType(), "system_limit")){
//                log.info("a错误类型日志：" + JSON.toJSONString(productLog));
//            }
            return;
        }

        Boolean result = productLog.getResult();
        String operateType = productLog.getOperateType();
        if(null == result || result){
            return;
        }

        String resultMsg = productLog.getFailInfo();

        String taskErrorType = null;
        if(OperateLogTypeEnum.POST.getCode().equals(operateType)) {

            // 错误信息为空 直接系统错误
            if(StringUtils.isBlank(resultMsg)) {
                taskErrorType = TaskErrorTypeEnum.system_error.getErrorType();
            }
            else if (checkErrorType(resultMsg, systemLimits)) {
                taskErrorType = TaskErrorTypeEnum.system_limit.getErrorType();
            }
            else if(checkErrorType(resultMsg, brands)) {
                taskErrorType = TaskErrorTypeEnum.brand_error.getErrorType();
            }
            else if(checkErrorType(resultMsg, accountConfigErrors)) {
                taskErrorType = TaskErrorTypeEnum.account_config_error.getErrorType();
            }
            else if(checkErrorType(resultMsg, attrEmptys)) {
                taskErrorType = TaskErrorTypeEnum.attr_empty.getErrorType();
            }
            else if(checkErrorType(resultMsg, saleReasons)) {
                taskErrorType = TaskErrorTypeEnum.sale_reasons.getErrorType();
            }
            else if(checkErrorType(resultMsg, systemError)) {
                taskErrorType = TaskErrorTypeEnum.system_error.getErrorType();
            }
            else if(checkErrorType(resultMsg, platformError)) {
                taskErrorType = TaskErrorTypeEnum.platform_error.getErrorType();
            }
            // 未知错误归于销售原因
            else {
                taskErrorType = TaskErrorTypeEnum.sale_reasons.getErrorType();
            }
            productLog.setResultType(taskErrorType);
        }
    }

    /**
     * 判断单前错误是否属于该类型
     * @param resultMsg
     * @param errors
     * @return
     */
    private static Boolean checkErrorType(String resultMsg, List<String> errors) {
        if(StringUtils.isBlank(resultMsg) || CollectionUtils.isEmpty(errors)) {
            return false;
        }

        for (String error : errors) {
            if(StringUtils.contains(resultMsg, error)) {
                return true;
            }
        }

        return false;
    }
}
