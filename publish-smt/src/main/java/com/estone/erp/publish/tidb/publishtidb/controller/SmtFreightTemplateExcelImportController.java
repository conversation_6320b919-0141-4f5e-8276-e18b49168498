package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.bean.FreightTemplateExcel;
import com.estone.erp.publish.smt.componet.ExcelDownloaderZip;
import com.estone.erp.publish.smt.enums.FreightImportStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtFreightTemplateExcelImportDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateExcelImport;
import com.estone.erp.publish.tidb.publishtidb.service.SmtFreightTemplateExcelImportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * smt smt 运费模板excel 导入
 * </p>
 *
 * <AUTHOR>
 * @since 2025年4月25日14:57:24
 */
@Slf4j
@RestController
@RequestMapping("/smtFreightTemplateExcelImport")
public class SmtFreightTemplateExcelImportController {
    @Resource
    private SmtFreightTemplateExcelImportService smtFreightTemplateExcelImportService;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;

    /**
     * 分页查询
     */
    @PostMapping("queryPage")
    public ApiResult<IPage<SmtFreightTemplateExcelImport>> queryPage(@RequestBody SmtFreightTemplateExcelImportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            IPage<SmtFreightTemplateExcelImport> page = smtFreightTemplateExcelImportService.pageQuery(dto);
            return ApiResult.newSuccess(page);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 校验导入店铺是否有权限
     * @param dto
     * @return
     */
    @PostMapping("checkAccount")
    public ApiResult<?> checkAccount(@RequestBody SmtFreightTemplateExcelImportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        String account = dto.getAccount();
        Asserts.isTrue(StringUtils.isNotBlank(account), ErrorCode.PARAM_EMPTY_ERROR);
        if(StringUtils.contains(account, "_")){
            account = CommonUtils.splitList(account, "_").get(0);
        }
        try {
            // 判断是否有权限
            ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
            if (!superAdminOrEquivalent.isSuccess()) {
                throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
            }
            if (superAdminOrEquivalent.getResult()) {
                //如果是超管 需要校验下是否是有效店铺
                List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
                List<String> collect = saleAccountListBySaleChannel.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
                if(collect.contains(account)){
                    return ApiResult.newSuccess(true);
                }else{
                    return ApiResult.newError("店铺无效，请检查店铺是否不可用或者不存在" + account);
                }
            }
            List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(null, null, null, null, SaleChannel.CHANNEL_SMT, true);
            if(!currentUserPermission.contains(account)){
                return ApiResult.newSuccess(false);
            }
            return ApiResult.newSuccess(true);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 导入
     * @param freightTemplateExcelList
     * @return
     */
    @PostMapping("importInsert")
    public ApiResult<?> importInsert(@RequestBody List<FreightTemplateExcel> freightTemplateExcelList) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(freightTemplateExcelList), ErrorCode.PARAM_EMPTY_ERROR);
        try {

            Map<String, List<FreightTemplateExcel>> fileMap = new HashMap<>();

            for (FreightTemplateExcel freightTemplateExcel : freightTemplateExcelList) {
                //  publish/smt/店铺A_时间/运费模板/xl.excel
                String filePath = freightTemplateExcel.getFilePath();
                String[] split = filePath.split("/");

                int length = split.length;
                String tempName = split[length - 2].trim(); //运费模板名称
                String file_name = split[length - 3].trim(); //文件名称
                String account = file_name;
                if(file_name.contains("_")){
                    account = file_name.split("_")[0];
                }
                freightTemplateExcel.setAccount(account);
                freightTemplateExcel.setTempName(tempName);
                freightTemplateExcel.setFileName(file_name);

                String key = account + ";" + tempName;
                List<FreightTemplateExcel> freightTemplateExcels = fileMap.get(key);
                if(CollectionUtils.isEmpty(freightTemplateExcels)){
                    freightTemplateExcels = new ArrayList<>();
                    fileMap.put(key, freightTemplateExcels);
                }
                freightTemplateExcels.add(freightTemplateExcel);
            }

            List<SmtFreightTemplateExcelImport> createList = new ArrayList<>();
            List<SmtFreightTemplateExcelImport> updateList = new ArrayList<>();

            for (Map.Entry<String, List<FreightTemplateExcel>> stringListEntry : fileMap.entrySet()) {

                SmtFreightTemplateExcelImport smtFreightTemplateExcelImport = new SmtFreightTemplateExcelImport();

                String key = stringListEntry.getKey();
                List<FreightTemplateExcel> value = stringListEntry.getValue();

                String fileName = value.get(0).getFileName();

                List<String> urlList = value.stream().map(t -> t.getUrl()).collect(Collectors.toList());

                String[] split = key.split(";");
                String account = split[0];
                String tempName = split[1];

                smtFreightTemplateExcelImport.setAccount(account);
                smtFreightTemplateExcelImport.setFileName(fileName);
                smtFreightTemplateExcelImport.setTempName(tempName);
                smtFreightTemplateExcelImport.setExcelUrls(StringUtils.join(urlList, ","));
                smtFreightTemplateExcelImport.setExecuteStatus(FreightImportStatusEnum.S_0.getCode());

                LambdaQueryWrapper<SmtFreightTemplateExcelImport> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SmtFreightTemplateExcelImport::getAccount, account);
                queryWrapper.eq(SmtFreightTemplateExcelImport::getTempName, tempName);
                queryWrapper.eq(SmtFreightTemplateExcelImport::getExecuteStatus, FreightImportStatusEnum.S_0.getCode());
                SmtFreightTemplateExcelImport one = smtFreightTemplateExcelImportService.getOne(queryWrapper);
                if(one == null || one.getId() == null){
                    //新增
                    smtFreightTemplateExcelImport.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                    smtFreightTemplateExcelImport.setCreatedBy(WebUtils.getUserName());
                    createList.add(smtFreightTemplateExcelImport);

                }else{
                    //修改
                    smtFreightTemplateExcelImport.setId(one.getId());
                    smtFreightTemplateExcelImport.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
                    smtFreightTemplateExcelImport.setUpdateBy(WebUtils.getUserName());
                    updateList.add(smtFreightTemplateExcelImport);
                }
            }

            if(CollectionUtils.isNotEmpty(createList)){
                smtFreightTemplateExcelImportService.saveBatch(createList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                smtFreightTemplateExcelImportService.updateBatchById(updateList);
            }
            return ApiResult.newSuccess("导入成功");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 删除
     * @param excelImportList
     * @return
     */
    @PostMapping("deleteData")
    public ApiResult<?> deleteData(@RequestBody List<SmtFreightTemplateExcelImport> excelImportList) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(excelImportList), ErrorCode.PARAM_EMPTY_ERROR);
        List<Long> idList = excelImportList.stream().filter(t -> t.getExecuteStatus() != null && t.getExecuteStatus() != FreightImportStatusEnum.S_9.getCode()).map(t -> t.getId()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(idList)){
            return ApiResult.newError("没有需要删除的数据！");
        }
        try {
            boolean b = smtFreightTemplateExcelImportService.removeByIds(idList);
            //记录处理报告日志
            if(b){
                for (SmtFreightTemplateExcelImport smtFreightTemplateExcelImport : excelImportList) {
                    String account = smtFreightTemplateExcelImport.getAccount();
                    String tempName = smtFreightTemplateExcelImport.getTempName();

                    AliexpressProductLog productLog = new AliexpressProductLog();
                    productLog.setAccountNumber(account);
                    productLog.setOperateType(OperateLogTypeEnum.DELETE_FREIGHT_EXCLE_IMPORT.getCode());
                    productLog.setOperateStatus(OperateLogStatusEnum.end.getCode());
                    productLog.setOperator(WebUtils.getUserName());
                    productLog.setNewRemark(tempName);
                    aliexpressProductLogService.insert(productLog);
                }
            }
            return b ? ApiResult.newSuccess() : ApiResult.newError("删除失败！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 下载文件
     * @param dto
     * @return
     */
    @PostMapping("downFile")
    public void downFile(@RequestBody SmtFreightTemplateExcelImportDto dto, HttpServletResponse response) throws Exception{
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        Long id = dto.getId();
        Asserts.isTrue(id != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            SmtFreightTemplateExcelImport excelImport = smtFreightTemplateExcelImportService.getById(id);
            if(excelImport == null || excelImport.getId() == null || StringUtils.isBlank(excelImport.getExcelUrls())){
                log.error("没需要下载的数据");
//                return ApiResult.newError("没需要下载的数据");
                return;
            }
            String excelUrls = excelImport.getExcelUrls();
            String zipFilePath = excelImport.getTempName() + ".zip";
            ExcelDownloaderZip.downloadUrlsToZip(response, CommonUtils.splitList(excelUrls, ","), zipFilePath);
//            return  ApiResult.newSuccess();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
//            return ApiResult.newError(e.getMessage());
        }
    }


}
