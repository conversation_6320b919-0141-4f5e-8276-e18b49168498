package com.estone.erp.publish.smt.enums;

public enum UpdateStockResultEnum {
    fail(0, "失败"),
    success(1, "成功"),
    filter(2, "过滤");

    private int code;

    private String name;
    private UpdateStockResultEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static UpdateStockResultEnum build(int code) {
        UpdateStockResultEnum[] values = values();
        for (UpdateStockResultEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        UpdateStockResultEnum[] values = values();
        for (UpdateStockResultEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
