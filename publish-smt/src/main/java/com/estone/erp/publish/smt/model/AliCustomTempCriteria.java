package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> ali_custom_temp
 * 2020-06-01 15:33:29
 */
public class AliCustomTempCriteria extends AliCustomTemp {
    private static final long serialVersionUID = 1L;

    public AliCustomTempExample getExample() {
        AliCustomTempExample example = new AliCustomTempExample();
        AliCustomTempExample.Criteria criteria = example.createCriteria();
        if (this.getTempType() != null) {
            criteria.andTempTypeEqualTo(this.getTempType());
        }
        if (StringUtils.isNotBlank(this.getTempName())) {
            criteria.andTempNameEqualTo(this.getTempName());
        }
        if (StringUtils.isNotBlank(this.getTempJson())) {
            criteria.andTempJsonEqualTo(this.getTempJson());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdatedBy())) {
            criteria.andLastUpdatedByEqualTo(this.getLastUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1EqualTo(this.getAttribute1());
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }

        //排序
        example.setOrderByClause("create_date desc");

        return example;
    }
}