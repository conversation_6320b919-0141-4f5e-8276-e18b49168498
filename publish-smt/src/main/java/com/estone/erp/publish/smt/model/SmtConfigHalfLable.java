package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtConfigHalfLable implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_config_half_lable.id
     */
    private Integer id;

    /**
     * 店铺 database column smt_config_half_lable.account
     */
    private String account;

    /**
     * 产品标签 database column smt_config_half_lable.product_tag
     */
    private String productTag;

    /**
     * 平台标签 database column smt_config_half_lable.platform_tag
     */
    private String platformTag;

    /**
     * 状态 database column smt_config_half_lable.upload_state
     */
    private Integer uploadState;

    /**
     * 创建时间 database column smt_config_half_lable.created_time
     */
    private Timestamp createdTime;

    /**
     * 创建人 database column smt_config_half_lable.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column smt_config_half_lable.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column smt_config_half_lable.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column smt_config_half_lable.update_date
     */
    private Timestamp updateDate;
}