package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressSkuOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_sku_order.id
     */
    private Integer id;

    /**
     * 店铺 database column aliexpress_sku_order.account_number
     */
    private String accountNumber;

    /**
     * 平台SKU database column aliexpress_sku_order.seller_sku
     */
    private String sellerSku;

    /**
     * 系统SKU database column aliexpress_sku_order.system_sku
     */
    private String systemSku;

    /**
     * 主货号 database column aliexpress_sku_order.article_number
     */
    private String articleNumber;

    /**
     * 订单号 database column aliexpress_sku_order.order_no
     */
    private String orderNo;

    /**
     * 价格 database column aliexpress_sku_order.price
     */
    private Double price;

    /**
     * 付款时间 database column aliexpress_sku_order.payment_time
     */
    private Timestamp paymentTime;

    /**
     * 创建时间 database column aliexpress_sku_order.create_time
     */
    private Timestamp createTime;
}