package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProDTO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SmtSingleDiscountProductMapper {
    int countByExample(SmtSingleDiscountProductExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(SmtSingleDiscountProduct record);

    SmtSingleDiscountProduct selectByPrimaryKey(Long id);

    List<SmtSingleDiscountProduct> selectByExample(SmtSingleDiscountProductExample example);

    int updateByExampleSelective(@Param("record") SmtSingleDiscountProduct record, @Param("example") SmtSingleDiscountProductExample example);

    int updateByPrimaryKeySelective(SmtSingleDiscountProduct record);

    int batchInsert(@Param("list") List<SmtSingleDiscountProduct> singleDiscountProductList);

    void batchUpdate(@Param("list") List<SingleDiscountProDTO> needUpdateList);

    List<Long> selectIdByExample(SmtSingleDiscountProductExample singleDiscountProductExample2);

    int delete(@Param("needDelList") List<Long> needDelList,@Param("localSingleDiscountId") Long localSingleDiscountId);

    void deleteBySingleDiscount(@Param("singleDiscountId") Long singleDiscountId, @Param("productIdList") List<Long> productIdList);
}