package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.TemplateStatusEnum;
import com.estone.erp.publish.smt.model.AliexpressOnlineGlobalConfig;
import com.estone.erp.publish.smt.model.AliexpressTemplateExample;
import com.estone.erp.publish.smt.model.dto.AliexpressOnlineGlobalConfigParam;
import com.estone.erp.publish.smt.model.dto.AliexpressSpuDaysAllowPublishDto;
import com.estone.erp.publish.smt.service.AliexpressOnlineGlobalConfigService;
import com.estone.erp.publish.smt.service.AliexpressTemplateService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.QuerySpuOrderCount;
import com.estone.erp.publish.system.product.response.SpuOrderCount;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.smt.util
 * @Author: sj
 * @CreateTime: 2025-03-05  10:36
 * @Description: TODO
 */
@Component
public class AliexpressOnlineUtils {

    private static final Logger log = LoggerFactory.getLogger(AliexpressOnlineUtils.class);
    @Resource
    private AliexpressOnlineGlobalConfigService aliexpressOnlineGlobalConfigService;

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Resource
    private AliexpressTemplateService aliexpressTemplateService;

    /**
     * 判断是否可以发布
     * @param spu
     * @return false：不可以发布，true：可以发布
     */
    public boolean checkCanPublish(String spu) {
        if (ObjectUtils.isEmpty(spu)) {
            return true;
        }
        AliexpressOnlineGlobalConfig config = aliexpressOnlineGlobalConfigService.getConfig();
        if (ObjectUtils.isEmpty(config)) {
            return true;
        }
        AliexpressOnlineGlobalConfigParam param = config.toParam(config);
        if (ObjectUtils.isEmpty(param.getStatus()) || param.getStatus().equals(0)) {
            return true;
        }

        Integer setType = param.getSetType();
        if (ObjectUtils.isEmpty(setType)) {
            return true;
        }

        //获取SPU在线链接数：在线列表上架+审核中且在线状态+当天刊登中+刊登成功
        Integer onlineNum = getOnlineNum(spu);
        log.info("SMT刊登拦截：spu:{}，onlineNum:{}", spu,onlineNum);

        //spu允许刊登最大类型
        if (setType.equals(1)) {
            Integer configMaxPublishNum = param.getSpuAllowPublishNum();
            if (ObjectUtils.isEmpty(configMaxPublishNum)) {
                return true;
            }
            if (onlineNum >= configMaxPublishNum) {
                return false;
            }

        }

        //spu按天数限制类型
        if (setType.equals(0)) {
            List<AliexpressSpuDaysAllowPublishDto> spuDaysAllowPublish = param.getSpuDaysAllowPublish();
            if (CollectionUtils.isEmpty(spuDaysAllowPublish)) {
                return true;
            }
            List<AliexpressSpuDaysAllowPublishDto> publishList = spuDaysAllowPublish.stream()
                    .sorted(Comparator.comparing(AliexpressSpuDaysAllowPublishDto::getDays)
                    .thenComparing(AliexpressSpuDaysAllowPublishDto::getSpuNum, Comparator.reverseOrder())).collect(Collectors.toList());

            for (AliexpressSpuDaysAllowPublishDto aliexpressSpuDaysAllowPublishDto : publishList) {
                Integer configMaxPublishNum = aliexpressSpuDaysAllowPublishDto.getAllowPublishNum();
                Integer configOrderNum = aliexpressSpuDaysAllowPublishDto.getSpuNum();
                Integer days = aliexpressSpuDaysAllowPublishDto.getDays();
                Integer orderNum = querySpuOrderCount(List.of(spu), days).getOrDefault(spu, 0);
                log.info("SMT刊登拦截：spu:{}，orderNum:{}", spu,orderNum);
                if (onlineNum >= configMaxPublishNum && orderNum > configOrderNum) {
                    log.info("SMT刊登拦截：spu:{}，超过最大限制，在线链接数：{}，出单量：{}，匹配的规则是：{}", spu,onlineNum, orderNum,JSON.toJSON(aliexpressSpuDaysAllowPublishDto));
                    return false;
                }
            }
        }

        return true;

    }

    /**
     * 获取出单数量
     */
    public Map<String, Integer> querySpuOrderCount(List<String> spuList, Integer days) {
        QuerySpuOrderCount querySpuOrderCount = new QuerySpuOrderCount();
        querySpuOrderCount.setPlatform(SaleChannel.CHANNEL_SMT);
        querySpuOrderCount.setPlatformOrderCountCycle(days);
        querySpuOrderCount.setSpuList(spuList);
        List<SpuOrderCount> spuOrderCounts = ProductUtils.querySpuOrderCount(querySpuOrderCount);
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(spuOrderCounts)) {
            return new HashMap<>();
        }
        return spuOrderCounts.stream().collect(Collectors.toMap(SpuOrderCount::getSpu, SpuOrderCount::getPlatformOrderCount, (a, b) -> b));
    }

    //在线列表上架+审核中且在线状态+当天刊登中
    private Integer getOnlineNum(String spu) {
        //获取在线列表数量
        int onlineListingNum = this.getOnlineListingNum(spu);

        //获取模板中当天的刊登中的数量
        int todayTemplateNum = this.getTodayTemplateNum(spu);

        return onlineListingNum + todayTemplateNum;

    }

    private int getTodayTemplateNum(String spu) {
        AliexpressTemplateExample example = new AliexpressTemplateExample();
        example.createCriteria()
                .andArticleNumberEqualTo(spu)
                .andTemplateStatusIn(List.of(TemplateStatusEnum.PUBLISHING.intCode()))
                .andCreateTimeGreaterThanOrEqualTo(new Timestamp(DateUtils.getToday().getTime()));
        return aliexpressTemplateService.countByExample(example);
    }

    private int getOnlineListingNum(String spu) {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setSpu(spu);
        request.setProductStatusType("onSelling, auditing");
        request.setQueryFields(new String[]{"id", "productId", "spu"});
        List<EsAliexpressProductListing> productListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
        return CollectionUtils.isEmpty(productListing) ? 0 : productListing.size();
    }
}
