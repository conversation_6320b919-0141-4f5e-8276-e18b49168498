package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.crypto.engines.CramerShoupCiphertext;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> aliexpress_car_type
 * 2020-01-17 10:03:01
 */
@Data
public class AliexpressCarTypeCriteria extends AliexpressCarType {
    private static final long serialVersionUID = 1L;

    private Boolean findNullJson;

    private List<Long> typeIdList = new ArrayList<>();

    private String param2Like;

    public AliexpressCarTypeExample getExample() {
        AliexpressCarTypeExample example = new AliexpressCarTypeExample();
        AliexpressCarTypeExample.Criteria criteria = example.createCriteria();

        if(StringUtils.isNotBlank(param2Like)){
            criteria.andParam2Like("%" + param2Like + "%");
        }

        if(CollectionUtils.isNotEmpty(typeIdList)){
            criteria.andTypeIdIn(typeIdList);
        }

        if (this.getLeafCategoryId() != null) {
            criteria.andLeafCategoryIdEqualTo(this.getLeafCategoryId());
        }
        if (this.getTypeId() != null) {
            criteria.andTypeIdEqualTo(this.getTypeId());
        }
        if (this.getParentTypeId() != null) {
            criteria.andParentTypeIdEqualTo(this.getParentTypeId());
        }
        if (this.getTypeLevel() != null) {
            criteria.andTypeLevelEqualTo(this.getTypeLevel());
        }
        if (StringUtils.isNotBlank(this.getCarType())) {
            criteria.andCarTypeEqualTo(this.getCarType());
        }
        if (this.getParam1() != null) {
            criteria.andParam1EqualTo(this.getParam1());
        }
        if (StringUtils.isNotBlank(this.getParam2())) {
            criteria.andParam2EqualTo(this.getParam2());
        }
        if (StringUtils.isNotBlank(this.getAttributeJson())) {
            criteria.andAttributeJsonEqualTo(this.getAttributeJson());
        }

        if(this.getFindNullJson() != null && this.getFindNullJson()){
            criteria.andAttributeJsonIsNull();
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        return example;
    }
}