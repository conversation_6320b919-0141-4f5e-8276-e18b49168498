package com.estone.erp.publish.smt.enums;

/**
 * 0否 1是 2不调价
 */
public enum DeficitOrderPriceStatusEnum {

    ZEOR(0, "否"),

    ONE(1, "是"),

    TWO(2, "不调价"),
    ;

    private int code;

    private String name;

    private DeficitOrderPriceStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DeficitOrderPriceStatusEnum build(int code) {
        DeficitOrderPriceStatusEnum[] values = values();
        for (DeficitOrderPriceStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        DeficitOrderPriceStatusEnum[] values = values();
        for (DeficitOrderPriceStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
