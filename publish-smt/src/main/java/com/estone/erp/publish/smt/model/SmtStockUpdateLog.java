package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtStockUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_stock_update_log.id
     */
    private Long id;

    /**
     * 店铺 database column smt_stock_update_log.account
     */
    private String account;

    /**
     * 产品id database column smt_stock_update_log.product_id
     */
    private Long productId;

    /**
     * 货号 database column smt_stock_update_log.article_number
     */
    private String articleNumber;

    /**
     * sku_id database column smt_stock_update_log.sku_id
     */
    private String skuId;

    /**
     * redis库存 database column smt_stock_update_log.redis_stock
     */
    private Integer redisStock;

    /**
     * 改前库存 database column smt_stock_update_log.stock_before
     */
    private Integer stockBefore;

    /**
     * 改后库存 database column smt_stock_update_log.stock_after
     */
    private Integer stockAfter;

    /**
     * sku全平台30天销量 database column smt_stock_update_log.order_num_30d
     */
    private Integer orderNum30d;

    /**
     * 30天动销天数（全平台） database column smt_stock_update_log.order_days_within_30d
     */
    private Integer orderDaysWithin30d;

    /**
     * smt平台出单比例 database column smt_stock_update_log.smt_order_rate
     */
    private Double smtOrderRate;

    /**
     * 创建时间 database column smt_stock_update_log.create_date
     */
    private Timestamp createDate;

    /**
     * 操作结果 database column smt_stock_update_log.result
     */
    private Boolean result;

    /**
     * 错误信息 database column smt_stock_update_log.fail_info
     */
    private String failInfo;
}