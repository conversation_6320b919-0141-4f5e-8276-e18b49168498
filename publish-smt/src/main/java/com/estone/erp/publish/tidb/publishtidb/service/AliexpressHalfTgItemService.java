package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.smt.bean.HalfTgItemRequest;
import com.estone.erp.publish.smt.bean.HalfTgItemUpdatePack;
import com.estone.erp.publish.smt.bean.HalfTgItemUpdatePackageWeight;
import com.estone.erp.publish.smt.bean.UpdatePriceEntity;
import com.estone.erp.publish.smt.bean.excel.UpdateStockExcel;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateCountryDTO;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> aliexpress_half_tg_item
 * 2023-11-07 17:05:33
 */
public interface AliexpressHalfTgItemService {
    int countByExample(AliexpressHalfTgItemExample example);

    CQueryResult<AliexpressHalfTgItem> search(CQuery<AliexpressHalfTgItemCriteria> cquery);

    List<AliexpressHalfTgItem> selectByExample(AliexpressHalfTgItemExample example);

    AliexpressHalfTgItem selectByPrimaryKey(Long id);

    int insert(AliexpressHalfTgItem record);

    //货号变更更新数据
    int updateBySkuChange(AliexpressHalfTgItem record);

    int updateByPrimaryKeySelective(AliexpressHalfTgItem record);

    int updateByExampleSelective(AliexpressHalfTgItem record, AliexpressHalfTgItemExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void batchInsert(List<AliexpressHalfTgItem> recordList);

    void batchuUdate(List<AliexpressHalfTgItem> recordList);

    //定时同步产品信息变更 刷新到ES
    void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception;

    void synchItem(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, HalfTgSyncProductListRequest listRequest, Long logId);

    ResponseJson updateStocks(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemRequest> halfTgItemRequests, String userName);

    void updatePrices(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemRequest> halfTgItemRequests, String userName);

    void updatePackageWeight(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemUpdatePackageWeight> halfTgItemRequests, String userName);

    void updatePack(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, List<HalfTgItemUpdatePack> value, String userName);


    //根据货号更新 系统库存
    void batchUpdateSystemStock(String articleNumber);

    ResponseJson editItem(List<AliexpressHalfTgItem> halfTgItems, String userName);

    //excel 修改产品
    List<UpdatePriceEntity> updateHalfItem(String[] headers, MultipartFile file, String userName);

    //excel 修改半托管库存
    List<UpdateStockExcel> updateHalfStockForExcle(String[] headers, MultipartFile file, String userName);

    //查询店铺商品编码和商品条码不一致或者不是数字和字母的产品id
    List<Long> selectScCode(AliexpressHalfTgItemExample example);

    //批量修改产品的在线状态
    void batchUpdateOnlineStatus(List<Long> list);

    void deleteSmtHalfBinds(List<EsSkuBind> esSkuBindList);


    /**
     * 批量修改国家信息
     *
     * @param updateCountryDTO
     */
    void updateCountryList(AliexpressUpdateCountryDTO updateCountryDTO);

    /**
     * 半托管退出修改状态
     * @param account
     * @param productId
     */
    void exitUpdateOnlineStatus(String account, Long productId);
}