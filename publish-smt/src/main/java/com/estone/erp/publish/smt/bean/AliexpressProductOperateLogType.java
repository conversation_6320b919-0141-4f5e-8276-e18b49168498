package com.estone.erp.publish.smt.bean;

import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;

public class AliexpressProductOperateLogType {
	public final static String EDIT_PRICE = "edit price";
	public final static String EDIT_STOCK = "edit stock";
	public final static String ONLINE = "online";
	public final static String OFFLINE = "offline";
	public final static String POST = "post product";
	public final static String EDIT = "edit product";

	public final static String EDIT_IMG = "edit img";
	public final static String EDIT_IMG_TITLE = "edit img title";

	public final static String EDIT_CAR_TYPE = "edit car type";

	public final static String tran_temp = "tran temp";

	public final static String EDIT_SUBJECT_DETAIL = "edit subjectAndDetail";

	public final static String EDIT_TORT = "edit tort";

	public final static String UPDATE_ORIGIN = "update origin";

	public final static String PRICE28_UPDATE = "price28_update";

	public final static String PRICE28_CLEAR = "price28_clear";

	public final static String UPDATE_SEASON = "update season";

	public final static String delete_marketing_product = "delete marketing product";

	public final static String update_marketing_product = "update marketing product";

	public final static String add_marketing_product = "add marketing product";

	public final static String update_marketing_img = "update marketing img";

	public final static String DELETE = "delete";
	public final static String ON_SELLING = ProductStatusTypeEnum.onSelling.getCode();


}
