package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> aliexpress_sku_order
 * 2021-01-20 15:35:07
 */
public class AliexpressSkuOrderCriteria extends AliexpressSkuOrder {
    private static final long serialVersionUID = 1L;

    public AliexpressSkuOrderExample getExample() {
        AliexpressSkuOrderExample example = new AliexpressSkuOrderExample();
        AliexpressSkuOrderExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (StringUtils.isNotBlank(this.getSystemSku())) {
            criteria.andSystemSkuEqualTo(this.getSystemSku());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getOrderNo())) {
            criteria.andOrderNoEqualTo(this.getOrderNo());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getPaymentTime() != null) {
            criteria.andPaymentTimeEqualTo(this.getPaymentTime());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        return example;
    }
}