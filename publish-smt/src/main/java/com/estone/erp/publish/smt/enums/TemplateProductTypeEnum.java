package com.estone.erp.publish.smt.enums;

import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 范本模板产品类型
 * <AUTHOR>
 * @date 2022-09-15 15:15
 */
@Getter
@AllArgsConstructor
public enum TemplateProductTypeEnum {

    SINGLE(1, "产品系统"),
    SET_OF(2, "组合套装"),
    SPUS(3, "多SPU"),
    DATA_ANALYSE(4, "数据分析"),
    GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE(5, "冠通-大健云仓")
    ;

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static TemplateProductTypeEnum getEnum(Integer value) {
        if (value != null) {
            for (TemplateProductTypeEnum itemEnum : TemplateProductTypeEnum.values()) {
                if (itemEnum.getCode() == value) {
                    return itemEnum;
                }
            }
        }
        return null;
    }


    public static String fromValue(Integer value) {
        TemplateProductTypeEnum enums = getEnum(value);
        if (enums != null) {
            return enums.getDesc();
        }
        return null;
    }

    public static Integer transferToSkuDataSource(Integer productType) {
        if (SINGLE.isTrue(productType) || SPUS.isTrue(productType) || DATA_ANALYSE.isTrue(productType)) {
            return SkuDataSourceEnum.PRODUCT_SYSTEM.getCode();
        }

        if (SET_OF.isTrue(productType)) {
            return SkuDataSourceEnum.COMPOSE_SYSTEM.getCode();
        }

        if (GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(productType)) {
            return SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.getCode();
        }
        return null;

    }
}
