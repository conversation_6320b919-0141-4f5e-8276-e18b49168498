package com.estone.erp.publish.smt.model;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR> aliexpress_excel_log
 * 2022-01-08 17:07:30
 */
@Data
public class AliexpressExcelLogCriteria extends AliexpressExcelLog {
    private static final long serialVersionUID = 1L;

    private String fromCreateTime;

    private String toCreateTime;

    public AliexpressExcelLogExample getExample() throws Exception{
        AliexpressExcelLogExample example = new AliexpressExcelLogExample();
        AliexpressExcelLogExample.Criteria criteria = example.createCriteria();

        if(StringUtils.isNotBlank(this.getFromCreateTime())){
            criteria.andCreateTimeGreaterThanOrEqualTo(this.getFromCreateTime());
        }
        if(StringUtils.isNotBlank(this.getToCreateTime())){
            criteria.andCreateTimeLessThanOrEqualTo(this.getToCreateTime());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            String account = this.getAccount();
            criteria.andAccountsLike(CommonUtils.splitList(account, ","));
        }
        if (this.getType() != null) {
            criteria.andTypeEqualTo(this.getType());
        }
        if (this.getDownloadConut() != null) {
            criteria.andDownloadConutEqualTo(this.getDownloadConut());
        }
        if (this.getQueueUp() != null) {
            criteria.andQueueUpEqualTo(this.getQueueUp());
        }
        if (this.getVersionNumber() != null) {
            criteria.andVersionNumberEqualTo(this.getVersionNumber());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (StringUtils.isNotBlank(this.getExcelUploadUrl())) {
            criteria.andExcelUploadUrlEqualTo(this.getExcelUploadUrl());
        }
        if (StringUtils.isNotBlank(this.getExcelDownUrl())) {
            criteria.andExcelDownUrlEqualTo(this.getExcelDownUrl());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }else{
            //权限判断
            ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils
                    .isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
            if (!superAdminOrEquivalent.isSuccess()) {
                throw new Exception(superAdminOrEquivalent.getErrorMsg());
            }
            if (!superAdminOrEquivalent.getResult()) {
                criteria.andCreateByEqualTo(WebUtils.getUserName());
            }
        }
        if (StringUtils.isNotBlank(this.getCreateName())) {
            criteria.andCreateNameEqualTo(this.getCreateName());
        }
        if (this.getQueueExTime() != null) {
            criteria.andQueueExTimeEqualTo(this.getQueueExTime());
        }
        if (this.getCompleteTime() != null) {
            criteria.andCompleteTimeEqualTo(this.getCompleteTime());
        }
        if (StringUtils.isNotBlank(this.getErrorMsg())) {
            criteria.andErrorMsgEqualTo(this.getErrorMsg());
        }
        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("id desc");
        }
        return example;
    }
}