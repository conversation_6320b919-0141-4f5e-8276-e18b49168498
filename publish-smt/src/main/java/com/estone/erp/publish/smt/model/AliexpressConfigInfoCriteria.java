package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> aliexpress_config_info
 * 2020-01-08 11:09:01
 */
public class AliexpressConfigInfoCriteria extends AliexpressConfigInfo {
    private static final long serialVersionUID = 1L;

    public AliexpressConfigInfoExample getExample() {
        AliexpressConfigInfoExample example = new AliexpressConfigInfoExample();
        AliexpressConfigInfoExample.Criteria criteria = example.createCriteria();
        if (this.getConfigId() != null) {
            criteria.andConfigIdEqualTo(this.getConfigId());
        }
        if (this.getFromWeight() != null) {
            criteria.andFromWeightEqualTo(this.getFromWeight());
        }
        if (this.getToWeight() != null) {
            criteria.andToWeightEqualTo(this.getToWeight());
        }
        if (StringUtils.isNotBlank(this.getTagCodes())) {
            criteria.andTagCodesEqualTo(this.getTagCodes());
        }
        if (this.getGroupId() != null) {
            criteria.andGroupIdEqualTo(this.getGroupId());
        }
        if (this.getFreightTemplateId() != null) {
            criteria.andFreightTemplateIdEqualTo(this.getFreightTemplateId());
        }
        if (this.getPromiseTemplateId() != null) {
            criteria.andPromiseTemplateIdEqualTo(this.getPromiseTemplateId());
        }
        if (this.getGrossProfit() != null) {
            criteria.andGrossProfitEqualTo(this.getGrossProfit());
        }
        if (StringUtils.isNotBlank(this.getShippingMethod())) {
            criteria.andShippingMethodEqualTo(this.getShippingMethod());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getDiscountRate() != null) {
            criteria.andDiscountRateEqualTo(this.getDiscountRate());
        }
        return example;
    }
}