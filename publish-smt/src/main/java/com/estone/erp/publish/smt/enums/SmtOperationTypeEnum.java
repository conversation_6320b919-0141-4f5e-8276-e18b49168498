package com.estone.erp.publish.smt.enums;

public enum SmtOperationTypeEnum {
    MARKETING_ACTIVITY(1, "营销活动"),
    LISTING_CONFIG(2, "上架配置"),
    DELISTING_CONFIG(3, "下架配置"),
    LINK_MANAGEMENT(4, "链接管理生成");

    private final int code;
    private final String description;


    SmtOperationTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取枚举
    public static SmtOperationTypeEnum fromCode(int code) {
        for (SmtOperationTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching enum for code: " + code);
    }
}
