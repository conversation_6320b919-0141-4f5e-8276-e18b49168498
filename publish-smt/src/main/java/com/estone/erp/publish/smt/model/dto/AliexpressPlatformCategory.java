package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.smt.model.AliexpressCategory;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 分类 tree
 */
@Getter
@Setter
public class AliexpressPlatformCategory {

    private Integer id;

    private Integer categoryId;

    private String categoryName;

    private List<AliexpressPlatformCategory> subCategoryList;

    private Integer categoryLevel;

    public static AliexpressPlatformCategory of(AliexpressCategory aliexpressCategory) {
        return init(aliexpressCategory);
    }

    public AliexpressPlatformCategory addSubCategory(AliexpressCategory aliexpressCategory) {
        AliexpressPlatformCategory init = init(aliexpressCategory);
        this.getSubCategoryList().add(init);
        return init;
    }

    private static AliexpressPlatformCategory init(AliexpressCategory aliexpressCategory) {
        AliexpressPlatformCategory aliexpressPlatformCategory = new AliexpressPlatformCategory();
        aliexpressPlatformCategory.setCategoryId(aliexpressCategory.getCategoryId());
        aliexpressPlatformCategory.setCategoryName(aliexpressCategory.getCategoryZhName());
        aliexpressPlatformCategory.setId(aliexpressCategory.getId());
        aliexpressPlatformCategory.setCategoryLevel(aliexpressCategory.getCategoryLevel());
        aliexpressPlatformCategory.setSubCategoryList(new ArrayList<>());
        return aliexpressPlatformCategory;
    }

}
