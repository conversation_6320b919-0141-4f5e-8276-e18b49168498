package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-08-07 12:06:25
 */
public class SmtImgFailCriteria extends SmtImgFail {
    private static final long serialVersionUID = 1L;

    public SmtImgFailExample getExample() {
        SmtImgFailExample example = new SmtImgFailExample();
        SmtImgFailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getUrl())) {
            criteria.andUrlEqualTo(this.getUrl());
        }
        if (StringUtils.isNotBlank(this.getFailInfo())) {
            criteria.andFailInfoEqualTo(this.getFailInfo());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        return example;
    }
}