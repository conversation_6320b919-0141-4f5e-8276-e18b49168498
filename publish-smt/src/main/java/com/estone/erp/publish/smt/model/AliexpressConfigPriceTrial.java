package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import lombok.Data;

@Data
public class AliexpressConfigPriceTrial implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_config_price_trial.id
     */
    private Integer id;

    /**
     * 账号 database column aliexpress_config_price_trial.account_number
     */
    private String accountNumber;

    /**
     * 算价配置id database column aliexpress_config_price_trial.calc_config_id
     */
    private Integer calcConfigId;

    /**
     * 是否叶子结点 database column aliexpress_config_price_trial.leaf
     */
    private Boolean leaf;

    /**
     * 父级id database column aliexpress_config_price_trial.parent_id
     */
    private Integer parentId;

    /**
     * 国家 database column aliexpress_config_price_trial.country
     */
    private String country;

    /**
     * 毛利率 database column aliexpress_config_price_trial.gross_profit
     */
    private Double grossProfit;

    /**
     * 试算物流 database column aliexpress_config_price_trial.shipping_method
     */
    private String shippingMethod;

    /**
     * 价格区间前值 database column aliexpress_config_price_trial.from_price
     */
    private Double fromPrice;

    /**
     * 价格区间后值 database column aliexpress_config_price_trial.to_price
     */
    private Double toPrice;

    /**
     * 物流模板id database column aliexpress_config_price_trial.shipping_template_id
     */
    private Long shippingTemplateId;

    /**
     * 产品分组id database column aliexpress_config_price_trial.group_id
     */
    private Long groupId;

    /**
     * 创建人 database column aliexpress_config_price_trial.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_config_price_trial.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_config_price_trial.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_config_price_trial.update_date
     */
    private Timestamp updateDate;

    /**
     * 子配置
     */
    private List<AliexpressConfigPriceTrial> priceTrialList;

    /**
     * 随机id 非数据库字段 前端使用
     */
    private String randomId;

    /**
     * 随机父id 非数据库字段 前端使用
     */
    private String randomParentId;
}