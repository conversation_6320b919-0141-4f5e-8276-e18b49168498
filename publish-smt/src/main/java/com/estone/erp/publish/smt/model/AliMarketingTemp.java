package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class AliMarketingTemp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ali_marketing_temp.id
     */
    private Integer id;

    /**
     * 模板名称 database column ali_marketing_temp.temp_name
     */
    private String tempName;

    /**
     * 店铺 database column ali_marketing_temp.account
     */
    private String account;

    /**
     * 每行个数 database column ali_marketing_temp.array_num
     */
    private Integer arrayNum;

    /**
     * 模块产品数量 database column ali_marketing_temp.product_num
     */
    private Integer productNum;

    /**
     * 已关联产品数量 database column ali_marketing_temp.related_product_num
     */
    private Integer relatedProductNum;

    /**
     * 模板内容 database column ali_marketing_temp.html
     */
    private String html;

    /**
     * 创建时间 database column ali_marketing_temp.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column ali_marketing_temp.create_by
     */
    private String createBy;

    /**
     * 修改时间 database column ali_marketing_temp.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 修改人 database column ali_marketing_temp.last_updated_by
     */
    private String lastUpdatedBy;

    /**
     * 属性(预留) database column ali_marketing_temp.attribute1
     */
    private String attribute1;

    /**
     * 属性 database column ali_marketing_temp.attribute2
     */
    private String attribute2;

    /**
     * 属性 database column ali_marketing_temp.attribute3
     */
    private String attribute3;

    /**
     * 属性 database column ali_marketing_temp.attribute4
     */
    private String attribute4;

    /**
     * 属性 database column ali_marketing_temp.attribute5
     */
    private String attribute5;


    private List<AliMarketingTempInfo> infoList = new ArrayList<>();

}