package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> aliexpress_product_log
 * 2019-10-24 11:44:08
 */
@Data
public class AliexpressProductLogCriteria extends AliexpressProductLog {
    private static final long serialVersionUID = 1L;

    private String productIdStr;

    private Timestamp fromCreateTime;

    private Timestamp toCreateTime;

    private Timestamp fromOperateTime;

    private Timestamp toOperateTime;

    private String idStr;

    /**
     * 关联id
     */
    private String relationIdStr;

    /**
     * 权限账号
     */
    private List<String> authSellerList = new ArrayList<>();

    public AliexpressProductLogExample getExample() {
        AliexpressProductLogExample example = new AliexpressProductLogExample();
        AliexpressProductLogExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isNotBlank(this.getProblemType())){
            criteria.andProblemTypeEqualTo(this.getProblemType());
        }

        if(StringUtils.isNotBlank(this.getRelationIdStr())){
            criteria.andRelationIdIn(CommonUtils.splitList(this.getRelationIdStr(), ","));
        }

        if(StringUtils.isNotBlank(this.getResultType())){
            criteria.andResultTypeEqualTo(this.getResultType());
        }

        //操作状态
        if(this.getOperateStatus() != null){
            criteria.andOperateStatusEqualTo(this.getOperateStatus());
        }

        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }

        if(StringUtils.isNotBlank(this.getIdStr())){
            if(StringUtils.indexOf(this.getIdStr(), ",") != -1){
                criteria.andIdIn(CommonUtils.splitLongList(this.getIdStr(), ","));
            }else{
                criteria.andIdEqualTo(Long.valueOf(this.getIdStr()));
            }
        }

        if(StringUtils.isNotBlank(this.getProductIdStr())){
            if(StringUtils.indexOf(this.getProductIdStr(), ",") != -1){
                criteria.andProductIdIn(CommonUtils.splitLongList(this.getProductIdStr(), ","));
            }else{
                criteria.andProductIdEqualTo(Long.valueOf(this.getProductIdStr()));
            }
        }

        //操作人
        if (StringUtils.isNotBlank(this.getOperator())) {
            List<String> strings = CommonUtils.splitList(this.getOperator(), ",");
            if(strings.size() == 1){
                criteria.andOperatorEqualTo(this.getOperator());
            }else{
                criteria.andOperatorIn(strings);
            }

        }
        //账号
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            if(StringUtils.indexOf(this.getAccountNumber(), ",") != -1){
                criteria.andAccountNumberIn(CommonUtils.splitList(this.getAccountNumber(), ","));
            }else{
                criteria.andAccountNumberEqualTo(this.getAccountNumber());
            }
        }

        //权限
        if(StringUtils.isBlank(this.getOperator()) && StringUtils.isBlank(this.getAccountNumber())&& CollectionUtils.isNotEmpty(this.getAuthSellerList())){
            example.setAuthSellerList(this.getAuthSellerList());
        }

        if (StringUtils.isNotBlank(this.getSkuCode())) {
            if(StringUtils.indexOf(this.getSkuCode(), ",") != -1){
                criteria.andSkuCodeIn(CommonUtils.splitList(this.getSkuCode(), ","));
            }else{
                criteria.andSkuCodeEqualTo(this.getSkuCode());
            }
        }

        if (this.getOperateTime() != null) {
            criteria.andOperateTimeEqualTo(this.getOperateTime());
        }

        if(this.getFromOperateTime() != null){
            criteria.andOperateTimeGreaterThanOrEqualTo(this.getFromOperateTime());
        }

        if(this.getToOperateTime() != null){
            criteria.andOperateTimeLessThanOrEqualTo(this.getToOperateTime());
        }


        if(this.getFromCreateTime() != null){
            criteria.andCreateTimeGreaterThanOrEqualTo(this.getFromCreateTime());
        }

        if(this.getToCreateTime() != null){
            criteria.andCreateTimeLessThanOrEqualTo(this.getToCreateTime());
        }

        if (StringUtils.isNotBlank(this.getOperateType())) {
            criteria.andOperateTypeEqualTo(this.getOperateType());
        }
        if (this.getResult() != null) {
            criteria.andResultEqualTo(this.getResult());
        }
        if (StringUtils.isNotBlank(this.getFailInfo())) {
            criteria.andFailInfoEqualTo(this.getFailInfo());
        }
        if (this.getPriceBeforeEdit() != null) {
            criteria.andPriceBeforeEditEqualTo(this.getPriceBeforeEdit());
        }
        if (this.getPriceAfterEdit() != null) {
            criteria.andPriceAfterEditEqualTo(this.getPriceAfterEdit());
        }
        if (this.getStockBeforeEdit() != null) {
            criteria.andStockBeforeEditEqualTo(this.getStockBeforeEdit());
        }
        if (this.getStockAfterEdit() != null) {
            criteria.andStockAfterEditEqualTo(this.getStockAfterEdit());
        }

        if(StringUtils.isNotBlank(this.getRelationId())){
            criteria.andRelationIdEqualTo(this.getRelationId());
        }
        if(StringUtils.isNotBlank(this.getRuleName())){
            criteria.andRuleNameLike(this.getRuleName());
        }

        if(this.getRelationType() != null){
            criteria.andRelationTypeEqualTo(this.getRelationType());
        }

        if(StringUtils.isEmpty(example.getOrderByClause())){
            example.setOrderByClause("id desc");
        }
        return example;
    }
}