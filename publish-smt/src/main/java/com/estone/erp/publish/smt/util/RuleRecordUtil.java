package com.estone.erp.publish.smt.util;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.model.RuleRecord;
import com.estone.erp.publish.platform.model.RuleRecordExample;
import com.estone.erp.publish.platform.service.RuleRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：gt
 * @Date：2024/9/13 19:43
 */
@Slf4j
@Component
public class RuleRecordUtil {
    @Resource
    private RuleRecordService ruleRecordService;
    /**
     * 执行完保存记录
     */
    public void saveRuleRecord(Integer configId,String store,String ruleName,String ruleTableName,String ruleType){
        Timestamp now=new Timestamp(System.currentTimeMillis());
        RuleRecord record=new RuleRecord();
        record.setPlatform(SaleChannel.CHANNEL_SMT);
        record.setRuleTableName(ruleTableName);
        record.setRuleType(ruleType);
        record.setRuleId(configId);
        record.setAccounts(store);
        record.setRuleName(ruleName);
        record.setCreatedDate(now);
        record.setUpdateDate(now);
        ruleRecordService.insert(record);
    };


    /**
     * 查询执行记录
     * @param ruleId
     */
    public  int getRuleRecordCount(Integer ruleId,String ruleTableName,String ruleType,String store){
        RuleRecordExample example = new RuleRecordExample();
        RuleRecordExample.Criteria criteria=example.createCriteria();
        criteria.andPlatformEqualTo(SaleChannel.CHANNEL_SMT);
        criteria.andRuleTableNameEqualTo(ruleTableName);
        criteria.andRuleTypeEqualTo(ruleType);
        criteria.andRuleIdEqualTo(ruleId);
        criteria.andAccountsEqualTo(store);
        criteria.andCreatedDateBetween(new Timestamp(DateUtils.getDateBegin(0).getTime()),new Timestamp(DateUtils.getDateEnd(0).getTime()));
        int count=ruleRecordService.countByExample(example);

        return count;
    }

    /**
     * 查询执行记录
     * @param ruleId
     */
    public  Map<String,List<RuleRecord>> getRuleRecords(Integer ruleId, String ruleTableName, String ruleType, List<String> stores){
        RuleRecordExample example = new RuleRecordExample();
        RuleRecordExample.Criteria criteria=example.createCriteria();
        criteria.andPlatformEqualTo(SaleChannel.CHANNEL_SMT);
        criteria.andRuleTableNameEqualTo(ruleTableName);
        criteria.andRuleTypeEqualTo(ruleType);
        criteria.andRuleIdEqualTo(ruleId);
        criteria.andAccountsIn(stores);
        criteria.andCreatedDateBetween(new Timestamp(DateUtils.getDateBegin(0).getTime()),new Timestamp(DateUtils.getDateEnd(0).getTime()));
        List<RuleRecord> ruleRecords=ruleRecordService.selectByExample(example);
        Map<String,List<RuleRecord>> storeMaps=ruleRecords.stream().collect(Collectors.groupingBy(t->t.getAccounts()));
        return storeMaps;
    }

}
