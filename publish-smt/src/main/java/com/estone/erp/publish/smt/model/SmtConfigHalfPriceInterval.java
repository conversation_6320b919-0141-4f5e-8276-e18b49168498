package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtConfigHalfPriceInterval implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_config_half_price_interval.id
     */
    private Integer id;

    /**
     * 店铺 database column smt_config_half_price_interval.account
     */
    private String account;

    /**
     * 销售成本价区间 database column smt_config_half_price_interval.from_price
     */
    private Double fromPrice;

    /**
     * 销售成本价区间 database column smt_config_half_price_interval.to_price
     */
    private Double toPrice;

    /**
     * 重量区间 database column smt_config_half_price_interval.from_weight
     */
    private Double fromWeight;

    /**
     * 重量区间 database column smt_config_half_price_interval.to_weight
     */
    private Double toWeight;

    /**
     * 系数 更改为毛利率 database column smt_config_half_price_interval.ratio
     */
    private Double ratio;

    /**
     * 创建人 database column smt_config_half_price_interval.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column smt_config_half_price_interval.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column smt_config_half_price_interval.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column smt_config_half_price_interval.update_date
     */
    private Timestamp updateDate;
}