package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2025-02-20 15:04:56
 */
public class SmtAearPriceReLogCriteria extends SmtAearPriceReLog {
    private static final long serialVersionUID = 1L;

    public SmtAearPriceReLogExample getExample() {
        SmtAearPriceReLogExample example = new SmtAearPriceReLogExample();
        SmtAearPriceReLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getRequestJson())) {
            criteria.andRequestJsonEqualTo(this.getRequestJson());
        }
        if (this.getReCount() != null) {
            criteria.andReCountEqualTo(this.getReCount());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (this.getResultType() != null) {
            criteria.andResultTypeEqualTo(this.getResultType());
        }
        return example;
    }
}