package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressAutoPublishReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_auto_publish_report.id
     */
    private Long id;

    /**
     * 主sku database column aliexpress_auto_publish_report.spu
     */
    private String spu;

    /**
     * 图片 database column aliexpress_auto_publish_report.image
     */
    private String image;

    /**
     * 标题 database column aliexpress_auto_publish_report.title
     */
    private String title;

    /**
     * 系统类目code database column aliexpress_auto_publish_report.sys_category_code
     */
    private String sysCategoryCode;

    /**
     * 系统类目名称 database column aliexpress_auto_publish_report.sys_category_name
     */
    private String sysCategoryName;

    /**
     * 范本id集 database column aliexpress_auto_publish_report.parent_template_ids
     */
    private String parentTemplateIds;

    /**
     * spu关联模板id集 database column aliexpress_auto_publish_report.relation_template_ids
     */
    private String relationTemplateIds;

    /**
     * 刊登成功模板id集 database column aliexpress_auto_publish_report.success_template_ids
     */
    private String successTemplateIds;

    /**
     * 范本数量 database column smt_auto_publish_report.parent_template_count
     */
    private Integer parentTemplateCount;

    /**
     * spu关联模板数量 database column aliexpress_auto_publish_report.relation_count
     */
    private Integer relationCount;

    /**
     * 刊登成功模板数量 database column aliexpress_auto_publish_report.success_count
     */
    private Integer successCount;

    /**
     * 刊登成功率 database column aliexpress_auto_publish_report.success_rate
     */
    private Double successRate;

    /**
     * 订单数量 database column smt_auto_publish_report.order_count
     */
    private Integer orderCount;

    /**
     * 订单转化率 database column smt_auto_publish_report.out_order_rate
     */
    private Double outOrderRate;

    /**
     * 创建时间 database column aliexpress_auto_publish_report.create_time
     */
    private Timestamp createTime;

    /**
     * 读取模板的最后时间（以这个最大的时间，进行下次拉取） database column aliexpress_auto_publish_report.template_pull_time
     */
    private Timestamp templatePullTime;
}