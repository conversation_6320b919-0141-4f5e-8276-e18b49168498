package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtCategoryConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtCategoryConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIsNull() {
            addCriterion("category_full_path_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIsNotNull() {
            addCriterion("category_full_path_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeEqualTo(String value) {
            addCriterion("category_full_path_code =", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotEqualTo(String value) {
            addCriterion("category_full_path_code <>", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeGreaterThan(String value) {
            addCriterion("category_full_path_code >", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_full_path_code >=", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLessThan(String value) {
            addCriterion("category_full_path_code <", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLessThanOrEqualTo(String value) {
            addCriterion("category_full_path_code <=", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLike(String value) {
            addCriterion("category_full_path_code like", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotLike(String value) {
            addCriterion("category_full_path_code not like", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIn(List<String> values) {
            addCriterion("category_full_path_code in", values, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotIn(List<String> values) {
            addCriterion("category_full_path_code not in", values, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeBetween(String value1, String value2) {
            addCriterion("category_full_path_code between", value1, value2, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotBetween(String value1, String value2) {
            addCriterion("category_full_path_code not between", value1, value2, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameIsNull() {
            addCriterion("category_cn_full_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameIsNotNull() {
            addCriterion("category_cn_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameEqualTo(String value) {
            addCriterion("category_cn_full_name =", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameNotEqualTo(String value) {
            addCriterion("category_cn_full_name <>", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameGreaterThan(String value) {
            addCriterion("category_cn_full_name >", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_cn_full_name >=", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameLessThan(String value) {
            addCriterion("category_cn_full_name <", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameLessThanOrEqualTo(String value) {
            addCriterion("category_cn_full_name <=", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameLike(String value) {
            addCriterion("category_cn_full_name like", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameNotLike(String value) {
            addCriterion("category_cn_full_name not like", value, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameIn(List<String> values) {
            addCriterion("category_cn_full_name in", values, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameNotIn(List<String> values) {
            addCriterion("category_cn_full_name not in", values, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameBetween(String value1, String value2) {
            addCriterion("category_cn_full_name between", value1, value2, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andCategoryCnFullNameNotBetween(String value1, String value2) {
            addCriterion("category_cn_full_name not between", value1, value2, "categoryCnFullName");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueIsNull() {
            addCriterion("attribute_show_type_value is null");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueIsNotNull() {
            addCriterion("attribute_show_type_value is not null");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueEqualTo(String value) {
            addCriterion("attribute_show_type_value =", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueNotEqualTo(String value) {
            addCriterion("attribute_show_type_value <>", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueGreaterThan(String value) {
            addCriterion("attribute_show_type_value >", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueGreaterThanOrEqualTo(String value) {
            addCriterion("attribute_show_type_value >=", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueLessThan(String value) {
            addCriterion("attribute_show_type_value <", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueLessThanOrEqualTo(String value) {
            addCriterion("attribute_show_type_value <=", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueLike(String value) {
            addCriterion("attribute_show_type_value like", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueNotLike(String value) {
            addCriterion("attribute_show_type_value not like", value, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueIn(List<String> values) {
            addCriterion("attribute_show_type_value in", values, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueNotIn(List<String> values) {
            addCriterion("attribute_show_type_value not in", values, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueBetween(String value1, String value2) {
            addCriterion("attribute_show_type_value between", value1, value2, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttributeShowTypeValueNotBetween(String value1, String value2) {
            addCriterion("attribute_show_type_value not between", value1, value2, "attributeShowTypeValue");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdIsNull() {
            addCriterion("attr_name_id is null");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdIsNotNull() {
            addCriterion("attr_name_id is not null");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdEqualTo(Long value) {
            addCriterion("attr_name_id =", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdNotEqualTo(Long value) {
            addCriterion("attr_name_id <>", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdGreaterThan(Long value) {
            addCriterion("attr_name_id >", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdGreaterThanOrEqualTo(Long value) {
            addCriterion("attr_name_id >=", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdLessThan(Long value) {
            addCriterion("attr_name_id <", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdLessThanOrEqualTo(Long value) {
            addCriterion("attr_name_id <=", value, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdIn(List<Long> values) {
            addCriterion("attr_name_id in", values, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdNotIn(List<Long> values) {
            addCriterion("attr_name_id not in", values, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdBetween(Long value1, Long value2) {
            addCriterion("attr_name_id between", value1, value2, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIdNotBetween(Long value1, Long value2) {
            addCriterion("attr_name_id not between", value1, value2, "attrNameId");
            return (Criteria) this;
        }

        public Criteria andAttrNameIsNull() {
            addCriterion("attr_name is null");
            return (Criteria) this;
        }

        public Criteria andAttrNameIsNotNull() {
            addCriterion("attr_name is not null");
            return (Criteria) this;
        }

        public Criteria andAttrNameEqualTo(String value) {
            addCriterion("attr_name =", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameNotEqualTo(String value) {
            addCriterion("attr_name <>", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameGreaterThan(String value) {
            addCriterion("attr_name >", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameGreaterThanOrEqualTo(String value) {
            addCriterion("attr_name >=", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameLessThan(String value) {
            addCriterion("attr_name <", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameLessThanOrEqualTo(String value) {
            addCriterion("attr_name <=", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameLike(String value) {
            addCriterion("attr_name like", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameNotLike(String value) {
            addCriterion("attr_name not like", value, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameIn(List<String> values) {
            addCriterion("attr_name in", values, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameNotIn(List<String> values) {
            addCriterion("attr_name not in", values, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameBetween(String value1, String value2) {
            addCriterion("attr_name between", value1, value2, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameNotBetween(String value1, String value2) {
            addCriterion("attr_name not between", value1, value2, "attrName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameIsNull() {
            addCriterion("attr_name_cn_name is null");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameIsNotNull() {
            addCriterion("attr_name_cn_name is not null");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameEqualTo(String value) {
            addCriterion("attr_name_cn_name =", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameNotEqualTo(String value) {
            addCriterion("attr_name_cn_name <>", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameGreaterThan(String value) {
            addCriterion("attr_name_cn_name >", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameGreaterThanOrEqualTo(String value) {
            addCriterion("attr_name_cn_name >=", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameLessThan(String value) {
            addCriterion("attr_name_cn_name <", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameLessThanOrEqualTo(String value) {
            addCriterion("attr_name_cn_name <=", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameLike(String value) {
            addCriterion("attr_name_cn_name like", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameNotLike(String value) {
            addCriterion("attr_name_cn_name not like", value, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameIn(List<String> values) {
            addCriterion("attr_name_cn_name in", values, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameNotIn(List<String> values) {
            addCriterion("attr_name_cn_name not in", values, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameBetween(String value1, String value2) {
            addCriterion("attr_name_cn_name between", value1, value2, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrNameCnNameNotBetween(String value1, String value2) {
            addCriterion("attr_name_cn_name not between", value1, value2, "attrNameCnName");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesIsNull() {
            addCriterion("attr_value_cn_names is null");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesIsNotNull() {
            addCriterion("attr_value_cn_names is not null");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesEqualTo(String value) {
            addCriterion("attr_value_cn_names =", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesNotEqualTo(String value) {
            addCriterion("attr_value_cn_names <>", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesGreaterThan(String value) {
            addCriterion("attr_value_cn_names >", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesGreaterThanOrEqualTo(String value) {
            addCriterion("attr_value_cn_names >=", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesLessThan(String value) {
            addCriterion("attr_value_cn_names <", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesLessThanOrEqualTo(String value) {
            addCriterion("attr_value_cn_names <=", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesLike(String value) {
            addCriterion("attr_value_cn_names like", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesNotLike(String value) {
            addCriterion("attr_value_cn_names not like", value, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesIn(List<String> values) {
            addCriterion("attr_value_cn_names in", values, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesNotIn(List<String> values) {
            addCriterion("attr_value_cn_names not in", values, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesBetween(String value1, String value2) {
            addCriterion("attr_value_cn_names between", value1, value2, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrValueCnNamesNotBetween(String value1, String value2) {
            addCriterion("attr_value_cn_names not between", value1, value2, "attrValueCnNames");
            return (Criteria) this;
        }

        public Criteria andAttrJsonIsNull() {
            addCriterion("attr_json is null");
            return (Criteria) this;
        }

        public Criteria andAttrJsonIsNotNull() {
            addCriterion("attr_json is not null");
            return (Criteria) this;
        }

        public Criteria andAttrJsonEqualTo(String value) {
            addCriterion("attr_json =", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonNotEqualTo(String value) {
            addCriterion("attr_json <>", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonGreaterThan(String value) {
            addCriterion("attr_json >", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonGreaterThanOrEqualTo(String value) {
            addCriterion("attr_json >=", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonLessThan(String value) {
            addCriterion("attr_json <", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonLessThanOrEqualTo(String value) {
            addCriterion("attr_json <=", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonLike(String value) {
            addCriterion("attr_json like", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonNotLike(String value) {
            addCriterion("attr_json not like", value, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonIn(List<String> values) {
            addCriterion("attr_json in", values, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonNotIn(List<String> values) {
            addCriterion("attr_json not in", values, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonBetween(String value1, String value2) {
            addCriterion("attr_json between", value1, value2, "attrJson");
            return (Criteria) this;
        }

        public Criteria andAttrJsonNotBetween(String value1, String value2) {
            addCriterion("attr_json not between", value1, value2, "attrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdIsNull() {
            addCriterion("children_attr_name_id is null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdIsNotNull() {
            addCriterion("children_attr_name_id is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdEqualTo(Long value) {
            addCriterion("children_attr_name_id =", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdNotEqualTo(Long value) {
            addCriterion("children_attr_name_id <>", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdGreaterThan(Long value) {
            addCriterion("children_attr_name_id >", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdGreaterThanOrEqualTo(Long value) {
            addCriterion("children_attr_name_id >=", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdLessThan(Long value) {
            addCriterion("children_attr_name_id <", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdLessThanOrEqualTo(Long value) {
            addCriterion("children_attr_name_id <=", value, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdIn(List<Long> values) {
            addCriterion("children_attr_name_id in", values, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdNotIn(List<Long> values) {
            addCriterion("children_attr_name_id not in", values, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdBetween(Long value1, Long value2) {
            addCriterion("children_attr_name_id between", value1, value2, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIdNotBetween(Long value1, Long value2) {
            addCriterion("children_attr_name_id not between", value1, value2, "childrenAttrNameId");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIsNull() {
            addCriterion("children_attr_name is null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIsNotNull() {
            addCriterion("children_attr_name is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameEqualTo(String value) {
            addCriterion("children_attr_name =", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameNotEqualTo(String value) {
            addCriterion("children_attr_name <>", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameGreaterThan(String value) {
            addCriterion("children_attr_name >", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameGreaterThanOrEqualTo(String value) {
            addCriterion("children_attr_name >=", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameLessThan(String value) {
            addCriterion("children_attr_name <", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameLessThanOrEqualTo(String value) {
            addCriterion("children_attr_name <=", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameLike(String value) {
            addCriterion("children_attr_name like", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameNotLike(String value) {
            addCriterion("children_attr_name not like", value, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameIn(List<String> values) {
            addCriterion("children_attr_name in", values, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameNotIn(List<String> values) {
            addCriterion("children_attr_name not in", values, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameBetween(String value1, String value2) {
            addCriterion("children_attr_name between", value1, value2, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameNotBetween(String value1, String value2) {
            addCriterion("children_attr_name not between", value1, value2, "childrenAttrName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameIsNull() {
            addCriterion("children_attr_name_cn_name is null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameIsNotNull() {
            addCriterion("children_attr_name_cn_name is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameEqualTo(String value) {
            addCriterion("children_attr_name_cn_name =", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameNotEqualTo(String value) {
            addCriterion("children_attr_name_cn_name <>", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameGreaterThan(String value) {
            addCriterion("children_attr_name_cn_name >", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameGreaterThanOrEqualTo(String value) {
            addCriterion("children_attr_name_cn_name >=", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameLessThan(String value) {
            addCriterion("children_attr_name_cn_name <", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameLessThanOrEqualTo(String value) {
            addCriterion("children_attr_name_cn_name <=", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameLike(String value) {
            addCriterion("children_attr_name_cn_name like", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameNotLike(String value) {
            addCriterion("children_attr_name_cn_name not like", value, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameIn(List<String> values) {
            addCriterion("children_attr_name_cn_name in", values, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameNotIn(List<String> values) {
            addCriterion("children_attr_name_cn_name not in", values, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameBetween(String value1, String value2) {
            addCriterion("children_attr_name_cn_name between", value1, value2, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrNameCnNameNotBetween(String value1, String value2) {
            addCriterion("children_attr_name_cn_name not between", value1, value2, "childrenAttrNameCnName");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonIsNull() {
            addCriterion("children_attr_json is null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonIsNotNull() {
            addCriterion("children_attr_json is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonEqualTo(String value) {
            addCriterion("children_attr_json =", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonNotEqualTo(String value) {
            addCriterion("children_attr_json <>", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonGreaterThan(String value) {
            addCriterion("children_attr_json >", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonGreaterThanOrEqualTo(String value) {
            addCriterion("children_attr_json >=", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonLessThan(String value) {
            addCriterion("children_attr_json <", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonLessThanOrEqualTo(String value) {
            addCriterion("children_attr_json <=", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonLike(String value) {
            addCriterion("children_attr_json like", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonNotLike(String value) {
            addCriterion("children_attr_json not like", value, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonIn(List<String> values) {
            addCriterion("children_attr_json in", values, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonNotIn(List<String> values) {
            addCriterion("children_attr_json not in", values, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonBetween(String value1, String value2) {
            addCriterion("children_attr_json between", value1, value2, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andChildrenAttrJsonNotBetween(String value1, String value2) {
            addCriterion("children_attr_json not between", value1, value2, "childrenAttrJson");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("`enable` is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("`enable` is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Boolean value) {
            addCriterion("`enable` =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Boolean value) {
            addCriterion("`enable` <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Boolean value) {
            addCriterion("`enable` >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`enable` >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Boolean value) {
            addCriterion("`enable` <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("`enable` <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Boolean> values) {
            addCriterion("`enable` in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Boolean> values) {
            addCriterion("`enable` not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeIsNull() {
            addCriterion("started_last_time is null");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeIsNotNull() {
            addCriterion("started_last_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeEqualTo(Timestamp value) {
            addCriterion("started_last_time =", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeNotEqualTo(Timestamp value) {
            addCriterion("started_last_time <>", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeGreaterThan(Timestamp value) {
            addCriterion("started_last_time >", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("started_last_time >=", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeLessThan(Timestamp value) {
            addCriterion("started_last_time <", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("started_last_time <=", value, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeIn(List<Timestamp> values) {
            addCriterion("started_last_time in", values, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeNotIn(List<Timestamp> values) {
            addCriterion("started_last_time not in", values, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("started_last_time between", value1, value2, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andStartedLastTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("started_last_time not between", value1, value2, "startedLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeIsNull() {
            addCriterion("disabled_last_time is null");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeIsNotNull() {
            addCriterion("disabled_last_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeEqualTo(Timestamp value) {
            addCriterion("disabled_last_time =", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeNotEqualTo(Timestamp value) {
            addCriterion("disabled_last_time <>", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeGreaterThan(Timestamp value) {
            addCriterion("disabled_last_time >", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("disabled_last_time >=", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeLessThan(Timestamp value) {
            addCriterion("disabled_last_time <", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("disabled_last_time <=", value, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeIn(List<Timestamp> values) {
            addCriterion("disabled_last_time in", values, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeNotIn(List<Timestamp> values) {
            addCriterion("disabled_last_time not in", values, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("disabled_last_time between", value1, value2, "disabledLastTime");
            return (Criteria) this;
        }

        public Criteria andDisabledLastTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("disabled_last_time not between", value1, value2, "disabledLastTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}