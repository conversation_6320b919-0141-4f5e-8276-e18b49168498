package com.estone.erp.publish.smt.enums;

public enum AliexpressStoreCodeChannelEnum {
    FAULSE(0, "否/定向渠道发放"),
    TRUE(1, "是/常规展示");

    private int code;

    private String name;

    AliexpressStoreCodeChannelEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressStoreCodeChannelEnum build(int code) {
        AliexpressStoreCodeChannelEnum[] values = values();
        for (AliexpressStoreCodeChannelEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressStoreCodeChannelEnum[] values = values();
        for (AliexpressStoreCodeChannelEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
