package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtProductAptitudeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtProductAptitudeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andImageIsNull() {
            addCriterion("image is null");
            return (Criteria) this;
        }

        public Criteria andImageIsNotNull() {
            addCriterion("image is not null");
            return (Criteria) this;
        }

        public Criteria andImageEqualTo(String value) {
            addCriterion("image =", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageNotEqualTo(String value) {
            addCriterion("image <>", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageGreaterThan(String value) {
            addCriterion("image >", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageGreaterThanOrEqualTo(String value) {
            addCriterion("image >=", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageLessThan(String value) {
            addCriterion("image <", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageLessThanOrEqualTo(String value) {
            addCriterion("image <=", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageLike(String value) {
            addCriterion("image like", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageNotLike(String value) {
            addCriterion("image not like", value, "image");
            return (Criteria) this;
        }

        public Criteria andImageIn(List<String> values) {
            addCriterion("image in", values, "image");
            return (Criteria) this;
        }

        public Criteria andImageNotIn(List<String> values) {
            addCriterion("image not in", values, "image");
            return (Criteria) this;
        }

        public Criteria andImageBetween(String value1, String value2) {
            addCriterion("image between", value1, value2, "image");
            return (Criteria) this;
        }

        public Criteria andImageNotBetween(String value1, String value2) {
            addCriterion("image not between", value1, value2, "image");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIsNull() {
            addCriterion("issue_type is null");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIsNotNull() {
            addCriterion("issue_type is not null");
            return (Criteria) this;
        }

        public Criteria andIssueTypeEqualTo(String value) {
            addCriterion("issue_type =", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotEqualTo(String value) {
            addCriterion("issue_type <>", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeGreaterThan(String value) {
            addCriterion("issue_type >", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeGreaterThanOrEqualTo(String value) {
            addCriterion("issue_type >=", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLessThan(String value) {
            addCriterion("issue_type <", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLessThanOrEqualTo(String value) {
            addCriterion("issue_type <=", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLike(String value) {
            addCriterion("issue_type like", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotLike(String value) {
            addCriterion("issue_type not like", value, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeIn(List<String> values) {
            addCriterion("issue_type in", values, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeLikeIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "issue_type like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotIn(List<String> values) {
            addCriterion("issue_type not in", values, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeBetween(String value1, String value2) {
            addCriterion("issue_type between", value1, value2, "issueType");
            return (Criteria) this;
        }

        public Criteria andIssueTypeNotBetween(String value1, String value2) {
            addCriterion("issue_type not between", value1, value2, "issueType");
            return (Criteria) this;
        }

        public Criteria andRiskCountryIsNull() {
            addCriterion("risk_country is null");
            return (Criteria) this;
        }

        public Criteria andRiskCountryIsNotNull() {
            addCriterion("risk_country is not null");
            return (Criteria) this;
        }

        public Criteria andRiskCountryEqualTo(String value) {
            addCriterion("risk_country =", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryNotEqualTo(String value) {
            addCriterion("risk_country <>", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryGreaterThan(String value) {
            addCriterion("risk_country >", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryGreaterThanOrEqualTo(String value) {
            addCriterion("risk_country >=", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryLessThan(String value) {
            addCriterion("risk_country <", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryLessThanOrEqualTo(String value) {
            addCriterion("risk_country <=", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryLike(String value) {
            addCriterion("risk_country like", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryNotLike(String value) {
            addCriterion("risk_country not like", value, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryIn(List<String> values) {
            addCriterion("risk_country in", values, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryNotIn(List<String> values) {
            addCriterion("risk_country not in", values, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryBetween(String value1, String value2) {
            addCriterion("risk_country between", value1, value2, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andRiskCountryNotBetween(String value1, String value2) {
            addCriterion("risk_country not between", value1, value2, "riskCountry");
            return (Criteria) this;
        }

        public Criteria andImpactTypeIsNull() {
            addCriterion("impact_type is null");
            return (Criteria) this;
        }

        public Criteria andImpactTypeIsNotNull() {
            addCriterion("impact_type is not null");
            return (Criteria) this;
        }

        public Criteria andImpactTypeEqualTo(String value) {
            addCriterion("impact_type =", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeNotEqualTo(String value) {
            addCriterion("impact_type <>", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeGreaterThan(String value) {
            addCriterion("impact_type >", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeGreaterThanOrEqualTo(String value) {
            addCriterion("impact_type >=", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeLessThan(String value) {
            addCriterion("impact_type <", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeLessThanOrEqualTo(String value) {
            addCriterion("impact_type <=", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeLike(String value) {
            addCriterion("impact_type like", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeNotLike(String value) {
            addCriterion("impact_type not like", value, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeIn(List<String> values) {
            addCriterion("impact_type in", values, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeNotIn(List<String> values) {
            addCriterion("impact_type not in", values, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeBetween(String value1, String value2) {
            addCriterion("impact_type between", value1, value2, "impactType");
            return (Criteria) this;
        }

        public Criteria andImpactTypeNotBetween(String value1, String value2) {
            addCriterion("impact_type not between", value1, value2, "impactType");
            return (Criteria) this;
        }

        public Criteria andHandleStatusIsNull() {
            addCriterion("handle_status is null");
            return (Criteria) this;
        }

        public Criteria andHandleStatusIsNotNull() {
            addCriterion("handle_status is not null");
            return (Criteria) this;
        }

        public Criteria andHandleStatusEqualTo(String value) {
            addCriterion("handle_status =", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusNotEqualTo(String value) {
            addCriterion("handle_status <>", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusGreaterThan(String value) {
            addCriterion("handle_status >", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusGreaterThanOrEqualTo(String value) {
            addCriterion("handle_status >=", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusLessThan(String value) {
            addCriterion("handle_status <", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusLessThanOrEqualTo(String value) {
            addCriterion("handle_status <=", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusLike(String value) {
            addCriterion("handle_status like", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusNotLike(String value) {
            addCriterion("handle_status not like", value, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusIn(List<String> values) {
            addCriterion("handle_status in", values, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusNotIn(List<String> values) {
            addCriterion("handle_status not in", values, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusBetween(String value1, String value2) {
            addCriterion("handle_status between", value1, value2, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andHandleStatusNotBetween(String value1, String value2) {
            addCriterion("handle_status not between", value1, value2, "handleStatus");
            return (Criteria) this;
        }

        public Criteria andAuditResultListIsNull() {
            addCriterion("audit_result_list is null");
            return (Criteria) this;
        }

        public Criteria andAuditResultListIsNotNull() {
            addCriterion("audit_result_list is not null");
            return (Criteria) this;
        }

        public Criteria andAuditResultListEqualTo(String value) {
            addCriterion("audit_result_list =", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListNotEqualTo(String value) {
            addCriterion("audit_result_list <>", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListGreaterThan(String value) {
            addCriterion("audit_result_list >", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListGreaterThanOrEqualTo(String value) {
            addCriterion("audit_result_list >=", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListLessThan(String value) {
            addCriterion("audit_result_list <", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListLessThanOrEqualTo(String value) {
            addCriterion("audit_result_list <=", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListLike(String value) {
            addCriterion("audit_result_list like", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListNotLike(String value) {
            addCriterion("audit_result_list not like", value, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListIn(List<String> values) {
            addCriterion("audit_result_list in", values, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListNotIn(List<String> values) {
            addCriterion("audit_result_list not in", values, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListBetween(String value1, String value2) {
            addCriterion("audit_result_list between", value1, value2, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andAuditResultListNotBetween(String value1, String value2) {
            addCriterion("audit_result_list not between", value1, value2, "auditResultList");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsIsNull() {
            addCriterion("issue_details is null");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsIsNotNull() {
            addCriterion("issue_details is not null");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsEqualTo(String value) {
            addCriterion("issue_details =", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsNotEqualTo(String value) {
            addCriterion("issue_details <>", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsGreaterThan(String value) {
            addCriterion("issue_details >", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsGreaterThanOrEqualTo(String value) {
            addCriterion("issue_details >=", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsLessThan(String value) {
            addCriterion("issue_details <", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsLessThanOrEqualTo(String value) {
            addCriterion("issue_details <=", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsLike(String value) {
            addCriterion("issue_details like", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsNotLike(String value) {
            addCriterion("issue_details not like", value, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsIn(List<String> values) {
            addCriterion("issue_details in", values, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsNotIn(List<String> values) {
            addCriterion("issue_details not in", values, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsBetween(String value1, String value2) {
            addCriterion("issue_details between", value1, value2, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andIssueDetailsNotBetween(String value1, String value2) {
            addCriterion("issue_details not between", value1, value2, "issueDetails");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIsNull() {
            addCriterion("crawl_time is null");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIsNotNull() {
            addCriterion("crawl_time is not null");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeEqualTo(Timestamp value) {
            addCriterion("crawl_time =", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotEqualTo(Timestamp value) {
            addCriterion("crawl_time <>", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeGreaterThan(Timestamp value) {
            addCriterion("crawl_time >", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("crawl_time >=", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeLessThan(Timestamp value) {
            addCriterion("crawl_time <", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("crawl_time <=", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIn(List<Timestamp> values) {
            addCriterion("crawl_time in", values, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotIn(List<Timestamp> values) {
            addCriterion("crawl_time not in", values, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_time between", value1, value2, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_time not between", value1, value2, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeIsNull() {
            addCriterion("crawl_updated_time is null");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeIsNotNull() {
            addCriterion("crawl_updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("crawl_updated_time =", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("crawl_updated_time <>", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("crawl_updated_time >", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("crawl_updated_time >=", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeLessThan(Timestamp value) {
            addCriterion("crawl_updated_time <", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("crawl_updated_time <=", value, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("crawl_updated_time in", values, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("crawl_updated_time not in", values, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_updated_time between", value1, value2, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andCrawlUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_updated_time not between", value1, value2, "crawlUpdatedTime");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dIsNull() {
            addCriterion("account_order_num_7d is null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dIsNotNull() {
            addCriterion("account_order_num_7d is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dEqualTo(Integer value) {
            addCriterion("account_order_num_7d =", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dNotEqualTo(Integer value) {
            addCriterion("account_order_num_7d <>", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dGreaterThan(Integer value) {
            addCriterion("account_order_num_7d >", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_7d >=", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dLessThan(Integer value) {
            addCriterion("account_order_num_7d <", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dLessThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_7d <=", value, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dIn(List<Integer> values) {
            addCriterion("account_order_num_7d in", values, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dNotIn(List<Integer> values) {
            addCriterion("account_order_num_7d not in", values, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_7d between", value1, value2, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum7dNotBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_7d not between", value1, value2, "accountOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dIsNull() {
            addCriterion("account_order_num_14d is null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dIsNotNull() {
            addCriterion("account_order_num_14d is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dEqualTo(Integer value) {
            addCriterion("account_order_num_14d =", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dNotEqualTo(Integer value) {
            addCriterion("account_order_num_14d <>", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dGreaterThan(Integer value) {
            addCriterion("account_order_num_14d >", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_14d >=", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dLessThan(Integer value) {
            addCriterion("account_order_num_14d <", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dLessThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_14d <=", value, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dIn(List<Integer> values) {
            addCriterion("account_order_num_14d in", values, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dNotIn(List<Integer> values) {
            addCriterion("account_order_num_14d not in", values, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_14d between", value1, value2, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum14dNotBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_14d not between", value1, value2, "accountOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dIsNull() {
            addCriterion("account_order_num_30d is null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dIsNotNull() {
            addCriterion("account_order_num_30d is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dEqualTo(Integer value) {
            addCriterion("account_order_num_30d =", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dNotEqualTo(Integer value) {
            addCriterion("account_order_num_30d <>", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dGreaterThan(Integer value) {
            addCriterion("account_order_num_30d >", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_30d >=", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dLessThan(Integer value) {
            addCriterion("account_order_num_30d <", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dLessThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_30d <=", value, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dIn(List<Integer> values) {
            addCriterion("account_order_num_30d in", values, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dNotIn(List<Integer> values) {
            addCriterion("account_order_num_30d not in", values, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_30d between", value1, value2, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum30dNotBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_30d not between", value1, value2, "accountOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dIsNull() {
            addCriterion("account_order_num_60d is null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dIsNotNull() {
            addCriterion("account_order_num_60d is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dEqualTo(Integer value) {
            addCriterion("account_order_num_60d =", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dNotEqualTo(Integer value) {
            addCriterion("account_order_num_60d <>", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dGreaterThan(Integer value) {
            addCriterion("account_order_num_60d >", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_60d >=", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dLessThan(Integer value) {
            addCriterion("account_order_num_60d <", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dLessThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_60d <=", value, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dIn(List<Integer> values) {
            addCriterion("account_order_num_60d in", values, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dNotIn(List<Integer> values) {
            addCriterion("account_order_num_60d not in", values, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_60d between", value1, value2, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum60dNotBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_60d not between", value1, value2, "accountOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dIsNull() {
            addCriterion("account_order_num_90d is null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dIsNotNull() {
            addCriterion("account_order_num_90d is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dEqualTo(Integer value) {
            addCriterion("account_order_num_90d =", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dNotEqualTo(Integer value) {
            addCriterion("account_order_num_90d <>", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dGreaterThan(Integer value) {
            addCriterion("account_order_num_90d >", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_90d >=", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dLessThan(Integer value) {
            addCriterion("account_order_num_90d <", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dLessThanOrEqualTo(Integer value) {
            addCriterion("account_order_num_90d <=", value, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dIn(List<Integer> values) {
            addCriterion("account_order_num_90d in", values, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dNotIn(List<Integer> values) {
            addCriterion("account_order_num_90d not in", values, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_90d between", value1, value2, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andAccountOrderNum90dNotBetween(Integer value1, Integer value2) {
            addCriterion("account_order_num_90d not between", value1, value2, "accountOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dIsNull() {
            addCriterion("smt_plat_order_num_7d is null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dIsNotNull() {
            addCriterion("smt_plat_order_num_7d is not null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_7d =", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dNotEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_7d <>", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dGreaterThan(Integer value) {
            addCriterion("smt_plat_order_num_7d >", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_7d >=", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dLessThan(Integer value) {
            addCriterion("smt_plat_order_num_7d <", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dLessThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_7d <=", value, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_7d in", values, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dNotIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_7d not in", values, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_7d between", value1, value2, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum7dNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_7d not between", value1, value2, "smtPlatOrderNum7d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dIsNull() {
            addCriterion("smt_plat_order_num_14d is null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dIsNotNull() {
            addCriterion("smt_plat_order_num_14d is not null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_14d =", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dNotEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_14d <>", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dGreaterThan(Integer value) {
            addCriterion("smt_plat_order_num_14d >", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_14d >=", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dLessThan(Integer value) {
            addCriterion("smt_plat_order_num_14d <", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dLessThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_14d <=", value, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_14d in", values, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dNotIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_14d not in", values, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_14d between", value1, value2, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum14dNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_14d not between", value1, value2, "smtPlatOrderNum14d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dIsNull() {
            addCriterion("smt_plat_order_num_30d is null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dIsNotNull() {
            addCriterion("smt_plat_order_num_30d is not null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_30d =", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dNotEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_30d <>", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dGreaterThan(Integer value) {
            addCriterion("smt_plat_order_num_30d >", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_30d >=", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dLessThan(Integer value) {
            addCriterion("smt_plat_order_num_30d <", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dLessThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_30d <=", value, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_30d in", values, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dNotIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_30d not in", values, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_30d between", value1, value2, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum30dNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_30d not between", value1, value2, "smtPlatOrderNum30d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dIsNull() {
            addCriterion("smt_plat_order_num_60d is null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dIsNotNull() {
            addCriterion("smt_plat_order_num_60d is not null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_60d =", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dNotEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_60d <>", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dGreaterThan(Integer value) {
            addCriterion("smt_plat_order_num_60d >", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_60d >=", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dLessThan(Integer value) {
            addCriterion("smt_plat_order_num_60d <", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dLessThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_60d <=", value, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_60d in", values, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dNotIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_60d not in", values, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_60d between", value1, value2, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum60dNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_60d not between", value1, value2, "smtPlatOrderNum60d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dIsNull() {
            addCriterion("smt_plat_order_num_90d is null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dIsNotNull() {
            addCriterion("smt_plat_order_num_90d is not null");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_90d =", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dNotEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_90d <>", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dGreaterThan(Integer value) {
            addCriterion("smt_plat_order_num_90d >", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_90d >=", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dLessThan(Integer value) {
            addCriterion("smt_plat_order_num_90d <", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dLessThanOrEqualTo(Integer value) {
            addCriterion("smt_plat_order_num_90d <=", value, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_90d in", values, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dNotIn(List<Integer> values) {
            addCriterion("smt_plat_order_num_90d not in", values, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_90d between", value1, value2, "smtPlatOrderNum90d");
            return (Criteria) this;
        }

        public Criteria andSmtPlatOrderNum90dNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_plat_order_num_90d not between", value1, value2, "smtPlatOrderNum90d");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}