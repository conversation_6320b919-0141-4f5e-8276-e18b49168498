package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.system.product.response.QuerySpuByConditionVo;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressEarlyBirdActivityService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-14 12:09:21
 */
@RestController
@RequestMapping("aliexpressEarlyBirdActivity")
public class AliexpressEarlyBirdActivityController {
    @Resource
    private AliexpressEarlyBirdActivityService aliexpressEarlyBirdActivityService;

    @PostMapping
    public ApiResult<?> postAliexpressEarlyBirdActivity(@RequestBody(required = true) ApiRequestParam<String> requestParam) throws Exception {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressEarlyBirdActivity": // 查询列表
                    CQuery<AliexpressEarlyBirdActivityCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressEarlyBirdActivityCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliexpressEarlyBirdActivity> results = aliexpressEarlyBirdActivityService.search(cquery);
                    return results;
                case "addAliexpressEarlyBirdActivity": // 添加
                    AliexpressEarlyBirdActivity aliexpressEarlyBirdActivity = requestParam.getArgsValue(new TypeReference<AliexpressEarlyBirdActivity>() {});
                    aliexpressEarlyBirdActivityService.insert(aliexpressEarlyBirdActivity);
                    return ApiResult.newSuccess(aliexpressEarlyBirdActivity);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliexpressEarlyBirdActivity(@PathVariable(value = "id", required = true) Long id) {
        AliexpressEarlyBirdActivity aliexpressEarlyBirdActivity = aliexpressEarlyBirdActivityService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliexpressEarlyBirdActivity);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliexpressEarlyBirdActivity(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliexpressEarlyBirdActivity": // 单个修改
                    AliexpressEarlyBirdActivity aliexpressEarlyBirdActivity = requestParam.getArgsValue(new TypeReference<AliexpressEarlyBirdActivity>() {});
                    aliexpressEarlyBirdActivityService.updateByPrimaryKeySelective(aliexpressEarlyBirdActivity);
                    return ApiResult.newSuccess(aliexpressEarlyBirdActivity);
                }
        }
        return ApiResult.newSuccess();
    }


    @PostMapping(value = "/download")
    public ApiResult<?> downloadAliexpressEarlyBirdActivity( @RequestBody ApiRequestParam<String> requestParam) {

        CQuery<AliexpressEarlyBirdActivityCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressEarlyBirdActivityCriteria>>() {});
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        ResponseJson responseJson = aliexpressEarlyBirdActivityService.downloadEarlyBirdActivities(cquery);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/join")
    public ApiResult<?> joinAliexpressEarlyBirdActivity( @RequestBody ApiRequestParam<String> requestParam) {

        CQuery<List<AliexpressEarlyBirdActivity>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<AliexpressEarlyBirdActivity>>>() {});
        ResponseJson responseJson = aliexpressEarlyBirdActivityService.joinAliexpressEarlyBirdActivity(cquery);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据早鸟活动配置的id的数据查询 产品系统根据条件获取spu中最重sku数据
     * @param marketingConfigId
     * @return
     */
    @GetMapping(value = "/queryHeaviestSkuByMarketingConfigId/{marketingConfigId}")
    public ApiResult<List<QuerySpuByConditionVo>> queryHeaviestSkuByMarketingConfigId(@PathVariable(value = "marketingConfigId", required = true) Integer marketingConfigId) {
        List<QuerySpuByConditionVo> conditionVos=aliexpressEarlyBirdActivityService.queryHeaviestSkuByMarketingConfigId(marketingConfigId);
        return ApiResult.newSuccess(conditionVos);
    }
}


