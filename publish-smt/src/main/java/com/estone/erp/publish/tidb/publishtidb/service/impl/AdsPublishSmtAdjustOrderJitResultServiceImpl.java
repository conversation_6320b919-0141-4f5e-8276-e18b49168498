package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsPublishSmtAdjustOrderJitResultMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustOrderJitResult;
import com.estone.erp.publish.tidb.publishtidb.service.IAdsPublishSmtAdjustOrderJitResultService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class AdsPublishSmtAdjustOrderJitResultServiceImpl extends ServiceImpl<AdsPublishSmtAdjustOrderJitResultMapper, AdsPublishSmtAdjustOrderJitResult> implements IAdsPublishSmtAdjustOrderJitResultService {

    @Resource
    AdsPublishSmtAdjustOrderJitResultMapper adsPublishSmtAdjustOrderJitResultMapper;

    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AdsPublishSmtAdjustOrderJitResult> wrapper){
        Assert.notNull(wrapper, "wrapper is null!");
        List<Map<Object, Object>> mapList = adsPublishSmtAdjustOrderJitResultMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }
}
