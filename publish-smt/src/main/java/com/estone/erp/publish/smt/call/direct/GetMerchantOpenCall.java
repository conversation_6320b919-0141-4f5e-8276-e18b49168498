package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 店铺信息
 * <AUTHOR>
 * @description:
 * @date 2019/11/616:38
 */
@Slf4j
public class GetMerchantOpenCall {

    public ResponseJson getMerchant(SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (saleAccountByAccountNumber == null) {
            responseJson.setMessage("请求参数为空！");
            return responseJson;
        }
        String callRspStr = null;

        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.merchant.product.post.limit");
            IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            callRspStr = response.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        if(StringUtils.isBlank(callRspStr)) {
            responseJson.setMessage("请求无结果返回，请联系IT人员");
            return responseJson;
        }
        return checkErrorMessage(callRspStr);
    }

    /**
     *
     * 校验请求返回错误信息
     * @param callRspStr
     * @return
     */
    public ResponseJson checkErrorMessage(String callRspStr) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        try {
            JSONObject callRspJson = JSONObject.parseObject(callRspStr);
            if (callRspJson != null) {
                JSONObject response = callRspJson.getJSONObject("aliexpress_merchant_product_post_limit_response");
                if (response != null) {
                    JSONObject result = response.getJSONObject("result");
                    if (result != null) {
                        JSONObject sellerProductLimitData = result.getJSONObject("seller_product_limit_data");
                        if (sellerProductLimitData != null) {
                            responseJson.setStatus(StatusCode.SUCCESS);
                            responseJson.getBody().put("key", sellerProductLimitData);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage(e.getMessage() + callRspStr);
        }

        // 如果状态是FAIL且没有设置message，则返回整个响应字符串
        if (StringUtils.equalsIgnoreCase(StatusCode.FAIL, responseJson.getStatus())
                && StringUtils.isBlank(responseJson.getMessage())) {
            responseJson.setMessage(callRspStr);
        }

        return responseJson;
    }

}
