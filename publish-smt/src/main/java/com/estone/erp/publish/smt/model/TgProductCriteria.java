package com.estone.erp.publish.smt.model;

import lombok.Data;

/**
 * <AUTHOR> aliexpress_product
 * 2019-10-24 11:42:03
 */
@Data
public class TgProductCriteria {
    private Long productId;
    private Long leafCategoryId;
    private String productStatusType;//状态 多个英文逗号拼接
    private String createDateStart;
    private String createDateEnd;
    private String modifiedDateStart;
    private String modifiedDateEnd;

    private String aliexpressAccountNumber;//账号 多个英文逗号拼接 页面同步条件

    /**
     * 是否队列模式
     */
    private Boolean isQueue;
}