package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceConfirmDto;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceItemPoolQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceItemPoolVO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtAdjustPriceItemPool;
import com.estone.erp.publish.tidb.publishtidb.service.SmtAdjustPriceItemPoolService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * smt链接管理调价记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024年12月1日17:32:58
 */
@RestController
@RequestMapping("/smt-adjust-price-item-pool")
public class SmtAdjustPriceItemPoolController {

    @Resource
    private SmtAdjustPriceItemPoolService smtAdjustPriceItemPoolService;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private ExcelSend excelSend;

    //权限控制
    public void isSmtAuth(SmtAdjustPriceItemPoolQueryDto query) {
        if (query == null) {
            query = new SmtAdjustPriceItemPoolQueryDto();
        }
        List<String> accountNumbers = query.getAccountNumbers();
        List<String> managerIds = null;
        List<String> leaderIds = null;
        List<String> saleIds = null;
        List<Integer> groupIds = null;
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setAccountNumbers(authAccountNumbers);
    }

    /**
     * 分页查询
     */
    @PostMapping("queryPage")
    public CQueryResult<SmtAdjustPriceItemPoolVO> queryPage(@RequestBody CQuery<SmtAdjustPriceItemPoolQueryDto> query) {
        Asserts.isTrue(query != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            isSmtAuth(query.getSearch());
            return smtAdjustPriceItemPoolService.queryPage(query);
        } catch (Exception e) {
            return CQueryResult.failResult(e.getMessage());
        }
    }


    /**
     * 根据id查询详情
     */
    @GetMapping("getDetail/{id}")
    public ApiResult<SmtAdjustPriceItemPool> getDetailById(@PathVariable String id) {
        SmtAdjustPriceItemPool detail = smtAdjustPriceItemPoolService.getById(id);
        return ApiResult.newSuccess(detail);
    }


    /**
     * 确认调价
     */
    @PostMapping("confirm")
    public ApiResult<String> confirm(@RequestBody @Valid SmtAdjustPriceConfirmDto confirmDO) {
        return smtAdjustPriceItemPoolService.confirm(confirmDO);
    }


    /**
     * 导出
     */
    @PostMapping("export")
    public ApiResult<?> export(@RequestBody CQuery<SmtAdjustPriceItemPoolQueryDto> cquery, HttpServletResponse response) throws IOException {
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        SmtAdjustPriceItemPoolQueryDto query = cquery.getSearch();
        try {
            isSmtAuth(query);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(query.getAccountNumbers(), null, null, null, SaleChannel.CHANNEL_SMT, true);
        query.setAccountNumbers(currentUserPermission);
        int maxRow = 1000000;
        int pageNo = 1;
        int pageSize = 1;
        IPage<SmtAdjustPriceItemPool> itemPoolIPage = smtAdjustPriceItemPoolService.pageList(query, pageNo, pageSize);
        long total = itemPoolIPage.getTotal();
        if (total == 0) {
            return ApiResult.newError("导出数据为空");
        }
        if (total > maxRow) {
            return ApiResult.newError("导出数据超过" + maxRow + "条，请缩小查询条件");
        }
        excelSend.downloadAdjustPriceItem(ExcelTypeEnum.downloadAdjustPriceItem.getCode(), query);
        return ApiResult.newSuccess("请求成功，稍后前往导出日志查看！");
    }
}
