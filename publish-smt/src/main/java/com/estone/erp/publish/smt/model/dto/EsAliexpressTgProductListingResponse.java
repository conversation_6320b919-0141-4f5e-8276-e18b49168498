package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressTgProductListing;
import com.estone.erp.publish.smt.model.AliexpressEsTgExtend;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: ${description}
 * @Author: lc
 * @Date: 2023年4月6日15:50:20
 * @Version: 1.0.0
 */
@Data
public class EsAliexpressTgProductListingResponse {

    /**
     * es List
     */
    private Page<EsAliexpressTgProductListing> esTgProductListingPage ;

    private Map<String, AliexpressEsTgExtend> extendMap = new HashMap<>();


    /**
     * 账号对应销售
     */
    private Map<String,List<String>> accountSaleManMap;

    private List<EsAliexpressTgProductListing> esAliexpressTgProductListing;



}
