package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressConfigInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressConfigInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("config_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("config_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(Integer value) {
            addCriterion("config_id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(Integer value) {
            addCriterion("config_id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(Integer value) {
            addCriterion("config_id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("config_id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(Integer value) {
            addCriterion("config_id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(Integer value) {
            addCriterion("config_id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<Integer> values) {
            addCriterion("config_id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<Integer> values) {
            addCriterion("config_id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(Integer value1, Integer value2) {
            addCriterion("config_id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(Integer value1, Integer value2) {
            addCriterion("config_id not between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andFromWeightIsNull() {
            addCriterion("from_weight is null");
            return (Criteria) this;
        }

        public Criteria andFromWeightIsNotNull() {
            addCriterion("from_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFromWeightEqualTo(Double value) {
            addCriterion("from_weight =", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightNotEqualTo(Double value) {
            addCriterion("from_weight <>", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightGreaterThan(Double value) {
            addCriterion("from_weight >", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("from_weight >=", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightLessThan(Double value) {
            addCriterion("from_weight <", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightLessThanOrEqualTo(Double value) {
            addCriterion("from_weight <=", value, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightIn(List<Double> values) {
            addCriterion("from_weight in", values, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightNotIn(List<Double> values) {
            addCriterion("from_weight not in", values, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightBetween(Double value1, Double value2) {
            addCriterion("from_weight between", value1, value2, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andFromWeightNotBetween(Double value1, Double value2) {
            addCriterion("from_weight not between", value1, value2, "fromWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightIsNull() {
            addCriterion("to_weight is null");
            return (Criteria) this;
        }

        public Criteria andToWeightIsNotNull() {
            addCriterion("to_weight is not null");
            return (Criteria) this;
        }

        public Criteria andToWeightEqualTo(Double value) {
            addCriterion("to_weight =", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightNotEqualTo(Double value) {
            addCriterion("to_weight <>", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightGreaterThan(Double value) {
            addCriterion("to_weight >", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("to_weight >=", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightLessThan(Double value) {
            addCriterion("to_weight <", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightLessThanOrEqualTo(Double value) {
            addCriterion("to_weight <=", value, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightIn(List<Double> values) {
            addCriterion("to_weight in", values, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightNotIn(List<Double> values) {
            addCriterion("to_weight not in", values, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightBetween(Double value1, Double value2) {
            addCriterion("to_weight between", value1, value2, "toWeight");
            return (Criteria) this;
        }

        public Criteria andToWeightNotBetween(Double value1, Double value2) {
            addCriterion("to_weight not between", value1, value2, "toWeight");
            return (Criteria) this;
        }

        public Criteria andTagCodesIsNull() {
            addCriterion("tag_codes is null");
            return (Criteria) this;
        }

        public Criteria andTagCodesIsNotNull() {
            addCriterion("tag_codes is not null");
            return (Criteria) this;
        }

        public Criteria andTagCodesEqualTo(String value) {
            addCriterion("tag_codes =", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotEqualTo(String value) {
            addCriterion("tag_codes <>", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesGreaterThan(String value) {
            addCriterion("tag_codes >", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesGreaterThanOrEqualTo(String value) {
            addCriterion("tag_codes >=", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLessThan(String value) {
            addCriterion("tag_codes <", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLessThanOrEqualTo(String value) {
            addCriterion("tag_codes <=", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesLike(String value) {
            addCriterion("tag_codes like", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotLike(String value) {
            addCriterion("tag_codes not like", value, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesIn(List<String> values) {
            addCriterion("tag_codes in", values, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotIn(List<String> values) {
            addCriterion("tag_codes not in", values, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesBetween(String value1, String value2) {
            addCriterion("tag_codes between", value1, value2, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andTagCodesNotBetween(String value1, String value2) {
            addCriterion("tag_codes not between", value1, value2, "tagCodes");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNull() {
            addCriterion("freight_template_id is null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNotNull() {
            addCriterion("freight_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdEqualTo(Long value) {
            addCriterion("freight_template_id =", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotEqualTo(Long value) {
            addCriterion("freight_template_id <>", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThan(Long value) {
            addCriterion("freight_template_id >", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("freight_template_id >=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThan(Long value) {
            addCriterion("freight_template_id <", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("freight_template_id <=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIn(List<Long> values) {
            addCriterion("freight_template_id in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotIn(List<Long> values) {
            addCriterion("freight_template_id not in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdBetween(Long value1, Long value2) {
            addCriterion("freight_template_id between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("freight_template_id not between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNull() {
            addCriterion("promise_template_id is null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNotNull() {
            addCriterion("promise_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdEqualTo(Long value) {
            addCriterion("promise_template_id =", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotEqualTo(Long value) {
            addCriterion("promise_template_id <>", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThan(Long value) {
            addCriterion("promise_template_id >", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("promise_template_id >=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThan(Long value) {
            addCriterion("promise_template_id <", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("promise_template_id <=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIn(List<Long> values) {
            addCriterion("promise_template_id in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotIn(List<Long> values) {
            addCriterion("promise_template_id not in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdBetween(Long value1, Long value2) {
            addCriterion("promise_template_id between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("promise_template_id not between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andGrossProfitIsNull() {
            addCriterion("gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andGrossProfitIsNotNull() {
            addCriterion("gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andGrossProfitEqualTo(Double value) {
            addCriterion("gross_profit =", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitNotEqualTo(Double value) {
            addCriterion("gross_profit <>", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitGreaterThan(Double value) {
            addCriterion("gross_profit >", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitGreaterThanOrEqualTo(Double value) {
            addCriterion("gross_profit >=", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitLessThan(Double value) {
            addCriterion("gross_profit <", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitLessThanOrEqualTo(Double value) {
            addCriterion("gross_profit <=", value, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitIn(List<Double> values) {
            addCriterion("gross_profit in", values, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitNotIn(List<Double> values) {
            addCriterion("gross_profit not in", values, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitBetween(Double value1, Double value2) {
            addCriterion("gross_profit between", value1, value2, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andGrossProfitNotBetween(Double value1, Double value2) {
            addCriterion("gross_profit not between", value1, value2, "grossProfit");
            return (Criteria) this;
        }

        public Criteria andShippingMethodIsNull() {
            addCriterion("shipping_method is null");
            return (Criteria) this;
        }

        public Criteria andShippingMethodIsNotNull() {
            addCriterion("shipping_method is not null");
            return (Criteria) this;
        }

        public Criteria andShippingMethodEqualTo(String value) {
            addCriterion("shipping_method =", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodNotEqualTo(String value) {
            addCriterion("shipping_method <>", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodGreaterThan(String value) {
            addCriterion("shipping_method >", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_method >=", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodLessThan(String value) {
            addCriterion("shipping_method <", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodLessThanOrEqualTo(String value) {
            addCriterion("shipping_method <=", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodLike(String value) {
            addCriterion("shipping_method like", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodNotLike(String value) {
            addCriterion("shipping_method not like", value, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodIn(List<String> values) {
            addCriterion("shipping_method in", values, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodNotIn(List<String> values) {
            addCriterion("shipping_method not in", values, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodBetween(String value1, String value2) {
            addCriterion("shipping_method between", value1, value2, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andShippingMethodNotBetween(String value1, String value2) {
            addCriterion("shipping_method not between", value1, value2, "shippingMethod");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIsNull() {
            addCriterion("country_code is null");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIsNotNull() {
            addCriterion("country_code is not null");
            return (Criteria) this;
        }

        public Criteria andCountryCodeEqualTo(String value) {
            addCriterion("country_code =", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotEqualTo(String value) {
            addCriterion("country_code <>", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeGreaterThan(String value) {
            addCriterion("country_code >", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("country_code >=", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLessThan(String value) {
            addCriterion("country_code <", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLessThanOrEqualTo(String value) {
            addCriterion("country_code <=", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLike(String value) {
            addCriterion("country_code like", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotLike(String value) {
            addCriterion("country_code not like", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIn(List<String> values) {
            addCriterion("country_code in", values, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotIn(List<String> values) {
            addCriterion("country_code not in", values, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeBetween(String value1, String value2) {
            addCriterion("country_code between", value1, value2, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotBetween(String value1, String value2) {
            addCriterion("country_code not between", value1, value2, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIsNull() {
            addCriterion("special_tag_code is null");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIsNotNull() {
            addCriterion("special_tag_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeEqualTo(String value) {
            addCriterion("special_tag_code =", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotEqualTo(String value) {
            addCriterion("special_tag_code <>", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeGreaterThan(String value) {
            addCriterion("special_tag_code >", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeGreaterThanOrEqualTo(String value) {
            addCriterion("special_tag_code >=", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLessThan(String value) {
            addCriterion("special_tag_code <", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLessThanOrEqualTo(String value) {
            addCriterion("special_tag_code <=", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLike(String value) {
            addCriterion("special_tag_code like", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotLike(String value) {
            addCriterion("special_tag_code not like", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIn(List<String> values) {
            addCriterion("special_tag_code in", values, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotIn(List<String> values) {
            addCriterion("special_tag_code not in", values, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeBetween(String value1, String value2) {
            addCriterion("special_tag_code between", value1, value2, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotBetween(String value1, String value2) {
            addCriterion("special_tag_code not between", value1, value2, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andFromPriceIsNull() {
            addCriterion("from_price is null");
            return (Criteria) this;
        }

        public Criteria andFromPriceIsNotNull() {
            addCriterion("from_price is not null");
            return (Criteria) this;
        }

        public Criteria andFromPriceEqualTo(Double value) {
            addCriterion("from_price =", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotEqualTo(Double value) {
            addCriterion("from_price <>", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceGreaterThan(Double value) {
            addCriterion("from_price >", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("from_price >=", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceLessThan(Double value) {
            addCriterion("from_price <", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceLessThanOrEqualTo(Double value) {
            addCriterion("from_price <=", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceIn(List<Double> values) {
            addCriterion("from_price in", values, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotIn(List<Double> values) {
            addCriterion("from_price not in", values, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceBetween(Double value1, Double value2) {
            addCriterion("from_price between", value1, value2, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotBetween(Double value1, Double value2) {
            addCriterion("from_price not between", value1, value2, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceIsNull() {
            addCriterion("to_price is null");
            return (Criteria) this;
        }

        public Criteria andToPriceIsNotNull() {
            addCriterion("to_price is not null");
            return (Criteria) this;
        }

        public Criteria andToPriceEqualTo(Double value) {
            addCriterion("to_price =", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotEqualTo(Double value) {
            addCriterion("to_price <>", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceGreaterThan(Double value) {
            addCriterion("to_price >", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("to_price >=", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceLessThan(Double value) {
            addCriterion("to_price <", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceLessThanOrEqualTo(Double value) {
            addCriterion("to_price <=", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceIn(List<Double> values) {
            addCriterion("to_price in", values, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotIn(List<Double> values) {
            addCriterion("to_price not in", values, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceBetween(Double value1, Double value2) {
            addCriterion("to_price between", value1, value2, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotBetween(Double value1, Double value2) {
            addCriterion("to_price not between", value1, value2, "toPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNull() {
            addCriterion("discount_rate is null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNotNull() {
            addCriterion("discount_rate is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateEqualTo(Double value) {
            addCriterion("discount_rate =", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotEqualTo(Double value) {
            addCriterion("discount_rate <>", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThan(Double value) {
            addCriterion("discount_rate >", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThanOrEqualTo(Double value) {
            addCriterion("discount_rate >=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThan(Double value) {
            addCriterion("discount_rate <", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThanOrEqualTo(Double value) {
            addCriterion("discount_rate <=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIn(List<Double> values) {
            addCriterion("discount_rate in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotIn(List<Double> values) {
            addCriterion("discount_rate not in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateBetween(Double value1, Double value2) {
            addCriterion("discount_rate between", value1, value2, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotBetween(Double value1, Double value2) {
            addCriterion("discount_rate not between", value1, value2, "discountRate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}