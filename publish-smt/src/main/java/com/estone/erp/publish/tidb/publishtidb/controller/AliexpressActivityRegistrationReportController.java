package com.estone.erp.publish.tidb.publishtidb.controller;


import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.enums.DownActivityRegistrationReportEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReport;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReportCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.IAliexpressActivityRegistrationReportService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 平台活动报表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@RestController
@RequestMapping("aliexpressActivityRegistrationReport")
public class AliexpressActivityRegistrationReportController {

    @Resource
    private IAliexpressActivityRegistrationReportService aliexpressActivityRegistrationReportService;

    @PostMapping
    public ApiResult<?> postAliexpressActivityRegistrationReport(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressActivityRegistrationReport":
                    CQuery<AliexpressActivityRegistrationReportCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressActivityRegistrationReportCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    isAuth(cquery);
                    CQueryResult<AliexpressActivityRegistrationReport> results = aliexpressActivityRegistrationReportService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    private void isAuth(CQuery<AliexpressActivityRegistrationReportCriteria> cquery) {
        // 判断是否超管
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        boolean issupportDepartmentResult =NewUsermgtUtils.isDataSupportDepartment();
        boolean isSuperAdmin = superAdminOrEquivalent.getResult();
        String user="admin";//超管
        if(!isSuperAdmin && !issupportDepartmentResult){//如果不是超管
            user = WebUtils.getUserName();
        }
        cquery.getSearch().setUser(user);
    }

    /**
     * 获取下载字段列表
     * @return
     */
    @GetMapping(value = "/getDownColumnList")
    public Object getDownFieldList(){
        DownActivityRegistrationReportEnum[] fieldEnumArray = DownActivityRegistrationReportEnum.values();
        List<Map<String, Object>> fieldEnumList = new ArrayList<>();
        for (DownActivityRegistrationReportEnum downEnum : fieldEnumArray) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("name", downEnum.getName());
            enumMap.put("excelValue", downEnum.getExcelValue());
            fieldEnumList.add(enumMap);
        }
        return fieldEnumList;
    }


    /**
     * 导出
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/download")
    public ApiResult<?> download( @RequestBody ApiRequestParam<String> requestParam) {
        CQuery<AliexpressActivityRegistrationReportCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressActivityRegistrationReportCriteria>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        isAuth(cquery);
        ResponseJson responseJson=aliexpressActivityRegistrationReportService.download(cquery);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }



}
