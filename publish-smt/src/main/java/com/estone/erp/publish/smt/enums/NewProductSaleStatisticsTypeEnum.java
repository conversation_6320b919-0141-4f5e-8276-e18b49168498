package com.estone.erp.publish.smt.enums;

public enum NewProductSaleStatisticsTypeEnum {

    all(0, "全部"),
    sale(1, "销售"),
    saleLeader(2, "销售组长");

    private int code;

    private String name;

    private NewProductSaleStatisticsTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static NewProductSaleStatisticsTypeEnum build(int code) {
        NewProductSaleStatisticsTypeEnum[] values = values();
        for (NewProductSaleStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        NewProductSaleStatisticsTypeEnum[] values = values();
        for (NewProductSaleStatisticsTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
