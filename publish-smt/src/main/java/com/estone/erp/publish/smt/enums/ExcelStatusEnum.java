package com.estone.erp.publish.smt.enums;

public enum ExcelStatusEnum {

    WAIT(1, "排队中"),

    DOWNLOADING(2, "下载中"),

    COMPLETE(3, "完成"),
    ;

    private int code;

    private String name;

    private ExcelStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ExcelStatusEnum build(int code) {
        ExcelStatusEnum[] values = values();
        for (ExcelStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        ExcelStatusEnum[] values = values();
        for (ExcelStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
