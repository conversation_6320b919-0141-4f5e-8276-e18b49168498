package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import lombok.Data;

@Data
public class AliexpressCustomSet implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_custom_set.id
     */
    private Integer id;

    /**
     * 自定义内容 database column aliexpress_custom_set.custom_value
     */
    private String customValue;

    private List<String> nameList;

    /**
     * 创建人 database column aliexpress_custom_set.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_custom_set.create_time
     */
    private Timestamp createTime;

    /**
     * 修改人 database column aliexpress_custom_set.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_custom_set.last_update_date
     */
    private Timestamp lastUpdateDate;
}