package com.estone.erp.publish.smt.enums;

public enum DataViewStatusEnum {

    ADD(1, "新增"),

    ADD_SUCCESS(10, "新增刊登成功"),

    TOTAL(2, "总数")
    ;

    private Integer id;
    private String name;

    private DataViewStatusEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
