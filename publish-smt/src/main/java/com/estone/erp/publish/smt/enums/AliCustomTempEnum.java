package com.estone.erp.publish.smt.enums;

public enum AliCustomTempEnum {
    car(1, "车型库模板");

    private int code;

    private String name;

    private AliCustomTempEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliCustomTempEnum build(int code) {
        AliCustomTempEnum[] values = values();
        for (AliCustomTempEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliCustomTempEnum[] values = values();
        for (AliCustomTempEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
