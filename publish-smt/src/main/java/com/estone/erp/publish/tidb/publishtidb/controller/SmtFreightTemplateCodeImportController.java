package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.bean.excel.FreightTempCodeImportExcel;
import com.estone.erp.publish.smt.enums.FreightImportStatusEnum;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtFreightTemplateCodeImportDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport;
import com.estone.erp.publish.tidb.publishtidb.service.SmtFreightTemplateCodeImportService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * smt smt 运费模板code 导入
 * </p>
 *
 * <AUTHOR>
 * @since 2025年4月25日14:57:24
 */
@Slf4j
@RestController
@RequestMapping("/smtFreightTemplateCodeImport")
public class SmtFreightTemplateCodeImportController {

    @Resource
    private SmtFreightTemplateCodeImportService smtFreightTemplateCodeImportService;

    /**
     * 分页查询
     */
    @PostMapping("queryPage")
    public ApiResult<IPage<SmtFreightTemplateCodeImport>> queryPage(@RequestBody SmtFreightTemplateCodeImportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            IPage<SmtFreightTemplateCodeImport> page = smtFreightTemplateCodeImportService.pageQuery(dto);
            return ApiResult.newSuccess(page);
        } catch (Exception e) {
            log.error("查询失败",e.getMessage());
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 添加
     */
    @PostMapping("save")
    public ApiResult<?> save(@RequestBody SmtFreightTemplateCodeImport smtFreightTemplateCodeImport) {
        Asserts.isTrue(smtFreightTemplateCodeImport != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            String account = smtFreightTemplateCodeImport.getAccount();
            Integer executeType = smtFreightTemplateCodeImport.getExecuteType();
            String tempCode = smtFreightTemplateCodeImport.getTempCode();

            //复制的时候没有值
            Long tempId = smtFreightTemplateCodeImport.getTempId();

            LambdaQueryWrapper<SmtFreightTemplateCodeImport> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SmtFreightTemplateCodeImport::getAccount, account);
            queryWrapper.eq(SmtFreightTemplateCodeImport::getExecuteType, executeType);
            queryWrapper.eq(SmtFreightTemplateCodeImport::getTempCode, tempCode);
            queryWrapper.eq(tempId != null, SmtFreightTemplateCodeImport::getTempId, tempId);
            queryWrapper.eq(SmtFreightTemplateCodeImport::getExecuteStatus, FreightImportStatusEnum.S_0.getCode());
            SmtFreightTemplateCodeImport one = smtFreightTemplateCodeImportService.getOne(queryWrapper);

            if(one != null && one.getId() != null){
                return ApiResult.newError("该数据已经存在待处理状态的数据，请重新填写后保存");
            }
            //新增
            smtFreightTemplateCodeImport.setCreatedTime(new Timestamp(System.currentTimeMillis()));
            smtFreightTemplateCodeImport.setCreatedBy(WebUtils.getUserName());
            smtFreightTemplateCodeImport.setExecuteStatus(FreightImportStatusEnum.S_0.getCode());
            smtFreightTemplateCodeImportService.save(smtFreightTemplateCodeImport);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * excel 导入
     * @param multiPartFile
     * @param request
     * @return
     */
    @PostMapping(value = "/import")
    public ApiResult<?> excelImport(@RequestParam(value = "file", required = false) MultipartFile multiPartFile, HttpServletRequest request) throws RuntimeException, IOException {
        MultipartFile multipartFile = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        String userName = WebUtils.getUserName();
        if (fileMap.values().size() > 0) {
            multipartFile = (MultipartFile) fileMap.values().iterator().next();
            if(multipartFile == null){
                return ApiResult.newError("文件读取失败");
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            List<FreightTempCodeImportExcel> dataList = Lists.newArrayList();

            EasyExcel.read(multipartFile.getInputStream(), FreightTempCodeImportExcel.class, new AnalysisEventListener<FreightTempCodeImportExcel>() {
                @Override
                public void invoke(FreightTempCodeImportExcel rowData, AnalysisContext analysisContext) {
                    rowData.setRowNum(analysisContext.getCurrentRowNum());
                    List<String> remarks = Lists.newArrayList();
                    if (StringUtils.isBlank(rowData.getAccount())) {
                        remarks.add("店铺 不能为空");
                    }
                    ResponseJson rsp = ExcelOperationUtils.authIntercept(rowData.getAccount(), userName);
                    if(!rsp.isSuccess()){
                        remarks.add(rsp.getMessage());
                    }
                    if (StringUtils.isBlank(rowData.getExecuteType())) {
                        remarks.add("操作类型 不能为空");
                    }else{
                        List<String> strings = Arrays.asList("更新", "复制");
                        String executeType = rowData.getExecuteType();
                        if(!strings.contains(executeType)){
                            remarks.add("操作类型不对");
                        }
                        if(rowData.getExecuteType().equalsIgnoreCase("更新")){
                            if(StringUtils.isBlank(rowData.getTempId())){
                                remarks.add("操作模板id 不能为空");
                            }
                        }
                    }
                    if (StringUtils.isBlank(rowData.getTempCode())) {
                        remarks.add("模板口令 不能为空");
                    }

                    if (StringUtils.isBlank(rowData.getCodeExpireTime())) {
                        remarks.add("口令有效期 不能为空");
                    }

                    try {
                        //2025/5/4 20:31
                        String codeExpireTime = rowData.getCodeExpireTime();

                        String[] s = codeExpireTime.split(" ");
                        String yMd = s[0];
                        String Hms = s[1];

                        String[] yMds = null;
                        if(yMd.contains("-")){
                            yMds = yMd.split("-");
                        }else if(yMd.contains("/")){
                            yMds = yMd.split("/");
                        }
                        String year = yMds[0];
                        String month = yMds[1];
                        if(month.length() == 1){
                            month = "0"+month;
                        }
                        String day = yMds[2];
                        if(day.length() == 1){
                            day = "0"+day;
                        }

                        String[] Hmss = Hms.split(":");
                        String hour = Hmss[0];
                        if(hour.length() == 1){
                            hour = "0"+hour;
                        }
                        String minute = Hmss[1];
                        if(minute.length() == 1){
                            minute = "0"+minute;
                        }
                        String second = "00";
                        String pstTimeStr = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
                        // 解析PST时间
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime localDateTime = LocalDateTime.parse(pstTimeStr, formatter);
                        ZonedDateTime pstTime = localDateTime.atZone(ZoneId.of("America/Los_Angeles"));
                        // 转换为北京时间
                        ZonedDateTime chinaTime = pstTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                        String format = chinaTime.format(formatter);
                        Date parse = sdf.parse(format);
                        rowData.setCodeExpireTimeDate(parse);
                        if(rowData.getCodeExpireTimeDate() == null){
                            remarks.add("口令有效期 格式异常" + codeExpireTime);
                        }
                    } catch (Exception e) {
                        remarks.add("口令有效期 格式异常" + e.getMessage());
                    }
                    if (StringUtils.isBlank(rowData.getUpdateTempName())) {
                        remarks.add("更新为模板名称 不能为空");
                    }
                    if (CollectionUtils.isNotEmpty(remarks)) {
                        rowData.setRemark(String.join(",", remarks));
                    }
                    dataList.add(rowData);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    List<FreightTempCodeImportExcel> errorList = dataList.stream().filter(t -> StringUtils.isNotBlank(t.getRemark())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(errorList)){
                        for (FreightTempCodeImportExcel freightTempCodeImportExcel : errorList) {
                            Integer rowNum = freightTempCodeImportExcel.getRowNum();
                            log.info("第{}行，数据异常：" + freightTempCodeImportExcel.getRemark(), rowNum);
                        }
                    }
                    //需要处理的数据
                    List<FreightTempCodeImportExcel> haldList = dataList.stream().filter(t -> StringUtils.isBlank(t.getRemark())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(haldList)){
                        return;
                    }

                    //同样的数据，保留有效期最晚的数据
                    Map<String, List<FreightTempCodeImportExcel>> listMap = haldList.stream().collect(Collectors.groupingBy(t -> t.getAccount() + "-" + t.getTempId() + "-" + t.getTempCode() + "-" + t.getExecuteType()));

                    for (Map.Entry<String, List<FreightTempCodeImportExcel>> stringListEntry : listMap.entrySet()) {
                        FreightTempCodeImportExcel freightTempCodeImportExcel = stringListEntry.getValue().stream()
                                .sorted(Comparator.comparing(FreightTempCodeImportExcel::getCodeExpireTimeDate).reversed())
                                .collect(Collectors.toList()).get(0);
                        try {
                            String account = freightTempCodeImportExcel.getAccount();
                            String executeTypeStr = freightTempCodeImportExcel.getExecuteType();
                            Integer executeType = StringUtils.equalsIgnoreCase(executeTypeStr, "更新") ? 1 : 2;
                            String tempCode = freightTempCodeImportExcel.getTempCode();

                            //复制的时候没有值
                            Long tempId = null;
                            if(StringUtils.isNotBlank(freightTempCodeImportExcel.getTempId())){
                                tempId = Long.valueOf(freightTempCodeImportExcel.getTempId());
                            }
                            SmtFreightTemplateCodeImport smtFreightTemplateCodeImport = new SmtFreightTemplateCodeImport();
                            smtFreightTemplateCodeImport.setAccount(account);
                            smtFreightTemplateCodeImport.setTempId(tempId);
                            smtFreightTemplateCodeImport.setExecuteType(executeType);
                            smtFreightTemplateCodeImport.setTempCode(tempCode);
                            smtFreightTemplateCodeImport.setCodeExpireTime(freightTempCodeImportExcel.getCodeExpireTimeDate());
                            smtFreightTemplateCodeImport.setUpdateTempName(freightTempCodeImportExcel.getUpdateTempName());
                            smtFreightTemplateCodeImport.setExecuteStatus(FreightImportStatusEnum.S_0.getCode());

                            LambdaQueryWrapper<SmtFreightTemplateCodeImport> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(SmtFreightTemplateCodeImport::getAccount, account);
                            queryWrapper.eq(SmtFreightTemplateCodeImport::getExecuteType, executeType);
                            queryWrapper.eq(SmtFreightTemplateCodeImport::getTempCode, tempCode);
                            queryWrapper.eq(tempId != null, SmtFreightTemplateCodeImport::getTempId, tempId);
                            queryWrapper.eq(SmtFreightTemplateCodeImport::getExecuteStatus, FreightImportStatusEnum.S_0.getCode());
                            SmtFreightTemplateCodeImport one = smtFreightTemplateCodeImportService.getOne(queryWrapper);
                            if(one != null && one.getId() != null){
                                log.info("第{}行，已经存在 更新处理" , freightTempCodeImportExcel.getRowNum());
                                smtFreightTemplateCodeImport.setId(one.getId());
                                smtFreightTemplateCodeImport.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
                                smtFreightTemplateCodeImport.setUpdateBy(WebUtils.getUserName());
                                smtFreightTemplateCodeImportService.updateById(smtFreightTemplateCodeImport);
                            }else{
                                smtFreightTemplateCodeImport.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                                smtFreightTemplateCodeImport.setCreatedBy(WebUtils.getUserName());
                                smtFreightTemplateCodeImportService.save(smtFreightTemplateCodeImport);
                            }
                        } catch (Exception e) {
                            log.info("第{}行，数据异常：" + e.getMessage(), freightTempCodeImportExcel.getRowNum());
                        }
                    }
                }
            }).sheet().doRead();
        }
        return ApiResult.newSuccess();
    }

    /**
     * 导出
     */
    @PostMapping("export")
    public ApiResult<?> export(@RequestBody SmtFreightTemplateCodeImportDto dto) throws IOException {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        return smtFreightTemplateCodeImportService.download(dto);
    }
    
    /**
     * 删除记录
     */
    @PostMapping("delete")
    public ApiResult<?> delete(@RequestBody SmtFreightTemplateCodeImportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        return smtFreightTemplateCodeImportService.deleteByIds(dto);
    }
}
