package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionDetails;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AliexpressProductLogCountDto {
    private String accountNumber;
    private String ruleName;
    private Integer totalCount;
    private Integer successCount;
    private Integer failureCount;
    private String failureReasonsCountJson;

    public AliexpressTaskExecutionDetails toAliexpressTaskExecutionDetails(AliexpressProductLogCountDto dto){
        AliexpressTaskExecutionDetails details = new AliexpressTaskExecutionDetails();
        details.setAccount(dto.getAccountNumber());
        details.setRuleName(dto.getRuleName());
        details.setTotalNum(dto.getTotalCount());
        details.setSuccessNum(dto.getSuccessCount());
        details.setFailNum(dto.getFailureCount());
        //获取昨天的时间
        details.setCountTime(DateUtils.getYesterdayDate(new Date()));
        details.setUpdateTime(new Date());
        details.setFailureReasonsCountJson(dto.getFailureReasonsCountJson());
        return details;
    }
}
