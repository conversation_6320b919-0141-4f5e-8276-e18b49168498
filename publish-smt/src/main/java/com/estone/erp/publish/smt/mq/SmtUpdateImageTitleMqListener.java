package com.estone.erp.publish.smt.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateImageTitleRequest;
import com.estone.erp.publish.smt.mq.bean.UpdateImageTitleMqBean;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 更新主图和标题MQ监听器
 */
@Slf4j
@Component
public class SmtUpdateImageTitleMqListener implements ChannelAwareMessageListener {

    @Autowired
    private AliexpressEsExtendService aliexpressEsExtendService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {

            UpdateImageTitleMqBean mqBean = JSON.parseObject(message.getBody(), UpdateImageTitleMqBean.class);

            if (mqBean == null || CollectionUtils.isEmpty(mqBean.getRequests())) {
                log.warn("更新主图和标题消息内容为空");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            // 设置用户上下文
            DataContextHolder.setUsername(mqBean.getUserName());
            // 处理更新请求
            processUpdateRequests(mqBean.getRequests());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.error("处理更新主图和标题消息异常", e);
        }
    }


    /**
     * 处理更新请求
     *
     * @param requests 更新请求列表
     */
    private void processUpdateRequests(List<AliexpressUpdateImageTitleRequest> requests) {
        try {
            // 调用原有的处理逻辑
            aliexpressEsExtendService.processImageTitleRequests(requests);
        } catch (Exception e) {
            log.error("处理更新主图和标题请求异常", e);
        }
    }


}
