package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-08-21 09:56:59
 */
public class SmtCategoryGpsrLogoCriteria extends SmtCategoryGpsrLogo {
    private static final long serialVersionUID = 1L;

    public SmtCategoryGpsrLogoExample getExample() {
        SmtCategoryGpsrLogoExample example = new SmtCategoryGpsrLogoExample();
        SmtCategoryGpsrLogoExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (StringUtils.isNotBlank(this.getFullPathCode())) {
            criteria.andFullPathCodeEqualTo(this.getFullPathCode());
        }
        if (StringUtils.isNotBlank(this.getFullCnName())) {
            criteria.andFullCnNameEqualTo(this.getFullCnName());
        }
        if (StringUtils.isNotBlank(this.getUrl())) {
            criteria.andUrlEqualTo(this.getUrl());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (this.getResult() != null) {
            criteria.andResultEqualTo(this.getResult());
        }
        if (StringUtils.isNotBlank(this.getFailInfo())) {
            criteria.andFailInfoEqualTo(this.getFailInfo());
        }
        return example;
    }
}