package com.estone.erp.publish.smt.enums;

/**
 * 商品诊断状态
 */
public enum DiagnosisStatusEnum {
    already_optimize(-1, "已优化"),
    wait_optimize(0, "待优化"),
    Operated(1, "已操作、检测中"),
    off(2, "已下架"),
    ignore(3, "已忽略"),
    delete(4, "已删除");

    private int code;

    private String name;

    private DiagnosisStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DiagnosisStatusEnum build(int code) {
        DiagnosisStatusEnum[] values = values();
        for (DiagnosisStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        DiagnosisStatusEnum[] values = values();
        for (DiagnosisStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
