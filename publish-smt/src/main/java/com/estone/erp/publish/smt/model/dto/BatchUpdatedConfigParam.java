
package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class BatchUpdatedConfigParam implements Serializable {

//    @NotNull(message = "批量设置，限购数必填")
    private Integer buy_max_num;

//    @NotNull(message = "批量设置，折扣必填")
    private Integer discount;

    /**
     * 定向人群额外折扣类型,0=无,1=店铺粉丝
     */
//    @NotNull(message = "批量设置，定向人群额外折扣类型必填")
    private Integer club_discount_type;


//    @NotNull(message = "批量设置，粉丝折扣必填")
    private Integer store_club_discount_rate;
}