package com.estone.erp.publish.smt.enums;

public enum AliexpressMarketingConfigUpdateEnum {
    FAULSE(0,"未更改"),

    TRUE(1,"已更改"),
    FINISH(2,"结束")
    ;

    private int code;

    private String name;

    AliexpressMarketingConfigUpdateEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressMarketingConfigUpdateEnum build(int code) {
        AliexpressMarketingConfigUpdateEnum[] values = values();
        for (AliexpressMarketingConfigUpdateEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressMarketingConfigUpdateEnum[] values = values();
        for (AliexpressMarketingConfigUpdateEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }

}
