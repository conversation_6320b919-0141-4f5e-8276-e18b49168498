package com.estone.erp.publish.smt.util.convert;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 通用枚举转换器
 */
public class EConverter implements Converter<Integer> {
    @Override
    public Class supportJavaTypeKey() {
    //指定转换器接收参数类型
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
    //指定返回的参数类型
        return CellDataTypeEnum.STRING;
    }



    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
   		//value:状态码  contentProperty：字段属性  globalConfiguration：全局配置
    	//获取字段属性中的注解
        Field field = contentProperty.getField();
        //获取该字段所属枚举
        EasyExcel easyExcel = field.getAnnotation(EasyExcel.class);
        //获取注解中的枚举信息
        Class<? extends Enum> type = easyExcel.type();
        //获取枚举类的方法名 “codeOf”就是自己编写的函数，Integer.class 指定入参类型
        Method codeOf = type.getMethod("fromCode", int.class);
        //反射执行方法，此方法得到的是一个枚举实例（具体得到什么，结合自身项目）
        Object invoke = codeOf.invoke(type, value);
        //枚举实例调用getname方法，得到name的值
        Method getName = invoke.getClass().getMethod("getDescription");
        String name = String.valueOf(getName.invoke(invoke));
        //将转换的值进行返回
        return  new WriteCellData<>(name);
    }
}
