package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class HalfTemp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column half_temp.id
     */
    private Long id;

    /**
     * 店铺 database column half_temp.account
     */
    private String account;

    /**
     * 关联的模板id database column half_temp.temp_id
     */
    private Integer tempId;

    /**
     * 关联的草稿id database column half_temp.draft_id
     */
    private String draftId;

    /**
     * 该商品下已勾选参与的国家列表 database column half_temp.joined_country_list
     */
    private String joinedCountryList;

    /**
     * product_sku_list database column half_temp.product_sku_list
     */
    private String productSkuList;

    /**
     * 创建时间 database column half_temp.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column half_temp.create_by
     */
    private String createBy;

    /**
     * 修改时间 database column half_temp.update_date
     */
    private Timestamp updateDate;

    /**
     * 修改人 database column half_temp.update_by
     */
    private String updateBy;
}