package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.bean.SkuProperty.product.ProductSkuProperty;
import com.estone.erp.publish.smt.model.AliexpressTemplate;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpDas.ErpDasUtils;
import com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2020/10/20
 */
public class AliexpressSpUtils {

    /**
     * 更新试卖产品
     * @param template
     */
    public static ApiResult<?>  updateSpProduct(AliexpressTemplate template) {
        String aeopAeProductSkusJson = template.getAeopAeProductSkusJson();

        List<ProductSkuProperty> productSkuProperties = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<ProductSkuProperty>>(){});
        if(CollectionUtils.isEmpty(productSkuProperties)) {
            return ApiResult.newError("sku数据不全");
        }

        SaleAccountAndBusinessResponse account = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, template.getAliexpressAccountNumber());

        List<String> skuList = new ArrayList<>();
        for (ProductSkuProperty productSkuProperty : productSkuProperties) {
            String skuCode = productSkuProperty.getSku_code();
            if(skuCode.startsWith(account.getSellerSkuPrefix())){
                skuCode = skuCode.replaceFirst(account.getSellerSkuPrefix(), "");
            }
            skuList.add(skuCode);
        }

        if(CollectionUtils.isEmpty(skuList)) {
            skuList.add(template.getArticleNumber());
        }

        // 试卖sku数据对象
        SpProductSaleMsg spProductSaleMsg = new SpProductSaleMsg();

        spProductSaleMsg.setOriginPlatform(SaleChannel.CHANNEL_SMT);
        spProductSaleMsg.setMainSku(template.getArticleNumber());
        spProductSaleMsg.setSonSkus(JSON.toJSONString(skuList));

        return ErpDasUtils.pushSpSkuToDas(spProductSaleMsg);
    }
}
