package com.estone.erp.publish.config;

import com.estone.erp.common.mybatis.AbstractDataSourceConfig;
import com.estone.erp.publish.mybatis.DataSources;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/***
 * publish-smt数据库配置类
 */
@Configuration
@Slf4j
@MapperScan(basePackages = {"com.estone.erp.publish.smt.**.mapper","com.estone.erp.publish.base.**.mapper","com.estone.erp.publish.feginService.**.mapper",
        "com.estone.erp.publish.platform.**.mapper","com.estone.erp.publish.system.**.mapper" }, sqlSessionFactoryRef = DataSources.PUBLISH_SMT_FAC)
//@MapperScan(basePackages = { "com.estone.erp.publish.*.mapper",
//        "com.estone.erp.publish.system.*.mapper" }, sqlSessionFactoryRef = DataSources.PUBLISH_SMT_FAC)
@ConfigurationProperties(prefix = "mybatis.publish")
public class PublishDataSourceConfig extends AbstractDataSourceConfig {

    @Primary
    @Bean(DataSources.PUBLISH_SMT_DS)
    @ConfigurationProperties(prefix = "spring.datasource.publish")
    public DataSource dataSource() {
        log.info("===========加载数据源:spring.datasource.publish-smt========");
        return getDataSource();
    }

    @Bean(DataSources.PUBLISH_SMT_FAC)
    public SqlSessionFactory sqlSessionFactory() {
        return getSqlSessionFactory(dataSource());
    }

    @Bean(DataSources.PUBLISH_SMT_TEMP)
    public SqlSessionTemplate sqlSessionTemplate() {
        return getSqlSessionTemplate(sqlSessionFactory());
    }
}