package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> aliexpress_custom_set
 * 2022-01-17 10:27:46
 */
public class AliexpressCustomSetCriteria extends AliexpressCustomSet {
    private static final long serialVersionUID = 1L;

    public AliexpressCustomSetExample getExample() {
        AliexpressCustomSetExample example = new AliexpressCustomSetExample();
        AliexpressCustomSetExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getCustomValue())) {
            criteria.andCustomValueEqualTo(this.getCustomValue());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (StringUtils.isNotBlank(this.getLastUpdateBy())) {
            criteria.andLastUpdateByEqualTo(this.getLastUpdateBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        return example;
    }
}