package com.estone.erp.publish.smt.enums;

public enum AliexpressEarlyBirdJoinEnum {
    WAIT_JOIN("1", "待加入"),
    CONFIRM_JOIN("2", "确认加入"),
    SUCCESS_JOIN("3", "加入成功"),
    JOINING("5", "加入中"),
    FAIL_JOIN("4", "加入失败");

    private String code;
    private String name;

    private AliexpressEarlyBirdJoinEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static AliexpressEarlyBirdJoinEnum build(String code) {
        for (AliexpressEarlyBirdJoinEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (AliexpressEarlyBirdJoinEnum type : values()) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

}