package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.HttpUtils;
import com.estone.erp.publish.system.product.request.ImageRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpMethod;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年12月18日17:59:28
 */
@Slf4j
public class SmtImgSolutionUtil extends AbstractHttpClient {

    /**
     * 调用算法
     * @param imageRequest
     * @return
     */
    public static List<String> imgRepeatCheck(ImageRequest imageRequest) {
        List<String> imgList = new ArrayList<>();
        if(imageRequest == null || CollectionUtils.isEmpty(imageRequest.getImg_list1()) ||  CollectionUtils.isEmpty(imageRequest.getImg_list2())){
            return imgList;
        }
        List<String> img_list1 = imageRequest.getImg_list1();
        List<String> img_list2 = imageRequest.getImg_list2();

        try {
            JSONObject param = new JSONObject();
            param.put("img_list1", img_list1);
            param.put("img_list2", img_list2);
            HttpParams<String> httpParams = new HttpParams<>();
            httpParams.setUrl(CacheUtils.SystemParamGet("smt_img.images_contrast").getParamValue());
            httpParams.setHttpMethod(HttpMethod.POST);
            httpParams.setBody(param.toJSONString());
            String result = HttpUtils.exchange(httpParams, String.class);
            if(StringUtils.isBlank(result)){
                throw new RuntimeException("图片重复校验异常:" + JSON.toJSONString(imageRequest));
            }
            JSONObject jsonObject = JSON.parseObject(result);
            String msg = jsonObject.getString("msg");
            if(!StringUtils.equalsIgnoreCase(msg, "查询成功")){
                throw new RuntimeException("图片重复校验异常:" + result);
            }
            JSONObject responseObj = jsonObject.getJSONObject("response");

            JSONArray group_resArray = responseObj.getJSONArray("group_res");
            if(group_resArray == null || CollectionUtils.isEmpty(group_resArray)){
                return img_list1;
            }
            //重复的图片取一张
            for (int i = 0; i < group_resArray.size(); i++) {
                JSONArray jsonArray = group_resArray.getJSONArray(i);
                String s = jsonArray.get(0).toString();
                imgList.add(s);
            }
        } catch (Exception e) {
            throw new RuntimeException("图片重复校验异常:" +  e.getMessage());
        }
        return imgList;
    }

    /**
     * 自己对比
     * @param imageRequest
     * @return
     */
    public static List<String> imgRepeatCheckNew(ImageRequest imageRequest) {
        Set<String> imgListSet = new HashSet<>();
        if(imageRequest == null || CollectionUtils.isEmpty(imageRequest.getImg_list1()) ||  CollectionUtils.isEmpty(imageRequest.getImg_list2())){
            return new ArrayList<>(imgListSet);
        }
        List<String> img_list1 = imageRequest.getImg_list1();
        List<String> img_list2 = imageRequest.getImg_list2();

        List<String> removeImgList = new ArrayList<>();

        for (int i = 0; i < img_list1.size(); i++) {
            String url1 = img_list1.get(i);
            for (int i1 = 0; i1 < img_list2.size(); i1++) {
                String url2 = img_list2.get(i1);
                //不一样才对比
                if(!StringUtils.equalsIgnoreCase(url1, url2)){
                    try {
                        boolean isSame = areImagesIdentical(url1, url2);
                        if(isSame){
                            //需要去除最后的数据
                            if(i1 > i){
                                removeImgList.add(url2);
                            }
                        }
                    } catch (Exception e) {
                       log.error(e.getMessage(), e);
                    }
                }
            }
        }
//        removeImgList.forEach(s -> System.out.println("去除图片:" + s));
        img_list1.removeIf(removeImgList::contains);
        return img_list1;
    }

    private static  <T> ApiResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<T> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        try {
            try {
                String result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    return ApiResult.newError(result);
                }
                T t = JSON.parseObject(result, reference);
                return ApiResult.newSuccess(t);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }

    private static boolean areImagesIdentical(String imageUrl1, String imageUrl2) throws IOException, NoSuchAlgorithmException {
        byte[] hash1 = getImageHash(imageUrl1);
        byte[] hash2 = getImageHash(imageUrl2);

        return MessageDigest.isEqual(hash1, hash2);
    }

    private static byte[] getImageHash(String imageUrl) throws IOException, NoSuchAlgorithmException {
        URL url = new URL(imageUrl);
        try (InputStream in = url.openStream()) {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[8192];
            int read;

            while ((read = in.read(buffer)) != -1) {
                digest.update(buffer, 0, read);
            }

            return digest.digest();
        }
    }

    public static void main(String[] args) {
        try {
            String url1 = "http://172.16.10.51:8888/amazon/2024-12/21-10-21-58-449/19SQ100396-BG-000.jpg";
            String url2 = "http://172.16.10.51:8888/amazon/2024-12/21-10-21-58-453/19SQ100396-PK.jpg";
//            String url3 = "http://172.16.10.51:8888/public/2024-12/18-15-54-57-017/7HH1004784-PK.jpg";
//            String url4 = "http://172.16.10.51:8888/public/2024-12/18-15-54-21-027/7HH1004784-LBL.jpg";
//            String url5 = "http://172.16.10.51:8888/public/2024-12/18-15-57-26-364/7HH1004784-5.jpg";

            // 比较两个URL指向的图片是否相同
//            boolean isSame = areImagesIdentical(url1, url2);
//            System.out.println("The images are " + (isSame ? "the same." : "different."));

            ImageRequest request = new ImageRequest();
            request.setImg_list1(Lists.newArrayList(url1, url2));
            request.setImg_list2(Lists.newArrayList(url1,url2));
            List<String> strings = imgRepeatCheckNew(request);
            strings.forEach(s -> System.out.println(s));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
