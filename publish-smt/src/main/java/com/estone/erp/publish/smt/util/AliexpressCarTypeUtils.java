//package com.estone.erp.publish.smt.util;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.TypeReference;
//import com.estone.erp.common.model.api.ApiResult;
//import com.estone.erp.common.util.CommonUtils;
//import com.estone.erp.common.util.SpringUtils;
//import com.estone.erp.publish.common.util.ExcelUtils;
//import com.estone.erp.publish.common.util.POIUtils;
//import com.estone.erp.publish.smt.bean.*;
//import com.estone.erp.publish.smt.enums.AliCustomTempEnum;
//import com.estone.erp.publish.smt.enums.CarTypeEnum;
//import com.estone.erp.publish.smt.model.*;
//import com.estone.erp.publish.smt.service.AliexpressCarTypeInfoService;
//import com.estone.erp.publish.smt.service.AliexpressCarTypeService;
//import com.estone.erp.publish.smt.service.AliexpressProductService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.poi.ss.usermodel.Cell;
//import org.apache.tools.ant.taskdefs.Concat;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @description:
// * @date 2020/6/1116:52
// */
//@Slf4j
//public class AliexpressCarTypeUtils {
//
//    private static String[] headerState = { "Country", "Make", "Model", "Year", "Trim", "Engine"};
//
//
//    public static List<CarAttributeJson> getCarAttributeJson(Integer level, String attributeJson){
//        List<CarAttributeJson> attributes = null;
//
//        try{
//            if(level == 1){
//                attributes = new ArrayList<>();
//
//                CarAttributeJson carAttributeJson = JSONObject
//                        .parseObject(attributeJson, new TypeReference<CarAttributeJson>() {
//                        });
//
//                attributes.add(carAttributeJson);
//            }else{
//                CarAttributeBean carAttributeBean = JSONObject.parseObject(attributeJson, new TypeReference<CarAttributeBean>() {
//                });
//                attributes = carAttributeBean.getAttributes();
//            }
//
//        }catch (Exception e){
//            log.error(e.getMessage(), e);
//        }
//        return attributes;
//
//    }
//
//    /**
//     * 模板解析excel
//     * @param file
//     * @param infos
//     * @return
//     * @throws IOException
//     */
//    public static List<String> getDataFromExcel(MultipartFile file, List<AliexpressCarTypeInfo> infos) throws
//            IOException {
//
//        List<String> errorMsgList = new ArrayList<>();
//
//        AliexpressCarTypeInfoService aliexpressCarTypeInfoService = SpringUtils.getBean(AliexpressCarTypeInfoService.class);
//
//        POIUtils.readExcelSheet1(headerState, file, row -> {
//            try {
//                if(row == null) {
//                    return null;
//                }
//
//                int rowNum = row.getRowNum();
//
//                String country = ExcelUtils.getCellValue(row.getCell(0)).trim();
//                String make = ExcelUtils.getCellValue(row.getCell(1)).trim();
//                String model = ExcelUtils.getCellValue(row.getCell(2)).trim();
//                String year = ExcelUtils.getCellValue(row.getCell(3)).trim();
//                String trim = ExcelUtils.getCellValue(row.getCell(4)).trim();
//                String engine = ExcelUtils.getCellValue(row.getCell(5)).trim();
//
//                if(StringUtils.isBlank(country) || StringUtils.isBlank(make) || StringUtils.isBlank(model)
//                        || StringUtils.isBlank(year) || StringUtils.isBlank(trim) || StringUtils.isBlank(engine)){
//
//                    errorMsgList.add(String.format("第【%s】行 错误【%s】", rowNum, "数据不完整"));
//                    return null;
//                }
//
//                AliexpressCarTypeInfoCriteria query = new AliexpressCarTypeInfoCriteria();
//                query.setCountryEn(country);
//                query.setMakeEn(make);
//                query.setModelEn(model);
//                query.setYearEn(year);
//                query.setTrimEn(trim);
//                query.setEnigneEn(engine);
//                query.setTypeLevel(6);
//                List<AliexpressCarTypeInfo> aliexpressCarTypeInfos = aliexpressCarTypeInfoService
//                        .selectByExample(query.getExample());
//
//                if(CollectionUtils.isEmpty(aliexpressCarTypeInfos)){
//                    //匹配不到数据
//                    errorMsgList.add(String.format("第【%s】行 错误【%s】", rowNum, "匹配不到数据"));
//                    return null;
//                }
//
//                infos.addAll(aliexpressCarTypeInfos);
//
//                return row;
//            }catch(Exception e) {
//                log.error("Excel数据错误：rowNum:" + (row.getRowNum()+1));
//            }
//            return null;
//        }, false);
//        return errorMsgList;
//    }
//
//    /**
//     * 获取编辑数据
//     * @param infos
//     * @return
//     */
//    public static String getEditData(List<AliexpressCarTypeInfo> infos) {
//        Map<String, List<CarInfoTempBean>> resultMap = new HashMap<>();
//
//        for (AliexpressCarTypeInfo info : infos) {
//
//            CarInfoTempBean carInfoTempBean = new CarInfoTempBean();
//            Integer typeLevel = info.getTypeLevel();
//            Long typeId = info.getTypeId(); //id属性
//
//            String attr_name = CarTypeEnum.getNameByCode(typeLevel);
//
//            //名词取错，做特殊处理
//            if(typeLevel == 6){
//                attr_name = "Engine";
//            }
//            Long attr_name_id = null;
//            String label = ""; //label
//            String zh = ""; //需要层级拼接 (弃用)
//            String en = ""; //英文需要层级拼接
//
//            if(typeLevel == 1){
//                attr_name_id = info.getCountryUpId();
//                label = info.getCountryEn();
//                zh = info.getCountryZh();
//                en = info.getCountryEn();
//            }else if(typeLevel == 2){
//                attr_name_id = info.getMakeUpId();
//                label = info.getMakeEn();
//                zh = info.getCountryZh() + " - " + info.getMakeZh();
//                en = info.getCountryEn() + " - " + info.getMakeEn();
//            }else if(typeLevel == 3){
//                attr_name_id = info.getModelUpId();
//                label = info.getModelEn();
//                zh = info.getCountryZh() + " - " + info.getMakeZh() + " - " + info.getModelZh();
//                en = info.getCountryEn() + " - " + info.getMakeEn() + " - " + info.getModelEn();
//            }else if(typeLevel == 4){
//                attr_name_id = info.getYearUpId();
//                label = info.getYearEn();
//                zh = info.getCountryZh() + " - " + info.getMakeZh() + " - " + info.getModelZh() + " - " + info.getYearZh();
//                en = info.getCountryEn() + " - " + info.getMakeEn() + " - " + info.getModelEn() + " - " + info.getYearEn();
//            }else if(typeLevel == 5){
//                attr_name_id = info.getTrimUpId();
//                label = info.getTrimEn();
//                zh = info.getCountryZh() + " - " + info.getMakeZh() + " - " + info.getModelZh() + " - " + info.getYearZh() + " - " + info.getTrimZh();
//                en = info.getCountryEn() + " - " + info.getMakeEn() + " - " + info.getModelEn() + " - " + info.getYearEn() + " - " + info.getTrimEn();
//            }else if(typeLevel == 6){
//                attr_name_id = info.getEnigneUpId();
//                label = info.getEnigneEn();
//                zh = info.getCountryZh() + " - " + info.getMakeZh() + " - " + info.getModelZh() + " - " + info.getYearZh() + " - " + info.getTrimZh() + " - " + info.getEnigneZh();
//                en = info.getCountryEn() + " - " + info.getMakeEn() + " - " + info.getModelEn() + " - " + info.getYearEn() + " - " + info.getTrimEn() + " - " + info.getEnigneEn();
//            }
//
//            carInfoTempBean.setTypeLevel(typeLevel);
//            carInfoTempBean.setAttrName(attr_name);
//            carInfoTempBean.setAttrNameId(attr_name_id);
//            carInfoTempBean.setLabel(label);
////            carInfoTempBean.setZh(zh);
//            carInfoTempBean.setEn(en);
//            carInfoTempBean.setId(typeId);
//            carInfoTempBean.setCheckId(carInfoTempBean.getCheckId());
//
//            List<CarInfoTempBean> carInfoTempBeans = resultMap.get(attr_name);
//            if(CollectionUtils.isEmpty(carInfoTempBeans)){
//                carInfoTempBeans = new ArrayList<>();
//                resultMap.put(attr_name, carInfoTempBeans);
//            }
//            carInfoTempBeans.add(carInfoTempBean);
//        }
//        return JSON.toJSONString(resultMap);
//    }
//
//    /**
//     * 模板管理 编辑和添加 转换数据
//     * @param aliCustomTemp
//     * @param
//     * @return List<String> 错误信息
//     */
//    public static List<String> addOrEdit(AliCustomTemp aliCustomTemp){
//
//        List<String> errorMsgList = new ArrayList<>();
//
//        if(aliCustomTemp == null || aliCustomTemp.getTempType() == null){
//            return errorMsgList;
//        }
//
//        AliexpressCarTypeInfoService aliexpressCarTypeInfoService = SpringUtils.getBean(AliexpressCarTypeInfoService.class);
//
//        List<AliexpressCarTypeInfo> infos = new ArrayList<>();
//
//        if(aliCustomTemp.getTempType().intValue() == AliCustomTempEnum.car.intCode()){
//            List<CarInfoTempEntity> infoList = aliCustomTemp.getInfoList();
//
//            if(CollectionUtils.isNotEmpty(infoList)){
//                data(infoList, infos, errorMsgList);
//            }
//
//            if(CollectionUtils.isNotEmpty(errorMsgList)){
//                return errorMsgList;
//            }
//
//            String ids = "";
//            if(CollectionUtils.isNotEmpty(infos)){
//                //收集所有的id集合(没有去重)
//                List<String> parentIds = infos.stream().map(info -> {return info.getParentIds() + "," + info.getId();})
//                        .collect(Collectors.toList());
//                //去重
//                List<Integer> idList = CommonUtils
//                        .splitIntList(org.apache.commons.lang.StringUtils.join(parentIds, ","), ",");
//                ids = org.apache.commons.lang.StringUtils.join(idList, ",");
//            }
//
//            aliCustomTemp.setTempJson(ids);
//        }
//
//        return errorMsgList;
//
//    }
//
//    public static void data(List<CarInfoTempEntity> infoList, List<AliexpressCarTypeInfo> infos, List<String> errorMsgList){
//
//        AliexpressCarTypeInfoService aliexpressCarTypeInfoService = SpringUtils.getBean(AliexpressCarTypeInfoService.class);
//        for (CarInfoTempEntity carInfoTempEntity : infoList) {
//
//            String country = carInfoTempEntity.getCountry();
//            String make = carInfoTempEntity.getMake();
//            String model = carInfoTempEntity.getModel();
//            String year = carInfoTempEntity.getYear();
//            String trim = carInfoTempEntity.getTrim();
//            String engine = carInfoTempEntity.getEngine();
//
//            String msg = "country:" + country + " make:" + make + " model:" + model + " year:" + year + " trim:" + trim + " engine:" + engine;
//
//            if(StringUtils.isBlank(country) || StringUtils.isBlank(make) || StringUtils.isBlank(model)
//                    || StringUtils.isBlank(year) || StringUtils.isBlank(trim) || StringUtils.isBlank(engine)){
//
//                errorMsgList.add(String.format("【%s】 错误【%s】", msg, "数据不完整"));
//                continue;
//            }
//
//            AliexpressCarTypeInfoCriteria query = new AliexpressCarTypeInfoCriteria();
//            query.setCountryEn(country);
//            query.setMakeEn(make);
//            query.setModelEn(model);
//            query.setYearEn(year);
//            query.setTrimEn(trim);
//            query.setEnigneEn(engine);
//            query.setTypeLevel(6);
//            List<AliexpressCarTypeInfo> aliexpressCarTypeInfos = aliexpressCarTypeInfoService
//                    .selectByExample(query.getExample());
//
//            if(CollectionUtils.isEmpty(aliexpressCarTypeInfos)){
//                //匹配不到数据
//                errorMsgList.add(String.format("【%s】 错误【%s】", msg, "匹配不到数据"));
//                continue;
//            }
//
//            infos.addAll(aliexpressCarTypeInfos);
//        }
//    }
//
//
//
//    /**
//     * 模板/范本 编辑和添加 转换数据
//     * @param aliexpressTemplate
//     * @param
//     * @return List<String> 错误信息
//     */
//    public static List<String> templateAddOrEdit(AliexpressTemplate aliexpressTemplate){
//
//        List<String> errorMsgList = new ArrayList<>();
//
//        if(aliexpressTemplate == null){
//            return errorMsgList;
//        }
//
//        List<AliexpressCarTypeInfo> infos = new ArrayList<>();
//
//        List<CarInfoTempEntity> infoList = aliexpressTemplate.getInfoList();
//
//        if(CollectionUtils.isNotEmpty(infoList)){
//            data(infoList, infos, errorMsgList);
//        }
//
//        if(CollectionUtils.isNotEmpty(errorMsgList)){
//            return errorMsgList;
//        }
//
//        String ids = "";
//        if(CollectionUtils.isNotEmpty(infos)){
//            //收集所有的id集合(没有去重)
//            List<String> parentIds = infos.stream().map(info -> {return info.getParentIds() + "," + info.getId();})
//                    .collect(Collectors.toList());
//            //去重
//            List<Integer> idList = CommonUtils
//                    .splitIntList(org.apache.commons.lang.StringUtils.join(parentIds, ","), ",");
//            ids = org.apache.commons.lang.StringUtils.join(idList, ",");
//        }
//
//        aliexpressTemplate.setCarTypeJson(ids);
//
//        return errorMsgList;
//
//    }
//
//}
