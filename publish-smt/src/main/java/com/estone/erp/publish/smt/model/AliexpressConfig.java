package com.estone.erp.publish.smt.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.smt.bean.AliexpressAlianceConfigRequest;
import com.estone.erp.publish.smt.bean.AutoPublish.PublishConfig;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class AliexpressConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * database column aliexpress_config.id
     */
    private Integer id;

    /**
     * 账号 database column aliexpress_config.account
     */
    private String account;

    /**
     * 是否可用 database column aliexpress_config.usable
     */
    private Boolean usable;

    /**
     * 默认发货期 database column aliexpress_config.deliveryTime
     */
    private Integer deliverytime;

    /**
     * 是否批发价 database column aliexpress_config.wholesale
     */
    private Boolean wholesale;

    /**
     * 标签
     */
    private String labels;

    /**
     * 购买数量 database column aliexpress_config.bulkOrder
     */
    private Integer bulkorder;

    /**
     * 减免 database column aliexpress_config.bulkDiscount
     */
    private Integer bulkdiscount;

    /**
     * 库存 database column aliexpress_config.stock
     */
    private Integer stock;

    /**
     * 产品有效期（1-30） database column aliexpress_config.wsValidNum
     */
    private Integer wsvalidnum;


    /**
     * 间隔 database column aliexpress_config.timing_interval
     */
    private Integer timingInterval;

    /**
     * 分钟 database column aliexpress_config.timing_minute
     */
    private Integer timingMinute;

    /**
     * 小时 database column aliexpress_config.timing_hour
     */
    private Integer timingHour;

    /**
     * 标题组成优先级 database column aliexpress_config.title_json
     */
    private String titleJson;

    /**
     * logo 文字 database column aliexpress_config.logo_text
     */
    private String logoText;

    /**
     * logo 位置 database column aliexpress_config.logo_position
     */
    private String logoPosition;

    /**
     * 字体大小 database column aliexpress_config.font_size
     */
    private String fontSize;

    /**
     * logo 颜色 database column aliexpress_config.logo_color
     */
    private String logoColor;

    /**
     * 每天最大刊登数量 database column aliexpress_config.max_publish_num
     */
    private Integer maxPublishNum;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 开店时间
     */
    private String openDate;

    /**
     * cny结算
     */
    private Boolean cny;

    /**
     * 修改人 database column aliexpress_config.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_config.update_date
     */
    private Timestamp updateDate;

    /**
     * 创建人 database column aliexpress_config.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_config.create_date
     */
    private Timestamp createDate;

    private String categoryIds;

    /**
     * 自动上架新品
     */
    private Boolean autoGroundingNew;

    /**
     * 自动调整重量
     */
    private Boolean autoUpdateWeight;

    /**
     * 自动补库存
     */
    private Boolean autoSupplyStock;

    /**
     * 自动修改标题
     */
    private Boolean autoUpdateTitle;

    /**
     * 自动修改描述
     */
    private Boolean autoUpdateDetail;

    /**
     * 自动修改子sku图片
     */
    private Boolean autoUpdateSonimg;

    /**
     * 是否分配新品
     */
    private Boolean autoRecommendNewProduct;

    /**
     * 自动修改亏损订单
     */
    private Boolean autoUpdateDeficitOrder;

    /**
     * 自动下架竞争力不好的商品
     */
    private Boolean autoDownForBadItem;

    /**
     * 是否自动调整32国价格
     */
    @Deprecated
    private Boolean autoCalcPrice;

    /**
     * 是否自动添加EPR费用
     */
    private Boolean autoAddEprFee;

    /**
     * 是否加入商品及物流包装环保费
     */
    private Boolean addEprPackFee;

    /**
     * 包装尺寸 1实际尺寸 2店铺配置尺寸 3 系统默认
     */
    private Integer packingType;

    /**
     * 商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。 database column aliexpress_template.package_length
     */
    private Integer packageLength;

    /**
     * 商品包装宽度。取值范围:1-700,单位:厘米。 database column aliexpress_template.package_width
     */
    private Integer packageWidth;

    /**
     * 商品包装高度。取值范围:1-700,单位:厘米。 database column aliexpress_template.package_height
     */
    private Integer packageHeight;

    /**
     * 增加重量g
     */
    private Double addWeight;

    /**
     * 标题前后缀类型 0 前缀 1 后缀
     */
    private Integer titleType;

    /**
     * 标题文本
     */
    private String titleValue;

    /**
     * 经营大类
     */
    private String rootCategory;

    //重量区间 净重+包材+包装材料 + 3g
    private Double fromWeight;

    private Double toWeight;

    //销售成本价区间
    private Double fromPrice;

    private Double toPrice;

    //产品特殊标签
    private String specialTags;

    //item总数
    private Integer itemCount;

    /**
     * 系统自动刊登的产品分组
     */
    private Long autoGroupId;

    /**
     * 刊登数量限制
     */
    private Integer limitStoreProductNumber;

    private List<String> categoryIdList;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;

    /**
     * 昨天商品动销率
     */
    private double salesYesterdayRate;

    /**
     * 30天商品动销率
     */
    private double sales_30d_rate;

    /**
     * 60天商品动销率
     */
    private double sales_60d_rate;

    /**
     * 产品开发
     */
    private String productDev;

    /**
     * 月度销售额目标
     */
    private Double monthSaleTarget;

    /**
     * 年度销售额目标
     */
    private Double yearSaleTarget;

    /**
     * 月度新增链接数目标
     */
    private Integer monthAddListingTarget;

    /**
     * 好评率
     */
    private Double positiveRate;

    /**
     * 卖家评级
     */
    private String detailedSellerRatings;

    /**
     * 评价历史
     */
    private String feedbackInfo;

    /**
     * 自动修改资质
     */
    private Boolean autoUpdateQualifications;


    public List<String> getCategoryIdList() {

        try {
            if (StringUtils.isNotBlank(categoryIds)) {
                List<String> integers = CommonUtils.splitList(categoryIds, ",");

                return integers;
            }

        } catch (Exception e) {

        }

        return null;
    }

    private List<Integer> rootCategoryIdList;

    public List<Integer> getRootCategoryIdList() {
        try {
            if (StringUtils.isNotBlank(rootCategory)) {
                List<Integer> integers = CommonUtils.splitIntList(rootCategory, ",");

                return integers;
            }

        } catch (Exception e) {

        }

        return null;
    }

    /**
     * 水印模板id逗号分隔（按顺序）
     */
    private String watermarkTemplateIdStr;

    /**
     * 主品牌
     */
    private String mainBrand;

    /**
     * 配置详情
     */
    private List<AliexpressConfigInfo> infoList = new ArrayList<>();

    /**
     * 亏损订单毛利配置
     */
    private List<AliexpressConfigProfit> profitList = new ArrayList<>();

    /**
     * 32国调价配置（弃用 使用新规则替换）
     */
    @Deprecated
    private List<AliexpressConfigCalcPrice> calcPriceList = new ArrayList<>();

    private List<AliexpressCategory> aliexpressCategorieList;

    /**
     * 是否同步 （销售点击同步，不记录创建人时间和修改人时间）
     */
    private Boolean isSynch;

    /**
     * 是否自动下架侵权产品
     */
    private Boolean autoRetireTort;

    /**
     * 侵权类型
     */
    private String tortType;

    /**
     * 是否自动去掉侵权词汇
     */
    private Boolean autoRemoveTortWord;

    /**
     * 全托管毛利率
     */
    private Double tgProfitRate;

    /**
     * 半托管毛利率 作废
     */
    private Double halfTgProfitRate;

    /**
     * 自动预约半托管
     */
    private Boolean autoHalfReservation;

    /**
     * 揽收运费(半托管)
     */
    private Double halfShippingCost;

    /**
     * 操作费(半托管)
     */
    private Double halfOperateCost;

    /**
     * 加入半托管国家 多个英文逗号拼接
     */
    private String joinCountrys;

    /**
     * 半托管区间系数（更改为半托管价格毛利率配置）
     */
    private List<SmtConfigHalfPriceInterval> configHalfPriceIntervalList;

    /**
     * 半托管标签配置
     */
    private List<SmtConfigHalfLable> configHalfLableList;

    /**
     * 不调整库存分组
     */
    private List<AliexpressConfigNotUpdateStockGroup> notUpdateStockGroupList = new ArrayList<>();

    /**
     * 自动刊登json
     */
    private String publishJson;

    /**
     * 店铺分组名称
     */
    private String groupName;

    /**
     * 近30天发货率
     */
    private Double delivery30dRate;

    /**
     * 近30天发货率更新时间
     */
    private Timestamp delivery30dRateUpdateTime;


    /**
     * 周期内新发商品上限数
     */
    private Integer productCurrentMonthLimitNum;

    /**
     * 总可销售商品上限数
     */
    private Integer productTotalLimitNum;

    /**
     * 总审核中商品数
     */
    private Integer productTotalAuditNum;

    /**
     * 周期新发商品数
     */
    private Integer productCurrentMonthPubNum;

    /**
     * 总在线商品数
     */
    private Integer productTotalOnlineNum;


    /**
     * 预估层级
     */
    private String layerLevel;


    /**
     *经营大类
     */
    private String businessType;


    /**
     * 半托管禁售国家
     */
    private String halfForbidSaleCountry;


    /**
     * 是否自动admin范本更新在线列表属性
     */
    private Boolean autoUpdateAttr;

    /**
     * 1 不含税报价 (美国半托管必须设置1) 2 含税报价
     */
    private String taxType;

    public PublishConfig getPulishJsonConfig() {
        if (StringUtils.isNotBlank(this.getPublishJson())) {
            PublishConfig publishConfig = JSONObject.parseObject(this.getPublishJson(), new TypeReference<PublishConfig>() {
            });
            return publishConfig;
        }
        return new PublishConfig();
    }


    /**
     * 联盟配置
     */
    private AliexpressAlianceConfigRequest aliexpressAlianceConfigRequest;

}