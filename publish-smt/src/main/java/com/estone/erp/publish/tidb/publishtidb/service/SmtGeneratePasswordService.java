package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.model.SmtGeneratePassword;
import com.estone.erp.publish.tidb.publishtidb.model.SmtGeneratePasswordImportVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【smt_generate_password(smt生成口令)】的数据库操作Service
* @createDate 2025-06-12 17:16:42
*/
public interface SmtGeneratePasswordService extends IService<SmtGeneratePassword> {
    
    /**
     * 批量导入生成口令任务
     * @param importList 导入数据列表
     * @param currentUser 当前用户
     * @return 导入结果
     */
    ApiResult<String> batchImport(List<SmtGeneratePasswordImportVO> importList, String currentUser);
    
    /**
     * 检查店铺+模板ID的唯一性（针对待处理状态）
     * @param account 店铺
     * @param templateId 模板ID
     * @return 是否存在待处理的记录
     */
    boolean existsPendingRecord(String account, String templateId);
    
    /**
     * 更新任务状态
     * @param id 任务ID
     * @param status 新状态
     * @param result 执行结果
     * @param password 生成的口令（可选）
     * @return 更新结果
     */
    ApiResult<String> updateTaskStatus(Long id, Integer status, String result, String password);


    /**
     * 处理导入数据
     * @param importList 导入数据列表
     * @param currentUser 当前用户
     * @return 处理结果
     */
    int processImportData(List<SmtGeneratePasswordImportVO> importList, String currentUser);
}
