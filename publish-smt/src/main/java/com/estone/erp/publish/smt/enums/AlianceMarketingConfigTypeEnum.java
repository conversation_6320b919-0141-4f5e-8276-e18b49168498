package com.estone.erp.publish.smt.enums;

import com.estone.erp.publish.smt.componet.SingleDiscountConfigParam;
import com.estone.erp.publish.smt.componet.marking.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;

@AllArgsConstructor
public enum AlianceMarketingConfigTypeEnum {

    ALIANCE(1, "联盟配置", PlatformActivityConfigParam.class,"aliexpress_aliance_product_listing"),
    PLATFORM_ACTIVITY(2, "平台活动配置", PlatformActivityConfigParam.class,""),
    SINGLE_DISCOUNT(3, "单品折扣配置", SingleDiscountConfigParam.class,""),
    FULL_REDUCTION_CONFIG(4, "满减活动配置", FullReductionConfigParam.class,""),
    STORE_CODE_CONFIG(5, "店铺code配置", StoreCodeConfigParam.class,""),
    EARLY_BIRD_ACTIVITY(6,"早鸟活动配置", EarlyBirdActivityConfigParam.class,"aliexpress_early_bird_activity");
            ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    @Getter
    private final Class<? extends MarketingConfigParam> jsonClass;

    @Getter
    private final String tableName;

    public static Class<? extends MarketingConfigParam> getJsonClass(Integer value) {
        if (value == null) {
            return null;
        }

        return Arrays.stream(AlianceMarketingConfigTypeEnum.values())
                .filter(item -> item.getCode().equals(value))
                .map(AlianceMarketingConfigTypeEnum::getJsonClass)
                .findFirst()
                .orElse(null);
    }


    public static String getDescByCode(Integer code) {
        if (ObjectUtils.isEmpty(code)){
            return null;
        }
        AlianceMarketingConfigTypeEnum[] values = values();
        for (AlianceMarketingConfigTypeEnum type : values) {
            if (type.code == code) {
                return type.getDesc();
            }
        }
        return null;
    }
}
