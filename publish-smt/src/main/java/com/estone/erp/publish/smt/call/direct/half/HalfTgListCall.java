package com.estone.erp.publish.smt.call.direct.half;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressHalfTgPreItemService;
import com.estone.erp.publish.smt.util.AliexpressContentUtils;
import com.estone.erp.publish.smt.util.HalfPreTgUtils;
import com.estone.erp.publish.smt.util.HalfTgUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.erpCommon.constant.ErpCommonConstant;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * list同步
 */
@Slf4j
public class HalfTgListCall {
    private AliexpressHalfTgItemService aliexpressHalfTgItemService = SpringUtils.getBean(AliexpressHalfTgItemService.class);
    private AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
    private EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
    private AliexpressHalfTgPreItemService aliexpressHalfTgPreItemService = SpringUtils.getBean(AliexpressHalfTgPreItemService.class);
    private EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);

    private String halfTgPrefix = "YSTD";

    private String halfTgSmallPrefix = "ystd";

    /**
     *
     * {"aliexpress_pop_choice_products_list_response":{"result":{"total_item":139,"pop_choice_product_list":{"pop_choice_product":[{"max_sku_price":"15.75","modified_time":"2023-10-30 01:30:59","create_time":"2021-11-02 19:44:48","product_image":"https://ae04.alicdn.com/kf/S09eec8ce862c49e4ad4a24a55c39d188d.jpg","product_id":****************,"min_sku_price":"15.75","total_stocks":"3","product_status":ProductStatusTypeEnum.onSelling.getCode(),"title":"Clamp Meter hp-605a 600A DC/AC Current Clamp Adapter Clamp-On Meter Tester with Test Probes Works with Multimeters","currency_code":"USD"}]},"success":true,"total_page":7,"current_page":1},"request_id":"2102e91216989998432056616"}}
     *
     * 半托管list同步
     * @param saleAccountByAccountNumber
     */
    public String list(SaleAccountAndBusinessResponse saleAccountByAccountNumber, HalfTgSyncProductListRequest listRequest){
        try{
            String listResponse = getListResponse(saleAccountByAccountNumber, listRequest);

            //单个产品同步，判断是否删除
            if(listRequest.getIsSignOff() != null &&listRequest.getIsSignOff() && listRequest.getProductId() != null){
                String aa = "\"success\":true";
                String bb = "\"total_item\":0";
                if(StringUtils.contains(listResponse, aa) && StringUtils.contains(listResponse, bb)){
                    //说明是退出了半托管，改成不在线
                    aliexpressHalfTgItemService.exitUpdateOnlineStatus(saleAccountByAccountNumber.getAccountNumber(), listRequest.getProductId());
                }
            }
            if(StringUtils.isNotBlank(listResponse)){
                int totalPage = getTotalPage(listResponse);
                log.warn(saleAccountByAccountNumber.getAccountNumber() + "总页数：" + totalPage);
                for (int i = 1; i <= totalPage ; i++) {
                    if(i > 1){
                        listRequest.setCurrentPage(i);
                        listResponse = getListResponse(saleAccountByAccountNumber, listRequest);
                    }
                    if(StringUtils.isBlank(listResponse)){
                        continue;
                    }

                    JSONObject jsonObject = JSONObject.parseObject(listResponse);
                    if (jsonObject.containsKey("aliexpress_pop_choice_products_list_response")) {
                        JSONObject rspJson = jsonObject
                                .getJSONObject("aliexpress_pop_choice_products_list_response");
                        if (rspJson.containsKey("result")) {
                            JSONObject obj = rspJson.getJSONObject("result");
                            if (obj.containsKey("pop_choice_product_list")) {
                                JSONObject popListObj = obj.getJSONObject("pop_choice_product_list");
                                if(popListObj.containsKey("pop_choice_product")){
                                    JSONArray popObjList = popListObj.getJSONArray("pop_choice_product");
                                    for (int i1 = 0; i1 < popObjList.size(); i1++) {
                                        JSONObject popObj = popObjList.getJSONObject(i1);
                                        try {
                                            synchProduct(saleAccountByAccountNumber, popObj);
                                        } catch (Exception e) {
                                            log.error(e.getMessage(), e);
                                            if(listRequest.getProductId() != null){
                                                return e.getMessage();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return e.getMessage();
        }
        return null;
    }

    public void synchProduct(SaleAccountAndBusinessResponse saleAccountByAccountNumber, JSONObject popObj){

        AliexpressHalfTgItem baseItem = new AliexpressHalfTgItem();
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
        String articleNumberPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();//货号前缀
        baseItem.setAccount(accountNumber);
        Double max_sku_price = popObj.getDouble("max_sku_price");
        baseItem.setMaxSkuPrice(max_sku_price);
        Double min_sku_price = popObj.getDouble("min_sku_price");
        baseItem.setMinSkuPrice(min_sku_price);
        Timestamp modified_time = popObj.getTimestamp("modified_time");
        baseItem.setModifiedTime(modified_time);
        Timestamp create_time = popObj.getTimestamp("create_time");
        baseItem.setCreateTime(create_time);
        String product_image = popObj.getString("product_image");
        baseItem.setProductImage(product_image);
        Long product_id = popObj.getLong("product_id");
        baseItem.setProductId(product_id);
        Integer total_stocks = popObj.getInteger("total_stocks");
        baseItem.setTotalStocks(total_stocks);
        String product_status = popObj.getString("product_status");
        baseItem.setProductStatus(product_status);
        String title = popObj.getString("title");
        baseItem.setTitle(title);
        String currency_code = popObj.getString("currency_code");
        baseItem.setCurrencyCode(currency_code);

        AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
        halfTgItemExample.createCriteria().andAccountEqualTo(accountNumber).andProductIdEqualTo(product_id);
        halfTgItemExample.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);

        //本地 sku_id Map
        Map<String, List<AliexpressHalfTgItem>> skuIdMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(aliexpressHalfTgItems)){
            skuIdMap = aliexpressHalfTgItems.stream().collect(Collectors.groupingBy(t -> t.getSkuId()));
        }

        List<AliexpressHalfTgItem> createList = new ArrayList<>();
        List<AliexpressHalfTgItem> updateList = new ArrayList<>();
        List<AliexpressHalfTgItem> deleteList = new ArrayList<>();

        List<String> synchSkuId = new ArrayList<>();
        String productResponse = getProductResponse(saleAccountByAccountNumber, product_id);

        JSONObject jsonObject = JSONObject.parseObject(productResponse);
        if(jsonObject == null){
            return;
        }
        if(jsonObject.containsKey("aliexpress_pop_choice_product_query_response")){
            JSONObject aliexpress_pop_choice_product_query_response = jsonObject.getJSONObject("aliexpress_pop_choice_product_query_response");
            if(aliexpress_pop_choice_product_query_response.containsKey("result")){
                JSONObject result = aliexpress_pop_choice_product_query_response.getJSONObject("result");
                if(result.containsKey("pop_choice_product")){
                    JSONObject pop_choice_product = result.getJSONObject("pop_choice_product");

                    Integer category_id = pop_choice_product.getInteger("category_id");
                    baseItem.setCategoryId(category_id);


                    JSONObject joined_country_list = pop_choice_product.getJSONObject("joined_country_list");
                    if(joined_country_list != null && joined_country_list.containsKey("joined_country")){
                        JSONArray joined_country = joined_country_list.getJSONArray("joined_country");
                        baseItem.setJoinedCountryList(joined_country.toJSONString());
                    }
                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.searchAliexpressCategoryByCategoryId(category_id);
                    if(aliexpressCategory != null){
                        baseItem.setFullPathCode(aliexpressCategory.getFullCnName());
                    }
                    JSONObject product_sku_list = pop_choice_product.getJSONObject("product_sku_list");
                    JSONArray product_skuArr = product_sku_list.getJSONArray("product_sku");
                    for (int i = 0; i < product_skuArr.size(); i++) {
                        AliexpressHalfTgItem synchItem = new AliexpressHalfTgItem();
                        BeanUtils.copyProperties(baseItem, synchItem);
                        JSONObject product_sku = product_skuArr.getJSONObject(i);

                        if(product_sku.containsKey("sku_property_list")){
                            JSONObject sku_property_list = product_sku.getJSONObject("sku_property_list");
                            if(sku_property_list != null && sku_property_list.containsKey("sku_property")){
                                JSONArray sku_property = sku_property_list.getJSONArray("sku_property");
                                synchItem.setSkuPropertyList(sku_property.toJSONString());
                            }
                        }

                        Double package_width = product_sku.getDouble("package_width");
                        synchItem.setPackageWidth(package_width);
                        Double package_height = product_sku.getDouble("package_height");
                        synchItem.setPackageHeight(package_height);
                        Double package_length = product_sku.getDouble("package_length");
                        synchItem.setPackageLength(package_length);
                        JSONObject choice_sku_price_list = product_sku.getJSONObject("choice_sku_price_list");
                        if(choice_sku_price_list != null && choice_sku_price_list.containsKey("choice_sku_price")){
                            JSONArray choice_sku_price = choice_sku_price_list.getJSONArray("choice_sku_price");
                            synchItem.setChoiceSkuPriceList(choice_sku_price.toJSONString());
                        }
                        JSONObject freight_fee_list = product_sku.getJSONObject("freight_fee_list");
                        if(freight_fee_list != null && freight_fee_list.containsKey("freight_fee")){
                            JSONArray freight_fee = freight_fee_list.getJSONArray("freight_fee");
                            synchItem.setFreightFeeList(freight_fee.toJSONString());
                        }

                        JSONObject pop_choice_product_sku_sc_item_info = product_sku.getJSONObject("pop_choice_product_sku_sc_item_info");

                        String sc_item_code = pop_choice_product_sku_sc_item_info.getString("sc_item_code");
                        synchItem.setScItemCode(sc_item_code);
                        String sc_item_bar_code = pop_choice_product_sku_sc_item_info.getString("sc_item_bar_code");
                        synchItem.setScItemBarCode(sc_item_bar_code);
                        String original_box = pop_choice_product_sku_sc_item_info.getString("original_box");
                        synchItem.setOriginalBox(original_box);
                        String sc_item_id = pop_choice_product_sku_sc_item_info.getString("sc_item_id");
                        synchItem.setScItemId(sc_item_id);

                        if(pop_choice_product_sku_sc_item_info.containsKey("special_product_type_list")){
                            JSONObject special_product_type_list = pop_choice_product_sku_sc_item_info.getJSONObject("special_product_type_list");
                            if(special_product_type_list != null && special_product_type_list.containsKey("special_product_type")){
                                JSONArray special_product_type = special_product_type_list.getJSONArray("special_product_type");
                                synchItem.setSpecialProductTypeList(special_product_type.toJSONString());
                            }
                        }else{
                            synchItem.setSpecialProductTypeList("");
                        }

                        Double base_price = product_sku.getDouble("base_price");
                        synchItem.setBasePrice(base_price);
                        Double package_weight = product_sku.getDouble("package_weight");
                        synchItem.setPackageWeight(package_weight);

                        int skuStock = 0;
                        JSONObject pop_choice_sku_warehouse_stock_list = product_sku.getJSONObject("pop_choice_sku_warehouse_stock_list");
                        if(pop_choice_sku_warehouse_stock_list != null && pop_choice_sku_warehouse_stock_list.containsKey("pop_choice_sku_warehouse_stock")){
                            JSONArray pop_choice_sku_warehouse_stock = pop_choice_sku_warehouse_stock_list.getJSONArray("pop_choice_sku_warehouse_stock");
                            synchItem.setPopChoiceSkuWarehouseStockList(pop_choice_sku_warehouse_stock.toJSONString());
                            for (int i1 = 0; i1 < pop_choice_sku_warehouse_stock.size(); i1++) {
                                JSONObject jsonObject1 = pop_choice_sku_warehouse_stock.getJSONObject(i1);
                                if(jsonObject1 != null && jsonObject1.containsKey("sellable_quantity")){
                                    int sellable_quantity = jsonObject1.getIntValue("sellable_quantity");
                                    skuStock += sellable_quantity;
                                }
                            }
                        }
                        synchItem.setSkuStock(skuStock);
                        String sku_code = product_sku.getString("sku_code");
                        synchItem.setSkuCode(sku_code);
                        String sku_id = product_sku.getString("sku_id");
                        synchItem.setSkuId(sku_id);
                        synchSkuId.add(sku_id);
                        synchItem.setLastSynchTime(new Timestamp(System.currentTimeMillis()));
                        //解析货号  店铺前缀 或者YSTD
                        String articleNumber = sku_code;
                        if(StringUtils.isNotBlank(sku_code)){
                            if(sku_code.contains(halfTgPrefix)){
                                articleNumber = sku_code.replaceFirst(halfTgPrefix, "");
                            }else if(sku_code.contains(halfTgSmallPrefix)){
                                articleNumber = sku_code.replaceFirst(halfTgSmallPrefix, "");
                            }else{
                                articleNumber = AliexpressContentUtils.getArticleNumber(sku_code, articleNumberPrefix);
                            }
                        }
                        articleNumber = articleNumber.trim().toUpperCase();
                        synchItem.setArticleNumber(articleNumber);
                        // 补充产品系统信息
                        HalfTgUtils.assembleProductInfo(synchItem);

                        //新增产品在线状态
                        synchItem.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());

                        if(skuIdMap.keySet().contains(sku_id)){
                            List<AliexpressHalfTgItem> dbList = skuIdMap.get(sku_id);
                            AliexpressHalfTgItem dbItem = dbList.get(0);
                            synchItem.setSkuBind(dbItem.getSkuBind());
                            synchItem.setId(dbItem.getId());

                            String dbArticleNumber = dbItem.getArticleNumber();
                            //货号不同需要置空之前的库存值重新获取
                            if(!StringUtils.equals(dbArticleNumber, articleNumber)){
                                EsSkuBind esSkuBind = esSkuBindService.getEsSkuBind(sku_id, Platform.Smt.name());
                                if(esSkuBind != null && StringUtils.isNotBlank(esSkuBind.getBindSku())){
                                    esSkuBind.setSku(articleNumber);
                                    esSkuBindService.save(esSkuBind);
                                }
                                dbItem.setUsableStock(null);
                                aliexpressHalfTgItemService.updateBySkuChange(synchItem);
                            }

                            if(dbItem.getUsableStock() == null){
                                try {
                                    //smt中转
                                    Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                    //可用
                                    Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
                                    //可用-待发
                                    Integer systemStock = SkuStockUtils.getSkuStockToEbay(articleNumber);
                                    //可用+中转-待发
                                    Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                    if (null == avableStock) {
                                        log.info(String.format(" 更新listing系统库存失败,%s 查询redis库存为空", articleNumber));
                                    } else{
                                        synchItem.setSmtTransferStock(transferStockForRedis);
                                        synchItem.setUsableStock(avableStock);
                                        synchItem.setSystemStock(systemStock);
                                        synchItem.setSystemUsableTransferStock(skuTransferStock);
                                        synchItem.setUpdateSystemStockDate(new Timestamp(System.currentTimeMillis()));
                                    }
                                } catch (Exception e) {
                                    log.error(String.format(" 更新listing系统库存失败,%s ", articleNumber));
                                }
                            }
                            updateList.add(synchItem);
                        }else{
                            try {
                                //smt中转
                                Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                //可用
                                Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
                                //可用-待发
                                Integer systemStock = SkuStockUtils.getSkuStockToEbay(articleNumber);
                                //可用+中转-待发
                                Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                if (null == avableStock) {
                                    log.info(String.format(" 更新listing系统库存失败,%s 查询redis库存为空", articleNumber));
                                } else{
                                    synchItem.setSmtTransferStock(transferStockForRedis);
                                    synchItem.setUsableStock(avableStock);
                                    synchItem.setSystemStock(systemStock);
                                    synchItem.setSystemUsableTransferStock(skuTransferStock);
                                    synchItem.setUpdateSystemStockDate(new Timestamp(System.currentTimeMillis()));
                                }
                            } catch (Exception e) {
                                log.error(String.format(" 更新listing系统库存失败,%s ", articleNumber));
                            }

                            createList.add(synchItem);
                        }
                    }//多属性 end
                }
            } //result end
        }

        //只用同步到产品 才删除数据
        if(CollectionUtils.isNotEmpty(synchSkuId)){
            List<String> dbSkuIdList = new ArrayList<>(skuIdMap.keySet());
            dbSkuIdList.removeAll(synchSkuId);
            //需要删除的数据
            if(CollectionUtils.isNotEmpty(dbSkuIdList)){
                for (String dbSkuId : dbSkuIdList) {
                    deleteList.addAll(skuIdMap.get(dbSkuId));
                }
            }
            if(CollectionUtils.isNotEmpty(deleteList)){
                aliexpressHalfTgItemService.deleteByPrimaryKey(deleteList.stream().map(t->t.getId()).collect(Collectors.toList()));
            }
        }

        if(CollectionUtils.isNotEmpty(updateList)){
            aliexpressHalfTgItemService.batchuUdate(updateList);
        }
        if(CollectionUtils.isNotEmpty(createList)){
            List<EsSkuBind> createTSkuBindList = new ArrayList<>();
            for (AliexpressHalfTgItem aliexpressHalfTgItem : createList) {
                String skuId = aliexpressHalfTgItem.getSkuId();
                //扫描的数据都设置是
                aliexpressHalfTgItem.setSkuBind(true);

                //判断是否存在 如果存在不用创建
                EsSkuBind esSkuBind = esSkuBindService.getEsSkuBind(skuId, Platform.Smt.name());
                if(esSkuBind != null && StringUtils.isNotBlank(esSkuBind.getBindSku())){
                    continue;
                }
                EsSkuBind createTSkuBind = new EsSkuBind();
                createTSkuBindList.add(createTSkuBind);
                createTSkuBind.setId(Platform.Smt.name() + "_" + skuId);
                createTSkuBind.setSku(aliexpressHalfTgItem.getArticleNumber());
                createTSkuBind.setBindSku(skuId);
                createTSkuBind.setPlatform(Platform.Smt.name());
                createTSkuBind.setSellerId(aliexpressHalfTgItem.getAccount());
                createTSkuBind.setSkuDataSource(SkuDataSourceEnum.SMT_HALF.getCode());
                createTSkuBind.setCreateDate(new Date());
            }
            try {
                aliexpressHalfTgItemService.batchInsert(createList);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if(e.getMessage().contains("Data too long")){
                    List<String> skuList = createList.stream().map(t -> t.getArticleNumber()).collect(Collectors.toList());
                    log.error(createList.get(0).getAccount() + "字段超长的货号:" + StringUtils.join(skuList, ","));
                }
                if(e.getMessage().contains("Data too long") && e.getMessage().contains("forbid_channel")){
                    for (AliexpressHalfTgItem aliexpressHalfTgItem : createList) {
                        String forbidChannel = aliexpressHalfTgItem.getForbidChannel();
                        String articleNumber = aliexpressHalfTgItem.getArticleNumber();
                        log.error(aliexpressHalfTgItem.getAccount() + "字段超长的货号:" + articleNumber + " forbid_channel:" + forbidChannel);
                    }
                }
            }
            esSkuBindService.saveAll(createTSkuBindList, Platform.Smt.name());
        }
    }


    /**
     * 可加入数据同步
     * @param saleAccountByAccountNumber
     * @param listRequest
     * @return
     */
    public String preList(SaleAccountAndBusinessResponse saleAccountByAccountNumber, HalfTgSyncProductListRequest listRequest){
        // 用线程安全的链表记录同步的ItemId
        ConcurrentLinkedQueue<Long> syncItemIdQueue = new ConcurrentLinkedQueue<>();
        try{
            String listResponse = getListResponse(saleAccountByAccountNumber, listRequest);
            if(StringUtils.isNotBlank(listResponse)){
                int totalPage = getTotalPage(listResponse);
                log.warn(saleAccountByAccountNumber.getAccountNumber() + "总页数：" + totalPage);
                for (int i = 1; i <= totalPage ; i++) {
                    if(i > 1){
                        listRequest.setCurrentPage(i);
                        listResponse = getListResponse(saleAccountByAccountNumber, listRequest);
                    }
                    if(StringUtils.isBlank(listResponse)){
                        continue;
                    }

                    JSONObject jsonObject = JSONObject.parseObject(listResponse);
                    if (jsonObject.containsKey("aliexpress_pop_choice_products_list_response")) {
                        JSONObject rspJson = jsonObject
                                .getJSONObject("aliexpress_pop_choice_products_list_response");
                        if (rspJson.containsKey("result")) {
                            JSONObject obj = rspJson.getJSONObject("result");
                            if (obj.containsKey("pop_choice_product_list")) {
                                JSONObject popListObj = obj.getJSONObject("pop_choice_product_list");
                                if(popListObj.containsKey("pop_choice_product")){
                                    JSONArray popObjList = popListObj.getJSONArray("pop_choice_product");
                                    for (int i1 = 0; i1 < popObjList.size(); i1++) {
                                        JSONObject popObj = popObjList.getJSONObject(i1);
                                        Long product_id = popObj.getLong("product_id");
                                        String product_status = popObj.getString("product_status");
                                        //只用同步在线和审核状态的pop产品
                                        List<String> passStatus = Arrays.asList(ProductStatusTypeEnum.onSelling.getCode(), ProductStatusTypeEnum.auditing.getCode());
                                        if(StringUtils.isNotBlank(product_status) && passStatus.contains(product_status)){
                                            syncItemIdQueue.add(product_id);
                                            try {
                                                synchPreProduct(saleAccountByAccountNumber, popObj);
                                            } catch (Exception e) {
                                                log.error(e.getMessage(), e);
                                                if(listRequest.getProductId() != null){
                                                    return e.getMessage();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return e.getMessage();
        }

        //删除平台未返回的产品id数据
        List<Long> productIdList = syncItemIdQueue.stream().distinct().collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(productIdList)){
            AliexpressHalfTgPreItemExample preItemExample = new AliexpressHalfTgPreItemExample();
            AliexpressHalfTgPreItemExample.Criteria criteria = preItemExample.createCriteria();
            criteria.andAccountEqualTo(saleAccountByAccountNumber.getAccountNumber());
            criteria.andProductIdNotIn(productIdList);
            List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems = aliexpressHalfTgPreItemService.selectByExample(preItemExample);
            if(CollectionUtils.isNotEmpty(aliexpressHalfTgPreItems)){
                List<Long> longList = aliexpressHalfTgPreItems.stream().map(t -> t.getId()).collect(Collectors.toList());
                aliexpressHalfTgPreItemService.deleteByPrimaryKey(longList);
            }
        }
        return null;
    }

    /**
     * 待加入产品数据
     * @param saleAccountByAccountNumber
     * @param popObj
     */
    public void synchPreProduct(SaleAccountAndBusinessResponse saleAccountByAccountNumber, JSONObject popObj){
        AliexpressHalfTgPreItem baseItem = new AliexpressHalfTgPreItem();
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
        baseItem.setAccount(accountNumber);
        Double max_sku_price = popObj.getDouble("max_sku_price");
        baseItem.setMaxSkuPrice(max_sku_price);
        Double min_sku_price = popObj.getDouble("min_sku_price");
        baseItem.setMinSkuPrice(min_sku_price);
        Timestamp modified_time = popObj.getTimestamp("modified_time");
        baseItem.setModifiedTime(modified_time);
        Timestamp create_time = popObj.getTimestamp("create_time");
        baseItem.setCreateTime(create_time);
        String product_image = popObj.getString("product_image");
        baseItem.setProductImage(product_image);
        Long product_id = popObj.getLong("product_id");
        baseItem.setProductId(product_id);
        String product_status = popObj.getString("product_status");
        baseItem.setProductStatus(product_status);
        String title = popObj.getString("title");
        baseItem.setTitle(title);
        String currency_code = popObj.getString("currency_code");
        baseItem.setCurrencyCode(currency_code);

        //同步时间
        baseItem.setLastSynchTime(new Timestamp(System.currentTimeMillis()));
        baseItem.setItemStatus(0);

        Map<String, AliexpressHalfTgPreItem> dbProductIdSkuIdMap = new HashMap<>();
        List<String> dbSkuIdList = new ArrayList<>();
        //本地数据
        AliexpressHalfTgPreItemExample itemExample = new AliexpressHalfTgPreItemExample();
        itemExample.createCriteria().andProductIdEqualTo(product_id);
        List<AliexpressHalfTgPreItem> dbPreItemList = aliexpressHalfTgPreItemService.selectByExample(itemExample);
        if(CollectionUtils.isNotEmpty(dbPreItemList)){
            for (AliexpressHalfTgPreItem aliexpressHalfTgPreItem : dbPreItemList) {
                Long productId = aliexpressHalfTgPreItem.getProductId();
                String skuId = StringUtils.isNotBlank(aliexpressHalfTgPreItem.getSkuId()) ? aliexpressHalfTgPreItem.getSkuId() : "";
                dbSkuIdList.add(skuId);
                dbProductIdSkuIdMap.put(productId + skuId, aliexpressHalfTgPreItem);
            }
        }

        EsAliexpressProductListingRequest esAliexpressProductListingRequest = new EsAliexpressProductListingRequest();
        esAliexpressProductListingRequest.setProductId(product_id);
        esAliexpressProductListingRequest.setQueryFields(null);
        //查询所有在线状态
        esAliexpressProductListingRequest.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(esAliexpressProductListingRequest);
        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
            List<String> esSkuIdList = esAliexpressProductListing.stream().map(t -> t.getSkuId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(dbSkuIdList)){
                //全部新增
                for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                    AliexpressHalfTgPreItem synchPreItem = new AliexpressHalfTgPreItem();
                    BeanUtils.copyProperties(baseItem, synchPreItem);
                    Long productId = aliexpressProductListing.getProductId();
                    String skuId = aliexpressProductListing.getSkuId();
                    String articleNumber = aliexpressProductListing.getArticleNumber();
                    String skuCode = aliexpressProductListing.getSkuCode();
                    Integer categoryId = aliexpressProductListing.getCategoryId();

                    articleNumber = articleNumber.trim().toUpperCase();
                    synchPreItem.setArticleNumber(articleNumber);
                    synchPreItem.setSkuCode(skuCode);
                    synchPreItem.setSkuId(skuId);
                    synchPreItem.setProductId(productId);
                    synchPreItem.setCategoryId(categoryId);

                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.searchAliexpressCategoryByCategoryId(categoryId);
                    if(aliexpressCategory != null){
                        synchPreItem.setFullPathCode(aliexpressCategory.getFullCnName());
                    }

                    synchPreItem.setProductprice(aliexpressProductListing.getSkuPrice());
                    HalfPreTgUtils.assembleProductInfo(synchPreItem);

                    try {
                        //smt中转
                        Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                        //可用
                        Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
                        //可用+中转-待发
                        Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                        if (avableStock != null) {
                            synchPreItem.setSmtTransferStock(transferStockForRedis);
                            synchPreItem.setUsableStock(avableStock);
                            synchPreItem.setSystemUsableTransferStock(skuTransferStock);
                            synchPreItem.setUpdateSystemStockDate(new Timestamp(new Date().getTime()));
                        }
                    } catch (Exception e) {
                        log.error(String.format(" 更新listing系统库存失败,%s ", articleNumber));
                    }
                    aliexpressHalfTgPreItemService.insert(synchPreItem);
                }
            }else{
                for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                    AliexpressHalfTgPreItem synchPreItem = new AliexpressHalfTgPreItem();
                    BeanUtils.copyProperties(baseItem, synchPreItem);
                    Long productId = aliexpressProductListing.getProductId();
                    String skuId = aliexpressProductListing.getSkuId();
                    //本地存在就不用处理
                    if(dbSkuIdList.contains(skuId)){
                        AliexpressHalfTgPreItem aliexpressHalfTgPreItem = dbProductIdSkuIdMap.get(productId + skuId);
                        if(aliexpressHalfTgPreItem.getUsableStock() == null){
                            String articleNumber = aliexpressHalfTgPreItem.getArticleNumber();
                            try {
                                //smt中转
                                Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                //可用
                                Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
                                //可用+中转-待发
                                Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                                if (avableStock != null) {
                                    aliexpressHalfTgPreItem.setSmtTransferStock(transferStockForRedis);
                                    aliexpressHalfTgPreItem.setUsableStock(avableStock);
                                    aliexpressHalfTgPreItem.setSystemUsableTransferStock(skuTransferStock);
                                    aliexpressHalfTgPreItem.setUpdateSystemStockDate(new Timestamp(new Date().getTime()));
                                }
                            } catch (Exception e) {
                                log.error(String.format(" 更新listing系统库存失败,%s ", articleNumber));
                            }
                        }
                        aliexpressHalfTgPreItem.setLastSynchTime(new Timestamp(System.currentTimeMillis()));
                        aliexpressHalfTgPreItemService.updateByPrimaryKeySelective(aliexpressHalfTgPreItem);
                        continue;
                    }

                    //新增数据
                    String articleNumber = aliexpressProductListing.getArticleNumber();
                    String skuCode = aliexpressProductListing.getSkuCode();
                    Integer categoryId = aliexpressProductListing.getCategoryId();

                    synchPreItem.setArticleNumber(articleNumber.trim().toUpperCase());
                    synchPreItem.setSkuCode(skuCode);
                    synchPreItem.setSkuId(skuId);
                    synchPreItem.setProductId(productId);
                    synchPreItem.setCategoryId(categoryId);

                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.searchAliexpressCategoryByCategoryId(categoryId);
                    if(aliexpressCategory != null){
                        synchPreItem.setFullPathCode(aliexpressCategory.getFullCnName());
                    }

                    synchPreItem.setProductprice(aliexpressProductListing.getSkuPrice());
                    HalfPreTgUtils.assembleProductInfo(synchPreItem);
                    try {
                        //smt中转
                        Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                        //可用
                        Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
                        //可用+中转-待发
                        Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                        if (avableStock != null) {
                            synchPreItem.setSmtTransferStock(transferStockForRedis);
                            synchPreItem.setUsableStock(avableStock);
                            synchPreItem.setSystemUsableTransferStock(skuTransferStock);
                            synchPreItem.setUpdateSystemStockDate(new Timestamp(new Date().getTime()));
                        }
                    } catch (Exception e) {
                        log.error(String.format(" 更新listing系统库存失败,%s ", articleNumber));
                    }
                    aliexpressHalfTgPreItemService.insert(synchPreItem);
                }
                //本地有的数据 es已经不存在就去除
                dbSkuIdList.removeAll(esSkuIdList);
                if(CollectionUtils.isNotEmpty(dbSkuIdList)){
                    List<Long> deleteIdList = new ArrayList<>();
                    //删除
                    for (String dbSkuId : dbSkuIdList) {
                        AliexpressHalfTgPreItem aliexpressHalfTgPreItem = dbProductIdSkuIdMap.get(product_id + dbSkuId);
                        deleteIdList.add(aliexpressHalfTgPreItem.getId());
                    }
                    aliexpressHalfTgPreItemService.deleteByPrimaryKey(deleteIdList);
                }
            }
        }else{
            //没有es数据，也没有本地数据，新增一条产品数据
            if(CollectionUtils.isEmpty(dbPreItemList)){
                aliexpressHalfTgPreItemService.insert(baseItem);
            }
        }
    }

    /**
     * {"aliexpress_pop_choice_product_query_response":{"result":{"pop_choice_product":{"product_sku_list":{"product_sku":[{"package_width":"14.0","package_height":"6.0","choice_sku_price_list":{"choice_sku_price":[{"price":"20.80","nation_name":"BR"},{"price":"20.00","nation_name":"PL"},{"price":"20.21","nation_name":"UK"},{"price":"21.66","nation_name":"NL"},{"price":"21.10","nation_name":"IT"},{"price":"21.41","nation_name":"PT"},{"price":"21.35","nation_name":"BE"},{"price":"22.94","nation_name":"LT"},{"price":"23.68","nation_name":"NO"},{"price":"21.44","nation_name":"HU"},{"price":"22.30","nation_name":"RO"}]},"package_length":"22.0","freight_fee_list":{"freight_fee":[{"price":"4.25","nation_code":"PL"},{"price":"4.46","nation_code":"UK"},{"price":"5.05","nation_code":"BR"},{"price":"5.35","nation_code":"IT"},{"price":"5.60","nation_code":"BE"},{"price":"5.66","nation_code":"PT"},{"price":"5.69","nation_code":"HU"},{"price":"5.91","nation_code":"NL"},{"price":"6.55","nation_code":"RO"},{"price":"7.19","nation_code":"LT"},{"price":"7.93","nation_code":"NO"}]},"pop_choice_product_sku_sc_item_info":{"sc_item_code":"YSTD7AK700191","sc_item_bar_code":"YSTD7AK700191","original_box":"0","sc_item_id":745401851436},"base_price":"15.75","package_weight":"0.318","sku_id":"*****************","pop_choice_sku_warehouse_stock_list":{"pop_choice_sku_warehouse_stock":[{"warehouse_name":"东莞JIT库存","warehouse_code":"DGU002-JIT","sellable_quantity":3}]},"sku_code":"YSTD7AK700191"}]},"category_id":********,"product_id":****************,"joined_country_list":{"joined_country":["BR","PL","UK","NL","IT","PT","BE","LT","NO","HU","RO"]},"currency_code":"USD"},"success":true},"request_id":"2102fd2216990003375626746"}}
     * @param saleAccountByAccountNumber
     * @param productId
     * @return
     */
    public String getProductResponse(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId){
        if (saleAccountByAccountNumber == null || productId == null) {
            return null;
        }
        String callRspStr = "";
        try{
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.pop.choice.product.query");
            request.addApiParameter("product_id", productId.toString());
            request.addApiParameter("language", "zh_CN");
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            callRspStr = iopResponse.getBody();
            return callRspStr;
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取list 响应数据
     * @param saleAccountByAccountNumber
     * @param listRequest
     * @return
     */
    public String getListResponse(SaleAccountAndBusinessResponse saleAccountByAccountNumber, HalfTgSyncProductListRequest listRequest){
        if (saleAccountByAccountNumber == null || StringUtils.isBlank(saleAccountByAccountNumber.getAccountNumber())
                || StringUtils.isBlank(saleAccountByAccountNumber.getAccessToken()) || listRequest == null
                || listRequest.getCurrentPage() == null || listRequest.getPageSize() == null
        ) {
            return null;
        }
        String body = null;
        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.pop.choice.products.list");
            request.setHttpMethod("GET");
            request.addApiParameter("param", JSON.toJSONString(listRequest));
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            body = iopResponse.getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return body;
    }

    private int getTotalPage(String response) {
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.containsKey("aliexpress_pop_choice_products_list_response")) {
                JSONObject rspJson = jsonObject
                        .getJSONObject("aliexpress_pop_choice_products_list_response");
                if (rspJson.containsKey("result")) {
                    JSONObject obj = rspJson.getJSONObject("result");
                    if (obj.containsKey("total_page")) {
                        return obj.getIntValue("total_page");
                    }
                }
            }
        }
        return 0;
    }
}
