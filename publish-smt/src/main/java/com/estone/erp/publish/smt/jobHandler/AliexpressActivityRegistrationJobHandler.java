package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.job.executor.core.config.XxlJobConfig;
import com.estone.erp.publish.smt.componet.marking.PlatformActivityConfigParam;
import com.estone.erp.publish.smt.enums.ActivityConfirmationStatusEnum;
import com.estone.erp.publish.smt.enums.ActivityUploadStatusEnum;
import com.estone.erp.publish.smt.enums.AlianceMarketingConfigTypeEnum;
import com.estone.erp.publish.smt.mapper.AliexpressMarketingConfigMapper;
import com.estone.erp.publish.smt.model.AliexpressMarketingConfig;
import com.estone.erp.publish.smt.model.AliexpressMarketingConfigExample;
import com.estone.erp.publish.smt.mq.excel.constant.ExcelConstant;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressActivityRegistrationMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 活动配置
 * <AUTHOR>
 * @Date 2024/7/8 下午3:37
 */
@Slf4j
@Component
public class AliexpressActivityRegistrationJobHandler extends AbstractJobHandler {

    /**
     * 数据列表
     */
    private List<Map<Integer, String>> dataList = new ArrayList<>();

    /**
     * itemID productIdIndex
     */
    private int productIdIndex = -1;

    /**
     * 商品库存角标 currentStock
     */
    private int currentStockIndex = -1;

    /**
     * 买家限购角标 buyLimit
     */
    private int buyLimitIndex = -1;

    /**
     * 活动报名库存角标 applyStock
     */
    private int applyStockIndex = -1;

    /**
     * 报名角标 registration
     */
    private int registrationIndex = -1;

    /**
     * 旧报名角标 registration
     */
    private int lowRegistrationIndex = -1;

    /**
     * 报名文件数
     */
    private int registrationFileCount = 0;

    /**
     * 半托管集合
     */
    private List<Integer> semiManagedIndexList = new ArrayList<>();

    /**
     * 非半托管集合
     */
    private List<Integer> noSemiManagedIndexList = new ArrayList<>();

    @Autowired
    private AliexpressMarketingConfigMapper aliexpressMarketingConfigMapper;

    @Autowired
    private AliexpressActivityRegistrationMapper aliexpressActivityRegistrationMapper;


    public AliexpressActivityRegistrationJobHandler(XxlJobConfig xxlJobConfig) {
        super(AliexpressActivityRegistrationJobHandler.class.getName());
    }

    @Data
    public static class InnerParam {
        private List<String> accountList;
    }

    @XxlJob("AliexpressActivityRegistrationJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        AliexpressActivityRegistrationJobHandler.InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, AliexpressActivityRegistrationJobHandler.InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new AliexpressActivityRegistrationJobHandler.InnerParam();
        }

        XxlJobLogger.log("AliexpressActivityRegistrationJobHandler start");
        int currentPage = 0;
        int pageSize = 1000;
        while (true) {
            // 查询词频日志(获取未生成报名文件且未报名且未确认报名的报名或爬虫首次同步数据状态为null的数据)
            AliexpressActivityRegistrationExample registrationExample = new AliexpressActivityRegistrationExample();
            AliexpressActivityRegistrationExample.Criteria criteria = registrationExample.createCriteria();
            criteria.andGeneratedFileIsNull().andConfirmationStatusEqualOrIsNull(0).andRegistrationStatusEqualTo(0);
            List<String> accountList = innerParam.getAccountList();
            if (CollectionUtils.isNotEmpty(accountList)) {
                criteria.andAccountNumberIn(accountList);
            } else {
                registrationExample.or().andProCountIsNull();
            }
            registrationExample.setLimit(pageSize);
            registrationExample.setOffset((currentPage++) * pageSize);
            List<AliexpressActivityRegistration> aliexpressActivityRegistrations = aliexpressActivityRegistrationMapper.selectByExample(registrationExample);
            if (CollectionUtils.isEmpty(aliexpressActivityRegistrations)) {
                break;
            }

            // 店铺分组
            Map<String, List<AliexpressActivityRegistration>> accountNumberMap = aliexpressActivityRegistrations.stream().collect(Collectors.groupingBy(AliexpressActivityRegistration::getAccountNumber));
            accountNumberMap.forEach((accountNumber, activityRegistrations) -> {
                // AliexpressExecutors.generatePlatformActivityFiles(() -> {
                XxlJobLogger.log("开始处理店铺[{}]的活动报名数据,共[{}]条数据", accountNumber, activityRegistrations.size());
                for (AliexpressActivityRegistration activityRegistration : activityRegistrations) {
                    String fileUrl = activityRegistration.getRegistrationTemplate();
                    try {
                        InputStream inputStream = downloadFileFromURL(fileUrl);
                        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {

                            @Override
                            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                                // 第一行获取表头，并定位到具体需要判断的列
                                if (context.readRowHolder().getRowIndex() == 0) {
                                    List<String> headers = new ArrayList<>(data.values());
                                    for (int i = 0; i < headers.size(); i++) {
                                        String header = headers.get(i);

                                        // itemId
                                        if (header.contains("(itemId)")) {
                                            productIdIndex = i;
                                        }

                                        // 现有商品库存
                                        if (header.contains("(currentStock)")) {
                                            currentStockIndex = i;
                                        }

                                        // 买家限购
                                        if (header.contains("(buyLimit)")) {
                                            buyLimitIndex = i;
                                        }

                                        // 活动报名库存
                                        if (header.contains("(applyStock)")) {
                                            applyStockIndex = i;
                                        }

                                        // 是否报名
                                        if (header.contains("(isApply)")) {
                                            registrationIndex = i;
                                        }

                                        // SKU折扣率
                                        if (header.contains("SKU折扣率") || header.contains("(itemDiscountOff)")) {
                                            if (header.contains("(semi_hostSkuDiscountOff)") || header.contains("(semiHostItemDiscountOff)")) {
                                                semiManagedIndexList.add(i);
                                            } else {
                                                noSemiManagedIndexList.add(i);
                                            }
                                        }
                                    }
                                }
                                if (context.readRowHolder().getRowIndex() > 1) {
                                    List<String> columns = new ArrayList<>(data.values());

                                    // 判断是否需要修改库存信息(判断是否有现有库存列、买家限购列、报名库存列)
                                    if (currentStockIndex != -1 && applyStockIndex != -1) {
                                        data.put(applyStockIndex, columns.get(currentStockIndex));
                                        // 买家限购角标不等于空，且当前库存为0，则设置买家限购为0
                                        if (buyLimitIndex != -1) {
                                            if ("0".equals(columns.get(currentStockIndex))) {
                                                data.put(buyLimitIndex, "0");
                                            } else {
                                                data.put(buyLimitIndex, "1");
                                            }
                                        }
                                    }
                                }
                                // 添加到dataList中
                                dataList.add(data);
                            }

                            @Override
                            public void doAfterAllAnalysed(AnalysisContext context) {
                                try {
                                    // 如果存在副本文件则不生成生文件
                                    String generatedFile = activityRegistration.getGeneratedFile();
                                    if (StringUtils.isNotBlank(generatedFile)) {
                                        try {
                                            // 生成文件读取行数，作为报名产品数量
                                            InputStream inputStream = downloadFileFromURL(generatedFile);
                                            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                                                @Override
                                                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                                                    if (context.readRowHolder().getRowIndex() == 0) {
                                                        List<String> headers = new ArrayList<>(data.values());
                                                        for (int i = 0; i < headers.size(); i++) {
                                                            String header = headers.get(i);
                                                            // 是否报名
                                                            if (header.contains("(isApply)")) {
                                                                lowRegistrationIndex = i;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                    if (context.readRowHolder().getRowIndex() > 1) {
                                                        List<String> columns = new ArrayList<>(data.values());
                                                        if (columns.get(lowRegistrationIndex).equals("Y")) {
                                                            registrationFileCount++;
                                                        }
                                                    }
                                                }

                                                @Override
                                                public void doAfterAllAnalysed(AnalysisContext context) {

                                                }
                                            }).headRowNumber(0).sheet().doRead();
                                        } catch (IOException e) {
                                            XxlJobLogger.log("生成报名文件失败,原因: {}", e.getMessage());
                                        }
                                    } else {
                                        // 避免表格中只有表头数据
                                        if (dataList.size() > 2) {
                                            updateRegistrationStatusByDataList(activityRegistration);
                                        }
                                    }

                                    // 更新平台活动报名信息
                                    String newFileUrl = StringUtils.isNotBlank(generatedFile) ? null : fileUrl;
                                    updateActivityRegistration(newFileUrl, activityRegistration);

                                } catch (Exception e) {
                                    activityRegistration.setProCount(dataList.size() > 2 ? (dataList.size() - 2) : 0);
                                    activityRegistration.setConfirmationStatus(0);
                                    activityRegistration.setGenerationFailureReason(e.getMessage());
                                    aliexpressActivityRegistrationMapper.updateByPrimaryKeySelective(activityRegistration);
                                } finally {
                                    // 数据处理完后清理并还原变量
                                    reducingVariable();
                                }
                            }
                        }).headRowNumber(0).sheet().doRead();
                    } catch (Exception e) {
                        activityRegistration.setProCount(dataList.size() > 2 ? (dataList.size() - 2) : 0);
                        activityRegistration.setConfirmationStatus(0);
                        activityRegistration.setGenerationFailureReason(String.format("生成报名文件失败,原因: %s", e.getMessage()));
                        aliexpressActivityRegistrationMapper.updateByPrimaryKeySelective(activityRegistration);
                    }
                }
                //});
            });
        }

        XxlJobLogger.log("AliexpressActivityRegistrationJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 修改文档报名状态
     *
     * @param activityRegistration
     */
    private void updateRegistrationStatusByDataList(AliexpressActivityRegistration activityRegistration) {
        // 获取平台活动配置参数
        Map<String, Double> platformActivityConfigParam = getPlatformActivityConfigParam(activityRegistration);

        // 遍历dataList,修改报名状态
        dataList.stream().skip(2).forEach(columns -> {
            // 判断是否需要报名
            boolean shouldApply = false;
            // 非半托管活动类型
            if (activityRegistration.getActivityType().equals(0)) {
                // 检查是否有国家折扣率列
                if (noSemiManagedIndexList.isEmpty()) {
                    // 无需对比国家折扣率
                    shouldApply = true;
                } else {
                    // 判断是否所有非半托管国家折扣率都满足条件
                    boolean allNonSemiManagedRatesMatch = true;
                    for (Integer index : noSemiManagedIndexList) {
                        String value = columns.get(index);

                        // 检查是否为异常数据
                        if (value.contains("-")) {
                            continue;
                        }
                        Double nonSemiReservedProfitD = platformActivityConfigParam.get("nonSemiReservedProfit");
                        BigDecimal nonSemiReservedProfit = new BigDecimal(String.valueOf(nonSemiReservedProfitD)).setScale(2, RoundingMode.UP);
                        BigDecimal countryDiscountRate = BigDecimal.valueOf(Double.parseDouble(value.replace("%", ""))).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP);
                        XxlJobLogger.log("非半托管活动类型,product:{},非半托管-活动报名折扣率:{},countryDiscountRate:{}", columns.get(productIdIndex), nonSemiReservedProfit, countryDiscountRate);
                        // 如果有任何一个国家折扣率不满足条件，则不报名
                        if (nonSemiReservedProfit.compareTo(countryDiscountRate) < 0) {
                            allNonSemiManagedRatesMatch = false;
                            break;
                        }
                    }

                    // a.非半托管-活动报名折扣率>=所有国家折扣率时，是否报名填写Y
                    shouldApply = allNonSemiManagedRatesMatch;
                }

                // 设置报名状态
                columns.put(registrationIndex, shouldApply ? "Y" : "N");

                // 如果报名状态为Y，则设置活动报名库存=现有库存；否则清空活动报名库存
                if (currentStockIndex != -1 && applyStockIndex != -1) {
                    if (shouldApply) {
                        columns.put(applyStockIndex, columns.get(currentStockIndex));
                    } else {
                        columns.put(applyStockIndex, "");
                    }
                }
            }

            // 半托管活动类型
            if (activityRegistration.getActivityType().equals(1)) {
                // 判断是否所有非半托管国家折扣率都满足条件
                boolean allNonSemiManagedRatesMatch = true;
                // 如果不存在非半托管国家折扣率，则无需对比
                if (!noSemiManagedIndexList.isEmpty()) {
                    for (Integer index : noSemiManagedIndexList) {
                        String value = columns.get(index);

                        // 检查是否为异常数据
                        if (value.contains("-")) {
                            continue;
                        }

                        // 利润率A-X%
                        Double nonSemiReservedProfitD = platformActivityConfigParam.get("nonSemiReservedProfit");
                        BigDecimal nonSemiReservedProfit = new BigDecimal(String.valueOf(nonSemiReservedProfitD)).setScale(2, RoundingMode.UP);
                        BigDecimal countryDiscountRate = BigDecimal.valueOf(Double.parseDouble(value.replace("%", "")))
                                .divide(new BigDecimal(100)).setScale(2, RoundingMode.UP);
                        XxlJobLogger.log("半托管活动类型,product:{},非半托管-活动报名折扣率:{},countryDiscountRate:{}", columns.get(productIdIndex), nonSemiReservedProfit, countryDiscountRate);
                        // 如果有任何一个国家折扣率不满足条件，则不报名
                        if (nonSemiReservedProfit.compareTo(countryDiscountRate) < 0) {
                            allNonSemiManagedRatesMatch = false;
                            break;
                        }
                    }
                }

                // 判断是否所有半托管国家折扣率都满足条件
                boolean allSemiManagedRatesMatch = true;
                // 如果不存在半托管国家折扣率，则无需对比
                if (!semiManagedIndexList.isEmpty()) {
                    for (Integer index : semiManagedIndexList) {
                        String value = columns.get(index);

                        // 检查是否为异常数据
                        if (value.contains("-")) {
                            continue;
                        }
                        Double semiReservedProfitD = platformActivityConfigParam.get("semiReservedProfit");
                        BigDecimal semiReservedProfit = new BigDecimal(String.valueOf(semiReservedProfitD)).setScale(2, RoundingMode.UP);
                        BigDecimal countryDiscountRate = BigDecimal.valueOf(Double.parseDouble(value.replace("%", "")))
                                .divide(new BigDecimal(100)).setScale(2, RoundingMode.UP);

                        XxlJobLogger.log("半托管活动类型,product:{},半托管-活动报名折扣率:{},countryDiscountRate:{}", columns.get(productIdIndex), semiReservedProfit, countryDiscountRate);
                        // 如果有任何一个国家折扣率不满足条件，则不报名
                        if (semiReservedProfit.compareTo(countryDiscountRate) < 0) {
                            allSemiManagedRatesMatch = false;
                            break;
                        }
                    }
                }

                // a.非半托管-活动报名折扣率>=所有非半托管国家折扣率且半托管-活动报名折扣率>=所有半托管国家折扣率时，是否报名填写Y
                shouldApply = allNonSemiManagedRatesMatch && allSemiManagedRatesMatch;

                // 检查库存：如果库存为0，则不报名
                if (currentStockIndex != -1 && Integer.valueOf(columns.get(currentStockIndex)) <= 0) {
                    shouldApply = false;
                }

                // 设置报名状态
                columns.put(registrationIndex, shouldApply ? "Y" : "N");

                // 如果报名状态为Y，则设置活动报名库存=现有库存；否则清空活动报名库存
                if (currentStockIndex != -1 && applyStockIndex != -1) {
                    if (shouldApply) {
                        columns.put(applyStockIndex, columns.get(currentStockIndex));
                    } else {
                        columns.put(applyStockIndex, "");
                    }
                }
            }
        });
    }

    /**
     * 更新平台活动报名信息
     *
     * @param fileUrl
     * @param activityRegistration
     * @throws IOException
     */
    private void updateActivityRegistration(String fileUrl, AliexpressActivityRegistration activityRegistration) throws IOException {
        // 如果文件不为空，则更新文件
        if (StringUtils.isNotBlank(fileUrl)) {
            // 获取文件名
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);

            // 上传文件
            updateExcelFile(fileUrl, fileName, dataList,activityRegistration);
        }

        // 计算报名数量和更新状态
        if (registrationFileCount > 0) {
            activityRegistration.setSubmitProCount(registrationFileCount);
        } else {
            Long count = dataList.stream().filter(map -> "Y".equals(map.get(registrationIndex))).count();
            activityRegistration.setSubmitProCount(count.intValue());
        }
        Integer proCount = dataList.size() > 2 ? (dataList.size() - 2) : 0;
        activityRegistration.setProCount(proCount);
        if (activityRegistration.getProCount() == 0) {
            activityRegistration.setConfirmationStatus(ActivityConfirmationStatusEnum.CONFIRMED_NO_UPLOAD.getCode());
            activityRegistration.setUploadStatus(ActivityUploadStatusEnum.NO_NEED_UPLOAD.getCode());
        } else {
            activityRegistration.setConfirmationStatus(ActivityConfirmationStatusEnum.UNCONFIRMED.getCode());
        }
        activityRegistration.setGenerationTime(new Timestamp(System.currentTimeMillis()));
        aliexpressActivityRegistrationMapper.updateByPrimaryKeySelective(activityRegistration);
    }

    /**
     * 数据处理完后清理并还原变量
     */
    private void reducingVariable() {
        dataList.clear();
        productIdIndex = -1;
        currentStockIndex = -1;
        buyLimitIndex = -1;
        applyStockIndex = -1;
        registrationIndex = -1;
        lowRegistrationIndex = -1;
        registrationFileCount = 0;
        semiManagedIndexList.clear();
        noSemiManagedIndexList.clear();
    }

    /**
     * 获取平台活动配置参数
     *
     * @param aliexpressActivityRegistration
     * @return
     */
    private Map<String, Double> getPlatformActivityConfigParam(AliexpressActivityRegistration aliexpressActivityRegistration) {
        // 获取平台活动配置
        AliexpressMarketingConfigExample example = new AliexpressMarketingConfigExample();
        AliexpressMarketingConfigExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(1).andTypeEqualTo(AlianceMarketingConfigTypeEnum.PLATFORM_ACTIVITY.getCode()).andAccountsLike("%" + aliexpressActivityRegistration.getAccountNumber() + "%");
        List<AliexpressMarketingConfig> aliexpressMarketingConfigs = aliexpressMarketingConfigMapper.selectByExample(example);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(aliexpressMarketingConfigs)) {
            throw new RuntimeException(String.format("无法获取[%s]店铺配置信息,请核对内容", aliexpressActivityRegistration.getAccountNumber()));
        }

        // 当前规则一个店铺只能在一个启用的配置中存在,所以获取第一个即可
        AliexpressMarketingConfig marketingConfig = aliexpressMarketingConfigs.get(0);
        PlatformActivityConfigParam configParam = JSON.parseObject(marketingConfig.getRuleJson(), PlatformActivityConfigParam.class);

        // 获取配置参数
        Map<String, Double> profitMap = new HashMap<>();
        profitMap.put("nonSemiReservedProfit", configParam.getNonSemiReservedProfit());
        profitMap.put("semiReservedProfit", configParam.getSemiReservedProfit());
        return profitMap;
    }

    /**
     * 修改并写回Excel文件
     *
     * @param inputFilePath
     * @param outputFilePath
     * @param modifiedData
     * @throws IOException
     */
    private void updateExcelFile(String inputFilePath, String outputFilePath, List<Map<Integer, String>> modifiedData,AliexpressActivityRegistration activityRegistration) throws IOException {
        InputStream inputStream = downloadFileFromURL(inputFilePath);
        if (inputStream == null) {
            throw new IOException("下载文件失败，请核对链接是否正确");
        }

        // 读取Excel文件
        Workbook workbook = new XSSFWorkbook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        for (int rowNum = 0; rowNum < modifiedData.size(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                row = sheet.createRow(rowNum);
            }
            Map<Integer, String> rowData = modifiedData.get(rowNum);
            for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
                int cellIndex = entry.getKey();
                if (cellIndex < 0) {
                    break;
                }
                Cell cell = row.getCell(cellIndex);
                if (cell == null) {
                    cell = row.createCell(cellIndex);
                }
                cell.setCellValue(entry.getValue());
            }
        }

        // 写回文件
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] fileBytes = bos.toByteArray();
        bos.close();
        workbook.close();
        inputStream.close();

        // 转换文件
        MultipartFile multipartFile = new MockMultipartFile("file", outputFilePath, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileBytes);
        File convertedFile = convert(multipartFile);

        // 上传文件
        ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(convertedFile, outputFilePath, ExcelConstant.smtExcel, "admin");
        if (!uploadResult.isSuccess()) {
            throw new IOException("上传文件失败，请稍后重试");
        }

        SeaweedFile seaweedFile = uploadResult.getResult();
        if (Objects.nonNull(seaweedFile)) {
            activityRegistration.setGeneratedFile(seaweedFile.getUrl2());
            activityRegistration.setGeneratedFileSize(Double.valueOf(seaweedFile.getSize()));
        }
    }

    /**
     * 转换文件
     *
     * @param multipartFile
     * @return
     * @throws IOException
     */
    public static File convert(MultipartFile multipartFile) throws IOException {
        File file = new File(System.getProperty("java.io.tmpdir") + System.getProperty("file.separator") + multipartFile.getOriginalFilename());
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(multipartFile.getBytes());
        }
        return file;
    }

    /**
     * 从 URL 下载文件
     *
     * @param fileUrl
     * @return
     * @throws IOException
     */
    @SneakyThrows
    public static InputStream downloadFileFromURL(String fileUrl) throws IOException {
        // 使用 HttpClient 从 URL 下载文件
        HttpClient client = HttpClient
                .newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .followRedirects(HttpClient.Redirect.NORMAL)
                .connectTimeout(Duration.ofSeconds(20))
                .build();

        // 创建HTTP请求
        HttpRequest request = HttpRequest
                .newBuilder()
                .uri(URI.create(fileUrl))
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();

        // 发送请求并获取响应体作为字节数组
        HttpResponse<byte[]> response = client.send(request,
                HttpResponse.BodyHandlers.ofByteArray());

        // 检查响应状态码
        if (response.statusCode() != 200) {
            throw new IOException("下载文件失败" + fileUrl + "，HTTP状态码: " + response.statusCode());
        }

        byte[] fileBytes = response.body();

        // 将字节数组转换为 InputStream 并返回
        return new ByteArrayInputStream(fileBytes);


//        RestTemplate restTemplate = new RestTemplate();
//        ResponseEntity<byte[]> response = restTemplate.getForEntity(fileUrl, byte[].class);
//        byte[] fileBytes = response.getBody();
        // 将字节数组转换为 InputStream
//        return new ByteArrayInputStream(fileBytes);
    }

}
