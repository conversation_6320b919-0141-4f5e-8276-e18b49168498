package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeBean;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeJson;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeValueJson;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.helper.LayeredAssessmentDetailHelper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.SkuLessImgService;
import com.estone.erp.publish.smt.service.SmtAccountGroupService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fmis.FmisClient;
import com.estone.erp.publish.system.fmis.FmisUtils;
import com.estone.erp.publish.system.fmis.model.AccountInfoDTO;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.ImageRequest;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSellerLayeredAssessmentDetail;
import com.estone.erp.publish.tidb.publishtidb.service.ISmtSellerLayeredAssessmentDetailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.util.StopWatch;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
public class AliexpressContentUtils {
    public static SkuLessImgService skuLessImgService = SpringUtils.getBean(SkuLessImgService.class);

    public static FmisClient fmisClient = SpringUtils.getBean(FmisClient.class);

    public static ISmtSellerLayeredAssessmentDetailService smtSellerLayeredAssessmentDetailService = SpringUtils.getBean(ISmtSellerLayeredAssessmentDetailService.class);

    public static List<String> platImgMarkList = Arrays.asList(".alicdn.", ".aliexpress-media.");


    public static String[] spanArr = new String[] { "<span style=\"font-family:Helvetica;font-size:25px;\"",
            "<span style=\"font-family:Helvetica;font-size:22px;\"",
            "<span style=\"font-family: verdana;font-size: medium;\"",
            "<span style=\"font-family: arial;font-size: medium;\"",
            "<span style=\"background-color: rgb(255, 192, 0);\"",
            "<span style=\"background-color:#ffffff;font-family:Helvetica;font-size:16px;\"",
            "<span style=\"color: rgb(0, 0, 0); font-family: Arial, Helvetica, sans-serif;\"",
            "<span style=\"font-size: 18.0px;\"", "<span style=\"font-family:Helvetica;font-size:20px;\"" };

    public static String[] bLabel = new String[] { "<b style=\"color: #000000;font-size: medium;\"",
            "<b style=\"font-family: verdana;font-size: small;\"",
            "<b style=\"color: rgb(0, 0, 0); font-size: medium;\"",
            "<b style=\"border-bottom: 0.0px;border-left: 0.0px;padding-bottom: 0.0px;margin: 0.0px;padding-left: 0.0px;padding-right: 0.0px;font-family: helvetica , verdana , sans-serif;font-size: 13.0px;border-top: 0.0px;font-weight: bold;border-right: 0.0px;padding-top: 0.0px;\"",
            "<b style=\"border-bottom: 0.0px;border-left: 0.0px;padding-bottom: 0.0px;line-height: 13.0px;font-style: normal;margin: 0.0px;padding-left: 0.0px;padding-right: 0.0px;font-family: helvetica , verdana , sans-serif;color: #333333;font-size: 13.0px;border-top: 0.0px;font-weight: bold;border-right: 0.0px;padding-top: 0.0px;\"" };

    public static String[] pLabel = new String[] { "<p style=\"color: #000000;font-size: medium;\"",
            "<p style=\"text-align: left;color: #000000;font-size: medium;\"",
            "<p style=\"border-bottom: 0.0px;border-left: 0.0px;padding-bottom: 0.0px;margin: 0.0px;padding-left: 0.0px;padding-right: 0.0px;font-family: helvetica , verdana , sans-serif;font-size: 13.0px;border-top: 0.0px;font-weight: normal;border-right: 0.0px;padding-top: 0.0px;\"" };


    public static String re = " compaTible with ";

    /**
     * 判断是否smt图片
     * @param img
     * @return
     */
    public static boolean isSmtImg(String img){
        for (String platImgMark : platImgMarkList) {
            if(StringUtils.contains(img, platImgMark)){
                return true;
            }
        }
        return false;
    }

    public static String randomStyle(String detail) {

        String spanStyle = AliexpressContentUtils.randomStyle(detail, Arrays.asList(AliexpressContentUtils.spanArr),
                "<span>", "</span>", "<span", ">");

        String bStyle = AliexpressContentUtils.randomStyle(spanStyle, Arrays.asList(AliexpressContentUtils.bLabel),
                "<b>", "</b>", "<b", ">");

        String pStyle = AliexpressContentUtils.randomStyle(bStyle, Arrays.asList(AliexpressContentUtils.pLabel), "<p>",
                "</p>", "<p", ">");

        return pStyle;
    }

    /**
     * 
     * @param detail 内容
     * @param randomList 随机样式
     * @param full "<span>"
     * @param lable eq "</span>"
     * @param qian "<span"
     * @param hou ">"
     * @return
     */
    public static String randomStyle(String detail, List<String> randomList, String full, String lable, String qian,
            String hou) {

        if (StringUtils.isBlank(detail) || CollectionUtils.isEmpty(randomList) || StringUtils.isBlank(lable)
                || StringUtils.isBlank(full) || StringUtils.isBlank(qian) || StringUtils.isBlank(hou)) {
            return detail;
        }

        try {

            // 替换span标签
            String[] split = StringUtils.splitByWholeSeparator(detail, lable);

            // 切分不到 直接返回
            if (split.length == 1) {
                return detail;
            }

            // 替换span
            String newDetail = "";

            for (String string : split) {

                if (StringUtils.isBlank(string)) {
                    continue;
                }

                if (StringUtils.indexOf(string, full) == -1) {

                    int begin = string.indexOf(qian);

                    if (begin != -1) {

                        // 样式之前的内容
                        String befor = StringUtils.substring(string, 0, begin);

                        // 样式 + 内容
                        String end = StringUtils.substring(string, begin, string.length());

                        // 切分内容使用
                        int indexOf = end.indexOf(hou);

                        if (indexOf != -1) {

                            // 需要替换的
                            // String substring = StringUtils.substring(end, 0,
                            // indexOf);

                            // 内容
                            String content = StringUtils.substring(end, indexOf, end.length());

                            int random = (int) (Math.random() * randomList.size());
                            String newString = befor + randomList.get(random) + content;

                            newDetail += newString + lable;
                        }

                    }
                    else {
                        newDetail += string + lable;
                    }

                }
                else {
                    newDetail += string + lable;
                }
            }

            return newDetail;

        }
        catch (Exception e) {
            // 异常直接返回
            return detail;
        }
    }

    /**
     * 按长度截取标题 自动刊登取系统标题时候  根据店铺配置 需要加上前缀或者后缀
     * @param originalTitle
     * @param account
     * @return
     */
    public static String changTitleForAccount(String originalTitle, String account){
        if(StringUtils.isBlank(originalTitle)){
            return originalTitle;
        }
        originalTitle = originalTitle.replaceAll("(?i)"+ re, " for ");
        originalTitle = originalTitle.trim();
        String titleValue = "";
        Integer titleType = null;
        if(StringUtils.isNotBlank(account)){
            AliexpressConfigService configService = SpringUtils.getBean(AliexpressConfigService.class);
            AliexpressConfig aliexpressConfig = configService.selectByAccount(account);
            if(aliexpressConfig != null){
                titleValue = aliexpressConfig.getTitleValue();
                titleType = aliexpressConfig.getTitleType();
            }
        }

        //没类型直接置空
        if(titleType == null){
            titleValue = "";
        }

        if(StringUtils.isBlank(originalTitle)){
            return "";
        }else if((originalTitle + (StringUtils.isNotBlank(titleValue) ? " " + titleValue : "")).length() <=128){
            if(StringUtils.isBlank(titleValue)){
                return originalTitle;
            }else{
                //前缀
                if(titleType != null && titleType.intValue() == 0){
                    return titleValue + " " + originalTitle;
                }
                return originalTitle + " " + titleValue;
            }
        }

        StringBuffer sb = new StringBuffer();

        String[] split = originalTitle.split(" ");
        for (String s : split) {

            String sWithPrefix = s + (StringUtils.isNotBlank(titleValue) ? " " + titleValue : "");
            if(sWithPrefix.length() > 128){
                continue;
            }

            //组合标题
            String title = sb.toString() + s + (StringUtils.isNotBlank(titleValue) ? " " + titleValue : "");
            if(title.length() <= 128){
                sb.append(s + " ");
            }
        }

        //前缀
        if(titleType != null && titleType.intValue() == 0){
            return (titleValue + " " + sb.toString()).trim();
        }

        return (sb.toString() + (StringUtils.isNotBlank(titleValue) ? titleValue : "")).trim();
    }

    /**
     * 优先smt的图片，没有就取公共图片
     * @param imgMap
     * @return
     */
    public static Map<String, List<String>> imgPriorityForMap(Map<String, List<String>> imgMap){
        if(imgMap == null || imgMap.isEmpty()){
            return imgMap;
        }

        Map<String, List<String>> imgPriorityForMap = new HashMap<>();

        imgMap.forEach((k, v) -> {
            List<String> strings = imgPriorityForList(v);
            imgPriorityForMap.put(k, strings);
        });
        return imgPriorityForMap;
    }


    /**
     * http://172.16.2.103:8080/browse/ES-10985
     * @param images
     * @param sonSkuImgList
     * @param articleNumber
     * @return
     */
    public static List<String> removeRepeatImgs(List<String> images, List<String> sonSkuImgList, String articleNumber){
        return images;
//        if(CollectionUtils.isEmpty(images)){
//            SkuLessImg record = new SkuLessImg();
//            record.setSku(articleNumber);
//            record.setCreateTime(new Timestamp(System.currentTimeMillis()));
//            skuLessImgService.insert(record);
//            return new ArrayList<>();
//        }
//
//        List<String> allImages = new ArrayList<>();
//        allImages.addAll(images);
//
//        //调用产品系统接口去重  http://172.16.2.103:8080/browse/ES-10454
//        ImageRequest imageRequest = new ImageRequest();
//        imageRequest.setImg_list1(allImages);
//        imageRequest.setImg_list2(allImages);
//        //去重之后的图片
//        List<String> mutilImages = SmtImgSolutionUtil.imgRepeatCheckNew(imageRequest);
//
//        //TODO 小于6张需要 记录日志 托送产品系统
//        if(mutilImages.size() < 6){
//            SkuLessImg record = new SkuLessImg();
//            record.setSku(articleNumber);
//            record.setCreateTime(new Timestamp(System.currentTimeMillis()));
//            skuLessImgService.insert(record);
//        }
//        //说明有重复图片 需要重新把cmb图片放到第6位
//        if(mutilImages.size() < images.size()){
//            //smt 需求 包含 -cmb 的图片放在第6张
//            List<String> cmbList = mutilImages.stream().filter(t -> StringUtils.indexOf(t.toLowerCase(), "-cmb") != -1)
//                    .collect(Collectors.toList());
//
//            //正常图片
//            List<String> normalList = mutilImages.stream().filter(
//                    t -> org.apache.commons.lang.StringUtils.indexOf(t.toLowerCase(), "-cmb") == -1 &&
//                            org.apache.commons.lang.StringUtils.indexOf(t.toLowerCase(), "-effect-copy.") == -1)
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(sonSkuImgList)) {
//                normalList = normalList.stream().filter(t -> !sonSkuImgList.contains(t)).collect(Collectors.toList());
//            }
//            //最终的结果
//            List<String> resultImages = new ArrayList<>();
//
//            //补充图片
//            List<String> plugImgList = new ArrayList<>();
//
//            int pageSize = 5;
//            List<List<String>> lists = PagingUtils.pagingList(normalList, pageSize);
//            if (lists.size() == 1 && lists.get(0).size() < pageSize) {
//                //需要补齐5张图片
//                int i = pageSize - lists.get(0).size();
//
//                //补图片
//                for (int j = 0; j < i; j++) {
//                    if (CollectionUtils.isNotEmpty(sonSkuImgList)) {
//                        if (sonSkuImgList.size() >= (j + 1)) {
//                            String plugImg = sonSkuImgList.get(j);
//                            plugImgList.add(plugImg);
//                            lists.get(0).add(plugImg);
//                        }
//                    }
//                }
//            }
//
//            int i = 0;
//            for (List<String> list : lists) {
//                //是为了将cmb图片放在第6的位置上面
//                if (i == 1) {
//                    if (CollectionUtils.isNotEmpty(cmbList)) {
//                        resultImages.addAll(cmbList);
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(list)) {
//                    resultImages.addAll(list);
//                    //需要加上cmb图片
//                    if (lists.size() == 1) {
//                        if (CollectionUtils.isNotEmpty(cmbList)) {
//                            resultImages.addAll(cmbList);
//                        }
//                    }
//                }
//                i++;
//            }
//            //差集
//            if (CollectionUtils.isNotEmpty(sonSkuImgList)) {
//                sonSkuImgList.removeAll(plugImgList);
//                if (CollectionUtils.isNotEmpty(sonSkuImgList)) {
//                    resultImages.addAll(sonSkuImgList);
//                }
//            }
//            mutilImages = resultImages;
//        }
//        return mutilImages;
    }

    public static List<String> imgPriorityForList(List<String> list){
        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        List<String> noMarketImages = new ArrayList<>();
        for (String s : list) {
            if(StringUtils.isBlank(s) ){
                continue;
            }

            // 去除产品系统-effect-copy.长图750*1000营销图
            if(StringUtils.indexOf(s, "-effect-copy.") != -1) {
                continue;
            }

            noMarketImages.add(s);
        }

        List<String> smtList = new ArrayList<>();
        for (String s : noMarketImages) {
            if(StringUtils.isNotBlank(s) && StringUtils.indexOf(s, "/aliexpress/") != -1){
                smtList.add(s);
            }
        }
        return CollectionUtils.isEmpty(smtList) ? noMarketImages : smtList;
    }

    /**
     * 获取营销图
     * @param imgList
     * @return
     */
    public static List<MarketImage> getMarketImages(List<String> imgList, String spu){
        List<MarketImage> marketImages = new ArrayList<>();
        if(CollectionUtils.isEmpty(imgList) || StringUtils.isBlank(spu)){
            return marketImages;
        }
        List<String> copyImgList = imgList.stream().filter(t -> t.contains("-effect-copy.")).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(copyImgList)){
            MarketImage marketImage = new MarketImage();
            marketImages.add(marketImage);
            marketImage.setImage_type("1");
            marketImage.setUrl(copyImgList.get(0));
        }

        List<String> spuImgList = imgList.stream().filter(t -> t.contains(spu + ".")).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(spuImgList)){
            MarketImage marketImage = new MarketImage();
            marketImages.add(marketImage);
            marketImage.setImage_type("2");
            marketImage.setUrl(spuImgList.get(0));
        }else{
            String [] fields = {"mainSku","sonSku"};
            //查询spu下面所有的子sku
            List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(spu), fields);

            if(CollectionUtils.isNotEmpty(productInfoList)){
                List<String> sonSkuList = productInfoList.stream().filter(t -> StringUtils.isNotBlank(t.getSonSku())).map(t -> t.getSonSku()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(sonSkuList)){
                    for (String sonSku : sonSkuList) {
                        List<String> sonImgList = imgList.stream().filter(t -> t.contains(sonSku + ".")).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(sonImgList)){
                            MarketImage marketImage = new MarketImage();
                            marketImages.add(marketImage);
                            marketImage.setImage_type("2");
                            marketImage.setUrl(sonImgList.get(0));
                            break;
                        }
                    }
                }
            }
        }
//        for (String s : list) {
//            if(StringUtils.isNotBlank(s) && StringUtils.containsIgnoreCase(s, spu + ".")){
//                MarketImage marketImage = new MarketImage();
//                marketImages.add(marketImage);
//                marketImage.setImage_type("2");
//                marketImage.setUrl(s);
//            }
//            if(StringUtils.isNotBlank(s) && StringUtils.indexOf(s, "-effect-copy.") != -1){
//                MarketImage marketImage = new MarketImage();
//                marketImages.add(marketImage);
//                marketImage.setImage_type("1");
//                marketImage.setUrl(s);
//            }
//        }

        return marketImages;
    }

    public static void saleInfo(List<AliexpressConfig> aliexpressConfigs){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(aliexpressConfigs)){
            List<String> accountList = aliexpressConfigs.stream().map(t -> t.getAccount()).distinct()
                    .collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountList, SaleChannel.CHANNEL_SMT);
            SmtAccountGroupService smtAccountGroupService = SpringUtils.getBean(SmtAccountGroupService.class);
            //查询分组
            SmtAccountGroupExample groupExample = new SmtAccountGroupExample();
            groupExample.createCriteria().andAccountListLike(accountList);
            List<SmtAccountGroup> dbAccountGroupList = smtAccountGroupService.selectByExample(groupExample);
            Map<String, String> acccountGroupMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(dbAccountGroupList)){
                for (SmtAccountGroup smtAccountGroup : dbAccountGroupList) {
                    String accounts = smtAccountGroup.getAccounts();
                    List<String> strings = CommonUtils.splitList(accounts, ",");
                    for (String string : strings) {
                        acccountGroupMap.put(string, smtAccountGroup.getGroupName());
                    }
                }
            }

            //查询预估层级
            Map<String, String> layerLevelMap = getLayerLevelMap(accountList);

            //经营大类
            //将查询出来的店铺列表分片，分批调用外部接口
            List<List<String>> partitionList = Lists.partition(accountList, 200);
            Map<String, String> categoryMap = Maps.newConcurrentMap();
            StopWatch watch = new StopWatch();
            watch.start();
            CompletableFuture.allOf(partitionList.stream()
                            .map(list -> CompletableFuture.runAsync(() -> {
                                try {
                                    ApiResult<List<AccountInfoDTO>> results = fmisClient.listByAccount(org.apache.commons.lang.StringUtils.join(list,","));
                                    if (results.isSuccess() && !CollectionUtils.isEmpty(results.getResult())) {
                                        categoryMap.putAll(FmisUtils.getCategoryMap(results.getResult(),","));
                                    }
                                } catch (Exception e) {
                                    log.error("根据账号查询对应经营大类失败: errMsg:{}",e.getMessage(),e);
                                }
                            }, AliexpressExecutors.SMT_ACCOUNT_CONFIG_DOWNLOAD_POOL))
                            .toArray(CompletableFuture[]::new))
                    .join();

            for (AliexpressConfig aliexpressConfig : aliexpressConfigs) {
                String account = aliexpressConfig.getAccount();
                aliexpressConfig.setGroupName(acccountGroupMap.get(account));
                // 销售、销售组长、销售主管
                if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(account);
                    aliexpressConfig.setSalemanager(saleSuperiorTriple.getLeft());
                    aliexpressConfig.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                    aliexpressConfig.setSalesSupervisorName(saleSuperiorTriple.getRight());
                }
                aliexpressConfig.setLayerLevel(layerLevelMap.getOrDefault(account, ""));
                aliexpressConfig.setBusinessType(categoryMap.getOrDefault(account, ""));
            }
        }

        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    /**
     * 获取预估层级
     * @param accountList
     * @return
     */
    private static Map<String, String> getLayerLevelMap(List<String> accountList) {
        LambdaQueryWrapper<SmtSellerLayeredAssessmentDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SmtSellerLayeredAssessmentDetail::getAccountNumber, accountList);
        queryWrapper.eq(SmtSellerLayeredAssessmentDetail::getStype, 2);
        List<SmtSellerLayeredAssessmentDetail> list = smtSellerLayeredAssessmentDetailService.list(queryWrapper);
        Map<String, String> layerLevelMap = list.stream()
                .filter(t -> StringUtils.isNotBlank(t.getAssessment()))
                .collect(Collectors.toMap(
                        SmtSellerLayeredAssessmentDetail::getAccountNumber,
                        t -> LayeredAssessmentDetailHelper.convertDetailVo(t).getLayerLevel()
                ));
        return layerLevelMap;

    }

    public static void aptitudeSaleInfo(List<SmtProductAptitude> smtProductAptitudeList){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(smtProductAptitudeList)){
            List<String> accountList = smtProductAptitudeList.stream().map(t -> t.getAccountNumber()).distinct()
                    .collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountList, SaleChannel.CHANNEL_SMT);

            for (SmtProductAptitude smtProductAptitude : smtProductAptitudeList) {
                String account = smtProductAptitude.getAccountNumber();
                // 销售、销售组长、销售主管
                if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(account);
                    smtProductAptitude.setSalemanager(saleSuperiorTriple.getLeft());
                    smtProductAptitude.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                    smtProductAptitude.setSalesSupervisorName(saleSuperiorTriple.getRight());
                }

            }
        }

        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    /**
     * 更换年月日
     * @param stringDate
     * @return
     */
    public static Timestamp changeYearMonthDay(String stringDate){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar offCalendar = Calendar.getInstance();
        Date parse = null;
        try {
            parse = sdf.parse(stringDate);
        }
        catch (ParseException e) {
            log.error(e.getMessage());
        }
        offCalendar.setTime(parse);
        offCalendar.set(Calendar.YEAR, 2022);
        offCalendar.set(Calendar.MONTH, 0);
        offCalendar.set(Calendar.DAY_OF_MONTH, 1);
        return new Timestamp(offCalendar.getTimeInMillis());
    }

    /**
     * 更换年月日
     * @param timestamp
     * @return
     */
    public static Timestamp changeYearMonthDay(Timestamp timestamp){
        Calendar offCalendar = Calendar.getInstance();
        offCalendar.setTime(timestamp);
        offCalendar.set(Calendar.YEAR, 2022);
        offCalendar.set(Calendar.MONTH, 0);
        offCalendar.set(Calendar.DAY_OF_MONTH, 1);
        return new Timestamp(offCalendar.getTimeInMillis());
    }

    /**
     * 获取图片的子sku名称
     * @param imgUrl
     * @return
     */
    public static String getImgUrlSku(String imgUrl){
        String replace = null;
        try {
            String[] split = StringUtils.split(imgUrl,"/");
            String s = split[split.length - 1];
            String[] split1 = StringUtils.split(s, ".");
            String s1 = split1[split1.length - 1];
            replace = s.replace("." + s1, "");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return replace;
    }

    /**
     * 获取主图
     * 用于子图设置随机获取
     *
     * http://172.16.2.103:8080/browse/ES-11082
     * //需要优先smt的图片
     *
     * @param sku
     * @param images
     * @return
     */
    public static String randomSkuMainImage(String sku, List<String> images) {
        List<String> smtImgs = images.stream().filter(t -> t.contains("/aliexpress/")).collect(Collectors.toList());
        List<String> publicImgs = images.stream().filter(t -> !t.contains("/aliexpress/")).collect(Collectors.toList());

        String s = smtImgs.stream()
                .filter(o -> o.contains(String.format("/%s.", sku))
                        || o.contains(String.format("/%s.", sku + "-kd-"))
                        || o.contains(String.format("/%s.", sku + "-KD-")))
                .reduce((first, second) -> second).orElse(null);
        if(StringUtils.isNotBlank(s)){
            return s;
        }
        //筛选主图和kd图
        return publicImgs.stream()
                .filter(o -> o.contains(String.format("/%s.", sku))
                        || o.contains(String.format("/%s.", sku + "-kd-"))
                        || o.contains(String.format("/%s.", sku + "-KD-")))
                .reduce((first, second) -> second).orElse(null);
    }


    /**
     * 根据货号前缀得到货号
     * @param skuCode
     * @param articleNumberPrefix
     * @return
     */
    public static String getArticleNumber(String skuCode, String articleNumberPrefix){
        if (StringUtils.isNotBlank(skuCode)){
            //如果skuCode以GT-开头就需要取掉
            if(StringUtils.startsWith(skuCode, "GT-")){
                skuCode = skuCode.substring(3);
            }
        }
        if(StringUtils.isBlank(skuCode) || StringUtils.isBlank(articleNumberPrefix)){
            return skuCode;
        }
        String articleNumber = skuCode;
        if(StringUtils.isNotBlank(articleNumberPrefix) && StringUtils.contains(skuCode, articleNumberPrefix)){
            articleNumber = skuCode.replaceFirst(articleNumberPrefix, "");
        }
        return articleNumber.trim();
    }

    /**
     * 校验属性是否缺失
     * @param aeopAeProductPropertysJson
     * @param aliexpressCategory
     * @return
     */
    public static ResponseJson checkAttr(String aeopAeProductPropertysJson, AliexpressCategory aliexpressCategory, String account){
        ResponseJson rsp = new ResponseJson();
        if(StringUtils.isBlank(account)){
            account = "<EMAIL>";
        }

        String categoryAttributes = aliexpressCategory.getChildAttributesJson();
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);

        AttributeBean attributeBean = JSON.parseObject(categoryAttributes, new TypeReference<AttributeBean>() {
        });

        Set<Long> percentageIdSet = new HashSet<>();//百分比必填属性id
        Set<Long> requiredIdSet = new HashSet<>();//平台必填属性id
        Map<Long, String> idToNameMap = new HashMap<>();//属性idMap
        Set<String> requiredHasSonIdSet = new HashSet<>(); // 联动属性

        Set<Long> platValueIdSet = new HashSet<>();//平台 platValueIdSet
        Map<Long, String> valueIdToNameMap = new HashMap<>();//value属性idMap
        for (AttributeJson attribute : attributeBean.getAttributes()) {
            Boolean sku = attribute.getSku();//是否sku属性
            Boolean required = attribute.getRequired();
            Long id = attribute.getId();
            if(sku != null && !sku && required != null && required){
                requiredIdSet.add(id);
            }
            JSONObject names = attribute.getNames();
            String zh = names.getString("zh");
            idToNameMap.put(id, zh);

            List<AttributeValueJson> values = attribute.getValues();
            if(CollectionUtils.isNotEmpty(values)){
                for (AttributeValueJson value : values) {
                    Long valueId = value.getId();
                    platValueIdSet.add(valueId);
                    JSONObject names1 = value.getNames();
                    if(names1 != null){
                        String zh1 = names1.getString("zh");
                        valueIdToNameMap.put(valueId, zh1);
                    }

                    //包含联动属性
                    Boolean hasSubAttr = value.getHasSubAttr();
                    if(hasSubAttr != null && hasSubAttr){
                        requiredHasSonIdSet.add(id + "=" + valueId);
                    }
                }
            }

            //校验材质百分比
            String features = attribute.getFeatures();
            String attributeShowTypeValue = attribute.getAttributeShowTypeValue();
            if(StringUtils.isNotBlank(features) && StringUtils.equalsIgnoreCase(attributeShowTypeValue, "check_box")){
                Map<String, String> map = JSON.parseObject(features, Map.class);
                String ae_feature_material_ratio = map.get("AE_FEATURE_material_ratio");
                if(StringUtils.isNotBlank(ae_feature_material_ratio) && StringUtils.equalsIgnoreCase(ae_feature_material_ratio, "1")){
                    percentageIdSet.add(id);
                }
            }
        }

        Set<Long> tempNameIdSet = new HashSet<>();//模板nameidSet
//            Set<Long> tempValueIdSet = new HashSet<>();//模板valueidSet

        JSONArray aeop_ae_product_propertys = JSONArray.parseArray(aeopAeProductPropertysJson);

        /**
         * {
         * "inputType":"select",
         * "attr_name_id":200001484,
         * "attr_value_id":""
         * }
         */
        for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
            JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
            if(jsonObject1.containsKey("attr_value_id")){
                String attr_value_id1 = jsonObject1.getString("attr_value_id");
                if(org.apache.commons.lang.StringUtils.isBlank(attr_value_id1)){
                    aeop_ae_product_propertys.remove(i);
                }
            }
        }

        //获取平台的属性值
        CategoryOpenCall categoryOpenCall = new CategoryOpenCall();

        for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
            JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
            long attr_name_id = jsonObject1.getLongValue("attr_name_id");
            tempNameIdSet.add(attr_name_id);
            long attr_value_id = jsonObject1.getLongValue("attr_value_id");

            //去除省份属性
            if(attr_name_id != 266081643L && attr_value_id != 0L){
                //联动属性 继续请求接口获取 联动属性的 id必填和value值
                if(requiredHasSonIdSet.contains(attr_name_id + "=" + attr_value_id)){
                    String param2 = attr_name_id + "=" + attr_value_id;
                    String nextAttributes = categoryOpenCall.getCategoryAttributes(saleAccountByAccountNumber, aliexpressCategory.getCategoryId().toString(), param2);
                    String attributes = AliexpressCategoryUtils.parseCategoryAttributes(nextAttributes);

                    if(StringUtils.isNotBlank(attributes)){
                        AttributeBean nextAttributeBean = JSON.parseObject(attributes, new TypeReference<AttributeBean>() {
                        });

                        for (AttributeJson attribute : nextAttributeBean.getAttributes()) {
                            Boolean sku = attribute.getSku();//是否sku属性
                            Boolean required = attribute.getRequired();
                            Long id = attribute.getId();
                            if(sku != null && !sku && required != null && required){
                                requiredIdSet.add(id);
                            }
                            JSONObject names = attribute.getNames();
                            String zh = names.getString("zh");
                            idToNameMap.put(id, zh);

                            List<AttributeValueJson> values = attribute.getValues();
                            if(CollectionUtils.isNotEmpty(values)){
                                for (AttributeValueJson value : values) {
                                    Long valueId = value.getId();
                                    platValueIdSet.add(valueId);
                                    JSONObject names1 = value.getNames();
                                    if(names1 != null){
                                        String zh1 = names1.getString("zh");
                                        valueIdToNameMap.put(valueId, zh1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
            JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
            long attr_name_id = jsonObject1.getLongValue("attr_name_id");
            tempNameIdSet.add(attr_name_id);
            long attr_value_id = jsonObject1.getLongValue("attr_value_id");

            //去除省份属性和品牌
            if((attr_name_id != 266081643L || attr_name_id != 2L) && attr_value_id != 0L){
                // 如果存在不属于平台的属性，需要去除掉
                if(attr_value_id != -1L && !platValueIdSet.contains(attr_value_id)){
                    aeop_ae_product_propertys.remove(i);
                }
            }
        }

        //重新设置
        rsp.setMessage(aeop_ae_product_propertys.toJSONString());
        requiredIdSet.removeAll(tempNameIdSet); //平台的必填id 去除模板存在的id 如果不为空说明有必填id没有填
        requiredIdSet.remove(266081643L);//省份
        if(CollectionUtils.isNotEmpty(requiredIdSet)){
            List<String> errorMsgList = new ArrayList<>();
            for (Long aLong : requiredIdSet) {
                errorMsgList.add(aLong + ":" + idToNameMap.get(aLong));
            }
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage(PreCheckUtils.preCheck + "商品的必填属性没填" + org.apache.commons.lang.StringUtils.join(errorMsgList, ","));
            return rsp; //商品的必填属性没填，propId=***的属性不能为空
        }

        //百分比属性校验
        /**
         * {
         *         "attr_name_id": "200000043",
         *         "attr_name": "size",
         *         "attr_value_id": "493",
         *         "attr_value_unit": "0",
         *         "percent": "1",
         *         "attr_value": "2 - 5 kg",
         *         "attr_value_end": "0",
         *         "attr_value_start": "0"
         *       }
         */
        if(!percentageIdSet.isEmpty()){
            Map<Long, List<JSONObject>> product_propertysMap = new HashMap<>();
            for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                long attr_name_id = jsonObject1.getLongValue("attr_name_id");
                if(percentageIdSet.contains(attr_name_id)){
                    List<JSONObject> jsonObjects = product_propertysMap.get(attr_name_id);
                    if(CollectionUtils.isEmpty(jsonObjects)){
                        jsonObjects = new ArrayList<>();
                        product_propertysMap.put(attr_name_id, jsonObjects);
                    }
                    jsonObjects.add(jsonObject1);
                }
            }

            if(!product_propertysMap.isEmpty()){
                List<String> errorMsgList = new ArrayList<>();
                for (Map.Entry<Long, List<JSONObject>> longListEntry : product_propertysMap.entrySet()) {
                    Long key = longListEntry.getKey();
                    List<JSONObject> value = longListEntry.getValue();
                    if(value.size() > 1){
                        int totalPercent = 0;
                        for (JSONObject object : value) {
                            String percent = object.getString("percent");
                            if(org.apache.commons.lang.StringUtils.isBlank(percent)){
//                                errorMsgList.add(key + ":" + idToNameMap.get(key));
                                break;
                            }else{
                                totalPercent += Integer.valueOf(percent);
                            }
                        }
                        if(totalPercent < 100){
                            errorMsgList.add(key + ":" + idToNameMap.get(key));
                        }
                    }
                }

                if(CollectionUtils.isNotEmpty(errorMsgList)){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "商品的必填属性 百分百属性没填 或者没有达到100" + org.apache.commons.lang.StringUtils.join(errorMsgList, ","));
                    return rsp; //商品的必填属性没填，propId=***的属性不能为空
                }
            }
        }
        return rsp;
    }

    /**
     * 去除禁编属性
     * @param aeopAeProductPropertysJson 原属性
     * @param categoryAttributes 平台属性
     * @return
     */
    public static String removeDisableAttr(String aeopAeProductPropertysJson, String categoryAttributes){
        if(StringUtils.isBlank(aeopAeProductPropertysJson) || StringUtils.isBlank(categoryAttributes)){
            return aeopAeProductPropertysJson;
        }

        AttributeBean attributeBean = JSON.parseObject(categoryAttributes, new TypeReference<AttributeBean>() {
        });

        Set<Long> disableAttrNameIdSet = new HashSet<>();//禁编属性 nameId Set

        Set<Long> disableAttrValueIdSet = new HashSet<>();//禁编属性 valueId Set

        for (AttributeJson attribute : attributeBean.getAttributes()) {
            Long id = attribute.getId();
            //禁编属性
            String features = attribute.getFeatures();
            if(StringUtils.isNotBlank(features)){
                Map<String, String> map = JSON.parseObject(features, Map.class);
                String AE_FEATURE_forbidden_use = map.get("AE_FEATURE_forbidden_use");
                if(StringUtils.isNotBlank(AE_FEATURE_forbidden_use) && StringUtils.equalsIgnoreCase(AE_FEATURE_forbidden_use, "1")){
                    disableAttrNameIdSet.add(id);
                }
            }

            List<AttributeValueJson> values = attribute.getValues();
            if(CollectionUtils.isNotEmpty(values)){
                for (AttributeValueJson value : values) {
                    Long valueId = value.getId();
                    Object valueTags = value.getValueTags();
                    if(valueTags != null){
                        Map<String, String> valueMap = JSON.parseObject(valueTags.toString(), Map.class);
                        String AE_FEATURE_forbidden_use = valueMap.get("AE_FEATURE_forbidden_use");
                        if(StringUtils.isNotBlank(AE_FEATURE_forbidden_use) && StringUtils.equalsIgnoreCase(AE_FEATURE_forbidden_use, "1")){
                            disableAttrValueIdSet.add(valueId);
                        }
                    }
                }
            }
        }
        if(disableAttrNameIdSet.isEmpty() && disableAttrValueIdSet.isEmpty()){
            return aeopAeProductPropertysJson;
        }
        JSONArray initJsonArray = JSONArray.parseArray(aeopAeProductPropertysJson);
        JSONArray finalJsonArray = new JSONArray();
        for (int i = 0; i < initJsonArray.size(); i++) {
            JSONObject jsonObject = initJsonArray.getJSONObject(i);
            Long attr_name_id = jsonObject.getLong("attr_name_id");
            //可能没有valueid
            Long attr_value_id = jsonObject.getLong("attr_value_id");
            if(disableAttrNameIdSet.contains(attr_name_id)){
                continue;
            }
            if(attr_value_id != null && disableAttrValueIdSet.contains(attr_value_id)){
                continue;
            }
            finalJsonArray.add(jsonObject);
        }
        return finalJsonArray.toJSONString();
    }

    public static void main(String[] args) {
//        String aa = "http://10.100.1.200:8888/public/2023-08/16-15-31-50-135/11JJ403157-RW1.5.jpg";
//        String[] split = StringUtils.split(aa,"/");
//        String s = split[split.length - 1];
//        String[] split1 = StringUtils.split(s, ".");
//        String s1 = split1[split1.length - 1];
//        String replace = s.replace("." + s1, "");
//        System.out.println(replace);
    }

}
