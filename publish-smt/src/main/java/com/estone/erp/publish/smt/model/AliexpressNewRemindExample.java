package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressNewRemindExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressNewRemindExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPublishStatusIn(List<Integer> values) {
            addCriterion("publish_status in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusEqualTo(Integer value) {
            addCriterion("publish_status =", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSpuIsNull() {
            addCriterion("spu is null");
            return (Criteria) this;
        }

        public Criteria andSpuIsNotNull() {
            addCriterion("spu is not null");
            return (Criteria) this;
        }

        public Criteria andSpuEqualTo(String value) {
            addCriterion("spu =", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotEqualTo(String value) {
            addCriterion("spu <>", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThan(String value) {
            addCriterion("spu >", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThanOrEqualTo(String value) {
            addCriterion("spu >=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThan(String value) {
            addCriterion("spu <", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThanOrEqualTo(String value) {
            addCriterion("spu <=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLike(String value) {
            addCriterion("spu like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotLike(String value) {
            addCriterion("spu not like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuIn(List<String> values) {
            addCriterion("spu in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotIn(List<String> values) {
            addCriterion("spu not in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuBetween(String value1, String value2) {
            addCriterion("spu between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotBetween(String value1, String value2) {
            addCriterion("spu not between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andEditorEqualTo(String value) {
            addCriterion("editor =", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorIn(List<String> values) {
            addCriterion("editor in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andPublishRoleEqualTo(Integer value) {
            addCriterion("publish_role =", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIn(List<Integer> values) {
            addCriterion("publish_role in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotEqualTo(Integer value) {
            addCriterion("publish_role <>", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andSaleManEqualTo(String value) {
            addCriterion("sale_man =", value, "saleMan");
            return (Criteria) this;
        }

        public Criteria andSaleManIsNull() {
            addCriterion("sale_man is null");
            return (Criteria) this;
        }

        public Criteria andSaleManIn(List<String> values) {
            addCriterion("sale_man in", values, "saleMan");
            return (Criteria) this;
        }

        public Criteria andSaleLeaderManEqualTo(String value) {
            addCriterion("sale_leader_man =", value, "saleLeaderMan");
            return (Criteria) this;
        }

        public Criteria andSaleLeaderManIn(List<String> values) {
            addCriterion("sale_leader_man in", values, "saleLeaderMan");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andRootCategoryIsNull() {
            addCriterion("root_category is null");
            return (Criteria) this;
        }

        public Criteria andRootCategoryIsNotNull() {
            addCriterion("root_category is not null");
            return (Criteria) this;
        }

        public Criteria andRootCategoryEqualTo(String value) {
            addCriterion("root_category =", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryNotEqualTo(String value) {
            addCriterion("root_category <>", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryGreaterThan(String value) {
            addCriterion("root_category >", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("root_category >=", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryLessThan(String value) {
            addCriterion("root_category <", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryLessThanOrEqualTo(String value) {
            addCriterion("root_category <=", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryIn(List<String> values) {
            addCriterion("root_category in", values, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryNotIn(List<String> values) {
            addCriterion("root_category not in", values, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryBetween(String value1, String value2) {
            addCriterion("root_category between", value1, value2, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryNotBetween(String value1, String value2) {
            addCriterion("root_category not between", value1, value2, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameIsNull() {
            addCriterion("root_category_zhname is null");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameIsNotNull() {
            addCriterion("root_category_zhname is not null");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameEqualTo(String value) {
            addCriterion("root_category_zhname =", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameNotEqualTo(String value) {
            addCriterion("root_category_zhname <>", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameGreaterThan(String value) {
            addCriterion("root_category_zhname >", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameGreaterThanOrEqualTo(String value) {
            addCriterion("root_category_zhname >=", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameLessThan(String value) {
            addCriterion("root_category_zhname <", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameLessThanOrEqualTo(String value) {
            addCriterion("root_category_zhname <=", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameLike(String value) {
            addCriterion("root_category_zhname like", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameNotLike(String value) {
            addCriterion("root_category_zhname not like", value, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameIn(List<String> values) {
            addCriterion("root_category_zhname in", values, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameNotIn(List<String> values) {
            addCriterion("root_category_zhname not in", values, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameBetween(String value1, String value2) {
            addCriterion("root_category_zhname between", value1, value2, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andRootCategoryZhnameNotBetween(String value1, String value2) {
            addCriterion("root_category_zhname not between", value1, value2, "rootCategoryZhname");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeIsNull() {
            addCriterion("edit_finish_time is null");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeIsNotNull() {
            addCriterion("edit_finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeEqualTo(Timestamp value) {
            addCriterion("edit_finish_time =", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeNotEqualTo(Timestamp value) {
            addCriterion("edit_finish_time <>", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeGreaterThan(Timestamp value) {
            addCriterion("edit_finish_time >", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("edit_finish_time >=", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeLessThan(Timestamp value) {
            addCriterion("edit_finish_time <", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("edit_finish_time <=", value, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeIn(List<Timestamp> values) {
            addCriterion("edit_finish_time in", values, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeNotIn(List<Timestamp> values) {
            addCriterion("edit_finish_time not in", values, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("edit_finish_time between", value1, value2, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andEditFinishTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("edit_finish_time not between", value1, value2, "editFinishTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeIsNull() {
            addCriterion("push_time is null");
            return (Criteria) this;
        }

        public Criteria andPushTimeIsNotNull() {
            addCriterion("push_time is not null");
            return (Criteria) this;
        }

        public Criteria andPushTimeEqualTo(Timestamp value) {
            addCriterion("push_time =", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotEqualTo(Timestamp value) {
            addCriterion("push_time <>", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeGreaterThan(Timestamp value) {
            addCriterion("push_time >", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("push_time >=", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeLessThan(Timestamp value) {
            addCriterion("push_time <", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("push_time <=", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeIn(List<Timestamp> values) {
            addCriterion("push_time in", values, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotIn(List<Timestamp> values) {
            addCriterion("push_time not in", values, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("push_time between", value1, value2, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("push_time not between", value1, value2, "pushTime");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempIsNull() {
            addCriterion("is_success_temp is null");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempIsNotNull() {
            addCriterion("is_success_temp is not null");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempEqualTo(Boolean value) {
            addCriterion("is_success_temp =", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempNotEqualTo(Boolean value) {
            addCriterion("is_success_temp <>", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempGreaterThan(Boolean value) {
            addCriterion("is_success_temp >", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_success_temp >=", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempLessThan(Boolean value) {
            addCriterion("is_success_temp <", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempLessThanOrEqualTo(Boolean value) {
            addCriterion("is_success_temp <=", value, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempIn(List<Boolean> values) {
            addCriterion("is_success_temp in", values, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempNotIn(List<Boolean> values) {
            addCriterion("is_success_temp not in", values, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempBetween(Boolean value1, Boolean value2) {
            addCriterion("is_success_temp between", value1, value2, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andIsSuccessTempNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_success_temp not between", value1, value2, "isSuccessTemp");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeIsNull() {
            addCriterion("temp_finish_time is null");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeIsNotNull() {
            addCriterion("temp_finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeEqualTo(Timestamp value) {
            addCriterion("temp_finish_time =", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeNotEqualTo(Timestamp value) {
            addCriterion("temp_finish_time <>", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeGreaterThan(Timestamp value) {
            addCriterion("temp_finish_time >", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("temp_finish_time >=", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeLessThan(Timestamp value) {
            addCriterion("temp_finish_time <", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("temp_finish_time <=", value, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeIn(List<Timestamp> values) {
            addCriterion("temp_finish_time in", values, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeNotIn(List<Timestamp> values) {
            addCriterion("temp_finish_time not in", values, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("temp_finish_time between", value1, value2, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andTempFinishTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("temp_finish_time not between", value1, value2, "tempFinishTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByIsNull() {
            addCriterion("last_update_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByIsNotNull() {
            addCriterion("last_update_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByEqualTo(String value) {
            addCriterion("last_update_by =", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByNotEqualTo(String value) {
            addCriterion("last_update_by <>", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByGreaterThan(String value) {
            addCriterion("last_update_by >", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_by >=", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByLessThan(String value) {
            addCriterion("last_update_by <", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByLessThanOrEqualTo(String value) {
            addCriterion("last_update_by <=", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByLike(String value) {
            addCriterion("last_update_by like", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByNotLike(String value) {
            addCriterion("last_update_by not like", value, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByIn(List<String> values) {
            addCriterion("last_update_by in", values, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByNotIn(List<String> values) {
            addCriterion("last_update_by not in", values, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByBetween(String value1, String value2) {
            addCriterion("last_update_by between", value1, value2, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateByNotBetween(String value1, String value2) {
            addCriterion("last_update_by not between", value1, value2, "lastUpdateBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null or remarks = ''");
            return (Criteria) this;
        }

        public Criteria andTagCodeLikeIn(List<String> list) {
            int len = list.size();
            StringBuilder sb = new StringBuilder(len * 32);
            for (int i = 0; i < len; i++) {
                sb.append("tag_codes LIKE '%,").append(list.get(i)).append(",%' ");
                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}