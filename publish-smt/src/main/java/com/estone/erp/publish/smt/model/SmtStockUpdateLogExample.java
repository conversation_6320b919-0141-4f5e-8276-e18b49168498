package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtStockUpdateLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtStockUpdateLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(String value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(String value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(String value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(String value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(String value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLike(String value) {
            addCriterion("sku_id like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotLike(String value) {
            addCriterion("sku_id not like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<String> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<String> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(String value1, String value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(String value1, String value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andRedisStockIsNull() {
            addCriterion("redis_stock is null");
            return (Criteria) this;
        }

        public Criteria andRedisStockIsNotNull() {
            addCriterion("redis_stock is not null");
            return (Criteria) this;
        }

        public Criteria andRedisStockEqualTo(Integer value) {
            addCriterion("redis_stock =", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockNotEqualTo(Integer value) {
            addCriterion("redis_stock <>", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockGreaterThan(Integer value) {
            addCriterion("redis_stock >", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("redis_stock >=", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockLessThan(Integer value) {
            addCriterion("redis_stock <", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockLessThanOrEqualTo(Integer value) {
            addCriterion("redis_stock <=", value, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockIn(List<Integer> values) {
            addCriterion("redis_stock in", values, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockNotIn(List<Integer> values) {
            addCriterion("redis_stock not in", values, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockBetween(Integer value1, Integer value2) {
            addCriterion("redis_stock between", value1, value2, "redisStock");
            return (Criteria) this;
        }

        public Criteria andRedisStockNotBetween(Integer value1, Integer value2) {
            addCriterion("redis_stock not between", value1, value2, "redisStock");
            return (Criteria) this;
        }

        public Criteria andStockBeforeIsNull() {
            addCriterion("stock_before is null");
            return (Criteria) this;
        }

        public Criteria andStockBeforeIsNotNull() {
            addCriterion("stock_before is not null");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEqualTo(Integer value) {
            addCriterion("stock_before =", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeNotEqualTo(Integer value) {
            addCriterion("stock_before <>", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeGreaterThan(Integer value) {
            addCriterion("stock_before >", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_before >=", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeLessThan(Integer value) {
            addCriterion("stock_before <", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeLessThanOrEqualTo(Integer value) {
            addCriterion("stock_before <=", value, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeIn(List<Integer> values) {
            addCriterion("stock_before in", values, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeNotIn(List<Integer> values) {
            addCriterion("stock_before not in", values, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeBetween(Integer value1, Integer value2) {
            addCriterion("stock_before between", value1, value2, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockBeforeNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_before not between", value1, value2, "stockBefore");
            return (Criteria) this;
        }

        public Criteria andStockAfterIsNull() {
            addCriterion("stock_after is null");
            return (Criteria) this;
        }

        public Criteria andStockAfterIsNotNull() {
            addCriterion("stock_after is not null");
            return (Criteria) this;
        }

        public Criteria andStockAfterEqualTo(Integer value) {
            addCriterion("stock_after =", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterNotEqualTo(Integer value) {
            addCriterion("stock_after <>", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterGreaterThan(Integer value) {
            addCriterion("stock_after >", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_after >=", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterLessThan(Integer value) {
            addCriterion("stock_after <", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterLessThanOrEqualTo(Integer value) {
            addCriterion("stock_after <=", value, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterIn(List<Integer> values) {
            addCriterion("stock_after in", values, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterNotIn(List<Integer> values) {
            addCriterion("stock_after not in", values, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterBetween(Integer value1, Integer value2) {
            addCriterion("stock_after between", value1, value2, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andStockAfterNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_after not between", value1, value2, "stockAfter");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIsNull() {
            addCriterion("order_num_30d is null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIsNotNull() {
            addCriterion("order_num_30d is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dEqualTo(Integer value) {
            addCriterion("order_num_30d =", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotEqualTo(Integer value) {
            addCriterion("order_num_30d <>", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dGreaterThan(Integer value) {
            addCriterion("order_num_30d >", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d >=", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dLessThan(Integer value) {
            addCriterion("order_num_30d <", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d <=", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIn(List<Integer> values) {
            addCriterion("order_num_30d in", values, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotIn(List<Integer> values) {
            addCriterion("order_num_30d not in", values, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d between", value1, value2, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d not between", value1, value2, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIsNull() {
            addCriterion("order_days_within_30d is null");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIsNotNull() {
            addCriterion("order_days_within_30d is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dEqualTo(Integer value) {
            addCriterion("order_days_within_30d =", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotEqualTo(Integer value) {
            addCriterion("order_days_within_30d <>", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dGreaterThan(Integer value) {
            addCriterion("order_days_within_30d >", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_days_within_30d >=", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dLessThan(Integer value) {
            addCriterion("order_days_within_30d <", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dLessThanOrEqualTo(Integer value) {
            addCriterion("order_days_within_30d <=", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIn(List<Integer> values) {
            addCriterion("order_days_within_30d in", values, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotIn(List<Integer> values) {
            addCriterion("order_days_within_30d not in", values, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dBetween(Integer value1, Integer value2) {
            addCriterion("order_days_within_30d between", value1, value2, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotBetween(Integer value1, Integer value2) {
            addCriterion("order_days_within_30d not between", value1, value2, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIsNull() {
            addCriterion("smt_order_rate is null");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIsNotNull() {
            addCriterion("smt_order_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateEqualTo(Double value) {
            addCriterion("smt_order_rate =", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotEqualTo(Double value) {
            addCriterion("smt_order_rate <>", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateGreaterThan(Double value) {
            addCriterion("smt_order_rate >", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateGreaterThanOrEqualTo(Double value) {
            addCriterion("smt_order_rate >=", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateLessThan(Double value) {
            addCriterion("smt_order_rate <", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateLessThanOrEqualTo(Double value) {
            addCriterion("smt_order_rate <=", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIn(List<Double> values) {
            addCriterion("smt_order_rate in", values, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotIn(List<Double> values) {
            addCriterion("smt_order_rate not in", values, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateBetween(Double value1, Double value2) {
            addCriterion("smt_order_rate between", value1, value2, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotBetween(Double value1, Double value2) {
            addCriterion("smt_order_rate not between", value1, value2, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("`result` is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("`result` is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(Boolean value) {
            addCriterion("`result` =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(Boolean value) {
            addCriterion("`result` <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(Boolean value) {
            addCriterion("`result` >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`result` >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThan(Boolean value) {
            addCriterion("`result` <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(Boolean value) {
            addCriterion("`result` <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<Boolean> values) {
            addCriterion("`result` in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<Boolean> values) {
            addCriterion("`result` not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(Boolean value1, Boolean value2) {
            addCriterion("`result` between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`result` not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNull() {
            addCriterion("fail_info is null");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNotNull() {
            addCriterion("fail_info is not null");
            return (Criteria) this;
        }

        public Criteria andFailInfoEqualTo(String value) {
            addCriterion("fail_info =", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotEqualTo(String value) {
            addCriterion("fail_info <>", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThan(String value) {
            addCriterion("fail_info >", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fail_info >=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThan(String value) {
            addCriterion("fail_info <", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThanOrEqualTo(String value) {
            addCriterion("fail_info <=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLike(String value) {
            addCriterion("fail_info like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotLike(String value) {
            addCriterion("fail_info not like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoIn(List<String> values) {
            addCriterion("fail_info in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotIn(List<String> values) {
            addCriterion("fail_info not in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoBetween(String value1, String value2) {
            addCriterion("fail_info between", value1, value2, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotBetween(String value1, String value2) {
            addCriterion("fail_info not between", value1, value2, "failInfo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}