package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressScItemInvitationPageQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.BatchRejectIds;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressScItemInvitation;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressScItemInvitationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/aliexpressScItemInvitation")
public class AliexpressScItemInvitationController {

    @Resource
    private AliexpressScItemInvitationService aliexpressScItemInvitationService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @PostMapping("/pageQuery")
    public ApiResult<IPage<AliexpressScItemInvitation>> pageQuery(@RequestBody AliexpressScItemInvitationPageQueryDto dto) {
        isAuth(dto);
        IPage<AliexpressScItemInvitation> page = aliexpressScItemInvitationService.pageQuery(dto);
        return ApiResult.newSuccess(page);
    }

    private void isAuth(AliexpressScItemInvitationPageQueryDto dto) {
        List<String> strings = permissionsHelper.smtAuth(dto.getAccounts(), dto.getManagerIds(), dto.getLeaderIds(), dto.getSaleIds(), null, "0", false);
        dto.setAccounts(strings);
    }

    @PostMapping("/batch/confirm")
    public ApiResult<?> batchConfirm(@RequestBody List<AliexpressScItemInvitation> aliexpressScItemInvitations) {
        aliexpressScItemInvitationService.batchConfirm(aliexpressScItemInvitations);
        return ApiResult.newSuccess();
    }

    @PostMapping("/batch/reject")
    public ApiResult<?> batchReject(@RequestBody BatchRejectIds batchRejectIds) {
        aliexpressScItemInvitationService.batchReject(batchRejectIds);
        return ApiResult.newSuccess();
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody AliexpressScItemInvitationPageQueryDto dto) {
        isAuth(dto);
        ResponseJson responseJson = aliexpressScItemInvitationService.export(dto);
        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }


}
