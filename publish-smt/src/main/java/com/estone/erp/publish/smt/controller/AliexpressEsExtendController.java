package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.*;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.SaleAccountListResponse;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.elasticsearch2.util.EsAliexpressProductListingUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.smt.bean.*;
import com.estone.erp.publish.smt.bean.SkuProperty.temp.TempSkuProperty;
import com.estone.erp.publish.smt.bean.brand.BrandRequestDO;
import com.estone.erp.publish.smt.call.direct.FreightTemplateOpenCall;
import com.estone.erp.publish.smt.call.direct.GroupOpenCall;
import com.estone.erp.publish.smt.call.direct.SynchItemOpenCall;
import com.estone.erp.publish.smt.call.direct.grsr.MerchantBindCall;
import com.estone.erp.publish.smt.call.direct.grsr.MerchantListCall;
import com.estone.erp.publish.smt.call.direct.wholesale.bean.DiscountParam;
import com.estone.erp.publish.smt.componet.AliexpressUpdateAttrHelper;
import com.estone.erp.publish.smt.componet.AliexpressUpdateSeasonHelper;
import com.estone.erp.publish.smt.componet.SmtItemEsBulkProcessor;
import com.estone.erp.publish.smt.constant.SmtConstant;
import com.estone.erp.publish.smt.enums.*;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.AliexpressProductListingMsgDto;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateImageTitleRequest;
import com.estone.erp.publish.smt.model.dto.EsAliexpressProductListingResponse;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.mq.excel.bean.ExcelBean;
import com.estone.erp.publish.smt.mq.excel.constant.ExcelConstant;
import com.estone.erp.publish.smt.mq.publish.enums.PublishTypeEnum;
import com.estone.erp.publish.smt.mq.publish.mq.PublishSend;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.smt.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/2615:35
 */
@RestController
@Slf4j
@RequestMapping("aliexpressEsExtend")
public class AliexpressEsExtendController {
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;
    @Resource
    private AliexpressProductGroupService aliexpressProductGroupService;
    @Resource
    private AliexpressFreightTemplateService aliexpressFreightTemplateService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private DrainageSkuService drainageSkuService;
    @Resource
    private ExcelSend excelSend;
    @Resource
    private SingleItemEsService singleItemEsService;
    @Resource
    private AliexpressConfigService aliexpressConfigService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private AliInternationalCaleConfigService aliInternationalCaleConfigService;
    @Resource
    private AliexpressEsProductDeleteRecordService aliexpressEsProductDeleteRecordService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AliexpressTgTemplateService aliexpressTgTemplateService;
    @Resource
    private SmtItemEsBulkProcessor smtItemEsBulkProcessor;
    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;
    @Resource
    private AliexpressAutoTemplateService aliexpressAutoTemplateService;
    @Resource
    private SmtAdminTempAttrRecordService smtAdminTempAttrRecordService;

    public SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final Map<String, String> productStatusTypes = new HashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        {
            ProductStatusTypeEnum[] values = ProductStatusTypeEnum.values();
            for (ProductStatusTypeEnum value : values) {
                super.put(value.getName(), value.getCode());
            }
        }
    };

    public static final String[] headerStateCheckResult = {"帐号", "product id", "sku",
            "调价方式", "Russian Federation", "毛利", "United States", "毛利", "Canada", "毛利", "Spain", "毛利",
            "France", "毛利", "United Kingdom", "毛利", "Netherlands", "毛利", "Israel", "毛利",
            "Brazil", "毛利", "Chile", "毛利", "Australia", "毛利", "Ukraine", "毛利",
            "Belarus", "毛利", "Japan", "毛利", "Thailand", "毛利", "Singapore", "毛利",
            "South Korea", "毛利", "Indonesia", "毛利", "Malaysia", "毛利", "Philippines", "毛利",
            "Vietnam", "毛利", "Italy", "毛利", "Germany", "毛利", "Saudi Arabia", "毛利",
            "United Arab Emirates", "毛利", "Poland", "毛利", "Turkey", "毛利", "Portugal", "毛利", "Belgium", "毛利", "Colombia", "毛利", "Mexico", "毛利", "Morocco", "毛利"};

    /**
     * 在线列表初始化页面条件
     *
     * @param method
     * @return
     */
    @GetMapping(value = "/pageInfo/{method}")
    public Object getSmtProductPageInfo(@PathVariable(value = "method", required = true) String method) {
        switch (method) {
            case "getProductStatusTypes":
                return productStatusTypes;
            case "getOrderByList":
                List<Map<String, String>> orderByList = new ArrayList<>();
                Map<String, String> orderByMap = new HashMap<>();
                orderByMap.put("code", "上架时间");
                orderByMap.put("name", "gmtCreate");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "同步时间");
                orderByMap.put("name", "lastSyncTime");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "编辑时间");
                orderByMap.put("name", "gmtModified");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "24小时销量");
                orderByMap.put("name", "order_24H_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "7天销量");
                orderByMap.put("name", "order_last_7d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "14天销量");
                orderByMap.put("name", "order_last_14d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "30天销量");
                orderByMap.put("name", "order_last_30d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "60天销量");
                orderByMap.put("name", "order_last_60d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "总销量");
                orderByMap.put("name", "order_num_total");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "30天动销天数");
                orderByMap.put("name", "order_days_within_30d");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "30天动销率");
                orderByMap.put("name", "order_days_within_30d_rate");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "60天动销天数");
                orderByMap.put("name", "order_days_within_60d");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "60天动销率");
                orderByMap.put("name", "order_days_within_60d_rate");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "7天浏览量");
                orderByMap.put("name", "view_7d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "7天曝光量");
                orderByMap.put("name", "exposure_7d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "14天浏览量");
                orderByMap.put("name", "view_14d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "14天曝光量");
                orderByMap.put("name", "exposure_14d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "30天浏览量");
                orderByMap.put("name", "view_30d_count");
                orderByList.add(orderByMap);

                orderByMap = new HashMap<>();
                orderByMap.put("code", "30天曝光量");
                orderByMap.put("name", "exposure_30d_count");
                orderByList.add(orderByMap);
                return orderByList;
            case "getDownloadFieldList":
                DownEnum[] fieldEnumArray = DownEnum.values();
                List<Map<String, Object>> fieldEnumList = new ArrayList<>();
                for (DownEnum downEnum : fieldEnumArray) {
                    Map<String, Object> enumMap = new HashMap<>();
                    enumMap.put("code", downEnum.getCode());
                    enumMap.put("name", downEnum.getName());
                    fieldEnumList.add(enumMap);
                }
                return fieldEnumList;
        }
        return ApiResult.newSuccess();
    }

    /**
     * 搜索和其他按钮功能
     *
     * @param requestParam
     * @param request
     * @param response
     * @return
     */
    @PostMapping
    public ApiResult<?> postAliexpressProduct(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletRequest request, HttpServletResponse response) {
        String method = requestParam.getMethod();
        if (StringUtils.isEmpty(method)) {
            return ApiResult.newSuccess();
        }
        switch (method) {
            case "searchAliexpressProduct":
                try {
                    CQuery<EsAliexpressProductListingRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsAliexpressProductListingRequest>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空！");
                    EsAliexpressProductListingRequest querySearch = cquery.getSearch();
                    Asserts.isTrue(querySearch != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空！");
                    //默认不查产品
                    querySearch.setIsSeleteProduct(false);
                    EsAliexpressProductListingResponse esResponse = aliexpressEsExtendService.list(cquery);
                    return ApiResult.newSuccess(esResponse);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    return ApiResult.newError(e.getMessage());
                }
            case "synchSmtProductList": //同步产品
                AliexpressProductCriteria syncProductCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(syncProductCriteria != null && StringUtils.isNotBlank(syncProductCriteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> syncProductAccountList = CommonUtils
                        .splitList(syncProductCriteria.getAliexpressAccountNumber(), ",");
                syncProductAccountList.forEach(accountNumber -> {
                    AliexpressExecutors.executeSyncAccountProduct(() -> {
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                        SynchItemOpenCall call = new SynchItemOpenCall();
                        call.syncAliexpressProductList(saleAccountByAccountNumber, syncProductCriteria);
                    });
                });
                return ApiResult.newSuccess("正在后台同步中");
            case "synchSmtProductListAll": //同步产品
                AliexpressProductCriteria syncProductCriteriaAll = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(syncProductCriteriaAll != null && StringUtils.isNotBlank(syncProductCriteriaAll.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> syncProductAccountListAll = CommonUtils
                        .splitList(syncProductCriteriaAll.getAliexpressAccountNumber(), ",");
                syncProductAccountListAll.forEach(accountNumber -> {
                    AliexpressExecutors.executeSyncAccountProductAll(() -> {
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                        SynchItemOpenCall call = new SynchItemOpenCall();
                        syncProductCriteriaAll.setProductStatusType("onSelling,offline,auditing,editingRequired");
                        syncProductCriteriaAll.setIsSynchAll(true);
                        call.syncAliexpressProductList(saleAccountByAccountNumber, syncProductCriteriaAll);
                    });
                });
                return ApiResult.newSuccess("正在后台同步中");
            case "syncProductGroups": // 同步分组
                AliexpressProductCriteria syncGroupCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(syncGroupCriteria != null && StringUtils.isNotBlank(syncGroupCriteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> syncGroupAccountList = CommonUtils.splitList(syncGroupCriteria.getAliexpressAccountNumber(), ",");
                syncGroupAccountList.forEach(accountNumber -> {
                    AliexpressExecutors.synchGroup((rsp) -> {
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                        GroupOpenCall call = new GroupOpenCall();
                        call.syncAliexpressProductGroups(saleAccountByAccountNumber);
                    });
                });
                return ApiResult.newSuccess("正在后台同步中");
            case "downProductGroups": // 下载分组
                AliexpressProductCriteria downGroupCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(downGroupCriteria != null && StringUtils.isNotBlank(downGroupCriteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                String account = downGroupCriteria.getAliexpressAccountNumber();
                if (StringUtils.indexOf(account, ",") != -1) {
                    return ApiResult.newError("只支持一个产品组下载");
                }
                SaleAccountAndBusinessResponse saleAccount = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT,
                                account);
                GroupOpenCall groupCall = new GroupOpenCall();
                groupCall.syncAliexpressProductGroups(saleAccount);

                OutputStream os = null;
                try {
                    String fileName = "smtGroup-" + com.estone.erp.publish.common.util.POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
                    //fileName = POIUtils.getEncodeFileName(request, fileName);
                    response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                    os = response.getOutputStream();

                    AliexpressProductGroupExample groupExample = new AliexpressProductGroupExample();
                    groupExample.createCriteria().andAccountNumberEqualTo(account);
                    List<AliexpressProductGroup> aliexpressProductGroups = aliexpressProductGroupService
                            .selectByExample(groupExample);
                    if (CollectionUtils.isNotEmpty(aliexpressProductGroups)) {
                        final String[] headers = {"group_id", "group_name", "full_path_code"};
                        final List<List<String>> awLists = new ArrayList<>();
                        com.estone.erp.publish.common.util.POIUtils.createExcel(headers, aliexpressProductGroups, group -> {
                            awLists.clear();
                            List<String> awList = new ArrayList<>(headers.length);
                            awList.add(POIUtils.transferObj2Str(group.getGroupId()));
                            awList.add(POIUtils.transferObj2Str(group.getGroupName()));
                            awList.add(POIUtils.transferObj2Str(group.getFullPathCode()));
                            awLists.add(awList);
                            return awLists;
                        }, true, os);
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    IOUtils.closeQuietly(os);
                }
                return ApiResult.newSuccess();
            case "downFreightTemplates": // 下载运费模板
                AliexpressProductCriteria aliexpressProductCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(aliexpressProductCriteria != null && StringUtils.isNotBlank(aliexpressProductCriteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                String aliexpressAccount = aliexpressProductCriteria.getAliexpressAccountNumber();
                if (StringUtils.isEmpty(aliexpressAccount)) {
                    return ApiResult.newError("请选择要下载的账号");
                }
                List<String> strings = CommonUtils.splitList(aliexpressAccount, ",");
                excelSend.downFreightTemplatesSend(strings);
                return ApiResult.newSuccess("请到excel下载日志查看结果！");
            case "syncFreightTemplates": //同步运费模板
                AliexpressProductCriteria syncTemplateCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductCriteria>() {
                });
                Asserts.isTrue(syncTemplateCriteria != null && StringUtils.isNotBlank(syncTemplateCriteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> syncTemplateAccountList = CommonUtils.splitList(syncTemplateCriteria.getAliexpressAccountNumber(), ",");
                syncTemplateAccountList.forEach(accountNumber -> {
                    AliexpressExecutors.synchTemplate((rsp) -> {
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                        FreightTemplateOpenCall call = new FreightTemplateOpenCall();
                        call.getFreightTemplateList(saleAccountByAccountNumber);
                    });
                });
                return ApiResult.newSuccess("正在后台同步中");
            case "checkEditPrice"://修改价格前校验是否存在重复商品编码
                String productIds = requestParam.getArgs();
                List<Long> repeatSkuCodeList = aliexpressEsExtendService.repeatSkuCode(CommonUtils.splitLongList(productIds, ","));
                if (CollectionUtils.isNotEmpty(repeatSkuCodeList)) {
                    return ApiResult.newError(String.format("itemid:%s存在相同商品编码!", StringUtils.join(repeatSkuCodeList, ",")));
                }
                return ApiResult.newSuccess();
            case "batchEditProductPrice": //批量修改价格
                Map<String, String> batchUpdatePriceMap = requestParam.getArgsValue(new TypeReference<HashMap<String, String>>() {
                });
                String editPriceIdStr = batchUpdatePriceMap.get("ids");
                String expectedMargin = batchUpdatePriceMap.get("expectedMargin");
                String sfmCode = batchUpdatePriceMap.get("sfmCode");
                //汇率
                String customCurrencyRate = batchUpdatePriceMap.get("customCurrencyRate");
                //调价幅度
                String adjustmentRange = batchUpdatePriceMap.get("adjustmentRange");
                //直接调价
                String expectPrice = batchUpdatePriceMap.get("expectPrice");
                Asserts.isTrue(StringUtils.isNotBlank(editPriceIdStr), ErrorCode.PARAM_EMPTY_ERROR);
                Asserts.isTrue(StringUtils.isNotBlank(expectedMargin) || StringUtils.isNotBlank(adjustmentRange) || StringUtils.isNotBlank(expectPrice), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> editPriceIds = CommonUtils.splitList(editPriceIdStr, ",");
                if (CollectionUtils.isNotEmpty(editPriceIds)) {
                    Double expectedMarginVal = null;
                    if (StringUtils.isNotBlank(expectedMargin)) {
                        expectedMarginVal = Double.valueOf(expectedMargin);
                    }
                    Double adjustmentRangeVal = null;
                    if (StringUtils.isNotBlank(adjustmentRange)) {
                        adjustmentRangeVal = Double.valueOf(adjustmentRange);
                    }
                    if (StringUtils.isBlank(sfmCode)) {
                        sfmCode = "SMTPY";
                    }
                    //直接调价
                    Double expectPriceVal = null;
                    if (StringUtils.isNotBlank(expectPrice)) {
                        expectPriceVal = Double.valueOf(expectPrice);
                    }
                    String errorInfo = aliexpressEsExtendService.batchEditProductPriceNew(editPriceIds, expectedMarginVal,
                            adjustmentRangeVal, sfmCode, customCurrencyRate, expectPriceVal);
                    if (StringUtils.isNotBlank(errorInfo)) {
                        return ApiResult.newError(errorInfo);
                    }
                }
                return ApiResult.newSuccess("修改成功！");
            case "batchEditProductStock": //批量修改库存
                Map<String, String> batchUpdateStockMap = requestParam.getArgsValue(new TypeReference<HashMap<String, String>>() {
                });
                String editStockIdStr = batchUpdateStockMap.get("ids");
                String stock = batchUpdateStockMap.get("stock");
                Asserts.isTrue(StringUtils.isNotBlank(editStockIdStr), ErrorCode.PARAM_EMPTY_ERROR);
                Asserts.isTrue(StringUtils.isNotBlank(stock), ErrorCode.PARAM_EMPTY_ERROR);
                List<String> editStockIds = CommonUtils.splitList(editStockIdStr, ",");
                if (CollectionUtils.isNotEmpty(editStockIds)) {
                    String errorInfo = aliexpressEsExtendService.batchEditProductStock(editStockIds, stock, false, true, true, null, false);
                    if (StringUtils.isNotBlank(errorInfo)) {
                        return ApiResult.newError(errorInfo);
                    }
                }
                return ApiResult.newSuccess();
            case "batchUpdateImg": //批量修改图片
                List<UpdateImgBean> updateImgAliexpressProductList = requestParam.getArgsValue(new TypeReference<List<UpdateImgBean>>() {
                });
                if (CollectionUtils.isNotEmpty(updateImgAliexpressProductList)) {
                    String userName = WebUtils.getUserName();
                    List<Long> productIdList = updateImgAliexpressProductList.stream().map(t -> t.getProductId()).collect(Collectors.toList());
                    EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
                    listingRequest.setProductIdList(productIdList);
                    listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.editingRequired.getCode());
                    listingRequest.setQueryFields(new String[]{"productId"});
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);

                    if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                        Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
                        updateImgAliexpressProductList = updateImgAliexpressProductList.stream().filter(t -> productIdSet.contains(t.getProductId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(updateImgAliexpressProductList)) {
                            updateImgAliexpressProductList.forEach(t -> {
                                AliexpressExecutors.updateImg(rsp -> {
                                    aliexpressEsExtendService.updateImg(t, userName);
                                });
                            });
                        }
                    }
                }
                return ApiResult.newSuccess();
            case "batchUpdateSonSkuImg": //批量修改子sku图片
                List<EsAliexpressProductListing> updateSonSkuImgAliexpressProductList = requestParam.getArgsValue(new TypeReference<List<EsAliexpressProductListing>>() {
                });
                if (CollectionUtils.isNotEmpty(updateSonSkuImgAliexpressProductList)) {
                    String userName = WebUtils.getUserName();

                    EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
                    listingRequest.setProductIdList(updateSonSkuImgAliexpressProductList.stream().map(t -> t.getProductId()).collect(Collectors.toList()));
                    listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.editingRequired.getCode());
                    listingRequest.setQueryFields(new String[]{"productId"});
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
                    if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                        Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());

                        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
                        Map<Long, List<EsAliexpressProductListing>> productIdMap = updateSonSkuImgAliexpressProductList.stream()
                                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
                        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                            Long key = longListEntry.getKey();
                            if (productIdSet.contains(key.longValue())) {
                                List<EsAliexpressProductListing> value = longListEntry.getValue();
                                String aliexpressAccountNumber = value.get(0).getAliexpressAccountNumber();
                                AliexpressExecutors.updateSonskuImg(() -> {
                                    SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap
                                            .get(aliexpressAccountNumber);
                                    if (saleAccountAndBusinessResponse == null) {
                                        saleAccountAndBusinessResponse = AccountUtils
                                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT,
                                                        aliexpressAccountNumber);
                                        accountMap.put(aliexpressAccountNumber, saleAccountAndBusinessResponse);
                                    }
                                    aliexpressEsExtendService.updateSonSkuImg(value, saleAccountAndBusinessResponse, userName);
                                });
                            }
                        }
                    }

                }
                return ApiResult.newSuccess();
            case "batchUpdateSubjectAndDetail": //批量修改标题描述
                Map<String, String> updateSubjectAndDetailMap = requestParam.getArgsValue(new TypeReference<HashMap<String, String>>() {
                });
                String updateSubjectAndDetailIdStr = updateSubjectAndDetailMap.get("ids");
                String isUpdateSubjectStr = updateSubjectAndDetailMap.get("isUpdateSubject");
                String isUpdateDetailStr = updateSubjectAndDetailMap.get("isUpdateDetail");
                Asserts.isTrue(StringUtils.isNotBlank(updateSubjectAndDetailIdStr), ErrorCode.PARAM_EMPTY_ERROR);
                Boolean isUpdateSubject = "1".equals(isUpdateSubjectStr) ? true : false;
                Boolean isUpdateDetail = "1".equals(isUpdateDetailStr) ? true : false;
                Asserts.isTrue(isUpdateSubject || isUpdateDetail, ErrorCode.PARAM_EMPTY_ERROR);
                // 批量修改标题描述
                String errorInfo = aliexpressEsExtendService.batchUpdateSubjectAndDetail(updateSubjectAndDetailIdStr, isUpdateSubject, isUpdateDetail);
                if (StringUtils.isNotBlank(errorInfo)) {
                    return ApiResult.newError(errorInfo);
                }
                return ApiResult.newSuccess();
            case "batchReplaceSubjectAndDetail": //批量替换标题描述
                Map<String, String> replaceSubjectAndDetailMap = requestParam.getArgsValue(new TypeReference<HashMap<String, String>>() {
                });
                String replaceIdStr = replaceSubjectAndDetailMap.get("ids");
                String isReplaceSubjectStr = replaceSubjectAndDetailMap.get("isReplaceSubjectStr");
                String isReplaceDetailStr = replaceSubjectAndDetailMap.get("isReplaceDetailStr");
                String oldWord = replaceSubjectAndDetailMap.get("oldWord").trim();
                //新词不用校验必填可以替换成空串
                String newWord = replaceSubjectAndDetailMap.get("newWord").trim();
                Asserts.isTrue(StringUtils.isNotBlank(replaceIdStr) && StringUtils.isNotBlank(oldWord), ErrorCode.PARAM_EMPTY_ERROR);
                Boolean isReplaceSubject = "1".equals(isReplaceSubjectStr) ? true : false;
                Boolean isReplaceDetail = "1".equals(isReplaceDetailStr) ? true : false;

                String errorMsg = aliexpressEsExtendService.batchReplaceSubjectAndDetail(replaceIdStr, oldWord, newWord, isReplaceSubject, isReplaceDetail);
                if (StringUtils.isNotBlank(errorMsg)) {
                    return ApiResult.newError(errorMsg);
                }
                return ApiResult.newSuccess();
            case "batchUpdateGrossWeight": //修改重量
                List<EsAliexpressProductListing> updateGrossWeightList = requestParam.getArgsValue(new TypeReference<List<EsAliexpressProductListing>>() {
                });
                if (CollectionUtils.isNotEmpty(updateGrossWeightList)) {
                    Map<String, String> productWeightMap = new HashMap<String, String>();
                    List<UpdatePriceEntity> returnResultList = new ArrayList<>();
                    for (EsAliexpressProductListing item : updateGrossWeightList) {
                        String accountNumber = item.getAliexpressAccountNumber();
                        if (StringUtils.isBlank(accountNumber) || item.getProductId() == null
                                || StringUtils.isBlank(item.getGrossWeight())) {
                            continue;
                        }
                        String productId = String.valueOf(item.getProductId());
                        UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                        updatePriceEntity.setSeller(accountNumber);
                        updatePriceEntity.setProductId(productId);
                        returnResultList.add(updatePriceEntity);
                        productWeightMap.put(productId, item.getGrossWeight());
                    }
                    aliexpressEsExtendService.updateGrossWeight(returnResultList, productWeightMap, WebUtils.getUserName(), false);
                } else {
                    return ApiResult.newError("无有效数据！");
                }
                return ApiResult.newSuccess();
            case "transTemp": //转模板
                CQuery<AliexpressProductCriteria> transTempCquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressProductCriteria>>() {
                });
                Asserts.isTrue(transTempCquery != null && transTempCquery.getSearch() != null && StringUtils.isNotBlank(transTempCquery.getSearch().getProductIdStr()), ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");

                //页面产品id 需要去重
                String productIdStr = transTempCquery.getSearch().getProductIdStr();
                List<Long> longs = CommonUtils.splitLongList(productIdStr, ",");
                String userName = WebUtils.getUserName();
                for (Long aLong : longs) {
                    AliexpressExecutors.tranTemp(() -> {
                        aliexpressEsExtendService.transTemp(aLong, userName);
                    });
                }
                return ApiResult.newSuccess();
            case "download": //下载
                CQuery<EsAliexpressProductListingRequest> downQuery = requestParam.getArgsValue(new TypeReference<CQuery<EsAliexpressProductListingRequest>>() {
                });
                Asserts.isTrue(downQuery != null && downQuery.getSearch() != null && CollectionUtils.isNotEmpty(downQuery.getSearch().getFields()), ErrorCode.PARAM_EMPTY_ERROR, "参数为空，请检查！");
                EsAliexpressProductListingRequest downSearch = downQuery.getSearch();
                Asserts.isTrue(downSearch != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空！");
                ResponseJson responseJson = excelSend.downloadSend(ExcelTypeEnum.download.getCode(), downQuery);

                if (!responseJson.isSuccess()) {
                    return ApiResult.newError(responseJson.getMessage());
                }

                return ApiResult.newSuccess();
            case "downloadState": //下载32国改价模板
                CQuery<EsAliexpressProductListingRequest> downloadStateQuery = requestParam.getArgsValue(new TypeReference<CQuery<EsAliexpressProductListingRequest>>() {
                });
                Asserts.isTrue(downloadStateQuery != null && downloadStateQuery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                Asserts.isTrue(StringUtils.isNotBlank(downloadStateQuery.getSearch().getShippingMethodCode()), ErrorCode.PARAM_EMPTY_ERROR, "请选择物流方式！");
                ResponseJson downloadSend = excelSend
                        .downloadSend(ExcelTypeEnum.downloadState.getCode(), downloadStateQuery);
                if (!downloadSend.isSuccess()) {
                    return ApiResult.newError(downloadSend.getMessage());
                }
                return ApiResult.newSuccess();

            case "downloadStateResult": //下载32国价格信息 根据主键id下载32国 价格信息
                CQuery<EsAliexpressProductListingRequest> downloadStateResultQuery = requestParam.getArgsValue(new TypeReference<CQuery<EsAliexpressProductListingRequest>>() {
                });
                Asserts.isTrue(downloadStateResultQuery != null && downloadStateResultQuery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");

                ResponseJson downloadStateResult = excelSend
                        .downloadStateResult(ExcelTypeEnum.downloadStateResult.getCode(), downloadStateResultQuery);
                if (!downloadStateResult.isSuccess()) {
                    return ApiResult.newError(downloadStateResult.getMessage());
                }
                return ApiResult.newSuccess();
            case "downloadCount": //
                AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
                AliexpressConfigExample configExample = new AliexpressConfigExample();
                configExample.createCriteria().andUsableEqualTo(true);
                List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);

                List<AliexpressAccountCount> countList = new ArrayList<>();
                for (AliexpressConfig aliexpressConfig : aliexpressConfigs) {

                    AliexpressAccountCount aliexpressAccountCount = new AliexpressAccountCount();
                    countList.add(aliexpressAccountCount);
                    aliexpressAccountCount.setAccount(aliexpressConfig.getAccount());

                    EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
                    listingRequest.setAliexpressAccountNumber(aliexpressConfig.getAccount());
                    listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                    listingRequest.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "articleNumber"});
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                            .getEsAliexpressProductListing(listingRequest);
                    if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                        aliexpressAccountCount.setProductSum(0);
                        aliexpressAccountCount.setSkuSum(0);
                    } else {
                        aliexpressAccountCount.setSkuSum(esAliexpressProductListing.size());
                        Map<Long, List<EsAliexpressProductListing>> collect = esAliexpressProductListing.stream()
                                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
                        aliexpressAccountCount.setProductSum(collect.size());
                    }
                }


                final String[] countResult = {"店铺账号", "在线sku总数", "商品id总数"};

                OutputStream downloadCountOs = null;
                try {
                    String fileName = "速卖通在线数据统计-" + POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
                    response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                    downloadCountOs = response.getOutputStream();
                    if (CollectionUtils.isNotEmpty(countList)) {
                        final List<List<String>> awLists = new ArrayList<>();
                        com.estone.erp.publish.common.util.POIUtils.createExcel(countResult, countList, item -> {
                            awLists.clear();
                            List<String> awList = new ArrayList<>(countResult.length);
                            awList.add(POIUtils.transferObj2Str(item.getAccount()));
                            awList.add(POIUtils.transferObj2Str(item.getSkuSum()));
                            awList.add(POIUtils.transferObj2Str(item.getProductSum()));
                            awLists.add(awList);
                            return awLists;

                        }, true, downloadCountOs);
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    if (downloadCountOs != null) {
                        IOUtils.closeQuietly(downloadCountOs);
                    }
                }
                return ApiResult.newSuccess();
            case "updateTaxType"://修改计税方式
                try {
                    String name = WebUtils.getUserName();
                    CQuery<EsAliexpressProductListingRequest> updateTaxTypeCquery = requestParam.getArgsValue(new TypeReference<CQuery<EsAliexpressProductListingRequest>>() {
                    });
                    Asserts.isTrue(updateTaxTypeCquery != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空！");
                    EsAliexpressProductListingRequest querySearch = updateTaxTypeCquery.getSearch();
                    Asserts.isTrue(querySearch != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空！");

                    String setTaxType = querySearch.getSetTaxType();
                    List<String> strings1 = Arrays.asList("1", "2");
                    if (StringUtils.isBlank(setTaxType) || !strings1.contains(setTaxType)) {
                        return ApiResult.newError("请设置关税类型");
                    }
                    Set<Long> productIdSet = new HashSet<>();

                    CQuery<EsAliexpressProductListingRequest> popCQuery = new CQuery<>();
                    //默认不查产品
                    querySearch.setIsSeleteProduct(false);
                    popCQuery.setSearch(querySearch);
                    querySearch.setPageFields(new String[]{"id", "aliexpressAccountNumber", "productId", "taxType"});

                    int errorCount = 0;

                    int offset = 0;
                    int limit = 1000;
                    while (true) {
                        try {
                            popCQuery.setOffset(offset++);
                            popCQuery.setLimit(limit);
                            EsAliexpressProductListingResponse esResponse = aliexpressEsExtendService.list(popCQuery);
                            Page<EsAliexpressProductListing> esProductListingPage = esResponse.getEsProductListingPage();
                            if (esProductListingPage == null || CollectionUtils.isEmpty(esProductListingPage.getContent())) {
                                //跳出循环
                                break;
                            }
                            List<EsAliexpressProductListing> productListingList = esProductListingPage.getContent();

                            Map<Long, List<EsAliexpressProductListing>> longListMap = productListingList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : longListMap.entrySet()) {
                                Long key = longListEntry.getKey();
                                List<EsAliexpressProductListing> value = longListEntry.getValue();
                                if (productIdSet.contains(key)) {
                                    continue;
                                }
                                EsAliexpressProductListing esAliexpressProductListing = value.get(0);
                                String taxType = esAliexpressProductListing.getTaxType();
                                if (StringUtils.isNotBlank(taxType) && StringUtils.equalsIgnoreCase(taxType, setTaxType)) {
                                    continue;
                                }

                                AliexpressExecutors.UPLOAD_TAX_TYPE_POOL.execute(() -> {
                                    //记录处理报告 并发送队列
                                    AliexpressProductLog productLog = new AliexpressProductLog();
                                    productLog.setOperateType(OperateLogTypeEnum.UPDATE_TAX_TYPE.getCode());
                                    productLog.setAccountNumber(value.get(0).getAliexpressAccountNumber());
                                    productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                                    productLog.setProductId(key);
                                    productLog.setOperator(name);
                                    aliexpressProductLogService.insert(productLog);
                                    TaxTypeBean taxTypeBean = new TaxTypeBean();
                                    taxTypeBean.setSetTaxType(setTaxType);
                                    taxTypeBean.setProductListingList(value);
                                    taxTypeBean.setProductLog(productLog);
                                    rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_TAX_TYPE_ROUTE_KEY, JSON.toJSON(taxTypeBean));
                                });
                                productIdSet.add(key);
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            errorCount++;
                            if (errorCount > 20) {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    return ApiResult.newError(e.getMessage());
                }
                return ApiResult.newSuccess("请求成功，结果请查看处理报告");
        }
        return ApiResult.newSuccess();
    }

    //获取产品分组
    @GetMapping(value = "/getGroup")
    public ApiResult<?> getGroups(@RequestParam(value = "accountNumber", required = true) String accountNumber) {
        Asserts.isTrue(StringUtils.isNotBlank(accountNumber), ErrorCode.PARAM_EMPTY_ERROR);
        List<String> accountNumberList = new ArrayList<>();
        if (accountNumber.indexOf(",") == -1) {
            accountNumberList.add(accountNumber);
        } else {
            String[] accountNumberArr = accountNumber.split(",");
            accountNumberList.addAll(Arrays.asList(accountNumberArr));
        }
        List<AliexpressProductGroup> groupList = new ArrayList<>();
        for (Iterator<String> iterator = accountNumberList.iterator(); iterator.hasNext(); ) {
            String aliexpressAccountNumber = iterator.next();
            List<AliexpressProductGroup> groups = aliexpressProductGroupService
                    .getGroupsByAccountNumber(aliexpressAccountNumber);
            groupList.addAll(groups);
        }
        return ApiResult.newSuccess(groupList);
    }

    /**
     * 根据主键id同步
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/synchSmtProductById")
    public ApiResult<?> synchSmtProductById(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        List<String> ids = requestParam.getArgsValue(new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("请求参数错误：id不能为空");
        }
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdStr(StringUtils.join(ids, ","));
        esRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber"});
        esRequest.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("没有需要同步的产品");
        }

        Map<Long, List<EsAliexpressProductListing>> productMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));

        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productMap.entrySet()) {
            try {
                List<EsAliexpressProductListing> value = longListEntry.getValue();
                EsAliexpressProductListing product = value.get(0);
                String aliexpressAccountNumber = product.getAliexpressAccountNumber();
                SaleAccountAndBusinessResponse saleAccountByAccountNumber = accountMap.get(aliexpressAccountNumber);
                if (saleAccountByAccountNumber == null) {
                    saleAccountByAccountNumber = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    accountMap.put(aliexpressAccountNumber, saleAccountByAccountNumber);
                }
                SynchItemOpenCall call = new SynchItemOpenCall();
                call.syncAliexpressProductInfo(saleAccountByAccountNumber, product.getProductId());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
//        aliexpressEsExtendService.syncProductByIds(ids);
        return ApiResult.newSuccess("同步完成");
    }

    /**
     * 根据产品id同步产品
     *
     * @param criteria
     * @return
     */
    @PostMapping(value = "/syncProductsByProductId")
    public ApiResult<?> syncProductsByProductId(@RequestBody(required = true) AliexpressProductCriteria criteria) {

        Asserts.isTrue(criteria != null, ErrorCode.PARAM_EMPTY_ERROR);
        String productIdStr = criteria.getProductIdStr();
        Asserts.isTrue(StringUtils.isNotBlank(productIdStr), ErrorCode.PARAM_EMPTY_ERROR);
        Asserts.isTrue(StringUtils.isNotBlank(criteria.getAliexpressAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
        List<Long> longs = CommonUtils.splitLongList(criteria.getProductIdStr(), ",");
        List<Long> productIdList = CommonUtils.splitLongList(productIdStr, ",");
        //过滤3开头的产品id
        productIdList = productIdList.stream().filter(t -> !StringUtils.startsWith(t.toString(), "3")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIdList)) {
            return ApiResult.newError("3开头的产品id不可以同步！");
        }

        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, criteria.getAliexpressAccountNumber());

        longs.forEach(productId -> {
            SynchItemOpenCall call = new SynchItemOpenCall();
            call.syncAliexpressProductInfo(saleAccountByAccountNumber, productId);
        });
        return ApiResult.newSuccess("同步完成");
    }

    /**
     * 批量设置分组
     *
     * @return
     */
    @PostMapping(value = "/batchSetGroup")
    public ApiResult<?> batchSetGroup(@RequestBody(required = true) List<AliexpressProductCriteria> criteriaList) {

        for (AliexpressProductCriteria criteria : criteriaList) {
            String productIdStr = criteria.getProductIdStr();
            String groupIdStr = criteria.getGroupIdStr();
            Boolean retain = criteria.getRetain();

            if (StringUtils.isBlank(productIdStr) || groupIdStr == null) {
                continue;
            }

            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdStr(productIdStr);
            esRequest.setQueryFields(new String[]{"id"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                continue;
            }
            List<String> ids = esAliexpressProductListing.stream().map(t -> t.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                aliexpressEsExtendService.batchSetGroup(ids, groupIdStr, retain);
            }
        }
        return ApiResult.newSuccess("后台处理中，请到处理报告查看结果！");
    }

    /**
     * 设置运费模板
     *
     * @param requestCriteriaList
     * @return
     */
    @PostMapping(value = "/batchSetFreightTemplateId")
    public ApiResult<?> batchSetFreightTemplateId(@RequestBody(required = true) List<AliexpressProductCriteria> requestCriteriaList) {
        for (AliexpressProductCriteria requestCriteria : requestCriteriaList) {
            //产品idList
            String productIdStr = requestCriteria.getProductIdStr();
            Long freightTemplateId = requestCriteria.getFreightTemplateId();
            if (StringUtils.isBlank(productIdStr) || freightTemplateId == null) {
                continue;
            }

            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdStr(productIdStr);
            esRequest.setQueryFields(new String[]{"id"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                continue;
            }

            List<String> ids = esAliexpressProductListing.stream().map(t -> t.getId()).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(ids)) {
                aliexpressEsExtendService.batchSetEditsimpleproductfiled(ids, "freightTemplateId",
                        freightTemplateId.toString(), WebUtils.getUserName());
            }
        }
        return ApiResult.newSuccess("后台处理中，请到处理报告查看结果！");
    }

    /**
     * 运费模板和产品分组检查店铺产品
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping(value = "/checkAccountProductId")
    public ApiResult<?> checkAccountProductId(@RequestBody(required = true) AliexpressProductCriteria requestCriteria) {
        //产品idList
        String productIdStr = requestCriteria.getProductIdStr();
        String aliexpressAccountNumber = requestCriteria.getAliexpressAccountNumber();
        if (StringUtils.isBlank(productIdStr) || StringUtils.isBlank(aliexpressAccountNumber)) {
            return ApiResult.newError("参数不能为空！");
        }

        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setProductIdStr(productIdStr);
        esRequest.setAliexpressAccountNumber(aliexpressAccountNumber);
        esRequest.setQueryFields(new String[]{"id", "productId"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("请确认店铺和产品id是否存在！");
        }

        List<Long> longs = CommonUtils.splitLongList(productIdStr, ",");

        //数据库中的产品id
        List<Long> dbProductIdList = esAliexpressProductListing.stream().map(t -> t.getProductId())
                .collect(Collectors.toList());

        longs.removeAll(dbProductIdList);

        if (CollectionUtils.isNotEmpty(longs)) {
            return ApiResult.newError(StringUtils.join(longs, ",") + "不存在该店铺");
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/batchSetDeliveryTime")
    public ApiResult<?> batchSetDeliveryTime(@RequestBody(required = true) EsAliexpressProductListingRequest requestCriteria) {
        String idStr = requestCriteria.getIdStr();

        String aliexpressAccountNumber = requestCriteria.getAliexpressAccountNumber();

        Integer deliveryTime = requestCriteria.getDeliveryTime();

        if (StringUtils.isBlank(idStr) && StringUtils.isBlank(aliexpressAccountNumber)) {
            return ApiResult.newError("请选择要设置的数据或账号！");
        }
//        if(deliveryTime > 7 || deliveryTime < 1) {
//            return ApiResult.newError("请输入不小于1，不大于7的整数！");
//        }

        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotBlank(idStr)) {
            idList = CommonUtils.splitList(idStr, ",");
        }
        if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setAliexpressAccountNumber(aliexpressAccountNumber);
            esRequest.setProductStatusType("onSelling, auditing");
            esRequest.setQueryFields(new String[]{"id"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                idList = esAliexpressProductListing.stream().map(t -> t.getId()).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(idList)) {
            ApiResult.newError("店铺没有在线审核状态数据需要修改！");
        }

        List<List<String>> lists = PagingUtils.pagingList(idList, 100);
        CountDownLatch countDownLatch = new CountDownLatch(lists.size());
        String userName = WebUtils.getUserName();
        for (List<String> idsList : lists) {
            AliexpressExecutors.submitUpdate(new Runnable() {
                @Override
                public void run() {
                    try {
                        aliexpressEsExtendService.batchSetEditsimpleproductfiled(idsList, "deliveryTime",
                                deliveryTime.toString(), userName);
                    } catch (Exception e) {
                    } finally {
                        countDownLatch.countDown();
                    }
                }
            });
        }
        try {
            countDownLatch.await(2, TimeUnit.HOURS);
        } catch (Exception e) {
        }
        return ApiResult.newSuccess(String.format("设置成功！数据:%s条", idList.size()));
    }

    @PostMapping(value = "/batchSetReduceStrategy")
    public ApiResult<?> batchSetReduceStrategy(@RequestBody EsAliexpressProductListingRequest requestCriteria) {
        String idStr = requestCriteria.getIdStr();
        String accountNumber = requestCriteria.getAliexpressAccountNumber();
        String reduceStrategy = requestCriteria.getReduceStrategy();
        if (StringUtils.isBlank(idStr) && StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("参数不能为空！");
        }

        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotBlank(idStr)) {
            idList = CommonUtils.splitList(idStr, ",");
        }
        if (StringUtils.isNotBlank(accountNumber)) {
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setAliexpressAccountNumber(accountNumber);
            esRequest.setQueryFields(new String[]{"id"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                idList = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getId).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(idList)) {
            ApiResult.newError("店铺没有数据！");
        }

        List<List<String>> lists = PagingUtils.pagingList(idList, 100);
        CountDownLatch countDownLatch = new CountDownLatch(lists.size());
        String userName = WebUtils.getUserName();
        for (List<String> idsList : lists) {
            AliexpressExecutors.submitUpdate(() -> {
                try {
                    aliexpressEsExtendService.batchSetEditsimpleproductfiled(idsList, "reduceStrategy",
                            reduceStrategy, userName);
                } catch (Exception e) {
                    log.error(e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(2, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            log.error("应用中断异常", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return ApiResult.newSuccess(String.format("设置成功！数据:%s条", idList.size()));
    }

    /**
     * 批量上下架 3个条件的一个
     *
     * @param requestCriteria
     */
    @PostMapping(value = "/batchOnlineOrOffProduct")
    public ApiResult<?> batchOnlineProduct(@RequestBody(required = true) AliexpressProductCriteria requestCriteria) {
        Asserts.isTrue(requestCriteria != null, ErrorCode.PARAM_EMPTY_ERROR);
        Asserts.isTrue(StringUtils.isNotBlank(requestCriteria.getIdStr()) && requestCriteria.getType() != null, ErrorCode.PARAM_EMPTY_ERROR);

        Integer type = requestCriteria.getType();
        if (!type.equals(AliexpressProductManipulateTypeEnum.OFFLINE.getType())
                && !type.equals(AliexpressProductManipulateTypeEnum.ONLINE.getType())) {
            return ApiResult.newError("状态码有误！");
        }
        aliexpressEsExtendService.batchOnlineOrOfflineProduct(CommonUtils.splitList(requestCriteria.getIdStr(), ","), type, true, requestCriteria.getOptimized());
        return ApiResult.newSuccess("执行完毕");
    }

    /**
     * 批量修改侵权产品 信息替换
     *
     * @param criteria
     * @return
     */
    @PostMapping(value = "/batchUpdateTortProduct")
    public ApiResult<?> batchUpdateTortProduct(@RequestBody(required = true) AliexpressProductCriteria criteria) {
        Asserts.isTrue(criteria != null && StringUtils.isNotBlank(criteria.getIdStr()), ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");

        String idStr = criteria.getIdStr();
        List<String> ids = CommonUtils.splitList(idStr, ",");
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdList(ids);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("找不到产品");
        }
        Map<String, List<EsAliexpressProductListing>> accountMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(EsAliexpressProductListing::getAliexpressAccountNumber));
        String updateImg = SmtConstant.updateImg;
        String updateText = SmtConstant.updateText;
        accountMap.forEach((k, v) -> {
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, k);
            for (EsAliexpressProductListing esProduct : v) {
                AliexpressProduct product = new AliexpressProduct();
                BeanUtils.copyProperties(esProduct, product);
                product.setOperator(WebUtils.getUserName());
                AliexpressExecutors.submitUpdate(() -> {
                    aliexpressEsExtendService.updateTortProduct(saleAccountByAccountNumber, product, updateImg, updateText);
                });
            }
        });
        return ApiResult.newSuccess("后台执行中，结果请查看处理报告！");
    }

    /**
     * 批量修改图片 请求到页面数据
     *
     * @param criteria
     * @return
     */
    @PostMapping(value = "/batchUpdateImg")
    public ApiResult<?> batchUpdateImg(@RequestBody(required = true) AliexpressProductCriteria criteria) {
        String idStr = criteria.getIdStr();
        if (StringUtils.isEmpty(idStr)) {
            return ApiResult.newError("id必填");
        }
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdStr(idStr);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("找不到产品");
        }

        esAliexpressProductListing = esAliexpressProductListing.stream()
                .filter(o -> o.getProductId() != null).collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getProductId()))),
                        ArrayList::new));

        Map<String, AliexpressEsExtend> extendMap = new HashMap<>();
        esAliexpressProductListing.forEach(t -> {
            try {
                String articleNumber = t.getArticleNumber();
                List<String> images = FmsUtils.getSmtImgs(articleNumber, null, new Boolean[]{true});

                Long productId = t.getProductId();
                String aliexpressAccountNumber = t.getAliexpressAccountNumber();
                AliexpressEsExtend extend = aliexpressEsExtendService.selectByAccountandProductId(aliexpressAccountNumber, productId);

                String spu = t.getSpu();
                if (StringUtils.isBlank(spu)) {
                    spu = ProductUtils.getMainSku(articleNumber);
                }

                //去除了宽图(正常图片池选择)
                List<String> strings = AliexpressContentUtils.imgPriorityForList(images);
                extend.setImages(strings);

                //营销图片池选择
                extend.setMarketImages(images);

                //新白底图取产品系统SMT白底图
                for (String image : images) {
                    if (StringUtils.containsIgnoreCase(image, spu + ".")) {
                        extend.setSquareImg(image);
                        break;
                    }
                }

                if (StringUtils.isBlank(extend.getLongImg())) {
                    for (String image : images) {
                        if (StringUtils.indexOf(image, "-effect-copy.") != -1) {
                            extend.setLongImg(image);
                            break;
                        }
                    }
                }
                extendMap.put(t.getId(), extend);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        EsAliexpressProductListingResponse response = new EsAliexpressProductListingResponse();
        response.setEsAliexpressProductListing(esAliexpressProductListing);
        response.setExtendMap(extendMap);
        return ApiResult.newSuccess(response);
    }


    /**
     * 批量修改子sku图片 请求到页面数据
     *
     * @param criteria
     * @return
     */
    @PostMapping(value = "/batchUpdateSonSkuImg")
    public ApiResult<?> batchUpdateSonSkuImg(@RequestBody(required = true) AliexpressProductCriteria criteria) {
        String idStr = criteria.getIdStr();
        if (StringUtils.isEmpty(idStr)) {
            return ApiResult.newError("id必填");
        }
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdStr(idStr);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("找不到产品");
        }

        //产品对应的图片池
        Map<Long, List<String>> productImgListMap = new HashMap<>();

        Map<String, AliexpressEsExtend> extendMap = new HashMap<>();
        esAliexpressProductListing.forEach(t -> {
            try {
                Long productId = t.getProductId();
                String articleNumber = t.getArticleNumber();

                List<String> productImgList = productImgListMap.get(productId);

                Boolean isQuery = false;
                if (CollectionUtils.isEmpty(productImgList)) {
                    List<String> images = FmsUtils.getSmtImgs(articleNumber, null);
                    productImgList = AliexpressContentUtils.imgPriorityForList(images);
                    productImgListMap.put(productId, productImgList);
                    isQuery = true;
                }

                AliexpressEsExtend extend = new AliexpressEsExtend();

                if (CollectionUtils.isEmpty(productImgList)) {
                    productImgList = new ArrayList<>();
                }

                List<String> sonSkuImgs = productImgList.stream()
                        .filter(img -> StringUtils.containsIgnoreCase(img, articleNumber + "."))
                        .collect(Collectors.toList());

                //没有匹配的图片 就在查一次,有可能一个产品 有多个spu
                if (!isQuery && CollectionUtils.isEmpty(sonSkuImgs)) {
                    List<String> images = FmsUtils.getSmtImgs(articleNumber, null);
                    productImgList = AliexpressContentUtils.imgPriorityForList(images);
                    sonSkuImgs = productImgList.stream()
                            .filter(img -> StringUtils.containsIgnoreCase(img, articleNumber + "."))
                            .collect(Collectors.toList());
                }
                extend.setImages(sonSkuImgs);
                extendMap.put(t.getId(), extend);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        EsAliexpressProductListingResponse response = new EsAliexpressProductListingResponse();
        response.setEsAliexpressProductListing(esAliexpressProductListing);
        response.setExtendMap(extendMap);
        return ApiResult.newSuccess(response);
    }


    /**
     * 返回支持车型库修改的产品
     *
     * @param aliexpressProductCriteria
     * @return
     */
    @PostMapping(value = "/batchUpdateCayType")
    public ApiResult<?> batchUpdateCayType(@RequestBody AliexpressProductCriteria aliexpressProductCriteria) {
        Asserts.isTrue(aliexpressProductCriteria != null, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
        if (StringUtils.isBlank(aliexpressProductCriteria.getIdStr())) {
            return ApiResult.newError("产品id必填！");
        }
        EsAliexpressProductListingRequest esAliexpressProductListingRequest = new EsAliexpressProductListingRequest();
        esAliexpressProductListingRequest.setIdStr(aliexpressProductCriteria.getIdStr());
        esAliexpressProductListingRequest.setIsCayType(true);

        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esAliexpressProductListingRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("没有支持车型库分类数据");
        }
        return ApiResult.newSuccess(esAliexpressProductListing);
    }

    /**
     * 车型库执行修改数据
     *
     * @param aliexpressProductCriteria
     * @return
     */
    @PostMapping(value = "/executeUpdateCayType")
    public ApiResult<?> executeUpdateCayType(@RequestBody AliexpressProductCriteria aliexpressProductCriteria) {
        Asserts.isTrue(aliexpressProductCriteria != null, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
        if (StringUtils.isBlank(aliexpressProductCriteria.getIdStr()) || StringUtils.isBlank(aliexpressProductCriteria.getCayJson())) {
            return ApiResult.newError("产品id和json必填！");
        }
        EsAliexpressProductListingRequest esAliexpressProductListingRequest = new EsAliexpressProductListingRequest();
        esAliexpressProductListingRequest.setIdStr(aliexpressProductCriteria.getIdStr());
        esAliexpressProductListingRequest.setIsCayType(true);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esAliexpressProductListingRequest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("没有可用数据");
        }
        String userName = WebUtils.getUserName();
        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            AliexpressProduct aliexpressProduct = new AliexpressProduct();
            BeanUtils.copyProperties(esProduct, aliexpressProduct);
            AliexpressExecutors.executeUpdateCarCategory(() -> {
                aliexpressEsExtendService.updateProductCarType(aliexpressProduct, aliexpressProductCriteria.getCayJson(), userName);
            });
        }
        return ApiResult.newSuccess();
    }

    /**
     * 修改季节
     *
     * @param aliexpressProductCriteria
     * @return
     */
    @PostMapping(value = "/executeUpdateSeason")
    public ApiResult<?> executeUpdateSeason(@RequestBody AliexpressProductCriteria aliexpressProductCriteria) {

        try {
            Asserts.isTrue(aliexpressProductCriteria != null, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
            Integer type = aliexpressProductCriteria.getType();
            String idStr = aliexpressProductCriteria.getIdStr();
            String json = aliexpressProductCriteria.getCayJson();
            Asserts.isTrue(type != null && StringUtils.isNotBlank(idStr) && StringUtils.isNotBlank(json), ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
            AliexpressUpdateSeasonHelper.updateSeasonForEs(idStr, type, json);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("后台处理中！");
    }

    @GetMapping(value = "/getAccountCategorys")
    public ApiResult<?> getAccountCategorys(@RequestParam(value = "account") String account) {
        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setAliexpressAccountNumber(account);
        listingRequest.setProductStatusType("onSelling,auditing");
        listingRequest.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "categoryId"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        Map<Integer, List<EsAliexpressProductListing>> map = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getCategoryId()));

        Map<Integer, UpdateCategoryAttrBean> categoryMap = new HashMap<>();
        for (Map.Entry<Integer, List<EsAliexpressProductListing>> integerListEntry : map.entrySet()) {
            List<EsAliexpressProductListing> list = integerListEntry.getValue().stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EsAliexpressProductListing::getProductId))), ArrayList::new));
            Integer key = integerListEntry.getKey();
            List<String> idList = list.stream().map(t -> t.getId()).collect(Collectors.toList());


            AliexpressCategory fullCategory = aliexpressCategoryService
                    .selectAllSubSetAliexpressCategory(aliexpressCategoryService.selectByCategoryId(key));
            List<String> aaList = new ArrayList<>();
            wholeCategory(fullCategory, aaList);
            String name = StringUtils.join(aaList, "-");

            UpdateCategoryAttrBean updateCategoryAttrBean = new UpdateCategoryAttrBean();
            updateCategoryAttrBean.setIdList(idList);
            updateCategoryAttrBean.setName(name);
            categoryMap.put(key, updateCategoryAttrBean);
        }
        return ApiResult.newSuccess(categoryMap);
    }

    private void wholeCategory(AliexpressCategory fullCategory, List<String> aaList) {
        aaList.add(fullCategory.getCategoryZhName());
        if (fullCategory.getSonCategory() == null) {
            return;
        }
        wholeCategory(fullCategory.getSonCategory(), aaList);
    }

    /**
     * 修改属性
     */
    @PostMapping(value = "/updateAttr")
    public ApiResult<?> updateAttr(@RequestBody List<AliexpressProductCriteria> aliexpressProductCriteriaList) {
        try {
            Asserts.isTrue(CollectionUtils.isNotEmpty(aliexpressProductCriteriaList), ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");

            for (AliexpressProductCriteria aliexpressProductCriteria : aliexpressProductCriteriaList) {
                Integer type = aliexpressProductCriteria.getType();
                String idStr = aliexpressProductCriteria.getIdStr();
                String json = aliexpressProductCriteria.getCayJson();
                Boolean isClear = aliexpressProductCriteria.getIsClear();
                Asserts.isTrue(type != null && StringUtils.isNotBlank(idStr) && StringUtils.isNotBlank(json) && isClear != null, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
                AliexpressUpdateAttrHelper.updateAttrForEs(idStr, type, json, isClear);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("后台处理中！");
    }

    /**
     * 在线列表 32国调价 请求到页面
     *
     * @param criteria
     * @return
     */
    @PostMapping(value = "/updateInternationalPrice")
    public ApiResult<?> updateInternationalPrice(@RequestBody AliexpressProductCriteria criteria) {
        Aliexpress28CalcBean bean = new Aliexpress28CalcBean();
        try {
            Asserts.isTrue(criteria != null
                            && StringUtils.isNotBlank(criteria.getProductIdStr())
                            && StringUtils.isNotBlank(criteria.getPriceType())
                            && StringUtils.isNotBlank(criteria.getLogisticsName())
                            && StringUtils.isNotBlank(criteria.getCreateBy()),
                    ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdStr(criteria.getProductIdStr());
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                return ApiResult.newError("没有需要调整的上架产品！");
            }
            bean.setEsAliexpressProductListing(esAliexpressProductListing);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                return ApiResult.newError("没有需要调整的上架产品！");
            }
            String logisticsName = criteria.getLogisticsName();
            Double gross = criteria.getGross();
            String createBy = criteria.getCreateBy();
            try {
                long begin = System.currentTimeMillis();
                //需要试算的国家
                List<String> updateCountryCodeList = criteria.getUpdateCountryCodeList();

                Map<String, AliexpressEsExtend> esExtendMap = new HashMap<>();

                ResponseJson responseJson = aliexpressEsExtendService
                        .product28Calc(esAliexpressProductListing, esExtendMap, logisticsName, createBy, gross, updateCountryCodeList, true);
                long end = System.currentTimeMillis();
                log.warn(String.format("%s产品，32国算价耗时%s秒", esAliexpressProductListing.size(), (end - begin) / 1000));

                if (!responseJson.isSuccess()) {
                    return ApiResult.newError(responseJson.getMessage());
                }
                Map<String, List<BatchPriceCalculatorResponse>> map = (Map<String, List<BatchPriceCalculatorResponse>>) responseJson.getBody().get("key");
                bean.setMap(map);

                // 添加EPR费用
                aliexpressEsExtendService.increaseEprFee(esAliexpressProductListing, map);

                AliInternationalCaleConfigExample example = new AliInternationalCaleConfigExample();
                example.createCriteria().andTempNameEqualTo(criteria.getLogisticsName()).andCreateByEqualTo(createBy);
                List<AliInternationalCaleConfig> aliInternationalCaleConfigs = aliInternationalCaleConfigService
                        .selectByExample(example);
                //国家对应的店铺折扣
                Map<String, Double> codeDiscountRateMap = new HashMap<>();
                for (AliInternationalCaleConfig aliInternationalCaleConfig : aliInternationalCaleConfigs) {
                    String countryCode = aliInternationalCaleConfig.getCountryCode();
                    Double discountRate = aliInternationalCaleConfig.getDiscountRate();
                    codeDiscountRateMap.put(countryCode, discountRate);
                }
                bean.setCodeDiscountRateMap(codeDiscountRateMap);
            } catch (Exception e) {
                log.error("毛利计算异常：" + e.getMessage(), e);
                return ApiResult.newError(e.getMessage());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(bean);
    }

    /**
     * 执行修改32国改价
     * http://172.16.2.103:8080/browse/ES-9756 半托管国家需要保留原价
     *
     * @param bean
     * @return
     */
    @PostMapping(value = "/executeInternationalPrice")
    public ApiResult<?> executeInternationalPrice(@RequestBody Aliexpress28CalcBean bean) {
        String priceType = bean.getPriceType();
        List<EsAliexpressProductListing> esAliexpressProductListing = bean.getEsAliexpressProductListing();
        boolean isPartCountry = false; //是否部分国家调价

        //修改的部分国家
        List<String> updateCountryCodeList = bean.getUpdateCountryCodeList();
        if (CollectionUtils.isNotEmpty(updateCountryCodeList)) {
            isPartCountry = true;
        }
        //String key = product.getProductId() + "-" + skuCode;
        Map<String, Map<String, Double>> dbProduct28Map = new HashMap<>();

        //产品对应的价格类型
        Map<Long, String> dbPriceTypeMap = new HashMap<>();
        //当只需要修改部分国家的时候，同步下产品最新信息
        Map<Long, List<EsAliexpressProductListing>> map = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : map.entrySet()) {
            List<EsAliexpressProductListing> value = longListEntry.getValue();
            Long key = longListEntry.getKey();
            String aliexpressAccountNumber = value.get(0).getAliexpressAccountNumber();

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
            SynchItemOpenCall call = new SynchItemOpenCall();
            call.syncAliexpressProductInfo(saleAccountByAccountNumber, key);
        }

        //只修改部分国家 需要查询出产品原本的价格
        List<Long> productIdList = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toList());
        AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
        esExtendExample.createCriteria().andProductIdIn(productIdList);
        List<AliexpressEsExtend> extendList = aliexpressEsExtendService.selectByExample(esExtendExample);

        //获取产品的半托管国家
        Map<String, List<String>> halfCountryMap = new HashMap<>();

        //产品去重
        for (AliexpressEsExtend extend : extendList) {
            //获取其他国家的32国价格
            AliexpressStatePriceUtils.getProduct28MapForEs(extend, dbProduct28Map);
            dbPriceTypeMap.put(extend.getProductId(), extend.getPriceType());

            //判断是否属于半托管产品
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            halfTgItemExample.createCriteria().andProductIdEqualTo(extend.getProductId()).andProductStatusEqualTo("onSelling");
            List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            if (CollectionUtils.isNotEmpty(aliexpressHalfTgItems)) {
                //["ES","FR","BR","US","IL","MX","CL","UA","PL","DE","UK","NL","IT","AU","MY","TH","PT","BE","CH","CZ","NZ","LT","LV","SK","NO","HU","BG","EE","RO","PK","HR","NG","IE","AT","GR","SE","FI","DK","SI","MT","LK","LU","PE","SG","CA","CY","ET","KR"]
                String joinedCountryList = aliexpressHalfTgItems.get(0).getJoinedCountryList();
                List<String> strings = JSONArray.parseArray(joinedCountryList, String.class);
                halfCountryMap.put(extend.getProductId().toString(), strings);
            }
        }

        //组装数据
        Map<String, List<AliexpressEditProductBean>> returnMap = new HashMap<>();

        Map<String, AliexpressEsExtend> extendMap = bean.getExtendMap();

        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            String productId = esProduct.getProductId().toString();
            List<AliexpressEditProductBean> excelDataList = returnMap.get(productId);
            if (CollectionUtils.isEmpty(excelDataList)) {
                excelDataList = new ArrayList<>();
                returnMap.put(productId, excelDataList);
            }
            //原价
            Double skuPrice = esProduct.getSkuPrice();

            AliexpressEditProductBean item = new AliexpressEditProductBean();
            item.setAccountNum(esProduct.getAliexpressAccountNumber());
            item.setProductId(productId);
            item.setSkuCode(esProduct.getSkuCode());
            item.setSkuId(esProduct.getSkuId());
            item.setPriceType(priceType);

            AliexpressEsExtend extend = extendMap.get(esProduct.getId());
            Map<String, Double> countryPriceMap = extend.getCountryPriceMap();
            item.setCountryPriceMap(countryPriceMap);
            excelDataList.add(item);

            List<String> halfCountryList = halfCountryMap.get(productId); //产品包含的半托管国家
            if (CollectionUtils.isEmpty(halfCountryList)) {
                halfCountryList = new ArrayList<>();
            }

            //产品之前的价格类型
            String dbPriceType = dbPriceTypeMap.get(esProduct.getProductId());
            if (StringUtils.isEmpty(dbPriceType)) {
                continue;
            }
            //其他国家 设置原值
            Map<String, Double> dbStringDoubleMap = dbProduct28Map
                    .get(esProduct.getProductId() + "-" + esProduct.getSkuCode());

            if (null == dbStringDoubleMap || dbStringDoubleMap.isEmpty()) {
                continue;
            }

            for (Map.Entry<String, Double> stringDoubleEntry : dbStringDoubleMap.entrySet()) {
                //国家
                String key = stringDoubleEntry.getKey();
                //价格
                Double dbValue28 = stringDoubleEntry.getValue();
                if (null == dbValue28 || dbValue28 == 0.0d) {
                    continue;
                }
                //半托管包含的国家 或者 不修改的国家 都需要用原值
                if (halfCountryList.contains(key) || (isPartCountry && !updateCountryCodeList.contains(key))) {
                    Double aDouble = AliexpressCalcPriceUtil.convertPrice(skuPrice, dbValue28, priceType, dbPriceType);
                    countryPriceMap.put(key, aDouble);
                }
            }// end dbStringDoubleMap
        }

        //处理结果
        EnvironmentSupplierWrapper.execute(task -> {
            aliexpressEsExtendService.updateProductCountryPriceNew(returnMap, WebUtils.getUserName(), null, false);
        }, defaultTask -> {
            log.info("{}改价，param:{}", defaultTask, JSON.toJSONString(returnMap));
        });
        return ApiResult.newSuccess("请到处理报告查看结果");
    }

    /**
     * 查看32国价格详情
     */
    @PostMapping("/get28PriceDetail")
    public ApiResult<Map<String, Double>> get28PriceDetail(@RequestBody Aliexpress28PriceBean aliexpress28PriceBean) {
        Map<String, Double> countryPriceMap;
        try {
            countryPriceMap = aliexpressEsExtendService.get28PriceDetail(aliexpress28PriceBean);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(countryPriceMap);
    }

    /**
     * 清空区域调价
     *
     * @param bean
     * @return
     */
    @PostMapping(value = "/clearAreaModifyPrice")
    public ApiResult<?> clearAreaModifyPrice(@RequestBody Aliexpress28CalcBean bean) {
        aliexpressEsExtendService.clearAreaModifyPrice(bean);
        return ApiResult.newSuccess("请到处理报告查看结果");
    }

    /**
     * 32国改价价格核对
     *
     * @param criteria
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/check28Price")
    public ApiResult<?> check28Price(@RequestBody AliexpressProductCriteria criteria, HttpServletRequest request, HttpServletResponse response) {

        try {
            Asserts.isTrue(criteria != null
                            && StringUtils.isNotBlank(criteria.getProductIdStr())
                            && StringUtils.isNotBlank(criteria.getLogisticsName())
                            && StringUtils.isNotBlank(criteria.getCreateBy()),
                    ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");

            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdStr(criteria.getProductIdStr());
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
            esExtendExample.createCriteria().andProductIdIn(CommonUtils.splitLongList(criteria.getProductIdStr(), ","));
            esExtendExample.setFields("extend_id, aliexpress_account_number, owner_member_id, product_id, aeop_ae_product_skus_json, aeop_national_quote_configuration");
            List<AliexpressEsExtend> extendList = aliexpressEsExtendService.selectByExample(esExtendExample);

            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                return ApiResult.newError("没有需要核对的上架产品！");
            }

            //TODO 解析数据 得到每个产品 32国的 Map<String,Double> priceMap
            Map<String, Map<String, Double>> product28Map = new HashMap<>();

            Set<Long> productIdSet = new HashSet<>();

            //产品id 对应的调价类型
            Map<Long, String> priceTypeMap = new HashMap<>();

            //产品id 对应的 扩展map
            Map<Long, AliexpressEsExtend> productIdExTendMap = new HashMap<>();
            for (AliexpressEsExtend extend : extendList) {

                productIdExTendMap.put(extend.getProductId(), extend);
                //获取其他国家的32国价格
                AliexpressStatePriceUtils.getProduct28MapForEs(extend, product28Map);
                productIdSet.add(extend.getProductId());
                priceTypeMap.put(extend.getProductId(), extend.getPriceType());
            }

            //ES主键id 分配一个 扩展属性，主要是 每个单属性对应的CountryPriceMap
            Map<String, AliexpressEsExtend> esExtendMap = new HashMap<>();
            for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
                Long productId = esProduct.getProductId();
                String skuCode = esProduct.getSkuCode();
                String id = esProduct.getId();

                String key = productId + "-" + skuCode;
                Map<String, Double> stringDoubleMap = product28Map.get(key);

                AliexpressEsExtend extend = productIdExTendMap.get(productId);
                AliexpressEsExtend copyExtend = new AliexpressEsExtend();
                copyExtend.setExtendId(extend.getExtendId());
                copyExtend.setAliexpressAccountNumber(extend.getAliexpressAccountNumber());
                copyExtend.setOwnerMemberId(extend.getOwnerMemberId());
                copyExtend.setProductId(extend.getProductId());
                copyExtend.setAeopAeProductSkusJson(extend.getAeopAeProductSkusJson());
                copyExtend.setAeopNationalQuoteConfiguration(extend.getAeopNationalQuoteConfiguration());
                copyExtend.setPriceType(extend.getPriceType());
                copyExtend.setShiptoCountryList(extend.getShiptoCountryList());
                copyExtend.setCountryPriceMap(stringDoubleMap);
                esExtendMap.put(id, copyExtend);
            }

            Map<String, List<BatchPriceCalculatorResponse>> map = null;
            try {
                long begin = System.currentTimeMillis();

                ResponseJson responseJson = aliexpressEsExtendService
                        .product28Calc(esAliexpressProductListing, esExtendMap, criteria.getLogisticsName(), criteria.getCreateBy(), null, null, false);

                long end = System.currentTimeMillis();
                log.warn(String.format("%s产品，32国算价耗时%s秒", esAliexpressProductListing.size(), (end - begin) / 1000));

                if (!responseJson.isSuccess()) {
                    return ApiResult.newError(responseJson.getMessage());
                }

                map = (Map<String, List<BatchPriceCalculatorResponse>>) responseJson.getBody().get("key");

            } catch (Exception e) {
                log.error("毛利计算异常：" + e.getMessage(), e);
                return ApiResult.newError(e.getMessage());
            }

            for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
                String id = esProduct.getId();
                List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = map.get(id);
                AliexpressEsExtend extend = esExtendMap.get(id);
                extend.setCalculatorResponseList(batchPriceCalculatorResponses);
            }

            //excel下载
            OutputStream stateResultOs = null;

            try {
                String fileName = "smt-32-price-check-" + com.estone.erp.publish.common.util.POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
                response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));

                stateResultOs = response.getOutputStream();

                if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                    final List<List<String>> awLists = new ArrayList<>();
                    com.estone.erp.publish.common.util.POIUtils
                            .createExcel(headerStateCheckResult, esAliexpressProductListing, item -> {
                                awLists.clear();
                                List<String> awList = new ArrayList<>();
                                awList.add(POIUtils.transferObj2Str(item.getAliexpressAccountNumber()));
                                awList.add(POIUtils.transferObj2Str(item.getProductId()));
                                awList.add(POIUtils.transferObj2Str(item.getSkuCode()));
                                AliexpressEsExtend extend = esExtendMap.get(item.getId());
                                String priceType = extend.getPriceType();
                                if (StringUtils.isNotBlank(priceType)) {
                                    if (priceType.equals("percentage")) {
                                        awList.add(POIUtils.transferObj2Str("百分比"));
                                    } else if (priceType.equals("relative")) {
                                        awList.add(POIUtils.transferObj2Str("基准价"));
                                    } else if (priceType.equals("absolute")) {
                                        awList.add(POIUtils.transferObj2Str("直接调价"));
                                    } else {
                                        awList.add(POIUtils.transferObj2Str(priceType));
                                    }
                                } else {
                                    awList.add("");
                                }

                                //这里 包含毛利
                                Map<String, BatchPriceCalculatorResponse> countryCodeGrossMap = null;
                                List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = extend.getCalculatorResponseList();
                                if (CollectionUtils.isNotEmpty(batchPriceCalculatorResponses)) {
                                    countryCodeGrossMap = batchPriceCalculatorResponses.stream()
                                            .collect(Collectors.toMap(k -> k.getCountryCode(), v -> v));
                                }

                                if (countryCodeGrossMap == null) {
                                    countryCodeGrossMap = new HashMap<>();
                                }

                                //这里 是32国显示值
                                Map<String, Double> countryPriceMap = extend.getCountryPriceMap();
                                if (countryPriceMap == null) {
                                    countryPriceMap = new HashMap<>();
                                }
                                String[] countryList = AliexpressStatePriceUtils.countryList;
                                for (String s : countryList) {
                                    Double aDouble = countryPriceMap.get(s);
                                    if (aDouble != null) {
                                        awList.add(POIUtils.transferObj2Str(aDouble));
                                    } else {
                                        awList.add(POIUtils.transferObj2Str(""));
                                    }
                                    BatchPriceCalculatorResponse batchPriceCalculatorResponse = countryCodeGrossMap.get(s);

                                    //毛利
                                    if (null != batchPriceCalculatorResponse && batchPriceCalculatorResponse.getIsSuccess()) {
                                        awList.add(POIUtils.transferObj2Str(NumberUtils.round(batchPriceCalculatorResponse.getForeignGrossProfit(), 2)));
                                    } else {
                                        if (null != batchPriceCalculatorResponse) {
                                            awList.add(POIUtils.transferObj2Str(batchPriceCalculatorResponse.getErrorMsg()));
                                        } else {
                                            awList.add(POIUtils.transferObj2Str(""));
                                        }
                                    }
                                }
                                awLists.add(awList);
                                return awLists;

                            }, true, stateResultOs);

                }
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
                return ApiResult.newError(e.getMessage());
            } finally {
                IOUtils.closeQuietly(stateResultOs);
            }

        } catch (Exception e) {
            log.error("异常：" + e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess();
    }

    /**
     * 请求到页面
     *
     * @param productList
     * @return
     */
    @PostMapping(value = "/matchMarkeImages")
    public ApiResult<?> matchMarkeImages(@RequestBody List<AliexpressProduct> productList) {
        List<AliexpressProduct> productArrayList = productList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AliexpressProduct::getProductId))),
                ArrayList::new));

        List<MarketImageBean> marketImageBeanList = new ArrayList<>();
        for (AliexpressProduct aliexpressProduct : productArrayList) {
            MarketImageBean marketImageBean = new MarketImageBean();
            marketImageBeanList.add(marketImageBean);
            String articleNumber = aliexpressProduct.getArticleNumber();
            String mainSku = ProductUtils.getMainSku(articleNumber);

            String aliexpressAccountNumber = aliexpressProduct.getAliexpressAccountNumber();
            Long productId = aliexpressProduct.getProductId();
            AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(aliexpressAccountNumber, productId);

            marketImageBean.setAccount(aliexpressAccountNumber);
            marketImageBean.setProductId(productId);
            marketImageBean.setMainSku(mainSku);

            List<String> images = FmsUtils.getSmtImgs(mainSku, null, new Boolean[]{true});
            // 营销图
            List<MarketImage> marketImages = AliexpressContentUtils.getMarketImages(images, mainSku);
            for (MarketImage marketImage : marketImages) {
                String image_type = marketImage.getImage_type();
                if (StringUtils.equalsIgnoreCase(image_type, "2")) {
                    marketImageBean.setSquareImg(marketImage.getUrl());
                } else {
                    marketImageBean.setLongImg(marketImage.getUrl());
                }
            }
            //优先产品本身的图片
            if (StringUtils.isNotBlank(aliexpressEsExtend.getLongImg())) {
                marketImageBean.setLongImg(aliexpressEsExtend.getLongImg());
            }
            //新白底图取产品系统SMT白底图
//            if (StringUtils.isNotBlank(aliexpressEsExtend.getSquareImg())) {
//                marketImageBean.setSquareImg(aliexpressEsExtend.getSquareImg());
//            }
        }
        return ApiResult.newSuccess(marketImageBeanList);
    }

    @GetMapping(value = "/getProductMarketImageBySku")
    public ApiResult<?> getMarketImageBySku(@RequestParam(value = "sku", required = true) String sku) {

        String mainSku = ProductUtils.getMainSku(sku);

        AliexpressProductInfo info = new AliexpressProductInfo();
        info.setMainSku(mainSku);
        List<String> images = FmsUtils.getSmtImgs(info.getMainSku(), null, new Boolean[]{true});

        //800 * 800
        List<String> squareImgList = new ArrayList<>();

        //750 * 1000
        List<String> longImgList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(images)) {
            for (String image : images) {
                if (StringUtils.indexOf(image, "-effect-copy.") == -1) {
                    squareImgList.add(image);
                } else {
                    longImgList.add(image);
                }
            }
        }

        //获取文件系统的产品库图片Amazon以及模板上传图片
        String type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
        List<String> productImgeList = FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
        if (CollectionUtils.isEmpty(productImgeList)) {
            type = PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName();
            productImgeList = FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
        }

        if (CollectionUtils.isNotEmpty(productImgeList)) {
            longImgList.addAll(productImgeList);
        }

        Map<String, List<String>> imgMap = new HashMap<>();
        imgMap.put("1", longImgList); //长图
        imgMap.put("2", squareImgList); //方图
        return ApiResult.newSuccess(imgMap);
    }

    /**
     * 批量修改营销图片
     *
     * @param imageBeanList
     * @return
     */
    @PostMapping(value = "/updateMarkeImages")
    public ApiResult<?> updateMarkeImages(@RequestBody List<MarketImageBean> imageBeanList) {
        try {
            String userName = WebUtils.getUserName();
            aliexpressEsExtendService.updateMarketImages(imageBeanList, userName);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 修改店铺尺寸
     *
     * @param productArrayList
     * @return
     */
    @PostMapping(value = "/updateSize")
    public ApiResult<?> updateSize(@RequestBody List<ProductSizeBean> productArrayList) {
        //去重
        List<ProductSizeBean> productSizeBeanList = productArrayList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProductSizeBean::getProductId))),
                ArrayList::new));
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (ProductSizeBean productSizeBean : productSizeBeanList) {
            String aliexpressAccountNumber = productSizeBean.getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
            if (saleAccountAndBusinessResponse == null) {
                saleAccountAndBusinessResponse = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, saleAccountAndBusinessResponse);
            }
            Long productId = productSizeBean.getProductId();
            ResponseJson responseJson = aliexpressEsExtendService
                    .updateProductSize(productSizeBean, saleAccountAndBusinessResponse);

            AliexpressProductLog log = new AliexpressProductLog();
            log.setProductId(productId);
            log.setAccountNumber(aliexpressAccountNumber);
            log.setOperator(WebUtils.getUserName());
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            log.setOperateType(OperateLogTypeEnum.update_size.getCode());
            log.setResult(responseJson.isSuccess());
            log.setFailInfo(responseJson.getMessage());
            aliexpressProductLogService.insert(log);
        }
        return ApiResult.newSuccess("请在处理报告查看修改结果！");
    }


    /**
     * 通过店铺获取引流sku 并区分不存在的或者下架的引流sku
     *
     * @param account
     */
    @GetMapping(value = "/getDrainageSkuByAccount")
    public ApiResult<?> getDrainageSkuByAccount(@RequestParam("account") String account) {
        DrainageSkuExample drainageSkuExample = new DrainageSkuExample();
        drainageSkuExample.createCriteria().andPlatformEqualTo(SaleChannel.CHANNEL_SMT).andAccountNumberEqualTo(account).andIsDrainageEqualTo(true);
        List<DrainageSku> drainageSkus = drainageSkuService.selectByExample(drainageSkuExample);
        Map<String, Boolean> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(drainageSkus)) {
            List<String> skuList = drainageSkus.stream().map(t -> t.getSku()).collect(Collectors.toList());
            //查询在线
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setAliexpressAccountNumber(account);
            esRequest.setArticleNumberStr(StringUtils.join(skuList, ","));
            esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            List<String> onlineSkuList = esAliexpressProductListing.stream().map(t -> t.getArticleNumber())
                    .collect(Collectors.toList());
            for (String sku : skuList) {
                map.put(sku, CollectionUtils.isNotEmpty(onlineSkuList) && onlineSkuList.contains(sku));
            }
        }
        return ApiResult.newSuccess(map);
    }

    /**
     * 设置引流sku
     *
     * @param aliexpressProductCriteria
     * @return
     */
    @PostMapping(value = "/setDrainageSkuByAccount")
    public ApiResult<?> setDrainageSkuByAccount(@RequestBody AliexpressProductCriteria aliexpressProductCriteria) {
        String aliexpressAccountNumber = aliexpressProductCriteria.getAliexpressAccountNumber();
        Map<String, Boolean> map = aliexpressProductCriteria.getMap();
        Asserts.isTrue(StringUtils.isNotBlank(aliexpressAccountNumber) && null != map && !map.isEmpty(), ErrorCode.PARAM_EMPTY_ERROR, "参数错误！");
        //需要检查sku 是否是在线产品
        List<String> checkSkuList = new ArrayList<>();
        for (Map.Entry<String, Boolean> stringBooleanEntry : map.entrySet()) {
            String key = stringBooleanEntry.getKey();
            Boolean value = stringBooleanEntry.getValue();
            if (null != value && value) {
                checkSkuList.add(key);
            }
        }
        if (CollectionUtils.isNotEmpty(checkSkuList)) {
            //查询在线
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setAliexpressAccountNumber(aliexpressAccountNumber);
            esRequest.setArticleNumberStr(StringUtils.join(checkSkuList, ","));
            esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
            List<String> onlineSkuList = esAliexpressProductListing.stream().map(t -> t.getArticleNumber())
                    .collect(Collectors.toList());
            checkSkuList.removeAll(onlineSkuList);
            if (CollectionUtils.isNotEmpty(checkSkuList)) {
                return ApiResult.newError(String.format("存在sku不存在在线产品【%s】", StringUtils.join(checkSkuList, ",")));
            }
        }
        List<DrainageSku> dtoList = new ArrayList<>();
        //修改引流表，并推送MQ给产品系统
        for (Map.Entry<String, Boolean> stringBooleanEntry : map.entrySet()) {
            String key = stringBooleanEntry.getKey();
            Boolean value = stringBooleanEntry.getValue();
            DrainageSku drainageSku = new DrainageSku();
            drainageSku.setPlatform(SaleChannel.CHANNEL_SMT);
            drainageSku.setAccountNumber(aliexpressAccountNumber);
            drainageSku.setSku(key);
            drainageSku.setIsDrainage(value);
            dtoList.add(drainageSku);
        }
        return drainageSkuService.updateOrInsert(dtoList);
    }

    /**
     * excel导入功能，修改重量，价格，32国调价
     *
     * @param multiPartFile
     * @param type
     * @param priceType
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/excel/update/{type}")
    public ApiResult<?> excelUpdateState(@RequestParam(value = "file", required = false) MultipartFile multiPartFile,
                                         @PathVariable(value = "type", required = true) String type,
                                         @RequestParam(value = "priceType", required = false) String priceType,
                                         @RequestParam(value = "isAdvancedPermission", required = false) Boolean isAdvancedPermission,
                                         HttpServletRequest request, HttpServletResponse response) {
        if (type.equals("statePrice") && !Arrays.asList(ExcelConstant.PRICE_TYPE_LIST).contains(priceType)) {
            return ApiResult.newError("调价方式有误");
        }
        try {
            MultipartFile file = null;
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Map fileMap = multiRequest.getFileMap();
            if (fileMap.values().size() > 0) {
                file = (MultipartFile) fileMap.values().iterator().next();
            } else {
                throw new Exception("请先上传文件");
            }
            if (file != null && file.getSize() > 0) {
                ExcelBean excelBean = new ExcelBean();
                excelBean.setOperationType(type);
                excelBean.setPriceType(priceType);
                excelBean.setIsAdvancedPermission(isAdvancedPermission);
                //一个人 同一份文件 一个小时限制
                if (StringUtils.equalsIgnoreCase(type, "freightTemplateId") || StringUtils.equalsIgnoreCase(type, "groupId")) {
                    String originalFilename = file.getOriginalFilename();

                    String key = WebUtils.getUserName() + "_" + originalFilename;
                    String value = PublishRedisClusterUtils.get(key);
                    if (StringUtils.isNotBlank(value)) {
                        return ApiResult.newError("同一份文件一个小时内请勿重新上传！");
                    }

                    PublishRedisClusterUtils.set(key, key, 1, TimeUnit.HOURS);

                }
                ResponseJson responseJson = excelSend.excelOperationSend(excelBean, file);
                if (!responseJson.isSuccess()) {
                    return ApiResult.newError(responseJson.getMessage());
                }
            } else {
                return ApiResult.newError("获取不到文件解决");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError("修改失败-" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    /**
     * 上传视频
     *
     * @param
     * @return
     */
    @PostMapping(value = "/batchUploadVideo")
    public ApiResult<?> batchUploadVideo(@RequestBody List<AliexpressEsExtend> esExtendList) {
        aliexpressEsExtendService.batchUploadVideo(esExtendList);
        return ApiResult.newSuccess("正在处理中，请到处理报告查看结果");
    }

    /**
     * 批量设置运费模板，服务模板，产品分组
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping("updateBatchProductPatam")
    public ApiResult<?> updateBatchProductPatam(@RequestBody(required = true) List<EsAliexpressProductListingRequest> requestCriteria) {
        String userName = WebUtils.getUserName();
        for (EsAliexpressProductListingRequest requestCriterion : requestCriteria) {
            String idStr = requestCriterion.getIdStr();
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            if (StringUtils.isBlank(idStr)) {
                //上架状态
                request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            }
            String aliexpressAccountNumber = requestCriterion.getAliexpressAccountNumber();
            if (StringUtils.isBlank(aliexpressAccountNumber)) {
                log.error("修改运费服务模板，产品分组没有传入账号");
                continue;
            }
            request.setAliexpressAccountNumber(aliexpressAccountNumber);
            List<String> ids = CommonUtils.splitList(idStr, ",");
            request.setIdList(ids);
            aliexpressEsExtendService.batchSetEditsimpleproductfiled(request, requestCriterion.getFreightTemplateIdStr()
                    , requestCriterion.getGroupIdStr(), requestCriterion.getPromiseTemplateIdStr(), userName);
        }
        return ApiResult.newSuccess("请求成功，请到处理报告查询结果！");
    }

    /**
     * 自动上下架
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping("autoOffandOnProduct")
    public ApiResult<?> autoOffandOnProduct(@RequestBody(required = true) AliexpressEsExtendCriteria requestCriteria) {
        String productIdStr = requestCriteria.getProductIdStr();
        String autoOffDateStr = requestCriteria.getAutoOffDateStr();
        Timestamp offDate = StringUtils.isBlank(autoOffDateStr) ?
                null :
                AliexpressContentUtils.changeYearMonthDay(autoOffDateStr);
        String autoOnDateStr = requestCriteria.getAutoOnDateStr();
        Timestamp onDate = StringUtils.isBlank(autoOnDateStr) ?
                null :
                AliexpressContentUtils.changeYearMonthDay(autoOnDateStr);
        String startTaskDateStr = requestCriteria.getStartTaskDateStr();
        String endTaskDateStr = requestCriteria.getEndTaskDateStr();

        List<Long> productIdList = CommonUtils.splitLongList(productIdStr, ",");
        for (Long productId : productIdList) {
            AliexpressEsExtend extend = new AliexpressEsExtend();
            extend.setProductId(productId);
            extend.setAutoOffDate(offDate);
            extend.setAutoOnDate(onDate);
            extend.setStartTaskDate(StringUtils.isBlank(startTaskDateStr) ? null : Timestamp.valueOf(startTaskDateStr));
            extend.setEndTaskDate(StringUtils.isBlank(endTaskDateStr) ? null : Timestamp.valueOf(endTaskDateStr));
            aliexpressEsExtendService.updateOFFandOnByProductId(extend);
        }
        return ApiResult.newSuccess("设置成功！");
    }

    /**
     * 更新货号状态
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping("updateArticleNumber")
    public ApiResult<?> updateArticleNumber(@RequestBody(required = true) AliexpressEsExtendCriteria requestCriteria) {
        String idStr = requestCriteria.getIdStr();
        if (StringUtils.isEmpty(idStr)) {
            return ApiResult.newError("id不能为空！");
        }
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setIdStr(idStr);
        request.setQueryFields(new String[]{"id", "articleNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        Map<String, ProductInfoVO> map = new HashMap<>(20000);

        for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
            try {
                EsAliexpressProductListing listing = esAliexpressProductListingService
                        .findAllById(aliexpressProductListing.getId());
                ProductInfoVO productInfoVO = map.get(listing.getArticleNumber());
                if (ObjectUtils.isEmpty(productInfoVO)) {
                    productInfoVO = ProductUtils.getSkuInfo(listing.getArticleNumber());
                    map.put(listing.getArticleNumber(), productInfoVO);
                }
                if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                    log.error(listing.getArticleNumber() + "ES无法查询信息");
                    continue;
                }
                EsAliexpressProductListingUtils.handleAliexpressProductinfo(listing, productInfoVO, false);
                esAliexpressProductListingService.save(listing);
            } catch (Exception e) {
                log.error("循环报错：", e);
            }
        }
        return ApiResult.newSuccess("设置成功！");
    }

    /**
     * 产品搬家 店铺下拉
     *
     * @return
     */
    @GetMapping("productMoveAccountList")
    public ApiResult<?> productMoveAccountList() {
        ApiResult<SaleAccountListResponse> authorAccountList;
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setIsNeedAccounts(true);
        request.setSaleChannel(SaleChannel.CHANNEL_SMT);

        //超管 和超级管理员
        ApiResult<Boolean> booleanApiResult = NewUsermgtUtils
                .isSuperAdminOrSupervisor(SaleChannel.CHANNEL_SMT);
        if (!booleanApiResult.isSuccess()) {
            return ApiResult.newError(booleanApiResult.getErrorMsg());
        }
        Boolean result = booleanApiResult.getResult();
        if (result) {
            //超管和刊登超级管理员查所有
            authorAccountList = EsAccountUtils.getAuthorAccountList(request);
        } else {
            //组员 组长 只看自己，初级主管看下属  判断角色是否包含主管
            ApiResult<Boolean> juniorEexecutive = NewUsermgtUtils.isJuniorEexecutive(SaleChannel.CHANNEL_SMT);
            if (!juniorEexecutive.isSuccess()) {
                return ApiResult.newError(juniorEexecutive.getErrorMsg());
            }
            Boolean eexecutiveResult = juniorEexecutive.getResult();
            //初级主管查询下级
            if (eexecutiveResult) {
                authorAccountList = EsAccountUtils.getAuthorAccountList(request);
            } else {
                //只查自己
                request.setIsOnlySelf(true);
                authorAccountList = EsAccountUtils.getAuthorAccountListOnlySelf(request);
            }
        }
        if (!authorAccountList.isSuccess()) {
            return ApiResult.newError(authorAccountList.getErrorMsg());
        }
        SaleAccountListResponse response = authorAccountList.getResult();
        return ApiResult.newSuccess(response);
    }

    /**
     * 产品搬家
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping("productMove")
    public ApiResult<?> productMove(@RequestBody(required = true) AliexpressEsExtendCriteria requestCriteria) {
        List<EsAliexpressProductListing> esAliexpressProductListing = requestCriteria.getEsAliexpressProductListing();
        //原店铺
        String aliexpressAccountNumber = requestCriteria.getAliexpressAccountNumber();
        //刊登店铺
        String publishAccount = requestCriteria.getPublishAccount();
        if (StringUtils.equalsIgnoreCase(aliexpressAccountNumber, publishAccount)) {
            return ApiResult.newError("刊登店铺不能相同！");
        }

        //是否全部产品 如果有勾选产品有限勾选的产品搬家
        Boolean wholeProduct = requestCriteria.getWholeProduct();
        if (wholeProduct != null && wholeProduct && CollectionUtils.isEmpty(esAliexpressProductListing)) {
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setAliexpressAccountNumber(aliexpressAccountNumber);
            request.setProductStatusType("onSelling,offline");
            request.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "articleNumber", "spu"});
            esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(request);
        }
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("没有可刊登的数据");
        }

        Map<Long, List<EsAliexpressProductListing>> productIdMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));
        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
            EsAliexpressProductListing productListing = longListEntry.getValue().get(0);
            PublishSend send = new PublishSend();
            send.productMoveCreateSend(PublishTypeEnum.product_moving.intCode(), productListing.getProductId(), productListing.getArticleNumber(), aliexpressAccountNumber, publishAccount, requestCriteria.getProductMoveBean());
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查看结果");
    }

    /**
     * 关键词匹配
     *
     * @param requestCriteria
     */
    @PostMapping("keyWordMatching")
    public ApiResult<?> keyWordMatching(@RequestBody(required = true) AliexpressEsExtendCriteria requestCriteria) {

        String keyWord = requestCriteria.getSubject();

        if (StringUtils.isBlank(keyWord)) {
            return ApiResult.newSuccess("请求成功！请到处理报告查看结果");
        }
        ResponseJson responseJson = excelSend.keyWordMatching(ExcelTypeEnum.keyWord.getCode(), keyWord);
        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess("请求成功！请到excel日志查看下载进度");
    }

    /**
     * 匹配产品最重重量
     *
     * @param updateGrossWeightList
     * @return
     */
    @PostMapping("findProductWeight")
    public ApiResult<?> findProductWeight(@RequestBody(required = true) List<EsAliexpressProductListing> updateGrossWeightList) {
        if (CollectionUtils.isEmpty(updateGrossWeightList)) {
            return ApiResult.newError("传参不能为空!");
        }
        Map<Long, Double> productIdtoWeightMap = new HashMap<>();
        Map<String, AliexpressConfig> configMap = new HashMap<>();
        try {
            for (EsAliexpressProductListing esAliexpressProductListing : updateGrossWeightList) {
                Long productId = esAliexpressProductListing.getProductId();
                String aliexpressAccountNumber = esAliexpressProductListing.getAliexpressAccountNumber();

                AliexpressConfig aliexpressConfig = configMap.get(aliexpressAccountNumber);
                if (aliexpressConfig == null) {
                    aliexpressConfig = aliexpressConfigService.selectByAccount(aliexpressAccountNumber);
                    configMap.put(aliexpressAccountNumber, aliexpressConfig);
                }
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setQueryFields(new String[]{"id", "spu", "articleNumber"});
                request.setProductId(productId);
                request.setAliexpressAccountNumber(aliexpressAccountNumber);
                List<EsAliexpressProductListing> productList = esAliexpressProductListingService
                        .getEsAliexpressProductListing(request);

                if (CollectionUtils.isEmpty(productList)) {
                    return ApiResult.newError(String.format("产品id[%s]获取不到产品信息", productId));
                }

                List<String> spuList = productList.stream().map(t -> StringUtils.isEmpty(t.getSpu()) ? t.getArticleNumber() : t.getSpu())
                        .collect(Collectors.toList());

                //需要找到产品内 最重的spu
                ProductInfo maxWeightSingleItem = singleItemEsService.getMaxWeightSingleItemForSpuList(spuList);
                if (maxWeightSingleItem == null) {
                    return ApiResult.newError(String.format("产品id[%s]spu[%s]获取不到产品信息", productId, StringUtils.join(spuList, ",")));
                }

                //单位g
                Double maxWeight = AliexpressWeightUtils.getMaxWeight(maxWeightSingleItem, aliexpressConfig.getAddWeight());
                //按照净重+包材+包装材料+面单3g + 店铺配置增加重量
                double format = com.estone.erp.publish.common.util.NumberUtils.format(maxWeight);
                double v = AliexpressWeightUtils.gramsToKilograms(format, 3, RoundingMode.UP);
                productIdtoWeightMap.put(productId, v);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(productIdtoWeightMap);
    }


    /**
     * 生成全托管模板
     *
     * @param productId
     * @return
     */
    @GetMapping("generateTgTemplate")
    public ApiResult<?> generateTgTemplate(@RequestParam(value = "productId") Long productId,
                                           @RequestParam(value = "account") String account,
                                           @RequestParam(value = "grossMargin") Double grossMargin) {
        Asserts.isTrue(productId != null, ErrorCode.PARAM_EMPTY_ERROR, "productId参数不能为空！");
        Asserts.isTrue(StringUtils.isNotBlank(account), ErrorCode.PARAM_EMPTY_ERROR, "account参数不能为空！");
        Asserts.isTrue(grossMargin != null, ErrorCode.PARAM_EMPTY_ERROR, "grossMargin参数不能为空！");
        try {
            AliexpressTgTemplate template = aliexpressEsExtendService.esTransTgTemp(productId, account, grossMargin);
            String articleNumber = template.getArticleNumber();
            List<String> images = FmsUtils.getSmtImgs(articleNumber, null);

            images = AliexpressContentUtils.imgPriorityForList(images);
            template.setImages(images);
            return ApiResult.newSuccess(template);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 批量刊登
     *
     * @param tgBatchPublishBean
     * @return
     */
    @PostMapping("batchPublishTgTemplate")
    public ApiResult<?> batchPublishTgTemplate(@RequestBody TgBatchPublishBean tgBatchPublishBean) {
        if (tgBatchPublishBean == null) {
            return ApiResult.newError("参数不能为空！");
        }
        String productIdStr = tgBatchPublishBean.getProductIdStr();
        String account = tgBatchPublishBean.getAccount();
        Double grossMargin = tgBatchPublishBean.getGrossMargin();
        if (StringUtils.isBlank(productIdStr) || StringUtils.isBlank(account) || grossMargin == null) {
            return ApiResult.newError("参数不能为空！");
        }
        List<Long> productIdList = CommonUtils.splitLongList(productIdStr, ",");
        for (Long productId : productIdList) {
            aliexpressTgTemplateService.popItemPublish(productId, account, grossMargin);
        }
        return ApiResult.newSuccess("后台处理中");
    }

    /**
     * 获取制造商列表
     *
     * @param aliexpressAccountNumber
     * @return
     */
    @GetMapping(value = "/getMerchantList")
    public ApiResult<?> getMerchantList(
            @RequestParam(value = "aliexpressAccountNumber") String aliexpressAccountNumber,
            @RequestParam(value = "isTg", defaultValue = "false") Boolean isTg) {
        try {
            if (StringUtils.isBlank(aliexpressAccountNumber)) {
                return ApiResult.newError("账号必填！");
            }

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

            MerchantListCall merchantListCall = new MerchantListCall();
            ResponseJson rsp = merchantListCall.merchantList(saleAccountByAccountNumber, isTg);
            if (!rsp.isSuccess()) {
                return ApiResult.newError(rsp.getMessage());
            }
            return ApiResult.newSuccess(rsp.getBody().get("map"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }


    /**
     * 在线列表编辑
     *
     * @param productId
     * @return
     */
    @GetMapping("findEsProduct")
    public ApiResult<?> findEsProduct(@RequestParam(value = "productId") Long productId) {
        Asserts.isTrue(productId != null, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
        try {
            AliexpressTemplate template = aliexpressEsExtendService.esTransTemp(productId);
            template.setProductId(productId);

            String articleNumber = template.getArticleNumber();
            List<String> images = FmsUtils.getSmtImgs(articleNumber, null);

            images = AliexpressContentUtils.imgPriorityForList(images);
            template.setImages(images);

            return ApiResult.newSuccess(template);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 修改admin范本属性
     *
     * @param aliexpressTemplate
     * @return
     */
    @PostMapping("updateAdminAttr")
    public ApiResult<?> updateAdminAttr(@RequestBody(required = true) AliexpressTemplate aliexpressTemplate) {
        String articleNumber = aliexpressTemplate.getArticleNumber();
        Integer categoryId = aliexpressTemplate.getCategoryId();
        String aeopAeProductPropertysJson = aliexpressTemplate.getAeopAeProductPropertysJson();

        AliexpressAutoTemplateExample autoTemplateExample = new AliexpressAutoTemplateExample();
        autoTemplateExample.createCriteria().andCategoryIdEqualTo(categoryId).andArticleNumberEqualTo(articleNumber);
        autoTemplateExample.setFields("id");
        List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService.selectByExample(autoTemplateExample);

        if (CollectionUtils.isEmpty(aliexpressAutoTemplates)) {
            return ApiResult.newSuccess("没有需要更新的admin范本");
        }

        for (AliexpressAutoTemplate aliexpressAutoTemplate : aliexpressAutoTemplates) {
            aliexpressAutoTemplate.setAeopAeProductPropertysJson(aeopAeProductPropertysJson);
            aliexpressAutoTemplateService.updateByPrimaryKeySelective(aliexpressAutoTemplate);
        }
        List<Integer> adminTempIdList = aliexpressAutoTemplates.stream().map(t -> t.getId()).collect(Collectors.toList());
        SmtAdminTempAttrRecordExample adminTempAttrRecordExample = new SmtAdminTempAttrRecordExample();
        adminTempAttrRecordExample.createCriteria().andAdminTempIdIn(adminTempIdList).andResultTypeEqualTo(0);
        List<SmtAdminTempAttrRecord> smtAdminTempAttrRecords = smtAdminTempAttrRecordService.selectByExample(adminTempAttrRecordExample);
        if (CollectionUtils.isNotEmpty(smtAdminTempAttrRecords)) {
            for (SmtAdminTempAttrRecord smtAdminTempAttrRecord : smtAdminTempAttrRecords) {
                smtAdminTempAttrRecord.setResultType(1);
                smtAdminTempAttrRecordService.updateByPrimaryKeySelective(smtAdminTempAttrRecord);
            }
        }
        return ApiResult.newSuccess("更新成功！");
    }

    @PostMapping("updateEsProduct")
    public ApiResult<?> updateEsProduct(@RequestBody(required = true) AliexpressTemplate aliexpressTemplate) {
        long begin = System.currentTimeMillis();
        String aliexpressAccountNumber = aliexpressTemplate.getAliexpressAccountNumber();
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
        try {
            String skuPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();
            List<TempSkuProperty> productSkuProperties = JSON.parseObject(aliexpressTemplate.getAeopAeProductSkusJson(),
                    new TypeReference<List<TempSkuProperty>>() {
                    });

            List<Integer> skuStatusList = Arrays.asList(SingleItemEnum.CLEARANCE.getCode(), SingleItemEnum.REDUCTION.getCode());

            for (TempSkuProperty productSkuProperty : productSkuProperties) {
                Integer ipm_sku_stock = productSkuProperty.getIpm_sku_stock();
                String sku_code = productSkuProperty.getSku_code();
                if (ipm_sku_stock == null || ipm_sku_stock.intValue() == 0) {
                    //需要判断货号是否清仓甩卖 和实际库存 smt 禁售等
                    sku_code = sku_code.replaceFirst(skuPrefix, "");
                    SingleItemEs skuInfo = singleItemEsService.getSkuInfo(sku_code);
                    if (skuInfo != null) {
                        Integer itemStatus = skuInfo.getItemStatus();
                        if (skuStatusList.contains(itemStatus)) {
                            Integer skuSystemStock = SkuStockUtils.getSkuSystemStock(sku_code);
                            Boolean hasForbiddenSales = AliexpressCheckUtils.isHasForbiddenSales(SaleChannel.CHANNEL_SMT, Arrays.asList(sku_code));
                            if ((!hasForbiddenSales && skuSystemStock == null) || (!hasForbiddenSales && skuSystemStock != null && skuSystemStock.intValue() > 0)) {
                                String errMsg = String.format("sku[%s]单品状态为[%s]，且SKU在SMT不禁售，库存+在途-待发[%s] 不允许修改", sku_code, SingleItemEnum.getNameByCode(itemStatus), skuSystemStock);
                                return ApiResult.newError(errMsg);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            return ApiResult.newError("修改失败" + e.getMessage());
        }

        long end = System.currentTimeMillis();
        if ((end - begin) > 5000L) {
            log.info("编辑产品判断单品状态耗时" + (end - begin));
        }

        //aliexpressTemplate 转成编辑产品
        ResponseJson responseJson = aliexpressEsExtendService.updateProduct(aliexpressTemplate);

        Long productId = aliexpressTemplate.getProductId();

        //需要修改的制造商id
        Long upManufactureId = aliexpressTemplate.getManufactureId();

        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setProductId(productId);
        listingRequest.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "articleNumber", "productId", "manufactureId", "manufactureName"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            Long dbManufactureId = esAliexpressProductListing.get(0).getManufactureId();
            if (upManufactureId != null && (dbManufactureId == null || dbManufactureId.longValue() != upManufactureId)) {
                //需要修改
                MerchantBindCall merchantBindCall = new MerchantBindCall();
                ResponseJson bindRsp = merchantBindCall.merchantList(saleAccountByAccountNumber, false, productId, upManufactureId);

                //修改es数据
                if (bindRsp.isSuccess()) {
                    List<EsAliexpressProductListing> items = new ArrayList<>();
                    for (EsAliexpressProductListing list : esAliexpressProductListing) {
                        EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                        updateItem.setId(list.getId());
                        updateItem.setManufactureId(upManufactureId);
                        updateItem.setManufactureName(aliexpressTemplate.getManufactureName().trim());
                        items.add(updateItem);
                    }
                    smtItemEsBulkProcessor.batchUpdateListingManufacture(items);
                }

                //记录处理报告
                AliexpressProductLog productLog = new AliexpressProductLog();
                productLog.setOperateType(OperateLogTypeEnum.UPDATE_MANUFACTURER.getCode());
                productLog.setAccountNumber(aliexpressAccountNumber);
                productLog.setOperateStatus(OperateLogStatusEnum.end.getCode());
                productLog.setProductId(productId);
                productLog.setSkuCode(esAliexpressProductListing.get(0).getArticleNumber());
                productLog.setOperator(WebUtils.getUserName());
                String manufactureName = aliexpressTemplate.getManufactureName();
                if (StringUtils.isNotBlank(manufactureName)) {
                    productLog.setNewRemark(manufactureName.trim());
                }
                productLog.setResult(bindRsp.isSuccess());
                productLog.setFailInfo(bindRsp.getMessage());
                aliexpressProductLogService.insert(productLog);
            }
        }

        if (responseJson.isSuccess()) {
            return ApiResult.newSuccess("修改成功");
        }
        return ApiResult.newError("修改失败" + responseJson.getMessage());
    }


    /**
     * 上传修改标题
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/uploadUpdateSubject")
    public ApiResult<?> uploadUpdateSubject(HttpServletRequest request) {
        MultipartFile file = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        if (fileMap.values().size() > 0) {
            file = (MultipartFile) fileMap.values().iterator().next();
        } else {
            return ApiResult.of(false, null, "请先上传文件!");
        }

        return aliexpressEsExtendService.uploadUpdateSubject(file);
    }

    /**
     * 修改销售方式
     *
     * @param requestCriteriaList
     */
    @PostMapping("updateSaleMode")
    public ApiResult<?> updateSaleMode(@RequestBody(required = true) List<AliexpressEsExtendCriteria> requestCriteriaList) {
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        String userName = WebUtils.getUserName();
        for (AliexpressEsExtendCriteria criteria : requestCriteriaList) {
            //需要修改的产品id
            String aliexpressAccountNumber = criteria.getAliexpressAccountNumber();
            Long productId = criteria.getProductId();
            Integer productUnit = criteria.getProductUnit();
            Boolean packageType = criteria.getPackageType();
            Integer lotNum = criteria.getLotNum();
            SaleAccountAndBusinessResponse saleAccount = accountMap.get(aliexpressAccountNumber);
            if (saleAccount == null) {
                saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, saleAccount);
            }
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
            AliexpressExecutors.smtUpdateSaleMode(() -> {
                aliexpressEsExtendService.updateSaleMode(saleAccountAndBusinessResponse, productId, productUnit, packageType, lotNum, userName);
            });
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果");
    }

    /**
     * 修改资质
     *
     * @param requestCriteriaList
     */
    @PostMapping("updateQualification")
    public ApiResult<?> updateQualification(@RequestBody(required = true) List<AliexpressEsExtendCriteria> requestCriteriaList) {
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        String userName = WebUtils.getUserName();
        for (AliexpressEsExtendCriteria criteria : requestCriteriaList) {
            //店铺 多个英文逗号分割
            String aliexpressAccountNumber = criteria.getAliexpressAccountNumber();
            Long productId = criteria.getProductId();
            String aeopQualificationStructJson = criteria.getAeopQualificationStructJson();

            SaleAccountAndBusinessResponse saleAccount = accountMap.get(aliexpressAccountNumber);
            if (saleAccount == null) {
                saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, saleAccount);
            }

            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);

            //查询真实存在的产品
            EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
            listingRequest.setAliexpressAccountNumber(aliexpressAccountNumber);
            listingRequest.setProductId(productId);
            listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            listingRequest.setQueryFields(new String[]{"id", "productId"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                continue;
            }

            List<Long> collect = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            Set<Long> productSet = new HashSet<>(collect);
            if (CollectionUtils.isEmpty(productSet)) {
                continue;
            }
            for (Long aLong : productSet) {
                AliexpressExecutors.smtUpdateQualification(() -> {
                    aliexpressEsExtendService.updateQualification(saleAccountAndBusinessResponse, aLong, aeopQualificationStructJson, userName, false, false);
                });
            }
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果");
    }


    /**
     * 通过spu集合修改资质
     *
     * @param criteria
     */
    @PostMapping("updateQualificationBySpus")
    public ApiResult<?> updateQualificationBySpus(@RequestBody(required = true) AliexpressEsExtendCriteria criteria) {
        if (criteria == null) {
            return ApiResult.newError("参数不能为空！");
        }
        List<String> spuList = criteria.getSpuList();
        if (CollectionUtils.isEmpty(spuList)) {
            return ApiResult.newError("spu不能为空");
        }

        List<Integer> updateQualificationTypeList = criteria.getUpdateQualificationTypeList();

        AliexpressConfigExample configExample = new AliexpressConfigExample();
        configExample.createCriteria().andUsableEqualTo(true).andAutoUpdateQualificationsEqualTo(true);
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);
        if (CollectionUtils.isEmpty(aliexpressConfigs)) {
            return ApiResult.newError("没有启用店铺自动修改资质！");
        }

        List<String> autoAccountList = aliexpressConfigs.stream().map(t -> t.getAccount()).collect(Collectors.toList());

        List<List<String>> lists = PagingUtils.newPagingList(spuList, 50);

        for (List<String> list : lists) {
            try {
                EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
                productListingRequest.setSpuList(list);
                //只操作开启自动修改资质的店铺
                productListingRequest.setAliexpressAccountNumber(StringUtils.join(autoAccountList, ","));
                productListingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                productListingRequest.setIsHasQualification(false);//只改资质为否的数据
                productListingRequest.setQueryFields(new String[]{"id", "spu", "aliexpressAccountNumber", "productId", "categoryId"});
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
                if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                    continue;
                }

                Map<Long, List<EsAliexpressProductListing>> productIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                    EsAliexpressProductListing productListing = longListEntry.getValue().get(0);
                    SpuUpdateQualification spuUpdateQualification = new SpuUpdateQualification();
                    spuUpdateQualification.setAliexpressAccountNumber(productListing.getAliexpressAccountNumber());
                    spuUpdateQualification.setCategoryId(productListing.getCategoryId());
                    spuUpdateQualification.setProductId(productListing.getProductId());
                    spuUpdateQualification.setSpu(productListing.getSpu());
                    spuUpdateQualification.setUserName(WebUtils.getUserName());
                    spuUpdateQualification.setUpdateQualificationTypeList(updateQualificationTypeList);
                    //3.发送队列
                    rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_QUALIFICATION_ROUTE_KEY, spuUpdateQualification);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果！");
    }


    /**
     * 通过excel修改资质
     *
     * @param multiPartFile
     * @param updateQualificationTypeList
     */
    @PostMapping("updateQualificationByExcel")
    public ApiResult<?> updateQualificationByExcel(@RequestParam(value = "file") MultipartFile multiPartFile,
                                                   @RequestParam(value = "updateQualificationTypeList", required = false) List<Integer> updateQualificationTypeList,
                                                   HttpServletRequest request) throws Exception {
        if (multiPartFile == null) {
            return ApiResult.newError("excel文件不能为空！");
        }

        String userName = WebUtils.getUserName();
        final String[] headerState = {"itemID"};
        MultipartFile file = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        if (fileMap.values().size() > 0) {
            file = (MultipartFile) fileMap.values().iterator().next();
            com.estone.erp.publish.common.util.POIUtils.readExcelSheet1(headerState, file, row -> {
                try {
                    if (row == null) {
                        return null;
                    }
                    if (!ExcelUtils.isNotBlankCell(row.getCell(0))) {
                        return null;
                    }
                    String productIdStr = ExcelUtils.getCellValue(row.getCell(0)).trim();

                    // 查询在线列表数据
                    EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
                    productListingRequest.setProductIdStr(productIdStr);
                    productListingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                    productListingRequest.setQueryFields(new String[]{"id", "spu", "aliexpressAccountNumber", "productId", "categoryId", "articleNumber"});
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);

                    if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                        log.error("修改GPSR " + productIdStr + "es查询不到在线产品");
                        return null;
                    }
                    AliexpressExecutors.smtUpdateQualificationByTime(() -> {
                        EsAliexpressProductListing productListing = esAliexpressProductListing.get(0);
                        String spu = productListing.getSpu();
                        if (StringUtils.isBlank(spu)) {
                            spu = esAliexpressProductListing.get(0).getArticleNumber();
                        }
                        String account = esAliexpressProductListing.get(0).getAliexpressAccountNumber();

                        SpuUpdateQualification spuUpdateQualification = new SpuUpdateQualification();
                        spuUpdateQualification.setAliexpressAccountNumber(account);
                        spuUpdateQualification.setCategoryId(productListing.getCategoryId());
                        spuUpdateQualification.setProductId(productListing.getProductId());
                        spuUpdateQualification.setSpu(spu);
                        spuUpdateQualification.setUserName(userName);
                        spuUpdateQualification.setUpdateQualificationTypeList(updateQualificationTypeList);
                        //3.发送队列
                        rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_QUALIFICATION_ROUTE_KEY, spuUpdateQualification);

                    });
                    return row;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    return null;
                }
            }, false);
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果！");
    }

    /**
     * 修改产品分类
     *
     * @param esExtendCriteria
     * @return
     */
    @PostMapping("updateCategoryId")
    public ApiResult<?> updateCategoryId(@RequestBody(required = true) AliexpressEsExtendCriteria esExtendCriteria) {
        if (esExtendCriteria == null ||
                StringUtils.isBlank(esExtendCriteria.getAliexpressAccountNumber())
                || StringUtils.isBlank(esExtendCriteria.getProductIdStr())
                || StringUtils.isBlank(esExtendCriteria.getCayJson()) ||
                esExtendCriteria.getCategoryId() == null) {
            return ApiResult.newError("参数不能为空！");
        }

        String aliexpressAccountNumber = esExtendCriteria.getAliexpressAccountNumber();
        String productIdStr = esExtendCriteria.getProductIdStr();
        Integer categoryId = esExtendCriteria.getCategoryId();
        String cayJson = esExtendCriteria.getCayJson();//属性

        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
        String userName = WebUtils.getUserName();

        List<Long> longs = CommonUtils.splitLongList(productIdStr, ",");
        for (Long productId : longs) {
            AliexpressExecutors.smtUpdateSaleMode(() -> {
                aliexpressEsExtendService.updateProductCategoryId(saleAccountAndBusinessResponse, productId, categoryId, cayJson, userName);
            });
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果");
    }

    /**
     * 批量删除产品
     *
     * @param deleteList
     * @return
     */
    @PostMapping(value = "/batchDeleteProduct")
    public ApiResult<?> batchDeleteProduct(@RequestBody(required = true) List<EsAliexpressProductListing> deleteList) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(deleteList), ErrorCode.PARAM_EMPTY_ERROR);
        Set<Long> productIdSet = deleteList.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
        if (productIdSet.size() > 1000) {
            return ApiResult.newError("一次最多只允许删除1000个");
        }

        Map<Long, List<EsAliexpressProductListing>> longListMap = deleteList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        String userName = WebUtils.getUserName();
        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : longListMap.entrySet()) {
            EsAliexpressProductListing esAliexpressProductListing = longListEntry.getValue().get(0);
            String aliexpressAccountNumber = esAliexpressProductListing.getAliexpressAccountNumber();
            Long productId = esAliexpressProductListing.getProductId();
            SaleAccountAndBusinessResponse saleAccount = accountMap.get(aliexpressAccountNumber);
            if (saleAccount == null) {
                saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, saleAccount);
            }
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
            AliexpressExecutors.smtDeleteProduct(() -> {
                aliexpressEsExtendService.deleteProduct(saleAccountAndBusinessResponse, productId, userName, null);
            });
        }
        return ApiResult.newSuccess("请求成功！请到处理报告查询修改结果！");
    }

    /**
     * 批量删除本地数据
     *
     * @param accountList
     * @return
     */
    @PostMapping(value = "/batchDeleteAccountsData")
    public ApiResult<?> batchDeleteAccountsData(@RequestBody(required = true) List<String> accountList) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(accountList), ErrorCode.PARAM_EMPTY_ERROR);
        String userName = WebUtils.getUserName();
        for (String account : accountList) {
            //1.检测是否存在任务
            AliexpressProductLogExample logExample = new AliexpressProductLogExample();
            logExample.createCriteria().andAccountNumberEqualTo(account)
                    .andOperateTypeEqualTo(OperateLogTypeEnum.delete_account_data.getCode())
                    .andOperateStatusNotEqualTo(OperateLogStatusEnum.end.intCode());
            List<AliexpressProductLog> aliexpressProductLogs = aliexpressProductLogService.selectByExample(logExample);
            if (CollectionUtils.isNotEmpty(aliexpressProductLogs)) {
                continue;
            }
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(account);
            log.setOperateType(OperateLogTypeEnum.delete_account_data.getCode());
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            log.setOperator(userName);
            aliexpressProductLogService.insert(log);

            AliexpressEsProductDeleteRecord productDeleteRecord = new AliexpressEsProductDeleteRecord();
            productDeleteRecord.setAccount(account);
            productDeleteRecord.setOperateLogId(log.getId());
            productDeleteRecord.setStatus(OperateLogStatusEnum.wait.intCode());
            aliexpressEsProductDeleteRecordService.insert(productDeleteRecord);
//            AliexpressExecutors.smtDeleteProduct(()->{
//                aliexpressEsExtendService.deleteAccountData(account, userName);
//            });
        }
        return ApiResult.newSuccess("请求成功！后台正在处理中");

    }

    /**
     * 通过选择删除
     *
     * @param productIdList
     * @return
     */
    @PostMapping(value = "/batchDeleteBySelect")
    public ApiResult<?> batchDeleteBySelect(@RequestBody(required = true) List<Long> productIdList) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(productIdList), ErrorCode.PARAM_EMPTY_ERROR);
        String userName = WebUtils.getUserName();
        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setProductIdList(productIdList);
        listingRequest.setQueryFields((new String[]{"id", "aliexpressAccountNumber", "productId"}));
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newSuccess("数据删除成功！");
        }

        long begin = System.currentTimeMillis();
        List<List<EsAliexpressProductListing>> lists = PagingUtils.newPagingList(esAliexpressProductListing, 1000);
        for (List<EsAliexpressProductListing> list : lists) {
            esAliexpressProductListingService.deleteByList(list);
        }

        //用产品id删除，防止店铺正常 误操作，导致删除后面同步的产品
        Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
        aliexpressEsExtendService.deleteByProductId(new ArrayList<>(productIdSet));
        long end = System.currentTimeMillis();
        long l = (end - begin) / 1000;
        if (l > 120) {
            log.info(esAliexpressProductListing.size() + "条数据删除耗时" + l);
        }

        Map<Long, List<EsAliexpressProductListing>> productIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
            String aliexpressAccountNumber = longListEntry.getValue().get(0).getAliexpressAccountNumber();
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(aliexpressAccountNumber);
            log.setProductId(longListEntry.getKey());
            log.setOperateType(OperateLogTypeEnum.delete_account_data.getCode());
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setOperator(userName);
            log.setResult(true);
            aliexpressProductLogService.insert(log);
        }
        return ApiResult.newSuccess("删除成功！数据存在延迟 请等待10秒后再查询！");

    }

    /**
     * 根据账号和产品id获取折扣（提供给爬虫）
     *
     * @param accountProductIdBean
     * @return
     */
    @PostMapping("/getDiscountByProductId")
    public ApiResult<Map<String, List<String>>> getDiscountByProductId(@RequestBody AccountProductIdBean accountProductIdBean) {
        String accountNumber = accountProductIdBean.getAccountNumber();
        List<String> productIdList = accountProductIdBean.getProductIdList();
        if (StringUtils.isBlank(accountNumber) || CollectionUtils.isEmpty(productIdList)) {
            return ApiResult.newError("参数不能为空");
        }
        Map<String, List<String>> discountMap;
        try {
            discountMap = aliexpressEsExtendService.getDiscountByProductId(accountNumber, productIdList);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(discountMap);
    }

    /**
     * 根据账号获取所有产品id
     *
     * @param accountNumber
     * @return
     */
    @GetMapping("/getProductIdByAccount")
    public ApiResult<List<Long>> getProductIdByAccount(@RequestParam("accountNumber") String accountNumber) {
        if (StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("参数不能为空");
        }

        List<Long> productIdList;
        try {
            productIdList = aliexpressEsExtendService.getProductIdByAccount(accountNumber);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(productIdList);
    }

    /**
     * 同步品牌
     *
     * @param request
     * @return
     */
    @PostMapping("/syncBrand")
    public ApiResult<String> syncBrand(@RequestBody BrandRequestDO request) {
        if (null == request || StringUtils.isBlank(request.getAccountNumber())) {
            return ApiResult.newError("参数异常");
        }

        String accountNumber = request.getAccountNumber();
        List<Integer> categoryIdList = request.getCategoryIdList();
        try {
            aliexpressEsExtendService.syncAccountBrand(accountNumber, categoryIdList);
            return ApiResult.newSuccess("同步品牌执行成功");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 批量修改制造商
     *
     * @param criteria
     * @return
     */
    @PostMapping("/batchUpManufacturer")
    public ApiResult<?> batchUpManufacturer(@RequestBody AliexpressProductCriteria criteria) {
        String aliexpressAccountNumber = criteria.getAliexpressAccountNumber(); //店铺
        String productIdStr = criteria.getProductIdStr(); //产品id
        if (StringUtils.isBlank(aliexpressAccountNumber) && StringUtils.isBlank(productIdStr)) {
            return ApiResult.newError("参数必填!");
        }

        CQuery<EsAliexpressProductListingRequest> cquery = new CQuery<>();
        EsAliexpressProductListingRequest querySearch = new EsAliexpressProductListingRequest();
        cquery.setSearch(querySearch);
        //默认不查产品
        cquery.setPageReqired(false);

        querySearch.setPageFields(new String[]{"id", "aliexpressAccountNumber", "productId", "categoryId", "articleNumber", "skuStatus", "manufactureId", "manufactureName"});

        querySearch.setIsSeleteProduct(false);
        List<String> productStatusList = Arrays.asList(ProductStatusTypeEnum.onSelling.getCode(), ProductStatusTypeEnum.auditing.getCode());
        querySearch.setProductStatusType(StringUtils.join(productStatusList, ","));//只查上架
        if (StringUtils.isNotBlank(productIdStr)) {
            querySearch.setProductIdStr(productIdStr);
        } else if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
            querySearch.setAliexpressAccountNumber(aliexpressAccountNumber);
        }
        querySearch.setOrderBy("gmtCreate"); //产品创建时间排序

        String userName = WebUtils.getUserName();

        try {
            int offset = 0;
            int limit = 1000;

            while (true) {
                cquery.setLimit(limit);
                cquery.setOffset(offset);
                Page<EsAliexpressProductListing> results = esAliexpressProductListingService
                        .page(querySearch, cquery.getLimit(), cquery.getOffset());
                if (results == null || results.getContent() == null || CollectionUtils.isEmpty(results.getContent())) {
                    break;
                }
                List<EsAliexpressProductListing> contentList = results.getContent();
                AliexpressExecutors.UPDATE_MANUFACTURE_POOL.execute(() -> {
                    aliexpressEsExtendService.upManufacturer(contentList, userName);
                });
                offset++;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 获取修改CE 认证数据
     *
     * @param criteria
     * @return
     */
    @PostMapping("/obtainUpdateCeData")
    public ApiResult<?> obtainUpdateCeData(@RequestBody AliexpressProductCriteria criteria) {

        //勾选数据
        String idStr = criteria.getIdStr();
        //产品id
        String productIdStr = criteria.getProductIdStr();
        //选中的店铺
        String aliexpressAccountNumber = criteria.getAliexpressAccountNumber();

        CQuery<EsAliexpressProductListingRequest> cquery = new CQuery<>();

        EsAliexpressProductListingRequest querySearch = new EsAliexpressProductListingRequest();
        cquery.setSearch(querySearch);
        //默认不查产品
        cquery.setPageReqired(false);

        querySearch.setPageFields(new String[]{"id", "aliexpressAccountNumber", "productId", "categoryId"});

        querySearch.setIsSeleteProduct(false);
        querySearch.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());//只查在线
        if (StringUtils.isNotBlank(idStr)) {
            querySearch.setIdStr(idStr);
        } else if (StringUtils.isNotBlank(productIdStr)) {
            querySearch.setProductIdStr(productIdStr);
        } else if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
            querySearch.setAliexpressAccountNumber(aliexpressAccountNumber);
        }

        Set<Long> productIdSet = new HashSet<>();
        Set<Integer> categoryIdSet = new HashSet<>();
        List<EsAliexpressProductListing> lists = new ArrayList<>();

        UpdateCeBean updateCeBean = new UpdateCeBean();

        try {
            int offset = 0;
            int limit = 1000;

            while (true) {
                cquery.setLimit(limit);
                cquery.setOffset(offset);
                EsAliexpressProductListingResponse esResponse = aliexpressEsExtendService.list(cquery);
                if (esResponse == null || esResponse.getEsProductListingPage() == null || CollectionUtils.isEmpty(esResponse.getEsProductListingPage().getContent())) {
                    break;
                }
                List<EsAliexpressProductListing> content = esResponse.getEsProductListingPage().getContent();
                for (EsAliexpressProductListing esAliexpressProductListing : content) {
                    Long productId = esAliexpressProductListing.getProductId();
                    categoryIdSet.add(esAliexpressProductListing.getCategoryId());
                    if (!productIdSet.contains(productId.longValue())) {
                        lists.add(esAliexpressProductListing);
                        productIdSet.add(productId);
                    }
                }
                offset++;
            }

            if (CollectionUtils.isEmpty(lists)) {
                return ApiResult.newError("没有可用数据");
            }

            Map<Integer, List<EsAliexpressProductListing>> dataMap = lists.stream().collect(Collectors.groupingBy(t -> t.getCategoryId()));
            updateCeBean.setDataMap(dataMap);

            AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
            categoryExample.createCriteria().andCategoryIdIn(new ArrayList<>(categoryIdSet));
            List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategoryTree(categoryExample);
            Map<Integer, String> categoryIdMap = new HashMap<>();
            for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
                Integer categoryId = aliexpressCategory.getCategoryId();
                String fullCnName = aliexpressCategory.getFullCnName();
                categoryIdMap.put(categoryId, fullCnName);
            }
            updateCeBean.setCategoryIdMap(categoryIdMap);
            if (CollectionUtils.isEmpty(lists)) {
                return ApiResult.newError("没有可用数据");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(updateCeBean);
    }

    /**
     * 批量更新欧盟负责人
     *
     * @param UpdateCeBeanList
     * @return
     */
    @PostMapping("batchUpdateEsProductEuId")
    public ApiResult<?> batchUpdateEsProductEuId(@RequestBody List<UpdateCeBean> UpdateCeBeanList) {
        String userName = WebUtils.getUserName();
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (UpdateCeBean updateCeBean : UpdateCeBeanList) {
            String idStr = updateCeBean.getIdStr();
            Long msrEuId = updateCeBean.getMsrEuId();
            String msrEuIdName = updateCeBean.getMsrEuIdName(); //只传名称

            List<String> idList = CommonUtils.splitList(idStr, ",");

            for (String id : idList) {
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setId(id);
                request.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "categoryId", "msrEuId"});
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                    continue;
                }

                Long listMsrEuId = esAliexpressProductListing.get(0).getMsrEuId();
                Long productId = esAliexpressProductListing.get(0).getProductId();
                String aliexpressAccountNumber = esAliexpressProductListing.get(0).getAliexpressAccountNumber();
                Integer categoryId = esAliexpressProductListing.get(0).getCategoryId();

                //如果一样就不用调整
                if (listMsrEuId != null && msrEuId != null && msrEuId == listMsrEuId.longValue()) {
                    AliexpressProductLog productLog = new AliexpressProductLog();
                    productLog.setAccountNumber(aliexpressAccountNumber);
                    productLog.setProductId(productId);
                    productLog.setOperateType(OperateLogTypeEnum.update_product_euid.getCode());
                    productLog.setOperator(userName);
                    productLog.setNewRemark(msrEuId.toString()); //欧盟负责人
                    productLog.setResult(true);
                    productLog.setFailInfo("欧盟责任人一样不需要调整");
                    aliexpressProductLogService.insert(productLog);
                    continue;
                }

                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
                if (saleAccountAndBusinessResponse == null) {
                    saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    if (aliexpressAccountNumber == null) {
                        continue;
                    }
                    accountMap.put(aliexpressAccountNumber, saleAccountAndBusinessResponse);
                }
                SaleAccountAndBusinessResponse saleAccountByAccountNumber = saleAccountAndBusinessResponse;
                AliexpressExecutors.updateEuId(() -> {
                    //先记录处理报告，在发送mq处理
                    AliexpressProductLog productLog = new AliexpressProductLog();
                    productLog.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                    productLog.setProductId(productId);
                    productLog.setOperateType(OperateLogTypeEnum.update_product_euid.getCode());
                    productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                    productLog.setOperator(userName);
                    productLog.setNewRemark(msrEuId != null ? msrEuId.toString() : msrEuIdName); //欧盟负责人
                    aliexpressProductLogService.insert(productLog);

                    UpMsrEuIdBean upMsrEuIdBean = new UpMsrEuIdBean();
                    upMsrEuIdBean.setProductLog(productLog);
                    upMsrEuIdBean.setMsrEuId(msrEuId);
                    upMsrEuIdBean.setCategoryId(categoryId);
                    upMsrEuIdBean.setListMsrEuId(listMsrEuId);
                    upMsrEuIdBean.setMsrEuIdName(msrEuIdName);
                    rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_MSR_EU_ID_KEY, JSON.toJSON(upMsrEuIdBean));
//                    aliexpressEsExtendService.updateProductEuId(saleAccountByAccountNumber, productId, msrEuId, userName);
                });
            }
        }
        return ApiResult.newSuccess("请求成功，后台处理中，请到处理报告查看处理结果！");
    }


    /**
     * 批量修改产品折扣
     *
     * @param requestCriteria
     * @return
     */
    @PostMapping(value = "/batchSetBulkDiscount")
    public ApiResult<?> batchSetBulkDiscount(@RequestBody(required = true) EsAliexpressProductListingRequest requestCriteria) {
        List<Long> productIdList = requestCriteria.getProductIdList();
        Integer bulkOrder = requestCriteria.getBulkOrder();
        Integer bulkDiscount = requestCriteria.getBulkDiscount();
        if (CollectionUtils.isEmpty(productIdList) || bulkOrder == null || bulkDiscount == null) {
            return ApiResult.newError("参数不能为空！");
        }

        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        esRequest.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "articleNumber"});
        esRequest.setProductIdList(productIdList);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return ApiResult.newError("店铺没有在线状态数据需要修改！");
        }

        Map<Long, List<EsAliexpressProductListing>> productIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
        String userName = WebUtils.getUserName();

        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
            String aliexpressAccountNumber = longListEntry.getValue().get(0).getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
            if (saleAccountAndBusinessResponse == null) {
                saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                if (saleAccountAndBusinessResponse == null) {
                    continue;
                }
                accountMap.put(aliexpressAccountNumber, saleAccountAndBusinessResponse);
            }
            Long key = longListEntry.getKey();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = saleAccountAndBusinessResponse;
            AliexpressExecutors.updateDiscountPool(() -> {
                aliexpressEsExtendService.updateDiscount(saleAccountByAccountNumber, key, bulkOrder, bulkDiscount, userName);
            });
        }
        return ApiResult.newSuccess("后台处理中，结果请查看处理报告！");
    }


    @GetMapping("testxx")
    public ApiResult<?> testxx() {
        AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId("<EMAIL>", 1005002669710713L);
        String aeopQualificationStructJson = aliexpressEsExtend.getAeopQualificationStructJson();
        String aa = "http://172.16.10.51:8888/AliexpressTemplate/*************/3T0382+CE+package.jpg";
        String bb = "https://ae03.alicdn.com/kf/S26c968cfd36340e6b4251b90d0424360e.jpg";
        aeopQualificationStructJson = Pattern.quote(aeopQualificationStructJson);
        aa = Pattern.quote(aa);
        System.out.println(aeopQualificationStructJson);
        System.out.println(aa);
        System.out.println(bb);
        boolean contains = StringUtils.contains(aeopQualificationStructJson, aa);
        System.out.println(contains);
        aeopQualificationStructJson = aeopQualificationStructJson.replaceAll(aa, bb);
        System.out.println(aeopQualificationStructJson);
        return ApiResult.newSuccess();
    }

    /**
     * 提供给产品系统,根据商品id查询标题描述信息
     *
     * @param productId 商品id
     * @return
     */
    @GetMapping(value = "/api/toProduct/listingMsg")
    public ApiResult<AliexpressProductListingMsgDto> apiToProductListingMsgDto(@RequestParam("productId") String productId) {
        return aliexpressEsExtendService.apiToProductListingMsgDto(productId);
    }


    /**
     * 设置批发价
     *
     * @param wholesaleBean
     * @return
     */
    @PostMapping(value = "/aliexpressProductLadderPriceUpdate")
    public ApiResult<?> aliexpressProductLadderPriceUpdate(@RequestBody(required = true) WholesaleBean wholesaleBean) {
        String userName = WebUtils.getUserName();
        String type = wholesaleBean.getType();
        Set<Long> productIdSet = new HashSet<>();
        DiscountParam discountParam = wholesaleBean.getDiscountParam();
        if (StringUtils.equalsIgnoreCase(type, "pop")) {

            CQuery<EsAliexpressProductListingRequest> popCQuery = new CQuery<>();
            EsAliexpressProductListingRequest search = wholesaleBean.getPopQuery();
            popCQuery.setSearch(search);
            search.setPageFields(new String[]{"id", "aliexpressAccountNumber", "productId"});
            String productStatusType = search.getProductStatusType();
            if (StringUtils.isBlank(productStatusType)) {
                search.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());
            } else {
                List<String> productStatusTypeList = new ArrayList<>();
                if (productStatusType.contains(ProductStatusTypeEnum.onSelling.getCode())) {
                    productStatusTypeList.add(ProductStatusTypeEnum.onSelling.getCode());
                }
                if (productStatusType.contains(ProductStatusTypeEnum.auditing.getCode())) {
                    productStatusTypeList.add(ProductStatusTypeEnum.auditing.getCode());
                }
                if (CollectionUtils.isNotEmpty(productStatusTypeList)) {
                    search.setProductStatusType(StringUtils.join(productStatusTypeList, ","));
                } else {
                    return ApiResult.newError("请求失败，产品状态必须是在线或者审核！");
                }
            }
            int offset = 0;
            int limit = 1000;
            while (true) {
                try {
                    popCQuery.setOffset(offset++);
                    popCQuery.setLimit(limit);
                    EsAliexpressProductListingResponse esResponse = aliexpressEsExtendService.list(popCQuery);
                    Page<EsAliexpressProductListing> esProductListingPage = esResponse.getEsProductListingPage();
                    if (esProductListingPage == null || CollectionUtils.isEmpty(esProductListingPage.getContent())) {
                        //跳出循环
                        break;
                    }
                    List<EsAliexpressProductListing> productListingList = esProductListingPage.getContent();

                    Map<Long, List<EsAliexpressProductListing>> longListMap = productListingList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                    for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : longListMap.entrySet()) {
                        Long key = longListEntry.getKey();
                        List<EsAliexpressProductListing> value = longListEntry.getValue();

                        if (productIdSet.contains(key)) {
                            continue;
                        }

                        AliexpressExecutors.UPLOAD_LADDER_PRICE_POOL.execute(() -> {
                            //记录处理报告 并发送队列
                            AliexpressProductLog productLog = new AliexpressProductLog();
                            productLog.setOperateType(OperateLogTypeEnum.UPDATE_LADDER_PRICE_POP.getCode());
                            productLog.setAccountNumber(value.get(0).getAliexpressAccountNumber());
                            productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                            productLog.setProductId(key);
                            productLog.setOperator(userName);
                            aliexpressProductLogService.insert(productLog);

                            WholesaleBean wholesaleBeanMQ = new WholesaleBean();
                            wholesaleBean.setDiscountParam(discountParam);
                            wholesaleBean.setProductId(key);
                            wholesaleBean.setProductLog(productLog);
                            rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_LADDER_PRICE_ROUTE_KEY, JSON.toJSON(wholesaleBeanMQ));
                        });
                        productIdSet.add(key);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } else {
            CQuery<AliexpressHalfTgItemCriteria> halfCQuery = new CQuery<>();
            AliexpressHalfTgItemCriteria halfSearch = wholesaleBean.getHalfQuery();
            halfCQuery.setSearch(halfSearch);
            int offset = 0;
            int limt = 1000;
            while (true) {
                halfCQuery.setOffset(offset++);
                halfCQuery.setLimit(limt);

                CQueryResult<AliexpressHalfTgItem> searchResult = aliexpressHalfTgItemService.search(halfCQuery);
                if (null == searchResult || CollectionUtils.isEmpty(searchResult.getRows())) {
                    break;
                }
                List<AliexpressHalfTgItem> tgItemList = searchResult.getRows();

                Map<Long, List<AliexpressHalfTgItem>> collect = tgItemList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                for (Map.Entry<Long, List<AliexpressHalfTgItem>> longListEntry : collect.entrySet()) {
                    Long key = longListEntry.getKey();
                    List<AliexpressHalfTgItem> value = longListEntry.getValue();

                    if (productIdSet.contains(key)) {
                        continue;
                    }

                    AliexpressExecutors.UPLOAD_LADDER_PRICE_POOL.execute(() -> {
                        //记录处理报告 并发送队列
                        AliexpressProductLog productLog = new AliexpressProductLog();
                        productLog.setOperateType(OperateLogTypeEnum.UPDATE_LADDER_PRICE_HALF.getCode());
                        productLog.setAccountNumber(value.get(0).getAccount());
                        productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                        productLog.setProductId(key);
                        productLog.setOperator(userName);
                        aliexpressProductLogService.insert(productLog);

                        WholesaleBean wholesaleBeanMQ = new WholesaleBean();
                        wholesaleBean.setDiscountParam(discountParam);
                        wholesaleBean.setProductId(key);
                        wholesaleBean.setProductLog(productLog);
                        rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_LADDER_PRICE_ROUTE_KEY, JSON.toJSON(wholesaleBeanMQ));
                    });
                    productIdSet.add(key);
                }
            }
        }
        return ApiResult.newSuccess("请求成功，结果请查看处理报告");
    }


    /**
     * 获取修改主图和标题数据
     * @param ids
     * @return
     */
    @PostMapping("/getUpdateMainImgTitle")
    public ApiResult<List<AliexpressImageTitleResponse>> getUpdateMainImgTitle(@RequestBody List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("参数不能为空");
        }
        List<AliexpressImageTitleResponse> imageAndTitleData = aliexpressEsExtendService.getImageAndTitleData(ids);
        return ApiResult.newSuccess(imageAndTitleData);
    }

    /**
     * 批量修改主图标题
     */
    @PostMapping("/updateMainImgTitle")
    public ApiResult<String> updateMainImgTitle( @RequestBody List<AliexpressUpdateImageTitleRequest> request) {
//        if (StringUtils.isBlank(requestData)) {
//            return ApiResult.newError("请求数据不能为空");
//        }
//        List<AliexpressUpdateImageTitleRequest> requestList = JSON.parseArray(requestData, AliexpressUpdateImageTitleRequest.class);
//
//        if (CollectionUtils.isEmpty(requestList)) {
//            return ApiResult.newError("参数不能为空");
//        }
//
//        if (files != null && files.length > 0) {
//            for (int i = 0; i < requestList.size() && i < files.length; i++) {
//                AliexpressUpdateImageTitleRequest item = requestList.get(i);
//                MultipartFile file = files[i];
//
//                if (file != null && !file.isEmpty() && BooleanUtils.isTrue(item.getNeedUpload())) {
//                    item.setImageFile(file);
//                }
//            }
//        }
        aliexpressEsExtendService.updateMainImgTitle(request);
       return ApiResult.newSuccess("请到处理报告查看处理结果，正在处理。。。");
    }

    /**
     * 批量去除半托管退出标签
     * @param requestCriteria 查询条件
     * @return 结果
     */
    @PostMapping(value = "/batchClearHalfCountryExitLabel")
    public ApiResult<String> batchClearHalfCountryExitLabel(@RequestBody(required = true) EsAliexpressProductListingRequest requestCriteria) {
        try {
            // 参数校验
            if (requestCriteria == null) {
                return ApiResult.newError("参数不能为空");
            }
            // 调用服务层方法
            String result = aliexpressEsExtendService.batchClearHalfCountryExitLabel(requestCriteria);
            if (StringUtils.isNotBlank(result)) {
                return ApiResult.newError(result);
            }
            return ApiResult.newSuccess("成功");
        } catch (Exception e) {
            log.error("批量去除半托管退出标签异常", e);
            return ApiResult.newError("操作失败：" + e.getMessage());
        }
    }

}
