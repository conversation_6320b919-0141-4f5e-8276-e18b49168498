package com.estone.erp.publish.smt.call.direct.singlediscount;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.AbstractSmtCall;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscount;
import com.estone.erp.publish.smt.model.dto.BatchUpdatedConfigParam;
import com.estone.erp.publish.smt.model.dto.SingleDiscountEditProDTO;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProDTO;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 修改店铺限时限量活动商品
 *
 * @Auther lc
 * @Date 2024年7月29日16:46:01
 */
@Slf4j
public class SingleDiscountEditProductCall extends AbstractSmtCall {

    public static ResponseJson editProductCommonDiscount(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount smtMarketingSingleDiscount,
                                                         List<Long> productIdList, Integer discount, Integer buy_max_num, BatchUpdatedConfigParam batchUpdatedConfigParam) {
        SingleDiscountEditProDTO singleDiscountEditProDTO = new SingleDiscountEditProDTO();

        List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjectsDtoList = productIdList.stream().map(t -> {
            SingleDiscountEditProDTO.ProductObjectsDTO productObjectsDTO = new SingleDiscountEditProDTO.ProductObjectsDTO();
            productObjectsDTO.setProduct_id(t.toString());

            SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO productDiscountListDTO = new SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO();
            productDiscountListDTO.setTerminal("ALL");
            productDiscountListDTO.setDiscount(discount);

            ArrayList<SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO> productDiscountListDtoList = Lists.newArrayList(productDiscountListDTO);
            productObjectsDTO.setProduct_discount_list(productDiscountListDtoList);
            productObjectsDTO.setBuy_max_num(buy_max_num);
            return productObjectsDTO;
        }).collect(Collectors.toList());

        singleDiscountEditProDTO.setProduct_objects(productObjectsDtoList);
        singleDiscountEditProDTO.setPromotion_id(smtMarketingSingleDiscount.getSingleDiscountId().toString());
        if (ObjectUtils.isNotEmpty(batchUpdatedConfigParam) && ObjectUtils.isNotEmpty(batchUpdatedConfigParam.getStore_club_discount_rate())){
            singleDiscountEditProDTO.setStore_club_discount_rate(batchUpdatedConfigParam.getStore_club_discount_rate().toString());
        }else{
            singleDiscountEditProDTO.setStore_club_discount_rate("");
        }
        return platUp(saleAccount, singleDiscountEditProDTO);
    }

    public static ResponseJson editProductList(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount smtMarketingSingleDiscount,
                                          List<SingleDiscountProDTO> singleDiscountProDTOS){
        SingleDiscountEditProDTO singleDiscountEditProDTO = new SingleDiscountEditProDTO();
        //实际就一个元素
        List<SingleDiscountEditProDTO.ProductObjectsDTO> productObjectsDtoList = singleDiscountProDTOS.stream().map(t -> {
            SingleDiscountEditProDTO.ProductObjectsDTO productObjectsDTO = new SingleDiscountEditProDTO.ProductObjectsDTO();
            productObjectsDTO.setProduct_id(t.getItemId().toString());

            SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO productDiscountListDTO = new SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO();
            productDiscountListDTO.setTerminal("ALL");
            productDiscountListDTO.setDiscount(t.getDiscount());

            ArrayList<SingleDiscountEditProDTO.ProductObjectsDTO.ProductDiscountListDTO> productDiscountListDtoList = Lists.newArrayList(productDiscountListDTO);
            productObjectsDTO.setProduct_discount_list(productDiscountListDtoList);
            productObjectsDTO.setBuy_max_num(t.getBuy_max_num());
            return productObjectsDTO;
        }).collect(Collectors.toList());

        singleDiscountEditProDTO.setProduct_objects(productObjectsDtoList);
        singleDiscountEditProDTO.setPromotion_id(smtMarketingSingleDiscount.getSingleDiscountId().toString());
        if (ObjectUtils.isNotEmpty(singleDiscountProDTOS.get(0).getStore_club_discount_rate())) {
            singleDiscountEditProDTO.setStore_club_discount_rate(singleDiscountProDTOS.get(0).getStore_club_discount_rate().toString());
        } else {
            singleDiscountEditProDTO.setStore_club_discount_rate("");
        }
        return platUp(saleAccount, singleDiscountEditProDTO);
    }

    public static ResponseJson platUp(SaleAccountAndBusinessResponse saleAccount, SingleDiscountEditProDTO singleDiscountEditProDTO) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        List<String> testAccounts = Optional.ofNullable(testAccountsConfig.getEditProductNewCallTestAccounts()).orElse(Collections.emptyList());

        try {
            // 执行请求
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.marketing.limitdiscountpromotionproduct.edit");
            request.addApiParameter("param_limited_disc_product_input_dto", JSON.toJSONString(singleDiscountEditProDTO));
            IopResponse iopResponse = EnvironmentSupplierWrapper.execute(() -> {
                IopResponse iopResp = new IopResponse();
                try {
                    iopResp = AbstractSmtOpenCall.execute(saleAccount, request);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    iopResp.setCode("500");
                    iopResp.setMessage(e.getMessage());
                    iopResp.setBody(e.getMessage());
                }
                return iopResp;
            }, () -> {
                IopResponse response = new IopResponse();
                response.setCode("500");
                response.setMessage("非正式环境不允许更改折扣商品");
                response.setBody("非正式环境不允许更改折扣商品");
                return response;
            }, testAccounts, saleAccount);
            String body = iopResponse.getBody();
            rsp.setMessage(body);
            if (ObjectUtils.isNotEmpty(iopResponse) && iopResponse.isSuccess() && StringUtils.isNotBlank(iopResponse.getBody()) && (JSON.parseObject(iopResponse.getBody()).getJSONObject("aliexpress_marketing_limitdiscountpromotionproduct_edit_response")) != null) {
                rsp.setMessage(null);
                rsp.setStatus(StatusCode.SUCCESS);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }
}
