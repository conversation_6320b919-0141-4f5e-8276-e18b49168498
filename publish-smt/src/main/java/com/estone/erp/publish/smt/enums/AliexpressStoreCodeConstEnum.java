package com.estone.erp.publish.smt.enums;

public enum AliexpressStoreCodeConstEnum {
    NO_THRESHOLD(0, "无门槛"),
    THRESHOLD(1, "订单金额大于等于US $"),
    CHANNEL_NORMAL(2, "常规展示"),
    CHANNEL_APPOINT(3, "定向渠道发放"),
    ANOTHER_CHANNEL_TRUE(4, "是"),
    ANOTHER_CHANNEL_FALSE(5, "否"),
    TYPE(6, "可传播（通用型）"),
    ACTIVITY_SCOP(7, "全部"),
    DISCOUNT_NUM_PREFIX(8, "US $")
    ;

    private int code;

    private String name;

    AliexpressStoreCodeConstEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressStoreCodeConstEnum build(int code) {
        AliexpressStoreCodeConstEnum[] values = values();
        for (AliexpressStoreCodeConstEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressStoreCodeConstEnum[] values = values();
        for (AliexpressStoreCodeConstEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
