package com.estone.erp.publish.smt.enums;

public enum UpdateStockTypeEnum {
    pop("pop", "pop"),
    halfTg("halfTg", "半托管"),
    halfTgByOrder("halfTgByOrder", "半托管(订单预扣)"),
    popByOrder("popByOrder", "pop(订单预扣)"),
    popByRule("popByRule", "pop(规则)"),
    halfTgByRule("halfTgByRule", "半托管(规则)"),
    ;

    private String code;

    private String name;
    private UpdateStockTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }



}
