package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class SmtCategoryConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * 分类全code
     */
    private String categoryFullPathCode;

    /**
     * 分类全称
     */
    private String categoryCnFullName;

    /**
     * 类型
     */
    private String attributeShowTypeValue;

    /**
     * 属性id
     */
    private Long attrNameId;

    /**
     * 属性英文名
     */
    private String attrName;

    /**
     * 属性中文名
     */
    private String attrNameCnName;

    /**
     * 属性值的中文集合 按照逗号隔开，列表使用
     */
    private String attrValueCnNames;

    /**
     * 属性相关的json
     */
    private String attrJson;

    /**
     * 子属性id
     */
    private Long childrenAttrNameId;

    /**
     * 子属性名称
     */
    private String childrenAttrName;

    /**
     * 子属性中文名称
     */
    private String childrenAttrNameCnName;

    /**
     * 子属性集合json
     */
    private String childrenAttrJson;

    /**
     * true 启用 false 禁用
     */
    private Boolean enable;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updatedTime;

    /**
     * 启动的最新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startedLastTime;

    /**
     * 禁用的最新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp disabledLastTime;
}