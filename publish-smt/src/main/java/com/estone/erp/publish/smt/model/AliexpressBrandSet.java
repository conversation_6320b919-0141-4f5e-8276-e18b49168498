package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressBrandSet implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_brand_set.id
     */
    private Integer id;

    /**
     * 账户名 database column aliexpress_brand_set.account_name
     */
    private String accountName;

    /**
     * 唯一id(爬虫用) database column aliexpress_brand_set.sole_id
     */
    private String soleId;

    /**
     * 状态 database column aliexpress_brand_set.status
     */
    private String status;

    /**
     * 品牌名称 database column aliexpress_brand_set.brand_name
     */
    private String brandName;

    /**
     * 品牌类目 database column aliexpress_brand_set.category_path
     */
    private String categoryPath;

    /**
     * 品牌id database column aliexpress_brand_set.ae_brand_id
     */
    private Long aeBrandId;

    /**
     * 分类id database column aliexpress_brand_set.category_id
     */
    private Integer categoryId;

    /**
     * 品牌有效时间 database column aliexpress_brand_set.brand_deadline_time
     */
    private Timestamp brandDeadlineTime;

    /**
     * 更新时间 database column aliexpress_brand_set.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 抓取时间 database column aliexpress_brand_set.crawl_time
     */
    private Timestamp crawlTime;

    /**
     * 状态更新时间 database column aliexpress_brand_set.state_update_time
     */
    private Timestamp stateUpdateTime;

    /**
     * 是否首选 database column aliexpress_brand_set.is_first
     */
    private Boolean isFirst;

    /**
     * 创建人 database column aliexpress_brand_set.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_brand_set.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_brand_set.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_brand_set.last_update_date
     */
    private Timestamp lastUpdateDate;
}