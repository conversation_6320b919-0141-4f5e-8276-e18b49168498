package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplate;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplateCriteria;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplateExample;
import com.estone.erp.publish.smt.service.AliexpressFreightTemplateService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/417:07
 */
@Slf4j
public class FreightTemplateOpenCall {

    public List<AliexpressFreightTemplate> getFreightTemplateList(SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        return setRedisSynchFreight(saleAccountByAccountNumber);
    }

    public List<AliexpressFreightTemplate> setRedisSynchFreight(SaleAccountAndBusinessResponse saleAccountByAccountNumber){
        List<AliexpressFreightTemplate> freightTemplateList = new ArrayList<>();
        if (saleAccountByAccountNumber != null && StringUtils.isNotBlank(saleAccountByAccountNumber.getAccessToken())) {
            try {
                IopRequest request = new IopRequest();
                request.setApiName("aliexpress.freight.redefining.listfreighttemplate");
                IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
                String body = response.getBody();

                if (StringUtils.isNotEmpty(body)) {
                    JSONObject obj = JSONObject.parseObject(body);
                    if (obj.containsKey("aliexpress_freight_redefining_listfreighttemplate_response")) {
                        JSONObject rsp = obj
                                .getJSONObject("aliexpress_freight_redefining_listfreighttemplate_response");
                        if (rsp.containsKey("aeop_freight_template_d_t_o_list")) {
                            JSONObject templateDTOList = rsp.getJSONObject("aeop_freight_template_d_t_o_list");
                            if (templateDTOList.containsKey("aeopfreighttemplatedtolist")) {
                                JSONArray freightTemplateArray = templateDTOList
                                        .getJSONArray("aeopfreighttemplatedtolist");
                                for (int i = 0; i < freightTemplateArray.size(); i++) {
                                    JSONObject freightTemplateObj = freightTemplateArray.getJSONObject(i);
                                    AliexpressFreightTemplate freightTemplate = new AliexpressFreightTemplate();
                                    freightTemplate.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                                    if (freightTemplateObj.containsKey("template_id")) {
                                        freightTemplate.setTemplateId(freightTemplateObj.getLong("template_id"));
                                    }
                                    if (freightTemplateObj.containsKey("template_name")) {
                                        freightTemplate.setTemplateName(freightTemplateObj.getString("template_name"));
                                    }
                                    if (freightTemplateObj.containsKey("is_default")) {
                                        freightTemplate.setIsDefault(freightTemplateObj.getBoolean("is_default"));
                                    }
                                    freightTemplateList.add(freightTemplate);
                                }
                            }
                        }
                    }
                }
            } catch (ApiException e) {
                log.error("{} ({})", e.getErrorCode(), e.getErrorMessage());
            }
        }
        //同步有结果，先删除本地数据
        if(CollectionUtils.isNotEmpty(freightTemplateList)){
            AliexpressFreightTemplateService aliexpressFreightTemplateService = SpringUtils.getBean(AliexpressFreightTemplateService.class);

            //平台id对应的name
            Map<Long, String> idNameMap = freightTemplateList.stream()
                    .collect(Collectors.toMap(e -> e.getTemplateId(), e -> e.getTemplateName()));

            AliexpressFreightTemplateCriteria criteria = new AliexpressFreightTemplateCriteria();
            String accountNumber = saleAccountByAccountNumber.getAccountNumber();
            criteria.setAccountNumber(accountNumber);
            AliexpressFreightTemplateExample example = criteria.getExample();
            //本地的模板运费
            List<AliexpressFreightTemplate> dbTemplates = aliexpressFreightTemplateService
                    .selectByExample(example);
            if(CollectionUtils.isNotEmpty(dbTemplates)){
                List<Integer> ids = new ArrayList<>();
                for (AliexpressFreightTemplate dbTemplate : dbTemplates) {
                    try {
                        Long dbTempId = dbTemplate.getTemplateId();
                        //同步的名称
                        String synchName = idNameMap.get(dbTempId);
                        String key = RedisConstant.SMT_FREIGHT_TEMPLATE + accountNumber + ":" + dbTempId;
                        if(StringUtils.isNotBlank(synchName)){
                            PublishRedisClusterUtils.set(key, synchName, 6, TimeUnit.HOURS);
                        }else{
                            PublishRedisClusterUtils.del(key);
                        }
                    }
                    catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    ids.add(dbTemplate.getId());
                }
                aliexpressFreightTemplateService.deleteByPrimaryKey(ids);
            }
            //新增同步的数据
            for (AliexpressFreightTemplate aliexpressFreightTemplate : freightTemplateList) {
                aliexpressFreightTemplateService.insert(aliexpressFreightTemplate);
            }
        }
        return freightTemplateList;
    }
}
