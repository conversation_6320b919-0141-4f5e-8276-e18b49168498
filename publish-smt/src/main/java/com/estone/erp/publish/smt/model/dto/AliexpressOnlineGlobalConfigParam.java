package com.estone.erp.publish.smt.model.dto;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.smt.model.AliexpressOnlineGlobalConfig;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.smt.model.dto
 * @Author: sj
 * @CreateTime: 2025-03-05  09:21
 * @Description: TODO
 */

@Data
public class AliexpressOnlineGlobalConfigParam {

    private Integer id;
    /**
     * 设置类型，0-spu按天数限制类型，1-spu允许刊登最大类型
     */
    private Integer setType;

    /**
     * 状态0-禁用，1-启用
     */
    private Integer status;

    /**
     * SPU只允许刊登链接数
     */
    private Integer spuAllowPublishNum;

    private List<AliexpressSpuDaysAllowPublishDto> spuDaysAllowPublish;


    public AliexpressOnlineGlobalConfig toEntity(AliexpressOnlineGlobalConfigParam param) {
        if (param == null) {
            return null;
        }
        AliexpressOnlineGlobalConfig aliexpressOnlineGlobalConfig = new AliexpressOnlineGlobalConfig();
        aliexpressOnlineGlobalConfig.setId(param.getId());
        aliexpressOnlineGlobalConfig.setSetType(param.getSetType());
        aliexpressOnlineGlobalConfig.setStatus(param.getStatus());
        aliexpressOnlineGlobalConfig.setSpuAllowPublishNum(param.getSpuAllowPublishNum());
        if (CollectionUtils.isNotEmpty(param.getSpuDaysAllowPublish())) {
            aliexpressOnlineGlobalConfig.setSpuDaysAllowPublishJson(JSON.toJSONString(param.getSpuDaysAllowPublish()));
        }
        return aliexpressOnlineGlobalConfig;
    }
}
