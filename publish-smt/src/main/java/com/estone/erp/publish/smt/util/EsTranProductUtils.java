package com.estone.erp.publish.smt.util;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.smt.model.AliexpressProductForAreaPrice;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 区域调价字段转换
 * <AUTHOR>
 * @description:
 * @date 2021/11/2812:56
 */
public class EsTranProductUtils {

    public static List<AliexpressProductForAreaPrice> tranAliexpressProductForAreaPrice(List<EsAliexpressProductListing> esAliexpressProductListing){
        List<AliexpressProductForAreaPrice> productList = new ArrayList<>();
        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
            return productList;
        }
        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            AliexpressProductForAreaPrice aliexpressProductForAreaPrice = new AliexpressProductForAreaPrice();
            productList.add(aliexpressProductForAreaPrice);
            aliexpressProductForAreaPrice.setAliexpressAccountNumber(esProduct.getAliexpressAccountNumber());
            aliexpressProductForAreaPrice.setProductId(esProduct.getProductId());
            aliexpressProductForAreaPrice.setDisplayImageUrl(esProduct.getDisplayImageUrl());
            aliexpressProductForAreaPrice.setSkuCode(esProduct.getSkuCode());
            aliexpressProductForAreaPrice.setArticleNumber(esProduct.getArticleNumber());
            aliexpressProductForAreaPrice.setSkuId(esProduct.getSkuId());
            aliexpressProductForAreaPrice.setPlatSkuId(esProduct.getPlatSkuId());
            aliexpressProductForAreaPrice.setCurrencyCode(esProduct.getCurrencyCode());
            aliexpressProductForAreaPrice.setSkuPrice(esProduct.getSkuPrice());
            aliexpressProductForAreaPrice.setCategoryId(esProduct.getCategoryId());
            aliexpressProductForAreaPrice.setFreightTemplateId(esProduct.getFreightTemplateId());
            aliexpressProductForAreaPrice.setPromiseTemplateId(esProduct.getPromiseTemplateId());
            aliexpressProductForAreaPrice.setGroupId(esProduct.getGroupId());
            aliexpressProductForAreaPrice.setGroupIds(esProduct.getGroupIds());
            aliexpressProductForAreaPrice.setCreateTime(new Timestamp(System.currentTimeMillis()));
        }
        return productList;
    }

    public static List<EsAliexpressProductListing> tranEsAliexpressProductListing(List<AliexpressProductForAreaPrice> aliexpressProductForAreaPriceList){
        List<EsAliexpressProductListing> esProductList = new ArrayList<>();
        if(CollectionUtils.isEmpty(aliexpressProductForAreaPriceList)){
            return esProductList;
        }
        for (AliexpressProductForAreaPrice aliexpressProductForAreaPrice : aliexpressProductForAreaPriceList) {
            EsAliexpressProductListing esProduct = new EsAliexpressProductListing();
            esProductList.add(esProduct);
            String id = aliexpressProductForAreaPrice.getAliexpressAccountNumber() + "-" + aliexpressProductForAreaPrice.getProductId() + "-" + aliexpressProductForAreaPrice.getSkuId();
            esProduct.setId(id);
            esProduct.setAliexpressAccountNumber(aliexpressProductForAreaPrice.getAliexpressAccountNumber());
            esProduct.setProductId(aliexpressProductForAreaPrice.getProductId());
            esProduct.setDisplayImageUrl(aliexpressProductForAreaPrice.getDisplayImageUrl());
            esProduct.setSkuCode(aliexpressProductForAreaPrice.getSkuCode());
            esProduct.setArticleNumber(aliexpressProductForAreaPrice.getArticleNumber());
            esProduct.setSkuId(aliexpressProductForAreaPrice.getSkuId());
            esProduct.setPlatSkuId(aliexpressProductForAreaPrice.getPlatSkuId());
            esProduct.setCurrencyCode(aliexpressProductForAreaPrice.getCurrencyCode());
            esProduct.setSkuPrice(aliexpressProductForAreaPrice.getSkuPrice());
            esProduct.setCategoryId(aliexpressProductForAreaPrice.getCategoryId());
            esProduct.setFreightTemplateId(aliexpressProductForAreaPrice.getFreightTemplateId());
            esProduct.setPromiseTemplateId(aliexpressProductForAreaPrice.getPromiseTemplateId());
            esProduct.setGroupId(aliexpressProductForAreaPrice.getGroupId());
            esProduct.setGroupIds(aliexpressProductForAreaPrice.getGroupIds());
            esProduct.setCreateTime(new Timestamp(System.currentTimeMillis()));
        }
        return esProductList;
    }
}
