package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

/**
 */
public enum ProductAttrMarkEnum {
    S_1("1", "默认基础属性"),
    S_2("2", "属性不全"),
    S_3("3", "已引用admin范本");

    private String code;

    private String name;

    ProductAttrMarkEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ProductAttrMarkEnum build(String code) {
        ProductAttrMarkEnum[] values = values();
        for (ProductAttrMarkEnum type : values) {
            if (StringUtils.equalsIgnoreCase(type.code, code)) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ProductAttrMarkEnum[] values = values();
        for (ProductAttrMarkEnum type : values) {
            if (StringUtils.equalsIgnoreCase(type.code, code)) {
                return type.getName();
            }
        }
        return null;
    }
}
