package com.estone.erp.publish.smt.model;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class AliexpressEsExtend extends EsAliexpressProductListing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * database column aliexpress_es_extend.id
     */
    private Long extendId;

    /**
     * 账号 database column aliexpress_es_extend.aliexpress_account_number
     */
    private String aliexpressAccountNumber;

    /**
     * 账号id database column aliexpress_es_extend.owner_member_id
     */
    private String ownerMemberId;

    /**
     * 产品id database column aliexpress_es_extend.product_id
     */
    private Long productId;

    /**
     *  database column aliexpress_es_extend.aeop_ae_product_skus_json
     */
    private String aeopAeProductSkusJson;

    /**
     * 商品分国家报价的配置 database column aliexpress_es_extend.aeop_national_quote_configuration
     */
    private String aeopNationalQuoteConfiguration;

    /**
     * 商品多媒体信息，该属性主要包含商品的视频列表 database column aliexpress_es_extend.aeop_ae_multimedia
     */
    private String aeopAeMultimedia;

    /**
     *  database column aliexpress_es_extend.aeop_ae_product_propertys_json
     */
    private String aeopAeProductPropertysJson;

    /**
     * mobile Detail详情 database column aliexpress_es_extend.mobile_detail
     */
    private String mobileDetail;
    //自动下架时间
    private Timestamp autoOffDate;
    //自动上架时间
    private Timestamp autoOnDate;
    //任务周期开始
    private Timestamp startTaskDate;
    //任务周期结束
    private Timestamp endTaskDate;
    //记录下架时间
    private Timestamp recordOffDate;
    //记录上架时间
    private Timestamp recordOnDate;

    /**
     * 方图
     */
    private String squareImg;

    /**
     * 长图
     */
    private String longImg;

    /**
     * 多语言标题
     */
    private String interSubjects;

    /**
     * 资质信息
     */
    private String aeopQualificationStructJson;

    /**
     *     S_1("1", "默认基础属性"),
     *     S_2("2", "属性不全"),
     *     S_3("3", "已引用admin范本");
     * extended_field1  已使用，标识属性
     */
    private String extendedField1;

    /**
     *
     * extended_field2 已使用，标识描述问题
     */
    private String extendedField2;

    /**
     * extended_field3
     */
    private String extendedField3;

    /**
     * extended_field4
     */
    private String extendedField4;

    /**
     * extended_field5
     */
    private String extendedField5;

    //-------------分割(上面是数据库字段)--------------//

    //时间格式问题 导致转换时间有差异
    //自动下架时间
    private String autoOffDateStr;
    //自动上架时间
    private String autoOnDateStr;
    //任务周期开始
    private String startTaskDateStr;
    //任务周期结束
    private String endTaskDateStr;

    //毛利
    private String grossProfit;

    private String grossProfitMargin;

    //分类
    private AliexpressCategory aliexpressCategory;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    private String salesSupervisorName;

    // 中文申报名
    private String declarecnname;

    //sku状态
    private String skuStatus;

    //sku总重量
    private Double skuTotalWeight;

    //sku采购价 改为销售成本价
    private Double skuPurchasePrice;

    //包材重量
    private Double pmWeight;

    //包材价格 + 填充物价格
    private Double pmPrice;

    private String lifeCyclePhase;

    private String forbiddenSaleChannel;

    private String tags;

    private String freightTemplateName;

    private String groupNames;

    private String operator;

    //修改图片
    private List<String> images;

    //营销图片池
    private List<String> marketImages;

    private Map<String, Double> countryPriceMap = new HashMap<>();

    private String priceType;

    //区域国家
    private List<String> shiptoCountryList;

    private List<BatchPriceCalculatorResponse> calculatorResponseList = new ArrayList<>();

    //视频链接
    private String videoLink;

    private Boolean isOffandOn;

    //系统上传视频
    private Boolean isSystemUploadVideo;

    //采购运费
    private Double shippingCost;

    //标准重量
    private Double standardWeight;

    /**
     * 单品折扣名称
     */
    private String singleDisCountName;

    /**
     * 活动状态
     */
    private String statusName;


    /**
     * 全站折扣
     */
    private Integer wholeStationDiscount;


    /**
     * 粉丝折扣
     */
    private Integer storeClubDiscountRate;
}