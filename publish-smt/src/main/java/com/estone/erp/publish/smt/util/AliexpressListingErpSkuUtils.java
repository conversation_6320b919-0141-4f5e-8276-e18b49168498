package com.estone.erp.publish.smt.util;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据分析
 */
public class AliexpressListingErpSkuUtils {

    private static EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);

    /**
     * 获取全部
     */
    public static List<EsSkuBind> getAllErpDataSkus() {
        EsSkuBindRequest request = new EsSkuBindRequest();
        request.setPlatform(SaleChannel.CHANNEL_SMT);
        request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
        return esSkuBindService.getEsSkuBinds(request);
    }

    /**
     * 获取昨天的增量
     */
    public static List<EsSkuBind> getIncrementErpDataSkus() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        EsSkuBindRequest request = new EsSkuBindRequest();
        request.setPlatform(SaleChannel.CHANNEL_SMT);
        request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
        Date dateBegin = DateUtils.getDateBegin(-1);
        String format1 = sdf.format(dateBegin);
        request.setFromCreateDate(format1);
        String format2 = DateUtils.formatMaxDateTime(DateUtils.format(dateBegin, "yyyy-MM-dd"));
        request.setToCreateDate(format2);
        return esSkuBindService.getEsSkuBinds(request);
    }

    /**
     * 获取当天的自增
     */
    public static List<EsSkuBind> getIncrementErpDataSkusAtToday() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        EsSkuBindRequest request = new EsSkuBindRequest();
        request.setPlatform(SaleChannel.CHANNEL_SMT);
        request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
        Date endDate = new Date();
        String format1 = sdf.format(endDate);
        request.setToCreateDate(format1);
        String format2 = DateUtils.formatMinDateTime(DateUtils.format(endDate, "yyyy-MM-dd"));
        request.setFromCreateDate(format2);
        return esSkuBindService.getEsSkuBinds(request);
    }

    public static List<EsSkuBind> getSkuBind(String bindSku) {
        if(StringUtils.isBlank(bindSku)){
            return new ArrayList<>();
        }
        EsSkuBindRequest request = new EsSkuBindRequest();
        request.setPlatform(SaleChannel.CHANNEL_SMT);
        request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
        request.setBindSku(bindSku);
        return esSkuBindService.getEsSkuBinds(request);
    }

}
