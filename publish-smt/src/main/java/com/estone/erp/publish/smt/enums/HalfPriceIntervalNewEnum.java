package com.estone.erp.publish.smt.enums;

public enum HalfPriceIntervalNewEnum {
    intervale1(0.0, 2.0, 0.0, 50.0, 0.6),
    intervale2(0.0, 2.0, 50.0, 100.0, 0.6),
    intervale3(0.0, 2.0, 100.0, 150.0, 0.6),
    intervale4(0.0, 2.0, 150.0, 300.0, 0.56),
    intervale5(0.0, 2.0, 300.0, 1950.0, 0.48),
    intervale6(2.0, 5.0, 0.0, 50.0, 0.4),
    intervale7(2.0, 5.0, 50.0, 100.0, 0.44),
    intervale8(2.0, 5.0, 100.0, 150.0, 0.46),
    intervale9(2.0, 5.0, 150.0, 300.0, 0.46),
    intervale10(2.0, 5.0, 300.0, 1950.0, 0.46),
    intervale11(5.0, 10.0, 0.0, 50.0, 0.32),
    intervale12(5.0, 10.0, 50.0, 100.0, 0.35),
    intervale13(5.0, 10.0, 100.0, 150.0, 0.37),
    intervale14(5.0, 10.0, 150.0, 300.0, 0.4),
    intervale15(5.0, 10.0, 300.0, 1950.0, 0.42),
    intervale16(10.0, 15.0, 0.0, 50.0, 0.36),
    intervale17(10.0, 15.0, 50.0, 100.0, 0.3),
    intervale18(10.0, 15.0, 100.0, 150.0, 0.33),
    intervale19(10.0, 15.0, 150.0, 300.0, 0.36),
    intervale20(10.0, 15.0, 300.0, 1950.0, 0.4),
    intervale21(15.0, 20.0, 0.0, 50.0, 0.3),
    intervale22(15.0, 20.0, 50.0, 100.0, 0.3),
    intervale23(15.0, 20.0, 100.0, 150.0, 0.31),
    intervale24(15.0, 20.00, 150.0, 300.0, 0.34),
    intervale25(15.0, 20.0, 300.0, 1950.0, 0.38),
    intervale26(20.0, 25.0, 0.0, 50.0, 0.3),
    intervale27(20.0, 25.0, 50.0, 100.0, 0.3),
    intervale28(20.0, 25.0, 100.0, 150.0, 0.3),
    intervale29(20.0, 25.0, 150.0, 300.0, 0.32),
    intervale30(20.0, 25.0, 300.0, 1950.0, 0.38),
    intervale31(25.0, 30.0, 0.0, 50.0, 0.3),
    intervale32(25.0, 30.0, 50.0, 100.0, 0.3),
    intervale33(25.0, 30.0, 100.0, 150.0, 0.3),
    intervale34(25.0, 30.0, 150.0, 300.0, 0.3),
    intervale35(25.0, 30.0, 300.0, 1950.0, 0.38),
    intervale36(30.0, 50.0, 0.0, 50.0, 0.3),
    intervale37(30.0, 50.0, 50.0, 100.0, 0.3),
    intervale38(30.0, 50.0, 100.0, 150.0, 0.3),
    intervale39(30.0, 50.0, 150.0, 300.0, 0.31),
    intervale40(30.0, 50.0, 300.0, 1950.0, 0.4),
    intervale41(50.0, 100.0, 0.0, 150.0, 0.3),
    intervale42(50.0, 100.0, 150.0, 300.0, 0.3),
    intervale43(50.0, 100.0, 300.0, 1950.0, 0.34),
    intervale44(100.0, 1000.0, 0.0, 1950.0, 0.3),


    ;

    private Double fromPrice;
    private Double toPrice;
    private Double fromWeight;
    private Double toWeight;
    private Double ratio;

    private HalfPriceIntervalNewEnum(Double fromPrice, Double toPrice, Double fromWeight, Double toWeight, Double ratio) {
        this.fromPrice = fromPrice;
        this.toPrice = toPrice;
        this.fromWeight = fromWeight;
        this.toWeight = toWeight;
        this.ratio = ratio;
    }

    public Double getFromPrice() {
        return fromPrice;
    }

    public void setFromPrice(Double fromPrice) {
        this.fromPrice = fromPrice;
    }

    public Double getToPrice() {
        return toPrice;
    }

    public void setToPrice(Double toPrice) {
        this.toPrice = toPrice;
    }

    public Double getFromWeight() {
        return fromWeight;
    }

    public void setFromWeight(Double fromWeight) {
        this.fromWeight = fromWeight;
    }

    public Double getToWeight() {
        return toWeight;
    }

    public void setToWeight(Double toWeight) {
        this.toWeight = toWeight;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }
}
