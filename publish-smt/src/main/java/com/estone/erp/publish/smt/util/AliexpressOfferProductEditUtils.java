package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.call.direct.UploadImageOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferEditProductOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferQueryProductOpenCall;
import com.estone.erp.publish.smt.model.AliexpressProduct;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2020/8/27
 */
@Slf4j
public class AliexpressOfferProductEditUtils {

    /**
     * 同步产品信息更新营销图
     * @param aliexpressProduct
     * @param saleAccount
     * @param marketImages
     * @return
     */
    public static ResponseJson syncProductUpdateMarkerimages(AliexpressProduct aliexpressProduct, SaleAccountAndBusinessResponse saleAccount, List<MarketImage> marketImages) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if(CollectionUtils.isEmpty(marketImages)) {
            responseJson.setMessage("没有营销图！");
            return responseJson;
        }

        //上传图片到平台 没有图片也需要上传
        List<MarketImage> newMarketImages = new ArrayList<>();
        for (MarketImage marketImage : marketImages) {
            String url= marketImage.getUrl();
            if(StringUtils.isBlank(url)) {
                continue;
            }
            try{
                if(StringUtils.isNotBlank(url) && !AliexpressContentUtils.isSmtImg(url)){
                    UploadImageOpenCall call = new UploadImageOpenCall();
                    String postedImgUrl = call.uploadImageToAliexpress(saleAccount, url, null, false,null);
                    marketImage.setUrl(postedImgUrl);
                }
                newMarketImages.add(marketImage);
            }catch (Exception e) {
                log.error("上传营销图失败异常", e);
                responseJson.setMessage("上传营销图失败" + e.getMessage());
                return responseJson;
            }
        }

        JSONObject productEntity = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccount, aliexpressProduct.getProductId());
        if (null == productEntity) {
            responseJson.setMessage("获取产品信息失败！");
            return responseJson;
        }

        // 营销图
        JSONArray marketImageJsonArray = JSONArray.parseArray(JSON.toJSONString(newMarketImages));
        productEntity.put("market_images", marketImageJsonArray);

        responseJson = OfferEditProductOpenCall.offerEditProduct(saleAccount, productEntity.toJSONString());
        //存储营销图
        for (MarketImage newMarketImage : newMarketImages) {
            responseJson.getBody().put(newMarketImage.getImage_type(), StringUtils.isBlank(newMarketImage.getUrl()) ? "" : newMarketImage.getUrl());
        }
        return responseJson;
    }
}
