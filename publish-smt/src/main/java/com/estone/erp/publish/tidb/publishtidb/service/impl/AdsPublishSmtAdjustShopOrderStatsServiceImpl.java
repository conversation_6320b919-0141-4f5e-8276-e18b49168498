package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsPublishSmtAdjustShopOrderStatsMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustShopOrderStats;
import com.estone.erp.publish.tidb.publishtidb.service.AdsPublishSmtAdjustShopOrderStatsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdsPublishSmtAdjustShopOrderStatsServiceImpl extends ServiceImpl<AdsPublishSmtAdjustShopOrderStatsMapper, AdsPublishSmtAdjustShopOrderStats> implements AdsPublishSmtAdjustShopOrderStatsService {
    @Override
    public List<String> passAccountList(List<String> ruleAccountList, Integer accountOrderTime, Integer accountOrderTimeFrom, Integer accountOrderTimeTo){
        List<String> passAccountList = new ArrayList<>();
        if(CollectionUtils.isEmpty(ruleAccountList) || accountOrderTime == null || !Arrays.asList(14,30, 60, 90, 180).contains(accountOrderTime) || accountOrderTimeFrom == null || accountOrderTimeTo == null ){
            return null;
        }
        LambdaQueryWrapper<AdsPublishSmtAdjustShopOrderStats> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AdsPublishSmtAdjustShopOrderStats::getAccountNumber, ruleAccountList);
        if(accountOrderTime == 14){
            wrapper.ge(AdsPublishSmtAdjustShopOrderStats::getOrder_num_14d, accountOrderTimeFrom);
            wrapper.lt(AdsPublishSmtAdjustShopOrderStats::getOrder_num_14d, accountOrderTimeTo);
        }else if(accountOrderTime == 30){
            wrapper.ge(AdsPublishSmtAdjustShopOrderStats::getOrder_num_30d, accountOrderTimeFrom);
            wrapper.lt(AdsPublishSmtAdjustShopOrderStats::getOrder_num_30d, accountOrderTimeTo);
        }else if(accountOrderTime == 60){
            wrapper.ge(AdsPublishSmtAdjustShopOrderStats::getOrder_num_60d, accountOrderTimeFrom);
            wrapper.lt(AdsPublishSmtAdjustShopOrderStats::getOrder_num_60d, accountOrderTimeTo);
        }else if(accountOrderTime == 90){
            wrapper.ge(AdsPublishSmtAdjustShopOrderStats::getOrder_num_90d, accountOrderTimeFrom);
            wrapper.lt(AdsPublishSmtAdjustShopOrderStats::getOrder_num_90d, accountOrderTimeTo);
        }else if(accountOrderTime == 180){
            wrapper.ge(AdsPublishSmtAdjustShopOrderStats::getOrder_num_180d, accountOrderTimeFrom);
            wrapper.lt(AdsPublishSmtAdjustShopOrderStats::getOrder_num_180d, accountOrderTimeTo);
        }
        List<AdsPublishSmtAdjustShopOrderStats> list = this.list(wrapper);
        if(CollectionUtils.isNotEmpty(list)){
            passAccountList = list.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
        }
        return passAccountList;
    }
}
