package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressAccountBrand implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID database column aliexpress_account_brand.id
     */
    private Integer id;

    /**
     * 店铺 database column aliexpress_account_brand.account_number
     */
    private String accountNumber;

    /**
     * 平台类目id database column aliexpress_account_brand.category_id
     */
    private Integer categoryId;

    /**
     * 品牌属性 database column aliexpress_account_brand.brand_attr
     */
    private String brandAttr;

    /**
     * 创建时间 database column aliexpress_account_brand.created_at
     */
    private Timestamp createdAt;

    /**
     * 修改时间 database column aliexpress_account_brand.updated_at
     */
    private Timestamp updatedAt;
}