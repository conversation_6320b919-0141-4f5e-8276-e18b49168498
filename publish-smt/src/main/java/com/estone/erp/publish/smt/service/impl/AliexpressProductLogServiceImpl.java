package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.model.TemplateQueue;
import com.estone.erp.publish.smt.bean.AliexpressProductOperateLogType;
import com.estone.erp.publish.smt.bean.ProductUploadUpdateSubjectBo;
import com.estone.erp.publish.smt.bean.UpdatePriceEntity;
import com.estone.erp.publish.smt.enums.*;
import com.estone.erp.publish.smt.mapper.AliexpressProductLogMapper;
import com.estone.erp.publish.smt.mapper.custom.CustomAliexpressProductLogMapper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.AliexpressProductLogCountDto;
import com.estone.erp.publish.smt.mq.bean.HolidayStockBean;
import com.estone.erp.publish.smt.mq.bean.HolidayStockMqBean;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.smt.service.AliexpressTemplateService;
import com.estone.erp.publish.smt.service.SmtHolidaysStockUpdateLogService;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressReportProblemMaintain;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressReportProblemMaintainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_product_log
 * 2019-10-24 11:44:08
 */
@Service("aliexpressProductLogService")
@Slf4j
public class AliexpressProductLogServiceImpl implements AliexpressProductLogService {
    @Resource
    private AliexpressProductLogMapper aliexpressProductLogMapper;

    @Resource
    private CustomAliexpressProductLogMapper customAliexpressProductLogMapper;

    @Resource
    private AliexpressTemplateService aliexpressTemplateService;

    @Resource
    private SmtHolidaysStockUpdateLogService smtHolidaysStockUpdateLogService;

    @Resource
    private AliexpressReportProblemMaintainService aliexpressReportProblemMaintainService;

    @Override
    public int countByExample(AliexpressProductLogExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressProductLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressProductLog> search(CQuery<AliexpressProductLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressProductLogCriteria query = cquery.getSearch();
        AliexpressProductLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressProductLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressProductLog> aliexpressProductLogs = aliexpressProductLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressProductLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressProductLogs);
        return result;
    }

    @Override
    public AliexpressProductLog selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressProductLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressProductLog> selectByExample(AliexpressProductLogExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressProductLogMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressProductLog record) {
        Assert.notNull(record, "record is null!");

        if (record.getOperateStatus() == null) {
            record.setOperateStatus(OperateLogStatusEnum.end.intCode());
            record.setOperateTime(new Timestamp(System.currentTimeMillis()));
        }

        AliexpressLogUtils.setTaskErrorType(record);

        // 默认加时间和人
        record.setCreateTime(new Timestamp(System.currentTimeMillis()));
        record.setOperator(StringUtils.isEmpty(record.getOperator()) ? WebUtils.getUserName() : record.getOperator());

        if (BooleanUtils.isFalse(record.getResult()) && StringUtils.isNotEmpty(record.getFailInfo()) && StringUtils.isNotEmpty(record.getOperateType())){
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(record.getFailInfo(), record.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                record.setProblemType(problemMaintain.getProblemType());
                record.setSolutionType(problemMaintain.getSolutionType());
            }

        }

        return aliexpressProductLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressProductLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        AliexpressLogUtils.setTaskErrorType(record);
        AliexpressProductLog aliexpressProductLog = aliexpressProductLogMapper.selectByPrimaryKey(record.getId());
        if (BooleanUtils.isFalse(record.getResult()) && StringUtils.isNotEmpty(record.getFailInfo()) && StringUtils.isNotEmpty(aliexpressProductLog.getOperateType())){
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(record.getFailInfo(), aliexpressProductLog.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                record.setProblemType(problemMaintain.getProblemType());
                record.setSolutionType(problemMaintain.getSolutionType());
            }

        }
        return aliexpressProductLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressProductLog record, AliexpressProductLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        AliexpressLogUtils.setTaskErrorType(record);
        if (BooleanUtils.isFalse(record.getResult()) && StringUtils.isNotEmpty(record.getFailInfo()) && StringUtils.isNotEmpty(record.getOperateType())){
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(record.getFailInfo(), record.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                record.setProblemType(problemMaintain.getProblemType());
                record.setSolutionType(problemMaintain.getSolutionType());
            }

        }
        return aliexpressProductLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressProductLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByDateAndType(String date, String type) {
        Assert.notNull(date, "date is null!");
        return aliexpressProductLogMapper.deleteByDateAndType(date, type);
    }

    @Override
    public int tranTemp(AliexpressProduct product, String mainSkum, ResponseJson rsp) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(product.getAliexpressAccountNumber());
        log.setSkuCode(mainSkum);
        log.setOperateType(AliexpressProductOperateLogType.tran_temp);
        log.setRelationType(LogRelationTypeEnum.product.intCode());
        log.setRelationId(product.getProductId().toString());
        log.setOperator(StringUtils.isNotBlank(product.getOperator()) ? product.getOperator() : WebUtils.getUserName());
        log.setResult(rsp.isSuccess());
        log.setProductId(product.getProductId());
        log.setFailInfo(rsp.getMessage());

        return insert(log);
    }

    @Override
    public int tranTemp(Long productId, String operator, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setOperateType(AliexpressProductOperateLogType.tran_temp);
        log.setRelationType(LogRelationTypeEnum.product.intCode());
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setResult(false);
        log.setProductId(productId);
        log.setFailInfo(errorMsg);
        return insert(log);
    }


    @Override
    public int batchInsert(List<AliexpressProductLog> logList) {

        for (AliexpressProductLog record : logList) {
            if (record == null) {
                continue;
            }
            if (record.getOperateStatus() == null) {
                record.setOperateStatus(OperateLogStatusEnum.end.intCode());
                record.setOperateTime(new Timestamp(System.currentTimeMillis()));
            }

            AliexpressLogUtils.setTaskErrorType(record);

            // 默认加时间和人
            record.setCreateTime(new Timestamp(System.currentTimeMillis()));
            record.setOperator(StringUtils.isEmpty(record.getOperator()) ? WebUtils.getUserName() : record.getOperator());

            if (BooleanUtils.isFalse(record.getResult()) && StringUtils.isNotEmpty(record.getFailInfo()) && StringUtils.isNotEmpty(record.getOperateType())){
                //失败获取问题分类和解决方案
                AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(record.getFailInfo(), record.getOperateType());
                if (ObjectUtils.isNotEmpty(problemMaintain)) {
                    record.setProblemType(problemMaintain.getProblemType());
                    record.setSolutionType(problemMaintain.getSolutionType());
                }

            }
        }

        return customAliexpressProductLogMapper.batchInsert(logList);
    }

    @Override
    public int batchUpdate(List<AliexpressProductLog> logList) {
        return aliexpressProductLogMapper.batchUpdate(logList);
    }

    @Override
    public int updateProductImgCreate(AliexpressProduct product, boolean result) {

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(product.getAliexpressAccountNumber());
        log.setSkuCode(product.getArticleNumber());
        log.setProductId(product.getProductId());
        log.setOperateType(AliexpressProductOperateLogType.EDIT_IMG);
        log.setOperator(StringUtils.isNotBlank(product.getOperator()) ? product.getOperator() : WebUtils.getUserName());
        log.setResult(result);
        log.setFailInfo(product.getErrorTip());

        return insert(log);
    }

    @Override
    public int updateProductImgAndTitleCreate(AliexpressProduct product, boolean result) {

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(product.getAliexpressAccountNumber());
        log.setSkuCode(product.getArticleNumber());
        log.setProductId(product.getProductId());
        log.setOperateType(AliexpressProductOperateLogType.EDIT_IMG_TITLE);
        log.setOperator(StringUtils.isNotBlank(product.getOperator()) ? product.getOperator() : WebUtils.getUserName());
        log.setResult(result);
        log.setFailInfo(product.getErrorTip());

        return insert(log);
    }

    @Override
    public void updateStockCreate(List<AliexpressProduct> productList, String currentUser, Map<String, Double> productSkuIdToStockMap, ResponseJson rsp, String stock, boolean isCheck, HolidayStockMqBean holidayStockMqBean) {
        if (CollectionUtils.isNotEmpty(productList)) {
            List<AliexpressProductLog> batchCreateLogList = new ArrayList<>();

            boolean success = rsp.isSuccess();
            String message = rsp.getMessage();

            for (AliexpressProduct aliexpressProduct : productList) {
                AliexpressProductLog log = new AliexpressProductLog();
                log.setProductId(aliexpressProduct.getProductId());
                log.setAccountNumber(aliexpressProduct.getAliexpressAccountNumber());
                log.setSkuCode(aliexpressProduct.getArticleNumber());
                log.setOperator(currentUser);
                log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                log.setOperateType(AliexpressProductOperateLogType.EDIT_STOCK);
                log.setStockBeforeEdit(aliexpressProduct.getIpmSkuStock().doubleValue());
                Double stockAfterEdit = productSkuIdToStockMap.get(aliexpressProduct.getSkuId());

                if (isCheck && StringUtils.equalsIgnoreCase(stock, "0") && stockAfterEdit != null && stockAfterEdit.doubleValue() != 0d) {
                    log.setFailInfo("货号清仓甩卖并且smt不禁售库存改为可用库存：" + stockAfterEdit + (StringUtils.isBlank(message) ? "" : message));
                } else {
                    log.setFailInfo(message);
                }
                log.setStockAfterEdit(stockAfterEdit);
                log.setResult(success);
                batchCreateLogList.add(log);

                //需要记录休假日志
                if (holidayStockMqBean != null) {
                    String updateType = holidayStockMqBean.getUpdateType();
                    //恢复库存 改变状态
                    if (StringUtils.equalsIgnoreCase(updateType, "3")) {
                        if (success) {
                            List<SmtHolidaysStockUpdateLog> value = holidayStockMqBean.getValue();
                            for (SmtHolidaysStockUpdateLog smtHolidaysStockUpdateLog : value) {
                                smtHolidaysStockUpdateLog.setResultType(3);
                                smtHolidaysStockUpdateLogService.updateByPrimaryKeySelective(smtHolidaysStockUpdateLog);
                            }
                        }
                    } else {
                        SmtHolidaysStockUpdateLog smtHolidaysStockUpdateLog = new SmtHolidaysStockUpdateLog();
                        smtHolidaysStockUpdateLog.setAccount(log.getAccountNumber());
                        smtHolidaysStockUpdateLog.setProductId(log.getProductId());
                        smtHolidaysStockUpdateLog.setArticleNumber(log.getSkuCode());
                        smtHolidaysStockUpdateLog.setSkuId(aliexpressProduct.getSkuId());
                        smtHolidaysStockUpdateLog.setStockBefore(aliexpressProduct.getIpmSkuStock());
                        smtHolidaysStockUpdateLog.setStockAfter(0);
                        smtHolidaysStockUpdateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                        smtHolidaysStockUpdateLog.setUpdateType(updateType);
                        smtHolidaysStockUpdateLog.setResultType(success ? 1 : 0);
                        smtHolidaysStockUpdateLog.setFailInfo(message);
                        if (StringUtils.equalsIgnoreCase(updateType, "1")) {
                            smtHolidaysStockUpdateLog.setSkuStatus(SingleItemEnum.HOLIDAY.getName());
                            smtHolidaysStockUpdateLog.setUpdateBy("HolidayAliexpressUpdateStockZeroJobHandler");
                        } else {
                            List<HolidayStockBean> holidayStockBeanList = holidayStockMqBean.getHolidayStockBeanList();
                            Map<String, HolidayStockBean> stockBeanMap = holidayStockBeanList.stream().collect(Collectors.toMap(k -> k.getSonSku(), v -> v, (k1, k2) -> k1));
                            smtHolidaysStockUpdateLog.setUpdateBy("HolidayTwoAliexpressUpdateStockZeroJobHandler");
                            HolidayStockBean holidayStockBean = stockBeanMap.get(log.getSkuCode());
                            if (holidayStockBean != null) {
                                smtHolidaysStockUpdateLog.setSkuStatus(holidayStockBean.getItemStatus());
                                Integer availableStock = holidayStockBean.getAvailableStock() == null ? 0 : holidayStockBean.getAvailableStock();
                                Integer pendingCount = holidayStockBean.getPendingCount() == null ? 0 : holidayStockBean.getPendingCount();
                                smtHolidaysStockUpdateLog.setUsableStock(availableStock);
                                smtHolidaysStockUpdateLog.setPendingStock(pendingCount);
                                smtHolidaysStockUpdateLog.setRemainingStock(availableStock - pendingCount);
                            }
                        }
                        log.setOperator(smtHolidaysStockUpdateLog.getUpdateBy());
                        smtHolidaysStockUpdateLogService.insert(smtHolidaysStockUpdateLog);
                    }
                }
            }
            batchInsert(batchCreateLogList);
        }
    }

    @Override
    public int insert(AliexpressProduct product, String type, ResponseJson responseJson) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(product.getAliexpressAccountNumber());
        log.setSkuCode(product.getArticleNumber());
        log.setProductId(product.getProductId());
        log.setOperateType(type);
        log.setOperator(StringUtils.isNotBlank(product.getOperator()) ? product.getOperator() : WebUtils.getUserName());
        log.setResult(StatusCode.SUCCESS.equals(responseJson.getStatus()));
        log.setFailInfo(responseJson.getMessage());

        //失败获取问题分类和解决方案
        if (BooleanUtils.isFalse(log.getResult()) && StringUtils.isNotBlank(responseJson.getMessage()) && StringUtils.isNotBlank(type)) {
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(responseJson.getMessage(), type);
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        }


        return insert(log);
    }

    @Override
    public int insert(ProductUploadUpdateSubjectBo uploadUpdateSubjectBo, String type, String operator, ResponseJson responseJson) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(uploadUpdateSubjectBo.getAliexpressAccountNumber());
        log.setProductId(uploadUpdateSubjectBo.getProductId());
        log.setOperateType(type);
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setResult(StatusCode.SUCCESS.equals(responseJson.getStatus()));
        log.setFailInfo(responseJson.getMessage());
        //失败获取问题分类和解决方案

        if (BooleanUtils.isFalse(log.getResult()) && StringUtils.isNotBlank(responseJson.getMessage()) && StringUtils.isNotBlank(type)) {
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(responseJson.getMessage(), type);
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        }

        return insert(log);
    }

    @Override
    public int insert(EsAliexpressProductListing esAlie, String type, String operator, ResponseJson responseJson) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(esAlie.getAliexpressAccountNumber());
        log.setSkuCode(esAlie.getSpu());
        log.setProductId(esAlie.getProductId());
        log.setOperateType(type);
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setResult(StatusCode.SUCCESS.equals(responseJson.getStatus()));
        log.setFailInfo(responseJson.getMessage());

        //失败获取问题分类和解决方案
        if (BooleanUtils.isFalse(log.getResult()) && StringUtils.isNotBlank(responseJson.getMessage()) && StringUtils.isNotBlank(type)) {
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(responseJson.getMessage(), type);
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        }
        return insert(log);
    }

    @Override
    public int product28Update(UpdatePriceEntity updatePriceEntity, String userName) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(updatePriceEntity.getSeller());
        log.setSkuCode(updatePriceEntity.getSkuCode());
        log.setProductId(Long.valueOf(updatePriceEntity.getProductId()));
        log.setOperateType(AliexpressProductOperateLogType.PRICE28_UPDATE);
        log.setOperator(StringUtils.isNotBlank(userName) ? userName : WebUtils.getUserName());

        if ("修改成功".equals(updatePriceEntity.getErrorTip())) {
            log.setResult(true);
        } else {
            log.setResult(false);
        }
        log.setFailInfo(updatePriceEntity.getErrorTip());

        return insert(log);
    }

    @Override
    public int product28Clear(UpdatePriceEntity updatePriceEntity, String userName) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(updatePriceEntity.getSeller());
        log.setSkuCode(updatePriceEntity.getSkuCode());
        log.setProductId(Long.valueOf(updatePriceEntity.getProductId()));
        log.setOperateType(AliexpressProductOperateLogType.PRICE28_CLEAR);
        log.setOperator(StringUtils.isNotBlank(userName) ? userName : WebUtils.getUserName());

        if ("清空成功".equals(updatePriceEntity.getErrorTip())) {
            log.setResult(true);
        } else {
            log.setResult(false);
        }
        log.setFailInfo(updatePriceEntity.getErrorTip());

        return insert(log);
    }


    @Override
    public int seasonInsert(String account, Long productId, Integer templateId, String operator, Boolean result, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(account);
        log.setOperateType(AliexpressProductOperateLogType.UPDATE_SEASON);
        if (productId != null) {
            log.setRelationType(LogRelationTypeEnum.product.intCode());
            log.setRelationId(productId.toString());
            log.setProductId(productId);
        } else {
            log.setRelationType(LogRelationTypeEnum.template.intCode());
            log.setRelationId(templateId.toString());
        }
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setResult(result);
        log.setFailInfo(errorMsg);
        return insert(log);
    }


    @Override
    public int attrInsert(String account, Long productId, String id, Integer type, String operator, Boolean result, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(account);
        log.setOperateType(OperateLogTypeEnum.update_attr.getCode());

        if (type == 0 || type == 1) {
            log.setRelationType(LogRelationTypeEnum.template.intCode());
            log.setRelationId(id);
        } else if (type == 2) {
            log.setRelationType(LogRelationTypeEnum.product.intCode());
            log.setRelationId(productId.toString());
            log.setProductId(productId);
        } else if (type == 4) {
            log.setRelationType(LogRelationTypeEnum.auto_template.intCode());
            log.setRelationId(id);
        } else {
            log.setRelationType(LogRelationTypeEnum.product_source.intCode());
            log.setRelationId(id);
        }
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setResult(result);
        log.setFailInfo(errorMsg);
        return insert(log);
    }


    /**
     * 模板
     *
     * @param template
     * @param operator
     * @return
     */
    @Override
    public AliexpressProductLog tempPublishCreateLogByMq(AliexpressTemplate template, String operator, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();

        log.setAccountNumber(template.getAliexpressAccountNumber());
        log.setSkuCode(template.getArticleNumber());
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        if (StringUtils.isBlank(operator)) {
            log.setOperator("admin");
        }
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));

        String operateType = OperateLogTypeEnum.POST.getCode();
        Integer templatePublishType = template.getPublishType();
        if (templatePublishType != null && templatePublishType == TemplatePublishTypeEnum.t_2.intCode()) {
            operateType = OperateLogTypeEnum.HALF_TG_POST.getCode();
        }
        log.setOperateType(operateType);

        log.setRelationType(LogRelationTypeEnum.template.intCode());
        log.setRelationId(template.getId().toString());

        if (StringUtils.isNotBlank(errorMsg)) {
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setResult(false);
            log.setFailInfo(errorMsg);
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //失败获取问题分类和解决方案
            if (BooleanUtils.isFalse(log.getResult()) && StringUtils.isNotBlank(errorMsg) && StringUtils.isNotBlank(operateType)) {
                AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(errorMsg, operateType);
                if (ObjectUtils.isNotEmpty(problemMaintain)) {
                    log.setProblemType(problemMaintain.getProblemType());
                    log.setSolutionType(problemMaintain.getSolutionType());
                }
            }
        } else {
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        }

        insert(log);
        return log;
    }

    @Override
    public AliexpressProductLog tempPublishCreateLogByMq(AliexpressTgTemplate tgTemplate, String operator, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();

        log.setAccountNumber(tgTemplate.getAliexpressAccountNumber());
        log.setSkuCode(tgTemplate.getArticleNumber());
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));
        log.setOperateType(OperateLogTypeEnum.TG_POST.getCode());
        log.setRelationType(LogRelationTypeEnum.tg_template.intCode());
        log.setRelationId(tgTemplate.getId().toString());

        if (StringUtils.isNotBlank(errorMsg)) {
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setResult(false);
            log.setFailInfo(errorMsg);
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(errorMsg, log.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        } else {
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        }
        insert(log);
        return log;
    }

    /**
     * 产品库
     *
     * @param productSource
     * @param operator
     * @param errorMsg
     * @return
     */
    @Override
    public AliexpressProductLog productSourcePublishCreateLogByMq(AliexpressProductSource productSource, String account, String operator, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();

        log.setAccountNumber(account);
        log.setSkuCode(productSource.getArticleNumber());
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));
        log.setOperateType(OperateLogTypeEnum.POST.getCode());

        log.setRelationType(LogRelationTypeEnum.product_source.intCode());
        log.setRelationId(productSource.getId().toString());

        if (StringUtils.isNotBlank(errorMsg)) {
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setResult(false);
            log.setFailInfo(errorMsg);
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(errorMsg, log.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        } else {
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        }

        insert(log);
        return log;
    }

    @Override
    public AliexpressProductLog productMoveCreateLogByMq(Long productId, String skuCode, String publishAccount, String operator, String errorMsg) {
        AliexpressProductLog log = new AliexpressProductLog();

        log.setAccountNumber(publishAccount);
        log.setSkuCode(skuCode);
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));
        log.setOperateType(OperateLogTypeEnum.POST.getCode());
        log.setRelationType(LogRelationTypeEnum.product.intCode());
        log.setRelationId(productId.toString());

        if (StringUtils.isNotBlank(errorMsg)) {
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setResult(false);
            log.setFailInfo(errorMsg);
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(errorMsg, log.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        } else {
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        }
        insert(log);
        return log;
    }

    @Override
    public AliexpressProductLog spuPublishCreateLogByMq(String account, String spu, String operator, String errorMsg, String operateType) {
        AliexpressProductLog log = new AliexpressProductLog();

        log.setAccountNumber(account);
        log.setSkuCode(spu);
        log.setOperator(StringUtils.isNotBlank(operator) ? operator : WebUtils.getUserName());
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));
        if (StringUtils.isBlank(operateType)) {
            operateType = OperateLogTypeEnum.POST.getCode(); //默认
        }
        log.setOperateType(operateType);
        if (StringUtils.isNotBlank(errorMsg)) {
            log.setOperateStatus(OperateLogStatusEnum.end.intCode());
            log.setResult(false);
            log.setFailInfo(errorMsg);
            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
            //失败获取问题分类和解决方案
            AliexpressReportProblemMaintain problemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(errorMsg, log.getOperateType());
            if (ObjectUtils.isNotEmpty(problemMaintain)) {
                log.setProblemType(problemMaintain.getProblemType());
                log.setSolutionType(problemMaintain.getSolutionType());
            }
        } else {
            log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        }

        insert(log);
        return log;
    }

    @Override
    public AliexpressProductLog popToSkuPublishCreateLogByMq(String account, String spu, TemplateQueue templateQueue) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(account);
        log.setSkuCode(spu);
        log.setOperator("admin");
        log.setCreateTime(new Timestamp(System.currentTimeMillis()));
        log.setOperateType(OperateLogTypeEnum.POP_TO_SKU.getCode());
        log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        log.setRuleName(templateQueue.getRuleName());
        insert(log);
        return log;
    }

    @Override
    public Integer getPublishRoleByProduct(Long productId) {
        //默认销售刊登
        int publishRole = PublishRoleEnum.SALE.getPublishRole();

        //寻找刊登产品并有模板的处理报告
        AliexpressProductLogExample logExample = new AliexpressProductLogExample();
        logExample.createCriteria().andProductIdEqualTo(productId)
                .andOperateTypeEqualTo(OperateLogTypeEnum.POST.getCode())
                .andRelationTypeEqualTo(LogRelationTypeEnum.template.intCode())
                .andResultEqualTo(true);
        List<AliexpressProductLog> aliexpressProductLogs = this.selectByExample(logExample);
        if (CollectionUtils.isEmpty(aliexpressProductLogs)) {
            return publishRole;
        }

        String relationId = aliexpressProductLogs.get(0).getRelationId();
        if (StringUtils.isNotBlank(relationId)) {
            AliexpressTemplate template = aliexpressTemplateService.selectByPrimaryKey(Integer.valueOf(relationId), AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE.getCode());
            if (template != null && template.getPublishRole() != null) {
                return template.getPublishRole();
            }
        }
        return publishRole;
    }


    @Override
    public List<AliexpressProductLogCountDto> getaliexpressProductLogCountDtoList(String code) {
        return aliexpressProductLogMapper.selectCountByType(code);
    }


}