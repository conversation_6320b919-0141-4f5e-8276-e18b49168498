package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.system.fms.model.SpuToFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/27 9:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeFileDO {

    /**
     * 账号
     */
    private List<String> accountList;

    /**
     * spu
     */
    private String spu;

    /**
     * ce资质文件
     */
    private List<SpuToFile> spuToCeFiles = new ArrayList<>();

    /**
     * 包装图片文件
     */
    private List<SpuToFile> spuToPackageFiles = new ArrayList<>();

    /**
     * 当前分类
     */
    private String categoryId;

    /**
     * 需排除的分类
     */
    private List<String> excludeCategoryIds;

    /**
     * 定时任务的参数
     */
    private List<Long> productIdList;

    /**
     * 需要排除的产品id
     */
    private List<Long> excludeProductIdList;



}
