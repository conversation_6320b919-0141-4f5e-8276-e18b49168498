package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.Data;

@Data
public class AliexpressCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表id database column aliexpress_category.id
     */
    private Integer id;

    /**
     * 速卖通类目id database column aliexpress_category.category_id
     */
    private Integer categoryId;

    /**
     * 类目层级 database column aliexpress_category.category_level
     */
    private Integer categoryLevel;

    /**
     * 是否叶子类目 database column aliexpress_category.leaf_category
     */
    private Boolean leafCategory;

    /**
     * 父类目表id database column aliexpress_category.parent_id
     */
    private Integer parentId;

    /**
     * 中文 database column aliexpress_category.category_zh_name
     */
    private String categoryZhName;

    /**
     * 英文 database column aliexpress_category.category_en_name
     */
    private String categoryEnName;

    /**
     * 葡萄牙文 database column aliexpress_category.category_pt_name
     */
    private String categoryPtName;

    /**
     * 法文 database column aliexpress_category.category_fr_name
     */
    private String categoryFrName;

    /**
     * 俄文 database column aliexpress_category.category_ru_name
     */
    private String categoryRuName;

    /**
     * 印文 database column aliexpress_category.category_in_name
     */
    private String categoryInName;

    /**
     * 西班牙文 database column aliexpress_category.category_es_name
     */
    private String categoryEsName;

    /**
     * 类目属性，品牌等等 database column aliexpress_category.child_attributes_json
     */
    private String childAttributesJson;

    /**
     * 类目族谱 database column aliexpress_category.full_path_code
     */
    private String fullPathCode;

    /**
     * 该分类的父级分类
     */
    private AliexpressCategory upCategory;

    /**
     * 该分类的下级分类 搜索用
     */
    private AliexpressCategory sonCategory;

    /**
     * 是否支持车载属性
     */
    private Boolean carType;

    /**
     * 产地属性
     */
    private Boolean origin;

    /**
     * 季节
     */
    private Boolean season;

    private Boolean province;

    private String provinceParam2;

    private String provinceAttributes;

    private Timestamp lastUpdateDate;

    private Timestamp synchDate;

    private Boolean isShow;

    /**
     * 完整的中文路径
     */
    private String fullCnName;

    /**
     * 是否需要资质
     */
    private Boolean isQualification;

    /**
     * 是否支持化学属性
     */
    private Boolean chemistry;

}