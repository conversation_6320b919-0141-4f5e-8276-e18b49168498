package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressOfflineProductListingQueryDO;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressOfflineProductListing;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.vo.AliexpressOfflineProductListingVO;

/**
 * <p>
 * 速卖通下架产品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface AliexpressOfflineProductListingService extends IService<AliexpressOfflineProductListing> {

    CQueryResult<AliexpressOfflineProductListingVO> search(CQuery<AliexpressOfflineProductListingQueryDO> query);

    ApiResult<?> downloadTable(CQuery<AliexpressOfflineProductListingQueryDO> query);

    IPage<AliexpressOfflineProductListing> selectPage(AliexpressOfflineProductListingQueryDO search, int pageSize, int limit);
}
