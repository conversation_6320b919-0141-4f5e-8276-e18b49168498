package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FreightImportStatusEnum {

    S_0(0, "待处理"),
    S_1(1, "成功"),
    S_2(2, "失败"),
    S_9(9, "处理中");

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static String convert(Integer value) {
        for (FreightImportStatusEnum status : FreightImportStatusEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

}
