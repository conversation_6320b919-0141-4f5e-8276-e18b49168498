package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressGetSolutionDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressReportProblemMaintainQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgVO;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressReportProblemMaintain;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressReportProblemMaintainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.controller
 * @Author: sj
 * @CreateTime: 2025-03-04  14:39
 * @Description: Aliexpress /处理报告问题维护前端控制器
 */

@Slf4j
@RestController
@RequestMapping("/aliexpressReportProblemMaintain")
public class AliexpressReportProblemMaintainController {


    @Resource
    private AliexpressReportProblemMaintainService aliexpressReportProblemMaintainService;

    /**
     * 分页查询
     */
    @PostMapping("/pageQuery")
    public ApiResult<IPage<AliexpressReportProblemMaintain>> pageQuery(@RequestBody AliexpressReportProblemMaintainQueryDto dto) {
        IPage<AliexpressReportProblemMaintain> page = null;
        try {
            page = aliexpressReportProblemMaintainService.pageQuery(dto);
        } catch (Exception e) {
            log.error("处理报告问题维护分页查询失败", e);
            throw new BusinessException("分页查询失败" + e.getMessage());
        }
        return ApiResult.newSuccess(page);
    }

    /**
     * 添加或修改
     */
    @PostMapping("/saveOrUpdate")
    public ApiResult<String> saveOrUpdate(@RequestBody @Valid AliexpressReportProblemMaintain entity){
        String result = null;
        try {
            result = aliexpressReportProblemMaintainService.saveOrUpdateByEntity(entity);
        } catch (Exception e) {
            log.error("处理报告问题维护添加或修改失败", e);
            throw new BusinessException("添加或修改失败" + e.getMessage());
        }
        return ApiResult.newSuccess(result);
    }

    /**
     * 根据id删除
     */
    @PostMapping("/delete")
    public ApiResult<String> delete(@RequestBody  List<Integer> idList){
        if (CollectionUtils.isEmpty(idList)){
            return ApiResult.newError("删除失败，请选择数据！");
        }
        aliexpressReportProblemMaintainService.removeByIds(idList);
        return ApiResult.newSuccess("删除成功！");
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody AliexpressReportProblemMaintainQueryDto dto){
        ResponseJson responseJson = null;
       try {
           responseJson = aliexpressReportProblemMaintainService.export(dto);
       } catch (Exception e) {
           log.error("处理报告问题维护导出失败", e);
           throw new BusinessException("导出失败" + e.getMessage());
       }
        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }

    /**
     * 获取分类下拉
     */
    @PostMapping("/getAllSolutionType")
    public ApiResult<List<String>> getAllSolutionType(){
        List<String> list = null;
        try {
            list = aliexpressReportProblemMaintainService.getAllSolutionType();
        } catch (Exception e) {
            log.error("处理报告问题维护获取分类下拉失败", e);
            throw new BusinessException("获取分类下拉失败" + e.getMessage());
        }
        return ApiResult.newSuccess(list);
    }

    /**
     * 根据报错信息获取处理方案
     */
    @PostMapping("/getSolutionByErrorMsg")
    public ApiResult<AliexpressReportProblemMaintain> getSolutionByErrorMsg(@RequestBody AliexpressGetSolutionDto dto){
        AliexpressReportProblemMaintain aliexpressReportProblemMaintain = null;
        try {
            aliexpressReportProblemMaintain = aliexpressReportProblemMaintainService.getSolutionByErrorMsg(dto.getErrorMsg(), dto.getOperationType());
        } catch (Exception e) {
            log.error("根据报错信息获取处理方案失败", e);
            throw new BusinessException("根据报错信息获取处理方案失败");
        }
        return ApiResult.newSuccess(aliexpressReportProblemMaintain);
    }

    /**
     * 模板获取操作报告错误信息和解决方案
     */
    @PostMapping("/getTemplateErrorMsgAndSolution")
    public ApiResult<AliexpressTemplateErrorMsgVO> getTemplateErrorMsgAndSolution(@RequestBody AliexpressTemplateErrorMsgDto dto){
        AliexpressTemplateErrorMsgVO vo = null;
        try {
            vo = aliexpressReportProblemMaintainService.getTemplateErrorMsgAndSolution(dto);
        } catch (Exception e) {
            log.error("模板获取操作报告错误信息和解决方案失败", e);
            throw new BusinessException("模板获取操作报告错误信息和解决方案失败");
        }
        return ApiResult.newSuccess(vo);
    }



}
