package com.estone.erp.publish.smt.enums;


public enum DownHalfItemEnum {
    account("店铺", "account"),
    productId("商品ID", "productId"),
    skuCode("商品编码", "skuCode"),
    scItemCode("货品编码", "scItemCode"),
    scItemBarCode("货品条码", "scItemBarCode"),
    title("标题", "title"),
    fullPathCode("平台类目", "fullPathCode"),
    basePrice("商品价格", "basePrice"),
    totalStocks("总库存", "totalStocks"),
    systemUsableTransferStock("可用+中转-待发", "systemUsableTransferStock"),
    productStatus("状态", "productStatus"),
    articleNumber("单品货号", "articleNumber"),
    skuStatus("单品状态", "skuStatus"),
    proCategoryCnName("分类", "proCategoryCnName"),
    skuTagCode("产品标签", "skuTagCode"),
    specialGoodCode("特殊标签", "specialGoodCode"),
    promotion("是否促销", "promotion"),
    newState("是否新品", "newState"),
    salemanager("销售", "salemanager"),
    salemanagerLeader("销售组长", "salemanagerLeader"),
    salesSupervisorName("销售主管", "salesSupervisorName"),
    modifiedTime("编辑时间", "modifiedTime"),
    createTime("创建时间", "createTime"),
    lastSynchTime("同步时间", "lastSynchTime"),
    infringementTypeName("禁售类型", "infringementTypeName"),
    infringementObj("禁售原因", "infringementObj"),
    forbidChannel("禁售平台", "forbidChannel"),
    prohibitionSites("禁售站点", "prohibitionSites"),
    skuBind("存在绑定关系", "skuBind"),
    usableStockStr("可用库存", "usableStockStr"),
    smtTransferStockStr("中转仓库存", "smtTransferStockStr"),
    onlineStatus("是否在线", "onlineStatus"),
    standardWeight("标准重量", "standardWeight"),
    ;

    private String name;
    private String excelValue;

    private DownHalfItemEnum(String name, String excelValue) {
        this.name = name;
        this.excelValue = excelValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExcelValue() {
        return excelValue;
    }

    public void setExcelValue(String excelValue) {
        this.excelValue = excelValue;
    }
}
