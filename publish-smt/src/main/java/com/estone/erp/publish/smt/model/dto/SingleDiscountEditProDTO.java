package com.estone.erp.publish.smt.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-08 15:53
 **/
@NoArgsConstructor
@Data
public class SingleDiscountEditProDTO {

    private List<ProductObjectsDTO> product_objects;
    private String promotion_id;
    private String store_club_discount_rate;

    @NoArgsConstructor
    @Data
    public static class ProductObjectsDTO {

        private List<ProductDiscountListDTO> product_discount_list;
        private String product_id;

        private Integer buy_max_num;

        @NoArgsConstructor
        @Data
        public static class ProductDiscountListDTO {
            private Integer discount;
            private String terminal;
        }
    }
}
