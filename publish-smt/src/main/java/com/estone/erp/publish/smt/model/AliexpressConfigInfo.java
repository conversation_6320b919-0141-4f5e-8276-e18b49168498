package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class AliexpressConfigInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_config_info.id
     */
    private Integer id;

    /**
     * 配置主键id database column aliexpress_config_info.config_id
     */
    private Integer configId;

    /**
     * >=重量 database column aliexpress_config_info.from_weight
     */
    private Double fromWeight;

    /**
     * <重量 database column aliexpress_config_info.to_weight
     */
    private Double toWeight;

    /**
     * 产品标签，逗号拼接 database column aliexpress_config_info.tag_codes
     */
    private String tagCodes;

    /**
     * 特殊标签
     */
    private String specialTagCode;

    /**
     * 产品分组 database column aliexpress_config_info.group_id
     */
    private Long groupId;

    /**
     * 运费模板 database column aliexpress_config_info.freight_template_id
     */
    private Long freightTemplateId;

    /**
     * 服务模板id database column aliexpress_config_info.promise_template_id
     */
    private Long promiseTemplateId;

    /**
     * 毛利 database column aliexpress_config_info.gross_profit
     */
    private Double grossProfit;

    /**
     * 运输方式 database column aliexpress_config_info.shipping_method
     */
    private String shippingMethod;

    /**
     * 计算国家
     */
    private String countryCode;


    /**
     * 创建人 database column aliexpress_config_info.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_config_info.create_date
     */
    private Timestamp createDate;

    /**
     * 试算价格配置
     */
    private List<AliexpressConfigPriceTrial> priceTrialList = new ArrayList<>();

    /**
     * 需删除的价格配置id
     */
    private List<Integer> priceTrialDeleteIdList;

    /**
     * 价格区间开始值
     */
    private Double fromPrice;

    /**
     * 价格区间结束值
     */
    private Double toPrice;

    /**
     * 折扣率
     */
    private Double discountRate;
}