package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.componet.marking.EarlyBirdActivityConfigParam;
import com.estone.erp.publish.smt.enums.AlianceMarketingConfigTypeEnum;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.jobHandler.AliexpressAutoJoinEarlyBirdActivityJobHandler;
import com.estone.erp.publish.smt.model.AliexpressMarketingConfig;
import com.estone.erp.publish.smt.model.SmtAccountGroup;
import com.estone.erp.publish.smt.model.SmtAccountGroupExample;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressMarketingConfigService;
import com.estone.erp.publish.smt.service.SmtAccountGroupService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.QuerySpuByCondition;
import com.estone.erp.publish.system.product.response.QuerySpuByConditionVo;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressEarlyBirdActivityMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsSkuSaleAttribute;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressEarlyBirdActivityService;
import com.estone.erp.publish.tidb.publishtidb.service.IAdsSkuSaleAttributeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-06-14 12:09:21
 */
@Service("aliexpressEarlyBirdActivityService")
@Slf4j
public class AliexpressEarlyBirdActivityServiceImpl extends ServiceImpl<AliexpressEarlyBirdActivityMapper, AliexpressEarlyBirdActivity> implements AliexpressEarlyBirdActivityService {
    @Resource
    private AliexpressEarlyBirdActivityMapper aliexpressEarlyBirdActivityMapper;
    @Resource
    private ExcelSend excelSend;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Autowired
    private SmtAccountGroupService smtAccountGroupService;
    @Autowired
    private AliexpressMarketingConfigService aliexpressMarketingConfigService;
    @Resource
    private AliexpressAutoJoinEarlyBirdActivityJobHandler joinEarlyBirdActivityJobHandler;
    @Resource
    private IAdsSkuSaleAttributeService adsSkuSaleAttributeService;

    @Override
    public int countByExample(AliexpressEarlyBirdActivityExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEarlyBirdActivityMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressEarlyBirdActivity> search(CQuery<AliexpressEarlyBirdActivityCriteria> cquery) throws Exception {
        // 组装结果
        CQueryResult<AliexpressEarlyBirdActivity> result = new CQueryResult<>();

        Assert.notNull(cquery, "cquery is null!");
        AliexpressEarlyBirdActivityCriteria query = cquery.getSearch();
        //查询账号权限
        isSmtAuth(query);

        //店铺分组过滤
        if (ObjectUtils.isNotEmpty(cquery.getSearch()) && CollectionUtils.isNotEmpty(cquery.getSearch().getGroupIdList())) {
            SmtAccountGroupExample groupExample = new SmtAccountGroupExample();
            groupExample.createCriteria().andIdIn(cquery.getSearch().getGroupIdList());
            List<SmtAccountGroup> accountGroupList = smtAccountGroupService.selectByExample(groupExample);
            List<String> accountList = accountGroupList.stream().flatMap(t -> t.getAccountList().stream()).distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(cquery.getSearch().getStores())){
                cquery.getSearch().getStores().retainAll(accountList);
                //没有交集，直接返回空数据
                if (CollectionUtils.isEmpty(cquery.getSearch().getStores())){
                    result.setRows(Lists.newArrayList());
                    return result;
                }
            }else {
                cquery.getSearch().setStores(accountList);
            }
        }

        AliexpressEarlyBirdActivityExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressEarlyBirdActivityMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressEarlyBirdActivity> aliexpressEarlyBirdActivitys = aliexpressEarlyBirdActivityMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(aliexpressEarlyBirdActivitys)){
            List<String> accountNumberList = aliexpressEarlyBirdActivitys.stream().map(t -> t.getStore()).distinct().collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SMT);

            //店铺分组
            SmtAccountGroupExample groupExample = new SmtAccountGroupExample();
            List<SmtAccountGroup> accountGroupList = smtAccountGroupService.selectByExample(groupExample);

            aliexpressEarlyBirdActivitys.forEach(aliexpressEarlyBirdActivity->{
                Optional<SmtAccountGroup> first = accountGroupList.stream().filter(t -> t.getAccountList().contains(aliexpressEarlyBirdActivity.getStore())).findFirst();
                if(first.isPresent()){
                    aliexpressEarlyBirdActivity.setGroupName(first.get().getGroupName());
                }

                // 销售、销售组长、销售主管
                if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(aliexpressEarlyBirdActivity.getStore());
                    aliexpressEarlyBirdActivity.setSeller(saleSuperiorTriple.getLeft());
                    aliexpressEarlyBirdActivity.setSellerLeader(saleSuperiorTriple.getMiddle());
                    aliexpressEarlyBirdActivity.setSellerManager(saleSuperiorTriple.getRight());
                }

            });

        }

        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressEarlyBirdActivitys);
        return result;
    }


    @Override
    public AliexpressEarlyBirdActivity selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressEarlyBirdActivityMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressEarlyBirdActivity> selectByExample(AliexpressEarlyBirdActivityExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEarlyBirdActivityMapper.selectByExample(example);
    }

    @Override
    public List<AliexpressEarlyBirdActivity> selectFieldsByExample(AliexpressEarlyBirdActivityExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEarlyBirdActivityMapper.selectFieldsByExample(example);
    }

    @Override
    public void deleteOverDueProduct() {
        aliexpressEarlyBirdActivityMapper.deleteOverDueProduct();
    }

    @Override
    public List<String> selectAllStore() {
        return aliexpressEarlyBirdActivityMapper.selectAllStore();
    }

    @Override
    public List<String> selectItemIdsByExample(AliexpressEarlyBirdActivityExample aliexpressEarlyBirdActivityExample) {
        return aliexpressEarlyBirdActivityMapper.selectItemIdsByExample(aliexpressEarlyBirdActivityExample);
    }

    @Override
    public void deleteByItemIds(List<String> deleteItemIds) {
        if(deleteItemIds.size() != 0){
            aliexpressEarlyBirdActivityMapper.deleteByItemIds(deleteItemIds);
        }
    }


    @Override
    public int insert(AliexpressEarlyBirdActivity record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressEarlyBirdActivityMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressEarlyBirdActivity record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEarlyBirdActivityMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressEarlyBirdActivity record, AliexpressEarlyBirdActivityExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEarlyBirdActivityMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressEarlyBirdActivityMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ResponseJson joinAliexpressEarlyBirdActivity(CQuery<List<AliexpressEarlyBirdActivity>> cquery) {
        List<AliexpressEarlyBirdActivity> search = cquery.getSearch();
        SystemParam systemParam = systemParamService.queryParamValue("Smt", "smt_param", "early_bird_activity_join_condition");
        int number = Integer.valueOf(systemParam.getParamValue()).intValue();

        for (AliexpressEarlyBirdActivity aliexpressEarlyBirdActivity : search) {
            if(aliexpressEarlyBirdActivity.getOrderLast30dCount() == null){
                continue;
            }
            if(aliexpressEarlyBirdActivity.getOrderLast30dCount().intValue() >= number){
                throw new RuntimeException("只允许加入30天销量小于"+String.valueOf(number)+"的,请重试");
            }
        }
        List<Long> ids = search.stream().map(AliexpressEarlyBirdActivity::getId).collect(Collectors.toList());
        aliexpressEarlyBirdActivityMapper.updateStatusToJoin(ids);
        return new ResponseJson(StatusCode.SUCCESS);
    }
    @Override
    public ResponseJson downloadEarlyBirdActivities(CQuery<AliexpressEarlyBirdActivityCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressEarlyBirdActivityCriteria query = cquery.getSearch();
        //查询账号权限
        isSmtAuth(query);
        if(query.getIds() == null) {
            AliexpressEarlyBirdActivityExample example = query.getExample();
            int total = aliexpressEarlyBirdActivityMapper.countByExample(example);
            if(total>500000){
                throw new RuntimeException("下载数量不能大于50w");
            }
        }
        ResponseJson responseJson = excelSend.downloadEarlyBirdActivities(ExcelTypeEnum.downloadEarlyBirdActivities.getCode(), cquery);
        return responseJson;
    }
    @Override
    public void batchInsert(ArrayList<AliexpressEarlyBirdActivity> aliexpressEarlyBirdActivities) {
        if(aliexpressEarlyBirdActivities.size() != 0){
            aliexpressEarlyBirdActivityMapper.batchInsert(aliexpressEarlyBirdActivities);
        }
    }

    @Override
    public void batchUpdate(ArrayList<AliexpressEarlyBirdActivity> earlyBirdActivities) {
        if(earlyBirdActivities.size() != 0){
            aliexpressEarlyBirdActivityMapper.batchUpdate(earlyBirdActivities);
        }
    }

    public void isSmtAuth(AliexpressEarlyBirdActivityCriteria query) {
        List<String> accountNumbers = query.getStores();
        List<String> managerIds = query.getSellerManagers();
        List<String> leaderIds = query.getSellerLeaders();
        List<String> saleIds = query.getSellers();
        List<Integer> groupIds = query.getGroupIdList();
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setStores(authAccountNumbers);
    }


    @Override
    public List<QuerySpuByConditionVo> queryHeaviestSkuByMarketingConfigId(Integer marketingConfigId) {
        AliexpressMarketingConfig marketingConfig=aliexpressMarketingConfigService.selectByPrimaryKey(marketingConfigId);
        if(null == marketingConfig){
            throw new RuntimeException(marketingConfigId+"配置数据不存在");
        }
        if(!AlianceMarketingConfigTypeEnum.EARLY_BIRD_ACTIVITY.getCode().equals(marketingConfig.getType())){
            throw new RuntimeException(marketingConfigId+"这个配置id不是早鸟活动类型配置数据");
        }

        EarlyBirdActivityConfigParam configParam = JSON.parseObject(marketingConfig.getRuleJson(), EarlyBirdActivityConfigParam.class);
        if (null != configParam.getMaxStoreSignUpNum() && configParam.getMaxStoreSignUpNum() <= 0) {
            throw new RuntimeException(marketingConfigId+"这个早鸟活动类型配置的店铺最大报名数为0");
        }
        List<String> stores = CommonUtils.splitList(StrUtil.strDeldComma(marketingConfig.getAccounts()), ",").subList(0,1);

        AliexpressEarlyBirdActivityExample example = new AliexpressEarlyBirdActivityExample();
        AliexpressEarlyBirdActivityExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIn(stores);
        AliexpressAutoJoinEarlyBirdActivityJobHandler.setCretia(configParam,criteria);
        example.setOrderByClause("id");
        List<AliexpressEarlyBirdActivity> earlyBirdActivities = aliexpressEarlyBirdActivityMapper.selectByExample(example);
        if (null == earlyBirdActivities || CollectionUtils.isEmpty(earlyBirdActivities)) {
            log.info("配置规则id{}没有符合定时自动加入早鸟活动的配置规则的数据",marketingConfigId);
            return null;
        }
        //过滤上架时间即最近X天刊登产品
        if (null != configParam.getPublishProductFrom() && null != configParam.getPublishProductTo()) {
            List<String> productIds=earlyBirdActivities.stream().map(p->p.getItemid()).distinct().collect(Collectors.toList());
            List<String> filterProductIds=joinEarlyBirdActivityJobHandler.getProductIdByListingTime(configParam,productIds);
            earlyBirdActivities=earlyBirdActivities.stream().filter(e->filterProductIds.contains(e.getItemid())).collect(Collectors.toList());
        }

        List<String> spus = earlyBirdActivities.stream().map(e -> e.getSpu()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spus)) {
            log.info("配置规则id{} 按规则筛选的早鸟活动数据spu为空",marketingConfigId);
            return null;
        }
        QuerySpuByCondition condition = new QuerySpuByCondition();
        condition.setSpuList(spus);
        List<Integer> skuStatusList = CommonUtils.splitList(StrUtil.strDeldComma(configParam.getSkuStatus()), ",").stream().map(Integer::valueOf).collect(Collectors.toList());
        condition.setItemStatusList(skuStatusList);//SingleItemEnum
        condition.setSalesNumStartByPlat(configParam.getOrderFrom());
        condition.setSalesNumEndByPlat(configParam.getOrderTo());
        condition.setSalesNumCycleByPlat(configParam.getOrderDay());
        condition.setSalesNumByPlat(SaleChannel.CHANNEL_SMT.toLowerCase());
        log.info("配置规则id{}调用产品系统，请求参数为:{}", marketingConfigId,JSONObject.toJSONString(condition));
        List<QuerySpuByConditionVo> allSkuVos = ProductUtils.queryHeaviestSkuWithCondition(condition);
        if (CollectionUtils.isEmpty(allSkuVos)) {
            log.info("配置规则id{} 店铺{} 请求参数{}调用产品系统根据条件获取spu中最重sku返回数据为空",marketingConfigId,stores, JSONObject.toJSONString(condition));
            return allSkuVos;
        }

        //筛选返回空的sku数据打印日志
        List<QuerySpuByConditionVo> emptySkuVos = allSkuVos.stream().filter(s -> StringUtils.isBlank(s.getSku())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptySkuVos)) {
            log.info("配置规则id{} 店铺{} 请求参数{} 返回数据{}调用产品系统根据条件获取spu中最重sku返回数据存在空sku数据为{}",marketingConfigId , stores, JSONObject.toJSONString(condition), JSONObject.toJSONString(allSkuVos), JSONObject.toJSONString(emptySkuVos));
        }

        List<String> allSkus = allSkuVos.stream().map(skuVo -> skuVo.getSku()).filter(sku->StringUtils.isNotBlank(sku)).collect(Collectors.toList());

        List<List<String>> lists = Lists.partition(allSkus, 1000);

        LambdaQueryWrapper<AdsSkuSaleAttribute> wrapper = new LambdaQueryWrapper<>();
        for (List<String> list : lists) {
            wrapper.or().in(AdsSkuSaleAttribute::getSku, list);
        }
        List<AdsSkuSaleAttribute> adsSkuSaleAttributes = adsSkuSaleAttributeService.list(wrapper);
        Map<String, List<AdsSkuSaleAttribute>> attributeMap = adsSkuSaleAttributes.stream().collect(Collectors.groupingBy(t -> t.getSku()));
        List<AdsSkuSaleAttribute> filterSkuAttrs = StringUtils.isBlank(configParam.getNotContainWareHouseSaleAttr()) ? adsSkuSaleAttributes : adsSkuSaleAttributes.stream().filter(at -> !configParam.getNotContainWareHouseSaleAttr().contains(at.getSaleAttribute())).collect(Collectors.toList());

        //符合销售属性的sku
        List<String> filterSkus = filterSkuAttrs.stream().map(a -> a.getSku()).collect(Collectors.toList());
        List<QuerySpuByConditionVo> filterSkuVos = allSkuVos.stream().filter(s -> filterSkus.contains(s.getSku())).collect(Collectors.toList());
        for (QuerySpuByConditionVo vo : filterSkuVos) {
            List<AdsSkuSaleAttribute> skuSaleAttributes = attributeMap.get(vo.getSku());
            if (CollectionUtils.isNotEmpty(skuSaleAttributes)) {
                vo.setSaleAttr(skuSaleAttributes.get(0).getSaleAttribute());
            }
        }

        List<String> filterSpus = filterSkuVos.stream().map(s -> s.getSpu()).collect(Collectors.toList());
        List<AliexpressEarlyBirdActivity> autoJoinEarlyBirdActivities = earlyBirdActivities.stream().filter(e -> filterSpus.contains(e.getSpu())).collect(Collectors.toList());
        List<Long> ids=autoJoinEarlyBirdActivities.stream().map(s->s.getId()).collect(Collectors.toList());
        log.info("ids====={} 最大报名数{}======{}",ids,configParam.getMaxStoreSignUpNum());

        return allSkuVos;
    }

    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AliexpressEarlyBirdActivity> wrapper) {
        Assert.notNull(wrapper, "wrapper is null!");
        List<Map<Object, Object>> mapList = aliexpressEarlyBirdActivityMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }


}