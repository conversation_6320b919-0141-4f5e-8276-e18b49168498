package com.estone.erp.publish.smt.enums;

public enum AliexpressFullReductionActivityTypeEnum {
    REDUCE(1, "满立减"),
    DISCOUNT(2, "满立折");

    private int code;

    private String name;

    AliexpressFullReductionActivityTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressFullReductionActivityTypeEnum build(int code) {
        AliexpressFullReductionActivityTypeEnum[] values = values();
        for (AliexpressFullReductionActivityTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressFullReductionActivityTypeEnum[] values = values();
        for (AliexpressFullReductionActivityTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
