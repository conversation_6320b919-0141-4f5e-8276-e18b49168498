package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import lombok.Data;

@Data
public class AliexpressAlianceConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_aliance_config.id
     */
    private Integer id;

    /**
     * 账号 database column aliexpress_aliance_config.account_number
     */
    private String accountNumber;

    /**
     * 自动配置联盟状态;1：配置生效；0:配置不生效 database column aliexpress_aliance_config.aliance_auto_status
     */
    private Integer alianceAutoStatus;

    /**
     * 初始佣金率或者策略佣金率 database column aliexpress_aliance_config.init_commission_rate
     */
    private Integer initCommissionRate;

    /**
     * 链接上架时间 database column aliexpress_aliance_config.link_publish_time
     */
    private String linkPublishTime;

    /**
     * 销量区间 database column aliexpress_aliance_config.sales_range
     */
    private String salesRange;

    /**
     * 移除分组 database column aliexpress_aliance_config.remove_group
     */
    private String removeGroup;

    /**
     * 设置频率;every_day;every_week;every_month database column aliexpress_aliance_config.trigger_type
     */
    private String triggerType;

    /**
     * 每周/每月执行哪些天,多个使用逗号隔开 database column aliexpress_aliance_config.exec_days_time
     */
    private String execDaysTime;

    /**
     * 开始设置时间 database column aliexpress_aliance_config.start_time
     */
    private String startTime;

    /**
     * 计划推广时长 database column aliexpress_aliance_config.plan_days
     */
    private Integer planDays;

    /**
     * 计划出单数量 database column aliexpress_aliance_config.plan_order_counts
     */
    private Integer planOrderCounts;

    /**
     * 创建者 database column aliexpress_aliance_config.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_aliance_config.create_time
     */
    private Timestamp createTime;

    /**
     * 修改人 database column aliexpress_aliance_config.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_aliance_config.update_time
     */
    private Timestamp updateTime;

    /**
     * 店铺配置表的id database column aliexpress_aliance_config.config_id
     */
    private Integer configId;

    private String publishTimeSalesRange;

    /**
     * 策略开始时间
     */
    private Date strategyStartTime;

    /**
     * 策略结束时间
     */
    private Date strategyEndTime;


}