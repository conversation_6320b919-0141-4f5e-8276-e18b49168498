package com.estone.erp.publish.smt.model;

import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.common.util.CommonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class SmtAccountGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 分组名称
     */
    @NeedToLog(value = "店铺分组名称")
    private String groupName;

    /**
     * 店铺多个逗号拼接
     */
    @NeedToLog(value = "适用店铺")
    private String accounts;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateDate;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp syncTime;

    /**
     * 同步人
     */
    private String syncBy;


    public List<String> getAccountList(){
        List<String> accountList = new ArrayList<>();
        if(StringUtils.isNotBlank(this.getAccounts())){
            return CommonUtils.splitList(this.getAccounts(), ",");
        }
        return accountList;
    }
}