package com.estone.erp.publish.smt.enums;

public enum HalfPriceIntervalEnum {
    intervale1(0.0, 10.0, 0.0, 50.0, 0.35),
    intervale2(0.0, 10.0, 50.0, 100.0, 0.4),
    intervale3(0.0, 10.0, 100.0, 150.0, 0.45),
    intervale4(0.0, 10.0, 150.0, 300.0, 0.5),
    intervale5(0.0, 10.0, 300.0, 1000.0, 0.6),
    intervale6(0.0, 10.0, 1000.0, 1950.0, 0.7),
    intervale7(10.0, 30.0, 0.0, 50.0, 0.30),
    intervale8(10.0, 30.0, 50.0, 100.0, 0.35),
    intervale9(10.0, 30.0, 100.0, 150.0, 0.4),
    intervale10(10.0, 30.0, 150.0, 300.0, 0.45),
    intervale11(10.0, 30.0, 300.0, 1000.0, 0.55),
    intervale12(10.0, 30.0, 1000.0, 1950.0, 0.65),
    intervale13(30.0, 10000.0, 0.0, 50.0, 0.30),
    intervale14(30.0, 10000.0, 50.0, 100.0, 0.35),
    intervale15(30.0, 10000.0, 100.0, 150.0, 0.4),
    intervale16(30.0, 10000.0, 150.0, 300.0, 0.45),
    intervale17(30.0, 10000.0, 300.0, 1000.0, 0.55),
    intervale18(30.0, 10000.0, 1000.0, 1950.0, 0.65),
    ;

    private Double fromPrice;
    private Double toPrice;
    private Double fromWeight;
    private Double toWeight;
    private Double ratio;

    private HalfPriceIntervalEnum(Double fromPrice, Double toPrice, Double fromWeight, Double toWeight, Double ratio) {
        this.fromPrice = fromPrice;
        this.toPrice = toPrice;
        this.fromWeight = fromWeight;
        this.toWeight = toWeight;
        this.ratio = ratio;
    }

    public Double getFromPrice() {
        return fromPrice;
    }

    public void setFromPrice(Double fromPrice) {
        this.fromPrice = fromPrice;
    }

    public Double getToPrice() {
        return toPrice;
    }

    public void setToPrice(Double toPrice) {
        this.toPrice = toPrice;
    }

    public Double getFromWeight() {
        return fromWeight;
    }

    public void setFromWeight(Double fromWeight) {
        this.fromWeight = fromWeight;
    }

    public Double getToWeight() {
        return toWeight;
    }

    public void setToWeight(Double toWeight) {
        this.toWeight = toWeight;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }
}
