package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AdsSkuStockUpdateLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdsSkuStockUpdateLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityIsNull() {
            addCriterion("original_quantity is null");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityIsNotNull() {
            addCriterion("original_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityEqualTo(Integer value) {
            addCriterion("original_quantity =", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityNotEqualTo(Integer value) {
            addCriterion("original_quantity <>", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityGreaterThan(Integer value) {
            addCriterion("original_quantity >", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("original_quantity >=", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityLessThan(Integer value) {
            addCriterion("original_quantity <", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("original_quantity <=", value, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityIn(List<Integer> values) {
            addCriterion("original_quantity in", values, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityNotIn(List<Integer> values) {
            addCriterion("original_quantity not in", values, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityBetween(Integer value1, Integer value2) {
            addCriterion("original_quantity between", value1, value2, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andOriginalQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("original_quantity not between", value1, value2, "originalQuantity");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedIsNull() {
            addCriterion("quantity_updated is null");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedIsNotNull() {
            addCriterion("quantity_updated is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedEqualTo(Integer value) {
            addCriterion("quantity_updated =", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedNotEqualTo(Integer value) {
            addCriterion("quantity_updated <>", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedGreaterThan(Integer value) {
            addCriterion("quantity_updated >", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_updated >=", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedLessThan(Integer value) {
            addCriterion("quantity_updated <", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_updated <=", value, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedIn(List<Integer> values) {
            addCriterion("quantity_updated in", values, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedNotIn(List<Integer> values) {
            addCriterion("quantity_updated not in", values, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedBetween(Integer value1, Integer value2) {
            addCriterion("quantity_updated between", value1, value2, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andQuantityUpdatedNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_updated not between", value1, value2, "quantityUpdated");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenIsNull() {
            addCriterion("before_value_frozen is null");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenIsNotNull() {
            addCriterion("before_value_frozen is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenEqualTo(Integer value) {
            addCriterion("before_value_frozen =", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenNotEqualTo(Integer value) {
            addCriterion("before_value_frozen <>", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenGreaterThan(Integer value) {
            addCriterion("before_value_frozen >", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenGreaterThanOrEqualTo(Integer value) {
            addCriterion("before_value_frozen >=", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenLessThan(Integer value) {
            addCriterion("before_value_frozen <", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenLessThanOrEqualTo(Integer value) {
            addCriterion("before_value_frozen <=", value, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenIn(List<Integer> values) {
            addCriterion("before_value_frozen in", values, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenNotIn(List<Integer> values) {
            addCriterion("before_value_frozen not in", values, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenBetween(Integer value1, Integer value2) {
            addCriterion("before_value_frozen between", value1, value2, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andBeforeValueFrozenNotBetween(Integer value1, Integer value2) {
            addCriterion("before_value_frozen not between", value1, value2, "beforeValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenIsNull() {
            addCriterion("after_value_frozen is null");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenIsNotNull() {
            addCriterion("after_value_frozen is not null");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenEqualTo(Integer value) {
            addCriterion("after_value_frozen =", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenNotEqualTo(Integer value) {
            addCriterion("after_value_frozen <>", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenGreaterThan(Integer value) {
            addCriterion("after_value_frozen >", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenGreaterThanOrEqualTo(Integer value) {
            addCriterion("after_value_frozen >=", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenLessThan(Integer value) {
            addCriterion("after_value_frozen <", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenLessThanOrEqualTo(Integer value) {
            addCriterion("after_value_frozen <=", value, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenIn(List<Integer> values) {
            addCriterion("after_value_frozen in", values, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenNotIn(List<Integer> values) {
            addCriterion("after_value_frozen not in", values, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenBetween(Integer value1, Integer value2) {
            addCriterion("after_value_frozen between", value1, value2, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andAfterValueFrozenNotBetween(Integer value1, Integer value2) {
            addCriterion("after_value_frozen not between", value1, value2, "afterValueFrozen");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Timestamp value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Timestamp value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Timestamp value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Timestamp value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Timestamp> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Timestamp> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNull() {
            addCriterion("createdby is null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIsNotNull() {
            addCriterion("createdby is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedbyEqualTo(Integer value) {
            addCriterion("createdby =", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotEqualTo(Integer value) {
            addCriterion("createdby <>", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThan(Integer value) {
            addCriterion("createdby >", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyGreaterThanOrEqualTo(Integer value) {
            addCriterion("createdby >=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThan(Integer value) {
            addCriterion("createdby <", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyLessThanOrEqualTo(Integer value) {
            addCriterion("createdby <=", value, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyIn(List<Integer> values) {
            addCriterion("createdby in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotIn(List<Integer> values) {
            addCriterion("createdby not in", values, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyBetween(Integer value1, Integer value2) {
            addCriterion("createdby between", value1, value2, "createdby");
            return (Criteria) this;
        }

        public Criteria andCreatedbyNotBetween(Integer value1, Integer value2) {
            addCriterion("createdby not between", value1, value2, "createdby");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}