package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressCategoryDeliveryDay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_category_delivery_day.id
     */
    private Integer id;

    /**
     * 速卖通类目id database column aliexpress_category_delivery_day.category_id
     */
    private Integer categoryId;

    /**
     * 子类目英文 database column aliexpress_category_delivery_day.leaf_category_en_name
     */
    private String leafCategoryEnName;

    /**
     * 一级类目英文 database column aliexpress_category_delivery_day.one_category_en_name
     */
    private String oneCategoryEnName;

    /**
     * 二级类目英文 database column aliexpress_category_delivery_day.two_category_en_name
     */
    private String twoCategoryEnName;

    /**
     * 发货天数 database column aliexpress_category_delivery_day.day
     */
    private Integer day;

    /**
     * 创建时间 database column aliexpress_category_delivery_day.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column aliexpress_category_delivery_day.create_by
     */
    private String createBy;

    /**
     * 修改人 database column aliexpress_category_delivery_day.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_category_delivery_day.update_date
     */
    private Timestamp updateDate;
}