package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-25 10:21
 **/
@Data
public class EnableDisableDTO {
    @NotEmpty(message = "id列表不能为空")
    private List<Integer> idList;

    @NotNull(message = "禁启用状态不能为空")
    private Integer status; //0=禁用，1=启用

}
