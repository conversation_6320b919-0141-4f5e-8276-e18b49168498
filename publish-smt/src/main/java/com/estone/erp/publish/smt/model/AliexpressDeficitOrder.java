package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressDeficitOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_deficit_order.id
     */
    private Integer id;

    /**
     * 店铺 database column aliexpress_deficit_order.account_number
     */
    private String accountNumber;

    /**
     * 货号 database column aliexpress_deficit_order.article_number
     */
    private String articleNumber;

    /**
     * 产品id database column aliexpress_deficit_order.product_id
     */
    private Long productId;

    /**
     * 商品编码 database column aliexpress_deficit_order.sku_code
     */
    private String skuCode;

    /**
     * 订单毛利 database column aliexpress_deficit_order.order_gross_profit
     */
    private Double orderGrossProfit;

    /**
     * 订单毛利率 database column aliexpress_deficit_order.order_gross_profit_rate
     */
    private Double orderGrossProfitRate;

    /**
     * 标红
     */
    private Boolean signRed;

    /**
     * 之前的价格 database column aliexpress_deficit_order.before_price
     */
    private Double beforePrice;

    /**
     * 现在的价格 database column aliexpress_deficit_order.now_price
     */
    private Double nowPrice;

    /**
     * 产品毛利 database column aliexpress_deficit_order.product_gross_profit
     */
    private Double productGrossProfit;

    /**
     * 产品毛利率 database column aliexpress_deficit_order.product_gross_profit_rate
     */
    private Double productGrossProfitRate;

    /**
     * 是否修改价格 0否 1是 2不调价 database column aliexpress_deficit_order.modify_price
     */
    private Integer modifyPrice;

    private String groupIds;

    /**
     * 之前运费模板id database column aliexpress_deficit_order.before_freight_template_id
     */
    private Long beforeFreightTemplateId;

    /**
     * 现在运费模板id database column aliexpress_deficit_order.now_freight_template_id
     */
    private Long nowFreightTemplateId;

    /**
     * 推送时间 database column aliexpress_deficit_order.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_deficit_order.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_deficit_order.update_date
     */
    private Timestamp updateDate;

    /**
     * 价格修改时间（及时没调价也会更新这个时间） database column aliexpress_deficit_order.price_update_date
     */
    private Timestamp priceUpdateDate;


    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    private String salesSupervisorName;

    //产品分组
    private String groupName;

    //运费模板
    private String freightTemplateName;

    //计算毛利使用
    private String shippingMethodCode;
    private String currencyCode;

    //店铺配置毛利率 用于标红判断
    private Double configGrossProfitRate;
}