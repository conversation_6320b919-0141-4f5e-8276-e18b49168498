package com.estone.erp.publish.smt.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-05 14:53
 **/
@NoArgsConstructor
@Data
public class SingleDiscountListDTO {


    private AliexpressMarketingStorepromotionsListResponseDTO aliexpress_marketing_storepromotions_list_response;

    @NoArgsConstructor
    @Data
    public static class AliexpressMarketingStorepromotionsListResponseDTO {
        private Integer current_page;
        private DataListDTO data_list;
        private Integer page_size;
        private String request_id;
        private Integer total_count;

        @NoArgsConstructor
        @Data
        public static class DataListDTO {
            private List<PromotionSimpleInfoDtoDTO> promotion_simple_info_dto;

            @NoArgsConstructor
            @Data
            public static class PromotionSimpleInfoDtoDTO {
                private String end_time;
                private Long promotion_id;
                private String promotion_name;
                private String start_time;
                private String status;
            }
        }
    }
}
