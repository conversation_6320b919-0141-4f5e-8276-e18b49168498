package com.estone.erp.publish.smt.enums;

public enum ScitemInvitationOperatorStatusEnum {
    WAITING(0, "未确认"),
    COMFIRM_SUCCESS(1, "已确认抢占成功"),
    COMFIRM_FAIL(2, "已确认抢占失败"),
    REJECTED_SUCCESS(3, "已确认不抢占成功"),
    REJECTED_FAIL(4, "已确认不抢占失败"),
    END(5, "已结束");

    private final int code;

    private final String desc;

    ScitemInvitationOperatorStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return desc;
    }

    // 根据code获取枚举对象
    public static ScitemInvitationOperatorStatusEnum fromCode(int code) {
        for (ScitemInvitationOperatorStatusEnum status : ScitemInvitationOperatorStatusEnum.values()) {
            if (status.getCode() == (code)) {
                return status;
            }
        }
        return null;
    }
}
