package com.estone.erp.publish.smt.enums;

public enum TemplateTypeEnum {

    COMMON(1, "普通刊登"),

    TIMED(2, "定时刊登"),

    AUTO_PUBLISH(3, "自动刊登"),

    SP_PUBLISH(4, "试卖刊登");

    private int code;

    private String name;

    private TemplateTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TemplateTypeEnum build(int code) {
        TemplateTypeEnum[] values = values();
        for (TemplateTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        TemplateTypeEnum[] values = values();
        for (TemplateTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
