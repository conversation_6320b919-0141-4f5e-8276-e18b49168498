package com.estone.erp.publish.smt.util.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import org.apache.commons.lang3.StringUtils;

public class SaleConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null || value.trim().isEmpty()) {
            return new WriteCellData("");
        }
        return new WriteCellData(this.getWriteValue(value));
    }

    private String getWriteValue(String value) {
        NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
        }, value);
        if (ObjectUtils.isNotEmpty(newUser) && StringUtils.isNotBlank(newUser.getName())) {
            return value + "-" + newUser.getName();
        }
        return value;
    }

}