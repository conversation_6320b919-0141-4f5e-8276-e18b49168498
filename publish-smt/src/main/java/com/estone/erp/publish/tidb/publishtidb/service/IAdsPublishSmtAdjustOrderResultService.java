package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustOrderResult;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

public interface IAdsPublishSmtAdjustOrderResultService extends IService<AdsPublishSmtAdjustOrderResult> {

    //IPage<AdsPublishSmtAdjustOrderResult> getPageWithHint(int pageNum, int pageSize);

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AdsPublishSmtAdjustOrderResult> wrapper);

}
