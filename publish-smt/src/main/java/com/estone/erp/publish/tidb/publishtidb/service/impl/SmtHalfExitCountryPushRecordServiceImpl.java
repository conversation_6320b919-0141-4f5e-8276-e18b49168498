package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountryPushRecord;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtHalfExitCountryPushRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.service.SmtHalfExitCountryPushRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * smt半托管商品退出部分国家待推送记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
public class SmtHalfExitCountryPushRecordServiceImpl extends ServiceImpl<SmtHalfExitCountryPushRecordMapper, SmtHalfExitCountryPushRecord> implements SmtHalfExitCountryPushRecordService {

}
