package com.estone.erp.publish.smt.enums;

public enum AliexpressCommitStatusEnum {
    SUCCESS(1, "提交成功"),
    FAIL(0, "提交失败"),
    WAIT(2, "待提交");

    private int code;

    private String name;

    AliexpressCommitStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressCommitStatusEnum build(int code) {
        AliexpressCommitStatusEnum[] values = values();
        for (AliexpressCommitStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressCommitStatusEnum[] values = values();
        for (AliexpressCommitStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }

}
