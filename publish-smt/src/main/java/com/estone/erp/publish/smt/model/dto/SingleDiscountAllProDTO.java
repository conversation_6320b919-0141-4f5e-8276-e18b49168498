package com.estone.erp.publish.smt.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-05 14:53
 **/
@NoArgsConstructor
@Data
public class SingleDiscountAllProDTO {


    private AliexpressMarketingStorepromotionProductsQueryResponseDTO aliexpress_marketing_storepromotion_products_query_response;

    @NoArgsConstructor
    @Data
    public static class AliexpressMarketingStorepromotionProductsQueryResponseDTO {
        private Integer total_count;
        private Integer page_no;
        private ProductListDTO product_list;
        private Integer page_size;
        private String request_id;

        @NoArgsConstructor
        @Data
        public static class ProductListDTO {
            private List<Long> number;
        }
    }
}
