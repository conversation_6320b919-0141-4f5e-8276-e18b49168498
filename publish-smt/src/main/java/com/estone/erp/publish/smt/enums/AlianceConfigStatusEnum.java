package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月08日/17:51
 * @Description: <p>联盟自动配置枚举类型</p>
 * @Version: 1.0.0
 * @modified:
 */
public enum AlianceConfigStatusEnum {
    ON(1, "开启自动配置"),
    OFF(0, "关闭自动配置");


    private int code;

    private String name;

    AlianceConfigStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AlianceConfigStatusEnum build(int code) {
        AlianceConfigStatusEnum[] values = values();
        for (AlianceConfigStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AlianceConfigStatusEnum[] values = values();
        for (AlianceConfigStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
