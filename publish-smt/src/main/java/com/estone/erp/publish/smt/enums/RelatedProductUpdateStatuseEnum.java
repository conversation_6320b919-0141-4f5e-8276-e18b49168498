package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/10/1911:58
 */
public enum RelatedProductUpdateStatuseEnum {

    YES(1, "已更新"),

    NO(0, "未更新")
    ;

    private Integer id;
    private String name;

    private RelatedProductUpdateStatuseEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
