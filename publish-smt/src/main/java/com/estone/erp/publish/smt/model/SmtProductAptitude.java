package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtProductAptitude implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_product_aptitude.id
     */
    private Long id;

    /**
     * 店铺 database column smt_product_aptitude.account_number
     */
    private String accountNumber;

    /**
     * 主图 database column smt_product_aptitude.image
     */
    private String image;

    /**
     * 产品ID database column smt_product_aptitude.product_id
     */
    private Long productId;

    /**
     * sku database column smt_product_aptitude.sku
     */
    private String sku;

    /**
     * 标题 database column smt_product_aptitude.title
     */
    private String title;

    /**
     * 问题类型(资质不合规 | UKCA认证) database column smt_product_aptitude.issue_type
     */
    private String issueType;

    /**
     * 影响国家 database column smt_product_aptitude.risk_country
     */
    private String riskCountry;

    /**
     * 商品影响(即将屏蔽) database column smt_product_aptitude.impact_type
     */
    private String impactType;

    /**
     * 处理状态(待整改) database column smt_product_aptitude.handle_status
     */
    private String handleStatus;

    /**
     * 待整改项详情 database column smt_product_aptitude.audit_result_list
     */
    private String auditResultList;

    /**
     * 问题影响详情 database column smt_product_aptitude.issue_details
     */
    private String issueDetails;

    /**
     * 创建时间 database column smt_product_aptitude.create_date
     */
    private Timestamp createDate;

    /**
     * 修改时间 database column smt_product_aptitude.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 爬虫时间 database column smt_product_aptitude.crawl_time
     */
    private Timestamp crawlTime;

    /**
     * 爬虫更新时间 database column smt_product_aptitude.crawl_updated_time
     */
    private Timestamp crawlUpdatedTime;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;

    /**
     * sku在该店铺的7天销量
     */
    private Integer accountOrderNum7d;

    /**
     * sku在该店铺的14天销量
     */
    private Integer accountOrderNum14d;

    /**
     * sku在该店铺的30天销量
     */
    private Integer accountOrderNum30d;

    /**
     * sku在该店铺的60天销量
     */
    private Integer accountOrderNum60d;

    /**
     * sku在该店铺的90天销量
     */
    private Integer accountOrderNum90d;

    /**
     * sku在smt平台的7天销量
     */
    private Integer smtPlatOrderNum7d;

    /**
     * sku在smt平台的14天销量
     */
    private Integer smtPlatOrderNum14d;

    /**
     * sku在smt平台的30天销量
     */
    private Integer smtPlatOrderNum30d;

    /**
     * sku在smt平台的60天销量
     */
    private Integer smtPlatOrderNum60d;

    /**
     * sku在smt平台的90天销量
     */
    private Integer smtPlatOrderNum90d;
}