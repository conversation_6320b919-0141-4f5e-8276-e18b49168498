package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsPublishSmtAdjustOrderResultMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustOrderResult;
import com.estone.erp.publish.tidb.publishtidb.service.IAdsPublishSmtAdjustOrderResultService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class AdsPublishSmtAdjustOrderResultServiceImpl extends ServiceImpl<AdsPublishSmtAdjustOrderResultMapper, AdsPublishSmtAdjustOrderResult> implements IAdsPublishSmtAdjustOrderResultService {

    @Resource
    AdsPublishSmtAdjustOrderResultMapper adsPublishSmtAdjustOrderResultMapper;

    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AdsPublishSmtAdjustOrderResult> wrapper){
        Assert.notNull(wrapper, "wrapper is null!");
        List<Map<Object, Object>> mapList = adsPublishSmtAdjustOrderResultMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }
}
