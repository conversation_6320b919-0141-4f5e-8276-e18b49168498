package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024年11月27日15:42:34
 */
@Getter
@AllArgsConstructor
public enum AdjustPriceConfirmStatusEnum {
    // 确认状态 0:待确认 1:已确认 2:确认不调整
    TO_BE_CONFIRMED(0, "待确认"),
    CONFIRMED(1, "已确认"),
    NOT_ADJUST(2, "确认不调整");

    private final int code;
    private final String desc;

    public static String convert(Integer value) {
        for (AdjustPriceConfirmStatusEnum status : AdjustPriceConfirmStatusEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

    public boolean isTrue(Integer value) {
        if (value == null) return false;
        return this.code == value;
    }

}
