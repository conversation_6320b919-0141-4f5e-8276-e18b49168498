package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressMsgLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_msg_log.id
     */
    private Integer id;

    /**
     * 消息 database column aliexpress_msg_log.message
     */
    private String message;

    /**
     * 创建人 database column aliexpress_msg_log.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_msg_log.create_date
     */
    private Timestamp createDate;
}