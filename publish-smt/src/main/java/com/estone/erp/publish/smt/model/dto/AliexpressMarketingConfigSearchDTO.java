package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AliexpressMarketingConfigSearchDTO implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 规则名称 database column aliexpress_marketing_config.name
     */
    private String name;

    /**
     * 配置类型 1=联盟 database column aliexpress_marketing_config.type
     */
    private List<Integer> types;

    /**
     * 店铺 database column aliexpress_marketing_config.accounts
     */
    private List<String> accounts;


    /**
     * 状态 0 禁用 1 启用 database column aliexpress_marketing_config.status
     */
    private Integer status;


    /**
     * 创建开始时间
     */
    private Date createdStartTime;

    /**
     * 创建结束时间
     */
    private Date createdEndTime;

    /**
     * 创建人 database column aliexpress_marketing_config.created_by
     */
    private String createdBy;


    /**
     * 店铺分组id
     */
    private List<Integer> accountGroupIdList;

}