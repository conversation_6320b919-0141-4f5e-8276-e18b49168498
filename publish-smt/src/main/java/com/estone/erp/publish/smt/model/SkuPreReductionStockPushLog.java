package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SkuPreReductionStockPushLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column sku_pre_reduction_stock_push_log.id
     */
    private Long id;

    /**
     *  database column sku_pre_reduction_stock_push_log.sku
     */
    private String sku;

    /**
     * 可用库存 database column sku_pre_reduction_stock_push_log.usable_stock
     */
    private Integer usableStock;

    /**
     * 待发库存 database column sku_pre_reduction_stock_push_log.pending_stock
     */
    private Integer pendingStock;

    /**
     * 预扣库存 database column sku_pre_reduction_stock_push_log.pre_reduction_stock
     */
    private Integer preReductionStock;

    /**
     * 预扣库存json database column sku_pre_reduction_stock_push_log.pre_reduction_stock_json
     */
    private String preReductionStockJson;

    /**
     * 创建时间 database column sku_pre_reduction_stock_push_log.created_date
     */
    private Timestamp createdDate;
}