package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> smt_tidb_data
 * 2024-01-16 18:23:42
 */
public class SmtTidbDataCriteria extends SmtTidbData {
    private static final long serialVersionUID = 1L;

    public SmtTidbDataExample getExample() {
        SmtTidbDataExample example = new SmtTidbDataExample();
        SmtTidbDataExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (StringUtils.isNotBlank(this.getSkuId())) {
            criteria.andSkuIdEqualTo(this.getSkuId());
        }
        if (StringUtils.isNotBlank(this.getCountryCode())) {
            criteria.andCountryCodeEqualTo(this.getCountryCode());
        }
        if (this.getIsArea() != null) {
            criteria.andIsAreaEqualTo(this.getIsArea());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getTemplateId() != null) {
            criteria.andTemplateIdEqualTo(this.getTemplateId());
        }
        if (this.getUploadState() != null) {
            criteria.andUploadStateEqualTo(this.getUploadState());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        return example;
    }
}