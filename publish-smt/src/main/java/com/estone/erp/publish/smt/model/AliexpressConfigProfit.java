package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressConfigProfit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_config_profit.id
     */
    private Integer id;

    /**
     * 配置主键id database column aliexpress_config_profit.config_id
     */
    private Integer configId;

    /**
     * 店铺 database column aliexpress_config_profit.account
     */
    private String account;

    /**
     * 运费模板id database column aliexpress_config_profit.freight_template_id
     */
    private Long freightTemplateId;

    /**
     * 运费模板name database column aliexpress_config_profit.freight_template_name
     */
    private String freightTemplateName;

    /**
     * 试算物流 database column aliexpress_config_profit.shipping_method
     */
    private String shippingMethod;

    /**
     * 毛利率 database column aliexpress_config_profit.gross_profit_rate
     */
    private Double grossProfitRate;

    /**
     * 区域调价毛利率
     */
    private Double areaGrossProfitRate;

    /**
     * 关联区域算价配置 最小的主键id
     */
    private Integer areaId;

    /**
     * 折扣率 database column aliexpress_config_profit.discount_rate
     */
    private Double discountRate;

    /**
     * 计算国家 database column aliexpress_config_profit.country_code
     */
    private String countryCode;

    /**
     * 创建人 database column aliexpress_config_profit.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_config_profit.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_config_profit.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_config_profit.update_date
     */
    private Timestamp updateDate;
}