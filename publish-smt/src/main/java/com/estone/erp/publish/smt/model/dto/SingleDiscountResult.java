package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-05 14:53
 **/
@Data
public class SingleDiscountResult {
    private Boolean success;
    private String errorMsg;
    private List<Long> productIds = null;

    private Boolean isDelete = false; //是否可以删除

    private List<SingleDiscountProDTO> singleDiscountProDTOS = null;
}
