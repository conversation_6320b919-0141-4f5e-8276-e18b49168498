package com.estone.erp.publish.smt.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.estone.erp.publish.component.converter.SuccessIntegerConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * smt 规则下架队列日志导出
 */
@Data
public class SmtPopRuleOffRecordExcel {

    @ExcelProperty(value = "图片")
    private String img;

    @ExcelProperty(value = "店铺")
    private String account;

    @ExcelProperty(value = "商品id", converter = LongStringConverter.class)
    private Long productId;

    @ExcelProperty(value = "SPU")
    private String spu;

    @ExcelProperty(value = "规则名称")
    private String ruleName;

    @ExcelProperty(value = "24H销量")
    private Integer order24hCount;

    @ExcelProperty(value = "7天销量")
    private Integer orderLast7dCount;

    @ExcelProperty(value = "14天销量")
    private Integer orderLast14dCount;

    @ExcelProperty(value = "30天销量")
    private Integer orderLast30dCount;

    @ExcelProperty(value = "60天销量")
    private Integer orderLast60dCount;

    @ExcelProperty(value = "180天销量")
    private Integer orderLast180dCount;

    @ExcelProperty(value = "7天浏览量")
    private Integer view7dCount;

    @ExcelProperty(value = "14天浏览量")
    private Integer view14dCount;

    @ExcelProperty(value = "30天浏览量")
    private Integer view30dCount;

    @ExcelProperty(value = "7天曝光量")
    private Integer exposure7dCount;

    @ExcelProperty(value = "14天曝光量")
    private Integer exposure14dCount;

    @ExcelProperty(value = "30天曝光量")
    private Integer exposure30dCount;

    @ExcelProperty(value = "下架状态", converter = SuccessIntegerConverter.class)
    private Integer executeState;

    @ExcelProperty(value = "失败备注")
    private String failInfo;

    @ExcelProperty(value = "销售")
    private String salemanager;

    @ExcelProperty(value = "销售组长")
    private String salemanagerLeader;

    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    @ExcelProperty(value = "匹配规则时间", converter = TimestampFormatConverter.class)
    private Timestamp createDate;

    @ExcelProperty(value = "下架时间", converter = TimestampFormatConverter.class)
    private Timestamp offDate;
}
