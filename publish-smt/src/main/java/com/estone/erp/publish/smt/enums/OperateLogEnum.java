package com.estone.erp.publish.smt.enums;

public enum OperateLogEnum {

    UPDATE_ACCOUNT_CONFIG("update_account_config", "修改店铺配置"),
    UPDATE_AUTO_TEMP("update_auto_temp", "修改自动刊登模板"),
    UPDATE_SALE_MUST_PUBLISH("update_sale_must_publish", "修改销售必刊登"),
    UPDATE_LISTING_CONFIG("update_listing_config","修改listing配置" ),
    DELETE_SPU_CATEGORY_FORBID_PUBLISH("delete_spu_category_forbid_publish","删除分类禁刊登" ),
    SYNC_SPU_CATEGORY_FORBID_PUBLISH("sync_spu_category_forbid_publish","违规处罚数据写入SPU-禁止刊登分类列表"),
    DELETE_EARLY_BIRD_PLAN_END_GT30DAY("delete_early_bird_plan_end_gt30day","删除托管已结束，爬取时间超过30天的数据的早鸟活动数据" ),
    AUTO_JOIN_EARLY_BIRD("auto_join_early_bird","自动加入早鸟活动" ),
    POP_PUBLISH("pop_publish","POP定时刊登"),
    PUBLISH_GLOBAL_LIMIT("publish_global_limit","全局刊登限制" ),
    SALE_LEADER_PUBLISH_CONFIG("sale_leader_publish_config", "组长刊登次数配置");

    private String code;

    private String name;

    private OperateLogEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }
}
