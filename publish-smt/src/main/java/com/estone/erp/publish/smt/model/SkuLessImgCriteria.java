package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-12-16 18:01:26
 */
public class SkuLessImgCriteria extends SkuLessImg {
    private static final long serialVersionUID = 1L;

    public SkuLessImgExample getExample() {
        SkuLessImgExample example = new SkuLessImgExample();
        SkuLessImgExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        return example;
    }
}