package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtFreightTemplateExcelImportDto;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtFreightTemplateExcelImportMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateExcelImport;
import com.estone.erp.publish.tidb.publishtidb.service.SmtFreightTemplateExcelImportService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class SmtFreightTemplateExcelImportServiceImpl extends ServiceImpl<SmtFreightTemplateExcelImportMapper, SmtFreightTemplateExcelImport>
    implements SmtFreightTemplateExcelImportService{

    @Resource
    private PermissionsHelper permissionsHelper;

    @Override
    public IPage<SmtFreightTemplateExcelImport> pageQuery(SmtFreightTemplateExcelImportDto dto){
        isAuth(dto);
        LambdaQueryWrapper<SmtFreightTemplateExcelImport> pageQueryWrapper = this.getPageQueryWrapper(dto);
        setSort(dto, pageQueryWrapper);
        Page<SmtFreightTemplateExcelImport> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return this.page(page, pageQueryWrapper);
    }

    private void isAuth(SmtFreightTemplateExcelImportDto dto) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        //查询销售为空
        if (!superAdminOrEquivalent.getResult() && CollectionUtils.isEmpty(dto.getAccountList()) && CollectionUtils.isEmpty(dto.getCreatedByList())) {
            // 获取当前权限下的销售人员
            List<String> currentPermissionEmployeeNo = permissionsHelper.getCurrentUserEmployeeNoPermission(null, null, null, SaleChannel.CHANNEL_SMT);
            dto.setCreatedByList(currentPermissionEmployeeNo);
        }
    }

    private static void setSort(SmtFreightTemplateExcelImportDto dto, LambdaQueryWrapper<SmtFreightTemplateExcelImport> pageQueryWrapper) {
        if (StringUtils.isBlank(dto.getSort())) {
            dto.setSort("createdTime");
        }
        if (dto.getIsAsc() == null) {
            dto.setIsAsc(false);
        }
        // order by
        if ("createdTime".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), SmtFreightTemplateExcelImport::getCreatedTime);
        }
    }
    private LambdaQueryWrapper<SmtFreightTemplateExcelImport> getPageQueryWrapper(SmtFreightTemplateExcelImportDto dto) {
        LambdaQueryWrapper<SmtFreightTemplateExcelImport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getIdList()), SmtFreightTemplateExcelImport::getId, dto.getIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getAccountList()), SmtFreightTemplateExcelImport::getAccount, dto.getAccountList());
        queryWrapper.like(StringUtils.isNotBlank(dto.getTempNameLike()), SmtFreightTemplateExcelImport::getTempName, dto.getTempNameLike());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getExecuteStatusList()), SmtFreightTemplateExcelImport::getExecuteStatus, dto.getExecuteStatusList());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getCreatedByList()), SmtFreightTemplateExcelImport::getCreatedBy, dto.getCreatedByList());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromCreatedTime()), SmtFreightTemplateExcelImport::getCreatedTime, dto.getFromCreatedTime());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToCreatedTime()), SmtFreightTemplateExcelImport::getCreatedTime, dto.getToCreatedTime());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromExecuteTime()), SmtFreightTemplateExcelImport::getExecuteTime, dto.getFromExecuteTime());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToExecuteTime()), SmtFreightTemplateExcelImport::getExecuteTime, dto.getToExecuteTime());
        return queryWrapper;
    }
}




