package com.estone.erp.publish.smt.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.bean.CarInfoTempEntity;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.call.direct.dto.pre.PreItemSubmit;
import com.estone.erp.publish.smt.enums.AliexpressTemplateTableEnum;
import com.estone.erp.publish.smt.enums.TemplateTypeEnum;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@Slf4j
public class AliexpressTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_template.id
     */
    private Integer id;

    /**
     * Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\textarea\style\iframe\frame\input\pre\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签. database column aliexpress_template.detail
     */
    private String detail;

    /**
     * 列表类型，以json格式来表达。参看aeopAeProductSKUs数据结构。特别提示：新增SKU实际可售库存属性ipmSkuStock，该属性值的合理取值范围为0~999999，如该商品有SKU时，请确保至少有一个SKU是有货状态，也就是ipmSkuStock取值是1~999999，在整个商品纬度库存值的取值范围是1~999999。 database column aliexpress_template.aeop_ae_product_skus_json
     */
    private String aeopAeProductSkusJson;

    /**
     * 备货期。取值范围:1-60;单位:天。 database column aliexpress_template.delivery_time
     */
    private Integer deliveryTime;

    /**
     * 服务模板设置。（需和服务模板查询接口api.queryPromiseTemplateById进行关联使用） database column aliexpress_template.promise_template_id
     */
    private Long promiseTemplateId;

    /**
     * 类目名 database column aliexpress_template.category_name
     */
    private String categoryName;

    /**
     * 类目在数据表的id database column aliexpress_template.category_table_id
     */
    private Long categoryTableId;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。 database column aliexpress_template.category_id
     */
    private Integer categoryId;

    /**
     * 	商品标题 长度在1-128之间英文。 database column aliexpress_template.subject
     */
    private String subject;

    /**
     * 商品一口价。取值范围:0-100000,保留两位小数;单位:美元。如:200.07，表示:200美元7分。需要在正确的价格区间内。上传多属性产品的时候，有好几个SKU和价格，productprice无需填写。 database column aliexpress_template.product_price
     */
    private Double productPrice;

    /**
     * 运费模版ID。通过运费接口listFreightTemplate获取。 database column aliexpress_template.freight_template_id
     */
    private Long freightTemplateId;

    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。 database column aliexpress_template.image_urls
     */
    private String imageUrls;

    /**
     * 商品单位 (存储单位编号) 100000000:袋 (bag/bags) 100000001:桶 (barrel/barrels) 100000002:蒲式耳 (bushel/bushels) 100078580:箱 (carton) 100078581:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_template.product_unit
     */
    private Integer productUnit;

    /**
     * 打包销售: true 非打包销售:false database column aliexpress_template.package_type
     */
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_template.lot_num
     */
    private Integer lotNum;

    /**
     * 商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。 database column aliexpress_template.package_length
     */
    private Integer packageLength;

    /**
     * 商品包装宽度。取值范围:1-700,单位:厘米。 database column aliexpress_template.package_width
     */
    private Integer packageWidth;

    /**
     * 商品包装高度。取值范围:1-700,单位:厘米。 database column aliexpress_template.package_height
     */
    private Integer packageHeight;

    /**
     * 商品毛重,取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_template.gross_weight
     */
    private String grossWeight;

    /**
     * 是否自定义计重.true为自定义计重,false反之. database column aliexpress_template.is_pack_sell
     */
    private Boolean isPackSell;

    /**
     * 是否自定义计重 database column aliexpress_template.is_wholesale
     */
    private Boolean isWholesale;

    /**
     * isPackSell为true时,此项必填。购买几件以内不增加运费。取值范围1-1000 database column aliexpress_template.base_unit
     */
    private Integer baseUnit;

    /**
     * isPackSell为true时,此项必填。 每增加件数.取值范围1-1000。 database column aliexpress_template.add_unit
     */
    private Integer addUnit;

    /**
     * isPackSell为true时,此项必填。 对应增加的重量.取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_template.add_weight
     */
    private String addWeight;

    /**
     * 商品有效天数。取值范围:1-30,单位:天。 database column aliexpress_template.ws_valid_num
     */
    private Integer wsValidNum;

    /**
     * 产品属性，以json格式进行封装后提交。参看aeopAeProductPropertys数据结构。此字段是否必填，需从类目接口getChildAttributesResultByPostCateIdAndPath获取（即获取到的required来判断属性是否必填），该项只输入普通类目属性数据，不可输入sku类目属性。 对于类目属性包含子类目属性的情况，此处不确认父属性和子属性，即选择任何属性，都以该对象提交。 对于一个属性多个选中值的情况，以多个该对象存放。 其中"attrNameId","attrValueId"为整型(Integer), "attrName", "attrValue"为字符串类型(String)。 1. 当设置一些系统属性时，如果类目自定义了一些候选值，只需要提供"attrNameId"和"attrValueId"即可。例如：{"attrNameId":494, "attrValueId":284}。 2. 当设置一些需要手工输入属性内容时，只需要提供"attrNameId"和"attrValue"即可。例如：{"attrNameId": 1000, "attrValue": "test"} 3. 当设置自定义属性时，需要提供"attrName"和"attrValue"即可。例如: {"attrName": "Color", "attrValue": "red"} 4. 当设置一个Other属性时，需要提供"attrNameId", "attrValueId", "attrValue"三个参数。例如：{"attrNameId": 1000, "attrValueId": 4, "attrValue": "Other Value"}。 database column aliexpress_template.aeop_ae_product_propertys_json
     */
    private String aeopAeProductPropertysJson;

    /**
     * 批发最小数量 。取值范围2-100000。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_template.bulk_order
     */
    private Integer bulkOrder;

    /**
     * 批发折扣。扩大100倍，存整数。取值范围:1-99。注意：这是折扣，不是打折率。 如,打68折,则存32。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_template.bulk_discount
     */
    private Integer bulkDiscount;

    /**
     * 尺码表模版ID。必须选择当前类目下的尺码模版。 database column aliexpress_template.size_chart_id
     */
    private Long sizeChartId;

    /**
     * 新尺码表多个英文逗号拼接 database column aliexpress_template.size_chart_id_list
     */
    private String sizeChartIdList;

    /**
     * 库存扣减策略，总共有2种：下单减库存(place_order_withhold)和支付减库存(payment_success_deduct)。 database column aliexpress_template.reduce_strategy
     */
    private String reduceStrategy;

    /**
     * 这个产品需要关联的产品分组ID. 只能关联一个产品分组，如果想关联多个产品分组，请使用api.setGroups接口。 database column aliexpress_template.group_id
     */
    private Long groupId;

    /**
     * 货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。 database column aliexpress_template.currency_code
     */
    private String currencyCode;

    /**
     * mobile Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\textarea\style\iframe\frame\input\pre\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签. database column aliexpress_template.mobile_detail
     */
    private String mobileDetail;

    /**
     * 卡券商品开始有效期 database column aliexpress_template.coupon_start_date
     */
    private Timestamp couponStartDate;

    /**
     * 卡券商品结束有效期 database column aliexpress_template.coupon_end_date
     */
    private Timestamp couponEndDate;

    /**
     * 商品分国家报价的配置 database column aliexpress_template.aeop_national_quote_configuration
     */
    private String aeopNationalQuoteConfiguration;

    /**
     * 商品多媒体信息，该属性主要包含商品的视频列表 database column aliexpress_template.aeop_ae_multimedia
     */
    private String aeopAeMultimedia;

    /**
     * 系统单品货号 database column aliexpress_template.article_number
     */
    private String articleNumber;

    /**
     * 创建者 database column aliexpress_template.creator
     */
    private String creator;

    /**
     * 创建时间 database column aliexpress_template.create_time
     */
    private Timestamp createTime;

    /**
     * 上次编辑时间 database column aliexpress_template.last_edit_time
     */
    private Timestamp lastEditTime;

    /**
     * 速卖通帐号 database column aliexpress_template.aliexpress_account_number
     */
    private String aliexpressAccountNumber;

    /**
     * 模版展示图 database column aliexpress_template.display_image_url
     */
    private String displayImageUrl;

    /**
     * 商品编码 database column aliexpress_template.product_code
     */
    private String productCode;

    /**
     * 商品库存 database column aliexpress_template.product_stock
     */
    private Integer productStock;

    /**
     * 刊登时间 database column aliexpress_template.post_time
     */
    private Timestamp postTime;

    /**
     * 毛利率 database column aliexpress_template.margin
     */
    private Double margin;

    /**
     * 物流方式代码 database column aliexpress_template.shipping_method_code
     */
    private String shippingMethodCode;

    /**
     * 将模版分享给其他销售 database column aliexpress_template.share_user
     */
    private String shareUser;

    /**
     * 是否公开 database column aliexpress_template.is_public
     */
    private Boolean isPublic;

    /**
     * 是否范本 database column aliexpress_template.is_parent
     */
    private Boolean isParent;

    /**
     * 状态 database column aliexpress_template.template_status
     */
    private Integer templateStatus;

    /**
     * 类型 database column aliexpress_template.template_type
     */
    private Integer templateType;

    /**
     * 模板标签 database column aliexpress_template.template_label
     */
    private String templateLabel;

    private Boolean isTiming;

    /**
     * 修改标签可选项
     */
    private List<String> labelList = new ArrayList<>();

    /**
     * 复制范本的id
     */
    private Integer parentId;


    private Long productId;

    private List<String> skuList = new ArrayList<>();

    /**
     * 侵权词
     */
    private String infringementWordStr;

    /**
     * 禁售平台
     */
    private String forbidChannelStr;

    /**
     * 图片池
     */
    private List<String> images = new ArrayList<>();

    /**
     * 存放刊登成功的产品id
     */
    private Long successProductId;

    /**
     * 日志id
     */
    private Long logId;

    /**
     * 刊登失败错误记录
     */
    private String errorMsg;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 车型库json 用于页面回写 （改版 存储数据库主键 2020年6月17日17）
     */
    private String carTypeJson;

    /**
     * 车型库编辑数据(用于前端回写数据)
     */
    private String editData;

    /**
     * 营销图json格式
     */
    private String marketImagesJson;

    /**
     * 标题规则
     */
    private String titleRule;


    private Integer publishRole;

    /**
     * 经营大类
     */
    private Integer rootCategory;
    private String rootCategoryZhName;

    /**
     * 营销图列表
     */
    private List<MarketImage> marketImages;

    /**
     * 车型库 编辑修改 转换
     */
    private List<CarInfoTempEntity> infoList = new ArrayList<>();

    /**
     * 类目详情
     */
    private AliexpressCategory aliexpressCategory;

    /**
     * 是否产品库 刊登 ，是的话，就不拦截 sku状态
     */
    private Boolean isProductSource;


    //审核状态
    private String examineState;

    private Timestamp examineDate;

    private String examineSaleman;

    //视频链接
    private String videoLink;

    //是否使用了类目预测
    private Boolean isUseCategoryForecast;

    /**
     * 查询那张表 非数据库字段
     */
    private String table;

    /**
     * 产品类型：1 管理单品 2 组合套装
     */
    private Integer productType;

    /**
     * 推荐类目
     */
    private Integer recommendCategoryId;
    private String recommendCategoryPath;

    /**
     * 单品状态
     */
    private String skuStatus;

    /**
     * 文案类型
     */
    private Integer wenAnType;

    /**
     * 标示
     */
    private Boolean sign;

    /**
     * 多语言描述
     */
    private String interSubjects;

    /**
     * 资质信息Json
     */
    private String aeopQualificationStructJson;

    /**
     * 模板类型 1.pop模板 2.半托管 database column aliexpress_template.publish_type
     */
    private Integer publishType;

    /**
     * 关联的pop模板id database column aliexpress_template.pop_temp_id
     */
    private Integer popTempId;

    /**
     * 关联的半托管模板id database column aliexpress_template.half_temp_id
     */
    private Long halfTempId;

    /**
     * 关联的草稿id database column aliexpress_template.draft_id
     */
    private String draftId;

    //半托管模板信息
    private PreItemSubmit preItemSubmit;

    /**
     * 半托管模板 接收数据
     */
    private List<String> joinedCountryList;

    private String productSkuListJsonStr;

    private Boolean isPopToSKU;

    /**
     * 制造商id
     */
    private Long manufactureId;

    /**
     * 制造商name
     */
    private String manufactureName;

    /**
     * 区域调价折扣率
     */
    private Double areaDiscountRate;

    /**
     * 新品推荐主键id
     */
    private String newProductId;

    /**
     * 问题分类
     */
    private String problemType;

    /**
     * 税费类型 4pl 3pl
     */
    private String taxType;

    /**
     * 海关编码
     */
    private String hacodeJson;


    //获取模板所有sku信息
    @JSONField(serialize = false)
    public List<String> getSkuList() {
        Set<String> set = new HashSet<>();
        try{
            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, this.getAliexpressAccountNumber());

            String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();

            JSONArray jsonArray = JSON.parseArray(aeopAeProductSkusJson);
            for(int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String sku_code = jsonObject.getString("sku_code");
                if(StringUtils.isNotBlank(sku_code)){
                    //如果sku_code是GT-开头就先取掉
                    if(StringUtils.startsWith(sku_code, "GT-")){
                        sku_code = sku_code.substring(3);
                    }
                    if(StringUtils.isNotBlank(account.getSellerSkuPrefix()) && sku_code.startsWith(account.getSellerSkuPrefix())){
                        sku_code = sku_code.replaceFirst(account.getSellerSkuPrefix(), "");
                    }
                    set.add(sku_code);
                }
            }

            if(set.isEmpty()){
                set.add(this.getArticleNumber());
            }
            return new ArrayList<>(set);

        }catch (Exception e){
            //忽略报错
            //log.error(e.getMessage(), e);
        }
        return new ArrayList<>(set);
    }

    //重复刊登 校验模板使用
    @JSONField(serialize = false)
    public List<String> getCheckSkuList() {
        Set<String> set = new HashSet<>();
        set.add(this.getArticleNumber());
        try{
            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, this.getAliexpressAccountNumber());

            String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();

            JSONArray jsonArray = JSON.parseArray(aeopAeProductSkusJson);
            for(int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String sku_code = jsonObject.getString("sku_code");
                if(StringUtils.isNotBlank(sku_code)){
                    if(StringUtils.isNotBlank(account.getSellerSkuPrefix()) && sku_code.startsWith(account.getSellerSkuPrefix())){
                        sku_code = sku_code.replaceFirst(account.getSellerSkuPrefix(), "");
                    }
                    set.add(sku_code);
                }
            }
            return new ArrayList<>(set);

        }catch (Exception e){
            //忽略报错
            //log.error(e.getMessage(), e);
        }
        return new ArrayList<>(set);
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public String getArticleNumber() {
        return StrUtil.strTrimToUpperCase(articleNumber);
    }

    public List<MarketImage> getMarketImages() {

        if(CollectionUtils.isNotEmpty(marketImages)){
            return this.marketImages;
        }

        List<MarketImage> marketImages = new ArrayList<>();
        if(StringUtils.isNotBlank(marketImagesJson)) {
            try {
                marketImages = JSON.parseObject(marketImagesJson, new TypeReference<List<MarketImage>>() {
                });
            }catch (Exception e) {
                log.error("解析营销图json出错" + e.getMessage(), e);
            }
        }

        return marketImages;
    }

    /**
     * 获取模板所有的图片url
     * @return
     */
    @JSONField(serialize = false)
    public List<String> getUrlList() {
        Set urlSet = new HashSet();
        String imageUrls = this.getImageUrls();//;分割
        List<String> strings = CommonUtils.splitList(imageUrls, ";");
        urlSet.addAll(strings);

        String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();
        String detail = this.getDetail();
        String mobileDetail = this.getMobileDetail();

        String aa = aeopAeProductSkusJson + " " + detail + " " + mobileDetail;
        Integer templateType = this.getTemplateType();
        Matcher m = Pattern.compile("http://(.*?\")").matcher(aa);
        if(null != templateType && TemplateTypeEnum.SP_PUBLISH.intCode() == templateType) {
            m = Pattern.compile("https://(.*?\")").matcher(aa);
        }
        while (m.find()) {
            String imgUrl = m.group();
            imgUrl = imgUrl.substring(0, imgUrl.length() - 1);
            urlSet.add(imgUrl);
        }
        List<MarketImage> marketImages = this.getMarketImages();
        if(CollectionUtils.isNotEmpty(marketImages)){
            for (MarketImage marketImage : marketImages) {
                String url = marketImage.getUrl();
                urlSet.add(url);
            }
        }
        return new ArrayList<>(urlSet);
    }

    /**
     * 初始化查询那张表
     */
    public void initTable() {
        if(StringUtils.isBlank(this.table)) {
            String table = null;
            if(BooleanUtils.isTrue(isParent)) {
                table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode();
            } else {
                table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE.getCode();
            }
            this.table = table;
        }
    }
}