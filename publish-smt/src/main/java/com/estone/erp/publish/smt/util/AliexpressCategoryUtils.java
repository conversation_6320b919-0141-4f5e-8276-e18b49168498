package com.estone.erp.publish.smt.util;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/1310:09
 */
public class AliexpressCategoryUtils {


    public static String parseCategoryAttributes(String response) {
        if (StringUtils.isNotBlank(response)) {
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(response);
                if (jsonObject.containsKey(
                        "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response")) {
                    com.alibaba.fastjson.JSONObject rspObj = jsonObject.getJSONObject(
                            "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response");
                    if (rspObj.containsKey("result")) {
                        com.alibaba.fastjson.JSONObject resultObj = rspObj.getJSONObject("result");
                        if (resultObj.containsKey("attributes")) {
                            com.alibaba.fastjson.JSONObject attributesObj = resultObj.getJSONObject("attributes");
                            if (attributesObj.containsKey("aeop_attribute_dto")) {
                                com.alibaba.fastjson.JSONArray attributeDtoArr = attributesObj.getJSONArray("aeop_attribute_dto");
                                if (attributeDtoArr != null) {
                                    com.alibaba.fastjson.JSONArray cateAttrArr = new com.alibaba.fastjson.JSONArray();
                                    int size = attributeDtoArr.size();
                                    for (int i = 0; i < size; i++) {
                                        com.alibaba.fastjson.JSONObject attrDto = attributeDtoArr.getJSONObject(i);
                                        // 按照原来旧平台接口的数据结构重新封装valus
                                        if (attrDto.containsKey("values")) {
                                            com.alibaba.fastjson.JSONObject valuesObj = attrDto.getJSONObject("values");
                                            com.alibaba.fastjson.JSONArray cateAttrValArr = new com.alibaba.fastjson.JSONArray();
                                            if (valuesObj.containsKey("aeop_attr_value_dto")) {
                                                com.alibaba.fastjson.JSONArray valueDtoArr = valuesObj.getJSONArray("aeop_attr_value_dto");
                                                if (valueDtoArr != null) {
                                                    int size2 = valueDtoArr.size();
                                                    for (int j = 0; j < size2; j++) {
                                                        com.alibaba.fastjson.JSONObject valueDto = valueDtoArr.getJSONObject(j);
                                                        com.alibaba.fastjson.JSONObject cateAttrValObj = new com.alibaba.fastjson.JSONObject();
                                                        if (valueDto.containsKey("id")) {
                                                            cateAttrValObj.put("id", valueDto.getLongValue("id"));
                                                        }
                                                        if (valueDto.containsKey("names")) {
                                                            com.alibaba.fastjson.JSONObject namesObj = valueDto.getJSONObject("names");
                                                            cateAttrValObj.put("names", namesObj);
                                                        }
                                                        if (valueDto.containsKey("value_tags")) {
                                                            com.alibaba.fastjson.JSONObject valueTags = valueDto.getJSONObject("value_tags");
                                                            cateAttrValObj.put("value_tags", valueTags);
                                                        }
                                                        if (valueDto.containsKey("has_sub_attr")){
                                                            Boolean has_sub_attr = valueDto.getBoolean("has_sub_attr");
                                                            cateAttrValObj.put("has_sub_attr", has_sub_attr);
                                                        }
                                                        cateAttrValArr.add(cateAttrValObj);
                                                    }
                                                    attrDto.put("values", cateAttrValArr);
                                                }
                                            }
                                        }
                                        if (attrDto.containsKey("units")) {
                                            com.alibaba.fastjson.JSONObject unitsObj = attrDto.getJSONObject("units");
                                            if (unitsObj.containsKey("aeop_unit")) {
                                                attrDto.put("units", unitsObj.getJSONArray("aeop_unit"));
                                            }
                                        }
                                        if (attrDto.containsKey("names")) {
                                            attrDto.put("names", attrDto.getJSONObject("names"));
                                        }
                                        cateAttrArr.add(attrDto);
                                    }
                                    com.alibaba.fastjson.JSONObject cateAttrObj = new com.alibaba.fastjson.JSONObject();
                                    cateAttrObj.put("attributes", cateAttrArr);
                                    return cateAttrObj.toJSONString();
                                }
                            }
                        }
                    }
                }
        }
        return null;
    }


    public static void parseCarCategoryAttributes(String response) {

        if (StringUtils.isNotBlank(response)) {
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(response);
            if (jsonObject.containsKey("response")) {
                com.alibaba.fastjson.JSONObject callRspObj = jsonObject.getJSONObject("response");
                if (callRspObj.containsKey(
                        "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response")) {
                    com.alibaba.fastjson.JSONObject rspObj = callRspObj.getJSONObject(
                            "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response");
                    if (rspObj.containsKey("result")) {
                        com.alibaba.fastjson.JSONObject resultObj = rspObj.getJSONObject("result");
                        if (resultObj.containsKey("attributes")) {
                            com.alibaba.fastjson.JSONObject attributesObj = resultObj.getJSONObject("attributes");
                            if (attributesObj.containsKey("aeop_attribute_dto")) {
                                com.alibaba.fastjson.JSONArray attributeDtoArr = attributesObj.getJSONArray("aeop_attribute_dto");
                                if (attributeDtoArr != null) {
                                    com.alibaba.fastjson.JSONArray cateAttrArr = new com.alibaba.fastjson.JSONArray();
                                    int size = attributeDtoArr.size();
                                    for (int i = 0; i < size; i++) {
                                        com.alibaba.fastjson.JSONObject attrDto = attributeDtoArr.getJSONObject(i);


                                        // 按照原来旧平台接口的数据结构重新封装valus
                                        if (attrDto.containsKey("values")) {
                                            com.alibaba.fastjson.JSONObject valuesObj = attrDto.getJSONObject("values");
                                            com.alibaba.fastjson.JSONArray cateAttrValArr = new com.alibaba.fastjson.JSONArray();
                                            if (valuesObj.containsKey("aeop_attr_value_dto")) {
                                                com.alibaba.fastjson.JSONArray valueDtoArr = valuesObj.getJSONArray("aeop_attr_value_dto");
                                                if (valueDtoArr != null) {
                                                    int size2 = valueDtoArr.size();
                                                    for (int j = 0; j < size2; j++) {
                                                        com.alibaba.fastjson.JSONObject valueDto = valueDtoArr.getJSONObject(j);
                                                        com.alibaba.fastjson.JSONObject cateAttrValObj = new com.alibaba.fastjson.JSONObject();
                                                        if (valueDto.containsKey("id")) {
                                                            cateAttrValObj.put("id", valueDto.getIntValue("id"));
                                                        }
                                                        if (valueDto.containsKey("names")) {
                                                            com.alibaba.fastjson.JSONObject namesObj = valueDto.getJSONObject("names");
                                                            cateAttrValObj.put("names", namesObj);
                                                        }
                                                        if (valueDto.containsKey("value_tags")) {
                                                            com.alibaba.fastjson.JSONObject valueTags = valueDto.getJSONObject("value_tags");
                                                            cateAttrValObj.put("value_tags", valueTags);
                                                        }
                                                        cateAttrValArr.add(cateAttrValObj);
                                                    }
                                                    attrDto.put("values", cateAttrValArr);
                                                }
                                            }
                                        }
                                        if (attrDto.containsKey("units")) {
                                            com.alibaba.fastjson.JSONObject unitsObj = attrDto.getJSONObject("units");
                                            if (unitsObj.containsKey("aeop_unit")) {
                                                attrDto.put("units", unitsObj.getJSONArray("aeop_unit"));
                                            }
                                        }
                                        if (attrDto.containsKey("names")) {
                                            attrDto.put("names", attrDto.getJSONObject("names"));
                                        }
                                        cateAttrArr.add(attrDto);
                                    }
                                    com.alibaba.fastjson.JSONObject cateAttrObj = new com.alibaba.fastjson.JSONObject();
                                    cateAttrObj.put("attributes", cateAttrArr);
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}
