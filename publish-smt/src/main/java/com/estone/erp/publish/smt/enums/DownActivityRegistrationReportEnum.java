package com.estone.erp.publish.smt.enums;

public enum DownActivityRegistrationReportEnum {
    activityName("活动名称", "activityName"),
    activityId("活动ID", "activityId"),
    activityTypeText("活动类型", "activityTypeText"),
    activityCountries("活动面向国家", "activityCountries"),
    recruitmentStartTime("招商开始时间", "recruitmentStartTime"),
    recruitmentEndTime("招商结束时间", "recruitmentEndTime"),
    displayStartTime("展示开始时间", "displayStartTime"),
    displayEndTime("展示结束时间", "displayEndTime"),
    accountNumbers("采集店铺数", "accountNumbers"),
    validNum("有效生成活动数", "validNum"),
    confirmedEntryNum("已确认报名数", "confirmedEntryNum"),
    successEntryNum("成功报名数", "successEntryNum"),
    uploadSuccessRateText("上传成功率", "uploadSuccessRateText"),
    totalProductNum("总产品数", "totalProductNum"),
    canEntryProductNum("可报名产品数", "canEntryProductNum"),
    realEntryProductNum("实际报名产品数", "realEntryProductNum"),
    reportDate("统计日期", "reportDate"),
    updateTime("更新时间", "updateTime"),
    ;

    private String name;
    private String excelValue;

    private DownActivityRegistrationReportEnum(String name, String excelValue) {
        this.name = name;
        this.excelValue = excelValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExcelValue() {
        return excelValue;
    }

    public void setExcelValue(String excelValue) {
        this.excelValue = excelValue;
    }

}
