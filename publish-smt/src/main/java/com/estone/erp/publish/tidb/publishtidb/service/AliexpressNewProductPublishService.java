package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressNewProductPublishDo;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressNewProductPublishVo;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublish;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【aliexpress_new_product_publish(Smt 刊登次数达成率)】的数据库操作Service
* @createDate 2025-03-13 09:40:16
*/
public interface AliexpressNewProductPublishService extends IService<AliexpressNewProductPublish> {

    CQueryResult<AliexpressNewProductPublishVo> queryPage(CQuery<AliexpressNewProductPublishDo> query);

    IPage<AliexpressNewProductPublishVo> page(Page<AliexpressNewProductPublish> page, AliexpressNewProductPublishDo search);

    ApiResult<String> download(CQuery<AliexpressNewProductPublishDo> query);

    List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<AliexpressNewProductPublish> wrapper);

    void saveEmptyPublish(List<SmtNewProductRecommendation> smtNewProductRecommendations, Set<String> strings);
}
