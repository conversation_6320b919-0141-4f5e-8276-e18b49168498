package com.estone.erp.publish.smt.model;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.smt.model.dto.AliexpressOnlineGlobalConfigParam;
import com.estone.erp.publish.smt.model.dto.AliexpressSpuDaysAllowPublishDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AliexpressOnlineGlobalConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 设置类型，0-spu按天数限制类型，1-spu允许刊登最大类型
     */
    private Integer setType;

    /**
     * 状态0-禁用，1-启用
     */
    private Integer status;

    /**
     * SPU只允许刊登链接数
     */
    private Integer spuAllowPublishNum;

    /**
     * SPU按天数限制刊登JSON
     */
    private String spuDaysAllowPublishJson;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Timestamp updatedTime;


    public AliexpressOnlineGlobalConfigParam toParam(AliexpressOnlineGlobalConfig config) {
        if (config == null) {
            return null;
        }
        AliexpressOnlineGlobalConfigParam aliexpressOnlineGlobalConfigParam = new AliexpressOnlineGlobalConfigParam();
        aliexpressOnlineGlobalConfigParam.setId(config.getId());
        aliexpressOnlineGlobalConfigParam.setSetType(config.getSetType());
        aliexpressOnlineGlobalConfigParam.setStatus(config.getStatus());
        aliexpressOnlineGlobalConfigParam.setSpuAllowPublishNum(config.getSpuAllowPublishNum());
        if (StringUtils.isNotBlank(config.getSpuDaysAllowPublishJson())){
            aliexpressOnlineGlobalConfigParam.setSpuDaysAllowPublish(JSON.parseArray(config.getSpuDaysAllowPublishJson(), AliexpressSpuDaysAllowPublishDto.class));
        }

        return aliexpressOnlineGlobalConfigParam;
    }
}