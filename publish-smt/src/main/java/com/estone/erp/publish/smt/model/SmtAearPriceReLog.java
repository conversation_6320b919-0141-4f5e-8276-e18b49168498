package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtAearPriceReLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 店铺
     */
    private String account;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 请求信息
     */
    private String requestJson;

    /**
     * 重试次数
     */
    private Integer reCount;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 修改时间
     */
    private Timestamp updateDate;

    /**
     * 操作结果 1 成功 0 失败 2.失败但是不重试
     */
    private Integer resultType;
}