package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.SmtPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtPublishOperationLogMapper;
import com.estone.erp.publish.tidb.publishtidb.service.SmtPublishOperationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * smt发布操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class SmtPublishOperationLogServiceImpl extends ServiceImpl<SmtPublishOperationLogMapper, SmtPublishOperationLog> implements SmtPublishOperationLogService {

}
