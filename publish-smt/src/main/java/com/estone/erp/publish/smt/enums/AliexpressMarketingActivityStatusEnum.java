package com.estone.erp.publish.smt.enums;

public enum AliexpressMarketingActivityStatusEnum {
    NOT_START(1, "未开始"),
    USING(2, "生效中"),
    STOP(3, "已暂停"),
    <PERSON><PERSON>(4, "已结束"),
    Finish(5, "已关闭");

    private int code;

    private String name;

    AliexpressMarketingActivityStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressMarketingActivityStatusEnum build(int code) {
        AliexpressMarketingActivityStatusEnum[] values = values();
        for (AliexpressMarketingActivityStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressMarketingActivityStatusEnum[] values = values();
        for (AliexpressMarketingActivityStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
