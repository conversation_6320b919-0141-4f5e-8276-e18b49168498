package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProDTO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-06 10:29:06
 */
public interface SmtSingleDiscountProductService {
    int countByExample(SmtSingleDiscountProductExample example);

    CQueryResult<SmtSingleDiscountProduct> search(CQuery<SmtSingleDiscountProductCriteria> cquery);

    List<SmtSingleDiscountProduct> selectByExample(SmtSingleDiscountProductExample example);

    SmtSingleDiscountProduct selectByPrimaryKey(Long id);

    int insert(SmtSingleDiscountProduct record);

    int updateByPrimaryKeySelective(SmtSingleDiscountProduct record);

    int updateByExampleSelective(SmtSingleDiscountProduct record, SmtSingleDiscountProductExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int delete(List<Long> needDelList, Long localSingleDiscountId);

    int batchInsert(List<SmtSingleDiscountProduct> singleDiscountProductList);

    void batchUpdate(List<SingleDiscountProDTO> needUpdateList);

    void deleteBySingleDiscount(Long singleDiscountId, List<Long> productIdList);
}