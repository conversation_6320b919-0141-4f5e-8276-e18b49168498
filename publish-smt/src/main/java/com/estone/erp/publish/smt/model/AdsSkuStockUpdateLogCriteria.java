package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> ads_sku_stock_update_log
 * 2023-11-11 14:05:55
 */
public class AdsSkuStockUpdateLogCriteria extends AdsSkuStockUpdateLog {
    private static final long serialVersionUID = 1L;

    public AdsSkuStockUpdateLogExample getExample() {
        AdsSkuStockUpdateLogExample example = new AdsSkuStockUpdateLogExample();
        AdsSkuStockUpdateLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (this.getOriginalQuantity() != null) {
            criteria.andOriginalQuantityEqualTo(this.getOriginalQuantity());
        }
        if (this.getQuantityUpdated() != null) {
            criteria.andQuantityUpdatedEqualTo(this.getQuantityUpdated());
        }
        if (StringUtils.isNotBlank(this.getContent())) {
            criteria.andContentEqualTo(this.getContent());
        }
        if (this.getBeforeValueFrozen() != null) {
            criteria.andBeforeValueFrozenEqualTo(this.getBeforeValueFrozen());
        }
        if (this.getAfterValueFrozen() != null) {
            criteria.andAfterValueFrozenEqualTo(this.getAfterValueFrozen());
        }
        if (this.getCreatedDate() != null) {
            criteria.andCreatedDateEqualTo(this.getCreatedDate());
        }
        if (this.getCreatedby() != null) {
            criteria.andCreatedbyEqualTo(this.getCreatedby());
        }
        return example;
    }
}