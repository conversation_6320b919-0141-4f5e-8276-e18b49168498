package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import lombok.Data;

@Data
public class AliexpressEsTgExtend implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_es_tg_extend.extend_id
     */
    private Long extendId;

    /**
     * 账号 database column aliexpress_es_tg_extend.aliexpress_account_number
     */
    private String aliexpressAccountNumber;

    /**
     * 账号id database column aliexpress_es_tg_extend.owner_member_id
     */
    private String ownerMemberId;

    /**
     * 产品id database column aliexpress_es_tg_extend.product_id
     */
    private Long productId;

    /**
     *  database column aliexpress_es_tg_extend.aeop_ae_product_skus_json
     */
    private String aeopAeProductSkusJson;

    /**
     * 商品多媒体信息，该属性主要包含商品的视频列表 database column aliexpress_es_tg_extend.aeop_ae_multimedia
     */
    private String aeopAeMultimedia;

    /**
     *  database column aliexpress_es_tg_extend.aeop_ae_product_propertys_json
     */
    private String aeopAeProductPropertysJson;

    /**
     * Detail详情 database column aliexpress_es_tg_extend.detail
     */
    private String detail;

    /**
     * mobile Detail详情 database column aliexpress_es_tg_extend.mobile_detail
     */
    private String mobileDetail;

    /**
     * 资质信息 database column aliexpress_es_tg_extend.aeop_qualification_struct_list
     */
    private String aeopQualificationStructList;

    /**
     * 方图 database column aliexpress_es_tg_extend.square_img
     */
    private String squareImg;

    /**
     * 长图 database column aliexpress_es_tg_extend.long_img
     */
    private String longImg;

    /**
     * 属性(预留) database column aliexpress_es_tg_extend.attribute1
     */
    private String attribute1;

    /**
     * 属性 database column aliexpress_es_tg_extend.attribute2
     */
    private String attribute2;

    /**
     * 属性 database column aliexpress_es_tg_extend.attribute3
     */
    private String attribute3;

    /**
     * 属性 database column aliexpress_es_tg_extend.attribute4
     */
    private String attribute4;

    /**
     * 属性 database column aliexpress_es_tg_extend.attribute5
     */
    private String attribute5;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;
}