package com.estone.erp.publish.smt.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class AliexpressEditProductBean implements Serializable {

    private static final long serialVersionUID = 1L;

    private String accountNum;

    private String productId;

    private String skuCode;

    private String skuId;

    private String priceType;

    private Map<String, Double> countryPriceMap;

    private Long templateId;

    private Double skuPrice;

    private Boolean isTidb;

    /**
     * tidb 唯一id
     */
    private String uniqueId;

    private String ruleName;

    /**
     * 是否pop规则调整，如果是需要会写结果
     */
    private Boolean isPopRule;

    private String batchId;
}


