package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtAdminTempAttrRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * admin模板id
     */
    private Integer adminTempId;

    /**
     * spu
     */
    private String spu;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * 完整的中文名称
     */
    private String fullCnName;

    /**
     * 操作结果 0 缺失属性 2.修复基础数据
     */
    private Integer resultType;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 错误信息
     */
    private String failInfo;
}