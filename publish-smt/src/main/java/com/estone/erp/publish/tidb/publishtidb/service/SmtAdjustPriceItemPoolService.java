package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.bean.AdjustPrice.SmtAdjustPriceRuleConfig;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceConfirmDto;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceItemPoolQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtAdjustPriceItemPoolVO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtAdjustPriceItemPool;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 *
 */
public interface SmtAdjustPriceItemPoolService extends IService<SmtAdjustPriceItemPool> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    CQueryResult<SmtAdjustPriceItemPoolVO> queryPage(CQuery<SmtAdjustPriceItemPoolQueryDto> query);


    /**
     * 导出分页查询
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<SmtAdjustPriceItemPool> pageList(SmtAdjustPriceItemPoolQueryDto query, int pageNo, int pageSize);


    /**
     * 过滤调价记录，过滤已存在调价或调折扣价记录的记录
     *
     * @param productIdList
     * @param accountList
     * @param smtAdjustPriceRuleConfig
     * @returns
     */
    Set<Long> filterAdjustPriceRecordLimitedTime(List<Long> productIdList, List<String> accountList, SmtAdjustPriceRuleConfig smtAdjustPriceRuleConfig);


    /**
     * 确认调价
     *
     * @param confirmDO
     */
    ApiResult<String> confirm(SmtAdjustPriceConfirmDto confirmDO);

}
