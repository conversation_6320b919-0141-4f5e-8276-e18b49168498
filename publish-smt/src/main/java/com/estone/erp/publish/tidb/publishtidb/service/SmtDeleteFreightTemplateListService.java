package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.model.SmtDeleteFreightTemplateList;
import com.estone.erp.publish.tidb.publishtidb.model.SmtDeleteFreightTemplateListImportVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【smt_delete_freight_template_list(SMT删除运费模板)】的数据库操作Service
* @createDate 2025-06-13 11:31:22
*/
public interface SmtDeleteFreightTemplateListService extends IService<SmtDeleteFreightTemplateList> {

    /**
     * 批量导入删除运费模板任务
     * @param importList 导入数据
     * @param currentUser 当前操作人
     * @return 处理结果
     */
    ApiResult<String> batchImport(List<SmtDeleteFreightTemplateListImportVO> importList, String currentUser);

}
