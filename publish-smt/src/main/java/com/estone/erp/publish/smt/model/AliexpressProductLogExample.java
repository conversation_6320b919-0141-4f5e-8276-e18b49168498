package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressProductLogExample {

    private List<String> authSellerList = new ArrayList<>();

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String fields;

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public AliexpressProductLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOperateStatusEqualTo(Integer value) {
            addCriterion("operate_status =", value, "operateStatus");
            return (Criteria) this;
        }
        public Criteria andOperateStatusIn(List<Integer> values) {
            addCriterion("operate_status in", values, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andOperateStatusNotEqualTo(Integer value) {
            addCriterion("operate_status <>", value, "operateStatus");
            return (Criteria) this;
        }

        public Criteria andResultTypeEqualTo(String value) {
            addCriterion("result_type =", value, "resultType");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("`operator` is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("`operator` is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("`operator` =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("`operator` <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("`operator` >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("`operator` >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("`operator` <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("`operator` <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("`operator` like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("`operator` not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("`operator` in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("`operator` not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("`operator` between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("`operator` not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNull() {
            addCriterion("operate_time is null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNotNull() {
            addCriterion("operate_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeEqualTo(Timestamp value) {
            addCriterion("operate_time =", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotEqualTo(Timestamp value) {
            addCriterion("operate_time <>", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThan(Timestamp value) {
            addCriterion("operate_time >", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("operate_time >=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThan(Timestamp value) {
            addCriterion("operate_time <", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("operate_time <=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIn(List<Timestamp> values) {
            addCriterion("operate_time in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotIn(List<Timestamp> values) {
            addCriterion("operate_time not in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("operate_time between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("operate_time not between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNull() {
            addCriterion("operate_type is null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNotNull() {
            addCriterion("operate_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeEqualTo(String value) {
            addCriterion("operate_type =", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotEqualTo(String value) {
            addCriterion("operate_type <>", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThan(String value) {
            addCriterion("operate_type >", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThanOrEqualTo(String value) {
            addCriterion("operate_type >=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThan(String value) {
            addCriterion("operate_type <", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThanOrEqualTo(String value) {
            addCriterion("operate_type <=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLike(String value) {
            addCriterion("operate_type like", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotLike(String value) {
            addCriterion("operate_type not like", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIn(List<String> values) {
            addCriterion("operate_type in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotIn(List<String> values) {
            addCriterion("operate_type not in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeBetween(String value1, String value2) {
            addCriterion("operate_type between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotBetween(String value1, String value2) {
            addCriterion("operate_type not between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("`result` is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("`result` is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(Boolean value) {
            addCriterion("`result` =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(Boolean value) {
            addCriterion("`result` <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(Boolean value) {
            addCriterion("`result` >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`result` >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThan(Boolean value) {
            addCriterion("`result` <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(Boolean value) {
            addCriterion("`result` <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<Boolean> values) {
            addCriterion("`result` in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<Boolean> values) {
            addCriterion("`result` not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(Boolean value1, Boolean value2) {
            addCriterion("`result` between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`result` not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNull() {
            addCriterion("fail_info is null");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNotNull() {
            addCriterion("fail_info is not null");
            return (Criteria) this;
        }

        public Criteria andFailInfoEqualTo(String value) {
            addCriterion("fail_info =", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotEqualTo(String value) {
            addCriterion("fail_info <>", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThan(String value) {
            addCriterion("fail_info >", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fail_info >=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThan(String value) {
            addCriterion("fail_info <", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThanOrEqualTo(String value) {
            addCriterion("fail_info <=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLike(String value) {
            addCriterion("fail_info like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotLike(String value) {
            addCriterion("fail_info not like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoIn(List<String> values) {
            addCriterion("fail_info in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotIn(List<String> values) {
            addCriterion("fail_info not in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoBetween(String value1, String value2) {
            addCriterion("fail_info between", value1, value2, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotBetween(String value1, String value2) {
            addCriterion("fail_info not between", value1, value2, "failInfo");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditIsNull() {
            addCriterion("price_before_edit is null");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditIsNotNull() {
            addCriterion("price_before_edit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditEqualTo(Double value) {
            addCriterion("price_before_edit =", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditNotEqualTo(Double value) {
            addCriterion("price_before_edit <>", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditGreaterThan(Double value) {
            addCriterion("price_before_edit >", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditGreaterThanOrEqualTo(Double value) {
            addCriterion("price_before_edit >=", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditLessThan(Double value) {
            addCriterion("price_before_edit <", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditLessThanOrEqualTo(Double value) {
            addCriterion("price_before_edit <=", value, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditIn(List<Double> values) {
            addCriterion("price_before_edit in", values, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditNotIn(List<Double> values) {
            addCriterion("price_before_edit not in", values, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditBetween(Double value1, Double value2) {
            addCriterion("price_before_edit between", value1, value2, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceBeforeEditNotBetween(Double value1, Double value2) {
            addCriterion("price_before_edit not between", value1, value2, "priceBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditIsNull() {
            addCriterion("price_after_edit is null");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditIsNotNull() {
            addCriterion("price_after_edit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditEqualTo(Double value) {
            addCriterion("price_after_edit =", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditNotEqualTo(Double value) {
            addCriterion("price_after_edit <>", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditGreaterThan(Double value) {
            addCriterion("price_after_edit >", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditGreaterThanOrEqualTo(Double value) {
            addCriterion("price_after_edit >=", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditLessThan(Double value) {
            addCriterion("price_after_edit <", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditLessThanOrEqualTo(Double value) {
            addCriterion("price_after_edit <=", value, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditIn(List<Double> values) {
            addCriterion("price_after_edit in", values, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditNotIn(List<Double> values) {
            addCriterion("price_after_edit not in", values, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditBetween(Double value1, Double value2) {
            addCriterion("price_after_edit between", value1, value2, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andPriceAfterEditNotBetween(Double value1, Double value2) {
            addCriterion("price_after_edit not between", value1, value2, "priceAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditIsNull() {
            addCriterion("stock_before_edit is null");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditIsNotNull() {
            addCriterion("stock_before_edit is not null");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditEqualTo(Double value) {
            addCriterion("stock_before_edit =", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditNotEqualTo(Double value) {
            addCriterion("stock_before_edit <>", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditGreaterThan(Double value) {
            addCriterion("stock_before_edit >", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditGreaterThanOrEqualTo(Double value) {
            addCriterion("stock_before_edit >=", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditLessThan(Double value) {
            addCriterion("stock_before_edit <", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditLessThanOrEqualTo(Double value) {
            addCriterion("stock_before_edit <=", value, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditIn(List<Double> values) {
            addCriterion("stock_before_edit in", values, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditNotIn(List<Double> values) {
            addCriterion("stock_before_edit not in", values, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditBetween(Double value1, Double value2) {
            addCriterion("stock_before_edit between", value1, value2, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockBeforeEditNotBetween(Double value1, Double value2) {
            addCriterion("stock_before_edit not between", value1, value2, "stockBeforeEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditIsNull() {
            addCriterion("stock_after_edit is null");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditIsNotNull() {
            addCriterion("stock_after_edit is not null");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditEqualTo(Double value) {
            addCriterion("stock_after_edit =", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditNotEqualTo(Double value) {
            addCriterion("stock_after_edit <>", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditGreaterThan(Double value) {
            addCriterion("stock_after_edit >", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditGreaterThanOrEqualTo(Double value) {
            addCriterion("stock_after_edit >=", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditLessThan(Double value) {
            addCriterion("stock_after_edit <", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditLessThanOrEqualTo(Double value) {
            addCriterion("stock_after_edit <=", value, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditIn(List<Double> values) {
            addCriterion("stock_after_edit in", values, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditNotIn(List<Double> values) {
            addCriterion("stock_after_edit not in", values, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditBetween(Double value1, Double value2) {
            addCriterion("stock_after_edit between", value1, value2, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andStockAfterEditNotBetween(Double value1, Double value2) {
            addCriterion("stock_after_edit not between", value1, value2, "stockAfterEdit");
            return (Criteria) this;
        }

        public Criteria andRelationTypeEqualTo(Integer value) {
            addCriterion("relation_type =", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationIdEqualTo(String value) {
            addCriterion("relation_id =", value, "relationId");
            return (Criteria) this;
        }
        public Criteria andRuleNameEqualTo(String value) {
            addCriterion("rule_name =", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", "%"+value+"%", "ruleName");
            return (Criteria) this;
        }

        public Criteria andRelationIdIn(List<String> values) {
            addCriterion("relation_id in", values, "values");
            return (Criteria) this;
        }

        public Criteria andRuleTableIsNull() {
            addCriterion("rule_table is null");
            return (Criteria) this;
        }

        public Criteria andRuleTableIsNotNull() {
            addCriterion("rule_table is not null");
            return (Criteria) this;
        }

        public Criteria andRuleTableEqualTo(String value) {
            addCriterion("rule_table =", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableNotEqualTo(String value) {
            addCriterion("rule_table <>", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableGreaterThan(String value) {
            addCriterion("rule_table >", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableGreaterThanOrEqualTo(String value) {
            addCriterion("rule_table >=", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableLessThan(String value) {
            addCriterion("rule_table <", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableLessThanOrEqualTo(String value) {
            addCriterion("rule_table <=", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableLike(String value) {
            addCriterion("rule_table like", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableNotLike(String value) {
            addCriterion("rule_table not like", value, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableIn(List<String> values) {
            addCriterion("rule_table in", values, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableNotIn(List<String> values) {
            addCriterion("rule_table not in", values, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableBetween(String value1, String value2) {
            addCriterion("rule_table between", value1, value2, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleTableNotBetween(String value1, String value2) {
            addCriterion("rule_table not between", value1, value2, "ruleTable");
            return (Criteria) this;
        }

        public Criteria andRuleIdIsNull() {
            addCriterion("rule_id is null");
            return (Criteria) this;
        }

        public Criteria andRuleIdIsNotNull() {
            addCriterion("rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andRuleIdEqualTo(String value) {
            addCriterion("rule_id =", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotEqualTo(String value) {
            addCriterion("rule_id <>", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThan(String value) {
            addCriterion("rule_id >", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThanOrEqualTo(String value) {
            addCriterion("rule_id >=", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThan(String value) {
            addCriterion("rule_id <", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThanOrEqualTo(String value) {
            addCriterion("rule_id <=", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLike(String value) {
            addCriterion("rule_id like", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotLike(String value) {
            addCriterion("rule_id not like", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdIn(List<String> values) {
            addCriterion("rule_id in", values, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotIn(List<String> values) {
            addCriterion("rule_id not in", values, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdBetween(String value1, String value2) {
            addCriterion("rule_id between", value1, value2, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotBetween(String value1, String value2) {
            addCriterion("rule_id not between", value1, value2, "ruleId");
            return (Criteria) this;
        }

        public Criteria andProblemTypeIn(List<String> values) {
            addCriterion("problem_type in", values, "problemType");
            return (Criteria) this;
        }
        public Criteria andProblemTypeEqualTo(String value) {
            addCriterion("problem_type =", value, "problemType");

            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public List<String> getAuthSellerList() {
        return authSellerList;
    }

    public void setAuthSellerList(List<String> authSellerList) {
        this.authSellerList = authSellerList;
    }
}