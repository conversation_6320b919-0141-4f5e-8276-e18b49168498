package com.estone.erp.publish.smt.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.EditCategoryAttributesOpenCall;
import com.estone.erp.publish.smt.componet.SmtItemEsBulkProcessor;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Slf4j
@Component
public class SmtAdminAttrSynchToListMqListener implements ChannelAwareMessageListener {
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private SmtItemEsBulkProcessor smtItemEsBulkProcessor;

    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        boolean sign = false;
        AliexpressProductLog productLog = null;
        try {
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            productLog = JSON.parseObject(body, new TypeReference<AliexpressProductLog>() {
            });

            if (productLog == null) {
                throw new RuntimeException("参数异常");
            }

            String aliexpressAccountNumber = productLog.getAccountNumber();
            Long productId = productLog.getProductId();
            Boolean lackAttr = productLog.getLackAttr();
            List<String> listIds = productLog.getListIds();
            AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(aliexpressAccountNumber, productId);
            String aeopAeProductPropertysJson = aliexpressEsExtend.getAeopAeProductPropertysJson();
            //admin范本属性
            String requestData = productLog.getRequestData();

            JSONArray newJsonArray = new JSONArray();
            JSONArray dbJsonArray = JSONArray.parseArray(aeopAeProductPropertysJson);
            JSONArray adminJsonArray = JSONArray.parseArray(requestData);

            Set<Long> existSet = new HashSet<>();
            for (int i = 0; i < dbJsonArray.size(); i++) {
                JSONObject jsonObject = dbJsonArray.getJSONObject(i);
                long attr_name_id = jsonObject.getLongValue("attr_name_id");
                existSet.add(attr_name_id);
                newJsonArray.add(jsonObject);
            }
            boolean isUpdate = false;
            for (int i = 0; i < adminJsonArray.size(); i++) {
                JSONObject jsonObject = adminJsonArray.getJSONObject(i);
                long attr_name_id = jsonObject.getLongValue("attr_name_id");
                if (!existSet.contains(attr_name_id)) {
                    isUpdate = true;
                    newJsonArray.add(jsonObject);
                }
            }

            if (!isUpdate) {
                productLog.setResult(true);
                productLog.setFailInfo("没有需要更新的属性！");

                if (CollectionUtils.isNotEmpty(listIds) && lackAttr != null) {
                    // 这里要取反，缺失属性为true 就是 adminToListing 为 false
                    smtItemEsBulkProcessor.updateAdminToListing(listIds, !lackAttr);
                }
            } else {
                //需要调用属性接口修改
                SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

                EditCategoryAttributesOpenCall editCall = new EditCategoryAttributesOpenCall();
                String productProperties = newJsonArray.toJSONString();
                ResponseJson responseJson = editCall
                        .editCategoryAttributes(saleAccountByAccountNumber,
                                String.valueOf(productId), productProperties);
                //成功，修改属性
                if (responseJson.isSuccess()) {
                    aliexpressEsExtend.setAeopAeProductPropertysJson(productProperties);
                    aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
                    productLog.setResult(true);

                    if (CollectionUtils.isNotEmpty(listIds) && lackAttr != null) {
                        // 这里要取反，缺失属性为true 就是 adminToListing 为 false
                        smtItemEsBulkProcessor.updateAdminToListing(listIds, !lackAttr);
                    }
                } else {
                    productLog.setResult(false);
                    productLog.setFailInfo(responseJson.getMessage());
                }
            }
            sign = true;
            productLog.setOperateStatus(OperateLogStatusEnum.end.getCode());
            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
        } catch (Exception e) {
            log.error("SmtAdminAttrSynchToListMqListener 异常：" + body + e.getMessage(), e);
            if (productLog != null) {
                productLog.setResult(false);
                productLog.setFailInfo(body + e.getMessage());
                productLog.setOperateStatus(OperateLogStatusEnum.end.getCode());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        } finally {
            try {
                if (sign) {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } else {
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (Exception e) {
                log.error("SmtAdminAttrSynchToListMqListener 异常：" + e.getMessage(), e);
            }
        }
    }
}
