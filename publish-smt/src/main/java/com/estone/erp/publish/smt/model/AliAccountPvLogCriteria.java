package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> ali_account_pv_log
 * 2024-03-05 09:11:27
 */
public class AliAccountPvLogCriteria extends AliAccountPvLog {
    private static final long serialVersionUID = 1L;

    public AliAccountPvLogExample getExample() {
        AliAccountPvLogExample example = new AliAccountPvLogExample();
        AliAccountPvLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (this.getTotal() != null) {
            criteria.andTotalEqualTo(this.getTotal());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        return example;
    }
}