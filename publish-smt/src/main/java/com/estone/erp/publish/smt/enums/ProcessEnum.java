package com.estone.erp.publish.smt.enums;

public enum ProcessEnum {
    NOT_PROCESSED("未处理", 0),
    PROCESSED("已处理", 1);

    private Integer code;

    private String name;

    private ProcessEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static String getNameByCode(int code) {
        ProcessEnum[] values = values();
        for (ProcessEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }


}
