package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AliexpressStoreCodeRuleConfigDto {

    /**
     * 优惠名称
     */
    private String discountName;
    /**
     * 优惠总额
     */
    private String discountNum;
    /**
     * 优惠门槛类型 0无门槛 1订单金额大于等于
     */
    private Integer discountThresholdConditionType;
    /**
     * 订单金额大于值
     */
    private String discountThresholdValue;
    /**
     * 发放张数
     */
    private Integer num;

    /**
     * 每人限额
     */
    private Integer limitNum;
    /**
     * 开始时间
     */
    private String timeStart;
    /**
     * 结束时间
     */
    private String timeEnd;

    /**
     * 提前领取1是0否
     */
    private Integer isAdvance;
    /**
     * 渠道1常规展示0定向渠道发放
     */
    private Integer channel;
    /**
     * 其他渠道1是0否
     */
    private Integer anotherChannel;

}
