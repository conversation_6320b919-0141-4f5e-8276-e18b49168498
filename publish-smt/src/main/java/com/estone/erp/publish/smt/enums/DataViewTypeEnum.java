package com.estone.erp.publish.smt.enums;

public enum DataViewTypeEnum {

    TEMPLATE(1, "模板"),

    LISTING(2, "listing");

    private Integer id;
    private String name;

    private DataViewTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
