package com.estone.erp.publish.smt.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> aliexpress_config
 * 2019-11-16 20:06:02
 */
@Data
public class AliexpressConfigCriteria extends AliexpressConfig {
    private static final long serialVersionUID = 1L;

    private List<Integer> ids;

    /**
     * 批量修改参数 ids
     */
    private String batchIds;

    /**
     * 1:修改分类 2：修改最大刊登数量 3:修改刊登时间 4:自动上架新品
     */
    private String batchType;

    private String batchValue;

    /**
     * 查询店铺是否异常
     */
    private Boolean isAbnormal;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;

    /**
     * 查询可刊登系统分类 categoryIdList get方法被重写不可以直接使用categoryIdList
     */
    private List<String> queryCategoryIdList;

    /**
     * 需要复制的店铺
     */
    private List<String> copyAccountList;

    /**
     * 是否加入联盟 true/false
     */
    private Integer addAliance;

    /**
     * 店铺分组id
     */
    private List<Integer> groupIdList;

    /**
     * 周期内新发商品上限数
     */
    private Integer productCurrentMonthLimitNumFrom;
    private Integer productCurrentMonthLimitNumTo;

    /**
     * 总可销售商品上限数
     */
    private Integer productTotalLimitNumFrom;
    private Integer productTotalLimitNumTo;

    /**
     * 总审核中商品数
     */
    private Integer productTotalAuditNumFrom;
    private Integer productTotalAuditNumTo;

    /**
     * 周期新发商品数
     */
    private Integer productCurrentMonthPubNumFrom;
    private Integer productCurrentMonthPubNumTo;

    /**
     * 总在线商品数
     */
    private Integer productTotalOnlineNumFrom;
    private Integer productTotalOnlineNumTo;

    public AliexpressConfigExample getExample() {
        AliexpressConfigExample example = new AliexpressConfigExample();
        AliexpressConfigExample.Criteria criteria = example.createCriteria();

        if(this.getAutoHalfReservation() != null){
            criteria.andAutoHalfReservationEqualTo(this.getAutoHalfReservation());
        }

        if(this.getAutoUpdateQualifications() != null){
            criteria.andAutoUpdateQualificationsEqualTo(this.getAutoUpdateQualifications());
        }

        //是否自动刊登
        if(this.getAutoGroundingNew() != null){
            criteria.andAutoGroundingNewEqualTo(this.getAutoGroundingNew());
        }
        //是否自动修改重量
        if(this.getAutoUpdateWeight() != null){
            criteria.andAutoUpdateWeightEqualTo(this.getAutoUpdateWeight());
        }
        //是否自动补库存
        if(this.getAutoSupplyStock() != null){
            criteria.andAutoSupplyStockEqualTo(this.getAutoSupplyStock());
        }
        //是否自动修改标题
        if(this.getAutoUpdateTitle() != null){
            criteria.andAutoUpdateTitleEqualTo(this.getAutoUpdateTitle());
        }
        //是否自动修改描述
        if(this.getAutoUpdateDetail() != null){
            criteria.andAutoUpdateDetailEqualTo(this.getAutoUpdateDetail());
        }
        //是否自动修改子sku图片
        if(this.getAutoUpdateSonimg() != null){
            criteria.andAutoUpdateSonimgEqualTo(this.getAutoUpdateSonimg());
        }
        //是否分配新品
        if(this.getAutoRecommendNewProduct() != null){
            criteria.andAutoRecommendNewProductEqualTo(this.getAutoRecommendNewProduct());
        }
        //是否自动下架竞争力不佳的商品
        if(this.getAutoDownForBadItem() != null){
            criteria.andAutoDownForBadItemEqualTo(this.getAutoDownForBadItem());
        }
        // 是否自动修改亏损订单
        if (this.getAutoUpdateDeficitOrder() != null) {
            criteria.andAutoUpdateDeficitOrderEqualTo(this.getAutoUpdateDeficitOrder());
        }
        // 是否加入商品及物流包装环保费
        if (this.getAddEprPackFee() != null) {
            criteria.andAddEprPackFeeEqualTo(this.getAddEprPackFee());
        }
        if(CollectionUtils.isNotEmpty(this.getIds())){
            criteria.andIdIn(this.getIds());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            if(StringUtils.indexOf(this.getAccount(), ",") != -1){
                criteria.andAccountIn(CommonUtils.splitList(this.getAccount(), ","));
            }else{
                criteria.andAccountEqualTo(this.getAccount());
            }
        }

        if (this.getUsable() != null) {
            criteria.andUsableEqualTo(this.getUsable());
        }
        if (this.getDeliverytime() != null) {
            criteria.andDeliverytimeEqualTo(this.getDeliverytime());
        }
        if (this.getWholesale() != null) {
            criteria.andWholesaleEqualTo(this.getWholesale());
        }
        if (this.getBulkorder() != null) {
            criteria.andBulkorderEqualTo(this.getBulkorder());
        }
        if (this.getBulkdiscount() != null) {
            criteria.andBulkdiscountEqualTo(this.getBulkdiscount());
        }
        if (this.getStock() != null) {
            criteria.andStockEqualTo(this.getStock());
        }
        if (this.getWsvalidnum() != null) {
            criteria.andWsvalidnumEqualTo(this.getWsvalidnum());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getOpenDate())){
            criteria.andOpenDateEqualTo(this.getOpenDate());
        }
        if(CollectionUtils.isNotEmpty(this.getQueryCategoryIdList())) {
            criteria.andCategoryIdIn(this.getQueryCategoryIdList());
        }

        if (ObjectUtils.isNotEmpty(this.getProductCurrentMonthLimitNumFrom())){
            criteria.andProductCurrentMonthLimitNumGreaterThanOrEqualTo(this.getProductCurrentMonthLimitNumFrom());
        }
        if (ObjectUtils.isNotEmpty(this.getProductCurrentMonthLimitNumTo())){
            criteria.andProductCurrentMonthLimitNumLessThanOrEqualTo(this.getProductCurrentMonthLimitNumTo());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalLimitNumFrom())){
            criteria.andProductTotalLimitNumGreaterThanOrEqualTo(this.getProductTotalLimitNumFrom());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalLimitNumTo())){
            criteria.andProductTotalLimitNumLessThanOrEqualTo(this.getProductTotalLimitNumTo());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalOnlineNumFrom())){
            criteria.andProductTotalOnlineNumGreaterThanOrEqualTo(this.getProductTotalOnlineNumFrom());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalOnlineNumTo())){
            criteria.andProductTotalOnlineNumLessThanOrEqualTo(this.getProductTotalOnlineNumTo());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalAuditNumFrom())){
            criteria.andProductTotalAuditNumGreaterThanOrEqualTo(this.getProductTotalAuditNumFrom());
        }
        if (ObjectUtils.isNotEmpty(this.getProductTotalAuditNumTo())){
            criteria.andProductTotalAuditNumLessThanOrEqualTo(this.getProductTotalAuditNumTo());
        }
        if (ObjectUtils.isNotEmpty(this.getProductCurrentMonthPubNumFrom())){
            criteria.andProductCurrentMonthPubNumGreaterThanOrEqualTo(this.getProductCurrentMonthPubNumFrom());
        }
        if (ObjectUtils.isNotEmpty(this.getProductCurrentMonthPubNumTo())){
            criteria.andProductCurrentMonthPubNumLessThanOrEqualTo(this.getProductCurrentMonthPubNumTo());
        }
        if(ObjectUtils.isNotEmpty(this.getAutoAddEprFee())){
            criteria.andAutoAddEprFeeEqualTo(this.getAutoAddEprFee());
        }
        return example;
    }
}