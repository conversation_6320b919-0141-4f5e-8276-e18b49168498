package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtCategoryGpsrLogo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 店铺
     */
    private String account;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * path_code
     */
    private String fullPathCode;

    /**
     * cn_name
     */
    private String fullCnName;

    /**
     * url
     */
    private String url;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 更新时间
     */
    private Timestamp updateDate;

    /**
     * 操作结果
     */
    private Boolean result;

    /**
     * 错误信息
     */
    private String failInfo;
}