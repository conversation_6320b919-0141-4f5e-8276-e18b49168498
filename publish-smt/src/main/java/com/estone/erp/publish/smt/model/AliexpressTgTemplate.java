package com.estone.erp.publish.smt.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Data
public class AliexpressTgTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_tg_template.id
     */
    private Integer id;

    /**
     * 使用状态 0禁用 1启用
     */
    private Integer applyState;

    /**
     *  database column aliexpress_tg_template.is_parent
     */
    private Boolean isParent;

    /**
     * 状态 database column aliexpress_tg_template.template_status
     */
    private Integer templateStatus;

    /**
     * 单品货号 database column aliexpress_tg_template.article_number
     */
    private String articleNumber;

    /**
     * 主图 database column aliexpress_tg_template.main_img
     */
    private String mainImg;

    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚
     */
    private String imageUrls;

    /**
     * 速卖通账号 database column aliexpress_tg_template.aliexpress_account_number
     */
    private String aliexpressAccountNumber;

    /**
     * sku价格库存属性 database column aliexpress_tg_template.aeop_ae_product_skus_json
     */
    private String aeopAeProductSkusJson;

    /**
     * sku产品属性(品牌) database column aliexpress_tg_template.aeop_ae_product_propertys_json
     */
    private String aeopAeProductPropertysJson;

    /**
     * 标题 database column aliexpress_tg_template.subject
     */
    private String subject;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取 database column aliexpress_tg_template.category_id
     */
    private Integer categoryId;

    /**
     * 经营大类
     */
    private Integer rootCategory;

    /**
     * 类目名 database column aliexpress_tg_template.category_name
     */
    private String categoryName;

    /**
     * 商品单位 (存储单位编号) *********:袋 (bag/bags) *********:桶 (barrel/barrels) *********:蒲式耳 (bushel/bushels) *********:箱 (carton) *********:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_tg_template.product_unit
     */
    private Integer productUnit;

    /**
     * 打包销售: true 非打包销售:false database column aliexpress_tg_template.package_type
     */
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_tg_template.lot_num
     */
    private Integer lotNum;

    /**
     * 币种 database column aliexpress_tg_template.currency_code
     */
    private String currencyCode;

    /**
     * 资质信息Json database column aliexpress_tg_template.aeop_qualification_struct_json
     */
    private String aeopQualificationStructJson;

    /**
     * 特殊商品类型，目前只支持两种类型的商品发布 ：弱磁 ：240003 ； 内电 ：239818 database column aliexpress_tg_template.special_product_type_list_str
     */
    private String specialProductTypeListStr;

    /**
     * 欧盟责任人id database column aliexpress_tg_template.msr_eu_id
     */
    private Long msrEuId;

    /**
     * 产品所关联的尺码模版ID database column aliexpress_tg_template.size_chart_id
     */
    private Long sizeChartId;

    /**
     * 全托管商品备货类型， 仓发：0 ； 即时补货模式（JIT）：1  database column aliexpress_tg_template.product_type
     */
    private String productType;

    /**
     * 营销图json格式 database column aliexpress_tg_template.market_images_json
     */
    private String marketImagesJson;

    /**
     * 视频地址 database column aliexpress_tg_template.video_link
     */
    private String videoLink;

    /**
     * Detail详情。 database column aliexpress_tg_template.detail
     */
    private String detail;

    /**
     * mobile_detail详情。 database column aliexpress_tg_template.mobile_detail
     */
    private String mobileDetail;

    /**
     * 属性(预留) database column aliexpress_tg_template.attribute1
     */
    private String attribute1;

    /**
     * 属性 database column aliexpress_tg_template.attribute2
     */
    private String attribute2;

    /**
     * 属性 database column aliexpress_tg_template.attribute3
     */
    private String attribute3;

    /**
     * 属性 database column aliexpress_tg_template.attribute4
     */
    private String attribute4;

    /**
     * 属性 database column aliexpress_tg_template.attribute5
     */
    private String attribute5;

    /**
     * 创建人 database column aliexpress_tg_template.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_tg_template.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_tg_template.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_tg_template.update_date
     */
    private Timestamp updateDate;

    /**
     * 刊登失败错误记录
     */
    private String errorMsg;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 制造商id
     */
    private Long manufactureId;

    /**
     * 制造商name
     */
    private String manufactureName;

    /**
     * 图片池
     */
    private List<String> images = new ArrayList<>();

    private Long productId;

    /**
     * 营销图列表
     */
    private List<MarketImage> marketImages;

    //图片池
    private List<String> productImages;

    //重量
    private Double totalWeight;

    //产品状态
    private String itemStatusCn;

    //侵权平台
    private Set<String> infringementProSaleChannels;

    /**
     * 禁售平台集合
     */
    private List<String> salesProbition = new ArrayList<>();

    //产品标签
    private String tag;

    //自动刊登匹配店铺配置用
    private String grossWeight;

    //-----自动刊登算价用
    private String countryCode;
    private String shippingMethodCode;
    private Double margin;
    private String maxWeightSku;

    private Integer packageLength;
    private Integer packageWidth;
    private Integer packageHeight;

    //是否定时刊登
    private Boolean isTiming;

    /**
     * 新尺码表多个英文逗号拼接 database column aliexpress_template.size_chart_id_list
     */
    private String sizeChartIdList;

    /**
     * 海关编码
     */
    private String hacodeJson;

    public List<MarketImage> getMarketImages() {

        if(CollectionUtils.isNotEmpty(marketImages)){
            return this.marketImages;
        }

        List<MarketImage> marketImages = new ArrayList<>();
        if(StringUtils.isNotBlank(marketImagesJson)) {
            try {
                marketImages = JSON.parseObject(marketImagesJson, new TypeReference<List<MarketImage>>() {
                });
            }catch (Exception e) {
                log.error("解析营销图json出错" + e.getMessage(), e);
            }
        }
        return marketImages;
    }

    //获取模板所有sku信息
    @JSONField(serialize = false)
    public List<String> getSkuList() {
        Set<String> set = new HashSet<>();
        try{
            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, this.getAliexpressAccountNumber());

            String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();

            JSONArray jsonArray = JSON.parseArray(aeopAeProductSkusJson);
            for(int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String sku_code = jsonObject.getString("sku_code");
                if(StringUtils.isNotBlank(sku_code)){
                    if(StringUtils.isNotBlank(account.getSellerSkuPrefix()) && sku_code.startsWith(account.getSellerSkuPrefix())){
                        sku_code = sku_code.replaceFirst(account.getSellerSkuPrefix(), "");
                    }
                    set.add(sku_code);
                }
            }

            if(set.isEmpty()){
                set.add(this.getArticleNumber());
            }
            return new ArrayList<>(set);

        }catch (Exception e){
            //忽略报错
            //log.error(e.getMessage(), e);
        }
        return new ArrayList<>(set);
    }

    //重复刊登 校验模板使用
    @JSONField(serialize = false)
    public List<String> getCheckSkuList() {
        Set<String> set = new HashSet<>();
        set.add(this.getArticleNumber());
        try{
            SaleAccountAndBusinessResponse account = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, this.getAliexpressAccountNumber());

            String aeopAeProductSkusJson = this.getAeopAeProductSkusJson();

            JSONArray jsonArray = JSON.parseArray(aeopAeProductSkusJson);
            for(int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String sku_code = jsonObject.getString("sku_code");
                if(StringUtils.isNotBlank(sku_code)){
                    if(StringUtils.isNotBlank(account.getSellerSkuPrefix()) && sku_code.startsWith(account.getSellerSkuPrefix())){
                        sku_code = sku_code.replaceFirst(account.getSellerSkuPrefix(), "");
                    }
                    set.add(sku_code);
                }
            }
            return new ArrayList<>(set);

        }catch (Exception e){
            //忽略报错
            //log.error(e.getMessage(), e);
        }
        return new ArrayList<>(set);
    }
}