
package com.estone.erp.publish.smt.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NeedDelProDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 需要删除的商品id，商品id,非列表id
     */
    @NotNull(message = "商品id不能为空")
    private Long itemId;

    /**
     * 折扣率
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

}