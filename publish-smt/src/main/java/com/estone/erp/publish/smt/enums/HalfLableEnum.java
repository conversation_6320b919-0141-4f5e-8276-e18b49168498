package com.estone.erp.publish.smt.enums;

public enum HalfLableEnum {
    one("cellbattery", "274526"),
    two("cellbatteryandmagnetism", "274526"),
    three("rechargeablebattery", "274526"),
    fout("rechargeablebatteryandmagnetism", "274526"),
    five("magnetism", "274452"),
    six("resistance", ""),
    seven("Adult erotica products", ""),
    eight("normal goods", ""),
    ;

    private String productTag;
    private String platformTag;

    private HalfLableEnum(String productTag, String platformTag) {
        this.productTag = productTag;
        this.platformTag = platformTag;
    }

    public String getProductTag() {
        return productTag;
    }

    public void setProductTag(String productTag) {
        this.productTag = productTag;
    }

    public String getPlatformTag() {
        return platformTag;
    }

    public void setPlatformTag(String platformTag) {
        this.platformTag = platformTag;
    }
}
