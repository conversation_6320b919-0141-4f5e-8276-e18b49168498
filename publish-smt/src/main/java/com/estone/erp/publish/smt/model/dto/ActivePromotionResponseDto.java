package com.estone.erp.publish.smt.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 活动查询响应DTO
 */
@Data
public class ActivePromotionResponseDto {
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 店铺
     */
    private String store;

    /**
     * 类型
     */
    private String type;

    /**
     * 优惠名称
     */
    private String discountName;

    /**
     * 优惠金额
     */
    private String discountNum;

    /**
     * 优惠门槛
     */
    private String discountThreshold;

    /**
     * 使用开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp timeStart;

    /**
     * 使用结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp timeEnd;

    /**
     * 适用商品
     */
    private String activityScope;

    /***
     * 活动id
     */
    private String activityId;
}