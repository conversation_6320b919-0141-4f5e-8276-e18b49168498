package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.redis.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.AliecpressPersonnelDimension;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.RuleTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalHistoryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalDateRangeDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalExportDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalQueryDto;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressTaskExecutionDetailsMapper;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressTaskExecutionTotalMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionTotal;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressTaskExecutionTotalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【aliexpress_task_execution_total】的数据库操作Service实现
 * @createDate 2024-10-11 17:31:42
 */
@Slf4j
@Service
public class AliexpressTaskExecutionTotalServiceImpl extends ServiceImpl<AliexpressTaskExecutionTotalMapper, AliexpressTaskExecutionTotal>
        implements AliexpressTaskExecutionTotalService {

    @Resource
    private AliexpressTaskExecutionDetailsMapper aliexpressTaskExecutionDetailsMapper;

    @Resource
    private ExcelSend excelSend;
    @Resource
    private PermissionsHelper permissionsHelper;


    @Override
    public ResponseJson export(AliexpressTaskExecutionTotalExportDto dto) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        dto.setPageSize(10);
        dto.setPageNum(1);
        IPage<AliexpressTaskExecutionTotal> aliexpressTaskExecutionTotalIPage = this.pageQuery(dto);
        Long count = aliexpressTaskExecutionTotalIPage.getTotal();
        if (ObjectUtils.isEmpty(count) || count == 0) {
            responseJson.setMessage("不存在数据");
            return responseJson;
        }
        if (count > 1000000) {
            responseJson.setMessage("导出数据不可超过100W,请缩小查询范围");
            return responseJson;
        }
        if (CollectionUtils.isEmpty(dto.getAccounts())) {
            this.getPageQueryWrapper(dto);
        } else {
            dto.setUserName(WebUtils.getUserName());
            List<String> underlingPermissionUsers = permissionsHelper.getUnderlingPermissionUsers(SaleChannel.CHANNEL_SMT);
            if (CollectionUtils.isEmpty(underlingPermissionUsers)) {
                dto.setIsAdmin(true);
            }else {
                dto.setIsAdmin(false);
            }
        }
        return excelSend.downloadAliexpressTaskExecutionTotal(ExcelTypeEnum.downloadTaskList.getCode(), dto);
    }

    @Override
    public List<AliexpressTaskExecutionDetailsToTotalHistoryDto> getHistory(AliexpressTaskExecutionTotalDateRangeDto dto) {
        String granularity = dto.getGranularity();
        if ("day".equals(granularity)) {
            return aliexpressTaskExecutionDetailsMapper.aggregateByDay(dto);
        } else if ("week".equals(granularity)) {
            return aliexpressTaskExecutionDetailsMapper.aggregateByWeek(dto);
        } else if ("month".equals(granularity)) {
            return aliexpressTaskExecutionDetailsMapper.aggregateByMonth(dto);
        } else {
            throw new IllegalArgumentException("Invalid granularity: " + granularity);
        }
    }

    @Override
    public IPage<AliexpressTaskExecutionTotal> pageQuery(AliexpressTaskExecutionTotalQueryDto dto) {
        Page<AliexpressTaskExecutionTotal> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isNotEmpty(dto.getAccounts())) {
            return getAliexpressTaskExecutionTotalIPageByAccountId(dto, page);
        }
        LambdaQueryWrapper<AliexpressTaskExecutionTotal> queryWrapper = this.getPageQueryWrapper(dto);
        return getAliexpressTaskExecutionTotalIPage(page, queryWrapper);
    }

    public IPage<AliexpressTaskExecutionTotal> getAliexpressTaskExecutionTotalIPage(Page<AliexpressTaskExecutionTotal> page, LambdaQueryWrapper<AliexpressTaskExecutionTotal> queryWrapper) {
        return baseMapper.selectPage(page, queryWrapper);
    }

    public IPage<AliexpressTaskExecutionTotal> getAliexpressTaskExecutionTotalIPageByAccountId(AliexpressTaskExecutionTotalQueryDto dto, Page<AliexpressTaskExecutionTotal> page) {
        return this.getCountTotalByAccount(page, dto);
    }

    private IPage<AliexpressTaskExecutionTotal> getCountTotalByAccount(Page<AliexpressTaskExecutionTotal> page, AliexpressTaskExecutionTotalQueryDto dto) {
        try {
            boolean isAdmin = false;
            if (ObjectUtils.isNotEmpty(dto.getIsAdmin())) {
                isAdmin = dto.getIsAdmin();
            } else {
                List<String> underlingPermissionUsers = permissionsHelper.getUnderlingPermissionUsers(SaleChannel.CHANNEL_SMT);
                if (CollectionUtils.isEmpty(underlingPermissionUsers)) {
                    isAdmin = true;
                }
            }
            if (!isAdmin) {
                this.getQueryConditions(dto);
            }
            return aliexpressTaskExecutionDetailsMapper.selectTotalListByAccount(page, dto);
        } catch (Exception e) {
            log.error("查询失败", e);
            throw new RuntimeException(e);
        }
    }

    private void getQueryConditions(AliexpressTaskExecutionTotalQueryDto dto) {
        // 获取当前登录用户信息
        String employeeNo = StringUtils.isBlank(dto.getUserName()) ? WebUtils.getUserName() : dto.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            throw new BusinessException("查询不到员工信息！");
        }
        if (ObjectUtils.isNotEmpty(dto.getDimension())) {
            if (dto.getDimension().equals(AliecpressPersonnelDimension.SALE.getCode())) {
                if (CollectionUtils.isEmpty(dto.getSale())) {
                    dto.setSale(List.of(employeeNo));
                }
            }
            if (dto.getDimension().equals(AliecpressPersonnelDimension.SALE_LEADER.getCode())) {
                if (CollectionUtils.isEmpty(dto.getSaleLeader())) {
                    dto.setSaleLeader(List.of(employeeNo));
                }
            }
            if (dto.getDimension().equals(AliecpressPersonnelDimension.SALE_SUPERVISOR.getCode())) {
                if (CollectionUtils.isEmpty(dto.getSaleSupervisor())) {
                    dto.setSaleSupervisor(List.of(employeeNo));
                }
            }
        }
    }

    public LambdaQueryWrapper<AliexpressTaskExecutionTotal> getPageQueryWrapper(AliexpressTaskExecutionTotalQueryDto dto) {
        LambdaQueryWrapper<AliexpressTaskExecutionTotal> queryWrapper = new LambdaQueryWrapper<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String todayDate = LocalDate.now().format(formatter);
        queryWrapper.eq(AliexpressTaskExecutionTotal::getTodayDate, todayDate);
        if (CollectionUtils.isNotEmpty(dto.getIds())) {
            queryWrapper.in(AliexpressTaskExecutionTotal::getId, dto.getIds());
            return queryWrapper;
        }
        // 获取当前登录用户信息
        String employeeNo = StringUtils.isBlank(dto.getUserName()) ? WebUtils.getUserName() : dto.getUserName();
        dto.setUserName(employeeNo);
        ApiResult<Boolean> superAdminOrEquivalent = ObjectUtils.isEmpty(dto.getSuperAdminOrSupervisor()) ?
                NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT) : dto.getSuperAdminOrSupervisor();
        dto.setSuperAdminOrSupervisor(superAdminOrEquivalent);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }

        ApiResult<NewUser> newUserApiResult = ObjectUtils.isEmpty(dto.getNewUserApiResult()) ?
                NewUsermgtUtils.getUserByNo(employeeNo) : dto.getNewUserApiResult();
        dto.setNewUserApiResult(newUserApiResult);
        if (!newUserApiResult.isSuccess()) {
            throw new BusinessException(newUserApiResult.getErrorMsg());
        }
        if (StringUtils.isBlank(employeeNo)) {
            throw new BusinessException("查询不到员工信息！");
        }
        if (ObjectUtils.isEmpty(dto.getRuleNameType())) {
            queryWrapper.eq(AliexpressTaskExecutionTotal::getHasRuleName, RuleTypeEnum.TOTAL.getCode());
        }
        if (ObjectUtils.isNotEmpty(dto.getRuleNameType())) {
            queryWrapper.eq(AliexpressTaskExecutionTotal::getHasRuleName, dto.getRuleNameType());
        }
        if (ObjectUtils.isEmpty(dto.getDimension()) && CollectionUtils.isEmpty(dto.getSale()) && CollectionUtils.isEmpty(dto.getSaleSupervisor()) && CollectionUtils.isEmpty(dto.getSaleLeader())) {
            // 超级管理员或数据支持部（数据分析）查询所有
            if (superAdminOrEquivalent.getResult() || newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
                queryWrapper.eq(AliexpressTaskExecutionTotal::getDimension, 0);
            } else {
                //查询出当前登录人这个字段dimension的最大值
                LambdaQueryWrapper<AliexpressTaskExecutionTotal> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(AliexpressTaskExecutionTotal::getSaleNum, employeeNo)
                        .eq(AliexpressTaskExecutionTotal::getTodayDate, todayDate)
                        .orderByDesc(AliexpressTaskExecutionTotal::getDimension);
                List<AliexpressTaskExecutionTotal> aliexpressTaskExecutionTotals = baseMapper.selectList(lambdaQueryWrapper);
                if (CollectionUtils.isNotEmpty(aliexpressTaskExecutionTotals)) {
                    queryWrapper.eq(AliexpressTaskExecutionTotal::getSaleNum, employeeNo);
                    queryWrapper.eq(AliexpressTaskExecutionTotal::getDimension, aliexpressTaskExecutionTotals.get(0).getDimension());
                } else {
                    queryWrapper.isNull(AliexpressTaskExecutionTotal::getDimension);
                }
            }
            return queryWrapper;
        }
        if (ObjectUtils.isNotEmpty(dto.getDimension()) && CollectionUtils.isEmpty(dto.getSale()) && CollectionUtils.isEmpty(dto.getSaleSupervisor()) && CollectionUtils.isEmpty(dto.getSaleLeader())) {
            if (superAdminOrEquivalent.getResult() || newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
                queryWrapper.eq(AliexpressTaskExecutionTotal::getDimension, dto.getDimension());
            } else {
                queryWrapper.eq(AliexpressTaskExecutionTotal::getDimension, dto.getDimension());
                queryWrapper.eq(AliexpressTaskExecutionTotal::getSaleNum, employeeNo);
            }
        } else {
            queryWrapper.eq(AliexpressTaskExecutionTotal::getDimension, dto.getDimension());
        }
        if (CollectionUtils.isNotEmpty(dto.getSale())) {
            queryWrapper.in(AliexpressTaskExecutionTotal::getSaleNum, dto.getSale());
        }
        if (CollectionUtils.isNotEmpty(dto.getSaleLeader())) {
            queryWrapper.in(AliexpressTaskExecutionTotal::getSaleNum, dto.getSaleLeader());
        }
        if (CollectionUtils.isNotEmpty(dto.getSaleSupervisor())) {
            queryWrapper.in(AliexpressTaskExecutionTotal::getSaleNum, dto.getSaleSupervisor());
        }
        return queryWrapper;
    }
}
