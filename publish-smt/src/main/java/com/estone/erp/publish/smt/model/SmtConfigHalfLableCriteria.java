package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> smt_config_half_lable
 * 2024-01-17 15:49:10
 */
public class SmtConfigHalfLableCriteria extends SmtConfigHalfLable {
    private static final long serialVersionUID = 1L;

    public SmtConfigHalfLableExample getExample() {
        SmtConfigHalfLableExample example = new SmtConfigHalfLableExample();
        SmtConfigHalfLableExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (StringUtils.isNotBlank(this.getProductTag())) {
            criteria.andProductTagEqualTo(this.getProductTag());
        }
        if (StringUtils.isNotBlank(this.getPlatformTag())) {
            criteria.andPlatformTagEqualTo(this.getPlatformTag());
        }
        if (this.getUploadState() != null) {
            criteria.andUploadStateEqualTo(this.getUploadState());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        return example;
    }
}