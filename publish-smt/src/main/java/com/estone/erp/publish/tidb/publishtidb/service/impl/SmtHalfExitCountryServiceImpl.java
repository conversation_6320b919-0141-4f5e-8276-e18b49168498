package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.HalfExitCountryExamineStatusEnum;
import com.estone.erp.publish.smt.enums.HalfExitCountryLabelEnum;
import com.estone.erp.publish.smt.helper.AliexpressHalfExitCountryHelper;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtHalfExitCountryMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountry;
import com.estone.erp.publish.tidb.publishtidb.service.SmtHalfExitCountryService;
import com.estone.erp.publish.tidb.publishtidb.vo.SmtHalfExitCountryQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * smt半托管商品退出部分国家 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
@Service
public class SmtHalfExitCountryServiceImpl extends ServiceImpl<SmtHalfExitCountryMapper, SmtHalfExitCountry> implements SmtHalfExitCountryService {

    @Resource
    public SmtHalfExitCountryMapper smtHalfExitCountryMapper;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private ExcelSend excelSend;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;
    @Resource
    private AliexpressHalfExitCountryHelper aliexpressHalfExitCountryHelper;

    /**
     * 根据店铺、商品ID和审核状态查询记录数量
     * 
     * @param shop 店铺
     * @param productId 商品ID
     * @param reviewStatus 审核状态
     * @return 记录数量
     */
    @Override
    public Integer countByShopAndProductIdAndReviewStatus(String shop, Long productId, Integer reviewStatus) {
        // 校验参数有效性
        if (Objects.isNull(productId) || Objects.isNull(reviewStatus)) {
            return 0;
        }

        return count(new LambdaQueryWrapper<SmtHalfExitCountry>()
                      .eq(SmtHalfExitCountry::getAccount, shop)
                      .eq(SmtHalfExitCountry::getProductId, productId)
                      .eq(SmtHalfExitCountry::getExamineStatus, reviewStatus));}


    @Override
    public LambdaQueryWrapper<SmtHalfExitCountry> getPageQueryWrapper(SmtHalfExitCountryQueryVO queryVO){
        // 创建查询条件构建器
        LambdaQueryWrapper<SmtHalfExitCountry> queryWrapper = new LambdaQueryWrapper<>();

        //idList 查询
        if (Objects.nonNull(queryVO.getIdList()) && !queryVO.getIdList().isEmpty()) {
            queryWrapper.in(SmtHalfExitCountry::getId, queryVO.getIdList());
        }

        // 店铺多选查询
        if (Objects.nonNull(queryVO.getShopList()) && !queryVO.getShopList().isEmpty()) {
            queryWrapper.in(SmtHalfExitCountry::getAccount, queryVO.getShopList());
        }

        // 商品ID查询（多个用逗号分隔）
        if (Objects.nonNull(queryVO.getProductIds()) && !queryVO.getProductIds().isEmpty()) {
            List<String> productIdArray = CommonUtils.splitList(queryVO.getProductIds(), ",");
            queryWrapper.in(SmtHalfExitCountry::getProductId, productIdArray);
        }

        // 退出国家多选查询
        if (Objects.nonNull(queryVO.getExitCountries()) && !queryVO.getExitCountries().isEmpty()) {
            //exit_country_info 多个国家 模糊 用 or 拼接查询
            List<String> exitCountries = queryVO.getExitCountries();
            int len = exitCountries.size();
            StringBuilder sb = new StringBuilder(len * 32);
            for (int i = 0; i < len; i++) {
                // 直接拼接参数值，注意转义
                String value = exitCountries.get(i).replace("'", "''"); // 防止 SQL 注入，简单转义单引号
                sb.append("exit_country_info LIKE '%").append(value).append("%'");
                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            // 直接将拼接的 SQL 条件加入 queryWrapper
            queryWrapper.apply("(" + sb.toString() + ")");
        }

        // 审核状态单选查询
        if (Objects.nonNull(queryVO.getReviewStatus())) {
            queryWrapper.eq(SmtHalfExitCountry::getExamineStatus, queryVO.getReviewStatus());
        }

        // 审核通过时间范围查询
        if (Objects.nonNull(queryVO.getReviewStartTime())) {
            queryWrapper.ge(SmtHalfExitCountry::getExamineSuccessTime, queryVO.getReviewStartTime());
        }
        if (Objects.nonNull(queryVO.getReviewEndTime())) {
            queryWrapper.le(SmtHalfExitCountry::getExamineSuccessTime, queryVO.getReviewEndTime());
        }

        // 提交状态单选查询
        if (Objects.nonNull(queryVO.getSubmitStatus())) {
            queryWrapper.eq(SmtHalfExitCountry::getSubmitStatus, queryVO.getSubmitStatus());
        }

        // 添加人多选查询
        if (Objects.nonNull(queryVO.getCreatorList()) && !queryVO.getCreatorList().isEmpty()) {
            queryWrapper.in(SmtHalfExitCountry::getCreatedBy, queryVO.getCreatorList());
        }

        // 添加时间范围查询
        if (StringUtils.isNotBlank(queryVO.getCreateStartTime())) {
            queryWrapper.ge(SmtHalfExitCountry::getCreatedTime, queryVO.getCreateStartTime());
        }
        if (StringUtils.isNotBlank(queryVO.getCreateEndTime())) {
            queryWrapper.le(SmtHalfExitCountry::getCreatedTime, queryVO.getCreateEndTime());
        }
            
        //提交时间查询 submitStartTime
        if (Objects.nonNull(queryVO.getSubmitStartTime())) {
            queryWrapper.ge(SmtHalfExitCountry::getSubmitTime, queryVO.getSubmitStartTime());
        }
        if (Objects.nonNull(queryVO.getSubmitEndTime())) {
            queryWrapper.le(SmtHalfExitCountry::getSubmitTime, queryVO.getSubmitEndTime());
        }

        // 提交成功时间范围查询
        if (Objects.nonNull(queryVO.getSubmitSuccessStartTime())) {
            queryWrapper.ge(SmtHalfExitCountry::getSubmitSuccessTime, queryVO.getSubmitSuccessStartTime());
        }
        if (Objects.nonNull(queryVO.getSubmitSuccessEndTime())) {
            queryWrapper.le(SmtHalfExitCountry::getSubmitSuccessTime, queryVO.getSubmitSuccessEndTime());
        }

        // 同步时间范围查询
        if (Objects.nonNull(queryVO.getSyncStartTime())) {
            queryWrapper.ge(SmtHalfExitCountry::getSynchTime, queryVO.getSyncStartTime());
        }
        if (Objects.nonNull(queryVO.getSyncEndTime())) {
            queryWrapper.le(SmtHalfExitCountry::getSynchTime, queryVO.getSyncEndTime());
        }
        // 默认按添加时间倒序排列
        queryWrapper.orderByDesc(SmtHalfExitCountry::getCreatedTime);

        return queryWrapper;
    }

    @Override
    public IPage<SmtHalfExitCountry> list(SmtHalfExitCountryQueryVO queryVO) {
        // 权限校验
        isAuth(queryVO);
        LambdaQueryWrapper<SmtHalfExitCountry> queryWrapper = getPageQueryWrapper(queryVO);
        // 执行分页查询
        return smtHalfExitCountryMapper.selectPage(new Page<>(queryVO.getPageNum(), queryVO.getPageSize()), queryWrapper);
    }

    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtHalfExitCountry> wrapper) {
        List<Map<Object, Object>> tidbPageMetaMap = smtHalfExitCountryMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }

    /**
     * 权限校验
     * 
     * @param queryVO 查询条件
     */
    private void isAuth(SmtHalfExitCountryQueryVO queryVO) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        // 查询销售为空
        if (!superAdminOrEquivalent.getResult() && CollectionUtils.isEmpty(queryVO.getShopList()) && CollectionUtils.isEmpty(queryVO.getCreatorList())) {
            // 获取当前权限下的销售人员
            List<String> currentPermissionEmployeeNo = permissionsHelper.getCurrentUserEmployeeNoPermission(null, null, null, SaleChannel.CHANNEL_SMT);
            queryVO.setCreatorList(currentPermissionEmployeeNo);
        }
    }

    /**
     * 导出功能
     * 
     * @param dto 导出参数
     * @return ApiResult<?> 导出结果
     */
    @Override
    public ApiResult<String> download(SmtHalfExitCountryQueryVO dto) {
        try {
            int maxRow = 500000;
            dto.setPageNum(1);
            dto.setPageSize(1);

            // 判断是否有权限
            isAuth(dto);
            IPage<SmtHalfExitCountry> page = this.list(dto);
            long total = page.getTotal();
            if (total == 0) {
                return ApiResult.newError("导出数据为空");
            }
            if (total > maxRow) {
                return ApiResult.newError("导出数据超过" + maxRow + "条，请缩小查询条件");
            }
            excelSend.downHalfExitCountry(ExcelTypeEnum.downHalfExitCountry.getCode(), dto);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 更新数据
     * 
     * @param smtHalfExitCountry 数据对象
     * @return boolean 是否更新成功
     */
    @Override
    public boolean updateBySubmitStatus(SmtHalfExitCountry smtHalfExitCountry) {
        // 校验必填字段
        if (smtHalfExitCountry.getId() == null || smtHalfExitCountry.getSubmitStatus() == null) {
            throw new IllegalArgumentException("ID和审核状态为必填项");
        }

        // 获取当前时间
        Date now = new Date();
        // 设置更新时间
        smtHalfExitCountry.setUpdatedTime(now);

        // 根据提交状态处理不同情况
        switch (smtHalfExitCountry.getSubmitStatus()) {
            case 1: // 提交成功
                // 设置提交成功时间
                smtHalfExitCountry.setSubmitSuccessTime(now);
                smtHalfExitCountry.setExamineStatus(HalfExitCountryExamineStatusEnum.S_0.getCode());

                //1.获取 店铺和产品id
                String account = smtHalfExitCountry.getAccount();
                Long productId = smtHalfExitCountry.getProductId();
                //2.根据 店铺和产品id 调用 EsAliexpressProductListingService  getEsAliexpressProductListing 只用查询 id 字段
                EsAliexpressProductListingRequest esAliexpressProductListingRequest = new EsAliexpressProductListingRequest();
                esAliexpressProductListingRequest.setAliexpressAccountNumber(account);
                esAliexpressProductListingRequest.setProductId(productId);
                esAliexpressProductListingRequest.setQueryFields(new String[]{"id"});
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(esAliexpressProductListingRequest);
                if(CollectionUtils.isEmpty(esAliexpressProductListing)){
                    log.info("根据店铺和产品id获取不到EsAliexpressProductListing:" + productId);
                    break;
                }

                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("halfCountryExitLabel", HalfExitCountryLabelEnum.S_1.getCode());
                String jsonString = JSON.toJSONString(updateMap);

                //3.调用  EsAliexpressProductListingService 中的方法 updateRequest 循环更新
                for (EsAliexpressProductListing productListing : esAliexpressProductListing) {
                    esAliexpressProductListingService.updateRequest(jsonString, productListing.getId());
                }

                //4.需要调整库存为0
                List<String> idList = esAliexpressProductListing.stream().map(t -> t.getId()).collect(Collectors.toList());
                aliexpressEsExtendService.batchEditProductStock(idList, "0", false, false, true,null, false);


                SmtHalfExitCountry dbSmtHalfExitCountry = this.getById(smtHalfExitCountry.getId());

                //5.同步半托管产品
                aliexpressHalfExitCountryHelper.halfCountryExitCheck(dbSmtHalfExitCountry);

                break;
            case 2: // 提交失败
                // 提交状态为2时，需要设置更新时间和失败备注（如果提供）
                if (smtHalfExitCountry.getFailRemark() != null && !smtHalfExitCountry.getFailRemark().isEmpty()) {
                    smtHalfExitCountry.setFailRemark(smtHalfExitCountry.getFailRemark());
                }
                break;
            default:
                // 对于其他状态，可选地抛出异常或进行默认处理
                break;
        }

        // 执行更新操作
        return this.updateById(smtHalfExitCountry);
    }
}