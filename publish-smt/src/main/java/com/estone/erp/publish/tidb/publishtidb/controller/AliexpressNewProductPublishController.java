package com.estone.erp.publish.tidb.publishtidb.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressNewProductPublishDo;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressNewProductPublishVo;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressNewProductPublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.controller
 * @Author: sj
 * @CreateTime: 2025-03-13  09:42
 * @Description: 刊登次数达成率
 */

@Slf4j
@RestController
@RequestMapping("/aliexpressNewProductPublish")
public class AliexpressNewProductPublishController {

    @Resource
    private AliexpressNewProductPublishService aliexpressNewProductPublishService;


    /**
     *  分页查询
     * @param query
     * @return
     */
    @PostMapping
    public CQueryResult<AliexpressNewProductPublishVo> queryPage(@RequestBody CQuery<AliexpressNewProductPublishDo> query) {
        try {
            return aliexpressNewProductPublishService.queryPage(query);
        } catch (Exception e) {
            return CQueryResult.failResult(e.getMessage());
        }
    }

    /**
     * 导出
     */
    @PostMapping("/download")
    public ApiResult<String> download(@RequestBody CQuery<AliexpressNewProductPublishDo> query) {
        try {
            return aliexpressNewProductPublishService.download(query);
        } catch (Exception e) {
            log.error("导出失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }
}
