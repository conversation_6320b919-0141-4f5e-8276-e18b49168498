package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class LayeredAssessmentDetailVo {
    /**
     * 分层标题
     */
    private String title;

    /**
     * 主营主赚类目
     */
    private String cates;

    /**
     * 得分
     */
    private String layerScore;

    /**
     * 层级
     */
    private String layerLevel;

    /**
     * 更新时间
     */
    private String sysUpdateTime;

    /**
     * tab列表
     */
    private List<TabInfo> tabInfos;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LayeredAssessmentDetailVo that = (LayeredAssessmentDetailVo) o;
        return Objects.equals(title, that.title) &&
                Objects.equals(cates, that.cates) &&
                Objects.equals(layerScore, that.layerScore) &&
                Objects.equals(layerLevel, that.layerLevel) &&
                Objects.equals(sysUpdateTime, that.sysUpdateTime) &&
                areTabInfosEqual(that.tabInfos);
    }

    @Override
    public int hashCode() {
        // 计算所有字段的哈希值，忽略 tabInfos 顺序
        return Objects.hash(
                title,
                cates,
                layerScore,
                layerLevel,
                sysUpdateTime,
                tabInfosHashCode(tabInfos) // 计算 tabInfos 的哈希值，忽略顺序
        );
    }

    /**
     * 比较 tabInfos 列表时，忽略顺序
     */
    private boolean areTabInfosEqual(List<TabInfo> otherTabInfos) {
        if (this.tabInfos == null && otherTabInfos == null) return true;
        if (this.tabInfos == null || otherTabInfos == null) return false;
        Set<TabInfo> thisSet = new HashSet<>(this.tabInfos);
        Set<TabInfo> otherSet = new HashSet<>(otherTabInfos);
        return thisSet.equals(otherSet);
    }

    /**
     * 计算 tabInfos 的 hashCode，忽略顺序
     */
    private int tabInfosHashCode(List<TabInfo> tabInfos) {
        if (tabInfos == null) return 0;
        Set<TabInfo> tabInfoSet = new HashSet<>(tabInfos);
        return tabInfoSet.hashCode();
    }

    @Data
    public static class TabInfo {

        /**
         * tab名称
         */
        private String tabName;

        /**
         * 分国家指标权益
         */
        private String indexEquity;

        /**
         * 层级
         */
        private String indexEquityLevel;

        /**
         * 指标明细列表
         */
        private List<IndicatorDetail> indicatorDetails;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TabInfo tabInfo = (TabInfo) o;
            return Objects.equals(tabName, tabInfo.tabName) &&
                    Objects.equals(indexEquity, tabInfo.indexEquity) &&
                    Objects.equals(indexEquityLevel, tabInfo.indexEquityLevel) &&
                    areIndicatorDetailsEqual(tabInfo.indicatorDetails);
        }

        @Override
        public int hashCode() {
            return Objects.hash(tabName, indexEquity, indexEquityLevel, indicatorDetailsHashCode(indicatorDetails));
        }

        /**
         * 比较 indicatorDetails 列表时，忽略顺序
         */
        private boolean areIndicatorDetailsEqual(List<IndicatorDetail> otherDetails) {
            if (this.indicatorDetails == null && otherDetails == null) return true;
            if (this.indicatorDetails == null || otherDetails == null) return false;
            Set<IndicatorDetail> thisSet = new HashSet<>(this.indicatorDetails);
            Set<IndicatorDetail> otherSet = new HashSet<>(otherDetails);
            return thisSet.equals(otherSet);
        }

        /**
         * 计算 indicatorDetails 的 hashCode，忽略顺序
         */
        private int indicatorDetailsHashCode(List<IndicatorDetail> indicatorDetails) {
            if (indicatorDetails == null) return 0;
            Set<IndicatorDetail> indicatorDetailSet = new HashSet<>(indicatorDetails);
            return indicatorDetailSet.hashCode();
        }
    }

    @Data
    public static class IndicatorDetail {

        /**
         * 指标名称
         */
        private String label;

        /**
         * 指标值
         */
        private String value;

        /**
         * 单项指标得分
         */
        private String score;

        /**
         * 指标权重
         */
        private String weight;
    }

}


