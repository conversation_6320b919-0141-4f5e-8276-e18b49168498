package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.*;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionTotal;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressTaskExecutionTotalService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/aliexpressTaskExecutionTotal")
public class AliexpressTaskExecutionTotalController {

    @Resource
    private AliexpressTaskExecutionTotalService aliexpressTaskExecutionTotalService;
    @PostMapping("/pageQuery")
    public ApiResult<IPage<AliexpressTaskExecutionTotal>> pageQuery(@RequestBody AliexpressTaskExecutionTotalQueryDto dto) {
        IPage<AliexpressTaskExecutionTotal> page = aliexpressTaskExecutionTotalService.pageQuery(dto);
        return ApiResult.newSuccess(page);
    }

    @PostMapping("/getHistory")
    public ApiResult<List<AliexpressTaskExecutionDetailsToTotalHistoryDto>> getHistory(@RequestBody AliexpressTaskExecutionTotalDateRangeDto dto) {
        List<AliexpressTaskExecutionDetailsToTotalHistoryDto> list = aliexpressTaskExecutionTotalService.getHistory(dto);
        return ApiResult.newSuccess(list);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody AliexpressTaskExecutionTotalExportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        IPage<AliexpressTaskExecutionTotal> page = aliexpressTaskExecutionTotalService.pageQuery(dto);
        ResponseJson responseJson = aliexpressTaskExecutionTotalService.export(dto);
        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }

}
