package com.estone.erp.publish.smt.util;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.smt.model.AliSystemCategoryMapping;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressCategoryExample;
import com.estone.erp.publish.smt.service.AliSystemCategoryMappingService;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.response.ProductCategoryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.List;

@Slf4j
public class AliexpressSystemCategoryMappingUtils {

    private static AliSystemCategoryMappingService aliSystemCategoryMappingService = SpringUtils.getBean(AliSystemCategoryMappingService.class);

    private static AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

    public static List<ProductCategoryResponse> getDataList(){
        ApiResult<List<ProductCategoryResponse>> allSonFullPathResult = ProductUtils.getAllSonFullPath();
        if(!allSonFullPathResult.isSuccess()){
            log.error(allSonFullPathResult.getErrorMsg());
            return null;
        }
        return allSonFullPathResult.getResult();
    }

    public static void createData(List<ProductCategoryResponse> categoryResponseList, String userName){
        log.warn("产品分类 size:" + categoryResponseList.size());
        int i = 0;
        for (ProductCategoryResponse productCategoryResponse : categoryResponseList) {
            try {
                log.warn("产品分类 size:" + categoryResponseList.size() + "处理到:" + ++i);

                String fullpath = productCategoryResponse.getFullpath();
                String fullpathcode = productCategoryResponse.getFullpathcode();
                Boolean enable = productCategoryResponse.getIs_enable();
                if(StringUtils.isBlank(fullpathcode) || StringUtils.isBlank(fullpath)){
                    continue;
                }
                List<String> strings = CommonUtils.splitList(fullpathcode, "_");
                if(strings.size() < 2){
                    continue;
                }
                String code = strings.get(strings.size() - 1);
                String parentCode = strings.get(strings.size() - 2);

                AliSystemCategoryMapping aliSystemCategoryMapping = aliSystemCategoryMappingService.selectByCode(code);
                if(aliSystemCategoryMapping != null){
                    continue;
                }

                aliSystemCategoryMapping = new AliSystemCategoryMapping();
                aliSystemCategoryMapping.setEnable(enable); //默认启用
                aliSystemCategoryMapping.setSystemCategoryFullCode(fullpathcode);
                aliSystemCategoryMapping.setSystemCategoryFullName(fullpath);
                aliSystemCategoryMapping.setSystemCategoryCode(code);
                aliSystemCategoryMapping.setSystemCategoryParentCode(parentCode);
                aliSystemCategoryMapping.setCreateBy(userName);
                aliSystemCategoryMapping.setCreateDate(new Timestamp(System.currentTimeMillis()));

                //用完整类目名，查询smt分类
                AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                categoryExample.createCriteria().andIsShowEqualTo(true).andFullCnNameEqualTo(fullpath);
                List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                if(CollectionUtils.isNotEmpty(aliexpressCategories)){
                    Integer categoryId = aliexpressCategories.get(0).getCategoryId();
                    String fullCnName = aliexpressCategories.get(0).getFullCnName();
                    aliSystemCategoryMapping.setPlatformCategoryCodes("," + categoryId + ",");
                    aliSystemCategoryMapping.setPlatformCategoryNames(fullCnName);
                }
                aliSystemCategoryMappingService.insert(aliSystemCategoryMapping);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

}
