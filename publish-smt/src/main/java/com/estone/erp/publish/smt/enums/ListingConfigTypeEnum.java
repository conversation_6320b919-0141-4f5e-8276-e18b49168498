package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang3.ObjectUtils;

public enum ListingConfigTypeEnum {
    s_join_half(1, "加入半托管"),
    s_pop(2, "pop配置"),
    s_pop_spu(3, "pop指定spu"),
    s_pop_off(4, "pop下架"),
    pop_adjust_inventory(5, "pop调库存"),
    half_adjust_inventory(6, "半托管调库存"),
    pop_adjust_price(7, "pop调价")
    ;
    private int code;

    private String name;

    private ListingConfigTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ListingConfigTypeEnum build(int code) {
        ListingConfigTypeEnum[] values = values();
        for (ListingConfigTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        if (ObjectUtils.isEmpty(code)){
            return null;
        }
        ListingConfigTypeEnum[] values = values();
        for (ListingConfigTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
