package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

public enum ProductStatusTypeEnum {
    onSelling("onSelling", "上架"),
    offline("offline", "下架"),
    auditing("auditing", "审核中"),
    editingRequired("editingRequired", "审核不通过"),
    delete("delete", "删除"),
    ;

    private String code;
    private String name;

    private ProductStatusTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        ProductStatusTypeEnum[] values = values();
        for (ProductStatusTypeEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static ProductStatusTypeEnum build(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        ProductStatusTypeEnum[] values = values();
        for (ProductStatusTypeEnum type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
