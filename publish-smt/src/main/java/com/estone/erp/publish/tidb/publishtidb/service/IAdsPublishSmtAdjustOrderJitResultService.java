package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustOrderJitResult;

import java.util.List;

public interface IAdsPublishSmtAdjustOrderJitResultService extends IService<AdsPublishSmtAdjustOrderJitResult> {

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AdsPublishSmtAdjustOrderJitResult> wrapper);

}
