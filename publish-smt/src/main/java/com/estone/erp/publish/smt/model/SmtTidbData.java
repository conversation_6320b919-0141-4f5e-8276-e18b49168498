package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtTidbData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_tidb_data.id
     */
    private Long id;

    /**
     * 账号 database column smt_tidb_data.account
     */
    private String account;

    /**
     * 产品id database column smt_tidb_data.product_id
     */
    private Long productId;

    /**
     * 货号 database column smt_tidb_data.sku
     */
    private String sku;

    /**
     * sku_id database column smt_tidb_data.sku_id
     */
    private String skuId;

    /**
     * 国家code database column smt_tidb_data.country_code
     */
    private String countryCode;

    /**
     * 区域化国家 database column smt_tidb_data.is_area
     */
    private Boolean isArea;

    /**
     * 价格 database column smt_tidb_data.price
     */
    private Double price;

    /**
     * 运费模板id database column smt_tidb_data.template_id
     */
    private Long templateId;

    /**
     * 状态 database column smt_tidb_data.upload_state
     */
    private Integer uploadState;

    /**
     * 创建时间 database column smt_tidb_data.created_time
     */
    private Timestamp createdTime;

    /**
     * 更新时间 database column smt_tidb_data.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 唯一id
     */
    private String uniqueId;
}