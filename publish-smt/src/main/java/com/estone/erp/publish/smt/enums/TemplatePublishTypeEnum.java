package com.estone.erp.publish.smt.enums;

public enum TemplatePublishTypeEnum {
    t_1(1, "POP模板"),
    t_2(2, "半托管模板"),
    ;
    private int code;

    private String name;

    private TemplatePublishTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TemplatePublishTypeEnum build(int code) {
        TemplatePublishTypeEnum[] values = values();
        for (TemplatePublishTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        TemplatePublishTypeEnum[] values = values();
        for (TemplatePublishTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
