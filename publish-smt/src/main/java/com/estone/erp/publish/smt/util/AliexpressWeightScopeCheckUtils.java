package com.estone.erp.publish.smt.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.estone.erp.publish.smt.model.SmtConfigHalfPriceInterval;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.estone.erp.publish.smt.model.AliexpressConfigInfo;

/**
 * 账号配置重量标签校验
 * <AUTHOR>
 *
 */
public class AliexpressWeightScopeCheckUtils {

    public static String getCheckScopeMsg(List<AliexpressConfigInfo> infoList) {
        Map<String, List<Map<String, Double>>> tagWeightMap = new HashMap<>();
        for (AliexpressConfigInfo info : infoList) {
            if(info.getFromWeight() == null) {
                return "重量范围有误：左范围为空，请修改后重新保存！";
            }
            if(info.getToWeight() != null && info.getFromWeight().compareTo(info.getToWeight()) > 0) {
                return "重量范围有误：左大于右，请修改后重新保存！";
            }
            if(StringUtils.isNotBlank(info.getTagCodes())) {
                String[] tagList = StringUtils.split(info.getTagCodes(), ",");
                for (String tag : tagList) {
                    List<Map<String, Double>> weightMapList;
                    if(tagWeightMap.containsKey(tag)) {
                        weightMapList = tagWeightMap.get(tag);
                        for (Map<String, Double> map : weightMapList) {
                            Double fromW = map.get("fromWeight");
                            Double toW = map.get("toWeight");
                            if((gtOrEqThan(info.getFromWeight(), fromW) && ltThan(info.getFromWeight(), toW)) //左在范围内
                                    || (gtOrEqThan(info.getToWeight(), fromW) && ltThan(info.getToWeight(), toW)) //右在范围内
                                    || (ltThan(info.getFromWeight(), fromW) && gtOrEqThan(info.getToWeight(), toW)) //左小于左，右大于右
                                    ) {
                                return  String.format("产品标签在重量范围[%s - %s][%s - %s]存在重复，请修改后重新保存！", info.getFromWeight(),
                                        info.getToWeight() == null ? "无穷大" : info.getToWeight(), fromW, toW == null ? "无穷大" : toW);
                            }
                        }
                        Map<String, Double> weightMap = new HashMap<>();
                        weightMap.put("fromWeight", info.getFromWeight());
                        weightMap.put("toWeight", info.getToWeight());
                        weightMapList.add(weightMap);
                    }else {
                        weightMapList = new ArrayList<>();
                        Map<String, Double> weightMap = new HashMap<>();
                        weightMap.put("fromWeight", info.getFromWeight());
                        weightMap.put("toWeight", info.getToWeight());
                        weightMapList.add(weightMap);
                    }
                    tagWeightMap.put(tag, weightMapList);
                }
            }else {
                String tag = "普货";
                List<Map<String, Double>> weightMapList;
                if(tagWeightMap.containsKey(tag)) {
                    weightMapList = tagWeightMap.get(tag);
                    for (Map<String, Double> map : weightMapList) {
                        Double fromW = map.get("fromWeight");
                        Double toW = map.get("toWeight");
                        if((gtOrEqThan(info.getFromWeight(), fromW) && ltThan(info.getFromWeight(), toW)) //左在范围内
                                || (gtOrEqThan(info.getToWeight(), fromW) && ltThan(info.getToWeight(), toW)) //右在范围内
                                || (ltThan(info.getFromWeight(), fromW) && gtOrEqThan(info.getToWeight(), toW)) //左小于左，右大于右
                                ) {
                            return  String.format("产品标签在重量范围[%s - %s][%s - %s]存在重复，请修改后重新保存！", info.getFromWeight(),
                                    info.getToWeight() == null ? "无穷大" : info.getToWeight(), fromW, toW == null ? "无穷大" : toW);
                        }
                    }
                    Map<String, Double> weightMap = new HashMap<>();
                    weightMap.put("fromWeight", info.getFromWeight());
                    weightMap.put("toWeight", info.getToWeight());
                    weightMapList.add(weightMap);
                }else {
                    weightMapList = new ArrayList<>();
                    Map<String, Double> weightMap = new HashMap<>();
                    weightMap.put("fromWeight", info.getFromWeight());
                    weightMap.put("toWeight", info.getToWeight());
                    weightMapList.add(weightMap);
                }
                tagWeightMap.put(tag, weightMapList);
            }
        }
        return null;
    }


    /**
     * 半托管价格毛利率配置重叠校验
     * @param configHalfPriceIntervalList
     * @return
     */
    public static String getCheckHalfPriceInterva( List<SmtConfigHalfPriceInterval> configHalfPriceIntervalList) {
        if(CollectionUtils.isEmpty(configHalfPriceIntervalList)){
            return null;
        }

        //重量区间有可能为空，默认设置了填写重量区间就都有值。 把重量区间为空的数据单独出来比较 销售成本价 有没有重叠
        List<SmtConfigHalfPriceInterval> nullWeightList = configHalfPriceIntervalList.stream().filter(t -> t.getFromWeight() == null).collect(Collectors.toList());
        List<SmtConfigHalfPriceInterval> notNullWeightList = configHalfPriceIntervalList.stream().filter(t -> t.getFromWeight() != null).collect(Collectors.toList());

        //只有销售成本价区间
        if(CollectionUtils.isNotEmpty(nullWeightList)){
            for (int i = 0; i < nullWeightList.size(); i++) {
                SmtConfigHalfPriceInterval smtConfigHalfPriceInterval = nullWeightList.get(i);
                Double fromPrice = smtConfigHalfPriceInterval.getFromPrice();
                Double toPrice = smtConfigHalfPriceInterval.getToPrice();
                for (int i1 = 0; i1 < nullWeightList.size(); i1++) {
                    //list 有序，一直比较下面的数据
                    if(i1 > i){
                        SmtConfigHalfPriceInterval smtConfigHalfPriceInterval1 = nullWeightList.get(i1);
                        Double fromPrice1 = smtConfigHalfPriceInterval1.getFromPrice();
                        Double toPrice1 = smtConfigHalfPriceInterval1.getToPrice();
                        boolean b = fromPrice1 >= fromPrice && fromPrice1 < toPrice;
                        boolean b1 = toPrice1 <= toPrice && toPrice1 > fromPrice;
                        if(b || b1){
                            return String.format("销售成本价范围[%s - %s][%s - %s]存在重复，请修改后重新保存！", fromPrice,
                                    toPrice, fromPrice1, toPrice1);
                        }
                    }
                }
            }
        }

        //重量必有值
        if(CollectionUtils.isNotEmpty(notNullWeightList)){
            for (int i = 0; i < notNullWeightList.size(); i++) {
                SmtConfigHalfPriceInterval smtConfigHalfPriceInterval = notNullWeightList.get(i);
                Double fromPrice = smtConfigHalfPriceInterval.getFromPrice();
                Double toPrice = smtConfigHalfPriceInterval.getToPrice();
                Double fromWeight = smtConfigHalfPriceInterval.getFromWeight();
                Double toWeight = smtConfigHalfPriceInterval.getToWeight();

                for (int i1 = 0; i1 < notNullWeightList.size(); i1++) {
                    if(i1 > i){
                        SmtConfigHalfPriceInterval smtConfigHalfPriceInterval1 = notNullWeightList.get(i1);
                        Double fromPrice1 = smtConfigHalfPriceInterval1.getFromPrice();
                        Double toPrice1 = smtConfigHalfPriceInterval1.getToPrice();
                        Double fromWeight1 = smtConfigHalfPriceInterval1.getFromWeight();
                        Double toWeight1 = smtConfigHalfPriceInterval1.getToWeight();

                        //这个是销售价格区间的重叠
                        boolean b = fromPrice1 >= fromPrice && fromPrice1 < toPrice;
                        boolean b1 = toPrice1 <= toPrice && toPrice1 > fromPrice;

                        //这个是重量区间的重叠
                        boolean c = fromWeight1 >= fromWeight && fromWeight1 < toWeight;
                        boolean c1 = toWeight1 <= toWeight && toWeight1 > fromWeight;

                        //判断标准，销售价格区间重叠 并且重量区间重叠
                        if((b || b1) && (c || c1)){
                            return String.format("销售成本价范围[%s - %s] 重量区间[%s - %s] 与 销售成本价范围[%s - %s] 重量区间 [%s - %s]存在重复，请修改后重新保存！", fromPrice,
                                    toPrice, fromWeight, toWeight, fromPrice1, toPrice1, fromWeight1, toWeight1);
                        }

                    }
                }
            }
        }
        return null;
    }

    /**
     * A大于等于B,null为无穷大
     * @param numA
     * @param numB
     * @return
     */
    private static Boolean gtOrEqThan(Double numA, Double numB) {
        if(numA == null) {
            return true;
        }
        if(numB == null) {
            return false;
        }
        return !(numA.compareTo(numB) < 0);
    }

    /**
     * A小于B,null为无穷大
     * @param numA
     * @param numB
     * @return
     */
    private static Boolean ltThan(Double numA, Double numB) {
        if(numA == null) {
            return false;
        }
        if(numB == null) {
            return true;
        }
        return numA.compareTo(numB) < 0;
    }
}
