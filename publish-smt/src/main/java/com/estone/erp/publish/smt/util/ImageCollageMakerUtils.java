package com.estone.erp.publish.smt.util;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.service.PictureUploadService;
import com.estone.erp.publish.common.PictureCommon;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImageCollageMakerUtils {
    private static PictureUploadService pictureUploadService = SpringUtils.getBean(PictureUploadService.class);

    public static String createAndUploadCollage(List<String> imageUrls) throws Exception {
        // 生成拼接图片
        BufferedImage collage = createCollage(imageUrls);

        // 将 BufferedImage 转换为 MultipartFile
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(collage, "jpg", baos);
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        String outputFileName = "collage_" + System.currentTimeMillis() + ".jpg";
        MultipartFile multipartFile = new MockMultipartFile(
                outputFileName, outputFileName, "image/jpeg", bais);

        // 调用上传方法
        ApiResult<?> result = uploadPicture(multipartFile);
        String imgUrl = "";
        if (result.isSuccess() && !ObjectUtils.isEmpty(result.getResult())) {
            imgUrl = (String) ((HashMap<?, ?>) result.getResult()).get("imgUrl");
        }
        return imgUrl;
    }

    private static BufferedImage createCollage(List<String> imageUrls) throws IOException {
        int imageCount = imageUrls.size();
        BufferedImage result = new BufferedImage(1024, 1024, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = result.createGraphics();
        g2d.setBackground(Color.WHITE);
        g2d.clearRect(0, 0, 1024, 1024);

        switch (imageCount) {
            case 2:
                createTwoImageDiagonalLayout(g2d, imageUrls);
                break;
            case 3:
                createThreeImageLayout(g2d, imageUrls);
                break;
            case 4:
                createFourImageLayout(g2d, imageUrls);
                break;
            case 6:
                createSixImageLayout(g2d, imageUrls);
                break;
            case 9:
                createNineImageLayout(g2d, imageUrls);
                break;
            default:
                throw new IllegalArgumentException("Unsupported number of images: " + imageCount);
        }

        g2d.dispose();
        return result;
    }

    // 2 张图片布局：2x2 四宫格，对角线（左上和右下）
    private static void createTwoImageDiagonalLayout(Graphics2D g2d, List<String> imageUrls) throws IOException {
        BufferedImage topLeft = loadImage(imageUrls.get(0));
        BufferedImage bottomRight = loadImage(imageUrls.get(1));

        topLeft = scaleImageProportionally(topLeft, 512, 512);
        bottomRight = scaleImageProportionally(bottomRight, 512, 512);

        g2d.drawImage(topLeft, 0, 0, null);
        g2d.drawImage(bottomRight, 512, 512, null);
    }

    // 3 张图片布局：品字形，上1居中，下2居中，两侧留白
    private static void createThreeImageLayout(Graphics2D g2d, List<String> imageUrls) throws IOException {
        BufferedImage topImage = loadImage(imageUrls.get(0));
        BufferedImage bottomLeft = loadImage(imageUrls.get(1));
        BufferedImage bottomRight = loadImage(imageUrls.get(2));

        // 顶部图片保持512x512
        topImage = scaleImageProportionally(topImage, 512, 512);

        // 底部两张图片都设置为512x512
        bottomLeft = scaleImageProportionally(bottomLeft, 512, 512);
        bottomRight = scaleImageProportionally(bottomRight, 512, 512);

        // 计算宫格位置
        int gridWidth = 1024 / 3;
        int firstGridCenter = gridWidth / 2;
        int thirdGridCenter = gridWidth * 2 + gridWidth / 2;

        // 计算底部图片位置
        // 图2：左侧对齐第一个宫格中间
        int image2X = firstGridCenter - 512 / 2;
        image2X = 0;

        int image3X = thirdGridCenter - 512 / 2;
        // 检查是否会超出画布
        if (image3X + 512 > 1024) {
            image3X = 1024 - 512;
        }

        // 绘制顶部图片居中
        int topX = (1024 - 512) / 2;
        g2d.drawImage(topImage, topX, 0, null);

        // 绘制底部图片
        g2d.drawImage(bottomLeft, image2X, 512, null);
        g2d.drawImage(bottomRight, image3X, 512, null);
    }

    // 4 张图片布局：2x2 四宫格
    private static void createFourImageLayout(Graphics2D g2d, List<String> imageUrls) throws IOException {
        for (int i = 0; i < 4; i++) {
            BufferedImage image = loadImage(imageUrls.get(i));
            image = scaleImageProportionally(image, 512, 512);
            int x = (i % 2) * 512;
            int y = (i / 2) * 512;
            g2d.drawImage(image, x, y, null);
        }
    }

    // 6 张图片布局：2x3 六宫格
    private static void createSixImageLayout(Graphics2D g2d, List<String> imageUrls) throws IOException {
        for (int i = 0; i < 6; i++) {
            BufferedImage image = loadImage(imageUrls.get(i));
            image = scaleImageProportionally(image, 512, 341);
            int x = (i % 3) * 341;
            int y = (i / 3) * 512;
            g2d.drawImage(image, x, y, null);
        }
    }

    // 9 张图片布局：3x3 九宫格
    private static void createNineImageLayout(Graphics2D g2d, List<String> imageUrls) throws IOException {
        for (int i = 0; i < 9; i++) {
            BufferedImage image = loadImage(imageUrls.get(i));
            image = scaleImageProportionally(image, 341, 341);
            int x = (i % 3) * 341;
            int y = (i / 3) * 341;
            g2d.drawImage(image, x, y, null);
        }
    }

    // 从 URL 加载图片
    private static BufferedImage loadImage(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        return ImageIO.read(url);
    }

    // 等比缩放图片到目标尺寸
    private static BufferedImage scaleImageProportionally(BufferedImage original, int targetWidth, int targetHeight) {
        double widthRatio = (double) targetWidth / original.getWidth();
        double heightRatio = (double) targetHeight / original.getHeight();
        double ratio = Math.min(widthRatio, heightRatio);

        int scaledWidth = (int) (original.getWidth() * ratio);
        int scaledHeight = (int) (original.getHeight() * ratio);

        BufferedImage scaled = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaled.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, scaledWidth, scaledHeight, null);
        g2d.dispose();

        BufferedImage result = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        g2d = result.createGraphics();
        g2d.setBackground(Color.WHITE);
        g2d.clearRect(0, 0, targetWidth, targetHeight);
        int x = (targetWidth - scaledWidth) / 2;
        int y = (targetHeight - scaledHeight) / 2;
        g2d.drawImage(scaled, x, y, null);
        g2d.dispose();

        return result;
    }

    // 上传图片
    private static ApiResult<?> uploadPicture(MultipartFile file) {
        String url = PictureCommon.SMT_PICTURE_ROUTE;
        String fileName = file.getOriginalFilename();
        try {
            Map<String, Object> rspMap = pictureUploadService.uploadPicture(url, file, null);
            if (rspMap == null) {
                return ApiResult.newError("上传失败");
            }
            if (rspMap.get("success").equals(true)) {
                Map<String, Object> body = new HashMap<>();
                body.put("imgUrl", rspMap.get("result").toString());
                body.put("fileName", fileName);
                return ApiResult.newSuccess(body);
            }
        } catch (Exception e) {
            return ApiResult.newError("上传失败:" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }

}