package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class UpdateStockTimeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column update_stock_time_record.id
     */
    private Long id;

    /**
     * eg:pop database column update_stock_time_record.update_type
     */
    private String updateType;

    /**
     * 开始时间 database column update_stock_time_record.begin_date
     */
    private Timestamp beginDate;

    /**
     * 结束时间 database column update_stock_time_record.end_date
     */
    private Timestamp endDate;

    /**
     * 耗时分钟 database column update_stock_time_record.time_minute
     */
    private Integer timeMinute;

    /**
     * 时间频率 database column update_stock_time_record.time_interval
     */
    private Integer timeInterval;

    /**
     * 是否异常 database column update_stock_time_record.is_warn
     */
    private Boolean isWarn;

    /**
     * 创建时间 database column update_stock_time_record.created_date
     */
    private Timestamp createdDate;
}