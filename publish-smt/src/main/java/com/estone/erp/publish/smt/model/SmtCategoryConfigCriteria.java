package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-12-12 10:24:00
 */
@Data
public class SmtCategoryConfigCriteria extends SmtCategoryConfig {
    private static final long serialVersionUID = 1L;

    private List<Integer> categoryIdList;

    public SmtCategoryConfigExample getExample() {
        SmtCategoryConfigExample example = new SmtCategoryConfigExample();
        SmtCategoryConfigExample.Criteria criteria = example.createCriteria();
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (StringUtils.isNotBlank(this.getCategoryFullPathCode())) {
            criteria.andCategoryFullPathCodeEqualTo(this.getCategoryFullPathCode());
        }
        if (StringUtils.isNotBlank(this.getCategoryCnFullName())) {
            criteria.andCategoryCnFullNameEqualTo(this.getCategoryCnFullName());
        }
        if (StringUtils.isNotBlank(this.getAttributeShowTypeValue())) {
            criteria.andAttributeShowTypeValueEqualTo(this.getAttributeShowTypeValue());
        }
        if (this.getAttrNameId() != null) {
            criteria.andAttrNameIdEqualTo(this.getAttrNameId());
        }
        if (StringUtils.isNotBlank(this.getAttrName())) {
            criteria.andAttrNameEqualTo(this.getAttrName());
        }
        if (StringUtils.isNotBlank(this.getAttrNameCnName())) {
            criteria.andAttrNameCnNameEqualTo(this.getAttrNameCnName());
        }
        if (StringUtils.isNotBlank(this.getAttrValueCnNames())) {
            criteria.andAttrValueCnNamesEqualTo(this.getAttrValueCnNames());
        }
        if (StringUtils.isNotBlank(this.getAttrJson())) {
            criteria.andAttrJsonEqualTo(this.getAttrJson());
        }
        if (this.getChildrenAttrNameId() != null) {
            criteria.andChildrenAttrNameIdEqualTo(this.getChildrenAttrNameId());
        }
        if (StringUtils.isNotBlank(this.getChildrenAttrName())) {
            criteria.andChildrenAttrNameEqualTo(this.getChildrenAttrName());
        }
        if (StringUtils.isNotBlank(this.getChildrenAttrNameCnName())) {
            criteria.andChildrenAttrNameCnNameEqualTo(this.getChildrenAttrNameCnName());
        }
        if (StringUtils.isNotBlank(this.getChildrenAttrJson())) {
            criteria.andChildrenAttrJsonEqualTo(this.getChildrenAttrJson());
        }
        if (this.getEnable() != null) {
            criteria.andEnableEqualTo(this.getEnable());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (StringUtils.isNotBlank(this.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(this.getUpdatedBy());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        if (this.getStartedLastTime() != null) {
            criteria.andStartedLastTimeEqualTo(this.getStartedLastTime());
        }
        if (this.getDisabledLastTime() != null) {
            criteria.andDisabledLastTimeEqualTo(this.getDisabledLastTime());
        }

        // 自定义
        if (this.getCategoryIdList() != null && !this.getCategoryIdList().isEmpty()) {
            criteria.andCategoryIdIn(this.getCategoryIdList());
        }
        return example;
    }
}