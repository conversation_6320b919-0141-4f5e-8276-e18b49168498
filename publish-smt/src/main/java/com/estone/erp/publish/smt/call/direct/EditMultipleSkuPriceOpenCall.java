package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/615:18
 */
@Slf4j
public class EditMultipleSkuPriceOpenCall {

    private static String editprice = "aliexpress.offer.product.skuprices.edit";

    public ResponseJson editMultipleSkuPrice(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String productId, String skuIdPriceMap) {
        return EnvironmentSupplierWrapper.execute(()->{
            return executeEditMultipleSkuPrice(saleAccountByAccountNumber, productId, skuIdPriceMap);
        },()->{
            log.info("非正式环境不执行改价,productId:{}, skuIdPriceMap:{}", productId, skuIdPriceMap);
            ResponseJson rsp = new ResponseJson();
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("非正式环境不执行改价");
            return rsp;
        });
    }

    public ResponseJson executeEditMultipleSkuPrice(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String productId, String skuIdPriceMap) {
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        if (saleAccountByAccountNumber == null || StringUtils.isBlank(productId) || StringUtils.isBlank(skuIdPriceMap)) {
            rsp.setMessage("请求参数为空！");
            return rsp;
        }

        Map<String, String> map = JSON.parseObject(skuIdPriceMap, Map.class);
        for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
            String key = stringStringEntry.getKey();
            String value = stringStringEntry.getValue();
            log.info("pop 价格 测试打印 " + value);
            if(StringUtils.isBlank(value)){
                rsp.setMessage("产品id " + productId + " " + key + "价格不能小于等于0！");
                return rsp;
            }
            Double aDouble = Double.valueOf(value);
            log.info("pop 价格 测试打印 " + value);
            if(aDouble == null || aDouble <= 0d){
                rsp.setMessage("产品id " + productId + " " + key + "价格不能小于等于0！");
                return rsp;
            }
        }
        String callRspStr = "";
        try {
            IopRequest request = new IopRequest();
            request.setApiName(editprice);
            request.addApiParameter("product_id", productId);
            request.addApiParameter("sku_id_price_map", skuIdPriceMap);
            long begin = System.currentTimeMillis();
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            callRspStr = iopResponse.getBody();
            long end = System.currentTimeMillis();
            long l = (end - begin) / 1000;
            if (l > AbstractSmtOpenCall.logTime) {
//                log.warn(String.format("skuprices.edit不通过奇门%s秒 rsp%s", l, callRspStr));
            }
            if(StringUtils.isNotBlank(callRspStr)){
                JSONObject callRspJson = JSONObject.parseObject(callRspStr);
                if (callRspJson.containsKey(
                        "aliexpress_offer_product_skuprices_edit_response")) {
                    JSONObject editRsp = callRspJson.getJSONObject(
                            "aliexpress_offer_product_skuprices_edit_response");
                    //响应空 也是成功
                    if(editRsp == null){
                        rsp.setStatus(StatusCode.SUCCESS);
                        return rsp;
                    }
                    if (editRsp.containsKey("result")) {
                        JSONObject result = editRsp.getJSONObject("result");
                        if (result.containsKey("success") && result.getBoolean("success")) {
                            rsp.setStatus(StatusCode.SUCCESS);
                        }
                        if (result.containsKey("error_message")) {
                            String errorCode = result.getString("error_code");
                            String errorMessage = result.getString("error_message");
                            String request_id = result.getString("request_id");
                            if (StringUtils.isNotBlank(errorMessage)) {
                                rsp.setMessage("error_code:" + errorCode + ",error_message:" + errorMessage + ",request_id:" + request_id);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
        if(StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL) && StringUtils.isEmpty(rsp.getMessage())){
            rsp.setMessage(callRspStr);
        }
        return rsp;
    }
}
