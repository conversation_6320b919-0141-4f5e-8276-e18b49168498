package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-06-25 11:20:12
 */
public class LogisticsInfoCriteria extends LogisticsInfo {
    private static final long serialVersionUID = 1L;

    public LogisticsInfoExample getExample() {
        LogisticsInfoExample example = new LogisticsInfoExample();
        LogisticsInfoExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getCode())) {
            criteria.andCodeEqualTo(this.getCode());
        }
        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameEqualTo(this.getName());
        }
        return example;
    }
}