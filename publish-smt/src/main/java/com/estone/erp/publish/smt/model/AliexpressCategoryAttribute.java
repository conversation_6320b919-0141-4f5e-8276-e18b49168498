package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 速卖通类目属性表
 */
@Data
public class AliexpressCategoryAttribute implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID database column aliexpress_category_attribute.id
     */
    private Integer id;

    /**
     * 速卖通类目ID database column aliexpress_category_attribute.category_id
     */
    private Integer categoryId;

    /**
     * 属性ID database column aliexpress_category_attribute.attribute_id
     */
    private Long attributeId;

    /**
     * 属性中文名称 database column aliexpress_category_attribute.attribute_name_zh
     */
    private String attributeNameZh;

    /**
     * 属性英文名称 database column aliexpress_category_attribute.attribute_name_en
     */
    private String attributeNameEn;

    /**
     * 属性类型 database column aliexpress_category_attribute.attribute_type
     */
    private String attributeType;

    /**
     * 是否必填 database column aliexpress_category_attribute.required
     */
    private Boolean required;

    /**
     * 输入类型 database column aliexpress_category_attribute.input_type
     */
    private String inputType;

    /**
     * 是否自定义名称 database column aliexpress_category_attribute.customized_name
     */
    private Boolean customizedName;

    /**
     * 是否可见 database column aliexpress_category_attribute.visible
     */
    private Boolean visible;

    /**
     * 是否自定义图片 database column aliexpress_category_attribute.customized_pic
     */
    private Boolean customizedPic;

    /**
     * 是否关键属性 database column aliexpress_category_attribute.key_attribute
     */
    private Boolean keyAttribute;

    /**
     * 属性展示类型值 database column aliexpress_category_attribute.attribute_show_type_value
     */
    private String attributeShowTypeValue;

    /**
     * 规格 database column aliexpress_category_attribute.spec
     */
    private Integer spec;

    /**
     * 特性 database column aliexpress_category_attribute.features
     */
    private String features;

    /**
     * 是否支持枚举输入 database column aliexpress_category_attribute.support_enum_input
     */
    private Boolean supportEnumInput;

    /**
     * 是否SKU属性 database column aliexpress_category_attribute.sku
     */
    private Boolean sku;

    /**
     * 属性值JSON字符串 database column aliexpress_category_attribute.values_json
     */
    private String valuesJson;

    /**
     * 创建时间 database column aliexpress_category_attribute.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column aliexpress_category_attribute.create_by
     */
    private String createBy;

    /**
     * 修改时间 database column aliexpress_category_attribute.update_date
     */
    private Timestamp updateDate;

    /**
     * 修改人 database column aliexpress_category_attribute.update_by
     */
    private String updateBy;
}
