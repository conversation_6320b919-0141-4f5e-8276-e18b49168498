package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample;
import com.estone.erp.publish.tidb.publishtidb.domain.AliexpressActivityRegistrationVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-07-06 18:07:16
 */
public interface AliexpressActivityRegistrationService extends IService<AliexpressActivityRegistration> {
    int countByExample(AliexpressActivityRegistrationExample example);

    CQueryResult<AliexpressActivityRegistrationVO> search(CQuery<AliexpressActivityRegistrationCriteria> cquery);

    List<AliexpressActivityRegistration> selectByExample(AliexpressActivityRegistrationExample example);

    AliexpressActivityRegistration selectByPrimaryKey(Long id);

    int insert(AliexpressActivityRegistration record);

    int updateByPrimaryKeySelective(AliexpressActivityRegistration record);

    int updateByExampleSelective(AliexpressActivityRegistration record, AliexpressActivityRegistrationExample example);

    int deleteByPrimaryKey(List<Long> ids);

    List<AliexpressActivityRegistration> selectSumFileSizeByExample(AliexpressActivityRegistrationExample example);

    /**
     * 导出列表
     * @param cquery
     * @return
     */
    ResponseJson downloadList(CQuery<AliexpressActivityRegistrationCriteria> cquery);

    /**
     * 导出活动文件
     * @param cquery
     * @return
     */
    ResponseJson downloadActivityFile(CQuery<AliexpressActivityRegistrationCriteria> cquery);

    /**
     * 导入确认文件
     * @param criteria
     * @return
     */
    ResponseJson importActivityRegistrationFile(List<AliexpressActivityRegistrationCriteria> criteria);

    /**
     * 导入确认文件回调接口
     * @param criteria
     * @return
     */
    ResponseJson importActivityRegistrationFileBack(MultipartFile[] files,AliexpressActivityRegistrationCriteria criteria);


}