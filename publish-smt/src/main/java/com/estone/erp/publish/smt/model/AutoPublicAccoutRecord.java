package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AutoPublicAccoutRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column auto_public_accout_record.id
     */
    private Long id;

    /**
     * 店铺 database column auto_public_accout_record.account
     */
    private String account;

    /**
     * 结果 database column auto_public_accout_record.result
     */
    private Boolean result;

    /**
     * 提示 database column auto_public_accout_record.msg
     */
    private String msg;

    /**
     * 创建时间 database column auto_public_accout_record.created_date
     */
    private Timestamp createdDate;

    /**
     * 修改时间 database column auto_public_accout_record.update_date
     */
    private Timestamp updateDate;
}