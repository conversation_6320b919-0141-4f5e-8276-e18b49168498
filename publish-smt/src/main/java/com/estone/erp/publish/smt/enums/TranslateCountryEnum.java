package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * smt需要翻译的语言
 *   pl_PL(波兰语),
 *   en_US(英语),
 *   ru_RU(俄语),
 *   pt_BR(葡语),
 *   fr_FR(法语),
 *   es_ES(西班牙语),
 *   in_ID(印尼语),
 *   it_IT(意大利语),
 *   ar_MA(阿拉伯语),
 *   de_DE(德语),
 *   nl_NL(荷兰语),
 *   ja_JP(日语),
 *   ko_KR(韩语),
 *   th_TH(泰语),
 *   vi_VN(越南语),
 *   iw_IL(希伯来语),
 *   tr_TR(土耳其语)
 * <AUTHOR>
 * @date 2023年6月16日11:23:20
 */
public enum TranslateCountryEnum {

    PT("PT", "葡萄牙", "葡萄牙语", "pt_BR"),
    RU("RU", "俄罗斯", "俄语", "ru_RU"),
    FR("FR", "法国", "法语", "fr_FR"),
    ES("ES", "西班牙", "西班牙语", "es_ES"),
    ID("ID", "印度尼西亚", "印度尼西亚语", "in_ID"),
    IT("IT", "意大利", "意大利语", "it_IT"),
    NP("NP", "阿拉伯", "阿拉伯语", "ar_MA"),
    DE("DE", "德国", "德语", "de_DE"),
    NL("NL", "荷兰", "荷兰语", "nl_NL"),
    JP("JP", "日本", "日语", "ja_JP"),
    KR("KR", "韩国", "韩语", "ko_KR"),
    TH("TH", "泰国", "泰国语", "th_TH"),
    VN("VN", "越南", "越南语", "vi_VN"),
    IL("IL", "以色列", "希伯来语", "iw_IL"),
    TR("TR", "土耳其", "土耳其语", "tr_TR"),
    PL("PL", "波兰", "波兰语", "pl_PL"),
    ;

    private String site;

    private String name;

    private String language;

    private String languagesCode;

    private TranslateCountryEnum(String site, String name, String language, String languagesCode) {
        this.site = site;
        this.name = name;
        this.language = language;
        this.languagesCode = languagesCode;
    }

    public static Map<String, String> getLanguageMap(){
        Map<String, String> languageMap = new HashMap<>();
        TranslateCountryEnum[] values = TranslateCountryEnum.values();
        for (TranslateCountryEnum value : values) {
            languageMap.put(value.site, value.language);
        }
        return languageMap;
    }

    public static List<String> getSiteList(){
        List<String> siteList = new ArrayList<>();
        TranslateCountryEnum[] values = TranslateCountryEnum.values();
        for (TranslateCountryEnum value : values) {
            siteList.add(value.site);
        }
        return siteList;
    }

    public static String getLanguagesCodeBySite(String site){
        TranslateCountryEnum[] values = TranslateCountryEnum.values();
        for (TranslateCountryEnum value : values) {
            if(StringUtils.equalsIgnoreCase(value.site, site)){
                return value.languagesCode;
            }
        }
        return null;
    }

    public static String getSiteByLanguagesCode(String languagesCode){
        TranslateCountryEnum[] values = TranslateCountryEnum.values();
        for (TranslateCountryEnum value : values) {
            if(StringUtils.equalsIgnoreCase(value.languagesCode, languagesCode)){
                return value.site;
            }
        }
        return null;
    }
}
