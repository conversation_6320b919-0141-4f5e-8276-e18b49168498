package com.estone.erp.publish.smt.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.estone.erp.publish.component.converter.ExcelMonitorTypeConverter;
import com.estone.erp.publish.smt.util.convert.ExcelPublishRoleConverter;
import com.estone.erp.publish.smt.util.convert.ExcelSMTItemStatusConverter;
import com.estone.erp.publish.smt.util.convert.ExcelSkuStatusConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监控链接
 * <AUTHOR>
 * @date 2022-07-12 17:24
 */
@Data
public class EsAliexpressProductMonitorVO implements Serializable {


    /**
     * ID
     */
    @ExcelIgnore
    private String id;

    /**
     * 店铺账号
     */
    @ExcelProperty("店铺账号")
    private String accountNumber;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID", converter = LongStringConverter.class)
    private Long productId;

    /**
     * 商品编码
     */
    @ExcelProperty("商品编码")
    private String skuCode;

    /**
     * 图片
     */
    @ExcelProperty("图片")
    private String skuDisplayImg;

    /**
     * 单品货号
     */
    @ExcelProperty("单品货号")
    private String articleNumber;

    /**
     * 单品状态
     */
    @ExcelProperty(value = "单品状态", converter = ExcelSkuStatusConverter.class)
    private String skuStatus;

    /**
     * 标题
     */
    @ExcelProperty("标题")
    private String subject;

    /**
     * 产品类目
     */
    @ExcelProperty("产品类目")
    private String category;

    /**
     * 价格
     */
    @ExcelProperty("价格")
    private String price;

    /**
     * 刊登角色
     */
    @ExcelProperty(value = "刊登角色", converter = ExcelPublishRoleConverter.class)
    private Integer publishRole;

    /**
     * 在售状态
     */
    @ExcelProperty(value = "在售状态", converter = ExcelSMTItemStatusConverter.class)
    private String productStatusType;

    /**
     * 监控类型  1.在线时间， 2.销量
     */
    @ExcelProperty(value = "监控类型", converter = ExcelMonitorTypeConverter.class)
    private Integer monitorType;

    /**
     * 监控条件
     */
    @ExcelProperty("监控条件")
    private String monitorContent;

    /**
     * 24小时销量
     */
    @ExcelProperty("24小时销量")
    private Integer order_24H_count ;

    /**
     * 7天销量
     */
    @ExcelProperty("7天销量")
    private Integer order_last_7d_count;

    /**
     * 14天销量
     */
    @ExcelProperty("14天销量")
    private Integer order_last_14d_count;

    /**
     * 30天销量
     */
    @ExcelProperty("30天销量")
    private Integer order_last_30d_count;

    /**
     * 60天销量
     */
    @ExcelProperty("60天销量")
    private Integer order_last_60d_count;

    /**
     * 总销量
     */
    @ExcelProperty("总销量")
    private Integer order_num_total;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String saleMan;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleManLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String saleManManager;

    /**
     * 上架时间
     */
    @ExcelProperty("上架时间")
    private Date gmtCreate;

    /**
     * 下架时间
     */
    @ExcelProperty("下架时间")
    private Date wsOfflineDate;

    /**
     * 跟踪时间
     */
    @ExcelProperty("跟踪时间")
    private Date offlineCreatedAt;

}
