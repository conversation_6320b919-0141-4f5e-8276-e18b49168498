package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import lombok.Data;

@Data
public class LogisticsInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column logistics_info.id
     */
    private Integer id;

    /**
     * 物流code database column logistics_info.code
     */
    private String code;

    /**
     * 物流name database column logistics_info.name
     */
    private String name;
}