package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsExportDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsPageQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalDto;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionDetails;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【aliexpress_task_execution_details】的数据库操作Service
* @createDate 2024-10-08 09:56:08
*/
public interface AliexpressTaskExecutionDetailsService extends IService<AliexpressTaskExecutionDetails> {

    IPage<AliexpressTaskExecutionDetails> pageQuery(AliexpressTaskExecutionDetailsPageQueryDto dto);

    /**
     * 获取SMT所有规则名称
     * @return
     */
    List<String> getRuleNameList();

    /**
     * 获取昨天的SMT销售账号
     * @return
     */
    List<String> getSaleList();

    /**
     * 获取销售
     *
     * @param userSet
     * @param code
     * @return
     */
    List<AliexpressTaskExecutionDetailsToTotalDto> getTotalListBySale(Set<String> userSet, int code);

    /**
     * 获取销售组长
     * @return
     */
    List<String> getSaleLeaderList();

    /**
     * 销售主管
     * @return
     */
    List<String> getSaleSupervisorList();

    /**
     * 导出队列导出
     * @param dto
     * @return
     */
    ResponseJson export(AliexpressTaskExecutionDetailsExportDto dto);

    IPage<AliexpressTaskExecutionDetails> getAliexpressTaskExecutionDetailsIPage(Page<AliexpressTaskExecutionDetails> page, LambdaQueryWrapper<AliexpressTaskExecutionDetails> queryWrapper);

    /**
     * 通过上级找销售
     *
     * @param saleLeader 销售组长
     * @param saleSupervisor 销售主管
     * @return
     */
    List<String> getSaleByLeader(String saleLeader, String saleSupervisor);

    /**
     * 获取账号
     * @return
     */
    List<String> getAccounts();
}
