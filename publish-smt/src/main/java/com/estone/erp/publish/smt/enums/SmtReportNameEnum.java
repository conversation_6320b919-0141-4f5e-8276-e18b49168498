package com.estone.erp.publish.smt.enums;

public enum SmtReportNameEnum {
    ALLIANCE_SINGLE_PRODUCT_REGISTRATION(1, "联盟单品营销-报名统计报表"),
    ALLIANCE_SINGLE_PRODUCT_REMOVAL(2, "联盟单品营销-移除统计报表"),
    SINGLE_PRODUCT_DISCOUNT_EVENT_CREATION(3, "单品折扣-活动创建统计报表"),
    SINGLE_PRODUCT_DISCOUNT_ADD_PRODUCTS(4, "单品折扣-添加商品统计报表"),
    SINGLE_PRODUCT_DISCOUNT_EDIT_PRODUCT_DISCOUNT(5, "单品折扣-编辑商品折扣统计报表"),
    SINGLE_PRODUCT_DISCOUNT_SYNC_DISCOUNT(6, "单品折扣-同步折扣统计报表"),
    STORE_CODE_EVENT_CREATION(7, "店铺code-活动创建统计报表"),
    STORE_FULL_REDUCTION_EVENT_CREATION(8, "店铺满减-活动创建统计报表"),
    EARLY_BIRD_REGISTRATION(9, "早鸟活动-报名统计报表"),
    LISTING_PUBLISH_STATISTICS(10, "上架-刊登统计报表"),
    LISTING_SPECIFIED_SPU_PUBLISH(11, "上架-指定SPU刊登统计报表"),
    HALF_MANAGED_JOIN_STATISTICS(12, "半托管-加入半托管统计报表"),
    DELISTING_EXECUTION_STATISTICS(13, "下架-下架执行统计报表"),
    POP_STOCK_ADJUSTMENT_STATISTICS(14, "库存调整-POP库存统计报表"),
    HALF_MANAGED_STOCK_ADJUSTMENT_STATISTICS(15, "库存调整-半托管库存统计报表");

    private final int code;
    private final String description;

    SmtReportNameEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取枚举
    public static SmtReportNameEnum fromCode(int code) {
        for (SmtReportNameEnum report : values()) {
            if (report.getCode() == code) {
                return report;
            }
        }
        throw new IllegalArgumentException("No matching enum for code: " + code);
    }
}
