package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> aliexpress_brand_set
 * 2023-10-16 18:08:12
 */
@Data
public class AliexpressBrandSetCriteria extends AliexpressBrandSet {
    private static final long serialVersionUID = 1L;

    private List<Integer> idList;

    /**
     * 店铺配置的主键id
     */
    private Integer configId;

    public AliexpressBrandSetExample getExample() {
        AliexpressBrandSetExample example = new AliexpressBrandSetExample();
        AliexpressBrandSetExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountName())) {
            criteria.andAccountNameEqualTo(this.getAccountName());
        }
        if (StringUtils.isNotBlank(this.getSoleId())) {
            criteria.andSoleIdEqualTo(this.getSoleId());
        }
        if (StringUtils.isNotBlank(this.getStatus())) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (StringUtils.isNotBlank(this.getBrandName())) {
            criteria.andBrandNameEqualTo(this.getBrandName());
        }
        if (StringUtils.isNotBlank(this.getCategoryPath())) {
            criteria.andCategoryPathEqualTo(this.getCategoryPath());
        }
        if (this.getAeBrandId() != null) {
            criteria.andAeBrandIdEqualTo(this.getAeBrandId());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (this.getBrandDeadlineTime() != null) {
            criteria.andBrandDeadlineTimeEqualTo(this.getBrandDeadlineTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        if (this.getCrawlTime() != null) {
            criteria.andCrawlTimeEqualTo(this.getCrawlTime());
        }
        if (this.getStateUpdateTime() != null) {
            criteria.andStateUpdateTimeEqualTo(this.getStateUpdateTime());
        }
        if (this.getIsFirst() != null) {
            criteria.andIsFirstEqualTo(this.getIsFirst());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdateBy())) {
            criteria.andLastUpdateByEqualTo(this.getLastUpdateBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("brand_deadline_time DESC");
        }
        return example;
    }
}