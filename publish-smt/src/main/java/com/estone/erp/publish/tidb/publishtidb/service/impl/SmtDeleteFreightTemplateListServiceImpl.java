package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtDeleteFreightTemplateListMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtDeleteFreightTemplateList;
import com.estone.erp.publish.tidb.publishtidb.model.SmtDeleteFreightTemplateListImportVO;
import com.estone.erp.publish.tidb.publishtidb.service.SmtDeleteFreightTemplateListService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【smt_delete_freight_template_list(SMT删除运费模板)】的数据库操作Service实现
* @createDate 2025-06-13 11:31:22
*/
@Service
public class SmtDeleteFreightTemplateListServiceImpl extends ServiceImpl<SmtDeleteFreightTemplateListMapper, SmtDeleteFreightTemplateList>
    implements SmtDeleteFreightTemplateListService{

    /**
     * 批量导入删除运费模板任务
     * @param importList 导入数据
     * @param currentUser 当前操作人
     * @return 处理结果
     */
    @Override
    public ApiResult<String> batchImport(List<SmtDeleteFreightTemplateListImportVO> importList, String currentUser) {
        if (CollectionUtils.isEmpty(importList)) {
            return ApiResult.newError("导入数据不能为空");
        }
        int updateCount = 0;
        int insertCount = 0;
        Date now = new Date();
        for (SmtDeleteFreightTemplateListImportVO vo : importList) {
            if (vo == null || StringUtils.isBlank(vo.getAccount()) || StringUtils.isBlank(vo.getTemplateId())) {
                continue;
            }
            // 只处理待处理状态（0）
            SmtDeleteFreightTemplateList query = lambdaQuery()
                    .eq(SmtDeleteFreightTemplateList::getAccount, vo.getAccount())
                    .eq(SmtDeleteFreightTemplateList::getTemplateId, vo.getTemplateId())
                    .eq(SmtDeleteFreightTemplateList::getExecuteStatus, 0)
                    .one();
            if (query != null) {
                // 覆盖更新
                query.setCreatedBy(currentUser);
                query.setCreatedTime(now);
                // 待处理
                query.setExecuteStatus(0);
                query.setExecuteResult(null);
                query.setExecuteTime(null);
                query.setExecuteCompleteTime(null);
                query.setUpdateTime(now);
                query.setUpdatedBy(currentUser);
                updateById(query);
                updateCount++;
            } else {
                // 新增
                SmtDeleteFreightTemplateList entity = new SmtDeleteFreightTemplateList();
                entity.setAccount(vo.getAccount());
                entity.setTemplateId(vo.getTemplateId());
                entity.setCreatedBy(currentUser);
                entity.setCreatedTime(now);
                // 待处理
                entity.setExecuteStatus(0);
                entity.setUpdateTime(now);
                entity.setUpdatedBy(currentUser);
                save(entity);
                insertCount++;
            }
        }
        int total = updateCount + insertCount;
        String msg = String.format("导入完成，共处理%d条，新增%d条，更新%d条", total, insertCount, updateCount);
        return ApiResult.newSuccess(msg);
    }

}




