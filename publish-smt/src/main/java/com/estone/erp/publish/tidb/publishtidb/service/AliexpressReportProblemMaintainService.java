package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressReportProblemMaintainQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTemplateErrorMsgVO;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressReportProblemMaintain;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【aliexpress_report_problem_maintain】的数据库操作Service
* @createDate 2025-03-04 14:38:43
*/
public interface AliexpressReportProblemMaintainService extends IService<AliexpressReportProblemMaintain> {

    IPage<AliexpressReportProblemMaintain> pageQuery(AliexpressReportProblemMaintainQueryDto dto);

    String saveOrUpdateByEntity(AliexpressReportProblemMaintain aliexpress);

    ResponseJson export(AliexpressReportProblemMaintainQueryDto dto);

    List<String> getAllSolutionType();

    /**
     *根据错误信息和操作类型 获取对应的解决方案
     * @param errorMsg 错误信息
     * @param operationType 处理报告类型
     * @return
     */
    AliexpressReportProblemMaintain getSolutionByErrorMsg(String errorMsg,String operationType);


    /**
     * 根据模板编号和店铺获取错误信息和解决方案
     * @param dto
     * @return
     */
    AliexpressTemplateErrorMsgVO getTemplateErrorMsgAndSolution(AliexpressTemplateErrorMsgDto dto);
}
