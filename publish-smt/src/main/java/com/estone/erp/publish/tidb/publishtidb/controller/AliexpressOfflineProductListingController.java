package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressOfflineProductListingQueryDO;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressOfflineProductListingService;
import com.estone.erp.publish.tidb.publishtidb.vo.AliexpressOfflineProductListingVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 速卖通下架产品 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/aliexpressOfflineProductListing")
public class AliexpressOfflineProductListingController {

    @Autowired
    private AliexpressOfflineProductListingService aliexpressProductOfflineListingService;

    @PostMapping("search")
    public CQueryResult<AliexpressOfflineProductListingVO> search(@RequestBody CQuery<AliexpressOfflineProductListingQueryDO> query) {
        return aliexpressProductOfflineListingService.search(query);
    }


    /**
     * 导出
     */
    @PostMapping("download")
    public ApiResult<?> downloadTable(@RequestBody CQuery<AliexpressOfflineProductListingQueryDO> query) {
        return aliexpressProductOfflineListingService.downloadTable(query);
    }
}