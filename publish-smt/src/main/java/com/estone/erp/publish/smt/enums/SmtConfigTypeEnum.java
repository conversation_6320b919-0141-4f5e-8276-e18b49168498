package com.estone.erp.publish.smt.enums;

public enum SmtConfigTypeEnum {
    ALLIANCE(1, "联盟"),
    SINGLE_PRODUCT_DISCOUNT(2, "单品折扣"),
    STORE_CODE(3, "店铺code"),
    STORE_FULL_REDUCTION(4, "店铺满减"),
    EARLY_BIRD(5, "早鸟活动"),
    POP_CONFIG(6, "POP配置"),
    POP_SPECIFIED_SPU(7, "POP指定SPU"),
    JOIN_HALF_MANAGED(8, "加入半托管"),
    DELIST_CONFIG(9, "下架配置"),
    POP_STOCK_ADJUSTMENT(10, "POP调库存"),
    HALF_MANAGED_STOCK_ADJUSTMENT(11, "半托管调库存");

    private final int code;
    private final String description;

    SmtConfigTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取枚举
    public static SmtConfigTypeEnum fromCode(int code) {
        for (SmtConfigTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching enum for code: " + code);
    }
}
