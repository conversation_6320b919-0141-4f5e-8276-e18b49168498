package com.estone.erp.publish.smt.enums;

public enum LogRelationTypeEnum {
    template(1, "模板"),
    product_source(2, "产品库"),
    product(3, "产品"),
    auto_template(4, "admin范本"),
    tg_template(5, "托管模板"),
    ;

    private int code;

    private String name;

    private LogRelationTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static LogRelationTypeEnum build(int code) {
        LogRelationTypeEnum[] values = values();
        for (LogRelationTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        LogRelationTypeEnum[] values = values();
        for (LogRelationTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
