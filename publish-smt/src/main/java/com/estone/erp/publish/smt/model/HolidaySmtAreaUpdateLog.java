package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class HolidaySmtAreaUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column holiday_smt_area_update_log.id
     */
    private Long id;

    /**
     * 店铺 database column holiday_smt_area_update_log.account
     */
    private String account;

    /**
     * 产品id database column holiday_smt_area_update_log.product_id
     */
    private Long productId;

    /**
     * 创建时间 database column holiday_smt_area_update_log.create_date
     */
    private Timestamp createDate;

    /**
     * sku database column holiday_smt_area_update_log.sku
     */
    private String sku;

    /**
     * sku_id database column holiday_smt_area_update_log.sku_id
     */
    private String skuId;

    /**
     * skuStatus database column holiday_smt_area_update_log.sku_status
     */
    private String skuStatus;

    /**
     * sku库存 database column holiday_smt_area_update_log.system_stock
     */
    private Integer systemStock;

    /**
     * 支持cny结算 database column holiday_smt_area_update_log.cny
     */
    private Boolean cny;

    /**
     * 之前的价格 database column holiday_smt_area_update_log.before_us_price
     */
    private Double beforeUsPrice;

    /**
     * 现在的价格 database column holiday_smt_area_update_log.now_us_price
     */
    private Double nowUsPrice;

    /**
     * 63国区域改前价格 database column holiday_smt_area_update_log.before_json
     */
    private String beforeJson;

    /**
     * 63国区域改后价 database column holiday_smt_area_update_log.now_json
     */
    private String nowJson;
}