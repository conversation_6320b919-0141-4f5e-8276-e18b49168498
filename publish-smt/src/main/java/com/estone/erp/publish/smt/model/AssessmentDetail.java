package com.estone.erp.publish.smt.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentDetail {
    private String label;
    private String score;
    private String weight;
    private String value;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AssessmentDetail that = (AssessmentDetail) o;
        return Objects.equals(label, that.label) &&
                Objects.equals(score, that.score) &&
                Objects.equals(weight, that.weight) &&
                Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(label, score, weight, value);
    }
}

