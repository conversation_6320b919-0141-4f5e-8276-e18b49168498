package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-06-04 09:45:53
 */
public class HalfTempCriteria extends HalfTemp {
    private static final long serialVersionUID = 1L;

    public HalfTempExample getExample() {
        HalfTempExample example = new HalfTempExample();
        HalfTempExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getTempId() != null) {
            criteria.andTempIdEqualTo(this.getTempId());
        }
        if (StringUtils.isNotBlank(this.getDraftId())) {
            criteria.andDraftIdEqualTo(this.getDraftId());
        }
        if (StringUtils.isNotBlank(this.getJoinedCountryList())) {
            criteria.andJoinedCountryListEqualTo(this.getJoinedCountryList());
        }
        if (StringUtils.isNotBlank(this.getProductSkuList())) {
            criteria.andProductSkuListEqualTo(this.getProductSkuList());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        return example;
    }
}