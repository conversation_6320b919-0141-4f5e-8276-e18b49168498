package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductPublishLeaderConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface SmtNewProductPublishLeaderConfigService extends IService<SmtNewProductPublishLeaderConfig> {

    /**
     * 同步组长刊登次数配置表
     */
    void syncLeaderChanges();

    List<SmtNewProductPublishLeaderConfig> getList();
}
