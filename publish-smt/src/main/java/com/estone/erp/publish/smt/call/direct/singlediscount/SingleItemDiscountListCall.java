package com.estone.erp.publish.smt.call.direct.singlediscount;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.model.dto.SingleDiscountListDTO;
import com.estone.erp.publish.smt.model.dto.SingleItemDiscountListReqVo;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.List;

/**
 * 查询商家营销活动列表
 * @Auther lc
 * @Date 2024年7月30日10:09:13
 */
@Slf4j
public class SingleItemDiscountListCall {
    public static final String key = "promotionList";
    public static IopResponse list(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Integer currentPage, Integer pageSize, Long promotionId) throws ApiException {
        // 执行请求
        IopRequest request = new IopRequest();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("promotion_type", "ProductDiscount");
        jsonObject.put("current_page", currentPage);
        jsonObject.put("page_size", pageSize);
        if (null != promotionId) {
            jsonObject.put("promotion_id", promotionId);
        }
        request.setApiName("aliexpress.marketing.storepromotions.list");
        request.addApiParameter("promotion_query_dto", jsonObject.toJSONString());
        IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber, request);
        return iopResponse;
    }

    public static ResponseJson getPromotionList(SaleAccountAndBusinessResponse saleAccount, SingleItemDiscountListReqVo reqVo) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            IopResponse iopResponse = list(saleAccount, reqVo.getCurrentPage(), reqVo.getPageSize(), reqVo.getPromotionId());
            SingleDiscountListDTO singleDiscountListDTO;
            if (ObjectUtils.isNotEmpty(iopResponse) && iopResponse.isSuccess() && StringUtils.isNotBlank(iopResponse.getBody())) {
                if ((singleDiscountListDTO = JSON.parseObject(iopResponse.getBody(), SingleDiscountListDTO.class)) != null
                        && ObjectUtils.isNotEmpty(singleDiscountListDTO.getAliexpress_marketing_storepromotions_list_response())
                        && ObjectUtils.isNotEmpty(singleDiscountListDTO.getAliexpress_marketing_storepromotions_list_response().getData_list())
                        && CollectionUtils.isNotEmpty(singleDiscountListDTO.getAliexpress_marketing_storepromotions_list_response().getData_list().getPromotion_simple_info_dto())) {

                    List<SingleDiscountListDTO.AliexpressMarketingStorepromotionsListResponseDTO.DataListDTO.PromotionSimpleInfoDtoDTO> promotionList =
                            singleDiscountListDTO.getAliexpress_marketing_storepromotions_list_response().getData_list().getPromotion_simple_info_dto();
                    rsp.setStatus(StatusCode.SUCCESS);
                    rsp.getBody().put(key, promotionList);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            rsp.getBody().put(key, Collections.emptyList());
        }
        return rsp;
    }

    public static SingleItemDiscountListReqVo buildReqVo(Integer currentPage, Integer pageSize, Long promotionId) {
        return SingleItemDiscountListReqVo.builder()
                .currentPage(currentPage)
                .pageSize(pageSize)
                .promotionId(promotionId)
                .build();
    }
}
