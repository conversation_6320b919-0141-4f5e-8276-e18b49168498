package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressAlianceConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * 自定义字段
     */
    private String columns;

    public AliexpressAlianceConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusIsNull() {
            addCriterion("aliance_auto_status is null");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusIsNotNull() {
            addCriterion("aliance_auto_status is not null");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusEqualTo(Integer value) {
            addCriterion("aliance_auto_status =", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusNotEqualTo(Integer value) {
            addCriterion("aliance_auto_status <>", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusGreaterThan(Integer value) {
            addCriterion("aliance_auto_status >", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("aliance_auto_status >=", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusLessThan(Integer value) {
            addCriterion("aliance_auto_status <", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusLessThanOrEqualTo(Integer value) {
            addCriterion("aliance_auto_status <=", value, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusIn(List<Integer> values) {
            addCriterion("aliance_auto_status in", values, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusNotIn(List<Integer> values) {
            addCriterion("aliance_auto_status not in", values, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusBetween(Integer value1, Integer value2) {
            addCriterion("aliance_auto_status between", value1, value2, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andAlianceAutoStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("aliance_auto_status not between", value1, value2, "alianceAutoStatus");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateIsNull() {
            addCriterion("init_commission_rate is null");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateIsNotNull() {
            addCriterion("init_commission_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateEqualTo(Integer value) {
            addCriterion("init_commission_rate =", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateNotEqualTo(Integer value) {
            addCriterion("init_commission_rate <>", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateGreaterThan(Integer value) {
            addCriterion("init_commission_rate >", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_commission_rate >=", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateLessThan(Integer value) {
            addCriterion("init_commission_rate <", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateLessThanOrEqualTo(Integer value) {
            addCriterion("init_commission_rate <=", value, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateIn(List<Integer> values) {
            addCriterion("init_commission_rate in", values, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateNotIn(List<Integer> values) {
            addCriterion("init_commission_rate not in", values, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateBetween(Integer value1, Integer value2) {
            addCriterion("init_commission_rate between", value1, value2, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andInitCommissionRateNotBetween(Integer value1, Integer value2) {
            addCriterion("init_commission_rate not between", value1, value2, "initCommissionRate");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeIsNull() {
            addCriterion("link_publish_time is null");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeIsNotNull() {
            addCriterion("link_publish_time is not null");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeEqualTo(String value) {
            addCriterion("link_publish_time =", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeNotEqualTo(String value) {
            addCriterion("link_publish_time <>", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeGreaterThan(String value) {
            addCriterion("link_publish_time >", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeGreaterThanOrEqualTo(String value) {
            addCriterion("link_publish_time >=", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeLessThan(String value) {
            addCriterion("link_publish_time <", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeLessThanOrEqualTo(String value) {
            addCriterion("link_publish_time <=", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeLike(String value) {
            addCriterion("link_publish_time like", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeNotLike(String value) {
            addCriterion("link_publish_time not like", value, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeIn(List<String> values) {
            addCriterion("link_publish_time in", values, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeNotIn(List<String> values) {
            addCriterion("link_publish_time not in", values, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeBetween(String value1, String value2) {
            addCriterion("link_publish_time between", value1, value2, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andLinkPublishTimeNotBetween(String value1, String value2) {
            addCriterion("link_publish_time not between", value1, value2, "linkPublishTime");
            return (Criteria) this;
        }

        public Criteria andSalesRangeIsNull() {
            addCriterion("sales_range is null");
            return (Criteria) this;
        }

        public Criteria andSalesRangeIsNotNull() {
            addCriterion("sales_range is not null");
            return (Criteria) this;
        }

        public Criteria andSalesRangeEqualTo(String value) {
            addCriterion("sales_range =", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeNotEqualTo(String value) {
            addCriterion("sales_range <>", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeGreaterThan(String value) {
            addCriterion("sales_range >", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeGreaterThanOrEqualTo(String value) {
            addCriterion("sales_range >=", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeLessThan(String value) {
            addCriterion("sales_range <", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeLessThanOrEqualTo(String value) {
            addCriterion("sales_range <=", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeLike(String value) {
            addCriterion("sales_range like", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeNotLike(String value) {
            addCriterion("sales_range not like", value, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeIn(List<String> values) {
            addCriterion("sales_range in", values, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeNotIn(List<String> values) {
            addCriterion("sales_range not in", values, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeBetween(String value1, String value2) {
            addCriterion("sales_range between", value1, value2, "salesRange");
            return (Criteria) this;
        }

        public Criteria andSalesRangeNotBetween(String value1, String value2) {
            addCriterion("sales_range not between", value1, value2, "salesRange");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupIsNull() {
            addCriterion("remove_group is null");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupIsNotNull() {
            addCriterion("remove_group is not null");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupEqualTo(String value) {
            addCriterion("remove_group =", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupNotEqualTo(String value) {
            addCriterion("remove_group <>", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupGreaterThan(String value) {
            addCriterion("remove_group >", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupGreaterThanOrEqualTo(String value) {
            addCriterion("remove_group >=", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupLessThan(String value) {
            addCriterion("remove_group <", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupLessThanOrEqualTo(String value) {
            addCriterion("remove_group <=", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupLike(String value) {
            addCriterion("remove_group like", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupNotLike(String value) {
            addCriterion("remove_group not like", value, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupIn(List<String> values) {
            addCriterion("remove_group in", values, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupNotIn(List<String> values) {
            addCriterion("remove_group not in", values, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupBetween(String value1, String value2) {
            addCriterion("remove_group between", value1, value2, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andRemoveGroupNotBetween(String value1, String value2) {
            addCriterion("remove_group not between", value1, value2, "removeGroup");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeIsNull() {
            addCriterion("trigger_type is null");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeIsNotNull() {
            addCriterion("trigger_type is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeEqualTo(String value) {
            addCriterion("trigger_type =", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeNotEqualTo(String value) {
            addCriterion("trigger_type <>", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeGreaterThan(String value) {
            addCriterion("trigger_type >", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeGreaterThanOrEqualTo(String value) {
            addCriterion("trigger_type >=", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeLessThan(String value) {
            addCriterion("trigger_type <", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeLessThanOrEqualTo(String value) {
            addCriterion("trigger_type <=", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeLike(String value) {
            addCriterion("trigger_type like", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeNotLike(String value) {
            addCriterion("trigger_type not like", value, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeIn(List<String> values) {
            addCriterion("trigger_type in", values, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeNotIn(List<String> values) {
            addCriterion("trigger_type not in", values, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeBetween(String value1, String value2) {
            addCriterion("trigger_type between", value1, value2, "triggerType");
            return (Criteria) this;
        }

        public Criteria andTriggerTypeNotBetween(String value1, String value2) {
            addCriterion("trigger_type not between", value1, value2, "triggerType");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeIsNull() {
            addCriterion("exec_days_time is null");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeIsNotNull() {
            addCriterion("exec_days_time is not null");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeEqualTo(String value) {
            addCriterion("exec_days_time =", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeNotEqualTo(String value) {
            addCriterion("exec_days_time <>", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeGreaterThan(String value) {
            addCriterion("exec_days_time >", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeGreaterThanOrEqualTo(String value) {
            addCriterion("exec_days_time >=", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeLessThan(String value) {
            addCriterion("exec_days_time <", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeLessThanOrEqualTo(String value) {
            addCriterion("exec_days_time <=", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeLike(String value) {
            addCriterion("exec_days_time like", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeNotLike(String value) {
            addCriterion("exec_days_time not like", value, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeIn(List<String> values) {
            addCriterion("exec_days_time in", values, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeNotIn(List<String> values) {
            addCriterion("exec_days_time not in", values, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeBetween(String value1, String value2) {
            addCriterion("exec_days_time between", value1, value2, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andExecDaysTimeNotBetween(String value1, String value2) {
            addCriterion("exec_days_time not between", value1, value2, "execDaysTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(String value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(String value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(String value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(String value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(String value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(String value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLike(String value) {
            addCriterion("start_time like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotLike(String value) {
            addCriterion("start_time not like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<String> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<String> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(String value1, String value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(String value1, String value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andPlanDaysIsNull() {
            addCriterion("plan_days is null");
            return (Criteria) this;
        }

        public Criteria andPlanDaysIsNotNull() {
            addCriterion("plan_days is not null");
            return (Criteria) this;
        }

        public Criteria andPlanDaysEqualTo(Integer value) {
            addCriterion("plan_days =", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysNotEqualTo(Integer value) {
            addCriterion("plan_days <>", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysGreaterThan(Integer value) {
            addCriterion("plan_days >", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("plan_days >=", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysLessThan(Integer value) {
            addCriterion("plan_days <", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysLessThanOrEqualTo(Integer value) {
            addCriterion("plan_days <=", value, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysIn(List<Integer> values) {
            addCriterion("plan_days in", values, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysNotIn(List<Integer> values) {
            addCriterion("plan_days not in", values, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysBetween(Integer value1, Integer value2) {
            addCriterion("plan_days between", value1, value2, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("plan_days not between", value1, value2, "planDays");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsIsNull() {
            addCriterion("plan_order_counts is null");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsIsNotNull() {
            addCriterion("plan_order_counts is not null");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsEqualTo(Integer value) {
            addCriterion("plan_order_counts =", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsNotEqualTo(Integer value) {
            addCriterion("plan_order_counts <>", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsGreaterThan(Integer value) {
            addCriterion("plan_order_counts >", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsGreaterThanOrEqualTo(Integer value) {
            addCriterion("plan_order_counts >=", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsLessThan(Integer value) {
            addCriterion("plan_order_counts <", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsLessThanOrEqualTo(Integer value) {
            addCriterion("plan_order_counts <=", value, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsIn(List<Integer> values) {
            addCriterion("plan_order_counts in", values, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsNotIn(List<Integer> values) {
            addCriterion("plan_order_counts not in", values, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsBetween(Integer value1, Integer value2) {
            addCriterion("plan_order_counts between", value1, value2, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andPlanOrderCountsNotBetween(Integer value1, Integer value2) {
            addCriterion("plan_order_counts not between", value1, value2, "planOrderCounts");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("config_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("config_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(Integer value) {
            addCriterion("config_id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(Integer value) {
            addCriterion("config_id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(Integer value) {
            addCriterion("config_id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("config_id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(Integer value) {
            addCriterion("config_id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(Integer value) {
            addCriterion("config_id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<Integer> values) {
            addCriterion("config_id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<Integer> values) {
            addCriterion("config_id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(Integer value1, Integer value2) {
            addCriterion("config_id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(Integer value1, Integer value2) {
            addCriterion("config_id not between", value1, value2, "configId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}