package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月15日/14:34
 * @Description: <p>简要描述作用</p>
 * @Version: 1.0.0
 * @modified:
 */
public enum AlianceStatusEnum {
    SUCCESS(1, "成功"),
    FAIL(0, "失败");


    private int code;

    private String name;

    AlianceStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AlianceStatusEnum build(int code) {
        AlianceStatusEnum[] values = values();
        for (AlianceStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        AlianceStatusEnum[] values = values();
        for (AlianceStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
