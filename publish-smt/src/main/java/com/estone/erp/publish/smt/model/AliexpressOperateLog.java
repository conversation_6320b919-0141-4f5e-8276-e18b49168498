package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressOperateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_operate_log.id
     */
    private Integer id;

    /**
     * 类型 database column aliexpress_operate_log.type
     */
    private String type;

    /**
     * 业务编号 database column aliexpress_operate_log.business_id
     */
    private Integer businessId;

    /**
     * 字段名 database column aliexpress_operate_log.field_name
     */
    private String fieldName;

    /**
     * 改前 database column aliexpress_operate_log.before
     */
    private String before;

    /**
     * 改后 database column aliexpress_operate_log.after
     */
    private String after;

    /**
     * 记录信息 database column aliexpress_operate_log.message
     */
    private String message;

    /**
     * 创建人 database column aliexpress_operate_log.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_operate_log.create_date
     */
    private Timestamp createDate;
}