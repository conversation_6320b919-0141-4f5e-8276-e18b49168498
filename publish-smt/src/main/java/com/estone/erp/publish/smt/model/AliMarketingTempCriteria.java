package com.estone.erp.publish.smt.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> ali_marketing_temp
 * 2020-10-16 16:13:35
 */
@Data
public class AliMarketingTempCriteria extends AliMarketingTemp {
    private static final long serialVersionUID = 1L;

    private String likeTempName;

    private Timestamp fromCreateDate;

    private Timestamp toCreateDate;

    //删除关联 + 更新使用
    private List<Long> productIdList;

    //在线列表 关联用
    private String idStr;

    private List<String> createByList;

    public AliMarketingTempExample getExample() {
        AliMarketingTempExample example = new AliMarketingTempExample();
        AliMarketingTempExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getTempName())) {
            criteria.andTempNameEqualTo(this.getTempName());
        }

        if(StringUtils.isNotBlank(this.getLikeTempName())){
            criteria.andTempNameLike("%" + this.getLikeTempName() + "%");
        }

        if (StringUtils.isNotBlank(this.getAccount())) {
            List<String> strings = CommonUtils.splitList(this.getAccount(), ",");
            if(strings.size() > 1){
                criteria.andAccountIn(strings);
            }else{
                criteria.andAccountEqualTo(this.getAccount());
            }
        }

        if (this.getArrayNum() != null) {
            criteria.andArrayNumEqualTo(this.getArrayNum());
        }
        if (this.getProductNum() != null) {
            criteria.andProductNumEqualTo(this.getProductNum());
        }
        if (this.getRelatedProductNum() != null) {
            criteria.andRelatedProductNumEqualTo(this.getRelatedProductNum());
        }
        if (StringUtils.isNotBlank(this.getHtml())) {
            criteria.andHtmlEqualTo(this.getHtml());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreateByIn(this.getCreateByList());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdatedBy())) {
            criteria.andLastUpdatedByEqualTo(this.getLastUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1EqualTo(this.getAttribute1());
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }

        if(this.getFromCreateDate() != null){
            criteria.andCreateDateGreaterThanOrEqualTo(this.getFromCreateDate());
        }

        if(this.getToCreateDate() != null){
            criteria.andCreateDateLessThanOrEqualTo(this.getToCreateDate());
        }
        
        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("last_update_date DESC");
        }
        return example;
    }
}