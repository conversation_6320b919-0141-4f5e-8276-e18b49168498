package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FreightExecuteTypeEnum {

    S_1(1, "更新"),
    S_2(2, "复制");

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static String convert(Integer value) {
        for (FreightExecuteTypeEnum status : FreightExecuteTypeEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

}
