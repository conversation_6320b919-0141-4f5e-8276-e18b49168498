package com.estone.erp.publish.smt.util;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.smt.enums.TemplateStatusEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressNewRemindService;
import com.estone.erp.publish.smt.service.AliexpressTemplateService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;

/**
 * 修复 在线数据
 */
@Component
@Slf4j
public class AliexpressNewRemindSyncUtils {

    @Autowired
    private AliexpressNewRemindService aliexpressNewRemindService;

    @Autowired
    private AliexpressTemplateService aliexpressTemplateService;
    /**
     * 修复数
     */
    public ApiResult<?> syncTemplateInfo() {
        Integer maxId = null;
        int size = 1000;
        List<Integer> arrayList = new ArrayList<>();
        arrayList.add(TemplateStatusEnum.PUBLISH_SUCCESS.getCode());
        arrayList.add(TemplateStatusEnum.PUBLISHING.getCode());
        arrayList.add(TemplateStatusEnum.PUBLISH_FAILED.getCode());
        int updateSize = 0;
        int searchSize = 0;
        int errorSize = 0;
        //创建查询条件构造器
        StopWatch stopWatch = new StopWatch();
        do {
            searchSize += 1000;
            stopWatch.start();
            List<AliexpressNewRemind> aliexpressNewReminds = aliexpressNewRemindService.selectTemplateInfo(arrayList, maxId, size);
            if (!aliexpressNewReminds.isEmpty()) {
                maxId = aliexpressNewReminds.get(aliexpressNewReminds.size() - 1).getId();
            }
            List<AliexpressNewRemind> updateList = new ArrayList<>();
            for (AliexpressNewRemind aliexpressNewRemind : aliexpressNewReminds) {
                try {
                    String spu = aliexpressNewRemind.getSpu();
                    if (aliexpressNewRemind.getPublishStatus() == TemplateStatusEnum.PUBLISH_SUCCESS.intCode()) {
                        // 数据问题不处理
                        if (StringUtils.isBlank(spu)
                                || aliexpressNewRemind.getTempFinishTime() == null) {
                            errorSize++;
                            continue;
                        }
                        //  刊登中只需要spu对应有发版完成，且lastEditTimeEqual 相同，就能取到对应时间
                        AliexpressTemplateExample templateExample = new AliexpressTemplateExample();
                        templateExample.createCriteria()
                                .andArticleNumberEqualTo(spu)
                                .andLastEditTimeEqualTo(aliexpressNewRemind.getTempFinishTime())
                                .andIsParentEqualTo(false)
                                .andTemplateStatusEqualTo(TemplateStatusEnum.PUBLISH_SUCCESS.intCode());
                        templateExample.setLimit(1);
                        templateExample.setFields("id, last_edit_time, creator");
                        List<AliexpressTemplate> aliexpressTemplates = aliexpressTemplateService.selectByExample(templateExample);
                        // 数据丢失
                        if (aliexpressTemplates.isEmpty()) {
                            errorSize++;
                            continue;
                        }
                        AliexpressTemplate template = aliexpressTemplates.get(0);
                        aliexpressNewRemind.setTempFinishTime(template.getLastEditTime());
                        aliexpressNewRemind.setTemplateId(template.getId());
                        aliexpressNewRemind.setTempCreator(template.getCreator());
                        aliexpressNewRemind.setFailInfo("");
                        updateList.add(aliexpressNewRemind);

                    } else if (aliexpressNewRemind.getPublishStatus().equals(TemplateStatusEnum.PUBLISHING.intCode())) {
                        // 数据问题，不处理
                        if (StringUtils.isBlank(aliexpressNewRemind.getAccount()) || StringUtils.isBlank(spu)) {
                            errorSize++;
                            continue;
                        }
                        //刊登中的：需要 店铺账号和spu对应有在刊登中
                        AliexpressTemplateExample templateExample = new AliexpressTemplateExample();
                        templateExample.createCriteria()
                                .andArticleNumberEqualTo(spu)
                                .andAliexpressAccountNumberEqualTo(aliexpressNewRemind.getAccount())
                                .andIsParentEqualTo(false)
                                .andTemplateStatusEqualTo(TemplateStatusEnum.PUBLISHING.intCode());
                        templateExample.setLimit(1);
                        templateExample.setOrderByClause("create_time desc");
                        templateExample.setFields("id, last_edit_time, creator");

                        List<AliexpressTemplate> list = aliexpressTemplateService.selectByExample(templateExample);
                        // 数据丢失
                        if (CollectionUtils.isEmpty(list)) {
                            errorSize++;
                            continue;
                        }
                        AliexpressTemplate aliexpressTemplate = list.get(0);
                        aliexpressNewRemind.setIsSuccessTemp(false);

                        //存在刊登中的模板 刊登状态必然改成刊登中，并置空刊登失败备注
                        aliexpressNewRemind.setTempFinishTime(aliexpressTemplate.getLastEditTime());
                        aliexpressNewRemind.setTempCreator(aliexpressTemplate.getCreator());
                        aliexpressNewRemind.setTemplateId(aliexpressTemplate.getId());
                        aliexpressNewRemind.setFailInfo("");

                        updateList.add(aliexpressNewRemind);
                    } else if (aliexpressNewRemind.getPublishStatus().equals(TemplateStatusEnum.PUBLISH_FAILED.intCode())) {
                        // 数据问题，不处理
                        if (aliexpressNewRemind.getTemplateId() == null) {
                            errorSize++;
                            continue;
                        }
                        // 刊登失败的模板
                        // 这里有具体的 templateId
                        AliexpressTemplateExample templateExample = new AliexpressTemplateExample();
                        templateExample.createCriteria().andIdEqualTo(aliexpressNewRemind.getTemplateId());
                        templateExample.setFields("id, last_edit_time, creator");
                        templateExample.setLimit(1);
                        List<AliexpressTemplate> list = aliexpressTemplateService.selectByExample(templateExample);
                        // 数据丢失
                        if (CollectionUtils.isEmpty(list)) {
                            errorSize++;
                            continue;
                        }
                        // 失败原因是已经有了，所以不需要查
                        AliexpressTemplate aliexpressTemplate = list.get(0);
                        aliexpressNewRemind.setTempFinishTime(aliexpressTemplate.getLastEditTime());
                        aliexpressNewRemind.setTempCreator(aliexpressTemplate.getCreator());
                        aliexpressNewRemind.setTemplateId(aliexpressTemplate.getId());
                        updateList.add(aliexpressNewRemind);
                    }
                } catch (Exception e) {
                    XxlJobLogger.log("更新数据异常: [{}]", e.getMessage());
                }
            }

            if (!updateList.isEmpty()) {
                updateSize = updateSize + updateList.size();
                aliexpressNewRemindService.batchUpdateTemplateInfo(updateList);
                updateList.clear();
            }
            stopWatch.stop();
            double totalTimeSeconds = stopWatch.getTotalTimeSeconds();
            XxlJobLogger.log("已查询：" + searchSize + "个" + ",现已消耗：" + totalTimeSeconds + "s" + ", 现已更新：" + updateSize + "个" + ",修复不了的数据个数：" + errorSize + "个");
            if (aliexpressNewReminds.isEmpty() || aliexpressNewReminds.size() < size) {
                break;
            }
        } while (true);

        return ApiResult.newSuccess();
    }
}
