package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/614:37
 */
@Slf4j
public class EditMutilpleSkuStocksOpenCall {
    private static String editmutilpleskustocks = "aliexpress.postproduct.redefining.editmutilpleskustocks";

    public EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);

    public ResponseJson editMutilpleSkuStocksNew(SaleAccountAndBusinessResponse saleAccountByAccountNumber,String productId,
            String skuStocks, boolean halfLableCheck) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

        //需要校验是否存在半托管退出标签，如果存在不能调整库存
        if(halfLableCheck){
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setAliexpressAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            request.setProductId(Long.valueOf(productId));
            request.setHalfCountryExitLabelIsNull(false);
            request.setQueryFields(new String[]{"id", "articleNumber", "ipmSkuStock"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(request);
            if(esAliexpressProductListing != null && esAliexpressProductListing.size() > 0){
                responseJson.setMessage("存在半托管退出标签，不能调整库存！");
                return responseJson;
            }
        }

        try{
            if (saleAccountByAccountNumber != null && StringUtils.isNotBlank(productId) && StringUtils.isNotBlank(skuStocks)) {
                IopRequest request = new IopRequest();
                request.setApiName(editmutilpleskustocks);
                request.addApiParameter("product_id", productId);
                request.addApiParameter("sku_stocks", skuStocks);
                long begin = System.currentTimeMillis();
                IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
                String callRspStr = iopResponse.getBody();
                long end = System.currentTimeMillis();
                long l = (end - begin) / 1000;
                if (l > AbstractSmtOpenCall.logTime) {
//                    log.warn(String.format("editmutilpleskustocks不通过奇门%s秒 rsp%s", l, callRspStr));
                }
                if (StringUtils.isNotBlank(callRspStr)) {
                    JSONObject callRspJson = JSONObject.parseObject(callRspStr);
                    if (callRspJson.containsKey(
                            "aliexpress_postproduct_redefining_editmutilpleskustocks_response")) {
                        JSONObject rsp = callRspJson.getJSONObject(
                                "aliexpress_postproduct_redefining_editmutilpleskustocks_response");
                        if (rsp.containsKey("result")) {
                            JSONObject result = rsp.getJSONObject("result");
                            if (result.containsKey("success") && result.getBoolean("success")) {
                                responseJson.setStatus(StatusCode.SUCCESS);
                            }
                            else if (result.containsKey("error_message")) {
                                String errorCode = result.getString("error_code");
                                String errorMessage = result.getString("error_message");
                                String request_id = result.getString("request_id");
                                responseJson.setMessage("error_code:" + errorCode + ",error_message:" + errorMessage + ",request_id:" + request_id);
                            }
                        }
                    }
                }

                //没有解析到结果
                if(!responseJson.isSuccess() && StringUtils.isEmpty(responseJson.getMessage())){
                    responseJson.setMessage(StringUtils.isBlank(callRspStr) ? "smt无返回，请联系it技术支持！" : callRspStr);
                }
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        return responseJson;
    }
}
