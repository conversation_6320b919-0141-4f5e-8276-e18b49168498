package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtFreightTemplateCodeImportDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport;
import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountry;
import com.estone.erp.publish.tidb.publishtidb.vo.SmtHalfExitCountryQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;

import java.util.List;

/**
 * <AUTHOR> @description 针对表【smt_half_exit_country】的数据库操作IService
 * @create 2024-10-15 16:38:39
 */
public interface SmtHalfExitCountryService extends IService<SmtHalfExitCountry> {
    /**
     * 根据店铺、商品ID和审核状态查询记录数量
     * 
     * @param shop 店铺
     * @param productId 商品ID
     * @param reviewStatus 审核状态
     * @return 记录数量
     */
    Integer countByShopAndProductIdAndReviewStatus(String shop, Long productId, Integer reviewStatus);

    /**
     * 分页查询
     * 
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<SmtHalfExitCountry> list(SmtHalfExitCountryQueryVO queryVO);

    LambdaQueryWrapper<SmtHalfExitCountry> getPageQueryWrapper(SmtHalfExitCountryQueryVO dto);


    List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtHalfExitCountry> wrapper);

    /**
     * 导出功能
     * 
     * @param dto 导出参数
     * @return ApiResult<?> 导出结果
     */
    ApiResult<String> download(SmtHalfExitCountryQueryVO dto);

    /**
     * 更新数据
     * 
     * @param smtHalfExitCountry 数据对象
     * @return boolean 是否更新成功
     */
    boolean updateBySubmitStatus(SmtHalfExitCountry smtHalfExitCountry);}