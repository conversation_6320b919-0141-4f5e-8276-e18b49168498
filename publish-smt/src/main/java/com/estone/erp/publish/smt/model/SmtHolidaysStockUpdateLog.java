package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtHolidaysStockUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 店铺
     */
    private String account;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * sku状态
     */
    private String skuStatus;

    /**
     * sku_id
     */
    private String skuId;

    /**
     * 可用库存
     */
    private Integer usableStock;

    /**
     * 待发库存
     */
    private Integer pendingStock;

    /**
     * 可用-待发
     */
    private Integer remainingStock;

    /**
     * 改前库存（在线列表链接库存）
     */
    private Integer stockBefore;

    /**
     * 改后库存（在线列表链接库存）
     */
    private Integer stockAfter;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 1.休假改0  2.可用-待发<1 改0
     */
    private String updateType;

    /**
     * 操作结果 1 成功 0 失败 2.忽略不修改
     */
    private Integer resultType;

    /**
     * 错误信息
     */
    private String failInfo;
}