package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressSaleMustPublish implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_sale_must_publish.id
     */
    private Integer id;

    /**
     * spu database column aliexpress_sale_must_publish.spu
     */
    private String spu;

    /**
     * 分类中文名 database column aliexpress_sale_must_publish.category_path
     */
    private String categoryPath;

    /**
     * 分类完整code database column aliexpress_sale_must_publish.fullpathcode
     */
    private String fullpathcode;

    /**
     * 产品类目code database column aliexpress_sale_must_publish.root_category
     */
    private String rootCategory;

    /**
     * 产品开发 database column aliexpress_sale_must_publish.product_dev
     */
    private String productDev;

    /**
     * 店铺 database column aliexpress_sale_must_publish.account
     */
    private String account;

    /**
     * 销售 database column aliexpress_sale_must_publish.sale_man
     */
    private String saleMan;

    /**
     * 模板id database column aliexpress_sale_must_publish.temp_id
     */
    private Integer tempId;

    /**
     * 刊登状态 1.待刊登 2.刊登中, 3.刊登成功 4.刊登失败 database column aliexpress_sale_must_publish.publish_status
     */
    private Integer publishStatus;

    /**
     * 刊登角色 database column aliexpress_sale_must_publish.publish_role
     */
    private Integer publishRole;

    /**
     * 产品id database column aliexpress_sale_must_publish.product_id
     */
    private Long productId;

    /**
     * 是否在线 database column aliexpress_sale_must_publish.is_online
     */
    private Boolean isOnline;

    /**
     * 在线时长（天） database column aliexpress_sale_must_publish.online_day
     */
    private Integer onlineDay;

    /**
     * 分配时间 database column aliexpress_sale_must_publish.push_time
     */
    private Timestamp pushTime;

    /**
     * 产品的上架日期 database column aliexpress_sale_must_publish.gmt_create
     */
    private Timestamp gmtCreate;

    /**
     * 产品的下架日期 database column aliexpress_sale_must_publish.ws_offline_date
     */
    private Timestamp wsOfflineDate;

    /**
     * 编辑完成时间 database column aliexpress_sale_must_publish.edit_finish_time
     */
    private Timestamp editFinishTime;

    /**
     * 开发录入时间 database column aliexpress_sale_must_publish.create_at
     */
    private Timestamp createAt;

    /**
     * 备注 database column aliexpress_sale_must_publish.remarks
     */
    private String remarks;

    /**
     * 创建人 database column aliexpress_sale_must_publish.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_sale_must_publish.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_sale_must_publish.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_sale_must_publish.last_update_date
     */
    private Timestamp lastUpdateDate;
}