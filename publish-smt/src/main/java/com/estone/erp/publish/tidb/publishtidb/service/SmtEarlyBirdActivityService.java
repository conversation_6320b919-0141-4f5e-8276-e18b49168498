package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivity;
import com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivityCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivityExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-24 09:12:42
 */
public interface SmtEarlyBirdActivityService extends IService<SmtEarlyBirdActivity> {
    int countByExample(SmtEarlyBirdActivityExample example);

    CQueryResult<SmtEarlyBirdActivity> search(CQuery<SmtEarlyBirdActivityCriteria> cquery);

    List<SmtEarlyBirdActivity> selectByExample(SmtEarlyBirdActivityExample example);

    SmtEarlyBirdActivity selectByPrimaryKey(String productid);

    int insert(SmtEarlyBirdActivity record);

    int updateByPrimaryKeySelective(SmtEarlyBirdActivity record);

    int updateByExampleSelective(SmtEarlyBirdActivity record, SmtEarlyBirdActivityExample example);

    int deleteByPrimaryKey(List<String> productids);

    List<String> selectAllStore();

    List<String> selectProductIdsByExample(SmtEarlyBirdActivityExample smtEarlyBirdActivityExample);

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<SmtEarlyBirdActivity> wrapper);
}