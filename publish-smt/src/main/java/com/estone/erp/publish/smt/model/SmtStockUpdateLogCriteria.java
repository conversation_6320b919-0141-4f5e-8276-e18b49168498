package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> smt_stock_update_log
 * 2024-02-01 14:55:07
 */
public class SmtStockUpdateLogCriteria extends SmtStockUpdateLog {
    private static final long serialVersionUID = 1L;

    public SmtStockUpdateLogExample getExample() {
        SmtStockUpdateLogExample example = new SmtStockUpdateLogExample();
        SmtStockUpdateLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getSkuId())) {
            criteria.andSkuIdEqualTo(this.getSkuId());
        }
        if (this.getRedisStock() != null) {
            criteria.andRedisStockEqualTo(this.getRedisStock());
        }
        if (this.getStockBefore() != null) {
            criteria.andStockBeforeEqualTo(this.getStockBefore());
        }
        if (this.getStockAfter() != null) {
            criteria.andStockAfterEqualTo(this.getStockAfter());
        }
        if (this.getOrderNum30d() != null) {
            criteria.andOrderNum30dEqualTo(this.getOrderNum30d());
        }
        if (this.getOrderDaysWithin30d() != null) {
            criteria.andOrderDaysWithin30dEqualTo(this.getOrderDaysWithin30d());
        }
        if (this.getSmtOrderRate() != null) {
            criteria.andSmtOrderRateEqualTo(this.getSmtOrderRate());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getResult() != null) {
            criteria.andResultEqualTo(this.getResult());
        }
        if (StringUtils.isNotBlank(this.getFailInfo())) {
            criteria.andFailInfoEqualTo(this.getFailInfo());
        }
        return example;
    }
}