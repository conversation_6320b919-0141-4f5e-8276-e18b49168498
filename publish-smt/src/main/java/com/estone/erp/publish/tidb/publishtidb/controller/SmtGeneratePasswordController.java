package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.SmtGeneratePassword;
import com.estone.erp.publish.tidb.publishtidb.model.SmtGeneratePasswordCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.SmtGeneratePasswordImportVO;
import com.estone.erp.publish.tidb.publishtidb.service.SmtGeneratePasswordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SMT生成口令控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/smtGeneratePassword")
public class SmtGeneratePasswordController {

    @Resource
    private SmtGeneratePasswordService smtGeneratePasswordService;
    
    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private ExcelSend excelSend;

    /**
     * 查询SMT生成口令列表
     */
    @PostMapping("/search")
    public CQueryResult<SmtGeneratePassword> search(@RequestBody CQuery<SmtGeneratePasswordCriteria> cquery) {
        try {
            Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
            Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
            
            SmtGeneratePasswordCriteria criteria = cquery.getSearch();
            
            // 权限控制
            applyPermissions(criteria);
            
            // 构建查询条件
            LambdaQueryWrapper<SmtGeneratePassword> queryWrapper = criteria.buildQueryWrapper();
            
            // 分页查询
            Page<SmtGeneratePassword> page = new Page<>(cquery.getOffset() / cquery.getLimit() + 1, cquery.getLimit());
            IPage<SmtGeneratePassword> result = smtGeneratePasswordService.page(page, queryWrapper);
            
            // 构建返回结果
            CQueryResult<SmtGeneratePassword> cQueryResult = new CQueryResult<>();
            cQueryResult.setRows(result.getRecords());
            cQueryResult.setTotal((int) result.getTotal());
            cQueryResult.setSuccess(true);
            
            return cQueryResult;
        } catch (Exception e) {
            log.error("查询SMT生成口令列表失败", e);
            return CQueryResult.failResult(e.getMessage());
        }
    }

    /**
     * 导入数据
     */
    @PostMapping("/import")
    public ApiResult<String> importData(HttpServletRequest request) {
        try {
            // 获取上传文件
            MultipartFile file = getUploadFile(request);
            if (file == null) {
                return ApiResult.newError("请先上传文件");
            }
            
            String currentUser = WebUtils.getUserName();
            if (StringUtils.isBlank(currentUser)) {
                return ApiResult.newError("请重新登录");
            }
            
            // 解析Excel文件
            List<SmtGeneratePasswordImportVO> importList = new ArrayList<>();
            List<String> errorList = new ArrayList<>();

            List<SmtGeneratePasswordImportVO> finalImportList = importList;
            EasyExcel.read(file.getInputStream(), SmtGeneratePasswordImportVO.class,
                new AnalysisEventListener<SmtGeneratePasswordImportVO>() {
                    @Override
                    public void invoke(SmtGeneratePasswordImportVO data, AnalysisContext context) {
                        // 数据校验
                        if (validateImportData(data, context.readRowHolder().getRowIndex() + 1, errorList)) {
                            finalImportList.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("导入数据解析完成，共{}条", finalImportList.size());
                    }
                }).sheet().doRead();
            
            if (CollectionUtils.isNotEmpty(errorList)) {
                return ApiResult.newError("数据校验失败：" + String.join("; ", errorList));
            }
            
            if (CollectionUtils.isEmpty(importList)) {
                return ApiResult.newError("没有有效的导入数据");
            }
            
            // 权限校验
            List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(null, null, null, null, SaleChannel.CHANNEL_SMT, true);

            if (CollectionUtils.isNotEmpty(currentUserPermission)){
                // 过滤无权限的店铺
                importList = importList.stream()
                        .filter(item -> currentUserPermission.contains(item.getAccount()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(importList)) {
                    return ApiResult.newError("没有权限操作任何店铺数据");
                }
            }

            // 执行导入
            int successCount = smtGeneratePasswordService.processImportData(importList, currentUser);
            
            return ApiResult.newSuccess(String.format("导入成功，共处理%d条数据", successCount));
            
        } catch (Exception e) {
            log.error("导入数据失败", e);
            return ApiResult.newError("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出数据
     */
    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody CQuery<SmtGeneratePasswordCriteria> cquery) {
        try {
            SmtGeneratePasswordCriteria criteria = cquery.getSearch();
            // 权限控制
            applyPermissions(criteria);
            
            // 构建查询条件
            LambdaQueryWrapper<SmtGeneratePassword> queryWrapper = criteria.buildQueryWrapper();
            
            // 检查导出数量限制
            long count = smtGeneratePasswordService.count(queryWrapper);
            final int maxExportSize = 500000;
            if (count > maxExportSize) {
               throw new BusinessException(String.format("导出数据超过最大%d行", maxExportSize));

            }
            if (count == 0) {
                throw new BusinessException("当前筛选条件没有符合条件的结果");
            }
            excelSend.downloadGenerateKeyWord(ExcelTypeEnum.downGenerateCode.getCode(), criteria);

        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }


    /**
     * 应用权限控制
     */
    private void applyPermissions(SmtGeneratePasswordCriteria criteria) {
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(criteria.getAccountList(), null, null, null, SaleChannel.CHANNEL_SMT, true);
        criteria.setAccountList(currentUserPermission);
    }

    /**
     * 获取上传文件
     */
    private MultipartFile getUploadFile(HttpServletRequest request) {
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        return fileMap.values().stream().findFirst().orElse(null);
    }

    /**
     * 校验导入数据
     */
    private boolean validateImportData(SmtGeneratePasswordImportVO data, int rowIndex, List<String> errorList) {
        if (StringUtils.isBlank(data.getAccount())) {
            errorList.add(String.format("第%d行：店铺不能为空", rowIndex));
            return false;
        }
        
        if (StringUtils.isBlank(data.getTemplateId())) {
            errorList.add(String.format("第%d行：模板ID不能为空", rowIndex));
            return false;
        }
        
        // 校验店铺是否存在
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(
            SaleChannel.CHANNEL_SMT, data.getAccount());
        if (account == null) {
            errorList.add(String.format("第%d行：店铺%s不存在或不可用", rowIndex, data.getAccount()));
            return false;
        }
        
        return true;
    }
    
} 