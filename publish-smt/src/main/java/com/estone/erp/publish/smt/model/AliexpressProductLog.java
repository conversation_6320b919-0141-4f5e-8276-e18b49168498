package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Data
public class AliexpressProductLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_product_log.id
     */
    private Long id;

    /**
     *  database column aliexpress_product_log.product_id
     */
    private Long productId;

    /**
     *  database column aliexpress_product_log.account_number
     */
    private String accountNumber;

    /**
     *  database column aliexpress_product_log.sku_code
     */
    private String skuCode;

    /**
     * 操作人 database column aliexpress_product_log.operator
     */
    private String operator;


    private Timestamp createTime;


    /**
     * 操作时间 database column aliexpress_product_log.operate_time
     */
    private Timestamp operateTime;

    /**
     * 操作类型 database column aliexpress_product_log.operate_type
     */
    private String operateType;

    /**
     * 操作结果 database column aliexpress_product_log.result
     */
    private Boolean result;

    /**
     * 错误信息 database column aliexpress_product_log.fail_info
     */
    private String failInfo;

    /**
     *  database column aliexpress_product_log.price_before_edit
     */
    private Double priceBeforeEdit;

    /**
     *  database column aliexpress_product_log.price_after_edit
     */
    private Double priceAfterEdit;

    /**
     *  database column aliexpress_product_log.stock_before_edit
     */
    private Double stockBeforeEdit;

    /**
     *  database column aliexpress_product_log.stock_after_edit
     */
    private Double stockAfterEdit;

    private Double weightBeforeEdit;

    private Double weightAfterEdit;

    /**
     * 关联类型
     */
    private Integer relationType;

    /**
     * 关联id
     */
    private String relationId;

    private Integer operateStatus;

    private String requestData;

    /**
     * 结果类型
     */
    private String resultType;

    /**
     * 新增备注
     */
    private String newRemark;

    private String ruleName;

    /**
     * 规则表
     */
    private String ruleTable;

    /**
     * 用String是防止表有的是id或者long类型
     */
    private String ruleId;


    /**
     * POP + 半托管 规则调整库存使用
     */
    private Integer ignoreAdjustDay;

    /**
     * 半托管 主键id
     */
    private Long halfTgItemId;

    /**
     * POP规则调整库存使用
     */
    private String skuId;

    /**
     * POP规则调整库存使用
     */
    private String productEsId;

    /**
     * admin 范本属性是否缺失属性
     */
    private Boolean lackAttr;

    /**
     * 问题分类
     */
    private String problemType;

    /**
     * 解决方案
     */
    private String solutionType;

    /**
     * listing id列表
     */
    private List<String> listIds;

    /**
     * 单独设置failInfo的方法，进行字符串截取
     * 由于failInfo类型为text,最多存放65535个字节长度,在utf8mb4字符集下,大约能存放21845个汉字,这里直接截取前45000个字符,防止数据库保存报错
     *
     * @param failInfo
     */
    public void setFailInfo(String failInfo) {
        //这里如果字符串长度超过20000,直接截取前20000个字符,防止数据库保存报错
        failInfo = Optional.ofNullable(failInfo).map(info -> info.substring(0, Math.min(info.length(), 45000))).orElse(null);
        this.failInfo = failInfo;
    }
}