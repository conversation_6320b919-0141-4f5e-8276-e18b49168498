package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ActivityConfirmationStatusEnum;
import com.estone.erp.publish.smt.enums.ActivityUploadStatusEnum;
import com.estone.erp.publish.smt.model.SmtAccountGroup;
import com.estone.erp.publish.smt.model.SmtAccountGroupExample;
import com.estone.erp.publish.smt.model.dto.PlatSubmitFileListener;
import com.estone.erp.publish.smt.model.dto.SubmitFileVO;
import com.estone.erp.publish.smt.mq.excel.constant.ExcelConstant;
import com.estone.erp.publish.smt.service.SmtAccountGroupService;
import com.estone.erp.publish.tidb.publishtidb.domain.AliexpressActivityRegistrationVO;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressActivityRegistrationDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressActivityRegistrationService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-06 18:07:16
 */
@RestController
@RequestMapping("aliexpressActivityRegistration")
public class AliexpressActivityRegistrationController {

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private AliexpressActivityRegistrationService aliexpressActivityRegistrationService;
    @Autowired
    private SmtAccountGroupService smtAccountGroupService;

    @PostMapping
    public ApiResult<?> postAliexpressActivityRegistration(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressActivityRegistration":
                    CQuery<AliexpressActivityRegistrationCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressActivityRegistrationCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    isSmtAuth(cquery.getSearch());

                    //店铺分组过滤
                    if (ObjectUtils.isNotEmpty(cquery.getSearch()) && CollectionUtils.isNotEmpty(cquery.getSearch().getGroupIdList())) {
                        SmtAccountGroupExample groupExample = new SmtAccountGroupExample();
                        groupExample.createCriteria().andIdIn(cquery.getSearch().getGroupIdList());
                        List<SmtAccountGroup> accountGroupList = smtAccountGroupService.selectByExample(groupExample);
                        List<String> accountList = accountGroupList.stream().flatMap(t -> t.getAccountList().stream()).distinct().collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(cquery.getSearch().getAccountNumberList())){
                            cquery.getSearch().getAccountNumberList().retainAll(accountList);
                            //没有交集，直接返回空数据
                            if (CollectionUtils.isEmpty(cquery.getSearch().getAccountNumberList())){
                                CQueryResult<AliexpressActivityRegistrationVO> result = new CQueryResult<>();
                                result.setRows(Lists.newArrayList());
                                return result;
                            }
                        }else {
                            cquery.getSearch().setAccountNumberList(accountList);
                        }
                    }

                    CQueryResult<AliexpressActivityRegistrationVO> results = aliexpressActivityRegistrationService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/status")
    public ApiResult<?> putAliexpressActivityRegistration(@RequestBody(required = true) AliexpressActivityRegistrationDTO requestParam) {
        AliexpressActivityRegistration activityRegistration = new AliexpressActivityRegistration();
        activityRegistration.setConfirmationStatus(requestParam.getStatus());
        activityRegistration.setUploadStatusModifiedBy(WebUtils.getUserName());
        activityRegistration.setUploadStatusModifiedAt(new Timestamp(System.currentTimeMillis()));
        AliexpressActivityRegistrationExample example = new AliexpressActivityRegistrationExample();
        example.createCriteria().andIdIn(requestParam.getIds());
        aliexpressActivityRegistrationService.updateByExampleSelective(activityRegistration, example);
        return ApiResult.newSuccess();
    }


    @PostMapping(value = "/replaceGeneratedFile/{id}")
    public ApiResult<?> replaceGeneratedFile(@RequestParam(value = "file", required = true) MultipartFile file,@PathVariable Long id) throws IOException {

        AliexpressActivityRegistration aliexpressActivityRegistration = new AliexpressActivityRegistration();
        aliexpressActivityRegistration.setId(id);
        aliexpressActivityRegistration.setSubmitProCount(0);

        /*更改商品总数与报名商品总数*/
        PlatSubmitFileListener platSubmitFileListener = new PlatSubmitFileListener(aliexpressActivityRegistration);
        EasyExcel.read(file.getInputStream(), SubmitFileVO.class, platSubmitFileListener).sheet().headRowNumber(2).doRead();

        if (0==aliexpressActivityRegistration.getSubmitProCount()){
            aliexpressActivityRegistration.setConfirmationStatus(ActivityConfirmationStatusEnum.CONFIRMED_NO_UPLOAD.getCode());
            aliexpressActivityRegistration.setUploadStatus(ActivityUploadStatusEnum.NO_NEED_UPLOAD.getCode());
        }else if(aliexpressActivityRegistration.getSubmitProCount()>0){
            aliexpressActivityRegistration.setConfirmationStatus(ActivityConfirmationStatusEnum.CONFIRMED_UPLOAD.getCode());
            aliexpressActivityRegistration.setUploadStatus(ActivityUploadStatusEnum.TO_BE_UPLOADED.getCode());
        }
        String fileName = file.getOriginalFilename();
        // 创建临时文件
        File tempFile = File.createTempFile(fileName, null);
        file.transferTo(tempFile);

        // 上传文件服务器
        ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(tempFile, file.getOriginalFilename(), ExcelConstant.smtExcel, WebUtils.getUserName());
        String url = null;
        if (!uploadResult.isSuccess()) {
            throw new RuntimeException("上传文件服务器报错：" + uploadResult.getErrorMsg());
        }

        SeaweedFile seaweedFile = uploadResult.getResult();
        if (null != seaweedFile) {
            url = seaweedFile.getUrl2();
            //更新库
            aliexpressActivityRegistration.setGeneratedFile(url);
            aliexpressActivityRegistration.setUploadStatusModifiedBy(WebUtils.getUserName());
            aliexpressActivityRegistration.setUploadStatusModifiedAt(new Timestamp(System.currentTimeMillis()));
            aliexpressActivityRegistration.setRegistrationTemplateFileSize(Double.valueOf(seaweedFile.getSize()));
            aliexpressActivityRegistrationService.updateByPrimaryKeySelective(aliexpressActivityRegistration);
        }

        tempFile.delete();
        return ApiResult.newSuccess();
    }


    @PostMapping(value = "/download/{id}")
    public void download(HttpServletResponse response, @PathVariable Long id) throws IOException {
        AliexpressActivityRegistration aliexpressActivityRegistration = aliexpressActivityRegistrationService.selectByPrimaryKey(id);

        if (ObjectUtils.isEmpty(aliexpressActivityRegistration) || StringUtils.isBlank(aliexpressActivityRegistration.getGeneratedFile())){
            throw new BusinessException("数据不存在");
        }

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> responseEntity = restTemplate.getForEntity(aliexpressActivityRegistration.getGeneratedFile(), byte[].class);
        byte[] fileBytes = responseEntity.getBody();
        response.getOutputStream().write(fileBytes);
        response.flushBuffer();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String[] split = aliexpressActivityRegistration.getGeneratedFile().split("/");

        String fileName = URLEncoder.encode(split[split.length-1], StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
    }

    public void isSmtAuth(AliexpressActivityRegistrationCriteria query) {
        List<String> accountNumbers = query.getAccountNumberList();

        List<String> managerIds = StringUtils.isBlank(query.getSalesSupervisorList()) ? Collections.emptyList() : Collections.singletonList(query.getSalesSupervisorList());
        List<String> leaderIds = StringUtils.isBlank(query.getSalesSupervisorList()) ? Collections.emptyList() : Collections.singletonList(query.getSaleTeamLeaderList());
        List<String> saleIds = query.getSaleManList();
        List<Integer> groupIds = query.getGroupIdList();
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setAccountNumberList(authAccountNumbers);
    }




    /**
     * 导出列表
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/downloadList")
    public ApiResult<?> downloadList( @RequestBody ApiRequestParam<String> requestParam) {
        CQuery<AliexpressActivityRegistrationCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressActivityRegistrationCriteria>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        isSmtAuth(cquery.getSearch());
        ResponseJson responseJson=aliexpressActivityRegistrationService.downloadList(cquery);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 导出活动文件
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/downloadActivityFile")
    public ApiResult<?> downloadActivityFile( @RequestBody ApiRequestParam<String> requestParam) {
        CQuery<AliexpressActivityRegistrationCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressActivityRegistrationCriteria>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        isSmtAuth(cquery.getSearch());
        ResponseJson responseJson=aliexpressActivityRegistrationService.downloadActivityFile(cquery);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 导入确认文件
     * @param criterias
     * @return
     */
    @PostMapping(value = "/importActivityRegistrationFile")
    public ApiResult<?> importActivityRegistrationFile( @RequestBody List<AliexpressActivityRegistrationCriteria> criterias) {
        ResponseJson responseJson=aliexpressActivityRegistrationService.importActivityRegistrationFile(criterias);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 导入确认文件回调接口
     * @param uploadTime
     * @return
     */
    @PostMapping(value = "/importActivityRegistrationFileBack/{uploadTime}")
    public ApiResult<?> importActivityRegistrationFileBack(@RequestParam(value = "files", required = true) MultipartFile[] files,@PathVariable Timestamp uploadTime) throws IOException{
        AliexpressActivityRegistrationCriteria criteria=new AliexpressActivityRegistrationCriteria();
        criteria.setUploadTime(uploadTime);
        ResponseJson responseJson=aliexpressActivityRegistrationService.importActivityRegistrationFileBack(files,criteria);
        if(!responseJson.isSuccess()){
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess();
    }

}