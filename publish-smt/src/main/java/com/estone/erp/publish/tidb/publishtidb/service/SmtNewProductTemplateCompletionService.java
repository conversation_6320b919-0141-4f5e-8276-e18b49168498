package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductTemplateCompletionDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductTemplateCompletion;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface SmtNewProductTemplateCompletionService extends IService<SmtNewProductTemplateCompletion> {

    /**
     * 有权限分页查询
     * @param dto
     * @return
     */
    IPage<SmtNewProductTemplateCompletion> pageQuery(SmtNewProductTemplateCompletionDto dto);

    /**
     * 无权限查询
     * @param dto
     * @return
     */
    IPage<SmtNewProductTemplateCompletion> page(SmtNewProductTemplateCompletionDto dto);

    List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtNewProductTemplateCompletion> wrapper);

    void download(SmtNewProductTemplateCompletionDto dto);

    void downloadUnfinishedSpu(SmtNewProductTemplateCompletionDto dto);
}
