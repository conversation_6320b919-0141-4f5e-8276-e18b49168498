package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Entity com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport
 */
public interface SmtFreightTemplateCodeImportMapper extends BaseMapper<SmtFreightTemplateCodeImport> {

    List<Map<Object, Object>> getTidbPageMetaMap(@Param(Constants.WRAPPER) LambdaQueryWrapper<SmtFreightTemplateCodeImport> wrapper);

}




