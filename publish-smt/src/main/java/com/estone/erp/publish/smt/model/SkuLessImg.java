package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SkuLessImg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelIgnore
    private Long id;

    /**
     * sku
     */
    @ExcelProperty("sku")
    private String sku;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Timestamp createTime;
}