package com.estone.erp.publish.smt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AliexpressMarketingConfigLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_marketing_config_log.id
     */
    private Integer id;

    /**
     * 配置id database column aliexpress_marketing_config_log.marketing_id
     */
    private Integer marketingId;

    /**
     * 操作的属性 database column aliexpress_marketing_config_log.operate_attr
     */
    private String operateAttr;

    /**
     * 改前值 database column aliexpress_marketing_config_log.previous_value
     */
    private String previousValue;

    /**
     * 改后值 database column aliexpress_marketing_config_log.after_value
     */
    private String afterValue;

    /**
     * 操作者 database column aliexpress_marketing_config_log.operator
     */
    private String operator;

    /**
     * 操作时间 database column aliexpress_marketing_config_log.operate_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    /**
     * 1=营销配置，2=店铺分组
     */
    private Integer type;
}