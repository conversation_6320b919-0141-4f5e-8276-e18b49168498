package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/8/1010:12
 */
public enum SpuRequestTypeEnum {

    CREATE_TEMPLATE(10, "生成模板"),

    PUBLISHING(20, "直接刊登"),

    TIMING_PUBLISHING(30, "定时刊登");

    private int code;

    private String name;

    private SpuRequestTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static SpuRequestTypeEnum build(int code) {
        SpuRequestTypeEnum[] values = values();
        for (SpuRequestTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        SpuRequestTypeEnum[] values = values();
        for (SpuRequestTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

    public Integer integerCode(){return Integer.valueOf(this.code);}

}
