package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class AliSystemCategoryMapping implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column ali_system_category_mapping.id
     */
    private Integer id;

    /**
     * 是否启用 database column ali_system_category_mapping.enable
     */
    private Boolean enable;

    /**
     * 系统类目完整分类 database column ali_system_category_mapping.system_category_full_code
     */
    private String systemCategoryFullCode;

    /**
     * 系统类目code database column ali_system_category_mapping.system_category_code
     */
    private String systemCategoryCode;

    /**
     * 系统类目上级code database column ali_system_category_mapping.system_category_parent_code
     */
    private String systemCategoryParentCode;

    /**
     * 系统类目名称 database column ali_system_category_mapping.system_category_full_name
     */
    private String systemCategoryFullName;

    /**
     * 平台类目codes database column ali_system_category_mapping.platform_category_codes
     */
    private String platformCategoryCodes;

    /**
     * 平台完整类目名称截取前10个 database column ali_system_category_mapping.platform_category_names
     */
    private String platformCategoryNames;

    /**
     * 创建人 database column ali_system_category_mapping.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column ali_system_category_mapping.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column ali_system_category_mapping.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column ali_system_category_mapping.update_date
     */
    private Timestamp updateDate;

    private String codeEnName;

    public String getCodeEnName() {
        if(StringUtils.isBlank(this.codeEnName) && StringUtils.isNotBlank(this.getSystemCategoryFullName())){
            List<String> strings = CommonUtils.splitList(this.getSystemCategoryFullName(), ">");
            return strings.get(strings.size() - 1);
        }
        return codeEnName;
    }

    public void setCodeEnName(String codeEnName) {
        this.codeEnName = codeEnName;
    }
}