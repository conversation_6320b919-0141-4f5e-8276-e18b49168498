package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressNewRemind implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_new_remind.id
     */
    private Integer id;

    /**
     * 货号 database column aliexpress_new_remind.spu
     */
    private String spu;

    /**
     * 店铺 database column aliexpress_new_remind.account
     */
    private String account;

    private String firstImage;

    private String title;

    /**
     * 经营大类 database column aliexpress_new_remind.root_category
     */
    private String rootCategory;

    /**
     * 经营大类中文名 database column aliexpress_new_remind.root_category_zhname
     */
    private String rootCategoryZhname;

    /**
     * 编辑完成时间 database column aliexpress_new_remind.edit_finish_time
     */
    private Timestamp editFinishTime;

    /**
     * 产品录入时间
     */
    private Timestamp createAt;

    /**
     * 推送时间 database column aliexpress_new_remind.push_time
     */
    private Timestamp pushTime;

    /**
     * 是否有成功模板 database column aliexpress_new_remind.is_success_temp
     */
    private Boolean isSuccessTemp;

    /**
     * 模板创建时间 database column aliexpress_new_remind.temp_finish_time
     */
    private Timestamp tempFinishTime;

    /**
     * 模板创建人
     */
    private String tempCreator;

    /**
     * 创建人 database column aliexpress_new_remind.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_new_remind.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_new_remind.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_new_remind.last_update_date
     */
    private Timestamp lastUpdateDate;

    private String saleMan;

    private String saleLeaderMan;

    //文案
    private String editor;

    //刊登角色
    private Integer publishRole;

    private String remarks;

    //刊登状态
    private Integer publishStatus;

    //失败备注
    private String failInfo;

    //用到的模板id
    private Integer templateId;

    private String tagCodes;

    // 模板创建人名字 辅助字段
    private String tempCreatorName;
}