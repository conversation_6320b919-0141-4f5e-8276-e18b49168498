package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> aliexpress_car_type_info
 * 2020-06-11 15:50:37
 */
@Data
public class AliexpressCarTypeInfoCriteria extends AliexpressCarTypeInfo {
    private static final long serialVersionUID = 1L;

    private List<Integer> ids = new ArrayList<>();

    public AliexpressCarTypeInfoExample getExample() {
        AliexpressCarTypeInfoExample example = new AliexpressCarTypeInfoExample();
        AliexpressCarTypeInfoExample.Criteria criteria = example.createCriteria();

        if(CollectionUtils.isNotEmpty(this.getIds())){
            criteria.andIdIn(this.getIds());
        }

        if (this.getTypeLevel() != null) {
            criteria.andTypeLevelEqualTo(this.getTypeLevel());
        }
        if (StringUtils.isNotBlank(this.getCarType())) {
            criteria.andCarTypeEqualTo(this.getCarType());
        }
        if (this.getParentId() != null) {
            criteria.andParentIdEqualTo(this.getParentId());
        }
        if (this.getParam1() != null) {
            criteria.andParam1EqualTo(this.getParam1());
        }
        if (StringUtils.isNotBlank(this.getParam2())) {
            criteria.andParam2EqualTo(this.getParam2());
        }

        if(this.getTypeId() != null){
            criteria.andTypeIdEqualTo(this.getTypeId());
        }

        if (this.getCountryUpId() != null) {
            criteria.andCountryUpIdEqualTo(this.getCountryUpId());
        }
        if (this.getCountryId() != null) {
            criteria.andCountryIdEqualTo(this.getCountryId());
        }
        if (StringUtils.isNotBlank(this.getCountryEn())) {
            criteria.andCountryEnEqualTo(this.getCountryEn());
        }
        if (StringUtils.isNotBlank(this.getCountryZh())) {
            criteria.andCountryZhEqualTo(this.getCountryZh());
        }
        if (this.getMakeUpId() != null) {
            criteria.andMakeUpIdEqualTo(this.getMakeUpId());
        }
        if (this.getMakeId() != null) {
            criteria.andMakeIdEqualTo(this.getMakeId());
        }
        if (StringUtils.isNotBlank(this.getMakeEn())) {
            criteria.andMakeEnEqualTo(this.getMakeEn());
        }
        if (StringUtils.isNotBlank(this.getMakeZh())) {
            criteria.andMakeZhEqualTo(this.getMakeZh());
        }
        if (this.getModelUpId() != null) {
            criteria.andModelUpIdEqualTo(this.getModelUpId());
        }
        if (this.getModelId() != null) {
            criteria.andModelIdEqualTo(this.getModelId());
        }
        if (StringUtils.isNotBlank(this.getModelEn())) {
            criteria.andModelEnEqualTo(this.getModelEn());
        }
        if (StringUtils.isNotBlank(this.getModelZh())) {
            criteria.andModelZhEqualTo(this.getModelZh());
        }
        if (this.getYearUpId() != null) {
            criteria.andYearUpIdEqualTo(this.getYearUpId());
        }
        if (this.getYearId() != null) {
            criteria.andYearIdEqualTo(this.getYearId());
        }
        if (StringUtils.isNotBlank(this.getYearEn())) {
            criteria.andYearEnEqualTo(this.getYearEn());
        }
        if (StringUtils.isNotBlank(this.getYearZh())) {
            criteria.andYearZhEqualTo(this.getYearZh());
        }
        if (this.getTrimUpId() != null) {
            criteria.andTrimUpIdEqualTo(this.getTrimUpId());
        }
        if (this.getTrimId() != null) {
            criteria.andTrimIdEqualTo(this.getTrimId());
        }
        if (StringUtils.isNotBlank(this.getTrimEn())) {
            criteria.andTrimEnEqualTo(this.getTrimEn());
        }
        if (StringUtils.isNotBlank(this.getTrimZh())) {
            criteria.andTrimZhEqualTo(this.getTrimZh());
        }
        if (this.getEnigneUpId() != null) {
            criteria.andEnigneUpIdEqualTo(this.getEnigneUpId());
        }
        if (this.getEnigneId() != null) {
            criteria.andEnigneIdEqualTo(this.getEnigneId());
        }
        if (StringUtils.isNotBlank(this.getEnigneEn())) {
            criteria.andEnigneEnEqualTo(this.getEnigneEn());
        }
        if (StringUtils.isNotBlank(this.getEnigneZh())) {
            criteria.andEnigneZhEqualTo(this.getEnigneZh());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateTime() != null) {
            criteria.andUpdateTimeEqualTo(this.getUpdateTime());
        }
        return example;
    }
}