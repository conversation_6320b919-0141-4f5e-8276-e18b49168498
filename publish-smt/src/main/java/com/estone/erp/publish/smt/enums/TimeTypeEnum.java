package com.estone.erp.publish.smt.enums;

import lombok.Getter;

@Getter
public enum TimeTypeEnum {
    DAY("day","天"),
    WEEK("week","周"),
    MONTH("month","月"),
    YEAR("year","年"),
            ;

    private String code;
    private String name;

    private TimeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isTrue(String val) {
        return this.code.equals(val);
    }
}
