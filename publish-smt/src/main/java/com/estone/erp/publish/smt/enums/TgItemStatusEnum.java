package com.estone.erp.publish.smt.enums;

/**
 * 同步产品的状态
 */
public enum TgItemStatusEnum {

    ONLINE("ONLINE", "在线"),
    PENDING_LAUNCH("PENDING_LAUNCH", "待上架"),
    PENDING_APPROVAL("PENDING_APPROVAL", "审核中"),
    VIOLATION_QC_FAILED("VIOLATION_QC_FAILED", "审核不通过"),
    OFFLINE("OFFLINE", "已下架"),
    ;

    private String code;

    private String name;

    private TgItemStatusEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }
}
