package com.estone.erp.publish.smt.enums;

/**
 * 审核状态
 */
public enum ExamineStateEnum {

    WAIT_EXAMINE("wait_examine", "待审核"),

    PASS_EXAMINE("pass_examine", "审核成功"),

    NOPASS_EXAMINE("nopass_examine", "审核失败"),
    ;

    private String code;

    private String name;

    private ExamineStateEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        ExamineStateEnum[] values = values();
        for (ExamineStateEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
