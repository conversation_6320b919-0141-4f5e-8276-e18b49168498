package com.estone.erp.publish.smt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmtMarketingSingleDiscount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 平台单品折扣id（平台标识）
     */
    private Long singleDiscountId;

    /**
     * 折扣名称
     */
    private String name;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTime;

    /**
     * 状态(未生效=1，生效中=2，已结束=3,4=已暂停)
     */
    private Integer status;

    /**
     * 活动关联商品数量
     */
    private Integer singleDiscountProdNum;

    /**
     * 店铺在线-关联商品数量
     */
    private Integer noLinkNum;

    /**
     * 最新报名时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastSubmitTime;

    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp syncTime;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 更新时间
     */
    private Timestamp updatedTime;

    /**
     * 对应的配置id
     */
    private Integer configId;

    /**
     * 规格内容
     */
    private String ruleJson;


    private Integer listingNum;

    /**
     * 创建人类学
     * 1 销售
     * 2 系统
     */
    private Integer createdType;

    /**
     * 销售
     */
    private String salesMan;

    /**
     * 销售组长
     */
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    private String salesSupervisorName;
}