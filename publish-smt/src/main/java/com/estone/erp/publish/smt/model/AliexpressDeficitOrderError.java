package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressDeficitOrderError implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_deficit_order_error.id
     */
    private Integer id;

    /**
     * body database column aliexpress_deficit_order_error.body
     */
    private String body;

    /**
     * 错误信息 database column aliexpress_deficit_order_error.error
     */
    private String error;

    /**
     * 推送时间 database column aliexpress_deficit_order_error.create_date
     */
    private Timestamp createDate;
}