package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.hr.HrClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.SaleStructureVO;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductRecommendationDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductRecommendationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * smt
 * </p>
 *
 * <AUTHOR>
 * @since 2024年12月1日17:32:58
 */
@Slf4j
@RestController
@RequestMapping("/smtNewProductRecommendation")
public class SmtNewProductRecommendationController {
    @Resource
    private SmtNewProductRecommendationService smtNewProductRecommendationService;
    @Resource
    private HrClient hrClient;

    /**
     * 导入分配销售
     * @param file
     * @return
     */
    @PostMapping(value = "/importUpdateSale")
    public ApiResult<String> saveOrUpdate(@RequestParam("file") MultipartFile file) {
        Asserts.isTrue(file != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            String errorMsg = smtNewProductRecommendationService.importUpdateSale(file);
            if (StringUtils.isNotBlank(errorMsg)) {
                log.error("分配失败：{}", errorMsg);
                return ApiResult.newError(errorMsg);
            }
        } catch (Exception e) {
            log.error("分配失败：", e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("分配成功");
    }

    /**
     * 分页查询
     */
    @PostMapping("queryPage")
    public ApiResult<IPage<SmtNewProductRecommendation>> queryPage(@RequestBody SmtNewProductRecommendationDto smtNewProductRecommendationDto) {
        Asserts.isTrue(smtNewProductRecommendationDto != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            IPage<SmtNewProductRecommendation> page = smtNewProductRecommendationService.pageQuery(smtNewProductRecommendationDto);
            return ApiResult.newSuccess(page);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 获取smt 新品推荐组长结构
     * @return
     */
    @GetMapping("getLeader")
    public ApiResult<?> leaderInfo(){
        Map<String, String> argsMap = new HashMap<>(2);
        argsMap.put("args", SaleChannel.CHANNEL_SMT);
        return hrClient.getLastLeaderMembersByPlatform(JSON.toJSONString(argsMap));
    }

    /**
     * 分配销售
     */
    @PostMapping("matchSale")
    public ApiResult<String> matchSale(@RequestBody SmtNewProductRecommendationDto smtNewProductRecommendationDto) {
        if(smtNewProductRecommendationDto == null){
            return ApiResult.newError("参数不能为空");
        }
        String idStr = smtNewProductRecommendationDto.getIdStr();
        String sale = smtNewProductRecommendationDto.getSale();
        if(idStr == null || StringUtils.isBlank(sale)){
            return ApiResult.newError("idStr 和 sale参数不能为空");
        }
        List<Long> longs = CommonUtils.splitLongList(idStr, ",");
        for (Long id : longs) {
            try {
                SmtNewProductRecommendation productRecommendation = smtNewProductRecommendationService.getById(id);
                String saleLeader = productRecommendation.getSaleLeader();
                if(StringUtils.isBlank(saleLeader)){
                    SaleStructureVO saleSuperiorNew = NewUsermgtUtils.getSaleSuperiorNew(sale);
                    if(StringUtils.isNotBlank(saleSuperiorNew.getErrorMsg())){
                        log.info(saleSuperiorNew.getErrorMsg());
                    }
                    String saleLeader1 = saleSuperiorNew.getSaleLeader();
                    if(StringUtils.isNotBlank(saleLeader1) && StringUtils.contains(saleLeader1, "-")){
                        String s = saleLeader1.split("-")[1];
                        productRecommendation.setSaleLeader(s);
                    }
                }
                productRecommendation.setSale(sale);
                productRecommendation.setUpdatedTime(new Date());
                smtNewProductRecommendationService.updateById(productRecommendation);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 备注
     */
    @PostMapping("remarks")
    public ApiResult<String> remark(@RequestBody SmtNewProductRecommendationDto smtNewProductRecommendationDto) {
        if(smtNewProductRecommendationDto == null){
            return ApiResult.newError("参数不能为空");
        }
        Long id = smtNewProductRecommendationDto.getId();
        String remark = smtNewProductRecommendationDto.getRemark();

        if(id == null || StringUtils.isBlank(remark)){
            return ApiResult.newError("id 和 remark参数不能为空");
        }
        SmtNewProductRecommendation productRecommendation = smtNewProductRecommendationService.getById(id);
        productRecommendation.setRemark(remark);
        productRecommendation.setUpdatedTime(new Date());
        smtNewProductRecommendationService.updateById(productRecommendation);
        return ApiResult.newSuccess();
    }


    /**
     * 导出
     */
    @PostMapping("export")
    public ApiResult<?> export(@RequestBody SmtNewProductRecommendationDto dto) throws IOException {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        return smtNewProductRecommendationService.download(dto);
    }
}
