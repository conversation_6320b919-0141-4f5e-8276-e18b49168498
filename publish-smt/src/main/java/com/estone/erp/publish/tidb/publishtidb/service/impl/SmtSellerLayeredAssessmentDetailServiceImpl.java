package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.SmtSellerLayeredAssessmentDetail;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtSellerLayeredAssessmentDetailMapper;
import com.estone.erp.publish.tidb.publishtidb.service.ISmtSellerLayeredAssessmentDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * smt卖家分层详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class SmtSellerLayeredAssessmentDetailServiceImpl extends ServiceImpl<SmtSellerLayeredAssessmentDetailMapper, SmtSellerLayeredAssessmentDetail> implements ISmtSellerLayeredAssessmentDetailService {

}
