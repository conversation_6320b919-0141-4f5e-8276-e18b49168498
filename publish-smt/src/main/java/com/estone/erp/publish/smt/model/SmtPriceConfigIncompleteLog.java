package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class SmtPriceConfigIncompleteLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_price_config_incomplete_log.id
     */
    private Integer id;

    /**
     * 账号 database column smt_price_config_incomplete_log.account
     */
    private String account;

    /**
     * 天（按天查询问题） database column smt_price_config_incomplete_log.day
     */
    private Date day;
}