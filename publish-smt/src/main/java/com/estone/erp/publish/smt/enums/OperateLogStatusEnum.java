package com.estone.erp.publish.smt.enums;

public enum OperateLogStatusEnum {

    wait(10, "待处理"),

    processing(20, "处理中"),

    end(30, "处理完成");

    private int code;

    private String name;

    private OperateLogStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static OperateLogStatusEnum build(int code) {
        OperateLogStatusEnum[] values = values();
        for (OperateLogStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        OperateLogStatusEnum[] values = values();
        for (OperateLogStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
