package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressScItemInvitationPageQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.BatchRejectIds;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressScItemInvitation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * smt 半托管抢占入仓 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface AliexpressScItemInvitationService extends IService<AliexpressScItemInvitation> {

    IPage<AliexpressScItemInvitation> pageQuery(AliexpressScItemInvitationPageQueryDto dto);

    void batchConfirm(List<AliexpressScItemInvitation> aliexpressScItemInvitations);

    void batchReject(BatchRejectIds batchRejectIds);

    ResponseJson  export(AliexpressScItemInvitationPageQueryDto dto);
}
