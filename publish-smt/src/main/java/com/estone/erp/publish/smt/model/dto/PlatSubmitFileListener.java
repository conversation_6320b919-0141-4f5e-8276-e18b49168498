package com.estone.erp.publish.smt.model.dto;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-24 16:55
 **/
@Data
public class PlatSubmitFileListener extends AnalysisEventListener<SubmitFileVO> {
    private AliexpressActivityRegistration aliexpressActivityRegistration;


    public PlatSubmitFileListener(AliexpressActivityRegistration aliexpressActivityRegistration) {
        this.aliexpressActivityRegistration=aliexpressActivityRegistration;
    }

    @Override
    public void invoke(SubmitFileVO data, AnalysisContext context) {
        if (ObjectUtils.isNotEmpty(data) &&  ("Y".equals(data.getSubmit()) || "y".equals(data.getSubmit()))){
            aliexpressActivityRegistration.setSubmitProCount(aliexpressActivityRegistration.getSubmitProCount()+1);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
