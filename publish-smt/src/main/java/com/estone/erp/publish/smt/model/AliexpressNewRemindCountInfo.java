package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressNewRemindCountInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_new_remind_count_info.id
     */
    private Integer id;

    private Integer publishType;

    private String editor;

    /**
     * 销售 database column aliexpress_new_remind_count_info.sale_man
     */
    private String saleMan;

    /**
     * 店铺 database column aliexpress_new_remind_count_info.account
     */
    private String account;

    /**
     * 推送时间 database column aliexpress_new_remind_count_info.push_time
     */
    private Timestamp pushTime;

    /**
     * 当天需刊登spu数 database column aliexpress_new_remind_count_info.today_need_up_count
     */
    private Integer todayNeedUpCount;

    /**
     * 当天未刊登spu数 database column aliexpress_new_remind_count_info.today_no_up_count
     */
    private Integer todayNoUpCount;

    /**
     * 当天新品上架率 database column aliexpress_new_remind_count_info.today_up_rate
     */
    private Double todayUpRate;

    /**
     * 4天需刊登spu数 database column aliexpress_new_remind_count_info.four_need_up_count
     */
    private Integer fourNeedUpCount;

    /**
     * 4天未刊登spu数 database column aliexpress_new_remind_count_info.four_no_up_count
     */
    private Integer fourNoUpCount;

    /**
     * 4天新品上架率 database column aliexpress_new_remind_count_info.four_up_rate
     */
    private Double fourUpRate;

    /**
     * 创建人 database column aliexpress_new_remind_count_info.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_new_remind_count_info.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_new_remind_count_info.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_new_remind_count_info.last_update_date
     */
    private Timestamp lastUpdateDate;
}