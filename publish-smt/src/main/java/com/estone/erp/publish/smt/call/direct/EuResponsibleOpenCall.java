package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 获取欧盟负责人接口
 */
@Slf4j
public class EuResponsibleOpenCall {

    /**
     * {"body":{"key":[{"msr_eu_id":-2,"name":"暂不关联"},{"msr_eu_id":430,"name":"SHUNSHUN GmbH"}]},"errors":[],"status":"200","success":true}
     * @param saleAccountByAccountNumber
     * @param categoryId
     * @return
     */
    public ResponseJson euResponsible(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Integer categoryId,
                                       boolean isTg){
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        if (categoryId == null || saleAccountByAccountNumber == null) {
            rsp.setMessage("请求参数为空！");
            return rsp;
        }

        if(isTg){
            if(StringUtils.isBlank(saleAccountByAccountNumber.getColStr3()) || StringUtils.isBlank(saleAccountByAccountNumber.getColStr2())){
                rsp.setMessage("托管店铺必须有渠道和sellerId！");
                return rsp;
            }
        }

        String callRspStr = "";
        try{
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.category.eu.responsible.persons.list");
            request.setHttpMethod("GET");
            request.addApiParameter("category_id", categoryId.toString());
            if(isTg){
                request.addApiParameter("channel", saleAccountByAccountNumber.getColStr3());
                request.addApiParameter("channel_seller_id", saleAccountByAccountNumber.getColStr2());
            }

            long begin = System.currentTimeMillis();
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            callRspStr = iopResponse.getBody();
            long end = System.currentTimeMillis();
            long l = (end - begin) / 1000;
            if (l > AbstractSmtOpenCall.logTime) {
//                log.warn(String.format("eu.responsible不通过奇门%s秒 rsp%s", l, callRspStr));
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage() + callRspStr);
        }
        return checkErrorMessage(callRspStr);
    }

    /**
     *
     * @param callRspStr
     * @return
     */
    public ResponseJson checkErrorMessage(String callRspStr) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

        if(StringUtils.isBlank(callRspStr)) {
            responseJson.setMessage("请求无结果返回，请联系IT人员");
            return responseJson;
        }

        try{
            JSONObject callRspJson = JSONObject.parseObject(callRspStr);
            if(callRspJson != null) {
                if(callRspJson.containsKey("aliexpress_category_eu_responsible_persons_list_response")){
                    responseJson.setStatus(StatusCode.SUCCESS);
                    JSONObject response = callRspJson.getJSONObject("aliexpress_category_eu_responsible_persons_list_response");
                    if(response != null) {
                        if(response.containsKey("eu_contact_module_list")){
                            JSONObject eu_contact_module_list = response.getJSONObject("eu_contact_module_list");
                            if(eu_contact_module_list.containsKey("eu_contact_module")){
                                JSONArray eu_contact_module = eu_contact_module_list.getJSONArray("eu_contact_module");
                                responseJson.getBody().put("key", eu_contact_module);
                            }
                        }
                    }
                }
                else{
                    responseJson.setMessage(callRspStr);
                }
            }

        }catch (Exception e) {
            log.error(e.getMessage(), e);
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage(e.getMessage() + callRspStr);
        }
        return responseJson;
    }
}
