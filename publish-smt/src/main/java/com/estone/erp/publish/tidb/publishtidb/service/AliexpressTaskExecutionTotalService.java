package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalHistoryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalDateRangeDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalExportDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionTotalQueryDto;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionTotal;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【aliexpress_task_execution_total】的数据库操作Service
* @createDate 2024-10-11 17:31:42
*/
public interface AliexpressTaskExecutionTotalService extends IService<AliexpressTaskExecutionTotal> {

    IPage<AliexpressTaskExecutionTotal> pageQuery(AliexpressTaskExecutionTotalQueryDto dto);

    List<AliexpressTaskExecutionDetailsToTotalHistoryDto> getHistory(AliexpressTaskExecutionTotalDateRangeDto dto);

    ResponseJson export(AliexpressTaskExecutionTotalExportDto dto);
}
