package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

public enum TgItemProductTypeEnum {
    CF("0", "仓发"),
    JIT("1", "国内履约"),
    overseas("2", "海外备仓"),
    ;
    private String code;

    private String name;

    private TgItemProductTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        if(StringUtils.isBlank(code)){
            return "";
        }
        TgItemProductTypeEnum[] values = values();
        for (TgItemProductTypeEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
