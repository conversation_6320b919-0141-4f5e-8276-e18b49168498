package com.estone.erp.publish.smt.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.ResultModel;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.service.PictureUploadService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.PictureCommon;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.AliexpressListingDataSourceEnum;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.*;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EprEcoContributionsInfo;
import com.estone.erp.publish.elasticsearch.model.EsAliexpressEprEcoFee;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.elasticsearch2.util.EsAliexpressProductListingUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.smt.bean.*;
import com.estone.erp.publish.smt.bean.SkuProperty.AeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.product.AeopSkuPropertyList;
import com.estone.erp.publish.smt.bean.SkuProperty.product.ProductSkuProperty;
import com.estone.erp.publish.smt.bean.brand.BrandDO;
import com.estone.erp.publish.smt.bean.excel.DiscountProExcel;
import com.estone.erp.publish.smt.bean.excel.PopStockExcel;
import com.estone.erp.publish.smt.bean.excel.UpdateStockExcel;
import com.estone.erp.publish.smt.call.direct.*;
import com.estone.erp.publish.smt.call.direct.v2.OfferEditProductOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferPostProductOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferQueryProductOpenCall;
import com.estone.erp.publish.smt.call.direct.wholesale.WholesaleCall;
import com.estone.erp.publish.smt.call.direct.wholesale.bean.DiscountNum;
import com.estone.erp.publish.smt.call.direct.wholesale.bean.DiscountParam;
import com.estone.erp.publish.smt.componet.AliexpressProductMonitorHelper;
import com.estone.erp.publish.smt.componet.AliexpressProductMoveHelper;
import com.estone.erp.publish.smt.componet.SmtItemEsBulkProcessor;
import com.estone.erp.publish.smt.enums.*;
import com.estone.erp.publish.smt.helper.AliexpressProductLogHelper;
import com.estone.erp.publish.smt.helper.DiscountProExcelHelper;
import com.estone.erp.publish.smt.helper.SingleDiscountOrProductHelper;
import com.estone.erp.publish.smt.mapper.AliexpressEsExtendMapper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.AliexpressProductListingMsgDto;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateImageTitleRequest;
import com.estone.erp.publish.smt.model.dto.EsAliexpressProductListingResponse;
import com.estone.erp.publish.smt.mq.bean.HolidayStockMqBean;
import com.estone.erp.publish.smt.mq.bean.TidbAreaResult;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import com.estone.erp.publish.smt.mq.publish.bean.PublishBean;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.smt.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.fms.enums.VideoTypeEnum;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.*;
import com.estone.erp.publish.system.product.bean.gt.GtProductDetail;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.GtProductUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.model.SmtAdjustPriceItemPool;
import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountry;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.estone.erp.publish.tidb.publishtidb.service.SmtAdjustPriceItemPoolService;
import com.estone.erp.publish.tidb.publishtidb.service.SmtHalfExitCountryService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_es_extend
 * 2021-11-23 18:46:22
 */
@Service("aliexpressEsExtendService")
@Slf4j
public class AliexpressEsExtendServiceImpl implements AliexpressEsExtendService {
    @Resource
    private AliexpressEsExtendMapper aliexpressEsExtendMapper;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private AliexpressProductGroupService aliexpressProductGroupService;
    @Resource
    private AliexpressFreightTemplateService aliexpressFreightTemplateService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private AliexpressTemplateService aliexpressTemplateService;
    @Resource
    private AliexpressConfigService aliexpressConfigService;
    @Resource
    private AliexpressDeficitOrderService aliexpressDeficitOrderService;
    @Autowired
    private AliexpressProductMonitorHelper productMonitorHelper;
    @Autowired
    private AliexpressAccountBrandService accountBrandService;
    @Autowired
    private AliexpressProductMoveHelper aliexpressProductMoveHelper;
    @Autowired
    private AliexpressEsExtendService aliexpressEsExtendService;
    @Resource
    private AliexpressConfigProfitService aliexpressConfigProfitService;
    @Resource
    private AliexpressEprEcoFeeEsService aliexpressEprEcoFeeEsService;
    @Resource
    private SmtItemEsBulkProcessor smtItemEsBulkProcessor;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private SingleItemEsService singleItemEsService;
    @Resource
    private AliexpressProductCategoryLabelService aliexpressProductCategoryLabelService;
    @Resource
    private AliexpressProductCategoryLabelItemService aliexpressProductCategoryLabelItemService;
    @Resource
    private AliexpressProductCategoryLabelSkuService aliexpressProductCategoryLabelSkuService;
    @Resource
    private AliexpressProductDiagnosisService aliexpressProductDiagnosisService;
    @Resource
    private SmtTidbDataUpdateLogService smtTidbDataUpdateLogService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AliexpressCategoryDeliveryDayService aliexpressCategoryDeliveryDayService;
    @Resource
    private SingleDiscountOrProductHelper singleDiscountOrProductHelper;
    @Resource
    private DiscountProExcelHelper discountProExcelHelper;
    @Resource
    private AliexpressProductLogHelper aliexpressProductLogHelper;
    @Resource
    private AliexpressProductForAreaPriceService aliexpressProductForAreaPriceService;
    @Resource
    private SmtAdjustPriceItemPoolService smtAdjustPriceItemPoolService;
    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private AliexpressAutoTemplateService aliexpressAutoTemplateService;
    @Resource
    private SmtAearPriceReLogService smtAearPriceReLogService;
    @Resource
    private SmtHalfExitCountryService smtHalfExitCountryService;
    @Resource
    private PictureUploadService pictureUploadService;


    /**
     * 需排除的收费类型
     */
    private static final List<String> excludeFeeTypeList = Arrays.asList("商品及物流包装 环保费", "Product/Logistics Packaging Fee");

    @Override
    public int countByExample(AliexpressEsExtendExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEsExtendMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressEsExtend> search(CQuery<AliexpressEsExtendCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressEsExtendCriteria query = cquery.getSearch();
        AliexpressEsExtendExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressEsExtendMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressEsExtend> aliexpressEsExtends = aliexpressEsExtendMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressEsExtend> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressEsExtends);
        return result;
    }

    @Override
    public AliexpressEsExtend selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressEsExtendMapper.selectByPrimaryKey(id);
    }


    public AliexpressEsExtend selectByAccountandProductId(String account, Long productId) {
        Assert.notNull(productId);
        AliexpressEsExtendExample extendExample = new AliexpressEsExtendExample();
        AliexpressEsExtendExample.Criteria criteria = extendExample.createCriteria();
        if (StringUtils.isNotBlank(account)) {
            criteria.andAliexpressAccountNumberEqualTo(account);
        }
        criteria.andProductIdEqualTo(productId);

        extendExample.setLimit(1);
        List<AliexpressEsExtend> aliexpressEsExtends = this.selectByExample(extendExample);
        if (CollectionUtils.isNotEmpty(aliexpressEsExtends)) {
            return aliexpressEsExtends.get(0);
        }
        return null;
    }

    @Override
    public List<AliexpressEsExtend> selectByExample(AliexpressEsExtendExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEsExtendMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressEsExtend record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressEsExtendMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressEsExtend record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEsExtendMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateOFFandOnByProductId(AliexpressEsExtend record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEsExtendMapper.updateOFFandOnByProductId(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressEsExtend record, AliexpressEsExtendExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEsExtendMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressEsExtendMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByProductId(List<Long> productId) {
        Assert.notNull(productId, "productId is null!");
        return aliexpressEsExtendMapper.deleteByProductId(productId);
    }

    @Override
    public void syncProductByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdStr(StringUtils.join(ids, ","));
        esRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return;
        }

        // 去除重复产品
        Map<Long, EsAliexpressProductListing> productMap = new HashMap<>();
        esAliexpressProductListing.forEach(p -> {
            Long productId = p.getProductId();
            if (productMap.get(productId) == null) {
                productMap.put(productId, p);
            }
        });

        if (productMap.size() == 0) {
            return;
        }

        for (EsAliexpressProductListing esProduct : productMap.values()) {
            String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
            AliexpressProduct product = new AliexpressProduct();
            product.setProductId(esProduct.getProductId());
            product.setAliexpressAccountNumber(aliexpressAccountNumber);
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
            AliexpressExecutors.executeSyncProduct(() -> {
                SynchItemOpenCall call = new SynchItemOpenCall();
                call.syncAliexpressProductInfo(saleAccountByAccountNumber, esProduct.getProductId());
            });
        }
    }

    @Override
    public String synchProduct(String response, EsAliexpressProductListing esAliexpressProductListing, SaleAccountAndBusinessResponse saleAccountByAccountNumber, String... field) {
        //多spu
        boolean isSpus = false;
        if (field != null && field.length > 0) {
            String s = field[0];
            if (StringUtils.equalsIgnoreCase(s, String.valueOf(AliexpressListingDataSourceEnum.SPUS.getCode()))) {
                isSpus = true;
            }
        }

        StringBuilder result = new StringBuilder();
        Long productId = esAliexpressProductListing.getProductId();
        String aliexpressAccountNumber = saleAccountByAccountNumber.getAccountNumber();
        if (StringUtils.isBlank(response)) {
            return null;
        }
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        //置空 弄全字段
        request.setQueryFields(null);
        request.setAliexpressAccountNumber(aliexpressAccountNumber);
        request.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
        List<EsAliexpressProductListing> dbEsProductList = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);

        //这种返回说明 速卖通后台已经删除
        if (StringUtils.containsIgnoreCase(response, "product is not exist") || StringUtils.containsIgnoreCase(response, "Product not found")) {
            //删除产品
            if (CollectionUtils.isNotEmpty(dbEsProductList)) {
                //dbEsProductList.forEach(t -> {
                //    String id = aliexpressAccountNumber + "-" + productId + "-" + t.getSkuId();
                //    t.setProductStatusType(AliexpressProductOperateLogType.DELETE);
                //    esAliexpressProductListingService.deleteById(id);
                //});
                //AliexpressEsExtend extend = this.selectByAccountandProductId(aliexpressAccountNumber, productId);
                //if(extend != null){
                //    this.deleteByPrimaryKey(Arrays.asList(extend.getExtendId()));
                //}
                //平台查询不到，将产品改成不在线状态
                dbEsProductList.forEach(t -> {
                    EsAliexpressProductListing productListing = new EsAliexpressProductListing();
                    productListing.setId(t.getId());
                    productListing.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
                    esAliexpressProductListingService.updateRequest(productListing);
                    //log.info("平台中查询不到该产品id-店铺:{},productId:{}",aliexpressAccountNumber,productId);
                });
                // 删除的链接监控
                productMonitorHelper.listingItemStatusListening(AliexpressProductOperateLogType.DELETE, dbEsProductList);
            }
            return null;
        }
        try {
            JSONObject object = JSONObject.parseObject(response);
            if (object.containsKey("aliexpress_postproduct_redefining_findaeproductbyid_response")) {
                JSONObject productRsp = object
                        .getJSONObject("aliexpress_postproduct_redefining_findaeproductbyid_response");
                if (productRsp.containsKey("result")) {
                    esAliexpressProductListing.setLastSyncTime(new Date());
                    //大字段
                    AliexpressEsExtend aliexpressEsExtend = new AliexpressEsExtend();
                    aliexpressEsExtend.setAliexpressAccountNumber(aliexpressAccountNumber);
                    aliexpressEsExtend.setProductId(productId);
                    //本地存在的skuid 集合，如果线上同步回来少于本地，本地的记录需要删除
                    List<String> exsitSkuIds = new ArrayList<>();
                    //接口返回的skuid
                    List<String> synchSkuIds = new ArrayList<>();
                    //productId + "_" + existSkuId  对应的 es唯一键
                    Map<String, String> existProductIdMaP = new HashMap<>();
                    //skuId 对应的 ES唯一键
                    Map<String, String> skuIdToProductIdMap = new HashMap<>();
                    AliexpressCategory aliexpressCategory = null;
                    if (CollectionUtils.isNotEmpty(dbEsProductList)) {
                        for (Iterator<EsAliexpressProductListing> iterator = dbEsProductList.iterator(); iterator
                                .hasNext(); ) {
                            EsAliexpressProductListing existProduct = iterator.next();
                            //唯一键
                            String existId = aliexpressAccountNumber + "-" + productId + "-" + existProduct.getSkuId();
                            String existSkuId = existProduct.getSkuId();
                            exsitSkuIds.add(existSkuId);
                            skuIdToProductIdMap.put(existSkuId, existId);
                            existProductIdMaP.put(productId + "_" + existSkuId, existId);
                        }
                    }
                    JSONObject obj = productRsp.getJSONObject("result");
                    esAliexpressProductListing.setAliexpressAccountNumber(aliexpressAccountNumber);
                    if (obj.containsKey("gmt_create")) {
                        esAliexpressProductListing.setGmtCreate(new Date(obj.getTimestamp("gmt_create").getTime()));
                    }
                    if (obj.containsKey("gmt_modified")) {
                        esAliexpressProductListing.setGmtModified(new Date(obj.getTimestamp("gmt_modified").getTime()));
                    }
                    if (obj.containsKey("detail")) {
                        esAliexpressProductListing.setDetail(obj.getString("detail"));
                    }
                    if (obj.containsKey("delivery_time")) {
                        esAliexpressProductListing.setDeliveryTime(obj.getInteger("delivery_time"));
                    }
                    if (obj.containsKey("owner_member_id")) {
                        esAliexpressProductListing.setOwnerMemberId(obj.getString("owner_member_id"));
                        aliexpressEsExtend.setOwnerMemberId(esAliexpressProductListing.getOwnerMemberId());
                    }
                    if (obj.containsKey("owner_member_seq")) {
                        esAliexpressProductListing.setOwnerMemberSeq(obj.getInteger("owner_member_seq"));
                    }
                    if (obj.containsKey("product_id")) {
                        esAliexpressProductListing.setProductId(obj.getLong("product_id"));
                    }
                    if (obj.containsKey("tax_type")) {
                        String tax_type = obj.getString("tax_type");
                        esAliexpressProductListing.setTaxType(tax_type);
                    } else {
                        //存在美国半托管 平台返回空
                        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
                        Long product_id = esAliexpressProductListing.getProductId();
                        AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
                        AliexpressHalfTgItemExample.Criteria criteria = example.createCriteria();
                        criteria.andAccountEqualTo(accountNumber).andProductIdEqualTo(product_id);
                        example.setFields("joined_country_list");
                        List<AliexpressHalfTgItem> tgItems = aliexpressHalfTgItemService.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(tgItems)) {
                            String joinedCountryList = tgItems.get(0).getJoinedCountryList();
                            if (joinedCountryList.contains("US")) {
                                esAliexpressProductListing.setTaxType("1");
                            }
                        }
                    }
                    if (obj.containsKey("hscode")) {
                        String hscode = obj.getString("hscode");
                        esAliexpressProductListing.setHscode(hscode);
                    }
                    if (obj.containsKey("category_id")) {
                        esAliexpressProductListing.setCategoryId(obj.getInteger("category_id"));
                        try {
                            aliexpressCategory = aliexpressCategoryService
                                    .selectByCategoryId(esAliexpressProductListing.getCategoryId());
                            esAliexpressProductListing.setIsCayType(aliexpressCategory.getCarType());
                            esAliexpressProductListing.setIsCnProvinceCategory(aliexpressCategory.getProvince());
                        } catch (Exception e) {
                            //忽略异常
                        }
                    }
                    if (obj.containsKey("subject")) {
                        esAliexpressProductListing.setSubject(obj.getString("subject"));
                    }
                    if (obj.containsKey("package_type")) {
                        esAliexpressProductListing.setPackageType(obj.getBoolean("package_type"));
                    }
                    if (obj.containsKey("lot_num")) {
                        esAliexpressProductListing.setLotNum(obj.getInteger("lot_num"));
                    }
                    if (obj.containsKey("package_length")) {
                        esAliexpressProductListing.setPackageLength(obj.getInteger("package_length"));
                    }
                    if (obj.containsKey("package_width")) {
                        esAliexpressProductListing.setPackageWidth(obj.getInteger("package_width"));
                    }
                    if (obj.containsKey("package_height")) {
                        esAliexpressProductListing.setPackageHeight(obj.getInteger("package_height"));
                    }
                    if (obj.containsKey("gross_weight")) {
                        esAliexpressProductListing.setGrossWeight(obj.getString("gross_weight"));
                    }
                    if (obj.containsKey("is_pack_sell")) {
                        esAliexpressProductListing.setIsPackSell(obj.getBoolean("is_pack_sell"));
                    }
                    if (obj.containsKey("reduce_strategy")) {
                        esAliexpressProductListing.setReduceStrategy(obj.getString("reduce_strategy"));
                    }
                    if (obj.containsKey("group_ids")) {
                        JSONObject groupIds = obj.getJSONObject("group_ids");
                        if (groupIds.containsKey("number")) {
                            JSONArray jsonArray = groupIds.getJSONArray("number");
                            List<Long> groupIdList = new ArrayList<>();
                            if (jsonArray != null) {
                                int size = jsonArray.size();
                                for (int i = 0; i < size; i++) {
                                    Long groupId = jsonArray.getLong(i);
                                    groupIdList.add(groupId);
                                }
                                if (CollectionUtils.isNotEmpty(groupIdList)) {
                                    esAliexpressProductListing.setGroupIds(StringUtils.join(groupIdList, ","));
                                }
                            }
                        }
                    }
                    if (obj.containsKey("bulk_discount")) {
                        esAliexpressProductListing.setBulkDiscount(obj.getInteger("bulk_discount"));
                    }
                    if (obj.containsKey("image_u_r_ls")) {
                        esAliexpressProductListing.setImageUrls(obj.getString("image_u_r_ls"));
                    }
                    if (obj.containsKey("product_unit")) {
                        esAliexpressProductListing.setProductUnit(obj.getInteger("product_unit"));
                    }
                    if (obj.containsKey("ws_valid_num")) {
                        esAliexpressProductListing.setWsValidNum(obj.getInteger("ws_valid_num"));
                    }
                    if (obj.containsKey("src")) {
                        esAliexpressProductListing.setSrc(obj.getString("src"));
                    }
                    if (obj.containsKey("ws_offline_date")) {
                        String wsOfflineDate = obj.getString("ws_offline_date");
                        Date parseDate = org.apache.commons.lang3.time.DateUtils
                                .parseDate(wsOfflineDate, "yyyy-MM-dd HH:mm:ss");
                        esAliexpressProductListing.setWsOfflineDate(new Timestamp(parseDate.getTime()));
                    }
                    if (obj.containsKey("ws_display")) {
                        esAliexpressProductListing.setWsDisplay(obj.getString("ws_display"));
                    }
                    if (obj.containsKey("product_status_type")) {
                        esAliexpressProductListing.setProductStatusType(obj.getString("product_status_type"));
                    }
                    if (obj.containsKey("currency_code")) {
                        esAliexpressProductListing.setCurrencyCode(obj.getString("currency_code"));
                    }
                    if (obj.containsKey("freight_template_id")) {
                        esAliexpressProductListing.setFreightTemplateId(obj.getLong("freight_template_id"));
                    }
                    if (obj.containsKey("add_unit")) {
                        esAliexpressProductListing.setAddUnit(obj.getInteger("add_unit"));
                    }
                    if (obj.containsKey("add_weight")) {
                        esAliexpressProductListing.setAddWeight(obj.getString("add_weight"));
                    }
                    if (obj.containsKey("base_unit")) {
                        esAliexpressProductListing.setBaseUnit(obj.getInteger("base_unit"));
                    }
                    if (obj.containsKey("bulk_order")) {
                        esAliexpressProductListing.setBulkOrder(obj.getInteger("bulk_order"));
                    }
                    if (obj.containsKey("group_id")) {
                        esAliexpressProductListing.setGroupId(obj.getLong("group_id"));
                    }
                    if (obj.containsKey("is_image_dynamic")) {
                        esAliexpressProductListing.setIsImageDynamic(obj.getBoolean("is_image_dynamic"));
                    }
                    if (obj.containsKey("product_price")) {
                        esAliexpressProductListing.setProductPrice(obj.getDouble("product_price"));
                    }
                    if (obj.containsKey("promise_template_id")) {
                        esAliexpressProductListing.setPromiseTemplateId(obj.getLong("promise_template_id"));
                    }
                    if (obj.containsKey("sizechart_id")) {
                        esAliexpressProductListing.setSizeChartId(obj.getLong("sizechart_id"));
                    }
                    if (obj.containsKey("coupon_start_date")) {
                        String couponStartDate = obj.getString("coupon_start_date");
                        Date parseDate = org.apache.commons.lang3.time.DateUtils
                                .parseDate(couponStartDate, "yyyy-MM-dd HH:mm:ss");
                        esAliexpressProductListing.setCouponStartDate(new Timestamp(parseDate.getTime()));
                    }
                    if (obj.containsKey("coupon_end_date")) {
                        String couponEndDate = obj.getString("coupon_end_date").substring(0, 14);
                        Date parseDate = org.apache.commons.lang3.time.DateUtils
                                .parseDate(couponEndDate, "yyyy-MM-dd HH:mm:ss");
                        esAliexpressProductListing.setCouponEndDate(new Timestamp(parseDate.getTime()));
                    }

                    //skuid对应的json
                    Map<String, JSONObject> skuIdMap = new HashMap<>();

                    //用新版的接口 获取营销图
                    try {
                        OfferQueryProductOpenCall call = new OfferQueryProductOpenCall();
                        String syncResult = call.getProduct(saleAccountByAccountNumber, productId);
                        if (StringUtils.isNotBlank(syncResult)) {
                            JSONObject objectNew = JSONObject.parseObject(syncResult);
                            if (objectNew.containsKey("aliexpress_offer_product_query_response")) {
                                JSONObject productRspNew = objectNew
                                        .getJSONObject("aliexpress_offer_product_query_response");
                                if (productRspNew.containsKey("result")) {
                                    JSONObject objNew = productRspNew.getJSONObject("result");

                                    if (objNew.containsKey("aeop_ae_product_s_k_us")) {
                                        JSONObject aeop_ae_product_s_k_us = objNew.getJSONObject("aeop_ae_product_s_k_us");
                                        if (aeop_ae_product_s_k_us.containsKey("aeop_ae_product_sku")) {
                                            JSONArray aeop_ae_product_sku = aeop_ae_product_s_k_us.getJSONArray("aeop_ae_product_sku");
                                            for (int i = 0; i < aeop_ae_product_sku.size(); i++) {
                                                JSONObject jsonObject = aeop_ae_product_sku.getJSONObject(i);
                                                String id = jsonObject.getString("id");
                                                if (StringUtils.isBlank(id)) {
                                                    id = "<none>";
                                                }
                                                skuIdMap.put(id, jsonObject);
                                            }
                                        }
                                    }

                                    try {
                                        List<Long> sizeChartIdList = new ArrayList<>();
                                        if (objNew.containsKey("sizechart_id_list")) {
                                            JSONObject sizechart_id_listObject = objNew.getJSONObject("sizechart_id_list");
                                            if (sizechart_id_listObject.containsKey("number")) {
                                                JSONArray numberList = sizechart_id_listObject.getJSONArray("number");
                                                for (int i = 0; i < numberList.size(); i++) {
                                                    long longValue = numberList.getLongValue(i);
                                                    sizeChartIdList.add(longValue);
                                                }
                                            }
                                        }
                                        if (CollectionUtils.isNotEmpty(sizeChartIdList)) {
                                            esAliexpressProductListing.setSizechartIdList(StringUtils.join(sizeChartIdList, ","));
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage(), e);
                                    }

                                    if (objNew.containsKey("market_images")) {
                                        JSONObject market_images = objNew.getJSONObject("market_images");
                                        JSONArray market_image = market_images.getJSONArray("market_image");
                                        for (int i = 0; i < market_image.size(); i++) {
                                            JSONObject jsonObject = market_image.getJSONObject(i);
                                            String url = jsonObject.getString("url");
                                            Integer image_type = jsonObject.getInteger("image_type");
                                            if (image_type.intValue() == 1) {
                                                aliexpressEsExtend.setLongImg(url);
                                            } else if (image_type.intValue() == 2) {
                                                aliexpressEsExtend.setSquareImg(url);
                                            }
                                        }
                                    }

                                    Map<String, String> map = new HashMap<>();
                                    JSONObject subject_list = objNew.getJSONObject("subject_list");
                                    JSONArray subject_listJSONArray = subject_list.getJSONArray("subject");
                                    for (int i = 0; i < subject_listJSONArray.size(); i++) {
                                        JSONObject jsonObject = subject_listJSONArray.getJSONObject(i);
                                        String locale = jsonObject.getString("locale");
                                        String value = jsonObject.getString("value");
                                        if (StringUtils.isNotBlank(value)) {
                                            String siteByLanguagesCode = TranslateCountryEnum.getSiteByLanguagesCode(locale);
                                            //英语存在外面了
                                            if (StringUtils.isNotBlank(siteByLanguagesCode)) {
                                                map.put(siteByLanguagesCode, value);
                                            }
                                        }
                                    }
                                    aliexpressEsExtend.setInterSubjects(JSON.toJSONString(map));

                                    //是否有资质 要求信息要全
                                    boolean isHasQualification = false;
                                    // 判断是否有json窜
                                    boolean isHasQualificationJson = false;

                                    QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
                                    ResponseJson qualifications = qualificationsOpenCall.qualifications(saleAccountByAccountNumber, esAliexpressProductListing.getCategoryId());
                                    //只有在同步类目资质成功的前提下还可以判断 产品是否包含对于的图片
                                    if (qualifications.isSuccess()) {
                                        JSONArray qualificationModule = (JSONArray) qualifications.getBody().get("key");
                                        //产品资质信息
                                        JSONArray productQualificationsValue = new JSONArray();
                                        if (objNew.containsKey("aeop_qualification_struct_list")) {
                                            JSONObject aeop_qualification_struct_list = objNew.getJSONObject("aeop_qualification_struct_list");
                                            if (aeop_qualification_struct_list != null && aeop_qualification_struct_list.containsKey("aeop_qualification_struct")) {
                                                productQualificationsValue = aeop_qualification_struct_list.getJSONArray("aeop_qualification_struct");
                                            }
                                        }
                                        Map<String, String> hasQualificationMap = new HashMap<>();
                                        productQualificationsValue.forEach(qualification -> {
                                            JSONObject qual = (JSONObject) qualification;
                                            hasQualificationMap.put(qual.getString("key"), qual.getString("value"));
                                        });

                                        esAliexpressProductListing.setIsHasPackageImg(null);
                                        esAliexpressProductListing.setIsHasStockImg(null);
                                        esAliexpressProductListing.setIsHasGPSRImg(null);

                                        //判断 是否包含资质图片
                                        for (int i = 0; i < qualificationModule.size(); i++) {
                                            JSONObject qualification = qualificationModule.getJSONObject(i);
                                            String key = qualification.getString("key");
                                            if (AliexpressQualificationUtils.PACKAGE_IMAGE_GPSR_KEY.equalsIgnoreCase(key)) {
                                                String s = hasQualificationMap.get(key);
                                                if (StringUtils.isBlank(s)) {
                                                    esAliexpressProductListing.setIsHasGPSRImg(false);
                                                } else {
                                                    esAliexpressProductListing.setIsHasGPSRImg(true);
                                                }
                                            } else if (AliexpressQualificationUtils.PRODUCT_STOCK_IMAGE_KEY.equalsIgnoreCase(key)) {
                                                String s = hasQualificationMap.get(key);
                                                if (StringUtils.isBlank(s)) {
                                                    esAliexpressProductListing.setIsHasStockImg(false);
                                                } else {
                                                    esAliexpressProductListing.setIsHasStockImg(true);
                                                }
                                            } else if (AliexpressQualificationUtils.PACKAGE_IMAGE_EU_KEY.equalsIgnoreCase(key) || AliexpressQualificationUtils.PACKAGE_IMAGE_KEY.equalsIgnoreCase(key)) {
                                                Boolean isHasPackageImg = esAliexpressProductListing.getIsHasPackageImg();
                                                //有一种有图片就算有
                                                if (isHasPackageImg == null || !isHasPackageImg) {
                                                    String s = hasQualificationMap.get(key);
                                                    if (StringUtils.isBlank(s)) {
                                                        esAliexpressProductListing.setIsHasPackageImg(false);
                                                    } else {
                                                        esAliexpressProductListing.setIsHasPackageImg(true);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (objNew.containsKey("aeop_qualification_struct_list")) {
                                        JSONObject aeop_qualification_struct_list = objNew.getJSONObject("aeop_qualification_struct_list");
                                        if (aeop_qualification_struct_list != null && aeop_qualification_struct_list.containsKey("aeop_qualification_struct")) {
                                            JSONArray aeop_qualification_struct = aeop_qualification_struct_list.getJSONArray("aeop_qualification_struct");
                                            if (aeop_qualification_struct != null) {
                                                isHasQualificationJson = true;
                                                if (qualifications.isSuccess()) {
                                                    JSONArray qualificationModule = (JSONArray) qualifications.getBody().get("key");
                                                    isHasQualification = AliexpressQualificationUtils.isHasQualification(aeop_qualification_struct, qualificationModule);
                                                }
                                                aliexpressEsExtend.setAeopQualificationStructJson(JSON.toJSONString(aeop_qualification_struct));
                                            }
                                        }
                                    }
                                    if (!isHasQualificationJson) {
                                        aliexpressEsExtend.setAeopQualificationStructJson("");
                                        aliexpressEsExtend.setIsHasQualification(false);
                                    }
                                    esAliexpressProductListing.setIsHasQualification(isHasQualification);

                                    if (objNew.containsKey("msr_eu_id")) {
                                        Long dbMsrEuId = null;
                                        String dbMsrEuIdType = "";
                                        if (CollectionUtils.isNotEmpty(dbEsProductList)) {
                                            dbMsrEuId = dbEsProductList.get(0).getMsrEuId();
                                            dbMsrEuIdType = dbEsProductList.get(0).getMsrEuIdType();
                                            if (StringUtils.isNotBlank(dbMsrEuIdType)) {
                                                esAliexpressProductListing.setMsrEuIdType(dbMsrEuIdType);
                                            }
                                        }
                                        Long msr_eu_id = objNew.getLong("msr_eu_id");
                                        //初始或者不一样的欧盟需要重新更新类别
                                        if (StringUtils.isBlank(dbMsrEuIdType) || dbMsrEuId == null || dbMsrEuId != msr_eu_id.longValue()) {
                                            EuResponsibleOpenCall euResponsibleOpenCall = new EuResponsibleOpenCall();
                                            ResponseJson euResponsible = euResponsibleOpenCall.euResponsible(saleAccountByAccountNumber, esAliexpressProductListing.getCategoryId(), false);
                                            if (!euResponsible.isSuccess()) {
                                                log.error("获取欧盟负责人异常:" + euResponsible.getMessage());
                                                esAliexpressProductListing.setMsrEuIdType("异常:" + euResponsible.getMessage());
                                            } else {
                                                esAliexpressProductListing.setMsrEuIdType("其他");//默认其他
                                                Object euResponsibleObject = euResponsible.getBody().get("key");
                                                if (euResponsibleObject != null) {
                                                    JSONArray eu_contact_module = JSON.parseArray(euResponsibleObject.toString());
                                                    for (int i = 0; i < eu_contact_module.size(); i++) {
                                                        JSONObject jsonObject = eu_contact_module.getJSONObject(i);
                                                        if (jsonObject != null && jsonObject.containsKey("msr_eu_id")) {
                                                            Long plat_msr_eu_id = jsonObject.getLong("msr_eu_id");
                                                            String name = jsonObject.getString("name");
                                                            if (msr_eu_id.longValue() == plat_msr_eu_id) {
                                                                String type = (StringUtils.equalsIgnoreCase("E-CrossStu GmbH", name) || StringUtils.equalsIgnoreCase("JUAN SERRANO GONZALEZ SOCIEDAD LIMITADA", name)) ? name : "其他";
                                                                esAliexpressProductListing.setMsrEuIdType(type);
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        esAliexpressProductListing.setMsrEuId(msr_eu_id);
                                    } else {
                                        esAliexpressProductListing.setMsrEuIdType("空");
                                    }

                                    if (objNew.containsKey("manufacturer_id")) {
                                        Long manufacturer_id = objNew.getLong("manufacturer_id");
                                        esAliexpressProductListing.setManufactureId(manufacturer_id);
                                    }

                                    if (objNew.containsKey("tax_type")) {
                                        String tax_type = objNew.getString("tax_type");
                                        esAliexpressProductListing.setTaxType(tax_type);
                                    }
                                    if (objNew.containsKey("hscode")) {
                                        String hscode = objNew.getString("hscode");
                                        esAliexpressProductListing.setHscode(hscode);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    //大字段
                    if (obj.containsKey("aeop_ae_product_propertys")) {
                        JSONObject prodPerpJson = obj.getJSONObject("aeop_ae_product_propertys");
                        if (prodPerpJson.containsKey("aeop_ae_product_property")) {
                            String aeopAeProductPropertys = prodPerpJson
                                    .getString("aeop_ae_product_property");
                            try {
                                aeopAeProductPropertys = aeopAeProductPropertys.replaceAll("\\\\\"", "&quot;");
                                aeopAeProductPropertys = StringEscapeUtils.unescapeJava(aeopAeProductPropertys);

                                aeopAeProductPropertys = aeopAeProductPropertys.replaceAll("\\\\r|\\\\n", "").replaceAll("\\\\", "");
                                // 解析品牌
                                List<ProductPropertys> propertyList = JSON.parseObject(aeopAeProductPropertys,
                                        new TypeReference<List<ProductPropertys>>() {
                                        });
                                propertyList.stream()
                                        .filter(productProperties -> Long.valueOf(2).equals(productProperties.getAttrNameId()))
                                        .findFirst().ifPresent(brandProperty -> {
                                            Long brandId = brandProperty.getAttrValueId();
                                            Integer categoryId = esAliexpressProductListing.getCategoryId();
                                            BrandDO brandDO = accountBrandService.getBrand(aliexpressAccountNumber, categoryId, brandId);
                                            if (brandDO != null) {
                                                esAliexpressProductListing.setBrand(brandDO.getNames());
                                            } else {
                                                String attrValue = brandProperty.getAttrValue();
                                                Map<String, String> branMap = new HashMap<>();
                                                branMap.put("en", attrValue);
                                                esAliexpressProductListing.setBrand(branMap);
                                            }
                                            esAliexpressProductListing.setBrandId(brandId);
                                        });
                            } catch (Exception e) {
                                //忽略异常
                                log.error(e.getMessage(), e);
                            }
                            aliexpressEsExtend.setAeopAeProductPropertysJson(aeopAeProductPropertys);

                            if (CollectionUtils.isNotEmpty(dbEsProductList)) {
                                try {
                                    // 应用了admin范本属性才需要再次判断属性全不全
                                    long count = dbEsProductList.stream().filter(a -> BooleanUtils.isTrue(a.getAdminToProductFlag())).count();
                                    if (count > 0 && aliexpressCategory != null) {
                                        // 还是要判断 aliexpressCategory 是否为null,如果为null 就不处理判断了
                                        ResponseJson responseJson = AliexpressContentUtils.checkAttr(aeopAeProductPropertys, aliexpressCategory, null);
                                        if (responseJson.isSuccess()) {
                                            esAliexpressProductListing.setAdminToProductFlag(true);
                                        } else {
                                            esAliexpressProductListing.setAdminToProductFlag(false);
                                        }
                                        esAliexpressProductListing.setAdminToProductTime(new Date());
                                    }
                                } catch (Exception e) {
                                    log.error("判断admin范本属性异常:", e);
                                }
                            }

                            List<Long> idList = ProvinceEnum.getIdList();
                            for (Long aLong : idList) {
                                String aa = ":" + aLong + ",";
                                if (StringUtils.containsIgnoreCase(aeopAeProductPropertys, aa)) {
                                    String nameById = ProvinceEnum.getNameById(aLong);
                                    esAliexpressProductListing.setCnProvince(nameById);
                                    break;
                                }
                            }
                        }
                    }
                    boolean isHasVideo = false;
                    if (obj.containsKey("aeop_a_e_multimedia")) {
                        aliexpressEsExtend.setAeopAeMultimedia(obj.getString("aeop_a_e_multimedia"));
                        JSONObject aeop_a_e_multimedia = obj.getJSONObject("aeop_a_e_multimedia");
                        if (aeop_a_e_multimedia.containsKey("aeop_a_e_videos")) {
                            JSONObject aeop_a_e_videos = aeop_a_e_multimedia.getJSONObject("aeop_a_e_videos");
                            if (aeop_a_e_videos.containsKey("aeop_ae_video")) {
                                JSONArray aeop_ae_video = aeop_a_e_videos.getJSONArray("aeop_ae_video");
                                for (int i = 0; i < aeop_ae_video.size(); i++) {
                                    JSONObject jsonObject = aeop_ae_video.getJSONObject(i);
                                    if (jsonObject.containsKey("media_id")) {
                                        isHasVideo = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    esAliexpressProductListing.setIsHasVideo(isHasVideo);
                    if (obj.containsKey("aeop_national_quote_configuration")) {
                        Object aeop_national_quote_configuration = obj
                                .get("aeop_national_quote_configuration");
                        aliexpressEsExtend.setAeopNationalQuoteConfiguration(
                                JSONObject.toJSONString(aeop_national_quote_configuration));
                    } else {
                        aliexpressEsExtend.setAeopNationalQuoteConfiguration("");
                    }

                    //是否区域调价
                    esAliexpressProductListing.setIsRegionPrice(StringUtils.isNotBlank(aliexpressEsExtend.getAeopNationalQuoteConfiguration()) ? true : false);

                    if (obj.containsKey("mobile_detail")) {
                        aliexpressEsExtend.setMobileDetail(obj.getString("mobile_detail"));
                    }
                    if (obj.containsKey("aeop_ae_product_s_k_us")) {
                        JSONObject skusJson = obj.getJSONObject("aeop_ae_product_s_k_us");
                        if (skusJson.containsKey("aeop_ae_product_sku")) {
                            JSONArray aeopAeProductSKUs = skusJson.getJSONArray("aeop_ae_product_sku");
                            aliexpressEsExtend.setAeopAeProductSkusJson(aeopAeProductSKUs.toJSONString());
                            String articleNumberPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();
                            List<EsAliexpressProductListing> createEsAliexpressProductList = new ArrayList<>();
                            List<EsAliexpressProductListing> updateEsProductList = new ArrayList<>();
                            Boolean isVariant = aeopAeProductSKUs.size() > 1 ? true : false;

                            //是否有图片
                            Boolean isHasImg = false;

                            // 变种属性
                            for (int i = 0; i < aeopAeProductSKUs.size(); i++) {
                                EsAliexpressProductListing synchProduct = new EsAliexpressProductListing();
                                BeanUtils.copyProperties(esAliexpressProductListing, synchProduct);

                                JSONObject sku = aeopAeProductSKUs.getJSONObject(i);
                                String skuId = sku.getString("id");
                                if (StringUtils.isBlank(skuId)) {
                                    skuId = "<none>";
                                }

                                JSONObject jsonObject = skuIdMap.get(skuId);
                                if (jsonObject != null) {
                                    synchProduct.setPlatSkuId(jsonObject.getString("sku_Id"));
                                }

                                synchSkuIds.add(skuId);
                                String skuCode = sku.getString("sku_code");
                                Integer skuStock = sku.getInteger("ipm_sku_stock");
                                Double skuPrice = sku.getDouble("sku_price");
                                Double skuDiscountPrice = sku.getDouble("sku_discount_price");
                                String barcode = sku.getString("barcode");
                                String nationalDiscountPriceList = sku
                                        .getString("aeop_s_k_u_national_discount_price_list");
                                String skuPropertyList = sku.getString("aeop_s_k_u_property_list");
                                synchProduct.setAeopSKUPropertyList(skuPropertyList);
                                synchProduct.setAeopSKUNationalDiscountPriceList(nationalDiscountPriceList);
                                JSONObject skuPropsJson = sku.getJSONObject("aeop_s_k_u_property_list");
                                String skuImage = null;
                                if (skuPropsJson != null && skuPropsJson.containsKey("aeop_sku_property")) {
                                    JSONArray skuPropArr = skuPropsJson.getJSONArray("aeop_sku_property");
                                    if (skuPropArr != null) {
                                        int skuPropAmount = skuPropArr.size();
                                        for (int j = 0; j < skuPropAmount; j++) {
                                            JSONObject skuPropObj = skuPropArr.getJSONObject(j);
                                            if (skuPropObj.containsKey("sku_image")) {
                                                skuImage = skuPropObj.getString("sku_image");
                                                break;
                                            }
                                        }
                                    }
                                }
                                String articleNumber = skuCode;

                                //发货地址，默认中国
                                String deliveryAddress = "CN";
                                if (StringUtils.isNotBlank(skuCode) && StringUtils.isNotBlank(articleNumberPrefix)) {
                                    articleNumber = AliexpressContentUtils.getArticleNumber(skuCode, articleNumberPrefix);
                                    if (StringUtils.indexOf(articleNumber, "[") != -1) {
                                        String[] split = StringUtils.split(articleNumber, "[");
                                        articleNumber = split[0];
                                        deliveryAddress = split[1].replace("]", "");
                                    }
                                }
                                synchProduct.setDeliveryAddress(deliveryAddress);
                                //是否海外仓产品
                                synchProduct.setIsOverseas(StringUtils.equalsIgnoreCase(deliveryAddress, "CN") ? false : true);

                                synchProduct.setArticleNumber(StringUtils.isBlank(articleNumber) ? "" : articleNumber.trim().toUpperCase());
                                synchProduct.setSkuId(skuId);
                                synchProduct.setSkuCode(skuCode);
                                synchProduct.setIpmSkuStock(skuStock);
                                synchProduct.setSkuPrice(skuPrice);
                                synchProduct.setSkuDiscountPrice(skuDiscountPrice);
                                synchProduct.setSkuDisplayImg(skuImage);
                                synchProduct.setBarcode(barcode);

                                isHasImg = StringUtils.isNotBlank(skuImage);

                                synchProduct.setLastSyncTime(new Timestamp(System.currentTimeMillis()));
                                synchProduct.setIsVariant(isVariant);
                                String existId = existProductIdMaP.get(productId + "_" + skuId);
                                // 更新已存在的product
                                if (StringUtils.isNotBlank(existId)) {
                                    synchProduct.setId(existId);
                                    updateEsProductList.add(synchProduct);
                                } else {
                                    //创建时间
                                    synchProduct.setCreateTime(new Date());
                                    createEsAliexpressProductList.add(synchProduct);
                                    //增量更新推redis TODO ES暂时屏蔽
                                        /*if(StringUtils.isNotBlank(synchProduct.getArticleNumber())){
                                            PublishRedisClusterUtils
                                                    .sAdd(RedisKeyConstant.PUBLISH_PRODUCT_STATISTICS, StrUtil.strTrimToUpperCase(synchProduct.getArticleNumber()));
                                        }*/
                                }
//                                  // 插入产品源库 TODO ES暂时屏蔽
//                                    AliexpressProductSourceService aliexpressProductSourceService = SpringUtils
//                                            .getBean(AliexpressProductSourceService.class);

//                                    try {
//                                        List<AliexpressProduct> productList = EsTranProductUtils
//                                                .tranProduct(Arrays.asList(synchProduct),
//                                                        Arrays.asList(aliexpressEsExtend));
//                                        aliexpressProductSourceService.syncAliexProductSource(productList.get(0));
//                                    }
//                                    catch (Exception e) {
//                                        log.error(e.getMessage(), e);
//                                    }
                            }

                            //多属性，但是无图片
                            boolean isMultiNoImg = isVariant && !isHasImg;

                            // 批量插入或更新产品信息
                            if (CollectionUtils.isNotEmpty(createEsAliexpressProductList)) {
                                //ES 新增才会有刊登角色
                                Integer publishRole = aliexpressProductLogService
                                        .getPublishRoleByProduct(productId);

                                for (EsAliexpressProductListing createEsProduct : createEsAliexpressProductList) {
                                    String id = aliexpressAccountNumber + "-" + productId + "-" + createEsProduct.getSkuId();
                                    createEsProduct.setId(id);
                                    createEsProduct.setIsMultiNoImg(isMultiNoImg);
                                    createEsProduct.setCreateTime(new Date());
                                    createEsProduct.setLastEditTime(new Date());
                                    createEsProduct.setPublishRole(publishRole);
                                    //新增默认在线状态
                                    createEsProduct.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                                    ProductInfoVO productInfoVO = null;
                                    //判断是否是大健云仓的产品

                                    if (isSpus) {
                                        createEsProduct.setDataSourceType(AliexpressListingDataSourceEnum.SPUS.getCode());
                                    } else if (this.getIsDaJianCloudWareHouse(createEsProduct.getArticleNumber())) {
                                        createEsProduct.setDataSourceType(AliexpressListingDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.getCode());
                                    } else {
                                        // 判断是否是试卖的， 看下是否绑定产品信息了
                                        List<EsSkuBind> skuBinds = AliexpressListingErpSkuUtils.getSkuBind(createEsProduct.getSkuCode());
                                        if (skuBinds.size() > 0) {
                                            createEsProduct.setDataSourceType(AliexpressListingDataSourceEnum.ERP_DATA_SYSTEM.getCode());
                                            esAliexpressProductListing.setDataSourceType(AliexpressListingDataSourceEnum.ERP_DATA_SYSTEM.getCode());
                                        }
                                    }
                                    EsAliexpressProductListingUtils.handleAliexpressProductinfo(createEsProduct, productInfoVO, false);
                                }
                                //新增数据 需要匹配 产品物流模板标签
                                Integer categoryId = createEsAliexpressProductList.get(0).getCategoryId();
                                //货号
                                List<String> articleNumberList = createEsAliexpressProductList.stream().map(t -> t.getArticleNumber()).collect(Collectors.toList());
                                String categoryLabel = null;
                                while (true) {
                                    //产品
                                    AliexpressProductCategoryLabelItemExample labelItemExample = new AliexpressProductCategoryLabelItemExample();
                                    labelItemExample.createCriteria().andProductIdEqualTo(productId);
                                    List<AliexpressProductCategoryLabelItem> aliexpressProductCategoryLabelItems = aliexpressProductCategoryLabelItemService.selectByExample(labelItemExample);
                                    if (CollectionUtils.isNotEmpty(aliexpressProductCategoryLabelItems)) {
                                        categoryLabel = LabelTypeEnum.online_cainiao.getType();
                                        break;
                                    }

                                    //sku
                                    AliexpressProductCategoryLabelSkuExample labelSkuExample = new AliexpressProductCategoryLabelSkuExample();
                                    labelSkuExample.createCriteria().andSkuIn(articleNumberList);
                                    List<AliexpressProductCategoryLabelSku> aliexpressProductCategoryLabelSkus = aliexpressProductCategoryLabelSkuService.selectByExample(labelSkuExample);
                                    if (CollectionUtils.isNotEmpty(aliexpressProductCategoryLabelSkus)) {
                                        categoryLabel = LabelTypeEnum.online_cainiao.getType();
                                        break;
                                    }

                                    //类目
                                    AliexpressProductCategoryLabelExample labelExample = new AliexpressProductCategoryLabelExample();
                                    labelExample.createCriteria().andCategoryIdEqualTo(Long.valueOf(categoryId));
                                    List<AliexpressProductCategoryLabel> aliexpressProductCategoryLabels = aliexpressProductCategoryLabelService.selectByExample(labelExample);
                                    if (CollectionUtils.isNotEmpty(aliexpressProductCategoryLabels)) {
                                        List<String> labelTypeList = aliexpressProductCategoryLabels.stream().map(t -> t.getLabelType()).collect(Collectors.toList());
                                        if (labelTypeList.contains(LabelTypeEnum.online_cainiao.getType())) {
                                            categoryLabel = LabelTypeEnum.online_cainiao.getType();
                                        } else {
                                            categoryLabel = aliexpressProductCategoryLabels.get(0).getLabelType();
                                        }
                                        break;
                                    }
                                    //最终需要跳出循环
                                    break;
                                }
                                if (StringUtils.isNotBlank(categoryLabel)) {
                                    for (EsAliexpressProductListing aliexpressProductListing : createEsAliexpressProductList) {
                                        aliexpressProductListing.setCategoryLabel(categoryLabel);
                                    }
                                }
                                esAliexpressProductListingService.saveAll(createEsAliexpressProductList);

                                //需要单独存储一份mysql 用于区域调价 解决es延迟刷新问题
                                List<AliexpressProductForAreaPrice> aliexpressProductForAreaPrices = EsTranProductUtils.tranAliexpressProductForAreaPrice(createEsAliexpressProductList);
                                for (AliexpressProductForAreaPrice productForAreaPrice : aliexpressProductForAreaPrices) {
                                    try {
                                        aliexpressProductForAreaPriceService.insert(productForAreaPrice);
                                    } catch (Exception e) {
                                        //忽略异常 有本地操作会删除产品
                                    }
                                }

                            }
                            if (CollectionUtils.isNotEmpty(updateEsProductList)) {
                                //需要全表查询出来 在修改
                                for (EsAliexpressProductListing updateEsProduct : updateEsProductList) {
                                    String skuId = updateEsProduct.getSkuId();
                                    updateEsProduct.setIsMultiNoImg(isMultiNoImg);
                                    //新增默认在线状态
                                    updateEsProduct.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                                    for (EsAliexpressProductListing dbEsProduct : dbEsProductList) {
                                        String exsitSkuId = dbEsProduct.getSkuId();
                                        if (StringUtils.equalsIgnoreCase(skuId, exsitSkuId)) {
                                            updateEsProduct.setLastEditTime(new Date());
                                            if (dbEsProduct.getDataSourceType() != null && dbEsProduct.getDataSourceType() == AliexpressListingDataSourceEnum.ERP_DATA_SYSTEM.getCode()) {
                                                List<EsSkuBind> skuBinds = AliexpressListingErpSkuUtils.getSkuBind(updateEsProduct.getSkuCode());
                                                if (skuBinds.size() > 0) {
                                                    EsSkuBind esSkuBind = skuBinds.get(0);
                                                    if (StringUtils.isNotBlank(esSkuBind.getSystemSku())) {
                                                        updateEsProduct.setArticleNumber(esSkuBind.getSystemSku());
                                                    }
                                                }
                                            }
                                            //是否还需要设置产品信息
                                            boolean isSetProductInfo = true;
                                            //TODO 当货号不一致的时候，重新设置货号为合并sku 状态也是合并sku状态 (重新查询接口)
                                            if (!StringUtils.equalsIgnoreCase(updateEsProduct.getArticleNumber(), dbEsProduct.getArticleNumber())) {
                                                updateEsProduct.setDataSourceType(dbEsProduct.getDataSourceType());
                                                //这里只是重新设置了产品信息，还需要设置除开产品信息和
                                                EsAliexpressProductListingUtils.handleAliexpressProductinfo(updateEsProduct, null, true);
                                                isSetProductInfo = false;
                                            }
                                            EsAliexpressProductListingUtils.setEsAliexpressProductExtends(updateEsProduct, dbEsProduct, isSetProductInfo);
                                            break;
                                        }
                                    }
                                }
                                esAliexpressProductListingService.saveAll(updateEsProductList);
                            }
                            //扩展信息表 需要重新判断下是否更新或者创建
                            try {
                                AliexpressEsExtend exsitAliexpressEsExtend = this
                                        .selectByAccountandProductId(aliexpressAccountNumber, productId);
                                if (exsitAliexpressEsExtend == null) {
                                    this.insert(aliexpressEsExtend);
                                } else {
                                    aliexpressEsExtend.setExtendId(exsitAliexpressEsExtend.getExtendId());
                                    this.updateByPrimaryKeySelective(aliexpressEsExtend);
                                }
                            } catch (Exception e) {
                                //数据库有唯一索引，担心同时插入失败
//                                    log.error(e.getMessage(), e);
                            }
                            //去除不存在的数据
                            if (CollectionUtils.isNotEmpty(exsitSkuIds)) {
                                exsitSkuIds.removeAll(synchSkuIds);
                                if (CollectionUtils.isNotEmpty(exsitSkuIds)) {
                                    exsitSkuIds.forEach(t -> {
                                        String id = skuIdToProductIdMap.get(t);
                                        esAliexpressProductListingService.deleteById(id);
                                    });
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            String errorInfo = "accountNumber：【" + aliexpressAccountNumber + "】 productId：【" + productId + "】"
                    + e.getMessage();
            log.error(e.getMessage(), e);
            result.append(errorInfo).append("<br>");
        }
        return result.toString();
    }

    private Boolean getIsDaJianCloudWareHouse(String articleNumber) {
        // 根据货号获取产品信息
        List<ProductInfo> productInfos = ProductUtils.findProductInfos(Collections.singletonList(articleNumber));

        if (CollectionUtils.isEmpty(productInfos)) {
            return false;
        }

        ProductInfo productInfo = productInfos.get(0);
        GtProductDetail gtDetail = productInfo.getOther();
        if (null == gtDetail || !GtProductUtils.judgeIsDaJianCloudWareHouse(gtDetail.getDataSource())) {
            return false;
        }
        return true;
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(200);

        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setQueryFields(new String[]{"id"});
        request.setArticleNumberStr(StringUtils.join(skuList, ","));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            request.setAliexpressAccountNumber(StringUtils.join(accountNumberList, ","));
        }

        List<EsAliexpressProductListing> res = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);

        if (CollectionUtils.isEmpty(res)) {
            return;
        }
        List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(skuList);
        if (CollectionUtils.isNotEmpty(singleItemEsList)) {
            for (SingleItemEs singleItemEs : singleItemEsList) {
                String sonSku = singleItemEs.getSonSku();
                if (StringUtils.isNotBlank(sonSku)) {
                    map.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                }
            }
        }

//        log.info("当前更新数量：{}，第一个id{}", res.size(), res.get(0).getId());
        long start1 = System.currentTimeMillis();
        res.forEach(t -> {
            EsAliexpressProductListing listing = esAliexpressProductListingService
                    .findAllById(t.getId());
            String articleNumber = listing.getArticleNumber();
            if (StringUtils.isNotBlank(articleNumber)) {
                try {
                    ProductInfoVO productInfoVO = map.get(listing.getArticleNumber());
                    if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                        return;
                    }
                    EsAliexpressProductListingUtils.handleAliexpressProductinfo(listing, productInfoVO, false);
                    esAliexpressProductListingService.save(listing);
                } catch (Exception e) {
                    log.error(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()), e);
                    throw new RuntimeException(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()));
                }
            } else {
                log.error(String.format("产品货号[%s]为空", t.getId()));
            }
        });
        long start2 = System.currentTimeMillis() - start1;
//        log.info("当前更新数量：{}，页耗时{}ms，第一个id{}", res.size(), start2, res.get(0).getId());
    }

    @Override
    public void timingSynchAccountForDelete(SaleAccountAndBusinessResponse aliexpressAccount, String toDate) {
        //5天前没有更新的产品 通过调用id 详情来删除
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType("onSelling,auditing");
        request.setQueryFields(new String[]{"id", "productId"});
        request.setToLastSynchDate(toDate);
        request.setAliexpressAccountNumber(aliexpressAccount.getAccountNumber());
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);

        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {

            List<List<EsAliexpressProductListing>> lists = PagingUtils.pagingList(esAliexpressProductListing, 1000);

            for (List<EsAliexpressProductListing> list : lists) {
                List<String> ids = new ArrayList<>();

                for (EsAliexpressProductListing esProduct : list) {
                    ids.add(esProduct.getId());
                }
                syncProductByIds(ids);
            }
        }

        esAliexpressProductListing.clear();
    }


    public void isSmtAuth(EsAliexpressProductListingRequest query) throws Exception {
        List<String> accountNumbers = CommonUtils.splitList(query.getAliexpressAccountNumber(), ",");
        List<String> managerIds = CommonUtils.splitList(query.getSalesSupervisorName(), ",");
        List<String> leaderIds = CommonUtils.splitList(query.getSalemanagerLeader(), ",");
        List<String> saleIds = CommonUtils.splitList(query.getSalemanager(), ",");
        List<Integer> groupIds = null;
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setAliexpressAccountNumber(StringUtils.join(authAccountNumbers, ","));
    }


    @Override
    public EsAliexpressProductListingResponse list(CQuery<EsAliexpressProductListingRequest> cquery) throws Exception {
        EsAliexpressProductListingRequest querySearch = cquery.getSearch();
        //查询账号权限
        isSmtAuth(querySearch);
        //账号和id
        String aliexpressAccountNumber = querySearch.getAliexpressAccountNumber();
        String productIdStr = querySearch.getProductIdStr();

        EsAliexpressProductListingResponse esResponse = new EsAliexpressProductListingResponse();

        //查询是否上下架
        Boolean isOffandOn = querySearch.getIsOffandOn();
        if (isOffandOn != null) {
            //查询配置为是的产品
            Timestamp timestamp = new Timestamp(System.currentTimeMillis()); //现在的时间
            AliexpressEsExtendExample extendExample = new AliexpressEsExtendExample();
            extendExample.setFields("product_id");
            //结束时间大于当前时间
            AliexpressEsExtendExample.Criteria criteria = extendExample.createCriteria();
            criteria.andEndTaskDateGreaterThanOrEqualTo(timestamp);
            if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
                criteria.andAliexpressAccountNumberIn(CommonUtils.splitList(aliexpressAccountNumber, ","));
            }
            if (StringUtils.isNotBlank(productIdStr)) {
                criteria.andProductIdIn(CommonUtils.splitLongList(productIdStr, ","));
            }
            List<AliexpressEsExtend> extendList = this.selectByExample(extendExample);
            List<Long> productIdList = extendList.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            if (isOffandOn) {
                if (CollectionUtils.isEmpty(productIdList)) {
                    return esResponse;
                }
                productIdStr = StringUtils.join(productIdList, ",");
                querySearch.setProductIdList(productIdList);
            } else {
                //查询配置为否的产品
                querySearch.setNotInProductIdList(productIdList);
            }
        }

        Boolean isNullMarketingImg = querySearch.getIsNullMarketingImg();
        if (isNullMarketingImg != null && isNullMarketingImg) {
            if (StringUtils.isBlank(aliexpressAccountNumber)) {
                throw new Exception("查询营销图必须要选择店铺！");
            }
            List<String> strings = CommonUtils.splitList(aliexpressAccountNumber, ",");
            if (CollectionUtils.isEmpty(strings) || strings.size() > 10) {
                throw new Exception("查询营销图必须要选择店铺,并且不能大于10个店铺！");
            }

            AliexpressEsExtendExample extendExample = new AliexpressEsExtendExample();
            extendExample.setFields("product_id");
            AliexpressEsExtendExample.Criteria criteria = extendExample.createCriteria();
            criteria.andAliexpressAccountNumberIn(strings);
            if (StringUtils.isNotBlank(productIdStr)) {
                criteria.andProductIdIn(CommonUtils.splitLongList(productIdStr, ","));
            }
            criteria.andIsNullMarketingImg();
            List<AliexpressEsExtend> extendList = this.selectByExample(extendExample);
            List<Long> productIdList = extendList.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productIdList)) {
                return esResponse;
            }
            querySearch.setProductIdList(productIdList);
        }

        //查询重复产品
        Boolean isSeleteRepeat = querySearch.getIsSeleteRepeat();
        if (isSeleteRepeat != null && isSeleteRepeat) {
            if (StringUtils.isBlank(aliexpressAccountNumber)) {
                throw new Exception("查询重复产品必须要选择店铺！");
            }
            List<String> strings = CommonUtils.splitList(aliexpressAccountNumber, ",");
            if (CollectionUtils.isEmpty(strings) || strings.size() > 5) {
                throw new Exception("查询重复产品必须要选择店铺,并且不能大于5个店铺！");
            }
            List<JSONObject> jsonObjects = esAliexpressProductListingService.repeatProductByAccountList(strings);
            if (CollectionUtils.isEmpty(jsonObjects)) {
                return esResponse;
            }
            String productStatusType = "onSelling,auditing";

            List<String> idList = new ArrayList<>();
            for (int i = 0; i < jsonObjects.size(); i++) {
                JSONObject jsonObject = jsonObjects.get(i);
                String account = jsonObject.getString("aliexpressAccountNumber");
                String articleNumber = jsonObject.getString("articleNumber");
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setAliexpressAccountNumber(account);
                request.setArticleNumber(articleNumber);
                request.setQueryFields(new String[]{"id"});
                request.setProductStatusType(productStatusType);
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                    idList.addAll(esAliexpressProductListing.stream().map(t -> t.getId()).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isEmpty(idList)) {
                return esResponse;
            } else {
                querySearch.setIdList(idList);
            }
        }

        //获取最子分类节点id
        Integer categoryId = querySearch.getCategoryId();
        if (categoryId != null) {
            List<Integer> allSubCategoryId = aliexpressCategoryService
                    .findAllSubCategoryId(categoryId.toString());
            querySearch.setCategoryIdList(allSubCategoryId);
            querySearch.setCategoryId(null);
        }

        //查询不存在资质的数据，需要带上类目条件
        if (querySearch.getIsHasQualification() != null && !querySearch.getIsHasQualification()) {
            AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
            AliexpressCategoryExample.Criteria aliexpressCategoryExampleCriteria = aliexpressCategoryExample.createCriteria();
            aliexpressCategoryExampleCriteria.andIsQualificationEqualTo(true);
            aliexpressCategoryExampleCriteria.andIsShowEqualTo(true);
            aliexpressCategoryExampleCriteria.andLeafCategoryEqualTo(true);
            aliexpressCategoryExample.setFields("category_id");
            List<Integer> needQualificationList = aliexpressCategoryService.selectByExample(aliexpressCategoryExample)
                    .stream()
                    .map(AliexpressCategory::getCategoryId)
                    .collect(Collectors.toList());

            //页面条件
            List<Integer> categoryIdList = querySearch.getCategoryIdList();
            if (CollectionUtils.isNotEmpty(categoryIdList)) {
                categoryIdList.retainAll(needQualificationList);
                if (CollectionUtils.isEmpty(categoryIdList)) {
                    return esResponse;
                } else {
                    querySearch.setCategoryIdList(categoryIdList);
                }
            } else {
                querySearch.setCategoryIdList(needQualificationList);
            }
        }
        Page<EsAliexpressProductListing> results = esAliexpressProductListingService
                .page(querySearch, cquery.getLimit(), cquery.getOffset());

        boolean isFilter = false;
        //数据需要过滤
        if ((StringUtils.isNotBlank(querySearch.getLeftMark()) && querySearch.getLeftValue() != null) || (
                StringUtils.isNotBlank(querySearch.getRightMark()) && querySearch.getRightValue() != null)) {
            isFilter = true;
        }

        //扩展信息，存储毛利毛利率，运费模板名称，产品分组 分类树形结构 销售，销售组长
        Map<String, AliexpressEsExtend> esExtendMap = new HashMap<>();
        if (null != results && results.getTotalElements() > 0) {
            //勾选物流 就需要计算毛利
            if (StringUtils.isNotBlank(querySearch.getShippingMethodCode())) {
                List<Integer> fields = querySearch.getFields();
                //下载产品信息时，勾选毛利和毛利率才进行计算
                if (CollectionUtils.isEmpty(fields)
                        || (fields.contains(DownEnum.gross_profit.getCode()) || fields.contains(DownEnum.gross_profit_rate.getCode()))) {
                    esExtendMap = shippingMethodCodeInfo(
                            results.getContent(), querySearch.getShippingMethodCode());
                }
            }
            if (isFilter) {
                if (null != results && results.getTotalElements() > 0) {
                    //过滤数据
                    List<EsAliexpressProductListing> esProductList = results.getContent();
                    List<EsAliexpressProductListing> filterList = filterData(esProductList,
                            querySearch, esExtendMap);

                    if (CollectionUtils.isEmpty(filterList)) {
                        return esResponse;
                    }
                    List<String> idList = filterList.stream().map(t -> t.getId())
                            .collect(Collectors.toList());

                    Iterator<EsAliexpressProductListing> iterator = results.iterator();
                    while (iterator.hasNext()) {
                        EsAliexpressProductListing productListing = iterator.next();
                        String id = productListing.getId();
                        if (!idList.contains(id)) {
                            iterator.remove();
                        }
                    }
                }
            }
        }

        if (null != results && results.getTotalElements() > 0) {
            //扩展运费模板名称 和 产品分组，分类树
            List<EsAliexpressProductListing> esProductList = results.getContent();
            esExtendMap = productInfo(esProductList,
                    querySearch.getIsSeleteProduct(), esExtendMap);
        }

        //TODO 在线状态转成页面可读
        if (null != results && results.getTotalElements() > 0) {
            for (EsAliexpressProductListing productListing : results) {
                if (Objects.isNull(productListing.getOnlineStatus())) {
                    continue;
                }
                productListing.setOnlineStatus(OnlineStatusEnum.getNameByCode(productListing.getOnlineStatus()));
            }
        }
        esResponse.setExtendMap(esExtendMap);
        esResponse.setEsProductListingPage(results);
        return esResponse;
    }

    /**
     * 在线列表产品扩展
     */
    @Override
    public Map<String, AliexpressEsExtend> productInfo(List<EsAliexpressProductListing> esProductList, boolean isSeleteProduct, Map<String, AliexpressEsExtend> esExtendMap) {
        log.warn("*******isSeleteProduct:" + isSeleteProduct);
        if (CollectionUtils.isEmpty(esProductList)) {
            return esExtendMap;
        }
        //关联类目信息
        List<Integer> categoryIdList = new ArrayList<>();
        esProductList.forEach((t) -> {
            categoryIdList.add(t.getCategoryId());
        });
        long start = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(categoryIdList)) {
            AliexpressCategoryCriteria criteria = new AliexpressCategoryCriteria();
            criteria.setCategoryIdList(categoryIdList);

            List<AliexpressCategory> aliexpressCategoryList = aliexpressCategoryService.searchCategoryTree(criteria.getExample());
            // 速卖通分类map
            Map<Integer, AliexpressCategory> categoryMap = new HashMap<>();

            aliexpressCategoryList.forEach((t) -> {
                categoryMap.put(t.getCategoryId(), t);
            });
            esProductList.forEach((t) -> {
                AliexpressEsExtend aliexpressEsExtend = esExtendMap.get(t.getId());
                if (aliexpressEsExtend == null) {
                    aliexpressEsExtend = new AliexpressEsExtend();
                    esExtendMap.put(t.getId(), aliexpressEsExtend);
                }
                Integer categoryId = t.getCategoryId();
                AliexpressCategory aliexpressCategory = categoryMap.get(categoryId);
                if (aliexpressCategory != null) {
                    aliexpressEsExtend.setAliexpressCategory(aliexpressCategory);
                }
            });
        }
        long end = System.currentTimeMillis();
        long l1 = (end - start) / 1000;
        if (l1 > 0) {
            log.warn(String.format("%s个产品分类信息需要%s秒", esProductList.size(), l1));
        }
        start = System.currentTimeMillis();

        // 获取查询到的产品列表中的所有的店铺账号集合和所有的sku集合
        List<String> accountNumberList = new ArrayList<>();
        List<String> skuList = new ArrayList<>();
        for (EsAliexpressProductListing esProduct : esProductList) {
            if (null != esProduct.getAliexpressAccountNumber()) {
                if (!accountNumberList.contains(esProduct.getAliexpressAccountNumber())) {
                    accountNumberList.add(esProduct.getAliexpressAccountNumber());
                }
            }
            if (null != esProduct.getArticleNumber()) {
                if (!skuList.contains(esProduct.getArticleNumber())) {
                    skuList.add(esProduct.getArticleNumber());
                }
            }
        }
        // 根据sku集合 存入productInfo
        Map<String, ProductInfo> skuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuList)) {
            if (isSeleteProduct) {
                List<List<String>> lists = PagingUtils.pagingList(skuList, 500);
                for (List<String> list : lists) {
                    ResponseJson json = ProductUtils.findSkuInfos(list);
                    if (json.isSuccess()) {
                        List<ProductInfo> infos = (List<ProductInfo>) json.getBody().get(ProductUtils.resultKey);
                        if (CollectionUtils.isNotEmpty(infos)) {
                            for (ProductInfo info : infos) {
                                skuMap.put(info.getSonSku().toUpperCase(), info);
                            }
                        }
                    } else {
                        log.error(json.getMessage());
                    }
                }
            }
        }
        end = System.currentTimeMillis();
        long l2 = (end - start) / 1000;
        if (l2 > 5) {
            log.warn(String.format("%s个产品调用getSingleItemEsList需要%s秒", esProductList.size(), l2));
        }

        if (l2 > 60) {
            log.warn(String.format("%s个产品调用getSingleItemEsList需要%s秒 货号%s", esProductList.size(), l2, StringUtils.join(skuList, ",")));
        }

        start = System.currentTimeMillis();
        // 套装货号
        Map<String, SuiteSku> suiteSkuMap = new HashMap<>();
        Map<String, SuiteSku> suiteSkuAllInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuList)) {
            if (isSeleteProduct) {
                List<List<String>> lists = PagingUtils.pagingList(skuList, 500);
                for (List<String> list : lists) {
                    ResponseJson responseJson = ProductUtils.getSuiteWeightAndCost(list);
                    if (responseJson.isSuccess()) {
                        List<SuiteSku> suiteSkuinfos = (List<SuiteSku>) responseJson.getBody().get(ProductUtils.resultKey);
                        if (CollectionUtils.isNotEmpty(suiteSkuinfos)) {
                            for (SuiteSku suiteSku : suiteSkuinfos) {
                                suiteSkuMap.put(suiteSku.getSuiteSku().toUpperCase(), suiteSku);
                            }
                        }
                    }
                }
            }
        }
        end = System.currentTimeMillis();
        long l21 = (end - start) / 1000;
        if (l21 > 5) {
            log.warn(String.format("%s个产品调用套装接口需要%s秒", esProductList.size(), l21));
        }
        start = System.currentTimeMillis();
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SMT);

        end = System.currentTimeMillis();
        long l3 = (end - start) / 1000;
        if (l3 > 5) {
            log.warn(String.format("%s个产品查询销售组长组员需要%s秒", esProductList.size(), l3));
        }
        start = System.currentTimeMillis();

        //运费模板存储
        Map<String, String> freightTemplateMap = new HashMap<>();

        //产品分组map
        Map<String, String> groupMap = new HashMap<>();

        //扩展表map
        Map<Long, AliexpressEsExtend> extendMap = new HashMap<>();

        // 把销售组长和销售加入到商品列表中
        for (EsAliexpressProductListing esProduct : esProductList) {
            AliexpressEsExtend aliexpressEsExtend = esExtendMap.get(esProduct.getId());
            if (aliexpressEsExtend == null) {
                aliexpressEsExtend = new AliexpressEsExtend();
                esExtendMap.put(esProduct.getId(), aliexpressEsExtend);
            }

            //加入扩展时间查询
            Long productId = esProduct.getProductId();
            long exStart = System.currentTimeMillis();
            long exEnd = System.currentTimeMillis();
            AliexpressEsExtend extend = extendMap.get(productId);
            if (extend == null) {
                extend = this
                        .selectByAccountandProductId(esProduct.getAliexpressAccountNumber(), productId);
                exEnd = System.currentTimeMillis();
                long l = (exEnd - exStart) / 1000;
                if (l > 2) {
                    log.warn(String.format("产品id%s查询扩展表耗时%s秒", productId, l));
                }
                extendMap.put(productId, extend);
            }

            boolean isOffandOn = false;
            if (extend != null) {
                Timestamp startTaskDate = extend.getStartTaskDate();
                Timestamp endTaskDate = extend.getEndTaskDate();

                aliexpressEsExtend.setAutoOnDate(extend.getAutoOnDate());
                aliexpressEsExtend.setAutoOffDate(extend.getAutoOffDate());
                aliexpressEsExtend.setStartTaskDate(startTaskDate);
                aliexpressEsExtend.setEndTaskDate(endTaskDate);
                aliexpressEsExtend.setRecordOffDate(extend.getRecordOffDate());
                aliexpressEsExtend.setRecordOnDate(extend.getRecordOnDate());
                if (startTaskDate != null && endTaskDate != null && (System.currentTimeMillis() <= endTaskDate.getTime())) {
                    isOffandOn = true;
                }
            }
            aliexpressEsExtend.setIsOffandOn(isOffandOn);

            Long freightTemplateId = esProduct.getFreightTemplateId() == null ? 0L : esProduct.getFreightTemplateId();
            String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();

            String key = aliexpressAccountNumber + "-" + freightTemplateId;
            String freightTemplateName = freightTemplateMap.get(key);
            if (StringUtils.isEmpty(freightTemplateName)) {
                exStart = System.currentTimeMillis();
                AliexpressFreightTemplateExample freightTemplateExample = new AliexpressFreightTemplateExample();
                freightTemplateExample.createCriteria().andAccountNumberEqualTo(aliexpressAccountNumber).andTemplateIdEqualTo(freightTemplateId);
                freightTemplateName = aliexpressFreightTemplateService
                        .selectByTemplateId(aliexpressAccountNumber, freightTemplateId);
                exEnd = System.currentTimeMillis();
                long l = (exEnd - exStart) / 1000;
                if (l > 2) {
                    log.warn(String.format("产品id%s查询运费模板耗时%s秒", productId, l));
                }
                freightTemplateMap.put(key, freightTemplateName);
            }
            aliexpressEsExtend.setFreightTemplateName(freightTemplateMap.get(key));

            String groupIds = esProduct.getGroupIds();

            List<Long> longs = CommonUtils.splitLongList(groupIds, ",");
            if (CollectionUtils.isNotEmpty(longs)) {
                String groupKey = aliexpressAccountNumber + "-" + groupIds;
                String groupName = groupMap.get(groupKey);
                if (StringUtils.isBlank(groupName)) {
                    exStart = System.currentTimeMillis();
                    groupName = aliexpressProductGroupService
                            .selectGroupName(aliexpressAccountNumber, groupIds);
                    exEnd = System.currentTimeMillis();
                    long l = (exEnd - exStart) / 1000;
                    if (l > 2) {
                        log.warn(String.format("产品id%s查询产品分组耗时%s秒", productId, l));
                    }
                    groupMap.put(groupKey, groupName);
                }
                aliexpressEsExtend.setGroupNames(groupMap.get(groupKey));
            }


            Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(esProduct.getAliexpressAccountNumber());
            aliexpressEsExtend.setSalemanager(saleSuperiorTriple.getLeft());
            aliexpressEsExtend.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
            aliexpressEsExtend.setSalesSupervisorName(saleSuperiorTriple.getRight());


            //产品货号
            String articleNumber = esProduct.getArticleNumber();
            exStart = System.currentTimeMillis();

            if (isSeleteProduct) {
                ProductInfo info = skuMap.get(articleNumber);
                if (info != null) {
                    //中文申报名称
                    aliexpressEsExtend.setDeclarecnname(info.getCnCustoms());

                    //sku总重量
                    Double maxWeight = AliexpressWeightUtils.getMaxWeight(info, null);
                    aliexpressEsExtend.setSkuTotalWeight(maxWeight);
                    //sku采购价 改为销售成本价
                    if (info.getSaleCost() != null) {
                        aliexpressEsExtend.setSkuPurchasePrice(info.getSaleCost().setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                    //包材重量
                    if (info.getPackingWeight() != null) {
                        aliexpressEsExtend.setPmWeight(info.getPackingWeight().doubleValue());
                    }
                    //包材价格 + 填充物价格
                    if (info.getPackingPrice() != null || info.getMatchPrice() != null) {
                        double packingPrice = info.getPackingPrice() == null ? BigDecimal.valueOf(0).doubleValue() : info.getPackingPrice().doubleValue();
                        double matchPrice = info.getMatchPrice() == null ? BigDecimal.valueOf(0).doubleValue() : info.getMatchPrice().doubleValue();
                        aliexpressEsExtend.setPmPrice(packingPrice + matchPrice);
                    }
                    //产品状态
                    aliexpressEsExtend.setLifeCyclePhase(SkuStatusEnum.buildName(info.getItemStatus()));
                    //禁售平台
                    aliexpressEsExtend.setForbiddenSaleChannel(StringUtils.join(info.getSaleForbiddenList(), ","));
                    //标签
                    aliexpressEsExtend.setTags(info.getTag());

                    //采购运费
                    Double shippingCost = info.getShippingCost();
                    aliexpressEsExtend.setShippingCost(shippingCost);
                    if (Objects.isNull(shippingCost)) {
                        log.warn("查询单品货号{}的采购运费为空", articleNumber);
                    }
                    //标准重量
                    aliexpressEsExtend.setStandardWeight(info.getStandardWeight());
                } else {
                    //查询是否是组合sku 优先组合 然后在套装
                    ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
                    if (composeProduct != null) {
                        Double maxWeight = AliexpressWeightUtils.getMaxWeight(composeProduct, null);
                        aliexpressEsExtend.setSkuTotalWeight(maxWeight);
                        if (composeProduct.getSaleCost() != null) {
                            aliexpressEsExtend.setSkuPurchasePrice(composeProduct.getSaleCost().doubleValue());
                        }
                        if (composeProduct.getComposeStatus() != null) {
                            aliexpressEsExtend.setLifeCyclePhase(SingleItemEnum.getNameByCode(composeProduct.getComposeStatus()));
                        }
                        aliexpressEsExtend.setTags(composeProduct.getTag());
                        if (CollectionUtils.isNotEmpty(composeProduct.getForbidChannels())) {
                            aliexpressEsExtend.setForbiddenSaleChannel(StringUtils.join(composeProduct.getForbidChannels(), ","));
                        }
                        //包材重量
                        aliexpressEsExtend.setPmWeight(4.5);//暂时取06#快递袋(220*330mm)重量

                        Double pmPrice = 0.1d; //暂时取
                        List<ComposeItem> composeItems = composeProduct.getComposeItems();
                        for (ComposeItem composeItem : composeItems) {
                            pmPrice += composeItem.getPurchaseprice() == null ? 0.0 : composeItem.getPurchaseprice();
                        }
                        aliexpressEsExtend.setPmPrice(pmPrice);//暂时取06#快递袋(220*330mm)价格 + 每个sku的搭配包材价格（多个数量算1个）
                    } else {
                        SuiteSku suiteSku = suiteSkuMap.get(articleNumber);
                        if (suiteSku != null) {
                            //  待定1 这里有两个重量设置 一个包材 一个全部 这里需要再次去查询
                            SuiteSku saleSuiteInfoBySonSku = suiteSkuAllInfoMap.get(suiteSku.getSuiteSku());
                            if (saleSuiteInfoBySonSku == null) {
                                saleSuiteInfoBySonSku = ProductUtils.getSaleSuiteInfoBySonSku(suiteSku.getSuiteSku());
                                suiteSkuAllInfoMap.put(suiteSku.getSuiteSku(), saleSuiteInfoBySonSku);
                            }
                            if (saleSuiteInfoBySonSku == null || AliexpressWeightUtils.notCode(saleSuiteInfoBySonSku.getTagCode())) {
                                aliexpressEsExtend.setSkuTotalWeight(suiteSku.getCalcSuitWeight().doubleValue() + 3);
                            } else {
                                aliexpressEsExtend.setSkuTotalWeight(AliexpressWeightUtils.getMaxWeight(suiteSku, null));
                            }
                            BigDecimal allSingleCost = suiteSku.getAllSingleCost();
                            if (allSingleCost != null) {
                                aliexpressEsExtend.setSkuPurchasePrice(allSingleCost.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue());
                            }
                            SuiteSkuItem maxPackWeightSuiteSkuItem = suiteSku.getMaxPackWeightSuiteSkuItem();
                            if (maxPackWeightSuiteSkuItem != null) {
                                double v = maxPackWeightSuiteSkuItem.getPackingWeight() == null ?
                                        0.0 :
                                        maxPackWeightSuiteSkuItem.getPackingWeight().doubleValue();

                                aliexpressEsExtend.setPmWeight(v);
                            }
                            SuiteSkuItem maxPackCostSuiteSkuItem = suiteSku.getMaxPackCostSuiteSkuItem();
                            if (maxPackCostSuiteSkuItem != null) {
                                double v = maxPackCostSuiteSkuItem.getPackingCost() == null ?
                                        0.0 :
                                        maxPackCostSuiteSkuItem.getPackingCost().doubleValue();

                                double v1 = maxPackCostSuiteSkuItem.getMatchCost() == null ?
                                        0.0 :
                                        maxPackCostSuiteSkuItem.getMatchCost().doubleValue();
                                aliexpressEsExtend.setPmPrice(v + v1);
                            }
                        }
                    }
                }
                exEnd = System.currentTimeMillis();
                long l = (exEnd - exStart) / 1000;
                if (l > 2) {
                    log.warn(String.format("产品id%s设置产品信息耗时%s秒", productId, l));
                }
            }

            //采购运费
//            SingleItemEs skuInfo = singleItemEsService.getSkuInfo(articleNumber);
//            if(!Objects.isNull(skuInfo) && !Objects.isNull(skuInfo.getSingleItemAllSupplier())){
//                Double shippingCost = skuInfo.getSingleItemAllSupplier().getShippingCost();
//                if(Objects.isNull(shippingCost)){
//                    log.warn("查询单品货号{}的采购运费为空",articleNumber);
//                }else{
//                    aliexpressEsExtend.setShippingCost(shippingCost);
//                }
//            }


            //单品折扣活动状态为未生效、生效中、已暂停
            singleDiscountOrProductHelper.extendDiscountInfo(aliexpressEsExtend, esProduct.getAliexpressAccountNumber(), productId);
        }
        end = System.currentTimeMillis();
        long l = (end - start) / 1000;
        if (l > 2) {
            log.warn(String.format("%s个产品设置产品信息,店铺销售组长组员信息,运费模板,产品分组需要%s秒", esProductList.size(), l));
        }
        return esExtendMap;
    }

    private Map<String, AliexpressEsExtend> shippingMethodCodeInfo(List<EsAliexpressProductListing> esAliexpressProductListing, String shippingMethodCode) {
        Map<String, AliexpressEsExtend> extendMap = new HashMap<>();
        if (CollectionUtils.isEmpty(esAliexpressProductListing) || StringUtils.isBlank(shippingMethodCode)) {
            return extendMap;
        }
        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        log.warn(String.format("一共%s个产品需要计算毛利", esAliexpressProductListing.size()));
        start = System.currentTimeMillis();
        //需要拆分请求
        List<List<EsAliexpressProductListing>> lists = PagingUtils.pagingList(esAliexpressProductListing, 100);
        List<Map<String, BatchPriceCalculatorResponse>> listMap = new ArrayList<>();
        List<Future<ResponseJson>> futureList = new ArrayList<>();
        try {
            for (List<EsAliexpressProductListing> list : lists) {

                List<AliexpressProduct> productList = new ArrayList<>();
                for (EsAliexpressProductListing esProduct : list) {
                    AliexpressProduct product = new AliexpressProduct();
                    BeanUtils.copyProperties(esProduct, product);
                    productList.add(product);
                }
                Future<ResponseJson> responseJsonFuture = AliexpressExecutors.calcProduct(rsp -> {
                    //计算毛利和毛利率 不需要传 grossProfitRate
                    ResponseJson responseJson = AliexpressCalcPriceUtil
                            .productCalc(productList, shippingMethodCode, null);
                    if (responseJson.isSuccess()) {

                        rsp.getBody().put(AliexpressCalcPriceUtil.key, responseJson
                                .getBody().get(AliexpressCalcPriceUtil.key));
                    } else {
                        log.warn("毛利计算出错：" + responseJson.getMessage());
                    }
                    rsp.setStatus(responseJson.getStatus());
                    rsp.setMessage(responseJson.getMessage());
                });

                futureList.add(responseJsonFuture);
            }
            for (Future<ResponseJson> responseJsonFuture : futureList) {
                ResponseJson responseJson = responseJsonFuture.get(3, TimeUnit.MINUTES);
                if (responseJson.isSuccess()) {
                    Map<String, BatchPriceCalculatorResponse> resultMap = (Map<String, BatchPriceCalculatorResponse>) responseJson.getBody().get(AliexpressCalcPriceUtil.key);
                    listMap.add(resultMap);
                }
            }

        } catch (Exception e) {
            log.error("毛利计算出错：" + e.getMessage());
            return extendMap;
        }
        if (listMap.isEmpty()) {
            return extendMap;
        }
        Map<String, BatchPriceCalculatorResponse> map = CommonUtils.mergeMaps(listMap);
        end = System.currentTimeMillis();
        log.warn(String.format("%s个产品计算毛利需要%s秒", esAliexpressProductListing.size(), (end - start) / 1000));
        if (null == map || map.isEmpty()) {
            log.warn("毛利计算结果返回空！");
            return extendMap;
        }
        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            String articleNumber = esProduct.getArticleNumber();
            Double skuPrice = esProduct.getSkuPrice();

            String id = esProduct.getId();
            AliexpressEsExtend aliexpressEsExtend = extendMap.get(id);
            if (aliexpressEsExtend == null) {
                aliexpressEsExtend = new AliexpressEsExtend();
                extendMap.put(id, aliexpressEsExtend);
            }
            BatchPriceCalculatorResponse response = map.get(articleNumber + "-" + skuPrice);
            if (response != null && response.getIsSuccess()) {
                //外币
                Double grossProfit = response.getForeignGrossProfit();
                if (grossProfit != null) {
                    BigDecimal bg = BigDecimal.valueOf(grossProfit).setScale(2, RoundingMode.HALF_UP);
                    aliexpressEsExtend.setGrossProfit(String.valueOf(bg.doubleValue()));
                }
                Double grossProfitRate = response.getGrossProfitRate();
                if (grossProfitRate != null) {
                    BigDecimal bg = BigDecimal.valueOf(grossProfitRate).setScale(2, RoundingMode.HALF_UP);
                    aliexpressEsExtend.setGrossProfitMargin(String.valueOf(bg.doubleValue()));
                }
            }
        }
        return extendMap;
    }

    private List<EsAliexpressProductListing> filterData(List<EsAliexpressProductListing> esProductList, EsAliexpressProductListingRequest query, Map<String, AliexpressEsExtend> esExtendMap) {
        //符合的数据
        List<EsAliexpressProductListing> filterList = new ArrayList<>();

        //符号判断  1.> , 2.>= 3. =
        String leftMark = query.getLeftMark();
        Integer left = left(leftMark);

        //符号判断  4.< , 5.<=
        String rightMark = query.getRightMark();
        Integer right = right(rightMark);
        Double leftValue = query.getLeftValue();
        Double rightValue = query.getRightValue();
        //值的判断 1.左右都有值  2.左边有值  3.右边有值
        int value = value(leftMark, rightMark);
        for (EsAliexpressProductListing esAliexpressProductListing : esProductList) {
            String id = esAliexpressProductListing.getId();
            AliexpressEsExtend aliexpressEsExtend = esExtendMap.get(id);
            if (aliexpressEsExtend == null) {
                continue;
            }
            //毛利率
            String grossProfitMargin = aliexpressEsExtend.getGrossProfitMargin();
            if (StringUtils.isBlank(grossProfitMargin)) {
                continue;
            }
            double aDouble = Double.valueOf(grossProfitMargin).doubleValue();
            //left right 必有
            if (value == 1) {
                if (left == 1 && right == 4) {
                    if (aDouble > leftValue && aDouble < rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else if (left == 1 && right == 5) {
                    if (aDouble > leftValue && aDouble <= rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else if (left == 2 && right == 4) {
                    if (aDouble >= leftValue && aDouble < rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else if (left == 2 && right == 5) {
                    if (aDouble >= leftValue && aDouble <= rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else if (left == 3) {
                    if (aDouble == leftValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                }

            } else if (value == 2) {  //左边有值
                if (left == 1) {
                    if (aDouble > leftValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else if (left == 2) {
                    if (aDouble >= leftValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else {
                    if (aDouble == leftValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                }
            } else if (value == 3) { //右边有值

                if (right == 4) {
                    if (aDouble < rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                } else {
                    if (aDouble <= rightValue) {
                        filterList.add(esAliexpressProductListing);
                    }
                }
            }
        }
        return filterList;
    }

    public static Integer left(String leftMark) {
        Integer left;
        if (StringUtils.isBlank(leftMark)) {
            return null;
        }
        if (StringUtils.equalsIgnoreCase(leftMark, ">")) {
            left = 1; // >
        } else if (StringUtils.equalsIgnoreCase(leftMark, ">=")) {
            left = 2; // >=
        } else {
            left = 3; // =
        }
        return left;
    }

    public static Integer right(String rightMark) {
        Integer right;
        if (StringUtils.isBlank(rightMark)) {
            return null;
        }
        if (StringUtils.equalsIgnoreCase(rightMark, "<")) {
            right = 4; //<
        } else {
            right = 5; // <=
        }
        return right;
    }

    public static int value(String leftMark, String rightMark) {
        int value;
        if (StringUtils.isNotBlank(leftMark) && StringUtils.isNotBlank(rightMark)) {
            value = 1;
        } else if (StringUtils.isNotBlank(leftMark) && StringUtils.isBlank(rightMark)) {
            value = 2;
        } else {
            value = 3;
        }
        return value;
    }

    @Override
    public List<Long> repeatSkuCode(List<Long> productIdList) {
        EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
        checkReqest.setProductIdStr(StringUtils.join(productIdList, ","));
        checkReqest.setQueryFields(new String[]{"productId", "skuCode"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(checkReqest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return null;
        }
        List<Long> repeatSkuCodeList = new ArrayList<>();
        Map<String, List<EsAliexpressProductListing>> checkEditPriceMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(eapl -> eapl.getProductId() + "-" + eapl.getSkuCode()));
        for (Map.Entry<String, List<EsAliexpressProductListing>> stringListEntry : checkEditPriceMap
                .entrySet()) {
            List<EsAliexpressProductListing> value = stringListEntry.getValue();
            if (value.size() > 1) {
                repeatSkuCodeList.add(value.get(0).getProductId());
            }
        }
        return repeatSkuCodeList;
    }

    /**
     * @param ids                es的主键id
     * @param expectedMargin     预期毛利
     * @param sfmCode            物流方式(预留)
     * @param customCurrencyRate 汇率
     * @param adjustmentRange    调价幅度
     * @param expectPriceVal     期望价格（直接调价)
     * @return
     */
    @Override
    public String batchEditProductPriceNew(List<String> ids, Double expectedMargin, Double adjustmentRange,
                                           String sfmCode, String customCurrencyRate, Double expectPriceVal) {
        if (CollectionUtils.isEmpty(ids)) {
            return "ids不能为空！";
        }
        // 错误信息
        List<String> errorMsgList = new ArrayList<>();
        // 最低价格
        Double lowestSkuPrice = 0.01;

        //减少查询信息
        EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
        checkReqest.setIdStr(StringUtils.join(ids, ","));
        checkReqest.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
        checkReqest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(checkReqest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            log.warn("修改价格 查询不到需要修改的产品！");
            return "非在线、上架状态数据不可修改产品价格！";
        }

        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? "admin" : WebUtils.getUserName();

        Map<String, AliexpressProductLog> logMap = new HashMap<>();
        //记录处理报告
        for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setProductId(aliexpressProductListing.getProductId());
            productLog.setPriceBeforeEdit(aliexpressProductListing.getSkuPrice());
            productLog.setAccountNumber(aliexpressProductListing.getAliexpressAccountNumber());
            productLog.setSkuCode(aliexpressProductListing.getSkuCode());
            productLog.setOperator(currentUser);
            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            productLog.setOperateType(AliexpressProductOperateLogType.EDIT_PRICE);
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            aliexpressProductLogService.insert(productLog);
            logMap.put(aliexpressProductListing.getId(), productLog);
        }

        List<Long> productIdList = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getProductId).collect(Collectors.toList());

        //存在重复的商品编码
        List<Long> repeatSkuCodeList = this
                .repeatSkuCode(productIdList);
        if (CollectionUtils.isNotEmpty(repeatSkuCodeList)) {
            List<EsAliexpressProductListing> filterList = new ArrayList<>();
            for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
                Long productId = esProduct.getProductId();
                if (!repeatSkuCodeList.contains(productId)) {
                    filterList.add(esProduct);
                }
            }
            esAliexpressProductListing = filterList;
        }
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            for (Map.Entry<String, AliexpressProductLog> stringAliexpressProductLogEntry : logMap.entrySet()) {
                AliexpressProductLog value = stringAliexpressProductLogEntry.getValue();
                value.setResult(false);
                value.setFailInfo("查询不到需要修改的产品,请检查修改的产品是否存在重复的sku_id！");
                value.setOperateStatus(OperateLogStatusEnum.end.intCode());
                value.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(value);
            }
            return "查询不到需要修改的产品,请检查修改的产品是否存在重复的sku_id！";
        }
        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        //是否需要计算毛利
        boolean ifCalcPrice = false;
        if (adjustmentRange == null && expectedMargin != null) {
            ifCalcPrice = true;
        }
        // 执行修改smt 接口 productid ==> List<EsAliexpressProductListing>
        Map<Long, List<EsAliexpressProductListing>> updateMap = new HashMap<>();

        Set<String> skuSet = new HashSet<>();
        //算价会用到
        List<AliexpressProduct> productList = new ArrayList<>();

        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            if (ifCalcPrice) {
                AliexpressProduct product = new AliexpressProduct();
                BeanUtils.copyProperties(esProduct, product);
                productList.add(product);
            }
            Long productId = esProduct.getProductId();
            List<EsAliexpressProductListing> esProductList = updateMap.get(productId);

            if (CollectionUtils.isEmpty(esProductList)) {
                esProductList = new ArrayList<>();
                updateMap.put(productId, esProductList);
            }
            esProductList.add(esProduct);

            String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
            if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
                if (!accountMap.containsKey(aliexpressAccountNumber)) {
                    SaleAccountAndBusinessResponse account = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    accountMap.put(aliexpressAccountNumber, account);
                }
            }
            String articleNumber = esProduct.getArticleNumber();
            skuSet.add(articleNumber);
        }
        long start = System.currentTimeMillis();
        Map<String, BatchPriceCalculatorResponse> resultMap = new HashMap<>();

        //计算毛利
        if (ifCalcPrice) {

            ResponseJson responseJson = AliexpressCalcPriceUtil
                    .productCalc(productList, sfmCode, expectedMargin, customCurrencyRate);
            if (!responseJson.isSuccess()) {
                errorMsgList.add(responseJson.getMessage());

                for (Map.Entry<String, AliexpressProductLog> stringAliexpressProductLogEntry : logMap.entrySet()) {
                    AliexpressProductLog value = stringAliexpressProductLogEntry.getValue();
                    value.setResult(false);
                    value.setFailInfo(responseJson.getMessage());
                    value.setOperateStatus(OperateLogStatusEnum.end.intCode());
                    value.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    aliexpressProductLogService.updateByPrimaryKeySelective(value);
                }

                //毛利计算失败直接返回，不执行
                return StringUtils.join(errorMsgList, "<br>");
            }
            resultMap = (Map<String, BatchPriceCalculatorResponse>) responseJson.getBody().get(AliexpressCalcPriceUtil.key);

            long end = System.currentTimeMillis();

            log.warn(String.format("计算%s个SKU需要%s秒", skuSet.size(), (end - start) / 1000));
        }

        Map<String, Map<String, BatchPriceCalculatorResponse>> caleMap = new HashMap<>();
        caleMap.put("key", resultMap);

//        List<AliexpressProductLog> logList = new ArrayList<>();


        // 线程返回结果
//        Map<Long, Future<ResponseJson>> resultFutureMap = new HashMap<>();

        // 收集平台调价成功的产品 方便修改本地库
//        List<AliexpressProduct> updateDbAliexpressProducts = new ArrayList<>();

        for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : updateMap.entrySet()) {
            Long k = longListEntry.getKey();
            List<EsAliexpressProductListing> v = longListEntry.getValue();
            AliexpressExecutors.submitItemPriceUpdate(responseJson -> {
                //skuId -> 价格
                Map<String, String> skuIdMap = new HashMap<>();
                Map<String, Double> idPriceMap = new HashMap<>();
                List<EsAliexpressProductListing> requestProductList = new ArrayList<>();
                for (EsAliexpressProductListing esProduct : v) {
                    //错误信息
                    String errorMessage = "";
                    String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                    Long productId = esProduct.getProductId();
                    String articleNumber = esProduct.getArticleNumber();
                    String skuCode = esProduct.getSkuCode();
                    String skuId = esProduct.getSkuId();
                    String newPriceStr = "";
                    Double newPrice = null;
                    Double oldPrice = esProduct.getSkuPrice();

                    //获取处理报告
                    AliexpressProductLog productLog = logMap.get(esProduct.getId());
                    productLog.setPriceAfterEdit(newPrice);
                    productLog.setProductId(productId);
                    productLog.setPriceBeforeEdit(oldPrice);
                    productLog.setAccountNumber(aliexpressAccountNumber);
                    productLog.setSkuCode(skuCode);
                    productLog.setOperator(currentUser);
                    productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    productLog.setOperateType(AliexpressProductOperateLogType.EDIT_PRICE);
                    productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());

                    try {
                        if (adjustmentRange != null && oldPrice != null) {
                            // 调价幅度
                            newPrice = BigDecimal.valueOf(oldPrice).add(BigDecimal.valueOf(adjustmentRange)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            newPriceStr = String.valueOf(newPrice);
                        } else if (expectedMargin != null && StringUtils.isNotBlank(articleNumber)) {
                            Map<String, BatchPriceCalculatorResponse> map = caleMap.get("key");
                            BatchPriceCalculatorResponse batchPriceCalculatorResponse = map
                                    .get(articleNumber);
                            if (batchPriceCalculatorResponse == null) {
                                batchPriceCalculatorResponse = map
                                        .get(articleNumber.toUpperCase());
                            }
                            if (batchPriceCalculatorResponse == null) {
                                errorMessage = String
                                        .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, "没结果返回");
                                productLog.setFailInfo(errorMessage);
                                productLog.setResult(false);
                                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                                continue;
                            }
                            if (batchPriceCalculatorResponse.getIsSuccess()) {
                                BigDecimal bg = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                                newPrice = bg.doubleValue();
                                newPriceStr = String.valueOf(newPrice);
                            } else {
                                errorMessage = String
                                        .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, batchPriceCalculatorResponse.getErrorMsg());
                                productLog.setFailInfo(errorMessage);
                                productLog.setResult(false);
                                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                                continue;
                            }
                        } else if (expectPriceVal != null) {
                            newPrice = expectPriceVal;
                            newPriceStr = String.valueOf(newPrice);
                        }

                        if (newPrice == null) {
                            errorMessage = String
                                    .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, "修改失败 获取不到该SKU的新价格");
                            productLog.setFailInfo(errorMessage);
                            productLog.setResult(false);
                            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                            continue;
                        }

                        // 最低价格限制
                        if (newPrice < lowestSkuPrice) {
                            errorMessage = String
                                    .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, "不得低于最低价格：" + lowestSkuPrice);
                            productLog.setFailInfo(errorMessage);
                            productLog.setResult(false);
                            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                            continue;
                        }
                        skuIdMap.put(skuId, newPriceStr);
                        idPriceMap.put(esProduct.getId(), newPrice);

                        //请求的产品
                        requestProductList.add(esProduct);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        errorMessage = e.getMessage();
                        productLog.setFailInfo(errorMessage);
                        productLog.setResult(false);
                        aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                        continue;
                    } finally {
                        if (StringUtils.isNotBlank(errorMessage)) {
                            errorMsgList.add(errorMessage);
                        }
                    }

                }//end for

                //组装一个产品的数据 请求
                if (CollectionUtils.isNotEmpty(requestProductList)) {
                    EsAliexpressProductListing esProduct = requestProductList.get(0);
                    String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                    SaleAccountAndBusinessResponse account = accountMap.get(aliexpressAccountNumber);
                    Long productId = esProduct.getProductId();

                    EditMultipleSkuPriceOpenCall priceCall = new EditMultipleSkuPriceOpenCall();
                    ResponseJson rsp = priceCall
                            .editMultipleSkuPrice(account, productId.toString(), JSON.toJSONString(skuIdMap));
                    boolean result = rsp.isSuccess();
                    for (EsAliexpressProductListing reEsProduct : requestProductList) {
                        //获取处理报告
                        AliexpressProductLog log = logMap.get(reEsProduct.getId());

                        Double newPrice = idPriceMap.get(reEsProduct.getId());

                        log.setResult(result);
                        log.setFailInfo(rsp.getMessage());
                        log.setPriceAfterEdit(newPrice);
                        log.setProductId(productId);
                        log.setPriceBeforeEdit(reEsProduct.getSkuPrice());
                        log.setAccountNumber(aliexpressAccountNumber);
                        log.setSkuCode(reEsProduct.getArticleNumber());
                        log.setOperator(currentUser);
                        log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                        log.setOperateType(AliexpressProductOperateLogType.EDIT_PRICE);
                        aliexpressProductLogService.updateByPrimaryKeySelective(log);
                        if (!result) {
                            String errorMessage = String
                                    .format("产品id:%s, 货号:%s, %s", productId, reEsProduct.getArticleNumber(), rsp.getMessage());
                            errorMsgList.add(errorMessage);
                        } else {
                            try {
                                String id = reEsProduct.getId();
                                EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                updateItem.setId(id);
                                updateItem.setSkuPrice(newPrice);
                                smtItemEsBulkProcessor.updateSkuPrice(updateItem);

//                                EsAliexpressProductListing fullEsProduct = esAliexpressProductListingService.findAllById(id);
//                                if(null != fullEsProduct && fullEsProduct.getSkuPrice() != null){
//                                    if(fullEsProduct.getSkuPrice().doubleValue() != newPrice){
//                                        fullEsProduct.setSkuPrice(newPrice);
//                                        esAliexpressProductListingService.save(fullEsProduct);
//                                    }
//                                }
                            } catch (Exception e) {
                                //
                            }
                        }
                    }
                }
            });

//            resultFutureMap.put(k, responseJsonFuture);
        }//end for

//        resultFutureMap.forEach((k, v) ->{
//            try {
//                v.get(1, TimeUnit.MINUTES);
//            }
//            catch (Exception e) {
//                errorMsgList.add(e.getMessage());
//            }
//        });
        // 批量记录日志
//        if (CollectionUtils.isNotEmpty(logList)) {
//            aliexpressProductLogService.batchInsert(logList);
//        }
//        if (CollectionUtils.isNotEmpty(errorMsgList)) {
//            return StringUtils.join(errorMsgList, "<br>");
//        }
        return null;
    }

    @Override
    public String batchEditProductStock(List<String> ids, String stock, boolean isTimingUpdateStock, boolean isCheck, boolean halfLableCheck, HolidayStockMqBean holidayStockMqBean, boolean isQueue) {
        if (CollectionUtils.isEmpty(ids)) {
            return "ids不能为空！";
        }
        return excuteUpdateStock(ids, stock, WebUtils.getUserName(), isTimingUpdateStock, isCheck, halfLableCheck, holidayStockMqBean, isQueue);
    }

    public String excuteUpdateStock(List<String> ids, String stock, String operator, boolean isTimingUpdateStock, boolean isCheck, boolean halfLableCheck, HolidayStockMqBean holidayStockMqBean, boolean isQueue) {
        if (CollectionUtils.isEmpty(ids)) {
            return "ids不能为空！";
        }

        if(StringUtils.isEmpty(operator)){
            operator = "admin";
        }

        String currentUser = operator;

        //减少查询信息
        EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
        checkReqest.setIdStr(StringUtils.join(ids, ","));
        checkReqest.setQueryFields(new String[]{"aliexpressAccountNumber", "productId", "articleNumber", "skuId", "skuStatus", "forbidChannel", "ipmSkuStock"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(checkReqest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return null;
        }

        List<String> skuStatusList = Arrays.asList(SingleItemEnum.CLEARANCE.getEnName(), SingleItemEnum.REDUCTION.getEnName());

        Map<Long, List<EsAliexpressProductListing>> productMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
        for (Map.Entry<Long, List<EsAliexpressProductListing>> entry : productMap.entrySet()) {
            Long productId = entry.getKey();
            List<EsAliexpressProductListing> esProductList = entry.getValue();
            String aliexpressAccountNumber = esProductList.get(0).getAliexpressAccountNumber();
            if (StringUtils.isBlank(aliexpressAccountNumber)) {
                continue;
            }

            //货号对应的库存
            Map<String, Double> productSkuIdToStockMap = new HashMap<>();

            JSONObject skuStocks = new JSONObject();
            List<AliexpressProduct> productList = new ArrayList<>();
            // 拼接skuId:stock的json
            for (Iterator<EsAliexpressProductListing> iterator = esProductList.iterator(); iterator.hasNext(); ) {
                EsAliexpressProductListing esProduct = iterator.next();
                //检查数据 若选中的产品单品状态为清仓，甩卖，且库存+在途-待发＞0，且SKU不存在SMT禁售站点，
                boolean isHit = false;
                Integer redisStock = null;
                if (isCheck && StringUtils.equalsIgnoreCase(stock, "0")) {
                    String skuStatus = esProduct.getSkuStatus();
                    String articleNumber = esProduct.getArticleNumber();
                    String forbidChannel = esProduct.getForbidChannel();
                    //包含清仓 甩卖
                    if (skuStatusList.contains(skuStatus)) {
                        //只有true false
                        Boolean hasForbiddenSales = StringUtils.isNotBlank(forbidChannel) && StringUtils.contains(forbidChannel, "," + SaleChannel.CHANNEL_SMT + ",");
                        Integer skuSystemStock = SkuStockUtils.getSkuSystemStock(articleNumber);
                        if ((!hasForbiddenSales && skuSystemStock == null) || (!hasForbiddenSales && skuSystemStock != null && skuSystemStock.intValue() > 0)) {
                            isHit = true;
                            redisStock = skuSystemStock;
                        }
                    }
                }

                //不修改 只记录处理报告
                if (isHit && redisStock == null) {
                    AliexpressProductLog log = new AliexpressProductLog();
                    log.setProductId(esProduct.getProductId());
                    log.setAccountNumber(esProduct.getAliexpressAccountNumber());
                    log.setSkuCode(esProduct.getArticleNumber());
                    log.setOperator(operator);
                    log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    log.setOperateType(AliexpressProductOperateLogType.EDIT_STOCK);
                    log.setStockBeforeEdit(esProduct.getIpmSkuStock().doubleValue());
                    log.setStockAfterEdit(Double.valueOf(stock));
                    log.setFailInfo(String.format("sku[%s]单品状态清仓甩卖，且SKU在SMT不禁售，库存+在途-待发 redis查询不到！", esProduct.getArticleNumber()));
                    log.setResult(false);
                    aliexpressProductLogService.insert(log);
                } else if (isHit && redisStock != null) {
                    //修改库存为 redis可用库存 并备注
                    AliexpressProduct product = new AliexpressProduct();
                    BeanUtils.copyProperties(esProduct, product);
                    productList.add(product);
                    String skuId = esProduct.getSkuId();
                    skuStocks.put(skuId, redisStock.toString());

                    productSkuIdToStockMap.put(skuId, Double.valueOf(redisStock));
                } else {
                    AliexpressProduct product = new AliexpressProduct();
                    BeanUtils.copyProperties(esProduct, product);
                    productList.add(product);
                    String skuId = esProduct.getSkuId();
                    skuStocks.put(skuId, stock);
                    productSkuIdToStockMap.put(skuId, Double.valueOf(stock));
                }
            }

            if (skuStocks.isEmpty()) {
                continue;
            }

            if (isTimingUpdateStock) {
                if (isQueue) {
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    if (saleAccountByAccountNumber != null) {
                        long begin = System.currentTimeMillis();
                        EditMutilpleSkuStocksOpenCall call = new EditMutilpleSkuStocksOpenCall();
                        ResponseJson rsp = call
                                .editMutilpleSkuStocksNew(saleAccountByAccountNumber, String.valueOf(productId),
                                        skuStocks.toJSONString(), halfLableCheck);

                        long end = System.currentTimeMillis();
                        long l = (end - begin) / 1000L;
                        if (l > 2) {
                            log.warn("修改库存耗时:" + l);
                        }

                        if (rsp.isSuccess()) {
                            begin = System.currentTimeMillis();
                            for (EsAliexpressProductListing aliexpressProductListing : esProductList) {
                                try {
                                    EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                    updateItem.setId(aliexpressProductListing.getId());
                                    updateItem.setIpmSkuStock(Integer.valueOf(stock));
                                    smtItemEsBulkProcessor.updateIpmSkuStock(updateItem);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                            }
                            end = System.currentTimeMillis();
                            l = (end - begin) / 1000L;
                            if (l > 2) {
                                log.warn("修改库存回写Es耗时:" + l);
                            }
                        }
                        //记录日志
                        aliexpressProductLogService.updateStockCreate(productList, "admin", productSkuIdToStockMap, rsp, stock, isCheck, holidayStockMqBean);
                    }
                }else{
                    AliexpressExecutors.timingUpdateStock(responseJson -> {
                        responseJson.setStatus(StatusCode.FAIL);
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        if (saleAccountByAccountNumber != null) {
                            long begin = System.currentTimeMillis();
                            EditMutilpleSkuStocksOpenCall call = new EditMutilpleSkuStocksOpenCall();
                            ResponseJson rsp = call
                                    .editMutilpleSkuStocksNew(saleAccountByAccountNumber, String.valueOf(productId),
                                            skuStocks.toJSONString(), halfLableCheck);

                            long end = System.currentTimeMillis();
                            long l = (end - begin) / 1000L;
                            if (l > 2) {
                                log.warn("修改库存耗时:" + l);
                            }

                            if (rsp.isSuccess()) {
                                begin = System.currentTimeMillis();
                                for (EsAliexpressProductListing aliexpressProductListing : esProductList) {
                                    try {
                                        EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                        updateItem.setId(aliexpressProductListing.getId());
                                        updateItem.setIpmSkuStock(Integer.valueOf(stock));
                                        smtItemEsBulkProcessor.updateIpmSkuStock(updateItem);
                                    } catch (Exception e) {
                                        log.error(e.getMessage(), e);
                                    }
                                }
                                end = System.currentTimeMillis();
                                l = (end - begin) / 1000L;
                                if (l > 2) {
                                    log.warn("修改库存回写Es耗时:" + l);
                                }
                            }
                            //记录日志
                            aliexpressProductLogService.updateStockCreate(productList, "admin", productSkuIdToStockMap, rsp, stock, isCheck, holidayStockMqBean);
                        }
                    });
                }

            } else {
                AliexpressExecutors.updateStock(responseJson -> {
                    responseJson.setStatus(StatusCode.FAIL);
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    if (saleAccountByAccountNumber != null) {
                        long begin = System.currentTimeMillis();
                        EditMutilpleSkuStocksOpenCall call = new EditMutilpleSkuStocksOpenCall();
                        ResponseJson rsp = call
                                .editMutilpleSkuStocksNew(saleAccountByAccountNumber, String.valueOf(productId),
                                        skuStocks.toJSONString(), halfLableCheck);

                        long end = System.currentTimeMillis();
                        long l = (end - begin) / 1000L;
                        if (l > 2) {
                            log.warn("修改库存耗时:" + l);
                        }

                        if (rsp.isSuccess()) {
                            begin = System.currentTimeMillis();
                            for (EsAliexpressProductListing aliexpressProductListing : esProductList) {
                                try {
                                    EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                    updateItem.setId(aliexpressProductListing.getId());
                                    updateItem.setIpmSkuStock(Integer.valueOf(stock));
                                    smtItemEsBulkProcessor.updateIpmSkuStock(updateItem);
//                                    EsAliexpressProductListing allById = esAliexpressProductListingService.findAllById(aliexpressProductListing.getId());
//                                    allById.setIpmSkuStock(Integer.valueOf(stock));
//                                    esAliexpressProductListingService.save(allById);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                            }
                            end = System.currentTimeMillis();
                            l = (end - begin) / 1000L;
                            if (l > 2) {
                                log.warn("修改库存回写Es耗时:" + l);
                            }
                        }
                        //记录日志
                        aliexpressProductLogService.updateStockCreate(productList, currentUser, productSkuIdToStockMap, rsp, stock, isCheck, holidayStockMqBean);
                    }
                });
            }

        }
        return null;
    }

    @Override
    public void updateImg(UpdateImgBean updateImgBean, String operator) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        boolean isUpdateMark = false;

        //修改了营销图需要修改到本地数据库
        //长图
        String longImg = updateImgBean.getLongImg();
        //方图
        String squareImg = updateImgBean.getSquareImg();

        AliexpressEsExtend aliexpressEsExtend = null;

        try {
            //先同步
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, updateImgBean.getAliexpressAccountNumber());
            JSONObject productEntity = OfferQueryProductOpenCall
                    .transResultToOfferUpdate(saleAccountByAccountNumber, updateImgBean.getProductId());
            String imageURLs = updateImgBean.getImageUrls();
            // ;分割的图片
            String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, imageURLs, null);
            //图片修改类型：1 只修改描述图片，2 只修改附图 3.都改
            Integer changeType = updateImgBean.getChangeType();
            if (changeType == 1) {
                OfferQueryProductOpenCall.updateDetailImgs(productEntity, null, CommonUtils
                        .splitList(imgUrlsWithPostedImg, ";"));
            } else if (changeType == 2) {
                List<String> strings = CommonUtils.splitList(imgUrlsWithPostedImg, ";");
                List<List<String>> lists = PagingUtils.newPagingList(strings, 6);
                productEntity.put("image_u_r_ls", StringUtils.join(lists.get(0), ";"));
            } else {
                List<String> strings = CommonUtils.splitList(imgUrlsWithPostedImg, ";");
                List<List<String>> lists = PagingUtils.newPagingList(strings, 6);
                productEntity.put("image_u_r_ls", StringUtils.join(lists.get(0), ";"));

                OfferQueryProductOpenCall.updateDetailImgs(productEntity, null, CommonUtils
                        .splitList(imgUrlsWithPostedImg, ";"));
            }

            //上传营销图到平台
            List<MarketImage> newMarketImages = new ArrayList<>();

            boolean sign = false;

            //都有smt的图的时候就不需要调整了
            if ((StringUtils.isNotBlank(squareImg) && AliexpressContentUtils.isSmtImg(squareImg))
                    && (StringUtils.isNotBlank(longImg) && AliexpressContentUtils.isSmtImg(longImg))) {
                sign = true;
            }

            //都为空也是需要调整的 数据库本身没有 就不需要改
            if (StringUtils.isBlank(longImg) && StringUtils.isBlank(squareImg)) {
                aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(updateImgBean.getAliexpressAccountNumber(), updateImgBean.getProductId());
                boolean b = aliexpressEsExtend != null && StringUtils.isBlank(aliexpressEsExtend.getLongImg()) && StringUtils.isBlank(aliexpressEsExtend.getSquareImg());
                if (!b) {
                    isUpdateMark = true;
                }
            }

            if (StringUtils.isNotBlank(longImg) && !sign) {
                isUpdateMark = true;
                //需要压缩图片
                if (StringUtils.indexOf(longImg, "-effect-copy.") == -1 && !AliexpressContentUtils.isSmtImg(longImg)) {
                    ResponseJson responseJson = AliexpressWaterMarkImgUtil.changeImg(longImg, 750, 1000);
                    if (responseJson.isSuccess()) {
                        longImg = responseJson.getMessage();
                    } else {
                        throw new Exception(responseJson.getMessage());
                    }
                }

                if (!AliexpressContentUtils.isSmtImg(longImg)) {
                    UploadImageOpenCall call = new UploadImageOpenCall();
                    longImg = call.uploadImageToAliexpress(saleAccountByAccountNumber, longImg, null, false, null);
                }

                MarketImage marketImage = new MarketImage();
                marketImage.setUrl(longImg);
                marketImage.setImage_type("1");
                newMarketImages.add(marketImage);
            }

            if (StringUtils.isNotBlank(squareImg) && !sign) {
                isUpdateMark = true;

                if (!AliexpressContentUtils.isSmtImg(squareImg)) {
                    UploadImageOpenCall call = new UploadImageOpenCall();
                    squareImg = call.uploadImageToAliexpress(saleAccountByAccountNumber, squareImg, null, false, null);
                }

                //方图
                MarketImage squareMarketImage = new MarketImage();
                squareMarketImage.setUrl(squareImg);
                squareMarketImage.setImage_type("2");
                newMarketImages.add(squareMarketImage);
            }

            if (isUpdateMark) {
                // 营销图
                JSONArray marketImageJsonArray = JSONArray.parseArray(JSON.toJSONString(newMarketImages));
                productEntity.put("market_images", marketImageJsonArray);
            }
            rsp = OfferEditProductOpenCall.offerEditProduct(saleAccountByAccountNumber, productEntity.toJSONString());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        //设置提示
        AliexpressProduct aliexpressProduct = new AliexpressProduct();
        aliexpressProduct.setAliexpressAccountNumber(updateImgBean.getAliexpressAccountNumber());
        aliexpressProduct.setArticleNumber(updateImgBean.getArticleNumber());
        aliexpressProduct.setProductId(updateImgBean.getProductId());
        aliexpressProduct.setOperator(StringUtils.isEmpty(operator) ? WebUtils.getUserName() : operator);
        aliexpressProduct.setErrorTip(rsp.getMessage());
        aliexpressProductLogService.updateProductImgCreate(aliexpressProduct, StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()));

        if (isUpdateMark) {
            aliexpressProductLogService.insert(aliexpressProduct, AliexpressProductOperateLogType.update_marketing_img, rsp);
            if (rsp.isSuccess()) {
                if (aliexpressEsExtend == null) {
                    aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(updateImgBean.getAliexpressAccountNumber(), updateImgBean.getProductId());
                }
                aliexpressEsExtend.setLongImg(StringUtils.isBlank(longImg) ? "" : longImg);
                aliexpressEsExtend.setSquareImg(StringUtils.isBlank(squareImg) ? "" : squareImg);
                aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
            }
        }
    }

    @Override
    public void updateSonSkuImg(List<EsAliexpressProductListing> esProductList, SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, String operator) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            if (CollectionUtils.isEmpty(esProductList)) {
                return;
            }
            EsAliexpressProductListing esAliexpressProductListing = esProductList.get(0);
            Long productId = esAliexpressProductListing.getProductId();
            //通过skuid分组
            Map<String, EsAliexpressProductListing> skuIdMap = new HashMap<>();
            for (EsAliexpressProductListing aliexpressProductListing : esProductList) {
                skuIdMap.put(aliexpressProductListing.getSkuId(), aliexpressProductListing);
            }
            //产品最新数据
            JSONObject productEntity = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccountAndBusinessResponse, productId);
            JSONArray aeProductSKUsArray = productEntity.getJSONArray("aeop_ae_product_s_k_us");
            JSONArray submitAeProductSKUsArray = new JSONArray();
            for (int i = 0; i < aeProductSKUsArray.size(); i++) {
                JSONObject jsonObject = aeProductSKUsArray.getJSONObject(i);
                submitAeProductSKUsArray.add(jsonObject);
                String skuId = jsonObject.getString("id");
                if (StringUtils.isBlank(skuId)) {
                    skuId = "<none>";
                }
                EsAliexpressProductListing productListing = skuIdMap.get(skuId);
                if (productListing != null) {
                    //新图
                    String skuDisplayImg = productListing.getSkuDisplayImg();
                    UploadImageOpenCall call = new UploadImageOpenCall();
                    String postedImgUrl = call.uploadImageToAliexpress(saleAccountAndBusinessResponse, skuDisplayImg, null, false, null);
                    if (StringUtils.isNotBlank(postedImgUrl)) {
                        JSONArray aeop_s_k_u_property = jsonObject.getJSONArray("aeop_s_k_u_property");
                        JSONArray submitAeop_s_k_u_property_list = new JSONArray();
                        for (int i1 = 0; i1 < aeop_s_k_u_property.size(); i1++) {
                            JSONObject jsonObject1 = aeop_s_k_u_property.getJSONObject(i1);
//                            String sku_image = jsonObject1.getString("sku_image");
//                            if(StringUtils.isNotBlank(sku_image)){
//                                jsonObject1.put("sku_image", postedImgUrl);
//                            }
                            jsonObject1.put("sku_image", postedImgUrl);
                            submitAeop_s_k_u_property_list.add(jsonObject1);
                        }
                        jsonObject.put("aeop_s_k_u_property", submitAeop_s_k_u_property_list);
                    }
                }
            }
            productEntity.put("aeop_ae_product_s_k_us", submitAeProductSKUsArray);
            rsp = OfferEditProductOpenCall.offerEditProduct(saleAccountAndBusinessResponse, productEntity.toJSONString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        } finally {
            List<AliexpressProductLog> logList = new ArrayList<>();
            for (EsAliexpressProductListing esAliexpressProductListing : esProductList) {
                AliexpressProductLog log = new AliexpressProductLog();
                log.setAccountNumber(esAliexpressProductListing.getAliexpressAccountNumber());
                log.setSkuCode(esAliexpressProductListing.getArticleNumber());
                log.setProductId(esAliexpressProductListing.getProductId());
                log.setOperateType(OperateLogTypeEnum.EDIT_SKU_IMG.getCode());
                log.setOperator(operator);
                log.setResult(rsp.isSuccess());
                log.setFailInfo(rsp.getMessage());
                logList.add(log);
            }
            aliexpressProductLogService.batchInsert(logList);
        }
    }

    @Override
    public String batchUpdateSubjectAndDetail(String idStr, Boolean isUpdateSubject, Boolean isUpdateDetail) {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.editingRequired.getCode()); //只修改上架产品
        request.setIdStr(idStr);
        request.setNotInDataSourceTypeList(Arrays.asList(AliexpressListingDataSourceEnum.SPUS.getCode())); //排查多SPU产品
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return "无有效数据";
        }
        // 根据产品编号去重 多属性只需要修改一次即可
        List<EsAliexpressProductListing> esProductList = esAliexpressProductListing.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(EsAliexpressProductListing::getProductId))), ArrayList::new));
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (EsAliexpressProductListing esProduct : esProductList) {
            try {
                String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                if (saleAccountAndBusiness != null) {
                    accountMap.put(aliexpressAccountNumber, saleAccountAndBusiness);
                }
            } catch (Exception e) {
                //忽略错误
                log.error(e.getMessage(), e);
            }
        }
        String userName = WebUtils.getUserName();
        for (EsAliexpressProductListing esProduct : esProductList) {

            Long productId = esProduct.getProductId();
            EsAliexpressProductListingRequest request1 = new EsAliexpressProductListingRequest();
            request1.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber", "spu", "articleNumber"});
            request1.setProductId(productId);
            List<EsAliexpressProductListing> listing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(request1);

            Set<String> spuSet = new HashSet<>();
            for (EsAliexpressProductListing aliexpressProductListing : listing) {
                String spu = StringUtils.isBlank(aliexpressProductListing.getSpu()) ? aliexpressProductListing.getArticleNumber() : aliexpressProductListing.getSpu();
                spuSet.add(spu);
            }

            if (spuSet.size() > 1) {
                //判断产品是不是多spu产品，如果是 排除
                log.warn("产品是多spu 不修改：" + productId);
                continue;
            }

            AliexpressProduct product = new AliexpressProduct();
            BeanUtils.copyProperties(esProduct, product);
            product.setOperator(userName);
            AliexpressExecutors.batchUpdateSubjectAndDetail(responseJson -> {
                responseJson.setStatus(StatusCode.FAIL);
                String articleNumber = esProduct.getArticleNumber();
                if (StringUtils.isBlank(articleNumber)) {
                    responseJson.setMessage("商品货号不存在!");
                    aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, responseJson);
                    return;
                }
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap
                        .get(esProduct.getAliexpressAccountNumber());

                if (saleAccountAndBusinessResponse == null) {
                    responseJson.setMessage("调用订单系统 获取不到账号!");
                    aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, responseJson);
                    return;
                }
                try {
                    AliexpressTemplate spuTitleAndDetail = AliexpressTemplateDataUtils
                            .getSpuTitleAndDetail(articleNumber, esProduct.getAliexpressAccountNumber(), null);

                    String subject = spuTitleAndDetail.getSubject();
                    String detail = spuTitleAndDetail.getDetail();
                    detail = detail.replaceAll("(?i)Features:", "<b>Features:</b><br>");
                    detail = detail.replaceAll("(?i)Specifications:", "<b>Specifications:</b><br>");
                    detail = detail.replaceAll("(?i)Package Includes:", "<b>Package Includes:</b><br>");
                    detail = detail.replaceAll("(?i)Note:", "<b>Note:</b><br>");
                    detail = "<div style=\"font-size: 18px;\">" + detail + "</div>";

                    //产品本身的图片
                    String productImageUrls = esProduct.getImageUrls();
                    List<String> productImgList = com.estone.erp.publish.common.util.CommonUtils
                            .splitList(productImageUrls, ";");

                    //如果需要更新描述 就用全字段编辑
                    if (isUpdateDetail) {
                        JSONObject jsonObject = OfferQueryProductOpenCall
                                .transResultToOfferUpdate(saleAccountAndBusinessResponse,
                                        esProduct.getProductId());
                        OfferQueryProductOpenCall.updateDetailImgs(jsonObject, detail, productImgList);

                        //替换标题
                        if (isUpdateSubject) {
                            // 多语言标题
                            JSONArray subjectJSONArray = new JSONArray();
                            JSONObject subjectJsonObject = new JSONObject();
                            subjectJSONArray.add(subjectJsonObject);
                            subjectJsonObject.put("value", subject);
                            subjectJsonObject.put("locale", "en_US");
                            jsonObject.put("subject_list", subjectJSONArray);
                        }
                        responseJson = OfferEditProductOpenCall.offerEditProduct(saleAccountAndBusinessResponse, jsonObject.toJSONString());

                    } else {
                        //标题规则
                        EditSimpleproductfiledOpenCall updateSubjectCall = new EditSimpleproductfiledOpenCall();
                        String updateSubjectCallRspStr = updateSubjectCall.setEditsimpleproductfiled(saleAccountAndBusinessResponse, esProduct.getProductId().toString(), "subject", subject);
                        responseJson = updateSubjectCall.checkErrorMessage(updateSubjectCallRspStr);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    responseJson.setMessage(e.getMessage());
                    aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, responseJson);
                    return;
                }
                aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, responseJson);
            });
        }
        return null;
    }

    @Override
    public String batchReplaceSubjectAndDetail(String idStr, String oldWord, String newWord, Boolean isReplaceSubject, Boolean isReplaceDetail) {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setIdStr(idStr);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return "无有效数据";
        }

        // 根据产品编号去重 多属性只需要修改一次即可
        List<EsAliexpressProductListing> esProductList = esAliexpressProductListing.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(EsAliexpressProductListing::getProductId))), ArrayList::new));
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (EsAliexpressProductListing esProduct : esProductList) {
            try {
                String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                if (saleAccountAndBusiness != null) {
                    accountMap.put(aliexpressAccountNumber, saleAccountAndBusiness);
                }
            } catch (Exception e) {
                //忽略错误
                log.error(e.getMessage(), e);
            }
        }
        String userName = WebUtils.getUserName();
        for (EsAliexpressProductListing esProduct : esProductList) {
            AliexpressProduct product = new AliexpressProduct();
            product.setAliexpressAccountNumber(esProduct.getAliexpressAccountNumber());
            product.setArticleNumber(esProduct.getArticleNumber());
            product.setProductId(esProduct.getProductId());
            product.setOperator(userName);
            AliexpressExecutors.autoUpdateOneDetail(() -> {
                ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
                String articleNumber = esProduct.getArticleNumber();
                if (StringUtils.isBlank(articleNumber)) {
                    responseJson.setMessage("商品货号不存在!");
                    aliexpressProductLogService.insert(product, OperateLogTypeEnum.RE_SUBJECT_DETAIL.getCode(), responseJson);
                    return;
                }
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap
                        .get(esProduct.getAliexpressAccountNumber());

                if (saleAccountAndBusinessResponse == null) {
                    responseJson.setMessage("调用订单系统 获取不到账号!");
                    aliexpressProductLogService.insert(product, OperateLogTypeEnum.RE_SUBJECT_DETAIL.getCode(), responseJson);
                    return;
                }
                try {
                    JSONObject jsonObject = OfferQueryProductOpenCall
                            .transResultToOfferUpdate(saleAccountAndBusinessResponse,
                                    esProduct.getProductId());
                    //标题
                    String enTitle = "";

                    //获取产品的标题
                    JSONArray subject_list = jsonObject.getJSONArray("subject_list");
                    for (int i = 0; i < subject_list.size(); i++) {
                        JSONObject titleJsonObject = subject_list.getJSONObject(i);
                        String locale = titleJsonObject.getString("locale");
                        if (StringUtils.equalsIgnoreCase(locale, "en_US")) {
                            enTitle = titleJsonObject.getString("value");
                            enTitle = TortUtils.replace(enTitle, oldWord, newWord, true);
                            enTitle = AliexpressContentUtils.changTitleForAccount(enTitle, null);
                        }
                    }

                    //如果需要更新描述 就用全字段编辑
                    if (isReplaceDetail) {
                        //替换标题
                        if (isReplaceSubject) {
                            // 多语言标题
                            JSONArray subjectJSONArray = new JSONArray();
                            JSONObject subjectJsonObject = new JSONObject();
                            subjectJSONArray.add(subjectJsonObject);
                            subjectJsonObject.put("value", enTitle);
                            subjectJsonObject.put("locale", "en_US");
                            jsonObject.put("subject_list", subjectJSONArray);
                        }

                        //描述处理
                        JSONArray detail_source_list = jsonObject.getJSONArray("detail_source_list");
                        for (int i = 0; i < detail_source_list.size(); i++) {
                            JSONObject jsonObject1 = detail_source_list.getJSONObject(i);
                            String locale = jsonObject1.getString("locale");
                            if (StringUtils.equalsIgnoreCase(locale, "en_US")) {
                                //pc端
                                JSONObject web_detail = jsonObject1.getJSONObject("web_detail");
                                if (web_detail != null) {
                                    JSONArray moduleList = web_detail.getJSONArray("moduleList");
                                    if (CollectionUtils.isNotEmpty(moduleList)) {
                                        for (int i1 = 0; i1 < moduleList.size(); i1++) {
                                            JSONObject jsonObject2 = moduleList.getJSONObject(i1);
                                            if (jsonObject2 != null) {
                                                String type = jsonObject2.getString("type");
                                                switch (type) {
                                                    case "html":
                                                        JSONObject html = jsonObject2.getJSONObject("html");
                                                        String content = html.getString("content");
                                                        content = TortUtils.replace(content, oldWord, newWord, true);
                                                        //回写数据
                                                        html.put("content", content);
                                                        break;
                                                    case "text-image":
                                                    case "text":
                                                        JSONArray texts1 = jsonObject2.getJSONArray("texts");
                                                        for (int i2 = 0; i2 < texts1.size(); i2++) {
                                                            JSONObject jsonObject3 = texts1.getJSONObject(i2);
                                                            String content1 = jsonObject3.getString("content");
                                                            content1 = TortUtils.replace(content1, oldWord, newWord, true);
                                                            jsonObject3.put("content", content1);
                                                        }
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                                //设置回原值
                                jsonObject1.put("web_detail", web_detail);

                                //移动端
                                JSONObject mobile_detail = jsonObject1.getJSONObject("mobile_detail");
                                if (mobile_detail != null) {
                                    JSONArray moduleList = mobile_detail.getJSONArray("moduleList");
                                    if (CollectionUtils.isNotEmpty(moduleList)) {
                                        for (int i1 = 0; i1 < moduleList.size(); i1++) {
                                            JSONObject jsonObject2 = moduleList.getJSONObject(i1);
                                            if (jsonObject2 != null) {
                                                String type = jsonObject2.getString("type");
                                                switch (type) {
                                                    case "text-image":
                                                    case "text":
                                                        JSONArray texts1 = jsonObject2.getJSONArray("texts");
                                                        for (int i2 = 0; i2 < texts1.size(); i2++) {
                                                            JSONObject jsonObject3 = texts1.getJSONObject(i2);
                                                            String content1 = jsonObject3.getString("content");
                                                            content1 = TortUtils.replace(content1, oldWord, newWord, true);
                                                            jsonObject3.put("content", content1);
                                                        }
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                                //设置回原值
                                jsonObject1.put("mobile_detail", mobile_detail);
                            }
                        }
                        responseJson = OfferEditProductOpenCall.offerEditProduct(saleAccountAndBusinessResponse, jsonObject.toJSONString());

                    } else {
                        //标题规则
                        EditSimpleproductfiledOpenCall updateSubjectCall = new EditSimpleproductfiledOpenCall();
                        String updateSubjectCallRspStr = updateSubjectCall.setEditsimpleproductfiled(saleAccountAndBusinessResponse, esProduct.getProductId().toString(), "subject", enTitle);
                        responseJson = updateSubjectCall.checkErrorMessage(updateSubjectCallRspStr);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    responseJson.setMessage(e.getMessage());
                    aliexpressProductLogService.insert(product, OperateLogTypeEnum.RE_SUBJECT_DETAIL.getCode(), responseJson);
                    return;
                }
                aliexpressProductLogService.insert(product, OperateLogTypeEnum.RE_SUBJECT_DETAIL.getCode(), responseJson);
            });
        }
        return null;
    }

    @Override
    public void updateGrossWeight(List<UpdatePriceEntity> returnResultList, Map<String, String> productWeightMap, String user, boolean isWait) {
        long start = System.currentTimeMillis();
        log.warn("速卖通改重量--开始");
        CountDownLatch countDownLatch = new CountDownLatch(returnResultList.size());

        String userName = StringUtils.isNotBlank(user) ? user : WebUtils.getUserName();
        returnResultList.forEach(item -> {
            AliexpressExecutors.updateWeight(() -> {
                try {
                    if (StringUtils.isNotBlank(item.getErrorTip())) {
                        return;
                    }
                    String accountNum = item.getSeller();
                    String productId = item.getProductId();
                    String grossWeight = productWeightMap.get(item.getProductId());

                    //减少查询信息
                    EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
                    checkReqest.setProductId(Long.valueOf(productId));
                    checkReqest.setAliexpressAccountNumber(accountNum);
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                            .getEsAliexpressProductListing(checkReqest);

                    //es中的重量
                    String esWeight = esAliexpressProductListing.get(0).getGrossWeight();

                    if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                        if (StringUtils.equalsIgnoreCase(esWeight, grossWeight)) {
                            item.setErrorTip("修改成功");
                            AliexpressProductLog log = new AliexpressProductLog();
                            log.setProductId(Long.valueOf(productId));
                            log.setAccountNumber(accountNum);
                            log.setSkuCode(esAliexpressProductListing.get(0).getArticleNumber());
                            log.setOperator(userName);
                            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                            log.setOperateType(OperateLogTypeEnum.update_weight.getCode());
                            log.setResult(true);
                            log.setFailInfo("重量相同不需要修改");
                            log.setWeightBeforeEdit(Double.valueOf(esWeight));
                            log.setWeightAfterEdit(Double.valueOf(grossWeight));
                            aliexpressProductLogService.insert(log);
                            return;
                        }
                    }

                    ResponseJson rsp = ExcelOperationUtils.authIntercept(accountNum, userName);
                    if (!rsp.isSuccess()) {
                        item.setErrorTip(rsp.getMessage());
                        return;
                    }

                    StringBuilder sb = new StringBuilder();
                    if (StringUtils.isBlank(grossWeight)) {
                        item.setErrorTip("重量不能为空");
                        return;
                    }
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNum);
                    if (saleAccountByAccountNumber == null) {
                        item.setErrorTip(accountNum + "账号查询为空！");
                        return;
                    }

                    EditSimpleproductfiledOpenCall call = new EditSimpleproductfiledOpenCall();
                    String callRspStr = call.setEditsimpleproductfiled(saleAccountByAccountNumber, productId, "grossWeight", grossWeight);
                    if (StringUtils.isNotBlank(callRspStr)) {
                        ResponseJson responseJson = call.checkErrorMessage(callRspStr);
                        if (responseJson.isSuccess()) {
                            sb.append("修改成功");
                        } else {
                            sb.append(responseJson.getMessage());
                        }

                        AliexpressProductLog log = new AliexpressProductLog();
                        log.setProductId(Long.valueOf(productId));
                        log.setAccountNumber(accountNum);
                        log.setSkuCode(esAliexpressProductListing.get(0).getArticleNumber());
                        log.setOperator(userName);
                        log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                        log.setOperateType(OperateLogTypeEnum.update_weight.getCode());
                        log.setResult(responseJson.isSuccess());
                        log.setFailInfo(responseJson.getMessage());
                        log.setWeightBeforeEdit(Double.valueOf(esWeight));
                        log.setWeightAfterEdit(Double.valueOf(grossWeight));
                        aliexpressProductLogService.insert(log);

                    } else {
                        sb.append("请求无结果返回，请联系IT人员");
                    }
                    item.setErrorTip(sb.toString());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    if (null != item.getErrorTip() && item.getErrorTip().equals("修改成功")) {
                        String productId = item.getProductId();
                        //减少查询信息
                        EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
                        checkReqest.setProductId(Long.valueOf(productId));
                        checkReqest.setQueryFields(new String[]{"id"});
                        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                                .getEsAliexpressProductListing(checkReqest);

                        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                            for (EsAliexpressProductListing product : esAliexpressProductListing) {
                                if (null != productWeightMap.get(productId)) {
                                    EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                    updateItem.setId(product.getId());
                                    updateItem.setGrossWeight(productWeightMap.get(productId));
                                    smtItemEsBulkProcessor.updateGrossWeight(updateItem);
                                }
                            }
                        }
                    }

                    if (isWait) {
                        countDownLatch.countDown();
                    }
                }
            });
        });

        if (isWait) {
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
//        long syncEnd = System.currentTimeMillis();
//        log.warn("速卖通改重量：数据" + returnResultList.size() + "条，耗时：" + (syncEnd - start) / 1000 + "秒");
//        List<EsAliexpressProductListing> updateList = new ArrayList<>();
//        for (UpdatePriceEntity item : returnResultList) {
//            if(null != item.getErrorTip() && item.getErrorTip().equals("修改成功")) {
//                String productId = item.getProductId();
//                //减少查询信息
//                EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
//                checkReqest.setProductId(Long.valueOf(productId));
//                checkReqest.setQueryFields(null);
//                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
//                        .getEsAliexpressProductListing(checkReqest);
//
//                if(CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
//                    for (EsAliexpressProductListing product : esAliexpressProductListing) {
//                        if(null != productWeightMap.get(productId)) {
//                            product.setGrossWeight(productWeightMap.get(productId));
//                            updateList.add(product);
//                        }
//                    }
//                }
//            }
//        }
//        if(CollectionUtils.isNotEmpty(updateList)) {
//            esAliexpressProductListingService.saveAll(updateList);
//        }
    }

    public void excelDeleteProduct(List<UpdatePriceEntity> returnResultList, String user) {
        log.warn("速卖通删除产品--开始");
        if (CollectionUtils.isEmpty(returnResultList)) {
            return;
        }

        Map<String, List<UpdatePriceEntity>> map = returnResultList.stream().collect(Collectors.groupingBy(UpdatePriceEntity::getSeller));

        for (Map.Entry<String, List<UpdatePriceEntity>> stringListEntry : map.entrySet()) {
            String key = stringListEntry.getKey();
            List<UpdatePriceEntity> value = stringListEntry.getValue();

            List<Long> excelProductIdList = value.stream().map(t -> Long.valueOf(t.getProductId())).collect(Collectors.toList());

            long begin = System.currentTimeMillis();

            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setQueryFields(new String[]{"id", "productId"});
            request.setAliexpressAccountNumber(key);
            request.setProductIdList(excelProductIdList);
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);

            long end = System.currentTimeMillis();
            long l = (end - begin) / 1000;
            if (l > 10) {
                log.warn(key + "账号删除 查询耗时：" + l);
            }

            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                for (UpdatePriceEntity updatePriceEntity : value) {
                    updatePriceEntity.setErrorTip("数据不存在");
                    break;
                }
            } else {
                List<Long> dbProductIdList = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toList());
                excelProductIdList.removeAll(dbProductIdList);
                if (CollectionUtils.isNotEmpty(excelProductIdList)) {
                    for (UpdatePriceEntity updatePriceEntity : value) {
                        String productId = updatePriceEntity.getProductId();
                        if (excelProductIdList.contains(Long.valueOf(productId))) {
                            updatePriceEntity.setErrorTip("数据不存在");
                        }
                    }
                }

                begin = System.currentTimeMillis();
                List<List<EsAliexpressProductListing>> lists = PagingUtils.newPagingList(esAliexpressProductListing, 1000);
                for (List<EsAliexpressProductListing> list : lists) {
                    esAliexpressProductListingService.deleteByList(list);
                }
                end = System.currentTimeMillis();
                l = (end - begin) / 1000;
                if (l > 10) {
                    log.warn(key + "账号删除耗时：" + l + " 数据size:" + esAliexpressProductListing.size());
                }

                begin = System.currentTimeMillis();
                //用产品id删除，防止店铺正常 误操作，导致删除后面同步的产品
                Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
                aliexpressEsExtendService.deleteByProductId(new ArrayList<>(productIdSet));
                end = System.currentTimeMillis();
                l = (end - begin) / 1000;
                if (l > 10) {
                    log.warn(key + "账号 扩展表删除耗时：" + l + " 数据size:" + productIdSet.size());
                }

                for (UpdatePriceEntity updatePriceEntity : value) {
                    String productId = updatePriceEntity.getProductId();
                    if (dbProductIdList.contains(Long.valueOf(productId))) {
                        updatePriceEntity.setErrorTip("删除成功！");
                    }
                }
            }
        }

//        returnResultList.forEach(item -> {
//            try {
//                if(StringUtils.isNotBlank(item.getErrorTip())) {
//                    return;
//                }
//                String accountNum = item.getSeller();
//                String productId = item.getProductId();
//
//                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
//                request.setQueryFields(new String[]{"id", "productId"});
//                request.setAliexpressAccountNumber(accountNum);
//                request.setProductId(Long.valueOf(productId));
//                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
//                if(CollectionUtils.isEmpty(esAliexpressProductListing)){
//                    item.setErrorTip("数据不存在");
//                    return;
//                }
//                esAliexpressProductListingService.deleteByList(esAliexpressProductListing);
//                //用产品id删除，防止店铺正常 误操作，导致删除后面同步的产品
//                Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
//                aliexpressEsExtendService.deleteByProductId(new ArrayList<>(productIdSet));
//
//                item.setErrorTip("删除成功");
//            }catch(Exception e) {
//                log.error(e.getMessage(), e);
//            }
//        });

    }

    @Override
    public void transTemp(Long productId, String operator) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            if (productId == null) {
                rsp.setMessage("产品id不能为空");
                return;
            }
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setProductId(productId);
            request.setQueryFields(null);//查全字段
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(request);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                aliexpressProductLogService.tranTemp(productId, operator, "查询不到本地产品！");
                return;
            }

            EsAliexpressProductListing esAliexpressProduct = esAliexpressProductListing.get(0);
            AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
            esExtendExample.createCriteria().andProductIdEqualTo(productId).andAliexpressAccountNumberEqualTo(esAliexpressProduct.getAliexpressAccountNumber());
            List<AliexpressEsExtend> extendList = this.selectByExample(esExtendExample);

            AliexpressEsExtend aliexpressEsExtend = null;

            if (CollectionUtils.isNotEmpty(extendList)) {
                aliexpressEsExtend = extendList.get(0);
                //设置操作人，线程操作会取不到登陆人
                aliexpressEsExtend.setOperator(operator);
            }
            transTemp(esAliexpressProduct, aliexpressEsExtend);
        } catch (Exception e) {
            rsp.setMessage("转范本异常:" + e.getMessage());
            aliexpressProductLogService.tranTemp(productId, operator, rsp.getMessage());
            return;
        }
    }

    @Override
    public AliexpressTgTemplate esTransTgTemp(Long productId, String account, Double grossMargin) throws Exception {
        if (productId == null) {
            return null;
        }
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        request.setQueryFields(null);//查全字段
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            throw new Exception("es无法查到该产品，请求确定产品是否存在！");
        }

        EsAliexpressProductListing esProduct = esAliexpressProductListing.get(0);
        AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
        esExtendExample.createCriteria().andProductIdEqualTo(productId).andAliexpressAccountNumberEqualTo(esProduct.getAliexpressAccountNumber());
        List<AliexpressEsExtend> extendList = this.selectByExample(esExtendExample);
        if (CollectionUtils.isEmpty(extendList)) {
            throw new Exception("查询不到扩展信息");
        }

        AliexpressEsExtend aliexpressEsExtend = extendList.get(0);
        AliexpressTgTemplate tgTemplate = AliexpressProductUnits.compileEsProductToTgTemplate(esProduct, aliexpressEsExtend, account, grossMargin);
        return tgTemplate;
    }

    @Override
    public AliexpressTemplate esTransTemp(Long productId) throws Exception {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        request.setQueryFields(null);//查全字段
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            throw new Exception("es无法查到该产品，请求确定产品是否存在！");
        }

        EsAliexpressProductListing esProduct = esAliexpressProductListing.get(0);
        AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
        esExtendExample.createCriteria().andProductIdEqualTo(productId).andAliexpressAccountNumberEqualTo(esProduct.getAliexpressAccountNumber());
        List<AliexpressEsExtend> extendList = this.selectByExample(esExtendExample);
        if (CollectionUtils.isEmpty(extendList)) {
            throw new Exception("查询不到扩展信息");
        }

        AliexpressEsExtend aliexpressEsExtend = extendList.get(0);
        AliexpressTemplate template = AliexpressProductUnits.compileEsProductToTemplate(esProduct, aliexpressEsExtend);

        List<String> stopStatusList = Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode());

        //如果本身没有，需要系统默认
        if (template.getManufactureId() == null) {
            String articleNumber = esAliexpressProductListing.stream().filter(t -> StringUtils.isNotBlank(t.getSkuStatus()) && !stopStatusList.contains(t.getSkuStatus()))
                    .map(t -> t.getArticleNumber()).findFirst().orElse(null);
            if (StringUtils.isBlank(articleNumber)) {
                log.info("系统关联制造商无有效货号:" + esProduct.getProductId());
            } else {
                try {
                    //制造商信息
                    Map<String, String> skuMap = ProductUtils.getGpsrManufacturerBySku(Arrays.asList(articleNumber));
                    if (skuMap == null || skuMap.isEmpty()) {
                        log.info("sku无制造商信息 " + articleNumber);
                    } else {
                        String name = skuMap.get(articleNumber.toUpperCase());
                        if (StringUtils.isBlank(name)) {
                            log.info("产品id[%s], 货号[%s], 产品系统无制造商公司", esProduct.getProductId(), articleNumber);
                        } else {
                            template.setManufactureName(name);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

        }
        return template;
    }

    @Override
    public void transTemp(EsAliexpressProductListing esProduct, AliexpressEsExtend esExtend) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        String operator = "";
        if (esExtend != null) {
            operator = esExtend.getOperator();
        } else {
            esExtend = new AliexpressEsExtend();
        }
        Long productId = esProduct.getProductId();

        AliexpressProduct aliexpressProduct = new AliexpressProduct();
        aliexpressProduct.setOperator(operator);
        BeanUtils.copyProperties(esProduct, aliexpressProduct);
        try {
            //通过货号查询主货号
            String articleNumber = esProduct.getArticleNumber();
            Integer categoryId = esProduct.getCategoryId();
            if (StringUtils.isBlank(articleNumber) || categoryId == null) {
                rsp.setMessage(productId + " 货号或者分类id为空，请检查数据！");
                aliexpressProductLogService.tranTemp(aliexpressProduct, articleNumber, rsp);
                return;
            }
            List<String> articleNumberList = new ArrayList<>();
            articleNumberList.add(articleNumber);
            Map<String, String> mainSkuMap = ProductUtils.getMainSkuBySubSku(articleNumberList);

            String mainSku = mainSkuMap.get(articleNumber);
            if (StringUtils.isBlank(mainSku)) {
                //查询不到主货号
                rsp.setMessage(productId + ": 货号：" + articleNumber + " 查询不到主货号！");
                aliexpressProductLogService.tranTemp(aliexpressProduct, articleNumber, rsp);
                return;
            }

            //通过主货号 + 分类id 查询是否存在
            AliexpressTemplateCriteria criteria = new AliexpressTemplateCriteria();
            criteria.setArticleNumber(mainSku);
            criteria.setCategoryId(categoryId);
            criteria.setIsParent(true);
            if (CollectionUtils
                    .isNotEmpty(aliexpressTemplateService.timingSelectByExample(criteria.getExample()))) {
                rsp.setMessage(productId + ": 货号：" + mainSku + ": 分类id：" + categoryId + " 范本已存在！");
                aliexpressProductLogService.tranTemp(aliexpressProduct, mainSku, rsp);
                //分类已存在
                return;
            }
            aliexpressProduct.setAeopAeProductSkusJson(esExtend.getAeopAeProductSkusJson());
            aliexpressProduct.setAeopNationalQuoteConfiguration(esExtend.getAeopNationalQuoteConfiguration());
            aliexpressProduct.setAeopAeProductPropertysJson(esExtend.getAeopAeProductPropertysJson());
            aliexpressProduct.setAeopAeMultimedia(esExtend.getAeopAeMultimedia());
            aliexpressProduct.setMobileDetail(esExtend.getMobileDetail());
            AliexpressTemplate aliexpressTemplate = AliexpressProductUnits
                    .compileProductToTemplate(aliexpressProduct, mainSku);

            //获取产品系统最新的标题，描述 图片
            AliexpressTemplate aliexpressTemplateNew = AliexpressTemplateDataUtils.changeNewInfo(aliexpressTemplate);

            if (aliexpressTemplate == null) {
                rsp.setMessage("转范本异常:" + aliexpressTemplate.getErrorMsg());
                aliexpressProductLogService.tranTemp(productId, operator, "转范本异常:" + aliexpressTemplate.getErrorMsg());
                return;
            }
            aliexpressTemplateNew.setIsParent(true);
            aliexpressTemplateNew.setTable(AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode());
            //设置操作人
            aliexpressTemplate.setCreator(operator);
            aliexpressTemplateNew.setCreateTime(new Timestamp(System.currentTimeMillis()));
            int insert = aliexpressTemplateService.insert(aliexpressTemplateNew);
            //成功
            if (insert > 0) {
                rsp.setStatus(StatusCode.SUCCESS);
                aliexpressProductLogService.tranTemp(aliexpressProduct, mainSku, rsp);
            }

        } catch (Exception e) {
            rsp.setMessage("转范本异常:" + e.getMessage());
            aliexpressProductLogService.tranTemp(productId, operator, rsp.getMessage());
        }
    }

    @Override
    public String batchSetGroup(List<String> ids, String groupId, boolean retain) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        String userName = WebUtils.getUserName();
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdList(ids);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return "没有获取到产品数据！";
        }

        // 去除重复产品
        Map<Long, EsAliexpressProductListing> productMap = new HashMap<>();

        // 存储账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        esAliexpressProductListing.forEach(p -> {
            Long productId = p.getProductId();
            String aliexpressAccountNumber = p.getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse aliexpressAccount = accountMap.get(aliexpressAccountNumber);
            if (aliexpressAccount == null) {
                aliexpressAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, aliexpressAccount);
            }
            if (aliexpressAccount != null) {
                if (productMap.get(productId) == null) {
                    productMap.put(productId, p);
                }
            }
        });

        if (productMap.size() == 0) {
            return null;
        }
        for (EsAliexpressProductListing esProduct : productMap.values()) {
            Long productId = esProduct.getProductId();
            AliexpressExecutors.setGroup(responseJson -> {
                String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                SaleAccountAndBusinessResponse account = accountMap.get(aliexpressAccountNumber);
                boolean isError = false;
                //需要提交的分组id
                String submitGroupIds = groupId;

                //是否保留之前分组
                if (retain) {
                    SynchItemOpenCall synchItemCall = new SynchItemOpenCall();
                    AliexpressProduct product = new AliexpressProduct();
                    product.setProductId(productId);
                    product.setAliexpressAccountNumber(account.getAccountNumber());
                    String syncResult = synchItemCall.getProduct(account, productId);
                    if (StringUtils.isBlank(syncResult)) {
                        responseJson.addError(
                                new ResponseError(StatusCode.FAIL, productId.toString(), "同步产品数据返回为空！"));
                        isError = true;
                    }
                    try {
                        JSONObject object = JSONObject.parseObject(syncResult);
                        if (object.containsKey("aliexpress_postproduct_redefining_findaeproductbyid_response")) {
                            JSONObject productRsp = object
                                    .getJSONObject("aliexpress_postproduct_redefining_findaeproductbyid_response");
                            if (productRsp.containsKey("result")) {
                                JSONObject obj = productRsp.getJSONObject("result");
                                Object group_id = obj.get("group_id");
                                if (group_id != null) {
                                    submitGroupIds += "," + group_id.toString();
                                }
                            }
                        }
                    } catch (Exception e) {
                        responseJson.addError(
                                new ResponseError(StatusCode.FAIL, productId.toString(), "同步产品异常" + e.getMessage()));
                        isError = true;
                    }
                }
                if (!isError) {
                    GroupOpenCall call = new GroupOpenCall();
                    //去除重复分组
                    List<String> strings = CommonUtils.splitList(submitGroupIds, ",");
                    String join = StringUtils.join(strings, ",");
                    ResponseJson resultJson = call.setGroup(account, String.valueOf(productId), join);
                    if (resultJson.isSuccess()) {
                        responseJson.addError(
                                new ResponseError(StatusCode.SUCCESS, productId.toString(), "修改成功！"));
                        SynchItemOpenCall synchCall = new SynchItemOpenCall();
                        AliexpressProduct aliexpressProduct = new AliexpressProduct();
                        aliexpressProduct.setProductId(productId);
                        aliexpressProduct.setAliexpressAccountNumber(account.getAccountNumber());
                        synchCall.syncAliexpressProductInfo(account, productId);
                    } else {
                        String format = String.format(
                                "account number[%s], product id[%s], group id[%s] error message[]",
                                aliexpressAccountNumber, productId, groupId, resultJson.getMessage());
                        responseJson.addError(
                                new ResponseError(StatusCode.FAIL, productId.toString(), format));
                    }

                    AliexpressProductLog log = new AliexpressProductLog();
                    log.setAccountNumber(aliexpressAccountNumber);
                    log.setOperateType(OperateLogTypeEnum.update_group_id.getCode());
                    log.setOperator(userName);
                    log.setResult(resultJson.isSuccess());
                    log.setProductId(Long.valueOf(productId));
                    log.setFailInfo(resultJson.getMessage());
                    aliexpressProductLogService.insert(log);
                }

            });
        }
        return null;
    }

    @Override
    public String batchSetEditsimpleproductfiled(List<String> ids, String fiedName, String fiedValue, String userName, String... ruleName) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setIdList(ids);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return null;
        }
        try {
            Map<Long, List<EsAliexpressProductListing>> longListMap = esAliexpressProductListing.stream()
                    .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
            //账号map
            Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : longListMap.entrySet()) {
                AliexpressExecutors.updateTemplate((rep) -> {
                    EsAliexpressProductListing esProduct = longListEntry.getValue().get(0);
                    String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                    SaleAccountAndBusinessResponse account = accountMap
                            .get(aliexpressAccountNumber);
                    if (null == account) {
                        account = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        accountMap.put(aliexpressAccountNumber, account);
                    }
                    if (account != null) {
                        Long productId = esProduct.getProductId();

                        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

                        //http://************:8080/browse/ES-8936
                        //如果修改发货天数 需要校验 分类

                        if (StringUtils.equalsIgnoreCase("deliveryTime", fiedName)) {
                            Integer categoryId = esProduct.getCategoryId();
                            String msg = aliexpressCategoryDeliveryDayService.tempCategoryIdCheck(categoryId, Integer.valueOf(fiedValue));
                            if (StringUtils.isNotBlank(msg)) {
                                //修改发货天数已经大于配置 需要拦截报错
                                AliexpressProductLog log = new AliexpressProductLog();
                                log.setAccountNumber(aliexpressAccountNumber);
                                log.setSkuCode(esProduct.getArticleNumber());
                                log.setOperateType(OperateLogTypeEnum.update_delivery_time.getCode());
                                log.setOperator(userName);
                                log.setResult(responseJson.isSuccess());
                                log.setProductId(esProduct.getProductId());
                                String format = String.format("产品id%s, %s", productId, msg);
                                log.setFailInfo(format);
                                aliexpressProductLogService.insert(log);
                                return;
                            }
                        }

                        try {
                            EditSimpleproductfiledOpenCall call = new EditSimpleproductfiledOpenCall();
                            String callRspStr = call.setEditsimpleproductfiled(account,
                                    productId.toString(), fiedName, fiedValue);
                            log.warn(String.format("修改单个字段 产品id[%s] + 请求结果[%s]", productId, callRspStr));
                            responseJson = call.checkErrorMessage(callRspStr);

                        } catch (Exception e) {
                            responseJson.setMessage(e.getMessage());
                            sb.append(String.format(
                                    "account number[%s], product id[%s], fiedName[%s] error message[]<br>",
                                    aliexpressAccountNumber, productId, fiedName, "请求异常：" + e.getMessage()));
                        }

                        //记录日志
                        if (StringUtils.equalsIgnoreCase("deliveryTime", fiedName)
                                || StringUtils.equalsIgnoreCase("freightTemplateId", fiedName)
                                || StringUtils.equalsIgnoreCase("reduceStrategy", fiedName)) {

                            if (responseJson.isSuccess()) {
                                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                                request.setProductId(esProduct.getProductId());
                                request.setQueryFields(new String[]{"id"});
                                List<EsAliexpressProductListing> productListing = esAliexpressProductListingService
                                        .getEsAliexpressProductListing(request);

                                if (StringUtils.equalsIgnoreCase("deliveryTime", fiedName)) {
                                    for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                                        aliexpressProductListing.setDeliveryTime(Integer.valueOf(fiedValue));
                                    }
                                } else if (StringUtils.equalsIgnoreCase("freightTemplateId", fiedName)) {
                                    for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                                        aliexpressProductListing.setFreightTemplateId(Long.valueOf(fiedValue));
                                    }
                                } else if (StringUtils.equalsIgnoreCase("reduceStrategy", fiedName)) {
                                    for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                                        aliexpressProductListing.setReduceStrategy(fiedValue);
                                    }
                                }

                                for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                                    esAliexpressProductListingService.updateRequest(aliexpressProductListing);
                                }
//                                esAliexpressProductListingService.saveAll(productListing);
                            }

                            AliexpressProductLog log = new AliexpressProductLog();
                            log.setAccountNumber(aliexpressAccountNumber);
                            log.setSkuCode(esProduct.getArticleNumber());
                            if (StringUtils.equalsIgnoreCase("deliveryTime", fiedName)) {
                                log.setOperateType(OperateLogTypeEnum.update_delivery_time.getCode());
                            } else if (StringUtils.equalsIgnoreCase("freightTemplateId", fiedName)) {
                                log.setOperateType(OperateLogTypeEnum.update_freight_template_id.getCode());
                            } else if (StringUtils.equalsIgnoreCase("reduceStrategy", fiedName)) {
                                log.setOperateType(OperateLogTypeEnum.update_reduce_strategy.getCode());
                            }
                            log.setOperator(userName);
                            log.setResult(responseJson.isSuccess());
                            log.setProductId(esProduct.getProductId());
                            log.setFailInfo(responseJson.getMessage());
                            if (ruleName != null && ruleName.length > 0) {
                                log.setRuleName(ruleName[0]);
                            }
                            aliexpressProductLogService.insert(log);
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            sb.append("数据异常：" + e.getMessage());
        }
        return sb.toString();
    }

    @Override
    public void batchSetEditsimpleproductfiled(EsAliexpressProductListingRequest esRequest, String freightTemplateIdStr, String groupIdStr, String promiseTemplateIdStr, String userName) {
        //页面修改 这2个参数必须有一个
        String productStatusType = esRequest.getProductStatusType();
        List<String> idList = esRequest.getIdList();
        if (StringUtils.isBlank(productStatusType) && CollectionUtils.isEmpty(idList)) {
            return;
        }
        esRequest.setQueryFields(new String[]{"productId", "aliexpressAccountNumber", "spu", "articleNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return;
        }
        //一个产品只修改一次
        Map<Long, List<EsAliexpressProductListing>> productMap = esAliexpressProductListing.stream()
                .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));

        //账号map
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        for (List<EsAliexpressProductListing> esProductList : productMap.values()) {
            AliexpressExecutors.smtUpdateTemplateExecute(() -> {
                ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
                EsAliexpressProductListing esProduct = esProductList.get(0);
                try {
                    String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                    Long productId = esProduct.getProductId();
                    if (StringUtils.isBlank(aliexpressAccountNumber) || productId == null) {
                        return;
                    }
                    SaleAccountAndBusinessResponse account = accountMap
                            .get(aliexpressAccountNumber);
                    if (null == account) {
                        account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        accountMap.put(aliexpressAccountNumber, account);
                    }
                    if (null == account) {
                        return;
                    }
                    //修改运费模板
                    String freightTemplateIdRsp = null;
                    ResponseJson groupIdResponse = null;
                    String promiseTemplateIdRsp = null;
                    EditSimpleproductfiledOpenCall call = new EditSimpleproductfiledOpenCall();
                    if (StringUtils.isNotBlank(freightTemplateIdStr)) {
                        freightTemplateIdRsp = call.setEditsimpleproductfiled(account, productId.toString(), "freightTemplateId", freightTemplateIdStr);
                    }
                    //设置产品分组
                    if (StringUtils.isNotBlank(groupIdStr)) {
                        //同一个产品连续修改，平台会锁定
                        if (StringUtils.isNotBlank(freightTemplateIdStr)) {
                            try {
                                Thread.sleep(1500L);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                        GroupOpenCall groupCall = new GroupOpenCall();
                        groupIdResponse = groupCall.setGroup(account, productId.toString(), groupIdStr);
                    }
                    //设置服务模板
                    if (StringUtils.isNotBlank(promiseTemplateIdStr)) {
                        //同一个产品连续修改，平台会锁定
                        if (StringUtils.isNotBlank(freightTemplateIdStr) || StringUtils.isNotBlank(groupIdStr)) {
                            try {
                                Thread.sleep(1500L);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                        promiseTemplateIdRsp = call.setEditsimpleproductfiled(account, productId.toString(), "promiseTemplateId", promiseTemplateIdStr);
                    }

                    ResponseJson freightTemplateIdResponse = null;
                    if (null != freightTemplateIdRsp) {
                        freightTemplateIdResponse = call.checkErrorMessage(freightTemplateIdRsp);
                    }

                    ResponseJson promiseTemplateResponse = null;
                    if (null != promiseTemplateIdRsp) {
                        promiseTemplateResponse = call.checkErrorMessage(promiseTemplateIdRsp);
                    }
                    // 综合处理多次请求结果结果合并一条记录
                    String errorMsg = "";
                    if (freightTemplateIdResponse != null && StringUtils.isNotBlank(freightTemplateIdResponse.getMessage())) {
                        errorMsg += "修改运费模板失败:" + freightTemplateIdResponse.getMessage();
                    }
                    if (groupIdResponse != null && StringUtils.isNotBlank(groupIdResponse.getMessage())) {
                        errorMsg += "修改产品分组失败:" + groupIdResponse.getMessage();
                    }
                    if (promiseTemplateResponse != null && StringUtils.isNotBlank(promiseTemplateResponse.getMessage())) {
                        errorMsg += "修改服务模板失败:" + promiseTemplateResponse.getMessage();
                    }

                    rsp.setMessage(errorMsg);

                    //没有错误就认为成功
                    if (StringUtils.isBlank(errorMsg)) {
                        rsp.setStatus(StatusCode.SUCCESS);
                    }

                    SynchItemOpenCall synchCall = new SynchItemOpenCall();
                    synchCall.syncAliexpressProductInfo(account, productId);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    rsp.setMessage(e.getMessage());
                }
                //处理结果
                aliexpressProductLogService.insert(esProduct, OperateLogTypeEnum.update_product_template.getCode(), userName, rsp);
            });
        }
    }

    @Override
    public void batchOnlineOrOfflineProduct(List<String> idList, int manipulateType, Boolean isCheck, String optimized) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        String currentUser = WebUtils.getUserName();
        List<EsAliexpressProductListing> esAliexpressProductListing = null;
        //待优化产品
        if (StringUtils.isNotBlank(optimized) && StringUtils.equalsIgnoreCase(optimized, "true")) {
            AliexpressProductDiagnosisExample diagnosisExample = new AliexpressProductDiagnosisExample();
            diagnosisExample.createCriteria().andIdIn(CommonUtils.splitIntList(StringUtils.join(idList, ","), ","));
            List<AliexpressProductDiagnosis> aliexpressProductDiagnoses = aliexpressProductDiagnosisService.selectByExample(diagnosisExample);
            if (CollectionUtils.isEmpty(aliexpressProductDiagnoses)) {
                return;
            }
            List<Long> productIdList = aliexpressProductDiagnoses.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdList(productIdList);
            esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
        } else {
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setIdList(idList);
            esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);
        }

        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            // 去掉重复的productId
            Map<Long, EsAliexpressProductListing> productMap = new HashMap<>();
            for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
                Long productId = esProduct.getProductId();
                if (productId != null && !productMap.containsKey(productId)) {
                    productMap.put(productId, esProduct);
                }
            }
            esAliexpressProductListing = new ArrayList<>(productMap.values());

            // 根据账号分组
            Map<String, List<EsAliexpressProductListing>> accountMap = new HashMap<>();
            for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
                String accountNumber = esProduct.getAliexpressAccountNumber();
                List<EsAliexpressProductListing> esProductList = accountMap.get(accountNumber);
                if (esProductList == null) {
                    esProductList = new ArrayList<>();
                    esProductList.add(esProduct);
                    accountMap.put(accountNumber, esProductList);
                } else {
                    esProductList.add(esProduct);
                }
            }

            for (Map.Entry<String, List<EsAliexpressProductListing>> entry : accountMap.entrySet()) {
                String accountNumber = entry.getKey();
                List<EsAliexpressProductListing> esProductList = entry.getValue();

                AliexpressExecutors.onlineOrOffline(rsp -> {
                    List<List<EsAliexpressProductListing>> lists = PagingUtils.pagingList(esProductList, 10);
                    for (List<EsAliexpressProductListing> list : lists) {
                        List<Long> productIds = new ArrayList<>();
                        for (EsAliexpressProductListing esProduct : list) {
                            productIds.add(esProduct.getProductId());
                        }
                        onlineOrOfflineProduct(accountNumber, productIds, manipulateType, currentUser, isCheck, null, optimized);
                    }
                });
            }
        }
    }

    //上下架
    @Override
    public void onlineOrOfflineProduct(String accountNumber, List<Long> productIds, int manipulateType, String currentUser, Boolean isCheck, SaleAccountAndBusinessResponse saleAccountByAccountNumber, String... optimized) {
        try {

            if (saleAccountByAccountNumber == null) {
                saleAccountByAccountNumber = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
            }
            ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
            if (manipulateType == AliexpressProductManipulateTypeEnum.ONLINE.getType()) {
                OnlineProductsOpenCall onlinCall = new OnlineProductsOpenCall();
                rsp = onlinCall.onlineProducts(saleAccountByAccountNumber, StringUtils.join(productIds, ";"));
            } else {
                //需要排除的产品id
                List<Long> excludeProductIdList = new ArrayList<>();
                if (isCheck != null && isCheck) {
                    List<String> skuStatusList = Arrays.asList(SingleItemEnum.CLEARANCE.getEnName(), SingleItemEnum.REDUCTION.getEnName());
                    for (Long productId : productIds) {
                        boolean isHit = false;
                        String errMsg = "";
                        try {
                            //只用判断上架的产品
                            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                            request.setAliexpressAccountNumber(accountNumber);
                            request.setProductId(productId);
                            request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                            request.setQueryFields(new String[]{"productId", "articleNumber", "skuStatus", "forbidChannel", "productStatusType"});
                            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                                continue;
                            }

                            boolean isFilter = false;
                            //如果产品有禁售平台 就不用考虑sku状态库存了，是可以下架的
                            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                                String forbidChannel = aliexpressProductListing.getForbidChannel();
                                Boolean hasForbiddenSales = StringUtils.isNotBlank(forbidChannel) && StringUtils.contains(forbidChannel, "," + SaleChannel.CHANNEL_SMT + ",");
                                if (hasForbiddenSales) {
                                    isFilter = true;
                                    break;
                                }
                            }

                            if (isFilter) {
                                continue;
                            }

                            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                                String skuStatus = aliexpressProductListing.getSkuStatus();
                                String articleNumber = aliexpressProductListing.getArticleNumber();
                                String forbidChannel = aliexpressProductListing.getForbidChannel();
                                //包含清仓 甩卖
                                if (skuStatusList.contains(skuStatus)) {
                                    //只有true false
                                    Boolean hasForbiddenSales = StringUtils.isNotBlank(forbidChannel) && StringUtils.contains(forbidChannel, "," + SaleChannel.CHANNEL_SMT + ",");
                                    Integer skuSystemStock = SkuStockUtils.getSkuSystemStock(articleNumber);
                                    if ((!hasForbiddenSales && skuSystemStock == null) || (!hasForbiddenSales && skuSystemStock != null && skuSystemStock.intValue() > 0)) {
                                        isHit = true;
                                        errMsg = String.format("sku[%s]单品状态为[%s]，且SKU在SMT不禁售，库存+在途-待发[%s] 不允许下架", articleNumber, SingleItemEnum.getNameByEnName(skuStatus), skuSystemStock);
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            isHit = true;
                            errMsg = "产品下架判断异常" + e.getMessage();
                        }

                        if (isHit) {
                            excludeProductIdList.add(productId);
                            AliexpressProductLog log = new AliexpressProductLog();
                            log.setProductId(productId);
                            log.setAccountNumber(accountNumber);
                            log.setOperator(currentUser);
                            if (StringUtils.equalsIgnoreCase(currentUser, "admin-item-bad")) {
                                log.setNewRemark("商品竞争力不佳自动下架");
                            }
                            log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                            log.setOperateType(manipulateType == AliexpressProductManipulateTypeEnum.ONLINE.getType() ? AliexpressProductOperateLogType.ONLINE : AliexpressProductOperateLogType.OFFLINE);
                            log.setResult(false);
                            log.setFailInfo(errMsg);
                            aliexpressProductLogService.insert(log);
                        }
                    }
                }

                //去除不能下架的产品
                productIds.removeAll(excludeProductIdList);
                if (CollectionUtils.isEmpty(productIds)) {
                    return;
                }

                OfflineProductsOpenCall offlineCall = new OfflineProductsOpenCall();
                rsp = offlineCall.offlineProducts(saleAccountByAccountNumber, StringUtils.join(productIds, ";"));
            }

            if (rsp.isSuccess()) {
                String type = AliexpressProductManipulateTypeEnum.ONLINE.getType() == manipulateType ? ProductStatusTypeEnum.onSelling.getCode() : ProductStatusTypeEnum.offline.getCode();

                //修改本地产品上下架  最多30个产品 查询全数据
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setProductIdList(productIds);
                request.setQueryFields(null);
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                        .getEsAliexpressProductListing(request);
                esAliexpressProductListing.forEach(t -> t.setProductStatusType(type));
                esAliexpressProductListingService.saveAll(esAliexpressProductListing);
                // 链接跟踪
                productMonitorHelper.listingItemStatusListening(type, esAliexpressProductListing);

                if (optimized != null && optimized.length > 0) {
                    String s = optimized[0];
                    if (StringUtils.isNotBlank(s) && StringUtils.equalsIgnoreCase(s, "true") && manipulateType == AliexpressProductManipulateTypeEnum.OFFLINE.getType()) {
                        //需要删除待优化产品数据
                        AliexpressProductDiagnosisExample diagnosisExample = new AliexpressProductDiagnosisExample();
                        diagnosisExample.createCriteria().andProductIdIn(productIds);
                        List<AliexpressProductDiagnosis> aliexpressProductDiagnoses = aliexpressProductDiagnosisService.selectByExample(diagnosisExample);
                        if (CollectionUtils.isNotEmpty(aliexpressProductDiagnoses)) {
                            List<Integer> idList = aliexpressProductDiagnoses.stream().map(t -> t.getId()).collect(Collectors.toList());
                            aliexpressProductDiagnosisService.deleteByPrimaryKey(idList);
                        }
                    }
                }
            }

            AliexpressProductLogService aliexpressProductLogService = SpringUtils.getBean(AliexpressProductLogService.class);

            List<AliexpressProductLog> logs = new ArrayList<>();
            for (Long productId : productIds) {
                AliexpressProductLog log = new AliexpressProductLog();
                log.setProductId(productId);
                log.setAccountNumber(accountNumber);
                log.setOperator(currentUser);
                if (StringUtils.equalsIgnoreCase(currentUser, "admin-item-bad")) {
                    log.setNewRemark("商品竞争力不佳自动下架");
                }

                if (StringUtils.equalsIgnoreCase(currentUser, "admin-repeat")) {
                    log.setNewRemark("1个小时重复刊登系统自动下架");
                }

                if (StringUtils.equalsIgnoreCase(currentUser, "admin-item-infringement")) {
                    log.setNewRemark("侵权禁售系统自动下架");
                }

                if (StringUtils.equalsIgnoreCase(currentUser, "admin-tort")) {
                    log.setNewRemark("侵权-律所代理链接，系统自动下架");
                }

                log.setOperateTime(new Timestamp(System.currentTimeMillis()));
                log.setOperateType(manipulateType == AliexpressProductManipulateTypeEnum.ONLINE.getType() ? AliexpressProductOperateLogType.ONLINE : AliexpressProductOperateLogType.OFFLINE);
                log.setResult(rsp.isSuccess());
                if (!rsp.isSuccess()) {
                    log.setFailInfo(rsp.getMessage());
                }
                logs.add(log);
                aliexpressProductLogService.insert(log);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    @Override
    public ResponseJson updateTortProduct(SaleAccountAndBusinessResponse saleAccountByAccountNumber, AliexpressProduct product, String updateImg, String updateText) {
        Long productId = product.getProductId();
        log.warn(String.format("修改侵权产品id%s", productId));
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            JSONObject productEntity = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, productId);
            String updateDetail = updateText + "<br>" + "<img src=\"" + updateImg + "\" style=\"width:800px;\">";

            //更换新的图片
            String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, updateImg, null);

            // 多语言标题
            JSONArray subjectJSONArray = new JSONArray();
            JSONObject subjectJsonObject = new JSONObject();
            subjectJSONArray.add(subjectJsonObject);
            subjectJsonObject.put("value", updateText);
            subjectJsonObject.put("locale", "en_US");
            productEntity.put("subject_list", subjectJSONArray);

            productEntity.put("image_u_r_ls", imgUrlsWithPostedImg);

            updateDetail = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, updateDetail, null);

            // 多语言描述
            JSONArray detailJSONArray = new JSONArray();
            JSONObject detailJsonObject = new JSONObject();
            detailJSONArray.add(detailJsonObject);
            detailJsonObject.put("web_detail", AliexpressDetailUtils.getDetail(updateDetail));
            detailJsonObject.put("mobile_detail", AliexpressDetailUtils.getMobileDetail(updateDetail));
            detailJsonObject.put("locale", "en_US");
            productEntity.put("detail_source_list", detailJSONArray);

            String aeop_ae_product_s_k_us = JSON.toJSONString(productEntity.get("aeop_ae_product_s_k_us"));

            String newSkuProperty = UploadImageOpenCall
                    .postSkuPropertyImageReplate(saleAccountByAccountNumber, aeop_ae_product_s_k_us, updateImg, null);

            JSONArray newSkuPropertyJsonArray = JSONArray.parseArray(newSkuProperty);
            productEntity.put("aeop_ae_product_s_k_us", newSkuPropertyJsonArray);

            UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
            updatePriceEntity.setSeller(saleAccountByAccountNumber.getAccountNumber());

            rsp = OfferEditProductOpenCall.offerEditProduct(saleAccountByAccountNumber, productEntity.toJSONString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.EDIT_TORT, rsp);
        log.warn(String.format("修改侵权产品id%s end", productId));
        return rsp;
    }

    @Override
    public void updateProductCarType(AliexpressProduct aliexpressProduct, String json, String userName) {
        AliexpressProductLog log = new AliexpressProductLog();
        Long productId = aliexpressProduct.getProductId();
        log.setProductId(productId);
        log.setAccountNumber(aliexpressProduct.getAliexpressAccountNumber());
        log.setOperator(userName);
        log.setOperateTime(new Timestamp(System.currentTimeMillis()));
        log.setOperateType(AliexpressProductOperateLogType.EDIT_CAR_TYPE);
        try {
            //先同步数据，拿到最新产品属性信息
            String accountNum = aliexpressProduct.getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNum);
            SynchItemOpenCall call = new SynchItemOpenCall();

            //解析同步回来的数据，把车型库信息添加进去
            String response = call.getProduct(saleAccountByAccountNumber, productId);
            if (StringUtils.isNotBlank(response)) {
                JSONObject object = JSONObject.parseObject(response);
                if (object.containsKey("aliexpress_postproduct_redefining_findaeproductbyid_response")) {
                    JSONObject productRsp = object.getJSONObject(
                            "aliexpress_postproduct_redefining_findaeproductbyid_response");
                    if (productRsp.containsKey("result")) {

                        JSONObject obj = productRsp.getJSONObject("result");
                        if (obj.containsKey("aeop_ae_product_propertys")) {
                            JSONObject prodPerpJson = obj.getJSONObject("aeop_ae_product_propertys");
                            if (prodPerpJson.containsKey("aeop_ae_product_property")) {
                                String aeopAeProductPropertys = prodPerpJson
                                        .getString("aeop_ae_product_property");
                                aeopAeProductPropertys = aeopAeProductPropertys.replaceAll("\\\\\"", "&quot;");

                                //需要去除之前含有的车型库属性
                                String request = AliexpressBrandUtils.reMoveCarType(aeopAeProductPropertys);

                                request = StringUtils.replace(request, "[", " ");
                                request = StringUtils.replace(request, "]", " ");

                                //新的属性
                                request = "[" + json + "," + request + "]";

                                EditCategoryAttributesOpenCall editCall = new EditCategoryAttributesOpenCall();
                                ResponseJson responseJson = editCall
                                        .editCategoryAttributes(saleAccountByAccountNumber,
                                                String.valueOf(productId), request);

                                if (responseJson.isSuccess()) {
                                    log.setResult(true);
                                } else {
                                    log.setFailInfo(responseJson.getMessage());
                                    log.setResult(false);
                                }

                            }
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.setFailInfo(e.getMessage());
            log.setResult(false);
        }

        //保存日志
        aliexpressProductLogService.insert(log);
    }


    @Override
    public List<UpdatePriceEntity> updateProductCountryPriceNew(Map<String, List<AliexpressEditProductBean>> excelDataMap, String user, Long groupId, boolean isCheckAuth, String... isMysql) {
        // 需要修改的产品集合，key皆为productId
        Map<String, JSONObject> editProductEntityMap = new ConcurrentHashMap<>();
        Map<String, UpdatePriceEntity> editProductReturnMap = new ConcurrentHashMap<>();

        String userName = StringUtils.isNotBlank(user) ? user : WebUtils.getUserName();

        //产品id 对应的 日志
        Map<String, AliexpressProductLog> logMap = new HashMap<>();
        for (Map.Entry<String, List<AliexpressEditProductBean>> stringListEntry : excelDataMap.entrySet()) {
            AliexpressEditProductBean aliexpressEditProductBean = stringListEntry.getValue().get(0);
            AliexpressProductLog productLog = new AliexpressProductLog();

            productLog.setAccountNumber(aliexpressEditProductBean.getAccountNum());
            productLog.setSkuCode(aliexpressEditProductBean.getSkuCode());
            String productId = aliexpressEditProductBean.getProductId();
            productLog.setProductId(Long.valueOf(productId));
            productLog.setOperator(StringUtils.isNotBlank(userName) ? userName : WebUtils.getUserName());
            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            productLog.setOperateType(AliexpressProductOperateLogType.PRICE28_UPDATE);
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            productLog.setRuleName(aliexpressEditProductBean.getRuleName());
            aliexpressProductLogService.insert(productLog);
            logMap.put(productId, productLog);
        }

        long start = System.currentTimeMillis();
        log.warn("new速卖通63国改价--开始");

        boolean paramIsMySql = false;
        if (isMysql != null && isMysql.length > 0) {
            String s = isMysql[0];
            //产品搬家传入的参数
            if (StringUtils.isNotBlank(s) && StringUtils.equalsIgnoreCase(AliexpressStatePriceUtils.isMySql, s)) {
                paramIsMySql = true;
            }
        }

        final boolean finalIsMySql = paramIsMySql;

        CompletionService<ResponseJson> completionService = new ExecutorCompletionService<>(AliexpressExecutors.UPDATE_STATE_PRICE_POOL);
        int i = 0;
        int count = 0;
        int surplus_size = excelDataMap.size();
        for (Map.Entry<String, List<AliexpressEditProductBean>> itemData : excelDataMap.entrySet()) {
            try {
                DataContextHolder.setUsername(userName);
                i++;
                String productId = itemData.getKey();

                AliexpressExecutors.submitStatePriceUpdate2(completionService, rsp -> {
                    List<AliexpressEditProductBean> value = itemData.getValue();
                    List<EsAliexpressProductListing> esAliexpressProductListing = null;
                    if (finalIsMySql) {
                        List<AliexpressProductForAreaPrice> aliexpressProductForAreaPrices = aliexpressProductForAreaPriceService.selectByAccountAndProductId(value.get(0).getAccountNum(), Long.valueOf(productId));
                        esAliexpressProductListing = EsTranProductUtils.tranEsAliexpressProductListing(aliexpressProductForAreaPrices);
                    } else {
                        EsAliexpressProductListingRequest checkReqest = new EsAliexpressProductListingRequest();
                        checkReqest.setProductId(Long.valueOf(productId));
                        checkReqest.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                        checkReqest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());
                        esAliexpressProductListing = esAliexpressProductListingService
                                .getEsAliexpressProductListing(checkReqest);
                    }
                    Boolean sign = true;
                    if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                        UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                        updatePriceEntity.setSeller(value.get(0).getAccountNum());
                        updatePriceEntity.setProductId(value.get(0).getProductId());
                        updatePriceEntity.setSkuCode(value.get(0).getSkuCode());
                        updatePriceEntity.setTidbUniqueId(value.get(0).getUniqueId());
                        updatePriceEntity.setBatchId(value.get(0).getBatchId());
                        updatePriceEntity.setErrorTip("非在线、上架状态数据不可修改产品价格！");
                        editProductReturnMap.put(productId, updatePriceEntity);
                        sign = false;
                    } else {
                        List<String> skuIdList = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getSkuId)
                                .collect(Collectors.toList());
                        List<String> excelSkuIdList = value.stream().map(AliexpressEditProductBean::getSkuId)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(skuIdList)
                                || CollectionUtils.isEmpty(excelSkuIdList)
                                || skuIdList.size() != excelSkuIdList.size()
                        ) {
                            UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                            updatePriceEntity.setSeller(value.get(0).getAccountNum());
                            updatePriceEntity.setProductId(value.get(0).getProductId());
                            updatePriceEntity.setSkuCode(value.get(0).getSkuCode());
                            updatePriceEntity.setTidbUniqueId(value.get(0).getUniqueId());
                            updatePriceEntity.setBatchId(value.get(0).getBatchId());
                            updatePriceEntity.setErrorTip("传入数据不完整，或者有重复数据请检查！");
                            editProductReturnMap.put(productId, updatePriceEntity);
                            sign = false;
                        } else {
                            skuIdList.removeAll(excelSkuIdList);

                            if (CollectionUtils.isNotEmpty(skuIdList)) {
                                UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                                updatePriceEntity.setSeller(value.get(0).getAccountNum());
                                updatePriceEntity.setProductId(value.get(0).getProductId());
                                updatePriceEntity.setSkuCode(value.get(0).getSkuCode());
                                updatePriceEntity.setTidbUniqueId(value.get(0).getUniqueId());
                                updatePriceEntity.setBatchId(value.get(0).getBatchId());
                                updatePriceEntity.setErrorTip("传入数据不完整！" + StringUtils.join(skuIdList, ","));
                                editProductReturnMap.put(productId, updatePriceEntity);
                                sign = false;
                            }
                        }
                    }

                    //是否校验权限
                    if (isCheckAuth) {
                        String accountNum = value.get(0).getAccountNum();
                        ResponseJson responseJson1 = ExcelOperationUtils.authIntercept(accountNum, userName);
                        if (!responseJson1.isSuccess()) {
                            UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                            updatePriceEntity.setSeller(value.get(0).getAccountNum());
                            updatePriceEntity.setProductId(value.get(0).getProductId());
                            updatePriceEntity.setSkuCode(value.get(0).getSkuCode());
                            updatePriceEntity.setTidbUniqueId(value.get(0).getUniqueId());
                            updatePriceEntity.setBatchId(value.get(0).getBatchId());
                            updatePriceEntity.setErrorTip(responseJson1.getMessage());
                            editProductReturnMap.put(productId, updatePriceEntity);
                            sign = false;
                        }
                    }
                    if (sign) {
                        AliexpressStatePriceUtils.syncProductForUpdate(value, editProductEntityMap, editProductReturnMap);
                        UpdatePriceEntity updatePriceEntity = editProductReturnMap.get(productId);
                        JSONObject editProductData = editProductEntityMap.get(productId);

                        if (editProductData != null) {
                            //需要调整子sku价格
                            List<AliexpressEditProductBean> skuPriceList = value.stream().filter(t -> t.getSkuPrice() != null).collect(Collectors.toList());

                            Map<String, List<AliexpressEditProductBean>> skuIdMap = new HashMap<>();

                            if (CollectionUtils.isNotEmpty(skuPriceList)) {
                                skuIdMap = skuPriceList.stream().collect(Collectors.groupingBy(t -> t.getSkuId()));

                                JSONArray aeop_ae_product_s_k_us = editProductData.getJSONArray("aeop_ae_product_s_k_us");
                                List<JSONObject> jsonObjectList = new ArrayList<>();
                                for (int i1 = 0; i1 < aeop_ae_product_s_k_us.size(); i1++) {
                                    JSONObject jsonObject = aeop_ae_product_s_k_us.getJSONObject(i1);
                                    String id = jsonObject.getString("id");
                                    List<AliexpressEditProductBean> aliexpressEditProductBeans = skuIdMap.get(id);
                                    if (CollectionUtils.isNotEmpty(aliexpressEditProductBeans)) {
                                        Double skuPrice = aliexpressEditProductBeans.get(0).getSkuPrice();
                                        if (skuPrice != null) {
                                            jsonObject.put("sku_price", skuPrice);
                                        }
                                    }
                                    jsonObjectList.add(jsonObject);
                                }
                                editProductData.put("aeop_ae_product_s_k_us", JSON.toJSONString(jsonObjectList));
                            }

                            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, updatePriceEntity.getSeller());
                            String requestData = editProductData.toJSONString();
//                            log.info(requestData);

                            SmtTidbDataUpdateLog updateLog = new SmtTidbDataUpdateLog();

                            //记录修改请求数据
                            if (value.get(0).getIsTidb() != null && value.get(0).getIsTidb()) {
                                updateLog.setAccount(updatePriceEntity.getSeller());
                                updateLog.setProductId(Long.valueOf(productId));
                                updateLog.setRequestData(requestData);
                                updateLog.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                                smtTidbDataUpdateLogService.insert(updateLog);
                            }

//                            ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
//                            responseJson.setMessage("PRODUCT_IN_AUDIT_PENDING:Product is pending in audit process, please refresh the management page");
                            ResponseJson responseJson = OfferEditProductOpenCall
                                    .offerEditProduct(saleAccountByAccountNumber, requestData, user);
                            if (responseJson.isSuccess()) {

                                if (updateLog.getId() != null) {
                                    updateLog.setUploadState(1);
                                    smtTidbDataUpdateLogService.updateByPrimaryKeySelective(updateLog);
                                }

                                // 修改产品分组
                                ResponseJson groupIdResponse = null;
                                if (null != groupId) {
                                    groupIdResponse = new GroupOpenCall().setGroup(saleAccountByAccountNumber, productId, groupId.toString());
                                }

                                AliexpressEsExtend extend = this
                                        .selectByAccountandProductId(saleAccountByAccountNumber.getAccountNumber(),
                                                Long.valueOf(productId));
                                extend.setAeopNationalQuoteConfiguration(
                                        editProductData.getString("aeop_national_quote_configuration"));
                                this.updateByPrimaryKeySelective(extend);

                                //更新 是否区域调价字段
                                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                                request.setProductId(Long.valueOf(productId));
                                request.setAliexpressAccountNumber(updatePriceEntity.getSeller());
                                request.setQueryFields(new String[]{"id", "skuId"});
                                List<EsAliexpressProductListing> listing = esAliexpressProductListingService
                                        .getEsAliexpressProductListing(request);

                                boolean isRegionPrice = StringUtils.isNotBlank(extend.getAeopNationalQuoteConfiguration()) ? true : false;
                                if (CollectionUtils.isEmpty(listing)) {
                                    //需要在同步一次，说明es还未刷新进来
                                    //同步产品
                                    SynchItemOpenCall call = new SynchItemOpenCall();
                                    call.syncAliexpressProductInfo(saleAccountByAccountNumber, Long.valueOf(productId));
                                } else {
                                    for (EsAliexpressProductListing aliexpressProductListing : listing) {
                                        aliexpressProductListing.setIsRegionPrice(isRegionPrice);
                                        if (null != groupIdResponse && groupIdResponse.isSuccess()) {
                                            aliexpressProductListing.setGroupId(groupId);
                                        }

                                        String skuId = aliexpressProductListing.getSkuId();
                                        List<AliexpressEditProductBean> aliexpressEditProductBeans = skuIdMap.get(skuId);
                                        //说明修改了价格
                                        if (CollectionUtils.isNotEmpty(aliexpressEditProductBeans) && aliexpressEditProductBeans.get(0).getSkuPrice() != null) {
                                            Double skuPrice = aliexpressEditProductBeans.get(0).getSkuPrice();
                                            aliexpressProductListing.setSkuPrice(skuPrice);
                                        }
                                        Long templateId = value.get(0).getTemplateId();
                                        if (templateId != null) {
                                            aliexpressProductListing.setFreightTemplateId(templateId);
                                        }
                                        esAliexpressProductListingService.updateRequest(aliexpressProductListing);
//                                    esAliexpressProductListingService.save(aliexpressProductListing);
                                    }
                                }
//                                esAliexpressProductListingService.saveAll(listing);
                                //目前admin用户修改 只有亏损订单定时任务 更新亏损订单 是否调价字段
                                if (StringUtils.equalsIgnoreCase(userName, "admin")) {
                                    AliexpressDeficitOrderExample deficitOrderExample = new AliexpressDeficitOrderExample();
                                    deficitOrderExample.createCriteria()
                                            .andAccountNumberEqualTo(updatePriceEntity.getSeller())
                                            .andProductIdEqualTo(Long.valueOf(productId))
                                            .andModifyPriceEqualTo(DeficitOrderPriceStatusEnum.ZEOR.getCode());
                                    List<AliexpressDeficitOrder> aliexpressDeficitOrders = aliexpressDeficitOrderService
                                            .selectByExample(deficitOrderExample);
                                    if (CollectionUtils.isNotEmpty(aliexpressDeficitOrders)) {
                                        for (AliexpressDeficitOrder aliexpressDeficitOrder : aliexpressDeficitOrders) {
                                            aliexpressDeficitOrder.setModifyPrice(DeficitOrderPriceStatusEnum.ONE.getCode());
                                            aliexpressDeficitOrderService.updateByPrimaryKeySelective(aliexpressDeficitOrder);
                                        }
                                    }
                                }

                                if (null == groupIdResponse || groupIdResponse.isSuccess()) {
                                    updatePriceEntity.setErrorTip("修改成功");
                                } else {
                                    updatePriceEntity.setErrorTip("价格修改成功，产品分组修改失败：" + groupIdResponse.getMessage());
                                }
                            } else {
                                if (updateLog.getId() != null) {
                                    updateLog.setUploadState(0);
                                    smtTidbDataUpdateLogService.updateByPrimaryKeySelective(updateLog);
                                }
                                updatePriceEntity.setErrorTip(responseJson.getMessage());
                            }

                            AliexpressProductLog productLog = logMap.get(value.get(0).getProductId());
                            productLog.setResult(responseJson.isSuccess());
                            productLog.setFailInfo(updatePriceEntity.getErrorTip());
                            productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
//                            aliexpressProductLogService.product28Update(updatePriceEntity, userName);

                            //如果是状态审核原因 需要加入重试日志表  http://************:8080/browse/ES-10962
                            if (finalIsMySql) {
                                smtAearPriceReLogService.haldLog(saleAccountByAccountNumber.getAccountNumber(),
                                        Long.valueOf(productId), JSON.toJSONString(excelDataMap), responseJson, productLog);
                            }
                            String tidbUniqueId = updatePriceEntity.getTidbUniqueId();
                            if (StringUtils.isNotBlank(tidbUniqueId)) {
                                TidbAreaResult tidbAreaResult = new TidbAreaResult();
                                tidbAreaResult.setTidbUniqueId(tidbUniqueId);
                                tidbAreaResult.setResult(responseJson.isSuccess());
                                tidbAreaResult.setFailInfo(updatePriceEntity.getErrorTip());
                                rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_TIDB_AREA_RESULT_ROUTE_KEY, JSON.toJSON(tidbAreaResult));
                            }

                            String batchId = updatePriceEntity.getBatchId();
                            if (StringUtils.isNotBlank(batchId)) {
                                // 批量修改
                                UpdateWrapper<SmtAdjustPriceItemPool> updateWrapper = new UpdateWrapper<>();
                                LambdaUpdateWrapper<SmtAdjustPriceItemPool> lambdaUpdateWrapper = updateWrapper.lambda();
                                lambdaUpdateWrapper.eq(SmtAdjustPriceItemPool::getBatchId, batchId);
                                lambdaUpdateWrapper.set(SmtAdjustPriceItemPool::getUpdatePriceStatus, responseJson.isSuccess() ? AdjustPriceStatusEnum.SUCCESS.getCode() : AdjustPriceStatusEnum.FAIL.getCode());
                                lambdaUpdateWrapper.set(SmtAdjustPriceItemPool::getRemark, updatePriceEntity.getErrorTip());
                                smtAdjustPriceItemPoolService.update(new SmtAdjustPriceItemPool(), lambdaUpdateWrapper);
                            }

                        }
                    } else {
                        UpdatePriceEntity updatePriceEntity = editProductReturnMap.get(value.get(0).getProductId());
                        AliexpressProductLog productLog = logMap.get(value.get(0).getProductId());
                        productLog.setResult(false);
                        productLog.setFailInfo(updatePriceEntity.getErrorTip());
                        productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                        productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                        aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
//                            aliexpressProductLogService.product28Update(updatePriceEntity1, userName);

                        //如果是状态审核原因 需要加入重试日志表  http://************:8080/browse/ES-10962
                        if (finalIsMySql) {
                            ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
                            responseJson.setMessage(updatePriceEntity.getErrorTip());
                            smtAearPriceReLogService.haldLog(updatePriceEntity.getSeller(),
                                    Long.valueOf(productId), JSON.toJSONString(excelDataMap), responseJson, productLog);
                        }
                        String tidbUniqueId = updatePriceEntity.getTidbUniqueId();
                        if (StringUtils.isNotBlank(tidbUniqueId)) {
                            TidbAreaResult tidbAreaResult = new TidbAreaResult();
                            tidbAreaResult.setTidbUniqueId(tidbUniqueId);
                            tidbAreaResult.setResult(false);
                            tidbAreaResult.setFailInfo(updatePriceEntity.getErrorTip());
                            rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_TIDB_AREA_RESULT_ROUTE_KEY, JSON.toJSON(tidbAreaResult));
                        }

                        String batchId = updatePriceEntity.getBatchId();
                        if (StringUtils.isNotBlank(batchId)) {
                            // 批量修改
                            UpdateWrapper<SmtAdjustPriceItemPool> updateWrapper = new UpdateWrapper<>();
                            LambdaUpdateWrapper<SmtAdjustPriceItemPool> lambdaUpdateWrapper = updateWrapper.lambda();
                            lambdaUpdateWrapper.eq(SmtAdjustPriceItemPool::getBatchId, batchId);
                            lambdaUpdateWrapper.set(SmtAdjustPriceItemPool::getUpdatePriceStatus, AdjustPriceStatusEnum.FAIL.getCode());
                            lambdaUpdateWrapper.set(SmtAdjustPriceItemPool::getRemark, updatePriceEntity.getErrorTip());
                            smtAdjustPriceItemPoolService.update(new SmtAdjustPriceItemPool(), lambdaUpdateWrapper);
                        }
                    }
                });

                if (i >= 200) {
                    try {
                        completionService.take().get();
                        count++;
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        int left = surplus_size - count;
        try {
            for (i = 0; i < left; i++) {
                completionService.take().get();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        long updateEnd = System.currentTimeMillis();
        log.warn("new速卖通63国改价--调用接口改价：数据" + editProductEntityMap.size() + "条，耗时：" + (updateEnd - start) / 1000 + "秒");

        List<UpdatePriceEntity> returnResultList = new ArrayList<>();
        if (!editProductReturnMap.isEmpty()) {
            returnResultList = new ArrayList<>(editProductReturnMap.values());
        }
        return returnResultList;
    }

    public void increaseEprFee(List<EsAliexpressProductListing> esAliexpressProductListing, Map<String, List<BatchPriceCalculatorResponse>> map) {
        // 根据账号分组
        Map<String, List<EsAliexpressProductListing>> accountToListingMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getAliexpressAccountNumber));
        for (String account : accountToListingMap.keySet()) {
            List<EsAliexpressProductListing> esAliexpressProductListings = accountToListingMap.get(account);

            // 判断当前账号是否需要添加EPR费用
            AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(account);
            if (BooleanUtils.isNotTrue(aliexpressConfig.getAutoAddEprFee())) {
                continue;
            }

            // 是否是CNY店铺
            Boolean cny = aliexpressConfig.getCny();

            // 是否加入商品及物流包装环保费
            Boolean addEprPackFee = aliexpressConfig.getAddEprPackFee();

            // 根据产品id分组
            Map<Long, List<EsAliexpressProductListing>> productIdToListingMap = esAliexpressProductListings.stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));
            for (Long productId : productIdToListingMap.keySet()) {
                List<EsAliexpressProductListing> productListings = productIdToListingMap.get(productId);
                executeIncreaseEprFee(productListings, map, cny, addEprPackFee);
            }
        }
    }

    private void executeIncreaseEprFee(List<EsAliexpressProductListing> productListings, Map<String, List<BatchPriceCalculatorResponse>> map, Boolean cny, Boolean addEprPackFee) {
        // 获取EPR收费标准
        List<EsAliexpressEprEcoFee> eprEcoFeeList = aliexpressEprEcoFeeEsService
                .findByAccountProductId(productListings.get(0).getAliexpressAccountNumber(), productListings.get(0).getProductId().toString());
        if (CollectionUtils.isEmpty(eprEcoFeeList)) {
            return;
        }

        //按照时间倒序，取最新的数据
        eprEcoFeeList = eprEcoFeeList.stream().sorted(Comparator.comparing(EsAliexpressEprEcoFee::getCreatedTime).reversed()).collect(Collectors.toList());
        List<EprEcoContributionsInfo> feeInfoList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        for (EsAliexpressEprEcoFee eprEcoFee : eprEcoFeeList) {
            List<EprEcoContributionsInfo> eprEcoContributionsInfoList = eprEcoFee.getEprEcoContributionsInfoList();
            for (EprEcoContributionsInfo eprEcoContributionsInfo : eprEcoContributionsInfoList) {
                String destCountryName = eprEcoContributionsInfo.getDestCountryName();
                String eprCateName = eprEcoContributionsInfo.getEprCateName();
                String feeTypeName = eprEcoContributionsInfo.getFeeTypeName();
                String key = destCountryName + eprCateName + feeTypeName;
                if (!keySet.contains(key)) {
                    feeInfoList.add(eprEcoContributionsInfo);
                    keySet.add(key);
                }
            }
//            feeInfoList.addAll(eprEcoContributionsInfoList);
        }
        if (CollectionUtils.isEmpty(feeInfoList)) {
            return;
        }

        // 过滤指定收费类型
        if (addEprPackFee != null && !addEprPackFee) {
            feeInfoList = feeInfoList.stream().filter(o -> !excludeFeeTypeList.contains(o.getFeeTypeName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(feeInfoList)) {
                return;
            }
        }

        // 根据国家 环保品类 收费类型去重，取最新的数据
        feeInfoList = feeInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(o -> o.getDestCountryName() + "#" + o.getEprCateName() + "#" + o.getFeeTypeName()))), ArrayList::new));
        Map<String, List<EprEcoContributionsInfo>> countryToInfoMap = feeInfoList.stream().collect(Collectors.groupingBy(EprEcoContributionsInfo::getDestCountryName));

        for (String country : countryToInfoMap.keySet()) {
            List<EprEcoContributionsInfo> infoList = countryToInfoMap.get(country);

            // 获取销售目的国
            String EprCountry = EprFeeCountryEnum.getCodeByRealName(country);
            if (StringUtils.isEmpty(EprCountry)) {
                throw new RuntimeException("获取EPR费用时国家code解析失败，请联系技术人员");
            }

            // 获取币种
            String currency = EprFeeCountryEnum.getCurrency(country, infoList.get(0).getBillingPriceText());
            if (StringUtils.isBlank(currency)) {
                throw new RuntimeException("获取EPR费用时币种解析失败，请联系技术人员");
            }

            // 获取EPR费用
            Double eprPrice = 0.0;
            for (EprEcoContributionsInfo info : infoList) {
                //需要异常 当0处理
                Double price = 0.0;
                try {
                    price = EprFeeCountryEnum.getPrice(country, info.getBillingPriceText());
                    if (null == price) {
                        price = 0.0;
//                        throw new RuntimeException("获取EPR费用时价格解析失败，请联系技术人员");
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                eprPrice += price;
            }

            // EPR费用汇率转换
            Double rate;
            ApiResult<Double> rateResult;
            if (BooleanUtils.isTrue(cny)) {
                rateResult = PriceCalculatedUtil.getExchangeRate(currency, CurrencyConstant.CNY);
            } else {
                rateResult = PriceCalculatedUtil.getExchangeRate(currency, CurrencyConstant.USD);
            }
            if (rateResult.isSuccess()) {
                rate = rateResult.getResult();
            } else {
                throw new RuntimeException(String.format("计算EPR费用时获取汇率失败：%s", rateResult.getErrorMsg()));
            }
            eprPrice = NumberUtils.round(eprPrice * rate, 2);

            for (EsAliexpressProductListing productListing : productListings) {
                String id = productListing.getId();
                List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = map.get(id);

                for (BatchPriceCalculatorResponse response : batchPriceCalculatorResponses) {
                    String countryCode = response.getCountryCode();
                    Double foreignPrice = response.getForeignPrice();
                    if (!response.getIsSuccess() || !EprCountry.equals(countryCode) || null == foreignPrice) {
                        continue;
                    }

                    response.setForeignPrice(foreignPrice + eprPrice / 0.9);
                }
            }
        }
    }

    @Override
    public List<AliexpressProductStatePriceEntity> getStateResultList(EsAliexpressProductListingRequest request) {
        Map<Long, List<AliexpressProductStatePriceEntity>> resultMap = new HashMap<>();

        final int maxDownload = 500000;
        List<EsAliexpressProductListing> esAliexpressProductListing = new ArrayList<>();

        if (StringUtils.isBlank(request.getIdStr())) {
            List<String> accountNumbers = CommonUtils.splitList(request.getAliexpressAccountNumber(), ",");
            List<String> managerIds = CommonUtils.splitList(request.getSalesSupervisorName(), ",");
            List<String> leaderIds = CommonUtils.splitList(request.getSalemanagerLeader(), ",");
            List<String> saleIds = CommonUtils.splitList(request.getSalemanager(), ",");
            List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, null, null, false);
            request.setAliexpressAccountNumber(StringUtils.join(authAccountNumbers, ","));
        }

        int offset = 0;
        int limit = 1000;
        while (true) {
            request.setPageFields(request.getQueryFields());
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, limit, offset);
            if (page == null || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            if (limit * (offset + 1) > maxDownload) {
                throw new RuntimeException(" 超过最大导出数量" + maxDownload);
            }
            List<EsAliexpressProductListing> reslultList = page.getContent();
            esAliexpressProductListing.addAll(reslultList);
            offset++;
        }
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return null;
        }
        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            if (resultMap.containsKey(esProduct.getProductId())) {
                continue;
            }
            List<AliexpressProductStatePriceEntity> stateEntityList = new ArrayList<>();
            try {
                stateEntityList = AliexpressStatePriceUtils.getStateEntityListForEs(esProduct);
            } catch (Exception e) {
                log.error(e.getMessage());
                AliexpressProductStatePriceEntity errorEntity = new AliexpressProductStatePriceEntity();
                errorEntity.setAccountNumber(esProduct.getAliexpressAccountNumber());
                errorEntity.setProductId(esProduct.getProductId());
                errorEntity.setPriceType(e.getMessage());
                stateEntityList.add(errorEntity);
            }
            if (CollectionUtils.isNotEmpty(stateEntityList)) {
                resultMap.put(esProduct.getProductId(), stateEntityList);
            }
        }
        List<AliexpressProductStatePriceEntity> resultList = new ArrayList<>();
        if (!resultMap.isEmpty()) {
            for (Map.Entry<Long, List<AliexpressProductStatePriceEntity>> item : resultMap.entrySet()) {
                resultList.addAll(item.getValue());
            }
        }
        resultList.removeAll(Collections.singleton(null));
        return resultList;
    }

    @Override
    public ResponseJson product28Calc(List<EsAliexpressProductListing> esAliexpressProductListing, Map<String, AliexpressEsExtend> esExtendMap, String logisticsName, String createBy, Double gross, List<String> updateCountryCodeList, Boolean isCalcPrice) throws Exception {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        List<Future<ResponseJson>> futureList = new ArrayList<>();
        List<Map<String, List<BatchPriceCalculatorResponse>>> listMap = new ArrayList<>();

        //需要拆分请求
        List<List<EsAliexpressProductListing>> lists = PagingUtils.pagingList(esAliexpressProductListing, 20);

        for (List<EsAliexpressProductListing> list : lists) {

            //通过毛利计算价格  gross可能为空，但是用物流名称配置的毛利计算价格
            if ((isCalcPrice != null && isCalcPrice) || gross != null) {
                Future<ResponseJson> responseJsonFuture = AliexpressExecutors.calcProduct28(rsp -> {
                    ResponseJson responseJson = AliexpressCalcPriceUtil.product28CalcForEs(list, logisticsName, createBy, gross, updateCountryCodeList);

                    if (responseJson.isSuccess()) {
                        rsp.getBody().put(AliexpressCalcPriceUtil.key, responseJson
                                .getBody().get(AliexpressCalcPriceUtil.key));
                    } else {
                        log.warn("毛利计算出错：" + responseJson.getMessage());
                    }
                    rsp.setStatus(responseJson.getStatus());
                    rsp.setMessage(responseJson.getMessage());
                });

                futureList.add(responseJsonFuture);
            } else {

                Future<ResponseJson> responseJsonFuture = AliexpressExecutors.checkProduct28(rsp -> {
                    ResponseJson responseJson = AliexpressCalcPriceUtil.product28CheckForEs(list, esExtendMap, logisticsName, createBy);

                    if (responseJson.isSuccess()) {
                        rsp.getBody().put(AliexpressCalcPriceUtil.key, responseJson
                                .getBody().get(AliexpressCalcPriceUtil.key));
                    } else {
                        log.warn("毛利计算出错：" + responseJson.getMessage());
                    }
                    rsp.setStatus(responseJson.getStatus());
                    rsp.setMessage(responseJson.getMessage());
                });

                futureList.add(responseJsonFuture);

            }
        }

        for (Future<ResponseJson> responseJsonFuture : futureList) {
            ResponseJson responseJson = responseJsonFuture.get(5, TimeUnit.MINUTES);

            if (responseJson.isSuccess()) {
                Map<String, List<BatchPriceCalculatorResponse>> resultMap = (Map<String, List<BatchPriceCalculatorResponse>>) responseJson.getBody().get(AliexpressCalcPriceUtil.key);
                listMap.add(resultMap);
            } else {
                response.setStatus(responseJson.getStatus());
                response.setMessage(responseJson.getMessage());
                return response;
            }
        }

        if (!listMap.isEmpty()) {
            Map<String, List<BatchPriceCalculatorResponse>> map = com.estone.erp.common.util.CommonUtils.mergeMaps(listMap);
            response.setStatus(StatusCode.SUCCESS);
            response.getBody().put(AliexpressCalcPriceUtil.key, map);
        }
        return response;
    }

    @Override
    public ResponseJson updateProductSize(ProductSizeBean productSizeBean, SaleAccountAndBusinessResponse saleAccountAndBusinessResponse) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        String accountNumber = productSizeBean.getAliexpressAccountNumber();
        Long productId = productSizeBean.getProductId();
        String sku = productSizeBean.getSku();
        String packingType = productSizeBean.getPackingType();
        if (StringUtils.isEmpty(accountNumber) || StringUtils.isEmpty(sku) || StringUtils.isEmpty(packingType) || productId == null) {
            rsp.setMessage("参数为空！");
            return rsp;
        }
        Integer packageLength = productSizeBean.getPackageLength();
        Integer packageWidth = productSizeBean.getPackageWidth();
        Integer packageHeight = productSizeBean.getPackageHeight();

        //实际尺寸
        if (StringUtils.equalsIgnoreCase(packingType, "1")) {
            String mainSku = ProductUtils.getMainSku(sku);
            ResponseJson spuInfoRsp = ProductUtils.findSpuInfo(mainSku);
            if (!spuInfoRsp.isSuccess()) {
                rsp.setMessage(spuInfoRsp.getMessage());
                return rsp;
            }
            List<SpuInfo> spuInfos = (List<SpuInfo>) spuInfoRsp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isEmpty(spuInfos)) {
                rsp.setMessage(String.format("spu:%s 查询信息为空！", mainSku));
                return rsp;
            }
            SpuInfo spuInfo = spuInfos.get(0);
            BigDecimal length = spuInfo.getLength();
            BigDecimal wide = spuInfo.getWide();
            BigDecimal height = spuInfo.getHeight();
            if (length != null && wide != null && height != null) {
                packageLength = length.setScale(0, BigDecimal.ROUND_UP).intValue();
                packageWidth = wide.setScale(0, BigDecimal.ROUND_UP).intValue();
                packageHeight = height.setScale(0, BigDecimal.ROUND_UP).intValue();
            } else {
                rsp.setMessage(String.format("spu:%s 长宽高数据不全！", mainSku));
                return rsp;
            }
        }
        if (packageLength == null || packageWidth == null || packageHeight == null) {
            rsp.setMessage(String.format("sku:%s 长宽高数据不全！", sku));
            return rsp;
        }
        EditSimpleproductfiledOpenCall editCall = new EditSimpleproductfiledOpenCall();
        String lengthResponse = editCall
                .setEditsimpleproductfiled(saleAccountAndBusinessResponse, productId.toString(),
                        "packageLength", packageLength.toString());
        try {
            Thread.sleep(1000L);
        } catch (Exception e) {

        }
        String widthResponse = editCall
                .setEditsimpleproductfiled(saleAccountAndBusinessResponse, productId.toString(),
                        "packageWidth", packageWidth.toString());
        try {
            Thread.sleep(1000L);
        } catch (Exception e) {

        }
        String heightResponse = editCall
                .setEditsimpleproductfiled(saleAccountAndBusinessResponse, productId.toString(),
                        "packageHeight", packageHeight.toString());
        ResponseJson updateLengthResponse = editCall.checkErrorMessage(lengthResponse);
        ResponseJson updateWidthResponse = editCall.checkErrorMessage(widthResponse);
        ResponseJson updateHeightResponse = editCall.checkErrorMessage(heightResponse);
        // 综合处理多次请求结果结果合并一条记录
        if (StatusCode.SUCCESS.equals(updateLengthResponse.getStatus()) && StatusCode.SUCCESS.equals(updateWidthResponse.getStatus())
                && StatusCode.SUCCESS.equals(updateHeightResponse.getStatus())) {
            rsp.setStatus(StatusCode.SUCCESS);
        }
        String message = "";
        if (StringUtils.isNotBlank(updateLengthResponse.getMessage())) {
            message += updateLengthResponse.getMessage();
        }
        if (StringUtils.isNotBlank(updateWidthResponse.getMessage())) {
            message += updateWidthResponse.getMessage();
        }
        if (StringUtils.isNotBlank(updateHeightResponse.getMessage())) {
            message += updateHeightResponse.getMessage();
        }
        rsp.setMessage(message);
        return rsp;
    }

    @Override
    public List<UpdatePriceEntity> excelDeleteProduct(String[] headers, MultipartFile file, String userName) {
        List<UpdatePriceEntity> returnResultList = new ArrayList<>();
        Set<String> productIdSet = new HashSet<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                if (ExcelUtils.isNotBlankCell(row.getCell(0)) && ExcelUtils.isNotBlankCell(row.getCell(1))) {
                    UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                    try {
                        String accountNum = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        updatePriceEntity.setSeller(accountNum);
                        String productId = ExcelUtils.getCellValue(row.getCell(1)).trim();
                        updatePriceEntity.setProductId(productId);
                    } catch (Exception e) {
                        updatePriceEntity.setErrorTip("数据有误！");
                    }
                    if (productIdSet.isEmpty() || !productIdSet.contains(updatePriceEntity.getProductId())) {
                        returnResultList.add(updatePriceEntity);
                        productIdSet.add(updatePriceEntity.getProductId());
                    }

                }
                return row;
            }, false);
        } catch (Exception e) {
            throw new RuntimeException("速卖通---删除产品解析excel报错");
        }
        //执行
        excelDeleteProduct(returnResultList, userName);
        return returnResultList;
    }

    @Override
    public List<UpdatePriceEntity> excelUpdateGrossWeight(String[] headers, MultipartFile file, String userName) {
        List<UpdatePriceEntity> returnResultList = new ArrayList<UpdatePriceEntity>();
        Set<String> productIdSet = new HashSet<>();
        Map<String, String> productWeightMap = new HashMap<String, String>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                if (ExcelUtils.isNotBlankCell(row.getCell(0)) && ExcelUtils.isNotBlankCell(row.getCell(1)) && ExcelUtils.isNotBlankCell(row.getCell(2))) {
                    UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                    try {
                        String accountNum = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        updatePriceEntity.setSeller(accountNum);
                        String productId = ExcelUtils.getCellValue(row.getCell(1)).trim();
                        updatePriceEntity.setProductId(productId);
                        String grossWeight = String.valueOf(ExcelUtils.getDoubleCellValue(row.getCell(2), "0.000"));
                        productWeightMap.put(productId, grossWeight);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        updatePriceEntity.setErrorTip("数据有误！");
                    }
                    if (productIdSet.isEmpty() || !productIdSet.contains(updatePriceEntity.getProductId())) {
                        returnResultList.add(updatePriceEntity);
                        productIdSet.add(updatePriceEntity.getProductId());
                    }

                }
                return row;
            }, false);
        } catch (Exception e) {
            throw new RuntimeException("速卖通---改重量解析excel报错");
        }
        //执行
        updateGrossWeight(returnResultList, productWeightMap, userName, true);
        return returnResultList;
    }

    /**
     * excle 修改运费模板
     *
     * @param headers
     * @param file
     * @param userName
     * @return
     */
    @Override
    public List<UpdatePriceEntity> updateFreightTemplateIdForExcle(String[] headers, MultipartFile file, String userName) {
        List<UpdatePriceEntity> returnResultList = new ArrayList<>();
        Set<String> productIdSet = new HashSet<>();

        //产品id 对应的运费模板中文名称
        Map<String, String> productTempMap = new HashMap<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                if (ExcelUtils.isNotBlankCell(row.getCell(0)) && ExcelUtils.isNotBlankCell(row.getCell(1)) && ExcelUtils.isNotBlankCell(row.getCell(2))) {
                    UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                    try {
                        String accountNum = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        updatePriceEntity.setSeller(accountNum);
                        String productId = ExcelUtils.getCellValue(row.getCell(1)).trim();
                        updatePriceEntity.setProductId(productId);
                        String freightTempId = ExcelUtils.getCellValue(row.getCell(2)).trim();
                        productTempMap.put(productId, freightTempId);
                    } catch (Exception e) {
                        log.error("解析修改运费模板异常:" + e.getMessage(), e);
                        updatePriceEntity.setErrorTip("解析修改运费模板异常:" + e.getMessage());
                    }
                    if (productIdSet.isEmpty() || !productIdSet.contains(updatePriceEntity.getProductId())) {
                        returnResultList.add(updatePriceEntity);
                        productIdSet.add(updatePriceEntity.getProductId());
                    }
                }
                return row;
            }, false);
        } catch (Exception e) {
            throw new RuntimeException("速卖通---改运费模板解析excel报错");
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        //存放 账号对应的运费
//        Map<String, Map<String, Long>> freightTempMap = new HashMap<>();

        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productTempMap.size());

        for (UpdatePriceEntity updatePriceEntity : returnResultList) {
            AliexpressExecutors.updateTemplate((rsp) -> {
                String seller = updatePriceEntity.getSeller();
                String productId = updatePriceEntity.getProductId();
                String freightTempId = productTempMap.get(productId);
                ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

                try {
                    SaleAccountAndBusinessResponse account = accountMap
                            .get(seller);
                    if (null == account) {
                        account = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, seller);

                        if (account != null) {
                            accountMap.put(seller, account);
//                            Map<String, Long> stringLongMap = freightTempMap.get(seller);
//                            if(MapUtils.isEmpty(stringLongMap)){
//                                FreightTemplateCall call = new FreightTemplateCall();
//                                List<AliexpressFreightTemplate> freightTemplateList = call.getFreightTemplateList(account);
//                                if(CollectionUtils.isNotEmpty(freightTemplateList)){
//                                    Map<String, Long> collect = freightTemplateList.stream()
//                                            .collect(Collectors.toMap(e -> e.getTemplateName(), e -> e.getTemplateId()));
//                                    freightTempMap.put(seller, collect);
//                                }
//                            }
                        }
                    }
                    if (account == null) {
                        updatePriceEntity.setErrorTip("查询不到店铺信息！");
                        responseJson.setMessage(updatePriceEntity.getErrorTip());
                        return;
                    }

//                    Map<String, Long> stringLongMap = freightTempMap.get(seller);
//
//                    if(MapUtils.isEmpty(stringLongMap)){
//                        updatePriceEntity.setErrorTip("运费模板接口获取异常！");
//                        responseJson.setMessage(updatePriceEntity.getErrorTip());
//                        return;
//                    }
//
//                    Long aLong = stringLongMap.get(freightTempName);
//                    if(aLong == null){
//                        updatePriceEntity.setErrorTip("运费模板不存在");
//                        responseJson.setMessage(updatePriceEntity.getErrorTip());
//                        return;
//                    }

                    try {
                        EditSimpleproductfiledOpenCall call = new EditSimpleproductfiledOpenCall();
                        String callRspStr = call.setEditsimpleproductfiled(account,
                                productId, "freightTemplateId", freightTempId);
                        log.warn(String.format("修改单个字段 产品id[%s] + 请求结果[%s]", productId, callRspStr));
                        responseJson = call.checkErrorMessage(callRspStr);

                    } catch (Exception e) {
                        updatePriceEntity.setErrorTip(e.getMessage());
                        responseJson.setMessage(e.getMessage());
                    }

                    if (responseJson.isSuccess()) {
                        updatePriceEntity.setErrorTip("修改成功！");

                        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                        request.setProductId(Long.valueOf(productId));
                        request.setQueryFields(new String[]{"id"});
                        List<EsAliexpressProductListing> productListing = esAliexpressProductListingService
                                .getEsAliexpressProductListing(request);

                        for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                            aliexpressProductListing.setFreightTemplateId(Long.valueOf(freightTempId));
                            esAliexpressProductListingService.updateRequest(aliexpressProductListing);
                        }
//                        esAliexpressProductListingService.saveAll(productListing);
                    } else {
                        updatePriceEntity.setErrorTip(responseJson.getMessage());
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    updatePriceEntity.setErrorTip(e.getMessage());
                    responseJson.setMessage(e.getMessage());
                } finally {
                    countDownLatch.countDown();
                    AliexpressProductLog log = new AliexpressProductLog();
                    log.setAccountNumber(seller);
                    log.setOperateType(OperateLogTypeEnum.update_freight_template_id.getCode());
                    log.setOperator(userName);
                    log.setResult(responseJson.isSuccess());
                    log.setProductId(Long.valueOf(productId));
                    log.setFailInfo(responseJson.getMessage());
                    aliexpressProductLogService.insert(log);
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        return returnResultList;
    }

    /**
     * excle 修改产品分组
     *
     * @param headers
     * @param file
     * @param userName
     * @return
     */
    @Override
    public List<UpdatePriceEntity> updateGroupIdForExcle(String[] headers, MultipartFile file, String userName) {
        List<UpdatePriceEntity> returnResultList = new ArrayList<>();
        Set<String> productIdSet = new HashSet<>();

        //产品id 对应的产品分组id英文逗号拼接
        Map<String, String> productGroupMap = new HashMap<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                if (ExcelUtils.isNotBlankCell(row.getCell(0)) && ExcelUtils.isNotBlankCell(row.getCell(1)) && ExcelUtils.isNotBlankCell(row.getCell(2))) {
                    UpdatePriceEntity updatePriceEntity = new UpdatePriceEntity();
                    try {
                        String accountNum = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        updatePriceEntity.setSeller(accountNum);
                        String productId = ExcelUtils.getCellValue(row.getCell(1)).trim();
                        updatePriceEntity.setProductId(productId);
                        String groupId = ExcelUtils.getCellValue(row.getCell(2)).trim();
                        productGroupMap.put(productId, groupId);
                    } catch (Exception e) {
                        log.error("解析修改产品分组异常:" + e.getMessage(), e);
                        updatePriceEntity.setErrorTip("解析修改产品分组异常:" + e.getMessage());
                    }
                    if (productIdSet.isEmpty() || !productIdSet.contains(updatePriceEntity.getProductId())) {
                        returnResultList.add(updatePriceEntity);
                        productIdSet.add(updatePriceEntity.getProductId());
                    }
                }
                return row;
            }, false);
        } catch (Exception e) {
            throw new RuntimeException("速卖通---改产品分组解析excel报错");
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productGroupMap.size());

        for (UpdatePriceEntity updatePriceEntity : returnResultList) {
            AliexpressExecutors.updateTemplate((rsp) -> {
                ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
                String seller = updatePriceEntity.getSeller();
                String productId = updatePriceEntity.getProductId();
                String groupIds = productGroupMap.get(productId);

                try {
                    SaleAccountAndBusinessResponse account = accountMap
                            .get(seller);
                    if (null == account) {
                        account = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, seller);

                        if (account != null) {
                            accountMap.put(seller, account);

                            //同步最新分组
//                            GroupCall call = new GroupCall();
//                            call.syncAliexpressProductGroups(account);
                        }
                    }
                    if (account == null) {
                        updatePriceEntity.setErrorTip("查询不到店铺信息！");
                        responseJson.setMessage(updatePriceEntity.getErrorTip());
                        return;
                    }

                    //通过excel的名称匹配产品分组id
//                    List<String> strings = CommonUtils.splitList(groupName, ",");
//
//                    List<Long> leafGroupIdList = new ArrayList<>();
//
//                    for (String string : strings) {
//                        String[] split = StringUtils.split(string, "/");
//                        Long leafGroupId = getLeafGroupId(split, 0, seller, null);
//
//                        if(leafGroupId == null){
//                            updatePriceEntity.setErrorTip(string + " 查询不到分组信息！");
//                            responseJson.setMessage(updatePriceEntity.getErrorTip());
//                            return;
//                        }
//                        leafGroupIdList.add(leafGroupId);
//                    }

                    GroupOpenCall call = new GroupOpenCall();
                    responseJson = call.setGroup(account, productId, groupIds);

                    if (responseJson.isSuccess()) {
                        updatePriceEntity.setErrorTip("修改成功！");
                        SynchItemOpenCall synchCall = new SynchItemOpenCall();
                        synchCall.syncAliexpressProductInfo(account, Long.valueOf(productId));
                    } else {
                        updatePriceEntity.setErrorTip(responseJson.getMessage());
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    updatePriceEntity.setErrorTip(e.getMessage());
                    responseJson.setMessage(updatePriceEntity.getErrorTip());
                } finally {
                    countDownLatch.countDown();
                    AliexpressProductLog log = new AliexpressProductLog();
                    log.setAccountNumber(seller);
                    log.setOperateType(OperateLogTypeEnum.update_group_id.getCode());
                    log.setOperator(userName);
                    log.setResult(responseJson.isSuccess());
                    log.setProductId(Long.valueOf(productId));
                    log.setFailInfo(responseJson.getMessage());
                    aliexpressProductLogService.insert(log);
                }
            });
        }

        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return returnResultList;
    }


    //excel 修改pop库存
    @Override
    public List<UpdateStockExcel> updatePopStockForExcle(String[] headers, MultipartFile file, String userName) {
        //excel 返回结果
        List<UpdateStockExcel> returnResultList = new ArrayList<>();
        // 日志
        List<AliexpressProductLog> logList = new ArrayList<>();
        //解析excel
        Map<String, List<UpdateStockExcel>> productIdEntityListMap = new HashMap<>();

        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                UpdateStockExcel rowData = new UpdateStockExcel();
                String productIdStr = "";
                try {
                    String sellerStr = ExcelUtils.getCellValue(row.getCell(0)).trim();
                    rowData.setAccount(sellerStr);
                    productIdStr = ExcelUtils.getCellValue(row.getCell(1)).trim();
                    rowData.setProductId(productIdStr);
                    String skuCodeStr = ExcelUtils.getCellValue(row.getCell(2)).trim();
                    rowData.setSkuCode(skuCodeStr);

                    String systemStockString = "";
                    Cell cell = row.getCell(3);
                    if (cell != null) {
                        cell.setCellType(CellType.STRING);
                        systemStockString = row.getCell(3).getStringCellValue().toString();
                    }
                    String updateStockString = "";
                    Cell cell1 = row.getCell(4);
                    if (cell1 != null) {
                        cell1.setCellType(CellType.STRING);
                        updateStockString = row.getCell(4).getStringCellValue().toString();
                    }
                    Integer systemStock, updateStock = null;
                    try {
                        if (StringUtils.isNotBlank(systemStockString)) {
                            systemStock = Integer.parseInt(systemStockString);
                            rowData.setSystemStock(systemStock);
                        }
                        if (StringUtils.isNotBlank(updateStockString)) {
                            updateStock = Integer.parseInt(updateStockString);
                            rowData.setUpdateStock(updateStock);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        rowData.setTip("库存格式有误！" + e.getMessage());
                        rowData.setResult("失败");
                    }
                    if (StringUtils.isBlank(sellerStr) || StringUtils.isBlank(productIdStr) ||
                            StringUtils.isBlank(skuCodeStr) || null == updateStock) {
                        rowData.setTip("数据为空不处理！");
                        rowData.setResult("失败");
                        return row;
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    rowData.setTip("数据有误！" + e.getMessage());
                    rowData.setResult("失败");
                } finally {
                    if (StringUtils.isNotBlank(productIdStr)) {
                        List<UpdateStockExcel> entityList = productIdEntityListMap.get(productIdStr);
                        if (CollectionUtils.isNotEmpty(entityList)) {
                            entityList.add(rowData);
                        } else {
                            entityList = new ArrayList<>();
                            entityList.add(rowData);
                        }
                        productIdEntityListMap.put(productIdStr, entityList);
                    }
                }
                return row;
            }, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("速卖通---修改pop库存解析excel报错");
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productIdEntityListMap.size());
        productIdEntityListMap.forEach((k, v) -> {
            AliexpressExecutors.smtItemPopStockUpdate(() -> {
                try {
                    String seller = v.get(0).getAccount();
                    ResponseJson responseJson = ExcelOperationUtils.authIntercept(seller, userName);
                    if (!responseJson.isSuccess()) {
                        for (UpdateStockExcel updateStockExcel : v) {
                            updateStockExcel.setTip(responseJson.getMessage());
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return;
                    }
                    Set<String> skuCodeSet = v.stream().map(t -> t.getSkuCode()).collect(Collectors.toSet());
                    if (skuCodeSet.size() < v.size()) {
                        for (UpdateStockExcel updateStockExcel : v) {
                            updateStockExcel.setTip("excel 同个产品 商品编码重复 请检查！");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return;
                    }

                    List<UpdateStockExcel> requestUpdateStockExcelList = new ArrayList<>();

                    for (UpdateStockExcel updateStockExcel : v) {
                        String result = updateStockExcel.getResult();
                        if (StringUtils.equalsIgnoreCase("失败", result)) {
                            returnResultList.add(updateStockExcel);
                        } else {
                            requestUpdateStockExcelList.add(updateStockExcel);
                        }
                    }

                    if (CollectionUtils.isEmpty(requestUpdateStockExcelList)) {
                        return;
                    }

                    String account = requestUpdateStockExcelList.get(0).getAccount();
                    List<String> skuCodeList = requestUpdateStockExcelList.stream().map(t -> t.getSkuCode()).collect(Collectors.toList());

                    EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                    request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());//设置状态
                    request.setAliexpressAccountNumber(account);
                    request.setProductId(Long.valueOf(k));
                    request.setSkuCode(StringUtils.join(skuCodeList, ","));
                    request.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                    List<EsAliexpressProductListing> dbList = esAliexpressProductListingService
                            .getEsAliexpressProductListing(request);
                    if (CollectionUtils.isEmpty(dbList)) {
                        for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                            updateStockExcel.setTip("请检查商品id和商品编码是否正确或者产品非上架、在线状态！");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                        }
                        return; //查询不到本地数据直接返回
                    }

                    if (dbList.size() < requestUpdateStockExcelList.size()) {
                        List<String> dbSkuCodeList = dbList.stream().map(t -> t.getSkuCode()).collect(Collectors.toList());
                        for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                            String excelSkuCode = updateStockExcel.getSkuCode();
                            if (!dbSkuCodeList.contains(excelSkuCode)) {
                                updateStockExcel.setTip("请检查商品id和商品编码是否正确或者产品非上架、在线状态！");
                                updateStockExcel.setResult("失败");
                                returnResultList.add(updateStockExcel);
                            }
                        }
                    } //部分数据

                    Map<String, UpdateStockExcel> skuCodeMap = new HashMap<>();
                    for (UpdateStockExcel updateStockExcel : requestUpdateStockExcelList) {
                        String skuCode = updateStockExcel.getSkuCode();
                        skuCodeMap.put(skuCode, updateStockExcel);
                    }
                    List<EsAliexpressProductListing> list = new ArrayList<>(); //最终请求到平台的list
                    //库存json
                    JSONObject skuStocks = new JSONObject();

                    for (EsAliexpressProductListing dbItem : dbList) {
                        String skuCode = dbItem.getSkuCode();
                        String articleNumber = dbItem.getArticleNumber();
                        UpdateStockExcel updateStockExcel = skuCodeMap.get(skuCode);
                        if (updateStockExcel == null) {
                            updateStockExcel = new UpdateStockExcel();
                        }
                        Integer excleUpdateStock = updateStockExcel.getUpdateStock(); //excel需要调整的库存
                        if (excleUpdateStock == null) {
                            updateStockExcel.setTip(articleNumber + " 需要调整的库存为空");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                            continue;
                        }

                        Integer systemStock = SkuStockUtils.getSkuStockToEbay(articleNumber.trim().toUpperCase());
                        if (systemStock == null) {
                            updateStockExcel.setTip(articleNumber + " redis 可用-待发为空");
                            updateStockExcel.setResult("失败");
                            returnResultList.add(updateStockExcel);
                            continue;
                        }

                        //提交到平台的库存值
                        Integer submitStock = excleUpdateStock;
                        if (excleUpdateStock > systemStock.intValue()) {
                            submitStock = systemStock;
                            updateStockExcel.setRemark("库存大于可用-待发，改后库存为实际可用-待发库存，改后库存为：" + systemStock);
                        }
                        if (dbItem.getIpmSkuStock().intValue() == submitStock) {
                            updateStockExcel.setTip("当前库存和需要修改库存一直 不需要调整:" + submitStock);
                            updateStockExcel.setResult("成功");
                            returnResultList.add(updateStockExcel);
                            continue;
                        }

                        skuStocks.put(dbItem.getSkuId(), submitStock.toString());

                        AliexpressProductLog log = new AliexpressProductLog();
                        log.setProductId(Long.valueOf(k));
                        log.setAccountNumber(account);
                        log.setSkuCode(dbItem.getArticleNumber());
                        log.setOperator(userName);
                        log.setOperateType(AliexpressProductOperateLogType.EDIT_STOCK);
                        log.setStockBeforeEdit(dbItem.getIpmSkuStock().doubleValue());
                        log.setStockAfterEdit(submitStock.doubleValue());
                        logList.add(log);

                        dbItem.setIpmSkuStock(submitStock); //设置改后库存
                        list.add(dbItem);
                    }

                    if (CollectionUtils.isNotEmpty(list)) {
                        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(account);
                        if (saleAccountAndBusinessResponse == null) {
                            saleAccountAndBusinessResponse = AccountUtils
                                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
                            accountMap.put(account, saleAccountAndBusinessResponse);
                        }
                        EditMutilpleSkuStocksOpenCall call = new EditMutilpleSkuStocksOpenCall();
                        ResponseJson rsp = call
                                .editMutilpleSkuStocksNew(saleAccountAndBusinessResponse, k,
                                        skuStocks.toJSONString(), true);
//                    ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
//                    rsp.setMessage("没调用平台接口，默认失败！");
                        if (rsp.isSuccess()) {
                            for (EsAliexpressProductListing productListing : list) {
                                try {
                                    EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                    updateItem.setId(productListing.getId());
                                    updateItem.setIpmSkuStock(productListing.getIpmSkuStock());
                                    smtItemEsBulkProcessor.updateIpmSkuStock(updateItem);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                            }
                        }
                        for (AliexpressProductLog productLog : logList) {
                            productLog.setResult(rsp.isSuccess());
                            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                            productLog.setFailInfo(rsp.getMessage());
                        }
                        for (EsAliexpressProductListing aliexpressProductListing : list) {
                            String skuCode = aliexpressProductListing.getSkuCode();
                            UpdateStockExcel updateStockExcel = skuCodeMap.get(skuCode);
                            if (updateStockExcel == null) {
                                updateStockExcel = new UpdateStockExcel();
                            }
                            updateStockExcel.setResult(rsp.isSuccess() ? "成功" : "失败");
                            updateStockExcel.setTip(rsp.getMessage());
                            returnResultList.add(updateStockExcel);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        aliexpressProductLogService.batchInsert(logList);
        return returnResultList;
    }

    //excel 获取库存
    @Override
    public List<PopStockExcel> popStockForExcle(String[] headers, MultipartFile file, String userName) {
        List<PopStockExcel> popStockExcelList = new ArrayList<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                String sku = ExcelUtils.getCellValue(row.getCell(0)).trim();
                String account = ExcelUtils.getCellValue(row.getCell(1)).trim();
                String prodouctId = ExcelUtils.getCellValue(row.getCell(2)).trim();
                String skuCode = ExcelUtils.getCellValue(row.getCell(3)).trim();
                if (StringUtils.isBlank(prodouctId)) {
                    return null;
                }

                PopStockExcel stockExcel = new PopStockExcel();
                stockExcel.setSku(sku);
                stockExcel.setAccount(account);
                stockExcel.setProductId(prodouctId);
                stockExcel.setSkuCode(skuCode);
                popStockExcelList.add(stockExcel);
                return row;
            }, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("速卖通---pop库存解析excel报错" + e.getMessage());
        }

        List<List<PopStockExcel>> lists = PagingUtils.newPagingList(popStockExcelList, 10000);
        for (List<PopStockExcel> list : lists) {
            List<String> productIdList = list.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            List<String> skuList = list.stream().map(t -> t.getSku()).collect(Collectors.toList());
            EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
            productListingRequest.setProductIdStr(StringUtils.join(productIdList, ","));
            productListingRequest.setArticleNumberStr(StringUtils.join(skuList, ","));
            productListingRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber", "articleNumber", "ipmSkuStock"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
            Map<String, Integer> map = esAliexpressProductListing.stream().collect(Collectors.toMap(k -> k.getProductId() + k.getArticleNumber(), v -> v.getIpmSkuStock(), (k1, k2) -> k1));

            for (PopStockExcel popStockExcel : list) {
                String productId = popStockExcel.getProductId();
                String sku = popStockExcel.getSku();
                Integer ipmStock = map.get(productId + sku);
                if (ipmStock != null) {
                    popStockExcel.setIpmStock(ipmStock.toString());
                }
            }
            map.clear();
        }
        return popStockExcelList;
    }


    private Long getLeafGroupId(String[] names, Integer size, String seller, Integer parentId) {
        if (size >= names.length) {
            return null;
        }
        String name = names[size++];
        AliexpressProductGroupExample groupExample = new AliexpressProductGroupExample();
        AliexpressProductGroupExample.Criteria criteria = groupExample.createCriteria();
        criteria.andAccountNumberEqualTo(seller).andGroupNameEqualTo(name);
        if (parentId != null) {
            criteria.andParentIdEqualTo(parentId);
        }
        List<AliexpressProductGroup> aliexpressProductGroups = aliexpressProductGroupService
                .selectByExample(groupExample);

        if (CollectionUtils.isNotEmpty(aliexpressProductGroups)) {
            for (AliexpressProductGroup aliexpressProductGroup : aliexpressProductGroups) {
                if (size == names.length && aliexpressProductGroup.getLeaf()) {
                    return aliexpressProductGroup.getGroupId();
                } else {
                    Long leafGroupId = getLeafGroupId(names, size, seller, aliexpressProductGroup.getId());
                    if (leafGroupId != null) {
                        return leafGroupId;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<UpdatePriceEntity> updateProductPrice(String[] headers, MultipartFile file, String user) {
        //操作人
        String userName = StringUtils.isNotBlank(user) ? user : WebUtils.getUserName();
        //excel 返回结果
        List<UpdatePriceEntity> returnResultList = new ArrayList<>();
        // 日志
        List<AliexpressProductLog> logList = new ArrayList<>();
        //解析excel
        Map<String, List<UpdatePriceEntity>> productIdEntityListMap = new HashMap<>();
        try {
            POIUtils.readExcelSheet1(headers, file, row -> {
                if (row == null) {
                    return null;
                }
                UpdatePriceEntity rowData = new UpdatePriceEntity();
                String productIdStr = "";
                try {
                    String sellerStr = ExcelUtils.getCellValue(row.getCell(0)).trim();
                    rowData.setSeller(sellerStr);
                    productIdStr = ExcelUtils.getCellValue(row.getCell(1)).trim();
                    rowData.setProductId(productIdStr);
                    String skuCodeStr = ExcelUtils.getCellValue(row.getCell(2)).trim();
                    rowData.setSkuCode(skuCodeStr);

                    row.getCell(3).setCellType(CellType.STRING);
                    String priceString = row.getCell(3).getStringCellValue().toString();
                    Double price = null;
                    try {
                        price = Double.parseDouble(priceString);
                        rowData.setPrice(price);
                    } catch (Exception e) {
                        rowData.setErrorTip("价格格式错误有误！");
                    }
                    if (StringUtils.isBlank(sellerStr) || StringUtils.isBlank(productIdStr) ||
                            StringUtils.isBlank(skuCodeStr) || null == price) {
                        return row;
                    }
                } catch (Exception e) {
                    rowData.setErrorTip("数据有误！");
                } finally {
                    if (StringUtils.isNotBlank(productIdStr)) {
                        List<UpdatePriceEntity> entityList = productIdEntityListMap.get(productIdStr);
                        if (CollectionUtils.isNotEmpty(entityList)) {
                            entityList.add(rowData);
                        } else {
                            entityList = new ArrayList<>();
                            entityList.add(rowData);
                        }
                        productIdEntityListMap.put(productIdStr, entityList);
                    }
                }
                return row;
            }, false);
        } catch (Exception e) {
            throw new RuntimeException("速卖通---改价解析excel报错");
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        //修改成功的产品
        List<EsAliexpressProductListing> updateDbAliexpressProducts = new ArrayList<>();

        //执行修改
        CountDownLatch countDownLatch = new CountDownLatch(productIdEntityListMap.size());
        productIdEntityListMap.forEach((k, v) -> {
            AliexpressExecutors.submitItemPriceUpdate(() -> {
                try {
                    //skuId -> 价格
                    Map<String, String> skuIdMap = new HashMap<>();

                    Map<String, Double> idPriceMap = new HashMap<>();

                    //请求产品
                    List<EsAliexpressProductListing> requestProductList = new ArrayList<>();

                    for (UpdatePriceEntity item : v) {
                        String errorTip = item.getErrorTip();
                        String seller = item.getSeller();
                        Long productId = Long.valueOf(item.getProductId());
                        String skuCode = item.getSkuCode();
                        Double skuPrice = item.getPrice();

                        if (StringUtils.isBlank(errorTip)) {
                            if (StringUtils.isBlank(seller) || StringUtils.isBlank(skuCode) || skuPrice == null) {
                                log.warn("*****空数据*****");
                                return;
                            }
                            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
                            esRequest.setAliexpressAccountNumber(seller);
                            esRequest.setProductId(productId);
                            esRequest.setSkuCode(skuCode);
                            esRequest.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                            esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                                    .getEsAliexpressProductListing(esRequest);

                            // 这里加判断是否在线
                            EsAliexpressProductListing esProduct = CollectionUtils.isNotEmpty(esAliexpressProductListing) ? esAliexpressProductListing.get(0) : null;
                            if (esProduct == null) {
                                item.setErrorTip("请检查商品id和商品编码是否正确或者产品非上架、在线状态！");
                                returnResultList.add(item);
                                return;
                            }

                            ResponseJson rsp = ExcelOperationUtils.authIntercept(seller, userName);
                            if (!rsp.isSuccess()) {
                                item.setErrorTip(rsp.getMessage());
                                returnResultList.add(item);
                                return;
                            }

                            skuIdMap.put(esProduct.getSkuId(), skuPrice.toString());
                            idPriceMap.put(esProduct.getId(), skuPrice);

                            //请求的产品
                            requestProductList.add(esProduct);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(requestProductList)) {
                        EsAliexpressProductListing esProduct = requestProductList.get(0);
                        Long productId = esProduct.getProductId();
                        String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                        SaleAccountAndBusinessResponse account = accountMap.get(aliexpressAccountNumber);
                        if (account == null) {
                            account = AccountUtils
                                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                            accountMap.put(aliexpressAccountNumber, account);
                        }
                        EditMultipleSkuPriceOpenCall priceCall = new EditMultipleSkuPriceOpenCall();
                        ResponseJson rsp = priceCall
                                .editMultipleSkuPrice(account, productId.toString(), JSON.toJSONString(skuIdMap));
                        //结果
                        boolean result = rsp.isSuccess();
                        for (EsAliexpressProductListing reqEsProduct : requestProductList) {
                            Double newPrice = idPriceMap.get(reqEsProduct.getId());
                            // 调价日志
                            AliexpressProductLog productLog = new AliexpressProductLog();
                            productLog.setResult(result);
                            productLog.setFailInfo(rsp.getMessage());
                            productLog.setPriceAfterEdit(newPrice);
                            productLog.setProductId(productId);
                            productLog.setPriceBeforeEdit(reqEsProduct.getSkuPrice());
                            productLog.setAccountNumber(aliexpressAccountNumber);
                            productLog.setSkuCode(reqEsProduct.getArticleNumber());
                            productLog.setOperator(userName);
                            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                            productLog.setOperateType(AliexpressProductOperateLogType.EDIT_PRICE);
                            logList.add(productLog);

                            if (result) {
                                String id = reqEsProduct.getId();

                                // 收集调价成功产品 修改本地库
                                EsAliexpressProductListing updateItem = new EsAliexpressProductListing();
                                updateItem.setId(id);
                                updateItem.setSkuPrice(newPrice);
                                smtItemEsBulkProcessor.updateSkuPrice(updateItem);

                                // 收集调价成功产品 修改本地库
//                                EsAliexpressProductListing allById = esAliexpressProductListingService.findAllById(id);
//                                if(allById != null && allById.getSkuPrice() != newPrice){
//                                    allById.setSkuPrice(newPrice);
//                                    updateDbAliexpressProducts.add(allById);
//                                }
                            }
                        }
                        for (UpdatePriceEntity item : v) {
                            if (result) {
                                item.setErrorTip("修改成功！");
                            } else {
                                item.setErrorTip(rsp.getMessage());
                            }
                            //结果返回
                            returnResultList.add(item);
                        }
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);

                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        // 修改本地库产品价格
//        if(CollectionUtils.isNotEmpty(updateDbAliexpressProducts)) {
//            esAliexpressProductListingService.saveAll(updateDbAliexpressProducts);
//        }
        // 批量记录日志
        if (CollectionUtils.isNotEmpty(logList)) {
            aliexpressProductLogService.batchInsert(logList);
        }
        return returnResultList;
    }

    @Override
    public AliexpressProduct querySkuSaleAttr(String articleNumber, Integer categoryId) {

        AliexpressProduct product = new AliexpressProduct();

        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setArticleNumber(articleNumber);
        esRequest.setCategoryId(categoryId);
        esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        esRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber", "categoryId", "articleNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);

        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            EsAliexpressProductListing productListing = esAliexpressProductListing.get(0);
            BeanUtils.copyProperties(productListing, product);

            AliexpressEsExtend extend = this.selectByAccountandProductId(productListing.getAliexpressAccountNumber(),
                    productListing.getProductId());
            if (null != extend) {
                product.setAeopAeProductSkusJson(extend.getAeopAeProductSkusJson());
            }
        }

        return product;
    }

    @Override
    public List<AliexpressDataView> countListing(String beginTime, String endTime) {
        List<AliexpressDataView> aliexpressDataViewList = new ArrayList<>();
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setProductStatusType("onSelling, auditing");
        esRequest.setFromGmtCreateDate(beginTime);
        esRequest.setToGmtCreateDate(endTime);
        esRequest.setQueryFields(new String[]{"aliexpressAccountNumber", "productId"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            Map<String, List<EsAliexpressProductListing>> collect = esAliexpressProductListing.stream()
                    .collect(Collectors.groupingBy(eapl -> eapl.getAliexpressAccountNumber()));
            for (Map.Entry<String, List<EsAliexpressProductListing>> stringListEntry : collect.entrySet()) {
                AliexpressDataView aliexpressDataView = new AliexpressDataView();
                String key = stringListEntry.getKey();
                List<EsAliexpressProductListing> value = stringListEntry.getValue();
                Map<Long, List<EsAliexpressProductListing>> productMap = value.stream()
                        .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
                int size = productMap.size();
                aliexpressDataView.setAccountNumber(key);
                aliexpressDataView.setType(DataViewTypeEnum.LISTING.getId());
                aliexpressDataView.setStatus(DataViewStatusEnum.ADD.getId());
                aliexpressDataView.setListingNum(size);
                aliexpressDataViewList.add(aliexpressDataView);
            }
        }
        return aliexpressDataViewList;
    }

    @Override
    public List<AliexpressDataView> selectTotalListing(AliexpressDataViewCriteria query) {
        List<AliexpressDataView> aliexpressDataViewList = new ArrayList<>();

        Date dateFrom = query.getDateFrom();
        Date dateTo = query.getDateTo();
        //页面选择的账号
        List<String> accountList = query.getAccountList();

        Integer numFrom = query.getNumFrom();
        Integer numTo = query.getNumTo();

        //页面选择的销售权限
        List<String> saleAccountAuth = query.getSaleAccountAuth();
        //登陆用户 的销售权限
        List<String> loginSaleAccountAuth = query.getLoginSaleAccountAuth();

        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        List<String> queryAccountList = null;
        if (CollectionUtils.isEmpty(accountList) && CollectionUtils.isEmpty(saleAccountAuth)) { //页面账号和销售都没选
            queryAccountList = loginSaleAccountAuth;
        } else if (CollectionUtils.isEmpty(accountList) && CollectionUtils.isNotEmpty(saleAccountAuth)) {
            queryAccountList = saleAccountAuth;
        } else if (CollectionUtils.isNotEmpty(accountList) && CollectionUtils.isEmpty(saleAccountAuth)) {
            queryAccountList = accountList;
        } else if (CollectionUtils.isNotEmpty(accountList) && CollectionUtils.isNotEmpty(saleAccountAuth)) {
            //取交集
            accountList.retainAll(saleAccountAuth);
            queryAccountList = accountList;
        }

        if (CollectionUtils.isNotEmpty(queryAccountList)) {
            esRequest.setAliexpressAccountNumber(StringUtils.join(queryAccountList, ","));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        esRequest.setFromGmtCreateDate(sdf.format(dateFrom));
        esRequest.setToGmtCreateDate(sdf.format(dateTo));
        esRequest.setQueryFields(new String[]{"aliexpressAccountNumber", "productId"});

        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(esRequest);
        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            Map<String, List<EsAliexpressProductListing>> collect = esAliexpressProductListing.stream()
                    .collect(Collectors.groupingBy(eapl -> eapl.getAliexpressAccountNumber()));

            for (Map.Entry<String, List<EsAliexpressProductListing>> stringListEntry : collect.entrySet()) {
                AliexpressDataView aliexpressDataView = new AliexpressDataView();
                String key = stringListEntry.getKey();
                List<EsAliexpressProductListing> value = stringListEntry.getValue();
                Map<Long, List<EsAliexpressProductListing>> productMap = value.stream()
                        .collect(Collectors.groupingBy(eapl -> eapl.getProductId()));
                int size = productMap.size();

                if (numFrom != null) {
                    if (numFrom > size) {
                        continue;
                    }
                }

                if (numTo != null) {
                    if (numTo < size) {
                        continue;
                    }
                }

                aliexpressDataView.setAccountNumber(key);
                aliexpressDataView.setType(DataViewTypeEnum.LISTING.getId());
                aliexpressDataView.setStatus(DataViewStatusEnum.TOTAL.getId());
                aliexpressDataView.setListingNum(size);
                aliexpressDataViewList.add(aliexpressDataView);
            }
        }
        return aliexpressDataViewList;
    }

    @Override
    public String queryProductAttr(List<String> skuList, Integer categoryId) {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        request.setCategoryId(categoryId);
        request.setArticleNumberStr(StringUtils.join(skuList, ","));
        request.setOrderBy("gmtCreate");
        request.setSequence("DESC");
        request.setPageFields(new String[]{"productId", "aliexpressAccountNumber"});
        Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, 1, 0);
        if (null != page && CollectionUtils.isNotEmpty(page.getContent())) {
            List<EsAliexpressProductListing> content = page.getContent();
            Long productId = content.get(0).getProductId();
            String aliexpressAccountNumber = content.get(0).getAliexpressAccountNumber();

            AliexpressEsExtend extend = this.selectByAccountandProductId(aliexpressAccountNumber, productId);
            if (null != extend) {
                return extend.getAeopAeProductPropertysJson();
            }
        }
        return null;
    }

    @Override
    public void batchUploadVideo(List<AliexpressEsExtend> esExtendList) {
        if (CollectionUtils.isEmpty(esExtendList)) {
            return;
        }
        Map<String, List<AliexpressEsExtend>> listMap = esExtendList.stream()
                .filter(t -> StringUtils.isNotBlank(t.getAliexpressAccountNumber()) && t.getProductId() != null)
                .collect(Collectors.groupingBy(e -> e.getAliexpressAccountNumber()));

        String userName = WebUtils.getUserName();

        //成人spu 不使用系统自动上传的视频
        SystemParam systemParam = systemParamService.querySystemParamByCodeKey("smt_param.adult_category");
        String paramValue = systemParam.getParamValue();
        List<Integer> adultCategoryIdList = ProductUtils.getAllCodeByFullPath(com.estone.erp.common.util.CommonUtils.splitList(paramValue, ","));
        if (CollectionUtils.isEmpty(adultCategoryIdList)) {
            adultCategoryIdList = new ArrayList<>();
        }

        for (Map.Entry<String, List<AliexpressEsExtend>> stringListEntry : listMap.entrySet()) {
            String key = stringListEntry.getKey();
            List<AliexpressEsExtend> value = stringListEntry.getValue();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, key);
            for (AliexpressEsExtend extend : value) {
                Long productId = extend.getProductId();
                String spu = extend.getSpu();
                String articleNumber = extend.getArticleNumber();
                String videoLink = extend.getVideoLink();
                //需要系统自动生成
                Boolean isSystemUploadVideo = extend.getIsSystemUploadVideo();
                if (StringUtils.isBlank(videoLink) && isSystemUploadVideo != null && isSystemUploadVideo) {
                    generateSystemVideo(spu, articleNumber, productId, adultCategoryIdList, userName, saleAccountByAccountNumber);
                    continue;
                }
                if (StringUtils.isBlank(videoLink)) {
                    continue;
                }
                handVidoeLink(videoLink, spu, articleNumber, productId, userName, saleAccountByAccountNumber);
            }
        }
    }

    /**
     * 处理视频url 上传
     *
     * @param videoLink
     * @param spu
     * @param articleNumber
     * @param productId
     * @param userName
     * @param saleAccountByAccountNumber
     */
    public void handVidoeLink(String videoLink, String spu, String articleNumber, Long productId, String userName, SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        if (StringUtils.isBlank(videoLink)) {
            return;
        }

        //转外网
        videoLink = ImageUtils.transferFileServiceImageUrl(videoLink);
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();

        AliexpressProductLogExample logExample = new AliexpressProductLogExample();
        logExample.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andProductIdEqualTo(productId)
                .andOperateStatusEqualTo(OperateLogStatusEnum.processing.intCode());
        List<AliexpressProductLog> aliexpressProductLogs = aliexpressProductLogService.selectByExample(logExample);
        if (CollectionUtils.isEmpty(aliexpressProductLogs)) {

            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            productLog.setProductId(productId);
            productLog.setNewRemark(videoLink);
            productLog.setOperator(userName);
            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            productLog.setOperateType(OperateLogTypeEnum.upload_video_new.getCode());
            productLog.setOperateStatus(OperateLogStatusEnum.processing.intCode());
            aliexpressProductLogService.insert(productLog);

            Long id = productLog.getId();
            String bizId = id + "_" + productId;
            UploadVideoNewCall videoNewCall = new UploadVideoNewCall();
            ResponseJson responseJson = videoNewCall.uploadVideoNew(saleAccountByAccountNumber, bizId, videoLink,
                    StringUtils.isBlank(spu) ? articleNumber : spu);
            if (!responseJson.isSuccess()) {
                productLog.setResult(false);
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setFailInfo(responseJson.getMessage());
                productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }
    }

    /**
     * 如果有系统视频 就用系统视频上传，没有就自动生成在上传
     *
     * @param spu
     * @param articleNumber
     * @param productId
     * @param adultCategoryIdList
     */
    public void generateSystemVideo(String spu, String articleNumber, Long productId, List<Integer> adultCategoryIdList, String userName, SaleAccountAndBusinessResponse saleAccountByAccountNumber) {
        String uploadVideo = "";
        if (StringUtils.isBlank(spu)) {
            spu = ProductUtils.getMainSku(articleNumber);
        }
        boolean isContainAdult = false;
        SingleItemEs skuInfoByMainSku = singleItemEsService.getSkuInfoByMainSku(spu);
        if (skuInfoByMainSku != null) {
            Integer categoryId = skuInfoByMainSku.getCategoryId();
            if (adultCategoryIdList.contains(categoryId)) {
                isContainAdult = true;
            }
        }
        if (isContainAdult) {
            log.info("产品id：" + productId + ", 属于成人用品spu 不使用系统生成的视频！");
            return;
        }

        //查询系统生成的视频
        String filePath = FmsUtils.getFilePath(SaleChannel.CHANNEL_SMT, VideoTypeEnum.system.getCode());
        ApiResult<List<String>> publishVideoResult = FmsUtils.getPublishVideo(spu, filePath);
        if (!publishVideoResult.isSuccess()) {
            log.error(String.format("[%s]获取文件系统视频异常:[%s]", spu, publishVideoResult.getErrorMsg()));
        }
        List<String> systemVideoList = publishVideoResult.getResult();
        if (CollectionUtils.isNotEmpty(systemVideoList)) {
            Collections.shuffle(systemVideoList);
            uploadVideo = systemVideoList.get(0);
        }

        if (StringUtils.isNotBlank(uploadVideo)) {
            handVidoeLink(uploadVideo, spu, articleNumber, productId, userName, saleAccountByAccountNumber);
        } else {
            //用图片生成视频
            AliexpressVideoUtils.generateSystemVideo(spu, saleAccountByAccountNumber.getAccountNumber(), productId.toString());
        }
    }

    @Override
    public int batchUpdate(List<AliexpressEsExtend> extendList) {
        return aliexpressEsExtendMapper.batchUpdate(extendList);
    }

    @Override
    public void clearAreaModifyPrice(Aliexpress28CalcBean bean) {
        // 勾选的国家
        List<String> updateCountryCodeList = bean.getUpdateCountryCodeList();

        // 产品id集合
        List<Long> productIdList = bean.getProductIdList();

        // 国家价格对应关系
        Map<String, Map<String, Double>> product28Map = new HashMap<>();

        //产品对应的价格类型
        Map<Long, String> priceTypeMap = new HashMap<>();

        // 获取清空后的价格
        AliexpressEsExtendExample esExtendExample = new AliexpressEsExtendExample();
        esExtendExample.createCriteria().andProductIdIn(productIdList);
        List<AliexpressEsExtend> extendList = selectByExample(esExtendExample);
        for (AliexpressEsExtend extend : extendList) {
            //具体产品会做移除操作，重新new个对象
            List<String> countryCodeList = new ArrayList<>();
            countryCodeList.addAll(updateCountryCodeList);
            AliexpressStatePriceUtils.getProduct28MapForEs(extend, product28Map, countryCodeList);
            priceTypeMap.put(extend.getProductId(), extend.getPriceType());
        }

        // 组装数据
        Map<String, List<AliexpressEditProductBean>> returnMap = new HashMap<>();

        // 查询es
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        String[] fields = {"id", "productId", "aliexpressAccountNumber", "articleNumber", "skuCode", "skuId"};
        request.setQueryFields(fields);
        request.setProductIdStr(StringUtils.join(productIdList, ","));
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);

        for (EsAliexpressProductListing esProduct : esAliexpressProductListing) {
            String productId = esProduct.getProductId().toString();
            List<AliexpressEditProductBean> dataList = returnMap.get(productId);
            if (CollectionUtils.isEmpty(dataList)) {
                dataList = new ArrayList<>();
                returnMap.put(productId, dataList);
            }

            AliexpressEditProductBean item = new AliexpressEditProductBean();
            item.setAccountNum(esProduct.getAliexpressAccountNumber());
            item.setProductId(productId);
            item.setPriceType(priceTypeMap.get(esProduct.getProductId()));
            item.setSkuCode(esProduct.getArticleNumber());
            item.setSkuId(esProduct.getSkuId());
            Map<String, Double> countryPriceMap = product28Map
                    .get(esProduct.getProductId() + "-" + esProduct.getSkuCode());
            item.setCountryPriceMap(countryPriceMap);

            dataList.add(item);
        }

        // 处理结果
        EnvironmentSupplierWrapper.execute(task -> {
            clearProductCountryPriceNew(returnMap);
        }, defaultTask -> {
            log.info("{}清空调价, param:{}", defaultTask, JSON.toJSONString(returnMap));
        });
    }

    private void clearProductCountryPriceNew(Map<String, List<AliexpressEditProductBean>> dataMap) {
        // 需要修改的产品集合，key皆为productId
        Map<String, JSONObject> editProductEntityMap = new ConcurrentHashMap<>();
        Map<String, UpdatePriceEntity> editProductReturnMap = new ConcurrentHashMap<>();

        long start = System.currentTimeMillis();
        log.warn("new速卖通清空区域调价--开始");
        String userName = WebUtils.getUserName();
        CompletionService<ResponseJson> completionService = new ExecutorCompletionService<>(AliexpressExecutors.UPDATE_STATE_PRICE_POOL);
        int i = 0;
        int count = 0;
        int surplus_size = dataMap.size();
        for (Map.Entry<String, List<AliexpressEditProductBean>> itemData : dataMap.entrySet()) {
            try {
                DataContextHolder.setUsername(userName);
                i++;
                String productId = itemData.getKey();

                AliexpressExecutors.submitStatePriceUpdate2(completionService, rsp -> {
                    List<AliexpressEditProductBean> value = itemData.getValue();
                    AliexpressStatePriceUtils.syncProductForUpdate(value, editProductEntityMap, editProductReturnMap);
                    UpdatePriceEntity updatePriceEntity = editProductReturnMap.get(productId);
                    JSONObject editProductData = editProductEntityMap.get(productId);
                    if (editProductData != null) {
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, updatePriceEntity.getSeller());
                        ResponseJson responseJson = OfferEditProductOpenCall
                                .offerEditProduct(saleAccountByAccountNumber, editProductData.toJSONString());
                        if (responseJson.isSuccess()) {
                            AliexpressEsExtend extend = this
                                    .selectByAccountandProductId(saleAccountByAccountNumber.getAccountNumber(),
                                            Long.valueOf(productId));

                            //为了判断是否全部清空
                            boolean isSign = true;
                            for (AliexpressEditProductBean aliexpressEditProductBean : value) {
                                Map<String, Double> countryPriceMap = aliexpressEditProductBean.getCountryPriceMap();
                                if (MapUtils.isNotEmpty(countryPriceMap)) {
                                    isSign = false;
                                    break;
                                }
                            }
                            extend.setAeopNationalQuoteConfiguration(isSign ? null :
                                    editProductData.getString("aeop_national_quote_configuration"));
                            this.updateByPrimaryKeySelective(extend);
                            updatePriceEntity.setErrorTip("清空成功");

                            //更新 是否区域调价字段
                            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                            request.setProductId(Long.valueOf(productId));
                            request.setAliexpressAccountNumber(updatePriceEntity.getSeller());
                            request.setQueryFields(new String[]{"id"});
                            List<EsAliexpressProductListing> listing = esAliexpressProductListingService
                                    .getEsAliexpressProductListing(request);

                            boolean isRegionPrice = StringUtils.isNotBlank(extend.getAeopNationalQuoteConfiguration()) ? true : false;
                            for (EsAliexpressProductListing aliexpressProductListing : listing) {
                                aliexpressProductListing.setIsRegionPrice(isRegionPrice);
                                esAliexpressProductListingService.updateRequest(aliexpressProductListing);
                            }

                        } else {
                            updatePriceEntity.setErrorTip(responseJson.getMessage());
                        }
                        aliexpressProductLogService.product28Clear(updatePriceEntity, userName);
                    }
                });

                if (i >= 200) {
                    try {
                        completionService.take().get();
                        count++;
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        int left = surplus_size - count;
        try {
            for (i = 0; i < left; i++) {
                completionService.take().get();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        long updateEnd = System.currentTimeMillis();
        log.warn("new速卖通清空区域调价：数据" + editProductEntityMap.size() + "条，耗时：" + (updateEnd - start) / 1000 + "秒");
    }

    @Override
    public ResponseJson productMoving(PublishBean publishBean) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        Long productId = publishBean.getProductId();
        String account = publishBean.getAccount();
        String publishAccount = publishBean.getPublishAccount();

        ProductMoveBean productMoveBean = publishBean.getProductMoveBean();
        ProductMoveContext productMoveContext = new ProductMoveContext();
        productMoveContext.setProductMoveBean(productMoveBean);
        try {
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setProductId(productId);
            request.setQueryFields(null);
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
            AliexpressEsExtend extend = this.selectByAccountandProductId(account, productId);

            boolean isCompose = esAliexpressProductListing.stream()
                    .map(EsAliexpressProductListing::getDataSourceType)
                    .allMatch(AliexpressListingDataSourceEnum.COMPOSE::isTrue);
            productMoveContext.setIsCompose(isCompose);

            boolean isSuit = false;
            if (isCompose) {
                //判断是否套装
                String articleNumber = esAliexpressProductListing.get(0).getArticleNumber();
                isSuit = ProductUtils.isExistSaleSuite(articleNumber);
                productMoveContext.setIsSuit(isSuit);
            }

            // 刊登店铺配置
            AliexpressConfig config = aliexpressConfigService.getDetailForAccount(publishAccount);
            if (config == null || CollectionUtils.isEmpty(config.getInfoList())) {
                rsp.setMessage(account + "该账号没有设置好配置规则！");
                return rsp;
            }
            productMoveContext.setAliexpressConfig(config);
            // 过滤可用的货号
            List<String> passSkuList = aliexpressProductMoveHelper.getPassSku(esAliexpressProductListing, isCompose, isSuit, publishAccount);
            if (CollectionUtils.isEmpty(passSkuList)) {
                rsp.setMessage("没有可刊登的货号！");
                return rsp;
            }
            productMoveContext.setPassSkuList(passSkuList);
            EsAliexpressProductListing aliexpressProductListing = esAliexpressProductListing.get(0);
            productMoveContext.setListing(aliexpressProductListing);
            // 设置产品重量 和 产品标签
            aliexpressProductMoveHelper.setProductWeightAndTagCode(productMoveContext);

            List<AliexpressConfigInfo> infoList = config.getInfoList();
            double accountCfgWeight = config.getAddWeight() == null ? 0 : config.getAddWeight();
            Double matchWeight = productMoveContext.getWeight() * 1000 + accountCfgWeight;
            String tagCode = productMoveContext.getTagCode();
            WeightTagCode weightTagCode = new WeightTagCode();
            weightTagCode.setMaxWeight(matchWeight);
            weightTagCode.setTagCode(tagCode);
            weightTagCode.setSpecialTags(productMoveContext.getSpecialTags());

            //寻找匹配的配置详情
            AliexpressConfigInfo matchConfigInfo = AliexpressStatePriceUtils.getConfigByMaxWeightAndTagCode(weightTagCode, infoList);
            if (matchConfigInfo == null) {
                rsp.setMessage(String.format("没有合适的配置匹配请检查 商品最重重量[%s] + 产品标签[%s]", matchWeight, tagCode));
                return rsp;
            }

            //如果是不同spu 就取原产品的标题描述图片
            List<EsAliexpressProductListing> listings = esAliexpressProductListing.stream()
                    .filter(t -> passSkuList.contains(t.getArticleNumber())).collect(Collectors.toList());

            Map<String, String> spuMap = new HashMap<>();
            for (EsAliexpressProductListing listing : listings) {
                spuMap.put(listing.getSpu(), listing.getSpu());
            }

            boolean isSame = spuMap.size() == 1;

            String articleNumber = passSkuList.get(0);
            SpuInfo spuInfo = aliexpressProductMoveHelper.getSpuInfo(productMoveContext);

            SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, publishAccount);
            SaleAccountAndBusinessResponse oldAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);

            //直接取原产品的图片，不用考虑被过滤sku的图片
            EsAliexpressProductListing productListing = esAliexpressProductListing.get(0);

            AliexpressTemplate template = new AliexpressTemplate();//初始化模板对象用于记录日志

            //图片
            String imageUrlStr = "";
            String subject = "";
            String detail = "";
            String mobileDetail = "";
            //如果spu相同，并且不是套装
            if (isSame && !isSuit) {
                if (productMoveBean == null || productMoveBean.getChangeImg() == null || !productMoveBean.getChangeImg()) {
                    //不用更换图片
                    imageUrlStr = productListing.getImageUrls();
                } else {
                    List<String> imageUrls = FmsUtils.getSmtImgs(articleNumber, null);
                    imageUrls = AliexpressContentUtils.imgPriorityForList(imageUrls);
                    if (CollectionUtils.isNotEmpty(imageUrls)) {
                        if (imageUrls.size() <= 6) {
                            imageUrlStr = StringUtils.join(imageUrls.toArray(), ";");
                        } else {
                            imageUrlStr = StringUtils.join(imageUrls.subList(0, 6).toArray(), ";");
                        }
                    } else {
                        //无图片
                        rsp.setMessage("无图片");
                        return rsp;
                    }
                }//end 图片

                if (productMoveBean == null || productMoveBean.getChangeTitle() == null || !productMoveBean.getChangeTitle()) {
                    //不用更换标题
                    subject = productListing.getSubject();
                }

                if (productMoveBean == null || productMoveBean.getChangeDesc() == null || !productMoveBean.getChangeDesc()) {
                    //不用更换描述
                    detail = productListing.getDetail(); //已经包含原产品图片

                    String aa = "<div class=\"detailmodule_html\"><div class=\"detail-desc-decorate-richtext\">";
                    String bb = "</div></div>";

                    if (StringUtils.contains(detail, aa)
                            && StringUtils.contains(detail, bb)) {
                        detail = StringUtils.replace(detail, aa, "").replace(bb, "");
                    }

                    //如果不需要更换描述，但是需要更换主图，需要把 描述的图片去除 并加上新描述
                    if (productMoveBean != null && productMoveBean.getChangeImg() != null && productMoveBean.getChangeImg()) {
                        detail = UploadImageOpenCall.changeDetailImg(detail, imageUrlStr);
                    }
                }

                //说明是需要更换标题或者描述 或者标题描述都需要更换
                if (StringUtils.isBlank(subject) || StringUtils.isBlank(detail)) {
                    SpuOfficial spuOfficial = aliexpressProductMoveHelper.getSpuOfficial(productMoveContext);
                    AliexpressTemplate spuTitleAndDetail = AliexpressTemplateDataUtils
                            .getSpuTitleAndDetail(spuOfficial, publishAccount);
                    if (StringUtils.isBlank(subject)) {
                        subject = spuTitleAndDetail.getSubject();
                        if (StringUtils.isBlank(subject)) {
                            rsp.setMessage("标题为空！");
                            return rsp;
                        }
                        //标题长度
//                        subject = AliexpressContentUtils.changTitleForAccount(subject, publishAccount);
                    }

                    if (StringUtils.isBlank(detail)) {
                        detail = spuTitleAndDetail.getDetail();
                        if (StringUtils.isBlank(detail)) {
                            rsp.setMessage("描述为空！");
                            return rsp;
                        }

                        if (!detail.contains("<!--")) {
                            detail = detail.replaceAll("\r\n", "<br>");
                            detail = detail.replaceAll("\n", "<br>");
                        }
                        detail = this.handleDescriptionImg(CommonUtils.splitList(imageUrlStr, ";"), detail);//描述后面添加图片
                        detail = detail.replaceAll("(?i)Features:", "<b>Features:</b><br>");
                        detail = detail.replaceAll("(?i)Specifications:", "<b>Specifications:</b><br>");
                        detail = detail.replaceAll("(?i)Package Includes:", "<b>Package Includes:</b><br>");
                        detail = detail.replaceAll("(?i)Note:", "<b>Note:</b><br>");
                        detail = "<div style=\"font-size: 18px;\">" + detail + "</div>";
                    }
                }

            } else {
                imageUrlStr = productListing.getImageUrls();
                subject = productListing.getSubject();
                detail = productListing.getDetail(); //包含了之前的图片
            }

            template.setAliexpressAccountNumber(publishAccount);
            template.setArticleNumber(productMoveContext.getSpu());
            template.setDetail(detail);

            mobileDetail = detail;
            mobileDetail = mobileDetail.replace("<b>", "");
            mobileDetail = mobileDetail.replace("</b>", "");
            if (StringUtils.indexOf(mobileDetail, "<div style=\"font-size: 18px;\">") != -1) {
                mobileDetail = mobileDetail.replaceAll("<div style=\"font-size: 18px;\">", "");
                mobileDetail = mobileDetail.substring(0, mobileDetail.length() - 6);
            }

            template.setMobileDetail(mobileDetail);
            template.setSubject(subject);
            template.setImageUrls(imageUrlStr);
            template.setGrossWeight(String.valueOf(productMoveContext.getWeight()));

            Map<String, String> map = AliexpressTemplateDataUtils.translate(template.getSubject());
            template.setInterSubjects(JSON.toJSONString(map));
//            Boolean cny = config.getCny();
//            if(null != cny && cny){
//                template.setCurrencyCode("CNY");
//            }
            rsp = compileEsAliexpressToTemplate(productListing, extend, config,
                    matchConfigInfo, spuInfo, passSkuList,
                    newAccount, oldAccount, template, productMoveBean);


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("产品搬家组装信息异常:" + e.getMessage());
        }
        return rsp;
    }

    @Override
    public ResponseJson updateProduct(AliexpressTemplate aliexpressTemplate) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            long begin = System.currentTimeMillis();
            String aliexpressAccountNumber = aliexpressTemplate.getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
            JSONObject productEntity = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, aliexpressTemplate.getProductId());
            long end = System.currentTimeMillis();
            if ((end - begin) > 5000L) {
                log.info(aliexpressTemplate.getProductId() + "编辑产品获取平台最新产品信息耗时" + (end - begin));
            }
            if (productEntity == null || productEntity.isEmpty()) {
                rsp.setMessage("获取平台产品信息异常，确定token无异常或者产品存在，如果不是请重试！");
                return rsp;
            }

            String taxType = aliexpressTemplate.getTaxType();
            if (StringUtils.isBlank(taxType)) {
                taxType = "1";
            }
            productEntity.put("tax_type", taxType);
            productEntity.put("hscode", aliexpressTemplate.getHacodeJson());
            productEntity.put("add_unit", aliexpressTemplate.getAddUnit());
            productEntity.put("add_weight", aliexpressTemplate.getAddWeight());
            productEntity.put("base_unit", aliexpressTemplate.getBaseUnit());
            productEntity.put("bulk_discount", aliexpressTemplate.getBulkDiscount());
            productEntity.put("bulk_order", aliexpressTemplate.getBulkOrder());
            productEntity.put("category_id", aliexpressTemplate.getCategoryId());
            if (StringUtils.isNotBlank(aliexpressTemplate.getCurrencyCode())) {
                productEntity.put("currency_code", aliexpressTemplate.getCurrencyCode());
            }
            productEntity.put("delivery_time", aliexpressTemplate.getDeliveryTime());
            productEntity.put("gross_weight", aliexpressTemplate.getGrossWeight());
            productEntity.put("group_id", aliexpressTemplate.getGroupId());
            productEntity.put("is_pack_sell", aliexpressTemplate.getIsPackSell());
            productEntity.put("lot_num", aliexpressTemplate.getLotNum());
            productEntity.put("package_height", aliexpressTemplate.getPackageHeight());
            productEntity.put("package_length", aliexpressTemplate.getPackageLength());
            productEntity.put("package_type", aliexpressTemplate.getPackageType());
            productEntity.put("package_width", aliexpressTemplate.getPackageWidth());
            if (aliexpressTemplate.getProductPrice() != null) {
                productEntity.put("product_price", aliexpressTemplate.getProductPrice());
            }
            productEntity.put("product_unit", aliexpressTemplate.getProductUnit());
            productEntity.put("promise_template_id", aliexpressTemplate.getPromiseTemplateId());
            productEntity.put("reduce_strategy", aliexpressTemplate.getReduceStrategy());
            productEntity.put("sizechart_id", aliexpressTemplate.getSizeChartId());
            productEntity.put("sizechart_id_list", CommonUtils.splitLongList(aliexpressTemplate.getSizeChartIdList(), ","));
            productEntity.put("ws_valid_num", aliexpressTemplate.getWsValidNum());
            productEntity.put("freight_template_id", aliexpressTemplate.getFreightTemplateId());
            productEntity.put("dp_bulk_discount", aliexpressTemplate.getBulkDiscount());
            productEntity.put("dp_bulk_order", aliexpressTemplate.getBulkOrder());

            String aeopQualificationStructJson = aliexpressTemplate.getAeopQualificationStructJson();
            if (StringUtils.isBlank(aeopQualificationStructJson)) {
                productEntity.put("aeop_qualification_struct_list", null);
            } else {
                aeopQualificationStructJson = UploadImageOpenCall.postSkuPropertyImage(saleAccountByAccountNumber, aeopQualificationStructJson, null);
                productEntity.put("aeop_qualification_struct_list", aeopQualificationStructJson);
            }

            begin = System.currentTimeMillis();
            //需要调整
            String aeopAeProductPropertysJson = aliexpressTemplate.getAeopAeProductPropertysJson();
            String prodPropJsonWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, aeopAeProductPropertysJson, null, "1");
            end = System.currentTimeMillis();

            if ((end - begin) > 10000L) {
                log.info(aliexpressTemplate.getProductId() + "编辑产品aeopAeProductPropertysJson上传图片耗时" + (end - begin));
            }
            if (StringUtils.isNotBlank(prodPropJsonWithPostedImg)) {
                JSONArray jsonArray = JSONArray.parseArray(prodPropJsonWithPostedImg);
                productEntity.put("aeop_ae_product_propertys", jsonArray);
            }

            begin = System.currentTimeMillis();
            String aeopAeProductSKUsJson = aliexpressTemplate.getAeopAeProductSkusJson();
            String skuPropJsonWithPostedImg = UploadImageOpenCall.postSkuPropertyImage(saleAccountByAccountNumber, aeopAeProductSKUsJson, null, "1");
            end = System.currentTimeMillis();
            if ((end - begin) > 10000L) {
                log.info(aliexpressTemplate.getProductId() + "编辑产品aeopAeProductSKUsJson传图片耗时" + (end - begin));
            }

            if (StringUtils.isNotBlank(skuPropJsonWithPostedImg)) {
                JSONArray jsonArray = JSONArray.parseArray(skuPropJsonWithPostedImg);
                productEntity.put("aeop_ae_product_s_k_us", jsonArray);
            }

            begin = System.currentTimeMillis();
            //6张图片 ;分割
            String imageURLs = aliexpressTemplate.getImageUrls();
            String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, imageURLs, null, "1");
            end = System.currentTimeMillis();
            if ((end - begin) > 10000L) {
                log.info(aliexpressTemplate.getProductId() + "编辑产品aimageURLs传图片耗时" + (end - begin));
            }
            productEntity.put("image_u_r_ls", imgUrlsWithPostedImg);

            //营销图
            List<MarketImage> marketImages = aliexpressTemplate.getMarketImages();
            if (CollectionUtils.isNotEmpty(marketImages)) {
                begin = System.currentTimeMillis();
                List<MarketImage> newMarketImages = new ArrayList<>();
                for (MarketImage marketImage : marketImages) {
                    String url = marketImage.getUrl();
                    if (StringUtils.isBlank(url)) {
                        continue;
                    }

                    //不是线上的图片 才传图片
                    if (!StringUtils.contains(url, "alicdn")) {
                        UploadImageOpenCall call = new UploadImageOpenCall();
                        String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, url, null, false, null);
                        marketImage.setUrl(postedImgUrl);
                    }
                    newMarketImages.add(marketImage);
                }

                // 营销图
                JSONArray marketImageJsonArray = JSONArray.parseArray(JSON.toJSONString(newMarketImages));
                productEntity.put("market_images", marketImageJsonArray);

                end = System.currentTimeMillis();
                if ((end - begin) > 10000L) {
                    log.info(aliexpressTemplate.getProductId() + "编辑产品营销图传图片耗时" + (end - begin));
                }
            }

            //标题超过128 按照词组切分
            if (StringUtils.isNotBlank(aliexpressTemplate.getSubject()) && aliexpressTemplate.getSubject().length() > 128) {
                begin = System.currentTimeMillis();
                String newTitle = AliexpressContentUtils.changTitleForAccount(aliexpressTemplate.getSubject(), null);
                //重新设置标题
                aliexpressTemplate.setSubject(newTitle);
                end = System.currentTimeMillis();
                if ((end - begin) > 10000L) {
                    log.info(aliexpressTemplate.getProductId() + "编辑产品重新判断标题长度耗时" + (end - begin));
                }
            }

            String systemParamValue = "";
            SystemParam fileParam = systemParamService.querySystemParamByCodeKey("smt_filter_infringement.accounts");
            if (fileParam != null) {
                systemParamValue = fileParam.getParamValue();
            }
            List<String> filterAccountList = CommonUtils.splitList(systemParamValue, ",");
            if (!filterAccountList.contains(aliexpressAccountNumber)) {
                // 获取速卖通侵权词
                ApiResult<InfringmentResponse> checkResult = AliexpressCheckUtils.checkInfringWordAndBrandForTemplate(aliexpressTemplate);
                if (!checkResult.isSuccess()) {
                    rsp.setMessage("调用校验侵权服务 " + checkResult.getErrorMsg());
                    return rsp;
                }

                //收集所有的侵权词，商标词
                Set<String> infringementSet = new HashSet<>();
                InfringmentResponse infringmentResponse = checkResult.getResult();
                if (MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                }

                if (MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                }
                List<String> infringementList = new ArrayList<>(infringementSet);

                begin = System.currentTimeMillis();
                //去除侵权词
                AliexpressTemplateDataUtils.removeTort(aliexpressTemplate, infringementList);
                String message = AliexpressTemplateDataUtils.getTortMessage(aliexpressTemplate, infringementList);
                if (StringUtils.isNotBlank(message)) {
                    rsp.setMessage("存在侵权词 " + message);
                    return rsp;
                }
                end = System.currentTimeMillis();
                if ((end - begin) > 10000L) {
                    log.info(aliexpressTemplate.getProductId() + "编辑产品侵权词去除和校验耗时" + (end - begin));
                }
            }

            // 多语言标题
            String subject = aliexpressTemplate.getSubject();
            JSONArray subjectJSONArray = new JSONArray();
            JSONObject subjectJsonObject = new JSONObject();
            subjectJSONArray.add(subjectJsonObject);
            subjectJsonObject.put("value", subject);
            subjectJsonObject.put("locale", "en_US");

            String interSubjects = aliexpressTemplate.getInterSubjects();
            if (StringUtils.isNotBlank(interSubjects)) {
                Map<String, String> map = JSON.parseObject(interSubjects, Map.class);
                if (MapUtil.isNotEmpty(map)) {
                    for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                        String key = stringStringEntry.getKey();
                        String value = stringStringEntry.getValue();
                        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                            String languagesCodeBySite = TranslateCountryEnum.getLanguagesCodeBySite(key);
                            if (StringUtils.isNotBlank(languagesCodeBySite)) {
                                JSONObject jsonObject = new JSONObject();
                                subjectJSONArray.add(jsonObject);
                                jsonObject.put("value", value);
                                jsonObject.put("locale", languagesCodeBySite);
                            }
                        }
                    }
                }
            }

            productEntity.put("subject_list", subjectJSONArray);

            String detail = aliexpressTemplate.getDetail();
            String detailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, detail, null, "1");
            detailWithPostedImg = StringUtils.replace(detailWithPostedImg, "<span style=\"background: white; color: #fff;\">OOTDTY</span>", "");
            detailWithPostedImg = StringUtils.replace(detailWithPostedImg, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");
            //smt 刊登 新增白色字体 OOTDTY
            detailWithPostedImg = detailWithPostedImg.replaceFirst("<br>", "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>");

            //图片池的图片
            List<String> images = CommonUtils.splitList(imgUrlsWithPostedImg, ";");

            List<String> imgList = new ArrayList<>();
            //追加图片到描述
            for (String image : images) {
                imgList.add("<img src=\"" + image + "\" style=\"width:800px;\">");
                if (imgList.size() >= 6) {
                    break;
                }
            }

            //如果描述不包含图片
            if (!AliexpressContentUtils.isSmtImg(detailWithPostedImg)) {
                detailWithPostedImg += "<br>" + StringUtils.join(imgList, "<br>");
            }

            // 多语言描述
            JSONArray detailJSONArray = new JSONArray();
            JSONObject detailJsonObject = new JSONObject();
            detailJSONArray.add(detailJsonObject);
            detailJsonObject.put("web_detail", AliexpressDetailUtils.getDetail(detailWithPostedImg));

            if (StringUtils.isNotBlank(detail)) {
                //手机端描述去除样式
                String mobileDetail = StringUtils.replace(detail, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");

                String mobileDetailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, mobileDetail, null, "1");

                //如果描述不包含图片
                if (!AliexpressContentUtils.isSmtImg(mobileDetailWithPostedImg)) {
                    mobileDetailWithPostedImg += "<br>" + StringUtils.join(imgList, "<br>");
                }

                detailJsonObject.put("mobile_detail", AliexpressDetailUtils.getMobileDetail(mobileDetailWithPostedImg));
            }
            detailJsonObject.put("locale", "en_US");
            productEntity.put("detail_source_list", detailJSONArray);

            begin = System.currentTimeMillis();
            rsp = OfferEditProductOpenCall
                    .offerEditProduct(saleAccountByAccountNumber, productEntity.toJSONString());

            end = System.currentTimeMillis();
            if ((end - begin) > 10000L) {
                log.info(aliexpressTemplate.getProductId() + "编辑产品上传平台接口耗时" + (end - begin));
            }

            //记录处理报告
            if (rsp.isSuccess()) {
                SynchItemOpenCall synchItemCall = new SynchItemOpenCall();
                synchItemCall.syncAliexpressProductInfo(saleAccountByAccountNumber, aliexpressTemplate.getProductId());
            }

            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(aliexpressTemplate.getProductId());
            log.setOperateType(OperateLogTypeEnum.update_product.getCode());
            log.setOperator(WebUtils.getUserName());
            log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
            log.setFailInfo(rsp.getMessage());
            aliexpressProductLogService.insert(log);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
        return rsp;
    }

    @Override
    public ApiResult<?> uploadUpdateSubject(MultipartFile file) {
        String userName = WebUtils.getUserName();
        if (file == null || file.getSize() == 0) {
            return ApiResult.of(false, null, "上传失败 文件为空!");
        }
        ResultModel<ProductUploadUpdateSubjectBo> resultModel;
        Map<String, String> msgMap = new HashMap<>();
        try {
            String[] uploadUpdateSubjectHeaders = {"账号", "商品ID", "标题"};
            resultModel = POIUtils.readExcelSheet1(uploadUpdateSubjectHeaders, file, (row) -> {
                if (row == null) {
                    return null;
                }

                ProductUploadUpdateSubjectBo uploadUpdateSubjectBo = new ProductUploadUpdateSubjectBo();
                uploadUpdateSubjectBo.setRowNum(row.getRowNum() + 1);
                try {
                    String error = "";
                    String aliexpressAccountNumber = ExcelUtils.getCellValue(row.getCell(0)).trim();
                    if (StringUtils.isBlank(aliexpressAccountNumber)) {
                        error += "账号为空！";
                    } else {
                        uploadUpdateSubjectBo.setAliexpressAccountNumber(aliexpressAccountNumber);
                    }

                    String productIdStr = ExcelUtils.getCellValue(row.getCell(1)).trim();
                    if (StringUtils.isBlank(productIdStr)) {
                        error += "商品ID为空！";
                    } else {
                        uploadUpdateSubjectBo.setProductId(Long.valueOf(productIdStr));
                    }
                    String subject = ExcelUtils.getCellValue(row.getCell(2)).trim();
                    if (StringUtils.isBlank(subject)) {
                        error += "标题为空！";
                    } else {
                        uploadUpdateSubjectBo.setSubject(subject);
                    }
                    if (StringUtils.isNotBlank(error)) {
                        msgMap.put(uploadUpdateSubjectBo.getRowNum() + "行", error);
                    } else {
                        return uploadUpdateSubjectBo;
                    }
                } catch (Exception e) {
                    log.error(String.format("解析row %s,错误：", row.getRowNum()), e);
                    msgMap.put(uploadUpdateSubjectBo.getRowNum() + "行", e.getMessage());
                }
                return null;
            }, false);
        } catch (Exception e) {
            log.error("导入出错：", e);
            return ApiResult.newError("导入出错：" + e.getMessage());
        }
        if (!resultModel.isSuccess()) {
            return ApiResult.newError("导入失败：" + resultModel.getMsg());
        }
        List<ProductUploadUpdateSubjectBo> list = resultModel.getList();
        // 过滤出空对象 已记录错误
        list = list.stream().filter(o -> null != o).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            if (MapUtils.isEmpty(msgMap)) {
                return ApiResult.newError("导入数据为空！");
            } else {
                return ApiResult.newError(JSON.toJSONString(msgMap));
            }
        }

        Map<String, List<ProductUploadUpdateSubjectBo>> accountMap = list.stream().collect(Collectors.groupingBy(o -> o.getAliexpressAccountNumber()));
        accountMap.forEach((accountNumber, accountUploadUpdateSubjectBos) -> {
            SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
            List<Long> localProductIds = new ArrayList<>();
            List<Long> productIds = accountUploadUpdateSubjectBos.stream().map(o -> o.getProductId()).collect(Collectors.toList());
            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductIdList(productIds);
            esRequest.setAliexpressAccountNumber(accountNumber);
            esRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(esRequest);
            if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                localProductIds = esAliexpressProductListing.stream().map(o -> o.getProductId()).collect(Collectors.toList());
            }

            for (ProductUploadUpdateSubjectBo uploadUpdateSubjectBo : accountUploadUpdateSubjectBos) {
                List<Long> finalLocalProductIds = localProductIds;
                AliexpressExecutors.batchUpdateSubjectAndDetail(responseJson -> {
                    try {
                        responseJson.setStatus(StatusCode.FAIL);
                        if (!finalLocalProductIds.contains(uploadUpdateSubjectBo.getProductId())) {
                            responseJson.setMessage(uploadUpdateSubjectBo.getRowNum() + "行，商品货号不存在该店铺中!");
                            aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                            return;
                        }
                        if (null == saleAccountAndBusiness) {
                            responseJson.setMessage(uploadUpdateSubjectBo.getRowNum() + "行, 订单接口获取账号信息为空!");
                            aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                            return;
                        }

                        // 去除侵权词
                        String subject = uploadUpdateSubjectBo.getSubject();

                        // 获取速卖通侵权词
                        SearchVo searchVo = new SearchVo();
                        searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                        searchVo.setText(subject);
                        ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
                        if (!checkResult.isSuccess()) {
                            String s = "调用校验侵权服务 " + checkResult.getErrorMsg();
                            responseJson.setMessage(uploadUpdateSubjectBo.getRowNum() + "行, " + s);
                            aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                            return;
                        }

                        //收集所有的侵权词，商标词
                        Set<String> infringementSet = new HashSet<>();
                        InfringmentResponse infringmentResponse = checkResult.getResult();
                        if (MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                            infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                        }

                        if (MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                            infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                        }
                        List<String> infringementList = new ArrayList<>(infringementSet);

                        subject = AliexpressTemplateDataUtils.delInfringementWord(subject, infringementList);
                        if (StringUtils.isBlank(subject)) {
                            responseJson.setMessage(uploadUpdateSubjectBo.getRowNum() + "行, 去除侵权词后标题为空!");
                            aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                            return;
                        }

                        EditSimpleproductfiledOpenCall updateSubjectCall = new EditSimpleproductfiledOpenCall();
                        String updateSubjectCallRspStr = updateSubjectCall.setEditsimpleproductfiled(saleAccountAndBusiness, uploadUpdateSubjectBo.getProductId().toString(), "subject", subject);
                        responseJson = updateSubjectCall.checkErrorMessage(updateSubjectCallRspStr);
                        aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                    } catch (Exception e) {
                        log.error("execl导入修改标题异常" + e.getMessage(), e);
                        aliexpressProductLogService.insert(uploadUpdateSubjectBo, AliexpressProductOperateLogType.EDIT_SUBJECT_DETAIL, userName, responseJson);
                    }
                });
            }
        });

        if (MapUtils.isEmpty(msgMap)) {
            return ApiResult.newSuccess("请求中 稍后查看处理报告");
        } else {
            return ApiResult.newSuccess("部分数据错误" + JSON.toJSONString(msgMap) + "其他数据请求中 稍后查看处理报告");
        }
    }

    @Override
    public void updateSaleMode(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, Integer productUnit, Boolean packageType, Integer lotNum, String userName) {
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountAndBusinessResponse,
                        productId);
        if (jsonObject == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_sale_mode.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        //不是打包销售 lot_num  就=1
        if (!packageType) {
            lotNum = 1;
        }
        jsonObject.put("product_unit", productUnit);
        jsonObject.put("package_type", packageType);
        jsonObject.put("lot_num", lotNum);

        long begin = System.currentTimeMillis();
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountAndBusinessResponse, jsonObject.toJSONString());

        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "编辑产品上传平台接口耗时" + (end - begin));
        }

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.update_sale_mode.getCode());
        log.setOperator(userName);
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);
    }

    @Override
    public void updateQualification(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String json, String userName, boolean isCheck, boolean isSpecial) {
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountAndBusinessResponse,
                        productId);
        if (jsonObject == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_qualification.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        try {
            //置空
            if (StringUtils.isNotBlank(json)) {
                AliexpressEsExtend aliexpressEsExtend = this.selectByAccountandProductId(saleAccountAndBusinessResponse.getAccountNumber(), productId);
                String aeopQualificationStructJson = aliexpressEsExtend.getAeopQualificationStructJson();
                //页面输入
                JSONArray jsonArray = JSONArray.parseArray(json);

                //链接本身的属性
                if (StringUtils.isNotBlank(aeopQualificationStructJson)) {
                    JSONArray dbJsonArray = JSONArray.parseArray(aeopQualificationStructJson);
                    if (CollectionUtils.isNotEmpty(dbJsonArray)) {
                        //覆盖json 传入的数据 组合 本地数据一起上传
                        JSONArray coverJsonArray = new JSONArray();

                        //通过传入的json判断链接没有的才上传 适用于定时任务
                        JSONArray specialJsonArray = new JSONArray();

                        //本地
                        Map<String, JSONObject> dbMap = new HashMap<>();
                        for (int i = 0; i < dbJsonArray.size(); i++) {
                            JSONObject dbJson = dbJsonArray.getJSONObject(i);
                            String dbKey = dbJson.getString("key");
                            dbMap.put(dbKey, dbJson);
                        }

                        //页面输入 需要全部加入
                        Map<String, JSONObject> inputMap = new HashMap<>();
                        for (int i1 = 0; i1 < jsonArray.size(); i1++) {
                            JSONObject inputJson = jsonArray.getJSONObject(i1);
                            String key = inputJson.getString("key");
                            inputMap.put(key, inputJson);
                            coverJsonArray.add(inputJson);

                            //本地没有的才加入
                            if (dbMap.get(key) == null) {
                                specialJsonArray.add(inputJson);
                            }
                        }

                        //只有页面输入的没有才加上
                        for (Map.Entry<String, JSONObject> stringJSONObjectEntry : dbMap.entrySet()) {
                            String key = stringJSONObjectEntry.getKey();
                            JSONObject value = stringJSONObjectEntry.getValue();
                            if (inputMap.get(key) == null) {
                                coverJsonArray.add(value);
                            }
                        }
                        //新的json
                        if (isSpecial) {
                            //没有需要修改的
                            if (specialJsonArray.isEmpty()) {
                                AliexpressProductLog log = new AliexpressProductLog();
                                log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
                                log.setProductId(productId);
                                log.setOperateType(OperateLogTypeEnum.update_qualification.getCode());
                                log.setOperator(userName);
                                log.setResult(true);
                                log.setFailInfo("链接资质存在不做修改！");
                                aliexpressProductLogService.insert(log);
                                return;
                            } else {
                                for (int i = 0; i < specialJsonArray.size(); i++) {
                                    JSONObject jsonObject1 = specialJsonArray.getJSONObject(i);
                                    dbJsonArray.add(jsonObject1);
                                }
                                json = JSON.toJSONString(dbJsonArray);
                            }
                        } else {
                            json = JSON.toJSONString(coverJsonArray);
                        }
                    }
                }
                json = UploadImageOpenCall.postSkuPropertyImage(saleAccountAndBusinessResponse, json, null);
                jsonObject.put("aeop_qualification_struct_list", json);
            } else {
                jsonObject.put("aeop_qualification_struct_list", null);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_qualification.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo(e.getMessage());
            aliexpressProductLogService.insert(log);
            return;
        }

        long begin = System.currentTimeMillis();
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountAndBusinessResponse, jsonObject.toJSONString());

        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "编辑产品上传平台接口耗时" + (end - begin));
        }

        if (rsp.isSuccess()) {
            AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(saleAccountAndBusinessResponse.getAccountNumber(), productId);
            aliexpressEsExtend.setAeopQualificationStructJson(json);
            aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
            EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
            listingRequest.setProductId(productId);
            listingRequest.setQueryFields(new String[]{"id", "isHasQualification", "categoryId"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
            boolean isHasQualification = false;
            if (StringUtils.isNotBlank(json) && CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
                EsAliexpressProductListing productListing = esAliexpressProductListing.get(0);
                // 判断是否全部资质都够
                JSONArray jsonArray = JSONArray.parseArray(json);
                QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
                ResponseJson qualifications = qualificationsOpenCall.qualifications(saleAccountAndBusinessResponse, productListing.getCategoryId());
                if (qualifications.isSuccess()) {
                    JSONArray qualificationModule = (JSONArray) qualifications.getBody().get("key");
                    isHasQualification = AliexpressQualificationUtils.isHasQualification(jsonArray, qualificationModule);
                }
            }
            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                aliexpressProductListing.setIsHasQualification(isHasQualification);
                esAliexpressProductListingService.updateRequest(aliexpressProductListing);
            }
        }

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.update_qualification.getCode());
        log.setOperator(userName);
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);
    }

    @Override
    public void updateProductCategoryId(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, Integer categoryId, String json, String userName) {
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountAndBusinessResponse,
                        productId);
        if (jsonObject == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_categoryId.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        jsonObject.put("category_id", categoryId);
        jsonObject.put("aeop_ae_product_propertys",
                json);
        long begin = System.currentTimeMillis();
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountAndBusinessResponse, jsonObject.toJSONString());

        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "编辑产品上传平台接口耗时" + (end - begin));
        }
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.update_product_categoryId.getCode());
        log.setOperator(userName);
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);
    }

    /**
     * ES产品转换成模板
     *
     * @param productListing
     * @param extend
     * @param template
     */
    private ResponseJson compileEsAliexpressToTemplate(
            EsAliexpressProductListing productListing, AliexpressEsExtend extend,
            AliexpressConfig config, AliexpressConfigInfo matchConfigInfo,
            SpuInfo spuInfo, List<String> passSkuList,
            SaleAccountAndBusinessResponse newAccount, SaleAccountAndBusinessResponse oldAccount,
            AliexpressTemplate template, ProductMoveBean productMoveBean) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        //默认发货时间
        template.setDeliveryTime(config.getDeliverytime());
        template.setIsWholesale(config.getWholesale());
        template.setBulkOrder(config.getBulkorder());
        template.setBulkDiscount(config.getBulkdiscount());
        template.setProductStock(config.getStock());
        template.setWsValidNum(config.getWsvalidnum());

        //更换运费模板
        if (productMoveBean != null && productMoveBean.getChangeTempId() != null) {
            template.setFreightTemplateId(productMoveBean.getChangeTempId());
        } else {
            template.setFreightTemplateId(matchConfigInfo.getFreightTemplateId());
        }

        if (productMoveBean != null && productMoveBean.getChangeGroupId() != null) {
            template.setGroupId(productMoveBean.getChangeGroupId());
        } else {
            template.setGroupId(matchConfigInfo.getGroupId());
        }

        template.setPromiseTemplateId(matchConfigInfo.getPromiseTemplateId());
        template.setReduceStrategy(productListing.getReduceStrategy());
        template.setCategoryId(productListing.getCategoryId());
        template.setProductUnit(productListing.getProductUnit());
        template.setPackageType(productListing.getPackageType());
        template.setLotNum(productListing.getLotNum());

        //1实际尺寸 2店铺配置尺寸 3 系统默认
        Integer packingType = config.getPackingType();
        Integer packageLength = 20;
        Integer packageWidth = 20;
        Integer packageHeight = 20;
        if (packingType != null && packingType == 1) {
            if (spuInfo != null) {
                BigDecimal length = spuInfo.getLength();
                BigDecimal wide = spuInfo.getWide();
                BigDecimal height = spuInfo.getHeight();
                if (length != null && wide != null && height != null) {
                    packageLength = length.setScale(0, BigDecimal.ROUND_UP).intValue();
                    packageWidth = wide.setScale(0, BigDecimal.ROUND_UP).intValue();
                    packageHeight = height.setScale(0, BigDecimal.ROUND_UP).intValue();
                }
            } else {
                //说明原产品不是同一spu 需要去产品本身的长宽高
                packageLength = productListing.getPackageLength();
                packageWidth = productListing.getPackageWidth();
                packageHeight = productListing.getPackageHeight();
            }

        } else if (packingType != null && packingType == 2) {
            packageLength = config.getPackageLength();
            packageWidth = config.getPackageWidth();
            packageHeight = config.getPackageHeight();
        }
        template.setPackageLength(packageLength);
        template.setPackageWidth(packageWidth);
        template.setPackageHeight(packageHeight);
        template.setIsPackSell(productListing.getIsPackSell());
        template.setBaseUnit(productListing.getBaseUnit());
        template.setAddUnit(productListing.getAddUnit());
        template.setAddWeight(productListing.getAddWeight());
        template.setSizeChartId(productListing.getSizeChartId());
        String currencyCode = "USD";
        Boolean cny = config.getCny();
        if (cny != null && cny) {
            currencyCode = "CNY";
        }
        template.setCurrencyCode(currencyCode);
        Date couponStartDate = productListing.getCouponStartDate();
        if (couponStartDate != null) {
            template.setCouponStartDate(new Timestamp(couponStartDate.getTime()));
        }
        Date couponEndDate = productListing.getCouponEndDate();
        if (couponEndDate != null) {
            template.setCouponEndDate(new Timestamp(couponEndDate.getTime()));
        }
        template.setAeopNationalQuoteConfiguration(extend.getAeopNationalQuoteConfiguration());
        template.setAeopAeMultimedia(extend.getAeopAeMultimedia());

        //属性，后面会统一 去除原店铺前缀，加上新店铺前缀
        //需要使用admin范本的属性  但是品牌和型号 不用处理
        Integer categoryId = template.getCategoryId();
        String mainSku = ProductUtils.getMainSku(template.getArticleNumber());
        AliexpressAutoTemplateExample autoTemplateExample = new AliexpressAutoTemplateExample();
        autoTemplateExample.createCriteria().andCategoryIdEqualTo(categoryId)
                .andArticleNumberEqualTo(mainSku).andApplyStateEqualTo(ApplyStatusEnum.YES.getIntCode());

        List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService.selectByExample(autoTemplateExample);

        String aeopAeProductPropertysJson = extend.getAeopAeProductPropertysJson();
        if (CollectionUtils.isNotEmpty(aliexpressAutoTemplates) && StringUtils.isNotBlank(aliexpressAutoTemplates.get(0).getAeopAeProductPropertysJson())) {
            JSONArray finalJsonArray = new JSONArray();
            //品牌和型号 需要保留
            JSONArray initJsonArray = JSONArray.parseArray(aeopAeProductPropertysJson);
            JSONArray adminJsonArray = JSONArray.parseArray(aliexpressAutoTemplates.get(0).getAeopAeProductPropertysJson());
            for (int i = 0; i < initJsonArray.size(); i++) {
                JSONObject jsonObject = initJsonArray.getJSONObject(i);
                Long attr_name_id = jsonObject.getLong("attr_name_id");
                if (attr_name_id != null && (attr_name_id == 2l || attr_name_id == 3l)) {
                    finalJsonArray.add(jsonObject);
                }
            }
            for (int i = 0; i < adminJsonArray.size(); i++) {
                JSONObject jsonObject = adminJsonArray.getJSONObject(i);
                Long attr_name_id = jsonObject.getLong("attr_name_id");
                if (attr_name_id != null && attr_name_id != 2l && attr_name_id != 3l) {
                    finalJsonArray.add(jsonObject);
                }
            }
            aeopAeProductPropertysJson = finalJsonArray.toJSONString();
        }
        template.setAeopAeProductPropertysJson(aeopAeProductPropertysJson);

        //产品图片
        List<String> images = FmsUtils.getSmtImgs(passSkuList.get(0), null);
        images = AliexpressContentUtils.imgPriorityForList(images);

        //sku属性，需要去除过滤的sku
        String aeopAeProductSkusJson = extend.getAeopAeProductSkusJson();
        List<ProductSkuProperty> productSkuProperties = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<ProductSkuProperty>>() {
        });
        List<ProductSkuProperty> newProductSkuProperties = new ArrayList<>();
        for (ProductSkuProperty productSkuProperty : productSkuProperties) {
            String articleNumber = "";
            boolean isReplaceImg = false;
            String sku_code = productSkuProperty.getSku_code();
            String skuPrefix = oldAccount.getSellerSkuPrefix();
            if (StringUtils.isNotBlank(sku_code)
                    && StringUtils.isNotBlank(skuPrefix)) {
                articleNumber = sku_code.replaceAll(skuPrefix, "");
                if (StringUtils.indexOf(articleNumber, "[") != -1) {
                    String[] split = StringUtils.split(articleNumber, "[");
                    articleNumber = split[0];
                }
                if (passSkuList.contains(articleNumber)) {
                    newProductSkuProperties.add(productSkuProperty);
                    isReplaceImg = true;
                }
            }

            String articleNumberStr = articleNumber;

            //通过的产品需要重新查询新的子sku主图  勾选了更换子sku图片
            if (isReplaceImg && (productMoveBean != null && productMoveBean.getChangeSonSkuImg() != null && productMoveBean.getChangeSonSkuImg())) {
                AeopSkuPropertyList aeop_s_k_u_property_list = productSkuProperty.getAeop_s_k_u_property_list();
                if (null != aeop_s_k_u_property_list) {
                    List<AeopSkuProperty> aeop_sku_property = aeop_s_k_u_property_list.getAeop_sku_property();
                    if (CollectionUtils.isNotEmpty(aeop_sku_property)) {
//                        List<AeopSkuProperty> newAeop_sku_property = new ArrayList<>();
                        for (AeopSkuProperty aeopSkuProperty : aeop_sku_property) {
                            String sku_image = aeopSkuProperty.getSku_image();
                            if (StringUtils.isNotBlank(sku_image)) {
                                List<String> sonSkuImgs = images.stream()
                                        .filter(img -> StringUtils.containsIgnoreCase(img, articleNumberStr + "."))
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(sonSkuImgs)) {
                                    sonSkuImgs = FmsUtils
                                            .getSmtImgs(articleNumberStr, null);
                                    sonSkuImgs = AliexpressContentUtils.imgPriorityForList(images);
                                    sonSkuImgs = sonSkuImgs.stream()
                                            .filter(img -> StringUtils.containsIgnoreCase(img, articleNumberStr + "."))
                                            .collect(Collectors.toList());
                                }
                                if (CollectionUtils.isNotEmpty(sonSkuImgs)) {
                                    aeopSkuProperty.setSku_image(sonSkuImgs.get(0));
                                }
                            }
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(newProductSkuProperties)) {
            rsp.setMessage("没有可刊登产品！");
            return rsp;
        }

        template.setAeopAeProductSkusJson(JSON.toJSONString(newProductSkuProperties));

        AliexpressProductSourceCriteria productSourceCriteria = new AliexpressProductSourceCriteria();
        //需要刊登的店铺
        productSourceCriteria.setAliexpressAccountNumber(newAccount.getAccountNumber());
        productSourceCriteria.setStock(config.getStock());
        productSourceCriteria.setShippingMethodCode(matchConfigInfo.getShippingMethod());
        productSourceCriteria.setMargin(matchConfigInfo.getGrossProfit());
        productSourceCriteria.setCountryCode(matchConfigInfo.getCountryCode());
        //设置原账号  用于 获取之前的skucode前缀 还有获取skuList
        template.setAliexpressAccountNumber(oldAccount.getAccountNumber());

        ResponseJson responseJson = AliexpressTemplateDataUtils
                .executeTemplateData(template, productSourceCriteria, "productMove");

        String errorMsg = responseJson.getMessage();
        if (StringUtils.isNotBlank(errorMsg)) {
            rsp.setMessage(errorMsg);
            return rsp;
        }

        //上个方法会置换 账号 重新设置新的账号 一定要放在计算毛利后面 计算毛利 是需要原数据的账号
        template.setAliexpressAccountNumber(newAccount.getAccountNumber());

        //先检查是否刊登过
        ResponseJson checkRsp = AliexpressCheckUtils.isProductHavePublished(template);
        if (StringUtils.equalsIgnoreCase(StatusCode.FAIL, checkRsp.getStatus())) {
            template.setErrorMsg(checkRsp.getMessage());
            rsp.setMessage(checkRsp.getMessage());
            return rsp;
        }

        errorMsg = AliexpressTemplateDataUtils.publishPreHandle(template);
        if (StringUtils.isNotBlank(errorMsg)) {
            rsp.setMessage(errorMsg);
            return rsp;
        }

        //新增产地中国
        String propertysJson = template.getAeopAeProductPropertysJson();
        String addOrigin = AliexpressBrandUtils
                .addOrigin(propertysJson, newAccount, template.getCategoryId().toString());
        template.setAeopAeProductPropertysJson(addOrigin);

        rsp = OfferPostProductOpenCall.postProduct(newAccount, template, passSkuList);
        return rsp;
    }


    @Override
    public ResponseJson deleteProduct(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String userName, String ruleName) {
        long begin = System.currentTimeMillis();
        ResponseJson rsp = DeleteProductOpenCall.deleteProduct(saleAccountAndBusinessResponse, productId);
        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "删除产品接口耗时" + (end - begin));
        }
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.delete_product.getCode());
        log.setOperator(userName);
        if (StringUtils.isNotBlank(ruleName)) {
            log.setRuleName(ruleName);
        }
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);

        if (rsp.isSuccess()) {
            //删除成功
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setProductId(productId);
            request.setQueryFields(null);
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(request);
            //改成不在线
            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                aliexpressProductListing.setProductStatusType(ProductStatusTypeEnum.delete.getCode());
                aliexpressProductListing.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
            }
            esAliexpressProductListingService.saveAll(esAliexpressProductListing);
            // 链接跟踪
            productMonitorHelper.listingItemStatusListening(ProductStatusTypeEnum.delete.getCode(), esAliexpressProductListing);
        }
        return rsp;
    }

    @Override
    public void deleteAccountData(String account, String userName) {
        try {
            Assert.notNull(account);
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setQueryFields(new String[]{"id", "productId"});
            request.setAliexpressAccountNumber(account);
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                return;
            }

            List<List<EsAliexpressProductListing>> lists = PagingUtils.newPagingList(esAliexpressProductListing, 1000);
            for (List<EsAliexpressProductListing> list : lists) {
                esAliexpressProductListingService.deleteByList(list);
            }

            //用产品id删除，防止店铺正常 误操作，导致删除后面同步的产品
            Set<Long> productIdSet = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
            aliexpressEsExtendService.deleteByProductId(new ArrayList<>(productIdSet));

            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(account);
            log.setOperateType(OperateLogTypeEnum.delete_account_data.getCode());
            log.setOperator(userName);
            log.setResult(true);
            aliexpressProductLogService.insert(log);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(account);
            log.setOperateType(OperateLogTypeEnum.delete_account_data.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo(e.getMessage());
            aliexpressProductLogService.insert(log);
        }
    }

    /**
     * 处理描述的图片
     *
     * @param imageList
     * @param descriptionImag
     * @return
     */
    public String handleDescriptionImg(List<String> imageList, String descriptionImag) {
        descriptionImag = descriptionImag + "<br>";
        if (imageList.size() > 8) {
            imageList = imageList.subList(0, 8);
        }
        for (String imag : imageList) {
            descriptionImag = descriptionImag + "<br><img src=\"" + imag + "\" style=\"width:800px;\">";
        }
        return descriptionImag;
    }

    @Override
    public Map<String, Double> get28PriceDetail(Aliexpress28PriceBean aliexpress28PriceBean) {
        String account = aliexpress28PriceBean.getAccount();
        Long productId = aliexpress28PriceBean.getProductId();
        String skuCode = aliexpress28PriceBean.getSkuCode();
        Double skuPrice = aliexpress28PriceBean.getSkuPrice();
        if (StringUtils.isBlank(account) || null == productId || StringUtils.isBlank(skuCode) || null == skuPrice) {
            throw new RuntimeException("参数不完整");
        }

        // key：productId + "-" + skuCode value：32国价格
        Map<String, Map<String, Double>> product28Map = new HashMap<>();

        // 获取32国价格
        AliexpressEsExtend extend = aliexpressEsExtendService.selectByAccountandProductId(account, productId);
        AliexpressStatePriceUtils.getProduct28MapForEs(extend, product28Map);

        Map<String, Double> countryPriceMap = new HashMap<>();
        String priceType = extend.getPriceType();
        Map<String, Double> stringDoubleMap = product28Map.get(productId + "-" + skuCode);
        if (MapUtils.isEmpty(stringDoubleMap)) {
            throw new RuntimeException("暂无32国价格信息");
        }
        for (Map.Entry<String, Double> stringDoubleEntry : stringDoubleMap.entrySet()) {
            // 国家
            String key = stringDoubleEntry.getKey();
            // 价格
            Double value28 = stringDoubleEntry.getValue();
            if (null == value28 || value28 == 0.0d) {
                countryPriceMap.put(key, value28);
                continue;
            }
            if ("absolute".equalsIgnoreCase(priceType)) {
                countryPriceMap.put(key, value28);
            } else if ("percentage".equalsIgnoreCase(priceType)) {
                // 百分比转成直接调价价格
                double round = NumberUtils.round(skuPrice + (skuPrice * value28) / 100, 2);
                countryPriceMap.put(key, round);
            } else {
                // 基准转换成直接调价价格
                double round = NumberUtils.round((value28 + skuPrice), 2);
                countryPriceMap.put(key, round);
            }
        }

        // 如果是cny店铺，需要进行汇率转换
        AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(account);
        Boolean cny = aliexpressConfig.getCny();
        if (cny) {
            Double cnyRate;
            ApiResult<Double> cnyRateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.CNY, CurrencyConstant.USD);
            if (cnyRateResult.isSuccess()) {
                cnyRate = cnyRateResult.getResult();
            } else {
                throw new RuntimeException(String.format("获取汇率失败：%s", cnyRateResult.getErrorMsg()));
            }
            for (String country : countryPriceMap.keySet()) {
                Double cnyPrice = countryPriceMap.get(country);
                Double usdPrice = NumberUtils.round(cnyPrice * cnyRate, 2);
                countryPriceMap.put(country, usdPrice);
            }
        }

        return countryPriceMap;
    }

    @Override
    public Map<String, List<String>> getDiscountByProductId(String accountNumber, List<String> productIdStrList) {
        Map<String, List<String>> returnMap = new HashMap<>();

        // 查询物流模板
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setQueryFields(new String[]{"freightTemplateId", "productId"});
        request.setAliexpressAccountNumber(accountNumber);
        List<Long> productIdList = productIdStrList.stream().map(Long::parseLong).collect(Collectors.toList());
        request.setProductIdList(productIdList);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            returnMap.put("查询不到该商品", productIdStrList);
            return returnMap;
        }
        List<String> productIdList1 = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getProductId).map(String::valueOf).distinct().collect(Collectors.toList());
        List<String> notFoundProductList = productIdStrList.stream().filter(o -> !productIdList1.contains(o)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notFoundProductList)) {
            returnMap.put("查询不到该商品", notFoundProductList);
            productIdStrList.removeAll(notFoundProductList);
        }

        esAliexpressProductListing = esAliexpressProductListing.stream().filter(o -> o.getFreightTemplateId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            returnMap.put("该商品运费模板为空", productIdStrList);
            return returnMap;
        }
        List<String> productIdList2 = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getProductId).map(String::valueOf).distinct().collect(Collectors.toList());
        List<String> noTemplateIdProductList = productIdStrList.stream().filter(o -> !productIdList2.contains(o)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noTemplateIdProductList)) {
            returnMap.put("该商品运费模板为空", noTemplateIdProductList);
            productIdStrList.removeAll(noTemplateIdProductList);
        }

        List<Long> freightTemplateIdList = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getFreightTemplateId).distinct().collect(Collectors.toList());

        // 根据物流模板查询折扣
        AliexpressConfigProfitExample example = new AliexpressConfigProfitExample();
        example.createCriteria().andAccountEqualTo(accountNumber).andFreightTemplateIdIn(freightTemplateIdList);
        List<AliexpressConfigProfit> aliexpressConfigProfits = aliexpressConfigProfitService.selectByExample(example);
        if (CollectionUtils.isEmpty(aliexpressConfigProfits)) {
            returnMap.put("查询不到该商品折扣", productIdStrList);
            return returnMap;
        }
        HashMap<Long, Double> freightIdToDiscountMap = aliexpressConfigProfits.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getFreightTemplateId(), item.getDiscountRate()), HashMap::putAll);

        // 获取折扣率对应产品
        List<ProductToDiscount> productToDiscounts = new ArrayList<>();
        for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
            Double discount = freightIdToDiscountMap.get(aliexpressProductListing.getFreightTemplateId());
            if (null == discount) {
                continue;
            }
            ProductToDiscount productToDiscount = new ProductToDiscount();
            productToDiscount.setProductId(aliexpressProductListing.getProductId().toString());
            productToDiscount.setDiscount(discount);
            productToDiscounts.add(productToDiscount);
        }
        if (CollectionUtils.isEmpty(productToDiscounts)) {
            returnMap.put("查询不到该商品折扣", productIdStrList);
            return returnMap;
        }
        Map<Double, List<ProductToDiscount>> productToDiscountMap = productToDiscounts.stream().collect(Collectors.groupingBy(ProductToDiscount::getDiscount));
        for (Double discount : productToDiscountMap.keySet()) {
            List<String> productIdStr = productToDiscountMap.get(discount).stream().map(ProductToDiscount::getProductId).distinct().collect(Collectors.toList());
            returnMap.put(discount.toString(), productIdStr);
        }

        // 筛选出没有折扣的产品
        List<String> discountProductList = productToDiscounts.stream().map(ProductToDiscount::getProductId).collect(Collectors.toList());
        productIdStrList.removeAll(discountProductList);
        if (CollectionUtils.isNotEmpty(productIdStrList)) {
            returnMap.put("查询不到该商品折扣", productIdStrList);
        }

        return returnMap;
    }

    @Override
    public List<Long> getProductIdByAccount(String accountNumber) {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setQueryFields(new String[]{"productId"});
        request.setAliexpressAccountNumber(accountNumber);
        request.setProductStatusType("onSelling, auditing");
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            return Collections.emptyList();
        }

        return esAliexpressProductListing.stream().filter(o -> o.getProductId() != null).map(EsAliexpressProductListing::getProductId).distinct().collect(Collectors.toList());
    }

    @Override
    public void syncAccountBrand(String accountNumber, List<Integer> categoryIds) {
        if (StringUtils.isBlank(accountNumber)) {
            return;
        }

        // 查询该店铺所有有品牌id的商品类目
        if (CollectionUtils.isEmpty(categoryIds)) {
            categoryIds = new ArrayList<>();
            Set<Integer> categorySet = esAliexpressProductListingService.listAccountBrandCategory(accountNumber);
            if (CollectionUtils.isEmpty(categorySet)) {
                throw new RuntimeException("查询不到该店铺的所有类目");
            }
            categoryIds.addAll(categorySet);
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);

        List<CompletableFuture<Void>> taskList = Lists.newArrayList();
        List<AliexpressAccountBrand> updateList = Lists.newArrayList();
        List<AliexpressAccountBrand> addList = Lists.newArrayList();

        List<List<Integer>> partition = Lists.partition(categoryIds, 200);
        for (List<Integer> catIds : partition) {
            catIds.forEach(catId -> {
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    JSONArray aliexpressBrands = AliexpressBrandUtils.getAliexpressBrands(account, String.valueOf(catId));
                    if (CollectionUtils.isEmpty(aliexpressBrands)) {
                        return;
                    }
                    AliexpressAccountBrand accountBrand = accountBrandService.getAccountCategoryBrand(accountNumber, catId);
                    if (accountBrand == null) {
                        AliexpressAccountBrand addAccountBrand = buildBrand(accountNumber, catId, aliexpressBrands.toJSONString());
                        addList.add(addAccountBrand);
                        return;
                    }
                    accountBrand.setBrandAttr(aliexpressBrands.toJSONString());
                    accountBrand.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                    updateList.add(accountBrand);
                }, AliexpressExecutors.SYNC_BRAND);
                taskList.add(completableFuture);
            });
            CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
            if (CollectionUtils.isNotEmpty(addList)) {
                accountBrandService.batchInsert(addList);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                accountBrandService.batchUpdate(updateList);
            }
            taskList.clear();
            updateList.clear();
            addList.clear();
        }
    }

    private AliexpressAccountBrand buildBrand(String accountNumber, Integer categoryId, String brandJsonStr) {
        AliexpressAccountBrand accountBrand = new AliexpressAccountBrand();
        accountBrand.setAccountNumber(accountNumber);
        accountBrand.setCategoryId(categoryId);
        accountBrand.setBrandAttr(brandJsonStr);
        long currentTimeMillis = System.currentTimeMillis();
        accountBrand.setCreatedAt(new Timestamp(currentTimeMillis));
        accountBrand.setUpdatedAt(new Timestamp(currentTimeMillis));
        return accountBrand;
    }

    @Override
    public void updateProductEuId(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, Long euId, String userName) {
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountByAccountNumber,
                        productId);
        if (jsonObject == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_euid.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        Long plat_msr_eu_id = jsonObject.getLong("msr_eu_id");
        if (plat_msr_eu_id != null && plat_msr_eu_id.longValue() == euId) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_euid.getCode());
            log.setOperator(userName);
            log.setResult(true);
            log.setFailInfo("欧盟负责人相同,不用修改!");
            aliexpressProductLogService.insert(log);
            return;
        }

        jsonObject.put("msr_eu_id", euId);

        long begin = System.currentTimeMillis();
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountByAccountNumber, jsonObject.toJSONString());

        if (rsp.isSuccess()) {
            //同步数据
            SynchItemOpenCall call = new SynchItemOpenCall();
            call.syncAliexpressProductInfo(saleAccountByAccountNumber, productId);
        }

        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "编辑产品上传平台接口耗时" + (end - begin));
        }

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.update_product_euid.getCode());
        log.setOperator(userName);
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);
    }

    @Override
    public ResponseJson updateProductTaxType(SaleAccountAndBusinessResponse saleAccountByAccountNumber, List<EsAliexpressProductListing> productListingList, String setTaxType) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(productListingList) || StringUtils.isBlank(setTaxType)) {
            return rsp;
        }
        Long productId = productListingList.get(0).getProductId();

        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountByAccountNumber,
                        productId);
        if (jsonObject == null) {
            rsp.setMessage("产品不存在，或者获取平台产品数据接口失败！");
            return rsp;
        }
        String taxType = jsonObject.getString("tax_type");
        if (StringUtils.isNotBlank(taxType) && StringUtils.equalsIgnoreCase(taxType, setTaxType)) {
            rsp.setStatus(StatusCode.SUCCESS);
            smtItemEsBulkProcessor.updateTaxType(productListingList, setTaxType);
            return rsp;
        }
        jsonObject.put("tax_type", setTaxType);
        rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountByAccountNumber, jsonObject.toJSONString());
        if (rsp.isSuccess()) {
            smtItemEsBulkProcessor.updateTaxType(productListingList, setTaxType);
        }
        return rsp;
    }

    @Override
    public void updateDiscount(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, Integer bulkOrder, Integer bulkDiscount, String userName) {
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountByAccountNumber,
                        productId);
        if (jsonObject == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_discount.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        jsonObject.put("bulk_discount", bulkDiscount);
        jsonObject.put("bulk_order", bulkOrder);

        long begin = System.currentTimeMillis();
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountByAccountNumber, jsonObject.toJSONString());

        long end = System.currentTimeMillis();
        if ((end - begin) > 10000L) {
            log.info(productId + "编辑产品上传平台接口耗时" + (end - begin));
        }

        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
        log.setProductId(productId);
        log.setOperateType(OperateLogTypeEnum.update_product_discount.getCode());
        log.setOperator(userName);
        log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
        log.setFailInfo(rsp.getMessage());
        aliexpressProductLogService.insert(log);
    }

    @Override
    public void updateMarketImages(List<MarketImageBean> imageBeanList, String userName) {
        if (CollectionUtils.isEmpty(imageBeanList)) {
            return;
        }

        for (MarketImageBean marketImageBean : imageBeanList) {
            try {
                //如果本身是smt的图片，就不需要修改营销图
                String longImg2 = marketImageBean.getLongImg();
                String squareImg2 = marketImageBean.getSquareImg();
                if ((StringUtils.isNotBlank(squareImg2) && AliexpressContentUtils.isSmtImg(squareImg2))
                        && (StringUtils.isNotBlank(longImg2) && AliexpressContentUtils.isSmtImg(longImg2))) {
                    AliexpressProduct product = new AliexpressProduct();
                    product.setOperator(userName);
                    product.setAliexpressAccountNumber(marketImageBean.getAccount());
                    product.setProductId(marketImageBean.getProductId());
                    ResponseJson rsp = new ResponseJson();
                    rsp.setMessage("不需要修改！");
                    aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, rsp);
                    continue;
                }

                if (StrConstant.ADMIN.equals(userName)) {
                    // 如果是系统自动修改 若产品系统无白底图，则提示，SPU无白底图。若无场景图，则提示，Spu无场景图
                    StringBuilder stringBuilder = new StringBuilder();
                    if (StringUtils.isBlank(squareImg2)) {
                        stringBuilder.append("SPU无白底图;");
                    }
                    if (StringUtils.isBlank(longImg2)) {
                        stringBuilder.append("SPU无场景图;");
                    }
                    if (StringUtils.isNotBlank(stringBuilder.toString())) {
                        AliexpressProduct product = new AliexpressProduct();
                        product.setOperator(userName);
                        product.setAliexpressAccountNumber(marketImageBean.getAccount());
                        product.setProductId(marketImageBean.getProductId());
                        ResponseJson rsp = new ResponseJson();
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(stringBuilder.toString());
                        aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, rsp);
                        continue;
                    }
                } else {
                    //数据都为空，判断本地数据，看是否需要过滤
                    if (StringUtils.isBlank(longImg2) && StringUtils.isBlank(squareImg2)) {
                        AliexpressEsExtend aliexpressEsExtend = this.selectByAccountandProductId(marketImageBean.getAccount(), marketImageBean.getProductId());
                        if (StringUtils.isBlank(aliexpressEsExtend.getLongImg()) && StringUtils.isBlank(aliexpressEsExtend.getSquareImg())) {
                            AliexpressProduct product = new AliexpressProduct();
                            product.setOperator(userName);
                            product.setAliexpressAccountNumber(marketImageBean.getAccount());
                            product.setProductId(marketImageBean.getProductId());
                            ResponseJson rsp = new ResponseJson();
                            rsp.setMessage("不需要修改！");
                            aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, rsp);
                            continue;
                        }
                    }
                }

                AliexpressExecutors.updateMarkeImages((rsp) -> {
                    List<MarketImage> marketImages = new ArrayList<>();
                    //方图
                    MarketImage squareImg = new MarketImage();
                    squareImg.setUrl(marketImageBean.getSquareImg());
                    squareImg.setImage_type("2");
                    marketImages.add(squareImg);
                    //长图
                    MarketImage longImg = new MarketImage();
                    String longImg1 = marketImageBean.getLongImg();

                    AliexpressProduct product = new AliexpressProduct();
                    product.setOperator(userName);
                    product.setAliexpressAccountNumber(marketImageBean.getAccount());
                    product.setProductId(marketImageBean.getProductId());

                    //需要压缩图片
                    if (StringUtils.isNotBlank(longImg1) &&
                            StringUtils.indexOf(longImg1, "-effect-copy.") == -1
                            && !AliexpressContentUtils.isSmtImg(longImg1)) {
                        ResponseJson responseJson = AliexpressWaterMarkImgUtil.changeImg(longImg1, 750, 1000);
                        if (!responseJson.isSuccess()) {
                            aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, responseJson);
                            return;
                        } else {
                            longImg1 = responseJson.getMessage();
                        }
                    }

                    longImg.setUrl(longImg1);
                    longImg.setImage_type("1");
                    marketImages.add(longImg);
                    SaleAccountAndBusinessResponse saleAccount = marketImageBean.getSaleAccount();
                    if (null == saleAccount) {
                        saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, marketImageBean.getAccount());
                    }
                    ResponseJson updateMarketresponseJson = AliexpressOfferProductEditUtils
                            .syncProductUpdateMarkerimages(product, saleAccount, marketImages);
                    aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, updateMarketresponseJson);
                    //需要修改本地数据
                    if (updateMarketresponseJson.isSuccess()) {
                        AliexpressEsExtend aliexpressEsExtend = this.selectByAccountandProductId(marketImageBean.getAccount(), marketImageBean.getProductId());
                        Object o = updateMarketresponseJson.getBody().get("1");
                        if (o != null) {
                            aliexpressEsExtend.setLongImg(o.toString());
                        } else {
                            aliexpressEsExtend.setLongImg("");
                        }
                        Object o1 = updateMarketresponseJson.getBody().get("2");
                        if (o1 != null) {
                            aliexpressEsExtend.setSquareImg(o1.toString());
                        } else {
                            aliexpressEsExtend.setSquareImg("");
                        }
                        this.updateByPrimaryKeySelective(aliexpressEsExtend);
                    }
                });
            } catch (Exception e) {
                AliexpressProduct product = new AliexpressProduct();
                product.setOperator(userName);
                product.setAliexpressAccountNumber(marketImageBean.getAccount());
                product.setProductId(marketImageBean.getProductId());
                ResponseJson rsp = new ResponseJson();
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(e.getMessage());
                aliexpressProductLogService.insert(product, AliexpressProductOperateLogType.update_marketing_img, rsp);
            }
        }
    }

    @Override
    public ApiResult<AliexpressProductListingMsgDto> apiToProductListingMsgDto(String productId) {
        EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
        esRequest.setQueryFields(new String[]{"subject", "detail"});
        esRequest.setProductId(Long.parseLong(productId));

        List<EsAliexpressProductListing> esAliexpressProductListingList = esAliexpressProductListingService.getEsAliexpressProductListing(esRequest);
        AliexpressProductListingMsgDto msgDto = new AliexpressProductListingMsgDto();
        if (!esAliexpressProductListingList.isEmpty()) {
            EsAliexpressProductListing productListing = esAliexpressProductListingList.get(0);
            msgDto.setItemName(productListing.getSubject());
            msgDto.setItemDescription(productListing.getDetail());
        }
        return ApiResult.newSuccess(msgDto);
    }

    @Override
    public void upManufacturer(List<EsAliexpressProductListing> contentList, String userName) {
        try {
            if (CollectionUtils.isEmpty(contentList)) {
                return;
            }

            List<String> stopStatusList = Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode());

            //产品id对应的货号
            Map<Long, String> productIdToSkuMap = new HashMap<>();

            //需要筛选
            Map<Long, List<EsAliexpressProductListing>> productIdMap = contentList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                Long key = longListEntry.getKey();
                List<EsAliexpressProductListing> value = longListEntry.getValue();
                String articleNumber = value.stream().filter(t -> StringUtils.isNotBlank(t.getSkuStatus()) && !stopStatusList.contains(t.getSkuStatus()))
                        .map(t -> t.getArticleNumber()).findFirst().orElse(null);
                if (StringUtils.isBlank(articleNumber)) {
                    continue;
                }
                productIdToSkuMap.put(key, articleNumber.toUpperCase());
            }
            Set<String> skuSet = productIdToSkuMap.entrySet().stream().map(t -> t.getValue()).collect(Collectors.toSet());
            if (skuSet == null || skuSet.isEmpty()) {
                log.info("选择的产品没有有效的sku，请检查sku的状态");
                return;
            }

            //制造商信息
            Map<String, String> skuMap = ProductUtils.getGpsrManufacturerBySku(new ArrayList<>(skuSet));

            if (skuMap == null || skuMap.isEmpty()) {
                log.info("sku无制造商信息 " + StringUtils.join(skuSet, ","));
                return;
            }

            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                Long key = longListEntry.getKey();
                List<EsAliexpressProductListing> value = longListEntry.getValue();
                EsAliexpressProductListing esAliexpressProductListing = value.get(0);
                String sku = productIdToSkuMap.get(key);
                if (StringUtils.isBlank(sku)) {
                    log.info("产品id[%s], 无有效的货号", key);
                    continue;
                }
                String name = skuMap.get(sku);
                if (StringUtils.isBlank(name)) {
                    log.info("产品id[%s], 货号[%s], 产品系统无制造商公司", key, sku);
                    continue;
                }

                String manufactureName = esAliexpressProductListing.getManufactureName();
                if (StringUtils.equalsIgnoreCase(manufactureName, name)) {
                    log.info("产品id[%s], 货号[%s],制造商[%s], 制造商公司一样不需要调整", key, sku, name);
                    continue;
                }

                //记录处理报告 并发送队列
                AliexpressProductLog productLog = new AliexpressProductLog();
                productLog.setOperateType(OperateLogTypeEnum.UPDATE_MANUFACTURER.getCode());
                productLog.setAccountNumber(esAliexpressProductListing.getAliexpressAccountNumber());
                productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                productLog.setProductId(key);
                productLog.setSkuCode(sku);
                productLog.setOperator(userName);
                productLog.setNewRemark(name.trim());
                aliexpressProductLogService.insert(productLog);

                UpManufacturerBean upManufacturerBean = new UpManufacturerBean();
                upManufacturerBean.setProductLog(productLog);
                upManufacturerBean.setEsAliexpressProductListingList(value);
                rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_MANUFACTURER_KEY, JSON.toJSON(upManufacturerBean));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public List<DiscountProExcel> updateOrAddDiscountProductForExcel(MultipartFile file, String userName) {
        Map<String, DiscountProExcel> excelMap = new HashMap<>();
        List<DiscountProExcel> dataList = Lists.newArrayList();
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        try {
            EasyExcel.read(file.getInputStream(), DiscountProExcel.class, new AnalysisEventListener<DiscountProExcel>() {
                @Override
                public void invoke(DiscountProExcel rowData, AnalysisContext analysisContext) {
                    dataList.add(buildVo(rowData, analysisContext, excelMap));
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                    Map<Boolean, List<DiscountProExcel>> map = dataList.stream().collect(Collectors.groupingBy(data -> null == data.getResult()));
                    //已处理的数据
                    List<DiscountProExcel> handledProExcels = map.getOrDefault(false, Collections.emptyList());
                    //生成类型为【生成或编辑单品折扣】的失败处理报告
                    for (DiscountProExcel discountProExcel : handledProExcels) {
                        String newRemark = "活动ID:" + discountProExcel.getSingleDiscountIdStr() + ",导入商品折扣";
                        aliexpressProductLogHelper.insertLog(null, null, discountProExcel.getItemId(),
                                OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                                Boolean.FALSE, discountProExcel.getResult(), discountProExcel.getAccountNumber(), currentUser, newRemark, null);
                    }
                    //未处理的数据
                    List<DiscountProExcel> toBeHandleProExcels = map.getOrDefault(true, Collections.emptyList());
                    DataContextHolder.setUsername(userName);
                    //处理导入的商品数据
                    discountProExcelHelper.handleDiscountProExcels(toBeHandleProExcels, excelMap);
                }
            }).sheet().doRead();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("上传商品折扣解析excel报错");
        }
        return dataList;
    }

    private DiscountProExcel buildVo(DiscountProExcel rowData, AnalysisContext analysisContext, Map<String, DiscountProExcel> excelMap) {
        rowData.setRowNum(analysisContext.getCurrentRowNum());
        List<String> remarks = Lists.newArrayList();
        if (StringUtils.isEmpty(rowData.getAccountNumber())) {
            remarks.add("店铺为必填项");
        }
        if (StringUtils.isEmpty(rowData.getSingleDiscountIdStr())) {
            remarks.add("活动ID为必填项");
        }
        if (StringUtils.isEmpty(rowData.getItemIdStr())) {
            remarks.add("商品ID为必填项");
        }
        if (StringUtils.isEmpty(rowData.getDiscountStr())) {
            remarks.add("全站折扣为必填项");
        }
        if (StringUtils.isEmpty(rowData.getBuyMaxNumStr())) {
            remarks.add("限购数为必填项");
        }

        if (CollectionUtils.isNotEmpty(remarks)) {
            rowData.setResult("失败");
            rowData.setRemark(String.join(",", remarks));
            return rowData;
        }

        try {
            rowData.setSingleDiscountId(Long.valueOf(rowData.getSingleDiscountIdStr()));
        } catch (NumberFormatException e) {
            remarks.add("活动ID格式错误");
        }
        try {
            rowData.setItemId(Long.valueOf(rowData.getItemIdStr()));
        } catch (NumberFormatException e) {
            remarks.add("商品ID格式错误");
        }
        try {
            rowData.setDiscount(Integer.valueOf(rowData.getDiscountStr()));
        } catch (NumberFormatException e) {
            remarks.add("全站折扣格式错误");
        }
        try {
            rowData.setBuyMaxNum(Integer.valueOf(rowData.getBuyMaxNumStr()));
        } catch (NumberFormatException e) {
            remarks.add("限购数格式错误");
        }
        try {
            if (!ObjectUtils.isEmpty(rowData.getStoreClubDiscountRateStr())) {
                rowData.setStoreClubDiscountRate(Integer.valueOf(rowData.getStoreClubDiscountRateStr()));
            }
        } catch (NumberFormatException e) {
            remarks.add("粉丝折扣格式错误");
        }
        if (CollectionUtils.isNotEmpty(remarks)) {
            rowData.setResult("失败");
            rowData.setRemark(String.join(",", remarks));
            return rowData;
        }


        String uniqueSign = rowData.getAccountNumber() + "-" + rowData.getSingleDiscountId() + "-" + rowData.getItemId();
        //存在重复数据,仅处理最后一条
        if (excelMap.containsKey(uniqueSign)) {
            rowData.setResult("失败");
            rowData.setRemark("表格中存在该活动重复的商品,仅处理最后一条");
        } else {
            excelMap.put(uniqueSign, rowData);
        }
        return rowData;
    }

    @Override
    public ResponseJson aliexpressProductLadderPriceUpdate(String account, Long productId, DiscountParam discountParam, AliexpressProductLog productLog) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (productId == null || discountParam == null || StringUtils.isBlank(account)) {
            rsp.setMessage("参数为空！");
            return rsp;
        }
        EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
        productListingRequest.setAliexpressAccountNumber(account);
        productListingRequest.setProductId(productId);
        productListingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);

        if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
            rsp.setMessage("无es有效数据 请检查产品id 是否有效！产品id: " + productId);
            return rsp;
        }

        //改前
        String ladderPrice = esAliexpressProductListing.get(0).getLadderPrice();
        List<DiscountNum> bulk_discount_array = discountParam.getBulk_discount_array();

        StringBuffer sb = new StringBuffer();
        sb.append("改前:" + ladderPrice).append(" 改后: " + JSON.toJSONString(bulk_discount_array));
        productLog.setNewRemark(sb.toString());

        String aliexpressAccountNumber = esAliexpressProductListing.get(0).getAliexpressAccountNumber();
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
        discountParam.setProduct_id(productId.toString());
        WholesaleCall call = new WholesaleCall();
        rsp = call.priceUpdate(saleAccountByAccountNumber, discountParam);
        if (rsp.isSuccess()) {
            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                aliexpressProductListing.setLadderPrice(JSON.toJSONString(bulk_discount_array));
                aliexpressProductListing.setIsHasLadderPrice(true);
            }
            smtItemEsBulkProcessor.updateLadderPrice(esAliexpressProductListing);

            AliexpressHalfTgItemExample tgItemExample = new AliexpressHalfTgItemExample();
            tgItemExample.createCriteria().andProductIdEqualTo(productId);
            List<AliexpressHalfTgItem> tgItems = aliexpressHalfTgItemService.selectByExample(tgItemExample);

            for (AliexpressHalfTgItem tgItem : tgItems) {
                tgItem.setIsHasLadderPrice(true);
                tgItem.setLadderPrice(JSON.toJSONString(bulk_discount_array));
            }
            aliexpressHalfTgItemService.batchuUdate(tgItems);
        }
        return rsp;
    }

    @Override
    public List<AliexpressImageTitleResponse> getImageAndTitleData(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        List<AliexpressImageTitleResponse> resultList = new ArrayList<>();

        // 1. 获取在线列表数据
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setQueryFields(new String[]{"id", "productId", "articleNumber", "imageUrls", "subject", "aliexpressAccountNumber"});
        request.setIdList(ids);
        request.setProductStatusType("onSelling,auditing,editingRequired");
        List<EsAliexpressProductListing> productListings = esAliexpressProductListingService.getEsAliexpressProductListing(request);

        if (CollectionUtils.isEmpty(productListings)) {
            throw new BusinessException("无有效数据,请选择审核中，上架，审核不通过的商品！");
        }

        Set<String> spus = productListings.stream().map(item -> {
            String spu = item.getSpu();
            if (StringUtils.isBlank(spu)) {
                spu = ProductUtils.getMainSku(item.getArticleNumber());
                item.setSpu(spu);
            }
            return spu;
        }).collect(Collectors.toSet());
        List<SpuOfficial> spuOfficialTitles = ProductUtils.getSpuOfficialTitles(new ArrayList<>(spus));
        //收集spuOfficialTitles为一个Map，key为spu
        Map<String, List<String>> spuOfficialMap = spuOfficialTitles.stream().collect(Collectors.toMap(SpuOfficial::getSpu,
                spuOfficial -> {
                    ArrayList<String> titleList = new ArrayList<>();
                    // 解析 shortTitleJson
                    if (StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
                        List<String> shortTitles = JSON.parseArray(spuOfficial.getShortTitleJson(), String.class);
                        titleList.addAll(shortTitles);
                    }

                    // 解析 longTitleJson
                    if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
                        List<String> longTitles = JSON.parseArray(spuOfficial.getLongTitleJson(), String.class);
                        titleList.addAll(longTitles);
                    }
                    return titleList;
                }));


        // 2. 处理每个产品
        List<CompletableFuture<AliexpressImageTitleResponse>> futures = productListings.stream()
                .map(listing -> CompletableFuture.supplyAsync(() -> {
                    AliexpressImageTitleResponse response = new AliexpressImageTitleResponse();
                    String spu = listing.getSpu();
                    if (StringUtils.isBlank(spu)) {
                        spu = ProductUtils.getMainSku(listing.getArticleNumber());
                    }

                    // 设置基本信息
                    response.setProductId(listing.getProductId());
                    response.setArticleNumber(listing.getArticleNumber());
                    response.setAccount(listing.getAliexpressAccountNumber());
                    String imageUrls = listing.getImageUrls();
                    if (StringUtils.isNotBlank(imageUrls)) {
                        response.setOriginalMainImage(List.of(imageUrls.split(";")).get(0));
                    }

                    response.setOriginalTitle(listing.getSubject());

                    // 3. 从产品系统获取SPU信息
                    try {
                        // 获取SPU图片
                        List<String> images = FmsUtils.getSmtImgs(spu, null, new Boolean[]{true});
                        response.setSmtImages(images);

                        // 4. 获取店铺标题前后缀
                        AliexpressConfig detailForAccount = aliexpressConfigService.getDetailForAccount(listing.getAliexpressAccountNumber());
                        if (ObjectUtils.isEmpty(detailForAccount)) {
                            throw new BusinessException("获取店铺配置为空");
                        }

                        List<String> titles = spuOfficialMap.get(spu);
                        List<String> newTitles = CollectionUtils.isEmpty(titles) ? List.of() : titles.stream()
                                .map(title -> {
                                    Integer titleType = detailForAccount.getTitleType();
                                    String titleValue = detailForAccount.getTitleValue();
                                    if (!ObjectUtils.isEmpty(titleType)) {
                                        titleValue = StringUtils.defaultString(titleValue);
                                        if (titleType == 0) {
                                            return titleValue + " " + title;
                                        } else if (titleType == 1) {
                                            return title + " " + titleValue;
                                        }
                                    }
                                    return title;
                                })
                                .collect(Collectors.toList());

                        response.setTitles(newTitles);

                    } catch (Exception e) {
                        log.error("获取产品系统数据异常, productId={}, articleNumber={}", listing.getProductId(), listing.getArticleNumber(), e);
                    }

                    return response;
                }, AliexpressExecutors.GET_UPDATE_IMAGE_TITLE_POOL))
                .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        resultList = futures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("处理商品数据异常", e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return resultList;
    }

    @Override
    public void updateMainImgTitle(List<AliexpressUpdateImageTitleRequest> request) {
        String userName = WebUtils.getUserName();
        if (CollectionUtils.isEmpty(request)) {
            return;
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        request.forEach(item -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                DataContextHolder.setUsername(userName);
                ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
                try {
//                    //判断是否上传图片
//                    ApiResult<?> result;
//                    if(BooleanUtils.isTrue(item.getNeedUpload())){
//                         result = this.uploadPicture(item.getArticleNumber(), item.getImageFile());
//                        if (result.isSuccess() && !ObjectUtils.isEmpty(result.getResult())){
//                            String imgUrl = (String) ((JSONObject) result.getResult()).get("imgUrl");
//                            if (StringUtils.isNotBlank(imgUrl)){
//                                item.setMainImage(imgUrl);
//                            }
//                        }else {
//                            log.error("图片上传失败, articleNumber={},error={}", item.getArticleNumber(), result.getErrorMsg());
//                            rsp.setMessage(result.getErrorMsg());
//                        }
//                    }

                    if (ObjectUtils.isEmpty(item.getProductId()) || StringUtils.isBlank(item.getMainImage()) || StringUtils.isBlank(item.getAccountNumber())) {
                        throw new BusinessException("有参数为空");
                    }
                    //先同步
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, item.getAccountNumber());
                    JSONObject productEntity = OfferQueryProductOpenCall
                            .transResultToOfferUpdate(saleAccountByAccountNumber, item.getProductId());

                    // 4. 获取店铺标题前后缀
                    AliexpressConfig detailForAccount = aliexpressConfigService.getDetailForAccount(item.getAccountNumber());
                    if (ObjectUtils.isEmpty(detailForAccount)) {
                        throw new BusinessException("获取店铺配置为空");
                    }

                    String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, item.getMainImage(), StringUtils.isNotBlank(detailForAccount.getWatermarkTemplateIdStr()) ? 3 : null);
                    String imageURLs = productEntity.getString("image_u_r_ls");
                    if (StringUtils.isNotBlank(imageURLs)) {
                        List<String> imageList = Arrays.asList(imageURLs.split(";"));
                        imageList.set(0, imgUrlsWithPostedImg);
                        productEntity.put("image_u_r_ls", String.join(";", imageList));
                    } else {
                        productEntity.put("image_u_r_ls", imgUrlsWithPostedImg);
                    }

                    if (BooleanUtils.isTrue(item.getUpdateTitle()) && StringUtils.isNotBlank(item.getTitle())) {
                        // 去除侵权词
                        String subject = item.getTitle();

                        // 获取速卖通侵权词
                        SearchVo searchVo = new SearchVo();
                        searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                        searchVo.setText(subject);
                        ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
                        if (!checkResult.isSuccess()) {
                            String s = "调用校验侵权服务 " + checkResult.getErrorMsg();
                            throw new BusinessException(s);
                        }

                        //收集所有的侵权词，商标词
                        Set<String> infringementSet = new HashSet<>();
                        InfringmentResponse infringmentResponse = checkResult.getResult();
                        if (MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                            infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                        }

                        if (MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                            infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                        }
                        List<String> infringementList = new ArrayList<>(infringementSet);

                        subject = AliexpressTemplateDataUtils.delInfringementWord(subject, infringementList);
                        if (StringUtils.isBlank(subject)) {
                            throw new BusinessException("去除侵权词后标题为空!");

                        }

                        // 多语言标题
                        JSONArray subjectJSONArray = new JSONArray();
                        JSONObject subjectJsonObject = new JSONObject();
                        subjectJSONArray.add(subjectJsonObject);
                        subjectJsonObject.put("value", subject);
                        subjectJsonObject.put("locale", "en_US");
                        productEntity.put("subject_list", subjectJSONArray);
                    }

                    rsp = OfferEditProductOpenCall.offerEditProduct(saleAccountByAccountNumber, productEntity.toJSONString());

                } catch (Exception e) {
                    log.error("更新主图和标题失败", e);
                    rsp.setMessage(e.getMessage());
                }

                //添加操作报告
                AliexpressProduct aliexpressProduct = new AliexpressProduct();
                aliexpressProduct.setAliexpressAccountNumber(item.getAccountNumber());
                aliexpressProduct.setArticleNumber(item.getArticleNumber());
                aliexpressProduct.setProductId(item.getProductId());
                aliexpressProduct.setOperator(StringUtils.isEmpty(DataContextHolder.getUsername()) ? WebUtils.getUserName() : DataContextHolder.getUsername());
                aliexpressProduct.setErrorTip(rsp.getMessage());
                aliexpressProductLogService.updateProductImgAndTitleCreate(aliexpressProduct, StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, rsp.getStatus()));
            }, AliexpressExecutors.UPDATE_IMAGE_TITLE_POOL);
        });

    }

    @Override
    public String batchClearHalfCountryExitLabel(EsAliexpressProductListingRequest request) {
        try {
            // 1. 查询符合条件的产品列表
            List<EsAliexpressProductListing> productList = esAliexpressProductListingService.getEsAliexpressProductListing(request);
            if (CollectionUtils.isEmpty(productList)) {
                return "未找到符合条件的产品";
            }

            // 2. 过滤出有半托管退出标签的产品
            List<EsAliexpressProductListing> hasLabelProducts = productList.stream()
                    .filter(product -> StringUtils.isNotBlank(product.getHalfCountryExitLabel()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(hasLabelProducts)) {
                return "所选产品中没有半托管退出标签需要清除";
            }

            // 3. 批量更新ES数据
            for (EsAliexpressProductListing product : hasLabelProducts) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("halfCountryExitLabel", null); // 设置为null清除标签
                String jsonString = new ObjectMapper().writeValueAsString(updateMap);
                esAliexpressProductListingService.updateRequest(jsonString, product.getId());

                LambdaUpdateWrapper<SmtHalfExitCountry> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SmtHalfExitCountry::getAccount, product.getAliexpressAccountNumber());
                updateWrapper.eq(SmtHalfExitCountry::getProductId, product.getProductId());
                updateWrapper.in(SmtHalfExitCountry::getExamineStatus, Arrays.asList(HalfExitCountryExamineStatusEnum.S_0.getCode()));

                SmtHalfExitCountry updateSmtHalfExitCountry = new SmtHalfExitCountry();
                updateSmtHalfExitCountry.setExamineStatus(HalfExitCountryExamineStatusEnum.S_3.getCode());
                updateSmtHalfExitCountry.setUpdateBy(WebUtils.getUserName());
                updateSmtHalfExitCountry.setUpdatedTime(new Date());
                updateSmtHalfExitCountry.setFailRemark("半托管退出标签已清除");
                smtHalfExitCountryService.update(updateSmtHalfExitCountry, updateWrapper);
            }
            // 5. 记录操作日志
            log.info("批量去除半托管退出标签完成，共处理{}个产品", hasLabelProducts.size());
            return null; // 成功返回null
        } catch (Exception e) {
            log.error("批量去除半托管退出标签异常", e);
            return "操作失败：" + e.getMessage();
        }
    }

    private ApiResult<?> uploadPicture( String sku,
                                                  MultipartFile file) {
        Asserts.isTrue(StringUtils.isNotBlank(sku), ErrorCode.PARAM_EMPTY_ERROR);
        String url = PictureCommon.SMT_PICTURE_ROUTE;
        String fileName = file.getOriginalFilename();
        try {
            //FtpUtils.uploadFile("AliexpressTemplate/" + imgType + "/", fileName, file.getInputStream());
            Map<String, Object> rspMap = pictureUploadService.uploadPicture(url, file, null);
            if (rspMap == null){
                return ApiResult.newError("上传失败");
            }
            if (rspMap.get("success").equals(true)) {
                Map<String, Object> body = new HashMap<>();
                body.put("imgUrl", rspMap.get("result").toString());
                body.put("fileName", fileName);
                return ApiResult.newSuccess(body);
            }
        } catch (Exception e) {
            // e.printStackTrace();
            return ApiResult.newError("上传失败:" + e.getMessage());
        }

        return ApiResult.newSuccess();
    }
}
