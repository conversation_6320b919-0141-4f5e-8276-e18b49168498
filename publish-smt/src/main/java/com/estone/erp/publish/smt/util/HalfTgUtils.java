package com.estone.erp.publish.smt.util;

import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class HalfTgUtils {

    public static void assembleProductInfo(AliexpressHalfTgItem sourceItem) {
        if (StringUtils.isBlank(sourceItem.getArticleNumber())) {
            return;
        }
        // 产品系统信息补充
        ProductInfoVO skuInfo = ProductUtils.getSkuInfo(sourceItem.getArticleNumber());
        setProductInfo(sourceItem, skuInfo);
    }

    /**
     * 设置产品系统信息
     * @param sourceItem
     * @param productInfoVO
     */
    public static void setProductInfo(AliexpressHalfTgItem sourceItem, ProductInfoVO productInfoVO) {
        String articleNumber = sourceItem.getArticleNumber();
        try{
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(articleNumber);
            }

            if (null != productInfoVO  && StringUtils.isNotBlank(productInfoVO.getSonSku())) {
                String skuStatus = productInfoVO.getSkuStatus();
                if(StringUtils.equalsIgnoreCase(skuStatus, SkuStatusEnum.DISCARD.getCode())){
                    String mergeSku = ProductUtils.getMergeSku(articleNumber);
                    //不一样就从新查询产品信息
                    if(!StringUtils.equalsIgnoreCase(articleNumber, mergeSku)){
                        //重新查询产品信息
                        productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                    }
                    articleNumber = mergeSku;
                }
                //重设货号
                sourceItem.setArticleNumber(articleNumber);
                // 产品系统类目
                sourceItem.setProCategoryId(productInfoVO.getCatId());
                sourceItem.setProCategoryIdPath(productInfoVO.getCategoryId());
                sourceItem.setProCategoryCnName(productInfoVO.getCategoryCnName());
                // 特殊标签
                sourceItem.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
                // 单品状态
                sourceItem.setSkuStatus(productInfoVO.getSkuStatus());
                // 产品标签
                sourceItem.setSkuTagCode(productInfoVO.getTagCodes());
                // 禁售平台
                sourceItem.setForbidChannel(productInfoVO.getForbidChannel());
                // 禁售原因
                sourceItem.setInfringementObj(productInfoVO.getInfringementObj());
                // 禁售类型
                sourceItem.setInfringementTypeName(productInfoVO.getInfringementTypeName());
                // 禁售站点
                List<String> prohibitionSites = productInfoVO.getProhibitionSiteWithPlatformDefaultSite();
                if (CollectionUtils.isNotEmpty(prohibitionSites)) {
                    String prohibitionSiteStr = String.join(",", prohibitionSites);
                    sourceItem.setProhibitionSites("," + prohibitionSiteStr + ",");
                }
                // 是否促销
                sourceItem.setPromotion(productInfoVO.getPromotion());
                // 是否新品
                sourceItem.setNewState(productInfoVO.getNewState());
                sourceItem.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            }else{
                // 匹配套装
                matchComposeProduct(sourceItem);
            }

        }catch (Exception e){
            log.error(String.format("同步listing数据获取产品SKu信息: %s, ", articleNumber), e);
        }
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param item  listing
     */
    public static Boolean matchComposeProduct(AliexpressHalfTgItem item) {
        String articleNumber = item.getArticleNumber();
        log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",item.getAccount(), articleNumber);
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(item, composeProduct);
            return true;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (org.apache.commons.collections4.MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {

            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return false;
            }

            item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            item.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
//            item.setComposeStatus(ComposeCheckStepEnum.NORMAL.getCode());

            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            item.setComposeStatus(stepEnum.getCode());

            // 禁售平台
            item.setForbidChannel(StringUtils.join(suiteSku.getForbidChannels(),","));
            // 禁售类型
            item.setInfringementTypeName(StringUtils.join(suiteSku.getInfringementTypeNames(), ","));
            // 禁售原因
            item.setInfringementObj(StringUtils.join(suiteSku.getInfringementObjs(), ","));
            // 禁售站点
            item.setProhibitionSites(StringUtils.join(suiteSku.getProhibitionPlatSites(),","));
            return true;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(item, composeProductRef);
            return true;
        }
        return false;
    }

    private static void setProductInfoByCompose(AliexpressHalfTgItem item, ComposeSku composeProduct) {
        item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        item.setArticleNumber(composeProduct.getComposeSku());
        item.setComposeStatus(composeProduct.getCheckStep());

        String categoryName =StringUtils.isNotBlank(composeProduct.getCategoryName()) ? composeProduct.getCategoryName().replaceAll(">", ",") : null;
        item.setProCategoryCnName(categoryName);
        // 禁售平台
        item.setForbidChannel(StringUtils.join(composeProduct.getForbidChannels(),","));
        // 禁售类型
        item.setInfringementTypeName(StringUtils.join(composeProduct.getInfringementTypeNames(), ","));
        // 禁售原因
        item.setInfringementObj(StringUtils.join(composeProduct.getInfringementObjs(), ","));
        // 禁售站点
        item.setProhibitionSites(StringUtils.join(composeProduct.getProhibitionPlatSites(),","));
        // 单品状态
        Integer composeStatus = composeProduct.getComposeStatus();
        if(composeStatus != null){
            item.setSkuStatus(SingleItemEnum.getEnNameByCode(composeStatus));
        }
        // 产品标签code
        item.setSkuTagCode(composeProduct.getTagCode());
        // 特殊标签
        item.setSpecialGoodsCode(null);
        // 是否促销
        item.setPromotion(0);
        // 是否新品
        item.setNewState(false);
    }
}
