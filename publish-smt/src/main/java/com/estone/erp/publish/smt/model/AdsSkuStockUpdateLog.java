package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AdsSkuStockUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ads_sku_stock_update_log.id
     */
    private Integer id;

    /**
     *  database column ads_sku_stock_update_log.sku
     */
    private String sku;

    /**
     * 修改前的库存 database column ads_sku_stock_update_log.original_quantity
     */
    private Integer originalQuantity;

    /**
     * 修改后的库存 database column ads_sku_stock_update_log.quantity_updated
     */
    private Integer quantityUpdated;

    /**
     *  database column ads_sku_stock_update_log.content
     */
    private String content;

    /**
     * 修改前的冻结库存 database column ads_sku_stock_update_log.before_value_frozen
     */
    private Integer beforeValueFrozen;

    /**
     * 修改后的冻结库存 database column ads_sku_stock_update_log.after_value_frozen
     */
    private Integer afterValueFrozen;

    /**
     * 创建时间 database column ads_sku_stock_update_log.created_date
     */
    private Timestamp createdDate;

    /**
     * 0:订单系统，1：大数据 database column ads_sku_stock_update_log.createdby
     */
    private Integer createdby;
}