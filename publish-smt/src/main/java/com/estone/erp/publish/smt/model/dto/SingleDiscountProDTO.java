
package com.estone.erp.publish.smt.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleDiscountProDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id,编辑时必传
     */
    private Long id;


    /**
     * itemId,商品id
     */
    @NotNull(message = "itemId不能为空")
    private Long itemId;

    /**
     * 折扣率
     */
//    @NotNull(message = "折扣率不能为空")
    private Integer discount;

    /**
     * 定向人群额外折扣类型,0=无,1=店铺粉丝
     */
//    @NotNull(message = "定向人群额外折扣类型不能为空")
    private Integer club_discount_type;



    /**
     * 粉丝折扣率
     */
    private Integer store_club_discount_rate;


    /**
     * 限购数
     */
    private Integer buy_max_num;

    /**
     * 状态,0=未参加，1=已参加
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}