package com.estone.erp.publish.smt.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class NewProductUtils {

    /**
     * 新品推荐排除组长（分配的时候去除）
     */
    public static List<String> excludeNewProductLeaderList = Arrays.asList("3381","031","KJ031");

    /**
     * 新品推荐排除销售
     */
    public static List<String> excludeNewProductSalesList = Arrays.asList("031","KJ031");

    /**
     * 其他地方都需要去除
     */
    public static List<String> excludeLeaderList = Arrays.asList("031","KJ031");

    /**
     * 统计数据需要加上
     */
    public static List<String> addLeaderList = Arrays.asList("3381");

    public static boolean isAllProhibitions(List<ProductInfo> productInfoList){
        Map<String, Set<String>> allSkuSalesProhibitions = getAllSkuSalesProhibitions(productInfoList);

        boolean isAllProhibitions = true;
        if(allSkuSalesProhibitions.isEmpty()){
            isAllProhibitions = false;
        }
        for (Map.Entry<String, Set<String>> stringSetEntry : allSkuSalesProhibitions.entrySet()) {
            Set<String> value = stringSetEntry.getValue();
            if(CollectionUtils.isEmpty(value)){
                isAllProhibitions = false;
                break;
            }
        }
        return isAllProhibitions;
    }


    private static Map<String, Set<String>> getAllSkuSalesProhibitions(List<ProductInfo> productInfoList) {
        Map<String, SalesProhibitionsVo> salesProhibitionsVoMap = new HashMap<>();
        for (ProductInfo productInfo : productInfoList) {
            List<SalesProhibitionsVo> salesProhibitionsVos = productInfo.getSalesProhibitionsVos();
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                SalesProhibitionsVo salesProhibitionsVo = new SalesProhibitionsVo();
                salesProhibitionsVo.setPlat(SaleChannel.CHANNEL_SMT);
                salesProhibitionsVo.setSites(List.of());
                salesProhibitionsVoMap.put(productInfo.getSonSku(), salesProhibitionsVo);
                continue;
            }
            Optional<SalesProhibitionsVo> first1 = salesProhibitionsVos.stream().filter(a -> SaleChannel.CHANNEL_SMT.equalsIgnoreCase(a.getPlat())).findFirst();
            if (first1.isPresent()) {
                salesProhibitionsVoMap.put(productInfo.getSonSku(), first1.get());
            } else {
                SalesProhibitionsVo salesProhibitionsVo = new SalesProhibitionsVo();
                salesProhibitionsVo.setPlat(SaleChannel.CHANNEL_SMT);
                salesProhibitionsVo.setSites(List.of());
                salesProhibitionsVoMap.put(productInfo.getSonSku(), salesProhibitionsVo);
            }
        }
        Map<String, Set<String>> skuAndSitesMap = new HashMap<>();
        for (Map.Entry<String, SalesProhibitionsVo> skuAndSalesProhibitionsVoEntry : salesProhibitionsVoMap.entrySet()) {
            SalesProhibitionsVo value = skuAndSalesProhibitionsVoEntry.getValue();
            List<Sites> sites = value.getSites();
            String sku = skuAndSalesProhibitionsVoEntry.getKey();
            if (CollectionUtils.isEmpty(sites)) {
                skuAndSitesMap.put(sku, Set.of());
                continue;
            }
            Set<String> collect = sites.stream().map(Sites::getSite).filter(StringUtils::isNotBlank).map(String::toUpperCase).collect(Collectors.toSet());
            skuAndSitesMap.put(sku, collect);
        }

        return skuAndSitesMap;
    }
}
