package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * EPR收费标准对应国家枚举 后续新增国家需修改该枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 18:23
 */
public enum EprFeeCountryEnum {

    FRANCE("法国", "法国 *", "FR"),
    SPAIN("西班牙", "西班牙", "ES"),
    FRANCE_EN("法国", "France *", "FR"),
    SPAIN_EN("西班牙", "Spain", "ES"),
    SPAIN_EN_1("西班牙", "Spain\n", "ES"),
    ;

    private String name;

    // 数据库存储的真实名称
    private String realName;

    private String code;

    private EprFeeCountryEnum(String name, String realName, String code) {
        this.name = name;
        this.realName = realName;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static String getRealNameByName(String name) {
        EprFeeCountryEnum[] values = values();
        for (EprFeeCountryEnum type : values) {
            if (type.name.equals(name)) {
                return type.getRealName();
            }
        }
        return null;
    }

    public static String getCodeByRealName(String realName) {
        EprFeeCountryEnum[] values = values();
        for (EprFeeCountryEnum type : values) {
            if (type.realName.equals(realName)) {
                return type.getCode();
            }
        }
        return null;
    }

    /**
     * 获取币种
     * @param realName
     * @param priceText
     * @return
     */
    public static String getCurrency(String realName, String priceText) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(priceText)) {
            return null;
        }

        List<String> realNameList = new ArrayList<>();
        for (EprFeeCountryEnum countryEnum : EprFeeCountryEnum.values()) {
            realNameList.add(countryEnum.getRealName());
        }

        String currency = "EUR";
        if (realNameList.contains(realName) && priceText.contains("€")) {
            return currency;
        }

        return null;
    }

    /**
     * 转换价格
     * @param realName
     * @param priceText
     * @return
     */
    public static Double getPrice(String realName, String priceText) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(priceText)) {
            return null;
        }

        List<String> realNameList = new ArrayList<>();
        for (EprFeeCountryEnum countryEnum : EprFeeCountryEnum.values()) {
            realNameList.add(countryEnum.getRealName());
        }

        if (realNameList.contains(realName)) {
            priceText = priceText.trim().replace("€", "").replace(",", ".");
            try {
                return Double.parseDouble(priceText);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
