package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressDataView implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_data_view.id
     */
    private Integer id;

    /**
     * 店铺 database column aliexpress_data_view.account_number
     */
    private String accountNumber;

    /**
     * 销售 database column aliexpress_data_view.sale_user
     */
    private String saleUser;

    /**
     * 类型 database column aliexpress_data_view.type
     */
    private Integer type;

    /**
     * 状态 database column aliexpress_data_view.status
     */
    private Integer status;

    /**
     * 模板数量 database column aliexpress_data_view.template_num
     */
    private Integer templateNum;

    /**
     * listing数量 database column aliexpress_data_view.listing_num
     */
    private Integer listingNum;

    /**
     * 统计数据1 database column aliexpress_data_view.count_num1
     */
    private String countNum1;

    /**
     * 统计数据2 database column aliexpress_data_view.count_num2
     */
    private String countNum2;

    /**
     * 统计数据3 database column aliexpress_data_view.count_num3
     */
    private String countNum3;

    /**
     * 统计数据4 database column aliexpress_data_view.count_num4
     */
    private String countNum4;

    /**
     * 统计数据5 database column aliexpress_data_view.count_num5
     */
    private String countNum5;

    /**
     * 统计数据6 database column aliexpress_data_view.count_num6
     */
    private String countNum6;

    /**
     * 创建时间 database column aliexpress_data_view.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column aliexpress_data_view.created_by
     */
    private String createdBy;
}