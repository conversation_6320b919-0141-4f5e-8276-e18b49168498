package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.util.NewProductUtils;
import com.estone.erp.publish.system.hr.HrClient;
import com.estone.erp.publish.system.hr.model.HrNewUser;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtNewProductPublishLeaderConfigMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductPublishLeaderConfig;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductPublishLeaderConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class SmtNewProductPublishLeaderConfigServiceImpl extends ServiceImpl<SmtNewProductPublishLeaderConfigMapper, SmtNewProductPublishLeaderConfig>
    implements SmtNewProductPublishLeaderConfigService{

    @Resource
    private HrClient hrClient;

    @Override
    public List<SmtNewProductPublishLeaderConfig> getList() {
        // 同步平台组长信息
        this.syncLeaderChanges();

        // 获取所有配置信息
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        LambdaQueryWrapper<SmtNewProductPublishLeaderConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (!superAdminOrEquivalent.getResult()) {
            //需要查询当前人管理的下级人员 eg：主管
            ApiResult<List<NewUser>> result = NewUsermgtUtils.listSecondaryTeamByEmployeeNo(SaleChannel.CHANNEL_SMT, WebUtils.getUserName());
            if(!result.isSuccess()){
                throw new RuntimeException(result.getErrorMsg());
            }
            Set<String> authSaleLeader = result.getResult()
                    .stream()
                    .map(NewUser::getEmployeeNo)
                    .collect(Collectors.toSet());
            authSaleLeader.add(WebUtils.getUserName()); //添加自己
            configLambdaQueryWrapper.in(SmtNewProductPublishLeaderConfig::getSaleLeader, authSaleLeader);
        }
        return this.list(configLambdaQueryWrapper);
    }

    @Override
    public void syncLeaderChanges() {
        try {

            Map<String, String> argsMap = new HashMap<>(2);
            argsMap.put("args", SaleChannel.CHANNEL_SMT);
            ApiResult<List<HrNewUser>> lastLeaderMembersByPlatformResult = hrClient.getLastLeaderMembersByPlatform(JSON.toJSONString(argsMap));
            if(!lastLeaderMembersByPlatformResult.isSuccess()){
                log.error("smt新品推荐 获取HR 组长结构异常" + lastLeaderMembersByPlatformResult.getErrorMsg());
                return;
            }

            // 获取平台所有组长信息 需要排除特殊人员
            List<HrNewUser> hrNewUserListUser = lastLeaderMembersByPlatformResult.getResult();
            if(CollectionUtils.isEmpty(hrNewUserListUser)){
                log.info("smt新品推荐 hr 返回 无组长");
                return;
            }

            Set<String> leaderInfo = hrNewUserListUser.stream()
                    .map(HrNewUser::getEmployeeNo)
                    .filter(t -> !NewProductUtils.excludeLeaderList.contains(t))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(leaderInfo)) {
                log.info("smt新品推荐 无组长");
                return;
            }
            // 获取现有配置中的组长列表
            List<SmtNewProductPublishLeaderConfig> existingConfigs = this.list(
                    new LambdaQueryWrapper<SmtNewProductPublishLeaderConfig>().select(SmtNewProductPublishLeaderConfig::getSaleLeader)
            );

            // 如果配置表为空，直接插入所有组长
            if (CollectionUtils.isEmpty(existingConfigs)) {
                List<SmtNewProductPublishLeaderConfig> newConfigs = buildNewConfigs(leaderInfo);
                this.saveBatch(newConfigs);
                return;
            }

            // 判断是否需要新增或删除数据
            Set<String> existingLeaders = existingConfigs.stream()
                    .map(SmtNewProductPublishLeaderConfig::getSaleLeader)
                    .collect(Collectors.toSet());

            // 判断新增的组长(判断组长接口的数据和配置表中的数据是否有差异)
            Set<String> leadersToAdd = new HashSet<>(leaderInfo);
            leadersToAdd.removeAll(existingLeaders);
            if (!leadersToAdd.isEmpty()) {
                List<SmtNewProductPublishLeaderConfig> newConfigs = buildNewConfigs(leadersToAdd);
                this.saveBatch(newConfigs);
            }

            // 判断删除的组长
            Set<String> leadersToRemove = new HashSet<>(existingLeaders);
            leadersToRemove.removeAll(leaderInfo);
            if (!leadersToRemove.isEmpty()) {
                this.remove(new LambdaQueryWrapper<SmtNewProductPublishLeaderConfig>().in(SmtNewProductPublishLeaderConfig::getSaleLeader, leadersToRemove));
            }
        } catch (Exception e) {
            log.error("同步组长配置失败", e);
        }
    }

    /**
     * 构建新的组长配置列表
     *
     * @param leaders 组长列表
     * @return 配置对象列表
     */
    private List<SmtNewProductPublishLeaderConfig> buildNewConfigs(Set<String> leaders) {
        Date now = new Date();
        return leaders.stream()
                .map(leader -> {
                    SmtNewProductPublishLeaderConfig config = new SmtNewProductPublishLeaderConfig();
                    config.setSaleLeader(leader);
                    config.setCreatedTime(now);
                    config.setUpdatedTime(now);
                    return config;
                })
                .collect(Collectors.toList());
    }
}




