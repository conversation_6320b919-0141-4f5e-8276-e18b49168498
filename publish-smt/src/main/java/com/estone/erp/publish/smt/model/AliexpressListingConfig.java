package com.estone.erp.publish.smt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AliexpressListingConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column aliexpress_listing_config.id
     */
    private Integer id;

    /**
     * 规则名称 database column aliexpress_listing_config.rule_name
     */
    private String ruleName;

    /**
     * 店铺信息 database column aliexpress_listing_config.store_information
     */
    private String storeInformation;

    /**
     * 相关的配置json database column aliexpress_listing_config.config_json
     */
    private String configJson;

    /**
     * 相关的时间json database column aliexpress_listing_config.time_json
     */
    private String timeJson;

    /**
     * 额外json1 database column aliexpress_listing_config.extra_json1
     */
    private String extraJson1;

    /**
     * 额外json2 database column aliexpress_listing_config.extra_json2
     */
    private String extraJson2;

    /**
     * 额外json3 database column aliexpress_listing_config.extra_json3
     */
    private String extraJson3;

    /**
     * 启用状态1启用2禁用 database column aliexpress_listing_config.status
     */
    private Integer status;

    /**
     * 规则类型1加入半托管2pop配置3pop指定sku database column aliexpress_listing_config.rule_type
     */
    private Integer ruleType;

    /**
     * 创建人 database column aliexpress_listing_config.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_listing_config.create_time
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

    /**
     * 修改人 database column aliexpress_listing_config.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_listing_config.update_time
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;


    /**
     * 店铺类型 1 店铺分组 2 店铺
     */
    private Integer accountType;

    /**
     * 店铺分组名称
     */
    private String accountGroupNames;

    /**
     * 店铺分组id
     */
    private String accountGroupIds;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 单个店铺,发送MQ时用
     */
    private String singleStore;


}