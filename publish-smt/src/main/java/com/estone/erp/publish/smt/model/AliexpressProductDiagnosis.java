package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import lombok.Data;

@Data
public class AliexpressProductDiagnosis implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column aliexpress_product_diagnosis.id
     */
    private Integer id;

    /**
     * 店铺账号 database column aliexpress_product_diagnosis.account
     */
    private String account;

    /**
     * 产品ID database column aliexpress_product_diagnosis.product_id
     */
    private Long productId;

    /**
     * 问题商品名称 database column aliexpress_product_diagnosis.product_title
     */
    private String productTitle;

    /**
     * 商品主图url database column aliexpress_product_diagnosis.main_picture
     */
    private String mainPicture;

    /**
     * 商品问题描述 database column aliexpress_product_diagnosis.problem_description
     */
    private String problemDescription;

    /**
     * 商品问题类型 database column aliexpress_product_diagnosis.problem_type
     */
    private String problemType;

    /**
     * 商品影响类型 database column aliexpress_product_diagnosis.product_impact
     */
    private String productImpact;

    /**
     * 商品优化建议 database column aliexpress_product_diagnosis.optimization_suggestion
     */
    private String optimizationSuggestion;

    /**
     * 商品的操作状态： -1（已优化） 0（待优化） 1（已操作、检测中） 2（已下架） 3（已忽略） 4（已删除） database column aliexpress_product_diagnosis.operate_status
     */
    private Integer operateStatus;

    /**
     * 创建人 database column aliexpress_product_diagnosis.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_product_diagnosis.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_product_diagnosis.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column aliexpress_product_diagnosis.last_update_date
     */
    private Timestamp lastUpdateDate;

    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;

    private Integer categoryId;

    private String categoryName;

    //在线列表相关数据
    private List<EsAliexpressProductListing> esAliexpressProductListing;
}