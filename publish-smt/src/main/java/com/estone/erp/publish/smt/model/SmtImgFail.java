package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtImgFail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * url
     */
    private String url;

    /**
     * 错误信息
     */
    private String failInfo;

    /**
     * 创建时间
     */
    private Timestamp createDate;

    /**
     * 店铺
     */
    private String account;

    /**
     * 是否token异常
     */
    private Boolean tokenError;
}