package com.estone.erp.publish.smt.enums;

public enum HalfLableNewEnum {
    cellbattery("cellbattery", "274526"),
    cellbatteryandmagnetism("cellbatteryandmagnetism", "274526"),
    rechargeablebattery("rechargeablebattery", "274526"),
    rechargeablebatteryandmagnetism("rechargeablebatteryandmagnetism", "274526"),
    magnetism("magnetism", "274452"),
    resistance("resistance", ""),
    solar_cell("solar cell", "274526"),
    powder("powder", "274511"),
    Pump("Pump", "274259"),
    liquid("liquid", "274259"),
    large_goods("large goods", ""),
    Commemorative_Coins("Commemorative Coins", ""),
    grease("grease", "274363"),
    Mask_products("Mask products", ""),
    black_mask("black mask", ""),
    Capacitor("Capacitor", "274526"),
    normal_goods("normal goods", ""),
    ;

    private String productTag;
    private String platformTag;

    private HalfLableNewEnum(String productTag, String platformTag) {
        this.productTag = productTag;
        this.platformTag = platformTag;
    }

    public String getProductTag() {
        return productTag;
    }

    public void setProductTag(String productTag) {
        this.productTag = productTag;
    }

    public String getPlatformTag() {
        return platformTag;
    }

    public void setPlatformTag(String platformTag) {
        this.platformTag = platformTag;
    }
}
