package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AliexpressFullReductionRuleConfigDto {
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动开始时间
     */
    private String activityStartTime;
    /**
     * 活动结束时间
     */
    private String activityEndTime;
    /**
     * 活动适用范围
     */
    private String activityScope;
    /**
     * 满减适用国家
     */
    private String activityCountry;
    /**
     * 活动类型1.满立减 2.满立折
     */
    private Integer activityType;
    /**
     * 活动详情参数
     */
    private List<AliexpressFullReductionGradientDto> activityDetail;
    /**
     * 优惠是否可累加 0不可以 1可以
     */
    private Integer isAccumulation;
    /**
     * 活动详情
     */
    private String activityDetailContext;

}
