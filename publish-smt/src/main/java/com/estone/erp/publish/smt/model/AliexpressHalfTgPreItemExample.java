package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class AliexpressHalfTgPreItemExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String fields;

    public AliexpressHalfTgPreItemExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProductImageIsNull() {
            addCriterion("product_image is null");
            return (Criteria) this;
        }

        public Criteria andProductImageIsNotNull() {
            addCriterion("product_image is not null");
            return (Criteria) this;
        }

        public Criteria andProductImageEqualTo(String value) {
            addCriterion("product_image =", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotEqualTo(String value) {
            addCriterion("product_image <>", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageGreaterThan(String value) {
            addCriterion("product_image >", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageGreaterThanOrEqualTo(String value) {
            addCriterion("product_image >=", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLessThan(String value) {
            addCriterion("product_image <", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLessThanOrEqualTo(String value) {
            addCriterion("product_image <=", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLike(String value) {
            addCriterion("product_image like", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotLike(String value) {
            addCriterion("product_image not like", value, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageIn(List<String> values) {
            addCriterion("product_image in", values, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotIn(List<String> values) {
            addCriterion("product_image not in", values, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageBetween(String value1, String value2) {
            addCriterion("product_image between", value1, value2, "productImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotBetween(String value1, String value2) {
            addCriterion("product_image not between", value1, value2, "productImage");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductStatusIsNull() {
            addCriterion("product_status is null");
            return (Criteria) this;
        }

        public Criteria andProductStatusIsNotNull() {
            addCriterion("product_status is not null");
            return (Criteria) this;
        }

        public Criteria andProductStatusEqualTo(String value) {
            addCriterion("product_status =", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusNotEqualTo(String value) {
            addCriterion("product_status <>", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusGreaterThan(String value) {
            addCriterion("product_status >", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusGreaterThanOrEqualTo(String value) {
            addCriterion("product_status >=", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusLessThan(String value) {
            addCriterion("product_status <", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusLessThanOrEqualTo(String value) {
            addCriterion("product_status <=", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusLike(String value) {
            addCriterion("product_status like", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusNotLike(String value) {
            addCriterion("product_status not like", value, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusIn(List<String> values) {
            addCriterion("product_status in", values, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusNotIn(List<String> values) {
            addCriterion("product_status not in", values, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusBetween(String value1, String value2) {
            addCriterion("product_status between", value1, value2, "productStatus");
            return (Criteria) this;
        }

        public Criteria andProductStatusNotBetween(String value1, String value2) {
            addCriterion("product_status not between", value1, value2, "productStatus");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(String value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(String value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(String value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(String value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(String value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLike(String value) {
            addCriterion("sku_id like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotLike(String value) {
            addCriterion("sku_id not like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<String> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<String> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(String value1, String value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(String value1, String value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceIsNull() {
            addCriterion("max_sku_price is null");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceIsNotNull() {
            addCriterion("max_sku_price is not null");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceEqualTo(Double value) {
            addCriterion("max_sku_price =", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceNotEqualTo(Double value) {
            addCriterion("max_sku_price <>", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceGreaterThan(Double value) {
            addCriterion("max_sku_price >", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("max_sku_price >=", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceLessThan(Double value) {
            addCriterion("max_sku_price <", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceLessThanOrEqualTo(Double value) {
            addCriterion("max_sku_price <=", value, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceIn(List<Double> values) {
            addCriterion("max_sku_price in", values, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceNotIn(List<Double> values) {
            addCriterion("max_sku_price not in", values, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceBetween(Double value1, Double value2) {
            addCriterion("max_sku_price between", value1, value2, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMaxSkuPriceNotBetween(Double value1, Double value2) {
            addCriterion("max_sku_price not between", value1, value2, "maxSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceIsNull() {
            addCriterion("min_sku_price is null");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceIsNotNull() {
            addCriterion("min_sku_price is not null");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceEqualTo(Double value) {
            addCriterion("min_sku_price =", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceNotEqualTo(Double value) {
            addCriterion("min_sku_price <>", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceGreaterThan(Double value) {
            addCriterion("min_sku_price >", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("min_sku_price >=", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceLessThan(Double value) {
            addCriterion("min_sku_price <", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceLessThanOrEqualTo(Double value) {
            addCriterion("min_sku_price <=", value, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceIn(List<Double> values) {
            addCriterion("min_sku_price in", values, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceNotIn(List<Double> values) {
            addCriterion("min_sku_price not in", values, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceBetween(Double value1, Double value2) {
            addCriterion("min_sku_price between", value1, value2, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andMinSkuPriceNotBetween(Double value1, Double value2) {
            addCriterion("min_sku_price not between", value1, value2, "minSkuPrice");
            return (Criteria) this;
        }

        public Criteria andProductpriceIsNull() {
            addCriterion("productPrice is null");
            return (Criteria) this;
        }

        public Criteria andProductpriceIsNotNull() {
            addCriterion("productPrice is not null");
            return (Criteria) this;
        }

        public Criteria andProductpriceEqualTo(Double value) {
            addCriterion("productPrice =", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceNotEqualTo(Double value) {
            addCriterion("productPrice <>", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceGreaterThan(Double value) {
            addCriterion("productPrice >", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceGreaterThanOrEqualTo(Double value) {
            addCriterion("productPrice >=", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceLessThan(Double value) {
            addCriterion("productPrice <", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceLessThanOrEqualTo(Double value) {
            addCriterion("productPrice <=", value, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceIn(List<Double> values) {
            addCriterion("productPrice in", values, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceNotIn(List<Double> values) {
            addCriterion("productPrice not in", values, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceBetween(Double value1, Double value2) {
            addCriterion("productPrice between", value1, value2, "productprice");
            return (Criteria) this;
        }

        public Criteria andProductpriceNotBetween(Double value1, Double value2) {
            addCriterion("productPrice not between", value1, value2, "productprice");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeIsNull() {
            addCriterion("full_path_code is null");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeIsNotNull() {
            addCriterion("full_path_code is not null");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeEqualTo(String value) {
            addCriterion("full_path_code =", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeNotEqualTo(String value) {
            addCriterion("full_path_code <>", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeGreaterThan(String value) {
            addCriterion("full_path_code >", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeGreaterThanOrEqualTo(String value) {
            addCriterion("full_path_code >=", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeLessThan(String value) {
            addCriterion("full_path_code <", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeLessThanOrEqualTo(String value) {
            addCriterion("full_path_code <=", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeLike(String value) {
            addCriterion("full_path_code like", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeNotLike(String value) {
            addCriterion("full_path_code not like", value, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeIn(List<String> values) {
            addCriterion("full_path_code in", values, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeNotIn(List<String> values) {
            addCriterion("full_path_code not in", values, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeBetween(String value1, String value2) {
            addCriterion("full_path_code between", value1, value2, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andFullPathCodeNotBetween(String value1, String value2) {
            addCriterion("full_path_code not between", value1, value2, "fullPathCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andSaleCostIsNull() {
            addCriterion("sale_cost is null");
            return (Criteria) this;
        }

        public Criteria andSaleCostIsNotNull() {
            addCriterion("sale_cost is not null");
            return (Criteria) this;
        }

        public Criteria andSaleCostEqualTo(Double value) {
            addCriterion("sale_cost =", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostNotEqualTo(Double value) {
            addCriterion("sale_cost <>", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostGreaterThan(Double value) {
            addCriterion("sale_cost >", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostGreaterThanOrEqualTo(Double value) {
            addCriterion("sale_cost >=", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostLessThan(Double value) {
            addCriterion("sale_cost <", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostLessThanOrEqualTo(Double value) {
            addCriterion("sale_cost <=", value, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostIn(List<Double> values) {
            addCriterion("sale_cost in", values, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostNotIn(List<Double> values) {
            addCriterion("sale_cost not in", values, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostBetween(Double value1, Double value2) {
            addCriterion("sale_cost between", value1, value2, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSaleCostNotBetween(Double value1, Double value2) {
            addCriterion("sale_cost not between", value1, value2, "saleCost");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNull() {
            addCriterion("sku_status is null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNotNull() {
            addCriterion("sku_status is not null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusEqualTo(String value) {
            addCriterion("sku_status =", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotEqualTo(String value) {
            addCriterion("sku_status <>", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThan(String value) {
            addCriterion("sku_status >", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThanOrEqualTo(String value) {
            addCriterion("sku_status >=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThan(String value) {
            addCriterion("sku_status <", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThanOrEqualTo(String value) {
            addCriterion("sku_status <=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLike(String value) {
            addCriterion("sku_status like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotLike(String value) {
            addCriterion("sku_status not like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIn(List<String> values) {
            addCriterion("sku_status in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotIn(List<String> values) {
            addCriterion("sku_status not in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusBetween(String value1, String value2) {
            addCriterion("sku_status between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotBetween(String value1, String value2) {
            addCriterion("sku_status not between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeIsNull() {
            addCriterion("sku_tag_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeIsNotNull() {
            addCriterion("sku_tag_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeEqualTo(String value) {
            addCriterion("sku_tag_code =", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeNotEqualTo(String value) {
            addCriterion("sku_tag_code <>", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeGreaterThan(String value) {
            addCriterion("sku_tag_code >", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_tag_code >=", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeLessThan(String value) {
            addCriterion("sku_tag_code <", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_tag_code <=", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeLike(String value) {
            addCriterion("sku_tag_code like", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeNotLike(String value) {
            addCriterion("sku_tag_code not like", value, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "sku_tag_code like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeNotIn(List<String> values) {
            addCriterion("sku_tag_code not in", values, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeBetween(String value1, String value2) {
            addCriterion("sku_tag_code between", value1, value2, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSkuTagCodeNotBetween(String value1, String value2) {
            addCriterion("sku_tag_code not between", value1, value2, "skuTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIsNull() {
            addCriterion("special_goods_code is null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIsNotNull() {
            addCriterion("special_goods_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeEqualTo(String value) {
            addCriterion("special_goods_code =", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotEqualTo(String value) {
            addCriterion("special_goods_code <>", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeGreaterThan(String value) {
            addCriterion("special_goods_code >", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("special_goods_code >=", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLessThan(String value) {
            addCriterion("special_goods_code <", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLessThanOrEqualTo(String value) {
            addCriterion("special_goods_code <=", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeLike(String value) {
            addCriterion("special_goods_code like", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotLike(String value) {
            addCriterion("special_goods_code not like", value, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIn(List<String> values) {
            addCriterion("special_goods_code in", values, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotIn(List<String> values) {
            addCriterion("special_goods_code not in", values, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeBetween(String value1, String value2) {
            addCriterion("special_goods_code between", value1, value2, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeNotBetween(String value1, String value2) {
            addCriterion("special_goods_code not between", value1, value2, "specialGoodsCode");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNull() {
            addCriterion("forbid_channel is null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNotNull() {
            addCriterion("forbid_channel is not null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelEqualTo(String value) {
            addCriterion("forbid_channel =", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotEqualTo(String value) {
            addCriterion("forbid_channel <>", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThan(String value) {
            addCriterion("forbid_channel >", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_channel >=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThan(String value) {
            addCriterion("forbid_channel <", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThanOrEqualTo(String value) {
            addCriterion("forbid_channel <=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLike(String value) {
            addCriterion("forbid_channel like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotLike(String value) {
            addCriterion("forbid_channel not like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "forbid_channel like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andProhibitionPlatformSiteIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "prohibition_sites like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "prohibition_sites like '%_"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }


        public Criteria andForbidChannelNotIn(List<String> values) {
            addCriterion("forbid_channel not in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelBetween(String value1, String value2) {
            addCriterion("forbid_channel between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotBetween(String value1, String value2) {
            addCriterion("forbid_channel not between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIsNull() {
            addCriterion("infringement_type_name is null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIsNotNull() {
            addCriterion("infringement_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameEqualTo(String value) {
            addCriterion("infringement_type_name =", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotEqualTo(String value) {
            addCriterion("infringement_type_name <>", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameGreaterThan(String value) {
            addCriterion("infringement_type_name >", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_type_name >=", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLessThan(String value) {
            addCriterion("infringement_type_name <", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLessThanOrEqualTo(String value) {
            addCriterion("infringement_type_name <=", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLike(String value) {
            addCriterion("infringement_type_name like", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotLike(String value) {
            addCriterion("infringement_type_name not like", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "infringement_type_name like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotIn(List<String> values) {
            addCriterion("infringement_type_name not in", values, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameBetween(String value1, String value2) {
            addCriterion("infringement_type_name between", value1, value2, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotBetween(String value1, String value2) {
            addCriterion("infringement_type_name not between", value1, value2, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNull() {
            addCriterion("infringement_obj is null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNotNull() {
            addCriterion("infringement_obj is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjEqualTo(String value) {
            addCriterion("infringement_obj =", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotEqualTo(String value) {
            addCriterion("infringement_obj <>", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThan(String value) {
            addCriterion("infringement_obj >", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_obj >=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThan(String value) {
            addCriterion("infringement_obj <", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThanOrEqualTo(String value) {
            addCriterion("infringement_obj <=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLike(String value) {
            addCriterion("infringement_obj like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotLike(String value) {
            addCriterion("infringement_obj not like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "infringement_obj like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotIn(List<String> values) {
            addCriterion("infringement_obj not in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjBetween(String value1, String value2) {
            addCriterion("infringement_obj between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotBetween(String value1, String value2) {
            addCriterion("infringement_obj not between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesIsNull() {
            addCriterion("prohibition_sites is null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesIsNotNull() {
            addCriterion("prohibition_sites is not null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesEqualTo(String value) {
            addCriterion("prohibition_sites =", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesNotEqualTo(String value) {
            addCriterion("prohibition_sites <>", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesGreaterThan(String value) {
            addCriterion("prohibition_sites >", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesGreaterThanOrEqualTo(String value) {
            addCriterion("prohibition_sites >=", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesLessThan(String value) {
            addCriterion("prohibition_sites <", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesLessThanOrEqualTo(String value) {
            addCriterion("prohibition_sites <=", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesLike(String value) {
            addCriterion("prohibition_sites like", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesNotLike(String value) {
            addCriterion("prohibition_sites not like", value, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesIn(List<String> values) {
            addCriterion("prohibition_sites in", values, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesNotIn(List<String> values) {
            addCriterion("prohibition_sites not in", values, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesBetween(String value1, String value2) {
            addCriterion("prohibition_sites between", value1, value2, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andProhibitionSitesNotBetween(String value1, String value2) {
            addCriterion("prohibition_sites not between", value1, value2, "prohibitionSites");
            return (Criteria) this;
        }

        public Criteria andPromotionIsNull() {
            addCriterion("promotion is null");
            return (Criteria) this;
        }

        public Criteria andPromotionIsNotNull() {
            addCriterion("promotion is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionEqualTo(Integer value) {
            addCriterion("promotion =", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionNotEqualTo(Integer value) {
            addCriterion("promotion <>", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionGreaterThan(Integer value) {
            addCriterion("promotion >", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionGreaterThanOrEqualTo(Integer value) {
            addCriterion("promotion >=", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionLessThan(Integer value) {
            addCriterion("promotion <", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionLessThanOrEqualTo(Integer value) {
            addCriterion("promotion <=", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionIn(List<Integer> values) {
            addCriterion("promotion in", values, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionNotIn(List<Integer> values) {
            addCriterion("promotion not in", values, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionBetween(Integer value1, Integer value2) {
            addCriterion("promotion between", value1, value2, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionNotBetween(Integer value1, Integer value2) {
            addCriterion("promotion not between", value1, value2, "promotion");
            return (Criteria) this;
        }

        public Criteria andNewStateIsNull() {
            addCriterion("new_state is null");
            return (Criteria) this;
        }

        public Criteria andNewStateIsNotNull() {
            addCriterion("new_state is not null");
            return (Criteria) this;
        }

        public Criteria andNewStateEqualTo(Boolean value) {
            addCriterion("new_state =", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateNotEqualTo(Boolean value) {
            addCriterion("new_state <>", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateGreaterThan(Boolean value) {
            addCriterion("new_state >", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("new_state >=", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateLessThan(Boolean value) {
            addCriterion("new_state <", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateLessThanOrEqualTo(Boolean value) {
            addCriterion("new_state <=", value, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateIn(List<Boolean> values) {
            addCriterion("new_state in", values, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateNotIn(List<Boolean> values) {
            addCriterion("new_state not in", values, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateBetween(Boolean value1, Boolean value2) {
            addCriterion("new_state between", value1, value2, "newState");
            return (Criteria) this;
        }

        public Criteria andNewStateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("new_state not between", value1, value2, "newState");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("sku_data_source is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("sku_data_source is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("sku_data_source >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("sku_data_source <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("sku_data_source not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIsNull() {
            addCriterion("compose_status is null");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIsNotNull() {
            addCriterion("compose_status is not null");
            return (Criteria) this;
        }

        public Criteria andComposeStatusEqualTo(Integer value) {
            addCriterion("compose_status =", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotEqualTo(Integer value) {
            addCriterion("compose_status <>", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusGreaterThan(Integer value) {
            addCriterion("compose_status >", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("compose_status >=", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusLessThan(Integer value) {
            addCriterion("compose_status <", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusLessThanOrEqualTo(Integer value) {
            addCriterion("compose_status <=", value, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusIn(List<Integer> values) {
            addCriterion("compose_status in", values, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotIn(List<Integer> values) {
            addCriterion("compose_status not in", values, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusBetween(Integer value1, Integer value2) {
            addCriterion("compose_status between", value1, value2, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andComposeStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("compose_status not between", value1, value2, "composeStatus");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeIsNull() {
            addCriterion("modified_time is null");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeIsNotNull() {
            addCriterion("modified_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeEqualTo(Timestamp value) {
            addCriterion("modified_time =", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeNotEqualTo(Timestamp value) {
            addCriterion("modified_time <>", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeGreaterThan(Timestamp value) {
            addCriterion("modified_time >", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("modified_time >=", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeLessThan(Timestamp value) {
            addCriterion("modified_time <", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("modified_time <=", value, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeIn(List<Timestamp> values) {
            addCriterion("modified_time in", values, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeNotIn(List<Timestamp> values) {
            addCriterion("modified_time not in", values, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("modified_time between", value1, value2, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andModifiedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("modified_time not between", value1, value2, "modifiedTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeIsNull() {
            addCriterion("last_synch_time is null");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeIsNotNull() {
            addCriterion("last_synch_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeEqualTo(String value) {
            addCriterion("last_synch_time =", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeNotEqualTo(String value) {
            addCriterion("last_synch_time <>", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeGreaterThan(String value) {
            addCriterion("last_synch_time >", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeGreaterThanOrEqualTo(String value) {
            addCriterion("last_synch_time >=", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeLessThan(String value) {
            addCriterion("last_synch_time <", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeLessThanOrEqualTo(String value) {
            addCriterion("last_synch_time <=", value, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeIn(List<String> values) {
            addCriterion("last_synch_time in", values, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeNotIn(List<String> values) {
            addCriterion("last_synch_time not in", values, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeBetween(String value1, String value2) {
            addCriterion("last_synch_time between", value1, value2, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andLastSynchTimeNotBetween(String value1, String value2) {
            addCriterion("last_synch_time not between", value1, value2, "lastSynchTime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andItemStatusEqualTo(Integer value) {
            addCriterion("item_status =", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotEqualTo(Long value) {
            addCriterion("item_status <>", value, "itemStatus");
            return (Criteria) this;
        }


        public Criteria andSystemStockIsNotNull() {
            addCriterion("system_stock is not null");
            return (Criteria) this;
        }

        public Criteria andSystemStockEqualTo(Integer value) {
            addCriterion("system_stock =", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andSystemStockNotEqualTo(Integer value) {
            addCriterion("system_stock <>", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andSystemStockGreaterThan(Integer value) {
            addCriterion("system_stock >", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andSystemStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_stock >=", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andSystemStockLessThan(Integer value) {
            addCriterion("system_stock <", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andSystemStockLessThanOrEqualTo(Integer value) {
            addCriterion("system_stock <=", value, "SystemStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockIsNull() {
            addCriterion("usable_stock is null");
            return (Criteria) this;
        }

        public Criteria andUsableStockIsNotNull() {
            addCriterion("usable_stock is not null");
            return (Criteria) this;
        }

        public Criteria andUsableStockEqualTo(Integer value) {
            addCriterion("usable_stock =", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockNotEqualTo(Integer value) {
            addCriterion("usable_stock <>", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockGreaterThan(Integer value) {
            addCriterion("usable_stock >", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("usable_stock >=", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockLessThan(Integer value) {
            addCriterion("usable_stock <", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockLessThanOrEqualTo(Integer value) {
            addCriterion("usable_stock <=", value, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockIn(List<Integer> values) {
            addCriterion("usable_stock in", values, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockNotIn(List<Integer> values) {
            addCriterion("usable_stock not in", values, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockBetween(Integer value1, Integer value2) {
            addCriterion("usable_stock between", value1, value2, "usableStock");
            return (Criteria) this;
        }

        public Criteria andUsableStockNotBetween(Integer value1, Integer value2) {
            addCriterion("usable_stock not between", value1, value2, "usableStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockIsNull() {
            addCriterion("smt_transfer_stock is null");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockIsNotNull() {
            addCriterion("smt_transfer_stock is not null");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockEqualTo(Integer value) {
            addCriterion("smt_transfer_stock =", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockNotEqualTo(Integer value) {
            addCriterion("smt_transfer_stock <>", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockGreaterThan(Integer value) {
            addCriterion("smt_transfer_stock >", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("smt_transfer_stock >=", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockLessThan(Integer value) {
            addCriterion("smt_transfer_stock <", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockLessThanOrEqualTo(Integer value) {
            addCriterion("smt_transfer_stock <=", value, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockIn(List<Integer> values) {
            addCriterion("smt_transfer_stock in", values, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockNotIn(List<Integer> values) {
            addCriterion("smt_transfer_stock not in", values, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockBetween(Integer value1, Integer value2) {
            addCriterion("smt_transfer_stock between", value1, value2, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSmtTransferStockNotBetween(Integer value1, Integer value2) {
            addCriterion("smt_transfer_stock not between", value1, value2, "smtTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockIsNull() {
            addCriterion("system_usable_transfer_stock is null");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockIsNotNull() {
            addCriterion("system_usable_transfer_stock is not null");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockEqualTo(Integer value) {
            addCriterion("system_usable_transfer_stock =", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockNotEqualTo(Integer value) {
            addCriterion("system_usable_transfer_stock <>", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockGreaterThan(Integer value) {
            addCriterion("system_usable_transfer_stock >", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_usable_transfer_stock >=", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockLessThan(Integer value) {
            addCriterion("system_usable_transfer_stock <", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockLessThanOrEqualTo(Integer value) {
            addCriterion("system_usable_transfer_stock <=", value, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockIn(List<Integer> values) {
            addCriterion("system_usable_transfer_stock in", values, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockNotIn(List<Integer> values) {
            addCriterion("system_usable_transfer_stock not in", values, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockBetween(Integer value1, Integer value2) {
            addCriterion("system_usable_transfer_stock between", value1, value2, "systemUsableTransferStock");
            return (Criteria) this;
        }

        public Criteria andSystemUsableTransferStockNotBetween(Integer value1, Integer value2) {
            addCriterion("system_usable_transfer_stock not between", value1, value2, "systemUsableTransferStock");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}