
package com.estone.erp.publish.smt.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleDiscountGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分组id
     */
    @NotBlank(message = "分组id不能为空")
    private String groupId;

    /**
     * 折扣率
     */
    @NotNull(message = "折扣率不能为空")
    private Integer discount;

}