package com.estone.erp.publish.smt.util;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.model.AliexpressSkuOrder;
import com.estone.erp.publish.smt.model.AliexpressTemplate;
import com.estone.erp.publish.system.order.modle.CustomerOrderBySkuRequest;
import com.estone.erp.publish.system.order.modle.CustomerOrderBySkuResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Date 2021年1月20日
 */
@Slf4j
public class AliexpressSkuOrderUtils {

    /**
     * 转换smt平台SKU出单信息
     * @param responses
     * @return
     */
    public static List<AliexpressSkuOrder> toAliexpressSkuOrder(List<CustomerOrderBySkuResponse> responses) {
        List<AliexpressSkuOrder> aliexpressSkuOrders = new ArrayList<>();

        for (CustomerOrderBySkuResponse response : responses) {
            AliexpressSkuOrder aliexpressSkuOrder = new AliexpressSkuOrder();

            aliexpressSkuOrder.setAccountNumber(response.getAccountNumber());
            aliexpressSkuOrder.setSellerSku(response.getCustomLabel());
            aliexpressSkuOrder.setSystemSku(response.getArticleNumber());
            aliexpressSkuOrder.setArticleNumber(response.getSpu());
            aliexpressSkuOrder.setOrderNo(response.getCustomerOrderNo());
            aliexpressSkuOrder.setPrice(response.getOrderAmount());
            aliexpressSkuOrder.setPaymentTime(response.getOrderUpdateTime());

            aliexpressSkuOrders.add(aliexpressSkuOrder);
        }
        return aliexpressSkuOrders;
    }

    /**
     * 模板转换为获取订单请求列表
     * @param aliexpressTemplateList
     * @return
     */
    public static List<CustomerOrderBySkuRequest> toCustomerOrderBySkuRequest(List<AliexpressTemplate> aliexpressTemplateList) {
        List<CustomerOrderBySkuRequest> customerOrderBySkuRequests = new ArrayList<>();

        Map<String, List<AliexpressTemplate>> spuTemplateMap = aliexpressTemplateList.stream().collect(Collectors.groupingBy(AliexpressTemplate::getArticleNumber));
        for (String spu : spuTemplateMap.keySet()) {

            List<AliexpressTemplate> aliexpressTemplates = spuTemplateMap.get(spu);
            if(CollectionUtils.isEmpty(aliexpressTemplates)) {
                continue;
            }

            // 收集账号和SKU
            List<String> accountNumberList = new ArrayList<>();
            List<String> skuList = new ArrayList<>();

            for (AliexpressTemplate aliexpressTemplate :aliexpressTemplates) {
                skuList = aliexpressTemplate.getSkuList();
                accountNumberList.add(aliexpressTemplate.getAliexpressAccountNumber());
            }

            CustomerOrderBySkuRequest customerOrderBySkuRequest = new CustomerOrderBySkuRequest();
            customerOrderBySkuRequests.add(customerOrderBySkuRequest);

            customerOrderBySkuRequest.setSpu(spu);
            customerOrderBySkuRequest.setSaleChannel(SaleChannel.CHANNEL_SMT);
            customerOrderBySkuRequest.setArticleNumberList(skuList);
            customerOrderBySkuRequest.setAccountNumberList(accountNumberList);
        }

        return customerOrderBySkuRequests;
    }
}
