package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsExportDto;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsPageQueryDto;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionDetails;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressTaskExecutionDetailsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/aliexpressTaskExecutionDetails")
public class AliexpressTaskExecutionDetailsController {

    @Resource
    private AliexpressTaskExecutionDetailsService aliexpressTaskExecutionDetailsService;



    @PostMapping("/pageQuery")
    public ApiResult<IPage<AliexpressTaskExecutionDetails>> pageQuery(@RequestBody AliexpressTaskExecutionDetailsPageQueryDto dto) {
        IPage<AliexpressTaskExecutionDetails> page = aliexpressTaskExecutionDetailsService.pageQuery(dto);
        return ApiResult.newSuccess(page);
    }


    /**
     * 获取所有SMT规则名称
     */
    @GetMapping("/getRuleNameList")
    public ApiResult<List<String>> getRuleNameList() {
        List<String> ruleNameList = aliexpressTaskExecutionDetailsService.getRuleNameList();
        return ApiResult.newSuccess(ruleNameList);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody AliexpressTaskExecutionDetailsExportDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR);
        ResponseJson responseJson = aliexpressTaskExecutionDetailsService.export(dto);
        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }
        return ApiResult.newSuccess("请到excel日志下载记录查看结果！");
    }


}
