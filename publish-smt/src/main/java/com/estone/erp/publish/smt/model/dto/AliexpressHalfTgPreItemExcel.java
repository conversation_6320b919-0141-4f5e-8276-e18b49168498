package com.estone.erp.publish.smt.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.estone.erp.publish.component.converter.BooleanCodeConverter;
import com.estone.erp.publish.component.converter.PromotionConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.smt.util.convert.ExcelSkuStatusConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 可加入商品导出
 */
@Data
public class AliexpressHalfTgPreItemExcel {

    /**
     * 店铺账号
     */
    @ExcelProperty(value = "店铺")
    private String account;

    @ExcelProperty(value = "商品ID", converter = LongStringConverter.class)
    private Long productId;

    @ExcelProperty(value = "商品编号")
    private String skuCode;

    @ExcelProperty(value = "标题")
    private String title;

    @ExcelProperty(value = "平台类目")
    private String fullPathCode;

    @ExcelProperty(value = "零售价")
    private Double productprice;

    @ExcelProperty(value = "可用+中转-待发")
    private Integer systemUsableTransferStock;

    @ExcelProperty(value = "可用库存")
    private Integer usableStock;

    @ExcelProperty(value = "中转仓库存")
    private Integer smtTransferStock;

    @ExcelProperty(value = "SKU")
    private String articleNumber;

    @ExcelProperty(value = "单品状态", converter = ExcelSkuStatusConverter.class)
    private String skuStatus;

    @ExcelProperty(value = "销售成本价")
    private Double saleCost;

    @ExcelProperty(value = "分类")
    private String proCategoryCnName;

    @ExcelProperty(value = "产品标签")
    private String skuTagCode;

    @ExcelProperty(value = "特殊标签")
    private String specialGoodsCode;

    @ExcelProperty(value = "是否促销", converter = PromotionConverter.class)
    private Integer promotion;

    @ExcelProperty(value = "是否新品", converter = BooleanCodeConverter.class)
    private Boolean newState;

    @ExcelProperty(value = "销售")
    private String salemanager;

    @ExcelProperty(value = "销售组长")
    private String salemanagerLeader;

    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp lastSynchTime;

    @ExcelProperty(value = "禁售类型")
    private String infringementTypeName;

    @ExcelProperty(value = "禁售原因")
    private String infringementObj;

    @ExcelProperty(value = "禁售平台")
    private String forbidChannel;

    @ExcelProperty(value = "禁售站点")
    private String prohibitionSites;


}


