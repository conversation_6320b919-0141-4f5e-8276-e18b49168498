package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.smt.controller
 * @Author: sj
 * @CreateTime: 2025-06-05  16:00
 * @Description: TODO
 */
@Data
public class AliexpressUpdateImageTitleRequest {
     /**
     * 商品 ID
     */
    private Long productId;

    /**
     * 单品货号
     */
    private String articleNumber;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 主图链接
     */
    private String mainImage;

    /**
     * 是否修改标题
     */
    private Boolean updateTitle;

    /**
     * 标题
     */
    private String title;

//    /**
//     * 图片文件
//     */
//    private MultipartFile imageFile;

//    /**
//     * 是否需要上传
//     */
//    private Boolean needUpload;

}
