package com.estone.erp.publish.smt.call.direct.singlediscount;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.AbstractSmtCall;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 创建店铺限时限量活动
 *
 * @Auther lc
 * @Date 2024年7月29日17:13:40
 */
@Slf4j
public class SingleDiscountCreateCall extends AbstractSmtCall {

    /**
     * 创建单品折扣
     *
     * @param saleAccountByAccountNumber
     * @param name
     * @param startTime
     * @param endTime
     * @return
     */
    public static ResponseJson createSingleDiscount(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String name, LocalDateTime startTime, LocalDateTime endTime) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try {
            IopResponse iopResponse = create(saleAccountByAccountNumber, name, startTime, endTime);
            String body = iopResponse.getBody();
            rsp.setMessage(body);
            if (ObjectUtils.isNotEmpty(iopResponse) && iopResponse.isSuccess() && StringUtils.isNotBlank(iopResponse.getBody()) && (JSON.parseObject(iopResponse.getBody()).getJSONObject("aliexpress_marketing_limitdiscountpromotion_create_response")) != null) {
                //折扣id
                String target = JSON.parseObject(iopResponse.getBody()).getJSONObject("aliexpress_marketing_limitdiscountpromotion_create_response").getString("target");
                if (StringUtils.isNotBlank(target)) {
                    rsp.setMessage(target);
                    rsp.setStatus(StatusCode.SUCCESS);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    public static IopResponse create(SaleAccountAndBusinessResponse saleAccount, String name, LocalDateTime startTime, LocalDateTime endTime) throws ApiException {
        List<String> testAccounts = Optional.ofNullable(testAccountsConfig.getCreateCallTestAccounts()).orElse(Collections.emptyList());

        // 执行请求
        IopRequest request = new IopRequest();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("promotion_name", name);
        jsonObject.put("promotion_start_time", startTime.minusHours(AbstractSmtOpenCall.timeDiff).format(dateTimeFormatter));
        jsonObject.put("promotion_end_time", endTime.minusHours(AbstractSmtOpenCall.timeDiff).format(dateTimeFormatter));
        jsonObject.put("has_promo", "false");

        request.setApiName("aliexpress.marketing.limitdiscountpromotion.create");
        request.addApiParameter("param_limited_disc_input_dto", jsonObject.toJSONString());
        return EnvironmentSupplierWrapper.execute(() -> {
            IopResponse iopResponse = new IopResponse();
            try {
                iopResponse = AbstractSmtOpenCall.execute(saleAccount, request);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                iopResponse.setCode("500");
                iopResponse.setMessage(e.getMessage());
                iopResponse.setBody(e.getMessage());
            }
            return iopResponse;
        }, () -> {
            IopResponse response = new IopResponse();
            response.setCode("500");
            response.setMessage("非正式环境不执行创建活动");
            response.setBody("非正式环境不执行创建活动");
            return response;
        }, testAccounts, saleAccount);
    }


}
