package com.estone.erp.publish.smt.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SmtSellerProductLimitData {
    /**
     * 周期内新发商品上限数
     */
    @JsonProperty("product_current_month_limit_num")
    private String productCurrentMonthLimitNum;

    /**
     * 总审核中商品数
     */
    @JsonProperty("product_total_audit_num")
    private String productTotalAuditNum;

    /**
     * 总在线商品数
     */
    @JsonProperty("product_total_online_num")
    private String productTotalOnlineNum;

    /**
     * 周期内新发商品数
     */
    @JsonProperty("product_current_month_pub_num")
    private String productCurrentMonthPubNum;


    /**
     * 总可销售商品上限数
     */
    @JsonProperty("product_total_limit_num")
    private String productTotalLimitNum;
}
