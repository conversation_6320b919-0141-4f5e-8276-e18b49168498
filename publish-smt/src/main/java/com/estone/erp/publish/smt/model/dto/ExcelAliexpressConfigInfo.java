package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

@Data
public class ExcelAliexpressConfigInfo{

    private String account;

    //刊登算价配置

    private Double fromWeight;

    private Double toWeight;

    private String tagCodes;

    /**
     * 运费模板
     */
    private Long freightTemplateId;

    /**
     * 毛利率
     */
    private Double grossProfit;

    /**
     * 试算物流渠道
     */
    private String shippingMethod;

    /**
     * 国家
     */
    private String countryCode;


    //计算在线毛利配置

    /**
     * 运费模板
     */
    private Long freightTemplateIdCount;

    /**
     * 试算物流渠道
     */
    private String shippingMethodCount;

    /**
     * 国家
     */
    private String countryCodeCount;

    /**
     * 价格毛利率
     */
    private Double priceGrossProfit;

    /**
     * 区域价格毛利率
     */
    private Double areaGrossProfit;

    /**
     * 折扣率
     */
    private Double discountRate;

}