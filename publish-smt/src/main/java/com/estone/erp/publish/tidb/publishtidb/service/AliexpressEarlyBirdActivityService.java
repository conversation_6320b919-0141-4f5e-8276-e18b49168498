package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.system.product.response.QuerySpuByConditionVo;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-14 12:09:21
 */
public interface AliexpressEarlyBirdActivityService extends IService<AliexpressEarlyBirdActivity> {
    int countByExample(AliexpressEarlyBirdActivityExample example);

    CQueryResult<AliexpressEarlyBirdActivity> search(CQuery<AliexpressEarlyBirdActivityCriteria> cquery) throws Exception;

    List<AliexpressEarlyBirdActivity> selectByExample(AliexpressEarlyBirdActivityExample example);

    AliexpressEarlyBirdActivity selectByPrimaryKey(Long id);

    int insert(AliexpressEarlyBirdActivity record);

    int updateByPrimaryKeySelective(AliexpressEarlyBirdActivity record);

    int updateByExampleSelective(AliexpressEarlyBirdActivity record, AliexpressEarlyBirdActivityExample example);

    int deleteByPrimaryKey(List<Long> ids);

    ResponseJson joinAliexpressEarlyBirdActivity(CQuery<List<AliexpressEarlyBirdActivity>> cquery);

    ResponseJson downloadEarlyBirdActivities(CQuery<AliexpressEarlyBirdActivityCriteria> cquery);

    void batchInsert(ArrayList<AliexpressEarlyBirdActivity> aliexpressEarlyBirdActivities);

    void batchUpdate(ArrayList<AliexpressEarlyBirdActivity> earlyBirdActivities);

    List<AliexpressEarlyBirdActivity> selectFieldsByExample(AliexpressEarlyBirdActivityExample aliexpressEarlyBirdActivityExample);

    void deleteOverDueProduct();

    List<String> selectAllStore();

    List<String> selectItemIdsByExample(AliexpressEarlyBirdActivityExample aliexpressEarlyBirdActivityExample);

    void deleteByItemIds(List<String> deleteItemIds);

    /**
     * 根据早鸟活动配置的id的数据查询 产品系统根据条件获取spu中最重sku数据
     * @param marketingConfigId
     * @return
     */
    List<QuerySpuByConditionVo> queryHeaviestSkuByMarketingConfigId(Integer marketingConfigId);

    List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AliexpressEarlyBirdActivity> wrapper);
}