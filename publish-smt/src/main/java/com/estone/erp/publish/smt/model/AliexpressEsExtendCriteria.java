package com.estone.erp.publish.smt.model;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.smt.bean.ProductMoveBean;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> aliexpress_es_extend
 * 2021-11-23 18:46:22
 */
@Data
public class AliexpressEsExtendCriteria extends AliexpressEsExtend {
    private static final long serialVersionUID = 1L;

    private List<String> spuList;

    /**
     * 指定修改资质图片类型 1.GPSR 2.库存图 3.外包装
     */
    private List<Integer> updateQualificationTypeList;

    private String idStr;

    //productIdStr
    private String productIdStr;

    private String articleNumberStr;

    //库存有无
    private String stockStatus;

    private String orderBy;

    //禁售平台
    private String forbidChannel;

    //sku状态
    private String skuStatusStr;

    private Date fromCreateTime;

    private Date toCreateDate;

    private Date fromGmtCreateDate;

    private Date toGmtCreateDate;

    private Date fromGmtModifiedDate;

    private Date toGmtModifiedDate;

    private Date fromLastSynchDate;

    private Date toLastSynchDate;

    private List<Integer> fields;//导出文件自选字段

    private String shippingMethodCode;

    private String subjectLike;

    private String groupBy;

    private Double fromGrossWeight;

    private Double toGrossWeight;

    private String freightTemplateIds;

    //权限账号
    private List<String> authSellerList = new ArrayList<>();


    private String leftMark;

    private Double leftValue;

    private String rightMark;

    private Double rightValue;

    /**
     * 修改车型库提交的json
     */
    private String cayJson;

    /**
     * 产品上下架 的类型 上架—1 、下架—0
     */
    private Integer type;

    /**
     * 是否保留之前分组
     */
    private Boolean retain;

    private String groupIdStr;

    /**
     * 是否支持车型库
     */
    private Boolean isCayType;

    //---在线列表32国调价

    private String logisticsName;

    private Double gross;

    //调价方式
    private String priceType;

    private String fromLastphaseChangeTime;

    //是否查询产品，默认true 在线列表先不查
    private Boolean isSeleteProduct = true;

    private Boolean isClear;

    //库存范围
    private Integer fromIpmSkuStock;

    private Integer toIpmSkuStock;

    //引流sku
    private Map<String, Boolean> map;

    //需要算价的国家
    private List<String> updateCountryCodeList;

    //产品搬家
    private List<EsAliexpressProductListing> esAliexpressProductListing;
    private String publishAccount;
    private Boolean wholeProduct;
    private ProductMoveBean productMoveBean;

    /**
     * 商品单位 (存储单位编号) *********:袋 (bag/bags) *********:桶 (barrel/barrels) *********:蒲式耳 (bushel/bushels) *********:箱 (carton) *********:厘米 (centimeter) *********:立方米 (cubic meter) *********:打 (dozen) *********:英尺 (feet) *********:加仑 (gallon) *********:克 (gram) *********:英寸 (inch) *********:千克 (kilogram) *********:千升 (kiloliter) *********:千米 (kilometer) *********:升 (liter/liters) *********:英吨 (long ton) *********:米 (meter) *********:公吨 (metric ton) *********:毫克 (milligram) *********:毫升 (milliliter) *********:毫米 (millimeter) *********:盎司 (ounce) *********:包 (pack/packs) *********:双 (pair) *********:件/个 (piece/pieces) *********:磅 (pound) *********:夸脱 (quart) *********:套 (set/sets) *********:美吨 (short ton) *********:平方英尺 (square feet) *********:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_product.product_unit
     */
    private Integer productUnit;

    /**
     * 打包销售: true 非打包销售:false database column aliexpress_product.package_type
     */
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_product.lot_num
     */
    private Integer lotNum;

    public AliexpressEsExtendExample getExample() {
        AliexpressEsExtendExample example = new AliexpressEsExtendExample();
        AliexpressEsExtendExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAliexpressAccountNumber())) {
            criteria.andAliexpressAccountNumberEqualTo(this.getAliexpressAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getOwnerMemberId())) {
            criteria.andOwnerMemberIdEqualTo(this.getOwnerMemberId());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductSkusJson())) {
            criteria.andAeopAeProductSkusJsonEqualTo(this.getAeopAeProductSkusJson());
        }
        if (StringUtils.isNotBlank(this.getAeopNationalQuoteConfiguration())) {
            criteria.andAeopNationalQuoteConfigurationEqualTo(this.getAeopNationalQuoteConfiguration());
        }
        if (StringUtils.isNotBlank(this.getAeopAeMultimedia())) {
            criteria.andAeopAeMultimediaEqualTo(this.getAeopAeMultimedia());
        }
        if (StringUtils.isNotBlank(this.getAeopAeProductPropertysJson())) {
            criteria.andAeopAeProductPropertysJsonEqualTo(this.getAeopAeProductPropertysJson());
        }
        if (StringUtils.isNotBlank(this.getMobileDetail())) {
            criteria.andMobileDetailEqualTo(this.getMobileDetail());
        }
        if (StringUtils.isNotBlank(this.getExtendedField1())) {
            criteria.andExtendedField1EqualTo(this.getExtendedField1());
        }
        if (StringUtils.isNotBlank(this.getExtendedField2())) {
            criteria.andExtendedField2EqualTo(this.getExtendedField2());
        }
        if (StringUtils.isNotBlank(this.getExtendedField3())) {
            criteria.andExtendedField3EqualTo(this.getExtendedField3());
        }
        if (StringUtils.isNotBlank(this.getExtendedField4())) {
            criteria.andExtendedField4EqualTo(this.getExtendedField4());
        }
        if (StringUtils.isNotBlank(this.getExtendedField5())) {
            criteria.andExtendedField5EqualTo(this.getExtendedField5());
        }
        return example;
    }
}