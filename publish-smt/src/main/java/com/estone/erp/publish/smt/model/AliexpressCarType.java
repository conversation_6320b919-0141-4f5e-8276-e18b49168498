package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressCarType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_car_type.id
     */
    private Integer id;

    /**
     * 子节点分类id database column aliexpress_car_type.leaf_category_id
     */
    private Integer leafCategoryId;

    /**
     * 类型id database column aliexpress_car_type.type_id
     */
    private Long typeId;

    /**
     * 父级类型id database column aliexpress_car_type.parent_type_id
     */
    private Long parentTypeId;

    /**
     * 类型层级 database column aliexpress_car_type.type_level
     */
    private Integer typeLevel;

    /**
     * 车载类型 database column aliexpress_car_type.car_type
     */
    private String carType;

    /**
     * 请求参数1(子节点分类id) database column aliexpress_car_type.param1
     */
    private Integer param1;

    /**
     * 请求参数2 database column aliexpress_car_type.param2
     */
    private String param2;

    /**
     * 属性json database column aliexpress_car_type.attribute_json
     */
    private String attributeJson;

    /**
     * 创建人 database column aliexpress_car_type.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_car_type.create_time
     */
    private Timestamp createTime;
}