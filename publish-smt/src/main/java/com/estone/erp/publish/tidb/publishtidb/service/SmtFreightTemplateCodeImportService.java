package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtFreightTemplateCodeImportDto;
import com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface SmtFreightTemplateCodeImportService extends IService<SmtFreightTemplateCodeImport> {
    IPage<SmtFreightTemplateCodeImport> pageQuery(SmtFreightTemplateCodeImportDto dto);

    ApiResult<String> download(SmtFreightTemplateCodeImportDto dto);

    IPage<SmtFreightTemplateCodeImport> page(SmtFreightTemplateCodeImportDto dto);

    LambdaQueryWrapper<SmtFreightTemplateCodeImport> getPageQueryWrapper(SmtFreightTemplateCodeImportDto dto);

    List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtFreightTemplateCodeImport> wrapper);

    /**
     * 根据ID列表删除数据
     * 限制条件：不允许删除执行状态为处理中的数据且添加人为登录人
     * 
     * @param dto 包含idList的DTO对象
     * @return 删除结果
     */
    ApiResult<?> deleteByIds(SmtFreightTemplateCodeImportDto dto);
}
