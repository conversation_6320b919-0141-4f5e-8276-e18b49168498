package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class SmtHolidaysStockErrorLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 操作类型
     */
    private Integer opType;

    /**
     * 查询语句
     */
    private String query;

    /**
     * 查询语句
     */
    private String message;

    /**
     * 改前数据
     */
    private String beforeObj;

    /**
     * 改后数据
     */
    private String afterObj;

    /**
     * 用户
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createdTime;
}