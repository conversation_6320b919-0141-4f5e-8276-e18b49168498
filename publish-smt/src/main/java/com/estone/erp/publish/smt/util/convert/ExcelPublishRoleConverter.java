package com.estone.erp.publish.smt.util.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * <AUTHOR>
 * @date 2022-07-13 18:50
 */
public class ExcelPublishRoleConverter implements Converter<Integer> {
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        //1.在线时间， 2.销量
        if (value == 0) {
            return new WriteCellData<>("系统刊登");
        }
        if (value == 1) {
            return new WriteCellData<>("销售刊登");
        }
        if (value == 2) {
            return new WriteCellData<>("文案刊登");
        }
        if (value == 3) {
            return new WriteCellData<>("主管刊登");
        }
        return new WriteCellData<>("未知");
    }
}
