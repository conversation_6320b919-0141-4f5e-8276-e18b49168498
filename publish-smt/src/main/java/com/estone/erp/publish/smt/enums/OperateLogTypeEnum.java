package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/10/2916:18
 */
public enum OperateLogTypeEnum {

    EDIT_PRICE("edit price", "修改价格"),
    EDIT_TG_PRICE("edit tg price", "修改全托管价格"),
    EDIT_STOCK("edit stock", "修改库存"),
    EDIT_TG_STOCK("edit tg stock", "修改全托管库存"),
    ONLINE("online", "上架"),
    OFFLINE("offline", "下架"),
    POST("post product", "发布产品"),
    POP_TO_SKU("pop to sku", "pop指定sku刊登"),
    HALF_TG_POST("half tg post product", "半托管刊登"),
    TG_POST("tg post product", "托管发布产品"),
    EDIT("edit product", "编辑产品"),
    EDIT_IMG("edit img", "修改图片"),
    EDIT_SKU_IMG("edit_sku_img", "修改子sku图片"),
    EDIT_CAR_TYPE("edit car type", "修改车型库"),
    tran_temp("tran temp", "批量转范本"),
    EDIT_SUBJECT_DETAIL("edit subjectAndDetail", "更新标题描述"),
    RE_SUBJECT_DETAIL("re subjectAndDetail", "替换标题描述关键词"),
    EDIT_TORT("edit tort", "更新侵权产品"),
    UPDATE_ORIGIN("update origin", "修改产地"),
    PRICE28_UPDATE("price28_update", "63国修改价格"),
    PRICE28_CLEAR("price28_clear", "清空区域调价"),
    UPDATE_SEASON("update season", "修改季节"),
    delete_marketing_product("delete marketing product", "营销产品删除"),
    update_marketing_product("update marketing product", "营销产品更新"),
    add_marketing_product("add marketing product", "营销产品添加"),
    update_marketing_img("update marketing img", "修改营销图"),
    update_size("update size", "修改尺寸"),
    update_attr("update attr", "修改属性"),
    upload_video("upload_video", "上传视频"),
    upload_video_new("upload_video_new", "上传视频(新)"),
    update_weight("update_weight", "修改重量"),
    update_tg_weight("update_tg_weight", "修改全托管重量"),
    update_delivery_time("update_delivery_time", "修改发货时间"),
    update_freight_template_id("update_freight_template_id", "修改运费模板"),
    update_reduce_strategy("update_reduce_strategy", "修改库存扣减方式"),
    update_group_id("update_group_id", "修改产品分组"),
    update_product_template("update_product_template", "修改产品分组/运费服务模板"),
    update_product("update_product", "修改产品"),
    update_product_tg("update_product_tg", "修改产品(托管)"),
    update_product_euid("update_product_euid", "修改欧盟负责人"),
    update_product_euid_tg("update_product_euid_tg", "修改欧盟负责人(托管)"),
    update_product_car("update_product_car", "修改产品车型库"),
    delete_product_car("delete_product_car", "删除产品车型库"),
    update_sale_mode("update_sale_mode", "修改销售方式"),
    update_product_categoryId("update_product_categoryId", "修改产品类目id"),
    delete_product("delete_product", "删除产品"),
    delete_account_data("delete_account_data", "删除店铺数据"),
    update_qualification("update_qualification", "修改资质"),
    update_tg_qualification("update_tg_qualification", "修改资质(托管)"),
    update_product_discount("update_product_discount", "修改批发折扣"),
    synch_half_tg_item("synch_half_tg_item", "同步半托管产品"),
    edit_half_tg_stock("edit_half_tg_stock", "修改半托管库存"),
    edit_half_tg_price("edit_half_tg_price", "修改半托管价格"),
    edit_half_tg_package_weight("edit_half_tg_package_weight", "修改半托管重量"),
    edit_half_tg_pack("edit_half_tg_pack", "修改半托管包装尺寸"),
    edit_half_tg_item("edit_half_tg_item", "编辑半托管产品"),
    synch_half_tg_pre_item("synch_half_tg_pre_item", "同步半托管可加入商品产品"),
    half_tg_pre_join_item("half_tg_pre_join_item", "半托管可加入商品产品预约加入"),
    ADD_HALF_TG_ITEM("add_half_tg_item", "半托管立即加入"),
    SYNC_ALL_ITEM("sync_all_item", "全量同步产品"),
    SYNC_SMT_ALIANCE_PRODUCT_LISTING("sync_smt_aliance_produclisting", "生成设置联盟数据"),
    SINGLE_DISCOUNT_CREATE("single_discount_create", "生成单品折扣"),
    SINGLE_DISCOUNT_ADD_PRO("single_discount_add_pro", "单品折扣添加商品"),
    SINGLE_DISCOUNT_SYNC("single_discount_sync", "同步单品折扣"),
    SINGLE_DISCOUNT_STATUS_SYNC("single_discount_status_sync", "同步单品折扣(状态)"),
    SINGLE_DISCOUNT_CREATE_OR_UPDATE("single_discount_createorupdate", "生成或编辑单品折扣"),
    UPDATE_COUNTRY("update_country", "修改加入国家"),
    UPDATE_MANUFACTURER("update_manufacturer", "修改制造商"),
    UPDATE_TG_MANUFACTURER("update_tg_manufacturer", "修改制造商(全托管)"),
    TIMING_SINGLE_DISCOUNT_ADD_PRO("timing_single_discount_add_pro", "定时执行单品折扣添加商品"),
    AUTO_JOIN_EARLY_BIRD("auto_join_early_bird","自动加入早鸟活动" ),
    FULL_REDUCTION_CONFIG("full_reduction_config","满减活动配置" ),
    STORE_CODE_CONFIG("store_code_config","店铺code配置" ),
    REMOVE_SMT_ALIANCE_PRODUCT_LISTING("remove_smt_aliance_produclisting", "移除联盟数据"),
    ADMIN_ATTR_SYNCH_TO_LIST("admin_attr_synch_to_list", "admin范本属性同步到列表"),
    UPDATE_LADDER_PRICE_POP("update_ladder_price_pop", "修改批发价(pop)"),
    UPDATE_LADDER_PRICE_HALF("update_ladder_price_half", "修改批发价(半托管)"),
    SC_ITEM_INVITATION_CONFIRM("sc_item_invitation_confirm", "半托管确认抢占入仓"),
    SC_ITEM_INVITATION_REJECT("sc_item_invitation_reject", "半托管放弃抢占入仓"),
    UPDATE_TAX_TYPE("update_tax_type", "修改链接计税方式"),
    DELETE_FREIGHT_EXCLE_IMPORT("delete_freight_excle_import", "删除运费模板excel导入数据"),
    ;

    private String code;

    private String name;

    private OperateLogTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static OperateLogTypeEnum build(String code) {
        OperateLogTypeEnum[] values = values();
        for (OperateLogTypeEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}
