package com.estone.erp.publish.smt.model;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-04 11:33:16
 */
@Data
public class SmtAccountGroupCriteria extends SmtAccountGroup {
    private static final long serialVersionUID = 1L;

    /**
     * 更新时间 从
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateDateFrom;

    /**
     * 更新时间 到
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateDateTo;



    public SmtAccountGroupExample getExample() {
        SmtAccountGroupExample example = new SmtAccountGroupExample();
        SmtAccountGroupExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getGroupName())) {
            criteria.andGroupNameLike("%"+this.getGroupName()+"%");
        }
        if (StringUtils.isNotBlank(this.getAccounts())) {
            List<String> accountList = Arrays.stream(this.getAccounts().split(",")).collect(Collectors.toList());
            criteria.andAccountsOrLike(accountList);
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }

        if (this.getUpdateDateFrom() != null) {
            criteria.andUpdateDateGreaterThanOrEqualTo(this.getUpdateDateFrom());
        }
        if (this.getUpdateDateTo() != null) {
            criteria.andUpdateDateLessThanOrEqualTo(this.getUpdateDateTo());
        }
        if (this.getId() != null) {
            criteria.andIdEqualTo(this.getId());
        }

        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils
                .isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        if (!superAdminOrEquivalent.getResult()) {
            List<String> authorEmployeeNos = PermissionsHelper.getAuthorEmployeeNoList(SaleChannel.CHANNEL_SMT);
            if(CollectionUtils.isNotEmpty(authorEmployeeNos)){
                criteria.andCreateByIn(authorEmployeeNos);
            }else{
                throw new RuntimeException("未获取到授权用户！");
            }
        }

        return example;
    }
}