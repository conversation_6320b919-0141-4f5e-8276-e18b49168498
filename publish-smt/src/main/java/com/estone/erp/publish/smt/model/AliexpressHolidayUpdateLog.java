package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressHolidayUpdateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_holiday_update_log.id
     */
    private Integer id;

    /**
     * 商品id database column aliexpress_holiday_update_log.product_id
     */
    private Long productId;

    /**
     * 货号 database column aliexpress_holiday_update_log.article_number
     */
    private String articleNumber;

    /**
     * 原始价格 database column aliexpress_holiday_update_log.from_price
     */
    private Double fromPrice;

    /**
     * 修改后价格 database column aliexpress_holiday_update_log.to_price
     */
    private Double toPrice;

    /**
     * 创建时间 database column aliexpress_holiday_update_log.create_date
     */
    private Timestamp createDate;
}