package com.estone.erp.publish.smt.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> aliexpress_category
 * 2019-10-24 11:41:02
 */
@Data
public class AliexpressCategoryCriteria extends AliexpressCategory {
    private static final long serialVersionUID = 1L;

    private List<Integer> categoryIdList = new ArrayList<>();

    private String categoryIdStr;

    private String categoryEnNameLike;

    private String categoryZhNameLike;

    public AliexpressCategoryExample getExample() {
        AliexpressCategoryExample example = new AliexpressCategoryExample();
        AliexpressCategoryExample.Criteria criteria = example.createCriteria();

        if(StringUtils.isNotBlank(this.getCategoryEnNameLike())){
            criteria.andCategoryEnNameLike("%" + this.getCategoryEnNameLike() +"%");
        }

        if(StringUtils.isNotBlank(this.getCategoryZhNameLike())){
            criteria.andCategoryZhNameLike("%" + this.getCategoryZhNameLike() +"%");
        }

        if(CollectionUtils.isNotEmpty(this.getCategoryIdList())){
            criteria.andCategoryIdIn(this.getCategoryIdList());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (this.getCategoryLevel() != null) {
            criteria.andCategoryLevelEqualTo(this.getCategoryLevel());
        }
        if (this.getLeafCategory() != null) {
            criteria.andLeafCategoryEqualTo(this.getLeafCategory());
        }
        if (this.getIsShow() != null) {
            criteria.andIsShowEqualTo(this.getIsShow());
        }
        if (this.getParentId() != null) {
            criteria.andParentIdEqualTo(this.getParentId());
        }
        if (StringUtils.isNotBlank(this.getCategoryZhName())) {
            criteria.andCategoryZhNameEqualTo(this.getCategoryZhName());
        }
        if (StringUtils.isNotBlank(this.getCategoryEnName())) {
            criteria.andCategoryEnNameEqualTo(this.getCategoryEnName());
        }
        if (StringUtils.isNotBlank(this.getCategoryPtName())) {
            criteria.andCategoryPtNameEqualTo(this.getCategoryPtName());
        }
        if (StringUtils.isNotBlank(this.getCategoryFrName())) {
            criteria.andCategoryFrNameEqualTo(this.getCategoryFrName());
        }
        if (StringUtils.isNotBlank(this.getCategoryRuName())) {
            criteria.andCategoryRuNameEqualTo(this.getCategoryRuName());
        }
        if (StringUtils.isNotBlank(this.getCategoryInName())) {
            criteria.andCategoryInNameEqualTo(this.getCategoryInName());
        }
        if (StringUtils.isNotBlank(this.getCategoryEsName())) {
            criteria.andCategoryEsNameEqualTo(this.getCategoryEsName());
        }
        if (StringUtils.isNotBlank(this.getChildAttributesJson())) {
            criteria.andChildAttributesJsonEqualTo(this.getChildAttributesJson());
        }
        if (StringUtils.isNotBlank(this.getFullPathCode())) {
            criteria.andFullPathCodeEqualTo(this.getFullPathCode());
        }

        if(this.getCarType() != null){
            criteria.andCarTypeEqualTo(this.getCarType());
        }

        if(this.getOrigin() != null){
            criteria.andOriginEqualTo(this.getOrigin());
        }

        return example;
    }
}