package com.estone.erp.publish.smt.enums;


public enum DownTgItemEnum {
    aliexpressAccountNumber("店铺", "aliexpressAccountNumber"),
    productId("商品ID", "productId"),
    skuCode("商品编码", "skuCode"),
    scItemCode("货品编码", "scItemCode"),
    scItemBarCode("货品条码", "scItemBarCode"),
    subject("标题", "subject"),
    skuValueEn("属性值", "skuValueEn"),
    categoryName("分类", "categoryName"),
    supplyPrice("供货价（CNY）", "supplyPrice"),
    saleCostPrice("销售成本价", "saleCostPrice"),
    totalStock("库存", "totalStock"),
    productType("备货类型", "productType"),
    productStatusType("在售状态", "productStatusType"),
    articleNumber("单品货号", "articleNumber"),
    skuStatus("单品状态", "skuStatus"),
    tagNames("产品标签", "tagNames"),
    specialGoodsName("特殊标签", "specialGoodsName"),
    promotion("是否促销", "promotion"),
    newState("是否新品", "newState"),
    grossWeight("重量", "grossWeight"),
    salemanager("销售", "salemanager"),
    salemanagerLeader("销售组长", "salemanagerLeader"),
    salesSupervisorName("销售主管", "salesSupervisorName"),
    gmtCreate("上架时间", "gmtCreate"),
    wsOfflineDate("下架时间", "wsOfflineDate"),
    lastSyncTime("同步时间", "lastSyncTime"),
    gmtModified("编辑时间", "gmtModified"),
    infringementTypeNames("禁售类型", "infringementTypeNames"),
    infringementObjs("禁售原因", "infringementObjs"),
    forbidChannel("禁售平台", "forbidChannel"),
    prohibitionSites("禁售站点", "prohibitionSites"),
    ;

    private String name;
    private String excelValue;

    private DownTgItemEnum(String name, String excelValue) {
        this.name = name;
        this.excelValue = excelValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExcelValue() {
        return excelValue;
    }

    public void setExcelValue(String excelValue) {
        this.excelValue = excelValue;
    }
}
