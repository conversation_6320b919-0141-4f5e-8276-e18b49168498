package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.tidb.publishtidb.model.AdsSmtListingOrderAdjustPriceResult;
import com.estone.erp.publish.tidb.publishtidb.service.AdsSmtListingOrderAdjustPriceResultService;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsSmtListingOrderAdjustPriceResultMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class AdsSmtListingOrderAdjustPriceResultServiceImpl extends ServiceImpl<AdsSmtListingOrderAdjustPriceResultMapper, AdsSmtListingOrderAdjustPriceResult>
    implements AdsSmtListingOrderAdjustPriceResultService{

    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AdsSmtListingOrderAdjustPriceResult> wrapper) {
        List<Map<Object, Object>> mapList = baseMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }

}




