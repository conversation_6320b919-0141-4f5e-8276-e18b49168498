package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressConfigCalcPrice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_config_calc_price.id
     */
    private Integer id;

    /**
     * 配置主键id database column aliexpress_config_calc_price.config_id
     */
    private Integer configId;

    /**
     * 店铺 database column aliexpress_config_calc_price.account
     */
    private String account;

    /**
     * 价格区间 database column aliexpress_config_calc_price.from_price
     */
    private Double fromPrice;

    /**
     * 价格区间 database column aliexpress_config_calc_price.to_price
     */
    private Double toPrice;

    /**
     * 物流模板id database column aliexpress_config_calc_price.shipping_id
     */
    private Integer shippingId;

    /**
     * 产品分组
     */
    private Long groupId;

    /**
     * 创建人 database column aliexpress_config_calc_price.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_config_calc_price.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column aliexpress_config_calc_price.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_config_calc_price.update_date
     */
    private Timestamp updateDate;
}