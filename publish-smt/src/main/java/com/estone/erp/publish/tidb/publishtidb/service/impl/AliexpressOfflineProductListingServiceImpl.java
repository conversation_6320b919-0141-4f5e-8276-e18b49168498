package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressCategoryCriteria;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressOfflineProductListingQueryDO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressOfflineProductListingMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressOfflineProductListing;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressOfflineProductListingService;
import com.estone.erp.publish.tidb.publishtidb.vo.AliexpressOfflineProductListingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 速卖通下架产品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Service
public class AliexpressOfflineProductListingServiceImpl extends ServiceImpl<AliexpressOfflineProductListingMapper, AliexpressOfflineProductListing> implements AliexpressOfflineProductListingService {

    @Resource
    private ExcelSend excelSend;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private AliexpressCategoryService aliexpressCategoryService;

    @Override
    public CQueryResult<AliexpressOfflineProductListingVO> search(CQuery<AliexpressOfflineProductListingQueryDO> cquery) {
        CQueryResult<AliexpressOfflineProductListingVO> result = new CQueryResult<>();
        AliexpressOfflineProductListingQueryDO search = cquery.getSearch();

        try {
            // 分页查询
            IPage<AliexpressOfflineProductListing> pageResult = this.selectPage(search, cquery.getPage(), cquery.getLimit());
            if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                List<AliexpressOfflineProductListingVO> aliexpressOfflineProductListingVOList = pageResult.getRecords().stream().map(registration -> BeanUtil.copyProperties(registration, AliexpressOfflineProductListingVO.class)).collect(Collectors.toList());
                List<String> accountNumberList = aliexpressOfflineProductListingVOList.stream().map(AliexpressOfflineProductListingVO::getAliexpressAccountNumber).collect(Collectors.toList());
                Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SMT);
                for (AliexpressOfflineProductListingVO vo : aliexpressOfflineProductListingVOList) {
                    // 销售、销售组长、销售主管
                    if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                        Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(vo.getAliexpressAccountNumber());
                        vo.setSaleMan(saleSuperiorTriple.getLeft());
                        vo.setSaleTeamLeader(saleSuperiorTriple.getMiddle());
                        vo.setSalesSupervisor(saleSuperiorTriple.getRight());
                    }
                }
                result.setRows(aliexpressOfflineProductListingVOList);
            }

            result.setSuccess(true);
            result.setTotal(pageResult.getTotal());
            result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        } catch (Exception e) {
            log.error("查询竞价SKU基本信息失败", e);
            return CQueryResult.failResult(e.getMessage());
        }

        return result;
    }

    @Override
    public ApiResult<?> downloadTable(CQuery<AliexpressOfflineProductListingQueryDO> cquery) {
        AliexpressOfflineProductListingQueryDO search = cquery.getSearch();
        try {
            // 构建查询条件
            LambdaQueryWrapper<AliexpressOfflineProductListing> lambdaQueryWrapper = getLambdaQueryWrapper(search);
            int count = this.count(lambdaQueryWrapper);
            if (count == 0) {
                return ApiResult.newError("没有数据，无法下载");
            }
            if (count > 500000) {
                return ApiResult.newError("超出最大下载数量限制，无法下载");
            }

            excelSend.downAliexpressOfflineProductListing(ExcelTypeEnum.downloadOfflinetProducListing.getCode(), search);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }


    @Override
    public IPage<AliexpressOfflineProductListing> selectPage(AliexpressOfflineProductListingQueryDO search, int pageSize, int limit) {
        // 构建查询条件
        LambdaQueryWrapper<AliexpressOfflineProductListing> lambdaQueryWrapper = this.getLambdaQueryWrapper(search);

        // 分页查询
        IPage<AliexpressOfflineProductListing> page = new Page<>(pageSize, limit);
        return this.page(page, lambdaQueryWrapper);
    }


    /**
     * 构建查询条件
     *
     * @param search
     * @return
     */
    private LambdaQueryWrapper<AliexpressOfflineProductListing> getLambdaQueryWrapper(AliexpressOfflineProductListingQueryDO search) {
        LambdaQueryWrapper<AliexpressOfflineProductListing> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 默认按最后同步时间降序排序
        lambdaQueryWrapper.orderByDesc(AliexpressOfflineProductListing::getLastSyncTime);

        if (CollectionUtils.isNotEmpty(search.getIdList())) {
            lambdaQueryWrapper.in(AliexpressOfflineProductListing::getId, search.getIdList());
            return lambdaQueryWrapper;
        }

        // 权限处理
        List<String> accountList = search.getAliexpressAccountNumbers();
        if (CollectionUtils.isNotEmpty(accountList) && accountList.get(0).equals("all")) {
            accountList = new ArrayList<>();
        }
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountList, search.getSalesSupervisor(), search.getSaleTeamLeader(), search.getSaleMan(), null, "0", false);
        search.setAliexpressAccountNumbers(authAccountNumbers);

        // 基础信息筛选
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getAliexpressAccountNumbers()),
                AliexpressOfflineProductListing::getAliexpressAccountNumber, search.getAliexpressAccountNumbers());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getArticleNumbers()),
                AliexpressOfflineProductListing::getArticleNumber, search.getArticleNumbers());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getProductIds()),
                AliexpressOfflineProductListing::getProductId, search.getProductIds());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getProductStatusTypeList()),
                AliexpressOfflineProductListing::getProductStatusType, search.getProductStatusTypeList());

        // 特殊处理
        if (CollectionUtils.isNotEmpty(search.getCategoryIds())) {
            AliexpressCategoryCriteria criteria = new AliexpressCategoryCriteria();
            criteria.setCategoryIdList(search.getCategoryIds());
            List<AliexpressCategory> aliexpressCategoryList = aliexpressCategoryService.searchCategoryTree(criteria.getExample());
            if (CollectionUtils.isNotEmpty(aliexpressCategoryList)) {
                List<Integer> categoryIdList = aliexpressCategoryList.stream().map(AliexpressCategory::getCategoryId).collect(Collectors.toList());
                lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getCategoryIds()), AliexpressOfflineProductListing::getCategoryId, categoryIdList);
            }
        }

        // 销量数据筛选
        if (search.getOrder24hCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrder24hCount, search.getOrder24hCountFrom());
        }
        if (search.getOrder24hCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrder24hCount, search.getOrder24hCountTo());
        }

        if (search.getOrderLast7dCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderLast7dCount, search.getOrderLast7dCountFrom());
        }
        if (search.getOrderLast7dCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderLast7dCount, search.getOrderLast7dCountTo());
        }

        if (search.getOrderLast14dCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderLast14dCount, search.getOrderLast14dCountFrom());
        }
        if (search.getOrderLast14dCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderLast14dCount, search.getOrderLast14dCountTo());
        }

        if (search.getOrderLast30dCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderLast30dCount, search.getOrderLast30dCountFrom());
        }
        if (search.getOrderLast30dCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderLast30dCount, search.getOrderLast30dCountTo());
        }

        if (search.getOrderLast60dCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderLast60dCount, search.getOrderLast60dCountFrom());
        }
        if (search.getOrderLast60dCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderLast60dCount, search.getOrderLast60dCountTo());
        }

        if (search.getOrderLast180dCountFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderLast180dCountNew, search.getOrderLast180dCountFrom());
        }
        if (search.getOrderLast180dCountTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderLast180dCountNew, search.getOrderLast180dCountTo());
        }

        if (search.getOrderNumTotalFrom() != null) {
            lambdaQueryWrapper.ge(AliexpressOfflineProductListing::getOrderNumTotal, search.getOrderNumTotalFrom());
        }
        if (search.getOrderNumTotalTo() != null) {
            lambdaQueryWrapper.lt(AliexpressOfflineProductListing::getOrderNumTotal, search.getOrderNumTotalTo());
        }

        return lambdaQueryWrapper;
    }
}
