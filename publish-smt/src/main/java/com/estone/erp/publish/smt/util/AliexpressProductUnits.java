package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.AliexpressListingDataSourceEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressTgProductListing;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.smt.bean.HalfItemCalcPriceRequest;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.bean.SkuProperty.AeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.product.AeopSkuPropertyList;
import com.estone.erp.publish.smt.bean.SkuProperty.product.ProductSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.temp.TempSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgAeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgScItemInfoDto;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgTempSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgWarehouse;
import com.estone.erp.publish.smt.call.direct.dto.pre.*;
import com.estone.erp.publish.smt.call.direct.half.HalfTgAccounInfoCall;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.controller.AliexpressHalfTgItemController;
import com.estone.erp.publish.smt.enums.TemplateTgStatusEnum;
import com.estone.erp.publish.smt.enums.TgItemProductTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.SmtConfigHalfLableService;
import com.estone.erp.publish.smt.service.SmtConfigHalfPriceIntervalService;
import com.estone.erp.publish.smt.template.attribute.AliexpressTemplateAttributeHelper;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/1314:14
 */
@Slf4j
public class AliexpressProductUnits {

    public static final Map<Integer, String> PRODUCT_UNITS = new LinkedHashMap<Integer, String>() {
        private static final long serialVersionUID = -506153284015657628L;

        {
            super.put(100000015, "件/个 (piece/pieces)");
            super.put(100000000, "袋 (bag/bags)");
            super.put(100000001, "桶 (barrel/barrels)");
            super.put(100000002, "蒲式耳 (bushel/bushels)");
            super.put(100078580, "箱 (carton)");
            super.put(100078581, "厘米 (centimeter)");
            super.put(100000003, "立方米 (cubic meter)");
            super.put(100000004, "打 (dozen)");
            super.put(100078584, "英尺 (feet)");
            super.put(100000005, "加仑 (gallon)");
            super.put(100000006, "克 (gram)");
            super.put(100078587, "英寸 (inch)");
            super.put(100000007, "千克 (kilogram)");
            super.put(100078589, "千升 (kiloliter)");
            super.put(100000008, "千米 (kilometer)");
            super.put(100078559, "升 (liter/liters)");
            super.put(100000009, "英吨 (long ton)");
            super.put(100000010, "米 (meter)");
            super.put(100000011, "公吨 (metric ton)");
            super.put(100078560, "毫克 (milligram)");
            super.put(100078596, "毫升 (milliliter)");
            super.put(100078597, "毫米 (millimeter)");
            super.put(100000012, "盎司 (ounce)");
            super.put(100000014, "包 (pack/packs)");
            super.put(100000013, "双 (pair)");
            super.put(100000016, "磅 (pound)");
            super.put(100078603, "夸脱 (quart)");
            super.put(100000017, "套 (set/sets)");
            super.put(100000018, "美吨 (short ton)");
            super.put(100078606, "平方英尺 (square feet)");
            super.put(100078607, "平方英寸 (square inch)");
            super.put(100000019, "平方米 (square meter)");
            super.put(100078609, "平方码 (square yard)");
            super.put(100000020, "吨 (ton)");
            super.put(100078558, "码 (yard/yards)");
        }
    };


    public static AliexpressTemplate compileProductToTemplate(AliexpressProduct product, String sku) {

        AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

        Boolean wholesale = product.getIsWholesale();
        Integer bulkOrder = product.getBulkOrder();
        Integer bulkDiscount = product.getBulkDiscount();

        Integer deliveryTime = product.getDeliveryTime();
        Long freightTemplateId = product.getFreightTemplateId();
        Long promiseTemplateId = product.getPromiseTemplateId();
        Long groupId = product.getGroupId();

        AliexpressTemplate template = new AliexpressTemplate();

        template.setArticleNumber(sku);
        template.setIsWholesale(wholesale);
        template.setBulkOrder(bulkOrder);
        template.setBulkDiscount(bulkDiscount);
        // 库存扣减方式
        template.setReduceStrategy(product.getReduceStrategy());
        template.setDeliveryTime(deliveryTime);
        template.setFreightTemplateId(freightTemplateId);
        template.setPromiseTemplateId(promiseTemplateId);
        template.setGroupId(groupId);

        template.setProductStock(product.getIpmSkuStock());
        template.setProductPrice(product.getProductPrice());

        template.setAliexpressAccountNumber(product.getAliexpressAccountNumber());

        // 随机加样式
        // String detail = productSource.getDetail();
        //String spanStyle = AliexpressContentUtils.randomStyle(detail);
        template.setDetail(product.getDetail());

        template.setAeopAeProductSkusJson(product.getAeopAeProductSkusJson());
        template.setCategoryId(product.getCategoryId());

        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(product.getCategoryId());
        if(aliexpressCategory != null){
            template.setCategoryTableId(Long.valueOf(aliexpressCategory.getId()));
            template.setCategoryName(aliexpressCategory.getCategoryZhName());
        }

        template.setSubject(product.getSubject());

        String imageUrls = product.getImageUrls();

        List<String> splitList = CommonUtils.splitList(imageUrls, ";");
        // 乱序
        //Collections.shuffle(splitList);
        // 随机首图
        Random random = new Random();
        int n = random.nextInt(splitList.size());
        if (n!=0){
            Collections.swap(splitList,0,n);
        }
        template.setImageUrls(StringUtils.join(splitList, ";"));
        template.setProductUnit(product.getProductUnit());
        template.setPackageType(product.getPackageType());
        template.setLotNum(product.getLotNum());
        template.setPackageLength(product.getPackageLength());
        template.setPackageWidth(product.getPackageWidth());
        template.setPackageHeight(product.getPackageHeight());
        template.setGrossWeight(product.getGrossWeight());
        template.setIsPackSell(product.getIsPackSell());
        template.setBaseUnit(product.getBaseUnit());
        template.setAddUnit(product.getAddUnit());
        template.setAddWeight(product.getAddWeight());
        template.setWsValidNum(product.getWsValidNum());
        template.setAeopAeProductPropertysJson(product.getAeopAeProductPropertysJson());
        template.setSizeChartId(product.getSizeChartId());
        template.setCurrencyCode(product.getCurrencyCode());

        template.setMobileDetail(product.getDetail());
        Date couponStartDate = product.getCouponStartDate();
        if (couponStartDate != null) {
            template.setCouponStartDate(new Timestamp(couponStartDate.getTime()));
        }
        Date couponEndDate = product.getCouponEndDate();
        if (couponEndDate != null) {
            template.setCouponEndDate(new Timestamp(couponEndDate.getTime()));
        }

        template.setAeopNationalQuoteConfiguration(product.getAeopNationalQuoteConfiguration());
        template.setAeopAeMultimedia(product.getAeopAeMultimedia());

        template.setDisplayImageUrl(
                CommonUtils.splitList(template.getImageUrls(), ";").get(0));
        return template;
    }

    public static AliexpressTgTemplate compileEsTgProductToTgTemplate(EsAliexpressTgProductListing esProduct, AliexpressEsTgExtend aliexpressEsExtend) {
        AliexpressTgTemplate tgTemplate = new AliexpressTgTemplate();

        String spu = esProduct.getSpu();
        if(StringUtils.isBlank(spu)){
            spu = ProductUtils.getMainSku(esProduct.getArticleNumber());
        }
        tgTemplate.setArticleNumber(spu);
        tgTemplate.setImageUrls(esProduct.getImageUrls());
        tgTemplate.setAliexpressAccountNumber(esProduct.getAliexpressAccountNumber());
        tgTemplate.setAeopAeProductSkusJson(aliexpressEsExtend.getAeopAeProductSkusJson());
        tgTemplate.setAeopAeProductPropertysJson(aliexpressEsExtend.getAeopAeProductPropertysJson());
        tgTemplate.setSubject(esProduct.getSubject());
        tgTemplate.setCategoryId(esProduct.getCategoryId());
        tgTemplate.setCategoryName(esProduct.getCategoryName());
        tgTemplate.setProductUnit(esProduct.getProductUnit());
        tgTemplate.setPackageType(esProduct.getPackageType());
        tgTemplate.setLotNum(esProduct.getLotNum());
        tgTemplate.setCurrencyCode(esProduct.getCurrencyCode());
        tgTemplate.setAeopQualificationStructJson(aliexpressEsExtend.getAeopQualificationStructList());
        tgTemplate.setSpecialProductTypeListStr(StringUtils.join(esProduct.getSpecialProductTypeList(), ","));
        tgTemplate.setMsrEuId(esProduct.getMsrEuId());
        tgTemplate.setSizeChartId(esProduct.getSizeChartId());
        tgTemplate.setSizeChartIdList(esProduct.getSizechartIdList());
        String productType = esProduct.getProductType();
        if(StringUtils.isNotBlank(productType) && StringUtils.equalsIgnoreCase(productType, TgItemProductTypeEnum.CF.getCode())){
            productType = TgItemProductTypeEnum.JIT.getCode();
        }
        tgTemplate.setProductType(productType);

        String squareImg = aliexpressEsExtend.getSquareImg();
        String longImg = aliexpressEsExtend.getLongImg();
        if(StringUtils.isNotBlank(squareImg) || StringUtils.isNotBlank(longImg)){
            List<MarketImage> marketImageList = new ArrayList<>();
            if(StringUtils.isNotBlank(squareImg)){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("2");
                marketImage.setUrl(squareImg);
                marketImageList.add(marketImage);
            }

            if(StringUtils.isNotBlank(longImg)){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("1");
                marketImage.setUrl(longImg);
                marketImageList.add(marketImage);
            }
            tgTemplate.setMarketImagesJson(JSON.toJSONString(marketImageList));
        }

        tgTemplate.setVideoLink(aliexpressEsExtend.getAeopAeMultimedia());
        tgTemplate.setDetail(aliexpressEsExtend.getDetail());
        tgTemplate.setMobileDetail(aliexpressEsExtend.getMobileDetail());

        //制造商
        tgTemplate.setManufactureId(esProduct.getManufactureId());
        tgTemplate.setManufactureName(esProduct.getManufactureName());
        tgTemplate.setHacodeJson(esProduct.getHscode());
        return tgTemplate;
    }


    public static PreItemSubmit compilePreItem(List<EsAliexpressProductListing> esAliexpressProductListing, List<SmtConfigHalfLable> smtConfigHalfLables, List<SmtConfigHalfPriceInterval> smtConfigHalfPriceIntervals, AliexpressConfig aliexpressConfig) throws Exception{
        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
            return new PreItemSubmit();
        }
        EsAliexpressProductListing dbItem = esAliexpressProductListing.get(0);
        String account = dbItem.getAliexpressAccountNumber();
        long begin = System.currentTimeMillis();
        Boolean cny = aliexpressConfig.getCny();
        Double rate = null;
        //获取汇率
        if(cny != null && cny){
            rate = 1.0;
        }else{
            ApiResult<Double> rateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.USD, CurrencyConstant.CNY);
            if (rateResult.isSuccess()) {
                rate = rateResult.getResult();
            } else {
                throw new RuntimeException(String.format("获取汇率失败：%s", rateResult.getErrorMsg()));
            }
        }
        long end = System.currentTimeMillis();
        log.info("获取汇率耗时："  + (end -begin));

        begin = System.currentTimeMillis();
        AliexpressTemplateAttributeHelper aliexpressTemplateAttributeHelper = SpringUtils.getBean(AliexpressTemplateAttributeHelper.class);
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        HalfTgAccounInfoCall accounInfoCall = new HalfTgAccounInfoCall();
        ResponseJson responseJson = accounInfoCall.accountInfo(saleAccountByAccountNumber);
        if(!responseJson.isSuccess()){
            throw new Exception(responseJson.getMessage());
        }
        end = System.currentTimeMillis();
        log.info("获取店铺半托管信息耗时："  + (end -begin));

        Map<String, Object> bodyMap = responseJson.getBody();
        JSONArray pop_choice_warehouse = (JSONArray)bodyMap.get("warehouse_list");

        //默认仓库
        PopChoiceSkuWarehouseStock defaultWarehouseStock = new PopChoiceSkuWarehouseStock();
        for (int i = 0; i < pop_choice_warehouse.size(); i++) {
            JSONObject jsonObject = pop_choice_warehouse.getJSONObject(i);
            String warehouse_name = jsonObject.getString("warehouse_name");
            String warehouse_code = jsonObject.getString("warehouse_code");
            if(i == 0 || StringUtils.equalsIgnoreCase(warehouse_name, "东莞JIT库存")){
                defaultWarehouseStock.setWarehouseCode(warehouse_code);
                defaultWarehouseStock.setWarehouseName(warehouse_name);
            }
        }

        //国家
        List<String> joinedCountryList = new ArrayList<>();
        JSONArray pop_choice_country = (JSONArray)bodyMap.get("country_list");
        for (int i = 0; i < pop_choice_country.size(); i++) {
            JSONObject jsonObject = pop_choice_country.getJSONObject(i);
            joinedCountryList.add(jsonObject.getString("country_code"));
        }

        begin = System.currentTimeMillis();
        //管理单品
        List<String> articleNumberList = esAliexpressProductListing.stream().map(t -> t.getArticleNumber()).collect(Collectors.toList());
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(articleNumberList);
        Map<String, ProductInfo> sonSkuInfoMap = productInfoList.stream().collect(Collectors.toMap(o -> (o.getSonSku()), o -> o, (k1, k2) -> k1));
        end = System.currentTimeMillis();
        log.info("调用产品信息耗时："  + (end -begin));

//        begin = System.currentTimeMillis();
//        Map<String, SkuPackingmaterialPriceResponse> packMap = new HashMap<>();
//        List<SkuPackingmaterialPriceResponse> packingmaterialPriceBySkus = ProductUtils.getPackingmaterialPriceBySkus(articleNumberList);
//        if(CollectionUtils.isNotEmpty(packingmaterialPriceBySkus)){
//            for (SkuPackingmaterialPriceResponse priceBySkus : packingmaterialPriceBySkus) {
//                String sku = priceBySkus.getSku();
//                packMap.put(sku, priceBySkus);
//            }
//        }
//        end = System.currentTimeMillis();
//        log.info("获取包装费耗时："  + (end -begin));

        PreItemSubmit preItemSubmit = new PreItemSubmit();
        preItemSubmit.setCurrencyCode(dbItem.getCurrencyCode());
        preItemSubmit.setCategoryId(dbItem.getCategoryId());
        preItemSubmit.setProductId(dbItem.getProductId());
        preItemSubmit.setJoinedCountryList(joinedCountryList);

        Double addWeight = aliexpressConfig.getAddWeight(); //店铺配置新增重量
        if (addWeight == null) {
            addWeight = 0D;
        }

        //统计匹配平台特殊标签个数
        int tagCodeCount = 0;

        List<ProductSku> productSkuList = new ArrayList<>();
        preItemSubmit.setProductSkuList(productSkuList);
        for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
            ProductSku productSku = new ProductSku();
            productSku.setSkuCode(aliexpressProductListing.getSkuCode());
            productSku.setSkuId(aliexpressProductListing.getPlatSkuId());
            productSkuList.add(productSku);
            String articleNumber = aliexpressProductListing.getArticleNumber();
            Integer skuSystemStock = SkuStockUtils.getSkuStockToEbay(articleNumber); //可用库存 - 待发
            //库存
            PopChoiceSkuWarehouseStock popChoiceSkuWarehouseStock = new PopChoiceSkuWarehouseStock();
            popChoiceSkuWarehouseStock.setWarehouseCode(defaultWarehouseStock.getWarehouseCode());
            popChoiceSkuWarehouseStock.setWarehouseName(defaultWarehouseStock.getWarehouseName());
            popChoiceSkuWarehouseStock.setSellableQuantity(skuSystemStock);
            productSku.setPopChoiceSkuWarehouseStock(popChoiceSkuWarehouseStock);

            //货品
            PopChoiceProductSkuScItemInfo popChoiceProductSkuScItemInfo = new PopChoiceProductSkuScItemInfo();
            popChoiceProductSkuScItemInfo.setOriginalBox("1"); //默认原箱
            String scItemBarCode = articleNumber.replaceAll("[^a-zA-Z0-9]", "");
            popChoiceProductSkuScItemInfo.setScItemBarCode(scItemBarCode);
            popChoiceProductSkuScItemInfo.setScItemCode(scItemBarCode);
            productSku.setPopChoiceProductSkuScItemInfo(popChoiceProductSkuScItemInfo);

            //颜色尺寸等属性
            List<SkuProperty> skuPropertyList = new ArrayList<>();
            String aeopSKUPropertyList = aliexpressProductListing.getAeopSKUPropertyList();
            if(StringUtils.isNotBlank(aeopSKUPropertyList)){
                JSONObject jsonObject = JSONObject.parseObject(aeopSKUPropertyList);
                if(jsonObject.containsKey("aeop_sku_property")){
                    JSONArray aeop_sku_property = jsonObject.getJSONArray("aeop_sku_property");
                    for (int i = 0; i < aeop_sku_property.size(); i++) {
                        SkuProperty skuProperty = new SkuProperty();
                        JSONObject jsonObject1 = aeop_sku_property.getJSONObject(i);
                        Long sku_property_id = jsonObject1.getLong("sku_property_id");
                        Long property_value_id = jsonObject1.getLong("property_value_id");
                        String property_value_definition_name = jsonObject1.getString("property_value_definition_name");
                        String sku_image = jsonObject1.getString("sku_image");
                        skuProperty.setSkuImage(sku_image);
                        skuProperty.setPropertyValueDefinitionName(property_value_definition_name);
                        skuProperty.setSkuPropertyIdLong(sku_property_id);
                        skuProperty.setPropertyValueIdLong(property_value_id);
                        skuPropertyList.add(skuProperty);
                    }
                }
                productSku.setSkuPropertyList(skuPropertyList);
            }

            //匹配店铺配置 获取 特殊标签 和 毛利率
            String configTag = "";
            Double configSaleCost = null;
            Double configWeight = null; //注意单位是g 提交到平台的重量是kg

            ProductInfo productInfo = sonSkuInfoMap.get(articleNumber);
            if(productInfo != null){
                // 默认取SKU在产品系统的包裹尺寸，为空则取产品规格
                BigDecimal length = null;
                BigDecimal width = null;
                BigDecimal height = null;
                if(productInfo.getPackLength() != null && productInfo.getPackWidth() != null && productInfo.getPackHeight() != null){
                    length = productInfo.getPackLength();
                    width = productInfo.getPackWidth();
                    height = productInfo.getPackHeight();
                }else if(productInfo.getLength() != null && productInfo.getWide() != null && productInfo.getHeight() != null){
                    length = productInfo.getLength();
                    width = productInfo.getWide();
                    height = productInfo.getHeight();
                }
                if(length != null){
                    productSku.setPackageLenght(length.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(width != null){
                    productSku.setPackageWidth(width.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(height != null){
                    productSku.setPackageHeight(height.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                //重量g
                double input = AliexpressWeightUtils.getMaxWeight(productInfo, addWeight);

                boolean isAerateCode = false;
                String packingMaterialsCode = productInfo.getPackingMaterialsCode();
                List<String> matchMaterialsCodeList = productInfo.getMatchMaterialsCodeList();
                if(CollectionUtils.isEmpty(matchMaterialsCodeList)){
                    matchMaterialsCodeList = new ArrayList<>();
                }
                if(StringUtils.isNotBlank(packingMaterialsCode)){
                    matchMaterialsCodeList.add(packingMaterialsCode);
                }
                if(CollectionUtils.isNotEmpty(matchMaterialsCodeList)){
                    for (String matchMaterialsCode : matchMaterialsCodeList) {
                        if(AliexpressHalfTgItemController.aerateCodeList.contains(matchMaterialsCode)){
                            isAerateCode = true;
                            break;
                        }
                    }
                }
                if(isAerateCode){
                    input += 20.00;
                }

                double weightKg = AliexpressWeightUtils.gramsToKilograms(input, 3, RoundingMode.UP);
                productSku.setPackageWeight(weightKg);

                configWeight = NumberUtils.format(weightKg * 1000);
                configSaleCost = productInfo.getSaleCost() == null ? 0.00 : productInfo.getSaleCost().doubleValue();
                configTag = productInfo.getEnTag();
                configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(configTag);
            }else{
                ComposeSku composeProduct = null;
                SuiteSku suiteSku = null;
                Long dataSourceType = aliexpressProductListing.getDataSourceType();
                if(AliexpressListingDataSourceEnum.COMPOSE.getCode() == dataSourceType){
                    Boolean existSaleSuit = ProductUtils.isExistSaleSuite(articleNumber);
                    if (existSaleSuit) {
                        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
                        String mappingSpu = MapUtils.getString(composeSkuSuitMap, articleNumber, null);
                        if(StringUtils.isNotBlank(mappingSpu)){
                            //组合产品
                            articleNumber = mappingSpu;
                            composeProduct = ProductUtils.getComposeProduct(articleNumber);
                        }else{
                            //套装
                            suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
                        }
                    }else{
                        //组合产品
                        composeProduct = ProductUtils.getComposeProduct(articleNumber);
                    }
                }

                if(composeProduct != null){
                    configSaleCost = composeProduct.getSaleCost()  == null ? 0.00 : composeProduct.getSaleCost().doubleValue();
                    configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(composeProduct.getTagCode());
                    Double packageWeight = AliexpressWeightUtils.getMaxWeight(composeProduct, addWeight, true);
                    if(packageWeight != null){
                        double weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight, 3, RoundingMode.UP);
                        productSku.setPackageWeight(weightKg);
                        configWeight = NumberUtils.format(weightKg * 1000);
                    }
                }else if(suiteSku != null){
                    Double packageWeight = AliexpressWeightUtils.getMaxWeight(suiteSku, addWeight, true);
                    if(packageWeight != null){
                        double weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight, 3, RoundingMode.UP);
                        productSku.setPackageWeight(weightKg);
                        configWeight = NumberUtils.format(weightKg * 1000);
                    }
                    //标签优先级
                    configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(suiteSku.getTagCode());
                    configSaleCost = suiteSku.getAllSingleCost() == null ? 0.00 : suiteSku.getAllSingleCost().doubleValue();
                }
            }

            //标签匹配
            if(CollectionUtils.isNotEmpty(smtConfigHalfLables) && StringUtils.isNotBlank(configTag)){
                //多个标签安装优先级匹配
                List<String> strings = CommonUtils.splitList(configTag, ",");
                for (String string : strings) {
                    List<SmtConfigHalfLable> collect = smtConfigHalfLables.stream().filter(t -> StringUtils.equalsIgnoreCase(t.getProductTag(), string)).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect)){
                        tagCodeCount ++; // 匹配成功
                        String platformTag = collect.get(0).getPlatformTag();
                        if(StringUtils.isNotBlank(platformTag)){
                            popChoiceProductSkuScItemInfo.setSpecialProductTypeList(Arrays.asList(platformTag));
                        }
                        break;
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(smtConfigHalfPriceIntervals) && configSaleCost != null && configWeight != null){
                HalfItemCalcPriceRequest priceRequest = new HalfItemCalcPriceRequest();
                priceRequest.setAccout(account);
                priceRequest.setArticleNumber(articleNumber);
                priceRequest.setRate(rate);
                priceRequest.setSonSkuInfoMap(sonSkuInfoMap);
                priceRequest.setAliexpressConfig(aliexpressConfig);
                priceRequest.setSmtConfigHalfPriceIntervals(smtConfigHalfPriceIntervals);
                priceRequest.setConfigWeight(configWeight);
                priceRequest.setConfigSaleCost(configSaleCost);
                try {
                    Double aDouble = calcHalfPrice(priceRequest);
                    if(aDouble != null){
                        productSku.setBasePrice(aDouble);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        if(articleNumberList.size() != tagCodeCount){
            preItemSubmit.setSpecialProductTypeDeletion(true);
        }
        return preItemSubmit;
    }


    //半托管算价
    public static Double calcHalfPrice(HalfItemCalcPriceRequest calcPriceRequest){
        if(calcPriceRequest == null || StringUtils.isBlank(calcPriceRequest.getAccout())
                || StringUtils.isBlank(calcPriceRequest.getArticleNumber())
                || calcPriceRequest.getRate() == null){
            return null;
        }

        Double rate = calcPriceRequest.getRate();
        String accout = calcPriceRequest.getAccout();
        String articleNumber = calcPriceRequest.getArticleNumber();
        Map<String, ProductInfo> sonSkuInfoMap = calcPriceRequest.getSonSkuInfoMap();

        AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
        SmtConfigHalfPriceIntervalService smtConfigHalfPriceIntervalService = SpringUtils.getBean(SmtConfigHalfPriceIntervalService.class);

        AliexpressConfig aliexpressConfig = calcPriceRequest.getAliexpressConfig();
        if(aliexpressConfig == null){
            aliexpressConfig = aliexpressConfigService.selectByAccount(accout);
        }

        List<SmtConfigHalfPriceInterval> smtConfigHalfPriceIntervals = calcPriceRequest.getSmtConfigHalfPriceIntervals();
        if(CollectionUtils.isEmpty(smtConfigHalfPriceIntervals)){
            SmtConfigHalfPriceIntervalExample intervalExample = new SmtConfigHalfPriceIntervalExample();
            intervalExample.createCriteria().andAccountEqualTo(accout);
            smtConfigHalfPriceIntervals = smtConfigHalfPriceIntervalService.selectByExample(intervalExample);
        }
        Double configSaleCost = calcPriceRequest.getConfigSaleCost();
        Double configWeight = calcPriceRequest.getConfigWeight();

        ProductInfo productInfo = sonSkuInfoMap.get(articleNumber);
        if (productInfo != null) {
            //单位g
            Double maxWeight = AliexpressWeightUtils.getMaxWeight(productInfo, null);
            double weightKg = AliexpressWeightUtils.gramsToKilograms(maxWeight, 3, RoundingMode.UP);
            configWeight = NumberUtils.format(weightKg * 1000);
            configSaleCost = productInfo.getSaleCost() == null ? 0.00 : productInfo.getSaleCost().doubleValue();
        }else{
            ComposeSku composeProduct = null;
            SuiteSku suiteSku = null;
            Boolean existSaleSuit = ProductUtils.isExistSaleSuite(articleNumber);
            if (existSaleSuit) {
                Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
                String mappingSpu = MapUtils.getString(composeSkuSuitMap, articleNumber, null);
                if(StringUtils.isNotBlank(mappingSpu)){
                    //组合产品
                    articleNumber = mappingSpu;
                    composeProduct = ProductUtils.getComposeProduct(articleNumber);
                }else{
                    //套装
                    suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
                }
            }else{
                //组合产品
                composeProduct = ProductUtils.getComposeProduct(articleNumber);
            }

            if (composeProduct != null) {
                configSaleCost = composeProduct.getSaleCost() == null ? 0.00 : composeProduct.getSaleCost().doubleValue();
                Double packageWeight = AliexpressWeightUtils.getMaxWeight(composeProduct, null, true);
                if (packageWeight != null) {
                    double weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight, 3, RoundingMode.UP);
                    configWeight = NumberUtils.format(weightKg * 1000);
                }
            } else if (suiteSku != null) {
                Double packageWeight = AliexpressWeightUtils.getMaxWeight(suiteSku, null, true);
                if (packageWeight != null) {
                    double weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight, 3, RoundingMode.UP);
                    configWeight = NumberUtils.format(weightKg * 1000);
                }
                configSaleCost = suiteSku.getAllSingleCost() == null ? 0.00 : suiteSku.getAllSingleCost().doubleValue();
            }
        }

        if(aliexpressConfig == null || StringUtils.isBlank(aliexpressConfig.getAccount()) || configSaleCost == null || configWeight == null){
            return null;
        }

        //获取毛利率
        Double ratio = null;
        if(CollectionUtils.isNotEmpty(smtConfigHalfPriceIntervals) && configSaleCost != null && configWeight != null){
            double defaultMaxValue = *********.00d;
            //销售成本价必填，如果满足销售成本价 优先取有配置重量区间的数据
            for (SmtConfigHalfPriceInterval smtConfigHalfPriceInterval : smtConfigHalfPriceIntervals) {
                double fromPrice = smtConfigHalfPriceInterval.getFromPrice() == null ? 0.0 : smtConfigHalfPriceInterval.getFromPrice();
                double toPrice = smtConfigHalfPriceInterval.getToPrice() == null ? defaultMaxValue : smtConfigHalfPriceInterval.getToPrice();
                double fromWeight = smtConfigHalfPriceInterval.getFromWeight() == null ? 0.0 : smtConfigHalfPriceInterval.getFromWeight();
                double toWeight = smtConfigHalfPriceInterval.getToWeight() == null ? defaultMaxValue : smtConfigHalfPriceInterval.getToWeight();
                if(configSaleCost > fromPrice && configSaleCost <= toPrice && configWeight > fromWeight && configWeight <= toWeight){
                    ratio = smtConfigHalfPriceInterval.getRatio();
                    if(toWeight != defaultMaxValue){ //defaultMaxValue 算没有配置重量区间，如果不等于就说明找到了有配置好的重量区间
                        break;
                    }
                }
            }
        }

        //毛利率和汇率 如果毛利率不为空 必然有销售成本价
        //http://************:8080/browse/ES-7672
        if(ratio != null && rate != null){
//            if(ratio > 0.9d){
//                log.error("毛利率超过了0.9");
//                throw new RuntimeException("毛利率超过了0.9");
//            }

            //站点
            String site =  rate.doubleValue() == 1.0d ? "CN" : "US";

            //支付公式费率
            Double paymentFee = PublishRedisClusterUtils.hGet(RedisConstant.SMT_PAYMENT_COMMISSION_FORMULA_KEY, Double.class, site);
            if(paymentFee == null){
                throw new RuntimeException("支付费率为空 key: " + RedisConstant.SMT_PAYMENT_COMMISSION_FORMULA_KEY + " site:" + site);
            }

            //交易公式费率
            Double tradeFee = PublishRedisClusterUtils.hGet(RedisConstant.SMT_TRANSACTION_COMMISSION_FORMULA_KEY, Double.class, site);
            if(tradeFee == null){
                throw new RuntimeException("交易费率为空 key: " + RedisConstant.SMT_TRANSACTION_COMMISSION_FORMULA_KEY + " site:" + site);
            }

            //揽收运费(半托管)
            Double halfShippingCost = (aliexpressConfig.getHalfShippingCost() == null || aliexpressConfig.getHalfShippingCost() < 0.0) ? 0.0 : aliexpressConfig.getHalfShippingCost();
            //操作费(半托管)
            Double halfOperateCost = (aliexpressConfig.getHalfOperateCost() == null || aliexpressConfig.getHalfOperateCost() < 0.0) ? 0.0 : aliexpressConfig.getHalfOperateCost();
            //计算公式 (销售成本价 + 操作费 + 揽收运费)/(1-10%-利润率)/汇率   若店铺是CNY店铺，则不用除以汇率
//            log.info(String.format("(店铺[%s] 货号[%s] 销售成本价[%s] + 操作费[%s] + 揽收运费[%s])/(1-0.1-利润率[%s])/汇率[%s]", aliexpressConfig.getAccount(), articleNumber, configSaleCost, halfOperateCost, halfShippingCost, ratio, rate));
//            double format = NumberUtils.format((configSaleCost + halfOperateCost + halfShippingCost) /(0.9d - ratio)/ rate);
            //http://************:8080/browse/ES-9139
            // 新公式
            //计算公式：(销售成本价 + 操作费 + 产品重量g*揽收运费/1000)/(1-支付费率-佣金率-利润率)/汇率，若店铺是CNY店铺，则不用除以汇率；
            String msg = String.format("(店铺[%s] 货号[%s] 销售成本价[%s] + 操作费[%s] + (产品重量g[%s] * 揽收运费[%s])/1000)/(1-支付费率[%s]-佣金率[%s]-利润率[%s])/汇率[%s]",
                    aliexpressConfig.getAccount(), articleNumber, configSaleCost, halfOperateCost, configWeight, halfShippingCost, paymentFee, tradeFee, ratio, rate);
            log.info(msg);
            double v = 1.0d - paymentFee - tradeFee - ratio;
            if(v <= 0){
                throw new RuntimeException("半托管计算公式 分母小于等于0 " + msg);
            }
            double halfPrice = NumberUtils.format((configSaleCost + halfOperateCost + (halfShippingCost * configWeight)/1000 ) / v / rate);
            return halfPrice;
        }
        return null;
    }

    /**
     * 在线列表转成托管模板
     * @param esProduct
     * @param aliexpressEsExtend
     * @return
     */
    public static AliexpressTgTemplate compileEsProductToTgTemplate(EsAliexpressProductListing esProduct, AliexpressEsExtend aliexpressEsExtend, String account, Double grossMargin) throws Exception{
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        SmtConfigHalfLableService smtConfigHalfLableService = SpringUtils.getBean(SmtConfigHalfLableService.class);
        AliexpressTemplateAttributeHelper aliexpressTemplateAttributeHelper = SpringUtils.getBean(AliexpressTemplateAttributeHelper.class);

        //查询半托管的特殊标签配置
        SmtConfigHalfLableExample lableExample = new SmtConfigHalfLableExample();
        lableExample.createCriteria().andAccountEqualTo(account);
        List<SmtConfigHalfLable> smtConfigHalfLables = smtConfigHalfLableService.selectByExample(lableExample);
        if(CollectionUtils.isEmpty(smtConfigHalfLables)){
            throw new Exception(account + " 无半托管特殊标签配置");
        }
        //统计匹配平台特殊标签个数
        int tagCodeCount = 0;

        AliexpressTgTemplate tgTemplate = new AliexpressTgTemplate();
        tgTemplate.setProductType(TgItemProductTypeEnum.JIT.getCode());
        String spu = esProduct.getSpu();
        if(StringUtils.isBlank(spu)){
            spu = ProductUtils.getMainSku(esProduct.getArticleNumber());
        }

        tgTemplate.setApplyState(ApplyStatusEnum.YES.getIntCode());
        tgTemplate.setIsParent(false);
        tgTemplate.setTemplateStatus(TemplateTgStatusEnum.WAIT_PUBLISH.getCode());
        tgTemplate.setArticleNumber(spu);

        //获取原产品的前缀
        String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
        String skuPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();

        SaleAccountAndBusinessResponse newSaleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);

        String imageUrls = esProduct.getImageUrls();
        List<String> imageUrlList = CommonUtils.splitList(imageUrls, ";");
        tgTemplate.setMainImg(imageUrlList.get(0));
        tgTemplate.setImageUrls(imageUrls);
        tgTemplate.setAliexpressAccountNumber(account); //店铺设置
        String aeopAeProductSkusJson = aliexpressEsExtend.getAeopAeProductSkusJson();
        if(StringUtils.isNotBlank(aeopAeProductSkusJson)){
            List<ProductSkuProperty> skuPropertyList = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<ProductSkuProperty>>() {
            });
            List<TgTempSkuProperty> tgTempSkuPropertyList = new ArrayList<>();
            for (ProductSkuProperty productSkuProperty : skuPropertyList) {
                String sku_code = productSkuProperty.getSku_code();
                //货号
                String sku = sku_code;
                if(StringUtils.isNotBlank(skuPrefix)){
                    sku = sku_code.replaceFirst(skuPrefix, "");
                }
                //新的商品编码
                String newSkuCode = sku; //托管不需要加前缀
                TgTempSkuProperty tgTempSkuProperty = new TgTempSkuProperty();
                tgTempSkuPropertyList.add(tgTempSkuProperty);
                tgTempSkuProperty.setSku_code(newSkuCode);
                tgTempSkuProperty.setStatus("active");
                //属性
                AeopSkuPropertyList aeop_s_k_u_property_list = productSkuProperty.getAeop_s_k_u_property_list();
                if(aeop_s_k_u_property_list != null){
                    List<AeopSkuProperty> aeop_sku_property = aeop_s_k_u_property_list.getAeop_sku_property();
                    if(CollectionUtils.isNotEmpty(aeop_sku_property)){
                        List<TgAeopSkuProperty> tg_sku_property_list = new ArrayList<>();
                        tgTempSkuProperty.setSku_property_list(tg_sku_property_list);
                        for (AeopSkuProperty aeopSkuProperty : aeop_sku_property) {
                            Long skuPropertyId = aeopSkuProperty.getSku_property_id();
                            //全托管需要去除中国产地属性
                            if(skuPropertyId.longValue() != PreCheckUtils.sendAttrId){
                                TgAeopSkuProperty tgAeopSkuProperty = new TgAeopSkuProperty();
                                tg_sku_property_list.add(tgAeopSkuProperty);
                                tgAeopSkuProperty.setSku_image(aeopSkuProperty.getSku_image());
                                tgAeopSkuProperty.setProperty_value_definition_name(aeopSkuProperty.getProperty_value_definition_name());
                                tgAeopSkuProperty.setProperty_value_id(aeopSkuProperty.getProperty_value_id());
                                tgAeopSkuProperty.setSku_property_id(skuPropertyId);
                            }
                        }
                    }
                }

                //仓库
                TgWarehouse tgWarehouse = new TgWarehouse();
                tgWarehouse.setWarehouse_code("DGU002-JIT");
                tgWarehouse.setWarehouse_name("东莞JIT库存");
                Integer systemStock = SkuStockUtils.getSkuStockToEbay(sku);
                tgWarehouse.setSellable_quantity(systemStock);//需要可用-待发
                //设置长宽高 供货价
                tgTempSkuProperty.setWarehouse_list(Arrays.asList(tgWarehouse));
                Double weightKg = null; //重量kg
                BigDecimal saleCostDecimal = null; //销售成本价
                Double length = null;
                Double wide = null;
                Double height = null; //尺寸
                String configTag = "";
                SingleItemEs skuInfo = singleItemEsService.getSkuInfo(sku);
                if (skuInfo != null) {
                    List<ProductInfo> productInfos = SingleItemEsUtils.tranSingleItemEsToProductInfo(List.of(skuInfo));
                    ProductInfo productInfo = productInfos.get(0);
                    List<Integer> specialTypeList = productInfo.getSpecialTypeList();
                    if (CollectionUtils.isNotEmpty(specialTypeList)) {
                        for (Integer specialType : specialTypeList) {
                            //包含爆款标签
                            if (specialType != null && specialType == SpecialTagEnum.s_1004.code) {
                                throw new Exception(sku + " 存在特殊标签为爆款！");
                            }
                        }
                    }
                    // 重量g
                    double weightg = AliexpressWeightUtils.getMaxWeight(productInfo, null);
                    //保留两位第三位开始四舍五入
                    NumberFormat nf = NumberFormat.getNumberInstance();
                    nf.setMaximumFractionDigits(3);
                    nf.setRoundingMode(RoundingMode.DOWN);
                    // kg
                    weightKg = AliexpressWeightUtils.gramsToKilograms(weightg);
                    //长
                    length = productInfo.getLength() == null ? null : productInfo.getLength().doubleValue();
                    wide = productInfo.getWide() == null ? null : productInfo.getWide().doubleValue();
                    height = productInfo.getHeight() == null ? null : productInfo.getHeight().doubleValue();
                    saleCostDecimal = productInfo.getSaleCost();
                    configTag = productInfo.getEnTag();
                    configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(configTag);
                }else{
                    ComposeSku composeProduct = null;
                    SuiteSku suiteSku = null;
                    Boolean existSaleSuit = ProductUtils.isExistSaleSuite(sku);
                    if (existSaleSuit) {
                        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(sku));
                        String mappingSpu = MapUtils.getString(composeSkuSuitMap, sku, null);
                        if(org.apache.commons.lang.StringUtils.isNotBlank(mappingSpu)){
                            //组合产品
                            composeProduct = ProductUtils.getComposeProduct(mappingSpu);
                        }else{
                            //套装
                            suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(sku);
                        }
                    }else{
                        //组合产品
                        composeProduct = ProductUtils.getComposeProduct(sku);
                    }
                    if (composeProduct != null) {
                        Double packageWeight = AliexpressWeightUtils.getMaxWeight(composeProduct, null, true);
                        if (packageWeight != null) {
                            weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight);
                        }
                        saleCostDecimal = composeProduct.getSaleCost();
                        configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(composeProduct.getTagCode());
                    } else if (suiteSku != null) {
                        Double packageWeight = AliexpressWeightUtils.getMaxWeight(suiteSku, null, true);
                        if (packageWeight != null) {
                            weightKg = AliexpressWeightUtils.gramsToKilograms(packageWeight);
                        }
                        saleCostDecimal = suiteSku.getAllSingleCost();
                        //标签优先级
                        configTag = aliexpressTemplateAttributeHelper.matchComposeTagCode(suiteSku.getTagCode());
                    }
                }
                if(weightKg != null){
                    tgTempSkuProperty.setPackage_weight(String.valueOf(weightKg));
                }
                if(height != null){
                    tgTempSkuProperty.setPackage_height(String.valueOf(height));
                }
                if(wide != null){
                    tgTempSkuProperty.setPackage_width(String.valueOf(wide));
                }
                if(length != null){
                    tgTempSkuProperty.setPackage_length(String.valueOf(length));
                }
                if(saleCostDecimal != null){
                    Double supply_price = BigDecimal.valueOf(saleCostDecimal.doubleValue()/(1-grossMargin)).setScale(2,   BigDecimal.ROUND_HALF_UP).doubleValue();
                    tgTempSkuProperty.setSupply_price(String.valueOf(supply_price));
                }

                //特殊标签
                TgScItemInfoDto tgScItemInfoDto = new TgScItemInfoDto();

                boolean isFind = false;
                //标签匹配
                if(CollectionUtils.isNotEmpty(smtConfigHalfLables) && StringUtils.isNotBlank(configTag)){
                    //多个标签安装优先级匹配
                    List<String> strings = CommonUtils.splitList(configTag, ",");
                    for (String string : strings) {
                        List<SmtConfigHalfLable> collect = smtConfigHalfLables.stream().filter(t -> StringUtils.equalsIgnoreCase(t.getProductTag(), string)).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            tagCodeCount ++; // 匹配成功
                            isFind = true;
                            String platformTag = collect.get(0).getPlatformTag();
                            if(StringUtils.isNotBlank(platformTag)){
                                tgScItemInfoDto.setSpecial_product_type_list(Arrays.asList(platformTag));
                            }
                            break;
                        }
                    }
                }
                if(!isFind){
                    throw new Exception(sku + " 未匹配到特殊标签 "  + configTag);
                }
                tgScItemInfoDto.setOriginal_box("1");
                tgScItemInfoDto.setSc_item_code(newSkuCode);
                tgTempSkuProperty.setSc_item_info_dto(tgScItemInfoDto);
            }//end 属性
            tgTemplate.setAeopAeProductSkusJson(JSON.toJSONString(tgTempSkuPropertyList));
        }

        tgTemplate.setSubject(esProduct.getSubject());
        tgTemplate.setAeopAeProductPropertysJson(aliexpressEsExtend.getAeopAeProductPropertysJson());
        tgTemplate.setCategoryId(esProduct.getCategoryId()); //需要再品牌设置前面
        //需要替换品牌
        ResponseJson responseJson = AliexpressBrandUtils.transBrand(tgTemplate, newSaleAccountByAccountNumber);
        if(!responseJson.isSuccess()){
            throw new Exception("替换品牌出错" + responseJson.getMessage());
        }

        AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(esProduct.getCategoryId());
        if(aliexpressCategory != null){
            String fullPathCode = aliexpressCategory.getFullPathCode();
            String[] split = fullPathCode.split("_");
            tgTemplate.setRootCategory(Integer.valueOf(split[0]));
            tgTemplate.setCategoryName(aliexpressCategory.getCategoryZhName());
        }

        tgTemplate.setProductUnit(esProduct.getProductUnit());
        tgTemplate.setPackageType(esProduct.getPackageType());
        tgTemplate.setLotNum(esProduct.getLotNum());
//        tgTemplate.setCountryCode(esProduct.getCurrencyCode());
        tgTemplate.setAeopQualificationStructJson(aliexpressEsExtend.getAeopQualificationStructJson());
//        tgTemplate.setSpecialProductTypeListStr("");
        tgTemplate.setMsrEuId(null);
        tgTemplate.setSizeChartId(esProduct.getSizeChartId());

        //组装营销图
        String squareImg = aliexpressEsExtend.getSquareImg();
        String longImg = aliexpressEsExtend.getLongImg();
        if(StringUtils.isNotBlank(squareImg) || StringUtils.isNotBlank(longImg)){
            List<MarketImage> marketImageList = new ArrayList<>();
            if(StringUtils.isNotBlank(squareImg)){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("2");
                marketImage.setUrl(squareImg);
                marketImageList.add(marketImage);
            }
            if(StringUtils.isNotBlank(longImg)){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("1");
                marketImage.setUrl(longImg);
                marketImageList.add(marketImage);
            }
            tgTemplate.setMarketImagesJson(JSON.toJSONString(marketImageList));
        }

//        tgTemplate.setVideoLink("");
        tgTemplate.setDetail(esProduct.getDetail());
        tgTemplate.setMobileDetail(tgTemplate.getDetail());
        tgTemplate.setCreateBy(WebUtils.getUserName());
        tgTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
        tgTemplate.setUpdateBy(WebUtils.getUserName());
        tgTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        tgTemplate.setSizeChartIdList(esProduct.getSizechartIdList());
        return tgTemplate;
    }


    public static AliexpressTemplate compileEsProductToTemplate(EsAliexpressProductListing esProduct, AliexpressEsExtend aliexpressEsExtend) {
        Boolean wholesale = esProduct.getIsWholesale();
        Integer bulkOrder = esProduct.getBulkOrder();
        Integer bulkDiscount = esProduct.getBulkDiscount();

        Integer deliveryTime = esProduct.getDeliveryTime();
        Long freightTemplateId = esProduct.getFreightTemplateId();
        Long promiseTemplateId = esProduct.getPromiseTemplateId();
        Long groupId = esProduct.getGroupId();

        AliexpressTemplate template = new AliexpressTemplate();

        String spu = esProduct.getSpu();
        if(StringUtils.isBlank(spu)){
            spu = ProductUtils.getMainSku(esProduct.getArticleNumber());
        }

        String taxType = esProduct.getTaxType();
        template.setTaxType(taxType);
        template.setArticleNumber(spu);
        template.setIsWholesale(wholesale);
        template.setBulkOrder(bulkOrder);
        template.setBulkDiscount(bulkDiscount);
        // 库存扣减方式
        template.setReduceStrategy(esProduct.getReduceStrategy());
        template.setDeliveryTime(deliveryTime);
        template.setFreightTemplateId(freightTemplateId);
        template.setPromiseTemplateId(promiseTemplateId);
        template.setGroupId(groupId);

        template.setProductStock(esProduct.getIpmSkuStock());
        template.setProductPrice(esProduct.getProductPrice());

        template.setAliexpressAccountNumber(esProduct.getAliexpressAccountNumber());
        template.setDetail(esProduct.getDetail());

        String aeopAeProductSkusJson = aliexpressEsExtend.getAeopAeProductSkusJson();
        template.setAeopAeProductSkusJson(aeopAeProductSkusJson);
        template.setCategoryId(esProduct.getCategoryId());
        template.setInterSubjects(aliexpressEsExtend.getInterSubjects());

        List<TempSkuProperty> tempSkuProperties = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<TempSkuProperty>>() {

        });
        if(CollectionUtils.isNotEmpty(tempSkuProperties)){
            TempSkuProperty tempSkuProperty = tempSkuProperties.get(0);
            template.setProductCode(tempSkuProperty.getSku_code());
//            template.setProductStock(tempSkuProperty.getIpm_sku_stock());
//            template.setProductPrice(tempSkuProperty.getSku_price());
        }

        AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(esProduct.getCategoryId());
        if(aliexpressCategory != null){
            template.setCategoryTableId(Long.valueOf(aliexpressCategory.getId()));
            template.setCategoryName(aliexpressCategory.getCategoryZhName());
        }

        template.setSubject(esProduct.getSubject());

        String imageUrls = esProduct.getImageUrls();

        List<String> splitList = CommonUtils.splitList(imageUrls, ";");
        template.setImageUrls(StringUtils.join(splitList, ";"));
        template.setProductUnit(esProduct.getProductUnit());
        template.setPackageType(esProduct.getPackageType());
        template.setLotNum(esProduct.getLotNum());
        template.setPackageLength(esProduct.getPackageLength());
        template.setPackageWidth(esProduct.getPackageWidth());
        template.setPackageHeight(esProduct.getPackageHeight());
        template.setGrossWeight(esProduct.getGrossWeight());
        template.setIsPackSell(esProduct.getIsPackSell());
        template.setBaseUnit(esProduct.getBaseUnit());
        template.setAddUnit(esProduct.getAddUnit());
        template.setAddWeight(esProduct.getAddWeight());
        template.setWsValidNum(esProduct.getWsValidNum());
        template.setAeopAeProductPropertysJson(aliexpressEsExtend.getAeopAeProductPropertysJson());
        template.setSizeChartId(esProduct.getSizeChartId());
        template.setSizeChartIdList(esProduct.getSizechartIdList());
        template.setCurrencyCode(esProduct.getCurrencyCode());

        template.setMobileDetail(esProduct.getDetail());
        Date couponStartDate = esProduct.getCouponStartDate();
        if (couponStartDate != null) {
            template.setCouponStartDate(new Timestamp(couponStartDate.getTime()));
        }
        Date couponEndDate = esProduct.getCouponEndDate();
        if (couponEndDate != null) {
            template.setCouponEndDate(new Timestamp(couponEndDate.getTime()));
        }

        template.setAeopNationalQuoteConfiguration(aliexpressEsExtend.getAeopNationalQuoteConfiguration());
        template.setAeopAeMultimedia(aliexpressEsExtend.getAeopAeMultimedia());

        template.setDisplayImageUrl(
                CommonUtils.splitList(template.getImageUrls(), ";").get(0));

        template.setAeopQualificationStructJson(aliexpressEsExtend.getAeopQualificationStructJson());
        template.setManufactureId(esProduct.getManufactureId());
        template.setManufactureName(esProduct.getManufactureName());
        template.setHacodeJson(esProduct.getHscode());

        List<MarketImage> marketImages = new ArrayList<>();
        if(StringUtils.isNotBlank(aliexpressEsExtend.getSquareImg()) || StringUtils.isNotBlank(aliexpressEsExtend.getLongImg())){
            if(StringUtils.isNotBlank(aliexpressEsExtend.getSquareImg())){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("2");
                marketImage.setUrl(aliexpressEsExtend.getSquareImg());
                marketImages.add(marketImage);
            }

            if(StringUtils.isNotBlank(aliexpressEsExtend.getLongImg())){
                MarketImage marketImage = new MarketImage();
                marketImage.setImage_type("1");
                marketImage.setUrl(aliexpressEsExtend.getLongImg());
                marketImages.add(marketImage);
            }
        }
        if(CollectionUtils.isNotEmpty(marketImages)){
            template.setMarketImagesJson(JSONObject.toJSONString(marketImages));
        }

        return template;
    }

    public static List<String> getSaleAccountAuth(String sale){
        String compareSale = "," + sale + ",";

        List<String> accountList = new ArrayList<>();

        //账号> 销售List  这里必须是小写
        Map<String, List<String>> sellerToUserListMap = AccountUtils.getSalesman("smt");

        if(sellerToUserListMap != null){

            for (Map.Entry<String, List<String>> stringListEntry : sellerToUserListMap.entrySet()) {

                //账号
                String key = stringListEntry.getKey();

                for (String user : stringListEntry.getValue()) {
                    String[] split = user.split("-");

                    String s = "," + split[0] + ",";
                    if(StringUtils.contains(compareSale, s)){
                        accountList.add(key);
                        break;
                    }
                }

            }
        }

        return accountList;

    }

    public static List<AliexpressDataView> getWholeData(List<AliexpressDataView> listingDataViews){

        //一个店铺 多个销售需要 新增数据
        List<AliexpressDataView> createListingDataViews = new ArrayList<>();

        if(CollectionUtils.isEmpty(listingDataViews)){
            return createListingDataViews;
        }

        //账号> 销售List  这里必须是小写
        Map<String, List<String>> sellerToUserListMap = AccountUtils.getSalesman("smt");

        for (AliexpressDataView listingDataView : listingDataViews) {

            //店铺
            String accountNumber = listingDataView.getAccountNumber();
            List<String> userList = sellerToUserListMap.get(accountNumber);
            if(CollectionUtils.isNotEmpty(userList)){
                for (String user : userList) {
                    AliexpressDataView copyView = new AliexpressDataView();
                    BeanUtils.copyProperties(listingDataView, copyView);
                    copyView.setSaleUser(user);
                    if(org.apache.commons.lang.StringUtils.contains(user, "-")){
                        String[] split = user.split("-");

                        String s = split[0];

                        copyView.setSaleUser(s);
                    }

                    createListingDataViews.add(copyView);
                }
            }
        }

        return createListingDataViews;
    }


    //获取没有创建的销售
    public static Set<String> notCreateSet(Set<String> saleCreateSet){

        Set<String> notCreateSet = new HashSet<>();

        //账号> 销售List  这里必须是小写
        Map<String, List<String>> sellerToUserListMap = AccountUtils.getSalesman("smt");

        sellerToUserListMap.forEach((k,v) ->{
            v.forEach(sale ->{
                String[] split = sale.split("-");
                String s = split[0];

                if(null == saleCreateSet || saleCreateSet.isEmpty()){
                    notCreateSet.add(s);
                }else{
                    if(!saleCreateSet.contains(s)){
                        notCreateSet.add(s);
                    }
                }
            });
        });
        return notCreateSet;

    }


}
