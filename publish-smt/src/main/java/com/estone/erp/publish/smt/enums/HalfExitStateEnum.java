package com.estone.erp.publish.smt.enums;

/**
    半托管退出状态
 */
public enum HalfExitStateEnum {
    S_1(1, "待退出"),
    S_2(2, "提交成功"),
    S_3(3, "提交失败");


    private int code;

    private String name;

    HalfExitStateEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static HalfExitStateEnum build(int code) {
        HalfExitStateEnum[] values = values();
        for (HalfExitStateEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        HalfExitStateEnum[] values = values();
        for (HalfExitStateEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}
