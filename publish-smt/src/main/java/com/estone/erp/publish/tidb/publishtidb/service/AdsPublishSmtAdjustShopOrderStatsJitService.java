package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishSmtAdjustShopOrderStatsJit;

import java.util.List;

public interface AdsPublishSmtAdjustShopOrderStatsJitService extends IService<AdsPublishSmtAdjustShopOrderStatsJit> {

    /**
     * 获取可以跑的规则店铺
     * @param accountOrderTime
     * @param accountOrderTimeFrom
     * @param accountOrderTimeTo
     * @return
     */
    List<String> passAccountList(List<String> ruleAccountList, Integer accountOrderTime, Integer accountOrderTimeFrom, Integer accountOrderTimeTo);

}
