package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.smt.enums.StypeEnum;
import com.estone.erp.publish.smt.helper.LayeredAssessmentDetailHelper;
import com.estone.erp.publish.smt.model.LayeredAssessmentDetailVo;
import com.estone.erp.publish.smt.model.SellerLayeredAssessmentDetailResVo;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSellerLayeredAssessmentDetail;
import com.estone.erp.publish.tidb.publishtidb.service.ISmtSellerLayeredAssessmentDetailService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * smt卖家分层详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@RestController
@RequestMapping("/smtSellerLayeredAssessmentDetail")
public class SmtSellerLayeredAssessmentDetailController {

    @Resource
    private ISmtSellerLayeredAssessmentDetailService smtSellerLayeredAssessmentDetailService;
    @Resource
    private LayeredAssessmentDetailHelper layeredAssessmentDetailHelper;

    @PostMapping(value = "/search/{accountNumer}")
    public ApiResult<SellerLayeredAssessmentDetailResVo> search(@PathVariable(value = "accountNumer", required = true) String accountNumer) {
        LambdaQueryWrapper<SmtSellerLayeredAssessmentDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmtSellerLayeredAssessmentDetail::getAccountNumber, accountNumer);
        Map<Integer, SmtSellerLayeredAssessmentDetail> map = smtSellerLayeredAssessmentDetailService.getBaseMapper().selectList(queryWrapper).stream()
                .collect(Collectors.toMap(SmtSellerLayeredAssessmentDetail::getStype, Function.identity()));

        if (map.isEmpty()) {
            return ApiResult.newSuccess(null);
        }

        LayeredAssessmentDetailVo forecastLayeredDetailVo = Optional.ofNullable(map.get(StypeEnum.FORECAST_LAYER.getCode()))
                .map(LayeredAssessmentDetailHelper::convertDetailVo).orElse(null);
        LayeredAssessmentDetailVo currentLayeredDetailVo = Optional.ofNullable(map.get(StypeEnum.CURRENT_LAYER.getCode()))
                .map(LayeredAssessmentDetailHelper::convertDetailVo).orElse(null);
        SellerLayeredAssessmentDetailResVo showVo = SellerLayeredAssessmentDetailResVo.builder()
                .forecastLayeredDetailVo(forecastLayeredDetailVo)
                .currentLayeredDetailVo(currentLayeredDetailVo)
                .build();
        return ApiResult.newSuccess(showVo);
    }
}
