package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressExcelLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressExcelLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountsLike(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("( ");
            for (int i = 0; i < values.size(); i++) {
                if(i==0){
                    str.append("account like '%," + values.get(i) + ",%' ");
                }else{
                    str.append("or account like '%," + values.get(i) + ",%'");
                }
            }
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andDownloadConutIsNull() {
            addCriterion("download_conut is null");
            return (Criteria) this;
        }

        public Criteria andDownloadConutIsNotNull() {
            addCriterion("download_conut is not null");
            return (Criteria) this;
        }

        public Criteria andDownloadConutEqualTo(Integer value) {
            addCriterion("download_conut =", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutNotEqualTo(Integer value) {
            addCriterion("download_conut <>", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutGreaterThan(Integer value) {
            addCriterion("download_conut >", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutGreaterThanOrEqualTo(Integer value) {
            addCriterion("download_conut >=", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutLessThan(Integer value) {
            addCriterion("download_conut <", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutLessThanOrEqualTo(Integer value) {
            addCriterion("download_conut <=", value, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutIn(List<Integer> values) {
            addCriterion("download_conut in", values, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutNotIn(List<Integer> values) {
            addCriterion("download_conut not in", values, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutBetween(Integer value1, Integer value2) {
            addCriterion("download_conut between", value1, value2, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andDownloadConutNotBetween(Integer value1, Integer value2) {
            addCriterion("download_conut not between", value1, value2, "downloadConut");
            return (Criteria) this;
        }

        public Criteria andQueueUpIsNull() {
            addCriterion("queue_up is null");
            return (Criteria) this;
        }

        public Criteria andQueueUpIsNotNull() {
            addCriterion("queue_up is not null");
            return (Criteria) this;
        }

        public Criteria andQueueUpEqualTo(Integer value) {
            addCriterion("queue_up =", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpNotEqualTo(Integer value) {
            addCriterion("queue_up <>", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpGreaterThan(Integer value) {
            addCriterion("queue_up >", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpGreaterThanOrEqualTo(Integer value) {
            addCriterion("queue_up >=", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpLessThan(Integer value) {
            addCriterion("queue_up <", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpLessThanOrEqualTo(Integer value) {
            addCriterion("queue_up <=", value, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpIn(List<Integer> values) {
            addCriterion("queue_up in", values, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpNotIn(List<Integer> values) {
            addCriterion("queue_up not in", values, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpBetween(Integer value1, Integer value2) {
            addCriterion("queue_up between", value1, value2, "queueUp");
            return (Criteria) this;
        }

        public Criteria andQueueUpNotBetween(Integer value1, Integer value2) {
            addCriterion("queue_up not between", value1, value2, "queueUp");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIsNull() {
            addCriterion("version_number is null");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIsNotNull() {
            addCriterion("version_number is not null");
            return (Criteria) this;
        }

        public Criteria andVersionNumberEqualTo(Integer value) {
            addCriterion("version_number =", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotEqualTo(Integer value) {
            addCriterion("version_number <>", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberGreaterThan(Integer value) {
            addCriterion("version_number >", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("version_number >=", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberLessThan(Integer value) {
            addCriterion("version_number <", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberLessThanOrEqualTo(Integer value) {
            addCriterion("version_number <=", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIn(List<Integer> values) {
            addCriterion("version_number in", values, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotIn(List<Integer> values) {
            addCriterion("version_number not in", values, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberBetween(Integer value1, Integer value2) {
            addCriterion("version_number between", value1, value2, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("version_number not between", value1, value2, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlIsNull() {
            addCriterion("excel_upload_url is null");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlIsNotNull() {
            addCriterion("excel_upload_url is not null");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlEqualTo(String value) {
            addCriterion("excel_upload_url =", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlNotEqualTo(String value) {
            addCriterion("excel_upload_url <>", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlGreaterThan(String value) {
            addCriterion("excel_upload_url >", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlGreaterThanOrEqualTo(String value) {
            addCriterion("excel_upload_url >=", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlLessThan(String value) {
            addCriterion("excel_upload_url <", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlLessThanOrEqualTo(String value) {
            addCriterion("excel_upload_url <=", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlLike(String value) {
            addCriterion("excel_upload_url like", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlNotLike(String value) {
            addCriterion("excel_upload_url not like", value, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlIn(List<String> values) {
            addCriterion("excel_upload_url in", values, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlNotIn(List<String> values) {
            addCriterion("excel_upload_url not in", values, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlBetween(String value1, String value2) {
            addCriterion("excel_upload_url between", value1, value2, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelUploadUrlNotBetween(String value1, String value2) {
            addCriterion("excel_upload_url not between", value1, value2, "excelUploadUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlIsNull() {
            addCriterion("excel_down_url is null");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlIsNotNull() {
            addCriterion("excel_down_url is not null");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlEqualTo(String value) {
            addCriterion("excel_down_url =", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlNotEqualTo(String value) {
            addCriterion("excel_down_url <>", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlGreaterThan(String value) {
            addCriterion("excel_down_url >", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlGreaterThanOrEqualTo(String value) {
            addCriterion("excel_down_url >=", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlLessThan(String value) {
            addCriterion("excel_down_url <", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlLessThanOrEqualTo(String value) {
            addCriterion("excel_down_url <=", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlLike(String value) {
            addCriterion("excel_down_url like", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlNotLike(String value) {
            addCriterion("excel_down_url not like", value, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlIn(List<String> values) {
            addCriterion("excel_down_url in", values, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlNotIn(List<String> values) {
            addCriterion("excel_down_url not in", values, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlBetween(String value1, String value2) {
            addCriterion("excel_down_url between", value1, value2, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andExcelDownUrlNotBetween(String value1, String value2) {
            addCriterion("excel_down_url not between", value1, value2, "excelDownUrl");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeIsNull() {
            addCriterion("queue_ex_time is null");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeIsNotNull() {
            addCriterion("queue_ex_time is not null");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeEqualTo(Timestamp value) {
            addCriterion("queue_ex_time =", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeNotEqualTo(Timestamp value) {
            addCriterion("queue_ex_time <>", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeGreaterThan(Timestamp value) {
            addCriterion("queue_ex_time >", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("queue_ex_time >=", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeLessThan(Timestamp value) {
            addCriterion("queue_ex_time <", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("queue_ex_time <=", value, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeIn(List<Timestamp> values) {
            addCriterion("queue_ex_time in", values, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeNotIn(List<Timestamp> values) {
            addCriterion("queue_ex_time not in", values, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("queue_ex_time between", value1, value2, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andQueueExTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("queue_ex_time not between", value1, value2, "queueExTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNull() {
            addCriterion("complete_time is null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNotNull() {
            addCriterion("complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeEqualTo(Timestamp value) {
            addCriterion("complete_time =", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotEqualTo(Timestamp value) {
            addCriterion("complete_time <>", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThan(Timestamp value) {
            addCriterion("complete_time >", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("complete_time >=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThan(Timestamp value) {
            addCriterion("complete_time <", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("complete_time <=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIn(List<Timestamp> values) {
            addCriterion("complete_time in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotIn(List<Timestamp> values) {
            addCriterion("complete_time not in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("complete_time between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("complete_time not between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIsNull() {
            addCriterion("error_msg is null");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIsNotNull() {
            addCriterion("error_msg is not null");
            return (Criteria) this;
        }

        public Criteria andErrorMsgEqualTo(String value) {
            addCriterion("error_msg =", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotEqualTo(String value) {
            addCriterion("error_msg <>", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgGreaterThan(String value) {
            addCriterion("error_msg >", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgGreaterThanOrEqualTo(String value) {
            addCriterion("error_msg >=", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLessThan(String value) {
            addCriterion("error_msg <", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLessThanOrEqualTo(String value) {
            addCriterion("error_msg <=", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLike(String value) {
            addCriterion("error_msg like", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotLike(String value) {
            addCriterion("error_msg not like", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIn(List<String> values) {
            addCriterion("error_msg in", values, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotIn(List<String> values) {
            addCriterion("error_msg not in", values, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgBetween(String value1, String value2) {
            addCriterion("error_msg between", value1, value2, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotBetween(String value1, String value2) {
            addCriterion("error_msg not between", value1, value2, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andExcelSendDataEqualTo(String value) {
            addCriterion("excel_send_data =", value, "excelSendData");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}