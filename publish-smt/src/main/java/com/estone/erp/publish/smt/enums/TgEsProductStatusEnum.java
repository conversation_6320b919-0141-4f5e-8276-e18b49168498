package com.estone.erp.publish.smt.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 产品本身的状态
 */
public enum TgEsProductStatusEnum {

    onSelling("onSelling", "正在销售"),
    PENDING_LAUNCH("PENDING_LAUNCH", "待上架"),
    auditing("auditing", "审核中"),
    editingRequired("editingRequired", "审核不通过"),
    offline("offline", "已下架"),
    ;

    private String code;

    private String name;

    private TgEsProductStatusEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (TgEsProductStatusEnum value : TgEsProductStatusEnum.values()) {
            if(StringUtils.equalsIgnoreCase(value.getCode(), code)){
                return value.getName();
            }
        }
        return null;
    }
}
