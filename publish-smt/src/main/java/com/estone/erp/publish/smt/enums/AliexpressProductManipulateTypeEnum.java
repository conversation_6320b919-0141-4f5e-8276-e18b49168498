package com.estone.erp.publish.smt.enums;

/**
 * @Description: 产品来源
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/4/23 10:08
 * @Version: 1.0.0
 */
public enum AliexpressProductManipulateTypeEnum {
    OFFLINE(0),

    ONLINE(1);

    private int type;

    AliexpressProductManipulateTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }}
