package com.estone.erp.publish.smt.enums;

/**
 * @Description: 产品来源
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/4/23 10:08
 * @Version: 1.0.0
 */
public enum AliexpressProductSourceEnum {
    //产品数据来源 10：listing同步 20：平台新建
    LISTING_SYNC(10, "listing"),

    CREATE_SYS(20, "平台创建");

    private int code;

    private String name;

    private AliexpressProductSourceEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressProductSourceEnum build(int code) {
        AliexpressProductSourceEnum[] values = values();

        for (AliexpressProductSourceEnum type : values) {
            if (type.code ==code) {
                return type;
            }
        }

        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressProductSourceEnum[] values = values();
        for (AliexpressProductSourceEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
