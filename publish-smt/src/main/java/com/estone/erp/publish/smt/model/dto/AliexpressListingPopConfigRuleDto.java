package com.estone.erp.publish.smt.model.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AliexpressListingPopConfigRuleDto  implements Serializable {
    private String categoryIds;

    /**
     * 系统库存区间（可用+在途+待上架-待发）
     */
    private Integer stockStart;
    private Integer stockEnd;


    /**
     * 全平台指定周期（7/14/30/90）查询总销量，配合 salesNumList 使用
     */
    private Integer plaformSalesNumCycle;
    /**
     * 总销量区间 维度 1/7/14/30/60/90 区间值
     */
    private Integer plaformSalesNumStart;
    private Integer plaformSalesNumEnd;


    /**
     * smt指定周期（7/14/30/90）查询总销量，配合 salesNumList 使用
     */
    private Integer smtSalesNumCycle;
    /**
     * 总销量区间 维度 1/7/14/30/60/90 区间值
     */
    private Integer smtSalesNumStart;
    private Integer smtSalesNumEnd;



    /**
     * 录入时间区间
     */
    private List<AliexpressInputTimeDto> inputTime;



    //销售成本价区间
    private Double fromPrice;

    private Double toPrice;


    //产品标签
    private List<String> productLabel;


    //单品状态
    private List<String> singleItemStatus;


    //重量区间 净重+包材+包装材料 + 3g
    private Double fromWeight;

    private Double toWeight;

    //季节性商品
    private List<String> seasonProduct;

    //禁售信息
    private List<AliexpressProhibitionInfoDto> excludeProhibitionInfo;

    /**
     * 总额度使用率
     */
    private Double totalQuotaUsageRateFrom;
    private Double totalQuotaUsageRateTo;

    /**
     * 新发品额度使用率
     */
    private Double newProductQuotaUsageRateFrom;
    private Double newProductQuotaUsageRateTo;
}
