package com.estone.erp.publish.smt.util;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeBean;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeJson;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeValueJson;
import com.estone.erp.publish.smt.bean.ProductPropertys;
import com.estone.erp.publish.smt.bean.brand.BrandDO;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.feginService.bean.SaleAttrValue;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class AliexpressBrandUtils {

    private static List<String> carTypeNameList = CommonUtils.splitList("Country,Make,Model,Year,Trim,Engine", ",");

    private static List<String> provinceList = CommonUtils.splitList("广东,浙江,江苏,福建,上海,山东,江西,湖南,湖北,河南", ",");

    private static AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

    private static AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
    private static AliexpressAutoTemplateService aliexpressAutoTemplateService = SpringUtils.getBean(AliexpressAutoTemplateService.class);
    private static AliexpressBrandSetService aliexpressBrandSetService = SpringUtils.getBean(AliexpressBrandSetService.class);
    private static SmtCategoryConfigService smtCategoryConfigService = SpringUtils.getBean(SmtCategoryConfigService.class);
    //判断是否含有化学属性值
    private static String chemistryValue = "\"attr_name_id\":*********,";

    /**
     * 需要默认的属性值
     */
    private final static Map<Long, Long> attrMap = new HashMap<>(){
        private static final long serialVersionUID = 1L;
        {
            super.put(348L, 200003454L);
            super.put(200000941L, 350216L);
//            super.put(200000631L, 349906L);
        }
    };

    /**
     * http://172.16.2.103:8080/browse/ES-11408
     * @param spu
     * @param account
     * @param categoryId
     * @return
     */
    public static String initAttr(String spu, String account, Integer categoryId){
        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(categoryId);
        if(aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()){
            log.info("分类不存在或者过期: " + categoryId);
            return null;
        }
        String aeopAeProductPropertysJson = "";
        AliexpressAutoTemplateExample example = new AliexpressAutoTemplateExample();
        example.createCriteria().andArticleNumberEqualTo(spu).andCategoryIdEqualTo(categoryId).andApplyStateEqualTo(ApplyStatusEnum.YES.getIntCode());
        List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService.selectByExample(example);
        if(CollectionUtils.isNotEmpty(aliexpressAutoTemplates)){
            aeopAeProductPropertysJson = aliexpressAutoTemplates.get(0).getAeopAeProductPropertysJson();
        }else{
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("attr_name_id", 2);
            jsonObject.put("attr_value_id", -1);
            jsonArray.add(jsonObject); //品牌占位默认属性
            aeopAeProductPropertysJson = jsonArray.toJSONString();
        }
        String defaultAttr = defaultAttr(aeopAeProductPropertysJson, aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory, spu);
        SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        AliexpressTemplate template = new AliexpressTemplate();
        transBrand(categoryId, defaultAttr, template::setAeopAeProductPropertysJson, newAccount);
        return template.getAeopAeProductPropertysJson();
    }


    /**
     * 全托管默认属性
     * @param paramObj
     * @return
     */
    public static String tgDefaultAttr(JSONObject paramObj){
        if(paramObj == null){
            return null;
        }
        JSONObject product_info_dto = paramObj.getJSONObject("product_info_dto");
        Integer categoryId = product_info_dto.getInteger("category_id");

        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(categoryId);
        if(aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()){
            log.info("分类不存在或者过期: " + categoryId);
            return null;
        }
        //属性
        JSONArray product_property_list = paramObj.getJSONArray("product_property_list");
        return defaultAttr(product_property_list.toJSONString(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);

    }


    /**
     * ES-10558
     * 如果属性是空的，则取分类属性配置的默认值
     * 产地          "id":219,       "id":**********,   测试环境 admin  范本 807864   32701      孕婴童>儿童配件>帽子
     * 高关注化学物质  "id":*********, "id":23399591357   测试环境 admin  范本 807864   32701      孕婴童>儿童配件>帽子
     * 认证          "id":348,       "id":200003454,    测试环境 admin  范本 801631   100006342  家装（硬装）>卫浴设施>浴室镜
     * 是否有包装     "id":200000941, "id":350216        线上环境 admin  范本 3277     70806      电脑和办公>电脑外设>电脑线缆及接插件>电脑硬件线缆&接头
     * 是否可定制     "id":200000631, "id":349906,       线上环境 admin  范本 364543   200004329  新奇特及特殊用途服装>扮演服及配件（不要发布情趣制服）>动漫配饰>动漫徽章
     * @param aeopAeProductPropertysJson
     * @param attributes
     * @param provinceAttributes
     * @param aliexpressCategory
     * @return
     */
    //默认属性 修复admin范本使用 http://172.16.2.103:8080/browse/ES-9916
    public static String defaultAttr(String aeopAeProductPropertysJson, String attributes, String provinceAttributes, AliexpressCategory aliexpressCategory, String... spu){

        //产地和化学属性
        aeopAeProductPropertysJson = addOrigin(aeopAeProductPropertysJson, attributes, provinceAttributes, aliexpressCategory, spu);

        // 默认规则走完，再走分类配置的
        if (aliexpressCategory != null && aliexpressCategory.getCategoryId() != null) {
            aeopAeProductPropertysJson = addOtherCategoryAttrConfig(aeopAeProductPropertysJson, aliexpressCategory);
        }

        JSONArray jsonArray = JSONObject.parseArray(aeopAeProductPropertysJson);
        for (Map.Entry<Long, Long> longLongEntry : attrMap.entrySet()) {
            Long key = longLongEntry.getKey();
            Long value = longLongEntry.getValue();
            String dbAttr = "\"attr_name_id\":" + key + ",";
            String platAttrKey = "\"id\":" + key + ",";
            String platAttrValue = "\"id\":" + value + ",";
            //如果本身属性没有，接口有就需要默认
            if(StringUtils.contains(attributes,platAttrKey) && StringUtils.contains(attributes, platAttrValue) && !StringUtils.contains(aeopAeProductPropertysJson, dbAttr)){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("attr_name_id", key);
                jsonObject.put("attr_value_id", value);
                jsonArray.add(jsonObject);
            }
        }
        return jsonArray.toJSONString();
    }

    /**
     * 追加改分类下面不存在的属性
     * @param aeopAeProductPropertysJson
     * @param aliexpressCategory
     * @return
     */
    public static String addOtherCategoryAttrConfig(String aeopAeProductPropertysJson, AliexpressCategory aliexpressCategory) {
        if (aliexpressCategory == null || StringUtils.isBlank(aeopAeProductPropertysJson)) {
            return aeopAeProductPropertysJson;
        }
        JSONArray currentArray = JSONObject.parseArray(aeopAeProductPropertysJson);

        Set<Long> existAttrId = new HashSet<>();
        for (int i = 0; i < currentArray.size(); i++) {
            JSONObject jsonObject = currentArray.getJSONObject(i);
            Long attrNameId = jsonObject.getLong("attr_name_id");
            if (attrNameId == null) {
                continue;
            }
            existAttrId.add(attrNameId);
        }

        String childAttributesJson = aliexpressCategory.getChildAttributesJson();
        JSONObject parseObject = JSONObject.parseObject(childAttributesJson);
        if (parseObject == null) {
            return aeopAeProductPropertysJson;
        }
        JSONArray categoryAttrArray = parseObject.getJSONArray("attributes");
        Set<Long> notCategoryAttrId = new HashSet<>();
        for (int i = 0; i < categoryAttrArray.size(); i++) {
            JSONObject jsonObject = categoryAttrArray.getJSONObject(i);
            Long attrNameId = jsonObject.getLong("id");
            Boolean sku = jsonObject.getBoolean("sku");
            if (attrNameId != null && !existAttrId.contains(attrNameId) && BooleanUtils.isFalse(sku)) {
                notCategoryAttrId.add(attrNameId);
            }
        }
        if (notCategoryAttrId.isEmpty()) {
            return aeopAeProductPropertysJson;
        }

        //获取分类配置 需要判断是否有子属性
        List<SmtCategoryConfig> attrByCategory = smtCategoryConfigService.getAttrByCategory(aliexpressCategory.getCategoryId(), true);
        if (attrByCategory.isEmpty()) {
            return aeopAeProductPropertysJson;
        }
        // 只有父属性是没有添加的情况下蔡添加子属性
        Map<Long, SmtCategoryConfig> collect = attrByCategory.stream().collect(Collectors.toMap(SmtCategoryConfig::getAttrNameId, a -> a, (a, b) -> b));
        for (Long attrNameId : notCategoryAttrId) {
            SmtCategoryConfig smtCategoryConfig = collect.get(attrNameId);
            if (smtCategoryConfig == null) {
                continue;
            }
            List<ProductPropertys> productPropertys = JSON.parseArray(smtCategoryConfig.getAttrJson(), ProductPropertys.class);
            for (ProductPropertys productProperty : productPropertys) {
                Object json = JSONObject.toJSON(productProperty);
                currentArray.add((json));
            }
            // 判断是否有子属性信息
            String childrenAttrJson = smtCategoryConfig.getChildrenAttrJson();
            if (StringUtils.isNotBlank(childrenAttrJson)) {
                List<ProductPropertys> productPropertys1 = JSON.parseArray(childrenAttrJson, ProductPropertys.class);
                for (ProductPropertys productProperty : productPropertys1) {
                    Object json = JSONObject.toJSON(productProperty);
                    currentArray.add((json));
                }
            }
        }
        return currentArray.toJSONString();
    }

    //attr_name_id 219 产地 attr_value_id ********** 中国 attr_name_id 266081643 中国省份
    public static String addOrigin(String aeopAeProductPropertysJson, String attributes, String provinceAttributes, AliexpressCategory aliexpressCategory, String... spuStrs){
        AttributeBean attributeBean = JSON.parseObject(attributes, new TypeReference<AttributeBean>() {
        });

        //需要判断是否默认化学属性
        aeopAeProductPropertysJson = addChemistry(aeopAeProductPropertysJson, aliexpressCategory);

        //中国产地属性
        SaleAttrValue saleAttrValue = new SaleAttrValue();

        //中国省份
        SaleAttrValue provinceValue = new SaleAttrValue();

        //标识找到
        boolean hit = false;

        String spu = "";
        if (spuStrs != null && spuStrs.length > 0) {
            spu = spuStrs[0];
        }
        List<AttributeJson> attributesList = attributeBean.getAttributes();
        //说明需要默认型号
        if(StringUtils.isNotBlank(spu)){
            for (AttributeJson attributeJson : attributesList) {
                //父id
                Long id = attributeJson.getId();
                //有型号
                if(id == 3L){
                    JSONArray parseArray = JSONObject.parseArray(aeopAeProductPropertysJson);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("attr_name_id", 3L);
                    jsonObject.put("attr_value", spu);
                    parseArray.add(jsonObject);
                    aeopAeProductPropertysJson = parseArray.toJSONString();
                    break;
                }
            }
        }
        for (AttributeJson attributeJson : attributesList) {
            //父id
            Long id = attributeJson.getId();

            JSONObject names = attributeJson.getNames();

            String zh = names.getString("zh");
            String en = names.getString("en");

            //存在产地信息
            if(id == 219L){
                saleAttrValue.setAttrId(id);
                saleAttrValue.setAttrZh(zh);
                saleAttrValue.setAttrEn(en);
            }

            List<AttributeValueJson> values = attributeJson.getValues();

            if(CollectionUtils.isNotEmpty(values)){
                for (AttributeValueJson value : values) {
                    JSONObject valueNames = value.getNames();

                    if(null ==valueNames){
                        continue;
                    }

                    Long valueId = value.getId();

                    String valueZh = valueNames.getString("zh");
                    String valueEn = valueNames.getString("en");

                    if(StringUtils.equalsIgnoreCase(valueEn, "CN(Origin)") || StringUtils.equalsIgnoreCase(valueEn, "Mainland China")){
                        //是否有省份
                        Boolean hasSubAttr = value.getHasSubAttr();
                        if(hasSubAttr != null && hasSubAttr){
                            if(StringUtils.isNotBlank(provinceAttributes)){
                                AttributeBean provinceAttributeBean = JSON.parseObject(provinceAttributes, new TypeReference<AttributeBean>() {
                                });

                                //根据优先级 获取省份值
                                List<AttributeJson> provinceAttributesList = provinceAttributeBean.getAttributes();
                                for (AttributeJson json : provinceAttributesList) {
                                    Long id1 = json.getId();
                                    JSONObject names1 = json.getNames();
                                    String zh1 = names1.getString("zh");
                                    String en1 = names1.getString("en");

                                    SaleAttrValue provinceAttrValue = new SaleAttrValue();

                                    provinceAttrValue.setAttrId(id1);
                                    provinceAttrValue.setAttrZh(zh1);
                                    provinceAttrValue.setAttrEn(en1);

                                    List<AttributeValueJson> values1 = json.getValues();
                                    if(CollectionUtils.isNotEmpty(values1)){
                                        Map<String, SaleAttrValue> provinceMap = new HashMap<>();
                                        for (AttributeValueJson attributeValueJson : values1) {
                                            SaleAttrValue provinceAttrValue1 = new SaleAttrValue();
                                            BeanUtils.copyProperties(provinceAttrValue, provinceAttrValue1);
                                            Long valueId1 = attributeValueJson.getId();
                                            JSONObject valueNames1 = attributeValueJson.getNames();
                                            if(valueNames1 == null || valueNames1.isEmpty()){
                                                continue;
                                            }
                                            String valueZh1 = valueNames1.getString("zh");
                                            String valueEn1 = valueNames1.getString("en");

                                            provinceAttrValue1.setValueId(valueId1);
                                            provinceAttrValue1.setValueZh(valueZh1);
                                            provinceAttrValue1.setValueEn(valueEn1);
                                            provinceMap.put(valueZh1, provinceAttrValue1);

                                            if(provinceValue.getAttrId() == null){
                                                provinceValue = provinceAttrValue1; //先默认一个值
                                            }
                                        }

                                        for (String s : provinceList) {
                                            SaleAttrValue provinceAttrValue1 = provinceMap.get(s);
                                            if(provinceAttrValue1 != null){
                                                provinceValue = provinceAttrValue1;
                                                break;
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        saleAttrValue.setValueId(valueId);
                        saleAttrValue.setValueZh(valueZh);
                        saleAttrValue.setValueEn(valueEn);
                        hit = true;
                        break;
                    }
                }
            }

            if(hit){
                break;
            }
        }

        //有产地属性
        if(saleAttrValue.getAttrId() != null && saleAttrValue.getAttrId().longValue() != 0L && StringUtils.isNotBlank(saleAttrValue.getValueEn())){
            JSONArray parseArray = JSONObject.parseArray(aeopAeProductPropertysJson);

            //中国产地json
            JSONObject originJsonObject = new JSONObject();
            originJsonObject.put("attr_name_id", saleAttrValue.getAttrId());
            originJsonObject.put("attr_name", saleAttrValue.getAttrEn());
            originJsonObject.put("attr_value_id", saleAttrValue.getValueId());
            originJsonObject.put("attr_value", saleAttrValue.getValueEn());


            //中国产地省份json(不一定有)
            JSONObject provinceJsonObject = new JSONObject();
            if(provinceValue.getAttrId() != null){
                provinceJsonObject.put("attr_name_id", provinceValue.getAttrId());
                provinceJsonObject.put("attr_name", provinceValue.getAttrEn());
                provinceJsonObject.put("attr_value_id", provinceValue.getValueId());
                provinceJsonObject.put("attr_value", provinceValue.getValueEn());
            }

            //用来判断原属性是否有这些信息
            boolean origin = false; //产地
            boolean cnOrigin = false;//判断是否是中国产地
            boolean cnProvince = false; //判断是否有中国省份

            for (int i = 0; i < parseArray.size(); i++) {
                JSONObject jsonObject = parseArray.getJSONObject(i);
                long attr_name_id = jsonObject.getLongValue("attr_name_id");
                if(saleAttrValue.getAttrId().longValue() == attr_name_id){
                    origin = true;
                    //确定是中国产地
                    long attr_value_id = jsonObject.getLongValue("attr_value_id");
                    if(attr_value_id == **********L){
                        cnOrigin = true;
                    }
                }
                if(attr_name_id == 266081643L){
                    cnProvince = true;
                }
            }

            JSONArray newParseArray = new JSONArray();
            if(!origin){ //没有产地属性，就需要加上 中国产地 和中国产地省份(如果不为空)
                for (int i = 0; i < parseArray.size(); i++) {
                    newParseArray.add(parseArray.get(i));
                }
                newParseArray.add(originJsonObject);
                if(provinceJsonObject.containsKey("attr_name_id")){
                    newParseArray.add(provinceJsonObject);
                }
                return newParseArray.toJSONString();

            }else if(cnOrigin && !cnProvince){ //有产地，并且是有中国产地，没有中国产地省份，需要加上中国省份(如果不为空)
                for (int i = 0; i < parseArray.size(); i++) {
                    newParseArray.add(parseArray.get(i));
                }
                if(provinceJsonObject.containsKey("attr_name_id")){
                    newParseArray.add(provinceJsonObject);
                }
                return newParseArray.toJSONString();
            }
        }
        return aeopAeProductPropertysJson;
    }

    /**
     * 添加化学属性
     * @param aeopAeProductPropertysJson
     * @param aliexpressCategory
     * @return
     */
    public static String addChemistry(String aeopAeProductPropertysJson, AliexpressCategory aliexpressCategory){
        if(StringUtils.isBlank(aeopAeProductPropertysJson) || StringUtils.contains(aeopAeProductPropertysJson, chemistryValue)
                || aliexpressCategory == null || aliexpressCategory.getChemistry() == null || !aliexpressCategory.getChemistry()){
            return aeopAeProductPropertysJson;
        }
        JSONArray jsonArray = JSONObject.parseArray(aeopAeProductPropertysJson);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("attr_name_id", *********L);
        jsonObject.put("attr_value_id", 23399591357L);
        jsonArray.add(jsonObject);
        return jsonArray.toJSONString();
    }


    public static String addOrigin(String aeopAeProductPropertysJson, SaleAccountAndBusinessResponse newAccount, String categoryId){
        CategoryOpenCall call = new CategoryOpenCall();
        String categoryAttributes = call.getCategoryAttributes(newAccount,categoryId);
        String attributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);

        if(StringUtils.isBlank(attributes)){
            return aeopAeProductPropertysJson;
        }
        AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(Integer.valueOf(categoryId));
        //需要判断是否默认化学属性
        aeopAeProductPropertysJson = addChemistry(aeopAeProductPropertysJson, aliexpressCategory);

        String provinceAttributes = "";
        if(aliexpressCategory != null){
            Boolean origin = aliexpressCategory.getOrigin();
            if(origin == null || !origin){ //没有产地属性
                return aeopAeProductPropertysJson;
            }
            Boolean province = aliexpressCategory.getProvince();
            if(province != null && province){
                provinceAttributes = aliexpressCategory.getProvinceAttributes();
                if(StringUtils.isBlank(provinceAttributes)){
                    //接口重新获取一次
                    String provinceCategoryAttributes = call.getCategoryAttributes(newAccount,categoryId, "219=**********");
                    provinceAttributes = AliexpressCategoryUtils.parseCategoryAttributes(provinceCategoryAttributes);
                }
            }
        }else{
            String provinceCategoryAttributes = call.getCategoryAttributes(newAccount,categoryId, "219=**********");
            provinceAttributes = AliexpressCategoryUtils.parseCategoryAttributes(provinceCategoryAttributes);
        }
        return addOrigin(aeopAeProductPropertysJson, attributes, provinceAttributes, aliexpressCategory);
    }

    //如果存在车载属性，去除掉
    public static String reMoveCarType(String aeopAeProductPropertysJson){
        JSONArray parseArray = JSONObject.parseArray(aeopAeProductPropertysJson);

        JSONArray newParseArray = new JSONArray();

        for (int i = 0; i < parseArray.size(); i++) {
            JSONObject jsonObject = parseArray.getJSONObject(i);

            String attr_name = jsonObject.getString("attr_name");

            //去除
            if(!(StringUtils.isNotBlank(attr_name) && carTypeNameList.contains(attr_name))){
                newParseArray.add(jsonObject);
            }
        }
        return newParseArray.toJSONString();
    }

    public static void replaceModel(AliexpressTemplate template, SaleAccountAndBusinessResponse newAccount) {
        String s = reGenerateModel(template.getArticleNumber(), template.getAeopAeProductPropertysJson(), newAccount);
        template.setAeopAeProductPropertysJson(s);
    }
    public static void replaceModel(AliexpressTgTemplate template, SaleAccountAndBusinessResponse newAccount) {
        String s = reGenerateModel(template.getArticleNumber(), template.getAeopAeProductPropertysJson(), newAccount);
        template.setAeopAeProductPropertysJson(s);
    }
    /**
     * 重新生成 型号
     * @param articleNumber
     * @param aeopAeProductPropertysJson
     * @param newAccount
     * @return
     */
    public static String reGenerateModel(String articleNumber, String aeopAeProductPropertysJson, SaleAccountAndBusinessResponse newAccount) {
        // 替换成新的JSON
        JSONArray newJSONArray = new JSONArray();
        JSONArray parseArray = JSONObject.parseArray(aeopAeProductPropertysJson);
        for (int i = 0; i < parseArray.size(); i++) {

            JSONObject jsonObject = parseArray.getJSONObject(i);

            Integer attr_name_id = jsonObject.getInteger("attr_name_id");
            // Integer attr_value_id = jsonObject.getInteger("attr_value_id");

            // id= 3 为型号，替换成账号前缀 +货号
            if (attr_name_id != null && attr_name_id == 3) {
                JSONObject newJSONObject = new JSONObject();
                newJSONObject.put("attr_name_id", 3);

                if (StringUtils.isNotBlank(newAccount.getSellerSkuPrefix())) {
                    articleNumber = newAccount.getSellerSkuPrefix() + articleNumber;
                }

                newJSONObject.put("attr_value", articleNumber);

                newJSONArray.add(newJSONObject);
            } else {
                // 保存原值
                newJSONArray.add(jsonObject);
            }
        }
        return newJSONArray.toJSONString();
    }

    /**
     * ES-10559
     * 刊登产品时，品牌属性按照原有逻辑进行默认
     * 根据产品的类目，优先查找店铺配置
     * 第一优先级:优先的品牌，
     * 第二优先级:产品对应的品牌名称进行刊登，
     * 第三优先级:下拉第一个品牌
     * 第四优先级:取none.
     * 第三步其实就已经结束了
     * @param template
     * @param newAccount
     * @return
     */
    public static ResponseJson transBrand(AliexpressTemplate template, SaleAccountAndBusinessResponse newAccount) {
        return transBrand(template.getCategoryId(), template.getAeopAeProductPropertysJson(), template::setAeopAeProductPropertysJson, newAccount);
    }
    public static ResponseJson transBrand(AliexpressTgTemplate template, SaleAccountAndBusinessResponse newAccount) {
        return transBrand(template.getCategoryId(), template.getAeopAeProductPropertysJson(), template::setAeopAeProductPropertysJson, newAccount);
    }
    private static ResponseJson transBrand(Integer categoryId, String aeopAeProductPropertysJson,
                                           Consumer<String> setAeopAeProductPropertysJson,
                                           SaleAccountAndBusinessResponse newAccount) {
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        try {
            /**
             * [{"names":{"en":"NoEnName_Null","zh":"代佳龙"},"id":*********,"value_tags":{}},
             * {"names":{"en":"NoEnName_Null","zh":"花影"},"id":*********,"value_tags":{}},
             * {"names":{"en":"OPEN-SMART","zh":"OPEN-SMART"},"id":*********,"value_tags":{}},
             * {"names":{"en":"peacefair","zh":"peacefair"},"id":*********,"value_tags":{}},
             * {"names":{"en":"WEICHEN","zh":"威臣"},"id":*********,"value_tags":{}}
             * {"names": {"en": "None","zh": "无品牌"},"id":9 ,"value_tags":
             * {}} ]
             */
            JSONArray aliexpressBrands = getAliexpressBrands(newAccount, String.valueOf(categoryId));

            // 跟卖账号同类目没有可用的 品牌
            String string = "账号:" + newAccount.getAccountNumber() + "类目id:" + categoryId + "跟卖失败：调用平台接口异常，请重试，或者店铺中无品牌 如果重试多次不成功，请检查分类或者账号token！</br>";

            if (aliexpressBrands == null || aliexpressBrands.isEmpty()) {
                rsp.setMessage(string);
                return rsp;
            }

            // 存储有品牌的值
            Map<Long, String> brandIdMap = new HashMap<>();
            List<Long> bandIdList = new ArrayList<>();
            // 寻找可用品牌，优先 无品牌 需要返回id值
            for (int i = 0; i < aliexpressBrands.size(); i++) {
                JSONObject jsonObject = aliexpressBrands.getJSONObject(i);
                JSONObject namesJson = jsonObject.getJSONObject("names");
                String zh = namesJson.getString("zh");
                Long brandId = jsonObject.getLongValue("id");
                brandIdMap.put(brandId, zh);
                bandIdList.add(brandId);
            }

            if (brandIdMap.isEmpty()) {
                rsp.setMessage(string);
                return rsp;
            }

            // 被跟卖的产品属性 ====》 {"attr_name_id":2,"attr_value_id":201606071} 品牌id
            // = 2 , 需要替换 跟卖账号类目 自身的品牌属性

            // 替换成新的JSON
            JSONArray newjsonArray = new JSONArray();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            JSONArray parseArray = JSONObject.parseArray(aeopAeProductPropertysJson);
            for (int i = 0; i < parseArray.size(); i++) {
                JSONObject jsonObject = parseArray.getJSONObject(i);
                Long attr_name_id = jsonObject.getLong("attr_name_id");
                Long tempalte_attr_value_id = jsonObject.getLong("attr_value_id");

                if (attr_name_id != null && attr_name_id == 2) {
                    JSONObject newjsonobject = new JSONObject();
                    newjsonobject.put("attr_name_id", 2);
                    AliexpressBrandSetExample brandSetExample = new AliexpressBrandSetExample();
                    brandSetExample.createCriteria().andAccountNameEqualTo(newAccount.getAccountNumber()).andCategoryIdEqualTo(categoryId)
                            .andStatusEqualTo("已生效").andBrandDeadlineTimeGreaterThanOrEqualTo(Timestamp.valueOf(sdf.format(new Date())));
                    List<AliexpressBrandSet> aliexpressBrandSets = aliexpressBrandSetService.selectByExample(brandSetExample);
                    if (CollectionUtils.isNotEmpty(aliexpressBrandSets)) {
                        //优先设置的首选品牌
                        List<AliexpressBrandSet> firstList = aliexpressBrandSets.stream().filter(AliexpressBrandSet::getIsFirst).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(firstList)) {
                            if (firstList.size() > 1) {
                                Collections.shuffle(firstList);
                            }
                            for (AliexpressBrandSet aliexpressBrandSet : firstList) {
                                Long aeBrandId = aliexpressBrandSet.getAeBrandId();
                                String s = brandIdMap.get(aeBrandId);
                                if (StringUtils.isNotBlank(s)) {
                                    newjsonobject.put("attr_value_id", aeBrandId);
                                    break;
                                }
                            }
                        }
                    }
                    //判断是否还存在值
                    Object attr_value_id1 = newjsonobject.get("attr_value_id");
                    // 如果第一步取不到优先的品牌，就先尝试用模板自带的品牌名称进行怕匹对
                    if (attr_value_id1 == null) {
                        String zh = brandIdMap.get(tempalte_attr_value_id);
                        if (StringUtils.isNotEmpty(zh)) {
                            // 品牌一致
                            newjsonobject.put("attr_value_id", tempalte_attr_value_id);
                            // 如果还娶不到，就取下拉列表的第一个品牌
                        } else if (MapUtils.isNotEmpty(brandIdMap)) {
                            Long listBoxFirstBandId = bandIdList.get(0);
                            newjsonobject.put("attr_value_id", listBoxFirstBandId);
                        }
                    }
                    newjsonArray.add(newjsonobject);
                } else {
                    // 保存原值
                    newjsonArray.add(jsonObject);
                }
            }
            setAeopAeProductPropertysJson.accept(newjsonArray.toJSONString());
            rsp.setStatus(StatusCode.SUCCESS);
        } catch (Exception e) {
            rsp.setMessage(e.getMessage() + "</br>");
        }
        return rsp;
    }

    /**
     * 获取速卖通品牌集合
     */
    public static JSONArray getAliexpressBrands(SaleAccountAndBusinessResponse account, String categoryId) {

        try {
            CategoryOpenCall call = new CategoryOpenCall();
            String categoryAttributesStr = call.getCategoryAttributes(account, categoryId);

            if (StringUtils.isNotBlank(categoryAttributesStr)) {
                JSONObject jsonObject = JSONObject.parseObject(categoryAttributesStr);
                    if (jsonObject.containsKey(
                            "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response")) {
                        JSONObject rspObj = jsonObject.getJSONObject(
                                "aliexpress_category_redefining_getchildattributesresultbypostcateidandpath_response");
                        if (rspObj.containsKey("result")) {
                            JSONObject resultObj = rspObj.getJSONObject("result");
                            if (resultObj.containsKey("attributes")) {
                                JSONObject attributesObj = resultObj.getJSONObject("attributes");
                                if (attributesObj.containsKey("aeop_attribute_dto")) {
                                    JSONArray attributeDtoArr = attributesObj.getJSONArray("aeop_attribute_dto");
                                    if (attributeDtoArr != null) {
                                        int size = attributeDtoArr.size();
                                        for (int i = 0; i < size; i++) {
                                            JSONObject attrDto = attributeDtoArr.getJSONObject(i);

                                            if (attrDto.containsKey("id")) {
                                                Integer id = attrDto.getInteger("id");

                                                // id = 2 的 为品牌
                                                if (id != null && id == 2) {

                                                    JSONArray cateAttrValArr = new JSONArray();

                                                    if (attrDto.containsKey("values")) {
                                                        JSONObject valuesObj = attrDto.getJSONObject("values");

                                                        if (valuesObj.containsKey("aeop_attr_value_dto")) {
                                                            JSONArray valueDtoArr = valuesObj
                                                                    .getJSONArray("aeop_attr_value_dto");
                                                            if (valueDtoArr != null) {
                                                                int size2 = valueDtoArr.size();
                                                                for (int j = 0; j < size2; j++) {
                                                                    JSONObject valueDto = valueDtoArr.getJSONObject(j);
                                                                    JSONObject cateAttrValObj = new JSONObject();
                                                                    if (valueDto.containsKey("id")) {
                                                                        cateAttrValObj.put("id",
                                                                                valueDto.getLongValue("id"));
                                                                    }
                                                                    if (valueDto.containsKey("names")) {
                                                                        JSONObject namesObj = valueDto
                                                                                .getJSONObject("names");
                                                                        cateAttrValObj.put("names", namesObj);
                                                                    }
                                                                    if (valueDto.containsKey("value_tags")) {
                                                                        JSONObject valueTags = valueDto
                                                                                .getJSONObject("value_tags");
                                                                        cateAttrValObj.put("value_tags", valueTags);
                                                                    }

                                                                    cateAttrValArr.add(cateAttrValObj);
                                                                }
                                                            }
                                                        }
                                                    }

                                                    return cateAttrValArr;

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
            }

        }
        catch (Exception e) {

        }
        return null;
    }

    public static List<BrandDO> passBrandAttr(String brandAttr) {
        if (StringUtils.isBlank(brandAttr)) {
            return Collections.emptyList();
        }

        try {
            return JSON.parseArray(brandAttr, BrandDO.class);
        } catch (Exception e) {
            log.error("brandAttr解析JSON失败,{}", brandAttr, e);
            return Collections.emptyList();
        }
    }

    public static BrandDO getBrandDO(String brandAttr, Long brandId) {
        List<BrandDO> brandDOS = passBrandAttr(brandAttr);
        return brandDOS.stream().filter(brandDO -> brandDO.getId().equals(brandId)).findFirst().orElseGet(()->null);
    }

    public static void main(String[] args) {

//        String[] aa = new String[] { "1233 456", "/shopeeTemplate/6EE500639/6EE500639-4 (1).jpg" };
//        List<String> asList = Arrays.asList(aa);
//
//        System.out.println(JSON.toJSONString(asList));

//        Pattern p = Pattern.compile("<div.*(class|style)='test'.*</div>");
//        //测试用的baihtml代码
//        String str = "<html><body>aa<div id='11'>bb</div> <div class='test'>bb111</div></body></html>";
//        Matcher m = p.matcher(str);
//        //去除标签
//        String result = m.replaceAll("");
//        System.out.println(result);

        List<String> list = new ArrayList<String>();
        list.add("a");
        list.add("a");
        list.add("b");
        list.add("a");
        list.add("a");

        System.out.print("remove前集合数据：");
        for (int i = 0; i < list.size(); i++) {
            System.out.print(list.get(i)+"，");
        }

        //删除集合中值为“a”的数据
        for (int i = 0; i < list.size(); i++) {
            if ("a".equals(list.get(i))) {
                list.remove(i);
                i--;
            }
        }

        System.out.println("");
        System.out.print("remove后集合数据：");
        for (int i = 0; i < list.size(); i++) {
            System.out.print(list.get(i)+"，");
        }


    }

}
