package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> aliexpress_holiday_update_log
 * 2020-01-10 10:29:16
 */
public class AliHolidayUpdateLogCriteria extends AliHolidayUpdateLog {
    private static final long serialVersionUID = 1L;

    public AliHolidayUpdateLogExample getExample() {
        AliHolidayUpdateLogExample example = new AliHolidayUpdateLogExample();
        AliHolidayUpdateLogExample.Criteria criteria = example.createCriteria();
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (this.getFromPrice() != null) {
            criteria.andFromPriceEqualTo(this.getFromPrice());
        }
        if (this.getToPrice() != null) {
            criteria.andToPriceEqualTo(this.getToPrice());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        return example;
    }
}