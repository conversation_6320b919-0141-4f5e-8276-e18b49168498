package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AdjustPriceStatusEnum {

    FAIL(0, "失败"),
    EXECUTE_ING(1, "处理中"),
    SUCCESS(2, "成功");;

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static String convert(Integer value) {
        for (AdjustPriceStatusEnum status : AdjustPriceStatusEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

}
