package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> aliexpress_msg_log
 * 2023-06-30 09:48:14
 */
public class AliexpressMsgLogCriteria extends AliexpressMsgLog {
    private static final long serialVersionUID = 1L;

    public AliexpressMsgLogExample getExample() {
        AliexpressMsgLogExample example = new AliexpressMsgLogExample();
        AliexpressMsgLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getMessage())) {
            criteria.andMessageEqualTo(this.getMessage());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        return example;
    }
}