package com.estone.erp.publish.smt.model.dto;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.smt.enums.PriorityEnum;
import lombok.Data;

/**
 * 资质信息
 */
@Data
public class AliexpressSpuCategoryQualificationVo {

    // ----- 下面是产品信息，每次都要更新
    /**
     * 是否已经有资质信息了
     */
    private Boolean hasValue;
    /**
     * 资质信息 图片地址
     */
    private String value;
    /**
     * spu信息
     */
    private String spu;

    /**
     * 资质信息
     */
    private AliexpressCategoryQualificationVo categoryQualificationVo;

    public JSONObject toProductCategoryQualification(PriorityEnum priorityEnum, Integer isCe) {
        JSONObject productCategoryQualification = new JSONObject();
        productCategoryQualification.put("spu", this.spu);
        productCategoryQualification.put("platformQualification", this.categoryQualificationVo.getLabel());
        productCategoryQualification.put("qualificationStatement", this.categoryQualificationVo.getTips());
        productCategoryQualification.put("priority", priorityEnum.getCode());
        productCategoryQualification.put("type", this.categoryQualificationVo.getType());
        productCategoryQualification.put("needCeCertified", isCe);
        return productCategoryQualification;
    }
}
