package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliAccountPvLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ali_account_pv_log.id
     */
    private Integer id;

    /**
     * 店铺 database column ali_account_pv_log.account_number
     */
    private String accountNumber;

    /**
     * 更新状态 0:未同步 1:同步中 2:同步完成 database column ali_account_pv_log.status
     */
    private Integer status;

    /**
     * 数量 database column ali_account_pv_log.total
     */
    private Integer total;

    /**
     * 创建时间 database column ali_account_pv_log.created_time
     */
    private Timestamp createdTime;

    /**
     * 更新时间 database column ali_account_pv_log.updated_time
     */
    private Timestamp updatedTime;
}