package com.estone.erp.publish.smt.util;

import com.alibaba.excel.util.BooleanUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/1116:37
 */
public class AliexpressAccountUtils {

    /**
     * 判断是否为海外仓
     * @param saleAccountByAccountNumber 账号
     * @return boolean
     */
    public static boolean isOverseasBusiness(SaleAccountAndBusinessResponse saleAccountByAccountNumber){
        if (saleAccountByAccountNumber == null) {
            return false;
        }
        Boolean overseasBusiness = saleAccountByAccountNumber.getOverseaWarehouse();
        return BooleanUtils.isTrue(overseasBusiness);
    }


}
