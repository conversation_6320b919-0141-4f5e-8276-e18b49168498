package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class LazadaInfringingWordsStorge implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * exel中的侵权词汇
     */
    private String word;

    /**
     * 分类
     */
    private String category;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}