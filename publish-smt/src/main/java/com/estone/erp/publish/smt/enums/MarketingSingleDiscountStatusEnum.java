package com.estone.erp.publish.smt.enums;

import lombok.Getter;


public enum MarketingSingleDiscountStatusEnum {
    /**
     * 未开始=1，进行中=2，已过期=3
     */
    NEXT(1, "未生效"),
    ONGOING(2, "生效中"),

    EXPIRED(3, "已结束"),
    PAUSED(4, "已暂停")
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    MarketingSingleDiscountStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String convert(Integer value) {
        if (value == null) {
            return null;
        }
        for (MarketingSingleDiscountStatusEnum item : MarketingSingleDiscountStatusEnum.values()) {
            if (item.getCode().equals(value)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
