package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressHalfTgPreItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_half_tg_pre_item.id
     */
    private Long id;

    /**
     * 商品主图 database column aliexpress_half_tg_pre_item.product_image
     */
    private String productImage;

    /**
     * 店铺 database column aliexpress_half_tg_pre_item.account
     */
    private String account;

    /**
     * 产品ID database column aliexpress_half_tg_pre_item.product_id
     */
    private Long productId;

    /**
     * 产品的状态 database column aliexpress_half_tg_pre_item.product_status
     */
    private String productStatus;

    /**
     * sku对应的单品货号 database column aliexpress_half_tg_pre_item.article_number
     */
    private String articleNumber;

    /**
     * sku商品编码 database column aliexpress_half_tg_pre_item.sku_code
     */
    private String skuCode;

    /**
     * sku_id database column aliexpress_half_tg_pre_item.sku_id
     */
    private String skuId;

    /**
     * 最大SKU价格 database column aliexpress_half_tg_pre_item.max_sku_price
     */
    private Double maxSkuPrice;

    /**
     * 最小SKU价格 database column aliexpress_half_tg_pre_item.min_sku_price
     */
    private Double minSkuPrice;

    /**
     * 零售价 database column aliexpress_half_tg_pre_item.productPrice
     */
    private Double productprice;

    /**
     * 标题 database column aliexpress_half_tg_pre_item.title
     */
    private String title;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。 database column aliexpress_half_tg_pre_item.category_id
     */
    private Integer categoryId;

    /**
     * 类目全路径 database column aliexpress_half_tg_pre_item.full_path_code
     */
    private String fullPathCode;

    /**
     * 货币码 database column aliexpress_half_tg_pre_item.currency_code
     */
    private String currencyCode;

    /**
     * 产品系统.类目ID database column aliexpress_half_tg_item.pro_category_id
     */
    private Integer proCategoryId;

    /**
     * 类目id路径 database column aliexpress_half_tg_item.pro_category_id_path
     */
    private String proCategoryIdPath;

    /**
     * 类目中文路径 database column aliexpress_half_tg_item.pro_category_cn_name
     */
    private String proCategoryCnName;

    /**
     * 销售成本价 database column aliexpress_half_tg_pre_item.sale_cost
     */
    private Double saleCost;

    /**
     * 单品状态 database column aliexpress_half_tg_pre_item.sku_status
     */
    private String skuStatus;

    /**
     * 产品标签 database column aliexpress_half_tg_pre_item.sku_tag_code
     */
    private String skuTagCode;

    /**
     * 特殊标签 database column aliexpress_half_tg_pre_item.special_goods_code
     */
    private String specialGoodsCode;

    /**
     * 禁售平台 database column aliexpress_half_tg_pre_item.forbid_channel
     */
    private String forbidChannel;

    /**
     * 禁售类型 database column aliexpress_half_tg_pre_item.infringement_type_name
     */
    private String infringementTypeName;

    /**
     * 禁售原因 database column aliexpress_half_tg_pre_item.infringement_obj
     */
    private String infringementObj;

    /**
     * 禁售站点 database column aliexpress_half_tg_pre_item.prohibition_sites
     */
    private String prohibitionSites;

    /**
     * 产品系统促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是） database column aliexpress_half_tg_pre_item.promotion
     */
    private Integer promotion;

    /**
     * 新品状态 database column aliexpress_half_tg_pre_item.new_state
     */
    private Boolean newState;

    /**
     * sku数据来源 database column aliexpress_half_tg_pre_item.sku_data_source
     */
    private Integer skuDataSource;

    /**
     * 组合状态 database column aliexpress_half_tg_pre_item.compose_status
     */
    private Integer composeStatus;

    /**
     * 平台修改时间 database column aliexpress_half_tg_pre_item.modified_time
     */
    private Timestamp modifiedTime;

    /**
     * 平台创建时间 database column aliexpress_half_tg_pre_item.create_time
     */
    private Timestamp createTime;

    /**
     * 最新同步时间 database column aliexpress_half_tg_pre_item.last_synch_time
     */
    private Timestamp lastSynchTime;

    /**
     * 备注 database column aliexpress_half_tg_pre_item.remark
     */
    private String remark;

    /**
     * 自定义item状态
     */
    private Integer itemStatus;


    /**
     * 系统库存（可用+在途+待上架-待发）废弃不维护了
     */
    private Integer systemStock;

    /**
     * 可用库存
     */
    private Integer usableStock;

    /**
     * smt中转仓库存
     */
    private Integer smtTransferStock;

    /**
     * 可用 + smt中转 - 待发
     */
    private Integer systemUsableTransferStock;

    /**
     * 更新系统库存日期,用于排查刷新情况
     */
    private Timestamp updateSystemStockDate;


    // 销售
    private String salemanager;

    // 销售组长
    private String salemanagerLeader;

    // 销售主管
    private String salesSupervisorName;
}