package com.estone.erp.publish.smt.enums;

public enum CarTypeEnum {

    Country(1, "Country"),

    Make(2, "Make"),

    Model(3, "Model"),

    Year(4, "Year"),

    Trim(5, "Trim"),

    Enigne(6, "Enigne");

    private int code;

    private String name;

    private CarTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static CarTypeEnum build(int code) {
        CarTypeEnum[] values = values();
        for (CarTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        CarTypeEnum[] values = values();
        for (CarTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
