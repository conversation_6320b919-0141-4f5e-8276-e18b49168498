package com.estone.erp.publish.smt.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Data
public class SingleDiscountAddOrUpdateDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 单品折扣id,编辑时必传
     */
    private Long localSingleDiscountId;


    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 折扣名称
     */
    private String name;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTime;


    /**
     * 营销分组数组
     */
    @Valid
    private List<SingleDiscountGroupDTO> groupList;

    /**
     * 需要删除的商品id数组
     */
    private List<NeedDelProDTO> needDelList;

    /**
     * 勾选需要新增或者更新的商品数组
     */
    @Valid
    private List<SingleDiscountProDTO> needUpdateList;

    /**
     *
     */

    private BatchUpdatedConfigParam batchUpdatedConfigParam;

    /**
     *优先级, 1营销分组 ，2 批量设置
     */
    private Integer priority;



    /**
     * 商品分页id，非商品id
     */
    private List<Long> idList;

}