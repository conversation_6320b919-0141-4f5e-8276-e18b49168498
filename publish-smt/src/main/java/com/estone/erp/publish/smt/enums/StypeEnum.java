package com.estone.erp.publish.smt.enums;

public enum StypeEnum {
    FORECAST_LAYER("预估分层", 1),
    CURRENT_LAYER("当前分层", 2);

    private Integer code;

    private String name;

    private StypeEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static String getNameByCode(int code) {
        StypeEnum[] values = values();
        for (StypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }


}
