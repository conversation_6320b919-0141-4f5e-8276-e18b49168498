package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @version: 1.0
 * @author: chenxianda
 * @create: 2024-07-10 18:40
 **/
@Data
public class IdListDTO {
    /**
     * 本地单品折扣id
     */
    @NotNull(message = "单品折扣id不能为空")
    private Long localSingleDiscountId;

    /**
     * 商品分页id，非商品id
     */
    private List<Long> idList;
}
