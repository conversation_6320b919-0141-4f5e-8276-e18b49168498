package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import lombok.Data;

@Data
public class AliexpressFreightTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_freight_template.id
     */
    private Integer id;

    /**
     * 运费模版id database column aliexpress_freight_template.template_id
     */
    private Long templateId;

    /**
     * 模版名字 database column aliexpress_freight_template.template_name
     */
    private String templateName;

    /**
     * 是否默认 database column aliexpress_freight_template.is_default
     */
    private Boolean isDefault;

    /**
     * 速卖通帐号 database column aliexpress_freight_template.account_number
     */
    private String accountNumber;
}