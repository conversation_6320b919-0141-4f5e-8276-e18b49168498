package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.smt.enums.DataViewStatusEnum;
import com.estone.erp.publish.smt.enums.DataViewTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> aliexpress_data_view
 * 2020-04-20 10:56:09
 */
@Data
public class AliexpressDataViewCriteria extends AliexpressDataView {
    private static final long serialVersionUID = 1L;

    private List<Integer> idList;

    private List<String> saleList;

    private List<String> accountList;

    /**
     * 页面选择销售 的时候 查询销售管理的账号
     */
    private List<String> saleAccountAuth;

    /**
     * 当前登录人的账号权限
     */
    private List<String> loginSaleAccountAuth;

    /**
     * 登录人
     */
    private String loginSale;


    private Integer numFrom;
    private Integer numTo;

    private Integer unStandarNum;

    private Date dateFrom;
    private Date dateTo;

    private String dateType;
    private String orderBy;

    private int limit;
    private int offset;

    public String dateTypeFormat(Date creationDate) {
        if ("week".equals(dateType)) {
            return weekFormat(dateFrom.getTime(), creationDate.getTime());
        }
        if ("month".equals(dateType)) {
            return monthFormat(creationDate.getTime());
        }
        return DateUtils.format(new Date(creationDate.getTime()), "yyyy-MM-dd");
    }

    private String monthFormat(long date) {
        String pattern = "yyyy-MM";
        return DateUtils.format(new Date(date), pattern);
    }

    private String weekFormat(long start, long date) {
        String pattern = "yyyy-MM-dd";
        long weekLength = 7 * 24 * 60 * 60 * 1000;
        long endWeekTime = start + weekLength;
        if (date < endWeekTime) {
            String startWeek = DateUtils.format(new Date(start), pattern);
            endWeekTime = endWeekTime - 24 * 60 * 60 * 1000;
            String endWeek = DateUtils.format(new Date(endWeekTime), pattern);
            return startWeek + "~" + endWeek;
        }
        return weekFormat(endWeekTime, date);
    }

    public AliexpressDataViewExample getExample() {
        AliexpressDataViewExample example = new AliexpressDataViewExample();
        AliexpressDataViewExample.Criteria criteria = example.createCriteria();

        if(CollectionUtils.isNotEmpty(this.getLoginSaleAccountAuth())){
            example.setLoginSaleAccountAuth(this.getLoginSaleAccountAuth());
        }

        //权限控制
        if(StringUtils.isNotBlank(this.getLoginSale())){
            example.setLoginSale(this.getLoginSale());
        }

        if (CollectionUtils.isNotEmpty(this.getIdList())) {
            criteria.andIdIn(this.getIdList());
        }
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            this.setAccountList(Arrays.asList(StringUtils.split(this.getAccountNumber(), ",")));
            criteria.andAccountNumberIn(Arrays.asList(StringUtils.split(this.getAccountNumber(), ",")));
        }
        if(this.getNumFrom() != null) {
            if(this.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
                criteria.andTemplateNumGreaterThanOrEqualTo(this.getNumFrom());
            }else {
                criteria.andListingNumGreaterThanOrEqualTo(this.getNumFrom());
            }
        }
        if(this.getNumTo() != null) {
            if(this.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
                criteria.andTemplateNumLessThanOrEqualTo(this.getNumFrom());
            }else {
                criteria.andListingNumLessThanOrEqualTo(this.getNumFrom());
            }
        }

        //不达标数量
        if(this.getUnStandarNum() != null){
            if(this.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
                criteria.andTemplateNumLessThan(this.getUnStandarNum());
            }else {
                criteria.andListingNumLessThan(this.getUnStandarNum());
            }
        }

        if(this.getDateFrom() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(this.getDateFrom());
        }
        if(this.getDateTo() != null) {
            criteria.andCreationDateLessThanOrEqualTo(this.getDateTo());
        }

        if (StringUtils.isNotBlank(this.getSaleUser())) {
            this.setSaleList(Arrays.asList(StringUtils.split(this.getSaleUser(), ",")));
            criteria.andSaleUserIn(Arrays.asList(StringUtils.split(this.getSaleUser(), ",")));
        }
        if (this.getType() != null) {
            criteria.andTypeEqualTo(this.getType());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (this.getTemplateNum() != null) {
            criteria.andTemplateNumEqualTo(this.getTemplateNum());
        }
        if (this.getListingNum() != null) {
            criteria.andListingNumEqualTo(this.getListingNum());
        }
        if (StringUtils.isNotBlank(this.getCountNum1())) {
            criteria.andCountNum1EqualTo(this.getCountNum1());
        }
        if (StringUtils.isNotBlank(this.getCountNum2())) {
            criteria.andCountNum2EqualTo(this.getCountNum2());
        }
        if (StringUtils.isNotBlank(this.getCountNum3())) {
            criteria.andCountNum3EqualTo(this.getCountNum3());
        }
        if (StringUtils.isNotBlank(this.getCountNum4())) {
            criteria.andCountNum4EqualTo(this.getCountNum4());
        }
        if (StringUtils.isNotBlank(this.getCountNum5())) {
            criteria.andCountNum5EqualTo(this.getCountNum5());
        }
        if (StringUtils.isNotBlank(this.getCountNum6())) {
            criteria.andCountNum6EqualTo(this.getCountNum6());
        }
        if (this.getCreationDate() != null) {
            criteria.andCreationDateEqualTo(this.getCreationDate());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }

        if (StringUtils.isNotBlank(this.getOrderBy())) {
            example.setOrderByClause(this.getOrderBy());
        }else{

            if(this.getType() != null){
                //默认排序
                if(this.getType().equals(DataViewTypeEnum.TEMPLATE.getId())) {
                    example.setOrderByClause("template_num desc, creation_date desc, id");
                }else{
                    example.setOrderByClause("listing_num desc, creation_date desc, id");
                }
            }
        }
        return example;
    }
}