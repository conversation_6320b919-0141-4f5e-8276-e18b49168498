package com.estone.erp.publish.smt.enums;

public enum ListingConfigStatusEnum {
    ON(1, "启用"),
    OFF(0, "禁用"),
    ;
    private int code;

    private String name;

    private ListingConfigStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ListingConfigStatusEnum build(int code) {
        ListingConfigStatusEnum[] values = values();
        for (ListingConfigStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        ListingConfigStatusEnum[] values = values();
        for (ListingConfigStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
