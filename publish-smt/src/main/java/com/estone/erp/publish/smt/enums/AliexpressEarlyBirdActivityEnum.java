package com.estone.erp.publish.smt.enums;

/**
 * @Description: 早鸟活动活动状态枚举
 */
public enum AliexpressEarlyBirdActivityEnum {
    WAIT_JOIN("1", "可加入计划"),
    PLAN_PROGRESS("2", "计划进行中"),
    PLAN_END("3", "托管已结束");

    private String code;
    private String name;

    private AliexpressEarlyBirdActivityEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static String getNameByCode(String code) {
        for (AliexpressEarlyBirdActivityEnum type : values()) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}