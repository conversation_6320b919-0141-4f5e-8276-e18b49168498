package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressExcelLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_excel_log.id
     */
    private Integer id;

    /**
     * 多个店铺 database column aliexpress_excel_log.account
     */
    private String account;

    /**
     * 类型 1.下载 2.32国改价模板 3.32国改价结果校验 4.excle改价 5.32国excel改价 6.excel改重量 database column aliexpress_excel_log.type
     */
    private Integer type;

    /**
     * 上传和下载数量 database column aliexpress_excel_log.download_conut
     */
    private Integer downloadConut;

    /**
     * 当前排队 database column aliexpress_excel_log.queue_up
     */
    private Integer queueUp;

    /**
     * 版本号 database column aliexpress_excel_log.version_number
     */
    private Integer versionNumber;

    /**
     * 状态 database column aliexpress_excel_log.status
     */
    private Integer status;

    /**
     * 上传url(excel操作) database column aliexpress_excel_log.excel_upload_url
     */
    private String excelUploadUrl;

    /**
     * excel下载url database column aliexpress_excel_log.excel_down_url
     */
    private String excelDownUrl;

    /**
     * 上传和下载时间 database column aliexpress_excel_log.create_time
     */
    private Timestamp createTime;

    /**
     * 下载或上传人 database column aliexpress_excel_log.create_by
     */
    private String createBy;

    /**
     * 下载或上传人中文名 database column aliexpress_excel_log.create_name
     */
    private String createName;

    /**
     * 队列执行时间 database column aliexpress_excel_log.queue_ex_time
     */
    private Timestamp queueExTime;

    /**
     * 完成时间 database column aliexpress_excel_log.complete_time
     */
    private Timestamp completeTime;

    /**
     * 异常 database column aliexpress_excel_log.error_msg
     */
    private String errorMsg;

    /**
     * 队列发送的请求数据
     */
    private String excelSendData;
}