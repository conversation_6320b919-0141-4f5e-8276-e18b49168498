package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品系统刊登的spu类型
 * <AUTHOR>
 * @date 2023年7月18日14:47:50
 */
@Getter
@AllArgsConstructor
public enum TemplateSpuPublishTypeEnum {

    SINGLE(1, "管理单品"),
    Compose(2, "组合产品"),
    Suite(3, "套装"),
    GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE(4, "冠通-大健云仓");

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }
}
