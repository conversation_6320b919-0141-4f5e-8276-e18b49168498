package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class AliexpressListingPopConfigTimeDto {
    /**
     * 每天最大刊登数量 database column aliexpress_config.max_publish_num
     */
    private Integer maxPublishNum;

    /**
     * 刊登频率 day.每天 week.每周 month.每月
     */
    private String publishRate;

    /**
     * 数值 1 - 31
     */
    private List<Integer> timingDayList;


    private String ruleStartTime;
    private String ruleEndTime;
    /**
     * 执行时间
     */
    private String startTime;
}
