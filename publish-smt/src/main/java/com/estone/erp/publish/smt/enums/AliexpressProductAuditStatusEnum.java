package com.estone.erp.publish.smt.enums;

/**
 * @Description: 产品源数据审核状态枚举
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/4/23 10:06
 * @Version: 1.0.0
 */
public enum AliexpressProductAuditStatusEnum {
    //产品源数据审核状态 10：待审核 20：审核通过 30 审核拒绝
    WAIT_AUDIT(10, "待审核"),

    PASS_AUDIT(20, "审核通过"),

    REJECT_AUDIT(30, "审核拒绝");

    private int code;

    private String name;

    private AliexpressProductAuditStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static AliexpressProductAuditStatusEnum build(int code) {
        AliexpressProductAuditStatusEnum[] values = values();

        for (AliexpressProductAuditStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }

        return null;
    }

    public static String getNameByCode(int code) {
        AliexpressProductAuditStatusEnum[] values = values();
        for (AliexpressProductAuditStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }
}
