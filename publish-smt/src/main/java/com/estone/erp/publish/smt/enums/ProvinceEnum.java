package com.estone.erp.publish.smt.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public enum ProvinceEnum {
    Henan(100015193L, "Henan", "河南"),
    Anhui(100015188L, "Anhui", "安徽"),
    Sichuan(100015208L, "Sichuan", "四川"),
    Guizhou(100015191L, "Guizhou", "贵州"),
    Shanghai(100015210L, "Shanghai", "上海"),
    Inner_Mongoria(19713392638L, "Inner Mongoria", "内蒙古"),
    Guangdong(100015190L, "Guangdong", "广东"),
    Tibet(100015205L, "Tibet", "西藏"),
    Beijing(100015218L, "Beijing", "北京"),
    Hainan(100015214L, "Hainan", "海南"),
    Hunan(100015201L, "Hunan", "湖南"),
    Shaanxi(100015212L, "Shaanxi", "陕西"),
    Hong_Kong(100014911L, "Hong Kong", "香港"),
    Yunnan(100015204L, "Yunnan", "云南"),
    Shandong(100015211L, "Shandong", "山东"),
    Zhejiang(100015203L, "Zhejiang", "浙江"),
    Tianjin(18392598L, "<PERSON>ianjin", "天津"),
    Liaoning(100015197L, "Liaoning", "辽宁"),
    Fujian(100015189L, "Fujian", "福建"),
    Taiwan(100015022L, "Taiwan", "台湾"),
    Hubei(100015202L, "Hubei", "湖北"),
    <PERSON>njiang(100015206L, "<PERSON>njiang", "新疆"),
    Guangxi(100015215L, "Guangxi", "广西"),
    Jilin(100015198L, "Jilin", "吉林"),
    Hebei(100015192L, "Hebei", "河北"),
    Qinghai(100015194L, "Qinghai", "青海"),
    Heilongjiang(100015213L, "Heilongjiang", "黑龙江"),
    Macao(22071587L, "Macao", "澳门"),
    Shanxi(100015209L, "Shanxi", "山西"),
    Gansu(100015216L, "Gansu", "甘肃"),
    Chongqing(100015217L, "Chongqing", "重庆"),
    Jiangsu(100015200L, "Jiangsu", "江苏"),
    Jiangxi(100015199L, "Jiangxi", "江西"),
    Ningxia(100015195L, "Ningxia", "宁夏"),
    ;

    private Long id;

    private String code;

    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private ProvinceEnum(Long id, String code, String name) {
        this.id = id;
        this.code = code;
        this.name = name;
    }

    public static ProvinceEnum build(String code) {
        ProvinceEnum[] values = values();
        for (ProvinceEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ProvinceEnum[] values = values();
        for (ProvinceEnum type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static String getNameById(Long id) {
        ProvinceEnum[] values = values();
        for (ProvinceEnum type : values) {
            if (type.id.longValue() == id) {
                return type.getName();
            }
        }
        return null;
    }

    public static List<Long> getIdList() {
        List<Long> idList = new ArrayList<>();
        ProvinceEnum[] values = values();
        for (ProvinceEnum type : values) {
            idList.add(type.getId());
        }
        return idList;
    }
}
