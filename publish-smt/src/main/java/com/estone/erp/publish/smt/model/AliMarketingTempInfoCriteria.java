package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> ali_marketing_temp_info
 * 2020-10-16 15:58:55
 */
public class AliMarketingTempInfoCriteria extends AliMarketingTempInfo {
    private static final long serialVersionUID = 1L;

    public AliMarketingTempInfoExample getExample() {
        AliMarketingTempInfoExample example = new AliMarketingTempInfoExample();
        AliMarketingTempInfoExample.Criteria criteria = example.createCriteria();
        if (this.getTempId() != null) {
            criteria.andTempIdEqualTo(this.getTempId());
        }
        if (this.getProductId() != null) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getTitle())) {
            criteria.andTitleEqualTo(this.getTitle());
        }
        if (StringUtils.isNotBlank(this.getImgUrl())) {
            criteria.andImgUrlEqualTo(this.getImgUrl());
        }
        if (this.getSkuPrice() != null) {
            criteria.andSkuPriceEqualTo(this.getSkuPrice());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getLastUpdateBy())) {
            criteria.andLastUpdateByEqualTo(this.getLastUpdateBy());
        }
        if (this.getLastUpdateDate() != null) {
            criteria.andLastUpdateDateEqualTo(this.getLastUpdateDate());
        }
        return example;
    }
}