package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.call.direct.QualificationsOpenCall;
import com.estone.erp.publish.smt.model.AliexpressCategoryExample;
import com.estone.erp.publish.smt.model.dto.AliexpressCategoryQualificationVo;
import com.estone.erp.publish.smt.model.dto.AliexpressSpuCategoryQualificationVo;
import com.estone.erp.publish.smt.mq.model.CheckListingQualificationContext;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.QuerySpuCeFileRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 资质信息工具类
 */
@Slf4j
public class AliexpressQualificationUtils {
    //http://172.16.2.103:8080/browse/ES-12180
    public final static String defaultNoneKey = "产品安全信息/警示语";

    //http://172.16.2.103:8080/browse/ES-11121 统一取值 -label
    public final static String PACKAGE_IMAGE_GPSR_KEY = "EU_GPSR_label";
    public final static String PACKAGE_IMAGE_GPSR_LABEL = "欧盟GPSR合规标签";
    public final static String PACKAGE_IMAGE_EU_GPSR_NOT_CE_KEY = "-label"; //非CE
    public final static String PACKAGE_IMAGE_EU_GPSR_CE_KEY = "-label";//CE

    public final static String PACKAGE_IMAGE_EU_KEY = "Package_Label_Photo_EU";
    public final static String PACKAGE_IMAGE_EU_LABEL = "外包装/标签实拍图-欧盟";
    public final static String PACKAGE_IMAGE_EU_LABEL_KEY = "-Package";
    public final static String PACKAGE_IMAGE_EU_LABEL_CE_KEY = "-Package-eu-ce";

    public final static String PACKAGE_IMAGE_KEY = "Package_Label_Photo";
    public final static String PACKAGE_IMAGE_LABEL = "外包装/标签实拍图";
    public final static String PACKAGE_IMAGE_LABEL_KEY = "-Package";
    public final static String PACKAGE_IMAGE_LABEL_CE_KEY = "-Package-eu-ce";

    public final static String CE_LABEL = "CE认证";
    public final static String CE_LABEL_KEY = "-CE认证";

    public final static String PRODUCT_STOCK_IMAGE_KEY = "item_stock_photo";
    public final static String PRODUCT_STOCK_IMAGE_LABEL = "商品库存实拍图";
    public final static String PRODUCT_STOCK_IMAGE_LABEL_KEY = "-kucun";
    public final static String PRODUCT_STOCK_IMAGE_LABEL_CE_KEY = "-kucun";

    public final static Map<String, String> CUSTOMIZED_COLLECT = new HashMap<>();
    public final static Map<String, String> HAS_CE_QUALIFICATION = new HashMap<>();

    static {
        CUSTOMIZED_COLLECT.put(PACKAGE_IMAGE_GPSR_LABEL, PACKAGE_IMAGE_EU_GPSR_NOT_CE_KEY);
        CUSTOMIZED_COLLECT.put(PACKAGE_IMAGE_EU_LABEL, PACKAGE_IMAGE_EU_LABEL_KEY);
        CUSTOMIZED_COLLECT.put(PACKAGE_IMAGE_LABEL, PACKAGE_IMAGE_LABEL_KEY);
        CUSTOMIZED_COLLECT.put(PRODUCT_STOCK_IMAGE_LABEL, PRODUCT_STOCK_IMAGE_LABEL_KEY);

        HAS_CE_QUALIFICATION.put(PACKAGE_IMAGE_GPSR_LABEL, PACKAGE_IMAGE_EU_GPSR_CE_KEY);
        HAS_CE_QUALIFICATION.put(PACKAGE_IMAGE_EU_LABEL, PACKAGE_IMAGE_EU_LABEL_CE_KEY);
        HAS_CE_QUALIFICATION.put(PACKAGE_IMAGE_LABEL, PACKAGE_IMAGE_LABEL_CE_KEY);
        HAS_CE_QUALIFICATION.put(CE_LABEL, CE_LABEL_KEY);
        HAS_CE_QUALIFICATION.put(PRODUCT_STOCK_IMAGE_LABEL, PRODUCT_STOCK_IMAGE_LABEL_CE_KEY);
    }

    private static EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
    private static AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

    private static RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);

    /**
     * 设置资质信息
     *
     * @param spuQualifications
     * @param spu
     */
    public static void setSpuQualification(List<AliexpressSpuCategoryQualificationVo> spuQualifications, String spu, Integer categoryId, Boolean isRemove) {
        if (spuQualifications.isEmpty()) {
            return;
        }
        QuerySpuCeFileRequest querySpuCeFileRequest = new QuerySpuCeFileRequest();
        querySpuCeFileRequest.setSpu(spu);
        querySpuCeFileRequest.setCategoryId(categoryId);
        ApiResult<Map<String, List<String>>> mapApiResult = ProductUtils.queryProductProprietary(Lists.newArrayList(querySpuCeFileRequest));
        if (!mapApiResult.isSuccess()) {
            throw new BusinessException(spu + " 调用产品系统queryProductProprietary接口异常:" + mapApiResult.getErrorMsg());
        }
        Map<String, List<String>> productProprietaryMap = mapApiResult.getResult();
        List<String> result = productProprietaryMap.get(spu + "_" + categoryId);
        if(CollectionUtils.isNotEmpty(result)){
            //需要去除
            if(isRemove != null && isRemove){
                result = result.stream().filter(t -> !t.contains("检测报告.") && !t.contains("CPNP通报证明.")).collect(Collectors.toList());
            }
        }
        setSpuQualification(spuQualifications, result);
    }

    /**
     * 设置资质信息
     *
     * @param spuQualifications
     * @param allImageList      所有图片
     */
    public static void setSpuQualification(List<AliexpressSpuCategoryQualificationVo> spuQualifications, List<String> allImageList) {
        if (CollectionUtils.isEmpty(allImageList)) {
            allImageList = new ArrayList<>();
        }
        long count = spuQualifications.stream()
                .filter(spuCategoryQualificationVo -> spuCategoryQualificationVo.getCategoryQualificationVo().getLabel().equalsIgnoreCase(CE_LABEL))
                .count();
        Map<String, String> labelCollect = count > 0 ? HAS_CE_QUALIFICATION : CUSTOMIZED_COLLECT;
        for (AliexpressSpuCategoryQualificationVo spuQualification : spuQualifications) {
            AliexpressCategoryQualificationVo categoryQualificationVo = spuQualification.getCategoryQualificationVo();
            String label = categoryQualificationVo.getLabel();
            if (labelCollect.containsKey(label)) {
                setQualification(spuQualification, allImageList, labelCollect.get(label));
            } else {
                setQualification(spuQualification, allImageList, label);
            }
        }
    }

    /**
     * 设置资质信息
     *
     * @param spuQualifications
     * @param packageImgList     包装信息
     * @param productProprietary ce认证信息
     */
    public static void setSpuQualification(List<AliexpressSpuCategoryQualificationVo> spuQualifications, List<String> packageImgList, List<String> productProprietary) {
        if (CollectionUtils.isEmpty(packageImgList)) {
            packageImgList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(productProprietary)) {
            productProprietary = new ArrayList<>();
        }
        long count = spuQualifications.stream()
                .filter(spuCategoryQualificationVo -> spuCategoryQualificationVo.getCategoryQualificationVo().getLabel().equalsIgnoreCase(CE_LABEL))
                .count();
        Map<String, String> labelCollect = count > 0 ? HAS_CE_QUALIFICATION : CUSTOMIZED_COLLECT;
        for (AliexpressSpuCategoryQualificationVo spuQualification : spuQualifications) {
            AliexpressCategoryQualificationVo categoryQualificationVo = spuQualification.getCategoryQualificationVo();
            String label = categoryQualificationVo.getLabel();
            if (labelCollect.containsKey(label)) {
                if (label.equalsIgnoreCase(CE_LABEL)) {
                    setQualification(spuQualification, productProprietary, labelCollect.get(label));
                } else {
                    setQualification(spuQualification, packageImgList, labelCollect.get(label));
                }
            } else {
                List<String> allImageList = new ArrayList<>();
                allImageList.addAll(packageImgList);
                allImageList.addAll(productProprietary);
                setQualification(spuQualification, allImageList, label);
            }
        }
    }

    public static List<AliexpressSpuCategoryQualificationVo> init(List<AliexpressCategoryQualificationVo> vos, String spu) {
        // 初始化Spu相关属性信息
        return vos.stream().map(qualification -> {
            AliexpressSpuCategoryQualificationVo spuCategoryQualificationVo = new AliexpressSpuCategoryQualificationVo();
            spuCategoryQualificationVo.setSpu(spu);
            spuCategoryQualificationVo.setValue(null);
            spuCategoryQualificationVo.setHasValue(false);
            spuCategoryQualificationVo.setCategoryQualificationVo(qualification);
            return spuCategoryQualificationVo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取分类的资质信息
     *
     * @param saleAccountAndBusiness
     * @param categoryId
     * @return
     */
    public static List<AliexpressCategoryQualificationVo> getCategoryQualificationInfo(SaleAccountAndBusinessResponse saleAccountAndBusiness, Integer categoryId) {
        QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
        ResponseJson qualifications = qualificationsOpenCall.qualifications(saleAccountAndBusiness, categoryId);
        if (!qualifications.isSuccess()) {
            throw new BusinessException(categoryId + " 调用资质接口异常:" + qualifications.getMessage());
        }
        JSONArray qualificationModule = (JSONArray) qualifications.getBody().get("key");
        if (qualificationModule != null && !qualificationModule.isEmpty()) {
            return qualificationModule.toJavaList(AliexpressCategoryQualificationVo.class);
        } else {
            return null;
        }
    }

    private static void setQualification(AliexpressSpuCategoryQualificationVo spuQualification, List<String> list, String imageKey) {
        String sign = list.stream().filter(a -> a.contains(imageKey + ".")).findFirst().orElse(null);
        if (sign == null) {
            spuQualification.setHasValue(false);
        } else {
            spuQualification.setHasValue(true);
            spuQualification.setValue(sign);
        }
    }

    /**
     * 获取 有资质信息的
     *
     * @param spuCategoryQualification
     * @return
     */
    public static List<AliexpressSpuCategoryQualificationVo> getHasQualification(List<AliexpressSpuCategoryQualificationVo> spuCategoryQualification) {
        return spuCategoryQualification.stream().filter(AliexpressSpuCategoryQualificationVo::getHasValue).collect(Collectors.toList());
    }

    /**
     * 资质信息 json
     *
     * @param spuCategoryQualification
     * @return
     */
    public static JSONArray getAeopQualificationStructJsonArray(List<AliexpressSpuCategoryQualificationVo> spuCategoryQualification) {
        JSONArray jsonArray = new JSONArray();
        for (AliexpressSpuCategoryQualificationVo a : spuCategoryQualification) {
            if (a.getHasValue() && StringUtils.isNotBlank(a.getValue())) {
                AliexpressCategoryQualificationVo categoryQualificationVo = a.getCategoryQualificationVo();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", categoryQualificationVo.getType());
                jsonObject.put("key", categoryQualificationVo.getKey());
                jsonObject.put("value", a.getValue());
                jsonArray.add(jsonObject);
            }
        }
        if (jsonArray.isEmpty()) {
            return null;
        } else {
            return jsonArray;
        }
    }

    /**
     * 判断是否都有资质信息
     *
     * @param hasValue         分类资质信息，有value值
     * @param categoryInfoList 分类资质信息，没有value值
     * @return
     */
    public static boolean isHasQualification(JSONArray hasValue, JSONArray categoryInfoList) {
        if (hasValue == null || hasValue.isEmpty()) {
            return false;
        }
        Map<String, String> hasQualificationMap = new HashMap<>();
        hasValue.forEach(qualification -> {
            JSONObject qual = (JSONObject) qualification;
            hasQualificationMap.put(qual.getString("key"), qual.getString("value"));
        });

        for (Object a : categoryInfoList) {
            JSONObject qualification = ((JSONObject) a);
            String key = qualification.getString("key");
            if (hasQualificationMap.containsKey(key)) {
                String value = hasQualificationMap.get(key);
                if (StringUtils.isBlank(value)) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 发布成功后检查资质信息，不要跟同步代码放在一起，同步三小时一次，会持续发送就很有问题
     * 这里
     * @param saleAccountByAccountNumber
     * @param productId
     */
    public static void publishSuccessCheckProduceQualification(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId) {
        try {
            EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
            productListingRequest.setAliexpressAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            productListingRequest.setProductId(productId);
            List<EsAliexpressProductListing> esAliexpressProductListingList = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
            if (CollectionUtils.isEmpty(esAliexpressProductListingList)) {
                log.error("产品id：" + productId + "在线列表es查不到数据");
                return;
            }
            EsAliexpressProductListing esAliexpressProductListing = esAliexpressProductListingList.get(0);
            if (esAliexpressProductListing == null) {
                return;
            }
            Boolean isHasQualification = esAliexpressProductListing.getIsHasQualification();
            if (isHasQualification == null || !isHasQualification) {
                Integer categoryId = esAliexpressProductListing.getCategoryId();
                if (categoryId != null) {
                    // 没有资质信息，判断当前分类是否需要资质信息，需要就推送到 资质不全 队列
                    AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
                    AliexpressCategoryExample.Criteria aliexpressCategoryExampleCriteria = aliexpressCategoryExample.createCriteria();
                    aliexpressCategoryExampleCriteria.andIsQualificationEqualTo(true);
                    aliexpressCategoryExampleCriteria.andIsShowEqualTo(true);
                    aliexpressCategoryExampleCriteria.andLeafCategoryEqualTo(true);
                    aliexpressCategoryExampleCriteria.andCategoryIdEqualTo(categoryId);
                    int i = aliexpressCategoryService.countByExample(aliexpressCategoryExample);
                    if (i > 0) {
                        // 推送到队列
                        CheckListingQualificationContext context = new CheckListingQualificationContext();
                        context.setType(CheckListingQualificationContext.SourceType.LISTING.getCode());
                        context.setProductId(productId);
                        context.setAliexpressAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                        String msgId = PublishQueues.SMT_CHECK_QUALIFICATION_INFO_QUEUE + ":" + UUID.randomUUID();
                        rabbitTemplate.convertAndSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_CHECK_QUALIFICATION_INFO_KEY, context, (message) -> {
                            message.getMessageProperties().setHeader("msg-id", msgId);
                            return message;
                        });
                    }
                }
            }

        } catch (Exception e) {
            log.error("产品：productId" + productId + "刊登成功，但检查资质信息失败：" + e.getMessage());
        }

    }


    /**
     * 处理模板
     * @param saleAccountAndBusiness
     * @param spu
     */
    public static void handQualifications(Consumer<String> setAeopQualificationStructJson, Integer categoryId, String spu, SaleAccountAndBusinessResponse saleAccountAndBusiness){
        if(categoryId == null){
            return;
        }
        List<AliexpressCategoryQualificationVo> categoryQualificationInfo = AliexpressQualificationUtils.getCategoryQualificationInfo(saleAccountAndBusiness, categoryId);
        if(CollectionUtils.isNotEmpty(categoryQualificationInfo)){
            List<AliexpressSpuCategoryQualificationVo> spuCategoryQualification = AliexpressQualificationUtils.init(categoryQualificationInfo, spu);
            AliexpressQualificationUtils.setSpuQualification(spuCategoryQualification, spu, categoryId, false);

            JSONArray newJsonArray = AliexpressQualificationUtils.getAeopQualificationStructJsonArray(spuCategoryQualification);
            List<String> userKeyList = new ArrayList<>();
            if (newJsonArray != null && !newJsonArray.isEmpty()) {
                newJsonArray.forEach(a -> {
                    String key = (String) ((JSONObject) a).get("key");
                    userKeyList.add(key);
                });
            }
            //存在匹配数据
            if(CollectionUtils.isNotEmpty(newJsonArray)){
                setAeopQualificationStructJson.accept(newJsonArray.toJSONString());
            }
        }
    }
}
