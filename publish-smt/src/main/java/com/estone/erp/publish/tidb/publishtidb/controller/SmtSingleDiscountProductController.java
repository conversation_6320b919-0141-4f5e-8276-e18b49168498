package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.SmtSingleDiscountProductService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2024-07-06 10:29:06
 */
@RestController
@RequestMapping("smtSingleDiscountProduct")
public class SmtSingleDiscountProductController {
    @Resource
    private SmtSingleDiscountProductService smtSingleDiscountProductService;

    @PostMapping
    public ApiResult<?> postSmtSingleDiscountProduct(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchSmtSingleDiscountProduct": // 查询列表
                    CQuery<SmtSingleDiscountProductCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SmtSingleDiscountProductCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<SmtSingleDiscountProduct> results = smtSingleDiscountProductService.search(cquery);
                    return results;
                case "addSmtSingleDiscountProduct": // 添加
                    SmtSingleDiscountProduct smtSingleDiscountProduct = requestParam.getArgsValue(new TypeReference<SmtSingleDiscountProduct>() {
                    });
                    smtSingleDiscountProductService.insert(smtSingleDiscountProduct);
                    return ApiResult.newSuccess(smtSingleDiscountProduct);
            }
        }
        return ApiResult.newSuccess();
    }
}
