package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.NewProductSaleStatisticsTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductTemplateCompletionDto;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtNewProductTemplateCompletionMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductTemplateCompletion;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductTemplateCompletionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class SmtNewProductTemplateCompletionServiceImpl extends ServiceImpl<SmtNewProductTemplateCompletionMapper, SmtNewProductTemplateCompletion>
    implements SmtNewProductTemplateCompletionService{

    @Resource
    private SmtNewProductTemplateCompletionMapper smtNewProductTemplateCompletionMapper;
    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private ExcelSend excelSend;

    @Override
    public IPage<SmtNewProductTemplateCompletion> page(SmtNewProductTemplateCompletionDto dto) {
        LambdaQueryWrapper<SmtNewProductTemplateCompletion> pageQueryWrapper = this.getPageQueryWrapper(dto);
        setSort(dto, pageQueryWrapper);
        Page<SmtNewProductTemplateCompletion> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return this.page(page, pageQueryWrapper);
    }

    @Override
    public IPage<SmtNewProductTemplateCompletion> pageQuery(SmtNewProductTemplateCompletionDto dto){
        isAuth(dto);
        return this.page(dto);
    }

    private void isAuth(SmtNewProductTemplateCompletionDto dto) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        isPermissionProcessing(dto, superAdminOrEquivalent);
    }

    private void isPermissionProcessing(SmtNewProductTemplateCompletionDto dto, ApiResult<Boolean> superAdminResult) {
        //查询销售为空
        if (!superAdminResult.getResult()) {
            // 获取当前权限下的销售人员
            List<String> currentPermissionEmployeeNo = permissionsHelper.getCurrentUserEmployeeNoPermission(null, null, null, SaleChannel.CHANNEL_SMT);
            if(dto.getSaleType() == NewProductSaleStatisticsTypeEnum.sale.getCode()){
                dto.setSale(StringUtils.join(currentPermissionEmployeeNo, ","));
            }else if(dto.getSaleType() == NewProductSaleStatisticsTypeEnum.saleLeader.getCode()){
                dto.setSaleLeader(StringUtils.join(currentPermissionEmployeeNo, ","));
            }
        }
    }

    /**
     * 获取分页信息
     * @return 分页信息
     */
    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtNewProductTemplateCompletion> wrapper){
        List<Map<Object, Object>> tidbPageMetaMap = smtNewProductTemplateCompletionMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }

    private LambdaQueryWrapper<SmtNewProductTemplateCompletion> getPageQueryWrapper(SmtNewProductTemplateCompletionDto dto) {
        LambdaQueryWrapper<SmtNewProductTemplateCompletion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getIdList()), SmtNewProductTemplateCompletion::getId, dto.getIdList());
        queryWrapper.in(StringUtils.isNotBlank(dto.getSale()), SmtNewProductTemplateCompletion::getSale, CommonUtils.splitList(dto.getSale(), ","));
        queryWrapper.in(StringUtils.isNotBlank(dto.getSaleLeader()), SmtNewProductTemplateCompletion::getSaleLeader, CommonUtils.splitList(dto.getSaleLeader(), ","));
        if(StringUtils.isNotBlank(dto.getFromPushTime())){
            queryWrapper.ge(SmtNewProductTemplateCompletion::getPushTime, dto.getFromPushTime());
        }
        if(StringUtils.isNotBlank(dto.getToPushTime())){
            queryWrapper.le(SmtNewProductTemplateCompletion::getPushTime, dto.getToPushTime());
        }
        queryWrapper.eq(dto.getSaleType() != null, SmtNewProductTemplateCompletion::getSaleType, dto.getSaleType());
        return queryWrapper;
    }


    private static void setSort(SmtNewProductTemplateCompletionDto dto, LambdaQueryWrapper<SmtNewProductTemplateCompletion> pageQueryWrapper) {
        if (StringUtils.isBlank(dto.getSort())) {
            dto.setSort("pushTime");
        }
        if (dto.getIsAsc() == null) {
            dto.setIsAsc(false);
        }
        // order by
        if ("pushTime".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), SmtNewProductTemplateCompletion::getPushTime);
        }
    }

    @Override
    public void download(SmtNewProductTemplateCompletionDto dto) {
        dto.setPageNum(1);
        dto.setPageSize(500000);
        IPage<SmtNewProductTemplateCompletion> page = this.pageQuery(dto);
        if (page.getTotal() == 0 || CollectionUtils.isEmpty(page.getRecords())) {
            throw new RuntimeException("没有数据！");
        }

        if (page.getTotal() > 500000){
            throw new RuntimeException("数据量过大，请缩小查询范围！");
        }
        ResponseJson responseJson = excelSend.downloadSmtNewProductTemplateCompletion(ExcelTypeEnum.downloadSmtNewProductTemplateCompletion.getCode(), dto);
        if (!responseJson.isSuccess()) {
            throw new RuntimeException(responseJson.getMessage());
        }

    }

    @Override
    public void downloadUnfinishedSpu(SmtNewProductTemplateCompletionDto dto) {
        ResponseJson responseJson = excelSend.downloadSmtNewProductTemplateCompletion(ExcelTypeEnum.downloadUnfinishedSpu.getCode(), dto);
        if (!responseJson.isSuccess()) {
            throw new RuntimeException(responseJson.getMessage());
        }
    }
}




