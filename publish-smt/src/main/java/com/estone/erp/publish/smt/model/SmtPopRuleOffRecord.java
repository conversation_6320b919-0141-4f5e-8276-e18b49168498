package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class SmtPopRuleOffRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 图片
     */
    private String img;

    /**
     * 店铺
     */
    private String account;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * spu
     */
    private String spu;

    /**
     * 货号多个英文逗号拼接
     */
    private String skus;

    /**
     * skuId多个英文逗号拼接
     */
    private String skuIds;

    /**
     * 规则名称 
     */
    private String ruleName;

    /**
     * off 下架 delete 删除
     */
    private String offWay;

    /**
     * 规则内容
     */
    private String ruleContent;

    /**
     * 产品具体符合规则信息
     */
    private String productInfo;

    /**
     * 24小时销量
     */
    private Integer order24hCount;

    /**
     * 7天销量
     */
    private Integer orderLast7dCount;

    /**
     * 14天销量
     */
    private Integer orderLast14dCount;

    /**
     * 30天销量
     */
    private Integer orderLast30dCount;

    /**
     * 60天销量
     */
    private Integer orderLast60dCount;

    /**
     * 180天销量
     */
    private Integer orderLast180dCount;

    /**
     * 总销量
     */
    private Integer orderNumTotal;

    /**
     * 7天浏览量
     */
    private Integer view7dCount;

    /**
     * 14天浏览量
     */
    private Integer view14dCount;

    /**
     * 30天浏览量
     */
    private Integer view30dCount;

    /**
     * 7天曝光量
     */
    private Integer exposure7dCount;

    /**
     * 14天曝光量
     */
    private Integer exposure14dCount;

    /**
     * 30天曝光量
     */
    private Integer exposure30dCount;

    /**
     * 1 成功 0 失败
     */
    private Integer executeState;

    /**
     * 失败详情
     */
    private String failInfo;

    /**
     * 下架时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp offDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间(匹配时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createDate;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateDate;

    // 销售
    private String salemanager;
    // 销售组长
    private String salemanagerLeader;
    // 销售主管
    private String salesSupervisorName;
}