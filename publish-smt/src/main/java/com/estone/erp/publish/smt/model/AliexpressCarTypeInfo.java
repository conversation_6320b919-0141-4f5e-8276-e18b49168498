package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressCarTypeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column aliexpress_car_type_info.id
     */
    private Integer id;

    /**
     * 类型层级 database column aliexpress_car_type_info.type_level
     */
    private Integer typeLevel;

    /**
     * 车载类型 database column aliexpress_car_type_info.car_type
     */
    private String carType;

    /**
     * 自身属性id
     */
    private Long typeId;

    /**
     * 父级主键id database column aliexpress_car_type_info.parent_id
     */
    private Integer parentId;

    /**
     * 主键集合
     */
    private String parentIds;

    /**
     * 请求参数1(子节点分类id) database column aliexpress_car_type_info.param1
     */
    private Integer param1;

    /**
     * 请求参数2 database column aliexpress_car_type_info.param2
     */
    private String param2;

    /**
     * country_up_id database column aliexpress_car_type_info.country_up_id
     */
    private Long countryUpId;

    /**
     * country_id database column aliexpress_car_type_info.country_id
     */
    private Long countryId;

    /**
     * country_en database column aliexpress_car_type_info.country_en
     */
    private String countryEn;

    /**
     * country_zh database column aliexpress_car_type_info.country_zh
     */
    private String countryZh;

    /**
     * make_up_id database column aliexpress_car_type_info.make_up_id
     */
    private Long makeUpId;

    /**
     * make_id database column aliexpress_car_type_info.make_id
     */
    private Long makeId;

    /**
     * make_en database column aliexpress_car_type_info.make_en
     */
    private String makeEn;

    /**
     * make_zh database column aliexpress_car_type_info.make_zh
     */
    private String makeZh;

    /**
     * model_up_id database column aliexpress_car_type_info.model_up_id
     */
    private Long modelUpId;

    /**
     * model_id database column aliexpress_car_type_info.model_id
     */
    private Long modelId;

    /**
     * model_en database column aliexpress_car_type_info.model_en
     */
    private String modelEn;

    /**
     * model_zh database column aliexpress_car_type_info.model_zh
     */
    private String modelZh;

    /**
     * year_up_id database column aliexpress_car_type_info.year_up_id
     */
    private Long yearUpId;

    /**
     * year_id database column aliexpress_car_type_info.year_id
     */
    private Long yearId;

    /**
     * year_en database column aliexpress_car_type_info.year_en
     */
    private String yearEn;

    /**
     * year_zh database column aliexpress_car_type_info.year_zh
     */
    private String yearZh;

    /**
     * trim_up_id database column aliexpress_car_type_info.trim_up_id
     */
    private Long trimUpId;

    /**
     * trim_id database column aliexpress_car_type_info.trim_id
     */
    private Long trimId;

    /**
     * trim_en database column aliexpress_car_type_info.trim_en
     */
    private String trimEn;

    /**
     * trim_zh database column aliexpress_car_type_info.trim_zh
     */
    private String trimZh;

    /**
     * enigne_up_id database column aliexpress_car_type_info.enigne_up_id
     */
    private Long enigneUpId;

    /**
     * enigne_id database column aliexpress_car_type_info.enigne_id
     */
    private Long enigneId;

    /**
     * enigne_en database column aliexpress_car_type_info.enigne_en
     */
    private String enigneEn;

    /**
     * enigne_zh database column aliexpress_car_type_info.enigne_zh
     */
    private String enigneZh;

    /**
     * 创建人 database column aliexpress_car_type_info.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column aliexpress_car_type_info.create_time
     */
    private Timestamp createTime;

    /**
     * 修改人 database column aliexpress_car_type_info.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column aliexpress_car_type_info.update_time
     */
    private Timestamp updateTime;
}