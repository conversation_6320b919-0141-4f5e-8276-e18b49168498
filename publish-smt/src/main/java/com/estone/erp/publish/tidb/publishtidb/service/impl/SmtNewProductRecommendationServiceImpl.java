package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.newUsermgt.model.SaleStructureVO;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductRecommendationDto;
import com.estone.erp.publish.tidb.publishtidb.dto.SmtNewProductRecommendationImportUpdateSaleDto;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtNewProductRecommendationMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductRecommendationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class SmtNewProductRecommendationServiceImpl extends ServiceImpl<SmtNewProductRecommendationMapper, SmtNewProductRecommendation>
        implements SmtNewProductRecommendationService {
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private ExcelSend excelSend;
    @Resource
    private SmtNewProductRecommendationMapper smtNewProductRecommendationMapper;

    @Override
    public SmtNewProductRecommendation getBySpu(String spu) {
        LambdaQueryWrapper<SmtNewProductRecommendation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmtNewProductRecommendation::getSpu, spu);
        return smtNewProductRecommendationMapper.selectOne(queryWrapper);
    }

    @Override
    public String importUpdateSale(MultipartFile file) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        List<SmtNewProductRecommendationImportUpdateSaleDto> list = this.readExcel(file);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("导入失败，没有获取数据!");
        }
        Integer errorCount = 0;
        Integer successCount = 0;
        for (SmtNewProductRecommendationImportUpdateSaleDto dto : list) {
            if (StringUtils.isBlank(dto.getSpu()) || StringUtils.isBlank(dto.getSale())) {
                continue;
            }
            SmtNewProductRecommendation smtNewProductRecommendation = this.getBySpu(dto.getSpu());
            if (ObjectUtils.isEmpty(smtNewProductRecommendation)){
                stringBuilder.append("spu:").append(dto.getSpu()).append(" 不存在").append("\n");
                errorCount++;
                continue;
            }
            if (StringUtils.isNotBlank(smtNewProductRecommendation.getSale())){
                stringBuilder.append("spu:").append(dto.getSpu()).append(" 销售已存在").append("\n");
                errorCount++;
                continue;
            }

            String saleLeader = smtNewProductRecommendation.getSaleLeader();
            if(StringUtils.isBlank(saleLeader)){
                SaleStructureVO saleSuperiorNew = NewUsermgtUtils.getSaleSuperiorNew(dto.getSale());
                if(StringUtils.isNotBlank(saleSuperiorNew.getErrorMsg())){
                    log.info(saleSuperiorNew.getErrorMsg());
                }
                String saleLeader1 = saleSuperiorNew.getSaleLeader();
                if(StringUtils.isNotBlank(saleLeader1) && StringUtils.contains(saleLeader1, "-")){
                    String s = saleLeader1.split("-")[1];
                    smtNewProductRecommendation.setSaleLeader(s);
                }
            }
            smtNewProductRecommendation.setSale(dto.getSale());
            smtNewProductRecommendation.setUpdatedTime(new Date());
            smtNewProductRecommendationMapper.updateById(smtNewProductRecommendation);
            successCount++;
        }
        StringBuilder stringBuilder1 = new StringBuilder();
        if (successCount > 0){
            stringBuilder1.append("更新成功").append(successCount).append("条").append("\n");
        }
        if (errorCount > 0){
            stringBuilder1.append("更新失败").append(errorCount).append("条").append("\n");
            stringBuilder1.append("失败原因：").append("\n").append(stringBuilder);
        }
        return stringBuilder1.toString();

    }

    public List<SmtNewProductRecommendationImportUpdateSaleDto> readExcel(MultipartFile file) throws IOException {
        List<SmtNewProductRecommendationImportUpdateSaleDto> list = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue;
                }

                String spu = getCellValueAsString(row.getCell(0));
                String sale = getCellValueAsString(row.getCell(1));

                list.add(new SmtNewProductRecommendationImportUpdateSaleDto(spu, sale));
            }
        }

        return list;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return getCellValueAsString(evaluateFormulaCell(cell));
            default:
                return "";
        }
    }

    private Cell evaluateFormulaCell(Cell cell) {
        Workbook workbook = cell.getSheet().getWorkbook();
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        return evaluator.evaluateInCell(cell);
    }

    @Override
    public IPage<SmtNewProductRecommendation> pageQuery(SmtNewProductRecommendationDto dto) {
        isAuth(dto);
        return this.page(dto);
    }

    private void isAuth(SmtNewProductRecommendationDto dto) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        //不是超管
        if (!superAdminOrEquivalent.getResult()) {
            //页面条件
            String saleLeader = dto.getSaleLeader();
            String sale = dto.getSale();

            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.tokenUser();
            NewUser newUser = newUserApiResult.getResult();
            boolean isLeader = StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.GROUP_LEADER);
            boolean isSale = StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.SALE);
            if (isLeader) {
                if (StringUtils.isBlank(saleLeader)) {
                    //需要查询当前人管理的下级人员 eg：主管
                    ApiResult<List<NewUser>> result = NewUsermgtUtils.listSecondaryTeamByEmployeeNo(SaleChannel.CHANNEL_SMT, WebUtils.getUserName());
                    if (!result.isSuccess()) {
                        throw new RuntimeException(result.getErrorMsg());
                    }
                    Set<String> authSaleLeader = result.getResult()
                            .stream()
                            .map(NewUser::getEmployeeNo)
                            .collect(Collectors.toSet());
                    authSaleLeader.add(WebUtils.getUserName()); //添加自己
                    dto.setSaleLeader(StringUtils.join(authSaleLeader, ","));
                }
            } else if (isSale) {
                if (StringUtils.isBlank(sale)) {
                    dto.setSale(WebUtils.getUserName());
                }
            } else {
                if (StringUtils.isBlank(saleLeader) && StringUtils.isBlank(sale)) {
                    //需要查询当前人管理的下级人员 eg：主管
                    ApiResult<List<NewUser>> result = NewUsermgtUtils.listSecondaryTeamByEmployeeNo(SaleChannel.CHANNEL_SMT, WebUtils.getUserName());
                    if (!result.isSuccess()) {
                        throw new RuntimeException(result.getErrorMsg());
                    }
                    Set<String> authSaleLeader = result.getResult()
                            .stream()
                            .map(NewUser::getEmployeeNo)
                            .collect(Collectors.toSet());
                    authSaleLeader.add(WebUtils.getUserName()); //添加自己
                    dto.setSaleLeader(StringUtils.join(authSaleLeader, ","));
                }
            }
        }
    }

    @Override
    public IPage<SmtNewProductRecommendation> page(SmtNewProductRecommendationDto dto) {
        LambdaQueryWrapper<SmtNewProductRecommendation> pageQueryWrapper = this.getPageQueryWrapper(dto);
        setSort(dto, pageQueryWrapper);
        Page<SmtNewProductRecommendation> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return this.page(page, pageQueryWrapper);
    }

    /**
     * 权限处理
     *
     * @param dto
     */
    private void isPermissionProcessing(SmtNewProductRecommendationDto dto) {


    }

    public ApiResult<String> download(SmtNewProductRecommendationDto dto) {
        try {
            int maxRow = 500000;
            dto.setPageNum(1);
            dto.setPageSize(1);

            // 判断是否有权限
            isAuth(dto);
            IPage<SmtNewProductRecommendation> page = this.pageQuery(dto);
            long total = page.getTotal();
            if (total == 0) {
                return ApiResult.newError("导出数据为空");
            }
            if (total > maxRow) {
                return ApiResult.newError("导出数据超过" + maxRow + "条，请缩小查询条件");
            }
            excelSend.downNewProductRecommendation(ExcelTypeEnum.downNewProductRecommendation.getCode(), dto);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    private LambdaQueryWrapper<SmtNewProductRecommendation> getPageQueryWrapper(SmtNewProductRecommendationDto dto) {
        LambdaQueryWrapper<SmtNewProductRecommendation> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getIdList()), SmtNewProductRecommendation::getId, dto.getIdList());
        queryWrapper.in(StringUtils.isNotBlank(dto.getIdStr()), SmtNewProductRecommendation::getId, CommonUtils.splitList(dto.getIdStr(), ","));

        //spu
        queryWrapper.in(StringUtils.isNotBlank(dto.getSpu()), SmtNewProductRecommendation::getSpu, CommonUtils.splitList(dto.getSpu(), ","));
        queryWrapper.in(StringUtils.isNotBlank(dto.getItemStatus()), SmtNewProductRecommendation::getItemStatus, CommonUtils.splitList(dto.getItemStatus(), ","));
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getCategoryFullPathCodeList()), SmtNewProductRecommendation::getCategoryFullPathCode, dto.getCategoryFullPathCodeList());
        queryWrapper.like(StringUtils.isNotBlank(dto.getTitleLike()), SmtNewProductRecommendation::getTitle, dto.getTitleLike());
        queryWrapper.in(StringUtils.isNotBlank(dto.getSale()), SmtNewProductRecommendation::getSale, CommonUtils.splitList(dto.getSale(), ","));
        queryWrapper.in(StringUtils.isNotBlank(dto.getSaleLeader()), SmtNewProductRecommendation::getSaleLeader, CommonUtils.splitList(dto.getSaleLeader(), ","));
        Boolean saleIsNull = dto.getSaleIsNull();
        if (saleIsNull != null && saleIsNull) {
            queryWrapper.isNull(SmtNewProductRecommendation::getSale);
        }
        Boolean isBanned = dto.getIsBanned();
        queryWrapper.eq(isBanned != null, SmtNewProductRecommendation::getIsBanned, isBanned);
        queryWrapper.eq(dto.getIsTheSameDayPublish() != null, SmtNewProductRecommendation::getIsTheSameDayPublish, dto.getIsTheSameDayPublish());
        queryWrapper.eq(dto.getIsThreeDayPublish() != null, SmtNewProductRecommendation::getIsThreeDayPublish, dto.getIsThreeDayPublish());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getPublishStatusList()), SmtNewProductRecommendation::getPublishStatus, dto.getPublishStatusList());

        if (StringUtils.isNotBlank(dto.getFromPushTime())) {
            queryWrapper.ge(SmtNewProductRecommendation::getPushTime, dto.getFromPushTime());
        }
        if (StringUtils.isNotBlank(dto.getToPushTime())) {
            queryWrapper.le(SmtNewProductRecommendation::getPushTime, dto.getToPushTime());
        }
        if (StringUtils.isNotBlank(dto.getFromEnterProductTime())) {
            queryWrapper.ge(SmtNewProductRecommendation::getEnterProductTime, dto.getFromEnterProductTime());
        }
        if (StringUtils.isNotBlank(dto.getToEnterProductTime())) {
            queryWrapper.le(SmtNewProductRecommendation::getEnterProductTime, dto.getToEnterProductTime());
        }
        return queryWrapper;
    }

    private static void setSort(SmtNewProductRecommendationDto dto, LambdaQueryWrapper<SmtNewProductRecommendation> pageQueryWrapper) {
        if (StringUtils.isBlank(dto.getSort())) {
            dto.setSort("pushTime");
        }
        if (dto.getIsAsc() == null) {
            dto.setIsAsc(false);
        }
        // order by
        if ("pushTime".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), SmtNewProductRecommendation::getPushTime);
        }
    }

    /**
     * 获取分页信息
     *
     * @return 分页信息
     */
    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<SmtNewProductRecommendation> wrapper) {
        List<Map<Object, Object>> tidbPageMetaMap = smtNewProductRecommendationMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }

    @Override
    public void updateTitleToProductName() {
        List<SmtNewProductRecommendation> smtNewProductRecommendations = list();
        List<String> spuList = smtNewProductRecommendations.stream().map(SmtNewProductRecommendation::getSpu).collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(spuList, 100);
        Map<String, ProductInfo> productMap = partitions.stream()
                .map(ProductUtils::findProductInfos)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toMap(
                        ProductInfo::getMainSku,
                        Function.identity(),
                        (p1, p2) -> p1
                ));
        for (SmtNewProductRecommendation recommendation : smtNewProductRecommendations) {
            ProductInfo productInfo = productMap.get(recommendation.getSpu());
            if (Objects.isNull(productInfo)) {
                continue;
            }
            recommendation.setTitle(productInfo.getName());
        }
        updateBatchById(smtNewProductRecommendations, 200);
    }

}




