package com.estone.erp.publish.smt.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class AliexpressProductForAreaPrice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Long id;

    /**
     * 速卖通帐号
     */
    private String aliexpressAccountNumber;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 模版展示图
     */
    private String displayImageUrl;

    /**
     * sku商品编码
     */
    private String skuCode;

    /**
     * sku对应的单品货号
     */
    private String articleNumber;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * SKU ID
     */
    private String platSkuId;

    /**
     * 货币单位
     */
    private String currencyCode;

    /**
     * sku价格
     */
    private Double skuPrice;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。
     */
    private Integer categoryId;

    /**
     * 
     */
    private Long freightTemplateId;

    /**
     * 服务模板
     */
    private Long promiseTemplateId;

    /**
     * 
     */
    private Long groupId;

    /**
     * 产品所在的产品分组列表，多个用;分开
     */
    private String groupIds;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 创建时间(平台)
     */
    private Timestamp gmtCreate;

    /**
     * 更新时间
     */
    private Timestamp gmtModified;
}