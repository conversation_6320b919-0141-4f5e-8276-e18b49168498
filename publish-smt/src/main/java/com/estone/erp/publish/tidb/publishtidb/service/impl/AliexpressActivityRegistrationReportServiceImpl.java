package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressActivityRegistrationReportMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReport;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReportCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.IAliexpressActivityRegistrationReportService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 平台活动报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
public class AliexpressActivityRegistrationReportServiceImpl extends ServiceImpl<AliexpressActivityRegistrationReportMapper, AliexpressActivityRegistrationReport> implements IAliexpressActivityRegistrationReportService {

    @Resource
    private AliexpressActivityRegistrationReportMapper aliexpressActivityRegistrationReportMapper;
    @Resource
    private ExcelSend excelSend;

    @Override
    public CQueryResult<AliexpressActivityRegistrationReport> search(CQuery<AliexpressActivityRegistrationReportCriteria> cquery) {
        AliexpressActivityRegistrationReportCriteria criteria=cquery.getSearch();
        Page page=new Page();
        page.setCurrent(cquery.getPage());
        page.setSize(cquery.getLimit());
        LambdaQueryWrapper<AliexpressActivityRegistrationReport> queryWrapper=new LambdaQueryWrapper<>();
        handleWrapper(queryWrapper,criteria);

        IPage<AliexpressActivityRegistrationReport> iPage=aliexpressActivityRegistrationReportMapper.selectPage(page,queryWrapper);
        // 组装结果
        CQueryResult<AliexpressActivityRegistrationReport> result = new CQueryResult<>();
        result.setTotal(iPage.getTotal());
        result.setTotalPages((int)iPage.getPages());
        result.setRows(iPage.getRecords());
        return result;
    }

    void handleWrapper(LambdaQueryWrapper<AliexpressActivityRegistrationReport> queryWrapper,AliexpressActivityRegistrationReportCriteria criteria){
        queryWrapper.eq(AliexpressActivityRegistrationReport::getUser,criteria.getUser());
        List<String> activityIds = CommonUtils.splitList(criteria.getActivityId(), ",");

        queryWrapper.in(StringUtils.isNotBlank(criteria.getActivityId()),AliexpressActivityRegistrationReport::getActivityId,activityIds);
        queryWrapper.like(StringUtils.isNotBlank(criteria.getActivityName()),AliexpressActivityRegistrationReport::getActivityName,criteria.getActivityName());
        queryWrapper.eq(criteria.getActivityType() != null,AliexpressActivityRegistrationReport::getActivityType,criteria.getActivityType());
        queryWrapper.between(criteria.getRecruitmentStartTimeStart() != null && criteria.getRecruitmentStartTimeEnd() != null,AliexpressActivityRegistrationReport::getRecruitmentStartTime,criteria.getRecruitmentStartTimeStart(),criteria.getRecruitmentStartTimeEnd());
        queryWrapper.between(criteria.getRecruitmentEndTimeStart() != null && criteria.getRecruitmentEndTimeEnd() != null,AliexpressActivityRegistrationReport::getRecruitmentEndTime,criteria.getRecruitmentEndTimeStart(),criteria.getRecruitmentEndTimeEnd());
        queryWrapper.between(criteria.getDisplayStartTimeStart() != null && criteria.getDisplayStartTimeEnd() != null,AliexpressActivityRegistrationReport::getDisplayStartTime,criteria.getDisplayStartTimeStart(),criteria.getDisplayStartTimeEnd());
        queryWrapper.between(criteria.getDisplayEndTimeStart() != null && criteria.getDisplayEndTimeEnd() != null,AliexpressActivityRegistrationReport::getDisplayEndTime,criteria.getDisplayEndTimeStart(),criteria.getDisplayEndTimeEnd());
        queryWrapper.orderByDesc(AliexpressActivityRegistrationReport::getId);
    }

    @Override
    public ResponseJson download(CQuery<AliexpressActivityRegistrationReportCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressActivityRegistrationReportCriteria query=cquery.getSearch();
        if(CollectionUtils.isEmpty(query.getIds())) {
            LambdaQueryWrapper<AliexpressActivityRegistrationReport> queryWrapper=new LambdaQueryWrapper<>();
            handleWrapper(queryWrapper,query);
            int total=aliexpressActivityRegistrationReportMapper.selectCount(queryWrapper);
            if(total>1000000){
                throw new RuntimeException("导出数量不能大于100w");
            }
        }
        ResponseJson responseJson = excelSend.downloadActivityRegistrationReport(ExcelTypeEnum.downloadActivityRegistrationReport.getCode(), cquery);
        return responseJson;
    }
}
