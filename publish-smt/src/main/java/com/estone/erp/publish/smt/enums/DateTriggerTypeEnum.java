package com.estone.erp.publish.smt.enums;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月08日/17:55
 * @Description: <p>日期触发类型</p>
 * @Version: 1.0.0
 * @modified:
 */
public enum DateTriggerTypeEnum {
    EVERY_DAY("every_day", "每天"),
    EVERY_WEEK("every_week", "每周"),
    EVERY_MONTH("every_month", "每月");

    private String code;

    private String name;

    private DateTriggerTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }


    public static DateTriggerTypeEnum build(String code) {
        DateTriggerTypeEnum[] values = values();
        for (DateTriggerTypeEnum type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        DateTriggerTypeEnum[] values = values();
        for (DateTriggerTypeEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public String intCode() {
        return this.code;
    }
}
