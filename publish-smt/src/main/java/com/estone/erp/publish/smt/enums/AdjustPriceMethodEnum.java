package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AdjustPriceMethodEnum {

    ACCOUNT_CONFIG(1, "按照店铺配置的折扣毛利率重新算价"),
    PERCENTAGE(2, "按百分比调价"),
    FIXED_GROSS_PROFIT_MARGIN(3, "固定毛利率调价");

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

}
