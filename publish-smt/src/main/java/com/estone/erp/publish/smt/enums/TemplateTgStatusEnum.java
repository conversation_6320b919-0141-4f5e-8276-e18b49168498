package com.estone.erp.publish.smt.enums;

public enum TemplateTgStatusEnum {

    WAIT_PUBLISH(1, "待刊登"),

    WAIT_TIMING(5, "等待定时刊登"),

    WAIT_QUEUE_PUBLISH(6, "等待队列刊登"),

    PUBLISHING(2, "刊登中"),

    PUBLISH_SUCCESS(3, "刊登成功"),

    PUBLISH_FAILED(4, "刊登失败");

    private int code;

    private String name;

    private TemplateTgStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TemplateTgStatusEnum build(int code) {
        TemplateTgStatusEnum[] values = values();
        for (TemplateTgStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        TemplateTgStatusEnum[] values = values();
        for (TemplateTgStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

}
