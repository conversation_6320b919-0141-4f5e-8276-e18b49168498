package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublishLeader;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressNewProductPublishLeaderService;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressNewProductPublishLeaderMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【aliexpress_new_product_publish_leader(Smt 刊登次数组长刊登情况)】的数据库操作Service实现
* @createDate 2025-03-13 09:40:29
*/
@Service
public class AliexpressNewProductPublishLeaderServiceImpl extends ServiceImpl<AliexpressNewProductPublishLeaderMapper, AliexpressNewProductPublishLeader>
    implements AliexpressNewProductPublishLeaderService{

    @Override
    public Map<Long, List<AliexpressNewProductPublishLeader>> getPublishLeaderByMainId(List<Long> mainIdList) {
        if (mainIdList == null || mainIdList.isEmpty()) {
            return Map.of();
        }
        LambdaQueryWrapper<AliexpressNewProductPublishLeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AliexpressNewProductPublishLeader::getPublishId, mainIdList);
        List<AliexpressNewProductPublishLeader> list = list(wrapper);
        return list.stream().collect(Collectors.groupingBy(AliexpressNewProductPublishLeader::getPublishId, Collectors.toList()));
    }
}




