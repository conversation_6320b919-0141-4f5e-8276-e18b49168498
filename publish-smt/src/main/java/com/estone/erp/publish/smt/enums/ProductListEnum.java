package com.estone.erp.publish.smt.enums;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public enum ProductListEnum {
    account(1, "店铺账号"),
    product_id(2, "商品ID"),
    sku_code(3, "商品编码"),
    displayImage(4, "图片"),
    articleNumber(5, "单品货号"),
    skuStatus(6, "单品状态"),
    subject(7, "标题"),
    categoryCn(8, "产品类目"),
    forbidChannel(9, "禁售平台"),
    tagNames(10, "产品标签"),
    specialGoodsName(11, "特殊标签"),
    stock(12, "库存"),
    price(13, "价格"),
    grossProfit(14, "毛利"),
    grossProfitRate(15, "毛利率"),
    grossWeight(16, "重量"),
    groupName(17, "产品组"),
    freightName(18, "运费模板"),
    publishRole(19, "刊登角色"),
    autoONandOff(20, "自动上下架"),
    itemStatus(21, "在售状态"),
    salemanager(22, "销售"),
    salemanagerLeader(23, "销售组长"),
    salesSupervisorName(24, "销售主管"),
    order_24H_count(25, "24小时销量"),
    order_last_7d_count(26, "7天销量"),
    order_last_14d_count(27, "14天销量"),
    order_last_30d_count(28, "30天销量"),
    order_last_60d_count(29, "60天销量"),
    order_num_total(30, "总销量"),
    order_days_within_30d(31, "30天动销天数"),
    order_days_within_60d(32, "60天动销天数"),
    order_days_within_30d_rate(33, "30天动销率"),
    order_days_within_60d_rate(34, "60天动销率"),
    exposure_7d_count(62, "7天曝光量"),
    view_7d_count(63, "7天浏览量"),
    exposure_14d_count(64, "14天曝光量"),
    view_14d_count(65, "14天浏览量"),
    exposure_30d_count(35, "30天曝光量"),
    view_30d_count(36, "30天浏览量"),
    gmtCreate(37, "上架时间"),
    wsOfflineDate(38, "下架时间"),
    lastSyncTime(39, "同步时间"),
    deliveryAddress(40, "发货地"),
    skuId(41, "SKUid"),
    cnProvince(42, "中国省份"),
    brand(43, "品牌"),
    infringementTypeNames(44, "禁售类型"),
    infringementObjs(45, "禁售原因"),
    prohibitionSites(46, "禁售站点"),
    productDataSourceType(47, "数据来源"),
    composeStatus(48, "组合状态"),
    promotion(49, "是否促销"),
    newState(50, "是否新品"),
    order_last_180d_count_new(51, "180天销量"),
    itemShow(52, "商品质量"),
    unsalableTag(53, "滞销标签"),
    price28(54, "32国价格"),
    infringementWordList(55, "侵权词"),
    infringementWordInfos(56, "商标词标识"),
    isHasQualification(57, "产品资质"),
    infringementCheckTime(58, "侵权校验时间"),
    categoryLabel(59, "产品物流模板标签"),
    viewUpdateDate(60, "流量更新时间"),
    onlineStatus(61, "是否在线"),

    //上面曝光量放在一起了，从66开始
    singleDisCountName(66, "单品折扣名称"),
    statusName(67, "活动状态"),
    wholeStationDiscount(68, "全站折扣"),

    storeClubDiscountRate(69, "粉丝折扣"),
    jit_order_num_24h(70, "JIT 24小时销量"),
    jit_order_num_7d(71, "JIT 7天销量"),
    jit_order_num_14d(72, "JIT 14天销量"),
    jit_order_num_30d(73, "JIT 30天销量"),
    jit_order_num_60d(74, "JIT 60天销量"),
    jit_order_num_180d(75, "JIT 180天销量"),
    jit_order_num_total(76, "JIT 总销量"),
    halfCountryExitLabel(77, "半托管退出标签"),

    ;

    private int code;

    private String name;

    private ProductListEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ProductListEnum build(int code) {
        ProductListEnum[] values = values();
        for (ProductListEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        ProductListEnum[] values = values();
        for (ProductListEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }


    public static List<String> getNameList(List<Integer> codeList){

        if(CollectionUtils.isEmpty(codeList)){
            return null;
        }
        List<String> nameList = new ArrayList<>();
        ProductListEnum[] values = values();
        for (ProductListEnum type : values) {
            int code = type.getCode();

            if(codeList.contains(code)){
                nameList.add(type.name);
            }
        }
        return nameList;
    }

}
