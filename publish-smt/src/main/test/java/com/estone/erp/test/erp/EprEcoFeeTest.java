package com.estone.erp.test.erp;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.elasticsearch.model.EsAliexpressEprEcoFee;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAliexpressEprEcoFeeRequest;
import com.estone.erp.publish.elasticsearch.service.EsAliexpressEprEcoFeeService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.mq.excel.ExcelDispatcher;
import com.estone.erp.publish.smt.mq.excel.ExcelInterface;
import com.estone.erp.publish.smt.mq.excel.bean.ExcelBean;
import com.estone.erp.publish.smt.mq.excel.config.ExcelContext;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月13日/14:07
 * @Description: <p>测试查询并导出erp收费数据</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("dev")
public class EprEcoFeeTest {

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Resource
    private EsAliexpressEprEcoFeeService esAliexpressEprEcoFeeService;

    @Resource
    private RestHighLevelClient restHighLevelClient2;

    @Resource
    private AliexpressConfigService aliexpressConfigService;

    @Test
    public void testESData() throws IOException {
        //查询请求
        SearchRequest searchRequest = new SearchRequest("smt_epr_eco_fees");
        //查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        //复合查询
        sourceBuilder.query(QueryBuilders.boolQuery()
                .filter(QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("destCountryName.keyword", "西班牙"))
                        .must(QueryBuilders.termsQuery("account.keyword", Arrays.asList("<EMAIL>", "<EMAIL>"))))
        );
        searchRequest.source(sourceBuilder);
        SearchResponse search = restHighLevelClient2.search(searchRequest, RequestOptions.DEFAULT);
        System.out.println(JSON.toJSONString(search));
    }

    @ParameterizedTest
    @ValueSource(ints = {0, 1, 2})
    @DisplayName("测试查询所有西班牙的ES数据")
    public void testQueryESDataByCountry(int pageIndex) {
        //一组一组查询
        //查询请求
        //SearchRequest searchRequest = new SearchRequest();
        //查询条件
        //SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        //构建查询语句
        //多值查询-多个账号
        //sourceBuilder.query(QueryBuilders.termsQuery("account.keyword", Arrays.asList("<EMAIL>","<EMAIL>")));
        //等值查询-西班牙
        //sourceBuilder.query(QueryBuilders.termQuery("destCountryName.keyword","西班牙"));
        //复合查询
        //sourceBuilder.query(QueryBuilders.boolQuery()
        //    .filter(QueryBuilders.boolQuery()
        //       .must(QueryBuilders.termQuery("destCountryName.keyword","西班牙"))
        //       .must(QueryBuilders.termsQuery("account.keyword", Arrays.asList("<EMAIL>","<EMAIL>"))))
        //);
        //searchRequest.source(sourceBuilder);
        //查询一个账号五千条，1,5000, 100*20,2000
        EsAliexpressEprEcoFeeRequest request = new EsAliexpressEprEcoFeeRequest();
        request.setDestCountryName("西班牙");
        request.setPageSize(1000);
        request.setPageIndex(pageIndex);
        PageInfo<EsAliexpressEprEcoFee> pageList = esAliexpressEprEcoFeeService.getPageList(request);
        log.info("总记录数：{}，总页数：{}，当前页：{}，分页大小：{}", pageList.getTotal(), pageList.getTotalPages(), pageList.getPageIndex(), pageList.getPageSize());
        //导出

    }


    /**
     * 测试 文件导出功能
     */
    @Test
    @DisplayName("测试收费标准导出功能")
    public void testDownload() {
        ExcelContext context = new ExcelContext();
        EsAliexpressEprEcoFeeRequest request = new EsAliexpressEprEcoFeeRequest();
        //request.setAccount("<EMAIL>");
        request.setAccount("<EMAIL>");
        request.setDestCountryName("西班牙");
        ExcelBean excelBean = new ExcelBean();
        excelBean.setEsAliexpressEprEcoFeeRequest(request);
        excelBean.setExcelLogId(385941);
        excelBean.setType(ExcelTypeEnum.downloadEprEcoFee.getCode());
        excelBean.setCreateBy("182630");
        excelBean.setOperationType("下载收费标准");
        context.setExcelBean(excelBean);
        ExcelInterface call = ExcelDispatcher.getCall(ExcelTypeEnum.downloadEprEcoFee.getCode());
        call.call(context);
    }


    @DisplayName("测试查询所有可用店铺")
    @Test
    public void testSelectAllAccounts() {
        int allAccountsCount = 0;
        AliexpressConfigExample example = new AliexpressConfigExample();
        example.setDistinct(Boolean.TRUE);
        List<AliexpressConfig> list = aliexpressConfigService.selectByExample(example);
        List<List<AliexpressConfig>> partitionList = Lists.partition(list, 200);
        for (List<AliexpressConfig> configList : partitionList) {
            //过滤不可用店铺
            List<String> accountList = configList.stream().filter(t -> t.getUsable().equals(Boolean.TRUE)).map(AliexpressConfig::getAccount).collect(Collectors.toList());
            allAccountsCount += accountList.size();
        }
        log.info("testSelectAllAccounts-查询店铺总数 count:{}", allAccountsCount);
    }


    @ParameterizedTest
    @DisplayName("测试查询某个商品Id下的所有SKUId")
    @ValueSource(strings = {"****************"})
    public void testSelectSKUID(String productId) {
        List<String> productIdList = List.of(productId);
        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setProductIdStr(StringUtils.join(productIdList, ","));
        listingRequest.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "articleNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        Assertions.assertTrue(!CollectionUtils.isEmpty(esAliexpressProductListing));
        Map<String, List<EsAliexpressProductListing>> skuIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getProductId().toString()));
        skuIdMap.forEach((k, v) -> {
            v.forEach(t-> System.out.println("productId:"+k+"|"+"articleNumber:"+t.getArticleNumber()));
        });
    }
}

