package com.estone.erp.test.online;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.call.direct.half.HalfTgListCall;
import com.estone.erp.publish.smt.constant.SmtApiNameConstant;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月20日/17:53
 * @Description: <p>简要描述作用</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("test")
public class HalfTGOnlineStatusTest {

    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;

    @DisplayName("查询半托管在线列表需要更改的产品的在线状态")
    @Test
    public void testHalfTGOnlineStatus() throws InterruptedException {
        //内容变更时间间隔,默认15天
        int dataChangeTime = 15;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        log.info("UpdateSmtHalfTgItemOnlineStatusJobHandler-input param:间隔周期为{}天，筛选时间：{}", dataChangeTime, date);
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        CountDownLatch downLatch = new CountDownLatch(saleAccountList.size());
        for (SaleAccountAndBusinessResponse account : saleAccountList) {
            AliexpressExecutors.updateHalftgOnlineStatus(() -> {
                doUpdateHalfTGOnlineStatusWithNoSelectPlayform(date, account);
                downLatch.countDown();
            });
        }
        downLatch.await();
        log.info("batchUpdateOnlineStatus-output 执行完成");
    }

    /**
     * 二次查询平台再去更新状态
     * @param date
     * @param account
     */
    private void doUpdateHalfTGOnlineStatus(String date, SaleAccountAndBusinessResponse account) {
        log.info("doUpdateHalfTGOnlineStatus-开始执行 店铺：{}", account.getAccountNumber());
        HalfTgListCall tgListCall = new HalfTgListCall();
        HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
        //根据在线状态和最后更新时间,店铺筛选
        AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
        example.createCriteria().andLastSynchTimeLessThan(date)
                .addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode())
                .andAccountEqualTo(account.getAccountNumber());
        //计算总数,分批查询更新
        int total = aliexpressHalfTgItemService.countByExample(example);
        if (total == 0) {
            log.info("doUpdateHalfTGOnlineStatus-暂无需要更新的产品 店铺：{}", account.getAccountNumber());
            return;
        }
        int count = 0;
        int limit = 500;
        int pages = total % limit == 0 ? total / limit : total / limit + 1;
        example.setLimit(limit);
        example.setFields("id,product_id,online_status");
        example.setOrderByClause("id asc");
        Set<Long> realExistProductIdSet = Sets.newHashSet();
        for (int i = 1; i <= pages; i++) {
            //过滤掉不需要更新productId集合
            example.createCriteria().andProductIdNotIn(Lists.newArrayList(realExistProductIdSet));
            List<AliexpressHalfTgItem> itemList = aliexpressHalfTgItemService.selectByExample(example);
            if (CollectionUtils.isEmpty(itemList)) {
                break;
            }
            //存储需要更新在线状态的主键id
            List<Long> idList = Lists.newArrayList();
            //productId去重
            Set<Long> productIdSet = Sets.newHashSet();
            try {
                for (AliexpressHalfTgItem item : itemList) {
                    Long productId = item.getProductId();
                    if (productIdSet.contains(productId)) {
                        if (realExistProductIdSet.contains(productId)) {
                            continue;
                        }
                        idList.add(item.getId());
                        count++;
                        continue;
                    }
                    productIdSet.add(productId);
                    //long start = System.currentTimeMillis();
                    listRequest.setProductId(productId);
                    //TODO 考虑产品更新失败情况,通过产品id再去调用平台接口拿到信息进行判断
                    String existRsp = tgListCall.getListResponse(account, listRequest);
                    // log.info("dration:{}ms,account:{},productId:{},",System.currentTimeMillis()-start,account.getAccountNumber(),productId);
                    //查询商品不存在返回0
                    if (getTotalItem(existRsp) != 0) {
                        //存储特殊情况未被更新的产品id,后面查询过滤掉
                        realExistProductIdSet.add(productId);
                        continue;
                    }
                    idList.add(item.getId());
                    count++;
                }
                if (!CollectionUtils.isEmpty(idList)) {
                    aliexpressHalfTgItemService.batchUpdateOnlineStatus(idList);
                }
            } catch (Exception e) {
                log.error("doUpdateHalfTGOnlineStatus-error errMsg={},e", e.getMessage(), e);
                XxlJobLogger.log("doUpdateHalfTGOnlineStatus-error errMsg={},e", e.getMessage(), e);
            }
        }
        if (count > 0) {
            log.info("UpdateSmtHalfTgItemOnlineStatusJobHandler-output 店铺：{}，在线状态更新数量：{}", account.getAccountNumber(), count);
        }
    }

    /**
     * 不去二次查询平台再去更新状态
     * @param date
     * @param account
     */
    private void doUpdateHalfTGOnlineStatusWithNoSelectPlayform(String date, SaleAccountAndBusinessResponse account) {
        log.info("开始执行 店铺：{}", account.getAccountNumber());
        //根据在线状态和最后更新时间,店铺筛选
        AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
        example.createCriteria().andLastSynchTimeLessThan(date)
                .addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode())
                .andAccountEqualTo(account.getAccountNumber());
        example.setLimit(500);
        example.setFields("id,online_status");
        example.setOrderByClause("id asc");
        int count = 0;
        while (true) {
            try {
                //过滤掉不需要更新productId集合
                List<AliexpressHalfTgItem> itemList = aliexpressHalfTgItemService.selectByExample(example);
                if (CollectionUtils.isEmpty(itemList)) {
                    break;
                }
                //存储需要更新在线状态的主键id
                List<Long> idList = itemList.stream().map(AliexpressHalfTgItem::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(idList)) {
                    aliexpressHalfTgItemService.batchUpdateOnlineStatus(idList);
                    count += idList.size();
                }
            } catch (Exception e) {
                log.error("doUpdateHalfTGOnlineStatus-error errMsg={},e", e.getMessage(), e);
                XxlJobLogger.log("错误信息： errMsg={},e", e.getMessage(), e);
            }
        }
        XxlJobLogger.log("店铺：{}，在线状态更新数量：{}", account.getAccountNumber(), count);
    }


    @DisplayName("测试查询需要更改的产品在线状态，不再调平台接口返回判断")
    @Test
    public void testOnlineStatusWithNoApi() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //内容变更时间间隔,默认7天
        int dataChangeTime = 7;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        log.info("UpdateSmtHalfTgItemOnlineStatusJobHandler-input param:间隔周期为{}天，筛选时间：{}", dataChangeTime, date);
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        for (SaleAccountAndBusinessResponse account : saleAccountList) {
            //根据在线状态和最后更新时间,店铺筛选
            AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
            example.createCriteria().andLastSynchTimeLessThan(date)
                    .addOnlineStatusEqualTo(OnlineStatusEnum.ONLINE.getCode())
                    .andAccountEqualTo(account.getAccountNumber());
            //计算总数,分批查询更新
            int total = aliexpressHalfTgItemService.countByExample(example);
            if (total == 0) {
                continue;
            }
            int limit = 500;
            int pages = total % limit == 0 ? total / limit : total / limit + 1;
            example.setLimit(limit);
            example.setFields("id,product_id,online_status");
            example.setOrderByClause("id asc");
            for (int i = 1; i <= pages; i++) {
                try {
                    //过滤掉不需要更新productId集合
                    List<AliexpressHalfTgItem> itemList = aliexpressHalfTgItemService.selectByExample(example);
                    if (CollectionUtils.isEmpty(itemList)) {
                        break;
                    }
                    //存储需要更新在线状态的主键id
                    List<Long> idList = itemList.stream().map(AliexpressHalfTgItem::getId).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(idList)) {
                        aliexpressHalfTgItemService.batchUpdateOnlineStatus(idList);
                    }
                } catch (Exception e) {
                    log.error("batchUpdateOnlineStatus-error errMsg={},e", e.getMessage(), e);
                    XxlJobLogger.log("batchUpdateOnlineStatus-error errMsg={},e", e.getMessage(), e);
                }
            }
            log.info("batchUpdateOnlineStatus-output 店铺：{}，在线状态更新数量：{}", account.getAccountNumber(), total);
        }
        stopWatch.stop();
        log.info("batchUpdateOnlineStatus-output 执行总耗时：{}s", stopWatch.getTotalTimeSeconds());
    }


    /**
     * <p>
     * 查询不存在返回：通过判断total_item属性值==0
     */
    @Test
    @DisplayName("测试平台查询接口")
    public void testQuery() {
        String accountNumber = "<EMAIL>";
        HalfTgListCall tgListCall = new HalfTgListCall();
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
        HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
        //同步的
        long productId = 1005002752438499L;
        listRequest.setProductId(productId);
        String existRsp = tgListCall.getListResponse(saleAccountAndBusinessResponse, listRequest);
        if (getTotalItem(existRsp) != 0) {
            System.out.println(existRsp);
            log.info("{},通过平台接口查询到产品，不修改在线状态", productId);
        }

        //没有同步的
        productId = 1005006344273002L;
        listRequest.setProductId(productId);
        String noExistRsp = tgListCall.getListResponse(saleAccountAndBusinessResponse, listRequest);
        if (getTotalItem(noExistRsp) == 0) {
            System.out.println(noExistRsp);
            log.info("{},通过平台接口查询不到产品，继续修改在线状态", productId);
        }

    }

    /**
     * 返回相应体中的商品数量；若商品数量为0，则默认平台中没该商品
     *
     * @param response
     * @return
     */
    private int getTotalItem(String response) {
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.containsKey(SmtApiNameConstant.TGHALF_LIST_RESPONSE)) {
                JSONObject rspJson = jsonObject.getJSONObject(SmtApiNameConstant.TGHALF_LIST_RESPONSE);
                if (rspJson.containsKey("result")) {
                    JSONObject obj = rspJson.getJSONObject("result");
                    if (obj.containsKey("total_item")) {
                        return obj.getIntValue("total_item");
                    }
                }
            }
        }
        return 0;
    }
}



