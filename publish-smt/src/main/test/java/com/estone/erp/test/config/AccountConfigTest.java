package com.estone.erp.test.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.system.fmis.FmisClient;
import com.estone.erp.publish.system.fmis.FmisUtils;
import com.estone.erp.publish.system.fmis.model.AccountInfoDTO;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.Ignore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月12日/9:16
 * @Description: <p>测试获取平台的店铺的经营大类字段</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("dev")
public class AccountConfigTest {

    @Resource
    private FmisClient fmisClient;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private AliexpressConfigService aliexpressConfigService;


    /**
     * 测试调用配成系统参数的财务系统的接口查询店铺的经营大类类目
     */
    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>,<EMAIL>"})
    @DisplayName("系统参数查询店铺的经营大类")
    @Ignore
    public void testGetRootCateGoryByAccount(String accounts) {
        try {
            SystemParam systemParam = systemParamService.querySystemParamByCodeKey("Smt.GET_ROOT_CATEGORY");
            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpPost httpPost = new HttpPost(systemParam.getParamValue());
            httpPost.setHeader("Content-Type", "application/json");
            JSONObject json = new JSONObject();
            json.put("accounts", accounts);
            httpPost.setEntity(new StringEntity(json.toString(), StandardCharsets.UTF_8));
            log.info(MessageFormat.format("httpPost:{0}", httpPost));
            HttpResponse response = httpClient.execute(httpPost);
            log.info(MessageFormat.format("response:{0}", response));
            HttpEntity entity = response.getEntity();
            String responseJson = null;
            String res = EntityUtils.toString(entity);
            if (response.getStatusLine().getStatusCode() == 200) {
                responseJson = JSON.parseObject(res).getString("result");
                //List list = JSON.parseObject(responseJson, List.class);
                log.info(responseJson);
            }
            log.info("exec success!");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 测试feign调用财务系统的接口查询店铺的经营大类类目
     * <p>同一个店铺多个经营大类通过逗号分隔合并,经营大类状态仅显示成功开通和审核通过两种状态</p>
     * <P>测试四种情况：1）仅审核通过;2)仅成功开通;3)审核通过和成功开通度存在;4)不存在审核通过和成功开通状态</P>
     */
    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"})
    @DisplayName("feign查询店铺的经营大类")
    public void testFmisClient(String accounts) {
        ApiResult<List<AccountInfoDTO>> result = fmisClient.listByAccount(accounts);
        if (result.isSuccess() && !CollectionUtils.isEmpty(result.getResult())) {
            List<AccountInfoDTO> list = result.getResult();
            list.forEach(System.out::println);
            Map<String, String> categoryMap = FmisUtils.getCategoryMap(list, ",");
            categoryMap.forEach((k, v) -> log.info("{}:{}", k, v));
        }
    }


    /**
     * 测试feign调用财务系统的接口查询店铺的经营大类类目
     * <p>同一个店铺多个经营大类通过逗号分隔合并</p>
     * 测试验证接口执行时间：100   三次平均450ms左右
     * 200  平均350ms左右
     * 250   平均260ms左右
     * 300 报错
     */
    @RepeatedTest(3)
    @DisplayName("批量查询feign查询店铺的经营大类")
    public void testBatchFmisClient() {
        //查询所有的店铺信息
        AliexpressConfigExample example = new AliexpressConfigExample();
        example.setDistinct(Boolean.TRUE);
        List<AliexpressConfig> configList = aliexpressConfigService.selectByExample(example);
        List<String> accountList = configList.stream().filter(t -> t.getUsable().equals(Boolean.TRUE)).map(AliexpressConfig::getAccount).collect(Collectors.toList());
        //将查询出来的店铺列表分片
        List<List<String>> partition = Lists.partition(accountList, 250);
        Map<String, String> categoryMap = Maps.newHashMap();
        StopWatch watch = new StopWatch();
        watch.start();
        for (List<String> list : partition) {
            String accounts = StringUtils.join(list, ",");
            log.info("accounts：size:{},length:{},{}", list.size(), accounts.length(), accounts);
            ApiResult<List<AccountInfoDTO>> result = fmisClient.listByAccount(accounts);
            if (result.isSuccess()) {
                Assertions.assertTrue(CollectionUtils.isNotEmpty(result.getResult()));
                categoryMap.putAll(FmisUtils.getCategoryMap(result.getResult(), ","));
                //categoryMap.forEach((k, v) -> log.info("{}:{}", k, v));
            }
        }
        watch.stop();
        log.info("调用平台接口耗时：{}ms,查询店铺数量统计:{},接口返回有数据的店铺数量:{}", watch.getTotalTimeMillis(), accountList.size(), categoryMap.size());
        categoryMap.forEach((k, v) -> log.info("{}:{}", k, v));
    }

}
