package com.estone.erp.test.online;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.call.direct.SynchItemOpenCall;
import com.estone.erp.publish.smt.model.AliexpressProductCriteria;
import com.estone.erp.publish.smt.mq.publish.listing.SmtSynchAllItemBean;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月21日/15:19
 * @Description: <p>测试在线列表更新状态</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("test")
public class SmtProductListingOnlineStatusTest {

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    /**
     * 逻辑：
     * 1.通过店铺维度去查询,加上条件：最后同步时间和在线状态去查询一批符合条件的产品数据；
     * 2.分页遍历各个店铺的各个产品再逐个进行更新；
     * 3.日志打印：结果放在任务调度中心日志中，详细放在普通日志中
     */
    @Test
    @DisplayName("更新在线列表在线状态")
    public void testUpdateOnlineStatus() throws InterruptedException {
        //内容变更时间间隔,默认15天
        Integer dataChangeTime = 15;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        log.info("UpdateSmtListingOnlineStatusJobHandler-input param:间隔周期为{}天，筛选时间：{}", dataChangeTime, date);
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        CountDownLatch downLatch = new CountDownLatch(saleAccountList.size());
        for (SaleAccountAndBusinessResponse account : saleAccountList) {
            //多线程去执行
            AliexpressExecutors.updateProductListingOnlineStatus(() -> {
                doUpdateListingOnlineStatus(date, account);
                downLatch.countDown();
            });
        }
        downLatch.await();
        log.info("UpdateSmtListingOnlineStatusJobHandler-output 执行完成");
    }

    private void doUpdateListingOnlineStatus(String date, SaleAccountAndBusinessResponse account) {
        log.info("doUpdateListingOnlineStatus-开始执行 店铺：{}", account.getAccountNumber());
        SynchItemOpenCall synchItemOpenCall = new SynchItemOpenCall();
        int pageSize = 1000;
        int pageIndex = 0;
        //查询某个店铺产品id的最后更新时间小于指定时间且在线状态的数据
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setToLastSynchDate(date);
        request.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
        request.setAliexpressAccountNumber(account.getAccountNumber());
        request.setPageFields(new String[]{"id", "productId", "onlineStatus"});
        Page<EsAliexpressProductListing> pageRsp = esAliexpressProductListingService.page(request, pageSize, pageIndex);
        int totalPages = pageRsp.getTotalPages();
        if (totalPages == 0) {
            log.info("doUpdateListingOnlineStatus-暂无需要更新的产品 店铺：{}", account.getAccountNumber());
            return;
        }
        //存储不需要更新的产品id
        Set<Long> realExistProductIdSet = Sets.newHashSet();
        int count = 0;
        for (int i = 0; i < totalPages; i++) {
            //过滤掉不需要更新的产品id
            request.setNotInProductIdList(Lists.newArrayList(realExistProductIdSet));
            Page<EsAliexpressProductListing> productRsp = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            //productId去重,防止一个productId多次调用
            Set<Long> productIdSet = Sets.newHashSet();
            for (EsAliexpressProductListing product : productRsp) {
                Long productId = product.getProductId();
                try {
                    if (productIdSet.contains(productId)) {
                        if (realExistProductIdSet.contains(productId)) {
                            continue;
                        }
                        //更新在线状态
                        updateOnlineStatus(product);
                        count++;
                        continue;
                    }
                    productIdSet.add(productId);
                    //TODO 考虑产品更新失败情况,通过产品id再去调用平台接口拿到信息进行判断
                    String response = synchItemOpenCall.getProduct(account, productId);
                    if (!isNotExistProductId(response)) {
                        //存储特殊情况未被更新的产品id,后面过滤掉
                        realExistProductIdSet.add(productId);
                        continue;
                    }
                    //更新在线状态
                    updateOnlineStatus(product);
                    count++;
                } catch (Exception e) {
                    log.error("doUpdateListingOnlineStatus-error errMsg={},e", e.getMessage(), e);
                    XxlJobLogger.log("doUpdateListingOnlineStatus-error errMsg={},e", e.getMessage(), e);
                }
            }

        }
        if (count > 0) {
            log.info("UpdateSmtListingOnlineStatusJobHandler-run 店铺：{},在线状态更新数量:{}", account.getAccountNumber(), count);
        }
    }

    /**
     * 更新产品在线状态
     *
     * @param product
     */
    private void updateOnlineStatus(EsAliexpressProductListing product) {
        product.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
        product.setProductId(null);
        esAliexpressProductListingService.updateRequest(product);
    }

    @Test
    @DisplayName("测试api接口调用")
    public void testSmtApiWithfindaeproductbyid() {
        try {
            String accountNumber = "<EMAIL>";
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
            //通过店铺维度去查询
            //TODO 考虑产品更新失败情况,通过产品id再去调用平台接口拿到信息进行判断
            //存在商品
            long productId = 1005006464761390L;
            SynchItemOpenCall synchItemOpenCall = new SynchItemOpenCall();
            String existResponse = synchItemOpenCall.getProduct(account, productId);
            if (isNotExistProductId(existResponse)) {
                log.info("{},通过平台接口查询不到产品，继续修改在线状态", productId);
            } else {
                log.info("{},通过平台接口查询到产品，不修改在线状态", productId);
            }
            System.out.println(existResponse);
            //不存在，不是随便请求一个不存在的productId
            //productId = 920408371L;
            productId = 1005005950363876L;
            String noExistResponse = synchItemOpenCall.getProduct(account, productId);
            if (isNotExistProductId(noExistResponse)) {
                log.info("{},通过平台接口查询不到产品，继续修改在线状态", productId);
            } else {
                log.info("{},通过平台接口查询到产品，不修改在线状态", productId);
            }
            System.out.println(noExistResponse);
        } catch (Exception e) {
            log.error("错误信息-error errMsg:{}", e.getMessage(), e);
        }
    }

    /**
     * 判断平台是否存在该产品id
     *
     * @param response
     * @return
     */
    private boolean isNotExistProductId(String response) {
        return StringUtils.containsIgnoreCase(response, "product is not exist")
                || StringUtils.containsIgnoreCase(response, "Product not found");
    }


    /**
     * 修改测试环境的店铺产品id的在线状态数据
     */
    @Test
    @DisplayName("将测试环境的在线列表的不在线状态改成在线状态便于测试")
    public void testUpdateOnlineListing() {
        //内容变更时间间隔,默认15天
        Integer dataChangeTime = 15;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        log.info("UpdateSmtListingOnlineStatusJobHandler-input param:间隔周期为{}天，筛选时间：{}", dataChangeTime, date);
        SynchItemOpenCall synchItemOpenCall = new SynchItemOpenCall();
        int pageSize = 1000;
        int pageIndex = 0;
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        SaleAccountAndBusinessResponse account = saleAccountList.stream().filter(s -> "<EMAIL>".equals(s.getAccountNumber())).findFirst().get();
        // for (SaleAccountAndBusinessResponse account : saleAccountList) {
        //查询某个店铺产品id的最后更新时间小于指定时间且在线状态的数据
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setToLastSynchDate(date);
        //不在线
        request.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
        request.setAliexpressAccountNumber(account.getAccountNumber());
        request.setPageFields(new String[]{"id", "productId", "onlineStatus"});
        request.setProductId(1005006464761390L);
        Page<EsAliexpressProductListing> productRsp = esAliexpressProductListingService.page(request, pageSize, pageIndex);
        for (EsAliexpressProductListing product : productRsp) {
            try {
                //在线
                product.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                esAliexpressProductListingService.updateRequest(product);
            } catch (Exception e) {
                log.error("updateRequest-error errMsg={},e", e.getMessage(), e);
                XxlJobLogger.log("updateRequest-error errMsg={},e", e.getMessage(), e);
            }
        }
        log.info("UpdateSmtListingOnlineStatusJobHandler-output 执行完成");
    }

    /**
     * 测试利用管道设置默认值后，测试修改单个产品在线状态
     * 结论：利用管道设置默认值后，发现修改属性值失败
     */
    @Test
    public void testSingleUpdateListingOnlineStatus() {
        //内容变更时间间隔,默认15天
        Integer dataChangeTime = 15;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        log.info("UpdateSmtListingOnlineStatusJobHandler-input param:间隔周期为{}天，筛选时间：{}", dataChangeTime, date);
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        SaleAccountAndBusinessResponse account = saleAccountList.stream().filter(t -> "<EMAIL>".equals(t.getAccountNumber())).findFirst().get();
        doUpdateListingOnlineStatus(date, account);
        log.info("UpdateSmtListingOnlineStatusJobHandler-output 执行完成");
    }

    @DisplayName("用单个店铺帐号模拟测试smt同步全量在线列表通过消息队列处理")
    @Test
    public void testSmtSynchAllItemSend() throws InterruptedException {
        //单个模拟测试
        AliexpressProductCriteria syncProductCriteriaAll = new AliexpressProductCriteria();
        syncProductCriteriaAll.setProductStatusType("onSelling,offline,auditing,editingRequired");
        syncProductCriteriaAll.setIsSynchAll(true);
        SmtSynchAllItemBean smtSynchAllItemBean = new SmtSynchAllItemBean();
        smtSynchAllItemBean.setAliexpressProductCriteria(syncProductCriteriaAll);
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = saleAccountList.stream().filter(t -> "<EMAIL>".equals(t.getAccountNumber())).findFirst().get();
        //多线程执行改成单线程消息队列处理
       // for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            smtSynchAllItemBean.setSaleAccountAndBusinessResponse(saleAccountAndBusinessResponse);
            try {
                rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE
                        , PublishQueues.SMT_SYNCH_ALL_ITEM_KEY, JSON.toJSON(smtSynchAllItemBean));
            } catch (Exception e) {
                //发送队列失败
                log.info("发送队列失败：店铺：{},errMsg:{}", saleAccountAndBusinessResponse.getAccountNumber(), e.getMessage(), e);
            }
       // }
        Thread.sleep(100000L);
    }

    /**
     * 测试将之前平台查询不到的删除改成不在线状态,场景：在在线列表勾选某些产品进行产品同步，
     */
    @Test
    public void testSyncAliexpressProductInfo(){
        SynchItemOpenCall call = new SynchItemOpenCall();
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = saleAccountList.stream().filter(t -> "<EMAIL>".equals(t.getAccountNumber())).findFirst().get();
        long productId = 1005006039607876L;
        call.syncAliexpressProductInfo(saleAccountAndBusinessResponse, productId);
        //测试查看状态是否变更
    }

    /**
     * 测试不去调用平台接口二次查询
     */
    @Test
    public void testDirectUpdateOnlineStatus(){
        //通过店铺维度去查询
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = saleAccountList.stream().filter(t -> "<EMAIL>".equals(t.getAccountNumber())).findFirst().get();
        //内容变更时间间隔,默认15天
        Integer dataChangeTime = 15;
        //位运算取反
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        testUpdateListingOnlineStatus(date,saleAccountAndBusinessResponse);
    }

    public void testUpdateListingOnlineStatus(String date, SaleAccountAndBusinessResponse account) {
        log.info("doUpdateListingOnlineStatus- 店铺：{} 开始执行", account.getAccountNumber());
        int pageSize = 500;
        int pageIndex = 0;
        int count = 0;
        //查询某个店铺产品id的最后更新时间小于指定时间且在线状态的数据
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setToLastSynchDate(date);
        request.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
        request.setAliexpressAccountNumber(account.getAccountNumber());
        request.setPageFields(new String[]{"id","onlineStatus"});
        request.setOrderBy("lastSyncTime");
        request.setSequence("ASC");
        while (true) {
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            List<EsAliexpressProductListing> productListingList = page.getContent();
            for (EsAliexpressProductListing product : productListingList) {
                try {
                    //更新在线状态
                    product.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
                    esAliexpressProductListingService.updateRequest(product);
                    count++;
                } catch (Exception e) {
                    log.error("doUpdateListingOnlineStatus-更新在线状态失败 account:{},id:{}, errMsg={},e", account.getAccountNumber(), product.getId(), e.getMessage(), e);
                    XxlJobLogger.log("更新在线状态失败 account:{},id:{}, errMsg={},e", account.getAccountNumber(), product.getId(), e.getMessage(), e);
                }
            }
        }
       log.info("店铺：{},更新在线状态数量:{}", account.getAccountNumber(), count);
    }



}



