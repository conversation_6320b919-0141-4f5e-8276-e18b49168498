package com.estone.erp.test;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.UploadVideoNewCall;
import com.estone.erp.publish.smt.call.direct.tg.TgItemQueryCall;
import com.estone.erp.publish.smt.call.direct.tg.TgJitStockRuleCall;
import com.estone.erp.publish.smt.call.direct.tg.TgSynchItemOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferEditProductOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferQueryProductOpenCall;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductCriteria;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.smt.service.AliexpressProductService;
import com.estone.erp.publish.smt.service.AliexpressTemplateService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("dev")
public class PublishTest {

    @Autowired
    private AliexpressTemplateService aliexpressTemplateService;

    @Autowired
    private AliexpressProductService aliexpressProductService;

    @Autowired
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Autowired
    private AliexpressProductLogService aliexpressProductLogService;

    @Autowired
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;


//    @Test
//    public void test11() {
//        String account = "<EMAIL>";
//        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
//                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
//
//
//        Date start = new Date(System.currentTimeMillis() - 1000l * 60 * 840);
//        Date end = new Date();
//        HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
//        listRequest.setGmtModifiedStart(DateUtils.format(start, DateUtils.STANDARD_DATE_PATTERN));
//        listRequest.setGmtModifiedEnd(DateUtils.format(end, DateUtils.STANDARD_DATE_PATTERN));
//
//        AliexpressProductLog log = new AliexpressProductLog();
//        log.setAccountNumber(account);
//        log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
//        log.setOperateType(OperateLogTypeEnum.synch_half_tg_item.getCode());
//        log.setOperator("admin-modified");
//        aliexpressProductLogService.insert(log);
//        aliexpressHalfTgItemService.synchItem(saleAccountByAccountNumber, listRequest, log.getId());
//
//        try {
//            Thread.sleep(1000 * 60 * 60);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        System.out.println("xxxx");
//    }

    @Test
    public void test() {
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);

        Long productId = 1005007472487957L;
//        HalfTgDraftQueryCall openCall = new HalfTgDraftQueryCall();
//        ResponseJson responseJson = openCall.draftQuery(saleAccountByAccountNumber, productId.toString());
//        System.out.println(responseJson.getMessage());

        OfferQueryProductOpenCall offerQueryProductOpenCall = new OfferQueryProductOpenCall();
        offerQueryProductOpenCall.getProduct(saleAccountByAccountNumber, productId);

//        GetMerchantOpenCall getMerchantOpenCall = new GetMerchantOpenCall();
//        getMerchantOpenCall.getMerchant(saleAccountByAccountNumber);

//        AliexpressProductCriteria syncProductCriteria = new AliexpressProductCriteria();
//        //同步删除的产品
//        syncProductCriteria.setProductStatusType("deleted");
//
//        SynchItemOpenCall call = new SynchItemOpenCall();
//        call.syncAliexpressProductList(saleAccountByAccountNumber,syncProductCriteria);

//        SynchItemOpenCall openCall = new SynchItemOpenCall();
//        String product = openCall.getProduct(saleAccountByAccountNumber, 1005002563791820L);
//        System.out.println(product);


//        TgSynchItemOpenCall tgSynchItemOpenCall = new TgSynchItemOpenCall();
//        TgSyncProductListRequest request = new TgSyncProductListRequest();
//        request.setProductStatusType(TgItemStatusEnum.ONLINE.getCode());
//        String productList = tgSynchItemOpenCall.getProductList(saleAccountByAccountNumber, request);

        try {
            Thread.sleep(1000 * 60 * 60);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("xxxx");
    }


    @Test
    public void synchTgProduct() {
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        TgSynchItemOpenCall tgSynchItemOpenCall = new TgSynchItemOpenCall();
        tgSynchItemOpenCall.synchTgProductList(saleAccountByAccountNumber, null);

        try {
            Thread.sleep(1000 * 60 * 60);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    /**
     * {"aliexpress_ascp_item_query_response":{"result":{"data_list":{"data":[{"item_code":"9FF601146","length":100,"customs_unit_price":"6.67","weight":45,"is_hygroscopic":"false","title":"（1盒）唱片磁针 附着力配件磁针","whc_bar_code":"************","relation_list":{"relation":[{"bind_status":"2","item_id":"****************","sku_id":"*****************"}]},"is_origion":"false","category_id":0,"width":50,"extend_fields":"{\"Blength\":\"100\",\"Bweight\":\"33\",\"Bheight\":\"31\",\"Bwidth\":\"91\"}","height":50,"sc_item_id":************}]},"success":true,"total_count":1,"page_index":1,"page_size":20},"request_id":"2140fa3516952002439472359"}}
     */
    @Test
    public void synchTgProduct1() {
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        //************
        TgItemQueryCall itemQueryCall = new TgItemQueryCall();
        itemQueryCall.itemQueryBarCode(saleAccountByAccountNumber, "4NB907537");
        try {
            Thread.sleep(1000 * 60 * 60);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void synchTgProduct2() {
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        //************
        TgJitStockRuleCall stockRuleCall = new TgJitStockRuleCall();
        ResponseJson responseJson = stockRuleCall.jitStockRuleQuery(saleAccountByAccountNumber, "*********");
        System.out.println("111");
        try {
            Thread.sleep(1000 * 60 * 60);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void uploadbyurl() {
        /**
         * {"global_mediacenter_video_uploadbyurl_response":{"result":{"data":{"transfer_status":2,"biz_id":"****************"},"success":true},"request_id":"2102e22d16885227009953731"}}
         */

        /**
         * {"global_mediacenter_video_uploadbyurl_response":{"result":{"data":{"transfer_status":2,"biz_id":"****************"},"success":true},"request_id":"212a69f316889533525315204"}}
         */
        String account = "<EMAIL>";
        Long productId = ****************L;
        String url = "http://10.100.1.200:8888/other/2023-07/05-09-59-54-028/2SS317233.mp4";
        url = "http://image.eston.top:24980/fms/other/2023-07/05-09-59-54-028/2SS317233.mp4";
        System.out.println(url);
        String spu = "5AC605672";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        UploadVideoNewCall videoNewCall = new UploadVideoNewCall();
        videoNewCall.uploadVideoNew(saleAccountByAccountNumber, productId.toString(), url, spu);
    }

    @Test
    public void uploadPage() {
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        UploadVideoNewCall videoNewCall = new UploadVideoNewCall();
        videoNewCall.listPage(saleAccountByAccountNumber);
    }

    @Test
    public void testBrand() {
        String account = "<EMAIL>";
        String categoryId = "*********";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        CategoryOpenCall call = new CategoryOpenCall();
        String categoryAttributes = call.getCategoryAttributes(saleAccountByAccountNumber, categoryId);
        System.out.println(categoryAttributes);
    }

    @Test
    public void synchProduct() {

        String account = "<EMAIL>";
        Long productId = 1005007643048540L;

        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
//        SynchItemOpenCall call = new SynchItemOpenCall();
//        AliexpressProduct aliexpressProduct = new AliexpressProduct();
//        aliexpressProduct.setProductId(productId);
//        String s = call.getProduct(saleAccountByAccountNumber, productId);
//        System.out.println("V1:" + s);
//        SynchItemOpenCall call = new SynchItemOpenCall();
//        SyncProductListRequest syncProductListRequest = new SyncProductListRequest();
//        syncProductListRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
//        syncProductListRequest.setProductId(productId.toString());
//        syncProductListRequest.setCurrentPage(1);
//        String rspStr = call.getProductList(saleAccountByAccountNumber,syncProductListRequest);

        OfferQueryProductOpenCall openCall = new OfferQueryProductOpenCall();
        String product = openCall.getProduct(saleAccountByAccountNumber, productId);
        System.out.println("V2:" + product);

//        TgSynchItemOpenCall tgSynchItemOpenCall = new TgSynchItemOpenCall();
//
//        Long productId = 1005005098747522L;
//        String product = tgSynchItemOpenCall.getProduct(saleAccountByAccountNumber, productId);
//        System.out.println("rspStr:" + rspStr);

//
//        Long productId = 1005005126823387L;
//        AliexpressStatePriceUtils.calePriceAndUpload(saleAccountByAccountNumber, productId);
//        GetMerchantOpenCall openCall = new GetMerchantOpenCall();
//        openCall.getMerchant(saleAccountByAccountNumber);

//        CategoryOpenCall categoryOpenCall = new CategoryOpenCall();
//        categoryOpenCall.checkAuth(saleAccountByAccountNumber, "*********");

//        QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
//        qualificationsOpenCall.qualifications(saleAccountByAccountNumber, *********);

//        EuResponsibleOpenCall euResponsibleOpenCall = new EuResponsibleOpenCall();
//        ResponseJson responseJson = euResponsibleOpenCall.euResponsible(saleAccountByAccountNumber, *********, true);
//        Long productId1 = 1005005161250308L;
//        String product1 = openCall.getProduct(saleAccountByAccountNumber, productId1);
//        System.out.println("V2:" + product1);
//
//        System.out.println("xxxx");
    }

    @Test
    public void synchOffProduct() {

        String account = "<EMAIL>";
        Long productId = 1005004984348278L;

        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);

        OfferQueryProductOpenCall call = new OfferQueryProductOpenCall();

        //        String product = call.getProduct(saleAccountByAccountNumber, productId);

        JSONObject jsonObject = OfferQueryProductOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, productId);

        System.out.println("xxxx");
    }

//    @Test public void spuPublish() {
//        try {
//            AliexpressSpuToTempHelper helper = new AliexpressSpuToTempHelper();
//            SpuPublishRequest request = new SpuPublishRequest();
//            request.setAccount("<EMAIL>");
//            request.setSpu("11JJ101077");
//            helper.generateAutoPublishTemplate(request);
//        }
//        catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Test
    public void updateStock() {
        String accounts = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
        List<String> strings = CommonUtils.splitList(accounts, ",");

        for (String account : strings) {
            AliexpressProductCriteria query = new AliexpressProductCriteria();

            List<Integer> skuStatusList = CommonUtils.splitIntList("4", ",");
            if (CollectionUtils.isNotEmpty(skuStatusList)) {
                query.setSkuStatusStr(StringUtils.join(skuStatusList, ","));
            }

            query.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            query.setAliexpressAccountNumber(account);

//            List<AliexpressProduct> aliexpressProducts = customAliexpressProductMapper.serachSmtProductList(query.getExample());
//
//            if (CollectionUtils.isNotEmpty(aliexpressProducts)) {
//                System.out.println(account);
//
//                List<Integer> editStockIds = aliexpressProducts.stream().map(t -> t.getId()).collect(Collectors.toList());
//                aliexpressProductService.batchEditProductStock(editStockIds, "9999");
//                //                aliexpressProductService.syncProductByIds(editStockIds);
//            }
        }

        System.out.println("end");
    }

    @Test
    public void redisTest() throws Exception {

        PublishRedisClusterUtils
                .sAddExpire(RedisConstant.AMAZON_ACCOUNT_CATEGORY + "8888", 1, TimeUnit.MINUTES, "<EMAIL>");

        Set<String> accountList = new HashSet<>();
        String key = "Publish:Amazon:Account:Category:%s";
        List<Integer> codelist = Arrays.asList(8888);
        for (Integer code : codelist) {

            Set<String> smembers = PublishRedisClusterUtils.sMembers(String.format(key, code), String.class);
            //            if(smembers.contains("US-gaoshangcold") || smembers.contains("US-wuINbbyy") ||smembers.contains("UK-jieufe547ft"))
            System.out.println(String.format("code:%s -> account:%s", code, smembers));
            accountList.addAll(smembers);
        }

        Thread.sleep(1000 * 60);

        for (Integer code : codelist) {

            Set<String> smembers = PublishRedisClusterUtils.sMembers(String.format(key, code), String.class);
            //            if(smembers.contains("US-gaoshangcold") || smembers.contains("US-wuINbbyy") ||smembers.contains("UK-jieufe547ft"))
            System.out.println(String.format("code:%s -> account:%s", code, smembers));
            accountList.addAll(smembers);
        }
    }

    @Test
    public void aa() {

        List<SaleAccountAndBusinessResponse> accountList = AccountUtils
                .getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);

        int i = 0;
        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : accountList) {
            i++;
            String key = "synch_account_" + saleAccountAndBusinessResponse.getAccountNumber();
            String s = PublishRedisClusterUtils.get(key);
            System.out.println(i + ":" + s);
            PublishRedisClusterUtils.del(key);
            System.out.println(accountList.size() + ":" + i + ":" + PublishRedisClusterUtils.get(key));
        }

    }

    @Test
    public void bb() {
        String id = "<EMAIL>-****************-14:173;5:361386";

//        EsAliexpressProductListing allById = esAliexpressProductListingService.findAllById(id);
//        Long productId = allById.getProductId();

        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setId(id);
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);

        for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {

        }
    }


    @Test
    public void testJson() {
        Long productId = 1005003196454902L;
        String account = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        JSONObject jsonObject = OfferQueryProductOpenCall
                .transResultToOfferUpdate(saleAccountByAccountNumber,
                        productId);
        ResponseJson rsp = OfferEditProductOpenCall
                .offerEditProduct(saleAccountByAccountNumber, jsonObject.toJSONString());
        if (rsp.isSuccess()) {

        }
    }

    @Test
    public void test1() {
        List<String> strings = Arrays.asList("http://172.16.10.51:8888/public/2023-02/09-17-49-23-965/4NB906588.jpg");
        ResponseJson responseJson = FmsUtils.mosaicCheckImage(strings, SaleChannelEnum.ALIEXPRESS.getChannelName());
        System.out.println("xxx");
    }

    @Test
    public void test2() {
        ResponseJson rsp = new ResponseJson();
        Object urlObj = rsp.getBody().get("url");
        rsp.setMessage("包含马赛克图片:" + urlObj != null ? urlObj.toString() : "");
        System.out.println("xx");
    }
}

