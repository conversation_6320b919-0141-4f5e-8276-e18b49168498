package com.estone.erp.test;

import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductMonitor;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductMonitorRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductMonitorService;
import com.estone.erp.publish.platform.model.ItemMonitorConfig;
import com.estone.erp.publish.platform.model.ItemMonitorConfigExample;
import com.estone.erp.publish.platform.service.ItemMonitorConfigService;
import com.estone.erp.publish.smt.bean.AliexpressProductOperateLogType;
import com.estone.erp.publish.smt.componet.AliexpressProductMonitorHelper;
import com.estone.erp.publish.smt.componet.SmtItemEsBulkProcessor;
import com.estone.erp.publish.smt.jobHandler.AliexpressProductMonitorHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022-07-12 15:20
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("dev")
public class ItemMonitorTest {

    @Autowired
    private EsAliexpressProductListingService productListingService;
    @Autowired
    private EsAliexpressProductMonitorService monitorService;
    @Autowired
    private ItemMonitorConfigService monitorConfigService;
    @Autowired
    private AliexpressProductMonitorHelper monitorHelper;
    @Autowired
    private AliexpressProductMonitorHandler monitorHandler;
    @Resource
    private SmtItemEsBulkProcessor smtItemEsBulkProcessor;


    @Test
    @SneakyThrows
    public void resetPvTest() {
        EsAliexpressProductListing listing = new EsAliexpressProductListing();
        listing.setId("<EMAIL>-1005007359049011-14:200006151");
//        listing.setView_7d_count(0);
//        listing.setExposure_7d_count(0);
        listing.setView_14d_count(0);
        listing.setExposure_14d_count(0);
        listing.setView_30d_count(0);
        listing.setExposure_30d_count(0);
        listing.setLastPVUpdateDate(new Date());

        smtItemEsBulkProcessor.updateListingPv(List.of(listing));
        TimeUnit.SECONDS.sleep(60);
    }


    @Test
    public void syncItemToMonitor() {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType("offline");
        Page<EsAliexpressProductListing> page = productListingService.page(request, 500, 0);
        ItemMonitorConfigExample example = new ItemMonitorConfigExample();
        ItemMonitorConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformEqualTo("SMT");
        List<ItemMonitorConfig> itemMonitors = monitorConfigService.selectByExample(example);
        AtomicInteger a = new AtomicInteger();
        page.getContent().forEach(item -> {
            EsAliexpressProductMonitor esAliexpressProductMonitor = new EsAliexpressProductMonitor();
            BeanUtils.copyProperties(item, esAliexpressProductMonitor);
            int index = a.get() % itemMonitors.size();
            a.getAndIncrement();

            ItemMonitorConfig itemMonitorConfig = itemMonitors.get(index);
            esAliexpressProductMonitor.setMonitorId(itemMonitorConfig.getId());
            esAliexpressProductMonitor.setMonitorType(itemMonitorConfig.getMonitorType());
            esAliexpressProductMonitor.setMonitorContent(itemMonitorConfig.getContent());
            esAliexpressProductMonitor.setOfflineCreatedAt(new Date());
            log.info(esAliexpressProductMonitor.getId());
            monitorService.save(esAliexpressProductMonitor);
        });
    }

    @Test
    public void clearMonitor() {
        EsAliexpressProductMonitorRequest request = new EsAliexpressProductMonitorRequest();
        request.setQueryFields(new String[]{"id"});

        List<EsAliexpressProductMonitor> productMonitors = monitorService.listMonitor(request);
        for (EsAliexpressProductMonitor productMonitor : productMonitors) {
            monitorService.deleteById(productMonitor.getId());
        }
    }

    @Test
    public void addToOffline() {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType("offline");
        Page<EsAliexpressProductListing> page = productListingService.page(request, 500, 0);
        ItemMonitorConfigExample example = new ItemMonitorConfigExample();
        ItemMonitorConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformEqualTo("SMT");
        List<EsAliexpressProductListing> content = page.getContent();
        monitorHelper.listingItemStatusListening(AliexpressProductOperateLogType.OFFLINE, content);
    }

    @Test
    public void toMonitor() throws Exception {
        clearMonitor();
        monitorHandler.run("{\"accountNumberList\":[]}");
    }
}
