package com.estone.erp.test.product;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemAllSupplier;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月11日/19:23
 * @Description: <p>简要描述作用</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("test")
public class ItemProductTest {

    @Resource
    private SingleItemEsService singleItemEsService;
    
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    /**
     * 在线列表中的部分信息从产品信息中获取的，
     * 产品信息中的单品管理只有spu和子sku查询
     */
    @DisplayName("通过平台系统的单品ES查询对应的采购运费")
    @Test
    public void testGetShippingCost(){
        String accountNumber = "<EMAIL>";
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT,accountNumber );
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setAliexpressAccountNumber(accountNumber);
        List<EsAliexpressProductListing> esAliexpressProductListingList = esAliexpressProductListingService.getEsAliexpressProductListing(request);
        for(EsAliexpressProductListing listing:esAliexpressProductListingList){
            String articleNumber = listing.getArticleNumber();
            SingleItemEs skuInfo = singleItemEsService.getSkuInfo(articleNumber);
            if(!Objects.isNull(skuInfo)&& !Objects.isNull(skuInfo.getSingleItemAllSupplier())){
                SingleItemAllSupplier singleItemAllSupplier = skuInfo.getSingleItemAllSupplier();
                log.info(JSON.toJSONString(skuInfo));
                log.info("单品货号：{}，采购运费:{}",articleNumber,singleItemAllSupplier.getShippingCost());
            }
        }
        String skuPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();
        log.info("accouontNumber:{},skuPrefix:{}",accountNumber,skuPrefix);
    }
}
