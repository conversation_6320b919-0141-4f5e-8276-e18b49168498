package com.estone.erp.test.aliance;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.common.enums.AliexpressAlianceStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressAlianceProductListing;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressAlianceProductListingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月11日/14:19
 * @Description: <p>简要描述作用</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PublishSmtApplication.class)
@ActiveProfiles("test")
public class AlianceListingTest {

    @Resource
    private EsAliexpressAlianceProductListingService esAliexpressAlianceProductListingService;

    /**
     *  jackson -ObjectMapper +@JsonFormat
     *  fastjson - JSON +@JSONField
     *  由于fastjson内部会将date类型转成Long类型的时间戳，所以直接存储到ES中时间格式为（yyyy-mm-dd HH:mm:ss）会报错，
     *  需要使用fastjson的字段注解进行格式转换,如果没有字段没有进行格式转换，而直接进行更新或者添加会报错；
     *  当前错误是由于使用了jackson的字段格式注解@JsonFormat和fastjson的JSON进行混搭，导致时间更新失败
     */
    @DisplayName("测试验证使用日期类型去更新出错失败")
    @Test
    public void testUpdateESFieldDateByDate(){
        EsAliexpressAlianceProductListing alianceProductListing = new EsAliexpressAlianceProductListing();
        alianceProductListing.setId("<EMAIL>-1005005776342458");
        alianceProductListing.setWaitRemoveAlianceTime(new Date());
        alianceProductListing.setAllianceStatus(AliexpressAlianceStatusEnum.WAIT_REMOVING.getCode());
        esAliexpressAlianceProductListingService.updateRequest(alianceProductListing);
    }

    /**
     * 测试验证直接使用日期字符串更新:结论是可以的
     * fastjson JSON + jackson @JsonFormat
     */
    @DisplayName("测试使用字符串更新日期成功")
    @Test
    public void testUpdateESFieldDateByDateStr(){
        String id = "<EMAIL>-1005006507958960";
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("createTime", DateUtils.getCurrentTimeStr(null));
        String json = JSON.toJSONString(updateMap);
        esAliexpressAlianceProductListingService.updateRequest(json,id);
    }
}
