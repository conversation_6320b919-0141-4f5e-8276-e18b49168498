import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.PublishSmtApplication;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.componet.SmtInfringementForbiddenSaleHelper;
import com.estone.erp.publish.system.product.ProhibitionDO;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishSmtApplication.class, properties = {
        "spring.profiles.active=test",
        "spring.application.name=sale-publish-smt-service-phc-local",
        "spring.cloud.nacos.config.namespace="
})
@Slf4j
public class SmtTest {
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Test
    public void testSmtInfringement() {
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType("onSelling,auditing");

//        request.setInfringementTypeNames(Arrays.asList("侵权"));
//        request.setInfringementObjs(Arrays.asList("律所代理"));

        request.setQueryFields(new String[]{"id", "aliexpressAccountNumber", "productId", "articleNumber", "gmtCreate", "order_num_total"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);


        esAliexpressProductListing = esAliexpressProductListing.subList(0, 1000);

        if (CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            ProhibitionDO prohibitionDO = new ProhibitionDO(null, null, "侵权", "律所代理");
            //在调用产品系统接口校验一下
            ApiResult<Map<String, Boolean>> result = SmtInfringementForbiddenSaleHelper.checkForbiddenSale(esAliexpressProductListing, Collections.singletonList(prohibitionDO), "SmtTortOffItemJobHandler");
            if (!result.isSuccess()) {
                List<Long> productIds = esAliexpressProductListing.stream().map(EsAliexpressProductListing::getProductId).collect(Collectors.toList());
                XxlJobLogger.log(String.format("Smt:productIds[%s]校验禁售信息失败,不下架,error:%s", productIds, result.getErrorMsg()));
                return;
            }
            Map<String, Boolean> checkSku = result.getResult();

            esAliexpressProductListing = esAliexpressProductListing.stream().filter(item -> {
                if (BooleanUtils.isFalse(checkSku.get(item.getArticleNumber()))) {
                    // 过滤禁售产品
                    XxlJobLogger.log(String.format("Smt:productIds[%s]校验禁售信息不下架", item.getProductId()));
                    return false;
                } else {
                    return true;
                }
            }).collect(Collectors.toList());

            log.info(String.format("esAliexpressProductListing数量:%s", esAliexpressProductListing.size()));

        }
    }

}
