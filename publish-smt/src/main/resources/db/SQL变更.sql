
--2020年10月16日11:07:35
CREATE TABLE `ali_marketing_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `temp_name` varchar(255) DEFAULT NULL COMMENT '模板名称',
  `account` varchar(255) DEFAULT NULL COMMENT '店铺',
  `array_num` int(11) DEFAULT NULL COMMENT '每行个数',
  `product_num` int(11) DEFAULT NULL COMMENT '模块产品数量',
  `related_product_num` int(11) DEFAULT NULL COMMENT '已关联产品数量',
  `html` text COMMENT '模板内容',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  `last_updated_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `attribute1` varchar(255) DEFAULT NULL COMMENT '属性(预留)',
  `attribute2` varchar(255) DEFAULT NULL COMMENT '属性',
  `attribute3` varchar(255) DEFAULT NULL COMMENT '属性',
  `attribute4` varchar(255) DEFAULT NULL COMMENT '属性',
  `attribute5` varchar(255) DEFAULT NULL COMMENT '属性',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ids_temp_name_account` (`temp_name`,`account`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt营销模板';



CREATE TABLE `ali_marketing_temp_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `temp_id` int(11) DEFAULT NULL COMMENT '关联模板id',
  `product_id`  bigint(20) DEFAULT NULL COMMENT '产品ID',
  `article_number` varchar(255) DEFAULT NULL COMMENT '货号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `img_url` varchar(255) DEFAULT NULL COMMENT '图片',
  `sku_price` double DEFAULT NULL COMMENT 'sku价格',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt营销模板产品详情';


CREATE TABLE `ali_marketing_temp_related_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `temp_id` int(11) DEFAULT NULL COMMENT '关联模板id',
  `product_id`  bigint(20) DEFAULT NULL COMMENT '产品ID',
  `article_number` varchar(255) DEFAULT NULL COMMENT '货号',
  `related_date` datetime DEFAULT NULL COMMENT '关联时间',
  `update_status` int(11) DEFAULT NULL COMMENT '更新状态',
  `sku_price` double DEFAULT NULL COMMENT 'sku价格',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt营销模板关联产品';


ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `shop_id` varchar(255) DEFAULT NULL COMMENT 'shopId' AFTER `max_publish_num`;


ALTER TABLE `pmssale`.`aliexpress_product_log`
ADD COLUMN `operate_status` int(11) DEFAULT NULL COMMENT '日志状态' AFTER `operate_type`

ALTER TABLE `pmssale`.`aliexpress_product_log`
ADD COLUMN `request_data` text DEFAULT NULL COMMENT '请求条件' AFTER `operate_status`

ALTER TABLE `pmssale`.`aliexpress_product_log`
ADD COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间' AFTER `operator`


CREATE TABLE `aliexpress_auto_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `detail` text CHARACTER SET utf8mb4 NOT NULL COMMENT 'Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\\textarea\\style\\iframe\\frame\\input\\pre\\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签.',
  `aeop_ae_product_skus_json` text CHARACTER SET utf8mb4 NOT NULL COMMENT '列表类型，以json格式来表达。参看aeopAeProductSKUs数据结构。特别提示：新增SKU实际可售库存属性ipmSkuStock，该属性值的合理取值范围为0~999999，如该商品有SKU时，请确保至少有一个SKU是有货状态，也就是ipmSkuStock取值是1~999999，在整个商品纬度库存值的取值范围是1~999999。',
  `delivery_time` int(11) NOT NULL COMMENT '备货期。取值范围:1-60;单位:天。',
  `promise_template_id` bigint(20) DEFAULT NULL COMMENT '服务模板设置。（需和服务模板查询接口api.queryPromiseTemplateById进行关联使用）',
  `category_name` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '类目名',
  `category_table_id` bigint(20) DEFAULT NULL COMMENT '类目在数据表的id',
  `category_id` int(11) NOT NULL COMMENT '商品所属类目ID。必须是叶子类目，通过类目接口获取。',
  `subject` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '	商品标题 长度在1-128之间英文。',
  `product_price` double DEFAULT NULL COMMENT '商品一口价。取值范围:0-100000,保留两位小数;单位:美元。如:200.07，表示:200美元7分。需要在正确的价格区间内。上传多属性产品的时候，有好几个SKU和价格，productprice无需填写。',
  `freight_template_id` int(11) DEFAULT NULL COMMENT '运费模版ID。通过运费接口listFreightTemplate获取。',
  `image_urls` text CHARACTER SET utf8mb4 NOT NULL COMMENT '产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。',
  `product_unit` int(11) NOT NULL COMMENT '商品单位 (存储单位编号) 100000000:袋 (bag/bags) 100000001:桶 (barrel/barrels) 100000002:蒲式耳 (bushel/bushels) 100078580:箱 (carton) 100078581:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards)',
  `package_type` bit(1) NOT NULL COMMENT '打包销售: true 非打包销售:false',
  `lot_num` int(11) DEFAULT '1' COMMENT '每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1',
  `package_length` int(11) NOT NULL COMMENT '商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。',
  `package_width` int(11) NOT NULL COMMENT '商品包装宽度。取值范围:1-700,单位:厘米。',
  `package_height` int(11) NOT NULL COMMENT '商品包装高度。取值范围:1-700,单位:厘米。',
  `gross_weight` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '商品毛重,取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。',
  `is_pack_sell` bit(1) DEFAULT NULL COMMENT '是否自定义计重.true为自定义计重,false反之.',
  `is_wholesale` bit(1) DEFAULT NULL COMMENT '是否自定义计重',
  `base_unit` int(11) DEFAULT NULL COMMENT 'isPackSell为true时,此项必填。购买几件以内不增加运费。取值范围1-1000',
  `add_unit` int(11) DEFAULT NULL COMMENT 'isPackSell为true时,此项必填。 每增加件数.取值范围1-1000。',
  `add_weight` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'isPackSell为true时,此项必填。 对应增加的重量.取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。',
  `ws_valid_num` int(11) DEFAULT NULL COMMENT '商品有效天数。取值范围:1-30,单位:天。',
  `aeop_ae_product_propertys_json` longtext CHARACTER SET utf8mb4 NOT NULL COMMENT '产品属性，以json格式进行封装后提交。参看aeopAeProductPropertys数据结构。此字段是否必填，需从类目接口getChildAttributesResultByPostCateIdAndPath获取（即获取到的required来判断属性是否必填），该项只输入普通类目属性数据，不可输入sku类目属性。 对于类目属性包含子类目属性的情况，此处不确认父属性和子属性，即选择任何属性，都以该对象提交。 对于一个属性多个选中值的情况，以多个该对象存放。 其中"attrNameId","attrValueId"为整型(Integer), "attrName", "attrValue"为字符串类型(String)。 1. 当设置一些系统属性时，如果类目自定义了一些候选值，只需要提供"attrNameId"和"attrValueId"即可。例如：{"attrNameId":494, "attrValueId":284}。 2. 当设置一些需要手工输入属性内容时，只需要提供"attrNameId"和"attrValue"即可。例如：{"attrNameId": 1000, "attrValue": "test"} 3. 当设置自定义属性时，需要提供"attrName"和"attrValue"即可。例如: {"attrName": "Color", "attrValue": "red"} 4. 当设置一个Other属性时，需要提供"attrNameId", "attrValueId", "attrValue"三个参数。例如：{"attrNameId": 1000, "attrValueId": 4, "attrValue": "Other Value"}。',
  `bulk_order` int(11) DEFAULT NULL COMMENT '批发最小数量 。取值范围2-100000。批发最小数量和批发折扣需同时有值或无值。',
  `bulk_discount` int(11) DEFAULT NULL COMMENT '批发折扣。扩大100倍，存整数。取值范围:1-99。注意：这是折扣，不是打折率。 如,打68折,则存32。批发最小数量和批发折扣需同时有值或无值。',
  `size_chart_id` bigint(20) DEFAULT NULL COMMENT '尺码表模版ID。必须选择当前类目下的尺码模版。',
  `reduce_strategy` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '库存扣减策略，总共有2种：下单减库存(place_order_withhold)和支付减库存(payment_success_deduct)。',
  `group_id` bigint(20) DEFAULT NULL COMMENT '这个产品需要关联的产品分组ID. 只能关联一个产品分组，如果想关联多个产品分组，请使用api.setGroups接口。',
  `currency_code` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。',
  `mobile_detail` text CHARACTER SET utf8mb4 COMMENT 'mobile Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\\textarea\\style\\iframe\\frame\\input\\pre\\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签.',
  `coupon_start_date` datetime DEFAULT NULL COMMENT '卡券商品开始有效期',
  `coupon_end_date` datetime DEFAULT NULL COMMENT '卡券商品结束有效期',
  `aeop_national_quote_configuration` text CHARACTER SET utf8mb4 COMMENT '商品分国家报价的配置',
  `aeop_ae_multimedia` text CHARACTER SET utf8mb4 COMMENT '商品多媒体信息，该属性主要包含商品的视频列表',
  `article_number` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '系统单品货号',
  `aliexpress_account_number` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '速卖通帐号',
  `display_image_url` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '模版展示图',
  `product_code` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '商品编码',
  `product_stock` int(11) DEFAULT NULL COMMENT '商品库存',
  `post_time` datetime DEFAULT NULL COMMENT '刊登时间',
  `margin` double DEFAULT NULL COMMENT '毛利率',
  `shipping_method_code` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '物流方式代码',
  `share_user` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '将模版分享给其他销售',
  `is_public` bit(1) DEFAULT NULL COMMENT '是否公开',
  `is_parent` tinyint(1) DEFAULT NULL,
  `template_status` int(11) DEFAULT NULL COMMENT '状态',
  `template_type` int(11) DEFAULT '1' COMMENT '类型',
  `template_label` varchar(255) DEFAULT NULL COMMENT '模板标签',
  `parent_id` int(11) DEFAULT NULL COMMENT '复制的范本id',
  `car_type_json` longtext COMMENT '车型库json用于回写',
  `market_images_json` text COMMENT '营销图json格式',
  `video_file_url` varchar(255) DEFAULT NULL COMMENT '视频地址',
  `video_link` varchar(255) DEFAULT NULL COMMENT '视频链接',
	`apply_state` int(1) DEFAULT NULL COMMENT '使用状态 0禁用 1启用',
	`creator` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `last_edit_time` datetime DEFAULT NULL COMMENT '上次编辑时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_auto_spu` (`article_number`(32),`category_id`) USING BTREE,
  KEY `index_auto_account` (`aliexpress_account_number`(32)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8;


CREATE TABLE `spu_account_publish_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sale_channel` varchar(32) DEFAULT NULL COMMENT '平台',
  `account`  varchar(255) DEFAULT NULL COMMENT '店铺',
  `salesman` varchar(64) DEFAULT NULL COMMENT '销售人员',
  `success_spus` text DEFAULT NULL COMMENT '成功的spu',
  `fail_spus` text DEFAULT NULL COMMENT '失败的spu',
  `test_date` datetime DEFAULT NULL COMMENT '测试时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
	 KEY `index_account` (`account`(32)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='spu刊登店铺统计';


ALTER TABLE `pmssale`.`spu_account_publish_statistics`
ADD COLUMN `config_count` int(11) DEFAULT NULL COMMENT '配置数量';

ALTER TABLE `pmssale`.`spu_account_publish_statistics`
ADD COLUMN `success_count` int(11) DEFAULT NULL COMMENT '成功数量';

ALTER TABLE `pmssale`.`spu_account_publish_statistics`
ADD COLUMN `fail_count` int(11) DEFAULT NULL COMMENT '失败数量';

ALTER TABLE `pmssale`.`spu_account_publish_statistics`
ADD COLUMN `success_rate` double DEFAULT NULL COMMENT '成功率';


alter table aliexpress_product_source modify column id bigint(20) NOT NULL AUTO_INCREMENT;
alter table aliexpress_product_log modify column relation_id varchar(127);


CREATE TABLE `aliexpress_sku_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_number` varchar(50) DEFAULT NULL COMMENT '店铺',
  `seller_sku` varchar(255) DEFAULT NULL COMMENT '平台SKU',
  `system_sku` varchar(255) DEFAULT NULL COMMENT '系统SKU',
  `article_number` varchar(255) DEFAULT NULL COMMENT '主货号',
  `order_no` varchar(255) DEFAULT NULL COMMENT '订单号',
  `price` double DEFAULT NULL COMMENT '价格',
  `payment_time` datetime DEFAULT NULL COMMENT '付款时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_article_number` (`article_number`(32)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='SMT平台SKU出单信息';



ALTER TABLE `pmssale`.`aliexpress_auto_publish_report`
ADD COLUMN `parent_template_count` int(11) DEFAULT '0' COMMENT '范本数量' AFTER `success_template_ids`;

ALTER TABLE `pmssale`.`aliexpress_auto_publish_report`
ADD COLUMN `order_count` int(11) DEFAULT '0' COMMENT '订单数量' AFTER `success_rate`;

ALTER TABLE `pmssale`.`aliexpress_auto_publish_report`
ADD COLUMN `out_order_rate` double DEFAULT '0' COMMENT '订单转化率' AFTER `order_count`;


ALTER TABLE `pmssale`.`aliexpress_template`
ADD COLUMN `title_rule` varchar(255) DEFAULT NULL COMMENT 'spu标题取值规则记录';

ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `auto_grounding_new` bit(1) DEFAULT 0 COMMENT '自动上架新品' AFTER `shop_id`;

ALTER TABLE `pmssale`.`amazon_account_relation`
ADD COLUMN `auto_publish_new` bit(1) DEFAULT 0 COMMENT '自动上架新品' ;


ALTER TABLE `pmssale`.`amazon_seller_sku_rule`
ADD COLUMN `is_wenan` bit(1) DEFAULT 0 COMMENT '是否文案' AFTER `enable`;

ALTER TABLE `pmssale`.`aliexpress_template`
ADD COLUMN `publish_role` int(11) DEFAULT 1 COMMENT '刊登角色 0系统刊登 1销售刊登 2 文案刊登 ';

ALTER TABLE `pmssale`.`template_queue`
ADD COLUMN `publish_role` int(11) DEFAULT 1 COMMENT '刊登角色 0系统刊登 1销售刊登 2 文案刊登 ';

update aliexpress_template set publish_role = 1 where creator != 'admin' and creator is not null


update template_queue set publish_role = 1 where created_by != 'admin' and created_by is not null and sale_channel = 'SMT'



ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `packing_type` int(2) DEFAULT null COMMENT '包装尺寸 1实际尺寸 2店铺配置尺寸 3 系统默认';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `package_length` int(11) DEFAULT null COMMENT '长 cm 1-700';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `package_width` int(11) DEFAULT null COMMENT '宽 cm 1-700';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `package_height` int(11) DEFAULT null COMMENT '高 cm 1-700';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `add_weight` double DEFAULT null COMMENT 'g 增加重量';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `title_type` int(2) DEFAULT 0 COMMENT '标题前后缀默认前缀 0前缀 1后缀';
ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `title_value` varchar(255) DEFAULT NULL COMMENT '标题文本';



CREATE TABLE `aliexpress_new_remind` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `spu` varchar(255) DEFAULT NULL COMMENT '货号',
	`account` varchar(255) DEFAULT NULL COMMENT '店铺',
	`first_image` varchar(255) DEFAULT NULL COMMENT '主图',
	`title` varchar(255) DEFAULT NULL COMMENT '标题',
	`root_category` int(11) DEFAULT NULL COMMENT '经营大类',
	`root_category_zhname` varchar(255) DEFAULT NULL COMMENT '经营大类中文名',
  `edit_finish_time` datetime DEFAULT NULL COMMENT '编辑完成时间',
  `create_at` datetime DEFAULT NULL COMMENT '开发录入时间',
	`push_time` datetime DEFAULT NULL COMMENT '推送时间',
	`is_success_temp`  bit(1) DEFAULT NULL COMMENT '是否有成功模板',
	`temp_finish_time` datetime DEFAULT NULL COMMENT '模板创建时间',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_spu` (`spu`(32)) USING BTREE,
  KEY `index_account` (`account`(32)) USING BTREE,
  KEY `index_push_time` (`push_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt新品推荐';



CREATE TABLE `aliexpress_new_remind_count_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sale_man` varchar(255) DEFAULT NULL COMMENT '销售',
	`account` varchar(255) DEFAULT NULL COMMENT '店铺',
	`push_time` datetime DEFAULT NULL COMMENT '推送时间',
	`today_need_up_count` int(11) DEFAULT NULL COMMENT '当天需刊登spu数',
	`today_no_up_count` int(11) DEFAULT NULL COMMENT '当天未刊登spu数',
	`today_up_rate` double DEFAULT NULL COMMENT '当天新品上架率',
	`four_need_up_count` int(11) DEFAULT NULL COMMENT '4天需刊登spu数',
	`four_no_up_count` int(11) DEFAULT NULL COMMENT '4天未刊登spu数',
	`four_up_rate` double DEFAULT NULL COMMENT '4天新品上架率',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_sale_man` (`sale_man`(32)) USING BTREE,
  KEY `index_account` (`account`(32)) USING BTREE,
  KEY `index_push_time` (`push_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt新品推荐明细';


CREATE TABLE `aliexpress_new_remind_count_info_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
	`type` int(11) DEFAULT NULL COMMENT '类型 1销售，2平台',
  `sale_man` varchar(255) DEFAULT NULL COMMENT '销售',
	`platform` varchar(255) DEFAULT NULL COMMENT '平台',
	`push_time` datetime DEFAULT NULL COMMENT '推送时间',
	`today_up_rate` double DEFAULT NULL COMMENT '当天新品上架率',
	`four_up_rate` double DEFAULT NULL COMMENT '4天新品上架率',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_push_time` (`push_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt新品推荐明细统计';


update aliexpress_config set auto_grounding_new = 0 where auto_grounding_new is null;

alter table aliexpress_template add index creator (creator(32));

alter table aliexpress_template add index index_create_time (create_time);


ALTER TABLE `pmssale`.`aliexpress_product_log`
ADD COLUMN `result_type` varchar(63) DEFAULT NULL COMMENT '结果类型'



ALTER TABLE `pmssale`.`aliexpress_new_remind`
ADD COLUMN `sale_man` varchar(63) DEFAULT NULL COMMENT '销售' AFTER `account`;

ALTER TABLE `pmssale`.`aliexpress_new_remind`
ADD COLUMN `sale_leader_man` varchar(63) DEFAULT NULL COMMENT '销售组长' AFTER `sale_man`;

ALTER TABLE `pmssale`.`aliexpress_new_remind`
ADD COLUMN `remarks` varchar(225) DEFAULT NULL COMMENT '备注' AFTER `temp_finish_time`;


alter table template_queue add index idx_seller_id (seller_id(32));
alter table template_queue add index idx_sku (sku(32));


ALTER TABLE `template_queue` ADD COLUMN `publish_result` bit(1) DEFAULT NULL COMMENT '操作结果';
ALTER TABLE `template_queue` ADD COLUMN `extended_field1` varchar(255) DEFAULT NULL COMMENT '扩展字段1';
ALTER TABLE `template_queue` ADD COLUMN `extended_field2` varchar(255) DEFAULT NULL COMMENT '扩展字段2';
ALTER TABLE `template_queue` ADD COLUMN `extended_field3` varchar(255) DEFAULT NULL COMMENT '扩展字段3';



ALTER TABLE `pmssale`.`aliexpress_template`
ADD COLUMN `examine_state` varchar(255) DEFAULT NULL COMMENT '审核状态',
ADD COLUMN `examine_date` datetime DEFAULT NULL COMMENT '审核时间',
ADD COLUMN `examine_saleman` varchar(255) DEFAULT NULL COMMENT '审核人';


ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `cny`  bit(1) DEFAULT 0 COMMENT '支持cny结算' AFTER `shop_id`,
add Column `from_weight` double DEFAULT NULL COMMENT '重量区间 g 净重+包材+包装材料 + 3g',
add Column `to_weight` double DEFAULT NULL COMMENT '重量区间 g',
add Column `from_price` double DEFAULT NULL COMMENT '成本价格区间',
add Column `to_price` double DEFAULT NULL COMMENT '成本价格区间',
add Column `special_tags` text DEFAULT NULL COMMENT '特殊标签',
add Column `item_count` int(11) DEFAULT NULL COMMENT '在线item总数';


CREATE TABLE `aliexpress_es_extend` (
  `extend_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `aliexpress_account_number` varchar(255) DEFAULT NULL COMMENT '账号',
	`owner_member_id` varchar(255) DEFAULT NULL COMMENT '账号id',
	`product_id` bigint(20) NOT NULL COMMENT '产品id',
  `aeop_ae_product_skus_json` mediumtext,
	`aeop_national_quote_configuration` mediumtext CHARACTER SET utf8mb4 COMMENT '商品分国家报价的配置',
  `aeop_ae_multimedia` text CHARACTER SET utf8mb4 COMMENT '商品多媒体信息，该属性主要包含商品的视频列表',
  `aeop_ae_product_propertys_json` mediumtext,
  `mobile_detail` longtext CHARACTER SET utf8mb4 COMMENT 'mobile Detail详情',
  PRIMARY KEY (`extend_id`) USING BTREE,
  UNIQUE KEY `es_index_aliexpress_account_number_product_id` (`aliexpress_account_number`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smtES扩展表';


ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `cny`  bit(1) DEFAULT 0 COMMENT '支持cny结算' AFTER `shop_id`,
add Column `from_weight` double DEFAULT NULL COMMENT '重量区间 g 净重+包材+包装材料 + 3g',
add Column `to_weight` double DEFAULT NULL COMMENT '重量区间 g',
add Column `from_price` double DEFAULT NULL COMMENT '成本价格区间',
add Column `to_price` double DEFAULT NULL COMMENT '成本价格区间',
add Column `special_tags` text DEFAULT NULL COMMENT '特殊标签',
add Column `item_count` int(11) DEFAULT NULL COMMENT '在线item总数';

ALTER TABLE `aliexpress_new_remind` ADD `editor` varchar(255) DEFAULT NULL COMMENT '文案' AFTER `account`, ADD`publish_role` int(2) DEFAULT NULL COMMENT '刊登角色 1销售 2文案';
ALTER TABLE `aliexpress_new_remind_count_info` ADD `publish_type`  int(11) DEFAULT NULL COMMENT '1销售  2文案', ADD `editor`  varchar(255) DEFAULT NULL COMMENT '文案' AFTER `sale_man`;
ALTER TABLE `aliexpress_new_remind_count_info_statistics` ADD `editor`  varchar(255) DEFAULT NULL COMMENT '文案' AFTER `sale_man`;

ALTER TABLE `aliexpress_new_remind`
ADD `publish_status` int(2) DEFAULT NULL COMMENT '刊登状态 1.待刊登 2.刊登中, 3.刊登成功 4.刊登失败',
ADD `fail_info` text DEFAULT NULL COMMENT '失败原因',
ADD `template_id` int(11) DEFAULT NULL COMMENT '失败模板id';

update aliexpress_new_remind set publish_status = 3 where is_success_temp is true;
update aliexpress_new_remind set publish_status = 1 where publish_status is null;

ALTER TABLE `aliexpress_template` ADD `video_link` varchar(512) DEFAULT NULL COMMENT '视频地址';
ALTER TABLE `aliexpress_template_2021_11` ADD `video_link` varchar(512) DEFAULT NULL COMMENT '视频地址';
ALTER TABLE `aliexpress_template_model` ADD `video_link` varchar(512) DEFAULT NULL COMMENT '视频地址';

32  publish  smtTemplateVideo  0  7  刊登-smt临时存储7天
33  publish  smtListingVideo  0  1  刊登-smt临时存储1天

ALTER TABLE `aliexpress_config`
ADD `sales_yesterday_rate` double DEFAULT NULL COMMENT '昨天商品动销率',
ADD `sales_30d_rate` double DEFAULT NULL COMMENT '30天商品动销率',
ADD `sales_60d_rate` double DEFAULT NULL COMMENT '60天商品动销率';

ALTER TABLE `pmssale`.`aliexpress_es_extend`
ADD COLUMN `auto_off_date` datetime DEFAULT NULL COMMENT '自动下架时间',
ADD COLUMN `auto_on_date` datetime DEFAULT NULL COMMENT '自动上架时间',
ADD COLUMN `start_task_date` datetime DEFAULT NULL COMMENT '任务周期开始',
ADD COLUMN `end_task_date` datetime DEFAULT NULL COMMENT '任务周期结束',
ADD COLUMN `record_off_date` datetime DEFAULT NULL COMMENT '记录下架时间',
ADD COLUMN `record_on_date` datetime DEFAULT NULL COMMENT '记录上架时间';

alter table aliexpress_es_extend
add index index_auto_off_date (auto_off_date),
add index index_auto_on_date (auto_on_date),
add index index_start_task_date (start_task_date),
add index index_end_task_date (end_task_date),
add index index_record_off_date (record_off_date),
add index index_record_on_date (record_on_date)
;


CREATE TABLE `aliexpress_excel_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account` varchar(2048) DEFAULT NULL COMMENT '多个店铺',
  `type` int(11) DEFAULT NULL COMMENT '类型 1.下载 2.28国改价模板 3.28国改价结果校验 4.excle改价 5.28国excel改价 6.excel改重量',
  `download_conut` int(11) DEFAULT NULL COMMENT '上传和下载数量',
  `queue_up` int(11) DEFAULT NULL COMMENT '当前排队',
  `version_number` int(11) DEFAULT NULL COMMENT '版本号',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `excel_upload_url` varchar(255) DEFAULT NULL COMMENT '上传url(excel操作)',
  `excel_down_url` varchar(255) DEFAULT NULL COMMENT 'excel下载url',
  `create_time` datetime DEFAULT NULL COMMENT '上传和下载时间',
  `create_by` varchar(255) DEFAULT NULL COMMENT '下载或上传人',
  `create_name` varchar(255) DEFAULT NULL COMMENT '下载或上传人中文名',
  `queue_ex_time` datetime DEFAULT NULL COMMENT '队列执行时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
	`error_msg` varchar(1024) DEFAULT NULL COMMENT '异常',
  PRIMARY KEY (`id`),
  KEY `index_account` (`account`(32)) USING BTREE,
  KEY `index_create_by` (`account`(32)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='下载上传excel';


alter table aliexpress_es_extend
add index index_product_id (product_id)
;


CREATE TABLE `aliexpress_custom_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_value` varchar(1024) DEFAULT NULL COMMENT '自定义内容',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `last_update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `last_update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_custom_create_by` (`create_by`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt自定义设置';


ALTER TABLE `pmssale`.`aliexpress_product_log`
ADD COLUMN `weight_before_edit` double DEFAULT NULL COMMENT '修改前重量' AFTER `stock_after_edit`,
ADD COLUMN `weight_after_edit` double DEFAULT NULL COMMENT '修改后重量' AFTER `weight_before_edit`

ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `auto_update_weight` bit(1) DEFAULT NULL COMMENT '自动修改重量' AFTER `auto_grounding_new`,
ADD COLUMN `auto_supply_stock` bit(1) DEFAULT NULL COMMENT '自动补库存' AFTER `auto_update_weight`,
ADD COLUMN `auto_update_title` bit(1) DEFAULT NULL COMMENT '自动调整标题' AFTER `auto_supply_stock`,
ADD COLUMN `auto_update_detail` bit(1) DEFAULT NULL COMMENT '自动调整描述' AFTER `auto_update_title`,
ADD COLUMN `auto_update_sonimg` bit(1) DEFAULT NULL COMMENT '自动调整子sku图片' AFTER `auto_update_detail`,
ADD COLUMN `auto_recommend_new_product` bit(1) DEFAULT NULL COMMENT '是否分配新品' AFTER `auto_update_sonimg`;



ALTER TABLE `pmssale`.`ebay_item_variation`
ADD COLUMN `shipping_method` varchar(255) DEFAULT NULL COMMENT '试算物流' AFTER `currency_code`,
ADD COLUMN `gross_profit` double DEFAULT NULL COMMENT '毛利' AFTER `shipping_method`,
ADD COLUMN `gross_profit_margin` double DEFAULT NULL COMMENT '毛利率' AFTER `gross_profit`



ALTER TABLE `pmssale`.`aliexpress_config`
ADD COLUMN `auto_update_deficit_order` bit(1) DEFAULT NULL COMMENT '是否修改亏损订单' AFTER `auto_recommend_new_product`;

CREATE TABLE `aliexpress_config_profit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '配置主键id',
  `account` varchar(255) DEFAULT NULL COMMENT '店铺',
  `freight_template_id` bigint(20) DEFAULT NULL COMMENT '运费模板id',
  `freight_template_name` varchar(255)  DEFAULT NULL COMMENT '运费模板name',
  `shipping_method` varchar(255) DEFAULT NULL COMMENT '试算物流',
  `gross_profit_rate` double DEFAULT NULL COMMENT '价格毛利率',
  `discount_rate` double DEFAULT NULL COMMENT '折扣率',
  `country_code` varchar(255) DEFAULT NULL COMMENT '计算国家',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt店铺毛利配置';


CREATE TABLE `aliexpress_deficit_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_number` varchar(255) DEFAULT NULL COMMENT '店铺',
  `article_number` varchar(255) DEFAULT NULL COMMENT '货号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品id',
  `sku_code` varchar(255)  DEFAULT NULL COMMENT '商品编码',
  `order_gross_profit` double DEFAULT NULL COMMENT '订单毛利',
  `order_gross_profit_rate` double DEFAULT NULL COMMENT '订单毛利率',
  `before_price` double DEFAULT NULL COMMENT '之前的价格',
  `now_price` double DEFAULT NULL COMMENT '现在的价格',
  `product_gross_profit` double DEFAULT NULL COMMENT '产品毛利',
  `product_gross_profit_rate` double DEFAULT NULL COMMENT '产品毛利率',
  `sign_red` bit(1) DEFAULT NULL COMMENT '标红',
  `modify_price` int(2)  DEFAULT NULL COMMENT '是否修改价格 0否 1是 2不调价',
  `group_ids` varchar(255) DEFAULT NULL COMMENT '分组ids',
  `before_freight_template_id` bigint(20) DEFAULT NULL COMMENT '之前运费模板id',
  `now_freight_template_id` bigint(20) DEFAULT NULL COMMENT '现在运费模板id',
  `create_date` datetime DEFAULT NULL COMMENT '推送时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  `price_update_date` datetime DEFAULT NULL COMMENT '价格修改时间（即使没调价也会更新这个时间）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `deficit_product_id_sku_code` (`product_id`,`sku_code`) USING BTREE,
  KEY `index_account_number` (`account_number`(32)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt亏损订单';


CREATE TABLE `aliexpress_deficit_order_error` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `body` varchar(1023) DEFAULT NULL COMMENT 'body',
  `error` text COMMENT '错误信息',
  `create_date` datetime DEFAULT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt亏损订单异常消息';


ALTER TABLE `pmssale`.`aliexpress_config_profit`
ADD COLUMN `area_gross_profit_rate` double DEFAULT NULL COMMENT '区域价格毛利率' AFTER `gross_profit_rate`,
ADD COLUMN `area_id` int(11) DEFAULT NULL COMMENT '区域调价配置' AFTER `area_gross_profit_rate`

-------------------------------2024.6.27版本 cxd---------------------------

--创建至smt库
CREATE TABLE `aliexpress_marketing_config` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `name` varchar(255) DEFAULT NULL COMMENT '规则名称',
                                               `type` int(4) DEFAULT NULL COMMENT '配置类型 1=联盟',
                                               `accounts` longtext COMMENT '店铺',
                                               `rule_json` longtext COMMENT '规则内容',
                                               `status` int(4) DEFAULT NULL COMMENT '状态 0 禁用 1 启用',
                                               `remove_group` longtext COMMENT '移除分组',
                                               `trigger_type` varchar(20) DEFAULT NULL COMMENT '设置频率;every_day;every_week;every_month',
                                               `exec_days_time` varchar(255) DEFAULT NULL COMMENT '每周/每月执行哪些天,多个使用逗号隔开',
                                               `start_time` varchar(255) DEFAULT NULL COMMENT '开始设置时间',
                                               `strategy_start_time` datetime DEFAULT NULL COMMENT '策略开始时间',
                                               `strategy_end_time` datetime DEFAULT NULL COMMENT '策略结束时间',
                                               `plan_days` int(11) DEFAULT NULL COMMENT '计划推广时长',
                                               `plan_order_counts` int(11) DEFAULT NULL COMMENT '计划出单数量',
                                               `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `created_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                               `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               `updated_by` varchar(255) DEFAULT NULL COMMENT '更新人',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_name` (`name`) USING BTREE,
                                               KEY `idx_type` (`type`) USING BTREE,
                                               KEY `idx_status` (`status`) USING BTREE,
                                               KEY `idx_created_by` (`created_by`) USING BTREE,
                                               KEY `idx_created_time` (`created_time`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt营销配置';


CREATE TABLE `aliexpress_marketing_config_log` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `marketing_id` int(11) DEFAULT NULL COMMENT '配置id',
                                                   `operate_attr` varchar(128) DEFAULT NULL COMMENT '操作的属性',
                                                   `previous_value` longtext COMMENT '改前值',
                                                   `after_value` longtext COMMENT '改后值',
                                                   `operator` varchar(64) DEFAULT NULL COMMENT '操作者',
                                                   `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_marketing_id` (`marketing_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt营销配置更改日志';



-------------------------------2024.6.27版本 cxd---------------------------


DROP TABLE IF EXISTS `aliexpress_category_delivery_day`;
CREATE TABLE `aliexpress_category_delivery_day` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '速卖通类目id',
  `leaf_category_en_name` varchar(255) DEFAULT NULL COMMENT '子类目英文',
  `one_category_en_name` varchar(255) DEFAULT NULL COMMENT '一级类目英文',
  `two_category_en_name` varchar(255) DEFAULT NULL COMMENT '二级类目英文',
  `day` int(11) DEFAULT NULL COMMENT '发货天数',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by`  varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_category_id` (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='smt分类发货天数配置';


CREATE TABLE `smt_account_group` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `group_name` varchar(255) NOT NULL COMMENT '分组名称',
   `accounts` longtext COMMENT '店铺多个逗号拼接',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `create_by`  varchar(255) DEFAULT NULL COMMENT '创建人',
   `update_date` datetime DEFAULT NULL COMMENT '修改时间',
   `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
   PRIMARY KEY (`id`),
   UNIQUE KEY `smt_group_name` (`group_name`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt店铺分组';


INSERT INTO `publish_smt`.`system_param`(`param_code`, `param_key`, `param_name`, `param_value`, `param_type`, `param_display`, `param_enabled`, `platform`) VALUES ('smt_off', 'switch', 'smt下架开关', '0', 5, 1, 1, 'smt');

CREATE TABLE `smt_pop_rule_off_record` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `img` varchar(255) DEFAULT NULL COMMENT '图片',
   `account` varchar(255) DEFAULT NULL COMMENT '店铺',
   `product_id` bigint(20) DEFAULT NULL COMMENT '产品id',
   `spu` varchar(255) DEFAULT NULL COMMENT 'spu',
   `skus` text COMMENT '货号多个英文逗号拼接',
   `sku_ids` text COMMENT 'skuId多个英文逗号拼接',
   `rule_name` varchar(255) DEFAULT NULL COMMENT '规则名称 ',
   `off_way` varchar(255) DEFAULT NULL COMMENT 'off 下架 delete 删除',
   `rule_content` longtext COMMENT '规则内容',
   `product_info` longtext COMMENT '产品具体符合规则信息',
   `order_24H_count` int(11) DEFAULT NULL COMMENT '24小时销量',
   `order_last_7d_count` int(11) DEFAULT NULL COMMENT '7天销量',
   `order_last_14d_count` int(11) DEFAULT NULL COMMENT '14天销量',
   `order_last_30d_count` int(11) DEFAULT NULL COMMENT '30天销量',
   `order_last_60d_count` int(11) DEFAULT NULL COMMENT '60天销量',
   `order_last_180d_count` int(11) DEFAULT NULL COMMENT '180天销量',
   `order_num_total` int(11) DEFAULT NULL COMMENT '总销量',
   `view_7d_count` int(11) DEFAULT NULL COMMENT '7天浏览量',
   `view_14d_count` int(11) DEFAULT NULL COMMENT '14天浏览量',
   `view_30d_count` int(11) DEFAULT NULL COMMENT '30天浏览量',
   `exposure_7d_count` int(11) DEFAULT NULL COMMENT '7天曝光量',
   `exposure_14d_count` int(11) DEFAULT NULL COMMENT '14天曝光量',
   `exposure_30d_count` int(11) DEFAULT NULL COMMENT '30天曝光量',
   `execute_state` int(11) DEFAULT NULL COMMENT '1 成功 0 失败',
   `fail_info` text COMMENT '失败详情',
   `off_date` datetime DEFAULT NULL COMMENT '下架时间',
   `create_by`  varchar(255) DEFAULT NULL COMMENT '创建人',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间(匹配时间)',
   `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
   `update_date` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`),
   KEY `idx_account` (`account`) USING BTREE,
   KEY `idx_product_id` (`product_id`) USING BTREE,
   KEY `idx_spu` (`spu`) USING BTREE,
   KEY `idx_create_date` (`create_date`) USING BTREE,
   KEY `idx_off_date` (`off_date`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt规则下架队列日志';


CREATE TABLE `rule_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `platform` varchar(127) DEFAULT NULL COMMENT '平台',
  `rule_table_name` varchar(127) DEFAULT NULL COMMENT '具体规则的表名',
  `rule_name` varchar(127) DEFAULT NULL COMMENT '规则名称',
  `rule_type` varchar(127) DEFAULT NULL COMMENT '规则类型 String通用',
  `result` bit(1) DEFAULT NULL COMMENT '结果',
  `msg` text COMMENT '提示',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_rule_name` (`rule_name`(32)) USING BTREE,
  KEY `index_create_time` (`created_date`) USING BTREE,
  KEY `index_rule_table_name` (`rule_table_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='规则记录表';


alter table aliexpress_operate_log modify column `before` longtext;
alter table aliexpress_operate_log modify column after longtext;
alter table aliexpress_operate_log modify column message longtext;


alter table aliexpress_listing_config modify column `config_json` longtext;



ALTER TABLE aliexpress_marketing_config ADD `account_type` int(4) DEFAULT NULL COMMENT '店铺类型 1 店铺分组 2 店铺';
ALTER TABLE aliexpress_marketing_config ADD `account_group_name` text COMMENT '店铺分组名称';


CREATE TABLE `smt_marketing_single_discount_fail_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `single_discount_id` bigint(20) DEFAULT NULL COMMENT '平台单品折扣id（平台标识）',
  `account_number` varchar(128) NOT NULL COMMENT '店铺账号',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品id',
  `retry_count` int(11) DEFAULT NULL COMMENT '重试次数',
  `result` bit(1) DEFAULT NULL COMMENT '结果',
  `msg` text COMMENT '提示',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_account_number` (`account_number`(32)) USING BTREE,
  KEY `index_product_id` (`product_id`) USING BTREE,
  KEY `index_create_time` (`created_date`) USING BTREE,
  KEY `index_single_discount_id` (`single_discount_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt单品折扣活动失败日志';


INSERT INTO `publish_smt`.`system_param`(`param_code`, `param_key`, `param_name`, `param_value`, `param_type`, `param_display`, `param_enabled`, `platform`)
VALUES ('smt_discount_delete', 'try_count', 'smt单品折扣删除重试限制', '10', 5, 1, 1, 'smt');


CREATE TABLE `smt_stock_update_final_log_copy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account` varchar(255) DEFAULT NULL COMMENT '店铺',
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品id',
  `article_number` varchar(255) DEFAULT NULL COMMENT '货号',
  `sku_status` varchar(255) DEFAULT NULL COMMENT 'sku状态',
  `sku_id` varchar(255) DEFAULT NULL COMMENT 'sku_id',
  `usable_stock` int(11) DEFAULT NULL COMMENT '可用库存',
  `pending_stock` int(11) DEFAULT NULL COMMENT '待发库存',
  `pre_reduction_stock` int(11) DEFAULT NULL COMMENT '预扣库存',
  `redis_stock` int(11) DEFAULT NULL COMMENT 'redis库存(可用库存-待发库存-预扣库存)',
  `stock_before` int(11) DEFAULT NULL COMMENT '改前库存（在线列表链接库存）',
  `stock_after` int(11) DEFAULT NULL COMMENT '改后库存（在线列表链接库存）',
  `order_num_30d` int(11) DEFAULT NULL COMMENT 'sku全平台30天销量',
  `order_days_within_30d` int(11) DEFAULT NULL COMMENT '30天动销天数（全平台）',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `update_type` varchar(255) DEFAULT NULL COMMENT 'eg:pop',
  `result_type` int(11) DEFAULT NULL COMMENT '操作结果 1 成功 0 失败 2.忽略不修改',
  `fail_info` text COMMENT '错误信息',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_account` (`account`(32)) USING BTREE,
  KEY `index_product_id` (`product_id`) USING BTREE,
  KEY `index_create_date` (`create_date`) USING BTREE,
  KEY `index_article_number` (`article_number`(32)) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt调整库存最终日志';


-------------------------------2024.7.15版本 cxd---------------------------
#还有一张表




CREATE TABLE `smt_marketing_single_discount` (
                                                 `id` bigint(15) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                 `account_number` varchar(128) NOT NULL COMMENT '店铺账号',
                                                 `single_discount_id` bigint(15) DEFAULT NULL COMMENT '平台单品折扣id（平台标识）',
                                                 `name` varchar(255) DEFAULT NULL COMMENT '折扣名称',
                                                 `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                                 `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                                 `status` int(4) DEFAULT NULL COMMENT '状态(未生效=1，生效中=2，已结束=3)',
                                                 `single_discount_prod_num` int(11) DEFAULT NULL COMMENT '活动关联商品数量',
                                                 `no_link_num` int(11) DEFAULT NULL COMMENT '店铺在线-关联商品数量',
                                                 `last_submit_time` datetime DEFAULT NULL COMMENT '最新报名时间',
                                                 `sync_time` datetime DEFAULT NULL COMMENT '同步时间',
                                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                 `config_id` int(11) DEFAULT NULL COMMENT '对应的配置id',
                                                 `rule_json` longtext COMMENT '规格内容',
                                                 `listing_num` bigint(15) DEFAULT NULL COMMENT '在线列表商品总数',
                                                 PRIMARY KEY (`id`),
                                                 KEY `idx_account_number` (`account_number`) USING BTREE,
                                                 KEY `idx_status` (`status`) USING BTREE,
                                                 KEY `idx_start_time` (`start_time`) USING BTREE,
                                                 KEY `idx_end_time` (`end_time`) USING BTREE,
                                                 KEY `idx_name` (`name`) USING BTREE,
                                                 KEY `idx_single_discount_id` (`single_discount_id`) USING BTREE,
                                                 KEY `idx_single_discount_prod_num` (`single_discount_prod_num`) USING BTREE,
                                                 KEY `idx_no_link_num` (`no_link_num`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='smt 单品折扣列表';


CREATE TABLE `smt_single_discount_product` (
                                               `id` bigint(15) NOT NULL /*T![auto_rand] AUTO_RANDOM(5, 54) */ COMMENT '主键id',
                                               `account_number` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺账号',
                                               `item_id` bigint(15) NOT NULL COMMENT '商品id',
                                               `local_single_discount_id` bigint(15) NOT NULL COMMENT '本地单品折扣主键id',
                                               `plat_single_discount_id` bigint(15) NOT NULL COMMENT '平台单品折扣主键id',
                                               `store_club_discount_rate` int(11) DEFAULT NULL COMMENT '粉丝折扣率',
                                               `discount` int(11) DEFAULT NULL COMMENT '折扣率',
                                               `buy_max_num` int(11) DEFAULT NULL COMMENT '限购数',
                                               `create_time` datetime NOT NULL COMMENT '创建时间',
                                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                               `club_discount_type` int(11) DEFAULT NULL COMMENT '定向人群额外折扣类型,0=无,1=店铺粉丝',
                                               PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
                                               KEY `idx_account_number` (`account_number`),
                                               KEY `idx_item_id` (`item_id`),
                                               KEY `idx_local_single_discount_id` (`local_single_discount_id`),
                                               KEY `idx_plat_single_discount_id` (`plat_single_discount_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci  COMMENT='smt 单品折扣参加的商品';

-------------------------------2024.7.15版本 cxd---------------------------

-------------------------------2024.8.08版本 cxd---------------------------

ALTER TABLE `publish_tidb`.`aliexpress_activity_registration`
    ADD COLUMN `pro_count` int NULL DEFAULT NULL COMMENT '总产品数';

ALTER TABLE `publish_tidb`.`aliexpress_activity_registration`
    ADD COLUMN `submit_pro_count` int(11) NULL DEFAULT NULL COMMENT '报名产品数' ;


ALTER TABLE `publish_smt`.`aliexpress_marketing_config`
    ADD COLUMN `account_group_id` text  NULL COMMENT '店铺分组id' AFTER `account_group_name`;



ALTER TABLE `publish_smt`.`smt_account_group`
    ADD COLUMN `sync_time` datetime NULL COMMENT '同步时间' AFTER `update_by`;
ALTER TABLE `publish_smt`.`smt_account_group`
    ADD INDEX `index_update_by`(`update_by`) USING BTREE,
ADD INDEX `index_update_date`(`update_date`) USING BTREE;

ALTER TABLE `publish_smt`.`aliexpress_marketing_config_log`
    ADD COLUMN `type` int NULL COMMENT '1=营销配置，2=店铺分组' AFTER `operate_time`;

update aliexpress_marketing_config_log  set type=1;

ALTER TABLE `publish_smt`.`aliexpress_marketing_config_log`
    ADD INDEX `idx_type`(`type`) USING BTREE;


ALTER TABLE `publish_smt`.`aliexpress_config`
ADD COLUMN `delivery_30d_rate` double DEFAULT NULL COMMENT '近30天发货率',
ADD COLUMN `delivery_30d_rate_update_time` datetime DEFAULT NULL COMMENT '近30天发货率更新时间';


-------------------------------2024.8.08版本 cxd---------------------------


CREATE TABLE `aliexpress_category_attribute` (
     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `category_id` int(11) NOT NULL COMMENT '速卖通类目ID',
     `attribute_id` bigint(20) DEFAULT NULL COMMENT '属性ID',
     `attribute_name_zh` varchar(255) DEFAULT NULL COMMENT '属性中文名称',
     `attribute_name_en` varchar(255) DEFAULT NULL COMMENT '属性英文名称',
     `attribute_type` varchar(50) DEFAULT NULL COMMENT '属性类型',
     `required` tinyint(1) DEFAULT NULL COMMENT '是否必填',
     `input_type` varchar(50) DEFAULT NULL COMMENT '输入类型',
     `values_json` TEXT COMMENT '属性值JSON字符串',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
     PRIMARY KEY (`id`),
     KEY `idx_category_id` (`category_id`),
     KEY `idx_attribute_id` (`attribute_id`),
     KEY `idx_attribute_type` (`attribute_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='速卖通类目属性表';