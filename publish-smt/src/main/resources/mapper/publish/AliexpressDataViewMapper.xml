<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressDataViewMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressDataView" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="sale_user" property="saleUser" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="template_num" property="templateNum" jdbcType="INTEGER" />
    <result column="listing_num" property="listingNum" jdbcType="INTEGER" />
    <result column="count_num1" property="countNum1" jdbcType="VARCHAR" />
    <result column="count_num2" property="countNum2" jdbcType="VARCHAR" />
    <result column="count_num3" property="countNum3" jdbcType="VARCHAR" />
    <result column="count_num4" property="countNum4" jdbcType="VARCHAR" />
    <result column="count_num5" property="countNum5" jdbcType="VARCHAR" />
    <result column="count_num6" property="countNum6" jdbcType="VARCHAR" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
  </resultMap>


  <sql id="Example_Where_Clause" >
    <where >
      <include refid="Example_Clause" />
    </where>
  </sql>

  <sql id="Example_Clause" >
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, sale_user, `type`, `status`, template_num, listing_num, count_num1, 
    count_num2, count_num3, count_num4, count_num5, count_num6, creation_date, created_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressDataViewExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_data_view

    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="loginSale != null and loginSale != '' " >
        AND sale_user = #{loginSale,jdbcType=VARCHAR}
      </if>

      <if test="loginSaleAccountAuth != null" >
        AND account_number IN
        <foreach collection="loginSaleAccountAuth" item="account" open="(" close=")" separator="," >
          #{account}
        </foreach>
      </if>

    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_data_view
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_data_view
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressDataView" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_data_view (account_number, sale_user, `type`, 
      `status`, template_num, listing_num, 
      count_num1, count_num2, count_num3, 
      count_num4, count_num5, count_num6, 
      creation_date, created_by)
    values (#{accountNumber,jdbcType=VARCHAR}, #{saleUser,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{templateNum,jdbcType=INTEGER}, #{listingNum,jdbcType=INTEGER}, 
      #{countNum1,jdbcType=VARCHAR}, #{countNum2,jdbcType=VARCHAR}, #{countNum3,jdbcType=VARCHAR}, 
      #{countNum4,jdbcType=VARCHAR}, #{countNum5,jdbcType=VARCHAR}, #{countNum6,jdbcType=VARCHAR}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressDataViewExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_data_view
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="loginSale != null and loginSale != '' " >
        AND sale_user = #{loginSale,jdbcType=VARCHAR}
      </if>

      <if test="loginSaleAccountAuth != null" >
        AND account_number IN
        <foreach collection="loginSaleAccountAuth" item="account" open="(" close=")" separator="," >
          #{account}
        </foreach>
      </if>

    </where>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_data_view
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.saleUser != null" >
        sale_user = #{record.saleUser,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.templateNum != null" >
        template_num = #{record.templateNum,jdbcType=INTEGER},
      </if>
      <if test="record.listingNum != null" >
        listing_num = #{record.listingNum,jdbcType=INTEGER},
      </if>
      <if test="record.countNum1 != null" >
        count_num1 = #{record.countNum1,jdbcType=VARCHAR},
      </if>
      <if test="record.countNum2 != null" >
        count_num2 = #{record.countNum2,jdbcType=VARCHAR},
      </if>
      <if test="record.countNum3 != null" >
        count_num3 = #{record.countNum3,jdbcType=VARCHAR},
      </if>
      <if test="record.countNum4 != null" >
        count_num4 = #{record.countNum4,jdbcType=VARCHAR},
      </if>
      <if test="record.countNum5 != null" >
        count_num5 = #{record.countNum5,jdbcType=VARCHAR},
      </if>
      <if test="record.countNum6 != null" >
        count_num6 = #{record.countNum6,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressDataView" >
    update aliexpress_data_view
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="saleUser != null" >
        sale_user = #{saleUser,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="templateNum != null" >
        template_num = #{templateNum,jdbcType=INTEGER},
      </if>
      <if test="listingNum != null" >
        listing_num = #{listingNum,jdbcType=INTEGER},
      </if>
      <if test="countNum1 != null" >
        count_num1 = #{countNum1,jdbcType=VARCHAR},
      </if>
      <if test="countNum2 != null" >
        count_num2 = #{countNum2,jdbcType=VARCHAR},
      </if>
      <if test="countNum3 != null" >
        count_num3 = #{countNum3,jdbcType=VARCHAR},
      </if>
      <if test="countNum4 != null" >
        count_num4 = #{countNum4,jdbcType=VARCHAR},
      </if>
      <if test="countNum5 != null" >
        count_num5 = #{countNum5,jdbcType=VARCHAR},
      </if>
      <if test="countNum6 != null" >
        count_num6 = #{countNum6,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    <foreach collection="recordList" item="record" open="" separator=";" close=";">
      insert into aliexpress_data_view (account_number, sale_user, `type`,
      `status`, template_num, listing_num,
      count_num1, count_num2, count_num3,
      count_num4, count_num5, count_num6,
      creation_date, created_by)
      values (#{record.accountNumber,jdbcType=VARCHAR}, #{record.saleUser,jdbcType=VARCHAR}, #{record.type,jdbcType=INTEGER},
      #{record.status,jdbcType=INTEGER}, #{record.templateNum,jdbcType=INTEGER}, #{record.listingNum,jdbcType=INTEGER},
      #{record.countNum1,jdbcType=VARCHAR}, #{record.countNum2,jdbcType=VARCHAR}, #{record.countNum3,jdbcType=VARCHAR},
      #{record.countNum4,jdbcType=VARCHAR}, #{record.countNum5,jdbcType=VARCHAR}, #{record.countNum6,jdbcType=VARCHAR},
      #{record.creationDate,jdbcType=TIMESTAMP}, #{record.createdBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--定时任务-->
  <select id="countSuccessTemplate" resultMap="BaseResultMap">
    SELECT creator sale_user,IFNULL(aliexpress_account_number,"") account_number,1 type,
    10 `status`,
    COUNT(1) template_num
    FROM aliexpress_template
    WHERE creator is not null AND is_parent = 0 and template_status = 3
    AND create_time <![CDATA[>=]]> #{beginTime,jdbcType=VARCHAR}
    AND create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
    GROUP BY creator, aliexpress_account_number
  </select>


  <!--定时任务-->
  <select id="countTemplate" resultMap="BaseResultMap">
    SELECT creator sale_user,IFNULL(aliexpress_account_number,"") account_number,1 type,
    1 `status`,
    COUNT(1) template_num
    FROM aliexpress_template
    WHERE creator is not null AND is_parent = 0
    AND create_time <![CDATA[>=]]> #{beginTime,jdbcType=VARCHAR}
    AND create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}

    GROUP BY creator,aliexpress_account_number
  </select>


<!--列表查询-->
  <select id="countTotalTemplate" parameterType="com.estone.erp.publish.smt.model.AliexpressDataViewCriteria" resultType="java.lang.Integer" >
    select count(*) from (
    select creator,aliexpress_account_number,COUNT(1) template_num
    FROM aliexpress_template
    WHERE creator is not null AND is_parent = 0 AND template_status = 3
    <if test="search.dateFrom != null" >
      AND create_time <![CDATA[>=]]> #{search.dateFrom,jdbcType=TIMESTAMP}
    </if>
    <if test="search.dateTo != null" >
      AND create_time <![CDATA[<=]]> #{search.dateTo,jdbcType=TIMESTAMP}
    </if>

    <if test="search.loginSale != null and search.loginSale != '' " >
      AND creator = #{search.loginSale,jdbcType=VARCHAR}
    </if>

    <if test="search.saleList != null" >
      AND creator IN
      <foreach collection="search.saleList" item="sale" open="(" close=")" separator="," >
        #{sale}
      </foreach>
    </if>
    <if test="search.accountList != null" >
      AND aliexpress_account_number IN
      <foreach collection="search.accountList" item="account" open="(" close=")" separator="," >
        #{account}
      </foreach>
    </if>

    <if test="search.loginSaleAccountAuth != null" >
      AND aliexpress_account_number IN
      <foreach collection="search.loginSaleAccountAuth" item="account" open="(" close=")" separator="," >
        #{account}
      </foreach>
    </if>

    GROUP BY creator,aliexpress_account_number
    ) result
    WHERE 1 = 1
    <if test="search.numFrom != null" >
      AND result.template_num <![CDATA[>=]]> #{search.numFrom,jdbcType=INTEGER}
    </if>
    <if test="search.numTo != null" >
      AND result.template_num <![CDATA[<=]]> #{search.numTo,jdbcType=INTEGER}
    </if>
  </select>

<!--列表查询-->
  <select id="selectTotalTemplate" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressDataViewCriteria" >
    select * from (
    select aliexpress_account_number account_number, creator sale_user, 1 type, 2 `status`,COUNT(1) template_num
    FROM aliexpress_template
    WHERE creator is not null AND is_parent = 0 AND template_status = 3
      <if test="search.dateFrom != null" >
        AND create_time <![CDATA[>=]]> #{search.dateFrom,jdbcType=TIMESTAMP}
      </if>
      <if test="search.dateTo != null" >
        AND create_time <![CDATA[<=]]> #{search.dateTo,jdbcType=TIMESTAMP}
      </if>

      <if test="search.loginSale != null and search.loginSale != '' " >
        AND creator = #{search.loginSale,jdbcType=VARCHAR}
      </if>

      <if test="search.saleList != null" >
        AND creator IN
        <foreach collection="search.saleList" item="sale" open="(" close=")" separator="," >
          #{sale}
        </foreach>
      </if>
      <if test="search.accountList != null" >
        AND aliexpress_account_number IN
        <foreach collection="search.accountList" item="account" open="(" close=")" separator="," >
          #{account}
        </foreach>
      </if>

      <if test="search.loginSaleAccountAuth != null" >
        AND aliexpress_account_number IN
        <foreach collection="search.loginSaleAccountAuth" item="account" open="(" close=")" separator="," >
          #{account}
        </foreach>
      </if>
    GROUP BY creator,aliexpress_account_number
    ORDER by template_num desc
    <if test="search.limit != null" >
      <if test="search.offset != null" >
        limit ${search.offset}, ${search.limit}
      </if>
      <if test="search.offset == null" >
        limit ${search.limit}
      </if>
    </if>
    ) result
    WHERE 1 = 1
    <if test="search.numFrom != null" >
      AND result.template_num <![CDATA[>=]]> #{search.numFrom,jdbcType=INTEGER}
    </if>
    <if test="search.numTo != null" >
      AND result.template_num <![CDATA[<=]]> #{search.numTo,jdbcType=INTEGER}
    </if>
    <if test="search.orderBy != null and search.orderBy != ''" >
      ORDER BY ${search.orderBy}
    </if>
  </select>

  <!-- 查询销售上架list  按照销售 分组-->
  <select id="selectSaleList" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressDataViewCriteria" >
    select * FROM
    (
    select account_number, sale_user, type, `status`, SUM(listing_num) listing_num
    from
    aliexpress_data_view
    WHERE `status` = 1
    and type = 2
    <if test="search.dateFrom != null">AND creation_date <![CDATA[>=]]> #{search.dateFrom,jdbcType=TIMESTAMP}
    </if>
      <if test
                  ="search.dateTo != null" >AND creation_date <![CDATA[<=]]> #{search.dateTo,jdbcType=TIMESTAMP}
      </if>

      GROUP BY sale_user
      ORDER BY listing_num desc
    ) temp
    where 1 = 1
    <if test="search.unStandarNum != null" >
      AND listing_num <![CDATA[<]]> #{search.unStandarNum,jdbcType=INTEGER}
    </if>
  </select>


</mapper>