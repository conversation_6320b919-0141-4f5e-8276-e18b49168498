<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressCarTypeMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressCarType" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="leaf_category_id" property="leafCategoryId" jdbcType="INTEGER" />
    <result column="type_id" property="typeId" jdbcType="BIGINT" />
    <result column="parent_type_id" property="parentTypeId" jdbcType="BIGINT" />
    <result column="type_level" property="typeLevel" jdbcType="INTEGER" />
    <result column="car_type" property="carType" jdbcType="VARCHAR" />
    <result column="param1" property="param1" jdbcType="INTEGER" />
    <result column="param2" property="param2" jdbcType="VARCHAR" />
    <result column="attribute_json" property="attributeJson" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, leaf_category_id, type_id, parent_type_id, type_level, car_type, param1, param2, 
    attribute_json, create_by, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressCarTypeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_car_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_car_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_car_type
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="truncateAliexpressCarType" >
    truncate table `aliexpress_car_type`
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressCarType" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_car_type (leaf_category_id, type_id, parent_type_id, 
      type_level, car_type, param1, 
      param2, attribute_json, create_by, 
      create_time)
    values (#{leafCategoryId,jdbcType=INTEGER}, #{typeId,jdbcType=BIGINT}, #{parentTypeId,jdbcType=BIGINT}, 
      #{typeLevel,jdbcType=INTEGER}, #{carType,jdbcType=VARCHAR}, #{param1,jdbcType=INTEGER}, 
      #{param2,jdbcType=VARCHAR}, #{attributeJson,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressCarTypeExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_car_type
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_car_type
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.leafCategoryId != null" >
        leaf_category_id = #{record.leafCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.typeId != null" >
        type_id = #{record.typeId,jdbcType=BIGINT},
      </if>
      <if test="record.parentTypeId != null" >
        parent_type_id = #{record.parentTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.typeLevel != null" >
        type_level = #{record.typeLevel,jdbcType=INTEGER},
      </if>
      <if test="record.carType != null" >
        car_type = #{record.carType,jdbcType=VARCHAR},
      </if>
      <if test="record.param1 != null" >
        param1 = #{record.param1,jdbcType=INTEGER},
      </if>
      <if test="record.param2 != null" >
        param2 = #{record.param2,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeJson != null" >
        attribute_json = #{record.attributeJson,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressCarType" >
    update aliexpress_car_type
    <set >
      <if test="leafCategoryId != null" >
        leaf_category_id = #{leafCategoryId,jdbcType=INTEGER},
      </if>
      <if test="typeId != null" >
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="parentTypeId != null" >
        parent_type_id = #{parentTypeId,jdbcType=BIGINT},
      </if>
      <if test="typeLevel != null" >
        type_level = #{typeLevel,jdbcType=INTEGER},
      </if>
      <if test="carType != null" >
        car_type = #{carType,jdbcType=VARCHAR},
      </if>
      <if test="param1 != null" >
        param1 = #{param1,jdbcType=INTEGER},
      </if>
      <if test="param2 != null" >
        param2 = #{param2,jdbcType=VARCHAR},
      </if>
      <if test="attributeJson != null" >
        attribute_json = #{attributeJson,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>