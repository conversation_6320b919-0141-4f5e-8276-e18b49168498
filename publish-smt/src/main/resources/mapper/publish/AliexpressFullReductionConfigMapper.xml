<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressFullReductionConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressFullReductionConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="store" property="store" jdbcType="VARCHAR" />
    <result column="activity_type" property="activityType" jdbcType="INTEGER" />
    <result column="activity_name" property="activityName" jdbcType="VARCHAR" />
    <result column="activity_id" property="activityId" jdbcType="VARCHAR" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="commit_status" property="commitStatus" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="sale_manager" property="saleManager" jdbcType="VARCHAR" />
    <result column="sale_manager_leader" property="saleManagerLeader" jdbcType="VARCHAR" />
    <result column="sales_supervisor" property="salesSupervisor" jdbcType="VARCHAR" />
    <result column="activity_scope" property="activityScope" jdbcType="VARCHAR" />
    <result column="activity_country" property="activityCountry" jdbcType="VARCHAR" />
    <result column="activity_detail" property="activityDetail" jdbcType="VARCHAR" />
    <result column="error_message" property="errorMessage" jdbcType="VARCHAR" />
    <result column="activity_detail_context" property="activityDetailContext" jdbcType="VARCHAR" />
    <result column="activity_status" property="activityStatus" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="commit_time" property="commitTime" jdbcType="TIMESTAMP" />
    <result column="is_accumulation" property="isAccumulation" jdbcType="INTEGER" />
    <result column="is_update" property="isUpdate" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, store, activity_type, activity_name, activity_id, rule_name, commit_status, activity_start_time, 
    activity_end_time, activity_scope, activity_country, activity_detail, error_message, activity_detail_context, activity_status,
    create_by, create_time, commit_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_full_reduction_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_full_reduction_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_full_reduction_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_full_reduction_config (store, activity_type, activity_name, 
      activity_id, rule_name, commit_status, 
      activity_start_time, activity_end_time, 
      sale_manager, sale_manager_leader, sales_supervisor, 
      activity_scope, activity_country, activity_detail, 
      error_message, activity_detail_context, activity_status, 
      create_by, create_time, commit_time
      )
    values (#{store,jdbcType=VARCHAR}, #{activityType,jdbcType=INTEGER}, #{activityName,jdbcType=VARCHAR}, 
      #{activityId,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{commitStatus,jdbcType=VARCHAR}, 
      #{activityStartTime,jdbcType=TIMESTAMP}, #{activityEndTime,jdbcType=TIMESTAMP}, 
      #{saleManager,jdbcType=VARCHAR}, #{saleManagerLeader,jdbcType=VARCHAR}, #{salesSupervisor,jdbcType=VARCHAR}, 
      #{activityScope,jdbcType=VARCHAR}, #{activityCountry,jdbcType=VARCHAR}, #{activityDetail,jdbcType=VARCHAR}, 
      #{errorMessage,jdbcType=VARCHAR}, #{activityDetailContext,jdbcType=VARCHAR}, #{activityStatus,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{commitTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfigExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_full_reduction_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_full_reduction_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.store != null" >
        store = #{record.store,jdbcType=VARCHAR},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=INTEGER},
      </if>
      <if test="record.activityName != null" >
        activity_name = #{record.activityName,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.commitStatus != null" >
        commit_status = #{record.commitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStartTime != null" >
        activity_start_time = #{record.activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityEndTime != null" >
        activity_end_time = #{record.activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleManager != null" >
        sale_manager = #{record.saleManager,jdbcType=VARCHAR},
      </if>
      <if test="record.saleManagerLeader != null" >
        sale_manager_leader = #{record.saleManagerLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.salesSupervisor != null" >
        sales_supervisor = #{record.salesSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="record.activityScope != null" >
        activity_scope = #{record.activityScope,jdbcType=VARCHAR},
      </if>
      <if test="record.activityCountry != null" >
        activity_country = #{record.activityCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDetail != null" >
        activity_detail = #{record.activityDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMessage != null" >
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDetailContext != null" >
        activity_detail_context = #{record.activityDetailContext,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStatus != null" >
        activity_status = #{record.activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commitTime != null" >
        commit_time = #{record.commitTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfig" >
    update aliexpress_full_reduction_config
    <set >
      <if test="store != null" >
        store = #{store,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=INTEGER},
      </if>
      <if test="activityName != null" >
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="commitStatus != null" >
        commit_status = #{commitStatus,jdbcType=VARCHAR},
      </if>
      <if test="activityStartTime != null" >
        activity_start_time = #{activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityEndTime != null" >
        activity_end_time = #{activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saleManager != null" >
        sale_manager = #{saleManager,jdbcType=VARCHAR},
      </if>
      <if test="saleManagerLeader != null" >
        sale_manager_leader = #{saleManagerLeader,jdbcType=VARCHAR},
      </if>
      <if test="salesSupervisor != null" >
        sales_supervisor = #{salesSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="activityScope != null" >
        activity_scope = #{activityScope,jdbcType=VARCHAR},
      </if>
      <if test="activityCountry != null" >
        activity_country = #{activityCountry,jdbcType=VARCHAR},
      </if>
      <if test="activityDetail != null" >
        activity_detail = #{activityDetail,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null" >
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="activityDetailContext != null" >
        activity_detail_context = #{activityDetailContext,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        activity_status = #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitTime != null" >
        commit_time = #{commitTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_full_reduction_config (store, activity_type, activity_name,
    activity_id, rule_name, commit_status,
    activity_start_time, activity_end_time,
    activity_scope, activity_country, activity_detail,
    error_message, activity_detail_context, activity_status,
    create_by, create_time, commit_time,is_accumulation
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.store,jdbcType=VARCHAR}, #{item.activityType,jdbcType=INTEGER}, #{item.activityName,jdbcType=VARCHAR},
      #{item.activityId,jdbcType=VARCHAR}, #{item.ruleName,jdbcType=VARCHAR}, #{item.commitStatus,jdbcType=VARCHAR},
      #{item.activityStartTime,jdbcType=TIMESTAMP}, #{item.activityEndTime,jdbcType=TIMESTAMP},
      #{item.activityScope,jdbcType=VARCHAR}, #{item.activityCountry,jdbcType=VARCHAR}, #{item.activityDetail,jdbcType=VARCHAR},
      #{item.errorMessage,jdbcType=VARCHAR}, #{item.activityDetailContext,jdbcType=VARCHAR}, #{item.activityStatus,jdbcType=VARCHAR},
      #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.commitTime,jdbcType=TIMESTAMP}, #{item.isAccumulation,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <update id="batchUpdate" >
    <foreach collection="list" item="record" open="" separator=";" close=";">
      update aliexpress_full_reduction_config
      <set >
      <if test="record.store != null" >
        store = #{record.store,jdbcType=VARCHAR},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=INTEGER},
      </if>
      <if test="record.activityName != null" >
        activity_name = #{record.activityName,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.commitStatus != null" >
        commit_status = #{record.commitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStartTime != null" >
        activity_start_time = #{record.activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityEndTime != null" >
        activity_end_time = #{record.activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleManager != null" >
        sale_manager = #{record.saleManager,jdbcType=VARCHAR},
      </if>
      <if test="record.saleManagerLeader != null" >
        sale_manager_leader = #{record.saleManagerLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.salesSupervisor != null" >
        sales_supervisor = #{record.salesSupervisor,jdbcType=VARCHAR},
      </if>
      <if test="record.activityScope != null" >
        activity_scope = #{record.activityScope,jdbcType=VARCHAR},
      </if>
      <if test="record.activityCountry != null" >
        activity_country = #{record.activityCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDetail != null" >
        activity_detail = #{record.activityDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMessage != null" >
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDetailContext != null" >
        activity_detail_context = #{record.activityDetailContext,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStatus != null" >
        activity_status = #{record.activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commitTime != null" >
        commit_time = #{record.commitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isUpdate != null" >
        is_update = #{record.isUpdate,jdbcType=INTEGER},
      </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <select id="selectFieldByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfigExample" >
    select id,store,commit_status,activity_name,activity_start_time,activity_end_time,activity_type,activity_detail,is_accumulation
    from aliexpress_full_reduction_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectCrawlFieldByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressFullReductionConfigExample" >
    select id,store,activity_name,activity_start_time,activity_end_time,activity_type,activity_detail,is_accumulation,activity_id,is_update
    from aliexpress_full_reduction_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="batchUpdateCommitInfoByActivityId" >
    <foreach collection="list" item="record" open="" separator=";" close=";">
      update aliexpress_full_reduction_config
      <set >
        <if test="record.commitStatus != null" >
          commit_status = #{record.commitStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.errorMessage != null" >
          error_message = #{record.errorMessage,jdbcType=VARCHAR},
        </if>
        <if test="record.activityStatus != null" >
          activity_status = #{record.activityStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.commitTime != null" >
          commit_time = #{record.commitTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where activity_id = #{record.activityId,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="batchUpdateCommitInfo" >
    <foreach collection="list" item="record" open="" separator=";" close=";">
      update aliexpress_full_reduction_config
      <set >
        <if test="record.activityId != null" >
          activity_id = #{record.activityId,jdbcType=VARCHAR},
        </if>
        <if test="record.commitStatus != null" >
          commit_status = #{record.commitStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.errorMessage != null" >
          error_message = #{record.errorMessage,jdbcType=VARCHAR},
        </if>
        <if test="record.activityStatus != null" >
          activity_status = #{record.activityStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.commitTime != null" >
          commit_time = #{record.commitTime,jdbcType=TIMESTAMP},
        </if>
        is_update = NULL,
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="stopOreffectActivity" >
    update aliexpress_full_reduction_config
    <set >
      <if test="type == 2" >
        commit_status = "待暂停",
      </if>
      <if test="type == 3" >
        commit_status = "待生效",
      </if>
      is_update = #{type,jdbcType=INTEGER}
    </set>
    where id in
    <foreach collection="list" item="record" open="(" separator="," close=")">
      #{record,jdbcType=INTEGER}
    </foreach>
  </update>
  <select id="selectCountByCommitStatus"
          resultType="com.estone.erp.publish.smt.model.dto.AliexpressProductLogCountDto">
    select store as accountNumber,
           rule_name as ruleName,
           COUNT(*) as totalCount,
            IFNULL(SUM(CASE WHEN commit_status = '提交成功' THEN 1 ELSE 0 END),0) AS successCount,
           IFNULL(SUM(CASE WHEN commit_status = '提交失败' THEN 1 ELSE 0 END),0) AS failureCount
    from aliexpress_full_reduction_config
    where DATE(create_time) = CURDATE() - INTERVAL 1 DAY
    GROUP BY
      store,
      rule_name
  </select>
</mapper>