<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressProductMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressProduct" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="detail" property="detail" jdbcType="VARCHAR" />
    <result column="aeop_ae_product_skus_json" property="aeopAeProductSkusJson" jdbcType="VARCHAR" />
    <result column="delivery_time" property="deliveryTime" jdbcType="INTEGER" />
    <result column="promise_template_id" property="promiseTemplateId" jdbcType="BIGINT" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="category_table_id" property="categoryTableId" jdbcType="BIGINT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="subject" property="subject" jdbcType="VARCHAR" />
    <result column="product_price" property="productPrice" jdbcType="DOUBLE" />
    <result column="freight_template_id" property="freightTemplateId" jdbcType="BIGINT" />
    <result column="image_urls" property="imageUrls" jdbcType="VARCHAR" />
    <result column="product_unit" property="productUnit" jdbcType="INTEGER" />
    <result column="package_type" property="packageType" jdbcType="BIT" />
    <result column="lot_num" property="lotNum" jdbcType="INTEGER" />
    <result column="package_length" property="packageLength" jdbcType="INTEGER" />
    <result column="package_width" property="packageWidth" jdbcType="INTEGER" />
    <result column="package_height" property="packageHeight" jdbcType="INTEGER" />
    <result column="gross_weight" property="grossWeight" jdbcType="VARCHAR" />
    <result column="is_pack_sell" property="isPackSell" jdbcType="BIT" />
    <result column="is_wholesale" property="isWholesale" jdbcType="BIT" />
    <result column="base_unit" property="baseUnit" jdbcType="INTEGER" />
    <result column="add_unit" property="addUnit" jdbcType="INTEGER" />
    <result column="add_weight" property="addWeight" jdbcType="VARCHAR" />
    <result column="ws_valid_num" property="wsValidNum" jdbcType="INTEGER" />
    <result column="aeop_ae_product_propertys_json" property="aeopAeProductPropertysJson" jdbcType="VARCHAR" />
    <result column="bulk_order" property="bulkOrder" jdbcType="INTEGER" />
    <result column="bulk_discount" property="bulkDiscount" jdbcType="INTEGER" />
    <result column="size_chart_id" property="sizeChartId" jdbcType="BIGINT" />
    <result column="reduce_strategy" property="reduceStrategy" jdbcType="VARCHAR" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="group_ids" property="groupIds" jdbcType="VARCHAR" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="mobile_detail" property="mobileDetail" jdbcType="VARCHAR" />
    <result column="coupon_start_date" property="couponStartDate" jdbcType="TIMESTAMP" />
    <result column="coupon_end_date" property="couponEndDate" jdbcType="TIMESTAMP" />
    <result column="aeop_national_quote_configuration" property="aeopNationalQuoteConfiguration" jdbcType="VARCHAR" />
    <result column="aeop_ae_multimedia" property="aeopAeMultimedia" jdbcType="VARCHAR" />
    <result column="editor" property="editor" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="last_edit_time" property="lastEditTime" jdbcType="TIMESTAMP" />
    <result column="aliexpress_account_number" property="aliexpressAccountNumber" jdbcType="VARCHAR" />
    <result column="display_image_url" property="displayImageUrl" jdbcType="VARCHAR" />
    <result column="owner_member_id" property="ownerMemberId" jdbcType="VARCHAR" />
    <result column="owner_member_seq" property="ownerMemberSeq" jdbcType="INTEGER" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="src" property="src" jdbcType="VARCHAR" />
    <result column="ws_offline_date" property="wsOfflineDate" jdbcType="TIMESTAMP" />
    <result column="ws_display" property="wsDisplay" jdbcType="VARCHAR" />
    <result column="product_status_type" property="productStatusType" jdbcType="VARCHAR" />
    <result column="is_image_dynamic" property="isImageDynamic" jdbcType="BIT" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    <result column="product_min_price" property="productMinPrice" jdbcType="DOUBLE" />
    <result column="product_max_price" property="productMaxPrice" jdbcType="DOUBLE" />
    <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP" />
    <result column="article_numbers" property="articleNumbers" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
    <result column="ipm_sku_stock" property="ipmSkuStock" jdbcType="INTEGER" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="sku_display_img" property="skuDisplayImg" jdbcType="VARCHAR" />
    <result column="sku_price" property="skuPrice" jdbcType="DOUBLE" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="sku_discount_price" property="skuDiscountPrice" jdbcType="DOUBLE" />
    <result column="barcode" property="barcode" jdbcType="VARCHAR" />
    <result column="aeop_s_k_u_property_list" property="aeopSKUPropertyList" jdbcType="VARCHAR" />
    <result column="aeop_s_k_u_national_discount_price_list" property="aeopSKUNationalDiscountPriceList" jdbcType="VARCHAR" />
    <result column="is_variant" property="isVariant" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, detail, aeop_ae_product_skus_json, delivery_time, promise_template_id, category_name, 
    category_table_id, category_id, subject, product_price, freight_template_id, image_urls, 
    product_unit, package_type, lot_num, package_length, package_width, package_height, 
    gross_weight, is_pack_sell, is_wholesale, base_unit, add_unit, add_weight, ws_valid_num, 
    aeop_ae_product_propertys_json, bulk_order, bulk_discount, size_chart_id, reduce_strategy, 
    group_id, group_ids, currency_code, mobile_detail, coupon_start_date, coupon_end_date, 
    aeop_national_quote_configuration, aeop_ae_multimedia, editor, create_time, last_edit_time, 
    aliexpress_account_number, display_image_url, owner_member_id, owner_member_seq, 
    product_id, src, ws_offline_date, ws_display, product_status_type, is_image_dynamic, 
    gmt_create, gmt_modified, product_min_price, product_max_price, last_sync_time, article_numbers, 
    sku_id, ipm_sku_stock, sku_code, sku_display_img, sku_price, article_number, sku_discount_price, 
    barcode, aeop_s_k_u_property_list, aeop_s_k_u_national_discount_price_list, is_variant
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from aliexpress_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_product
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_product
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressProduct" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_product (detail, aeop_ae_product_skus_json, delivery_time, 
      promise_template_id, category_name, category_table_id, 
      category_id, subject, product_price, 
      freight_template_id, image_urls, product_unit, 
      package_type, lot_num, package_length, 
      package_width, package_height, gross_weight, 
      is_pack_sell, is_wholesale, base_unit, 
      add_unit, add_weight, ws_valid_num, 
      aeop_ae_product_propertys_json, bulk_order, 
      bulk_discount, size_chart_id, reduce_strategy, 
      group_id, group_ids, currency_code, 
      mobile_detail, coupon_start_date, coupon_end_date, 
      aeop_national_quote_configuration, aeop_ae_multimedia, 
      editor, create_time, last_edit_time, 
      aliexpress_account_number, display_image_url, 
      owner_member_id, owner_member_seq, product_id, 
      src, ws_offline_date, ws_display, 
      product_status_type, is_image_dynamic, gmt_create, 
      gmt_modified, product_min_price, product_max_price, 
      last_sync_time, article_numbers, sku_id, 
      ipm_sku_stock, sku_code, sku_display_img, 
      sku_price, article_number, sku_discount_price, 
      barcode, aeop_s_k_u_property_list, aeop_s_k_u_national_discount_price_list, is_variant
      )
    values (#{detail,jdbcType=VARCHAR}, #{aeopAeProductSkusJson,jdbcType=VARCHAR}, #{deliveryTime,jdbcType=INTEGER}, 
      #{promiseTemplateId,jdbcType=BIGINT}, #{categoryName,jdbcType=VARCHAR}, #{categoryTableId,jdbcType=BIGINT}, 
      #{categoryId,jdbcType=INTEGER}, #{subject,jdbcType=VARCHAR}, #{productPrice,jdbcType=DOUBLE}, 
      #{freightTemplateId,jdbcType=BIGINT}, #{imageUrls,jdbcType=VARCHAR}, #{productUnit,jdbcType=INTEGER}, 
      #{packageType,jdbcType=BIT}, #{lotNum,jdbcType=INTEGER}, #{packageLength,jdbcType=INTEGER}, 
      #{packageWidth,jdbcType=INTEGER}, #{packageHeight,jdbcType=INTEGER}, #{grossWeight,jdbcType=VARCHAR}, 
      #{isPackSell,jdbcType=BIT}, #{isWholesale,jdbcType=BIT}, #{baseUnit,jdbcType=INTEGER}, 
      #{addUnit,jdbcType=INTEGER}, #{addWeight,jdbcType=VARCHAR}, #{wsValidNum,jdbcType=INTEGER}, 
      #{aeopAeProductPropertysJson,jdbcType=VARCHAR}, #{bulkOrder,jdbcType=INTEGER}, 
      #{bulkDiscount,jdbcType=INTEGER}, #{sizeChartId,jdbcType=BIGINT}, #{reduceStrategy,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=BIGINT}, #{groupIds,jdbcType=VARCHAR}, #{currencyCode,jdbcType=VARCHAR}, 
      #{mobileDetail,jdbcType=VARCHAR}, #{couponStartDate,jdbcType=TIMESTAMP}, #{couponEndDate,jdbcType=TIMESTAMP}, 
      #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR}, #{aeopAeMultimedia,jdbcType=VARCHAR}, 
      #{editor,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{lastEditTime,jdbcType=TIMESTAMP}, 
      #{aliexpressAccountNumber,jdbcType=VARCHAR}, #{displayImageUrl,jdbcType=VARCHAR}, 
      #{ownerMemberId,jdbcType=VARCHAR}, #{ownerMemberSeq,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, 
      #{src,jdbcType=VARCHAR}, #{wsOfflineDate,jdbcType=TIMESTAMP}, #{wsDisplay,jdbcType=VARCHAR}, 
      #{productStatusType,jdbcType=VARCHAR}, #{isImageDynamic,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{productMinPrice,jdbcType=DOUBLE}, #{productMaxPrice,jdbcType=DOUBLE}, 
      #{lastSyncTime,jdbcType=TIMESTAMP}, #{articleNumbers,jdbcType=VARCHAR}, #{skuId,jdbcType=VARCHAR}, 
      #{ipmSkuStock,jdbcType=INTEGER}, #{skuCode,jdbcType=VARCHAR}, #{skuDisplayImg,jdbcType=VARCHAR}, 
      #{skuPrice,jdbcType=DOUBLE}, #{articleNumber,jdbcType=VARCHAR}, #{skuDiscountPrice,jdbcType=DOUBLE}, 
      #{barcode,jdbcType=VARCHAR}, #{aeopSKUPropertyList,jdbcType=VARCHAR}, #{aeopSKUNationalDiscountPriceList,jdbcType=VARCHAR},
      #{isVariant,jdbcType=TINYINT}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="countProduct" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample" resultType="java.lang.Integer" >
    select count(DISTINCT product_id) from aliexpress_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_product
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.detail != null" >
        detail = #{record.detail,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{record.aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryTime != null" >
        delivery_time = #{record.deliveryTime,jdbcType=INTEGER},
      </if>
      <if test="record.promiseTemplateId != null" >
        promise_template_id = #{record.promiseTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null" >
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryTableId != null" >
        category_table_id = #{record.categoryTableId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.subject != null" >
        subject = #{record.subject,jdbcType=VARCHAR},
      </if>
      <if test="record.productPrice != null" >
        product_price = #{record.productPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.freightTemplateId != null" >
        freight_template_id = #{record.freightTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.imageUrls != null" >
        image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="record.productUnit != null" >
        product_unit = #{record.productUnit,jdbcType=INTEGER},
      </if>
      <if test="record.packageType != null" >
        package_type = #{record.packageType,jdbcType=BIT},
      </if>
      <if test="record.lotNum != null" >
        lot_num = #{record.lotNum,jdbcType=INTEGER},
      </if>
      <if test="record.packageLength != null" >
        package_length = #{record.packageLength,jdbcType=INTEGER},
      </if>
      <if test="record.packageWidth != null" >
        package_width = #{record.packageWidth,jdbcType=INTEGER},
      </if>
      <if test="record.packageHeight != null" >
        package_height = #{record.packageHeight,jdbcType=INTEGER},
      </if>
      <if test="record.grossWeight != null" >
        gross_weight = #{record.grossWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.isPackSell != null" >
        is_pack_sell = #{record.isPackSell,jdbcType=BIT},
      </if>
      <if test="record.isWholesale != null" >
        is_wholesale = #{record.isWholesale,jdbcType=BIT},
      </if>
      <if test="record.baseUnit != null" >
        base_unit = #{record.baseUnit,jdbcType=INTEGER},
      </if>
      <if test="record.addUnit != null" >
        add_unit = #{record.addUnit,jdbcType=INTEGER},
      </if>
      <if test="record.addWeight != null" >
        add_weight = #{record.addWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.wsValidNum != null" >
        ws_valid_num = #{record.wsValidNum,jdbcType=INTEGER},
      </if>
      <if test="record.aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{record.aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="record.bulkOrder != null" >
        bulk_order = #{record.bulkOrder,jdbcType=INTEGER},
      </if>
      <if test="record.bulkDiscount != null" >
        bulk_discount = #{record.bulkDiscount,jdbcType=INTEGER},
      </if>
      <if test="record.sizeChartId != null" >
        size_chart_id = #{record.sizeChartId,jdbcType=BIGINT},
      </if>
      <if test="record.reduceStrategy != null" >
        reduce_strategy = #{record.reduceStrategy,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null" >
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.groupIds != null" >
        group_ids = #{record.groupIds,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileDetail != null" >
        mobile_detail = #{record.mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.couponStartDate != null" >
        coupon_start_date = #{record.couponStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.couponEndDate != null" >
        coupon_end_date = #{record.couponEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aeopNationalQuoteConfiguration != null" >
        aeop_national_quote_configuration = #{record.aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{record.aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="record.editor != null" >
        editor = #{record.editor,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastEditTime != null" >
        last_edit_time = #{record.lastEditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aliexpressAccountNumber != null" >
        aliexpress_account_number = #{record.aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.displayImageUrl != null" >
        display_image_url = #{record.displayImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerMemberId != null" >
        owner_member_id = #{record.ownerMemberId,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerMemberSeq != null" >
        owner_member_seq = #{record.ownerMemberSeq,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.src != null" >
        src = #{record.src,jdbcType=VARCHAR},
      </if>
      <if test="record.wsOfflineDate != null" >
        ws_offline_date = #{record.wsOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.wsDisplay != null" >
        ws_display = #{record.wsDisplay,jdbcType=VARCHAR},
      </if>
      <if test="record.productStatusType != null" >
        product_status_type = #{record.productStatusType,jdbcType=VARCHAR},
      </if>
      <if test="record.isImageDynamic != null" >
        is_image_dynamic = #{record.isImageDynamic,jdbcType=BIT},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null" >
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productMinPrice != null" >
        product_min_price = #{record.productMinPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.productMaxPrice != null" >
        product_max_price = #{record.productMaxPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.lastSyncTime != null" >
        last_sync_time = #{record.lastSyncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.articleNumbers != null" >
        article_numbers = #{record.articleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null" >
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.ipmSkuStock != null" >
        ipm_sku_stock = #{record.ipmSkuStock,jdbcType=INTEGER},
      </if>
      <if test="record.skuCode != null" >
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDisplayImg != null" >
        sku_display_img = #{record.skuDisplayImg,jdbcType=VARCHAR},
      </if>
      <if test="record.skuPrice != null" >
        sku_price = #{record.skuPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDiscountPrice != null" >
        sku_discount_price = #{record.skuDiscountPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.barcode != null" >
        barcode = #{record.barcode,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopSKUPropertyList != null" >
        aeop_s_k_u_property_list = #{record.aeopSKUPropertyList,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopSKUNationalDiscountPriceList != null" >
        aeop_s_k_u_national_discount_price_list = #{record.aeopSKUNationalDiscountPriceList,jdbcType=VARCHAR},
      </if>
      <if test="record.isVariant != null" >
        is_variant = #{record.isVariant,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressProduct" >
    update aliexpress_product
    <set >
      <if test="detail != null" >
        detail = #{detail,jdbcType=VARCHAR},
      </if>
      <if test="aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null" >
        delivery_time = #{deliveryTime,jdbcType=INTEGER},
      </if>
      <if test="promiseTemplateId != null" >
        promise_template_id = #{promiseTemplateId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null" >
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryTableId != null" >
        category_table_id = #{categoryTableId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="productPrice != null" >
        product_price = #{productPrice,jdbcType=DOUBLE},
      </if>
      <if test="freightTemplateId != null" >
        freight_template_id = #{freightTemplateId,jdbcType=BIGINT},
      </if>
      <if test="imageUrls != null" >
        image_urls = #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="productUnit != null" >
        product_unit = #{productUnit,jdbcType=INTEGER},
      </if>
      <if test="packageType != null" >
        package_type = #{packageType,jdbcType=BIT},
      </if>
      <if test="lotNum != null" >
        lot_num = #{lotNum,jdbcType=INTEGER},
      </if>
      <if test="packageLength != null" >
        package_length = #{packageLength,jdbcType=INTEGER},
      </if>
      <if test="packageWidth != null" >
        package_width = #{packageWidth,jdbcType=INTEGER},
      </if>
      <if test="packageHeight != null" >
        package_height = #{packageHeight,jdbcType=INTEGER},
      </if>
      <if test="grossWeight != null" >
        gross_weight = #{grossWeight,jdbcType=VARCHAR},
      </if>
      <if test="isPackSell != null" >
        is_pack_sell = #{isPackSell,jdbcType=BIT},
      </if>
      <if test="isWholesale != null" >
        is_wholesale = #{isWholesale,jdbcType=BIT},
      </if>
      <if test="baseUnit != null" >
        base_unit = #{baseUnit,jdbcType=INTEGER},
      </if>
      <if test="addUnit != null" >
        add_unit = #{addUnit,jdbcType=INTEGER},
      </if>
      <if test="addWeight != null" >
        add_weight = #{addWeight,jdbcType=VARCHAR},
      </if>
      <if test="wsValidNum != null" >
        ws_valid_num = #{wsValidNum,jdbcType=INTEGER},
      </if>
      <if test="aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="bulkOrder != null" >
        bulk_order = #{bulkOrder,jdbcType=INTEGER},
      </if>
      <if test="bulkDiscount != null" >
        bulk_discount = #{bulkDiscount,jdbcType=INTEGER},
      </if>
      <if test="sizeChartId != null" >
        size_chart_id = #{sizeChartId,jdbcType=BIGINT},
      </if>
      <if test="reduceStrategy != null" >
        reduce_strategy = #{reduceStrategy,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupIds != null" >
        group_ids = #{groupIds,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDetail != null" >
        mobile_detail = #{mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="couponStartDate != null" >
        coupon_start_date = #{couponStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="couponEndDate != null" >
        coupon_end_date = #{couponEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="aeopNationalQuoteConfiguration != null" >
        aeop_national_quote_configuration = #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      </if>
      <if test="aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="editor != null" >
        editor = #{editor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastEditTime != null" >
        last_edit_time = #{lastEditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="aliexpressAccountNumber != null" >
        aliexpress_account_number = #{aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="displayImageUrl != null" >
        display_image_url = #{displayImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="ownerMemberId != null" >
        owner_member_id = #{ownerMemberId,jdbcType=VARCHAR},
      </if>
      <if test="ownerMemberSeq != null" >
        owner_member_seq = #{ownerMemberSeq,jdbcType=INTEGER},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="src != null" >
        src = #{src,jdbcType=VARCHAR},
      </if>
      <if test="wsOfflineDate != null" >
        ws_offline_date = #{wsOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="wsDisplay != null" >
        ws_display = #{wsDisplay,jdbcType=VARCHAR},
      </if>
      <if test="productStatusType != null" >
        product_status_type = #{productStatusType,jdbcType=VARCHAR},
      </if>
      <if test="isImageDynamic != null" >
        is_image_dynamic = #{isImageDynamic,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null" >
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="productMinPrice != null" >
        product_min_price = #{productMinPrice,jdbcType=DOUBLE},
      </if>
      <if test="productMaxPrice != null" >
        product_max_price = #{productMaxPrice,jdbcType=DOUBLE},
      </if>
      <if test="lastSyncTime != null" >
        last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="articleNumbers != null" >
        article_numbers = #{articleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="ipmSkuStock != null" >
        ipm_sku_stock = #{ipmSkuStock,jdbcType=INTEGER},
      </if>
      <if test="skuCode != null" >
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuDisplayImg != null" >
        sku_display_img = #{skuDisplayImg,jdbcType=VARCHAR},
      </if>
      <if test="skuPrice != null" >
        sku_price = #{skuPrice,jdbcType=DOUBLE},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDiscountPrice != null" >
        sku_discount_price = #{skuDiscountPrice,jdbcType=DOUBLE},
      </if>
      <if test="barcode != null" >
        barcode = #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="aeopSKUPropertyList != null" >
        aeop_s_k_u_property_list = #{aeopSKUPropertyList,jdbcType=VARCHAR},
      </if>
      <if test="aeopSKUNationalDiscountPriceList != null" >
        aeop_s_k_u_national_discount_price_list = #{aeopSKUNationalDiscountPriceList,jdbcType=VARCHAR},
      </if>
      <if test="isVariant != null" >
        is_variant = #{isVariant,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOnsellingSkuCount" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample" resultType="java.lang.Integer">
    SELECT
    count( 1 )
    FROM
    ( SELECT DISTINCT article_number from aliexpress_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    ) AS t_article_number
  </select>

  <select id="selectOnSellingSkuByExample" resultType="java.lang.String" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample">
    select distinct article_number
    from aliexpress_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="getOnSellingSkuListingNum" parameterType="map" resultType="com.estone.erp.publish.system.skuSellAccountAmount.model.SkuSellAccountAmount">
    select article_number as articleNumber,count(aliexpress_account_number) as countDay${day}
    from aliexpress_product
    where gmt_create <![CDATA[ <= ]]> #{record.gmtCreate,jdbcType=TIMESTAMP}
    and (ws_offline_date <![CDATA[ >= ]]> #{record.wsOfflineDate,jdbcType=TIMESTAMP} or ws_offline_date is null)
    <if test="articleNumberList != null">
      AND article_number IN
      <foreach collection="articleNumberList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by article_number
  </select>
</mapper>