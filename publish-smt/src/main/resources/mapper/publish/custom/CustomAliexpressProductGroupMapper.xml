<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.smt.mapper.custom.CustomAliexpressProductGroupMapper">
    <resultMap id="resultMap" type="com.estone.erp.publish.smt.model.AliexpressProductGroup">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="full_path_code" jdbcType="VARCHAR" property="fullPathCode"/>
        <result column="leaf" jdbcType="BIT" property="leaf"/>
        <collection property="childGroups" column="id" select="getChildGroups"/>
    </resultMap>

    <select id="getChildGroups" parameterType="int" resultMap="resultMap">
      select *
      from aliexpress_product_group
      where parent_id=#{id}
  </select>

    <select id="getGroupsByAccountNumber" resultMap="resultMap">
      select *
      from aliexpress_product_group
      where (leaf is null or leaf = false) and account_number=#{accountNumber}
  </select>

    <select id="getAllGroupsByAccountNumber" resultMap="resultMap">
        select *
        from aliexpress_product_group
        where account_number=#{accountNumber}
    </select>

    <select id="getGroupByGroupId" resultMap="resultMap">
      select *
      from aliexpress_product_group
      where group_id=#{groupId}
  </select>

    <select id="getGroupById" resultMap="resultMap">
      select *
      from aliexpress_product_group
      where id=#{id}
  </select>

    <select id="getAllGroups" resultMap="resultMap">
      select *
      from aliexpress_product_group
      where leaf is null or leaf = false
  </select>

  <insert id="saveGroup" parameterType="com.estone.erp.publish.smt.model.AliexpressProductGroup" keyProperty="id">
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_product_group(account_number, group_id, group_name, parent_id, full_path_code, leaf)
    value(#{accountNumber}, #{groupId}, #{groupName}, #{parentId}, #{fullPathCode}, #{leaf})
  </insert>

    <insert id="saveChildGroups" parameterType="java.util.List">
        insert into aliexpress_product_group(account_number, group_id, group_name, parent_id, full_path_code, leaf)
        values
        <foreach collection="list" item="childGroup" index="index" separator=",">
            (#{childGroup.accountNumber}, #{childGroup.groupId}, #{childGroup.groupName}, #{childGroup.parentId},
            #{childGroup.fullPathCode}, #{childGroup.leaf})
        </foreach>
    </insert>

    <update id="updateGroup" parameterType="com.estone.erp.publish.smt.model.AliexpressProductGroup">
      update aliexpress_product_group set account_number = #{accountNumber}, group_id = #{groupId}, group_name = #{groupName},
      parent_id = #{parentId}, full_path_code = #{fullPathCode}, leaf = #{leaf}
      where id = #{id}
  </update>

    <delete id="deleteGroup" parameterType="com.estone.erp.publish.smt.model.AliexpressProductGroup">
      delete from aliexpress_product_group where id = #{id}
  </delete>

    <delete id="deleteChildGroups" parameterType="java.lang.Integer">
      delete from aliexpress_product_group where parent_id = #{parentId}
  </delete>

    <delete id="deleteGroupsByAccountNumber" parameterType="java.lang.String">
      delete from aliexpress_product_group where account_number = #{accountNumber}
  </delete>

</mapper>