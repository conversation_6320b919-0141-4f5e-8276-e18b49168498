<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.smt.mapper.custom.CustomAliexpressProductSourceMapper">
	<resultMap id="AliexpressProductSourceMap" type="com.estone.erp.publish.smt.model.AliexpressProductSource" >
        
        <result column="id" property="id" jdbcType="BIGINT" />
        
        <result column="detail" property="detail" jdbcType="LONGVARCHAR" />
        
        <result column="aeop_ae_product_skus_json" property="aeopAeProductSkusJson" jdbcType="LONGVARCHAR" />
        
        <result column="delivery_time" property="deliveryTime" jdbcType="INTEGER" />
        
        <result column="promise_template_id" property="promiseTemplateId" jdbcType="BIGINT" />
        
        <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
        
        <result column="category_table_id" property="categoryTableId" jdbcType="BIGINT" />
        
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
        
        <result column="subject" property="subject" jdbcType="VARCHAR" />
        
        <result column="product_price" property="productPrice" jdbcType="DOUBLE" />
        
        <result column="freight_template_id" property="freightTemplateId" jdbcType="BIGINT" />
        
        <result column="image_urls" property="imageUrls" jdbcType="LONGVARCHAR" />
        
        <result column="product_unit" property="productUnit" jdbcType="INTEGER" />
        
        <result column="package_type" property="packageType" jdbcType="BIT" />
        
        <result column="lot_num" property="lotNum" jdbcType="INTEGER" />
        
        <result column="package_length" property="packageLength" jdbcType="INTEGER" />
        
        <result column="package_width" property="packageWidth" jdbcType="INTEGER" />
        
        <result column="package_height" property="packageHeight" jdbcType="INTEGER" />
        
        <result column="gross_weight" property="grossWeight" jdbcType="VARCHAR" />
        
        <result column="is_pack_sell" property="isPackSell" jdbcType="BIT" />
        
        <result column="is_wholesale" property="isWholesale" jdbcType="BIT" />
        
        <result column="base_unit" property="baseUnit" jdbcType="INTEGER" />
        
        <result column="add_unit" property="addUnit" jdbcType="INTEGER" />
        
        <result column="add_weight" property="addWeight" jdbcType="VARCHAR" />
        
        <result column="ws_valid_num" property="wsValidNum" jdbcType="INTEGER" />
        
        <result column="aeop_ae_product_propertys_json" property="aeopAeProductPropertysJson" jdbcType="LONGVARCHAR" />
        
        <result column="bulk_order" property="bulkOrder" jdbcType="INTEGER" />
        
        <result column="bulk_discount" property="bulkDiscount" jdbcType="INTEGER" />
        
        <result column="size_chart_id" property="sizeChartId" jdbcType="BIGINT" />
        
        <result column="reduce_strategy" property="reduceStrategy" jdbcType="VARCHAR" />
        
        <result column="group_id" property="groupId" jdbcType="BIGINT" />
        
        <result column="group_ids" property="groupIds" jdbcType="VARCHAR" />
        
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
        
        <result column="mobile_detail" property="mobileDetail" jdbcType="LONGVARCHAR" />
        
        <result column="coupon_start_date" property="couponStartDate" jdbcType="TIMESTAMP" />
        
        <result column="coupon_end_date" property="couponEndDate" jdbcType="TIMESTAMP" />
        
        <result column="aeop_national_quote_configuration" property="aeopNationalQuoteConfiguration" jdbcType="LONGVARCHAR" />
        
        <result column="aeop_ae_multimedia" property="aeopAeMultimedia" jdbcType="LONGVARCHAR" />
        
        <result column="editor" property="editor" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="last_edit_time" property="lastEditTime" jdbcType="TIMESTAMP" />
        
        <result column="aliexpress_account_number" property="aliexpressAccountNumber" jdbcType="VARCHAR" />
        
        <result column="display_image_url" property="displayImageUrl" jdbcType="VARCHAR" />
        
        <result column="owner_member_id" property="ownerMemberId" jdbcType="VARCHAR" />
        
        <result column="owner_member_seq" property="ownerMemberSeq" jdbcType="INTEGER" />
        
        <result column="product_id" property="productId" jdbcType="BIGINT" />
        
        <result column="src" property="src" jdbcType="VARCHAR" />
        
        <result column="ws_offline_date" property="wsOfflineDate" jdbcType="TIMESTAMP" />
        
        <result column="ws_display" property="wsDisplay" jdbcType="VARCHAR" />
        
        <result column="product_status_type" property="productStatusType" jdbcType="VARCHAR" />
        
        <result column="is_image_dynamic" property="isImageDynamic" jdbcType="BIT" />
        
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
        
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
        
        <result column="product_min_price" property="productMinPrice" jdbcType="DOUBLE" />
        
        <result column="product_max_price" property="productMaxPrice" jdbcType="DOUBLE" />
        
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP" />
        
        <result column="article_numbers" property="articleNumbers" jdbcType="LONGVARCHAR" />
        
        <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
        
        <result column="ipm_sku_stock" property="ipmSkuStock" jdbcType="INTEGER" />
        
        <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
        
        <result column="sku_display_img" property="skuDisplayImg" jdbcType="VARCHAR" />
        
        <result column="sku_price" property="skuPrice" jdbcType="DOUBLE" />
        
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
        
        <result column="sku_discount_price" property="skuDiscountPrice" jdbcType="DOUBLE" />
        
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        
        <result column="aeop_s_k_u_property_list" property="aeopSKUPropertyList" jdbcType="LONGVARCHAR" />
        
        <result column="aeop_s_k_u_national_discount_price_list" property="aeopSKUNationalDiscountPriceList" jdbcType="LONGVARCHAR" />
        
        <result column="product_resource" property="productResource" jdbcType="SMALLINT" />
        
        <result column="product_audit_status" property="productAuditStatus" jdbcType="SMALLINT" />

        <result column="weight" property="weight" jdbcType="DOUBLE" />

        <result column="forbid_channel" property="forbidChannel" jdbcType="VARCHAR" />

        <result column="sku_status" property="skuStatus" jdbcType="INTEGER" />

        <result column="is_synch" property="isSynch" jdbcType="TINYINT" />

    </resultMap>
    
    
    <sql id="Serach_Short_Column_List" >
        aps.id as id,
        aps.product_id as product_id,
        aps.category_name as category_name,
        aps.category_table_id as category_table_id,
        aps.category_id as category_id,
        aps.subject as subject,
        aps.image_urls as image_urls,
        aps.create_time as create_time,
        aps.last_edit_time as last_edit_time,
        aps.display_image_url as display_image_url,
        aps.article_number as article_number,
        aps.product_resource as product_resource,
        aps.aliexpress_account_number as aliexpress_account_number,
        aps.product_audit_status as product_audit_status,
        aps.product_unit as product_unit,
        aps.is_synch as is_synch
    </sql>
    
    <sql id="Serach_Long_Column_List" >
        aps.id as id,
        aps.detail as detail,
        aps.aeop_ae_product_skus_json as aeop_ae_product_skus_json,
        aps.delivery_time as delivery_time,
        aps.promise_template_id as promise_template_id,
        aps.category_name as category_name,
        aps.category_table_id as category_table_id,
        aps.category_id as category_id,
        aps.subject as subject,
        aps.product_price as product_price,
        aps.freight_template_id as freight_template_id,
        aps.image_urls as image_urls,
        aps.product_unit as product_unit,
        aps.package_type as package_type,
        aps.lot_num as lot_num,
        aps.package_length as package_length,
        aps.package_width as package_width,
        aps.package_height as package_height,
        aps.gross_weight as gross_weight,
        aps.is_pack_sell as is_pack_sell,
        aps.is_wholesale as is_wholesale,
        aps.base_unit as base_unit,
        aps.add_unit as add_unit,
        aps.add_weight as add_weight,
        aps.ws_valid_num as ws_valid_num,
        aps.aeop_ae_product_propertys_json as aeop_ae_product_propertys_json,
        aps.bulk_order as bulk_order,
        aps.bulk_discount as bulk_discount,
        aps.size_chart_id as size_chart_id,
        aps.reduce_strategy as reduce_strategy,
        aps.group_id as group_id,
        aps.group_ids as group_ids,
        aps.currency_code as currency_code,
        aps.mobile_detail as mobile_detail,
        aps.coupon_start_date as coupon_start_date,
        aps.coupon_end_date as coupon_end_date,
        aps.aeop_national_quote_configuration as aeop_national_quote_configuration,
        aps.aeop_ae_multimedia as aeop_ae_multimedia,
        aps.editor as editor,
        aps.create_time as create_time,
        aps.last_edit_time as last_edit_time,
        aps.aliexpress_account_number as aliexpress_account_number,
        aps.display_image_url as display_image_url,
        aps.owner_member_id as owner_member_id,
        aps.owner_member_seq as owner_member_seq,
        aps.product_id as product_id,
        aps.src as src,
        aps.ws_offline_date as ws_offline_date,
        aps.ws_display as ws_display,
        aps.product_status_type as product_status_type,
        aps.is_image_dynamic as is_image_dynamic,
        aps.gmt_create as gmt_create,
        aps.gmt_modified as gmt_modified,
        aps.product_min_price as product_min_price,
        aps.product_max_price as product_max_price,
        aps.last_sync_time as last_sync_time,
        aps.article_numbers as article_numbers,
        aps.sku_id as sku_id,
        aps.ipm_sku_stock as ipm_sku_stock,
        aps.sku_code as sku_code,
        aps.sku_display_img as sku_display_img,
        aps.sku_price as sku_price,
        aps.article_number as article_number,
        aps.sku_discount_price as sku_discount_price,
        aps.barcode as barcode,
        aps.aeop_s_k_u_property_list as aeop_s_k_u_property_list,
        aps.aeop_s_k_u_national_discount_price_list as aeop_s_k_u_national_discount_price_list,
        aps.product_resource as product_resource,
        aps.product_audit_status as product_audit_status,
        aps.is_synch as is_synch
    </sql>
    
    <sql id="Base_Column_List" >
	    id, detail, aeop_ae_product_skus_json, delivery_time, promise_template_id, category_name,
	    category_table_id, category_id, subject, product_price, freight_template_id, image_urls, 
	    product_unit, package_type, lot_num, package_length, package_width, package_height, 
	    gross_weight, is_pack_sell, is_wholesale, base_unit, add_unit, add_weight, ws_valid_num, 
	    aeop_ae_product_propertys_json, bulk_order, bulk_discount, size_chart_id, reduce_strategy, 
	    group_id, group_ids, currency_code, mobile_detail, coupon_start_date, coupon_end_date, 
	    aeop_national_quote_configuration, aeop_ae_multimedia, editor, create_time, last_edit_time, 
	    aliexpress_account_number, display_image_url, owner_member_id, owner_member_seq, 
	    product_id, src, ws_offline_date, ws_display, product_status_type, is_image_dynamic, 
	    gmt_create, gmt_modified, product_min_price, product_max_price, last_sync_time, article_numbers, 
	    sku_id, ipm_sku_stock, sku_code, sku_display_img, sku_price, article_number, sku_discount_price, 
	    barcode, aeop_s_k_u_property_list, aeop_s_k_u_national_discount_price_list, product_resource, 
	    product_audit_status, is_synch
  	</sql>

    <sql id="normalWhere">
        <if test="id!=null">
            and aps.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="detail!=null">
            and aps.detail = #{detail,jdbcType=LONGVARCHAR}
        </if>
        <if test="aeopAeProductSkusJson!=null">
            and aps.aeop_ae_product_skus_json = #{aeopAeProductSkusJson,jdbcType=LONGVARCHAR}
        </if>
        <if test="deliveryTime!=null">
            and aps.delivery_time = #{deliveryTime,jdbcType=INTEGER}
        </if>
        <if test="promiseTemplateId!=null">
            and aps.promise_template_id = #{promiseTemplateId,jdbcType=BIGINT}
        </if>
        <if test="categoryName!=null">
            and aps.category_name = #{categoryName,jdbcType=VARCHAR}
        </if>
        <if test="categoryTableId!=null">
            and aps.category_table_id = #{categoryTableId,jdbcType=BIGINT}
        </if>
        <if test="categoryId!=null">
            and aps.category_id = #{categoryId,jdbcType=INTEGER}
        </if>
        <if test="subject!=null">
            and aps.subject = #{subject,jdbcType=VARCHAR}
        </if>
        
        <if test="productPrice!=null">
            and aps.product_price = #{productPrice,jdbcType=DOUBLE}
        </if>
        <if test="freightTemplateId!=null">
            and aps.freight_template_id = #{freightTemplateId,jdbcType=BIGINT}
        </if>
        <if test="imageUrls!=null">
            and aps.image_urls = #{imageUrls,jdbcType=LONGVARCHAR}
        </if>
        <if test="productUnit!=null">
            and aps.product_unit = #{productUnit,jdbcType=INTEGER}
        </if>
        <if test="packageType!=null">
            and aps.package_type = #{packageType,jdbcType=BIT}
        </if>
        <if test="lotNum!=null">
            and aps.lot_num = #{lotNum,jdbcType=INTEGER}
        </if>
        <if test="packageLength!=null">
            and aps.package_length = #{packageLength,jdbcType=INTEGER}
        </if>
        <if test="packageWidth!=null">
            and aps.package_width = #{packageWidth,jdbcType=INTEGER}
        </if>
        <if test="packageHeight!=null">
            and package_height = #{packageHeight,jdbcType=INTEGER}
        </if>
        <if test="grossWeight!=null">
            and aps.gross_weight = #{grossWeight,jdbcType=VARCHAR}
        </if>
        <if test="isPackSell!=null">
            and aps.is_pack_sell = #{isPackSell,jdbcType=BIT}
        </if>
        <if test="isWholesale!=null">
            and aps.is_wholesale = #{isWholesale,jdbcType=BIT}
        </if>
        <if test="baseUnit!=null">
            and aps.base_unit = #{baseUnit,jdbcType=INTEGER}
        </if>
        <if test="addUnit!=null">
            and aps.add_unit = #{addUnit,jdbcType=INTEGER}
        </if>
        <if test="addWeight!=null">
            and aps.add_weight = #{addWeight,jdbcType=VARCHAR}
        </if>
        <if test="wsValidNum!=null">
            and aps.ws_valid_num = #{wsValidNum,jdbcType=INTEGER}
        </if>
        <if test="aeopAeProductPropertysJson!=null">
            and aps.aeop_ae_product_propertys_json = #{aeopAeProductPropertysJson,jdbcType=LONGVARCHAR}
        </if>
        <if test="bulkOrder!=null">
            and aps.bulk_order = #{bulkOrder,jdbcType=INTEGER}
        </if>
        <if test="bulkDiscount!=null">
            and aps.bulk_discount = #{bulkDiscount,jdbcType=INTEGER}
        </if>
        <if test="sizeChartId!=null">
            and aps.size_chart_id = #{sizeChartId,jdbcType=BIGINT}
        </if>
        <if test="reduceStrategy!=null">
            and aps.reduce_strategy = #{reduceStrategy,jdbcType=VARCHAR}
        </if>
        <if test="groupId!=null">
            and aps.group_id = #{groupId,jdbcType=BIGINT}
        </if>
        <if test="groupIds!=null">
            and aps.group_ids = #{groupIds,jdbcType=VARCHAR}
        </if>
        <if test="currencyCode!=null">
            and aps.currency_code = #{currencyCode,jdbcType=VARCHAR}
        </if>
        <if test="mobileDetail!=null">
            and aps.mobile_detail = #{mobileDetail,jdbcType=LONGVARCHAR}
        </if>
        <if test="couponStartDate!=null">
            and aps.coupon_start_date = #{couponStartDate,jdbcType=TIMESTAMP}
        </if>
        <if test="couponEndDate!=null">
            and aps.coupon_end_date = #{couponEndDate,jdbcType=TIMESTAMP}
        </if>
        <if test="aeopNationalQuoteConfiguration!=null">
            and aps.aeop_national_quote_configuration = #{aeopNationalQuoteConfiguration,jdbcType=LONGVARCHAR}
        </if>
        <if test="aeopAeMultimedia!=null">
            and aps.aeop_ae_multimedia = #{aeopAeMultimedia,jdbcType=LONGVARCHAR}
        </if>
        <if test="editor!=null">
            and aps.editor = #{editor,jdbcType=VARCHAR}
        </if>
        <if test="createTime!=null">
            and aps.create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastEditTime!=null">
            and aps.last_edit_time = #{lastEditTime,jdbcType=TIMESTAMP}
        </if>
        <if test="aliexpressAccountNumber!=null">
            and aps.aliexpress_account_number = #{aliexpressAccountNumber,jdbcType=VARCHAR}
        </if>
        <if test="displayImageUrl!=null">
            and aps.display_image_url = #{displayImageUrl,jdbcType=VARCHAR}
        </if>
        <if test="ownerMemberId!=null">
            and aps.owner_member_id = #{ownerMemberId,jdbcType=VARCHAR}
        </if>
        <if test="ownerMemberSeq!=null">
            and aps.owner_member_seq = #{ownerMemberSeq,jdbcType=INTEGER}
        </if>
        <if test="productId!=null">
            and aps.product_id = #{productId,jdbcType=BIGINT}
        </if>
        <if test="src!=null">
            and aps.src = #{src,jdbcType=VARCHAR}
        </if>
        <if test="wsOfflineDate!=null">
            and aps.ws_offline_date = #{wsOfflineDate,jdbcType=TIMESTAMP}
        </if>
        <if test="wsDisplay!=null">
            and aps.ws_display = #{wsDisplay,jdbcType=VARCHAR}
        </if>
        <if test="productStatusType!=null">
            and aps.product_status_type = #{productStatusType,jdbcType=VARCHAR}
        </if>
        <if test="isImageDynamic!=null">
            and aps.is_image_dynamic = #{isImageDynamic,jdbcType=BIT}
        </if>
        <if test="gmtCreate!=null">
            and aps.gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="gmtModified!=null">
            and aps.gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="productMinPrice!=null">
            and aps.product_min_price = #{productMinPrice,jdbcType=DOUBLE}
        </if>
        <if test="productMaxPrice!=null">
            and aps.product_max_price = #{productMaxPrice,jdbcType=DOUBLE}
        </if>
        <if test="lastSyncTime!=null">
            and aps.last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP}
        </if>
        <if test="articleNumbers!=null">
            and aps.article_numbers = #{articleNumbers,jdbcType=LONGVARCHAR}
        </if>
        <if test="skuId!=null">
            and aps.sku_id = #{skuId,jdbcType=VARCHAR}
        </if>
        <if test="ipmSkuStock!=null">
            and aps.ipm_sku_stock = #{ipmSkuStock,jdbcType=INTEGER}
        </if>
        <if test="skuCode!=null">
            and aps.sku_code = #{skuCode,jdbcType=VARCHAR}
        </if>
        <if test="skuDisplayImg!=null">
            and aps.sku_display_img = #{skuDisplayImg,jdbcType=VARCHAR}
        </if>
        <if test="skuPrice!=null">
            and aps.sku_price = #{skuPrice,jdbcType=DOUBLE}
        </if>
        <if test="articleNumber!=null">
            and aps.article_number = #{articleNumber,jdbcType=VARCHAR}
        </if>
        <if test="skuDiscountPrice!=null">
            and aps.sku_discount_price = #{skuDiscountPrice,jdbcType=DOUBLE}
        </if>
        <if test="barcode!=null">
            and aps.barcode = #{barcode,jdbcType=VARCHAR}
        </if>
        <if test="aeopSKUPropertyList!=null">
            and aps.aeop_s_k_u_property_list = #{aeopSKUPropertyList,jdbcType=LONGVARCHAR}
        </if>
        <if test="aeopSKUNationalDiscountPriceList!=null">
            and aps.aeop_s_k_u_national_discount_price_list = #{aeopSKUNationalDiscountPriceList,jdbcType=LONGVARCHAR}
        </if>
        <if test="productResource!=null">
            and aps.product_resource = #{productResource,jdbcType=SMALLINT}
        </if>
        <if test="productAuditStatus!=null">
            and aps.product_audit_status = #{productAuditStatus,jdbcType=SMALLINT}
        </if>
        <if test="isSynch!=null">
            and aps.is_synch = #{isSynch,jdbcType=TINYINT}
        </if>
    </sql>
    
    <sql id="search_normalWhere">

    	<if test="likeSubject!=null">
            and aps.subject like CONCAT('%', #{likeSubject,jdbcType=VARCHAR} '%')
        </if>
        
        <if test="createDateStart!=null and createDateStart != '' ">
	       and aps.create_time <![CDATA[>=]]> #{createDateStart}
        </if>
        
        <if test="createDateEnd!=null and createDateEnd != '' ">
	       and aps.create_time <![CDATA[<=]]> #{createDateEnd}
        </if>
        
        <if test="modifiedDateStart!=null and modifiedDateStart != '' ">
	       and aps.last_edit_time <![CDATA[>=]]> #{modifiedDateStart}
        </if>
        
        <if test="modifiedDateEnd!=null and modifiedDateEnd != '' ">
	       and aps.last_edit_time <![CDATA[<=]]> #{modifiedDateEnd}
        </if>

        <if test="forbidChannel!=null and forbidChannel != '' ">
            and sku.forbid_channel like CONCAT('%,', #{forbidChannel,jdbcType=VARCHAR} ',%')
        </if>

        <if test="fromWeight!=null and fromWeight != '' ">
            and sku.weight <![CDATA[>=]]> #{fromWeight}
        </if>

        <if test="toWeight!=null and toWeight != '' ">
            and sku.weight <![CDATA[<=]]> #{toWeight}
        </if>

        <if test="skuStatusList!=null and skuStatusList.size>0">
            and sku.status IN
            <foreach item="item" index="index" collection="skuStatusList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        
        <if test="serachArticleNumberList!=null and serachArticleNumberList.size>0">
          and aps.article_number IN
                <foreach item="item" index="index" collection="serachArticleNumberList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>

         <if test="idList!=null and idList.size>0">
          and aps.id IN
                <foreach item="item" index="index" collection="idList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        
        <if test="searchProductIdList!=null and searchProductIdList.size>0">
          and aps.product_id IN
                <foreach item="item" index="index" collection="searchProductIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>

        <if test="categoryIdList!=null and categoryIdList.size>0">
            and aps.category_id IN
            <foreach item="item" index="index" collection="categoryIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        
        <if test="orderBy!=null and orderBy != '' ">
        	<choose>
	        	<when test="orderBy=='10'">
	        		order by create_time
	        	</when>
	        	
	        	<when test="orderBy=='20'">
	        		order by create_time DESC
	        	</when>
	        	
	        	<when test="orderBy=='30'">
	        		order by last_edit_time
	        	</when>
	        	
	        	<when test="orderBy=='40'">
	        		order by last_edit_time DESC
	        	</when>
        	</choose>
        </if>
        
        <if test="start!=null and end!=null">
            limit ${start}, ${end}
        </if>
    
    </sql>
    
    
     <select id="searchAliexpressProductSourcesCount" parameterType="com.estone.erp.publish.smt.model.AliexpressProductSourceCriteria" resultType="java.lang.Integer">
	  			
  			SELECT COUNT(1)
  		    FROM aliexpress_product_source aps
             <if test="leftJoinTable!=null and leftJoinTable != '' ">
                 LEFT JOIN pms_sku sku on aps.article_number = sku.article_number
             </if>

  		    where 1 = 1
  		    <include refid="normalWhere" />
  		    <include refid="search_normalWhere" />
  		    
  		    
	 </select>
    
     <select id="searchAliexpressProductSources" parameterType="com.estone.erp.publish.smt.model.AliexpressProductSourceCriteria" resultMap="AliexpressProductSourceMap">
        select
        sku.weight as weight,
         sku.status as sku_status,
         sku.forbid_channel as forbid_channel,
         <include refid="Serach_Long_Column_List" />
        from aliexpress_product_source aps
        LEFT JOIN pms_sku sku on aps.article_number = sku.article_number
        where 1 = 1
        <include refid="normalWhere" />
        <include refid="search_normalWhere" />
    </select>

    <select id="searchAliexpressShortProductSources" parameterType="com.estone.erp.publish.smt.model.AliexpressProductSourceCriteria" resultMap="AliexpressProductSourceMap">
        select

        <include refid="Serach_Short_Column_List" />
        from aliexpress_product_source aps
        where 1 = 1
        <include refid="normalWhere" />
        <include refid="search_normalWhere" />
    </select>
    
    
    <select id="queryPublishAliexpressProductSourceList" parameterType="com.estone.erp.publish.smt.model.AliexpressProductExample" resultMap="AliexpressProductSourceMap">
        select 
        <include refid="Serach_Long_Column_List" />
        from aliexpress_product_source aps
        where 1 = 1
        <include refid="normalWhere" />
        <include refid="search_normalWhere" />
    </select>


    <select id="batchSelectSkuByIds" parameterType="java.util.List" resultType="java.lang.String">
        select article_number
        from aliexpress_product_source aps
        WHERE 1=1
        <if test="list != null">
            and id IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertAliexpressProductSource" parameterType="com.estone.erp.publish.smt.model.AliexpressProductSource" >
        insert into aliexpress_product_source (<include refid="Base_Column_List" />)
        values (
        #{id,jdbcType=BIGINT},
        #{detail,jdbcType=LONGVARCHAR},
        #{aeopAeProductSkusJson,jdbcType=LONGVARCHAR},
        #{deliveryTime,jdbcType=INTEGER},
        #{promiseTemplateId,jdbcType=BIGINT},
        #{categoryName,jdbcType=VARCHAR},
        #{categoryTableId,jdbcType=BIGINT},
        #{categoryId,jdbcType=INTEGER},
        #{subject,jdbcType=VARCHAR},
        #{productPrice,jdbcType=DOUBLE},
        #{freightTemplateId,jdbcType=BIGINT},
        #{imageUrls,jdbcType=LONGVARCHAR},
        #{productUnit,jdbcType=INTEGER},
        #{packageType,jdbcType=BIT},
        #{lotNum,jdbcType=INTEGER},
        #{packageLength,jdbcType=INTEGER},
        #{packageWidth,jdbcType=INTEGER},
        #{packageHeight,jdbcType=INTEGER},
        #{grossWeight,jdbcType=VARCHAR},
        #{isPackSell,jdbcType=BIT},
        #{isWholesale,jdbcType=BIT},
        #{baseUnit,jdbcType=INTEGER},
        #{addUnit,jdbcType=INTEGER},
        #{addWeight,jdbcType=VARCHAR},
        #{wsValidNum,jdbcType=INTEGER},
        #{aeopAeProductPropertysJson,jdbcType=LONGVARCHAR},
        #{bulkOrder,jdbcType=INTEGER},
        #{bulkDiscount,jdbcType=INTEGER},
        #{sizeChartId,jdbcType=BIGINT},
        #{reduceStrategy,jdbcType=VARCHAR},
        #{groupId,jdbcType=BIGINT},
        #{groupIds,jdbcType=VARCHAR},
        #{currencyCode,jdbcType=VARCHAR},
        #{mobileDetail,jdbcType=LONGVARCHAR},
        #{couponStartDate,jdbcType=TIMESTAMP},
        #{couponEndDate,jdbcType=TIMESTAMP},
        #{aeopNationalQuoteConfiguration,jdbcType=LONGVARCHAR},
        #{aeopAeMultimedia,jdbcType=LONGVARCHAR},
        #{editor,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{lastEditTime,jdbcType=TIMESTAMP},
        #{aliexpressAccountNumber,jdbcType=VARCHAR},
        #{displayImageUrl,jdbcType=VARCHAR},
        #{ownerMemberId,jdbcType=VARCHAR},
        #{ownerMemberSeq,jdbcType=INTEGER},
        #{productId,jdbcType=BIGINT},
        #{src,jdbcType=VARCHAR},
        #{wsOfflineDate,jdbcType=TIMESTAMP},
        #{wsDisplay,jdbcType=VARCHAR},
        #{productStatusType,jdbcType=VARCHAR},
        #{isImageDynamic,jdbcType=BIT},
        #{gmtCreate,jdbcType=TIMESTAMP},
        #{gmtModified,jdbcType=TIMESTAMP},
        #{productMinPrice,jdbcType=DOUBLE},
        #{productMaxPrice,jdbcType=DOUBLE},
        #{lastSyncTime,jdbcType=TIMESTAMP},
        #{articleNumbers,jdbcType=LONGVARCHAR},
        #{skuId,jdbcType=VARCHAR},
        #{ipmSkuStock,jdbcType=INTEGER},
        #{skuCode,jdbcType=VARCHAR},
        #{skuDisplayImg,jdbcType=VARCHAR},
        #{skuPrice,jdbcType=DOUBLE},
        #{articleNumber,jdbcType=VARCHAR},
        #{skuDiscountPrice,jdbcType=DOUBLE},
        #{barcode,jdbcType=VARCHAR},
        #{aeopSKUPropertyList,jdbcType=LONGVARCHAR},
        #{aeopSKUNationalDiscountPriceList,jdbcType=LONGVARCHAR},
        #{productResource,jdbcType=SMALLINT},
        #{productAuditStatus,jdbcType=SMALLINT},
        #{isSynch,jdbcType=TINYINT}
        )
    </insert>
</mapper>