<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.custom.CustomAliexpressCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressCategory" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_level" property="categoryLevel" jdbcType="INTEGER" />
    <result column="leaf_category" property="leafCategory" jdbcType="BIT" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="category_zh_name" property="categoryZhName" jdbcType="VARCHAR" />
    <result column="category_en_name" property="categoryEnName" jdbcType="VARCHAR" />
    <result column="category_pt_name" property="categoryPtName" jdbcType="VARCHAR" />
    <result column="category_fr_name" property="categoryFrName" jdbcType="VARCHAR" />
    <result column="category_ru_name" property="categoryRuName" jdbcType="VARCHAR" />
    <result column="category_in_name" property="categoryInName" jdbcType="VARCHAR" />
    <result column="category_es_name" property="categoryEsName" jdbcType="VARCHAR" />
    <result column="child_attributes_json" property="childAttributesJson" jdbcType="VARCHAR" />
    <result column="full_path_code" property="fullPathCode" jdbcType="VARCHAR" />
    <result column="car_type" property="carType" jdbcType="BIT" />
    <result column="origin" property="origin" jdbcType="BIT" />
    <result column="season" property="season" jdbcType="BIT" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="province" property="province" jdbcType="BIT" />
    <result column="province_param2" property="provinceParam2" jdbcType="VARCHAR" />
    <result column="province_attributes" property="provinceAttributes" jdbcType="VARCHAR" />
    <result column="synch_date" property="synchDate" jdbcType="TIMESTAMP" />
    <result column="is_show" property="isShow" jdbcType="BIT" />
    <result column="full_cn_name" property="fullCnName" jdbcType="VARCHAR" />
    <result column="is_qualification" property="isQualification" jdbcType="BIT" />
    <result column="chemistry" property="chemistry" jdbcType="BIT" />
    <association property="upCategory" column="parent_id" select="getParentCategory"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, category_id, category_level, leaf_category, parent_id, category_zh_name, category_en_name, 
    category_pt_name, category_fr_name, category_ru_name, category_in_name, category_es_name, 
    child_attributes_json, full_path_code, car_type, origin, season, last_update_date, province,
    province_param2, province_attributes, synch_date, is_show, full_cn_name, is_qualification, chemistry
  </sql>

  <sql id="Serach_Category_Base_Column_List" >
    id, category_id, category_level, leaf_category, parent_id, category_zh_name, category_en_name,
    category_pt_name, category_fr_name, category_ru_name, category_in_name, category_es_name, full_path_code, car_type,
    origin, season, last_update_date, province, province_param2, province_attributes, synch_date, is_show, full_cn_name, is_qualification, chemistry
  </sql>

  <select id="searchCategeroyTreeList" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Serach_Category_Base_Column_List" />
    from aliexpress_category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="getParentCategory" parameterType="int" resultMap="BaseResultMap">
      select
	  <include refid="Serach_Category_Base_Column_List" />
      from aliexpress_category
      WHERE id=#{parentId}
  </select>
</mapper>