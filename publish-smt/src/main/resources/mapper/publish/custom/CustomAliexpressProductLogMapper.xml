<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.custom.CustomAliexpressProductLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressProductLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP" />
    <result column="operate_type" property="operateType" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="BIT" />
    <result column="fail_info" property="failInfo" jdbcType="VARCHAR" />
    <result column="price_before_edit" property="priceBeforeEdit" jdbcType="DOUBLE" />
    <result column="price_after_edit" property="priceAfterEdit" jdbcType="DOUBLE" />
    <result column="stock_before_edit" property="stockBeforeEdit" jdbcType="DOUBLE" />
    <result column="stock_after_edit" property="stockAfterEdit" jdbcType="DOUBLE" />
    <result column="relation_type" property="relationType" jdbcType="INTEGER" />
    <result column="relation_id" property="relationId" jdbcType="VARCHAR" />
    <result column="operate_status" property="operateStatus" jdbcType="INTEGER" />
    <result column="request_data" property="requestData" jdbcType="VARCHAR" />
    <result column="result_type" property="resultType" jdbcType="VARCHAR" />
    <result column="new_remark" property="newRemark" jdbcType="VARCHAR" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="rule_table" property="ruleTable" jdbcType="VARCHAR" />
    <result column="rule_id" property="ruleId" jdbcType="VARCHAR" />
    <result column="problem_type" property="problemType" jdbcType="VARCHAR" />
    <result column="solution_type" property="solutionType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <include refid="Example_Clause" />
    </where>
  </sql>

  <sql id="Example_Clause">
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>
  </sql>
  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_product_log (product_id, account_number, sku_code,
    `operator`, create_time, operate_time, operate_type,
    `result`, fail_info, price_before_edit,
     price_after_edit, stock_before_edit, stock_after_edit, relation_type, relation_id, operate_status, request_data,
     result_type, new_remark, rule_name, rule_table, rule_id,problem_type, solution_type
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.productId,jdbcType=BIGINT}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR},
      #{item.operator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.operateTime,jdbcType=TIMESTAMP}, #{item.operateType,jdbcType=VARCHAR},
      #{item.result,jdbcType=BIT}, #{item.failInfo,jdbcType=VARCHAR}, #{item.priceBeforeEdit,jdbcType=DOUBLE},
      #{item.priceAfterEdit,jdbcType=DOUBLE}, #{item.stockBeforeEdit,jdbcType=DOUBLE}, #{item.stockAfterEdit,jdbcType=DOUBLE},
      #{item.relationType,jdbcType=INTEGER}, #{item.relationId,jdbcType=VARCHAR}, #{item.operateStatus,jdbcType=INTEGER}, #{item.requestData,jdbcType=VARCHAR},
      #{item.resultType,jdbcType=VARCHAR}, #{item.newRemark,jdbcType=VARCHAR}, #{item.ruleName,jdbcType=VARCHAR}, #{item.ruleTable,jdbcType=VARCHAR}, #{item.ruleId,jdbcType=VARCHAR},
      #{item.problemType,jdbcType=VARCHAR}, #{item.solutionType,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>