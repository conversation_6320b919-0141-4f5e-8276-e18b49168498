<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.custom.CustomAliexpressTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressTemplate" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="detail" property="detail" jdbcType="VARCHAR" />
    <result column="aeop_ae_product_skus_json" property="aeopAeProductSkusJson" jdbcType="VARCHAR" />
    <result column="delivery_time" property="deliveryTime" jdbcType="INTEGER" />
    <result column="promise_template_id" property="promiseTemplateId" jdbcType="BIGINT" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="category_table_id" property="categoryTableId" jdbcType="BIGINT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="subject" property="subject" jdbcType="VARCHAR" />
    <result column="product_price" property="productPrice" jdbcType="DOUBLE" />
    <result column="freight_template_id" property="freightTemplateId" jdbcType="BIGINT" />
    <result column="image_urls" property="imageUrls" jdbcType="VARCHAR" />
    <result column="product_unit" property="productUnit" jdbcType="INTEGER" />
    <result column="package_type" property="packageType" jdbcType="BIT" />
    <result column="lot_num" property="lotNum" jdbcType="INTEGER" />
    <result column="package_length" property="packageLength" jdbcType="INTEGER" />
    <result column="package_width" property="packageWidth" jdbcType="INTEGER" />
    <result column="package_height" property="packageHeight" jdbcType="INTEGER" />
    <result column="gross_weight" property="grossWeight" jdbcType="VARCHAR" />
    <result column="is_pack_sell" property="isPackSell" jdbcType="BIT" />
    <result column="is_wholesale" property="isWholesale" jdbcType="BIT" />
    <result column="base_unit" property="baseUnit" jdbcType="INTEGER" />
    <result column="add_unit" property="addUnit" jdbcType="INTEGER" />
    <result column="add_weight" property="addWeight" jdbcType="VARCHAR" />
    <result column="ws_valid_num" property="wsValidNum" jdbcType="INTEGER" />
    <result column="aeop_ae_product_propertys_json" property="aeopAeProductPropertysJson" jdbcType="VARCHAR" />
    <result column="bulk_order" property="bulkOrder" jdbcType="INTEGER" />
    <result column="bulk_discount" property="bulkDiscount" jdbcType="INTEGER" />
    <result column="size_chart_id" property="sizeChartId" jdbcType="BIGINT" />
    <result column="reduce_strategy" property="reduceStrategy" jdbcType="VARCHAR" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="mobile_detail" property="mobileDetail" jdbcType="VARCHAR" />
    <result column="coupon_start_date" property="couponStartDate" jdbcType="TIMESTAMP" />
    <result column="coupon_end_date" property="couponEndDate" jdbcType="TIMESTAMP" />
    <result column="aeop_national_quote_configuration" property="aeopNationalQuoteConfiguration" jdbcType="VARCHAR" />
    <result column="aeop_ae_multimedia" property="aeopAeMultimedia" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="last_edit_time" property="lastEditTime" jdbcType="TIMESTAMP" />
    <result column="aliexpress_account_number" property="aliexpressAccountNumber" jdbcType="VARCHAR" />
    <result column="display_image_url" property="displayImageUrl" jdbcType="VARCHAR" />
    <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    <result column="product_stock" property="productStock" jdbcType="INTEGER" />
    <result column="post_time" property="postTime" jdbcType="TIMESTAMP" />
    <result column="margin" property="margin" jdbcType="DOUBLE" />
    <result column="shipping_method_code" property="shippingMethodCode" jdbcType="VARCHAR" />
    <result column="share_user" property="shareUser" jdbcType="VARCHAR" />
    <result column="is_public" property="isPublic" jdbcType="BIT" />
    <result column="is_parent" property="isParent" jdbcType="BIT" />
    <result column="template_status" property="templateStatus" jdbcType="INTEGER" />
    <result column="template_type" property="templateType" jdbcType="INTEGER" />
    <result column="template_label" property="templateLabel" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="car_type_json" property="carTypeJson" jdbcType="VARCHAR" />
    <result column="market_images_json" property="marketImagesJson" jdbcType="VARCHAR" />
    <result column="title_rule" property="titleRule" jdbcType="VARCHAR" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="root_category" property="rootCategory" jdbcType="INTEGER" />
    <result column="examine_state" property="examineState" jdbcType="VARCHAR" />
    <result column="examine_date" property="examineDate" jdbcType="TIMESTAMP" />
    <result column="examine_saleman" property="examineSaleman" jdbcType="VARCHAR" />
    <result column="video_link" property="videoLink" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="TINYINT" />
    <result column="recommend_category_id" property="recommendCategoryId" jdbcType="INTEGER" />
    <result column="recommend_category_path" property="recommendCategoryPath" jdbcType="VARCHAR" />
    <result column="sku_status" property="skuStatus" jdbcType="VARCHAR" />
    <result column="wen_an_type" property="wenAnType" jdbcType="INTEGER" />
    <result column="sign" property="sign" jdbcType="BIT" />
    <result column="inter_subjects" property="interSubjects" jdbcType="VARCHAR" />
    <result column="aeop_qualification_struct_json" property="aeopQualificationStructJson" jdbcType="VARCHAR" />
    <result column="size_chart_id_list" property="sizeChartIdList" jdbcType="VARCHAR" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="pop_temp_id" property="popTempId" jdbcType="INTEGER" />
    <result column="half_temp_id" property="halfTempId" jdbcType="BIGINT" />
    <result column="draft_id" property="draftId" jdbcType="VARCHAR" />
    <result column="manufacture_id" property="manufactureId" jdbcType="BIGINT" />
    <result column="area_discount_rate" property="areaDiscountRate" jdbcType="DOUBLE" />
    <result column="problem_type" property="problemType" jdbcType="VARCHAR" />
    <result column="tax_type" property="taxType" jdbcType="VARCHAR" />
    <result column="hacode_json" property="hacodeJson" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <include refid="Example_Clause" />
    </where>
  </sql>

  <sql id="Example_Clause">
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>

  </sql>

  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, detail, aeop_ae_product_skus_json, delivery_time, promise_template_id, category_name, 
    category_table_id, category_id, subject, product_price, freight_template_id, image_urls, 
    product_unit, package_type, lot_num, package_length, package_width, package_height, 
    gross_weight, is_pack_sell, is_wholesale, base_unit, add_unit, add_weight, ws_valid_num, 
    aeop_ae_product_propertys_json, bulk_order, bulk_discount, size_chart_id, reduce_strategy, 
    group_id, currency_code, mobile_detail, coupon_start_date, coupon_end_date, aeop_national_quote_configuration, 
    aeop_ae_multimedia, article_number, creator, create_time, last_edit_time, aliexpress_account_number, 
    display_image_url, product_code, product_stock, post_time, margin, shipping_method_code, 
    share_user, is_public, is_parent, template_status, template_type, template_label, parent_id,
    car_type_json, market_images_json, title_rule, publish_role, root_category, examine_state, examine_date, examine_saleman,
    video_link, product_type, recommend_category_path, recommend_category_id, sku_status, wen_an_type, sign,
    inter_subjects, aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id, half_temp_id, draft_id, manufacture_id,
    area_discount_rate,problem_type, tax_type, hacode_json
  </sql>
  <sql id="Attr_Column_List" >
    id, aeop_ae_product_skus_json, category_id, article_number, creator, create_time, last_edit_time, aliexpress_account_number,
    is_parent, video_link, product_type, wen_an_type, sign, aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id, half_temp_id, draft_id, manufacture_id,
    area_discount_rate, tax_type, hacode_json
  </sql>

  <sql id="Serach_Column_List" >
    id, delivery_time, promise_template_id, category_name,
    category_table_id, category_id, subject, product_price, freight_template_id, image_urls,
    product_unit, package_type, lot_num, package_length, package_width, package_height,
    gross_weight, is_pack_sell, is_wholesale, base_unit, add_unit, add_weight, ws_valid_num,
    aeop_ae_product_propertys_json, bulk_order, bulk_discount, size_chart_id, reduce_strategy,
    group_id, currency_code, coupon_start_date, coupon_end_date,
    aeop_ae_multimedia, article_number, creator, create_time, last_edit_time, aliexpress_account_number,
    display_image_url, product_code, product_stock, post_time, margin, shipping_method_code,
    share_user, is_public, is_parent, template_status, template_type, template_label, parent_id, title_rule, publish_role,
    root_category, examine_state, examine_date, examine_saleman, video_link, product_type, recommend_category_path,
    recommend_category_id, sku_status, wen_an_type, sign, aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id, half_temp_id, draft_id, manufacture_id,
    area_discount_rate, problem_type, tax_type, hacode_json
  </sql>

  <sql id="timing_Column_List" >
    id, article_number,category_name ,
    category_table_id, category_id, subject, template_status, template_type, template_label, parent_id, display_image_url,
    title_rule, publish_role, root_category, examine_state, examine_date, examine_saleman, video_link, product_type, recommend_category_path,
    recommend_category_id, wen_an_type, sign, aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id, half_temp_id, draft_id, manufacture_id,
    area_discount_rate,problem_type, tax_type, hacode_json
  </sql>

  <select id="searchItems" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Serach_Column_List" />
    from ${table}
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and aliexpress_account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="timingSelectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="timing_Column_List" />
    from ${table}
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and aliexpress_account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from ${table}
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and aliexpress_account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ${table}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ${table}
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByDate" >
    delete from ${table}
    where 1 = 1
    and is_parent is FALSE
    and create_time <![CDATA[ <= ]]> #{date}
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplate" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ${table} (detail, aeop_ae_product_skus_json, delivery_time,
      promise_template_id, category_name, category_table_id, 
      category_id, subject, product_price, 
      freight_template_id, image_urls, product_unit, 
      package_type, lot_num, package_length, 
      package_width, package_height, gross_weight, 
      is_pack_sell, is_wholesale, base_unit, 
      add_unit, add_weight, ws_valid_num, 
      aeop_ae_product_propertys_json, bulk_order, 
      bulk_discount, size_chart_id, reduce_strategy, 
      group_id, currency_code, mobile_detail, 
      coupon_start_date, coupon_end_date, aeop_national_quote_configuration, 
      aeop_ae_multimedia, article_number, creator, 
      create_time, last_edit_time, aliexpress_account_number, 
      display_image_url, product_code, product_stock, 
      post_time, margin, shipping_method_code, 
      share_user, is_public, is_parent, 
      template_status, template_type, template_label, parent_id,
      car_type_json, market_images_json, title_rule, publish_role,
      root_category, examine_state, examine_date, examine_saleman, video_link, product_type,
      recommend_category_id, recommend_category_path, sku_status, wen_an_type, sign, inter_subjects,
      aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id, half_temp_id, draft_id, manufacture_id,
      area_discount_rate,problem_type, tax_type, hacode_json
      )
    values (#{detail,jdbcType=VARCHAR}, #{aeopAeProductSkusJson,jdbcType=VARCHAR}, #{deliveryTime,jdbcType=INTEGER}, 
      #{promiseTemplateId,jdbcType=BIGINT}, #{categoryName,jdbcType=VARCHAR}, #{categoryTableId,jdbcType=BIGINT}, 
      #{categoryId,jdbcType=INTEGER}, #{subject,jdbcType=VARCHAR}, #{productPrice,jdbcType=DOUBLE}, 
      #{freightTemplateId,jdbcType=BIGINT}, #{imageUrls,jdbcType=VARCHAR}, #{productUnit,jdbcType=INTEGER},
      #{packageType,jdbcType=BIT}, #{lotNum,jdbcType=INTEGER}, #{packageLength,jdbcType=INTEGER}, 
      #{packageWidth,jdbcType=INTEGER}, #{packageHeight,jdbcType=INTEGER}, #{grossWeight,jdbcType=VARCHAR}, 
      #{isPackSell,jdbcType=BIT}, #{isWholesale,jdbcType=BIT}, #{baseUnit,jdbcType=INTEGER}, 
      #{addUnit,jdbcType=INTEGER}, #{addWeight,jdbcType=VARCHAR}, #{wsValidNum,jdbcType=INTEGER}, 
      #{aeopAeProductPropertysJson,jdbcType=VARCHAR}, #{bulkOrder,jdbcType=INTEGER}, 
      #{bulkDiscount,jdbcType=INTEGER}, #{sizeChartId,jdbcType=BIGINT}, #{reduceStrategy,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=BIGINT}, #{currencyCode,jdbcType=VARCHAR}, #{mobileDetail,jdbcType=VARCHAR}, 
      #{couponStartDate,jdbcType=TIMESTAMP}, #{couponEndDate,jdbcType=TIMESTAMP}, #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR}, 
      #{aeopAeMultimedia,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{lastEditTime,jdbcType=TIMESTAMP}, #{aliexpressAccountNumber,jdbcType=VARCHAR}, 
      #{displayImageUrl,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productStock,jdbcType=INTEGER}, 
      #{postTime,jdbcType=TIMESTAMP}, #{margin,jdbcType=DOUBLE}, #{shippingMethodCode,jdbcType=VARCHAR}, 
      #{shareUser,jdbcType=VARCHAR}, #{isPublic,jdbcType=BIT}, #{isParent,jdbcType=BIT}, 
      #{templateStatus,jdbcType=INTEGER}, #{templateType,jdbcType=INTEGER}, #{templateLabel,jdbcType=VARCHAR},
      #{parentId,jdbcType=INTEGER}, #{carTypeJson,jdbcType=VARCHAR}, #{marketImagesJson,jdbcType=VARCHAR}, #{titleRule,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER},
      #{rootCategory,jdbcType=INTEGER}, #{examineState,jdbcType=VARCHAR}, #{examineDate,jdbcType=TIMESTAMP}, #{examineSaleman,jdbcType=VARCHAR}, #{videoLink,jdbcType=VARCHAR},
      #{productType,jdbcType=TINYINT},#{recommendCategoryId,jdbcType=INTEGER},#{recommendCategoryPath,jdbcType=VARCHAR},
      #{skuStatus,jdbcType=VARCHAR}, #{wenAnType,jdbcType=INTEGER}, #{sign,jdbcType=BIT}, #{interSubjects,jdbcType=VARCHAR},
      #{aeopQualificationStructJson,jdbcType=VARCHAR}, #{sizeChartIdList,jdbcType=VARCHAR}, #{publishType,jdbcType=INTEGER}, #{popTempId,jdbcType=INTEGER},
      #{halfTempId,jdbcType=BIGINT}, #{draftId,jdbcType=VARCHAR}, #{manufactureId,jdbcType=BIGINT},
      #{areaDiscountRate,jdbcType=DOUBLE},#{problemType,jdbcType=INTEGER}, #{taxType,jdbcType=VARCHAR},
      #{hacodeJson,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" resultType="java.lang.Integer" >
    select count(*) from ${table}
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>
      <if test="null != authSellerList and authSellerList.size > 0">
        and aliexpress_account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ${example.table}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.detail != null" >
        detail = #{record.detail,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{record.aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryTime != null" >
        delivery_time = #{record.deliveryTime,jdbcType=INTEGER},
      </if>
      <if test="record.promiseTemplateId != null" >
        promise_template_id = #{record.promiseTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null" >
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryTableId != null" >
        category_table_id = #{record.categoryTableId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.subject != null" >
        subject = #{record.subject,jdbcType=VARCHAR},
      </if>
      <if test="record.productPrice != null" >
        product_price = #{record.productPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.freightTemplateId != null" >
        freight_template_id = #{record.freightTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.imageUrls != null" >
        image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="record.productUnit != null" >
        product_unit = #{record.productUnit,jdbcType=INTEGER},
      </if>
      <if test="record.packageType != null" >
        package_type = #{record.packageType,jdbcType=BIT},
      </if>
      <if test="record.lotNum != null" >
        lot_num = #{record.lotNum,jdbcType=INTEGER},
      </if>
      <if test="record.packageLength != null" >
        package_length = #{record.packageLength,jdbcType=INTEGER},
      </if>
      <if test="record.packageWidth != null" >
        package_width = #{record.packageWidth,jdbcType=INTEGER},
      </if>
      <if test="record.packageHeight != null" >
        package_height = #{record.packageHeight,jdbcType=INTEGER},
      </if>
      <if test="record.grossWeight != null" >
        gross_weight = #{record.grossWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.isPackSell != null" >
        is_pack_sell = #{record.isPackSell,jdbcType=BIT},
      </if>
      <if test="record.isWholesale != null" >
        is_wholesale = #{record.isWholesale,jdbcType=BIT},
      </if>
      <if test="record.baseUnit != null" >
        base_unit = #{record.baseUnit,jdbcType=INTEGER},
      </if>
      <if test="record.addUnit != null" >
        add_unit = #{record.addUnit,jdbcType=INTEGER},
      </if>
      <if test="record.addWeight != null" >
        add_weight = #{record.addWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.wsValidNum != null" >
        ws_valid_num = #{record.wsValidNum,jdbcType=INTEGER},
      </if>
      <if test="record.aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{record.aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="record.bulkOrder != null" >
        bulk_order = #{record.bulkOrder,jdbcType=INTEGER},
      </if>
      <if test="record.bulkDiscount != null" >
        bulk_discount = #{record.bulkDiscount,jdbcType=INTEGER},
      </if>
      <if test="record.sizeChartId != null" >
        size_chart_id = #{record.sizeChartId,jdbcType=BIGINT},
      </if>
      <if test="record.reduceStrategy != null" >
        reduce_strategy = #{record.reduceStrategy,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null" >
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileDetail != null" >
        mobile_detail = #{record.mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.couponStartDate != null" >
        coupon_start_date = #{record.couponStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.couponEndDate != null" >
        coupon_end_date = #{record.couponEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aeopNationalQuoteConfiguration != null" >
        aeop_national_quote_configuration = #{record.aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{record.aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastEditTime != null" >
        last_edit_time = #{record.lastEditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aliexpressAccountNumber != null" >
        aliexpress_account_number = #{record.aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.displayImageUrl != null" >
        display_image_url = #{record.displayImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null" >
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productStock != null" >
        product_stock = #{record.productStock,jdbcType=INTEGER},
      </if>
      <if test="record.postTime != null" >
        post_time = #{record.postTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.margin != null" >
        margin = #{record.margin,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingMethodCode != null" >
        shipping_method_code = #{record.shippingMethodCode,jdbcType=VARCHAR},
      </if>
      <if test="record.shareUser != null" >
        share_user = #{record.shareUser,jdbcType=VARCHAR},
      </if>
      <if test="record.isPublic != null" >
        is_public = #{record.isPublic,jdbcType=BIT},
      </if>
      <if test="record.isParent != null" >
        is_parent = #{record.isParent,jdbcType=BIT},
      </if>
      <if test="record.templateStatus != null" >
        template_status = #{record.templateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.templateType != null" >
        template_type = #{record.templateType,jdbcType=INTEGER},
      </if>
      <if test="record.templateLabel != null" >
        template_label = #{record.templateLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.carTypeJson != null" >
        car_type_json = #{record.carTypeJson,jdbcType=VARCHAR},
      </if>
      <if test="record.marketImagesJson != null" >
        market_images_json = #{record.marketImagesJson,jdbcType=VARCHAR},
      </if>
      <if test="record.titleRule != null" >
        title_rule = #{record.titleRule,jdbcType=VARCHAR},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.rootCategory != null" >
        root_category = #{record.rootCategory,jdbcType=INTEGER},
      </if>
      <if test="record.examineState != null" >
        examine_state = #{record.examineState,jdbcType=VARCHAR},
      </if>
      <if test="record.examineDate != null" >
        examine_date = #{record.examineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.examineSaleman != null" >
        examine_saleman = #{record.examineSaleman,jdbcType=VARCHAR},
      </if>
      <if test="record.videoLink != null" >
        video_link = #{record.videoLink,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=TINYINT},
      </if>
      <if test="record.recommendCategoryId != null" >
        recommend_category_id = #{record.recommendCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.recommendCategoryPath != null" >
        recommend_category_path = #{record.recommendCategoryPath,jdbcType=VARCHAR},
      </if>
      <if test="record.skuStatus != null" >
        sku_status = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.wenAnType != null" >
        wen_an_type = #{record.wenAnType,jdbcType=INTEGER},
      </if>
      <if test="record.sign != null" >
        sign = #{record.sign,jdbcType=BIT},
      </if>
      <if test="record.interSubjects != null" >
        inter_subjects = #{record.interSubjects,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopQualificationStructJson != null" >
        aeop_qualification_struct_json = #{record.aeopQualificationStructJson,jdbcType=VARCHAR},
      </if>
      <if test="record.sizeChartIdList != null" >
        size_chart_id_list = #{record.sizeChartIdList,jdbcType=VARCHAR},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=INTEGER},
      </if>
      <if test="record.popTempId != null" >
        pop_temp_id = #{record.popTempId,jdbcType=INTEGER},
      </if>
      <if test="record.halfTempId != null" >
        half_temp_id = #{record.halfTempId,jdbcType=BIGINT},
      </if>
      <if test="record.draftId != null" >
        draft_id = #{record.draftId,jdbcType=VARCHAR},
      </if>
      <if test="record.manufactureId != null" >
        manufacture_id = #{record.manufactureId,jdbcType=BIGINT},
      </if>
      <if test="record.areaDiscountRate != null" >
        area_discount_rate = #{record.areaDiscountRate,jdbcType=DOUBLE},
      </if>
      <if test="record.problemType != null" >
        problem_type = #{record.problemType,jdbcType=VARCHAR},
      </if>
      <if test="record.taxType != null" >
        tax_type = #{record.taxType,jdbcType=VARCHAR},
      </if>
      <if test="record.hacodeJson != null" >
        hacode_json = #{record.hacodeJson,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplate" >
    update ${table}
    <set >
      <if test="detail != null" >
        detail = #{detail,jdbcType=VARCHAR},
      </if>
      <if test="aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null" >
        delivery_time = #{deliveryTime,jdbcType=INTEGER},
      </if>
      <if test="promiseTemplateId != null" >
        promise_template_id = #{promiseTemplateId,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null" >
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryTableId != null" >
        category_table_id = #{categoryTableId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="productPrice != null" >
        product_price = #{productPrice,jdbcType=DOUBLE},
      </if>
      <if test="freightTemplateId != null" >
        freight_template_id = #{freightTemplateId,jdbcType=BIGINT},
      </if>
      <if test="imageUrls != null" >
        image_urls = #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="productUnit != null" >
        product_unit = #{productUnit,jdbcType=INTEGER},
      </if>
      <if test="packageType != null" >
        package_type = #{packageType,jdbcType=BIT},
      </if>
      <if test="lotNum != null" >
        lot_num = #{lotNum,jdbcType=INTEGER},
      </if>
      <if test="packageLength != null" >
        package_length = #{packageLength,jdbcType=INTEGER},
      </if>
      <if test="packageWidth != null" >
        package_width = #{packageWidth,jdbcType=INTEGER},
      </if>
      <if test="packageHeight != null" >
        package_height = #{packageHeight,jdbcType=INTEGER},
      </if>
      <if test="grossWeight != null" >
        gross_weight = #{grossWeight,jdbcType=VARCHAR},
      </if>
      <if test="isPackSell != null" >
        is_pack_sell = #{isPackSell,jdbcType=BIT},
      </if>
      <if test="isWholesale != null" >
        is_wholesale = #{isWholesale,jdbcType=BIT},
      </if>
      <if test="baseUnit != null" >
        base_unit = #{baseUnit,jdbcType=INTEGER},
      </if>
      <if test="addUnit != null" >
        add_unit = #{addUnit,jdbcType=INTEGER},
      </if>
      <if test="addWeight != null" >
        add_weight = #{addWeight,jdbcType=VARCHAR},
      </if>
      <if test="wsValidNum != null" >
        ws_valid_num = #{wsValidNum,jdbcType=INTEGER},
      </if>
      <if test="aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="bulkOrder != null" >
        bulk_order = #{bulkOrder,jdbcType=INTEGER},
      </if>
      <if test="bulkDiscount != null" >
        bulk_discount = #{bulkDiscount,jdbcType=INTEGER},
      </if>
      <if test="sizeChartId != null" >
        size_chart_id = #{sizeChartId,jdbcType=BIGINT},
      </if>
      <if test="reduceStrategy != null" >
        reduce_strategy = #{reduceStrategy,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileDetail != null" >
        mobile_detail = #{mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="couponStartDate != null" >
        coupon_start_date = #{couponStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="couponEndDate != null" >
        coupon_end_date = #{couponEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="aeopNationalQuoteConfiguration != null" >
        aeop_national_quote_configuration = #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      </if>
      <if test="aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastEditTime != null" >
        last_edit_time = #{lastEditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="aliexpressAccountNumber != null" >
        aliexpress_account_number = #{aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="displayImageUrl != null" >
        display_image_url = #{displayImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null" >
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productStock != null" >
        product_stock = #{productStock,jdbcType=INTEGER},
      </if>
      <if test="postTime != null" >
        post_time = #{postTime,jdbcType=TIMESTAMP},
      </if>
      <if test="margin != null" >
        margin = #{margin,jdbcType=DOUBLE},
      </if>
      <if test="shippingMethodCode != null" >
        shipping_method_code = #{shippingMethodCode,jdbcType=VARCHAR},
      </if>
      <if test="shareUser != null" >
        share_user = #{shareUser,jdbcType=VARCHAR},
      </if>
      <if test="isPublic != null" >
        is_public = #{isPublic,jdbcType=BIT},
      </if>
      <if test="isParent != null" >
        is_parent = #{isParent,jdbcType=BIT},
      </if>
      <if test="templateStatus != null" >
        template_status = #{templateStatus,jdbcType=INTEGER},
      </if>
      <if test="templateType != null" >
        template_type = #{templateType,jdbcType=INTEGER},
      </if>
      <if test="templateLabel != null" >
        template_label = #{templateLabel,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="carTypeJson != null" >
        car_type_json = #{carTypeJson,jdbcType=VARCHAR},
      </if>
      <if test="marketImagesJson != null" >
        market_images_json = #{marketImagesJson,jdbcType=VARCHAR},
      </if>
      <if test="titleRule != null" >
        title_rule = #{titleRule,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="rootCategory != null" >
        root_category = #{rootCategory,jdbcType=INTEGER},
      </if>
      <if test="examineState != null" >
        examine_state = #{examineState,jdbcType=VARCHAR},
      </if>
      <if test="examineDate != null" >
        examine_date = #{examineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="examineSaleman != null" >
        examine_saleman = #{examineSaleman,jdbcType=VARCHAR},
      </if>
      <if test="videoLink != null" >
        video_link = #{videoLink,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=TINYINT},
      </if>
      <if test="recommendCategoryId != null" >
        recommend_category_id = #{recommendCategoryId,jdbcType=INTEGER},
      </if>
      <if test="recommendCategoryPath != null" >
        recommend_category_path = #{recommendCategoryPath,jdbcType=VARCHAR},
      </if>
      <if test="skuStatus != null" >
        sku_status = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="wenAnType != null" >
        wen_an_type = #{wenAnType,jdbcType=INTEGER},
      </if>
      <if test="sign != null" >
        sign = #{sign,jdbcType=BIT},
      </if>
      <if test="interSubjects != null" >
        inter_subjects = #{interSubjects,jdbcType=VARCHAR},
      </if>
      <if test="aeopQualificationStructJson != null" >
        aeop_qualification_struct_json = #{aeopQualificationStructJson,jdbcType=VARCHAR},
      </if>
      <if test="sizeChartIdList != null" >
        size_chart_id_list = #{sizeChartIdList,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="popTempId != null" >
        pop_temp_id = #{popTempId,jdbcType=INTEGER},
      </if>
      <if test="halfTempId != null" >
        half_temp_id = #{halfTempId,jdbcType=BIGINT},
      </if>
      <if test="draftId != null" >
        draft_id = #{draftId,jdbcType=VARCHAR},
      </if>
      <if test="problemType != null" >
        problem_type = #{problemType,jdbcType=VARCHAR},
      </if>
      <if test="taxType != null" >
        tax_type = #{taxType,jdbcType=VARCHAR},
      </if>
      <if test="hacodeJson != null" >
        hacode_json = #{hacodeJson,jdbcType=VARCHAR},
      </if>
        manufacture_id = #{manufactureId,jdbcType=BIGINT},
        area_discount_rate = #{areaDiscountRate,jdbcType=DOUBLE},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


  <update id="updateTempLable">
    update ${table}
    <set >
        template_label = #{label,jdbcType=VARCHAR},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateTempStatus">
    update ${table}
    <set >
      template_status = #{status,jdbcType=INTEGER},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


  <update id="batchUpdateTempStatus">
    update ${table}
    <set>
      template_status = #{status,jdbcType=INTEGER},

      <if test="templateType != null" >
        template_type = #{templateType,jdbcType=INTEGER},
      </if>
    </set>
    where id IN
    <foreach collection="ids" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </update>

  <update id="updateTempType">
    update ${table}
    <set >
      template_type = #{type,jdbcType=INTEGER},
    </set>
    where id IN
    <foreach collection="ids" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </update>

  <update id="updateTypeByArticleNumber">
    update ${table}
    <set >
      template_type = #{type,jdbcType=INTEGER}
    </set>
    where article_number = #{mainSku,jdbcType=VARCHAR}
  </update>
  <update id="updateRecommendCategoryById">
     update aliexpress_template_model set recommend_category_id = #{categoryId}, recommend_category_path = #{categoryNamePath} where id = #{id}
  </update>
  <update id="bathUpdateSkuStatus">
    <foreach collection="templateList" index="index" item="record" open="" separator=";" close=";">
      update ${record.table}
      set sku_status = #{record.skuStatus,jdbcType=VARCHAR}
      where id = #{record.id,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="selectArticleNumberByExample" resultType="java.lang.String" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select distinct article_number
    from ${table}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>


  <select id="selectSuccessTemp" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ${table}
    where is_parent is false
    and template_status = 3
    GROUP BY article_number, category_id
  </select>

  <select id="spuTempStatistics" resultMap="BaseResultMap">
    select aliexpress_account_number,
      GROUP_CONCAT(case when template_status = 3 then article_number else null end)  article_number,
      GROUP_CONCAT(case when template_status = 4 then article_number else null end)  category_name
    from ${table}
    where is_parent is false
    and template_type = 3
    and create_time <![CDATA[ >= ]]> #{fromCreateDate}
    and create_time <![CDATA[ <= ]]> #{toCreateDate}
    GROUP BY aliexpress_account_number
  </select>

  <select id="selectByUpdateAttr" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Attr_Column_List" />
    from ${table}
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>
    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="listIdByStartId" resultType="java.lang.Integer">
    select id from aliexpress_template_model where id > #{startId} limit #{size}
  </select>
  <insert id="batchInsert">
    insert into ${table} (detail, aeop_ae_product_skus_json, delivery_time,
    promise_template_id, category_name, category_table_id,
    category_id, subject, product_price,
    freight_template_id, image_urls, product_unit,
    package_type, lot_num, package_length,
    package_width, package_height, gross_weight,
    is_pack_sell, is_wholesale, base_unit,
    add_unit, add_weight, ws_valid_num,
    aeop_ae_product_propertys_json, bulk_order,
    bulk_discount, size_chart_id, reduce_strategy,
    group_id, currency_code, mobile_detail,
    coupon_start_date, coupon_end_date, aeop_national_quote_configuration,
    aeop_ae_multimedia, article_number, creator,
    create_time, last_edit_time, aliexpress_account_number,
    display_image_url, product_code, product_stock,
    post_time, margin, shipping_method_code,
    share_user, is_public, is_parent,
    template_status, template_type, template_label, parent_id,
    car_type_json, market_images_json, title_rule, publish_role,
    root_category, examine_state, examine_date, examine_saleman, video_link,product_type,
    recommend_category_id,recommend_category_path,sku_status, wen_an_type, sign, inter_subjects,
    aeop_qualification_struct_json, size_chart_id_list, publish_type, pop_temp_id,
    half_temp_id, draft_id, manufacture_id, area_discount_rate, problem_type, tax_type,
    hacode_json
    )
    values
    <foreach collection="list" item="item" separator=",">
    (#{item.detail,jdbcType=VARCHAR}, #{item.aeopAeProductSkusJson,jdbcType=VARCHAR}, #{item.deliveryTime,jdbcType=INTEGER},
      #{item.promiseTemplateId,jdbcType=BIGINT}, #{item.categoryName,jdbcType=VARCHAR}, #{item.categoryTableId,jdbcType=BIGINT},
      #{item.categoryId,jdbcType=INTEGER}, #{item.subject,jdbcType=VARCHAR}, #{item.productPrice,jdbcType=DOUBLE},
      #{item.freightTemplateId,jdbcType=BIGINT}, #{item.imageUrls,jdbcType=VARCHAR}, #{item.productUnit,jdbcType=INTEGER},
      #{item.packageType,jdbcType=BIT}, #{item.lotNum,jdbcType=INTEGER}, #{item.packageLength,jdbcType=INTEGER},
      #{item.packageWidth,jdbcType=INTEGER}, #{item.packageHeight,jdbcType=INTEGER}, #{item.grossWeight,jdbcType=VARCHAR},
      #{item.isPackSell,jdbcType=BIT}, #{item.isWholesale,jdbcType=BIT}, #{item.baseUnit,jdbcType=INTEGER},
      #{item.addUnit,jdbcType=INTEGER}, #{item.addWeight,jdbcType=VARCHAR}, #{item.wsValidNum,jdbcType=INTEGER},
      #{item.aeopAeProductPropertysJson,jdbcType=VARCHAR}, #{item.bulkOrder,jdbcType=INTEGER},
      #{item.bulkDiscount,jdbcType=INTEGER}, #{item.sizeChartId,jdbcType=BIGINT}, #{item.reduceStrategy,jdbcType=VARCHAR},
      #{item.groupId,jdbcType=BIGINT}, #{item.currencyCode,jdbcType=VARCHAR}, #{item.mobileDetail,jdbcType=VARCHAR},
      #{item.couponStartDate,jdbcType=TIMESTAMP}, #{item.couponEndDate,jdbcType=TIMESTAMP}, #{item.aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      #{item.aeopAeMultimedia,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.lastEditTime,jdbcType=TIMESTAMP}, #{item.aliexpressAccountNumber,jdbcType=VARCHAR},
      #{item.displayImageUrl,jdbcType=VARCHAR}, #{item.productCode,jdbcType=VARCHAR}, #{item.productStock,jdbcType=INTEGER},
      #{item.postTime,jdbcType=TIMESTAMP}, #{item.margin,jdbcType=DOUBLE}, #{item.shippingMethodCode,jdbcType=VARCHAR},
      #{item.shareUser,jdbcType=VARCHAR}, #{item.isPublic,jdbcType=BIT}, #{item.isParent,jdbcType=BIT},
      #{item.templateStatus,jdbcType=INTEGER}, #{item.templateType,jdbcType=INTEGER}, #{item.templateLabel,jdbcType=VARCHAR},
      #{item.parentId,jdbcType=INTEGER}, #{item.carTypeJson,jdbcType=VARCHAR}, #{item.marketImagesJson,jdbcType=VARCHAR}, #{item.titleRule,jdbcType=VARCHAR}, #{item.publishRole,jdbcType=INTEGER},
      #{item.rootCategory,jdbcType=INTEGER}, #{item.examineState,jdbcType=VARCHAR}, #{item.examineDate,jdbcType=TIMESTAMP}, #{item.examineSaleman,jdbcType=VARCHAR}, #{item.videoLink,jdbcType=VARCHAR},
      #{item.productType,jdbcType=TINYINT}, #{item.recommendCategoryId,jdbcType=INTEGER}, #{item.recommendCategoryPath,jdbcType=VARCHAR}, #{item.skuStatus,jdbcType=VARCHAR}, #{item.wenAnType,jdbcType=INTEGER},
      #{item.sign,jdbcType=BIT}, #{item.interSubjects,jdbcType=VARCHAR}, #{item.aeopQualificationStructJson,jdbcType=VARCHAR}, #{item.sizeChartIdList,jdbcType=VARCHAR},
      #{item.publishType,jdbcType=INTEGER}, #{item.popTempId,jdbcType=INTEGER},#{item.halfTempId,jdbcType=BIGINT}, #{item.draftId,jdbcType=VARCHAR}, #{item.manufactureId,jdbcType=BIGINT},
      #{item.areaDiscountRate,jdbcType=DOUBLE}, #{item.problemType,jdbcType=VARCHAR}, #{item.taxType,jdbcType=VARCHAR},
      #{item.hacodeJson,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>