<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressProductLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressProductLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP" />
    <result column="operate_type" property="operateType" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="BIT" />
    <result column="fail_info" property="failInfo" jdbcType="VARCHAR" />
    <result column="price_before_edit" property="priceBeforeEdit" jdbcType="DOUBLE" />
    <result column="price_after_edit" property="priceAfterEdit" jdbcType="DOUBLE" />
    <result column="stock_before_edit" property="stockBeforeEdit" jdbcType="DOUBLE" />
    <result column="stock_after_edit" property="stockAfterEdit" jdbcType="DOUBLE" />
    <result column="weight_before_edit" property="weightBeforeEdit" jdbcType="DOUBLE" />
    <result column="weight_after_edit" property="weightAfterEdit" jdbcType="DOUBLE" />
    <result column="relation_type" property="relationType" jdbcType="INTEGER" />
    <result column="relation_id" property="relationId" jdbcType="VARCHAR" />
    <result column="operate_status" property="operateStatus" jdbcType="INTEGER" />
    <result column="request_data" property="requestData" jdbcType="VARCHAR" />
    <result column="result_type" property="resultType" jdbcType="VARCHAR" />
    <result column="new_remark" property="newRemark" jdbcType="VARCHAR" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="rule_table" property="ruleTable" jdbcType="VARCHAR" />
    <result column="rule_id" property="ruleId" jdbcType="VARCHAR" />
    <result column="problem_type" property="problemType" jdbcType="VARCHAR" />
    <result column="solution_type" property="solutionType" jdbcType="VARCHAR" />


  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <include refid="Example_Clause" />
    </where>
  </sql>

  <sql id="Example_Clause">
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>
  </sql>

  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, product_id, account_number, sku_code, `operator`, create_time, operate_time, operate_type,
    `result`, fail_info, price_before_edit, price_after_edit, stock_before_edit, stock_after_edit,
    weight_before_edit, weight_after_edit,
    relation_type, relation_id, operate_status, request_data, result_type, new_remark, rule_name, rule_table, rule_id, problem_type, solution_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressProductLogExample" >
    select
      <choose>
        <when test="fields != null and fields != ''">
          ${fields}
        </when>
        <otherwise>
          <include refid="Base_Column_List" />
        </otherwise>
      </choose>
    from aliexpress_product_log
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>

    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_product_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_product_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByDateAndType" >
    delete from aliexpress_product_log
    where operate_type = #{type} and create_time <![CDATA[ <= ]]> #{date}

  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressProductLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_product_log (product_id, account_number, sku_code, 
      `operator`, create_time, operate_time, operate_type,
      `result`, fail_info, price_before_edit, 
      price_after_edit, stock_before_edit, stock_after_edit, weight_before_edit, weight_after_edit,
      relation_type, relation_id, operate_status, request_data, result_type, new_remark, rule_name, rule_table, rule_id, problem_type, solution_type
      )
    values (#{productId,jdbcType=BIGINT}, #{accountNumber,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{operateTime,jdbcType=TIMESTAMP}, #{operateType,jdbcType=VARCHAR},
      #{result,jdbcType=BIT}, #{failInfo,jdbcType=VARCHAR}, #{priceBeforeEdit,jdbcType=DOUBLE}, 
      #{priceAfterEdit,jdbcType=DOUBLE}, #{stockBeforeEdit,jdbcType=DOUBLE}, #{stockAfterEdit,jdbcType=DOUBLE}, #{weightBeforeEdit,jdbcType=DOUBLE}, #{weightAfterEdit,jdbcType=DOUBLE},
      #{relationType,jdbcType=INTEGER}, #{relationId,jdbcType=VARCHAR}, #{operateStatus,jdbcType=INTEGER}, #{requestData,jdbcType=VARCHAR},
      #{resultType,jdbcType=VARCHAR}, #{newRemark,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{ruleTable,jdbcType=VARCHAR},
      #{ruleId,jdbcType=VARCHAR}, #{problemType,jdbcType=VARCHAR}, #{solutionType,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressProductLogExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_product_log
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and account_number IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectCountByType" resultType="com.estone.erp.publish.smt.model.dto.AliexpressProductLogCountDto">
    SELECT
    accountNumber,
    ruleName,
    SUM(totalCount) AS totalCount,
    SUM(successCount) AS successCount,
    SUM(failureCount) AS failureCount,
    JSON_OBJECTAGG(problem_type, failureCount) AS failureReasonsCountJson
    FROM (
    SELECT
    account_number AS accountNumber,
    rule_name AS ruleName,
    COALESCE(problem_type, '其他') AS problem_type,
    COUNT(*) AS totalCount,
    SUM(result = 1) AS successCount,
    SUM(result = 0) AS failureCount
    FROM
    aliexpress_product_log
    WHERE
    operate_type = #{code}
    AND operate_status = 30
    AND DATE(operate_time) = CURDATE() - INTERVAL 1 DAY
    <if test="code == 'edit_half_tg_stock' or code == 'edit stock'">
      AND stock_before_edit != stock_after_edit
    </if>
    <if test="code == 'single_discount_create'">
      AND new_remark != '存在正在进行的活动且剩余时间最大值大于3天，不自动创建续期活动'
    </if>
    <if test="code == 'single_discount_add_pro'">
      AND (
      fail_info NOT LIKE CONCAT('%','Illegal product id', '%')
      OR result = 1
      OR (result = 0 AND (fail_info IS NULL OR fail_info = ''))
      )
    </if>
    GROUP BY
    account_number,
    rule_name,
    COALESCE(problem_type, '其他')
    ) sub
    GROUP BY
    accountNumber,
    ruleName
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_product_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null" >
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null" >
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operateTime != null" >
        operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operateType != null" >
        operate_type = #{record.operateType,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null" >
        `result` = #{record.result,jdbcType=BIT},
      </if>
      <if test="record.failInfo != null" >
        fail_info = #{record.failInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.priceBeforeEdit != null" >
        price_before_edit = #{record.priceBeforeEdit,jdbcType=DOUBLE},
      </if>
      <if test="record.priceAfterEdit != null" >
        price_after_edit = #{record.priceAfterEdit,jdbcType=DOUBLE},
      </if>
      <if test="record.stockBeforeEdit != null" >
        stock_before_edit = #{record.stockBeforeEdit,jdbcType=DOUBLE},
      </if>
      <if test="record.relationType != null" >
        relation_type = #{record.relationType,jdbcType=INTEGER},
      </if>
      <if test="record.relationId != null" >
        relation_id = #{record.relationId,jdbcType=VARCHAR},
      </if>
      <if test="record.operateStatus != null" >
        operate_status = #{record.operateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.requestData != null" >
        request_data = #{record.requestData,jdbcType=VARCHAR},
      </if>
      <if test="record.resultType != null" >
        result_type = #{record.resultType,jdbcType=VARCHAR},
      </if>
      <if test="record.newRemark != null" >
        new_remark = #{record.newRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleTable != null" >
        rule_table = #{record.ruleTable,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleId != null" >
        rule_id = #{record.ruleId,jdbcType=VARCHAR},
      </if>
      <if test="record.problemType != null" >
        problem_type = #{record.problemType,jdbcType=VARCHAR},
      </if>
      <if test="record.solutionType != null" >
        solution_type = #{record.solutionType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressProductLog" >
    update aliexpress_product_log
    <set >
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null" >
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateTime != null" >
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateType != null" >
        operate_type = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        `result` = #{result,jdbcType=BIT},
      </if>
      <if test="failInfo != null" >
        fail_info = #{failInfo,jdbcType=VARCHAR},
      </if>
      <if test="priceBeforeEdit != null" >
        price_before_edit = #{priceBeforeEdit,jdbcType=DOUBLE},
      </if>
      <if test="priceAfterEdit != null" >
        price_after_edit = #{priceAfterEdit,jdbcType=DOUBLE},
      </if>
      <if test="stockBeforeEdit != null" >
        stock_before_edit = #{stockBeforeEdit,jdbcType=DOUBLE},
      </if>
      <if test="stockAfterEdit != null" >
        stock_after_edit = #{stockAfterEdit,jdbcType=DOUBLE},
      </if>
      <if test="relationType != null" >
        relation_type = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="relationId != null" >
        relation_id = #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="operateStatus != null" >
        operate_status = #{operateStatus,jdbcType=INTEGER},
      </if>
      <if test="requestData != null" >
        request_data = #{requestData,jdbcType=VARCHAR},
      </if>
      <if test="resultType != null" >
        result_type = #{resultType,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="newRemark != null" >
        new_remark = #{newRemark,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleTable != null" >
        rule_table = #{ruleTable,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null" >
        rule_id = #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="problemType != null" >
        problem_type = #{problemType,jdbcType=VARCHAR},
      </if>
      <if test="solutionType != null" >
        solution_type = #{solutionType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update aliexpress_product_log
        <set >
          <if test="record.productId != null" >
            product_id = #{record.productId,jdbcType=BIGINT},
          </if>
          <if test="record.accountNumber != null" >
            account_number = #{record.accountNumber,jdbcType=VARCHAR},
          </if>
          <if test="record.skuCode != null" >
            sku_code = #{record.skuCode,jdbcType=VARCHAR},
          </if>
          <if test="record.operator != null" >
            `operator` = #{record.operator,jdbcType=VARCHAR},
          </if>
          <if test="record.createTime != null" >
            create_time = #{record.createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.operateTime != null" >
            operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.operateType != null" >
            operate_type = #{record.operateType,jdbcType=VARCHAR},
          </if>
          <if test="record.result != null" >
            `result` = #{record.result,jdbcType=BIT},
          </if>
          <if test="record.failInfo != null" >
            fail_info = #{record.failInfo,jdbcType=VARCHAR},
          </if>
          <if test="record.priceBeforeEdit != null" >
            price_before_edit = #{record.priceBeforeEdit,jdbcType=DOUBLE},
          </if>
          <if test="record.priceAfterEdit != null" >
            price_after_edit = #{record.priceAfterEdit,jdbcType=DOUBLE},
          </if>
          <if test="record.stockBeforeEdit != null" >
            stock_before_edit = #{record.stockBeforeEdit,jdbcType=DOUBLE},
          </if>
          <if test="record.stockAfterEdit != null" >
            stock_after_edit = #{record.stockAfterEdit,jdbcType=DOUBLE},
          </if>
          <if test="record.relationType != null" >
            relation_type = #{record.relationType,jdbcType=INTEGER},
          </if>
          <if test="record.relationId != null" >
            relation_id = #{record.relationId,jdbcType=VARCHAR},
          </if>
          <if test="record.operateStatus != null" >
            operate_status = #{record.operateStatus,jdbcType=INTEGER},
          </if>
          <if test="record.requestData != null" >
            request_data = #{record.requestData,jdbcType=VARCHAR},
          </if>
          <if test="record.resultType != null" >
            result_type = #{record.resultType,jdbcType=VARCHAR},
          </if>
          <if test="record.ruleName != null" >
            rule_name = #{record.ruleName,jdbcType=VARCHAR},
          </if>
          <if test="record.newRemark != null" >
            new_remark = #{record.newRemark,jdbcType=VARCHAR},
          </if>
          <if test="record.ruleName != null" >
            rule_name = #{record.ruleName,jdbcType=VARCHAR},
          </if>
          <if test="record.ruleTable != null" >
            rule_table = #{record.ruleTable,jdbcType=VARCHAR},
          </if>
          <if test="record.ruleId != null" >
            rule_id = #{record.ruleId,jdbcType=VARCHAR},
          </if>
          <if test="record.problemType != null" >
            problem_type = #{record.problemType,jdbcType=VARCHAR},
          </if>
          <if test="record.solutionType != null" >
            solution_type = #{record.solutionType,jdbcType=VARCHAR},
          </if>
        </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>