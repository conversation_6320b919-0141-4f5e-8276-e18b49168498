<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.SmtStockUpdateLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.SmtStockUpdateLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
    <result column="redis_stock" property="redisStock" jdbcType="INTEGER" />
    <result column="stock_before" property="stockBefore" jdbcType="INTEGER" />
    <result column="stock_after" property="stockAfter" jdbcType="INTEGER" />
    <result column="order_num_30d" property="orderNum30d" jdbcType="INTEGER" />
    <result column="order_days_within_30d" property="orderDaysWithin30d" jdbcType="INTEGER" />
    <result column="smt_order_rate" property="smtOrderRate" jdbcType="DOUBLE" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="result" property="result" jdbcType="BIT" />
    <result column="fail_info" property="failInfo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, product_id, article_number, sku_id, redis_stock, stock_before, stock_after, 
    order_num_30d, order_days_within_30d, smt_order_rate, create_date, `result`, fail_info
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.SmtStockUpdateLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_stock_update_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from smt_stock_update_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_stock_update_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.SmtStockUpdateLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_stock_update_log (account, product_id, article_number, 
      sku_id, redis_stock, stock_before, 
      stock_after, order_num_30d, order_days_within_30d, 
      smt_order_rate, create_date, `result`, 
      fail_info)
    values (#{account,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{articleNumber,jdbcType=VARCHAR}, 
      #{skuId,jdbcType=VARCHAR}, #{redisStock,jdbcType=INTEGER}, #{stockBefore,jdbcType=INTEGER}, 
      #{stockAfter,jdbcType=INTEGER}, #{orderNum30d,jdbcType=INTEGER}, #{orderDaysWithin30d,jdbcType=INTEGER}, 
      #{smtOrderRate,jdbcType=DOUBLE}, #{createDate,jdbcType=TIMESTAMP}, #{result,jdbcType=BIT}, 
      #{failInfo,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.SmtStockUpdateLogExample" resultType="java.lang.Integer" >
    select count(*) from smt_stock_update_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_stock_update_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null" >
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.redisStock != null" >
        redis_stock = #{record.redisStock,jdbcType=INTEGER},
      </if>
      <if test="record.stockBefore != null" >
        stock_before = #{record.stockBefore,jdbcType=INTEGER},
      </if>
      <if test="record.stockAfter != null" >
        stock_after = #{record.stockAfter,jdbcType=INTEGER},
      </if>
      <if test="record.orderNum30d != null" >
        order_num_30d = #{record.orderNum30d,jdbcType=INTEGER},
      </if>
      <if test="record.orderDaysWithin30d != null" >
        order_days_within_30d = #{record.orderDaysWithin30d,jdbcType=INTEGER},
      </if>
      <if test="record.smtOrderRate != null" >
        smt_order_rate = #{record.smtOrderRate,jdbcType=DOUBLE},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.result != null" >
        `result` = #{record.result,jdbcType=BIT},
      </if>
      <if test="record.failInfo != null" >
        fail_info = #{record.failInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.SmtStockUpdateLog" >
    update smt_stock_update_log
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="redisStock != null" >
        redis_stock = #{redisStock,jdbcType=INTEGER},
      </if>
      <if test="stockBefore != null" >
        stock_before = #{stockBefore,jdbcType=INTEGER},
      </if>
      <if test="stockAfter != null" >
        stock_after = #{stockAfter,jdbcType=INTEGER},
      </if>
      <if test="orderNum30d != null" >
        order_num_30d = #{orderNum30d,jdbcType=INTEGER},
      </if>
      <if test="orderDaysWithin30d != null" >
        order_days_within_30d = #{orderDaysWithin30d,jdbcType=INTEGER},
      </if>
      <if test="smtOrderRate != null" >
        smt_order_rate = #{smtOrderRate,jdbcType=DOUBLE},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="result != null" >
        `result` = #{result,jdbcType=BIT},
      </if>
      <if test="failInfo != null" >
        fail_info = #{failInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>