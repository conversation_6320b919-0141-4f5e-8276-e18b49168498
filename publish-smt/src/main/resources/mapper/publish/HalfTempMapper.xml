<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.HalfTempMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.HalfTemp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="temp_id" property="tempId" jdbcType="INTEGER" />
    <result column="draft_id" property="draftId" jdbcType="VARCHAR" />
    <result column="joined_country_list" property="joinedCountryList" jdbcType="VARCHAR" />
    <result column="product_sku_list" property="productSkuList" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, temp_id, draft_id, joined_country_list, product_sku_list, create_date, 
    create_by, update_date, update_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.HalfTempExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from half_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from half_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from half_temp
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.HalfTemp" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into half_temp (account, temp_id, draft_id, 
      joined_country_list, product_sku_list, create_date, 
      create_by, update_date, update_by
      )
    values (#{account,jdbcType=VARCHAR}, #{tempId,jdbcType=INTEGER}, #{draftId,jdbcType=VARCHAR}, 
      #{joinedCountryList,jdbcType=VARCHAR}, #{productSkuList,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.HalfTempExample" resultType="java.lang.Integer" >
    select count(*) from half_temp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update half_temp
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.tempId != null" >
        temp_id = #{record.tempId,jdbcType=INTEGER},
      </if>
      <if test="record.draftId != null" >
        draft_id = #{record.draftId,jdbcType=VARCHAR},
      </if>
      <if test="record.joinedCountryList != null" >
        joined_country_list = #{record.joinedCountryList,jdbcType=VARCHAR},
      </if>
      <if test="record.productSkuList != null" >
        product_sku_list = #{record.productSkuList,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.HalfTemp" >
    update half_temp
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="tempId != null" >
        temp_id = #{tempId,jdbcType=INTEGER},
      </if>
      <if test="draftId != null" >
        draft_id = #{draftId,jdbcType=VARCHAR},
      </if>
      <if test="joinedCountryList != null" >
        joined_country_list = #{joinedCountryList,jdbcType=VARCHAR},
      </if>
      <if test="productSkuList != null" >
        product_sku_list = #{productSkuList,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>