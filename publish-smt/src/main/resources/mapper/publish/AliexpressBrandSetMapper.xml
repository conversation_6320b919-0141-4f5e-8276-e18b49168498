<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressBrandSetMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressBrandSet" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="sole_id" property="soleId" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="category_path" property="categoryPath" jdbcType="VARCHAR" />
    <result column="ae_brand_id" property="aeBrandId" jdbcType="BIGINT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="brand_deadline_time" property="brandDeadlineTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="crawl_time" property="crawlTime" jdbcType="TIMESTAMP" />
    <result column="state_update_time" property="stateUpdateTime" jdbcType="TIMESTAMP" />
    <result column="is_first" property="isFirst" jdbcType="BIT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_name, sole_id, `status`, brand_name, category_path, ae_brand_id, category_id, 
    brand_deadline_time, updated_time, crawl_time, state_update_time, is_first, create_by, 
    create_date, last_update_by, last_update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressBrandSetExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_brand_set
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_brand_set
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_brand_set
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressBrandSet" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_brand_set (account_name, sole_id, `status`, 
      brand_name, category_path, ae_brand_id, 
      category_id, brand_deadline_time, updated_time, 
      crawl_time, state_update_time, is_first, 
      create_by, create_date, last_update_by, 
      last_update_date)
    values (#{accountName,jdbcType=VARCHAR}, #{soleId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{categoryPath,jdbcType=VARCHAR}, #{aeBrandId,jdbcType=BIGINT}, 
      #{categoryId,jdbcType=INTEGER}, #{brandDeadlineTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, 
      #{crawlTime,jdbcType=TIMESTAMP}, #{stateUpdateTime,jdbcType=TIMESTAMP}, #{isFirst,jdbcType=BIT}, 
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateBy,jdbcType=VARCHAR}, 
      #{lastUpdateDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressBrandSetExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_brand_set
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_brand_set
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountName != null" >
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.soleId != null" >
        sole_id = #{record.soleId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryPath != null" >
        category_path = #{record.categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="record.aeBrandId != null" >
        ae_brand_id = #{record.aeBrandId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.brandDeadlineTime != null" >
        brand_deadline_time = #{record.brandDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.crawlTime != null" >
        crawl_time = #{record.crawlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.stateUpdateTime != null" >
        state_update_time = #{record.stateUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isFirst != null" >
        is_first = #{record.isFirst,jdbcType=BIT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressBrandSet" >
    update aliexpress_brand_set
    <set >
      <if test="accountName != null" >
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="soleId != null" >
        sole_id = #{soleId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="categoryPath != null" >
        category_path = #{categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="aeBrandId != null" >
        ae_brand_id = #{aeBrandId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandDeadlineTime != null" >
        brand_deadline_time = #{brandDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crawlTime != null" >
        crawl_time = #{crawlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stateUpdateTime != null" >
        state_update_time = #{stateUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isFirst != null" >
        is_first = #{isFirst,jdbcType=BIT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>