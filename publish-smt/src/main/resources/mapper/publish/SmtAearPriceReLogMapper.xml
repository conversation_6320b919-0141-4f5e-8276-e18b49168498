<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.SmtAearPriceReLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.SmtAearPriceReLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="request_json" property="requestJson" jdbcType="VARCHAR" />
    <result column="re_count" property="reCount" jdbcType="INTEGER" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="result_type" property="resultType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, product_id, request_json, re_count, create_date, update_date, result_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.SmtAearPriceReLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_aear_price_re_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from smt_aear_price_re_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_aear_price_re_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <delete id="deleteByDate" >
    delete from smt_aear_price_re_log
    where create_date <![CDATA[ <= ]]> #{date}
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.SmtAearPriceReLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_aear_price_re_log (account, product_id, request_json, 
      re_count, create_date, update_date, 
      result_type)
    values (#{account,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{requestJson,jdbcType=VARCHAR}, 
      #{reCount,jdbcType=INTEGER}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{resultType,jdbcType=INTEGER})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.SmtAearPriceReLogExample" resultType="java.lang.Integer" >
    select count(*) from smt_aear_price_re_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_aear_price_re_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.requestJson != null" >
        request_json = #{record.requestJson,jdbcType=VARCHAR},
      </if>
      <if test="record.reCount != null" >
        re_count = #{record.reCount,jdbcType=INTEGER},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resultType != null" >
        result_type = #{record.resultType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.SmtAearPriceReLog" >
    update smt_aear_price_re_log
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="requestJson != null" >
        request_json = #{requestJson,jdbcType=VARCHAR},
      </if>
      <if test="reCount != null" >
        re_count = #{reCount,jdbcType=INTEGER},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="resultType != null" >
        result_type = #{resultType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>