<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="usable" property="usable" jdbcType="BIT" />
    <result column="deliveryTime" property="deliverytime" jdbcType="INTEGER" />
    <result column="wholesale" property="wholesale" jdbcType="BIT" />
    <result column="bulkOrder" property="bulkorder" jdbcType="INTEGER" />
    <result column="bulkDiscount" property="bulkdiscount" jdbcType="INTEGER" />
    <result column="stock" property="stock" jdbcType="INTEGER" />
    <result column="wsValidNum" property="wsvalidnum" jdbcType="INTEGER" />
    <result column="labels" property="labels" jdbcType="VARCHAR" />
    <result column="timing_interval" property="timingInterval" jdbcType="INTEGER" />
    <result column="timing_minute" property="timingMinute" jdbcType="INTEGER" />
    <result column="timing_hour" property="timingHour" jdbcType="INTEGER" />
    <result column="title_json" property="titleJson" jdbcType="VARCHAR" />
    <result column="logo_text" property="logoText" jdbcType="VARCHAR" />
    <result column="logo_position" property="logoPosition" jdbcType="VARCHAR" />
    <result column="font_size" property="fontSize" jdbcType="VARCHAR" />
    <result column="logo_color" property="logoColor" jdbcType="VARCHAR" />
    <result column="max_publish_num" property="maxPublishNum" jdbcType="INTEGER" />
    <result column="category_ids" property="categoryIds" jdbcType="VARCHAR" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="shop_id" property="shopId" jdbcType="VARCHAR" />
    <result column="open_date" property="openDate" jdbcType="VARCHAR" />
    <result column="cny" property="cny" jdbcType="BIT" />
    <result column="auto_grounding_new" property="autoGroundingNew" jdbcType="BIT" />
    <result column="auto_update_weight" property="autoUpdateWeight" jdbcType="BIT" />
    <result column="auto_supply_stock" property="autoSupplyStock" jdbcType="BIT" />
    <result column="auto_update_title" property="autoUpdateTitle" jdbcType="BIT" />
    <result column="auto_update_detail" property="autoUpdateDetail" jdbcType="BIT" />
    <result column="auto_update_sonimg" property="autoUpdateSonimg" jdbcType="BIT" />
    <result column="auto_recommend_new_product" property="autoRecommendNewProduct" jdbcType="BIT" />
    <result column="auto_update_deficit_order" property="autoUpdateDeficitOrder" jdbcType="BIT" />
    <result column="auto_down_for_bad_item" property="autoDownForBadItem" jdbcType="BIT" />
    <result column="packing_type" property="packingType" jdbcType="INTEGER" />
    <result column="package_length" property="packageLength" jdbcType="INTEGER" />
    <result column="package_width" property="packageWidth" jdbcType="INTEGER" />
    <result column="package_height" property="packageHeight" jdbcType="INTEGER" />
    <result column="add_weight" property="addWeight" jdbcType="DOUBLE" />
    <result column="title_type" property="titleType" jdbcType="INTEGER" />
    <result column="title_value" property="titleValue" jdbcType="VARCHAR" />
    <result column="root_category" property="rootCategory" jdbcType="VARCHAR" />
    <result column="from_weight" property="fromWeight" jdbcType="DOUBLE" />
    <result column="to_weight" property="toWeight" jdbcType="DOUBLE" />
    <result column="from_price" property="fromPrice" jdbcType="DOUBLE" />
    <result column="to_price" property="toPrice" jdbcType="DOUBLE" />
    <result column="special_tags" property="specialTags" jdbcType="VARCHAR" />
    <result column="item_count" property="itemCount" jdbcType="INTEGER" />
    <result column="sales_yesterday_rate" property="salesYesterdayRate" jdbcType="DOUBLE" />
    <result column="sales_30d_rate" property="sales_30d_rate" jdbcType="DOUBLE" />
    <result column="sales_60d_rate" property="sales_60d_rate" jdbcType="DOUBLE" />
    <result column="product_dev" property="productDev" jdbcType="VARCHAR" />
    <result column="watermark_template_id_str" property="watermarkTemplateIdStr" jdbcType="VARCHAR"/>
    <result column="main_brand" property="mainBrand" jdbcType="VARCHAR"/>
    <result column="auto_calc_price" property="autoCalcPrice" jdbcType="BIT" />
    <result column="month_sale_target" jdbcType="DOUBLE" property="monthSaleTarget"/>
    <result column="year_sale_target" jdbcType="DOUBLE" property="yearSaleTarget"/>
    <result column="month_add_listing_target" jdbcType="INTEGER" property="monthAddListingTarget"/>
    <result column="positive_rate" jdbcType="DOUBLE" property="positiveRate"/>
    <result column="detailed_seller_ratings" jdbcType="VARCHAR" property="detailedSellerRatings"/>
    <result column="feedback_info" jdbcType="VARCHAR" property="feedbackInfo"/>
    <result column="auto_add_epr_fee" property="autoAddEprFee" jdbcType="BIT" />
    <result column="add_epr_pack_fee" property="addEprPackFee" jdbcType="BIT" />
    <result column="auto_retire_tort" property="autoRetireTort" jdbcType="BIT" />
    <result column="tort_type" property="tortType" jdbcType="VARCHAR" />
    <result column="auto_remove_tort_word" property="autoRemoveTortWord" jdbcType="BIT" />
    <result column="tg_profit_rate" property="tgProfitRate" jdbcType="DOUBLE" />
    <result column="half_tg_profit_rate" property="halfTgProfitRate" jdbcType="DOUBLE" />
    <result column="auto_update_qualifications" property="autoUpdateQualifications" jdbcType="BIT" />
    <result column="auto_half_reservation" property="autoHalfReservation" jdbcType="BIT" />
    <result column="half_shipping_cost" property="halfShippingCost" jdbcType="DOUBLE" />
    <result column="half_operate_cost" property="halfOperateCost" jdbcType="DOUBLE" />
    <result column="join_countrys" property="joinCountrys" jdbcType="VARCHAR" />
    <result column="publish_json" property="publishJson" jdbcType="VARCHAR" />
    <result column="auto_group_id" property="autoGroupId" jdbcType="BIGINT" />
    <result column="limit_store_product_number" property="limitStoreProductNumber" jdbcType="INTEGER" />
    <result column="delivery_30d_rate" property="delivery30dRate" jdbcType="DOUBLE" />
    <result column="delivery_30d_rate_update_time" property="delivery30dRateUpdateTime" jdbcType="TIMESTAMP" />
    <result column="half_forbid_sale_country" property="halfForbidSaleCountry" jdbcType="VARCHAR" />
    <result column="product_current_month_limit_num" property="productCurrentMonthLimitNum" jdbcType="INTEGER" />
    <result column="product_total_limit_num" property="productTotalLimitNum" jdbcType="INTEGER" />
    <result column="product_total_audit_num" property="productTotalAuditNum" jdbcType="INTEGER" />
    <result column="product_current_month_pub_num" property="productCurrentMonthPubNum" jdbcType="INTEGER" />
    <result column="product_total_online_num" property="productTotalOnlineNum" jdbcType="INTEGER" />
    <result column="auto_update_attr" property="autoUpdateAttr" jdbcType="BIGINT" />
    <result column="tax_type" property="taxType" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Example_Where_Clause" >
    <where>
      <include refid="Example_Clause" />
    </where>
  </sql>

  <sql id="Example_Clause" >
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, usable, deliveryTime, wholesale, bulkOrder, bulkDiscount, stock, wsValidNum, category_ids, labels,
    timing_interval, timing_minute, timing_hour, title_json, logo_text, logo_position,
    font_size, logo_color, max_publish_num, update_by, update_date, create_by, create_date, shop_id,open_date, cny, auto_grounding_new,
    auto_update_weight, auto_supply_stock, auto_update_title, auto_update_detail, auto_update_sonimg, auto_recommend_new_product,
    auto_update_deficit_order, auto_down_for_bad_item, packing_type, package_length, package_width, package_height, add_weight, title_type, title_value, root_category,
    from_weight, to_weight, from_price, to_price, special_tags, item_count, sales_yesterday_rate, sales_30d_rate, sales_60d_rate, product_dev,
    watermark_template_id_str, main_brand, auto_calc_price, month_sale_target, year_sale_target, month_add_listing_target,
    positive_rate, detailed_seller_ratings, feedback_info, auto_add_epr_fee, add_epr_pack_fee, auto_retire_tort, tort_type,
    auto_remove_tort_word, tg_profit_rate, half_tg_profit_rate, auto_update_qualifications, auto_half_reservation,
    half_shipping_cost, half_operate_cost, join_countrys, publish_json, auto_group_id, limit_store_product_number,
    delivery_30d_rate, delivery_30d_rate_update_time, half_forbid_sale_country,
    product_current_month_limit_num,product_total_limit_num, product_total_audit_num, product_current_month_pub_num,
    product_total_online_num, auto_update_attr, tax_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_config
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and account IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>

    </where>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectCustomColumnByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigExample" >
    select ${columns}
    from aliexpress_config
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and account IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>

    </where>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_config (account, usable, deliveryTime,
      wholesale, bulkOrder, bulkDiscount,
      stock, wsValidNum, labels,
      timing_interval, timing_minute, timing_hour,
      title_json, logo_text, logo_position,
      font_size, logo_color, max_publish_num,
      category_ids, update_by, update_date,
      create_by, create_date, shop_id,open_date, cny, auto_grounding_new,
      auto_update_weight, auto_supply_stock, auto_update_title, auto_update_detail, auto_update_sonimg, auto_recommend_new_product,
      auto_update_deficit_order, auto_down_for_bad_item,
      packing_type, package_length, package_width, package_height, add_weight, title_type, title_value,
      from_weight, to_weight, from_price, to_price, special_tags, item_count, product_dev,
      watermark_template_id_str, main_brand, auto_calc_price, month_sale_target, year_sale_target, month_add_listing_target,
      positive_rate, detailed_seller_ratings, feedback_info, auto_add_epr_fee, add_epr_pack_fee, auto_retire_tort, tort_type,
      auto_remove_tort_word, tg_profit_rate, half_tg_profit_rate, auto_update_qualifications, auto_half_reservation,
      half_shipping_cost, half_operate_cost, join_countrys, publish_json, auto_group_id, limit_store_product_number,
      delivery_30d_rate, delivery_30d_rate_update_time, half_forbid_sale_country,
      product_current_month_limit_num,product_total_limit_num, product_total_audit_num,
      product_current_month_pub_num, product_total_online_num, auto_update_attr, tax_type
      )
    values (#{account,jdbcType=VARCHAR}, #{usable,jdbcType=BIT}, #{deliverytime,jdbcType=INTEGER},
      #{wholesale,jdbcType=BIT}, #{bulkorder,jdbcType=INTEGER}, #{bulkdiscount,jdbcType=INTEGER},
      #{stock,jdbcType=INTEGER}, #{wsvalidnum,jdbcType=INTEGER}, #{labels,jdbcType=VARCHAR},
      #{timingInterval,jdbcType=INTEGER}, #{timingMinute,jdbcType=INTEGER}, #{timingHour,jdbcType=INTEGER},
      #{titleJson,jdbcType=VARCHAR}, #{logoText,jdbcType=VARCHAR}, #{logoPosition,jdbcType=VARCHAR},
      #{fontSize,jdbcType=VARCHAR}, #{logoColor,jdbcType=VARCHAR}, #{maxPublishNum,jdbcType=INTEGER},
      #{categoryIds,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{shopId,jdbcType=VARCHAR},#{openDate,jdbcType=VARCHAR}, #{cny,jdbcType=BIT}, #{autoGroundingNew,jdbcType=BIT},
      #{autoUpdateWeight,jdbcType=BIT}, #{autoSupplyStock,jdbcType=BIT}, #{autoUpdateTitle,jdbcType=BIT},
      #{autoUpdateDetail,jdbcType=BIT}, #{autoUpdateSonimg,jdbcType=BIT}, #{autoRecommendNewProduct,jdbcType=BIT},#{autoUpdateDeficitOrder,jdbcType=BIT},
      #{autoDownForBadItem,jdbcType=BIT},
      #{packingType,jdbcType=INTEGER}, #{packageLength,jdbcType=INTEGER}, #{packageWidth,jdbcType=INTEGER}, #{packageHeight,jdbcType=INTEGER},
      #{addWeight,jdbcType=DOUBLE}, #{titleType,jdbcType=INTEGER}, #{titleValue,jdbcType=VARCHAR},
      #{fromWeight,jdbcType=DOUBLE}, #{toWeight,jdbcType=DOUBLE}, #{fromPrice,jdbcType=DOUBLE}, #{toPrice,jdbcType=DOUBLE}, #{specialTags,jdbcType=VARCHAR},
      #{itemCount,jdbcType=INTEGER}, #{productDev,jdbcType=VARCHAR}, #{watermarkTemplateIdStr,jdbcType=VARCHAR}, #{mainBrand,jdbcType=VARCHAR}, #{autoCalcPrice,jdbcType=BIT},
      #{monthSaleTarget,jdbcType=DOUBLE}, #{yearSaleTarget,jdbcType=DOUBLE}, #{monthAddListingTarget,jdbcType=INTEGER},
      #{positiveRate,jdbcType=DOUBLE}, #{detailedSellerRatings,jdbcType=VARCHAR}, #{feedbackInfo,jdbcType=VARCHAR}, #{autoAddEprFee,jdbcType=BIT}, #{addEprPackFee,jdbcType=BIT},
      #{autoRetireTort,jdbcType=BIT}, #{tortType,jdbcType=VARCHAR}, #{autoRemoveTortWord,jdbcType=BIT}, #{tgProfitRate,jdbcType=DOUBLE}, #{halfTgProfitRate,jdbcType=DOUBLE},
      #{autoUpdateQualifications,jdbcType=BIT}, #{autoHalfReservation,jdbcType=BIT},
      #{halfShippingCost,jdbcType=DOUBLE}, #{halfOperateCost,jdbcType=DOUBLE}, #{joinCountrys,jdbcType=VARCHAR}, #{publishJson,jdbcType=VARCHAR},
      #{autoGroupId,jdbcType=BIGINT}, #{limitStoreProductNumber,jdbcType=INTEGER},
      #{delivery30dRate,jdbcType=DOUBLE}, #{delivery30dRateUpdateTime,jdbcType=TIMESTAMP}, #{halfForbidSaleCountry,jdbcType=VARCHAR},
      #{productCurrentMonthLimitNum,jdbcType=INTEGER}, #{productTotalLimitNum,jdbcType=INTEGER}, #{productTotalAuditNum,jdbcType=INTEGER},
      #{productCurrentMonthPubNum,jdbcType=INTEGER}, #{productTotalOnlineNum,jdbcType=INTEGER}, #{autoUpdateAttr,jdbcType=INTEGER},
      #{taxType,jdbcType=VARCHAR}
    )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_config
    <where>
      <if test="_parameter != null" >
        <include refid="Example_Clause" />
      </if>

      <if test="null != authSellerList and authSellerList.size > 0">
        and account IN
        <foreach collection="authSellerList" item = "seller" open="(" separator="," close=")">
          #{seller}
        </foreach>
      </if>
    </where>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.usable != null" >
        usable = #{record.usable,jdbcType=BIT},
      </if>
      <if test="record.deliverytime != null" >
        deliveryTime = #{record.deliverytime,jdbcType=INTEGER},
      </if>
      <if test="record.wholesale != null" >
        wholesale = #{record.wholesale,jdbcType=BIT},
      </if>
      <if test="record.bulkorder != null" >
        bulkOrder = #{record.bulkorder,jdbcType=INTEGER},
      </if>
      <if test="record.bulkdiscount != null" >
        bulkDiscount = #{record.bulkdiscount,jdbcType=INTEGER},
      </if>
      <if test="record.stock != null" >
        stock = #{record.stock,jdbcType=INTEGER},
      </if>
      <if test="record.wsvalidnum != null" >
        wsValidNum = #{record.wsvalidnum,jdbcType=INTEGER},
      </if>
      <if test="record.labels != null" >
        labels = #{record.labels,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryIds != null" >
        category_ids = #{record.categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="record.timingInterval != null" >
        timing_interval = #{record.timingInterval,jdbcType=INTEGER},
      </if>
      <if test="record.timingMinute != null" >
        timing_minute = #{record.timingMinute,jdbcType=INTEGER},
      </if>
      <if test="record.timingHour != null" >
        timing_hour = #{record.timingHour,jdbcType=INTEGER},
      </if>
      <if test="record.titleJson != null" >
        title_json = #{record.titleJson,jdbcType=VARCHAR},
      </if>
      <if test="record.logoText != null" >
        logo_text = #{record.logoText,jdbcType=VARCHAR},
      </if>
      <if test="record.logoPosition != null" >
        logo_position = #{record.logoPosition,jdbcType=VARCHAR},
      </if>
      <if test="record.fontSize != null" >
        font_size = #{record.fontSize,jdbcType=VARCHAR},
      </if>
      <if test="record.logoColor != null" >
        logo_color = #{record.logoColor,jdbcType=VARCHAR},
      </if>
      <if test="record.maxPublishNum != null" >
        max_publish_num = #{record.maxPublishNum,jdbcType=INTEGER},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shopId != null" >
        shop_id = #{record.shopId,jdbcType=VARCHAR},
      </if>
      <if test="record.openDate != null" >
        openDate = #{record.openDate,jdbcType=VARCHAR},
      </if>
      <if test="record.cny != null" >
        cny = #{record.cny,jdbcType=BIT},
      </if>
      <if test="record.autoGroundingNew != null" >
        auto_grounding_new = #{record.autoGroundingNew,jdbcType=BIT},
      </if>
      <if test="record.autoUpdateWeight != null" >
        auto_update_weight = #{record.autoUpdateWeight,jdbcType=BIT},
      </if>
      <if test="record.autoSupplyStock != null" >
        auto_supply_stock = #{record.autoSupplyStock,jdbcType=BIT},
      </if>
      <if test="record.autoUpdateTitle != null" >
        auto_update_title = #{record.autoUpdateTitle,jdbcType=BIT},
      </if>
      <if test="record.autoUpdateDetail != null" >
        auto_update_detail = #{record.autoUpdateDetail,jdbcType=BIT},
      </if>
      <if test="record.autoUpdateSonimg != null" >
        auto_update_sonimg = #{record.autoUpdateSonimg,jdbcType=BIT},
      </if>
      <if test="record.autoRecommendNewProduct != null" >
        auto_recommend_new_product = #{record.autoRecommendNewProduct,jdbcType=BIT},
      </if>
      <if test="record.autoUpdateDeficitOrder != null" >
        auto_update_deficit_order = #{record.autoUpdateDeficitOrder,jdbcType=BIT},
      </if>
      <if test="record.autoDownForBadItem != null" >
        auto_down_for_bad_item = #{record.autoDownForBadItem,jdbcType=BIT},
      </if>
      <if test="record.packingType != null" >
        packing_type = #{record.packingType,jdbcType=INTEGER},
      </if>
      package_length = #{record.packageLength,jdbcType=INTEGER},
      package_width = #{record.packageWidth,jdbcType=INTEGER},
      package_height = #{record.packageHeight,jdbcType=INTEGER},
      <if test="record.addWeight != null" >
        add_weight = #{record.addWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.titleType != null" >
        title_type = #{record.titleType,jdbcType=INTEGER},
      </if>
      <if test="record.titleValue != null" >
        title_value = #{record.titleValue,jdbcType=VARCHAR},
      </if>
      from_weight = #{record.fromWeight,jdbcType=DOUBLE},
      to_weight = #{record.toWeight,jdbcType=DOUBLE},
      from_price = #{record.fromPrice,jdbcType=DOUBLE},
      to_price = #{record.toPrice,jdbcType=DOUBLE},
      <if test="record.specialTags != null" >
        special_tags = #{record.specialTags,jdbcType=VARCHAR},
      </if>
      item_count = #{record.itemCount,jdbcType=INTEGER},
      <if test="record.productDev != null" >
        product_dev = #{record.productDev,jdbcType=VARCHAR},
      </if>
      <if test="record.watermarkTemplateIdStr != null" >
        watermark_template_id_str = #{record.watermarkTemplateIdStr,jdbcType=VARCHAR},
      </if>
      <if test="record.mainBrand != null" >
        main_brand = #{record.mainBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.autoCalcPrice != null" >
        auto_calc_price = #{record.autoCalcPrice,jdbcType=BIT},
      </if>
      <if test="record.monthSaleTarget != null" >
        month_sale_target = #{record.monthSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="record.yearSaleTarget != null" >
        year_sale_target = #{record.yearSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="record.monthAddListingTarget != null" >
        month_add_listing_target = #{record.monthAddListingTarget,jdbcType=INTEGER},
      </if>
      <if test="record.positiveRate != null" >
        positive_rate = #{record.positiveRate,jdbcType=DOUBLE},
      </if>
      <if test="record.detailedSellerRatings != null" >
        detailed_seller_ratings = #{record.detailedSellerRatings,jdbcType=VARCHAR},
      </if>
      <if test="record.feedbackInfo != null" >
        feedback_info = #{record.feedbackInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.autoAddEprFee != null" >
        auto_add_epr_fee = #{record.autoAddEprFee,jdbcType=BIT},
      </if>
      <if test="record.addEprPackFee != null" >
        add_epr_pack_fee = #{record.addEprPackFee,jdbcType=BIT},
      </if>
      <if test="record.autoRetireTort != null" >
        auto_retire_tort = #{record.autoRetireTort,jdbcType=BIT},
      </if>
      <if test="record.tortType != null" >
        tort_type = #{record.tortType,jdbcType=VARCHAR},
      </if>
      <if test="record.autoRemoveTortWord != null" >
        auto_remove_tort_word = #{record.autoRemoveTortWord,jdbcType=BIT},
      </if>
      <if test="record.tgProfitRate != null">
        tg_profit_rate = #{record.tgProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="record.halfTgProfitRate != null">
        half_tg_profit_rate = #{record.halfTgProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="record.autoUpdateQualifications != null">
        auto_update_qualifications = #{record.autoUpdateQualifications,jdbcType=BIT},
      </if>
      <if test="record.autoHalfReservation != null">
        auto_half_reservation = #{record.autoHalfReservation,jdbcType=BIT},
      </if>
      <if test="record.halfShippingCost != null">
        half_shipping_cost = #{record.halfShippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.halfOperateCost != null">
        half_operate_cost = #{record.halfOperateCost,jdbcType=DOUBLE},
      </if>
      <if test="record.joinCountrys != null">
        join_countrys = #{record.joinCountrys,jdbcType=VARCHAR},
      </if>
      <if test="record.publishJson != null">
        publish_json = #{record.publishJson,jdbcType=VARCHAR},
      </if>
      <if test="record.autoGroupId != null">
        auto_group_id = #{record.autoGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.limitStoreProductNumber != null">
        limit_store_product_number = #{record.limitStoreProductNumber,jdbcType=INTEGER},
      </if>
      <if test="record.delivery30dRate != null" >
        delivery_30d_rate = #{record.delivery30dRate,jdbcType=DOUBLE},
      </if>
      <if test="record.delivery30dRateUpdateTime != null" >
        delivery_30d_rate_update_time = #{record.delivery30dRateUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.halfForbidSaleCountry != null" >
        half_forbid_sale_country = #{record.halfForbidSaleCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.productCurrentMonthLimitNum != null">
        product_current_month_limit_num = #{record.productCurrentMonthLimitNum,jdbcType=INTEGER},
      </if>
      <if test="record.productTotalLimitNum != null">
        product_total_limit_num = #{record.productTotalLimitNum,jdbcType=INTEGER},
      </if>
      <if test="record.productTotalAuditNum != null">
        product_total_audit_num = #{record.productTotalAuditNum,jdbcType=INTEGER},
      </if>
      <if test="record.productCurrentMonthPubNum != null">
        product_current_month_pub_num = #{record.productCurrentMonthPubNum,jdbcType=INTEGER},
      </if>
      <if test="record.productTotalOnlineNum != null">
        product_total_online_num = #{record.productTotalOnlineNum,jdbcType=INTEGER},
      </if>
      <if test="record.autoUpdateAttr != null">
        auto_update_attr = #{record.autoUpdateAttr,jdbcType=BIT},
      </if>
      <if test="record.taxType != null">
        tax_type = #{record.taxType,jdbcType=VARCHAR},
      </if>

    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateDelivery30dRate" parameterType="com.estone.erp.publish.smt.model.AliexpressConfig" >
    update aliexpress_config
    <set >
      delivery_30d_rate = #{delivery30dRate,jdbcType=DOUBLE},
      <if test="delivery30dRateUpdateTime != null" >
        delivery_30d_rate_update_time = #{delivery30dRateUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressConfig" >
    update aliexpress_config
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="usable != null" >
        usable = #{usable,jdbcType=BIT},
      </if>
      <if test="deliverytime != null" >
        deliveryTime = #{deliverytime,jdbcType=INTEGER},
      </if>
      <if test="wholesale != null" >
        wholesale = #{wholesale,jdbcType=BIT},
      </if>
      <if test="bulkorder != null" >
        bulkOrder = #{bulkorder,jdbcType=INTEGER},
      </if>
      <if test="bulkdiscount != null" >
        bulkDiscount = #{bulkdiscount,jdbcType=INTEGER},
      </if>
      <if test="stock != null" >
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="wsvalidnum != null" >
        wsValidNum = #{wsvalidnum,jdbcType=INTEGER},
      </if>
      <if test="labels != null" >
        labels = #{labels,jdbcType=VARCHAR},
      </if>

      <if test="categoryIds != null" >
        category_ids = #{categoryIds,jdbcType=VARCHAR},
      </if>
      <if test="timingInterval != null" >
        timing_interval = #{timingInterval,jdbcType=INTEGER},
      </if>
      <if test="timingMinute != null" >
        timing_minute = #{timingMinute,jdbcType=INTEGER},
      </if>
      <if test="timingHour != null" >
        timing_hour = #{timingHour,jdbcType=INTEGER},
      </if>
      <if test="titleJson != null" >
        title_json = #{titleJson,jdbcType=VARCHAR},
      </if>
      <if test="logoText != null" >
        logo_text = #{logoText,jdbcType=VARCHAR},
      </if>
      <if test="logoPosition != null" >
        logo_position = #{logoPosition,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null" >
        font_size = #{fontSize,jdbcType=VARCHAR},
      </if>
      <if test="logoColor != null" >
        logo_color = #{logoColor,jdbcType=VARCHAR},
      </if>
      <if test="maxPublishNum != null" >
        max_publish_num = #{maxPublishNum,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null" >
        open_date = #{openDate,jdbcType=VARCHAR},
      </if>
      <if test="cny != null" >
        cny = #{cny,jdbcType=BIT},
      </if>
      <if test="autoGroundingNew != null" >
        auto_grounding_new = #{autoGroundingNew,jdbcType=BIT},
      </if>
      <if test="autoUpdateWeight != null" >
        auto_update_weight = #{autoUpdateWeight,jdbcType=BIT},
      </if>
      <if test="autoSupplyStock != null" >
        auto_supply_stock = #{autoSupplyStock,jdbcType=BIT},
      </if>
      <if test="autoUpdateTitle != null" >
        auto_update_title = #{autoUpdateTitle,jdbcType=BIT},
      </if>
      <if test="autoUpdateDetail != null" >
        auto_update_detail = #{autoUpdateDetail,jdbcType=BIT},
      </if>
      <if test="autoUpdateSonimg != null" >
        auto_update_sonimg = #{autoUpdateSonimg,jdbcType=BIT},
      </if>
      <if test="autoRecommendNewProduct != null" >
        auto_recommend_new_product = #{autoRecommendNewProduct,jdbcType=BIT},
      </if>
      <if test="autoUpdateDeficitOrder != null" >
        auto_update_deficit_order = #{autoUpdateDeficitOrder,jdbcType=BIT},
      </if>
      <if test="autoDownForBadItem != null" >
        auto_down_for_bad_item = #{autoDownForBadItem,jdbcType=BIT},
      </if>
      <if test="packingType != null" >
        packing_type = #{packingType,jdbcType=INTEGER},
      </if>
      package_length = #{packageLength,jdbcType=INTEGER},
      package_width = #{packageWidth,jdbcType=INTEGER},
      package_height = #{packageHeight,jdbcType=INTEGER},
      <if test="addWeight != null" >
        add_weight = #{addWeight,jdbcType=DOUBLE},
      </if>
      <if test="titleType != null" >
        title_type = #{titleType,jdbcType=INTEGER},
      </if>
      <if test="titleValue != null" >
        title_value = #{titleValue,jdbcType=VARCHAR},
      </if>
      <if test="rootCategory != null" >
        root_category = #{rootCategory,jdbcType=VARCHAR},
      </if>
      from_weight = #{fromWeight,jdbcType=DOUBLE},
      to_weight = #{toWeight,jdbcType=DOUBLE},
      from_price = #{fromPrice,jdbcType=DOUBLE},
      to_price = #{toPrice,jdbcType=DOUBLE},
      <if test="specialTags != null" >
        special_tags = #{specialTags,jdbcType=VARCHAR},
      </if>
      item_count = #{itemCount,jdbcType=INTEGER},
      <if test="productDev != null" >
        product_dev = #{productDev,jdbcType=VARCHAR},
      </if>
      <if test="watermarkTemplateIdStr != null" >
        watermark_template_id_str = #{watermarkTemplateIdStr,jdbcType=VARCHAR},
      </if>
      <if test="mainBrand != null" >
        main_brand = #{mainBrand,jdbcType=VARCHAR},
      </if>
      <if test="autoCalcPrice != null" >
        auto_calc_price = #{autoCalcPrice,jdbcType=BIT},
      </if>
      <if test="monthSaleTarget != null" >
        month_sale_target = #{monthSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="yearSaleTarget != null" >
        year_sale_target = #{yearSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="monthAddListingTarget != null" >
        month_add_listing_target = #{monthAddListingTarget,jdbcType=INTEGER},
      </if>
      <if test="positiveRate != null" >
        positive_rate = #{positiveRate,jdbcType=DOUBLE},
      </if>
      <if test="detailedSellerRatings != null" >
        detailed_seller_ratings = #{detailedSellerRatings,jdbcType=VARCHAR},
      </if>
      <if test="feedbackInfo != null" >
        feedback_info = #{feedbackInfo,jdbcType=VARCHAR},
      </if>
      <if test="autoAddEprFee != null" >
        auto_add_epr_fee = #{autoAddEprFee,jdbcType=BIT},
      </if>
      <if test="addEprPackFee != null" >
        add_epr_pack_fee = #{addEprPackFee,jdbcType=BIT},
      </if>
      <if test="autoRetireTort != null" >
        auto_retire_tort = #{autoRetireTort,jdbcType=BIT},
      </if>
      <if test="tortType != null" >
        tort_type = #{tortType,jdbcType=VARCHAR},
      </if>
      <if test="autoRemoveTortWord != null" >
        auto_remove_tort_word = #{autoRemoveTortWord,jdbcType=BIT},
      </if>
      <if test="autoUpdateQualifications != null">
        auto_update_qualifications = #{autoUpdateQualifications,jdbcType=BIT},
      </if>
      <if test="autoHalfReservation != null">
        auto_half_reservation = #{autoHalfReservation,jdbcType=BIT},
      </if>
      <if test="halfShippingCost != null">
        half_shipping_cost = #{halfShippingCost,jdbcType=DOUBLE},
      </if>
      <if test="halfOperateCost != null">
        half_operate_cost = #{halfOperateCost,jdbcType=DOUBLE},
      </if>
      <if test="joinCountrys != null">
        join_countrys = #{joinCountrys,jdbcType=VARCHAR},
      </if>
      <if test="publishJson != null">
        publish_json = #{publishJson,jdbcType=VARCHAR},
      </if>
      <if test="autoGroupId != null">
        auto_group_id = #{autoGroupId,jdbcType=VARCHAR},
      </if>
      <if test="limitStoreProductNumber != null">
        limit_store_product_number = #{limitStoreProductNumber,jdbcType=INTEGER},
      </if>
      <if test="delivery30dRate != null" >
        delivery_30d_rate = #{delivery30dRate,jdbcType=DOUBLE},
      </if>
      <if test="delivery30dRateUpdateTime != null" >
        delivery_30d_rate_update_time = #{delivery30dRateUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="halfForbidSaleCountry != null" >
        half_forbid_sale_country = #{halfForbidSaleCountry,jdbcType=VARCHAR},
      </if>
      <!-- 根据id跟新，tgProfitRate没有值也要设置为null -->
        tg_profit_rate = #{tgProfitRate,jdbcType=DOUBLE},
        half_tg_profit_rate = #{halfTgProfitRate,jdbcType=DOUBLE},
      <if test="productCurrentMonthLimitNum != null">
        product_current_month_limit_num = #{productCurrentMonthLimitNum,jdbcType=INTEGER},
      </if>
      <if test="productTotalLimitNum != null">
        product_total_limit_num = #{productTotalLimitNum,jdbcType=INTEGER},
      </if>
      <if test="productTotalAuditNum != null">
        product_total_audit_num = #{productTotalAuditNum,jdbcType=INTEGER},
      </if>
      <if test="productCurrentMonthPubNum != null">
        product_current_month_pub_num = #{productCurrentMonthPubNum,jdbcType=INTEGER},
      </if>
      <if test="productTotalOnlineNum != null">
        product_total_online_num = #{productTotalOnlineNum,jdbcType=INTEGER},
      </if>
      <if test="autoUpdateAttr != null">
        auto_update_attr = #{autoUpdateAttr,jdbcType=BIT},
      </if>
      <if test="taxType != null">
        tax_type = #{taxType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateConfigById" parameterType="com.estone.erp.publish.smt.model.AliexpressConfig" >
    update aliexpress_config
    <set >
      <if test="monthSaleTarget != null" >
        month_sale_target = #{monthSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="yearSaleTarget != null" >
        year_sale_target = #{yearSaleTarget,jdbcType=DOUBLE},
      </if>
      <if test="monthAddListingTarget != null" >
        month_add_listing_target = #{monthAddListingTarget,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="sumTarget" resultType="com.estone.erp.publish.common.model.dto.ShopMonthTotalDataDO">
    select  sum(month_sale_target) salesTotal,  SUM(month_add_listing_target) addListingTotal
    from aliexpress_config
    <where>
      <if test="accountNumbers != null" >
        account in
        <foreach collection="accountNumbers" item="accountNumber" open="(" close=")" separator="," >
          #{accountNumber}
        </foreach>
      </if>
    </where>
  </select>
</mapper>