<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.SmtCategoryConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.SmtCategoryConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_full_path_code" property="categoryFullPathCode" jdbcType="VARCHAR" />
    <result column="category_cn_full_name" property="categoryCnFullName" jdbcType="VARCHAR" />
    <result column="attribute_show_type_value" property="attributeShowTypeValue" jdbcType="VARCHAR" />
    <result column="attr_name_id" property="attrNameId" jdbcType="BIGINT" />
    <result column="attr_name" property="attrName" jdbcType="VARCHAR" />
    <result column="attr_name_cn_name" property="attrNameCnName" jdbcType="VARCHAR" />
    <result column="attr_value_cn_names" property="attrValueCnNames" jdbcType="VARCHAR" />
    <result column="attr_json" property="attrJson" jdbcType="VARCHAR" />
    <result column="children_attr_name_id" property="childrenAttrNameId" jdbcType="BIGINT" />
    <result column="children_attr_name" property="childrenAttrName" jdbcType="VARCHAR" />
    <result column="children_attr_name_cn_name" property="childrenAttrNameCnName" jdbcType="VARCHAR" />
    <result column="children_attr_json" property="childrenAttrJson" jdbcType="VARCHAR" />
    <result column="enable" property="enable" jdbcType="BIT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="started_last_time" property="startedLastTime" jdbcType="TIMESTAMP" />
    <result column="disabled_last_time" property="disabledLastTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, category_id, category_full_path_code, category_cn_full_name, attribute_show_type_value, 
    attr_name_id, attr_name, attr_name_cn_name, attr_value_cn_names, attr_json, children_attr_name_id, 
    children_attr_name, children_attr_name_cn_name, children_attr_json, `enable`, created_by, 
    created_time, updated_by, updated_time, started_last_time, disabled_last_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.SmtCategoryConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_category_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from smt_category_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_category_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.SmtCategoryConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_category_config (category_id, category_full_path_code, 
      category_cn_full_name, attribute_show_type_value, 
      attr_name_id, attr_name, attr_name_cn_name, 
      attr_value_cn_names, attr_json, children_attr_name_id, 
      children_attr_name, children_attr_name_cn_name, 
      children_attr_json, `enable`, created_by, 
      created_time, updated_by, updated_time, 
      started_last_time, disabled_last_time)
    values (#{categoryId,jdbcType=INTEGER}, #{categoryFullPathCode,jdbcType=VARCHAR}, 
      #{categoryCnFullName,jdbcType=VARCHAR}, #{attributeShowTypeValue,jdbcType=VARCHAR}, 
      #{attrNameId,jdbcType=BIGINT}, #{attrName,jdbcType=VARCHAR}, #{attrNameCnName,jdbcType=VARCHAR}, 
      #{attrValueCnNames,jdbcType=VARCHAR}, #{attrJson,jdbcType=VARCHAR}, #{childrenAttrNameId,jdbcType=BIGINT}, 
      #{childrenAttrName,jdbcType=VARCHAR}, #{childrenAttrNameCnName,jdbcType=VARCHAR}, 
      #{childrenAttrJson,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP}, 
      #{startedLastTime,jdbcType=TIMESTAMP}, #{disabledLastTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.SmtCategoryConfigExample" resultType="java.lang.Integer" >
    select count(*) from smt_category_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_category_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryFullPathCode != null" >
        category_full_path_code = #{record.categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCnFullName != null" >
        category_cn_full_name = #{record.categoryCnFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeShowTypeValue != null" >
        attribute_show_type_value = #{record.attributeShowTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.attrNameId != null" >
        attr_name_id = #{record.attrNameId,jdbcType=BIGINT},
      </if>
      <if test="record.attrName != null" >
        attr_name = #{record.attrName,jdbcType=VARCHAR},
      </if>
      <if test="record.attrNameCnName != null" >
        attr_name_cn_name = #{record.attrNameCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.attrValueCnNames != null" >
        attr_value_cn_names = #{record.attrValueCnNames,jdbcType=VARCHAR},
      </if>
      <if test="record.attrJson != null" >
        attr_json = #{record.attrJson,jdbcType=VARCHAR},
      </if>
      <if test="record.childrenAttrNameId != null" >
        children_attr_name_id = #{record.childrenAttrNameId,jdbcType=BIGINT},
      </if>
      <if test="record.childrenAttrName != null" >
        children_attr_name = #{record.childrenAttrName,jdbcType=VARCHAR},
      </if>
      <if test="record.childrenAttrNameCnName != null" >
        children_attr_name_cn_name = #{record.childrenAttrNameCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.childrenAttrJson != null" >
        children_attr_json = #{record.childrenAttrJson,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null" >
        `enable` = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startedLastTime != null" >
        started_last_time = #{record.startedLastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.disabledLastTime != null" >
        disabled_last_time = #{record.disabledLastTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.SmtCategoryConfig" >
    update smt_category_config
    <set >
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryFullPathCode != null" >
        category_full_path_code = #{categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryCnFullName != null" >
        category_cn_full_name = #{categoryCnFullName,jdbcType=VARCHAR},
      </if>
      <if test="attributeShowTypeValue != null" >
        attribute_show_type_value = #{attributeShowTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="attrNameId != null" >
        attr_name_id = #{attrNameId,jdbcType=BIGINT},
      </if>
      <if test="attrName != null" >
        attr_name = #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrNameCnName != null" >
        attr_name_cn_name = #{attrNameCnName,jdbcType=VARCHAR},
      </if>
      <if test="attrValueCnNames != null" >
        attr_value_cn_names = #{attrValueCnNames,jdbcType=VARCHAR},
      </if>
      <if test="attrJson != null" >
        attr_json = #{attrJson,jdbcType=VARCHAR},
      </if>
      <if test="childrenAttrNameId != null" >
        children_attr_name_id = #{childrenAttrNameId,jdbcType=BIGINT},
      </if>
      <if test="childrenAttrName != null" >
        children_attr_name = #{childrenAttrName,jdbcType=VARCHAR},
      </if>
      <if test="childrenAttrNameCnName != null" >
        children_attr_name_cn_name = #{childrenAttrNameCnName,jdbcType=VARCHAR},
      </if>
      <if test="childrenAttrJson != null" >
        children_attr_json = #{childrenAttrJson,jdbcType=VARCHAR},
      </if>
      <if test="enable != null" >
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startedLastTime != null" >
        started_last_time = #{startedLastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="disabledLastTime != null" >
        disabled_last_time = #{disabledLastTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert into smt_category_config (category_id, category_full_path_code, category_cn_full_name, attribute_show_type_value,
    attr_name_id, attr_name, attr_name_cn_name,
    attr_value_cn_names, attr_json, children_attr_name_id,
    children_attr_name, children_attr_name_cn_name,
    children_attr_json, `enable`, created_by,
    created_time, updated_by, updated_time,
    started_last_time, disabled_last_time
    )
    values
    <foreach collection="list" item="record" separator=",">
     (#{record.categoryId,jdbcType=INTEGER}, #{record.categoryFullPathCode,jdbcType=VARCHAR},#{record.categoryCnFullName,jdbcType=VARCHAR}, #{record.attributeShowTypeValue,jdbcType=VARCHAR},
        #{record.attrNameId,jdbcType=BIGINT}, #{record.attrName,jdbcType=VARCHAR}, #{record.attrNameCnName,jdbcType=VARCHAR},
        #{record.attrValueCnNames,jdbcType=VARCHAR}, #{record.attrJson,jdbcType=VARCHAR}, #{record.childrenAttrNameId,jdbcType=BIGINT},
        #{record.childrenAttrName,jdbcType=VARCHAR}, #{record.childrenAttrNameCnName,jdbcType=VARCHAR},
        #{record.childrenAttrJson,jdbcType=VARCHAR}, #{record.enable,jdbcType=BIT}, #{record.createdBy,jdbcType=VARCHAR},
        #{record.createdTime,jdbcType=TIMESTAMP}, #{record.updatedBy,jdbcType=VARCHAR}, #{record.updatedTime,jdbcType=TIMESTAMP},
        #{record.startedLastTime,jdbcType=TIMESTAMP}, #{record.disabledLastTime,jdbcType=TIMESTAMP}
        )
    </foreach>
    </insert>

  <update id="updateAttrByPrimary">
    update smt_category_config
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryFullPathCode != null">
        category_full_path_code = #{categoryFullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryCnFullName != null">
        category_cn_full_name = #{categoryCnFullName,jdbcType=VARCHAR},
      </if>
        attribute_show_type_value = #{attributeShowTypeValue,jdbcType=VARCHAR},
        attr_name_id = #{attrNameId,jdbcType=BIGINT},
        attr_name = #{attrName,jdbcType=VARCHAR},
        attr_name_cn_name = #{attrNameCnName,jdbcType=VARCHAR},
        attr_value_cn_names = #{attrValueCnNames,jdbcType=VARCHAR},
        attr_json = #{attrJson,jdbcType=VARCHAR},
        children_attr_name_id = #{childrenAttrNameId,jdbcType=BIGINT},
        children_attr_name = #{childrenAttrName,jdbcType=VARCHAR},
        children_attr_name_cn_name = #{childrenAttrNameCnName,jdbcType=VARCHAR},
        children_attr_json = #{childrenAttrJson,jdbcType=VARCHAR},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateEnable">
    <foreach collection="list" item="record" separator=";">
      update smt_category_config
      set `enable` = #{record.enable,jdbcType=BIT},
          <if test="record.disabledLastTime != null">
            disabled_last_time = #{record.disabledLastTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.startedLastTime != null">
            started_last_time = #{record.startedLastTime,jdbcType=TIMESTAMP},
          </if>
          updated_by = #{record.updatedBy,jdbcType=VARCHAR},
          updated_time = #{record.updatedTime,jdbcType=TIMESTAMP}
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>