<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.SmtTidbDataMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.SmtTidbData" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
    <result column="country_code" property="countryCode" jdbcType="VARCHAR" />
    <result column="is_area" property="isArea" jdbcType="BIT" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="template_id" property="templateId" jdbcType="BIGINT" />
    <result column="upload_state" property="uploadState" jdbcType="INTEGER" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, product_id, sku, sku_id, country_code, is_area, price, template_id, 
    upload_state, created_time, updated_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.SmtTidbDataExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from smt_tidb_data
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from smt_tidb_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_tidb_data
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.SmtTidbData" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_tidb_data (account, product_id, sku, 
      sku_id, country_code, is_area, 
      price, template_id, upload_state, 
      created_time, updated_time)
    values (#{account,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, 
      #{skuId,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, #{isArea,jdbcType=BIT}, 
      #{price,jdbcType=DOUBLE}, #{templateId,jdbcType=BIGINT}, #{uploadState,jdbcType=INTEGER}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.SmtTidbDataExample" resultType="java.lang.Integer" >
    select count(*) from smt_tidb_data
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_tidb_data
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null" >
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.countryCode != null" >
        country_code = #{record.countryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isArea != null" >
        is_area = #{record.isArea,jdbcType=BIT},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.uploadState != null" >
        upload_state = #{record.uploadState,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.SmtTidbData" >
    update smt_tidb_data
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null" >
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="isArea != null" >
        is_area = #{isArea,jdbcType=BIT},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="uploadState != null" >
        upload_state = #{uploadState,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>