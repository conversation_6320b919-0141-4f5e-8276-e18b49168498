<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressCategory" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_level" property="categoryLevel" jdbcType="INTEGER" />
    <result column="leaf_category" property="leafCategory" jdbcType="BIT" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="category_zh_name" property="categoryZhName" jdbcType="VARCHAR" />
    <result column="category_en_name" property="categoryEnName" jdbcType="VARCHAR" />
    <result column="category_pt_name" property="categoryPtName" jdbcType="VARCHAR" />
    <result column="category_fr_name" property="categoryFrName" jdbcType="VARCHAR" />
    <result column="category_ru_name" property="categoryRuName" jdbcType="VARCHAR" />
    <result column="category_in_name" property="categoryInName" jdbcType="VARCHAR" />
    <result column="category_es_name" property="categoryEsName" jdbcType="VARCHAR" />
    <result column="child_attributes_json" property="childAttributesJson" jdbcType="VARCHAR" />
    <result column="full_path_code" property="fullPathCode" jdbcType="VARCHAR" />
    <result column="car_type" property="carType" jdbcType="BIT" />
    <result column="origin" property="origin" jdbcType="BIT" />
    <result column="season" property="season" jdbcType="BIT" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="province" property="province" jdbcType="BIT" />
    <result column="province_param2" property="provinceParam2" jdbcType="VARCHAR" />
    <result column="province_attributes" property="provinceAttributes" jdbcType="VARCHAR" />
    <result column="synch_date" property="synchDate" jdbcType="TIMESTAMP" />
    <result column="is_show" property="isShow" jdbcType="BIT" />
    <result column="full_cn_name" property="fullCnName" jdbcType="VARCHAR" />
    <result column="is_qualification" property="isQualification" jdbcType="BIT" />
    <result column="chemistry" property="chemistry" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, category_id, category_level, leaf_category, parent_id, category_zh_name, category_en_name, 
    category_pt_name, category_fr_name, category_ru_name, category_in_name, category_es_name, 
    child_attributes_json, full_path_code, car_type, origin, season, last_update_date,
    province, province_param2, province_attributes, synch_date, is_show, full_cn_name, is_qualification, chemistry
  </sql>

  <sql id="Serach_Category_Base_Column_List" >
    id, category_id, category_level, leaf_category, parent_id, category_zh_name, category_en_name,
    category_pt_name, category_fr_name, category_ru_name, category_in_name, category_es_name, full_path_code,
    car_type, origin, season, last_update_date, province, province_param2, synch_date, is_show, full_cn_name, is_qualification, chemistry
  </sql>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from aliexpress_category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>


  <select id="searchCategeroyList" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Serach_Category_Base_Column_List" />
    from aliexpress_category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_category
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByCategoryId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_category
    where category_id = #{categoryId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_category
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressCategory" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_category (category_id, category_level, leaf_category, 
      parent_id, category_zh_name, category_en_name, 
      category_pt_name, category_fr_name, category_ru_name, 
      category_in_name, category_es_name, child_attributes_json, 
      full_path_code, car_type, origin, season, last_update_date,
      province, province_param2, province_attributes,
      synch_date, is_show, full_cn_name, is_qualification, chemistry)
    values (#{categoryId,jdbcType=INTEGER}, #{categoryLevel,jdbcType=INTEGER}, #{leafCategory,jdbcType=BIT}, 
      #{parentId,jdbcType=INTEGER}, #{categoryZhName,jdbcType=VARCHAR}, #{categoryEnName,jdbcType=VARCHAR}, 
      #{categoryPtName,jdbcType=VARCHAR}, #{categoryFrName,jdbcType=VARCHAR}, #{categoryRuName,jdbcType=VARCHAR}, 
      #{categoryInName,jdbcType=VARCHAR}, #{categoryEsName,jdbcType=VARCHAR}, #{childAttributesJson,jdbcType=VARCHAR}, 
      #{fullPathCode,jdbcType=VARCHAR}, #{carType,jdbcType=BIT}, #{origin,jdbcType=BIT}, #{season,jdbcType=BIT},
      #{lastUpdateDate,jdbcType=TIMESTAMP}, #{province,jdbcType=BIT}, #{provinceParam2,jdbcType=VARCHAR},
      #{provinceAttributes,jdbcType=VARCHAR}, #{synchDate,jdbcType=TIMESTAMP}, #{isShow,jdbcType=BIT},
      #{fullCnName,jdbcType=VARCHAR}, #{isQualification,jdbcType=BIT}, #{chemistry,jdbcType=BIT})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_category
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryLevel != null" >
        category_level = #{record.categoryLevel,jdbcType=INTEGER},
      </if>
      <if test="record.leafCategory != null" >
        leaf_category = #{record.leafCategory,jdbcType=BIT},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryZhName != null" >
        category_zh_name = #{record.categoryZhName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryEnName != null" >
        category_en_name = #{record.categoryEnName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryPtName != null" >
        category_pt_name = #{record.categoryPtName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryFrName != null" >
        category_fr_name = #{record.categoryFrName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryRuName != null" >
        category_ru_name = #{record.categoryRuName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryInName != null" >
        category_in_name = #{record.categoryInName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryEsName != null" >
        category_es_name = #{record.categoryEsName,jdbcType=VARCHAR},
      </if>
      <if test="record.childAttributesJson != null" >
        child_attributes_json = #{record.childAttributesJson,jdbcType=VARCHAR},
      </if>
      <if test="record.fullPathCode != null" >
        full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.carType != null" >
        car_type = #{record.carType,jdbcType=BIT},
      </if>
      <if test="record.origin != null" >
        origin = #{record.origin,jdbcType=BIT},
      </if>
      <if test="record.season != null" >
        season = #{record.season,jdbcType=BIT},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.province != null" >
        province = #{record.province,jdbcType=BIT},
      </if>
      <if test="record.provinceParam2 != null" >
        province_param2 = #{record.provinceParam2,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceAttributes != null" >
        province_attributes = #{record.provinceAttributes,jdbcType=VARCHAR},
      </if>
      <if test="record.synchDate != null" >
        synch_date = #{record.synchDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isShow != null" >
        is_show = #{record.isShow,jdbcType=BIT},
      </if>
      <if test="record.fullCnName != null" >
        full_cn_name = #{record.fullCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.isQualification != null" >
        is_qualification = #{record.isQualification,jdbcType=BIT},
      </if>
      <if test="record.chemistry != null" >
        chemistry = #{record.chemistry,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressCategory" >
    update aliexpress_category
    <set >
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryLevel != null" >
        category_level = #{categoryLevel,jdbcType=INTEGER},
      </if>
      <if test="leafCategory != null" >
        leaf_category = #{leafCategory,jdbcType=BIT},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryZhName != null" >
        category_zh_name = #{categoryZhName,jdbcType=VARCHAR},
      </if>
      <if test="categoryEnName != null" >
        category_en_name = #{categoryEnName,jdbcType=VARCHAR},
      </if>
      <if test="categoryPtName != null" >
        category_pt_name = #{categoryPtName,jdbcType=VARCHAR},
      </if>
      <if test="categoryFrName != null" >
        category_fr_name = #{categoryFrName,jdbcType=VARCHAR},
      </if>
      <if test="categoryRuName != null" >
        category_ru_name = #{categoryRuName,jdbcType=VARCHAR},
      </if>
      <if test="categoryInName != null" >
        category_in_name = #{categoryInName,jdbcType=VARCHAR},
      </if>
      <if test="categoryEsName != null" >
        category_es_name = #{categoryEsName,jdbcType=VARCHAR},
      </if>
      <if test="childAttributesJson != null" >
        child_attributes_json = #{childAttributesJson,jdbcType=VARCHAR},
      </if>
      <if test="fullPathCode != null" >
        full_path_code = #{fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="carType != null" >
        car_type = #{carType,jdbcType=BIT},
      </if>
      <if test="origin != null" >
        origin = #{origin,jdbcType=BIT},
      </if>
      <if test="season != null" >
        season = #{season,jdbcType=BIT},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=BIT},
      </if>
      <if test="provinceParam2 != null" >
        province_param2 = #{provinceParam2,jdbcType=VARCHAR},
      </if>
      <if test="provinceAttributes != null" >
        province_attributes = #{provinceAttributes,jdbcType=VARCHAR},
      </if>
      <if test="synchDate != null" >
        synch_date = #{synchDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isShow != null" >
        is_show = #{isShow,jdbcType=BIT},
      </if>
      <if test="fullCnName != null" >
        full_cn_name = #{fullCnName,jdbcType=VARCHAR},
      </if>
      <if test="isQualification != null">
        is_qualification = #{isQualification,jdbcType=BOOLEAN},
      </if>
      <if test="chemistry != null" >
        chemistry = #{chemistry,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_category (category_id, category_level, leaf_category,
    parent_id, category_zh_name, category_en_name,
    category_pt_name, category_fr_name, category_ru_name,
    category_in_name, category_es_name, child_attributes_json,
    full_path_code, car_type, origin, season, last_update_date,
    province, province_param2, province_attributes,
    synch_date, is_show, full_cn_name,
    is_qualification, chemistry)
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.categoryId,jdbcType=INTEGER}, #{item.categoryLevel,jdbcType=INTEGER}, #{item.leafCategory,jdbcType=BIT},
    #{item.parentId,jdbcType=INTEGER}, #{item.categoryZhName,jdbcType=VARCHAR}, #{item.categoryEnName,jdbcType=VARCHAR},
    #{item.categoryPtName,jdbcType=VARCHAR}, #{item.categoryFrName,jdbcType=VARCHAR}, #{item.categoryRuName,jdbcType=VARCHAR},
    #{item.categoryInName,jdbcType=VARCHAR}, #{item.categoryEsName,jdbcType=VARCHAR}, #{item.childAttributesJson,jdbcType=VARCHAR},
    #{item.fullPathCode,jdbcType=VARCHAR}, #{item.carType,jdbcType=BIT}, #{item.origin,jdbcType=BIT}, #{item.season,jdbcType=BIT},
    #{item.lastUpdateDate,jdbcType=TIMESTAMP},#{item.province,jdbcType=BIT}, #{item.provinceParam2,jdbcType=VARCHAR},
    #{item.provinceAttributes,jdbcType=VARCHAR}, #{synchDate,jdbcType=TIMESTAMP}, #{isShow,jdbcType=BIT}, #{item.fullCnName,jdbcType=VARCHAR}
    #{item.isQualification}, #{item.chemistry})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
	  update aliexpress_category
	   <set >
	     <if test="record.categoryId != null" >
	       category_id = #{record.categoryId,jdbcType=INTEGER},
	     </if>
	     <if test="record.categoryLevel != null" >
	       category_level = #{record.categoryLevel,jdbcType=INTEGER},
	     </if>
	     <if test="record.leafCategory != null" >
	       leaf_category = #{record.leafCategory,jdbcType=BIT},
	     </if>
	     <if test="record.categoryZhName != null" >
	       category_zh_name = #{record.categoryZhName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryEnName != null" >
	       category_en_name = #{record.categoryEnName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryPtName != null" >
	       category_pt_name = #{record.categoryPtName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryFrName != null" >
	       category_fr_name = #{record.categoryFrName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryRuName != null" >
	       category_ru_name = #{record.categoryRuName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryInName != null" >
	       category_in_name = #{record.categoryInName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.categoryEsName != null" >
	       category_es_name = #{record.categoryEsName,jdbcType=VARCHAR},
	     </if>
	     <if test="record.childAttributesJson != null" >
	       child_attributes_json = #{record.childAttributesJson,jdbcType=VARCHAR},
	     </if>
	     <if test="record.fullPathCode != null" >
	       full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
	     </if>
         <if test="record.carType != null" >
           car_type = #{record.carType,jdbcType=BIT},
         </if>
         <if test="record.origin != null" >
           origin = #{record.origin,jdbcType=BIT},
         </if>
         <if test="record.season != null" >
           season = #{record.season,jdbcType=BIT},
         </if>
         <if test="record.lastUpdateDate != null" >
           last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
         </if>
         <if test="record.province != null" >
           province = #{record.province,jdbcType=BIT},
         </if>
         <if test="record.provinceParam2 != null" >
           province_param2 = #{record.provinceParam2,jdbcType=VARCHAR},
         </if>
         <if test="record.provinceAttributes != null" >
           province_attributes = #{record.provinceAttributes,jdbcType=VARCHAR},
         </if>
         <if test="record.synchDate != null" >
           synch_date = #{record.synchDate,jdbcType=TIMESTAMP},
         </if>
         <if test="record.isShow != null" >
           is_show = #{record.isShow,jdbcType=BIT},
         </if>
         <if test="record.fullCnName != null" >
           full_cn_name = #{record.fullCnName,jdbcType=VARCHAR},
         </if>
         <if test="record.isQualification">
           is_qualification = #{record.isQualification,jdbcType=BIT},
         </if>
         <if test="record.chemistry">
           chemistry = #{record.chemistry,jdbcType=BIT},
         </if>
	   </set>
	   where id = #{record.id,jdbcType=INTEGER}
	 </foreach>
  </update>

  <select id="selectCategoryZhNameList" parameterType="java.util.List" resultMap="BaseResultMap">
      select category_id,category_zh_name
      from aliexpress_category
      WHERE 1=1
      <if test="list != null">
        and category_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </select>
</mapper>