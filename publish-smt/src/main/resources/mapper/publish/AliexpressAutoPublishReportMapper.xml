<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressAutoPublishReportMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressAutoPublishReport" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="image" property="image" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="sys_category_code" property="sysCategoryCode" jdbcType="VARCHAR" />
    <result column="sys_category_name" property="sysCategoryName" jdbcType="VARCHAR" />
    <result column="parent_template_ids" property="parentTemplateIds" jdbcType="VARCHAR" />
    <result column="relation_template_ids" property="relationTemplateIds" jdbcType="VARCHAR" />
    <result column="success_template_ids" property="successTemplateIds" jdbcType="VARCHAR" />
    <result column="parent_template_count" property="parentTemplateCount" jdbcType="INTEGER" />
    <result column="relation_count" property="relationCount" jdbcType="INTEGER" />
    <result column="success_count" property="successCount" jdbcType="INTEGER" />
    <result column="success_rate" property="successRate" jdbcType="DOUBLE" />
    <result column="order_count" property="orderCount" jdbcType="INTEGER" />
    <result column="out_order_rate" property="outOrderRate" jdbcType="DOUBLE" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="template_pull_time" property="templatePullTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, spu, image, title, sys_category_code, sys_category_name, parent_template_ids, 
    relation_template_ids, success_template_ids, parent_template_count, relation_count, success_count, success_rate,
    order_count, out_order_rate, create_time, template_pull_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressAutoPublishReportExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_auto_publish_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_auto_publish_report
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectBySpu" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from aliexpress_auto_publish_report
    where spu = #{spu,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_auto_publish_report
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressAutoPublishReport" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_auto_publish_report (spu, image, title, 
      sys_category_code, sys_category_name, parent_template_ids, 
      relation_template_ids, success_template_ids, parent_template_count,
      relation_count, success_count, success_rate, order_count, out_order_rate,
      create_time, template_pull_time)
    values (#{spu,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{sysCategoryCode,jdbcType=VARCHAR}, #{sysCategoryName,jdbcType=VARCHAR}, #{parentTemplateIds,jdbcType=VARCHAR}, 
      #{relationTemplateIds,jdbcType=VARCHAR}, #{successTemplateIds,jdbcType=VARCHAR}, #{parentTemplateCount,jdbcType=INTEGER},
      #{relationCount,jdbcType=INTEGER}, #{successCount,jdbcType=INTEGER}, #{successRate,jdbcType=DOUBLE}, #{orderCount,jdbcType=INTEGER}, #{outOrderRate,jdbcType=DOUBLE},
    #{createTime,jdbcType=TIMESTAMP}, #{templatePullTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressAutoPublishReportExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_auto_publish_report
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_auto_publish_report
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.image != null" >
        image = #{record.image,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.sysCategoryCode != null" >
        sys_category_code = #{record.sysCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sysCategoryName != null" >
        sys_category_name = #{record.sysCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentTemplateIds != null" >
        parent_template_ids = #{record.parentTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.relationTemplateIds != null" >
        relation_template_ids = #{record.relationTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.successTemplateIds != null" >
        success_template_ids = #{record.successTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.parentTemplateCount != null" >
        parent_template_count = #{record.parentTemplateCount,jdbcType=INTEGER},
      </if>
      <if test="record.relationCount != null" >
        relation_count = #{record.relationCount,jdbcType=INTEGER},
      </if>
      <if test="record.successCount != null" >
        success_count = #{record.successCount,jdbcType=INTEGER},
      </if>
      <if test="record.successRate != null" >
        success_rate = #{record.successRate,jdbcType=DOUBLE},
      </if>
      <if test="record.orderCount != null" >
        order_count = #{record.orderCount,jdbcType=INTEGER},
      </if>
      <if test="record.outOrderRate != null" >
        out_order_rate = #{record.outOrderRate,jdbcType=DOUBLE},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.templatePullTime != null" >
        template_pull_time = #{record.templatePullTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressAutoPublishReport" >
    update aliexpress_auto_publish_report
    <set >
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="image != null" >
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sysCategoryCode != null" >
        sys_category_code = #{sysCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="sysCategoryName != null" >
        sys_category_name = #{sysCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentTemplateIds != null" >
        parent_template_ids = #{parentTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="relationTemplateIds != null" >
        relation_template_ids = #{relationTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="successTemplateIds != null" >
        success_template_ids = #{successTemplateIds,jdbcType=VARCHAR},
      </if>
      <if test="parentTemplateCount != null" >
        parent_template_count = #{parentTemplateCount,jdbcType=INTEGER},
      </if>
      <if test="relationCount != null" >
        relation_count = #{relationCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null" >
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="successRate != null" >
        success_rate = #{successRate,jdbcType=DOUBLE},
      </if>
      <if test="orderCount != null" >
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="outOrderRate != null" >
        out_order_rate = #{outOrderRate,jdbcType=DOUBLE},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templatePullTime != null" >
        template_pull_time = #{templatePullTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_auto_publish_report (spu, image, title,
    sys_category_code, sys_category_name, parent_template_ids,
    relation_template_ids, success_template_ids, parent_template_count,
    relation_count, success_count, success_rate, order_count, out_order_rate,
    create_time, template_pull_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.spu,jdbcType=VARCHAR}, #{item.image,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR},
      #{item.sysCategoryCode,jdbcType=VARCHAR}, #{item.sysCategoryName,jdbcType=VARCHAR}, #{item.parentTemplateIds,jdbcType=VARCHAR},
      #{item.relationTemplateIds,jdbcType=VARCHAR}, #{item.successTemplateIds,jdbcType=VARCHAR}, #{item.parentTemplateCount,jdbcType=INTEGER},
      #{item.relationCount,jdbcType=INTEGER}, #{item.successCount,jdbcType=INTEGER}, #{item.successRate,jdbcType=DOUBLE}, #{item.orderCount,jdbcType=INTEGER}, #{item.outOrderRate,jdbcType=DOUBLE},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.templatePullTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="list" item="item" separator=";" close=";">
      update aliexpress_auto_publish_report
      <set >
        <if test="item.spu != null" >
          spu = #{item.spu,jdbcType=VARCHAR},
        </if>
        <if test="item.image != null" >
          image = #{item.image,jdbcType=VARCHAR},
        </if>
        <if test="item.title != null" >
          title = #{item.title,jdbcType=VARCHAR},
        </if>
        <if test="item.sysCategoryCode != null" >
          sys_category_code = #{item.sysCategoryCode,jdbcType=VARCHAR},
        </if>
        <if test="item.sysCategoryName != null" >
          sys_category_name = #{item.sysCategoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.parentTemplateIds != null" >
          parent_template_ids = #{item.parentTemplateIds,jdbcType=VARCHAR},
        </if>
        <if test="item.relationTemplateIds != null" >
          relation_template_ids = #{item.relationTemplateIds,jdbcType=VARCHAR},
        </if>
        <if test="item.successTemplateIds != null" >
          success_template_ids = #{item.successTemplateIds,jdbcType=VARCHAR},
        </if>
        <if test="item.parentTemplateCount != null" >
          parent_template_count = #{item.parentTemplateCount,jdbcType=INTEGER},
        </if>
        <if test="item.relationCount != null" >
          relation_count = #{item.relationCount,jdbcType=INTEGER},
        </if>
        <if test="item.successCount != null" >
          success_count = #{item.successCount,jdbcType=INTEGER},
        </if>
        <if test="item.successRate != null" >
          success_rate = #{item.successRate,jdbcType=DOUBLE},
        </if>
        <if test="item.orderCount != null" >
          order_count = #{item.orderCount,jdbcType=INTEGER},
        </if>
        <if test="item.outOrderRate != null" >
          out_order_rate = #{item.outOrderRate,jdbcType=DOUBLE},
        </if>
        <if test="item.createTime != null" >
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.templatePullTime != null" >
          template_pull_time = #{item.templatePullTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>