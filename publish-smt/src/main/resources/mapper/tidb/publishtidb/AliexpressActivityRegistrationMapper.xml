<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressActivityRegistrationMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="activity_type" property="activityType" jdbcType="INTEGER" />
    <result column="activity_name" property="activityName" jdbcType="VARCHAR" />
    <result column="activity_id" property="activityId" jdbcType="VARCHAR" />
    <result column="activity_description" property="activityDescription" jdbcType="VARCHAR" />
    <result column="activity_countries" property="activityCountries" jdbcType="VARCHAR" />
    <result column="recruitment_start_time" property="recruitmentStartTime" jdbcType="TIMESTAMP" />
    <result column="recruitment_end_time" property="recruitmentEndTime" jdbcType="TIMESTAMP" />
    <result column="display_start_time" property="displayStartTime" jdbcType="TIMESTAMP" />
    <result column="display_end_time" property="displayEndTime" jdbcType="TIMESTAMP" />
    <result column="registration_status" property="registrationStatus" jdbcType="INTEGER" />
    <result column="registration_template" property="registrationTemplate" jdbcType="VARCHAR" />
    <result column="registration_url" property="registrationUrl" jdbcType="VARCHAR" />
    <result column="generated_file" property="generatedFile" jdbcType="VARCHAR" />
    <result column="generation_failure_reason" property="generationFailureReason" jdbcType="VARCHAR" />
    <result column="confirmation_status" property="confirmationStatus" jdbcType="INTEGER" />
    <result column="upload_status" property="uploadStatus" jdbcType="INTEGER" />
    <result column="upload_status_modified_by" property="uploadStatusModifiedBy" jdbcType="VARCHAR"/>
    <result column="upload_status_modified_at" property="uploadStatusModifiedAt" jdbcType="TIMESTAMP"/>
    <result column="failure_remark" property="failureRemark" jdbcType="VARCHAR" />
    <result column="collection_time" property="collectionTime" jdbcType="TIMESTAMP" />
    <result column="generation_time" property="generationTime" jdbcType="TIMESTAMP" />
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
    <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP" />
    <result column="pro_count" property="proCount" jdbcType="INTEGER" />
    <result column="submit_pro_count" property="submitProCount" jdbcType="INTEGER" />
    <result column="registration_template_file_size" property="registrationTemplateFileSize" jdbcType="DOUBLE" />
    <result column="generated_file_size" property="generatedFileSize" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, activity_type, activity_name, activity_id, activity_description, 
    activity_countries, recruitment_start_time, recruitment_end_time, display_start_time,
    display_end_time,
    registration_status,
    registration_template,
    registration_url,
    generated_file,
    generation_failure_reason,
    confirmation_status,
    upload_status,
    upload_status_modified_by,
    upload_status_modified_at,
    failure_remark,
    collection_time,
    generation_time, upload_time, created_at, updated_at,submit_pro_count,pro_count,registration_template_file_size,generated_file_size
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_activity_registration
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectSumFileSizeByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample">
    select SUM(registration_template_file_size) registration_template_file_size,SUM(generated_file_size) generated_file_size
    from aliexpress_activity_registration
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_activity_registration
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_activity_registration
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_activity_registration (account_number, activity_type, activity_name, 
      activity_id, activity_description, activity_countries, 
      recruitment_start_time, recruitment_end_time, 
      display_start_time, display_end_time, registration_status, 
      registration_template, registration_url, 
      generated_file, generation_failure_reason, 
      confirmation_status, upload_status, failure_remark, 
      collection_time, generation_time, upload_time, 
      created_at, updated_at)
    values (#{accountNumber,jdbcType=VARCHAR}, #{activityType,jdbcType=INTEGER}, #{activityName,jdbcType=VARCHAR}, 
      #{activityId,jdbcType=VARCHAR}, #{activityDescription,jdbcType=VARCHAR}, #{activityCountries,jdbcType=VARCHAR}, 
      #{recruitmentStartTime,jdbcType=TIMESTAMP}, #{recruitmentEndTime,jdbcType=TIMESTAMP}, 
      #{displayStartTime,jdbcType=TIMESTAMP}, #{displayEndTime,jdbcType=TIMESTAMP}, #{registrationStatus,jdbcType=INTEGER}, 
      #{registrationTemplate,jdbcType=VARCHAR}, #{registrationUrl,jdbcType=VARCHAR}, 
      #{generatedFile,jdbcType=VARCHAR}, #{generationFailureReason,jdbcType=VARCHAR}, 
      #{confirmationStatus,jdbcType=INTEGER}, #{uploadStatus,jdbcType=INTEGER}, #{failureRemark,jdbcType=VARCHAR}, 
      #{collectionTime,jdbcType=TIMESTAMP}, #{generationTime,jdbcType=TIMESTAMP}, #{uploadTime,jdbcType=TIMESTAMP}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationExample"
          resultType="java.lang.Integer">
    select count(*) from aliexpress_activity_registration
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_activity_registration
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=INTEGER},
      </if>
      <if test="record.activityName != null" >
        activity_name = #{record.activityName,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDescription != null" >
        activity_description = #{record.activityDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.activityCountries != null" >
        activity_countries = #{record.activityCountries,jdbcType=VARCHAR},
      </if>
      <if test="record.recruitmentStartTime != null" >
        recruitment_start_time = #{record.recruitmentStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recruitmentEndTime != null" >
        recruitment_end_time = #{record.recruitmentEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.displayStartTime != null" >
        display_start_time = #{record.displayStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.displayEndTime != null" >
        display_end_time = #{record.displayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registrationStatus != null" >
        registration_status = #{record.registrationStatus,jdbcType=INTEGER},
      </if>
      <if test="record.registrationTemplate != null" >
        registration_template = #{record.registrationTemplate,jdbcType=VARCHAR},
      </if>
      <if test="record.registrationUrl != null" >
        registration_url = #{record.registrationUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.generatedFile != null" >
        generated_file = #{record.generatedFile,jdbcType=VARCHAR},
      </if>
      <if test="record.generationFailureReason != null" >
        generation_failure_reason = #{record.generationFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmationStatus != null" >
        confirmation_status = #{record.confirmationStatus,jdbcType=INTEGER},
      </if>
      <if test="record.uploadStatus != null" >
        upload_status = #{record.uploadStatus,jdbcType=INTEGER},
      </if>
      <if test="record.uploadStatusModifiedBy != null">
        upload_status_modified_by = #{record.uploadStatusModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadStatusModifiedAt != null">
        upload_status_modified_at = #{record.uploadStatusModifiedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.failureRemark != null" >
        failure_remark = #{record.failureRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.collectionTime != null" >
        collection_time = #{record.collectionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.generationTime != null" >
        generation_time = #{record.generationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.uploadTime != null" >
        upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdAt != null" >
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null" >
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registrationTemplateFileSize != null" >
        registration_template_file_size = #{record.registrationTemplateFileSize,jdbcType=DOUBLE},
      </if>
      <if test="record.generatedFileSize != null" >
        generated_file_size = #{record.generatedFileSize,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistration">
    update aliexpress_activity_registration
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=INTEGER},
      </if>
      <if test="activityName != null" >
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityDescription != null" >
        activity_description = #{activityDescription,jdbcType=VARCHAR},
      </if>
      <if test="activityCountries != null" >
        activity_countries = #{activityCountries,jdbcType=VARCHAR},
      </if>
      <if test="recruitmentStartTime != null" >
        recruitment_start_time = #{recruitmentStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recruitmentEndTime != null" >
        recruitment_end_time = #{recruitmentEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="displayStartTime != null" >
        display_start_time = #{displayStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="displayEndTime != null" >
        display_end_time = #{displayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registrationStatus != null" >
        registration_status = #{registrationStatus,jdbcType=INTEGER},
      </if>
      <if test="registrationTemplate != null" >
        registration_template = #{registrationTemplate,jdbcType=VARCHAR},
      </if>
      <if test="registrationUrl != null" >
        registration_url = #{registrationUrl,jdbcType=VARCHAR},
      </if>
      <if test="generatedFile != null" >
        generated_file = #{generatedFile,jdbcType=VARCHAR},
      </if>
      <if test="generationFailureReason != null" >
        generation_failure_reason = #{generationFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="confirmationStatus != null" >
        confirmation_status = #{confirmationStatus,jdbcType=INTEGER},
      </if>
      <if test="uploadStatus != null" >
        upload_status = #{uploadStatus,jdbcType=INTEGER},
      </if>
      <if test="failureRemark != null" >
        failure_remark = #{failureRemark,jdbcType=VARCHAR},
      </if>
      <if test="collectionTime != null" >
        collection_time = #{collectionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="generationTime != null" >
        generation_time = #{generationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uploadTime != null" >
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null" >
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null" >
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="proCount != null" >
        pro_count = #{proCount},
      </if>
      <if test="submitProCount != null" >
        submit_pro_count = #{submitProCount},
      </if>
      <if test="uploadStatusModifiedBy != null">
        upload_status_modified_by = #{uploadStatusModifiedBy},
      </if>
      <if test="uploadStatusModifiedAt != null">
        upload_status_modified_at = #{uploadStatusModifiedAt},
      </if>
      <if test="registrationTemplateFileSize != null">
        registration_template_file_size = #{registrationTemplateFileSize},
      </if>
      <if test="generatedFileSize != null">
        generated_file_size = #{generatedFileSize},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>