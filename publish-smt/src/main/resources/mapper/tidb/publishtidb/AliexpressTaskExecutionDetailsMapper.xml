<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressTaskExecutionDetailsMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionDetails">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="plate" column="plate" jdbcType="INTEGER"/>
        <result property="configType" column="config_type" jdbcType="INTEGER"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="titleType" column="title_type" jdbcType="INTEGER"/>
        <result property="totalNum" column="total_num" jdbcType="INTEGER"/>
        <result property="successNum" column="success_num" jdbcType="INTEGER"/>
        <result property="failNum" column="fail_num" jdbcType="INTEGER"/>
        <result property="countTime" column="count_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,plate,config_type,
        rule_name,account,title_type,
        total_num,success_num,fail_num,
        count_time
    </sql>
    <select id="selectSaleList" resultType="java.lang.String">
        SELECT DISTINCT sale
        FROM aliexpress_task_execution_details
        WHERE sale IS NOT NULL
          AND sale != ''
    </select>
    <select id="selectTotalListBySale" resultType="com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalDto">
        WITH numbers AS (
        SELECT 0 AS num UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
        UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
        UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14
        UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19
        UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23 UNION ALL SELECT 24
        UNION ALL SELECT 25 UNION ALL SELECT 26 UNION ALL SELECT 27 UNION ALL SELECT 28 UNION ALL SELECT 29
        UNION ALL SELECT 30
        ),

        yesterday_fail_kv AS (
        SELECT
        t.title_type,
        JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))) AS reason,
        JSON_EXTRACT(t.failure_reasons_count_json,
        CONCAT('$."', JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))), '"')) AS count
        FROM aliexpress_task_execution_details t
        JOIN numbers n ON n.num &lt; JSON_LENGTH(JSON_KEYS(t.failure_reasons_count_json))
        WHERE (t.title_type IN (1, 2) AND DATE(t.count_time) = CURDATE() - INTERVAL 2 DAY)
        OR (t.title_type NOT IN (1, 2) AND DATE(t.count_time) = CURDATE() - INTERVAL 1 DAY)
        <include refid="common_selectTotalListBySale_where_conditions" />
        ),

        yesterday_fail_agg AS (
        SELECT title_type, reason, SUM(count) AS total_count
        FROM yesterday_fail_kv
        WHERE reason IS NOT NULL
        GROUP BY title_type, reason
        ),

        yesterday_fail_json AS (
        SELECT title_type, JSON_OBJECTAGG(reason, total_count) AS yesterdayFailClassify
        FROM yesterday_fail_agg
        GROUP BY title_type
        ),

        last_seven_fail_kv AS (
        SELECT
        t.title_type,
        JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))) AS reason,
        JSON_EXTRACT(t.failure_reasons_count_json,
        CONCAT('$."', JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))), '"')) AS count FROM aliexpress_task_execution_details t
        JOIN numbers n ON n.num &lt; JSON_LENGTH(JSON_KEYS(t.failure_reasons_count_json))
        WHERE (t.title_type IN (1, 2) AND DATE(t.count_time) >= CURDATE() - INTERVAL 9 DAY)
        OR (t.title_type NOT IN (1, 2) AND DATE(t.count_time) >= CURDATE() - INTERVAL 8 DAY)
        <include refid="common_selectTotalListBySale_where_conditions" />
        ),

        last_seven_fail_agg AS (
        SELECT title_type, reason, SUM(count) AS total_count
        FROM last_seven_fail_kv
        WHERE reason IS NOT NULL
        GROUP BY title_type, reason
        ),

        last_seven_fail_json AS (
        SELECT title_type, JSON_OBJECTAGG(reason, total_count) AS lastSevenFailClassify
        FROM last_seven_fail_agg
        GROUP BY title_type
        )

        SELECT
        main.yesterdayStores,
        main.lastSevenStores,
        main.yesterdayTotalNum,
        main.lastSevenTotalNum,
        main.yesterdaySuccessNum,
        main.lastSevenSuccessNum,
        main.yesterdayFailNum,
        main.lastSevenFailNum,
        COALESCE(yf.yesterdayFailClassify, '{}') AS yesterdayFailClassify,
        COALESCE(lsf.lastSevenFailClassify, '{}') AS lastSevenFailClassify,
        main.titleType
        FROM (
        SELECT
        COUNT(DISTINCT CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN account
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN account
        ELSE NULL
        END) AS yesterdayStores,
        COUNT(DISTINCT CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN account
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN account
        ELSE NULL
        END) AS lastSevenStores,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN total_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN total_num
        ELSE 0
        END) AS yesterdayTotalNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN total_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN total_num
        ELSE 0
        END) AS lastSevenTotalNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN success_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN success_num
        ELSE 0
        END) AS yesterdaySuccessNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN success_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN success_num
        ELSE 0
        END) AS lastSevenSuccessNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN fail_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN fail_num
        ELSE 0
        END) AS yesterdayFailNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN fail_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN fail_num
        ELSE 0
        END) AS lastSevenFailNum,
        title_type AS titleType
        FROM aliexpress_task_execution_details
        WHERE 1=1
       <include refid="common_selectTotalListBySale_where_conditions" />
        GROUP BY title_type
        ) AS main
        LEFT JOIN yesterday_fail_json yf ON main.titleType = yf.title_type
        LEFT JOIN last_seven_fail_json lsf ON main.titleType = lsf.title_type
    </select>
    <sql id="common_selectTotalListBySale_where_conditions">
        <if test="userSet != null and userSet.size() > 0">
            AND sale IN
            <foreach collection="userSet" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="code == 1">
            AND rule_name IS NOT NULL
        </if>
        <if test="code == 2">
            AND rule_name IS NULL
        </if>
    </sql>
    <select id="selectTotalListByAccount" resultType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionTotal">
        WITH numbers AS (
        SELECT 0 AS num UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
        UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
        UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14
        UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19
        UNION ALL SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23 UNION ALL SELECT 24
        UNION ALL SELECT 25 UNION ALL SELECT 26 UNION ALL SELECT 27 UNION ALL SELECT 28 UNION ALL SELECT 29
        UNION ALL SELECT 30
        ),
        yesterday_fail_kv AS (
        SELECT
        t.title_type,
        <if test="dto.dimension == 1">t.sale</if>
        <if test="dto.dimension == 2">t.sale_leader</if>
        <if test="dto.dimension == 3">t.sale_supervisor</if>
        <if test="dto.dimension == null">NULL</if> AS sale_dimension,
        JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))) AS reason,
        JSON_EXTRACT(t.failure_reasons_count_json,
        CONCAT('$."', JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))), '"')) AS count
        FROM aliexpress_task_execution_details t
        JOIN numbers n ON n.num &lt; JSON_LENGTH(JSON_KEYS(t.failure_reasons_count_json))
        WHERE (
        (t.title_type IN (1, 2) AND DATE(t.count_time) = CURDATE() - INTERVAL 2 DAY)
        OR
        (t.title_type NOT IN (1, 2) AND DATE(t.count_time) = CURDATE() - INTERVAL 1 DAY)
        )
        AND t.sale IS NOT NULL
        <include refid="commonWhereConditions"/>
        ),
        yesterday_fail_agg AS (
        SELECT title_type, sale_dimension, reason, SUM(count) AS total_count
        FROM yesterday_fail_kv
        WHERE reason IS NOT NULL
        GROUP BY title_type, sale_dimension, reason
        ),
        yesterday_fail_json AS (
        SELECT title_type, sale_dimension, JSON_OBJECTAGG(reason, total_count) AS yesterdayFailClassify
        FROM yesterday_fail_agg
        GROUP BY title_type, sale_dimension
        ),
        last_seven_fail_kv AS (
        SELECT
        t.title_type,
        <if test="dto.dimension == 1">t.sale</if>
        <if test="dto.dimension == 2">t.sale_leader</if>
        <if test="dto.dimension == 3">t.sale_supervisor</if>
        <if test="dto.dimension == null">NULL</if> AS sale_dimension,
        JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))) AS reason,
        JSON_EXTRACT(t.failure_reasons_count_json,
        CONCAT('$."', JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(t.failure_reasons_count_json), CONCAT('$[', n.num, ']'))), '"')) AS count
        FROM aliexpress_task_execution_details t
        JOIN numbers n ON n.num &lt; JSON_LENGTH(JSON_KEYS(t.failure_reasons_count_json))
        WHERE (
        (t.title_type IN (1, 2) AND DATE(t.count_time) >= CURDATE() - INTERVAL 9 DAY)
        OR
        (t.title_type NOT IN (1, 2) AND DATE(t.count_time) >= CURDATE() - INTERVAL 8 DAY)
        )
        AND t.sale IS NOT NULL
        <include refid="commonWhereConditions"/>
        ),
        last_seven_fail_agg AS (
        SELECT title_type, sale_dimension, reason, SUM(count) AS total_count
        FROM last_seven_fail_kv
        WHERE reason IS NOT NULL
        GROUP BY title_type, sale_dimension, reason
        ),
        last_seven_fail_json AS (
        SELECT title_type, sale_dimension, JSON_OBJECTAGG(reason, total_count) AS lastSevenFailClassify
        FROM last_seven_fail_agg
        GROUP BY title_type, sale_dimension
        )
        SELECT
        main.yesterdayStores,
        main.lastSevenStores,
        main.yesterdayTotalNum,
        main.lastSevenTotalNum,
        main.yesterdaySuccessNum,
        main.lastSevenSuccessNum,
        main.yesterdayFailNum,
        main.lastSevenFailNum,
        COALESCE(yf.yesterdayFailClassify, '{}') AS yesterdayFailClassify,
        COALESCE(lsf.lastSevenFailClassify, '{}') AS lastSevenFailClassify,
        main.titleType,
        <if test="dto.dimension == 1">main.sale</if>
        <if test="dto.dimension == 2">main.sale_leader</if>
        <if test="dto.dimension == 3">main.sale_supervisor</if>
        <if test="dto.dimension == null">NULL</if> AS saleNum
        FROM (
        SELECT
        COUNT(DISTINCT CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN account
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN account
        ELSE NULL
        END) AS yesterdayStores,
        COUNT(DISTINCT CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN account
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN account
        ELSE NULL
        END) AS lastSevenStores,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN total_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN total_num
        ELSE 0
        END) AS yesterdayTotalNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN total_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN total_num
        ELSE 0
        END) AS lastSevenTotalNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN success_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN success_num
        ELSE 0
        END) AS yesterdaySuccessNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN success_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN success_num
        ELSE 0
        END) AS lastSevenSuccessNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 2 DAY THEN fail_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) = CURDATE() - INTERVAL 1 DAY THEN fail_num
        ELSE 0
        END) AS yesterdayFailNum,
        SUM(CASE
        WHEN title_type IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 9 DAY THEN fail_num
        WHEN title_type NOT IN (1, 2) AND DATE(count_time) >= CURDATE() - INTERVAL 8 DAY THEN fail_num
        ELSE 0
        END) AS lastSevenFailNum,
        title_type AS titleType
        <if test="dto.dimension == 1">,sale</if>
        <if test="dto.dimension == 2">,sale_leader</if>
        <if test="dto.dimension == 3">,sale_supervisor</if>
        FROM aliexpress_task_execution_details
        WHERE 1=1
        AND sale IS NOT NULL
        <include refid="commonWhereConditions"/>
        GROUP BY title_type
        <if test="dto.dimension == 1">, sale</if>
        <if test="dto.dimension == 2">, sale_leader</if>
        <if test="dto.dimension == 3">, sale_supervisor</if>
        ) AS main
        LEFT JOIN yesterday_fail_json yf
        ON main.titleType = yf.title_type
        <if test="dto.dimension == 1">AND main.sale = yf.sale_dimension</if>
        <if test="dto.dimension == 2">AND main.sale_leader = yf.sale_dimension</if>
        <if test="dto.dimension == 3">AND main.sale_supervisor = yf.sale_dimension</if>
        LEFT JOIN last_seven_fail_json lsf
        ON main.titleType = lsf.title_type
        <if test="dto.dimension == 1">AND main.sale = lsf.sale_dimension</if>
        <if test="dto.dimension == 2">AND main.sale_leader = lsf.sale_dimension</if>
        <if test="dto.dimension == 3">AND main.sale_supervisor = lsf.sale_dimension</if>
    </select>

    <sql id="commonWhereConditions">
        <if test="dto.accounts != null and dto.accounts.size() > 0">
            AND account IN
            <foreach collection="dto.accounts" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
        </if>
        <if test="dto.sale != null and dto.sale.size() > 0">
            AND sale IN
            <foreach collection="dto.sale" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleLeader != null and dto.saleLeader.size() > 0">
            AND sale_leader IN
            <foreach collection="dto.saleLeader" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleSupervisor != null and dto.saleSupervisor.size() > 0">
            AND sale_supervisor IN
            <foreach collection="dto.saleSupervisor" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 1">
            AND rule_name IS NOT NULL
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 2">
            AND rule_name IS NULL
        </if>
        <if test="dto.titleTypeList != null and dto.titleTypeList.size() > 0">
            AND title_type IN
            <foreach collection="dto.titleTypeList" item="titleType" open="(" separator="," close=")">
                #{titleType}
            </foreach>
        </if>
        <if test="dto.dimension == 2">
            AND sale_leader != ''
        </if>
        <if test="dto.dimension == 3">
            AND sale_supervisor != ''
        </if>
    </sql>



    <select id="aggregateByDay"
            resultType="com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalHistoryDto">
        WITH RECURSIVE date_range AS (
        SELECT DATE(#{dto.startTime}) AS date
        UNION ALL
        SELECT DATE_ADD(date, INTERVAL 1 DAY)
        FROM date_range
        WHERE date &lt; STR_TO_DATE(#{dto.endTime}, '%Y-%m-%d')
        )
        SELECT
        dr.date AS timePeriod,
        IFNULL(SUM(a.total_num), 0) AS totalNum,
        IFNULL(SUM(a.success_num), 0) AS successNum
        FROM
        date_range dr
        LEFT JOIN
        aliexpress_task_execution_details a
        ON
        DATE(a.count_time) = dr.date
        AND a.title_type = #{dto.titleType}
        <if test="dto.accounts != null and dto.accounts.size() > 0">
            AND a.account IN
            <foreach collection="dto.accounts" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
        </if>
        <if test="dto.sale != null and dto.sale.size() > 0">
            AND sale IN
            <foreach collection="dto.sale" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleLeader != null and dto.saleLeader.size() > 0">
            AND sale_leader IN
            <foreach collection="dto.saleLeader" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleSupervisor != null and dto.saleSupervisor.size() > 0">
            AND sale_supervisor IN
            <foreach collection="dto.saleSupervisor" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 1">
            AND a.rule_name IS NOT NULL
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 2">
            AND a.rule_name IS NULL
        </if>

        <if test="dto.dimension != null and dto.dimension == 1">
            AND sale = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 2">
            AND sale_leader = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 3">
            AND sale_supervisor = #{dto.saleNum}
        </if>

        GROUP BY dr.date
        ORDER BY dr.date
    </select>

    <select id="aggregateByWeek"
            resultType="com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalHistoryDto">

        WITH RECURSIVE week_range AS (
        SELECT
        DATE_FORMAT(#{dto.startTime}, '%Y-%u') AS yearWeek,
        #{dto.startTime} AS startDate
        UNION ALL
        SELECT
        DATE_FORMAT(DATE_ADD(startDate, INTERVAL 7 DAY), '%Y-%u'),
        DATE_ADD(startDate, INTERVAL 7 DAY)
        FROM week_range
        WHERE startDate  <![CDATA[<]]> #{dto.endTime}
        )
        SELECT
        wr.yearWeek AS timePeriod,
        IFNULL(SUM(a.total_num), 0) AS totalNum,
        IFNULL(SUM(a.success_num), 0) AS successNum
        FROM
        week_range wr
        LEFT JOIN
        aliexpress_task_execution_details a
        ON
        DATE_FORMAT(a.count_time, '%Y-%u') = wr.yearWeek
        AND a.title_type = #{dto.titleType}

        <if test="dto.accounts != null and dto.accounts.size() > 0">
            AND a.account IN
            <foreach collection="dto.accounts" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
        </if>

        <if test="dto.sale != null and dto.sale.size() > 0">
            AND sale IN
            <foreach collection="dto.sale" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleLeader != null and dto.saleLeader.size() > 0">
            AND sale_leader IN
            <foreach collection="dto.saleLeader" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleSupervisor != null and dto.saleSupervisor.size() > 0">
            AND sale_supervisor IN
            <foreach collection="dto.saleSupervisor" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 1">
            AND a.rule_name IS NOT NULL
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 2">
            AND a.rule_name IS NULL
        </if>

        <if test="dto.dimension != null and dto.dimension == 1">
            AND sale = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 2">
            AND sale_leader = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 3">
            AND sale_supervisor = #{dto.saleNum}
        </if>

        GROUP BY wr.yearWeek
        ORDER BY wr.yearWeek

    </select>


    <select id="aggregateByMonth"
            resultType="com.estone.erp.publish.tidb.publishtidb.dto.AliexpressTaskExecutionDetailsToTotalHistoryDto">
        WITH RECURSIVE month_range AS (
        SELECT DATE_FORMAT(#{dto.startTime}, '%Y-%m') AS yearMonth
        UNION ALL
        SELECT DATE_FORMAT(DATE_ADD(CONCAT(yearMonth, '-01'), INTERVAL 1 MONTH), '%Y-%m')
        FROM month_range
        WHERE yearMonth &lt; DATE_FORMAT(#{dto.endTime}, '%Y-%m')
        )
        SELECT
        mr.yearMonth AS timePeriod,
        IFNULL(SUM(a.total_num), 0) AS totalNum,
        IFNULL(SUM(a.success_num), 0) AS successNum
        FROM
        month_range mr
        LEFT JOIN
        aliexpress_task_execution_details a
        ON
        DATE_FORMAT(a.count_time, '%Y-%m') = mr.yearMonth
        AND a.title_type = #{dto.titleType}
        <if test="dto.accounts != null and dto.accounts.size() > 0">
            AND a.account IN
            <foreach collection="dto.accounts" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
        </if>
        <if test="dto.sale != null and dto.sale.size() > 0">
            AND sale IN
            <foreach collection="dto.sale" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleLeader != null and dto.saleLeader.size() > 0">
            AND sale_leader IN
            <foreach collection="dto.saleLeader" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.saleSupervisor != null and dto.saleSupervisor.size() > 0">
            AND sale_supervisor IN
            <foreach collection="dto.saleSupervisor" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 1">
            AND a.rule_name IS NOT NULL
        </if>
        <if test="dto.ruleNameType != null and dto.ruleNameType == 2">
            AND a.rule_name IS NULL
        </if>

        <if test="dto.dimension != null and dto.dimension == 1">
            AND sale = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 2">
            AND sale_leader = #{dto.saleNum}
        </if>
        <if test="dto.dimension != null and dto.dimension == 3">
            AND sale_supervisor = #{dto.saleNum}
        </if>

        GROUP BY mr.yearMonth
        ORDER BY mr.yearMonth
    </select>
    <select id="selectSaleLeaderList" resultType="java.lang.String">
        SELECT DISTINCT sale_leader AS sale
        FROM aliexpress_task_execution_details
        WHERE sale_leader IS NOT NULL
          AND sale_leader != ''
    </select>
    <select id="selectSupervisorList" resultType="java.lang.String">
        SELECT DISTINCT sale_supervisor AS sale
        FROM aliexpress_task_execution_details
        WHERE sale_supervisor IS NOT NULL
          AND sale_supervisor != ''
    </select>
    <select id="selectSaleByLeader" resultType="java.lang.String">
        SELECT DISTINCT sale
        FROM aliexpress_task_execution_details
        WHERE
            1=1
        <if test="saleLeader != null and saleLeader != ''">
            and sale_leader = #{saleLeader}
        </if>
        <if test="saleSupervisor != null and saleSupervisor != ''">
            and sale_supervisor = #{saleSupervisor}
        </if>
    </select>
    <select id="selectAccount" resultType="java.lang.String">
        select DISTINCT account from aliexpress_task_execution_details
    </select>

</mapper>
