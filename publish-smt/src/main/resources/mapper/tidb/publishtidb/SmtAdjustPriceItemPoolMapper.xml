<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtAdjustPriceItemPoolMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtAdjustPriceItemPool">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
            <result property="batchId" column="batch_id" jdbcType="VARCHAR"/>
            <result property="level" column="level" jdbcType="INTEGER"/>
            <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
            <result property="ruleContent" column="rule_content" jdbcType="VARCHAR"/>
            <result property="linkId" column="link_id" jdbcType="VARCHAR"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
            <result property="articleNumber" column="article_number" jdbcType="VARCHAR"/>
            <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
            <result property="beforeSkuPrice" column="before_sku_price" jdbcType="DOUBLE"/>
            <result property="afterSkuPrice" column="after_sku_price" jdbcType="DOUBLE"/>
            <result property="beforeAreaPrice" column="before_area_price" jdbcType="VARCHAR"/>
            <result property="afterAreaPrice" column="after_area_price" jdbcType="VARCHAR"/>
            <result property="beforeGrossProfitRate" column="before_gross_profit_rate" jdbcType="DOUBLE"/>
            <result property="afterGrossProfitRate" column="after_gross_profit_rate" jdbcType="DOUBLE"/>
            <result property="beforeTemplateId" column="before_template_id" jdbcType="BIGINT"/>
            <result property="beforeTemplateName" column="before_template_name" jdbcType="VARCHAR"/>
            <result property="afterTemplateId" column="after_template_id" jdbcType="BIGINT"/>
            <result property="afterTemplateName" column="after_template_name" jdbcType="VARCHAR"/>
            <result property="confirmStatus" column="confirm_status" jdbcType="INTEGER"/>
            <result property="confirmRemark" column="confirm_remark" jdbcType="VARCHAR"/>
            <result property="confirmUser" column="confirm_user" jdbcType="VARCHAR"/>
            <result property="confirmTime" column="confirm_time" jdbcType="TIMESTAMP"/>
            <result property="updatePriceStatus" column="update_price_status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,batch_id,rule_id,level,
        rule_name,rule_content,link_id,
        account_number,product_id,sku_code,
        article_number,sku_id,before_sku_price,
        after_sku_price,before_area_price,after_area_price,
        before_gross_profit_rate,after_gross_profit_rate,before_template_id,
        before_template_name,after_template_id,after_template_name,
        confirm_status,confirm_remark,confirm_user,
        confirm_time,update_price_status,remark,
        created_time,updated_time
    </sql>
</mapper>
