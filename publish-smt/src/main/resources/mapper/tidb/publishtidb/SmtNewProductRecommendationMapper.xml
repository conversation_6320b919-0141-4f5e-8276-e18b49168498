<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtNewProductRecommendationMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spu" column="spu" jdbcType="VARCHAR"/>
            <result property="itemStatus" column="item_status" jdbcType="VARCHAR"/>
            <result property="tags" column="tags" jdbcType="VARCHAR"/>
            <result property="categoryFullPathCode" column="category_full_path_code" jdbcType="VARCHAR"/>
            <result property="categoryPathName" column="category_path_name" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="sale" column="sale" jdbcType="VARCHAR"/>
            <result property="saleLeader" column="sale_leader" jdbcType="VARCHAR"/>
            <result property="templateId" column="template_id" jdbcType="INTEGER"/>
            <result property="publishStatus" column="publish_status" jdbcType="INTEGER"/>
            <result property="isBanned" column="is_banned" jdbcType="BOOLEAN"/>
            <result property="publishSuccessTime" column="publish_success_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="isTheSameDayPublish" column="is_the_same_day_publish" jdbcType="BOOLEAN"/>
            <result property="isThreeDayPublish" column="is_three_day_publish" jdbcType="BOOLEAN"/>
            <result property="pushTime" column="push_time" jdbcType="TIMESTAMP"/>
            <result property="enterProductTime" column="enter_product_time" jdbcType="TIMESTAMP"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="extendedField1" column="extended_field1" jdbcType="VARCHAR"/>
            <result property="extendedField2" column="extended_field2" jdbcType="VARCHAR"/>
            <result property="extendedField3" column="extended_field3" jdbcType="VARCHAR"/>
            <result property="extendedField4" column="extended_field4" jdbcType="VARCHAR"/>
            <result property="extendedField5" column="extended_field5" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spu,item_status,
        tags,category_full_path_code,category_path_name,
        title,sale,sale_leader,
        template_id,publish_status,is_banned,
        publish_success_time,remark,is_the_same_day_publish,
        is_three_day_publish,push_time,enter_product_time,
        created_time,updated_time,extended_field1,
        extended_field2,extended_field3,extended_field4,
        extended_field5
    </sql>

    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
            floor((t.row_num - 1) / 300) + 1 AS page_num,
            min(t.id) AS start_key,
            max(t.id) AS end_key,
            count(*) AS page_size
        FROM
            (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
             FROM  smt_new_product_recommendation
                       ${ew.customSqlSegment}
            ) t
        GROUP BY
            page_num
        ORDER BY
            page_num;
    </select>
</mapper>
