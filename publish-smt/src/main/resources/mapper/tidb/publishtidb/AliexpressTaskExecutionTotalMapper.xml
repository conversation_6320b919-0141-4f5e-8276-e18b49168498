<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressTaskExecutionTotalMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionTotal">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="todayDate" column="today_date" jdbcType="VARCHAR"/>
            <result property="saleNum" column="sale_num" jdbcType="INTEGER"/>
            <result property="titleType" column="title_type" jdbcType="INTEGER"/>
            <result property="yesterdayStores" column="yesterday_stores" jdbcType="INTEGER"/>
            <result property="yesterdayTotalNum	" column="yesterday_total_num	" jdbcType="INTEGER"/>
            <result property="yesterdayFailNum" column="yesterday_fail_num" jdbcType="INTEGER"/>
            <result property="yesterdaySuccessNum" column="yesterday_success_num" jdbcType="INTEGER"/>
            <result property="lastSevenStores" column="last_seven_stores" jdbcType="INTEGER"/>
            <result property="lastSevenTotalNum" column="last_seven_total_num" jdbcType="INTEGER"/>
            <result property="lastSevenFailNum" column="last_seven_fail_num" jdbcType="INTEGER"/>
            <result property="lastSevenSuccessNum" column="last_seven_success_num" jdbcType="INTEGER"/>
            <result property="superStatus" column="super_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,today_date,sale_num,
        title_type,yesterday_stores,yesterday_total_num	,
        yesterday_fail_num,yesterday_success_num,last_seven_stores,
        last_seven_total_num,last_seven_fail_num,last_seven_success_num,
        super_status
    </sql>
</mapper>
