<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtSingleDiscountProductMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="item_id" property="itemId" jdbcType="BIGINT" />
    <result column="local_single_discount_id" property="localSingleDiscountId" jdbcType="BIGINT" />
    <result column="plat_single_discount_id" property="platSingleDiscountId" jdbcType="BIGINT" />
    <result column="store_club_discount_rate" property="store_club_discount_rate" jdbcType="INTEGER" />
    <result column="discount" property="discount" jdbcType="INTEGER" />
    <result column="buy_max_num" property="buy_max_num" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, item_id, local_single_discount_id, plat_single_discount_id, store_club_discount_rate, 
    discount, buy_max_num, create_time, update_time,club_discount_type
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_single_discount_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from smt_single_discount_product
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_single_discount_product
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="delete">
    delete from smt_single_discount_product where local_single_discount_id=#{localSingleDiscountId} and
    item_id IN
    <foreach collection="needDelList" item="item" open="(" close=")" separator="," >
      #{item}
    </foreach>

  </delete>

  <delete id="deleteBySingleDiscount">
    delete from smt_single_discount_product where plat_single_discount_id=#{singleDiscountId} and
    item_id IN
    <foreach collection="productIdList" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_single_discount_product (account_number, item_id, local_single_discount_id, 
      plat_single_discount_id, store_club_discount_rate, 
      discount, buy_max_num, create_time, club_discount_type,
      update_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{itemId,jdbcType=BIGINT}, #{localSingleDiscountId,jdbcType=BIGINT}, 
      #{platSingleDiscountId,jdbcType=BIGINT}, #{store_club_discount_rate,jdbcType=INTEGER},
      #{discount,jdbcType=INTEGER}, #{buy_max_num,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},#{club_discount_type},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>

    <insert id="batchInsert">
      insert into smt_single_discount_product (account_number, item_id, local_single_discount_id,
                                               plat_single_discount_id, store_club_discount_rate,
                                               discount, buy_max_num, create_time,
                                               update_time,club_discount_type)
      values
      <foreach collection="list" item="item" separator="," >
          (#{item.accountNumber,jdbcType=VARCHAR}, #{item.itemId,jdbcType=BIGINT}, #{item.localSingleDiscountId,jdbcType=BIGINT},
              #{item.platSingleDiscountId,jdbcType=BIGINT}, #{item.store_club_discount_rate,jdbcType=INTEGER},
              #{item.discount,jdbcType=INTEGER}, #{item.buy_max_num,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
              #{item.updateTime,jdbcType=TIMESTAMP},#{item.club_discount_type})
      </foreach>

    </insert>

  <select id="countByExample"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample"
          resultType="java.lang.Integer">
    select count(*) from smt_single_discount_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectIdByExample" resultType="Long">
    select
    <if test="distinct" >
      distinct
    </if>
    item_id
    from smt_single_discount_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>

  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update smt_single_discount_product
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.itemId != null" >
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.localSingleDiscountId != null" >
        local_single_discount_id = #{record.localSingleDiscountId,jdbcType=BIGINT},
      </if>
      <if test="record.platSingleDiscountId != null" >
        plat_single_discount_id = #{record.platSingleDiscountId,jdbcType=BIGINT},
      </if>
      <if test="record.store_club_discount_rate != null" >
        store_club_discount_rate = #{record.store_club_discount_rate,jdbcType=INTEGER},
      </if>
      <if test="record.discount != null" >
        discount = #{record.discount,jdbcType=INTEGER},
      </if>
      <if test="record.buy_max_num != null" >
        buy_max_num = #{record.buy_max_num,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct">
    update smt_single_discount_product
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null" >
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="localSingleDiscountId != null" >
        local_single_discount_id = #{localSingleDiscountId,jdbcType=BIGINT},
      </if>
      <if test="platSingleDiscountId != null" >
        plat_single_discount_id = #{platSingleDiscountId,jdbcType=BIGINT},
      </if>
        store_club_discount_rate = #{store_club_discount_rate,jdbcType=INTEGER},
      <if test="discount != null" >
        discount = #{discount,jdbcType=INTEGER},
      </if>
      <if test="club_discount_type != null" >
        club_discount_type = #{club_discount_type},
      </if>

      <if test="buy_max_num != null" >
        buy_max_num = #{buy_max_num,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <update id="batchUpdate">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update smt_single_discount_product
      <set >
        <if test="record.discount != null" >
          discount = #{record.discount,jdbcType=INTEGER},
        </if>
        <if test="record.club_discount_type != null" >
          club_discount_type = #{record.club_discount_type},
        </if>

        <if test="record.store_club_discount_rate != null" >
          store_club_discount_rate = #{record.store_club_discount_rate,jdbcType=INTEGER},
        </if>

        <if test="record.buy_max_num != null" >
          buy_max_num = #{record.buy_max_num,jdbcType=INTEGER},
        </if>
          update_time = now(),
      </set>
      where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>