<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressNewProductPublishLeaderMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublishLeader">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="publishId" column="publish_id" jdbcType="BIGINT"/>
            <result property="saleLeader" column="sale_leader" jdbcType="VARCHAR"/>
            <result property="percent" column="percent" jdbcType="DOUBLE"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,publish_id,sale_leader,
        percent,created_time,updated_time
    </sql>
</mapper>
