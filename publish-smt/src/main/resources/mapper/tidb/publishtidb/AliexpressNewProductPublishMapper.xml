<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressNewProductPublishMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublish">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spu" column="spu" jdbcType="VARCHAR"/>
            <result property="itemStatus" column="item_status" jdbcType="VARCHAR"/>
            <result property="isBanned" column="is_banned" jdbcType="TINYINT"/>
            <result property="pushTime" column="push_time" jdbcType="TIMESTAMP"/>
            <result property="enterProductTime" column="enter_product_time" jdbcType="TIMESTAMP"/>
            <result property="oneListingNumber" column="one_listing_number" jdbcType="INTEGER"/>
            <result property="oneListingNumberPercent" column="one_listing_number_percent" jdbcType="DOUBLE"/>
            <result property="twoListingNumber" column="two_listing_number" jdbcType="INTEGER"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spu,item_status,
        is_banned,push_time,enter_product_time,
        one_listing_number,one_listing_number_percent,two_listing_number,
        created_time,updated_time
    </sql>
    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
            floor((t.row_num - 1) / 300) + 1 AS page_num,
            min(t.id) AS start_key,
            max(t.id) AS end_key,
            count(*) AS page_size
        FROM
            (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
             FROM  aliexpress_new_product_publish
                       ${ew.customSqlSegment}
            ) t
        GROUP BY
            page_num
        ORDER BY
            page_num;
    </select>
</mapper>
