<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtNewProductTemplateCompletionMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductTemplateCompletion">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="saleType" column="sale_type" jdbcType="INTEGER"/>
            <result property="sale" column="sale" jdbcType="VARCHAR"/>
            <result property="saleLeader" column="sale_leader" jdbcType="VARCHAR"/>
            <result property="pushTime" column="push_time" jdbcType="TIMESTAMP"/>
            <result property="completionRate" column="completion_rate" jdbcType="DECIMAL"/>
            <result property="unfinishedSpu" column="unfinished_spu" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sale_type,sale,
        sale_leader,push_time,completion_rate,
        unfinished_spu,created_time,updated_time
    </sql>

    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
            floor((t.row_num - 1) / 300) + 1 AS page_num,
            min(t.id) AS start_key,
            max(t.id) AS end_key,
            count(*) AS page_size
        FROM
            (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
             FROM  smt_new_product_template_completion
                       ${ew.customSqlSegment}
            ) t
        GROUP BY
            page_num
        ORDER BY
            page_num;
    </select>
</mapper>
