<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtEarlyBirdActivityMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivity">
    <id column="productId" property="productid" jdbcType="VARCHAR" />
    <result column="accountNumber" property="accountnumber" jdbcType="VARCHAR" />
    <result column="sellerId" property="sellerid" jdbcType="VARCHAR" />
    <result column="imageUrl" property="imageurl" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="stock" property="stock" jdbcType="VARCHAR" />
    <result column="rangePrice" property="rangeprice" jdbcType="VARCHAR" />
    <result column="activitieStatus" property="activitiestatus" jdbcType="VARCHAR" />
    <result column="addStatus" property="addstatus" jdbcType="VARCHAR" />
    <result column="hostStartTime" property="hoststarttime" jdbcType="VARCHAR" />
    <result column="hostEndTime" property="hostendtime" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="crawlTime" property="crawltime" jdbcType="VARCHAR" />
    <result column="earlyBirdHosting5" property="earlybirdhosting5" jdbcType="VARCHAR" />
    <result column="earlyBirdHosting7" property="earlybirdhosting7" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    productId, accountNumber, sellerId, imageUrl, title, stock, rangePrice, activitieStatus, 
    addStatus, hostStartTime, hostEndTime, remark, crawlTime,earlyBirdHosting5,earlyBirdHosting7
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivityExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from smt_early_bird_activity
    where productId = #{productid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_early_bird_activity
    where productId IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivity">
    insert into smt_early_bird_activity (productId, accountNumber, sellerId, 
      imageUrl, title, stock, 
      rangePrice, activitieStatus, addStatus, 
      hostStartTime, hostEndTime, remark, 
      crawlTime,earlyBirdHosting5,earlyBirdHosting7)
    values (#{productid,jdbcType=VARCHAR}, #{accountnumber,jdbcType=VARCHAR}, #{sellerid,jdbcType=VARCHAR}, 
      #{imageurl,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{stock,jdbcType=VARCHAR}, 
      #{rangeprice,jdbcType=VARCHAR}, #{activitiestatus,jdbcType=VARCHAR}, #{addstatus,jdbcType=VARCHAR}, 
      #{hoststarttime,jdbcType=VARCHAR}, #{hostendtime,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{crawltime,jdbcType=VARCHAR}),#{earlybirdhosting5,jdbcType=VARCHAR}),#{earlybirdhosting7,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivityExample"
          resultType="java.lang.Integer">
    select count(*) from smt_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_early_bird_activity
    <set >
      <if test="record.productid != null" >
        productId = #{record.productid,jdbcType=VARCHAR},
      </if>
      <if test="record.accountnumber != null" >
        accountNumber = #{record.accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerid != null" >
        sellerId = #{record.sellerid,jdbcType=VARCHAR},
      </if>
      <if test="record.imageurl != null" >
        imageUrl = #{record.imageurl,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.stock != null" >
        stock = #{record.stock,jdbcType=VARCHAR},
      </if>
      <if test="record.rangeprice != null" >
        rangePrice = #{record.rangeprice,jdbcType=VARCHAR},
      </if>
      <if test="record.activitiestatus != null" >
        activitieStatus = #{record.activitiestatus,jdbcType=VARCHAR},
      </if>
      <if test="record.addstatus != null" >
        addStatus = #{record.addstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.hoststarttime != null" >
        hostStartTime = #{record.hoststarttime,jdbcType=VARCHAR},
      </if>
      <if test="record.hostendtime != null" >
        hostEndTime = #{record.hostendtime,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.crawltime != null" >
        crawlTime = #{record.crawltime,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivity">
    update smt_early_bird_activity
    <set >
      <if test="accountnumber != null" >
        accountNumber = #{accountnumber,jdbcType=VARCHAR},
      </if>
      <if test="sellerid != null" >
        sellerId = #{sellerid,jdbcType=VARCHAR},
      </if>
      <if test="imageurl != null" >
        imageUrl = #{imageurl,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="stock != null" >
        stock = #{stock,jdbcType=VARCHAR},
      </if>
      <if test="rangeprice != null" >
        rangePrice = #{rangeprice,jdbcType=VARCHAR},
      </if>
      <if test="activitiestatus != null" >
        activitieStatus = #{activitiestatus,jdbcType=VARCHAR},
      </if>
      <if test="addstatus != null" >
        addStatus = #{addstatus,jdbcType=VARCHAR},
      </if>
      <if test="hoststarttime != null" >
        hostStartTime = #{hoststarttime,jdbcType=VARCHAR},
      </if>
      <if test="hostendtime != null" >
        hostEndTime = #{hostendtime,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="crawltime != null" >
        crawlTime = #{crawltime,jdbcType=VARCHAR},
      </if>
    </set>
    where productId = #{productid,jdbcType=VARCHAR}
  </update>
  <select id="selectAllStore" resultType="java.lang.String">
    SELECT DISTINCT accountNumber FROM smt_early_bird_activity ;
  </select>
  <select id="selectProductIdsByExample" resultType="java.lang.String"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.SmtEarlyBirdActivityExample">
    select productId
    from smt_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="getTidbPageMetaMap" resultType="map">
    SELECT
      floor((t.row_num - 1) / 300) + 1 AS page_num,
      min(t.productId) AS start_key,
      max(t.productId) AS end_key,
      count(*) AS page_size
    FROM
      (SELECT productId, row_number() OVER (ORDER BY productId ) AS row_num
       FROM  smt_early_bird_activity
               ${ew.customSqlSegment}
      ) t
    GROUP BY
      page_num
    ORDER BY
      page_num;
  </select>
</mapper>