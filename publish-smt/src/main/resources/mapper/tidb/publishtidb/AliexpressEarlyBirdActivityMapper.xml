<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressEarlyBirdActivityMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="store" property="store" jdbcType="VARCHAR" />
    <result column="picture" property="picture" jdbcType="VARCHAR" />
    <result column="itemid" property="itemid" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="sellersku" property="sellersku" jdbcType="VARCHAR" />
    <result column="base_price" property="basePrice" jdbcType="DECIMAL" />
    <result column="stock" property="stock" jdbcType="INTEGER" />
    <result column="custody_period" property="custodyPeriod" jdbcType="VARCHAR" />
    <result column="activity_status" property="activityStatus" jdbcType="VARCHAR" />
    <result column="join_status" property="joinStatus" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="available_stock" property="availableStock" jdbcType="INTEGER" />
    <result column="listing_time" property="listingTime" jdbcType="TIMESTAMP" />
    <result column="seller" property="seller" jdbcType="VARCHAR" />
    <result column="seller_leader" property="sellerLeader" jdbcType="VARCHAR" />
    <result column="seller_manager" property="sellerManager" jdbcType="VARCHAR" />
    <result column="order_last_7d_count" property="orderLast7dCount" jdbcType="INTEGER" />
    <result column="order_last_14d_count" property="orderLast14dCount" jdbcType="INTEGER" />
    <result column="order_last_30d_count" property="orderLast30dCount" jdbcType="INTEGER" />
    <result column="order_last_60d_count" property="orderLast60dCount" jdbcType="INTEGER" />
    <result column="order_num_total" property="orderNumTotal" jdbcType="INTEGER"/>
    <result column="crawling_time" property="crawlingTime" jdbcType="TIMESTAMP" />
    <result column="syn_time" property="synTime" jdbcType="TIMESTAMP" />
    <result column="confirm_join_time" property="confirmJoinTime" jdbcType="TIMESTAMP" />
    <result column="join_time" property="joinTime" jdbcType="TIMESTAMP" />

    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="host_end_time" property="hostEndTime" jdbcType="TIMESTAMP" />
    <result column="five_discount" property="fiveDiscount" jdbcType="DOUBLE" />
    <result column="five_discount_detail" property="fiveDiscountDetail" jdbcType="VARCHAR" />
    <result column="seven_discount" property="sevenDiscount" jdbcType="DOUBLE" />
    <result column="seven_discount_detail" property="sevenDiscountDetail" jdbcType="VARCHAR" />
    <result column="relate_data" property="relateData" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, store, picture, itemid, title, sellersku, base_price, stock, custody_period, 
    activity_status, join_status, remark, spu, available_stock, listing_time, seller,
    seller_leader, seller_manager, order_last_7d_count, order_last_14d_count, order_last_30d_count, 
    order_last_60d_count, crawling_time, syn_time, confirm_join_time, join_time, order_num_total,
      rule_name,host_end_time,five_discount,five_discount_detail,seven_discount,seven_discount_detail,relate_data
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from aliexpress_early_bird_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_early_bird_activity
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_early_bird_activity (store, picture, itemid, 
      title, sellersku, base_price, 
      stock, custody_period, activity_status, 
      join_status, remark, spu,
      available_stock, listing_time, seller, 
      seller_leader, seller_manager, order_last_7d_count, 
      order_last_14d_count, order_last_30d_count, order_last_60d_count, 
      crawling_time, syn_time, confirm_join_time, 
      join_time,rule_name,host_end_time,five_discount,five_discount_detail,
    seven_discount,seven_discount_detail,relate_data)
    values (#{store,jdbcType=VARCHAR}, #{picture,jdbcType=VARCHAR}, #{itemid,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{sellersku,jdbcType=VARCHAR}, #{basePrice,jdbcType=DECIMAL}, 
      #{stock,jdbcType=INTEGER}, #{custodyPeriod,jdbcType=VARCHAR}, #{activityStatus,jdbcType=VARCHAR}, 
      #{joinStatus,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{spu,jdbcType=VARCHAR},
      #{availableStock,jdbcType=INTEGER}, #{listingTime,jdbcType=TIMESTAMP}, #{seller,jdbcType=VARCHAR}, 
      #{sellerLeader,jdbcType=VARCHAR}, #{sellerManager,jdbcType=VARCHAR}, #{orderLast7dCount,jdbcType=INTEGER}, 
      #{orderLast14dCount,jdbcType=INTEGER}, #{orderLast30dCount,jdbcType=INTEGER}, #{orderLast60dCount,jdbcType=INTEGER}, 
      #{crawlingTime,jdbcType=TIMESTAMP}, #{synTime,jdbcType=TIMESTAMP}, #{confirmJoinTime,jdbcType=TIMESTAMP}, 
      #{joinTime,jdbcType=TIMESTAMP},
    #{ruleName,jdbcType=VARCHAR},#{hostEndTime,jdbcType=TIMESTAMP},#{fiveDiscount,jdbcType=DOUBLE},
    #{fiveDiscountDetail,jdbcType=VARCHAR},#{sevenDiscount,jdbcType=DOUBLE},#{sevenDiscountDetail,jdbcType=VARCHAR},#{relateData,jdbcType=VARCHAR})
  </insert>
  <delete id="deleteOverDueProduct">
    DELETE FROM aliexpress_early_bird_activity
    WHERE DATE_SUB(NOW(), INTERVAL 2 DAY) > syn_time
  </delete>
  <select id="countByExample"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample"
          resultType="java.lang.Integer">
    select count(*) from aliexpress_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectAllStore" resultType="java.lang.String">
    SELECT DISTINCT store FROM aliexpress_early_bird_activity ;
  </select>
  <select id="selectItemIdsByExample" resultType="java.lang.String"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample">
    select itemid
    from aliexpress_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByItemIds" >
    delete from aliexpress_early_bird_activity
    where itemid IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <select id="selectCountByJoinStatus"
          resultType="com.estone.erp.publish.smt.model.dto.AliexpressProductLogCountDto">
    select store as accountNumber,
           COUNT(*) as totalCount,
           rule_name as ruleName,
           IFNULL(SUM(CASE WHEN join_status = 3 THEN 1 ELSE 0 END),0) AS successCount,
           IFNULL(SUM(CASE WHEN join_status = 4 THEN 1 ELSE 0 END),0) AS failureCount
    from aliexpress_early_bird_activity
    where DATE(confirm_join_time) = CURDATE() - INTERVAL 1 DAY
    GROUP BY
      store,
      rule_name
  </select>
  <select id="selectFieldsByExample" resultMap="BaseResultMap"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivityExample">
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,spu, available_stock, listing_time, seller,
    seller_leader, seller_manager, order_last_7d_count, order_last_14d_count, order_last_30d_count,
    order_last_60d_count, confirm_join_time, join_time,itemid,id,rule_name,host_end_time,five_discount,
    five_discount_detail,seven_discount,seven_discount_detail,relate_data
    from aliexpress_early_bird_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_early_bird_activity
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.store != null" >
        store = #{record.store,jdbcType=VARCHAR},
      </if>
      <if test="record.picture != null" >
        picture = #{record.picture,jdbcType=VARCHAR},
      </if>
      <if test="record.itemid != null" >
        itemid = #{record.itemid,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.sellersku != null" >
        sellersku = #{record.sellersku,jdbcType=VARCHAR},
      </if>
      <if test="record.basePrice != null" >
        base_price = #{record.basePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.stock != null" >
        stock = #{record.stock,jdbcType=INTEGER},
      </if>
      <if test="record.custodyPeriod != null" >
        custody_period = #{record.custodyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStatus != null" >
        activity_status = #{record.activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.joinStatus != null" >
        join_status = #{record.joinStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.availableStock != null" >
        available_stock = #{record.availableStock,jdbcType=INTEGER},
      </if>
      <if test="record.listingTime != null" >
        listing_time = #{record.listingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.seller != null" >
        seller = #{record.seller,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerLeader != null" >
        seller_leader = #{record.sellerLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerManager != null" >
        seller_manager = #{record.sellerManager,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast7dCount != null" >
        order_last_7d_count = #{record.orderLast7dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast14dCount != null" >
        order_last_14d_count = #{record.orderLast14dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast30dCount != null" >
        order_last_30d_count = #{record.orderLast30dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast60dCount != null" >
        order_last_60d_count = #{record.orderLast60dCount,jdbcType=INTEGER},
      </if>
      <if test="record.crawlingTime != null" >
        crawling_time = #{record.crawlingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.synTime != null" >
        syn_time = #{record.synTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmJoinTime != null" >
        confirm_join_time = #{record.confirmJoinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.joinTime != null" >
        join_time = #{record.joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.hostEndTime != null" >
        host_end_time = #{record.hostEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fiveDiscount != null" >
        five_discount = #{record.fiveDiscount,jdbcType=DOUBLE},
      </if>
      <if test="record.fiveDiscountDetail != null" >
        five_discount_detail = #{record.fiveDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.sevenDiscount != null" >
        seven_discount = #{record.sevenDiscount,jdbcType=DOUBLE},
      </if>
      <if test="record.sevenDiscountDetail != null" >
        seven_discount_detail = #{record.sevenDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.relateData != null" >
        relate_data = #{record.relateData,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity">
    update aliexpress_early_bird_activity
    <set >
      <if test="store != null" >
        store = #{store,jdbcType=VARCHAR},
      </if>
      <if test="picture != null" >
        picture = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="itemid != null" >
        itemid = #{itemid,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sellersku != null" >
        sellersku = #{sellersku,jdbcType=VARCHAR},
      </if>
      <if test="basePrice != null" >
        base_price = #{basePrice,jdbcType=DECIMAL},
      </if>
      <if test="stock != null" >
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="custodyPeriod != null" >
        custody_period = #{custodyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        activity_status = #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="joinStatus != null" >
        join_status = #{joinStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="availableStock != null" >
        available_stock = #{availableStock,jdbcType=INTEGER},
      </if>
      <if test="listingTime != null" >
        listing_time = #{listingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="seller != null" >
        seller = #{seller,jdbcType=VARCHAR},
      </if>
      <if test="sellerLeader != null" >
        seller_leader = #{sellerLeader,jdbcType=VARCHAR},
      </if>
      <if test="sellerManager != null" >
        seller_manager = #{sellerManager,jdbcType=VARCHAR},
      </if>
      <if test="orderLast7dCount != null" >
        order_last_7d_count = #{orderLast7dCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast14dCount != null" >
        order_last_14d_count = #{orderLast14dCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast30dCount != null" >
        order_last_30d_count = #{orderLast30dCount,jdbcType=INTEGER},
      </if>
      <if test="orderLast60dCount != null" >
        order_last_60d_count = #{orderLast60dCount,jdbcType=INTEGER},
      </if>
      <if test="crawlingTime != null" >
        crawling_time = #{crawlingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="synTime != null" >
        syn_time = #{synTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmJoinTime != null" >
        confirm_join_time = #{confirmJoinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinTime != null" >
        join_time = #{joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="hostEndTime != null" >
        host_end_time = #{hostEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fiveDiscount != null" >
        five_discount = #{fiveDiscount,jdbcType=DOUBLE},
      </if>
      <if test="fiveDiscountDetail != null" >
        five_discount_detail = #{fiveDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="sevenDiscount != null" >
        seven_discount = #{sevenDiscount,jdbcType=DOUBLE},
      </if>
      <if test="sevenDiscountDetail != null" >
        seven_discount_detail = #{sevenDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="relateData != null" >
        relate_data = #{relateData,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateStatusToJoin"
          parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressEarlyBirdActivity">
    update aliexpress_early_bird_activity set join_status = 2 , confirm_join_time = NOW()
    where id in <foreach collection="ids" item="item" open="(" separator="," close=")">#{item}</foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_early_bird_activity (store, picture, itemid,
    title, sellersku, base_price,base_price_low,base_price_high,
    stock, custody_period, activity_status,
    join_status, remark, spu,
    available_stock, listing_time, seller,
    seller_leader, seller_manager, order_last_7d_count,
    order_last_14d_count, order_last_30d_count, order_last_60d_count,
    crawling_time, syn_time, confirm_join_time,
    join_time,order_num_total,rule_name,host_end_time,five_discount,
    five_discount_detail,seven_discount,seven_discount_detail,relate_data)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.store,jdbcType=VARCHAR}, #{item.picture,jdbcType=VARCHAR}, #{item.itemid,jdbcType=VARCHAR},
      #{item.title,jdbcType=VARCHAR}, #{item.sellersku,jdbcType=VARCHAR}, #{item.basePrice,jdbcType=DECIMAL},
      #{item.basePriceLow,jdbcType=DECIMAL},#{item.basePriceHigh,jdbcType=DECIMAL},
      #{item.stock,jdbcType=INTEGER}, #{item.custodyPeriod,jdbcType=VARCHAR}, #{item.activityStatus,jdbcType=VARCHAR},
      #{item.joinStatus,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.spu,jdbcType=VARCHAR},
      #{item.availableStock,jdbcType=INTEGER}, #{item.listingTime,jdbcType=TIMESTAMP}, #{item.seller,jdbcType=VARCHAR},
      #{item.sellerLeader,jdbcType=VARCHAR}, #{item.sellerManager,jdbcType=VARCHAR}, #{item.orderLast7dCount,jdbcType=INTEGER},
      #{item.orderLast14dCount,jdbcType=INTEGER}, #{item.orderLast30dCount,jdbcType=INTEGER}, #{item.orderLast60dCount,jdbcType=INTEGER},
      #{item.crawlingTime,jdbcType=TIMESTAMP}, #{item.synTime,jdbcType=TIMESTAMP}, #{item.confirmJoinTime,jdbcType=TIMESTAMP},
      #{item.joinTime,jdbcType=TIMESTAMP}, #{item.orderNumTotal,jdbcType=INTEGER},
      #{item.ruleName,jdbcType=VARCHAR}, #{item.hostEndTime,jdbcType=TIMESTAMP}, #{item.fiveDiscount,jdbcType=DOUBLE},
      #{item.fiveDiscountDetail,jdbcType=VARCHAR}, #{item.sevenDiscount,jdbcType=DOUBLE}, #{item.sevenDiscountDetail,jdbcType=VARCHAR}, #{item.relateData,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="batchUpdate" >
    <foreach collection="list" item="record" open="" separator=";" close=";">
    update aliexpress_early_bird_activity
    <set >
      <if test="record.store != null" >
        store = #{record.store,jdbcType=VARCHAR},
      </if>
      <if test="record.picture != null" >
        picture = #{record.picture,jdbcType=VARCHAR},
      </if>
      <if test="record.itemid != null" >
        itemid = #{record.itemid,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.sellersku != null" >
        sellersku = #{record.sellersku,jdbcType=VARCHAR},
      </if>
      <if test="record.basePrice != null" >
        base_price = #{record.basePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.basePriceLow != null" >
        base_price_low = #{record.basePriceLow,jdbcType=DECIMAL},
      </if>
      <if test="record.basePriceHigh != null" >
        base_price_high = #{record.basePriceHigh,jdbcType=DECIMAL},
      </if>
      <if test="record.stock != null" >
        stock = #{record.stock,jdbcType=INTEGER},
      </if>
      <if test="record.custodyPeriod != null" >
        custody_period = #{record.custodyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStatus != null" >
        activity_status = #{record.activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.joinStatus != null" >
        join_status = #{record.joinStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.availableStock != null" >
        available_stock = #{record.availableStock,jdbcType=INTEGER},
      </if>
      <if test="record.listingTime != null" >
        listing_time = #{record.listingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.seller != null" >
        seller = #{record.seller,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerLeader != null" >
        seller_leader = #{record.sellerLeader,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerManager != null" >
        seller_manager = #{record.sellerManager,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLast7dCount != null" >
        order_last_7d_count = #{record.orderLast7dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast14dCount != null" >
        order_last_14d_count = #{record.orderLast14dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast30dCount != null" >
        order_last_30d_count = #{record.orderLast30dCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderLast60dCount != null" >
        order_last_60d_count = #{record.orderLast60dCount,jdbcType=INTEGER},
      </if>
      <if test="record.crawlingTime != null" >
        crawling_time = #{record.crawlingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.synTime != null" >
        syn_time = #{record.synTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmJoinTime != null" >
        confirm_join_time = #{record.confirmJoinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.joinTime != null" >
        join_time = #{record.joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderNumTotal != null" >
        order_num_total = #{record.orderNumTotal,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.hostEndTime != null" >
        host_end_time = #{record.hostEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fiveDiscount != null" >
        five_discount = #{record.fiveDiscount,jdbcType=DOUBLE},
      </if>
      <if test="record.fiveDiscountDetail != null" >
        five_discount_detail = #{record.fiveDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.sevenDiscount != null" >
        seven_discount = #{record.sevenDiscount,jdbcType=DOUBLE},
      </if>
      <if test="record.sevenDiscountDetail != null" >
        seven_discount_detail = #{record.sevenDiscountDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.relateData != null" >
        relate_data = #{record.relateData,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getTidbPageMetaMap" resultType="map">
    SELECT
      floor((t.row_num - 1) / 300) + 1 AS page_num,
      min(t.id) AS start_key,
      max(t.id) AS end_key,
      count(*) AS page_size
    FROM
      (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
       FROM  aliexpress_early_bird_activity
               ${ew.customSqlSegment}
      ) t
    GROUP BY
      page_num
    ORDER BY
      page_num;
  </select>


</mapper>