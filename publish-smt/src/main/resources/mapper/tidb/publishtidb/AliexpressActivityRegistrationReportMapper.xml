<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressActivityRegistrationReportMapper">
    <select id="selectActivityRegistrationReport" resultType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressActivityRegistrationReport" >
        select activity_id,activity_name,activity_type,activity_countries activityCountries,recruitment_start_time recruitmentStartTime,
        recruitment_end_time recruitmentEndTime,display_start_time displayStartTime,display_end_time displayEndTime,
        IFNULL(count(distinct account_number),0) accountNumbers,IFNULL(count(IF(pro_count>0,1,NULL)),0) validNum,IFNULL(count(IF(confirmation_status=1,1,NULL)),0) confirmedEntryNum,IFNULL(count(IF(upload_status=1,1,NULL)),0) successEntryNum,IFNULL(sum(pro_count),0) totalProductNum,IFNULL(sum(submit_pro_count),0) canEntryProductNum,
        IFNULL(sum(CASE WHEN confirmation_status=1 and upload_status=1
        THEN submit_pro_count
        ELSE 0
        END
        ),0) realEntryProductNum from aliexpress_activity_registration
            ${ew.customSqlSegment}

        GROUP BY activity_id,activity_type
    </select>
</mapper>
