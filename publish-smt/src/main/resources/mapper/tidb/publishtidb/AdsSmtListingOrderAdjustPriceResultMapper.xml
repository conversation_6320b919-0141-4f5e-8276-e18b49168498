<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AdsSmtListingOrderAdjustPriceResultMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AdsSmtListingOrderAdjustPriceResult">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="ruleTypeId" column="rule_type_id" jdbcType="INTEGER"/>
            <result property="priority" column="priority" jdbcType="INTEGER"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
            <result property="itemStatus" column="item_status" jdbcType="VARCHAR"/>
            <result property="unsalableTag" column="unsalable_tag" jdbcType="VARCHAR"/>
            <result property="profitRate" column="profit_rate" jdbcType="DOUBLE"/>
            <result property="profitSum" column="profit_sum" jdbcType="DOUBLE"/>
            <result property="orderCountXd" column="order_count_xd" jdbcType="INTEGER"/>
            <result property="orderCountXdTrend" column="order_count_xd_trend" jdbcType="INTEGER"/>
            <result property="orderCount2xdTrend" column="order_count_2xd_trend" jdbcType="INTEGER"/>
            <result property="orderCountTrendRate" column="order_count_trend_rate" jdbcType="DOUBLE"/>
            <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,source,rule_type_id,priority,
        account_number,item_id,item_status,
        unsalable_tag,profit_rate,profit_sum,
        order_count_xd,order_count_xd_trend,order_count_2xd_trend,
        order_count_trend_rate,created_date
    </sql>

    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
        floor((t.row_num - 1) / 300) + 1 AS page_num,
        min(t.id) AS start_key,
        max(t.id) AS end_key,
        count(*) AS page_size
        FROM
        (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
        FROM ads_smt_listing_order_adjust_price_result
        ${ew.customSqlSegment}
        ) t
        GROUP BY
        page_num
        ORDER BY
        page_num;
    </select>
</mapper>
