<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtFreightTemplateCodeImportMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateCodeImport">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="tempId" column="temp_id" jdbcType="BIGINT"/>
            <result property="executeType" column="execute_type" jdbcType="INTEGER"/>
            <result property="tempCode" column="temp_code" jdbcType="VARCHAR"/>
            <result property="codeExpireTime" column="code_expire_time" jdbcType="TIMESTAMP"/>
            <result property="updateTempName" column="update_temp_name" jdbcType="VARCHAR"/>
            <result property="executeStatus" column="execute_status" jdbcType="INTEGER"/>
            <result property="executeTime" column="execute_time" jdbcType="TIMESTAMP"/>
            <result property="executeResult" column="execute_result" jdbcType="VARCHAR"/>
            <result property="executeSuccessTime" column="execute_success_time" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account,temp_id,
        execute_type,temp_code,code_expire_time,
        update_temp_name,execute_status,execute_time,
        execute_result,execute_success_time,created_by,
        created_time,update_by,updated_time
    </sql>

    <select id="getTidbPageMetaMap" resultType="java.util.Map">
        SELECT
            floor((t.row_num - 1) / 300) + 1 AS page_num,
            min(t.id) AS start_key,
            max(t.id) AS end_key,
            count(*) AS page_size
        FROM
            (SELECT id, row_number() OVER (ORDER BY id ) AS row_num
             FROM  smt_freight_template_code_import
                       ${ew.customSqlSegment}
            ) t
        GROUP BY
            page_num
        ORDER BY
            page_num;
    </select>
</mapper>
