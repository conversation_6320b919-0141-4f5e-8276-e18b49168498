<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.SmtFreightTemplateExcelImportMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.SmtFreightTemplateExcelImport">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="tempName" column="temp_name" jdbcType="VARCHAR"/>
            <result property="excelUrls" column="excel_urls" jdbcType="VARCHAR"/>
            <result property="executeStatus" column="execute_status" jdbcType="INTEGER"/>
            <result property="executeTime" column="execute_time" jdbcType="TIMESTAMP"/>
            <result property="executeResult" column="execute_result" jdbcType="VARCHAR"/>
            <result property="executeSuccessTime" column="execute_success_time" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account,file_name,
        temp_name,excel_urls,execute_status,
        execute_time,execute_result,execute_success_time,
        created_by,created_time,update_by,
        updated_time
    </sql>
</mapper>
