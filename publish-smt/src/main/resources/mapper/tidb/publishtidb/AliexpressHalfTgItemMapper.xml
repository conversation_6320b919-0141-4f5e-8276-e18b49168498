<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressHalfTgItemMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="product_status" property="productStatus" jdbcType="VARCHAR" />
    <result column="product_image" property="productImage" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
    <result column="sc_item_code" property="scItemCode" jdbcType="VARCHAR" />
    <result column="sc_item_bar_code" property="scItemBarCode" jdbcType="VARCHAR" />
    <result column="original_box" property="originalBox" jdbcType="VARCHAR" />
    <result column="sc_item_id" property="scItemId" jdbcType="VARCHAR" />
    <result column="special_product_type_list" property="specialProductTypeList" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="full_path_code" property="fullPathCode" jdbcType="VARCHAR" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="package_length" property="packageLength" jdbcType="DOUBLE" />
    <result column="package_width" property="packageWidth" jdbcType="DOUBLE" />
    <result column="package_height" property="packageHeight" jdbcType="DOUBLE" />
    <result column="package_weight" property="packageWeight" jdbcType="DOUBLE" />
    <result column="total_stocks" property="totalStocks" jdbcType="INTEGER" />
    <result column="max_sku_price" property="maxSkuPrice" jdbcType="DOUBLE" />
    <result column="min_sku_price" property="minSkuPrice" jdbcType="DOUBLE" />
    <result column="base_price" property="basePrice" jdbcType="DOUBLE" />
    <result column="choice_sku_price_list" property="choiceSkuPriceList" jdbcType="VARCHAR" />
    <result column="freight_fee_list" property="freightFeeList" jdbcType="VARCHAR" />
    <result column="pop_choice_sku_warehouse_stock_list" property="popChoiceSkuWarehouseStockList" jdbcType="VARCHAR" />
    <result column="joined_country_list" property="joinedCountryList" jdbcType="VARCHAR" />
    <result column="sku_property_list" property="skuPropertyList" jdbcType="VARCHAR" />
    <result column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="last_synch_time" property="lastSynchTime" jdbcType="TIMESTAMP" />
    <result column="sku_status" property="skuStatus" jdbcType="VARCHAR" />
    <result column="sku_tag_code" property="skuTagCode" jdbcType="VARCHAR" />
    <result column="special_goods_code" property="specialGoodsCode" jdbcType="VARCHAR" />
    <result column="forbid_channel" property="forbidChannel" jdbcType="VARCHAR" />
    <result column="infringement_type_name" property="infringementTypeName" jdbcType="VARCHAR" />
    <result column="infringement_obj" property="infringementObj" jdbcType="VARCHAR" />
    <result column="prohibition_sites" property="prohibitionSites" jdbcType="VARCHAR" />
    <result column="pro_category_id" property="proCategoryId" jdbcType="INTEGER" />
    <result column="pro_category_id_path" property="proCategoryIdPath" jdbcType="VARCHAR" />
    <result column="pro_category_cn_name" property="proCategoryCnName" jdbcType="VARCHAR" />
    <result column="promotion" property="promotion" jdbcType="INTEGER" />
    <result column="new_state" property="newState" jdbcType="BIT" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="compose_status" property="composeStatus" jdbcType="INTEGER" />
    <result column="system_stock" property="systemStock" jdbcType="INTEGER" />
    <result column="update_system_stock_date" property="updateSystemStockDate" jdbcType="TIMESTAMP" />
    <result column="sku_stock" property="skuStock" jdbcType="INTEGER" />
    <result column="sku_bind" property="skuBind" jdbcType="BIT" />
    <result column="usable_stock" property="usableStock" jdbcType="INTEGER" />
    <result column="smt_transfer_stock" property="smtTransferStock" jdbcType="INTEGER" />
    <result column="system_usable_transfer_stock" property="systemUsableTransferStock" jdbcType="INTEGER" />
    <result column="online_status" property="onlineStatus" jdbcType="VARCHAR" />
    <result column="exit_state" property="exitState" jdbcType="INTEGER" />
    <result column="exit_reason" property="exitReason" jdbcType="VARCHAR" />
    <result column="submit_exit_time" property="submitExitTime" jdbcType="TIMESTAMP" />
    <result column="exit_fail_info" property="exitFailInfo" jdbcType="VARCHAR" />
    <result column="exit_time" property="exitTime" jdbcType="TIMESTAMP" />
    <result column="ladder_price" property="ladderPrice" jdbcType="VARCHAR" />
    <result column="is_has_ladder_price" property="isHasLadderPrice" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Example_Clause" >
    <foreach collection="oredCriteria" item="criteria" separator="or" >
      <if test="criteria.valid" >
        <trim prefix="(" suffix=")" prefixOverrides="and" >
          <foreach collection="criteria.criteria" item="criterion" >
            <choose >
              <when test="criterion.noValue" >
                and ${criterion.condition}
              </when>
              <when test="criterion.singleValue" >
                and ${criterion.condition} #{criterion.value}
              </when>
              <when test="criterion.betweenValue" >
                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
              </when>
              <when test="criterion.listValue" >
                and ${criterion.condition}
                <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                  #{listItem}
                </foreach>
              </when>
            </choose>
          </foreach>
        </trim>
      </if>
    </foreach>
  </sql>

  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account, product_id, product_status, product_image, article_number, sku_code, 
    sku_id, sc_item_code, sc_item_bar_code, original_box, sc_item_id, special_product_type_list, 
    title, category_id, full_path_code, currency_code, package_length, package_width, 
    package_height, package_weight, total_stocks, max_sku_price, min_sku_price, base_price, 
    choice_sku_price_list, freight_fee_list, pop_choice_sku_warehouse_stock_list, joined_country_list, 
    sku_property_list, modified_time, create_time, last_synch_time, sku_status, sku_tag_code, 
    special_goods_code, forbid_channel, infringement_type_name, infringement_obj, prohibition_sites, 
    pro_category_id, pro_category_id_path, pro_category_cn_name, promotion, new_state, 
    sku_data_source, compose_status, system_stock, update_system_stock_date, sku_stock, sku_bind,
    usable_stock, smt_transfer_stock, system_usable_transfer_stock,online_status,
    exit_state, exit_reason, submit_exit_time, exit_fail_info, exit_time, ladder_price, is_has_ladder_price
  </sql>
  <sql id="Search_Column_List" >
    id, account, product_id, product_status, product_image, article_number, sku_code,
    sku_id, sc_item_code, sc_item_bar_code, original_box, sc_item_id, special_product_type_list,
    title, category_id, full_path_code, currency_code, package_length, package_width,
    package_height, package_weight, total_stocks, max_sku_price, min_sku_price, base_price,
    choice_sku_price_list, freight_fee_list, pop_choice_sku_warehouse_stock_list, sku_property_list, modified_time, create_time, last_synch_time, sku_status, sku_tag_code,
    special_goods_code, forbid_channel, infringement_type_name, infringement_obj, prohibition_sites,
    pro_category_id, pro_category_id_path, pro_category_cn_name, promotion, new_state,
    sku_data_source, compose_status, system_stock, update_system_stock_date, sku_stock, sku_bind,
    usable_stock, smt_transfer_stock, system_usable_transfer_stock,online_status,
    exit_state, exit_reason, submit_exit_time, exit_fail_info, exit_time, ladder_price, is_has_ladder_price
  </sql>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from aliexpress_half_tg_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="search" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Search_Column_List" />
    from aliexpress_half_tg_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_half_tg_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectScCode" resultType="Long" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample" >
    SELECT DISTINCT product_id FROM aliexpress_half_tg_item WHERE
    (sc_item_bar_code REGEXP '[^a-zA-Z0-9]' or sc_item_code != sc_item_bar_code ) and
    <if test="_parameter != null" >
      <include refid="Example_Clause" />
    </if>
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_half_tg_item
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_half_tg_item (account, product_id, product_status, 
      product_image, article_number, sku_code, 
      sku_id, sc_item_code, sc_item_bar_code, 
      original_box, sc_item_id, special_product_type_list, 
      title, category_id, full_path_code, 
      currency_code, package_length, package_width, 
      package_height, package_weight, total_stocks, 
      max_sku_price, min_sku_price, base_price, 
      choice_sku_price_list, freight_fee_list, pop_choice_sku_warehouse_stock_list, 
      joined_country_list, sku_property_list, modified_time, 
      create_time, last_synch_time, sku_status, 
      sku_tag_code, special_goods_code, forbid_channel, 
      infringement_type_name, infringement_obj, 
      prohibition_sites, pro_category_id, pro_category_id_path, 
      pro_category_cn_name, promotion, new_state, 
      sku_data_source, compose_status, system_stock, update_system_stock_date, sku_stock, sku_bind,
      usable_stock, smt_transfer_stock, system_usable_transfer_stock,online_status, exit_state, exit_reason,
        submit_exit_time, exit_fail_info, exit_time, ladder_price, is_has_ladder_price)
    values (#{account,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{productStatus,jdbcType=VARCHAR}, 
      #{productImage,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, 
      #{skuId,jdbcType=VARCHAR}, #{scItemCode,jdbcType=VARCHAR}, #{scItemBarCode,jdbcType=VARCHAR}, 
      #{originalBox,jdbcType=VARCHAR}, #{scItemId,jdbcType=VARCHAR}, #{specialProductTypeList,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}, #{fullPathCode,jdbcType=VARCHAR}, 
      #{currencyCode,jdbcType=VARCHAR}, #{packageLength,jdbcType=DOUBLE}, #{packageWidth,jdbcType=DOUBLE}, 
      #{packageHeight,jdbcType=DOUBLE}, #{packageWeight,jdbcType=DOUBLE}, #{totalStocks,jdbcType=INTEGER}, 
      #{maxSkuPrice,jdbcType=DOUBLE}, #{minSkuPrice,jdbcType=DOUBLE}, #{basePrice,jdbcType=DOUBLE}, 
      #{choiceSkuPriceList,jdbcType=VARCHAR}, #{freightFeeList,jdbcType=VARCHAR}, #{popChoiceSkuWarehouseStockList,jdbcType=VARCHAR}, 
      #{joinedCountryList,jdbcType=VARCHAR}, #{skuPropertyList,jdbcType=VARCHAR}, #{modifiedTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{lastSynchTime,jdbcType=TIMESTAMP}, #{skuStatus,jdbcType=VARCHAR}, 
      #{skuTagCode,jdbcType=VARCHAR}, #{specialGoodsCode,jdbcType=VARCHAR}, #{forbidChannel,jdbcType=VARCHAR}, 
      #{infringementTypeName,jdbcType=VARCHAR}, #{infringementObj,jdbcType=VARCHAR}, 
      #{prohibitionSites,jdbcType=VARCHAR}, #{proCategoryId,jdbcType=INTEGER}, #{proCategoryIdPath,jdbcType=VARCHAR}, 
      #{proCategoryCnName,jdbcType=VARCHAR}, #{promotion,jdbcType=INTEGER}, #{newState,jdbcType=BIT},
      #{skuDataSource,jdbcType=INTEGER}, #{composeStatus,jdbcType=INTEGER}, #{systemStock,jdbcType=INTEGER},
      #{updateSystemStockDate,jdbcType=TIMESTAMP}, #{skuStock,jdbcType=INTEGER}, #{skuBind,jdbcType=BIT},
      #{usableStock,jdbcType=INTEGER}, #{smtTransferStock,jdbcType=INTEGER}, #{systemUsableTransferStock,jdbcType=INTEGER}
      ,#{onlineStatus,jdbcType=VARCHAR}, #{exitState,jdbcType=INTEGER}, #{exitReason,jdbcType=VARCHAR},
     #{submitExitTime,jdbcType=TIMESTAMP}, #{exitFailInfo,jdbcType=VARCHAR}, #{exitTime,jdbcType=TIMESTAMP}, #{ladderPrice,jdbcType=VARCHAR}, #{isHasLadderPrice,jdbcType=BIT}
    )
  </insert>

  <insert id="batchInsert">
    <foreach collection="itemList" item="item" open="" separator=";" close=";">
      insert into aliexpress_half_tg_item (account, product_id, product_status,
      product_image, article_number, sku_code,
      sku_id, sc_item_code, sc_item_bar_code,
      original_box, sc_item_id, special_product_type_list,
      title, category_id, full_path_code,
      currency_code, package_length, package_width,
      package_height, package_weight, total_stocks,
      max_sku_price, min_sku_price, base_price,
      choice_sku_price_list, freight_fee_list, pop_choice_sku_warehouse_stock_list,
      joined_country_list, sku_property_list, modified_time,
      create_time, last_synch_time, sku_status,
      sku_tag_code, special_goods_code, forbid_channel,
      infringement_type_name, infringement_obj,
      prohibition_sites, pro_category_id, pro_category_id_path,
      pro_category_cn_name, promotion, new_state,
      sku_data_source, compose_status, system_stock, update_system_stock_date, sku_stock, sku_bind,
      usable_stock, smt_transfer_stock, system_usable_transfer_stock,online_status, exit_state, exit_reason,
      submit_exit_time, exit_fail_info, exit_time, ladder_price, is_has_ladder_price)
      values (#{item.account,jdbcType=VARCHAR}, #{item.productId,jdbcType=BIGINT}, #{item.productStatus,jdbcType=VARCHAR},
      #{item.productImage,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR},
      #{item.skuId,jdbcType=VARCHAR}, #{item.scItemCode,jdbcType=VARCHAR}, #{item.scItemBarCode,jdbcType=VARCHAR},
      #{item.originalBox,jdbcType=VARCHAR}, #{item.scItemId,jdbcType=VARCHAR}, #{item.specialProductTypeList,jdbcType=VARCHAR},
      #{item.title,jdbcType=VARCHAR}, #{item.categoryId,jdbcType=INTEGER}, #{item.fullPathCode,jdbcType=VARCHAR},
      #{item.currencyCode,jdbcType=VARCHAR}, #{item.packageLength,jdbcType=DOUBLE}, #{item.packageWidth,jdbcType=DOUBLE},
      #{item.packageHeight,jdbcType=DOUBLE}, #{item.packageWeight,jdbcType=DOUBLE}, #{item.totalStocks,jdbcType=INTEGER},
      #{item.maxSkuPrice,jdbcType=DOUBLE}, #{item.minSkuPrice,jdbcType=DOUBLE}, #{item.basePrice,jdbcType=DOUBLE},
      #{item.choiceSkuPriceList,jdbcType=VARCHAR}, #{item.freightFeeList,jdbcType=VARCHAR}, #{item.popChoiceSkuWarehouseStockList,jdbcType=VARCHAR},
      #{item.joinedCountryList,jdbcType=VARCHAR}, #{item.skuPropertyList,jdbcType=VARCHAR}, #{item.modifiedTime,jdbcType=TIMESTAMP},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.lastSynchTime,jdbcType=TIMESTAMP}, #{item.skuStatus,jdbcType=VARCHAR},
      #{item.skuTagCode,jdbcType=VARCHAR}, #{item.specialGoodsCode,jdbcType=VARCHAR}, #{item.forbidChannel,jdbcType=VARCHAR},
      #{item.infringementTypeName,jdbcType=VARCHAR}, #{item.infringementObj,jdbcType=VARCHAR},
      #{item.prohibitionSites,jdbcType=VARCHAR}, #{item.proCategoryId,jdbcType=INTEGER}, #{item.proCategoryIdPath,jdbcType=VARCHAR},
      #{item.proCategoryCnName,jdbcType=VARCHAR}, #{item.promotion,jdbcType=INTEGER}, #{item.newState,jdbcType=BIT},
      #{item.skuDataSource,jdbcType=INTEGER}, #{item.composeStatus,jdbcType=INTEGER}, #{item.systemStock,jdbcType=INTEGER},
      #{item.updateSystemStockDate,jdbcType=TIMESTAMP}, #{item.skuStock,jdbcType=INTEGER}, #{item.skuBind,jdbcType=BIT},
      #{item.usableStock,jdbcType=INTEGER}, #{item.smtTransferStock,jdbcType=INTEGER}, #{item.systemUsableTransferStock,jdbcType=INTEGER}
      ,#{item.onlineStatus,jdbcType=VARCHAR}, #{item.exitState,jdbcType=INTEGER}, #{item.exitReason,jdbcType=VARCHAR},
      #{item.submitExitTime,jdbcType=TIMESTAMP}, #{item.exitFailInfo,jdbcType=VARCHAR}, #{item.exitTime,jdbcType=TIMESTAMP}, #{item.ladderPrice,jdbcType=VARCHAR}, #{item.isHasLadderPrice,jdbcType=BIT}
      )
    </foreach>
  </insert>


  <select id="countByExample" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_half_tg_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_half_tg_item
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productStatus != null" >
        product_status = #{record.productStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.productImage != null" >
        product_image = #{record.productImage,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null" >
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null" >
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.scItemCode != null" >
        sc_item_code = #{record.scItemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.scItemBarCode != null" >
        sc_item_bar_code = #{record.scItemBarCode,jdbcType=VARCHAR},
      </if>
      <if test="record.originalBox != null" >
        original_box = #{record.originalBox,jdbcType=VARCHAR},
      </if>
      <if test="record.scItemId != null" >
        sc_item_id = #{record.scItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.specialProductTypeList != null" >
        special_product_type_list = #{record.specialProductTypeList,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.fullPathCode != null" >
        full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.packageLength != null" >
        package_length = #{record.packageLength,jdbcType=DOUBLE},
      </if>
      <if test="record.packageWidth != null" >
        package_width = #{record.packageWidth,jdbcType=DOUBLE},
      </if>
      <if test="record.packageHeight != null" >
        package_height = #{record.packageHeight,jdbcType=DOUBLE},
      </if>
      <if test="record.packageWeight != null" >
        package_weight = #{record.packageWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.totalStocks != null" >
        total_stocks = #{record.totalStocks,jdbcType=INTEGER},
      </if>
      <if test="record.maxSkuPrice != null" >
        max_sku_price = #{record.maxSkuPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.minSkuPrice != null" >
        min_sku_price = #{record.minSkuPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.basePrice != null" >
        base_price = #{record.basePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.choiceSkuPriceList != null" >
        choice_sku_price_list = #{record.choiceSkuPriceList,jdbcType=VARCHAR},
      </if>
      <if test="record.freightFeeList != null" >
        freight_fee_list = #{record.freightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="record.popChoiceSkuWarehouseStockList != null" >
        pop_choice_sku_warehouse_stock_list = #{record.popChoiceSkuWarehouseStockList,jdbcType=VARCHAR},
      </if>
      <if test="record.joinedCountryList != null" >
        joined_country_list = #{record.joinedCountryList,jdbcType=VARCHAR},
      </if>
      <if test="record.skuPropertyList != null" >
        sku_property_list = #{record.skuPropertyList,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedTime != null" >
        modified_time = #{record.modifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastSynchTime != null" >
        last_synch_time = #{record.lastSynchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.skuStatus != null" >
        sku_status = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.skuTagCode != null" >
        sku_tag_code = #{record.skuTagCode,jdbcType=VARCHAR},
      </if>
      <if test="record.specialGoodsCode != null" >
        special_goods_code = #{record.specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidChannel != null" >
        forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementTypeName != null" >
        infringement_type_name = #{record.infringementTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementObj != null" >
        infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="record.prohibitionSites != null" >
        prohibition_sites = #{record.prohibitionSites,jdbcType=VARCHAR},
      </if>
      <if test="record.proCategoryId != null" >
        pro_category_id = #{record.proCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.proCategoryIdPath != null" >
        pro_category_id_path = #{record.proCategoryIdPath,jdbcType=VARCHAR},
      </if>
      <if test="record.proCategoryCnName != null" >
        pro_category_cn_name = #{record.proCategoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotion != null" >
        promotion = #{record.promotion,jdbcType=INTEGER},
      </if>
      <if test="record.newState != null" >
        new_state = #{record.newState,jdbcType=BIT},
      </if>
      <if test="record.skuDataSource != null" >
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.composeStatus != null" >
        compose_status = #{record.composeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.systemStock != null" >
        system_stock = #{record.systemStock,jdbcType=INTEGER},
      </if>
      <if test="record.updateSystemStockDate != null" >
        update_system_stock_date = #{record.updateSystemStockDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.skuStock != null" >
        sku_stock = #{record.skuStock,jdbcType=INTEGER},
      </if>
      <if test="record.skuBind != null" >
        sku_bind = #{record.skuBind,jdbcType=BIT},
      </if>
      <if test="record.usableStock != null" >
        usable_stock = #{record.usableStock,jdbcType=INTEGER},
      </if>
      <if test="record.smtTransferStock != null" >
        smt_transfer_stock = #{record.smtTransferStock,jdbcType=INTEGER},
      </if>
      <if test="record.systemUsableTransferStock != null" >
        system_usable_transfer_stock = #{record.systemUsableTransferStock,jdbcType=INTEGER},
      </if>
      <if test="record.onlineStatus != null" >
        online_status = #{record.onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.exitState != null" >
        exit_state = #{record.exitState,jdbcType=INTEGER},
      </if>
      <if test="record.exitReason != null" >
        exit_reason = #{record.exitReason,jdbcType=VARCHAR},
      </if>
      <if test="record.submitExitTime != null" >
        submit_exit_time = #{record.submitExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.exitFailInfo != null" >
        exit_fail_info = #{record.exitFailInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.exitTime != null" >
        exit_time = #{record.exitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ladderPrice != null" >
        ladder_price = #{record.ladderPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.isHasLadderPrice != null" >
        is_has_ladder_price = #{record.isHasLadderPrice,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateBySkuChange" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem" >
    update aliexpress_half_tg_item
    <set >
        sku_status = #{skuStatus,jdbcType=VARCHAR},
        sku_tag_code = #{skuTagCode,jdbcType=VARCHAR},
        special_goods_code = #{specialGoodsCode,jdbcType=VARCHAR},
        forbid_channel = #{forbidChannel,jdbcType=VARCHAR},
        infringement_type_name = #{infringementTypeName,jdbcType=VARCHAR},
        infringement_obj = #{infringementObj,jdbcType=VARCHAR},
        prohibition_sites = #{prohibitionSites,jdbcType=VARCHAR},
        pro_category_id = #{proCategoryId,jdbcType=INTEGER},
        pro_category_id_path = #{proCategoryIdPath,jdbcType=VARCHAR},
        pro_category_cn_name = #{proCategoryCnName,jdbcType=VARCHAR},
        promotion = #{promotion,jdbcType=INTEGER},
        new_state = #{newState,jdbcType=BIT},
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
        compose_status = #{composeStatus,jdbcType=INTEGER},
        system_stock = #{systemStock,jdbcType=INTEGER},
        update_system_stock_date = #{updateSystemStockDate,jdbcType=TIMESTAMP},
        usable_stock = #{usableStock,jdbcType=INTEGER},
        smt_transfer_stock = #{smtTransferStock,jdbcType=INTEGER},
        system_usable_transfer_stock = #{systemUsableTransferStock,jdbcType=INTEGER},
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem" >
    update aliexpress_half_tg_item
    <set >
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productStatus != null" >
        product_status = #{productStatus,jdbcType=VARCHAR},
      </if>
      <if test="productImage != null" >
        product_image = #{productImage,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null" >
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="scItemCode != null" >
        sc_item_code = #{scItemCode,jdbcType=VARCHAR},
      </if>
      <if test="scItemBarCode != null" >
        sc_item_bar_code = #{scItemBarCode,jdbcType=VARCHAR},
      </if>
      <if test="originalBox != null" >
        original_box = #{originalBox,jdbcType=VARCHAR},
      </if>
      <if test="scItemId != null" >
        sc_item_id = #{scItemId,jdbcType=VARCHAR},
      </if>
      <if test="specialProductTypeList != null" >
        special_product_type_list = #{specialProductTypeList,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="fullPathCode != null" >
        full_path_code = #{fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="packageLength != null" >
        package_length = #{packageLength,jdbcType=DOUBLE},
      </if>
      <if test="packageWidth != null" >
        package_width = #{packageWidth,jdbcType=DOUBLE},
      </if>
      <if test="packageHeight != null" >
        package_height = #{packageHeight,jdbcType=DOUBLE},
      </if>
      <if test="packageWeight != null" >
        package_weight = #{packageWeight,jdbcType=DOUBLE},
      </if>
      <if test="totalStocks != null" >
        total_stocks = #{totalStocks,jdbcType=INTEGER},
      </if>
      <if test="maxSkuPrice != null" >
        max_sku_price = #{maxSkuPrice,jdbcType=DOUBLE},
      </if>
      <if test="minSkuPrice != null" >
        min_sku_price = #{minSkuPrice,jdbcType=DOUBLE},
      </if>
      <if test="basePrice != null" >
        base_price = #{basePrice,jdbcType=DOUBLE},
      </if>
      <if test="choiceSkuPriceList != null" >
        choice_sku_price_list = #{choiceSkuPriceList,jdbcType=VARCHAR},
      </if>
      <if test="freightFeeList != null" >
        freight_fee_list = #{freightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="popChoiceSkuWarehouseStockList != null" >
        pop_choice_sku_warehouse_stock_list = #{popChoiceSkuWarehouseStockList,jdbcType=VARCHAR},
      </if>
      <if test="joinedCountryList != null" >
        joined_country_list = #{joinedCountryList,jdbcType=VARCHAR},
      </if>
      <if test="skuPropertyList != null" >
        sku_property_list = #{skuPropertyList,jdbcType=VARCHAR},
      </if>
      <if test="modifiedTime != null" >
        modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastSynchTime != null" >
        last_synch_time = #{lastSynchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuStatus != null" >
        sku_status = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="skuTagCode != null" >
        sku_tag_code = #{skuTagCode,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsCode != null" >
        special_goods_code = #{specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="forbidChannel != null" >
        forbid_channel = #{forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypeName != null" >
        infringement_type_name = #{infringementTypeName,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null" >
        infringement_obj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="prohibitionSites != null" >
        prohibition_sites = #{prohibitionSites,jdbcType=VARCHAR},
      </if>
      <if test="proCategoryId != null" >
        pro_category_id = #{proCategoryId,jdbcType=INTEGER},
      </if>
      <if test="proCategoryIdPath != null" >
        pro_category_id_path = #{proCategoryIdPath,jdbcType=VARCHAR},
      </if>
      <if test="proCategoryCnName != null" >
        pro_category_cn_name = #{proCategoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="promotion != null" >
        promotion = #{promotion,jdbcType=INTEGER},
      </if>
      <if test="newState != null" >
        new_state = #{newState,jdbcType=BIT},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="composeStatus != null" >
        compose_status = #{composeStatus,jdbcType=INTEGER},
      </if>
      <if test="systemStock != null" >
        system_stock = #{systemStock,jdbcType=INTEGER},
      </if>
      <if test="updateSystemStockDate != null" >
        update_system_stock_date = #{updateSystemStockDate,jdbcType=TIMESTAMP},
      </if>
      <if test="skuStock != null" >
        sku_stock = #{skuStock,jdbcType=INTEGER},
      </if>
      <if test="skuBind != null" >
        sku_bind = #{skuBind,jdbcType=BIT},
      </if>
      <if test="usableStock != null" >
        usable_stock = #{usableStock,jdbcType=INTEGER},
      </if>
      <if test="smtTransferStock != null" >
        smt_transfer_stock = #{smtTransferStock,jdbcType=INTEGER},
      </if>
      <if test="systemUsableTransferStock != null" >
        system_usable_transfer_stock = #{systemUsableTransferStock,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null" >
        online_status = #{onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="exitState != null" >
        exit_state = #{exitState,jdbcType=INTEGER},
      </if>
      <if test="exitReason != null" >
        exit_reason = #{exitReason,jdbcType=VARCHAR},
      </if>
      <if test="submitExitTime != null" >
        submit_exit_time = #{submitExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="exitFailInfo != null" >
        exit_fail_info = #{exitFailInfo,jdbcType=VARCHAR},
      </if>
      <if test="exitTime != null" >
        exit_time = #{exitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ladderPrice != null" >
        ladder_price = #{ladderPrice,jdbcType=VARCHAR},
      </if>
      <if test="isHasLadderPrice != null" >
        is_has_ladder_price = #{isHasLadderPrice,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateSystemStockBySku" parameterType="com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem" >
      update aliexpress_half_tg_item
      <set >
          system_stock = #{systemStock,jdbcType=INTEGER},
          update_system_stock_date = #{updateSystemStockDate,jdbcType=TIMESTAMP},
          usable_stock = #{usableStock,jdbcType=INTEGER},
          smt_transfer_stock = #{smtTransferStock,jdbcType=INTEGER},
          system_usable_transfer_stock = #{systemUsableTransferStock,jdbcType=INTEGER},
      </set>
      where article_number = #{articleNumber,jdbcType=VARCHAR}
  </update>


  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="itemList" index="index" item="record" open="" separator=";" close=";">
    update aliexpress_half_tg_item
      <set >
        <if test="record.account != null" >
          account = #{record.account,jdbcType=VARCHAR},
        </if>
        <if test="record.productId != null" >
          product_id = #{record.productId,jdbcType=BIGINT},
        </if>
        <if test="record.productStatus != null" >
          product_status = #{record.productStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.productImage != null" >
          product_image = #{record.productImage,jdbcType=VARCHAR},
        </if>
        <if test="record.articleNumber != null" >
          article_number = #{record.articleNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.skuCode != null" >
          sku_code = #{record.skuCode,jdbcType=VARCHAR},
        </if>
        <if test="record.skuId != null" >
          sku_id = #{record.skuId,jdbcType=VARCHAR},
        </if>
        <if test="record.scItemCode != null" >
          sc_item_code = #{record.scItemCode,jdbcType=VARCHAR},
        </if>
        <if test="record.scItemBarCode != null" >
          sc_item_bar_code = #{record.scItemBarCode,jdbcType=VARCHAR},
        </if>
        <if test="record.originalBox != null" >
          original_box = #{record.originalBox,jdbcType=VARCHAR},
        </if>
        <if test="record.scItemId != null" >
          sc_item_id = #{record.scItemId,jdbcType=VARCHAR},
        </if>
        <if test="record.specialProductTypeList != null" >
          special_product_type_list = #{record.specialProductTypeList,jdbcType=VARCHAR},
        </if>
        <if test="record.title != null" >
          title = #{record.title,jdbcType=VARCHAR},
        </if>
        <if test="record.categoryId != null" >
          category_id = #{record.categoryId,jdbcType=INTEGER},
        </if>
        <if test="record.fullPathCode != null" >
          full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
        </if>
        <if test="record.currencyCode != null" >
          currency_code = #{record.currencyCode,jdbcType=VARCHAR},
        </if>
        <if test="record.packageLength != null" >
          package_length = #{record.packageLength,jdbcType=DOUBLE},
        </if>
        <if test="record.packageWidth != null" >
          package_width = #{record.packageWidth,jdbcType=DOUBLE},
        </if>
        <if test="record.packageHeight != null" >
          package_height = #{record.packageHeight,jdbcType=DOUBLE},
        </if>
        <if test="record.packageWeight != null" >
          package_weight = #{record.packageWeight,jdbcType=DOUBLE},
        </if>
        <if test="record.totalStocks != null" >
          total_stocks = #{record.totalStocks,jdbcType=INTEGER},
        </if>
        <if test="record.maxSkuPrice != null" >
          max_sku_price = #{record.maxSkuPrice,jdbcType=DOUBLE},
        </if>
        <if test="record.minSkuPrice != null" >
          min_sku_price = #{record.minSkuPrice,jdbcType=DOUBLE},
        </if>
        <if test="record.basePrice != null" >
          base_price = #{record.basePrice,jdbcType=DOUBLE},
        </if>
        <if test="record.choiceSkuPriceList != null" >
          choice_sku_price_list = #{record.choiceSkuPriceList,jdbcType=VARCHAR},
        </if>
        <if test="record.freightFeeList != null" >
          freight_fee_list = #{record.freightFeeList,jdbcType=VARCHAR},
        </if>
        <if test="record.popChoiceSkuWarehouseStockList != null" >
          pop_choice_sku_warehouse_stock_list = #{record.popChoiceSkuWarehouseStockList,jdbcType=VARCHAR},
        </if>
        <if test="record.joinedCountryList != null" >
          joined_country_list = #{record.joinedCountryList,jdbcType=VARCHAR},
        </if>
        <if test="record.skuPropertyList != null" >
          sku_property_list = #{record.skuPropertyList,jdbcType=VARCHAR},
        </if>
        <if test="record.modifiedTime != null" >
          modified_time = #{record.modifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.createTime != null" >
          create_time = #{record.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.lastSynchTime != null" >
          last_synch_time = #{record.lastSynchTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.skuStatus != null" >
          sku_status = #{record.skuStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.skuTagCode != null" >
          sku_tag_code = #{record.skuTagCode,jdbcType=VARCHAR},
        </if>
        <if test="record.specialGoodsCode != null" >
          special_goods_code = #{record.specialGoodsCode,jdbcType=VARCHAR},
        </if>
        <if test="record.forbidChannel != null" >
          forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
        </if>
        <if test="record.infringementTypeName != null" >
          infringement_type_name = #{record.infringementTypeName,jdbcType=VARCHAR},
        </if>
        <if test="record.infringementObj != null" >
          infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
        </if>
        <if test="record.prohibitionSites != null" >
          prohibition_sites = #{record.prohibitionSites,jdbcType=VARCHAR},
        </if>
        <if test="record.proCategoryId != null" >
          pro_category_id = #{record.proCategoryId,jdbcType=INTEGER},
        </if>
        <if test="record.proCategoryIdPath != null" >
          pro_category_id_path = #{record.proCategoryIdPath,jdbcType=VARCHAR},
        </if>
        <if test="record.proCategoryCnName != null" >
          pro_category_cn_name = #{record.proCategoryCnName,jdbcType=VARCHAR},
        </if>
        <if test="record.promotion != null" >
          promotion = #{record.promotion,jdbcType=INTEGER},
        </if>
        <if test="record.newState != null" >
          new_state = #{record.newState,jdbcType=BIT},
        </if>
        <if test="record.skuDataSource != null" >
          sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
        </if>
        <if test="record.composeStatus != null" >
          compose_status = #{record.composeStatus,jdbcType=INTEGER},
        </if>
        <if test="record.systemStock != null" >
          system_stock = #{record.systemStock,jdbcType=INTEGER},
        </if>
        <if test="record.updateSystemStockDate != null" >
          update_system_stock_date = #{record.updateSystemStockDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.skuStock != null" >
          sku_stock = #{record.skuStock,jdbcType=INTEGER},
        </if>
        <if test="record.skuBind != null" >
          sku_bind = #{record.skuBind,jdbcType=BIT},
        </if>
        <if test="record.usableStock != null" >
          usable_stock = #{record.usableStock,jdbcType=INTEGER},
        </if>
        <if test="record.smtTransferStock != null" >
          smt_transfer_stock = #{record.smtTransferStock,jdbcType=INTEGER},
        </if>
        <if test="record.systemUsableTransferStock != null" >
          system_usable_transfer_stock = #{record.systemUsableTransferStock,jdbcType=INTEGER},
        </if>
        <if test="record.onlineStatus != null" >
          online_status = #{record.onlineStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.exitState != null" >
          exit_state = #{record.exitState,jdbcType=INTEGER},
        </if>
        <if test="record.exitReason != null" >
          exit_reason = #{record.exitReason,jdbcType=VARCHAR},
        </if>
        <if test="record.submitExitTime != null" >
          submit_exit_time = #{record.submitExitTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.exitFailInfo != null" >
          exit_fail_info = #{record.exitFailInfo,jdbcType=VARCHAR},
        </if>
        <if test="record.exitTime != null" >
          exit_time = #{record.exitTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.ladderPrice != null" >
          ladder_price = #{record.ladderPrice,jdbcType=VARCHAR},
        </if>
        <if test="record.isHasLadderPrice != null" >
          is_has_ladder_price = #{record.isHasLadderPrice,jdbcType=BIT},
        </if>
      </set>
      where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateOnlineStatus" parameterType="java.util.List" >
    UPDATE aliexpress_half_tg_item
    SET
        online_status = 'not_online'
     where
        id IN
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND
        online_status = 'online'
  </update>

  <update id="exitUpdateOnlineStatus">
    UPDATE aliexpress_half_tg_item
    SET
    online_status = 'not_online'
    where 1 = 1
    AND account = #{account,jdbcType=VARCHAR}
    AND product_id = #{productId,jdbcType=BIGINT}
    AND online_status = 'online'
  </update>
</mapper>